/**
 * Table Of Content
 *
 * 	1. Globals
 *	2. Headers
 *	3. Navigations
 *	4. Banners
 *	5. Footers
 *	6. app
 *	7. Widgets
 *	8. Custom Templates
 */

@import "variable";
@import "functions";
@import "../../../node_modules/bootstrap/scss/bootstrap";
@import "app";
@import "../scss/icons/font-awesome/css/font-awesome.min.css";

// Notification system
@import "~ng-snotify/styles/material";

// PACTO CSS
@import "./pacto/basic-elements.scss";
@import "./pacto/ngb-pagination-override.scss";

// PLATAFORMA PACTO CSS
@import "./plataforma-pacto/ngb-modal.scss";

// color css
@import "default";

// import styles-adm.scss
@import "./styles-adm.scss";

.agenda-scroll-container .scroll-content {
	width: 100% !important;
}

.whats-icon {
	background: url("./../images/whats.svg");
	background-size: cover;
	position: absolute;
	width: 20px;
	height: 20px;
	text-decoration: none;
	cursor: pointer;
}

#side-filter.mat-dialog-container {
	padding: 0;
}
