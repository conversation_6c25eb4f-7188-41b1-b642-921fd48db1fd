@import "./pacto/plataforma-import.scss";

.carregando-dados {
	position: absolute;
	text-align: center;
	width: 168px;
	height: 16px;
	top: 50%;
	left: 50%;
	line-height: 30px;
	color: $azulPacto04;

	.icon-loding {
		font-size: 30px;
		-webkit-animation-name: spin;
		-webkit-animation-duration: 2000ms;
		-webkit-animation-iteration-count: infinite;
		-webkit-animation-timing-function: linear;
		-moz-animation-name: spin;
		-moz-animation-duration: 2000ms;
		-moz-animation-iteration-count: infinite;
		-moz-animation-timing-function: linear;

		animation-name: spin;
		animation-duration: 2000ms;
		animation-iteration-count: infinite;
		animation-timing-function: linear;
	}

	.info-carregando-dados {
		@extend .type-h6-bold;
	}
}

@-ms-keyframes spin {
	from {
		-ms-transform: rotate(0deg);
	}
	to {
		-ms-transform: rotate(359deg);
	}
}

@-moz-keyframes spin {
	from {
		-moz-transform: rotate(0deg);
	}
	to {
		-moz-transform: rotate(359deg);
	}
}

@-webkit-keyframes spin {
	from {
		-webkit-transform: rotate(0deg);
	}
	to {
		-webkit-transform: rotate(359deg);
	}
}
