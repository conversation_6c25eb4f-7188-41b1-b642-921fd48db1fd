/* 
    INSET BOX
*/
.pacto-inset-box {
	box-shadow: inset 0px 0px 9px 0px #f5f5f5;
	background-color: #fbfbfb;
	font-size: 12px;
}

/* 
    CARD SHADOW
*/

/* 
    OVERRIDE BOOTSTRAP INPUT LABEL
*/
.form-group label.control-label {
	font-weight: 600;
}

/* 
    OVERRIDE BOOTSTRAP CHECKBOX
*/
.form-group .form-check input[type="checkbox"] {
	margin-top: 5px;
}

/*
    TABLE WRAPPER
*/
.table-wrapper {
	background-color: #fff;
	border-radius: 6px;
}

/*
    REMOVING 'JS charts by amcharts'
*/
.amcharts-chart-div a {
	display: none !important;
}

/*
    EXTRA LARGE NG-BOOTSTRAP MODAL
*/
.modal-xl .modal-dialog {
	max-width: 100% !important;
	height: 100% !important;
	margin: 0 !important;
}

.modal-mxl .modal-dialog {
	max-width: 70% !important;
	margin-left: 15% !important;
}

.modal-xmxl .modal-dialog {
	max-width: 88% !important;
	margin-left: 6% !important;
	margin-top: 2vh !important;
}

.modal-xl .modal-lg .modal-content {
	height: 100% !important;
	border: none;
	border-radius: 0 !important;
}

.modal-reagendar-locacao .modal-dialog {
	width: 613px !important;
}

/* 
    Use .smooth-scroll-hide-scroll to hide scrollbars.
*/
.smooth-scroll-hide-scroll .scrollbar-track {
	opacity: 0 !important;
	display: none !important;
}

/* 
    Hide Google reCAPTCHA v3
*/
.grecaptcha-badge {
	visibility: collapse !important;
}
