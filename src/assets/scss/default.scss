@import "variable";
@import "functions";

$themecolor: #b60011;
$themecolor-dark: #a6000c;
$topbar: #ffffff;
$themecolor-alt: #db8088;
$topbar-alt: #ffffff;
$sidebar-icons: #a6b7bf;

.default {
	/*******************
/*General Elements
*******************/

	a.link {
		&:hover,
		&:focus {
			color: $themecolor !important;
		}
	}

	/*******************
/*Buttons
*******************/

	.btn-primary {
		-webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.141),
			0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
		box-shadow: 0 2px 2px 0 rgba(131, 131, 131, 0.141),
			0 3px 1px -2px rgba(129, 128, 128, 0.2),
			0 1px 5px 0 rgba(97, 97, 97, 0.12);
	}

	.btn-themecolor,
	.btn-themecolor.disabled {
		background: $themecolor;
		color: $white;
		border: 1px solid $themecolor;

		&:hover {
			background: $themecolor;
			opacity: 0.7;
			border: 1px solid $themecolor;
		}

		&.active,
		&:focus {
			background: $themecolor-dark;
		}
	}
}
