$bodyfont: "Nunito Sans", sans-serif;
$headingfont: "Nunito Sans", sans-serif;

/* PACTO COLORS */
$pacto-main-primary: #09416d;
$pacto-treino-primary: #00426b;
$pacto-light-grey: #a5a5a5;
$pacto-dark-grey: #666666;

/*Theme Colors*/

$topbar: #1976d2;
$sidebar: #fff;
$sidebar-white: #e5edef;
$sidebar-alt: #edf0f5;
$bodycolor: #f4f6f9;
$headingtext: #455a64;
$bodytext: #67757c;
$sidebar-text: #687384;
$sidebar-icons: #555f6d;

$light-text: #a6b7bf;
$themecolor: #b60011;
$themecolor-alt: #db8088;
$themecolor-dark: #a6000c;

/*bootstrap Color*/
$danger: #ef5350;
$success: #06d79c;
$warning: #ffb22b;
$primary: #00426b;
$info: #398bf7;
$inverse: #2f3d4a;
$secondary: #cccccc;
$muted: #99abb4;
$dark: #263238;
$light: #e9edf2;
$extra-light: #ebf3f5;
$bglight: rgba(0, 0, 0, 0.02);

/*Light colors*/
$light-danger: #f9e7eb;
$light-success: #e8fdeb;
$light-warning: #fff8ec;
$light-primary: #fdefef;
$light-info: #cfecfe;
$light-inverse: #f6f6f6;
$light-megna: #e0f2f4;

$danger-dark: #e6294b;
$success-dark: #04b381;
$warning-dark: #e9ab2e;
$primary-dark: $pacto-treino-primary;
$info-dark: #028ee1;
$red-dark: #d61f1f;
$inverse-dark: #232a37;
$dark-transparent: rgba(0, 0, 0, 0.05);

$info-shadow: 0 2px 2px 0 rgba(66, 165, 245, 0.14),
	0 3px 1px -2px rgba(66, 165, 245, 0.2), 0 1px 5px 0 rgba(66, 165, 245, 0.12);
$info-shadow-hover: 0 14px 26px -12px rgba(23, 105, 255, 0.42),
	0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(23, 105, 255, 0.2);

$warning-shadow: 0 2px 2px 0 rgba(248, 194, 0, 0.14),
	0 3px 1px -2px rgba(248, 194, 0, 0.2), 0 1px 5px 0 rgba(248, 194, 0, 0.12);
$warning-shadow-hover: 0 14px 26px -12px rgba(248, 194, 0, 0.42),
	0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(248, 194, 0, 0.2);

$danger-shadow: 0 2px 2px 0 rgba(239, 83, 80, 0.14),
	0 3px 1px -2px rgba(239, 83, 80, 0.2), 0 1px 5px 0 rgba(239, 83, 80, 0.12);
$danger-shadow-hover: 0 14px 26px -12px rgba(239, 83, 80, 0.42),
	0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(239, 83, 80, 0.2);

$success-shadow: 0 2px 2px 0 rgba(40, 190, 189, 0.14),
	0 3px 1px -2px rgba(40, 190, 189, 0.2), 0 1px 5px 0 rgba(40, 190, 189, 0.12);
$success-shadow-hover: 0 14px 26px -12px rgba(40, 190, 189, 0.42),
	0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(40, 190, 189, 0.2);

$primary-shadow: 0 2px 2px 0 rgba(238, 96, 108, 0.141),
	0 3px 1px -2px rgba(238, 96, 96, 0.2), 0 1px 5px 0 rgba(238, 96, 115, 0.12);
$primary-shadow-hover: 0 14px 26px -12px rgba(238, 112, 96, 0.42),
	0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(238, 96, 108, 0.2);

$default-shadow: 0 2px 2px 0 rgba(169, 169, 169, 0.14),
	0 3px 1px -2px rgba(169, 169, 169, 0.2), 0 1px 5px 0 rgba(169, 169, 169, 0.12);
$default-shadow-hover: 0 14px 26px -12px rgba(169, 169, 169, 0.42),
	0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(169, 169, 169, 0.2);

/*Normal Color*/
$white: #ffffff;
$red: #fb3a3a;
$yellow: #a0aec4;
$purple: #7460ee;
$blue: #02bec9;
$megna: #56c0d8;

/*Extra Variable*/
$rgt: right;
$lft: left;
$border: rgba(120, 130, 140, 0.13);
$table-border: #f3f1f1;
$card-brd: #d7dfe3;
$dark-text: #848a96;
$radius: 4px;
$form-brd: #b1b8bb;

/*Preloader*/

.preloader {
	width: 100%;
	height: 100%;
	top: 0px;
	position: fixed;
	z-index: 99999;
	background: #fff;

	.cssload-speeding-wheel {
		position: absolute;
		top: calc(50% - 3.5px);
		left: calc(50% - 3.5px);
	}
}

$colors: (
	color-cinza-escuro: rgb(51, 51, 51),
	color-cinza-escuro-menos: rgb(85, 85, 85),
	color-cinza: rgb(165, 165, 165),
	color-cinza-claro: rgb(242, 242, 242),
	color-red: rgb(182, 0, 17),
	color-white: #ffffff,
);

$primaryColors: (
	pacto: (
		base: #00426b,
	),
	treino: (
		base: rgb(182, 0, 17),
		shadow: rgba(238, 96, 108, 0.141),
		shadow-hover: rgba(238, 112, 96, 0.42),
	),
	crossfit: (
		base: rgb(26, 26, 26),
		shadow: rgb(61, 61, 61),
		shadow-hover: rgb(153, 153, 153),
	),
	avaliacao: (
		base: #d4145a,
		shadow: #ce8fa6,
		shadow-hover: #eccbd7,
	),
	canalCliente: (
		base: #09416d,
		shadow: #3694ff,
		shadow-hover: #8cbcf3,
	),
);
