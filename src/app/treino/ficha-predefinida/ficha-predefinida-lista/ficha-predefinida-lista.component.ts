import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	TreinoApiAulaService,
	AulaBase,
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	GridFilterType,
	RelatorioComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoApiFichaService } from "treino-api";
import { PerfilAcessoRecursoNome } from "treino-api";

@Component({
	selector: "pacto-ficha-predefinida-lista",
	templateUrl: "./ficha-predefinida-lista.component.html",
	styleUrls: ["./ficha-predefinida-lista.component.scss"],
})
export class FichaPredefinidaListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;

	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("situacaoTranslator", { static: true }) situacaoTranslator;

	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;

	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarModalMsg", { static: true }) ativarModalMsg;

	data: ApiResponseList<AulaBase> = {
		content: [],
	};

	itemToRemove;
	loading = false;
	ready = false;

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private aulaService: TreinoApiAulaService,
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private ambienteService: TreinoApiAmbienteService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private fichaService: TreinoApiFichaService
	) {}

	table: PactoDataGridConfig;
	filterConfig: any;
	integracaoZW = false;
	nomeFichaPredefinida;
	permissoesFichasPredefinidas;

	ngOnInit() {
		this.buttonName.disable = false;
		this.loadAllow();
		this.configTable();
		this.integracaoZW = this.sessionService.integracaoZW;
		this.configFilters();
		this.ready = true;
		this.cd.detectChanges();
	}

	loadAllow() {
		this.permissoesFichasPredefinidas = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.FICHAS_PRE_DEFINIDAS
		);
	}

	btnClickHandler() {
		this.router.navigate([
			"treino",
			"cadastros",
			"fichas-predefinidas",
			"adicionar",
		]);
	}

	btnEditHandler(item) {
		this.router.navigate([
			"treino",
			"cadastros",
			"fichas-predefinidas",
			item.id,
		]);
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "situacao") {
			this.editarSituacaoHandler($event.row);
			this.cd.detectChanges();
		}
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
	}

	editarSituacaoHandler(item) {
		this.nomeFichaPredefinida = item.nome;
		setTimeout(() => {
			let modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
			let modalBody = this.inativarModalBody.nativeElement.innerHTML;
			let modalButton = this.tooltipInativar.nativeElement.innerHTML;
			let modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
			if (item.ativo === false) {
				modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
				modalBody = this.ativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipAtivar.nativeElement.innerHTML;
				modalMsg = this.ativarModalMsg.nativeElement.innerHTML;
			}
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				modalButton
			);
			handler.result.then(() => {
				const fichaPre = { ativo: item.ativo };
				this.fichaService
					.editarSituacaoFichaPreDefinida(item.id, fichaPre)
					.subscribe(() => {
						this.snotifyService.success(modalMsg);
						this.tableData.reloadData();
					});
			});
		});
	}

	private configTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		if (this.permissoesFichasPredefinidas.incluir) {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.rest.buildFullUrl("fichas/pre-definido"),
				logUrl: this.rest.buildFullUrl("log/fichas-predefinidas"),
				quickSearch: true,
				buttons: {
					conteudo: this.buttonName,
					nome: "add",
					id: "btn-nova-ficha-predefinida",
				},
				columns: [
					{
						nome: "nome",
						titulo: this.nomeColumnName,
						buscaRapida: true,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "nome",
					},
					{
						nome: "ativo",
						titulo: this.situacaoColumnName,
						buscaRapida: true,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						celula: this.statusColumn,
						campo: "ativo",
					},
				],
				actions: [
					{
						nome: "edit",
						iconClass: "fa fa-pencil",
						showIconFn: (row) =>
							row.ativo === true && this.permissoesFichasPredefinidas.editar,
						tooltipText: tooltipEditar,
					},
					{
						nome: "situacao",
						iconClass: "fa fa-check-square-o",
						tooltipText: this.tooltipInativar.nativeElement.innerHTML,
						showIconFn: (row) =>
							row.ativo === true && this.permissoesFichasPredefinidas.excluir,
					},
					{
						nome: "situacao",
						iconClass: "fa fa-minus-square-o",
						tooltipText: this.tooltipAtivar.nativeElement.innerHTML,
						showIconFn: (row) =>
							row.ativo === false && this.permissoesFichasPredefinidas.excluir,
					},
				],
			});
		} else {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.rest.buildFullUrl("fichas/pre-definido"),
				quickSearch: true,
				columns: [
					{
						nome: "nome",
						titulo: this.nomeColumnName,
						buscaRapida: true,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "nome",
					},
					{
						nome: "ativo",
						titulo: this.situacaoColumnName,
						buscaRapida: true,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						celula: this.statusColumn,
						campo: "ativo",
					},
				],
				actions: [
					{
						nome: "edit",
						iconClass: "fa fa-pencil",
						showIconFn: (row) =>
							row.ativo === true && this.permissoesFichasPredefinidas.editar,
						tooltipText: tooltipEditar,
					},
					{
						nome: "situacao",
						iconClass: "fa fa-check-square-o",
						tooltipText: this.tooltipInativar.nativeElement.innerHTML,
						showIconFn: (row) =>
							row.ativo === true && this.permissoesFichasPredefinidas.excluir,
					},
					{
						nome: "situacao",
						iconClass: "fa fa-minus-square-o",
						tooltipText: this.tooltipAtivar.nativeElement.innerHTML,
						showIconFn: (row) =>
							row.ativo === false && this.permissoesFichasPredefinidas.excluir,
					},
				],
			});
		}
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "situacaoFichaPreDefinida",
					label: this.situacaoColumnName,
					type: GridFilterType.MANY,
					translator: this.situacaoTranslator,
					options: [{ value: "true" }, { value: "false" }],
					initialValue: ["true"],
				},
			],
		};
	}
}
