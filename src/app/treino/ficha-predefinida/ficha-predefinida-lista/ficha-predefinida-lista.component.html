<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'Fichas Predefinidas'
		}"
		class="first"></pacto-breadcrumbs>
	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="fichasPredefinidas"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud-aula:title">Fichas Predefinidas</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-aulas:description">G<PERSON><PERSON>ie as fichas predefinidas.</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-aulas:input:nome:label">Nome</span>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@crud-aulas:input:nome:label">Situação</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-atividades:table:status">Ações</span>
</ng-template>

<!--end result coluna situacao-->
<ng-template #statusColumn let-item="item">
	<span
		*ngIf="item.ativo == true"
		i18n="@@crud-fichas-predefinidas:ativo:result">
		Ativo
	</span>
	<span
		*ngIf="item.ativo == false"
		i18n="@@crud-fichas-predefinidas:inativo:result">
		Inativo
	</span>
</ng-template>

<!--tooltip icons-->
<span
	#tooltipAtivar
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:ativar:tooltip-icon">
	Ativar
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:inativar:tooltip-icon">
	Inativar
</span>
<span
	#tooltipEditar
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:editar:tooltip-icon">
	Editar
</span>
<!--end tooltip icons-->

<!--tooltip icons-->
<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:remover:tooltip-icon">
	Remover
</span>

<ng-template #situacaoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'true'" i18n="@@crud-fichas-predefinidas:ativo:result">
			Ativa
		</span>
		<span
			*ngSwitchCase="'false'"
			i18n="@@crud-fichas-predefinidas:inativo:result">
			Inativa
		</span>
	</ng-container>
</ng-template>

<span #inativarModalBody [hidden]="true">
	Deseja inativar o Ficha Predefinida: {{ nomeFichaPredefinida }} ?
</span>
<span #ativarModalBody [hidden]="true">
	Deseja ativar a Ficha Predefinida: {{ nomeFichaPredefinida }} ?
</span>

<span #inativarModalTitle [hidden]="true">Inativar Ficha Predefinida ?</span>
<span #inativarModalMsg [hidden]="true">
	Ficha Predefinida inativada com sucesso.
</span>
<span #ativarModalTitle [hidden]="true">Ativar Ficha Predefinida ?</span>
<span #ativarModalMsg [hidden]="true">
	Ficha Predefinida ativada com sucesso.
</span>
