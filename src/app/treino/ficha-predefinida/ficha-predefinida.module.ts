import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoRecursoNome,
	PerfilAcessoRecurso,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

import { FichaPredefinidaListaComponent } from "./ficha-predefinida-lista/ficha-predefinida-lista.component";
import { FichaPredefinidaEditComponent } from "./ficha-predefinida-edit/ficha-predefinida-edit.component";
import { MontagemTreinoModule } from "../montagem-treino/montagem-treino.module";
import { FichasPreDefinidasGuard } from "@base-core/guards/fichas-pre-definidas.guard";

const recurso = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.FICHAS_PRE_DEFINIDAS,
	[
		PerfilRecursoPermissoTipo.CONSULTAR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const routes: Routes = [
	{
		path: "list",
		component: FichaPredefinidaListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "adicionar",
		component: FichaPredefinidaEditComponent,
		canActivate: [FichasPreDefinidasGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: FichaPredefinidaEditComponent,
		canActivate: [FichasPreDefinidasGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		MontagemTreinoModule,
	],
	declarations: [FichaPredefinidaListaComponent, FichaPredefinidaEditComponent],
})
export class FichaPredefinidaModule {}
