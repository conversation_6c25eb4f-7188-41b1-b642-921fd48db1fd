import { ModalService } from "@base-core/modal/modal.service";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	HostListener,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { SessionService } from "@base-core/client/session.service";
import { TraducoesXinglingComponent } from "ui-kit";
import { ConfigurarFichaService } from "../../montagem-treino/configurar-ficha.service";
import { ModalAdicionarAtividadesComponent } from "../../montagem-treino/modal-adicionar-atividades/modal-adicionar-atividades.component";
import { RestService } from "@base-core/rest/rest.service";
import {
	FichaPrograma,
	TreinoApiFichaService,
	TreinoApiAulaService,
	Programa,
} from "treino-api";
import { TreinoApiCategoriaFichaService } from "treino-api";

@Component({
	selector: "pacto-ficha-predefinida-edit",
	templateUrl: "./ficha-predefinida-edit.component.html",
	styleUrls: ["./ficha-predefinida-edit.component.scss"],
})
export class FichaPredefinidaEditComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("atividadeTemplate", { static: true }) atividadeTemplate;
	@ViewChild("modalTranslate", { static: true })
	modalTranslate: TraducoesXinglingComponent;
	@ViewChild("modalAdicionarAtividadesComponent", { static: false })
	modalAdicionarAtividadesComponent: ModalAdicionarAtividadesComponent;

	adicionandoAtividade = false;
	categoriaFicha: Array<any> = [];
	ficha: FichaPrograma;
	operation: string;
	entity = true;
	integracaoZw;
	nomeFicha = "";
	fichaSendoModificada = false;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		categoriaId: new FormControl(null, [Validators.required]),
		mensagem: new FormControl(null),
		predefinida: new FormControl(true),
		ativo: new FormControl(true),
	});

	constructor(
		@Inject(LOCALE_ID) private locale,
		private fichaService: TreinoApiFichaService,
		private modalService: ModalService,
		private notificationService: SnotifyService,
		private aulaService: TreinoApiAulaService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private configurarFichaService: ConfigurarFichaService,
		private snotify: SnotifyService,
		private session: SessionService,
		private categoriaFichaService: TreinoApiCategoriaFichaService,
		private rest: RestService
	) {}

	ngOnInit() {
		this.integracaoZw = this.session.integracaoZW;
		this.setSelects();
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}
		});
	}

	private programaFake(item): Programa {
		item.dias_semana = "";
		item.predefinida = true;
		const p = {
			fichas: [item],
			alunoId: null,
			id: null,
			inicio: null,
			nome: null,
			professorMontou: null,
			qtdDiasSemana: null,
			revisao: null,
			termino: null,
			totalTreinos: null,
			treinosConcluidos: null,
		};
		return new Programa(p);
	}

	private newFicha(): any {
		const f = {
			atividades: [],
			ativo: true,
			categoria: null,
			dias_semana: "",
			id: null,
			mensagem: "",
			nome: "",
			predefinida: true,
			tipo_execucao: null,
		};
		return f;
	}

	private loadEntities(id) {
		if (id) {
			this.fichaService.obterFichasPreDefinidosById(id).subscribe((dados) => {
				if (dados) {
					this.ficha = dados;
					this.entity = false;
					this.loadForm();
					this.configurarFichaService.selecaoFichaIniciada = false;
					this.configurarFichaService.programa$.next(
						this.programaFake(this.ficha)
					);
					this.configurarFichaService.selecionarFichaInicial(0);
				} else {
					console.log("erro");
				}
				this.vincForm(false);
			});
		} else {
			this.configurarFichaService.selecaoFichaIniciada = false;
			this.configurarFichaService.programa$.next(
				this.programaFake(this.newFicha())
			);
			this.configurarFichaService.selecionarFichaInicial(0);
			this.vincForm(false);
		}
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.ficha.nome);
		const regex = new RegExp(this.ficha.categoria.id, "i");
		if (this.categoriaFicha.find((item) => regex.test(item.id)) !== undefined) {
			this.formGroup.get("categoriaId").setValue(this.ficha.categoria.id);
		}
		this.formGroup.get("mensagem").setValue(this.ficha.mensagem);
		this.formGroup.get("ativo").setValue(this.ficha.ativo);
	}

	private setSelects() {
		this.categoriaFichaService
			.obterTodasCategoriasFicha()
			.subscribe((dados) => {
				this.categoriaFicha = dados;
				this.cd.detectChanges();
			});
	}

	excluirFichaHandler() {
		const fichaAtual = this.configurarFichaService.obterFichaAtual();
		console.log(fichaAtual);
		if (fichaAtual) {
			this.nomeFicha = fichaAtual.nome;
			setTimeout(() => {
				const modal = this.modalService.confirm(
					this.modalTranslate.getLabel("ficha-remove-title"),
					this.modalTranslate.getLabel("ficha-remove-body"),
					this.modalTranslate.getLabel("ficha-remove-action")
				);
				modal.result.then(() => {
					this.configurarFichaService.removerFicha().subscribe(
						() => {
							this.cd.detectChanges();
							this.snotify.success(
								this.notificacoesTranslate.getLabel("ficha-remove-success")
							);
							this.router.navigate([
								"treino/cadastros/fichas-predefinidas/list",
							]);
						},
						() => {
							this.snotify.error(
								this.notificacoesTranslate.getLabel("ficha-delete-error")
							);
						}
					);
				});
			});
		}
	}

	validaFichaHandler(evento) {
		this.fichaSendoModificada = evento;
	}

	addAtividade(evento) {
		this.adicionandoAtividade = true;
		this.cd.detectChanges();
		this.modalAdicionarAtividadesComponent.init();
	}

	closeAtividade() {
		this.adicionandoAtividade = false;
	}

	salvarFichaHandler() {
		if (this.configurarFichaService.atividadesFichaView$.value.length < 1) {
			this.notificationService.warning(
				"A ficha predefinida deve ter pelo menos 1 atividade adicionada."
			);
		} else {
			this.configurarFichaService.salvarFicha().subscribe(
				(result) => {
					if (result) {
						this.snotify.success(
							this.notificacoesTranslate.getLabel("ficha-edit-success")
						);
						this.router.navigate(["treino/cadastros/fichas-predefinidas/list"]);
					} else {
						this.snotify.error(
							this.notificacoesTranslate.getLabel("ficha-edit-error")
						);
					}
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const error = httpResponseError.error.meta.message;
					this.snotify.error(error);
				}
			);
		}
	}

	markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("categoriaId").markAsTouched();
		this.formGroup.get("mensagem").markAsTouched();
	}

	vincForm(modificada) {
		if (this.configurarFichaService.fichaFormGroup) {
			this.configurarFichaService.fichaFormGroup.patchValue(
				this.formGroup.getRawValue()
			);
			if (this.configurarFichaService.atividadesFichaView$.value.length > 0) {
				this.configurarFichaService.fichaSendoModificada$.next(modificada);
			} else {
				this.configurarFichaService.fichaSendoModificada$.next(false);
			}
		}
	}

	get logUrl() {
		if (this.ficha) {
			return this.rest.buildFullUrl(`log/ficha-predefinida/${this.ficha.id}`);
		}
	}

	@HostListener("window:popstate")
	back() {
		if (this.configurarFichaService.atividadesFichaView$.value.length < 1) {
			this.notificationService.warning(
				"A ficha predefinida deve ter pelo menos 1 atividade adicionada."
			);
			history.forward();
		}
	}
}
