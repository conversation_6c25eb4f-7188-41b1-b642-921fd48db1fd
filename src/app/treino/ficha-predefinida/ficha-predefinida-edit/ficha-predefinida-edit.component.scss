@import "src/assets/scss/pacto/plataforma-import.scss";
/* Variáveis comuns */
$sticky-top: 0;
$z-index-sticky: 1;
$max-width-full: 100%;
$padding-standard: 5px 0px;
$background-color-white: white;
$border-color: #cccccc;
.actions {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;

	button {
		margin-left: 15px;
	}
}

::ng-deep .title-card-numero-exercicio {
	padding: 16px 20px 10px 20px !important;
}

.center-aux {
	width: calc(100vw - 344px);

	@media (max-width: $plataforma-breakpoint-large) {
		width: calc(100vw);
	}
}

@media (min-width: 768px) {
	.form-check {
		margin-top: 32px;
	}
}

.body-row {
	display: flex;
	justify-content: center;
	flex-grow: 1;
	margin-bottom: 16px;
}

.listagem-atividades {
	margin-bottom: 160px !important;
}

.input-row {
	display: flex;

	.form-group {
		padding-right: 15px;
		margin-bottom: 0px;
		flex-grow: 1;
	}

	.btn-position {
		height: 34px;
		margin-top: 30px;
	}
}

table {
	i {
		cursor: pointer;
	}

	.action-column {
		width: 50px;
		text-align: center;
	}
}

.position-validar {
	margin: 35px 35px 35px 0px;
}

.btn-cadastros-position {
	height: 34px;
	margin-top: 30px;
	margin-left: -10px;
}

.check-marcacao {
	margin-top: 35px;
}

.check-moda-contrato {
	margin-left: 25px;
	margin-top: 35px;
}

label.control-label {
	font-weight: 600;
}

.center-aux,
.container,
.row-form,
.title-card-adicionar-atividade,
.listagem-atividades,
.colunas-atividade {
	max-width: $max-width-full;
	width: $max-width-full;
}

.container {
	margin: 0;
}

.title-card-adicionar-atividade {
	display: flex;
	justify-content: space-between;
	padding: $padding-standard;
}

.card-info-tabs-ficha {
	position: sticky;
	top: $sticky-top;
	z-index: $z-index-sticky;
	background-color: $background-color-white;
}

.listagem-atividades {
	margin-bottom: 40px !important;
}

.colunas-atividade {
	justify-content: space-between;
}

.view-header {
	border-bottom: 2px solid $border-color;
}

.ficha-tabs {
	display: flex;
	width: 100%;
	flex-wrap: wrap;

	.ficha {
		@extend .type-h6;
		height: 32px;
		cursor: pointer;
		padding: 5px 10px 5px;
		margin-right: 4px;
		border: 1px solid #ccc;
		background-color: #fbfbfc;
		color: #9298a0;
		border-width: 1px 1px 1px 1px;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		text-align: center;
		width: 144px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		&.selected {
			color: #51555a;
			background-color: $branco;
			border-bottom: 2px solid #1998fc;
			border-width: 2px 2px 2px 2px;
			font-family: Nunito Sans;
			font-size: 16px;
			font-weight: 600;
		}
	}
}

.ficha-nova {
	width: 32px;
	height: 32px;
	display: flex;
	justify-content: center;
	align-items: center;
	@extend .type-h6;
	cursor: pointer;
	border: 2px solid $azulimPri;
	background-color: $azulimPri;
	border-width: 2px 2px 0px 2px;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: clip;
	color: white;
	font-size: 14px;
}

.card-info-tabs-ficha {
	display: flex;
	padding-top: 7px;
	background-color: white;
	width: 100%;
	border-bottom: 1px solid $cinza02;
}

.footer {
	background-color: #eff2f7;
	padding: 24px 18%;
	bottom: 0;
	left: auto;
	position: fixed;
	width: 100%;

	box-shadow: 0px -2px 4px 0px #e4e5e6;

	.margin-right-16px {
		margin-right: 16px;
	}

	::ng-deep {
		&.btn-excluir-ficha button {
			border: 1px solid #fafafa;
			background: #fafafa;
			color: $hellboyPri;
		}

		&.pct-trash-2 {
			color: $hellboyPri !important;
		}

		&.btn-default button {
			border: 1px solid #fafafa;
			background: #fafafa;
			color: $azulPacto02;
		}

		&.pct-bookmark {
			color: $azulPacto02 !important;
		}

		&.pct-settings {
			color: $azulPacto02 !important;
		}

		&.pct-send {
			color: $azulPacto02 !important;
		}
	}
}

::ng-deep .footer pacto-log {
	.btn {
		margin-right: 16px;
		height: 100%;
		background: $azulim05;
		border: 1px solid $azulim05;
	}

	.btn:hover {
		background: $azulim05;
		border: 1px solid $azulim05;
	}
}

.posicionamento-responsivo {
	::ng-deep#show-log {
		width: 50px;
		height: 43px;
	}
}

@media (max-width: 1200px) {
	.posicionamento-responsivo {
		bottom: 20px;
	}
}

@media (min-width: 1000px) {
	.posicionamento-responsivo {
		padding-right: 11%;
	}
}

@media (min-width: 1400px) {
	.posicionamento-responsivo {
		padding-right: 10%;
	}
}

@media (min-width: 1550px) {
	.posicionamento-responsivo {
		padding-right: 21%;
	}
}

@media (min-width: 1800px) {
	.posicionamento-responsivo {
		padding-right: 18%;
	}
}

::ng-deep .row {
	margin-left: 0 !important;
	margin-right: 0 !important;
}

.ficha-tabs-alt {
	display: flex;
	width: 80%;
	flex-wrap: wrap;
	height: 32px;

	.ficha {
		@extend .type-h6;
		height: 32px;
		cursor: pointer;
		padding: 5px 10px 5px;
		margin-right: 4px;
		border: 1px solid #ccc;
		background-color: #fbfbfc;
		color: #9298a0;
		border-width: 1px 1px 0px 1px;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		text-align: center;
		width: 144px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		&.selected {
			color: #51555a;
			background-color: $branco;
			border-width: 2px 2px 0px 2px;
			font-family: Nunito Sans;
			font-size: 16px;
			font-weight: 600;
		}
	}

	.ficha-nova {
		width: 32px;
		height: 32px;
		display: flex;
		justify-content: center;
		align-items: center;
		@extend .type-h6;
		cursor: pointer;
		border: 2px solid $azulimPri;
		background-color: $azulimPri;
		border-width: 2px 2px 0px 2px;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: clip;
		color: white;
		font-size: 14px;
	}
}
