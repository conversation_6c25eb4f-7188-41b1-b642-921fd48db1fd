<ng-template #cardTitle>
	<span
		*ngIf="entity"
		i18n="@@crud-ficha-predefinida:criar-ficha-predefinida:title">
		Criar Ficha Predefinida
	</span>
	<span
		*ngIf="!entity"
		i18n="@@crud-ficha-predefinida:editar-ficha-predefinida:title">
		Editar Ficha Predefinida
	</span>
</ng-template>

<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'Fichas Predefinidas'
		}"></pacto-breadcrumbs>

	<pacto-title-card [title]="cardTitle">
		<div class="row">
			<div class="col-lg-6">
				<pacto-input
					(change)="vincForm(true)"
					[control]="formGroup.get('nome')"
					[id]="'nome-ficha-input'"
					i18n-label="@@crud-ficha-predefinida:nome:label"
					i18n-mensagem="@@crud-ficha-predefinida:nome:mensagem"
					i18n-placeholder="@@crud-ficha-predefinida:nome:placeholder"
					label="Nome"
					mensagem="Defina um nome com pelo menos 3 caracteres."
					placeholder="Nome da Ficha"></pacto-input>
			</div>
			<div class="col-lg-6">
				<div class="form-group">
					<div class="form-check">
						<input
							(change)="vincForm(true)"
							[formControl]="formGroup.get('ativo')"
							class="form-check-input"
							id="defaultCheck1"
							type="checkbox"
							value="" />
						<label
							class="form-check-label"
							for="defaultCheck1"
							i18n="@@crud-ficha-predefinida:ativa:label">
							Ficha ativa ?
						</label>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-md-5">
				<pacto-select
					(change)="vincForm(true)"
					[control]="formGroup.get('categoriaId')"
					[id]="'categoria-select'"
					[nome]="'categoriaFicha'"
					[opcoes]="categoriaFicha"
					i18n-label="@@crud-ficha-predefinida:select:categoria:label"
					i18n-mensagem="@@crud-ficha-predefinida:select:categoria:mensagem"
					label="Categoria de Ficha"
					mensagem="Selecione uma categoria."></pacto-select>
			</div>
			<div class="col-md-5">
				<pacto-input
					(change)="vincForm(true)"
					[control]="formGroup.get('mensagem')"
					[id]="'mensagem-aluno-input'"
					[maxLength]="50"
					i18n-label="@@crud-ficha-predefinida:input:mensagem:label"
					i18n-placeholder="@@crud-ficha-predefinida:input:mensagem:placeholder"
					label="Mensagem ao aluno"
					placeholder="Informe uma mensagem para ser impressa na ficha do aluno."></pacto-input>
			</div>
		</div>
		<div>
			<div class="center-aux">
				<pacto-adicionar-atividade
					(addAtividade)="addAtividade($event)"
					(validaFicha)="validaFichaHandler($event)"
					[fichaFormGroup]="formGroup"
					[origemFichaPredefinida]="true"></pacto-adicionar-atividade>
			</div>
		</div>

		<div class="listagem-atividades">
			<div class="center-aux">
				<pacto-lista-atividades
					[atividades]="ficha?.atividades"></pacto-lista-atividades>
			</div>
		</div>

		<footer
			class="footer d-flex justify-content-end align-items-center posicionamento-responsivo">
			<div>
				<pacto-log [titulo]="nomeLog" [url]="logUrl"></pacto-log>
				<ng-template #nomeLog>
					<span>{{ ficha.nome }}</span>
				</ng-template>
			</div>
			<div class="margin-right-16px btn-excluir-ficha">
				<pacto-cat-button
					(click)="excluirFichaHandler()"
					[icon]="'pct pct-trash-2'"
					[v2]="true"
					i18n="@@perfil-aluno-programa-editar:excluir"
					label="Excluir ficha"></pacto-cat-button>
			</div>
			<div>
				<pacto-cat-button
					(click)="salvarFichaHandler()"
					[disabled]="!fichaSendoModificada"
					[icon]="'pct pct-check'"
					[v2]="true"
					i18n-label="@@adicionar-atividade:salvarFicha"
					label="Salvar Ficha"></pacto-cat-button>
			</div>
		</footer>
	</pacto-title-card>
</pacto-cat-layout-v2>

<pacto-modal-adicionar-atividades
	(fechar)="closeAtividade()"
	(validaFicha)="validaFichaHandler($event)"
	*ngIf="adicionandoAtividade"
	[ficha]="ficha"></pacto-modal-adicionar-atividades>
<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@crud-aulas:create-success" xingling="createSuccess">
		Aula criada com sucesso.
	</span>
	<span i18n="@@crud-aulas:create-edit" xingling="editSuccess">
		Aula editada com sucesso.
	</span>
	<span i18n="@@crud-aulas:campo-obrigatorio" xingling="campoObrigatorio">
		Campos obrigatórios não preenchido.
	</span>
	<span
		i18n="@@crud-aulas:campo-obrigatorio:inserir-horario"
		xingling="inserirHorario">
		Inserir pelo menos um horário na lista
	</span>
	<span i18n="@@crud-aulas:validacao:data" xingling="validacaoData">
		Data inicial não pode ser superior a data final
	</span>
	<span i18n="@@crud-aulas:validacao:horario" xingling="validacaoHorario">
		Horário cadastrado invalido, tente novamente
	</span>
	<span i18n="@@crud-aulas:validacao:dia semana" xingling="validacaoDiaSemana">
		Selecione ao menos um dia da semana.
	</span>
	<span xingling="horarionaoPodeSerRemovido">
		Não é possível excluir o horário, contém aula confirmada.
	</span>
	<span xingling="erro_aula_url_virtual">Informe a URL da aula virtual.</span>
	<span i18n="@@crud-aulas:erro_incluir_aula" xingling="erro_incluir_aula">
		Ocorreu um erro ao incluir a aula, tente novamente em alguns instantes!
	</span>
	<span xingling="ficha-edit-success">Ficha alterada com sucesso.</span>
	<span xingling="ficha-edit-error">
		Não foi possível salvar as alterações da ficha.
	</span>
	<span xingling="ficha-delete-error">Não foi possível deletar a ficha.</span>
	<span xingling="ficha-remove-success">Ficha excluida com sucesso.</span>
	<span xingling="ficha-create-success">Ficha criada com sucesso.</span>
	<span xingling="ficha-duplic-error">
		Já existe uma ficha cadastrada com o nome {{ nomeFicha }}.
	</span>
	<span xingling="ficha-predefinida-success">
		Ficha {{ nomeFicha }} foi adicionado a suas predefinidas.
	</span>
	<span xingling="usuariosempermissao">
		O usuário não possui permissão para Predefinir uma Ficha.
	</span>
	<span i18n="@@crud-ambientes:success:create" xingling="successCreate">
		Ambiente criado com sucesso.
	</span>
	<span
		i18n="@@crud-modalidade:create-modal:success"
		xingling="mensagemCreateSuccess">
		Modalidade criada com sucesso.
	</span>
</pacto-traducoes-xingling>
<pacto-traducoes-xingling #modalTranslate>
	<span xingling="ficha-remove-title">Excluir ficha</span>
	<span xingling="ficha-remove-body">
		Deseja excluir a ficha {{ nomeFicha }}?
	</span>
	<span xingling="ficha-remove-action">OK</span>
</pacto-traducoes-xingling>
