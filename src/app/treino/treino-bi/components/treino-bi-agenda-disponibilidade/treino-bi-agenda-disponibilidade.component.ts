import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
	OnDestroy,
	Input,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { TreinoBiAgenda } from "treino-api";
import { ModalRelatorioConfig } from "./relatorios.config";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfigDto,
	PactoDataGridConfig,
	RelatorioComponent,
	PactoColorScheme,
	PactoIcon,
	PieChartSet,
} from "ui-kit";

declare var moment;
import { relatorios } from "./relatorios.config";
import { RestService } from "@base-core/rest/rest.service";
import { Subscription } from "rxjs";
import { ShareComponent } from "src/app/share/share.component";
import { AgendaBiStateService } from "../../../../agenda/agenda/agenda-bi/agenda-bi-state";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

@Component({
	selector: "pacto-treino-bi-agenda-disponibilidade",
	templateUrl: "./treino-bi-agenda-disponibilidade.component.html",
	styleUrls: ["./treino-bi-agenda-disponibilidade.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreinoBiAgendaDisponibilidadeComponent
	implements OnInit, OnDestroy
{
	@Input() professorFiltro;
	@Input() dataInicio;
	@Input() dataFim;
	@ViewChild("traducoes", { static: true }) traducoes;
	chartColors;

	comportamentoFc = new FormControl();
	comportamentos: any[] = [];
	porComportamento: any = {};
	pieSeries: PieChartSet[];
	config: PactoDataGridConfigDto;

	private subscriptions: Subscription[] = [];
	endpointShare: string;
	relatorio: RelatorioComponent;
	primeroOnInit: boolean;

	constructor(
		private agendaBIState: AgendaBiStateService,
		private modal: ModalService,
		private session: SessionService,
		private rest: RestService,
		private cd: ChangeDetectorRef
	) {
		this.chartColors = PactoColorScheme.getDefaultChartScheme();
		this.primeroOnInit = true;
	}

	get comportamento() {
		return this.comportamentoFc.value;
	}

	ngOnInit() {
		this.subscriptions.push(
			this.agendaBIState.update$.subscribe((ready) => {
				if (ready) {
					this.buildGridData();
					this.buildPieChartData();
					setTimeout(() => {
						this.cd.detectChanges();
					});
				}
			})
		);
		this.subscriptions.push(
			this.comportamentoFc.valueChanges.subscribe(() => {
				this.buildPieChartData();
				setTimeout(() => {
					this.cd.detectChanges();
					if (!this.primeroOnInit) {
						this.session.notificarRecursoEmpresa(
							RecursoSistema.CLIENTE_TROCOU_TIPO_DE_AGENDAMENTO
						);
					} else {
						this.primeroOnInit = false;
					}
				});
			})
		);
	}

	ngOnDestroy() {
		this.subscriptions.forEach((subscription) => {
			subscription.unsubscribe();
		});
	}

	openModalHandler(name, numeroIndicador) {
		let size = "";
		if (name === "agendados") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.RESUMO_CLIQUES_AGENDADOS
			);
			size = "modal-mxl";
		} else if (name === "executados") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.RESUMO_CLIQUES_EXECUTADOS
			);
		} else if (name === "faltaram") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.RESUMO_CLIQUES_FALTAS
			);
		} else if (name === "cancelados") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.RESUMO_CLIQUES_CANCELADOS
			);
			size = "modal-mxl";
		}
		this.buildModalHandler(relatorios[name], numeroIndicador, size);
	}

	private buildModalHandler(
		item: ModalRelatorioConfig,
		numeroIndicador: number,
		size: string
	) {
		this.endpointShare = this.rest.buildFullUrl(item.endpoint);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE,
			size
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.telaId = item.title;
		this.relatorio.sessionService = this.session;
		this.relatorio.btnClick.subscribe(() => {
			this.openModalShare(item);
		});
		if (numeroIndicador > 0) {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(item.endpoint),
				logUrl: this.rest.buildFullUrl(
					"log/listar-log-exportacao/" + item.title
				),
				pagination: true,
				quickSearch: true,
				exportButton: false,
				rowClick: false,
				columns: [],
			};
		} else {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(item.endpoint),
				pagination: true,
				quickSearch: true,
				exportButton: false,
				rowClick: false,
				columns: [],
			};
		}
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (column.date) {
				columnConfig.valueTransform = (d) => moment(d).format("DD/MM/YYYY");
			}
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				professorId: this.professorFiltro,
				inicio: this.dataInicio,
				fim: this.dataFim,
			},
		};
	}

	private openModalShare(item: ModalRelatorioConfig) {
		const pctModal = this.modal.open(
			"Compartilhar",
			ShareComponent,
			PactoModalSize.MEDIUM
		);
		const relatorio: RelatorioComponent = pctModal.componentInstance;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.endpointShare,
			columns: [],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			config.columns.push(columnConfig);
		});
		relatorio.table = new PactoDataGridConfig(config);
		const filtroModal = this.relatorio.fetchFiltros();
		const filtroProf = {
			filters: {
				professorId: this.professorFiltro,
			},
		};
		relatorio.baseFilter = Object.assign(filtroModal, filtroProf);
		relatorio.baseFilter.size = 999999;
	}

	private buildGridData() {
		const agenda = this.agendaBIState.agenda;
		if (agenda && agenda.agendamentosPorTipoComportamento) {
			this.comportamentos = [];
			this.porComportamento = {};

			agenda.agendamentosPorTipoComportamento.forEach((tipo) => {
				this.comportamentos.push({
					id: tipo.tipo,
					label: tipo.tipo,
				});
				this.porComportamento[tipo.tipo] = {
					disponibilidade: tipo.disponibilidade,
					executaram: tipo.executaram,
					cancelaram: tipo.cancelaram,
					faltaram: tipo.faltaram,
					aguardandoConfirmacao: tipo.aguardandoConfirmacao,
					confirmado: tipo.confirmado,
				};
			});
		}

		if (this.comportamentos.length) {
			this.comportamentoFc.setValue(this.comportamentos[0].id);
		}
	}

	private buildPieChartData() {
		const comportamentoData = this.porComportamento[this.comportamento];
		if (comportamentoData) {
			this.pieSeries = [
				{ name: "Aguardando", data: comportamentoData.aguardandoConfirmacao },
				{ name: "Confirmado", data: comportamentoData.confirmado },
				{ name: "Executaram", data: comportamentoData.executaram },
				{ name: "Faltaram", data: comportamentoData.faltaram },
				{ name: "Cancelaram", data: comportamentoData.cancelaram },
			];
		}
	}

	get agenda(): TreinoBiAgenda {
		return this.agendaBIState.agenda;
	}

	get PactoIcon() {
		return PactoIcon;
	}

	pieFormatter = (v) => {
		return v + "H";
	};
}
