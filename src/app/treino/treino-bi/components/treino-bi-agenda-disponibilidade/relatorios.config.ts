export interface ModalRelatorioConfig {
	title: string;
	pagination?: boolean;
	endpoint: string;
	columns: {
		value: any;
		date?: boolean;
		ordenavel?: boolean;
	}[];
}

export const relatorios: { [relatorio: string]: ModalRelatorioConfig } = {
	agendados: {
		title: "agendadosTitle",
		endpoint: "treino-bi/agendamentos",
		columns: [
			{ value: "evento" },
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "nomeProfessor" },
			{ value: "inicio", date: true, ordenavel: false },
		],
	},
	executados: {
		title: "executadosTitle",
		endpoint: "treino-bi/agendamento-executaram",
		columns: [
			{ value: "evento" },
			{ value: "nomeAluno" },
			{ value: "nomeProfessor" },
			{ value: "inicio", date: true, ordenavel: false },
		],
	},
	faltaram: {
		title: "faltaramTitle",
		endpoint: "treino-bi/agendamento-faltaram",
		columns: [
			{ value: "evento" },
			{ value: "nomeAluno" },
			{ value: "nomeProfessor" },
			{ value: "inicio", date: true, ordenavel: false },
		],
	},
	cancelados: {
		title: "canceladosTitle",
		endpoint: "treino-bi/agendamento-cancelaram",
		columns: [
			{ value: "evento" },
			{ value: "nomeAluno" },
			{ value: "nomeProfessor" },
			{ value: "inicioComHora" },
			{ value: "ultimaAlteracaoStr" },
			{ value: "fonteCancelamento" },
		],
	},
	professores: {
		title: "professoresTitle",
		endpoint: "treino-bi/disponibilidades/professores",
		columns: [{ value: "codigo", ordenavel: false }, { value: "nome" }],
	},
	novosTreinos: {
		title: "novosTreinosTitle",
		endpoint: "treino-bi/disponibilidades/treinos-novos",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAluno" },
			{ value: "dataPrograma", ordenavel: false },
			{ value: "dataLancamento", date: true, ordenavel: false },
		],
	},
	treinosRenovados: {
		title: "renovadosTreinosTitle",
		endpoint: "treino-bi/treinos-renovados",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAluno" },
			{ value: "dataPrograma", ordenavel: false },
			{ value: "dataLancamento", date: true, ordenavel: false },
		],
	},
	horasDisponibilidade: {
		title: "horasDisponibilidadeTitle",
		endpoint: "treino-bi/disponibilidades/horas-disponibilidade",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAluno" },
			{ value: "dataPrograma", ordenavel: false },
			{ value: "dataLancamento", date: true, ordenavel: false },
		],
	},
	ocupacao: {
		title: "ocupacaoTitle",
		endpoint: "treino-bi/percentual-ocupacao",
		columns: [
			{ value: "nomeAluno" },
			{ value: "evento" },
			{ value: "nomeProfessor" },
		],
	},
	avaliacoesFisicas: {
		title: "avaliacoesFisicaTitle",
		endpoint: "treino-bi/avaliacoes-fisicas",
		columns: [
			{ value: "nomeAluno" },
			{ value: "evento" },
			{ value: "inicio", date: true, ordenavel: false },
			{ value: "nomeProfessor" },
		],
	},
	semAgendamentoAvaliacaoFisica: {
		title: "avaliacoesFisicaSemAgendamentoTitle",
		endpoint: "treino-bi/avaliacoes-fisicas-nao-realizado",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	comAgendamentoAvaliacaoFisica: {
		title: "avaliacoesFisicaComAgendamentoTitle",
		endpoint: "treino-bi/avaliacoes-fisicas-realizado",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	aulas: {
		title: "aulasTitle",
		endpoint: "bi-agenda/listagem-aulas",
		columns: [
			{ value: "aula" },
			{ value: "diaSemana" },
			{ value: "modalidade" },
			{ value: "presencas" },
			{ value: "ocupacao" },
			{ value: "frequencia" },
		],
	},
	modalidades: {
		title: "modalidadesTitle",
		endpoint: "bi-agenda/listagem-modalidades",
		columns: [
			{ value: "modalidade" },
			{ value: "professores", ordenavel: false },
			{ value: "aulas" },
			{ value: "ocupacao" },
			{ value: "frequencia" },
		],
	},
	professoresDashAgenda: {
		title: "professoresDashAgendaTitle",
		endpoint: "bi-agenda/listagem-professores",
		columns: [
			{ value: "professor" },
			{ value: "modalidades", ordenavel: false },
			{ value: "aulas" },
			{ value: "ocupacao" },
			{ value: "frequencia" },
		],
	},
	frequenciaAlunos: {
		title: "frequenciaAlunos",
		endpoint: "bi-agenda/listagem-frequencia-alunos",
		columns: [
			{ value: "nome" },
			{ value: "modalidades", ordenavel: false },
			{ value: "aulas", ordenavel: false },
			{ value: "frequencia" },
		],
	},
	presencasAlunos: {
		title: "frequenciaAlunos",
		endpoint: "bi-agenda/listagem-presenca",
		columns: [
			{ value: "nomeAluno" },
			{ value: "nomeProfessor", ordenavel: false },
			{ value: "nomeModalidade", ordenavel: false },
			{ value: "aula", ordenavel: false },
			{ value: "dia", date: true, ordenavel: false },
		],
	},
};
