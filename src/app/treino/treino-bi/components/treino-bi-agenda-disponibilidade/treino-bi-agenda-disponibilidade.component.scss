@import "src/assets/scss/pacto/plataforma-import.scss";

.agenda-title {
	@extend .type-h5;
	color: $pretoPri;
}

.resumo-agenda {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.top-row {
	display: flex;
	justify-content: space-between;
}

.center-row {
	display: flex;
	align-items: center;
	align-content: center;
	margin-top: 20px;
	flex-grow: 1;

	pacto-cat-pie-chart {
		position: relative;
		flex-shrink: 0;
		flex-basis: 40%;
		top: 8px;
		right: 8px;
	}
}

pacto-cat-chart-legend,
pacto-cat-pie-chart {
	display: none;
	@media (min-width: $bootstrap-breakpoint-xl) {
		display: block;
	}
}

.values-grid {
	display: grid;
	grid-template-rows: 1fr 1fr;
	grid-template-columns: 1fr 1fr 1fr;
	grid-column-gap: 20px;
	grid-row-gap: 60px;
	flex-grow: 1;
	text-align: center;

	.grid-item-label {
		@extend .type-h6;
		color: $cinza05;
	}

	.grid-item-value {
		@extend .type-h3-bold;
		color: $pretoPri;
	}
}

.resumo-grid {
	display: grid;
	margin-top: 30px;
	grid-template-rows: 1fr 1fr;
	grid-template-columns: 1fr 1fr;
	grid-gap: 30px;
}

.disponibilidade-agendamentos-aux {
	margin-top: 30px;
	@media (min-width: $bootstrap-breakpoint-lg) {
		margin-top: 0px;
	}
}

.button-compartilhar {
	display: -webkit-inline-box;
	font-size: small;
}

.btn-primary {
	color: $branco;
	background-color: $azulimPri !important;
	border-color: $azulimPri !important;
}

pacto-cat-card-plain {
	height: 100%;
}
