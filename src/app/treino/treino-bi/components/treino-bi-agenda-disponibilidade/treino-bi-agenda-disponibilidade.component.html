<div class="row">
	<div class="col-md-12 col-lg-5 col-xl-4 movimentacao-result-aux">
		<pacto-cat-card-plain>
			<div class="disponibilidade-agendamentos">
				<div class="type-h5 agenda-title" i18n="@@treino-bi:resumo-agenda">
					Resumo da agenda
				</div>
				<div class="resumo-grid">
					<pacto-cat-sm-info-block-icon
						(click)="openModalHandler('agendados', agenda?.agendamentos)"
						[icon]="PactoIcon.PCT_CALENDAR"
						[label]="'Agendados'"
						[value]="agenda?.agendamentos"
						id="total-agendados"
						title="Total de agendamentos realizados no período em dias (definido nas configurações da empresa)."></pacto-cat-sm-info-block-icon>
					<pacto-cat-sm-info-block-icon
						(click)="openModalHandler('executados', agenda?.executados)"
						[icon]="PactoIcon.PCT_TOGGLE_RIGHT"
						[value]="agenda?.executados"
						i18n-label="@@treino-bi:agenda:executados"
						id="total-executados"
						label="Executados"
						title="Total de agendamentos realizados, quantos foram considerados executados."></pacto-cat-sm-info-block-icon>
					<pacto-cat-sm-info-block-icon
						(click)="openModalHandler('faltaram', agenda?.faltas)"
						[icon]="PactoIcon.PCT_SMARTPHONE"
						[label]="'Faltas'"
						[value]="agenda?.faltas"
						id="total-faltas"
						title="Total de agendamentos realizados, quantos foram considerados faltas."></pacto-cat-sm-info-block-icon>
					<pacto-cat-sm-info-block-icon
						(click)="openModalHandler('cancelados', agenda?.cancelados)"
						[icon]="PactoIcon.PCT_REFRESH_CW"
						[label]="'Cancelados'"
						[value]="agenda?.cancelados"
						id="total-cancelados"
						title="Total de agendamentos realizados, quantos foram considerados cancelados."></pacto-cat-sm-info-block-icon>
				</div>
			</div>
		</pacto-cat-card-plain>
	</div>

	<div class="col-md-12 col-lg-7 col-xl-8 disponibilidade-agendamentos-aux">
		<pacto-cat-card-plain>
			<div class="resumo-agenda">
				<div class="top-row">
					<div
						class="type-h5 agenda-title"
						i18n="@@treino-bi:disponibilidade-vs-agendamentos">
						Disponibilidade X Agendamentos
					</div>
					<pacto-cat-select
						[control]="comportamentoFc"
						[items]="comportamentos"
						[size]="'SMALL'"></pacto-cat-select>
				</div>

				<div class="center-row">
					<pacto-cat-pie-chart
						*ngIf="pieSeries"
						[id]="'disponibilidade-agendamentos'"
						[labelFormatter]="pieFormatter"
						[series]="pieSeries"
						[showLegend]="false"></pacto-cat-pie-chart>

					<div
						class="values-grid"
						title="Total de horas de disponibilidades cadastradas.">
						<div class="grid-value">
							<div class="grid-item-label">
								<span i18n="@@treino-bi:agenda:total-de-horas">
									Total de horas
								</span>
							</div>
							<div class="grid-item-value">
								{{
									comportamento
										? porComportamento[comportamento]?.disponibilidade + "h"
										: "-"
								}}
							</div>
						</div>
						<div
							class="grid-value"
							title="Total de horas de agendamento aguardando confirmação.">
							<div class="grid-item-label" i18n="@@treino-bi:agenda:aguardando">
								Aguardando
							</div>
							<div class="grid-item-value">
								{{
									comportamento
										? porComportamento[comportamento]?.aguardandoConfirmacao +
										  "h"
										: "-"
								}}
							</div>
						</div>
						<div
							class="grid-value"
							title="Quantidade de horas de agendamentos confirmados.">
							<div class="grid-item-label" i18n="@@treino-bi:chart:confirmados">
								Confirmados
							</div>
							<div class="grid-item-value">
								{{
									comportamento
										? porComportamento[comportamento]?.confirmado + "h"
										: "-"
								}}
							</div>
						</div>
						<div
							class="grid-value"
							title="Quantidade de horas de agendamentos realizados e marcados como executados.">
							<div class="grid-item-label" i18n="@@treino-bi:agenda:executados">
								Executados
							</div>
							<div class="grid-item-value">
								{{
									comportamento
										? porComportamento[comportamento]?.executaram + "h"
										: "-"
								}}
							</div>
						</div>
						<div
							class="grid-value"
							title="Quantidade de horas dos agendamentos realizados que estão marcados como faltas.">
							<div class="grid-item-label">Faltas</div>
							<div class="grid-item-value">
								{{
									comportamento
										? porComportamento[comportamento]?.faltaram + "h"
										: "-"
								}}
							</div>
						</div>
						<div
							class="grid-value"
							title="Quantidade de horas dos agendamentos realizados que estão marcados como cancelados.">
							<div class="grid-item-label">Cancelados</div>
							<div class="grid-item-value">
								{{
									comportamento
										? porComportamento[comportamento]?.cancelaram + "h"
										: "-"
								}}
							</div>
						</div>
					</div>
				</div>

				<pacto-cat-chart-legend
					[colors]="chartColors"
					[labels]="chartTraducoes.getAllLabels()"></pacto-cat-chart-legend>
			</div>
		</pacto-cat-card-plain>
	</div>
</div>

<pacto-traducoes-xingling #chartTraducoes>
	<span i18n="@@treino-bi:chart:aguardando" xingling="aguardando">
		Aguardando
	</span>
	<span i18n="@@treino-bi:chart:confirmados" xingling="confirmados">
		Confirmados
	</span>
	<span i18n="@@treino-bi:chart:executados" xingling="executados">
		Executados
	</span>
	<span xingling="faltas">Faltas</span>
	<span xingling="cancelados">Cancelados</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducoes>
	<span xingling="agendadosTitle">Agendados</span>
	<span xingling="executadosTitle">Executados</span>
	<span xingling="faltaramTitle">Faltas</span>
	<span xingling="canceladosTitle">Cancelados</span>

	<ng-template xingling="nomeProfessor">Nome do Professor</ng-template>
	<ng-template xingling="matricula">Matricula</ng-template>
	<ng-template xingling="nomeAluno">Nome do aluno</ng-template>
	<ng-template xingling="evento">Evento</ng-template>
	<ng-template xingling="inicio">Data de Início</ng-template>
	<ng-template xingling="inicioComHora">Horário do agendamento</ng-template>
	<ng-template xingling="ultimaAlteracaoStr">Data do cancelamento</ng-template>
	<ng-template xingling="fonteCancelamento">Origem</ng-template>
</pacto-traducoes-xingling>
