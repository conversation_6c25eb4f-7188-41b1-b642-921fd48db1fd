@import "~src/assets/scss/pacto/plataforma-import.scss";

.professor-card-title {
	@extend .type-h5;
	color: $pretoPri;
}

.carteira-wrapper {
	margin-top: 30px;
	@media (min-width: $bootstrap-breakpoint-lg) {
		margin-top: 0px;
	}
}

.minha-avaliacao {
	display: flex;
	flex-direction: column;
	color: $pretoPri;
	justify-content: space-between;
	align-items: center;
	text-align: center;

	pacto-cat-person-avatar-rating {
		margin: 20px 0px;
	}

	.avaliacao-ratings {
		@extend .type-h3-bold;
	}

	.avaliacao-label {
		@extend .type-p-rounded;
		color: $cinza05;
	}
}

.carteira {
	display: flex;
	flex-direction: column;
	height: 100%;

	.permanencia {
		@extend .type-p-small;
		color: $gelo04;
		flex-grow: 1;

		&.value {
			color: $pretoPri;
		}
	}
}

pacto-cat-line-chart-time-series {
	display: block;
	height: 300px;
}
