import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	<PERSON><PERSON><PERSON>roy,
} from "@angular/core";
import { TreinoBiStateService } from "../treino-bi-home-v2/treino-bi-state.service";
import { TreinoBiCarteira } from "@treino-core/treino-bi/treino-bi2.model";
import { Subscription } from "rxjs";

@Component({
	selector: "pacto-treino-bi-professor",
	templateUrl: "./treino-bi-professor.component.html",
	styleUrls: ["./treino-bi-professor.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreinoBiProfessorComponent implements OnInit, OnDestroy {
	constructor(
		private cd: ChangeDetectorRef,
		private biState: TreinoBiStateService
	) {}

	carteira: TreinoBiCarteira;
	private updateSubscription: Subscription;

	cateiraTimeSeries: [number, number][] = [];

	ngOnInit() {
		this.updateSubscription = this.biState.update$.subscribe((ready) => {
			if (ready) {
				this.setupData();
				this.cd.detectChanges();
			}
		});
	}

	ngOnDestroy() {
		if (this.updateSubscription) {
			this.updateSubscription.unsubscribe();
		}
	}

	private setupData() {
		this.carteira = this.biState.carteira;
		const historico = this.biState.carteira.historicoCarteiraZW;
		this.cateiraTimeSeries = [];
		for (const dia in historico) {
			if (historico.hasOwnProperty(dia)) {
				this.cateiraTimeSeries.push([parseInt(dia, 10), historico[dia]]);
			}
		}
	}
}
