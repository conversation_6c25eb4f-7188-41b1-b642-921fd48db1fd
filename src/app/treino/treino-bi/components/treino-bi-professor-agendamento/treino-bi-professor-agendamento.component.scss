@import "src/assets/scss/pacto/plataforma-import.scss";

.prof-agendamentos-title {
	@extend .type-h5;
	color: $pretoPri;
}

.professor-agendamentos-chart {
	.grid {
		display: grid;
		margin-top: 30px;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: 1fr 1fr 1fr;
		grid-gap: 30px;

		&.unic {
			grid-template-columns: 1fr;
			grid-template-rows: 1fr 1fr;
		}

		@media (min-width: $bootstrap-breakpoint-lg) {
			grid-template-columns: 1fr 1fr 1fr;
			grid-template-rows: 1fr 1fr;
		}
	}
}

.avaliacao-fisica-aux {
	margin-top: 30px;
	@media (min-width: $bootstrap-breakpoint-xl) {
		margin-top: 0px;
	}
	color: $pretoPri;

	.value-label {
		color: $cinza05;
	}

	.card-wrapper {
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: space-between;
		text-align: center;
		height: 100%;
	}
}

.button-compartilhar {
	display: -webkit-inline-box;
	font-size: small;
}

.btn-primary {
	color: $branco;
	background-color: $azulimPri !important;
	border-color: $azulimPri !important;
}

pacto-cat-card-plain {
	height: 100%;
}

.meta {
	&.atingiu {
		background-color: #2ec750;
	}

	&.nada {
		background-color: $pequizaoPri;
	}

	&.legenda-meta {
		width: 14px;
		height: 14px;
	}

	background-color: #db2c3d;
	width: 22px;
	height: 22px;
	border-radius: 50px;
}

.legenda-meta {
	font-size: 12px;
	display: inline-block;
	margin-right: 5px;
	vertical-align: middle;
}
