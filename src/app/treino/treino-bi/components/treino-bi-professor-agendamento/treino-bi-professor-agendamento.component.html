<div class="row">
	<div class="col-md-12 col-lg-12 col-xl-3">
		<pacto-cat-card-plain>
			<div class="professor-agendamentos-chart">
				<div
					class="prof-agendamentos-title"
					i18n="@@treino-bi:professor-agendamento:bonificacoes">
					Bonificações de aulas coletivas
				</div>
				<div class="grid unic">
					<pacto-cat-sm-info-block-icon
						(click)="buildModalBonificacaoAulas()"
						[icon]="PactoIcon.PCT_AULA_CHEIA"
						[value]="(aulas?.totalAulas ? aulas?.totalAulas : '-') + '%'"
						i18n-label="@@treino-bi:professor-agendamento:bonificacoes-aula"
						id="bonificacoes-aula"
						label="Ocupação média"
						title="Exibe a porcentagem de ocupação exigida para a bonificação em aulas coletivas.
Ao clicar na porcentagem, é exibida uma lista das aulas configuradas para a bonificação."></pacto-cat-sm-info-block-icon>

					<pacto-cat-sm-info-block-icon
						(click)="buildModalBonificacaoProfessor()"
						[icon]="PactoIcon.PCT_DOLLAR_SIGN"
						[value]="
							'R$ ' + (aulas?.totalBonificado ? aulas?.totalBonificado : '-')
						"
						i18n-label="@@treino-bi:professor-agendamento:bonificacoes-total"
						id="bonificacoes-total"
						label="Valor total de bonificação"
						title="Apresenta o valor total de bonificação gerado no período consultado.
Ao clicar no valor, é exibida uma lista mostrando o total de bonificação para cada professor."></pacto-cat-sm-info-block-icon>
				</div>
			</div>
		</pacto-cat-card-plain>
	</div>

	<div class="col-md-12 col-lg-12 col-xl-9">
		<pacto-cat-card-plain>
			<div class="professor-agendamentos-chart">
				<div
					class="prof-agendamentos-title"
					i18n="@@treino-bi:professor-agendamento:professores-agendamentos">
					Professores X Agendamentos
				</div>
				<div class="grid">
					<pacto-cat-sm-info-block-icon
						(click)="openModalHandler('professores', agenda?.professores)"
						[icon]="PactoIcon.PCT_USERS"
						[value]="agenda?.professores"
						i18n-label="@@treino-bi:professor-agendamento:professor"
						id="total-professores"
						label="Professores"
						title="Quantidade de professores que possui disponibilidade cadastrada na agenda de serviços"></pacto-cat-sm-info-block-icon>
					<pacto-cat-sm-info-block-icon-cursor-auto
						[icon]="PactoIcon.PCT_CLOCK"
						[value]="
							agenda?.horasDisponibilidades
								? agenda?.horasDisponibilidades + 'h'
								: '-'
						"
						i18n-label="@@treino-bi:professor-agendamento:disponibilidade"
						label="Disponibilidade"
						title="Quantidade em horas de disponibilidades cadastradas"></pacto-cat-sm-info-block-icon-cursor-auto>

					<pacto-cat-sm-info-block-icon-cursor-auto
						[icon]="PactoIcon.PCT_USERS"
						[value]="agenda?.ocupacao + '%'"
						i18n-label="@@treino-bi:professor-agendamento:ocupacao"
						id="total-ocupacao"
						label="Ocupação"
						title="Taxa de ocupação em porcentagem referente às horas de disponibilidades de agendamento"></pacto-cat-sm-info-block-icon-cursor-auto>
					<pacto-cat-sm-info-block-icon
						(click)="openModalHandler('novosTreinos', agenda?.novosTreinos)"
						[icon]="PactoIcon.PCT_AWARD"
						[value]="agenda?.novosTreinos"
						i18n-label="@@treino-bi:professor-agendamento:treinos-novos"
						id="total-treinos-novos"
						label="Treinos Novos"
						title="Apresenta informações sobre as marcações de disponibilidade relacionadas aos Tipos de Agendamentos com o comportamento de Prescrição Treino.
Para que o aluno seja incluído neste relatório, além do agendamento, é necessário atribuir um novo programa de treino ao aluno"></pacto-cat-sm-info-block-icon>
					<pacto-cat-sm-info-block-icon
						(click)="
							openModalHandler('treinosRenovados', agenda?.treinosRenovados)
						"
						[icon]="PactoIcon.PCT_REFRESH_CW"
						[value]="agenda?.treinosRenovados"
						i18n-label="@@treino-bi:professor-agendamento:treinos-renovados"
						id="total-treinos-renovados"
						label="Treinos Renovados"
						title="Apresenta informações sobre as marcações de disponibilidade relacionadas aos Tipos de Agendamentos com o comportamento de Renovar Treino.
Para que o aluno seja incluído neste relatório, além do agendamento, é necessário renovar o programa de treino do aluno."></pacto-cat-sm-info-block-icon>

					<pacto-cat-sm-info-block-icon
						(click)="
							openModalHandler('avaliacoesFisicas', agenda?.avaliacoesFisicas)
						"
						[icon]="PactoIcon.PCT_CLIPBOARD"
						[value]="agenda?.avaliacoesFisicas"
						i18n-label="
							@@treino-bi:professor-agendamento:avaliacao-fisica-label"
						id="total-avalicao"
						label="Avaliação Física"
						title="Apresenta informações sobre as marcações de disponibilidade relacionadas aos Tipos de Agendamentos com o comportamento de Avaliação Física.
Para que o aluno seja incluído neste relatório, além do agendamento, é necessário registrar uma Avaliação Física para o aluno."></pacto-cat-sm-info-block-icon>
				</div>
			</div>
		</pacto-cat-card-plain>
	</div>
</div>

<ng-template #metaCelula let-item="item">
	<div
		class="meta {{ item.meta }}"
		title="{{
			item.metaAtingir > 0
				? 'Meta ' + item.metaAtingir + '%'
				: 'Meta não configurada'
		}}"></div>
</ng-template>

<ng-template #legendaCelula>
	<div class="meta legenda-meta"></div>
	<span class="legenda-meta">Não atingiram a meta</span>
	<div class="meta legenda-meta nada"></div>
	<span class="legenda-meta">Não há meta configurada</span>
	<div class="meta legenda-meta atingiu"></div>
	<span class="legenda-meta">Atingiram a meta</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span xingling="professoresTitle">Professores</span>
	<span xingling="novosTreinosTitle">Treinos novos</span>
	<span xingling="renovadosTreinosTitle">Treinos renovados</span>
	<span xingling="horasDisponibilidadeTitle">Treinos renovados</span>
	<span xingling="revisadosTitle">Treinos revisados</span>
	<span xingling="avaliacoesFisicaTitle">Avaliações fisicas</span>
	<span xingling="avaliacoesFisicaSemAgendamentoTitle">Não realizadas</span>
	<span xingling="avaliacoesFisicaComAgendamentoTitle">Realizadas</span>
	<span xingling="aulasTitle">Aulas</span>
	<span xingling="ocupacaoTitle">Ocupação</span>

	<ng-template xingling="nomeProfessor">Nome do Professor</ng-template>
	<ng-template xingling="nomeAluno">Nome do aluno</ng-template>
	<ng-template xingling="nomeAbreviado">Nome do aluno</ng-template>
	<ng-template xingling="nome">Nome</ng-template>
	<ng-template xingling="codigo">Codigo</ng-template>
	<ng-template xingling="matricula">Matricula</ng-template>
	<ng-template xingling="dataPrograma">Data programa</ng-template>
	<ng-template xingling="dataLancamento">Data lancamento</ng-template>
	<ng-template xingling="evento">Evento</ng-template>
	<ng-template xingling="inicio">Data de Início</ng-template>

	<ng-template xingling="aula">Aula</ng-template>
	<ng-template xingling="modalidades">Modalidades</ng-template>
	<ng-template xingling="modalidade">Modalidade</ng-template>
	<ng-template xingling="nomeModalidade">Modalidade</ng-template>
	<ng-template xingling="professor">Professor</ng-template>
	<ng-template xingling="horario">Horário</ng-template>
	<ng-template xingling="ocupacao">Ocupação</ng-template>
	<ng-template xingling="presencas">Presenças</ng-template>
	<ng-template xingling="aulas">Aulas</ng-template>
	<ng-template xingling="professores">Professor</ng-template>
	<ng-template xingling="frequencia">Frequência</ng-template>
	<ng-template xingling="capacidade">Capacidade</ng-template>
	<ng-template xingling="dia">Dia</ng-template>
</pacto-traducoes-xingling>
