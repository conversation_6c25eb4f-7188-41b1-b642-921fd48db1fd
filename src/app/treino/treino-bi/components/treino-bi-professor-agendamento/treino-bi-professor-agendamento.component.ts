import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
	OnDestroy,
	Input,
	TemplateRef,
} from "@angular/core";
import { PactoIcon, PieChartSet } from "ui-kit";
import { TreinoBiAgenda, BiAula } from "treino-api";
import {
	ModalRelatorioConfig,
	relatorios,
} from "../treino-bi-agenda-disponibilidade/relatorios.config";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	RelatorioComponent,
	PactoColor,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { Subscription } from "rxjs";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { ShareComponent } from "src/app/share/share.component";
import { AgendaBiStateService } from "../../../../agenda/agenda/agenda-bi/agenda-bi-state";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

@Component({
	selector: "pacto-treino-bi-professor-agendamento",
	templateUrl: "./treino-bi-professor-agendamento.component.html",
	styleUrls: ["./treino-bi-professor-agendamento.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreinoBiProfessorAgendamentoComponent
	implements OnInit, OnDestroy
{
	@ViewChild("traducoes", { static: true }) traducoes;
	@ViewChild("shareButton", { static: true }) shareButtonRef: TemplateRef<any>;
	@ViewChild("metaCelula", { static: true }) metaCelula;
	@ViewChild("legendaCelula", { static: true }) legendaCelula;
	@Input() professorFiltro;
	@Input() professorCodigoPessoaFiltro;
	@Input() dataInicio;
	@Input() dataFim;
	endpointShare: string;
	config: PactoDataGridConfigDto;
	relatorio: RelatorioComponent;
	chartColors: PactoColor[] = [PactoColor.AZULIM_PRI, PactoColor.GELO_05];

	constructor(
		private agendaState: AgendaBiStateService,
		private rest: RestService,
		private cd: ChangeDetectorRef,
		private modal: ModalService,
		private configService: TreinoConfigCacheService,
		private session: SessionService
	) {}

	composicaoSeries: PieChartSet[] = [];
	configuracaoGestao;
	percentualAlunosSemAvaliacao;
	private subscription: Subscription;

	ngOnInit() {
		this.configuracaoGestao =
			this.configService.configuracoesGestao.periodo_usado_bi;
		this.subscription = this.agendaState.update$.subscribe((ready) => {
			if (ready) {
				if (this.agenda && this.agenda.totalAlunosSemAvaliacaoFisica) {
					this.percentualAlunosSemAvaliacao = this.agenda.percentualAvaliacoes;
				} else {
					this.percentualAlunosSemAvaliacao = null;
				}
				setTimeout(() => {
					this.composicaoSeries.slice(0, 1);
					this.composicaoSeries = [
						{ name: "Realizadas", data: this.percentualAlunosSemAvaliacao },
						{
							name: "Não realizadas",
							data: 100 - this.percentualAlunosSemAvaliacao,
						},
					];
					this.cd.detectChanges();
				});
			}
		});
	}

	ngOnDestroy() {
		this.subscription.unsubscribe();
	}

	get agenda(): TreinoBiAgenda {
		return this.agendaState.agenda;
	}

	get aulas(): BiAula {
		return this.agendaState.graficosAula;
	}

	openModalHandler(name, numeroIndicador) {
		if (name === "professores") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.PROFESSOR_X_AGENDAMENTOS_CLIQUES_PROFESSORES
			);
		} else if (name === "novosTreinos") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.PROFESSOR_X_AGENDAMENTOS_CLIQUES_TREINOS_NOVOS
			);
		} else if (name === "treinosRenovados") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.PROFESSOR_X_AGENDAMENTOS_CLIQUES_TREINOS_RENOVADOS
			);
		} else if (name === "avaliacoesFisicas") {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.PROFESSOR_X_AGENDAMENTOS_CLIQUES_AVALIACAO_FISICA
			);
		}
		this.buildModalHandler(relatorios[name], numeroIndicador);
	}

	openModalCharPie(event: any) {
		if (event === 0) {
			this.buildModalHandler(relatorios["comAgendamentoAvaliacaoFisica"], 1);
		} else if (event === 1) {
			this.buildModalHandler(relatorios["semAgendamentoAvaliacaoFisica"], 1);
		}
	}

	private buildModalHandler(
		item: ModalRelatorioConfig,
		numeroIndicador: number
	) {
		const codigoPessoa = this.session.loggedUser.professorResponse.codigoPessoa;
		this.endpointShare = this.rest.buildFullUrl(
			item.endpoint + "/" + codigoPessoa
		);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.telaId = item.title;
		this.relatorio.sessionService = this.session;
		if (numeroIndicador > 0) {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(item.endpoint + "/" + codigoPessoa),
				logUrl: this.rest.buildFullUrl(
					"log/listar-log-exportacao/" + item.title
				),
				pagination: true,
				quickSearch: true,
				exportButton: false,
				rowClick: false,
				columns: [],
			};
		} else {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(item.endpoint + "/" + codigoPessoa),
				pagination: true,
				quickSearch: true,
				exportButton: false,
				rowClick: false,
				columns: [],
			};
		}
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				ordenavel: column.ordenavel,
				visible: true,
				valueTransform: null,
			};
			if (column.date) {
				columnConfig.valueTransform = (d) => moment(d).format("DD/MM/YYYY");
			}
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
		if (
			(this.dataInicio === undefined || this.dataInicio === null) &&
			(this.dataFim === undefined || this.dataFim === null)
		) {
			this.relatorio.baseFilter = {
				filters: {
					professorId: this.professorFiltro,
					codigoPessoa: this.professorCodigoPessoaFiltro,
				},
			};
		} else {
			this.relatorio.baseFilter = {
				filters: {
					professorId: this.professorFiltro,
					codigoPessoa: this.professorCodigoPessoaFiltro,
					inicio: this.dataInicio,
					fim: this.dataFim,
				},
			};
		}
		this.relatorio.btnClick.subscribe(() => {
			this.openModalShare(item);
		});
	}

	private openModalShare(item: ModalRelatorioConfig) {
		const pctModal = this.modal.open(
			"Compartilhar",
			ShareComponent,
			PactoModalSize.MEDIUM
		);
		const relatorio: RelatorioComponent = pctModal.componentInstance;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.endpointShare,
			columns: [],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			config.columns.push(columnConfig);
		});
		relatorio.table = new PactoDataGridConfig(config);
		const filtroModal = this.relatorio.fetchFiltros();
		const filtroProf = {
			filters: {
				professorId: this.professorFiltro,
			},
		};
		relatorio.baseFilter = Object.assign(filtroModal, filtroProf);
		relatorio.baseFilter.size = 999999;
	}

	get PactoIcon() {
		return PactoIcon;
	}

	composicaoLabelFn() {
		return (value) => {
			return `${value}%`;
		};
	}

	composicaoLabelBorderFn() {
		return (value) => {
			let arredondado;
			arredondado = Math.round(value * 100) / 100;
			return `${arredondado} %`;
		};
	}

	public buildModalBonificacaoAulas() {
		this.buildModalBonificacao(relatorios.aulas, -1);
	}

	private buildModalBonificacao(item: ModalRelatorioConfig, codigoRelatorio) {
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE,
			"modal-mxl"
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.legenda = this.legendaCelula;
		this.relatorio.telaId = item.title;
		this.relatorio.sessionService = this.session;
		const urlLog = "log/listar-log-exportacao/" + item.title;
		this.config = {
			endpointUrl: this.rest.buildFullUrl("bi-agenda/listagem-bonificacao"),
			logUrl: this.rest.buildFullUrl(urlLog),
			pagination: true,
			quickSearch: true,
			exportButton: false,
			rowClick: false,
			columns: [],
		};

		this.config.columns = [
			{
				nome: "dia",
				titulo: "Dia",
				buscaRapida: false,
				visible: true,
				ordenavel: false,
				valueTransform: (d) => moment(d).format("DD/MM/YYYY"),
				defaultVisible: true,
				campo: "dia",
			},
			{
				nome: "horario",
				titulo: "Horário",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "horario",
			},
			{
				nome: "aula",
				titulo: "Aula",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "aula",
			},
			{
				nome: "professor",
				titulo: "Professor(a)",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "professor",
			},
			{
				nome: "meta",
				titulo: "Meta",
				buscaRapida: true,
				visible: true,
				ordenavel: false,
				defaultVisible: true,
				campo: "meta",
				celula: this.metaCelula,
			},
			{
				nome: "ocupacao",
				titulo: "Ocupação",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "ocupacao",
			},
			{
				nome: "bonus",
				titulo: "Bônus",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "bonus",
			},
		];
		const inicio = this.dataInicio;
		const fim = this.dataFim;
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				codigoRelatorio,
				inicio,
				fim,
			},
		};
	}

	buildModalBonificacaoProfessor() {
		const pctModal = this.modal.open(
			"Valores de bonificação",
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.telaId = "bonificacaoProfessor";
		this.relatorio.sessionService = this.session;
		this.config = {
			endpointUrl: this.rest.buildFullUrl(
				"bi-agenda/listagem-bonificacao-professor"
			),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/bonificacaoProfessor"
			),
			pagination: true,
			quickSearch: true,
			exportButton: false,
			rowClick: false,
			columns: [],
		};

		this.config.columns = [
			{
				nome: "professor",
				titulo: "Professor(a)",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "professor",
			},
			{
				nome: "bonus",
				titulo: "Bônus",
				buscaRapida: false,
				visible: true,
				ordenavel: true,
				defaultVisible: true,
				campo: "bonus",
			},
		];
		const inicio = this.dataInicio;
		const fim = this.dataFim;
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				inicio,
				fim,
			},
		};
	}
}
