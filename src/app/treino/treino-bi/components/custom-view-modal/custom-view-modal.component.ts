import { Component, OnInit, ChangeDetectionStrategy } from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-custom-view-modal",
	templateUrl: "./custom-view-modal.component.html",
	styleUrls: ["./custom-view-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CustomViewModalComponent implements OnInit {
	fg: FormGroup = new FormGroup({
		nome: new FormControl(null, [
			Validators.required,
			Validators.minLength(1),
			Validators.pattern("^(?=. *[a-zA-Z ])[a-zA-Z0-9 ]+$"),
		]),
		professorIds: new FormControl(),
		indicadores: new FormControl(null, [
			Validators.required,
			(fc) => {
				const value = fc.value;
				if (value && value.length) {
					return null;
				} else {
					return { required: true };
				}
			},
		]),
	});

	indicadores = [
		{ id: "TOTAL", nome: "Total" },
		{ id: "ATIVOS", nome: "Ativos" },
		{ id: "INATIVOS", nome: "Inativos" },
		{ id: "ATIVOSTREINO", nome: "Ativos com Treino" },
		{ id: "EMDIA", nome: "Em Dia" },
		{ id: "TREINOSVENCIDOS", nome: "Treinos Vencidos" },
		{ id: "TREINOSRENOVAR", nome: "Treinos a Renovar" },
		{ id: "AGENDAMENTOS", nome: "Agendamentos" },
		{ id: "AGEXECUTARAM", nome: "Agendamentos Executaram" },
		{ id: "AGFALTARAM", nome: "Agendamentos Faltaram" },
		{ id: "AGCANCELARAM", nome: "Agendamentos Cancelaram" },
		{ id: "RENOVADOS", nome: "Renovados" },
		{ id: "NAORENOVADOS", nome: "Não Renovados" },
		{ id: "AVENCER", nome: "A Vencer" },
		{ id: "NOVOSCARTEIRA", nome: "Novos Carteira" },
		{ id: "TROCARAMCARTEIRA", nome: "Trocaram Carteira" },
		{ id: "MEDIOCARTEIRA", nome: "Médio Carteira" },
		{ id: "SEMTREINO", nome: "Sem Treino" },
		{ id: "PERCENTUALEMDIA", nome: "Percentual Em Dia" },
		{ id: "PERCENTUALVENCIDOS", nome: "Percentual Vencidos" },
		{ id: "MEDIORPROGRAMA", nome: "Médio Programa" },
		{ id: "NRAVALIACOES", nome: "Numero de Avaliações" },
		{ id: "AVALIACOES", nome: "Avaliações" },
		{ id: "ESTRELAUM", nome: "Estrela Um" },
		{ id: "ESTRELADOIS", nome: "Estrela Dois" },
		{ id: "ESTRELATRES", nome: "Estrela Três" },
		{ id: "ESTRELAQUATRO", nome: "Estrela Quatro" },
		{ id: "ESTRELACINCO", nome: "Estrela Cinco" },
		{ id: "COMAVALIACAO", nome: "Com Avaliação" },
		{ id: "SEMAVALIACAO", nome: "Sem Avaliação" },
		{ id: "AGPROFESSORES", nome: "Agendamento Professores" },
		{ id: "HORASDISPONIBILIDADE", nome: "Horas Disponibilidade" },
		{ id: "HORASEXECUTADAS", nome: "Horas Executadas" },
		{ id: "PERCOCUPACAO", nome: "Porcentagem de Ocupação" },
		{ id: "AGNOVOSTREINOS", nome: "Agendamento de Novos Treinos" },
		{ id: "AGTREINOSRENOVADOS", nome: "Agendamento Treinos Renovados" },
		{ id: "AGTREINOSREVISADOS", nome: "Agendamento Treinos Revisados" },
		{ id: "AGAVALIACAOFISICA", nome: "Agendamento Avaliações Físicas" },
		{ id: "PERCENTUALRENOVACAO", nome: "Percentual Renovação" },
	];

	constructor(private rest: RestService, private modal: NgbActiveModal) {}

	ngOnInit() {}

	get professorParamBuilder() {
		return (filter) => {
			return {
				filters: JSON.stringify({
					situacoes: ["ATIVO"],
					quicksearchValue: filter,
					quicksearchFields: ["nome"],
				}),
			};
		};
	}

	get resposeParser() {
		return (response) => response.content;
	}

	get professoresUrl() {
		return this.rest.buildFullUrl("colaboradores");
	}

	btnClickHandler() {
		this.fg.get("indicadores").markAsTouched();
		this.fg.get("professorIds").markAsTouched();
		this.fg.get("nome").markAsTouched();

		if (this.fg.valid) {
			this.modal.close(this.dto);
		}
	}

	private get dto() {
		const dto = this.fg.getRawValue();
		const professorIds = [];
		const indicadores = [];

		if (dto.professorIds) {
			dto.professorIds.forEach((item) => {
				professorIds.push(item.id);
			});
		}
		dto.professorIds = professorIds;

		if (dto.indicadores) {
			dto.indicadores.forEach((indicador) => {
				indicadores.push(indicador.id);
			});
		}
		dto.indicadores = indicadores;

		return dto;
	}
}
