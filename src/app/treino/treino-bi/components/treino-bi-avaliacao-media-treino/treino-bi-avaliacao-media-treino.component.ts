import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	ModalRelatorioConfig,
	relatorios,
} from "../indicadores-grid/relatorios.config";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
	PactoDataGridConfigDto,
} from "ui-kit";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoBiStateService } from "../treino-bi-home-v2/treino-bi-state.service";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import {
	TreinoBiAvaliacaoTreino,
	TreinoBiCarteira,
} from "@treino-core/treino-bi/treino-bi2.model";
import { Subscription } from "rxjs";
import { permissaoCategoria } from "../../../../base/perfil-acesso/components/config.model";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { Router } from "@angular/router";

declare var moment;

@Component({
	selector: "pacto-treino-bi-avaliacao-media-treino",
	templateUrl: "./treino-bi-avaliacao-media-treino.component.html",
	styleUrls: ["./treino-bi-avaliacao-media-treino.component.scss"],
})
export class TreinoBiAvaliacaoMediaTreinoComponent implements OnInit {
	@Input() professorFiltro;
	@Input() professorCodigoPessoaFiltro;
	@Input() rating: number;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("notaCelula", { static: true }) notaCelula;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;

	config: PactoDataGridConfigDto;
	relatorio: RelatorioComponent;

	avaliacaoTreino: TreinoBiAvaliacaoTreino;
	carteira: TreinoBiCarteira;
	integradoZW = true;
	subscription: Subscription;

	title1estrela: any;
	title2estrelas: any;
	title3estrelas: any;
	title4estrelas: any;
	title5estrelas: any;
	titleAvaliacaoTotal: any;
	podeVisualizarMedia;

	constructor(
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private modal: ModalService,
		private rest: RestService,
		private state: TreinoBiStateService,
		private treinoConfigCacheService: TreinoConfigCacheService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private router: Router
	) {}

	endpointShare: string;
	permissaoVerMediaAlunos;

	ngOnInit() {
		this.integradoZW = this.session.integracaoZW;
		this.verificarPermissao();
		this.subscription = this.state.update$.subscribe((ready) => {
			if (ready) {
				this.setupData();
			}
		});
	}

	private setupData() {
		this.avaliacaoTreino = this.state.avaliacaoTreino;
		this.verificarPermissao();
		this.loadTitle();
		this.cd.detectChanges();
	}

	private loadTitle() {
		if (this.avaliacaoTreino.nr1estrela === 0) {
			this.title1estrela = "Nenhum aluno deu 1 estrela";
		} else if (this.avaliacaoTreino.nr1estrela === 1) {
			this.title1estrela = "1 aluno deu 1 estrela";
		} else if (this.avaliacaoTreino.nr1estrela > 1) {
			this.title1estrela =
				this.avaliacaoTreino.nr1estrela + " alunos deram 1 estrela";
		}

		if (this.avaliacaoTreino.nr2estrelas === 0) {
			this.title2estrelas = "Nenhum aluno deu 2 estrelas";
		} else if (this.avaliacaoTreino.nr2estrelas === 1) {
			this.title2estrelas = "1 aluno deu 2 estrelas";
		} else if (this.avaliacaoTreino.nr2estrelas > 1) {
			this.title2estrelas =
				this.avaliacaoTreino.nr2estrelas + " alunos deram 2 estrela";
		}

		if (this.avaliacaoTreino.nr3estrelas === 0) {
			this.title3estrelas = "Nenhum aluno deu 3 estrelas";
		} else if (this.avaliacaoTreino.nr3estrelas === 1) {
			this.title3estrelas = "1 aluno deu 3 estrelas";
		} else if (this.avaliacaoTreino.nr3estrelas > 1) {
			this.title3estrelas =
				this.avaliacaoTreino.nr3estrelas + " alunos deram 3 estrelas";
		}

		if (this.avaliacaoTreino.nr4estrelas === 0) {
			this.title4estrelas = "Nenhum aluno deu 4 estrelas";
		} else if (this.avaliacaoTreino.nr4estrelas === 1) {
			this.title4estrelas = "1 aluno deu 4 estrelas";
		} else if (this.avaliacaoTreino.nr4estrelas > 1) {
			this.title4estrelas =
				this.avaliacaoTreino.nr4estrelas + " alunos deram 4 estrelas";
		}

		if (this.avaliacaoTreino.nr5estrelas === 0) {
			this.title5estrelas = "Nenhum aluno deu 5 estrelas";
		} else if (this.avaliacaoTreino.nr5estrelas === 1) {
			this.title5estrelas = "1 aluno deu 5 estrelas";
		} else if (this.avaliacaoTreino.nr5estrelas > 1) {
			this.title5estrelas =
				this.avaliacaoTreino.nr5estrelas + " alunos deram 5 estrelas";
		}

		if (this.avaliacaoTreino.nrAvaliacoesTreino === 1) {
			this.titleAvaliacaoTotal = "1 avaliação";
		} else if (this.avaliacaoTreino.nrAvaliacoesTreino !== 1) {
			this.titleAvaliacaoTotal =
				this.avaliacaoTreino.nrAvaliacoesTreino + " avaliações";
		}
	}

	private verificarPermissao() {
		const temPermissao = this.session.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.AVALIACAO_MEDIA_ALUNOS
		);
		this.podeVisualizarMedia = !!temPermissao;
	}

	ClickHandler(name, numeroIndicador) {
		if (this.podeVisualizarMedia) {
			try {
				const rota = this.router.url;
				localStorage.removeItem("config_table__" + rota);
			} catch (e) {
				console.log("problema ao limpar filtro dash");
			}
			this.openModalHandler(relatorios[name], numeroIndicador);
		} else {
			this.snotifyService.warning(
				this.notificacoesTranslate.getLabel("sem-permissao")
			);
		}
	}

	private openModalHandler(
		item: ModalRelatorioConfig,
		numeroIndicador: number
	) {
		const codigoPessoa = this.session.loggedUser.professorResponse.codigoPessoa;
		const tipoBusca = this.identificarTipoBusca(item.title);
		this.endpointShare = this.rest.buildFullUrl(
			item.endpoint + "/" + tipoBusca + "/" + codigoPessoa
		);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE,
			"modal-mxl"
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.rowClick.subscribe(($item) => {
			window
				.open(
					"cadastros/alunos/perfil/" + $item.matricula + "%3Forigem%3Dbi",
					"_blank"
				)
				.focus();
		});

		if (numeroIndicador > 0) {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(
					item.endpoint + "/" + tipoBusca + "/" + codigoPessoa
				),
				pagination: item.pagination,
				quickSearch: true,
				exportButton: false,
				rowClick: true,
				columns: [],
			};
		} else {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(
					item.endpoint + "/" + tipoBusca + "/" + codigoPessoa
				),
				pagination: item.pagination,
				quickSearch: true,
				exportButton: false,
				rowClick: true,
				columns: [],
			};
		}
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
				celula: null,
			};
			if (column.value === "nota") {
				columnConfig.celula = this.notaCelula;
			}
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("HH:MM - DD/MM/YY");
			}
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				professorId: this.professorFiltro,
				codigoPessoa: this.professorCodigoPessoaFiltro,
			},
		};
		this.relatorio.btnClick.subscribe(() => {});
	}

	private identificarTipoBusca(indicador) {
		switch (indicador) {
			case "avaliacaoTotalTreinoTitle":
				return 0;
			case "avaliacaoMediaTreino1Title":
				return 1;
			case "avaliacaoMediaTreino2Title":
				return 2;
				break;
			case "avaliacaoMediaTreino3Title":
				return 3;
			case "avaliacaoMediaTreino4Title":
				return 4;
			default:
				return 5;
		}
	}

	protected readonly permissaoCategoria = permissaoCategoria;
}
