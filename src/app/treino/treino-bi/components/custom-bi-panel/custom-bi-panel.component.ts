import {
	Component,
	OnInit,
	ViewChild,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormControl } from "@angular/forms";

import { Observable, Subscription, merge } from "rxjs";
import { map } from "rxjs/operators";

import { ColumnChartSet } from "ui-kit";
import {
	CustomBiView,
	CustomBiViewData,
	TreinoApiBiCustomizadoService,
} from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";

interface ProfessorIndicador {
	professor: { id: number; nome: string };
	indicador: string;
}

@Component({
	selector: "pacto-custom-bi-panel",
	templateUrl: "./custom-bi-panel.component.html",
	styleUrls: ["./custom-bi-panel.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CustomBiPanelComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	periodoFc = new FormControl(12);
	viewFc = new FormControl();

	loading = false;
	removeLoading = false;
	views: CustomBiView[] = [];
	viewData: CustomBiViewData = null;
	xAxisLabels = [];
	series: ColumnChartSet[] = [];
	periodos = [
		{ id: 3, label: "3 Meses" },
		{ id: 6, label: "6 Meses" },
		{ id: 12, label: "1 Ano" },
		{ id: 24, label: "2 Anos" },
	];

	/**
	 * Subscriptions
	 */
	fetchViewSubscription: Subscription;

	get indicadores() {
		if (this.viewData && this.viewData.indicadores) {
			return this.viewData.indicadores;
		} else {
			return [];
		}
	}

	get professores() {
		if (this.viewData && this.viewData.professores) {
			return this.viewData.professores;
		} else {
			return [];
		}
	}

	get indicadoresList() {
		let out = "";
		this.indicadores.forEach((indicador, index) => {
			const traduzido = this.traducoes.getLabel(indicador);
			if (index > 0) {
				out = `${out}, ${traduzido}`;
			} else {
				out = traduzido;
			}
		});
		return out;
	}

	get professoresList() {
		let out = "";
		this.professores.forEach((professor, index) => {
			if (index > 0) {
				out = `${out}, ${professor.nome}`;
			} else {
				out = professor.nome;
			}
		});
		return out;
	}

	constructor(
		private customBiService: TreinoApiBiCustomizadoService,
		private notify: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.eventSetup();
		this.loadData();
	}

	removerHandler() {
		if (!this.removeLoading) {
			const id = this.viewData.id;
			this.removeLoading = true;
			this.cd.detectChanges();
			this.customBiService.deleteCustomView(id).subscribe(() => {
				this.notify.success("Gráfico removido com sucesso.");
				this.removeLoading = false;
				this.cd.detectChanges();
				this.reloadDataAndSelectView();
			});
		}
	}

	reloadDataAndSelectView(viewId?: string) {
		this.loadData(viewId);
	}

	private loadData(viewToSelect?: string) {
		this.loading = true;
		this.cd.detectChanges();
		this.fetchAllViews().subscribe(() => {
			this.loading = false;
			this.cd.detectChanges();
			let view;
			if (viewToSelect) {
				view = this.views.find((i) => i.id === viewToSelect);
			} else {
				view = this.views[0];
			}
			this.viewFc.setValue(view);
		});
	}

	private prepareGraphData() {
		const professores = this.viewData.professores;
		const porProfessor = professores && professores.length;

		/*
      Labels
    */
		const labels = [];
		this.viewData.dados.forEach((dado) => {
			labels.push(dado.periodo);
		});
		this.xAxisLabels = labels;

		/*
      Data
    */
		if (porProfessor) {
			this.series = this.prepareSeriesPerTeacher();
		} else {
			this.series = this.prepareSeriesNoFilter();
		}
	}

	private prepareSeriesPerTeacher(): ColumnChartSet[] {
		const sets: ColumnChartSet[] = [];
		const professorIndicadores: ProfessorIndicador[] = [];
		this.viewData.professores.forEach((professor) => {
			this.viewData.indicadores.forEach((indicador) => {
				professorIndicadores.push({
					professor,
					indicador,
				});
			});
		});
		professorIndicadores.forEach((professorIndicador) => {
			const indicadorTraducao = this.traducoes.getLabel(
				professorIndicador.indicador
			);
			const set: ColumnChartSet = {
				name: `${indicadorTraducao} - ${professorIndicador.professor.nome}`,
				data: [],
			};
			this.viewData.dados.forEach((dado) => {
				const profId = professorIndicador.professor.id;
				const indicador = professorIndicador.indicador;
				const dataPoint = dado.valorPorProfessor[profId][indicador];
				set.data.push(dataPoint);
			});
			sets.push(set);
		});
		return sets;
	}

	private prepareSeriesNoFilter(): ColumnChartSet[] {
		const sets: ColumnChartSet[] = [];
		this.viewData.indicadores.forEach((indicador) => {
			const indicadorTraduzido = this.traducoes.getLabel(indicador);
			const set: ColumnChartSet = { name: `${indicadorTraduzido}`, data: [] };
			this.viewData.dados.forEach((dado) => {
				const dataPoint = dado.valor[indicador];
				set.data.push(dataPoint);
			});
			sets.push(set);
		});
		return sets;
	}

	private eventSetup() {
		const viewChange$ = this.viewFc.valueChanges;
		const periodoChange$ = this.periodoFc.valueChanges;
		merge(viewChange$, periodoChange$).subscribe((event) => {
			this.fetchView();
		});
	}

	private fetchAllViews(): Observable<any> {
		return this.customBiService.obterCustomBiViews().pipe(
			map((views) => {
				this.views = views;
			})
		);
	}

	private fetchView() {
		const period = this.periodoFc.value;
		const id = this.viewFc.value.id;
		this.loading = true;
		this.cd.detectChanges();
		if (this.fetchViewSubscription) {
			this.fetchViewSubscription.unsubscribe();
		}
		this.fetchViewSubscription = this.customBiService
			.obterCustomViewData(id, period)
			.subscribe((result) => {
				this.loading = false;
				this.viewData = result;
				this.prepareGraphData();
				this.cd.detectChanges();
			});
	}
}
