@import "~src/assets/scss/pacto/plataforma-import.scss";

.panel-card {
	display: block;
	margin-top: 15px;

	&.loading {
		filter: blur(2px);
		pointer-events: none;
	}
}

.title-row {
	display: flex;

	.view-title {
		flex-grow: 1;
		@extend .type-h5;
		text-transform: capitalize;
	}

	.filtro-periodo {
		width: 200px;
	}
}

.panel-actions {
	display: flex;
	flex-direction: row-reverse;
}

.indicadores,
.professores {
	@extend .type-p-small;
	color: $cinza03;

	.bi-label {
		color: $pretoPri;
	}
}
