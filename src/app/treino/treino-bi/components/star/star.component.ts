import { Component, Input, OnInit } from "@angular/core";

@Component({
	selector: "pacto-star",
	templateUrl: "./star.component.html",
	styleUrls: ["./star.component.scss"],
})
export class StarComponent implements OnInit {
	@Input() nrEstrela: number;
	@Input() repeticoes: number;
	@Input() preenchidas: number;
	@Input() tamanho: number;

	constructor() {}

	ngOnInit() {}

	stars(): Array<any> {
		const stars = [];
		if (this.repeticoes) {
			for (let i = 0; i < this.repeticoes; i++) {
				stars.push({
					classe: i <= this.preenchidas ? "preenchida" : "",
				});
			}
		} else {
			stars.push({
				classe: "preenchida",
			});
		}
		return stars;
	}
}
