<div class="row">
	<div class="col-md-12 col-lg-7 col-xl-8">
		<pacto-cat-card-plain>
			<div class="movimentacao-chart">
				<div
					class="type-h5 movimentacao-title"
					i18n="@@treino-bi:execucoes-30-dias">
					Execuções de treino nos últimos {{ configuracaoGestao }} dias
				</div>
				<pacto-cat-column-chart
					*ngIf="series"
					[colors]="chartColors"
					[height]="280"
					[series]="series"
					[xAxisLabels]="diaSemanaCurto.getAllLabels()"
					(clickEvent)="clickColumn($event)"></pacto-cat-column-chart>
			</div>
		</pacto-cat-card-plain>
	</div>

	<div class="col-md-12 col-lg-5 col-xl-4 movimentacao-result-aux">
		<pacto-cat-card-plain>
			<div class="movimentacao-result">
				<div class="movimentacao-desc" i18n="@@treino-bi:movimentacao">
					Esses são os dias de maior e menor movimento em seu negócio!
				</div>
				<div class="blocks-wrapper" style="margin-bottom: 3em">
					<div class="block">
						<div class="type-p-small" i18n="@@treino-bi:maior-movimento">
							Maior Movimento
						</div>
						<ng-container
							*ngTemplateOutlet="
								icon;
								context: { periodo: maiorPeriodo?.periodo }
							"></ng-container>
						<div class="type-h5-bold weekday">
							{{
								maiorPeriodo ? diaSemana.getLabel(maiorPeriodo.diaSemana) : "-"
							}}
						</div>
						<div class="type-caption execucoes">
							{{ maiorPeriodo ? maiorPeriodo.execucoes : "-" }}
							<span i18n="@@treino-bi:numero-execucoes">execuções</span>
						</div>
					</div>
					<div class="block">
						<div class="type-p-small" i18n="@@treino-bi:menor-movimento">
							Menor Movimento
						</div>
						<ng-container
							*ngTemplateOutlet="
								icon;
								context: { periodo: menorPeriodo?.periodo }
							"></ng-container>
						<div class="type-h5-bold weekday">
							{{
								menorPeriodo ? diaSemana.getLabel(menorPeriodo.diaSemana) : "-"
							}}
						</div>
						<div class="type-caption execucoes">
							{{ menorPeriodo ? menorPeriodo.execucoes : "-" }}
							<span i18n="@@treino-bi:numero-execucoes">execuções</span>
						</div>
					</div>
				</div>
			</div>
		</pacto-cat-card-plain>
	</div>
</div>

<ng-template #icon let-periodo="periodo">
	<div *ngIf="periodo === 'MANHA'" class="icon-wrapper sun">
		<i class="pct pct-sun"></i>
	</div>
	<div *ngIf="periodo !== 'MANHA'" class="icon-wrapper moon">
		<i class="pct pct-moon"></i>
	</div>
</ng-template>

<pacto-traducoes-xingling #diaSemana>
	<span i18n="@treino-bi:dias:segunda" xingling="segunda">Segunda</span>
	<span i18n="@treino-bi:dias:terca" xingling="terca">Terça</span>
	<span i18n="@treino-bi:dias:quarta" xingling="quarta">Quarta</span>
	<span i18n="@treino-bi:dias:quinta" xingling="quinta">Quinta</span>
	<span i18n="@treino-bi:dias:sexta" xingling="sexta">Sexta</span>
	<span i18n="@treino-bi:dias:sabado" xingling="sabado">Sabado</span>
	<span i18n="@treino-bi:dias:domingo" xingling="domingo">Domingo</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #diaSemanaCurto>
	<span i18n="@treino-bi:dias-curto:segunda" xingling="segunda">Seg</span>
	<span i18n="@treino-bi:dias-curto:terca" xingling="terca">Ter</span>
	<span i18n="@treino-bi:dias-curto:quarta" xingling="quarta">Qua</span>
	<span i18n="@treino-bi:dias-curto:quinta" xingling="quinta">Qui</span>
	<span i18n="@treino-bi:dias-curto:sexta" xingling="sexta">Sex</span>
	<span i18n="@treino-bi:dias-curto:sabado" xingling="sabado">Sab</span>
	<span i18n="@treino-bi:dias-curto:domingo" xingling="domingo">Dom</span>
</pacto-traducoes-xingling>
