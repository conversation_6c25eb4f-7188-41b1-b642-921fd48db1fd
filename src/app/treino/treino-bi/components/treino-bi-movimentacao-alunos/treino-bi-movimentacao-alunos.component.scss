@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	color: $pretoPri;
}

.movimentacao-result-aux {
	margin-top: 30px;
	@media (min-width: $bootstrap-breakpoint-lg) {
		margin-top: 0px;
	}
}

.movimentacao-result {
	display: flex;
	flex-wrap: wrap;
	flex-direction: column;
	text-align: center;
	height: 100%;

	.movimentacao-desc {
		@extend .type-h6;
		padding: 20px 10px;
		flex-grow: 1;
	}

	.blocks-wrapper {
		display: flex;
		margin-bottom: 10px;

		& .block {
			padding: 0px 20px 0px 20px;
			flex-basis: 50%;

			&:first-of-type() {
				border-right: 1px solid $cinzaClaro02;
			}
		}
	}

	.percent-over-agerage,
	.execucoes {
		color: $cinza04;
	}

	.weekday {
		line-height: 2em;
		text-transform: uppercase;
	}

	.icon-wrapper {
		padding: 7px 0px;
	}

	.pct {
		font-size: 32px;
	}

	.sun {
		color: $pequizaoPri;
	}

	.moon {
		color: $azulim05;
	}

	pacto-cat-percent-bar-simple {
		display: block;
		margin: 5px 0px;
	}
}
