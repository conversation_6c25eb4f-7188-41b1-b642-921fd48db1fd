@import "~src/assets/scss/pacto/plataforma-import.scss";

$custom-large-breakpoint: 1300px;

.bi-grid {
	@media (min-width: $custom-large-breakpoint) {
		display: flex;
		.indicadores-icone {
			width: 18%;
			margin-bottom: 0px;
			margin-right: 30px;
		}
		.indicadores-porcentagem {
			flex-grow: 1;
		}
	}
}

.indicadores-icone {
	width: 100%;
	min-width: 185px;
	margin-bottom: 30px;

	display: grid;
	grid-gap: 30px;
	grid-template-columns: 1fr;

	&.integradoZW {
		grid-template-columns: 1fr;
	}

	@media (min-width: $bootstrap-breakpoint-md) {
		grid-template-columns: 1fr 1fr;
		&.integradoZW {
			grid-template-columns: 1fr 1fr 1fr;
		}
	}

	@media (min-width: $bootstrap-breakpoint-lg) {
		grid-template-columns: 1fr 1fr 1fr 1fr;
		&.integradoZW {
			grid-template-columns: 1fr 1fr 1fr;
		}
	}

	@media (min-width: $custom-large-breakpoint) {
		grid-template-columns: 1fr;
		&.integradoZW {
			grid-template-columns: 1fr;
		}
	}
}

.indicadores-porcentagem {
	display: grid;
	grid-gap: 30px;
	grid-template-columns: 1fr;

	@media (min-width: $bootstrap-breakpoint-md) {
		grid-template-columns: 1fr 1fr;
		&.integradoZW {
			grid-template-columns: 1fr 1fr;
		}
	}

	@media (min-width: $custom-large-breakpoint) {
		grid-template-columns: 1fr 1fr 1fr;
		&.integradoZW {
			grid-template-columns: 1fr 1fr 1fr 1fr;
		}
	}
}

.button-compartilhar {
	display: -webkit-inline-box;
	font-size: small;
}

.btn-primary {
	color: $branco;
	background-color: $azulimPri !important;
	border-color: $azulimPri !important;
}

.pct-share-2 {
	padding-right: 5px;
}

::ng-deep.fa {
	margin-left: 5px;
}

.preview-object {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	border: 1px solid #c7c7c7;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
	margin-left: 25px;
	margin-right: -15px;
}
