import {
	AfterViewInit,
	ChangeDetector<PERSON><PERSON>,
	<PERSON>mponent,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ProgramaAcompanhamento, TreinoApiBiService } from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { LocalizationService } from "@base-core/localization/localization.service";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-acompanhar-treino",
	templateUrl: "./modal-acompanhar-treino.component.html",
	styleUrls: ["./modal-acompanhar-treino.component.scss"],
})
export class ModalAcompanharTreinoComponent implements OnInit, AfterViewInit {
	@ViewChild("finalizarModalMsg", { static: true }) finalizarModalMsg;
	@ViewChild("alterarSerieModalMsg", { static: true }) alterarSerieModalMsg;
	@ViewChild("modalWarningMsg", { static: true }) modalWarningMsg;
	@ViewChild("modalSerieRealizadaMsgErro", { static: true })
	modalSerieRealizadaMsgErro;
	programaAcompanhamento: ProgramaAcompanhamento;
	professorAcompanhamento: any;
	alunoAcompanhamento: any;
	formGroup: FormGroup = new FormGroup({
		ficha: new FormControl(),
	});

	fichas: Array<any> = [];
	series: Array<any> = [];
	fichaSelecionada;
	fichaSelecionadaNome;
	atividadeSelecionada;
	primeiraFicha;
	primeiraAtividade;
	ultimaAtividade;
	ultimaSerie;
	timeMask;
	btnConcluirAcompanhamento = false;
	btnConclu = true;
	contador;
	contadorSerie = 1;
	tempoRef;
	funcionando = false;
	validacaoRealizada = false;
	textoSituacao = "Iniciar";
	tempo;
	acompanhamentoFinalizado = true;
	msgAtividade;

	constructor(
		private treinoBiService: TreinoApiBiService,
		private localization: LocalizationService,
		private utils: WindowUtilService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef
	) {
		this.timeMask = {
			mask: this.localization.getTimeMask(),
		};
	}

	get minuteSecondMask() {
		return this.localization.getMinuteSecondMask();
	}

	get somenteInteiroMask() {
		return this.localization.getNumberMask();
	}

	ngOnInit() {
		this.loadData();
		this.initArrayFichas();
		this.finalizarAcompanhamentoPorAtividade(this.primeiraFicha);
		this.tempo = 0;
	}

	ngAfterViewInit() {
		this.formGroup.get("ficha").setValue(this.primeiraFicha.id);
		this.localizarFicha(this.primeiraFicha.id);
		this.localizarAtividade(this.primeiraAtividade);
		this.finalizarAcompanhamento(false);
		this.cd.detectChanges();
	}

	private loadData() {}

	private initArrayFichas() {
		this.primeiraFicha = this.programaAcompanhamento.fichas.find(
			(x) => x !== undefined
		);
		this.primeiraAtividade = this.primeiraFicha.atividades.find(
			(x) => x !== undefined
		);
		this.programaAcompanhamento.fichas.forEach((item) => {
			this.fichas.push({ id: item.id, label: item.nome });
		});
		this.cd.detectChanges();
	}

	public localizarFicha(ficha) {
		this.msgAtividade = false;
		this.fichaSelecionada = this.programaAcompanhamento.fichas.find(
			(item) => Number(item.id) === Number(ficha)
		);
		this.primeiraAtividade = this.fichaSelecionada.atividades.find(
			(x) => x !== undefined
		);
		if (this.primeiraAtividade === undefined) {
			this.fichaSelecionadaNome = this.fichaSelecionada.nome;
			this.msgAtividade = true;
			this.cd.detectChanges();
		}
		this.ultimaAtividade = this.fichaSelecionada.atividades.findLast(
			(x) => x !== undefined
		);
		this.ultimaSerie = this.ultimaAtividade.series.findLast(
			(x) => x !== undefined
		);
		this.localizarAtividade(this.primeiraAtividade);
		let cont = 0;
		this.fichaSelecionada.atividades.forEach((atividade) => {
			atividade.series.forEach((serie) => {
				if (serie.serieRealizada === true) {
					cont++;
				}
			});
			atividade.seriesRealizadas = cont;
			cont = 0;
		});
		this.finalizarAcompanhamentoPorAtividade(this.fichaSelecionada);
		this.cd.detectChanges();
	}

	public localizarAtividade(atividade) {
		this.atividadeSelecionada = atividade;
		this.series = [];
		this.atividadeSelecionada.series.forEach((serie) => {
			this.fillOutForm(serie);
			if (serie.serieRealizada === true) {
				serie.icon = "pct-check-circle";
			}
		});
		this.clearTempo();
	}

	public finalizarAcompanhamento(finalizar) {
		this.contadorSerie = 0;
		let contSeriesRealizadas = 0;
		this.fichaSelecionada.atividades.forEach((atividade) => {
			atividade.series.forEach((serie) => {
				if (serie.serieRealizada === false) {
					this.contadorSerie++;
				} else {
					contSeriesRealizadas++;
				}
			});
			atividade.seriesRealizadas = contSeriesRealizadas;
			contSeriesRealizadas = 0;
		});

		if (finalizar) {
			this.btnConcluirAcompanhamento = false;
			if (this.contadorSerie !== 0) {
				const modalWarningMsg = this.modalWarningMsg.nativeElement.innerHTML;
				this.snotifyService.success(modalWarningMsg);
			}

			if (this.contadorSerie === 0) {
				const modalSucessoMsg = this.finalizarModalMsg.nativeElement.innerHTML;
				this.treinoBiService
					.finalizarAcompanhamento(
						this.alunoAcompanhamento,
						this.professorAcompanhamento,
						this.programaAcompanhamento.programa.codigo,
						this.fichaSelecionada.id
					)
					.subscribe(() => {
						this.snotifyService.success(modalSucessoMsg);

						setTimeout(() => {
							this.beforeDismiss();
						}, 2000);
					});
			}
		}
		this.finalizarAcompanhamentoPorAtividade(this.fichaSelecionada);
	}
	public testeAcompanhamento(finalizar) {
		this.atividadeSelecionada.series.forEach((serie) => {
			this.treinoBiService.alterarSerieRealizada(serie.id).subscribe((resp) => {
				if (resp.retorno && resp.retorno === "OK") {
				} else {
				}
			});

			serie.serieRealizada = true;
			serie.icon = "pct-check-circle";
			this.cd.detectChanges();
		});
		this.finalizarAcompanhamentoPorAtividade(this.fichaSelecionada);
		this.atividadeSelecionada.seriesRealizadas =
			this.atividadeSelecionada.series.length;
		this.snotifyService.success("Atividade acompanhada com sucesso.");
		this.cd.detectChanges();
	}
	public serieForm() {
		return new FormGroup({
			id: new FormControl(),
			repeticoes: new FormControl(),
			repeticaoComp: new FormControl(),
			carga: new FormControl(),
			cargaComp: new FormControl(),
			cadencia: new FormControl(),
			descanso: new FormControl(),
			serieRealizada: new FormControl(),
		});
	}

	private fillOutForm(serie) {
		const form: FormGroup = this.serieForm();
		if (serie) {
			form.get("id").setValue(serie.id);
			form.get("cadencia").setValue(serie.cadencia);
			form.get("carga").setValue(serie.carga);
			form
				.get("descanso")
				.setValue(
					this.utils.convertSecondsIntoMinutesSecondsLabel(serie.descanso)
				);
			form.get("repeticoes").setValue(serie.repeticoes);
			form.get("repeticaoComp").setValue(serie.repeticaoComp);
			form.get("cargaComp").setValue(serie.cargaComp);
			form.get("serieRealizada").setValue(serie.serieRealizada);
			this.series.push(form);
			form.get("descanso").disable();
		}
		this.cd.detectChanges();
	}

	private serieAnteriorRealizada(serieId) {
		this.fichaSelecionada.atividades.forEach((atividade) => {
			atividade.series.forEach((serie) => {
				if (serie.id === serieId) {
					const indexSerieAtual = atividade.series.indexOf(serie);
					if (indexSerieAtual > 0) {
						if (
							atividade.series[indexSerieAtual - 1].serieRealizada === false
						) {
							const modalSerieRealizadaMsgErro =
								this.modalSerieRealizadaMsgErro.nativeElement.innerHTML;
							this.snotifyService.error(modalSerieRealizadaMsgErro);
							this.validacaoRealizada = true;
						}
					} else {
						this.validacaoRealizada = false;
					}
				}
			});
		});
	}

	startTempo(serieStart) {
		if (serieStart.icon && serieStart.icon === "pct-check-circle") {
			return;
		}
		const serieId = serieStart.id;
		this.serieAnteriorRealizada(serieId);
		if (this.validacaoRealizada) {
			this.validacaoRealizada = false;
			return;
		} else {
			this.funcionando = !this.funcionando;
			if (this.funcionando) {
				this.textoSituacao = "Parar";
				this.tempo++;
				this.tempoRef = setInterval(() => {
					this.contador = this.tempo++;
					this.getDisplayTempo(this.contador);
				}, 1000);
				serieStart.icon = "pct-check 1";
				serieStart.title = "Finalizar acompanhamento deste exercício";
			} else {
				this.treinoBiService
					.alterarSerieRealizada(serieId)
					.subscribe((result) => {
						this.atividadeSelecionada.series.forEach((serie) => {
							if (serie.id === serieId) {
								this.fillOutForm(serie);
							}
						});
					});
				serieStart.serieRealizada = true;
				this.finalizarAcompanhamento(false);
				serieStart.icon = "pct-check-circle";
				this.tempo--;
				this.textoSituacao = "Retornar";
				clearInterval(this.tempoRef);
				this.cd.detectChanges();
			}
		}
	}

	clearTempo() {
		this.funcionando = false;
		this.textoSituacao = "Iniciar";
		this.contador = undefined;
		this.tempo = 0;
		clearInterval(this.tempoRef);
		this.cd.detectChanges();
	}

	getDisplayTempo(tempo: number) {
		let min = "" + Math.floor((tempo % 3600) / 60);
		let seg = "" + Math.floor((tempo % 3600) % 60);

		if (Number(min) < 10) {
			min = "0" + min;
		} else {
			min = "" + min;
		}
		if (Number(seg) < 10) {
			seg = "0" + seg;
		} else {
			seg = "" + seg;
		}

		this.contador = min + ":" + seg;
		this.cd.detectChanges();
	}
	alterarSerie(serieFormGroup) {
		const dto = serieFormGroup.getRawValue();
		dto.descanso = this.utils.convertMinutesSecondsIntoSeconds(dto.descanso);
		this.treinoBiService.alterarSerie(dto.id, dto).subscribe((result) => {
			this.atividadeSelecionada.series.forEach((serie) => {
				if (serie.id === dto.id) {
					serie.repeticoes = dto.repeticoes;
					serie.repeticaoComp = dto.repeticaoComp;
					serie.carga = dto.carga;
					serie.cargaComp = dto.cargaComp;
					serie.cadencia = dto.cadencia;
					serie.descanso = dto.descanso;
					this.fillOutForm(serie);
					const modalSerieSucessoMsg =
						this.alterarSerieModalMsg.nativeElement.innerHTML;
					this.snotifyService.success(modalSerieSucessoMsg);
				}
			});
		});
	}

	public finalizarAcompanhamentoPorAtividade(ficha) {
		this.contadorSerie = 0;
		let validador = true;
		ficha.atividades.forEach((atividade) => {
			atividade.series.forEach((series) => {
				if (!series.serieRealizada) {
					validador = false;
				}
			});
		});
		this.btnConcluirAcompanhamento = validador;
	}

	beforeDismiss() {
		window.location.reload();
	}
}
