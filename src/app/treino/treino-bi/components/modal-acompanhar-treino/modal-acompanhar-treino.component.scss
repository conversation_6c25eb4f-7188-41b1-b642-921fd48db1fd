@import "src/assets/scss/pacto/plataforma-import.scss";

::ng-deep .modal-acompanhar-treino {
	.pacto-modal-wrapper {
		.modal-titulo {
			left: calc(50vh - 460px);
			z-index: 999;
			width: calc(100vw - 460px);
			max-width: 1230px;
			min-width: 1050px;
			background-color: #fff;
			border-top-left-radius: 0.3rem;
			border-top-right-radius: 0.3rem;
		}
	}
}

.background-modal-atividades {
	//position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 999;

	.pos-header {
		padding: 0px 23px 23px 23px;
		height: 100%;
		overflow: auto;
	}

	.content {
		left: calc(50vh - 460px);
		z-index: 999;
		position: relative;
		display: flex;
		flex-direction: column;
		pointer-events: auto;
		background-color: $branco;
		background-clip: padding-box;
		outline: 0;
		width: calc(100vw - 460px);
		max-width: 1230px;
		min-width: 1050px;
		overflow: hidden;
	}
}

.scroll-sticky {
	max-height: calc(100vh - 320px);
	overflow: auto;
}

.pretty-scroll {
	&::-webkit-scrollbar {
		padding: 11px 0 11px 11px;
		width: 11px;
		height: 18px;
	}

	&::-webkit-scrollbar-thumb {
		min-height: 100px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	&::-webkit-scrollbar-button {
		width: 11px;
		height: 0;
		display: none;
	}

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

.container-add-atividade {
	padding: 0px 24px;
	border-radius: 4px;

	pacto-cat-multi-select-filter {
		::ng-deep.pacto-label {
			font-size: 14px;
			color: $pretoPri !important;
		}
	}

	.block-info-atividade {
		background-color: #eff2f7;

		.lazy {
			border-radius: 59px;
			margin-left: 10px;
		}
	}

	.block-info-atividade:hover,
	.block-info-atividade:active {
		cursor: pointer;
		background-color: $azulPactoPri;

		.descricao-atividade {
			color: #ffffff;
		}

		.pct-chevron-right {
			color: $cinza01;
		}
	}

	pacto-cat-form-select {
		margin: 0;
	}

	::ng-deep .pacto-label {
		display: none;
	}

	.adicionadas {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 9px;
		text-align: center;
		margin-top: 20px;

		span {
			font-weight: 700;
		}
	}

	.msgAtividade {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 9px;
		margin-top: 20px;

		span {
			font-weight: 700;
		}
	}
}

.mgtp24 {
	margin-top: 24px;
}

.header {
	width: 100%;
	top: 0;
	left: 0;
	padding: 50px 32px;

	.titulo-msg {
		font-size: 12px;
		font-weight: 400;
	}

	.titulo-modal {
		font-size: 16px;
		font-weight: 600;
	}

	.pct-x {
		font-size: 40px;
		font-weight: 400;
		cursor: pointer;
	}
}

.block-info-atividade {
	margin-top: 6px;
	padding: 7px 0;

	.lazy {
		height: 54px;
		width: 54px;
	}

	pacto-cat-person-avatar {
		vertical-align: middle;
		display: inline-block;
		border-radius: 5px;
		border: 1px solid #b4b7bb;
	}

	.descricao-atividade {
		color: $cinzaPri;
		font-weight: 600;
		font-size: 12px;
		width: 60%;
		display: inline-block;
		vertical-align: middle;
		margin-left: 19px;
	}

	.block-actions {
		display: inline-block;
		vertical-align: middle;
		width: calc(40% - 87px);
		text-align: right;

		i {
			cursor: pointer;
		}

		.btn {
			padding: 0;
		}

		.pct-chevron-right {
			color: $azulPacto01;
			font-size: 24px;
		}
	}
}

.container-series {
	margin: 0px 24px 0 -24px;

	.contador {
		text-align: right;
	}
}

pacto-cat-accordion {
	display: block;
	margin-top: 30px;
}

::ng-deep .header-section {
	.accordeao {
		display: inline-block;
		margin-left: 20%;
	}
}

.col-center {
	label {
		margin-left: 5px;
	}

	input {
		margin-left: 5px;
		width: calc(100% - 5px);
	}

	width: calc(50% - 127px);
	display: inline-block;
	vertical-align: top;
}

input {
	&.cadencia {
		width: 95%;
	}

	&.descanso {
		width: 115px;
	}

	width: 68px;
}

.campo-serie {
	display: inline-block;
	height: 40px;
	width: 100%;
	left: 0px;
	top: 0px;
	border-radius: 4px;

	label {
		padding-left: 6px;
		margin-right: 12px;
	}

	input {
		border: 1px solid $cinza03;
		color: $pretoPri;
	}

	input:hover,
	input:focus {
		border: 1px solid $gelo03;
	}

	.input-campo-serie {
		margin-left: 38px;
	}
}

input {
	width: 185px;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

.row-serie-1 {
	padding: 22px 21px 37px 21px;
}

.row-serie-2 {
	padding: 0px 21px 37px 21px;
}

.row-button {
	float: left;
}

.action-status-modify {
	float: right;
	text-transform: uppercase;
	padding-right: 8px;
	margin-top: 45px;
	display: inline-block;
}

::ng-deep .status-control {
	display: none;
}

.accordion {
	width: 95%;
	display: inline-block;
}

.lazy-serie {
	border: 1px solid $cinzaPri;
	border-radius: 59px;
	margin: 40px 0 0 20px;
	height: 80px;
	width: 80px;
}

.timeline {
	width: 550px;
	height: 5px;
	display: flex;
	justify-content: space-around;
	margin: 80px auto;
	background: #eff2f7;
	border-radius: 2px;

	div {
		display: inline-block;
		width: 100%;
		text-align: center;
		line-height: 1.2;
		position: relative;
	}

	.iconeTrue {
		top: -5px;
		width: 15px;
		height: 15px;
		border-radius: 50%;
		border: 1px solid $chuchuzinho03;
		background: #eff2f7;
	}

	.iconeFalse {
		top: -5px;
		width: 15px;
		height: 15px;
		border-radius: 50%;
		border: 1px solid #bac7cc;
		background: #eff2f7;
	}

	.serieRealizada {
		background: $chuchuzinho03;
		top: 0px;
		display: inline-block;
		width: 100%;
		text-align: center;
		line-height: 1.2;
		position: relative;

		i {
			z-index: 999;
			color: $chuchuzinho03;
		}
	}
}

.pct-play {
	color: $azulPacto01;
	font-size: 16px;
}

.pct-check {
	font-size: 16px;
}

.pct-check-circle {
	color: $verdinho01;
	font-size: 16px;
}

.button-alterar-serie {
	float: right;
	margin-right: 70px;
}

.tamanho-input {
	width: 185px;
}

.container {
	display: flex;
}

.push {
	margin-left: auto;
}
