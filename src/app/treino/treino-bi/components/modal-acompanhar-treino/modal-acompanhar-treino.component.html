<div class="background-modal-atividades">
	<div class="content">
		<div class="header">
			<div class="row">
				<div class="col-md-12">
					<div class="row">
						<div class="col-md-2">
							<pacto-cat-person-avatar
								[borderRadius]="50"
								[diameter]="64"
								[showBorder]="true"
								[uri]="
									programaAcompanhamento?.imagemUri
										? programaAcompanhamento?.imagemUri
										: 'assets/images/empty-image.png'
								"></pacto-cat-person-avatar>
						</div>

						<div class="col-md-4">
							<div class="titulo-msg">Nome do Aluno</div>
							<div class="titulo-modal" title="aluno nome">
								{{ programaAcompanhamento?.nomeAluno | uppercase }}
							</div>
						</div>

						<div class="col-md-2">
							<div class="titulo-msg">Nível</div>
							<div class="titulo-modal" title="nivel">
								{{ programaAcompanhamento?.nivelAluno | uppercase }}
							</div>
						</div>

						<div class="col-md-4">
							<div class="titulo-msg">Fichas de hoje</div>
							<div class="titulo-modal" title="fichas do dia">
								<span
									*ngFor="
										let ficha of programaAcompanhamento?.fichas;
										let i = index
									">
									{{ ficha.nome | uppercase }}
									<span
										*ngIf="
											programaAcompanhamento.fichas.length > 1 &&
											programaAcompanhamento.fichas.length - 1 != i
										">
										,
									</span>
								</span>
							</div>
							<pacto-cat-button
								(click)="finalizarAcompanhamento(true)"
								*ngIf="btnConcluirAcompanhamento"
								[v2]="true"
								id="finalizar-acompanhamento"
								label="Concluir Acompanhamento"></pacto-cat-button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="body">
			<div class="row">
				<div class="col-md-4">
					<div class="container-atividades">
						<div class="container-add-atividade">
							<pacto-cat-form-select
								(change)="localizarFicha(formGroup.get('ficha').value)"
								[control]="formGroup.get('ficha')"
								[id]="'select-filtro'"
								[items]="fichas"></pacto-cat-form-select>

							<div *ngIf="!msgAtividade" class="mensagem-atividade">
								<div class="adicionadas">Exercícios</div>
								<div #atvadicionadas class="scroll-sticky pretty-scroll">
									<ng-container>
										<div
											(click)="localizarAtividade(atividade)"
											*ngFor="let atividade of fichaSelecionada?.atividades"
											class="block-info-atividade">
											<img
												class="lazy"
												loading="lazy"
												src="{{
													atividade.atividade.images.length > 0
														? atividade.atividade.images[0].uri
														: 'assets/images/no-image.jpg'
												}}" />
											<div
												class="descricao-atividade"
												title="NOME ORIGINAL: EX. MOBILIDADE">
												<span>{{ atividade.atividade.nome }}</span>
												<br />
												<span>
													<span *ngIf="atividade.seriesRealizadas < 10">0</span>
													{{ atividade.seriesRealizadas }}
													/
													<span *ngIf="atividade.series?.length < 10">0</span>
													{{ atividade.series?.length || 0 }} Séries
												</span>
											</div>
											<div class="block-actions">
												<i class="pct pct-chevron-right"></i>
											</div>
										</div>
									</ng-container>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div *ngIf="msgAtividade" class="col-md-12">
					<div class="container-atividades">
						<div class="container-add-atividade">
							<div class="msgAtividade">
								A FICHA {{ fichaSelecionadaNome }} NÃO POSSUI ATIVIDADES PARA
								ACOMPANHAMENTO.
							</div>
						</div>
					</div>
				</div>
				<div *ngIf="!msgAtividade" class="col-md-8">
					<div class="container-series">
						<div class="row">
							<div class="contagem col-md-6">
								{{ atividadeSelecionada?.atividade.nome }}
							</div>
							<div class="contador col-md-6">
								<div class="container">
									<section class="contador-label">
										<div *ngIf="contador; else elseBlock">{{ contador }}</div>
										<ng-template #elseBlock>00:00</ng-template>
									</section>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-2">
								<img
									class="lazy-serie"
									loading="lazy"
									src="{{
										atividadeSelecionada?.atividade.images.length > 0
											? atividadeSelecionada?.atividade.images[0].uri
											: 'assets/images/no-image.jpg'
									}}" />
							</div>
							<div class="col-md-10">
								<div class="timeline">
									<div
										*ngFor="
											let serie of atividadeSelecionada?.series;
											let i = index
										"
										class="{{
											serie.serieRealizada === true ? 'serieRealizada' : ''
										}}">
										<div
											class="{{
												serie.serieRealizada === true
													? 'iconeTrue'
													: 'iconeFalse'
											}}"></div>
										<span>
											<br />
											Série {{ i + 1 }}
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-12">
								<div #atvadicionadas class="scroll-sticky pretty-scroll">
									<ng-container>
										<div
											*ngFor="
												let serie of atividadeSelecionada?.series;
												let i = index
											">
											<div>
												<pacto-cat-accordion
													#accordionPermissoes
													class="accordion"
													title="Série {{ i + 1 }}">
													<accordion-header>
														<div class="accordeao">
															Repetições: {{ serie.repeticoes }}
														</div>
														<div class="accordeao">
															Carga: {{ serie.carga }} kg
														</div>
													</accordion-header>
													<accordion-body>
														<div
															*ngFor="
																let formGroup of series;
																let index = index
															">
															<div *ngIf="index == i" class="row row-serie-1">
																<div class="campo-serie col-md-6">
																	<label class="col-md-2">Repetições:</label>
																	<input
																		[formControl]="formGroup.get('repeticoes')"
																		class="input-campo-serie"
																		id="input-repeticoes-serie-{{ index }}"
																		type="text" />
																</div>
																<div class="campo-serie col-md-6">
																	<label>Carga</label>
																	<input
																		[formControl]="formGroup.get('carga')"
																		class="input-campo-serie"
																		id="input-carga-serie-{{ index }}"
																		type="text" />
																</div>
															</div>
															<div *ngIf="index == i" class="row row-serie-2">
																<div class="campo-serie col-md-6">
																	<label class="col-md-2">Cadência:</label>
																	<input
																		[formControl]="formGroup.get('cadencia')"
																		class="input-campo-serie tamanho-input"
																		id="input-cadencia-serie-{{ index }}"
																		maxlength="6"
																		type="text" />
																</div>
																<div class="campo-serie col-md-6">
																	<label class="col-md-2">Descanso:</label>
																	<input
																		[formControl]="formGroup.get('descanso')"
																		[textMask]="{
																			guide: false,
																			mask: minuteSecondMask
																		}"
																		class="input-campo-serie tamanho-input"
																		id="input-descanso-serie-{{ index }}"
																		placeholder="mm:ss"
																		type="text" />
																</div>
															</div>
															<div
																*ngIf="index == i"
																class="row row-button button-alterar-serie">
																<pacto-cat-button
																	(click)="alterarSerie(formGroup)"
																	[v2]="true"
																	id="alterar-serie"
																	label="Alterar Série"></pacto-cat-button>
															</div>
														</div>
													</accordion-body>
												</pacto-cat-accordion>
												<div
													(click)="startTempo(serie)"
													class="action-status-modify"
													id="btn-iniciar-tempo-{{ i }}"
													title="{{
														serie.title
															? serie.title
															: 'Iniciar o acompanhamento deste exercício'
													}}">
													<i
														class="pct {{
															serie.icon ? serie.icon : 'pct-play'
														}}"></i>
												</div>
											</div>
										</div>
									</ng-container>
									<div class="container">
										<pacto-cat-button
											class="push"
											*ngIf="
												atividadeSelecionada?.series.length !=
												atividadeSelecionada?.seriesRealizadas
											"
											(click)="testeAcompanhamento(atividadeSelecionada)"
											[v2]="true"
											id="finalizar-acompanhamento-atividade"
											label="Concluir acompanhamento de atividade"></pacto-cat-button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<span
	#finalizarModalMsg
	[hidden]="true"
	i18n="@@treino-bi:finalizado-sucesso:tooltip-icon">
	Acompanhamento concluído com sucesso.
</span>
<span
	#alterarSerieModalMsg
	[hidden]="true"
	i18n="@@treino-bi:alterado-sucesso:tooltip-icon">
	Série alterada com sucesso.
</span>
<span
	#modalWarningMsg
	[hidden]="true"
	i18n="@@treino-bi:alterado-sucesso:tooltip-icon">
	Não é possível concluir o acompanhamento, todas as séries devem ser
	finalizadas.
</span>
<span
	#modalSerieRealizadaMsgErro
	[hidden]="true"
	i18n="@@treino-bi:alterado-sucesso:tooltip-icon">
	Não é possível concluir a série, conclua a anterior.
</span>
