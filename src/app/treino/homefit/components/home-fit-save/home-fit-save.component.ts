import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Router, ActivatedRoute } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { ModalService } from "@base-core/modal/modal.service";
import { HomeFitService } from "@base-core/home-fit/home-fit.service";
import { AtividadeHomeFitAddComponent } from "../atividade-home-fit-add/atividade-home-fit-add.component";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { SessionService } from "@base-core/client/session.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SeletorImagemComponent } from "old-ui-kit";

@Component({
	selector: "pacto-ficha-nivel-edit",
	templateUrl: "./home-fit-save.component.html",
	styleUrls: ["./home-fit-save.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeFitSaveComponent implements OnInit {
	@ViewChild("seletorImagem", { static: false })
	seletorImagem: SeletorImagemComponent;
	id;
	edit;
	imageUri;
	atividades;
	urlImagem: string;
	imagemData: string;

	constructor(
		private modal: ModalService,
		private modalImg: NgbModal,
		private router: Router,
		private route: ActivatedRoute,
		private homeFitService: HomeFitService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private notify: SnotifyService
	) {}

	formGroup = new FormGroup({
		nome: new FormControl(null, [Validators.required, Validators.minLength(3)]),
		descricao: new FormControl(),
		tagredes: new FormControl(),
	});

	drop(event: CdkDragDrop<string[]>) {
		this.reordenarArray(event.previousIndex, event.currentIndex);
	}

	private reordenarArray(previous: number, current: number) {
		const removed = this.atividades.splice(previous, 1)[0];
		this.atividades.splice(current, 0, removed);
		let i = 0;
		this.atividades.forEach((at: any) => {
			at.orderm = i++;
		});
		this.cd.detectChanges();
	}

	novoModeloHandler() {
		const modal = this.modal.openFullscreen(
			"Adicionar nova atividade",
			AtividadeHomeFitAddComponent
		);
		modal.result.then(
			(atividade) => {
				atividade.idYouTube = this.getYoutubeID(atividade.idYouTube);
				this.atividades.push(atividade);
				let i = 0;
				this.atividades.forEach((at: any) => {
					at.orderm = i++;
				});
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	youtube(url): boolean {
		try {
			const YoutubeRegexObject_v1 =
				/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i;
			const YoutubeVideoID = url.match(YoutubeRegexObject_v1);
			return YoutubeVideoID[1];
		} catch (e) {
			return false;
		}
	}

	getYoutubeID(url): string {
		try {
			const YoutubeRegexObject_v1 =
				/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i;
			const YoutubeVideoID = url.match(YoutubeRegexObject_v1);
			return YoutubeVideoID[1];
		} catch (e) {
			return url;
		}
	}

	ngOnInit() {
		const params = this.route.snapshot.params;
		this.atividades = new Array();
		if (params.id !== "adicionar") {
			if (!this.homeFitService.selected) {
				this.router.navigate(["/treino", "home-fit", "lista-treinos"]);
			}
			this.id = params.id;
			this.edit = true;
			this.fillOutForm();
		} else {
			this.edit = false;
			this.cd.detectChanges();
		}
	}

	private fillOutForm(): void {
		if (this.homeFitService.selected) {
			this.formGroup.patchValue({
				nome: this.homeFitService.selected.nomeDoTreino,
				descricao: this.homeFitService.selected.descricao,
				tagredes: this.homeFitService.selected.tagredes
					? this.homeFitService.selected.tagredes
					: "",
			});
			if (
				this.homeFitService.selected.media &&
				this.homeFitService.selected.media[0].url
			) {
				this.imageUri = this.homeFitService.selected.media[0].url;
			}
			this.atividades = this.homeFitService.selected.atividades
				? this.homeFitService.selected.atividades
				: [];

			this.atividades.sort((a, b) => {
				const keyA = a.orderm;
				const keyB = b.orderm;
				// Compare
				if (keyA < keyB) {
					return -1;
				}
				if (keyA > keyB) {
					return 1;
				}
				return 0;
			});
		}
	}

	removeAtividade(atividade: any) {
		let indexRemover = null;
		this.atividades.forEach((at: any, idx) => {
			if (at.orderm === atividade.orderm) {
				indexRemover = idx;
			}
		});
		if (indexRemover !== null) {
			this.atividades.splice(indexRemover, 1);
		}
		this.cd.detectChanges();
	}

	removendoImagem() {
		this.imageUri = undefined;
	}

	salvarHandler() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			this.homeFitService
				.dadosApp(this.sessionService.chave)
				.subscribe((data) => {
					if (data.erro) {
						this.notify.error(
							"Não foi possível encontrar a chave da academia na base do app."
						);
					} else {
						this.homeFitService.infoapp = data.sucesso[0];
						this.router.navigate([
							"treino",
							"home-fit",
							"save-treino",
							"adicionar",
						]);
						const dto: any = this.formGroup.getRawValue();
						const media = {
							url: this.imageUri,
							tipo: "IMAGEM",
							duracao: "",
						};

						let i = 0;
						this.atividades.forEach((at: any) => {
							at.orderm = i++;
						});
						const refUsuario = this.homeFitService.refUsuario;
						const treino = {
							documentKey: this.edit ? this.id : undefined,
							nomeDoTreino: dto.nome,
							descricao: dto.descricao,
							clienteApp: this.homeFitService.infoapp.documentKey,
							treino: new Date().getTime(),
							media: [media],
							nivel: "",
							tagredes: dto.tagredes,
							atividades: this.atividades,
							nomeProfessor: this.sessionService.loggedUser.nome,
							fotoProfessor: this.sessionService.loggedUser.imageUri,
							refUsuarioApp: refUsuario
								? refUsuario
								: this.sessionService.loggedUser.username,
						};

						if (this.seletorImagem && this.seletorImagem.imagemData) {
							this.homeFitService
								.uploadImagem(this.seletorImagem.imagemData)
								.subscribe((dataImagem) => {
									treino.media[0].url = dataImagem.sucesso;
									this.gravar(treino);
								});
						} else {
							this.gravar(treino);
						}
					}
				});
		}
	}

	gravar(treino) {
		this.homeFitService.salvarTreinoSimples(treino).subscribe(() => {
			if (this.edit) {
				this.notify.success("Treino editado com sucesso.");
				this.homeFitService.selected = treino;
				this.router.navigate([
					"/treino",
					"home-fit",
					"treino-detalhes",
					this.id,
				]);
			} else {
				this.notify.success("Treino criado com sucesso.");
				this.router.navigate(["treino", "home-fit", "lista-treinos"]);
			}
		});
	}

	cancelHandler() {
		this.router.navigate(["treino", "home-fit", "lista-treinos"]);
	}
}
