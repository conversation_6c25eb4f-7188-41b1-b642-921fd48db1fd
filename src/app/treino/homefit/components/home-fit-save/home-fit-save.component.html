<pacto-cat-layout-v2>
	<div class="nivel-edit-title">
		<span
			*ngIf="edit"
			[routerLink]="['/treino', 'home-fit', 'treino-detalhes', id]">
			<i class="pct pct-arrow-left"></i>
			Editando Treino
		</span>
		<span *ngIf="!edit" [routerLink]="['/treino', 'home-fit', 'lista-treinos']">
			<i class="pct pct-arrow-left"></i>
			Novo Treino
		</span>
	</div>

	<pacto-cat-card-plain>
		<div class="content-aux">
			<div class="row">
				<div class="col-lg-6 col-md-12">
					<pacto-cat-form-input
						[control]="formGroup.get('nome')"
						[errorMsg]="'Defina um nome com pelo menos 3 characteres.'"
						[label]="'Nome'"
						[placeholder]="'Nome do treino'"></pacto-cat-form-input>
					<pacto-cat-form-textarea
						[control]="formGroup.get('descricao')"
						[placeholder]="'Descrição do treino'"
						i18n-label="@@treino-em-casa:descricao-treino"
						label="'Descrição do treino'"></pacto-cat-form-textarea>

					<pacto-cat-form-input
						[control]="formGroup.get('tagredes')"
						[label]="'Tag para redes sociais'"
						[placeholder]="'Tag para redes sociais'"></pacto-cat-form-input>

					<div *ngIf="imageUri">
						<img src="{{ imageUri }}" style="width: 150px" />
						<div
							(click)="removendoImagem()"
							class="spanRemove"
							i18n="@@crud-benchmarks:remover-imagem">
							<i class="fa fa-trash-o"></i>
							Remover imagem
						</div>
					</div>
					<pacto-seletor-imagem
						#seletorImagem
						*ngIf="!imageUri"
						[imagemData]="imagemData"
						[nome]="'midia'"
						[url]="urlImagem"
						id="selectImag"></pacto-seletor-imagem>
				</div>
				<div class="col-lg-6 col-md-12">
					<div class="seletor-imagem">
						<div class="upper">
							<div class="modelo-label">Atividades</div>
							<div (click)="novoModeloHandler()" class="modelo-add-label">
								Nova atividade
							</div>
						</div>
					</div>
					<div (cdkDropListDropped)="drop($event)" cdkDropList>
						<ng-container *ngFor="let atividade of atividades">
							<div cdkDrag class="content-row">
								<div cdkDragHandle class="drag-handler">
									<div class="colum-icon">
										<i class="pct pct-grid"></i>
									</div>
								</div>
								<div>{{ atividade.nome }}</div>
								<div style="text-align: right">
									<a
										*ngIf="youtube(atividade.idYouTube)"
										href="https://www.youtube.com/watch?v={{
											atividade.idYouTube
										}}"
										style="margin-right: 10px"
										target="_blank">
										<img
											class="thumb-yt"
											src="https://i.ytimg.com/vi/{{
												atividade.idYouTube
											}}/hqdefault.jpg" />
									</a>

									<a
										*ngIf="!youtube(atividade.idYouTube)"
										href="{{ atividade.idYouTube }}"
										style="margin-right: 10px"
										target="_blank">
										<img
											class="thumb-yt"
											src="https://i.ytimg.com/vi/{{
												atividade.idYouTube
											}}/hqdefault.jpg" />
									</a>

									<div
										(click)="removeAtividade(atividade)"
										style="
											display: inline-block;
											margin-right: 10px;
											margin-left: 10px;
										">
										<i class="fa fa-trash-o"></i>
									</div>
								</div>
							</div>
						</ng-container>
					</div>
				</div>
			</div>
		</div>

		<div class="actions-row">
			<pacto-cat-button
				(click)="cancelHandler()"
				[label]="'Cancelar'"
				[type]="'OUTLINE'"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvarHandler()"
				[label]="'Salvar'"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</pacto-cat-layout-v2>
