@import "~src/assets/scss/pacto/plataforma-import.scss";

// NIVEL BLOCK

.spanRemove {
	cursor: pointer;
	margin: 20px 0px 0px 0px;
	font-weight: 600;
	line-height: 20px;
	border-radius: 3px;
	display: block;
	margin-top: 10px;

	i {
		padding-right: 5px;
	}
}

.plain-card-wrapper {
	border-radius: 4px;
	background-color: $branco;
	height: 100%;
	box-shadow: 0 0 4px 0px $geloPri;
	border: solid 1px #dddee1;
}

.thumb-yt {
	width: 50px;
	margin-right: 10px;
}

.header-row {
	display: flex;
	@extend .type-h6-bold;
	padding: 30px 0px;

	> * {
		flex-basis: 25%;
	}

	color: $pretoPri;

	.pct {
		font-size: 18px;
	}

	.drag-handler {
		padding: 0px 30px;
		flex-basis: 76px;
		flex-grow: 0;
	}
}

.content-row {
	display: flex;
	align-items: center;
	cursor: pointer;
	border-top: 1px solid $canetaBic<PERSON>astel;
	color: $pretoPri;
	@extend .type-h6-bold;

	> * {
		flex-basis: 50%;
	}

	.drag-handler {
		cursor: grab;
		height: 50px;
		padding: 0px 30px;
		flex-basis: 50px;
		flex-grow: 0;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.nivel-image {
		display: flex;
		line-height: 60px;

		.nivel-nome {
			margin-left: 15px;
		}
	}

	.acoes {
		display: flex;

		.pct {
			font-size: 18px;
			cursor: pointer;
		}

		.pct-edit {
			position: relative;
			top: 7px;
		}

		.pct-toggle-left,
		.pct-toggle-right {
			top: 2px;
			position: relative;
			padding-right: 5px;
		}

		.toggle-box {
			margin-left: 15px;
			color: $verdinho05;
			@extend .type-h6;
			padding: 5px 0px;

			&.icon-inativo {
				color: $hellboyPri;
			}
		}
	}
}

.cdk-drag-preview {
	border-radius: 2px;
	box-sizing: border-box;
	opacity: 0.8;
	background-color: $branco;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
		0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
	opacity: 1;
	background-color: $cinzaClaroPri;
}

.cdk-drag-animating {
	transition: transform 150ms cubic-bezier(0, 0, 0.2, 1);
}

.atividades {
	display: flex;
	flex-direction: column;

	.atividades {
		margin-top: 15px;
	}

	.atividade {
		display: flex;
		align-items: center;
		line-height: 54px;
		padding-left: 15px;

		pacto-cat-person-avatar {
			margin-right: 15px;
		}

		&:nth-child(2n + 1) {
			background-color: $cinzaPri;
		}
	}
}

.nivel-edit-title {
	@extend .type-h2;
	color: #2c343b;
	margin-bottom: 30px;

	span {
		cursor: pointer;
	}

	.pct {
		font-size: 28px;
	}
}

.nivel-titulo-card {
	@extend .type-h5-bold;
}

.content-aux {
	display: flex;
	flex-wrap: wrap;

	.photo-column {
		width: 116px;
		margin-right: 30px;
	}

	.row {
		flex-grow: 1;
	}
}

pacto-seletor-imagem-user {
	margin-top: 47px;
}

.modelo-add-label {
	@extend .type-h6;
	color: $azulimPri;
	line-height: 32px;
	cursor: pointer;
}

.modelo-label {
	@extend .type-h6;
	color: $gelo04;
	line-height: 2em;
}

.seletor-imagem {
	margin: 15px 0px;

	.upper {
		display: flex;

		.modelo-label {
			flex-grow: 1;
		}
	}
}

.color-selector {
	margin: 15px 0px;

	.label-color {
		@extend .type-h6;
		line-height: 32px;
		color: $gelo04;
		padding-left: 3px;
	}

	.input-color {
		color: $gelo04;
		line-height: 36px;
		font-size: 36px;
		cursor: pointer;
		border: 1px solid $gelo04;
		border-radius: 20px;
		width: 40px;
		text-align: center;
		height: 40px;
	}
}

.actions-row {
	display: flex;
	justify-content: flex-end;
	margin-top: 30px;
	margin-bottom: 15px;

	pacto-cat-button {
		margin-left: 15px;
	}
}

@media (max-width: $bootstrap-breakpoint-lg) {
	.photo-column {
		flex-basis: 100%;
		display: flex;
		justify-content: center;
	}
}
