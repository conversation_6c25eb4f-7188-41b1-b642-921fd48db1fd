import {
	Component,
	OnInit,
	ViewChild,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import {
	FormControl,
	FormGroup,
	Validators,
	AbstractControl,
} from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-atividade-home-add",
	templateUrl: "./atividade-home-fit-add.component.html",
	styleUrls: ["./atividade-home-fit-add.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AtividadeHomeFitAddComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: false }) removeModalTitle;
	@ViewChild("removeModalBody", { static: false }) removeModalBody;
	@ViewChild("buttonRemove", { static: false }) buttonRemove;
	@ViewChild("removeModalMsg", { static: false }) removeModalMsg;

	loading = false;

	formGroup = new FormGroup({
		nome: new FormControl(null, [Validators.required, Validators.minLength(3)]),
		descricao: new FormControl(),
		idYouTube: new FormControl(null, [Validators.required]),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private snotify: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {}

	cancelHandler() {
		this.openModal.dismiss();
	}

	saveHandler() {
		this.markAsTouched();
		if (this.formGroup.valid) {
			const dto = this.getDto();
			this.openModal.close(dto);
		}
	}

	removeHandler(index) {
		const current: any[] = this.formGroup.get("atividades").value;
		current.splice(index, 1);
		this.formGroup.get("atividades").setValue(current, { emitEvent: false });
		this.cd.detectChanges();
	}

	private getDto() {
		const dto = this.formGroup.getRawValue();
		return dto;
	}

	private markAsTouched() {
		const options: { [key: string]: AbstractControl } = this.formGroup.controls;
		for (const control in options) {
			if (options.hasOwnProperty(control)) {
				options[control].markAsTouched();
			}
		}
	}
}
