@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding-top: 15px;
}

.actions {
	display: flex;
	justify-content: flex-end;
	margin: 30px 0px;

	pacto-cat-button {
		margin-left: 15px;
	}
}

.select-filter-wrapper {
	margin: 15px 0px;
}

.select-top {
	display: flex;

	.select-label {
		flex-grow: 1;
		@extend .type-h6;
		line-height: 32px;
		color: $gelo04;
	}

	.modelo-edit-label {
		@extend .type-h6;
		color: $azulimPri;
		line-height: 32px;
		cursor: pointer;
	}
}

.select-list {
	margin: 15px 0px;

	.pct {
		cursor: pointer;
	}
}

.select-item-view {
	display: flex;
	align-items: center;
	padding: 0px 15px 0px 0px;
	margin: 7px 0px;

	.content-nome {
		margin: 0px 15px;
		flex-grow: 1;
	}
}
