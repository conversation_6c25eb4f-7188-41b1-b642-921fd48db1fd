<pacto-cat-layout-v2>
	<div class="header-row">
		<div class="page-title">
			<i
				[routerLink]="['/treino', 'home-fit', 'lista-treinos']"
				class="pct pct-arrow-left"></i>
			Detalhes do Treino
		</div>
	</div>

	<div class="body">
		<pacto-cat-card-plain class="info">
			<!-- Row 1 -->
			<div class="first-row">
				<div class="avaliador">
					<div class="label">Professor</div>
					<div class="value">
						<pacto-cat-person-avatar
							[diameter]="32"
							[uri]="treino.fotoProfessor"></pacto-cat-person-avatar>
						<div class="block-value">{{ treino.nomeProfessor }}</div>
					</div>
				</div>

				<pacto-cat-button
					(click)="alterar()"
					[icon]="'edit-2'"
					[label]="'ALTERAR'"></pacto-cat-button>
			</div>
			<div class="second-row">
				<div class="block">
					<div class="label">Nome</div>
					<div class="block-value">{{ treino.nomeDoTreino }}</div>
				</div>
			</div>

			<div class="third-row">
				<div *ngIf="treino.media && treino.media[0].url" class="block">
					<img src="{{ treino.media[0].url }}" style="width: 70px" />
				</div>
				<div class="block">
					<div class="label descricao">{{ treino.descricao }}</div>
				</div>
			</div>

			<div *ngIf="permitido" class="actions" id="btn">
				<button
					(click)="replicar()"
					class="btn btn-outline-secondary"
					i18n="@@buttons:salvar"
					id="btnSalvar"
					title="Replica este treino em toda a rede de empresas. Essa ação deve ser executada a cada alteração nesse treino.">
					Replicar na rede
				</button>
			</div>
		</pacto-cat-card-plain>

		<pacto-cat-card-plain class="alunos">
			<div *ngIf="treino.tagredes">
				<div class="label">Acompanhe nas redes sociais</div>
				<div class="atividades">
					<table style="width: 100%; border-bottom: #d3d5d7 1px solid">
						<tr>
							<td style="width: 50%">
								<div class="value">#{{ treino.tagredes.replace("#", "") }}</div>
							</td>
							<td style="width: 50%; text-align: right">
								<a
									href="https://www.instagram.com/explore/tags/{{
										treino.tagredes.replace('#', '')
									}}/"
									style="margin-right: 10px; font-size: 28px; color: #51555a"
									target="_blank"
									title="Clique para ver as postagens no Instagram">
									<i class="fa fa-instagram"></i>
								</a>
								<a
									href="https://twitter.com/search?q=%23{{
										treino.tagredes.replace('#', '')
									}}"
									style="margin-right: 10px; font-size: 28px; color: #51555a"
									target="_blank"
									title="Clique para ver as postagens no Twitter">
									<i class="fa fa-twitter-square"></i>
								</a>
								<a
									href="https://web.facebook.com/hashtag/{{
										treino.tagredes.replace('#', '')
									}}/"
									style="margin-right: 10px; font-size: 28px; color: #51555a"
									target="_blank"
									title="Clique para ver as postagens no Facebook">
									<i class="fa fa-facebook"></i>
								</a>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<div *ngIf="!alunos.length" class="empty">
				<i class="pct pct-users"></i>
				<p class="description">Nenhum aluno participou até o momento.</p>
			</div>

			<div *ngIf="alunos.length" class="list">
				<div class="upper-row">
					<div class="type-h6 cor-cinza04">
						Alunos que participaram até agora
					</div>
				</div>
				<div [maxHeight]="'587px'" class="scroll-wrapper" pactoCatSmoothScroll>
					<div *ngFor="let aluno of alunos" class="aluno">
						<pacto-cat-person-avatar
							[diameter]="32"
							[uri]="aluno.urlFoto"></pacto-cat-person-avatar>
						<div class="nome type-h6 cor-preto04">{{ aluno.nome }}</div>
					</div>
				</div>
			</div>
		</pacto-cat-card-plain>

		<pacto-cat-card-plain class="nivel">
			<div class="label">Atividades</div>
			<div [maxHeight]="'1000px'" class="atividades" pactoCatSmoothScroll>
				<table style="width: 100%">
					<tr *ngFor="let atividade of treino.atividades" class="atividade">
						<td style="width: 50%">
							<div class="value" title="{{ atividade.descricao }}">
								{{ atividade.nome }}
							</div>
						</td>
						<td style="width: 50%; text-align: right">
							<a
								*ngIf="youtube(atividade.idYouTube)"
								href="https://www.youtube.com/watch?v={{ atividade.idYouTube }}"
								style="margin-right: 10px"
								target="_blank">
								<img
									class="thumb-yt"
									src="https://i.ytimg.com/vi/{{
										atividade.idYouTube
									}}/hqdefault.jpg" />
							</a>

							<a
								*ngIf="!youtube(atividade.idYouTube)"
								href="{{ atividade.idYouTube }}"
								style="margin-right: 10px"
								target="_blank">
								<img
									class="thumb-yt"
									src="https://i.ytimg.com/vi/{{
										atividade.idYouTube
									}}/hqdefault.jpg" />
							</a>
						</td>
					</tr>
				</table>
			</div>
		</pacto-cat-card-plain>
	</div>
</pacto-cat-layout-v2>
