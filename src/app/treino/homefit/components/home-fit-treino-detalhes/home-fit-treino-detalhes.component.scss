@import "src/assets/scss/pacto/plataforma-import.scss";

.header-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30px;
	align-items: center;
}

.page-title {
	@extend .type-h2;
	color: $pretoPri;
	margin: 20px 0px;

	.pct {
		font-size: 28px;
		color: $pretoPri;
		padding-right: 15px;
		cursor: pointer;
	}
}

.body {
	display: grid;
	grid-template-columns: 5fr 7fr;
	grid-template-rows: 260px 390px;
	gap: 30px;
}

.alunos {
	grid-row: 1 / 3;
	grid-column: 2;
}

.footer {
	display: flex;
	flex-direction: row-reverse;
	margin: 30px 0px;
}

@media (max-width: $plataforma-breakpoint-small) {
	.body {
		grid-template-columns: 1fr;
		grid-template-rows: 260px 390px 680px;
	}

	.info {
		grid-row: 1 / 1;
		grid-column: 1;
	}

	.alunos {
		grid-row: 3 / 3;
		grid-column: 1;
	}

	.nivel {
		grid-row: 2 / 2;
		grid-column: 1;
	}
}

.label.descricao {
	line-height: 14px;
	margin: 0;
	padding: 0;
	font-size: 13px;
}

.label {
	@extend .type-h6;
	margin-bottom: 6px;
	color: $cinza04;
}

.thumb-yt {
	width: 55px;
}

.block-value {
	@extend .type-h5-bold;
	color: $pretoPri;
}

.table .fa {
	margin-left: 10px;
}

// INFO BLOCK
.info {
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.first-row {
		display: flex;
		align-items: flex-start;

		.avaliador {
			flex-grow: 1;
		}

		.avaliador .value {
			display: flex;
			align-items: center;
		}

		pacto-cat-person-avatar {
			margin-right: 15px;
		}
	}

	.second-row,
	.third-row {
		display: flex;
	}

	.block {
		flex-basis: 50%;
		flex-grow: 1;
	}
}

// NIVEL BLOCK
.nivel {
	display: flex;
	flex-direction: column;

	.nivel-block {
		display: flex;
		align-items: center;
		margin: 15px 0px;

		.block-value {
			margin-left: 15px;
		}
	}

	.atividades {
		margin-top: 15px;
	}

	.atividade {
		display: flex;
		align-items: center;
		line-height: 54px;
		padding-left: 15px;

		pacto-cat-person-avatar {
			margin-right: 15px;
		}

		&:nth-child(2n + 1) {
			background-color: $cinzaPri;
		}
	}
}

// ALUNOS BLOCK
.alunos {
	.empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
	}

	.description {
		@extend .type-h5;
		text-align: center;
		width: 300px;
		margin: 15px 0px;
		color: $cinza02;
	}

	.pct {
		font-size: 48px;
		color: $cinza02;
	}

	.list .upper-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 7.5px;
	}

	.list .aluno {
		display: flex;
		align-items: center;
		line-height: 64px;

		&:nth-child(2n + 1) {
			background-color: $cinzaPri;
		}

		pacto-cat-person-avatar {
			margin: 0px 7.5px;
		}

		.nome {
			flex-grow: 1;
		}

		.pct {
			font-size: 18px;
			color: $hellboyPri;
			cursor: pointer;
			padding: 0px 25px 0px 15px;
		}
	}
}

.actions {
	margin-top: 20px;
	text-align: right;
}
