import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";

import { Observable, zip, of } from "rxjs";
import { tap, map, switchMap } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";
import { HomeFitService } from "@base-core/home-fit/home-fit.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-home-fit-treino-detalhes",
	templateUrl: "./home-fit-treino-detalhes.component.html",
	styleUrls: ["./home-fit-treino-detalhes.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeFitTreinoDetalhesComponent implements OnInit {
	treino: any;
	alunos: any;

	constructor(
		private homeFitService: HomeFitService,
		private route: ActivatedRoute,
		private session: SessionService,
		private snotify: SnotifyService,
		private router: Router,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.loadData();
	}

	alterar() {
		this.router.navigate([
			"treino",
			"home-fit",
			"save-treino",
			this.homeFitService.selected.documentKey,
		]);
	}

	private loadData(): void {
		this.alunos = [];
		const id = this.route.snapshot.params.id;
		this.treino = this.homeFitService.selected;
		if (this.treino) {
			this.homeFitService.alunostreino(id).subscribe((data) => {
				this.alunos = data.sucesso;
				this.cd.detectChanges();
			});
		} else {
			this.router.navigate(["/treino", "home-fit", "lista-treinos"]);
		}
	}

	get permitido(): boolean {
		let chave = this.session.tokenChave;
		if (!chave) {
			chave = this.session.chave;
		}
		return (
			chave === "aa5abb60d28e4583f5438c7d4a1cb376" ||
			chave === "28e67e434e532d0057a64b7471551cf9" ||
			chave === "d57083709f0fecd013ed05e81654cf9a" ||
			chave === "aca438e8c9e947e64db2236bb2f1f7a9"
		);
	}

	replicar() {
		let chave = this.session.tokenChave;
		if (!chave) {
			chave = this.session.chave;
		}
		this.homeFitService
			.replicar(chave, this.homeFitService.selected.documentKey)
			.subscribe((data) => {
				this.snotify.success(
					"Processo iniciado, seu treino será replicado na rede dentro de minutos."
				);
				this.cd.detectChanges();
			});
	}

	youtube(url): boolean {
		try {
			const YoutubeRegexObject_v1 =
				/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i;
			const YoutubeVideoID = url.match(YoutubeRegexObject_v1);
			return YoutubeVideoID[1];
		} catch (e) {
			return false;
		}
	}
}
