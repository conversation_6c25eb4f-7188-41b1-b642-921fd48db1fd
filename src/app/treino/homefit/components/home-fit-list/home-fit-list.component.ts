import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";

import { SnotifyService } from "ng-snotify";

import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { HomeFitService } from "@base-core/home-fit/home-fit.service";
import { ModalService } from "@base-core/modal/modal.service";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-atividade-list",
	templateUrl: "./home-fit-list.component.html",
	styleUrls: ["./home-fit-list.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeFitListComponent implements OnInit {
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: false }) tooltipRemover;

	nomeTreino = "";

	constructor(
		private snotifyService: SnotifyService,
		private homeFitService: HomeFitService,
		private sessionService: SessionService,
		private snotify: SnotifyService,
		private router: Router,
		private cd: ChangeDetectorRef,
		private modalService: ModalService
	) {}

	table: PactoDataGridConfig;
	itemToRemove: any;

	ngOnInit() {
		if (this.homeFitService.infoapp) {
			this.configTable();
		} else {
			this.homeFitService
				.dadosApp(this.sessionService.chave)
				.subscribe((data) => {
					if (data.erro) {
						this.snotifyService.error(
							"Não foi possível encontrar a chave da academia na base do app."
						);
					} else {
						this.homeFitService.infoapp = data.sucesso[0];
						this.configTable();
						this.cd.detectChanges();
					}
				});
		}
	}

	iconClickHandler($event: any) {
		if ($event.iconName === "editar") {
			this.homeFitService.selected = $event.row;
			this.router.navigate([
				"treino",
				"home-fit",
				"save-treino",
				$event.row.documentKey,
			]);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	btnClickHandler() {
		this.homeFitService
			.dadosApp(this.sessionService.chave)
			.subscribe((data) => {
				if (data.erro) {
					this.snotifyService.error(
						"Não foi possível encontrar a chave da academia na base do app."
					);
				} else {
					this.homeFitService.infoapp = data.sucesso[0];
					this.router.navigate([
						"treino",
						"home-fit",
						"save-treino",
						"adicionar",
					]);
				}
			});
	}

	rowClickHandler(item) {
		this.homeFitService.selected = item;
		this.router.navigate([
			"treino",
			"home-fit",
			"treino-detalhes",
			item.documentKey,
		]);
	}

	getConfOk(): boolean {
		return this.homeFitService.infoapp ? true : false;
	}

	private configTable() {
		const base =
			"https://us-central1-app-do-aluno-unificado.cloudfunctions.net";
		this.table = new PactoDataGridConfig({
			endpointUrl:
				base +
				"/treinoSimples/consultarTreinosSimples?refUsuario&clienteApp=" +
				this.homeFitService.infoapp.documentKey,
			quickSearch: false,
			rowClick: true,
			buttons: {
				id: "novo-treino",
				conteudo: this.buttonName,
				nome: "add",
			},
			dataAdapterFn: (data) => {
				const conteudo = new Array<any>();

				if (data.sucesso) {
					data.sucesso.forEach((t: any) => {
						conteudo.push(t);
					});
				}

				return {
					size: conteudo.length,
					content: conteudo,
					totalElements: conteudo.length,
					number: conteudo.length,
				};
			},
			columns: [
				{
					nome: "nomeDoTreino",
					titulo: "Treino",
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "nomeProfessor",
					titulo: "Professor",
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: "Remover o treino",
				},
			],
		});
	}

	removeHandler(item) {
		this.nomeTreino = item.nomeDoTreino;
		setTimeout(() => {
			const modalTitle = "Remover treino";
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					this.homeFitService
						.remove(item.documentKey)
						.subscribe((resultRemove) => {
							if (resultRemove.sucesso) {
								this.snotifyService.success(removeSuccess);
								this.tableData.reloadData();
							} else {
								this.snotifyService.error(removeError);
							}
						});
				})
				.catch(() => {});
		});
	}
}
