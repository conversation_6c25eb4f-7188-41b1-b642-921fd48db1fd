<pacto-cat-layout-v2>
	<div class="page-title">Treino em casa</div>

	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler($event)"
			(iconClick)="iconClickHandler($event)"
			(rowClick)="rowClickHandler($event)"
			*ngIf="getConfOk()"
			[sessionService]="sessionService"
			[table]="table"
			telaId="treinoEmCasa"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #buttonName>Novo treino</ng-template>

<span #removeModalTitle [hidden]="true">Remover Treino ?</span>
<span #removeModalBody [hidden]="true">
	Deseja remover o treino {{ nomeTreino }}?
</span>
<span #removeSuccess [hidden]="true">Treino removido com sucesso.</span>
<span #removeError [hidden]="true">
	Não foi possível excluir, tente novamente em instantes.
</span>
