@import "src/assets/scss/pacto/plataforma-import.scss";

.main-div {
	padding: 28px 30px 45px;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.input-nome {
	font-weight: 400;
	font-size: 16px;
	margin-top: 22px;
	color: #a6aab1;
}

.no-margin {
	margin: -16px;
}

.align-right {
	text-align: right;
}

.actions {
	display: flex;
	justify-content: space-between;
	margin-top: 20px;

	pacto-cat-button {
		width: 200px;
	}
}

::ng-deep.no-margin {
	& pacto-cat-form-input {
		display: block;
		margin: 0px 0px !important;
	}

	& pacto-cat-form-select {
		display: block;
		margin: 0px 0px !important;
	}

	& pacto-cat-form-select-filter {
		display: block;
		margin: 0px 0px !important;
	}
}

::ng-deep.pct-error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	line-height: 2em;
	min-height: 24px;
}

.error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	line-height: 2em;
	min-height: 24px;
}

.pacto-label {
	color: #a6aab1;
	line-height: 2em;
	min-height: 32px;
	padding-left: 3px;
}

.display-flex {
	display: flex;
}

.espacamento {
	margin-right: 16px;
}
