<div class="main-div">
	<div class="row no-margin">
		<div class="col-md-12">
			<pacto-cat-form-input
				[control]="fc.get('nomePrograma')"
				[errorMsg]="'Informe o nome do programa'"
				[id]="'input-nome-programa'"
				[label]="'Nome do Programa'"
				[placeholder]="'Dê um nome ao seu programa'"></pacto-cat-form-input>
		</div>

		<div class="col-md-12">
			<pacto-cat-form-select-filter
				[control]="fc.get('professor')"
				[endpointUrl]="_rest.buildFullUrl('colaboradores')"
				[errorMsg]="'Defina um professor'"
				[idKey]="'id'"
				[id]="'select-professor-montou'"
				[labelKey]="'nome'"
				[label]="'Quem criou'"
				[paramBuilder]="professorSelectBuilder"
				[placeholder]="'Selecione professor'"
				[resposeParser]="responseParser"></pacto-cat-form-select-filter>
		</div>
	</div>

	<div class="row">
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="fc.get('diasPorSemana')"
				[errorMsg]="'Informe quantas vezes na semana o programa será executado'"
				[id]="'select-quantidade-dias-semana'"
				[label]="'Dias por Semana'"
				[textMask]="{
					guide: false,
					mask: numberMaskDiasSemana
				}"></pacto-cat-form-input>
		</div>

		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="fc.get('totalAulasPrevistas')"
				[errorMsg]="'Numero de aulas previstas obrigatório'"
				[id]="'input-aulas-previstas'"
				[label]="'Aulas Previstas'"
				[textMask]="{ guide: false, mask: numberMask }"></pacto-cat-form-input>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<span class="pacto-label">Gênero</span>
		</div>
	</div>

	<div class="row">
		<div class="col-md-6 display-flex">
			<div class="espacamento">
				<pacto-cat-checkbox
					[control]="fc.get('feminino')"
					[id]="'ck-feminino'"
					[label]="'Feminino'"></pacto-cat-checkbox>
			</div>

			<div>
				<pacto-cat-checkbox
					[control]="fc.get('masculino')"
					[id]="'ck-masculino'"
					[label]="'Masculino'"></pacto-cat-checkbox>
			</div>
		</div>
	</div>

	<div class="error-msg">
		<span *ngIf="validarGenero">Informe o gênero do programa</span>
	</div>

	<div class="actions">
		<pacto-cat-button
			(click)="cancelarHandler()"
			[full]="true"
			[label]="'Cancelar'"
			[type]="'OUTLINE'"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvarHandler()"
			[disabled]="!true"
			[full]="true"
			[id]="'bt-salvar'"
			[label]="'Salvar'"></pacto-cat-button>
	</div>
</div>
<div #conflito [hidden]="true" id="help-message-duplicated-entity-name">
	Já existe um cadastro com esse mesmo nome
</div>
