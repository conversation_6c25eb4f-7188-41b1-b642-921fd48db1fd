import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { Subscription } from "rxjs";

import { RestService } from "@base-core/rest/rest.service";
import {
	Programa,
	ProgramaCriar,
	ProgramaPredefinido,
	TreinoApiProgramaService,
} from "treino-api";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TituloModalEdicaoFichaComponent } from "../../montagem-treino/titulo-modal-edicao-ficha/titulo-modal-edicao-ficha.component";
import { ConfigurarFichaComponent } from "../../montagem-treino/configurar-ficha/configurar-ficha.component";
import { ConfigurarFichaService } from "../../montagem-treino/configurar-ficha.service";
import { ModalService } from "@base-core/modal/modal.service";
import { AlunoBase } from "treino-api";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import {
	CatFormSelectComponent,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-programa-predefinido-edit",
	templateUrl: "./programa-predefinido-edit.component.html",
	styleUrls: ["./programa-predefinido-edit.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProgramaPredefinidoEditComponent implements OnInit, OnDestroy {
	@ViewChild("diasSemana", { static: true }) diasSemana: CatFormSelectComponent;
	@ViewChild("notificacoesTranslates", { static: true })
	notificacoesTranslates: TraducoesXinglingComponent;
	@ViewChild("genero", { static: true }) genero: CatFormSelectComponent;

	@Input() programa: ProgramaPredefinido;
	@Input() aluno: AlunoBase;
	@Output() update: EventEmitter<any> = new EventEmitter();

	fc = new FormGroup({
		id: new FormControl(null),
		categoria: new FormControl(null),
		codigoProfessor: new FormControl(null),
		nomePrograma: new FormControl(null, Validators.required),
		diasPorSemana: new FormControl(null, Validators.required),
		totalAulasPrevistas: new FormControl(null, [
			Validators.required,
			Validators.min(1),
		]),
		professor: new FormControl(null, Validators.required),
		genero: new FormControl(null),
		feminino: new FormControl(false),
		masculino: new FormControl(false),
	});

	dias: Array<any> = [
		{ id: 1, label: "1x por semana" },
		{ id: 2, label: "2x por semana" },
		{ id: 3, label: "3x por semana" },
		{ id: 4, label: "4x por semana" },
		{ id: 5, label: "5x por semana" },
		{ id: 6, label: "6x por semana" },
		{ id: 7, label: "7x por semana" },
	];

	permissaoProgramaTreino;
	validateSubscription: Subscription;
	numberMask = [/[0-9]/, /[0-9]/, /[0-9]/];
	numberMaskDiasSemana = [/[1-7]/];
	validarGenero = false;

	constructor(
		private modal: NgbActiveModal,
		private rest: RestService,
		private programaService: TreinoApiProgramaService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private configurarFichaService: ConfigurarFichaService,
		private appModal: ModalService,
		private router: Router
	) {}

	ngOnInit() {
		this.carregarPermissoes();
		if (!this.permissaoProgramaTreino.editar) {
		}
		if (this.programa) {
			this.fillOutForm();
		}
	}

	private fillOutForm() {
		let f = false;
		let m = false;
		switch (this.programa.genero) {
			case "Feminino":
				f = true;
				break;
			case "Masculino":
				m = true;
				break;
			case "Feminino, Masculino":
				f = true;
				m = true;
				break;
		}

		this.fc.patchValue(
			{
				id: this.programa.id,
				nomePrograma: this.programa.nomePrograma,
				professor: this.programa.professor,
				diasPorSemana: this.programa.diasPorSemana,
				totalAulasPrevistas: this.programa.totalAulasPrevistas,
				feminino: f,
				masculino: m,
			},
			{ emitEvent: false }
		);
	}

	ngOnDestroy() {
		if (this.validateSubscription) {
			this.validateSubscription.unsubscribe();
		}
	}

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	professorSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
	}

	salvarHandler() {
		const controls = this.fc.controls;
		for (const control in controls) {
			if (Object.prototype.hasOwnProperty.call(controls, control)) {
				controls[control].markAsTouched();
			}
		}
		this.processarGenero();
		if (this.fc.valid && !this.validarGenero) {
			const programa: ProgramaCriar = {
				nome: this.fc.value.nomePrograma,
				totalTreinos: this.fc.value.totalAulasPrevistas,
				qtdDiasSemana: this.fc.value.diasPorSemana,
				genero: this.fc.value.genero,
				professorId: this.fc.value.professor.id,
			};
			this.programaService.criarProgramaPredefindo(programa).subscribe(
				(result) => {
					this.modal.close({ programa: result, novo: true });
					// this.abrirModalEdicaoFicha(result);
					this.router.navigate(["treino", "cadastros", "programa", result.id]);

					this.snotifyService.success(
						"Programa predefinido criado com sucesso."
					);
					this.sessionService.notificarRecursoEmpresa(
						RecursoSistema.CRIOU_NOVO_PROGRAMA_PREDEFINIDO
					);
				},
				(httpResponseError) => {
					const error = httpResponseError.error.meta.message;
					this.snotifyService.error(error);
				}
			);
		} else {
			this.snotifyService.warning("Campos obrigatórios não preenchidos.");
		}
	}

	private abrirModalEdicaoFicha(programa: Programa) {
		if (this.permissaoProgramaTreino.consultar) {
			this.configurarFichaService.programa$.next(programa);
			this.configurarFichaService.selecionarFichaInicial(0);
			const modalConfigFicha = this.appModal.openFullscreen(
				TituloModalEdicaoFichaComponent,
				ConfigurarFichaComponent
			);
			modalConfigFicha.componentInstance.programa = programa;
			modalConfigFicha.componentInstanceHeader.programa = programa;
			modalConfigFicha.componentInstanceHeader.aluno = this.aluno;
			modalConfigFicha.componentInstanceHeader.abrirModalEditorFicha.subscribe(
				() => {
					modalConfigFicha.componentInstance.abrirModalEditorFicha();
				}
			);
			modalConfigFicha.componentInstanceHeader.novaFicha.subscribe(() => {
				modalConfigFicha.componentInstance.abrirModalAdicionarFicha();
			});
			modalConfigFicha.componentInstanceHeader.excluirFicha.subscribe(() => {
				modalConfigFicha.componentInstance.excluirFichaHandler();
			});
			modalConfigFicha.componentInstanceHeader.tornarFichaPredefinida.subscribe(
				() => {
					modalConfigFicha.componentInstance.tornarFichaPredefinidaHandler();
				}
			);
			modalConfigFicha.result.then(
				(result) => {},
				(programaEditado: Programa) => {
					this.update.emit(true);
				}
			);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu adiministrador"
			);
		}
	}

	processarGenero() {
		if (this.fc.value.feminino && !this.fc.value.masculino) {
			this.fc.value.genero = "F";
			this.validarGenero = false;
		} else if (!this.fc.value.feminino && this.fc.value.masculino) {
			this.fc.value.genero = "M";
			this.validarGenero = false;
		} else if (this.fc.value.feminino && this.fc.value.masculino) {
			this.fc.value.genero = "FM";
			this.validarGenero = false;
		} else {
			this.validarGenero = true;
		}
	}

	cancelarHandler() {
		this.modal.dismiss();
	}
}
