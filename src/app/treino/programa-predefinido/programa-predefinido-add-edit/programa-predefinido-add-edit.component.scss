@import "src/assets/scss/pacto/plataforma-import.scss";

/* Variáveis comuns */
$sticky-top: 0;
$z-index-sticky: 1;
$max-width-full: 100%;
$padding-standard: 5px 0px;
$background-color-white: white;
$border-color: #cccccc;

/* Estilos para pacto-configurar-ficha */
pacto-configurar-ficha {
	::ng-deep {
		.center-aux,
		.container,
		.row-form,
		.title-card-adicionar-atividade,
		.listagem-atividades,
		.colunas-atividade {
			max-width: $max-width-full;
			width: $max-width-full;
		}

		.container {
			margin: 0;
		}

		.title-card-adicionar-atividade {
			display: flex;
			justify-content: space-between;
			padding: $padding-standard;
		}

		.card-info-tabs-ficha {
			position: sticky;
			top: $sticky-top;
			z-index: $z-index-sticky;
			background-color: $background-color-white;
		}

		.listagem-atividades {
			margin-bottom: 40px !important;
		}

		.colunas-atividade {
			justify-content: space-between;
		}

		.view-header {
			border-bottom: 2px solid $border-color;
		}

		.footer {
			left: auto;
		}

		.ficha-tabs {
			display: flex;
			width: 100%;
			flex-wrap: wrap;

			.ficha {
				@extend .type-h6;
				height: 32px;
				cursor: pointer;
				padding: 5px 10px 5px;
				margin-right: 4px;
				border: 1px solid #ccc;
				background-color: #fbfbfc;
				color: #9298a0;
				border-width: 1px 1px 1px 1px;
				border-top-left-radius: 4px;
				border-top-right-radius: 4px;
				text-align: center;
				width: 144px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;

				&.selected {
					color: #51555a;
					background-color: $branco;
					border-bottom: 2px solid #1998fc;
					border-width: 2px 2px 2px 2px;
					font-family: Nunito Sans;
					font-size: 16px;
					font-weight: 600;
				}
			}
		}

		.ficha-nova {
			width: 32px;
			height: 32px;
			display: flex;
			justify-content: center;
			align-items: center;
			@extend .type-h6;
			cursor: pointer;
			border: 2px solid $azulimPri;
			background-color: $azulimPri;
			border-width: 2px 2px 0px 2px;
			border-top-left-radius: 3px;
			border-top-right-radius: 3px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: clip;
			color: white;
			font-size: 14px;
		}

		.card-info-tabs-ficha {
			display: flex;
			padding-top: 7px;
			background-color: white;
			width: 100%;
			border-bottom: 1px solid $cinza02;
		}
	}

	padding-top: 5px;
}

/* Estilos para pacto-titulo-modal-edicao-ficha */
pacto-titulo-modal-edicao-ficha {
	::ng-deep {
		.center-aux,
		.card-info-programa,
		.view-header {
			max-width: $max-width-full;
			width: $max-width-full;
		}

		.card-info-programa {
			justify-content: space-between;
			padding-right: 2rem;
			align-items: center;
		}

		.view-header,
		.content-aviso-medico {
			padding-top: 0;
			min-height: 0;
			/* Ajustado conforme necessário */
		}

		.info-programa,
		.header,
		.titulo-button {
			margin: 7px 0px;
			flex-direction: row;
			flex-wrap: wrap;
			align-items: center;
			padding-top: 0;
		}
	}
}
