import { Component, OnInit } from "@angular/core";
import { ConfigurarFichaService } from "../../montagem-treino/configurar-ficha.service";
import { HttpClient, HttpParams } from "@angular/common/http";
import { SessionService } from "@base-core/client/session.service";
import {
	PerfilAcessoRecursoNome,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { RestService } from "@base-core/rest/rest.service";
import { ActivatedRoute } from "@angular/router";

@Component({
	selector: "pacto-programa-predefinido-add-edit",
	templateUrl: "./programa-predefinido-add-edit.component.html",
	styleUrls: ["./programa-predefinido-add-edit.component.scss"],
})
export class ProgramaPredefinidoAddEditComponent implements OnInit {
	programa: any;
	permissaoProgramaTreino;
	permissoesProgramasPredefinidos;
	permissaotornarpredefinido;
	id: number;

	constructor(
		private http: HttpClient,
		public configurarFichaService: ConfigurarFichaService,
		private sessionService: SessionService,
		private rest: RestService,
		private route: ActivatedRoute
	) {
		this.carregarDados();
	}

	ngOnInit() {
		this.loadAllow();
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.ACESSOU_TELA_PROGRAMA_PREDEFINIDO
		);
	}

	loadAllow() {
		this.permissoesProgramasPredefinidos = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMAS_PREDEFINIDOS
		);
	}

	getPredefinedPrograms(id: number) {
		const fullUrl = this.rest.buildFullUrl("programas/") + id;
		return this.http.get(fullUrl);
	}

	private carregarPermissoes() {
		this.permissaotornarpredefinido = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.TORNAR_FICHA_PREDEFINIDA
		);
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
	}

	private carregarDados() {
		this.carregarPermissoes();
		this.route.params.subscribe((params) => {
			this.id = params.id;
			this.getPredefinedPrograms(this.id).subscribe(
				(data: any) => {
					this.programa = data.content;
					if (this.programa) {
						this.configurarFichaService.selecaoFichaIniciada = false;
						this.configurarFichaService.programa$.next(this.programa);
						this.configurarFichaService.selecionarFichaInicial(0);
					} else {
						console.error("Programa não encontrado");
					}
				},
				(error) => {
					console.error(error);
				}
			);
		});
	}
}
