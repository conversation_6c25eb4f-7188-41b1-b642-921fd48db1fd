import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";
import { RouterModule, Routes } from "@angular/router";
import { ProgramaPredefinidoListaComponent } from "./programa-predefinido-lista/programa-predefinido-lista.component";
import { CommonModule } from "@angular/common";
import { MontagemTreinoModule } from "../montagem-treino/montagem-treino.module";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { NgModule } from "@angular/core";
import { ProgramaPredefinidoEditComponent } from "./programa-predefinido-edit/programa-predefinido-edit.component";
import { ProgramaPredefinidoDeleteComponent } from "./programa-predefinido-delete/programa-predefinido-delete.component";
import { ProgramasPredefinidosGuard } from "@base-core/guards/programas-predefinidos.guard";
import { ProgramaPredefinidoAddEditComponent } from "./programa-predefinido-add-edit/programa-predefinido-add-edit.component";

const recurso = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.PROGRAMAS_PREDEFINIDOS,
	[
		PerfilRecursoPermissoTipo.CONSULTAR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const routes: Routes = [
	{
		path: "list",
		component: ProgramaPredefinidoListaComponent,
		canActivate: [ProgramasPredefinidosGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: ProgramaPredefinidoAddEditComponent,
		canActivate: [ProgramasPredefinidosGuard],
		data: {
			recurso,
		},
	},
	{
		path: "aluno/:id",
		component: ProgramaPredefinidoAddEditComponent,
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		MontagemTreinoModule,
	],
	declarations: [
		ProgramaPredefinidoListaComponent,
		ProgramaPredefinidoEditComponent,
		ProgramaPredefinidoDeleteComponent,
		ProgramaPredefinidoAddEditComponent,
	],
	entryComponents: [
		ProgramaPredefinidoEditComponent,
		ProgramaPredefinidoDeleteComponent,
	],
})
export class ProgramaPredefindoModule {}
