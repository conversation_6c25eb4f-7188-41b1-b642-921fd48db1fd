import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiProgramaService } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

@Component({
	selector: "pacto-programa-predefinido-delete",
	templateUrl: "./programa-predefinido-delete.component.html",
	styleUrls: ["./programa-predefinido-delete.component.scss"],
})
export class ProgramaPredefinidoDeleteComponent implements OnInit {
	@Input() programaId: number;
	@Input() programaNome: string;

	constructor(
		private modal: NgbActiveModal,
		private programaService: TreinoApiProgramaService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService
	) {}

	ngOnInit() {}

	excluirHandler() {
		this.programaService
			.removerPrograma(this.programaId)
			.subscribe((result) => {
				if (result === "registro_esta_sendo_usado") {
					this.snotifyService.error("Não foi possível excluir este programa!");
				} else {
					this.snotifyService.success("Programa removido com sucesso!");
					this.modal.close();
					this.sessionService.notificarRecursoEmpresa(
						RecursoSistema.EXCLUIU_PROGRAMA_PREDEFINIDO
					);
				}
			});
	}

	cancelarHandler() {
		this.modal.dismiss();
	}
}
