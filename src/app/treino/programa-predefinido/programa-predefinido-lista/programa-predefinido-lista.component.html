<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'Programas Predefinidos'
		}"
		class="first"></pacto-breadcrumbs>
	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<div>
			<div class="container-programa">
				<div class="filtros">
					<div class="search">
						<div>
							<input
								#quickSearch
								[formControl]="searchControl"
								class="form-control"
								id="input-busca-rapida"
								placeholder="Busca rápida..."
								type="text" />
							<i class="pct pct-search"></i>
						</div>
					</div>
					<div class="filtro1"></div>
					<div></div>
					<div class="compartilhar">
						<pacto-log [url]="urlLog" class="primary"></pacto-log>
						<pacto-share-button
							[columns]="obterColunasRelatorio(true)"
							[endpoint]="
								admRestService.buildFullUrlTreino('programas/pre-definido')
							"
							[filtros]="getFiltersShare()"
							id="btn-shared"></pacto-share-button>
						<div
							#filterDropdown="ngbDropdown"
							class="filter filtros-atividade d-inline-block"
							ngbDropdown>
							<pacto-filter
								#filtroButton
								(filterChange)="filterHandler($event)"
								[filterConfig]="filterConfig"></pacto-filter>
						</div>
						<button (click)="btnClickHandler()" class="btn btn-primary">
							<i class="pct pct-plus"></i>
							Adicionar
						</button>
					</div>
				</div>

				<div
					*ngFor="let item of listaProgramas; let index = index"
					class="pessoa-prescricao">
					<div class="grid-programa">
						<div class="dados-pessoais">
							<div class="column-foto">
								<div
									[ngStyle]="{
										'background-image': getImagem(
											item.professorMontou?.imageUri === undefined
												? ''
												: item.professorMontou?.imageUri
										)
									}"
									class="foto-prescricao"></div>
							</div>

							<div class="column-dados">
								<span class="programa">{{ item.nome }}</span>
								<span class="nome">
									Criado por: {{ item.professorMontou?.nome }}
								</span>
								<span class="matricula">{{ item.id }}</span>
							</div>
						</div>

						<div class="coluna-prescricao">
							<div class="column-dados">
								<span class="titulo">Gênero</span>
								<ng-container [ngSwitch]="item.genero">
									<span *ngSwitchCase="'F'">Feminino</span>
									<span *ngSwitchCase="'M'">Masculino</span>
									<span *ngSwitchCase="'FM'">Feminino/Masculino</span>
								</ng-container>
							</div>
						</div>

						<div class="coluna-prescricao">
							<div class="column-dados">
								<span class="titulo">Status</span>
								<span>
									<div
										class="situacaoAtividade {{
											item.situacao == 'ATIVO' ? 'ATIVA' : 'INATIVA'
										}}">
										{{
											notificacoesTranslate.getLabel(
												item.situacao == "ATIVO" ? "ATIVA" : "INATIVA"
											)
										}}
									</div>
								</span>
							</div>
						</div>

						<div class="coluna-botao">
							<button
								(click)="removerProgramaHandler(item)"
								class="btn btn-excluir">
								<i class="pct pct-trash-2"></i>
								Excluir
							</button>
							<button
								(click)="habilitaDesabilita(item, 'ATIVO')"
								*ngIf="item.situacao != 'ATIVO'"
								class="btn btn-secundary">
								<i class="pct pct-check"></i>
								Ativar
							</button>
							<button
								(click)="habilitaDesabilita(item, 'INATIVO')"
								*ngIf="item.situacao == 'ATIVO'"
								class="btn btn-secundary">
								<i class="pct pct-eye-off"></i>
								Inativar
							</button>
							<button (click)="btnEditHandler(item)" class="btn btn-primary">
								<i class="pct pct-edit"></i>
								Editar
							</button>
						</div>
					</div>
				</div>
			</div>

			<div class="footer-row">
				<ng-container>
					<div class="div-pagination">
						<ngb-pagination
							(pageChange)="pageChangeHandler($event)"
							[(page)]="ngbPage"
							[boundaryLinks]="true"
							[collectionSize]="data.totalElements"
							[ellipses]="false"
							[maxSize]="7"
							[pageSize]="data.size"
							[size]="'sm'"
							class="d-flex justify-content-end"></ngb-pagination>
					</div>

					<div class="div-show-and-select">
						<div class="div-select-qt-show">
							<pacto-cat-select
								(change)="listarProgramasPredefinidos()"
								[control]="pageSizeControl"
								[items]="itensPerPage"
								[size]="'SMALL'"></pacto-cat-select>
						</div>

						<div class="total-values">
							<span i18n="@@component-relatorio:mostrando">Mostrando</span>
							<span class="value">{{ data.content }}</span>
							<span i18n="@@component-relatorio:de">de</span>
							<span class="value">{{ data.totalElements }}</span>
						</div>
					</div>
				</ng-container>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@cadastro:programa-predefinido:titulo">
		Programas Predefinidos
	</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@cadastro:programa-predefinido:descricao">
		Gerencie os programas predefinidos.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@cadastro:programa-predefinido:table:nome">Nome</span>
</ng-template>
<ng-template #criadoPorColumnName>
	<span i18n="@@cadastro:programa-predefinido:table:criado">Criado por</span>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@cadastro:programa-predefinido:table:situacao">Status</span>
</ng-template>
<ng-template #generoColumnName>
	<span i18n="@@cadastro:programa-predefinido:table:genero">Gênero</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-tables:acoes">Ações</span>
</ng-template>

<ng-template #professorMontouCelula let-item="item">
	<div class="nivel-wp">
		<pacto-cat-person-avatar
			[diameter]="24"
			[uri]="item.professorMontou?.imageUri"></pacto-cat-person-avatar>
		<span class="nome">{{ item.professorMontou?.nome }}</span>
	</div>
</ng-template>

<!--end result coluna situacao-->
<ng-template #statusColumn let-item="item">
	<span *ngIf="item.ativo == true">Ativo</span>
	<span *ngIf="item.ativo == false">Inativo</span>
</ng-template>

<!--tooltip icons-->
<span #tooltipAtivar [hidden]="true">Ativar</span>
<span #tooltipInativar [hidden]="true">Inativar</span>
<span #tooltipEditar [hidden]="true">Editar</span>
<span #tooltipExcluir [hidden]="true">Excluir</span>
<!--end tooltip icons-->

<ng-template #situacaoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'true'">Ativa</span>
		<span *ngSwitchCase="'false'">Inativa</span>
	</ng-container>
</ng-template>

<ng-template #generoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'F'">Feminino</span>
		<span *ngSwitchCase="'M'">Masculino</span>
		<span *ngSwitchCase="'FM'">Feminino/Masculino</span>
	</ng-container>
</ng-template>

<ng-template #ordenacaoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'nome'">Atividade</span>
		<span *ngSwitchCase="'genero'">Gênero</span>
		<span *ngSwitchCase="'situacao'">Status</span>
	</ng-container>
</ng-template>

<span #inativarModalBody [hidden]="true">Deseja inativar o Programa:</span>
<span #ativarModalBody [hidden]="true">Deseja ativar o Programa:</span>

<span #inativarModalTitle [hidden]="true">Inativar Programa Predefinido?</span>
<span #inativarModalMsg [hidden]="true">
	Programa Predefinido inativado com sucesso.
</span>
<span #ativarModalTitle [hidden]="true">Ativar Programa Predefinido?</span>
<span #ativarModalMsg [hidden]="true">
	Programa Predefinido ativado com sucesso.
</span>

<!-- Criar programa -->
<span #criarProgramaModalTitle [hidden]="true">Criar Programa Predefinido</span>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="ATIVA">Ativo</span>
	<span xingling="INATIVA">Inativo</span>
</pacto-traducoes-xingling>
