@import "projects/ui/assets/import.scss";

::ng-deep.pct-trash-2 {
	color: $hellboyPri;
}

.nivel-wp {
	display: flex;
	align-items: center;

	.nome {
		padding: 0px 15px;
	}
}

@import "projects/ui/assets/import.scss";
.container-programa {
	display: grid;
	padding: 18px;

	.pessoa-prescricao {
		display: block;

		.grid-programa {
			display: grid;
			grid-template-columns: 1.4fr 1fr 1fr 1.4fr;
		}

		min-height: 110px;
		margin-bottom: 16px;
		padding: 16px;
		border: 1px solid $cinza03;
		border-radius: 8px;
		padding-right: 0px;

		.column-dados {
			min-height: 76px;
			display: grid;
			color: $preto05;
			font-size: 14px;
			font-weight: 400;
			line-height: 18px;

			.programa {
				font-size: 14px;
				font-weight: bold;
				margin-top: -10px;
			}

			.nome {
				font-size: 14px;
				text-transform: capitalize;
				line-height: 22px;
				color: $cinza05;
			}

			.genero {
				font-size: 16px;
				text-transform: capitalize;
				line-height: 22px;
				padding-left: 20px;
				color: $cinza05;
			}

			.matricula {
				color: $cinza05;
			}

			.titulo {
				padding-left: 10px;
				font-size: 16px;
			}

			.empresa {
				font-size: 16px;
				font-weight: bold;
			}
		}

		.dados-pessoais {
			.column-dados {
				margin-left: 16px;
				padding: 12px;
			}

			display: flex;

			.column-foto {
				padding-top: 7px;

				.foto-prescricao {
					width: 64px;
					height: 64px;
					border-radius: 32px;
					border: 1px solid #c7c7c7;
					background-repeat: no-repeat;
					background-position: center;
					background-size: cover;
				}
			}
		}

		.coluna-botao {
			padding-top: 19px;
			text-align: right;
			padding-right: 16px;

			.revisar {
				margin-right: 10px;
			}
		}
	}

	.btn-secundary {
		color: $azulim05;
		border: 1px solid $azulim05;
		background-color: $branco;
		height: 40px;

		font-size: 14px;
		margin-right: 10px;
		font-weight: bold;
	}

	.btn-primary {
		height: 40px;
		color: $branco;
		font-weight: bold;
		background-color: $azul !important;
		border: 1px solid $azul !important;
		font-size: 14px;
		width: 100px;
	}

	.btn-excluir {
		color: #fa1e1e;
		border: 1px solid $branco;
		background-color: $branco;
		height: 40px;
		width: 100px;
		font-size: 14px;
		margin-right: 10px;
		font-weight: bold;
	}

	.genero {
		color: $cinza05;
		width: 70px;
		padding: 5px 16px 5px 16px;
		text-align: center;
		font-size: 16px;
		font-weight: 400;
		line-height: 12px;
	}

	.situacaoAtividade {
		width: 70px;
		height: 22px;
		border-radius: 50px;
		padding: 5px 16px 5px 16px;
		text-align: center;
		font-size: 16px;
		font-weight: 400;
		line-height: 12px;
		background-color: $cinza01;
		color: $cinza06;

		&.INATIVA {
			background-color: $hellboy01;
			color: $hellboy05;
		}

		&.A_VENCER {
			background-color: $pequizao01;
			color: $pequizao06;
		}

		&.ATIVA {
			background-color: $chuchuzinho01;
			color: $chuchuzinho06;
		}
	}
}

.filtros {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1.3fr;
	padding: 24px 16px 16px 16px;

	.filtro1 {
		padding-right: 10%;
	}

	.search {
		position: relative;
		flex-grow: 1;
		margin-left: -15px;

		input {
			width: 70%;
			height: 41px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}

	pacto-cat-select {
		width: 70%;
	}

	.compartilhar {
		text-align: right;
	}

	::ng-deep {
		button.btn.pacto-primary {
			margin-right: 0px;
		}

		pacto-share-button {
			padding-right: 10px;

			.btn.pacto-primary {
				color: $azulim05;
				background-color: $branco;
				border: 1px solid $azulim05;

				.icon-drop {
					border-left: 1px solid $azulim05;
				}
			}
		}
	}
}

.footer-row {
	position: relative;
	display: flex;
	margin-right: 16px;
	flex-direction: row-reverse;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}

	.div-show-and-select {
		display: flex;

		> * {
			margin-left: 20px;
		}
	}
}

.filtros-atividade {
	margin-right: 10px;

	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}
}

::ng-deep pacto-log.primary {
	margin-right: -20px;
	height: 40px;

	.btn {
		height: 40px;
		color: $azul;
		padding: 4px 10px;
		margin-right: 8px;
		background: $branco;
		border: 1px solid $azulimPri;
	}

	.btn:hover {
		color: $azul;
		background: $branco;
		border: 1px solid $azul;
	}

	.btn.btn-primary:focus,
	.show > .btn-primary.dropdown-toggle {
		color: $azul;
		background: $branco;
		box-shadow: 0 0 0 -0.2rem rgb(3, 127, 226);
	}

	.btn-primary:focus,
	.btn-primary.focus {
		box-shadow: 0 0 0 0.2rem rgba(3, 127, 226, 0.5);
	}
}
