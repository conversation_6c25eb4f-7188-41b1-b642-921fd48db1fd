import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	DataFiltro,
	FilterComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	PactoDataGridColumnConfig,
} from "ui-kit";
import {
	PerfilAcessoRecursoNome,
	TreinoApiProgramaService,
	Programa,
} from "treino-api";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService } from "@base-core/modal/modal.service";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { ConfigurarFichaService } from "../../montagem-treino/configurar-ficha.service";
import { ProgramaPredefinidoEditComponent } from "../programa-predefinido-edit/programa-predefinido-edit.component";
import { ProgramaPredefinidoDeleteComponent } from "../programa-predefinido-delete/programa-predefinido-delete.component";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { Router } from "@angular/router";
import { FormControl } from "@angular/forms";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { debounceTime } from "rxjs/operators";
import { AdmRestService } from "../../../../../projects/adm/src/app/adm-rest.service";

@Component({
	selector: "pacto-programa-predefinido-lista",
	templateUrl: "./programa-predefinido-lista.component.html",
	styleUrls: ["./programa-predefinido-lista.component.scss"],
})
export class ProgramaPredefinidoListaComponent implements OnInit {
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("criadoPorColumnName", { static: true }) criadoPorColumnName;
	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("generoColumnName", { static: true }) generoColumnName;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("ordenacaoColumnName", { static: true }) ordenacaoColumnName;

	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("tooltipExcluir", { static: true }) tooltipExcluir;

	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarModalMsg", { static: true }) ativarModalMsg;

	@ViewChild("criarProgramaModalTitle", { static: true })
	criarProgramaModalTitle;

	@ViewChild("excluirModalTitle", { static: true }) excluirModalTitle;
	@ViewChild("excluirModalBody", { static: true }) excluirModalBody;

	@ViewChild("professorMontouCelula", { static: true }) professorMontouCelula;
	@Output() update: EventEmitter<any> = new EventEmitter();
	@ViewChild("situacaoTranslator", { static: true }) situacaoTranslator;
	@ViewChild("generoTranslator", { static: true }) generoTranslator;
	@ViewChild("ordenacaoTranslator", { static: true }) ordenacaoTranslator;
	@ViewChild("conflito", { static: true }) conflito;

	ready = false;

	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("filtroButton", { static: false }) filtroButton: FilterComponent;
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();

	searchControl = new FormControl();
	private filtro;
	baseFilter: DataFiltro = {};
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	filterConfig: GridFilterConfig;
	ngbPage = 1;
	pageSizeControl: FormControl = new FormControl();
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];

	listaProgramas: Array<Programa> = [];
	private temporaryFilters;

	constructor(
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private programaService: TreinoApiProgramaService,
		private configurarFichaService: ConfigurarFichaService,
		private router: Router,
		public admRestService: AdmRestService
	) {}

	table: PactoDataGridConfig;
	permissoesProgramasPredefinidos;
	nomeProgramaPredefinido;

	ngOnInit() {
		this.buttonName.disable = false;
		this.loadAllow();
		this.configFilters();
		this.ready = true;
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.ACESSOU_TELA_PROGRAMA_PREDEFINIDO
		);
		this.initAll();
		this.listarProgramasPredefinidos();
	}

	loadAllow() {
		this.permissoesProgramasPredefinidos = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMAS_PREDEFINIDOS
		);
	}

	btnClickHandler() {
		this.criarPrograma();
	}

	btnEditHandler(item) {
		this.editarPrograma(item);
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.editarPrograma($event.row);
		} else if ($event.iconName === "remover") {
			this.removerProgramaHandler($event.row);
		}
	}

	criarPrograma() {
		if (this.permissoesProgramasPredefinidos.incluir) {
			const modal = this.modalService.open(
				"Criar Programa Predefinido",
				ProgramaPredefinidoEditComponent
			);
			modal.result.then(
				(dto) => {
					this.tableData.reloadData();
					this.router.navigate([
						"treino",
						"cadastros",
						"programa",
						dto.programa.id,
					]);
				},
				() => {}
			);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu adiministrador"
			);
		}
	}

	editarPrograma(programa) {
		if (this.permissoesProgramasPredefinidos.editar) {
			this.configurarFichaService.programa$.next(programa);
			this.configurarFichaService.selecionarFichaInicial(0);
			this.router.navigate(["treino", "cadastros", "programa", programa.id]);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu adiministrador"
			);
		}
	}

	removerProgramaHandler(item) {
		if (this.permissoesProgramasPredefinidos.excluir) {
			const modal = this.modalService.open(
				"Exclusão de programa ",
				ProgramaPredefinidoDeleteComponent
			);
			modal.componentInstance.programaId = item.id;
			modal.componentInstance.programaNome = item.nome;
			modal.result.then(
				(dto) => {
					setTimeout(() => {
						this.listarProgramasPredefinidos();
						this.cd.detectChanges();
					});
					this.tableData.reloadData();
				},
				() => {}
			);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu adiministrador"
			);
		}
	}

	editarSituacaoHandler(item, status) {
		if (this.permissoesProgramasPredefinidos.editar) {
			this.nomeProgramaPredefinido = item.nomePrograma;
			setTimeout(() => {
				let modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
				let modalBody =
					this.inativarModalBody.nativeElement.innerHTML +
					" " +
					item.nome +
					"?";
				let modalButton = this.tooltipInativar.nativeElement.innerHTML;
				let modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
				if (item.situacao === "INATIVO") {
					modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
					modalBody =
						this.ativarModalBody.nativeElement.innerHTML +
						" " +
						item.nome +
						"?";
					modalButton = this.tooltipAtivar.nativeElement.innerHTML;
					modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
				}
				const handler = this.modalService.confirm(
					modalTitle,
					modalBody,
					modalButton
				);
				handler.result.then(() => {
					item.situacao = status;
					this.programaService
						.atualizarSituacaoProgramaPredefinido(item.id, item)
						.subscribe((response) => {
							if (response === "sucesso") {
								this.snotifyService.success(
									"Programa Predefinido atualizado com sucesso!"
								);

								setTimeout(() => {
									this.listarProgramasPredefinidos();
									this.listarProgramasPredefinidos();
									this.cd.detectChanges();
								}, 100);

								this.tableData.reloadData();
							} else {
								this.snotifyService.error(
									"Ocorreu algum problema ao tentar salvar a alteração, tente novamente!"
								);
							}
						});
				});
			});
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu adiministrador"
			);
		}
	}

	listarProgramasPredefinidos() {
		this.data.size = this.pageSizeControl.value;
		this.data.page = this.ngbPage;
		this.data.page = this.ngbPage - 1;

		if (this.filtro !== undefined) {
			this.filtro.quicksearchValue = this.searchControl.value;
		} else {
			this.filtro = {
				configs: {},
				quicksearchValue: this.searchControl.value,
				quicksearchFields: ["nome"],
				ordenacao: "asc",
				situacaoPrograma: ["true"],
			};
		}
		this.getFiltersShare();
		this.programaService
			.obterTodosProgramaPreDefinidos(this.data, this.filtro)
			.subscribe((data) => {
				this.listaProgramas = data.content;
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.cd.detectChanges();
			});
	}

	private configFilters() {
		this.filterConfig = {
			filters: this.addFilter(),
		};
	}

	private addFilter() {
		const result = [];

		result.push(
			{
				name: "situacaoPrograma",
				label: this.situacaoColumnName,
				type: GridFilterType.MANY,
				translator: this.situacaoTranslator,
				options: [{ value: "true" }, { value: "false" }],
				initialValue: ["true"],
			},
			{
				name: "generoPrograma",
				label: this.generoColumnName,
				type: GridFilterType.MANY,
				translator: this.generoTranslator,
				options: [{ value: "F" }, { value: "M" }, { value: "FM" }],
			},
			{
				name: "ordenacao",
				label: "Ordenação",
				type: GridFilterType.MANY,
				options: [
					{ value: "asc", label: "A|Z" },
					{ value: "desc", label: "Z|A" },
				],
			}
		);

		return result;
	}

	getFiltersShare(): DataFiltro {
		this.baseFilter.filters = this.filtro;
		this.baseFilter.configs = {};
		this.baseFilter.filters.share = "true";
		return this.baseFilter;
	}

	filtrar() {
		this.ngbPage = 1;
		this.listarProgramasPredefinidos();
	}

	filterHandler(filter) {
		this.temporaryFilters = filter;
		this.filterDropdown.close();
		let situacao = ["true"];
		if (this.temporaryFilters.filters.situacaoPrograma.length > 0) {
			situacao = this.temporaryFilters.filters.situacaoPrograma;
		}

		const genero = this.temporaryFilters.filters.generoPrograma;
		let ordenacao = "asc";
		if (this.temporaryFilters.filters.ordenacao.length === 1) {
			ordenacao = this.temporaryFilters.filters.ordenacao[0];
		}

		const filtros = {
			quicksearchValue: this.searchControl.value,
			situacaoPrograma: situacao,
			generoPrograma: genero,
			ordenacao: ordenacao,
			quicksearchFields: ["nome", "situacaoPrograma"],
		};
		this.filtro = filtros;
		this.data.size = this.pageSizeControl.value;
		this.data.page = this.ngbPage - 1;
		this.programaService
			.obterTodosProgramaPreDefinidos(this.data, this.filtro)
			.subscribe((data) => {
				this.listaProgramas = data.content;
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.cd.detectChanges();
			});
		this.filtroButton.close();
	}

	habilitaDesabilita(item, status) {
		this.editarSituacaoHandler(item, status);
	}

	pageChangeHandler(page) {
		if (page) {
			this.ngbPage = page;
			this.listarProgramasPredefinidos();
		}
	}

	fetchFiltros(): DataFiltro {
		return {
			filters: {
				quicksearchValue: this.searchControl.value,
				quicksearchFields: ["ativa"],
			},
			configs: {},
		};
	}

	initAll() {
		this.pageSizeControl.setValue(5);
		this.data.size = 5;
		this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
			this.filtrar();
		});
	}

	getImagem(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	obterColunasRelatorio(exportar?: boolean): Array<PactoDataGridColumnConfig> {
		const colunaNome: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			nome: "Nome",
			campo: "nome",
			titulo: "Nome",
			ordenavel: true,
			visible: true,
			width: "20%",
		};

		const colunaGenero: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			nome: "Genero",
			campo: "genero",
			titulo: "Genero",
			ordenavel: true,
			visible: true,
			width: "20%",
		};

		const colunaStatus: PactoDataGridColumnConfig = {
			inputType: "decimal",
			decimal: true,
			mostrarTitulo: true,
			nome: "Status",
			campo: "situacao",
			titulo: "Status",
			ordenavel: true,
			visible: true,
			width: "20%",
		};

		const colunasRelatorio: Array<PactoDataGridColumnConfig> = [];
		colunasRelatorio.push(colunaNome);
		colunasRelatorio.push(colunaGenero);
		colunasRelatorio.push(colunaStatus);
		return colunasRelatorio;
	}

	get urlLog(): string {
		return this.rest.buildFullUrl("log/programas-predefinidos");
	}
}
