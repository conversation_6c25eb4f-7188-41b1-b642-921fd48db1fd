import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { TreinoCoreModule } from "@treino-core/treino-core.module";
import { HintModule } from "../hint/hint.module";
import { ConhecimentoConceitoComponent } from "./conhecimento-conceito/conhecimento-conceito.component";
import { TreinoHomeComponent } from "./treino-home/treino-home.component";
import { TreinoRoutingModule } from "./treino-routing.module";
import { ModalPendenciasComponent } from "./treino-home/modal-pendencias/modal-pendencias.component";
import { HomePageModule } from "pacto-layout";

@NgModule({
	imports: [
		CommonModule,
		BaseSharedModule,
		TreinoCoreModule,
		TreinoRoutingModule,
		HintModule,
		HomePageModule,
	],
	declarations: [
		ConhecimentoConceitoComponent,
		TreinoHomeComponent,
		ModalPendenciasComponent,
	],
	entryComponents: [ModalPendenciasComponent],
})
export class TreinoModule {}
