import { Component, NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ModuleName } from "@base-core/modulo/modulo.model";
import { HomeFitListComponent } from "./homefit/components/home-fit-list/home-fit-list.component";
import { ConhecimentoConceitoComponent } from "./conhecimento-conceito/conhecimento-conceito.component";
import { TreinoHomeComponent } from "./treino-home/treino-home.component";

const treino = true;
export const routes: Routes = [
	{
		path: "",
		data: { module: ModuleName.TREINO },
		children: [
			{
				path: "",
				redirectTo: "home",
			},
			{
				path: "home",
				component: TreinoHomeComponent,
			},
			{
				path: "cadastros/atividades",
				loadChildren: () =>
					import("./atividade/atividade.module").then((m) => m.AtividadeModule),
			},
			{
				path: "cadastros/musculo",
				loadChildren: () =>
					import("src/app/base/musculo/musculo.module").then(
						(m) => m.MusculoModule
					),
			},
			{
				path: "cadastros/grupo-muscular",
				loadChildren: () =>
					import("src/app/base/grupo-muscular/grupo-muscular.module").then(
						(m) => m.GrupoMuscularModule
					),
			},
			{
				path: "cadastros/fichas-predefinidas",
				loadChildren: () =>
					import("./ficha-predefinida/ficha-predefinida.module").then(
						(m) => m.FichaPredefinidaModule
					),
			},
			{
				path: "cadastros/programa",
				loadChildren: () =>
					import("./programa-predefinido/programa-predefindo.module").then(
						(m) => m.ProgramaPredefindoModule
					),
			},
			{
				path: "cadastros/aparelhos",
				loadChildren: () =>
					import("src/app/base/aparelho/aparelho.routing.module").then(
						(m) => m.AparelhoRoutingModule
					),
			},
			{
				path: "cadastros/categoria-fichas-predefinidas",
				loadChildren: () =>
					import(
						"./categoria-ficha-predefinida/categoria-ficha-predefinida.module"
					).then((m) => m.CategoriaFichaPredefinidaModule),
			},
			{
				path: "cadastros/categoria-atividades",
				loadChildren: () =>
					import(
						"src/app/base/categoria-atividade/categoria-atividade.module"
					).then((m) => m.CategoriaAtividadeModule),
			},
			{
				path: "colaboradores",
				loadChildren: () =>
					import(
						"src/app/base/colaborador-simples/colaborador-simples.module"
					).then((m) => m.ColaboradorSimplesModule),
			},
			{
				path: "gestao",
				loadChildren: () =>
					import("src/app/treino/treino-gestao/treino-gestao.module").then(
						(m) => m.TreinoGestaoModule
					),
			},
			{
				path: "bi",
				loadChildren: () =>
					import("src/app/treino/treino-bi/treino-bi.module").then(
						(m) => m.TreinoBiModule
					),
			},
		],
	},
	{
		path: "montagem-programa",
		loadChildren: () =>
			import("src/app/treino/montagem-treino/montagem-treino.module").then(
				(m) => m.MontagemTreinoModule
			),
	},
	{
		path: "home-fit",
		loadChildren: () =>
			import("src/app/treino/montagem-treino/montagem-treino.module").then(
				(m) => m.MontagemTreinoModule
			),
	},
	{
		path: "conhecimento",
		component: ConhecimentoConceitoComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class TreinoRoutingModule {}
