import { ChangeDetector<PERSON>ef, Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { TreinoApiCategoriaFichaService } from "treino-api";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { ModalService } from "@base-core/modal/modal.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CategoriaFicha } from "../../../../../projects/treino-api/src/lib/programa.model";
import { CategoriaFichaPredefinidaEditComponent } from "../categoria-ficha-predefinida-edit/categoria-ficha-predefinida-edit.component";

@Component({
	selector: "pacto-categoria-ficha-predefinida-lista",
	templateUrl: "./categoria-ficha-predefinida-lista.component.html",
	styleUrls: ["./categoria-ficha-predefinida-lista.component.scss"],
})
export class CategoriaFichaPredefinidaListaComponent implements OnInit {
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("tableData", { static: false }) tableData;
	@ViewChild("codigoColumnName", { static: true }) codigoColumnName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("editError", { static: true }) editError;
	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@ViewChild("reverterModalTitle", { static: true }) reverterModalTitle;
	@ViewChild("reverterModalBody", { static: true }) reverterModalBody;
	@ViewChild("reverterSuccess", { static: true }) reverterSuccess;
	@ViewChild("reverterError", { static: true }) reverterError;

	categoriaFicha: CategoriaFicha = new (class implements CategoriaFicha {
		id: string;
		nome: string;
	})();

	constructor(
		private categoriaFichaService: TreinoApiCategoriaFichaService,
		private rest: RestService,
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private modal: NgbModal,
		private cd: ChangeDetectorRef
	) {}

	itemToRemove;
	loading = false;
	nomeCategoria = "";
	ready = false;
	table: PactoDataGridConfig;

	ngOnInit() {
		this.configTable();
		this.ready = true;
	}

	configTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("categoria-ficha/categorias"),
			logUrl: this.rest.buildFullUrl("log/categoria-ficha"),
			quickSearch: true,
			buttons: {
				conteudo: this.buttonName,
				nome: "add",
				id: "adicionarCategoria",
			},
			columns: [
				{
					nome: "codigo",
					titulo: this.codigoColumnName,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "id",
					width: "200px",
				},
				{
					nome: "nome",
					titulo: "nome",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
		if ($event.iconName === "removeFake") {
		}
	}

	btnClickHandler() {
		const modalRef = this.modal.open(CategoriaFichaPredefinidaEditComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue("");
		modalRef.componentInstance.categoria = "";
		modalRef.result.then((result) => {
			this.categoriaFicha = result;
			const createSuccess = this.createSuccess.nativeElement.innerHTML;
			this.categoriaFichaService
				.criarCategoriaFicha(this.categoriaFicha)
				.subscribe(
					(result) => {
						this.snotifyService.success(createSuccess);
						this.tableData.reloadData();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						this.snotifyService.error(err.meta.message);
					}
				);
		});
	}

	btnEditHandler(item) {
		const editError = this.editError.nativeElement.innerHTML;
		const modalRef = this.modal.open(CategoriaFichaPredefinidaEditComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue(item.nome);
		modalRef.componentInstance.categoria = item.categoria;
		modalRef.componentInstance.idSelecionado = item.id;
		modalRef.result
			.then((result) => {
				this.categoriaFicha = result;
				this.categoriaFicha.id = item.id;
				const editSuccess = this.editSuccess.nativeElement.innerHTML;

				this.categoriaFichaService
					.editarCategoriaFicha(this.categoriaFicha)
					.subscribe(
						(response) => {
							this.snotifyService.success(editSuccess);
							this.fetchData();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							this.snotifyService.error(err.meta.message);
						}
					);
			})
			.catch(() => {});
	}

	removeHandler(item) {
		const removeError = this.removeError.nativeElement.innerHTML;
		this.nomeCategoria = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					this.categoriaFichaService.removerCategoriaFicha(item.id).subscribe(
						(result) => {
							this.snotifyService.success(removeSuccess);
							this.fetchData();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							this.snotifyService.error(err.meta.message);
						}
					);
				})
				.catch(() => {});
		});
	}

	private fetchData() {
		this.tableData.reloadData();
	}
}
