.actions-row {
	width: 100px;
	text-align: center;

	i {
		cursor: pointer;
		padding-right: 10px;
	}
}

.loading-state,
.empty-state {
	text-align: center;
	padding-top: 60px;
	height: 150px;
}

.wrapper-row {
	min-height: 662px;
	width: 100%;
}

.wrapper-card {
	width: 333px;
	height: 114px;
	padding-top: 15px;
	padding-bottom: 15px;
	margin-right: 12px;
	margin-left: 12px;
	margin-top: 10px;
	margin-bottom: 10px;
	background-color: white;
	box-shadow: 0px 0px 2px 2px #eaeaea;
}

.top-row {
	display: flex;

	.first {
		flex-grow: 1;
		display: flex;
	}

	.second {
		display: flex;
		align-self: center;
	}
}

.navigator {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;
}

.preview-object {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	border: 1px solid #c7c7c7;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}
