import { Component, OnInit, ViewChild } from "@angular/core";
import {
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import {
	Nivel,
	PerfilAcessoRecursoNome,
	TreinoApiAparelhoService,
	TreinoApiAtividadeService,
	TreinoApiEmpresaService,
	TreinoApiGrupoMuscularService,
	TreinoApiNivelService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { SessionService } from "@base-core/client/session.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

@Component({
	selector: "pacto-atividade-lista-ia",
	templateUrl: "./atividade-lista-ia.component.html",
	styleUrls: ["./atividade-lista-ia.component.scss"],
})
export class AtividadeListaIaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: false }) removeModalTitle;
	@ViewChild("removeModalBody", { static: false }) removeModalBody;
	@ViewChild("removeModalMsg", { static: false }) removeModalMsg;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("tipoColumnName", { static: true }) tipoColumnName;
	@ViewChild("imagemColumnName", { static: true }) imagemColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("nivelColumnName", { static: false }) nivelColumnName;
	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("treinoia", { static: true }) iaColumnName;
	@ViewChild("nivelCelula", { static: false }) nivelCelula;
	@ViewChild("emailCelula", { static: false }) emailCelula;
	@ViewChild("imageCelula", { static: true }) imageCelula;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipRemover", { static: false }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("tipoTranslator", { static: true }) tipoTranslator;
	@ViewChild("situacaoTranslator", { static: true }) situacaoTranslator;
	@ViewChild("iaTranslator", { static: true }) iaTranslator;
	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarModalMsg", { static: true }) ativarModalMsg;
	@ViewChild("inativar", { static: false }) inativar;
	@ViewChild("ativar", { static: false }) ativar;
	@ViewChild("grupoMuscularesColumnName", { static: true })
	grupoMuscularColumnName;
	@ViewChild("aparelhosColumnName", { static: true }) aparelhosColumnName;
	@ViewChild("empresasColumnName", { static: true }) empresasColumnName;
	@ViewChild("empresaHabilitadasColumnName", { static: true })
	empresaHabilitadasColumnName;
	@ViewChild("empresaHabilitadas", { static: true }) empresaHabilitadas;

	ready = false;
	nomeAtividade;
	permissoesAtividades;
	grupoMuscularList;
	aparelhosList;
	empresasList;

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});

	constructor(
		private router: Router,
		private rest: RestService,
		private snotifyService: SnotifyService,
		private atividadeService: TreinoApiAtividadeService,
		private modalService: ModalService,
		private nivelService: TreinoApiNivelService,
		private session: SessionService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private aparelhoService: TreinoApiAparelhoService,
		private empresaService: TreinoApiEmpresaService
	) {}

	table: PactoDataGridConfig;
	filterConfig: any;
	niveis: ApiResponseList<Nivel> = {
		content: [],
	};

	ngOnInit() {
		this.loadAllow();
		this.configTable();
		this.configFilters();
		this.ready = true;
		this.getEmpresas().subscribe(() => {
			this.configFilters();
			this.ready = true;
		});
	}

	loadAllow() {
		this.permissoesAtividades = this.session.recursos.get(
			PerfilAcessoRecursoNome.ATIVIDADES
		);
	}

	btnClickHandler() {
		this.router.navigate(["treino", "cadastros", "atividades", "adicionar"]);
	}

	btnEditHandler(atividade) {
		this.router.navigate([
			"treino",
			"cadastros",
			"atividades",
			"ia",
			atividade.id,
		]);
	}

	editarSituacaoHandler(item) {
		this.nomeAtividade = item.nome;
		setTimeout(() => {
			let modalTitle = "";
			let modalBody = "";
			let modalButton = "";
			let modalMsg = "";
			if (item.ativa === true) {
				modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
				modalBody = this.inativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipInativar.nativeElement.innerHTML;
				modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
			} else {
				modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
				modalBody = this.ativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipAtivar.nativeElement.innerHTML;
				modalMsg = this.ativarModalMsg.nativeElement.innerHTML;
			}
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				modalButton
			);
			handler.result.then(() => {
				const atividade = { ativa: item.ativa };
				this.atividadeService
					.editarSituacaoAluno(item.id, atividade)
					.subscribe(() => {
						this.snotifyService.success(modalMsg);
						this.tableData.reloadData();
					});
			});
		});
	}

	getImagemAtividade(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	private configTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		const tooltipAtivar = this.tooltipAtivar.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("atividades/ia"),
			logUrl: this.rest.buildFullUrl("log/atividades/ia"),
			quickSearch: true,
			rowClick: this.permissoesAtividades.editar,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "id",
				},
				{
					nome: "image",
					titulo: this.imagemColumnName,
					mostrarTitulo: false,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.imageCelula,
					campo: "images",
				},
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "tipo",
					titulo: this.tipoColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "tipo",
				},
				{
					nome: "situacaoAtividade",
					titulo: this.situacaoColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.statusColumn,
					campo: "ativa",
				},
				{
					nome: "empresa",
					titulo: this.empresaHabilitadasColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.empresaHabilitadas,
					campo: "empresas",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
					showIconFn: (row) =>
						row.ativa === true && this.permissoesAtividades.editar,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: this.addFilter(),
		};
	}

	private addFilter() {
		const result = [];
		result.push({
			name: "tiposEnuns",
			label: this.tipoColumnName,
			type: GridFilterType.MANY,
			translator: this.tipoTranslator,
			options: [{ value: "ANAEROBICO" }, { value: "AEROBICO" }],
		});
		result.push({
			name: "situacaoAtividade",
			label: this.situacaoColumnName,
			type: GridFilterType.MANY,
			translator: this.situacaoTranslator,
			options: [{ value: "true" }, { value: "false" }],
		});
		result.push({
			name: "empresas",
			label: this.empresaHabilitadasColumnName,
			type: GridFilterType.MANY,
			options: this.empresasList,
		});
		result.push({
			name: "treinoia",
			label: this.iaColumnName,
			type: GridFilterType.MANY,
			translator: this.iaTranslator,
			options: [{ value: "editadas" }, { value: "naoeditadas" }],
		});
		return result;
	}

	private getGruposMusculares(): Observable<any> {
		const gruposMusculares$ = this.grupoMuscularService
			.obterTodosGruposMusculares()
			.pipe(
				map((data) => {
					data
						.sort((a, b) => (a.nome > b.nome ? 1 : -1))
						.forEach((grupoMuscular: any) => {
							grupoMuscular.value = grupoMuscular.id;
							grupoMuscular.label = grupoMuscular.nome.toUpperCase();
						});
					this.grupoMuscularList = data;
					return true;
				})
			);
		return gruposMusculares$;
	}

	private getEmpresas(): Observable<any> {
		const empresas$ = this.empresaService.obterTodasEmpresas().pipe(
			map((data) => {
				data
					.sort((a, b) => (a.nome > b.nome ? 1 : -1))
					.forEach((empresa: any) => {
						empresa.value = empresa.id;
						empresa.label = empresa.nome.toUpperCase();
					});
				this.empresasList = data;
				return true;
			})
		);
		return empresas$;
	}

	private getAparelhos(): Observable<any> {
		const aparelhos$ = this.aparelhoService.obterTodosAparelhos("false").pipe(
			map((data) => {
				data
					.sort((a, b) => (a.nome > b.nome ? 1 : -1))
					.forEach((aparelho: any) => {
						aparelho.value = aparelho.id;
						aparelho.label = aparelho.nome;
					});
				this.aparelhosList = data;
				return true;
			})
		);
		return aparelhos$;
	}

	private getNiveis(): Observable<any> {
		const niveis$ = this.nivelService.obterNiveis().pipe(
			map((dados) => {
				dados.content.forEach((nivel: any) => {
					nivel.value = nivel.id;
					nivel.label = nivel.nome;
				});
				this.niveis = dados;
				return true;
			})
		);
		return niveis$;
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "situacao") {
			this.editarSituacaoHandler($event.row);
		}
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
	}
}
