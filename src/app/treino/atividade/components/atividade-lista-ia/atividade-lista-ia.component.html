<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'ATIVIDADE_IA'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="filterConfig"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud-atividade:title">Atividades Treino por IA</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-alunos:alunos-cadastrados:description">
		Gere<PERSON>ie as atividades do Treino por IA.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Nome</span>
</ng-template>
<ng-template #tipoColumnName>
	<span i18n="@@crud-atividades:table:tipo">Tipo</span>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@crud-atividades:table:status">Situação</span>
</ng-template>
<ng-template #treinoia>
	<span i18n="@@crud-atividades:table:treinoia">Atividades da IA</span>
</ng-template>
<ng-template #empresaHabilitadasColumnName>
	<span>Empresa</span>
</ng-template>
<ng-template #empresaHabilitadas let-item="item">
	<div
		*ngIf="!item.empresas || !item.empresas.length"
		class="empty-list"
		i18n="@@perfil-aluno:nenhum-registro">
		Todas
	</div>
	<span
		*ngFor="let atvempresa of item.empresas; let index = index; last as isLast">
		{{ atvempresa.empresa.nome }}
		<span *ngIf="!isLast">|</span>
	</span>
</ng-template>
<ng-template #imagemColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Foto</span>
</ng-template>
<ng-template #grupoMuscularesColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Grupo Musculares</span>
</ng-template>
<ng-template #aparelhosColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Aparelhos</span>
</ng-template>
<ng-template #empresasColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Empresas</span>
</ng-template>

<!--end result coluna situacao-->
<ng-template #statusColumn let-item="item">
	<span *ngIf="item.ativa == true" i18n="@@crud-alunos:ativo:result">
		Ativo
	</span>
	<span *ngIf="item.ativa == false" i18n="@@crud-alunos:inativo:result">
		Inativo
	</span>
</ng-template>

<ng-template #imageCelula let-item="item">
	<div
		[ngStyle]="{
			'background-image': getImagemAtividade(
				item.images.length ? item.images[0].uri : 'assets/images/icon-peso.svg'
			),
			'background-size': item.images.length ? 'cover' : 'auto'
		}"
		class="preview-object"></div>
</ng-template>

<!--SITUACAO TRANSLATOR-->
<ng-template #tipoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'ANAEROBICO'" i18n="@@crud-alunos:ativo:result">
			Neuromuscular
		</span>
		<span *ngSwitchCase="'AEROBICO'" i18n="@@crud-alunos:inativo:result">
			Cardiovascular
		</span>
	</ng-container>
</ng-template>
<!--END SITUACAO TRANSLATOR-->
<!--SITUACAO TRANSLATOR 2-->
<ng-template #situacaoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'true'" i18n="@@crud-alunos:ativo:result">Ativa</span>
		<span *ngSwitchCase="'false'" i18n="@@crud-alunos:inativo:result">
			Inativa
		</span>
	</ng-container>
</ng-template>
<!--END SITUACAO TRANSLATOR-->
<!--IA TRANSLATOR 2-->
<ng-template #iaTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'todas'" i18n="@@crud-alunos:ativo:result">
			Todas as atividades da IA
		</span>
		<span *ngSwitchCase="'editadas'" i18n="@@crud-alunos:inativo:result">
			Editadas
		</span>
		<span *ngSwitchCase="'naoeditadas'" i18n="@@crud-alunos:inativo:result">
			Não editadas
		</span>
	</ng-container>
</ng-template>
<!--IA TRANSLATOR-->

<!--tooltip icons-->
<span #tooltipAtivar [hidden]="true" i18n="@@crud-alunos:ativar:tooltip-icon">
	Ativar
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
<!--end tooltip icons-->

<span #inativarModalBody [hidden]="true">
	Deseja inativar a atividade: {{ nomeAtividade }} ?
</span>
<span #ativarModalBody [hidden]="true">
	Deseja ativar a atividade: {{ nomeAtividade }} ?
</span>

<span #inativarModalTitle [hidden]="true">Inativar Atividade ?</span>
<span #inativarModalMsg [hidden]="true">Atividade inativada com sucesso.</span>
<span #ativarModalTitle [hidden]="true">Ativar Atividade ?</span>
<span #ativarModalMsg [hidden]="true">Atividade ativada com sucesso.</span>
