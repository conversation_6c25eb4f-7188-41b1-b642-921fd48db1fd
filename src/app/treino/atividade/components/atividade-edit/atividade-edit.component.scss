@import "~src/assets/scss/pacto/plataforma-import.scss";

@media (min-width: 768px) {
	.form-check {
		margin-top: 32px;
	}
}

.header-row {
	display: flex;
	justify-content: space-between;
	padding-bottom: 10px;
}

.images-wrapper {
	margin-top: 10px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	border-top: 1px solid #d1d1d1;
	padding-top: 15px;

	.imagem {
		margin: 10px;
		width: 150px;

		.imagem-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f5f5f5;
			width: 150px;
			height: 150px;
		}

		.professor {
			text-align: right;
			height: 30px;
			color: #5aacec;
			width: calc(100% - 20px);
			padding-top: 5px;
			padding-right: 5px;
			word-break: break-all;
			overflow: hidden;

			.form-check-input {
				margin-left: 0.4rem;
			}
		}

		.control-footer {
			display: flex;

			.name {
				width: calc(100% - 20px);
				padding-top: 5px;
				padding-right: 5px;
				word-break: break-all;
				overflow: hidden;
			}

			.icon {
				padding-top: 5px;
				flex-grow: 1;
				cursor: pointer;
			}
		}

		img {
			max-width: 150px;
			max-height: 150px;
		}
	}
}

.actions {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;

	button {
		margin-left: 15px;
	}
}

.loading-blur {
	filter: blur(5px);
}

.linkYouTube {
	user-select: text !important;
}

.sk-fading-circle {
	position: absolute;
	width: 30px;
	height: 30px;
	left: calc(50% - 30px / 2);
	top: calc(50% - 30px / 2);
}

.atividades-relacionadas {
	span.title {
		display: block;
		font-weight: 600;
		font-size: 16px;
		margin-bottom: 5px;
		margin-top: 10px;
	}
}

.btn.btn-primary {
	margin-left: 10px;
	background-color: $azulim04;
	border-color: $azulim04;

	label {
		margin-bottom: 0;
		padding-left: 5px;
	}
}

.table-item {
	width: 50px;
	text-align: center;
}

.form-link-video {
	display: grid;
	grid-template-columns: 0.5fr 1.1fr 1.3fr 1fr;
	padding-top: 40px;
	padding-left: 30px;

	.btn-adicionar-link-video {
		text-align: left;
	}
}
