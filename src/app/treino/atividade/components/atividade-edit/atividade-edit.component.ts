import { ChangeDetector<PERSON>ef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { DataUrl, NgxImageCompressService } from "ngx-image-compress";
import { ListaInsertSelectFilterComponent } from "old-ui-kit";
import {
	AparelhoBase,
	Atividade,
	AtividadeConfigEmpresa,
	CategoriaAtividade,
	EmpresaFinanceiro,
	GrupoMuscular,
	ImagemCatalogo,
	Musculo,
	Nivel,
	TreinoApiAparelhoService,
	TreinoApiAtividadeService,
	TreinoApiCatalogoImagensService,
	TreinoApiCategoriaAtividadeService,
	TreinoApiEmpresaService,
	TreinoApiGrupoMuscularService,
	TreinoApiMusculoService,
	TreinoApiNivelService,
} from "treino-api";
import { SelectFilterParamBuilder, SelectFilterResponseParser } from "ui-kit";
import { AtividadeEmpresaConfigComponent } from "../atividade-empresa-config/atividade-empresa-config.component";
import { SeletorImagemAvancadoComponent } from "../seletor-imagem-avancado/seletor-imagem-avancado.component";
import { PerfilAcessoRecursoNome } from "../../../../../../projects/treino-api/src/lib/perfil-acesso-recurso.model";
import { SessionService } from "@base-core/client/session.service";

enum ImageType {
	CATALOG = "CATALOG",
	UPLOAD = "UPLOAD",
}

interface ImagemConfig {
	type?: ImageType;
	id?: string;
	uri: string;
	nome?: string;
	data?: any;
	professor: boolean;
}

@Component({
	selector: "pacto-atividade-edit",
	templateUrl: "./atividade-edit.component.html",
	styleUrls: ["./atividade-edit.component.scss"],
})
export class AtividadeEditComponent implements OnInit {
	@ViewChild("aparelhoLista", { static: true })
	aparelhoLista: ListaInsertSelectFilterComponent;
	@ViewChild("categoriaLista", { static: true })
	categoriaLista: ListaInsertSelectFilterComponent;
	@ViewChild("grupoMuscularLista", { static: true })
	grupoMuscularLista: ListaInsertSelectFilterComponent;
	@ViewChild("nivelLista", { static: true })
	nivelLista: ListaInsertSelectFilterComponent;
	@ViewChild("successMsg", { static: true }) successMsg;
	@ViewChild("duplicateRecord", { static: true }) duplicateRecord;
	@ViewChild("grupoObrigatorio", { static: true }) grupoObrigatorio;
	@ViewChild("errorMsg", { static: true }) errorMsg;
	@ViewChild("conflito", { static: true }) conflito;

	constructor(
		private route: ActivatedRoute,
		private modal: NgbModal,
		private aparelhoService: TreinoApiAparelhoService,
		private atividadeService: TreinoApiAtividadeService,
		private router: Router,
		private notify: SnotifyService,
		private empresaService: TreinoApiEmpresaService,
		private categoriaAtividadeService: TreinoApiCategoriaAtividadeService,
		private musculoService: TreinoApiMusculoService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private catalogoService: TreinoApiCatalogoImagensService,
		private nivelService: TreinoApiNivelService,
		private rest: RestService,
		private imageCompress: NgxImageCompressService,
		private cd: ChangeDetectorRef,
		private session: SessionService
	) {}

	listaAtividadesRelacionadas = [];
	mostarCheckboxDuracao: boolean;
	edit = false;
	editEntity: Atividade;
	atividadeRelacionada = new FormControl();
	mensagem: string;
	linkVideos = [];
	permissoesAtividades;
	temPermissaoExcluir = false;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		ativa: new FormControl(true),
		serieApenasDuracao: new FormControl(),
		tipo: new FormControl("", [Validators.required]),
		descricao: new FormControl(),
		usarNaPrescricao: new FormControl(),
	});

	formGroupVideosUri: FormGroup = new FormGroup({
		id: new FormControl(),
		linkVideo: new FormControl("", [Validators.required]),
		professor: new FormControl(false),
	});

	restringirControl: FormControl = new FormControl();
	empresasConfiguradas: Array<AtividadeConfigEmpresa> = [];
	images: Array<ImagemConfig> = [];

	// Dados para listas
	empresas: Array<EmpresaFinanceiro> = [];
	aparelhos: Array<AparelhoBase> = [];
	categoriasAtividade: Array<CategoriaAtividade> = [];
	gruposMusculares: Array<GrupoMuscular> = [];
	musculos: Array<Musculo> = [];
	niveis: ApiResponseList<Nivel> = {
		content: [],
	};
	catalogImagens: Array<ImagemCatalogo> = [];
	imgAulaDataUrl: DataUrl = "";
	loading = false;
	veioDaIa = false;

	ngOnInit() {
		this.route.params.subscribe((params) => {
			if (params.id) {
				this.editInit(params.id);
			} else {
				this.createInit();
			}
		});
		this.route.url.subscribe((segments) => {
			this.veioDaIa = segments[0].path === "ia";
		});
		this.setupForm();
		this.loadListaData();
		this.loadAllow();
	}

	loadAllow() {
		this.permissoesAtividades = this.session.recursos.get(
			PerfilAcessoRecursoNome.ATIVIDADES
		);
		if (
			!this.permissoesAtividades ||
			!Array.isArray(this.permissoesAtividades.permissao)
		) {
			this.permissoesAtividades = { permissao: [] };
		}
		this.verificarPermissoes();
	}

	private verificarPermissoes() {
		if (
			!this.permissoesAtividades ||
			!Array.isArray(this.permissoesAtividades.permissao)
		) {
			this.permissoesAtividades = { permissao: [] };
		}
		this.temPermissaoExcluir =
			this.permissoesAtividades.permissao.includes("EXCLUIR") ||
			this.permissoesAtividades.permissao.includes("TOTAL");

		if (!this.temPermissaoExcluir) {
			this.formGroup.get("ativa").disable();
			this.notify.warning(
				"Seu usuário não possui permissão para inativar Atividades, procure seu administrador."
			);
		} else {
			this.formGroup.get("ativa").enable();
		}
	}

	atualizarMostarCheckboxDuracao() {
		this.mostarCheckboxDuracao =
			this.formGroup.get("tipo").value === "CARDIOVASCULAR";
	}

	cancelHandler() {
		if (this.veioDaIa) {
			this.router.navigate([
				"treino",
				"cadastros",
				"atividades",
				"ia",
				"lista",
			]);
		} else {
			this.router.navigate(["treino", "cadastros", "atividades"]);
		}
	}

	novaEmpresaConfigHandler() {
		const modal = this.modal.open(AtividadeEmpresaConfigComponent);
		modal.componentInstance.empresas = this.obterEmpresasNaoUtilizadas();
		modal.result.then(
			(atividadeConfig) => {
				this.empresasConfiguradas.push(atividadeConfig);
			},
			() => {}
		);
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/atividades/${this.editEntity.id}`);
	}

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		const result = response.content;
		result.forEach((atividade, index) => {
			atividade.index = index;
		});
		return result;
	};

	atividadeSelectBuilder: SelectFilterParamBuilder = (term) => {
		const ids = this.listaAtividadesRelacionadas.map((objeto) => objeto.id);
		ids.push(this.editEntity && this.editEntity.id ? this.editEntity.id : 0);
		return {
			page: "0",
			size: "200",
			filters: JSON.stringify({
				situacaoAtividade: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
				grupoMuscularesIds: [],
				avoid: ids,
			}),
		};
	};

	submitHandler() {
		this.markAsTouched();
		if (!this.editEntity) {
			this.createHandler();
		} else {
			this.updateHandler();
		}
	}

	private createHandler() {
		if (this.formGroup.valid) {
			const dto = this.obterDto();
			if (dto.grupoMuscularIds.length === 0) {
				this.notify.error(this.grupoObrigatorio.nativeElement.innerHTML);
				return;
			}
			this.atividadeService.criarAtividade(dto).subscribe((result) => {
				if (
					result &&
					(result.toString() === "registro_duplicado" ||
						result.toString() === "validacao_atividade_ja_existe" ||
						result.toString() === "Categoria de Atividade já existente.")
				) {
					this.notify.error(this.duplicateRecord.nativeElement.innerHTML);
				} else if (result) {
					this.notify.success(this.successMsg.nativeElement.innerHTML);
					this.cancelHandler();
				}
			});
		} else {
			this.camposObrigatorios();
		}
	}

	private updateHandler() {
		if (this.formGroup.valid) {
			const dto = this.obterDto();
			if (dto.grupoMuscularIds.length === 0) {
				this.notify.error(this.grupoObrigatorio.nativeElement.innerHTML);
				return;
			}
			this.atividadeService
				.atualizarAtividade(this.editEntity.id, dto)
				.subscribe((result) => {
					if (
						result &&
						(result.toString() === "registro_duplicado" ||
							result.toString() === "validacao_atividade_ja_existe" ||
							result.toString() === "Categoria de Atividade já existente.")
					) {
						this.notify.error(this.duplicateRecord.nativeElement.innerHTML);
					} else if (result) {
						this.notify.success(this.successMsg.nativeElement.innerHTML);
						this.cancelHandler();
					}
				});
		} else {
			this.camposObrigatorios();
		}
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("tipo").markAsTouched();
	}

	private obterDto() {
		const dto: any = this.formGroup.getRawValue();

		if (!dto.ativa) {
			dto.ativa = false;
		}

		if (dto.tipo === "NEUROMUSCULAR") {
			delete dto.serieApenasDuracao;
		} else if (!dto.serieApenasDuracao) {
			dto.serieApenasDuracao = false;
		}

		if (this.empresasConfiguradas.length) {
			dto.empresas = [];
			this.empresasConfiguradas.forEach((config) => {
				dto.empresas.push({
					id: config.id,
					identificador: config.identificador,
					empresa: {
						id: config.empresa.id,
						nome: config.empresa.nome,
					},
				});
			});
		}
		dto.crossfit = false;
		dto.categoriaAtividadeIds = this.categoriaLista.getSelectedIds();
		dto.aparelhoIds = this.aparelhoLista.getSelectedIds();
		dto.grupoMuscularIds = this.grupoMuscularLista.getSelectedIds();
		dto.nivelIds = this.nivelLista.getSelectedIds();
		dto.atividadesRelacionadas = this.listaAtividadesRelacionadas.map(
			(objeto) => objeto.id
		);
		dto.imageUploads = [];
		dto.images = [];
		this.images.forEach((img) => {
			if (img.id) {
				dto.images.push({
					type: img.type,
					id: img.id,
					professor: img.professor,
				});
			} else {
				dto.imageUploads.push({
					data: img.data,
					nome: img.nome,
					professor: img.professor,
				});
			}
		});
		if (this.formGroupVideosUri.get("linkVideo").value != "") {
			this.linkVideos.push({
				linkVideo: this.formGroupVideosUri.get("linkVideo").value,
				professor: this.formGroupVideosUri.get("professor").value,
			});
		}
		dto.linkVideos = this.linkVideos;
		return dto;
	}

	private setupFormAtvRelacionadas() {
		this.atividadeRelacionada.valueChanges.subscribe((value) => {
			this.atividadeRelacionada = new FormControl();
			if (this.listaAtividadesRelacionadas.length < 10) {
				this.listaAtividadesRelacionadas.push(value);
			}
			this.setupFormAtvRelacionadas();
		});
	}

	private setupForm() {
		this.atualizarMostarCheckboxDuracao();
		this.setupFormAtvRelacionadas();
		this.formGroup.get("tipo").valueChanges.subscribe(() => {
			this.atualizarMostarCheckboxDuracao();
		});
	}

	private loadListaData() {
		this.aparelhoService
			.obterTodosAparelhos("false")
			.subscribe((dados) => (this.aparelhos = dados));
		this.empresaService
			.obterTodasEmpresas()
			.subscribe((dados) => (this.empresas = dados));
		this.categoriaAtividadeService
			.obterTodasCategoriasAtividade()
			.subscribe((dados) => (this.categoriasAtividade = dados));
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.gruposMusculares = dados;
				this.cd.detectChanges();
			});
		this.musculoService
			.obterTodosMusculos()
			.subscribe((dados) => (this.musculos = dados));
		this.nivelService.obterNiveis().subscribe((dados) => (this.niveis = dados));
		this.catalogoService
			.obterCatalogoImagens()
			.subscribe((dados) => (this.catalogImagens = dados));
	}

	private editInit(id) {
		this.edit = true;
		this.atividadeService.obterAtividade(id).subscribe((dados) => {
			this.editEntity = dados;
			this.loadForm();
		});
	}

	private loadForm() {
		const src = this.editEntity;
		this.formGroup.patchValue({
			nome: src.nome,
			ativa: src.ativa,
			serieApenasDuracao: src.serieApenasDuracao,
			tipo: src.tipo,
			descricao: src.descricao,
			usarNaPrescricao: src.usarNaPrescricao,
		});
		this.listaAtividadesRelacionadas = src.atividadesRelacionadas;
		this.categoriaLista.setSelectedItens(
			src.categoriasAtividade ? src.categoriasAtividade : []
		);
		this.aparelhoLista.setSelectedItens(src.aparelhos ? src.aparelhos : []);
		this.grupoMuscularLista.setSelectedItens(
			src.gruposMusculares ? src.gruposMusculares : []
		);
		this.nivelLista.setSelectedItens(src.niveis ? src.niveis : []);
		src.images.forEach((image) => {
			this.images.push({
				id: image.id,
				type:
					image.type.toString() === ImageType.CATALOG
						? ImageType.CATALOG
						: ImageType.UPLOAD,
				uri: image.uri,
				nome: image.nome,
				data: null,
				professor: image.professor,
			});
		});
		src.linkVideos.forEach((link) => {
			this.linkVideos.push({
				id: link.id,
				linkVideo: link.linkVideo,
				professor: link.professor,
			});
		});
		this.empresasConfiguradas = [];
		src.empresas.forEach((empresa) => {
			this.empresasConfiguradas.push(empresa);
		});
		this.cd.detectChanges();
	}

	private obterEmpresasNaoUtilizadas() {
		const result = [];
		this.empresas.forEach((empresa) => {
			let found = false;
			this.empresasConfiguradas.forEach((config) => {
				if (
					parseInt(`${config.empresa.id}`, 10) === parseInt(`${empresa.id}`, 10)
				) {
					found = true;
				}
			});
			if (!found) {
				result.push(empresa);
			}
		});
		return result;
	}

	adicionarImagemHandler() {
		const tamanhoMaxImg = 1024 * 1024 * 1; // 1MB
		const tamanhoMaxGif = 1024 * 1024 * 2; // 2MB
		const modal = this.modal.open(SeletorImagemAvancadoComponent, {
			size: "lg",
		});
		modal.componentInstance.imagens = this.obterImagensDeCatalogoNaoUsadas();
		modal.componentInstance.sugerirUtilizarCampoVideo = true;
		modal.componentInstance.mensagemSugerida =
			"Caso utilize GIF muito grande, é recomendado utilizar a opção de link do youtube.";
		modal.result.then((image: ImagemConfig) => {
			if (image.type === ImageType.CATALOG) {
				this.handleCatalogImage(image);
			} else {
				this.imgAulaDataUrl = "";
				if (this.isImageGif(image)) {
					if (this.isTamanhoImagemPermitido(image, tamanhoMaxGif)) {
						this.handleUploadImage(image);
					} else {
						this.notify.error(
							"O arquivo de GIF escolhido excede o limite permitido de 1MB."
						);
					}
				} else {
					if (this.isTamanhoImagemPermitido(image, tamanhoMaxImg)) {
						this.handleUploadImage(image);
					} else {
						this.obterDataUrlImagem(image)
							.then(() => {
								this.comprimirImagem(tamanhoMaxImg);
								this.handleUploadImage(image);
							})
							.catch((error) => {
								this.handleUploadImage(image);
							});
					}
				}
			}
		});
	}

	obterDataUrlImagem(image: ImagemConfig): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			const file: File = image.data;
			const reader = new FileReader();
			reader.onload = () => {
				this.imgAulaDataUrl = reader.result as string;
				resolve();
			};
			reader.onerror = (error) => reject(error);
			if (file) {
				reader.readAsDataURL(file);
			}
		});
	}

	comprimirImagem(tamanhoMaxImg) {
		console.log(
			"Tamanho da imagem antes de ser comprimida: ",
			this.imgAulaDataUrl.length
		);
		// DOC: https://github.com/dfa1234/ngx-image-compress
		// Devido versão do angular, foi necessário utilizar outra versão mas com os mesmos comandos do link anterior:
		// https://www.npmjs.com/package/ngx-image-compress-legacy
		if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
			this.imageCompress
				.compressFile(this.imgAulaDataUrl, 1, 50, 50)
				.then((result: DataUrl) => {
					this.imgAulaDataUrl = result;
					if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
						this.comprimirImagem(tamanhoMaxImg);
					}
				});
		}
	}

	private isImageGif(image: ImagemConfig) {
		const regex: RegExp = /\.gif$/i;
		return regex.test(image.nome);
	}

	private isTamanhoImagemPermitido(image: ImagemConfig, tamanhoMax: number) {
		const file: File = image.data;
		if (file) {
			if (file.size > tamanhoMax) {
				return false;
			}
		}
		return true;
	}

	get showTipoErro() {
		const control = this.formGroup.get("tipo");
		return !control.valid && control.touched;
	}

	private camposObrigatorios() {
		const controlTipo = this.formGroup.get("tipo");
		const controlNome = this.formGroup.get("nome");
		this.mensagem = null;
		if (!controlNome.valid && controlNome.touched) {
			this.mensagem = "Campo obrigatório nome não foi preenchido.";
		}
		if (!controlTipo.valid && controlTipo.touched) {
			if (this.mensagem) {
				this.mensagem =
					"Campos obrigatórios nome e tipo de atividade não foram preenchidos.";
			} else {
				this.mensagem =
					"Campo obrigatório tipo de atividade não foi preenchido.";
			}
		}
		this.errorMsg.nativeElement.innerText = this.mensagem;
		const errorMsg = this.errorMsg.nativeElement.innerHTML;
		this.notify.error(errorMsg);
	}

	private obterImagensDeCatalogoNaoUsadas() {
		const result = [];
		this.catalogImagens.forEach((catalogImage) => {
			let found = false;
			this.images.forEach((image) => {
				if (image.type === ImageType.CATALOG && image.id === catalogImage.id) {
					found = true;
				}
			});
			if (!found) {
				result.push(catalogImage);
			}
		});
		return result;
	}

	private handleCatalogImage(image: ImagemConfig) {
		this.images.push(image);
		this.cd.detectChanges();
	}

	private handleUploadImage(image: ImagemConfig) {
		this.loading = true;
		const reader: FileReader = new FileReader();
		reader.onload = (event: any) => {
			if (this.imgAulaDataUrl !== undefined && this.imgAulaDataUrl !== "") {
				console.log(
					"Tamanho da imagem após ser comprimida: ",
					this.imgAulaDataUrl.length
				);
				image.uri = this.imgAulaDataUrl;
				image.data = this.imgAulaDataUrl.split(",")[1];
			} else {
				image.uri = event.target.result;
				const resultString: string = reader.result as string;
				image.data = resultString.split(",")[1];
			}
			this.cd.detectChanges();
		};
		reader.readAsDataURL(image.data);
		this.images.push(image);
		this.loading = false;
	}

	removeImageHandle(index) {
		this.images.splice(index, 1);
	}

	private createInit() {
		this.edit = false;
	}

	removeAtividadeRelacionada(item: any) {
		const removeIndex = this.listaAtividadesRelacionadas.findIndex(
			(i) => i.id === item.id
		);
		this.listaAtividadesRelacionadas.splice(removeIndex, 1);
		this.atividadeRelacionada = new FormControl();
		this.setupFormAtvRelacionadas();
	}

	get acaoHabilitada() {
		return this.formGroupVideosUri.get("linkVideo").value;
	}

	adicionarHandler() {
		this.linkVideos.push({
			linkVideo: this.formGroupVideosUri.get("linkVideo").value,
			professor: this.formGroupVideosUri.get("professor").value,
		});
		this.formGroupVideosUri.get("linkVideo").setValue("");
		this.formGroupVideosUri.get("professor").setValue(false);
	}

	deletButon(index) {
		this.linkVideos.splice(index, 1);
	}

	checkCheckBoxProfessor(objeto) {
		if (objeto.professor) {
			objeto.professor = false;
		} else {
			objeto.professor = true;
		}
	}
}
