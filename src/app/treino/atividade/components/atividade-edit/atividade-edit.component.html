<div [ngClass]="{ 'loading-blur': loading }">
	<ng-template #title>
		<span *ngIf="edit" i18n="@@crud-atividade:edit:title">
			Editar Atividade
		</span>
		<span *ngIf="!edit" i18n="@@crud-atividade:create:title">
			Criar Atividade
		</span>
	</ng-template>

	<pacto-cat-layout-v2>
		<pacto-breadcrumbs
			*ngIf="!veioDaIa"
			[breadcrumbConfig]="{
				categoryName: 'CADASTRO',
				menu: 'ATIVIDADE',
				menuLink: ['treino', 'cadastros', 'atividades']
			}"></pacto-breadcrumbs>

		<pacto-breadcrumbs
			*ngIf="veioDaIa"
			[breadcrumbConfig]="{
				categoryName: 'CADASTRO',
				menu: 'ATIVIDADE_IA',
				menuLink: ['treino', 'cadastros', 'atividades', 'ia', 'lista']
			}"></pacto-breadcrumbs>

		<pacto-title-card [title]="title">
			<div *ngIf="editEntity?.idIA2" class="alert alert-info mt-2">
				Esta atividade foi criada pela IA.
			</div>
			<div class="row">
				<div class="col-lg-6">
					<pacto-input
						[control]="formGroup.get('nome')"
						[id]="'inptNomeAtividade'"
						i18n-label="@@crud-atividade:nome:label"
						i18n-mensagem="@@crud-atividade:nome:mensagem"
						i18n-placeholder="@@crud-atividade:nome:placeholder"
						label="Nome"
						mensagem="Defina um nome com pelo menos 3 caracteres."
						placeholder="Nome da atividade"></pacto-input>
				</div>
				<div class="col-lg-6">
					<div *ngIf="!editEntity?.idIA2" class="form-group">
						<div class="form-check">
							<input
								[attr.disabled]="!temPermissaoExcluir ? true : null"
								[formControl]="formGroup.get('ativa')"
								class="form-check-input"
								id="defaultCheck1"
								type="checkbox"
								value="" />
							<label
								class="form-check-label"
								for="defaultCheck1"
								i18n="@@crud-atividade:ativa:label">
								Atividade ativa ?
							</label>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-6">
					<div [ngClass]="{ 'has-danger': showTipoErro }" class="form-group">
						<label
							class="control-label"
							i18n="@@crud-atividade:tipo-atividade:label">
							Tipo de atividade
						</label>
						<select
							[formControl]="formGroup.get('tipo')"
							class="form-control form-control-sm"
							id="selectTipoAtividade">
							<option
								i18n="@@crud-atividade:neuromuscular:option"
								value="NEUROMUSCULAR">
								Neuromuscular
							</option>
							<option
								i18n="@@crud-atividade:cardiovascular:option"
								value="CARDIOVASCULAR">
								Cardiovascular
							</option>
						</select>
						<small
							*ngIf="showTipoErro"
							class="form-control-feedback"
							i18n="@@crud-atividade:defina-tipo-atividade">
							Defina o tipo da atividade.
						</small>
					</div>
				</div>
				<div *ngIf="mostarCheckboxDuracao" class="col-lg-6">
					<div class="form-group">
						<div class="form-check">
							<input
								[formControl]="formGroup.get('serieApenasDuracao')"
								class="form-check-input"
								id="defaultCheck2"
								type="checkbox"
								value="" />
							<label
								class="form-check-label"
								for="defaultCheck2"
								i18n="@@crud-atividade:apenas duracao:label">
								Série apenas com duração ?
							</label>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-12">
					<pacto-textarea
						[control]="formGroup.get('descricao')"
						i18n-label="@@crud-atividade:descricao:label"
						label="Descrição"
						rows="3"></pacto-textarea>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-6">
					<pacto-atividade-empresas
						(criarNovo)="novaEmpresaConfigHandler()"
						[empresas]="empresasConfiguradas"></pacto-atividade-empresas>
				</div>
				<div class="atividades-relacionadas col-lg-6">
					<span class="title">Atividades alternativas (máximo de 10)</span>
					<pacto-cat-select-filter
						#atividadeSelect
						[control]="atividadeRelacionada"
						[endpointUrl]="_rest.buildFullUrl('atividades/montarTreino')"
						[id]="'select-atividade'"
						[labelKey]="'nome'"
						[paramBuilder]="atividadeSelectBuilder"
						[resposeParser]="responseParser"></pacto-cat-select-filter>

					<div class="list-wrapper">
						<div
							[maxHeight]="'200px'"
							[ngClass]="{
								empty:
									!listaAtividadesRelacionadas ||
									listaAtividadesRelacionadas.length === 0
							}"
							pactoCatSmoothScroll>
							<table class="table">
								<tbody>
									<tr *ngFor="let item of listaAtividadesRelacionadas">
										<td>{{ item.nome }}</td>
										<td
											(click)="removeAtividadeRelacionada(item)"
											class="action-column">
											<i class="fa fa-trash-o"></i>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-6">
					<pacto-lista-insert-select-filter
						#aparelhoLista
						[id]="'lista-insert'"
						[items]="aparelhos"
						[labelKey]="'nome'"
						i18n-placeholder="@@crud-atividade:aparelho:placeholder"
						i18n-title="@@crud-atividade:aparelho:title"
						placeholder="Relacionar aparelho..."
						title="Aparelhos Relacionados"></pacto-lista-insert-select-filter>
				</div>
				<div class="col-lg-6">
					<pacto-lista-insert-select-filter
						#categoriaLista
						[items]="categoriasAtividade"
						[labelKey]="'nome'"
						i18n-placeholder="@@crud-atividade:categoria:placeholder"
						i18n-title="@@crud-atividade:categoria-atividade:title"
						placeholder="Relacionar categoria..."
						title="Categorias de Atividade Relacionadas"></pacto-lista-insert-select-filter>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-6">
					<pacto-lista-insert-select-filter
						#grupoMuscularLista
						[items]="gruposMusculares"
						[labelKey]="'nome'"
						i18n-placeholder="@@crud-atividade:grupo-muscular:placeholder"
						i18n-title="@@crud-atividade:grupo-muscular:title"
						placeholder="Relacionar grupo muscular..."
						title="Grupos Musculares Relacionadas"></pacto-lista-insert-select-filter>
				</div>
				<div class="col-lg-6">
					<pacto-lista-insert-select-filter
						#nivelLista
						[items]="niveis.content"
						[labelKey]="'nome'"
						i18n-placeholder="@@crud-atividade:dificuldade:placeholder"
						i18n-title="@@crud-atividade:dificuldade:title"
						placeholder="Relacionar nível..."
						title="Níveis de dificuldade"></pacto-lista-insert-select-filter>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-6">
					<pacto-input
						[control]="formGroupVideosUri.get('linkVideo')"
						[id]="'input-link'"
						label="Video (link no YouTube)"
						placeholder="link"></pacto-input>
				</div>
				<div class="col-lg-6">
					<div class="form-link-video">
						<span [ds3Tooltip]="tooltipProfessor" [tooltipPosition]="'top'">
							<input
								[formControl]="formGroupVideosUri.get('professor')"
								class="form-check-input"
								id="checkProfessor"
								type="checkbox"
								value="" />
							<label
								class="form-check-label"
								for="checkProfessor"
								i18n="@@crud-atividade:ativa:label">
								Professor?
							</label>
						</span>

						<div class="btn-adicionar-link-video">
							<button
								(click)="adicionarHandler()"
								[disabled]="!acaoHabilitada"
								class="pct pct-plus btn btn-sm btn-primary"
								i18n="@@crud-atividade:imagem:button"
								id="sssdcew">
								<label>Adicionar</label>
							</button>
						</div>
					</div>
				</div>
			</div>

			<div *ngIf="linkVideos.length > 0" class="row linkYouTube">
				<div class="col-lg-6">
					<div class="list-wrapper">
						<table class="table">
							<thead>
								<tr>
									<th>Link</th>
									<th class="table-item">Professor</th>
									<th class="table-item">Excluir</th>
								</tr>
							</thead>
							<tbody>
								<tr *ngFor="let link of linkVideos; let index = index">
									<td>{{ link.linkVideo }}</td>
									<td class="table-item">
										<input
											(click)="checkCheckBoxProfessor(link)"
											[checked]="link.professor"
											[ds3Tooltip]="tooltipProfessor"
											[tooltipPosition]="'top'"
											class="form-check-input"
											type="checkbox" />
									</td>
									<td class="table-item">
										<i (click)="deletButon(index)" class="fa fa-trash-o"></i>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-lg-12">
					<div class="form-group images-wrapper">
						<div class="header-row">
							<label class="control-label" i18n="@@crud-atividade:imagem:label">
								Imagens
							</label>
							<button
								(click)="adicionarImagemHandler()"
								class="btn btn-sm btn-secondary"
								i18n="@@crud-atividade:imagem:button"
								id="btnAddImagem">
								Adicionar Imagem
							</button>
						</div>
						<div class="image-list">
							<div
								*ngFor="let imagem of images; let index = index"
								class="imagem">
								<div class="imagem-wrapper">
									<img src="{{ imagem.uri }}" />
								</div>
								<div class="professor">
									<label>Professor</label>
									<input
										(click)="checkCheckBoxProfessor(imagem)"
										[checked]="imagem.professor"
										[ds3Tooltip]="tooltipProfessor"
										[tooltipPosition]="'top'"
										class="form-check-input"
										type="checkbox" />
								</div>
								<div class="control-footer">
									<div class="name">
										<span>
											{{ imagem.nome != "" ? imagem.nome : "UPLOAD-" + index }}
										</span>
									</div>
									<div class="icon">
										<i
											(click)="removeImageHandle(index)"
											_ngcontent-c22=""
											class="fa fa-trash-o"></i>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!--		Primeira versão da I.A.-->
			<!--      <div *ngIf="editEntity?.idIA" title="ID IA: {{editEntity?.idIA}}">-->
			<!--        <div>Esta atividade é usada pela prescrição de treino por IA.</div>-->
			<!--          <div>Nome original: {{editEntity?.nomeOriginalIA}}.</div>-->
			<!--      </div>-->
			<!--		<div class="row">-->
			<!--			<div class="col-lg-6">-->
			<!--				<div class="form-group" *ngIf="editEntity?.idIA">-->
			<!--					<div class="form-check">-->
			<!--						<input class="form-check-input" [formControl]="formGroup.get('usarNaPrescricao')" type="checkbox" value=""-->
			<!--							   id="usarNaPrescricao" [disabled]="!formGroup.get('usarNaPrescricao').value">-->
			<!--						<label i18n="@@crud-atividade:ativa:label" class="form-check-label" for="defaultCheck1">-->
			<!--							Usar na prescrição de treino ?-->
			<!--						</label>-->
			<!--					</div>-->
			<!--				</div>-->
			<!--			</div>-->
			<!--		</div>-->

			<div class="actions">
				<button
					(click)="submitHandler()"
					class="btn btn-primary"
					i18n="@@buttons:salvar"
					id="btnSalvar">
					Salvar
				</button>
				<button
					(click)="cancelHandler()"
					class="btn btn-secondary"
					i18n="@@buttons:cancelar">
					Cancelar
				</button>
				<pacto-log *ngIf="editEntity?.id" [url]="urlLog"></pacto-log>
			</div>

			<!--	  <pacto-corpo-frontal-simplificado-->
			<!--		*ngIf="editEntity && editEntity.gruposMusculares && gruposMusculares.length > 0"-->
			<!--	  	[listaAtiva]="editEntity?.gruposMusculares"-->
			<!--		[listaCompletaGrupoMuscular]="gruposMusculares"-->
			<!--		[podeSelecionarGrupoMuscular]="true">-->
			<!--	  </pacto-corpo-frontal-simplificado>-->

			<!--	  <pacto-corpo-posterior-simplificado-->
			<!--		  *ngIf="editEntity && editEntity.gruposMusculares && gruposMusculares.length > 0"-->
			<!--		  [listaAtiva]="editEntity?.gruposMusculares"-->
			<!--		  [listaCompletaGrupoMuscular]="gruposMusculares"-->
			<!--		  [podeSelecionarGrupoMuscular]="true">-->
			<!--	  </pacto-corpo-posterior-simplificado>-->
		</pacto-title-card>
	</pacto-cat-layout-v2>
</div>

<div *ngIf="loading">
	<div class="sk-fading-circle">
		<img class="double-arrow" src="./assets/images/gif/loading-pacto.gif" />
	</div>
</div>

<span #successMsg [hidden]="true" i18n="@@crud-atividade:config-success">
	Atividade configurada com sucesso.
</span>
<span #duplicateRecord [hidden]="true" i18n="@@crud-atividade:config-duplicate">
	Já existe uma atividade com este mesmo nome.
</span>
<span #errorMsg [hidden]="true" i18n="@@crud-atividade:config-error"></span>
<div #conflito [hidden]="true" id="help-message-duplicated-entity-name">
	Já existe um cadastro com esse mesmo nome
</div>
<span
	#grupoObrigatorio
	[hidden]="true"
	i18n="@@crud-atividade:grupo-obrigatorio">
	É necessário adicionar Grupos Musculares
</span>
<ng-template #tooltipProfessor>
	<div class="itens-tooltip">
		Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o
		professor
	</div>
</ng-template>
