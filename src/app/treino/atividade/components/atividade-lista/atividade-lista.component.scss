@import "src/assets/scss/variable.scss";

.actions-row {
	width: 100px;
	text-align: center;

	i {
		cursor: pointer;
		padding-right: 10px;
	}
}

.loading-state,
.empty-state {
	text-align: center;
	padding-top: 60px;
	height: 150px;
}

.wrapper-row {
	min-height: 662px;
	width: 100%;
}

.wrapper-card {
	width: 333px;
	height: 114px;
	padding-top: 15px;
	padding-bottom: 15px;
	margin-right: 12px;
	margin-left: 12px;
	margin-top: 10px;
	margin-bottom: 10px;
	background-color: white;
	box-shadow: 0px 0px 2px 2px #eaeaea;
}

.top-row {
	display: flex;

	.first {
		flex-grow: 1;
		display: flex;
	}

	.second {
		display: flex;
		align-self: center;
	}
}

.navigator {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;
}

.preview-object {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	border: 1px solid #c7c7c7;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}

@import "projects/ui/assets/import.scss";
.container-prescricao {
	display: grid;
	padding: 18px;

	.pessoa-prescricao {
		display: block;

		.grid-prescricao {
			display: grid;
			grid-template-columns: 2fr 1fr 1fr 1.4fr;
		}

		min-height: 110px;
		margin-bottom: 16px;
		padding: 16px;
		border: 1px solid $cinza03;
		border-radius: 8px;
		padding-right: 0px;

		.column-dados {
			min-height: 76px;
			display: grid;
			color: $preto05;
			font-size: 14px;
			font-weight: 400;
			line-height: 18px;

			.atividade {
				font-size: 14px;
				font-weight: bold;
				margin-top: -10px;
			}

			.nome {
				font-size: 14px;
				text-transform: capitalize;
				line-height: 22px;
				color: $cinza05;
			}

			.matricula {
				color: $cinza05;
			}

			.titulo {
				font-size: 16px;
			}

			.empresa {
				font-size: 16px;
				font-weight: bold;
			}
		}

		.dados-pessoais {
			.column-dados {
				margin-left: 16px;
				padding: 12px;
			}

			display: flex;

			.column-foto {
				padding-top: 7px;

				.foto-prescricao {
					width: 64px;
					height: 64px;
					border-radius: 32px;
					border: 1px solid #c7c7c7;
					background-repeat: no-repeat;
					background-position: center;
					background-size: cover;
				}
			}
		}

		.coluna-botao {
			padding-top: 19px;
			text-align: right;
			padding-right: 16px;

			.revisar {
				margin-right: 10px;
			}
		}
	}

	.btn-secundary {
		color: $azulim05;
		border: 1px solid $azulim05;
		background-color: $branco;
		height: 40px;
		width: 100px;
		font-size: 14px;
		margin-right: 10px;
		font-weight: bold;
	}

	.btn-primary {
		height: 40px;
		color: $branco;
		font-weight: bold;
		background-color: $azul !important;
		border: 1px solid $azul !important;
		width: 100px;
		font-size: 14px;
	}

	.situacaoAtividade {
		width: 70px;
		height: 22px;
		border-radius: 50px;
		padding: 5px 16px 5px 16px;
		text-align: center;
		font-size: 16px;
		font-weight: 400;
		line-height: 12px;
		background-color: $cinza01;
		color: $cinza06;

		&.INATIVA {
			background-color: $hellboy01;
			color: $hellboy05;
		}

		&.A_VENCER {
			background-color: $pequizao01;
			color: $pequizao06;
		}

		&.ATIVA {
			background-color: $chuchuzinho01;
			color: $chuchuzinho06;
		}
	}
}

.filtros {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1.3fr;
	padding: 24px 16px 16px 16px;

	.filtro1 {
		padding-right: 10%;
	}

	.search {
		margin-left: -15px;
		position: relative;
		flex-grow: 1;

		input {
			width: 70%;
			height: 41px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}

	pacto-cat-select {
		width: 70%;
	}

	.compartilhar {
		min-width: 500px;
		text-align: right;
	}

	::ng-deep {
		button.btn.pacto-primary {
			margin-right: 0px;
		}

		pacto-share-button {
			padding-right: 10px;

			.btn.pacto-primary {
				color: $azulim05;
				background-color: $branco;
				border: 1px solid $azulim05;

				.icon-drop {
					border-left: 1px solid $azulim05;
				}
			}
		}
	}
}

.footer-row {
	position: relative;
	display: flex;
	margin-right: 16px;
	flex-direction: row-reverse;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}

	.div-show-and-select {
		display: flex;

		> * {
			margin-left: 20px;
		}
	}
}

.simple-total-row {
	background-color: #f9f9f9;
	border-top: 1px solid #ededed;
	border-bottom: 1px solid #ededed;
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 30px;
	margin-top: 14px;
	color: #7f7f7f;
}

.total-values {
	line-height: 32px;
	color: #9d9d9d;
	font-weight: bold;
	margin-right: 10px;
	font-size: 13px;

	.value {
		padding: 0px 3px;
	}
}

.status-cell {
	font-weight: 600;
	color: rgb(255, 0, 0);

	&.ativo {
		color: rgb(0, 128, 0);
	}
}

.filtros-atividade {
	margin-right: 10px;

	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}
}

::ng-deep pacto-log.primary {
	margin-right: -20px;
	height: 40px;

	.btn {
		height: 40px;
		color: $azul;
		padding: 4px 10px;
		margin-right: 8px;
		background: $branco;
		border: 1px solid $azulimPri;
	}

	.btn:hover {
		color: $azul;
		background: $branco;
		border: 1px solid $azul;
	}

	.btn.btn-primary:focus,
	.show > .btn-primary.dropdown-toggle {
		color: $azul;
		background: $branco;
		box-shadow: 0 0 0 -0.2rem rgb(3, 127, 226);
	}

	.btn-primary:focus,
	.btn-primary.focus {
		box-shadow: 0 0 0 0.2rem rgba(3, 127, 226, 0.5);
	}
}
