import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";

import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "../../../../base/base-core/rest/rest.model";
import { ModalService } from "../../../../base/base-core/modal/modal.service";
import {
	DataFiltro,
	FilterComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridColumnConfig,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import {
	TreinoApiAtividadeService,
	TreinoApiAparelhoService,
	TreinoApiGrupoMuscularService,
	TreinoApiNivelService,
	Nivel,
	TreinoApiEmpresaService,
	PerfilAcessoRecursoNome,
	AtividadeBase,
} from "treino-api";

import { Observable } from "rxjs";
import { debounceTime, map } from "rxjs/operators";

import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { AdmRestService } from "../../../../../../projects/adm/src/app/adm-rest.service";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";

const STORAGE_KEY = "atividade_lista_estado";

@Component({
	selector: "pacto-atividade-lista",
	templateUrl: "./atividade-lista.component.html",
	styleUrls: ["./atividade-lista.component.scss"],
})
export class AtividadeListaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: false }) removeModalTitle;
	@ViewChild("removeModalBody", { static: false }) removeModalBody;
	@ViewChild("removeModalMsg", { static: false }) removeModalMsg;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("tipoColumnName", { static: true }) tipoColumnName;
	@ViewChild("imagemColumnName", { static: true }) imagemColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("nivelColumnName", { static: false }) nivelColumnName;
	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("treinoia", { static: true }) iaColumnName;
	@ViewChild("nivelCelula", { static: false }) nivelCelula;
	@ViewChild("emailCelula", { static: false }) emailCelula;
	@ViewChild("imageCelula", { static: true }) imageCelula;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipRemover", { static: false }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("tipoTranslator", { static: true }) tipoTranslator;
	@ViewChild("situacaoTranslator", { static: true }) situacaoTranslator;
	@ViewChild("iaTranslator", { static: true }) iaTranslator;
	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarModalMsg", { static: true }) ativarModalMsg;
	@ViewChild("inativar", { static: false }) inativar;
	@ViewChild("ativar", { static: false }) ativar;
	@ViewChild("grupoMuscularesColumnName", { static: true })
	grupoMuscularColumnName;
	@ViewChild("aparelhosColumnName", { static: true }) aparelhosColumnName;
	@ViewChild("empresasColumnName", { static: true }) empresasColumnName;
	@ViewChild("empresaHabilitadasColumnName", { static: true })
	empresaHabilitadasColumnName;
	@ViewChild("empresaHabilitadas", { static: true }) empresaHabilitadas;
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;

	@ViewChild("filtroButton", { static: false }) filtroButton: FilterComponent;
	private temporaryFilters;
	private filtro;
	private statusAtividade;
	endpointUrlShare = this.rest.buildFullUrl("atividades/prescricao");
	baseFilter: DataFiltro = {};
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	filterConfig: GridFilterConfig;
	ngbPage = 1;
	pageSizeControl: FormControl = new FormControl();
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];
	ready = false;
	nomeAtividade;
	permissoesAtividades;
	podeExcluir = true;
	grupoMuscularList;
	aparelhosList;
	empresasList;
	searchControl = new FormControl();
	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
		ativo: new FormControl(false),
	});

	constructor(
		private router: Router,
		private rest: RestService,
		private snotifyService: SnotifyService,
		private atividadeService: TreinoApiAtividadeService,
		private modalService: ModalService,
		private nivelService: TreinoApiNivelService,
		private session: SessionService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private aparelhoService: TreinoApiAparelhoService,
		private empresaService: TreinoApiEmpresaService,
		private cd: ChangeDetectorRef,
		public admRestService: AdmRestService
	) {}

	listaAtividades: Array<AtividadeBase> = [];
	table: PactoDataGridConfig;
	niveis: ApiResponseList<Nivel> = {
		content: [],
	};

	ngOnInit() {
		const estadoSalvo = this.getSavedState();
		if (estadoSalvo) {
			this.restauraEstado(estadoSalvo);
			this.snotifyService.info(
				"Para melhorar sua experiência, mantivemos os filtros da última pesquisa."
			);
		}
		this.enviaEstadoLista();
		this.loadAllow();
		// this.configTable();
		this.configFilters();
		this.ready = true;
		this.getEmpresas().subscribe(() => {
			this.configFilters();
			this.ready = true;
		});
		this.initAll();
		this.listarAtividadePrescricao();
	}

	loadAllow() {
		this.permissoesAtividades = this.session.recursos.get(
			PerfilAcessoRecursoNome.ATIVIDADES
		);

		if (
			this.permissoesAtividades &&
			Array.isArray(this.permissoesAtividades.permissao)
		) {
			this.podeExcluir =
				this.permissoesAtividades.permissao.includes("EXCLUIR") ||
				this.permissoesAtividades.permissao.includes("TOTAL");
		} else {
			this.podeExcluir = false;
		}
	}

	btnClickHandler() {
		if (!this.permissoesAtividades || !this.permissoesAtividades.permissao) {
			this.snotifyService.warning(
				"Erro ao carregar permissões. Tente novamente."
			);
			return;
		}
		if (
			this.permissoesAtividades.permissao.includes("INCLUIR") ||
			this.permissoesAtividades.permissao.includes("TOTAL") ||
			this.permissoesAtividades.permissao.includes("TOTAL_EXCETO_EXCLUIR") ||
			this.permissoesAtividades.permissao.includes("INCLUIR_CONSULTAR")
		) {
			this.router.navigate(["treino", "cadastros", "atividades", "adicionar"]);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão para cadastrar Atividades, procure seu administrador."
			);
		}
	}

	btnEditHandler(atividade) {
		if (!this.permissoesAtividades || !this.permissoesAtividades.permissao) {
			this.snotifyService.warning(
				"Erro ao carregar permissões. Tente novamente."
			);
			return;
		}

		if (
			this.permissoesAtividades.permissao.includes("EDITAR") ||
			this.permissoesAtividades.permissao.includes("TOTAL") ||
			this.permissoesAtividades.permissao.includes("TOTAL_EXCETO_EXCLUIR") ||
			this.permissoesAtividades.permissao.includes("INCLUIR_CONSULTAR")
		) {
			this.router.navigate(["treino", "cadastros", "atividades", atividade.id]);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão para editar Atividades, procure seu administrador."
			);
		}
	}

	editarSituacaoHandler(item) {
		this.nomeAtividade = item.nome;
		setTimeout(() => {
			let modalTitle = "";
			let modalBody = "";
			let modalButton = "";
			let modalMsg = "";
			if (item.ativa === true) {
				modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
				modalBody = this.inativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipInativar.nativeElement.innerHTML;
				modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
			} else {
				modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
				modalBody = this.ativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipAtivar.nativeElement.innerHTML;
				modalMsg = this.ativarModalMsg.nativeElement.innerHTML;
			}
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				modalButton
			);
			handler.result.then(() => {
				const atividade = { ativa: item.ativa };
				item.ativa = this.statusAtividade;
				this.atividadeService
					.editarSituacaoAluno(item.id, atividade)
					.subscribe(() => {
						this.snotifyService.success(modalMsg);

						setTimeout(() => {
							this.listarAtividadePrescricao();
							this.listarAtividadePrescricao();
							this.cd.detectChanges();
						}, 1000);
						this.tableData.reloadData();
					});
			});
		});
	}

	getImagemAtividade(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	private configFilters() {
		this.filterConfig = {
			filters: this.addFilter(),
		};
	}

	private addFilter() {
		const result = [];
		result.push({
			name: "tiposEnuns",
			label: "Tipo",
			type: GridFilterType.DS3_SELECT_MANY,
			// translator: this.tipoTranslator,
			options: [
				{ value: "ANAEROBICO", label: "Neuromuscular" },
				{ value: "AEROBICO", label: "Cardiovascular" },
			],
		});
		result.push({
			name: "situacaoAtividade",
			label: "Status",
			type: GridFilterType.MANY,
			translator: this.situacaoTranslator,
			options: [{ value: "true" }, { value: "false" }],
			initialValue: ["true"],
		});
		result.push({
			name: "atividades",
			label: "Atividades",
			type: GridFilterType.MANY,
			showInputType: true,
			options: [
				{ value: "IA", label: "Atividades da IA" },
				{ value: "CONVENCIONAL", label: "Atividades Convencionais" },
			],
		});
		result.push({
			name: "empresas",
			label: "Empresas",
			// label: this.empresaHabilitadasColumnName,
			type: GridFilterType.DS3_SELECT_MANY,
			options: this.empresasList,
		});
		result.push({
			name: "ordenacao",
			label: "Ordenação",
			type: GridFilterType.MANY,
			options: [
				{ value: "asc", label: "A|Z" },
				{ value: "desc", label: "Z|A" },
			],
		});
		return result;
	}

	private getGruposMusculares(): Observable<any> {
		const gruposMusculares$ = this.grupoMuscularService
			.obterTodosGruposMusculares()
			.pipe(
				map((data) => {
					data
						.sort((a, b) => (a.nome > b.nome ? 1 : -1))
						.forEach((grupoMuscular: any) => {
							grupoMuscular.value = grupoMuscular.id;
							grupoMuscular.label = grupoMuscular.nome.toUpperCase();
						});
					this.grupoMuscularList = data;
					return true;
				})
			);
		return gruposMusculares$;
	}

	private getEmpresas(): Observable<any> {
		const empresas$ = this.empresaService.obterTodasEmpresas().pipe(
			map((data) => {
				data
					.sort((a, b) => (a.nome > b.nome ? 1 : -1))
					.forEach((empresa: any) => {
						empresa.value = empresa.id;
						empresa.label = empresa.nome.toUpperCase();
					});
				this.empresasList = data;
				return true;
			})
		);
		return empresas$;
	}

	private getAparelhos(): Observable<any> {
		const aparelhos$ = this.aparelhoService.obterTodosAparelhos("false").pipe(
			map((data) => {
				data
					.sort((a, b) => (a.nome > b.nome ? 1 : -1))
					.forEach((aparelho: any) => {
						aparelho.value = aparelho.id;
						aparelho.label = aparelho.nome;
					});
				this.aparelhosList = data;
				return true;
			})
		);
		return aparelhos$;
	}

	private getNiveis(): Observable<any> {
		const niveis$ = this.nivelService.obterNiveis().pipe(
			map((dados) => {
				dados.content.forEach((nivel: any) => {
					nivel.value = nivel.id;
					nivel.label = nivel.nome;
				});
				this.niveis = dados;
				return true;
			})
		);
		return niveis$;
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "situacao") {
			this.editarSituacaoHandler($event.row);
		}
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
	}

	initAll() {
		this.pageSizeControl.setValue(5);
		this.data.size = 5;
		this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
			this.filtrar();
		});
	}

	filtrar() {
		this.ngbPage = 1;
		this.listarAtividadePrescricao();
	}

	listarAtividadePrescricao() {
		this.data.size = this.pageSizeControl.value;
		this.data.page = this.ngbPage;
		this.data.page = this.ngbPage - 1;

		if (this.filtro !== undefined) {
			this.filtro.quicksearchValue = this.searchControl.value;
		} else {
			this.filtro = {
				quicksearchValue: this.searchControl.value,
				quicksearchFields: ["nome"],
				ordenacao: "asc",
				situacaoAtividade: ["true"],
				atividades: "CONVENCIONAL",
			};
		}

		this.atividadeService
			.obterTodasAtividadesPrescricao(this.data, this.filtro)
			.subscribe((data) => {
				this.listaAtividades = data.content;
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.cd.detectChanges();
			});
	}

	pageChangeHandler(page) {
		if (page) {
			this.ngbPage = page;
			this.listarAtividadePrescricao();
		}
	}

	getImagemAluno(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	habilitaDesabilita(item, status) {
		if (status) {
			this.statusAtividade = true;
		} else {
			this.statusAtividade = false;
		}
		this.editarSituacaoHandler(item);
		this.cd.detectChanges();
	}

	obterColunasRelatorio(exportar?: boolean): Array<PactoDataGridColumnConfig> {
		const colunaNome: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			nome: "Nome",
			campo: "nome",
			titulo: "Nome",
			ordenavel: true,
			visible: true,
			width: "20%",
		};

		const colunaTipo: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			nome: "Tipo",
			campo: "tipo",
			titulo: "Tipo",
			ordenavel: true,
			visible: true,
			width: "20%",
		};
		const colunaSituacao: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			nome: "Status",
			campo: "ativa",
			titulo: "Status",
			ordenavel: true,
			visible: true,
			width: "20%",
		};

		const colunasRelatorio: Array<PactoDataGridColumnConfig> = [];
		colunasRelatorio.push(colunaNome);
		colunasRelatorio.push(colunaTipo);
		colunasRelatorio.push(colunaSituacao);
		return colunasRelatorio;
	}

	getFiltersShare(): DataFiltro {
		this.baseFilter.filters = this.filtro;
		this.baseFilter.configs = {};
		this.baseFilter.filters.share = "true";
		return this.baseFilter;
	}

	filterHandler(filter) {
		this.temporaryFilters = filter;
		this.filterDropdown.close();

		const filtros = {
			quicksearchValue: this.searchControl.value,
			tiposEnuns: this.temporaryFilters.filters.tiposEnuns,
			situacaoAtividade:
				this.temporaryFilters.filters.situacaoAtividade.length > 0
					? this.temporaryFilters.filters.situacaoAtividade
					: ["true"],
			atividades: this.temporaryFilters.filters.atividades,
			ordenacao:
				this.temporaryFilters.filters.ordenacao.length === 1
					? this.temporaryFilters.filters.ordenacao[0]
					: "asc",
		};

		this.filtro = filtros;
		this.data.size = this.pageSizeControl.value;
		this.data.page = this.ngbPage - 1;

		this.atividadeService
			.obterTodasAtividadesPrescricao(this.data, this.filtro)
			.subscribe((data) => {
				this.listaAtividades = data.content;
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.cd.detectChanges();
			});
		this.filtroButton.close();
	}

	fetchFiltros(): DataFiltro {
		return {
			filters: {
				quicksearchValue: this.searchControl.value,
				quicksearchFields: ["ativa"],
			},
			configs: {},
		};
	}

	get urlLog(): string {
		return this.rest.buildFullUrl("log/atividades");
	}

	private salvaEstado() {
		const state = {
			searchValue: this.searchControl.value,
			filters: this.filtro || {},
			page: this.ngbPage,
		};
		localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
	}

	private getSavedState(): {
		searchValue: string;
		filters: any;
		page: number;
	} | null {
		const estadoSalvo = localStorage.getItem(STORAGE_KEY);
		return estadoSalvo ? JSON.parse(estadoSalvo) : null;
	}

	private restauraEstado(estadoSalvo: {
		searchValue: string;
		filters: any;
		page: number;
	}) {
		this.searchControl.setValue(estadoSalvo.searchValue);
		this.filtro = estadoSalvo.filters;
		this.ngbPage = estadoSalvo.page || 1;
		this.listarAtividadePrescricao();
	}

	private enviaEstadoLista() {
		this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
			this.salvaEstado();
		});

		this.filterHandler = (filter) => {
			this.temporaryFilters = filter;
			this.filterDropdown.close();

			const tipo = this.temporaryFilters.filters.tiposEnuns;
			const atividades = this.temporaryFilters.filters.atividades;
			let situacao = ["true"];
			if (this.temporaryFilters.filters.situacaoAtividade.length > 0) {
				situacao = this.temporaryFilters.filters.situacaoAtividade;
			}
			let ordenacao = "asc";
			if (this.temporaryFilters.filters.ordenacao.length === 1) {
				ordenacao = this.temporaryFilters.filters.ordenacao[0];
			}

			const filtros = {
				quicksearchValue: this.searchControl.value,
				tiposEnuns: tipo,
				situacaoAtividade: situacao,
				ordenacao: ordenacao,
				atividades: atividades,
				quicksearchFields: [
					"codigo",
					"nome",
					"tipo",
					"situacaoAtividade",
					"empresa",
					"atividades",
				],
			};
			this.filtro = filtros;
			this.data.size = this.pageSizeControl.value;
			this.data.page = this.ngbPage - 1;

			this.atividadeService
				.obterTodasAtividadesPrescricao(this.data, this.filtro)
				.subscribe((data) => {
					this.listaAtividades = data.content;
					this.data.content = data.content.length;
					this.data.totalElements = data.totalElements;
					this.cd.detectChanges();
					this.salvaEstado();
				});
			this.filtroButton.close();
		};
	}
}
