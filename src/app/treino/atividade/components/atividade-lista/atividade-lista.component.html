<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'ATIVIDADE'
		}"
		class="first"></pacto-breadcrumbs>

	<div class="table-wrapper pacto-shadow">
		<div>
			<div class="container-prescricao">
				<div class="filtros">
					<div class="search">
						<div>
							<input
								#quickSearch
								[formControl]="searchControl"
								class="form-control"
								id="input-busca-rapida"
								placeholder="Busca rápida..."
								type="text" />
							<i class="pct pct-search"></i>
						</div>
					</div>
					<div class="filtro1"></div>
					<div></div>
					<div class="compartilhar">
						<pacto-log [url]="urlLog" class="primary"></pacto-log>
						<pacto-share-button
							[columns]="obterColunasRelatorio(true)"
							[endpoint]="
								admRestService.buildFullUrlTreino('atividades/prescricao')
							"
							[filtros]="getFiltersShare()"
							id="btn-shared"></pacto-share-button>
						<div
							#filterDropdown="ngbDropdown"
							class="filter filtros-atividade d-inline-block"
							ngbDropdown>
							<pacto-filter
								#filtroButton
								(filterChange)="filterHandler($event)"
								[filterConfig]="filterConfig"></pacto-filter>
						</div>
						<button (click)="btnClickHandler()" class="btn btn-primary">
							<i class="pct pct-plus"></i>
							Adicionar
						</button>
					</div>
				</div>
				<div
					*ngFor="let item of listaAtividades; let index = index"
					class="pessoa-prescricao">
					<div class="grid-prescricao">
						<div class="dados-pessoais">
							<div class="column-foto">
								<div
									[ngStyle]="{
										'background-image': getImagemAluno(
											item.images[0] === undefined
												? ''
												: item.images[0].fotoKeyPequena
										)
									}"
									class="foto-prescricao"></div>
							</div>

							<div class="column-dados">
								<span class="atividade">{{ item.nome }}</span>
								<span class="nome">Tipo: {{ item.tipo }}</span>
								<span class="matricula">{{ item.id }}</span>
							</div>
						</div>

						<div class="coluna-prescricao">
							<div class="column-dados">
								<span class="titulo">Empresa</span>
								<div
									*ngIf="!item.empresas || !item.empresas.length"
									class="empresa"
									i18n="@@perfil-aluno:nenhum-registro">
									Todas
								</div>
								<span
									*ngFor="
										let atvempresa of item.empresas;
										let index = index;
										last as isLast
									"
									class="empresa">
									{{ atvempresa.empresa.nome }}
									<span *ngIf="!isLast">|</span>
								</span>
							</div>
						</div>

						<div class="coluna-prescricao">
							<div class="column-dados">
								<span class="titulo">Status</span>
								<span>
									<div
										class="situacaoAtividade {{
											item.ativa ? 'ATIVA' : 'INATIVA'
										}}">
										{{
											notificacoesTranslate.getLabel(
												item.ativa ? "ATIVA" : "INATIVA"
											)
										}}
									</div>
								</span>
							</div>
						</div>

						<div class="coluna-botao">
							<button
								(click)="habilitaDesabilita(item, true)"
								*ngIf="!item.ativa"
								class="btn btn-secundary">
								<i class="pct pct-check"></i>
								Ativar
							</button>
							<button
								(click)="habilitaDesabilita(item, false)"
								*ngIf="item.ativa && podeExcluir"
								class="btn btn-secundary">
								<i class="pct pct-eye-off"></i>
								Inativar
							</button>
							<button (click)="btnEditHandler(item)" class="btn btn-primary">
								<i class="pct pct-edit"></i>
								Editar
							</button>
						</div>
					</div>
				</div>
			</div>

			<div class="footer-row">
				<ng-container>
					<div class="div-pagination">
						<ngb-pagination
							(pageChange)="pageChangeHandler($event)"
							[(page)]="ngbPage"
							[boundaryLinks]="true"
							[collectionSize]="data.totalElements"
							[ellipses]="false"
							[maxSize]="7"
							[pageSize]="data.size"
							[size]="'sm'"
							class="d-flex justify-content-end"></ngb-pagination>
					</div>

					<div class="div-show-and-select">
						<div class="div-select-qt-show">
							<pacto-cat-select
								(change)="listarAtividadePrescricao()"
								[control]="pageSizeControl"
								[items]="itensPerPage"
								[size]="'SMALL'"></pacto-cat-select>
						</div>

						<div class="total-values">
							<span i18n="@@component-relatorio:mostrando">Mostrando</span>
							<span class="value">{{ data.content }}</span>
							<span i18n="@@component-relatorio:de">de</span>
							<span class="value">{{ data.totalElements }}</span>
						</div>
					</div>
				</ng-container>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud-atividade:title">Cadastro de Atividades</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-alunos:alunos-cadastrados:description">
		Gerencie as atividades da sua academia.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Nome</span>
</ng-template>
<ng-template #tipoColumnName>
	<span i18n="@@crud-atividades:table:tipo">Tipo</span>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@crud-atividades:table:status">Situação</span>
</ng-template>
<ng-template #treinoia>
	<span i18n="@@crud-atividades:table:treinoia">Atividades da IA</span>
</ng-template>
<ng-template #empresaHabilitadasColumnName>
	<span>Empresa</span>
</ng-template>
<ng-template #empresaHabilitadas let-item="item">
	<div
		*ngIf="!item.empresas || !item.empresas.length"
		class="empty-list"
		i18n="@@perfil-aluno:nenhum-registro">
		Todas
	</div>
	<span
		*ngFor="let atvempresa of item.empresas; let index = index; last as isLast">
		{{ atvempresa.empresa.nome }}
		<span *ngIf="!isLast">|</span>
	</span>
</ng-template>
<ng-template #imagemColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Foto</span>
</ng-template>
<ng-template #grupoMuscularesColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Grupo Musculares</span>
</ng-template>
<ng-template #aparelhosColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Aparelhos</span>
</ng-template>
<ng-template #empresasColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Empresas</span>
</ng-template>

<!--end result coluna situacao-->
<ng-template #statusColumn let-item="item">
	<span *ngIf="item.ativa == true" i18n="@@crud-alunos:ativo:result">
		Ativo
	</span>
	<span *ngIf="item.ativa == false" i18n="@@crud-alunos:inativo:result">
		Inativo
	</span>
</ng-template>

<ng-template #imageCelula let-item="item">
	<div
		[ngStyle]="{
			'background-image': getImagemAtividade(
				item.images.length ? item.images[0].uri : 'assets/images/icon-peso.svg'
			),
			'background-size': item.images.length ? 'cover' : 'auto'
		}"
		class="preview-object"></div>
</ng-template>

<!--SITUACAO TRANSLATOR-->
<ng-template #tipoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'ANAEROBICO'" i18n="@@crud-alunos:ativo:result">
			Neuromuscular
		</span>
		<span *ngSwitchCase="'AEROBICO'" i18n="@@crud-alunos:inativo:result">
			Cardiovascular
		</span>
	</ng-container>
</ng-template>
<!--END SITUACAO TRANSLATOR-->
<!--SITUACAO TRANSLATOR 2-->
<ng-template #situacaoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'true'" i18n="@@crud-alunos:ativo:result">Ativa</span>
		<span *ngSwitchCase="'false'" i18n="@@crud-alunos:inativo:result">
			Inativa
		</span>
	</ng-container>
</ng-template>
<!--END SITUACAO TRANSLATOR-->
<!--IA TRANSLATOR 2-->
<ng-template #iaTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span *ngSwitchCase="'todas'" i18n="@@crud-alunos:ativo:result">
			Todas as atividades da IA
		</span>
		<span *ngSwitchCase="'editadas'" i18n="@@crud-alunos:inativo:result">
			Editadas
		</span>
		<span *ngSwitchCase="'naoeditadas'" i18n="@@crud-alunos:inativo:result">
			Não editadas
		</span>
	</ng-container>
</ng-template>
<!--IA TRANSLATOR-->

<!--tooltip icons-->
<span #tooltipAtivar [hidden]="true" i18n="@@crud-alunos:ativar:tooltip-icon">
	Ativar
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
<!--end tooltip icons-->

<span #inativarModalBody [hidden]="true">
	Deseja inativar a atividade: {{ nomeAtividade }} ?
</span>
<span #ativarModalBody [hidden]="true">
	Deseja ativar a atividade: {{ nomeAtividade }} ?
</span>

<span #inativarModalTitle [hidden]="true">Inativar Atividade ?</span>
<span #inativarModalMsg [hidden]="true">Atividade inativada com sucesso.</span>
<span #ativarModalTitle [hidden]="true">Ativar Atividade ?</span>
<span #ativarModalMsg [hidden]="true">Atividade ativada com sucesso.</span>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="ATIVA">Ativo</span>
	<span xingling="INATIVA">Inativo</span>
</pacto-traducoes-xingling>
