import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { AtividadeConfigEmpresa } from "treino-api";

@Component({
	selector: "pacto-atividade-empresas",
	templateUrl: "./atividade-empresas.component.html",
	styleUrls: ["./atividade-empresas.component.scss"],
})
export class AtividadeEmpresasComponent implements OnInit {
	@Input() empresas: Array<AtividadeConfigEmpresa>;
	@Output() criarNovo: EventEmitter<boolean> = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	adicionar() {
		this.criarNovo.emit(true);
	}

	removeHandler(index) {
		this.empresas.splice(index, 1);
	}
}
