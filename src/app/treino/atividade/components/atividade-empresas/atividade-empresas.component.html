<div class="comp-wrapper">
	<div class="header-row">
		<div class="title" i18n="@@crud-atividade:restringir-atividade:title">
			Restringir atividade por empresa
		</div>
		<div
			(click)="adicionar()"
			class="action"
			i18n="@@crud-atividade:empresas:adicionar">
			Adicionar
		</div>
	</div>

	<div class="list-wrapper">
		<div
			[maxHeight]="'200px'"
			[ngClass]="{ empty: !empresas || empresas.length === 0 }"
			pactoCatSmoothScroll>
			<table *ngIf="empresas && empresas.length !== 0" class="table">
				<tbody>
					<tr *ngFor="let empresa of empresas; let index = index">
						<td>{{ empresa.identificador ? empresa.identificador : "-" }}</td>
						<td>{{ empresa.empresa.nome }}</td>
						<td (click)="removeHandler(index)" class="action-column">
							<i class="fa fa-trash-o"></i>
						</td>
					</tr>
				</tbody>
			</table>
			<div
				*ngIf="!empresas || empresas.length === 0"
				class="empty-state"
				i18n="@@crud-atividade:empresas:vazio">
				Para restringir essa atividade para algumas empresas usa essa
				configuração.
			</div>
		</div>
	</div>
</div>
