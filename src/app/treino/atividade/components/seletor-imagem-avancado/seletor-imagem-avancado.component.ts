import { Component, OnInit, Input, ChangeDetectorRef } from "@angular/core";
import { ImagemCatalogo } from "treino-api";
import { NgbActiveModal, NgbTabChangeEvent } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-seletor-imagem-avancado",
	templateUrl: "./seletor-imagem-avancado.component.html",
	styleUrls: ["./seletor-imagem-avancado.component.scss"],
})
export class SeletorImagemAvancadoComponent implements OnInit {
	constructor(private modal: NgbActiveModal, private cd: ChangeDetectorRef) {}

	@Input() id;
	filterControl: FormControl = new FormControl();
	imagens: Array<ImagemCatalogo> = [];
	selectedImage: ImagemCatalogo = {
		id: "-1",
		nome: "",
		uri: "",
		professor: false,
	};
	currentTab = "catalogo";
	apresentarCatalogo = true;
	apresentarEnviarImagem = true;
	sugerirUtilizarCampoVideo = false;
	mensagemSugerida = "";
	isCatalogoIcones: boolean = false;
	titulo = "Selecionar Imagem";

	formGroup: FormGroup = new FormGroup({
		professor: new FormControl(false),
	});

	ngOnInit() {
		if (this.isCatalogoIcones) {
			this.titulo = "Selecionar Ícone";
			this.cd.detectChanges();
		}
	}

	selecionarImagem(index) {
		this.selectedImage = this.imagensFiltradas[index];
	}

	get imagensFiltradas() {
		const filterValue = this.filterControl.value;
		const valueString = filterValue ? filterValue.trim() : "";
		const filter = new RegExp(valueString, "ig");
		return this.imagens.filter((item) => {
			return filter.test(item.nome);
		});
	}

	get selected() {
		return this.selectedImage && this.selectedImage.id !== "-1";
	}

	tabChangeHandler($event: NgbTabChangeEvent) {
		this.currentTab = $event.nextId;
	}

	dismiss() {
		this.modal.dismiss();
	}

	selectImage() {
		if (this.selected) {
			this.selectedImage.professor = this.formGroup.get("professor").value;
			this.modal.close({
				type: "CATALOG",
				uri: this.selectedImage.uri,
				id: this.selectedImage.id,
				nome: this.selectedImage.nome,
				professor: this.formGroup.get("professor").value,
			});
		}
	}

	handleFiles(event) {
		const input = event.target;
		const files: FileList = input.files;
		this.modal.close({
			type: "UPLOAD",
			data: files[0],
			nome: files[0].name,
			professor: this.formGroup.get("professor").value,
		});
	}
}
