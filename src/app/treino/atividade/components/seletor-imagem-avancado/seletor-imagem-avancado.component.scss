@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";

.seletor-avancado-wrapper {
	background-color: #ffffff;

	.catalog-wrapper {
		.list-wrapper {
			display: flex;
			flex-wrap: wrap;
			padding-top: 20px;

			.image-card {
				width: 105px;
				margin: 10px 10px 10px;
				cursor: pointer;

				.image-wrapper {
					width: 90px;
					height: 90px;
					background-color: #ffffff;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-wrap: wrap;
					border: 2px dashed #ececec;

					&.selected {
						border: 2px solid primaryColor(pacto, base);
					}

					img {
						max-width: 85px;
						max-height: 85px;
						margin: auto;
					}
				}

				.nome {
					text-align: center;
					width: 100%;
					font-size: 11px;
				}

				.icone-catalogo {
					font-size: 25px;
				}
			}
		}
	}

	.no-images {
		display: flex;
		width: 100%;
		height: 300px;
		justify-content: center;
		align-items: center;
		background-color: #f5f5f5;
		box-shadow: inset 0px 0px 10px #d6d6d6;
		margin-top: 5px;
	}

	.search {
		margin-top: 30px;
	}

	.search input {
		width: 315px;
		padding-left: 30px;
	}

	.search i.pct {
		position: absolute;
		left: 10px;
		top: 12px;
	}

	.uploader-wrapper {
		display: flex;
		height: 300px;
		justify-content: center;
		align-items: center;
		background-color: #f5f5f5;
		box-shadow: inset 0px 0px 10px #d6d6d6;
		margin-top: 5px;

		.inputfile {
			width: 0.1px;
			height: 0.1px;
			opacity: 0;
			overflow: hidden;
			position: absolute;
			z-index: -1;
		}

		label {
			display: block;
			width: 170px;
			text-align: center;
			font-weight: 600;
			font-size: 16px;
			padding: 10px;
			color: colors(color-white);
			margin-top: 32px;
			cursor: pointer;
			background-color: primaryColor(pacto, base);
		}
	}
}

.toast-beta {
	background-color: #fdfdb4;
	border-radius: 5px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin: 16px 0px 16px 0px;
	padding: 8px;

	.toast-text {
		color: #8f8f03;
		display: flex;

		span {
			.icone {
				font-size: 18px;
				padding: 8px;
			}

			strong {
				//styleName: Button/Default/2;
				font-family: Poppins;
				font-size: 12px;
				font-weight: 600;
				line-height: 12px;
				letter-spacing: 0.25px;
				text-align: left;
			}

			//styleName: Overline/2;
			font-family: Nunito Sans;
			font-size: 14px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
		}
	}
}
