<div class="seletor-avancado-wrapper">
	<div class="modal-header">
		<h4 class="modal-title" i18n="@@seletor-imagem-avancado:title">
			{{ titulo }}
		</h4>
		<button
			(click)="dismiss()"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<ngb-tabset #tabset (tabChange)="tabChangeHandler($event)">
			<ngb-tab
				*ngIf="apresentarCatalogo"
				i18n-title="@@seletor-imagem-avancado:catalogo:title"
				id="catalogo"
				title="Catálogo">
				<ng-template ngbTabContent>
					<div class="catalog-wrapper">
						<div [maxHeight]="'calc(100vh - 265px)'" pactoCatSmoothScroll>
							<div *ngIf="!isCatalogoIcones" class="search">
								<div>
									<input
										[formControl]="filterControl"
										class="form-control"
										i18n-placeholder="
											@@seletor-imagem-avancado:enviar-imagem:pesquise:placeholder"
										id="input-busca-rapida"
										placeholder="Pesquise pelo nome da imagem"
										type="text" />
									<i class="pct pct-search"></i>
								</div>
							</div>

							<div class="list-wrapper">
								<div
									(click)="selecionarImagem(index)"
									*ngFor="let image of imagensFiltradas; let index = index"
									class="image-card">
									<div
										[ngClass]="{ selected: selectedImage.id === image.id }"
										class="image-wrapper">
										<img
											*ngIf="!isCatalogoIcones"
											id="{{ 'imagem-' + index }}"
											src="{{ image.uri }}" />

										<i
											*ngIf="isCatalogoIcones"
											class="icone-catalogo pct {{ image.uri }}"></i>
									</div>
									<div class="nome">{{ image.nome }}</div>
								</div>

								<div
									*ngIf="!imagensFiltradas || !imagensFiltradas.length"
									class="no-images">
									<div
										class="msg"
										i18n="@@seletor-imagem-avancado:nehuma-imagem">
										Nenhuma imagem
									</div>
								</div>
							</div>
						</div>
					</div>
				</ng-template>
			</ngb-tab>
			<ngb-tab
				*ngIf="apresentarEnviarImagem"
				i18n-title="@@seletor-imagem-avancado:enviar-imagem:title"
				id="upload"
				title="Enviar Imagem">
				<ng-template ngbTabContent>
					<div class="toast-beta">
						<div class="toast-text">
							<span>
								<i class="pct pct-alert-triangle icone"></i>
								Por favor, certifique-se de que a imagem que você está enviando
								não ultrapasse 1MB.
							</span>
						</div>
					</div>

					<div *ngIf="sugerirUtilizarCampoVideo" class="toast-beta">
						<div class="toast-text">
							<span>
								<i class="pct pct-info icone"></i>
								{{ mensagemSugerida }}
							</span>
						</div>
					</div>

					<div class="uploader-wrapper">
						<div class="btn-wrapper">
							<input
								#inputImagem
								(change)="handleFiles($event)"
								accept="image/"
								class="inputfile"
								id="envioImg"
								name="file"
								type="file" />
							<label for="envioImg" i18n="@@crud-benchmarks:escolha-imagem">
								Escolha uma imagem
							</label>
						</div>
					</div>
				</ng-template>
			</ngb-tab>
		</ngb-tabset>
	</div>
	<div class="modal-footer">
		<div
			*ngIf="!isCatalogoIcones"
			[ds3Tooltip]="tooltipProfessor"
			[tooltipIndicator]="'bottom-left'"
			[tooltipPosition]="'top'"
			style="text-align: left; width: 100%; padding-left: 30px">
			<input
				[formControl]="formGroup.get('professor')"
				class="form-check-input"
				id="checkProfessor"
				type="checkbox"
				value="" />
			<label
				class="form-check-label"
				for="checkProfessor"
				i18n="@@crud-atividade:ativa:label">
				Professor?
			</label>
		</div>
		<button
			(click)="dismiss()"
			class="btn btn-secondary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Cancelar
		</button>
		<button
			(click)="selectImage()"
			*ngIf="currentTab === 'catalogo'"
			[disabled]="!selected"
			class="btn btn-primary modal-item"
			i18n="@@buttons:selecionar-imagem"
			id="selectImagem"
			type="button">
			{{ titulo }}
		</button>
	</div>
</div>
<ng-template #tooltipProfessor>
	<div class="itens-tooltip">
		Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o
		professor
	</div>
</ng-template>
