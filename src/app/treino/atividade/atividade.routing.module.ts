import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

import { AtividadeListaComponent } from "./components/atividade-lista/atividade-lista.component";
import { AtividadeEditComponent } from "./components/atividade-edit/atividade-edit.component";
import { AtividadeEmpresaConfigComponent } from "src/app/treino/atividade/components/atividade-empresa-config/atividade-empresa-config.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { AtividadeListaIaComponent } from "./components/atividade-lista-ia/atividade-lista-ia.component";

const recurso = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.ATIVIDADES, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: AtividadeListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "ia/lista",
		component: AtividadeListaIaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "adicionar",
		component: AtividadeEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "ia/:id",
		component: AtividadeEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: AtividadeEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	entryComponents: [AtividadeEmpresaConfigComponent],
})
export class AtividadeRoutingModule {}
