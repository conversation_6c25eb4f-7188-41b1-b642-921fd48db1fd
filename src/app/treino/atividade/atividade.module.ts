import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AtividadeEditComponent } from "./components/atividade-edit/atividade-edit.component";
import { AtividadeEmpresaConfigComponent } from "./components/atividade-empresa-config/atividade-empresa-config.component";
import { AtividadeEmpresasComponent } from "./components/atividade-empresas/atividade-empresas.component";
import { AtividadeListaComponent } from "./components/atividade-lista/atividade-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AtividadeRoutingModule } from "./atividade.routing.module";
import { RouterModule } from "@angular/router";
import { AtividadeListaIaComponent } from "./components/atividade-lista-ia/atividade-lista-ia.component";
import { PerfilClienteModule } from "../../pessoas/perfil-cliente/perfil-cliente.module";

@NgModule({
	declarations: [
		AtividadeEditComponent,
		AtividadeEmpresaConfigComponent,
		AtividadeEmpresasComponent,
		AtividadeListaComponent,
		AtividadeListaIaComponent,
	],
	imports: [
		BaseSharedModule,
		AtividadeRoutingModule,
		RouterModule,
		CommonModule,
		PerfilClienteModule,
	],
})
export class AtividadeModule {}
