import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
	HostListener,
} from "@angular/core";
import {
	FichaPrograma,
	AlunoBase,
	Programa,
	TreinoApiAlunosService,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";
import { ConfigurarFichaService } from "../configurar-ficha.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalEdicaoProgramaComponent } from "../modal-edicao-programa/modal-edicao-programa.component";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoRecursoNome } from "treino-api";
import { TreinoConfigCacheService } from "../../../base/configuracoes/configuration.service";
import { Router } from "@angular/router";
import { ModalCriarFichaComponent } from "../modal-criar-ficha/modal-criar-ficha.component";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";
import { Location } from "@angular/common";

enum Acoes {
	TROCAR_ABA = "TROCAR_ABA",
	NOVA_FICHA = "NOVA_FICHA",
	FECHAR_MODAL = "FECHAR_MODAL",
}

@Component({
	selector: "pacto-titulo-modal-edicao-ficha",
	templateUrl: "./titulo-modal-edicao-ficha.component.html",
	styleUrls: ["./titulo-modal-edicao-ficha.component.scss"],
})
export class TituloModalEdicaoFichaComponent implements OnInit {
	@Input() programa: Programa;
	@Input() aluno: AlunoBase;
	@Output() tabFichaChangeHandler: EventEmitter<number> = new EventEmitter();
	@Output() abrirModalEditorFicha: EventEmitter<boolean> = new EventEmitter();
	@Output() novaFicha: EventEmitter<boolean> = new EventEmitter();
	@Output() excluirFicha: EventEmitter<boolean> = new EventEmitter();
	@Output() tornarFichaPredefinida: EventEmitter<boolean> = new EventEmitter();
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;

	fichas: Array<FichaPrograma> = [];
	fichaSelecionadaIndex: number;
	permissaoProgramaTreino;
	predefinido;
	clienteMensagemAviso: string;
	showMensagemAviso = false;
	configTreino: any;
	existeFicha = false;

	acoes = ["TROCAR_ABA", "NOVA_FICHA", "FECHAR_MODAL"];
	constructor(
		private notificationService: SnotifyService,
		private configurarFichaService: ConfigurarFichaService,
		private configurationService: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private alunoService: TreinoApiAlunosService,
		private ngbActiveModal: NgbActiveModal,
		private modalService: ModalService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private router: Router,
		private snotifyService: SnotifyService,
		private location: Location
	) {}

	ngOnInit() {
		this.carregarPermissoes();
		this.loadData();
		this.configurarFichaService.fichaSelecionadaIndex$.subscribe((index) => {
			this.fichaSelecionadaIndex = index;
			this.cd.detectChanges();
		});
		this.getClienteMensagemAviso();
		this.configTreinoService
			.getConfiguracoesTreino()
			.subscribe((configTreino) => {
				this.configTreino = configTreino;
				this.cd.detectChanges();
			});
	}

	confirmarAcao(acao, index?) {
		if (this.configurarFichaService.fichaSendoModificada$.value) {
			const modal = this.modalService.confirm(
				"Ficha sendo modificada",
				"Existem informações que não foram salvas. Deseja continuar?",
				"CONTINUAR"
			);
			modal.result.then(() => {
				switch (acao) {
					case Acoes.TROCAR_ABA:
						this.tabChangeHandler(index);
						break;
					case Acoes.NOVA_FICHA:
						this.novaFicha.emit(true);
						break;
					case Acoes.FECHAR_MODAL:
						this.dismiss();
						break;
				}
			});
		} else {
			switch (acao) {
				case Acoes.TROCAR_ABA:
					this.tabChangeHandler(index);
					break;
				case Acoes.NOVA_FICHA:
					this.novaFicha.emit(true);
					break;
				case Acoes.FECHAR_MODAL:
					this.dismiss();
					break;
			}
		}
	}

	editarProgramaHandler() {
		let titleModal = "Editar programa";
		if (!this.permissaoProgramaTreino.editar) {
			titleModal = "Detalhes programa";
		}
		const modal = this.modalService.open(
			titleModal,
			ModalEdicaoProgramaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.loadForm(this.programa);
		modal.result.then((result: Programa) => {
			this.configurarFichaService.atualizarProgramaService(result);
			this.programa = result;
			this.cd.detectChanges();
		});
	}

	abrirModalAdicionarFicha() {
		const modal = this.modalService.open(
			"Criar ficha",
			ModalCriarFichaComponent
		);
		modal.result.then((result) => {
			this.configurarFichaService.adicionarFicha(result).subscribe(() => {
				this.cd.detectChanges();
				this.snotifyService.success(
					this.notificacoesTranslate.getLabel("ficha-create-success")
				);
				this.verificarFichaExiste();
			});
		});
	}

	verificarFichaExiste() {
		const verificarFicha = this.configurarFichaService.obterFichaAtual();
		this.existeFicha = verificarFicha === null ? false : true;
		this.cd.detectChanges();
	}

	private tabChangeHandler(index) {
		this.configurarFichaService.selecionarFicha(index);
		this.cd.detectChanges();
	}

	private dismiss() {
		this.configurarFichaService.selecaoFichaIniciada = false;
		this.configurarFichaService.fichaSendoModificada$.next(false);
		this.ngbActiveModal.dismiss(this.configurarFichaService.programa$.value);
	}

	private loadData() {
		this.configurarFichaService.programa$.subscribe((value: Programa) => {
			this.fichas = value.fichas;
			this.cd.detectChanges();
		});
		this.predefinido = this.programa.predefinido;
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
	}

	getTextoLabelEditarOuVisualizar() {
		return this.permissaoProgramaTreino && this.permissaoProgramaTreino.editar
			? "Editar Programa"
			: "Detalhes programa";
	}

	getClienteMensagemAviso() {
		if ((this.aluno && this.aluno.id) || this.programa.alunoId != null) {
			let alunoId;
			if (this.aluno !== undefined) {
				alunoId = this.aluno.id;
			} else {
				alunoId = this.programa.alunoId;
			}
			this.alunoService
				.obterClienteMensagem(alunoId, "AM")
				.subscribe((result) => {
					this.clienteMensagemAviso = result.toString().replace(/&nbsp;/g, " ");
					if (this.clienteMensagemAviso !== "") {
						this.validarConfigVisualizarMensagemAviso();
					}
					this.cd.detectChanges();
				});
		}
	}

	validarConfigVisualizarMensagemAviso() {
		if (
			this.configurationService &&
			this.configurationService.configuracoesTreino &&
			this.configurationService.configuracoesTreino.visualizar_mensagem_aviso
		) {
			this.showMensagemAviso = true;
		} else {
			this.showMensagemAviso = false;
		}
	}

	backList() {
		this.location.back();
	}

	@HostListener("window:popstate")
	back() {
		if (this.configurarFichaService.programa$.value.id == null) {
			if (this.configurarFichaService.atividadesFichaView$.value.length < 1) {
				this.notificationService.warning(
					"A ficha predefinida deve ter pelo menos 1 atividade adicionada."
				);
			}
		} else {
			if (this.configurarFichaService.atividadesFichaView$.value.length < 1) {
				history.forward();
				this.notificationService.warning(
					"O Programa predefinido deve ter pelo menos 1 atividade adicionada."
				);
			}
		}
	}
}
