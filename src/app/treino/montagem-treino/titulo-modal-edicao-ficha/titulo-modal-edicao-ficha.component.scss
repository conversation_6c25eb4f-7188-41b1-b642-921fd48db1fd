@import "src/assets/scss/pacto/plataforma-import.scss";

.view-header {
	border-bottom: 1px solid $cinza02;
	display: block;
	justify-content: center;
	padding-top: 25px;
	margin-left: auto;
	margin-right: auto;

	.header {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
}
.center-aux {
	width: calc(100vw - 460px);
	max-width: 1230px;
}
.info-programa {
	margin: 7px 0px 0px 15px;

	.titulo-modal {
		max-width: 200px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		@extend .type-h5;
		color: $pretoPri;
	}
	.titulo-msg {
		@extend .type-caption;
		word-break: break-word;
	}
}

.titulo-professor {
	overflow: hidden;
	text-overflow: ellipsis;
	.titulo-msg {
		@extend .type-caption;
	}
	.subtitulo-modal {
		color: $pretoPri;
		font-size: 15px;
	}
}
.titulo-cref {
	// margin: 10px 0px 0px 50px;
	// width: 15%;
	overflow: hidden;

	.titulo-msg {
		@extend .type-caption;
	}
	.subtitulo-modal {
		color: $pretoPri;
		font-size: 15px;
	}
}

.titulo-button {
	display: flex;
	flex-wrap: wrap;
	padding-top: 20px;
	width: 22%;
	justify-content: flex-end;
}

.titulo-data {
	text-overflow: ellipsis;
	.titulo-msg {
		@extend .type-caption;
	}
	.subtitulo-modal {
		color: $pretoPri;
	}
}

.titulo-aviso-msg {
	margin: -5px 15px;
}

.edit-programa {
	display: flex;
	line-height: 1;
	margin: 35px 0px 0px 15px;
	@extend .type-btn-bold;
	i {
		padding-right: 6px;
		font-size: 16px;
	}

	&:hover {
		cursor: pointer;
	}
}
.pct-arrow-left {
	font-size: 28px;
	color: $pretoPri;
	cursor: pointer;
	position: relative;
	top: 10px;
}
.card-retorno-container {
	display: flex;
	align-items: center;
	gap: 10px;
}
.card-info-programa {
	padding-top: 20px;
	display: flex;
	width: 100%;
	justify-content: space-between;
	align-items: center;
	@media (max-width: $plataforma-breakpoint-large) {
		width: calc(100vw - 260px);
	}
}
.card-info-tabs-ficha {
	display: flex;
	padding-top: 7px;
}
@media (max-width: 1200px) {
	.ficha-tabs {
		width: 100% !important;
	}
}
.ficha-tabs {
	display: flex;
	width: 80%;
	flex-wrap: wrap;
	.ficha {
		@extend .type-h6;
		cursor: pointer;
		padding: 5px 10px 5px;
		margin-right: 4px;
		border: 2px solid #ccc;
		border-width: 1px 1px 4px 1px;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		text-align: center;
		width: 100px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		background-color: #fbfbfc;
		color: #a1a5aa;

		&.selected {
			color: $pretoPri;
			background-color: $branco;
			border-width: 1px 1px 0px 1px;
		}
	}
	.ficha-nova {
		@extend .type-h6;
		cursor: pointer;
		padding: 6.5px 10px 5px;
		margin-right: 4px;
		border: 2px solid $azulimPri;
		background-color: $azulimPri;
		border-width: 2px 2px 0px 2px;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		text-align: center;
		width: 110px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: clip;
		color: white;
		font-size: 14px;
	}
}

.action-buttons {
	display: flex;
	width: 29%;
	justify-content: center;
	border: 2px solid #ccc;
	border-radius: 3px;
	border-width: 1px 1px 0px 1px;
	background-color: $branco;

	.space-action {
		margin-top: 10px;
		display: flex;
		padding: 0px 9px;
		cursor: pointer !important;

		.description-action {
			padding-left: 5px;
			@extend .type-btn-bold;
		}
	}
}
.predefinir {
	color: $azulimPri !important;
}
.excluir {
	color: $hellboyPri !important;
}

.ficha-tabs-alt {
	display: flex;
	width: 80%;
	flex-wrap: wrap;
	.ficha {
		@extend .type-h6;
		cursor: pointer;
		padding: 5px 10px 5px;
		margin-right: 4px;
		border: 2px solid #ccc;
		background-color: #fbfbfc;
		color: #a1a5aa;
		border-width: 2px 2px 4px 2px;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		text-align: center;
		width: 100px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		&.selected {
			color: $pretoPri;
			background-color: $branco;
			border-width: 2px 2px 0px 2px;
		}
	}
	.ficha-nova {
		@extend .type-h6;
		cursor: pointer;
		padding: 6.5px 10px 5px;
		margin-right: 10px;
		border: 2px solid $azulimPri;
		background-color: $azulimPri;
		border-width: 2px 2px 0px 2px;
		border-top-left-radius: 3px;
		border-top-right-radius: 3px;
		text-align: center;
		width: 110px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: clip;
		color: white;
		font-size: 14px;
	}
}

.action-buttons-alt {
	display: flex;
	width: 20%;
	justify-content: center;
	border: 2px solid #ccc;
	border-width: 2px 2px 0px 2px;
	background-color: $branco;

	.space-action {
		margin-top: 10px;
		display: flex;
		padding: 0px 9px;
		cursor: pointer !important;

		.description-action {
			padding-left: 5px;
			@extend .type-btn-bold;
		}
	}
}

.content-aviso-medico {
	min-height: 24px;
	width: 100%;
	.aviso-medico {
		margin-bottom: -10px;
		padding: 25px 15px;
		border-radius: 10px;
		box-shadow: 0 1px 4px 0 #e5e9f2;
		background: #fff;
		display: flex;
		height: 60px;
		align-items: center;
		justify-content: initial;
		font-size: 16px;
		font-family: "Nunito Sans";
		color: #db2c3d;

		i {
			margin-right: 10px;
		}

		span::first-letter {
			text-transform: uppercase;
		}
	}

	.flex-row-center {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}
