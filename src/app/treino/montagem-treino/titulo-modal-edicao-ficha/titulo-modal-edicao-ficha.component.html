<div class="view-header">
	<div class="header center-aux">
		<div class="card-info-programa">
			<div class="card-retorno-container">
				<i (click)="backList()" class="pct pct-arrow-left"></i>
				<div class="info-programa">
					<div
						class="titulo-msg"
						i18n="@@adicionar-atividade:visualizando-programa">
						Você está visualizando o programa:
					</div>
					<div class="titulo-modal" title="{{ programa?.nome }}">
						{{ programa?.nome }}
					</div>
				</div>
			</div>
			<div class="titulo-professor">
				<div class="titulo-msg" i18n="@@adicionar-atividade:criado-por">
					Criado por:
				</div>
				<div
					*ngIf="programa.professorMontou !== undefined"
					class="subtitulo-modal">
					{{ programa?.professorMontou.nome }}
				</div>
			</div>
			<div
				*ngIf="configTreino && configTreino.permitir_visualizar_cref"
				class="titulo-cref">
				<div class="titulo-msg" i18n="@@adicionar-atividade:criado-por">
					CREF:
				</div>
				<div
					*ngIf="programa.professorMontou.cref === ''"
					class="subtitulo-modal">
					Não informado
				</div>
				<div
					*ngIf="programa.professorMontou !== undefined"
					class="subtitulo-modal">
					{{ programa?.professorMontou.cref }}
				</div>
			</div>
			<div class="titulo-data">
				<div class="titulo-msg" i18n="@@adicionar-atividade:criado-em">
					Criado em:
				</div>
				<div class="subtitulo-modal">
					{{ programa?.dataLancamento | date : "dd/MM/yyyy" }}
				</div>
			</div>
			<div class="titulo-button">
				<pacto-cat-button
					(click)="editarProgramaHandler()"
					[icon]="'pct pct-edit-3'"
					[label]="getTextoLabelEditarOuVisualizar()"
					[v2]="true"
					i18n-label="@@adicionar-atividade:salvarFicha"
					id="editar-programa"
					type="SECUNDARY"></pacto-cat-button>
			</div>
		</div>
		<div class="content-aviso-medico">
			<div *ngIf="showMensagemAviso" class="aviso-medico">
				<i class="pct pct-alert-triangle"></i>
				<span>{{ clienteMensagemAviso }}</span>
			</div>
		</div>
	</div>
</div>
