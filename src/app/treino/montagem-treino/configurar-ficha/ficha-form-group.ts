import {
	AbstractControl,
	FormArray,
	FormControl,
	FormGroup,
} from "@angular/forms";
import {
	AtividadeFicha,
	SerieAtividade,
	DIA_SEMANA,
	FichaPrograma,
	GrupoMuscular,
} from "treino-api";

export function buildAtividadeFichaForm() {
	return new FormGroup({
		frontId: new FormControl(),
		id: new FormControl(),
		sequencia: new FormControl(),
		esforco: new FormControl(),
		metodoExecucao: new FormControl(),
		atividadeId: new FormControl(),
		atividadeSet1: new FormControl(null, [isAtividadeSet1]),
		atividadeSet2: new FormControl(null, [isAtividadeSet2]),
		series: new FormArray([]),
		complementoNomeAtividade: new FormControl(),
	});
}

function isAtividadeSet1(control: AbstractControl): any {
	let metodoSet = false;
	if (control.parent) {
		const metodoExecucao = control.parent.get("metodoExecucao").value;
		metodoSet = metodoExecucao === "BI_SET" || metodoExecucao === "TRI_SET";
	}
	const value = control.value;
	if (value) {
		return null;
	} else if (metodoSet) {
		return "invalidAtividadeSet1";
	} else {
		return null;
	}
}

function isAtividadeSet2(control: AbstractControl): any {
	let metodoSet = false;
	if (control.parent) {
		const metodoExecucao = control.parent.get("metodoExecucao").value;
		metodoSet = metodoExecucao === "TRI_SET";
	}
	const value = control.value;
	if (value) {
		return null;
	} else if (metodoSet) {
		return "invalidAtividadeSet1";
	} else {
		return null;
	}
}

export function buildSerieAtividadeForm() {
	return new FormGroup({
		id: new FormControl(),
		sequencia: new FormControl(),
		repeticoes: new FormControl(),
		carga: new FormControl(),
		cadencia: new FormControl(),
		descanso: new FormControl(),
		velocidade: new FormControl(),
		duracao: new FormControl(),
		distancia: new FormControl(),
		complemento: new FormControl(),
	});
}

export function buildFichaForm() {
	return new FormGroup({
		nome: new FormControl(),
		categoriaId: new FormControl(),
		tipo_execucao: new FormControl(),
		dias_semana: new FormArray([]),
		mensagem: new FormControl(),
		atividades: new FormArray([]),
		predefinida: new FormControl(),
		ativo: new FormControl(),
	});
}

export function filloutFichaForm(ficha: FichaPrograma) {
	return new FormGroup({
		id: new FormControl(ficha.id),
		nome: new FormControl(ficha.nome),
		categoriaId: new FormControl(ficha.categoria ? ficha.categoria.id : null),
		tipo_execucao: new FormControl(ficha.tipo_execucao),
		dias_semana: new FormArray([
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.SEG)),
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.TER)),
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.QUA)),
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.QUI)),
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.SEX)),
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.SAB)),
			new FormControl(ficha.dias_semana.includes(DIA_SEMANA.DOM)),
		]),
		mensagem: new FormControl(ficha.mensagem),
		atividades: new FormArray(filloutAtividadesFichaForm(ficha.atividades)),
		predefinida: new FormControl(ficha.predefinida),
		ativo: new FormControl(ficha.ativo),
	});
}

export function filloutAtividadesFichaForm(
	atividadesFicha: Array<AtividadeFicha>
) {
	const atividadesFichaForm: Array<FormGroup> = [];

	atividadesFicha.forEach((atividadeFicha) => {
		atividadesFichaForm.push(
			new FormGroup({
				frontId: new FormControl(atividadesFichaForm.length + 1),
				id: new FormControl(atividadeFicha.id),
				sequencia: new FormControl(atividadeFicha.sequencia),
				esforco: new FormControl(atividadeFicha.esforco),
				metodoExecucao: new FormControl(atividadeFicha.metodoExecucao),
				atividadeId: new FormControl(atividadeFicha.atividade.id),
				atividadeSet1: new FormControl(
					atividadeFicha.setAtividades.length > 0
						? atividadeFicha.setAtividades[0].id
						: null
				),
				atividadeSet2: new FormControl(
					atividadeFicha.setAtividades.length > 1
						? atividadeFicha.setAtividades[1].id
						: null
				),
				series: new FormArray(filloutSerieForm(atividadeFicha.series)),
				gruposMusculares: new FormArray(
					filloutGruposMuscularesForm(atividadeFicha.atividade.gruposMusculares)
				),
				complementoNomeAtividade: new FormControl(
					atividadeFicha.complementoNomeAtividade
				),
			})
		);
	});
	return atividadesFichaForm;
}

function filloutSerieForm(serieAtividade: Array<SerieAtividade>) {
	const seriesAtividadeForm: Array<FormGroup> = [];
	serieAtividade.forEach((serie) => {
		seriesAtividadeForm.push(
			new FormGroup({
				id: new FormControl(serie.id),
				sequencia: new FormControl(serie.sequencia),
				repeticoes: new FormControl(serie.repeticoes),
				carga: new FormControl(serie.carga),
				cadencia: new FormControl(serie.cadencia),
				descanso: new FormControl(serie.descanso),
				velocidade: new FormControl(serie.velocidade),
				duracao: new FormControl(serie.duracao),
				distancia: new FormControl(serie.distancia),
				complemento: new FormControl(serie.complemento),
			})
		);
	});
	return seriesAtividadeForm;
}

function filloutGruposMuscularesForm(gruposMusculares: Array<GrupoMuscular>) {
	const gruposMuscularesForm: Array<FormGroup> = [];
	if (gruposMusculares) {
		gruposMusculares.forEach((grupoMuscular) => {
			gruposMuscularesForm.push(
				new FormGroup({
					id: new FormControl(grupoMuscular.id),
					nome: new FormControl(grupoMuscular.nome),
				})
			);
		});
	}
	return gruposMuscularesForm;
}
