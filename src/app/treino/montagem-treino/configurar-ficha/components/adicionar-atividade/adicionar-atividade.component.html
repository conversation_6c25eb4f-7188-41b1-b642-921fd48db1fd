<div *ngIf="colunasGridFluxo" class="container-comparacao">
	<pacto-cat-button
		(click)="toggleContainerAtividades()"
		[icon]="'pct pct-chevrons-right'"
		[label]="
			mostrarContainerAtividades
				? 'Retrair comparação de fichas'
				: 'Expandir comparação de fichas'
		"
		[type]="'SECUNDARY'"
		[v2]="true"
		class="botao-comparacao"></pacto-cat-button>
</div>

<div class="row">
	<div
		*ngIf="mostrarContainerAtividades"
		[ngClass]="mostrarColuna ? 'fade-in-right' : 'fade-out-right'"
		class="col-md-4">
		<div class="container-atividades">
			<div class="topo-atividade">
				<span>Escolha uma ficha</span>
				<pacto-cat-form-select
					(change)="localizarFicha(formGroupFichas.get('ficha').value)"
					[control]="formGroupFichas.get('ficha')"
					[id]="'select-filtro'"
					[items]="fichasPrograma"
					label="'Escolha uma ficha'"></pacto-cat-form-select>
			</div>
			<hr />

			<div class="container-atividade">
				<div *ngIf="mensagemSemAtividades">{{ mensagemSemAtividades }}</div>
				<div
					*ngFor="
						let atividade of formGroupFichas.get('atividades').controls;
						let i = index
					">
					<div (click)="copiarAtividade(i)" class="copiar">
						<span id="copiar-atividade">Copiar atividade</span>
					</div>
					<pacto-cat-form-input
						[attr.disabled]="true"
						[control]="atividade.get('nome')"
						[id]="'input-nome'"
						[label]="'Atividade (' + (i + 1) + ')'"
						class="series"></pacto-cat-form-input>
					<div class="row-form">
						<pacto-cat-form-input
							[attr.disabled]="true"
							[control]="atividade.get('series')"
							[id]="'input-series'"
							[textMask]="{ guide: false, mask: inteiroDoisCaracteres }"
							class="series"
							label="Séries"
							placeholder="0"></pacto-cat-form-input>
						<pacto-cat-form-input
							[attr.disabled]="true"
							[control]="atividade.get('repeticoes')"
							[id]="'input-repeticoes'"
							label="Repetições"
							placeholder="0"></pacto-cat-form-input>
						<pacto-cat-form-input
							[attr.disabled]="true"
							[control]="atividade.get('carga')"
							[id]="'input-carga'"
							label="Carga"
							placeholder="0"></pacto-cat-form-input>
						<pacto-cat-form-input
							[attr.disabled]="true"
							[control]="atividade.get('cadencia')"
							[id]="'input-cadencia'"
							label="Cadência"
							placeholder="0"></pacto-cat-form-input>
					</div>
					<div class="row-form mt-2">
						<div class="input-criado">
							<div class="nome">
								<span>Descanso</span>
							</div>
							<div class="aux-wrapper">
								<input
									[attr.disabled]="true"
									[formControl]="atividade.get('descanso')"
									[placeholder]="'00:00'"
									[textMask]="{ guide: false, mask: minuteSecondMask }"
									id="input-descanso-atividade"
									type="text" />
							</div>
						</div>
						<pacto-cat-form-input
							[attr.disabled]="true"
							[control]="atividade.get('metodo')"
							[id]="'input-metodo'"
							class="metodo"
							label="Método"
							placeholder="Não atribuído"></pacto-cat-form-input>
					</div>
					<hr />
				</div>
			</div>
		</div>
	</div>

	<div [ngClass]="colunasGrid">
		<div class="container">
			<div class="title-card-adicionar-atividade">
				Adicione uma atividade

				<pacto-cat-button
					(click)="adicionarAtividades()"
					[disabled]="!permissaoProgramaTreino?.editar"
					[icon]="'pct pct-layers'"
					[type]="'SECUNDARY'"
					[v2]="true"
					class="add-varias-atividades"
					id="adicionar-varias-atividades"
					label="Adicionar várias atividades"></pacto-cat-button>
			</div>

			<div class="row">
				<div class="col-md-12 font-adicionar-atividade no-padding row">
					<pacto-cat-select-filter
						#atividadeSelect
						[class]="'col-md-5 select-atividade'"
						[id]="'select-atividade'"
						label="Busque uma atividade pelo nome ou grupo muscular"
						[control]="formGroup.get('atividadeId')"
						[endpointUrl]="_rest.buildFullUrl('atividades/montarTreino')"
						[responseParser]="responseParser"
						[paramBuilder]="atividadeSelectBuilder"
						[labelKey]="'nome'"></pacto-cat-select-filter>

					<pacto-cat-form-input
						class="series"
						[class]="'col-md-1'"
						*ngIf="camposAtivos.includes('series')"
						[id]="'input-series'"
						label="Séries"
						placeholder="3, 4, 5, 6..."
						[textMask]="{ guide: false, mask: inteiroDoisCaracteres }"
						[control]="formGroup.get('series')"
						[readonly]="
							!permissaoProgramaTreino?.editar
						"></pacto-cat-form-input>

					<pacto-cat-form-input
						[class]="'col-md-1'"
						*ngIf="camposAtivos.includes('repeticoes')"
						[id]="'input-repeticoes'"
						label="Repetições"
						placeholder="1, 2, ..."
						[control]="formGroup.get('repeticoes')"
						[readonly]="
							!permissaoProgramaTreino?.editar
						"></pacto-cat-form-input>

					<pacto-cat-form-input
						[class]="'col-md-1'"
						*ngIf="camposAtivos.includes('carga')"
						[id]="'input-carga'"
						label="Carga"
						placeholder="Peso em KG"
						[control]="formGroup.get('carga')"
						[readonly]="
							!permissaoProgramaTreino?.editar
						"></pacto-cat-form-input>

					<pacto-cat-form-input
						[class]="'col-md-1'"
						*ngIf="camposAtivos.includes('cadencia')"
						[id]="'input-cadencia'"
						label="Cadência"
						placeholder="Cadência"
						[control]="formGroup.get('cadencia')"
						[readonly]="
							!permissaoProgramaTreino?.editar
						"></pacto-cat-form-input>

					<div
						*ngIf="camposAtivos.includes('duracao')"
						class="input-criado col-md-1">
						<div class="nome">
							<span>Duração</span>
						</div>
						<div class="aux-wrapper">
							<input
								type="text"
								id="input-duracao"
								[textMask]="{ guide: false, mask: minuteSecondMask }"
								[placeholder]="'00:00'"
								[formControl]="formGroup.get('duracao')" />
						</div>
					</div>

					<pacto-cat-form-input
						*ngIf="camposAtivos.includes('velocidade')"
						[class]="'col-md-1'"
						[control]="formGroup.get('velocidade')"
						[id]="'input-velocidade'"
						[readonly]="!permissaoProgramaTreino?.editar"
						[textMask]="{ guide: false, mask: numeroInteiro }"
						label="Velocidade"
						placeholder="Velocidade em km/h"></pacto-cat-form-input>

					<div
						*ngIf="camposAtivos.includes('descanso')"
						class="input-criado padding-top-botton-15 col-md-1">
						<div class="nome">
							<span>Descanso</span>
						</div>
						<div class="aux-wrapper">
							<input
								[formControl]="formGroup.get('descanso')"
								[placeholder]="'00:00'"
								[readonly]="!permissaoProgramaTreino?.editar"
								[textMask]="{ guide: false, mask: minuteSecondMask }"
								id="input-descanso"
								type="text" />
						</div>
					</div>

					<pacto-cat-form-input
						[class]="'col-md-1'"
						*ngIf="camposAtivos.includes('distancia')"
						[id]="'input-distancia'"
						label="Distância"
						placeholder="Distância em metros"
						[textMask]="{ guide: false, mask: numeroInteiro }"
						[control]="formGroup.get('distancia')"
						[readonly]="
							!permissaoProgramaTreino?.editar
						"></pacto-cat-form-input>

					<div
						class="col-md-2 text-center group-buttons btn-adicionar-atividade">
						<pacto-cat-button
							(click)="adicionarHandler()"
							[disabled]="!acaoHabilitada"
							[icon]="'pct pct-plus'"
							[id]="'btn-adicionar'"
							[type]="'PRIMARY'"
							[v2]="true"
							label="Adicionar"></pacto-cat-button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<ng-template #atividadeTemplate let-item="item">
	<div
		class="user-item-wrapper"
		id="montagem-treino-atividade-{{ item.index }}">
		<div *ngIf="item.images" class="image">
			<img
				[ngStyle]="{
					'background-size': item.images.length ? 'cover' : 'auto'
				}"
				src="{{ hasImage(item) ? item.images[0].uri : defaultImage }}" />
		</div>
		<div class="name">
			{{ item.nome }}
		</div>
	</div>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@adicionar-atividade:selecionarAtividade"
		xingling="selecionarAtividade">
		Selecione uma atividade
	</span>
	<span
		i18n="@@adicionar-atividade:atividadeAdicionada"
		xingling="atividadeAdicionada">
		Atividade adicionada
	</span>
	<span
		i18n="@@adicionar-atividade:ficha-save-success"
		xingling="ficha-save-success">
		Ficha salva com sucesso.
	</span>
</pacto-traducoes-xingling>
