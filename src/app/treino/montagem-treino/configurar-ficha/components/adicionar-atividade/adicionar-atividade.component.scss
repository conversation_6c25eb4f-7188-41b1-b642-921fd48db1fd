@import "src/assets/scss/pacto/plataforma-import.scss";

.title-card-adicionar-atividade {
	padding: 32px 0;
	@extend .type-h5;
	color: #51555a;
	font-weight: 600;

	::ng-deep input {
		padding: 0 8px !important;
	}
}

.container {
	padding: 13px 18px;
	background-color: #eff2f7;
	border-radius: 4px;
	max-width: 1600px;
}

.add-varias-atividades {
	margin-right: 15px;
}

.row-form {
	display: flex;

	.input-criado {
		margin: 0 0 0 8px;
	}

	pacto-cat-multi-select-filter,
	pacto-cat-select-filter,
	pacto-cat-form-input {
		margin-right: 20px;

		::ng-deep .pacto-label {
			color: $pretoPri;
			line-height: 28px;
			min-height: auto;
			font-size: 12px;
		}
	}

	pacto-cat-time-input,
	pacto-cat-form-input {
		margin: 0 0 0 8px;

		&:first-child {
			margin-left: 0;
		}
	}

	pacto-cat-button {
		margin-top: 28px;
		margin-left: 8px;

		::ng-deep .pacto-button {
			line-height: 40px;
			width: 137px;
		}
	}

	.group-inputs {
		display: flex;
		width: 90%;
	}
}

.user-item-wrapper {
	display: flex;

	.image img {
		width: 30px;
		height: 30px;
		border-radius: 15px;
		border: 1px solid #c7c7c7;
	}

	.name {
		font-size: 13px;
		line-height: 30px;
		margin-left: 10px;
		font-weight: 600;
		color: #333;
	}
}

.nome {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
	font-weight: 400;
	font-size: 16px;
}

.aux-wrapper {
	position: relative;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0 30px 0 10px;
	line-height: 40px;
	color: $pretoPri;
	outline: 0 !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

::placeholder {
	color: $cinzaClaro02;
}

.botao-comparacao {
	background: #fff;
	color: inherit;
	border: none;
	box-shadow: none;

	&:hover {
		background: #fff;
		color: inherit;
		box-shadow: none;
		border: none;
	}
}

.container-atividades {
	background: #eff2f7;
	height: 580px;
	width: 100%;
	position: fixed;
	top: 0;
	right: 0;

	pacto-cat-multi-select-filter,
	pacto-cat-form-select {
		::ng-deep .pacto-label {
			font-size: 14px;
			color: $pretoPri !important;
		}
	}

	::ng-deep .pacto-label {
		display: none;
	}

	span,
	pacto-cat-form-input {
		font-family: Nunito Sans;
		font-size: 14px;
		font-weight: 600;
		line-height: 21px;
		text-align: left;
		color: #83888f;
	}

	.topo-atividade {
		padding: 16px;
	}

	.container-atividade {
		padding: 0 16px;
		max-height: 380px;
		overflow-y: auto;

		.copiar {
			@extend .type-h6;
			color: $azulim07 !important;
			min-height: auto;
			padding-right: 3px;
			letter-spacing: 0.25px;
			float: right;
		}

		.input-criado {
			width: 40%;
			margin: 0;
		}

		.metodo {
			width: 60%;
		}
	}
}

@keyframes fadeInRight {
	from {
		opacity: 0;
		transform: translateX(-100%);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes fadeOutRight {
	from {
		opacity: 1;
		transform: translateX(0);
	}
	to {
		opacity: 0;
		transform: translateX(100%);
	}
}

.fade-in-right {
	animation: fadeInRight 0.5s ease-in-out forwards;
}

.fade-out-right {
	animation: fadeOutRight 0.5s ease-in-out forwards;
}

.group-inputs {
	display: flex;
	align-items: flex-end;
}

.group-inputs pacto-cat-form-input {
	margin-right: 10px;
}

.group-inputs .group-buttons {
	margin-left: auto;
}

#input-descanso-atividade {
	border: none;
	color: #a6aab1;
}

#copiar-atividade {
	color: #02589c;
}

.flex-space-around {
	display: flex;
	justify-content: space-around;
}

.componente-corpo-grupo-muscular {
	display: flex;

	::ng-deep .grupo-muscular-frontal {
		max-height: 308px;
		max-width: 187px;
	}

	::ng-deep .grupo-muscular-posterior {
		max-height: 308px;
		max-width: 187px;
	}
}

.font-adicionar-atividade {
	::ng-deep .pacto-label {
		font-family: "Nunito Sans" !important;
		font-size: 14px !important;
		font-weight: 600 !important;
		line-height: 21px !important;
		text-align: left !important;
		color: $preto01;
		padding-bottom: 9px !important;
	}

	::ng-deep .nome {
		font-family: "Nunito Sans" !important;
		font-size: 14px !important;
		font-weight: 600 !important;
		line-height: 21px !important;
		text-align: left !important;
		color: $preto01;
	}

	.col-md-1 {
		padding: 0 5px;
		margin: 15px 0 !important;
	}
}

.no-padding {
	padding: 0px !important;
}

.margin-laterais-15 {
	margin-right: -15px !important;
	margin-left: -15px !important;
}

.padding-top-botton-15 {
	margin: 15px 0;
}

.btn-adicionar-atividade {
	::ng-deep button {
		width: 100%;
		margin-top: 48px;
	}
}

::ng-deep pacto-cat-button#gerar-treino-por-ia {
	.content {
		text-transform: inherit !important;
	}
}

.select-atividade {
	padding-top: 18px;
}

::ng-deep pacto-cat-button#gerar-treino-por-ia {
	.content {
		text-transform: inherit !important;
	}
}
