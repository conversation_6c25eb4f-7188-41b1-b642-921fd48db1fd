import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { TraducoesXinglingComponent } from "ui-kit";
import { AtividadeMetodoExecucao } from "treino-api";
import { FormArray, FormGroup } from "@angular/forms";
import { ConfigurarFichaService } from "../../../configurar-ficha.service";

@Component({
	selector: "pacto-atividade-ficha",
	templateUrl: "./atividade-ficha.component.html",
	styleUrls: ["./atividade-ficha.component.scss"],
})
export class AtividadeFichaComponent implements OnInit {
	@ViewChild("metudoExecucaoTraducao", { static: true })
	metudoExecucaoTraducao: TraducoesXinglingComponent;
	@Input() atividadeFichaForm: FormGroup;

	metodosExecucoes: Array<{ id: string; nome: string }> = [];
	esforcos: Array<any> = [
		{ id: 50, nome: "50%" },
		{ id: 55, nome: "55%" },
		{ id: 60, nome: "60%" },
		{ id: 65, nome: "65%" },
		{ id: 70, nome: "70%" },
		{ id: 75, nome: "75%" },
		{ id: 80, nome: "80%" },
		{ id: 85, nome: "85%" },
		{ id: 90, nome: "90%" },
		{ id: 95, nome: "95%" },
		{ id: 100, nome: "100%" },
	];
	metodoBiSet = false;
	metodoTriSet = false;
	duplicado = false;
	duplicado2 = false;
	atividades: Array<{ id: number; nome: string }> = [];

	constructor(
		private configurarFichaService: ConfigurarFichaService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.atividadeFichaForm
			.get("metodoExecucao")
			.valueChanges.subscribe((value) => {
				this.atividadeFichaForm
					.get("atividadeSet1")
					.setValue(null, { emitEvent: false });
				this.atividadeFichaForm
					.get("atividadeSet2")
					.setValue(null, { emitEvent: false });
				this.metodoBiSet =
					value === AtividadeMetodoExecucao.TRI_SET ||
					value === AtividadeMetodoExecucao.BI_SET;
				this.metodoTriSet = value === AtividadeMetodoExecucao.TRI_SET;
				if (this.metodoTriSet) {
					this.atividadeFichaForm.get("atividadeSet1").markAsTouched();
					this.atividadeFichaForm.get("atividadeSet2").markAsTouched();
				} else if (this.metodoBiSet) {
					this.atividadeFichaForm.get("atividadeSet1").markAsTouched();
				}
			});
		setTimeout(() => {
			this.setupSelect();
		});
		this.initItensSelects();
		this.atividadeFichaForm
			.get("atividadeSet1")
			.valueChanges.subscribe((value) => {
				this.duplicado = this.verificarSeEstaSendoUtilizado(
					value,
					"atividadeSet1"
				);
				if (this.duplicado) {
					this.atividadeFichaForm
						.get("atividadeSet1")
						.setValue(null, { emitEvent: false });
					this.atividadeFichaForm.get("atividadeSet1").markAsTouched();
				}
			});
		this.atividadeFichaForm
			.get("atividadeSet2")
			.valueChanges.subscribe((value) => {
				this.duplicado2 = this.verificarSeEstaSendoUtilizado(
					value,
					"atividadeSet2"
				);
				if (this.duplicado2) {
					this.atividadeFichaForm
						.get("atividadeSet2")
						.setValue(null, { emitEvent: false });
					this.atividadeFichaForm.get("atividadeSet2").markAsTouched();
				}
			});
	}

	private verificarSeEstaSendoUtilizado(atividadeFrontId, campoFezAcao) {
		let result = false;
		if (campoFezAcao === "atividadeSet1") {
			result =
				parseInt(this.atividadeFichaForm.get("atividadeSet2").value, 10) ===
				parseInt(atividadeFrontId, 10);
		} else if (campoFezAcao === "atividadeSet2") {
			result =
				parseInt(this.atividadeFichaForm.get("atividadeSet1").value, 10) ===
				parseInt(atividadeFrontId, 10);
		}

		if (!result) {
			const atividadesFichaForm = (
				this.configurarFichaService.fichaFormGroup.get(
					"atividades"
				) as FormArray
			).controls;
			atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
				if (
					parseInt(atividadeFichaForm.get("atividadeSet1").value, 10) ===
						parseInt(atividadeFrontId, 10) ||
					parseInt(atividadeFichaForm.get("atividadeSet2").value, 10) ===
						parseInt(atividadeFrontId, 10)
				) {
					result = true;
				}
			});
		}
		return result;
	}

	private initItensSelects() {
		const atividadeSelecionadaId = this.atividadeFichaForm.get("frontId").value;
		(
			this.configurarFichaService.fichaFormGroup.get("atividades") as FormArray
		).controls.forEach((atividadeFicha: FormGroup) => {
			if (atividadeSelecionadaId !== atividadeFicha.get("frontId").value) {
				this.atividades.push({
					id: atividadeFicha.get("frontId").value,
					nome: this.configurarFichaService.obterAtividadeMap(
						atividadeFicha.get("atividadeId").value
					).nome,
				});
			}
		});
	}

	private setupSelect() {
		this.metodosExecucoes = [];
		this.metodosExecucoes.push({
			id: "",
			nome: "",
		});
		Object.keys(AtividadeMetodoExecucao).forEach((metodoExecucao) => {
			this.metodosExecucoes.push({
				id: metodoExecucao,
				nome: this.metudoExecucaoTraducao.getLabel(metodoExecucao),
			});
		});
		this.metodosExecucoes.sort((a, b) => (a.nome < b.nome ? -1 : 1));
		this.iniComponent();
		this.cd.detectChanges();
	}

	private iniComponent() {
		const metodoexecucaoSelecionado =
			this.atividadeFichaForm.get("metodoExecucao").value;
		this.metodoBiSet =
			metodoexecucaoSelecionado === AtividadeMetodoExecucao.TRI_SET ||
			metodoexecucaoSelecionado === AtividadeMetodoExecucao.BI_SET;
		this.metodoTriSet =
			metodoexecucaoSelecionado === AtividadeMetodoExecucao.TRI_SET;
	}
}
