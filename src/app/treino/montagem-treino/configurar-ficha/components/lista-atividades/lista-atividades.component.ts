import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	Input,
	OnInit,
	QueryList,
	ViewChild,
	ViewChildren,
} from "@angular/core";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { ConfigurarFichaService } from "../../../configurar-ficha.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { EditorSeriesComponent } from "../editor-series/editor-series.component";
import { LocalizationService } from "@base-core/localization/localization.service";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import {
	buildAtividadeFichaForm,
	buildSerieAtividadeForm,
} from "../../ficha-form-group";
import { debounceTime } from "rxjs/operators";
import {
	AtividadeMetodoExecucao,
	AtividadeFicha,
	PerfilAcessoRecursoNome,
	TreinoApiAtividadeService,
	TreinoApiGrupoMuscularService,
	GrupoMuscular,
} from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { PadraoSeriesComponent } from "../../../padrao-series/padrao-series.component";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-lista-atividades",
	templateUrl: "./lista-atividades.component.html",
	styleUrls: ["./lista-atividades.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ListaAtividadesComponent implements OnInit {
	@Input() atividades: Array<AtividadeFicha> = [];
	@ViewChild("notificacaoTranslate", { static: true })
	notificacaoTranslate: TraducoesXinglingComponent;
	@ViewChild("metodoExecucaoTraducao", { static: true })
	metodoExecucaoTraducao: TraducoesXinglingComponent;
	@ViewChildren("txtArea") textAreas: QueryList<ElementRef>;
	metodosExecucoes: Array<{ id: string; nome: string }> = [];
	listaItensAtividades: Array<{ id: number; nome: string }> = [];
	listAtividades: Array<{
		id: number;
		frontId: number;
		nome: string;
		nomeAtivFicha: string;
		complementoNomeAtividade: string;
		setAtividade1?: any;
		setAtividade2?: any;
		imageUri: string;
		gruposMusculares?: any;
		tipo: string;
		metodoExecucao?: any;
		serieApenasDuracao: boolean;
		sequencia: number;
		aberto: boolean;
		editComplementoNomeAtividade: boolean;
		seriesEditadas: any;
		formGroup: FormGroup;
	}> = [];
	timeMask;
	esforcos: Array<any> = [
		{ id: 50, nome: "50%" },
		{ id: 55, nome: "55%" },
		{ id: 60, nome: "60%" },
		{ id: 65, nome: "65%" },
		{ id: 70, nome: "70%" },
		{ id: 75, nome: "75%" },
		{ id: 80, nome: "80%" },
		{ id: 85, nome: "85%" },
		{ id: 90, nome: "90%" },
		{ id: 95, nome: "95%" },
		{ id: 100, nome: "100%" },
	];
	permissaoProgramaTreino;
	colunasGrid: string;
	customizarStyle: { [key: string]: string };
	allGruposMusculares: Array<GrupoMuscular> = [];

	constructor(
		private configurarFichaService: ConfigurarFichaService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private modalService: ModalService,
		private localization: LocalizationService,
		private utils: WindowUtilService,
		private appModal: ModalService,
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private atividadeService: TreinoApiAtividadeService
	) {
		this.timeMask = {
			mask: this.localization.getTimeMask(),
		};
	}

	get minuteSecondMask() {
		return this.localization.getMinuteSecondMask();
	}

	get somenteInteiroMask() {
		return this.localization.getNumberMask();
	}

	ngOnInit() {
		this.atividadeService.resetMostrarContainerAtividades();
		this.atividadeService.mostrarContainerAtividades$.subscribe((value) => {
			this.colunasGrid = value ? "col-md-8" : "col-md-12";
			this.customizarStyle = value ? { float: "right" } : {};
			this.cd.detectChanges();
		});
		this.carregarPermissoes();
		this.configurarFichaService.atividadesFichaView$.subscribe(
			(atividadesFicha) => {
				setTimeout(() => {
					this.setupList(atividadesFicha);
				});
			}
		);
		this.configurarFichaService.removeAtividade.subscribe((frontId) => {
			this.removeHandler(frontId);
			this.salvarFichaHandler();
		});
		this.carregarTodosGruposMusculares();
	}

	carregarTodosGruposMusculares() {
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.allGruposMusculares = dados;
				this.cd.detectChanges();
			});
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
	}

	drop(event: CdkDragDrop<string[]>) {
		this.reordenarArray(event.previousIndex, event.currentIndex);
	}

	replicarSeriesRepeticoes() {
		if (this.permissaoProgramaTreino.editar) {
			const modal = this.appModal.open(
				"Criar padrão da série de atividades",
				PadraoSeriesComponent,
				PactoModalSize.MEDIUM
			);
			modal.componentInstance.ficha =
				this.configurarFichaService.obterFichaAtual();
			modal.componentInstance.nrAtividades = this.listAtividades.length;
			modal.result.then((fg: FormGroup) => {
				this.listAtividades.forEach((atividade) => {
					if (fg.get("series") && fg.get("series").value) {
						atividade.formGroup.get("series").setValue(fg.get("series").value);
					}
					if (fg.get("repeticoes") && fg.get("repeticoes").value) {
						atividade.formGroup
							.get("repeticoes")
							.setValue(fg.get("repeticoes").value);
						atividade.seriesEditadas.repeticao = false;
					}
					if (fg.get("carga") && fg.get("carga").value) {
						atividade.formGroup.get("carga").setValue(fg.get("carga").value);
						atividade.seriesEditadas.carga = false;
					}
					if (fg.get("cadencia") && fg.get("cadencia").value) {
						atividade.formGroup
							.get("cadencia")
							.setValue(fg.get("cadencia").value);
						atividade.seriesEditadas.cadencia = false;
					}
					if (fg.get("descanso") && fg.get("descanso").value) {
						atividade.formGroup
							.get("descanso")
							.setValue(fg.get("descanso").value);
						atividade.seriesEditadas.descanso = false;
					}
					atividade.seriesEditadas.seriesDiferentes =
						atividade.seriesEditadas.repeticao ||
						atividade.seriesEditadas.carga ||
						atividade.seriesEditadas.cadencia ||
						atividade.seriesEditadas.descanso ||
						atividade.seriesEditadas.velocidade ||
						atividade.seriesEditadas.duracao ||
						atividade.seriesEditadas.distancia;
					this.cd.detectChanges();
				});
			});
			this.cd.detectChanges();
		}
	}

	abrirEditorSeries(frontId: number) {
		const atividadeFichaForm: FormGroup = this.obterAtividadeFrontId(frontId);
		const modal = this.modalService.open(
			this.metodoExecucaoTraducao.getLabel("editarSerie"),
			EditorSeriesComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.ready(atividadeFichaForm);
		modal.result.then((result: FormGroup) => {
			this.configurarFichaService.atualizarAtividadeFicha(
				frontId,
				result,
				true
			);
			this.salvarFichaHandler();
			this.snotifyService.success(
				this.notificacaoTranslate.getLabel("serie-edit-success")
			);
		});
	}

	obterFrontIdPorNome(nome: string): any {
		return this.listAtividades.filter((i) => i.nome === nome)[0].frontId;
	}

	removerVinculosSetNovaAtividadeSelecionada(frontId: number) {
		const filterElement = this.listAtividades.filter(
			(i) => i.frontId === frontId
		)[0];
		const listFrontId: Array<number> = [];
		for (const atividade of this.listAtividades) {
			if (
				(atividade.setAtividade1 &&
					atividade.setAtividade1.id === filterElement.id) ||
				(atividade.setAtividade2 &&
					atividade.setAtividade2.id === filterElement.id)
			) {
				listFrontId.push(atividade.frontId);
			}
		}
		for (const atividadeFrontId of listFrontId) {
			this.removerRelacionamentoSet(atividadeFrontId);
		}
	}

	atualizarAtividadeSet(atividade, frontId) {
		const abertos = new Array<any>();
		this.listAtividades.forEach((at: any) => {
			if (at.aberto) {
				abertos.push(at.frontId);
			}
		});
		if (
			atividade.formGroup.get("metodoExecucao").value ===
				AtividadeMetodoExecucao.BI_SET &&
			atividade.formGroup.get("setAtividade1").value
		) {
			if (atividade.setAtividade1) {
				this.removerRelacionamentoSet(
					this.obterFrontIdPorNome(atividade.setAtividade1.nome)
				);
			}
			if (atividade.setAtividade2) {
				this.removerRelacionamentoSet(
					this.obterFrontIdPorNome(atividade.setAtividade2.nome)
				);
			}
			this.removerVinculosSetNovaAtividadeSelecionada(
				Number(atividade.formGroup.get("setAtividade1").value)
			);
			this.atualizarRelacionamentoBiSetV2(atividade.formGroup, frontId);
		} else if (
			atividade.formGroup.get("metodoExecucao").value ===
				AtividadeMetodoExecucao.TRI_SET &&
			atividade.formGroup.get("setAtividade1").value &&
			atividade.formGroup.get("setAtividade2").value
		) {
			// remove todos os vínculos atuais da atividade principal
			this.removerVinculosSetNovaAtividadeSelecionada(atividade.frontId);
			// início remove todos vínculos anteriores das atividades set selecionadas
			if (atividade.setAtividade1) {
				this.removerRelacionamentoSet(
					this.obterFrontIdPorNome(atividade.setAtividade1.nome)
				);
			}
			if (atividade.formGroup.get("setAtividade1").value) {
				this.removerVinculosSetNovaAtividadeSelecionada(
					Number(atividade.formGroup.get("setAtividade1").value)
				);
			}

			if (atividade.setAtividade2) {
				this.removerRelacionamentoSet(
					this.obterFrontIdPorNome(atividade.setAtividade2.nome)
				);
			}
			if (atividade.formGroup.get("setAtividade2").value) {
				this.removerVinculosSetNovaAtividadeSelecionada(
					Number(atividade.formGroup.get("setAtividade2").value)
				);
			}
			// fim
			this.atualizarRelacionamentoTriSet(atividade.formGroup, frontId);
		}
		const atividadePrincipal = this.obterAtividadeFrontId(
			parseInt(frontId, 10)
		);
		atividadePrincipal
			.get("metodoExecucao")
			.setValue(atividade.formGroup.get("metodoExecucao").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadePrincipal
			.get("atividadeSet1")
			.setValue(atividade.formGroup.get("setAtividade1").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadePrincipal
			.get("atividadeSet2")
			.setValue(
				atividade.formGroup.get("setAtividade2")
					? atividade.formGroup.get("setAtividade2").value
					: 0,
				{ onlySelf: false, emitEvent: false }
			);
		this.configurarFichaService.atualizarAtividadeFicha(
			frontId,
			atividadePrincipal,
			true
		);
		this.configurarFichaService.atualizarSetAtividade(this.listAtividades);
		this.listAtividades.forEach((at: any) => {
			at.aberto = abertos.indexOf(at.frontId) > -1;
		});
	}

	atualizarMetodoSet(atividade) {
		if (atividade.formGroup.get("metodoExecucao").value === "NAO_ATRIBUIDO") {
			if (
				atividade.setAtividade1 !== null ||
				atividade.formGroup.get("setAtividade1").value !== null
			) {
				this.removerRelacionamentoSet(atividade.frontId);
				this.removerRelacionamentoSet(
					atividade.formGroup.get("setAtividade1").value
				);
			}
			if (
				atividade.setAtividade2 !== null ||
				atividade.formGroup.get("setAtividade2").value !== null
			) {
				this.removerRelacionamentoSet(atividade.frontId);
				this.removerRelacionamentoSet(
					atividade.formGroup.get("setAtividade2").value
				);
			}
		}
	}

	salvarFichaHandler() {
		this.configurarFichaService.salvarFicha().subscribe(() => {
			this.cd.detectChanges();
		});
	}

	removeHandler(frontId: number) {
		const atividadefichaForm = this.obterAtividadeFrontId(frontId);
		const atividadeFichaFormSemRelacionamento =
			this.obterAtividadeFrontId(frontId);
		atividadeFichaFormSemRelacionamento.get("atividadeSet1").setValue(null);
		atividadeFichaFormSemRelacionamento.get("atividadeSet2").setValue(null);
		this.verificarSeOuveModificacaoSet(
			atividadefichaForm,
			atividadeFichaFormSemRelacionamento
		);
		this.configurarFichaService.removerAtividadeFicha(frontId);
		this.loadListItensAtividades();
		this.cd.detectChanges();
	}
	campoHabilitado(atividadeFicha: FormGroup) {
		return atividadeFicha.get("series").value > 0;
	}

	descricaoMetodoExecucao(atividade) {
		let retorno = "";
		if (atividade.metodoExecucao) {
			if (atividade.setAtividade1 || atividade.setAtividade2) {
				retorno += "Com ";
			}
			if (atividade.setAtividade1) {
				retorno += atividade.setAtividade1.nome;
			}
			if (atividade.setAtividade2) {
				retorno += " e " + atividade.setAtividade2.nome;
			}
		}
		return retorno;
	}

	private verificarSeOuveModificacaoSet(
		atividadeDesatualizada: FormGroup,
		atividadeAtualizada: FormGroup
	) {
		const atividadeSet1Desatualizada =
			atividadeDesatualizada.get("atividadeSet1").value;
		const atividadeSet2Desatualizada =
			atividadeDesatualizada.get("atividadeSet2").value;

		const atividadeSet1Atualizada =
			atividadeAtualizada.get("atividadeSet1").value;
		const atividadeSet2Atualizada =
			atividadeAtualizada.get("atividadeSet2").value;

		if (
			parseInt(atividadeSet1Desatualizada, 10) !==
			parseInt(atividadeSet1Atualizada, 10)
		) {
			if (atividadeSet1Desatualizada) {
				this.removerRelacionamentoSet(atividadeSet1Desatualizada);
			}
		}
		if (
			parseInt(atividadeSet2Desatualizada, 10) !==
			parseInt(atividadeSet2Atualizada, 10)
		) {
			if (atividadeSet2Desatualizada) {
				this.removerRelacionamentoSet(atividadeSet2Desatualizada);
			}
		}
	}

	private atualizarOrdemAtividades() {
		this.listAtividades.forEach((atividadeFicha) => {
			atividadeFicha.sequencia =
				this.configurarFichaService.obterOrdemAtividade(atividadeFicha.frontId);
		});
		let ordemAtividade1: number;
		let ordemAtividade2: number;
		this.listAtividades = this.listAtividades.sort((atividade1, atividade2) => {
			ordemAtividade1 = atividade1.sequencia;
			ordemAtividade2 = atividade2.sequencia;
			if (ordemAtividade1 > ordemAtividade2) {
				return 1;
			} else if (ordemAtividade1 < ordemAtividade2) {
				return -1;
			} else {
				return 0;
			}
		});
	}

	private removerRelacionamentoSet(frontId: number) {
		const atividadeFichaForm: FormGroup = this.obterAtividadeFrontId(frontId);
		atividadeFichaForm
			.get("metodoExecucao")
			.setValue(null, { onlySelf: false, emitEvent: false });
		atividadeFichaForm
			.get("atividadeSet1")
			.setValue(0, { onlySelf: false, emitEvent: false });
		atividadeFichaForm
			.get("atividadeSet2")
			.setValue(0, { onlySelf: false, emitEvent: false });
		this.configurarFichaService.atualizarAtividadeFicha(
			atividadeFichaForm.get("frontId").value,
			atividadeFichaForm,
			true
		);
	}

	private atualizarRelacionamentoBiSet(atividadeSet1: FormGroup) {
		const atividadePrincipal = this.obterAtividadeFrontId(
			parseInt(atividadeSet1.get("atividadeSet1").value, 10)
		);
		atividadePrincipal
			.get("metodoExecucao")
			.setValue(atividadeSet1.get("metodoExecucao").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadePrincipal
			.get("atividadeSet1")
			.setValue(atividadeSet1.get("frontId").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadePrincipal
			.get("atividadeSet2")
			.setValue(0, { onlySelf: false, emitEvent: false });
		this.configurarFichaService.atualizarAtividadeFicha(
			atividadePrincipal.get("frontId").value,
			atividadePrincipal,
			true
		);
	}

	private atualizarRelacionamentoBiSetV2(
		atividadeSet1: FormGroup,
		frontId: number
	) {
		const atividadePrincipal = this.obterAtividadeFrontId(
			parseInt(atividadeSet1.get("setAtividade1").value, 10)
		);
		atividadePrincipal
			.get("metodoExecucao")
			.setValue(atividadeSet1.get("metodoExecucao").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadePrincipal
			.get("atividadeSet1")
			.setValue(frontId, { onlySelf: false, emitEvent: false });
		atividadePrincipal
			.get("atividadeSet2")
			.setValue(0, { onlySelf: false, emitEvent: false });
		this.configurarFichaService.atualizarAtividadeFicha(
			atividadePrincipal.get("frontId").value,
			atividadePrincipal,
			true
		);
	}

	private atualizarRelacionamentoTriSet(
		atividadeSet1: FormGroup,
		frontId: number
	) {
		const atividadePrimaria = this.obterAtividadeFrontId(
			parseInt(atividadeSet1.get("setAtividade1").value, 10)
		);
		const atividadeSecundaria = this.obterAtividadeFrontId(
			parseInt(atividadeSet1.get("setAtividade2").value, 10)
		);

		atividadePrimaria
			.get("metodoExecucao")
			.setValue(atividadeSet1.get("metodoExecucao").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadePrimaria
			.get("atividadeSet1")
			.setValue(frontId, { onlySelf: false, emitEvent: false });
		atividadePrimaria
			.get("atividadeSet2")
			.setValue(atividadeSecundaria.get("frontId").value, {
				onlySelf: false,
				emitEvent: false,
			});
		this.configurarFichaService.atualizarAtividadeFicha(
			atividadePrimaria.get("frontId").value,
			atividadePrimaria,
			true
		);

		atividadeSecundaria
			.get("metodoExecucao")
			.setValue(atividadeSet1.get("metodoExecucao").value, {
				onlySelf: false,
				emitEvent: false,
			});
		atividadeSecundaria
			.get("atividadeSet1")
			.setValue(frontId, { onlySelf: false, emitEvent: false });
		atividadeSecundaria
			.get("atividadeSet2")
			.setValue(atividadePrimaria.get("frontId").value, {
				onlySelf: false,
				emitEvent: false,
			});
		this.configurarFichaService.atualizarAtividadeFicha(
			atividadeSecundaria.get("frontId").value,
			atividadeSecundaria,
			true
		);
	}

	atualizarSeriesSet(atividade) {
		const atividadeSet1: FormGroup = atividade.formGroup;
		if (
			atividadeSet1.get("setAtividade1") &&
			atividadeSet1.get("setAtividade1").value
		) {
			const id1 = parseInt(atividadeSet1.get("setAtividade1").value, 10);
			this.listAtividades.forEach((atv) => {
				if (atv.frontId === id1) {
					atv.formGroup
						.get("series")
						.setValue(atividadeSet1.get("series").value);
				}
			});
		}
		if (
			atividadeSet1.get("setAtividade2") &&
			atividadeSet1.get("setAtividade2").value
		) {
			const id2 = parseInt(atividadeSet1.get("setAtividade2").value, 10);
			this.listAtividades.forEach((atv) => {
				if (atv.frontId === id2) {
					atv.formGroup
						.get("series")
						.setValue(atividadeSet1.get("series").value);
				}
			});
		}
		this.validarSeSeriesEhMaiorQueZero(atividade);
	}

	validarSeSeriesEhMaiorQueZero(atividade) {
		const atividadeSet1: FormGroup = atividade.formGroup;
		const desabilitar = parseInt(atividadeSet1.get("series").value, 10) <= 0;
		const sufixoId = atividade.frontId - 1;
		const listIds = [
			"input-duracao-atividade-" + sufixoId,
			"input-repeticoes-atividade-" + sufixoId,
			"input-carga-atividade-" + sufixoId,
			"input-velocidade-atividade-" + sufixoId,
			"input-distancia-atividade-" + sufixoId,
			"input-descanso-atividade-" + sufixoId,
			"input-descanso-atividade-detalhe-" + sufixoId,
			"input-complemento-repeticoes-detalhe-" + sufixoId,
			"input-complemento-carga-detalhe-" + sufixoId,
			"input-duracao-atividade-detalhe-" + sufixoId,
			"select-esforco-atividade-" + sufixoId,
			"input-cadencia-atividade-detalhe-" + sufixoId,
			"input-velocidade-atividade-detalhe-" + sufixoId,
			"input-distancia-atividade-detalhe-" + sufixoId,
			"input-complemento-atividade-" + sufixoId,
		];

		listIds.forEach((id) => {
			this.desabilitarInput(id, desabilitar);
		});
	}

	desabilitarInput(inputId, desabilitar) {
		const input = document.getElementById(inputId) as HTMLInputElement;
		if (input) {
			input.disabled = desabilitar;
		}
	}

	private setupList(atividadesFicha) {
		this.setupMetodo();
		this.listAtividades = atividadesFicha;
		this.eventCampos();
		this.atualizarOrdemAtividades();
		this.loadListItensAtividades();
		this.cd.detectChanges();
		this.listAtividades.forEach((atividadeFicha) => {
			this.validarSeSeriesEhMaiorQueZero(atividadeFicha);
		});
	}

	private loadListItensAtividades() {
		this.listaItensAtividades = [];
		this.listaItensAtividades.push({ id: null, nome: "" });
		for (const item of this.listAtividades) {
			this.listaItensAtividades.push({ id: item.frontId, nome: item.nome });
		}
	}

	atividadesSet(frontId: number): Array<any> {
		return this.listaItensAtividades.filter((i) => i.id !== frontId);
	}

	private eventCampos() {
		this.listAtividades.forEach((atividadeFichaRow) => {
			atividadeFichaRow.formGroup.valueChanges
				.pipe(debounceTime(500))
				.subscribe((valueForm) => {
					this.atualizarCampos(
						valueForm,
						atividadeFichaRow.frontId,
						atividadeFichaRow.seriesEditadas
					);
				});
		});
	}

	private nomeAtividadeSet(frontId) {
		let atividadeSetNome = "";
		if (frontId) {
			const atividadesFichaForm = (
				this.configurarFichaService.fichaFormGroup.get(
					"atividades"
				) as FormArray
			).controls;
			atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
				if (
					parseInt(atividadeFichaForm.get("frontId").value, 10) ===
					parseInt(frontId, 10)
				) {
					const atividadeFicha = this.configurarFichaService.obterAtividadeMap(
						atividadeFichaForm.get("atividadeId").value
					);
					atividadeSetNome = atividadeFicha.nome;
				}
			});
		}

		return atividadeSetNome;
	}

	private atualizarCampos(
		valueForm: any,
		frontId: number,
		seriesEditadas: any
	) {
		const atividadeFichaForm = this.obterAtividadeFrontId(frontId);
		const seriesForm = (atividadeFichaForm.get("series") as FormArray).controls;
		seriesForm.forEach((serieForm) => {
			if (!seriesEditadas.repeticao) {
				serieForm.get("repeticoes").setValue(valueForm.repeticoes, {
					onlySelf: false,
					emitEvent: false,
				});
			}
			if (!seriesEditadas.carga) {
				serieForm
					.get("carga")
					.setValue(valueForm.carga, { onlySelf: false, emitEvent: false });
			}
			if (!seriesEditadas.velocidade) {
				serieForm.get("velocidade").setValue(valueForm.velocidade, {
					onlySelf: false,
					emitEvent: false,
				});
			}
			if (!seriesEditadas.distancia) {
				serieForm
					.get("distancia")
					.setValue(valueForm.distancia, { onlySelf: false, emitEvent: false });
			}
			if (!seriesEditadas.duracao) {
				if (valueForm.duracao) {
					serieForm
						.get("duracao")
						.setValue(
							this.utils.convertMinutesSecondsIntoSeconds(valueForm.duracao),
							{ onlySelf: false, emitEvent: false }
						);
				}
			}
			if (!seriesEditadas.descanso) {
				if (valueForm.descanso) {
					serieForm
						.get("descanso")
						.setValue(
							this.utils.convertMinutesSecondsIntoSeconds(valueForm.descanso),
							{ onlySelf: false, emitEvent: false }
						);
				}
			}
			if (!seriesEditadas.cadencia) {
				serieForm
					.get("cadencia")
					.setValue(valueForm.cadencia, { onlySelf: false, emitEvent: false });
			}
			serieForm
				.get("complemento")
				.setValue(valueForm.complemento, { onlySelf: false, emitEvent: false });
		});
		if (parseInt(valueForm.series, 10) > seriesForm.length) {
			this.addSerie(atividadeFichaForm, parseInt(valueForm.series, 10));
		} else if (valueForm.series < seriesForm.length) {
			this.removerSerie(atividadeFichaForm, parseInt(valueForm.series, 10));
		}
		atividadeFichaForm.value.metodoExecucao = valueForm.metodoExecucao;
		atividadeFichaForm.value.esforco = valueForm.esforco;
		atividadeFichaForm.value.atividadeSet1 = valueForm.setAtividade1;

		atividadeFichaForm.patchValue({
			metodoExecucao: valueForm.metodoExecucao,
			esforco: valueForm.esforco,
			complementoNomeAtividade: valueForm.complementoNomeAtividade,
			atividadeSet1: valueForm.setAtividade1,
		});
		this.configurarFichaService.atualizarAtividadeFicha(
			frontId,
			atividadeFichaForm,
			false
		);
	}

	private addSerie(
		atividadeFichaFormEspelho: FormGroup,
		quantSerieAtualizar: number
	) {
		const seriesForm = atividadeFichaFormEspelho.get("series") as FormArray;
		while (seriesForm.controls.length < quantSerieAtualizar) {
			if (seriesForm.controls.length > 0) {
				const copiaSerie: FormGroup = seriesForm.controls[0] as FormGroup;
				const novaSerie = buildSerieAtividadeForm();
				novaSerie.patchValue(copiaSerie.getRawValue());
				novaSerie.get("id").setValue(null);
				novaSerie
					.get("sequencia")
					.setValue(this.verificarProximaSequencia(seriesForm));
				seriesForm.push(novaSerie);
			} else {
				const serieForm = buildSerieAtividadeForm();
				serieForm
					.get("sequencia")
					.setValue(this.verificarProximaSequencia(seriesForm));
				seriesForm.push(buildSerieAtividadeForm());
			}
		}
	}

	private verificarProximaSequencia(seriesForm: FormArray): number {
		let result = 0;
		seriesForm.controls.forEach((serieForm: FormGroup) => {
			if (result <= parseInt(serieForm.get("sequencia").value, 10)) {
				result = parseInt(serieForm.get("sequencia").value, 10);
			}
		});
		return result + 1;
	}

	private removerSerie(
		atividadeFichaForm: FormGroup,
		quantSerieAtualizar: number
	) {
		const seriesForm = atividadeFichaForm.get("series") as FormArray;
		while (seriesForm.controls.length > quantSerieAtualizar) {
			seriesForm.removeAt(seriesForm.controls.length - 1);
		}
	}

	private buildSeriesForm(atividadeFichaForm: FormGroup): FormGroup {
		const resultForm: FormGroup = new FormGroup({
			series: new FormControl(),
			repeticoes: new FormControl(),
			carga: new FormControl(),
			velocidade: new FormControl(),
			duracao: new FormControl(),
			distancia: new FormControl(),
			descanso: new FormControl(),
			cadencia: new FormControl(),
			complemento: new FormControl(),
		});
		const quantSeries = (atividadeFichaForm.get("series") as FormArray).controls
			.length;
		resultForm.get("series").setValue(quantSeries);
		(atividadeFichaForm.get("series") as FormArray).controls.forEach(
			(serieForm: FormGroup, index) => {
				if (index === 0) {
					resultForm.patchValue(serieForm.getRawValue());
					resultForm
						.get("duracao")
						.setValue(
							this.utils.convertSecondsIntoMinutesSecondsLabel(
								resultForm.get("duracao").value
							)
						);
					resultForm
						.get("descanso")
						.setValue(
							this.utils.convertSecondsIntoMinutesSecondsLabel(
								resultForm.get("descanso").value
							)
						);
				}
			}
		);

		return resultForm;
	}

	private obterAtividadeFrontId(frontId): FormGroup {
		const atividadesFichaForm = this.configurarFichaService.fichaFormGroup.get(
			"atividades"
		) as FormArray;
		const formRetorno: FormGroup = buildAtividadeFichaForm();
		atividadesFichaForm.controls.forEach((atividadeFichaForm: FormGroup) => {
			if (
				parseInt(atividadeFichaForm.get("frontId").value, 10) ===
				parseInt(frontId, 10)
			) {
				formRetorno.patchValue(atividadeFichaForm.getRawValue(), {
					onlySelf: false,
					emitEvent: false,
				});
				(atividadeFichaForm.get("series") as FormArray).controls.forEach(
					(serieForm: FormGroup) => {
						const serieFormBuild = buildSerieAtividadeForm();
						serieFormBuild.patchValue(serieForm.getRawValue(), {
							onlySelf: false,
							emitEvent: false,
						});
						(formRetorno.get("series") as FormArray).push(serieFormBuild);
					}
				);
			}
		});
		return formRetorno;
	}

	private reordenarArray(previous: number, current: number) {
		this.configurarFichaService.fichaSendoModificada$.next(true);
		const removed = this.listAtividades.splice(previous, 1)[0];
		this.listAtividades.splice(current, 0, removed);

		const novaOrdemAtividades: Array<{ frontId: number; ordem: number }> = [];
		this.listAtividades.forEach((atividade, index) => {
			novaOrdemAtividades.push({
				frontId: atividade.frontId,
				ordem: index + 1,
			});
		});
		this.configurarFichaService.atualizarOrdemAtividades(novaOrdemAtividades);
	}

	get nrAtividades() {
		return this.listAtividades ? this.listAtividades.length : 0;
	}

	changeValue() {
		this.configurarFichaService.fichaSendoModificada$.next(true);
	}

	private setupMetodo() {
		this.metodosExecucoes = [];
		this.metodosExecucoes.push({
			id: "NAO_ATRIBUIDO",
			nome: this.metodoExecucaoTraducao.getLabel("naoAtribuido"),
		});
		this.metodosExecucoes.push({
			id: AtividadeMetodoExecucao.BI_SET,
			nome: this.metodoExecucaoTraducao.getLabel(
				AtividadeMetodoExecucao.BI_SET
			),
		});
		this.metodosExecucoes.push({
			id: AtividadeMetodoExecucao.TRI_SET,
			nome: this.metodoExecucaoTraducao.getLabel(
				AtividadeMetodoExecucao.TRI_SET
			),
		});
		const outrosMetodos = [];
		Object.keys(AtividadeMetodoExecucao).forEach((metodoExecucao) => {
			if (
				metodoExecucao !== AtividadeMetodoExecucao.BI_SET &&
				metodoExecucao !== AtividadeMetodoExecucao.TRI_SET
			) {
				outrosMetodos.push({
					id: metodoExecucao,
					nome: this.metodoExecucaoTraducao.getLabel(metodoExecucao),
				});
			}
		});
		outrosMetodos.sort((a, b) => (a.nome < b.nome ? -1 : 1));
		this.metodosExecucoes = this.metodosExecucoes.concat(outrosMetodos);
	}

	detalharTodos() {
		this.listAtividades.forEach((at: any) => {
			at.aberto = true;
		});
		this.cd.detectChanges();
	}

	get todosAbertos(): boolean {
		const aberto = (at) => at.aberto;
		return this.listAtividades.every(aberto);
	}

	recolherTodos() {
		this.listAtividades.forEach((at: any) => {
			at.aberto = false;
		});
		this.cd.detectChanges();
	}

	changeMetodo(atividade) {
		if (
			atividade.formGroup.get("metodoExecucao").value === "BI_SET" ||
			atividade.formGroup.get("metodoExecucao").value === "TRI_SET"
		) {
			this.detalhar(atividade);
		}
	}

	validarTamanhoDetalhar(atividade, campo, idxFocus) {
		if (atividade.formGroup.get(campo).value.length > 2) {
			this.detalhar(atividade);
			this.textAreas
				.find((item, idx) => {
					return item.nativeElement.id === idxFocus;
				})
				.nativeElement.focus();
		}
	}

	detalhar(atividade) {
		atividade.aberto = true;
		this.cd.detectChanges();
		this.validarSeSeriesEhMaiorQueZero(atividade);
	}

	editarComplementoNome(atividade) {
		atividade.editComplementoNomeAtividade = true;
		this.cd.detectChanges();
	}

	recolherComplementoNome(atividade) {
		atividade.editComplementoNomeAtividade = false;
		atividade.complementoNomeAtividade = atividade.formGroup.get(
			"complementoNomeAtividade"
		).value;
		this.cd.detectChanges();
	}

	recolher(atividade) {
		atividade.aberto = false;
		this.cd.detectChanges();
		this.validarSeSeriesEhMaiorQueZero(atividade);
	}
}
