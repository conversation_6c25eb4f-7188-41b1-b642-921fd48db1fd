<div [ngClass]="colunasGrid" [ngStyle]="customizarStyle">
	<div class="title-card-numero-exercicio row">
		<div class="col-md-4 pl-0">
			<pacto-cat-button
				(click)="replicarSeriesRepeticoes()"
				[disabled]="!permissaoProgramaTreino?.editar"
				[icon]="'pct pct-copy'"
				[v2]="true"
				i18n="@@perfil-aluno-programa-editar:replicar-series"
				id="replicar-series-repeticoes"
				label="Replicar séries e repetições"></pacto-cat-button>
		</div>
		<div class="col-md-4 text-center">
			{{ nrAtividades }} atividades na ficha
		</div>
		<div class="col-md-4 text-right pr-0">
			<pacto-cat-button
				(click)="detalharTodos()"
				*ngIf="!todosAbertos"
				[icon]="'pct pct-chevrons-down'"
				[type]="'SECUNDARY'"
				[v2]="true"
				class="detalhar-todos"
				label="Detalhar todos"></pacto-cat-button>

			<pacto-cat-button
				(click)="recolherTodos()"
				*ngIf="todosAbertos"
				[icon]="'pct pct-chevrons-up'"
				[type]="'SECUNDARY'"
				[v2]="true"
				class="detalhar-todos"
				label="Recolher todos"></pacto-cat-button>
		</div>
	</div>

	<div
		(cdkDropListDropped)="drop($event)"
		cdkDropList
		class="content-lista-atividade">
		<ng-container
			*ngFor="let atividade of listAtividades; let indexAtividade = index">
			<div
				*ngIf="atividade.aberto && atividade.aberto === true"
				cdkDrag
				class="row-atividade detalhado">
				<div class="infos-atividade">
					<div cdkDragHandle class="drag-handler">
						<div class="colum-icon">
							<i class="pct pct-move-2"></i>
						</div>
					</div>
					<div class="block-info-atividade">
						<div class="display-flex-important">
							<div class="imagem-detalhe">
								<img
									*ngIf="atividade.imageUri"
									class="img-blur"
									src="{{ atividade.imageUri }}" />
								<div class="imagem-atividade">
									<img
										*ngIf="atividade.imageUri"
										src="{{ atividade.imageUri }}" />
									<img
										*ngIf="!atividade.imageUri"
										src="assets/images/image-noimage.png" />
								</div>
							</div>

							<div class="componente-corpo-grupo-muscular">
								<div class="frontal">
									<pacto-corpo-frontal-grupo-muscular
										*ngIf="allGruposMusculares"
										[listaAtiva]="atividade.gruposMusculares"
										[listaCompletaGrupoMuscular]="allGruposMusculares"
										[podeSelecionarGrupoMuscular]="
											false
										"></pacto-corpo-frontal-grupo-muscular>
								</div>

								<div class="posterior">
									<pacto-corpo-posterior-grupo-muscular
										*ngIf="allGruposMusculares"
										[listaAtiva]="atividade.gruposMusculares"
										[listaCompletaGrupoMuscular]="allGruposMusculares"
										[podeSelecionarGrupoMuscular]="
											false
										"></pacto-corpo-posterior-grupo-muscular>
								</div>
							</div>
						</div>

						<div class="info-atividade">
							<div
								class="descricao-atividade"
								title="NOME ORIGINAL: {{ atividade.nome }}">
								<span
									[hidden]="
										!atividade.complementoNomeAtividade ||
										atividade.editComplementoNomeAtividade
									">
									{{ atividade.nomeAtivFicha }} ({{
										atividade.complementoNomeAtividade
									}})
								</span>
								<span
									[hidden]="
										atividade.complementoNomeAtividade &&
										!atividade.editComplementoNomeAtividade
									">
									{{ atividade.nomeAtivFicha }}
								</span>
								<i
									(click)="editarComplementoNome(atividade)"
									*ngIf="!atividade.editComplementoNomeAtividade"
									class="pct pct-edit-3"
									title="Editar detalhes da atividade"></i>

								<input
									(keyup.enter)="recolherComplementoNome(atividade)"
									*ngIf="atividade.editComplementoNomeAtividade"
									[formControl]="
										atividade.formGroup.get('complementoNomeAtividade')
									"
									id="input-complementoNomeAtividade-detalhe-{{
										indexAtividade
									}}"
									type="text" />
							</div>
						</div>
					</div>
				</div>
				<div class="content-atividade">
					<div>
						<div class="caixa-delta">
							<div class="caixa-alpha">
								<label>Método</label>
								<pacto-cat-form-select
									(change)="atualizarMetodoSet(atividade)"
									[control]="atividade.formGroup.get('metodoExecucao')"
									[idKey]="'id'"
									[id]="'select-metodo-execucao-' + indexAtividade"
									[items]="metodosExecucoes"
									[labelKey]="'nome'"
									class="metodoExecucao"
									i18n-label="
										@@montagem-treino-atividade-ficha:metodo-execucao:metodoExecucao"></pacto-cat-form-select>
							</div>
							<div class="caixa-beta bi-set">
								<label
									*ngIf="
										atividade.formGroup.get('metodoExecucao').value === 'BI_SET'
									">
									Bi-set com a atividade
								</label>
								<label
									*ngIf="
										atividade.formGroup.get('metodoExecucao').value ===
										'TRI_SET'
									">
									Tri-set com a atividade
								</label>
								<pacto-cat-form-select
									(change)="atualizarAtividadeSet(atividade, atividade.frontId)"
									*ngIf="
										atividade.formGroup.get('metodoExecucao').value ===
											'BI_SET' ||
										atividade.formGroup.get('metodoExecucao').value ===
											'TRI_SET'
									"
									[control]="atividade.formGroup.get('setAtividade1')"
									[idKey]="'id'"
									[id]="'select-atividadese-1' + indexAtividade"
									[items]="atividadesSet(atividade.frontId)"
									[labelKey]="'nome'"
									class="metodoExecucao"
									i18n-label="
										@@montagem-treino-atividade-ficha:metodo-execucao:atividade01"></pacto-cat-form-select>
							</div>
						</div>
						<div class="caixa-gama">
							<div class="caixa-beta tri-set">
								<label
									*ngIf="
										atividade.formGroup.get('metodoExecucao').value ===
										'TRI_SET'
									">
									Tri-set com a atividade
								</label>
								<pacto-cat-form-select
									(change)="atualizarAtividadeSet(atividade, atividade.frontId)"
									*ngIf="
										atividade.formGroup.get('metodoExecucao').value ===
										'TRI_SET'
									"
									[control]="atividade.formGroup.get('setAtividade2')"
									[idKey]="'id'"
									[id]="'select-atividadese-2' + indexAtividade"
									[items]="atividadesSet(atividade.frontId)"
									[labelKey]="'nome'"
									class="metodoExecucao"
									i18n-label="
										@@montagem-treino-atividade-ficha:metodo-execucao:atividade01"></pacto-cat-form-select>
							</div>
							<div class="text-right detalhes-atividade">
								<pacto-cat-button
									(click)="recolher(atividade)"
									[icon]="'pct pct-chevrons-up'"
									[type]="'SECUNDARY'"
									[v2]="true"
									i18n="@@perfil-aluno-programa-editar:replicar-series"
									label="Recolher"></pacto-cat-button>
								<i
									(click)="removeHandler(atividade.frontId)"
									*ngIf="permissaoProgramaTreino?.editar"
									class="pct pct-trash-2"
									title="Remover atividade da ficha"></i>
							</div>
						</div>
					</div>
					<div>
						<div
							class="caixa-delta {{
								atividade.serieApenasDuracao ? 'serieApenasDuracao' : ''
							}}">
							<div class="col-left">
								<label>Séries</label>
								<div class="col-ajuste-serie">
									<input
										(keyup)="atualizarSeriesSet(atividade)"
										[formControl]="atividade.formGroup.get('series')"
										[placeholder]="'Series'"
										[textMask]="{ guide: false, mask: somenteInteiroMask }"
										id="input-series-atividade-detalhe-{{ indexAtividade }}"
										maxlength="2"
										type="text" />
									<span
										(click)="abrirEditorSeries(atividade.frontId)"
										[ngClass]="{
											seriesEditadas: atividade.seriesEditadas.seriesDiferentes
										}"
										class="customizar"
										title="Customização de séries">
										<span
											*ngIf="atividade.seriesEditadas.seriesDiferentes"
											class="bola"></span>
										<i class="pct pct-sliders"></i>
									</span>
								</div>

								<label style="margin-top: 9px">Descanso</label>
								<input
									[attr.disabled]="
										atividade.seriesEditadas.descanso ? '' : null
									"
									[attr.title]="
										atividade.seriesEditadas.descanso
											? 'Edite na customização de séries'
											: null
									"
									[formControl]="atividade.formGroup.get('descanso')"
									[textMask]="{ guide: false, mask: minuteSecondMask }"
									class="descanso"
									i18n-placeholder="
										@@montagem-treino-atividade-ficha:descanso:label"
									id="input-descanso-atividade-detalhe-{{ indexAtividade }}"
									placeholder="mm:ss"
									type="text" />
							</div>
							<div
								*ngIf="atividade.tipo === 'NEUROMUSCULAR'"
								class="col-center">
								<label>Repetições</label>
								<textarea
									#txtArea
									[attr.disabled]="
										atividade.seriesEditadas.repeticao ? '' : null
									"
									[formControl]="atividade.formGroup.get('repeticoes')"
									i18n-placeholder="
										@@montagem-treino-atividade-ficha:complemento:label"
									id="input-complemento-repeticoes-detalhe-{{ indexAtividade }}"
									title="{{ atividade.formGroup.get('complemento').value }}"
									type="text"></textarea>
							</div>
							<div
								*ngIf="atividade.tipo === 'NEUROMUSCULAR'"
								class="col-center">
								<label>Carga</label>
								<textarea
									#txtArea
									[attr.disabled]="atividade.seriesEditadas.carga ? '' : null"
									[attr.title]="
										atividade.seriesEditadas.carga
											? 'Edite na customização de séries'
											: null
									"
									[formControl]="atividade.formGroup.get('carga')"
									i18n-placeholder="
										@@montagem-treino-atividade-ficha:complemento:label"
									id="input-complemento-carga-detalhe-{{ indexAtividade }}"
									type="text"></textarea>
							</div>

							<div
								class="{{
									atividade.tipo === 'NEUROMUSCULAR'
										? 'col-right'
										: 'col-center'
								}}">
								<label *ngIf="atividade.tipo === 'CARDIOVASCULAR'">
									Duração
								</label>

								<input
									*ngIf="atividade.tipo === 'CARDIOVASCULAR'"
									[attr.disabled]="atividade.seriesEditadas.duracao ? '' : null"
									[attr.title]="
										atividade.seriesEditadas.duracao
											? 'Edite na customização de séries'
											: null
									"
									[formControl]="atividade.formGroup.get('duracao')"
									[textMask]="{ guide: false, mask: minuteSecondMask }"
									id="input-duracao-atividade-detalhe-{{ indexAtividade }}"
									placeholder="mm:ss"
									type="text" />

								<label
									*ngIf="atividade.tipo === 'CARDIOVASCULAR'"
									style="margin-top: 9px">
									Esforço
								</label>
								<label *ngIf="atividade.tipo === 'NEUROMUSCULAR'">
									Esforço
								</label>
								<pacto-cat-form-select
									(change)="changeValue()"
									[control]="atividade.formGroup.get('esforco')"
									[idKey]="'id'"
									[id]="'select-esforco-atividade-' + indexAtividade"
									[items]="esforcos"
									[labelKey]="'nome'"
									class="metodoExecucao"
									i18n-label="
										@@montagem-treino-atividade-ficha:metodo-execucao:esforco"></pacto-cat-form-select>

								<label
									*ngIf="atividade.tipo === 'NEUROMUSCULAR'"
									style="margin-top: 9px">
									Cadência
								</label>
								<input
									*ngIf="atividade.tipo === 'NEUROMUSCULAR'"
									[attr.disabled]="
										atividade.seriesEditadas.cadencia ? '' : null
									"
									[attr.title]="
										atividade.seriesEditadas.cadencia
											? 'Edite na customização de séries'
											: null
									"
									[formControl]="atividade.formGroup.get('cadencia')"
									class="cadencia"
									id="input-cadencia-atividade-detalhe-{{ indexAtividade }}"
									maxlength="6"
									type="text" />
							</div>

							<div
								*ngIf="
									atividade.tipo === 'CARDIOVASCULAR' &&
									atividade.serieApenasDuracao === false
								"
								class="caixa-alpha cardio">
								<label>Velocidade</label>
								<input
									[attr.disabled]="
										atividade.seriesEditadas.velocidade ? '' : null
									"
									[formControl]="atividade.formGroup.get('velocidade')"
									[textMask]="{ guide: false, mask: somenteInteiroMask }"
									id="input-velocidade-atividade-detalhe-{{ indexAtividade }}"
									placeholder="km/h"
									type="text" />
								<label style="margin-top: 9px">Distância</label>
								<input
									[attr.disabled]="
										atividade.seriesEditadas.distancia ? '' : null
									"
									[formControl]="atividade.formGroup.get('distancia')"
									[textMask]="{ guide: false, mask: somenteInteiroMask }"
									id="input-distancia-atividade-detalhe-{{ indexAtividade }}"
									placeholder="m"
									type="number" />
							</div>
						</div>
						<div
							class="caixa-gama {{
								atividade.serieApenasDuracao ? 'serieApenasDuracao' : ''
							}}">
							<label>Complemento da atividade</label>
							<textarea
								[attr.disabled]="
									!campoHabilitado(atividade.formGroup) ? '' : null
								"
								[formControl]="atividade.formGroup.get('complemento')"
								i18n-placeholder="
									@@montagem-treino-atividade-ficha:complemento:label"
								id="input-complemento-atividade-{{ indexAtividade }}"
								placeholder="Exemplo: 5 20 30 peito"
								title="{{ atividade.formGroup.get('complemento').value }}"
								type="text"></textarea>
						</div>
					</div>
				</div>
			</div>

			<div *ngIf="!atividade.aberto || atividade.aberto === false" cdkDrag>
				<div class="row-atividade">
					<div class="colunas-atividade">
						<div class="infos-atividade">
							<div cdkDragHandle class="drag-handler">
								<div class="colum-icon">
									<i class="pct pct-move-2"></i>
								</div>
							</div>
							<div class="block-info-atividade">
								<pacto-cat-person-avatar
									[borderRadius]="4"
									[diameter]="56"
									[uri]="
										atividade.imageUri
											? atividade.imageUri
											: 'assets/images/empty-image.png'
									"></pacto-cat-person-avatar>
								<div class="info-atividade">
									<div
										class="descricao-atividade"
										title="NOME ORIGINAL: {{ atividade.nome }}">
										<span
											[hidden]="
												!atividade.complementoNomeAtividade ||
												atividade.editComplementoNomeAtividade
											">
											{{ atividade.nomeAtivFicha }} ({{
												atividade.complementoNomeAtividade
											}})
										</span>
										<span
											[hidden]="
												atividade.complementoNomeAtividade &&
												!atividade.editComplementoNomeAtividade
											">
											{{ atividade.nomeAtivFicha }}
										</span>
										<i
											(click)="editarComplementoNome(atividade)"
											*ngIf="!atividade.editComplementoNomeAtividade"
											class="pct pct-edit-3"
											title="Editar detalhes da atividade"></i>

										<input
											(keyup.enter)="recolherComplementoNome(atividade)"
											*ngIf="atividade.editComplementoNomeAtividade"
											[formControl]="
												atividade.formGroup.get('complementoNomeAtividade')
											"
											id="input-complementoNomeAtividade-{{ indexAtividade }}"
											type="text" />
									</div>
								</div>
							</div>
							<div class="block-actions"></div>
						</div>
						<div class="campo-serie metodo">
							<label>Método</label>
							<pacto-cat-form-select
								(change)="changeMetodo(atividade)"
								(change)="atualizarMetodoSet(atividade)"
								[control]="atividade.formGroup.get('metodoExecucao')"
								[idKey]="'id'"
								[id]="'select-metodo-execucao-' + indexAtividade"
								[items]="metodosExecucoes"
								[labelKey]="'nome'"
								class="metodoExecucao"
								i18n-label="
									@@montagem-treino-atividade-ficha:metodo-execucao:metodoExecucao"></pacto-cat-form-select>
						</div>
						<div class="campo-serie">
							<label i18n="@@montagem-treino-atividade-ficha:series:label">
								Séries
							</label>
							<input
								(keyup)="atualizarSeriesSet(atividade)"
								[formControl]="atividade.formGroup.get('series')"
								[placeholder]="'Series'"
								[textMask]="{ guide: false, mask: somenteInteiroMask }"
								id="input-series-atividade-{{ indexAtividade }}"
								maxlength="2"
								type="text" />
						</div>
						<div *ngIf="atividade.tipo === 'NEUROMUSCULAR'" class="campo-serie">
							<label i18n="@@montagem-treino-atividade-ficha:repeticoes:label">
								Repetições
							</label>
							<input
								(keyup)="
									validarTamanhoDetalhar(
										atividade,
										'repeticoes',
										'input-complemento-repeticoes-detalhe-' + indexAtividade
									)
								"
								*ngIf="atividade.tipo === 'NEUROMUSCULAR'"
								[attr.disabled]="atividade.seriesEditadas.repeticao ? '' : null"
								[formControl]="atividade.formGroup.get('repeticoes')"
								i18n-placeholder="
									@@montagem-treino-atividade-ficha:repeticoes:label"
								id="input-repeticoes-atividade-{{ indexAtividade }}"
								type="text" />
						</div>
						<div
							*ngIf="atividade.tipo === 'CARDIOVASCULAR'"
							class="campo-serie">
							<label>Duração</label>
							<input
								*ngIf="atividade.tipo === 'CARDIOVASCULAR'"
								[attr.disabled]="atividade.seriesEditadas.duracao ? '' : null"
								[formControl]="atividade.formGroup.get('duracao')"
								[textMask]="{ guide: false, mask: minuteSecondMask }"
								id="input-duracao-atividade-{{ indexAtividade }}"
								placeholder="mm:ss"
								type="text" />
						</div>
						<div *ngIf="atividade.tipo === 'NEUROMUSCULAR'" class="campo-serie">
							<label>Carga</label>
							<input
								(keyup)="
									validarTamanhoDetalhar(
										atividade,
										'carga',
										'input-complemento-carga-detalhe-' + indexAtividade
									)
								"
								*ngIf="atividade.tipo === 'NEUROMUSCULAR'"
								[attr.disabled]="atividade.seriesEditadas.carga ? '' : null"
								[formControl]="atividade.formGroup.get('carga')"
								i18n-placeholder="@@montagem-treino-atividade-ficha:carga:label"
								id="input-carga-atividade-{{ indexAtividade }}"
								placeholder="kg"
								type="text" />
						</div>
						<div
							*ngIf="atividade.tipo === 'CARDIOVASCULAR'"
							class="campo-serie">
							<label *ngIf="atividade.serieApenasDuracao === false">
								Velocidade
							</label>
							<input
								*ngIf="atividade.serieApenasDuracao === false"
								[attr.disabled]="
									atividade.seriesEditadas.velocidade ? '' : null
								"
								[formControl]="atividade.formGroup.get('velocidade')"
								[textMask]="{ guide: false, mask: somenteInteiroMask }"
								id="input-velocidade-atividade-{{ indexAtividade }}"
								placeholder="km/h"
								type="text" />
						</div>
						<div
							*ngIf="atividade.tipo === 'CARDIOVASCULAR'"
							class="campo-serie">
							<label *ngIf="atividade.serieApenasDuracao === false">
								Distância
							</label>
							<input
								*ngIf="atividade.serieApenasDuracao === false"
								[attr.disabled]="atividade.seriesEditadas.distancia ? '' : null"
								[formControl]="atividade.formGroup.get('distancia')"
								[textMask]="{ guide: false, mask: somenteInteiroMask }"
								id="input-distancia-atividade-{{ indexAtividade }}"
								placeholder="m"
								type="number" />
						</div>
						<div *ngIf="atividade.tipo === 'NEUROMUSCULAR'" class="campo-serie">
							<label>Descanso</label>
							<input
								[attr.disabled]="atividade.seriesEditadas.descanso ? '' : null"
								[formControl]="atividade.formGroup.get('descanso')"
								[textMask]="{ guide: false, mask: minuteSecondMask }"
								i18n-placeholder="
									@@montagem-treino-atividade-ficha:descanso:label"
								id="input-descanso-atividade-{{ indexAtividade }}"
								placeholder="mm:ss"
								type="text" />
						</div>
						<div class="detalhes-atividade">
							<pacto-cat-button
								(click)="detalhar(atividade)"
								[icon]="'pct pct-chevrons-down'"
								[type]="'SECUNDARY'"
								[v2]="true"
								i18n="@@perfil-aluno-programa-editar:replicar-series"
								label="Detalhes"></pacto-cat-button>
							<i
								(click)="removeHandler(atividade.frontId)"
								*ngIf="permissaoProgramaTreino?.editar"
								class="pct pct-trash-2"
								title="Remover atividade da ficha"></i>
						</div>
					</div>
					<div class="row-detalhe-atividade">
						<label
							*ngIf="
								atividade.formGroup.get('metodoExecucao').value === 'BI_SET' ||
								atividade.formGroup.get('metodoExecucao').value === 'TRI_SET'
							">
							{{ descricaoMetodoExecucao(atividade) }}
						</label>
					</div>
				</div>
			</div>
		</ng-container>
	</div>
</div>

<pacto-traducoes-xingling #notificacaoTranslate>
	<span xingling="serie-edit-success">Série editada com sucesso.</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #metodoExecucaoTraducao>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:editarSerie"
		xingling="editarSerie">
		Customizar séries
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:naoAtribuido"
		xingling="naoAtribuido">
		Não atribuído
	</span>
	<span
		i18n="
			@@montagem-treino-atividade-ficha:metodo-execucao:piramide-decrescente"
		xingling="PIRAMIDE_DECRESCENTE">
		Pirâmide Decrescente
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:piramide-crescente"
		xingling="PIRAMIDE_CRESCENTE">
		Pirâmide Crescente
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:circuito"
		xingling="CIRCUITO">
		Circuito
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:isométrico"
		xingling="ISOMETRICO">
		Isométrico
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:super-serie"
		xingling="SUPER_SERIE">
		Super-Série
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:bi-set"
		xingling="BI_SET">
		BI-Set
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:tri-set"
		xingling="TRI_SET">
		TRI-Set
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:drop-set"
		xingling="DROP_SET">
		Drop-Set
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:ondulatorio"
		xingling="ONDULATORIO">
		Ondulatório
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:progressao-dupla"
		xingling="PROGRESSAO_DUPLA">
		Progressão Dupla
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:de-lorme"
		xingling="DE_LORME">
		De Lorme
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:erpad"
		xingling="ERPAD">
		Erpad
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:parcelado"
		xingling="PARCELADO">
		Parcelado
	</span>
	<span
		i18n="
			@@montagem-treino-atividade-ficha:metodo-execucao:duplamente-parcelado"
		xingling="DUPLAMENTE_PARCELADO">
		Duplamente Parcelado
	</span>
	<span
		i18n="
			@@montagem-treino-atividade-ficha:metodo-execucao:triplamente-parcelado"
		xingling="TRIPLADO_PARCELADO">
		Triplamente Parcelado
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:puxe-empurre"
		xingling="PUXE_EMPURRE">
		Puxe-Empurre
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:repeticao-roubada"
		xingling="REPETICAO_ROUBADA">
		Repetição Roubada
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:repeticao-forcada"
		xingling="REPETICAO_FORCADA">
		Repetição Forçada
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:dta"
		xingling="DTA">
		D.T.A.
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:repeticao-parcial"
		xingling="REPETICAO_PARCIAL">
		Repetição Parcial
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:pico-contracao"
		xingling="PICO_CONTRACAO">
		Pico de Contração
	</span>
	<span
		i18n="
			@@montagem-treino-atividade-ficha:metodo-execucao:tensao-lenta-continua"
		xingling="TENSAO_LENTA_CONTINUA">
		Tensão Lenta e Contínua
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:set-descendente"
		xingling="SET_DESCENDENTE">
		Set Descendente
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:isolamento"
		xingling="ISOLAMENTO">
		Isolamento
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:super-set"
		xingling="SUPER_SET">
		Super-Set
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:serie-composta"
		xingling="SERIE_COMPOSTA">
		Série Composta
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:super-set-multiplo"
		xingling="SUPERSET_MULTIPLO">
		Super-Set Múltiplo
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:pre-exaustao"
		xingling="PRE_EXAUSTAO">
		Pré-Exaustão
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:serie-gigante"
		xingling="SERIE_GIGANTE">
		Série Gigante
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:pha"
		xingling="PHA">
		P.H.A.
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:super-circuito"
		xingling="SUPER_CIRCUITO">
		Super-Circuito
	</span>
	<span
		i18n="
			@@montagem-treino-atividade-ficha:metodo-execucao:musculacao-intervalada"
		xingling="MUSCULACAO_INTERVALADA">
		Musculação Intervalada
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:pliometrico"
		xingling="PLIOMETRICO">
		Pliométrico
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:repeticao-negativa"
		xingling="REPETICAO_NEGATIVA">
		Repetição Negativa
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:nautilus"
		xingling="NAUTILUS">
		Nautilus
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:heavy-duty"
		xingling="HEAVY_DUTY">
		Heavy-Duty
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:rest-pause"
		xingling="REST_PAUSE">
		Rest Pause
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:pos-exaustao"
		xingling="POS_EXAUSTAO">
		Pós-Exaustão
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:exaustao"
		xingling="EXAUSTAO">
		Exaustão
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:strip-set"
		xingling="STRIP_SET">
		Strip Set
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:set-21"
		xingling="SET_21">
		Set 21
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:super-drop-set"
		xingling="SUPER_DROP_SET">
		Super Drop Set
	</span>
	<span
		i18n="@@montagem-treino-atividade-ficha:metodo-execucao:flushing"
		xingling="FLUSHING">
		Flushing
	</span>
	<span
		i18n="
			@@montagem-treino-atividade-ficha:metodo-execucao:contracao-isometrica"
		xingling="CONTRACAO_ISOMETRICA">
		Contração Isométrica
	</span>
	<span xingling="CONTINUO">Contínuo</span>
	<span xingling="COMBINADO">Combinado</span>
	<span xingling="ALTERNADO">Alternado</span>
	<span xingling="ALTERNADO_SIMULTANEO">Alternado + Simultâneo</span>
	<span xingling="FST">FST-7</span>
	<span xingling="SST">SST</span>
	<span xingling="HIIT">HIIT</span>
	<span xingling="TABATA">Tabata</span>
	<span xingling="MET_6_20">6/20</span>
	<span xingling="TEMPOS_2">2 Tempos</span>
	<span xingling="TEMPOS_3">3 Tempos</span>
	<span xingling="NONSTOP">Nonstop</span>
	<span xingling="CLUSTER">Cluster</span>
	<span xingling="PONTO_ZERO">Ponto Zero</span>
	<span xingling="DEADSTOP">Deadstop</span>
</pacto-traducoes-xingling>
