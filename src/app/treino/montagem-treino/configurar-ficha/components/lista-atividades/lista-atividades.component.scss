@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.title-card-numero-exercicio {
	padding: 16px 0px 10px 0px;
	margin-bottom: 10px;
	@extend .type-h5;
	color: $cinzaPri;
	font-weight: 600;
	line-height: 44px;
	border-bottom: 1px solid $cinzaPri;

	::ng-deep {
		&.replicar i {
			font-size: 16px;
		}

		&.replicar button {
			font-size: 16px;
			text-transform: inherit;
			line-height: 40px;
		}
	}
}

.separator-nr-atividades {
	line-height: 48px;
	font-weight: 700;
	font-size: 16px;
	background-color: $cinzaClaroPri;
	border-radius: 4px;
}

.drag-handler {
	border-right: 4px solid $cinzaClaroPri;
}

.content-lista-atividade {
	margin-bottom: 50px;
}

.header-atividade {
	display: flex;
	width: 100%;
}

.content-lista-atividade {
	label {
		color: $preto02;
		font-size: 12px;
		line-height: 16px;
		font-weight: normal;
		margin: 0;
	}
}

.row-detalhe-atividade {
	padding-left: calc(36% + 5px);
}

.colunas-atividade {
	display: flex;
	width: 100%;
}

.row-atividade {
	border-left: solid 5px $branco;
	padding-top: 16px;
	border-bottom: dashed 1px $cinza02;
	padding-bottom: 16px;

	.pct-edit-3 {
		margin-left: 10px;
	}

	.pct-drag {
		cursor: move;
		font-size: 32px;
		line-height: 2;
	}

	&:hover {
		border-left: solid 5px $azulimPri;

		.descricao-atividade {
			color: $pretoPri;
		}
	}

	.metodoExecucao {
		cursor: pointer;
		margin: 0;
		width: 95%;
	}

	.infos-atividade {
		width: 36%;
		display: flex;

		.drag-handler,
		.block-info-atividade,
		.block-actions {
			display: flex;
		}

		.block-info-atividade {
			margin-left: 7px;
		}
	}

	.campo-serie.metodo {
		width: 14%;

		label {
			padding-left: 15px;
		}
	}

	.campo-serie {
		label {
			padding-left: 8px;
		}

		input {
			border: 1px solid $branco;
			width: 75%;
			display: block;
			color: $pretoPri;
		}

		input:hover,
		input:focus {
			border: 1px solid $gelo03;
		}

		width: 9%;
	}

	.detalhes-atividade {
		width: 200px;
		display: inline-block;
		text-align: right;

		pacto-cat-button {
			width: 125px;
			margin-left: 5px;
		}

		.pct-trash-2 {
			color: $hellboyPri;
			padding: 15px;
			background-color: $branco;
			cursor: pointer;
			margin-left: 5px;
			border-radius: 5px;
		}

		.pct-trash-2:hover {
			background-color: $hellboyPri;
			color: $branco;
			box-shadow: 0 4px 6px #e4e5e6;
		}
	}

	.campo-serie {
		::ng-deep {
			&.metodoExecucao select {
				border: 1px solid $branco;
			}

			&.metodoExecucao .double-arrow {
				display: none;
			}

			&.metodoExecucao:hover .double-arrow {
				display: inherit;
			}

			&.metodoExecucao:hover select {
				border: 1px solid $gelo03;
			}
		}
	}

	::ng-deep {
		&.metodoExecucao .pacto-label {
			display: none;
		}

		&.metodoExecucao .pct-error-msg {
			display: none;
		}
	}
}

.quarto {
	display: inline-block;
	width: 25%;
}

.colum-icon {
	padding: 23px 5px 0px 5px;
}

.block-info-atividade {
	display: flex;
	max-width: 331px;
	margin-top: 6px;
}

.row-atividade.detalhado {
	display: flex;

	.detalhes-atividade {
		width: 188px;
		vertical-align: top;
		margin-top: 23px;
	}

	.metodoExecucao {
		width: calc(100% - 5px);
		margin-left: 5px;
	}

	.caixa-alpha {
		.metodoExecucao {
			width: 100%;
			margin-left: 0px;
		}
	}

	.colum-icon {
		padding: 85px 5px 0px 5px;
	}

	.infos-atividade {
		width: 25%;
	}

	.content-atividade {
		width: 75%;
	}

	.caixa40 {
		width: 40%;
	}

	textarea {
		margin-left: 5px;
		display: block;
		width: calc(100% - 5px);
		border-radius: 3px;
		border: 1px solid $gelo03;
		height: 112px;
		padding: 0px 30px 0px 10px;
		color: $preto02;
		outline: 0px !important;

		&:disabled {
			border: 1px solid $azulim05;
		}
	}

	input {
		&:disabled {
			border: 1px solid $azulim05;
		}
	}

	.caixa-gama {
		vertical-align: top;
		width: 48%;

		&.serieApenasDuracao {
			width: calc(100% - 226.88px);
		}

		display: inline-block;

		label {
			margin-left: 10px;
		}
	}

	.caixa-delta {
		vertical-align: top;
		width: 52%;
		display: inline-block;

		&.serieApenasDuracao {
			width: 226.88px;

			.col-center {
				width: calc(100% - 115px);
			}
		}

		.col-left {
			width: 115px;
			display: inline-block;
			vertical-align: top;
		}

		.col-ajuste-serie {
			vertical-align: top;

			.customizar {
				display: inline-block;
				vertical-align: top;
				cursor: pointer;
				background-color: #fafafa;
				width: 42px;
				margin-left: 5px;
				border-radius: 4px;
				text-align: left;
				color: $azulPacto02;
				height: 40px;
				text-align: center;

				&.seriesEditadas {
					border: 1px solid $azulim05;
					position: relative;

					.bola {
						position: absolute;
						border-radius: 10px;
						background-color: $azulim05;
						width: 16px;
						height: 16px;
						top: -8px;
						right: -5px;
					}
				}

				i {
					margin-top: 12px;
					display: inline-block;
				}
			}
		}

		.col-center {
			label {
				margin-left: 5px;
			}

			input {
				margin-left: 5px;
				width: calc(100% - 5px);
			}

			width: calc(50% - 127px);
			display: inline-block;
			vertical-align: top;
		}

		.col-right {
			width: 138px;
			display: inline-block;
			vertical-align: top;

			* {
				margin-left: 5px;
			}
		}

		input {
			&.cadencia {
				width: 95%;
			}

			&.descanso {
				width: 115px;
			}

			width: 68px;
		}
	}

	.caixa-alpha {
		width: calc(50% - 12px);
		display: inline-block;

		&.tri-set,
		&.bi-set {
			.metodoExecucao {
				width: calc(100% - 5px);
			}

			width: 26.5%;

			* {
				margin-left: 5px;
			}
		}

		input {
			margin-left: 5px;
			width: calc(100% - 5px);
		}

		&.cardio {
			width: calc(50% + 11px);

			label {
				margin-left: 5px;
			}
		}
	}

	.caixa-beta {
		width: calc(50% + 11px);
		display: inline-block;
	}

	.caixa-gama {
		.caixa-beta {
			width: calc(100% - 193px);
		}
	}

	.imagem-detalhe {
		text-align: center;
		overflow: hidden;
		position: relative;
		border-radius: 4px;
		width: 112px;
		height: 112px;

		.imagem-atividade {
			position: absolute;
			text-align: center;
			top: 0;
			left: 0;
			width: 100%;

			img {
				height: 112px;
				border-radius: 4px;
				display: inline-flex;
			}
		}

		img.img-blur {
			height: auto;
			position: absolute;
			top: 0;
			left: 0;
			width: 120%;
			opacity: 0.5;
			filter: blur(5px);
		}
	}

	.infos-atividade {
		.block-info-atividade {
			display: block;
		}
	}

	.info-atividade {
		display: flex;
	}
}

.info-atividade {
	display: block;
	padding: 7px 0px 0px 15px;
	//width: 230px;

	.descricao-atividade {
		white-space: normal;
		overflow: hidden;
		text-overflow: ellipsis;
		@extend .type-h6-bold;
	}

	.subdescricao-atividade {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		@extend .type-p-small;
	}

	.atividades-set {
		padding-left: 15px;
		font-size: 9px;
		font-weight: 600;
	}
}

.block-input {
	display: flex;
	justify-content: flex-start;
	flex-grow: 1;
	width: 60%;

	.input-criado {
		margin: 0px 5px;
		width: 72px;
		color: black;
	}

	.input-descanso {
		margin: 0px 5px;
		width: 82px;
	}

	.input-complemento {
		margin: 0px 5px;
		width: 45%;
	}
}

.block-actions {
	width: 5%;

	i {
		cursor: pointer;
		padding: 28px 5px 0px 5px;
	}
}

.pct-edit-3 {
	color: $azulPacto02;
}

.cdk-drag-preview {
	background-color: $branco;
	z-index: 2147483547 !important;
	box-sizing: border-box;
	border-radius: 4px;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
		0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.content-lista-atividade.cdk-drop-list-dragging
	.row-atividade:not(.cdk-drag-placeholder) {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.nome {
	@extend .type-caption;
	color: $gelo04;
	//min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}

.aux-wrapper {
	position: relative;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

::placeholder {
	color: $cinzaClaro02;
}

.text-right {
	text-align: right;
}

.text-center {
	text-align: center;
}

.display-flex-important {
	display: flex !important;
	justify-content: space-around;
}

.componente-corpo-grupo-muscular {
	display: flex;

	::ng-deep .grupo-muscular-frontal {
		max-height: 107px;
		max-width: 65px;
	}

	::ng-deep .grupo-muscular-posterior {
		max-height: 107px;
		max-width: 65px;
	}
}
