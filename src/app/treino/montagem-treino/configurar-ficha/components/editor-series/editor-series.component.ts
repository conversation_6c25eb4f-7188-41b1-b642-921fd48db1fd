import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnChanges,
	OnInit,
} from "@angular/core";
import { FormArray, FormGroup } from "@angular/forms";
import {
	buildAtividadeFichaForm,
	buildSerieAtividadeForm,
} from "../../ficha-form-group";
import { ConfigurarFichaService } from "../../../configurar-ficha.service";
import { LocalizationService } from "@base-core/localization/localization.service";
import createNumberMask from "text-mask-addons/dist/createNumberMask";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { WindowUtilService } from "@base-core/utils/window-util.service";

@Component({
	selector: "pacto-editor-series",
	templateUrl: "./editor-series.component.html",
	styleUrls: ["./editor-series.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditorSeriesComponent implements OnInit, OnChanges {
	atividadeFichaForm: FormGroup;
	camposVisiveis: any = {};
	timeMask;

	constructor(
		private configurarFichaService: ConfigurarFichaService,
		private localization: LocalizationService,
		private utils: WindowUtilService,
		private ngbActiveModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {
		this.timeMask = {
			mask: this.localization.getTimeMask(),
		};
	}

	get seriesControl() {
		return (this.atividadeFichaForm.get("series") as FormArray).controls;
	}

	get atividade() {
		return this.configurarFichaService.obterAtividadeMap(
			this.atividadeFichaForm.get("atividadeId").value
		);
	}

	get neuro() {
		return this.atividade.tipo === "NEUROMUSCULAR";
	}

	get cardio() {
		return (
			!this.atividade.serieApenasDuracao &&
			this.atividade.tipo === "CARDIOVASCULAR"
		);
	}

	get cardioDuracao() {
		return (
			this.atividade.serieApenasDuracao &&
			this.atividade.tipo === "CARDIOVASCULAR"
		);
	}

	get numeroInteiro() {
		return createNumberMask({ prefix: "", thousandsSeparatorSymbol: "" });
	}

	ngOnInit() {
		this.atualizarCamposVisiveis();
	}

	ngOnChanges() {
		this.atualizarCamposVisiveis();
	}

	get atividadeNome() {
		const atividadeId = this.atividadeFichaForm.get("atividadeId").value;
		const atividade =
			this.configurarFichaService.obterAtividadeMap(atividadeId);
		return atividade ? atividade.nome : "";
	}

	ready(formFichaAtividade: FormGroup) {
		this.atividadeFichaForm = buildAtividadeFichaForm();
		this.atividadeFichaForm.patchValue(formFichaAtividade.getRawValue());
		(formFichaAtividade.get("series") as FormArray).controls.forEach(
			(serie: FormGroup) => {
				const serieAtividade: FormGroup = buildSerieAtividadeForm();
				serieAtividade.patchValue(serie.getRawValue());
				serieAtividade
					.get("duracao")
					.setValue(
						this.utils.convertSecondsIntoMinutesSecondsLabel(
							serieAtividade.get("duracao").value
						)
					);
				serieAtividade
					.get("descanso")
					.setValue(
						this.utils.convertSecondsIntoMinutesSecondsLabel(
							serieAtividade.get("descanso").value
						)
					);
				(this.atividadeFichaForm.get("series") as FormArray).push(
					serieAtividade
				);
			}
		);
		this.cd.detectChanges();
	}

	cancelar() {
		this.ngbActiveModal.dismiss();
	}

	removeHandler(index) {
		const seriesForm = this.atividadeFichaForm.get("series") as FormArray;
		seriesForm.removeAt(index);
	}

	clickHandlerAddSerie() {
		const seriesDaFicha = this.atividadeFichaForm.get("series") as FormArray;
		const index =
			seriesDaFicha.controls.length > 0 ? seriesDaFicha.controls.length - 1 : 0;
		const novaSerie = buildSerieAtividadeForm();
		seriesDaFicha.controls.forEach(
			(atividadeFichaForm: FormGroup, i: number) => {
				if (i === index) {
					novaSerie.patchValue(atividadeFichaForm.getRawValue());
				}
			}
		);

		novaSerie.get("id").setValue(null);
		novaSerie.get("sequencia").setValue(this.proximaSequencia());
		seriesDaFicha.push(novaSerie);
	}

	private proximaSequencia(): number {
		let result = 0;
		const seriesDaAtividadeFicha = (
			this.atividadeFichaForm.get("series") as FormArray
		).controls;
		seriesDaAtividadeFicha.forEach((serieForm: FormGroup) => {
			if (result <= parseInt(serieForm.get("sequencia").value, 10)) {
				result = parseInt(serieForm.get("sequencia").value, 10);
			}
		});
		return result + 1;
	}

	clickHandler() {
		if (this.atividadeFichaForm.valid) {
			const seriesForm = (this.atividadeFichaForm.get("series") as FormArray)
				.controls;
			seriesForm.forEach((serieForm) => {
				if (serieForm.get("duracao").value) {
					serieForm
						.get("duracao")
						.setValue(
							this.utils.convertMinutesSecondsIntoSeconds(
								serieForm.get("duracao").value
							)
						);
				}
				if (serieForm.get("descanso").value) {
					serieForm
						.get("descanso")
						.setValue(
							this.utils.convertMinutesSecondsIntoSeconds(
								serieForm.get("descanso").value
							)
						);
				}
			});
			this.ngbActiveModal.close(this.atividadeFichaForm);
		}
	}

	espelharSerie(cloneFormGroup) {
		const seriesForm = (this.atividadeFichaForm.get("series") as FormArray)
			.controls;
		seriesForm.forEach((serieForm: FormGroup) => {
			const serieAtualizada = buildSerieAtividadeForm();
			serieAtualizada.patchValue(cloneFormGroup.getRawValue());
			serieAtualizada
				.get("sequencia")
				.setValue(serieForm.get("sequencia").value);
			serieAtualizada.get("id").setValue(serieForm.get("id").value);
			serieForm.patchValue(serieAtualizada.getRawValue());
		});
	}

	private atualizarCamposVisiveis() {
		this.camposVisiveis = {
			repeticoes: this.neuro,
			carga: this.neuro,
			cadencia: this.neuro,
			descanso: true,
			velocidade: this.cardio,
			duracao: this.cardioDuracao || this.cardio,
			distancia: this.cardio,
			complemento: true,
		};
		this.cd.detectChanges();
	}
}
