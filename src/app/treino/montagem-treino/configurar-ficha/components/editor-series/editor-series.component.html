<div class="modal-body">
	<div class="linha">
		<div
			class="info-serie-atividade"
			i18n="@@montagem-treino-atividade-ficha:repeticoes:seriesPara">
			Séries para {{ atividadeNome }}
		</div>
		<pacto-cat-button
			(click)="clickHandlerAddSerie()"
			i18n-label="@@montagem-treino-atividade-ficha:repeticoes:adicionarSerie"
			label="Adicionar Série"></pacto-cat-button>
	</div>
	<div *ngIf="seriesControl.length">
		<div class="titulo-editor-serie">
			<div
				class="serie"
				i18n="@@montagem-treino-atividade-ficha:table:serie:title">
				Série
			</div>
			<div
				*ngIf="camposVisiveis.repeticoes"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:repeticoes:label">
				Repetições
			</div>
			<div
				*ngIf="camposVisiveis.carga"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:carga:label">
				Carga(kg)
			</div>
			<div
				*ngIf="camposVisiveis.cadencia"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:cadencia:label">
				Cadência
			</div>
			<div
				*ngIf="camposVisiveis.duracao"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:duracao:label">
				Duração(m:s)
			</div>
			<div
				*ngIf="camposVisiveis.velocidade"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:velocidade:label">
				Velocidade(km/h)
			</div>
			<div
				*ngIf="camposVisiveis.distancia"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:distancia:label">
				Distância(m)
			</div>
			<div
				*ngIf="camposVisiveis.descanso"
				class="coluna-tabela"
				i18n="@@montagem-treino-atividade-ficha:descanso:label">
				Descanso(m:s)
			</div>
			<div
				class="acoes"
				i18n="@@montagem-treino-atividade-ficha:table:acoes:title">
				Ações
			</div>
		</div>
		<div [maxHeight]="'233px'" class="corpo-tabela" pactoCatSmoothScroll>
			<div
				*ngFor="let formGroup of seriesControl; let index = index"
				class="linha-tabela">
				<div class="serie conteudo-serie">{{ index + 1 }}</div>
				<div *ngIf="camposVisiveis.repeticoes" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('repeticoes')"
						i18n-placeholder="
							@@montagem-treino-atividade-ficha:repeticoes:label"
						id="input-repeticoes-serie-{{ index }}"
						placeholder="Repetições"
						type="text" />
				</div>
				<div *ngIf="camposVisiveis.carga" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('carga')"
						i18n-placeholder="@@montagem-treino-atividade-ficha:carga:label"
						id="input-carga-serie-{{ index }}"
						placeholder="Carga (kg)"
						type="text" />
				</div>
				<div *ngIf="camposVisiveis.cadencia" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('cadencia')"
						i18n-placeholder="@@montagem-treino-atividade-ficha:cadencia:label"
						id="input-cadencia-serie-{{ index }}"
						placeholder="Cadência"
						type="text" />
				</div>
				<div *ngIf="camposVisiveis.duracao" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('duracao')"
						[textMask]="timeMask"
						i18n-placeholder="
							@@montagem-treino-atividade-ficha:duracao:placeholder"
						placeholder="30 minutos" />
				</div>
				<div *ngIf="camposVisiveis.velocidade" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('velocidade')"
						[textMask]="{ guide: false, mask: numeroInteiro }"
						i18n-placeholder="
							@@montagem-treino-atividade-ficha:velocidade:label"
						placeholder="34 km/h" />
				</div>
				<div *ngIf="camposVisiveis.distancia" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('distancia')"
						[textMask]="{ guide: false, mask: numeroInteiro }"
						i18n-placeholder="@@montagem-treino-atividade-ficha:distancia:label"
						maxlength="6"
						placeholder="Distânia em metros" />
				</div>
				<div *ngIf="camposVisiveis.descanso" class="coluna-tabela">
					<input
						[formControl]="formGroup.get('descanso')"
						[textMask]="timeMask"
						i18n-placeholder="@@montagem-treino-atividade-ficha:descanso:label"
						placeholder="Descanso(m:s)" />
				</div>

				<div class="acoes">
					<i
						(click)="espelharSerie(formGroup)"
						class="pct pct-copy"
						title="Espelhar"></i>
					<i
						(click)="removeHandler(index)"
						class="pct pct-trash-2"
						title="Remover"></i>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<pacto-cat-button
		(click)="clickHandler()"
		i18n-label="@@montagem-treino-atividade-ficha:repeticoes:concluir"
		label="Concluir"></pacto-cat-button>
</div>
