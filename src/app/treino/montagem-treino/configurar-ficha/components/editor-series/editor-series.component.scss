@import "src/assets/scss/pacto/plataforma-import.scss";

.linha {
	display: flex;
	justify-content: space-between;
	flex-grow: 1;
	padding: 0px 0px 15px 0px;
	line-height: 2;

	.info-serie-atividade {
		@extend .type-h6;
		color: #b4b7bb;
	}
}

.titulo-editor-serie {
	display: flex;
	border-bottom: 1px solid $cinza02;
	padding-bottom: 5px;
	justify-content: space-between;
	@extend .type-h6;
	color: $gelo04;
}

.corpo-tabela {
	margin: 15px 0px 32px 0px;

	.linha-tabela {
		display: flex;
		justify-content: space-between;
		padding: 25px 0px;
		border-bottom: dashed 1px $cinza02;

		&:first-child {
			padding-top: 0px;
		}
	}
}

.serie {
	width: 50px;
	text-align: center;
	margin-right: 5px;
}

.conteudo-serie {
	line-height: 2;
	padding-top: 4px;
}

.acoes {
	width: 50px;
	display: flex;
	justify-content: center;
	margin-left: 5px;

	i {
		margin: 0px 5px;
		cursor: pointer;
		line-height: 2;
		padding-top: 5px;
	}
}

.coluna-tabela {
	display: flex;
	margin: 0px 5px;
	width: 100px;
	text-align: center;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

::placeholder {
	color: $cinzaClaro02;
}

.complemento-nome-atividade {
	padding: 0px 5px;
	color: #a6aab1;
	line-height: 2em;
}
