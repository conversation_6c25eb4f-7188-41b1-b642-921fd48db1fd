<div class="card-retorno-container">
	<i (click)="backList()" class="pct pct-arrow-left"></i>
	<div class="info-programa">
		<div class="titulo-modal" title="Anamnese">Anamnese</div>
	</div>
</div>
<div class="table-wrapper pacto-shadow">
	<nav aria-label="Progress" class="steps-indicator">
		<div class="step" [class.active]="currentStep >= 1" (click)="goToStep(1)">
			<span class="step-circle">1</span>
			<span class="step-label">Restrição</span>
		</div>
		<span class="arrow">→</span>
		<div class="step" [class.active]="currentStep >= 2" (click)="goToStep(2)">
			<span class="step-circle">2</span>
			<span class="step-label">Dados do aluno</span>
		</div>
		<span class="arrow">→</span>
		<div class="step" [class.active]="currentStep >= 3" (click)="goToStep(3)">
			<span class="step-circle">3</span>
			<span class="step-label">Treino</span>
		</div>
	</nav>

	<form
		*ngIf="currentStep === 1"
		[formGroup]="restricaoFormGroup"
		class="formAll">
		<h4 class="dias-minutos">
			Seu aluno possui algum dos problemas listados abaixo?
		</h4>
		<ul>
			<li>Problemas cardíacos</li>
			<li>Problemas nas costas ou coluna</li>
			<li>Problemas de joelho</li>
			<li>Lesões físicas</li>
			<li>Restrição médica para prática de atividade física</li>
			<li>
				Outras restrições ou limitações para a prática de atividades físicas
			</li>
		</ul>
		<div class="dias-minutos">
			<ds3-form-field class="radio-itens">
				<input
					type="radio"
					ds3Input
					id="res-sim"
					formControlName="restricao"
					value="sim" />
				<label for="res-sim">
					Sim, possui algumas das restrições listadas acima
				</label>
			</ds3-form-field>

			<ds3-form-field class="radio-itens">
				<input
					type="radio"
					ds3Input
					id="res-nao"
					formControlName="restricao"
					value="nao" />
				<label for="res-nao">Não possui restrições</label>
			</ds3-form-field>
		</div>

		<div
			*ngIf="restricaoFormGroup.get('restricao').value === 'sim'"
			class="mensagem-restricao">
			<div class="alerta-ia" role="alert">
				<i class="alerta-ia__icon pct pct-alert-triangle"></i>
				<span class="alerta-ia__texto">
					Ops! Não conseguimos gerar o treino com IA devido às restrições
					especificadas.
				</span>
			</div>
		</div>

		<div class="footer-form">
			<button ds3-outlined-button type="button" (click)="backList()">
				Cancelar
			</button>
			<button
				ds3-flat-button
				type="button"
				[disabled]="!restricaoFormGroup.valid"
				(click)="goToStep(2)">
				Avançar
			</button>
		</div>
	</form>

	<form *ngIf="currentStep === 2" [formGroup]="dadosFormGroup" class="formAll">
		<h4 class="dias-minutos"></h4>
		<div class="row">
			<div class="col-md-4">
				<ds3-form-field>
					<ds3-field-label>Qual é o objetivo atual do aluno?*</ds3-field-label>
					<ds3-select
						[options]="optionsObjetivoAtual"
						ds3Input
						formControlName="objetivoAtual"></ds3-select>
				</ds3-form-field>
			</div>
			<div class="col-md-4">
				<ds3-form-field>
					<ds3-field-label>Qual o sexo biológico do aluno?*</ds3-field-label>
					<ds3-select
						ds3Input
						formControlName="sexoBiologico"
						[options]="optionsSexoBiologico"></ds3-select>
				</ds3-form-field>
			</div>
			<div class="col-md-4">
				<ds3-form-field>
					<ds3-field-label>Qual a idade do aluno?*</ds3-field-label>
					<input ds3Input type="number" formControlName="idadeAluno" />
					<ds3-helper-message *ngIf="!getError('idadeAluno')">
						Idade mínima de 14 anos, idade máxima de 60 anos
					</ds3-helper-message>
					<ds3-helper-message *ngIf="getError('idadeAluno')">
						{{ getError("idadeAluno") }}
					</ds3-helper-message>
				</ds3-form-field>
			</div>
		</div>
		<div class="row">
			<div class="col-md-4">
				<ds3-form-field>
					<ds3-field-label>Qual é a altura do aluno?*</ds3-field-label>
					<input ds3Input type="number" formControlName="alturaAluno" />
					<ds3-helper-message *ngIf="!getError('alturaAluno')">
						Preencher altura em centímetros
					</ds3-helper-message>
					<ds3-helper-message *ngIf="getError('alturaAluno')">
						{{ getError("alturaAluno") }}
					</ds3-helper-message>
				</ds3-form-field>
			</div>
			<div class="col-md-4">
				<ds3-form-field>
					<ds3-field-label>Qual é o peso atual do aluno?*</ds3-field-label>
					<input ds3Input type="number" formControlName="pesoAluno" />
					<ds3-helper-message *ngIf="!getError('pesoAluno')">
						Preencher peso em kg. Exemplo: 77.5
					</ds3-helper-message>
					<ds3-helper-message *ngIf="getError('pesoAluno')">
						{{ getError("pesoAluno") }}
					</ds3-helper-message>
				</ds3-form-field>
			</div>
		</div>
		<div class="row dias-minutos">
			<div class="col-md-6">
				<ds3-form-field>
					<ds3-field-label>
						Quantos dias por semana o aluno pratica atividades físicas?*
					</ds3-field-label>
					<ds3-select
						ds3Input
						formControlName="diasAtividades"
						[options]="optionsDiasAtividades"></ds3-select>
					<ds3-helper-message *ngIf="getError('diasAtividades')">
						{{ getError("diasAtividades") }}
					</ds3-helper-message>
				</ds3-form-field>
			</div>
			<div class="col-md-6">
				<ds3-form-field>
					<ds3-field-label>
						Quantos minutos por dia o aluno tem disponível para treinar?*
					</ds3-field-label>
					<input ds3Input type="number" formControlName="minutosDias" />
					<ds3-helper-message *ngIf="!getError('minutosDias')">
						O mínimo é 40 minutos, o máximo é 120 minutos
					</ds3-helper-message>
					<ds3-helper-message *ngIf="getError('minutosDias')">
						{{ getError("minutosDias") }}
					</ds3-helper-message>
				</ds3-form-field>
			</div>
		</div>
		<div class="footer-form">
			<button ds3-outlined-button (click)="backList()">Cancelar</button>
			<button ds3-outlined-button type="button" (click)="goToStep(1)">
				Voltar
			</button>
			<button
				ds3-flat-button
				type="button"
				[disabled]="!dadosFormGroup.valid"
				(click)="goToStep(3)">
				Avançar
			</button>
		</div>
	</form>

	<form *ngIf="currentStep === 3" [formGroup]="treinoFormGroup" class="formAll">
		<h4 class="dias-minutos">
			Em qual experiência de treino o aluno se encaixa?*
		</h4>
		<div>
			<ds3-form-field class="radio-itens">
				<input
					type="radio"
					ds3Input
					id="exp-nunca"
					formControlName="experienciaTreino"
					value="nivel1" />
				<label for="exp-nunca">
					Nunca treinou ou já treinou e está voltando agora
				</label>
			</ds3-form-field>
			<ds3-form-field class="radio-itens">
				<input
					type="radio"
					ds3Input
					id="exp-6m-1y"
					formControlName="experienciaTreino"
					value="nivel2" />
				<label for="exp-6m-1y">Já treina entre 6 meses e 1 ano</label>
			</ds3-form-field>
			<ds3-form-field class="radio-itens">
				<input
					type="radio"
					ds3Input
					id="exp-1y-2y"
					formControlName="experienciaTreino"
					value="nivel3" />
				<label for="exp-1y-2y">Já treina há mais de 1 ano</label>
			</ds3-form-field>
		</div>
		<h4 class="dias-minutos-title">
			Qual nível atual de treino que o aluno se encaixa?*
		</h4>
		<div class="row">
			<div class="row">
				<div class="col-md-4">
					<div class="box-nivel-treino">
						<div class="header-nivel">Iniciante</div>
						<div class="content-nivel">
							<ul>
								<li>
									O aluno não tem coordenação suficiente para fazer exercícios e
									precisa de ajuda ou de correções.
								</li>
								<li>
									O aluno não consegue sentir o músculo-alvo trabalhando durante
									o exercício.
								</li>
							</ul>
						</div>
						<div class="footer-nivel">
							<div class="button-selecionar">
								<label>
									<input
										type="checkbox"
										formControlName="nivelatualIniciante"
										(change)="onCheckboxChange('nivelatualIniciante')" />
									<span>
										{{ isChecked1 ? "Selecionado " : "Selecionar " }}
										<i *ngIf="isChecked1" class="pct pct-check"></i>
									</span>
								</label>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<div class="box-nivel-treino">
						<div class="header-nivel">Intermediário</div>
						<div class="content-nivel">
							<ul>
								<li>
									O aluno consegue executar todos os exercícios de forma
									satisfatória.
								</li>
								<li>
									O aluno consegue sentir os músculos sendo trabalhados
									corretamente.
								</li>
								<li>O aluno possui força significativa.</li>
							</ul>
						</div>
						<div class="footer-nivel">
							<div class="button-selecionar">
								<label>
									<input
										type="checkbox"
										formControlName="nivelatualIntermediario"
										(change)="onCheckboxChange('nivelatualIntermediario')" />
									<span>
										{{ isChecked2 ? "Selecionado " : "Selecionar " }}
										<i *ngIf="isChecked2" class="pct pct-check"></i>
									</span>
								</label>
							</div>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<div class="box-nivel-treino">
						<div class="header-nivel">Avançado</div>
						<div class="content-nivel">
							<ul>
								<li>
									O aluno executa perfeitamente todos os exercícios e consegue
									sentir o músculo-alvo trabalhando.
								</li>
								<li>
									O aluno compreende a execução dos exercícios e sabe como
									alinhar o corpo para atingir partes específicas dos músculos.
								</li>
								<li>
									O aluno já tem experiências bem sucedidas com várias técnicas
									como dropsets, supersets e outros métodos de alta intensidade.
								</li>
							</ul>
						</div>
						<div class="footer-nivel">
							<div class="button-selecionar">
								<label>
									<input
										type="checkbox"
										formControlName="nivelatualAvancado"
										(change)="onCheckboxChange('nivelatualAvancado')" />
									<span>
										{{ isChecked3 ? "Selecionado " : "Selecionar " }}
										<i *ngIf="isChecked3" class="pct pct-check"></i>
									</span>
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="footer-form">
			<button ds3-outlined-button (click)="backList()">Cancelar</button>
			<button ds3-outlined-button type="button" (click)="goToStep(2)">
				Voltar
			</button>
			<button
				ds3-flat-button
				[disabled]="treinoFormGroup.invalid"
				(click)="gerarTreino()">
				Gerar treino
			</button>
		</div>
	</form>
</div>
