import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { Location } from "@angular/common";
import {
	FormBuilder,
	FormGroup,
	ValidationErrors,
	Validators,
} from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalImpedimentoTreinoComponent } from "./modal-impedimento-treino/modal-impedimento-treino.component";
import {
	AlunoBase,
	AvaliacaoFisica,
	TreinoApiAlunosService,
	TreinoApiAvaliacaoCatalogoService,
	TreinoApiProgramaService,
	ProgramaIA,
} from "treino-api";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-adicionar-anamnese",
	templateUrl: "./adicionar-anamnese.component.html",
	styleUrls: ["./adicionar-anamnese.component.scss"],
})
export class AdicionarAnamneseComponent implements OnInit {
	restricaoFormGroup: FormGroup;
	dadosFormGroup: FormGroup;
	treinoFormGroup: FormGroup;
	editable = true;
	optionsObjetivoAtual = [
		{ value: "1", name: "Hipertrofia" },
		{ value: "2", name: "Emagrecimento" },
		{ value: "3", name: "Força" },
	];
	optionsSexoBiologico = [
		{ value: "1", name: "Masculino" },
		{ value: "2", name: "Feminino" },
	];
	optionsDiasAtividades = [
		{ value: "1", name: "1" },
		{ value: "2", name: "2" },
		{ value: "3", name: "3" },
		{ value: "4", name: "4" },
		{ value: "5", name: "5" },
		{ value: "6", name: "6" },
	];
	isFormDadosValid: boolean = false;
	isChecked1 = false;
	isChecked2 = false;
	isChecked3 = false;
	nextForm = true;
	cod;
	mat;
	aluno: AlunoBase;
	existeAvaliacao = false;
	ultima: AvaliacaoFisica;
	currentStep = 1;

	constructor(
		private cd: ChangeDetectorRef,
		private fb: FormBuilder,
		private location: Location,
		private modal: NgbModal,
		private programaService: TreinoApiProgramaService,
		private activatedRoute: ActivatedRoute,
		private snotifyService: SnotifyService,
		private router: Router,
		private catalogoService: TreinoApiAvaliacaoCatalogoService,
		private alunoService: TreinoApiAlunosService
	) {}

	ngOnInit() {
		this.activatedRoute.queryParams.subscribe((params) => {
			this.cod = params["cod"];
			this.mat = params["mat"];
		});

		this.restricaoFormGroup = this.fb.group(
			{
				possuiRestricoes: [null],
				naoPossuiRestricoes: [null],
			},
			{ validators: this.restricaoValidator }
		);

		this.dadosFormGroup = this.fb.group({
			objetivoAtual: ["", [Validators.required]],
			sexoBiologico: ["", [Validators.required]],
			idadeAluno: [
				"",
				[Validators.required, Validators.min(14), Validators.max(60)],
			],
			alturaAluno: [
				"",
				[Validators.required, Validators.min(100), Validators.max(220)],
			],
			pesoAluno: [
				"",
				[Validators.required, Validators.min(40), Validators.max(150)],
			],
			diasAtividades: [
				"",
				[Validators.required, Validators.min(1), Validators.max(6)],
			],
			minutosDias: [
				"",
				[Validators.required, Validators.min(40), Validators.max(120)],
			],
		});

		this.treinoFormGroup = this.fb.group(
			{
				experienciaTreino: ["", Validators.required],
				nivelatualIniciante: [false],
				nivelatualIntermediario: [false],
				nivelatualAvancado: [false],
			},
			{ validator: this.treinoValidator }
		);

		this.dadosFormGroup.valueChanges.subscribe(() => {
			this.verificaCamposValidos();
		});

		this.initDados();

		this.restricaoFormGroup = this.fb.group(
			{
				restricao: ["", Validators.required],
			},
			{
				validators: (group: FormGroup) => {
					return group.get("restricao").value === "nao"
						? null
						: { precisaNao: true };
				},
			}
		);

		this.treinoFormGroup = this.fb.group(
			{
				experienciaTreino: ["", Validators.required],
				nivelatualIniciante: [false],
				nivelatualIntermediario: [false],
				nivelatualAvancado: [false],
			},
			{ validator: this.treinoValidator }
		);

		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

	goToStep(step: number) {
		this.currentStep = step;
	}

	initDados() {
		this.alunoService
			.obterAlunoCompletoPorMatricula(this.mat)
			.subscribe((alunoResponse) => {
				this.aluno = alunoResponse;

				const patchData: any = {};
				if (this.aluno.idade && this.aluno.idade !== 0) {
					patchData.idadeAluno = this.aluno.idade;
				}
				if (this.aluno.sexo && this.aluno.sexo !== "N") {
					patchData.sexoBiologico = this.aluno.sexo === "F" ? "2" : "1";
				}
				this.dadosFormGroup.patchValue(patchData);

				this.dadosFormGroup.patchValue({
					diasAtividades: "5",
					minutosDias: 60,
				});

				this.catalogoService
					.obterUltimaAvaliacaoAluno(this.aluno.id)
					.subscribe((response) => {
						this.existeAvaliacao = response !== undefined;
						this.ultima = response;

						if (this.existeAvaliacao) {
							const peso = response.alunoBI.peso;
							const altura = response.alunoBI.altura;
							this.dadosFormGroup.patchValue({
								pesoAluno: peso,
								alturaAluno: altura * 100,
							});
						}

						this.cd.detectChanges();
					});
			});
	}

	treinoValidator(group: FormGroup): ValidationErrors | null {
		const exp = !!group.get("experienciaTreino").value;
		const nivelAtual =
			group.get("nivelatualIniciante").value ||
			group.get("nivelatualIntermediario").value ||
			group.get("nivelatualAvancado").value;

		return exp && nivelAtual ? null : { invalidTreino: true };
	}

	onCheckboxChange(changedCheckbox: string) {
		this.isChecked1 = this.treinoFormGroup.get("nivelatualIniciante").value;
		this.isChecked2 = this.treinoFormGroup.get("nivelatualIntermediario").value;
		this.isChecked3 = this.treinoFormGroup.get("nivelatualAvancado").value;

		if (
			changedCheckbox === "nivelatualIniciante" &&
			this.treinoFormGroup.get("nivelatualIniciante").value
		) {
			this.treinoFormGroup.get("nivelatualIntermediario").setValue(false);
			this.treinoFormGroup.get("nivelatualAvancado").setValue(false);
			this.isChecked2 = false;
			this.isChecked3 = false;
		} else if (
			changedCheckbox === "nivelatualIntermediario" &&
			this.treinoFormGroup.get("nivelatualIntermediario").value
		) {
			this.treinoFormGroup.get("nivelatualIniciante").setValue(false);
			this.treinoFormGroup.get("nivelatualAvancado").setValue(false);
			this.isChecked1 = false;
			this.isChecked3 = false;
		} else if (
			changedCheckbox === "nivelatualAvancado" &&
			this.treinoFormGroup.get("nivelatualAvancado").value
		) {
			this.treinoFormGroup.get("nivelatualIniciante").setValue(false);
			this.treinoFormGroup.get("nivelatualIntermediario").setValue(false);
			this.isChecked1 = false;
			this.isChecked2 = false;
		}
	}

	verificaCamposValidos() {
		this.isFormDadosValid = this.dadosFormGroup.valid;
	}

	getError(controlName: string): string | null {
		const control = this.dadosFormGroup.get(controlName);
		if (control.touched && control.invalid) {
			if (control.hasError("required")) {
				return "Este campo é obrigatório.";
			}
			if (control.hasError("min")) {
				return `O valor mínimo é ${control.errors["min"].min}.`;
			}
			if (control.hasError("max")) {
				return `O valor máximo é ${control.errors["max"].max}.`;
			}
		}
		return null;
	}

	ngAfterContentChecked() {
		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

	backList() {
		this.router.navigate(["treino", "montagem-programa", "prescricao"]);
	}

	onCheckboxChangeValue(changedCheckbox: string) {
		const possuiRestricoes = this.restricaoFormGroup.get("possuiRestricoes");
		const naoPossuiRestricoes = this.restricaoFormGroup.get(
			"naoPossuiRestricoes"
		);

		if (changedCheckbox === "possuiRestricoes") {
			if (possuiRestricoes.value) {
				naoPossuiRestricoes.setValue(false);
			}
		} else if (changedCheckbox === "naoPossuiRestricoes") {
			if (naoPossuiRestricoes.value) {
				possuiRestricoes.setValue(false);
			}
		}
	}

	onCheckboxClick(clickedCheckbox: string): void {
		const possuiRestricoes = this.restricaoFormGroup.get("possuiRestricoes");
		const naoPossuiRestricoes = this.restricaoFormGroup.get(
			"naoPossuiRestricoes"
		);

		if (clickedCheckbox === "possuiRestricoes") {
			if (possuiRestricoes.value) {
				naoPossuiRestricoes.setValue(false, { emitEvent: false });
			}
		} else if (clickedCheckbox === "naoPossuiRestricoes") {
			if (naoPossuiRestricoes.value) {
				possuiRestricoes.setValue(false, { emitEvent: false });
			}
		}
	}

	onTreinoCheckboxChange(changedCheckbox: string) {
		const nivel1 = this.treinoFormGroup.get("nivel1");
		const nivel2 = this.treinoFormGroup.get("nivel2");
		const nivel3 = this.treinoFormGroup.get("nivel3");

		if (changedCheckbox === "nivel1") {
			if (nivel1.value) {
				nivel2.setValue(false);
				nivel3.setValue(false);
			}
		} else if (changedCheckbox === "nivel2") {
			if (nivel2.value) {
				nivel1.setValue(false);
				nivel3.setValue(false);
			}
		} else if (changedCheckbox === "nivel3") {
			if (nivel3.value) {
				nivel1.setValue(false);
				nivel2.setValue(false);
			}
		}
	}

	onTreinoCheckboxClick(clickedCheckbox: string): void {
		const nivel1 = this.treinoFormGroup.get("nivel1");
		const nivel2 = this.treinoFormGroup.get("nivel2");
		const nivel3 = this.treinoFormGroup.get("nivel3");

		if (clickedCheckbox === "nivel1") {
			if (nivel1.value) {
				nivel2.setValue(false, { emitEvent: false });
				nivel3.setValue(false, { emitEvent: false });
			}
		} else if (clickedCheckbox === "nivel2") {
			if (nivel2.value) {
				nivel1.setValue(false, { emitEvent: false });
				nivel3.setValue(false, { emitEvent: false });
			}
		} else if (clickedCheckbox === "nivel3") {
			if (nivel3.value) {
				nivel1.setValue(false, { emitEvent: false });
				nivel2.setValue(false, { emitEvent: false });
			}
		}
	}

	limparForm(formGroupName: string) {
		this.nextForm = true;
		const formGroup = this[formGroupName] as FormGroup;

		if (formGroupName === "dadosFormGroup") {
			formGroup.get("objetivoAtual").setValue("");
			formGroup.get("sexoBiologico").setValue("");
			formGroup.get("diasAtividades").setValue("");
		}
		if (formGroup) {
			formGroup.reset({
				...Object.keys(formGroup.controls).reduce((acc, key) => {
					acc[key] = "";
					return acc;
				}, {}),
			});

			formGroup.markAsPristine();
			formGroup.markAsUntouched();
			if (formGroupName === "treinoFormGroup") {
				this.isChecked1 = false;
				this.isChecked2 = false;
				this.isChecked3 = false;
			}
		}
	}

	openModalImpedimento() {
		const modal = this.modal.open(ModalImpedimentoTreinoComponent, {
			windowClass: "modal-impedimento",
		});
	}

	gerarTreino() {
		const programa: ProgramaIA = {
			age: this.dadosFormGroup.get("idadeAluno").value,
			height: this.dadosFormGroup.get("alturaAluno").value,
			weight: this.dadosFormGroup.get("pesoAluno").value,
			body_type: this.optionsSexoBiologico.find(
				(opt) => opt.value === this.dadosFormGroup.get("sexoBiologico").value
			).name,
			goal: this.optionsObjetivoAtual.find(
				(opt) => opt.value === this.dadosFormGroup.get("objetivoAtual").value
			).name,
			training_days: parseInt(
				this.dadosFormGroup.get("diasAtividades").value,
				10
			),
			training_time: this.dadosFormGroup.get("minutosDias").value,
			experience_level: this.getNivelExperiencia(),
			current_condition: this.getSituacaoAtualValida(),
			client_id: this.cod,
		};

		const inProgressNotification = this.snotifyService.warning(
			"A IA está gerando o treino para o seu aluno.",
			{
				timeout: 0,
				showProgressBar: true,
				closeOnClick: false,
				pauseOnHover: true,
			}
		);
		this.programaService.criarProgramaPorIA(programa).subscribe({
			next: (result) => {
				this.snotifyService.success("Treino gerado com sucesso!");
				this.snotifyService.remove(inProgressNotification.id);
				this.programaService
					.obterProgramaAtual(this.mat)
					.subscribe((result) => {
						this.router.navigate([
							"treino",
							"cadastros",
							"programa",
							result.id,
						]);
					});
			},
			error: (error) => {
				const errorMessage = error.error.meta.message;
				this.snotifyService.error(errorMessage);
				this.snotifyService.remove(inProgressNotification.id);
			},
		});
	}

	getSituacaoAtualValida(): string {
		const exp = this.treinoFormGroup.get("experienciaTreino").value;
		switch (exp) {
			case "nunca":
				return "Estou voltando a treinar agora";
			case "6m-1y":
				return "Treino entre 6 meses e 1 ano";
			case "1y-2y":
				return "Treino entre 1 ano e 2 anos";
			default:
				return "Nunca treinei";
		}
	}

	getNivelExperiencia(): string {
		if (this.treinoFormGroup.get("nivelatualIniciante").value) {
			return "Iniciante";
		} else if (this.treinoFormGroup.get("nivelatualIntermediario").value) {
			return "Intermediário";
		} else if (this.treinoFormGroup.get("nivelatualAvancado").value) {
			return "Avançado";
		}
		return "";
	}

	getSituacaoAtual(): string {
		if (this.treinoFormGroup.get("nivel1").value) {
			return "Já treinou e está voltando";
		} else if (this.treinoFormGroup.get("nivel2").value) {
			return "Treino entre 6 meses e 1 ano";
		} else if (this.treinoFormGroup.get("nivel3").value) {
			return "Treino entre 1 ano e 2 anos";
		}
		return "";
	}

	restricaoValidator(formGroup: FormGroup): ValidationErrors | null {
		const naoPossuiRestricoes = formGroup.get("naoPossuiRestricoes").value;
		return naoPossuiRestricoes ? null : { required: true };
	}
}
