@import "src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

.view-header {
	border-bottom: 1px solid $cinza02;
	display: block;
	justify-content: center;
	padding-top: 25px;
	margin-left: auto;
	margin-right: auto;

	.header {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
}

.center-aux {
	width: calc(100vw - 460px);
	max-width: 1230px;
}

.info-programa {
	margin: 7px 0 0 15px;

	.titulo-modal {
		color: #51555a;
		font-size: 24px;
		font-style: normal;
		font-weight: 700;
		cursor: pointer;
		padding-top: 15px;
	}

	.titulo-msg {
		@extend .type-caption;
		word-break: break-word;
	}
}

.titulo-professor {
	overflow: hidden;
	text-overflow: ellipsis;

	.titulo-msg {
		@extend .type-caption;
	}

	.subtitulo-modal {
		color: $pretoPri;
		font-size: 15px;
	}
}

.titulo-cref {
	overflow: hidden;

	.titulo-msg {
		@extend .type-caption;
	}

	.subtitulo-modal {
		color: $pretoPri;
		font-size: 15px;
	}
}

.titulo-button {
	display: flex;
	flex-wrap: wrap;
	padding-top: 20px;
	width: 22%;
	justify-content: flex-end;
}

.titulo-data {
	text-overflow: ellipsis;

	.titulo-msg {
		@extend .type-caption;
	}

	.subtitulo-modal {
		color: $pretoPri;
	}
}

.titulo-aviso-msg {
	margin: -5px 15px;
}

.edit-programa {
	display: flex;
	line-height: 1;
	margin: 35px 0 0 15px;
	@extend .type-btn-bold;

	i {
		padding-right: 6px;
		font-size: 16px;
	}

	&:hover {
		cursor: pointer;
	}
}

.pct-arrow-left {
	font-size: 28px;
	color: $pretoPri;
	cursor: pointer;
	position: relative;
	top: 10px;
}

.card-retorno-container {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 15px;
}

.card-info-programa {
	padding-top: 20px;
	display: flex;
	width: 100%;
	justify-content: space-between;
	align-items: center;

	@media (max-width: $plataforma-breakpoint-large) {
		width: calc(100vw - 260px);
	}
}

@media (max-width: 1200px) {
	.ficha-tabs {
		width: 100% !important;
	}
}

.table-wrapper {
	margin: 5px 20px;
	padding: 15px;
	padding-top: 2.5rem;
}

h4 {
	color: hsla(213, 5%, 34%, 1);
	font-size: 14px;
	font-family: $fontPoppins;
	font-weight: 600;
	margin-bottom: 30px;
}

::ng-deep ds3-field-label label,
label {
	color: hsla(213, 5%, 34%, 1) !important;
}

::ng-deep ds3-select .ds3-select {
	border-width: 1px;
	border-style: solid;
	border-color: hsla(222, 4%, 80%, 1);
	background-color: #fff;
	padding: 0.5625rem;
	border-radius: 0.5rem;
}

.formAll .row {
	margin-bottom: 15px;
}

.dias-minutos {
	border-top: 1px solid $supportGray03;
	padding-top: 15px;
}

.dias-minutos-title {
	padding-top: 15px;
}

.footer-form {
	text-align: right;
	margin-top: 30px;

	button {
		margin-left: 15px;
	}
}

.box-nivel-treino {
	height: 384px;
	border: 1px solid $supportGray03;
	border-radius: 8px;

	.header-nivel {
		text-align: center;
		height: 60px;
		color: $plane02;
		font-family: $fontPoppins;
		font-weight: 600;
		justify-content: center;
		font-size: 22px;
		display: flex;
		align-items: center;
		background-color: hsla(222, 96%, 55%, 1);
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
	}

	.content-nivel {
		padding: 15px;
		height: 250px;

		ul {
			padding: 0;

			li {
				color: hsla(0, 0%, 0%, 1);
				font-size: 14px;
				padding-bottom: 10px;
				margin-bottom: 10px;
				list-style: none;
				position: relative;
				text-align: center;

				&:before {
					content: "";
					width: 80px;
					background-color: $supportGray03;
					height: 1px;
					position: absolute;
					left: 0;
					right: 0;
					bottom: 0;
					margin: auto;
				}

				&:last-child:before {
					display: none;
				}
			}
		}
	}
}

.footer-nivel {
	padding: 15px;
}

.button-selecionar {
	border: 2px solid $actionDefaultAble04;
	border-radius: 4px;
	background-color: #fff;
	overflow: hidden;
	width: 100%;
	text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;

	label {
		width: 100%;
		display: block;
		margin-bottom: 0;
		cursor: pointer;
	}

	label input {
		position: absolute;
		display: none;
	}

	label input + span {
		color: $actionDefaultAble04;
		width: 100%;
		font-size: 14px;
		font-weight: 600;
		font-family: $fontPoppins;
		padding: 10px;
	}

	input:checked + span {
		color: #fff !important;
		background-color: $actionDefaultAble04;
	}

	span {
		text-align: center;
		padding: 3px 0;
		display: block;
	}
}

.check-itens {
	display: inline-block;
	margin-right: 15px;
}

.modal-impedimento {
	max-width: 400px !important;
}

::ng-deep .painelLoader {
	background-color: transparent !important;
}

::ng-deep .snotify {
	z-index: 9999999 !important;
}

::ng-deep .content .input-area.type-select {
	height: 44px !important;
}

::ng-deep ds3-steps {
	.ds3-step-concluded-disable .ds3-step-concluded {
		background-color: #1e60fa !important;
		border-color: #1e60fa !important;
	}

	.ds3-step-concluded-disable .ds3-step-label .ds3-step-label-text {
		color: #1e60fa !important;
	}
}

.alerta-ia {
	background-color: #fee2e2;
	border-radius: 0.375rem;
	padding: 0.75rem;
	font-size: 14px;
	color: $hellboyPri;
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.alerta-ia__icon {
	font-size: 0.875rem;
	line-height: 1;
}

.alerta-ia__texto {
	line-height: 1.2;
}

/* adicionar-anamnese.component.scss */
.steps-indicator {
	display: flex;
	align-items: center;
	margin-bottom: 1.5rem;
	user-select: none;
}

.steps-indicator .step {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	cursor: pointer;
}

.steps-indicator .step-circle {
	width: 1.5rem;
	height: 1.5rem;
	border: 2px solid #ccc;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.75rem;
	color: #999;
}

.steps-indicator .step-label {
	font-size: 0.875rem;
	color: #999;
	font-weight: 600;
}

.steps-indicator .step.active .step-circle {
	border-color: #1e60fa;
	color: #1e60fa;
}

.steps-indicator .step.active .step-label {
	color: #1e60fa;
}

.steps-indicator .arrow {
	margin: 0 0.5rem;
	color: #ccc;
	font-size: 1rem;
}

.radio-itens {
	display: inline-flex;
	align-items: center;
	margin-right: 1.5rem;

	input[type="radio"] {
		margin-right: 0.5rem;
	}

	label {
		font-size: 0.875rem;
		color: hsla(213, 5%, 34%, 1);
		cursor: pointer;
	}
}
