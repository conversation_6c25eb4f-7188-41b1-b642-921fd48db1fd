import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-impedimento-treino",
	templateUrl: "./modal-impedimento-treino.component.html",
	styleUrls: ["./modal-impedimento-treino.component.scss"],
})
export class ModalImpedimentoTreinoComponent implements OnInit {
	constructor(private modal: NgbActiveModal, private router: Router) {}

	ngOnInit() {}

	dismiss() {
		this.modal.dismiss();
	}

	concluir() {
		this.router.navigate(["treino", "montagem-programa", "prescricao"]);
		this.dismiss();
	}
}
