import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { PerfilAcessoRecursoNome, TreinoApiFichaService } from "treino-api";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { map } from "rxjs/operators";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-editor-detalhes-ficha",
	templateUrl: "./editor-detalhes-ficha.component.html",
	styleUrls: ["./editor-detalhes-ficha.component.scss"],
})
export class EditorDetalhesFichaComponent implements OnInit {
	constructor(
		private fichaService: TreinoApiFichaService,
		private activeModal: NgbActiveModal,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required]),
		categoriaId: new FormControl(""),
		tipo_execucao: new FormControl("", [Validators.required]),
		dias_semana: new FormArray([
			new FormControl(null),
			new FormControl(null),
			new FormControl(null),
			new FormControl(null),
			new FormControl(null),
			new FormControl(null),
			new FormControl(null),
		]),
		mensagem: new FormControl(""),
	});
	categoriaOpcoes: Array<any> = [];
	tiposExecucaoItems = [
		{
			id: "ALTERNADO",
			nome: "Alternado",
		},
		{
			id: "DIAS_SEMANA",
			nome: "Dias da semana",
		},
	];
	diasSemanaMap = [
		"SEGUNDA",
		"TERCA",
		"QUARTA",
		"QUINTA",
		"SEXTA",
		"SABADO",
		"DOMINGO",
	];
	permissaoProgramaTreino;

	ready(fichaFormGroup) {
		this.loadData().subscribe(() => {
			this.fillForm(fichaFormGroup);
			this.cd.detectChanges();
		});
	}

	ngOnInit() {
		this.formGroup
			.get("tipo_execucao")
			.valueChanges.subscribe((tipo) => this.updateTipoHandler(tipo));
		this.carregarPermissoes();
		if (!this.permissaoProgramaTreino.editar) {
			this.disabledAllCampos();
		}
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
	}

	private disabledAllCampos() {
		setTimeout(() => {
			this.formGroup.get("nome").disable({ emitEvent: false });
			this.formGroup.get("categoriaId").disable({ emitEvent: false });
			this.formGroup.get("tipo_execucao").disable({ emitEvent: false });
			this.formGroup.get("dias_semana").disable({ emitEvent: false });
			this.formGroup.get("mensagem").disable({ emitEvent: false });
		});
	}

	get diasArray(): FormArray {
		const fa: any = this.formGroup.get("dias_semana");
		return fa;
	}

	private updateTipoHandler(tipo) {
		this.diasSemanaMap.forEach((diaSemana, index) => {
			if (tipo === "ALTERNADO") {
				this.diasArray.controls[index].reset();
				this.diasArray.controls[index].disable();
			} else {
				this.diasArray.controls[index].enable();
			}
		});
	}

	private fillForm(fichaFormGroup) {
		if (!fichaFormGroup.dias_semana) {
			fichaFormGroup.dias_semana = [];
		}
		this.formGroup.patchValue(fichaFormGroup);
	}

	private loadData() {
		return this.fichaService.obterTodasCategoriasFicha().pipe(
			map((dados) => {
				this.categoriaOpcoes = dados;
				return true;
			})
		);
	}

	dismiss() {
		this.activeModal.close();
	}

	salvarHandler() {
		if (this.formGroup.valid) {
			for (const key in this.formGroup.controls) {
				if (
					Object.prototype.hasOwnProperty.call(this.formGroup.controls, key)
				) {
					const element = this.formGroup.controls[key];
					element.markAsTouched();
				}
			}
			this.activeModal.close(this.formGroup);
		}
	}
}
