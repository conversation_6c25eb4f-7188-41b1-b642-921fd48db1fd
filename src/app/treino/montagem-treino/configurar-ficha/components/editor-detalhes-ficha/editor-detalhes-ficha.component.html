<div class="modal-body">
	<div class="row-form">
		<pacto-cat-form-input
			[control]="formGroup.get('nome')"
			[errorMsg]="'Defina um nome para a ficha.'"
			[id]="'input-nome-ficha'"
			[label]="'Nome'"
			[placeholder]="'Nome da Ficha'"
			[readonly]="!permissaoProgramaTreino?.editar"></pacto-cat-form-input>
		<pacto-cat-select
			[control]="formGroup.get('categoriaId')"
			[idKey]="'id'"
			[id]="'select-categoria-ficha'"
			[items]="categoriaOpcoes"
			[labelKey]="'nome'"
			[label]="'Categoria de ficha'"></pacto-cat-select>
	</div>
	<div class="row-form">
		<pacto-cat-select
			[control]="formGroup.get('tipo_execucao')"
			[idKey]="'id'"
			[id]="'select-tipo-execucao'"
			[items]="tiposExecucaoItems"
			[labelKey]="'nome'"
			[label]="'Tipo de Execução'"></pacto-cat-select>
		<div class="bloco-dias-semana">
			<div class="label-form">Dias da semana</div>
			<div class="row-form">
				<pacto-cat-checkbox
					*ngFor="let diaSemana of diasSemanaMap; let index = index"
					[control]="diasArray.controls[index]"
					[id]="'weekday-' + index"
					[label]="diasSemana.getLabel(diaSemana)"></pacto-cat-checkbox>
			</div>
		</div>
	</div>
	<pacto-cat-form-textarea
		[control]="formGroup.get('mensagem')"
		[id]="'input-mensagem-aluno'"
		[label]="'Mensagem ao aluno'"
		[placeholder]="'Deixe uma mensagem para o aluno'"></pacto-cat-form-textarea>
</div>
<div *ngIf="permissaoProgramaTreino?.editar" class="modal-footer">
	<pacto-cat-button
		(click)="salvarHandler()"
		[label]="'Concluir'"></pacto-cat-button>
</div>

<pacto-traducoes-xingling #diasSemana>
	<span xingling="SEGUNDA">Seg</span>
	<span xingling="TERCA">Ter</span>
	<span xingling="QUARTA">Qua</span>
	<span xingling="QUINTA">Qui</span>
	<span xingling="SEXTA">Sex</span>
	<span xingling="SABADO">Sab</span>
	<span xingling="DOMINGO">Dom</span>
</pacto-traducoes-xingling>
