import { HighlightDirective } from "./modal-criar-programa/highlight-directive/highlight.pipe";
import { Ng2SearchPipeModule } from "ng2-search-filter";
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { MontagemTreinoRoutingModule } from "./montagem-treino.routing.module";
import { BaseSharedModule } from "@base-shared/base-shared.module";

import { ModalEdicaoProgramaComponent } from "./modal-edicao-programa/modal-edicao-programa.component";
import { ListaAtividadesComponent } from "./configurar-ficha/components/lista-atividades/lista-atividades.component";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { TreinoCoreModule } from "@treino-core/treino-core.module";
import { ConfigurarFichaComponent } from "./configurar-ficha/configurar-ficha.component";
import { AdicionarAtividadeComponent } from "./configurar-ficha/components/adicionar-atividade/adicionar-atividade.component";
import { EditorDetalhesFichaComponent } from "./configurar-ficha/components/editor-detalhes-ficha/editor-detalhes-ficha.component";
import { AtividadeFichaComponent } from "./configurar-ficha/components/atividade-ficha/atividade-ficha.component";
import { EditorSeriesComponent } from "./configurar-ficha/components/editor-series/editor-series.component";
import { ModalCriarFichaComponent } from "./modal-criar-ficha/modal-criar-ficha.component";
import { TituloModalEdicaoFichaComponent } from "./titulo-modal-edicao-ficha/titulo-modal-edicao-ficha.component";
import { HomeFitListComponent } from "../homefit/components/home-fit-list/home-fit-list.component";
import { HomeFitSaveComponent } from "../homefit/components/home-fit-save/home-fit-save.component";
import { AtividadeHomeFitAddComponent } from "../homefit/components/atividade-home-fit-add/atividade-home-fit-add.component";
import { HomeFitTreinoDetalhesComponent } from "../homefit/components/home-fit-treino-detalhes/home-fit-treino-detalhes.component";
import { ModalCriarProgramaComponent } from "./modal-criar-programa/modal-criar-programa.component";
import { ModalAdicionarAtividadesComponent } from "./modal-adicionar-atividades/modal-adicionar-atividades.component";
import { PadraoSeriesComponent } from "./padrao-series/padrao-series.component";
import { ListaPrescricaoTreinoComponent } from "./lista-prescricao-treino/lista-prescricao-treino.component";
import { CatMultiSelectFilterNumberComponent } from "../../agenda/agenda/agenda-turma/agenda-control/cat-multi-select-filter-number/cat-multi-select-filter-number.component";
import { AgendaModule } from "../../agenda/agenda/agenda.module";
import { TreinoStateService } from "./lista-prescricao-treino/treino-state.service";
import { ModalEnviarProgramaAlunosComponent } from "./modal-enviar-programa-alunos/modal-enviar-programa-alunos.component";
import { CatTolltipModule } from "ui-kit";
import { AdicionarAnamneseComponent } from "./configurar-ficha/components/adicionar-anamnese/adicionar-anamnese.component";
import { CdkStepperModule } from "@angular/cdk/stepper";
import { ModalImpedimentoTreinoComponent } from "./configurar-ficha/components/adicionar-anamnese/modal-impedimento-treino/modal-impedimento-treino.component";

@NgModule({
	imports: [
		CommonModule,
		BaseSharedModule,
		TreinoCoreModule,
		RouterModule,
		MontagemTreinoRoutingModule,
		DragDropModule,
		Ng2SearchPipeModule,
		AgendaModule,
		CatTolltipModule,
		CdkStepperModule,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
	declarations: [
		HomeFitListComponent,
		HomeFitSaveComponent,
		HomeFitTreinoDetalhesComponent,
		ListaPrescricaoTreinoComponent,
		AtividadeHomeFitAddComponent,
		ModalEdicaoProgramaComponent,
		ConfigurarFichaComponent,
		AdicionarAtividadeComponent,
		EditorDetalhesFichaComponent,
		ListaAtividadesComponent,
		AtividadeFichaComponent,
		EditorSeriesComponent,
		ModalCriarFichaComponent,
		ModalEnviarProgramaAlunosComponent,
		TituloModalEdicaoFichaComponent,
		ModalCriarProgramaComponent,
		ModalAdicionarAtividadesComponent,
		PadraoSeriesComponent,
		HighlightDirective,
		AdicionarAnamneseComponent,
		ModalImpedimentoTreinoComponent,
	],
	exports: [
		ConfigurarFichaComponent,
		AdicionarAtividadeComponent,
		EditorDetalhesFichaComponent,
		ListaAtividadesComponent,
		AtividadeFichaComponent,
		EditorSeriesComponent,
		ModalCriarFichaComponent,
		ModalEnviarProgramaAlunosComponent,
		TituloModalEdicaoFichaComponent,
		ModalCriarProgramaComponent,
		ModalAdicionarAtividadesComponent,
		PadraoSeriesComponent,
		AdicionarAnamneseComponent,
	],
	entryComponents: [
		AtividadeHomeFitAddComponent,
		ModalEdicaoProgramaComponent,
		ConfigurarFichaComponent,
		EditorDetalhesFichaComponent,
		EditorSeriesComponent,
		ModalCriarFichaComponent,
		ModalEnviarProgramaAlunosComponent,
		TituloModalEdicaoFichaComponent,
		ModalCriarProgramaComponent,
		ModalAdicionarAtividadesComponent,
		PadraoSeriesComponent,
		CatMultiSelectFilterNumberComponent,
		ModalImpedimentoTreinoComponent,
	],
	providers: [TreinoStateService],
})
export class MontagemTreinoModule {}
