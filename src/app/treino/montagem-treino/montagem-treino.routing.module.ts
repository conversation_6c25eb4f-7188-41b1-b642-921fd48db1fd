import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { HomeFitListComponent } from "../homefit/components/home-fit-list/home-fit-list.component";
import { HomeFitSaveComponent } from "../homefit/components/home-fit-save/home-fit-save.component";
import { HomeFitTreinoDetalhesComponent } from "../homefit/components/home-fit-treino-detalhes/home-fit-treino-detalhes.component";
import { ModuleName } from "@base-core/modulo/modulo.model";
import { ListaPrescricaoTreinoComponent } from "./lista-prescricao-treino/lista-prescricao-treino.component";
import { AdicionarAnamneseComponent } from "./configurar-ficha/components/adicionar-anamnese/adicionar-anamnese.component";

const routes: Routes = [
	{
		path: "",
		data: { module: ModuleName.TREINO },
		children: [
			{
				path: "lista-treinos",
				component: HomeFitListComponent,
			},
			{
				path: "save-treino/:id",
				component: HomeFitSaveComponent,
			},
			{
				path: "treino-detalhes/:id",
				component: HomeFitTreinoDetalhesComponent,
			},
			{
				path: "prescricao",
				component: ListaPrescricaoTreinoComponent,
			},
			{
				path: "anamnese",
				component: AdicionarAnamneseComponent,
			},
		],
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class MontagemTreinoRoutingModule {}
