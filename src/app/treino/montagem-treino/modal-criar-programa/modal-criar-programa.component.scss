@import "src/assets/scss/pacto/plataforma-import.scss";

.sub-titulo-modal {
	@extend .type-p-small;
	color: $cinza03;
	margin-bottom: 26px;
}

.block-fichas-predefinida {
	margin-top: 20px;
}

.row-items {
	//display: flex;
	//justify-content: space-between;
	background-color: $cinzaClaroPri;
	border-left: 4px solid $cinzaClaroPri;
	margin-bottom: 8px;
	padding: 12px 20px;
	cursor: pointer;

	&:hover {
		border-left: 4px solid $pretoPri;
	}

	.descricao-ficha {
		@extend .type-p-small;
		color: $pretoPri;
		width: 50%;
		padding-right: 10px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.atividades-ficha {
		@extend .type-p-small;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: $cinza03;
		width: 50%;
	}
}

.search {
	position: relative;
	flex-grow: 1;

	input {
		padding-left: 30px;
	}

	i.pct {
		position: absolute;
		left: 10px;
		top: 12px;
	}
}

::ng-deep pacto-cat-button.treinoIA .pacto-button .content {
	text-transform: none !important;
}
