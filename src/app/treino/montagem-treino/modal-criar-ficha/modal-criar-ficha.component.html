<div class="modal-body">
	<div class="sub-titulo-modal">
		Selecione entre as fichas predefinidas ou crie a sua própria
	</div>

	<pacto-cat-select
		[control]="categoriaFichaControl"
		[idKey]="'id'"
		[id]="'select-categoria-ficha'"
		[items]="categoriaOpcoes"
		[labelKey]="'nome'"
		[label]="'Categorias das fichas'"></pacto-cat-select>

	<div class="search">
		<input
			#quickSearch
			[formControl]="nomeFichaControl"
			class="form-control"
			id="input-busca-rapida"
			placeholder="Nome da ficha"
			type="text" />
		<i class="pct pct-search"></i>
	</div>

	<div
		[maxHeight]="'160px'"
		class="block-fichas-predefinida"
		pactoCatSmoothScroll>
		<div
			(click)="criar<PERSON>icha<PERSON>andler(fichaPreDefinida.id)"
			*ngFor="let fichaPreDefinida of fichasPreDefinidas"
			class="row-items">
			<div class="descricao-ficha" title="{{ fichaPreDefinida?.nome }}">
				{{ fichaPreDefinida?.nome }}
			</div>
			<div class="atividades-ficha">
				{{
					fichaPreDefinida?.atividades?.length > 0
						? getAtividadesFichaResumido(fichaPreDefinida)
						: "Não atribuido"
				}}
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<pacto-cat-button
		(click)="criarFichaHandler()"
		[id]="'button-criar-nova-ficha'"
		[label]="'Criar nova ficha'"></pacto-cat-button>
</div>
