import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { debounceTime } from "rxjs/operators";
import {
	CategoriaFicha,
	FichaPrograma,
	TreinoApiFichaService,
} from "treino-api";

@Component({
	selector: "pacto-modal-criar-ficha",
	templateUrl: "./modal-criar-ficha.component.html",
	styleUrls: ["./modal-criar-ficha.component.scss"],
})
export class ModalCriarFichaComponent implements OnInit {
	categoriaFichaControl: FormControl = new FormControl();
	nomeFichaControl: FormControl = new FormControl();
	categoriaOpcoes: Array<CategoriaFicha> = [];
	fichasPreDefinidas: Array<FichaPrograma> = [];
	categoriaId: any;
	nomeFicha: any;

	constructor(
		private fichaService: TreinoApiFichaService,
		private ngbActiveModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.loadDataSelect();
		this.carregarFichasPreDefinidas();
		this.categoriaFichaControl.valueChanges.subscribe((value) => {
			this.categoriaId = value;
			this.carregarFichasPreDefinidas(this.categoriaId, this.nomeFicha);
		});
		this.nomeFichaControl.valueChanges
			.pipe(debounceTime(500))
			.subscribe((value) => {
				this.nomeFicha = value;
				this.carregarFichasPreDefinidas(this.categoriaId, this.nomeFicha);
			});
	}

	criarFichaHandler(fichaPreDefinadaId?: string) {
		this.ngbActiveModal.close(fichaPreDefinadaId);
	}

	getAtividadesFichaResumido(fichaPreDefinida: FichaPrograma) {
		let resultado = "";
		if (fichaPreDefinida.atividades) {
			fichaPreDefinida.atividades.forEach((atividadeFicha, index) => {
				if (index <= 1) {
					resultado += atividadeFicha.atividade.nome + ", ";
				}
			});
		}
		return resultado;
	}

	private carregarFichasPreDefinidas(categoriaId?: number, nomeFicha?: string) {
		this.fichaService
			.obterFichasPreDefinidos(categoriaId, nomeFicha)
			.subscribe((result) => {
				this.fichasPreDefinidas = result;
				this.cd.detectChanges();
			});
	}

	private loadDataSelect() {
		this.fichaService.obterTodasCategoriasFicha().subscribe((result) => {
			this.categoriaOpcoes = result;
			this.cd.detectChanges();
		});
	}
}
