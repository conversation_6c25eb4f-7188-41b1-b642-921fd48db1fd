@import "src/assets/scss/pacto/plataforma-import.scss";

.sub-titulo-modal {
	@extend .type-p-small;
	color: $cinza03;
	margin-bottom: 26px;
}

.block-fichas-predefinida {
	margin-top: 20px;
	height: 161px;
}

.row-items {
	display: flex;
	justify-content: space-between;
	background-color: $cinzaClaroPri;
	border-left: 4px solid $cinzaClaroPri;
	margin-bottom: 8px;
	padding: 12px 20px;
	cursor: pointer;

	&:hover {
		border-left: 4px solid $pretoPri;
	}

	.descricao-ficha {
		@extend .type-p-small;
		color: $pretoPri;
		width: 50%;
		padding-right: 10px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.atividades-ficha {
		@extend .type-p-small;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: $cinza03;
		width: 50%;
	}
}

.search {
	position: relative;
	margin-top: 20px;

	input {
		line-height: 40px;
		height: 40px;
		width: 100%;
		padding: 0 0 0 30px;

		&::placeholder {
			color: #a6aab1;
		}
	}

	i.pct {
		position: absolute;
		left: 10px;
		top: 12px;
	}
}
