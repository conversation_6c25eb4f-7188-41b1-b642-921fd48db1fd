@import "src/assets/scss/pacto/plataforma-import.scss";

.background-modal-atividades {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;

	.pos-header {
		padding: 0px 23px 23px 23px;
		height: 100%;
		overflow: auto;
	}

	.content {
		position: relative;
		display: flex;
		flex-direction: column;
		pointer-events: auto;
		background-color: $branco;
		background-clip: padding-box;
		border-radius: 8px;
		outline: 0;
		height: calc(100vh - 60px);
		width: calc(100vw - 460px);
		max-width: 1230px;
		min-width: 1050px;
		margin: 30px auto;
		overflow: hidden;
	}
}

.scroll-sticky {
	max-height: calc(100vh - 320px);
	overflow: auto;
}

.pretty-scroll {
	&::-webkit-scrollbar {
		padding: 11px 0 11px 11px;
		width: 11px;
		height: 18px;
	}

	&::-webkit-scrollbar-thumb {
		min-height: 100px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	&::-webkit-scrollbar-button {
		width: 11px;
		height: 0;
		display: none;
	}

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

.container-add-atividade {
	margin-top: 23px;
	padding: 24px;
	background-color: #eff2f7;
	border-radius: 4px;

	pacto-cat-multi-select-filter {
		::ng-deep.pacto-label {
			font-size: 14px;
			color: $pretoPri !important;
		}
	}

	.search {
		position: relative;

		i {
			position: absolute;
			color: $cinzaPri;
			font-size: 16px;
			top: 12px;
			right: 11px;
		}

		input {
			width: 100%;
			border: 1px solid #bcbfc7;
			padding: 2px 36px 0 3px;
			border-radius: 4px;
			line-height: 40px;
			min-height: 42px;
			color: #51555a;
			flex-wrap: wrap;
		}
	}

	.pacto-label {
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: 400;
		color: $pretoPri;
		line-height: 28px;
		display: block;
	}

	.adicionadas {
		font-size: 20px;
		font-weight: 400;
		padding-bottom: 11px;
		border-bottom: 1px solid #b4b7bb;
		margin-bottom: 9px;

		span {
			font-weight: 700;
		}
	}
}

.mgtp24 {
	margin-top: 24px;
}

.header {
	width: 100%;
	background: $cinzaClaroPri;
	top: 0;
	left: 0;
	padding: 26px 32px;
	border-bottom: 1px solid #dcdddf;

	.titulo-msg {
		font-size: 12px;
		font-weight: 400;
	}

	.titulo-modal {
		font-size: 16px;
		font-weight: 600;
	}

	.pct-x {
		font-size: 40px;
		font-weight: 400;
		cursor: pointer;
	}
}

.atividades-listagem {
	.atividades-pesquisa {
		width: 65%;
		display: inline-block;
		vertical-align: top;

		.container-add-atividade {
			padding: 16px;
			padding-left: 32px;
			background-color: $branco;

			.adicionadas {
				color: $azulPacto02;
			}

			.block-actions {
				width: calc(40% - 75px);
			}

			.block-info-atividade {
				&.estanotreino {
					.btn-atv.btn-remove {
						display: inline;
					}

					.btn-add {
						display: none;
					}
				}

				.btn-atv {
					cursor: pointer;
					display: inline;
					padding: 8px 20px 8px 20px;
					font-size: 16px;
					font-style: normal;
					font-weight: 700;
					line-height: 24px;
					letter-spacing: 0px;
					text-align: center;
					border-radius: 4px;
					width: 132px;
				}

				.btn-add {
					background-color: $branco;
					color: $azulPacto02;
				}

				.btn-atv.btn-remove {
					display: none;
					background-color: $hellboyPri;
					color: $branco;
				}

				.descricao-atividade {
					color: $preto02;
					font-weight: 600;
					font-size: 16px;
				}
			}
		}
	}

	.atividades-adicionadas {
		width: 35%;
		display: inline-block;
		vertical-align: top;

		.container-add-atividade {
			padding: 16px;
			min-height: 115px;
		}
	}
}

.atividades-adicionadas {
	position: -webkit-sticky; /* Safari */
	position: sticky;
	top: 0;
}

.block-info-atividade {
	padding: 7px 0;
	border-bottom: 1px dashed #dcdddf;

	.lazy {
		height: 54px;
		width: 54px;
	}

	pacto-cat-person-avatar {
		vertical-align: middle;
		display: inline-block;
		border-radius: 5px;
		border: 1px solid #b4b7bb;
	}

	.descricao-atividade {
		color: $cinzaPri;
		font-weight: 600;
		font-size: 16px;
		width: 60%;
		display: inline-block;
		vertical-align: middle;
		margin-left: 19px;
	}

	.block-actions {
		display: inline-block;
		vertical-align: middle;
		width: calc(40% - 87px);
		text-align: right;

		i {
			cursor: pointer;
		}

		.btn {
			padding: 0;
		}

		.btn-atv.btn-remove {
			.pct-trash-2 {
				color: $branco;
			}
		}

		.pct-trash-2 {
			color: $hellboyPri;
		}

		::ng-deep {
			&.btn button {
				font-size: 16px;
				line-height: 40px;
				text-transform: inherit;
				font-weight: 700;
			}

			&.btn i {
				font-size: 16px;
				line-height: 40px;
				font-weight: 700;
			}
		}

		::ng-deep {
			&.btn-add button {
				border: 1px solid $branco;
				background: $branco;
				color: $azulPacto02;
			}

			&.btn-add button i {
				color: $azulPacto02;
			}
		}

		::ng-deep {
			&.btn-remove button {
				border: 1px solid $hellboyPri;
				background: $hellboyPri;
			}
		}
	}
}

.salvando-status {
	margin-top: -22px;
	margin-right: 20px;
}

.componente-corpo-grupo-muscular {
	display: flex;
	justify-content: space-around;

	::ng-deep .grupo-muscular-frontal {
		max-height: 227px;
		max-width: 138px;
	}

	::ng-deep .grupo-muscular-posterior {
		max-height: 227px;
		max-width: 138px;
	}
}
