<div class="background-modal-atividades">
	<div class="content">
		<div class="header">
			<div class="row">
				<div class="col-md-9">
					<div class="row">
						<div class="col-md-5">
							<div class="titulo-msg">Você está editando a ficha:</div>
							<div class="titulo-modal" title="{{ ficha?.nome }}">
								{{ ficha?.nome }}
							</div>
						</div>

						<div *ngIf="programa" class="col-md-3">
							<div class="titulo-msg">Dentro do programa:</div>
							<div class="titulo-modal" title="{{ programa?.nome }}">
								{{ programa?.nome }}
							</div>
						</div>

						<div *ngIf="aluno" class="col-md-3">
							<div class="titulo-msg">Criado para:</div>
							<div class="titulo-modal" title="{{ aluno?.nome }}">
								{{ aluno?.nome }}
							</div>
						</div>
					</div>
				</div>

				<div class="col-md-3 text-right">
					<img
						*ngIf="salvo && loading == true"
						class="salvando-status"
						src="./assets/images/salvando.gif" />
					<img
						*ngIf="salvo && loading === false"
						class="salvando-status"
						src="./assets/images/salvo.png" />
					<i (click)="fecharModal()" class="pct pct-x"></i>
				</div>
			</div>
		</div>
		<div class="pos-header pretty-scroll">
			<div class="container-add-atividade">
				<div class="row">
					<div class="col-md-6 componente-corpo-grupo-muscular">
						<div class="frontal">
							<pacto-corpo-frontal-grupo-muscular
								(grupoMuscularSelecionado)="onGrupoMuscularSelecionado($event)"
								*ngIf="allGruposMusculares"
								[listaAtiva]="gruposMuscularesSelecionados"
								[listaCompletaGrupoMuscular]="allGruposMusculares"
								[podeSelecionarGrupoMuscular]="
									true
								"></pacto-corpo-frontal-grupo-muscular>
						</div>

						<div class="posterior">
							<pacto-corpo-posterior-grupo-muscular
								(grupoMuscularSelecionado)="onGrupoMuscularSelecionado($event)"
								*ngIf="allGruposMusculares"
								[listaAtiva]="gruposMuscularesSelecionados"
								[listaCompletaGrupoMuscular]="allGruposMusculares"
								[podeSelecionarGrupoMuscular]="
									true
								"></pacto-corpo-posterior-grupo-muscular>
						</div>
					</div>

					<div class="col-md-6">
						<div style="display: flex">
							<div class="col-md-6">
								<div class="search-atv">
									<span class="pacto-label">Buscar atividade por nome</span>
									<div class="search">
										<i class="pct pct-search"></i>
										<input [formControl]="fg.get('nome')" type="text" />
									</div>
								</div>

								<div class="">
									<pacto-cat-multi-select-filter
										#gruposSelect
										[control]="fg.get('gruposMusculares')"
										[endpointUrl]="_rest.buildFullUrl('grupos-musculares')"
										[id]="'select-grupos'"
										[labelKey]="'nome'"
										[paramBuilder]="selectBuilder"
										[resposeParser]="responseParser"
										i18n-label="@@adicionar-atividade:grupos_musculares"
										label="Grupos musculares"></pacto-cat-multi-select-filter>
								</div>

								<div class="">
									<pacto-cat-multi-select-filter
										#musculosSelect
										[control]="fg.get('musculos')"
										[endpointUrl]="_rest.buildFullUrl('musculos')"
										[id]="'select-musculos'"
										[labelKey]="'nome'"
										[paramBuilder]="selectBuilder"
										[resposeParser]="responseParser"
										i18n-label="@@adicionar-atividade:musculos"
										label="Músculos"></pacto-cat-multi-select-filter>
								</div>
							</div>

							<div class="col-md-6">
								<div class="">
									<pacto-cat-multi-select-filter
										#aparelhoSelect
										[control]="fg.get('aparelhos')"
										[endpointUrl]="_rest.buildFullUrl('aparelhos')"
										[id]="'select-aparelho'"
										[labelKey]="'nome'"
										[paramBuilder]="selectBuilder"
										[resposeParser]="responseParser"
										i18n-label="@@adicionar-atividade:aparelho_filtro"
										label="Aparelhos"></pacto-cat-multi-select-filter>
								</div>

								<div class="">
									<pacto-cat-multi-select-filter
										#categoriasSelect
										[control]="fg.get('categorias')"
										[endpointUrl]="_rest.buildFullUrl('categoria-atividade')"
										[id]="'select-categorias'"
										[labelKey]="'nome'"
										[paramBuilder]="selectBuilder"
										[resposeParser]="responseParser"
										i18n-label="@@adicionar-atividade:categorias_atividades"
										label="Categorias de atividades"></pacto-cat-multi-select-filter>
								</div>

								<div class="">
									<pacto-cat-multi-select-filter
										#niveisSelect
										[control]="fg.get('niveis')"
										[endpointUrl]="_rest.buildFullUrl('niveis')"
										[id]="'select-niveis'"
										[labelKey]="'nome'"
										[paramBuilder]="selectBuilder"
										[resposeParser]="responseParser"
										i18n-label="@@adicionar-atividade:niveis"
										label="Níveis"></pacto-cat-multi-select-filter>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="atividades-listagem">
				<div class="atividades-adicionadas">
					<div class="container-add-atividade">
						<div class="adicionadas">
							Atividades cadastradas na ficha
							<span>({{ listAtividades?.length }})</span>
						</div>
						<div #atvadicionadas class="scroll-sticky pretty-scroll">
							<ng-container *ngFor="let atividade of listAtividades">
								<div class="block-info-atividade">
									<img
										class="lazy"
										loading="lazy"
										src="{{
											atividade?.imageUri
												? atividade.imageUri
												: 'assets/images/no-image.jpg'
										}}" />
									<div
										class="descricao-atividade"
										title="NOME ORIGINAL: {{ atividade.nome }}">
										<span [hidden]="!atividade.complementoNomeAtividade">
											{{ atividade.nomeAtivFicha }} ({{
												atividade.complementoNomeAtividade
											}})
										</span>
										<span [hidden]="atividade.complementoNomeAtividade">
											{{ atividade.nomeAtivFicha }}
										</span>
									</div>
									<div class="block-actions">
										<i
											(click)="removeAtividadeHandler(atividade)"
											class="pct pct-trash-2"
											title="Remover atividade da ficha"></i>
									</div>
								</div>
							</ng-container>
						</div>
					</div>
				</div>
				<div class="atividades-pesquisa">
					<div class="container-add-atividade">
						<div class="adicionadas">
							Atividades cadastradas no sistema
							<span>({{ totalAtividades }})</span>
						</div>
						<ng-container #itemsContainer></ng-container>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<ng-template #item let-atividadeAdd="atividadeAdd">
	<div
		[ngClass]="atividadeAdd?.estaNoTreino === true ? 'estanotreino' : ''"
		class="block-info-atividade">
		<img
			class="lazy"
			loading="lazy"
			src="{{
				atividadeAdd?.image ? atividadeAdd.image : 'assets/images/no-image.jpg'
			}}" />
		<div
			class="descricao-atividade"
			title="NOME ORIGINAL: {{ atividadeAdd?.nome }}">
			<span>{{ atividadeAdd?.nome }}</span>
		</div>
		<div class="block-actions">
			<div (click)="adicionarHandler(atividadeAdd)" class="btn-atv btn-add">
				<i class="pct pct-plus"></i>
				Adicionar
			</div>
			<div
				(click)="removeAtividadeFromListHandler(atividadeAdd)"
				class="btn-atv btn-remove">
				<i class="pct pct-trash-2"></i>
				Remover
			</div>
		</div>
	</div>
</ng-template>
