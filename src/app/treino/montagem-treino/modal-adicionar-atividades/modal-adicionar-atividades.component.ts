import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewContainerRef,
} from "@angular/core";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { ConfigurarFichaService } from "../configurar-ficha.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	DataFiltro,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
} from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	FichaPrograma,
	Aluno,
	TreinoApiAtividadeService,
	Programa,
	GrupoMuscular,
	TreinoApiGrupoMuscularService,
} from "treino-api";
import {
	buildAtividadeFichaForm,
	buildSerieAtividadeForm,
} from "../configurar-ficha/ficha-form-group";
import { debounceTime } from "rxjs/operators";
import { Subscription } from "rxjs";
import { LoaderService } from "../../../loader/loader.service";

@Component({
	selector: "pacto-modal-adicionar-atividades",
	templateUrl: "./modal-adicionar-atividades.component.html",
	styleUrls: ["./modal-adicionar-atividades.component.scss"],
})
export class ModalAdicionarAtividadesComponent implements OnInit {
	@Input() aluno: Aluno;
	@Input() programa: Programa;
	@Input() ficha: FichaPrograma;
	@ViewChild("atvadicionadas", { static: false })
	private myScrollContainer: ElementRef;
	@ViewChild("itemsContainer", { read: ViewContainerRef, static: false })
	container: ViewContainerRef;
	@ViewChild("item", { read: TemplateRef, static: false })
	template: TemplateRef<any>;
	atividades: Array<any> = [];
	totalAtividades = 0;
	fichaSendoModificada = false;
	@Output() validaFicha = new EventEmitter();
	@Output() fechar = new EventEmitter();
	salvo = false;
	loading = false;
	notSearching = false;
	private searchSub: Subscription;
	private subscription: Subscription;
	listAtividades: Array<{
		frontId: number;
		nome: string;
		nomeAtivFicha: string;
		complementoNomeAtividade: string;
		setAtividade1?: string;
		setAtividade2?: string;
		imageUri: string;
		tipo: string;
		metodoExecucao: string;
		serieApenasDuracao: boolean;
		sequencia: number;
		formGroup: FormGroup;
	}> = [];
	fg = new FormGroup({
		nome: new FormControl(""),
		aparelhos: new FormControl(""),
		categorias: new FormControl(""),
		configEmpresas: new FormControl(""),
		gruposMusculares: new FormControl([]),
		musculos: new FormControl(""),
		niveis: new FormControl(""),
	});
	allGruposMusculares: Array<GrupoMuscular> = [];
	gruposMuscularesSelecionados: Array<GrupoMuscular> = [];

	constructor(
		private configurarFichaService: ConfigurarFichaService,
		private loaderService: LoaderService,
		private atividadeService: TreinoApiAtividadeService,
		private rest: RestService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.init();
	}

	init() {
		this.fg.valueChanges.pipe(debounceTime(300)).subscribe((value) => {
			this.fetchData();
		});
		this.fetchData();
		this.ficha = this.configurarFichaService.obterFichaAtual();
		this.configurarFichaService.atividadesFichaView$.subscribe(
			(atividadesFicha) => {
				setTimeout(() => {
					this.setupList(atividadesFicha);
				});
			}
		);
		this.configurarFichaService.fichaSendoModificada$.subscribe((value) => {
			this.fichaSendoModificada = value;
			this.validaFicha.emit(this.fichaSendoModificada);
			this.cd.detectChanges();
		});

		this.subscription = this.loaderService.main$.subscribe((loading) => {
			this.loading = loading && this.notSearching;
		});
		this.carregarTodosGruposMusculares();
		this.cd.detectChanges();
	}

	carregarTodosGruposMusculares() {
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.allGruposMusculares = dados;
				this.cd.detectChanges();
			});
	}

	onGrupoMuscularSelecionado(event) {
		this.fetchData();
	}

	get dataFiltro(): DataFiltro {
		if (!this.fg.get("gruposMusculares").value) {
			this.fg.get("gruposMusculares").setValue([]);
		}
		this.gruposMuscularesSelecionados = this.fg.get("gruposMusculares").value;
		this.cd.detectChanges();
		return {
			page: 0,
			size: 20,
			filters: {
				aparelhos: this.idsFromArray(this.fg.get("aparelhos").value),
				empresas: this.idsFromArray(this.fg.get("configEmpresas").value),
				categorias: this.idsFromArray(this.fg.get("categorias").value),
				niveis: this.idsFromArray(this.fg.get("niveis").value),
				grupoMuscularesIds: this.idsFromArray(
					this.fg.get("gruposMusculares").value
				),
				musculos: this.idsFromArray(this.fg.get("musculos").value),
				quicksearchValue: this.fg.get("nome").value,
				quicksearchFields: ["nome"],
			},
		};
	}

	idsFromArray(array): Array<number> {
		const ids = new Array<number>();
		if (array) {
			array.forEach((obj) => {
				ids.push(obj.id);
			});
		}
		return ids;
	}

	fetchData() {
		if (this.searchSub) {
			this.searchSub.unsubscribe();
		}
		const idFicha = this.ficha === undefined ? null : this.ficha.id;
		this.searchSub = this.atividadeService
			.obterAtividadesMontarTreino(idFicha, this.dataFiltro)
			.subscribe((dados) => {
				this.atividades = dados.content;
				this.totalAtividades = dados.totalElements;
				this.buildData();
				this.cd.detectChanges();
			});
	}

	private buildData() {
		this.container.clear();
		this.atividades.forEach((item) => {
			this.container.createEmbeddedView(this.template, {
				atividadeAdd: item,
			});
		});
	}

	adicionarHandler(atividade) {
		if (this.salvo === false) {
			this.salvo = true;
		}
		this.notSearching = true;
		atividade.formGroup = this.getDto(atividade.id);
		this.configurarFichaService.adicionarAtividadeFicha(
			this.getDto(atividade.id),
			atividade
		);
		this.salvarFichaHandler(atividade);
		this.scrollToBottom();
	}

	salvarFichaHandler(atividade) {
		if (this.fichaSendoModificada) {
			this.configurarFichaService.salvarFicha().subscribe(() => {
				atividade.estaNoTreino = true;
				this.notSearching = false;
				this.cd.detectChanges();
			});
		}
	}

	removeAtividadeHandler(atividade) {
		if (this.salvo === false) {
			this.salvo = true;
			this.cd.detectChanges();
		}
		this.notSearching = true;
		this.configurarFichaService.removeAtividadeLista(atividade.frontId);
		this.atividades.forEach((atv) => {
			if (atividade.nome === atv.nome) {
				atv.estaNoTreino = false;
			}
		});
		this.notSearching = false;
		this.cd.detectChanges();
	}

	removeAtividadeFromListHandler(atividade) {
		if (this.salvo === false) {
			this.salvo = true;
		}
		this.notSearching = true;
		this.listAtividades.forEach((atv) => {
			if (atividade.nome === atv.nome) {
				this.configurarFichaService.removeAtividadeLista(atv.frontId);
			}
		});
		atividade.estaNoTreino = false;
		this.notSearching = false;
		this.cd.detectChanges();
	}

	private getDto(idAtividade): FormGroup {
		const atividadeFichaForm: FormGroup = buildAtividadeFichaForm();
		atividadeFichaForm.get("atividadeId").setValue(idAtividade);
		const serieAtividadeForm = buildSerieAtividadeForm();
		serieAtividadeForm.get("sequencia").setValue(1);
		serieAtividadeForm.get("repeticoes").setValue(0);
		serieAtividadeForm.get("carga").setValue(0);
		serieAtividadeForm.get("cadencia").setValue("");
		serieAtividadeForm.get("distancia").setValue(0);
		serieAtividadeForm.get("descanso").setValue(0);
		serieAtividadeForm.get("duracao").setValue(0);
		serieAtividadeForm.get("velocidade").setValue(0);
		(atividadeFichaForm.get("series") as FormArray).push(serieAtividadeForm);
		return atividadeFichaForm;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		const result = response.content;
		result.forEach((atividade, index) => {
			atividade.index = index;
		});
		return result;
	};

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	fecharModal() {
		this.ficha = null;
		this.fechar.emit();
	}

	private setupList(atividadesFicha) {
		this.listAtividades = atividadesFicha;
		this.cd.detectChanges();
	}

	get _rest() {
		return this.rest;
	}

	scrollToBottom(): void {
		try {
			this.myScrollContainer.nativeElement.scrollTop =
				this.myScrollContainer.nativeElement.scrollHeight;
		} catch (err) {}
	}

	trackByFn(index, item) {
		return item.id;
	}
}
