<pacto-cat-layout-v2 *ngIf="ready">
	<div class="page-title">Prescrição de Treino</div>
	<div class="table-wrapper pacto-shadow">
		<div>
			<div class="filtros">
				<div class="search">
					<div>
						<input
							#quickSearch
							[formControl]="searchControl"
							class="form-control"
							id="input-busca-rapida"
							placeholder="Busca rápida..."
							type="text" />
						<i class="pct pct-search"></i>
					</div>
				</div>
				<div class="filtro1"></div>
				<div></div>
				<div class="compartilhar">
					<pacto-log [url]="logUrl" class="primary"></pacto-log>
					<pacto-share-button
						[columns]="obterColunasRelatorio()"
						[endpoint]="endpointUrl"
						[filterConfig]="filterConfig"
						[filtros]="getFiltersShare()"
						[sessionService]="sessionService"
						[titulo]="'Prescrição de treinos'"
						[total]="lista.length"
						telaId="exportacaoPrescricaoTreino"></pacto-share-button>
					<div
						#filterDropdown="ngbDropdown"
						class="filter filtros-atividade d-inline-block"
						ngbDropdown>
						<pacto-filter
							#filtroButton
							(filterChange)="filterHandler($event)"
							[filterConfig]="filterConfig"></pacto-filter>
					</div>
				</div>
			</div>

			<div class="container-prescricao">
				<div *ngFor="let item of lista" class="pessoa-prescricao">
					<div class="grid-prescricao">
						<div class="dados-pessoais">
							<div class="column-foto">
								<div
									(click)="abrirPerfilAluno(item.matricula)"
									*ngIf="item.tipo === 'Aluno'"
									[ngStyle]="{
										'background-image': getImagemAluno(item.urlFoto)
									}"
									class="foto-prescricao"></div>
								<div
									*ngIf="item.tipo === 'Colaborador'"
									[ngStyle]="{
										'background-image': getImagemAluno(item.urlFoto)
									}"
									class="foto-prescricao-professor"></div>
							</div>
							<div class="column-dados">
								<div>
									<span>
										{{ item.tipo }}
									</span>
									<ng-container *ngIf="item.tipo === 'Aluno'">
										<span [class]="item.situacao + ' statusBadge'">
											{{ item.situacao }}
										</span>
									</ng-container>
									<ng-container *ngIf="item.freepass">
										<span [class]="'freepass statusBadge'">Freepass</span>
									</ng-container>
									<ng-container *ngIf="item.gympass">
										<span [class]="'gympass statusBadge'">Gympass</span>
									</ng-container>

									<ng-container *ngIf="item.totalpass">
										<span [class]="'totalpass statusBadge'">TotalPass</span>
									</ng-container>
									<ng-container *ngIf="item.diaria">
										<span [class]="'diaria statusBadge'">Diária</span>
									</ng-container>
									<ng-container *ngIf="item.atestado">
										<span [class]="'atestado statusBadge'">Atestado</span>
									</ng-container>

									<ng-container *ngIf="item.avencer">
										<span [class]="'avencer statusBadge'">A vencer</span>
									</ng-container>
									<ng-container *ngIf="item.cancelado">
										<span [class]="'cancelado statusBadge'">Cancelado</span>
									</ng-container>
									<ng-container *ngIf="item.desistente">
										<span [class]="'desistente statusBadge'">Desistente</span>
									</ng-container>
									<ng-container *ngIf="item.dependente">
										<span [class]="'dependente statusBadge'">Dependente</span>
									</ng-container>
									<ng-container *ngIf="item.ferias">
										<span [class]="'ferias statusBadge'">Férias</span>
									</ng-container>
									<ng-container *ngIf="item.trancado">
										<span [class]="'trancado statusBadge'">Trancado</span>
									</ng-container>
									<ng-container *ngIf="item.vencido">
										<span [class]="'vencido statusBadge'">Vencido</span>
									</ng-container>
								</div>
								<span class="nome">{{ item.nome }}</span>
								<span class="matricula">{{ item.matricula }}</span>
							</div>
						</div>

						<div class="coluna-prescricao">
							<div class="column-dados">
								<span>Programa atual</span>
								<span class="nome">{{ item.programa }}</span>
								<span class="matricula">
									{{
										item.andamento > 0 ? item.andamento + "% executado" : "-"
									}}
								</span>
							</div>
						</div>
						<div class="coluna-prescricao">
							<div class="column-dados">
								<span>Período</span>
								<span *ngIf="item.inicio === '-'" class="nome">-</span>
								<span *ngIf="item.inicio !== '-'" class="nome">
									{{ item.inicio }} - {{ item.fim }}
								</span>
								<span class="matricula">
									<div
										*ngIf="item.geradoPorIA"
										class="situacaoTreino TREINO_IA">
										{{ notificacoesTranslate.getLabel("label_gerado_por_ia") }}
									</div>
									<div class="situacaoTreino {{ item.situacaoTreino }}">
										{{ notificacoesTranslate.getLabel(item.situacaoTreino) }}
									</div>
								</span>
							</div>
						</div>
						<div class="coluna-botao">
							<pacto-cat-button
								(click)="criarPrograma(item)"
								*ngIf="
									(!item.codigoPrograma || item.codigoPrograma == 0) &&
									item.permissaoCriar == true
								"
								[icon]="'plus'"
								[id]="'criar-programa'"
								i18n-label="
									perfil-aluno-treinamento:programas-aluno:criar-programa
								"
								label="CRIAR PROGRAMA"></pacto-cat-button>

							<pacto-cat-button
								(click)="revisarPrograma(item)"
								*ngIf="
									item.codigoPrograma &&
									item.codigoPrograma > 0 &&
									item.permissaoRevisar == true
								"
								[icon]="'pct pct-search'"
								[id]="'revisar-programa'"
								[type]="'OUTLINE'"
								class="revisar"
								i18n-label="
									@@perfil-aluno-treinamento:programas-aluno:revisar:label"
								label="REVISAR PROGRAMA"></pacto-cat-button>

							<pacto-cat-button
								(click)="aprovarPrograma(item, true)"
								*ngIf="
									item.revisadoProfessor == true &&
									item.geradoPorIA == true &&
									item.revisadoProfessor == true
								"
								[icon]="'pct pct-check'"
								[id]="'aprovar-programa'"
								i18n-label="
									@@perfil-aluno-treinamento:programas-aluno:aprovar:label"
								label="APROVAR PROGRAMA"></pacto-cat-button>

							<pacto-cat-button
								(click)="criarPrograma(item)"
								*ngIf="
									item.revisadoProfessor == false &&
									item.codigoPrograma &&
									item.codigoPrograma > 0 &&
									item.permissaoRenovar == true
								"
								[icon]="'refresh-cw'"
								[id]="'renovar-programa'"
								i18n-label="
									@@perfil-aluno-treinamento:programas-aluno:renovar:label"
								label="RENOVAR PROGRAMA"></pacto-cat-button>
						</div>
					</div>
					<div *ngIf="item.indicacaoMedica" class="aviso-medico">
						<i class="pct pct-alert-triangle"></i>
						<span>{{ item.indicacaoMedica }}</span>
					</div>
				</div>
			</div>

			<div class="footer-row">
				<ng-container>
					<div class="div-pagination">
						<ngb-pagination
							(pageChange)="pageChangeHandler($event)"
							[(page)]="ngbPage"
							[boundaryLinks]="true"
							[collectionSize]="data.totalElements"
							[ellipses]="false"
							[maxSize]="7"
							[pageSize]="data.size"
							[size]="'sm'"
							class="d-flex justify-content-end"></ngb-pagination>
					</div>

					<div class="div-show-and-select">
						<div class="div-select-qt-show">
							<pacto-cat-select
								(change)="listar()"
								[control]="pageSizeControl"
								[items]="itensPerPage"
								[size]="'SMALL'"></pacto-cat-select>
						</div>

						<div class="total-values">
							<span i18n="@@component-relatorio:mostrando">Mostrando</span>
							<span class="value">{{ data.content }}</span>
							<span i18n="@@component-relatorio:de">de</span>
							<span class="value">{{ data.totalElements }}</span>
						</div>
					</div>
				</ng-container>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>
<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="label_carteira_academia">Minha carteira na academia</span>
	<span xingling="label_carteira">Minha carteira</span>
	<span xingling="label_todos_alunos">Todos os alunos</span>
	<span xingling="label_na_academia">Alunos na academia</span>
	<span xingling="label_colaboradores">Colaboradores</span>
	<span xingling="label_gerado_por_ia">Treino por I.A.</span>
	<span xingling="tipocol_PR">Professor</span>
	<span xingling="tipocol_TW">Professor Treino</span>
	<span xingling="tipocol_PT">Personal Trainer</span>
	<span xingling="tipocol_OR">Orientador</span>
	<span xingling="tipocol_CO">Consultor</span>
	<span xingling="tipocol_PI">Personal Interno</span>
	<span xingling="tipocol_PE">Personal Externo</span>
	<span xingling="tipocol_TE">Terceirizado</span>
	<span xingling="tipocol_ES">Estúdio</span>
	<span xingling="tipocol_FO">Fornecedor</span>
	<span xingling="tipocol_CR">Coordenador</span>
	<span xingling="tipocol_MD">Médico</span>
	<span xingling="tipocol_FC">Funcionário</span>
	<span xingling="tipocol_AD">Administrador</span>
	<span xingling="PRESCRICAO">Prescrição de treino</span>
	<span xingling="SEM_TREINO">Sem treino</span>
	<span xingling="ATIVO">Em dia</span>
	<span xingling="TREINO_FUTURO">Treino futuro</span>
	<span xingling="A_APROVAR">A Aprovar</span>
	<span xingling="VENCIDO">Vencido</span>
	<span xingling="A_VENCER">A vencer</span>
</pacto-traducoes-xingling>
<span #messageErrorConfigPrescricao [hidden]="true">
	Não é permitido lançar programa de treino para alunos inativos ou visitantes
</span>
<span
	#sucessoCreate
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:create-programa:mensagem-success">
	Programa criado com sucesso.
</span>
