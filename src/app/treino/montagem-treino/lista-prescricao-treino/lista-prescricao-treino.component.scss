@import "projects/ui/assets/import.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

.page-title {
	@extend .type-h2;
	color: $pretoPri;
	margin-bottom: 30px;
}

.container-prescricao {
	display: grid;
	padding: 16px;

	.pessoa-prescricao {
		display: block;

		.grid-prescricao {
			display: grid;
			grid-template-columns: 1.4fr 1fr 1fr 1.4fr;
		}

		min-height: 110px;
		margin-bottom: 16px;
		padding: 16px;
		border: 1px solid $cinza03;
		border-radius: 8px;
		padding-right: 0px;

		.column-dados {
			min-height: 76px;
			display: grid;
			color: $preto05;
			font-size: 14px;
			font-weight: 400;
			line-height: 18px;

			.nome {
				font-size: 16px;
				text-transform: capitalize;
				line-height: 22px;
			}

			.matricula {
				color: $cinza05;
			}
		}

		.dados-pessoais {
			.column-dados {
				margin-left: 16px;

				.AT {
					background-color: $chuchuzinho01;
					color: $chuchuzinho06;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}

				.IN {
					background-color: $hellboy01;
					color: $hellboy06;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}

				.VI {
					background-color: $pequizao01;
					color: $pequizao06;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}

				.TR {
					background-color: #f3f3f4;
					color: #90949a;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}

				.totalpass {
					color: #9c5316;
					background-color: #efba8f;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.gympass {
					color: #9c5316;
					background-color: #f5d6bc;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.freepass {
					color: #0a4326;
					background-color: #1dc973;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.diaria {
					color: #107040;
					background-color: #bcf5d9;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.atestado {
					color: #105870;
					background-color: #63c7e9;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.avencer {
					background-color: #f5e7bc;
					color: #9c7b16;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.vencido {
					color: #705810;
					background-color: #e9c763;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.ferias,
				.trancado {
					background-color: #e4e5e7;
					color: #a1a4aa;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
				.cancelado,
				.desistente {
					background-color: #f5bcca;
					color: #9c1638;
					height: 22px;
					border-radius: 50px;
					padding: 3px 16px 3px 16px;
					margin-left: 4px;
					text-align: center;
					font-size: 12px;
					font-weight: 400;
					line-height: 12px;
				}
			}

			display: flex;

			.column-foto {
				padding-top: 7px;

				.foto-prescricao {
					width: 64px;
					height: 64px;
					border-radius: 32px;
					border: 1px solid #c7c7c7;
					background-repeat: no-repeat;
					background-position: center;
					background-size: cover;
					cursor: pointer;
				}

				.foto-prescricao-professor {
					width: 64px;
					height: 64px;
					border-radius: 32px;
					border: 1px solid #c7c7c7;
					background-repeat: no-repeat;
					background-position: center;
					background-size: cover;
				}
			}
		}

		.coluna-botao {
			padding-top: 19px;
			text-align: right;
			padding-right: 16px;

			.revisar {
				margin-right: 10px;
			}
		}
	}

	.aviso-medico {
		height: 35px;
		margin-right: 16px;
		border-radius: 5px;
		padding: 12px 9px 10px 16px;
		background-color: $laranjinha01;
		color: $laranjinha05;
		display: flex;
		font-size: 14px;
		font-weight: 700;
		line-height: 14px;
		margin-top: 16px;

		i {
			margin-right: 5px;
		}
	}

	.situacaoTreino {
		height: 22px;
		border-radius: 50px;
		padding: 5px 16px 5px 16px;
		text-align: center;
		font-size: 12px;
		font-weight: 400;
		line-height: 12px;
		background-color: $cinza01;
		color: $cinza06;

		&.VENCIDO {
			background-color: $hellboy01;
			color: $hellboy05;
		}

		&.A_VENCER {
			background-color: $pequizao01;
			color: $pequizao06;
		}

		&.ATIVO {
			background-color: $chuchuzinho01;
			color: $chuchuzinho06;
		}

		&.TREINO_FUTURO {
			background-color: $azulim01;
			color: $azulim06;
		}
		&.TREINO_IA {
			background-color: $azulim01;
			color: $azulim06;
			margin-bottom: 5px;
		}
		&.A_APROVAR {
			background-color: $azulim01;
			color: $azulim06;
		}
	}
}

.filtros {
	display: grid;
	grid-template-columns: 1.3fr 1.1fr 1.3fr 1fr;
	padding: 24px 16px 16px 16px;

	.filtro1 {
		padding-right: 10%;
	}

	.search {
		position: relative;
		flex-grow: 1;

		input {
			width: 70%;
			height: 41px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}

	pacto-cat-select {
		width: 70%;
	}

	.compartilhar {
		text-align: right;
	}

	::ng-deep {
		button.btn.pacto-primary {
			margin-right: 0px;
		}

		pacto-share-button {
			.btn.pacto-primary {
				color: $azulim05;
				background-color: $branco;
				border: 1px solid $azulim05;

				.icon-drop {
					border-left: 1px solid $azulim05;
				}
			}
		}
	}
}

.footer-row {
	position: relative;
	display: flex;
	margin-right: 16px;
	flex-direction: row-reverse;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}

	.div-show-and-select {
		display: flex;

		> * {
			margin-left: 20px;
		}
	}
}

.simple-total-row {
	background-color: #f9f9f9;
	border-top: 1px solid #ededed;
	border-bottom: 1px solid #ededed;
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 30px;
	margin-top: 14px;
	color: #7f7f7f;
}

.total-values {
	line-height: 32px;
	color: #9d9d9d;
	font-weight: bold;
	margin-right: 10px;
	font-size: 13px;

	.value {
		padding: 0px 3px;
	}
}

::ng-deep pacto-cat-multi-select-filter-number {
	.current-value {
		justify-content: flex-start !important;
	}
}

::ng-deep pacto-log.primary {
	margin-right: -20px;
	height: 40px;

	.btn {
		height: 40px;
		color: $azul;
		padding: 4px 10px;
		margin-right: 8px;
		background: $branco;
		border: 1px solid $azulimPri;
	}

	.btn:hover {
		color: $azul;
		background: $branco;
		border: 1px solid $azul;
	}

	.btn.btn-primary:focus,
	.show > .btn-primary.dropdown-toggle {
		color: $azul;
		background: $branco;
		box-shadow: 0 0 0 -0.2rem rgb(3, 127, 226);
	}

	.btn-primary:focus,
	.btn-primary.focus {
		box-shadow: 0 0 0 0.2rem rgba(3, 127, 226, 0.5);
	}
}

.filtros-atividade {
	margin-right: 10px;
	margin-left: 10px;
	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}
}
