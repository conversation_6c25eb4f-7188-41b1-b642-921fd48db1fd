<div class="content">
	<div class="digite">
		Digite abaixo o padrão que será aplicado a
		<b>todas as atividades</b>
		desta ficha. Dica: deixe em branco os campos que não serão padronizados.
	</div>

	<div class="detalhes-ficha">
		<div class="ficha">{{ ficha?.nome }}</div>
		<div class="nrAtividades">
			{{ nrAtividades }}
			{{ nrAtividades > 1 ? "atividades na ficha" : "atividade na ficha" }}
		</div>
	</div>

	<div class="block-input">
		<div class="input-criado">
			<div class="nome">
				<span i18n="@@montagem-treino-atividade-ficha:table:serie:title">
					Séries
				</span>
			</div>
			<div class="aux-wrapper">
				<input
					[formControl]="fg.get('series')"
					[textMask]="{ guide: false, mask: somenteInteiroMask }"
					maxlength="2"
					type="text" />
			</div>
		</div>
		<div class="input-criado">
			<div class="nome">
				<span i18n="@@montagem-treino-atividade-ficha:repeticoes:label">
					Repetições
				</span>
			</div>
			<div class="aux-wrapper">
				<input
					[formControl]="fg.get('repeticoes')"
					i18n-placeholder="@@montagem-treino-atividade-ficha:repeticoes:label"
					type="text" />
			</div>
		</div>
		<div class="input-criado">
			<div class="nome">
				<span i18n="@@montagem-treino-atividade-ficha:carga:label">Carga</span>
			</div>
			<div class="aux-wrapper">
				<input
					[formControl]="fg.get('carga')"
					i18n-placeholder="@@montagem-treino-atividade-ficha:carga:label"
					type="text" />
			</div>
		</div>

		<div class="input-criado">
			<div class="nome">
				<span>Cadência</span>
			</div>
			<div class="aux-wrapper">
				<input
					[formControl]="fg.get('cadencia')"
					i18n-placeholder="@@montagem-treino-atividade-ficha:cadencia:label"
					placeholder=""
					type="text" />
			</div>
		</div>
		<div class="input-criado">
			<div class="nome">
				<span i18n="@@montagem-treino-atividade-ficha:descanso:label">
					Descanso
				</span>
			</div>
			<div class="aux-wrapper">
				<input
					[formControl]="fg.get('descanso')"
					[textMask]="{ guide: false, mask: minuteSecondMask }"
					i18n-placeholder="@@montagem-treino-atividade-ficha:descanso:label"
					type="text" />
			</div>
		</div>
	</div>

	<div class="atencao">
		ATENÇÃO: Esta ação irá substituir os valores já definidos
	</div>

	<div class="options">
		<pacto-cat-button
			(click)="cancelar()"
			class="botao-padrao cancel"
			label="Cancelar"></pacto-cat-button>

		<pacto-cat-button
			(click)="definirPadrao()"
			class="botao-padrao definir"
			label="Definir padrão"></pacto-cat-button>
	</div>
</div>
