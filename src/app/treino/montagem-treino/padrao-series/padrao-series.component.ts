import { Component, Input, OnInit } from "@angular/core";
import { LocalizationService } from "@base-core/localization/localization.service";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { FichaPrograma } from "treino-api";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-padrao-series",
	templateUrl: "./padrao-series.component.html",
	styleUrls: ["./padrao-series.component.scss"],
})
export class PadraoSeriesComponent implements OnInit {
	ficha: FichaPrograma;
	nrAtividades: 0;
	fg = new FormGroup({
		series: new FormControl(""),
		repeticoes: new FormControl(""),
		carga: new FormControl(""),
		descanso: new FormControl(""),
		cadencia: new FormControl(""),
	});

	constructor(
		private localization: LocalizationService,
		private snotifyService: SnotifyService,
		private ngbActiveModal: NgbActiveModal
	) {}

	ngOnInit() {}

	cancelar() {
		this.ngbActiveModal.dismiss();
	}

	get minuteSecondMask() {
		return this.localization.getMinuteSecondMask();
	}

	get somenteInteiroMask() {
		return this.localization.getNumberMask();
	}

	definirPadrao() {
		if (this.fg.get("series") && this.fg.get("series").value === "0") {
			this.snotifyService.warning("Série não pode ficar zerada!");
		} else {
			this.ngbActiveModal.close(this.fg);
		}
	}
}
