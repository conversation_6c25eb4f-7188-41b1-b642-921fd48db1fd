@import "src/assets/scss/pacto/plataforma-import.scss";

.content {
	padding: 28px 32px;
}

.digite {
	font-weight: 400;
	font-size: 14px;

	b {
		font-weight: 700;
	}
}

.detalhes-ficha {
	.ficha {
		font-family: <PERSON><PERSON><PERSON> Sans;
		font-size: 16px;
		font-style: normal;
		font-weight: 700;
		line-height: 23px;
		letter-spacing: 0px;
		text-align: left;
	}

	.nrAtividades {
		font-family: Nunito Sans;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 23px;
		letter-spacing: 0px;
		text-align: left;
	}

	margin-top: 16px;
	padding: 16px;
	background-color: #eff2f7;
	border-radius: 4px;
}

.atencao {
	color: $pequizaoPri;
	font-weight: 600;
	font-size: 14px;
	margin-top: 20px;
}

.options {
	display: flex;
	margin-top: 20px;

	.botao-padrao.definir {
		margin-left: 2%;
	}

	.botao-padrao {
		width: 49%;
	}

	::ng-deep {
		&.botao-padrao button {
			text-transform: inherit;
			width: 100%;
			font-size: 16px;
			line-height: 40px;
			font-weight: 700;
		}

		&.cancel button {
			border: 1px solid #fafafa;
			background: #fafafa;
			color: $azulPacto02;
		}
	}
}

.block-input {
	margin-top: 20px;
	display: flex;
	justify-content: space-between;
	flex-grow: 1;
	width: 100%;

	.input-criado {
		.nome {
			font-size: 12px;
		}

		margin: 0px 5px;
		color: $preto02;
	}
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}
