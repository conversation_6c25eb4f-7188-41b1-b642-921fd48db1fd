<div class="modal-body">
	<div class="row">
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="fc.get('nome')"
				[errorMsg]="'Informa o nome do programa'"
				[id]="'input-nome-programa'"
				[label]="'Nome do Programa'"
				[placeholder]="'Dê um nome ao seu programa'"></pacto-cat-form-input>
		</div>
		<div class="col-md-6">
			<pacto-cat-form-select-filter
				[control]="fc.get('professorId')"
				[endpointUrl]="getUrlProfessorMontou"
				[errorMsg]="'Defina um professor.'"
				[idKey]="'id'"
				[id]="'select-professor-montou'"
				[labelKey]="'nome'"
				[label]="'Quem criou'"
				[paramBuilder]="professorSelectBuilder"
				[placeholder]="'Selecione professor'"
				[resposeParser]="responseParser"></pacto-cat-form-select-filter>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-3 col-md-6">
			<pacto-cat-form-select
				#diasSemana
				[control]="fc.get('qtdDiasSemana')"
				[errorMsg]="
					'Informe quantas vezes na semana, o programa será executada'
				"
				[idKey]="'id'"
				[id]="'select-quantidade-dias-semana'"
				[items]="[
					{ id: 1, label: '1x por semana' },
					{ id: 2, label: '2x por semana' },
					{ id: 3, label: '3x por semana' },
					{ id: 4, label: '4x por semana' },
					{ id: 5, label: '5x por semana' },
					{ id: 6, label: '6x por semana' },
					{ id: 7, label: '7x por semana' }
				]"
				[labelKey]="'label'"
				[label]="'Dias por Semana'"></pacto-cat-form-select>
		</div>
		<div class="col-lg-3 col-md-6">
			<pacto-cat-form-input
				[control]="fc.get('totalTreinos')"
				[errorMsg]="'Numero de aulas previstas obrigatório.'"
				[id]="'input-aulas-previstas'"
				[label]="'Aulas Previstas'"
				[textMask]="{ guide: false, mask: numberMask }"></pacto-cat-form-input>
		</div>
		<div [hidden]="apresentarBotaoPredefinir" class="col-lg-3 col-md-6">
			<pacto-cat-form-datepicker
				[control]="fc.get('inicio')"
				[errorMsg]="
					inicioErrorMsg ? inicioErrorMsg : 'Forneça uma data válida.'
				"
				[id]="'input-data-inicio'"
				[label]="'Data de Início'"></pacto-cat-form-datepicker>
		</div>
		<div [hidden]="apresentarBotaoPredefinir" class="col-lg-3 col-md-6">
			<pacto-cat-form-datepicker
				[control]="fc.get('termino')"
				[errorMsg]="fimErrorMsg ? fimErrorMsg : 'Forneça uma data válida.'"
				[id]="'input-data-termino'"
				[label]="'Data do Término'"></pacto-cat-form-datepicker>
		</div>
	</div>
</div>
<div
	*ngIf="permissaoProgramaTreino.editar"
	[ngClass]="{ actions: apresentarBotaoPredefinir }"
	class="modal-footer">
	<div class="col-md-12 no-padding">
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-6">
				<div
					(click)="tornarPredefinidoHandler()"
					*ngIf="!apresentarBotaoPredefinir"
					class="action-predefinir"
					id="btn-tornar-predefinido">
					<i class="pct pct-bookmark"></i>
					<div>PREDEFINIR ESTE PROGRAMA</div>
				</div>
			</div>
			<div class="col-lg-6 col-md-6 col-sm-6 position-right">
				<pacto-cat-button
					(click)="criarHandler()"
					[id]="'btn-salvar-programa'"
					[label]="'Salvar alterações'"></pacto-cat-button>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslates>
	<span xingling="camposObrigatorio">Campos obrigatórios não preenchidos.</span>
	<span xingling="saveSuccess">Programa salvo com sucesso.</span>
	<span xingling="registroDuplicado">
		Já existe um programa predefinido com esse mesmo nome.
	</span>
	<span xingling="usuariosempermissao">
		O usuário não possui permissão para Predefinir um treino.
	</span>
	<span xingling="periodoDuplicado">
		Já existe um programa criado, no periodo informado.
	</span>
	<span xingling="predefinirSuccess">
		Programa "{{ nomePrograma }}" predefinido com sucesso.
	</span>
	<span xingling="erro_formato_data_inicio_programa_treino">
		A data de início do programa está com formato incorreto.
	</span>
	<span xingling="erro_default">Ocorreu um problema ao executar a ação.</span>
</pacto-traducoes-xingling>
