import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	<PERSON>mponent,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";

import { RestService } from "@base-core/rest/rest.service";
import {
	CatFormSelectComponent,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { SessionService } from "@base-core/client/session.service";
import {
	PerfilAcessoRecursoNome,
	PerfilAcessoFuncionalidadeNome,
	Programa,
	TreinoApiProgramaService,
	ParamsCalcularAulasPrevista,
} from "treino-api";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

@Component({
	selector: "pacto-modal-edicao-programa",
	templateUrl: "./modal-edicao-programa.component.html",
	styleUrls: ["./modal-edicao-programa.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalEdicaoProgramaComponent implements OnInit, OnDestroy {
	@ViewChild("diasSemana", { static: true }) diasSemana: CatFormSelectComponent;
	@ViewChild("notificacoesTranslates", { static: true })
	notificacoesTranslates: TraducoesXinglingComponent;
	fc = new FormGroup({
		nome: new FormControl(null, Validators.required),
		inicio: new FormControl(null, Validators.required),
		qtdDiasSemana: new FormControl(null, Validators.required),
		totalTreinos: new FormControl(null, [
			Validators.required,
			Validators.min(1),
		]),
		termino: new FormControl(null, Validators.required),
		professorId: new FormControl(null),
		predefinido: new FormControl(false),
		geradoPorIA: new FormControl(false),
	});

	validateSubscription: Subscription;
	numberMask = [/[0-9]/, /[0-9]/, /[0-9]/];
	inicioErrorMsg;
	fimErrorMsg;
	programaEdit: Programa;
	permissaoProgramaTreino;
	permissaoalterarprofessor;
	permissaotornarpredefinido;
	nomePrograma = "";
	apresentarBotaoPredefinir = false;

	constructor(
		private cd: ChangeDetectorRef,
		private programaService: TreinoApiProgramaService,
		private rest: RestService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private modal: NgbActiveModal
	) {}

	ngOnInit() {
		this.eventSetup();
		this.carregarPermissoes();
		if (!this.permissaoProgramaTreino.editar) {
			this.disabledAllCampos();
		}
		if (!this.permissaoalterarprofessor) {
			this.disabledCampoProfessor();
		}
	}

	ngOnDestroy() {
		if (this.validateSubscription) {
			this.validateSubscription.unsubscribe();
		}
	}

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	professorSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "200",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	criarHandler() {
		const controls = this.fc.controls;
		for (const control in controls) {
			if (Object.prototype.hasOwnProperty.call(controls, control)) {
				controls[control].markAsTouched();
			}
		}
		if (this.fc.valid) {
			this.programaService
				.atualizarPrograma(this.programaEdit.id, this.getDto())
				.subscribe((response) => {
					if (response.id) {
						this.snotifyService.success(
							this.notificacoesTranslates.getLabel("saveSuccess")
						);
						this.modal.close(response);
						if (
							response.predefinido !== undefined &&
							response.predefinido === true
						) {
							this.sessionService.notificarRecursoEmpresa(
								RecursoSistema.EDITOU_PROGRAMA_PREDEFINIDO
							);
						}
					} else {
						this.snotifyServiceError(response);
					}
				});
		} else {
			this.snotifyService.error(
				this.notificacoesTranslates.getLabel("camposObrigatorio")
			);
		}
	}

	tornarPredefinidoHandler() {
		this.nomePrograma = this.programaEdit.nome;
		if (this.permissaotornarpredefinido) {
			this.programaService
				.tornarProgramaPreDefinido(this.programaEdit.id)
				.subscribe((response) => {
					if (response !== "erro_programa_treino_predefinido_duplicado") {
						this.snotifyService.success(
							this.notificacoesTranslates.getLabel("predefinirSuccess")
						);
					} else {
						this.snotifyService.error(
							this.notificacoesTranslates.getLabel("registroDuplicado")
						);
					}
				});
		} else {
			this.snotifyService.error(
				this.notificacoesTranslates.getLabel("usuariosempermissao")
			);
		}
	}

	loadForm(programa: Programa) {
		this.programaEdit = programa;
		this.fc.get("nome").setValue(programa.nome);
		this.fc.get("inicio").setValue(programa.inicio);
		this.fc.get("qtdDiasSemana").setValue(programa.qtdDiasSemana);
		this.fc.get("totalTreinos").setValue(programa.totalTreinos);
		this.fc.get("termino").setValue(programa.termino);
		this.fc.get("professorId").setValue(programa.professorMontou);
		this.fc.get("geradoPorIA").setValue(programa.geradoPorIA);
		if (programa.predefinido) {
			this.apresentarBotaoPredefinir = programa.predefinido;
			this.fc.get("inicio").clearValidators();
			this.fc.get("termino").clearValidators();
			this.fc.get("predefinido").setValue(programa.predefinido);
		}
	}

	get getUrlProfessorMontou() {
		const professorId =
			this.programaEdit && this.programaEdit.professorMontou
				? this.programaEdit.professorMontou.id
				: "0";
		return this.rest.buildFullUrl(
			"colaboradores/professores-dados-basicos/" + professorId
		);
	}

	atualizarCampos(programa: Programa) {
		this.fc
			.get("totalTreinos")
			.setValue(programa.totalTreinos, { emitEvent: false });
		this.fc.get("termino").setValue(programa.termino, { emitEvent: false });
		this.validarConflitos();
	}

	get paramBuilder() {
		return (filter) => {
			return {
				filters: JSON.stringify({
					quicksearchValue: filter,
					quicksearchFields: ["nome"],
				}),
			};
		};
	}

	private eventSetup() {
		const params: ParamsCalcularAulasPrevista = {
			qtdDiasSemana: this.fc.get("qtdDiasSemana").value,
			inicio: this.fc.get("inicio").value,
			termino: this.fc.get("termino").value,
			totalTreinos: this.fc.get("totalTreinos").value,
			value: "",
			campoAlterado: "",
		};
		this.fc.get("inicio").valueChanges.subscribe((value) => {
			if (!this.validarDataInicioSuperiorIgualADataTermino()) {
				params.campoAlterado = "inicio";
				params.value = value;
				params.inicio = value;
				this.programaService
					.calcularAulasPrevistas(this.programaEdit.id, params)
					.subscribe((result) => {
						this.atualizarCampos(result);
					});
			}
		});
		this.fc.get("termino").valueChanges.subscribe((value) => {
			if (!this.validarDataInicioSuperiorIgualADataTermino()) {
				params.campoAlterado = "termino";
				params.value = value;
				params.termino = value;
				this.programaService
					.calcularAulasPrevistas(this.programaEdit.id, params)
					.subscribe((result) => {
						this.atualizarCampos(result);
					});
			}
		});
		this.fc.get("qtdDiasSemana").valueChanges.subscribe((value) => {
			params.campoAlterado = "qtdDiasSemana";
			params.value = value;
			params.qtdDiasSemana = value;
			this.programaService
				.calcularAulasPrevistas(this.programaEdit.id, params)
				.subscribe((result) => {
					this.atualizarCampos(result);
				});
		});
		this.fc
			.get("totalTreinos")
			.valueChanges.pipe(debounceTime(500))
			.subscribe((value) => {
				params.campoAlterado = "totalTreinos";
				params.value = value;
				params.totalTreinos = value;
				this.programaService
					.calcularAulasPrevistas(this.programaEdit.id, params)
					.subscribe((result) => {
						this.atualizarCampos(result);
					});
			});
	}

	private validarDataInicioSuperiorIgualADataTermino(): boolean {
		const dataInicio = this.fc.get("inicio").value;
		const dataTermino = this.fc.get("termino").value;
		let result = false;
		if (dataInicio >= dataTermino) {
			this.fc
				.get("inicio")
				.setValidators([Validators.required, Validators.max(0)]);
			this.inicioErrorMsg =
				"A data de início do programa não pode ser maior ou igual que a data final";
			result = true;
		} else {
			this.fc.get("inicio").setValidators(Validators.required);
			this.inicioErrorMsg = null;
		}
		this.fc.get("inicio").updateValueAndValidity({ emitEvent: false });

		return result;
	}

	private dataConflita(inicio?, termino?) {
		if (inicio && termino) {
			const inicioApresentar = moment(inicio).format("DD/MM/YYYY");
			const terminoApresentar = moment(termino).format("DD/MM/YYYY");
			this.fc
				.get("termino")
				.setValidators([Validators.required, Validators.max(0)]);
			this.fimErrorMsg = `Conflito com um programa(${inicioApresentar} a ${terminoApresentar})`;
		} else {
			this.fc.get("termino").setValidators(Validators.required);
			this.fimErrorMsg = null;
		}
	}

	private validarConflitos() {
		const inicio = parseInt(this.fc.get("inicio").value, 10);
		const termino = parseInt(this.fc.get("termino").value, 10);
		if (this.validateSubscription) {
			this.validateSubscription.unsubscribe();
		}

		if (this.inputsAreValid(inicio, termino)) {
			const alunoId = this.programaEdit.alunoId
				? this.programaEdit.alunoId.toString()
				: null;
			const colaboradorId = this.programaEdit.colaboradorId
				? this.programaEdit.colaboradorId.toString()
				: null;

			const verificarConflitoDePrograma = alunoId
				? this.programaService.verificarConflitoDePrograma(
						inicio.toString(),
						termino.toString(),
						alunoId,
						this.programaEdit.id
				  )
				: this.programaService.verificarConflitoDeProgramaColaborador(
						inicio.toString(),
						termino.toString(),
						colaboradorId,
						this.programaEdit.id
				  );

			this.validateSubscription = verificarConflitoDePrograma.subscribe(
				(conflito: Programa) => {
					if (conflito) {
						this.dataConflita(conflito.inicio, conflito.termino);
					} else {
						this.dataConflita();
					}
					this.fc.get("termino").updateValueAndValidity({ emitEvent: false });
					this.fc.get("termino").markAsTouched();
					this.cd.detectChanges();
				}
			);
		} else {
			this.fc.get("termino").reset();
			this.dataConflita();
			this.fc.get("termino").markAsTouched();
			this.fc.get("termino").updateValueAndValidity();
			this.fc.get("inicio").updateValueAndValidity();
			this.cd.detectChanges();
		}
	}

	private inputsAreValid(inicio, termino): boolean {
		const inicioValid = inicio && inicio > 0;
		const terminoValid = termino && termino > 0;
		return inicioValid && terminoValid;
	}

	private disabledAllCampos() {
		setTimeout(() => {
			this.fc.get("nome").disable({ emitEvent: false });
			this.fc.get("inicio").disable({ emitEvent: false });
			this.fc.get("totalTreinos").disable({ emitEvent: false });
			this.fc.get("termino").disable({ emitEvent: false });
			this.fc.get("professorId").disable({ emitEvent: false });
		});
		this.diasSemana.disabled = true;
	}

	private disabledCampoProfessor() {
		setTimeout(() => {
			this.fc.get("professorId").disable({ emitEvent: false });
		});
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
		this.permissaoalterarprofessor = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.ALTERAR_PROFESSOR_TREINO
		);
		this.permissaotornarpredefinido = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.TORNAR_PROGRAMA_PREDEFINIDO
		);
	}

	private getDto() {
		const dto = this.fc.getRawValue();
		dto.alunoId = this.programaEdit.alunoId;
		dto.professorId = this.sessionService.integracaoZW
			? dto.professorId && dto.professorId.codigoColaborador
				? dto.professorId.codigoColaborador
				: null
			: dto.professorId
			? dto.professorId.id
			: null;
		dto.qtdDiasSemana = parseInt(dto.qtdDiasSemana, 10);
		dto.id = this.programaEdit.id;
		dto.geradoPorIA = this.programaEdit.geradoPorIA;
		return dto;
	}

	private snotifyServiceError(chaveErro) {
		switch (chaveErro) {
			case "erro_periodo_invalido":
				this.snotifyService.error(
					this.notificacoesTranslates.getLabel("periodoDuplicado")
				);
				break;
			case "erro_formato_data_inicio_programa_treino":
				this.snotifyService.error(
					this.notificacoesTranslates.getLabel(
						"erro_formato_data_inicio_programa_treino"
					)
				);
				break;
			default:
				this.snotifyService.error(
					this.notificacoesTranslates.getLabel("erro_default")
				);
				break;
		}
	}
}
