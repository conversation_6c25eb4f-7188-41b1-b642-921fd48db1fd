import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import {
	FilterComponent,
	GridFilterConfig,
	GridFilterType,
	TraducoesXinglingComponent,
} from "ui-kit";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	Prescricao,
	Programa,
	ProgramaAtual,
	TreinoApiAlunosService,
	TreinoApiColaboradorService,
	TreinoApiProgramaService,
} from "treino-api";
import { Observable, Subscription, zip } from "rxjs";
import { debounceTime, map } from "rxjs/operators";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-enviar-programa-alunos",
	templateUrl: "./modal-enviar-programa-alunos.component.html",
	styleUrls: ["./modal-enviar-programa-alunos.component.scss"],
})
export class ModalEnviarProgramaAlunosComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("listadiv", { static: true }) listadiv: ElementRef;
	@Output() fechar = new EventEmitter();
	confirmar = false;
	searchControl = new FormControl("");
	@ViewChild("filtro", { static: false }) filtro: FilterComponent;
	filterConfig: GridFilterConfig;
	lista: Array<Prescricao> = [];
	subscription: Subscription;
	@Input() codigoProgramaBase;
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	professores: ApiResponseList<any> = {
		content: [],
	};
	todosMarcados = false;
	matriculasSelecionadas: Array<number> = [];
	situacoes = [];
	situacoesAluno = [];
	professoresSelecionados = [];
	ordenacao = "";
	permissaoVisualizarAlunosOutrasCarteiras;
	permissaoProgramaTreino;
	permissaoAtribuirProgramaTreinoPreDefinido;

	constructor(
		private cd: ChangeDetectorRef,
		private colaboradorService: TreinoApiColaboradorService,
		private sessionService: SessionService,
		private programaService: TreinoApiProgramaService,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.carregarPermissoes();
		this.configFilters();
		this.listar();
		this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
			this.listar();
		});
		setTimeout(() => {
			this.onScroll(null);
		}, 1000);
	}

	private carregarPermissoes() {
		let permissaoCarteira = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.VER_ALUNOS_OUTRAS_CARTEIRAS
		);
		if (permissaoCarteira === undefined) {
			permissaoCarteira = false;
		}
		this.permissaoVisualizarAlunosOutrasCarteiras = permissaoCarteira;
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
		this.permissaoAtribuirProgramaTreinoPreDefinido =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.ATRIBUIR_PROGRAMA_TREINO_PRE_DEFINIDO
			);
	}

	fecharModal() {
		if (this.confirmar) {
			this.confirmar = false;
			this.cd.detectChanges();
		} else {
			this.fechar.emit();
		}
	}

	private getProfessores(): Observable<any> {
		return this.colaboradorService
			.obterTodosColaboradoresAptosAAula(false)
			.pipe(
				map((result) => {
					result.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = result;
				})
			);
	}

	private configFilters() {
		zip(this.getProfessores()).subscribe((resultado) => {
			const filters = [
				{
					name: "situacaoaluno",
					label: "Situação do aluno",
					type: GridFilterType.MANY,
					options: [
						{ value: "AT", label: "Ativo" },
						{ value: "IN", label: "Inativo" },
						{ value: "VI", label: "Visitante" },
					],
					initialValue: ["AT"],
				},
				{
					name: "situacoes",
					type: GridFilterType.MANY,
					label: "Situação de treino",
					options: [
						{ label: "Sem treino", value: "SEM_TREINO" },
						{ label: "Treino a vencer", value: "A_VENCER" },
						{ label: "Treino vencido", value: "VENCIDO" },
						{ label: "Treino em dia", value: "EM_DIA" },
					],
					initialValue: [],
				},
			];
			if (this.permissaoVisualizarAlunosOutrasCarteiras) {
				filters.push({
					initialValue: [],
					name: "professores",
					label: "Professor",
					type: GridFilterType.MANY,
					options: this.professores.content,
				});
			}
			this.filterConfig = {
				filters,
			};
			this.cd.detectChanges();
		});
	}

	onSearch(filtro) {
		this.situacoes = [];
		this.situacoesAluno = [];
		if (filtro && filtro.filters && filtro.filters.professores) {
			this.professoresSelecionados = filtro.filters.professores;
		}
		if (filtro && filtro.filters && filtro.filters.situacoes) {
			filtro.filters.situacoes.forEach((value) => {
				this.situacoes.push({
					id: value,
				});
			});
		}
		if (filtro && filtro.filters && filtro.filters.situacaoaluno) {
			filtro.filters.situacaoaluno.forEach((value) => {
				this.situacoesAluno.push({
					id: value,
				});
			});
		}
		this.listar();
		this.filtro.close();
	}

	listar() {
		this.lista = [];
		this.todosMarcados = false;
		this.listarAlunos(1);
	}

	listarAlunos(page: any) {
		this.data.size = 20;
		this.data.page = page;
		const filters = {
			search: this.searchControl.value,
			primario: null,
			pessoaProfessorLogado: null,
			professores: this.permissaoVisualizarAlunosOutrasCarteiras
				? this.professoresSelecionados
				: [this.sessionService.loggedUser.professorResponse.id],
			ordenacao: this.ordenacao,
			secundario: this.situacoes,
			terciario: this.situacoesAluno,
		};
		this.programaService
			.listaPrescricao(this.data, filters)
			.subscribe((data) => {
				this.lista.push(...data.content);
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.cd.detectChanges();
			});
	}

	getTextTooltip(tipo: "aluno" | "contrato" | "outro", situacaoSigla) {
		let captalizePipe = new CaptalizePipe();
		let valorPipeado = captalizePipe.transform(this.getSituacao(situacaoSigla));
		if (tipo === "aluno") {
			return `Situação do cliente: ${valorPipeado}`;
		}
		if (tipo === "contrato") {
			return `Situação do contrato: ${valorPipeado}`;
		}
		if (tipo === "outro") {
			return `${valorPipeado}`;
		}
	}

	getSituacao(s: string): string {
		switch (s) {
			case "AE":
				return "ATESTADO";
			case "AT":
				return "ATIVO";
			case "AV":
				return "A VENCER";
			case "NO":
				return "NORMAL";
			case "CR":
				return "FÉRIAS";
			case "DE":
				return "DESISTENTE";
			case "CA":
				return "CANCELADO";
			case "VE":
				return "VENCIDO";
			case "DI":
				return "DIÁRIA";
			case "IN":
				return "INATIVO";
			case "VI":
				return "VISITANTE";
			case "TP":
				return "TOTALPASS";
			case "TV":
				return "VENCIDO";
			case "TR":
				return "TRANCADO";
			case "GY":
				return "GYMPASS";
			case "PE":
				return "FREEPASS";
			default:
				return "OUTRO";
		}
	}

	marcarTodos() {
		this.todosMarcados = true;
		this.matriculas(true);
	}

	private matriculas(insert: boolean) {
		const filters = {
			search: this.searchControl.value,
			primario: null,
			pessoaProfessorLogado: null,
			professores: this.permissaoVisualizarAlunosOutrasCarteiras
				? this.professoresSelecionados
				: [this.sessionService.loggedUser.professorResponse.id],
			secundario: this.situacoes,
			terciario: this.situacoesAluno,
		};
		this.programaService
			.matriculasPrescricao(this.data, filters)
			.subscribe((data) => {
				if (data.content && insert) {
					data.content.forEach((value) => {
						if (!this.matriculasSelecionadas.includes(value)) {
							this.matriculasSelecionadas.push(value);
						}
					});
				} else if (data.content && !insert) {
					this.matriculasSelecionadas = this.matriculasSelecionadas.filter(
						(value) => !data.content.includes(value)
					);
				}
				this.cd.detectChanges();
			});
	}

	desmarcarTodos() {
		this.todosMarcados = false;
		this.matriculas(false);
	}

	marcado(matricula): boolean {
		// tslint:disable-next-line:radix
		return this.matriculasSelecionadas.includes(parseInt(matricula));
	}

	toggle(matricula) {
		// tslint:disable-next-line:radix
		matricula = parseInt(matricula);
		const indice = this.matriculasSelecionadas.indexOf(matricula);
		if (indice !== -1) {
			this.matriculasSelecionadas.splice(indice, 1);
			this.todosMarcados = false;
		} else {
			this.matriculasSelecionadas.push(matricula);
		}
		this.cd.detectChanges();
	}

	enviar() {
		if (
			this.permissaoProgramaTreino.incluir ||
			this.permissaoAtribuirProgramaTreinoPreDefinido
		) {
			if (this.matriculasSelecionadas.length < 10) {
				this.enviarConfirmando();
			} else {
				this.confirmar = true;
				this.cd.detectChanges();
			}
		} else {
			this.notify.warning(
				'Você não possui as permissões necessárias para enviar o programa de treino ("Aluno>Permissões>Programa de treino").'
			);
		}
	}

	enviarConfirmando() {
		this.programaService
			.enviarPrograma(this.codigoProgramaBase, this.matriculasSelecionadas)
			.subscribe((data) => {
				this.notify.success(
					this.confirmar
						? "Como há muitos alunos selecionados, pode levar alguns minutos para completar o processo. Mas você pode realizar outras ações normalmente  dentro do sistema enquanto isso."
						: "Programa enviado com sucesso!",
					{ pauseOnHover: true, timeout: 8000 }
				);
				this.fechar.emit();
			});
	}

	cancelarAviso() {
		this.confirmar = false;
		this.cd.detectChanges();
	}

	desmarcarSelecionados() {
		this.todosMarcados = false;
		this.matriculasSelecionadas = [];
		this.cd.detectChanges();
	}

	onScroll(event: Event) {
		if (this.listadiv) {
			const element = this.listadiv.nativeElement as HTMLElement;
			if (
				element.scrollHeight - element.scrollTop - 2 <=
				element.clientHeight
			) {
				this.listarAlunos(this.data.page + 1);
			}
		}
	}

	ordenarLista(coluna: string) {
		this.ordenacao = coluna;
		this.listar();
	}
}
