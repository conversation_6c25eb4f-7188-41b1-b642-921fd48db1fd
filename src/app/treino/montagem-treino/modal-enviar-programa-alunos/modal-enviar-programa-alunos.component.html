<div class="background-modal-atividades">
	<div [ngClass]="{ confirmando: confirmar }" class="content">
		<div class="header">
			<div class="row">
				<div class="col-md-9">
					<div class="row">
						<div class="col-md-5">
							<div *ngIf="confirmar" class="titulo-msg">Atenção</div>
							<div *ngIf="!confirmar" class="titulo-msg">
								Enviar treinos para outros alunos
							</div>
						</div>
					</div>
				</div>

				<div class="close-wrapper">
					<i (click)="fecharModal()" class="pct pct-x"></i>
				</div>
			</div>
		</div>

		<div class="pos-header pretty-scroll">
			<div *ngIf="!confirmar" class="row">
				<div class="col-md-6 busca-esquerda">
					<pacto-cat-form-input
						[control]="searchControl"
						[enableClearInput]="false"
						[iconPosition]="'before'"
						[icon]="'pct pct-search'"
						[label]="null"
						[placeholder]="'Busque por aluno'"></pacto-cat-form-input>
				</div>
				<div class="col-md-6 busca-direita">
					<pacto-filter
						#filtro
						(filterChange)="onSearch($event)"
						*ngIf="filterConfig"
						[filterConfig]="filterConfig"></pacto-filter>
				</div>
			</div>

			<div *ngIf="confirmar" class="alert-sirene">
				<img
					alt=""
					class="image-empty"
					src="assets/images/empty-state-sirene.svg" />
			</div>

			<div *ngIf="!confirmar" class="warning">
				<i class="pct pct-alert-circle"></i>
				<span>
					Para clientes que ainda não possuem treino, este programa de treino
					será adicionado ao aluno com início imediato. Já para alunos que
					possuem programas de treino vigentes, ele será adicionado como
					renovação e o início será um dia após o término do último programa.
				</span>
			</div>

			<div *ngIf="confirmar" class="warning">
				<i class="pct pct-alert-triangle"></i>
				<span>
					Você escolheu enviar o programa de treino para
					{{ matriculasSelecionadas.length }} alunos.
					<b>Esta ação é irreversível.</b>
					Por favor, tenha certeza, pois ao concluir esta ação, não poderá ser
					desfeita. Você tem certeza de que deseja continuar?
				</span>
			</div>

			<div #listadiv (scroll)="onScroll($event)" class="lista">
				<table class="scrollable-table">
					<thead>
						<tr>
							<th class="tdnome">
								<div class="containerth">
									<i
										(click)="desmarcarTodos()"
										*ngIf="todosMarcados"
										class="pct pct-check-square"></i>
									<i
										(click)="marcarTodos()"
										*ngIf="!todosMarcados"
										class="pct pct-square"></i>
									<span>Alunos</span>
									<i
										(click)="ordenarLista('aluno,ASC')"
										*ngIf="!ordenacao.startsWith('aluno')"
										class="orderby pct pct-drop-down"></i>
									<i
										(click)="ordenarLista('aluno,DESC')"
										*ngIf="ordenacao === 'aluno,ASC'"
										class="orderby pct pct-caret-up"></i>
									<i
										(click)="ordenarLista('aluno,ASC')"
										*ngIf="ordenacao === 'aluno,DESC'"
										class="orderby pct pct-caret-down"></i>
								</div>
							</th>
							<th class="tdstatus">
								<div class="containerth">
									<span>Status do aluno</span>
									<i
										(click)="ordenarLista('status,ASC')"
										*ngIf="!ordenacao.startsWith('status')"
										class="orderby pct pct-drop-down"></i>
									<i
										(click)="ordenarLista('status,DESC')"
										*ngIf="ordenacao === 'status,ASC'"
										class="orderby pct pct-caret-up"></i>
									<i
										(click)="ordenarLista('status,ASC')"
										*ngIf="ordenacao === 'status,DESC'"
										class="orderby pct pct-caret-down"></i>
								</div>
							</th>
							<th class="tdprof">
								<div class="containerth">
									<span>Professor TreinoWeb</span>
									<i
										(click)="ordenarLista('professor,ASC')"
										*ngIf="!ordenacao.startsWith('professor')"
										class="orderby pct pct-drop-down"></i>
									<i
										(click)="ordenarLista('professor,DESC')"
										*ngIf="ordenacao === 'professor,ASC'"
										class="orderby pct pct-caret-up"></i>
									<i
										(click)="ordenarLista('professor,ASC')"
										*ngIf="ordenacao === 'professor,DESC'"
										class="orderby pct pct-caret-down"></i>
								</div>
							</th>
							<th class="tdsituacao">
								<div class="containerth">
									<span>Situação dos programas</span>
									<i
										(click)="ordenarLista('situacao,ASC')"
										*ngIf="!ordenacao.startsWith('situacao')"
										class="orderby pct pct-drop-down"></i>
									<i
										(click)="ordenarLista('situacao,DESC')"
										*ngIf="ordenacao === 'situacao,ASC'"
										class="orderby pct pct-caret-up"></i>
									<i
										(click)="ordenarLista('situacao,ASC')"
										*ngIf="ordenacao === 'situacao,DESC'"
										class="orderby pct pct-caret-down"></i>
								</div>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr
							(click)="toggle(cliente?.matricula)"
							*ngFor="
								let cliente of lista;
								let even = even;
								let lastRow = last;
								let rowIndex = index
							"
							[ngClass]="{ 'zebra-row': even }">
							<td class="tdnome">
								<div>
									<i
										*ngIf="marcado(cliente?.matricula)"
										class="pct pct-check-square"></i>
									<i
										*ngIf="!marcado(cliente?.matricula)"
										class="pct pct-square"></i>
									<pacto-cat-person-avatar
										[diameter]="24"
										[uri]="cliente?.urlFoto"></pacto-cat-person-avatar>
									<span>{{ cliente?.nome }}</span>
								</div>
							</td>
							<td class="tdstatus">
								<div class="pills">
									<span
										*ngIf="cliente?.situacao"
										[class]="
											'situacao-aluno abreviado primario ' + cliente?.situacao
												| lowercase
										"
										[pactoCatTolltip]="
											getTextTooltip('aluno', cliente?.situacao)
										"
										class="status-pill">
										{{ cliente?.situacao | uppercase }}
									</span>
									<span
										*ngIf="
											cliente?.situacaoContrato &&
											cliente?.situacaoContrato !== 'VI' &&
											cliente?.situacaoContrato !== 'TR'
										"
										[class]="
											'situacao-contrato abreviado secundario ' +
												cliente?.situacaoContrato | lowercase
										"
										[pactoCatTolltip]="
											getTextTooltip('contrato', cliente?.situacaoContrato)
										"
										class="status-pill">
										{{ cliente?.situacaoContrato | uppercase }}
									</span>
									<span
										*ngIf="cliente?.gympass || cliente?.totalpass"
										[class]="
											'situacao-pass abreviado terciario ' +
											(cliente?.gympass ? 'gympass' : 'totalPass')
										"
										[pactoCatTolltip]="
											getTextTooltip('outro', cliente?.gympass ? 'GY' : 'TP')
										">
										{{ cliente?.gympass ? "GY" : "TP" }}
									</span>
									<span *ngIf="!cliente?.situacao">-</span>
								</div>
							</td>
							<td class="tdprof">
								<div>
									{{ cliente.professor }}
								</div>
							</td>
							<td class="tdsituacao">
								<div>
									<div class="situacaoTreino {{ cliente.situacaoTreino }}">
										{{ notificacoesTranslate.getLabel(cliente.situacaoTreino) }}
									</div>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<div *ngIf="confirmar" class="rodape">
				<div></div>

				<div class="right">
					<pacto-cat-button
						(click)="enviarConfirmando()"
						id="btn-enviar-programas"
						label="Enviar programa de treino"
						size="LARGE"
						type="OUTLINE_DANGER"></pacto-cat-button>

					<pacto-cat-button
						(click)="cancelarAviso()"
						i18n-label="@@label-confirmar-btn"
						id="btn-cancelar-aviso"
						label="Cancelar"
						size="LARGE"
						type="PRIMARY"></pacto-cat-button>
				</div>
			</div>

			<div *ngIf="!confirmar" class="rodape">
				<div class="alunos-selecionados">
					<span>Alunos selecionados</span>
					<span class="total">{{ matriculasSelecionadas.length }}</span>
				</div>
				<div class="right">
					<pacto-cat-button
						(click)="fecharModal()"
						i18n-label="@@label-cancelar"
						id="btn-cancelar"
						label="Cancelar"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>

					<pacto-cat-button
						(click)="desmarcarSelecionados()"
						[disabled]="
							!matriculasSelecionadas || matriculasSelecionadas.length === 0
						"
						i18n-label="@@label-cancelar"
						id="btn-limpar"
						label="Desmarcar seleções"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>

					<pacto-cat-button
						(click)="enviar()"
						[disabled]="
							!matriculasSelecionadas || matriculasSelecionadas.length === 0
						"
						i18n-label="@@label-confirmar-btn"
						id="btn-enviar"
						label="Enviar programa de treino"
						size="LARGE"
						type="PRIMARY"></pacto-cat-button>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="label_carteira_academia">Minha carteira na academia</span>
	<span xingling="label_carteira">Minha carteira</span>
	<span xingling="label_todos_alunos">Todos os alunos</span>
	<span xingling="label_na_academia">Alunos na academia</span>
	<span xingling="label_colaboradores">Colaboradores</span>
	<span xingling="tipocol_PR">Professor</span>
	<span xingling="tipocol_TW">Professor Treino</span>
	<span xingling="tipocol_PT">Personal Trainer</span>
	<span xingling="tipocol_OR">Orientador</span>
	<span xingling="tipocol_CO">Consultor</span>
	<span xingling="tipocol_PI">Personal Interno</span>
	<span xingling="tipocol_PE">Personal Externo</span>
	<span xingling="tipocol_TE">Terceirizado</span>
	<span xingling="tipocol_ES">Estúdio</span>
	<span xingling="tipocol_FO">Fornecedor</span>
	<span xingling="tipocol_CR">Coordenador</span>
	<span xingling="tipocol_MD">Médico</span>
	<span xingling="tipocol_FC">Funcionário</span>
	<span xingling="tipocol_AD">Administrador</span>
	<span xingling="PRESCRICAO">Prescrição de treino</span>
	<span xingling="SEM_TREINO">Sem treino</span>
	<span xingling="ATIVO">Treino em dia</span>
	<span xingling="TREINO_FUTURO">Treino futuro</span>
	<span xingling="VENCIDO">Treino vencido</span>
	<span xingling="A_VENCER">Treino a vencer</span>
</pacto-traducoes-xingling>
