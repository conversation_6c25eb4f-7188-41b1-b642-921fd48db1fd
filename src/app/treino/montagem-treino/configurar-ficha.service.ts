import { EventEmitter, Injectable, Output } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";

import { map } from "rxjs/operators";
import { BaseCoreModule } from "@base-core/base-core.module";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import {
	buildFichaForm,
	filloutAtividadesFichaForm,
	filloutFichaForm,
} from "./configurar-ficha/ficha-form-group";
import {
	AlunoBase,
	AtividadeBase,
	AtividadeMetodoExecucao,
	AtividadeFicha,
	FichaPrograma,
	FichaTipoExecucao,
	TreinoApiFichaService,
	Programa,
	TreinoApiProgramaService,
} from "treino-api";
import { WindowUtilService } from "@base-core/utils/window-util.service";

export interface AtividadeView {
	id: number;
	frontId: number;
	nome: string;
	nomeAtivFicha: string;
	complementoNomeAtividade: string;
	setAtividade1?: any;
	setAtividade2?: any;
	imageUri: string;
	gruposMusculares?: any;
	tipo: string;
	metodoExecucao: string;
	serieApenasDuracao: boolean;
	seriesEditadas: any;
	esforco: number;
	sequencia: number;
	formGroup: FormGroup;
}

@Injectable({
	providedIn: BaseCoreModule,
})
export class ConfigurarFichaService {
	programa$: BehaviorSubject<Programa> = new BehaviorSubject(null);
	fichaSelecionadaIndex$: BehaviorSubject<number> = new BehaviorSubject(0);
	atividadeFichaSelecionadaIndex$: BehaviorSubject<number> =
		new BehaviorSubject(0);
	atividadesSelecionadas$: BehaviorSubject<Array<any>> = new BehaviorSubject(
		[]
	);
	atividadesFichaView$: BehaviorSubject<Array<AtividadeView>> =
		new BehaviorSubject([]);
	fichaSendoModificada$: BehaviorSubject<boolean> = new BehaviorSubject(false);
	selecaoFichaIniciada = false;
	fichaFormGroup: FormGroup;
	@Output() removeAtividade = new EventEmitter<number>();

	/**
	 * Usado para notificar o aluno
	 */
	aluno: AlunoBase;

	private ordemAtividades: Array<{ frontId: number; ordem: number }>;

	constructor(
		private fichaService: TreinoApiFichaService,
		private utils: WindowUtilService,
		private programaService: TreinoApiProgramaService
	) {}

	selecionarFichaInicial(fichaIndex) {
		if (!this.selecaoFichaIniciada) {
			this.obterCopiaPrograma();
			this.selecionarFicha(fichaIndex);
			this.selecaoFichaIniciada = true;
		}
	}

	removeAtividadeLista(frontId) {
		this.removeAtividade.emit(frontId);
	}

	removerFicha(): Observable<boolean> {
		// Atualizar view
		const programaCopia = this.obterCopiaPrograma();
		const fichaId = this.obterFichaAtual().id;
		const fichaIndex = this.fichaSelecionadaIndex$.value;
		programaCopia.fichas.splice(fichaIndex, 1);
		this.programa$.next(programaCopia);

		// Escolher outra ficha
		this.selecionarFicha(
			programaCopia.fichas[fichaIndex] ? fichaIndex : fichaIndex - 1
		);

		if (fichaId) {
			return this.fichaService.removerFicha(fichaId).pipe(
				map(() => {
					return true;
				})
			);
		} else {
			return new Observable((observer) => {
				observer.next(true);
				observer.complete();
			});
		}
	}

	adicionarFicha(fichaPredefinidaId): Observable<boolean> {
		const programa = this.programa$.value;

		if (fichaPredefinidaId) {
			return this.fichaService
				.criarFichaPreDefinida(fichaPredefinidaId, programa.id)
				.pipe(
					map((resultado) => {
						// Atualizar memória
						const programaCopia = this.obterCopiaPrograma();
						programaCopia.fichas.push(resultado);
						this.programa$.next(programaCopia);
						const fichaIndex = programaCopia.fichas.findIndex((ficha) => {
							return ficha.id === resultado.id;
						});
						this.selecionarFicha(fichaIndex);
						return true;
					})
				);
		} else {
			return this.fichaService.criarFicha(programa.id).pipe(
				map((resultado) => {
					// Atualizar memória
					const programaCopia = this.obterCopiaPrograma();
					programaCopia.fichas.push(resultado);
					this.programa$.next(programaCopia);
					const fichaIndex = programaCopia.fichas.findIndex((ficha) => {
						return ficha.id === resultado.id;
					});
					this.selecionarFicha(fichaIndex);
					return true;
				})
			);
		}
	}

	editarFicha(fichaId, ficha): Observable<boolean> {
		return this.fichaService.atualizarFicha(fichaId, ficha).pipe(
			map((result) => {
				result.dias_semana = ficha.dias_semana;

				const programaCopia: Programa = this.obterCopiaPrograma();
				const fichaIndex = programaCopia.fichas.findIndex((fichaI) => {
					return fichaI.id === fichaId;
				});
				programaCopia.fichas[fichaIndex] = result;
				this.programa$.next(programaCopia);

				return true;
			})
		);
	}

	tornarFichaPreDefinida(): Observable<any> {
		const ficha = this.obterFichaAtual();
		return this.fichaService.tornarFichaPreDefinida(ficha.id).pipe(
			map((result) => {
				return result;
			})
		);
	}

	obterFichaAtual(): FichaPrograma {
		const programa: Programa = this.obterCopiaPrograma();
		const fichaIndex = this.fichaSelecionadaIndex$.value;
		if (programa && programa.fichas && programa.fichas[fichaIndex]) {
			return programa.fichas[fichaIndex];
		} else {
			return null;
		}
	}

	obterAtividadeMap(atividadeId: number): any {
		let atividadeRet = null;
		this.atividadesSelecionadas$.value.forEach((atividade) => {
			if (atividade.id === atividadeId) {
				atividadeRet = atividade;
			}
		});

		return atividadeRet;
	}

	obterAtividadeMapPorFrontId(frontId): any {
		let atividadeMap = null;
		if (frontId) {
			const atividadesFichaForm = (
				this.fichaFormGroup.get("atividades") as FormArray
			).controls;
			atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
				if (
					parseInt(frontId, 10) ===
					parseInt(atividadeFichaForm.get("frontId").value, 10)
				) {
					atividadeMap = this.obterAtividadeMap(
						atividadeFichaForm.get("atividadeId").value
					);
				}
			});
		}

		return atividadeMap;
	}

	adicionarAtividadeFicha(
		atividadeFichaForm: FormGroup,
		atividade: AtividadeBase
	) {
		if (atividadeFichaForm.get("sequencia").value == null) {
			atividadeFichaForm.get("sequencia").setValue(this.proximaSequencia());
		}
		atividadeFichaForm.get("frontId").setValue(this.proximoFrontId());
		(this.fichaFormGroup.get("atividades") as FormArray).push(
			atividadeFichaForm
		);
		this.adicionarAtividadeMap(atividade, null);
		this.ordemAtividades.push({
			frontId: atividadeFichaForm.get("frontId").value,
			ordem: atividadeFichaForm.get("sequencia").value,
		});
		this.adicionarAtividaFichaView(atividadeFichaForm);
		this.fichaSendoModificada$.next(true);
	}

	private proximaSequencia(): number {
		let result = 0;
		if (this.ordemAtividades) {
			this.ordemAtividades.forEach((ordemAtividade) => {
				if (result <= ordemAtividade.ordem) {
					result = ordemAtividade.ordem;
				}
			});
			return result + 1;
		}
		return result;
	}

	private proximoFrontId(): number {
		let result = 0;
		if (this.ordemAtividades) {
			this.ordemAtividades.forEach((ordemAtividade) => {
				if (result <= ordemAtividade.frontId) {
					result = ordemAtividade.frontId;
				}
			});
			return result + 1;
		}
		return result;
	}

	private adicionarAtividadeMap(
		atividade: AtividadeBase,
		nomeAtivFicha: string
	) {
		let atividadeDuplicada = false;
		this.atividadesSelecionadas$.value.forEach((atividadeSalva) => {
			if (atividadeSalva.id === atividade.id) {
				atividadeDuplicada = true;
			}
		});
		if (!atividadeDuplicada) {
			this.atividadesSelecionadas$.value.push({
				id: atividade.id,
				nome: atividade.nome,
				nomeAtivFicha: nomeAtivFicha ? nomeAtivFicha : atividade.nome,
				imageUri:
					atividade.images && atividade.images.length
						? atividade.images[0].uri
						: "",
				tipo: atividade.tipo,
				serieApenasDuracao: atividade.serieApenasDuracao,
				gruposMusculares: atividade.gruposMusculares
					? atividade.gruposMusculares
					: [],
			});
		}
	}

	private adicionarAtividaFichaView(atividadeFichaForm: FormGroup) {
		const atividadesfichaViewCopia = this.atividadesFichaView$.value;
		atividadesfichaViewCopia.push(
			this.filloutAtividadeView(atividadeFichaForm)
		);

		this.atividadesFichaView$.next(atividadesfichaViewCopia);
	}

	atualizarDetalhesFicha(detalhesFichaForm: FormGroup): Observable<boolean> {
		this.fichaFormGroup.patchValue(detalhesFichaForm.getRawValue());
		this.fichaSendoModificada$.next(true);
		return new Observable((observer) => {
			observer.next(true);
			observer.complete();
		});
	}

	atualizarAtividadeFicha(
		frontId,
		atividadeFichaForm: FormGroup,
		emitEvent: boolean
	) {
		const atividadesForm = (this.fichaFormGroup.get("atividades") as FormArray)
			.controls;
		atividadesForm.forEach((atividadeForm: FormGroup) => {
			if (frontId === atividadeForm.get("frontId").value) {
				this.limparSeriesAtividade(frontId);
				atividadeForm.patchValue(atividadeFichaForm.getRawValue(), {
					onlySelf: emitEvent,
					emitEvent,
				});
				this.addSeriesAtividadeForm(frontId, atividadeFichaForm);
			}
		});
		if (emitEvent) {
			this.atualizarAtividadeFichaView(frontId, atividadeFichaForm);
		}
		this.fichaSendoModificada$.next(true);
	}

	private atualizarAtividadeFichaView(frontId, atividadeFichaForm: FormGroup) {
		const copiaListaAtividadesFichaView = this.atividadesFichaView$.value;
		const atividadeFichaIndex = this.atividadesFichaView$.value.findIndex(
			(atividadeFicha) => {
				return atividadeFicha.frontId === frontId;
			}
		);
		copiaListaAtividadesFichaView.splice(atividadeFichaIndex, 1);
		copiaListaAtividadesFichaView.push(
			this.filloutAtividadeView(atividadeFichaForm)
		);
		this.atividadesFichaView$.next(copiaListaAtividadesFichaView);
	}

	private addSeriesAtividadeForm(frontId, atividadeFichaFormUp: FormGroup) {
		const atividadesFichaForm = (
			this.fichaFormGroup.get("atividades") as FormArray
		).controls;
		atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
			if (atividadeFichaForm.get("frontId").value === frontId) {
				const seriesFormUpdate = (
					atividadeFichaFormUp.get("series") as FormArray
				).controls;
				const seriesForm = atividadeFichaForm.get("series") as FormArray;
				seriesFormUpdate.forEach((serieUpdate: FormGroup) => {
					seriesForm.push(serieUpdate);
				});
			}
		});
	}

	private limparSeriesAtividade(frontId) {
		const atividadesFichaForm = (
			this.fichaFormGroup.get("atividades") as FormArray
		).controls;
		atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
			if (atividadeFichaForm.get("frontId").value === frontId) {
				const seriesForm = atividadeFichaForm.get("series") as FormArray;
				while (seriesForm.controls.length > 0) {
					seriesForm.removeAt(0);
				}
			}
		});
	}

	removerAtividadeFicha(frontId) {
		const atividadesFichaForm = this.fichaFormGroup.get(
			"atividades"
		) as FormArray;
		atividadesFichaForm.controls.forEach((atividadeFichaForm, index) => {
			if (atividadeFichaForm.get("frontId").value === frontId) {
				atividadesFichaForm.removeAt(index);
				const indexAtividadeOrdem = this.ordemAtividades.findIndex((ordem) => {
					return ordem.frontId === frontId;
				});
				this.ordemAtividades.splice(indexAtividadeOrdem, 1);
				this.removerAtividadeFichaView(frontId);
				this.fichaSendoModificada$.next(true);
			}
		});
	}

	private removerAtividadeFichaView(frontId) {
		const copiaAtividadeFichaView = this.atividadesFichaView$.value;
		const atividadeFichaViewRemovida: AtividadeView =
			copiaAtividadeFichaView.find((atividadeFichaView) => {
				return atividadeFichaView.frontId === frontId;
			});
		const index = copiaAtividadeFichaView.findIndex((atividadeFicha) => {
			return atividadeFicha.frontId === frontId;
		});
		copiaAtividadeFichaView.splice(index, 1);
		this.atividadesFichaView$.next(copiaAtividadeFichaView);
		if (atividadeFichaViewRemovida.sequencia) {
			const novaOrdem = [];
			this.ordemAtividades.forEach((ordemAtividade) => {
				if (ordemAtividade.ordem > atividadeFichaViewRemovida.sequencia) {
					ordemAtividade.ordem = ordemAtividade.ordem - 1;
				}
				novaOrdem.push(ordemAtividade);
			});
			this.atualizarOrdemAtividades(novaOrdem);
		}
	}

	private obterCopiaPrograma(): Programa {
		if (this.programa$.value) {
			/**
			 * Ordena as fichas pelo id
			 */
			if (
				this.programa$.value.fichas &&
				this.programa$.value.fichas.length > 1
			) {
				this.programa$.value.fichas.sort((a, b) => (a.id < b.id ? -1 : 1));
			}
			const copy = JSON.parse(JSON.stringify(this.programa$.value));
			return new Programa(copy);
		} else {
			return null;
		}
	}

	/**
	 * Seleciona uma nova ficha. Caso não exista
	 * define o index da ficha selecionada como NULL.
	 */
	selecionarFicha(index = 0) {
		const programa = this.programa$.value;
		if (programa.fichas && programa.fichas[index]) {
			this.fichaSelecionadaIndex$.next(index);
			this.atividadesSelecionadas$.next([]);
			programa.fichas[index].atividades.forEach(
				(atividadeFicha: AtividadeFicha) => {
					this.adicionarAtividadeMap(
						atividadeFicha.atividade,
						atividadeFicha.nomeNaFicha
					);
				}
			);
			if (this.fichaFormGroup) {
				this.limparAtividadesFormArray();
				this.fichaFormGroup.patchValue(
					filloutFichaForm(programa.fichas[index]).getRawValue()
				);
				this.addAtividadesFormArray(programa.fichas[index].atividades);
			} else {
				this.fichaFormGroup = filloutFichaForm(programa.fichas[index]);
			}
			this.atualizarSetAtividades();
			this.popularListaOrdemAtividades();
			this.inicializarListaAtividadesFicha();
		} else {
			if (this.fichaFormGroup) {
				this.limparAtividadesFormArray();
			} else {
				this.fichaFormGroup = buildFichaForm();
			}
			this.atividadesFichaView$.next([]);
			this.fichaSelecionadaIndex$.next(null);
			this.atividadeFichaSelecionadaIndex$.next(null);
		}
		this.fichaSendoModificada$.next(false);
	}

	private popularListaOrdemAtividades() {
		this.ordemAtividades = [];
		const atividadesFichaForm = (
			this.fichaFormGroup.get("atividades") as FormArray
		).controls;
		atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
			this.ordemAtividades.push({
				frontId: atividadeFichaForm.get("frontId").value,
				ordem: atividadeFichaForm.get("sequencia").value,
			});
		});
	}

	private atualizarSetAtividades() {
		const atividades = (this.fichaFormGroup.get("atividades") as FormArray)
			.controls;
		atividades.forEach((atividadeFichaForm: FormGroup) => {
			if (
				atividadeFichaForm.get("metodoExecucao").value ===
					AtividadeMetodoExecucao.BI_SET ||
				atividadeFichaForm.get("metodoExecucao").value ===
					AtividadeMetodoExecucao.TRI_SET
			) {
				atividadeFichaForm
					.get("atividadeSet1")
					.setValue(
						this.obterFrontIdPorAtividadeId(
							atividadeFichaForm.get("atividadeSet1").value
						)
					);
				atividadeFichaForm
					.get("atividadeSet2")
					.setValue(
						this.obterFrontIdPorAtividadeId(
							atividadeFichaForm.get("atividadeSet2").value
						)
					);
			}
		});
	}

	private inicializarListaAtividadesFicha() {
		const atividadesFichaView: Array<AtividadeView> = [];
		const atividadesFichaForm = (
			this.fichaFormGroup.get("atividades") as FormArray
		).controls;
		atividadesFichaForm.forEach((atividadeFichaForm: FormGroup) => {
			atividadesFichaView.push(this.filloutAtividadeView(atividadeFichaForm));
		});
		this.atualizarSetAtividade(atividadesFichaView);
		this.atividadesFichaView$.next(atividadesFichaView);
	}

	atualizarSetAtividade(atividades) {
		atividades.forEach((atividade) => {
			atividade.formGroup
				.get("setAtividade1")
				.setValue(
					atividade.setAtividade1 !== null
						? this.obterFrontIdPorAtividadeId_v2(atividade.setAtividade1.id)
						: null
				);
			atividade.formGroup
				.get("setAtividade2")
				.setValue(
					atividade.setAtividade2 !== null
						? this.obterFrontIdPorAtividadeId_v2(atividade.setAtividade2.id)
						: null
				);
		});
	}

	private serieEditada(atividadeFichaForm: FormGroup): any {
		const seriesDiferentes = {
			seriesDiferentes: false,
			valores: {
				distancia: "",
				duracao: "",
				velocidade: "",
				repeticao: "",
				carga: "",
			},
			distancia: false,
			duracao: false,
			velocidade: false,
			repeticao: false,
			descanso: false,
			cadencia: false,
			carga: false,
		};
		const controls = (atividadeFichaForm.get("series") as FormArray).controls;
		if (controls.length > 0) {
			const repeticoes = controls[0].get("repeticoes").value;
			const carga = controls[0].get("carga").value;
			const distancia = controls[0].get("distancia").value;
			const velocidade = controls[0].get("velocidade").value;
			const duracao = controls[0].get("duracao").value;
			const descanso = controls[0].get("descanso").value;
			const cadencia = controls[0].get("cadencia").value;
			controls.forEach((fg, idx) => {
				seriesDiferentes.valores.repeticao +=
					(idx > 0 ? " / " : "") + fg.get("repeticoes").value;
				if (repeticoes !== fg.get("repeticoes").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.repeticao = true;
				}
				seriesDiferentes.valores.carga +=
					(idx > 0 ? " / " : "") + fg.get("carga").value;
				if (carga !== fg.get("carga").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.carga = true;
				}
				seriesDiferentes.valores.duracao +=
					(idx > 0 ? " / " : "") + fg.get("duracao").value;
				if (duracao !== fg.get("duracao").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.duracao = true;
				}
				seriesDiferentes.valores.distancia +=
					(idx > 0 ? " / " : "") + fg.get("distancia").value;
				if (distancia !== fg.get("distancia").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.distancia = true;
				}
				seriesDiferentes.valores.velocidade +=
					(idx > 0 ? " / " : "") + fg.get("velocidade").value;
				if (velocidade !== fg.get("velocidade").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.velocidade = true;
				}
				if (cadencia !== fg.get("cadencia").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.cadencia = true;
				}
				if (descanso !== fg.get("descanso").value) {
					seriesDiferentes.seriesDiferentes = true;
					seriesDiferentes.descanso = true;
				}
			});
		}
		return seriesDiferentes;
	}

	private filloutAtividadeView(atividadeFichaForm: FormGroup): AtividadeView {
		const atividadeFicha = this.obterAtividadeMap(
			atividadeFichaForm.get("atividadeId").value
		);
		const seriesDiferentes = this.serieEditada(atividadeFichaForm);
		return {
			id: atividadeFichaForm.get("atividadeId").value,
			frontId: atividadeFichaForm.get("frontId").value,
			nome: atividadeFicha.nome,
			nomeAtivFicha: atividadeFicha.nomeAtivFicha,
			complementoNomeAtividade: atividadeFichaForm.get(
				"complementoNomeAtividade"
			).value,
			imageUri: atividadeFicha.imageUri,
			gruposMusculares: atividadeFicha.gruposMusculares,
			tipo: atividadeFicha.tipo,
			serieApenasDuracao: atividadeFicha.serieApenasDuracao,
			metodoExecucao: atividadeFichaForm.get("metodoExecucao").value,
			sequencia: atividadeFichaForm.get("sequencia").value,
			esforco: atividadeFichaForm.get("esforco").value,
			seriesEditadas: seriesDiferentes,
			formGroup: this.buildSeriesFormListaAtividadesFicha(
				atividadeFichaForm,
				seriesDiferentes
			),
			setAtividade1: this.obterAtividadeMapPorFrontId(
				atividadeFichaForm.get("atividadeSet1").value
			),
			setAtividade2: this.obterAtividadeMapPorFrontId(
				atividadeFichaForm.get("atividadeSet2").value
			),
		};
	}

	private buildSeriesFormListaAtividadesFicha(
		atividadeFichaForm: FormGroup,
		serieEditadas: any
	): FormGroup {
		const resultForm: FormGroup = new FormGroup({
			series: new FormControl(),
			repeticoes: new FormControl(),
			carga: new FormControl(),
			velocidade: new FormControl(),
			duracao: new FormControl(),
			distancia: new FormControl(),
			descanso: new FormControl(),
			cadencia: new FormControl(),
			metodoExecucao: new FormControl(),
			esforco: new FormControl(),
			complementoNomeAtividade: new FormControl(),
			complemento: new FormControl(),
			setAtividade1: new FormControl(),
			setAtividade2: new FormControl(),
		});
		const quantSeries = (atividadeFichaForm.get("series") as FormArray).controls
			.length;
		resultForm.get("series").setValue(quantSeries);
		resultForm
			.get("metodoExecucao")
			.setValue(atividadeFichaForm.get("metodoExecucao").value);
		resultForm.get("esforco").setValue(atividadeFichaForm.get("esforco").value);
		resultForm
			.get("complementoNomeAtividade")
			.setValue(atividadeFichaForm.get("complementoNomeAtividade").value);
		(atividadeFichaForm.get("series") as FormArray).controls.forEach(
			(serieForm: FormGroup, index) => {
				if (index === 0) {
					resultForm.patchValue(serieForm.getRawValue());
					resultForm
						.get("duracao")
						.setValue(
							this.utils.convertSecondsIntoMinutesSecondsLabel(
								resultForm.get("duracao").value
							)
						);
					resultForm
						.get("descanso")
						.setValue(
							this.utils.convertSecondsIntoMinutesSecondsLabel(
								resultForm.get("descanso").value
							)
						);
					if (serieEditadas.repeticao) {
						resultForm
							.get("repeticoes")
							.setValue(serieEditadas.valores.repeticao);
					}
					if (serieEditadas.carga) {
						resultForm.get("carga").setValue(serieEditadas.valores.carga);
					}
				}
			}
		);

		return resultForm;
	}

	private obterFrontIdPorAtividadeId(atividadeId: number) {
		const atividades = (this.fichaFormGroup.get("atividades") as FormArray)
			.controls;
		const resultado = atividades.find((atividadeFichaForm) => {
			return atividadeFichaForm.get("id").value === atividadeId;
		});
		let retorno = null;
		if (resultado) {
			retorno = resultado.get("frontId").value;
		}
		return retorno;
	}

	obterFrontIdPorAtividadeId_v2(atividadeId: number) {
		const atividades = (this.fichaFormGroup.get("atividades") as FormArray)
			.controls;
		const resultado = atividades.find((atividadeFichaForm) => {
			if (atividadeFichaForm.get("atividadeId").value === atividadeId) {
				return atividadeFichaForm;
			}
		});
		let retorno = null;
		if (resultado) {
			retorno = resultado.get("frontId").value;
		}
		return retorno;
	}

	private addAtividadesFormArray(atividadeFichas: Array<AtividadeFicha>) {
		const formaArray = this.fichaFormGroup.get("atividades") as FormArray;
		filloutAtividadesFichaForm(atividadeFichas).forEach(
			(atividadeFichaForm: FormGroup) => {
				formaArray.push(atividadeFichaForm);
			}
		);
	}

	private limparAtividadesFormArray() {
		const formArrayAtv = this.fichaFormGroup.get("atividades") as FormArray;
		while (formArrayAtv.length !== 0) {
			formArrayAtv.removeAt(0);
		}
	}

	salvarFicha(): Observable<boolean> {
		if (this.obterFichaAtual().id) {
			return this.fichaService
				.atualizarFicha(
					this.obterFichaAtual().id,
					this.convertFormToFichaPrograma()
				)
				.pipe(
					map((result) => {
						const copiaPrograma = this.obterCopiaPrograma();
						const indexFicha = copiaPrograma.fichas.findIndex((ficha) => {
							return ficha.id === result.id;
						});
						copiaPrograma.fichas[indexFicha] = result;
						this.programa$.next(copiaPrograma);
						this.fichaSendoModificada$.next(false);
						this.selecionarFicha(indexFicha);
						return true;
					})
				);
		} else {
			return this.fichaService
				.criarFicha(this.convertFormToFichaPrograma())
				.pipe(
					map((ret) => {
						if (
							this.programa$.value.fichas &&
							this.programa$.value.fichas.length === 1
						) {
							if (this.programa$.value.fichas[0].predefinida) {
								this.fichaFormGroup.get("id").setValue(ret.id);
								this.programa$.value.fichas[0].id = ret.id;
							}
						}
						this.fichaSendoModificada$.next(false);
						return true;
					})
				);
		}
	}

	salvarPrograma(programa): Observable<boolean> {
		return this.programaService.atualizarPrograma(programa.id, programa).pipe(
			map((response) => {
				if (response !== "aluno_possui_treino_periodo_informado") {
					this.atualizarProgramaService(response);
					return true;
				} else {
					return false;
				}
			})
		);
	}

	atualizarProgramaService(programaAtualizado) {
		const copiaPrograma = this.obterCopiaPrograma();
		copiaPrograma.nome = programaAtualizado.nome;
		copiaPrograma.inicio = programaAtualizado.inicio;
		copiaPrograma.termino = programaAtualizado.termino;
		copiaPrograma.totalTreinos = programaAtualizado.totalTreinos;
		copiaPrograma.qtdDiasSemana = programaAtualizado.qtdDiasSemana;

		if (
			programaAtualizado.professorMontou &&
			typeof programaAtualizado.professorMontou.id !== "undefined"
		) {
			copiaPrograma.professorMontou = {
				id: programaAtualizado.professorMontou.id,
				nome: programaAtualizado.professorMontou.nome,
				imageUri: programaAtualizado.professorMontou.imageUri,
			};
		} else {
			copiaPrograma.professorMontou = null;
		}

		this.programa$.next(copiaPrograma);
	}

	atualizarOrdemAtividades(
		atividadesFicha: Array<{ frontId: number; ordem: number }>
	) {
		atividadesFicha.forEach((atividadeFicha) => {
			this.ordemAtividades.forEach((ordemAtividade) => {
				if (ordemAtividade.frontId === atividadeFicha.frontId) {
					ordemAtividade.ordem = atividadeFicha.ordem;
				}
			});
		});
		this.atualizarOrdemAtividadesView();
	}

	obterOrdemAtividade(frontId: number): number {
		const ordemAtividade = this.ordemAtividades.find((ordem) => {
			return ordem.frontId === frontId;
		});

		return ordemAtividade.ordem;
	}

	private atualizarOrdemAtividadesView() {
		const copiaAtividadesView = this.atividadesFichaView$.value;
		this.ordemAtividades.forEach((ordemAtividade) => {
			copiaAtividadesView.forEach((atividadeView) => {
				if (atividadeView.frontId === ordemAtividade.frontId) {
					atividadeView.sequencia = ordemAtividade.ordem;
				}
			});
		});
		this.atividadesFichaView$.next(copiaAtividadesView);
	}

	private convertFormToFichaPrograma(): any {
		const result = this.fichaFormGroup.getRawValue();
		result.programaId = this.obterCopiaPrograma().id;
		if (
			result.dias_semana &&
			result.tipo_execucao === FichaTipoExecucao.DIAS_SEMANA
		) {
			const weekDays = ["SG", "TR", "QA", "QI", "SX", "SB", "DM"];
			const replacement = [];
			result.dias_semana.forEach((controlDia, index) => {
				if (controlDia) {
					replacement.push(weekDays[index]);
				}
			});
			result.dias_semana = replacement;
		} else {
			result.dias_semana = [];
		}
		const atividades = [];
		result.atividades.forEach((atividadeFicha) => {
			const atividadeResult: any = atividadeFicha;
			atividadeResult.sequencia = this.obterOrdemAtividade(
				atividadeResult.frontId
			);
			atividadeResult.atividadesSequenciaSet = [];
			if (
				atividadeFicha.metodoExecucao === AtividadeMetodoExecucao.BI_SET ||
				atividadeFicha.metodoExecucao === AtividadeMetodoExecucao.TRI_SET
			) {
				const atividadeSequenciaSet1 = result.atividades.find((atividade) => {
					return (
						parseInt(atividade.frontId, 10) ===
						parseInt(atividadeFicha.atividadeSet1, 10)
					);
				});
				if (atividadeSequenciaSet1) {
					atividadeResult.atividadesSequenciaSet.push(
						atividadeSequenciaSet1.id
					);
				}
				let atividadeSequenciaSet2 = null;
				if (atividadeFicha.atividadeSet2) {
					atividadeSequenciaSet2 = result.atividades.find((atividade) => {
						return (
							parseInt(atividade.frontId, 10) ===
							parseInt(atividadeFicha.atividadeSet2, 10)
						);
					});
				}
				if (atividadeSequenciaSet2) {
					atividadeResult.atividadesSequenciaSet.push(
						atividadeSequenciaSet2.id
					);
				}
			}
			atividades.push(atividadeResult);
		});
		atividades.forEach((atividade) => {
			delete atividade.atividadeSet1;
			delete atividade.atividadeSet2;
			delete atividade.frontId;
		});
		result.atividades = atividades;
		return result;
	}
}
