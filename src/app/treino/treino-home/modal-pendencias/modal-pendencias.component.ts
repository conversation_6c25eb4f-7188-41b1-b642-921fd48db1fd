import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoConfigCacheService } from "../../../base/configuracoes/configuration.service";

@Component({
	selector: "pacto-modal-pendencias",
	templateUrl: "./modal-pendencias.component.html",
	styleUrls: ["./modal-pendencias.component.scss"],
})
export class ModalPendenciasComponent implements OnInit {
	@Input() nomeProfessor: string;

	formGroup: FormGroup = new FormGroup({
		showMessage: new FormControl(),
	});

	constructor(
		private modal: NgbActiveModal,
		private router: Router,
		private treinoConfigService: TreinoConfigCacheService
	) {}

	ngOnInit() {}

	close() {
		if (this.formGroup.get("showMessage").value) {
			this.modal.close("notShowAgain");
		} else {
			this.modal.close();
		}
	}

	dismiss() {
		this.modal.dismiss();
	}

	remindLater() {
		this.modal.close("remindLater");
	}

	openPendencias() {
		if (this.formGroup.get("showMessage").value) {
			this.modal.close("notShowAgain");
		} else {
			this.modal.close("onlyPendencias");
		}

		this.router.navigate(["treino", "montagem-programa", "prescricao"], {
			queryParams: { fromModalPendencias: true },
		});
	}

	get validarConfigVisualizarAvisoDePendencias(): boolean {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino
				.permitir_visualizar_aviso_de_pendencias
		) {
			return true;
		} else {
			return false;
		}
	}
}
