@import "src/assets/scss/pacto/plataforma-import.scss";

.grid {
	display: grid;
	margin-top: 30px;
	grid-template-columns: 1fr 1fr;
	grid-template-rows: 1fr 1fr 1fr;
	grid-gap: 30px;

	@media (min-width: $bootstrap-breakpoint-lg) {
		grid-template-columns: 1fr 1fr 1fr 1fr;
		grid-template-rows: 1fr;
	}
}

pacto-cat-pie-chart {
	display: inline-block;
}

pacto-cat-card-plain {
	min-height: 100%;
}

.update-indicadores {
	@extend .type-caption;
	color: $cinza05;
	margin-left: 5px;
	height: 14px;
}

.card-wrapper.grafico {
	text-align: center;
}

.action-filters {
	display: flex;
}

.indicadores-titulo {
	text-transform: capitalize;
	flex-grow: 1;
}

.bi-wrapper .top-wrapper {
	display: flex;
	justify-content: space-between;
	color: $pretoPri;
	margin-bottom: 30px;

	pacto-cat-select {
		width: 200px;
		display: block;
	}

	pacto-cat-button {
		margin-left: 5px;
		margin-right: 5px;
	}

	.action-filters {
		display: flex;
	}
}

.loading-blur {
	filter: blur(5px);
	pointer-events: none;
}

.loading-text {
	@extend .type-h6-bold;
	color: $azulPacto04;
	position: absolute;
	width: 168px;
	height: 16px;
	left: calc(50% - 168px / 2);
	top: calc(50% + 35px / 2);
}

.pct-refresh-cw {
	color: $azulPacto04;
	font-size: 30px;
	line-height: 30px;
	font-style: normal;
}

.sk-fading-circle {
	position: absolute;
	width: 30px;
	height: 30px;
	left: calc(50% - 30px / 2);
	top: calc(50% - 30px / 2);
	-webkit-animation-name: spin;
	-webkit-animation-duration: 2000ms;
	-webkit-animation-iteration-count: infinite;
	-webkit-animation-timing-function: linear;
	-moz-animation-name: spin;
	-moz-animation-duration: 2000ms;
	-moz-animation-iteration-count: infinite;
	-moz-animation-timing-function: linear;

	animation-name: spin;
	animation-duration: 2000ms;
	animation-iteration-count: infinite;
	animation-timing-function: linear;
}

@-ms-keyframes spin {
	from {
		-ms-transform: rotate(0deg);
	}
	to {
		-ms-transform: rotate(359deg);
	}
}

@-moz-keyframes spin {
	from {
		-moz-transform: rotate(0deg);
	}
	to {
		-moz-transform: rotate(359deg);
	}
}

@-webkit-keyframes spin {
	from {
		-webkit-transform: rotate(0deg);
	}
	to {
		-webkit-transform: rotate(359deg);
	}
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(359deg);
	}
}
