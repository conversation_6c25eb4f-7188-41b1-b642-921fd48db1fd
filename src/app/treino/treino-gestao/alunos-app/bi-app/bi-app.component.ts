import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import {
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	PactoIcon,
	PieChartSet,
	RelatorioComponent,
	TraducoesXinglingComponent,
	PactoColor,
} from "ui-kit";
import { TreinoApiBiService, BiApp } from "treino-api";
import {
	ModalRelatorioConfig,
	relatorios,
} from "../../../treino-bi/components/indicadores-grid/relatorios.config";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

@Component({
	selector: "pacto-bi-app",
	templateUrl: "./bi-app.component.html",
	styleUrls: ["./bi-app.component.scss"],
})
export class BiAppComponent implements OnInit {
	constructor(
		private treinoBiService: TreinoApiBiService,
		private modal: ModalService,
		private rest: RestService,
		private cd: ChangeDetectorRef,
		private session: SessionService
	) {}

	colors: PactoColor[] = [PactoColor.AZULIM_PRI, PactoColor.CINZA_PRI];
	graficoSeries: PieChartSet[] = [];
	percentualAlunosApp;
	endpointShare: string;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	biApp: BiApp = {};
	relatorio: RelatorioComponent;
	loading = false;
	config: PactoDataGridConfigDto;

	ngOnInit() {
		this.obterBi("");
		this.session.notificarRecursoEmpresa(RecursoSistema.BI_APP);
	}

	atualizarBi() {
		this.obterBi("/reload");
	}

	obterBi(reload) {
		this.loading = true;
		this.treinoBiService.biApp(reload).subscribe((result) => {
			if (result) {
				this.biApp = result;
				this.percentualAlunosApp = this.biApp.percentualAlunosUsamApp;
				this.graficoSeries = [
					{ name: "utilizam", data: this.percentualAlunosApp },
					{ name: "não utilizam", data: 100 - this.percentualAlunosApp },
				];
				this.loading = false;
				this.cd.detectChanges();
			}
		});
	}

	get PactoIcon() {
		return PactoIcon;
	}

	cardClickHandler(name, numeroIndicador) {
		this.openModalHandler(relatorios[name], numeroIndicador);
	}

	pieFormatter = (v) => {
		return v + "%";
	};

	private openModalHandler(
		item: ModalRelatorioConfig,
		numeroIndicador: number
	) {
		const codigoPessoa = this.session.loggedUser.professorResponse.codigoPessoa;
		const endpoint =
			item.endpoint === "treino-bi/alunos-ativos"
				? item.endpoint + "/" + codigoPessoa
				: item.endpoint;
		this.endpointShare = this.rest.buildFullUrl(endpoint);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		this.relatorio = pctModal.componentInstance;
		switch (item.title) {
			case "todosAlunosAtivosTitle":
				this.relatorio.telaId = "todosalunosativos";
				break;
			case "usamAppTitle":
				this.relatorio.telaId = "usamapp";
				break;
			case "naoUsamAppTitle":
				this.relatorio.telaId = "naousamapp";
				break;
			case "inativosAppTitle":
				this.relatorio.telaId = "inativosapp";
				break;
		}
		this.relatorio.sessionService = this.session;
		if (numeroIndicador > 0) {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(endpoint),
				logUrl: this.rest.buildFullUrl(
					"log/listar-log-exportacao/" + this.relatorio.telaId
				),
				pagination: item.pagination,
				quickSearch: true,
				exportButton: false,
				rowClick: false,
				columns: [],
			};
		} else {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(endpoint),
				pagination: item.pagination,
				quickSearch: true,
				exportButton: false,
				rowClick: false,
				columns: [],
			};
		}
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
	}

	openModalCharPie(event: any) {
		const tipo = event === 0 ? "usamApp" : "naoUsamApp";
		this.openModalHandler(relatorios[tipo], event);
	}
}
