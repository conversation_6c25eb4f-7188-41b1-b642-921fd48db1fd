<pacto-cat-layout-v2>
	<div [ngClass]="{ 'loading-blur': loading }" class="bi-wrapper">
		<div class="top-wrapper">
			<div class="type-h2 indicadores-titulo">
				<ng-container i18n="@@bi-app:titulo">Aplicativos ativos</ng-container>
				<div class="update-indicadores">
					<span>
						<ng-container i18n="treino-bi:ultima-atualizacao:data">
							Última atualização:
						</ng-container>
						{{ biApp.ultimaAtualizacao }}
					</span>
				</div>
			</div>

			<div class="action-filters">
				<ng-container>
					<pacto-cat-button
						(click)="atualizarBi()"
						[icon]="'pct pct-refresh-cw'"
						[id]="'btn-atualizar-bi'"
						i18n-label="@@treino-bi:btn-atualizar-new"
						label="atualizar"></pacto-cat-button>
				</ng-container>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12 col-lg-12 col-xl-4">
				<pacto-cat-card-plain>
					<div class="card-wrapper grafico">
						<div class="utilizacao-title" i18n="@@treino-bi:titulo-grafico">
							Gráfico de utilização de aplicativos
						</div>
						<ng-container
							*ngIf="
								percentualAlunosApp != null && percentualAlunosApp != undefined
							">
							<pacto-cat-pie-chart
								(dataPointSelected)="openModalCharPie($event)"
								[centerValue]="percentualAlunosApp"
								[colors]="colors"
								[id]="'composicao-pie'"
								[labelFormatter]="pieFormatter"
								[series]="graficoSeries"
								[showLegend]="false"
								[simbol]="'%'"
								i18n-labelCenter="@@treino-bi:ativos-usam-app-new"
								labelCenter="dos ativos usam app"
								style="cursor: pointer"></pacto-cat-pie-chart>
						</ng-container>
					</div>
				</pacto-cat-card-plain>
			</div>

			<div class="col-md-12 col-lg-12 col-xl-8">
				<pacto-cat-card-plain>
					<div class="resumo-chart">
						<div class="utilizacao-title" i18n="@@treino-bi:resumo-alunos">
							Resumo dos alunos
						</div>
						<div class="grid">
							<pacto-cat-sm-info-block-icon
								(click)="cardClickHandler('todosAlunosAtivos', biApp.ativos)"
								[icon]="PactoIcon.PCT_USERS"
								[value]="biApp.ativos"
								i18n-label="@@treino-bi:total-alunos-ativos-new"
								id="total-ativos"
								label="Total de alunos ativos"></pacto-cat-sm-info-block-icon>

							<pacto-cat-sm-info-block-icon
								(click)="cardClickHandler('usamApp', biApp.ativosComApp)"
								[icon]="PactoIcon.PCT_USER_CHECK"
								[value]="biApp.ativosComApp"
								i18n-label="@@treino-bi:alunos-com-aplicativos-instalados-new"
								id="total-usam"
								label="Alunos que tem o aplicativo instalado"></pacto-cat-sm-info-block-icon>

							<pacto-cat-sm-info-block-icon
								(click)="cardClickHandler('naoUsamApp', biApp.ativosSemApp)"
								[icon]="PactoIcon.PCT_USER_X"
								[value]="biApp.ativosSemApp"
								i18n-label="@@treino-bi:alunos-sem-aplicativos-instalados-new"
								id="total-nao-usam"
								label="Alunos que não tem o aplicativo instalado"></pacto-cat-sm-info-block-icon>

							<pacto-cat-sm-info-block-icon
								(click)="cardClickHandler('inativosApp', biApp.inativosComApp)"
								[icon]="PactoIcon.PCT_X_CIRCLE"
								[value]="biApp.inativosComApp"
								i18n-label="
									@@treino-bi:alunos-inativos-com-aplicativos-instalados-new"
								id="total-inativos-app"
								label="Alunos inativos e visitantes com o aplicativo instalado"></pacto-cat-sm-info-block-icon>
						</div>
					</div>
				</pacto-cat-card-plain>
			</div>
		</div>
	</div>

	<pacto-traducoes-xingling #traducoes>
		<span xingling="alunosAtivosTitle">Alunos Ativos</span>
		<span xingling="todosAlunosAtivosTitle">Alunos Ativos</span>
		<span xingling="inativosAppTitle">
			Alunos inativos e visitantes com o aplicativo instalado
		</span>
		<span xingling="usamAppTitle">
			Alunos ativos que tem o aplicativo instalado
		</span>
		<span xingling="naoUsamAppTitle">
			Alunos ativos que não tem o aplicativo instalado
		</span>
		<ng-template xingling="matricula">Matrícula</ng-template>
		<ng-template xingling="nomeProfessor">Nome do Professor</ng-template>
		<ng-template xingling="nomeAbreviado">Nome do aluno</ng-template>
		<ng-template xingling="nome">Nome do aluno</ng-template>
		<ng-template xingling="dataPrograma">Venc. Programa</ng-template>
		<ng-template xingling="dataUltimoacesso">Último acesso</ng-template>
		<ng-template xingling="dataVigenciaAteAjustadaApresentar">
			Data Vencimento
		</ng-template>
	</pacto-traducoes-xingling>
</pacto-cat-layout-v2>
