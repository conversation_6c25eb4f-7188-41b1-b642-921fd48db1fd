<ng-template #celulaDataTerminoContrato let-item="item">
	{{ item.terminoContrato ? (item.terminoContrato | date : "shortDate") : "-" }}
</ng-template>
<ng-template #celulaDataTerminoProgVigente let-item="item">
	{{
		item.terminoProgramaVigente
			? (item.terminoProgramaVigente | date : "shortDate")
			: "-"
	}}
</ng-template>

<div class="modal-body">
	<pacto-relatorio
		*ngIf="ready"
		[baseFilter]="baseFilters"
		[table]="table"></pacto-relatorio>
</div>

<div class="modal-footer">
	<button
		(click)="dismiss()"
		class="btn btn-secondary"
		i18n="@@relatorio-atividade-professores:fechar">
		Fe<PERSON>r
	</button>
</div>

<pacto-traducoes-xingling #traducaoColunas>
	<span
		i18n="@@relatorio-atividade-professores:matricula:coluna"
		xingling="matriculaLabel">
		Matricula
	</span>
	<span
		i18n="@@relatorio-atividade-professores:nome:coluna"
		xingling="nomeLabel">
		Nome
	</span>
	<span
		i18n="@@relatorio-atividade-professores:situacao:coluna"
		xingling="situacaoLabel">
		Situação
	</span>
	<span
		i18n="@@relatorio-atividade-professores:termino-contrato:coluna"
		xingling="terminoContratoLabel">
		Término Contrato
	</span>
	<span
		i18n="@@relatorio-atividade-professores:termino-programa-vigente:coluna"
		xingling="terminoProgramaVigenteLabel">
		Término do programa Vigente
	</span>

	<span
		i18n="@@relatorio-atividade-professores:termino-programa-vigente:coluna"
		xingling="dataAcompanhamentoLabel">
		Data/hora do acompanhamento
	</span>
</pacto-traducoes-xingling>
