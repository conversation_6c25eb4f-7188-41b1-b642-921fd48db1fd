import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	DataFiltro,
	GridFilterConfig,
	GridFilterType,
} from "ui-kit";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { IndicadorDetalhesModalComponent } from "./indicador-detalhes-modal/indicador-detalhes-modal.component";
import { TreinoApiColaboradorService } from "treino-api";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService } from "@base-core/modal/modal.service";
import { TraducoesXinglingComponent } from "ui-kit";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-atividade-professores",
	templateUrl: "./atividade-professores.component.html",
	styleUrls: ["./atividade-professores.component.scss"],
})
export class AtividadeProfessoresComponent implements OnInit, AfterViewInit {
	@ViewChild("traducaoColunas", { static: true })
	traducaoColunas: TraducoesXinglingComponent;
	// TABLE LABEL
	@ViewChild("celulaProfessor", { static: true }) celulaProfessor;

	@ViewChild("relatorio", { static: false }) relatorio: RelatorioComponent;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("professorLabel", { static: true }) professorLabel;

	constructor(
		private rest: RestService,
		private modal: ModalService,
		private colaboradorService: TreinoApiColaboradorService,
		public sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;
	logUrl;
	filtrosCarregados = false;

	private professores: Array<any>;
	private indicadorEnum = {
		novos: "N_PROF_TREINO_NOVO",
		renovados: "N_PROF_TREINO_RENOVADO",
		revisados: "N_PROF_TREINO_REVISADO",
		acompanhamentos: "N_PROF_TREINO_ACOMPANHADO",
		revisar: "N_PROF_TREINO_REVISAR",
		atividadesAcompanhamentos: "N_ATIVIDADE_PROF_TREINO_ACOMPANHADO",
	};
	private dataInicioPadrao = new Date(
		new Date().setDate(new Date().getDate() - 30)
	).valueOf();
	private dataFimPadrao = new Date().valueOf();

	ngOnInit() {
		this.fetchFilterData().subscribe(() => {
			this.configFilters();
			this.filtrosCarregados = true;
			this.configTable();
			this.ready = true;
			this.logUrl = this.rest.buildFullUrl(
				"log/listar-log-exportacao/indicadoresatividadesprofessores"
			);
			this.cd.detectChanges();
		});
	}

	ngAfterViewInit() {
		setTimeout(() => {
			if (this.relatorio && this.relatorio.fetchFiltros) {
				this.aplicarFiltrosIniciais();
			}
		}, 200);
	}

	private aplicarFiltrosIniciais() {
		this.relatorio.temporaryFilters = {
			filters: {
				dataInicio: this.dataInicioPadrao,
				dataFim: this.dataFimPadrao,
				professoresIds: [],
			},
			configs: {},
		};
		this.relatorio.reloadData();
	}

	cellClickHandler($event) {
		if ($event.column.nome !== "nome") {
			let titulo;
			if (typeof $event.row.professor === "undefined") {
				titulo = `Total - ${this.traducaoColunas.getLabel($event.column.nome)}`;
			} else {
				titulo = `${
					$event.row.professor.nome
				} - ${this.traducaoColunas.getLabel($event.column.nome)}`;
			}
			const filter = this.buildDetailModalFilter($event);
			const modalHandle = this.modal.openFullscreen(
				titulo,
				IndicadorDetalhesModalComponent
			);
			modalHandle.componentInstance.setBaseFilters(filter);
		}
	}

	private buildDetailModalFilter($event) {
		const filter = this.relatorio.fetchFiltros();
		const detailsFilter: DataFiltro = {
			filters: {},
		};
		const targetKeys = ["dataInicio", "dataFim", "professores"];
		if (filter.filters) {
			targetKeys.forEach((target) => {
				const value = filter.filters[target];
				if (value !== null && value !== undefined) {
					detailsFilter.filters[target] = value;
				}
			});
		}
		detailsFilter.filters.indicador = this.indicadorEnum[$event.column.nome];
		if (typeof $event.row.professor === "undefined") {
			if (typeof filter.filters.professoresIds !== "undefined") {
				detailsFilter.filters.professoresIds = filter.filters.professoresIds;
			}
		} else {
			detailsFilter.filters.professorId = $event.row.professor.id;
		}
		return detailsFilter;
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService.obterTodosColaboradores().pipe(
			map((data) => {
				data.content.forEach((professor: any) => {
					professor.value = professor.id;
					professor.label = professor.nome;
				});
				this.professores = data.content;
				return true;
			})
		);
		return professores$;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("professores/indicadores-atividade"),
			exportButton: false,
			pagination: false,
			totalRow: true,
			rowClick: false,
			columns: [
				{
					nome: "nome",
					titulo: this.traducaoColunas.getLabel("nomeTitulo"),
					ordenavel: false,
					celula: this.celulaProfessor,
					defaultVisible: true,
				},
				{
					nome: "novos",
					titulo: this.traducaoColunas.getLabel("novos"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "renovados",
					titulo: this.traducaoColunas.getLabel("renovados"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "revisados",
					titulo: this.traducaoColunas.getLabel("revisados"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "acompanhamentos",
					titulo: this.traducaoColunas.getLabel("acompanhamentos"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "revisar",
					titulo: this.traducaoColunas.getLabel("revisar"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "atividadesAcompanhamentos",
					titulo: this.traducaoColunas.getLabel("atividadesAcompanhamentos"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dataInicio",
					label: this.dataInicioLabel,
					type: GridFilterType.DATE_POINT,
					initialValue: this.dataInicioPadrao,
				},
				{
					name: "dataFim",
					label: this.dataFimLabel,
					type: GridFilterType.DATE_POINT,
					initialValue: this.dataFimPadrao,
				},
				{
					name: "professoresIds",
					label: this.professorLabel,
					type: GridFilterType.MANY,
					options: this.professores,
				},
			],
		};
	}
}
