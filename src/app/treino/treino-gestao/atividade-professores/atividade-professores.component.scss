@import "src/assets/scss/pacto/plataforma-import.scss";

.table-wrapper {
	background-color: #fff;
	position: relative;
}

.external-actions {
	position: absolute;
	top: 10px;
	right: 10px;
	display: flex;
	align-items: center;
	gap: 10px;
}

.external-actions pacto-log.primary {
	margin: 0;
	order: 1;
}

.div-actions-btn {
	display: flex;
	align-items: center;
	gap: 10px;
}

.log-button {
	order: 1;
}
.share-button-tabela {
	order: 2;
}

::ng-deep pacto-log.primary {
	margin-left: -315px;

	.btn {
		height: 40px;
		color: $azul;
		padding: 4px 10px;
		margin-right: 8px;
		background: $branco;
		border: 1px solid $azulimPri;
	}

	.btn:hover {
		color: $azul;
		background: $branco;
		border: 1px solid $azul;
	}

	.btn.btn-primary:focus,
	.show > .btn-primary.dropdown-toggle {
		color: $azul;
		background: $branco;
		box-shadow: 0 0 0 -0.2rem rgb(3, 127, 226);
	}

	.btn-primary:focus,
	.btn-primary.focus {
		box-shadow: 0 0 0 0.2rem rgba(3, 127, 226, 0.5);
	}
}
