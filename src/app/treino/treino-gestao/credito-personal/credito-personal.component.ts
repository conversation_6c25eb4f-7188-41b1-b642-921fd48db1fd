import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-credito-personal",
	templateUrl: "./credito-personal.component.html",
	styleUrls: ["./credito-personal.component.scss"],
})
export class CreditoPersonalComponent implements OnInit {
	urlGestao;

	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.urlGestao =
			`${this.urlBase}/paginaGestaoCredito/` +
			`${this.sessionService.empresaId}/${this.sessionService.apiToken}`;
		this.cd.detectChanges();
	}

	get urlBase(): string {
		return this.discoveryService.getUrlMap().urlTreinoPersonal
			? this.discoveryService.getUrlMap().urlTreinoPersonal
			: "https://zw90.pactosolucoes.com.br:9090/GestaoPersonal";
	}
}
