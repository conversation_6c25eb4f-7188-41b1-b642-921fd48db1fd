import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";
import { IndicadoresAgendaComponent } from "./indicadores-agenda.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";

const routes: Routes = [
	{
		path: "",
		component: IndicadoresAgendaComponent,
	},
];

@NgModule({
	imports: [BaseSharedModule, RouterModule.forChild(routes), CommonModule],
	entryComponents: [IndicadoresAgendaComponent],
	declarations: [IndicadoresAgendaComponent],
})
export class IndicadoresAgendaModule {}
