import { ChangeDetectorR<PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiColaboradorService } from "treino-api";
import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	DataFiltro,
	RelatorioComponent,
} from "ui-kit";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";

import { TraducoesXinglingComponent } from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { IndicadorAgendaModalComponent } from "./indicador-agenda-modal/indicador-agenda-modal.component";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-indicadores-agenda",
	templateUrl: "./indicadores-agenda.component.html",
	styleUrls: ["./indicadores-agenda.component.scss"],
})
export class IndicadoresAgendaComponent implements OnInit {
	@ViewChild("celulaProfessor", { static: false }) celulaProfessor;

	@ViewChild("nomeTitulo", { static: false }) nomeTitulo;
	@ViewChild("agendadosTitulo", { static: false }) agendadosTitulo;
	@ViewChild("executadosTitulo", { static: false }) executadosTitulo;
	@ViewChild("canceladosTitulo", { static: false }) canceladosTitulo;
	@ViewChild("faltasTitulo", { static: false }) faltasTitulo;

	// Filter values
	@ViewChild("professorLabel", { static: false }) professorLabel;
	@ViewChild("professor2Label", { static: false }) professor2Label;
	@ViewChild("tiposEventoLabel", { static: false }) tiposEventoLabel;
	@ViewChild("tipoEventoTranslator", { static: false }) tipoEventoTranslator;

	// Status labels
	@ViewChild("relatorio", { static: false }) relatorio: RelatorioComponent;

	@ViewChild("dataInicioLabel", { static: false }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: false }) dataFimLabel;

	@ViewChild("titleModal", { static: true }) titleModal;
	@ViewChild("xingling", { static: true }) xingling: TraducoesXinglingComponent;

	constructor(
		private modal: NgbModal,
		private rest: RestService,
		private sessionService: SessionService,
		private colaboradorService: TreinoApiColaboradorService,
		private cd: ChangeDetectorRef
	) {}

	integracaoZW = false;

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;
	paramTitleModal: any = {};
	incluirProfessoresInativos = false;
	incluirTipoAgendamentoInativo = false;

	private professores: Array<any>;
	private tiposDeEvento: Array<any>;

	private indicadorEnum = {
		agendados: "AGENDADOS",
		executados: "EXECUTADO",
		cancelados: "CANCELADO",
		faltas: "FALTOU",
	};

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;

		setTimeout(() => {
			this.configTable();
			this.ready = true;
			this.cd.detectChanges();
		}, 500);
		this.fetchFilterData().subscribe(() => {
			this.configFilters();
		});
		this.getTiposEvento(false).subscribe(() => {
			this.configFilters();
		});
	}

	cellClickHandler($event) {
		if ($event.column.nome !== "nome" && $event.row.professor) {
			this.paramTitleModal.nomeColuna = $event.column.nome;
			this.paramTitleModal.nomeProfessor = $event.row.professor.nome;
			const filter = this.buildDetailModalFilter($event);
			const modalHandle = this.modal.open(IndicadorAgendaModalComponent, {
				size: "lg",
			});
			modalHandle.componentInstance.setBaseFiltersAgenda(
				filter,
				this.titleModal
			);
		} else {
			if ($event.column.nome !== "nome") {
				this.paramTitleModal.nomeColuna = $event.column.nome;
				this.paramTitleModal.nomeProfessor = "Todos";
				const filter = this.buildDetailModalFilter($event);
				const modalHandle = this.modal.open(IndicadorAgendaModalComponent, {
					size: "lg",
				});
				modalHandle.componentInstance.setBaseFiltersAgenda(
					filter,
					this.titleModal
				);
			}
		}
	}

	alterConfigHandler(statusconfig: any) {
		if (statusconfig.clickConfigId === "incluirTipoAgendamentoInativo") {
			this.incluirTipoAgendamentoInativo =
				!statusconfig.configsValue.incluirTipoAgendamentoInativo;
			this.getTiposEvento(this.incluirTipoAgendamentoInativo).subscribe(() => {
				this.configFilters();
			});
		}
	}

	private buildDetailModalFilter($event) {
		const filter = this.relatorio.fetchFiltros();
		const detailsFilter: DataFiltro = {
			filters: {},
		};
		const targetKeys = [
			"dataInicio",
			"dataFim",
			"tiposEvento",
			"professoresIds",
		];
		if (filter.filters) {
			targetKeys.forEach((target) => {
				const value = filter.filters[target];
				if (value !== null && value !== undefined) {
					detailsFilter.filters[target] = value;
				}
			});
		}
		detailsFilter.filters.status = this.indicadorEnum[$event.column.nome];
		if ($event.row.professor) {
			detailsFilter.filters.professorId = $event.row.professor.id;
		}

		// Make deep clone
		const json = JSON.stringify(detailsFilter);
		const clone = JSON.parse(json);

		return clone;
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradores(this.incluirProfessoresInativos)
			.pipe(
				map((data) => {
					data.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = data.content;
					return true;
				})
			);
		return professores$;
	}

	private getTiposEvento(incluirInativos: boolean): Observable<any> {
		return this.colaboradorService
			.obterTodosTiposDeEvento(incluirInativos)
			.pipe(
				map((data) => {
					data.content.forEach((evento: any) => {
						evento.value = evento.id;
						evento.label = evento.descricao;
					});
					this.tiposDeEvento = data.content;
				})
			);
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("gestao/indicadores-agenda"),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/relatoriodosservicosagendados"
			),
			exportButton: false,
			pagination: false,
			totalRow: true,
			rowClick: false,
			columns: [
				{
					nome: "nome",
					titulo: this.xingling.getTemplate("nomeTitulo"),
					ordenavel: false,
					celula: this.xingling.getTemplate("celulaProfessor"),
					defaultVisible: true,
				},
				{
					nome: "agendados",
					titulo: this.xingling.getTemplate("agendadosTitulo"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "executados",
					titulo: this.xingling.getTemplate("executadosTitulo"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "cancelados",
					titulo: this.xingling.getTemplate("canceladosTitulo"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
				{
					nome: "faltas",
					titulo: this.xingling.getTemplate("faltasTitulo"),
					defaultVisible: true,
					cellPointerCursor: true,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "tiposEvento",
					label: this.xingling.getTemplate("tiposEventoLabel"),
					type: GridFilterType.MANY,
					options: this.tiposDeEvento,
				},
				{
					name: "dataInicio",
					label: this.xingling.getTemplate("dataInicioLabel"),
					type: GridFilterType.DATE_POINT,
					initialValue: new Date(
						new Date().setDate(new Date().getDate() - 30)
					).valueOf(),
				},
				{
					name: "dataFim",
					label: this.xingling.getTemplate("dataFimLabel"),
					type: GridFilterType.DATE_POINT,
					initialValue: new Date().valueOf(),
				},
				{
					name: "professoresIds",
					label: this.xingling.getTemplate("professorLabel"),
					type: GridFilterType.MANY,
					options: this.professores,
				},
			],
			configs: [
				{
					id: "incluirTipoAgendamentoInativo",
					label: this.xingling.getTemplate("tiposAgendamentoInativoLabel"),
					cleanParams: ["tiposEvento"],
				},
			],
		};
	}
}
