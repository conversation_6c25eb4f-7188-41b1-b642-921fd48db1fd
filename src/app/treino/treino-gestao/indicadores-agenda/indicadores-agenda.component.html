<!-- TABLE LABELS -->
<pacto-traducoes-xingling #xingling>
	<ng-template
		i18n="@@relatorio-servicos-agendados:title"
		xingling="tableTitulo">
		Relatório dos Serviços Agendados
	</ng-template>
	<ng-template
		i18n="@@relatorio-servicos-agendados:professores"
		xingling="nomeTitulo">
		Professor<PERSON>
	</ng-template>
	<ng-template
		i18n="@@relatorio-servicos-agendados:agendados"
		xingling="agendadosTitulo">
		Agendados
	</ng-template>
	<ng-template
		i18n="@@relatorio-servicos-agendados:executados"
		xingling="executadosTitulo">
		Executados
	</ng-template>
	<ng-template
		i18n="@@relatorio-servicos-agendados:cancelados"
		xingling="canceladosTitulo">
		Cancelados
	</ng-template>
	<ng-template
		i18n="@@relatorio-servicos-agendados:faltas"
		xingling="faltasTitulo">
		Faltas
	</ng-template>

	<!-- FILTER LABELS -->
	<ng-template
		i18n="@@relatorio-carteira-professores:professores:filtro"
		xingling="professorLabel">
		Professores
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:situacao-contrato:filtro"
		xingling="tiposEventoLabel">
		Tipos de agendamento
	</ng-template>
	<ng-template
		i18n="
			@@relatorio-carteira-professores:situacao-contrato:incluirTiposAgendamento"
		xingling="tiposAgendamentoInativoLabel">
		Incluir tipos de agendamento inativo?
	</ng-template>
	<ng-template
		i18n="@@relatorio-atividade-professores:data-inicio:filtro"
		xingling="dataInicioLabel">
		Data Início
	</ng-template>
	<ng-template
		i18n="@@relatorio-atividade-professores:data-fim:filtro"
		xingling="dataFimLabel">
		Data Fim
	</ng-template>
</pacto-traducoes-xingling>

<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#relatorio
			(cellClick)="cellClickHandler($event)"
			(filterConfigUpdate)="alterConfigHandler($event)"
			*ngIf="ready"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableTitle]="xingling.getTemplate('tableTitulo')"
			[table]="table"
			telaId="relatoriodosservicosagendados"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #traducaoColunas let-traducaoColuna="traducaoColuna">
	<ng-container [ngSwitch]="traducaoColuna">
		<span
			*ngSwitchCase="'agendados'"
			i18n="@@relatorio-carteira-professores:com-treino:coluna">
			Agendados
		</span>
		<span
			*ngSwitchCase="'executados'"
			i18n="@@relatorio-carteira-professores:sem-treino:coluna">
			Executados
		</span>
		<span
			*ngSwitchCase="'cancelados'"
			i18n="@@relatorio-carteira-professores:vencidos:coluna">
			Cancelados
		</span>
		<span
			*ngSwitchCase="'faltas'"
			i18n="@@relatorio-carteira-professores:prox-vencimento:coluna">
			Faltas
		</span>
	</ng-container>
</ng-template>

<ng-template #titleModal>
	{{ paramTitleModal.nomeProfessor }} -
	<ng-container
		*ngTemplateOutlet="
			traducaoColunas;
			context: { traducaoColuna: paramTitleModal.nomeColuna }
		"></ng-container>
</ng-template>
