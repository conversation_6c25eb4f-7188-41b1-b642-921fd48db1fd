import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { PactoDataGridConfig, DataFiltro } from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-indicador-agenda-modal",
	templateUrl: "./indicador-agenda-modal.component.html",
	styleUrls: ["./indicador-agenda-modal.component.scss"],
})
export class IndicadorAgendaModalComponent implements OnInit {
	@ViewChild("nomeLabel", { static: true }) nomeLabel;
	@ViewChild("evento", { static: true }) evento;
	@ViewChild("situacao", { static: true }) situacao;
	@ViewChild("horario", { static: true }) horario;
	@ViewChild("celulaHorario", { static: true }) celulaHorario;

	ready = false;
	table: PactoDataGridConfig;
	baseFilters: DataFiltro;
	tituloCard: TemplateRef<any>;

	constructor(private openModal: NgbActiveModal, private rest: RestService) {}

	ngOnInit() {
		this.configTable();
	}

	setBaseFiltersAgenda(filters, titleModal) {
		this.tituloCard = titleModal;
		this.baseFilters = filters;
		this.ready = true;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("gestao/indicadores-agenda/alunos"),
			pagination: false,
			rowClick: false,
			showSimpleTotalCount: true,
			quickSearch: false,
			columns: [
				{ nome: "nome", titulo: this.nomeLabel, defaultVisible: true },
				{
					nome: "evento",
					titulo: this.evento,
					defaultVisible: true,
					ordenavel: false,
				},
				{
					nome: "horario",
					titulo: this.horario,
					defaultVisible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: this.situacao,
					defaultVisible: true,
					ordenavel: false,
				},
			],
		});
	}

	dismiss() {
		this.openModal.dismiss();
	}
}
