<ng-template #nomeLabel i18n="@@relatorio-atividade-professores:nome:coluna">
	Nome
</ng-template>
<ng-template #evento i18n="@@relatorio-atividade-professores:situacao:coluna">
	Evento
</ng-template>
<ng-template
	#horario
	i18n="@@relatorio-atividade-professores:termino-contrato:coluna">
	<PERSON><PERSON><PERSON><PERSON>
</ng-template>
<ng-template
	#situacao
	i18n="@@relatorio-atividade-professores:termino-programa-vigente:coluna">
	Situação
</ng-template>
<ng-template #celulaHorario let-item="item">
	{{ item.terminoContrato ? (item.terminoContrato | date : "shortDate") : "-" }}
</ng-template>

<pacto-relatorio
	*ngIf="ready"
	[baseFilter]="baseFilters"
	[tableTitle]="tituloCard"
	[table]="table"></pacto-relatorio>

<div class="modal-footer">
	<button
		(click)="dismiss()"
		class="btn btn-secondary"
		i18n="@@relatorio-atividade-professores:fechar">
		<PERSON><PERSON><PERSON>
	</button>
</div>
