export class ColunasRankingProfessor {
	private PERCENTUAL_CRESCIMENTO_CARTEIRA = "porcentagemCrescimento";
	private ATIVOS = "alunosAtivos";
	private BI_TEMPO_CARTEIRA = "mediaCarteira";
	private PERCENTUAL_RENOVACAO_CARTEIRA = "percRenovaramContrato";
	private PERC_TREINO_EM_DIA = "treinoEmDia";
	private PERC_TREINO_VENCIDOS = "treinoVencido";
	private AG_CONFIRMACAO = "agAguardandoConf";
	private CANCELARAM = "agCancelaram";
	private CONFIRMARAM = "agConfirmaram";
	private FALTARAM = "agFaltaram";
	private AVALIACOES_FISICAS = "agAvaliacoesRealizadas";
	private COMPARECERAM = "agCompareceram";
	private NOVOS_TREINOS = "agNovosTreino";
	private OCUPACAO = "agOcupacao";
	private TREINOS_RENOVADOS = "agTreinosRenovado";
	private TREINOS_REVISADOS = "agTreinosRevisado";
	private AGENDAMENTOS = "agendamentos";
	private ALUNOS_APP_NAO_INSTALADO = "alunosNaoUtilizaApp";
	private ALUNOS_APP_INSTALADO_ATIVOS = "alunosAtivosUtilizaApp";
	private ALUNOS_CANCELADOS = "alunosCancelados";
	private INATIVOS = "alunosInativos";
	private ALUNOS_APP_INSTALADO = "alunosInativosUtilizaApp";
	private NAO_RENOVARAM = "alunosNaoRenovaram";
	private ESTRELAS_1 = "estrela1";
	private ESTRELAS_2 = "estrela2";
	private ESTRELAS_3 = "estrela3";
	private ESTRELAS_4 = "estrela4";
	private ESTRELAS_5 = "estrela5";
	private AVALIACOES = "avaliacaoPeloApp";
	private COM_AVALIACAO_FISICA = "comAvaliacaoFisica";
	private ATIVOS_COM_TREINO = "comTreino";
	private ALUNOS_A_VENCER = "contratoAVencer";
	private VENCIDOS = "contratoVencido";
	private AGENDAMENTOS_DISPONIBILIDADE = "agDisponibilidade";
	private NOVOS_CARTEIRA = "entraramCarteira";
	private HRS_ATENDIMENTO = "agHorasAtendimento";
	private HRS_DISPONIBILIDADE = "agHorasDisponibilidade";
	private DISPONIBILIDADES = "disponibilidades";
	private BI_TEMPO_PROGRAMA = "mediaPrograma";
	private NOVOS_CARTEIRA_NOVOS = "novosAlunos";
	private ACESSOS = "acessaramTreino";
	private ACESSOS_TREINO = "alunosDoTreinoAcessaramTreino";
	private EXECUCOES_TREINO = "execucoesTreino";
	private SMARTPHONE = "execucoesTreinoPeloApp";
	private EM_DIA = "programaEmDia";
	private REAGENDARAM = "agReagendaram";
	private RENOVARAM = "renovaramContrato";
	private TROCARAM_CARTEIRA = "sairamCarteira";
	private SEM_AVALIACAO = "semAvaliacaoFisica";
	private ATIVOS_SEM_TREINO = "alunosAtivosSemTreino";
	private TOTAL_ALUNOS = "totalAlunos";
	private TREINOS_A_VENCER = "treinosVencendo";
	private NOVOS_CARTEIRA_TROCARAM = "trocaramDeCarteira";
}
