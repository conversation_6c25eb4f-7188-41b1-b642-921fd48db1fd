@import "src/assets/scss/pacto/plataforma-import.scss";

.cabecalho {
	margin: 30px 0px 30px 0px;
	display: grid;
	justify-content: space-between;
	grid-template-columns: repeat(4, 1fr);

	.prof-dados {
		min-width: 350px;
		display: flex;

		img {
			margin-right: 20px;
			border-radius: 50%;
			border: 2px solid $cinza03;
			width: 64px;
			height: 64px;
		}
	}

	.dados {
		display: grid;
		text-transform: capitalize;

		label {
			font-size: 16px;
			font-weight: 400;
			line-height: 22px;
			color: $cinza04;
		}

		span {
			color: $preto03;
			font-size: 16px;
			font-weight: 600;
			line-height: 22px;
		}
	}
}

.right {
	text-align: right;
}

.detalhes {
	display: flex;
	font-size: 16px;
	font-weight: 600;
	line-height: 22px;
	color: $pretoPri;

	.linha-tempo {
		.titulo {
			margin: 10px;
		}

		width: 270px;
		height: 60vh;
		overflow-y: auto;
		display: block;

		.item {
			font-size: 14px;
			font-weight: 400;
			line-height: 16px;
			display: flex;

			.texto {
				padding-top: 10px;

				label {
					font-size: 12px;
					font-weight: 400;
					line-height: 16px;
					color: $cinzaPri;
				}
			}

			.linha {
				margin-right: 8px;

				.palito {
					width: 2px;
					height: 35px;
					background-color: $preto01;
					margin-left: 12px;
				}

				.icone {
					margin: 5px 0;
					width: 26px;
					height: 26px;
					display: block;
					line-height: 26px;
					text-align: center;
					border: 1px solid $azulPactoPri;
					color: $azulimPri;
					font-size: 16px;
					border-radius: 50%;
				}
			}
		}
	}

	.linha-tempo::-webkit-scrollbar {
		display: none;
	}

	.graficos::-webkit-scrollbar {
		display: none;
	}

	.graficos {
		width: 640px;
		padding: 10px;
		height: 60vh;
		overflow-y: auto;

		.grafico {
			height: 397px;

			&.tabela {
				height: auto;
			}

			padding: 30px;
			left: 315px;
			border-radius: 12px;
			box-shadow: 0px 4px 6px 0px #e4e5e6;
			margin-bottom: 15px;

			table {
				font-weight: normal;
				width: 100%;
				font-size: 14px;

				.pontos {
					text-align: right;
				}

				tr {
					line-height: 30px;

					th,
					td {
						padding-left: 10px;
					}
				}

				tr:nth-child(even) {
					background-color: #fafafa;
				}
			}
		}
	}
}

.toggle-tabela {
	text-align: right;
	height: 30px;
	padding-right: 25px;

	i {
		color: $azulimPri;
		font-size: 16px;
		cursor: pointer;
	}
}
