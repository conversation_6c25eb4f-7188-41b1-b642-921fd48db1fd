import { Component, OnInit } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	ColumnChartSet,
	DataFiltro,
	GridFilterConfig,
	PactoColor,
	PactoDataGridColumnConfig,
} from "ui-kit";

@Component({
	selector: "pacto-detalhes-professor",
	templateUrl: "./detalhes-professor.component.html",
	styleUrls: ["./detalhes-professor.component.scss"],
})
export class DetalhesProfessorComponent implements OnInit {
	tabela = false;
	dadosRanking;
	detalhes;
	endpointUrl;
	baseFilter: DataFiltro = {};
	filterConfig: GridFilterConfig = {
		filters: [],
	};
	treinoIndicadores: Array<string> = new Array<string>();
	alunoIndicadores: Array<string> = new Array<string>();
	agendaIndicadores: Array<string> = new Array<string>();
	seriesTreino: Array<ColumnChartSet> = new Array<ColumnChartSet>();
	seriesAgenda: Array<ColumnChartSet> = new Array<ColumnChartSet>();
	seriesAluno: Array<ColumnChartSet> = new Array<ColumnChartSet>();
	cores: PactoColor[] = [PactoColor.AZULIM_PRI];

	constructor(private openModal: NgbActiveModal, private modal: NgbModal) {}

	toggleTabela() {
		this.tabela = !this.tabela;
		try {
			localStorage.setItem("detalhe-professor", String(this.tabela));
		} catch (e) {
			console.log(e);
		}
	}

	dismiss() {
		this.openModal.close("fechar");
	}

	ngOnInit() {
		this.povoarTreino();
		this.povoarAgenda();
		this.povoarAlunos();
		try {
			const item = localStorage.getItem("detalhe-professor");
			if (item && item === "true") {
				this.tabela = true;
			} else {
				this.tabela = false;
			}
		} catch (e) {
			console.log(e);
		}
	}

	povoarTreino() {
		this.seriesTreino = new Array<ColumnChartSet>();
		const data: Array<any> = new Array<any>();
		this.treinoIndicadores = new Array<string>();
		this.detalhes.treino.forEach((t) => {
			this.treinoIndicadores.push(t.label);
			data.push(t.valor);
		});
		this.seriesTreino.push({
			name: "Quantidade",
			data,
		});
	}

	povoarAlunos() {
		this.seriesAluno = new Array<ColumnChartSet>();
		const data: Array<any> = new Array<any>();
		this.alunoIndicadores = new Array<string>();
		this.detalhes.alunos.forEach((t) => {
			this.alunoIndicadores.push(t.label);
			data.push(t.valor);
		});
		this.seriesAluno.push({
			name: "Quantidade",
			data,
		});
	}

	povoarAgenda() {
		this.seriesAgenda = new Array<ColumnChartSet>();
		const data: Array<any> = new Array<any>();
		this.agendaIndicadores = new Array<string>();
		this.detalhes.agenda.forEach((t) => {
			this.agendaIndicadores.push(t.label);
			data.push(t.valor);
		});
		this.seriesAgenda.push({
			name: "Quantidade",
			data,
		});
	}

	obterColunasRelatorio() {
		const colunas = [];
		const label: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "Indicador",
			campo: "label",
			titulo: "Indicador",
			ordenavel: true,
			defaultVisible: true,
		};
		const valor: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "pontos",
			campo: "pontos",
			titulo: "Pontos",
			ordenavel: true,
			defaultVisible: true,
		};
		colunas.push(label);
		colunas.push(valor);
		return colunas;
	}

	getFiltersShare(): DataFiltro {
		this.baseFilter.filters = {};
		this.baseFilter.configs = {};
		return this.baseFilter;
	}
}
