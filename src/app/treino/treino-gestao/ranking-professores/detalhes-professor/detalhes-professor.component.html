<div>
	<div class="modal-header">
		<h4 class="modal-title">
			<span i18n="@@relatorio-ranking-professores:configuracoes:titulo">
				Análise de colaborador
			</span>
		</h4>
		<button
			(click)="dismiss()"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<div class="right">
			<pacto-share-button
				[columns]="obterColunasRelatorio()"
				[endpoint]="endpointUrl"
				[filterConfig]="filterConfig"
				[filtros]="getFiltersShare()"
				[titulo]="'Ranking de professores'"
				[total]="1000"></pacto-share-button>
		</div>
		<div class="cabecalho">
			<div class="prof-dados">
				<img
					src="{{
						dadosRanking && dadosRanking.urlFoto
							? dadosRanking.urlFoto
							: 'assets/images/default-user-icon.png'
					}}" />
				<div class="dados">
					<label>Colaborador</label>
					<span>{{ dadosRanking?.nome }}</span>
				</div>
			</div>

			<div class="dados">
				<label>Início</label>
				<span>{{ detalhes?.dataCadastro }}</span>
			</div>

			<div class="dados">
				<label>Posição</label>
				<span>{{ dadosRanking.posicao }}</span>
			</div>

			<div class="dados">
				<label>Pontuação</label>
				<span>{{ dadosRanking.total }}</span>
			</div>
		</div>

		<div class="toggle-tabela">
			<i
				(click)="toggleTabela()"
				*ngIf="tabela === false"
				class="pct pct-list"
				title="Visualize os pontos em formato de lista"></i>
			<i
				(click)="toggleTabela()"
				*ngIf="tabela === true"
				class="pct pct-bar-chart-2"
				title="Visualize os pontos em formato de gráfico"></i>
		</div>
		<div class="detalhes">
			<div class="linha-tempo">
				<div class="titulo">Linha do tempo</div>
				<div class="item">
					<span class="linha"></span>
				</div>
				<div *ngFor="let item of detalhes?.linha" class="item">
					<span class="linha">
						<span class="icone"><i class="pct pct-refresh-cw"></i></span>
						<div class="palito"></div>
					</span>
					<span class="texto">
						<div>{{ item.label }}</div>
						<label>{{ item.momento }}</label>
					</span>
				</div>
			</div>
			<div *ngIf="tabela === true" class="graficos">
				<div class="grafico tabela">
					<table>
						<tr>
							<th>Indicador</th>
							<th class="pontos">Pontos</th>
						</tr>
						<tr *ngFor="let item of detalhes.content">
							<td>{{ item.label }}</td>
							<td class="pontos">{{ item.pontos }}</td>
						</tr>
					</table>
				</div>
			</div>
			<div *ngIf="tabela === false" class="graficos">
				<div class="grafico">
					<span>Alunos</span>
					<pacto-cat-column-chart
						[colors]="cores"
						[height]="290"
						[series]="seriesAluno"
						[xAxisLabels]="alunoIndicadores"></pacto-cat-column-chart>
				</div>
				<div class="grafico">
					<span>Treino</span>
					<pacto-cat-column-chart
						[colors]="cores"
						[height]="290"
						[series]="seriesTreino"
						[xAxisLabels]="treinoIndicadores"></pacto-cat-column-chart>
				</div>
				<div class="grafico">
					<span>Agenda</span>
					<pacto-cat-column-chart
						[colors]="cores"
						[height]="290"
						[series]="seriesAgenda"
						[xAxisLabels]="agendaIndicadores"></pacto-cat-column-chart>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #xingling>
	<span
		i18n="@@relatorio-ranking-professores:perc-treinos-dia:indicador"
		xingling>
		% treinos em dia
	</span>
	<span
		i18n="@@relatorio-ranking-professores:perc-treinos-vencido:indicador"
		xingling>
		% treinos vencidos
	</span>
</pacto-traducoes-xingling>
