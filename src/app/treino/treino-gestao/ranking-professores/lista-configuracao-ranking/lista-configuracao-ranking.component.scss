@import "~src/assets/scss/pacto/plataforma-import.scss";

.modal-body {
	padding: 0px;
}

.tabs {
	display: flex;
	flex-direction: row;
	margin: 20px 0px;
	border-bottom: 1px solid $cinza03;
	padding-left: 32px;

	.tab {
		line-height: 30px;
		margin: 0px;
		font-size: 14px;
		color: $cinza05;
		padding: 10px 20px;
		cursor: pointer;
	}

	&.TREINO .TREINO,
	&.ALUNOS .ALUNOS,
	&.AGENDA .AGENDA {
		color: $pretoPri;
		border-bottom: 3px solid $azulimPri;
	}
}

input.pontuacao {
	border: 1px solid $cinza03;
	width: 72px;
	padding: 0 8px;
	height: 28px;
	border-radius: 4px;
	color: $pretoPri;
	font-size: 14px;
}

.cfg-ranking.pct {
	cursor: pointer;

	&.pct-check-square {
		color: $azulim05;
	}
}

.search {
	position: relative;
	flex-grow: 1;
	margin: 25px 0px 0px 32px;

	input {
		width: 230px;
		padding-left: 30px;
	}

	i.pct {
		position: absolute;
		left: 10px;
		top: 12px;
	}
}
