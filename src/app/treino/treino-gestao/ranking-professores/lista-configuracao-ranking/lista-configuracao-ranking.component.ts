import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColunasRankingProfessor } from "../colunasRankingProfessor.model";
import { SnotifyService } from "ng-snotify";
import {
	TreinoApiConfiguracaoRankingService,
	AgrupamentoConfiguracaoRankingModel,
	ConfiguracaoRankingModel,
} from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { debounceTime, distinctUntilChanged } from "rxjs/operators";

@Component({
	selector: "pacto-lista-configuracao-ranking",
	templateUrl: "./lista-configuracao-ranking.component.html",
	styleUrls: ["./lista-configuracao-ranking.component.scss"],
})
export class ListaConfiguracaoRankingComponent implements OnInit {
	table: PactoDataGridConfig;
	alteradaConfig = false;
	selecionado = "TREINO";
	@ViewChild("indicadorColumn", { static: true }) indicadorColumn;
	@ViewChild("pesoColumn", { static: true }) pesoColumn;
	@ViewChild("ativoColumn", { static: true }) ativoColumn;
	@ViewChild("operacao", { static: true }) operacao;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	indicador: ColunasRankingProfessor = new ColunasRankingProfessor();
	formGroup = new FormGroup({});
	quickSearchControl = new FormControl();
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();
	maskPontuacao = {
		guide: false,
		mask: [/[-+]?/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/],
	};

	constructor(
		private openModal: NgbActiveModal,
		private modal: NgbModal,
		private rest: RestService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private configuracaoService: TreinoApiConfiguracaoRankingService
	) {}

	ngOnInit() {
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.configTable();
		this.quickSearchControl.valueChanges
			.pipe(debounceTime(1000), distinctUntilChanged())
			.subscribe((value) => {
				this.tableData.quickSearchCustomValue = value;
				this.tableData.reloadData();
				this.cd.detectChanges();
			});
	}

	toggleAtivo(item) {
		this.configuracaoService
			.toggleAtivo(item.indicador)
			.subscribe((retorno) => {
				this.alteradaConfig = true;
				item.ativa = retorno;
				this.cd.detectChanges();
			});
	}

	formControlItem(item): FormControl {
		let control = this.formGroup.get(item.indicador)
			? (this.formGroup.get(item.indicador) as FormControl)
			: null;
		if (!control) {
			control = new FormControl(item.pontuacao);
			control.valueChanges
				.pipe(debounceTime(1000), distinctUntilChanged())
				.subscribe((value) => {
					const configs = {};
					configs[item.indicador] = value;
					this.configuracaoService.configs(configs).subscribe((retorno) => {
						this.alteradaConfig = true;
					});
				});
			this.formGroup.addControl(item.indicador, control);
		}
		return control;
	}

	selecionarAgrupamento(agrupamento) {
		this.selecionado = agrupamento;
		this.formGroup = new FormGroup({});
		this.tableData.table.state.paginaNumero = 0;
		this.tableData.ngbPage = 1;
		this.tableData.table.endpointUrl = this.rest.buildFullUrl(
			`professores/ranking/configuracao/${this.selecionado}`
		);
		this.tableData.reloadData();
		this.cd.detectChanges();
	}

	dismiss() {
		this.openModal.close(this.alteradaConfig === true ? "atualizar" : "fechar");
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				`professores/ranking/configuracao/${this.selecionado}`
			),
			quickSearch: false,
			state: this.state,
			columns: [
				{
					nome: "indicador",
					titulo: "Indicador",
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "indicador",
					celula: this.indicadorColumn,
				},
				{
					nome: "pontuacao",
					titulo: "Pontuação",
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "pontuacao",
					celula: this.pesoColumn,
				},
				{
					nome: "ativa",
					titulo: "Ações",
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "ativa",
					celula: this.ativoColumn,
				},
			],
		});
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "edit") {
			this.openEdit(event.row);
		} else if (event.iconName === "remove") {
			this.openDelete(event.row);
		}
	}

	openDelete(row: any) {
		this.configuracaoService.removerConfiguracao(row.id).subscribe(() => {
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			this.snotifyService.success(removeSuccess);
			this.tableData.reloadData();
		});
	}

	openEdit(row) {
		this.openModal.close(row);
	}
}
