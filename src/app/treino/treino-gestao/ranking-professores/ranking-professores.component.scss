@import "src/assets/scss/pacto/plataforma-import.scss";

.shareWrapper {
	display: flex;
	justify-content: flex-end;
	padding: 15px 30px 0px 0px;
}

#refresh-ranking-dates {
	width: 100%;
	margin-left: 0;
}

.table-wrapper.pacto-shadow {
	width: 100%;

	pacto-cat-button {
		display: flex;
		align-items: flex-end;
		justify-content: flex-end;
		margin-right: 25.5px;
		padding: 10px;
	}
}

.table-wrapper.pacto-shadow {
	display: grid;
}

.periodo-pontuacao {
	margin-top: 30px;
	text-align: center;
	font-size: 14px;
	font-weight: 400;
	line-height: 26px;
	color: $pretoPri;
}

.titulo {
	font-size: 16px;
	font-weight: 600;
	line-height: 22px;
	padding: 12px;
	margin: 30px 30px 0px 30px;

	&.linha {
		border-bottom: 1px solid $cinzaPri;
	}
}

.acoes {
	float: right;
	display: flex;
}

.desempenho {
	width: 100%;
	font-size: 24px;
	font-weight: 400;
	line-height: 24px;
	text-align: center;

	.pct-minus {
		color: $cinzaPri;
	}

	.pct-caret-down {
		color: $hellboyPri;
		display: flex;
		margin-bottom: 10px;
	}

	.pct-caret-up {
		margin-top: 10px;
		display: flex;
		color: $chuchuzinhoPri;
	}
}

.content-wrapper {
	display: grid;
	justify-content: space-between;
}

.titulo,
.layout-subtitle {
	.pacto-primary {
		background-color: $azulim05;
		color: white;
		margin-left: 10px;

		i {
			display: inline-block;
			margin-right: 10px;
		}

		i.pct-chevron-down {
			margin-right: 0px;
		}
	}
}

.foto-professor {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	margin-right: 8px;
}

::ng-deep .modal-detalhes .modal-dialog {
	max-width: 957px;
}

.update-indicadores {
	@extend .type-caption;
	color: $cinza05;
	margin-left: 5px;
	height: 14px;
	margin-top: 10px;
}

.content-wrapper {
	width: 100%;
	display: flex;
	margin-bottom: 30px;
}

.icon-voltar {
	padding: 4px;
	cursor: pointer;

	i {
		margin-right: 16px;
	}

	.type-h4-bold {
		padding-top: 4px;
	}

	display: flex;
}

.layout-subtitle {
	text-align: end;
}

.btn.pacto-primary {
	.icon-drop {
		position: absolute;
		top: 0;
		right: 0;
		width: 35px;
		height: 38px;
		line-height: 38px;
		text-align: center;
	}

	i.pct-calendar {
		padding-right: 12px;
	}

	i.pct-chevron-up {
		display: none;
	}

	i.pct-chevron-down {
		display: inline-block;
		margin-right: 0;
	}
}

.acoes {
	.show {
		i.pct-chevron-down {
			display: none;
		}

		i.pct-chevron-up {
			display: inline-block;
			margin-right: 0;
		}
	}
}

#content-dates {
	width: 250px;
	padding: 20px 12px;

	label {
		font-size: 16px;
		font-weight: 600;
		line-height: 22px;
		text-align: left;
		color: $cinza05;
	}

	::ng-deep.width-datepicker,
	::ng-deep.form-group.edit-datepicker .wrapper {
		width: 100%;
	}

	::ng-deep.form-group.edit-datepicker {
		margin-bottom: 10px;
	}

	div {
		button {
			margin-top: 15px;
		}

		text-align: center;
	}
}

::ng-deep pacto-relatorio.ranking-profs {
	.filter-wrapper {
		display: none;
	}
}

.filtros-ranking {
	margin-right: 10px;
	margin-left: 10px;
	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		padding-bottom: 33px;
	}
}

::ng-deep {
	pacto-share-button {
		.btn.pacto-primary {
			margin-left: 10px !important;
		}
	}
}
