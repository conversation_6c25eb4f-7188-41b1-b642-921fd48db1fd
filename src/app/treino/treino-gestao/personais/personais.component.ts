import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";

@Component({
	selector: "pacto-personais",
	templateUrl: "./personais.component.html",
	styleUrls: ["./personais.component.scss"],
})
export class PersonaisComponent implements OnInit {
	urlPersonais;

	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.urlPersonais =
			`${this.urlBase}` +
			`/paginaPersonalExterna/${this.sessionService.empresaId}/${this.sessionService.apiToken}`;
		this.cd.detectChanges();
	}

	get urlBase(): string {
		return this.discoveryService.getUrlMap().urlTreinoPersonal
			? this.discoveryService.getUrlMap().urlTreinoPersonal
			: "https://zw90.pactosolucoes.com.br:9090/GestaoPersonal";
	}
}
