import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import {
	PactoDataGridConfig,
	DataFiltro,
	TraducoesXinglingComponent,
} from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-indicador-detalhes-modal",
	templateUrl: "./aviso-medico-detalhes-modal.component.html",
	styleUrls: ["./aviso-medico-detalhes-modal.component.scss"],
})
export class AvisoMedicoDetalhesModalComponent implements OnInit {
	@ViewChild("traducaoColunas", { static: true })
	traducaoColunas: TraducoesXinglingComponent;
	@ViewChild("celulaDataTerminoContrato", { static: true })
	celulaDataTerminoContrato;
	@ViewChild("celulaDataTerminoProgVigente", { static: true })
	celulaDataTerminoProgVigente;

	ready = false;
	table: PactoDataGridConfig;
	baseFilters: DataFiltro;

	constructor(
		private openModal: NgbActiveModal,
		private rest: RestService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		setTimeout(() => {
			this.configTable();
			this.ready = true;
			this.cd.detectChanges();
		});
	}

	setBaseFilters(filters) {
		this.baseFilters = filters;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				"professores/alunos-aviso-medico/alunos"
			),
			pagination: false,
			rowClick: false,
			showSimpleTotalCount: true,
			columns: [
				{
					nome: "matricula",
					titulo: this.traducaoColunas.getLabel("matriculaLabel"),
					visible: true,
					defaultVisible: true,
				},
				{
					nome: "nome",
					titulo: this.traducaoColunas.getLabel("nomeLabel"),
					visible: true,
					defaultVisible: true,
				},
				{
					nome: "situacao",
					titulo: this.traducaoColunas.getLabel("situacaoLabel"),
					visible: true,
					defaultVisible: true,
				},
				{
					nome: "terminoContrato",
					titulo: this.traducaoColunas.getLabel("terminoContratoLabel"),
					visible: true,
					celula: this.celulaDataTerminoContrato,
					defaultVisible: true,
				},
				{
					nome: "terminoProgramaVigente",
					titulo: this.traducaoColunas.getLabel("terminoProgramaVigenteLabel"),
					visible: true,
					celula: this.celulaDataTerminoProgVigente,
					defaultVisible: true,
				},
				{
					nome: "avisoMedico",
					titulo: this.traducaoColunas.getLabel("avisoMedicoLabel"),
					visible: true,
					defaultVisible: true,
				},
			],
		});
	}

	dismiss() {
		this.openModal.dismiss();
	}
}
