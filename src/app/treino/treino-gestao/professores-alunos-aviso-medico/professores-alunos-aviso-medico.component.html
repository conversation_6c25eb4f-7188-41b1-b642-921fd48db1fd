<!-- TABLE LABELS -->
<ng-template
	#tableTitulo
	i18n="@@relatorio-professores-alunos-aviso-medico:titulo">
	Alunos com aviso médico por professor
</ng-template>
<ng-template #celulaProfessor let-item="item">
	{{ item.professor?.nome }}
</ng-template>

<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#relatorio
			(cellClick)="cellClickHandler($event)"
			*ngIf="ready"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableTitle]="tableTitulo"
			[table]="table"
			telaId="professoresAlunosAvisoMedico"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<ng-template
	#professorLabel
	i18n="@@relatorio-professores-alunos-aviso-medico:professores:filtro">
	Professores
</ng-template>

<pacto-traducoes-xingling #traducaoColunas>
	<span
		i18n="@@relatorio-professores-alunos-aviso-medico:nome:coluna"
		xingling="nomeTitulo">
		Nome
	</span>
	<span
		i18n="@@relatorio-professores-alunos-aviso-medico:alunos:coluna"
		xingling="qtdAlunosMsg">
		Aluno(s)
	</span>
</pacto-traducoes-xingling>
