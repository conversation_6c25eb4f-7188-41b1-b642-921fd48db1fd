import { ChangeDetector<PERSON>ef, Component, OnInit, ViewChild } from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	DataFiltro,
	GridFilterConfig,
	GridFilterType,
} from "ui-kit";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { AvisoMedicoDetalhesModalComponent } from "./aviso-medico-detalhes-modal/aviso-medico-detalhes-modal.component";
import { TreinoApiColaboradorService } from "treino-api";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService } from "@base-core/modal/modal.service";
import { TraducoesXinglingComponent } from "ui-kit";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-professores-alunos-aviso-medico",
	templateUrl: "./professores-alunos-aviso-medico.component.html",
	styleUrls: ["./professores-alunos-aviso-medico.component.scss"],
})
export class ProfessoresAlunosAvisoMedicoComponent implements OnInit {
	@ViewChild("traducaoColunas", { static: true })
	traducaoColunas: TraducoesXinglingComponent;
	// TABLE LABEL
	@ViewChild("celulaProfessor", { static: true }) celulaProfessor;
	@ViewChild("relatorio", { static: false }) relatorio: RelatorioComponent;
	@ViewChild("professorLabel", { static: true }) professorLabel;

	constructor(
		private rest: RestService,
		private modal: ModalService,
		private colaboradorService: TreinoApiColaboradorService,
		public sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;

	private professores: Array<any>;

	ngOnInit() {
		this.fetchFilterData().subscribe(() => {
			this.configTable();
			this.configFilters();
			this.ready = true;
			this.cd.detectChanges();
		});
	}

	cellClickHandler($event) {
		if ($event.column.nome !== "nome") {
			let titulo;
			if (typeof $event.row.professor === "undefined") {
				titulo = `Total - ${this.traducaoColunas.getLabel($event.column.nome)}`;
			} else {
				titulo = `${
					$event.row.professor.nome
				} - ${this.traducaoColunas.getLabel($event.column.nome)}`;
			}
			const filter = this.buildDetailModalFilter($event);
			const modalHandle = this.modal.openFullscreen(
				titulo,
				AvisoMedicoDetalhesModalComponent
			);
			modalHandle.componentInstance.setBaseFilters(filter);
		}
	}

	private buildDetailModalFilter($event) {
		const filter = this.relatorio.fetchFiltros();
		const detailsFilter: DataFiltro = {
			filters: {},
		};
		const targetKeys = ["professores"];
		if (filter.filters) {
			targetKeys.forEach((target) => {
				const value = filter.filters[target];
				if (value !== null && value !== undefined) {
					detailsFilter.filters[target] = value;
				}
			});
		}
		if (typeof $event.row.professor === "undefined") {
			if (typeof filter.filters.professoresIds !== "undefined") {
				detailsFilter.filters.professoresIds = filter.filters.professoresIds;
			}
		} else {
			detailsFilter.filters.professorId = $event.row.professor.id;
		}
		return detailsFilter;
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService.obterTodosColaboradores().pipe(
			map((data) => {
				data.content.forEach((professor: any) => {
					professor.value = professor.id;
					professor.label = professor.nome;
				});
				this.professores = data.content;
				return true;
			})
		);
		return professores$;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("professores/alunos-aviso-medico"),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/professoresAlunosAvisoMedico"
			),
			exportButton: false,
			totalRow: false,
			pagination: false,
			rowClick: false,
			columns: [
				{
					nome: "nome",
					titulo: this.traducaoColunas.getLabel("nomeTitulo"),
					ordenavel: false,
					celula: this.celulaProfessor,
					defaultVisible: true,
				},
				{
					nome: "qtdAlunosMsg",
					titulo: this.traducaoColunas.getLabel("qtdAlunosMsg"),
					ordenavel: true,
					defaultVisible: true,
					cellPointerCursor: true,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "professoresIds",
					label: this.professorLabel,
					type: GridFilterType.MANY,
					options: this.professores,
				},
			],
		};
	}
}
