import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { GridFilterConfig, GridFilterType, PactoDataGridConfig } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { TreinoApiColaboradorService } from "treino-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-execucoes-treino",
	templateUrl: "./execucoes-treino.component.html",
	styleUrls: ["./execucoes-treino.component.scss"],
})
export class ExecucoesTreinoComponent implements OnInit {
	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("columnMatricula", { static: true }) columnMatricula;
	@ViewChild("columnAluno", { static: true }) columnAluno;
	@ViewChild("columnProfessor", { static: true }) columnProfessor;
	@ViewChild("columnExecucoes", { static: true }) columnExecucoes;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("professorLabel", { static: true }) professorLabel;
	@ViewChild("origemExecucaoLabel", { static: true }) origemExecucaoLabel;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;
	private professores: Array<any>;

	constructor(
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private cd: ChangeDetectorRef,
		public sessionService: SessionService
	) {}

	ngOnInit() {
		this.fetchFilterData().subscribe(() => {
			this.configTable();
			this.configFilters();
			this.ready = true;
			this.cd.detectChanges();
		});
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService.obterTodosColaboradores().pipe(
			map((data) => {
				this.professores = [];
				this.professores.push({ value: 0, label: "SEM PROFESSOR" });
				data.content.forEach((professor: any) => {
					this.professores.push({ value: professor.id, label: professor.nome });
				});
				return true;
			})
		);
		return professores$;
	}

	private getOrigemExecucaoFilter() {
		const options = [];
		options.push(
			{ value: "ACOMP_PROFESSOR", label: "Acompanhamento Professor" },
			{ value: "SMARTPHONE", label: "App" },
			{ value: "RETIRA_FICHA", label: "Retira Ficha" }
		);
		return options;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("gestao/execucoes-treino"),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/execucoesdetreino"
			),
			quickSearch: true,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "matricula",
				},
				{
					nome: "aluno",
					titulo: this.columnAluno,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "aluno",
				},
				{
					nome: "professor",
					titulo: this.columnProfessor,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "professor",
				},
				{
					nome: "execucoes",
					titulo: this.columnExecucoes,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "execucoes",
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dataInicio",
					label: this.dataInicioLabel,
					type: GridFilterType.DATE_POINT,
					initialValue: new Date(
						new Date().setDate(new Date().getDate() - 30)
					).valueOf(),
				},
				{
					name: "dataFim",
					label: this.dataFimLabel,
					type: GridFilterType.DATE_POINT,
					initialValue: new Date().valueOf(),
				},
				{
					name: "professoresIds",
					label: this.professorLabel,
					type: GridFilterType.MANY,
					options: this.professores,
				},
				{
					name: "origemExecucao",
					label: this.origemExecucaoLabel,
					type: GridFilterType.MANY,
					options: this.getOrigemExecucaoFilter(),
				},
			],
		};
	}
}
