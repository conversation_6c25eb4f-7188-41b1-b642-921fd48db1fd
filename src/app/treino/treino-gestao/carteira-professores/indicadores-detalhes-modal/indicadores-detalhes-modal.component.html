<ng-template
	#matriculaLabel
	i18n="@@relatorio-carteira-professores:matricula:coluna">
	Matricula
</ng-template>
<ng-template #nomeLabel i18n="@@relatorio-carteira-professores:nome:coluna">
	Nome
</ng-template>
<ng-template
	#situacaoLabel
	i18n="@@relatorio-carteira-professores:situacao:coluna">
	Situação
</ng-template>
<ng-template
	#terminoProgramaVigenteLabel
	i18n="@@relatorio-carteira-professores:termino-programa-vigente:coluna">
	Término do programa Vigente
</ng-template>
<ng-template #celulaDataTerminoProgVigente let-item="item">
	{{
		item.terminoProgramaVigente
			? (item.terminoProgramaVigente | date : "shortDate")
			: "-"
	}}
</ng-template>

<pacto-relatorio
	*ngIf="ready"
	[baseFilter]="baseFilters"
	[tableTitle]="tituloCard"
	[table]="table"></pacto-relatorio>

<div class="modal-footer">
	<button
		(click)="dismiss()"
		class="btn btn-secondary"
		i18n="@@relatorio-carteira-professores:fechar">
		Fechar
	</button>
</div>
