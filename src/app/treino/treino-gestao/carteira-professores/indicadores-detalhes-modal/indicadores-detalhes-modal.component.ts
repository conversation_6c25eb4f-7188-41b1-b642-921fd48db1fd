import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { PactoDataGridConfig, DataFiltro } from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-indicadores-detalhes-modal",
	templateUrl: "./indicadores-detalhes-modal.component.html",
	styleUrls: ["./indicadores-detalhes-modal.component.scss"],
})
export class IndicadoresDetalhesModalComponent implements OnInit {
	@ViewChild("matriculaLabel", { static: true }) matriculaLabel;
	@ViewChild("nomeLabel", { static: true }) nomeLabel;
	@ViewChild("situacaoLabel", { static: true }) situacaoLabel;
	@ViewChild("terminoContratoLabel", { static: false }) terminoContratoLabel;
	@ViewChild("terminoProgramaVigenteLabel", { static: true })
	terminoProgramaVigenteLabel;
	@ViewChild("dataAcompanhamentoLabel", { static: true })
	dataAcompanhamentoLabel;
	@ViewChild("celulaDataTerminoContrato", { static: false })
	celulaDataTerminoContrato;
	@ViewChild("celulaDataTerminoProgVigente", { static: true })
	celulaDataTerminoProgVigente;

	ready = false;
	table: PactoDataGridConfig;
	baseFilters: DataFiltro;
	tituloCard: TemplateRef<any>;
	integracaoZW = false;

	constructor(
		private rest: RestService,
		private openModal: NgbActiveModal,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.configTable();
	}

	setBaseFiltersModal(filters, titleModal) {
		this.tituloCard = titleModal;
		this.baseFilters = filters;
		this.ready = true;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				"professores/indicadores-carteira-professores/alunos"
			),
			pagination: true,
			totalRow: false,
			showSimpleTotalCount: !this.integracaoZW,
			rowClick: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.matriculaLabel,
					visible: true,
				},
				{
					nome: "nome",
					titulo: this.nomeLabel,
					visible: true,
				},
				{
					nome: "situacao",
					titulo: this.situacaoLabel,
					visible: true,
				},
				{
					nome: "terminoProgramaVigente",
					titulo: this.terminoProgramaVigenteLabel,
					celula: this.celulaDataTerminoProgVigente,
					visible: true,
				},
			],
		});
	}

	dismiss() {
		console.log("#############");
		this.openModal.dismiss();
	}
}
