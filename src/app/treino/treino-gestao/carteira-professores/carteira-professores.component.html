<!-- TABLE LABELS -->
<pacto-traducoes-xingling #xingling>
	<ng-template
		i18n="@@relatorio-carteira-professores:titulo"
		xingling="tableTitulo">
		Indicadores da carteira dos professores
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:nome:coluna"
		xingling="nomeTitulo">
		Nome
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:com-treino:coluna"
		xingling="comTreinoTitulo">
		Com Treino
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:sem-treino:coluna"
		xingling="semTreinoTitulo">
		Sem Treino
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:vencidos:coluna"
		xingling="vencidosTitulo">
		Vencidos
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:prox-vencimento:coluna"
		xingling="proxVencimentoTitulo">
		Próx. Vencimento
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:avaliacao:coluna"
		xingling="avaliacaoTitulo">
		Avaliação
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:2-estrelas:colunas"
		xingling="estrelas2Titulo">
		2 Estrelas
	</ng-template>
	<ng-template let-item="item" xingling="celulaProfessor">
		{{ item.professor?.nome }}
	</ng-template>

	<!-- FILTER LABELS -->
	<ng-template
		i18n="@@relatorio-carteira-professores:professores:filtro"
		xingling="professorLabel">
		Professores
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:incluir-professores-inativos:filtro"
		xingling="professor2Label">
		Incluir Professores Inativos?
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:status-alunos:filtro"
		xingling="statusLabel">
		Status dos alunos hoje
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:situacao-programa:filtro"
		xingling="situacaoProgramaLabel">
		Situação do programa
	</ng-template>
	<ng-template
		i18n="@@relatorio-carteira-professores:situacao-contrato:filtro"
		xingling="situacaoContratoLabel">
		Situação de contrato
	</ng-template>
</pacto-traducoes-xingling>

<!--    FILTER TRANSLATOR XINGLING-->
<pacto-traducoes-xingling #xinglingFilter>
	<!--        STATUS DO ALUNO-->
	<span xingling="ativo">Ativo</span>
	<span xingling="visitante">Visitante</span>
	<span xingling="inativo">Inativo</span>
	<span xingling="atestado">Atestado</span>
	<span xingling="cancelado">Cancelado</span>
	<span xingling="carencia">Carencia</span>
	<span xingling="desistente">Desistente</span>
	<span xingling="trancado">Trancado</span>
	<span xingling="vencido">Vencido</span>
	<span xingling="outros">Outros</span>

	<!-- SITUAÇÃO DO PROGRAMA TRANSLATOR -->
	<span
		i18n="@@relatorio-carteira-professores:novos:situacao-programa"
		xingling="novos">
		Novos
	</span>
	<span
		i18n="@@relatorio-carteira-professores:renovados:situacao-programa"
		xingling="renovados">
		Renovados
	</span>

	<!-- SITUAÇÃO DO CONTRATO TRANSLATOR -->
	<span
		i18n="@@relatorio-carteira-professores:novos:situacao-programa"
		xingling="matricula">
		Matrícula
	</span>
	<span
		i18n="@@relatorio-carteira-professores:renovados:situacao-programa"
		xingling="rematricula">
		Rematrícula
	</span>
	<span
		i18n="@@relatorio-carteira-professores:revisados:situacao-programa"
		xingling="renovacao">
		Renovação
	</span>
</pacto-traducoes-xingling>

<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#relatorio
			(cellClick)="cellClickHandler($event)"
			(filterConfigUpdate)="alterConfigHandler($event)"
			*ngIf="ready"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableTitle]="xingling.getTemplate('tableTitulo')"
			[table]="table"
			telaId="indicadoresCarteiradosProfessores"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #traducaoColunas let-traducaoColuna="traducaoColuna">
	<ng-container [ngSwitch]="traducaoColuna">
		<span
			*ngSwitchCase="'comTreino'"
			i18n="@@relatorio-carteira-professores:com-treino:coluna">
			Com Treino
		</span>
		<span
			*ngSwitchCase="'semTreino'"
			i18n="@@relatorio-carteira-professores:sem-treino:coluna">
			Sem Treino
		</span>
		<span
			*ngSwitchCase="'vencidos'"
			i18n="@@relatorio-carteira-professores:vencidos:coluna">
			Vencidos
		</span>
		<span
			*ngSwitchCase="'proxVencimento'"
			i18n="@@relatorio-carteira-professores:prox-vencimento:coluna">
			Próx. Vencimento
		</span>
		<span
			*ngSwitchCase="'avaliacao'"
			i18n="@@relatorio-carteira-professores:avaliacao:coluna">
			Avaliação
		</span>
		<span
			*ngSwitchCase="'estrelas2'"
			i18n="@@relatorio-carteira-professores:2-estrelas:colunas">
			2 Estrelas
		</span>
	</ng-container>
</ng-template>

<ng-template #titleModal>
	{{ paramTitleModal.nomeProfessor }} -
	<ng-container
		*ngTemplateOutlet="
			traducaoColunas;
			context: { traducaoColuna: paramTitleModal.nomeColuna }
		"></ng-container>
</ng-template>
