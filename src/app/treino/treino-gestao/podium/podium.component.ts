import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { ConfeteComponent } from "../confete/confete.component";
import { LoaderService, RelatorioComponent } from "ui-kit";
import { TreinoApiProfessorService } from "treino-api";
import { DataFiltro } from "../../../cobranca/components/relatorio-cobranca/data-grid.model";

@Component({
	selector: "pacto-podium",
	templateUrl: "./podium.component.html",
	styleUrls: ["./podium.component.scss"],
})
export class PodiumComponent implements OnInit {
	carregando;
	primeiro;
	segundo;
	terceiro;
	@ViewChild("confete", { static: true }) confete: ConfeteComponent;
	@Output() clickLugar: EventEmitter<any> = new EventEmitter();
	@Input() inicio;
	@Input() fim;

	constructor(
		private cd: ChangeDetectorRef,
		private loaderService: LoaderService,
		private professorService: TreinoApiProfessorService
	) {}

	ngOnInit() {
		this.carregando = true;
	}

	click(posicao) {
		this.clickLugar.emit(posicao);
	}

	clear() {
		this.primeiro = null;
		this.segundo = null;
		this.terceiro = null;
		this.hide("terceiro-lugar");
		this.hide("segundo-lugar");
		this.hide("primeiro-lugar");
	}

	reload(filtros?: DataFiltro) {
		this.carregando = true;
		const params: any = {};
		if (filtros) {
			params.filtros = JSON.stringify(filtros.filters);
		} else {
			if (filtros === null || filtros === undefined) {
				filtros = { filters: {} };
			}
			filtros.filters["situacoes"] = ["ativo"];
			filtros.filters["personal"] = ["naoincluir"];
		}
		this.professorService.podium(this.inicio, this.fim, filtros).subscribe(
			(dados) => {
				this.loaderService.stopForce();
				this.primeiro = dados.content[0] ? dados.content[0] : null;
				this.segundo = dados.content[1] ? dados.content[1] : null;
				this.terceiro = dados.content[2] ? dados.content[2] : null;
				if (this.primeiro) {
					this.show("terceiro-lugar", 0);
					this.show("segundo-lugar", 1000);
					this.show("primeiro-lugar", 2000);
				}
				this.loaderService.hide();
				this.cd.detectChanges();
			},
			(error) => {},
			() => {
				this.carregando = false;
				this.cd.detectChanges();
			}
		);
	}

	get isVazio(): boolean {
		return !(this.primeiro || this.segundo || this.terceiro);
	}

	show(id, time) {
		setTimeout(() => {
			const lugar = document.getElementById(id);
			if (lugar) {
				lugar.classList.add("ready");
				setTimeout(() => {
					this.confete.surprise();
					this.cd.detectChanges();
				}, 500);
			}
		}, time);
	}

	hide(id) {
		const lugar = document.getElementById(id);
		if (lugar) {
			lugar.classList.remove("ready");
		}
	}
}
