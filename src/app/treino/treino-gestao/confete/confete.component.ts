import { Component, ElementRef, OnInit, Renderer2 } from "@angular/core";
import * as confetti from "canvas-confetti";

@Component({
	selector: "pacto-confete",
	templateUrl: "./confete.component.html",
	styleUrls: ["./confete.component.scss"],
})
export class ConfeteComponent implements OnInit {
	count = 200;
	defaults = {
		origin: { y: 0.7 },
	};

	constructor(private renderer2: Renderer2, private elementRef: ElementRef) {}

	ngOnInit() {}

	public surprise(): void {
		const canvas = this.renderer2.createElement("canvas");
		this.renderer2.appendChild(this.elementRef.nativeElement, canvas);
		this.fire(canvas);
		setTimeout(() => {
			this.renderer2.removeChild(this.elementRef.nativeElement, canvas);
		}, 3000);
	}

	fire(canvas) {
		const myConfetti = confetti.create(canvas, {
			particleCount: 50,
			spread: 55,
			origin: { y: 1 },
			resize: true,
		});
		myConfetti();
	}
}
