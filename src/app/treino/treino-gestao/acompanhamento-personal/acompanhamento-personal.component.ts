import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-acompanhamento-personal",
	templateUrl: "./acompanhamento-personal.component.html",
	styleUrls: ["./acompanhamento-personal.component.scss"],
})
export class AcompanhamentoPersonalComponent implements OnInit {
	urlAcompanhamento;

	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.urlAcompanhamento =
			`${this.urlBase}/paginaacompanharpersonais/` +
			`${this.sessionService.empresaId}/${this.sessionService.apiToken}`;
		this.cd.detectChanges();
	}

	get urlBase(): string {
		return this.discoveryService.getUrlMap().urlTreinoPersonal
			? this.discoveryService.getUrlMap().urlTreinoPersonal
			: "https://zw90.pactosolucoes.com.br:9090/GestaoPersonal";
	}
}
