import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-colaboradores-personal",
	templateUrl: "./colaboradores-personal.component.html",
	styleUrls: ["./colaboradores-personal.component.scss"],
})
export class ColaboradoresPersonalComponent implements OnInit {
	urlColaboradores;

	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.urlColaboradores =
			`${this.urlBase}/paginacolaboradores/` +
			`${this.sessionService.empresaId}/${this.sessionService.apiToken}`;
		this.cd.detectChanges();
	}

	get urlBase(): string {
		return this.discoveryService.getUrlMap().urlTreinoPersonal
			? this.discoveryService.getUrlMap().urlTreinoPersonal
			: "https://zw90.pactosolucoes.com.br:9090/GestaoPersonal";
	}
}
