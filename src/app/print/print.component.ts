import { Component, Injectable, OnInit } from "@angular/core";

@Injectable({
	providedIn: "root",
})
export class PrintService {
	set htmlContent(htmlContent) {
		localStorage.setItem("html-contrato-content", htmlContent);
	}

	get htmlContent() {
		return localStorage.getItem("html-contrato-content");
	}
}

@Component({
	selector: "pacto-print-by-html",
	templateUrl: "./print.component.html",
	styleUrls: ["./print.component.scss"],
})
export class PrintComponent implements OnInit {
	html: string;

	constructor(private printService: PrintService) {}

	ngOnInit() {
		this.html = this.printService.htmlContent;
		if (!this.html) {
			window.close();
			return;
		}
	}

	print() {
		window.print();
	}
}
