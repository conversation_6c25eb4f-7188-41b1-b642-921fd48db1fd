import { NgModule } from "@angular/core";
import { CommonModule, registerLocaleData } from "@angular/common";
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { RouterModule, Routes } from "@angular/router";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { HttpClientModule } from "@angular/common/http";
import localePt from "@angular/common/locales/pt";

import {
	SnotifyModule,
	SnotifyService,
	ToastDefaults,
	SnotifyPosition,
} from "ng-snotify";
import { MatDividerModule } from "@angular/material/divider";
import { OldUiKitModule } from "old-ui-kit";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { UiModule } from "ui-kit";

ToastDefaults.toast.timeout = 3000;
ToastDefaults.toast.position = SnotifyPosition.rightTop;

import { SolicitacoesComponent } from "canal-cliente/app/atendimento/components/solicitacoes/solicitacoes.component";
import { SolicitacoesResolvidasComponent } from "canal-cliente/app/atendimento/components/solicitacoes-resolvidas/solicitacoes-resolvidas.component";
import { SolicitacoesEmAbertoComponent } from "canal-cliente/app/atendimento/components/solicitacoes-em-aberto/solicitacoes-em-aberto.component";
import { DashboardComponent } from "canal-cliente/app/atendimento/components/dashboard/dashboard.component";
import { UltimasFaturasComponent } from "canal-cliente/app/faturas/components/ultimas-faturas/ultimas-faturas.component";
import { CadastroResponsaveisComponent } from "canal-cliente/app/responsaveis/components/cadastro-responsaveis/cadastro-responsaveis.component";
import { MeusDocumentosComponent } from "canal-cliente/app/documentos/components/meus-documentos/meus-documentos.component";
import { AssinarDocumentoComponent } from "canal-cliente/app/documentos/components/assinar-documento/assinar-documento.component";
import { IframeServicosComponent } from "canal-cliente/app/servicos/components/iframe-servicos/iframe-servicos.component";
import { LayoutEmbutidoComponent } from "canal-cliente/app/layout/components/layout-emputido/layout-embutido.component";
import { MenuLayoutEmbutidoComponent } from "canal-cliente/app/layout/components/menu-layout-embutido/menu-layout-embutido.component";
import { DadosEmpresaComponent } from "canal-cliente/app/meus-dados/components/dados-empresa/dados-empresa.component";
import { MeusPlanosComponent } from "canal-cliente/app/planos-sucesso/components/meus-planos/meus-planos.component";
import { LayoutConteudoComponent } from "canal-cliente/app/layout/components/layout-conteudo/layout-conteudo.component";
import { DetalhesPlanoComponent } from "canal-cliente/app/planos-sucesso/components/detalhes-plano/detalhes-plano.component";
import { DetalhesAcaoComponent } from "canal-cliente/app/planos-sucesso/components/detalhes-acao/detalhes-acao.component";
import { DetalhesNovaAcaoComponent } from "canal-cliente/app/planos-sucesso/components/detalhes-nova-acao/detalhes-nova-acao.component";
import { ModelosPlanoComponent } from "canal-cliente/app/planos-sucesso/components/modelos-plano/modelos-plano.component";
import { TabMenuPlanosSucessoComponent } from "canal-cliente/app/planos-sucesso/components/tab-menu-planos-sucesso/tab-menu-planos-sucesso.component";
import { DetalheModeloPlanoComponent } from "canal-cliente/app/planos-sucesso/components/detalhe-modelo-plano/detalhe-modelo-plano.component";
import { TodasAcoesComponent } from "canal-cliente/app/planos-sucesso/components/todas-acoes/todas-acoes.component";
import { SolicitacoesAguardandoAprovacaoComponent } from "canal-cliente/app/atendimento/components/solicitacoes-aguardando-aprovacao/solicitacoes-aguardando-aprovacao.component";
import { PlanosSucessoComponent } from "canal-cliente/app/planos-sucesso/components/planos-sucesso/planos-sucesso.component";
import { PlanosConcluidosComponent } from "canal-cliente/app/planos-sucesso/components/planos-concluidos/planos-concluidos.component";
import { AberturaChamadoModalComponent } from "canal-cliente/app/atendimento/components/abertura-chamado-modal/abertura-chamado-modal.component";
import { AdicionarAcaoModalComponent } from "canal-cliente/app/atendimento/components/adicionar-acao-modal/adicionar-acao-modal.component";
import { LoadComponent } from "canal-cliente/app/load/load.component";
import { AppComponent } from "canal-cliente/app/app.component";
import { ConfirmModalComponent } from "canal-cliente/app/shared/components/confirm-modal/confirm-modal.component";
import { ModalWrapperComponent } from "canal-cliente/app/shared/components/modal-wrapper/modal-wrapper.component";
import { FooterComponent } from "canal-cliente/app/shared/components/footer/footer.component";
import { DateFromNowPipe } from "canal-cliente/app/shared/date-short.pipe";

registerLocaleData(localePt, "pt");

const routes: Routes = [
	{
		path: "adicionarConta",
		component: LoadComponent,
	},
	{
		path: "cc",
		component: LayoutEmbutidoComponent,
		children: [
			{
				path: "atendimento",
				component: DashboardComponent,
			},
			{
				path: "atendimento/solicitacoes-em-aberto",
				component: SolicitacoesEmAbertoComponent,
			},
			{
				path: "atendimento/solicitacoes-resolvidas",
				component: SolicitacoesAguardandoAprovacaoComponent,
			},
			{
				path: "atendimento/solicitacoes-concluidas",
				component: SolicitacoesResolvidasComponent,
			},
			{
				path: "faturas",
				component: UltimasFaturasComponent,
			},
			{
				path: "responsaveis",
				component: CadastroResponsaveisComponent,
			},
			{
				path: "meus-documentos",
				component: MeusDocumentosComponent,
			},
			{
				path: "meus-documentos/assinar-documento/:id",
				component: AssinarDocumentoComponent,
			},
			{
				path: "servicos",
				component: IframeServicosComponent,
			},
			{
				path: "meus-dados",
				component: DadosEmpresaComponent,
			},
			{
				path: "planos-sucesso",
				component: MeusPlanosComponent,
			},
			{
				path: "modelos-plano-sucesso",
				component: ModelosPlanoComponent,
			},
			{
				path: "planos-sucesso-concluidos",
				component: PlanosConcluidosComponent,
			},
			{
				path: "todas-acoes",
				component: TodasAcoesComponent,
			},
			{
				path: "modelos-plano-sucesso/:id",
				component: DetalheModeloPlanoComponent,
			},
			{
				path: "planos-sucesso/:id",
				component: DetalhesPlanoComponent,
			},
			{
				path: "planos-sucesso/:plano_id/acao/:acao_id",
				component: DetalhesAcaoComponent,
			},
			{
				path: "planos-sucesso/:plano_id/nova-acao",
				component: DetalhesNovaAcaoComponent,
			},
			{
				path: "store",
				loadChildren: () =>
					import("canal-cliente/app/marketplace/marketplace.module").then(
						(m) => m.MarketplaceModule
					),
			},
		],
	},
];

@NgModule({
	imports: [
		CommonModule,
		FormsModule,
		RouterModule.forChild(routes),
		ReactiveFormsModule,
		HttpClientModule,
		NgbModule,
		SnotifyModule,
		OldUiKitModule,
		UiModule,
		MatDividerModule,
	],
	declarations: [
		ConfirmModalComponent,
		ModalWrapperComponent,
		LoadComponent,
		AppComponent,
		SolicitacoesComponent,
		SolicitacoesResolvidasComponent,
		SolicitacoesEmAbertoComponent,
		DashboardComponent,
		AssinarDocumentoComponent,
		UltimasFaturasComponent,
		CadastroResponsaveisComponent,
		MeusDocumentosComponent,
		IframeServicosComponent,
		LayoutEmbutidoComponent,
		MenuLayoutEmbutidoComponent,
		FooterComponent,
		DadosEmpresaComponent,
		MeusPlanosComponent,
		LayoutConteudoComponent,
		DetalhesPlanoComponent,
		DetalhesAcaoComponent,
		DetalhesNovaAcaoComponent,
		ModelosPlanoComponent,
		TabMenuPlanosSucessoComponent,
		DetalheModeloPlanoComponent,
		TodasAcoesComponent,
		SolicitacoesAguardandoAprovacaoComponent,
		PlanosSucessoComponent,
		PlanosConcluidosComponent,
		AberturaChamadoModalComponent,
		AdicionarAcaoModalComponent,
		DateFromNowPipe,
	],
	providers: [
		SnotifyService,
		{ provide: "SnotifyToastConfig", useValue: ToastDefaults },
	],
	entryComponents: [
		ConfirmModalComponent,
		AberturaChamadoModalComponent,
		ModalWrapperComponent,
		AdicionarAcaoModalComponent,
	],
	bootstrap: [AppComponent],
})
export class CanalClienteModule {}
