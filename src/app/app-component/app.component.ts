import { Component, OnInit } from "@angular/core";
import { NgbDatepickerConfig } from "@ng-bootstrap/ng-bootstrap";
import { ScriptLoaderService } from "sdk";

@Component({
	selector: "pacto-root",
	templateUrl: "./app.component.html",
})
export class AppComponent implements OnInit {
	title = "app";

	constructor(
		private ngbDatepickerConfig: NgbDatepickerConfig,
		private scriptLoaderService: ScriptLoaderService
	) {
		this.ngbDatepickerConfig.minDate = { year: 1958, month: 1, day: 1 };
	}

	async ngOnInit() {
		await this.scriptLoaderService.loadScripts();
	}
}
