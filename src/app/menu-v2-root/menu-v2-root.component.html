<pacto-menu-layout *ngIf="isMenuV2()"></pacto-menu-layout>

<pacto-cat-plataforma-menu-v2-router
	(changeUnit)="changeUnitHandler()"
	(logout)="logoutHandler()"
	*ngIf="!isMenuV2()"></pacto-cat-plataforma-menu-v2-router>

<!-- SELETOR DE EMPRESAS -->
<ng-template #mudarEmpresa>
	<div class="change-empesa-wrapper">
		<div class="title" i18n="@@layout:trocar-empresa">Trocar Empresa</div>
		<input [formControl]="fc" type="text" />
		<div class="empresas">
			<div
				(click)="trocaClickHandler(troca)"
				*ngFor="let troca of trocaEmpresaFiltradas"
				class="empresa">
				{{ troca?.nomeApresentar }}
				<span *ngIf="troca?.atual">
					<span
						class="empresaLogada"
						i18n="@@layout:trocar-empresa:empresa-logada">
						Empresa logada
					</span>
				</span>
			</div>
		</div>
		<div class="empresa-footer">
			<pacto-cat-button
				(click)="fecharHandler()"
				[type]="'OUTLINE'"
				i18n-label="@@layout:trocar-empresa:fechar"
				label="Fechar"></pacto-cat-button>
		</div>
	</div>
</ng-template>
