@import "src/assets/scss/pacto/plataforma-import.scss";

.change-empesa-wrapper {
	padding: 30px;

	.title {
		@extend .type-h4;
		color: $pretoPri;
		margin-bottom: 20px;
	}

	.empresa-footer {
		margin-top: 15px;
		display: flex;
		flex-direction: row-reverse;
	}

	.empresas {
		margin-top: 15px;
		min-height: 150px;
		max-height: 300px;
		overflow-y: auto;
	}

	.empresa {
		@extend .type-h6;
		color: $pretoPri;
		line-height: 45px;
		padding-left: 10px;
		border-top: 1px solid $cinzaClaroPri;
		cursor: pointer;

		&:last-child {
			border-bottom: 1px solid $cinzaClaroPri;
		}

		&:hover {
			background-color: $cinza01;
		}
	}
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 30px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.1rem rgba($azulim<PERSON>ri, 0.5);
	}
}

.empresaLogada {
	color: #0380e3;
	margin-left: 15px;
	font-family: "Nunito Sans";
	font-style: normal;
	font-weight: 700;
	font-size: 14px;
	line-height: 16px;
}

@media (min-width: 576px) {
	::ng-deep .larguraModal .modal-dialog {
		max-width: 700px !important;
	}
}
