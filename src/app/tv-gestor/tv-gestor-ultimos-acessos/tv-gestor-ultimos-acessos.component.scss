@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	background-color: $branco;
	border-radius: 10px;

	&.loading {
		filter: blur(2px);
	}
}

.title-section {
	line-height: 74px;
	@extend .type-h5;
	color: $pretoPri;
	border-bottom: 1px solid $canetaBicPastel;
	padding-left: 30px;
}

.body-section {
	padding: 15px 30px 30px 30px;
}

.acesso {
	display: flex;
	border-bottom: 1px solid $canetaBicPastel;
	margin-bottom: 10px;
	padding-bottom: 5px;

	&:last-child {
		border-bottom: 0px;
	}
}

.info {
	margin-left: 15px;

	.aluno-nome {
		@extend .type-h6;
	}

	.matricula {
		@extend .type-caption;
		color: $cinza05;
	}

	.data-acesso {
		@extend .type-caption;
		color: $cinza05;
	}

	.data-acesso {
		@extend .type-caption;
		color: $cinza05;
	}

	.nome-modalidade {
		@extend .type-caption;
		color: $preto05;
	}
}
