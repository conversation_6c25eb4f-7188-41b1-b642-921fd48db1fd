<div class="title-section" i18n="@@tv-gestor:ultimos-acessos">
	Últimos Acessos
</div>

<div class="body-section">
	<div
		[maxHeight]="'calc(100vh - 512px)'"
		class="scroll-container"
		pactoCatSmoothScroll>
		<div *ngFor="let acesso of acessos" class="acesso">
			<pacto-cat-person-avatar
				[diameter]="80"
				[uri]="acesso.imageUri"></pacto-cat-person-avatar>
			<div class="info">
				<div class="aluno-nome">{{ acesso.nomeAluno }}</div>
				<div class="matricula">{{ acesso.matriculaAluno }}</div>
				<div class="data-acesso">
					{{ acesso.dataHoraAcesso | date : "shortDate" }} às
					{{ acesso.dataHoraAcesso | date : "HH:mm" }}
				</div>
				<div class="nome-modalidade">{{ acesso.nomeTurma }}</div>
			</div>
		</div>
	</div>
	<!-- End scroll container -->
</div>
