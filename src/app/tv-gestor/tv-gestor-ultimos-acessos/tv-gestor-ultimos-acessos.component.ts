import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	HostBinding,
} from "@angular/core";
import { TvGestorUltimoAcesso } from "treino-api";

@Component({
	selector: "pacto-tv-gestor-ultimos-acessos",
	templateUrl: "./tv-gestor-ultimos-acessos.component.html",
	styleUrls: ["./tv-gestor-ultimos-acessos.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TvGestorUltimosAcessosComponent implements OnInit {
	@Input() acessos: TvGestorUltimoAcesso[] = [];
	@Input() loading;

	@HostBinding("class.loading") get _loading() {
		return this.loading;
	}

	constructor() {}

	ngOnInit() {}
}
