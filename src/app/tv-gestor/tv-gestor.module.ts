import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Route } from "@angular/router";
import { TvGestorComponent } from "./tv-gestor/tv-gestor.component";
import { TvGestorCardSemanaComponent } from "./tv-gestor-card-semana/tv-gestor-card-semana.component";
import { TvGestorUltimosAcessosComponent } from "./tv-gestor-ultimos-acessos/tv-gestor-ultimos-acessos.component";
import { TvGestorChartComponent } from "./tv-gestor-chart/tv-gestor-chart.component";
import { RelatorioComponent } from "ui-kit";

const routes: Route[] = [
	{
		path: "",
		component: TvGestorComponent,
	},
];

@NgModule({
	declarations: [
		TvGestorComponent,
		TvGestorCardSemanaComponent,
		TvGestorUltimosAcessosComponent,
		TvGestorChartComponent,
	],
	imports: [RouterModule.forChild(routes), BaseSharedModule, CommonModule],
	entryComponents: [RelatorioComponent],
})
export class TvGestorModule {}
