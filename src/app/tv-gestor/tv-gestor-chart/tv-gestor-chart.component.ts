import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	OnDestroy,
	ChangeDetectorRef,
	Input,
	OnChanges,
	HostBinding,
	ViewChild,
} from "@angular/core";
import { TvGestorBiPeriodo } from "treino-api";

import "apexcharts";
import * as moment from "moment";

@Component({
	selector: "pacto-tv-gestor-chart",
	templateUrl: "./tv-gestor-chart.component.html",
	styleUrls: ["./tv-gestor-chart.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TvGestorChartComponent implements OnInit, OnChanges, OnDestroy {
	@ViewChild("esperados", { static: true }) esperados;
	@ViewChild("compareceram", { static: true }) compareceram;
	@Input() dados: TvGestorBiPeriodo = {};
	@Input() loading;

	@HostBinding("class.loading") get _loading() {
		return this.loading;
	}

	chartInstance;
	series = [];
	labels = [];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnChanges() {
		this.buildChart();
	}

	private processData() {
		const esperados = {
			name: this.esperados.nativeElement.innerHTML,
			data: [],
		};
		const compareceram = {
			name: this.compareceram.nativeElement.innerHTML,
			data: [],
		};
		this.labels = [];
		for (const dia in this.dados) {
			if (this.dados.hasOwnProperty(dia)) {
				const data = this.dados[dia];
				this.labels.push(moment(dia, "YYYYMMDD").format("DD/MM"));
				esperados.data.push(data.esperado);
				compareceram.data.push(data.compareceu);
			}
		}
		this.series = [esperados, compareceram];
	}

	private getOptions() {
		const options = {
			chart: {
				height: null,
				type: "line",
				fontFamily: "'Nunito Sans', sans-serif",
				fontSize: "19px",
				animations: {
					enabled: false,
				},
				toolbar: { show: false },
			},
			grid: {
				show: true,
				borderColor: "#383B3E",
				yaxis: {
					lines: {
						show: true,
					},
				},
				xaxis: {
					lines: {
						show: true,
					},
				},
			},
			stroke: {
				show: true,
				width: 2,
			},
			markers: {
				size: 6,
			},
			colors: ["#FF4B2B", "#6F747B"],
			plotOptions: {
				bar: {
					horizontal: false,
					columnWidth: `${25 * this.series.length}%`,
					endingShape: "rounded",
				},
			},
			dataLabels: { enabled: false },
			series: this.series,
			xaxis: {
				categories: this.labels,
				labels: {
					style: {
						colors: new Array(this.labels.length).fill("#ffffff"),
						fontSize: "12px",
					},
				},
			},
			legend: { show: false },
			yaxis: {
				labels: {
					style: {
						fontSize: "14px",
						color: "#ffffff",
					},
				},
				title: {
					text: "Presenças",
					style: {
						color: "#ffffff",
						fontSize: "16px",
					},
				},
			},
			fill: { opacity: 1 },
		};
		options.chart.height = 205;
		return options;
	}

	ngOnDestroy() {
		this.chartInstance.destroy();
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
		this.processData();
		this.chartInstance = new ApexCharts(
			document.querySelector(`#gestor-chart`),
			this.getOptions()
		);
		this.chartInstance.render();
		this.cd.detectChanges();
	}
}
