import {
	AfterViewInit,
	ChangeDetectionStrategy,
	Component,
	HostBinding,
	Input,
	OnChanges,
	OnInit,
	ViewChild,
} from "@angular/core";
import { TvGestorBiSemana } from "treino-api";
import * as moment from "moment";
import {
	CatSmoothScrollDirective,
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-tv-gestor-card-semana",
	templateUrl: "./tv-gestor-card-semana.component.html",
	styleUrls: ["./tv-gestor-card-semana.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TvGestorCardSemanaComponent
	implements OnInit, AfterViewInit, OnChanges
{
	@ViewChild("colunasDetalhesAlunos", { static: true })
	colunasDetalhesAlunos: TraducoesXinglingComponent;
	@ViewChild("nomeModalTitulo", { static: true })
	nomeModalTitulo: TraducoesXinglingComponent;
	@ViewChild(CatSmoothScrollDirective, { static: true }) scrollDirective;
	@Input() data: TvGestorBiSemana = {};
	@Input() loading;

	@HostBinding("class.loading") get _loading() {
		return this.loading;
	}

	constructor(
		private modalService: ModalService,
		private rest: RestService,
		private localStorageSession: LocalStorageSessionService,
		private router: Router,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		if (!this.sessionService.loggedUser) {
			const urlParam: any = this.localStorageSession.getLocalStorageParams();
			urlParam.redirect = "/tv-gestor";
			this.router.navigate(["adicionarConta"], {
				queryParams: urlParam,
			});
		}

		setTimeout(() => {
			this.scrollToCurrentHour();
		}, 500);
	}

	ngAfterViewInit() {}

	ngOnChanges() {
		this.scrollToCurrentHour();
	}

	private scrollToCurrentHour() {
		const currentHour = moment().hour();
		const position = currentHour * 64;
		if (this.scrollDirective && this.scrollDirective.scroll) {
			this.scrollDirective.scroll.scrollTo(0, position, 600);
		}
	}

	isCurrentHour(hour: number) {
		const now = moment();
		return now.hour() === hour;
	}

	isCurrentDay(day: number) {
		const now = moment();
		const rowDay = moment(day);
		const dayMatch = now.day() === rowDay.day();
		const monthMatch = now.month() === rowDay.month();
		const yearMatch = now.year() === rowDay.year();
		return dayMatch && monthMatch && yearMatch;
	}

	getHourLabel(hour: number) {
		if (hour >= 10) {
			return `${hour}:00`;
		} else {
			return `0${hour}:00`;
		}
	}

	get diasAsTimestamp() {
		const result = [];
		if (this.data) {
			for (const data in this.data) {
				if (this.data.hasOwnProperty(data)) {
					const day = moment(data, "YYYYMMDD");
					result.push(day.valueOf());
				}
			}
		}
		return result;
	}

	get hours() {
		const result = [];
		for (let i = 0; i <= 23; i++) {
			result.push(i);
		}
		return result;
	}

	getCellData(day: number, hour: number): any {
		const dia = moment(day).format("YYYYMMDD");
		if (this.data[dia] && this.data[dia][hour]) {
			return this.data[dia][hour];
		} else {
			return null;
		}
	}

	detailAlunos(day: number, hour: number) {
		const modal = this.modalService.open(
			this.nomeModalTitulo.getLabel("tituloModal") +
				" " +
				moment(day).format("DD/MM"),
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		const relatorio: RelatorioComponent = modal.componentInstance;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.rest.buildFullUrl(`tv-gestor/alunos`),
			quickSearch: true,
			rowClick: false,
			columns: [
				{
					nome: "nomeAluno",
					titulo: this.colunasDetalhesAlunos.getLabel("nomeAluno"),
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					defaultVisible: true,
					campo: "nomeAluno",
				},
				{
					nome: "nomeTurma",
					titulo: this.colunasDetalhesAlunos.getLabel("nomeTurma"),
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					defaultVisible: true,
					campo: "nomeTurma",
				},
				{
					nome: "dataHoraInicioTurma",
					titulo: this.colunasDetalhesAlunos.getLabel("horarioInicio"),
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					defaultVisible: true,
					campo: "dataHoraInicioTurma",
					valueTransform: (item) =>
						item ? moment(item, "YYYYMMDDHHmm").format("HH:mm") : "-",
				},
				{
					nome: "dataHoraFimTurma",
					titulo: this.colunasDetalhesAlunos.getLabel("horarioFim"),
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					defaultVisible: true,
					campo: "dataHoraFimTurma",
					valueTransform: (item) =>
						item ? moment(item, "YYYYMMDDHHmm").format("HH:mm") : "-",
				},
				{
					nome: "dataHoraAcesso",
					titulo: this.colunasDetalhesAlunos.getLabel("acesso"),
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					defaultVisible: true,
					campo: "dataHoraAcesso",
					valueTransform: (item) =>
						item ? moment(item, "YYYYMMDDHHmm").format("HH:mm") : "-",
				},
			],
		};
		relatorio.table = new PactoDataGridConfig(config);
		relatorio.baseFilter = {
			filters: {
				data: day,
				hora: hour,
			},
		};
	}
}
