<div class="title-section" i18n="@@tv-gestor:alunos-esperados">
	<PERSON><PERSON><PERSON>
</div>

<div class="body-section">
	<div class="days-row">
		<div
			*ngFor="let dia of diasAsTimestamp"
			[ngClass]="{ current: isCurrentDay(dia) }"
			class="day">
			<div class="dia-semana-label">{{ dia | date : "EEEE" }}</div>
			<div class="dia-semana-total"></div>
		</div>
	</div>

	<div
		[maxHeight]="'calc(100vh - 589px)'"
		class="scroll-container"
		pactoCatSmoothScroll>
		<div *ngFor="let hour of hours" class="grid-hour-row">
			<div class="hour-label">
				<span
					*ngIf="isCurrentHour(hour)"
					class="current"
					i18n="@@tv-gestor:agora">
					Agora
				</span>
				<span *ngIf="!isCurrentHour(hour)">{{ getHourLabel(hour) }}</span>
			</div>

			<div
				(click)="detailAlunos(dia, hour)"
				*ngFor="let dia of diasAsTimestamp"
				[ngClass]="{
					'current-day': isCurrentDay(dia),
					'current-hour': isCurrentHour(hour)
				}"
				class="grid-cell">
				<span *ngIf="getCellData(dia, hour)" title="Compareceu / Esperado">
					{{ getCellData(dia, hour).compareceu }} /
					{{ getCellData(dia, hour).esperado }}
				</span>
			</div>
		</div>
	</div>
	<!-- End scroll container -->
</div>

<pacto-traducoes-xingling #colunasDetalhesAlunos>
	<span xingling="nomeAluno">Nome do aluno</span>
	<span xingling="nomeTurma">Turma</span>
	<span xingling="horarioInicio">Horário Inicio</span>
	<span xingling="horarioFim">Horário Fim</span>
	<span i18n="@@tv-gestor:acesso" xingling="acesso">Acesso</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #nomeModalTitulo>
	<span i18n="@@tv-gestor:modal:titulo" xingling="tituloModal">
		Alunos Esperados do dia
	</span>
</pacto-traducoes-xingling>
