import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	OnDestroy,
} from "@angular/core";
import {
	TvGestorBiSemana,
	TvGestorUltimoAcesso,
	TvGestorBiPeriodo,
	TreinoApiTvGestorService,
} from "treino-api";

import * as moment from "moment";
import { Observable, Subscription, zip } from "rxjs";
import { map, delay } from "rxjs/operators";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-tv-gestor",
	templateUrl: "./tv-gestor.component.html",
	styleUrls: ["./tv-gestor.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TvGestorComponent implements OnInit, OnDestroy {
	private clockHandle;

	DATA_UPDATE_DELAY = 5 * 60; // seconds
	dataFetchHandle;
	dadosSemana: TvGestorBiSemana;
	ultimosAcessos: TvGestorUltimoAcesso[] = [];
	dadosPeriodo: TvGestorBiPeriodo = {};
	loading = true;

	constructor(
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private tvGestorService: TreinoApiTvGestorService
	) {}

	ngOnInit() {
		this.setupClockUpdate();
		this.startDataFetchLoop();
	}

	ngOnDestroy() {
		this.clearClockUpdate();
		this.stopDataFetchLoop();
	}

	get integrado() {
		return this.session.integracaoZW;
	}

	private startDataFetchLoop() {
		this.fetchDados().subscribe();
		this.dataFetchHandle = setInterval(() => {
			this.fetchDados().subscribe();
		}, this.DATA_UPDATE_DELAY * 1000);
	}

	private stopDataFetchLoop() {
		clearInterval(this.clockHandle);
	}

	get hoje() {
		return moment().valueOf();
	}

	private setupClockUpdate() {
		this.clockHandle = setInterval(() => {
			this.cd.detectChanges();
		}, 60 * 1000);
	}

	private clearClockUpdate() {
		clearInterval(this.clockHandle);
	}

	get time() {
		const now = moment();
		return now.format("HH:mm");
	}

	private fetchDados(): Observable<any> {
		const semana$ = this.tvGestorService.obterBiSemanaAtual();
		const ultimos$ = this.tvGestorService.obterUltimosAcessos();
		const periodo$ = this.tvGestorService.obterBiPeriodo();
		this.loading = true;

		if (this.integrado) {
			return zip(semana$, ultimos$, periodo$).pipe(
				delay(1000),
				map((dados) => {
					this.dadosSemana = dados[0];
					this.ultimosAcessos = dados[1];
					this.dadosPeriodo = dados[2];
					this.ultimosAcessos.forEach((acesso) => {
						acesso.dataHoraAcesso = moment(
							acesso.dataHoraAcesso,
							"YYYYMMDDHHmm"
						)
							.valueOf()
							.toString();
					});
					this.loading = false;
					this.cd.detectChanges();
				})
			);
		} else {
			return zip(semana$, periodo$).pipe(
				map((dados) => {
					this.dadosSemana = dados[0];
					this.dadosPeriodo = dados[1];
					this.loading = false;
					this.cd.detectChanges();
				})
			);
		}
	}
}
