@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	width: 100%;
	height: 100vh;
	display: block;
}

.tv-gestor-header {
	height: 112px;
	background-color: $azulPacto05;
	display: flex;
}

.left-section {
	width: 400px;
	position: relative;

	.overlay {
		position: absolute;
		align-items: center;
		display: flex;
		bottom: 0px;
		right: 0px;
		left: 0px;
		top: 0px;
	}

	.logo {
		margin-left: 30px;
		cursor: pointer;
	}
}

.tv-gestor-title {
	@extend .type-h1;
	color: $branco;
	margin-left: 45px;
}

.center {
	flex-grow: 1;
	color: $branco;
	text-align: center;
	line-height: 112px;
	white-space: nowrap;
	@extend .type-hero;

	span {
		display: none;
	}

	.short {
		display: inline;
	}
}

@media (min-width: $plataforma-breakpoint-large) {
	.center {
		.short {
			display: none;
		}

		.large {
			display: inline;
		}
	}
}

.right-section {
	width: 400px;
	display: flex;
	flex-direction: row-reverse;
	padding-right: 30px;
	align-items: center;

	.pct {
		color: $branco;
		font-size: 40px;
	}

	.time {
		@extend .type-h2;
		line-height: 40px;
		padding-right: 15px;
		color: $branco;
	}
}

.tv-gestor-body {
	height: calc(100vh - 112px);
	flex-direction: column;
	display: flex;

	.upper {
		display: grid;
		grid-template-columns: 1fr;
		flex-grow: 1;
		padding: 30px;
		gap: 30px;

		&.integrado {
			grid-template-columns: 2.3fr 1fr;
		}
	}

	.lower {
		background-color: rgba($preto05, 0.8);
		flex-basis: 250px;
	}
}

.carregando-dados {
	left: 45%;
}
