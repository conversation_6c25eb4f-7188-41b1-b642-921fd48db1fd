<div class="tv-gestor-header">
	<!-- LEFT -->
	<div class="left-section">
		<img src="assets/images/tv-aula-header.png" />
		<div class="overlay">
			<img
				[routerLink]="['/treino']"
				class="logo"
				src="assets/images/logo-icon-pacto-branca.svg" />
			<span class="tv-gestor-title" i18n="@@tv-gestor:titulo">TV Gestor</span>
		</div>
	</div>

	<!-- CENTER -->
	<div class="center">
		<span class="large">{{ hoje | date : "longDate" }}</span>
		<span class="short">{{ hoje | date : "shortDate" }}</span>
	</div>

	<!-- RIGHT -->
	<div class="right-section">
		<i class="pct pct-clock"></i>
		<div class="time">{{ time }}</div>
	</div>
</div>

<div
	[ngStyle]="{
		'background-image': 'url(assets/images/tv-aula-bg.jpg)'
	}"
	class="tv-gestor-body">
	<div [ngClass]="{ integrado: integrado }" class="upper">
		<pacto-tv-gestor-card-semana
			[data]="dadosSemana"
			[loading]="loading"></pacto-tv-gestor-card-semana>
		<pacto-tv-gestor-ultimos-acessos
			*ngIf="integrado"
			[acessos]="ultimosAcessos"
			[loading]="loading"></pacto-tv-gestor-ultimos-acessos>
	</div>
	<div class="lower">
		<pacto-tv-gestor-chart
			[dados]="dadosPeriodo"
			[loading]="loading"></pacto-tv-gestor-chart>
	</div>
</div>

<div *ngIf="loading" class="carregando-dados">
	<div class="icon-loding">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="info-carregando-dados" i18n="@@tv-gestor:carregando-dados">
		Carregando os dados...
	</div>
</div>
