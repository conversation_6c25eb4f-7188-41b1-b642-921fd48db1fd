import { NgModule } from "@angular/core";
import { PreloadAllModules, RouterModule, Routes } from "@angular/router";

import { DragulaModule, DragulaService } from "ng2-dragula";

import { AvaliacaoEnabledGuard } from "@base-core/guards/avaliacaoEnabled.guard";
import { CrossfitEnabledGuard } from "@base-core/guards/crossfit-enabled.guard";
import { GraduacaoEnabledGuard } from "@base-core/guards/graduacao-enabled.guard";
import { LoggedinGuard } from "@base-core/guards/logged-in.guard";
import { TreinoEnabladGuard } from "@base-core/guards/treino-enablad.guard";
import { ModuleName } from "@base-core/modulo/modulo.model";
import { AddAccountControllerComponent } from "@base-shared/add-account-controller/add-account-controller.component";
import {
	MENU_I18N,
	MenuTranslation,
	PlataformaConfigService,
	PlataformaNavConfigService,
	PlataformaSearchService,
} from "ui-kit";
import { PlataformaLinkAdmComponent } from "./base/base-shared/layouts/plataforma-layout/plataforma-link-adm/plataforma-link-adm.component";
import { PlataformaV2ConfigService } from "./base/plataform-layout/plataforma-v2-config.service";
import { PlataformaV2NavConfigService } from "./base/plataform-layout/plataforma-v2-nav-config.service";
import { PlataformaV2SearchService } from "./base/plataform-layout/plataforma-v2-search.service";
import { MenuV2RootComponent } from "./menu-v2-root/menu-v2-root.component";

import { agendaPt } from "src/app/base/plataform-layout/i18n/pt/agenda";
import { avaliacaoPt } from "src/app/base/plataform-layout/i18n/pt/avaliacao";
import { configPt } from "src/app/base/plataform-layout/i18n/pt/config";
import { crossfitPt } from "src/app/base/plataform-layout/i18n/pt/crossfit";
import { graduacaoPt } from "src/app/base/plataform-layout/i18n/pt/graduacao";
import { modulesPt } from "src/app/base/plataform-layout/i18n/pt/modules";
import { pessoasPt } from "src/app/base/plataform-layout/i18n/pt/pessoas";
import { treinoPt } from "src/app/base/plataform-layout/i18n/pt/treino";

import { PactoLayoutSearchService } from "pacto-layout";
import { agendaEs } from "src/app/base/plataform-layout/i18n/es/agenda";
import { avaliacaoEs } from "src/app/base/plataform-layout/i18n/es/avaliacao";
import { configEs } from "src/app/base/plataform-layout/i18n/es/config";
import { crossfitEs } from "src/app/base/plataform-layout/i18n/es/crossfit";
import { graduacaoEs } from "src/app/base/plataform-layout/i18n/es/graduacao";
import { modulesEs } from "src/app/base/plataform-layout/i18n/es/modules";
import { pessoasEs } from "src/app/base/plataform-layout/i18n/es/pessoas";
import { treinoEs } from "src/app/base/plataform-layout/i18n/es/treino";
import { alunoPt } from "./base/plataform-layout/i18n/pt/aluno";
import { pactopayPt } from "./base/plataform-layout/i18n/pt/pactopay";
import { TreinoPactoLayoutSearchService } from "./base/plataform-layout/treino-pacto-layout-search.service";
import { PrintComponent } from "./print/print.component";

const translations: MenuTranslation = {
	pt: {
		modules: modulesPt,
		navigation: {
			graduacao: graduacaoPt,
			avaliacao: avaliacaoPt,
			crossfit: crossfitPt,
			agenda: agendaPt,
			treino: treinoPt,
			pessoas: pessoasPt,
			aluno: alunoPt,
			config: configPt,
			pactopay: pactopayPt,
		},
	},
	es: {
		modules: modulesEs,
		navigation: {
			graduacao: graduacaoEs,
			avaliacao: avaliacaoEs,
			crossfit: crossfitEs,
			agenda: agendaEs,
			aluno: alunoPt,
			treino: treinoEs,
			pessoas: pessoasEs,
			config: configEs,
		},
	},
};

const routes: Routes = [
	{
		path: "adicionarConta",
		component: AddAccountControllerComponent,
	},
	{
		path: "montagem-treino",
		loadChildren: () =>
			import("./treino/treino.module").then((m) => m.TreinoModule),
	},
	{
		path: "tv-aula",
		loadChildren: () =>
			import("./tv-aula/tv-aula.module").then((m) => m.TvAulaModule),
	},
	{
		path: "tv-gestor",
		loadChildren: () =>
			import("./tv-gestor/tv-gestor.module").then((m) => m.TvGestorModule),
	},
	{
		pathMatch: "full",
		redirectTo: "treino",
		path: "",
	},
	{
		path: "",
		component: MenuV2RootComponent,
		children: [
			{
				path: "pactopay",
				data: { moduleId: "pactopay", module: ModuleName.PACTOPAY },
				canActivate: [LoggedinGuard],
				children: [
					{
						path: "",
						loadChildren: () =>
							import("./cobranca/cobranca.module").then(
								(m) => m.CobrancaModule
							),
					},
				],
			},
			{
				path: "canal-cliente",
				data: { moduleId: "adm", module: ModuleName.ADM },
				canActivate: [LoggedinGuard],
				loadChildren: () =>
					import("./canal-cliente/canal-cliente.module").then(
						(m) => m.CanalClienteModule
					),
			},
			{
				path: "configuracao",
				data: { moduleId: "config", module: ModuleName.CONFIG },
				canActivate: [LoggedinGuard],
				loadChildren: () =>
					import("./configuracao/configuracao.module").then(
						(m) => m.ConfiguracaoModule
					),
			},
			{
				path: "agenda",
				data: { moduleId: "agenda" },
				canActivate: [LoggedinGuard],
				children: [
					{
						path: "",
						loadChildren: () =>
							import("./agenda/agenda-routing.module").then(
								(m) => m.AgendaRoutingModule
							),
					},
					{
						path: "gestao/indicadores-agenda",
						loadChildren: () =>
							import(
								"../app/treino/treino-gestao/indicadores-agenda/indicadores-agenda.component"
							).then((m) => m.IndicadoresAgendaComponent),
					},
				],
			},
			{
				path: "graduacao",
				data: { moduleId: "graduacao" },
				canActivate: [GraduacaoEnabledGuard, LoggedinGuard],
				loadChildren: () =>
					import("src/app/graduacao/graduacao-routing.module").then(
						(m) => m.GraduacaoRoutingModule
					),
			},
			{
				path: "pessoas",
				data: { moduleId: "aluno", module: ModuleName.PESSOAS },
				canActivate: [LoggedinGuard],
				loadChildren: () =>
					import("src/app/pessoas/pessoas.module").then((m) => m.PessoasModule),
			},
			{
				path: "avaliacao",
				data: { moduleId: "avaliacao" },
				canActivate: [AvaliacaoEnabledGuard, LoggedinGuard],
				loadChildren: () =>
					import("./avaliacao-fisica/avaliacao-fisica.module").then(
						(m) => m.AvaliacaoFisicaModule
					),
			},
			{
				path: "adm",
				data: { moduleId: "adm" },
				canActivate: [LoggedinGuard],
				loadChildren: () => import("./adm/adm.module").then((m) => m.AdmModule),
			},
			{
				path: "cross",
				data: { moduleId: "crossfit", module: ModuleName.CROSSFIT },
				canActivate: [CrossfitEnabledGuard, LoggedinGuard],
				loadChildren: () =>
					import("./crossfit/crossfit.module").then((m) => m.CrossfitModule),
			},
			{
				path: "crm",
				data: { moduleId: "ncrm", module: ModuleName.NCRM },
				canActivate: [LoggedinGuard],
				loadChildren: () => import("./crm/crm.module").then((m) => m.CrmModule),
			},
			{
				path: "financeiro",
				data: { moduleId: "financeiro", module: ModuleName.FIN },
				canActivate: [LoggedinGuard],
				loadChildren: () =>
					import("./financeiro/financeiro.module").then(
						(m) => m.FinanceiroModule
					),
			},
			{
				path: "treino",
				data: { moduleId: "treino", module: ModuleName.TREINO },
				canActivate: [TreinoEnabladGuard, LoggedinGuard],
				children: [
					{
						path: "",
						loadChildren: () =>
							import("./treino/treino.module").then((m) => m.TreinoModule),
					},
					{
						path: "cadastros/niveis",
						loadChildren: () =>
							import("src/app/base/niveis/niveis.module").then(
								(m) => m.NiveisModule
							),
					},
				],
			},
			{
				path: "config",
				data: { moduleId: "config", module: ModuleName.CONFIG },
				canActivate: [LoggedinGuard],
				children: [
					{
						path: "",
						loadChildren: () =>
							import("src/app/base/configuracoes/configuracoes.module").then(
								(m) => m.ConfiguracoesModule
							),
					},
					{
						path: "integracoes",
						loadChildren: () =>
							import(
								"src/app/base/configuracoes/integracoes/integracoes.module"
							).then((m) => m.IntegracoesModule),
					},
				],
			},
			{
				path: "cadastros/alunos",
				loadChildren: () =>
					import("src/app/base/alunos/alunos.module").then(
						(m) => m.AlunosModule
					),
			},
			{
				path: "cadastros/colaboradores",
				loadChildren: () =>
					import("src/app/base/colaborador/colaborador.module").then(
						(m) => m.ColaboradorModule
					),
			},
			{
				path: "colaboradores",
				loadChildren: () =>
					import(
						"src/app/base/colaborador-simples/colaborador-simples.module"
					).then((m) => m.ColaboradorSimplesModule),
			},
			{
				path: "redirectADM",
				data: { onStart: true },
				component: PlataformaLinkAdmComponent,
			},
			{
				path: "print",
				component: PrintComponent,
				data: {
					fullscreen: true,
				},
			},
		],
	},
];

@NgModule({
	imports: [
		RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules }),
		DragulaModule,
	],
	exports: [RouterModule],
	providers: [
		DragulaService,
		{
			provide: PlataformaConfigService,
			useClass: PlataformaV2ConfigService,
		},
		{
			provide: PlataformaNavConfigService,
			useClass: PlataformaV2NavConfigService,
		},
		{
			provide: PlataformaSearchService,
			useClass: PlataformaV2SearchService,
		},
		{
			provide: PactoLayoutSearchService,
			useClass: TreinoPactoLayoutSearchService,
		},
		{ provide: MENU_I18N, useValue: translations },
	],
})
export class AppRoutingModule {}
