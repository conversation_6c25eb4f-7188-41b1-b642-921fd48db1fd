import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { HomeComponent } from "./home/<USER>";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { FinanceiroCoreModule } from "./financeiro-core/financeiro-core.module";
import { FinanceiroSharedModule } from "./financeiro-shared/financeiro-shared.module";
import { HomePageModule } from "pacto-layout";
import { RouterModule, Routes } from "@angular/router";

const routes: Routes = [
	{
		path: "",
		redirectTo: "home",
	},
	{
		path: "home",
		component: HomeComponent,
	},
];

@NgModule({
	imports: [
		CommonModule,
		BaseSharedModule,
		FinanceiroCoreModule,
		FinanceiroSharedModule,
		RouterModule.forChild(routes),
		HomePageModule,
	],
	declarations: [HomeComponent],
})
export class FinanceiroModule {}
