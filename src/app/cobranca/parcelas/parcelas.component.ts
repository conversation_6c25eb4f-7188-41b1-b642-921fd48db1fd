import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { DadosDoFiltro, Filtro, PactoPayApiDashService } from "pactopay-api";
import { BUTTON_SIZE, BUTTON_TYPE } from "projects/ui/src/public-api";
import { PactoModalRef } from "ui-kit/public-api";
import { ConvenioTipo, ZwPactoPayApiDashService } from "zw-pactopay-api";
import { CobrancaService } from "../cobranca.service";
import { DetalhamentoDeCreditoComponent } from "../components/detalhamento-de-credito/detalhamento-de-credito.component";
import { ModalNotificacaoComponent } from "../components/modal-notificacao/modal-notificacao.component";
import { ModalSelecionarConvenioComponent } from "../components/modal-selecionar-convenio/modal-selecionar-convenio.component";
import { RelatorioCobrancaComponent } from "../components/relatorio-cobranca/relatorio-cobranca.component";
import { PactoDataGridConfig } from "./../components/relatorio-cobranca/data-grid.model";

@Component({
	selector: "pacto-parcelas",
	changeDetection: ChangeDetectionStrategy.Default,
	templateUrl: "./parcelas.component.html",
	styleUrls: ["./parcelas.component.scss", "../cobranca.scss"],
})
export class ParcelasComponent implements OnInit {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	public allowsCheck = {
		pendenteRetorno: false,
		seraCobradaHojeAutomaticamente: false,
		foiCobradaHojeManualmente: false,
	};

	@ViewChild("relatorio", { static: false })
	public relatorio: RelatorioCobrancaComponent;

	@ViewChild("actionParcelas", { static: false })
	public actionParcelas: TemplateRef<any>;

	@ViewChild("celulaSituacao", { static: true })
	public celulaSituacao: TemplateRef<any>;

	@ViewChild("celulaValor", { static: true })
	public celulaValor: TemplateRef<any>;

	@ViewChild("celulaBtnVerMais", { static: true })
	public celulaBtnVerMais: TemplateRef<any>;

	@ViewChild("celulaNomeAluno", { static: true })
	public celulaNomeAluno: TemplateRef<any>;

	@ViewChild("celulaDescricao", { static: true })
	public celulaDescricao: TemplateRef<any>;

	@ViewChild("celulaDetalharParcela", { static: true })
	public celulaDetalharParcela: TemplateRef<any>;

	public dataGridConfig: PactoDataGridConfig;
	public creditoPacto: number;
	public ultimaExecucao: string;
	public descricaoCredito: string;
	public utilizados: boolean;
	public buttonSendVisible = false;

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly modalService: ModalService,
		private readonly pactoPayApiDash: PactoPayApiDashService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService,
		private readonly sessionService: SessionService,
		private readonly restService: RestService,
		private readonly router: Router,
		private readonly service: CobrancaService
	) {}

	ngOnInit() {
		this.service.definirTitle("PactoPay - Parcelas em aberto");

		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});

		this.zwPactoPayApiDash
			.notificarRecursoEmpresa("PARCELAS_ABERTO_PACTO_PAY")
			.subscribe(console.log);
	}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.pactoPayApiDash.obterUltimaExecucao().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});

		this.relatorio.table = this.gridConfig();
		this.relatorio.addFilter("inicio", event.filtro.inicio);
		this.relatorio.addFilter("fim", event.filtro.fim);
		this.relatorio.addFilter("empresas", event.filtro.empresas);
		this.relatorio.addFilter("convenios", event.filtro.convenios);
		this.relatorio.reloadData();

		this.cd.detectChanges();
	}

	rowCheckHandler(itemCheck: {
		row?: any;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}) {
		this.buttonSendVisible =
			itemCheck.selectedAll || this.relatorio.selectedItems.length > 0;
	}

	gridConfig() {
		return new PactoDataGridConfig({
			quickSearch: false,
			endpointParamsType: "query",
			endpointUrl: this.restService.buildFullUrlZw(
				`pactopay/dash?op=parcelas&tipotipoParcela=2&key=${this.sessionService.chave}`
			),
			ghostLoad: true,
			ghostAmount: 10,
			rowClick: false,
			showFilters: true,
			valueRowCheck: "codigo",
			tooltipCheckbox:
				"Essa parcela já será cobrada hoje automaticamente ou está pendente de retorno da operadora ou já teve tentativa de cobrança manual.",
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: false,
					buscaRapida: true,
				},
				{
					nome: "nome",
					buscaRapida: true,
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeAluno,
				},
				{
					nome: "codigo",
					titulo: "Cód.: Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
					celula: this.celulaDescricao,
				},
				{
					nome: "nrTentativas",
					titulo: "Tentativas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacaoApresentar",
					titulo: "Status",
					visible: true,
					ordenavel: false,
					celula: this.celulaSituacao,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorTotal",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					celula: this.celulaValor,
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharParcela,
				},
			],
		});
	}

	navegarTelaAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modal = this.modalService.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modal.componentInstance.empresas = this.filtro.empresas;
		}
	}

	enviarParcelas() {
		this.zwPactoPayApiDash
			.getConvenioPorTipo(
				ConvenioTipo.CARTAO,
				Number(this.sessionService.empresaId)
			)
			.subscribe((res) => {
				const convenios = res.map((convenio) => {
					return { value: convenio.codigo, label: convenio.descricao };
				});
				const pctModal = this.modalService.open(
					"",
					ModalSelecionarConvenioComponent
				);
				pctModal.componentInstance.convenios = convenios;
				pctModal.componentInstance.convenioSelecionado.subscribe((convenio) => {
					const parcelas = Array.from(
						new Set(
							this.relatorio.selectedItems.map(
								(itemToMerge) => itemToMerge.codigo
							)
						)
					);

					const body: Partial<any> = {};
					body.parcelas = parcelas;
					body.convenio = convenio;
					body.empresa = this.sessionService.empresaId;

					if (this.sessionService.loggedUser.usuarioZw > 0) {
						body.usuario = this.sessionService.loggedUser.usuarioZw;
					} else {
						body.username = this.sessionService.loggedUser.username;
					}

					this.zwPactoPayApiDash.enviarCobranca(body).subscribe((result) => {
						let tituloModal: string;
						let travaPorMuitasParcelas: boolean;
						if (result.error) {
							travaPorMuitasParcelas = result.error.error.meta.message.includes(
								"Não é possível cobrar mais de"
							);
							if (travaPorMuitasParcelas) {
								tituloModal = "Aviso";
							} else {
								tituloModal = "Erro";
							}
						} else {
							tituloModal = "Sucesso";
						}

						const modalNotificacao: PactoModalRef = this.modalService.open(
							tituloModal,
							ModalNotificacaoComponent
						);
						if (result.error) {
							modalNotificacao.componentInstance.type = travaPorMuitasParcelas
								? "pct-alert-triangle"
								: "pct-x";
							modalNotificacao.componentInstance.title = "Ops!";
							modalNotificacao.componentInstance.subtitle =
								result.error.error.meta.message;
							modalNotificacao.componentInstance.actions = [
								{
									clickHandler: () => modalNotificacao.close(true),
									type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
									size: BUTTON_SIZE.LARGE,
									label: "Ok",
									full: true,
								},
							];
						} else {
							modalNotificacao.componentInstance.type = "pct-check";
							modalNotificacao.componentInstance.title = "Enviado com sucesso!";
							modalNotificacao.componentInstance.subtitle =
								"As cobranças foram enviadas com sucesso.";
							modalNotificacao.componentInstance.actions = [
								{
									clickHandler: () => {
										modalNotificacao.close(true);
										this.relatorio.reloadData();
									},
									type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
									size: BUTTON_SIZE.LARGE,
									label: "Ok",
									full: true,
								},
							];
							this.relatorio.selectedItems = [];
						}
						modalNotificacao.result
							.then()
							.catch((err) => this.relatorio.reloadData());
					});
				});
			});
	}
}
