import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AcoesDeDetalhamentoModule } from "../components/acoes-de-detalhamento/acoes-de-detalhamento.module";
import { PayFilterModule } from "../components/pay-filter/pay-filter.module";
import { RelatorioCobrancaModule } from "../components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusParcelaModule } from "../components/status-parcela/status-parcela.module";
import { ParcelasRoutingModule } from "./parcelas-routing.module";
import { ParcelasComponent } from "./parcelas.component";

@NgModule({
	declarations: [ParcelasComponent],
	imports: [
		CommonModule,
		ParcelasRoutingModule,
		BaseSharedModule,
		PayFilterModule,
		RelatorioCobrancaModule,
		StatusParcelaModule,
		AcoesDeDetalhamentoModule,
	],
})
export class ParcelasModule {}
