<pacto-cat-layout-v2>
	<div class="titulo"><PERSON><PERSON><PERSON><PERSON> em aberto</div>
	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label class="body-mini-card">{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<div
		class="table-wrapper pacto-shadow"
		style="
			background: #ffffff;
			box-shadow: 0px 2px 4px #e4e5e6;
			border-radius: 8px;
		">
		<pacto-relatorio-cobranca
			#relatorio
			(rowCheck)="rowCheckHandler($event)"
			[allowsCheck]="allowsCheck"
			[customActions]="actionParcelas"
			[emptyStateMessage]="
				'Não existem parcelas abertas no período selecionado'
			"
			[itensPerPage]="[
				{ id: 10, label: '10' },
				{ id: 20, label: '20' },
				{ id: 30, label: '30' },
				{ id: 50, label: '50' },
				{ id: 100, label: '100' }
			]"
			[tableTitle]="'Detalhamento de parcelas'"></pacto-relatorio-cobranca>
		<ng-template #celulaDetalharParcela let-parcela="item">
			<pacto-acoes-de-detalhamento
				[parcela]="parcela"></pacto-acoes-de-detalhamento>
		</ng-template>
	</div>
</pacto-cat-layout-v2>

<ng-template #celulaSituacao let-parcela="item">
	<pacto-status-parcela
		[retorno]="parcela.codigoRetorno"
		[situacao]="parcela.situacao"
		[tooltip]="parcela.situacao"></pacto-status-parcela>
</ng-template>

<ng-template #celulaValor let-parcela="item">
	<span>{{ parcela.valorTotal | currency : "R$" }}</span>
</ng-template>

<ng-template #actionParcelas>
	<pacto-cat-button
		(click)="enviarParcelas()"
		*ngIf="buttonSendVisible"
		[full]="true"
		[icon]="'send'"
		[label]="'Enviar cobrança'"
		[size]="'LARGE'"
		[type]="'ACTION'"></pacto-cat-button>
</ng-template>

<ng-template #celulaNomeAluno let-item="item">
	<a (click)="navegarTelaAluno(item)" class="list-item-link">
		{{ item.nome | captalize }}
	</a>
</ng-template>

<ng-template #celulaDescricao let-item="item">
	{{ item.descricao | captalize }}
</ng-template>
