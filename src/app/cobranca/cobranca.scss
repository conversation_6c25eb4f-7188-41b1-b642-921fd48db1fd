@import "dist/ui-kit/assets/import.scss";

.container {
	max-width: 100%;
}

.flex-center {
	display: flex;
	justify-content: center;
	align-content: center;
}

.flex-space {
	display: flex;
	justify-content: space-between;
}

.grid-center {
	display: grid;
	justify-content: center;
	align-content: center;
}

.line {
	width: 90%;
	height: 1px;
	margin: 0 auto;
	background: $cinza02;
}

.line-100 {
	width: 100%;
	height: 1px;
	background: $cinza02;
}

.container-mini-card {
	display: flex;
	margin: 50px 0px 50px 0px;
	font-size: 14px;

	.minicard {
		width: 219px;
		height: 81px;
		margin-right: 21px;
	}

	.label-reais {
		font-size: 12px;
		font-weight: 400;
		color: $azulPacto02;
	}

	.label-valor {
		font-size: 20px;
		font-weight: 700;
		color: $azulPacto02;
	}

	.label-credito {
		display: inline-block;
		color: #486697;
		font-size: 15px;
		margin-left: 5px;
		text-transform: uppercase;
		font-weight: 900;
	}

	.label-descricao {
		display: inline-block;
		margin-bottom: 7px;
	}
}

.grid-filter {
	margin: 10px 0px 32px 0px;
	display: flex;
	justify-content: space-between;

	button {
		width: 68px;
		height: 42px;
		background: #0380e3;
		border-radius: 6px;
		color: #ffffff;
		border: none;
		font-size: 18px;
		cursor: pointer;
	}

	.nome-filter {
		border: 1px solid #c7c9cc;
		padding: 0px 30px 0px 10px;
		line-height: 40px;
		color: #51555a;
		outline: 0px !important;
		box-sizing: border-box;
		border-radius: 6px;
		width: 562px;
		background-color: white;
		font-size: 14px;
		display: flex;
		@extend .type-p-small-rounded;

		.divisor {
			width: 24px;
			height: 0px;
			border: 1px solid #dcdddf;
			transform: rotate(90deg);
			margin-top: 19px;
		}

		i {
			font-size: 20px;
			color: $cinza07;
			position: relative;
			left: 0;
			top: 9px;
		}

		input {
			border: none;
			width: 95%;
		}

		&:focus {
			box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
		}
	}

	.buttons-filter {
		background: #ffffff;
		border-radius: 6px;
		width: 40.5%;
		justify-content: space-around;
		display: flex;

		.button-filter {
			align-self: center;
		}
	}

	.refine-button {
		width: 4%;
	}
}

.body-mini-card {
	font-size: 14px;
	font-weight: 600;
	color: $pretoPri;
}

.details {
	pacto-cat-grid-square-card {
		justify-content: center;
		align-content: center;
		height: 168px;
		border: 1px solid #e1e2e4;
		border-radius: 10px;
		color: $preto05;
		background: #ffffff;
		font-style: normal;

		h2 {
			font-weight: bold;
			font-size: 38px;
			line-height: 48px;
			color: $preto03;
		}

		h3 {
			font-weight: bold;
			font-size: 28px;
			line-height: 38px;
			color: $preto03;
		}

		p {
			font-weight: 400;
			font-size: 18px;
			line-height: 19px;
			color: $preto03;
		}

		label {
			font-weight: normal;
			font-size: 14px;
			line-height: 22px;
		}

		&:hover {
			border: 1px solid #e1e2e4;
			box-sizing: border-box;
			box-shadow: 0px 4px 6px #e4e5e6;
			border-radius: 10px;
		}

		.footer-details {
			border-radius: 8px;
			background: #ffff;
		}

		i {
			color: $azulimPri;
			font-size: 26px;
			line-height: 20px;
		}
	}
}
