import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import * as moment from "moment";
import {
	DadosDoFiltro,
	Filtro,
	IndicadorDeParcelas,
	PactoPayApiCartaoService,
	PactoPayApiDashService,
} from "pactopay-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { CobrancaService } from "../cobranca.service";
import { DetalhamentoDeCreditoComponent } from "../components/detalhamento-de-credito/detalhamento-de-credito.component";
import { PactoDataGridConfig } from "../components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "../components/relatorio-cobranca/relatorio-cobranca.component";

@Component({
	selector: "adm-cartao",
	templateUrl: "./cartao.component.html",
	styleUrls: ["./cartao.component.scss", "../cobranca.scss"],
})
export class CartaoComponent implements OnInit {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("relatorio", { static: false })
	relatorio: RelatorioCobrancaComponent;

	@ViewChild("celulaCobranca", { static: true }) celulaCobranca;
	@ViewChild("celulaAprovadas", { static: true }) celulaAprovadas;
	@ViewChild("celulaPendentes", { static: true }) celulaPendentes;
	@ViewChild("celulaNegadas", { static: true }) celulaNegadas;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaVerMais", { static: true }) celulaVerMais;

	public indicadores: IndicadorDeParcelas[] = [];
	public ultimaExecucao: string;
	public creditoPacto: number;
	public tableDetalhar = false;
	public dataTransacao: string;
	public valorTransacao: string;
	public descricaoCredito: string;
	public utilizados: boolean;

	constructor(
		private readonly pactoPayApiCartao: PactoPayApiCartaoService,
		private readonly pactoPayApiDash: PactoPayApiDashService,
		private readonly rest: RestService,
		private readonly cd: ChangeDetectorRef,
		private readonly router: Router,
		private readonly service: CobrancaService,
		private readonly modal: ModalService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService
	) {}

	ngOnInit() {
		this.service.definirTitle("PactoPay - Cartão de crédito");

		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});

		this.zwPactoPayApiDash
			.notificarRecursoEmpresa("CARTAO_CREDITO_PACTO_PAY")
			.subscribe(console.log);
	}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.pactoPayApiDash.obterUltimaExecucao().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});

		this.pactoPayApiCartao.obterIndicadores(this.filtro).subscribe(
			(res) => {
				if (res.content) {
					this.indicadores = res.content;
				} else {
					console.log(res.meta);
				}
				this.cd.detectChanges();
			},
			(error) => console.log(error)
		);

		this.relatorio.table = this.gridConfig();
		this.relatorio.addFilter("inicio", this.filtro.inicio);
		this.relatorio.addFilter("fim", this.filtro.fim);
		this.relatorio.addFilter("empresas", this.filtro.empresas);
		this.relatorio.addFilter("convenios", this.filtro.convenios);
		this.relatorio.reloadData();

		this.voltarParaHistorico();

		this.cd.detectChanges();
	}

	gridConfig() {
		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay("cartao/historico"),
			quickSearch: false,
			exportButton: false,
			endpointParamsType: "query",
			showFilters: false,
			ghostLoad: true,
			ghostAmount: 10,
			rowClick: false,
			actions: [],
			columns: [
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					celula: this.celulaStatus,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorTotal",
					titulo: "Cobranças enviadas",
					celula: this.celulaCobranca,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorRecebidos",
					titulo: "Cobranças aprovadas",
					celula: this.celulaAprovadas,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorRecebidos",
					titulo: "Cobranças pendentes",
					celula: this.celulaPendentes,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorNaoAprovada",
					titulo: "Cobranças negadas",
					celula: this.celulaNegadas,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "verMais",
					titulo: "",
					celula: this.celulaVerMais,
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	detalharTransacao(objeto: any) {
		const arrayData = objeto.data.split("/");
		this.dataTransacao = moment(
			new Date(arrayData[1] + "/" + arrayData[0] + "/" + arrayData[2])
		).format("YYYYMMDD");
		this.valorTransacao = objeto.valorTotal;
		this.tableDetalhar = true;
	}

	voltarParaHistorico() {
		this.tableDetalhar = false;
	}

	verParcelas(objeto: IndicadorDeParcelas) {
		switch (objeto.indicador) {
			case 1:
				this.router.navigate([`/pactopay/cartao/parcela-prevista`]);
				break;
			case 2:
				this.router.navigate([`/pactopay/cartao/parcela-recebida`]);
				break;
			case 3:
				this.router.navigate([`/pactopay/cartao/parcela-pendentes`]);
				break;
			case 4:
				this.router.navigate([`/pactopay/cartao/parcela-canceladas`]);
				break;
			case 5:
				this.router.navigate([`/pactopay/cartao/parcela-renegociadas`]);
				break;
			default:
				break;
		}
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modal = this.modal.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modal.componentInstance.empresas = this.filtro.empresas;
		}
	}

	public splitValor(valor: number): { inteiro: string; decimal: string } {
		const valorFormatado = valor
			.toLocaleString("pt-br", { minimumFractionDigits: 2 })
			.split(",");
		return {
			inteiro: valorFormatado[0],
			decimal: valorFormatado[1],
		};
	}
}
