<pacto-cat-layout-v2>
	<pacto-cat-page-title [link]="['/pactopay/cartao']">
		<span class="pct-page-title">Parcelas pendentes</span>
	</pacto-cat-page-title>
	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label>{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"
		[preservarFiltro]="true"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<div class="content">
		<div style="display: flex; justify-content: space-between">
			<div>
				<div>
					<span>Detalhamento das parcelas pendentes</span>
				</div>
				<ng-container *ngIf="indicador">
					<div class="subtext">
						<label>Valor pendente</label>
					</div>
					<div class="valor">
						<label>R$</label>
						<h2>{{ splitValor(indicador.valor).inteiro }}</h2>
						<p style="margin-top: 6px">
							,{{ splitValor(indicador.valor).decimal }}
						</p>
					</div>
				</ng-container>
			</div>
			<pacto-cat-button
				(click)="enviarParcelas()"
				*ngIf="podeEnviarCobranca"
				[full]="true"
				[icon]="'send'"
				[label]="'Enviar cobrança'"
				[size]="'LARGE'"
				[type]="'ACTION'"></pacto-cat-button>
		</div>

		<div class="line-divider"></div>

		<div
			*ngFor="let parcelaToogle of parcelasToogle; let i = index"
			class="accordion-wrapper alternColor">
			<div
				(click)="headerClickHandler(i)"
				[ngClass]="{ closed: !isOpen(i) }"
				class="header-section">
				<div class="status-control">
					<i
						*ngIf="isOpen(i)"
						class="pct pct-chevron-up"
						id="close-accordion-{{ parcelaToogle.operacao }}"></i>
					<i
						*ngIf="!isOpen(i)"
						class="pct pct-chevron-down"
						id="open-accordion-{{ parcelaToogle.operacao }}"></i>
				</div>
				<div class="title-content">
					<div class="header">
						<div style="font-weight: bold; width: 200px">
							{{ parcelaToogle.operacaoDescricao }}
						</div>
						<div class="divider"></div>
						<div class="itensHeader">
							<div>{{ transformMoneyValue(parcelaToogle.valorTotal) }}</div>
							<div>Qnt.: {{ parcelaToogle.qtdTotal }}</div>
							<div>
								Enviadas automaticamente: {{ parcelaToogle.qtdAutomatica }}
							</div>
							<div>Reenviadas manualmente: {{ parcelaToogle.qtdManual }}</div>
						</div>
					</div>
				</div>
			</div>

			<div [@openClose]="isOpen(i) ? 'true' : 'false'" class="body-section">
				<pacto-detail-items
					[allowsCheck]="allowsCheck"
					(sendItems)="setSelectedItems($event)"
					[codigoOp]="parcelaToogle.operacao"
					[filtro]="filtro"></pacto-detail-items>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>
