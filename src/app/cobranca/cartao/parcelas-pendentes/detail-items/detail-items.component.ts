import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig } from "src/app/cobranca/components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "./../../../components/relatorio-cobranca/relatorio-cobranca.component";

@Component({
	selector: "pacto-detail-items",
	templateUrl: "./detail-items.component.html",
	styleUrls: ["./detail-items.component.scss"],
})
export class DetailItemsComponent implements OnInit {
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaNomeAluno", { static: true }) celulaNomeAluno;
	@ViewChild("celulaDetalharParcela", { static: true }) celulaDetalharParcela;
	@ViewChild(RelatorioCobrancaComponent, { static: false })
	relatorioItems: RelatorioCobrancaComponent;
	@Input() filtro: any;
	@Input() codigoOp: string;
	// tslint:disable-next-line:ban-types
	@Output() sendItems: EventEmitter<any> = new EventEmitter();
	public tableItems: PactoDataGridConfig;
	@Input() public allowsCheck = { pendenteRetorno: false };

	constructor(
		private readonly router: Router,
		private readonly rest: RestService,
		private readonly modalService: ModalService,
		private readonly sessionService: SessionService
	) {}

	ngOnInit() {
		this.initTable();
	}

	initTable() {
		this.tableItems = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(`pactopay/dash?op=pendentelista`),
			quickSearch: false,
			endpointParamsType: "query",
			ghostLoad: true,
			ghostAmount: 10,
			rowClick: false,
			showFilters: true,
			valueRowCheck: "codigo",
			tooltipCheckbox:
				"Essa parcela já será cobrada hoje automaticamente ou está pendente de retorno da operadora ou já teve tentativa de cobrança manual.",
			initialFilters: [
				{ name: "inicio", value: this.filtro.inicio },
				{ name: "fim", value: this.filtro.fim },
				{ name: "convenios", value: this.filtro.convenios },
				{ name: "empresas", value: this.filtro.empresas },
				{ name: "key", value: this.sessionService.chave },
				{ name: "operacao", value: this.codigoOp },
			],
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeAluno,
				},
				{
					nome: "codigo",
					titulo: "Cód.: Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nrTentativas",
					titulo: "Tentativas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacaoApresentar",
					titulo: "Status",
					celula: this.celulaStatus,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorTotal",
					titulo: "Valor",
					visible: true,
					valueTransform: this.transformMoneyValue,
					ordenavel: false,
				},
				{
					nome: "codigo",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharParcela,
				},
			],
		});
	}

	transformMoneyValue(value: number) {
		return value
			? value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
			  })
			: value;
	}

	navegarTelaAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}

	rowCheckHandler(itemCheck: {
		row?: any;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}) {
		const retorno = {
			id: this.codigoOp,
			row: itemCheck.row,
			selectedItems: this.relatorioItems.selectedItems,
			selectedAll: itemCheck.selectedAll,
			clearAll: itemCheck.clearAll,
		};
		this.sendItems.emit(retorno);
	}

	updateFiltroConvenios() {
		this.relatorioItems.addFilter("convenios", this.filtro.convenios);
		this.relatorioItems.reloadData();
	}

	updateFiltroEmpresas() {
		this.relatorioItems.addFilter("empresas", this.filtro.empresas);
		this.relatorioItems.reloadData();
	}
}
