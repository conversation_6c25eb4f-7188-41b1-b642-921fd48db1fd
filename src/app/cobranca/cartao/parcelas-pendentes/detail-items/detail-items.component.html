<pacto-relatorio-cobranca
	(rowCheck)="rowCheckHandler($event)"
	*ngIf="tableItems"
	[allowsCheck]="allowsCheck"
	[alternatingColors]="'first'"
	[showShare]="false"
	[table]="tableItems"
	class="table"></pacto-relatorio-cobranca>

<ng-template #celulaNomeAluno let-item="item">
	<a (click)="navegarTelaAluno(item)" class="list-item-link">
		{{ item.nome | captalize }}
	</a>
</ng-template>

<ng-template #celulaStatus let-parcela="item">
	<pacto-status-parcela
		[retorno]="parcela.codigoRetorno"
		[situacao]="parcela.situacao"
		[tooltip]="parcela.situacao"></pacto-status-parcela>
</ng-template>

<ng-template #celulaDetalharParcela let-parcela="item">
	<pacto-acoes-de-detalhamento
		[parcela]="parcela"></pacto-acoes-de-detalhamento>
</ng-template>
