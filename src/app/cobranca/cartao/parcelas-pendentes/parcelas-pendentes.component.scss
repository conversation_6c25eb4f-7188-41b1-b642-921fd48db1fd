@import "dist/ui-kit/assets/import.scss";

.line-divider {
	height: 1px;
	background: #c4c4c4;
	width: 100%;
	margin: auto;
}

.content {
	background: #ffffff;
	box-shadow: 0px 2px 4px #e4e5e6;
	border-radius: 8px;
	padding: 24px;

	.title {
		font-style: normal;
		font-weight: bold;
		font-size: 16px;
		line-height: 24px;
		color: #51555a;
	}

	.divider {
		height: 0px;
		border: 1px solid #dcdddf;
		transform: rotate(90deg);
		width: 24px;
		margin-top: 6px;
	}

	.alternColor {
		&:nth-child(2n + 1) {
			background: #fafafa;
		}
	}

	.header-section {
		display: flex;
		align-items: center;
		padding: 11px;
		cursor: pointer;

		background: transparent !important;
		border-radius: 0px;
		box-shadow: none !important;

		.section-title {
			@extend .type-h6-bold;
			color: $azulPactoPri;
		}

		.title-content {
			flex-grow: 1;
		}

		.status-control {
			height: 28px;
			margin-right: 12px;

			.pct {
				position: relative;
				top: 2px;
				cursor: pointer;
				font-size: 24px;
				color: $azulPactoPri;
			}
		}
	}

	.body-section {
		background-color: $branco;
		border-bottom-left-radius: 10px;
		border-bottom-right-radius: 10px;
		box-shadow: inset 0 1px 1px 0px #d4d4d4;
		overflow: hidden;
		padding-bottom: 20px;
		width: 100%;
	}

	.header {
		display: flex;
		font-style: normal;
		font-weight: normal;
		font-size: 12px;
		line-height: 16px;
		color: #51555a;

		.itensHeader {
			margin-left: 32px;
			width: 485px;
			display: flex;
			justify-content: space-between;
		}
	}

	.subtext {
		font-weight: 600;
		font-size: 12px;
		line-height: 12px;
	}

	.valor {
		display: flex;
		color: $azulPacto03;

		label {
			font-size: 10px;
			line-height: 15px;
		}

		h2 {
			font-weight: bold;
			font-size: 24px;
			line-height: 33px;
		}
	}
}

.table {
	a {
		color: #026abc;
	}

	i {
		font-size: 24px;
	}
}

.minicard.clickavel {
	cursor: pointer;
}
