<pacto-cat-layout-v2>
	<pacto-cat-page-title [link]="['/pactopay/cartao']">
		<span class="pct-page-title">Pa<PERSON><PERSON><PERSON> renegociadas</span>
	</pacto-cat-page-title>
	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label>{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"
		[preservarFiltro]="true"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<div
		class="table-wrapper pacto-shadow"
		style="
			background: #ffffff;
			box-shadow: 0px 2px 4px #e4e5e6;
			border-radius: 8px;
		">
		<pacto-relatorio-cobranca
			#relatorio
			[customActions]="actionParcelas"
			[emptyStateMessage]="
				'Não existem cobranças renegociadas no período selecionado'
			"
			[tableTitle]="titulo"
			class="table"></pacto-relatorio-cobranca>
	</div>
</pacto-cat-layout-v2>

<ng-template #titulo>
	<div>
		<span>Detalhamento das parcelas renegociadas</span>
	</div>
	<ng-container *ngIf="indicador">
		<div class="subtext">
			<label>Valor previsto</label>
		</div>
		<div class="valor">
			<label>R$</label>
			<h2>{{ splitValor(indicador.valor).inteiro }}</h2>
			<p style="margin-top: 6px">,{{ splitValor(indicador.valor).decimal }}</p>
		</div>
	</ng-container>
</ng-template>

<ng-template #celulaStatus let-parcela="item">
	<pacto-status-parcela
		[retorno]="parcela.codigoRetorno"
		[situacao]="parcela.situacao"
		[tooltip]="parcela.situacao"></pacto-status-parcela>
</ng-template>

<ng-template #celulaNomeAluno let-item="item">
	<a (click)="navegarTelaAluno(item)" class="list-item-link">
		{{ item.nome | captalize }}
	</a>
</ng-template>

<ng-template #celulaDetalharParcela let-parcela="item">
	<pacto-acoes-de-detalhamento
		[parcela]="parcela"></pacto-acoes-de-detalhamento>
</ng-template>
