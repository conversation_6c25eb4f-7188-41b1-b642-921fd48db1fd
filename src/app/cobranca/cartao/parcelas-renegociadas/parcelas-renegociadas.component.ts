import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	DadosDoFiltro,
	Filtro,
	IndicadorDeParcelas,
	PactoPayApiCartaoService,
	PactoPayApiDashService,
} from "pactopay-api";
import { PactoModalSize } from "ui-kit";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { DetalhamentoDeCreditoComponent } from "../../components/detalhamento-de-credito/detalhamento-de-credito.component";
import { PactoDataGridConfig } from "../../components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "./../../components/relatorio-cobranca/relatorio-cobranca.component";

@Component({
	selector: "pacto-parcelas-renegociadas",
	templateUrl: "./parcelas-renegociadas.component.html",
	styleUrls: ["./parcelas-renegociadas.component.scss", "../../cobranca.scss"],
})
export class ParcelasRenegociadasComponent implements OnInit {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	public indicador: IndicadorDeParcelas;
	public table: PactoDataGridConfig;
	public ultimaExecucao: string;
	public creditoPacto: number;
	public buttonCancelVisible = false;
	public descricaoCredito: string;
	public utilizados: boolean;

	@ViewChild("relatorio", { static: false })
	relatorio: RelatorioCobrancaComponent;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaNomeAluno", { static: true }) celulaNomeAluno;
	@ViewChild("actionParcelas", { static: false }) actionParcelas;
	@ViewChild("celulaDetalharParcela", { static: true }) celulaDetalharParcela;

	constructor(
		private readonly rest: RestService,
		private readonly cd: ChangeDetectorRef,
		private readonly pactoPayApiCartao: PactoPayApiCartaoService,
		private readonly pactoPayApiDash: PactoPayApiDashService,
		private readonly router: Router,
		private readonly modalService: ModalService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService
	) {}

	ngOnInit() {}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.pactoPayApiCartao.obterIndicadores(this.filtro).subscribe(
			(res) => {
				if (res.content) {
					this.indicador = res.content.find((i) => i.indicador === 5);
				} else {
					console.log(res.meta);
				}
				this.cd.detectChanges();
			},
			(error) => console.log(error)
		);

		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});

		this.pactoPayApiDash.obterUltimaExecucao().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});

		this.relatorio.table = this.gridConfig();
		this.relatorio.addFilter("inicio", this.filtro.inicio);
		this.relatorio.addFilter("fim", this.filtro.fim);
		this.relatorio.addFilter("empresas", this.filtro.empresas);
		this.relatorio.addFilter("convenios", this.filtro.convenios);
		this.relatorio.reloadData();

		this.cd.detectChanges();
	}

	private gridConfig() {
		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay(`cartao/indicadores/lista/5`),
			endpointParamsType: "query",
			ghostLoad: true,
			ghostAmount: 10,
			rowClick: false,
			showFilters: true,
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeAluno,
				},
				{
					nome: "codigo",
					titulo: "Cód.: Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricaoParcela",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nrTentativas",
					titulo: "Tentativas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Status",
					celula: this.celulaStatus,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Vencimento",
					visible: true,
					ordenavel: false,
					valueTransform(value: string) {
						const dateArray = value.split(" ");
						return dateArray[0];
					},
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform(value: number) {
						return value
							? value.toLocaleString("pt-BR", {
									minimumFractionDigits: 2,
									style: "currency",
									currency: "BRL",
							  })
							: value;
					},
				},
				{
					nome: "codigo",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharParcela,
				},
			],
		});
	}

	public navegarTelaAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}

	public rowCheckHandler(itemCheck: {
		row?: any;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}) {
		this.buttonCancelVisible =
			itemCheck.selectedAll || this.relatorio.selectedItems.length > 0;
	}

	public enviarParcelas() {
		const parcelas = this.relatorio.selectedItems;
		// const modal = this.modalService.open('Cobrança de parcelas', ModalEnvioParcelaComponent);
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modal = this.modalService.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modal.componentInstance.empresas = this.filtro.empresas;
		}
	}

	public splitValor(valor: number): { inteiro: string; decimal: string } {
		const valorFormatado = valor
			.toLocaleString("pt-br", { minimumFractionDigits: 2 })
			.split(",");
		return {
			inteiro: valorFormatado[0],
			decimal: valorFormatado[1],
		};
	}
}
