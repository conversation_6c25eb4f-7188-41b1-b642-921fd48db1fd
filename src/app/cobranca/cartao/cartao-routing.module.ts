import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { CartaoComponent } from "./cartao.component";
import { ParcelasCanceladasComponent } from "./parcelas-canceladas/parcelas-canceladas.component";
import { ParcelasPendentesComponent } from "./parcelas-pendentes/parcelas-pendentes.component";
import { ParcelasPrevistasComponent } from "./parcelas-previstas/parcelas-previstas.component";
import { ParcelasRecebidasComponent } from "./parcelas-recebidas/parcelas-recebidas.component";
import { ParcelasRenegociadasComponent } from "./parcelas-renegociadas/parcelas-renegociadas.component";

const routes: Routes = [
	{ path: "", component: CartaoComponent },
	{ path: "parcela-prevista", component: ParcelasPrevistasComponent },
	{ path: "parcela-recebida", component: ParcelasRecebidasComponent },
	{ path: "parcela-canceladas", component: ParcelasCanceladasComponent },
	{ path: "parcela-pendentes", component: ParcelasPendentesComponent },
	{ path: "parcela-renegociadas", component: ParcelasRenegociadasComponent },
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class CartaoRoutingModule {}
