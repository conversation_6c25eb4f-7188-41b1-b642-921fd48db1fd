@import "dist/ui-kit/assets/import.scss";

.titulo {
	font-weight: bold;
	color: $pretoPri;
	line-height: 28px;
	font-size: 32px;
	width: 100%;
}

.parcelas {
	font-weight: bold;
	font-size: 24px;
	line-height: 24px;
	margin-top: 32px;
}

.details-cartao {
	margin-top: 20px;

	pacto-cat-grid-square-card {
		width: 220px;
		height: 168px;
		cursor: pointer;
		justify-content: center;
		align-content: center;
		display: grid;

		.grid-card-title {
			font-size: 18px !important;
		}

		.details h2 {
			font-size: 42px !important;
		}

		&:hover {
			box-sizing: border-box;
			box-shadow: 4px 4px 6px #e4e5e6;
			font-weight: bold;
		}
	}
}

.error {
	margin-top: 32px;

	img {
		width: 100%;
	}
}

.gradient {
	background: linear-gradient(45deg, #f0b924 38.02%, #f7d783 100%) !important;
}

.texto-branco {
	color: #fbfbfc !important;

	p {
		color: #fbfbfc !important;
	}

	h2 {
		color: #fbfbfc !important;
	}

	label {
		color: #fbfbfc !important;
	}
}

.content {
	margin-top: 32px;
	margin-bottom: 14px;
}

.t-status {
	color: #ffffff;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100px;
	height: 24px;
}

.t-status-paid {
	background: $chuchuzinho05;
}

.t-status-wait {
	background: $azulim05;
}

.filter-info-wrapper {
	display: flex;
	align-items: center;
	font-style: normal;
	font-size: 14px;
	line-height: 20px;
	color: $pretoPri;
	margin-bottom: 38px;
}

.filter-info-wrapper > div > i {
	width: 18px;
	height: 18px;
	margin-left: 4px;
	cursor: pointer;
}

.filter-info {
	background: #fafafa;
	border-radius: 4px;
	padding: 9px 11px 9px 16px;
	margin-left: 24px;
}

.btn-filter-search {
	background-color: $azulim06;
}

.minicard.clickavel {
	cursor: pointer;
}
