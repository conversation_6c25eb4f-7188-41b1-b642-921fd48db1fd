@import "~dist/ui-kit/assets/import.scss";

.table {
	a {
		color: #026abc;
	}

	i {
		font-size: 24px;
	}

	.subtext {
		font-weight: 600;
		font-size: 12px;
		line-height: 12px;
	}

	.valor {
		display: flex;
		color: $azulPacto03;

		label {
			font-size: 10px;
			line-height: 15px;
		}

		h2 {
			font-weight: bold;
			font-size: 24px;
			line-height: 33px;
		}
	}
}

.table-head {
	display: flex;
	justify-content: space-between;
	width: 950px;

	span {
		font-weight: bold;
		font-size: 14px;
		line-height: 14px;
	}
}

.list-item-link {
	font-weight: bold;
	color: #026abc !important;
	cursor: pointer;
	font-weight: bold;
	font-size: 12px;
	line-height: 16px;
}

@media (min-width: 992px) {
	::ng-deep .modal-lg {
		max-width: 953px;
	}
}

@media (min-width: 1440px) {
	::ng-deep .modal-lg {
		max-width: 1080px;
	}
}

.header-section {
	display: flex;
	align-items: center;
	cursor: pointer;

	background: transparent !important;
	border-radius: 0px;
	box-shadow: none !important;

	.section-title {
		@extend .type-h6-bold;
		color: $azulPactoPri;
	}

	.title-content {
		flex-grow: 1;
	}

	.status-control {
		height: 28px;
		margin-right: 12px;

		.pct {
			position: relative;
			top: 2px;
			cursor: pointer;
			font-size: 24px;
			color: $azulPactoPri;
		}
	}
}

.body-section {
	background-color: $branco;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
	box-shadow: inset 0 1px 1px 0px #d4d4d4;
	overflow: hidden;
	margin-left: -12px;
	width: 103%;
	margin-top: 10px;
}

.header {
	display: flex;
	font-style: normal;
	font-weight: normal;
	font-size: 12px;
	line-height: 16px;
	color: #51555a;
	align-items: center;
	justify-content: space-between;

	.itensHeader {
		margin-left: 32px;
		width: 485px;
		display: flex;
		justify-content: space-between;
	}
}

.margin-bottom {
	margin-bottom: 10px;
}
