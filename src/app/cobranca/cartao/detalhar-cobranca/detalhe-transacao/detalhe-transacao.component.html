<pacto-relatorio-cobranca
	#relatorio
	[emptyStateMessage]="'Não existem transações no período selecionado'"
	[itensPerPage]="[
		{ id: 10, label: '10' },
		{ id: 20, label: '20' },
		{ id: 30, label: '30' },
		{ id: 50, label: '50' },
		{ id: 100, label: '100' }
	]"
	[tableTitle]="titulo"
	[table]="detalhamento"
	class="table"></pacto-relatorio-cobranca>

<ng-template #titulo>
	<div>
		<span>Detalhamento das transações</span>
	</div>
	<div class="subtext">
		<label>Valor cobrado</label>
	</div>
	<div class="valor">
		<label>R$</label>
		<h2>{{ valorFormatado }}</h2>
		<p style="margin-top: 6px">,{{ valorFormatadoFracao }}</p>
	</div>
</ng-template>

<ng-template #celulaDetalharCobranca let-cobranca="item">
	<pacto-acoes-de-detalhamento
		[cobranca]="cobranca"
		[tipo]="'transacao'"></pacto-acoes-de-detalhamento>
</ng-template>

<ng-template #celulaTitular let-item="item">
	{{ item.titular | captalize }}
</ng-template>

<ng-template #celulaStatus let-cobranca="item">
	<pacto-status-transacao
		[codigo]="cobranca.codigoRetorno"
		[status]="cobranca.statusCodigo"
		[tooltip]="cobranca.descricaoRetorno"></pacto-status-transacao>
</ng-template>
