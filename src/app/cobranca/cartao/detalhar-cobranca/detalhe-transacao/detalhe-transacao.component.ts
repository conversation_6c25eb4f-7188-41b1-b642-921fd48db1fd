import { HttpParams } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig } from "src/app/cobranca/components/relatorio-cobranca/data-grid.model";
import { RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-detalhe-transacao",
	templateUrl: "./detalhe-transacao.component.html",
	styleUrls: ["./detalhe-transacao.component.scss"],
})
export class DetalheTransacaoComponent implements OnInit {
	public detalhamento: PactoDataGridConfig;
	public allowsCheck = { permiteCancelar: true };

	@Input() filtro: any;
	@Input() data: string;
	@Input() valorTransacao: number;
	@Input() valorTransacaoDecimal: number;
	valorFormatado: string;
	valorFormatadoFracao: string;
	@ViewChild("celulaToggle", { static: true }) celulaToggle;
	@ViewChild("relatorio", { static: false }) relatorio: RelatorioComponent;
	@ViewChild("celulaTitular", { static: true }) celulaTitular;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaDetalharCobranca", { static: true }) celulaDetalharCobranca;
	open: Array<boolean> = [];

	constructor(
		private readonly rest: RestService,
		private readonly sessionService: SessionService,
		private readonly modal: ModalService
	) {}

	ngOnInit() {
		this.initTable();
		this.formatValue();
	}

	formatValue() {
		const valorFormatado = this.valorTransacao
			.toLocaleString("pt-br", { minimumFractionDigits: 2 })
			.split(",");
		this.valorFormatado = valorFormatado[0];
		this.valorFormatadoFracao = valorFormatado[1];
	}

	transformMoneyValue(value: number) {
		return value
			? value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
			  })
			: value;
	}

	initTable() {
		const params = new HttpParams()
			.append("op", "dashcartaolista")
			.append("key", this.sessionService.chave);

		this.detalhamento = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?${params.toString()}`
			),
			quickSearch: false,
			endpointParamsType: "query",
			exportButton: false,
			showFilters: false,
			rowClick: false,
			ghostLoad: true,
			ghostAmount: 10,
			actions: [],
			initialFilters: [
				{ name: "dia", value: this.data },
				{ name: "inicio", value: this.filtro.inicio },
				{ name: "fim", value: this.filtro.fim },
				{ name: "convenios", value: this.filtro.convenios },
				{ name: "empresas", value: this.filtro.empresas },
			],
			columns: [
				{
					nome: "transacao",
					titulo: "Cód.: Cobrança",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "titular",
					titulo: "Titular do cartão",
					visible: true,
					ordenavel: false,
					celula: this.celulaTitular,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					visible: true,
					celula: this.celulaStatus,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					valueTransform: this.transformMoneyValue,
					ordenavel: false,
				},
				{
					nome: "",
					titulo: "",
					visible: true,
					celula: this.celulaDetalharCobranca,
					ordenavel: false,
				},
			],
		});
	}
}
