@import "~dist/ui-kit/assets/import.scss";

.header {
	margin: 0px 0px -10px 0px;
	display: flex;
	justify-content: space-between;
	padding: 24px 24px 0px 24px;

	.voltar {
		color: $azulim06;
		align-self: center;

		button {
			border: none;
			background: none;
			color: #026abc;
			align-self: center;
			cursor: pointer;
			font-weight: 600;
			font-size: 16px;
			line-height: 24px;

			i {
				font-size: 18px;
			}
		}
	}

	.buttons {
		width: 260px;
		height: 42px;
		background: #f3f3f4;
		border-radius: 4px;
		justify-content: space-around;
		display: flex;
		margin-right: 24px;

		.button {
			font-weight: bold;
			font-size: 12px;
			line-height: 12px;
			align-self: center;
		}
	}
}

.buttonTableGraf {
	display: flex;
	width: 104px;
	height: 42px;
	background: #f3f3f4;
	border-radius: 4px;
	justify-content: center;
	align-items: center;

	.divisor {
		width: 24px;
		height: 0px;
		border: 1px solid #dcdddf;
		transform: rotate(90deg);
	}

	.button {
		button {
			border: none;
			background: transparent;
			font-size: 24px;
			color: $cinza05;
		}

		.selecionado {
			color: $azulim05;
		}
	}
}
