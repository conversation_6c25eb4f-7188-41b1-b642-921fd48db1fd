<div>
	<div class="header">
		<div class="voltar">
			<button (click)="voltar()">
				<i class="pct pct-arrow-left"></i>
				Voltar
			</button>
		</div>
		<div style="display: flex">
			<div class="buttons">
				<pacto-cat-button
					(click)="changeView('transacao')"
					[label]="'Por transação'"
					[type]="
						typeView === 'transacao' ? 'PRIMARY_NO_TEXT_TRANSFORM' : 'NO_BORDER'
					"
					class="button"></pacto-cat-button>
				<pacto-cat-button
					(click)="changeView('remessa')"
					[label]="'Por remessa'"
					[type]="
						typeView === 'remessa' ? 'PRIMARY_NO_TEXT_TRANSFORM' : 'NO_BORDER'
					"
					class="button"></pacto-cat-button>
			</div>
			<!-- <div class="buttonTableGraf">
        <div class="button">
          <button [ngClass]="{ selecionado: lista }">
            <i class="pct pct-list"></i>
          </button>
        </div>
        <div class="divisor"></div>
        <div class="button">
          <button [ngClass]="{ selecionado: !lista }">
            <i class="pct pct-pie-chart"></i>
          </button>
        </div>
      </div> -->
		</div>
	</div>

	<div *ngIf="typeView === 'transacao'">
		<pacto-detalhe-transacao
			[data]="data"
			[filtro]="filtro"
			[valorTransacao]="valorTransacao"></pacto-detalhe-transacao>
	</div>

	<div *ngIf="typeView === 'remessa'">
		<pacto-detalhe-remessa
			[data]="data"
			[filtro]="filtro"
			[valorTransacao]="valorTransacao"></pacto-detalhe-remessa>
	</div>
</div>
