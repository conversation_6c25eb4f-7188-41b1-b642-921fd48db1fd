import { Component, Input, OnInit } from "@angular/core";
import { CartaoComponent } from "../cartao.component";

@Component({
	selector: "pacto-detalhar-cobranca",
	templateUrl: "./detalhar-cobranca.component.html",
	styleUrls: ["./detalhar-cobranca.component.scss"],
})
export class DetalharCobrancaComponent implements OnInit {
	@Input() valorTransacao: number;
	@Input() filtro: any;
	@Input() data: string;
	lista: boolean;
	typeView: string;

	constructor(private cartaoCreditoComponent: CartaoComponent) {}

	ngOnInit() {
		this.typeView = "transacao";
		this.lista = true;
	}

	filterClickHandler(filtro: number) {
		// faz nd ainda
	}

	voltar() {
		this.cartaoCreditoComponent.voltarParaHistorico();
	}

	changeView(tipo: string) {
		switch (tipo) {
			case "remessa":
				this.typeView = tipo;
				break;
			case "transacao":
				this.typeView = tipo;
				break;
			default:
				break;
		}
	}
}
