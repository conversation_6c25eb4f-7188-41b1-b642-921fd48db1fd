@import "~dist/ui-kit/assets/import.scss";

.table {
	a {
		color: #026abc;
	}

	i {
		font-size: 24px;
	}

	.subtext {
		font-weight: 600;
		font-size: 12px;
		line-height: 12px;
	}

	.valor {
		display: flex;
		color: $azulPacto03;

		label {
			font-size: 10px;
			line-height: 15px;
		}

		h2 {
			font-weight: bold;
			font-size: 24px;
			line-height: 33px;
		}
	}
}

.t-status {
	color: #ffffff;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 90px;
	height: 24px;
}

.t-status-send {
	background: #026abc;
}

.t-status-error {
	background: #cd1f00;
}

.t-status-pac {
	background: #d6a10f;
}

.t-status-paid {
	background: $chuchuzinho05;
}

.t-status-wait {
	background: $azulim05;
}

.t-status-generate {
	background: $canetaBic06;
}

.divider {
	height: 0px;
	border: 1px solid #dcdddf;
	transform: rotate(90deg);
	width: 24px;
}

.grid-informations-remessa {
	display: flex;
	justify-content: space-between;
	width: 350px;
	align-items: center;
}

.tipo-normal {
	color: $chuchuzinho06;
}

.tipo-cancelado {
	color: $laranjinha06;
}

@media (min-width: 992px) {
	::ng-deep .modal-lg {
		max-width: 953px;
	}
}

@media (min-width: 1440px) {
	::ng-deep .modal-lg {
		max-width: 1080px;
	}
}

.header-section {
	display: flex;
	align-items: center;
	cursor: pointer;

	background: transparent !important;
	border-radius: 0px;
	box-shadow: none !important;

	.section-title {
		@extend .type-h6-bold;
		color: $azulPactoPri;
	}

	.title-content {
		flex-grow: 1;
	}

	.status-control {
		height: 28px;
		margin-right: 12px;

		.pct {
			position: relative;
			top: 2px;
			cursor: pointer;
			font-size: 24px;
			color: $azulPactoPri;
		}
	}
}

.body-section {
	background-color: $branco;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
	box-shadow: inset 0 1px 1px 0px #d4d4d4;
}

.header {
	display: flex;
	font-style: normal;
	font-weight: normal;
	font-size: 12px;
	line-height: 16px;
	color: #51555a;
	align-items: center;
	justify-content: space-between;

	.itensHeader {
		margin-left: 32px;
		width: 485px;
		display: flex;
		justify-content: space-between;
	}
}

.margin-bottom {
	margin-bottom: 10px;
}
