<pacto-relatorio-cobranca
	[emptyStateMessage]="'Não existe remessa no período selecionado'"
	[tableTitle]="titulo"
	[table]="detalhamento"
	class="table"></pacto-relatorio-cobranca>

<ng-template #titulo>
	<div>
		<span>Detalhamento das remessas</span>
	</div>
	<div class="subtext">
		<label>Valor cobrado</label>
	</div>
	<div class="valor">
		<label>R$</label>
		<h2>{{ valorFormatado }}</h2>
		<p style="margin-top: 6px">,{{ valorFormatadoFracao }}</p>
	</div>
</ng-template>

<ng-template #celulaToggle let-remessa="item">
	<div class="accordion-wrapper alternColor">
		<div
			[ngClass]="{ closed: !isOpen(remessa.sequencial) }"
			class="header-section">
			<div class="status-control">
				<i
					(click)="headerClickHandler(remessa.sequencial)"
					*ngIf="isOpen(remessa.sequencial)"
					class="pct pct-chevron-up"
					id="close-accordion-{{ remessa.sequencial }}"></i>
				<i
					(click)="headerClickHandler(remessa.sequencial)"
					*ngIf="!isOpen(remessa.sequencial)"
					class="pct pct-chevron-down"
					id="open-accordion-{{ remessa.sequencial }}"></i>
			</div>
			<div class="title-content">
				<div class="header">
					<div style="font-weight: bold; width: 120px">{{ remessa.data }}</div>
					<div class="divider"></div>
					<div
						style="
							width: 166px;
							display: flex;
							justify-content: space-between;
							align-items: center;
						">
						<div style="display: flex; align-items: center">
							<span style="margin-right: 5px">Tipo:</span>
							<span *ngIf="remessa.tipo === 'Normal'" class="tipo-normal">
								Normal
							</span>
							<span *ngIf="remessa.tipo === 'Cancelado'" class="tipo-cancelado">
								Cancelado
							</span>
						</div>
						<div>
							<div
								*ngIf="remessa.statusCodigo === 11"
								class="t-status t-status-paid">
								<span>PROCESSADA</span>
							</div>
							<div
								*ngIf="remessa.statusCodigo === 4"
								class="t-status t-status-wait">
								<span>ENVIADA</span>
							</div>
							<div
								*ngIf="remessa.statusCodigo === 9"
								class="t-status t-status-generate">
								<span>GERADA</span>
							</div>
						</div>
					</div>
					<div class="divider"></div>
					<div class="grid-informations-remessa">
						<div>Cód.: {{ remessa.codigo }}</div>
						<div>Seq.: {{ remessa.sequencial }}</div>
						<div>Qnt.: {{ remessa.qtdTotal }}</div>
						<div>
							<div>Valor bruto:</div>
							<div>R$ {{ remessa.valorTotal }}</div>
						</div>
						<div>
							<div>Valor aceito:</div>
							<div>R$ {{ remessa.valorRecebidos }}</div>
						</div>
					</div>
					<div style="width: 250px">Usuário: {{ remessa.usuario }}</div>
					<i
						(click)="openDetailModal(remessa.codigo)"
						class="pct pct-arrow-up-right"></i>
				</div>
			</div>
		</div>

		<div
			*ngIf="isOpen(remessa.sequencial)"
			[@openClose]="isOpen(remessa.sequencial) ? 'true' : 'false'"
			class="body-section">
			<pacto-itens-remessa
				[codigoRemessa]="remessa.codigo"
				[filtro]="filtro"></pacto-itens-remessa>
		</div>
	</div>
</ng-template>
