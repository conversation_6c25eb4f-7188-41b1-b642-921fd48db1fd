import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import { HttpParams } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig, PactoModalSize } from "ui-kit";
import { ModalDetalheRemessaComponent } from "./modal-detalhe-remessa/modal-detalhe-remessa.component";

@Component({
	selector: "pacto-detalhe-remessa",
	templateUrl: "./detalhe-remessa.component.html",
	styleUrls: ["./detalhe-remessa.component.scss"],
	animations: [
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class DetalheRemessaComponent implements OnInit {
	detalhamento: PactoDataGridConfig;
	parcelasToogle: Array<any>;
	open: Array<boolean> = [];
	@Input() filtro: any;
	@Input() data: string;
	@Input() valorTransacao: number;
	@Input() valorTransacaoDecimal: number;
	valorFormatado: string;
	valorFormatadoFracao: string;
	@ViewChild("celulaAluno", { static: true }) celulaAluno;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaIcon", { static: true }) celulaIcon;
	@ViewChild("celulaToggle", { static: true }) celulaToggle;

	constructor(
		private rest: RestService,
		private sessionService: SessionService,
		private modal: ModalService
	) {}

	ngOnInit() {
		this.detalharTransacao();
		this.formatarValor();
	}

	formatarValor() {
		const valorFormatado = this.valorTransacao
			.toLocaleString("pt-br", { minimumFractionDigits: 2 })
			.split(",");
		this.valorFormatado = valorFormatado[0];
		this.valorFormatadoFracao = valorFormatado[1];
	}

	transformMoneyValue(value: number) {
		return value
			? value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
			  })
			: value;
	}

	detalharTransacao() {
		const params = new HttpParams()
			.append("op", "remessa")
			.append("key", this.sessionService.chave);

		this.detalhamento = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?${params.toString()}`
			),
			quickSearch: false,
			endpointParamsType: "query",
			ghostLoad: true,
			ghostAmount: 10,
			rowClick: false,
			showFilters: true,
			initialFilters: [
				{ name: "dia", value: this.data },
				{ name: "inicio", value: this.filtro.inicio },
				{ name: "fim", value: this.filtro.fim },
				{ name: "convenios", value: this.filtro.convenios },
				{ name: "empresas", value: this.filtro.empresas },
			],
			columns: [
				{
					nome: "",
					titulo: "",
					visible: true,
					celula: this.celulaToggle,
					ordenavel: false,
				},
			],
		});
	}

	openDetailModal(codigoRemessa: string) {
		const pctModal = this.modal.open(
			"Detalhamento da remessa",
			ModalDetalheRemessaComponent,
			PactoModalSize.LARGE
		);
		const modalRef = pctModal.componentInstance;
		modalRef.codigoRemessa = codigoRemessa;
	}

	isOpen(index: number) {
		if (this.open && this.open[index]) {
			this.open[index] = true;
			return true;
		} else {
			this.open[index] = false;
			return false;
		}
	}

	headerClickHandler(i: number) {
		this.open[i] = !this.open[i];
	}
}
