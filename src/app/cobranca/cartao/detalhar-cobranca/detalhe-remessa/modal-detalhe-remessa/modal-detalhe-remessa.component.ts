import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { DetalhesDaRemessa, PactoPayApiDetalheService } from "pactopay-api";

@Component({
	selector: "pacto-modal-detalhe-remessa",
	templateUrl: "./modal-detalhe-remessa.component.html",
	styleUrls: ["./modal-detalhe-remessa.component.scss"],
})
export class ModalDetalheRemessaComponent implements OnInit {
	@Input()
	public codigoRemessa: string;
	public detalhesDaRemessa: DetalhesDaRemessa;

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly pactoPayApiDetalhe: PactoPayApiDetalheService
	) {}

	ngOnInit() {
		this.pactoPayApiDetalhe
			.obterDetalhesDaRemessa(this.codigoRemessa)
			.subscribe(
				(res) => {
					if (res.content) {
						this.detalhesDaRemessa = res.content;
						this.cd.detectChanges();
					} else {
						console.log(res.meta);
					}
				},
				(error) => console.log(error)
			);
	}
}
