<pacto-relatorio-cobranca
	[alternatingColors]="'first'"
	[emptyStateMessage]="'Não existe remessa no período selecionado'"
	[showShare]="false"
	[table]="table"
	class="table"></pacto-relatorio-cobranca>

<ng-template #celulaNomeAluno let-item="item">
	<a (click)="navegarTelaAluno(item)" class="list-item-link">
		{{ item.nome | captalize }}
	</a>
</ng-template>

<ng-template #celulaStatus let-item="item">
	<div ngbTooltip="{{ item.codigoRetornoDescricao }}">
		<div
			*ngIf="item.statusDescricao === 'ERRO'"
			class="t-status t-status-error">
			<span>{{ item.codigoRetorno }}</span>
		</div>
		<div *ngIf="item.statusDescricao === 'PAGO'" class="t-status t-status-paid">
			<span>Paga</span>
		</div>
		<div
			*ngIf="item.statusDescricao === 'PENDENTE'"
			class="t-status t-status-send">
			<span>Pendente</span>
		</div>
	</div>
</ng-template>

<ng-template #celulaDetalharCobranca let-cobranca="item">
	<pacto-acoes-de-detalhamento
		[cobranca]="cobranca"
		[tipo]="'transacao'"></pacto-acoes-de-detalhamento>
</ng-template>
