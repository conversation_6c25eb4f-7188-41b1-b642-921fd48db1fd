import { HttpParams } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "pacto-itens-remessa",
	templateUrl: "./itens-remessa.component.html",
	styleUrls: ["./itens-remessa.component.scss"],
})
export class ItensRemessaComponent implements OnInit {
	@Input() filtro: any;
	@Input() codigoRemessa: number;
	@ViewChild("celulaNomeAluno", { static: true }) celulaNomeAluno;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaDetalharCobranca", { static: true }) celulaDetalharCobranca;
	table: PactoDataGridConfig;

	constructor(
		private readonly rest: RestService,
		private readonly sessionService: SessionService,
		private readonly router: Router,
		private readonly modal: ModalService
	) {}

	ngOnInit() {
		this.initTable();
	}

	initTable() {
		const params = new HttpParams()
			.append("op", "remessaitens")
			.append("key", this.sessionService.chave)
			.append("remessa", String(this.codigoRemessa));

		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?${params.toString()}`
			),
			quickSearch: false,
			endpointParamsType: "query",
			ghostLoad: true,
			ghostAmount: 10,
			rowClick: false,
			showFilters: false,
			exportButton: false,
			actions: [],
			initialFilters: [
				{ name: "inicio", value: this.filtro.inicio },
				{ name: "fim", value: this.filtro.fim },
				{ name: "convenios", value: this.filtro.convenios },
				{ name: "empresas", value: this.filtro.empresas },
			],
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeAluno,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "statusDescricao",
					titulo: "Status",
					visible: true,
					ordenavel: false,
					celula: this.celulaStatus,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. de parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: this.transformMoneyValue,
				},
				{
					nome: "codigo",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharCobranca,
				},
			],
		});
	}

	transformMoneyValue(value: number) {
		return value
			? value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
			  })
			: value;
	}

	navegarTelaAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}
}
