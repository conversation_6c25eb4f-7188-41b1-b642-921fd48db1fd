import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AcoesDeDetalhamentoModule } from "../components/acoes-de-detalhamento/acoes-de-detalhamento.module";
import { PayFilterModule } from "../components/pay-filter/pay-filter.module";
import { RelatorioCobrancaModule } from "../components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusParcelaModule } from "../components/status-parcela/status-parcela.module";
import { StatusTransacaoModule } from "../components/status-transacao/status-transacao.module";
import { CartaoRoutingModule } from "./cartao-routing.module";
import { CartaoComponent } from "./cartao.component";
import { DetalharCobrancaComponent } from "./detalhar-cobranca/detalhar-cobranca.component";
import { DetalheRemessaComponent } from "./detalhar-cobranca/detalhe-remessa/detalhe-remessa.component";
import { ItensRemessaComponent } from "./detalhar-cobranca/detalhe-remessa/itens-remessa/itens-remessa.component";
import { ModalDetalheRemessaComponent } from "./detalhar-cobranca/detalhe-remessa/modal-detalhe-remessa/modal-detalhe-remessa.component";
import { DetalheTransacaoComponent } from "./detalhar-cobranca/detalhe-transacao/detalhe-transacao.component";
import { ParcelasCanceladasComponent } from "./parcelas-canceladas/parcelas-canceladas.component";
import { DetailItemsComponent } from "./parcelas-pendentes/detail-items/detail-items.component";
import { ParcelasPendentesComponent } from "./parcelas-pendentes/parcelas-pendentes.component";
import { ParcelasPrevistasComponent } from "./parcelas-previstas/parcelas-previstas.component";
import { ParcelasRecebidasComponent } from "./parcelas-recebidas/parcelas-recebidas.component";
import { ParcelasRenegociadasComponent } from "./parcelas-renegociadas/parcelas-renegociadas.component";

@NgModule({
	declarations: [
		CartaoComponent,
		DetalharCobrancaComponent,
		DetalheTransacaoComponent,
		DetalheRemessaComponent,
		ItensRemessaComponent,
		ModalDetalheRemessaComponent,
		ParcelasCanceladasComponent,
		ParcelasPendentesComponent,
		DetailItemsComponent,
		ParcelasPrevistasComponent,
		ParcelasRecebidasComponent,
		ParcelasRenegociadasComponent,
	],
	imports: [
		CommonModule,
		CartaoRoutingModule,
		BaseSharedModule,
		PayFilterModule,
		RelatorioCobrancaModule,
		StatusParcelaModule,
		StatusTransacaoModule,
		AcoesDeDetalhamentoModule,
	],
	entryComponents: [ModalDetalheRemessaComponent],
})
export class CartaoModule {}
