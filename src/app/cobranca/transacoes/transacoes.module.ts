import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RelatorioCobrancaModule } from "../components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusParcelaModule } from "../components/status-parcela/status-parcela.module";
import { StatusPixModule } from "../components/status-pix/status-pix.module";
import { StatusTransacaoModule } from "../components/status-transacao/status-transacao.module";
import { DetalharParcelaComponent } from "./components/detalhar-parcela/detalhar-parcela.component";
import { DetalharTransacaoComponent } from "./components/detalhar-transacao/detalhar-transacao.component";
import { TransacoesRoutingModule } from "./transacoes-routing.module";

@NgModule({
	imports: [
		TransacoesRoutingModule,
		CommonModule,
		BaseSharedModule,
		StatusTransacaoModule,
		StatusParcelaModule,
		StatusPixModule,
		RelatorioCobrancaModule,
	],
	declarations: [DetalharParcelaComponent, DetalharTransacaoComponent],
	entryComponents: [DetalharParcelaComponent, DetalharTransacaoComponent],
})
export class TransacoesModule {}
