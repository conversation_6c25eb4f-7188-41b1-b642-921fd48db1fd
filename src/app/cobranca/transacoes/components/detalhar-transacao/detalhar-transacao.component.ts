import { HttpParams } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { ZwPactoPayApiTransacaoService } from "zw-pactopay-api";
import { ModalEnvioCancelaCobrancaComponent } from "../../../components/modal-envio-cancela-cobranca/modal-envio-cancela-cobranca.component";
import { ModalErroParcelaComponent } from "../../../components/modal-erro-parcela/modal-erro-parcela.component";
import { PactoDataGridConfig } from "../../../components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "../../../components/relatorio-cobranca/relatorio-cobranca.component";

@Component({
	selector: "pacto-detalhar-transacao",
	templateUrl: "./detalhar-transacao.component.html",
	styleUrls: ["./detalhar-transacao.component.scss"],
})
export class DetalharTransacaoComponent implements OnInit {
	@Input() inicio: string;
	@Input() fim: string;
	@Input() convenios: string;
	@Input() empresas: string;
	@Input() situacoes: number;
	@Input() allowsCheck: any;
	@Input() tipo: string;
	@Output() reloadData: EventEmitter<boolean> = new EventEmitter<boolean>();

	public dataGridTransacoes: PactoDataGridConfig;
	@ViewChild("celulaStatusDaTransacao", { static: true })
	celulaStatusDaTransacao: TemplateRef<any>;
	@ViewChild("celulaNomeDoAluno", { static: true })
	celulaNomeDoAluno: TemplateRef<any>;
	@ViewChild("celulaIconMenuCobranca", { static: true })
	celulaIconMenuCobranca: TemplateRef<any>;
	@ViewChild("relatorioTransacoes", { static: true })
	relatorioTransacoes: RelatorioCobrancaComponent;

	// public habilitarAcao = false;

	constructor(
		private readonly zwPactoPayApiTransacao: ZwPactoPayApiTransacaoService,
		private readonly snotifyService: SnotifyService,
		private readonly sessionService: SessionService,
		private readonly rest: RestService,
		private readonly router: Router,
		private readonly modal: ModalService,
		private readonly ngbModal: NgbModal,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initDataGridTransacoes();
	}

	private initDataGridTransacoes(): void {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "consultar");

		const gridConfig: Partial<any> = {};
		gridConfig.endpointUrl = this.rest.buildFullUrlZw(
			`pactopay/${this.tipo}?${params.toString()}`
		);
		gridConfig.endpointParamsType = "json";
		gridConfig.quickSearch = true;
		gridConfig.exportButton = true;
		gridConfig.showFilters = true;
		gridConfig.rowClick = false;
		gridConfig.valueRowCheck =
			this.allowsCheck && Object.keys(this.allowsCheck).length > 0
				? "transacao"
				: null;
		gridConfig.ghostLoad = true;
		gridConfig.ghostAmount = 10;
		gridConfig.initialFilters = [
			{ name: "inicio", value: this.inicio },
			{ name: "fim", value: this.fim },
			{ name: "convenios", value: this.convenios },
			{ name: "empresas", value: this.empresas },
			{ name: "situacoes", value: String(this.situacoes) },
		];

		gridConfig.columns = [];
		gridConfig.columns.push({
			nome: "transacao",
			titulo: "Cód.: Cobrança",
			visible: true,
			ordenavel: false,
			inputType: null,
		});
		gridConfig.columns.push({
			nome: "titular",
			titulo: "Nome do aluno",
			visible: true,
			ordenavel: false,
			celula: this.celulaNomeDoAluno,
			inputType: null,
		});
		if (this.tipo === "transacao") {
			gridConfig.columns.push({
				nome: "cartao",
				titulo: "Cartão",
				visible: true,
				ordenavel: false,
				inputType: null,
			});
		}
		gridConfig.columns.push({
			nome: "convenio",
			titulo: "Convênio",
			visible: true,
			ordenavel: false,
			inputType: null,
		});
		gridConfig.columns.push({
			nome: "qtd_parcelas",
			titulo: "Qtd. Parcelas",
			visible: true,
			ordenavel: false,
			inputType: null,
		});
		gridConfig.columns.push({
			nome: "status",
			titulo: "Status",
			visible: true,
			celula: this.celulaStatusDaTransacao,
			ordenavel: false,
			inputType: null,
		});
		gridConfig.columns.push({
			nome: "data",
			titulo: "Data",
			visible: true,
			ordenavel: false,
			inputType: null,
		});
		gridConfig.columns.push({
			nome: "valor",
			titulo: "Valor",
			visible: true,
			valueTransform(value: number = 0) {
				return value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
				});
			},
			ordenavel: false,
			inputType: null,
		});
		this.relatorioTransacoes.table = new PactoDataGridConfig(gridConfig);
	}

	public rowCheckTransacoesHandler(rowCheck): void {
		// this.habilitarAcao = rowCheck.selectedAll || this.relatorioTransacoes.selectedItems.length > 0;
	}

	public cancelarCobrancas(): void {
		if (!this.temItemSelecionado) {
			this.snotifyService.info("Selecione algum item da lista!");
			return;
		}

		const username = this.sessionService.loggedUser.professorResponse.username;
		const transacoes = this.relatorioTransacoes.selectedItems.map(
			(row) => row.transacao
		);
		this.zwPactoPayApiTransacao
			.cancelarTransacao({ username, transacoes })
			.subscribe((result) => {
				if (result.error) {
					const modal = this.modal.open("", ModalErroParcelaComponent);
					modal.componentInstance.resultado = result.message;
				} else {
					const modal = this.modal.open("", ModalEnvioCancelaCobrancaComponent);
					modal.componentInstance.resultado = result;
					this.relatorioTransacoes.reloadData();
					this.reloadData.emit(true);
					this.cd.detectChanges();
				}
			});
	}

	public sincronizarCobranca(): void {
		if (!this.temItemSelecionado) {
			this.snotifyService.info("Selecione algum item da lista!");
			return;
		}

		const username = this.sessionService.loggedUser.username;
		const transacoes = this.relatorioTransacoes.selectedItems.map(
			(item) => item.transacao
		);
		this.zwPactoPayApiTransacao
			.sincronizarCobranca(username, transacoes)
			.subscribe((result) => {
				if (result.error) {
					this.snotifyService.error(
						"Não foi possível sincronizar! Tente novamente."
					);
				} else {
					this.snotifyService.success(result.content);
					this.reloadData.emit(true);
				}
			});
	}

	private get temItemSelecionado() {
		return this.relatorioTransacoes.selectedItems.length > 0;
	}

	public verAluno(aluno): void {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
		this.ngbModal.dismissAll();
	}
}
