<!-- [table]="dataGridTransacoes" -->
<pacto-relatorio-cobranca
	#relatorioTransacoes
	(rowCheck)="rowCheckTransacoesHandler($event)"
	[allowsCheck]="allowsCheck"
	[customActions]="actionTransacao"
	[emptyStateMessage]="'Não existem transações no período selecionado'"
	[itensPerPage]="[
		{ id: 10, label: '10' },
		{ id: 20, label: '20' },
		{ id: 30, label: '30' },
		{ id: 50, label: '50' },
		{ id: 100, label: '100' }
	]"
	[showShare]="true"
	class="table"></pacto-relatorio-cobranca>

<ng-template #celulaStatusDaTransacao let-cobranca="item">
	<pacto-status-transacao
		*ngIf="tipo === 'transacao'"
		[codigo]="cobranca.codigoRetorno"
		[status]="cobranca.statusCodigo"
		[tooltip]="cobranca.descricaoRetorno"></pacto-status-transacao>
	<pacto-status-pix
		*ngIf="tipo === 'pix'"
		[codigo]="cobranca.codigoRetorno"
		[status]="cobranca.statusCodigo"
		[tooltip]="cobranca.descricaoRetorno"></pacto-status-pix>
	<!-- <pacto-status-transacao
    *ngIf="tipo === 'boleto'"
    [tooltip]="cobranca.descricaoRetorno"
    [status]="cobranca.statusCodigo"
    [codigo]="cobranca.codigoRetorno"
  ></pacto-status-transacao> -->
</ng-template>

<ng-template #celulaNomeDoAluno let-transacao="item">
	<a (click)="verAluno(transacao.pessoa)" class="list-item-link">
		{{ transacao.nome | captalize }}
	</a>
</ng-template>

<ng-template #actionTransacao>
	<div class="d-flex flex-wrap">
		<pacto-cat-button
			(click)="sincronizarCobranca()"
			*ngIf="tipo === 'transacao' && situacoes === 2"
			[icon]="'refresh-ccw'"
			[label]="'Sincronizar cobrança'"
			[size]="'NORMAL'"
			[type]="'PRIMARY'"
			style="margin: 0px 3px 3px 0px"></pacto-cat-button>

		<pacto-cat-button
			(click)="cancelarCobrancas()"
			*ngIf="tipo === 'transacao' && situacoes === 4"
			[icon]="'x-circle'"
			[label]="'Cancelar cobrança'"
			[size]="'NORMAL'"
			[type]="'ALERT_PARCELAS'"
			style="margin: 0px 3px 3px 0px"></pacto-cat-button>
	</div>
</ng-template>
