import { HttpParams } from "@angular/common/http";
import {
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "src/app/cobranca/components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "src/app/cobranca/components/relatorio-cobranca/relatorio-cobranca.component";
import { TipoDeParcela } from "./tipo-parcela.model";

@Component({
	selector: "pacto-detalhar-parcela",
	templateUrl: "./detalhar-parcela.component.html",
	styleUrls: ["./detalhar-parcela.component.scss"],
})
export class DetalharParcelaComponent implements OnInit {
	@Input() inicio: string;
	@Input() fim: string;
	@Input() convenios: string;
	@Input() empresas: string;
	@Input() situacoes: string;
	@Input() tipoDeParcela: TipoDeParcela;
	@Input() tipo: string;

	public dataGridTransacoes: PactoDataGridConfig;
	@ViewChild("celulaStatusDaTransacao", { static: true })
	celulaStatusDaTransacao: TemplateRef<any>;
	@ViewChild("celulaNomeDoAluno", { static: true })
	celulaNomeDoAluno: TemplateRef<any>;
	@ViewChild("celulaIconMenuCobranca", { static: true })
	celulaIconMenuCobranca: TemplateRef<any>;
	@ViewChild("relatorioTransacoes", { static: true })
	relatorioTransacoes: RelatorioCobrancaComponent;

	constructor(
		private readonly sessionService: SessionService,
		private readonly rest: RestService,
		private readonly router: Router,
		private readonly modal: NgbModal
	) {}

	ngOnInit() {
		this.initDataGridTransacoes();
	}

	private initDataGridTransacoes(): void {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "totalizador_parcela_lista")
			.append("situacao", this.tipoDeParcela);

		this.dataGridTransacoes = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/${this.tipo}?${params.toString()}`
			),
			endpointParamsType: "json",
			quickSearch: true,
			exportButton: true,
			showFilters: true,
			rowClick: false,
			ghostLoad: true,
			ghostAmount: 10,
			initialFilters: [
				{ name: "inicio", value: this.inicio },
				{ name: "fim", value: this.fim },
				{ name: "convenios", value: this.convenios },
				{ name: "empresas", value: this.empresas },
				{ name: "situacoes", value: this.situacoes },
			],
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeDoAluno,
				},
				{
					nome: "codigo",
					titulo: "Cód.: Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nrTentativas",
					titulo: "Tentativa",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Status",
					visible: true,
					ordenavel: false,
					celula: this.celulaStatusDaTransacao,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorParcela",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform(value: number = 0) {
						return value.toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							style: "currency",
							currency: "BRL",
						});
					},
				},
			],
		});
	}

	public verAluno(aluno): void {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
		this.modal.dismissAll();
	}
}
