<pacto-relatorio-cobranca
	#relatorioTransacoes
	[emptyStateMessage]="'Não existem parcelas no período selecionado'"
	[showShare]="true"
	[table]="dataGridTransacoes"
	class="table"></pacto-relatorio-cobranca>

<ng-template #celulaStatusDaTransacao let-parcela="item">
	<pacto-status-parcela
		[retorno]="parcela.codigoRetorno"
		[situacao]="parcela.situacao"
		[tooltip]="parcela.situacao"></pacto-status-parcela>
</ng-template>

<ng-template #celulaNomeDoAluno let-transacao="item">
	<a (click)="verAluno(transacao.pessoa)" class="list-item-link">
		{{ transacao.nome | captalize }}
	</a>
</ng-template>
