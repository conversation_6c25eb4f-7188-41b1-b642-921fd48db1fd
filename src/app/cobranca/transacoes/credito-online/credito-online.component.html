<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'PactoPay / Transações'
		}"></pacto-breadcrumbs>

	<pacto-cat-page-title [link]="['../../']">
		<span class="pct-page-title">Transações Cartão de crédito</span>
	</pacto-cat-page-title>

	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label class="body-mini-card">{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"
		[includesStatus]="true"
		[includesUsuario]="true"
		[obterConveniosPorTipo]="2"
		[obterSituacoesPorTipo]="'transacao'"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<div class="flex-space indicadores">
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(4)"
			class="clickavel"
			id="concluida_com_sucesso">
			<div card-title>Aprovada</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #2ec750">
						{{
							totalizadores["CONCLUIDA_COM_SUCESSO"].valor
								| currency : "BRL" : true
						}}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["CONCLUIDA_COM_SUCESSO"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(3)"
			class="clickavel"
			id="nao_aprovada">
			<div card-title>Não aprovada</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #db2c3d">
						{{ totalizadores["NAO_APROVADA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["NAO_APROVADA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(1)"
			class="clickavel"
			id="erro">
			<div card-title>Inválida</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #f0b924">
						{{ totalizadores["ERRO"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["ERRO"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(2)"
			class="clickavel"
			id="aguardando_aprovacao">
			<div card-title>Aguardando aprovação</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #1998fc">
						{{
							totalizadores["AGUARDANDO_APROVACAO"].valor
								| currency : "BRL" : true
						}}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["AGUARDANDO_APROVACAO"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(6)"
			class="clickavel"
			id="cancelada">
			<div card-title>Cancelada</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #ff5b92">
						{{ totalizadores["CANCELADA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["CANCELADA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(8)"
			class="clickavel"
			id="estornada">
			<div card-title>Estornada</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #c98ac9">
						{{ totalizadores["ESTORNADA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["ESTORNADA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
	</div>

	<br />

	<div class="card">
		<pacto-relatorio-cobranca
			#relatorioTransacoes
			[emptyStateMessage]="'Não existem transações no período selecionado'"
			[showShare]="true"
			[table]="tableTransacoes"
			class="table"
			id="transacoes"></pacto-relatorio-cobranca>
		<!-- CELULAS - TRANSAÇÃO -->
		<ng-template #celulaStatusDaTransacao let-cobranca="item">
			<pacto-status-transacao
				[codigo]="cobranca.codigoRetorno"
				[status]="cobranca.statusCodigo"
				[tooltip]="cobranca.descricaoRetorno"></pacto-status-transacao>
		</ng-template>

		<ng-template #celulaNomeDoAluno let-transacao="item">
			<a (click)="verAluno(transacao.pessoa)" class="list-item-link">
				{{ transacao.nome | captalize }}
			</a>
		</ng-template>

		<ng-template #celulaDetalharCobranca let-cobranca="item">
			<pacto-acoes-de-detalhamento
				[cobranca]="cobranca"
				[tipo]="'transacao'"></pacto-acoes-de-detalhamento>
		</ng-template>
		<!-- FIM -->
	</div>

	<br />

	<section class="row">
		<div class="col-6">
			<pacto-relatorio-cobranca
				#relatorioParcelas
				(rowClick)="detalharParcelas($event.codigo)"
				[emptyStateMessage]="'Não existem parcelas no período selecionado'"
				[itensPerPage]="[
					{ id: 4, label: '04' },
					{ id: 10, label: '10' },
					{ id: 20, label: '20' },
					{ id: 30, label: '30' }
				]"
				[showShare]="false"
				class="card"
				id="parcelas"></pacto-relatorio-cobranca>
			<!-- CELULAS - PARCELAS -->
			<ng-template #parcelaTituloQtdTotal>
				<span>Qtd.: {{ parcelasTitulo?.quantidade }}</span>
			</ng-template>

			<ng-template #parcelaTituloValorTotal>
				<span class="d-flex justify-content-end">
					Total: {{ parcelasTitulo?.currency }}
				</span>
			</ng-template>

			<ng-template #celulaParcelasQtd let-transacao="item">
				<span>Qtd.: {{ transacao.quantidade }}</span>
			</ng-template>

			<ng-template #celulaParcelasValor let-transacao="item">
				<span
					[style.color]="transacao.codigo === 'EA' ? 'red' : ''"
					class="d-flex justify-content-end">
					{{ transacao.valor | currency : "BRL" : true }}
				</span>
			</ng-template>
			<!-- FIM -->
		</div>
		<div class="col-6">
			<pacto-relatorio-cobranca
				#relatorioBandeiras
				[emptyStateMessage]="'Não existem bandeiras no período selecionado'"
				[itensPerPage]="[
					{ id: 3, label: '03' },
					{ id: 10, label: '10' },
					{ id: 20, label: '20' },
					{ id: 30, label: '30' }
				]"
				[showShare]="false"
				class="card"
				id="bandeiras"></pacto-relatorio-cobranca>
			<!-- CELULAS - BANDEIRAS -->
			<ng-template #bandeiraTituloQtdTotal>
				<span>Qtd.: {{ bandeirasTitulo?.quantidade }}</span>
			</ng-template>

			<ng-template #bandeiraTituloValorTotal>
				<span class="d-flex justify-content-end">
					Total: {{ bandeirasTitulo?.currency }}
				</span>
			</ng-template>

			<ng-template #celulaBandeiraQtd let-transacao="item">
				<span>Qtd.: {{ transacao.quantidade }}</span>
			</ng-template>

			<ng-template #celulaBandeiraValor let-transacao="item">
				<span class="d-flex justify-content-end">
					{{ transacao.valor | currency : "BRL" : true }}
				</span>
			</ng-template>
			<!-- FIM -->
		</div>
	</section>

	<br />

	<pacto-cat-tabs-transparent
		#tabs
		(activateTab)="(null)"
		*ngIf="totalizadoresPorConvenio.length > 1"
		class="tabs-creditos">
		<ng-template
			*ngFor="let convenio of totalizadoresPorConvenio"
			[label]="convenio.tipo_descricao"
			[pactoTabTransparent]="convenio.tipo_descricao">
			<div class="flex-space indicadores">
				<pacto-cat-grid-square-card class="card_indicadores">
					<div card-title>Aprovada</div>
					<div card-body>
						<div class="flex-center">
							<h2>
								{{
									convenio.totalizadores["CONCLUIDA_COM_SUCESSO"].valor
										| currency : "BRL" : true
								}}
							</h2>
						</div>
						<div class="flex-center">
							<label>Qnt.:</label>
							<label>
								{{ convenio.totalizadores["CONCLUIDA_COM_SUCESSO"].quantidade }}
							</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
				<pacto-cat-grid-square-card class="card_indicadores">
					<div card-title>Não aprovada</div>
					<div card-body>
						<div class="flex-center">
							<h2>
								{{
									convenio.totalizadores["NAO_APROVADA"].valor
										| currency : "BRL" : true
								}}
							</h2>
						</div>
						<div class="flex-center">
							<label>Qnt.:</label>
							<label>
								{{ convenio.totalizadores["NAO_APROVADA"].quantidade }}
							</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
				<pacto-cat-grid-square-card class="card_indicadores">
					<div card-title>Inválida</div>
					<div card-body>
						<div class="flex-center">
							<h2>
								{{
									convenio.totalizadores["ERRO"].valor | currency : "BRL" : true
								}}
							</h2>
						</div>
						<div class="flex-center">
							<label>Qnt.:</label>
							<label>{{ convenio.totalizadores["ERRO"].quantidade }}</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
				<pacto-cat-grid-square-card class="card_indicadores">
					<div card-title>Aguardando aprovação</div>
					<div card-body>
						<div class="flex-center">
							<h2>
								{{
									convenio.totalizadores["AGUARDANDO_APROVACAO"].valor
										| currency : "BRL" : true
								}}
							</h2>
						</div>
						<div class="flex-center">
							<label>Qnt.:</label>
							<label>
								{{ convenio.totalizadores["AGUARDANDO_APROVACAO"].quantidade }}
							</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
				<pacto-cat-grid-square-card class="card_indicadores">
					<div card-title>Cancelada</div>
					<div card-body>
						<div class="flex-center">
							<h2>
								{{
									convenio.totalizadores["CANCELADA"].valor
										| currency : "BRL" : true
								}}
							</h2>
						</div>
						<div class="flex-center">
							<label>Qnt.:</label>
							<label>
								{{ convenio.totalizadores["CANCELADA"].quantidade }}
							</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
				<pacto-cat-grid-square-card class="card_indicadores">
					<div card-title>Estornada</div>
					<div card-body>
						<div class="flex-center">
							<h2>
								{{
									convenio.totalizadores["ESTORNADA"].valor
										| currency : "BRL" : true
								}}
							</h2>
						</div>
						<div class="flex-center">
							<label>Qnt.:</label>
							<label>
								{{ convenio.totalizadores["ESTORNADA"].quantidade }}
							</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
			</div>
		</ng-template>
	</pacto-cat-tabs-transparent>
</pacto-cat-layout-v2>
