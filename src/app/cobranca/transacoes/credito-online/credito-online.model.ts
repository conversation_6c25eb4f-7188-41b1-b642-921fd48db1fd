import { TotalizadorTransacao } from "zw-pactopay-api";

export interface TotalizadoresMap {
	ERRO?: TotalizadorTransacao;
	AGUARDANDO_APROVACAO?: TotalizadorTransacao;
	NAO_APROVADA?: TotalizadorTransacao;
	CONCLUIDA_COM_SUCESSO?: TotalizadorTransacao;
	CANCELADA?: TotalizadorTransacao;
	ESTORNADA?: TotalizadorTransacao;
}

export interface TotalizadoresPorConvenioMap {
	totalizadores: TotalizadoresMap;
	tipo_descricao: string;
	tipo_codigo: number;
}
