import { HttpParams } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { DadosDoFiltro, Filtro, PactoPayApiDashService } from "pactopay-api";
import {
	TotalizadorTransacao,
	ZwPactoPayApiDashService,
	ZwPactoPayApiTransacaoService,
} from "zw-pactopay-api";
import { CobrancaService } from "../../cobranca.service";
import { DetalhamentoDeCreditoComponent } from "../../components/detalhamento-de-credito/detalhamento-de-credito.component";
import {
	PactoDataGridConfig,
	PactoDataGridState,
} from "../../components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "../../components/relatorio-cobranca/relatorio-cobranca.component";
import { DetalharParcelaComponent } from "../components/detalhar-parcela/detalhar-parcela.component";
import { TipoDeParcela } from "../components/detalhar-parcela/tipo-parcela.model";
import { DetalharTransacaoComponent } from "../components/detalhar-transacao/detalhar-transacao.component";
import { SituacaoDaTransacao } from "../transacoes.model";
import {
	TotalizadoresMap,
	TotalizadoresPorConvenioMap,
} from "./credito-online.model";

@Component({
	selector: "pacto-credito-online",
	templateUrl: "./credito-online.component.html",
	styleUrls: ["./credito-online.component.scss", "../../cobranca.scss"],
})
export class CreditoOnlineComponent implements OnInit {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	public tableTransacoes: PactoDataGridConfig = new PactoDataGridConfig({
		state: null,
		buttons: null,
		columns: [],
	});

	@ViewChild("celulaStatusDaTransacao", { static: true })
	celulaStatusDaTransacao: TemplateRef<any>;
	@ViewChild("celulaNomeDoAluno", { static: true })
	celulaNomeDoAluno: TemplateRef<any>;
	@ViewChild("celulaDetalharCobranca", { static: true })
	celulaDetalharCobranca: TemplateRef<any>;
	@ViewChild("relatorioTransacoes", { static: true })
	relatorioTransacoes: RelatorioCobrancaComponent;

	@ViewChild("relatorioParcelas", { static: true })
	relatorioParcelas: RelatorioCobrancaComponent;
	@ViewChild("parcelaTituloQtdTotal", { static: true })
	parcelaTituloQtdTotal: TemplateRef<any>;
	@ViewChild("parcelaTituloValorTotal", { static: true })
	parcelaTituloValorTotal: TemplateRef<any>;
	@ViewChild("celulaParcelasValor", { static: true })
	celulaParcelasValor: TemplateRef<any>;
	@ViewChild("celulaParcelasQtd", { static: true })
	celulaParcelasQtd: TemplateRef<any>;
	public parcelasTitulo = { currency: "R$ 0,00", quantidade: 0 };

	@ViewChild("relatorioBandeiras", { static: true })
	relatorioBandeiras: RelatorioCobrancaComponent;
	@ViewChild("bandeiraTituloQtdTotal", { static: true })
	bandeiraTituloQtdTotal: TemplateRef<any>;
	@ViewChild("bandeiraTituloValorTotal", { static: true })
	bandeiraTituloValorTotal: TemplateRef<any>;
	@ViewChild("celulaBandeiraValor", { static: true })
	celulaBandeiraValor: TemplateRef<any>;
	public bandeirasTitulo = { currency: "R$ 0,00", quantidade: 0 };

	public ultimaExecucao: string;
	public descricaoCredito: string;
	public creditoPacto: number;
	public utilizados: boolean;

	public totalizadores: TotalizadoresMap = {};
	public totalizadoresPorConvenio: TotalizadoresPorConvenioMap[] = [];
	public totalizadoresPorParcela: TotalizadorTransacao[] = [];
	public totalizadoresPorBandeira: TotalizadorTransacao[] = [];

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly cobrancaService: CobrancaService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService,
		private readonly zwPactoPayApiTransacao: ZwPactoPayApiTransacaoService,
		private readonly pactoPayApiDash: PactoPayApiDashService,
		private readonly rest: RestService,
		private readonly sessionService: SessionService,
		private readonly router: Router,
		private readonly modal: ModalService
	) {}

	ngOnInit() {
		this.cobrancaService.definirTitle(
			"PactoPay - Transações Cartão de crédito"
		);

		this.buscarCreditoPacto();

		this.tableTransacoes = this.gridConfigTransacoes();

		this.totalizadores = this.inicializarTotalizadores();
	}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.buscarUltimoEnvioDeCobranca();
		this.buscarTotalizadores(this.filtro);
		this.buscarTotalizadoresPorConvenio(this.filtro);

		this.reloadDataTransacoes(this.filtro);
		this.reloadDataParcelas(this.filtro);
		this.reloadDataBandeiras(this.filtro);

		this.cd.detectChanges();
	}

	private buscarCreditoPacto(): void {
		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});
	}

	private buscarUltimoEnvioDeCobranca(): void {
		this.pactoPayApiDash.obterUltimaExecucao().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});
	}

	private buscarTotalizadores(filtro: Filtro): void {
		this.zwPactoPayApiTransacao
			.buscarTotalizadores(filtro)
			.subscribe((totalizadores) => {
				this.totalizadores = this.mapearTotalizadores(totalizadores);
				this.cd.detectChanges();
			});
	}

	private buscarTotalizadoresPorConvenio(filtro: Filtro): void {
		this.zwPactoPayApiTransacao
			.buscarTotalizadoresPorConvenio(filtro)
			.subscribe((convenios) => {
				this.totalizadoresPorConvenio = convenios.map(
					({ totalizadores, tipo_codigo, tipo_descricao }) => {
						return {
							tipo_descricao,
							tipo_codigo,
							totalizadores: this.mapearTotalizadores(totalizadores),
						};
					}
				);
				this.cd.detectChanges();
			});
	}

	private mapearTotalizadores(
		totalizadores: TotalizadorTransacao[]
	): TotalizadoresMap {
		const mapeado: TotalizadoresMap = this.inicializarTotalizadores();

		for (const totalizador of totalizadores) {
			mapeado[totalizador.descricao] = totalizador;
		}
		return mapeado;
	}

	private inicializarTotalizadores(): TotalizadoresMap {
		const vazio: TotalizadorTransacao = {
			codigo: "",
			valor: 0,
			quantidade: 0,
			descricao: "",
		};

		const mapeado: TotalizadoresMap = {};
		mapeado["CONCLUIDA_COM_SUCESSO"] = vazio;
		mapeado["NAO_APROVADA"] = vazio;
		mapeado["ERRO"] = vazio;
		mapeado["AGUARDANDO_APROVACAO"] = vazio;
		mapeado["CANCELADA"] = vazio;
		mapeado["ESTORNADA"] = vazio;
		return mapeado;
	}

	private gridConfigTransacoes(): PactoDataGridConfig {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "consultar");

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/transacao?${params.toString()}`
			),
			endpointParamsType: "json",
			quickSearch: true,
			exportButton: true,
			showFilters: true,
			rowClick: false,
			ghostLoad: true,
			ghostAmount: 10,
			columns: [
				{
					nome: "transacao",
					titulo: "Cód.: Cobrança",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "titular",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeDoAluno,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					visible: true,
					celula: this.celulaStatusDaTransacao,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					valueTransform(value: number = 0) {
						return value.toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							style: "currency",
							currency: "BRL",
						});
					},
					ordenavel: false,
				},
				{
					nome: "",
					titulo: "",
					visible: true,
					celula: this.celulaDetalharCobranca,
					ordenavel: false,
				},
			],
		});
	}

	private reloadDataTransacoes(filtro: Filtro): void {
		// this.relatorioTransacoes.table = this.gridConfigTransacoes();
		this.relatorioTransacoes.addFilter("inicio", filtro.inicio);
		this.relatorioTransacoes.addFilter("fim", filtro.fim);
		this.relatorioTransacoes.addFilter("convenios", filtro.convenios);
		this.relatorioTransacoes.addFilter("empresas", filtro.empresas);
		this.relatorioTransacoes.addFilter("situacoes", filtro.situacoes);
		this.relatorioTransacoes.addFilter("usuarios", filtro.usuarios);
		this.relatorioTransacoes.reloadData();

		this.cd.detectChanges();
	}

	private gridConfigParcelas(): PactoDataGridConfig {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "totalizador_parcela");

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/transacao?${params.toString()}`
			),
			endpointParamsType: "json",
			pagination: false,
			rowClick: true,
			dataAdapterFn: (serverData: any) => {
				const quantidade = serverData.content
					.map((item) => item.quantidade)
					.reduce((prev, curr) => prev + curr, 0);
				const valor = serverData.content
					.map((item) => item.valor)
					.reduce((prev, curr) => prev + curr, 0);
				const currency = valor.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
				});
				this.parcelasTitulo = { currency, quantidade };
				return serverData;
			},
			ghostLoad: true,
			ghostAmount: 10,
			columns: [
				{
					nome: "descricao",
					titulo: "Parcelas",
					visible: true,
					ordenavel: false,
					valueTransform(word: string = "") {
						const splitStr = word.toLowerCase().split(" ");
						for (let i = 0; i < splitStr.length; i++) {
							splitStr[i] =
								splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
						}
						return splitStr.join(" ");
					},
				},
				{
					nome: "quantidade",
					titulo: this.parcelaTituloQtdTotal,
					visible: true,
					ordenavel: false,
					celula: this.celulaParcelasQtd,
				},
				{
					nome: "valor",
					titulo: this.parcelaTituloValorTotal,
					visible: true,
					ordenavel: false,
					celula: this.celulaParcelasValor,
				},
			],
		});
	}

	private reloadDataParcelas(filtro: Filtro): void {
		this.relatorioParcelas.table = this.gridConfigParcelas();
		this.relatorioParcelas.addFilter("inicio", filtro.inicio);
		this.relatorioParcelas.addFilter("fim", filtro.fim);
		this.relatorioParcelas.addFilter("convenios", filtro.convenios);
		this.relatorioParcelas.addFilter("empresas", filtro.empresas);
		this.relatorioParcelas.addFilter("situacoes", filtro.situacoes);
		this.relatorioParcelas.addFilter("usuarios", filtro.usuarios);
		this.relatorioParcelas.reloadData();

		this.cd.detectChanges();
	}

	private gridConfigBandeiras(): PactoDataGridConfig {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "totalizador_bandeira");

		const state: PactoDataGridState = new PactoDataGridState({
			paginaTamanho: 3,
			paginaNumero: 0,
		});

		return new PactoDataGridConfig({
			state,
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/transacao?${params.toString()}`
			),
			endpointParamsType: "json",
			dataAdapterFn: (serverData: any) => {
				const quantidade = serverData.content
					.map((item) => item.quantidade)
					.reduce((prev, curr) => prev + curr, 0);
				const valor = serverData.content
					.map((item) => item.valor)
					.reduce((prev, curr) => prev + curr, 0);
				const currency = valor.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
				});
				this.bandeirasTitulo = { currency, quantidade };
				return serverData;
			},
			rowClick: false,
			columns: [
				{
					nome: "descricao",
					titulo: "Bandeiras",
					visible: true,
					ordenavel: false,
					valueTransform(word: string = "") {
						const splitStr = word.toLowerCase().split(" ");
						for (let i = 0; i < splitStr.length; i++) {
							splitStr[i] =
								splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
						}
						return splitStr.join(" ");
					},
				},
				{
					nome: "quantidade",
					titulo: this.bandeiraTituloQtdTotal,
					visible: true,
					ordenavel: false,
					celula: this.celulaParcelasQtd,
				},
				{
					nome: "valor",
					titulo: this.bandeiraTituloValorTotal,
					visible: true,
					ordenavel: false,
					celula: this.celulaBandeiraValor,
					valueTransform(value: number = 0) {
						return value.toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							style: "currency",
							currency: "BRL",
						});
					},
				},
			],
		});
	}

	private reloadDataBandeiras(filtro: Filtro): void {
		this.relatorioBandeiras.table = this.gridConfigBandeiras();
		this.relatorioBandeiras.pageSizeControl.setValue(3);
		this.relatorioBandeiras.addFilter("inicio", filtro.inicio);
		this.relatorioBandeiras.addFilter("fim", filtro.fim);
		this.relatorioBandeiras.addFilter("convenios", filtro.convenios);
		this.relatorioBandeiras.addFilter("empresas", filtro.empresas);
		this.relatorioBandeiras.addFilter("situacoes", filtro.situacoes);
		this.relatorioBandeiras.addFilter("usuarios", filtro.usuarios);
		this.relatorioBandeiras.reloadData();

		this.cd.detectChanges();
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modalRef = this.modal.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modalRef.componentInstance.empresas = this.filtro.empresas;
		}
	}

	public detalharTransacao(status: SituacaoDaTransacao): void {
		let titulo = "Transações";
		let allowsCheck = {};

		switch (status) {
			case SituacaoDaTransacao.AGUARDANDO_APROVACAO:
				titulo = "Transações aguardando aprovação";
				allowsCheck = { permiteSincronizar: true };
				break;
			case SituacaoDaTransacao.APROVADA:
				titulo = "Transações aprovadas";
				allowsCheck = { permiteCancelar: true };
				break;
			case SituacaoDaTransacao.CANCELADA:
				titulo = "Transações canceladas";
				break;
			case SituacaoDaTransacao.COM_ERRO:
				titulo = "Transações inválidas";
				break;
			case SituacaoDaTransacao.ESTORNADA:
				titulo = "Transações estornadas";
				break;
			case SituacaoDaTransacao.NAO_APROVADA:
				titulo = "Transações não aprovadas";
				break;
		}

		const modalRef = this.modal.open(
			titulo,
			DetalharTransacaoComponent,
			PactoModalSize.LARGE
		);

		modalRef.componentInstance.inicio = this.filtro.inicio;
		modalRef.componentInstance.fim = this.filtro.fim;
		modalRef.componentInstance.convenios = this.filtro.convenios;
		modalRef.componentInstance.empresas = this.filtro.empresas;
		modalRef.componentInstance.situacoes = status;
		modalRef.componentInstance.allowsCheck = allowsCheck;
		modalRef.componentInstance.tipo = "transacao";
		modalRef.componentInstance.reloadData.subscribe((atualiza) => {
			if (atualiza) {
				this.buscarTotalizadores(this.filtro);
				this.buscarTotalizadoresPorConvenio(this.filtro);

				this.reloadDataTransacoes(this.filtro);
				this.reloadDataParcelas(this.filtro);
				this.reloadDataBandeiras(this.filtro);

				this.cd.detectChanges();
			}
		});
	}

	public detalharParcelas(tipoDeParcela: TipoDeParcela) {
		let titulo = "Parcelas";

		switch (tipoDeParcela) {
			case TipoDeParcela.PG:
				titulo = "Parcelas pagas";
				break;
			case TipoDeParcela.EA:
				titulo = "Parcelas em aberto";
				break;
			case TipoDeParcela.CA:
				titulo = "Parcelas canceladas";
				break;
			case TipoDeParcela.RG:
				titulo = "Parcelas renegociadas";
				break;
		}

		const modalRef = this.modal.open(
			titulo,
			DetalharParcelaComponent,
			PactoModalSize.LARGE
		);

		modalRef.componentInstance.inicio = this.filtro.inicio;
		modalRef.componentInstance.fim = this.filtro.fim;
		modalRef.componentInstance.convenios = this.filtro.convenios;
		modalRef.componentInstance.empresas = this.filtro.empresas;
		modalRef.componentInstance.situacoes = this.filtro.situacoes;
		modalRef.componentInstance.tipoDeParcela = tipoDeParcela;
		modalRef.componentInstance.tipo = "transacao";
	}

	public verAluno(aluno): void {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}
}
