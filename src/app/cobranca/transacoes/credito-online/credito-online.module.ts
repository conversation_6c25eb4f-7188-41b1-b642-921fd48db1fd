import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AcoesDeDetalhamentoModule } from "../../components/acoes-de-detalhamento/acoes-de-detalhamento.module";
import { PayFilterModule } from "../../components/pay-filter/pay-filter.module";
import { RelatorioCobrancaModule } from "../../components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusTransacaoModule } from "../../components/status-transacao/status-transacao.module";
import { CreditoOnlineRoutingModule } from "./credito-online-routing.module";
import { CreditoOnlineComponent } from "./credito-online.component";

@NgModule({
	declarations: [CreditoOnlineComponent],
	imports: [
		CommonModule,
		CreditoOnlineRoutingModule,
		BaseSharedModule,
		PayFilterModule,
		RelatorioCobrancaModule,
		StatusTransacaoModule,
		AcoesDeDetalhamentoModule,
	],
})
export class CreditoOnlineModule {}
