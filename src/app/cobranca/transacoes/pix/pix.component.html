<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'PactoPay / Transações'
		}"></pacto-breadcrumbs>

	<pacto-cat-page-title [link]="['../../']">
		<span class="pct-page-title">PIX</span>
	</pacto-cat-page-title>

	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label class="body-mini-card">{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"
		[includesStatus]="true"
		[obterConveniosPorTipo]="5"
		[obterSituacoesPorTipo]="'pix'"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<div class="flex-space indicadores">
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(9)"
			class="clickavel">
			<div card-title>Geradas</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #c98ac9">
						{{ totalizadores["GERADA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["GERADA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(13)"
			class="clickavel">
			<div card-title>Ativas</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #1998fc">
						{{ totalizadores["ATIVA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["ATIVA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(5)"
			class="clickavel">
			<div card-title>Pagas</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #2ec750">
						{{ totalizadores["PAGA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["PAGA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(7)"
			class="clickavel">
			<div card-title>Canceladas</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #db2c3d">
						{{ totalizadores["CANCELADA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["CANCELADA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
		<pacto-cat-grid-square-card
			(click)="detalharTransacao(14)"
			class="clickavel">
			<div card-title>Expiradas</div>
			<div card-body>
				<div class="flex-center">
					<h2 style="color: #b4b7bb">
						{{ totalizadores["EXPIRADA"].valor | currency : "BRL" : true }}
					</h2>
				</div>
				<div class="flex-center">
					<label>Qnt.:</label>
					<label>{{ totalizadores["EXPIRADA"].quantidade }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
	</div>

	<br />

	<div class="card">
		<pacto-relatorio-cobranca
			#relatorioTransacoes
			[emptyStateMessage]="'Não existem transações no período selecionado'"
			[showShare]="true"
			[table]="tableTransacoes"
			class="table"
			id="transacoes"></pacto-relatorio-cobranca>
		<!-- CELULAS - TRANSAÇÃO -->
		<ng-template #celulaStatusDaTransacao let-cobranca="item">
			<pacto-status-pix
				[codigo]="cobranca.codigoRetorno"
				[status]="cobranca.statusCodigo"
				[tooltip]="cobranca.descricaoRetorno"></pacto-status-pix>
		</ng-template>

		<ng-template #celulaNomeDoAluno let-transacao="item">
			<a (click)="verAluno(transacao.pessoa)" class="list-item-link">
				{{ transacao.nome | captalize }}
			</a>
		</ng-template>

		<ng-template #celulaDetalharCobranca let-cobranca="item">
			<pacto-acoes-de-detalhamento
				[cobranca]="cobranca"
				[tipo]="'pix'"></pacto-acoes-de-detalhamento>
		</ng-template>
		<!-- FIM -->
	</div>

	<br />

	<section class="row">
		<div class="col-6">
			<!-- *ngIf="totalizadoresPorConvenio.length > 1" -->
			<pacto-cat-tabs-transparent
				#tabs
				(activateTab)="(null)"
				class="tabs-creditos">
				<ng-template
					*ngFor="let convenio of totalizadoresPorConvenio"
					[label]="convenio.tipo_descricao"
					[pactoTabTransparent]="convenio.tipo_descricao">
					<div class="d-flex justify-content-around indicadores">
						<pacto-cat-grid-square-card class="card_indicadores">
							<div card-title>Ativas</div>
							<div card-body>
								<div class="flex-center">
									<h2 style="color: #1998fc">
										{{
											convenio.totalizadores["ATIVA"].valor
												| currency : "BRL" : true
										}}
									</h2>
								</div>
								<div class="flex-center">
									<label>Qnt.:</label>
									<label>
										{{ convenio.totalizadores["ATIVA"].quantidade }}
									</label>
								</div>
							</div>
						</pacto-cat-grid-square-card>
						<pacto-cat-grid-square-card class="card_indicadores">
							<div card-title>Pagas</div>
							<div card-body>
								<div class="flex-center">
									<h2 style="color: #2ec750">
										{{
											convenio.totalizadores["PAGA"].valor
												| currency : "BRL" : true
										}}
									</h2>
								</div>
								<div class="flex-center">
									<label>Qnt.:</label>
									<label>{{ convenio.totalizadores["PAGA"].quantidade }}</label>
								</div>
							</div>
						</pacto-cat-grid-square-card>
					</div>
					<div class="d-flex justify-content-around indicadores">
						<pacto-cat-grid-square-card class="card_indicadores">
							<div card-title>Canceladas</div>
							<div card-body>
								<div class="flex-center">
									<h2 style="color: #db2c3d">
										{{
											convenio.totalizadores["CANCELADA"].valor
												| currency : "BRL" : true
										}}
									</h2>
								</div>
								<div class="flex-center">
									<label>Qnt.:</label>
									<label>
										{{ convenio.totalizadores["CANCELADA"].quantidade }}
									</label>
								</div>
							</div>
						</pacto-cat-grid-square-card>
						<pacto-cat-grid-square-card class="card_indicadores">
							<div card-title>Expiradas</div>
							<div card-body>
								<div class="flex-center">
									<h2 style="color: #b4b7bb">
										{{
											convenio.totalizadores["EXPIRADA"].valor
												| currency : "BRL" : true
										}}
									</h2>
								</div>
								<div class="flex-center">
									<label>Qnt.:</label>
									<label>
										{{ convenio.totalizadores["EXPIRADA"].quantidade }}
									</label>
								</div>
							</div>
						</pacto-cat-grid-square-card>
					</div>
				</ng-template>
			</pacto-cat-tabs-transparent>
		</div>

		<div class="col-6">
			<section style="margin-top: calc(1.25rem + 45px + 1px)"></section>
			<pacto-relatorio-cobranca
				#relatorioParcelas
				(rowClick)="detalharParcelas($event.codigo)"
				[emptyStateMessage]="'Não existem parcelas no período selecionado'"
				[itensPerPage]="[
					{ id: 4, label: '04' },
					{ id: 10, label: '10' },
					{ id: 20, label: '20' },
					{ id: 30, label: '30' }
				]"
				[showShare]="false"
				class="card"
				id="parcelas"></pacto-relatorio-cobranca>
			<!-- CELULAS - PARCELAS -->
			<ng-template #parcelaTituloQtdTotal>
				<span>Qtd.: {{ parcelasTitulo?.quantidade }}</span>
			</ng-template>

			<ng-template #parcelaTituloValorTotal>
				<span class="d-flex justify-content-end">
					Total: {{ parcelasTitulo?.currency }}
				</span>
			</ng-template>

			<ng-template #celulaParcelasQtd let-transacao="item">
				<span>Qtd.: {{ transacao.quantidade }}</span>
			</ng-template>

			<ng-template #celulaParcelasValor let-transacao="item">
				<span
					[style.color]="transacao.codigo === 'EA' ? 'red' : ''"
					class="d-flex justify-content-end">
					{{ transacao.valor | currency : "BRL" : true }}
				</span>
			</ng-template>
			<!-- FIM -->
		</div>
	</section>
</pacto-cat-layout-v2>
