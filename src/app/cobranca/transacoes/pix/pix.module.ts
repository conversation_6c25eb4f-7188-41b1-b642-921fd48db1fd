import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AcoesDeDetalhamentoModule } from "../../components/acoes-de-detalhamento/acoes-de-detalhamento.module";
import { PayFilterModule } from "../../components/pay-filter/pay-filter.module";
import { RelatorioCobrancaModule } from "../../components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusPixModule } from "../../components/status-pix/status-pix.module";
import { PixRoutingModule } from "./pix-routing.module";
import { PixComponent } from "./pix.component";

@NgModule({
	declarations: [PixComponent],
	imports: [
		CommonModule,
		PixRoutingModule,
		BaseSharedModule,
		PayFilterModule,
		RelatorioCobrancaModule,
		StatusPixModule,
		AcoesDeDetalhamentoModule,
	],
})
export class PixModule {}
