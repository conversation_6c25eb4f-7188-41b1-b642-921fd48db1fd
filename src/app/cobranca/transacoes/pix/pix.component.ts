import { HttpParams } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { DadosDoFiltro, Filtro, PactoPayApiDashService } from "pactopay-api";
import {
	TotalizadorPix,
	ZwPactoPayApiDashService,
	ZwPactoPayApiPixService,
} from "zw-pactopay-api";
import { CobrancaService } from "../../cobranca.service";
import { DetalhamentoDeCreditoComponent } from "../../components/detalhamento-de-credito/detalhamento-de-credito.component";
import { PactoDataGridConfig } from "../../components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "../../components/relatorio-cobranca/relatorio-cobranca.component";
import { DetalharParcelaComponent } from "../components/detalhar-parcela/detalhar-parcela.component";
import { TipoDeParcela } from "../components/detalhar-parcela/tipo-parcela.model";
import { DetalharTransacaoComponent } from "../components/detalhar-transacao/detalhar-transacao.component";
import { SituacaoDoPix } from "../transacoes.model";
import { TotalizadoresMap, TotalizadoresPorConvenioMap } from "./pix.model";

@Component({
	selector: "pacto-pix",
	templateUrl: "./pix.component.html",
	styleUrls: ["./pix.component.scss", "../../cobranca.scss"],
})
export class PixComponent implements OnInit {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	public tableTransacoes: PactoDataGridConfig = new PactoDataGridConfig({
		state: null,
		buttons: null,
		columns: [],
	});

	@ViewChild("celulaStatusDaTransacao", { static: true })
	public celulaStatusDaTransacao: TemplateRef<any>;

	@ViewChild("celulaNomeDoAluno", { static: true })
	public celulaNomeDoAluno: TemplateRef<any>;

	@ViewChild("celulaDetalharCobranca", { static: true })
	public celulaDetalharCobranca: TemplateRef<any>;

	@ViewChild("relatorioTransacoes", { static: true })
	public relatorioTransacoes: RelatorioCobrancaComponent;

	@ViewChild("relatorioParcelas", { static: true })
	public relatorioParcelas: RelatorioCobrancaComponent;

	@ViewChild("parcelaTituloQtdTotal", { static: true })
	public parcelaTituloQtdTotal: TemplateRef<any>;

	@ViewChild("parcelaTituloValorTotal", { static: true })
	public parcelaTituloValorTotal: TemplateRef<any>;

	@ViewChild("celulaParcelasValor", { static: true })
	public celulaParcelasValor: TemplateRef<any>;

	@ViewChild("celulaParcelasQtd", { static: true })
	public celulaParcelasQtd: TemplateRef<any>;

	public parcelasTitulo = { currency: "R$ 0,00", quantidade: 0 };

	public ultimaExecucao: string;
	public descricaoCredito: string;
	public creditoPacto: number;
	public utilizados: boolean;

	public totalizadores: TotalizadoresMap = {};
	public totalizadoresPorConvenio: TotalizadoresPorConvenioMap[] = [];
	public totalizadoresPorParcela: TotalizadorPix[] = [];

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly cobrancaService: CobrancaService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService,
		private readonly pactoPayApiDash: PactoPayApiDashService,
		private readonly zwPactoPayApiPix: ZwPactoPayApiPixService,
		private readonly rest: RestService,
		private readonly sessionService: SessionService,
		private readonly router: Router,
		private readonly modal: ModalService
	) {}

	ngOnInit() {
		this.cobrancaService.definirTitle("PactoPay - Cartão de crédito online");

		this.buscarCreditoPacto();

		this.tableTransacoes = this.gridConfigTransacoes();

		this.totalizadores = this.inicializarTotalizadores();
	}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.buscarTotalizadores(this.filtro);
		this.buscarTotalizadoresPorConvenio(this.filtro);

		this.reloadDataTransacoes(this.filtro);
		this.reloadDataParcelas(this.filtro);

		this.pactoPayApiDash.obterUltimaExecucaoPix().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});

		this.cd.detectChanges();
	}

	private buscarCreditoPacto(): void {
		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});
	}

	private buscarTotalizadores(filtro: Filtro): void {
		this.zwPactoPayApiPix
			.buscarTotalizadores(filtro)
			.subscribe((totalizadores) => {
				this.totalizadores = this.mapearTotalizadores(totalizadores);
				this.cd.detectChanges();
			});
	}

	private buscarTotalizadoresPorConvenio(filtro: Filtro): void {
		this.zwPactoPayApiPix
			.buscarTotalizadoresPorConvenio(filtro)
			.subscribe((convenios) => {
				this.totalizadoresPorConvenio = convenios.map(
					({ totalizadores, tipo_codigo, tipo_descricao }) => {
						return {
							tipo_descricao,
							tipo_codigo,
							totalizadores: this.mapearTotalizadores(totalizadores),
						};
					}
				);
				this.cd.detectChanges();
			});
	}

	private mapearTotalizadores(
		totalizadores: TotalizadorPix[]
	): TotalizadoresMap {
		const mapeado: TotalizadoresMap = this.inicializarTotalizadores();
		for (const totalizador of totalizadores) {
			mapeado[totalizador.descricao] = totalizador;
		}
		return mapeado;
	}

	private inicializarTotalizadores() {
		const vazio: TotalizadorPix = {
			codigo: "",
			valor: 0,
			quantidade: 0,
			descricao: "",
		};

		const mapeado: TotalizadoresMap = {};
		mapeado["GERADA"] = vazio;
		mapeado["ATIVA"] = vazio;
		mapeado["PAGA"] = vazio;
		mapeado["CANCELADA"] = vazio;
		mapeado["EXPIRADA"] = vazio;
		return mapeado;
	}

	private gridConfigTransacoes(): PactoDataGridConfig {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "consultar");

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/pix?${params.toString()}`
			),
			endpointParamsType: "json",
			quickSearch: true,
			exportButton: true,
			showFilters: true,
			rowClick: false,
			ghostLoad: true,
			ghostAmount: 10,
			columns: [
				{
					nome: "txId",
					titulo: "Cód. TxId",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeDoAluno,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					visible: true,
					celula: this.celulaStatusDaTransacao,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					valueTransform(value: number = 0) {
						return value.toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							style: "currency",
							currency: "BRL",
						});
					},
					ordenavel: false,
				},
				{
					nome: "",
					titulo: "",
					visible: true,
					celula: this.celulaDetalharCobranca,
					ordenavel: false,
				},
			],
		});
	}

	private reloadDataTransacoes(filtro: Filtro): void {
		// this.relatorioTransacoes.table = this.gridConfigTransacoes();
		this.relatorioTransacoes.addFilter("inicio", filtro.inicio);
		this.relatorioTransacoes.addFilter("fim", filtro.fim);
		this.relatorioTransacoes.addFilter("convenios", filtro.convenios);
		this.relatorioTransacoes.addFilter("empresas", filtro.empresas);
		this.relatorioTransacoes.addFilter("situacoes", filtro.situacoes);
		this.relatorioTransacoes.reloadData();

		this.cd.detectChanges();
	}

	private gridConfigParcelas(): PactoDataGridConfig {
		const params = new HttpParams()
			.append("key", this.sessionService.chave)
			.append("op", "totalizador_parcela");

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/pix?${params.toString()}`
			),
			endpointParamsType: "json",
			pagination: false,
			rowClick: true,
			dataAdapterFn: (serverData: any) => {
				const quantidade = serverData.content
					.map((item) => item.quantidade)
					.reduce((prev, curr) => prev + curr, 0);
				const valor = serverData.content
					.map((item) => item.valor)
					.reduce((prev, curr) => prev + curr, 0);
				const currency = valor.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
				});
				this.parcelasTitulo = { currency, quantidade };
				return serverData;
			},
			ghostLoad: true,
			ghostAmount: 10,
			columns: [
				{
					nome: "descricao",
					titulo: "Parcelas",
					visible: true,
					ordenavel: false,
					valueTransform(word: string = "") {
						const splitStr = word.toLowerCase().split(" ");
						for (let i = 0; i < splitStr.length; i++) {
							splitStr[i] =
								splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
						}
						return splitStr.join(" ");
					},
				},
				{
					nome: "quantidade",
					titulo: this.parcelaTituloQtdTotal,
					visible: true,
					ordenavel: false,
					celula: this.celulaParcelasQtd,
				},
				{
					nome: "valor",
					titulo: this.parcelaTituloValorTotal,
					visible: true,
					ordenavel: false,
					celula: this.celulaParcelasValor,
				},
			],
		});
	}

	private reloadDataParcelas(filtro: Filtro): void {
		this.relatorioParcelas.table = this.gridConfigParcelas();
		this.relatorioParcelas.addFilter("inicio", filtro.inicio);
		this.relatorioParcelas.addFilter("fim", filtro.fim);
		this.relatorioParcelas.addFilter("convenios", filtro.convenios);
		this.relatorioParcelas.addFilter("empresas", filtro.empresas);
		this.relatorioParcelas.addFilter("situacoes", filtro.situacoes);
		this.relatorioParcelas.reloadData();

		this.cd.detectChanges();
	}

	public detalharParcelas(tipoDeParcela: TipoDeParcela) {
		let titulo = "Parcelas";

		switch (tipoDeParcela) {
			case TipoDeParcela.PG:
				titulo = "Parcelas pagas";
				break;
			case TipoDeParcela.EA:
				titulo = "Parcelas em aberto";
				break;
			case TipoDeParcela.CA:
				titulo = "Parcelas canceladas";
				break;
			case TipoDeParcela.RG:
				titulo = "Parcelas renegociadas";
				break;
		}

		const modalRef = this.modal.open(
			titulo,
			DetalharParcelaComponent,
			PactoModalSize.LARGE
		);

		modalRef.componentInstance.inicio = this.filtro.inicio;
		modalRef.componentInstance.fim = this.filtro.fim;
		modalRef.componentInstance.convenios = this.filtro.convenios;
		modalRef.componentInstance.empresas = this.filtro.empresas;
		modalRef.componentInstance.situacoes = this.filtro.situacoes;
		modalRef.componentInstance.tipoDeParcela = tipoDeParcela;
		modalRef.componentInstance.tipo = "pix";
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modalRef = this.modal.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modalRef.componentInstance.empresas = this.filtro.empresas;
		}
	}

	public detalharTransacao(status: SituacaoDoPix): void {
		let titulo = "Transações";
		// let allowsCheck = {};

		switch (status) {
			case SituacaoDoPix.GERADA:
				titulo = "Transações geradas";
				break;
			case SituacaoDoPix.ATIVA:
				titulo = "Transações ativas";
				break;
			case SituacaoDoPix.PAGA:
				titulo = "Transações pagas";
				break;
			case SituacaoDoPix.CANCELADA:
				titulo = "Transações canceladas";
				break;
			case SituacaoDoPix.EXPIRADA:
				titulo = "Transações expiradas";
				break;
		}

		const modalRef = this.modal.open(
			titulo,
			DetalharTransacaoComponent,
			PactoModalSize.LARGE
		);

		modalRef.componentInstance.inicio = this.filtro.inicio;
		modalRef.componentInstance.fim = this.filtro.fim;
		modalRef.componentInstance.convenios = this.filtro.convenios;
		modalRef.componentInstance.empresas = this.filtro.empresas;
		modalRef.componentInstance.situacoes = status;
		// modalRef.componentInstance.allowsCheck = allowsCheck;
		modalRef.componentInstance.tipo = "pix";
		modalRef.componentInstance.reloadData.subscribe((atualiza) => {
			if (atualiza) {
				this.buscarTotalizadores(this.filtro);
				this.buscarTotalizadoresPorConvenio(this.filtro);

				this.reloadDataTransacoes(this.filtro);
				this.reloadDataParcelas(this.filtro);

				this.cd.detectChanges();
			}
		});
	}

	public verAluno(aluno): void {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}
}
