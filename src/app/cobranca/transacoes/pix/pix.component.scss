@import "dist/ui-kit/assets/import.scss";

.indicadores {
	margin-top: 1.25rem;

	.clickavel {
		cursor: pointer;
	}

	pacto-cat-grid-square-card::ng-deep {
		max-width: 10.875rem;
		width: 100%;
		display: grid;

		justify-content: center;
		align-content: center;
		height: 10.5rem;
		border: 1px solid #e1e2e4;
		border-radius: 10px;
		color: $preto05;
		background: #ffffff;
		font-style: normal;

		.grid-card-title {
			padding: 0.313rem;
			font-size: 0.875rem !important;
		}

		h2 {
			font-size: 1.25rem !important;
			line-height: 1.5rem !important;
			font-weight: bold;
			color: $preto03;
		}

		label {
			font-weight: normal;
			font-size: 0.875rem;
			line-height: 1.375rem;
		}
	}
}

.card {
	box-shadow: 0px 2px 4px 0px #e4e5e6;
	background-color: #fff;
}

.list-item-link {
	font-weight: bold;
	color: #026abc !important;
	cursor: pointer;
	font-weight: bold;
	font-size: 12px;
	line-height: 16px;
}

.minicard.clickavel {
	cursor: pointer;
}

.clickavel {
	&:hover {
		box-sizing: border-box;
		box-shadow: 0px 4px 6px #e4e5e6;
	}
}

.card_indicadores {
	&:hover {
		box-sizing: border-box;
		box-shadow: 0px 4px 6px #e4e5e6;
	}
}
