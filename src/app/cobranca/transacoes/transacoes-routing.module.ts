import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

const routes: Routes = [
	{
		path: "credito-online",
		loadChildren: () =>
			import("./credito-online/credito-online.module").then(
				(m) => m.CreditoOnlineModule
			),
	},
	{
		path: "pix",
		loadChildren: () => import("./pix/pix.module").then((m) => m.PixModule),
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class TransacoesRoutingModule {}
