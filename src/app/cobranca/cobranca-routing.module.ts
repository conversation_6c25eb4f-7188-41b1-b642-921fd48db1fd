import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AlunoComponent } from "./aluno/aluno.component";
import { HomeComponent } from "./home/<USER>";

const routes: Routes = [
	{
		path: "",
		redirectTo: "home",
	},
	{
		path: "home",
		component: HomeComponent,
	},
	{
		path: "dashboard",
		loadChildren: () =>
			import("./dashboard/dashboard.module").then((m) => m.DashboardModule),
	},
	{
		path: "cartao",
		loadChildren: () =>
			import("./cartao/cartao.module").then((m) => m.CartaoModule),
	},
	{
		path: "parcelas",
		loadChildren: () =>
			import("./parcelas/parcelas.module").then((m) => m.ParcelasModule),
	},
	{
		path: "transacoes",
		loadChildren: () =>
			import("./transacoes/transacoes.module").then((m) => m.TransacoesModule),
	},
	{
		path: "regua",
		loadChildren: () =>
			import("./regua/regua.module").then((m) => m.ReguaModule),
	},
	{
		path: "aluno/:pessoa",
		component: AlunoComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class CobrancaRoutingModule {}
