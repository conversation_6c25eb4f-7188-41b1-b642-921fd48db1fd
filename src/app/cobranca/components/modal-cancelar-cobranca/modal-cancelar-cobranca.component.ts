import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ZwPactoPayApiTransacaoService } from "zw-pactopay-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-cancelar-cobranca",
	templateUrl: "./modal-cancelar-cobranca.component.html",
	styleUrls: ["./modal-cancelar-cobranca.component.scss"],
})
export class ModalCancelarCobrancaComponent implements OnInit {
	@Input() resultado =
		"Ao cancelar essa transação, o recibo também será estornado e a parcela do aluno ficará em aberto. " +
		"Caso haja nota fiscal emitida, você deve realizar o cancelamento manualmente da nota fiscal.";
	@Input() transacoes: any;
	@Output() acoes: EventEmitter<any> = new EventEmitter();

	constructor(
		private openModal: NgbActiveModal,
		private notify: SnotifyService,
		private zwPactoPayApiTransacao: ZwPactoPayApiTransacaoService,
		private sessionService: SessionService
	) {}

	ngOnInit() {}

	fechar() {
		this.openModal.close();
	}

	cancelar() {
		const username = this.sessionService.loggedUser.username;
		const transacoes = [this.transacoes];
		this.zwPactoPayApiTransacao
			.cancelarTransacao({ username, transacoes })
			.subscribe((result) => {
				if (result.error) {
					this.notify.error(result.message);
				} else {
					this.acoes.emit("atualizarListaCartao");
					this.notify.success(result);
					this.openModal.close();
				}
			});
	}
}
