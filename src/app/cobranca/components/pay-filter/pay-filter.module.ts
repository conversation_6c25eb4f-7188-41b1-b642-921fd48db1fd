import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { ClientSearchComponent } from "./client-search/client-search.component";
import { PayFilterInfoComponent } from "./pay-filter-info/pay-filter-info.component";
import { PayFilterComponent } from "./pay-filter.component";

@NgModule({
	declarations: [
		PayFilterComponent,
		PayFilterInfoComponent,
		ClientSearchComponent,
	],
	imports: [CommonModule, BaseSharedModule],
	exports: [PayFilterComponent, PayFilterInfoComponent],
})
export class PayFilterModule {}
