<div class="filter-info-wrapper">
	<span style="margin-right: 24px; align-self: normal">
		Resultado do filtro:
	</span>

	<div
		*ngIf="
			filtro?.periodoSelecionado === 'HOJE' ||
			filtro?.periodoSelecionado === 'ONTEM'
		"
		class="filter-info">
		<strong>Data:</strong>
		{{ inicio | date : "dd/MM/yyyy" }}
	</div>
	<div
		*ngIf="
			filtro?.periodoSelecionado !== 'HOJE' &&
			filtro?.periodoSelecionado !== 'ONTEM'
		"
		class="filter-info">
		<strong>Período:</strong>
		{{ inicio | date : "dd/MM/yyyy" }} à {{ fim | date : "dd/MM/yyyy" }}
	</div>

	<ng-container *ngFor="let empresa of empresasChecked">
		<div class="filter-info">
			<span>
				<strong>Empresa:</strong>
				{{ empresa.label | captalize }}
			</span>
			<i (click)="removeEmpresa(empresa)" class="pct pct-x-circle"></i>
		</div>
	</ng-container>

	<ng-container *ngFor="let convenio of conveniosChecked">
		<div class="filter-info">
			<span>
				<strong>Convênio:</strong>
				{{ convenio.label | captalize }}
			</span>
			<i (click)="removeConvenio(convenio)" class="pct pct-x-circle"></i>
		</div>
	</ng-container>

	<ng-container *ngFor="let situacao of situacoesChecked">
		<div class="filter-info">
			<span>
				<strong>Situação:</strong>
				{{ situacao.label }}
			</span>
			<i (click)="removeSituacao(situacao)" class="pct pct-x-circle"></i>
		</div>
	</ng-container>

	<ng-container *ngFor="let usuario of usuariosChecked">
		<div class="filter-info">
			<span>
				<strong>Usuário:</strong>
				{{ usuario.label }}
			</span>
			<i (click)="removeUsuario(usuario)" class="pct pct-x-circle"></i>
		</div>
	</ng-container>
</div>
