import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	SimpleChanges,
} from "@angular/core";
import * as moment from "moment";

@Component({
	selector: "pacto-pay-filter-info",
	templateUrl: "./pay-filter-info.component.html",
	styleUrls: ["./pay-filter-info.component.scss"],
})
export class PayFilterInfoComponent implements OnInit, OnChanges {
	@Input() filtro;
	@Output() removerFiltro: EventEmitter<any> = new EventEmitter();

	public situacoesChecked: Array<{
		checked: boolean;
		value: number;
		label: string;
	}> = [];
	public empresasChecked: Array<{
		checked: boolean;
		value: number;
		label: string;
	}> = [];
	public conveniosChecked: Array<{
		checked: boolean;
		value: number;
		label: string;
	}> = [];
	public usuariosChecked: Array<{
		checked: boolean;
		value: number;
		label: string;
	}> = [];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {}

	ngOnChanges(changes: SimpleChanges): void {
		const { filtro } = changes;
		if (!filtro.firstChange) {
			this.situacoesChecked = filtro.currentValue.situacoes;
			this.empresasChecked = filtro.currentValue.empresas;
			this.conveniosChecked = filtro.currentValue.convenios;
			this.usuariosChecked = filtro.currentValue.usuarios;
			this.cd.detectChanges();
		}
	}

	public get inicio() {
		if (this.filtro) {
			return moment(this.filtro.periodo.inicio);
		} else {
			return "";
		}
	}

	public get fim() {
		if (this.filtro) {
			return moment(this.filtro.periodo.fim);
		} else {
			return "";
		}
	}

	public removeSituacao(selecionada: {
		checked: boolean;
		value: number;
		label: string;
	}) {
		selecionada.checked = false;
		this.removerFiltro.emit({
			situacao: selecionada,
		});
	}

	public removeEmpresa(selecionada: {
		checked: boolean;
		value: number;
		label: string;
	}) {
		selecionada.checked = false;
		this.removerFiltro.emit({
			empresa: selecionada,
		});
	}

	public removeConvenio(selecionada: {
		checked: boolean;
		value: number;
		label: string;
	}) {
		selecionada.checked = false;
		this.removerFiltro.emit({
			convenio: selecionada,
		});
	}

	public removeUsuario(selecionada: {
		checked: boolean;
		value: number;
		label: string;
	}) {
		selecionada.checked = false;
		this.removerFiltro.emit({
			usuario: selecionada,
		});
	}
}
