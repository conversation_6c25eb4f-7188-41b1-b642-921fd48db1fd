@import "dist/ui-kit/assets/import.scss";

.grid-filter {
	margin: 10px 0px 32px 0px;
	display: flex;
	justify-content: space-between;

	#filtros-dropdown {
		width: 68px;
		height: 42px;
		background: #0380e3;
		border-radius: 6px;
		color: #ffffff;
		border: none;
		font-size: 18px;
		cursor: pointer;
	}

	.nome-filter {
		border: 1px solid #c7c9cc;
		padding: 0px 30px 0px 10px;
		line-height: 40px;
		color: #51555a;
		outline: 0px !important;
		box-sizing: border-box;
		border-radius: 6px;
		width: 47%;
		background-color: white;
		font-size: 14px;
		display: flex;
		@extend .type-p-small-rounded;

		.divisor {
			width: 24px;
			height: 0px;
			border: 1px solid #dcdddf;
			transform: rotate(90deg);
			margin-top: 19px;
		}

		i {
			font-size: 20px;
			color: $cinza07;
			position: relative;
			left: 0;
			top: 9px;
		}

		input {
			border: none;
			width: 95%;
		}

		&:focus {
			box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
		}
	}

	.buttons-filter {
		background: #ffffff;
		border-radius: 6px;
		width: 500px;
		justify-content: space-around;
		display: flex;

		.button-filter {
			align-self: center;
		}
	}

	.refine-button {
		width: 4%;
	}
}

.line {
	background: #f3f3f4;
	height: 1px;
	width: 99%;
	margin: 12px 0px;
}

.drop-down-section {
	display: flex;
	cursor: pointer;
	align-items: flex-end;
}

.section {
	// overflow: hidden;
	// position:relative;
	width: 100%;

	.item {
		font-weight: normal;
		font-size: 14px;
		line-height: 20px;
		color: #383b3e;
		padding-left: 25px;
		margin: 12px 0px;
		cursor: pointer;

		input {
			position: initial;
			margin-right: 8px;
		}
	}
}

.section-title {
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	line-height: 16px;
	color: #383b3e;
}

::ng-deep.datepicker {
	.nome {
		display: none;
	}

	.pct-error-msg {
		display: none;
	}
}

.setinha-pra-baixo {
	margin-right: 6px;
	font-size: 12px;
	margin-bottom: 5px;
}

.setinha-pra-cima {
	margin-right: 6px;
	font-size: 12px;
	margin-top: 5px;
}

.buscar {
	height: 36px;
	background: #0380e3;
	border-radius: 4px;
	border: none;
	color: white;
	width: 100%;
	margin-top: 12px;
	cursor: pointer;
}
