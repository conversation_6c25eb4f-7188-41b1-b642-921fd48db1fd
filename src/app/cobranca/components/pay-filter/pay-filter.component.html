<div class="grid-filter">
	<pacto-client-search class="nome-filter"></pacto-client-search>

	<div class="buttons-filter">
		<pacto-cat-button
			(click)="selecionaPeriodo('ONTEM')"
			[label]="'Ontem'"
			[type]="
				periodoSelecionado === 'ONTEM'
					? 'PRIMARY_NO_TEXT_TRANSFORM'
					: 'NO_BORDER'
			"
			class="button-filter"></pacto-cat-button>
		<pacto-cat-button
			(click)="selecionaPeriodo('HOJE')"
			[label]="'Hoje'"
			[type]="
				periodoSelecionado === 'HOJE'
					? 'PRIMARY_NO_TEXT_TRANSFORM'
					: 'NO_BORDER'
			"
			class="button-filter"></pacto-cat-button>
		<pacto-cat-button
			(click)="selecionaPeriodo('ESTE_MES')"
			[label]="'Este mês'"
			[type]="
				periodoSelecionado === 'ESTE_MES'
					? 'PRIMARY_NO_TEXT_TRANSFORM'
					: 'NO_BORDER'
			"
			class="button-filter"></pacto-cat-button>
		<pacto-cat-button
			(click)="selecionaPeriodo('MES_ANTERIOR')"
			[label]="'Mês anterior'"
			[type]="
				periodoSelecionado === 'MES_ANTERIOR'
					? 'PRIMARY_NO_TEXT_TRANSFORM'
					: 'NO_BORDER'
			"
			class="button-filter"></pacto-cat-button>
	</div>

	<div class="filter-wrapper">
		<div
			#filterDropdown="ngbDropdown"
			[autoClose]="false"
			[placement]="'bottom-right'"
			class="d-inline-block"
			ngbDropdown>
			<button
				#filterToggleButton
				class="btn btn-primary"
				id="filtros-dropdown"
				ngbDropdownToggle>
				<i class="pct pct-filter"></i>
			</button>

			<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
				<div style="padding: 0px 14px; width: 250px">
					<div
						(click)="toggleFilterVisibility(0)"
						*ngIf="includesPeriodo"
						[ngClass]="{ closed: !isOpen(0) }"
						class="drop-down-section">
						<i
							*ngIf="!isOpen(0)"
							class="pct pct-caret-down setinha-pra-baixo"></i>
						<i *ngIf="isOpen(0)" class="pct pct-caret-up setinha-pra-cima"></i>
						<span class="section-title">Período</span>
					</div>
					<div
						*ngIf="includesPeriodo"
						[@openClose]="isOpen(0) ? 'true' : 'false'"
						class="section">
						<div *ngIf="isOpen(0)">
							<div style="height: 36px; margin: 2px">
								<pacto-cat-datepicker
									[formControl]="
										periodoFormGroup.controls['inicio']
									"></pacto-cat-datepicker>
							</div>
							<span>até</span>
							<div style="height: 36px; margin: 2px">
								<pacto-cat-datepicker
									[formControl]="
										periodoFormGroup.controls['fim']
									"></pacto-cat-datepicker>
							</div>
						</div>
					</div>

					<div *ngIf="includesEmpresa" class="line"></div>
					<div
						(click)="toggleFilterVisibility(1)"
						*ngIf="includesEmpresa"
						[ngClass]="{ closed: !isOpen(1) }"
						class="drop-down-section">
						<i
							*ngIf="!isOpen(1)"
							class="pct pct-caret-down setinha-pra-baixo"></i>
						<i *ngIf="isOpen(1)" class="pct pct-caret-up setinha-pra-cima"></i>
						<span class="section-title">Empresas</span>
					</div>
					<div
						*ngIf="includesEmpresa"
						[@openClose]="isOpen(1) ? 'true' : 'false'"
						class="section">
						<div *ngIf="isOpen(1)">
							<div *ngFor="let empresa of empresas; index as i" class="item">
								<input
									[formControl]="empresasFormGroup.controls[empresa.label]"
									checked="empresasFormGroup.controls[empresa.label].value"
									class="form-check-input"
									id="empresa-{{ i }}"
									type="checkbox" />
								<label
									class="form-check-label"
									for="empresa-{{ i }}"
									style="display: revert">
									{{ empresa.label | captalize }}
								</label>
							</div>
						</div>
					</div>

					<div *ngIf="includesConvenio" class="line"></div>
					<div
						(click)="toggleFilterVisibility(2)"
						*ngIf="includesConvenio"
						[ngClass]="{ closed: !isOpen(2) }"
						class="drop-down-section">
						<i
							*ngIf="!isOpen(2)"
							class="pct pct-caret-down setinha-pra-baixo"></i>
						<i *ngIf="isOpen(2)" class="pct pct-caret-up setinha-pra-cima"></i>
						<span class="section-title">Convênios</span>
					</div>
					<div
						*ngIf="includesConvenio"
						[@openClose]="isOpen(2) ? 'true' : 'false'"
						class="section">
						<div *ngIf="isOpen(2)">
							<div *ngFor="let convenio of convenios; index as i" class="item">
								<input
									[formControl]="conveniosFormGroup.controls[convenio.label]"
									checked="conveniosFormGroup.controls[convenio.label].value"
									class="form-check-input"
									id="convenio-{{ i }}"
									type="checkbox" />
								<label
									class="form-check-label"
									for="convenio-{{ i }}"
									style="display: revert">
									{{ convenio.label | captalize }}
								</label>
							</div>
						</div>
					</div>

					<div *ngIf="includesStatus" class="line"></div>
					<div
						(click)="toggleFilterVisibility(3)"
						*ngIf="includesStatus"
						[ngClass]="{ closed: !isOpen(3) }"
						class="drop-down-section">
						<i
							*ngIf="!isOpen(3)"
							class="pct pct-caret-down setinha-pra-baixo"></i>
						<i *ngIf="isOpen(3)" class="pct pct-caret-up setinha-pra-cima"></i>
						<span class="section-title">Situações</span>
					</div>
					<div
						*ngIf="includesStatus"
						[@openClose]="isOpen(3) ? 'true' : 'false'"
						class="section">
						<div *ngIf="isOpen(3)">
							<div *ngFor="let situacao of situacoes; index as i" class="item">
								<input
									[formControl]="situacoesFormGroup.controls[situacao.label]"
									checked="situacoesFormGroup.controls[situacao.label].value"
									class="form-check-input"
									id="situacao-{{ i }}"
									type="checkbox" />
								<label class="form-check-label" for="situacao-{{ i }}">
									{{ situacao.label | captalize }}
								</label>
							</div>
						</div>
					</div>

					<div *ngIf="includesUsuario" class="line"></div>
					<div
						(click)="toggleFilterVisibility(4)"
						*ngIf="includesUsuario"
						[ngClass]="{ closed: !isOpen(4) }"
						class="drop-down-section">
						<i
							*ngIf="!isOpen(4)"
							class="pct pct-caret-down setinha-pra-baixo"></i>
						<i *ngIf="isOpen(4)" class="pct pct-caret-up setinha-pra-cima"></i>
						<span class="section-title">Usuários</span>
					</div>
					<div
						*ngIf="includesUsuario"
						[@openClose]="isOpen(4) ? 'true' : 'false'"
						class="section">
						<div *ngIf="isOpen(4)">
							<div *ngFor="let usuario of usuarios; index as i" class="item">
								<input
									[formControl]="usuariosFormGroup.controls[usuario.label]"
									checked="usuariosFormGroup.controls[usuario.label].value"
									class="form-check-input"
									id="usuario-{{ i }}"
									type="checkbox" />
								<label class="form-check-label" for="usuario-{{ i }}">
									{{ usuario.label | captalize }}
								</label>
							</div>
						</div>
					</div>

					<div>
						<button (click)="buscar()" class="buscar">
							<i class="pct pct-search"></i>
							Buscar
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
