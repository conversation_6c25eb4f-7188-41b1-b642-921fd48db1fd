<i class="pct pct-search"></i>
<div class="container">
	<div class="divisor"></div>
	<input
		[formControl]="searchFc"
		placeholder="Buscar por alunos..."
		type="text" />

	<div *ngIf="open" class="search-results">
		<ng-container>
			<ng-container *ngIf="resultPeople && resultPeople.length">
				<div *ngIf="resultPeople && resultPeople.length" class="result-list">
					<a
						(click)="navegarTelaAluno(cliente)"
						*ngFor="let cliente of resultPeople"
						class="cliente">
						<img src="{{ cliente.foto }}" />
						<div class="nome">{{ cliente.nome }}</div>
					</a>
				</div>
			</ng-container>
			<div *ngIf="!hasResults" class="empty">
				<div>Sem resultado...</div>
			</div>
		</ng-container>
	</div>
</div>
