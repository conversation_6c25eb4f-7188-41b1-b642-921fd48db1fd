import {
	ChangeDetectorR<PERSON>,
	Component,
	<PERSON>ement<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router } from "@angular/router";
import { Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";

@Component({
	selector: "pacto-client-search",
	templateUrl: "./client-search.component.html",
	styleUrls: ["./client-search.component.scss"],
})
export class ClientSearchComponent implements OnInit, OnDestroy {
	private peopleSearchSub: Subscription;
	searchFc: FormControl = new FormControl();
	resultPeople: Array<any>;
	open = false;
	hasResults = false;

	constructor(
		private cd: ChangeDetectorRef,
		private zwPactoPayApiDash: ZwPactoPayApiDashService,
		private router: Router,
		private ref: ElementRef
	) {}

	ngOnInit() {
		this.setupEvents();
	}

	@HostListener("document:click", ["$event"])
	public clickHandler($event: MouseEvent) {
		const innerClick = this.isDescendant(this.ref.nativeElement, $event.target);
		if (!innerClick) {
			this.open = false;
			this.cd.detectChanges();
		}
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private setupEvents() {
		this.peopleSearchSub = this.searchFc.valueChanges
			.pipe(debounceTime(300))
			.subscribe((value) => this.search(value));
	}

	private search(term) {
		if (term && term.trim()) {
			this.open = true;
			this.resultPeople = [];
			this.cd.detectChanges();

			this.peopleSearchSub = this.zwPactoPayApiDash
				.consultarClientes(term.trim())
				.subscribe((result) => {
					this.resultPeople = result;
					this.hasResults = this.resultPeople.length > 0;
					this.cd.detectChanges();
				});
		}
	}

	ngOnDestroy() {
		if (this.peopleSearchSub) {
			this.peopleSearchSub.unsubscribe();
		}
	}

	navegarTelaAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}
}
