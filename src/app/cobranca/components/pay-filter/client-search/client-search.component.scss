@import "dist/ui-kit/assets/import.scss";

.divisor {
	width: 24px;
	height: 0px;
	border: 1px solid #dcdddf;
	transform: rotate(90deg);
	margin-top: 19px;
	margin-left: -10px;
	position: absolute;
}

i {
	font-size: 20px;
	color: $cinza07;
	position: relative;
	left: 0;
	top: 9px;
}

input {
	border: none;
	padding-left: 10px;
	margin-top: 0px;
}

.empty {
	padding-left: 25px;
}

.container {
	position: relative;
	width: 100%;
}

.search-results {
	background-color: #ffffff;
	padding: 10px 0px 14px;
	box-shadow: 0px 2px 4px 3px #dcdcdca6;
	max-height: 125px;
	overflow-y: auto;
	margin-left: -45px;
	position: absolute;
	width: 559px;
	z-index: 10;
}

.result-section-name {
	color: $cinza05;
	@extend .type-h6;
	padding: 20px 24px 0px;

	&:first-of-type {
		padding-top: 0px;
	}
}

.cliente {
	align-items: center;
	display: flex;
	cursor: pointer;
	padding: 10px 24px;
	text-decoration: none;

	&:hover {
		background-color: #eff2f7;
	}

	img {
		width: 32px;
		height: 32px;
		border-radius: 16px;
		margin-right: 12px;
	}

	.nome {
		@extend .type-h6;
		color: $pretoPri;
	}
}
