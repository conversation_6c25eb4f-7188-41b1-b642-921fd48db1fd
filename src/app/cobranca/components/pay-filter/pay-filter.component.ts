import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import {
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	SimpleChanges,
	ViewChild,
} from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment";
import { SnotifyService } from "ng-snotify";
import {
	DadosDoFiltro,
	Filtro,
	MenuPeriodo,
	PactoPayApiFiltroService,
} from "pactopay-api";
import { ZwPactoPayApiUtilService } from "zw-pactopay-api";

@Component({
	selector: "pacto-pay-filter",
	templateUrl: "./pay-filter.component.html",
	styleUrls: ["./pay-filter.component.scss"],
	animations: [
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class PayFilterComponent implements OnInit, OnChanges {
	@Input() preservarFiltro = false;
	@Input() includesPeriodo = false;
	@Input() includesStatus = false;
	@Input() includesConvenio = false;
	@Input() includesEmpresa = false;
	@Input() includesUsuario = false;

	@Input() obterConveniosPorTipo: number;
	@Input() obterSituacoesPorTipo: string;

	private initPeriodo: any = null;
	private initSituacoes: Array<number> = [];
	private initConvenios: Array<number> = [];
	private initEmpresas: Array<number> = [];
	private initUsuario: Array<number> = [];

	@Output() obterFiltro: EventEmitter<any> = new EventEmitter<{
		dados: DadosDoFiltro;
		filtro: Filtro;
	}>();

	public periodoFormGroup: FormGroup;
	public situacoesFormGroup: FormGroup;
	public conveniosFormGroup: FormGroup;
	public empresasFormGroup: FormGroup;
	public usuariosFormGroup: FormGroup;

	public periodo: Array<any> = [];
	public situacoes: Array<{ value: number; label: string }> = [];
	public convenios: Array<{
		value: number;
		label: string;
		empresas: Array<number>;
	}> = [];
	public empresas: Array<{ value: number; label: string }> = [];
	// 3 - Usuário Recorrência no ZW => 0 - Referência para todos os usuários diferente de Recorrência
	public usuarios: Array<{ value: number; label: string }> = [
		{ value: 3, label: "Automático" },
		{ value: 0, label: "Manual" },
	];

	public periodoSelecionado: MenuPeriodo = MenuPeriodo.ESTE_MES;

	public open: Array<boolean> = [];
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;

	constructor(
		private readonly pactoPayApiFiltro: PactoPayApiFiltroService,
		private readonly zwPactoPayApiUtil: ZwPactoPayApiUtilService,
		private readonly session: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly fb: FormBuilder
	) {}

	ngOnInit() {
		if (this.preservarFiltro) {
			const filtro = JSON.parse(sessionStorage.getItem("pay-filter:filtro"));
			if (filtro) {
				this.initPeriodo = { inicio: filtro.inicio, fim: filtro.fim };
				this.initSituacoes = filtro.situacoes
					? filtro.situacoes.split(",").map((f) => parseInt(f, 0))
					: [];
				this.initConvenios = filtro.convenios
					? filtro.convenios.split(",").map((f) => parseInt(f, 0))
					: [];
				this.initEmpresas = filtro.empresas
					? filtro.empresas.split(",").map((f) => parseInt(f, 0))
					: [];
				this.periodoSelecionado = filtro.periodoSelecionado
					? filtro.periodoSelecionado
					: MenuPeriodo.CUSTOMIZADO;
				this.initUsuario = filtro.usuarios
					? filtro.usuarios.split(",").map((f) => parseInt(f, 0))
					: [];
			} else {
				this.initEmpresas = [parseInt(this.session.empresaId, 0)];
			}
		} else {
			sessionStorage.removeItem("pay-filter:filtro");
		}

		this.criarControls();
	}

	ngOnChanges(changes: SimpleChanges): void {}

	private criarControls() {
		const promises = [];
		if (this.includesStatus) {
			promises.push(
				this.zwPactoPayApiUtil
					.obterSituacoesPorTipo(this.obterSituacoesPorTipo)
					.toPromise()
			);
		}
		if (this.includesConvenio) {
			if (this.obterConveniosPorTipo) {
				promises.push(
					this.zwPactoPayApiUtil
						.obterConveniosPorTipo(this.obterConveniosPorTipo)
						.toPromise()
				);
			} else {
				promises.push(this.pactoPayApiFiltro.obterConvenios().toPromise());
			}
		}
		if (this.includesEmpresa) {
			promises.push(this.pactoPayApiFiltro.obterEmpresas().toPromise());
		}

		this.buildUsuarios(this.usuarios);

		Promise.all(promises).then((res) => {
			let index = 0;

			if (this.includesPeriodo) {
				this.buildControlsPeriodo(this.initPeriodo);
			}

			if (this.includesStatus) {
				this.situacoes = res[index++].map((status) => {
					return { value: status.codigo, label: status.descricao };
				});
				this.buildControlsSituacoes(this.situacoes);
			}

			if (this.includesConvenio) {
				this.convenios = res[index++].map((convenio) => {
					return {
						value: convenio.codigo,
						label: convenio.descricao,
						empresas: convenio.empresas,
					};
				});
				this.buildControlsConvenios(this.convenios);
			}

			if (this.includesEmpresa) {
				this.empresas = res[index++].map((empresa) => {
					return { value: empresa.codigo, label: empresa.nome };
				});
				this.buildControlsEmpresas(this.empresas);
			}

			this.emitirFiltro();
		});
	}

	private buildControlsPeriodo(initPeriodo) {
		this.periodoFormGroup = this.fb.group({});
		if (initPeriodo) {
			this.periodoFormGroup.addControl(
				"inicio",
				this.fb.control(initPeriodo.inicio)
			);
			this.periodoFormGroup.addControl("fim", this.fb.control(initPeriodo.fim));
		} else {
			this.periodoFormGroup.addControl(
				"inicio",
				this.fb.control(moment().startOf("month"))
			);
			this.periodoFormGroup.addControl("fim", this.fb.control(moment()));
		}
	}

	private buildControlsSituacoes(situacoes) {
		this.situacoesFormGroup = this.fb.group({});
		if (this.preservarFiltro) {
			for (const situacao of situacoes) {
				const checked = this.initSituacoes.includes(situacao.value);
				this.situacoesFormGroup.addControl(
					situacao.label,
					this.fb.control(checked)
				);
			}
		} else {
			for (const situacao of situacoes) {
				this.situacoesFormGroup.addControl(
					situacao.label,
					this.fb.control(true)
				);
			}
		}
	}

	private buildControlsEmpresas(
		empresas: Array<{ value: number; label: string }>
	) {
		this.empresasFormGroup = this.fb.group({});
		if (this.preservarFiltro) {
			for (const empresa of empresas) {
				const checked = this.initEmpresas.includes(empresa.value);
				this.empresasFormGroup.addControl(
					empresa.label,
					this.fb.control(checked)
				);
			}
		} else {
			for (const empresa of empresas) {
				const checked = String(empresa.value) === this.session.empresaId;
				this.empresasFormGroup.addControl(
					empresa.label,
					this.fb.control(checked)
				);
			}
		}
	}

	private buildControlsConvenios(
		convenios: Array<{ value: number; label: string; empresas: Array<number> }>
	) {
		this.conveniosFormGroup = this.fb.group({});
		if (this.preservarFiltro) {
			for (const convenio of convenios) {
				const checked = this.initConvenios.includes(convenio.value);
				this.conveniosFormGroup.addControl(
					convenio.label,
					this.fb.control(checked)
				);
			}
		} else {
			for (const convenio of convenios) {
				const checked = convenio.empresas.includes(
					parseInt(this.session.empresaId, 0)
				);
				this.conveniosFormGroup.addControl(
					convenio.label,
					this.fb.control(checked)
				);
			}
		}
	}

	private buildUsuarios(usuarios: Array<{ value: number; label: string }>) {
		this.usuariosFormGroup = this.fb.group({});
		if (this.preservarFiltro) {
			for (const usuario of usuarios) {
				const checked = this.initUsuario.includes(usuario.value);
				this.usuariosFormGroup.addControl(
					usuario.label,
					this.fb.control(checked)
				);
			}
		} else {
			for (const usuario of usuarios) {
				this.usuariosFormGroup.addControl(usuario.label, this.fb.control(true));
			}
		}
	}

	public selecionaPeriodo(periodoSelecionado) {
		this.periodoSelecionado = periodoSelecionado;

		switch (periodoSelecionado) {
			case MenuPeriodo.ONTEM:
				this.periodoFormGroup.controls["inicio"].setValue(
					moment().subtract(1, "day")
				);
				this.periodoFormGroup.controls["fim"].setValue(
					moment().subtract(1, "day")
				);
				break;
			case MenuPeriodo.HOJE:
				this.periodoFormGroup.controls["inicio"].setValue(moment());
				this.periodoFormGroup.controls["fim"].setValue(moment());
				break;
			case MenuPeriodo.ESTE_MES:
				this.periodoFormGroup.controls["inicio"].setValue(
					moment().startOf("month")
				);
				this.periodoFormGroup.controls["fim"].setValue(moment());
				break;
			case MenuPeriodo.MES_ANTERIOR:
				this.periodoFormGroup.controls["inicio"].setValue(
					moment().subtract(1, "month").startOf("month")
				);
				this.periodoFormGroup.controls["fim"].setValue(
					moment().subtract(1, "month").endOf("month")
				);
				break;
			default:
				this.periodoSelecionado = MenuPeriodo.CUSTOMIZADO;
				break;
		}

		this.emitirFiltro();
	}

	private emitirFiltro() {
		this.filterDropdown.close();

		const inicio = moment(this.periodoFormGroup.controls["inicio"].value);
		const fim = moment(this.periodoFormGroup.controls["fim"].value);
		const meses = fim.diff(inicio, "months", false);
		if (meses > 3) {
			this.snotifyService.error(
				"O limite máximo para o filtro no seu dashboard é de 3 meses. " +
					"Por favor, informe as datas de inicio e fim novamente.",
				{ timeout: 5000 }
			);
			return;
		}

		const filtro: Filtro = {
			periodoSelecionado: this.periodoSelecionado,
			inicio: moment(this.periodoFormGroup.controls["inicio"].value).format(
				"YYYYMMDD"
			),
			fim: moment(this.periodoFormGroup.controls["fim"].value).format(
				"YYYYMMDD"
			),
			situacoes: this.situacoes
				.filter(
					(situacao) => this.situacoesFormGroup.controls[situacao.label].value
				)
				.map((s) => s.value)
				.toString(),
			empresas: this.empresas
				.filter(
					(empresa) => this.empresasFormGroup.controls[empresa.label].value
				)
				.map((e) => e.value)
				.toString(),
			convenios: this.convenios
				.filter(
					(convenio) => this.conveniosFormGroup.controls[convenio.label].value
				)
				.map((c) => c.value)
				.toString(),
			usuarios: this.usuarios
				.filter(
					(usuario) => this.usuariosFormGroup.controls[usuario.label].value
				)
				.map((c) => c.value)
				.toString(),
		};

		const dados: DadosDoFiltro = {
			periodoSelecionado: this.periodoSelecionado,
			periodo: this.periodoFormGroup.value,
			situacoes: this.situacoes.filter(
				(situacao) => this.situacoesFormGroup.controls[situacao.label].value
			),
			empresas: this.empresas.filter(
				(empresa) => this.empresasFormGroup.controls[empresa.label].value
			),
			convenios: this.convenios.filter(
				(convenio) => this.conveniosFormGroup.controls[convenio.label].value
			),
			usuarios: this.usuarios.filter(
				(usuario) => this.usuariosFormGroup.controls[usuario.label].value
			),
		};

		this.obterFiltro.emit({ dados, filtro });

		sessionStorage.setItem("pay-filter:filtro", JSON.stringify(filtro));
	}

	public buscar() {
		this.periodoSelecionado = MenuPeriodo.CUSTOMIZADO;
		this.emitirFiltro();
	}

	public definirValor(dados) {
		const { convenio, empresa, situacao, usuario } = dados;

		if (this.includesStatus && situacao) {
			this.situacoes = this.situacoes.map((atual) =>
				situacao.label === atual.label ? situacao : atual
			);
			this.situacoesFormGroup.controls[situacao.label].setValue(
				situacao.checked
			);
		}

		if (this.includesConvenio && convenio) {
			this.convenios = this.convenios.map((atual) =>
				convenio.label === atual.label ? convenio : atual
			);
			this.conveniosFormGroup.controls[convenio.label].setValue(
				convenio.checked
			);
		}

		if (this.includesEmpresa && empresa) {
			this.empresas = this.empresas.map((atual) =>
				empresa.label === atual.label ? empresa : atual
			);
			this.empresasFormGroup.controls[empresa.label].setValue(empresa.checked);
		}

		if (this.includesUsuario && usuario) {
			this.usuarios = this.usuarios.map((atual) =>
				usuario.label === atual.label ? usuario : atual
			);
			this.usuariosFormGroup.controls[usuario.label].setValue(usuario.checked);
		}

		this.emitirFiltro();
	}

	public isOpen(index: number) {
		if (this.open && this.open[index]) {
			this.open[index] = true;
			return true;
		} else {
			this.open[index] = false;
			return false;
		}
	}

	public toggleFilterVisibility(i: number) {
		this.open[i] = !this.open[i];
	}
}
