import { Component, OnInit } from "@angular/core";
import { ZwPactoPayApiUtilService } from "zw-pactopay-api";

@Component({
	selector: "pacto-wehelp",
	template: `
		<div id="root-wehelp"></div>
	`,
	styleUrls: ["./wehelp.component.scss"],
})
export class WehelpComponent implements OnInit {
	constructor(private zwPactoPayApiUtil: ZwPactoPayApiUtilService) {}

	ngOnInit() {
		this.zwPactoPayApiUtil.obterInfoWehelp().subscribe((result) => {
			result.person.company_unit = "2";
			const customerInfo = {
				debug: 1,
				survey_token:
					"MTFhYjhiN2E3Y2U2MjA0YmRkMGYzMDUwZjk2NDdlNGNhMTc0ZWY5OTJjNGExMGIxNzU0YWIzZTczODc3OWUyNgIHp0esD5F6M8qMkaT2UE5N6Yj-Y3g-X0jRX0WzABLN",
				type: "modal",
				message_open: "false",
				language: "PORTUGUESE",
				...result,
			};

			const widget = document.createElement("script");
			widget.src =
				"https://app.wehelpsoftware.com/js/built/production/widget.js";
			document.body.appendChild(widget);
			widget.onload = () => {
				(window as any).loadWeHelpWidgetScreen(
					"https://app.wehelpsoftware.com",
					customerInfo,
					"root-wehelp"
				);
			};
		});
	}
}
