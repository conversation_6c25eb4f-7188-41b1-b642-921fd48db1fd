@import "dist/ui-kit/assets/import.scss";

.status {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 90px;
	height: 24px;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	color: #fff;
}

.ativo {
	background-color: #1998fc;
}

.pago {
	background-color: #2ec750;
}

.cancelado {
	background-color: #db2c3d;
}

.expirado {
	background-color: #b4b7bb;
}

:host(.ds-3) {
	.status {
		font-size: 12px;
		font-weight: 400;
		line-height: 15px;
		width: 107px;
		padding: 5px 28px;
		border-radius: 50px;
		height: initial;
	}

	.pago {
		color: $chuchuzinho05;
		background-color: $chuchuzinho01;
	}

	.t-status-pac {
		color: $pequizao05;
		background-color: $pequizao01;
	}

	.expirado {
		color: $cinza05;
		background-color: $cinza01;
	}

	.cancelado {
		color: $hellboy05;
		background-color: $hellboy01;
	}

	// .t-status-can {
	//     color: $laranjinha05;
	//     background-color: $laranjinha01;
	// }

	.ativo {
		color: $azulim05;
		background-color: $azulim01;
	}
}
