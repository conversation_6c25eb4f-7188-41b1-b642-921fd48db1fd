<table class="table">
	<thead>
		<tr class="first-line">
			<th *ngIf="dataGridConfig.valueRowCheck">
				<input
					(click)="selectAllF($event)"
					[checked]="dataGridConfig.allCheck"
					type="checkbox" />
			</th>
			<th
				*ngFor="let column of visibleColumns"
				[ngClass]="{ sortable: isSortable(column.nome) }">
				<span
					(click)="sortClick(column)"
					*ngIf="column.mostrarTitulo"
					class="column-title">
					<ng-container *ngIf="isTemplate(column.titulo)">
						<ng-container *ngTemplateOutlet="column.titulo"></ng-container>
					</ng-container>
					<ng-container *ngIf="!isTemplate(column.titulo)">
						{{ column.titulo }}
					</ng-container>
					<i
						*ngIf="isSortable(column.nome)"
						class="{{ getSortIconClass(column.nome) }}"></i>
				</span>
			</th>
			<th *ngIf="dataGridConfig.actions.length" class="action-column">
				{{ dataGridConfig.actions }}
			</th>
			<th *ngIf="dataGridConfig.dropDownActions.length" class="action-column">
				{{ dataGridConfig.dropDownActions }}
			</th>
		</tr>
	</thead>
	<tbody class="body-table">
		<ng-container *ngIf="customContent">
			<ng-container
				*ngTemplateOutlet="
					customContent;
					context: { dados: dados, colunas: visibleColumns }
				"></ng-container>
		</ng-container>

		<ng-container *ngIf="!customContent">
			<ng-container
				*ngFor="let row of dados; let lastRow = last; let rowIndex = index">
				<tr
					[@fadeIn]="dados.length"
					[ngClass]="{
						'total-row': dataGridConfig.totalRow && lastRow,
						'row-clickable': dataGridConfig.rowClick,
						ghost: dataGridConfig.ghostLoad && loading
					}"
					id="element-{{ rowIndex }}">
					<td *ngIf="dataGridConfig.valueRowCheck" class="selectable">
						<div
							[ngbTooltip]="
								dataGridConfig.tooltipCheckbox ? showTooltip(row) : ''
							">
							<input
								(click)="checkRow(row)"
								[checked]="rowChecked(row)"
								[disabled]="disableCheck(row)"
								class="form-check-input"
								type="checkbox" />
						</div>
					</td>
					<!-- Data columns -->
					<td
						(click)="rowClickHandler(row, $event)"
						*ngFor="let column of visibleColumns; let firstColumn = first">
						<ng-container
							[ngSwitch]="dataGridConfig.totalRow && firstColumn && lastRow">
							<!-- TOTAL COLUMN -->
							<ng-container *ngSwitchCase="true">
								<span>Total</span>
							</ng-container>

							<!-- DATA COLUMNS -->
							<ng-container *ngSwitchCase="false">
								<span
									(click)="columnCellClickHandler(row, column)"
									[ngClass]="{ 'hover-cell': column.cellPointerCursor }"
									class="column-cell">
									<ng-container *ngIf="!column.celula && column.campo && row">
										{{
											column.valueTransform
												? column.valueTransform(row[column.campo])
												: row[column.campo]
										}}
									</ng-container>
									<ng-container *ngIf="column.celula && row">
										<ng-container
											*ngTemplateOutlet="
												column.celula;
												context: { item: row }
											"></ng-container>
									</ng-container>
								</span>
								<div
									*ngIf="dataGridConfig.ghostLoad && !row"
									class="ghost-bar"
									style="height: 20px !important; width: 100% !important"></div>
							</ng-container>
						</ng-container>
					</td>

					<td *ngIf="accordion" class="status-control">
						<i
							(click)="headerClickHandler(rowIndex)"
							*ngIf="isOpen(rowIndex)"
							class="pct pct-chevron-up"
							id="close-accordion-{{ rowIndex }}"></i>
						<i
							(click)="headerClickHandler(rowIndex)"
							*ngIf="!isOpen(rowIndex)"
							class="pct pct-chevron-down"
							id="open-accordion-{{ rowIndex }}"></i>
					</td>

					<!--Action column -->
					<td *ngIf="dataGridConfig.actions.length" class="action-cell">
						<i
							(click)="iconClickHandler(row, icon)"
							*ngFor="let icon of rawDataIcons[rowIndex]"
							class="{{ icon.iconClass }}"
							id="element-{{ rowIndex }}-{{ icon.nome.toLowerCase() }}"
							title="{{ icon.tooltipText }}"></i>
					</td>

					<!--Dropdown Action column -->
					<td *ngIf="dataGridConfig.dropDownActions.length" class="action-cell">
						<div
							#dropdownActions="ngbDropdown"
							[autoClose]="'outside'"
							[placement]="'bottom-right'"
							class="d-inline-block"
							ngbDropdown>
							<div id="dropdownActions" ngbDropdownToggle>
								<i
									class="pct pct-more-horizontal"
									id="ddaction-{{ rowIndex }}"
									title=""></i>
							</div>

							<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
								<div
									*ngFor="
										let item of rawDropdownActionItems[rowIndex];
										let i = index
									"
									[attr.data-index]="i">
									<hr *ngIf="i > 0" class="solid" />
									<div
										(click)="item.actionFn(row)"
										class="type-p-small item-dropdown"
										id="ddAction-{{ rowIndex }}-{{ item.nome.toLowerCase() }}"
										title="{{ item.tooltipText }}">
										<i
											*ngIf="item.iconClass.length"
											class="{{ item.iconClass }}"></i>
										{{ item.nome }}
									</div>
								</div>
							</div>
						</div>
					</td>
				</tr>
				<tr *ngIf="isOpen(rowIndex)" style="background: #fafafa">
					<td [attr.colspan]="visibleColumns.length + 1" style="padding: 0px">
						<div
							[@openClose]="isOpen(rowIndex) ? 'true' : 'false'"
							class="body-section">
							<ng-container
								*ngTemplateOutlet="
									accordion;
									context: { item: row }
								"></ng-container>
						</div>
					</td>
				</tr>
			</ng-container>
		</ng-container>
	</tbody>
</table>

<div *ngIf="dados.length === 0 && !loading" class="empty-state">
	{{ emptyStateMessage }}
</div>

<div *ngIf="loading && !dataGridConfig.ghostLoad" class="loading-state">
	<span id="carregando_dados">Carregando dados...</span>
	<img class="loading-data-icon" src="pacto-ui/images/loading.svg" />
</div>
