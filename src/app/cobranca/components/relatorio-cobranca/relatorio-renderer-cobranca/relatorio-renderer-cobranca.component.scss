@import "dist/ui-kit/assets/import.scss";

table.table {
	margin-bottom: 0px;
	background-color: transparent;

	tbody > tr:nth-child(odd) {
		background: #fafafa;
	}

	.body-table {
		border-top: 2px solid #dee2e6;
		background: #ffff;
	}

	th {
		@extend .type-btn-bold;
		color: $pretoPri;

		&.sortable {
			cursor: pointer;

			i.pct {
				padding-left: 10px;
				color: $pretoPri;
			}
		}
	}

	tr {
		@extend .type-p-small;
		color: $preto02;
	}

	tr.row-clickable:not(.ghost) {
		cursor: pointer;
	}

	tr.total-row td {
		font-weight: bold;
	}

	.row-colored-first > tbody > tr:nth-child(odd) {
		background: #fafafa;
	}

	.row-colored-second > tbody > tr:nth-child(even) {
		background: #fafafa;
	}

	.column-cell.hover-cell:hover {
		cursor: pointer;
		text-decoration: underline;
	}

	td {
		&.center {
			text-align: center;
		}

		&.right {
			text-align: right;
		}
	}

	th {
		&.center {
			text-align: center;
		}

		&.right {
			text-align: right;
		}
	}
}

.loading-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;

	.loading-data-icon {
		width: 30px;
		position: relative;
		top: -1px;
	}
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

.selectable {
	width: 10px;
	text-align: center;

	input {
		position: relative;
		margin-top: calc(25% + 3px);
		margin-bottom: calc(25% + 3px);
		margin-left: -2px;
	}
}

.action-cell i {
	font-size: 16px;
}

.action-cell i.fa-trash-o {
	color: #db2c3d;
}

.action-cell i.pct {
	padding-right: 5px;
	cursor: pointer;
}

.action-column {
	width: 100px;
}

.dropdown-toggle::after {
	display: none;
}

.first-line > th {
	border-top: 0;
	text-align: left;
	text-transform: none;
	font-weight: bold;
}

.column-title {
	text-align: center;
	text-transform: none;
	font-weight: bold;
	font-size: 14px;
}

hr.solid {
	width: 92%;
	margin: 2px 8px;
	cursor: default !important;
}

.item-dropdown {
	padding: 8px;
	line-height: 1;
	color: $cinza05;
}

.item-dropdown:hover {
	color: $azulimPri;
	background: $cinzaPastel;
}

.body-section {
	background: #fafafa;
}

.status-control {
	font-size: 28px;
	margin-right: 12px;

	.pct {
		position: relative;
		top: 2px;
		cursor: pointer;
		font-size: 24px;
		color: $azulPactoPri;
	}
}
