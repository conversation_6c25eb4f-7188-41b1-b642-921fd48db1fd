import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "src/app/cobranca/components/relatorio-cobranca/data-grid.model";
import {
	fadeIn,
	PactoActionConfig,
	PactoDataGridColumnConfig,
	PactoDataGridOrdenacaoDirecao,
} from "ui-kit";

declare var $;

@Component({
	// tslint:disable-next-line:component-selector
	selector: "pacto-relatorio-cobranca-renderer",
	templateUrl: "./relatorio-renderer-cobranca.component.html",
	styleUrls: ["./relatorio-renderer-cobranca.component.scss"],
	changeDetection: ChangeDetectionStrategy.Default,
	animations: [
		trigger("fadeIn", fadeIn(":enter")),
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class RelatorioCobrancaRendererComponent implements OnInit {
	@Input() dataGridConfig: PactoDataGridConfig;
	@Input() data: Array<any>;
	@Input() loading = false;
	@Input() customContent: TemplateRef<any>;
	@Input() accordion: TemplateRef<any>;
	@Input() rawDataIcons: Array<Array<PactoActionConfig>>;
	@Input() rawDropdownActionItems: Array<Array<PactoActionConfig>>;
	@Input() emptyStateMessage: string;
	@Input() alternatingColors: "first" | "second" | "none" = "none";
	@Input() detailCobranca = false;
	open: Array<boolean> = [];

	@Output() sort: EventEmitter<{ column: string; direction: string }> =
		new EventEmitter();
	@Output() cellClick: EventEmitter<{ row: any; column: any }> =
		new EventEmitter();
	@Output() iconClick: EventEmitter<{ row: any; iconName: string }> =
		new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() rowCheck: EventEmitter<{ row: any; checked: boolean }> =
		new EventEmitter();
	@Output() selectAll: EventEmitter<any> = new EventEmitter();
	@Input() allowsCheck: any;

	@ViewChild("dropdownActions", { static: false }) dropdownActions: NgbDropdown;

	constructor(private cd: ChangeDetectorRef) {}

	get dados() {
		return this.data;
	}

	get visibleColumns() {
		return this.dataGridConfig.columns.filter((column) => {
			if (column.visible != null) {
				return column.visible;
			} else {
				return column.defaultVisible;
			}
		});
	}

	ngOnInit() {}

	rowChecked(row): boolean {
		if (this.dataGridConfig.allCheck && !this.disableCheck(row)) {
			return true;
		} else if (
			row &&
			row[this.dataGridConfig.valueRowCheck] &&
			this.dataGridConfig.checkeds.includes(
				row[this.dataGridConfig.valueRowCheck]
			)
		) {
			return true;
		}
		return false;
	}

	checkRow(row) {
		const id = row[this.dataGridConfig.valueRowCheck];
		if (this.dataGridConfig.checkeds.includes(id)) {
			const index = this.dataGridConfig.checkeds.indexOf(id, 0);
			if (index > -1) {
				this.dataGridConfig.checkeds.splice(index, 1);
				this.rowCheck.emit({
					row,
					checked: false,
				});
			}
		} else {
			this.dataGridConfig.checkeds.push(id);
			this.rowCheck.emit({
				row,
				checked: true,
			});
		}
		this.cd.detectChanges();
	}

	disableCheck(row) {
		const propertyNames = Object.getOwnPropertyNames(this.allowsCheck);
		if (propertyNames.length > 0 && row) {
			const disable = [];
			for (const propertyName of propertyNames) {
				if (row.hasOwnProperty(propertyName)) {
					disable.push(row[propertyName] === this.allowsCheck[propertyName]);
				} else {
					disable.push(false);
				}
			}
			return disable.includes(false);
		} else {
			return false;
		}
	}

	selectAllF(event: any) {
		this.selectAll.emit(event.target.checked);
	}

	rowClickHandler(row, $event) {
		const $target = $($event.target);
		const actionClick = $target.closest(".action-cell").length;
		if (!actionClick && this.dataGridConfig.rowClick) {
			this.rowClick.emit(row);
		}
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else if (type === "string") {
			return false;
		} else {
			return true;
		}
	}

	getSortIconClass(column: string) {
		const ordenada = this.dataGridConfig.state.ordenacaoColuna === column;
		const asc =
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.ASC;
		const desc =
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.DESC;
		const ordenavel = this.dataGridConfig.columns.find(
			(c) => c.nome === column
		).ordenavel;
		if (ordenada && asc) {
			return "pct pct-caret-up";
		} else if (ordenada && desc) {
			return "pct pct-caret-down";
		} else if (ordenavel) {
			return "pct pct-drop-down";
		}
	}

	iconClickHandler(row: any, icon: PactoActionConfig) {
		this.iconClick.emit({
			row,
			iconName: icon.nome,
		});
	}

	isSortable(column: string) {
		return this.dataGridConfig.columns.find((i) => i.nome === column).ordenavel;
	}

	sortClick(column: PactoDataGridColumnConfig) {
		if (this.isSortable(column.nome)) {
			this.updateSortState(column);
			this.sort.emit({
				column: this.dataGridConfig.state.ordenacaoColuna,
				direction: this.dataGridConfig.state.ordenacaoDirecao,
			});
		}
	}

	columnCellClickHandler(row, column) {
		this.cellClick.emit({ row, column });
	}

	private updateSortState(column: PactoDataGridColumnConfig) {
		if (!column.ordenavel) {
			return false;
		}
		if (this.dataGridConfig.state.ordenacaoColuna !== column.nome) {
			this.dataGridConfig.state.ordenacaoColuna = column.nome;
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.ASC;
		} else if (
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.ASC
		) {
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.DESC;
		} else {
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.ASC;
		}
	}

	isOpen(index: number) {
		if (this.open && this.open[index]) {
			this.open[index] = true;
			return true;
		} else {
			this.open[index] = false;
			return false;
		}
	}

	headerClickHandler(i: number) {
		this.open[i] = !this.open[i];
	}

	showTooltip(row) {
		return this.disableCheck(row) ? this.dataGridConfig.tooltipCheckbox : "";
	}
}
