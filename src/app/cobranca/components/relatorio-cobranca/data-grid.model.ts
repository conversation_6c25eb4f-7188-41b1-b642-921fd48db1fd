import { TemplateRef } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Observable } from "rxjs";
import { PactoModalSize, SelectFilterParamBuilder } from "ui-kit";
import { TableData } from "./relatorio-cobranca.component";

export enum PactoDataGridOrdenacaoDirecao {
	ASC = "ASC",
	DESC = "DESC",
}

export class PactoDataGridState {
	ordenacaoColuna: string;
	ordenacaoDirecao: PactoDataGridOrdenacaoDirecao;
	paginaTamanho?: number;
	paginaNumero?: number;

	constructor(dados?) {
		dados = dados || {};
		this.ordenacaoColuna = dados.ordenacaoColuna;
		this.ordenacaoDirecao =
			dados.ordenacaoDirecao || PactoDataGridOrdenacaoDirecao.ASC;
		this.paginaTamanho = dados.paginaTamanho || 10;
		this.paginaNumero = dados.paginaNumero || 0;
	}
}

export interface PactoDataGridConfigDto {
	endpointUrl?: string;
	endpointParamsType?: "json" | "query";
	quickSearch?: boolean;
	valueRowCheck?: string;
	tooltipCheckbox?: string;
	exportButton?: boolean;
	showFilters?: boolean;
	initialFilters?: Array<{ name: string; value: string }>;
	pagination?: boolean;
	rowClick?: boolean;
	totalRow?: boolean;
	dataAdapterFn?: (serverData: any) => TableData<any>;
	actions?: Array<{
		nome: string;
		iconClass: string;
		tooltipText?: string;
		showIconFn?: (row: any) => boolean;
		actionFn?: (row: any) => any;
	}>;
	dropDownActions?: Array<{
		nome: string;
		iconClass: string;
		tooltipText?: string;
		showIconFn?: (row: any) => boolean;
		actionFn?: (row: any) => any;
	}>;
	showSimpleTotalCount?: boolean;
	buttons?: PactoDataGridButtonConfig;
	columns?: Array<PactoDataGridColumnConfigDto>;
	state?: PactoDataGridState;
	ghostLoad?: boolean;
	ghostAmount?: number;
	ghostAmination?: boolean;
	visibleColumnFn?: (column: PactoDataGridColumnConfig) => boolean;
	formGroup?: FormGroup;
	selectParamBuilder?: SelectFilterParamBuilder;
	beforeConfirm?: (row, form, data, indexRow) => boolean;
	showDelete?: (row, isAdd) => boolean;
	selectOptionChange?: (option, form, row) => boolean;
}

export declare type ColumnInputType =
	| "text"
	| "decimal"
	| "date"
	| "number"
	| "checkbox"
	| "currency"
	| "select"
	| "color"
	| "switch"
	| "select-multi";

export class PactoDataGridColumnConfig {
	titulo: TemplateRef<any> | string;
	mostrarTitulo?: boolean;
	ordenavel: boolean;
	date?: boolean;
	/**
	 * É um atributo da entidade e é
	 * filtrado pela busca rápida.
	 */
	buscaRapida?: boolean;
	cellPointerCursor?: boolean;
	campo?: string;
	nome: string;
	visible: boolean;
	defaultVisible?: boolean;
	valueTransform?: (v: any) => any;
	celula?: TemplateRef<any>;
	classes?: Array<string>;
	styleClass?: string;
	editable?: boolean;
	inputType?: ColumnInputType;
	inputSelectData?: Array<any>;
	control?: FormControl;
	width?: string;
	labelSelectKey = "label";
	idSelectKey = "codigo";
	modalComponent?: any;
	modalSize?: PactoModalSize;
	modalTitle = "Cadastrar";
	inputTextMask?;
	labelText?: string;
	labelTextUnchecked?: string;
	isObject?: boolean;
	objectAttrLabelName = "label";
	showAddSelectBtn = true;
	showSelectFilter = true;
	endpointUrl?: string;
	inputSelectDataSubscription?: Observable<any>;
	errorMessage?: string;
	selectParamBuilder?: SelectFilterParamBuilder;
	maxlength?: number;
	maxValue?: number;
	minValue?: number;
	decimal?: boolean;
	decimalPrecision = 2;
	prefix = "";
	isDisabled?: (row) => boolean;
	selectOptionChange?: (option, form, row) => void;
	addEmptyOption?: boolean;
	orderColumn?: string;

	constructor(dados: PactoDataGridColumnConfigDto) {
		this.nome = dados.nome;
		this.titulo = dados.titulo;
		this.date = dados.date;
		this.mostrarTitulo =
			dados.mostrarTitulo === undefined ? true : dados.mostrarTitulo;
		this.buscaRapida = dados.buscaRapida != null ? dados.buscaRapida : false;
		this.cellPointerCursor =
			dados.cellPointerCursor != null ? dados.cellPointerCursor : false;
		this.ordenavel = dados.ordenavel != null ? dados.ordenavel : true;
		this.defaultVisible =
			dados.defaultVisible != null ? dados.defaultVisible : false;
		this.campo = dados.campo || dados.nome;
		this.valueTransform = dados.valueTransform ? dados.valueTransform : null;
		this.celula = dados.celula;
		this.visible = dados.visible;
		this.classes = dados.classes || [];
		this.styleClass = dados.styleClass;
		this.inputType = dados.inputType;
		this.editable = dados.editable;
		this.inputSelectData = dados.inputSelectData;
		this.orderColumn = dados.orderColumn;
		if (dados.control) {
			this.control = dados.control;
		}
		if (dados.width) {
			this.width = dados.width;
		} else {
			this.width = "auto";
		}

		if (dados.labelSelectKey) {
			this.labelSelectKey = dados.labelSelectKey;
		}

		if (dados.idSelectKey) {
			this.idSelectKey = dados.idSelectKey;
		}

		if (dados.selectOptionChange !== undefined) {
			this.selectOptionChange = dados.selectOptionChange;
		}

		if (dados.dialogComponent) {
			this.modalComponent = dados.dialogComponent;
		}
		if (dados.modalSize) {
			this.modalSize = dados.modalSize;
		}
		if (dados.modalAddTitle) {
			this.modalTitle = dados.modalAddTitle;
		}
		if (dados.inputTextMask) {
			this.inputTextMask = dados.inputTextMask;
		}

		if (dados.labelText) {
			this.labelText = dados.labelText;
		}

		if (dados.labelTextUnchecked) {
			this.labelTextUnchecked = dados.labelTextUnchecked;
		}

		this.isObject = dados.isObject;
		this.addEmptyOption = dados.addEmptyOption;
		if (dados.objectAttrLabelName) {
			this.objectAttrLabelName = dados.objectAttrLabelName;
		}

		if (dados.showAddSelectBtn !== undefined) {
			this.showAddSelectBtn = dados.showAddSelectBtn;
		}
		if (dados.endpointUrl) {
			this.endpointUrl = dados.endpointUrl;
		}
		if (dados.inputSelectDataSubscription) {
			this.inputSelectDataSubscription = dados.inputSelectDataSubscription;
		}
		if (dados.showSelectFilter !== undefined) {
			this.showSelectFilter = dados.showSelectFilter;
		}
		if (dados.errorMessage) {
			this.errorMessage = dados.errorMessage;
		}
		if (dados.selectParamBuilder) {
			this.selectParamBuilder = dados.selectParamBuilder;
		}

		if (dados.maxlength) {
			this.maxlength = dados.maxlength;
		}

		if (dados.maxValue) {
			this.maxValue = dados.maxValue;
		}
		if (dados.minValue) {
			this.minValue = dados.minValue;
		}
		if (dados.prefix) {
			this.prefix = dados.prefix;
		}
		if (dados.decimal !== undefined) {
			this.decimal = dados.decimal;
		}
		if (dados.decimalPrecision !== undefined) {
			this.decimalPrecision = dados.decimalPrecision;
		}

		if (dados.isDisabled !== undefined) {
			this.isDisabled = dados.isDisabled;
		}
	}
}

export class PactoActionConfig {
	nome: string;
	iconClass: string;
	tooltipText?: string;
	showIconFn?: (row: any) => boolean;
	actionFn?: (row: any) => any;

	constructor(dados) {
		this.nome = dados.nome;
		this.iconClass = dados.iconClass;
		this.tooltipText = dados.tooltipText;
		this.showIconFn = dados.showIconFn ? dados.showIconFn : null;
		this.actionFn = dados.actionFn ? dados.actionFn : null;
	}
}

export class PactoDataGridConfig {
	endpointUrl: string;
	endpointParamsType: "json" | "query";
	valueRowCheck: string;
	tooltipCheckbox?: string;
	quickSearch: boolean;
	exportButton: boolean;
	pagination: boolean;
	pageSize?: number;
	showFilters: boolean;
	initialFilters?: Array<{ name: string; value: string }> = [];
	rowClick: boolean;
	totalRow: boolean;
	showSimpleTotalCount: boolean;
	actions: Array<PactoActionConfig> = [];
	dropDownActions: Array<PactoActionConfig> = [];
	columns: Array<PactoDataGridColumnConfig> = [];
	dataAdapterFn: (serverData: any) => TableData<any>;
	buttons: PactoDataGridButtonConfig;
	state: PactoDataGridState;
	ghostLoad?: boolean;
	ghostAmount?: number;
	ghostAnimation?: boolean;
	visibleColumnFn?: (column: PactoDataGridColumnConfig) => boolean;
	formGroup?: FormGroup;
	beforeConfirm?: (row, form, data, indexRow) => boolean;
	showDelete?: (row, isAdd) => boolean;
	checkeds: Array<any> = [];
	allCheck = false;

	constructor(config: PactoDataGridConfigDto) {
		if (
			config.endpointParamsType === null ||
			config.endpointParamsType === undefined
		) {
			config.endpointParamsType = "json";
		}

		if (config.columns) {
			config.columns.forEach((column) => {
				this.columns.push(new PactoDataGridColumnConfig(column));
			});
		}

		if (config.actions) {
			config.actions.forEach((action) => {
				this.actions.push(new PactoActionConfig(action));
			});
		}

		if (config.dropDownActions) {
			config.dropDownActions.forEach((action) => {
				this.dropDownActions.push(new PactoActionConfig(action));
			});
		}

		if (config.beforeConfirm !== undefined) {
			this.beforeConfirm = config.beforeConfirm;
		}

		if (config.showDelete !== undefined) {
			this.showDelete = config.showDelete;
		}

		this.endpointUrl = config.endpointUrl;
		this.endpointParamsType = config.endpointParamsType;
		this.valueRowCheck = config.valueRowCheck;
		this.tooltipCheckbox = config.tooltipCheckbox;
		this.dataAdapterFn = config.dataAdapterFn;
		this.quickSearch = config.quickSearch != null ? config.quickSearch : false;
		this.showFilters = config.showFilters != null ? config.showFilters : true;
		this.initialFilters = config.initialFilters;
		this.exportButton =
			config.exportButton != null ? config.exportButton : false;
		this.showSimpleTotalCount =
			config.showSimpleTotalCount != null ? config.showSimpleTotalCount : false;
		this.rowClick = config.rowClick != null ? config.rowClick : true;
		this.pagination = config.pagination != null ? config.pagination : true;
		this.totalRow = config.totalRow != null ? config.totalRow : false;
		this.buttons = config.buttons;
		this.ghostLoad = config.ghostLoad === null ? false : config.ghostLoad;
		this.state = new PactoDataGridState(config.state);
		this.ghostAmount = config.ghostAmount || this.state.paginaTamanho;
		this.ghostAnimation =
			config.ghostAmination === null || config.ghostAmination === undefined
				? false
				: config.ghostAmination;
		if (config.formGroup) {
			this.formGroup = config.formGroup;
		}

		this.visibleColumnFn = config.visibleColumnFn;
	}
}

export class PactoDataGridButtonConfig {
	conteudo: TemplateRef<any>;
	nome: string;
	id?: string;
}

interface PactoDataGridColumnConfigDto {
	nome: string;
	titulo: TemplateRef<any> | string;
	valueTransform?: (v: any) => any;
	dataAdapterFn?: (serverData: any) => TableData<any>;
	mostrarTitulo?: boolean;
	ordenavel?: boolean;
	buscaRapida?: boolean;
	cellPointerCursor?: boolean;
	visible?: boolean;
	defaultVisible?: boolean;
	date?: boolean;
	campo?: string;
	classes?: Array<string>;
	styleClass?: string;

	editable?: boolean;
	inputType?: ColumnInputType;
	inputSelectData?: Array<any>;
	inputSelectDataSubscription?: Observable<any>;
	control?: FormControl;
	width?: string;
	labelSelectKey?: string;
	idSelectKey?: string;
	dialogComponent?: any;
	modalSize?: PactoModalSize;
	modalAddTitle?: string;
	inputTextMask?;
	labelText?: string;
	labelTextUnchecked?: string;
	isObject?: boolean;
	objectAttrLabelName?: string;
	showAddSelectBtn?: boolean;
	endpointUrl?: string;
	showSelectFilter?: boolean;
	errorMessage?: string;
	/**
	 * Permite customizar conteudo da célula
	 *
	 * A variável item é igual o objeto renderizado
	 */
	celula?: TemplateRef<any>;
	selectParamBuilder?: SelectFilterParamBuilder;
	maxlength?: number;
	maxValue?: number;
	minValue?: number;
	decimalPrecision?: number;
	prefix?: string;
	decimal?: boolean;
	isDisabled?: (row) => boolean;
	selectOptionChange?: (option, form, row) => void;
	addEmptyOption?: boolean;
	orderColumn?: string;
}

export enum RelatorioExportarFormato {
	PDF = "PDF",
	XLS = "XLS",
}

export interface DataFiltro {
	quicksearchValue?: filterValue;
	quicksearchFields?: Array<filterValue>;
	filters?: { [key: string]: filterValue | Array<filterValue> };
	configs?: { [key: string]: any };
	page?: number;
	size?: number;
	/**
	 * ASC/DESC
	 */
	sortDirection?: string;
	sortField?: string;
}

export type filterValue = number | string;
