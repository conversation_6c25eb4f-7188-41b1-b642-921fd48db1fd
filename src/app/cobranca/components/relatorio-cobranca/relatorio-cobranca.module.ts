import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RelatorioCobrancaComponent } from "./relatorio-cobranca.component";
import { RelatorioCobrancaRendererComponent } from "./relatorio-renderer-cobranca/relatorio-renderer-cobranca.component";
import { ShareButtonCobrancaComponent } from "./share-button-cobranca/share-button-cobranca.component";

@NgModule({
	declarations: [
		RelatorioCobrancaComponent,
		RelatorioCobrancaRendererComponent,
		ShareButtonCobrancaComponent,
	],
	imports: [CommonModule, BaseSharedModule],
	exports: [RelatorioCobrancaComponent],
	entryComponents: [RelatorioCobrancaComponent],
})
export class RelatorioCobrancaModule {}
