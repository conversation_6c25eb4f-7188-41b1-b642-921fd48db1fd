import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { PactoDataGridConfig } from "src/app/cobranca/components/relatorio-cobranca/data-grid.model";
import {
	DataFiltro,
	DataGridFilterComponent,
	DataGridService,
	GridFilterConfig,
	PactoActionConfig,
	RelatorioExportarFormato,
} from "ui-kit";

export interface TableData<T> {
	/**
	 * Size of each page.
	 */
	size?: number;
	/**
	 * Data to be displayed.
	 */
	content?: Array<T>;
	/**
	 * Number of total elements.
	 */
	totalElements?: number;
	/**
	 * Number of current page, where zero is the first one.
	 */
	number?: number; // Zero-indexed
}

@Component({
	// tslint:disable-next-line:component-selector
	selector: "pacto-relatorio-cobranca",
	templateUrl: "./relatorio-cobranca.component.html",
	styleUrls: ["./relatorio-cobranca.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [DataGridService],
})
export class RelatorioCobrancaComponent implements OnInit, AfterViewInit {
	@Input() showShare = true;
	@Input() tableTitle: TemplateRef<any> | string;
	@Input() tableDescription: TemplateRef<any>;
	@Input() baseFilter: DataFiltro = {};
	@Input() customContent: TemplateRef<any>;
	@Input() accordion: TemplateRef<any>;
	@Input() customActions: TemplateRef<any>;
	@Input() customActionsTop: TemplateRef<any>;
	@Input() filterConfig: GridFilterConfig;
	@Input() apiReturnProperty;
	@Input() emptyStateMessage;
	@Input() alternatingColors: "first" | "second" | "none" = "none";
	@Input() table: PactoDataGridConfig = new PactoDataGridConfig({
		state: null,
		buttons: null,
		columns: [],
	});

	@Input() itensPerPage = [
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];

	@Output() iconClick: EventEmitter<{ row: any; iconName: string }> =
		new EventEmitter();
	@Output() cellClick: EventEmitter<{ row: any; column: any }> =
		new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() rowCheck: EventEmitter<{
		row?: any;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}> = new EventEmitter();
	@Input() allowsCheck: any = {};
	@Output() btnClick: EventEmitter<any> = new EventEmitter();
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();

	@ViewChild("quickSearch", { static: false }) quickSearch;
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("dataGridFilter", { static: false })
	dataGridFilter: DataGridFilterComponent;
	@ViewChild("filterToggleButton", { static: false })
	filterToggleButton: ElementRef;

	ngbPage;
	private dataFetchSubscription: Subscription;
	private temporaryFilters;

	pageSizeControl: FormControl = new FormControl(10);
	quickSearchControl: FormControl = new FormControl();
	quickSearchCustomValue: string;
	dataFetchLoading = false;
	data: any = {
		content: [],
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		number: 0,
	};
	selectedItems = [];
	rawData = [];
	rawDataIcons: Array<Array<PactoActionConfig>> = [];
	rawDropdownActionItems: Array<Array<PactoActionConfig>> = [];

	constructor(
		private dataService: DataGridService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	ngAfterViewInit() {
		setTimeout(() => {
			this.initialSetup();
			if (this.dataGridFilter) {
				this.temporaryFilters = this.dataGridFilter.getFilterData();
			}
			if (this.table.initialFilters) {
				this.table.initialFilters.forEach((filter) => {
					this.addFilter(filter.name, filter.value);
				});
			}
			this.fetchData();
			this.autoFocus();
		});
	}

	@HostListener("document:click", ["$event.target"])
	public clickHandler(targetElement) {
		const filter = this.filterConfig && this.table.showFilters;
		if (filter) {
			const filterBtnClick = this.isDescendant(
				this.filterToggleButton.nativeElement,
				targetElement
			);
			const filterClick = this.isDescendant(
				this.dataGridFilter.nativeElement,
				targetElement
			);
			const calendarClick = this.isCalendarClick(targetElement);
			if (!filterClick && !filterBtnClick && !calendarClick) {
				this.filterDropdown.close();
			}
		}
	}

	get RelatorioExportarFormato() {
		return RelatorioExportarFormato;
	}

	checkSelectdItem(checkItem: { row: any; checked: boolean }) {
		if (checkItem.checked) {
			this.selectedItems.push(checkItem.row);
		} else {
			this.selectedItems = this.selectedItems.filter((item) => {
				return (
					item[this.table.valueRowCheck] !==
					checkItem.row[this.table.valueRowCheck]
				);
			});
		}
		this.rowCheck.emit({ ...checkItem, clearAll: false, selectedAll: false });
	}

	selectAll(checked: boolean) {
		if (checked) {
			this.table.allCheck = true;
			this.selectedItems = this.data.content.filter((row) => {
				if (Object.keys(this.allowsCheck).length === 0) {
					return true;
				} else {
					const permissoes = [];
					for (const propertyName of Object.getOwnPropertyNames(
						this.allowsCheck
					)) {
						if (row.hasOwnProperty(propertyName)) {
							permissoes.push(
								row[propertyName] === this.allowsCheck[propertyName]
							);
						}
					}
					return permissoes.every((permissao) => permissao === true);
				}
			});
			this.rowCheck.emit({ clearAll: false, selectedAll: true });
		} else {
			this.table.allCheck = false;
			this.table.checkeds = new Array<any>();
			this.selectedItems = new Array<any>();
			this.rowCheck.emit({ clearAll: true, selectedAll: false });
		}
		this.cd.detectChanges();
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else {
			return type !== "string";
		}
	}

	relatorioHandler(format: RelatorioExportarFormato) {
		const filtros = this.fetchFiltros();
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}
		this.dataService
			.obterRelatorio(
				this.table.endpointUrl,
				filtros,
				format,
				this.table.endpointParamsType
			)
			.subscribe((link) => {
				window.open(link, "_blank");
			});
	}

	reloadData() {
		this.table.allCheck = false;
		this.table.checkeds = new Array<any>();
		this.selectedItems = new Array<any>();
		this.rowCheck.emit({ clearAll: true, selectedAll: false });

		this.fetchData();
	}

	sortUpdateHandler() {
		this.fetchData();
	}

	pageChangeHandler(page) {
		/* Futuramente não será necessário pois ao paginar deverá manter mercado */
		this.table.allCheck = false;
		this.table.checkeds = new Array<any>();
		this.selectedItems = new Array<any>();
		this.rowCheck.emit({ clearAll: true, selectedAll: false });
		/* --------------------------------------------------------------------- */

		this.table.state.paginaNumero = page - 1;
		this.fetchData();
	}

	btnCLickHandler() {
		this.btnClick.emit(this.table.buttons.nome);
	}

	filterHandler(filter) {
		this.table.state.paginaNumero = 0;
		this.temporaryFilters = filter;
		this.filterDropdown.close();
		this.fetchData();
	}

	cellClickHandler($event) {
		this.cellClick.emit($event);
	}

	alterfilterConfigUpdate(statusConfid) {
		this.filterConfigUpdate.emit(statusConfid);
	}

	private populateRawDataIcons() {
		this.rawDataIcons = [];
		if (this.rawData) {
			this.rawData.forEach((rawItem) => {
				const actions = [];
				this.table.actions.forEach((action) => {
					if (action.showIconFn === null) {
						actions.push(action);
					} else if (action.showIconFn(rawItem)) {
						actions.push(action);
					}
				});

				this.rawDataIcons.push(actions);
			});
		}
	}

	private populateRawDropdownActionItems() {
		this.rawDropdownActionItems = [];
		this.rawData.forEach((rawItem) => {
			const ddActions = [];
			this.table.dropDownActions.forEach((ddAction) => {
				if (ddAction.showIconFn === null) {
					ddActions.push(ddAction);
				} else if (ddAction.showIconFn(rawItem)) {
					ddActions.push(ddAction);
				}
			});

			this.rawDropdownActionItems.push(ddActions);
		});
	}

	private isDescendant(parent, child) {
		let node = child.parentNode;
		if (parent === child) {
			return true;
		} else {
			while (node !== null) {
				if (node === parent) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private isCalendarClick(target) {
		let node = target.parentNode;
		while (node !== null) {
			if (node.tagName === "NGB-DATEPICKER") {
				return true;
			}
			node = node.parentNode;
		}
		return false;
	}

	private initialSetup() {
		// current page
		this.ngbPage = this.table.state.paginaNumero + 1;
		// quick search
		if (this.table.quickSearch) {
			this.quickSearchControl.valueChanges
				.pipe(debounceTime(500))
				.subscribe(() => {
					this.table.state.paginaNumero = 0;
					this.ngbPage = 1;
					this.fetchData();
				});
		}
		// page size
		this.pageSizeControl.valueChanges.subscribe((pageSize) => {
			this.table.state.paginaTamanho = parseInt(
				pageSize,
				this.table.pageSize || 10
			);
			this.table.state.paginaNumero = 0;
			this.ngbPage = 1;
			this.fetchData();
		});
	}

	public fetchData() {
		const filtros = this.fetchFiltros();
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));

		/**
		 * Merge 'filters'
		 */
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}

		this.dataFetchLoading = true;
		if (this.table.ghostLoad) {
			this.rawData = new Array(this.table.ghostAmount);
		} else {
			this.rawData = [];
		}

		if (this.dataFetchSubscription) {
			this.dataFetchSubscription.unsubscribe();
		}

		this.dataFetchSubscription = this.dataService
			.obterDados(
				this.table.endpointUrl,
				filtros,
				this.table.endpointParamsType
			)
			.subscribe((raw) => {
				let tranformedData;
				if (this.table.dataAdapterFn) {
					tranformedData = this.table.dataAdapterFn(raw);
				} else {
					tranformedData = raw;
				}
				this.dataFetchLoading = false;
				this.data = tranformedData;
				this.rawData = tranformedData.content;
				this.populateRawDataIcons();
				this.populateRawDropdownActionItems();
				this.cd.detectChanges();
			});
	}

	allFilters(): DataFiltro {
		const filtros = this.fetchFiltros();
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));
		/**
		 * Merge 'filters'
		 */
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}
		return filtros;
	}

	addFilter(name: string, value: string) {
		if (this.temporaryFilters === null || this.temporaryFilters === undefined) {
			this.temporaryFilters = { filters: {} };
		}
		this.temporaryFilters.filters[name] = value;
	}

	removeFilter(name: string) {
		if (this.temporaryFilters !== null && this.temporaryFilters !== undefined) {
			delete this.temporaryFilters.filters[name];
		}
	}

	fetchFiltros(): DataFiltro {
		const filtros: DataFiltro = {
			filters:
				this.temporaryFilters && this.temporaryFilters.filters
					? this.temporaryFilters.filters
					: {},
			configs:
				this.temporaryFilters && this.temporaryFilters.configs
					? this.temporaryFilters.configs
					: {},
			sortField: this.table.state.ordenacaoColuna,
			sortDirection: this.table.state.ordenacaoDirecao,
		};

		if (this.table.quickSearch) {
			filtros.filters.quicksearchValue = this.quickSearchControl.value;
			filtros.filters.quicksearchFields = this.fetchQuicksearchFields();
		}

		if (
			this.quickSearchCustomValue !== null &&
			this.quickSearchCustomValue !== undefined &&
			this.quickSearchCustomValue.trim() !== ""
		) {
			filtros.filters.quicksearchValue = this.quickSearchCustomValue;
		}

		if (this.table.pagination) {
			filtros.page = this.table.state.paginaNumero;
			filtros.size = this.table.state.paginaTamanho;
		}
		this.sanitizeFilter(filtros);
		const copy: DataFiltro = {};
		Object.assign(copy, filtros);
		return copy;
	}

	private sanitizeFilter(filtro: DataFiltro) {
		if (!filtro.sortField) {
			filtro.sortDirection = null;
		}
		this.removeUnsetValues(filtro);
		this.removeUnsetValues(filtro.filters);
	}

	private removeUnsetValues(object) {
		for (const key in object) {
			const ownProperty = Object.prototype.hasOwnProperty.call(object, key);
			if (ownProperty) {
				const element = object[key];
				if (element === undefined || element === null || element.length === 0) {
					delete object[key];
				}
			}
		}
	}

	private fetchQuicksearchFields() {
		const result = [];
		this.table.columns.forEach((column) => {
			if (column.buscaRapida) {
				result.push(column.nome);
			}
		});
		return result;
	}

	private autoFocus() {
		if (this.table.quickSearch) {
			this.quickSearch.nativeElement.focus();
		}
	}
}
