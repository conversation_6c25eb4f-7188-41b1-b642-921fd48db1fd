<ng-container *ngIf="table">
	<div
		*ngIf="
			table.quickSearch ||
			tableTitle ||
			showShare ||
			(table.showFilters && filterConfig) ||
			table.buttons
		"
		class="pacto-table-title-block">
		<div *ngIf="tableTitle" class="title-aux-wrapper">
			<div *ngIf="isTemplate(tableTitle)" class="table-title">
				<ng-container *ngTemplateOutlet="tableTitle"></ng-container>
			</div>
			<div *ngIf="!isTemplate(tableTitle)" class="table-title">
				{{ tableTitle }}
			</div>
			<div *ngIf="isTemplate(tableDescription)" class="table-description">
				<ng-container *ngTemplateOutlet="tableDescription"></ng-container>
			</div>
			<div *ngIf="!isTemplate(tableDescription)" class="table-description">
				{{ tableDescription }}
			</div>
		</div>

		<!-- <div class="command-buttons" *ngIf="table.valueRowCheck">
      <div class="command" (click)="selectAll()">
        <span class="icone"><i class="pct pct-check-square"></i></span><span class="texto">SELECIONAR TODOS</span>
      </div>
      <div class="command" (click)="clearAll()">
        <span class="icone"><i class="pct pct-trash-2"></i></span><span class="texto">LIMPAR SELEÇÃO</span>
      </div>
    </div> -->

		<div class="search">
			<div *ngIf="table.quickSearch">
				<input
					#quickSearch
					[formControl]="quickSearchControl"
					class="form-control"
					id="input-busca-rapida"
					type="text" />
				<i class="pct pct-search"></i>
			</div>
		</div>

		<div class="actions">
			<div *ngIf="customActionsTop">
				<ng-container *ngTemplateOutlet="customActionsTop"></ng-container>
			</div>

			<pacto-share-button-cobranca
				*ngIf="showShare"
				[columns]="table.columns"
				[endpoint]="table.endpointUrl"
				[filterConfig]="filterConfig"
				[filtros]="allFilters()"
				[titulo]="tableTitle"
				[total]="data.totalElements"></pacto-share-button-cobranca>

			<div *ngIf="table.showFilters && filterConfig" class="filter-wrapper">
				<div
					#filterDropdown="ngbDropdown"
					[autoClose]="false"
					[placement]="'bottom-right'"
					class="d-inline-block"
					ngbDropdown>
					<button
						#filterToggleButton
						class="btn btn-primary btn-icon"
						id="filtros-dropdown"
						ngbDropdownToggle>
						<i class="pct pct-filter"></i>
						<span class="icon-drop">
							<i class="fa fa-angle-up"></i>
							<i class="fa fa-angle-down"></i>
						</span>
					</button>

					<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
						<pacto-data-grid-filter
							#dataGridFilter
							(configUpdate)="alterfilterConfigUpdate($event)"
							(filter)="filterHandler($event)"
							[columnsConfig]="table.columns"
							[config]="filterConfig"></pacto-data-grid-filter>
					</div>
				</div>
			</div>

			<div>
				<button
					(click)="btnCLickHandler()"
					*ngIf="table.buttons"
					class="btn btn-primary novo-botao"
					id="{{ table.buttons.id }}">
					<ng-container
						*ngTemplateOutlet="table.buttons.conteudo"></ng-container>
				</button>
			</div>
		</div>
	</div>

	<div [class.rm-relative]="table.valueRowCheck" class="table-content">
		<pacto-relatorio-cobranca-renderer
			(cellClick)="cellClickHandler($event)"
			(iconClick)="iconClick.emit($event)"
			(rowCheck)="checkSelectdItem($event)"
			(rowClick)="rowClick.emit($event)"
			(selectAll)="selectAll($event)"
			(sort)="sortUpdateHandler()"
			[accordion]="accordion"
			[allowsCheck]="allowsCheck"
			[alternatingColors]="alternatingColors"
			[customContent]="customContent"
			[dataGridConfig]="table"
			[data]="rawData"
			[emptyStateMessage]="emptyStateMessage"
			[loading]="dataFetchLoading"
			[rawDataIcons]="rawDataIcons"
			[rawDropdownActionItems]="
				rawDropdownActionItems
			"></pacto-relatorio-cobranca-renderer>

		<div
			*ngIf="table.showSimpleTotalCount && !dataFetchLoading"
			class="simple-total-row">
			{{ table.pagination ? data.totalElements : rawData.length }}
			<span>items</span>
		</div>

		<div [ngClass]="customActions ? 'footer-row' : 'footer-row-no-flex'">
			<div *ngIf="customActions">
				<ng-container *ngTemplateOutlet="customActions"></ng-container>
			</div>
			<div class="footer-page">
				<ng-container
					*ngIf="table.pagination && !dataFetchLoading && rawData.length">
					<ngb-pagination
						(pageChange)="pageChangeHandler($event)"
						[(page)]="ngbPage"
						[boundaryLinks]="true"
						[collectionSize]="data.totalElements"
						[ellipses]="false"
						[maxSize]="7"
						[pageSize]="data.size"
						[size]="'sm'"
						class="d-flex justify-content-end margin-left"></ngb-pagination>

					<pacto-cat-select
						[control]="pageSizeControl"
						[items]="itensPerPage"
						[size]="'SMALL'"></pacto-cat-select>
					<div class="total-values">
						<span>Mostrando</span>
						<span class="value">{{ data.content.length }}</span>
						<span>de</span>
						<span class="value">{{ data.totalElements }}</span>
					</div>
				</ng-container>
			</div>
		</div>
	</div>
</ng-container>
