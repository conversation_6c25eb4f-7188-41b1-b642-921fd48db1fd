@import "dist/ui-kit/assets/import.scss";

.pacto-primary {
	background-color: $azulim05;
	color: white;
	margin-left: 24px;
}

#contentShare {
	height: 245px;
	width: 336px;

	.options-share {
		margin: 8px 16px;
		display: flex;
		justify-content: space-between;
		border-bottom: #f3f3f4 1px solid;

		i {
			margin-right: 2px;
			margin-left: 4px;
			color: $azulim05;
		}

		span {
			font-size: 14px;
			color: #a1a5aa;
		}
	}

	.links {
		margin: 8px 16px;
		display: flex;

		.link {
			margin-right: 5px;
			color: #a1a5aa;
			cursor: pointer;
			padding: 10px;
			border-radius: 6px;

			i {
				margin-right: 6px;
				color: $azulim05;
			}

			&:hover {
				background-color: #eff2f7;
			}
		}
	}

	.sends {
		margin: 0px 16px;

		.send {
			margin-top: 8px;
			color: #a1a5aa;

			i {
				margin-left: 10px;
				margin-right: 6px;
				color: $azulim05;
			}

			.input-send {
				padding: 4px 10px;
				position: relative;

				input {
					border-radius: 6px;
				}

				span {
					position: absolute;
					top: 4px;
					right: 10px;
					background: $azulim05;
					border-radius: 6px;
					width: 36px;
					height: 37px;
					line-height: 41px;
					border: 1px solid $azulim05;
					cursor: pointer;

					i {
						color: white;
					}
				}
			}
		}
	}
}

.dropdown-toggle::after {
	display: none;
}

.btn.pacto-primary {
	position: relative;

	&:not(.novo-botao) {
		padding-right: 35px;
	}

	margin-right: 10px;
	margin-left: 10px;

	.btn-share-label {
		margin-right: 10px;
	}

	.icon-drop {
		position: absolute;
		top: 0;
		right: 0;
		width: 35px;
		height: 38px;
		line-height: 38px;
		text-align: center;
		border-left: 1px solid white;
	}
}

.pct-chevron-up {
	display: none;
}

.pct-chevron-down {
	display: inline-block;
}

.show {
	.pct-chevron-down {
		display: none;
	}

	.pct-chevron-up {
		display: inline-block;
	}
}
