import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

@Injectable({
	providedIn: "root",
})
export class ShareCobrancaService {
	constructor(private http: HttpClient) {}

	share(dados: any): Observable<any> {
		const url = `/share-ms/share`;
		return this.http.post(url, dados).pipe(map((result: any) => result));
	}

	send(dados: any): Observable<any> {
		const url = `/share-ms/send`;
		return this.http.post(url, dados).pipe(map((result: any) => result));
	}
}
