import {
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { SnotifyService } from "ng-snotify";
import { ShareCobrancaService } from "./share-cobranca.service";

@Component({
	selector: "pacto-share-button-cobranca",
	templateUrl: "./share-button-cobranca.component.html",
	styleUrls: ["./share-button-cobranca.component.scss"],
})
export class ShareButtonCobrancaComponent implements OnInit {
	@Input() titulo: TemplateRef<any> | string;
	@Input() endpoint: string;
	@Input() columns: any;
	@Input() filtros: any;
	@Input() filterConfig: any;
	@Input() total: number;
	@ViewChild("inputFone", { static: false }) inputFone;
	@ViewChild("inputEmail", { static: false }) inputEmail;
	public link: string;
	public tipo = "PDF";
	public loading = false;

	constructor(
		private service: ShareCobrancaService,
		private notificationService: SnotifyService,
		private translateService: TranslateService
	) {}

	ngOnInit() {}

	onTipoChange(value) {
		this.tipo = value;
	}

	salvar(): void {
		this.exportar("save");
	}

	copiar(): void {
		this.exportar("copy");
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else {
			return type !== "string";
		}
	}

	get colunasExportar() {
		if (this.columns) {
			const colunasExportar = new Array();
			this.columns.forEach((col) => {
				if (
					(col.mostrarTitulo && col.visible === true) ||
					(col.visible === false &&
						col.exportNoVisible === true &&
						this.tipo === "XLS")
				) {
					if (col.date) {
						colunasExportar.push({
							campo: col.campo,
							date: col.date,
						});
					} else {
						colunasExportar.push({
							campo: col.campo,
						});
					}
				}
			});
			return colunasExportar;
		}
		return [];
	}

	configsFiltros() {
		if (this.filterConfig && this.filterConfig.filters) {
			const cfgs = new Array();
			this.filterConfig.filters.forEach((cfg) => {
				if (cfg.name && cfg.options) {
					cfgs.push({
						name: cfg.name,
						options: cfg.options,
					});
				}
			});
			return cfgs;
		}
		return [];
	}

	exportar(destino): void {
		if (this.total && this.total > 5000) {
			this.notificationService.error(
				this.translateService.instant("relatorio.tabela5KRegistros")
			);
			return;
		}
		let dados: any = {
			endpoint: this.endpoint,
			filtros: this.filtros,
			titulo: new Date().getTime(),
			filterConfig: this.configsFiltros(),
			colunas: this.colunasExportar,
			destino,
			tipo: this.tipo,
		};

		if (destino === "save") {
			//No front, o campo status tráz a informação diferente do que os clientes querem
			//É necessário alterar para codigoRetorno que é o campo solicitado pelo cliente
			dados.colunas.forEach((coluna: any) => {
				if (coluna.campo === "status") {
					coluna.campo = "codigoRetorno";
				}
			});
		}

		if (destino === "email") {
			dados.email = this.inputEmail.nativeElement.value;
			this.service.send(dados).subscribe((retorno) => {
				this.notificationService.success(
					this.translateService.instant("relatorio.emailEnviadoSucesso")
				);
			});
		} else {
			this.service.share(dados).subscribe(
				(retorno) => {
					this.link = retorno.content.url;
					if (destino === "whatsapp") {
						const valorInserido = this.inputFone.nativeElement.value;
						const valorInput = Number(
							valorInserido
								.replace("(", "")
								.replace(")", "")
								.replace(" ", "")
								.replace("-", "")
						);
						const target =
							"https://api.whatsapp.com/send?phone=" +
							"55" +
							valorInput +
							"&text=" +
							retorno.content.msg;
						window.open(target, "_blank");
					} else if (destino === "copy") {
						navigator.clipboard.writeText(retorno.content.url);
						this.notificationService.success(
							this.translateService.instant("relatorio.linkCopiado")
						);
					} else {
						window.open(this.link, "_blank");
					}
				},
				(httpError) => {
					if (httpError.error.meta && httpError.error.meta.error) {
						this.notificationService.error(httpError.error.meta.message);
					} else {
						this.notificationService.error(
							this.translateService.instant("relatorio.shareErrorConnection")
						);
					}
				}
			);
		}
	}

	enviarWhatsApp() {
		this.exportar("whatsapp");
	}

	async copyMessage(msg) {
		const textarea = document.createElement("textarea");
		textarea.textContent = msg;
		document.body.appendChild(textarea);
		const selection = document.getSelection();
		const range = document.createRange();
		range.selectNode(textarea);
		selection.removeAllRanges();
		selection.addRange(range);
		console.log("copy success", document.execCommand("copy"));
		selection.removeAllRanges();
		document.body.removeChild(textarea);
		this.delay(500).then((any) => {
			this.notificationService.success(
				this.translateService.instant("relatorio.linkCopiado")
			);
		});
	}

	async delay(ms: number) {
		await new Promise((resolve) => setTimeout(() => resolve(), ms)).then();
	}

	enviarEmail(): void {
		this.exportar("email");
	}
}
