import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-erro-parcela",
	templateUrl: "./modal-erro-parcela.component.html",
	styleUrls: ["./modal-erro-parcela.component.scss"],
})
export class ModalErroParcelaComponent implements OnInit {
	@Input() resultado =
		"Algo inesperado aconteceu no envio. Por favor, tente novamente";

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	confirm() {
		this.openModal.close(true);
	}
}
