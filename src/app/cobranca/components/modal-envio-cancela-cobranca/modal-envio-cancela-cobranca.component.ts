import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-envio-cancela-cobranca",
	templateUrl: "./modal-envio-cancela-cobranca.component.html",
	styleUrls: ["./modal-envio-cancela-cobranca.component.scss"],
})
export class ModalEnvioCancelaCobrancaComponent implements OnInit {
	@Input() resultado =
		"O cancelamento das cobranças foram enviadas com sucesso.";

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	confirm() {
		this.openModal.close(true);
	}
}
