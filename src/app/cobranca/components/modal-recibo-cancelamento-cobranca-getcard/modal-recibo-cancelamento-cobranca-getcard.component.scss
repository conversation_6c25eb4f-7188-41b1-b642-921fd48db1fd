/* Estilos para o contêiner flex */
.flex-container {
	display: flex; /* Ativa o Flexbox */
	justify-content: center; /* Centraliza o conteúdo horizontalmente */
	align-items: center; /* Centraliza o conteúdo verticalmente */
	width: 100%; /* Define a largura como 100% */
	height: 100%; /* Faz a div ter altura de 100% */
	overflow: auto; /* Caso o texto ultrapasse o tamanho da tela, permite rolar */
}

pre {
	white-space: pre-wrap; /* Preserva quebras de linha e espaços */
	word-wrap: break-word; /* Quebra palavras longas que excedem o tamanho */
	margin: 0; /* Remove margens automáticas */
	padding: 10px; /* Um pouco de preenchimento para deixar o texto mais legível */
	background-color: #f4f4f4; /* Pode adicionar um fundo, para destacar o texto */
	border-radius: 5px; /* <PERSON><PERSON><PERSON> arredonda<PERSON> (opcional) */
	max-width: 90%; /* Limita a largura do texto */
	box-sizing: border-box; /* Garantir que padding e borda não extrapolem a largura */
}

/* Estilos para o contêiner do botão */
.button-container {
	display: flex; /* Ativa o Flexbox */
	justify-content: center; /* Centraliza o botão horizontalmente */
	margin-top: 50px; /* Adiciona um espaçamento de 50px da div superior */
}

/* Estilo para o botão */
.print-button {
	padding: 10px 20px; /* Adiciona um pouco de preenchimento para o botão */
	background-color: #007bff; /* Cor de fundo do botão (você pode alterar) */
	color: white; /* Cor do texto do botão */
	border: none; /* Remove a borda do botão */
	border-radius: 5px; /* Bordas arredondadas */
	font-size: 16px; /* Define o tamanho da fonte do botão */
	cursor: pointer; /* Muda o cursor para indicar que é clicável */
	transition: background-color 0.3s; /* Efeito suave ao passar o mouse */
	margin-bottom: 20px;
}

.print-button:hover {
	background-color: #0056b3; /* Cor de fundo ao passar o mouse (efeito hover) */
}
