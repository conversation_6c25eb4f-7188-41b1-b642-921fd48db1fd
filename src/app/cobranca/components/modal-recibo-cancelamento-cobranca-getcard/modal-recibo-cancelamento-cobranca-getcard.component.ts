import { Component, Input, OnInit } from "@angular/core";

@Component({
	selector: "pacto-modal-recibo-cancelamento-cobranca-getcard",
	templateUrl: "./modal-recibo-cancelamento-cobranca-getcard.component.html",
	styleUrls: ["./modal-recibo-cancelamento-cobranca-getcard.component.scss"],
})
export class ModalReciboCancelamentoCobrancaGetcardComponent implements OnInit {
	@Input()
	public readonly dadoApresentarModal: string;

	constructor() {}

	ngOnInit() {}

	imprimirRecibo(): void {
		const conteudoElement = document.getElementById("conteudo-impressao");

		if (conteudoElement) {
			// Verifica se o elemento foi encontrado
			const conteudo = conteudoElement.innerHTML;

			const janelaImpressao = window.open("", "_blank"); // Cria uma nova janela para impressão
			janelaImpressao.document.write("<html><head><title>Impressão</title>");

			// Adicionando estilos para garantir que a formatação seja mantida
			janelaImpressao.document.write(`
      <style>
        body {
          font-family: Arial, sans-serif;
          font-size: 14px;
          margin: 20px;
        }
        pre {
          white-space: pre-wrap; /* Preserva quebras de linha e espaços */
          word-wrap: break-word; /* Quebra palavras longas */
          font-family: monospace; /* Fonte para manter a formatação */
          background-color: #f4f4f4; /* Cor de fundo, para destacar o conteúdo */
          padding: 10px; /* Espaçamento para o conteúdo */
          border-radius: 5px; /* Borda arredondada */
        }
      </style>
    `);

			// Envolvendo o conteúdo em uma tag <pre> para preservar a formatação
			janelaImpressao.document.write(
				"<body><pre>" + conteudo + "</pre></body>"
			);
			janelaImpressao.document.write("</html>");
			janelaImpressao.document.close();
			janelaImpressao.focus();
			janelaImpressao.print(); // Chama a função de impressão do navegador
		} else {
			console.error("Conteúdo para impressão não encontrado!");
		}
	}
}
