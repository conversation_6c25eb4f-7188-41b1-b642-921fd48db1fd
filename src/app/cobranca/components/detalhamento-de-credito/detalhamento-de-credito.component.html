<div class="container-detalhamento">
	<div class="titulo">
		<h5>Dados dos créditos</h5>
		<hr />
	</div>
	<pacto-cat-tabs-transparent
		#tabs
		(activateTab)="activateTabHandler($event)"
		class="tabs-creditos">
		<ng-template
			label="Total utilizado ({{ totalElementsGeral }})"
			pactoTabTransparent="geral"></ng-template>
		<ng-template
			label="Utilizado em remessa ({{ totalElementsRemessa }})"
			pactoTabTransparent="remessa"></ng-template>
		<ng-template
			label="Utilizado em transação ({{ totalElementsTransacao }})"
			pactoTabTransparent="transacao"></ng-template>
		<ng-template
			label="Utilizado em pix ({{ totalElementsPix }})"
			pactoTabTransparent="pix"></ng-template>
	</pacto-cat-tabs-transparent>

	<div [hidden]="!habilitarGeral" class="grid-relatorio">
		<pacto-relatorio-cobranca
			#relatorioGeral
			[customActions]="btnBaixarXLSGeral"
			[emptyStateMessage]="'Não existem dados no momento'"
			[showShare]="false"
			[table]="tabelaCreditos.geral"></pacto-relatorio-cobranca>
		<ng-template #btnBaixarXLSGeral>
			<pacto-exportar-relatorio
				[table]="tabelaCreditos.geral"
				[tipo]="'XLS'"
				[total]="totalElementsGeral">
				<pacto-cat-button
					[full]="true"
					[icon]="'file-text'"
					[label]="'Baixar XLS'"></pacto-cat-button>
			</pacto-exportar-relatorio>
		</ng-template>
	</div>
	<div [hidden]="!habilitarRemessa" class="grid-relatorio">
		<pacto-relatorio-cobranca
			#relatorioRemessa
			[customActions]="btnBaixarXLSRemessa"
			[emptyStateMessage]="'Não existem dados no momento'"
			[showShare]="false"
			[table]="tabelaCreditos.remessa"></pacto-relatorio-cobranca>
		<ng-template #btnBaixarXLSRemessa>
			<pacto-exportar-relatorio
				[table]="tabelaCreditos.remessa"
				[tipo]="'XLS'"
				[total]="totalElementsRemessa">
				<pacto-cat-button
					[full]="true"
					[icon]="'file-text'"
					[label]="'Baixar XLS'"></pacto-cat-button>
			</pacto-exportar-relatorio>
		</ng-template>
	</div>
	<div [hidden]="!habilitarTransacao" class="grid-relatorio">
		<pacto-relatorio-cobranca
			#relatorioTransacao
			[customActions]="btnBaixarXLSTransacao"
			[emptyStateMessage]="'Não existem dados no momento'"
			[showShare]="false"
			[table]="tabelaCreditos.transacao"></pacto-relatorio-cobranca>
		<ng-template #btnBaixarXLSTransacao>
			<pacto-exportar-relatorio
				[table]="tabelaCreditos.transacao"
				[tipo]="'XLS'"
				[total]="totalElementsTransacao">
				<pacto-cat-button
					[full]="true"
					[icon]="'file-text'"
					[label]="'Baixar XLS'"></pacto-cat-button>
			</pacto-exportar-relatorio>
		</ng-template>
	</div>
	<div [hidden]="!habilitarPix" class="grid-relatorio">
		<pacto-relatorio-cobranca
			#relatorioPix
			[customActions]="btnBaixarXLSPix"
			[emptyStateMessage]="'Não existem dados no momento'"
			[showShare]="false"
			[table]="tabelaCreditos.pix"></pacto-relatorio-cobranca>
		<ng-template #btnBaixarXLSPix>
			<pacto-exportar-relatorio
				[table]="tabelaCreditos.pix"
				[tipo]="'XLS'"
				[total]="totalElementsPix">
				<pacto-cat-button
					[full]="true"
					[icon]="'file-text'"
					[label]="'Baixar XLS'"></pacto-cat-button>
			</pacto-exportar-relatorio>
		</ng-template>
	</div>
</div>

<div class="container-detalhamento">
	<div class="titulo">
		<h5>Histórico</h5>
		<hr />
	</div>
	<pacto-relatorio-cobranca
		[emptyStateMessage]="'Não existem dados no momento'"
		[showShare]="false"
		[table]="tabelaHistorico"></pacto-relatorio-cobranca>
</div>

<ng-template #celulaAluno let-item="item">
	<a (click)="navegarParaTelaDoAluno(item)" class="list-item-link">
		{{ item.nome | captalize }}
	</a>
</ng-template>

<ng-template #celulaItens let-itens="item">
	<pacto-exportar-relatorio
		*ngIf="itens.existeItens"
		[table]="gerarGridConfigDeItensDoHistorico(itens.codigo)"
		[tipo]="'XLS'">
		<a
			style="
				color: #1998fc;
				font-size: 0.8rem;
				text-decoration: underline;
				display: block;
				width: 80px;
				cursor: pointer;
			">
			Baixar itens
		</a>
	</pacto-exportar-relatorio>
</ng-template>

<ng-template #celulaObservacao let-item="item">
	<div [innerHTML]="item.observacao_Apresentar"></div>
</ng-template>
