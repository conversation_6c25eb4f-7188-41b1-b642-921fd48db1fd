import { HttpParams } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CatTabsTransparentComponent, PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "pacto-detalhamento-de-credito",
	templateUrl: "./detalhamento-de-credito.component.html",
	styleUrls: ["./detalhamento-de-credito.component.scss"],
})
export class DetalhamentoDeCreditoComponent implements OnInit {
	@Input() empresas;
	@ViewChild("tabs", { static: true }) tabs: CatTabsTransparentComponent;
	@ViewChild("celulaAluno", { static: true }) celulaAluno;
	@ViewChild("celulaObservacao", { static: true }) celulaObservacao;
	@ViewChild("celulaItens", { static: true }) celulaItens;

	public tabelaCreditos: {
		geral?: PactoDataGridConfig;
		remessa?: PactoDataGridConfig;
		transacao?: PactoDataGridConfig;
		pix?: PactoDataGridConfig;
	} = {};

	public tabelaHistorico: PactoDataGridConfig;

	public totalElementsGeral = 0;
	public totalElementsRemessa = 0;
	public totalElementsTransacao = 0;
	public totalElementsPix = 0;

	constructor(
		private rest: RestService,
		private session: SessionService,
		private cd: ChangeDetectorRef,
		private router: Router,
		private modal: NgbModal
	) {}

	public ngOnInit() {
		this.tabs.tabId = "geral";

		this.tabelaCreditos.geral = this.carregarDadosDosCreditos({
			type: "geral",
		});
		this.tabelaCreditos.remessa = this.carregarDadosDosCreditos({
			type: "remessa",
		});
		this.tabelaCreditos.transacao = this.carregarDadosDosCreditos({
			type: "transacao",
		});
		this.tabelaCreditos.pix = this.carregarDadosDosCreditos({ type: "pix" });

		this.tabelaHistorico = this.carregarHistorico();
	}

	public get habilitarGeral() {
		return this.tabs.tabId === "geral";
	}

	public get habilitarRemessa() {
		return this.tabs.tabId === "remessa";
	}

	public get habilitarTransacao() {
		return this.tabs.tabId === "transacao";
	}

	public get habilitarPix() {
		return this.tabs.tabId === "pix";
	}

	public activateTabHandler(e) {
		// console.log(e);
	}

	public navegarParaTelaDoAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
		this.modal.dismissAll();
	}

	private carregarDadosDosCreditos({ type }) {
		const params: HttpParams = new HttpParams()
			.append("op", "creditolista")
			.append("key", this.session.chave)
			.append("e", this.empresas)
			.append("tipo", type);

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?${params.toString()}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			endpointParamsType: "query",
			dataAdapterFn: (serverData) => {
				this.setTotalElements({
					type,
					totalElements: serverData.totalElements,
				});
				return serverData;
			},
			columns: [
				{
					nome: "nome",
					titulo: "Nome",
					visible: true,
					ordenavel: false,
					celula: this.celulaAluno,
				},
				{
					nome: "movParcela",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricaoParcela",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor_Apresentar",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	private setTotalElements({ type, totalElements }) {
		switch (type) {
			case "geral":
				this.totalElementsGeral = totalElements;
				break;
			case "remessa":
				this.totalElementsRemessa = totalElements;
				break;
			case "transacao":
				this.totalElementsTransacao = totalElements;
				break;
			case "pix":
				this.totalElementsPix = totalElements;
				break;
		}
		this.cd.detectChanges();
	}

	private getTotalElements({ type }) {
		switch (type) {
			case "geral":
				return this.totalElementsGeral;
			case "remessa":
				return this.totalElementsRemessa;
			case "transacao":
				return this.totalElementsTransacao;
			case "pix":
				return this.totalElementsPix;
		}
	}

	private carregarHistorico() {
		const params: HttpParams = new HttpParams()
			.append("op", "creditohistorico")
			.append("key", this.session.chave)
			.append("e", this.empresas);

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?${params.toString()}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			endpointParamsType: "query",
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataCobranca",
					titulo: "Data e hora",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "quantidade",
					titulo: "Quantidade",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valorTotal_Apresentar",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "usuario",
					titulo: "Usuário",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "observacao_Apresentar",
					titulo: "Observação",
					visible: true,
					ordenavel: false,
					celula: this.celulaObservacao,
				},
				{
					nome: "codigo",
					titulo: "Itens",
					visible: true,
					ordenavel: false,
					celula: this.celulaItens,
				},
			],
		});
	}

	public gerarGridConfigDeItensDoHistorico(codigo) {
		const params: HttpParams = new HttpParams()
			.append("op", "creditohistoricoitens")
			.append("key", this.session.chave)
			.append("codigo", codigo);

		return new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?${params.toString()}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			endpointParamsType: "query",
			columns: [
				{
					nome: "tipoItem",
					titulo: "Tipo",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "pessoa",
					titulo: "Pessoa",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "transacao",
					titulo: "Transação",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricaoParcela",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor_Apresentar",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "movParcela",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
				},
			],
		});
	}
}
