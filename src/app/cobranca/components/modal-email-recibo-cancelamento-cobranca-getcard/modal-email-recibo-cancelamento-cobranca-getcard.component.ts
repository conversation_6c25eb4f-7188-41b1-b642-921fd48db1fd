import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-recibo-cancelamento-cobranca-getcard",
	templateUrl:
		"./modal-email-recibo-cancelamento-cobranca-getcard.component.html",
	styleUrls: [
		"./modal-email-recibo-cancelamento-cobranca-getcard.component.scss",
	],
})
export class ModalEmailReciboCancelamentoCobrancaGetcardComponent
	implements OnInit
{
	@Input() email: string = "";
	@Input() codigoRecibo: number;
	@Input() msgRecibo: string = "";

	formGroup = new FormGroup({
		email: new FormControl("", [Validators.required, Validators.email]),
	});

	constructor(
		private notificationService: SnotifyService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.formGroup.get("email").setValue(this.email);
		console.log(this.codigoRecibo);
		console.log(this.msgRecibo);
	}

	enviar() {
		if (this.formGroup.valid) {
			this.telaClienteService
				.enviarEmailReciboCancelamentoCobrancaGetcard(
					this.sessionService.chave,
					this.codigoRecibo,
					this.msgRecibo,
					this.formGroup.get("email").value,
					this.sessionService.empresaId
				)
				.subscribe(
					(resp) => {
						this.notificationService.success(resp.content);
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.message) {
							this.notificationService.error(err.meta.message);
						}
					}
				);
		} else {
			this.notificationService.error("O campo e-mail é obrigatório!");
		}
	}
}
