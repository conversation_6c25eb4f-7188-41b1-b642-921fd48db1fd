@import "dist/ui-kit/assets/import.scss";

.wrapper {
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	flex-direction: column;
	color: #43474b;
	min-height: 25rem;
	padding: 1rem;
}

#type {
	font-size: 8.75rem;
}

.pct-check {
	color: $verdinho05;
}

.pct-alert-triangle {
	color: $pequizaoPri;
}

.pct-x-circle,
.pct-x {
	color: $hellboy04;
}

.title {
	color: #43474b;
	font-weight: bold;
	font-size: 1.5rem;
	line-height: 1.5rem;
	text-align: center;
}

.subtitle,
.body {
	font-weight: normal;
	font-size: 1.25rem;
	line-height: 1.25rem;
	text-align: center;
}

.actions {
	display: flex;
	justify-content: space-evenly;
	align-items: center;

	pacto-cat-button {
		min-width: 6.25rem;
		margin-left: 0.5rem;
		margin-right: 0.5rem;
	}
}

pacto-cat-button::ng-deep {
	.pacto-button {
		padding: 0.875rem 0.313rem;
	}

	.pacto-button.large {
		line-height: 0.75rem;
	}
}
