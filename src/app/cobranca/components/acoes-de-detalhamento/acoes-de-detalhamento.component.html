<div class="d-flex justify-content-between acoes-de-detalhamento">
	<div
		*ngIf="cobranca && cobranca.ambiente === 2"
		class="pct pct-alert-triangle cor-laranjinha05 font-16"
		id="pay-transacao-teste"
		ngbTooltip="Transação realizada em ambiente de Homologação/Sandbox"></div>
	<i
		(click)="abrirParametrosDeEnvio()"
		*ngIf="cobranca && (cobranca.transacao || cobranca.codigo)"
		class="pct pct-corner-up-right"
		id="pay-abrir-parametro-envio"
		ngbTooltip="Visualizar os parâmetros de envio"></i>
	<i
		(click)="abrirParametrosDeRetorno()"
		*ngIf="cobranca && (cobranca.transacao || cobranca.codigo)"
		class="pct pct-corner-up-left"
		id="pay-abrir-parametro-retorno"
		ngbTooltip="Visualizar os parâmetros de resposta"></i>
	<i
		(click)="abrirDetalhesDaCobranca()"
		*ngIf="cobranca && !cobranca.transacaoVerificarCartao"
		class="pct pct-zoom-in"
		id="pay-abrir-detalhe-cobranca"
		ngbTooltip="Mais detalhes sobre esta transação"></i>
	<i
		(click)="abrirDetalhesDaParcela()"
		*ngIf="parcela"
		class="pct pct-zoom-in"
		id="pay-abrir-detalhe-parcela"></i>
	<i
		(click)="abrirSincronizar()"
		*ngIf="
			cobranca && !cobranca.transacaoVerificarCartao && cobranca.podeSincronizar
		"
		class="pct pct-refresh-cw"
		id="pay-sincronizar-cobranca"
		ngbTooltip="Consultar situação da transação"></i>
	<i
		(click)="abrirComprovanteCancelamentoTransacao()"
		*ngIf="
			cobranca &&
			!cobranca.transacaoVerificarCartao &&
			cobranca.tipo_cobranca === 2 &&
			cobranca.situacao === 7
		"
		class="pct pct-file"
		id="pay-link-comprovante-cancelamento"
		ngbTooltip="Download do comprovante de cancelamento da transação"></i>
	<i
		(click)="enviarComprovanteCancelamentoTransacao()"
		*ngIf="
			cobranca &&
			!cobranca.transacaoVerificarCartao &&
			cobranca.tipo_cobranca === 2 &&
			cobranca.situacao === 7
		"
		class="pct pct-send"
		id="pay-email-comprovante-cancelamento"
		ngbTooltip="Enviar e-mail do comprovante de cancelamento"></i>
	<i
		(click)="abrirCancelarCobranca()"
		*ngIf="cobranca && cobranca.podeCancelar"
		class="pct pct-x-circle cor-hellboy05"
		id="pay-cancelar-cobranca"
		ngbTooltip="Cancelar/estornar transação"></i>
	<i
		(click)="abrirRetentar()"
		*ngIf="
			cobranca &&
			!cobranca.transacaoVerificarCartao &&
			cobranca.tipo_cobranca === 2 &&
			(cobranca.situacao === 1 ||
				cobranca.situacao === 3 ||
				cobranca.situacao === 7)
		"
		class="pct pct-repeat"
		id="pay-retentar-transacao"
		ngbTooltip="Realizar retentativa imediata da transação"></i>
</div>
