import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { transformMoney } from "@base-shared/utils/money.util";
import { PactoPayApiDetalheService } from "pactopay-api";
import { PactoDataGridConfig } from "ui-kit";
import { Parcela } from "../../../models/parcela.model";

@Component({
	selector: "pacto-detalhamento-da-parcela",
	templateUrl: "./detalhamento-da-parcela.component.html",
	styleUrls: ["./detalhamento-da-parcela.component.scss"],
})
export class DetalhamentoDaParcelaComponent implements OnInit {
	@Input() public parcela: Parcela;
	public detalhamento;

	@ViewChild("celulaStatusCobranca", { static: true })
	public celulaStatusCobranca: TemplateRef<any>;
	@ViewChild("celulaCodigoCobranca", { static: true })
	public celulaCodigoCobranca: TemplateRef<any>;
	@ViewChild("celulaTitular", { static: true })
	public celulaTitular: TemplateRef<any>;

	cobrancasRelatorioConfig: PactoDataGridConfig;

	constructor(
		private readonly restService: RestService,
		private readonly sessionService: SessionService,
		private readonly pactoPayApiDetalhe: PactoPayApiDetalheService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initCobrancasRelatorioConfig();

		this.pactoPayApiDetalhe
			.obterDetalhesDaParcela(this.parcela.codigo)
			.subscribe((result) => {
				this.detalhamento = result;
				this.cd.detectChanges();
			});
	}

	initCobrancasRelatorioConfig() {
		this.cobrancasRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.restService.buildFullUrlZw(
				`pactopay/dash?op=cobrancasparcela&key=${this.sessionService.chave}&p=${this.parcela.codigo}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			ghostAmount: 3,
			ghostLoad: true,
			actions: [],
			columns: [
				{
					nome: "transacao",
					titulo: "Cód.: Cobrança",
					celula: this.celulaCodigoCobranca,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "titular",
					titulo: "Titular do cartão",
					visible: true,
					ordenavel: false,
					celula: this.celulaTitular,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					celula: this.celulaStatusCobranca,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data",
					visible: true,
					ordenavel: false,
					valueTransform: (value: string) => {
						return value ? value.replace("-", "ás") : value;
					},
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: transformMoney,
				},
			],
		});
	}
}
