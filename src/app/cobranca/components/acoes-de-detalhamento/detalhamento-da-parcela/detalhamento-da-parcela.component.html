<div class="content-wrapper">
	<div class="card-table" style="margin-top: 32px">
		<span class="title"><PERSON><PERSON> da parcela</span>
		<div class="divider"></div>
		<table *ngIf="detalhamento">
			<tbody>
				<tr>
					<td>
						<span class="title">Matrícula:</span>
						<p>{{ detalhamento.pessoa?.matricula }}</p>
					</td>
					<td>
						<span class="title">Aluno:</span>
						<p>{{ detalhamento.pessoa?.nome | captalize }}</p>
					</td>
					<td>
						<span class="title">Código da parcela:</span>
						<p>{{ detalhamento.codigo }}</p>
					</td>
					<td>
						<span class="title">Descrição da parcela:</span>
						<p>{{ detalhamento.descricao }}</p>
					</td>
					<td>
						<span class="title">Status:</span>
						<pacto-status-parcela
							[situacao]="parcela?.situacao"
							[tooltip]="parcela?.situacao"></pacto-status-parcela>
					</td>
				</tr>
				<tr>
					<td *ngIf="detalhamento.contrato && detalhamento.contrato?.codigo">
						<span class="title">Contrato:</span>
						<p>{{ detalhamento.contrato?.codigo }}</p>
					</td>
					<td *ngIf="detalhamento.vendaAvulsa?.codigo">
						<span class="title">Venda avulsa:</span>
						<p>{{ detalhamento.vendaAvulsa?.codigo }}</p>
					</td>
					<td>
						<span class="title">Vencimento:</span>
						<p>{{ detalhamento.vencimento }}</p>
					</td>
					<td>
						<span class="title">Data de lançamento:</span>
						<p>{{ detalhamento.dataRegistro }}</p>
					</td>
					<td>
						<span class="title">Valor da parcela:</span>
						<p>{{ detalhamento.valor | currency : "BRL" }}</p>
					</td>
					<td>
						<span class="title">Empresa:</span>
						<p>{{ detalhamento.empresa }}</p>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-table">
		<span class="title">Dados do recebimento</span>
		<div class="divider"></div>
		<span
			*ngIf="!detalhamento || (detalhamento && !detalhamento.pagamento?.recibo)"
			class="empty-state">
			Não houve recebimento para esta parcela
		</span>
		<table *ngIf="detalhamento && detalhamento.pagamento?.recibo">
			<tbody>
				<tr>
					<td>
						<span class="title">Código de recebimento:</span>
						<p>{{ detalhamento.pagamento?.movpagamento }}</p>
					</td>
					<td>
						<span class="title">Recibo:</span>
						<p>
							{{ detalhamento.pagamento?.recibo }}
						</p>
					</td>
					<td>
						<span class="title">Pagador:</span>
						<p>{{ detalhamento.pagamento?.pagador | captalize }}</p>
					</td>
					<td>
						<span class="title">Data do pagamento:</span>
						<p>{{ detalhamento.pagamento?.lancamento }}</p>
					</td>
				</tr>
				<tr>
					<td>
						<span class="title">Usuário:</span>
						<p>{{ detalhamento.pagamento?.usuario }}</p>
					</td>
					<td>
						<span class="title">Operação:</span>
						<p>{{ parcela.tipoOperacao }}</p>
					</td>
					<td>
						<span class="title">Forma de pagamento:</span>
						<p>{{ detalhamento.pagamento?.formaPagamento }}</p>
					</td>
					<td>
						<span class="title">Valor do pagamento:</span>
						<p>{{ detalhamento.pagamento?.valor | currency : "BRL" }}</p>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-table">
		<span class="title cobrancas">Histórico de cobrança da parcela</span>
		<div class="divider"></div>
		<pacto-relatorio-cobranca
			[accordion]="accordionDetalharCobranca"
			[emptyStateMessage]="'Não houve cobranças para esta parcela'"
			[showShare]="false"
			[table]="cobrancasRelatorioConfig"></pacto-relatorio-cobranca>
	</div>
</div>

<ng-template #accordionDetalharCobranca let-cobranca="item">
	<section class="detalhamento-da-parcela">
		<h5>Detalhamento da cobrança</h5>
		<hr />
		<pacto-detalhamento-da-cobranca
			[cobranca]="cobranca"
			[tipo]="'transacao'"></pacto-detalhamento-da-cobranca>
	</section>
</ng-template>

<ng-template #celulaStatusCobranca let-cobranca="item">
	<pacto-status-transacao
		[codigo]="cobranca.codigoRetorno"
		[status]="cobranca.statusCodigo"
		[tooltip]="cobranca.descricaoRetorno"></pacto-status-transacao>
</ng-template>

<ng-template #celulaTitular let-item="item">
	{{ item.titular | captalize }}
</ng-template>

<ng-template #celulaCodigoCobranca let-item="item">
	<div *ngIf="item.transacao">
		<span>{{ item.transacao }}</span>
	</div>
	<div *ngIf="item.remessaItem">
		<span>{{ item.remessaItem }}</span>
	</div>
</ng-template>
