@import "dist/ui-kit/assets/import.scss";

.content-wrapper {
	padding: 0px 24px 24px 24px;
}

.card-table {
	background: #fafafa;
	display: flex;
	flex-direction: column;
	padding: 15.81px;
	margin-bottom: 17px;
}

.divider {
	background: #c4c4c4;
	height: 1px;
	width: 99%;
	margin: auto;
	margin-top: 16px;
}

.title {
	font-weight: 900;
	font-size: 14px;
	line-height: 14px;
	color: #51555a;
}

table > tbody > tr > td {
	width: 130px;

	p {
		font-size: 14px;
		line-height: 14px;
		color: #51555a;
	}
}

table > tbody > :nth-child(odd) {
	border-bottom: 1px dashed #dcdddf;
}

@media (min-width: 992px) {
	::ng-deep .modal-lg {
		max-width: 953px;
	}
}

@media (min-width: 1440px) {
	::ng-deep .modal-lg {
		max-width: 1080px;
	}
}

.cobrancas {
	margin-bottom: 16px;
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

.detalhamento-da-parcela {
	padding-top: 1rem;

	h5 {
		color: #51555a;
		font-weight: 700;
		font-size: 1rem;
		line-height: 1rem;
	}

	pacto-detalhamento-da-cobranca::ng-deep {
		.content {
			padding: 0;
		}
	}
}
