<div class="content">
	<div *ngIf="cobranca.identificador" class="identificador">
		<!-- <hr /> -->
		<section>
			<h5>ID:&nbsp;</h5>
			{{ cobranca.identificador }}
		</section>
		<hr />
	</div>

	<pacto-transacao
		*ngIf="tipo === 'transacao'"
		[cobranca]="cobranca"></pacto-transacao>
	<pacto-pix
		*ngIf="tipo === 'pix'"
		[codigo]="cobranca.transacao || cobranca.codigo"></pacto-pix>
	<hr />
	<div class="parcelas">
		<h5>Parcelas da cobrança</h5>

		<pacto-relatorio-cobranca
			[emptyStateMessage]="'Não existem parcelas'"
			[showShare]="false"
			[table]="gridConfigParcelas"></pacto-relatorio-cobranca>

		<ng-template #celulaNomeDoAluno let-item="item">
			<a (click)="verAluno(item)" class="list-item-link">
				{{ item.nome | captalize }}
			</a>
		</ng-template>

		<ng-template #celulaStatus let-parcela="item">
			<pacto-status-parcela
				[retorno]="parcela.codigoRetorno"
				[situacao]="parcela.situacao"
				[tooltip]="parcela.situacao"></pacto-status-parcela>
		</ng-template>
	</div>
</div>
