<div class="campos">
	<div class="campo">
		<label><PERSON>ódigo da cobrança:</label>
		<span>{{ cobranca.codigo }}</span>
	</div>
	<div class="campo">
		<label>Autorização:</label>
		<span *ngIf="cobranca.autorizacao">{{ cobranca.autorizacao }}</span>
		<span *ngIf="!cobranca.autorizacao">Não informado</span>
	</div>
	<div class="campo">
		<label>NSU:</label>
		<span *ngIf="cobranca.nsu">{{ cobranca.nsu }}</span>
		<span *ngIf="!cobranca.nsu">Não informado</span>
	</div>
	<div class="campo">
		<label>Valor:</label>
		<span>{{ cobranca.valor | currency : "BRL" }}</span>
	</div>
	<div class="campo">
		<label>Parcelamento:</label>
		<span>{{ cobranca.parcelamento }}x</span>
	</div>
	<div class="campo">
		<label>Qtd. Parcelas:</label>
		<span>{{ cobranca.qtd_parcelas }}</span>
	</div>
	<div class="campo" *ngIf="cobranca.cartao">
		<label>Cartão:</label>
		<span>{{ cobranca.cartao | captalize }}</span>
	</div>
	<div class="campo">
		<label>Titular:</label>
		<span>{{ cobranca.titular | captalize }}</span>
	</div>
	<div class="campo">
		<label>Bandeira:</label>
		<span *ngIf="cobranca.bandeira">{{ cobranca.bandeira }}</span>
		<span *ngIf="!cobranca.bandeira">Não informado</span>
	</div>
	<div class="campo">
		<label>Recibo:</label>
		<span
			*ngIf="cobranca.situacao_descricao !== 'CANCELADA' && cobranca.recibo">
			{{ cobranca.recibo }}
		</span>
		<span
			*ngIf="
				cobranca.situacao_descricao !== 'CANCELADA' &&
				cobranca.situacao_descricao !== 'ESTORNADA' &&
				!cobranca.recibo
			">
			Não informado
		</span>
		<span
			*ngIf="
				(cobranca.situacao_descricao === 'CANCELADA' ||
					cobranca.situacao_descricao === 'ESTORNADA') &&
				!cobranca.recibo
			">
			Estornado
		</span>
		<span
			*ngIf="
				(cobranca.situacao_descricao === 'CANCELADA' ||
					cobranca.situacao_descricao === 'ESTORNADA') &&
				cobranca.recibo
			">
			{{ cobranca.recibo }}
		</span>
	</div>
	<div class="campo">
		<label>Origem:</label>
		<span *ngIf="cobranca.origem">{{ cobranca.origem | captalize }}</span>
		<span *ngIf="!cobranca.origem">Não informado</span>
	</div>
	<div class="campo">
		<label>Usuário:</label>
		<span *ngIf="cobranca.usuario">{{ cobranca.usuario | captalize }}</span>
		<span *ngIf="!cobranca.usuario">Não informado</span>
	</div>
	<div class="campo" *ngIf="cobranca.situacao_descricao === 'CANCELADA'">
		<label>Dt. cancelamento:</label>
		<span *ngIf="cobranca.data_cancelamento">
			{{ cobranca.data_cancelamento }}
		</span>
		<span *ngIf="!cobranca.data_cancelamento">Não informado</span>
	</div>
	<div class="campo">
		<label>Convênio:</label>
		<span *ngIf="cobranca.codConvenio">
			Cód. {{ cobranca.codConvenio }} | {{ cobranca.convenio }}
		</span>
		<span *ngIf="!cobranca.codConvenio">Não informado</span>
	</div>
</div>
