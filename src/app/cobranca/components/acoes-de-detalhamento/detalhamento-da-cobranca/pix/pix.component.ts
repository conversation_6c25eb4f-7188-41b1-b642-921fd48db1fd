import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { PactoPayApiDetalheService } from "pactopay-api";

@Component({
	selector: "pacto-pix",
	templateUrl: "./pix.component.html",
	styleUrls: ["./pix.component.scss"],
})
export class PixComponent implements OnInit {
	@Input()
	public readonly codigo: number;

	public detalhamento: any;

	constructor(
		private pactoPayApiDetalhe: PactoPayApiDetalheService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.pactoPayApiDetalhe.obterDetalhesDoPix(this.codigo).subscribe((pix) => {
			this.detalhamento = pix;
			this.cd.detectChanges();
		});
	}
}
