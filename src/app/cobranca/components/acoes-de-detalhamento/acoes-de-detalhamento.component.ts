import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { Cobranca } from "../../models/cobranca.model";
import { <PERSON><PERSON><PERSON> } from "../../models/parcela.model";
import { DetalhamentoDaCobrancaComponent } from "./detalhamento-da-cobranca/detalhamento-da-cobranca.component";
import { DetalhamentoDaParcelaComponent } from "./detalhamento-da-parcela/detalhamento-da-parcela.component";
import { ParametrosDaCobrancaComponent } from "./parametros-da-cobranca/parametros-da-cobranca.component";
import { ModalCancelarCobrancaComponent } from "../modal-cancelar-cobranca/modal-cancelar-cobranca.component";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-acoes-de-detalhamento",
	templateUrl: "./acoes-de-detalhamento.component.html",
	styleUrls: ["./acoes-de-detalhamento.component.scss"],
})
export class AcoesDeDetalhamentoComponent implements OnInit {
	@Input()
	public readonly parcela: Parcela;
	@Input()
	public readonly cobranca: Cobranca;
	@Input()
	public readonly tipo: string;
	@Output() acoes: EventEmitter<any> = new EventEmitter();

	constructor(
		private modal: ModalService,
		private readonly snotifyService: SnotifyService,
		private readonly admLegadoService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService
	) {}

	ngOnInit() {}

	public abrirDetalhesDaParcela() {
		const modal = this.modal.open(
			"Detalhamento da parcela",
			DetalhamentoDaParcelaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.parcela = this.parcela;
	}

	public abrirDetalhesDaCobranca() {
		const modal = this.modal.open(
			"Detalhamento da cobrança",
			DetalhamentoDaCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.cobranca = this.cobranca;
		modal.componentInstance.tipo = this.tipo;
	}

	public abrirParametrosDeEnvio() {
		const modal = this.modal.open(
			"Detalhamento - Parâmetros de Envio",
			ParametrosDaCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.codigo =
			this.cobranca.transacao || this.cobranca.codigo;
		modal.componentInstance.tipoDeParametros = "envio";
		modal.componentInstance.tipoDeDetalhamento = this.tipo;
	}

	public abrirParametrosDeRetorno() {
		const modal = this.modal.open(
			"Detalhamento - Parâmetros de Resposta",
			ParametrosDaCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.codigo =
			this.cobranca.transacao || this.cobranca.codigo;
		modal.componentInstance.tipoDeParametros = "resposta";
		modal.componentInstance.tipoDeDetalhamento = this.tipo;
	}

	public abrirCancelarCobranca() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"GestaoTransacoes",
				"4.20 - Permitir acesso ao Gestão de Transações"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					this.abrirCancelarCobrancaModal();
				},
				(error) => {
					const errorMsg =
						error.meta && error.meta.message
							? error.meta.message
							: `Usuário não possui permissão: 4.20 - Permitir acesso ao Gestão de Transações.`;
					this.snotifyService.error(errorMsg);
				}
			);
	}

	public abrirCancelarCobrancaModal() {
		const modal = this.modal.open(
			"Cancelar cobrança",
			ModalCancelarCobrancaComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.transacoes = this.cobranca.codigo;
		modal.componentInstance.acoes.subscribe((res) => {
			this.acoes.emit(res);
		});
	}

	public abrirSincronizar() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"GestaoTransacoes",
				"4.20 - Permitir acesso ao Gestão de Transações"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					this.abrirSincronizarModal();
				},
				(error) => {
					const errorMsg =
						error.meta && error.meta.message
							? error.meta.message
							: `Usuário não possui permissão: 4.20 - Permitir acesso ao Gestão de Transações.`;
					this.snotifyService.error(errorMsg);
				}
			);
	}

	public abrirSincronizarModal() {
		const modal = this.modal.confirm(
			"Sincronizar Transação",
			"Ao sincronizar a transação, o sistema irá verificar a situação da transação na adquirente, " +
				"caso ela tenha sofrido alguma alteração será atualizado no ZillyonWeb. ",
			"Confirmar"
		);
		modal.result.then(
			() => {
				this.admLegadoService
					.sincronizarTransacao(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						Number(this.cobranca.codigo)
					)
					.subscribe(
						(res) => {
							if (res.content) {
								this.acoes.emit("atualizarListaCartao");
								this.snotifyService.success(res.content);
							} else {
								this.snotifyService.error(res.meta.message);
							}
						},
						({ error }) => {
							this.snotifyService.error(error.meta.message);
						}
					);
			},
			() => {}
		);
	}

	public abrirComprovanteCancelamentoTransacao() {
		this.admLegadoService
			.linkComprovanteCancelamentoTransacao(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				Number(this.cobranca.codigo)
			)
			.subscribe(
				(res) => {
					if (res.content && res.content.length > 0) {
						window.open(res.content, "_blank");
					} else {
						this.snotifyService.error(
							"Não foi possível obter o comprovante de cancelamento"
						);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	public enviarComprovanteCancelamentoTransacao() {
		this.admLegadoService
			.emailComprovanteCancelamentoTransacao(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				Number(this.cobranca.codigo)
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.snotifyService.success("E-mail enviado.");
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	public abrirRetentar() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"GestaoTransacoes",
				"4.20 - Permitir acesso ao Gestão de Transações"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					this.abrirRetentarModal();
				},
				(error) => {
					const errorMsg =
						error.meta && error.meta.message
							? error.meta.message
							: `Usuário não possui permissão: 4.20 - Permitir acesso ao Gestão de Transações.`;
					this.snotifyService.error(errorMsg);
				}
			);
	}

	public abrirRetentarModal() {
		const modal = this.modal.confirm(
			"Retentativa Transação",
			"Ao realizar a retentativa da transação o sistema irá realizar uma nova " +
				"tentativa imediata de cobrança das parcelas em aberto da transação selecionada.",
			"Confirmar"
		);
		modal.result.then(
			() => {
				this.admLegadoService
					.retentarTransacao(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						Number(this.cobranca.codigo)
					)
					.subscribe(
						(res) => {
							if (res.content) {
								this.acoes.emit("atualizarListaCartao");
								this.snotifyService.success(res.content);
							} else {
								this.snotifyService.error(res.meta.message);
							}
						},
						({ error }) => {
							this.snotifyService.error(error.meta.message);
						}
					);
			},
			() => {}
		);
	}
}
