import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RelatorioCobrancaModule } from "../relatorio-cobranca/relatorio-cobranca.module";
import { StatusParcelaModule } from "../status-parcela/status-parcela.module";
import { StatusTransacaoModule } from "../status-transacao/status-transacao.module";
import { AcoesDeDetalhamentoComponent } from "./acoes-de-detalhamento.component";
import { DetalhamentoDaCobrancaComponent } from "./detalhamento-da-cobranca/detalhamento-da-cobranca.component";
import { PixComponent } from "./detalhamento-da-cobranca/pix/pix.component";
import { TransacaoComponent } from "./detalhamento-da-cobranca/transacao/transacao.component";
import { DetalhamentoDaParcelaComponent } from "./detalhamento-da-parcela/detalhamento-da-parcela.component";
import { ParametrosDaCobrancaComponent } from "./parametros-da-cobranca/parametros-da-cobranca.component";
import { ModalCancelarCobrancaComponent } from "../modal-cancelar-cobranca/modal-cancelar-cobranca.component";
import { MatTooltipModule } from "@angular/material/tooltip";

@NgModule({
	declarations: [
		AcoesDeDetalhamentoComponent,
		DetalhamentoDaParcelaComponent,
		DetalhamentoDaCobrancaComponent,
		TransacaoComponent,
		PixComponent,
		ParametrosDaCobrancaComponent,
		ModalCancelarCobrancaComponent,
	],
	imports: [
		CommonModule,
		BaseSharedModule,
		RelatorioCobrancaModule,
		StatusParcelaModule,
		StatusTransacaoModule,
		MatTooltipModule,
	],
	exports: [AcoesDeDetalhamentoComponent],
	entryComponents: [
		DetalhamentoDaParcelaComponent,
		DetalhamentoDaCobrancaComponent,
		ParametrosDaCobrancaComponent,
		ModalCancelarCobrancaComponent,
	],
})
export class AcoesDeDetalhamentoModule {}
