<div class="params-content" *ngIf="params.length > 0">
	<p style="width: 50%">Atributo</p>
	<p style="width: 50%">Valor</p>
</div>
<div class="wrapper-table" *ngIf="params.length > 0">
	<div *ngFor="let param of params" class="table-striped lines">
		<div style="width: 50%">{{ param.atribute }}</div>
		<div style="width: 50%" [matTooltip]="param.value">
			{{
				param.value.length > 45
					? (param.value | slice : 0 : 45) + "..."
					: param.value
			}}
		</div>
	</div>
</div>
<ng-container *ngIf="params.length <= 0">
	<p [style.margin-left]="'15px'" class="ds3-select-options-empty">
		Não possui parâmetros de {{ tipoDeParametros }}
	</p>
</ng-container>
