import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { PactoPayApiDetalheService } from "pactopay-api";

@Component({
	selector: "pacto-parametros-da-cobranca",
	templateUrl: "./parametros-da-cobranca.component.html",
	styleUrls: ["./parametros-da-cobranca.component.scss"],
})
export class ParametrosDaCobrancaComponent implements OnInit {
	@Input()
	public readonly codigo: string;
	@Input()
	public readonly tipoDeParametros: "resposta" | "envio";
	@Input()
	public readonly tipoDeDetalhamento: string;

	public params: Array<{ atribute: string; value: string }> = [];

	constructor(
		private pactoPayApiDetalhe: PactoPayApiDetalheService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.pactoPayApiDetalhe
			.obterParametros(this.tipoDeDetalhamento, this.codigo)
			.subscribe((parametros) => {
				this.extrair({
					key: undefined,
					object: JSON.parse(parametros[this.tipoDeParametros]),
				});
				this.cd.detectChanges();
			});
	}

	private extrair({ key, object }: { key?: string; object: any }): void {
		for (const [entryKey, entryValue] of Object.entries(object)) {
			let atribute: string;

			if (key && Array.isArray(object)) {
				atribute = `${key}[${entryKey}]`;
			} else if (key && typeof object === "object") {
				atribute = `${key}.${entryKey}`;
			} else {
				atribute = entryKey;
			}

			if (typeof entryValue === "object" && entryValue !== null) {
				this.extrair({ key: atribute, object: entryValue });
			} else {
				const cleanedValue =
					typeof entryValue === "string"
						? entryValue
								.replace(/\n/g, " ") // Substitui quebras de linha por espaços
								.replace(/\s+/g, " ") // Substitui múltiplos espaços consecutivos por um único espaço
								.trim() // Remove espaços nas extremidades
						: String(entryValue); // Converte valores não string para string

				this.params.push({ atribute, value: cleanedValue });
			}
		}
	}
}
