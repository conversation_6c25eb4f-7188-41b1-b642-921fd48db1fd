import { AfterViewInit, Component, Input, OnInit } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig } from "src/app/cobranca/components/relatorio-cobranca/data-grid.model";
import { DataFiltro } from "ui-kit";
import { ShareCobrancaService } from "../relatorio-cobranca/share-button-cobranca/share-cobranca.service";

@Component({
	selector: "pacto-exportar-relatorio",
	templateUrl: "./exportar-relatorio.component.html",
	styleUrls: ["./exportar-relatorio.component.scss"],
})
export class ExportarRelatorioComponent implements OnInit, AfterViewInit {
	@Input() table: PactoDataGridConfig;
	@Input() tipo = "XLS";
	@Input() icon: string;
	@Input() filterConfig: any;
	@Input() total: number;

	constructor(
		private shareService: ShareCobrancaService,
		private notificationService: SnotifyService,
		private translateService: TranslateService
	) {}

	ngOnInit() {}

	ngAfterViewInit() {}

	get tituloString() {
		try {
			const strings = this.table.endpointUrl.split("/");
			return strings[strings.length - 1];
		} catch (e) {
			return "";
		}
	}

	get colunasExportar() {
		if (this.table.columns) {
			const colunasExportar = new Array();
			this.table.columns.forEach((col) => {
				if (col.mostrarTitulo && col.visible === true) {
					if (col.date) {
						colunasExportar.push({
							campo: col.campo,
							date: col.date,
						});
					} else {
						colunasExportar.push({
							campo: col.campo,
						});
					}
				}
			});
			return colunasExportar;
		}
		return [];
	}

	private get configsFiltros() {
		if (this.filterConfig && this.filterConfig.filters) {
			return this.filterConfig.filters;
		}
		return [];
	}

	public exportar(): void {
		if (this.total && this.total > 5000) {
			this.notificationService.error(
				this.translateService.instant("relatorio.tabela5KRegistros")
			);
			return;
		}

		const dados: any = {
			endpoint: this.table.endpointUrl,
			filtros: this.fetchFiltros,
			titulo: this.tituloString,
			filterConfig: this.configsFiltros,
			colunas: this.colunasExportar,
			destino: "save",
			tipo: this.tipo,
		};

		this.shareService.share(dados).subscribe(
			(retorno) => {
				window.open(retorno.content.url, "_blank");
			},
			(httpError) => {
				if (httpError.error.meta && httpError.error.meta.error) {
					this.notificationService.error(httpError.error.meta.message);
				} else {
					this.notificationService.error(
						this.translateService.instant("relatorio.shareErrorConnection")
					);
				}
			}
		);
	}

	private get fetchFiltros(): DataFiltro {
		const filtros: DataFiltro = {
			sortField: this.table.state.ordenacaoColuna,
			sortDirection: this.table.state.ordenacaoDirecao,
		};

		if (this.table.pagination) {
			filtros.page = this.table.state.paginaNumero;
			filtros.size = this.table.state.paginaTamanho;
		}
		const copy: DataFiltro = {};
		Object.assign(copy, filtros);
		return copy;
	}
}
