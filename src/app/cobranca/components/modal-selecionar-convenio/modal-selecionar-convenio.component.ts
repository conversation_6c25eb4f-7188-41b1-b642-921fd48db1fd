import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-selecionar-convenio",
	templateUrl: "./modal-selecionar-convenio.component.html",
	styleUrls: ["./modal-selecionar-convenio.component.scss"],
})
export class ModalSelecionarConvenioComponent implements OnInit {
	@Input()
	public convenios: Array<any>;
	@Output()
	public readonly convenioSelecionado: EventEmitter<any> =
		new EventEmitter<any>();
	public readonly convenioFC = new FormControl("", Validators.required);
	public error = false;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	public sendConvenioSelecionado() {
		if (this.convenioFC.valid) {
			this.convenioSelecionado.emit(this.convenioFC.value);
			this.openModal.close(true);
		} else {
			this.error = true;
		}
	}

	public cancel() {
		this.openModal.close(true);
	}
}
