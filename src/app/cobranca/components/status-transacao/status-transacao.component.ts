import { Component, Input, OnInit } from "@angular/core";
import { <PERSON><PERSON>anitizer, SafeHtml } from "@angular/platform-browser";

@Component({
	selector: "pacto-status-transacao",
	templateUrl: "./status-transacao.component.html",
	styleUrls: ["./status-transacao.component.scss"],
})
export class StatusTransacaoComponent implements OnInit {
	@Input() status: number | string;
	@Input() tipoCobranca: number | string;
	@Input() codigo: number | string;
	@Input() set tooltip(value: string) {
		this._tooltip = this.sanitizer.bypassSecurityTrustHtml(value);
	}
	_tooltip: SafeHtml;

	constructor(private sanitizer: DomSanitizer) {}

	ngOnInit() {}
}
