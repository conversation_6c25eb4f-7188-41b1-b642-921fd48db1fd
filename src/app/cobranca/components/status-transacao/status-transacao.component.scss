@import "dist/ui-kit/assets/import.scss";

.t-status {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 90px;
	height: 24px;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	color: #fff;
}

.t-status-paid {
	background: #28ab45;
}

.t-status-send {
	background: #026abc;
}

.t-status-error {
	background: #cd1f00;
}

.t-status-pac {
	background: #d6a10f;
}

.t-status-can {
	background: #ff5b92;
}

.t-status-est {
	background: #914391;
}

:host(.ds-3) {
	.t-status {
		font-size: 12px;
		font-weight: 400;
		line-height: 15px;
		width: 107px;
		padding: 5px 16px;
		text-align: center;
		border-radius: 50px;
		height: initial;
		display: inline-flex;
		justify-content: center;

		span {
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}
	}

	.t-status-paid {
		color: $chuchuzinho05;
		background-color: $chuchuzinho01;
	}

	.t-status-pac {
		color: $pequizao05;
		background-color: $pequizao01;
	}

	.t-status-est {
		background: #961ee6a1;
		color: #ffffff;
	}

	// .t-status-in {
	//   color: $cinza05;
	//   background-color: $cinza01;
	// }

	.t-status-can {
		color: $hellboy05;
		background-color: $hellboy01;
	}

	.t-status-error {
		color: $laranjinha05;
		background-color: $laranjinha01;
	}

	.t-status-send {
		color: $azulim05;
		background-color: $azulim01;
	}
}
