<ng-template #tooltipHtml>
	<div [innerHTML]="_tooltip"></div>
</ng-template>

<div [ngbTooltip]="tooltipHtml" [tooltipClass]="'tooltipster'" placement="top">
	<div class="t-status t-status-pac" *ngIf="status === 1">
		<span>{{ codigo }}</span>
	</div>
	<div class="t-status t-status-send" *ngIf="status === 2 || status === 4">
		<span>Enviada</span>
	</div>
	<div class="t-status t-status-error" *ngIf="status === 3">
		<span>{{ codigo }}</span>
	</div>
	<div class="t-status t-status-paid" *ngIf="status === 5">
		<span *ngIf="tipoCobranca !== 1">Aprovada</span>
		<span *ngIf="tipoCobranca === 1">Pago</span>
	</div>
	<div class="t-status t-status-can" *ngIf="status === 7">
		<span>Cancelada</span>
	</div>
	<div class="t-status t-status-can" *ngIf="status === 6">
		<span>Cancelamento Solicitado</span>
	</div>
	<div class="t-status t-status-est" *ngIf="status === 10">
		<span>Estornada</span>
	</div>
	<div class="t-status t-status-send" *ngIf="status === 13">
		<span>Aguard. Pgto.</span>
	</div>
	<div class="t-status t-status-send" *ngIf="status === 15">
		<span>Aguardando Registro</span>
	</div>
	<div class="t-status t-status-pac" *ngIf="status === 16">
		<span>Sem Parcela Vinculada</span>
	</div>
	<div class="t-status t-status-send" *ngIf="status === 9">
		<span>Gerada</span>
	</div>
</div>
