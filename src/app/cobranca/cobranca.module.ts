import { CommonModule } from "@angular/common";
import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { ClickOutsideModule } from "ng-click-outside";
import {
	CURRENT_MODULE,
	HomePageModule,
	PlataformModuleConfig,
} from "pacto-layout";
import { AlunoComponent } from "./aluno/aluno.component";
import { CobrancaRoutingModule } from "./cobranca-routing.module";
import { AcoesDeDetalhamentoModule } from "./components/acoes-de-detalhamento/acoes-de-detalhamento.module";
import { DetalhamentoDeCreditoComponent } from "./components/detalhamento-de-credito/detalhamento-de-credito.component";
import { ExportarRelatorioComponent } from "./components/exportar-relatorio/exportar-relatorio.component";
import { ModalEmailReciboCancelamentoCobrancaGetcardComponent } from "./components/modal-email-recibo-cancelamento-cobranca-getcard/modal-email-recibo-cancelamento-cobranca-getcard.component";
import { ModalEnvioCancelaCobrancaComponent } from "./components/modal-envio-cancela-cobranca/modal-envio-cancela-cobranca.component";
import { ModalEnvioParcelaComponent } from "./components/modal-envio-parcela/modal-envio-parcela.component";
import { ModalErroParcelaComponent } from "./components/modal-erro-parcela/modal-erro-parcela.component";
import { ModalNotificacaoComponent } from "./components/modal-notificacao/modal-notificacao.component";
import { ModalReciboCancelamentoCobrancaGetcardComponent } from "./components/modal-recibo-cancelamento-cobranca-getcard/modal-recibo-cancelamento-cobranca-getcard.component";
import { ModalSelecionarConvenioComponent } from "./components/modal-selecionar-convenio/modal-selecionar-convenio.component";
import { RelatorioCobrancaModule } from "./components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusAlunoModule } from "./components/status-aluno/status-aluno.module";
import { StatusParcelaModule } from "./components/status-parcela/status-parcela.module";
import { StatusTransacaoModule } from "./components/status-transacao/status-transacao.module";
import { HomeComponent } from "./home/<USER>";

@NgModule({
	imports: [
		CobrancaRoutingModule,
		CommonModule,
		BaseSharedModule,
		ClickOutsideModule,
		AcoesDeDetalhamentoModule,
		RelatorioCobrancaModule, // remover quando tirar a tela do aluno
		StatusParcelaModule, // remover quando tirar a tela do aluno
		StatusTransacaoModule, // remover quando tirar a tela do aluno
		StatusAlunoModule, // remover quando tirar a tela do aluno
		HomePageModule,
	],
	entryComponents: [
		ModalEnvioCancelaCobrancaComponent,
		ModalEnvioParcelaComponent,
		ModalSelecionarConvenioComponent,
		ModalErroParcelaComponent,
		DetalhamentoDeCreditoComponent,
		ModalNotificacaoComponent,
		ModalReciboCancelamentoCobrancaGetcardComponent,
		ModalEmailReciboCancelamentoCobrancaGetcardComponent,
	],
	declarations: [
		AlunoComponent,
		ModalEnvioParcelaComponent,
		DetalhamentoDeCreditoComponent,
		ModalSelecionarConvenioComponent,
		ModalErroParcelaComponent,
		ModalEnvioCancelaCobrancaComponent,
		ExportarRelatorioComponent,
		ModalNotificacaoComponent,
		HomeComponent,
		ModalReciboCancelamentoCobrancaGetcardComponent,
		ModalEmailReciboCancelamentoCobrancaGetcardComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
	providers: [
		{
			provide: CURRENT_MODULE,
			useValue: PlataformModuleConfig.TREINO,
			multi: true,
		},
	],
})
export class CobrancaModule {}
