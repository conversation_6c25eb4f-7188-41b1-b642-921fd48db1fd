<pacto-cat-layout-v2>
	<pacto-cat-page-title (click)="comeBack()" style="cursor: pointer">
		<i class="pct pct-arrow-left" style="font-size: 22px"></i>
		<span class="pct-page-title">Resumo do aluno</span>
	</pacto-cat-page-title>
	<pacto-cat-card-plain class="card">
		<div class="row">
			<div class="avatar-wrapper">
				<pacto-cat-person-avatar
					[diameter]="156.25"
					[uri]="avatar"></pacto-cat-person-avatar>
			</div>
			<div class="situacao-aluno-avatar">
				<pacto-status-aluno [aluno]="aluno"></pacto-status-aluno>
			</div>
			<!-- <span class="situacao">
        <pacto-cat-situacao-aluno
          [situacaoAluno]="aluno?.situacaoAluno"
          class="item"
        ></pacto-cat-situacao-aluno>
        <pacto-cat-situacao-contrato
          [situacaoContrato]="aluno?.situacaoContratoZW"
        ></pacto-cat-situacao-contrato>
      </span> -->
			<div class="data-wrapper">
				<div class="card-row space">
					<div>
						<span class="pct-page-title">{{ aluno?.nome | captalize }}</span>
					</div>
					<div class="edit-icon" style="display: none">
						<i class="pct pct-edit icon">Editar perfil</i>
					</div>
				</div>
				<div class="card-row space">
					<div class="card-row matricula">
						<div>
							<span>
								<span class="output-label">Matricula:</span>
								{{ aluno?.matricula }}
							</span>
						</div>
						<div>
							<span>
								<span class="output-label">Último acesso:</span>
								{{ aluno?.ultimoAcesso }}
							</span>
						</div>
					</div>
					<div class="card-row" style="display: none">
						<span>Desbloqueado na catraca:</span>
						<pacto-cat-switch class="switch"></pacto-cat-switch>
					</div>
				</div>
				<div class="card-row dadospessoais">
					<div class="card-column">
						<span><strong class="title-2">Dados pessoais</strong></span>
						<div>
							<span class="output-label">Idade:</span>
							<span>{{ aluno?.idade }} anos</span>
						</div>
					</div>
					<div class="card-column">
						<span><strong class="title-2">Contato</strong></span>
						<div>
							<span>
								<span class="output-label">Telefone:</span>
								{{ (aluno?.telefones)[0]?.numero }}
							</span>
							<span class="column-space-2">
								<span class="output-label">E-mail:</span>
								{{ (aluno?.emails)[0]?.email }}
							</span>
						</div>
					</div>
				</div>
				<div class="card-row divider"></div>
				<div class="card-column">
					<strong class="title-1">Empresa</strong>
					<div class="card-row empresa">
						<span>{{ aluno?.nomeEmpresa }}</span>
					</div>
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain class="card card-wrapper">
		<pacto-relatorio-cobranca
			[alternatingColors]="'first'"
			[emptyStateMessage]="'Não existem contratos para este aluno'"
			[showShare]="false"
			[tableTitle]="'Detalhamento do plano'"
			[table]="contratosRelatorioConfig"></pacto-relatorio-cobranca>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain class="card card-wrapper">
		<pacto-relatorio-cobranca
			[showShare]="false"
			[tableTitle]="'Autorização de cobrança'"
			[table]="autorizacoesRelatorioConfig"></pacto-relatorio-cobranca>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain class="card card-wrapper">
		<pacto-relatorio-cobranca
			[alternatingColors]="'first'"
			[emptyStateMessage]="'Não existem parcelas para este aluno'"
			[showShare]="true"
			[tableTitle]="'Parcelas geradas'"
			[table]="parcelasRelatorioConfig"></pacto-relatorio-cobranca>
		<ng-template #celulaDetalharParcela let-parcela="item">
			<pacto-acoes-de-detalhamento
				[parcela]="parcela"></pacto-acoes-de-detalhamento>
		</ng-template>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain class="card card-wrapper"></pacto-cat-card-plain>

	<div class="accordion-block">
		<pacto-cat-accordion>
			<accordion-header>
				<span class="title-accordion">
					Histórico de cobrança em cartão de crédito
				</span>
			</accordion-header>
			<accordion-body>
				<pacto-relatorio-cobranca
					#relatorioDeCobrancaCredito
					(rowCheck)="
						podeCancelarCredito = rowCheckHandler(
							$event,
							relatorioDeCobrancaCredito
						)
					"
					[alternatingColors]="'first'"
					[customActions]="btnCancelarCredito"
					[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
					[showShare]="false"
					[tableTitle]="'Histórico de cobrança'"
					[table]="cobrancasCartaoRelatorioConfig"></pacto-relatorio-cobranca>
				<ng-template #btnCancelarCredito>
					<pacto-cat-button
						(click)="cancelarCobrancas(relatorioDeCobrancaCredito)"
						*ngIf="podeCancelarCredito"
						[full]="true"
						[icon]="'x-circle'"
						[label]="'Cancelar cobrança'"
						[size]="'LARGE'"
						[type]="'ALERT_PARCELAS'"></pacto-cat-button>
				</ng-template>
			</accordion-body>
		</pacto-cat-accordion>
	</div>
	<div class="accordion-block">
		<pacto-cat-accordion>
			<accordion-header>
				<span class="title-accordion">Histórico de cobrança em PIX</span>
			</accordion-header>
			<accordion-body>
				<pacto-relatorio-cobranca
					#relatorioDeCobrancaPix
					(rowCheck)="
						podeCancelarPix = rowCheckHandler($event, relatorioDeCobrancaPix)
					"
					[alternatingColors]="'first'"
					[customActions]="btnCancelarPix"
					[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
					[showShare]="false"
					[tableTitle]="'Histórico de cobrança'"
					[table]="cobrancasPixRelatorioConfig"></pacto-relatorio-cobranca>
				<ng-template #btnCancelarPix>
					<pacto-cat-button
						(click)="cancelarCobrancas(relatorioDeCobrancaPix)"
						*ngIf="podeCancelarPix"
						[full]="true"
						[icon]="'x-circle'"
						[label]="'Cancelar cobrança'"
						[size]="'LARGE'"
						[type]="'ALERT_PARCELAS'"></pacto-cat-button>
				</ng-template>
				<ng-template #celulaDetalharCobrancaPix let-cobranca="item">
					<pacto-acoes-de-detalhamento
						[cobranca]="cobranca"
						[tipo]="'pix'"></pacto-acoes-de-detalhamento>
				</ng-template>
			</accordion-body>
		</pacto-cat-accordion>
	</div>
	<div class="accordion-block">
		<pacto-cat-accordion>
			<accordion-header>
				<span class="title-accordion">
					Histórico de cobrança em débito em conta
				</span>
			</accordion-header>
			<accordion-body>
				<pacto-relatorio-cobranca
					#relatorioDeCobrancaDebito
					(rowCheck)="
						podeCancelarDebito = rowCheckHandler(
							$event,
							relatorioDeCobrancaDebito
						)
					"
					[alternatingColors]="'first'"
					[customActions]="btnCancelarDebito"
					[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
					[showShare]="false"
					[tableTitle]="'Histórico de cobrança'"
					[table]="cobrancasDebitoRelatorioConfig"></pacto-relatorio-cobranca>
				<ng-template #btnCancelarDebito>
					<pacto-cat-button
						(click)="cancelarCobrancas(relatorioDeCobrancaDebito)"
						*ngIf="podeCancelarDebito"
						[full]="true"
						[icon]="'x-circle'"
						[label]="'Cancelar cobrança'"
						[size]="'LARGE'"
						[type]="'ALERT_PARCELAS'"></pacto-cat-button>
				</ng-template>
			</accordion-body>
		</pacto-cat-accordion>
	</div>
	<div class="accordion-block">
		<pacto-cat-accordion>
			<accordion-header>
				<span class="title-accordion">
					Histórico de cobrança em boleto bancário
				</span>
			</accordion-header>
			<accordion-body>
				<pacto-relatorio-cobranca
					#relatorioDeCobrancaBoleto
					(rowCheck)="
						podeCancelarBoleto = rowCheckHandler(
							$event,
							relatorioDeCobrancaBoleto
						)
					"
					[alternatingColors]="'first'"
					[customActions]="btnCancelarBoleto"
					[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
					[showShare]="false"
					[tableTitle]="'Histórico de cobrança'"
					[table]="cobrancasBoletoRelatorioConfig"></pacto-relatorio-cobranca>
				<ng-template #btnCancelarBoleto>
					<pacto-cat-button
						(click)="cancelarCobrancas(relatorioDeCobrancaBoleto)"
						*ngIf="podeCancelarBoleto"
						[full]="true"
						[icon]="'x-circle'"
						[label]="'Cancelar cobrança'"
						[size]="'LARGE'"
						[type]="'ALERT_PARCELAS'"></pacto-cat-button>
				</ng-template>
			</accordion-body>
		</pacto-cat-accordion>
	</div>
</pacto-cat-layout-v2>

<ng-template #celulaStatus let-cobranca="item">
	<pacto-status-transacao
		[codigo]="cobranca.codigoRetorno"
		[status]="cobranca.statusCodigo"
		[tooltip]="cobranca.descricaoRetorno"></pacto-status-transacao>
</ng-template>

<ng-template #celulaStatusPix let-transacao="item">
	<div *ngIf="transacao.situacao === 'ATIVA'" class="t-status t-status-send">
		<span>{{ transacao.situacaoDescricao }}</span>
	</div>
	<div
		*ngIf="transacao.situacao === 'CONCLUIDA'"
		class="t-status t-status-paid">
		<span>{{ transacao.situacaoDescricao }}</span>
	</div>
	<div
		*ngIf="transacao.situacao === 'REMOVIDA_PELO_USUARIO_RECEBEDOR'"
		class="t-status t-status-error">
		<span>{{ transacao.situacaoDescricao }}</span>
	</div>
	<div
		*ngIf="transacao.situacao === 'REMOVIDA_PELO_PSP'"
		class="t-status t-status-error">
		<span>{{ transacao.situacaoDescricao }}</span>
	</div>
	<div
		*ngIf="transacao.situacao === 'EXPIRADA'"
		class="t-status t-status-error">
		<span>{{ transacao.situacaoDescricao }}</span>
	</div>
	<div *ngIf="transacao.situacao === 'CANCELADA'" class="t-status t-status-can">
		<span>{{ transacao.situacaoDescricao }}</span>
	</div>
</ng-template>

<ng-template #celulaVigencia let-contrato="item">
	<span>{{ contrato.inicio }} à {{ contrato.fim }}</span>
</ng-template>

<ng-template #celulaStatusContrato let-contrato="item">
	<div *ngIf="contrato.situacao === 'AT'" class="t-status t-status-paid">
		<span>ATIVO</span>
	</div>
	<div
		*ngIf="contrato.situacao === 'IN' || contrato.situacao === 'CA'"
		class="t-status t-status-in">
		<span>INATIVO</span>
	</div>
</ng-template>

<ng-template #celulaSituacaoParcela let-parcela="item">
	<pacto-status-parcela
		[retorno]="parcela.codigoRetorno"
		[situacao]="parcela.situacao"
		[tooltip]="parcela.situacao"></pacto-status-parcela>
</ng-template>

<ng-template #celulaValor let-parcela="item">
	<span>{{ parcela.valorTotal | currency : "R$" }}</span>
</ng-template>

<ng-template #celulaDetalharCobranca let-cobranca="item">
	<pacto-acoes-de-detalhamento
		[cobranca]="cobranca"
		[tipo]="'transacao'"></pacto-acoes-de-detalhamento>
</ng-template>

<ng-template #celulaCodigoCobranca let-transacao="item">
	<div *ngIf="transacao.transacao">
		<span>{{ transacao.transacao }}</span>
	</div>
	<div *ngIf="transacao.remessaItem">
		<span>{{ transacao.remessaItem }}</span>
	</div>
</ng-template>

<ng-template #formaDeCobranca let-cobranca="item">
	<span *ngIf="cobranca.tipo === 1">Cartão de crédito</span>
	<span *ngIf="cobranca.tipo === 2">Débito em conta</span>
	<span *ngIf="cobranca.tipo === 3">Boleto bancário</span>
</ng-template>

<ng-template #tipoDeParcela let-cobranca="item">
	<span *ngIf="cobranca.tipoCobrar === 1">Em aberto</span>
	<span *ngIf="cobranca.tipoCobrar === 2">Planos</span>
	<span *ngIf="cobranca.tipoCobrar === 3">Produtos especificados</span>
	<span *ngIf="cobranca.tipoCobrar === 4">Contratos autorrenováveis</span>
</ng-template>
