import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { transformMoney } from "@base-shared/utils/money.util";
import { Aluno, PactoPayApiAlunoService } from "pactopay-api";
import { PactoDataGridConfig } from "ui-kit";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { RelatorioCobrancaComponent } from "../components/relatorio-cobranca/relatorio-cobranca.component";

@Component({
	selector: "pacto-aluno",
	templateUrl: "./aluno.component.html",
	styleUrls: ["./aluno.component.scss"],
})
export class AlunoComponent implements OnInit {
	public pessoa: number;
	public aluno: Aluno;
	public avatar: string;

	public contratosRelatorioConfig: PactoDataGridConfig;
	public autorizacoesRelatorioConfig: PactoDataGridConfig;
	public parcelasRelatorioConfig: PactoDataGridConfig;
	public cobrancasPixRelatorioConfig: PactoDataGridConfig;
	public cobrancasBoletoRelatorioConfig: PactoDataGridConfig;
	public cobrancasDebitoRelatorioConfig: PactoDataGridConfig;
	public cobrancasCartaoRelatorioConfig: PactoDataGridConfig;

	public podeCancelarCredito = false;
	public podeCancelarPix = false;
	public podeCancelarDebito = false;
	public podeCancelarBoleto = false;

	@ViewChild("celulaVigencia", { static: true }) celulaVigencia;
	@ViewChild("celulaStatusContrato", { static: true }) celulaStatusContrato;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaStatusPix", { static: true }) celulaStatusPix;
	@ViewChild("celulaCodigoCobranca", { static: true }) celulaCodigoCobranca;
	@ViewChild("celulaSituacaoParcela", { static: true }) celulaSituacaoParcela;
	@ViewChild("celulaValor", { static: true }) celulaValor;
	@ViewChild("celulaDetalharParcela", { static: true }) celulaDetalharParcela;
	@ViewChild("celulaDetalharCobranca", { static: true }) celulaDetalharCobranca;
	@ViewChild("celulaDetalharCobrancaPix", { static: true })
	celulaDetalharCobrancaPix;
	@ViewChild("formaDeCobranca", { static: true })
	formaDeCobranca: TemplateRef<any>;
	@ViewChild("tipoDeParcela", { static: true }) tipoDeParcela: TemplateRef<any>;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly rest: RestService,
		private readonly cd: ChangeDetectorRef,
		private readonly sessionService: SessionService,
		private readonly router: Router,
		private readonly pactoPayApiAluno: PactoPayApiAlunoService,
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService
	) {}

	ngOnInit() {
		this.pessoa = this.route.snapshot.params["pessoa"];

		this.pactoPayApiAluno.aluno(this.pessoa).subscribe((aluno) => {
			this.aluno = aluno;
			this.cd.detectChanges();
		});

		this.zwPactoPayApiDash.obterFoto(this.pessoa).subscribe((uri) => {
			this.avatar = uri;
			this.cd.detectChanges();
		});

		this.initContratosRelatorioConfig();
		this.initAutorizacoesRelatorioConfig();
		this.initHistPixRelatorioConfig();
		this.initHistBoletoRelatorioConfig();
		this.initHistDebitoRelatorioConfig();
		this.initHistCartaoRelatorioConfig();
		this.initParcelasRelatorioConfig();
		this.scrollToTop();
	}

	scrollToTop() {
		const contentElements = Array.from(
			document.getElementsByClassName("content-wrapper")
		);
		contentElements.forEach((element) => {
			element.scrollTo(0, 0);
		});
	}

	transformMoneyValue(value: number) {
		return value
			? value.toLocaleString("pt-BR", {
					minimumFractionDigits: 2,
					style: "currency",
					currency: "BRL",
			  })
			: value;
	}

	initParcelasRelatorioConfig() {
		this.parcelasRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?op=parcelascliente&key=${this.sessionService.chave}&p=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			endpointParamsType: "query",
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "codigo",
					titulo: "Cód. da parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nrTentativas",
					titulo: "Nº de tentativas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacaoApresentar",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					celula: this.celulaSituacaoParcela,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "valorTotal",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					celula: this.celulaValor,
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharParcela,
				},
			],
		});
	}

	initContratosRelatorioConfig() {
		this.contratosRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay(
				`aluno/contratos?pessoa=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			columns: [
				{
					nome: "plano",
					titulo: "Plano",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "codigo",
					titulo: "Contrato",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "vigencia",
					titulo: "Vigência",
					celula: this.celulaVigencia,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: transformMoney,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					celula: this.celulaStatusContrato,
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	initAutorizacoesRelatorioConfig() {
		this.autorizacoesRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay(
				`aluno/autorizacoes?pessoa=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			columns: [
				{
					nome: "tipo",
					titulo: "Forma de cobrança",
					celula: this.formaDeCobranca,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "tipoCobrar",
					titulo: "Tipo de parcela",
					celula: this.tipoDeParcela,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	initHistCartaoRelatorioConfig() {
		this.cobrancasCartaoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?op=cobrancas&tipo=cartao&key=${this.sessionService.chave}&p=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			// valueRowCheck: 'transacao',
			rowClick: false,
			totalRow: false,
			ghostAmount: 10,
			ghostLoad: true,
			actions: [],
			columns: [
				{
					nome: "transacao",
					titulo: "Cód.: Cobrança",
					celula: this.celulaCodigoCobranca,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "titular",
					titulo: "Titular do cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					celula: this.celulaStatus,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data e hora",
					visible: true,
					ordenavel: false,
					valueTransform: (value: string) => {
						return value ? value.replace("-", "ás") : value;
					},
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: this.transformMoneyValue,
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharCobranca,
				},
			],
		});
	}

	initHistPixRelatorioConfig() {
		this.cobrancasPixRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?op=cobrancas&tipo=pix&key=${this.sessionService.chave}&p=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			// valueRowCheck: 'transacao',
			rowClick: false,
			totalRow: false,
			ghostAmount: 10,
			ghostLoad: true,
			actions: [],
			columns: [
				{
					nome: "identificador",
					titulo: "Cód. TxId",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtd_Parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Status",
					celula: this.celulaStatusPix,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Data e hora",
					visible: true,
					ordenavel: false,
					valueTransform: (value: string) => {
						return value ? value.replace("-", "ás") : value;
					},
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: this.transformMoneyValue,
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharCobrancaPix,
				},
			],
		});
	}

	initHistBoletoRelatorioConfig() {
		this.cobrancasBoletoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?op=cobrancas&tipo=boleto&key=${this.sessionService.chave}&p=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			// valueRowCheck: 'transacao',
			rowClick: false,
			totalRow: false,
			ghostAmount: 10,
			ghostLoad: true,
			actions: [],
			columns: [
				{
					nome: "parcela",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricaoRetorno",
					titulo: "Status",
					celula: this.celulaStatus,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharCobranca,
				},
			],
		});
	}

	initHistDebitoRelatorioConfig() {
		this.cobrancasDebitoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`pactopay/dash?op=cobrancas&tipo=debito&key=${this.sessionService.chave}&p=${this.pessoa}`
			),
			quickSearch: false,
			exportButton: true,
			pagination: true,
			showFilters: false,
			// valueRowCheck: 'transacao',
			rowClick: false,
			totalRow: false,
			ghostAmount: 10,
			ghostLoad: true,
			actions: [],
			columns: [
				{
					nome: "transacao",
					titulo: "Cód.: Cobrança",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "titular",
					titulo: "Titular do cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "N° Parcelas",
					titulo: "N° Parcelas",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "status",
					titulo: "Status",
					celula: this.celulaStatus,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharCobranca,
				},
			],
		});
	}

	rowCheckHandler(itemCheck, relatorio): boolean {
		return itemCheck.selectedAll || relatorio.selectedItems.length > 0;
	}

	cancelarCobrancas(relatorio: RelatorioCobrancaComponent) {
		const transacaoIds = relatorio.selectedItems.map((item) => item.transacao);
		// this.cartaoCreditoService.sendCancelarCobrancas(transacaoIds).subscribe((result) => {
		//   if (result.error) {
		//     this.modal.open('', ModalErroParcelaComponent);
		//   } else {
		//     const modal = this.modal.open('', ModalEnvioCancelaCobrancaComponent);
		//     modal.componentInstance.resultado = JSON.parse(result.content);
		//   }
		//   this.openedModal.close(true);
		// });
	}

	comeBack() {
		if (
			localStorage
				.getItem("pactopay:aluno:cameFrom")
				.includes("pactopay/pt/pactopay")
		) {
			this.router.navigate([
				localStorage
					.getItem("pactopay:aluno:cameFrom")
					.replace("pactopay/pt/", ""),
			]);
		} else {
			this.router.navigate([
				localStorage.getItem("pactopay:aluno:cameFrom").replace("pt/", ""),
			]);
		}
	}
}
