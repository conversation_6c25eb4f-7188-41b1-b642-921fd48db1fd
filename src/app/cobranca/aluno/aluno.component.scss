@import "~dist/ui-kit/assets/import.scss";

.card {
	margin-top: 32px;
}

.card-row {
	display: flex;
	align-items: center;
}

.card-row.space {
	justify-content: space-between;
}

.card-row.matricula {
	padding-top: 16px;
}

.card-row.matricula > div {
	padding-right: 24px;
}

.avatar-wrapper {
	padding-left: 14px;
	flex-grow: 0.1;
	position: absolute;
}

.data-wrapper {
	flex-grow: 0.9;
	margin-left: 200px;
}

.card-row.dadospessoais {
	padding-top: 37px;
}

.card-row.dadospessoais > div:first-child {
	padding-right: 70px;
}

.column-space-1 {
	padding-right: 70px;
}

.column-space-2 {
	padding-left: 24px;
}

.output-label {
	font-weight: bold;
	margin-right: 3px;
	size: 14px;
}

.edit-icon {
	align-self: baseline;
}

.icon {
	color: $azulim07;
	font-size: 14px;
	line-height: 19px;
}

.switch {
	margin-left: 10px;
}

strong {
	color: #51555a;
}

strong.label {
	font-weight: bold;
	font-size: 14px;
	line-height: 19px;
}

strong.title-1 {
	font-weight: 600;
	font-size: 16px;
	line-height: 22px;
}

strong.title-2 {
	font-weight: 600;
	font-size: 18px;
	line-height: 20px;
}

.divider {
	width: 100%;
	height: 0px;
	border: 1px solid #d1d1d1;
	margin-top: 24px;
	margin-bottom: 24px;
}

.card-row.empresa {
	margin-top: 12px;
}

.card-tile {
	font-weight: bold;
	font-size: 16px;
	line-height: 16px;
	color: #51555a;
}

.card-wrapper {
	padding: 0px;
}

.t-status {
	color: #ffffff;
	text-align: center;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100px;
	height: 24px;
	padding: 15px;
}

.t-status-paid {
	background: #28ab45;
}

.t-status-send {
	background: #026abc;
}

.t-status-error {
	background: #cd1f00;
}

.t-status-pac {
	background: #d6a10f;
}

.t-status-can {
	background: #ff5b92;
}

.t-status-est {
	background: #914391;
}

.t-status-in {
	background: #b4b7bb;
}

.situacao {
	display: flex;

	.item {
		display: block;
		margin-right: 10px;
	}
}

.situacao-aluno-avatar {
	position: absolute;
	margin-left: 100px;
}

.list-title {
	font-weight: bold;
	font-size: 16px;
	line-height: 16px;
	color: #51555a;
	margin-bottom: 24px;
}

.table-striped-invert tbody > tr:nth-child(even) > td,
tbody > tr:nth-child(even) > th {
	background: #fafafa;
}

.table-striped-invert tbody > tr:nth-child(odd) > td,
tr:nth-child(odd) > th {
	background: #ffffff;
}

.table th,
.table td {
	border-top: none;
}

.title-accordion {
	color: #51555a;
	font-weight: bold;
}

.accordion-block {
	margin-bottom: 32px;
}
