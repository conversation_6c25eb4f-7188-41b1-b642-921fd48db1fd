import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ConfiguracaoDeEmailComponent } from "./configuracao-de-email/configuracao-de-email.component";
import { ConfiguracaoDeFasesComponent } from "./configuracao-de-fases/configuracao-de-fases.component";
import { DashboardComponent } from "./dashboard/dashboard.component";

const routes: Routes = [
	{ path: "dashboard", component: DashboardComponent },
	{ path: "configuracao-de-email", component: ConfiguracaoDeEmailComponent },
	{ path: "configuracao-de-fases", component: ConfiguracaoDeFasesComponent },
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class ReguaRoutingModule {}
