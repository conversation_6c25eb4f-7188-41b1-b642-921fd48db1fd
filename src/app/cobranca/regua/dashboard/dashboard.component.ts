import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ColumnChartSet, PactoModalSize, PieChartSet } from "ui-kit";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { FormControl, FormGroup } from "@angular/forms";
import { MsPactoPayApiCobrancaService } from "ms-pactopay-api";
import { ModalService } from "@base-core/modal/modal.service";
import { ModalListaPessoasReguaComponent } from "./modal-lista-pessoas-regua/modal-lista-pessoas-regua.component";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { formatDate } from "@angular/common";
import { locale } from "moment/moment";
import { PactoColor } from "ui-kit";

declare var moment;

@Component({
	selector: "pacto-dashboard",
	templateUrl: "./dashboard.component.html",
	styleUrls: ["./dashboard.component.scss"],
})
export class DashboardComponent implements OnInit {
	abas: Array<{
		id?: string;
		nome?: string;
		route?: string;
		class?: string;
		clSelecionado?: string;
		apresentar?: boolean;
	}> = new Array<{
		id?: string;
		nome?: string;
		route?: string;
		class?: string;
		clSelecionado?: string;
		apresentar?: boolean;
	}>();
	abasDetalhe: Array<{
		id?: string;
		meioEnvio?: number;
		nome?: string;
		apresentar?: boolean;
	}> = new Array<{
		id?: string;
		meioEnvio?: number;
		nome?: string;
		apresentar?: boolean;
	}>();
	abaSelecionada: string;
	detalheSelecionado: string;
	formGroup = new FormGroup({
		empresas: new FormControl(),
		dtInicio: new FormControl(),
		dtFinal: new FormControl(),
	});
	empresaOptions = [];
	dataAtualizacao: Date;

	eficienciaTotal: any;
	eficienciaFormasPagamento: any;
	eficienciaComunicacao: any;
	eficienciaFormasPagamentoLabels: any[];
	eficienciaFormasPagamentoDados: ColumnChartSet;
	eficienciaComunicacaoLabels: any[];
	eficienciaComunicacaoSeries: ColumnChartSet;
	eficienciaComunicacaoColors: PactoColor[] = [
		PactoColor.AZULIM_PRI,
		PactoColor.LARANJINHA,
		PactoColor.CHUCHUZINHO,
	];

	retentativaTotal: any;

	retentativaTentativa: any;
	retentativaTentativaGrafico: any = [];

	retentativaHistoricoDia: any;
	retentativaHistoricoDiaLabels: any = [];
	retentativaHistoricoDiaSeries: ColumnChartSet[];
	retentativaHistoricoDiaColors: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.CHUCHUZINHO,
	];

	retentativaDetalheLabels: any[];
	retentativaDetalheSeries: ColumnChartSet;

	comunicacaoTotal: any;
	comunicacaoHistoricoDia: any;
	comunicacaoHistoricoDiaLabels: any = [];
	comunicacaoHistoricoDiaSeries: ColumnChartSet[];
	comunicacaoHistoricoDiaColors: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.CHUCHUZINHO,
	];

	comunicacaoTentativa: any;

	comunicacaoCanais: any;
	comunicacaoCanaisLabels: any[];
	comunicacaoCanaisSeries: ColumnChartSet;
	comunicacaoCanaisColors: PactoColor[] = [
		PactoColor.AZULIM_PRI,
		PactoColor.LARANJINHA,
		PactoColor.CHUCHUZINHO,
	];

	comunicacaoFormasPagamento: any;
	comunicacaoFormasPagamentoLabels: any[];
	comunicacaoFormasPagamentoDados: ColumnChartSet;
	comunicacaoDetalhe: any;
	comunicacaoDetalheSeries: PieChartSet[] = [];
	comunicacaoDetalheColors: PactoColor[] = [
		PactoColor.AZULIM_PRI,
		PactoColor.LARANJINHA,
		PactoColor.CHUCHUZINHO,
	];

	antecipacaoTotal: any;
	antecipacaoFormasPagamento: any;
	antecipacaoFormasPagamentoLabels: any[];
	antecipacaoFormasPagamentoDados: ColumnChartSet;

	constructor(
		private readonly route: ActivatedRoute,
		private pactoModal: ModalService,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly msPactoPayApiCobrancaService: MsPactoPayApiCobrancaService,
		private readonly restApi: RestService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.empresaOptions = this.sessionService.empresas
			.filter(
				(e, index, self) =>
					self.findIndex((opt) => opt.codigo === e.codigo) === index
			)
			.map((e) => ({
				id: e.codigo,
				label: e.nome,
			}));
		this.sessionService.empresas.forEach((e) => {
			if (e.codigo === parseInt(this.sessionService.empresaId)) {
				this.formGroup.get("empresas").setValue([
					{
						id: e.codigo,
						label: e.nome,
					},
				]);
			}
		});
		const date = new Date();
		const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
		this.formGroup.get("dtInicio").setValue(firstDay);
		this.formGroup.get("dtFinal").setValue(new Date());
		this.carregarAbas();
		this.carregarAbasDetalhe();
		this.fecharMenu();
	}

	fecharMenu() {
		try {
			const menu = document.getElementById("sidebar-menu-toggle");
			if (menu.classList.contains("opened")) {
				menu.click();
				menu.classList.remove("opened");
			}
		} catch (e) {
			console.log(e);
		}
	}

	clickAba(aba) {
		this.abaSelecionada = aba;
		this.atualizar();
	}

	abaEficiencia() {
		return this.abaSelecionada === "pcc-tab-eficiencia";
	}

	abaRetentativa() {
		return this.abaSelecionada === "pcc-tab-retentativa";
	}

	abaComunicacao() {
		return this.abaSelecionada === "pcc-tab-comunicacao";
	}

	abaAntecipacao() {
		return this.abaSelecionada === "pcc-tab-antecipacao";
	}

	filtros() {
		const empresaSel = [];
		this.formGroup.get("empresas").value.forEach((e) => {
			empresaSel.push(e.id);
		});
		return {
			empresas: empresaSel,
			dataInicio: moment(this.formGroup.get("dtInicio").value).format(
				"YYYYMMDD"
			),
			dataFinal: moment(this.formGroup.get("dtFinal").value).format("YYYYMMDD"),
		};
	}

	atualizar() {
		if (this.abaEficiencia()) {
			this.carregarEficiencia();
		} else if (this.abaRetentativa()) {
			this.carregarRetentativa();
		} else if (this.abaComunicacao()) {
			this.carregarComunicacao();
		} else if (this.abaAntecipacao()) {
			this.carregarAntecipacao();
		}
	}

	carregarEficiencia() {
		this.limparDadosEficiencia();

		const body = {};

		this.msPactoPayApiCobrancaService
			.eficienciaTotalizador(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.eficienciaTotal = res.content;
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.eficienciaFormaRecebimento(
				this.sessionService.chave,
				this.filtros(),
				body
			)
			.subscribe(
				(res) => {
					this.eficienciaFormasPagamento = res.content;
					this.processarGraficoEficienciaFormasPagamento();
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.eficienciaComunicacao(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.eficienciaComunicacao = res.content;
					this.processarGraficoEficienciaComunicacao();
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
	}

	carregarRetentativa() {
		this.limparDadosRetentativa();

		const body = {};

		this.msPactoPayApiCobrancaService
			.retentativaTotalizador(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.retentativaTotal = res.content;
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.retentativaHistorico(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.retentativaHistoricoDia = res.content;

					const dataRecebido = [];
					const dataNaoRecebido = [];
					this.retentativaHistoricoDia.forEach((result) => {
						const data = new Date(result.data);
						const dia = formatDate(data, "dd/MM/yyyy", locale());
						this.retentativaHistoricoDiaLabels.push(dia);
						dataRecebido.push(result.valorRecebido);
						dataNaoRecebido.push(result.valorNaoRecebido);
					});

					this.retentativaHistoricoDiaSeries = [
						{ name: "Inadimplência", data: dataNaoRecebido },
						{ name: "Recebido", data: dataRecebido },
					];

					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.retentativaRetentativas(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.retentativaTentativaGrafico = [];
					this.retentativaTentativa = res.content;
					this.retentativaTentativa.forEach((tentativa) => {
						const dados1 = [];
						const retentativaDetalheLabels = [];
						let retentativaDetalheSeries = {};

						tentativa.formasPagamento.forEach((forma) => {
							dados1.push(forma.valorRecebido);
							retentativaDetalheLabels.push(forma.formaPagamentoDescricao);
						});
						retentativaDetalheSeries = {
							name: "",
							data: dados1,
						};

						this.retentativaTentativaGrafico.push({
							retentativaDetalheLabels,
							retentativaDetalheSeries,
						});
					});

					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
	}

	carregarComunicacao() {
		this.limparDadosComunicacao();

		const body = {};

		this.msPactoPayApiCobrancaService
			.comunicacaoTotalizador(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.comunicacaoTotal = res.content;
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.comunicacaoHistoricoDia(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.comunicacaoHistoricoDia = res.content;

					const dataRecebido = [];
					const dataNaoRecebido = [];
					this.comunicacaoHistoricoDia.forEach((result) => {
						const dia = moment(result.data).format("DD/MM/YYYY");
						this.comunicacaoHistoricoDiaLabels.push(dia);
						dataRecebido.push(result.valorRecebido);
						dataNaoRecebido.push(result.valorNaoRecebido);
					});

					this.comunicacaoHistoricoDiaSeries = [
						{ name: "Inadimplência", data: dataNaoRecebido },
						{ name: "Recebido", data: dataRecebido },
					];

					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.comunicacaoCanais(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.comunicacaoCanais = res.content;
					this.processarGraficoComunicacaoCanais();
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.comunicacaoFormasPagamento(
				this.sessionService.chave,
				this.filtros(),
				body
			)
			.subscribe(
				(res) => {
					this.comunicacaoFormasPagamento = res.content;
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.comunicacaoTentativa(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.comunicacaoTentativa = res.content;
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.clickDetalhe(this.abasDetalhe[0]);
	}

	carregarAntecipacao() {
		this.limparDadosAntecipacao();

		const body = {};

		this.msPactoPayApiCobrancaService
			.antecipacaoTotalizador(this.sessionService.chave, this.filtros(), body)
			.subscribe(
				(res) => {
					this.antecipacaoTotal = res.content;
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);

		this.msPactoPayApiCobrancaService
			.antecipacaoFormasPagamento(
				this.sessionService.chave,
				this.filtros(),
				body
			)
			.subscribe(
				(res) => {
					this.antecipacaoFormasPagamento = res.content;
					this.processarGraficoAntecipacaoFormasPagamento();
					this.dataAtualizacao = new Date();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
	}

	carregarAbas() {
		this.abas.push(
			{
				id: "pcc-tab-eficiencia",
				nome: "Eficiência",
				route: "eficiencia",
				class: "tab eficiencia",
				clSelecionado: "eficiencia",
				apresentar: true,
			},
			{
				id: "pcc-tab-retentativa",
				nome: "Retentativas",
				route: "retentativa",
				class: "tab retentativa",
				clSelecionado: "retentativa",
				apresentar: true,
			},
			{
				id: "pcc-tab-comunicacao",
				nome: "Comunicação",
				route: "comunicacao",
				class: "tab comunicacao",
				clSelecionado: "comunicacao",
				apresentar: true,
			},
			{
				id: "pcc-tab-antecipacao",
				nome: "Antecipação",
				route: "antecipacao",
				class: "tab antecipacao",
				clSelecionado: "antecipacao",
				apresentar: true,
			}
		);

		this.clickAba(this.abas[0].id);
	}

	carregarAbasDetalhe() {
		this.abasDetalhe.push(
			{
				id: "tab-detalhe-geral",
				meioEnvio: 0,
				nome: "Geral",
				apresentar: true,
			},
			{
				id: "tab-detalhe-email",
				meioEnvio: 1,
				nome: "Emails",
				apresentar: true,
			},
			{
				id: "tab-detalhe-sms",
				meioEnvio: 2,
				nome: "SMS",
				apresentar: true,
			},
			{
				id: "tab-detalhe-whatsapp",
				meioEnvio: 7,
				nome: "WhatsApp",
				apresentar: true,
			},
			{
				id: "tab-detalhe-app",
				meioEnvio: 3,
				nome: "App",
				apresentar: true,
			}
		);
	}

	processarGraficoEficienciaFormasPagamento() {
		this.eficienciaFormasPagamentoLabels = [];
		const dados = [];
		const captalizePipe = new CaptalizePipe();
		this.eficienciaFormasPagamento.forEach((f) => {
			dados.push(f.valorRecebido);
			this.eficienciaFormasPagamentoLabels.push(
				captalizePipe.transform(f.formaPagamentoDescricao)
			);
		});
		this.eficienciaFormasPagamentoDados = {
			name: "",
			data: dados,
		};
	}

	processarGraficoEficienciaComunicacao() {
		this.eficienciaComunicacaoLabels = [];
		const dados = [];
		this.eficienciaComunicacao.forEach((f) => {
			dados.push(f.valorRecebido);
			this.eficienciaComunicacaoLabels.push(f.origem);
		});
		this.eficienciaComunicacaoSeries = {
			name: "",
			data: dados,
		};
	}

	processarGraficoAntecipacaoFormasPagamento() {
		this.antecipacaoFormasPagamentoLabels = [];
		const dados = [];
		const captalizePipe = new CaptalizePipe();
		this.antecipacaoFormasPagamento.forEach((f) => {
			dados.push(f.valorRecebido);
			this.antecipacaoFormasPagamentoLabels.push(
				captalizePipe.transform(f.formaPagamentoDescricao)
			);
		});
		this.antecipacaoFormasPagamentoDados = {
			name: "",
			data: dados,
		};
	}

	processarGraficoComunicacaoCanais() {
		this.comunicacaoCanaisLabels = [];
		const dados = [];
		this.comunicacaoCanais.forEach((f) => {
			dados.push(f.valorRecebido);
			this.comunicacaoCanaisLabels.push(f.origem);
		});
		this.comunicacaoCanaisSeries = {
			name: "",
			data: dados,
		};
	}

	limparDadosEficiencia() {
		this.eficienciaTotal = {
			qtdTotal: 0,
			valorTotal: 0.0,
			valorRecebido: 0.0,
			valorNaoRecebido: 0.0,
			eficiencia: 0.0,
		};
	}

	limparDadosRetentativa() {
		this.retentativaHistoricoDia = [];
		this.retentativaHistoricoDiaLabels = [];
		this.retentativaHistoricoDiaSeries = [];
		this.retentativaDetalheLabels = [];
		this.retentativaTotal = {
			qtdTotal: 0,
			valorTotal: 0.0,
			valorRecebido: 0.0,
			valorNaoRecebido: 0.0,
			eficiencia: 0.0,
		};
	}

	limparDadosComunicacao() {
		this.retentativaHistoricoDia = [];
		this.comunicacaoDetalhe = [];
		this.comunicacaoHistoricoDiaLabels = [];
		this.comunicacaoHistoricoDiaSeries = [];
		this.comunicacaoTotal = {
			qtdTotal: 0,
			valorTotal: 0.0,
			valorRecebido: 0.0,
			valorNaoRecebido: 0.0,
			eficiencia: 0.0,
		};
	}

	processarGraficoDetalheRetentativa(dados: any) {
		this.retentativaDetalheLabels = [];
		const dados1 = [];
		dados.formasPagamento.forEach((f) => {
			dados1.push(f.valorRecebido);
			this.retentativaDetalheLabels.push(f.formaPagamentoDescricao);
		});
		this.retentativaDetalheSeries = {
			name: "",
			data: dados1,
		};
	}

	limparDadosAntecipacao() {
		this.antecipacaoFormasPagamento = [];
		this.antecipacaoTotal = {
			qtdTotal: 0,
			valorTotal: 0.0,
			valorRecebido: 0.0,
			valorNaoRecebido: 0.0,
			valorDesconto: 0.0,
			eficiencia: 0.0,
		};
	}

	tooltipFormatterValor() {
		return (val) => {
			const valorFormatado = Number(val).toLocaleString("pt-BR", {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2,
			});
			return "R$ " + valorFormatado;
		};
	}

	public transformMoneyValue(valor: string) {
		const valorFormatado = Number(valor).toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
		return "R$ " + valorFormatado;
	}

	openModalListaPessoas(tipo: number, pago: boolean) {
		const modalRef = this.pactoModal.open(
			pago ? "Recebido" : "Não recebido",
			ModalListaPessoasReguaComponent,
			PactoModalSize.LARGE,
			"modal-pessoa-regua"
		);
		modalRef.componentInstance.tipo = tipo;
		modalRef.componentInstance.aba = this.abaSelecionada;
		modalRef.componentInstance.filtros = this.filtros();
	}

	tooltipFormatterMoney() {
		return (val) => {
			const valorFormatado = Number(val).toLocaleString("pt-BR", {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2,
			});
			return "R$ " + valorFormatado;
		};
	}

	clickDetalhe(abaDetalhe) {
		this.detalheSelecionado = abaDetalhe.id;
		this.carregarDetalhe(abaDetalhe.meioEnvio);
	}

	carregarDetalhe(meio) {
		this.comunicacaoDetalheSeries = [];

		const body = {};
		const filtros = {
			meioEnvio: meio,
			...this.filtros(),
		};
		this.msPactoPayApiCobrancaService
			.comunicacaoDetalhe(this.sessionService.chave, filtros, body)
			.subscribe(
				(res) => {
					this.comunicacaoDetalhe = res.content;
					this.comunicacaoDetalheSeries.push(
						{ name: "Lidos", data: this.comunicacaoDetalhe.qtdLido },
						{ name: "Não lidos", data: this.comunicacaoDetalhe.qtdNaoLido }
					);
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
	}

	composicaoLabelFn() {
		return (value) => {
			return `${value}`;
		};
	}

	composicaoLabelBorderFn() {
		return (value) => {
			let arredondado;
			arredondado = Math.round(value * 100) / 100;
			return `${arredondado}`;
		};
	}
}
