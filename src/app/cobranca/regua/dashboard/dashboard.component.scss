@import "src/assets/scss/pacto/plataforma-import.scss";

.nav-aux {
	display: flex;
	grid-template-columns: 1fr 1fr;
	//align-items: center;
	flex-basis: 100%;
	//margin: 20px 0px 20px 0px;
	margin: 30px;
	font-size: 32px;
	font-weight: 400;
	line-height: 44px;

	:first-child {
		justify-self: start; /* Alinhamento esquerdo */
	}

	:last-child {
		justify-self: end; /* Alinhamento direito */
	}

	a {
		color: $pretoPri;
	}

	i {
		margin-right: 12px;
	}

	.acesso {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $preto02;
		margin-left: 45px;
	}

	#voltar-alunos {
		cursor: pointer;
	}
}

.tab-content {
	margin: 30px;
}

pacto-cat-form-input,
pacto-cat-form-input-number,
pacto-cat-form-select-filter,
pacto-cat-form-select,
pacto-cat-form-datepicker {
	margin: 0px;
}

.divSuperior {
	::ng-deep pacto-cat-form-datepicker {
		margin: 0;

		.nome,
		.pct-error-msg {
			height: 0;
			min-height: 0;
			margin: 0;
		}
	}
}

.divFiltros {
	width: 50%;
	padding-left: 25px;
}

.divTituloGrafico {
	padding: 20px 0 15px 0;
}

.tituloGrafico {
	font-size: 14px;
	font-weight: bold;
	color: #55585e;
}

.divTotalEficiencia {
	border: 1.5px solid #c7c9cc;
	border-radius: 5px;
	width: 100%;
	padding: 20px;
}

.divIconAtualizar {
	margin-left: 20px;
	border: 1px solid #1e60fa;
	border-radius: 5px;
	color: #1e60fa;
	cursor: pointer;
	height: 40px;
	width: 80px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.divIconAtualizar:hover {
	background: #bde5fe;
}

.divItemEficiencia {
	display: grid;
	align-items: center;
	justify-content: center;
	text-align: center;
	padding: 10px;
	width: 100%;

	.tituloItemEficiencia {
		font-weight: bold;
		font-size: 22px;
		color: #55585e;
	}

	.itemClick {
		color: #1e60fa;
		cursor: pointer;
	}

	.infoItemEficiencia {
		font-size: 12px;
		color: #80858c;
	}
}

.atualizadoEm {
	font-size: 12px;
	color: #55585e;
	padding: 10px 0 10px 0;
}

.divSpaceLeft {
	border-left: 1.5px solid #c7c9cc;
}

.divSpaceBottom {
	border-bottom: 1.5px solid #c7c9cc;
}

:host {
	::ng-deep.columnTitle,
	::ng-deep.action-column {
		@extend .type-p-small-rounded;
		font-size: 14px !important;
		font-weight: 700 !important;
		color: $pretoPri;
	}

	.interagivel {
		color: $azulim06 !important;
		text-decoration: none !important;
	}

	::ng-deep.table-content {
		padding: 20px 0 0 0 !important;
	}

	.action-cell {
		display: flex;
		flex-wrap: wrap;
	}

	.table-title {
		font-family: Poppins;
		font-size: 14px;
		font-weight: 600;
		line-height: 18px;
		letter-spacing: 0.25px;
		text-align: left;
		color: $pretoPri;
	}
}

::ng-deep.table-nota-fiscal > .pacto-table-title-block {
	padding: unset;
}

.centered {
	display: flex;
	justify-content: space-around;
}

.acoes-nota-fiscal {
	a {
		padding-right: 7px;
		cursor: pointer;
	}
}

.divTabs {
	display: flex;
	flex-direction: row;
	border-radius: 5px;
	margin: 0;
	padding: 0;
	width: 50%;

	.tab {
		line-height: 30px;
		margin: 0px;
		font-size: 14px;
		color: #1e60fa;
		padding: 5px 20px;
		cursor: pointer;
		font-weight: 700;
		border-radius: 5px;
	}

	.selecionado {
		color: #0547e1;
		background: #b4cafd;
		border-radius: 5px;
	}

	.tab:hover {
		background: #bde5fe;
	}
}

.modal-pessoa-regua {
}

.comBorder {
	border-top: 1px solid #c7c9cc;
}

.div-geral-table-retentativas {
	display: grid;
	grid-template-columns: 1fr;
	padding: 20px 0 20px 0;

	.div-geral-table-retentativas-superior {
		display: grid;
		grid-template-columns: 1fr 1fr;

		.div-geral-table-retentativas-tentativa {
			text-align: left;
			color: #797d86;
			font-size: 12px;
			font-weight: 700;
			line-height: 15px;
			letter-spacing: 0.25px;
			word-wrap: break-word;
		}

		.div-geral-table-retentativas-valores {
			text-align: right;
			color: #55585e;
			font-size: 14px;
			font-weight: 500;
			line-height: 14px;
			letter-spacing: 0.25px;
			word-wrap: break-word;
		}
	}

	.div-geral-table-retentativas-centro {
		.div-geral-table-retentativas-grafico {
			display: flex;
			align-items: center;
			margin: 7px 0;

			.div-geral-table-retentativas-grafico-fundo {
				background: #d7d8db;
				height: 10px;
				width: 100%;
				border-radius: 8px;

				.div-geral-table-retentativas-grafico-cor {
					background: #366ae2;
					height: 10px;
					width: 0;
					border-radius: 8px;
				}
			}

			.div-geral-table-retentativas-grafico-percentual {
				color: #55585e;
				font-size: 18px;
				font-weight: 500;
				line-height: 18px;
				letter-spacing: 0.25px;
				word-wrap: break-word;
				padding-left: 10px;
			}

			.div-geral-table-retentativas-grafico-percentual-perc {
				font-size: 12px;
				padding-left: 2px;
			}
		}
	}

	.div-geral-table-retentativas-inferior {
		display: grid;
		grid-template-columns: 1fr;

		.div-geral-table-retentativas-inferior-texto {
			color: #797d86;
			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			word-wrap: break-word;
		}
	}
}

.div-geral-detalhe-comunicacao-dados {
	display: grid;
	grid-template-columns: 1fr;
}

.div-geral-detalhe-comunicacao-dados-superior {
	display: grid;
	border: 1.5px solid #c7c9cc;
	padding: 20px;
	border-radius: 5px;
}

.div-geral-detalhe-comunicacao-dados-inferior {
	display: grid;
	grid-template-columns: 1fr 1fr;
	border: 1.5px solid #c7c9cc;
	border-radius: 5px;
	margin-top: 30px;
	grid-gap: 16px;
	padding: 20px;
}

.item-superior-detalhe-envio {
	color: #55585e;
	font-size: 22px;
	font-weight: 500;
	line-height: 22px;
	letter-spacing: 0.25px;
	word-wrap: break-word;
}

.item-inferior-detalhe-envio {
	color: #80858c;
	font-size: 12px;
	font-weight: 400;
	line-height: 16px;
	word-wrap: break-word;
}

.div-geral-detalhe-comunicacao {
}

.div-detalhe-comunicacao {
	display: grid;
	grid-template-columns: 1fr 1fr;
	text-align: center;
	align-items: center;
}

.div-geral-detalhe-comunicacao-dados-inferior-item {
	display: grid;
	grid-template-columns: 1fr;
	text-align: center;
	align-items: center;
}

.div-grafico-historico-dia {
	margin-top: 30px;
}

.div-geral-detalhe-comunicacao-grafico {
	display: grid;
	justify-content: center;
}

.divTabsDetalhe {
	display: flex;
	flex-direction: row;
	border-radius: 5px;
	margin: 0;
	padding: 0;
	margin-bottom: 15px;
	width: 100%;
	border-bottom: 1.5px solid #c7c9cc;

	.tabDet {
		color: #55585e;
		font-size: 12px;
		font-family: Poppins;
		font-weight: 600;
		line-height: 12px;
		letter-spacing: 0.25px;
		word-wrap: break-word;
		padding: 10px 20px;
		cursor: pointer;
	}

	.selecionadoDet {
		border-bottom: 2px solid #0547e1;
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
	}

	.tabDet:hover {
		border-bottom: 2px solid #bde5fe;
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
	}
}

pacto-cat-accordion::ng-deep {
	.body-section {
		background: none;
		box-shadow: none;
	}

	.header-section {
		padding: 5px 15px;
		text-align: end;
		background: none;
		box-shadow: none;

		.status-control {
			color: #1e60fa !important;
			.pct {
				color: #1e60fa !important;
			}
			.i {
				color: #1e60fa !important;
			}
		}

		.section-title {
			font-weight: 600 !important;
			font-size: 14px !important;
			color: #1e60fa !important;
			width: 100%;
		}
	}
}

.div-pacto-cat-accordion {
	margin: 20px 0 20px 0;
}

.div-geral-table-retentativas-inferior-convenios-geral {
	margin: 20px 0 5px 0;
	display: grid;
	grid-template-columns: 1fr 1fr;
	text-align: center;
	align-items: center;
	border: 1.5px solid #c7c9cc;
	border-radius: 5px;
	padding: 15px;
}

.div-geral-table-retentativas-inferior-convenios {
	display: grid;
	grid-template-columns: 1fr;

	.div-titulo {
		display: flex;
		grid-template-columns: 1fr;
		justify-content: center;
		align-items: center;
		gap: 10px;

		.titulo {
			font-weight: 500;
			font-size: 22px;
			color: #55585e;
		}

		.titulo-percent {
			font-weight: 500;
			font-size: 12px;
			color: #55585e;
		}
	}

	.info {
		font-weight: 400;
		font-size: 12px;
		color: #80858c;
	}
}

.div-grafico-detalhe-retentativas {
	margin-top: 10px;
}
