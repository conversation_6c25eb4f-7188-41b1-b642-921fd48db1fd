<div class="div-geral">
	<pacto-relatorio
		#tableData
		*ngIf="table"
		[emptyStateMessage]="'Nenhuma registro encontrado'"
		[enableZebraStyle]="true"
		[id]="getIdTable()"
		[showShare]="true"
		[table]="table"
		class="table-nota-fiscal"></pacto-relatorio>
</div>

<ng-template #colunaFormaPagamento let-item="item">
	<span [darkTheme]="true" [pactoCatTolltip]="item.cobranca_origem">
		{{ item.forma_pagamento }}
	</span>
</ng-template>

<ng-template #cellTipo let-item="item">
	<div *ngIf="item.tipo === 0">NF-e</div>
	<div *ngIf="item.tipo === 1">NFS-e</div>
	<div *ngIf="item.tipo === 2">NFC-e</div>
</ng-template>
<ng-template #cellAcoes let-item="item">
	<div class="acoes-nota-fiscal">
		<a
			*ngIf="item.linkPDF && item.linkPDF.length > 0"
			[darkTheme]="true"
			[pactoCatTolltip]="'Download do PDF'"
			href="{{ item.linkPDF }}"
			role="button"
			target="_blank">
			<i class="pct pct-file cor-azulim05"></i>
		</a>
		<a
			*ngIf="item.linkXML && item.linkXML.length > 0"
			[darkTheme]="true"
			[pactoCatTolltip]="'Download do XML'"
			href="{{ item.linkXML }}"
			role="button"
			target="_blank">
			<img src="assets/images/pct-xml.svg" style="padding-bottom: 4px" />
		</a>
		<a
			(click)="enviarEmail(item)"
			*ngIf="item.linkPDF && item.linkPDF.length > 0"
			[darkTheme]="true"
			[pactoCatTolltip]="'Enviar por e-mail'">
			<i class="pct pct-send cor-azulim05"></i>
		</a>
	</div>
</ng-template>
