import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { AdmCoreApiClienteService } from "adm-core-api";
import { ActivatedRoute } from "@angular/router";
import {
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { Api } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";

declare var moment;

@Component({
	selector: "pacto-modal-lista-pessoas-regua",
	templateUrl: "./modal-lista-pessoas-regua.component.html",
	styleUrls: ["./modal-lista-pessoas-regua.component.scss"],
})
export class ModalListaPessoasReguaComponent implements OnInit {
	@Input()
	public filtros: any;
	@Input()
	public tipo: number;
	@Input()
	public aba: any;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	public table: PactoDataGridConfig;
	@ViewChild("cellTipo", { static: false })
	private cellTipo: TemplateRef<any>;
	@ViewChild("cellAcoes", { static: false })
	private cellAcoes: TemplateRef<any>;
	data = false;
	@ViewChild("colunaFormaPagamento", { static: true })
	colunaFormaPagamento: TemplateRef<any>;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly msAdmCoreService: AdmCoreApiClienteService,
		private readonly admLegadoService: AdmLegadoTelaClienteService,
		private readonly restApi: RestService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initTable();
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.restApi.buildFullUrl(
				`regua-cobranca-bi/lista-pessoas/${this.tipo}`,
				true,
				Api.PACTOPAYMS
			),
			quickSearch: true,
			exportButton: true,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			initialFilters: [
				{ name: "dataInicio", value: this.filtros.dataInicio },
				{ name: "dataFinal", value: this.filtros.dataFinal },
				{ name: "empresas", value: this.filtros.empresas },
				{ name: "tipoConsulta", value: this.tipo },
			],
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "matricula",
					titulo: "Matricula",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nome",
					titulo: "Nome",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "parcela_codigo",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "parcela_descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "parcela_vencimento",
					titulo: "Dt. Vencimento",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "parcela_nrtentativas",
					titulo: "Nr Tentativas",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "forma_pagamento",
					titulo: "Forma Pagamento",
					visible: true,
					ordenavel: true,
					celula: this.colunaFormaPagamento,
				},
				{
					nome: "data_pagamento",
					titulo: "Dt. Pagamento",
					visible: true,
					ordenavel: false,
					valueTransform: (v) =>
						v ? moment(v).format("DD/MM/YYYY HH:mm:ss") : "",
				},
				{
					nome: "parcela_valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
				{
					nome: "dias_atraso",
					titulo: "Dias Atraso",
					visible: this.aba === "pcc-tab-eficiencia",
					ordenavel: true,
				},
				{
					nome: "cobranca_desconto",
					titulo: "Desconto",
					visible: this.aba === "pcc-tab-antecipacao",
					ordenavel: true,
					valueTransform(v) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
			],
		});
		this.cd.detectChanges();
	}

	enviarEmail(row) {
		this.admLegadoService
			.enviarEmailNotaFiscal(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				row.codigo
			)
			.subscribe(
				(response) => {
					if (response.content) {
						this.snotifyService.success(response.content);
					} else {
						this.snotifyService.error(response.meta.message);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else {
						this.snotifyService.error(
							"Ocorreu um erro inesperado, tente novamente."
						);
					}
				}
			);
	}

	getIdTable() {
		return "REGUA_PESSOA_" + new Date().getTime();
	}
}
