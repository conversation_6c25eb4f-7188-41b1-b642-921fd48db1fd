<div class="nav-aux">
	<a class="top-navigation" id="regua-cobranca">
		<span>Régua de cobrança</span>
	</a>
</div>

<div class="tab-content">
	<pacto-cat-card-plain>
		<div class="divSuperior d-flex align-items-center">
			<div class="divTabs">
				<div *ngFor="let aba of abas; let index = index">
					<div
						(click)="clickAba(aba.id)"
						*ngIf="aba.apresentar"
						[ngClass]="abaSelecionada === aba.id ? 'selecionado' : ''"
						class="{{ aba.class }}"
						id="{{ aba.id }}">
						{{ aba.nome }}
					</div>
				</div>
			</div>
			<div class="divFiltros d-flex align-items-center">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('empresas')"
					[options]="empresaOptions"></pacto-cat-multi-select-filter>
				<pacto-cat-form-datepicker
					[control]="formGroup.get('dtInicio')"
					id="regua-dt-inicio"></pacto-cat-form-datepicker>
				<pacto-cat-form-datepicker
					[control]="formGroup.get('dtFinal')"
					id="regua-dt-final"></pacto-cat-form-datepicker>
				<div (click)="atualizar()" class="divIconAtualizar">
					<i class="pct pct-refresh-cw"></i>
				</div>
			</div>
		</div>
		<div class="d-flex float-right">
			<span *ngIf="dataAtualizacao" class="atualizadoEm">
				Atualizado em {{ dataAtualizacao | date : "shortDate" }} às
				{{ dataAtualizacao | date : "HH:mm" }}
			</span>
		</div>

		<div class="divSpaceBottom divTituloGrafico"></div>

		<!-- EFICIENCIA -->
		<div *ngIf="abaEficiencia()" class="divGeralEficiencia">
			<div class="divTituloGrafico">
				<span class="tituloGrafico">Parcelas x Recuperação financeira</span>
			</div>
			<div class="d-flex divTotalEficiencia">
				<div class="d-grid divItemEficiencia">
					<span class="tituloItemEficiencia">
						{{ eficienciaTotal?.qtdTotal }}
					</span>
					<span class="infoItemEficiencia">Total de parcelas cobradas</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ transformMoneyValue(eficienciaTotal?.valorTotal) }}
					</span>
					<span class="infoItemEficiencia">Valor total a receber</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(2, true)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(eficienciaTotal?.valorRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(3, false)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(eficienciaTotal?.valorNaoRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor não recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ eficienciaTotal?.eficiencia }} %
					</span>
					<span class="infoItemEficiencia">de eficiência</span>
				</div>
			</div>

			<div class="divSpaceBottom divTituloGrafico"></div>

			<div class="divTituloGrafico">
				<span class="tituloGrafico">Formas de recebimento</span>
			</div>
			<pacto-cat-column-chart
				[animation]="false"
				[distributed]="true"
				[legend]="false"
				[series]="[eficienciaFormasPagamentoDados]"
				[tooltipFormatter]="tooltipFormatterValor"
				[xAxisLabels]="eficienciaFormasPagamentoLabels"
				height="300px"></pacto-cat-column-chart>

			<div class="divTituloGrafico">
				<span class="tituloGrafico">Canal de comunicação</span>
			</div>
			<pacto-cat-column-chart
				[animation]="false"
				[colors]="eficienciaComunicacaoColors"
				[distributed]="true"
				[height]="300"
				[legend]="false"
				[series]="[eficienciaComunicacaoSeries]"
				[tooltipFormatter]="tooltipFormatterValor"
				[xAxisLabels]="eficienciaComunicacaoLabels"></pacto-cat-column-chart>
		</div>

		<!-- RETENTATIVA -->
		<div *ngIf="abaRetentativa()" class="divGeralRetentativa">
			<div class="divTituloGrafico">
				<span class="tituloGrafico">Retentativas nos últimos dias</span>
			</div>
			<div class="d-flex divTotalEficiencia">
				<div class="d-grid divItemEficiencia">
					<span class="tituloItemEficiencia">
						{{ retentativaTotal?.qtdTotal }}
					</span>
					<span class="infoItemEficiencia">Total de parcelas cobradas</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ transformMoneyValue(retentativaTotal?.valorTotal) }}
					</span>
					<span class="infoItemEficiencia">Valor total a receber</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(5, true)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(retentativaTotal?.valorRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(6, false)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(retentativaTotal?.valorNaoRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor não recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ retentativaTotal?.eficiencia }} %
					</span>
					<span class="infoItemEficiencia">de eficiência</span>
				</div>
			</div>

			<div class="div-grafico-historico-dia">
				<pacto-cat-line-chart
					[colors]="retentativaHistoricoDiaColors"
					[series]="retentativaHistoricoDiaSeries"
					[tooltipFormatter]="tooltipFormatterMoney"
					[xAxisLabels]="retentativaHistoricoDiaLabels"
					[yAxisTitle]="''"></pacto-cat-line-chart>
			</div>

			<div class="divSpaceBottom divTituloGrafico"></div>

			<div class="divTituloGrafico">
				<span class="tituloGrafico">Retentativas automáticas</span>
			</div>

			<div
				*ngFor="let item of retentativaTentativa; let index = index"
				class="teste-histor-regua-aaaaaa">
				<div
					[ngClass]="{ comBorder: index != 0 }"
					class="div-geral-table-retentativas">
					<div class="div-geral-table-retentativas-superior">
						<div class="div-geral-table-retentativas-tentativa">
							{{ item.tentativa }}
						</div>
						<div class="div-geral-table-retentativas-valores">
							{{ transformMoneyValue(item?.valorRecebido) }} /
							{{ transformMoneyValue(item?.valorTotal) }}
						</div>
					</div>

					<div class="div-geral-table-retentativas-centro">
						<div class="div-geral-table-retentativas-grafico">
							<div class="div-geral-table-retentativas-grafico-fundo">
								<div
									[ngStyle]="
										item?.eficiencia ? { width: item?.eficiencia + '%' } : {}
									"
									class="div-geral-table-retentativas-grafico-cor"></div>
							</div>
							<div class="div-geral-table-retentativas-grafico-percentual">
								{{ item?.eficiencia }}
							</div>
							<div class="div-geral-table-retentativas-grafico-percentual-perc">
								%
							</div>
						</div>
					</div>

					<div class="div-geral-table-retentativas-inferior">
						<div class="div-geral-table-retentativas-inferior-texto">
							{{ transformMoneyValue(item?.valorRecebido) }} recuperados nesta
							retentativa
						</div>
					</div>

					<pacto-cat-accordion
						[title]="'Expandir'"
						class="div-pacto-cat-accordion"
						id="pca-forma-recebimento-retentativa-accord-{{ index }}">
						<accordion-header
							class="div-pacto-cat-accordion-header"
							id="pca-forma-recebimento-retentativa-accord-header-{{
								index
							}}"></accordion-header>
						<accordion-body class="div-pacto-cat-accordion-body">
							<div class="div-grafico-detalhe-retentativas">
								<pacto-cat-column-chart
									[animation]="false"
									[distributed]="true"
									[height]="300"
									[legend]="false"
									[series]="[
										retentativaTentativaGrafico[index].retentativaDetalheSeries
									]"
									[tooltipFormatter]="tooltipFormatterValor"
									[xAxisLabels]="
										retentativaTentativaGrafico[index].retentativaDetalheLabels
									"></pacto-cat-column-chart>
							</div>

							<div
								class="div-geral-table-retentativas-inferior-convenios-geral">
								<div class="div-geral-table-retentativas-inferior-convenios">
									<div class="div-titulo">
										<span class="titulo">
											{{ transformMoneyValue(item?.convenioPrincipalValor) }}
										</span>
										<span class="titulo-percent">
											{{ item?.convenioPrincipalPercentual }} %
										</span>
									</div>
									<span class="info">Recebido no convênio padrão</span>
								</div>
								<div
									class="div-geral-table-retentativas-inferior-convenios divSpaceLeft">
									<div class="div-titulo">
										<span class="titulo">
											{{ transformMoneyValue(item?.convenioDemaisValor) }}
										</span>
										<span class="titulo-percent">
											{{ item?.convenioDemaisPercentual }} %
										</span>
									</div>
									<span class="info">Recebido nos demais convênios</span>
								</div>
							</div>
						</accordion-body>
					</pacto-cat-accordion>
				</div>
			</div>
		</div>

		<!-- COMUNICAÇÃO -->
		<div *ngIf="abaComunicacao()" class="divGeralComunicacao">
			<div class="divTituloGrafico">
				<span class="tituloGrafico">Comunicação nos últimos dias</span>
			</div>
			<div class="d-flex divTotalEficiencia">
				<div class="d-grid divItemEficiencia">
					<span class="tituloItemEficiencia">
						{{ comunicacaoTotal?.qtdTotal }}
					</span>
					<span class="infoItemEficiencia">Total de comunicações enviadas</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ transformMoneyValue(comunicacaoTotal?.valorTotal) }}
					</span>
					<span class="infoItemEficiencia">Valor total a receber</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(8, true)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(comunicacaoTotal?.valorRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(9, false)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(comunicacaoTotal?.valorNaoRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor não recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ comunicacaoTotal?.eficiencia }} %
					</span>
					<span class="infoItemEficiencia">de eficiência</span>
				</div>
			</div>

			<div class="div-grafico-historico-dia">
				<pacto-cat-line-chart
					[colors]="comunicacaoHistoricoDiaColors"
					[series]="comunicacaoHistoricoDiaSeries"
					[tooltipFormatter]="tooltipFormatterMoney"
					[xAxisLabels]="comunicacaoHistoricoDiaLabels"
					[yAxisTitle]="''"></pacto-cat-line-chart>
			</div>

			<div class="divSpaceBottom divTituloGrafico"></div>
			<div class="divTituloGrafico">
				<span class="tituloGrafico">Canal de comunicação</span>
			</div>

			<pacto-cat-column-chart
				[animation]="false"
				[colors]="comunicacaoCanaisColors"
				[distributed]="true"
				[height]="300"
				[legend]="false"
				[series]="[comunicacaoCanaisSeries]"
				[tooltipFormatter]="tooltipFormatterValor"
				[xAxisLabels]="comunicacaoCanaisLabels"></pacto-cat-column-chart>

			<div class="divSpaceBottom divTituloGrafico"></div>

			<div class="divTituloGrafico">
				<span class="tituloGrafico">Envios de comunicação de cobranças</span>
			</div>

			<div
				*ngFor="let item of comunicacaoTentativa; let index = index"
				class="teste-histor-regua-aaaaaa">
				<div
					[ngClass]="{ comBorder: index != 0 }"
					class="div-geral-table-retentativas">
					<div class="div-geral-table-retentativas-superior">
						<div class="div-geral-table-retentativas-tentativa">
							{{ item.tentativa }}
						</div>
						<div class="div-geral-table-retentativas-valores">
							{{ transformMoneyValue(item?.valorRecebido) }} /
							{{ transformMoneyValue(item?.valorTotal) }}
						</div>
					</div>

					<div class="div-geral-table-retentativas-centro">
						<div class="div-geral-table-retentativas-grafico">
							<div class="div-geral-table-retentativas-grafico-fundo">
								<div
									[ngStyle]="
										item?.eficiencia ? { width: item?.eficiencia + '%' } : {}
									"
									class="div-geral-table-retentativas-grafico-cor"></div>
							</div>
							<div class="div-geral-table-retentativas-grafico-percentual">
								{{ item?.eficiencia }}
							</div>
							<div class="div-geral-table-retentativas-grafico-percentual-perc">
								%
							</div>
						</div>
					</div>

					<div class="div-geral-table-retentativas-inferior">
						<div class="div-geral-table-retentativas-inferior-texto">
							{{ transformMoneyValue(item?.valorRecebido) }} recuperados nesta
							retentativa
						</div>
					</div>
				</div>
			</div>

			<div class="divSpaceBottom divTituloGrafico"></div>
			<div class="divTituloGrafico">
				<span class="tituloGrafico">Detalhes dos envios de comunicação</span>
			</div>

			<div class="div-geral-detalhe-comunicacao">
				<div class="divTabsDetalhe">
					<div *ngFor="let aba of abasDetalhe; let index = index">
						<div
							(click)="clickDetalhe(aba)"
							*ngIf="aba.apresentar"
							[ngClass]="detalheSelecionado === aba.id ? 'selecionadoDet' : ''"
							class="tabDet"
							id="{{ aba.id }}">
							{{ aba.nome }}
						</div>
					</div>
				</div>
				<div class="div-detalhe-comunicacao">
					<div class="div-geral-detalhe-comunicacao-grafico">
						<pacto-cat-pie-chart
							[colors]="comunicacaoDetalheColors"
							[height]="250"
							[id]="'grafico-detalhe'"
							[labelFormatterBorder]="composicaoLabelBorderFn()"
							[labelFormatter]="composicaoLabelFn()"
							[series]="comunicacaoDetalheSeries"></pacto-cat-pie-chart>
					</div>
					<div class="div-geral-detalhe-comunicacao-dados">
						<div class="div-geral-detalhe-comunicacao-dados-superior">
							<span class="item-superior-detalhe-envio">
								{{ comunicacaoDetalhe?.qtdTotal }}
							</span>
							<span class="item-inferior-detalhe-envio">
								Comunicações enviadas
							</span>
						</div>

						<div class="div-geral-detalhe-comunicacao-dados-inferior">
							<div class="div-geral-detalhe-comunicacao-dados-inferior-item">
								<span class="item-superior-detalhe-envio">
									{{ comunicacaoDetalhe?.qtdLido }}
								</span>
								<span class="item-inferior-detalhe-envio">
									Comunicações lidas
								</span>
							</div>
							<div
								class="div-geral-detalhe-comunicacao-dados-inferior-item divSpaceLeft">
								<span class="item-superior-detalhe-envio">
									{{ comunicacaoDetalhe?.qtdNaoLido }}
								</span>
								<span class="item-inferior-detalhe-envio">
									Comunicações não lidas
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- ANTECIPACAO -->
		<div *ngIf="abaAntecipacao()" class="divGeralAntecipacao">
			<div class="divTituloGrafico">
				<span class="tituloGrafico">Cobranças antecipadas</span>
			</div>
			<div class="d-flex divTotalEficiencia">
				<div class="d-grid divItemEficiencia">
					<span class="tituloItemEficiencia">
						{{ antecipacaoTotal?.qtdTotal }}
					</span>
					<span class="infoItemEficiencia">Total de parcelas antecipadas</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ transformMoneyValue(antecipacaoTotal?.valorTotal) }}
					</span>
					<span class="infoItemEficiencia">Valor total enviado</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(11, true)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(antecipacaoTotal?.valorRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span
						(click)="openModalListaPessoas(12, false)"
						class="tituloItemEficiencia itemClick">
						{{ transformMoneyValue(antecipacaoTotal?.valorNaoRecebido) }}
					</span>
					<span class="infoItemEficiencia">Valor não recebido</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ transformMoneyValue(antecipacaoTotal?.valorDesconto) }}
					</span>
					<span class="infoItemEficiencia">Valor cedido em descontos</span>
				</div>
				<div class="d-grid divItemEficiencia divSpaceLeft">
					<span class="tituloItemEficiencia">
						{{ antecipacaoTotal?.eficiencia }} %
					</span>
					<span class="infoItemEficiencia">de eficiência</span>
				</div>
			</div>

			<div class="divSpaceBottom divTituloGrafico"></div>

			<div class="divTituloGrafico">
				<span class="tituloGrafico">Formas de recebimento</span>
			</div>
			<pacto-cat-column-chart
				[animation]="false"
				[distributed]="true"
				[legend]="false"
				[series]="[antecipacaoFormasPagamentoDados]"
				[tooltipFormatter]="tooltipFormatterValor"
				[xAxisLabels]="antecipacaoFormasPagamentoLabels"
				height="300px"></pacto-cat-column-chart>
		</div>
	</pacto-cat-card-plain>
</div>
