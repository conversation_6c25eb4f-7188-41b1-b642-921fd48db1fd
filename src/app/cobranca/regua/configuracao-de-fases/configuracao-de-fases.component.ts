import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import {
	AbstractControl,
	FormArray,
	FormBuilder,
	FormGroup,
	Validators,
} from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { CatAccordionComponent } from "ui-kit";
import { <PERSON>ua, ZwPactopayApiConfigService } from "zw-pactopay-api";
import { CobrancaService } from "../../cobranca.service";

@Component({
	selector: "pacto-configuracao-de-fases",
	templateUrl: "./configuracao-de-fases.component.html",
	styleUrls: ["./configuracao-de-fases.component.scss"],
})
export class ConfiguracaoDeFasesComponent implements OnInit {
	public form: FormGroup;
	public cobranca_antecipada: FormGroup;
	public comunicado_resultado: FormGroup;
	public retentativa: FormGroup;
	public multiplos_convenios: FormGroup;
	public comunicado_atraso: FormGroup;
	public comunicado_cartao: FormGroup;
	public config: any;
	public qtdDiasEficienciaComunicacao: number;

	constructor(
		private readonly formBuilder: FormBuilder,
		private readonly notify: SnotifyService,
		private readonly cd: ChangeDetectorRef,
		private readonly cobrancaService: CobrancaService,
		private readonly zwPactopayApiConfig: ZwPactopayApiConfigService
	) {}

	ngOnInit(): void {
		this.cobrancaService.definirTitle("PactoPay - Régua de cobrança");

		this.cobranca_antecipada = this.formBuilder.group({
			ativo: this.formBuilder.control(false),
			sms: this.formBuilder.control(false),
			email: this.formBuilder.control(false),
			whatsapp: this.formBuilder.control(false),
			app: this.formBuilder.control(false),
			gymbotpro: this.formBuilder.control(false),
			com_autorizacao: this.formBuilder.control(false),
			aplicar_desconto: this.formBuilder.control(false),
			desconto: this.formBuilder.control(0),
			valor_fixo: this.formBuilder.control(false),
			dias_limite: this.formBuilder.control(1),
			dias_anteriores: this.formBuilder.control(5),
		});

		this.comunicado_resultado = this.formBuilder.group({
			ativo: this.formBuilder.control(false),
			sms: this.formBuilder.control(false),
			email: this.formBuilder.control(false),
			whatsapp: this.formBuilder.control(false),
			app: this.formBuilder.control(false),
			gymbotpro: this.formBuilder.control(false),
			aprovada: this.formBuilder.control(false),
			negada: this.formBuilder.control(false),
			cancelada: this.formBuilder.control(false),
		});

		this.retentativa = this.formBuilder.group({
			ativo: this.formBuilder.control(false),
			limite_dias: this.formBuilder.control(1),
			intervalo_dias: this.formBuilder.control(3),
		});

		this.multiplos_convenios = this.formBuilder.group({
			ativo: this.formBuilder.control(false),
			convenios: this.formBuilder.array([]),
			qtd_tentativas: this.formBuilder.control(1),
		});

		this.comunicado_atraso = this.formBuilder.group({
			ativo: this.formBuilder.control(false),
			email: this.formBuilder.control(false),
			sms: this.formBuilder.control(false),
			whatsapp: this.formBuilder.control(false),
			app: this.formBuilder.control(false),
			gymbotpro: this.formBuilder.control(false),
			com_autorizacao: this.formBuilder.control(false),
			dias_vencido_minimo: this.formBuilder.control(1),
			dias_vencido_maximo: this.formBuilder.control(0),
			intervalo_dias: this.formBuilder.control(3),
			quantidade_maxima_envios: this.formBuilder.control(3),
		});

		this.comunicado_cartao = this.formBuilder.group({
			ativo: this.formBuilder.control(false),
			email: this.formBuilder.control(false),
			sms: this.formBuilder.control(false),
			whatsapp: this.formBuilder.control(false),
			app: this.formBuilder.control(false),
			gymbotpro: this.formBuilder.control(false),
			vencido: this.formBuilder.control(false),
			proximo_vencimento: this.formBuilder.control(false),
		});

		this.form = this.formBuilder.group({
			codigo: this.formBuilder.control(null, Validators.required),
			empresa: this.formBuilder.control(null, Validators.required),
			envio_automatico_cobranca: this.formBuilder.control(false),
			cobranca_antecipada: this.cobranca_antecipada,
			comunicado_resultado: this.comunicado_resultado,
			retentativa: this.retentativa,
			multiplos_convenios: this.multiplos_convenios,
			comunicado_atraso: this.comunicado_atraso,
			comunicado_cartao: this.comunicado_cartao,
			qtdDiasEficienciaComunicacao: this.qtdDiasEficienciaComunicacao,
		});

		this.zwPactopayApiConfig.obterConfiguracoes().subscribe((res) => {
			if (res.content) {
				this.config = res.content;
				this.popularFormulario(res.content);
			}
		});
	}

	private popularFormulario(regua: Regua): void {
		this.form.patchValue(regua);

		this.convenios.clear();
		for (const convenio of regua.multiplos_convenios.convenios) {
			this.convenios.push(
				this.formBuilder.group({
					codigo: this.formBuilder.control(
						convenio.codigo,
						Validators.required
					),
					tipo: this.formBuilder.control(convenio.tipo, Validators.required),
					posicao: this.formBuilder.control(
						convenio.posicao,
						Validators.required
					),
					tipo_cobranca: this.formBuilder.control(
						convenio.tipo_cobranca,
						Validators.required
					),
					descricao: this.formBuilder.control(
						convenio.descricao,
						Validators.required
					),
				})
			);
		}

		this.cd.detectChanges();
	}

	private get convenios(): FormArray {
		return this.multiplos_convenios.controls["convenios"] as FormArray;
	}

	public salvar(): void {
		this.form.markAllAsTouched();
		if (this.form.valid) {
			const regua: Regua = this.form.value;
			this.zwPactopayApiConfig
				.salvarConfiguracoes(regua)
				.subscribe((res: any) => {
					if (res.error) {
						this.notify.error(res.message);
					} else {
						this.notify.success("Sucesso!");
						this.popularFormulario(res);
					}
				});
		} else {
			this.procuraErro(this.form.controls);
		}
	}

	private procuraErro(controls: { [key: string]: AbstractControl }): void {
		const erros: string[] = [];
		for (const name in controls) {
			if (controls[name].invalid) {
				switch (name) {
					case "envio_automatico_cobranca":
						erros.push("Envio de cobrança automática");
						break;
					case "cobranca_antecipada":
						erros.push("Envio antecipado de cobrança");
						break;
					case "comunicado_resultado":
						erros.push("Comunicar resultado da cobrança");
						break;
					case "retentativa":
						erros.push("Retentativa automática de cobrança");
						break;
					case "multiplos_convenios":
						erros.push("Multiplos convênios");
						break;
					case "comunicado_atraso":
						erros.push("Comunicação de atraso");
						break;
					case "comunicado_cartao":
						erros.push("Comunicar vencimento do cartão");
						break;
				}
			}
		}
		this.notify.confirm(
			`Dados inválidos em ${erros
				.toString()
				.replace(/,/g, ", ")}! Revise os formulários e tente novamente.`
		);
	}

	public toogleAccordion(element: CatAccordionComponent, check: boolean): void {
		element.open = check;
	}
}
