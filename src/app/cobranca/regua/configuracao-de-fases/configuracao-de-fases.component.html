<div class="container">
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'PactoPay / Régua de cobrança',
			menu: 'Configuração de fases'
		}"></pacto-breadcrumbs>

	<form [formGroup]="form" class="form-regua">
		<section class="section-cobranca-automatica">
			<div class="title-content">
				<pacto-cat-switch
					[control]="form.get('envio_automatico_cobranca')"></pacto-cat-switch>
				<section>
					<h5><strong>Envio de cobrança automática</strong></h5>
					<p>
						Envie cobranças automáticas para os convênios habilitados. Se
						desmarcado, não envia para nenhum.
						<br />
						<b>
							(Verifique os convênios habilitados no Cadastro dos Convênios no
							módulo ADM)
						</b>
					</p>
				</section>
			</div>
		</section>

		<pacto-cat-accordion #accordionEnvioAntecipadoDeCobranca>
			<accordion-header class="title-content">
				<pacto-cat-switch
					(click)="
						toogleAccordion(
							accordionEnvioAntecipadoDeCobranca,
							cobranca_antecipada.get('ativo').value
						)
					"
					[control]="cobranca_antecipada.get('ativo')"></pacto-cat-switch>
				<section>
					<h5><strong>Envio antecipado de cobrança</strong></h5>
					<p>
						Envie um comunicado por e-mail, whatsapp, aplicativo ou via
						GymBotPro até 15 dias
						<b>antes</b>
						do vencimento da cobrança para pagamentos via link de cobrança.
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<pacto-envio-antecipado-de-cobranca
						[config]="config"
						[form]="cobranca_antecipada"></pacto-envio-antecipado-de-cobranca>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion #accordionComunicarResultadoDaCobranca>
			<accordion-header class="title-content">
				<pacto-cat-switch
					(click)="
						toogleAccordion(
							accordionComunicarResultadoDaCobranca,
							comunicado_resultado.get('ativo').value
						)
					"
					[control]="comunicado_resultado.get('ativo')"></pacto-cat-switch>
				<section>
					<h5><strong>Comunicar resultado da cobrança</strong></h5>
					<p>
						Envie um comunicado por e-mail, sms, whatsapp, aplicativo ou via
						GymBotPro para o cliente com o
						<b>status da cobrança</b>
						: cobrança aprovada, cobrança negada, cobrança cancelada.
						<br />
						<b>Obs.: o modelo de mensagem é padrão e não pode ser alterado</b>
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<pacto-comunicar-resultado-da-cobranca
						[config]="config"
						[form]="
							comunicado_resultado
						"></pacto-comunicar-resultado-da-cobranca>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion #accordionRetentativaAutomatica>
			<accordion-header class="title-content">
				<pacto-cat-switch
					(click)="
						toogleAccordion(
							accordionRetentativaAutomatica,
							retentativa.get('ativo').value
						)
					"
					[control]="retentativa.get('ativo')"></pacto-cat-switch>
				<section>
					<h5><strong>Retentativa automática de cobrança</strong></h5>
					<p>
						Realize
						<b>novas tentativas de cobrança</b>
						para parcelas com pendências e/ou vencidas de seus clientes
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<pacto-retentativa-automatica
						[form]="retentativa"></pacto-retentativa-automatica>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion #accordionMultiplosConvenios>
			<accordion-header class="title-content">
				<pacto-cat-switch
					(click)="
						toogleAccordion(
							accordionMultiplosConvenios,
							multiplos_convenios.get('ativo').value
						)
					"
					[control]="multiplos_convenios.get('ativo')"></pacto-cat-switch>
				<section>
					<h5><strong>Multiplos convênios</strong></h5>
					<p>
						Adicione seus
						<b>convênios</b>
						e escolha qual será a hierarquia deles para a cobrança de suas
						parcelas
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<pacto-multiplos-convenios
						[form]="multiplos_convenios"></pacto-multiplos-convenios>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion #accordionComunicadoDeAtraso>
			<accordion-header class="title-content">
				<pacto-cat-switch
					(click)="
						toogleAccordion(
							accordionComunicadoDeAtraso,
							comunicado_atraso.get('ativo').value
						)
					"
					[control]="comunicado_atraso.get('ativo')"></pacto-cat-switch>
				<section>
					<h5><strong>Comunicação de atraso</strong></h5>
					<p>
						Envie um comunicado por e-mail, sms, whatsapp, aplicativo ou via
						GymBotPro para seu cliente sobre as
						<b>parcelas em atraso</b>
						com um link de pagamento e um sms com um informativo da pendência.
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<pacto-comunicado-de-atraso
						[config]="config"
						[form]="comunicado_atraso"></pacto-comunicado-de-atraso>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion #accordionComunicarVencimentoDoCartao>
			<accordion-header class="title-content">
				<pacto-cat-switch
					(click)="
						toogleAccordion(
							accordionComunicarVencimentoDoCartao,
							comunicado_cartao.get('ativo').value
						)
					"
					[control]="comunicado_cartao.get('ativo')"></pacto-cat-switch>
				<section>
					<h5><strong>Comunicar vencimento do cartão</strong></h5>
					<p>
						Envie um comunicado por e-mail, sms, whatsapp, aplicativo ou via
						GymBotPro para seu cliente informando que o cartão de crédito
						utilizado para pagamento
						<b>venceu</b>
						ou está
						<b>vencendo</b>
						e um
						<b>e-mail com o link para o cadastro de um novo cartão</b>
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<pacto-comunicar-vencimento-do-cartao
						[config]="config"
						[form]="comunicado_cartao"></pacto-comunicar-vencimento-do-cartao>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion>
			<accordion-header class="title-content">
				<section>
					<h5><strong>Janela de Eficiência da Comunicação</strong></h5>
					<p>
						Informe a quantidade de dias para se considerar pagamentos de
						parcelas realizados como eficiência de uma comunicação enviada para
						um aluno.
					</p>
				</section>
			</accordion-header>
			<accordion-body>
				<div class="accordion-body-content">
					<div class="form-content row">
						<div class="col-5 d-flex align-items-center">
							<span>Dias a serem considerados</span>
						</div>

						<div class="col-5">
							<pacto-cat-form-input
								[errorMsg]="'Inválido!'"
								[control]="form.controls.qtdDiasEficienciaComunicacao"
								[type]="'number'"
								[min]="'1'"
								[max]="'30'"></pacto-cat-form-input>
						</div>
					</div>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<br />
		<br />

		<div class="d-flex justify-content-end">
			<!-- [disabled]="form.invalid" -->
			<button (click)="salvar()" class="btn">Salvar alterações</button>
		</div>
	</form>
</div>
