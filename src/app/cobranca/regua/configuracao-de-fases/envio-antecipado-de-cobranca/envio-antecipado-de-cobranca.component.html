<!-- {{ form.value | json }} -->
<div class="row">
	<div class="col-12 d-flex align-items-center">
		<div *ngIf="config?.email_configurado" class="form-check form-check-inline">
			<pacto-cat-checkbox [control]="form.controls.email"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">
				<i [class.checked]="form.controls.email.value" class="pct pct-mail"></i>
				enviar por e-mail
			</label>
		</div>
		<!--		<div class='form-check form-check-inline' *ngIf='config?.sms_configurado'>-->
		<!--			<pacto-cat-checkbox [control]='form.controls.sms'></pacto-cat-checkbox>-->
		<!--			<label class='form-check-label' style='color: #51555a'>-->
		<!--				<i class='pct pct-tablet' [class.checked]='form.controls.sms.value'></i>-->
		<!--				enviar por sms-->
		<!--			</label>-->
		<!--		</div>-->
		<div
			*ngIf="config?.whatsapp_configurado"
			class="form-check form-check-inline">
			<pacto-cat-checkbox
				[control]="form.controls.whatsapp"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">
				<i
					[class.checked]="form.controls.whatsapp.value"
					class="pct pct-whatsapp"></i>
				enviar por whatsapp
			</label>
		</div>
		<div *ngIf="config?.app_configurado" class="form-check form-check-inline">
			<pacto-cat-checkbox [control]="form.controls.app"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">
				<i [class.checked]="form.controls.app.value" class="pct pct-tablet"></i>
				enviar por aplicativo
			</label>
		</div>
		<div
			class="form-check form-check-inline"
			*ngIf="config?.gymbotpro_configurado">
			<pacto-cat-checkbox
				[control]="form.controls.gymbotpro"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">
				<i
					class="pct pct-whatsapp"
					[class.checked]="form.controls.gymbotpro.value"></i>
				enviar por GymBotPro
			</label>
		</div>
	</div>
</div>
<br />
<div class="row">
	<div class="col-8 d-flex align-items-center">
		<pacto-cat-checkbox
			[control]="form.controls.com_autorizacao"
			[label]="
				'Enviar apenas para quem tem autorização de cobrança automática'
			"></pacto-cat-checkbox>
	</div>
</div>

<div class="row">
	<div class="col-8 d-flex align-items-center">
		<pacto-cat-checkbox
			[control]="form.controls.aplicar_desconto"
			[label]="
				'Aplicar desconto para pagamento antecipado'
			"></pacto-cat-checkbox>
	</div>

	<div class="col-4 row flex-nowrap">
		<div class="col-6">
			<pacto-cat-form-input
				[control]="form.controls.desconto"
				[errorMsg]="'Inválido!'"
				[max]="!form.controls.valor_fixo.value ? '99' : ''"
				[min]="'0'"
				[readonly]="!form.controls.aplicar_desconto.value"
				[type]="'number'"></pacto-cat-form-input>
		</div>

		<div class="form-check form-check-inline col-6">
			<input
				[formControl]="form.controls.valor_fixo"
				[value]="false"
				class="form-check-input"
				name="valor_fixo"
				type="radio" />
			<label class="form-check-label" for="">
				<strong>%&nbsp;&nbsp;&nbsp;</strong>
			</label>

			<input
				[formControl]="form.controls.valor_fixo"
				[value]="true"
				class="form-check-input"
				name="valor_fixo"
				type="radio" />
			<label class="form-check-label" for=""><strong>R$</strong></label>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-8 d-flex align-items-center">
		<span>
			Quantos dias antes do vencimento este e-mail deve ser enviado?
			<br />
			<sub class="detalhe">Mínimo: 5 dias / Máximo: 15 dias</sub>
		</span>
	</div>

	<div class="col-4 row">
		<div class="col-6">
			<pacto-cat-form-input
				[control]="form.controls.dias_anteriores"
				[errorMsg]="'Inválido!'"
				[max]="'15'"
				[min]="'5'"
				[type]="'number'"></pacto-cat-form-input>
		</div>
	</div>
</div>
