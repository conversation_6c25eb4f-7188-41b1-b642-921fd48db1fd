import { Component, Input, OnInit } from "@angular/core";
import { FormGroup, Validators } from "@angular/forms";

@Component({
	selector: "pacto-envio-antecipado-de-cobranca",
	templateUrl: "./envio-antecipado-de-cobranca.component.html",
	styleUrls: ["./envio-antecipado-de-cobranca.component.scss"],
})
export class EnvioAntecipadoDeCobrancaComponent implements OnInit {
	@Input() form: FormGroup;
	@Input() config: any;

	constructor() {}

	ngOnInit() {
		this.form.get("ativo").valueChanges.subscribe((ativo) => {
			ativo ? this.setValidators(this.form) : this.clearValidators(this.form);
		});

		this.form.get("valor_fixo").valueChanges.subscribe((valorFixoAtivado) => {
			if (valorFixoAtivado) {
				this.form.get("desconto").clearValidators();
				this.form
					.get("desconto")
					.setValidators([Validators.required, Validators.min(0)]);
			} else {
				this.form
					.get("desconto")
					.setValidators([
						Validators.required,
						Validators.min(0),
						Validators.max(99),
					]);
			}
			this.form.get("desconto").updateValueAndValidity();
		});

		this.form.get("aplicar_desconto").valueChanges.subscribe((ativo) => {
			if (!ativo) {
				this.form.get("desconto").setValue(0);
			}
		});
	}

	private setValidators(form: FormGroup) {
		const validationType = {
			dias_anteriores: [
				Validators.required,
				Validators.min(5),
				Validators.max(15),
			],
		};

		for (const key of Object.keys(validationType)) {
			form.get(key).setValidators(validationType[key]);
			form.get(key).updateValueAndValidity();
		}
	}

	private clearValidators(form: FormGroup) {
		for (const key in form.controls) {
			form.get(key).clearValidators();
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}
}
