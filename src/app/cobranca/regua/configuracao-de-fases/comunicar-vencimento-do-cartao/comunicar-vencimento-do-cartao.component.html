<div class="form-content">
	<h5><strong>Configurações de envio do comunicado</strong></h5>
	<br />
	<div *ngIf="config?.email_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.email"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.email.value" class="pct pct-mail"></i>
			enviar por e-mail
		</label>
	</div>
	<div *ngIf="config?.sms_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.sms"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.sms.value" class="pct pct-tablet"></i>
			enviar por sms
		</label>
	</div>
	<div
		*ngIf="config?.whatsapp_configurado"
		class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.whatsapp"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i
				[class.checked]="form.controls.whatsapp.value"
				class="pct pct-whatsapp"></i>
			enviar por whatsapp
		</label>
	</div>
	<div *ngIf="config?.app_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.app"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.app.value" class="pct pct-tablet"></i>
			enviar por aplicativo
		</label>
	</div>
	<div
		class="form-check form-check-inline"
		*ngIf="config?.gymbotpro_configurado">
		<pacto-cat-checkbox
			[control]="form.controls.gymbotpro"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i
				class="pct pct-whatsapp"
				[class.checked]="form.controls.gymbotpro.value"></i>
			enviar por GymBotPro
		</label>
	</div>
</div>

<div class="form-content">
	<div style="align-items: center">
		<pacto-cat-checkbox
			[control]="form.controls.vencido"
			[label]="'Comunicar cartão vencido'"></pacto-cat-checkbox>
		<sub class="detalhe">
			Quando selecionado, envia no primeiro dia do próximo mês após o
			vencimento.
		</sub>
	</div>
</div>

<div class="form-content">
	<div style="align-items: center">
		<pacto-cat-checkbox
			[control]="form.controls.proximo_vencimento"
			[label]="'Comunicar cartão próximo do vencimento'"></pacto-cat-checkbox>
		<sub class="detalhe">
			Quando selecionado, envia apenas uma vez no mês anterior ao mês de
			vencimento.
		</sub>
	</div>
</div>
