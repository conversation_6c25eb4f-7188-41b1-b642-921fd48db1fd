<!-- {{ form.value | json }} -->
<div class="row">
	<div class="col-10 d-flex align-items-center">
		<span>
			Quantidade de tentativas de cobrança da parcela
			<br />
			<sub class="detalhe">Mínimo: 1 tentativa / Máximo: 5 tentativas</sub>
		</span>
	</div>

	<div class="col-2">
		<pacto-cat-form-input
			[control]="form.controls.qtd_tentativas"
			[errorMsg]="'Inválido!'"
			[max]="'5'"
			[min]="'1'"
			[type]="'number'"></pacto-cat-form-input>
	</div>
</div>

<h5 style="color: #51555a">Multiplos convênios de cobrança</h5>

<table (cdkDropListDropped)="drop($event)" cdkDropList class="table">
	<thead>
		<tr class="first-line">
			<th><span class="column-title"></span></th>
			<th><span class="column-title">Convênio</span></th>
			<th><span class="column-title">Tipo</span></th>
			<th><span class="column-title">Forma de envio</span></th>
			<th><span class="column-title">Opções</span></th>
			<th><span class="column-title"></span></th>
		</tr>
	</thead>

	<tbody class="body-table">
		<ng-container
			*ngFor="
				let row of convenios.value;
				let lastRow = last;
				let rowIndex = index
			">
			<tr
				[@fadeIn]="convenios.value.length"
				cdkDrag
				id="element-{{ rowIndex }}">
				<!-- Data columns -->
				<td>
					<span class="column-cell">{{ row.posicao }}°</span>
				</td>
				<td>
					<span class="column-cell">
						{{ row.descricao }}
					</span>
				</td>
				<td>
					<span class="column-cell">
						{{ row.tipo }}
					</span>
				</td>
				<td>
					<span class="column-cell">
						{{ row.tipo_cobranca }}
					</span>
				</td>
				<td>
					<span class="column-cell">
						<div
							(click)="removerConvenio(rowIndex)"
							class="d-flex justify-content-center">
							<i class="pct pct-trash-2"></i>
						</div>
					</span>
				</td>
				<td>
					<span cdkDragHandle class="column-cell">
						<div class="mover-convenio">
							<i class="pct pct-move-2"></i>
						</div>
					</span>
				</td>
			</tr>
		</ng-container>
	</tbody>
</table>

<br />

<div class="d-flex justify-content-end">
	<button (click)="novoConvenio()" [disabled]="form.invalid" class="btn">
		<i class="pct pct-plus-circle"></i>
		Novo convênio
	</button>
</div>

<ng-template #descricaoDoErro>
	<section class="d-flex flex-column">
		<span>Não foi possível salvar suas alterações.</span>
		<span>Por favor, tente de novo.</span>
	</section>
</ng-template>
