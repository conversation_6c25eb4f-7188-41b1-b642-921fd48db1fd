import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormArray, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ModalService } from "@base-core/modal/modal.service";
import { BUTTON_SIZE, BUTTON_TYPE, fadeIn, PactoModalRef } from "ui-kit";
import { ZwPactopayApiConfigService } from "zw-pactopay-api";

import { ModalNotificacaoComponent } from "src/app/cobranca/components/modal-notificacao/modal-notificacao.component";
import { AdicionarConvenioComponent } from "./adicionar-convenio/adicionar-convenio.component";

@Component({
	selector: "pacto-multiplos-convenios",
	templateUrl: "./multiplos-convenios.component.html",
	styleUrls: ["./multiplos-convenios.component.scss"],
	animations: [
		trigger("fadeIn", fadeIn(":enter")),
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class MultiplosConveniosComponent implements OnInit {
	@Input() form: FormGroup;
	@ViewChild("descricaoDoErro", { static: false }) descricaoDoErro;

	constructor(
		private readonly zwPactopayApiConfig: ZwPactopayApiConfigService,
		private readonly modalService: ModalService,
		private readonly formBuilder: FormBuilder
	) {}

	ngOnInit() {
		this.form.get("ativo").valueChanges.subscribe((ativo) => {
			ativo ? this.setValidators(this.form) : this.clearValidators(this.form);
		});
	}

	private setValidators(form: FormGroup) {
		const validationType = {
			qtd_tentativas: [
				Validators.required,
				Validators.min(1),
				Validators.max(5),
			],
		};

		for (const key of Object.keys(validationType)) {
			form.get(key).setValidators(validationType[key]);
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}

	private clearValidators(form: FormGroup) {
		for (const key in form.controls) {
			form.get(key).clearValidators();
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}

	public get convenios() {
		return this.form.controls["convenios"] as FormArray;
	}

	drop(event: CdkDragDrop<string[]>) {
		const dir = event.currentIndex > event.previousIndex ? 1 : -1;

		const from = event.previousIndex;
		const to = event.currentIndex;

		const temp = this.convenios.at(from);
		for (let i = from; i * dir < to * dir; i = i + dir) {
			const current = this.convenios.at(i + dir);
			current.get("posicao").setValue(i + 1);
			this.convenios.setControl(i, current);
		}
		temp.get("posicao").setValue(to + 1);
		this.convenios.setControl(to, temp);
	}

	removerConvenio(index) {
		this.convenios.removeAt(index);
		for (let i = 0; i < this.convenios.length; i++) {
			const current = this.convenios.at(i);
			current.get("posicao").setValue(i + 1);
			this.convenios.setControl(i, current);
		}
	}

	novoConvenio() {
		this.zwPactopayApiConfig.getConvenios("retentativa").subscribe((res) => {
			const convenios = res.map((convenio) => {
				return {
					value: convenio.codigo,
					label: convenio.descricao,
				};
			});
			const modalAdicionarConvenio: PactoModalRef = this.modalService.open(
				"Adicionar convênio",
				AdicionarConvenioComponent
			);
			modalAdicionarConvenio.componentInstance.convenios = convenios;
			modalAdicionarConvenio.componentInstance.convenioSelecionado.subscribe(
				(selecionado: number) => {
					if (this.convenios.value.map((c) => c.codigo).includes(selecionado)) {
						const modalNotificacao: PactoModalRef = this.modalService.open(
							"Erro",
							ModalNotificacaoComponent
						);
						modalNotificacao.componentInstance.type = "pct-x";
						modalNotificacao.componentInstance.title = "Ah não!";
						modalNotificacao.componentInstance.body = this.descricaoDoErro;
						modalNotificacao.componentInstance.actions = [
							{
								clickHandler: () => modalNotificacao.close(true),
								type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
								size: BUTTON_SIZE.LARGE,
								label: "Ok",
								full: true,
							},
						];
					} else {
						const convenio = res.find((c) => c.codigo === selecionado);
						this.convenios.push(
							this.formBuilder.group({
								codigo: this.formBuilder.control(
									convenio.codigo,
									Validators.required
								),
								tipo: this.formBuilder.control(
									convenio.tipo_convenio_descricao,
									Validators.required
								),
								posicao: this.formBuilder.control(
									this.convenios.length + 1,
									Validators.required
								),
								tipo_cobranca: this.formBuilder.control(
									convenio.tipo_cobranca_descricao,
									Validators.required
								),
								descricao: this.formBuilder.control(
									convenio.descricao,
									Validators.required
								),
							})
						);
						const modalNotificacao: PactoModalRef = this.modalService.open(
							"Sucesso",
							ModalNotificacaoComponent
						);
						modalNotificacao.componentInstance.type = "pct-check";
						modalNotificacao.componentInstance.title = "Tudo certo!";
						modalNotificacao.componentInstance.subtitle =
							"O convênio foi adicionado com sucesso ";
						modalNotificacao.componentInstance.actions = [
							{
								clickHandler: () => modalNotificacao.close(true),
								type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
								size: BUTTON_SIZE.LARGE,
								label: "Ok",
								full: true,
							},
						];
					}
				}
			);
		});
	}
}
