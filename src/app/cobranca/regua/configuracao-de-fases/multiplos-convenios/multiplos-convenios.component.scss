@import "dist/ui-kit/assets/import.scss";

* {
	color: $pretoPri;
}

.detalhe {
	bottom: 0.5em;
}

.btn {
	padding: 0.5rem 2.5rem;
	color: #fff;
	background-color: $azulim05;

	i {
		padding-right: 0.3125rem;
		color: #fff;
	}
}

.form-check-label {
	display: flex;
	align-items: center;

	i {
		padding-right: 0.3125rem;
		font-size: 1.25rem;
	}
}

.pct-trash-2 {
	color: $hellboyPri;
	cursor: pointer;
}

.mover-convenio {
	cursor: pointer;
	padding-left: 0.813rem;
	border-left: 1px solid #dcdddf;
}

table.table {
	margin-bottom: 0px;
	background-color: transparent;

	// tbody > tr:nth-child(odd) {
	//   background: #fafafa;
	// }

	.body-table {
		border-top: 2px solid #dee2e6;
		background: #ffff;
	}

	th {
		@extend .type-btn-bold;
		color: $pretoPri;

		&.sortable {
			cursor: pointer;

			i.pct {
				padding-left: 10px;
				color: $pretoPri;
			}
		}
	}

	tr {
		@extend .type-p-small;
		color: $preto02;
	}

	tr.row-clickable:not(.ghost) {
		cursor: pointer;
	}

	tr.total-row td {
		font-weight: bold;
	}

	.row-colored-first > tbody > tr:nth-child(odd) {
		background: #fafafa;
	}

	.row-colored-second > tbody > tr:nth-child(even) {
		background: #fafafa;
	}

	.column-cell.hover-cell:hover {
		cursor: pointer;
		text-decoration: underline;
	}

	td {
		&.center {
			text-align: center;
		}

		&.right {
			text-align: right;
		}
	}

	th {
		&.center {
			text-align: center;
		}

		&.right {
			text-align: right;
		}
	}
}

.loading-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;

	.loading-data-icon {
		width: 30px;
		position: relative;
		top: -1px;
	}
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

.selectable {
	width: 10px;
	text-align: center;

	input {
		position: relative;
		margin-top: calc(25% + 3px);
		margin-bottom: calc(25% + 3px);
		margin-left: -2px;
	}
}

.action-cell i {
	font-size: 16px;
}

.action-cell i.fa-trash-o {
	color: #db2c3d;
}

.action-cell i.pct {
	padding-right: 5px;
	cursor: pointer;
}

.action-column {
	width: 100px;
}

.dropdown-toggle::after {
	display: none;
}

.first-line > th {
	border-top: 0;
	text-align: left;
	text-transform: none;
	font-weight: bold;
}

.column-title {
	text-align: center;
	text-transform: none;
	font-weight: bold;
	font-size: 14px;
}

hr.solid {
	width: 92%;
	margin: 2px 8px;
	cursor: default !important;
}

.item-dropdown {
	padding: 8px;
	line-height: 1;
	color: $cinza05;
}

.item-dropdown:hover {
	color: $azulimPri;
	background: $cinzaPastel;
}

.body-section {
	background: #fafafa;
}

.status-control {
	font-size: 28px;
	margin-right: 12px;

	.pct {
		position: relative;
		top: 2px;
		cursor: pointer;
		font-size: 24px;
		color: $azulPactoPri;
	}
}

.cdk-drag-preview {
	display: inline-table;
	background-color: $branco;
	z-index: 2147483547 !important;
	font-size: 14px;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
		0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-preview td {
	padding: 0.75rem;
}

.cdk-drag-animating {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .body-table tr:not(.cdk-drag-placeholder) {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

pacto-cat-form-input::ng-deep {
	margin: 0px;

	input {
		padding: 0.25rem 0.5rem;
		line-height: 1.5rem;
	}

	.pct {
		display: none;
	}

	.nome {
		color: $pretoPri;
	}

	.nome,
	.pct-error-msg {
		min-height: 0.875rem;
		font-weight: 400;
		font-size: 0.875rem;
		line-height: 0.875rem;
	}
}
