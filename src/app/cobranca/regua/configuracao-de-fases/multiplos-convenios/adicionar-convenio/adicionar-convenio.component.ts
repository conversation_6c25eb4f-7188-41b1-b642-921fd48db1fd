import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-adicionar-convenio",
	templateUrl: "./adicionar-convenio.component.html",
	styleUrls: ["./adicionar-convenio.component.scss"],
})
export class AdicionarConvenioComponent implements OnInit {
	@Input() convenios: { value: number; label: string };
	@Output() convenioSelecionado: EventEmitter<number> =
		new EventEmitter<number>();
	public convenio: FormControl = new FormControl(null, Validators.required);

	constructor(
		private readonly openModal: NgbActiveModal,
		private readonly notify: SnotifyService
	) {}

	ngOnInit() {}

	public adicionar() {
		this.convenio.markAsTouched();
		if (this.convenio.valid) {
			this.convenioSelecionado.emit(parseInt(this.convenio.value, 0));
			this.openModal.close();
		} else {
			this.notify.info("Selecione um convênio para ser adicionado");
		}
	}

	public cancelar() {
		this.openModal.close();
	}
}
