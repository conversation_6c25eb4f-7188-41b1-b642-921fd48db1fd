import { Component, Input, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ModalService, PactoModalRef } from "@base-core/modal/modal.service";

import { SnotifyService } from "ng-snotify";
import { BUTTON_SIZE, BUTTON_TYPE } from "ui-kit";
import { ZwPactopayApiConfigService } from "zw-pactopay-api";

import { ModalNotificacaoComponent } from "src/app/cobranca/components/modal-notificacao/modal-notificacao.component";

@Component({
	selector: "pacto-comunicar-resultado-da-cobranca",
	templateUrl: "./comunicar-resultado-da-cobranca.component.html",
	styleUrls: ["./comunicar-resultado-da-cobranca.component.scss"],
})
export class ComunicarResultadoDaCobrancaComponent implements OnInit {
	@Input() form: FormGroup;
	@Input() config: any;
	public testeForm: FormGroup;
	public modelos: Array<{ label: string; value: string }>;

	constructor(
		private readonly formBuilder: FormBuilder,
		private readonly notify: SnotifyService,
		private readonly modalService: ModalService,
		private readonly zwPactopayApiConfig: ZwPactopayApiConfigService
	) {}

	public ngOnInit() {
		this.modelos = [
			{ value: "aprovada", label: "Cobrança aprovada" },
			{ value: "negada", label: "Cobrança negada" },
			{ value: "cancelada", label: "Cobrança cancelada" },
			{ value: "todas", label: "Todas as opções" },
		];

		this.testeForm = this.formBuilder.group({
			testar: this.formBuilder.control(false),
			email: this.formBuilder.control("", Validators.email),
			celular: this.formBuilder.control(""),
			modelo: this.formBuilder.control(null, Validators.required),
		});

		this.form.get("ativo").valueChanges.subscribe((ativo) => {
			ativo ? this.setValidators(this.form) : this.clearValidators(this.form);
		});
	}

	private setValidators(form: FormGroup) {
		const validationType = {
			sms: Validators.required,
			email: Validators.required,
		};

		for (const key of Object.keys(validationType)) {
			form.get(key).setValidators(validationType[key]);
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}

	private clearValidators(form: FormGroup) {
		for (const key in form.controls) {
			form.get(key).clearValidators();
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}

	public enviar(): void {
		this.testeForm.markAllAsTouched();
		if (this.testeForm.invalid) {
			this.notify.warning(
				"Verifique as informações digitadas e tente novamente"
			);
			return;
		}

		this.zwPactopayApiConfig
			.sendTesteDeResultadoDaCobranca(this.testeForm.value)
			.subscribe((res) => {
				const modalNotificacao: PactoModalRef = this.modalService.open(
					res.error ? "Erro" : "Sucesso",
					ModalNotificacaoComponent
				);
				if (res.error) {
					modalNotificacao.componentInstance.type = "pct-x";
					modalNotificacao.componentInstance.title = "Ops!";
					modalNotificacao.componentInstance.subtitle = res.message;
					modalNotificacao.componentInstance.actions = [
						{
							clickHandler: () => modalNotificacao.close(true),
							type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
							size: BUTTON_SIZE.LARGE,
							label: "Ok",
							full: true,
						},
					];
				} else {
					modalNotificacao.componentInstance.type = "pct-check";
					modalNotificacao.componentInstance.title = "Enviado!";
					modalNotificacao.componentInstance.subtitle =
						"Em instantes o teste chegará no e-mail e celular cadastrados";
					modalNotificacao.componentInstance.actions = [
						{
							clickHandler: () => modalNotificacao.close(true),
							type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
							size: BUTTON_SIZE.LARGE,
							label: "Ok",
							full: true,
						},
					];
				}
			});
	}

	public validaCelular(evt) {
		let tel = evt.target.value;
		tel = tel.replace(/\D/g, "");
		tel = tel.slice(0, 11);
		tel = tel.replace(/(\d{2})(\d{1})(\d{4})(\d{4})/, "($1) $2$3-$4");
		this.testeForm.get("celular").setValue(tel);
	}
}
