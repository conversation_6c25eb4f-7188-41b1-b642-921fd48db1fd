<div class="form-content">
	<h5><strong>Configurações de envio de resultado de cobrança</strong></h5>
	<br />

	<label class="form-check-label">Resultado da transação</label>
	<div class="tipoTransacao">
		<div class="form-check form-check-inline">
			<pacto-cat-checkbox
				[control]="form.controls.aprovada"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">Aprovada</label>
		</div>

		<div class="form-check form-check-inline">
			<pacto-cat-checkbox [control]="form.controls.negada"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">Negada</label>
		</div>

		<div class="form-check form-check-inline">
			<pacto-cat-checkbox
				[control]="form.controls.cancelada"></pacto-cat-checkbox>
			<label class="form-check-label" style="color: #51555a">Cancelada</label>
		</div>
	</div>

	<br />
	<div *ngIf="config?.email_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.email"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.email.value" class="pct pct-mail"></i>
			enviar por e-mail
		</label>
	</div>
	<div *ngIf="config?.sms_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.sms"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.sms.value" class="pct pct-tablet"></i>
			enviar por sms
		</label>
	</div>
	<div
		*ngIf="config?.whatsapp_configurado"
		class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.whatsapp"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i
				[class.checked]="form.controls.whatsapp.value"
				class="pct pct-whatsapp"></i>
			enviar por whatsapp
		</label>
	</div>
	<div *ngIf="config?.app_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.app"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.app.value" class="pct pct-tablet"></i>
			enviar por aplicativo
		</label>
	</div>
	<div
		class="form-check form-check-inline"
		*ngIf="config?.gymbotpro_configurado">
		<pacto-cat-checkbox
			[control]="form.controls.gymbotpro"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i
				class="pct pct-whatsapp"
				[class.checked]="form.controls.gymbotpro.value"></i>
			enviar por GymBotPro
		</label>
	</div>
</div>

<div class="form-content">
	<div style="align-items: center">
		<pacto-cat-checkbox
			[control]="testeForm.controls.testar"
			[label]="
				'Receber um teste antes de enviar para o cliente'
			"></pacto-cat-checkbox>
	</div>
</div>

<div *ngIf="testeForm.controls.testar.value" class="form-content form-row">
	<div class="col-6">
		<pacto-cat-form-input
			[control]="testeForm.controls.email"
			[errorMsg]="'Inválido!'"
			[label]="'Email'"
			[placeholder]="'<EMAIL>'"
			[type]="'email'"></pacto-cat-form-input>
	</div>
	<div class="col-3">
		<pacto-cat-form-input
			(keyup)="validaCelular($event)"
			[control]="testeForm.controls.celular"
			[errorMsg]="'Inválido!'"
			[label]="'Celular'"
			[placeholder]="'(62) 00000-0000'"
			[type]="'text'"></pacto-cat-form-input>
	</div>
	<div class="col-3">
		<pacto-cat-form-select
			[control]="testeForm.controls.modelo"
			[errorMsg]="'Inválido!'"
			[idKey]="'value'"
			[items]="modelos"
			[labelKey]="'label'"
			[label]="'Modelo de comunicação'"
			[size]="'SMALL'"></pacto-cat-form-select>
	</div>
	<div class="col-12">
		<pacto-cat-button
			(click)="enviar()"
			[label]="'Enviar'"
			[size]="'LARGE'"></pacto-cat-button>
	</div>
</div>
