@import "dist/ui-kit/assets/import.scss";

b,
strong {
	font-weight: bold;
}

.container {
	background-color: #eff2f7;
	width: 100%;
	padding: 4% 4% 0% 4%;
}

.form-regua {
	padding: 1rem;
	background-color: #fff;
}

.section-cobranca-automatica {
	display: flex;
	align-items: center;
	background-color: #fff;
	padding: 11px;

	.title-content {
		flex-grow: 1;
	}
}

pacto-cat-accordion::ng-deep {
	.accordion-wrapper {
		.header-section,
		.closed {
			border-radius: 0px;
			box-shadow: none;
		}
	}
}

.title-content {
	display: flex;

	pacto-cat-switch {
		padding-right: 0.875rem;
	}

	section {
		p {
			font-size: 0.875rem;
		}
	}
}

.btn {
	padding: 0.5rem 2.5rem;
	color: #fff;
	background-color: $azulim05;
}

.accordion-body-content {
	padding-top: 1.25rem;
	padding-bottom: 1.25rem;
	padding-left: calc(11px + 48px + 0.875rem);
	padding-right: calc(11px + 48px + 0.875rem);
}
