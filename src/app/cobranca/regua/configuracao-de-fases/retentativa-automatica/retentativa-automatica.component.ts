import { Component, Input, OnInit } from "@angular/core";
import { FormGroup, Validators } from "@angular/forms";

@Component({
	selector: "pacto-retentativa-automatica",
	templateUrl: "./retentativa-automatica.component.html",
	styleUrls: ["./retentativa-automatica.component.scss"],
})
export class RetentativaAutomaticaComponent implements OnInit {
	@Input() form: FormGroup;

	constructor() {}

	ngOnInit() {
		this.form.get("ativo").valueChanges.subscribe((ativo) => {
			ativo ? this.setValidators(this.form) : this.clearValidators(this.form);
		});
	}

	private setValidators(form: FormGroup) {
		const validationType = {
			limite_dias: [Validators.required, Validators.min(1), Validators.max(95)],
			intervalo_dias: [
				Validators.required,
				Validators.min(3),
				Validators.max(15),
			],
		};

		for (const key of Object.keys(validationType)) {
			form.get(key).setValidators(validationType[key]);
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}

	private clearValidators(form: FormGroup) {
		for (const key in form.controls) {
			form.get(key).clearValidators();
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}
}
