<div class="form-content">
	<div *ngIf="config?.email_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.email"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.email.value" class="pct pct-mail"></i>
			enviar por e-mail
		</label>
	</div>
	<div *ngIf="config?.sms_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.sms"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.sms.value" class="pct pct-tablet"></i>
			enviar por sms
		</label>
	</div>
	<div
		*ngIf="config?.whatsapp_configurado"
		class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.whatsapp"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i
				[class.checked]="form.controls.whatsapp.value"
				class="pct pct-whatsapp"></i>
			enviar por whatsapp
		</label>
	</div>
	<div *ngIf="config?.app_configurado" class="form-check form-check-inline">
		<pacto-cat-checkbox [control]="form.controls.app"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i [class.checked]="form.controls.app.value" class="pct pct-tablet"></i>
			enviar por aplicativo
		</label>
	</div>
	<div
		class="form-check form-check-inline"
		*ngIf="config?.gymbotpro_configurado">
		<pacto-cat-checkbox
			[control]="form.controls.gymbotpro"></pacto-cat-checkbox>
		<label class="form-check-label" style="color: #51555a">
			<i
				class="pct pct-whatsapp"
				[class.checked]="form.controls.gymbotpro.value"></i>
			enviar por GymBotPro
		</label>
	</div>
</div>

<br />

<div class="row">
	<div class="col-8 d-inline-flex align-items-center">
		<pacto-cat-checkbox
			[control]="form.controls.com_autorizacao"
			[label]="
				'Enviar apenas para quem tem autorização de cobrança automática'
			"></pacto-cat-checkbox>
	</div>
</div>

<div class="row">
	<div class="col-10 d-flex align-items-center">
		<span>
			Quanto tempo após o vencimento
			<br />
			<sub class="detalhe">Máximo 95 dias</sub>
		</span>
	</div>

	<div class="col-2">
		<pacto-cat-form-input
			[control]="form.controls.dias_vencido_minimo"
			[errorMsg]="'Inválido!'"
			[label]="'Dias'"
			[max]="'95'"
			[min]="'1'"
			[type]="'number'"></pacto-cat-form-input>
	</div>
</div>

<div class="row">
	<div class="col-10 d-flex align-items-center">
		<span>
			Limite máximo de dias para envio de parcelas em atraso
			<br />
			<sub class="detalhe">
				Exemplo: parcelas atrasadas em até 100 dias recebem o link de pagamento,
				acima de 100 dias já não recebem mais. Máximo de 250 dias.
			</sub>
		</span>
	</div>

	<div class="col-2">
		<pacto-cat-form-input
			[control]="form.controls.dias_vencido_maximo"
			[errorMsg]="'Inválido!'"
			[label]="'Dias'"
			[max]="250"
			[min]="limiteDeDiasParaEnvio"
			[type]="'number'"></pacto-cat-form-input>
	</div>
</div>

<div class="row">
	<div class="col-10 d-flex align-items-center">
		<span>
			Intervalo de envio
			<br />
			<sub class="detalhe">Mínimo 3 dias, máximo 30 dias</sub>
		</span>
	</div>

	<div class="col-2">
		<span></span>
		<pacto-cat-form-input
			[control]="form.controls.intervalo_dias"
			[errorMsg]="'Inválido!'"
			[label]="'Dias'"
			[max]="'30'"
			[min]="'3'"
			[type]="'number'"></pacto-cat-form-input>
	</div>
</div>

<div class="row">
	<div class="col-10 d-flex align-items-center">
		<span>
			Quantidade de envios
			<br />
			<sub class="detalhe">Mínimo 3 envios, máximo 5 envios</sub>
		</span>
	</div>

	<div class="col-2">
		<pacto-cat-form-input
			[control]="form.controls.quantidade_maxima_envios"
			[errorMsg]="'Inválido!'"
			[label]="'Qtd'"
			[max]="'5'"
			[min]="'3'"
			[type]="'number'"></pacto-cat-form-input>
	</div>
</div>
