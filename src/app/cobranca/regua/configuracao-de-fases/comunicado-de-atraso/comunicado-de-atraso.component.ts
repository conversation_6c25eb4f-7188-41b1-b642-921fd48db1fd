import { Component, Input, OnInit } from "@angular/core";
import { FormGroup, Validators } from "@angular/forms";

@Component({
	selector: "pacto-comunicado-de-atraso",
	templateUrl: "./comunicado-de-atraso.component.html",
	styleUrls: ["./comunicado-de-atraso.component.scss"],
})
export class ComunicadoDeAtrasoComponent implements OnInit {
	@Input() form: FormGroup;
	public limiteDeDiasParaEnvio = 0;
	@Input() config: any;

	constructor() {}

	ngOnInit() {
		this.form.get("ativo").valueChanges.subscribe((ativo) => {
			ativo ? this.setValidators(this.form) : this.clearValidators(this.form);
		});

		this.form
			.get("dias_vencido_minimo")
			.valueChanges.subscribe((v) => this.calcLimiteDeDiasParaEnvio());
		this.form
			.get("intervalo_dias")
			.valueChanges.subscribe((v) => this.calcLimiteDeDiasParaEnvio());
		this.form
			.get("quantidade_maxima_envios")
			.valueChanges.subscribe((v) => this.calcLimiteDeDiasParaEnvio());
	}

	private setValidators(form: FormGroup) {
		const validationType = {
			dias_vencido_minimo: [
				Validators.required,
				Validators.min(1),
				Validators.max(95),
			],
			dias_vencido_maximo: [Validators.required, Validators.min(1)],
			intervalo_dias: [
				Validators.required,
				Validators.min(3),
				Validators.max(30),
			],
			quantidade_maxima_envios: [
				Validators.required,
				Validators.min(3),
				Validators.max(5),
			],
		};

		for (const key of Object.keys(validationType)) {
			form.get(key).setValidators(validationType[key]);
			form.get(key).updateValueAndValidity();
		}
	}

	private clearValidators(form: FormGroup) {
		for (const key in form.controls) {
			form.get(key).clearValidators();
			if (key !== "ativo") {
				form.get(key).updateValueAndValidity();
			}
		}
	}

	private calcLimiteDeDiasParaEnvio() {
		const inicio = parseInt(this.form.get("dias_vencido_minimo").value || 0, 0);
		const intervalo = parseInt(this.form.get("intervalo_dias").value || 0, 0);
		const envios = parseInt(
			this.form.get("quantidade_maxima_envios").value || 0,
			0
		);
		const min = inicio + intervalo * (envios - 1) + 1;

		// console.log(`(${inicio} + (${intervalo} * (${envios} - 1))) + 1 = ${min}`);

		const limiteDeDiasParaEnvio = parseInt(
			this.form.get("dias_vencido_maximo").value,
			0
		);
		if (limiteDeDiasParaEnvio < min) {
			this.form.get("dias_vencido_maximo").setValue(min);
		} else if (limiteDeDiasParaEnvio > 250) {
			this.form.get("dias_vencido_maximo").setValue(250);
		}

		this.limiteDeDiasParaEnvio = min || 1;
	}
}
