<div class="container">
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'PactoPay / Régua de cobrança',
			menu: 'Configurações'
		}"></pacto-breadcrumbs>

	<form [formGroup]="form" class="form-regua">
		<section>
			<h5>
				<strong>Configurações</strong>
			</h5>
			<p style="font-size: 0.875rem">
				Para começar a enviar as comunicações da sua régua de cobrança
				precisaremos de realizar as configurações de SMS e e-mail.
			</p>
		</section>

		<pacto-cat-accordion *ngIf="config?.sms_configurado">
			<accordion-header>
				<h5><strong>Configurações de SMS</strong></h5>
			</accordion-header>
			<accordion-body>
				<section style="padding-top: 20px">
					<h5>
						<strong>Remetente de SMS</strong>
					</h5>
					<p style="font-size: 0.875rem">
						Informe o nome do remetente que será apresentado no começo do SMS
						enviado pela régua de cobrança.
					</p>
				</section>

				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.remetenteSMS"
							[errorMsg]="'Inválido!'"
							[label]="'Remetente SMS*'"
							[type]="'text'"></pacto-cat-form-input>
					</div>
				</div>

				<div class="d-flex justify-content-end">
					<pacto-cat-button
						(click)="salvarRemetenteSMS()"
						[full]="true"
						[label]="'Salvar'"
						[size]="'LARGE'"
						[type]="'PRIMARY'"></pacto-cat-button>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<br *ngIf="config?.email_configurado" />
		<pacto-cat-accordion *ngIf="config?.email_configurado">
			<accordion-header>
				<h5><strong>Configurações de e-mail</strong></h5>
			</accordion-header>
			<accordion-body>
				<section style="padding-top: 20px">
					<h5>
						<strong>
							{{ emailCriadoWagi ? "Alterar" : "Cadastrar" }} e-mail
						</strong>
					</h5>
					<p style="font-size: 0.875rem">
						Para começar a enviar as comunicações da sua régua de cobrança
						precisaremos que cadastre um e-mail, este e-mail será o remetente de
						tudo o que for enviado pela régua de cobrança.
					</p>
				</section>

				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.smtpLogin"
							[errorMsg]="'Inválido!'"
							[label]="'E-mail*'"
							[type]="'email'"></pacto-cat-form-input>
					</div>
					<div class="col-6">
						<pacto-cat-form-input
							[control]="confirmacao"
							[errorMsg]="'Inválido!'"
							[label]="'Confirmar e-mail*'"
							[type]="'email'"></pacto-cat-form-input>
					</div>
				</div>

				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.remetente"
							[errorMsg]="'Inválido!'"
							[label]="'Remetente*'"
							[type]="'text'"></pacto-cat-form-input>
					</div>
				</div>

				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-input-color
							[formControl]="form.controls.cssCorBtnComprovante"
							[readonly]="false">
							<label>Cor background button "Imprimir Comprovante"</label>
						</pacto-cat-input-color>
					</div>

					<div class="col-6">
						<pacto-cat-input-color
							[formControl]="form.controls.cssCorBtnCadastrar"
							[readonly]="false">
							<label>Cor background button "Cadastrar cartão"</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-input-color
							[formControl]="form.controls.cssCorEmail"
							[readonly]="false">
							<label>Cor background e-mail</label>
						</pacto-cat-input-color>
					</div>

					<div class="col-6">
						<pacto-cat-input-color
							[formControl]="form.controls.cssCorBtnPagar"
							[readonly]="false">
							<label>Cor background button "Pagar"</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="d-flex justify-content-end">
					<pacto-cat-button
						[label]="'Salvar Alterações Cores'"
						[full]="true"
						[size]="'LARGE'"
						[type]="'PRIMARY'"
						(click)="salvarAlteracoesCores()"
						style="margin-right: 15px"></pacto-cat-button>

					<pacto-cat-button
						(click)="criarEmail()"
						[full]="true"
						[label]="emailCriadoWagi ? 'Salvar alterações' : 'Cadastrar'"
						[size]="'LARGE'"
						[type]="'PRIMARY'"></pacto-cat-button>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<pacto-cat-accordion *ngIf="false">
			<accordion-header>
				<h5><strong>Configurações avançadas de e-mail</strong></h5>
			</accordion-header>
			<accordion-body>
				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.smtpPorta"
							[errorMsg]="'Inválido!'"
							[label]="'SMTP porta'"
							[type]="'text'"></pacto-cat-form-input>
					</div>
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.smtpServer"
							[errorMsg]="'Inválido!'"
							[label]="'SMTP server'"
							[type]="'text'"></pacto-cat-form-input>
					</div>
				</div>
				<div class="form-content row">
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.smtpEmail"
							[errorMsg]="'Inválido!'"
							[label]="'SMTP e-mail'"
							[type]="'text'"></pacto-cat-form-input>
					</div>
					<div class="col-6">
						<pacto-cat-form-input
							[control]="form.controls.smtpSenha"
							[errorMsg]="'Inválido!'"
							[label]="'SMTP senha'"
							[type]="'password'"></pacto-cat-form-input>
					</div>
				</div>

				<div class="col-12">
					<pacto-cat-switch
						[control]="form.controls.iniciarTLS"></pacto-cat-switch>
					<span [style.marginLeft]="'1rem'">Iniciar TLS</span>
				</div>
				<div class="col-12">
					<pacto-cat-switch
						[control]="form.controls.smtpConexaoSegura"></pacto-cat-switch>
					<span [style.marginLeft]="'1rem'">SMTP conexão segura</span>
				</div>
				<div class="col-12">
					<pacto-cat-switch
						[control]="form.controls.usaSMTPS"></pacto-cat-switch>
					<span [style.marginLeft]="'1rem'">Usa SMTPS</span>
				</div>
				<div class="col-12">
					<pacto-cat-switch [control]="form.controls.wagi"></pacto-cat-switch>
					<span [style.marginLeft]="'1rem'">Wagi</span>
				</div>
				<br />
				<div class="d-flex justify-content-end">
					<pacto-cat-button
						(click)="excluir()"
						*ngIf="temDados"
						[full]="true"
						[label]="'Excluir cadastro'"
						[size]="'LARGE'"
						[style.paddingRight]="'1rem'"
						[type]="'ACTION'"></pacto-cat-button>
					<pacto-cat-button
						(click)="salvar()"
						[disabled]="temDados && !formularioAlterado"
						[full]="true"
						[label]="temDados ? 'Salvar alterações' : 'Cadastrar'"
						[size]="'LARGE'"
						[type]="'PRIMARY'"></pacto-cat-button>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<br *ngIf="config?.whatsapp_configurado" />
		<pacto-cat-accordion *ngIf="config?.whatsapp_configurado">
			<accordion-header>
				<h5><strong>Configurações do GymBot (WhatsApp)</strong></h5>
			</accordion-header>
			<accordion-body>
				<section style="padding-top: 20px">
					<h5>
						<strong>URL Webhook</strong>
					</h5>
					<p style="font-size: 0.875rem">
						Informe a URL de webhook para comunicação com o GymBot.
					</p>
				</section>

				<div class="form-content row">
					<div class="col-12">
						<div class="divGeralInputUrlGymBot">
							<div class="divInputUrlGymBot">
								<pacto-cat-form-input
									[control]="form.controls.urlCobrancaAntecipada"
									[errorMsg]="'Inválido!'"
									[label]="'URL webhook comunicação cobrança antecipada'"
									[type]="'text'"></pacto-cat-form-input>
							</div>
							<div
								(click)="iniciarUrlCobrancaAntecipada()"
								[darkTheme]="true"
								[pactoCatTolltip]="'Enviar webhook teste'"
								class="divIconConfigGymBot">
								<i class="pct pct-refresh-cw"></i>
							</div>
						</div>
						<div class="divGeralInputUrlGymBot">
							<div class="divInputUrlGymBot">
								<pacto-cat-form-input
									[control]="form.controls.urlCobrancaPendente"
									[errorMsg]="'Inválido!'"
									[label]="'URL webhook comunicação parcela em atraso'"
									[type]="'text'"></pacto-cat-form-input>
							</div>
							<div
								(click)="iniciarUrlCobrancaPendente()"
								[darkTheme]="true"
								[pactoCatTolltip]="'Enviar webhook teste'"
								class="divIconConfigGymBot">
								<i class="pct pct-refresh-cw"></i>
							</div>
						</div>
						<div class="divGeralInputUrlGymBot">
							<div class="divInputUrlGymBot">
								<pacto-cat-form-input
									[control]="form.controls.urlCobrancaCartao"
									[errorMsg]="'Inválido!'"
									[label]="'URL webhook comunicação vencimento do cartão'"
									[type]="'text'"></pacto-cat-form-input>
							</div>
							<div
								(click)="iniciarUrlCobrancaCartao()"
								[darkTheme]="true"
								[pactoCatTolltip]="'Enviar webhook teste'"
								class="divIconConfigGymBot">
								<i class="pct pct-refresh-cw"></i>
							</div>
						</div>
						<div class="divGeralInputUrlGymBot">
							<div class="divInputUrlGymBot">
								<pacto-cat-form-input
									[control]="form.controls.urlCobrancaResultadoCobranca"
									[errorMsg]="'Inválido!'"
									[label]="'URL webhook comunicação resultado da cobrança'"
									[type]="'text'"></pacto-cat-form-input>
							</div>
							<div
								(click)="iniciarUrlCobrancaResultadoCobranca()"
								[darkTheme]="true"
								[pactoCatTolltip]="'Enviar webhook teste'"
								class="divIconConfigGymBot">
								<i class="pct pct-refresh-cw"></i>
							</div>
						</div>
					</div>
				</div>
				<div class="d-flex justify-content-end" style="gap: 30px">
					<pacto-cat-button
						(click)="salvarConfigGymBot()"
						[full]="true"
						[label]="'Salvar'"
						[size]="'LARGE'"
						[type]="'PRIMARY'"></pacto-cat-button>
				</div>
			</accordion-body>
		</pacto-cat-accordion>

		<br *ngIf="config?.app_configurado" />
		<pacto-cat-accordion *ngIf="config?.app_configurado">
			<accordion-header>
				<h5><strong>Configurações do Aplicativo</strong></h5>
			</accordion-header>
			<accordion-body>
				<!--				<section style='padding-top: 20px'>-->
				<!--					<h5>-->
				<!--						<strong>URL Webhook</strong>-->
				<!--					</h5>-->
				<!--					<p style='font-size: 0.875rem'>-->
				<!--						Informe a URL de webhook para comunicação com o GymBot.-->
				<!--					</p>-->
				<!--				</section>-->

				<div class="form-content row">
					<div class="col-12"></div>
				</div>
				<div class="d-flex justify-content-end" style="gap: 30px">
					<!--					<pacto-cat-button-->
					<!--						[label]="'Salvar'"-->
					<!--						[full]='true'-->
					<!--						[size]="'LARGE'"-->
					<!--						[type]="'PRIMARY'"-->
					<!--						(click)='salvarConfigGymBot()'-->
					<!--					></pacto-cat-button>-->
				</div>
			</accordion-body>
		</pacto-cat-accordion>
		<br *ngIf="config?.gymbotpro_configurado" />
		<pacto-cat-accordion *ngIf="config?.gymbotpro_configurado">
			<accordion-header>
				<h5><strong>Configurações do GymBotPro</strong></h5>
			</accordion-header>

			<accordion-body>
				<section style="padding-top: 20px">
					<h5>
						<strong>Token e Id da Integração</strong>
					</h5>
					<p style="font-size: 0.875rem">
						Informe o Token e o Id da Integração para comunicação com o
						GymBotPro.
					</p>
				</section>

				<div class="form-content row">
					<div class="col-12 divGeralInputUrlGymBotPro">
						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Token Comunicado Cobrança Antecipada'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.tokenCobrancaAntecipada"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Id Integração Comunicado Cobrança Antecipada'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.idIntegracaoCobrancaAntecipada"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Token Comunicado Resultado Cobrança'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.tokenComunicadoResultadoCobranca"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Id Integração Comunicado Resultado Cobrança'"
										[errorMsg]="'Inválido!'"
										[control]="
											form.controls.idIntegracaoComunicadoResultadoCobranca
										"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Token Comunicado Atraso'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.tokenComunicadoAtraso"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Id Integração Comunicado Atraso'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.idIntegracaoComunicadoAtraso"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Token Comunicado Cartão'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.tokenComunicadoCartao"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>

						<div class="col-6">
							<div class="divGeralInputUrlGymBot">
								<div class="divInputUrlGymBot">
									<pacto-cat-form-input
										[label]="'Id Integração Comunicado Cartão'"
										[errorMsg]="'Inválido!'"
										[control]="form.controls.idIntegracaoComunicadoCartao"
										[type]="'text'"></pacto-cat-form-input>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="d-flex justify-content-end" style="gap: 30px">
					<pacto-cat-button
						[label]="'Salvar'"
						[full]="true"
						[size]="'LARGE'"
						[type]="'PRIMARY'"
						(click)="salvarConfigGymBotPro()"></pacto-cat-button>
				</div>
			</accordion-body>
		</pacto-cat-accordion>
	</form>
</div>
