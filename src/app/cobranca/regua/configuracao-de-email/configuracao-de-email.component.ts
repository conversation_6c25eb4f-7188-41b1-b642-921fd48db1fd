import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import {
	AbstractControl,
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { SnotifyService } from "ng-snotify";
import {
	ConfiguracaoDeEmail,
	ZwPactopayApiConfigService,
} from "zw-pactopay-api";
import { CobrancaService } from "../../cobranca.service";
import { ConfirmacaoDeExclusaoComponent } from "./confirmacao-de-exclusao/confirmacao-de-exclusao.component";

@Component({
	selector: "pacto-configuracao-de-email",
	templateUrl: "./configuracao-de-email.component.html",
	styleUrls: ["./configuracao-de-email.component.scss"],
})
export class ConfiguracaoDeEmailComponent implements OnInit {
	private configuracaoDeEmail: ConfiguracaoDeEmail = null;
	public form: FormGroup;
	public confirmacao: FormControl;
	public temDados = false;
	public emailCriadoWagi = false;
	public formularioAlterado = false;
	public config: any;

	constructor(
		private readonly formBuilder: FormBuilder,
		private readonly notify: SnotifyService,
		private readonly cd: ChangeDetectorRef,
		private readonly zwPactopayApiConfig: ZwPactopayApiConfigService,
		private readonly cobrancaService: CobrancaService,
		private readonly sessionService: SessionService,
		private readonly modalService: ModalService
	) {}

	ngOnInit() {
		this.cobrancaService.definirTitle("PactoPay - Régua de cobrança");

		this.form = this.formBuilder.group({
			remetente: this.formBuilder.control("", Validators.required),
			smtpLogin: this.formBuilder.control("", [
				Validators.required,
				Validators.email,
			]),
			smtpPorta: this.formBuilder.control(null),
			smtpServer: this.formBuilder.control(""),
			iniciarTLS: this.formBuilder.control(false),
			smtpConexaoSegura: this.formBuilder.control(false),
			smtpEmail: this.formBuilder.control("", Validators.email),
			usaSMTPS: this.formBuilder.control(false),
			wagi: this.formBuilder.control(false),
			configuracaoEmailValida: this.formBuilder.control(false),
			smtpSenha: this.formBuilder.control(""),
			wagiEmail: this.formBuilder.control("", Validators.email),
			remetenteSMS: this.formBuilder.control("", [
				Validators.required,
				Validators.maxLength(10),
			]),
			urlCobrancaAntecipada: this.formBuilder.control("", [
				Validators.required,
				Validators.minLength(10),
			]),
			urlCobrancaPendente: this.formBuilder.control("", [
				Validators.required,
				Validators.minLength(10),
			]),
			urlCobrancaCartao: this.formBuilder.control("", [
				Validators.required,
				Validators.minLength(10),
			]),
			urlCobrancaResultadoCobranca: this.formBuilder.control("", [
				Validators.required,
				Validators.minLength(10),
			]),
			cssCorEmail: this.formBuilder.control(""),
			cssCorBtnPagar: this.formBuilder.control(""),
			cssCorBtnComprovante: this.formBuilder.control(""),
			cssCorBtnCadastrar: this.formBuilder.control(""),
			tokenCobrancaAntecipada: this.formBuilder.control(
				"",
				Validators.required
			),
			idIntegracaoCobrancaAntecipada: this.formBuilder.control(
				"",
				Validators.required
			),
			tokenComunicadoResultadoCobranca: this.formBuilder.control(
				"",
				Validators.required
			),
			idIntegracaoComunicadoResultadoCobranca: this.formBuilder.control(
				"",
				Validators.required
			),
			tokenComunicadoAtraso: this.formBuilder.control("", Validators.required),
			idIntegracaoComunicadoAtraso: this.formBuilder.control(
				"",
				Validators.required
			),
			tokenComunicadoCartao: this.formBuilder.control("", Validators.required),
			idIntegracaoComunicadoCartao: this.formBuilder.control(
				"",
				Validators.required
			),
		});

		this.confirmacao = this.formBuilder.control("", Validators.required);

		this.obterConfiguracoes();
	}

	public get isPermissaoParaAvancado(): boolean {
		return this.sessionService.loggedUser.username === "pactobr";
	}

	private obterConfiguracoes(): void {
		this.zwPactopayApiConfig.obterConfiguracoes().subscribe(
			(res) => {
				if (res.content) {
					this.config = res.content;
					this.form.patchValue(res.content.configuracao_email);
					this.form
						.get("urlCobrancaAntecipada")
						.setValue(res.content.configuracao_gymbot.urlCobrancaAntecipada);
					this.form
						.get("urlCobrancaPendente")
						.setValue(res.content.configuracao_gymbot.urlCobrancaPendente);
					this.form
						.get("urlCobrancaCartao")
						.setValue(res.content.configuracao_gymbot.urlCobrancaCartao);
					this.form
						.get("urlCobrancaResultadoCobranca")
						.setValue(
							res.content.configuracao_gymbot.urlCobrancaResultadoCobranca
						);
					this.form
						.get("tokenCobrancaAntecipada")
						.setValue(
							res.content.configuracao_gymbotpro.tokenCobrancaAntecipada
						);
					this.form
						.get("idIntegracaoCobrancaAntecipada")
						.setValue(
							res.content.configuracao_gymbotpro.idIntegracaoCobrancaAntecipada
						);
					this.form
						.get("tokenComunicadoResultadoCobranca")
						.setValue(
							res.content.configuracao_gymbotpro
								.tokenComunicadoResultadoCobranca
						);
					this.form
						.get("idIntegracaoComunicadoResultadoCobranca")
						.setValue(
							res.content.configuracao_gymbotpro
								.idIntegracaoComunicadoResultadoCobranca
						);
					this.form
						.get("tokenComunicadoAtraso")
						.setValue(res.content.configuracao_gymbotpro.tokenComunicadoAtraso);
					this.form
						.get("idIntegracaoComunicadoAtraso")
						.setValue(
							res.content.configuracao_gymbotpro.idIntegracaoComunicadoAtraso
						);
					this.form
						.get("tokenComunicadoCartao")
						.setValue(res.content.configuracao_gymbotpro.tokenComunicadoCartao);
					this.form
						.get("idIntegracaoComunicadoCartao")
						.setValue(
							res.content.configuracao_gymbotpro.idIntegracaoComunicadoCartao
						);
					this.confirmacao.setValue(res.content.configuracao_email.smtpLogin);
					this.configuracaoDeEmail = res.content.configuracao_email;
					this.emailCriadoWagi = this.configuracaoDeEmail.emailWagiCriado;

					if (
						res.content.configuracao_email.remetente &&
						res.content.configuracao_email.smtpLogin
					) {
						this.temDados = true;
						this.observarAlteracao();
					} else {
						this.temDados = false;
					}
					this.cd.detectChanges();
				} else {
					this.notify.error(res.meta.message);
				}
			},
			(error) => {
				this.notify.error(
					"Não foi possível recuperar os dados, tente novamente"
				);
				console.error(error);
			}
		);
	}

	public salvar() {
		this.form.markAllAsTouched();
		this.confirmacao.markAsTouched();

		if (this.form.valid && this.confirmacao.valid) {
			if (this.form.get("smtpLogin").value !== this.confirmacao.value) {
				this.notify.error(`A confirmação não confere com o e-mail informado`);
				return;
			}
			this.zwPactopayApiConfig
				.salvarConfiguracaoDeEmail(
					this.form.value,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe(
					(res) => {
						if (res.content) {
							if (this.temDados) {
								this.notify.success(
									"Alteração de e-mail realizada com sucesso !!"
								);
							} else {
								this.notify.success("E-mail cadastrado com sucesso !!");
							}
							this.obterConfiguracoes();
						} else {
							this.notify.error(res.meta.message);
						}

						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.notify.error(httpErrorResponse.error.meta.message);
					}
				);
		} else {
			// this.procuraErro(this.form.controls);
			this.notify.error("É necessário preencher todos os campos");
		}
	}

	public excluir(): void {
		const modal = this.modalService.open(
			"Excluir e-mail",
			ConfirmacaoDeExclusaoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.confirmacao.subscribe((confirmado) => {
			if (confirmado) {
				const vazio: ConfiguracaoDeEmail = {
					remetente: "",
					smtpLogin: "",
					smtpPorta: null,
					smtpServer: "",
					iniciarTLS: false,
					smtpConexaoSegura: false,
					smtpEmail: "",
					usaSMTPS: false,
					wagi: false,
					configuracaoEmailValida: false,
					smtpSenha: "",
					wagiEmail: "",
					remetenteSMS: "",
					appKeyWagiCriado: "",
					emailWagiCriado: false,
					cssCorEmail: "",
					cssCorBtnPagar: "",
					cssCorBtnComprovante: "",
					cssCorBtnCadastrar: "",
				};
				this.zwPactopayApiConfig
					.salvarConfiguracaoDeEmail(vazio, this.sessionService.codigoUsuarioZw)
					.subscribe(
						(res) => {
							if (res.content) {
								this.form.reset();
								this.confirmacao.reset();
								this.notify.success("E-mail excluído com sucesso !!");
								this.obterConfiguracoes();
							} else {
								this.notify.error(res.meta.message);
							}

							this.cd.detectChanges();
						},
						(httpErrorResponse) => {
							this.notify.error(httpErrorResponse.error.meta.message);
						}
					);
			}
		});
	}

	private procuraErro(controls: { [key: string]: AbstractControl }) {
		const erros: string[] = [];
		for (const name in controls) {
			if (controls[name].invalid) {
				switch (name) {
					case "smtpLogin":
						erros.push("E-mail");
						break;
					case "remetente":
						erros.push("Remetente");
						break;
					case "remetenteSMS":
						erros.push("Remetente SMS");
						break;
					case "smtpPorta":
						erros.push("SMTP porta");
						break;
					case "smtpServer":
						erros.push("SMTP server");
						break;
					case "smtpEmail":
						erros.push("SMTP e-mail");
						break;
					case "smtpSenha":
						erros.push("SMTP senha");
						break;
					case "iniciarTLS":
						erros.push("Iniciar TLS");
						break;
					case "smtpConexaoSegura":
						erros.push("SMTP conexão segura");
						break;
					case "usaSMTPS":
						erros.push("Usa SMTPS");
						break;
					case "wagi":
						erros.push("Wagi");
						break;
				}
			}
		}

		if (this.confirmacao.invalid) {
			erros.push("Confirmar e-mail");
		}

		// Usar new Intl.ListFormat('pt-BR', { style: 'long', type: 'conjunction' }).format(erros) em vez de erros.toString().replace(/,/g, ', ') quando fizer update da versão do angular
		const plural = erros.length > 1;
		this.notify.error(
			`Campo${plural ? "s" : ""} ${erros
				.toString()
				.replace(/,/g, ", ")} inválido${plural ? "s" : ""}!`
		);
	}

	private observarAlteracao(): void {
		this.form.valueChanges.subscribe((controls) => {
			for (const controlName of Object.keys(controls)) {
				if (
					`${this.configuracaoDeEmail[controlName]}` !==
					`${controls[controlName]}`
				) {
					this.formularioAlterado = true;
					this.confirmacao.reset();
					break;
				} else {
					this.formularioAlterado = false;
				}
			}
		});
	}

	public criarEmail() {
		const remetente = this.form.get("remetente").value;
		const smtpLogin = this.form.get("smtpLogin").value;
		if (remetente.length === 0) {
			this.notify.error("Informe o remetente do e-mail");
			return;
		}
		if (this.form.get("smtpLogin").value !== this.confirmacao.value) {
			this.notify.error(`A confirmação não confere com o e-mail informado`);
			return;
		}
		this.zwPactopayApiConfig
			.criarEmail(
				this.sessionService.codigoEmpresa,
				smtpLogin,
				remetente,
				this.sessionService.codigoUsuarioZw
			)
			.subscribe(
				(res) => {
					if (res.content) {
						if (this.emailCriadoWagi) {
							this.notify.success("E-mail alterado com sucesso!");
						} else {
							this.notify.success("E-mail criado com sucesso!");
						}
						this.obterConfiguracoes();
					} else {
						console.log(res);
						this.notify.error(res.meta.message);
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.notify.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	public salvarRemetenteSMS() {
		const remetenteSMS = this.form.get("remetenteSMS").value;
		if (remetenteSMS.length === 0) {
			this.notify.error("Informe o remetente do SMS");
			return;
		}
		if (remetenteSMS.length > 10) {
			this.notify.error("O remetente do SMS é de no máximo 10 caracteres");
			return;
		}
		this.zwPactopayApiConfig
			.alterarConfigSMS(
				this.sessionService.codigoEmpresa,
				remetenteSMS,
				this.sessionService.codigoUsuarioZw
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.notify.success("Remetente do SMS alterado com sucesso!");
						this.obterConfiguracoes();
					} else {
						console.log(res);
						this.notify.error(res.meta.message);
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.notify.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	public configuracaoInicialGymBot() {
		const urlCobrancaAntecipada = this.form.get("urlCobrancaAntecipada").value;
		const urlCobrancaPendente = this.form.get("urlCobrancaPendente").value;
		const urlCobrancaCartao = this.form.get("urlCobrancaCartao").value;
		if (
			!this.form.get("urlCobrancaAntecipada").valid &&
			!this.form.get("urlCobrancaPendente").valid &&
			!this.form.get("urlCobrancaCartao").valid
		) {
			this.notify.error("Informe pelo menos uma url");
			return;
		}

		const body = {
			urlCobrancaAntecipada,
			urlCobrancaPendente,
			urlCobrancaCartao,
		};
		this.zwPactopayApiConfig
			.configuracaoInicialGymBot(
				this.sessionService.codigoEmpresa,
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.notify.success("Remetente do SMS alterado com sucesso!");
						this.obterConfiguracoes();
					} else {
						console.log(res);
						this.notify.error(res.meta.message);
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.notify.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	public salvarConfigGymBot() {
		const urlCobrancaAntecipada = this.form.get("urlCobrancaAntecipada").value;
		const urlCobrancaPendente = this.form.get("urlCobrancaPendente").value;
		const urlCobrancaCartao = this.form.get("urlCobrancaCartao").value;
		const urlCobrancaResultadoCobranca = this.form.get(
			"urlCobrancaResultadoCobranca"
		).value;

		const body = {
			usuario: this.sessionService.codigoUsuarioZw,
			username: this.sessionService.loggedUser.username,
			urlCobrancaAntecipada,
			urlCobrancaPendente,
			urlCobrancaCartao,
			urlCobrancaResultadoCobranca,
		};

		this.zwPactopayApiConfig
			.alterarConfigGymBot(
				this.sessionService.codigoEmpresa,
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.notify.success("Configuração GymBot gravada com sucesso!");
						this.obterConfiguracoes();
					} else {
						console.log(res);
						this.notify.error(res.meta.message);
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.notify.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	iniciarUrlCobrancaAntecipada() {
		const urlCobrancaAntecipada = this.form.get("urlCobrancaAntecipada").value;
		this.iniciarUrlGymBot(urlCobrancaAntecipada);
	}

	iniciarUrlCobrancaPendente() {
		const urlCobrancaPendente = this.form.get("urlCobrancaPendente").value;
		this.iniciarUrlGymBot(urlCobrancaPendente);
	}

	iniciarUrlCobrancaCartao() {
		const urlCobrancaCartao = this.form.get("urlCobrancaCartao").value;
		this.iniciarUrlGymBot(urlCobrancaCartao);
	}

	iniciarUrlCobrancaResultadoCobranca() {
		const urlCobrancaResultadoCobranca = this.form.get(
			"urlCobrancaResultadoCobranca"
		).value;
		this.iniciarUrlGymBot(urlCobrancaResultadoCobranca);
	}

	iniciarUrlGymBot(url) {
		if (url.length < 10) {
			this.notify.error("Informe a URL");
			return;
		}
		const body = {
			usuario: this.sessionService.codigoUsuarioZw,
			username: this.sessionService.loggedUser.username,
			url,
		};
		this.zwPactopayApiConfig
			.configuracaoInicialGymBot(
				this.sessionService.codigoEmpresa,
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.notify.success("Configuração enviada para GymBot!");
					} else {
						console.log(res);
						this.notify.error(res.meta.message);
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.notify.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	salvarAlteracoesCores() {
		const cssCorEmail = this.form.get("cssCorEmail").value;
		const cssCorBtnPagar = this.form.get("cssCorBtnPagar").value;
		const cssCorBtnComprovante = this.form.get("cssCorBtnComprovante").value;
		const cssCorBtnCadastrar = this.form.get("cssCorBtnCadastrar").value;

		const body = {
			usuario: this.sessionService.codigoUsuarioZw,
			username: this.sessionService.loggedUser.username,
			cssCorEmail,
			cssCorBtnPagar,
			cssCorBtnComprovante,
			cssCorBtnCadastrar,
		};

		this.zwPactopayApiConfig
			.salvarAlteracoesCores(
				this.sessionService.codigoEmpresa,
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.notify.success("Cores do e-mail alteradas com sucesso!");
						this.obterConfiguracoes();
					} else {
						console.log(res);
						this.notify.error(res.meta.message);
					}
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					this.notify.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	public salvarConfigGymBotPro() {
		const tokenCobrancaAntecipada = this.form.get(
			"tokenCobrancaAntecipada"
		).value;
		const idIntegracaoCobrancaAntecipada = this.form.get(
			"idIntegracaoCobrancaAntecipada"
		).value;
		const tokenComunicadoResultadoCobranca = this.form.get(
			"tokenComunicadoResultadoCobranca"
		).value;
		const idIntegracaoComunicadoResultadoCobranca = this.form.get(
			"idIntegracaoComunicadoResultadoCobranca"
		).value;
		const tokenComunicadoAtraso = this.form.get("tokenComunicadoAtraso").value;
		const idIntegracaoComunicadoAtraso = this.form.get(
			"idIntegracaoComunicadoAtraso"
		).value;
		const tokenComunicadoCartao = this.form.get("tokenComunicadoCartao").value;
		const idIntegracaoComunicadoCartao = this.form.get(
			"idIntegracaoComunicadoCartao"
		).value;

		const body = {
			usuario: this.sessionService.codigoUsuarioZw,
			username: this.sessionService.loggedUser.username,
			tokenCobrancaAntecipada,
			idIntegracaoCobrancaAntecipada,
			tokenComunicadoResultadoCobranca,
			idIntegracaoComunicadoResultadoCobranca,
			tokenComunicadoAtraso,
			idIntegracaoComunicadoAtraso,
			tokenComunicadoCartao,
			idIntegracaoComunicadoCartao,
		};

		if (this.validateBodyForGymbotPro(body)) {
			this.zwPactopayApiConfig
				.alterarConfigGymBotPro(
					this.sessionService.codigoEmpresa,
					this.sessionService.codigoUsuarioZw,
					body
				)
				.subscribe(
					(res) => {
						if (res.content) {
							this.notify.success(
								"Configuração GymBotPro gravada com sucesso!"
							);
							this.obterConfiguracoes();
						} else {
							console.log(res);
							this.notify.error(res.meta.message);
						}
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.notify.error(httpErrorResponse.error.meta.message);
					}
				);
		}
	}

	private validateBodyForGymbotPro(body): boolean {
		const fields = [
			{
				token: "tokenCobrancaAntecipada",
				id: "idIntegracaoCobrancaAntecipada",
			},
			{
				token: "tokenComunicadoResultadoCobranca",
				id: "idIntegracaoComunicadoResultadoCobranca",
			},
			{ token: "tokenComunicadoAtraso", id: "idIntegracaoComunicadoAtraso" },
			{ token: "tokenComunicadoCartao", id: "idIntegracaoComunicadoCartao" },
		];

		let isValid = true;

		fields.forEach(({ token, id }) => {
			const tokenValue = body[token as keyof typeof body];
			const idValue = body[id as keyof typeof body];

			var tokenLabel = "";
			var idLabel = "";

			if ((tokenValue && !idValue) || (!tokenValue && idValue)) {
				// debugger;

				if (
					token == "tokenCobrancaAntecipada" ||
					id == "idIntegracaoCobrancaAntecipada"
				) {
					tokenLabel = "Token Comunicado Cobrança Antecipada";
					idLabel = "Id Integração Comunicado Cobrança Antecipada";
				} else if (
					token == "tokenComunicadoResultadoCobranca" ||
					id == "idIntegracaoComunicadoResultadoCobranca"
				) {
					tokenLabel = "Token Comunicado Resultado Cobrança";
					idLabel = "Id Integração Comunicado Resultado Cobrança";
				} else if (
					token == "tokenComunicadoAtraso" ||
					id == "idIntegracaoComunicadoAtraso"
				) {
					tokenLabel = "Token Comunicado Atraso";
					idLabel = "Id Integração Comunicado Atraso";
				} else if (
					token == "tokenComunicadoCartao" ||
					id == "idIntegracaoComunicadoCartao"
				) {
					tokenLabel = "Token Comunicado Cartão";
					idLabel = "Id Integração Comunicado Cartão";
				}

				this.notify.error(
					`O campo "${tokenLabel}" e "${idLabel}" devem estar ambos preenchidos para prosseguir.`
				);
				isValid = false;
			}
		});

		return isValid;
	}
}
