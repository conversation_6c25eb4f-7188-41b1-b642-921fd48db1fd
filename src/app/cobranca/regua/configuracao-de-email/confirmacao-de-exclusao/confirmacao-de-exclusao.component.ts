import { Component, EventEmitter, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-confirmacao-de-exclusao",
	templateUrl: "./confirmacao-de-exclusao.component.html",
})
export class ConfirmacaoDeExclusaoComponent implements OnInit {
	public readonly confirmacao: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	constructor(private readonly activeModal: NgbActiveModal) {}

	ngOnInit() {}

	public excluir(): void {
		this.confirmacao.emit(true);
		this.activeModal.dismiss();
	}

	public cancelar(): void {
		this.activeModal.dismiss();
	}
}
