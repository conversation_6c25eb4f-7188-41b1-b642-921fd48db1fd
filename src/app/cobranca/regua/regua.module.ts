import { DragDropModule } from "@angular/cdk/drag-drop";
import { CommonModule, DeprecatedI18NPipesModule } from "@angular/common";
import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";

import { ConfiguracaoDeEmailComponent } from "./configuracao-de-email/configuracao-de-email.component";
import { ComunicadoDeAtrasoComponent } from "./configuracao-de-fases/comunicado-de-atraso/comunicado-de-atraso.component";
import { ComunicarResultadoDaCobrancaComponent } from "./configuracao-de-fases/comunicar-resultado-da-cobranca/comunicar-resultado-da-cobranca.component";
import { ComunicarVencimentoDoCartaoComponent } from "./configuracao-de-fases/comunicar-vencimento-do-cartao/comunicar-vencimento-do-cartao.component";
import { ConfiguracaoDeFasesComponent } from "./configuracao-de-fases/configuracao-de-fases.component";
import { EnvioAntecipadoDeCobrancaComponent } from "./configuracao-de-fases/envio-antecipado-de-cobranca/envio-antecipado-de-cobranca.component";
import { AdicionarConvenioComponent } from "./configuracao-de-fases/multiplos-convenios/adicionar-convenio/adicionar-convenio.component";
import { MultiplosConveniosComponent } from "./configuracao-de-fases/multiplos-convenios/multiplos-convenios.component";
import { RetentativaAutomaticaComponent } from "./configuracao-de-fases/retentativa-automatica/retentativa-automatica.component";
import { DashboardComponent } from "./dashboard/dashboard.component";
import { ReguaRoutingModule } from "./regua-routing.module";
import { ConfirmacaoDeExclusaoComponent } from "./configuracao-de-email/confirmacao-de-exclusao/confirmacao-de-exclusao.component";
import { ModalListaPessoasReguaComponent } from "./dashboard/modal-lista-pessoas-regua/modal-lista-pessoas-regua.component";
import { CatTolltipModule } from "ui-kit";

@NgModule({
	declarations: [
		ConfiguracaoDeFasesComponent,
		EnvioAntecipadoDeCobrancaComponent,
		ComunicarResultadoDaCobrancaComponent,
		RetentativaAutomaticaComponent,
		ComunicadoDeAtrasoComponent,
		ComunicarVencimentoDoCartaoComponent,
		MultiplosConveniosComponent,
		AdicionarConvenioComponent,
		DashboardComponent,
		ConfiguracaoDeEmailComponent,
		ConfirmacaoDeExclusaoComponent,
		ModalListaPessoasReguaComponent,
	],
	imports: [
		CommonModule,
		ReguaRoutingModule,
		BaseSharedModule,
		DragDropModule,
		DeprecatedI18NPipesModule,
		CatTolltipModule,
	],
	entryComponents: [
		AdicionarConvenioComponent,
		ConfirmacaoDeExclusaoComponent,
		ModalListaPessoasReguaComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class ReguaModule {}
