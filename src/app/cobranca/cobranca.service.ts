import { Injectable } from "@angular/core";
import { Title } from "@angular/platform-browser";

@Injectable({
	providedIn: "root",
})
export class CobrancaService {
	favIcon: HTMLLinkElement = document.querySelector("#favicon");

	constructor(private titleService: Title) {}

	definirTitle(title: string) {
		this.titleService.setTitle(title);
		this.favIcon.href = "assets/images/logos/pct-icone-fundo-pactopay.svg";
	}

	definirTitleTreino(title: string) {
		this.titleService.setTitle(title);
		this.favIcon.href = "assets/images/logos/pct-icone-fundo-pacto-branco.svg";
	}
}
