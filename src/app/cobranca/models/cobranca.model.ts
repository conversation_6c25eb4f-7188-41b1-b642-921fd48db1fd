import { Parc<PERSON> } from "./parcela.model";

export interface Cobranca {
	tipo: "TRANSAÇÃO" | "REMESSA";
	identificador: string;
	cartao: string;
	transacao: number;
	remessaItem: number;
	descricaoRetorno: string;
	data: string;
	convenio: string;
	codConvenio: number;
	codigoRetorno: string;
	status: string;
	codigo: string;
	autorizacao: string;
	nsu: string;
	valor: number;
	qtd_parcelas: number;
	parcelamento: number;
	titular: string;
	bandeira: string;
	recibo: string;
	tipoOperacao: string;
	usuario: string;
	remessa: string;
	id: string;
	statusCodigo: number;
	podeCancelar: boolean;
	podeSincronizar: boolean;
	podeVisualizarDetalhe: boolean;
	ambiente: number;
	tipo_cobranca?: number;
	origem?: string;
	situacao?: number;
	situacao_descricao?: string;
	data_cancelamento?: string;
	transacaoVerificarCartao?: boolean;
	movParcelas?: Parcela[];
	hintExibir?: string;
}
