export interface Parcela {
	cliente?: number;
	codigo?: number;
	descricao?: string;
	juros?: number;
	matricula?: string;
	nome?: string;
	nrTentativas?: number;
	pessoa?: number;
	situacao?: string;
	situacaoApresentar?: string;
	situacaoDescricao?: string;
	valorParcela?: number;
	valorTotal?: number;
	vencimento?: string;
	dataRegistro?: string;
	contrato?: number;
	vendaAvulsa?: number;
	recibo?: number;
	usuarioPagamento?: string;
	tipoOperacao?: string;
	formaPagamento?: string;
	plano?: string;
	empresa?: string;
	inicioContrato?: string;
	fimContrato?: string;
	status?: string;
	lancamento?: string;
	recebimento?: string;
	recebido?: string;
	pagador?: string;
	data?: string;
}
