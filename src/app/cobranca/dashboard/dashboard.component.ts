import { HttpParams } from "@angular/common/http";
import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import {
	ModalService,
	PactoModalRef,
	PactoModalSize,
} from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { ApexOptions } from "apexcharts";
import * as moment from "moment";
import { DadosDoFiltro, Filtro, PactoPayApiDashService } from "pactopay-api";
import { PactoColor, TraducoesXinglingComponent } from "ui-kit";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { CobrancaService } from "../cobranca.service";
import { DetalhamentoDeCreditoComponent } from "../components/detalhamento-de-credito/detalhamento-de-credito.component";
import { PayChartComponent } from "../components/pay-chart/pay-chart.component";
import {
	PactoDataGridConfig,
	PactoDataGridConfigDto,
} from "../components/relatorio-cobranca/data-grid.model";
import { RelatorioCobrancaComponent } from "../components/relatorio-cobranca/relatorio-cobranca.component";
import { Indicadores, InfoGenerico } from "./dashboard.models";
import {
	ModalRelatorioColunaConfig,
	ModalRelatorioConfig,
	relatorios,
} from "./relatorios.config";

import ResizeObserver from "resize-observer-polyfill";
@Component({
	selector: "dashboard",
	templateUrl: "./dashboard.component.html",
	styleUrls: ["./dashboard.component.scss", "../cobranca.scss"],
})
export class DashboardComponent implements OnInit, OnDestroy {
	public filtro: Filtro;
	public filtroInfo: DadosDoFiltro;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	observer;
	@ViewChild("graficoEficiencaTentativas", { static: true })
	viewGraficoEficienciaPorTentativa: PayChartComponent;
	@ViewChild("graficoRelatorioCodigoInterno", { static: true })
	viewGraficoRelatorioDeCodigoDeRetorno: PayChartComponent;
	@ViewChild("graficoEnviosConvenio", { static: true })
	viewGraficoEnviosPorConvenio: PayChartComponent;

	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("celulaNomeAluno", { static: true }) celulaNomeAluno;
	@ViewChild("celulaStatus", { static: true }) celulaStatus;
	@ViewChild("celulaTelefone", { static: true }) celulaTelefone;
	@ViewChild("celulaSituacao", { static: true }) celulaSituacao;
	@ViewChild("celulaValor", { static: true }) celulaValor;
	@ViewChild("celulaSituacaoParcela", { static: true }) celulaSituacaoParcela;

	public relatorio: RelatorioCobrancaComponent;
	public config: PactoDataGridConfigDto;
	public infoIndicadores: Array<Indicadores>;
	public infoCartao: Array<InfoGenerico>;
	public infoParcelas: Array<InfoGenerico>;
	public resumoAluno: Array<InfoGenerico>;
	public ultimaExecucao: string;
	public creditoPacto: number;
	public endpointShare: string;
	public modalRef: PactoModalRef;
	public valorTpv: number;
	public descricaoCredito: string;
	public utilizados: boolean;

	public graficoIndiceDeCobranca: Partial<any> = {
		type: "serial",
		dataProvider: [],
	};
	// public graficoIndiceDeCobranca: Partial<ApexOptions> = {};
	public graficoEficienciaPorTentativa: Partial<ApexOptions> = {};
	public graficoRelatorioDeCodigoDeRetorno: Partial<ApexOptions> = {};
	public graficoEnviosPorConvenio: Partial<ApexOptions> = {};

	constructor(
		private pactoPayApiDash: PactoPayApiDashService,
		private modal: ModalService,
		private rest: RestService,
		private zwPactoPayApiDash: ZwPactoPayApiDashService,
		private cd: ChangeDetectorRef,
		private router: Router,
		private service: CobrancaService
	) {}

	ngOnInit() {
		this.service.definirTitle("Sistema Pacto");
		this.zwPactoPayApiDash
			.notificarRecursoEmpresa("DASHBOARD_PACTO_PAY")
			.subscribe(console.log);

		const dashboard = document.getElementById("dashboard");
		this.observer = new ResizeObserver(() => {
			this.viewGraficoEficienciaPorTentativa.updateOptions(
				this.graficoEficienciaPorTentativa
			);
			this.viewGraficoRelatorioDeCodigoDeRetorno.updateOptions(
				this.graficoRelatorioDeCodigoDeRetorno
			);
			this.viewGraficoEnviosPorConvenio.updateOptions(
				this.graficoEnviosPorConvenio
			);
		});

		this.observer.observe(dashboard);
	}

	ngOnDestroy() {
		this.service.definirTitleTreino("Sistema Pacto");
		const dashboard = document.getElementById("dashboard");
		this.observer.unobserve(dashboard);
	}

	public filtrar(event) {
		this.filtro = event.filtro;
		this.filtroInfo = event.dados;

		this.pactoPayApiDash
			.obterIndiceDeCobranca(this.filtro)
			.subscribe((result) => {
				this.criarGraficoIndiceDeCobranca({ dataProvider: result });
				this.cd.detectChanges();
			});

		this.pactoPayApiDash
			.obterEfecienciaPorTentativa(this.filtro)
			.subscribe((result) => {
				const categories = result.categories;
				const series = [];
				series.push({
					data: result.data,
					name: result.categories,
				});

				this.criarGraficoEficienciaPorTentativa({ series, categories });
				this.cd.detectChanges();
			});

		this.zwPactoPayApiDash.obterInfoRetorno(this.filtro).subscribe((res) => {
			this.criarGraficoRelatorioDeCodigoDeRetorno(res);
			this.cd.detectChanges();
		});

		this.pactoPayApiDash
			.obterInfoDeEnviosPorConvenio(this.filtro)
			.subscribe((result) => {
				this.valorTpv = 0;
				const series = [];
				const labels = [];
				result.forEach((item) => {
					series.push(item.aprovadoQtd + item.naoAprovadoQtd);
					labels.push(item.tipoConvenioDescricao + ";" + item.convenioCobranca);
					this.valorTpv = this.valorTpv + item.aprovadoValor;
				});

				this.criarGraficoEnviosPorConvenio({ series, labels });
				this.cd.detectChanges();
			});

		this.pactoPayApiDash.obterIndicadores(this.filtro).subscribe((result) => {
			this.infoIndicadores = result;
			this.formatarDadosIndicador(this.infoIndicadores);

			for (const indicador of result) {
				if (indicador.descricao === "Recebidas") {
					const qtdDeCobrancasRecebidas = indicador.qtd;

					this.graficoEficienciaPorTentativa.tooltip = {
						enabled: true,
						custom: ({ series, seriesIndex, dataPointIndex, w }) => {
							const valSelected = series[seriesIndex][dataPointIndex];
							return this.templateCustomisadoDoTooltip({
								cabecalho: w.config.xaxis.categories[dataPointIndex],
								bolinha: w.config.colors[dataPointIndex],
								informacao: `${valSelected}: (${(
									(valSelected / qtdDeCobrancasRecebidas) *
									100
								).toFixed(2)}%) &nbsp`,
							});
						},
					};
					break;
				}
			}
			this.cd.detectChanges();
		});

		this.pactoPayApiDash
			.obterResumoPorAluno(this.filtro)
			.subscribe((result) => {
				this.resumoAluno = result;
				this.formatarDadosAluno(this.resumoAluno);
				this.cd.detectChanges();
			});

		this.pactoPayApiDash.obterInfoDoCartao(this.filtro).subscribe((result) => {
			this.infoCartao = result;
			this.formatarDadosCartao(this.infoCartao);
			this.cd.detectChanges();
		});

		this.pactoPayApiDash
			.obterInfoDasParcelas(this.filtro)
			.subscribe((result) => {
				this.infoParcelas = result;
				this.formatarDadosParcela(this.infoParcelas);
				this.cd.detectChanges();
			});

		this.pactoPayApiDash.obterUltimaExecucao().subscribe((result) => {
			this.ultimaExecucao = result;
			this.cd.detectChanges();
		});

		this.zwPactoPayApiDash.obterCredito().subscribe((result) => {
			this.utilizados = result.utilizados;
			this.creditoPacto = result.quantidade;
			this.descricaoCredito = result.utilizados
				? "Utilizados:"
				: result.descricao;
			this.cd.detectChanges();
		});
	}

	navegarTelaAluno(aluno) {
		this.modalRef.close(null);
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
	}

	formatarDadosParcela(infoParcelas: any) {
		infoParcelas.map((infoParcela) => {
			switch (infoParcela.nome) {
				case "VENCIDAS_EM_ABERTO":
					infoParcela.title = "Vencidas em aberto";
					break;
				case "SEM_TENTATIVA_COBRANCA":
					infoParcela.title = "Sem tentativa de cobrança";
					break;
			}
		});
	}

	formatarDadosCartao(infosCartoes: any) {
		infosCartoes.map((infoCartao) => {
			switch (infoCartao.nome) {
				case "VENCIDOS":
					infoCartao.title = "Vencidos";
					break;
				case "VENCER_PROXIMO_MES":
					infoCartao.title = "À vencer no próximo mês";
					break;
				case "MESMO_CARTAO":
					infoCartao.title = "Clientes com o mesmo cartão";
					break;
				case "CARTAO_NAO_VERIFICADO":
					infoCartao.title = "Alunos sem cartão verificado";
					break;
			}
		});
	}

	transformMoneySplit(value: number) {
		return value
			.toLocaleString("pt-br", { minimumFractionDigits: 2 })
			.split(",");
	}

	formatarDadosIndicador(indicadores: any) {
		indicadores.map((indicador) => {
			let valorFormatado;
			switch (indicador.descricao) {
				case "Enviadas":
					valorFormatado = this.transformMoneySplit(indicador.valor);
					indicador.valor = valorFormatado[0];
					indicador.valorDecimal = valorFormatado[1];
					indicador.title = "Enviadas";
					indicador.icon = "pct-rotate-cw";
					indicador.tooltiptext = `COBRANÇAS PREVISTAS ${indicador.qtd} PARCELAS`;
					break;
				case "Recebidas":
					valorFormatado = this.transformMoneySplit(indicador.valor);
					indicador.valor = valorFormatado[0];
					indicador.valorDecimal = valorFormatado[1];
					indicador.title = "Recebidas";
					indicador.icon = "pct-check-circle";
					indicador.tooltiptext = `COBRANÇAS RECEBIDAS ${indicador.qtd} PARCELAS`;
					break;
				case "Recebidas fora do Convênio":
					valorFormatado = this.transformMoneySplit(indicador.valor);
					indicador.valor = valorFormatado[0];
					indicador.valorDecimal = valorFormatado[1];
					indicador.title = "Fora do convênio";
					indicador.icon = "pct-minus-circle";
					indicador.tooltiptext = `COBRANÇAS RECEBIDAS FORA DO CONVÊNIO ${indicador.qtd} PARCELAS`;
					break;
				case "Pendentes":
					valorFormatado = this.transformMoneySplit(indicador.valor);
					indicador.valor = valorFormatado[0];
					indicador.valorDecimal = valorFormatado[1];
					indicador.title = "Pendentes";
					indicador.icon = "pct-alert-triangle";
					indicador.tooltiptext = `COBRANÇAS PENDENTES ${indicador.qtd} PARCELAS`;
					break;
				case "Canceladas":
					valorFormatado = this.transformMoneySplit(indicador.valor);
					indicador.valor = valorFormatado[0];
					indicador.valorDecimal = valorFormatado[1];
					indicador.title = "Canceladas";
					indicador.icon = "pct-x-circle";
					indicador.tooltiptext = `COBRANÇAS CANCELADAS ${indicador.qtd} PARCELAS`;
					break;
				case "Renegociadas":
					valorFormatado = this.transformMoneySplit(indicador.valor);
					indicador.valor = valorFormatado[0];
					indicador.valorDecimal = valorFormatado[1];
					indicador.title = "Renegociadas";
					indicador.icon = "pct-corner-up-left";
					indicador.tooltiptext = `COBRANÇAS RENEGOCIADAS ${indicador.qtd} PARCELAS`;
					break;
			}
		});
	}

	formatarDadosAluno(resumoAlunos: any) {
		resumoAlunos.map((resumoAluno) => {
			switch (resumoAluno.nome) {
				case "COM_AUTORIZACAO":
					resumoAluno.title = "Autorização de cobrança";
					resumoAluno.icon = "pct-users";
					break;
				case "CARTAO_CREDITO":
					resumoAluno.title = "Cobrança no \n cartão de crédito";
					resumoAluno.icon = "pct-users";
					break;
				case "DEBITO_CONTA":
					resumoAluno.title = "Cobrança no DCO";
					resumoAluno.icon = "pct-users";
					break;
				case "BOLETO_BANCARIO":
					resumoAluno.title = "Cobrança no \n boleto bancário";
					resumoAluno.icon = "pct-users";
					break;
				case "COBRANCA_BLOQUEADA":
					resumoAluno.title = "Cobrança bloqueada";
					resumoAluno.icon = "pct-users";
					break;
				case "COBRANCA_LIBERADAS":
					resumoAluno.title = "Cobrança liberada";
					resumoAluno.icon = "pct-users";
					break;
			}
		});
	}

	cardClickHandler(name: string, index: string) {
		this.openModalHandler(relatorios[name], index);
	}

	cardClickHandlerIndicador(index: string) {
		this.openModalHandler(relatorios[index], index);
	}

	getCelula(coluna: ModalRelatorioColunaConfig) {
		if (coluna.value === "nome") {
			return this.celulaNomeAluno;
		}

		if (coluna.value === "situacao" && coluna.entity === "parcelaInd") {
			return this.celulaStatus;
		}

		if (coluna.value === "telefone") {
			return this.celulaTelefone;
		}

		if (coluna.value === "situacao" && coluna.entity === "aluno") {
			return this.celulaSituacao;
		}

		if (coluna.value === "situacao" && coluna.entity === "parcela") {
			return this.celulaStatus;
		}

		if (coluna.value === "valor") {
			return this.celulaValor;
		}
	}

	private openModalHandler(item: ModalRelatorioConfig, index: string) {
		const params = new HttpParams()
			.append("empresas", this.filtro.empresas)
			.append("convenios", this.filtro.convenios)
			.append("inicio", this.filtro.inicio)
			.append("fim", this.filtro.fim);
		this.endpointShare = this.rest.buildFullUrlPactoPay(
			`${item.endpoint}/${index}?${params.toString()}`
		);
		const pctModal = this.modal.open(
			item.title,
			RelatorioCobrancaComponent,
			PactoModalSize.LARGE
		);
		this.modalRef = pctModal;
		this.relatorio = pctModal.componentInstance;
		this.config = {
			endpointUrl: this.endpointShare,
			pagination: item.pagination,
			quickSearch: true,
			exportButton: false,
			rowClick: false,
			columns: [],
		};

		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: column.titulo,
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
				celula: this.getCelula(column),
			};
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
	}

	public openModalDetalhamentoDeCredito(): void {
		if (this.utilizados) {
			const modal = this.modal.open(
				"Detalhamento de crédito Pacto",
				DetalhamentoDeCreditoComponent,
				PactoModalSize.LARGE
			);
			modal.componentInstance.empresas = this.filtro.empresas;
		}
	}

	criarGraficoIndiceDeCobranca({ dataProvider = [] }): void {
		this.graficoIndiceDeCobranca = {
			type: "serial",
			dataProvider,
			valueAxes: [
				{
					id: "axisEsquerda",
					// title: "Eficiência / Inadimplência %",
					axisAlpha: 0,
					position: "left",
					maximum: 100,
					fontSize: 12,
				},
				{
					id: "axisDireita",
					// title: "Valor (R$)",
					axisAlpha: 0,
					gridAlpha: 0,
					position: "right",
					fontSize: 12,
				},
			],
			xAxes: [
				{
					fontSize: 12,
				},
			],
			startDuration: 0.4,
			legend: {
				maxColumns: 5,
				align: "center",
				equalWidths: false,
				useGraphSettings: true,
				valueWidth: 0,
				spacing: 3,
			},
			numberFormatter: {
				precision: 2,
				decimalSeparator: ",",
				thousandsSeparator: ".",
			},
			graphs: [
				{
					title: "Eficiência (%)",
					type: "column",
					clustered: false,
					balloonText: "[[value]]% de Eficiência",
					columnWidth: 0.5,
					fillAlphas: 0.9,
					lineAlpha: 0.2,
					fillColors: PactoColor.GELO_05,
					lineColor: PactoColor.GELO_05,
					valueField: "eficienciaDouble",
					valueAxis: "axisEsquerda",
				},
				{
					title: "Inadimplência (%)",
					type: "column",
					clustered: false,
					balloonText: "[[value]]% de Inadimplência",
					columnWidth: 0.4,
					fillAlphas: 0.9,
					lineAlpha: 0.2,
					fillColors: PactoColor.AZULIM_PRI,
					lineColor: PactoColor.AZULIM_PRI,
					valueField: "inadimplencia",
					valueAxis: "axisEsquerda",
				},
				{
					title: "Enviadas",
					type: "line",
					balloonText: "[[title]]: <b>R$ [[value]]</b>",
					lineAlpha: 1,
					lineThickness: 3,
					lineColor: PactoColor.AZUL_PACTO_PRI,
					bullet: "round",
					bulletColor: PactoColor.AZUL_PACTO_PRI,
					valueField: "valorTotal",
					valueAxis: "axisDireita",
				},
				{
					title: "Recebidas",
					type: "line",
					balloonText: "[[title]]: <b>R$ [[value]]</b>",
					lineAlpha: 1,
					lineThickness: 3,
					lineColor: PactoColor.CHUCHUZINHO,
					bullet: "round",
					bulletColor: PactoColor.CHUCHUZINHO,
					valueField: "valorPago",
					valueAxis: "axisDireita",
				},
				{
					title: "Pendentes",
					type: "line",
					balloonText: "[[title]]: <b>R$ [[value]]</b>",
					lineAlpha: 1,
					lineThickness: 3,
					lineColor: PactoColor.HELLBOY_PRI,
					bullet: "round",
					bulletColor: PactoColor.HELLBOY_PRI,
					valueField: "valorEmAberto",
					valueAxis: "axisDireita",
				},
			],
			chartCursor: {
				categoryBalloonEnabled: false,
				cursorAlpha: 0,
				zoomable: false,
			},
			categoryField: "mes",
			categoryAxis: {
				gridAlpha: 0,
				// axisAlpha: 0,
				// tickLength: 0,
			},
			fontFamily: "'Nunito Sans', sans-serif",
			fontWeight: 300,
			export: {
				enabled: true,
			},
		};
	}

	criarGraficoEficienciaPorTentativa({ series = [], categories = [] }): void {
		this.graficoEficienciaPorTentativa.chart = {
			width: "100%",
			height: 350,
			type: "bar",
			fontFamily: "'Nunito Sans', sans-serif",
			toolbar: { show: false },
		};
		this.graficoEficienciaPorTentativa.series = series;
		this.graficoEficienciaPorTentativa.xaxis = {
			categories,
			labels: {
				show: false,
			},
		};
		this.graficoEficienciaPorTentativa.yaxis = {
			labels: {
				style: {
					fontSize: "12px",
				},
			},
		};
		this.graficoEficienciaPorTentativa.colors = [
			PactoColor.VERDINHO_PRI,
			PactoColor.AZULIM_PRI,
			PactoColor.PEQUIZAO_PRI,
			PactoColor.HELLBOY_PRI,
		];
		this.graficoEficienciaPorTentativa.plotOptions = {
			bar: {
				distributed: true,
			},
		};
		this.graficoEficienciaPorTentativa.dataLabels = { enabled: false };
		this.graficoEficienciaPorTentativa.legend = {
			markers: {
				width: 12,
				height: 12,
				radius: 7,
			},
		};
		this.graficoEficienciaPorTentativa.fill = { opacity: 1 };
	}

	criarGraficoRelatorioDeCodigoDeRetorno(relatorio = []): void {
		const categories = relatorio.map((c) => c.codigo);
		const series = [{ data: relatorio.map((c) => c.qtd), name: "" }];

		this.graficoRelatorioDeCodigoDeRetorno.chart = {
			width: "100%",
			height: 350,
			type: "bar",
			fontFamily: "'Nunito Sans', sans-serif",
			toolbar: { show: false },
		};
		this.graficoRelatorioDeCodigoDeRetorno.series = series;
		this.graficoRelatorioDeCodigoDeRetorno.xaxis = {
			categories,
			labels: {
				show: true,
				style: {
					fontSize: "12px",
				},
			},
		};
		this.graficoRelatorioDeCodigoDeRetorno.yaxis = {
			title: {
				text: "Quantidade",
			},
			labels: {
				style: {
					fontSize: "12px",
				},
			},
		};
		this.graficoRelatorioDeCodigoDeRetorno.colors = ["#F0B924"];
		this.graficoRelatorioDeCodigoDeRetorno.dataLabels = { enabled: false };
		this.graficoRelatorioDeCodigoDeRetorno.legend = {
			markers: {
				width: 12,
				height: 12,
				radius: 7,
			},
		};
		this.graficoRelatorioDeCodigoDeRetorno.fill = { opacity: 1 };
		this.graficoRelatorioDeCodigoDeRetorno.tooltip = {
			custom: (res) => {
				const informacao = relatorio.find(
					(c) => c.qtd === res.series[res.seriesIndex][res.dataPointIndex]
				);
				return this.templateCustomisadoDoTooltip({
					cabecalho: res.w.globals.labels[res.dataPointIndex],
					bolinha: res.w.globals.markers.colors[res.seriesIndex],
					informacao: informacao.descricao,
				});
			},
		};
	}

	criarGraficoEnviosPorConvenio({ series = [], labels = [] }): void {
		this.graficoEnviosPorConvenio.chart = {
			width: 385,
			height: 500,
			type: "pie",
			fontFamily: "'Nunito Sans', sans-serif",
			toolbar: { show: false },
		};
		this.graficoEnviosPorConvenio.series = series;
		this.graficoEnviosPorConvenio.labels = labels;
		this.graficoEnviosPorConvenio.grid = {
			// show: false,
			padding: { top: 0, bottom: 0, left: 0, right: 0 },
		};
		this.graficoEnviosPorConvenio.legend = {
			show: true,
			fontSize: "12px",
			offsetX: 10,
			offsetY: 0,
			floating: false,
			formatter(seriesName, opts) {
				let tipoconveio = seriesName.split(";")[0];
				if (tipoconveio.length > 17) {
					tipoconveio = tipoconveio.substring(0, 16) + "...";
				}
				return tipoconveio;
			},
		};
		this.graficoEnviosPorConvenio.tooltip = {
			enabled: true,
			theme: "light",
			custom: ({ seriesIndex, w }) => {
				const cabecalho = w.globals.labels[seriesIndex].split(";")[1];
				const bolinha = w.globals.markers.colors[seriesIndex];
				const informacao = w.globals.series[seriesIndex];
				return `
          <div style="max-width: 20rem">
          <div class="apexcharts-tooltip-title" style="font-family: 'Nunito Sans', sans-serif; font-size: 12px; z-index: 999">
            ${cabecalho}
          </div>
          <div class="apexcharts-tooltip-series-group apexcharts-active" style="order: 1; display: flex">
            <span class="apexcharts-tooltip-marker" style="background-color: ${bolinha}; padding: 6px"></span>
            <div class="apexcharts-tooltip-text" style="font-family: 'Nunito Sans', sans-serif; font-size: 12px">
              <div class="apexcharts-tooltip-y-group">
                <span class="apexcharts-tooltip-text-y-label"></span>
                <span class="apexcharts-tooltip-text-y-value" style="white-space: normal">${informacao}</span>
              </div>
              <div class="apexcharts-tooltip-goals-group">
                <span class="apexcharts-tooltip-text-goals-label"></span>
                <span class="apexcharts-tooltip-text-goals-value"></span>
              </div>
              <div class="apexcharts-tooltip-z-group">
                <span class="apexcharts-tooltip-text-z-label"></span>
                <span class="apexcharts-tooltip-text-z-value"></span>
              </div>
            </div>
          </div>
        </div>
      `;
			},
			fillSeriesColor: false,
			style: {
				fontSize: "12px",
				fontFamily: undefined,
			},
			onDatasetHover: {
				highlightDataSeries: false,
			},
			marker: {
				show: true,
			},
		};
	}

	templateCustomisadoDoTooltip({ cabecalho, bolinha, informacao }): string {
		return `
      <div style="max-width: 12.5rem">
        <div class="apexcharts-tooltip-title" style="font-family: 'Nunito Sans', sans-serif; font-size: 12px; z-index: 999">
          ${cabecalho}
        </div>
        <div class="apexcharts-tooltip-series-group apexcharts-active" style="order: 1; display: flex">
          <span class="apexcharts-tooltip-marker" style="background-color: ${bolinha}; padding: 6px"></span>
          <div class="apexcharts-tooltip-text" style="font-family: 'Nunito Sans', sans-serif; font-size: 12px">
            <div class="apexcharts-tooltip-y-group">
              <span class="apexcharts-tooltip-text-y-label"></span>
              <span class="apexcharts-tooltip-text-y-value" style="white-space: normal">${informacao}</span>
            </div>
            <div class="apexcharts-tooltip-goals-group">
              <span class="apexcharts-tooltip-text-goals-label"></span>
              <span class="apexcharts-tooltip-text-goals-value"></span>
            </div>
            <div class="apexcharts-tooltip-z-group">
              <span class="apexcharts-tooltip-text-z-label"></span>
              <span class="apexcharts-tooltip-text-z-value"></span>
            </div>
          </div>
        </div>
      </div>
    `;
	}
}
