export interface ModalRelatorioConfig {
	title: string;
	pagination?: boolean;
	endpoint: string;
	filtro?: boolean;
	columns: ModalRelatorioColunaConfig[];
}

export interface ModalRelatorioColunaConfig {
	value: any;
	date?: boolean;
	ordenavel?: boolean;
	titulo: string;
	entity?: string;
}

export const relatorios: { [relatorio: string]: ModalRelatorioConfig } = {
	1: {
		title: "Parcelas enviadas",
		endpoint: "dash/indicadores/lista",
		filtro: true,
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcelaInd",
			},
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
			{ value: "valor", titulo: "Valor", ordenavel: false },
		],
	},
	2: {
		title: "Parcelas recebidas",
		endpoint: "dash/indicadores/lista",
		filtro: true,
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcelaInd",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	3: {
		title: "Parcelas recebidas fora do convênio",
		endpoint: "dash/indicadores/lista",
		filtro: true,
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcelaInd",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	4: {
		title: "Parcelas pendentes",
		endpoint: "dash/indicadores/lista",
		filtro: true,
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcelaInd",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	5: {
		title: "Parcelas canceladas",
		endpoint: "dash/indicadores/lista",
		filtro: true,
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcelaInd",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	6: {
		title: "Parcelas Renegociadas",
		endpoint: "dash/indicadores/lista",
		filtro: true,
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcelaInd",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	VENCIDOS: {
		title: "Alunos com cartão de crédito vencido",
		endpoint: "dash/infocartao/lista",
		columns: [
			{ value: "matricula", ordenavel: false, titulo: "Matrícula" },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "cartao", titulo: "Cartão", ordenavel: false },
			{ value: "bandeira", titulo: "Bandeira", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	VENCER_PROXIMO_MES: {
		title: "Alunos com cartão de crédito à vencer no próximo mês",
		endpoint: "dash/infocartao/lista",
		columns: [
			{ value: "matricula", ordenavel: false, titulo: "Matrícula" },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "cartao", titulo: "Cartão", ordenavel: false },
			{ value: "bandeira", titulo: "Bandeira", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	MESMO_CARTAO: {
		title: "Alunos com o mesmo cartão de crédito",
		endpoint: "dash/infocartao/lista",
		columns: [
			{ value: "matricula", ordenavel: false, titulo: "Matrícula" },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "cartao", titulo: "Cartão", ordenavel: false },
			{ value: "bandeira", titulo: "Bandeira", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	CARTAO_NAO_VERIFICADO: {
		title: "Alunos sem cartão verificado",
		endpoint: "dash/infocartao/lista",
		columns: [
			{ value: "matricula", ordenavel: false, titulo: "Matrícula" },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "cartao", titulo: "Cartão", ordenavel: false },
			{ value: "bandeira", titulo: "Bandeira", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	VENCIDAS_EM_ABERTO: {
		title: "Parcelas vencidas em aberto",
		endpoint: "dash/infoparcelas/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Situação",
				ordenavel: false,
				entity: "parcela",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	SEM_TENTATIVA_COBRANCA: {
		title: "Parcelas sem tentiva de cobrança",
		endpoint: "dash/infoparcelas/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "codigo", titulo: "Cód.: Parcela", ordenavel: false },
			{ value: "descricao", titulo: "Descrição", ordenavel: false },
			{ value: "nrTentativas", titulo: "Tentativas", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "parcela",
			},
			{ value: "valor", titulo: "Valor", ordenavel: false },
			{ value: "vencimento", titulo: "Vencimento", ordenavel: false },
		],
	},
	COM_AUTORIZACAO: {
		title: "Todas autorizações de cobrança",
		endpoint: "dash/infoalunos/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Situação",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	CARTAO_CREDITO: {
		title: "Todas autorizações de cobrança em cartão de crédito",
		endpoint: "dash/infoalunos/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	DEBITO_CONTA: {
		title: "Todas autorizações de cobrança em débito em conta",
		endpoint: "dash/infoalunos/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	BOLETO_BANCARIO: {
		title: "Todas autorizações de cobrança em boleto",
		endpoint: "dash/infoalunos/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	COBRANCA_BLOQUEADA: {
		title: "Alunos com cobranças bloqueadas.",
		endpoint: "dash/infoalunos/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
	COBRANCA_LIBERADAS: {
		title: "Alunos com cobranças liberadas.",
		endpoint: "dash/infoalunos/lista",
		columns: [
			{ value: "matricula", titulo: "Matrícula", ordenavel: false },
			{ value: "nome", titulo: "Nome do aluno", ordenavel: false },
			{ value: "convenio", titulo: "Convênio", ordenavel: false },
			{ value: "telefone", titulo: "Contato", ordenavel: false },
			{
				value: "situacao",
				titulo: "Status",
				ordenavel: false,
				entity: "aluno",
			},
		],
	},
};
