<pacto-cat-layout-v2 id="dashboard">
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'PactoPay / Business Intelligence'
		}"></pacto-breadcrumbs>
	<div class="titulo">Business Intelligence</div>
	<div class="container-mini-card">
		<pacto-cat-grid-mini-card
			[headerText]="'Último envio de cobrança'"
			[icon]="'pct-repeat'"
			[type]="'AZULIM'"
			class="minicard">
			<div card-body>
				<label class="body-mini-card">{{ ultimaExecucao }}</label>
			</div>
		</pacto-cat-grid-mini-card>
		<pacto-cat-grid-mini-card
			(click)="openModalDetalhamentoDeCredito()"
			[headerText]="'Crédito Pacto'"
			[icon]="'pct-dollar-sign'"
			[ngClass]="{ minicard: true, clickavel: utilizados }"
			[type]="'CHUCHUZINHO'"
			id="credito-pacto">
			<div card-body>
				<label class="body-mini-card"></label>
				<div class="label-descricao">{{ descricaoCredito }}</div>
				<div *ngIf="utilizados" class="label-credito">{{ creditoPacto }}</div>
			</div>
		</pacto-cat-grid-mini-card>
	</div>

	<pacto-pay-filter
		#payFilter
		(obterFiltro)="filtrar($event)"
		[includesConvenio]="true"
		[includesEmpresa]="true"
		[includesPeriodo]="true"></pacto-pay-filter>

	<pacto-pay-filter-info
		(removerFiltro)="payFilter.definirValor($event)"
		[filtro]="filtroInfo"></pacto-pay-filter-info>

	<div class="flex-space details details-width">
		<pacto-cat-grid-square-card
			(click)="cardClickHandlerIndicador(item.indicador)"
			*ngFor="let item of infoIndicadores">
			<div card-title style="color: gray">{{ item.title }}</div>
			<div card-body>
				<div class="flex-center" style="padding-top: 10px">
					<p style="font-size: 14px; line-height: 17px; color: rgb(0, 0, 0)">
						R$
					</p>
					<h2 style="font-size: 20px; line-height: 24px; color: rgb(0, 0, 0)">
						{{ item.valor }}
					</h2>
					<p style="font-size: 14px; margin-top: 4px; color: rgb(0, 0, 0)">
						,{{ item.valorDecimal }}
					</p>
				</div>
				<div class="flex-center" style="color: gray">
					<label>Qnt.:</label>
					<label>{{ item.qtd }}</label>
				</div>
			</div>
		</pacto-cat-grid-square-card>
	</div>
	<div class="grid-charts">
		<div class="chart-line">
			<h5 class="title">Índice de cobrança</h5>
			<amCharts
				[options]="graficoIndiceDeCobranca"
				[style.height.px]="350"
				[style.width.%]="100"></amCharts>
		</div>
		<div class="chart-column">
			<h5 class="title">Eficiência por tentativa</h5>
			<!-- [noData]="{ text: 'Sem dados no momento' }" -->
			<pacto-pay-chart
				#graficoEficiencaTentativas
				[chart]="graficoEficienciaPorTentativa.chart"
				[colors]="graficoEficienciaPorTentativa.colors"
				[dataLabels]="graficoEficienciaPorTentativa.dataLabels"
				[fill]="graficoEficienciaPorTentativa.fill"
				[legend]="graficoEficienciaPorTentativa.legend"
				[plotOptions]="graficoEficienciaPorTentativa.plotOptions"
				[series]="graficoEficienciaPorTentativa.series"
				[tooltip]="graficoEficienciaPorTentativa.tooltip"
				[xaxis]="graficoEficienciaPorTentativa.xaxis"
				[yaxis]="graficoEficienciaPorTentativa.yaxis"></pacto-pay-chart>
		</div>
	</div>
	<div class="informations">
		<div class="resume-client-details">
			<h5 class="title">Alunos com</h5>
			<div class="grid-resume-client">
				<pacto-cat-grid-square-card
					(click)="cardClickHandler(clientResume.nome, clientResume.indicador)"
					*ngFor="let clientResume of resumoAluno"
					class="card-resume-client">
					<div
						card-title
						style="white-space: pre-line; line-height: 18px; margin: -5px">
						{{ clientResume.title }}
					</div>
					<div card-body style="margin-top: 10px; text-align: center">
						<div style="z-index: 1">
							<i class="pct {{ clientResume.icon }}"></i>
						</div>
						<div style="z-index: 2">
							<label class="label-qnt-client">
								{{ clientResume.valor }}
							</label>
						</div>
					</div>
				</pacto-cat-grid-square-card>
			</div>
		</div>
		<div class="cards">
			<div class="grid-v2">
				<pacto-cat-grid-card-v2 [type]="'BRANCO'">
					<div card-title class="title-card">Cartões de crédito</div>
					<div card-body>
						<div class="line-100"></div>
						<div *ngFor="let cartao of infoCartao">
							<pacto-cat-button-custom
								(click)="cardClickHandler(cartao.nome, cartao.indicador)"
								[type]="'AZULIM'">
								<div button-body class="button-body">
									<div class="text">{{ cartao.title }}</div>
									<div class="value">
										{{ cartao.valor }}
										<i class="pct pct-chevron-right"></i>
									</div>
								</div>
							</pacto-cat-button-custom>
							<div class="line"></div>
						</div>
					</div>
				</pacto-cat-grid-card-v2>
			</div>
			<div class="grid-v2">
				<pacto-cat-grid-card-v2 [type]="'BRANCO'">
					<div card-title class="title-card">Parcelas</div>
					<div card-body>
						<div class="line-100"></div>
						<div *ngFor="let infoParcela of infoParcelas">
							<pacto-cat-button-custom
								(click)="
									cardClickHandler(infoParcela.nome, infoParcela.indicador)
								"
								[type]="'LARANJINHA'">
								<div button-body class="button-body">
									<div class="text">
										{{ infoParcela.title }}
									</div>
									<div class="value" style="color: #ff4b2b">
										{{ infoParcela.valor }}
										<i class="pct pct-chevron-right"></i>
									</div>
								</div>
							</pacto-cat-button-custom>
							<div class="line"></div>
						</div>
					</div>
				</pacto-cat-grid-card-v2>
			</div>
		</div>
	</div>
	<div class="informations-2">
		<div class="chart-column">
			<h5 class="title">Relatório de código de retorno</h5>
			<!-- [noData]="{ text: 'Sem dados no momento' }" -->
			<pacto-pay-chart
				#graficoRelatorioCodigoInterno
				[chart]="graficoRelatorioDeCodigoDeRetorno.chart"
				[colors]="graficoRelatorioDeCodigoDeRetorno.colors"
				[dataLabels]="graficoRelatorioDeCodigoDeRetorno.dataLabels"
				[fill]="graficoRelatorioDeCodigoDeRetorno.fill"
				[legend]="graficoRelatorioDeCodigoDeRetorno.legend"
				[plotOptions]="graficoRelatorioDeCodigoDeRetorno.plotOptions"
				[series]="graficoRelatorioDeCodigoDeRetorno.series"
				[tooltip]="graficoRelatorioDeCodigoDeRetorno.tooltip"
				[xaxis]="graficoRelatorioDeCodigoDeRetorno.xaxis"
				[yaxis]="graficoRelatorioDeCodigoDeRetorno.yaxis"></pacto-pay-chart>
		</div>
		<div class="chart-pie">
			<h5 class="title">Envios por convênio</h5>
			<!-- [noData]="{ text: 'Sem dados no momento' }" -->
			<pacto-pay-chart
				#graficoEnviosConvenio
				[chart]="graficoEnviosPorConvenio.chart"
				[grid]="graficoEnviosPorConvenio.grid"
				[labels]="graficoEnviosPorConvenio.labels"
				[legend]="graficoEnviosPorConvenio.legend"
				[series]="graficoEnviosPorConvenio.series"
				[tooltip]="graficoEnviosPorConvenio.tooltip"></pacto-pay-chart>
			<hr />
			<div
				*ngIf="
					valorTpv > 0 ||
					(valorTpv === 0 && graficoEnviosPorConvenio.labels?.length > 0)
				"
				class="descricaoTpv">
				Volume total de pagamento (TPV):
			</div>
			<div *ngIf="valorTpv > 0" class="valorTpv">
				{{ valorTpv | currency : "BRL" : true : "1.2-2" }}
			</div>
			<div
				*ngIf="valorTpv === 0 && graficoEnviosPorConvenio.labels?.length > 0"
				class="valorTpvZero">
				Nenhum valor recebido
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>

<ng-template #celulaNomeAluno let-item="item">
	<div class="cel-nome-aluno">
		<a (click)="navegarTelaAluno(item)" class="list-item-link">
			{{ item.nome | captalize }}
		</a>
	</div>
</ng-template>

<ng-template #celulaStatus let-parcela="item">
	<pacto-status-parcela
		[situacao]="parcela?.situacao"
		[tooltip]="parcela?.situacao"></pacto-status-parcela>
</ng-template>

<ng-template #celulaTelefone let-item="item">
	<div *ngIf="item.telefone" class="cel-phone">
		<span>{{ item.telefone }}</span>
	</div>
</ng-template>

<ng-template #celulaSituacao let-item="item">
	<pacto-status-aluno [aluno]="item"></pacto-status-aluno>
</ng-template>

<ng-template #celulaVerMais let-item="item">
	<span style="color: blue">Veja mais</span>
</ng-template>

<ng-template #celulaValor let-item="item">
	<span>{{ item.valor | currency : "R$" }}</span>
</ng-template>

<pacto-wehelp></pacto-wehelp>
