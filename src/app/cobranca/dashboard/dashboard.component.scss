@import "dist/ui-kit/assets/import.scss";

.titulo {
	font-weight: bold;
	color: $pretoPri;
	line-height: 28px;
	font-size: 24px;
	width: 100%;
}

.details-width {
	margin-bottom: 24px;

	pacto-cat-grid-square-card {
		width: 171px;
		height: 118px !important;
		cursor: pointer;

		p {
			display: grid;
		}
	}
}

.hover-button {
	&:hover {
		box-sizing: border-box;
		box-shadow: 0px 4px 6px #e4e5e6;
	}
}

.grid-charts {
	display: flex;
	justify-content: space-between;
	margin-bottom: 48px;

	.chart-line {
		background-color: #ffffff;
		width: 66.5%;
		height: 425px;
		border-radius: 6px;
		padding-bottom: 24px;
	}

	.chart-column {
		background-color: #ffffff;
		width: 31.7%;
		height: 425px;
		border-radius: 6px;
		padding-bottom: 24px;
	}
}

.resume-client-details {
	background: #ffff;
	border-radius: 6px;
	width: 66.5%;
	height: 392px;
}

.grid-resume-client {
	flex-wrap: wrap;
	display: flex;
	justify-content: space-between;
	padding-right: 24px;
	padding-left: 24px;
	margin-top: 16px;
}

.informations {
	display: flex;
	justify-content: space-between;
	margin-bottom: 48px;

	i {
		color: $azulimPri;
		font-size: 20px;
		line-height: 20px;
	}
}

.card-resume-client {
	border: 1px solid #e1e2e4;
	border-radius: 8px;
	width: 30%;
	height: 148px;
	margin-bottom: 18px;
	position: relative;
	background: 0 0;
	justify-content: center;
	align-content: center;
	display: grid;
	cursor: pointer;

	&:hover {
		font-weight: bold;
		box-sizing: border-box;
		box-shadow: 4px 4px 6px #e4e5e6;
	}
}

.cards {
	width: 31.7%;
}

.grid-v2 {
	margin-bottom: 42px;

	.title-card {
		color: #43474b;
		font-weight: 700;
		font-size: 16px;
		margin-top: -8px;
	}
}

.grid-card-v2-detail {
	color: $gelo05;
	border-bottom-left-radius: 8px;
	border-top-left-radius: 8px;
}

.button-body {
	display: flex;
	justify-content: space-between;
	max-height: 38px;
	text-align: center;
	padding: 1.5% 9% 2.5% 4%;

	i {
		font-size: 13px;
		color: $pretoPri;
	}
}

.value {
	font-weight: bold;
}

.informations-2 {
	display: flex;
	justify-content: space-between;
	margin-top: 32px;
	margin-bottom: 24px;

	.chart-column {
		background-color: #ffffff;
		width: 66.5%;
		height: 425px;
		padding: 10px 15px 0px 10px;
		border-radius: 6px;
		padding-bottom: 24px;
	}

	.chart-pie {
		background-color: #ffffff;
		width: 32%;
		height: 425px;
		border-radius: 6px;
		padding-top: 5px;
		padding-right: 0px;
	}

	.sends-grid {
		background-color: #ffffff;
		width: 31.7%;
		height: 250px;
		border-radius: 6px;

		th {
			border-top: none;
			font-weight: 600;
			font-size: 12px;
			line-height: 12px;
		}

		td {
			border-top: none;
			line-height: 12px;
		}

		.column-convenio {
			max-width: 100px;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}

		.table-striped-invert > tbody > tr:nth-child(even) > td,
		.table-striped-invert > tbody > tr:nth-child(even) > th {
			background: #eff2f7;
		}

		table .subtext {
			font-weight: 400;
			line-height: 12px;
			@media (max-width: 1280px) {
				font-size: 8px;
			}
			@media (min-width: 1280px) {
				font-size: 10px;
			}
		}

		table .text {
			font-weight: 600;
			color: $pretoPri;
			@media (max-width: 1280px) {
				font-size: 12px;
			}
			@media (min-width: 1280px) {
				font-size: 14px;
			}
		}

		table .text-chuchuzinho {
			font-weight: 600;
			color: $chuchuzinho06;
			@media (max-width: 1280px) {
				font-size: 12px;
			}
			@media (min-width: 1280px) {
				font-size: 14px;
			}
		}

		table .text-pequizaoPri {
			font-weight: 600;
			color: $pequizaoPri;
			@media (max-width: 1280px) {
				font-size: 12px;
			}
			@media (min-width: 1280px) {
				font-size: 14px;
			}
		}
	}
}

.title {
	color: $pretoPri;
	font-weight: 700;
	font-size: 16px;
	padding: 24px 0px 0px 12px;
}

.list-item-link {
	font-weight: bold;
	color: #026abc !important;
	cursor: pointer;
	font-weight: bold;
	font-size: 12px;
	line-height: 16px;
}

.cel-phone {
	color: #51555a;
}

.cel-phone > span {
	margin-right: 10px;
}

@media (min-width: 992px) {
	::ng-deep .modal-lg {
		max-width: 953px;
	}
}

@media (min-width: 1440px) {
	::ng-deep .modal-lg {
		max-width: 1080px;
	}
}

.text-no-wrap {
	white-space: nowrap;
	text-overflow: ellipsis;
}

.btn-filter-search {
	background-color: $azulim06;
}

.valorTpv {
	margin-top: 10px;
	text-align: center;
	color: limegreen;
	font-size: 20px;
	font-weight: bold;
}

.descricaoTpv {
	margin-top: 30px;
	width: 100%;
	text-align: center;
	font-size: 18px;
	font-weight: bold;
}

.valorTpvZero {
	margin-top: 10px;
	text-align: center;
	color: rgb(240, 185, 36);
	font-size: larger;
	font-weight: bold;
}

.minicard.clickavel {
	cursor: pointer;
}
