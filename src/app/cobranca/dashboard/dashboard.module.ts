import { AmChartsModule } from "@amcharts/amcharts3-angular";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { PayChartModule } from "../components/pay-chart/pay-chart.module";
import { PayFilterModule } from "../components/pay-filter/pay-filter.module";
import { RelatorioCobrancaModule } from "../components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusAlunoModule } from "../components/status-aluno/status-aluno.module";
import { StatusParcelaModule } from "../components/status-parcela/status-parcela.module";
import { WehelpModule } from "../components/wehelp/wehelp.module";
import { DashboardRoutingModule } from "./dashboard-routing.module";
import { DashboardComponent } from "./dashboard.component";

@NgModule({
	declarations: [DashboardComponent],
	imports: [
		CommonModule,
		DashboardRoutingModule,
		BaseSharedModule,
		PayFilterModule,
		PayChartModule,
		AmChartsModule,
		StatusParcelaModule,
		StatusAlunoModule,
		WehelpModule,
		RelatorioCobrancaModule,
	],
})
export class DashboardModule {}
