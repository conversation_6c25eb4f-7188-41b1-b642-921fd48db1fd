export interface Indicadores {
	valor: string;
	valorDecimal?: string;
	title?: string;
	nome?: string;
	icon?: string;
	tooltiptext?: string;
	descricao: string;
	indicador: number;
	qtd: number;
}

export interface InfoGrafico {
	data: Array<number>;
	name: string;
}

export interface InfoGenerico {
	indicador: number;
	nome: string;
	valor: number;
	title?: string;
	icon?: string;
}

export interface InfoConvenio {
	aprovadoQtd: number;
	aprovadoValor: number;
	convenioCobranca: string;
	tipoConvenioDescricao: string;
	convenioCobrancaCodigo: number;
	eficiencia: number;
	naoAprovadoQtd: number;
	naoAprovadoValor: number;
}
