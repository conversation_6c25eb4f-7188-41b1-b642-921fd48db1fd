import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";

import { BenchmarkListaComponent } from "./components/benchmark-lista/benchmark-lista.component";
import { BenchmarkEditComponent } from "./components/benchmark-edit/benchmark-edit.component";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const funcionalidade = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.BENCHMARK,
	true
);

const routes: Routes = [
	{
		path: "",
		component: BenchmarkListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: "adicionar",
		component: BenchmarkEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: ":id",
		component: BenchmarkEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), BaseSharedModule, CommonModule],
	declarations: [BenchmarkListaComponent, BenchmarkEditComponent],
})
export class BenchmarkModule {}
