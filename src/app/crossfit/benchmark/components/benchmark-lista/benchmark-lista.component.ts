import { Component, OnInit, ViewChild, ChangeDetectorRef } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

import { SnotifyService } from "ng-snotify";

import { ModalService } from "@base-core/modal/modal.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { Nivel } from "treino-api";
import {
	GridFilterManyOption,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { Router } from "@angular/router";
import {
	TreinoApiTipoBenchmarkService,
	TipoBenchmark,
	TreinoApiBenchmarkService,
} from "treino-api";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-benchmark-lista",
	templateUrl: "./benchmark-lista.component.html",
	styleUrls: ["./benchmark-lista.component.scss"],
})
export class BenchmarkListaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeModalButton", { static: false }) removeModalButton;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("buttonName", { static: true }) buttonName;

	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("tipoExercicioColumnName", { static: true })
	tipoExercicioColumnName;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("preDefinidoColumnName", { static: false }) preDefinidoColumnName;
	@ViewChild("exerciciosColumnName", { static: true }) exerciciosColumnName;
	@ViewChild("tipoTranslatorColumn", { static: true }) tipoTranslatorColumn;
	@ViewChild("tipoBenchmarkColumnName", { static: true })
	tipoBenchmarkColumnName;
	@ViewChild("tipoExercicios", { static: true }) tipoExercicios;
	@ViewChild("tipoExercicioTranslator", { static: true })
	tipoExercicioTranslator;
	@ViewChild("tipoBenchmark", { static: false }) tipoBenchmark;
	@ViewChild("tipoBenchmarkCelula", { static: true }) tipoBenchmarkCelula;

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});

	loading = false;
	nomeBenchmark = "";
	ready = false;

	constructor(
		private router: Router,
		private rest: RestService,
		private modalService: ModalService,
		private benchmarkService: TreinoApiBenchmarkService,
		private snotifyService: SnotifyService,
		private tipoBenchmarkService: TreinoApiTipoBenchmarkService,
		private sessionServive: SessionService,
		private cd: ChangeDetectorRef
	) {}

	data: ApiResponseList<Nivel> = {
		content: [],
	};

	tipoBenchmarksList: ApiResponseList<TipoBenchmark> = {
		content: [],
	};

	table: PactoDataGridConfig;
	filterConfig: any;
	tipoBenchmarks: Array<GridFilterManyOption>;

	ngOnInit() {
		this.configTable();
		this.setFilters();
		this.getTipoBenchmark().subscribe(() => {
			this.configFilters();
			this.ready = true;
		});
		this.ready = true;
	}

	btnClickHandler() {
		this.router.navigate(["cross", "cadastros", "benchmarks", "adicionar"]);
	}

	btnEditHandler(item) {
		this.router.navigate(["cross", "cadastros", "benchmarks", item.id]);
	}

	removeHandler(item) {
		this.nomeBenchmark = item.nome;
		setTimeout(() => {
			const removeModalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const removeModalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(
				removeModalTitle,
				removeModalBody
			);
			handler.result
				.then(() => {
					this.benchmarkService.removerBenchmark(item.id).subscribe(() => {
						this.snotifyService.success(removeSuccess);
						this.tableData.reloadData();
					});
				})
				.catch(() => {});
		});
	}

	searchHandler(value) {
		this.fetchData();
	}

	private setFilters() {
		this.tipoBenchmarkService.obterTodosTipoBenchmark().subscribe((result) => {
			this.tipoBenchmarks = [];
			result.content.forEach((tipoBenchmark) => {
				this.tipoBenchmarks.push({
					value: tipoBenchmark.id,
					label: tipoBenchmark.nome,
				});
			});
			this.configFilters();
			this.cd.detectChanges(); // Chame detectChanges aqui
		});
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	private configTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("benchmarks"),
			logUrl: this.rest.buildFullUrl("log/benchmarks"),
			quickSearch: true,
			buttons: {
				conteudo: this.buttonName,
				nome: "add",
				id: "btn-novo-benchmark",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "exercicios",
					titulo: this.exerciciosColumnName,
					buscaRapida: false,
					visible: false,
					ordenavel: false,
					defaultVisible: true,
					campo: "exercicios",
				},
				{
					nome: "tipoExercicio",
					titulo: this.tipoExercicioColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.tipoTranslatorColumn,
					campo: "tipoExercicio",
				},
				{
					nome: "tipoBenchmark.nome",
					titulo: this.tipoBenchmarkColumnName,
					buscaRapida: false,
					visible: false,
					ordenavel: true,
					defaultVisible: true,
					campo: "tipoBenchmark",
					celula: this.tipoBenchmarkCelula,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "tipoBenchmark",
					label: this.tipoBenchmarkColumnName,
					type: GridFilterType.MANY,
					options: this.tipoBenchmarksList.content,
				},
				{
					name: "tipoExercicios",
					label: this.tipoExercicios,
					type: GridFilterType.MANY,
					translator: this.tipoExercicioTranslator,
					options: [
						{ value: "FOR_TIME" },
						{ value: "FOR_REPS" },
						{ value: "FOR_WEIGHT" },
						{ value: "AMRAP" },
					],
				},
			],
		};
	}

	private getTipoBenchmark(): Observable<any> {
		const tipoBenchmarks$ = this.tipoBenchmarkService
			.obterListaTipoBenchmark()
			.pipe(
				map((dados) => {
					dados.content.forEach((tipoBenchmark: any) => {
						tipoBenchmark.value = tipoBenchmark.id;
						tipoBenchmark.label = tipoBenchmark.nome;
					});
					this.tipoBenchmarksList = dados;
					return true;
				})
			);
		return tipoBenchmarks$;
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}
}
