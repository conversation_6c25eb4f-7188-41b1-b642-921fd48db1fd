.actions {
	display: flex;
	margin-top: 15px;
	flex-direction: row-reverse;

	button {
		margin-left: 10px;
	}
}

.header-row {
	display: flex;
	justify-content: space-between;
	padding-bottom: 10px;
}

.images-wrapper {
	margin-top: 10px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	border-top: 1px solid #d1d1d1;
	padding-top: 15px;

	.imagem {
		margin: 5px;
		width: 150px;

		.imagem-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f5f5f5;
			width: 150px;
			height: 150px;
		}

		.control-footer {
			display: flex;

			.name {
				width: calc(100% - 20px);
				padding-top: 5px;
				padding-right: 5px;
				word-break: break-all;
				overflow: hidden;
			}

			.icon {
				padding-top: 5px;
				flex-grow: 1;
				cursor: pointer;
			}
		}

		img {
			max-width: 150px;
			max-height: 150px;
		}
	}
}
