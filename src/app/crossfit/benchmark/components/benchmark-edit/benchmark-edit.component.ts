import { Component, OnInit, ViewChild, ChangeDetectorRef } from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import { SeletorImagemComponent } from "old-ui-kit";

import {
	TreinoApiBenchmarkService,
	Benchmark,
	TipoBenchmark,
	TreinoApiTipoBenchmarkService,
} from "treino-api";
import { SeletorImagemAvancadoComponent } from "../../../../treino/atividade/components/seletor-imagem-avancado/seletor-imagem-avancado.component";
import { DataUrl, NgxImageCompressService } from "ngx-image-compress";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

interface ImagemConfig {
	id?: string;
	uri: string;
	nome?: string;
	data?: any;
	extensao?: string;
}

@Component({
	selector: "pacto-benchmark-edit",
	templateUrl: "./benchmark-edit.component.html",
	styleUrls: ["./benchmark-edit.component.scss"],
})
export class BenchmarkEditComponent implements OnInit {
	@ViewChild("seletorImagem", { static: true })
	seletorImagem: SeletorImagemComponent;
	@ViewChild("optionSelecione", { static: true }) optionSelecione;
	@ViewChild("createSucess", { static: true }) createSucess;
	@ViewChild("editSucess", { static: true }) editSucess;
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;

	tiposBenchmark: TipoBenchmark[];
	benchmark: Benchmark;
	operation: string;
	tipoExercicio = [
		{ id: "FOR_TIME", nome: "For Time" },
		{ id: "FOR_REPS", nome: "For Reps and Time" },
		{ id: "FOR_WEIGHT", nome: "For Weight" },
		{ id: "AMRAP", nome: "Amrap" },
	];
	urlImagem: string;
	imagemData: string;

	entity = true;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		tipoBenchmarkId: new FormControl("", [Validators.required]),
		tipoExercicio: new FormControl("0"),
		exercicios: new FormControl("", [Validators.required]),
		observacao: new FormControl(""),
		imagemData: new FormControl(""),
		extensaoImagem: new FormControl(""),
	});

	images: Array<ImagemConfig> = [];
	loading = false;
	salvando: boolean = false;
	imgAulaDataUrl: DataUrl = "";
	manterFoto: boolean = true;

	constructor(
		private tipoBenchmarkService: TreinoApiTipoBenchmarkService,
		private benchmarkService: TreinoApiBenchmarkService,
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService,
		private route: ActivatedRoute,
		private modal: NgbModal,
		private imageCompress: NgxImageCompressService,
		private router: Router
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}
		});
		this.getTiposBenchmark();
	}

	private loadEntities(id) {
		this.getTiposBenchmark();
		if (id) {
			this.benchmarkService.obterBenchmark(id).subscribe((dado) => {
				this.benchmark = dado;
				this.entity = false;
				this.loadForm();
				this.cd.detectChanges();
			});
		}
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.benchmark.nome);
		this.formGroup
			.get("tipoBenchmarkId")
			.setValue(this.benchmark.tipoBenchmark.id);
		this.formGroup.get("tipoExercicio").setValue(this.benchmark.tipoExercicio);
		this.formGroup.get("exercicios").setValue(this.benchmark.exercicios);
		this.formGroup.get("observacao").setValue(this.benchmark.observacao);

		if (this.benchmark.imagemUri) {
			setTimeout(() => {
				this.urlImagem = this.benchmark.imagemUri;
				this.imagemData = this.benchmark.imagemUri;
			});
			this.images.push({
				uri: this.benchmark.imagemUri,
			});
		}
	}

	get Validators() {
		return Validators;
	}

	private getTiposBenchmark() {
		const optionSelecione = this.optionSelecione.nativeElement.innerHTML;
		this.tipoBenchmarkService
			.obterTodosTipoBenchmark()
			.subscribe((data: ApiResponseList<TipoBenchmark>) => {
				data.content.unshift({ id: null, nome: optionSelecione });
				this.tiposBenchmark = data.content;
				this.cd.detectChanges();
			});
	}

	submitHandler() {
		this.markAsTouched();
		if (this.benchmark) {
			this.udpateHandler();
		} else {
			this.createHandler();
		}
	}

	private createHandler() {
		if (this.formGroup.valid) {
			const formulario = this.formGroup.getRawValue();
			delete formulario.url;
			this.images.forEach((img) => {
				formulario.imagemData = img.data;
				formulario.extensaoImagem = "." + img.extensao.toLowerCase();
			});
			const createSucess = this.createSucess.nativeElement.innerHTML;
			this.benchmarkService.criarBenchmark(formulario).subscribe(
				() => {
					this.snotifyService.success(createSucess);
					this.cancelHandler();
				},

				(error) => {
					if (
						error.status === 409 &&
						error.error &&
						error.error.meta &&
						error.error.meta.message
					) {
						const errorMessage = error.error.meta.message;
						this.snotifyService.error(errorMessage);
					} else {
						this.snotifyService.error("Benchmark já existente");
					}
				}
			);
		} else {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
		}
	}

	private udpateHandler() {
		if (this.formGroup.valid) {
			const formulario = this.formGroup.getRawValue();
			delete formulario.url;
			this.images.forEach((img) => {
				formulario.imagemData = img.data;
				formulario.extensaoImagem = "." + img.extensao.toLowerCase();
			});
			const editSucess = this.editSucess.nativeElement.innerHTML;
			this.benchmarkService
				.atualizarBenchmark(this.benchmark.id, formulario)
				.subscribe(() => {
					this.snotifyService.success(editSucess);
					this.cancelHandler();
				});
		} else {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
		}
	}

	cancelHandler() {
		this.router.navigate(["cross", "cadastros", "benchmarks"]);
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("tipoBenchmarkId").markAsTouched();
		this.formGroup.get("exercicios").markAsTouched();
	}

	adicionarImagemHandler() {
		const tamanhoMaxImg = 1024 * 1024 * 1; // 1MB
		const modal = this.modal.open(SeletorImagemAvancadoComponent, {
			size: "lg",
		});
		modal.componentInstance.apresentarCatalogo = false;
		modal.result.then((image: ImagemConfig) => {
			this.imgAulaDataUrl = "";
			if (this.isImageGif(image)) {
				this.snotifyService.error(
					"Formato da imagem não suportado. Por favor, selecione uma imagem JPG ou PNG."
				);
			} else {
				if (this.isTamanhoImagemPermitido(image, tamanhoMaxImg)) {
					this.handleUploadImage(image);
				} else {
					this.obterDataUrlImagem(image)
						.then(() => {
							this.comprimirImagem(tamanhoMaxImg);
							this.handleUploadImage(image);
						})
						.catch((error) => {
							this.handleUploadImage(image);
						});
				}
			}
		});
	}

	obterDataUrlImagem(image: ImagemConfig): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			const file: File = image.data;
			const reader = new FileReader();
			reader.onload = () => {
				this.imgAulaDataUrl = reader.result as string;
				resolve();
			};
			reader.onerror = (error) => reject(error);
			if (file) {
				reader.readAsDataURL(file);
			}
		});
	}

	comprimirImagem(tamanhoMaxImg) {
		console.log(
			"Tamanho da imagem antes de ser comprimida: ",
			this.imgAulaDataUrl.length
		);
		// DOC: https://github.com/dfa1234/ngx-image-compress
		// Devido versão do angular, foi necessário utilizar outra versão mas com os mesmos comandos do link anterior:
		// https://www.npmjs.com/package/ngx-image-compress-legacy
		if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
			this.imageCompress
				.compressFile(this.imgAulaDataUrl, 1, 50, 50)
				.then((result: DataUrl) => {
					this.imgAulaDataUrl = result;
					if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
						this.comprimirImagem(tamanhoMaxImg);
					}
				});
		}
	}

	private isImageGif(image: ImagemConfig) {
		const regex: RegExp = /\.gif$/i;
		return regex.test(image.nome);
	}

	private isTamanhoImagemPermitido(image: ImagemConfig, tamanhoMax: number) {
		const file: File = image.data;
		if (file) {
			if (file.size > tamanhoMax) {
				return false;
			}
		}
		return true;
	}

	private handleUploadImage(image: ImagemConfig) {
		this.loading = true;
		this.images = [];
		const reader: FileReader = new FileReader();
		reader.onload = (event: any) => {
			if (this.imgAulaDataUrl !== undefined && this.imgAulaDataUrl !== "") {
				console.log(
					"Tamanho da imagem após ser comprimida: ",
					this.imgAulaDataUrl.length
				);
				image.uri = this.imgAulaDataUrl;
				image.data = this.imgAulaDataUrl.split(",")[1];
				image.extensao = image.nome.split(".")[1];
			} else {
				image.uri = event.target.result;
				const resultString: string = reader.result as string;
				image.data = resultString.split(",")[1];
				image.extensao = image.nome.split(".")[1];
			}
			this.cd.detectChanges();
		};
		reader.readAsDataURL(image.data);
		this.images.push(image);
		this.loading = false;
	}

	removeImageHandle(index) {
		this.images.splice(index, 1);
		this.manterFoto = false;
	}
}
