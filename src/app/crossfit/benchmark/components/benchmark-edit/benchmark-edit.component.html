<ng-template #cardTitle>
	<span *ngIf="entity" i18n="@@crud-benchmarks:create:title">
		Criar <PERSON>mark
	</span>
	<span *ngIf="!entity" i18n="@@crud-benchmarks:edit:title">
		Editar Benchmark
	</span>
</ng-template>

<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'BENCHMARK',
			menuLink: ['cross', 'cadastros', 'benchmarks']
		}"></pacto-breadcrumbs>
	<pacto-title-card [title]="cardTitle">
		<div class="row">
			<div class="col-md-6">
				<pacto-input
					[control]="formGroup.get('nome')"
					[id]="'nome-benchmark-input'"
					[name]="'nome'"
					i18n-label="@@crud-benchmarks:input:nome:label"
					i18n-mensagem="@@crud-benchmarks:input:nome:mensagem"
					label="Nome do Benchmark"
					mensagem="Definir um nome com pelo menos 3 caracteres."></pacto-input>
			</div>
			<div class="col-md-3">
				<pacto-select
					[control]="formGroup.get('tipoBenchmarkId')"
					[id]="'tipo-benchmark-select'"
					[nome]="'nome'"
					[opcoes]="tiposBenchmark"
					i18n-label="@@crud-benchmarks:select:tipos-benchmark:label"
					i18n-mensagem="@@crud-benchmarks:select:tipo-benchmark:mensagem"
					label="Tipo de Benchmark"
					mensagem="Selecione um tipo de Benchmark."></pacto-select>
			</div>
			<div class="col-md-3">
				<div class="form-group">
					<label
						class="control-label"
						i18n="@@crud-benchmarks:select:tipo-exercicio:label">
						Tipo de Exercício
					</label>
					<select
						[formControl]="formGroup.get('tipoExercicio')"
						class="form-control form-control-sm"
						id="tipo-exercicio-select">
						<option i18n="@@crud-benchmarks:option-selecione" value="0">
							Selecione
						</option>
						<option i18n="@@crossfit:benchmarks:options:time" value="FOR_TIME">
							For Time
						</option>
						<option
							i18n="@@crossfit:benchmarks:options:reps-time"
							value="FOR_REPS">
							For Reps and Time
						</option>
						<option
							i18n="@@crossfit:benchmarks:options:weight"
							value="FOR_WEIGHT">
							For Weight
						</option>
						<option i18n="@@crossfit:benchmarks:options:amrap" value="AMRAP">
							Amrap
						</option>
					</select>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<div class="form-group">
					<pacto-textarea
						[control]="formGroup.get('exercicios')"
						[id]="'exercicios-benchmark-text-area'"
						[name]="'exercicios'"
						i18n-label="@@crud-benchmarks:textarea:exercicio:label"
						i18n-placeholder="@@crud-benchmarks:textarea:exercicio:placeholder"
						label="Exercicios"
						placeholder="Descreva aqui os exercícios..."></pacto-textarea>
					<pacto-textarea
						[control]="formGroup.get('observacao')"
						[id]="'observacao-benchmark-text-area'"
						[name]="'observacao'"
						i18n-label="@@crud-benchmarks:textarea:observacao:label"
						i18n-placeholder="@@crud-benchmarks:textarea:observacao:mensagem"
						label="Observação"
						placeholder="Descreva aqui dicas e informações adicionais..."></pacto-textarea>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12">
				<div class="form-group images-wrapper">
					<div class="header-row">
						<label class="control-label" i18n="@@crud-atividade:imagem:label">
							Imagem
						</label>
						<button
							(click)="adicionarImagemHandler()"
							class="btn btn-sm btn-secondary"
							i18n="@@crud-atividade:imagem:button"
							id="btnAddImagem">
							Adicionar Imagem
						</button>
					</div>
					<div class="image-list">
						<div
							*ngFor="let imagem of images; let index = index"
							class="imagem">
							<div class="imagem-wrapper">
								<img src="{{ imagem.uri }}" />
							</div>
							<div class="control-footer">
								<div class="name">
									{{ imagem.nome }}
								</div>
								<div class="icon">
									<i
										(click)="removeImageHandle(index)"
										_ngcontent-c22=""
										class="fa fa-trash-o"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<br />
		<div class="actions">
			<button
				(click)="submitHandler()"
				class="btn btn-primary"
				i18n="@@buttons:salvar"
				id="btn-add-benchmark">
				Salvar
			</button>
			<button
				(click)="cancelHandler()"
				class="btn btn-secondary"
				i18n="@@buttons:cancelar">
				Cancelar
			</button>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<span
	#optionSelecione
	[hidden]="true"
	i18n="@@crud-benchmarks:option-selecione">
	Selecione
</span>
<span
	#createSucess
	[hidden]="true"
	i18n="@@crud-benchmarks:mensagem:createSucess">
	Benchmark criado com sucesso.
</span>
<span #editSucess [hidden]="true" i18n="@@crud-benchmarks:mensagem:editSucess">
	Benchmark editado com sucesso.
</span>
<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crud-benchmarks:campos-obrigatorios">
	Campos obrigatórios não preenchido.
</span>
