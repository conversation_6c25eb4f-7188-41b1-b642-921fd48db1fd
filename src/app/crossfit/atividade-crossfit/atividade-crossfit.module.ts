import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AtividadeCrossfitEditComponent } from "./components/atividade-crossfit-edit/atividade-crossfit-edit.component";
import { AtividadeCrossfitListaComponent } from "./components/atividade-crossfit-lista/atividade-crossfit-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Routes } from "@angular/router";
import { AtividadeCrossfitEmpresaComponent } from "./components/atividade-crossfit-empresa/atividade-crossfit-empresa.component";
import { AtividadeCrossfitEmpresaConfigComponent } from "./components/atividade-crossfit-empresa-config/atividade-crossfit-empresa-config.component";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const funcionalidade = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.ATIVIDADES_WOD,
	true
);

const routes: Routes = [
	{
		path: "",
		component: AtividadeCrossfitListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: "adicionar",
		component: AtividadeCrossfitEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: ":id",
		component: AtividadeCrossfitEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
];

@NgModule({
	imports: [BaseSharedModule, CommonModule, RouterModule.forChild(routes)],
	entryComponents: [AtividadeCrossfitEmpresaConfigComponent],
	declarations: [
		AtividadeCrossfitEditComponent,
		AtividadeCrossfitListaComponent,
		AtividadeCrossfitEmpresaComponent,
		AtividadeCrossfitEmpresaConfigComponent,
	],
})
export class AtividadeCrossfitModule {}
