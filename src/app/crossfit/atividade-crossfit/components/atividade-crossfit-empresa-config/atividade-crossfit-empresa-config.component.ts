import { Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup, Validators } from "@angular/forms";

@Component({
	selector: "pacto-atividade-crossfit-empresa-config",
	templateUrl: "./atividade-crossfit-empresa-config.component.html",
	styleUrls: ["./atividade-crossfit-empresa-config.component.scss"],
})
export class AtividadeCrossfitEmpresaConfigComponent implements OnInit {
	constructor(private modal: NgbActiveModal) {}

	formGroup: FormGroup = new FormGroup({
		empresaControl: new FormControl("", [Validators.required]),
		idControl: new FormControl(),
	});

	empresas: Array<any> = [];

	ngOnInit() {}

	dismiss() {
		this.modal.dismiss();
	}

	confirmar() {
		this.formGroup.get("empresaControl").markAsTouched();
		this.formGroup.get("idControl").markAsTouched();
		if (this.formGroup.valid) {
			const empresaId = this.formGroup.get("empresaControl").value;
			const empresa = this.empresas.find((i) => {
				if (parseInt(i.id, 10) === parseInt(empresaId, 10)) {
					return true;
				}
			});
			const out = {
				empresa,
				identificador: this.formGroup.get("idControl").value,
			};
			this.modal.close(out);
		}
	}
}
