import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { AtividadeConfigEmpresa } from "treino-api";

@Component({
	selector: "pacto-atividade-crossfit-empresa",
	templateUrl: "./atividade-crossfit-empresa.component.html",
	styleUrls: ["./atividade-crossfit-empresa.component.scss"],
})
export class AtividadeCrossfitEmpresaComponent implements OnInit {
	@Input() empresas: Array<AtividadeConfigEmpresa>;
	@Output() criarNovo: EventEmitter<boolean> = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	adicionar() {
		this.criarNovo.emit(true);
	}

	removeHandler(index) {
		this.empresas.splice(index, 1);
	}
}
