import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ActivatedRoute, Router } from "@angular/router";
import { AtividadeCrossfitEmpresaConfigComponent } from "../atividade-crossfit-empresa-config/atividade-crossfit-empresa-config.component";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import {
	EmpresaFinanceiro,
	AtividadeConfigEmpresa,
	AtividadeCrossfit,
	TreinoApiAtividadeCrossfitService,
	TreinoApiEmpresaService,
} from "treino-api";

@Component({
	selector: "pacto-atividade-crossfit-edit",
	templateUrl: "./atividade-crossfit-edit.component.html",
	styleUrls: ["./atividade-crossfit-edit.component.scss"],
})
export class AtividadeCrossfitEditComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("duplicateRecord", { static: true }) duplicateRecord;
	@ViewChild("editSuccess", { static: true }) editSuccess;

	categorias = [
		"BARBELL",
		"GYMNASTIC",
		"ENDURANCE",
		"NOTABLES",
		"GIRLS",
		"OPEN",
		"THEHEROES",
		"CAMPEONATOS",
		"CROSSFIT_GAMES",
	];
	unidadesMedida = [
		"REPS",
		"TIME",
		"DISTANCE",
		"WEIGHT",
		"CALS",
		"REPSFORTIME",
		"ROUNDS",
		"ROUNDS_REPS",
	];
	empresasConfiguradas: Array<AtividadeConfigEmpresa> = [];

	empresas: Array<EmpresaFinanceiro> = [];
	atividade: AtividadeCrossfit;
	entity = true;
	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required]),
		categoria: new FormControl("BARBELL", [Validators.required]),
		unidadeMedida: new FormControl("REPS", [Validators.required]),
		ativo: new FormControl(true),
		empresas: new FormControl(null),
		descricao: new FormControl(""),
		videoUri: new FormControl(""),
	});

	constructor(
		private modal: NgbModal,
		private empresaService: TreinoApiEmpresaService,
		private router: Router,
		private atividadeService: TreinoApiAtividadeCrossfitService,
		private route: ActivatedRoute,
		private rest: RestService,
		private notify: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			if (params.id) {
				this.loadEntite(params.id);
				this.entity = false;
			} else {
				this.entity = true;
			}
		});
		this.loadListaData();
	}

	submiteHandler() {
		this.markAsTouched();
		if (this.formGroup.valid) {
			if (this.entity) {
				this.createHandler();
			} else {
				this.editHandler();
			}
		} else {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.notify.error(camposObrigatorios);
		}
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/atividades/${this.atividade.id}`);
	}

	novaEmpresaConfigHandler() {
		const modal = this.modal.open(AtividadeCrossfitEmpresaConfigComponent);
		modal.componentInstance.empresas = this.obterEmpresasNaoUtilizadas();
		modal.result.then(
			(atividadeConfig) => {
				this.empresasConfiguradas.push(atividadeConfig);
			},
			() => {}
		);
	}

	cancelHandler() {
		this.router.navigate(["cross", "cadastros", "atividades-cross"]);
	}

	get showErrorCategoria() {
		if (this.formGroup.get("categoria")) {
			return (
				!this.formGroup.get("categoria").valid &&
				this.formGroup.get("categoria").touched
			);
		} else {
			return false;
		}
	}

	get showErrorUnidadeMedida() {
		if (this.formGroup.get("unidadeMedida")) {
			return (
				!this.formGroup.get("unidadeMedida").valid &&
				this.formGroup.get("unidadeMedida").touched
			);
		} else {
			return false;
		}
	}

	private createHandler() {
		const dto = this.obterDto();
		this.atividadeService.criarAtividade(dto).subscribe((resultCreate) => {
			if (resultCreate && resultCreate.toString() === "registro_duplicado") {
				this.notify.error(this.duplicateRecord.nativeElement.innerHTML);
			} else if (resultCreate) {
				this.notify.success(this.createSuccess.nativeElement.innerHTML);
				this.cancelHandler();
			}
		});
	}

	private editHandler() {
		const dto = this.obterDto();
		const editSuccess = this.editSuccess.nativeElement.innerHTML;
		this.atividadeService.editarAtividade(dto).subscribe((resultEdit) => {
			if (resultEdit && resultEdit.toString() === "registro_duplicado") {
				this.notify.error(this.duplicateRecord.nativeElement.innerHTML);
			} else if (resultEdit) {
				this.notify.success(editSuccess);
				this.cancelHandler();
			}
		});
	}

	private obterDto() {
		const dto: any = this.formGroup.getRawValue();
		dto.id = this.atividade ? this.atividade.id : null;
		if (!dto.ativo) {
			dto.ativo = false;
		}

		if (this.empresasConfiguradas.length) {
			dto.empresas = [];
			this.empresasConfiguradas.forEach((config) => {
				dto.empresas.push({
					id: config.id,
					identificador: config.identificador,
					empresa: config.empresa,
				});
			});
		}

		return dto;
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("categoria").markAsTouched();
		this.formGroup.get("unidadeMedida").markAsTouched();
	}

	private loadEntite(atividadeId: number) {
		this.atividadeService.obterAtividade(atividadeId).subscribe((result) => {
			this.atividade = result;
			this.loadForm();
		});
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.atividade.nome);
		this.formGroup.get("categoria").setValue(this.atividade.categoria);
		this.formGroup.get("unidadeMedida").setValue(this.atividade.unidadeMedida);
		this.formGroup.get("descricao").setValue(this.atividade.descricao);
		this.formGroup.get("videoUri").setValue(this.atividade.videoUri);
		this.formGroup.get("ativo").setValue(this.atividade.ativo);
		this.empresasConfiguradas = [];
		this.atividade.empresas.forEach((empresa) => {
			this.empresasConfiguradas.push(empresa);
		});
		this.cd.detectChanges();
	}

	private obterEmpresasNaoUtilizadas() {
		const result = [];
		this.empresas.forEach((empresa) => {
			let found = false;
			this.empresasConfiguradas.forEach((config) => {
				if (
					parseInt(`${config.empresa.id}`, 10) === parseInt(`${empresa.id}`, 10)
				) {
					found = true;
				}
			});
			if (!found) {
				result.push(empresa);
			}
		});
		return result;
	}

	private loadListaData() {
		this.empresaService.obterTodasEmpresas().subscribe((result) => {
			this.empresas = result;
		});
	}
}
