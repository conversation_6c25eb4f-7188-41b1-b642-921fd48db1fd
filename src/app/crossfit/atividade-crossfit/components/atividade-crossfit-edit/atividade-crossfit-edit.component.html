<ng-template #cardTitle>
	<span *ngIf="entity" i18n="@@crud-atividade-crossfit:cadastrar-atividade">
		Cadastrar Atividade
	</span>
	<span *ngIf="!entity" i18n="@@crud-atividade-crossfit:editar-atividade">
		Editar Atividade
	</span>
</ng-template>
<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			categoryLink: ['cadastros', 'atividades-cross'],
			menu: 'ATIVIDADE',
			menuLink: ['cross', 'cadastros', 'atividades-cross']
		}">
		}" >
	</pacto-breadcrumbs>

	<pacto-title-card [title]="cardTitle">
		<ng-container [ngTemplateOutlet]="formContent"></ng-container>
	</pacto-title-card>
</pacto-cat-layout-v2>

<ng-template #formContent>
	<div class="form-wrapper">
		<div class="row">
			<div class="col-md-10">
				<pacto-input
					[control]="formGroup.get('nome')"
					[id]="'nome-atividade-crossfit-input'"
					i18n-label="@@crud-atividade-crossfit:nome:label"
					i18n-mensagem="@@crud-atividade-crossfit:nome:mensagem"
					i18n-placeholder="@@crud-atividade-crossfit:nome:placeholder"
					label="Nome"
					mensagem="O nome da atividade é obrigatório"
					placeholder="Nome da atividade"></pacto-input>
			</div>
			<div class="col-md-2">
				<div class="form-group">
					<div class="form-check">
						<input
							[formControl]="formGroup.get('ativo')"
							class="form-check-input"
							id="defaultCheck1"
							type="checkbox"
							value="" />
						<label
							class="form-check-label"
							for="defaultCheck1"
							i18n="@@crud-atividade-crossfit:ativa:label">
							Atividade ativa ?
						</label>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div
				[ngClass]="{
					'has-danger': showErrorCategoria
				}"
				class="col-md-6 form-group">
				<label
					class="control-label"
					i18n="@@crud-atividade-crossfit:categoria-atividade:label">
					Categoria da Atividade
				</label>
				<select
					[formControl]="formGroup.get('categoria')"
					class="form-control form-control-sm"
					id="categoria-atividade-select">
					<option *ngFor="let categoria of categorias" value="{{ categoria }}">
						<ng-container
							*ngTemplateOutlet="
								traducaoCategorias;
								context: { traducaoCategoria: categoria }
							"></ng-container>
					</option>
				</select>
				<small
					*ngIf="showErrorCategoria"
					class="form-control-feedback"
					i18n="@@crud-atividade-crossfit:categoria-atividade:mensagem">
					Categoria é obrigatório
				</small>
			</div>
			<div
				[ngClass]="{
					'has-danger': showErrorUnidadeMedida
				}"
				class="col-md-6 form-group">
				<label
					class="control-label"
					i18n="@@crud-atividade-crossfit:unidade-medida:label">
					Unidade de Medida
				</label>
				<select
					[formControl]="formGroup.get('unidadeMedida')"
					class="form-control form-control-sm"
					id="unidade-medida-select">
					<option
						*ngFor="let unidadeMedida of unidadesMedida"
						value="{{ unidadeMedida }}">
						<ng-container
							*ngTemplateOutlet="
								traducaoUnidades;
								context: { traducaoUnidade: unidadeMedida }
							"></ng-container>
					</option>
				</select>
				<small
					*ngIf="showErrorUnidadeMedida"
					class="form-control-feedback"
					i18n="@@crud-atividade-crossfit:unidade-medida:mensagem">
					Unidade de medida é obrigatório
				</small>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-atividade-crossfit-empresa
					(criarNovo)="novaEmpresaConfigHandler()"
					[empresas]="empresasConfiguradas"></pacto-atividade-crossfit-empresa>
			</div>
			<div class="col-md-6">
				<pacto-textarea
					[control]="formGroup.get('descricao')"
					[id]="'descricao-atividade-text-area'"
					i18n-label="@@crud-atividade-crossfit:descricao:label"
					i18n-placeholder="@@crud-atividade-crossfit:descricao:placeholder"
					label="Descrição"
					placeholder="Informe uma descrição"></pacto-textarea>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<pacto-input
					[control]="formGroup.get('videoUri')"
					[id]="'link-youtube-atividade-input'"
					i18n-label="@@crud-atividade-crossfit:video:label"
					i18n-placeholder="@@crud-atividade-crossfit:video:placeholder"
					label="Vídeo (Link do YouTube)"
					placeholder="Informe a URL do video"></pacto-input>
			</div>
		</div>
	</div>

	<div class="actions">
		<button
			(click)="submiteHandler()"
			class="btn btn-primary"
			i18n="@@buttons:salvar"
			id="btn-add-atividade-crossfit">
			Salvar
		</button>
		<button
			(click)="cancelHandler()"
			class="btn btn-secondary"
			i18n="@@buttons:cancelar">
			Cancelar
		</button>
		<pacto-log *ngIf="atividade?.id" [url]="urlLog"></pacto-log>
	</div>
</ng-template>

<ng-template #traducaoCategorias let-traducaoCategoria="traducaoCategoria">
	<ng-container [ngSwitch]="traducaoCategoria">
		<span
			*ngSwitchCase="'BARBELL'"
			i18n="@@crud-atividade-crossfit:barbell:option-categoria">
			Barbell
		</span>
		<span
			*ngSwitchCase="'GYMNASTIC'"
			i18n="@@crud-atividade-crossfit:gymnastic:option-categoria">
			Gymnastic
		</span>
		<span
			*ngSwitchCase="'ENDURANCE'"
			i18n="@@crud-atividade-crossfit:endurance:option-categoria">
			Endurance
		</span>
		<span
			*ngSwitchCase="'NOTABLES'"
			i18n="@@crud-atividade-crossfit:notables:option-categoria">
			Notables
		</span>
		<span
			*ngSwitchCase="'GIRLS'"
			i18n="@@crud-atividade-crossfit:girls:option-categoria">
			Girls
		</span>
		<span
			*ngSwitchCase="'OPEN'"
			i18n="@@crud-atividade-crossfit:open:option-categoria">
			Open
		</span>
		<span
			*ngSwitchCase="'THEHEROES'"
			i18n="@@crud-atividade-crossfit:the-heroes:option-categoria">
			The Heroes
		</span>
		<span
			*ngSwitchCase="'CAMPEONATOS'"
			i18n="@@crud-atividade-crossfit:campeonatos:option-categoria">
			Campeonatos
		</span>
		<span
			*ngSwitchCase="'CROSSFIT_GAMES'"
			i18n="@@crud-atividade-crossfit:crossfit-games:option-categoria">
			Cross Games
		</span>
	</ng-container>
</ng-template>

<ng-template #traducaoUnidades let-traducaoUnidade="traducaoUnidade">
	<ng-container [ngSwitch]="traducaoUnidade">
		<span
			*ngSwitchCase="'REPS'"
			i18n="@@crud-atividade-crossfit:reps:option-unidades">
			Reps
		</span>
		<span
			*ngSwitchCase="'TIME'"
			i18n="@@crud-atividade-crossfit:time:option-unidades">
			Time
		</span>
		<span
			*ngSwitchCase="'DISTANCE'"
			i18n="@@crud-atividade-crossfit:distance:option-unidades">
			Distance
		</span>
		<span
			*ngSwitchCase="'WEIGHT'"
			i18n="@@crud-atividade-crossfit:weight:option-unidades">
			Weight
		</span>
		<span
			*ngSwitchCase="'CALS'"
			i18n="@@crud-atividade-crossfit:cals:option-unidades">
			Cals
		</span>
		<span
			*ngSwitchCase="'REPSFORTIME'"
			i18n="@@crud-atividade-crossfit:reps-for-time:option-unidades">
			Reps For Time
		</span>
		<span
			*ngSwitchCase="'ROUNDS'"
			i18n="@@crud-atividade-crossfit:rounds:option-unidades">
			Rounds
		</span>
		<span
			*ngSwitchCase="'ROUNDS_REPS'"
			i18n="@@crud-atividade-crossfit:rounds-and-reps:option-unidades">
			Rounds and Reps
		</span>
	</ng-container>
</ng-template>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:campos-obrigatorios">
	Campos obrigatórios não preenchidos.
</span>
<span
	#createSuccess
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:atividade-criada:mensagem-success">
	Atividade criada com sucesso.
</span>
<span
	#duplicateRecord
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:atividade-duplicate">
	Já existe uma atividade com este mesmo nome.
</span>
<span
	#editSuccess
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:atividade-editada:mensagem-success">
	Atividade editada com sucesso.
</span>
