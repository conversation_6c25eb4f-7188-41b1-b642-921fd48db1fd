<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler($event)"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="rowClickHandler($event)"
			[sessionService]="sessionServive"
			[tableDescription]="subtituloPage"
			[tableTitle]="tituloPage"
			[table]="table"
			telaId="crossAparelhos"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<!--title page-->
<ng-template #tituloPage i18n="@@crud-atividade-crossfit:atividade:title-page">
	Atividade
</ng-template>
<ng-template
	#subtituloPage
	i18n="@@crud-atividade-crossfit:gerencie-atividades:subtitle-page">
	Gerencie as atividades cadastradas
</ng-template>
<!--end title page-->

<!--nome colunas-->
<ng-template #colunaNome i18n="@@crud-atividade-crossfit:nome:title-column">
	Nome
</ng-template>
<ng-template
	#colunaSituacao
	i18n="@@crud-atividade-crossfit:situacao:title-column">
	Situação
</ng-template>
<!--end nome colunas-->

<!--tooltip icons-->
<span
	#tooltipAtivar
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:ativar:tooltip-icon">
	Ativar
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
<!--end tooltip icons-->

<!--result coluna situacao-->
<ng-template #statusColumn let-item="item">
	<span *ngIf="item.ativo" i18n="@@crud-atividade-crossfit:ativo:result">
		Ativo
	</span>
	<span *ngIf="!item.ativo" i18n="@@crud-atividade-crossfit:inativo:result">
		Inativo
	</span>
</ng-template>
<!--end result coluna situacao-->

<!--name buttons-->
<ng-template #addLabel i18n="@@crud-atividade-crossfit:adicionar:label-button">
	Adicionar
</ng-template>
<!--end name buttons-->

<span
	#ativarModalTitle
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:ativar-atividade:title-modal">
	Ativar Atividade?
</span>
<span
	#ativarModalBody
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:deseja-ativar:body-modal">
	Deseja ativar a atividade {{ nomeAtividade }}?
</span>
<span
	#ativarSuccess
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:atividade-ativada:mensage-modal">
	Atividade ativida com sucesso.
</span>
<span #ativarBtn [hidden]="true">Ativar</span>

<span
	#inativarModalTitle
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:inativar-atividade:title-modal">
	Inativar Atividade?
</span>
<span
	#inativarModalBody
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:deseja-inativar:body-modal">
	Deseja inativar a atividade {{ nomeAtividade }}?
</span>
<span
	#inativarSuccess
	[hidden]="true"
	i18n="@@crud-atividade-crossfit:atividade-inativada:message-modal">
	Atividade inativada com sucesso.
</span>
<span #inativarBtn [hidden]="true">Inativar</span>
