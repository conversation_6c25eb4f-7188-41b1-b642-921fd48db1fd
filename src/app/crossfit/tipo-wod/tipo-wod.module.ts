import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { TipoWodListaComponent } from "./components/tipo-wod-lista/tipo-wod-lista.component";
import { TipoWodEditComponent } from "./components/tipo-wod-edit/tipo-wod-edit.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { DragulaModule, DragulaService } from "ng2-dragula";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const funcionalidade = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.CADASTRAR_TIPO_WOD,
	true
);

const routes: Routes = [
	{
		path: "",
		component: TipoWodListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: "adicionar",
		component: TipoWodEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: ":id",
		component: TipoWodEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		DragulaModule,
		CommonModule,
	],
	declarations: [TipoWodListaComponent, TipoWodEditComponent],
	providers: [DragulaService],
})
export class TipoWodModule {}
