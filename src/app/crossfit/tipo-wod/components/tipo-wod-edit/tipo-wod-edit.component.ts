import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { OrdenamentosEnum, TipoWod, TreinoApiTipoWodService } from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-tipo-wod-edit",
	templateUrl: "./tipo-wod-edit.component.html",
	styleUrls: ["./tipo-wod-edit.component.scss"],
})
export class TipoWodEditComponent implements OnInit {
	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@ViewChild("tipoWodLabels", { static: true })
	tipoWodLabels: TraducoesXinglingComponent;
	@ViewChild("conflito", { static: true }) conflito;

	ORDENAMENTOS = "ORDENAMENTOS";
	tipoWod: TipoWod = new (class implements TipoWod {
		id: string;
		nome: string;
		ordenamentosSelecionados: Array<OrdenamentosEnum> = [];
		profissionalEmPrimeiro: boolean;
		usarRanking: boolean;
	})();
	operation: string;
	error;

	tipoWodFormName: string;
	tipoWodFormDesc: string;
	selectCrossfitOption: boolean;

	ordenamentos: Array<OrdenamentosEnum> = [
		OrdenamentosEnum.TEMPO,
		OrdenamentosEnum.PESO,
		OrdenamentosEnum.ROUNDS,
		OrdenamentosEnum.REPETICOES,
	];
	ordenamentosSelecionados: Array<OrdenamentosEnum> = [];
	ordenamentosNaoSelecionados: Array<OrdenamentosEnum> = [];
	entity = true;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		utilizarRanking: new FormControl(""),
		profissionais: new FormControl(true),
	});

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private tipoWodService: TreinoApiTipoWodService,
		private snotifyService: SnotifyService,
		private configurationService: TreinoConfigCacheService
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			if (id) {
				this.loadEntities(id);
				this.operation = "edit";
			} else {
				this.operation = "create";
				this.preencherListaOrdenamentos();
			}
		});
		setTimeout(() => {
			this.updateHeader();
		});
	}

	submitHandler() {
		this.markAsTouched();
		if (!this.entity) {
			this.updateHandler();
		} else {
			this.createHandler();
		}
	}

	cancelHandler() {
		this.router.navigate(["cross", "cadastros", "tipos-wod"]);
	}

	private updateHandler() {
		if (this.formGroup.valid) {
			const createSuccess = this.notificacoesLabels.getLabel("createSuccess");
			this.tipoWodService
				.atualizarTipoWod(this.createEntity())
				.subscribe((result) => {
					this.snotifyService.success(createSuccess);
					this.cancelHandler();
				});
		} else {
			const validMensage = this.notificacoesLabels.getLabel("validMensage");
			this.snotifyService.error(validMensage);
		}
	}

	private createHandler() {
		if (this.formGroup.valid) {
			const createSuccess = this.notificacoesLabels.getLabel("createSuccess");
			this.tipoWodService
				.criarTipoWod(this.createEntity())
				.subscribe((result) => {
					this.snotifyService.success(createSuccess);
					this.cancelHandler();
				});
		} else {
			const validMensage = this.notificacoesLabels.getLabel("validMensage");
			this.snotifyService.error(validMensage);
		}
	}

	private loadEntities(id) {
		this.tipoWodService.obterTipoWod(id).subscribe((dados) => {
			this.tipoWod = dados;
			this.entity = false;
			this.loadForm();
		});
	}

	private loadForm() {
		if (!this.tipoWod.preDefinido) {
			this.formGroup.get("nome").setValue(this.tipoWod.nome);
			this.formGroup.get("utilizarRanking").setValue(this.tipoWod.usarRanking);
			this.formGroup
				.get("profissionais")
				.setValue(this.tipoWod.profissionalEmPrimeiro);
			this.preencherListaOrdenamentos();
		} else {
			this.cancelHandler();
		}
	}

	private createEntity() {
		this.tipoWod.nome = this.formGroup.get("nome").value;
		this.tipoWod.usarRanking = this.formGroup.get("utilizarRanking").value;
		if (this.tipoWod.usarRanking) {
			this.tipoWod.profissionalEmPrimeiro =
				this.formGroup.get("profissionais").value;
			const copy = Object.assign([], this.ordenamentosSelecionados);
			if (this.formGroup.get("profissionais").value === true) {
				copy.unshift(OrdenamentosEnum.PROFISSIONAIS);
			}
			this.tipoWod.ordenamentosSelecionados = copy;
		} else {
			this.tipoWod.ordenamentosSelecionados = [];
		}
		return this.tipoWod;
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
	}

	private preencherListaOrdenamentos() {
		this.ordenamentos.forEach((ordenamento) => {
			if (!this.ordenamentoSelecionado(ordenamento)) {
				this.ordenamentosNaoSelecionados.push(ordenamento);
			}
		});
		this.ordenamentosSelecionados = this.tipoWod.ordenamentosSelecionados;
		this.cd.detectChanges();
	}

	private ordenamentoSelecionado(ordenamento: string) {
		if (
			this.tipoWod.ordenamentosSelecionados &&
			this.tipoWod.ordenamentosSelecionados.length > 0
		) {
			for (const ordenamentoSelecionado of this.tipoWod
				.ordenamentosSelecionados) {
				if (ordenamentoSelecionado === ordenamento) {
					return true;
				}
			}
			return false;
		} else {
			return false;
		}
	}

	private updateHeader() {
		this.selectCrossfitOption =
			this.configurationService.configuracoesGerais.troca_nomenclatura_crossfit.toString() ===
			"true";
		if (this.selectCrossfitOption) {
			this.tipoWodFormName = this.tipoWodLabels.getLabel(
				"periodizacaoLabelName"
			);
			this.tipoWodFormDesc = this.tipoWodLabels.getLabel(
				"periodizacaoLabelDesc"
			);
		} else {
			this.tipoWodFormName = this.tipoWodLabels.getLabel("tipoWodLabelName");
			this.tipoWodFormDesc = this.tipoWodLabels.getLabel("tipoWodLabelDesc");
		}
		this.cd.detectChanges();
	}
}
