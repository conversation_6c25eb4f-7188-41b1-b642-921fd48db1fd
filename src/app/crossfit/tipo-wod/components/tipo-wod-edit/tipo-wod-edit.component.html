<ng-template #cardTitle>
	<span *ngIf="entity" i18n="@@crossfit:crud-tipos-wod:criar-tipo-wod:title">
		Criar {{ tipoWodFormName }}
	</span>
	<span *ngIf="!entity" i18n="@@crossfit:crud-tipos-wod:editar-tipo-wod:title">
		Editar {{ tipoWodFormName }}
	</span>
</ng-template>
<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: tipoWodFormName,
			menuLink: ['cross', 'cadastros', 'tipos-wod']
		}"></pacto-breadcrumbs>

	<pacto-title-card [title]="cardTitle">
		<div class="row">
			<div class="col-md-6">
				<pacto-input
					[control]="formGroup.get('nome')"
					[label]="tipoWodFormName"
					i18n-label="@@crossfit:crud-tipos-wod:input:nome:label"
					i18n-mensagem="@@crossfit:crud-tipor-wod:input:nome:mensagem"
					mensagem="Definir um nome com pelo menos 3 caracteres."
					name="nome"></pacto-input>
			</div>
			<div class="col-md-6 tipo-wod-confirm-ranking">
				<div class="form-check form-check-inline">
					<input
						[formControl]="formGroup.get('utilizarRanking')"
						class="form-check-input"
						id="inlineRadio3"
						name="name"
						type="checkbox"
						value="SIM" />
					<label
						class="form-check-label"
						for="inlineRadio3"
						i18n="@@crossfit:crud-tipos-wod:checkBox:usarRanking:label">
						Vai utilizar ranking?
					</label>
				</div>
				<div
					*ngIf="formGroup.get('utilizarRanking').value === true"
					class="form-check form-check-inline">
					<input
						[formControl]="formGroup.get('profissionais')"
						class="form-check-input"
						id="inlineRadio4"
						name="name"
						type="checkbox"
						value="SIM" />
					<label
						class="form-check-label"
						for="inlineRadio4"
						i18n="@@crossfit:crud-tipos-wod:checkBox:profissional:label">
						Priorizar nível na ordenação
					</label>
				</div>
			</div>
		</div>

		<div *ngIf="formGroup.get('utilizarRanking').value === true" class="row">
			<div class="col-md-6">
				<div class="title-label">
					<label
						class="control-label"
						i18n="@@crossfit:crud-tipos-wod:ordenamentos:label">
						Ordenamentos
					</label>
				</div>
				<div
					[(dragulaModel)]="ordenamentosNaoSelecionados"
					[dragula]="ORDENAMENTOS"
					class="container-ordenamento tamanho-dragula">
					<div
						*ngFor="let ordem of ordenamentosNaoSelecionados"
						class="tipo-wod-conteudo-ordenamento">
						<ng-container [ngSwitch]="ordem">
							<span
								*ngSwitchCase="'TEMPO'"
								i18n="@@crossfit:crud-tipos-wod:ordenamentos:tempo:option"
								id="btn-add-tempo">
								Tempo
							</span>
							<span
								*ngSwitchCase="'REPETICOES'"
								i18n="@@crossfit:crud-tipos-wod:ordenamentos:repeticoes:option"
								id="btn-add-reps">
								Repetições
							</span>
							<span
								*ngSwitchCase="'ROUNDS'"
								i18n="@@crossfit:crud-tipos-wod:ordenamentos:rounds:option"
								id="btn-add-rounds">
								Rounds
							</span>
							<span
								*ngSwitchCase="'PESO'"
								i18n="@@crossfit:crud-tipos-wod:ordenamentos:peso:option"
								id="btn-add-peso">
								Peso
							</span>
						</ng-container>
					</div>
				</div>
			</div>

			<div class="col-md-6">
				<div class="title-label">
					<label
						class="control-label"
						i18n="@@crossfit:crud-tipos-wod:ordenamentos-selecionados:label">
						Ordenamentos Selecionados
					</label>
				</div>
				<div class="container-ordenamento" id="variavel-add">
					<div
						*ngIf="formGroup.get('profissionais').value === true"
						class="tipo-wod-conteudo-ordenamento quadro-profissional">
						<i class="fa fa-lock position-icon"></i>
						<div
							class="position-text"
							i18n="@@crossfit:crus-tipos-wod:profissional:option">
							Por nível
						</div>
					</div>
					<div
						[(dragulaModel)]="ordenamentosSelecionados"
						[dragula]="ORDENAMENTOS"
						class="tamanho-dragula">
						<div
							*ngFor="
								let selecionados of ordenamentosSelecionados;
								let index = index
							"
							class="tipo-wod-conteudo-ordenamento">
							<ng-container [ngSwitch]="selecionados">
								<span
									*ngSwitchCase="'TEMPO'"
									i18n="@@crossfit:crud-tipos-wod:ordenamentos:tempo:option">
									Tempo
								</span>
								<span
									*ngSwitchCase="'REPETICOES'"
									i18n="
										@@crossfit:crud-tipos-wod:ordenamentos:repeticoes:option">
									Repetições
								</span>
								<span
									*ngSwitchCase="'ROUNDS'"
									i18n="@@crossfit:crud-tipos-wod:ordenamentos:rounds:option">
									Rounds
								</span>
								<span
									*ngSwitchCase="'PESO'"
									i18n="@@crossfit:crud-tipos-wod:ordenamentos:peso:option">
									Peso
								</span>
							</ng-container>
						</div>
					</div>
				</div>
			</div>
		</div>

		<br />

		<div class="actions">
			<button
				(click)="submitHandler()"
				class="btn btn-primary"
				i18n="@@buttons:salvar">
				Salvar
			</button>
			<button
				(click)="cancelHandler()"
				class="btn btn-secondary"
				i18n="@@buttons:cancelar">
				Cancelar
			</button>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<div #conflito [hidden]="true" id="help-message-duplicated-entity-name">
	Já existe um cadastro com esse mesmo nome
</div>

<pacto-traducoes-xingling #notificacoesLabels>
	<span xingling="createSuccess">
		{{ tipoWodFormName }} criada com sucesso.
	</span>
	<span xingling="editSuccess">{{ tipoWodFormName }} editada com sucesso.</span>
	<span xingling="validMensage">Campos obrigatórios não preenchido.</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #tipoWodLabels>
	<span xingling="tipoWodLabelName">Tipo Wod</span>
	<span xingling="tipoWodLabelDesc">Gerencie os tipos de Wods cadastrados</span>
	<span xingling="periodizacaoLabelName">Periodização</span>
	<span xingling="periodizacaoLabelDesc">Gerencie as periodizações</span>
</pacto-traducoes-xingling>
