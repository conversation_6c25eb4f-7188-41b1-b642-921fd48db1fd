<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		*ngIf="tipoWodFormName == 'Tipo Wod'"
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'TIPO-WOD'
		}"></pacto-breadcrumbs>
	<pacto-breadcrumbs
		*ngIf="tipoWodFormName == 'Periodização'"
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'Periodização'
		}"></pacto-breadcrumbs>

	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="editHandler($event)"
			*ngIf="table"
			[sessionService]="sessionServive"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="crossTipoWod"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span *ngIf="true">{{ tipoWodFormName }}</span>
</ng-template>

<ng-template #subtitulo>
	<span *ngIf="true">{{ tipoWodFormDesc }}</span>
</ng-template>

<!--End title table-->
<!--clumns Name-->
<ng-template #columnNome>
	<span i18n="@@crud-benchmarks:table:nome">Nome</span>
</ng-template>
<!--End columns Name-->
<ng-template #novoTipoWod>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>

<pacto-traducoes-xingling #notificacoesLabels>
	<span xingling="removeModalTitle">Remover {{ tipoWodFormName }}?</span>
	<span xingling="removeModalBody">
		Deseja remover {{ tipoWodFormName }} {{ nomeTipoWod }}?
	</span>
	<span xingling="removeSuccess">
		{{ tipoWodFormName }} removido com sucesso.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #tipoWodLabels>
	<span xingling="tipoWodLabelName">Tipo Wod</span>
	<span xingling="tipoWodLabelDesc">Gerencie os tipos de Wods cadastrados</span>
	<span i18n="@@tipos-wod:titulo" xingling="periodizacaoLabelName">
		Periodização
	</span>
	<span i18n="@@tipos-wod:descricao" xingling="periodizacaoLabelDesc">
		Gerencie as periodizações
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #tooltipLabels>
	<span xingling="tooltipEditar">Editar</span>
	<span xingling="tooltipRemover">Remover</span>
</pacto-traducoes-xingling>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
