import { Component, Inject, OnInit, ViewChild } from "@angular/core";

import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import { ModalService } from "@base-core/modal/modal.service";

import { TipoWodBase, TreinoApiTipoWodService } from "treino-api";
import { Router } from "@angular/router";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { RestService } from "@base-core/rest/rest.service";
import { DOCUMENT } from "@angular/common";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-tipo-wod-lista",
	templateUrl: "./tipo-wod-lista.component.html",
	styleUrls: ["./tipo-wod-lista.component.scss"],
})
export class TipoWodListaComponent implements OnInit {
	@ViewChild("columnNome", { static: true }) columnNome;
	@ViewChild("novoTipoWod", { static: true }) novoTipoWod;
	@ViewChild("tableData", { static: false }) tableData;

	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@ViewChild("tipoWodLabels", { static: true })
	tipoWodLabels: TraducoesXinglingComponent;
	@ViewChild("tooltipLabels", { static: true })
	tooltipLabels: TraducoesXinglingComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;

	selectCrossfitOption: boolean;

	data: ApiResponseList<TipoWodBase> = {
		content: [],
	};
	loading = false;
	nomeTipoWod = "";
	table: PactoDataGridConfig;
	tipoWodFormName: string;
	tipoWodFormDesc: string;

	constructor(
		private snotifyService: SnotifyService,
		private router: Router,
		private rest: RestService,
		private tipoWodService: TreinoApiTipoWodService,
		private modalService: ModalService,
		private configurationService: TreinoConfigCacheService,
		public sessionServive: SessionService,
		@Inject(DOCUMENT) private document: any
	) {}

	ngOnInit() {
		if (this.document.getElementById("md-app-widget") !== null) {
			this.document.getElementById("md-app-widget").style.visibility =
				"visible";
		}
		this.configTable();
		setTimeout(() => {
			this.updateHeader();
		});
	}

	btnClickHandler() {
		this.router.navigate(["cross", "cadastros", "tipos-wod", "adicionar"]);
	}

	editHandler(item) {
		this.router.navigate(["cross", "cadastros", "tipos-wod", item.id]);
	}

	removeHandler(item) {
		this.nomeTipoWod = item.nome;
		setTimeout(() => {
			const modalTitle = this.notificacoesLabels.getLabel("removeModalTitle");
			const modalBody = this.notificacoesLabels.getLabel("removeModalBody");
			const removeSuccess = this.notificacoesLabels.getLabel("removeSuccess");
			const handler = this.modalService.confirm(modalTitle, modalBody);
			handler.result
				.then(() => {
					this.tipoWodService.removerTipoWod(item.id).subscribe((response) => {
						if (response) {
							this.snotifyService.success(removeSuccess);
							this.tableData.reloadData();
						} else {
							this.snotifyService.error(
								"Não foi possível excluir o tipo de wod informado."
							);
						}
					});
				})
				.catch(() => {});
		});
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;

		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("tipos-wod"),
			quickSearch: true,
			logUrl: this.rest.buildFullUrl("log/tipos-wod"),
			pagination: false,
			buttons: {
				conteudo: this.novoTipoWod,
				nome: "add",
				id: "btn-novo-tipo-wod",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.editHandler($event.row);
		} else if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	private updateHeader() {
		this.selectCrossfitOption =
			this.configurationService.configuracoesGerais.troca_nomenclatura_crossfit.toString() ===
			"true";
		if (this.selectCrossfitOption) {
			this.tipoWodFormName = this.tipoWodLabels.getLabel(
				"periodizacaoLabelName"
			);
			this.tipoWodFormDesc = this.tipoWodLabels.getLabel(
				"periodizacaoLabelDesc"
			);
		} else {
			this.tipoWodFormName = this.tipoWodLabels.getLabel("tipoWodLabelName");
			this.tipoWodFormDesc = this.tipoWodLabels.getLabel("tipoWodLabelDesc");
		}
	}
}
