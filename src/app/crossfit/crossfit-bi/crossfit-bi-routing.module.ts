import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { CrossfitBiPainelComponent } from "./components/crossfit-bi-painel/crossfit-bi-painel.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";

const recursos = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GESTAO, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: CrossfitBiPainelComponent,
	},
];

@NgModule({
	imports: [CommonModule, RouterModule.forChild(routes)],
	declarations: [],
})
export class CrossfitBiRoutingModule {}
