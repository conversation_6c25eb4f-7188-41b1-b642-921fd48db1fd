@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";

.small-info-card-wrapper {
	background-color: #ffffff;
	position: relative;
	box-shadow: 0px 0px 2px 2px #eaeaea;
	display: flex;
	width: 100%;
	height: 100%;
	padding: 15px 25px;

	.marker {
		width: 5px;
		height: 30px;
		position: absolute;
		left: 0px;
		top: 29px;

		&.module-crossfit {
			background-color: primaryColor(pacto, base);
		}

		&.module-treino {
			background-color: primaryColor(pacto, base);
		}
	}

	.left-side {
		i {
			font-size: 38px;
			position: relative;
			padding-right: 10px;
			top: 9px;
			color: #333;
		}

		img {
			width: 45px;
			margin-top: 9px;
			padding-right: 15px;
		}
	}

	.right-side {
		width: 100%;
		margin-left: 5px;

		.title {
			color: #888;
			font-weight: 600;
			font-size: 15px;
			overflow: hidden;
			text-overflow: ellipsis;
			margin-right: 30px;
		}

		.valor {
			color: #000;
			font-size: 23px;
			font-weight: 600;
		}
	}
}
