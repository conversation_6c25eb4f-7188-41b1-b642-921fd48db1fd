import { Component, OnInit, Input } from "@angular/core";

@Component({
	selector: "pacto-small-info-card",
	templateUrl: "./small-info-card.component.html",
	styleUrls: ["./small-info-card.component.scss"],
})
export class SmallInfoCardComponent implements OnInit {
	@Input() titulo;
	@Input() simpleLineIcon;
	@Input() materialIcon;
	@Input() imageUrl;
	@Input() valor;
	@Input() id;

	constructor() {}

	ngOnInit() {}

	get iconClass() {
		if (this.simpleLineIcon) {
			return this.simpleLineIcon;
		} else {
			return false;
		}
	}
}
