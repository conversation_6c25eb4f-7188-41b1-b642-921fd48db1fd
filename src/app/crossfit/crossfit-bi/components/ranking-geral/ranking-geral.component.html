<pacto-title-card
	i18n-title="@@dashboard-crossfit:ranking-geral"
	title="Ranking geral">
	<table class="ranking-table table">
		<thead>
			<tr>
				<td i18n="@@dashboard-crossfit:aluno">Aluno</td>
				<td class="aproveitamento-column">
					<span (click)="orderingHandler()" class="ordering-control">
						<span i18n="@@dashboard-crossfit:aproveitamento">
							Aproveitamento
						</span>
						<i *ngIf="!orderingAsc" class="fa fa-caret-down"></i>
						<i *ngIf="orderingAsc" class="fa fa-caret-up"></i>
					</span>
				</td>
			</tr>
		</thead>
		<tbody>
			<tr *ngFor="let aluno of ranking">
				<td>{{ aluno.nome }}</td>
				<td class="aproveitamento-value">{{ aluno.aproveitamento }}%</td>
			</tr>
		</tbody>
	</table>
</pacto-title-card>
