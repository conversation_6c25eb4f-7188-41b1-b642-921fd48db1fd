import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { CrossfitBIRankingAluno } from "treino-api";

@Component({
	selector: "pacto-ranking-geral",
	templateUrl: "./ranking-geral.component.html",
	styleUrls: ["./ranking-geral.component.scss"],
})
export class RankingGeralComponent implements OnInit {
	@Input() ranking: Array<CrossfitBIRankingAluno> = [];
	@Output() orderingAsc$: EventEmitter<boolean> = new EventEmitter();

	orderingAsc = false;

	constructor() {}

	ngOnInit() {}

	orderingHandler() {
		this.orderingAsc = !this.orderingAsc;
		this.orderingAsc$.emit(this.orderingAsc);
	}
}
