import { Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { DataFiltro, PactoDataGridConfig } from "ui-kit";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-detalhes-modal",
	templateUrl: "./detalhes-modal.component.html",
	styleUrls: ["./detalhes-modal.component.scss"],
})
export class DetalhesModalComponent implements OnInit {
	table: PactoDataGridConfig;
	title: any;
	reading = false;
	baseFilters: DataFiltro;
	telaId: any;

	constructor(
		private openModal: NgbActiveModal,
		public sessionService: SessionService,
		private route: Router
	) {}

	ngOnInit() {}

	ready(table: PactoDataGridConfig, title: any, filter, tlID: any) {
		this.title = title;
		this.table = table;
		this.reading = true;
		this.baseFilters = filter;
		this.telaId = tlID;
	}

	dismiss() {
		this.openModal.dismiss();
	}

	abrirPerfilAluno(item) {
		window
			.open(
				"cadastros/alunos/perfil/" + item.matricula + "%3Forigem%3Dbi",
				"_blank"
			)
			.focus();
	}
}
