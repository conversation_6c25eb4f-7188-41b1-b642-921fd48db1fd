<div>
	<div class="modal-body">
		<div class="row">
			<div class="col-md-6">
				<pacto-cat-select-filter
					[control]="formGroup.get('aluno')"
					[endpointUrl]="alunoUrl"
					[id]="'aluno-adicionar-lesao'"
					[imageKey]="'imageUri'"
					[labelKey]="'nome'"
					[label]="'Aluno*'"
					[paramBuilder]="alunoParamBuilder"
					[resposeParser]="resposeParser"
					class="aluno-select"
					id="alunos-adicionar-lesao"></pacto-cat-select-filter>
			</div>

			<div class="col-md-6">
				<pacto-cat-select
					[control]="formGroup.get('gravidade')"
					[idKey]="'id'"
					[id]="'select-gravidade-lesao'"
					[items]="itensGravidade"
					[labelKey]="'nome'"
					[label]="'Gravidade*'"></pacto-cat-select>
			</div>
		</div>

		<div class="row">
			<div class="col-md-6" style="margin: 19px 0px 0px 0px">
				<div class="input-nome">Região de lesão*</div>
				<input
					[formControl]="formGroup.get('regiaoLesao')"
					class="input"
					id="regiao-lesao"
					type="text" />
			</div>

			<div class="col-md-6">
				<pacto-cat-form-datepicker
					[control]="formGroup.get('data')"
					[id]="'data-lesao'"
					[label]="'Data*'"
					style="margin-bottom: 0px !important"></pacto-cat-form-datepicker>
			</div>
		</div>

		<div class="row">
			<div class="col-md-6">
				<pacto-cat-select
					[control]="formGroup.get('status')"
					[idKey]="'id'"
					[id]="'select-status-lesao'"
					[items]="itensStatus"
					[labelKey]="'nome'"
					[label]="'Status*'"></pacto-cat-select>
			</div>
		</div>

		<div class="row">
			<div class="col-md-12" style="margin: 19px 0">
				<div class="input-nome">Observação</div>
				<input
					[formControl]="formGroup.get('observacao')"
					class="input"
					id="observacao-lesao"
					type="text" />
			</div>
		</div>
	</div>

	<div class="modal-footer">
		<button
			(click)="dismiss()"
			class="btn btn-secondary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Cancelar
		</button>
		<button
			(click)="close()"
			class="btn btn-primary btn-registrar"
			i18n="@@buttons:salvar"
			type="button">
			Registrar
		</button>
	</div>
</div>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crossfit:tipos-benchmark:mensagem:campos-obrigatorio">
	Campos obrigatorios não preenchidos!
</span>
