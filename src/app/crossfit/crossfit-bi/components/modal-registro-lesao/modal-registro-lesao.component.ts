import { Component, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoApiLesaoService } from "treino-api";
import moment from "moment/moment";

@Component({
	selector: "pacto-modal-registro-lesao",
	templateUrl: "./modal-registro-lesao.component.html",
	styleUrls: ["./modal-registro-lesao.component.scss"],
})
export class ModalRegistroLesaoComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;

	formGroup: FormGroup = new FormGroup({
		aluno: new FormControl("", [Validators.required]),
		gravidade: new FormControl("", [Validators.required]),
		regiaoLesao: new FormControl("", [Validators.required]),
		data: new FormControl("", [Validators.required]),
		status: new FormControl("", [Validators.required]),
		observacao: new FormControl(""),
	});

	itensGravidade = [
		{ id: "LEVE", nome: "Leve" },
		{ id: "MODERADA", nome: "Moderada" },
		{ id: "GRAVE", nome: "Grave" },
	];

	itensStatus = [
		{ id: "LEVE", nome: "Em recuperação" },
		{ id: "MODERADA", nome: "Em recuperação 2" },
		{ id: "GRAVE", nome: "Em recuperação 3" },
	];

	alunoParamBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	resposeParser = (result) => result.content;

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal,
		private rest: RestService,
		private lesaoService: TreinoApiLesaoService
	) {}

	get alunoUrl() {
		return this.rest.buildFullUrl("alunos");
	}

	ngOnInit() {}

	dismiss() {
		this.openModal.dismiss();
	}

	close() {
		const dto = this.buildDto(this.formGroup.getRawValue());
		if (this.validForm()) {
			this.lesaoService.salvarLesao(dto).subscribe({
				error: (error) => {
					this.snotifyService.error(error.error.meta.message);
				},
				next: (result) => {
					this.snotifyService.success("Lesão registrada com sucesso.");
					this.openModal.close(this.formGroup.getRawValue());
				},
			});
		} else {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
		}
	}

	private buildDto(rawValue) {
		return {
			matricula: rawValue.aluno.matriculaZW,
			observacao: rawValue.observacao,
			regiaoLesao: rawValue.regiaoLesao,
			gravidade: rawValue.gravidade,
			status: rawValue.status,
			dataLesao: moment(rawValue.data).format("YYYY-MM-DD"),
		};
	}

	private validForm() {
		if (!this.formGroup.valid) {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			return false;
		} else {
			return true;
		}
	}
}
