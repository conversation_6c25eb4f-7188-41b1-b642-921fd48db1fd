<pacto-cat-layout-v2>
	<div class="top-row">
		<pacto-breadcrumbs
			[breadcrumbConfig]="{
				menu: 'Business Intelligence'
			}"
			class="first"></pacto-breadcrumbs>

		<div class="second">
			<div class="selectWrapper">
				<select
					(change)="changeFiltro()"
					[formControl]="formGroup.get('dateSelected')"
					class="form-control form-control-sm">
					<option i18n="@@dashboard-crossfit:mes-atual" value="0">
						Mês atual
					</option>
					<option *ngFor="let mes of mesData" value="{{ mes.id }}">
						{{ mes.mes }} / {{ mes.ano }}
					</option>
				</select>
			</div>
			<div
				*ngIf="!professores || professores.length == 0"
				class="selectWrapper">
				<select class="selectBox">
					<option i18n="@@dashboard-crossfit:carregando">Carregando...</option>
				</select>
			</div>
			<div *ngIf="professores && professores.length > 0" class="selectWrapper">
				<select
					(change)="changeFiltro()"
					[attr.disabled]="!permissaoVisualizarBiOutrosProfessor ? '' : null"
					[formControl]="formGroup.get('professorSelected')"
					class="form-control form-control-sm">
					<option
						i18n="@@dashboard-crossfit:todos-professores:option-select"
						value="0">
						Todos os professores
					</option>
					<option
						*ngFor="let professor of professores"
						value="{{ professor.id }}">
						{{ professor.nome }}
					</option>
				</select>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-7">
			<div class="row">
				<div class="col-md-6 select-indicador">
					<pacto-small-info-card
						(click)="getDetalhesModal('alunos')"
						*ngIf="crossfitDados"
						[imageUrl]="'assets/images/icon-crossfit-workout-grey.svg'"
						[valor]="crossfitDados.numeroDealunos"
						i18n-titulo="@@dashboard-crossfit:alunos-ativos:titulo"
						titulo="Alunos Ativos"></pacto-small-info-card>
				</div>
				<div class="col-md-6 select-indicador">
					<pacto-small-info-card
						(click)="getDetalhesModal('resultados')"
						*ngIf="crossfitDados"
						[simpleLineIcon]="'icon-user'"
						[valor]="crossfitDados.resultadosLancados"
						i18n-titulo="@@dashboard-crossfit:resultados-lancados:titulo"
						titulo="Resultados Lançados"></pacto-small-info-card>
				</div>
			</div>
		</div>
		<div class="col-md-5 select-indicador">
			<pacto-small-info-card
				(click)="getDetalhesModal('agendamentos')"
				*ngIf="crossfitDados"
				[simpleLineIcon]="'icon-notebook'"
				[valor]="crossfitDados.numeroAgendamentos"
				i18n-titulo="@@dashboard-crossfit:marcacao-aula:titulo"
				titulo="Marcação em Aula"></pacto-small-info-card>
		</div>
	</div>

	<div class="row graficos-coluna">
		<div class="col-md-7">
			<pacto-title-card
				i18n-title="@@dashboard-crossfit:alunos-professor:titulo"
				title="Alunos nas aulas por Professor">
				<div
					[style.height.px]="400"
					[style.width.%]="100"
					id="frequencia"></div>
			</pacto-title-card>
		</div>

		<div class="col-md-5">
			<pacto-title-card
				i18n-title="@@dashboard-crossfit:ocupacao-dia-semana:titulo"
				title="Ocupação das aulas por dia da semana">
				<div [style.height.px]="400" [style.width.%]="100" id="ocupacao"></div>
			</pacto-title-card>
		</div>
	</div>

	<div *ngIf="showIndiceLesoes" class="indicadores-lesao">
		<div class="row cabecalho-lesao">
			<div class="titulo">Índice de lesões</div>
			<div>
				<button (click)="registrarLesao()" class="btn btn-primary">
					Registrar lesão
				</button>
				<pacto-cat-select
					(change)="atualizarIndicadoresLesao()"
					[control]="formGroup.get('anoIndicadorIndiceLesao')"
					[idKey]="'id'"
					[id]="'select-status-lesao'"
					[items]="listaAnos"
					[labelKey]="'label'"
					style="margin-top: 16px"></pacto-cat-select>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-3 select-indicador">
						<pacto-small-info-card-v2
							(click)="getDetalhesModal('lesoesLeves')"
							[tooltipTexto]="
								'Lesões leves: Isso inclui coisas como contusões, pequenas distensões\n' +
								'musculares ou articulares, bolhas nas mãos devido ao uso frequente de\n' +
								'barras e outros equipamentos.'
							"
							[valor]="indiceLesoes.totalLesoesLeves.toString()"
							titulo="Total de lesões leves"></pacto-small-info-card-v2>
					</div>

					<div class="col-md-3 select-indicador">
						<pacto-small-info-card-v2
							(click)="getDetalhesModal('lesoesModeradas')"
							[tooltipTexto]="
								'Lesões moderadas: Estas podem envolver distensões musculares mais\n' +
								'significativas, entorses, pequenas lacerações, ou até mesmo lesões articulares\n' +
								'mais graves como tendinites.'
							"
							[valor]="indiceLesoes.totalLesoesModeradas.toString()"
							titulo="Total de lesões moderadas"></pacto-small-info-card-v2>
					</div>

					<div class="col-md-3 select-indicador">
						<pacto-small-info-card-v2
							(click)="getDetalhesModal('lesoesGraves')"
							[tooltipTexto]="
								'Lesões graves: Lesões mais graves podem incluir rupturas musculares, lesões\n' +
								'ligamentares significativas (como um ligamento cruzado rompido no joelho),\n' +
								'fraturas de estresse de ossos devido à sobrecarga repetitiva, ou até mesmo lesões\n' +
								'graves na coluna vertebral.'
							"
							[valor]="indiceLesoes.totalLesoesGraves.toString()"
							titulo="Total de lesões graves"></pacto-small-info-card-v2>
					</div>

					<div class="col-md-3 select-indicador">
						<pacto-small-info-card-v2
							(click)="getDetalhesModal('todasLesoes')"
							[valor]="indiceLesoes.totalLesoesAoAno.toString()"
							titulo="Total de lesões ao ano"></pacto-small-info-card-v2>
					</div>
				</div>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>

<!--translator detalhes modal-->
<!--title table-->
<ng-template #titleModal>
	<ng-container
		*ngTemplateOutlet="
			traducaoTitles;
			context: { traducaoTitle: titleId }
		"></ng-container>
</ng-template>
<ng-template #traducaoTitles let-traducaoTitle="traducaoTitle">
	<ng-container [ngSwitch]="traducaoTitle">
		<span
			*ngSwitchCase="'alunos'"
			i18n="@@dashboard-crossfit:alunos-ativos:titulo">
			Alunos Ativos
		</span>
		<span
			*ngSwitchCase="'resultados'"
			i18n="@@dashboard-crossfit:resultados-lancados:titulo">
			Resultados Lançados
		</span>
		<span
			*ngSwitchCase="'agendamentos'"
			i18n="@@dashboard-crossfit:marcacao-aula:titulo">
			Marcação em Aula
		</span>
		<span
			*ngSwitchCase="'frequencia'"
			i18n="@@dashboard-crossfit:alunos-professor:titulo">
			Alunos nas aulas por Professor
		</span>
		<span
			*ngSwitchCase="'ocupacao'"
			i18n="@@dashboard-crossfit:ocupacao-dia-semana:titulo">
			Ocupação das aulas por dia da semana
		</span>
	</ng-container>
	<span *ngIf="descriptionTitle">- {{ descriptionTitle }}</span>
</ng-template>
<!--end title table-->
<!--table columns-->
<ng-template #columnMatricula>
	<span i18n="@@dashboard-crossfit:detalhe-modal-column:matricula">
		Matrícula
	</span>
</ng-template>
<ng-template #columnNome>
	<span i18n="@@dashboard-crossfit:detalhe-modal-column:nome">Nome</span>
</ng-template>
<ng-template #columnSituacao>
	<span i18n="@@dashboard-crossfit:detalhe-modal-column:situacao">
		Situação
	</span>
</ng-template>
<ng-template #columnDataLancamento>
	<span i18n="@@dashboard-crossfit:detalhe-modal-column:data-lancamento">
		Data de Lançamento
	</span>
</ng-template>
<ng-template #columnDiaMarcado>
	<span i18n="@@dashboard-crossfit:detalhe-modal-column:dia">Dia</span>
</ng-template>
<ng-template #columnModalidade>
	<span i18n="@@dashboard-crossfit:detalhe-modal-column:modalidade">
		Modalidade
	</span>
</ng-template>
<!--end table columns-->
<!--extraindo celula-->
<ng-template #statusColumn let-item="item">
	<span *ngIf="item.situacaoAluno === 'AT'" i18n="@@crud-alunos:ativo:result">
		Ativo
	</span>
	<span *ngIf="item.situacaoAluno === 'IN'" i18n="@@crud-alunos:inativo:result">
		Inativo
	</span>
	<span
		*ngIf="item.situacaoAluno === 'VI'"
		i18n="@@crud-alunos:visitante:result">
		Visitante
	</span>
	<span *ngIf="item.situacaoAluno === 'TR'">Trancado</span>
	<span
		*ngIf="item.situacaoAluno === 'ATIVO'"
		i18n="@@crud-alunos:ativo:result">
		Ativo
	</span>
	<span
		*ngIf="item.situacaoAluno === 'INATIVO'"
		i18n="@@crud-alunos:inativo:result">
		Inativo
	</span>
	<span
		*ngIf="item.situacaoAluno === 'VISITANTE'"
		i18n="@@crud-alunos:visitante:result">
		Visitante
	</span>
	<span *ngIf="item.situacaoAluno === 'TRANCADO'">Trancado</span>
</ng-template>
<ng-template #diaFormater let-item="item">
	<span>{{ item.dia | date : "dd/MM/yyyy" : "UTC" }}</span>
</ng-template>
<!--end extraindo celula-->
<!--end translator detalhes modal-->
<span #manhaLegend [hidden]="true" i18n="@@dashboard-crossfit:manha">
	Manhã
</span>
<span #tardeLegend [hidden]="true" i18n="@@dashboard-crossfit:tarde">
	Tarde
</span>
<span #noiteLegend [hidden]="true" i18n="@@dashboard-crossfit:noite">
	Noite
</span>

<span #segunda [hidden]="true" i18n="@@dashboard-crossfit:segunda">
	Segunda
</span>
<span #terca [hidden]="true" i18n="@@dashboard-crossfit:terca">Terça</span>
<span #quarta [hidden]="true" i18n="@@dashboard-crossfit:quarta">Quarta</span>
<span #quinta [hidden]="true" i18n="@@dashboard-crossfit:quinta">Quinta</span>
<span #sexta [hidden]="true" i18n="@@dashboard-crossfit:sexta">Sexta</span>
<span #sabado [hidden]="true" i18n="@@dashboard-crossfit:sabado">Sábado</span>

<span #frequencia [hidden]="true" i18n="@@dashboard-crossfit:frequencia">
	Frequência
</span>
