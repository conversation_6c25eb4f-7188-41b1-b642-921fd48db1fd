import { <PERSON><PERSON><PERSON>, AmChartsService } from "@amcharts/amcharts3-angular";
import {
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	CrossfitBIDados,
	CrossfitBIRankingAluno,
	FiltroData,
	PerfilAcessoFuncionalidadeNome,
	TreinoApiColaboradorService,
	TreinoApiCrossfitBiService,
	UsuarioBase,
	TreinoApiLesaoService,
	IndiceLesoes,
} from "treino-api";
import { PactoDataGridConfig } from "ui-kit";
import { DetalhesModalComponent } from "../detalhes-modal/detalhes-modal.component";

import { RestService } from "@base-core/rest/rest.service";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { RecursoSistema } from "../../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { ModalRegistroLesaoComponent } from "../modal-registro-lesao/modal-registro-lesao.component";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";

declare var moment;

@Component({
	selector: "pacto-crossfit-bi-painel",
	templateUrl: "./crossfit-bi-painel.component.html",
	styleUrls: ["./crossfit-bi-painel.component.scss"],
})
export class CrossfitBiPainelComponent implements OnInit, OnDestroy {
	@ViewChild("manhaLegend", { static: true }) manhaLegend;
	@ViewChild("tardeLegend", { static: true }) tardeLegend;
	@ViewChild("noiteLegend", { static: true }) noiteLegend;

	@ViewChild("segunda", { static: true }) segunda;
	@ViewChild("terca", { static: true }) terca;
	@ViewChild("quarta", { static: true }) quarta;
	@ViewChild("quinta", { static: true }) quinta;
	@ViewChild("sexta", { static: true }) sexta;
	@ViewChild("sabado", { static: true }) sabado;

	@ViewChild("frequencia", { static: true }) frequencia;

	@ViewChild("columnMatricula", { static: true }) columnMatricula;
	@ViewChild("columnNome", { static: true }) columnNome;
	@ViewChild("columnSituacao", { static: true }) columnSituacao;
	@ViewChild("columnDataLancamento", { static: true }) columnDataLancamento;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("columnDiaMarcado", { static: true }) columnDiaMarcado;
	@ViewChild("columnModalidade", { static: true }) columnModalidade;
	@ViewChild("titleModal", { static: true }) titleModal;
	@ViewChild("diaFormater", { static: true }) diaFormater;

	crossfitDados: CrossfitBIDados;
	private ocupacaoChart: AmChart;
	private filtroData: FiltroData = { idProfessor: 0, mes: 0, ano: 0 };
	_ocupacaoData: Array<any> = [];
	_frequenciaData: Array<any> = [];
	rankingGeral: Array<CrossfitBIRankingAluno> = [];
	professores: UsuarioBase[];
	mesData = [];
	table: PactoDataGridConfig;
	formGroup: FormGroup = new FormGroup({
		professorSelected: new FormControl(0),
		dateSelected: new FormControl(0),
		anoIndicadorIndiceLesao: new FormControl(0),
	});
	titleId: string;
	descriptionTitle: string;
	filtroFrequenciaRelatorio: any;
	permissaoVisualizarBiOutrosProfessor;
	showIndiceLesoes = false;
	indiceLesoes: IndiceLesoes;
	listaAnos = [];
	anoAtual: number;

	constructor(
		private AmCharts: AmChartsService,
		private crossfitBiService: TreinoApiCrossfitBiService,
		private lesaoService: TreinoApiLesaoService,
		private colaboradorService: TreinoApiColaboradorService,
		private modalService: NgbModal,
		private modalServiceV2: ModalService,
		private rest: RestService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.carregarPermissoes();
		this.getDataFilter().subscribe(() => {
			this.initData();
			if (!this.permissaoVisualizarBiOutrosProfessor) {
				this.professorASerConsultado().subscribe(() => {
					this.initData();
				});
			}
		});
	}

	private initDados() {
		this.crossfitDados = {
			numeroDealunos: 0,
			faltasNosUltimos7Dias: 0,
			numeroAgendamentos: 0,
			resultadosLancados: 0,
			ocupacao: {
				segunda: { manha: 0, tarde: 0, noite: 0 },
				terca: { manha: 0, tarde: 0, noite: 0 },
				quarta: { manha: 0, tarde: 0, noite: 0 },
				quinta: { manha: 0, tarde: 0, noite: 0 },
				sexta: { manha: 0, tarde: 0, noite: 0 },
				sabado: { manha: 0, tarde: 0, noite: 0 },
			},
			frequenciaPorProfessor: [],
		};
	}

	private initSelectAnoIndiceLesao() {
		const dataAtual = new Date();
		this.anoAtual = dataAtual.getFullYear();
		this.formGroup.get("anoIndicadorIndiceLesao").setValue(this.anoAtual);
		this.listaAnos = [];
		for (let index = this.anoAtual; index >= 2010; index--) {
			this.listaAnos.push({ id: index, label: index });
		}
		this.consultarIndicadorIndiceLesoes(this.anoAtual);
	}

	private initData() {
		this.initDados();
		this.initSelectAnoIndiceLesao();
		if (this.filtroData.ano === 0) {
			this.filtroData.ano = new Date().getFullYear();
		}
		if (this.filtroData.mes === 0) {
			this.filtroData.mes = new Date().getMonth() + 1;
		}
		if (this.formGroup.get("professorSelected").value !== "SEM_PERMISSAO") {
			this.crossfitBiService
				.obterDadosCrossfitBI(this.filtroData)
				.subscribe((dados) => {
					this.crossfitDados = dados;
					this.prepareData();
					this.buildGraphs();
					this.cd.detectChanges();
					this.sessionService.notificarRecursoEmpresa(
						RecursoSistema.ENTROU_DASHBOARD_CROSSFIT_NCR
					);
				});
		}
	}

	changeFiltro() {
		const obj = this.getFiltroObject();
		const mesSelected = this.getDateOnId(parseInt(obj.dateSelected, 32));
		this.filtroData.idProfessor = obj.professorSelected;
		this.filtroData.mes = mesSelected.mes;
		this.filtroData.ano = mesSelected.ano;
		this._ocupacaoData = [];
		this._frequenciaData = [];
		this.initData();
	}

	private professorASerConsultado(): Observable<any> {
		const ret = this.colaboradorService
			.consultarPorUsuario(this.sessionService.loggedUser.id)
			.pipe(
				map((response) => {
					this.professores.unshift({ id: "SEMP_PERMISSAO", nome: "" });
					this.formGroup.get("professorSelected").setValue("SEM_PERMISSAO");
					this.professores.forEach((professor) => {
						if (professor.codigoPessoa === response.codigoPessoa) {
							this.filtroData.idProfessor = response.id;
							this.formGroup.get("professorSelected").setValue(professor.id);
						}
					});
					return true;
				})
			);

		return ret;
	}

	private getDateOnId(id: number) {
		if (id === 0) {
			return { mes: 0, ano: 0 };
		} else {
			return this.mesData.find((mes) => {
				return mes.id === id;
			});
		}
	}

	setMesesArray(now) {
		for (let i = 1; i < 7; i++) {
			const newDate = new Date(moment().subtract(i, "month"));
			this.mesData.push({
				ano: newDate.getUTCFullYear(),
				mes: newDate.getMonth() + 1,
				id: i,
			});
		}
	}

	getMesesData() {
		this.setMesesArray(new Date());
	}

	getDataFilter(): Observable<any> {
		this.getMesesData();
		const ret = this.colaboradorService.obterTodosColaboradores().pipe(
			map((data) => {
				this.professores = data.content;
				return true;
			})
		);
		return ret;
	}

	private prepareData() {
		this.prepareOcupacaoData();
		this.prepareFrequenciaData();
	}

	private buildGraphs() {
		this.buildOcupacaoGraph();
		this.buildFrequenciaGraph();
	}

	getFiltroObject() {
		const raw = this.formGroup.getRawValue();
		return raw;
	}

	getDetalhesModal(indicador: string, descricao?: string) {
		let filter = null;
		let telaID = null;
		this.titleId = indicador;
		this.descriptionTitle = null;
		if (indicador === "alunos") {
			telaID = "alunosbicross";
			this.buildRelatorioAlunosAtivos();
			filter = this.buildfiltersAlunosAtivos();
		} else if (indicador === "resultados") {
			telaID = "resultadosbicross";
			this.buildRelatorioResultados();
			filter = this.buildFiltersRelatorios(this.filtroData);
		} else if (indicador === "agendamentos") {
			telaID = "agendamentosbicross";
			this.buildRelatorioAgendamentosAula();
			filter = this.buildFiltersRelatorios(this.filtroData);
		} else if (indicador === "frequencia") {
			telaID = "frequenciabicross";
			this.buildRelatorioAlunosFrequentamAulaProfessor();
			filter = this.filtroFrequenciaRelatorio;
			this.descriptionTitle = descricao;
		} else if (indicador === "ocupacao") {
			telaID = "ocupacaobicross";
			this.buildRelatorioAlunosFrequentamAulaProfessor();
			filter = this.filtroFrequenciaRelatorio;
			this.descriptionTitle = descricao;
		} else if (indicador === "lesoesLeves") {
			telaID = "lesoeslevesbicross";
			this.buildRelatorioLesoes();
			filter = this.buildFiltersIndicadorLesaoLeve("LEVE");
		} else if (indicador === "lesoesModeradas") {
			telaID = "lesoesmoderadasbicross";
			this.buildRelatorioLesoes();
			filter = this.buildFiltersIndicadorLesaoLeve("MODERADA");
		} else if (indicador === "lesoesGraves") {
			telaID = "lesoesmoderadasbicross";
			this.buildRelatorioLesoes();
			filter = this.buildFiltersIndicadorLesaoLeve("GRAVE");
		} else if (indicador === "todasLesoes") {
			telaID = "lesoesmoderadasbicross";
			this.buildRelatorioLesoes();
			filter = this.buildFiltersIndicadorLesaoLeve("TODAS");
		}
		const modal = this.modalService.open(DetalhesModalComponent, {
			size: "lg",
			windowClass: "modal-xmxl",
			centered: true,
		});
		modal.componentInstance.telaId = telaID;
		modal.componentInstance.sessionService = this.sessionService;
		this.table.logUrl = this.rest.buildFullUrl(
			"log/listar-log-exportacao/" + telaID
		);
		modal.componentInstance.ready(this.table, this.titleModal, filter, telaID);
	}

	buildRelatorioAlunosAtivos() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("alunos"),
			quickSearch: true,
			rowClick: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "matriculaZW",
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					ordenavel: true,
					visible: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "situacao",
					titulo: this.columnSituacao,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.statusColumn,
				},
			],
		});
	}

	private buildfiltersAlunosAtivos() {
		const filter = {
			filters: {
				situacoesEnuns: ["AT"],
				professorId: this.filtroData.idProfessor,
			},
		};
		return filter;
	}

	private buildRelatorioResultados() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("crossfit-bi/alunos-resultados"),
			quickSearch: true,
			rowClick: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "matricula",
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					ordenavel: true,
					visible: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "situacao",
					titulo: this.columnSituacao,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.statusColumn,
				},
				{
					nome: "dataLancamento",
					titulo: this.columnDataLancamento,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.diaFormater,
				},
			],
		});
	}

	private buildFiltersRelatorios(filtroData: FiltroData) {
		const filter = {
			filters: {
				mes: filtroData.mes,
				ano: filtroData.ano,
				idProfessor: filtroData.idProfessor,
			},
		};
		return filter;
	}

	private buildRelatorioAgendamentosAula() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("crossfit-bi/alunos-agendamentos"),
			quickSearch: true,
			rowClick: false,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "matricula",
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					ordenavel: true,
					visible: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "modalidade",
					titulo: this.columnModalidade,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "modalidade",
				},
				{
					nome: "dia",
					titulo: this.columnDiaMarcado,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.diaFormater,
				},
			],
		});
	}

	private buildRelatorioAlunosFrequentamAulaProfessor() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				"crossfit-bi/alunos-frequentam-aula/professor"
			),
			quickSearch: true,
			rowClick: true,
			columns: [
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "matricula",
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					ordenavel: true,
					visible: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "situacao",
					titulo: this.columnSituacao,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.statusColumn,
				},
				{
					nome: "dia",
					titulo: this.columnDiaMarcado,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.diaFormater,
				},
			],
		});
	}

	buildRelatorioLesoes() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("lesao/bi-cross/listar-lesoes"),
			quickSearch: true,
			rowClick: false,
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "matricula",
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					ordenavel: false,
					visible: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "dataLesao",
					titulo: "Data",
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "dataLesao",
				},
				{
					nome: "professor",
					titulo: "Professor",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					defaultVisible: true,
					campo: "professor",
				},
				{
					nome: "regiaoLesao",
					titulo: "Região da lesão",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					defaultVisible: true,
					campo: "regiaoLesao",
				},
				{
					nome: "gravidade",
					titulo: "Gravidade",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					defaultVisible: true,
					campo: "gravidade",
				},
				{
					nome: "observacao",
					titulo: "Observação",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					defaultVisible: true,
					campo: "observacao",
				},
			],
		});
	}

	private buildFiltersIndicadorLesaoLeve(gravidade) {
		const filter = {
			filters: {
				gravidadeEnun: gravidade,
				ano: this.formGroup.get("anoIndicadorIndiceLesao").value,
				professorId: this.filtroData.idProfessor,
			},
		};
		return filter;
	}

	private prepareOcupacaoData() {
		const diasSemana: any = {
			segunda: this.segunda.nativeElement.innerHTML,
			terca: this.terca.nativeElement.innerHTML,
			quarta: this.quarta.nativeElement.innerHTML,
			quinta: this.quinta.nativeElement.innerHTML,
			sexta: this.sexta.nativeElement.innerHTML,
			sabado: this.sabado.nativeElement.innerHTML,
		};
		const keys = ["segunda", "terca", "quarta", "quinta", "sexta", "sabado"];
		keys.forEach((key) => {
			const teste = {
				dia: diasSemana[key],
				manha: this.crossfitDados.ocupacao[key].manha,
				tarde: this.crossfitDados.ocupacao[key].tarde,
				noite: this.crossfitDados.ocupacao[key].noite,
				key,
			};
			this._ocupacaoData.push(teste);
		});
	}

	aulasPorProfessor(event: any) {
		this.filtroFrequenciaRelatorio = {
			filters: {
				mes: this.filtroData.mes,
				ano: this.filtroData.ano,
				idProfessor: event.item.dataContext.professorId,
			},
		};
		this.getDetalhesModal("frequencia", event.item.category);
	}

	buildOcupacaoDiaSemanaETurno(event: any) {
		this.filtroFrequenciaRelatorio = {
			filters: {
				mes: this.filtroData.mes,
				ano: this.filtroData.ano,
				idProfessor: event.item.dataContext.professorId,
				turno: event.graph.valueField,
				diasSemana: event.item.dataContext.key,
			},
		};
		this.getDetalhesModal(
			"ocupacao",
			event.item.category + " / " + event.graph.legendTextReal
		);
	}

	private prepareFrequenciaData() {
		this._frequenciaData = [];
		this.crossfitDados.frequenciaPorProfessor.forEach((frequecia) => {
			this._frequenciaData.push({
				professorId: frequecia.id,
				professor: frequecia.nome,
				valor: frequecia.frequencia,
				imageUri: frequecia.imageUri,
			});
		});
	}

	private buildOcupacaoGraph() {
		const manha = this.manhaLegend.nativeElement.innerHTML;
		const tarde = this.tardeLegend.nativeElement.innerHTML;
		const noite = this.noiteLegend.nativeElement.innerHTML;
		this.ocupacaoChart = this.AmCharts.makeChart("ocupacao", {
			type: "serial",
			theme: "none",
			legend: {
				horizontalGap: 10,
				maxColumns: 3,
				position: "top",
				align: "center",
				markerSize: 15,
			},
			fontFamily: "'Nunito Sans', sans-serif",
			dataProvider: this._ocupacaoData,
			valueAxes: [
				{
					stackType: "regular",
					axisAlpha: 0.3,
					gridAlpha: 0.2,
				},
			],
			graphs: [
				{
					balloonText:
						'<b>[[title]]</b><br><span style="font-size:14px">[[category]]: <b>[[value]]</b></span>',
					fillAlphas: 1,
					labelText: "[[value]]",
					lineAlpha: 0,
					fillColors: "#f0b924",
					title: manha,
					fontSize: "14",
					type: "column",
					color: "#f0b924",
					valueField: "manha",
					showHandOnHover: "true",
				},
				{
					balloonText:
						'<b>[[title]]</b><br><span style="font-size:14px">[[category]]: <b>[[value]]</b></span>',
					fillAlphas: 1,
					labelText: "[[value]]",
					lineAlpha: 0,
					fillColors: "#0380e3",
					title: tarde,
					fontSize: "14",
					type: "column",
					color: "#0380e3",
					valueField: "tarde",
					showHandOnHover: "true",
				},
				{
					balloonText:
						'<b>[[title]]</b><br><span style="font-size:14px">[[category]]: <b>[[value]]</b></span>',
					fillAlphas: 1,
					labelText: "[[value]]",
					lineAlpha: 0,
					fillColors: "#122d47",
					title: noite,
					fontSize: "14",
					type: "column",
					color: "#122d47",
					valueField: "noite",
					showHandOnHover: "true",
				},
			],
			categoryField: "dia",
			categoryAxis: {
				gridPosition: "start",
				axisAlpha: 0.2,
				gridAlpha: 0.2,
				position: "left",
			},
			listeners: [
				{
					event: "clickGraphItem",
					method: (event) => {
						this.buildOcupacaoDiaSemanaETurno(event);
					},
				},
			],
		});
	}

	private buildFrequenciaGraph() {
		const frequencia = this.frequencia.nativeElement.innerHTML;
		this.ocupacaoChart = this.AmCharts.makeChart("frequencia", {
			type: "serial",
			theme: "none",
			fontFamily: "'Nunito Sans', sans-serif",
			dataProvider: this._frequenciaData,
			valueAxes: [
				{
					stackType: "regular",
					axisAlpha: 0.3,
					gridAlpha: 0.2,
				},
			],
			graphs: [
				{
					balloonText:
						'<b>[[title]]</b><br><span style="font-size:14px">[[category]]: <b>[[value]]</b></span>',
					fillAlphas: 1,
					image: "https://randomuser.me/api/portraits/women/71.jpg",
					labelText: "[[value]]",
					lineAlpha: 0,
					fillColors: "#aaa",
					title: frequencia,
					fontSize: "14",
					type: "column",
					color: "#fff",
					valueField: "valor",
					showHandOnHover: "true",
				},
			],
			categoryField: "professor",
			categoryAxis: {
				gridPosition: "start",
				axisAlpha: 0.2,
				gridAlpha: 0.2,
				position: "left",
			},
			listeners: [
				{
					event: "clickGraphItem",
					method: (event) => {
						this.aulasPorProfessor(event);
					},
				},
			],
		});
	}

	ngOnDestroy() {
		if (this.ocupacaoChart) {
			this.AmCharts.destroyChart(this.ocupacaoChart);
		}
	}

	private carregarPermissoes() {
		this.permissaoVisualizarBiOutrosProfessor =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.VER_BI_OUTROS_PROFESSORES
			);
	}

	registrarLesao() {
		const modalRef = this.modalServiceV2.open(
			"Registro de lesão",
			ModalRegistroLesaoComponent,
			PactoModalSize.LARGE
		);
		modalRef.result.then((result) => {
			this.consultarIndicadorIndiceLesoes(
				this.formGroup.get("anoIndicadorIndiceLesao").value
			);
		});
	}

	atualizarIndicadoresLesao() {
		this.consultarIndicadorIndiceLesoes(
			this.formGroup.get("anoIndicadorIndiceLesao").value
		);
	}

	consultarIndicadorIndiceLesoes(ano) {
		this.lesaoService.obterIndiceLesoes(ano).subscribe({
			error: (error) => {
				console.error(
					"Erro ao carregar o índice de lesões: " + error.error.meta.message
				);
			},
			next: (result) => {
				this.showIndiceLesoes = true;
				this.indiceLesoes = result;
				this.cd.detectChanges();
			},
		});
	}
}
