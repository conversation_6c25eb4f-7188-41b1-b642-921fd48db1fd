import { Component, Input, OnInit, ViewChild } from "@angular/core";

@Component({
	selector: "pacto-small-info-card-v2",
	templateUrl: "./small-info-card-v2.component.html",
	styleUrls: ["./small-info-card-v2.component.scss"],
})
export class SmallInfoCardV2Component implements OnInit {
	@ViewChild("tooltip", { static: true }) tooltip;

	@Input() tooltipTexto = "";
	@Input() titulo = "";
	@Input() valor = "0";
	@Input() id;

	constructor() {}

	ngOnInit() {}
}
