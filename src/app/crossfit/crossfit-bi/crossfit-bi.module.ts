import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { CrossfitBiRoutingModule } from "./crossfit-bi-routing.module";
import { CrossfitBiPainelComponent } from "./components/crossfit-bi-painel/crossfit-bi-painel.component";
import { RankingGeralComponent } from "./components/ranking-geral/ranking-geral.component";
import { DetalhesModalComponent } from "./components/detalhes-modal/detalhes-modal.component";
import { SmallInfoCardComponent } from "./components/small-info-card/small-info-card.component";
import { SmallInfoCardV2Component } from "./components/small-info-card-v2/small-info-card-v2.component";
import { MatTooltipModule } from "@angular/material/tooltip";
import { ModalRegistroLesaoComponent } from "./components/modal-registro-lesao/modal-registro-lesao.component";

@NgModule({
	imports: [
		CrossfitBiRoutingModule,
		BaseSharedModule,
		CommonModule,
		MatTooltipModule,
	],
	entryComponents: [DetalhesModalComponent, ModalRegistroLesaoComponent],
	declarations: [
		CrossfitBiPainelComponent,
		RankingGeralComponent,
		SmallInfoCardComponent,
		DetalhesModalComponent,
		SmallInfoCardV2Component,
		ModalRegistroLesaoComponent,
	],
})
export class CrossfitBiModule {}
