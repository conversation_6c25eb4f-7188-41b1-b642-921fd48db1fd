import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { TipoBenchmarkListaComponent } from "./components/tipo-benchmark-lista/tipo-benchmark-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { TipoBenchmarkEditModalComponent } from "./components/tipo-benchmark-edit-modal/tipo-benchmark-edit-modal.component";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoFuncionalidade,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const funcionalidade = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.TIPO_BENCHMARK,
	true
);

const routes: Routes = [
	{
		path: "",
		component: TipoBenchmarkListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), BaseSharedModule, CommonModule],
	entryComponents: [TipoBenchmarkEditModalComponent],
	declarations: [TipoBenchmarkListaComponent, TipoBenchmarkEditModalComponent],
})
export class TipoBenchmarkModule {}
