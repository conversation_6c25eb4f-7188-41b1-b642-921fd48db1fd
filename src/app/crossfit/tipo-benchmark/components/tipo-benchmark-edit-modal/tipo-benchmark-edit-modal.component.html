<div>
	<div class="modal-header">
		<h4 class="modal-title">
			<span
				*ngIf="verificaTitle()"
				i18n="@@crud-tipos-benchmark:modal:create:title">
				Criar Tipo de Benchmark
			</span>
			<span
				*ngIf="!verificaTitle()"
				i18n="@@crud-tipos-benchmark:modal:edit:title">
				Editar Tipo de Benchmark
			</span>
		</h4>
		<button
			(click)="dismiss()"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<pacto-input
			[control]="formGroup.get('nome')"
			[name]="'nome'"
			i18n-label="@@crud-tipos-benchmark:input:nome:label"
			i18n-mensagem="@@crud-tipos-benchmark:input:nome:mensagem"
			i18n-placeholder="@@crud-tipos-benchmark:input:nome:placeholder"
			label="Nome"
			mensagem="Fornecer um nome com pelo menos 3 digitos."
			placeholder="Nome do tipo benchmark"></pacto-input>
	</div>
	<div class="modal-footer">
		<button
			(click)="dismiss()"
			class="btn btn-secondary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Cancelar
		</button>
		<button
			(click)="close()"
			class="btn btn-primary"
			i18n="@@buttons:salvar"
			type="button">
			Salvar
		</button>
	</div>
</div>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crossfit:tipos-benchmark:mensagem:campos-obrigatorio">
	Campos obrigatorio não preenchido!
</span>
