import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-tipo-benchmark-edit-modal",
	templateUrl: "./tipo-benchmark-edit-modal.component.html",
	styleUrls: ["./tipo-benchmark-edit-modal.component.scss"],
})
export class TipoBenchmarkEditModalComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
	});
	statusFormulario = false;
	verificado = false;

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.verificaTitle();
	}

	verificaTitle() {
		if (!this.verificado) {
			this.verificado = true;

			this.statusFormulario = this.formGroup.get("nome").value === "";
		}

		return this.statusFormulario;
	}

	dismiss() {
		this.verificado = false;
		this.openModal.dismiss();
	}

	private validForm() {
		if (!this.formGroup.valid) {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
			return false;
		} else {
			return true;
		}
	}

	close() {
		this.formGroup.get("nome").markAsTouched();
		if (this.validForm()) {
			this.verificado = false;
			this.openModal.close(this.formGroup.getRawValue());
		} else {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
		}
	}

	get Validators() {
		return Validators;
	}
}
