import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { ModalService } from "@base-core/modal/modal.service";
import { ApiResponseList } from "@base-core/rest/rest.model";

import { RestService } from "@base-core/rest/rest.service";
import {
	Nivel,
	TipoBenchmark,
	TreinoApiTipoBenchmarkService,
} from "treino-api";
import {
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { TipoBenchmarkEditModalComponent } from "../tipo-benchmark-edit-modal/tipo-benchmark-edit-modal.component";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-tipo-benchmark-lista",
	templateUrl: "./tipo-benchmark-lista.component.html",
	styleUrls: ["./tipo-benchmark-lista.component.scss"],
})
export class TipoBenchmarkListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("removeError", { static: true }) removeError;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("conflito", { static: true }) conflito;

	tipoBenchmark: TipoBenchmark = new (class implements TipoBenchmark {
		id: string;
		nome: string;
	})();

	data: ApiResponseList<Nivel> = {
		content: [],
	};
	loading = false;
	ready = false;
	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});

	itemToRemove;
	nomeTipoBenchmark = "";

	constructor(
		private tipoBenchmarkService: TreinoApiTipoBenchmarkService,
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private rest: RestService,
		public sessionServive: SessionService,
		private modal: NgbModal
	) {}

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;

	ngOnInit() {
		this.configTable();
		this.ready = true;
	}

	btnClickHandler() {
		const modalRef = this.modal.open(TipoBenchmarkEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue("");
		modalRef.result.then((result) => {
			this.tipoBenchmark = result;
			const createSuccess = this.createSuccess.nativeElement.innerHTML;
			this.tipoBenchmarkService
				.criarTipoBenchmark(this.tipoBenchmark)
				.subscribe(() => {
					this.snotifyService.success(createSuccess);
					this.fetchData();
				});
		});
	}

	btnEditHandler(item) {
		const modalRef = this.modal.open(TipoBenchmarkEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue(item.nome);
		modalRef.result.then((result) => {
			this.tipoBenchmark = result;
			this.tipoBenchmark.id = item.id;
			const editSuccess = this.editSuccess.nativeElement.innerHTML;
			this.tipoBenchmarkService
				.atualizarTipoBenchmark(item.id, this.tipoBenchmark)
				.subscribe((response) => {
					this.snotifyService.success(editSuccess);
					this.fetchData();
				});
		});
	}

	removeHandler(item) {
		this.nomeTipoBenchmark = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					const idToRemove = this.data.content.findIndex(
						(itemI) => itemI.id === item.id
					);
					this.tipoBenchmarkService
						.removerTipoBenchmark(item.id)
						.subscribe((result) => {
							if (result === "registro_esta_sendo_usado") {
								this.snotifyService.error(removeError);
							} else {
								this.snotifyService.success(removeSuccess);
								this.data.content.splice(idToRemove, 1);
								this.fetchData();
							}
						});
				})
				.catch(() => {});
		});
	}

	private configTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("tipos-benchmark"),
			logUrl: this.rest.buildFullUrl("log/tipos-benchmarks"),
			quickSearch: true,
			buttons: {
				conteudo: this.buttonName,
				nome: "add",
				id: "adicionarTipoBenchmark",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	get Validators() {
		return Validators;
	}
}
