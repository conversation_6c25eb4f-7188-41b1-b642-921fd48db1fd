<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'TIPOS_BENCHMARK'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			[filterConfig]="false"
			[sessionService]="sessionServive"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="crossTipoBenchmarks"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<div #conflito [hidden]="true" id="help-message-duplicated-entity-name">
	Já existe um cadastro com esse mesmo nome
</div>
<!--title table-->
<ng-template #titulo>
	<span i18n-title="@@crud-tipos-benchmark:title">Tipos Benchmark</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-tipos-benchmark:description">
		Gerencie os tipos Benchmark cadastrados.
	</span>
</ng-template>

<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-tipos-benchmark:table:nome">Nome</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-tipos-tables:acoes">Ações</span>
</ng-template>

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-tipos-benchmark:remove-modal:title">
	Remover Tipo Benchmark ?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud-tipos-benchmark:remove-modal:body">
	Deseja remover o tipo benchmark {{ nomeTipoBenchmark }}?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud-tipos-benchmark:remove:success">
	Tipo benchmark removido com sucesso.
</span>
<span #removeError [hidden]="true" i18n="@@crud-tipos-benchmark:remove:error">
	Não foi possivel excluir o tipo de benchmark pois ele tem vínculo!
</span>

<span
	#createSuccess
	[hidden]="true"
	i18n="@@crud-tipos-benchmark:create:success">
	Tipo benchmark criado com sucesso.
</span>
<span #editSuccess [hidden]="true" i18n="@@crud-tipos-benchmark:edit:success">
	Tipo benchmark editado com sucesso.
</span>
<!--tooltip icons-->
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
