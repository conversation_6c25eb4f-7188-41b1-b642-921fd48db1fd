import { Injectable, EventEmitter } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { Wod, RankingAluno, RankingNivelSigla } from "treino-api";

export enum MONITOR_RANKING_EXIBICAO {
	LISTA = "LISTA",
	GRADE = "GRADE",
}

export enum MONITOR_VIEW_MODE {
	WOD = "WOD",
	RANKING = "RANKING",
	NOTHING = "NOTHING",
}

export enum MONITOR_RANKING_ORGANIZACAO {
	UNISSEX = "UNISSEX",
	MASCULINO = "MASCULINO",
	FEMININO = "FEMININO",
}

export interface MonitorOptions {
	resultados?: boolean;
	parceiros?: boolean;
	visitantes?: boolean;
	fonte?: string;
	workout?: {
		active?: boolean;
	};
	ranking?: {
		active?: boolean;
		exibicao?: MONITOR_RANKING_EXIBICAO;
		organizacao?: MONITOR_RANKING_ORGANIZACAO;
	};
}

@Injectable()
export class MonitorService {
	wod: Wod;
	wodId: number;
	ranking: RankingAluno[];
	view$: BehaviorSubject<MONITOR_VIEW_MODE> = new BehaviorSubject(null);
	displayDone$: EventEmitter<boolean> = new EventEmitter();
	options$: BehaviorSubject<MonitorOptions> = new BehaviorSubject(null);
	nivelAtual$: BehaviorSubject<RankingNivelSigla> = new BehaviorSubject(null);
	screenResize$: EventEmitter<boolean> = new EventEmitter();

	constructor() {}
}
