import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { OptionMonitorComponent } from "./components/option-monitor/option-monitor.component";
import { PreviewMonitorComponent } from "./components/preview-monitor/preview-monitor.component";
import { RankingComponent } from "./components/ranking/ranking.component";
import { MonitorWodComponent } from "./components/monitor-wod/monitor-wod.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Routes } from "@angular/router";
import { NavbarNiveisComponent } from "./components/ranking/components/navbar-niveis/navbar-niveis.component";
import { SlideshowModule } from "ng-simple-slideshow";
import { MonitorRootComponent } from "./components/monitor-root/monitor-root.component";
import { MonitorControladorComponent } from "./components/monitor-controlador/monitor-controlador.component";
import { MonitorViewComponent } from "./components/monitor-view/monitor-view.component";
import { MonitorRankingListaSimplesComponent } from "./components/monitor-ranking-lista-simples/monitor-ranking-lista-simples.component";
import { CrossfitSharedModule } from "../crossfit-shared/crossfit-shared.module";

const routes: Routes = [
	{
		path: "visualizar",
		component: MonitorViewComponent,
		data: { fullscreen: true },
	},
	{
		path: "preview",
		component: PreviewMonitorComponent,
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		SlideshowModule,
		CommonModule,
		BaseSharedModule,
		CrossfitSharedModule,
	],
	declarations: [
		OptionMonitorComponent,
		PreviewMonitorComponent,
		MonitorRootComponent,
		MonitorWodComponent,
		NavbarNiveisComponent,
		RankingComponent,
		MonitorControladorComponent,
		MonitorViewComponent,
		MonitorRankingListaSimplesComponent,
	],
})
export class MonitorModule {}
