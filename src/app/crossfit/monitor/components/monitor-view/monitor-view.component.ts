import { Component, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import {
	MonitorService,
	MonitorOptions,
	MONITOR_RANKING_ORGANIZACAO,
	MONITOR_RANKING_EXIBICAO,
} from "../../monitor.service";

interface MonitorUrlOptions {
	resultados: boolean;
	parceiros: boolean;
	visitantes: boolean;
	fonte: string;
	workoutActive: boolean;
	rankingActive: boolean;
	workoutId: number;
	rankingOrganizacao: MONITOR_RANKING_ORGANIZACAO;
	rankingExibicacao: MONITOR_RANKING_EXIBICAO;
}

@Component({
	selector: "pacto-monitor-view",
	templateUrl: "./monitor-view.component.html",
	styleUrls: ["./monitor-view.component.scss"],
	providers: [MonitorService],
})
export class MonitorViewComponent implements OnInit {
	fonteSize: number;

	constructor(
		private route: ActivatedRoute,
		private monitorService: MonitorService
	) {}

	ngOnInit() {
		// workoutId
		this.route.queryParams.subscribe((params: MonitorUrlOptions) => {
			this.parseOptions(params);
			const workoutId = params.workoutId;
			this.monitorService.wodId = workoutId;
		});
	}

	parseOptions(options: MonitorUrlOptions) {
		this.route.queryParams.subscribe((params) => {
			const result: MonitorOptions = {
				resultados: params.resultados === "true",
				parceiros: params.parceiros === "true",
				visitantes: params.visitantes === "true",
				fonte: params.fonte,
				workout: {
					active: params.workoutActive === "true",
				},
				ranking: {
					active: params.rankingActive === "true",
					organizacao: params.rankingOrganizacao,
					exibicao: params.rankingExibicao,
				},
			};
			switch (params.fonte) {
				case "1": {
					this.fonteSize = 0.012;
					break;
				}
				case "2": {
					this.fonteSize = 0.032;
					break;
				}
				case "3": {
					this.fonteSize = 0.042;
					break;
				}
				case "4": {
					this.fonteSize = 0.052;
					break;
				}
				case "5": {
					this.fonteSize = 0.062;
					break;
				}
			}
			this.monitorService.options$.next(result);
		});
	}
}
