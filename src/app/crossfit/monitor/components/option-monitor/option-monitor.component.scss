@import "src/assets/scss/pacto/plataforma-import";

.line {
	display: flex;
	justify-content: space-between;
	border-bottom: 1px solid #f3f3f3;
	padding: 6px 0px 6px 0px;
}

.selected-toggle {
	display: flex;

	.selected-no {
		flex-grow: 1;
		padding-right: 20px;
		color: #a5a5a5;

		&.nao-selecionado {
			font-weight: 400;
			color: #666666;
		}
	}

	.option-toggle {
		flex-grow: 1;
	}

	.selected-yes {
		flex-grow: 1;
		padding-left: 20px;
		color: #a5a5a5;

		&.sim-selecionado {
			font-weight: 400;
			color: #666666;
		}
	}
}

.exibir {
	cursor: pointer;
}

.name-label {
	font-size: 16px;
	font-weight: 700;
	color: #333333;
}

.space-label-icon {
	padding-left: 3.44px;
}

.toolbar-radio-button {
	font-size: 12px;
	font-weight: 400;
	color: #a5a5a5;
}

.toolbar-container {
	background-color: #f9f9f9;
	padding: 15px;
}

.radio-button {
	font-size: 12px;
	font-weight: 400;
	color: #666666;
	opacity: 1;
}

.select-space {
	flex-grow: 1;
	padding-left: 76px;
}

pacto-select .form-group {
	margin-bottom: 5px;
}

.select-workout {
	width: 200px;
}

.actions-monitor {
	display: flex;
	margin-top: 15px;

	button {
		margin: 0px 145px 0px 145px;
	}
}

.acoes {
	display: flex;

	.pct {
		font-size: 30px;
		cursor: pointer;
	}

	.pct-toggle-left,
	.pct-toggle-right {
		position: relative;
		padding-right: 5px;
	}

	.toggle-box {
		color: $pretoPri;
		@extend .type-h6;

		&.icon-inativo {
			color: $cinzaClaroPri;
		}
	}
}
