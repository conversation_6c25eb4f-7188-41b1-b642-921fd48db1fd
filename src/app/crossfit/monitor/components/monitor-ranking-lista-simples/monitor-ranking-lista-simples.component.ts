import {
	Component,
	ElementRef,
	Inject,
	OnInit,
	ViewChild,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";
import { DOCUMENT } from "@angular/common";

import { debounceTime } from "rxjs/operators";

import { MonitorService, MONITOR_VIEW_MODE } from "../../monitor.service";
import { RankingAluno } from "treino-api";
import { Subscription } from "rxjs";

export enum RANKING_DISPLAY_STAGE {
	NO_SCROLL = "NO_SCROLL",
	PRE_DELAY = "PRE_DELAY",
	POST_DELAY = "POST_DELAY",
	SCROLL = "SCROLL",
}

@Component({
	selector: "pacto-monitor-ranking-lista-simples",
	templateUrl: "./monitor-ranking-lista-simples.component.html",
	styleUrls: ["./monitor-ranking-lista-simples.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MonitorRankingListaSimplesComponent implements OnInit, On<PERSON><PERSON>roy {
	@ViewChild("containerRanking", { static: false })
	containerRanking: ElementRef;
	@ViewChild("containerRankingLista", { static: true })
	containerRankingLista: ElementRef;
	@ViewChild("imagem", { static: false }) imagem;

	displayStage: RANKING_DISPLAY_STAGE;
	SCROLL_START_STOP_DELAY = 5000;
	VIEWING_TIME_NO_SCROLL = 10000;
	dados: RankingAluno[] = [];
	height = "";
	/**
	 * Indicates if scroll has finished, in which case the view is frozen for
	 * a few seconds at the bottom.
	 */
	private finished = false;
	private startDelayTimeout;
	private endDelayTimeout;
	private noScrollTimeout;
	private scrollLoopInterval;
	private viewSubscription: Subscription;

	constructor(
		@Inject(DOCUMENT) private document: any,
		private monitorService: MonitorService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.monitorService.screenResize$.pipe(debounceTime(100)).subscribe(() => {
			this.setUpDimensions();
			setTimeout(() => {
				this.displayContent();
			});
		});

		setTimeout(() => {
			this.setUpDimensions();
		});
		this.viewSubscription = this.monitorService.view$.subscribe((view) => {
			if (view === MONITOR_VIEW_MODE.RANKING) {
				this.dados = this.monitorService.ranking;
				this.cd.detectChanges();
				setTimeout(() => {
					this.displayContent();
				});
			}
		});
	}

	ngOnDestroy() {
		if (this.viewSubscription) {
			this.viewSubscription.unsubscribe();
		}
		this.cleanHandles();
	}

	/**
	 * Screens without scroll will be displayed for 20 seconds.
	 * Screens with scroll will be scrolled slowly with an additional
	 * delay of 5 seconds at the start and at the end.
	 */
	private displayContent() {
		this.cleanHandles();
		this.finished = false;
		const scroll = this.containerRankingLista.nativeElement;
		scroll.scrollTop = 0;
		const scrollable = Math.abs(scroll.clientHeight - scroll.scrollHeight);

		if (scrollable) {
			this.displayStage = RANKING_DISPLAY_STAGE.PRE_DELAY;
			this.startDelayTimeout = setTimeout(() => {
				this.scrollLoopInterval = setInterval(() => {
					this.scrollLoop();
				}, 90);
			}, this.SCROLL_START_STOP_DELAY);
		} else {
			this.displayStage = RANKING_DISPLAY_STAGE.NO_SCROLL;
			this.noScrollTimeout = setTimeout(() => {
				this.monitorService.displayDone$.emit(true);
			}, this.VIEWING_TIME_NO_SCROLL);
		}
	}

	private scrollLoop() {
		this.displayStage = RANKING_DISPLAY_STAGE.SCROLL;
		const scroll = this.containerRankingLista.nativeElement;
		const scrollable = Math.abs(scroll.clientHeight - scroll.scrollHeight);
		const leftover = Math.abs(scrollable - scroll.scrollTop);
		if (leftover < 5 && !this.finished) {
			this.finished = true;
			clearInterval(this.scrollLoopInterval);
			this.displayStage = RANKING_DISPLAY_STAGE.POST_DELAY;
			this.endDelayTimeout = setTimeout(() => {
				this.monitorService.displayDone$.emit(true);
			}, this.SCROLL_START_STOP_DELAY);
		} else if (leftover >= 5) {
			scroll.scrollTop = scroll.scrollTop + 1;
		}
	}

	private cleanHandles() {
		clearTimeout(this.startDelayTimeout);
		clearTimeout(this.endDelayTimeout);
		clearTimeout(this.noScrollTimeout);
		clearTimeout(this.scrollLoopInterval);
	}

	get options() {
		return this.monitorService.options$.value;
	}

	get exibirLista() {
		return this.options.ranking.exibicao === "LISTA";
	}

	getResultados(aluno: RankingAluno) {
		const camposDisponiveis = [];
		let campos: string[];
		if (aluno.campos) {
			campos = aluno.campos.split(",");
		}
		const labelMap = {
			repeticoes: "REPETIÇÕES",
			rounds: "ROUNDS",
			peso: "PESO",
			tempo: "TEMPO",
		};
		let result = "";

		if (aluno.campos && Array.isArray(campos)) {
			campos.forEach((campo) => {
				if (campo === "repeticoes") {
					camposDisponiveis.push("repeticoes");
				} else if (campo === "rounds") {
					camposDisponiveis.push("rounds");
				} else if (campo === "peso") {
					camposDisponiveis.push("peso");
				} else if (campo === "tempo") {
					camposDisponiveis.push("tempo");
				}
			});
		}

		camposDisponiveis.forEach((campo, index) => {
			if (index > 0 && index === camposDisponiveis.length - 1) {
				result += " E ";
			} else if (index > 0 && camposDisponiveis.length > 1) {
				result += ", ";
			}

			if (campo === "peso") {
				result += `${labelMap[campo]} - ${aluno[campo]} LBS`;
			} else if (campo === "tempo") {
				result += `${labelMap[campo]} - ${this.tempoExecucao(aluno.tempo)}`;
			} else {
				result += `${labelMap[campo]} - ${aluno[campo]}`;
			}
		});

		return result;
	}

	/**
	 * Converte o numer de segundos para o
	 * formato: mm:ss, por exemplo,  365 => 6:05.
	 */
	private tempoExecucao(tempo: number) {
		const minutos = Math.trunc(tempo / 60);
		const segundos = tempo % 60;
		const segundosString = segundos <= 9 ? `0${segundos}` : `${segundos}`;
		return `${minutos}:${segundosString}`;
	}

	private setUpDimensions() {
		this.applyImageSlideBlockHeight();
		this.applyImageHeight();
		this.cd.detectChanges();
	}

	private applyImageHeight() {
		if (this.exibirLista) {
			this.height = "";
		} else {
			if (this.imagem) {
				const height = Math.round(this.imagem.nativeElement.clientWidth) + 1;
				this.height = `${height}px`;
			}
		}
	}

	private applyImageSlideBlockHeight() {
		const baseAvaiableSpace = this.document.getElementById(
			"controlador-wrapper"
		);
		const spaceUsedByTitle = this.document.getElementById("ranking-upper-area");
		if (baseAvaiableSpace && spaceUsedByTitle) {
			const available =
				baseAvaiableSpace.clientHeight - spaceUsedByTitle.clientHeight;
			this.containerRankingLista.nativeElement.style.height = `${available}px`;
		}
	}
}
