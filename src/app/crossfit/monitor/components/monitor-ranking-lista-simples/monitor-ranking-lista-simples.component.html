<div class="scroll-wrapper-aux">
	<div #containerRankingLista class="scroll-container">
		<div *ngIf="dados.length" class="dados-ranking-grid three-column">
			<ng-container *ngIf="dados.length">
				<div
					*ngFor="let item of dados; let index = index"
					[ngClass]="{
						first: index === 0,
						second: index === 1,
						third: index === 2,
						visitor: item.visitante && options.visitantes
					}"
					[ngStyle]="{ height: height }"
					class="item">
					<div #imagem class="image">
						<img src="{{ item.foto }}" />
						<div class="posicao">
							<div class="posicao-text-wrapper">
								<div class="posicao-text">{{ item.posicao }}º</div>
							</div>
						</div>
					</div>
					<div class="info">
						<div class="unidade-valor">
							{{ item.nome }}
						</div>
						<div *ngIf="options.resultados" class="unidade-label">
							{{ getResultados(item) }}
						</div>
					</div>
				</div>
			</ng-container>
		</div>
		<div *ngIf="!dados.length" class="empty-state-wrapper">
			<div
				class="empty-state-msg"
				i18n="@@config-monitor:nivel:nenhum-resultado-lancado">
				Nenhum resultado lançado para esse nível.
			</div>
		</div>
	</div>
</div>
