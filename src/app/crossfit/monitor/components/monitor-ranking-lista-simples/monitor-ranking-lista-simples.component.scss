.scroll-wrapper-aux {
	position: relative;
	margin: 0% 3% 0%;
	overflow: hidden;
	right: 10px;
}

.scroll-container {
	overflow: auto;
	position: relative;
	right: -20px;
	border-top: 1px solid #292929;
	border-bottom: 1px solid #292929;
}

#container {
	overflow: scroll;
	height: 100px;
}

.dados-ranking-grid {
	padding: 1% 3% 1% 3%;
	display: grid;
	grid-gap: 0.5em;
	flex-grow: 1;

	&.two-column {
		grid-template-columns: repeat(2, 1fr);
	}

	&.three-column {
		grid-template-columns: repeat(3, 1fr);
	}

	&.four-column {
		grid-template-columns: repeat(4, 1fr);
	}

	&.five-column {
		grid-template-columns: repeat(5, 1fr);
	}

	&.six-column {
		grid-template-columns: repeat(6, 1fr);
	}

	&.seven-column {
		grid-template-columns: repeat(7, 1fr);
	}

	&.eight-column {
		grid-template-columns: repeat(8, 1fr);
	}

	.item {
		width: 100%;
		display: flex;

		&.first .image img {
			border-color: #ffb12a;
		}

		&.second .image img {
			border-color: #a5a5a5;
		}

		&.third .image img {
			border-color: #b96c28;
		}

		&.visitor .image img {
			border-color: #ff0000;
		}

		.image {
			flex-basis: 30%;
			flex-shrink: 0;
			position: relative;

			img {
				width: 100%;
				border-radius: 50%;
				border: 3px solid #ffffff;
			}
		}

		&.first .posicao {
			background-color: #ffd700;
			color: #3e464a;
		}

		&.second .posicao {
			background-color: #8e7878;
			color: #ffffff;
		}

		&.third .posicao {
			background-color: #cd7f32;
			color: #ffffff;
		}

		.posicao {
			position: absolute;
			bottom: 0%;
			right: 0%;
			width: 35%;
			height: 35%;
			font-size: 45%;
			font-weight: 700;
			text-align: center;
			border-radius: 50%;
			background-color: #ffffff;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}
	}
}

.info {
	width: 100%;
	padding-left: 2%;
	padding-top: 1%;

	.unidade-label {
		font-size: 40%;
		color: #c7c7c7;
		font-weight: 500;
		text-align: left;
	}

	.unidade-valor {
		font-size: 50%;
		color: #ffffff;
		font-weight: 800;
	}
}

.dados-ranking-lista {
	padding: 2% 10% 0% 10%;
	background-color: #000000;

	.dados-usuario {
		width: 100%;
		display: flex;
		padding: 5px 15px 5px 15px;

		&.first {
			position: relative;
			display: block;
		}

		.image-lista {
			flex-basis: 10%;
			flex-shrink: 0;
			position: relative;

			&.first {
				text-align: center;

				img {
					border-color: #ffd700;
					width: 13%;
				}
			}

			img {
				width: 100%;
				border-radius: 50%;
				border: 2px solid #ffffff;
			}
		}

		.info-lista {
			width: 82%;
			margin: auto 1%;

			&.first {
				text-align: center;
				margin-top: 1%;
				width: 100%;
			}

			.unidade-label-lista-ranking {
				font-size: 64%;
				color: #c7c7c7;
				font-weight: 500;

				&.first {
					font-size: 74%;
				}
			}

			.unidade-valor-lista-ranking {
				font-size: 80%;
				color: #ffffff;
				font-weight: 800;
			}
		}

		.posicao-lista {
			width: 8%;
			position: relative;
			margin: auto 0px;
			font-size: 90%;
			font-weight: 700;
			line-height: 250%;
			text-align: center;
			border-radius: 50%;
			border: 2px solid #ffffff;
			color: #ffffff;

			&:after {
				content: "";
				display: block;
				padding-bottom: 100%;
			}

			&.first {
				position: absolute;
				border-color: #ffd700;
				background-color: #ffd700;
				color: #000000;
				right: 0;
				top: 62%;
				left: 52%;
				width: 5%;
				font-size: 70%;
			}

			&.second {
				border-color: #c0c0c0;
				color: #c0c0c0;
			}

			&.third {
				border-color: #cd7f32;
				color: #cd7f32;
			}

			.posicao-text-wrapper {
				position: absolute;
				top: 0px;
				left: 0px;
				right: 0px;
				bottom: 0px;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}
		}
	}
}

.empty-state-wrapper {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;

	.empty-state-msg {
		font-size: 77%;
		text-align: center;
	}
}
