import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewEncapsulation } from "@angular/core";
import { MonitorService } from "../../monitor.service";

@Component({
	selector: "pacto-preview-monitor",
	templateUrl: "./preview-monitor.component.html",
	styleUrls: ["./preview-monitor.component.scss"],
	encapsulation: ViewEncapsulation.None,
	providers: [MonitorService],
})
export class PreviewMonitorComponent implements OnInit, OnDestroy {
	constructor() {}

	ngOnInit() {}

	ngOnDestroy() {}
}
