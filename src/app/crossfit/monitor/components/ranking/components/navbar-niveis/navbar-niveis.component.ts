import {
	Component,
	Input,
	OnInit,
	Output,
	EventEmitter,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnDestroy,
} from "@angular/core";
import { MonitorService } from "src/app/crossfit/monitor/monitor.service";
import { RankingNivelSigla } from "treino-api";
import { Subscription } from "rxjs";

@Component({
	selector: "pacto-navbar-niveis",
	templateUrl: "./navbar-niveis.component.html",
	styleUrls: ["./navbar-niveis.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NavbarNiveisComponent implements OnInit, OnDestroy {
	itemId: RankingNivelSigla;
	private nivelSubscription: Subscription;

	constructor(
		private monitorService: MonitorService,
		private cd: ChangeDetectorRef
	) {}

	niveis = [
		{ id: RankingNivelSigla.IN, nome: "Iniciante" },
		{ id: RankingNivelSigla.SC, nome: "Scale" },
		{ id: RankingNivelSigla.AM, nome: "Amador" },
		{ id: RankingNivelSigla.RX, nome: "RX" },
	];

	ngOnInit() {
		this.nivelSubscription = this.monitorService.nivelAtual$.subscribe((id) => {
			this.itemId = id;
			this.cd.detectChanges();
		});
	}

	ngOnDestroy() {
		this.nivelSubscription.unsubscribe();
	}
}
