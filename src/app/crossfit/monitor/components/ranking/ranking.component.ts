import {
	Component,
	Input,
	OnInit,
	ViewEncapsulation,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
} from "@angular/core";
import { MonitorService } from "../../monitor.service";

@Component({
	selector: "pacto-ranking",
	templateUrl: "./ranking.component.html",
	styleUrls: ["./ranking.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RankingComponent implements OnInit {
	@Input() niveis;
	@ViewChild("mainTitle", { static: true }) mainTitle;

	radius: number;
	percent = 0;
	private endTime = null;
	private startTime = null;
	private intervalHandler;

	constructor() {}

	ngOnInit() {}
}
