import {
	Component,
	Input,
	OnInit,
	OnDestroy,
	ChangeDetectorRef,
} from "@angular/core";

import { Observable, Subscription } from "rxjs";
import { map } from "rxjs/operators";

import { MonitorService, MONITOR_VIEW_MODE } from "../../monitor.service";
import {
	TreinoApiWodService,
	TreinoApiRankingService,
	RankingNivelSigla,
	RankingAluno,
} from "treino-api";

enum RANKING_WOD_LOOP_VIEW {
	RANK_IN = "RANK_IN",
	RANK_SC = "RANK_SC",
	RANK_AM = "RANK_AM",
	RANK_RX = "RANK_RX",
	WOD = "WOD",
}

@Component({
	selector: "pacto-monitor-controlador",
	templateUrl: "./monitor-controlador.component.html",
	styleUrls: ["./monitor-controlador.component.scss"],
})
export class MonitorControladorComponent implements OnInit, OnDestroy {
	@Input() niveis;

	constructor(
		private monitorService: MonitorService,
		private rankingService: TreinoApiRankingService,
		private cd: ChangeDetectorRef,
		private wodService: TreinoApiWodService
	) {}

	get currentView(): MONITOR_VIEW_MODE {
		return this.monitorService.view$.value;
	}

	get MONITOR_VIEW_MODE() {
		return MONITOR_VIEW_MODE;
	}

	private rankingComplete: RankingAluno[];
	private displayDoneSubscription: Subscription;
	private rankingWodLoopView: RANKING_WOD_LOOP_VIEW;

	ngOnInit() {
		this.monitorService.options$.subscribe((options) => {
			if (options) {
				this.cleanUp();
				this.setupView(options);
			}
		});

		this.monitorService.view$.subscribe(() => {
			this.cd.detectChanges();
		});
	}

	ngOnDestroy() {
		this.cleanUp();
	}

	private cleanUp() {
		if (this.displayDoneSubscription) {
			this.displayDoneSubscription.unsubscribe();
		}
	}

	private setupView(options) {
		this.monitorService.view$.next(MONITOR_VIEW_MODE.NOTHING);
		if (options.workout.active && options.ranking.active) {
			this.handleWodAndRanking();
		} else if (options.workout.active && !options.ranking.active) {
			this.handleOnlyWod();
		} else if (!options.workout.active && options.ranking.active) {
			this.handleOnlyRanking();
		}
	}

	private handleOnlyWod() {
		this.fetchWod().subscribe(() => {
			this.monitorService.view$.next(MONITOR_VIEW_MODE.WOD);
		});

		this.displayDoneSubscription = this.monitorService.displayDone$.subscribe(
			() => {
				this.fetchWod().subscribe(() => {
					this.monitorService.view$.next(MONITOR_VIEW_MODE.WOD);
				});
			}
		);
	}

	private handleOnlyRanking() {
		this.fetchRanking().subscribe(() => {
			this.monitorService.nivelAtual$.next(RankingNivelSigla.IN);
			const data = this.buildDataArray();
			this.monitorService.ranking = data;
			this.monitorService.view$.next(MONITOR_VIEW_MODE.RANKING);
		});

		this.displayDoneSubscription = this.monitorService.displayDone$.subscribe(
			() => {
				this.fetchRanking().subscribe(() => {
					this.progressNivel();
					const data = this.buildDataArray();
					this.monitorService.ranking = data;
					this.monitorService.view$.next(MONITOR_VIEW_MODE.RANKING);
				});
			}
		);
	}

	private progressNivel() {
		switch (this.monitorService.nivelAtual$.value) {
			case RankingNivelSigla.AM:
				this.monitorService.nivelAtual$.next(RankingNivelSigla.RX);
				break;
			case RankingNivelSigla.RX:
				this.monitorService.nivelAtual$.next(RankingNivelSigla.IN);
				break;
			case RankingNivelSigla.IN:
				this.monitorService.nivelAtual$.next(RankingNivelSigla.SC);
				break;
			case RankingNivelSigla.SC:
				this.monitorService.nivelAtual$.next(RankingNivelSigla.AM);
				break;
		}
	}

	private handleWodAndRanking() {
		this.rankingWodLoopView = RANKING_WOD_LOOP_VIEW.WOD;
		this.fetchWod().subscribe(() => {
			this.monitorService.view$.next(MONITOR_VIEW_MODE.WOD);
			this.wodRankingIncrementCycle();
		});

		this.displayDoneSubscription = this.monitorService.displayDone$.subscribe(
			() => {
				if (this.rankingWodLoopView === RANKING_WOD_LOOP_VIEW.WOD) {
					this.fetchWod().subscribe(() => {
						this.monitorService.view$.next(MONITOR_VIEW_MODE.WOD);
						this.wodRankingIncrementCycle();
					});
				} else {
					this.fetchRanking().subscribe(() => {
						switch (this.rankingWodLoopView) {
							case RANKING_WOD_LOOP_VIEW.RANK_IN:
								this.monitorService.nivelAtual$.next(RankingNivelSigla.IN);
								break;
							case RANKING_WOD_LOOP_VIEW.RANK_SC:
								this.monitorService.nivelAtual$.next(RankingNivelSigla.SC);
								break;
							case RANKING_WOD_LOOP_VIEW.RANK_AM:
								this.monitorService.nivelAtual$.next(RankingNivelSigla.AM);
								break;
							case RANKING_WOD_LOOP_VIEW.RANK_RX:
								this.monitorService.nivelAtual$.next(RankingNivelSigla.RX);
								break;
						}
						const data = this.buildDataArray();
						this.monitorService.ranking = data;
						this.monitorService.view$.next(MONITOR_VIEW_MODE.RANKING);
						this.wodRankingIncrementCycle();
					});
				}
			}
		);
	}

	private wodRankingIncrementCycle() {
		switch (this.rankingWodLoopView) {
			case RANKING_WOD_LOOP_VIEW.WOD:
				this.rankingWodLoopView = RANKING_WOD_LOOP_VIEW.RANK_IN;
				break;
			case RANKING_WOD_LOOP_VIEW.RANK_IN:
				this.rankingWodLoopView = RANKING_WOD_LOOP_VIEW.RANK_SC;
				break;
			case RANKING_WOD_LOOP_VIEW.RANK_SC:
				this.rankingWodLoopView = RANKING_WOD_LOOP_VIEW.RANK_AM;
				break;
			case RANKING_WOD_LOOP_VIEW.RANK_AM:
				this.rankingWodLoopView = RANKING_WOD_LOOP_VIEW.RANK_RX;
				break;
			case RANKING_WOD_LOOP_VIEW.RANK_RX:
				this.rankingWodLoopView = RANKING_WOD_LOOP_VIEW.WOD;
				break;
		}
	}

	private buildDataArray() {
		let result: RankingAluno[] = [];
		const options = this.monitorService.options$.value;
		const alunos = this.rankingComplete;
		const ranking = this.monitorService.nivelAtual$.value;
		if (options.ranking.organizacao === "UNISSEX") {
			result = this.fetchUnisexArray(alunos, ranking);
		} else if (options.ranking.organizacao === "MASCULINO") {
			result = this.fetchSingleSexArray(alunos, ranking, "M");
		} else if (options.ranking.organizacao === "FEMININO") {
			result = this.fetchSingleSexArray(alunos, ranking, "F");
		}
		return result;
	}

	private fetchUnisexArray(
		alunos: RankingAluno[],
		ranking: RankingNivelSigla
	): RankingAluno[] {
		const out: RankingAluno[] = [];
		alunos.forEach((aluno, index) => {
			const filterMatches = aluno.nivelSigla === ranking;
			if (filterMatches) {
				aluno.posicao = out.length + 1;
				out.push(Object.assign({}, aluno));
			}
		});
		return out;
	}

	private fetchSingleSexArray(
		alunos: RankingAluno[],
		ranking: RankingNivelSigla,
		sex
	) {
		const out = [];
		alunos.forEach((aluno, index) => {
			const filterMatches = aluno.nivelSigla === ranking;
			const sexMatches = aluno.sexo === sex;
			if (filterMatches && sexMatches) {
				aluno.posicao = out.length + 1;
				out.push(Object.assign({}, aluno));
			}
		});
		return out;
	}

	private fetchRanking(): Observable<boolean> {
		const wodId = this.monitorService.wodId;
		return this.rankingService.obterRankingAlunos(wodId).pipe(
			map((alunos: RankingAluno[]) => {
				this.rankingComplete = alunos;
				return true;
			})
		);
	}

	private fetchWod(): Observable<boolean> {
		const wodId = this.monitorService.wodId;
		return this.wodService.obterWod(wodId).pipe(
			map((wod) => {
				this.monitorService.wod = wod;
				return true;
			})
		);
	}
}
