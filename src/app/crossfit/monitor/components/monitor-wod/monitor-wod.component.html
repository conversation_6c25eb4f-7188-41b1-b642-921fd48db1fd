<div class="title-page" id="wod-upper-area">
	<div #wodTitle class="title-desc">{{ wod ? wod.nome : "" }}</div>
	<div class="title-desc" ngClass="align">
		{{ wod && wod.tipoWod ? wod.tipoWod.nome : "" }}
	</div>
</div>

<div #scrollContainer class="wod-wrapper-aux">
	<div class="wod-wrapper">
		<div #scrollHandler class="wod">
			<div *ngFor="let dado of dados" class="column">
				<div class="titulo">
					{{ dado.column }}
				</div>
				<div class="dados">
					<ng-container *ngIf="dado.dados.forEach">
						<div *ngFor="let item of dado.dados">{{ item }}</div>
					</ng-container>
					<ng-container *ngIf="!dado.dados.forEach">
						{{ dado.dados }}
					</ng-container>
				</div>
			</div>
		</div>
	</div>
</div>

<span
	#columnAlongamento
	[hidden]="true"
	i18n="@@config-monitor:preview-wod:alongamento">
	Mobilidade
</span>
<span
	#columnComplex
	[hidden]="true"
	i18n="@@config-monitor:preview-wod:complex">
	Complex
</span>
<span
	#columnTecnica
	[hidden]="true"
	i18n="@@config-monitor:preview-wod:tecnica">
	Técnica
</span>
<span #columnWod [hidden]="true" i18n="@@config-monitor:preview-wod:wod">
	Wod
</span>
<span #columnAquecimento [hidden]="true">Aquecimento</span>
