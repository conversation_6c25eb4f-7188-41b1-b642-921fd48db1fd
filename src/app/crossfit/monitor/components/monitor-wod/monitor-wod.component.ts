import { debounceTime } from "rxjs/operators";
import {
	Component,
	OnInit,
	ViewChild,
	ViewEncapsulation,
	ElementRef,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnDestroy,
} from "@angular/core";

import { MonitorService, MONITOR_VIEW_MODE } from "../../monitor.service";
import { Subscription } from "rxjs";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { SessionService } from "@base-core/client/session.service";

declare var document;

interface Dado {
	column: string;
	dados: string;
}

export enum WOD_DISPLAY_STAGE {
	NO_SCROLL = "NO_SCROLL",
	PRE_DELAY = "PRE_DELAY",
	POST_DELAY = "POST_DELAY",
	SCROLL = "SCROLL",
}

@Component({
	selector: "pacto-monitor-wod",
	templateUrl: "./monitor-wod.component.html",
	styleUrls: ["./monitor-wod.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MonitorWodComponent implements OnInit, OnDestroy {
	@ViewChild("scrollContainer", { static: true }) scrollContainer: ElementRef;
	@ViewChild("scrollHandler", { static: true }) scrollHandler: ElementRef;
	@ViewChild("wodTitle", { static: true }) wodTitle: ElementRef;

	@ViewChild("columnAquecimento", { static: true }) columnAquecimento;
	@ViewChild("columnAlongamento", { static: true }) columnAlongamento;
	@ViewChild("columnComplex", { static: true }) columnComplex;
	@ViewChild("columnTecnica", { static: true }) columnTecnica;
	@ViewChild("columnWod", { static: true }) columnWod;

	columns: Array<string> = [];
	dados: Array<Dado> = [];
	SCROLL_START_STOP_DELAY = 5000;
	VIEWING_TIME_NO_SCROLL = 10000;

	displayStage: WOD_DISPLAY_STAGE;
	private finished = false;
	private startDelayTimeout;
	private endDelayTimeout;
	private noScrollTimeout;
	private scrollLoopInterval;
	private viewSubscription: Subscription;

	get wod() {
		return this.monitorService.wod;
	}

	constructor(
		private monitorService: MonitorService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.monitorService.screenResize$.pipe(debounceTime(100)).subscribe(() => {
			this.applyImageSlideBlockHeight();
			setTimeout(() => {
				this.displayContent();
			});
		});

		setTimeout(() => {
			this.applyImageSlideBlockHeight();
		});
		this.viewSubscription = this.monitorService.view$.subscribe((view) => {
			if (view === MONITOR_VIEW_MODE.WOD) {
				this.camposASerPreenchido();
				this.cd.detectChanges();
				setTimeout(() => {
					this.displayContent();
				});
			}
		});
		this.sessionService.notificarRecursoEmpresa(RecursoSistema.MONITOR_WOD);
	}

	ngOnDestroy(): void {
		this.viewSubscription.unsubscribe();
	}

	/**
	 * Screens without scroll will be displayed for 20 seconds.
	 * Screens with scroll will be scrolled slowly with an additional
	 * delay of 5 seconds at the start and at the end.
	 */
	private displayContent() {
		this.cleanHandles();
		this.finished = false;
		const scroll = this.scrollHandler.nativeElement;
		scroll.scrollTop = 0;
		const scrollable = Math.abs(scroll.clientHeight - scroll.scrollHeight);

		if (scrollable) {
			this.displayStage = WOD_DISPLAY_STAGE.PRE_DELAY;
			this.startDelayTimeout = setTimeout(() => {
				this.scrollLoopInterval = setInterval(() => {
					this.scrollLoop();
				}, 90);
			}, this.SCROLL_START_STOP_DELAY);
		} else {
			this.displayStage = WOD_DISPLAY_STAGE.NO_SCROLL;
			this.noScrollTimeout = setTimeout(() => {
				this.monitorService.displayDone$.emit(true);
			}, this.VIEWING_TIME_NO_SCROLL);
		}
	}

	private cleanHandles() {
		clearTimeout(this.startDelayTimeout);
		clearTimeout(this.endDelayTimeout);
		clearTimeout(this.noScrollTimeout);
		clearInterval(this.scrollLoopInterval);
	}

	private camposASerPreenchido() {
		this.dados = [];
		const columnAquecimento = this.columnAquecimento.nativeElement.innerHTML;
		const columnAlongamento = this.columnAlongamento.nativeElement.innerHTML;
		const columnComplex = this.columnComplex.nativeElement.innerHTML;
		const columnTecnica = this.columnTecnica.nativeElement.innerHTML;
		const columnWod = this.columnWod.nativeElement.innerHTML;

		if (this.wod) {
			let dado: any = {};

			if (this.wod.aquecimento) {
				dado = {};
				dado.column = columnAquecimento.toUpperCase();
				dado.dados = this.wod.aquecimento.split("\n");
				this.dados.push(dado);
			}
			if (this.wod.alongamentoMobilidade) {
				dado = {};
				dado.column = columnAlongamento.toUpperCase();
				dado.dados = this.wod.alongamentoMobilidade.split("\n");
				this.dados.push(dado);
			}
			if (this.wod.complexEmom) {
				dado = {};
				dado.column = columnComplex.toLocaleUpperCase();
				dado.dados = this.wod.complexEmom.split("\n");
				this.dados.push(dado);
			}
			if (this.wod.parteTecnicaSkill) {
				dado = {};
				dado.column = columnTecnica.toUpperCase();
				dado.dados = this.wod.parteTecnicaSkill.split("\n");
				this.dados.push(dado);
			}
			if (this.wod.wod) {
				dado = {};
				dado.column = columnWod.toLocaleUpperCase();
				dado.dados = this.wod.wod.split("\n");
				this.dados.push(dado);
			}
		}
	}

	private scrollLoop() {
		this.displayStage = WOD_DISPLAY_STAGE.SCROLL;
		const scroll = this.scrollHandler.nativeElement;
		const scrollable = Math.abs(scroll.clientHeight - scroll.scrollHeight);
		const leftover = Math.abs(scrollable - scroll.scrollTop);
		if (leftover < 10 && !this.finished) {
			this.finished = true;
			clearInterval(this.scrollLoopInterval);
			this.displayStage = WOD_DISPLAY_STAGE.POST_DELAY;
			this.endDelayTimeout = setTimeout(() => {
				this.monitorService.displayDone$.emit(true);
			}, this.SCROLL_START_STOP_DELAY);
		} else if (leftover >= 10) {
			scroll.scrollTop = scroll.scrollTop + 1;
		}
	}

	private applyImageSlideBlockHeight() {
		const baseAvaiableSpace = document.getElementById("controlador-wrapper");
		const spaceUsedByTitle = document.getElementById("wod-upper-area");
		if (baseAvaiableSpace && spaceUsedByTitle) {
			const available =
				baseAvaiableSpace.clientHeight - spaceUsedByTitle.clientHeight;
			this.scrollContainer.nativeElement.style.height = `${available}px`;
		}
	}
}
