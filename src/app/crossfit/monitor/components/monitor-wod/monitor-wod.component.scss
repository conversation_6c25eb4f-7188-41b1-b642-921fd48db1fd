.title-page {
	display: flex;
	justify-content: space-between;
	padding: 3% 3% 3% 3%;

	.title-desc {
		color: #ffffff;
		font-size: 100%;
		font-weight: 900;
		text-transform: uppercase;
	}
}

#ranking-loader {
	position: absolute;
	top: 0px;
	left: 0px;
}

.wod-wrapper-aux {
	position: relative;
	overflow: hidden;
	margin: 0% 3%;
	right: 10px;
}

.wod-wrapper {
	position: relative;
	right: -20px;
	height: 100%;
	border-top: 1px solid #292929;
	border-bottom: 1px solid #292929;

	.wod {
		display: flex;
		height: 100%;
		margin: 0% 0%;
		flex-grow: 1;
		overflow-y: scroll;

		.column {
			flex-grow: 1;
			flex-basis: 25%;

			.titulo {
				color: #f61920;
				font-size: 70%;
				font-weight: 700;
				padding-bottom: 1%;
			}

			.dados {
				color: #ffffff;
				font-size: 60%;
				font-weight: 700;
				margin-bottom: 3vh;
				padding: 0em 0.3em;
				word-break: break-word;
				text-transform: capitalize;
			}
		}
	}
}
