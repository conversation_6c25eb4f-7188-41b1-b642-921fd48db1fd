import { debounceTime } from "rxjs/operators";
import {
	Component,
	Input,
	OnInit,
	ViewChild,
	ChangeDetectorRef,
} from "@angular/core";

import { MonitorService, MonitorOptions } from "../../monitor.service";

declare var document;

@Component({
	selector: "pacto-monitor-root",
	templateUrl: "./monitor-root.component.html",
	styleUrls: ["./monitor-root.component.scss"],
})
export class MonitorRootComponent implements OnInit {
	@Input() preview = false;
	@Input() fonteSize: number;

	niveis = [
		{ id: "1", nome: "Iniciate" },
		{ id: "2", nome: "Scale" },
		{ id: "3", nome: "Amador" },
		{ id: "4", nome: "RX" },
	];

	proporcaoImagemParceiros = 1.7777;
	/**
	 * 0.03 => 3% da largura do quadro de espaco entre dois parceiros.
	 */
	porcentagemEspacoEntreParceiros = 0.03;

	/**
	 * CALCULATED VALUES
	 */
	imageHeight;
	imageWidth;
	spaceBetween;

	parceiroImagens = [];
	parceirosNovaLista: Array<any> = [];

	@ViewChild("planoFundo", { static: true }) planoFundo;
	@ViewChild("controlador", { static: true }) controlador;

	showParceiros = false;

	constructor(
		private monitorService: MonitorService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.monitorService.options$.subscribe((options: MonitorOptions) => {
			if (options) {
				setTimeout(() => {
					this.setupDimensions(options);
				});
			}
			setTimeout(() => {
				this.cd.detectChanges();
			}, 250);
		});

		/**
		 * Handle resize updates
		 */
		this.monitorService.screenResize$.pipe(debounceTime(100)).subscribe(() => {
			this.setupDimensions(this.monitorService.options$.value);
		});
	}

	monitorResizeHandler() {
		this.monitorService.screenResize$.emit();
	}

	private setupDimensions(options: MonitorOptions) {
		/**
		 * Ajust font size
		 */
		this.aplicarTamanhoDaFonte();
	}

	private obterLarguraQuadroConteudo() {
		return this.planoFundo.nativeElement.clientWidth;
	}

	private aplicarTamanhoDaFonte() {
		this.planoFundo.nativeElement.style.fontSize =
			this.fonteSize * this.obterLarguraQuadroConteudo() + "px";
	}
}
