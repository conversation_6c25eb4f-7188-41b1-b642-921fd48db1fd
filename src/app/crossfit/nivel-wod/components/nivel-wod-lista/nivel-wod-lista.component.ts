import { Component, OnInit, TemplateRef, ViewChild } from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { ModalService } from "@base-core/modal/modal.service";
import { TreinoApiNivelWodService, NivelWod } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { NivelWodEditModalComponent } from "../nivel-wod-edit/nivel-wod-edit-modal.component";
import { ApiResponseList } from "@base-core/rest/rest.model";

@Component({
	selector: "pacto-nivel-wod-lista",
	templateUrl: "./nivel-wod-lista.component.html",
	styleUrls: ["./nivel-wod-lista.component.scss"],
})
export class NivelWodListaComponent implements OnInit {
	@ViewChild("columnAcoes", { static: true }) columnAcoes: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome;
	@ViewChild("ColumnCategoria", { static: true }) ColumnCategoria;
	@ViewChild("novoTipoWod", { static: true }) novoTipoWod;
	@ViewChild("tableData", { static: false }) tableData;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;

	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@ViewChild("tipoWodLabels", { static: true })
	tipoWodLabels: TraducoesXinglingComponent;
	@ViewChild("tooltipLabels", { static: true })
	tooltipLabels: TraducoesXinglingComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;

	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;

	@ViewChild("reverterModalTitle", { static: true }) reverterModalTitle;
	@ViewChild("reverterModalBody", { static: true }) reverterModalBody;
	@ViewChild("reverterSuccess", { static: true }) reverterSuccess;
	@ViewChild("reverterError", { static: true }) reverterError;

	nivelWod: NivelWod = new (class implements NivelWod {
		categoria: string;
		id: string;
		nome: string;
	})();
	data: ApiResponseList<NivelWod> = {
		content: [],
	};
	ready = false;

	constructor(
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private rest: RestService,
		private modal: NgbModal,
		private nivelWodService: TreinoApiNivelWodService
	) {}

	loading = false;
	nomeTipoWod = "";
	itemToRemove;
	table: PactoDataGridConfig;
	tipoWodFormName: string;
	tipoWodFormDesc: string;

	ngOnInit() {
		this.configTable();
		this.ready = true;
	}

	configTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("nivel-wod"),
			logUrl: this.rest.buildFullUrl("log/nivel-wod"),
			quickSearch: true,
			buttons: {
				conteudo: this.buttonName,
				nome: "add",
				id: "adicionarNivelWod",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "categoria",
					titulo: this.ColumnCategoria,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "categoria",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => !this.verificaCategoria(row.categoria),
				},
				{
					nome: "removeFake",
					iconClass: "pct pct-trash-2",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.verificaCategoria(row.categoria),
					actionFn: null,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
		if ($event.iconName === "removeFake") {
		}
	}

	btnClickHandler() {
		const modalRef = this.modal.open(NivelWodEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue("");
		modalRef.componentInstance.categoria = "";
		modalRef.result.then((result) => {
			this.nivelWod = result;
			const createSuccess = this.createSuccess.nativeElement.innerHTML;
			this.nivelWodService.criarNivelWod(this.nivelWod).subscribe(
				(result) => {
					this.snotifyService.success(createSuccess);
					this.fetchData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta.error && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
		});
	}

	btnEditHandler(item) {
		const modalRef = this.modal.open(NivelWodEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue(item.nome);
		modalRef.componentInstance.categoria = item.categoria;
		localStorage.setItem(
			"sessionCategoria",
			this.verificaCategoria(item.categoria).toString()
		);
		modalRef.componentInstance.idSelecionado = item.id;
		modalRef.result
			.then((result) => {
				this.nivelWod = result;
				this.nivelWod.id = item.id;
				const editSuccess = this.editSuccess.nativeElement.innerHTML;
				this.nivelWodService
					.atualizarNivelWod(this.nivelWod)
					.subscribe((response) => {
						this.snotifyService.success(editSuccess);
						this.fetchData();
					});
			})
			.catch(() => {
				const validacao = localStorage.getItem("sessionCategoria");
				if (validacao === "true") {
					this.reverteHandler(item);
				}
			});
	}

	removeHandler(item) {
		this.nomeTipoWod = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					this.nivelWodService.removerNivelWod(item.id).subscribe(
						(result) => {
							this.snotifyService.success(removeSuccess);
							this.fetchData();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta.error && err.meta.message) {
								this.snotifyService.error(err.meta.message);
							}
						}
					);
				})
				.catch(() => {});
		});
	}

	reverteHandler(item) {
		this.nomeTipoWod = item.nome;
		setTimeout(() => {
			const modalTitle = this.reverterModalTitle.nativeElement.innerHTML;
			const modalBody = this.reverterModalBody.nativeElement.innerHTML;
			const removeSuccess = this.reverterSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				"Reverter"
			);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					this.nivelWodService.reverterNivelWod(item.id).subscribe(
						(result) => {
							this.snotifyService.success(removeSuccess);
							this.fetchData();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta.error && err.meta.message) {
								this.snotifyService.error(err.meta.message);
							}
						}
					);
				})
				.catch(() => {});
		});
	}

	searchHandler(value) {
		this.fetchData();
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	verificaCategoria(item) {
		return item.indexOf("Padrão") != -1;
	}
}
