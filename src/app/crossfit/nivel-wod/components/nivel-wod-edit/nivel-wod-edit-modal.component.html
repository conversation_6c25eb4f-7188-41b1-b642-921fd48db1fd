<div>
	<div class="modal-header">
		<h4 class="modal-title">
			<span *ngIf="verificaTitle()" i18n="@@crud-nivel-wod:modal:create:title">
				Adicionar Nível de WOD
			</span>
			<span *ngIf="!verificaTitle()" i18n="@@crud-nivel-wod:modal:edit:title">
				Editar Nível de WOD
			</span>
		</h4>
		<button
			(click)="dismiss()"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<pacto-input
			[control]="formGroup.get('nome')"
			[name]="'nome'"
			i18n-label="@@crud-nivel-wod:input:nome:label"
			i18n-mensagem="@@crud-nivel-wod:input:nome:mensagem"
			i18n-placeholder="@@crud-nivel-wod:input:nome:placeholder"
			label="Nome"
			mensagem="Fornecer um nome com pelo menos 3 digitos."
			placeholder="Nome do Nível WOD"></pacto-input>
	</div>
	<div class="modal-footer">
		<button
			(click)="dismiss()"
			*ngIf="!verificaReverter"
			class="btn btn-secondary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Cancelar
		</button>
		<button
			(click)="reverterNivelWod()"
			*ngIf="verificaReverter"
			class="btn btn-secondary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Reverter
		</button>
		<button
			(click)="close()"
			[disabled]="!acaoHabilitada"
			class="btn btn-primary"
			i18n="@@buttons:salvar"
			type="button">
			{{ nomeBotao }}
		</button>
	</div>
</div>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crossfit:nivel-wod:mensagem:campos-obrigatorio">
	Campos obrigatorio não preenchido!
</span>
