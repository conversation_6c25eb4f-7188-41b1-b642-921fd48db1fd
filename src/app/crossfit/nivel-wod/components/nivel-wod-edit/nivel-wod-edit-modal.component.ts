import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TreinoApiNivelWodService } from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { NivelWodListaComponent } from "../nivel-wod-lista/nivel-wod-lista.component";

@Component({
	selector: "pacto-tipo-benchmark-edit-modal",
	templateUrl: "./nivel-wod-edit-modal.component.html",
	styleUrls: ["./nivel-wod-edit-modal.component.scss"],
})
export class NivelWodEditModalComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;
	@Input() categoria: string;
	@Input() idSelecionado: string;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
	});
	nomeBotao: string;
	statusFormulario = false;
	verificado = false;

	constructor(
		private modalService: ModalService,
		private rest: RestService,
		private modal: NgbModal,
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.verificaTitle();
	}

	verificaTitle() {
		if (!this.verificado) {
			this.verificado = true;
			this.statusFormulario = this.formGroup.get("nome").value === "";
		}
		if (this.statusFormulario) {
			this.nomeBotao = "Adicionar";
		} else {
			this.nomeBotao = "Salvar";
		}
		return this.statusFormulario;
	}

	dismiss() {
		localStorage.setItem("sessionCategoria", "false");
		this.verificado = false;
		this.openModal.dismiss();
	}

	private validForm() {
		if (!this.formGroup.valid) {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
			return false;
		} else {
			return true;
		}
	}

	close() {
		this.formGroup.get("nome").markAsTouched();
		if (this.validForm()) {
			this.verificado = false;
			this.openModal.close(this.formGroup.getRawValue());
		} else {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
		}
	}

	reverterNivelWod() {
		this.openModal.close();
	}

	get Validators() {
		return Validators;
	}

	get acaoHabilitada() {
		return this.formGroup.get("nome").value;
	}

	get verificaReverter() {
		if (this.categoria === "Padrão") {
			return false;
		} else {
			return this.categoria.indexOf("Padrão") != -1;
		}
	}
}
