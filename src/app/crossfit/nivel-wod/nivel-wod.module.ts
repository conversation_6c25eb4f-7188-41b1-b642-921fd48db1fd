import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { NivelWodListaComponent } from "./components/nivel-wod-lista/nivel-wod-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { DragulaModule, DragulaService } from "ng2-dragula";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { NivelWodEditModalComponent } from "./components/nivel-wod-edit/nivel-wod-edit-modal.component";

const funcionalidade = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.CADASTRAR_TIPO_WOD,
	true
);

const routes: Routes = [
	{
		path: "",
		component: NivelWodListaComponent,
		data: {
			funcionalidade,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		DragulaModule,
		CommonModule,
	],
	entryComponents: [NivelWodEditModalComponent],
	declarations: [NivelWodListaComponent, NivelWodEditModalComponent],
	providers: [DragulaService],
})
export class NivelWodModule {}
