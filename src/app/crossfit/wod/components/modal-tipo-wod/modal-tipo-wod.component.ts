import {
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TreinoApiTipoWodService, TipoWod, OrdenamentosEnum } from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-tipo-wod",
	templateUrl: "./modal-tipo-wod.component.html",
	styleUrls: ["./modal-tipo-wod.component.scss"],
})
export class ModalTipoWodComponent implements OnInit {
	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@Output() aparelhoCriado: EventEmitter<void> = new EventEmitter<void>();

	formGroupTipoWOD: FormGroup = new FormGroup({
		nomeTipoWOD: new FormControl("", [
			Validators.required,
			Validators.minLength(3),
		]),
	});

	tipoWod: TipoWod = new (class implements TipoWod {
		id: string;
		nome: string;
		ordenamentosSelecionados: Array<OrdenamentosEnum> = [];
		profissionalEmPrimeiro: boolean;
		usarRanking: boolean;
	})();

	constructor(
		private openModal: NgbActiveModal,
		private tipoWodService: TreinoApiTipoWodService,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {}

	createHandler() {
		this.markAsTouched();
		if (this.formGroupTipoWOD.valid) {
			const createSuccess = this.notificacoesLabels.getLabel("createSuccess");
			this.tipoWodService
				.criarTipoWod(this.createEntity())
				.subscribe((result) => {
					this.snotifyService.success(createSuccess);
					this.aparelhoCriado.emit();
					this.openModal.close("updated");
				});
		} else {
			const validMensage = this.notificacoesLabels.getLabel("validMensage");
			this.snotifyService.error(validMensage);
		}
	}
	private markAsTouched() {
		this.formGroupTipoWOD.get("nomeTipoWOD").markAsTouched();
	}
	private createEntity() {
		this.tipoWod.nome = this.formGroupTipoWOD.get("nomeTipoWOD").value;
		this.tipoWod.usarRanking = false;
		this.tipoWod.ordenamentosSelecionados = [];

		return this.tipoWod;
	}
	dismiss() {
		this.openModal.dismiss();
	}
}
