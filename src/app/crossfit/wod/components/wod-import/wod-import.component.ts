import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { Benchmark, TreinoApiBenchmarkService } from "treino-api";

@Component({
	selector: "pacto-wod-import",
	templateUrl: "./wod-import.component.html",
	styleUrls: ["./wod-import.component.scss"],
})
export class WodImportComponent implements OnInit {
	benchmarks: Array<Benchmark> = [];
	dataAtual = new Date();

	constructor(
		private openModal: NgbActiveModal,
		private benchmarkService: TreinoApiBenchmarkService
	) {}

	formGroup: FormGroup = new FormGroup({
		origemWod: new FormControl(""),
		benchmark: new FormControl(""),
		diaCrossfit: new FormControl(""),
	});

	ngOnInit() {}

	dismiss() {
		this.openModal.dismiss();
	}

	close() {
		this.openModal.close(this.formGroup.value);
	}

	changeCarregarBenchmark() {
		if (this.formGroup.controls["origemWod"].value === "1") {
			this.benchmarkService.obterTodosBenchmarks().subscribe((dados) => {
				this.benchmarks = dados;
			});
		} else {
			this.formGroup.controls["diaCrossfit"].setValue("");
		}
		if (this.formGroup.controls["origemWod"].value === "2") {
			this.formGroup.controls["benchmark"].setValue("");
		}
	}
}
