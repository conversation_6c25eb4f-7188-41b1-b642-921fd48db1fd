@import "projects/ui/assets/ds3/colors.var";
.actions-row {
	width: 100px;
	text-align: center;

	i {
		cursor: pointer;
		padding-right: 10px;
	}
}
body {
	background-color: red;
}

.order {
	cursor: pointer;
}

.loading-state,
.empty-state {
	text-align: center;
	padding-top: 60px;
	height: 150px;
}

.div-btn-sync-wods {
	position: relative;
	padding: 0px 30px 15px;
	display: flex;
	justify-content: end;
}

::ng-deep pacto-relatorio .fa-pencil {
	margin-right: 15px;
}
::ng-deep pacto-relatorio {
	.pct.pct-edit {
		color: $actionDefaultAble04;
	}
	.pct-printer {
		color: $actionDefaultAble04;
	}
	.pct-trash-2 {
		color: $actionDefaultRisk04;
	}
}
.typography-title-4 {
	padding: 20px 0px 0 15px;
}
