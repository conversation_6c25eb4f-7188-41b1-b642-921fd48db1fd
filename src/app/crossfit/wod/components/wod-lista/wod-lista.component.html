<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		*ngIf="wodFormName == 'Wods'"
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'WOD'
		}"></pacto-breadcrumbs>
	<pacto-breadcrumbs
		*ngIf="wodFormName == 'Objetivos'"
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'Objetivo'
		}"></pacto-breadcrumbs>

	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="editHandler($event)"
			*ngIf="table"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="wod"
			[actionTitulo]="wodLabels.getLabel('titleActions')"></pacto-relatorio>
		<div *ngIf="showBtnSyncWodRede" class="div-btn-sync-wods">
			<pacto-cat-button
				(click)="importarWodsFranqueadora()"
				[icon]="'pct pct-refresh-cw'"
				[id]="'id-importar-wods-franqueadora'"
				[label]="'Importar wods da franqueadora'"></pacto-cat-button>
		</div>
	</div>
</pacto-cat-layout-v2>

<!--title table -->
<ng-template #titulo>
	<span i18n="@@crud-wod:title">WOD</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-wod:description">Lista de WODs criados</span>
</ng-template>
<!--End title table-->
<!--Actions buttons-->
<ng-template #addWod>
	<span i18n="@@buttons:adicionar">Adicionar WOD</span>
</ng-template>
<!--End actions buttons-->
<!--Columns name-->
<ng-template #columnNome>
	<span i18n="@@crossfit:crud-wods:table:nome">Nome do WOD</span>
</ng-template>
<ng-template #columnDia>
	<span i18n="@@crossfit:crud-wods:table:dia">Data da criação</span>
</ng-template>
<ng-template #columnUnidade>
	<span i18n="@@crossfit:crud-wods:table:unidade">Unidade</span>
</ng-template>

<!--End columns name-->
<!--filter result-->
<ng-template #celulaNome let-item="item">
	{{ item.nome }}
	{{
		item.chaveLancouRede && item.chaveLancouRede !== chave
			? "(compartilhado por " + item.usuarioLancouRede + ")"
			: ""
	}}
</ng-template>

<ng-template #celulaDia let-item="item">
	{{ item.dia | date : "shortDate" }}
</ng-template>

<ng-template #celulaUnidade let-item="item">
	{{ item.nomeEmpresa }}
</ng-template>

<pacto-traducoes-xingling #notificacoesLabels>
	<span xingling="removeModalTitleObjetivo">Remover o objetivo?</span>
	<span xingling="removeModalTitleWod">Remover o WOD?</span>
	<span xingling="removeModalBodyObjetivo">
		Deseja remover o objetivo {{ nomeWod }}?
	</span>
	<span xingling="removeModalBodyWod">Deseja remover o WOD {{ nomeWod }}?</span>
	<span xingling="removeSuccessObjetivo">Objetivo removido com sucesso.</span>
	<span xingling="removeSuccessWod">WOD removido com sucesso.</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #wodLabels>
	<span xingling="wodLabelName">Wod</span>
	<span xingling="wodLabelDesc">Gerencie os WODs cadastrados</span>
	<span xingling="objetivoLabelName">Objetivos</span>
	<span xingling="objetivoLabelDesc">Gerencie os objetivos</span>
	<span xingling="titleActions">Ações</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #tooltipLabels>
	<span xingling="tooltipEditar">Editar</span>
	<span xingling="tooltipRemover">Remover</span>
</pacto-traducoes-xingling>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
