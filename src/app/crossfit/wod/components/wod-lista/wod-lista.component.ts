import { ChangeDete<PERSON><PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";

import { SnotifyService } from "ng-snotify";

import { ModalService } from "@base-core/modal/modal.service";

import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { GridFilterType } from "../../../../cobranca/components/relatorio-cobranca/data-grid-filter.model";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { EmpresaFinanceiro } from "canal-cliente/app/shared/empresa-financeiro.model";
import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	PerfilAcessoFuncionalidadeNome,
	TreinoApiEmpresaService,
	TreinoApiWodService,
	Wod,
	WodBase,
} from "treino-api";

@Component({
	selector: "pacto-wod-lista",
	templateUrl: "./wod-lista.component.html",
	styleUrls: ["./wod-lista.component.scss"],
})
export class WodListaComponent implements OnInit {
	@ViewChild("addWod", { static: true }) addWod;
	@ViewChild("columnNome", { static: true }) columnNome;
	@ViewChild("columnDia", { static: true }) columnDia;
	@ViewChild("columnUnidade", { static: true }) columnUnidade;
	@ViewChild("celulaDia", { static: true }) celulaDia;
	@ViewChild("celulaNome", { static: true }) celulaNome;
	@ViewChild("celulaUnidade", { static: true }) celulaUnidade;
	@ViewChild("tableData", { static: false }) tableData;

	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@ViewChild("wodLabels", { static: true })
	wodLabels: TraducoesXinglingComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("tooltipImprimir", { static: true }) tooltipImprimir;
	@ViewChild("traducaoColunas", { static: true })
	traducaoColunas: TraducoesXinglingComponent;
	@ViewChild("unidadeLabel", { static: true }) unidadeLabel;

	showBtnSyncWodRede = false;
	showFiltroUnidade: boolean = true;

	constructor(
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private router: Router,
		private wodService: TreinoApiWodService,
		private modalService: ModalService,
		private configurationService: TreinoConfigCacheService,
		private sessionService: SessionService,
		private empresaService: TreinoApiEmpresaService
	) {}

	wodFormName: string;
	wodFormDesc: string;
	nomeWod: string;
	nomeUnidade: string;
	selectCrossfitOption: boolean;
	table: PactoDataGridConfig;
	empresas: any[] = [];
	empresaId: number = parseInt(this.sessionService.empresaId, 10);
	filterConfig: any;
	ready = false;
	empresasList;
	podeVisualizarWOD;
	wod: Wod;

	ngOnInit() {
		this.traducaoColunas =
			this.traducaoColunas || new TraducoesXinglingComponent();
		this.configTable();
		this.isFranqueado();
		this.verificarPermissaoVisualizarWOD();
		this.configFilters();
		this.getUnidades().subscribe(() => {
			this.configFilters();
			this.ready = true;
			this.fetchData();
		});
	}

	btnClickHandler() {
		this.router.navigate(["cross", "cadastros", "wods", "adicionar"]);
	}

	editHandler(item) {
		this.router.navigate(["cross", "cadastros", "wods", item.id]);
	}

	get chave() {
		return this.sessionService.chave;
	}

	private configTable(data: any[] = []) {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;

		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(`wods`),
			quickSearch: true,
			logUrl: this.rest.buildFullUrl("log/wods"),
			buttons: {
				conteudo: this.addWod,
				nome: "add",
				id: "btn-add-wod",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
					celula: this.celulaNome,
				},
				{
					nome: "dia",
					titulo: this.columnDia,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.celulaDia,
					campo: "dia",
				},
				{
					nome: "unidade",
					titulo: this.columnUnidade,
					mostrarTitulo: true,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.celulaUnidade,
					campo: "nomeEmpresa",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "pct pct-edit",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "pct pct-trash-2",
					tooltipText: tooltipRemover,
				},
			],
		});
		(this.table as any).data = data;
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.editHandler($event.row);
		} else if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	removeHandler(item) {
		if (item.chaveLancouRede) {
			this.snotifyService.warning(
				"Este wod foi compartilhado pela rede e não pode ser removido."
			);
			return;
		}
		this.nomeWod = item.nome;
		setTimeout(() => {
			const modalTitle = this.selectCrossfitOption
				? this.notificacoesLabels.getLabel("removeModalTitleWod")
				: this.notificacoesLabels.getLabel("removeModalTitleWod");
			const modalBody = this.selectCrossfitOption
				? this.notificacoesLabels.getLabel("removeModalBodyWod")
				: this.notificacoesLabels.getLabel("removeModalBodyWod");
			const removeSuccess = this.selectCrossfitOption
				? this.notificacoesLabels.getLabel("removeSuccessWod")
				: this.notificacoesLabels.getLabel("removeSuccessWod");
			const handler = this.modalService.confirm(modalTitle, modalBody);
			handler.result
				.then(() => {
					this.wodService.removerWod(item.id).subscribe(
						(result) => {
							this.snotifyService.success(removeSuccess);
							this.tableData.reloadData();
						},
						(responseError) => {
							this.snotifyService.error(responseError.error.meta.message);
						}
					);
				})
				.catch(() => {});
		});
	}

	private updateHeader() {
		this.selectCrossfitOption =
			this.configurationService.configuracoesGerais.troca_nomenclatura_crossfit.toString() ===
			"true";
		if (this.selectCrossfitOption) {
			this.wodFormName = this.wodLabels.getLabel("wodLabelName");
			this.wodFormDesc = this.wodLabels.getLabel("wodLabelDesc");
		} else {
			this.wodFormName = this.wodLabels.getLabel("objetivoLabelName");
			this.wodFormDesc = this.wodLabels.getLabel("objetivoLabelDesc");
		}
	}

	isFranqueado() {
		this.showBtnSyncWodRede =
			this.sessionService.configsTreinoRede !== undefined &&
			this.sessionService.configsTreinoRede.chaveFranqueadora !== undefined &&
			this.sessionService.configsTreinoRede.chaveFranqueadora !== "" &&
			!this.sessionService.configsTreinoRede.empresaLogadoIsFranqueadora;
		this.cd.detectChanges();
	}

	importarWodsFranqueadora() {
		this.wodService.importarWodFranqueadora().subscribe({
			error: (error) => {
				this.snotifyService.error(error.error.meta.message);
			},
			next: (dados) => {
				this.tableData.reloadData();
				this.snotifyService.success(dados);
			},
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dia",
					label: this.columnDia,
					type: GridFilterType.DATE_POINT,
				},
				{
					name: "unidade",
					label: this.columnUnidade,
					type: GridFilterType.MANY,
				},
			],
		};
		if (this.podeVisualizarWOD) {
			this.filterConfig = {
				filters: this.addFilter(),
			};
		}
	}

	private addFilter() {
		const result = [];
		result.push({
			name: "unidade",
			label: this.columnUnidade,
			type: GridFilterType.MANY,
			options: this.empresasList,
		});
		return result;
	}

	private getUnidades(): Observable<boolean> {
		return this.empresaService.obterUnidadesAtivas().pipe(
			map((data: EmpresaFinanceiro[]) => {
				data
					.sort((a, b) => (a.nome > b.nome ? 1 : -1))
					.forEach((empresa: EmpresaFinanceiro) => {
						(empresa as any).value = empresa.id;
						(empresa as any).label = empresa.nome
							? empresa.nome.toUpperCase()
							: empresa.nomeFantasia.toUpperCase();
					});
				this.empresasList = data;
				return true;
			})
		);
	}

	private fetchData() {
		this.wodService.obterListaWod(this.empresaId).subscribe(
			(response: ApiResponseList<WodBase>) => {
				const data = response.content || [];
				const unidadesAtivasIds = this.empresasList.map((emp) => emp.value);
				const filteredData = data.filter((wod) =>
					unidadesAtivasIds.includes(wod.empresa)
				);

				const formattedData = filteredData.map((item) => {
					let empresa;
					if (item.empresa !== undefined && item.empresa !== null) {
						empresa = this.getUnidadesByChave(item.empresa.toString());
					} else {
						empresa = undefined;
					}
					const unidade = empresa ? empresa.label : "N/A";
					return {
						...item,
						unidade: unidade,
					};
				});
				this.configTable(formattedData);
				this.cd.markForCheck();
			},
			(error) => {
				console.error("Erro ao obter lista de WODs:", error);
			}
		);
	}

	private getUnidadesByChave(chave: string): any {
		return this.empresasList.find(
			(empresa) => empresa.value.toString() === chave
		);
	}

	private verificarPermissaoVisualizarWOD() {
		const temPermissao = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.VISUALIZAR_WOD_OUTRAS_UNIDADES
		);
		this.podeVisualizarWOD = !!temPermissao;
	}
}
