import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import {
	ListaInsertSelectFilterComponent,
	ListInsertCreateComponent,
} from "old-ui-kit";
import {
	Aparelho,
	AparelhoEdit,
	AtividadeBase,
	TreinoApiAparelhoService,
	TreinoApiAtividadeService,
} from "treino-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-aparelhos-relacionados",
	templateUrl: "./modal-aparelhos-relacionados.component.html",
	styleUrls: ["./modal-aparelhos-relacionados.component.scss"],
})
export class ModalAparelhosRelacionadosComponent implements OnInit {
	@ViewChild("tableAddAjustesDisponiveis", { static: false })
	private readonly tableAddAjustesDisponiveis: RelatorioComponent;
	@ViewChild("columnAjuste", { static: true })
	private readonly columnAjuste: TemplateRef<Element>;
	@ViewChild("criarEditSuccess", { static: true }) criarEditSuccess;
	@Input() operation: string = "create";
	@ViewChild("atividadesComp", { static: false })
	atividadesComp: ListaInsertSelectFilterComponent;
	@ViewChild("ajustesComp", { static: false })
	ajustesComp: ListInsertCreateComponent;
	@ViewChild("messageError", { static: true }) messageError;
	tableAjustesDisponiveis: PactoDataGridConfig;

	formGroupAparelhos: FormGroup = new FormGroup({
		nomeAparelho: new FormControl("", [
			Validators.required,
			Validators.minLength(3),
		]),
		atividadesRelacionadas: new FormControl(""),
	});

	addAjustesDisponiveis: FormControl = new FormControl();
	data: Array<any> = [];
	atividades: Array<AtividadeBase> = [];
	entity: Aparelho;

	constructor(
		private activeModal: NgbActiveModal,
		private atividadeService: TreinoApiAtividadeService,
		private route: ActivatedRoute,
		private entityService: TreinoApiAparelhoService,
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.loadEntities();
		this.initTableAjuste();
		this.cd.detectChanges();
	}

	get crossfitModule() {
		return true;
	}
	private loadEntities() {
		this.atividadeService
			.obterTodasAtividades(this.crossfitModule ? "true" : "false")
			.subscribe((dados) => {
				this.atividades = dados;
				this.cd.markForCheck();
			});
	}

	dismiss() {
		this.activeModal.dismiss();
	}
	addItem() {
		const valorAjuste = { ajuste: this.addAjustesDisponiveis.value };
		this.data.push(valorAjuste);
		this.tableAddAjustesDisponiveis.reloadData();
		this.addAjustesDisponiveis.reset();
	}

	private initTableAjuste(): void {
		this.tableAjustesDisponiveis = new PactoDataGridConfig({
			quickSearch: false,
			pagination: false,
			initialFilters: [],
			dataAdapterFn: () => ({ content: this.data }),
			columns: [
				{
					nome: "ajuste",
					titulo: "Ajuste",
					width: "80%",
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: "Remover",
				},
			],
		});
	}
	private getFormValues(): AparelhoEdit {
		const atividadesRelacionadas = this.formGroupAparelhos.get(
			"atividadesRelacionadas"
		).value;
		const out: AparelhoEdit = {
			nome: this.formGroupAparelhos.get("nomeAparelho").value,
			atividadeIds:
				atividadesRelacionadas && atividadesRelacionadas.length > 0
					? atividadesRelacionadas
					: [],
			ajusteIds: [],
			novosAjustes: this.data.map((item) => item.ajuste),
			crossfit: this.crossfitModule,
			sigla: "",
			icone: "",
			usarEmReservaEquipamentos: false,
			sensorSelfloops: null,
		};
		return out;
	}
	submitHandler() {
		if (this.formGroupAparelhos.valid && !this.entity) {
			const message = this.criarEditSuccess.nativeElement.innerHTML;
			const values = this.getFormValues();
			let path;
			if (this.crossfitModule) {
				path = "cross";
			} else {
				path = "treino";
			}
			this.entityService.criarAparelho(values).subscribe(() => {
				this.snotifyService.success(message);
				this.activeModal.close("updated");
			});
		} else {
			const messageError = this.messageError.nativeElement.innerHTML;
			this.snotifyService.error(messageError);
			this.formGroupAparelhos.get("nomeAparelho").markAsTouched();
		}
	}

	private createHandler() {}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			const index = this.data.indexOf($event.row);

			if (index > -1) {
				this.data.splice(index, 1);
			}
			this.tableAddAjustesDisponiveis.reloadData();
		}
	}
}
