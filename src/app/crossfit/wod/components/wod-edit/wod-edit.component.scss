@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

.textarea-component {
	position: relative;
}

.link-importacao {
	position: absolute;
	right: 14px;
	top: 0px;
	font-weight: 600;
	cursor: pointer;
	font-size: 12px;

	&.module-crossfit {
		color: primaryColor(pacto, base);
	}
}

.icon-importacao {
	padding-right: 5px;
}

.actions {
	display: flex;
	margin-top: 15px;
	flex-direction: row-reverse;

	button {
		margin-left: 10px;
	}
}

.observacao-topo {
	font-weight: 600;
	color: primaryColor(pacto, base);
}

::ng-deep pacto-textarea {
	.has-danger {
		textarea {
			border: 1px solid $hellboyPri;
			outline: none;
		}

		small {
			color: $hellboyPri;
		}
	}
}

::ng-deep pacto-select {
	.has-danger {
		select {
			border: 1px solid $hellboyPri;
			outline: none;
		}

		small {
			color: $hellboyPri;
		}
	}
}

::ng-deep pacto-input {
	.has-danger {
		small {
			color: $hellboyPri;
		}
	}
}

.pct-toastr-inline {
	margin-bottom: 15px;
}

.toast-container {
	position: unset !important;
}

.header-row {
	display: flex;
	justify-content: space-between;
	padding-bottom: 10px;
}

.images-wrapper {
	margin-top: 20px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	border-top: 1px solid #d1d1d1;
	padding-top: 15px;

	.imagem {
		margin: 5px;
		width: 150px;

		.imagem-wrapper {
			max-width: 100%;
			max-height: 100%;
			object-fit: contain;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f5f5f5;
			width: 150px;
			height: 150px;
		}

		.control-footer {
			display: flex;

			.name {
				width: calc(100% - 20px);
				padding-top: 5px;
				padding-right: 5px;
				word-break: break-all;
				overflow: hidden;
			}

			.icon {
				padding-top: 5px;
				flex-grow: 1;
				cursor: pointer;
			}
		}

		img {
			max-width: 150px;
			max-height: 150px;
		}
	}
}

::ng-deep pacto-title-card {
	.card-wrapper {
		box-shadow: 0px 2px 2px 0px hsla(210, 4%, 90%, 1) !important;
		border-radius: 8px;
	}
}
.line-form {
	margin-bottom: 25px;
}
.link-form {
	color: $actionDefaultAble04;
	* {
		font-family: $fontPoppins;
		font-size: 12px;
		font-weight: 600;
	}
}
.buttons {
	padding: 0 15px;
}
::ng-deep .lg {
	.modal-dialog {
		max-width: 1000px !important;
	}
}
::ng-deep .cdk-overlay-container {
	z-index: 9999 !important;
}
