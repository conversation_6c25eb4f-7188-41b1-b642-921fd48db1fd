<ng-template #cardTitle>
	<h4
		*ngIf="entity"
		i18n="@@crossfit:crud-wods:criar-wod:title"
		class="typography-title-4">
		Criar {{ wodFormName }}
	</h4>
	<h4
		*ngIf="!entity"
		i18n="@@crossfit:crud-wods:editar-wod:title"
		class="typography-title-4">
		Editar {{ wodFormName }}
	</h4>
</ng-template>

<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: wodFormName,
			menuLink: ['cross', 'cadastros', 'wods']
		}"></pacto-breadcrumbs>

	<pacto-title-card [title]="cardTitle">
		<div class="row" *ngIf="temResultado">
			<div class="col-md-12">
				<div class="pct-toastr-inline toast-container">
					<div class="pct-toastr-warning cor-plano02 pct-toastr">
						<div class="pct-toastr-content-container">
							<div class="pct-toastr-icon"></div>
							<div class="pct-toastr-content">
								<div class="pct-toastr-title-section"></div>
								<div
									class="toast-message"
									i18n="@@crossfit:crud-wods:mensagem-observacao">
									Já existe resultado lançado para esse WOD. Não é possível
									alterar o Dia e o Tipo de WOD
								</div>
							</div>
						</div>
						<div class="pct-toastr-close-section">
							<i class="pct pct-x"></i>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row line-form">
			<div class="col-md-4">
				<ds3-form-field id="'nome-wod-input'" name="'nome'">
					<ds3-field-label>{{ wodFormLabelName }}</ds3-field-label>
					<input
						type="text"
						ds3Input
						[formControl]="formGroup.controls['nome']" />
					<ds3-helper-message
						*ngIf="
							formGroup.controls['nome'].touched &&
							formGroup.controls['nome'].hasError('required')
						">
						Definir um nome com pelo menos 3 caracteres.
					</ds3-helper-message>
				</ds3-form-field>
			</div>
			<div class="col-md-4">
				<ds3-form-field>
					<ds3-field-label>Tipo de {{ wodFormName }}</ds3-field-label>
					<ds3-select
						[formControl]="formGroup.controls['tipoWod']"
						[options]="tiposWod"
						[nameKey]="'nome'"
						[valueKey]="'id'"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
			<div class="col-md-4">
				<ds3-form-field id="select-atividade">
					<ds3-field-label>Atividades Relacionadas</ds3-field-label>
					<ds3-select-multi
						[formControl]="formGroup.controls['atividade']"
						[nameKey]="'nome'"
						[valueKey]="'id'"
						[options]="atividades"
						ds3Input></ds3-select-multi>
				</ds3-form-field>
				<div class="link-importacao link-form">
					<i class="pct pct-plus sort-icon icon-importacao"></i>
					<span (click)="modalAtividade()">Nova atividade</span>
				</div>
			</div>
		</div>

		<div class="row line-form">
			<div class="col-md-4">
				<ds3-form-field id="'select-aparelho'">
					<ds3-field-label i18n-title="@@crossfit:crud-wods:aparelhos:title">
						Aparelhos Relacionados
					</ds3-field-label>
					<ds3-select-multi
						[formControl]="formGroup.controls['aparelho']"
						[nameKey]="'nome'"
						[valueKey]="'id'"
						[options]="aparelhos"
						ds3Input></ds3-select-multi>
				</ds3-form-field>
				<div class="link-importacao link-form">
					<i class="pct pct-plus sort-icon icon-importacao"></i>
					<span (click)="modalAparelho()">Novo aparelho</span>
				</div>
			</div>
			<div class="col-md-4">
				<ds3-form-field id="selectData" name="dia">
					<ds3-field-label
						i18n-label="@@crossfit:crud-wods:datepicker:dia:label">
						Dia
					</ds3-field-label>
					<ds3-input-date
						ds3Input
						[control]="formGroup.controls['dia']"></ds3-input-date>
				</ds3-form-field>
			</div>
			<div class="col-md-4">
				<ds3-form-field id="'nivel-wod-select'" name="'nivelWOD'">
					<ds3-field-label
						i18n-label="@@crossfit:crud-wods:select:nivelWod:label">
						{{ wodNivel }}
					</ds3-field-label>
					<ds3-select
						[formControl]="formGroup.controls['nivelWod']"
						[options]="niveisWod"
						[nameKey]="'nome'"
						[valueKey]="'id'"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>
		</div>
		<div class="row line-form">
			<div class="col-lg-4">
				<ds3-form-field id="'text-Along-Mobil'" name="'alongamentoMobilidade'">
					<ds3-field-label
						i18n-label="
							@@crossfit:crud-wods:textarea:alongamentoMobilidade:label">
						Stretching (Alongamento)
					</ds3-field-label>
					<textarea
						autofocus
						ds3Input
						[formControl]="formGroup.controls['alongamentoMobilidade']"
						placeholder="Descreva aqui os alongamentos ou as mobilidades..."
						rows="5"></textarea>
				</ds3-form-field>
			</div>
			<div class="col-lg-4">
				<ds3-form-field id="'text-Aquecimento'" name="'aquecimento'">
					<ds3-field-label>WarmUp (Aquecimento)</ds3-field-label>
					<textarea
						autofocus
						ds3Input
						i18n-placeholder="@@wod-edit:descreva-aquecimentos"
						[formControl]="formGroup.controls['aquecimento']"
						placeholder="Descreva aqui os aquecimentos..."
						rows="5"></textarea>
				</ds3-form-field>
			</div>
			<div class="col-lg-4">
				<ds3-form-field id="'textParteTecSkill'" name="'parteTecnicaSkill'">
					<ds3-field-label
						i18n-label="@@crossfit:crud-wods:textarea:parteTecnicaSkill:label">
						Skill (Técnica)
					</ds3-field-label>
					<textarea
						autofocus
						ds3Input
						i18n-placeholder="
							@@crossfit:crud-wods:textarea:parteTecnicaSkill:placeholder"
						[formControl]="formGroup.controls['parteTecnicaSkill']"
						placeholder="Descreva aqui a parte técnica ou skill..."
						rows="5"></textarea>
				</ds3-form-field>
			</div>
		</div>
		<div class="row line-form">
			<div class="col-lg-4">
				<ds3-form-field id="'textComplexEmom'" name="'complexEmom'">
					<ds3-field-label
						i18n-label="@@crossfit:crud-wods:textarea:complexEmom:label">
						Complex
					</ds3-field-label>
					<textarea
						autofocus
						ds3Input
						i18n-placeholder="
							@@crossfit:crud-wods:textarea:complexEmom:placeholder"
						[formControl]="formGroup.controls['complexEmom']"
						placeholder="Descreva aqui os complex..."
						rows="5"></textarea>
				</ds3-form-field>
			</div>
			<div class="col-lg-4">
				<div class="textarea-component">
					<ds3-form-field id="'textWod'" name="'wod'">
						<ds3-field-label
							i18n-label="@@crossfit:crud-wods:textarea:wod:label">
							WOD (Treino do dia)
						</ds3-field-label>
						<textarea
							autofocus
							ds3Input
							i18n-placeholder="@@crossfit:crud-wods:textarea:wod:placeholder"
							mensagem="Wod é obrigatório."
							[formControl]="formGroup.controls['wod']"
							placeholder="Descreva aqui os wod..."
							rows="5"
							i18n-mensagem="
								@@crossfit:crud-wods:textarea:wod:mensagem"></textarea>
					</ds3-form-field>
					<div class="link-importacao link-form" *ngIf="!compartilhado">
						<i class="pct pct-download sort-icon icon-importacao"></i>
						<span
							(click)="importWodsHandler()"
							i18n="@@crossfit:crud-wods:importar-wod">
							Importar Wod
						</span>
					</div>
				</div>
			</div>
		</div>
		<div class="row line-form"></div>

		<div class="row">
			<div class="col-md-6" *ngIf="compartilhado">
				Criado por {{ wod.usuarioLancouRede }} na unidade
				{{ wod.unidadeLancouRede }}
			</div>
			<div class="col-md-6" *ngIf="!compartilhado && wod?.idRede">
				Este wod está compartilhado na sua rede de empresas
			</div>
			<div
				class="col-md-6"
				*ngIf="!compartilhado && !wod?.idRede && podeCompartilhar">
				<pacto-cat-checkbox
					[title]="'Compartilhar este WOD com as outras unidades da sua rede'"
					[id]="'compartilhar-rede'"
					[label]="'Compartilhar o wod com a rede'"
					[control]="formGroup.get('compartilharRede')"></pacto-cat-checkbox>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12">
				<div class="form-group images-wrapper">
					<div class="header-row">
						<label class="control-label" i18n="@@crud-atividade:imagem:label">
							Imagem
						</label>
						<button
							(click)="adicionarImagemHandler()"
							*ngIf="!compartilhado"
							class="btn btn-sm btn-secondary"
							i18n="@@crud-atividade:imagem:button"
							id="btnAddImagem">
							Adicionar Imagem
						</button>
					</div>
					<div class="image-list">
						<div
							*ngFor="let imagem of images; let index = index"
							class="imagem">
							<div class="imagem-wrapper">
								<img [src]="imagem.uri" />
							</div>
							<div class="control-footer">
								<div class="name">
									{{ imagem.nome }}
								</div>
								<div *ngIf="!compartilhado" class="icon">
									<i
										(click)="removeImageHandle(index)"
										class="fa fa-trash-o"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<br />
		<div class="row justify-content-end buttons" id="btn">
			<button
				style="margin-right: 15px"
				ds3-outlined-button
				(click)="cancelHandler()"
				i18n="@@buttons:cancelar"
				id="btnCancelar">
				Cancelar
			</button>
			<button
				ds3-flat-button
				(click)="submitHandler()"
				i18n="@@buttons:salvar"
				id="btnSalvar">
				Salvar
			</button>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<div [hidden]="true" #conflito id="help-message-duplicated-entity-name">
	Já existe um cadastro com esse mesmo nome
</div>

<pacto-traducoes-xingling #notificacoesLabels>
	<span xingling="createWod">WOD criado com sucesso.</span>
	<span xingling="editWod">Tipo WOD editado com sucesso.</span>
	<span xingling="camposObrigatorios">Campos obrigatórios não preenchido.</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #wodLabels>
	<span xingling="wodLabelName">WOD</span>
	<span xingling="wodLabelDesc">Gerencie os WODs cadastrados</span>
	<span xingling="objetivoLabelName">Objetivo</span>
	<span xingling="objetivoCampoLabelName">Nome do Objetivo</span>
	<span xingling="wodCampoLabelName">Nome do WOD</span>
	<span xingling="objetivoLabelDesc">Gerencie os objetivos</span>
	<span xingling="nomeObjetivoLabel">Tipo do objetivo</span>
	<span xingling="nomeWodLabel">Tipo de WOD</span>
	<span xingling="mensagemObjetivoLabel">Objetivo é obrigatório</span>
	<span xingling="mensagemWodLabel">Tipo WOD é obrigatório</span>
	<span xingling="nivelObjetivoLabel">Nível de Objetivo</span>
	<span xingling="nivelWodLabel">Nível de WOD</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #tooltipLabels>
	<span xingling="tooltipEditar">Editar</span>
	<span xingling="tooltipRemover">Remover</span>
</pacto-traducoes-xingling>
