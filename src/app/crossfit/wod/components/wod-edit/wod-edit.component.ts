import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	SnotifyButton,
	SnotifyPosition,
	SnotifyService,
	SnotifyToastConfig,
} from "ng-snotify";

import {
	ListaInsertSelectFilterComponent,
	SeletorImagemComponent,
} from "old-ui-kit";

import { SafeHtml } from "@angular/platform-browser";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyAnimate } from "ng-snotify/snotify/interfaces/SnotifyAnimate.interface";
import { SnotifyType } from "ng-snotify/snotify/types/snotify.type";
import { DataUrl, NgxImageCompressService } from "ngx-image-compress";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import {
	AparelhoBase,
	AtividadeBase,
	NivelWod,
	TipoWodBase,
	TreinoApiAparelhoService,
	TreinoApiAtividadeService,
	TreinoApiNivelWodService,
	TreinoApiTipoWodService,
	TreinoApiWodService,
	Wod,
} from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { RecursoSistema } from "../../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { SeletorImagemAvancadoComponent } from "../../../../treino/atividade/components/seletor-imagem-avancado/seletor-imagem-avancado.component";
import { WodService } from "../../wod.service";
import { ModalTipoWodComponent } from "../modal-tipo-wod/modal-tipo-wod.component";
import { WodImportComponent } from "../wod-import/wod-import.component";
import { ModalAtividadesRelacionadasComponent } from "../modal-atividades-relacionadas/modal-atividades-relacionadas.component";
import { ModalAparelhosRelacionadosComponent } from "../modal-aparelhos-relacionados/modal-aparelhos-relacionados.component";
declare var moment;

interface ImagemConfig {
	id?: string;
	uri: string;
	nome?: string;
	data?: any;
	extensao?: string;
}

@Component({
	selector: "pacto-wod-edit",
	templateUrl: "./wod-edit.component.html",
	styleUrls: ["./wod-edit.component.scss"],
})
export class WodEditComponent implements OnInit, AfterViewInit {
	@ViewChild("atividadesComp", { static: true })
	atividadesComp: ListaInsertSelectFilterComponent;
	@ViewChild("aparelhosComp", { static: true })
	aparelhosComp: ListaInsertSelectFilterComponent;
	@ViewChild("seletorImagem", { static: true })
	seletorImagem: SeletorImagemComponent;
	@ViewChild("notificacoesLabels", { static: true })
	notificacoesLabels: TraducoesXinglingComponent;
	@ViewChild("wodLabels", { static: true })
	wodLabels: TraducoesXinglingComponent;
	@ViewChild("tooltipLabels", { static: true })
	tooltipLabels: TraducoesXinglingComponent;
	@ViewChild("conflito", { static: true }) conflito;

	operation: string;
	wod: Wod;
	selectCrossfitOption: boolean;

	tiposWod: Array<TipoWodBase>;
	niveisWod: Array<NivelWod>;

	aparelhos: Array<AparelhoBase> = [];
	podeCompartilhar = false;
	atividades: Array<AtividadeBase> = [];
	urlImagem: string;
	imagemData: string;
	temResultado = false;
	compartilhado = false;
	mensagemWod: string;
	wodName: string;
	wodNivel: string;
	wodFormName: string;
	wodFormLabelName: string;
	wodFormDesc: string;
	images: Array<ImagemConfig> = [];
	loading = false;
	salvando: boolean = false;
	imgAulaDataUrl: DataUrl = "";
	manterFoto: boolean = true;
	filtroData: Number;

	entity = true;
	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		dia: new FormControl(new Date().getTime(), [Validators.required]),
		tipoWod: new FormControl(null, [Validators.required]),
		aquecimento: new FormControl(""),
		alongamentoMobilidade: new FormControl(""),
		parteTecnicaSkill: new FormControl(""),
		complexEmom: new FormControl(""),
		wod: new FormControl(null, [Validators.required]),
		atividade: new FormControl(null),
		aparelho: new FormControl(null),
		imagemData: new FormControl(""),
		extensaoImagem: new FormControl(""),
		compartilharRede: new FormControl(false),
		nivelWod: new FormControl(null),
	});

	//optionsTipoWod: Array<any> = [];
	select = new FormControl();
	control = new FormControl();

	configSnotify: SnotifyToastConfig = new (class implements SnotifyToastConfig {
		animation: SnotifyAnimate;
		backdrop: number;
		bodyMaxLength: number;
		buttons: SnotifyButton[];
		closeOnClick: boolean;
		html: string | SafeHtml;
		icon: string;
		iconClass: string;
		pauseOnHover: boolean;
		placeholder: string;
		position: SnotifyPosition;
		showProgressBar: boolean;
		timeout: number;
		titleMaxLength: number;
		type: SnotifyType;
	})();

	constructor(
		private atividadeService: TreinoApiAtividadeService,
		private aparelhoService: TreinoApiAparelhoService,
		private cd: ChangeDetectorRef,
		private modal: NgbModal,
		private wodService: TreinoApiWodService,
		private wodServiceOld: WodService,
		private route: ActivatedRoute,
		private router: Router,
		private snotifyService: SnotifyService,
		private tipoWodService: TreinoApiTipoWodService,
		private nivelWodService: TreinoApiNivelWodService,
		private configurationService: TreinoConfigCacheService,
		private imageCompress: NgxImageCompressService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.getSelects();
		this.verificarCompartilhamento();
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}
			this.cd.detectChanges();
		});
	}

	ngAfterViewInit() {
		setTimeout(() => {
			if (this.atividadesComp && this.aparelhosComp) {
				this.updateHandler();
			}
			this.updateHeader();
			this.cd.detectChanges();
		});
	}

	private loadEntities(id) {
		if (id) {
			this.wodService.obterWod(id).subscribe((dados) => {
				this.wod = dados;
				this.temResultado = this.wod.temResultados;
				this.entity = false;
				if (
					this.wod.chaveLancouRede &&
					this.sessionService.chave !== this.wod.chaveLancouRede
				) {
					this.compartilhado = true;
					this.formGroup.disable();
				}

				if (this.wod) {
					const atividades = this.wod.atividades
						? this.wod.atividades.map((a) => a.id)
						: [];
					const aparelhos = this.wod.aparelhos
						? this.wod.aparelhos.map((a) => a.id)
						: [];

					this.formGroup.patchValue({
						nome: this.wod.nome,
						dia: new Date(this.wod.dia),
						tipoWod: this.wod.tipoWod.id,
						nivelWod: this.wod.nivelWod ? +this.wod.nivelWod : null,
						atividade: atividades,
						aparelho: aparelhos,
						wod: this.wod.wod,
						imagemData: null,
					});
					if (this.wod.urlImagem) {
						this.urlImagem = this.wod.urlImagem;
						this.images.push({
							uri: this.wod.urlImagem,
							extensao: this.wod.urlImagem.split(".").pop(),
						});
					}
				} else {
					console.error("Dados incompletos", dados);
				}
				this.loadForm();
				this.cd.detectChanges();
			});
		}
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.wod.nome);
		this.formGroup.get("tipoWod").setValue(this.wod.tipoWod.id);
		if (this.wod.nivelWod != null) {
			this.formGroup.get("nivelWod").setValue(Number(this.wod.nivelWod));
		}
		this.formGroup
			.get("alongamentoMobilidade")
			.setValue(this.wod.alongamentoMobilidade);
		this.formGroup.get("aquecimento").setValue(this.wod.aquecimento);
		this.formGroup
			.get("parteTecnicaSkill")
			.setValue(this.wod.parteTecnicaSkill);
		this.formGroup.get("complexEmom").setValue(this.wod.complexEmom);
		this.formGroup.get("wod").setValue(this.wod.wod);
		if (this.wod.atividades != null && this.atividadesComp != undefined) {
			this.atividadesComp.setSelectedItens(this.wod.atividades);
			this.formGroup
				.get("atividade")
				.setValue(this.wod.atividades.map((a) => a.id));
		}
		if (
			this.wod.aparelhos &&
			this.aparelhosComp != null &&
			this.aparelhosComp != undefined
		) {
			this.aparelhosComp.setSelectedItens(this.wod.aparelhos);
			this.formGroup
				.get("aparelho")
				.setValue(this.wod.aparelhos.map((a) => a.id));
		}
		this.formGroup.get("imagemData").setValue(this.wod.imagemData);
		this.formGroup.get("compartilharRede").setValue(this.wod.compartilharRede);
		if (this.wod.urlImagem) {
			setTimeout(() => {
				this.urlImagem = this.wod.urlImagem;
				this.imagemData = this.wod.urlImagem;
				this.cd.detectChanges();
			});
		}
	}

	submitHandler() {
		this.markAsTouched();
		if (!this.entity) {
			this.updateHandler();
		} else {
			this.createHandler();
		}
	}

	private createHandler() {
		if (this.formGroup.valid) {
			const formulario = this.formGroup.getRawValue();
			formulario.dia = this.convertToTimestamp(formulario.dia);
			this.images.forEach((img) => {
				formulario.imagemData = img.data;
				formulario.extensaoImagem = "." + img.extensao.toLowerCase();
			});
			this.wodService.criarWod(formulario).subscribe({
				error: (error) => {
					if (
						error.status !== 409 ||
						(error.status === 409 &&
							error.error.meta.message !== "registro_duplicado")
					) {
						this.snotifyService.error(
							"Algo inesperado ocorreu durante a operação"
						);
					}
				},
				next: (result) => {
					const createWod = this.notificacoesLabels.getLabel("createWod");
					this.snotifyService.success(createWod);
					this.sessionService.notificarRecursoEmpresa(
						RecursoSistema.CRIOU_WOD_NCR
					);
					this.cancelHandler();
				},
			});
		} else {
			const camposObrigatorios =
				this.notificacoesLabels.getLabel("camposObrigatorios");
			this.snotifyService.error(camposObrigatorios);
		}
	}

	private updateHandler() {
		if (this.formGroup.valid) {
			const formulario = this.formGroup.getRawValue();
			formulario.dia = this.convertToTimestamp(formulario.dia);
			const editWod = this.notificacoesLabels.getLabel("editWod");
			if (this.atividadesComp && this.aparelhosComp) {
				const formulario = this.formGroup.getRawValue();
				formulario.atividade = this.atividadesComp.getSelectedIds() || [];
				formulario.aparelho = this.aparelhosComp.getSelectedIds() || [];
			} else {
				this.images.forEach((img) => {
					if (img.data == undefined) {
						formulario.imagemData = img.uri;
						formulario.extensaoImagem = "." + img.extensao.toLowerCase();
					} else {
						formulario.imagemData = img.data;
						formulario.extensaoImagem = "." + img.extensao.toLowerCase();
					}
				});
				this.wodService
					.atualizarWod(this.wod.id, formulario)
					.subscribe((result) => {
						this.snotifyService.success(editWod);
						this.cancelHandler();
					});
			}
		} else {
			const camposObrigatorios =
				this.notificacoesLabels.getLabel("camposObrigatorios");
			this.snotifyService.error(camposObrigatorios);
		}
	}

	get Validators() {
		return Validators;
	}

	importWodsHandler() {
		const modalRef = this.modal.open(WodImportComponent);

		modalRef.result.then((result) => {
			if (result.benchmark) {
				this.formGroup.controls["wod"].setValue(result.benchmark.exercicios);
			} else {
				this.wodService.obterWodImportado(result.diaCrossfit).subscribe({
					error: (error) => {
						this.snotifyService.error(error.error.meta.message);
					},
					next: (dados) => {
						this.formGroup.controls["wod"].setValue(dados.content);
					},
				});
			}
		});
	}

	private verificarCompartilhamento() {
		this.wodServiceOld
			.podeCompartilhar(this.sessionService.chave)
			.subscribe((dados) => {
				this.podeCompartilhar = dados;
				this.cd.detectChanges();
			});
	}

	private getSelects() {
		this.atividadeService.obterTodasAtividades(true).subscribe(
			(dados) => {
				this.atividades = dados || [];
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar atividades:", error);
			}
		);

		this.aparelhoService.obterTodosAparelhos("true").subscribe(
			(dados) => {
				this.aparelhos = dados || [];
				this.cd.detectChanges();
			},
			(error) => {
				console.error("Erro ao carregar aparelhos:", error);
			}
		);

		this.tipoWodService.obterListaTipoWod().subscribe((dados) => {
			this.tiposWod = dados.content;
		});

		this.nivelWodService.obterListaNivelWod().subscribe((dados) => {
			this.niveisWod = dados.content;
			this.cd.detectChanges();
		});
	}

	cancelHandler() {
		this.router.navigate(["cross", "cadastros", "wods"], {
			queryParamsHandling: "merge",
		});
	}

	private markAsTouched() {
		this.formGroup.markAllAsTouched();

		Object.keys(this.formGroup.getRawValue()).forEach((controlName) => {
			this.formGroup.controls[controlName].updateValueAndValidity();
			this.formGroup.controls[controlName].markAsDirty();
		});
	}

	private updateHeader() {
		this.selectCrossfitOption =
			this.configurationService.configuracoesGerais.troca_nomenclatura_crossfit.toString() ===
			"true";
		if (this.selectCrossfitOption) {
			this.wodFormName = this.wodLabels.getLabel("objetivoLabelName");
			this.wodFormLabelName = this.wodLabels.getLabel("objetivoCampoLabelName");
			this.wodFormDesc = this.wodLabels.getLabel("objetivoLabelDesc");
			this.wodName = this.wodLabels.getLabel("nomeObjetivoLabel");
			this.wodNivel = this.wodLabels.getLabel("nivelObjetivoLabel");
			this.mensagemWod = this.wodLabels.getLabel("mensagemObjetivoLabel");
		} else {
			this.wodFormName = this.wodLabels.getLabel("wodLabelName");
			this.wodFormLabelName = this.wodLabels.getLabel("wodCampoLabelName");
			this.wodFormDesc = this.wodLabels.getLabel("wodLabelDesc");
			this.wodName = this.wodLabels.getLabel("nomeWodLabel");
			this.wodNivel = this.wodLabels.getLabel("nivelWodLabel");
			this.mensagemWod = this.wodLabels.getLabel("mensagemWodLabel");
		}
	}

	adicionarImagemHandler() {
		const tamanhoMaxImg = 1024 * 1024 * 1; // 1MB
		const modal = this.modal.open(SeletorImagemAvancadoComponent, {
			size: "lg",
		});
		modal.componentInstance.apresentarCatalogo = false;
		modal.result.then((image: ImagemConfig) => {
			this.imgAulaDataUrl = "";
			if (this.isImageGif(image)) {
				this.snotifyService.error(
					"Formato da imagem não suportado. Por favor, selecione uma imagem JPG ou PNG."
				);
			} else {
				if (this.isTamanhoImagemPermitido(image, tamanhoMaxImg)) {
					this.handleUploadImage(image);
				} else {
					this.obterDataUrlImagem(image)
						.then(() => {
							this.comprimirImagem(tamanhoMaxImg);
							this.handleUploadImage(image);
						})
						.catch((error) => {
							this.handleUploadImage(image);
						});
				}
			}
		});
	}

	obterDataUrlImagem(image: ImagemConfig): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			const file: File = image.data;
			const reader = new FileReader();
			reader.onload = () => {
				this.imgAulaDataUrl = reader.result as string;
				resolve();
			};
			reader.onerror = (error) => reject(error);
			if (file) {
				reader.readAsDataURL(file);
			}
		});
	}

	comprimirImagem(tamanhoMaxImg) {
		console.log(
			"Tamanho da imagem antes de ser comprimida: ",
			this.imgAulaDataUrl.length
		);
		// DOC: https://github.com/dfa1234/ngx-image-compress
		// Devido versão do angular, foi necessário utilizar outra versão mas com os mesmos comandos do link anterior:
		// https://www.npmjs.com/package/ngx-image-compress-legacy
		if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
			this.imageCompress
				.compressFile(this.imgAulaDataUrl, 1, 50, 50)
				.then((result: DataUrl) => {
					this.imgAulaDataUrl = result;
					if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
						this.comprimirImagem(tamanhoMaxImg);
					}
				});
		}
	}

	private isImageGif(image: ImagemConfig) {
		const regex: RegExp = /\.gif$/i;
		return regex.test(image.nome);
	}

	private isTamanhoImagemPermitido(image: ImagemConfig, tamanhoMax: number) {
		const file: File = image.data;
		if (file) {
			if (file.size > tamanhoMax) {
				return false;
			}
		}
		return true;
	}

	private handleUploadImage(image: ImagemConfig) {
		this.loading = true;
		const reader: FileReader = new FileReader();
		reader.onload = (event: any) => {
			if (this.imgAulaDataUrl && this.imgAulaDataUrl !== "") {
				console.log(
					"Tamanho da imagem após ser comprimida: ",
					this.imgAulaDataUrl.length
				);
				image.uri = this.imgAulaDataUrl;
				image.data = this.imgAulaDataUrl.split(",")[1];
				image.extensao = image.nome.split(".").pop();
			} else {
				image.uri = event.target.result;
				const resultString: string = reader.result as string;
				image.data = resultString.split(",")[1];
				image.extensao = image.nome.split(".").pop();
			}
			this.images.push(image);
			this.loading = false;
			this.cd.detectChanges();
		};
		reader.readAsDataURL(image.data);
	}

	removeImageHandle(index) {
		this.images.splice(index, 1);
		this.manterFoto = false;
	}
	modalTipoWod() {
		const dialogRef = this.modal.open(ModalTipoWodComponent, {
			windowClass: "sm",
		});
	}
	modalAtividade() {
		const dialogRef = this.modal.open(ModalAtividadesRelacionadasComponent, {
			windowClass: "lg",
		});

		dialogRef.result
			.then((result) => {
				if (result === "updated") {
					this.atividadeService
						.obterTodasAtividades(true)
						.subscribe((dados) => {
							this.atividades = dados;
							this.cd.detectChanges();
						});
				}
			})
			.catch(() => {});
	}

	modalAparelho() {
		const dialogRef = this.modal.open(ModalAparelhosRelacionadosComponent, {
			windowClass: "lg",
		});

		dialogRef.result
			.then((result) => {
				if (result === "updated") {
					this.aparelhoService
						.obterTodosAparelhos("true")
						.subscribe((dados) => {
							this.aparelhos = dados;
							this.cd.detectChanges();
						});
				}
			})
			.catch(() => {});
	}

	private convertToTimestamp(date: string): number | null {
		if (!date) {
			return null;
		}
		return new Date(date).getTime();
	}

	onDateChange(event: any) {
		const value = event.target ? event.target.value : event;
		const timestamp = this.convertToTimestamp(value);
		if (!isNaN(timestamp)) {
			this.formGroup.controls["dia"].setValue(timestamp);
		} else {
			console.error("Data inválida:", value);
		}
	}
}
