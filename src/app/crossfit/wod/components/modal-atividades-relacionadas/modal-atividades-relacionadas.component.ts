import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TreinoApiAtividadeCrossfitService } from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-atividades-relacionadas",
	templateUrl: "./modal-atividades-relacionadas.component.html",
	styleUrls: ["./modal-atividades-relacionadas.component.scss"],
})
export class ModalAtividadesRelacionadasComponent
	implements OnInit, AfterViewInit
{
	@Output() atividadeCriada: EventEmitter<void> = new EventEmitter<void>();
	@ViewChild("traducaoCategorias", { static: true })
	traducaoCategorias: TraducoesXinglingComponent;
	@ViewChild("traducaoUnidades", { static: true })
	traducaoUnidades: TraducoesXinglingComponent;
	@ViewChild("duplicateRecord", { static: true }) duplicateRecord;
	@ViewChild("createSuccess", { static: true }) createSuccess;

	categorias: Array<any>;
	unidadesMedida: Array<any>;

	formGroupAtividades: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required]),
		categoria: new FormControl(""),
		unidadeMedida: new FormControl(""),
		descricao: new FormControl(""),
		videoUri: new FormControl(""),
		ativo: new FormControl(true),
	});

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private atividadeService: TreinoApiAtividadeCrossfitService,
		private notify: SnotifyService
	) {}

	ngOnInit() {}

	ngAfterViewInit() {
		this.loadCategoriasEUnidades();
	}

	private loadCategoriasEUnidades() {
		this.categorias = [
			{
				codigo: "BARBELL",
				descricao: this.traducaoCategorias.getLabel("categoria-barbell"),
			},
			{
				codigo: "GYMNASTIC",
				descricao: this.traducaoCategorias.getLabel("categoria-gymnastic"),
			},
			{
				codigo: "ENDURANCE",
				descricao: this.traducaoCategorias.getLabel("categoria-endurance"),
			},
			{
				codigo: "NOTABLES",
				descricao: this.traducaoCategorias.getLabel("categoria-notables"),
			},
			{
				codigo: "GIRLS",
				descricao: this.traducaoCategorias.getLabel("categoria-girls"),
			},
			{
				codigo: "OPEN",
				descricao: this.traducaoCategorias.getLabel("categoria-open"),
			},
			{
				codigo: "THEHEROES",
				descricao: this.traducaoCategorias.getLabel("categoria-thereroes"),
			},
			{
				codigo: "CAMPEONATOS",
				descricao: this.traducaoCategorias.getLabel("categoria-campeonatos"),
			},
			{
				codigo: "CROSSFIT_GAMES",
				descricao: this.traducaoCategorias.getLabel("categoria-crossfit_games"),
			},
		];
		this.unidadesMedida = [
			{
				codigo: "REPS",
				descricao: this.traducaoUnidades.getLabel("unidade-reps"),
			},
			{
				codigo: "TIME",
				descricao: this.traducaoUnidades.getLabel("unidade-time"),
			},
			{
				codigo: "DISTANCE",
				descricao: this.traducaoUnidades.getLabel("unidade-distance"),
			},
			{
				codigo: "WEIGHT",
				descricao: this.traducaoUnidades.getLabel("unidade-weight"),
			},
			{
				codigo: "CALS",
				descricao: this.traducaoUnidades.getLabel("unidade-cals"),
			},
			{
				codigo: "REPSFORTIME",
				descricao: this.traducaoUnidades.getLabel("unidade-repsfortime"),
			},
			{
				codigo: "ROUNDS",
				descricao: this.traducaoUnidades.getLabel("unidade-rounds"),
			},
			{
				codigo: "ROUNDS_REPS",
				descricao: this.traducaoUnidades.getLabel("unidade-rounds_reps"),
			},
		];
	}

	dismiss() {
		this.activeModal.dismiss();
	}

	private obterDto() {
		const dto = this.formGroupAtividades.getRawValue();
		dto.id = null;
		dto.ativo = true;
		return dto;
	}

	createHandler() {
		const dto = this.obterDto();
		this.atividadeService.criarAtividade(dto).subscribe((resultCreate) => {
			if (resultCreate && resultCreate.toString() === "registro_duplicado") {
				this.notify.error(this.duplicateRecord.nativeElement.innerHTML);
			} else if (resultCreate) {
				this.notify.success(this.createSuccess.nativeElement.innerHTML);
				this.atividadeCriada.emit();
				this.activeModal.close("updated");
			}
		});
	}
}
