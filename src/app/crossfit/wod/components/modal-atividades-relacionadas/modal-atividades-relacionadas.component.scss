@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

.modal-header {
	padding-bottom: 0;
	.close {
		padding: 0;
		position: relative;
		top: -5px;
		margin: 0;
		span {
			line-height: 0;
		}
		&:focus {
			outline: none;
		}
	}
}
.modal-footer {
	border: 0 !important;
}
.btn-last {
	margin-left: 15px;
}
.line-form {
	margin-bottom: 15px;
}
.table-add {
	border-top: 1px solid $supportGray03;
	padding-top: 20px;
	margin-top: 25px;
}
::ng-deep pacto-cat-table-editable {
	border: 1px solid $supportGray02;
	border-radius: 10px;
	padding: 15px;
	display: block;
	margin-top: 15px;
	.pct-table-cell {
		span {
			font-family: $fontPoppins !important;
			color: hsla(220, 5%, 35%, 1) !important;
			font-weight: 700 !important;
			font-size: 12px !important;
		}
	}
	.pct-drop-down:before {
		color: $actionDefaultAble04 !important;
	}
	.pct-table-body {
		::ng-deep pacto-column-select,
		::ng-deep pacto-column-input {
			width: 80% !important;
		}
	}
}
