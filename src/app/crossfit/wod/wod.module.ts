import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { ModalAparelhosRelacionadosComponent } from "./components/modal-aparelhos-relacionados/modal-aparelhos-relacionados.component";
import { ModalAtividadesRelacionadasComponent } from "./components/modal-atividades-relacionadas/modal-atividades-relacionadas.component";
import { ModalTipoWodComponent } from "./components/modal-tipo-wod/modal-tipo-wod.component";
import { WodEditComponent } from "./components/wod-edit/wod-edit.component";
import { WodImportComponent } from "./components/wod-import/wod-import.component";
import { WodListaComponent } from "./components/wod-lista/wod-lista.component";

const funcionalidade = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.WOD,
	true
);

const routes: Routes = [
	{
		path: "",
		component: WodListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: "adicionar",
		component: WodEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
	{
		path: ":id",
		component: WodEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), BaseSharedModule, CommonModule],
	entryComponents: [
		WodImportComponent,
		ModalTipoWodComponent,
		ModalAtividadesRelacionadasComponent,
		ModalAparelhosRelacionadosComponent,
	],
	declarations: [
		WodListaComponent,
		WodEditComponent,
		WodImportComponent,
		ModalAtividadesRelacionadasComponent,
		ModalAparelhosRelacionadosComponent,
		ModalTipoWodComponent,
		ModalAtividadesRelacionadasComponent,
		ModalAparelhosRelacionadosComponent,
	],
})
export class WodModule {}
