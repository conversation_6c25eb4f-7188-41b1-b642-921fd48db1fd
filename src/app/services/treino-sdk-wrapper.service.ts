import { Injectable } from "@angular/core";
import {
	EmpresaFinanceiro,
	LoginUrlQueries,
	PactoLayoutSDKWrapper,
	PlataformaMenuV2Config,
	ServiceMap,
} from "pacto-layout";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../microservices/client-discovery/client-discovery.service";
import { Observable, of } from "rxjs";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { UsuarioBase } from "treino-api";
import { MovideskService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class TreinoSdkWrapperService extends PactoLayoutSDKWrapper {
	constructor(
		private _sessionService: SessionService,
		private _clientDiscoveryService: ClientDiscoveryService,
		private _localStorageService: LocalStorageSessionService,
		private _movideskService: MovideskService
	) {
		super();
	}

	apiToken(): string {
		return this._sessionService.apiToken;
	}

	currentCompany(): EmpresaFinanceiro {
		return this._sessionService.currentEmpresa || ({} as EmpresaFinanceiro);
	}

	getLocalStorageParams(): LoginUrlQueries {
		return this._localStorageService.getLocalStorageParams();
	}

	linkZw(usuarioOamd: string | number): Observable<any> {
		return this._clientDiscoveryService.linkZw(
			usuarioOamd as string,
			this._sessionService.empresaId
		);
	}

	get loggedCompanyId(): string | number {
		return this._sessionService.empresaId;
	}

	moduloUrl(module: string, empresaId: string | number): Observable<any> {
		return this._clientDiscoveryService.moduloUrl(module, empresaId);
	}

	oamdUserId(): string | number {
		return this._sessionService.usuarioOamd;
	}

	serviceUrls(): ServiceMap {
		return this._clientDiscoveryService.getUrlMap();
	}

	companyKey(): string {
		return this._sessionService.chave;
	}

	codigoUsuarioZw(): string {
		return this._sessionService.codUsuarioZW;
	}

	modulosHabilitados(): string[] {
		return this._sessionService.modulosHabilitados;
	}

	getConfig(): Observable<PlataformaMenuV2Config> {
		const paTreino =
			this.user && this.user.perfis && this.user.perfis.length > 0
				? this.user.perfis[0]
				: "";

		const paAdm =
			this._sessionService.perfilUsuarioAdm &&
			this._sessionService.perfilUsuarioAdm.perfilUsuario
				? this._sessionService.perfilUsuarioAdm.perfilUsuario.nome
				: "";

		return of(
			new PlataformaMenuV2Config({
				colaboradorNome: this.user
					? this.user.nome +
					  (this.user.perfis && this.user.perfis.length > 0
							? " (" + this.user.perfis[0] + ")"
							: "")
					: "",
				colaboradorNomeSimples: this.user ? this.user.nome : "",
				perfilAcessoTreino: paTreino,
				perfilAcessoAdm: paAdm,
				colaboradorAvatarUrl: this.user ? this.user.imageUri : "",
				avatarRedeUrl:
					this._sessionService.currentEmpresa &&
					this._sessionService.currentEmpresa.fotoKey !== ""
						? this._sessionService.currentEmpresa.fotoKey
						: "./assets/images/empty-image.png",
				nomeUnidade: this._sessionService.currentEmpresa
					? this._sessionService.currentEmpresa.nome
					: "",
				multiUnidade: this._sessionService.multiUnidade,
				independente: !this._sessionService.integracaoZW,
				configurationUrl: "/config",
				colaboradorUrl:
					"/colaboradores/user/" + (this.user ? this.user.id : ""),
				pathUrlZw: this._sessionService.pathUrlZw,
				key: this._sessionService.getKey,
				codigoUsuarioZw: this._sessionService.codigoUsuarioZw,
				empresa: this._sessionService.codigoEmpresa,
			})
		);
	}

	get user(): UsuarioBase {
		return this._sessionService.loggedUser;
	}

	get empresasAcesso() {
		return this._sessionService.empresasAcesso;
	}

	get empresas() {
		return this._sessionService.empresas;
	}

	get sessionService() {
		return this._sessionService;
	}

	get clientDiscoveryService() {
		return this._clientDiscoveryService;
	}

	get localStorageParamsService() {
		return this._localStorageService;
	}

	logout() {
		this.sessionService.logOut();
	}

	get movideskService(): any {
		return this._movideskService;
	}
}
