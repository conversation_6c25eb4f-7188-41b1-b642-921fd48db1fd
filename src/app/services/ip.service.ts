import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Observable, of } from "rxjs";
import { tap } from "rxjs/operators";

@Injectable({
	providedIn: "root",
})
export class IpService {
	constructor(private http: HttpClient) {}

	getIp(): Observable<any> {
		if (localStorage.getItem("client-ip")) {
			return of(localStorage.getItem("client-ip"));
		}
		return this.http
			.get("https://app.pactosolucoes.com.br/ip/v2.php", {
				responseType: "text",
			})
			.pipe(
				tap((response) => {
					localStorage.setItem("client-ip", response);
				})
			);
	}
}
