<pacto-cat-layout-v2>
	<div class="bi-wrapper">
		<div class="top-wrapper">
			<div class="header-titulo">
				<div class="type-h2 indicadores-titulo">
					<ng-container i18n="@@treino-bi:indicadores">
						Business Intelligence
					</ng-container>
				</div>
				<div class="type-caption indicadores-subtitulo">
					<ng-container>
						<span i18n="@@treino-bi:indicadores:atualizado-em">
							Atualizado em
						</span>
						{{ lastUpdate }}
					</ng-container>
				</div>
			</div>
			<div class="header-filtro">
				<div class="action-filters" i18n="@@avaliacao-bi:periodo:label">
					Período
					<!-- TIME CONTROLS SEMANA -->
				</div>
				<ng-container>
					<div class="time-controls">
						<div class="time-controls-content dataInicio">
							<pacto-datepicker
								[control]="formGroup.get('dataInicio')"></pacto-datepicker>
						</div>

						<div
							class="time-controls-content ate"
							i18n="@@avaliacao-bi:periodo-ate:label">
							até
						</div>
						<div class="time-controls-content dataFim">
							<pacto-datepicker
								[control]="formGroup.get('dataFim')"></pacto-datepicker>
						</div>

						<pacto-cat-button
							(click)="atualizarBi()"
							[icon]="'pct pct-refresh-cw'"
							[id]="'btn-atualizar-bi'"
							i18n-label="@@treino-bi:btn-atualizar-new"
							label="atualizar"></pacto-cat-button>
					</div>
				</ng-container>
			</div>
		</div>
	</div>

	<ng-container>
		<div class="row graficos">
			<div
				class="ocuAulas-card colunas"
				title="São contabilizados quantos alunos compareceram a uma aula coletiva ou turma em cada respectivo horário.">
				<pacto-cat-card-plain>
					<div class="card-wrapper">
						<div
							class="type-h5 ocuAulas-titulo"
							i18n="@@treino-bi:chart:ocupacao-aulas">
							Ocupação por Aulas
						</div>
						<pacto-cat-pie-chart
							(click)="openModalCenterAulas()"
							(dataPointSelected)="openModalCharPieAulas($event)"
							[colors]="colors"
							[id]="'ocupacao-aulas'"
							[labelCenter]="'Aulas'"
							[series]="pieOcuAulas"
							[showLegend]="false"></pacto-cat-pie-chart>
						<pacto-cat-chart-legend
							[colors]="colors"
							[labels]="ocuAulasLabels.getAllLabels()"
							[vertical]="true"
							class="ocuAulasLabels"></pacto-cat-chart-legend>
					</div>
				</pacto-cat-card-plain>
			</div>
			<div
				class="ocuAulas-card colunas"
				title="São contabilizados o total de alunos participantes nas aulas/turmas por modalidade.">
				<pacto-cat-card-plain>
					<div class="card-wrapper">
						<div
							class="type-h5 ocuAulas-titulo"
							i18n="@@treino-bi:chart:ocupacao-modalidades">
							Ocupação por Modalidades
						</div>
						<pacto-cat-pie-chart
							(click)="openModalCenterModalidades()"
							(dataPointSelected)="openModalCharPieModalidades($event)"
							[colors]="colors"
							[id]="'ocupacao-modalidades'"
							[labelCenter]="'modalidades'"
							[series]="pieOcuModalidades"
							[showLegend]="false"></pacto-cat-pie-chart>
						<pacto-cat-chart-legend
							[colors]="colors"
							[labels]="ocuAulasLabels.getAllLabels()"
							[vertical]="true"
							class="ocuAulasLabels"></pacto-cat-chart-legend>
					</div>
				</pacto-cat-card-plain>
			</div>
			<div
				class="ocuAulas-card colunas"
				title="Informações individuais para cada Professor/Colaborador, incluindo a quantidade de alunos participantes
nas aulas/turmas e a porcentagem de ocupação com base na capacidade total.">
				<pacto-cat-card-plain>
					<div class="card-wrapper">
						<div
							class="type-h5 ocuAulas-titulo"
							i18n="@@treino-bi:chart:ocupacao-professores">
							Ocupação por Professores
						</div>
						<pacto-cat-pie-chart
							(click)="openModalCenterProfessores()"
							(dataPointSelected)="openModalCharPieProfessores($event)"
							[colors]="colors"
							[id]="'ocupacao-professores'"
							[labelCenter]="'professores'"
							[series]="pieOcuProfessores"
							[showLegend]="false"></pacto-cat-pie-chart>
						<!-- <pacto-cat-chart-legend
							*ngIf="currentWindowWidth < 1666"
							[colors]="colors"
							[labels]="ocuAulasLabelsMD.getAllLabels()"
							[vertical]="true"
							class="ocuAulasLabels"></pacto-cat-chart-legend> -->
						<pacto-cat-chart-legend
							*ngIf="currentWindowWidth"
							[colors]="colors"
							[labels]="ocuAulasLabels.getAllLabels()"
							[vertical]="true"
							class="ocuAulasLabels"></pacto-cat-chart-legend>
					</div>
				</pacto-cat-card-plain>
			</div>
			<div
				class="ocuAulas-card colunas"
				title="Número de aulas coletivas ou de turma agendadas pelos alunos no período, com a taxa de frequência correspondente.
O relatório inclui listas individuais de presenças e faltas, acessíveis através do clique sobre a cor correspondente no gráfico.">
				<pacto-cat-card-plain>
					<div class="card-wrapper">
						<div
							class="type-h5 ocuAulas-titulo"
							i18n="@@treino-bi:chart:frequencia-alunos">
							Frequência dos Alunos
						</div>
						<pacto-cat-pie-chart
							(click)="openModalCenterFrequenciaAlunos()"
							(dataPointSelected)="openModalCharPieFrequenciaAlunos($event)"
							[centerValue]="alunos"
							[colors]="colorsFreq"
							[id]="'frequencia-alunos'"
							[labelCenter]="'alunos'"
							[series]="pieFrequencia"
							[showLegend]="false"></pacto-cat-pie-chart>
						<pacto-cat-chart-legend
							[colors]="colorsFreq"
							[labels]="ocuFrequenciaLabels.getAllLabels()"
							[vertical]="true"
							class="ocuAulasLabels"></pacto-cat-chart-legend>
					</div>
				</pacto-cat-card-plain>
			</div>
		</div>
		<div class="row">
			<pacto-treino-bi-professor-agendamento
				[dataFim]="this.formGroup.get('dataFim').value"
				[dataInicio]="this.formGroup.get('dataInicio').value"
				[professorFiltro]="null"></pacto-treino-bi-professor-agendamento>
		</div>
		<div class="row">
			<pacto-treino-bi-agenda-disponibilidade
				[dataFim]="this.formGroup.get('dataFim').value"
				[dataInicio]="this.formGroup.get('dataInicio').value"
				[professorFiltro]="null"></pacto-treino-bi-agenda-disponibilidade>
		</div>
	</ng-container>

	<!-- LABELS -->
	<pacto-traducoes-xingling #chartTraducoes>
		<span i18n="@@treino-bi:chart:aguardando" xingling="aguardando">
			Aguardando
		</span>
		<span i18n="@@treino-bi:chart:confirmados" xingling="confirmados">
			Confirmados
		</span>
		<span i18n="@@treino-bi:chart:executados" xingling="executados">
			Executados
		</span>
		<span xingling="faltas">Faltas</span>
		<span xingling="cancelados">Cancelados</span>
	</pacto-traducoes-xingling>

	<pacto-traducoes-xingling #traducoes>
		<span xingling="agendadosTitle">Agendados</span>
		<span xingling="executadosTitle">Executados</span>
		<span xingling="faltaramTitle">Faltas</span>
		<span xingling="canceladosTitle">Cancelados</span>

		<ng-template xingling="nomeProfessor">Nome do Professor</ng-template>
		<ng-template xingling="nomeAluno">Nome do aluno</ng-template>
		<ng-template xingling="evento">Evento</ng-template>
		<ng-template xingling="inicio">Data de Início</ng-template>

		<span xingling="professoresTitle">Professores</span>
		<span xingling="novosTreinosTitle">Treinos novos</span>
		<span xingling="renovadosTreinosTitle">Treinos renovados</span>
		<span xingling="horasDisponibilidadeTitle">Treinos renovados</span>
		<span xingling="revisadosTitle">Treinos revisados</span>
		<span xingling="avaliacoesFisicaTitle">Avaliações fisicas</span>
		<span xingling="avaliacoesFisicaSemAgendamentoTitle">Não realizadas</span>
		<span xingling="avaliacoesFisicaComAgendamentoTitle">Realizadas</span>
		<span xingling="aulasTitle">Aulas</span>
		<span xingling="modalidadesTitle">Modalidades</span>
		<span xingling="professoresDashAgendaTitle">Professores</span>
		<span xingling="frequenciaAlunos">Frequência Alunos</span>
		<span xingling="ocupacaoTitle">Ocupação</span>

		<ng-template xingling="nomeProfessor">Nome do Professor</ng-template>
		<ng-template xingling="nomeAluno">Nome do aluno</ng-template>
		<ng-template xingling="nomeAbreviado">Nome do aluno</ng-template>
		<ng-template xingling="nome">Nome</ng-template>
		<ng-template xingling="codigo">Codigo</ng-template>
		<ng-template xingling="matricula">Matricula</ng-template>
		<ng-template xingling="dataPrograma">Data programa</ng-template>
		<ng-template xingling="dataLancamento">Data lancamento</ng-template>
		<ng-template xingling="evento">Evento</ng-template>
		<ng-template xingling="inicio">Data de Início</ng-template>

		<ng-template xingling="aula">Aula</ng-template>
		<ng-template xingling="diaSemana">Dia</ng-template>
		<ng-template xingling="modalidades">Modalidades</ng-template>
		<ng-template xingling="modalidade">Modalidade</ng-template>
		<ng-template xingling="nomeModalidade">Modalidade</ng-template>
		<ng-template xingling="professor">Professor</ng-template>
		<ng-template xingling="horario">Horário</ng-template>
		<ng-template xingling="ocupacao">Ocupação</ng-template>
		<ng-template xingling="presencas">Presenças</ng-template>
		<ng-template xingling="aulas">Aulas</ng-template>
		<ng-template xingling="professores">Professor</ng-template>
		<ng-template xingling="frequencia">Frequência</ng-template>
		<ng-template xingling="capacidade">Capacidade</ng-template>
		<ng-template xingling="dia">Dia</ng-template>
	</pacto-traducoes-xingling>

	<pacto-traducoes-xingling #ocuFrequenciaLabels>
		<span xingling="Presenca">Presença</span>
		<span xingling="Faltas">Faltas</span>
	</pacto-traducoes-xingling>

	<pacto-traducoes-xingling #ocuAulasLabels>
		<span xingling="menorQue25">menor que 25%</span>
		<span xingling="entre25e50">25% a 50%</span>
		<span xingling="entre50e75">50% a 75%</span>
		<span xingling="maiorQue75">maior que 75%</span>
	</pacto-traducoes-xingling>

	<pacto-traducoes-xingling #ocuAulasLabels>
		<span xingling="menorQue25">menor que 25%</span>
		<span xingling="entre25e50">25% a 50%</span>
		<span xingling="entre50e75">50% a 75%</span>
		<span xingling="maiorQue75">maior que 75%</span>
	</pacto-traducoes-xingling>

	<pacto-traducoes-xingling #ocuAulasLabelsMD>
		<span xingling="menorQue25">menor que 25%</span>
		<span xingling="entre25e50">25 a 50%</span>
		<span xingling="entre50e75">50 a 75%</span>
		<span xingling="maiorQue75">maior que 75%</span>
	</pacto-traducoes-xingling>
</pacto-cat-layout-v2>
