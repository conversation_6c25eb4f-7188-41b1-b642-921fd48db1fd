@import "~src/assets/scss/pacto/plataforma-import.scss";

.bi-wrapper > * {
	display: block;
}

.header-filtro {
	margin-right: -30px;
}

pacto-cat-pie-chart {
	cursor: pointer;
}

.bi-wrapper .top-wrapper {
	display: flex;
	justify-content: space-between;
	color: $pretoPri;
	max-width: 1539px;

	.action-filters {
		margin-top: 10px;
		margin-left: 20px;
		display: flex;
	}
}

.indicadores-titulo {
	text-transform: capitalize;
}

.row {
	padding-top: 30px;
	margin-left: 0px;
	margin-right: 0px;
}

.header-filtro {
	margin-right: -15px;
}

.dash-content {
	padding: 30px;

	.row.row-agenda-disponibilidade {
		display: flex;
		justify-content: space-between;
		z-index: 0;

		.col-resumo-agenda {
			.card-resumo-agenda {
				position: relative;
				max-width: 385px;
				height: 450px;

				@media (min-width: $bootstrap-breakpoint-xl) {
					width: 385px;
				}

				.resumo-agenda {
					.resumo-agenda-title {
						padding-bottom: 24px;
					}

					.resumo-agenda-grid {
						display: grid;
						gap: 30px 29px;
						grid-template-columns: 1fr 1fr;
						grid-template-rows: 1fr 1fr;

						.resumo-agenda-grid-content {
							@media (min-width: $bootstrap-breakpoint-lg) {
								height: 155px;
							}

							max-width: 148px;
							max-height: 155px;
							display: block;
							align-items: center;
							justify-content: center;
							font-size: 40px;
							font-family: sans-serif;
						}
					}
				}
			}
		}

		.col-disponibilidade {
			.card-disponibilidade {
				position: relative;
				max-width: 803px;
				height: 450px;

				.disponibilidade {
					.top-row {
						display: flex;
						justify-content: space-between;

						.disponibilidade-title {
							padding-bottom: 24px;
						}
					}

					.center-row {
						display: flex;
						align-items: center;
						align-content: center;
						margin-top: 20px;
						flex-grow: 1;

						pacto-cat-pie-chart {
							position: relative;
							flex-shrink: 0;
							flex-basis: 40%;
							top: 8px;
							right: 8px;
						}

						.values-grid {
							display: grid;
							grid-template-rows: 1fr 1fr;
							grid-template-columns: 1fr 1fr 1fr;
							grid-column-gap: 20px;
							grid-row-gap: 60px;
							flex-grow: 1;
							text-align: center;

							.grid-value {
								@extend .type-h3-bold;
								color: $pretoPri;
							}

							.grid-item-label {
								@extend .type-h6;
								color: $cinza05;
							}
						}
					}

					pacto-cat-chart-legend {
					}
				}
			}
		}
	}

	.row.row-professores-avaliacao {
		position: block;
		display: flex;
		justify-content: left;
		z-index: 0;

		.col-professores-agendamentos {
			max-width: 803px;

			.card-professores-agendamentos {
				position: relative;
				display: block;
				height: 450px;
				max-width: 803px;

				.professores-agendamentos {
					display: block;

					.professores-agendamentos-grid {
						display: grid;
						gap: 29px 31px;
						grid-template-columns: 1fr 1fr 1fr 1fr;
						grid-template-rows: 1fr 1fr;

						.professores-agendamentos-grid-content {
							height: 156px;
							max-height: 156px;
							max-width: 162px;
						}
					}

					.prof-agendamentos-title {
						padding-bottom: 24px;
					}
				}
			}
		}
	}
}

.resumo-grid {
	display: grid;
	margin-top: 30px;
	grid-template-rows: 1fr 1fr;
	grid-template-columns: 1fr 1fr;
	grid-gap: 30px;
}

.header-titulo {
	margin-left: 1px;
}

.time-controls {
	display: flex;
	justify-content: space-between;
	margin: 0px 15px;
}

.time-controls-content.dataInicio {
	margin-right: 12px;
}

.time-controls-content.ate {
	margin-right: 12px;
	margin-top: 4px;
}

.time-controls-content.dataFim {
	margin-right: 24px;
}

//old -----------------------------------------------------------------

.agenda-dash {
	padding: 60px;

	.agenda-wrapper .top-agenda-wrapper {
		display: flex;
		justify-content: space-between;
		color: $pretoPri;
		margin-bottom: 30px;

		pacto-cat-select {
			width: 200px;
			display: block;
		}

		pacto-cat-button {
			margin-left: 5px;
			margin-right: 5px;
		}

		.action-filters {
			display: flex;
		}
	}

	@media (max-width: 1024px) {
		padding: 30px;
	}
}

.agenda-title {
	@extend .type-h5;
	color: $pretoPri;
}

.resumo-agenda {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.top-row {
	display: flex;
	justify-content: space-between;
}

.center-row {
	display: flex;
	align-items: center;
	align-content: center;
	margin-top: 20px;
	flex-grow: 1;

	pacto-cat-pie-chart {
		position: relative;
		flex-shrink: 0;
		flex-basis: 40%;
		top: 8px;
		right: 8px;
	}
}

pacto-cat-chart-legend {
	display: none;

	@media (min-width: $bootstrap-breakpoint-xl) {
		display: inline-table;
	}
}

pacto-cat-pie-chart {
	display: none;

	@media (min-width: $bootstrap-breakpoint-xl) {
		display: block;
	}
}

.values-grid {
	display: grid;
	grid-template-rows: 1fr 1fr;
	grid-template-columns: 1fr 1fr 1fr;
	grid-column-gap: 20px;
	grid-row-gap: 60px;
	flex-grow: 1;
	text-align: center;

	.grid-item-label {
		@extend .type-h6;
		color: $cinza05;
	}

	.grid-item-value {
		@extend .type-h3-bold;
		color: $pretoPri;
	}
}

.resumo-grid {
	display: grid;
	margin-top: 30px;
	margin-right: 2px;
	grid-template-rows: 1fr 1fr;
	grid-template-columns: 1fr 1fr;
	grid-gap: 30px;
}

//.disponibilidade-agendamentos{
//}

prof-agendamentos-title {
	@extend .type-h5;
	color: $pretoPri;
}

.professor-agendamentos-chart {
	.grid {
		display: grid;
		margin-top: 30px;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: 1fr 1fr 1fr;
		grid-gap: 30px;

		@media (min-width: $bootstrap-breakpoint-lg) {
			grid-template-columns: 1fr 1fr 1fr;
			grid-template-rows: 1fr 1fr;
		}
	}
}

.avaliacao-fisica-aux {
	margin-top: 30px;

	@media (min-width: $bootstrap-breakpoint-xl) {
		margin-top: 0px;
	}

	color: $pretoPri;

	.value-label {
		color: $pretoPri;
		font-size: 24px;
		font-weight: bold;

		.value-label-sub {
			color: $cinza05;
			font-size: 12px;
			font-weight: normal;
		}
	}

	.card-wrapper {
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: space-between;
		text-align: center;
		height: 100%;
	}
}

.card-wrapper {
	::ng-deep.apexcharts-text.apexcharts-datalabel-label {
		font-size: 0.6vw;
	}

	::ng-deep.apexcharts-text.apexcharts-datalabel-value {
		font-size: 1.3vw;
	}
}

.button-compartilhar {
	display: -webkit-inline-box;
	font-size: small;
}

.btn-primary {
	color: $branco;
	background-color: $azulimPri !important;
	border-color: $azulimPri !important;
}

.agenda-title-sub {
	float: right;
}

:host {
	color: $pretoPri;
}

.movimentacao-result {
	display: flex;
	flex-wrap: wrap;
	flex-direction: column;
	text-align: center;
	height: 100%;

	.movimentacao-desc {
		@extend .type-h6;
		padding: 20px 10px;
		flex-grow: 1;
	}

	.blocks-wrapper {
		display: flex;
		margin-bottom: 10px;

		& .block {
			padding: 0px 20px 0px 20px;
			flex-basis: 50%;

			&:first-of-type() {
				border-right: 1px solid $cinzaClaro02;
			}
		}
	}

	.percent-over-agerage,
	.execucoes {
		color: $cinza04;
	}

	.weekday {
		line-height: 2em;
		text-transform: uppercase;
	}

	.icon-wrapper {
		padding: 7px 0px;
	}

	.pct {
		font-size: 32px;
	}

	.sun {
		color: $pequizaoPri;
	}

	.moon {
		color: $azulim05;
	}

	pacto-cat-percent-bar-simple {
		display: block;
		margin: 5px 0px;
	}
}

.ocuAulas-titulo {
	text-align: center;
	padding-bottom: 20px;
}

.disponibilidade-agendamentos-aux {
	float: left;
	display: block;
	margin-top: 30px;

	@media (min-width: $bootstrap-breakpoint-lg) {
		margin-top: 0px;
		display: flex;
	}
}

.movimentacao-result-aux {
	position: relative;
	width: 100%;
	min-height: 1px;
	padding-right: 15px;
	padding-left: 15px;
	display: block;
	//min-width: 350px;
	margin-top: 30px;

	@media (min-width: $bootstrap-breakpoint-lg) {
		margin-top: 0px;
		display: flex;
		max-width: 340px;
	}
}

.ocuAulas-card {
	@media (min-width: $bootstrap-breakpoint-lg) {
		margin-top: 0px;
	}
}

.card-wrapper {
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: space-between;
	text-align: center;
	height: 100%;
}

.graficos {
	gap: 30px;
	justify-content: space-between;
	// display: grid;
	// grid-template-columns: 1fr 1fr;
	// grid-gap: 30px;

	// @media (min-width: 1300px) {
	// 	grid-template-columns: 1fr 1fr 1fr 1fr;
	// 	grid-template-rows: 1fr;
	// 	grid-gap: 30px;
	// 	::ng-deep.pacto-pie-chart-wrapper {
	// 		width: 100%;
	// 	}
	// }
	.colunas {
		flex: 0 0 0%;
		max-width: calc(270px - 30px);

		@media (min-width: 1265px) and (max-width: 1400px) {
			flex: 0 0 50%;
			max-width: calc(50% - 30px);
		}

		@media (min-width: 1400.01px) {
			flex: 0 0 25%;
			max-width: calc(25% - 30px);
		}
	}
}

pacto-treino-bi-agenda-disponibilidade,
pacto-treino-bi-professor-agendamento {
	width: 100%;
}

pacto-cat-card-plain {
	height: 100%;
}
