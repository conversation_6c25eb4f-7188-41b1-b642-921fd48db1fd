import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { FormControl, FormGroup } from "@angular/forms";
import {
	PactoColor,
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	PactoIcon,
	PieChartSet,
	RelatorioComponent,
} from "ui-kit";
import { AgendaBiStateService } from "./agenda-bi-state";
import { Subscription } from "rxjs";
import { NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import {
	ModalRelatorioConfig,
	relatorios,
} from "../../../treino/treino-bi/components/treino-bi-agenda-disponibilidade/relatorios.config";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

@Component({
	selector: "pacto-agenda-bi",
	templateUrl: "./agenda-bi.component.html",
	styleUrls: ["./agenda-bi.component.scss"],
})
export class AgendaBiComponent implements OnInit {
	lastUpdate = null;
	dataMinima: NgbDateStruct = null;
	formGroup: FormGroup = new FormGroup({
		dataInicio: new FormControl(new Date().setDate(1)),
		dataFim: new FormControl(new Date().getTime()),
	});
	endpointShare: string;
	currentWindowWidth: number;
	comportamentoFc = new FormControl();
	comportamentos: any[] = [];
	turma = false;
	pieSeries: PieChartSet[];
	pieOcuAulas: PieChartSet[];
	pieOcuModalidades: PieChartSet[];
	pieOcuProfessores: PieChartSet[];
	pieFrequencia: PieChartSet[];
	alunos;
	colors: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.LARANJINHA,
		PactoColor.PEQUIZAO_PRI,
		PactoColor.CHUCHUZINHO,
	];
	colorsFreq: PactoColor[] = [PactoColor.CHUCHUZINHO, PactoColor.HELLBOY_PRI];
	configuracaoGestao;
	@ViewChild("traducoes", { static: true }) traducoes;
	config: PactoDataGridConfigDto;
	relatorio: RelatorioComponent;
	periodo: {
		inicio: "";
		fim: "";
	};

	constructor(
		private agendaService: AgendaBiStateService,
		private modal: ModalService,
		private rest: RestService,
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService,
		private session: SessionService
	) {
		this.periodo = {
			inicio: this.formGroup.get("dataInicio").value,
			fim: this.formGroup.get("dataFim").value,
		};
	}

	biDataLoadSubscription: Subscription;

	ngOnInit() {
		if (!this.lastUpdate) {
			this.lastUpdate = "-";
		}
		const dataInicio = new Date(this.formGroup.get("dataInicio").value);
		this.dataMinima = {
			day: dataInicio.getDate(),
			month: dataInicio.getMonth() + 1,
			year: dataInicio.getFullYear(),
		};
		this.currentWindowWidth = window.innerWidth;
		this.obterBi();
	}

	private buildPieChartData() {
		if (this.agendaService.graficosAula) {
			this.pieOcuAulas = this.agendaService.graficosAula.aulas || [];
			this.pieOcuModalidades =
				this.agendaService.graficosAula.modalidades || [];
			this.pieOcuProfessores =
				this.agendaService.graficosAula.professores || [];
			this.pieFrequencia = this.agendaService.graficosAula.frequencia || [];
			this.alunos = this.agendaService.graficosAula.alunos || [];
		}
	}

	atualizarBi() {
		if (
			this.periodo.inicio !== this.formGroup.get("dataInicio").value ||
			this.periodo.fim !== this.formGroup.get("dataFim").value
		) {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.ALTEROU_DATA_ATAUALIZOU
			);
			this.periodo = {
				inicio: this.formGroup.get("dataInicio").value,
				fim: this.formGroup.get("dataFim").value,
			};
		} else {
			this.session.notificarRecursoEmpresa(RecursoSistema.APENAS_ATUALIZOU);
		}
		this.setupEvents(true);
		this.buildPieChartData();
	}

	obterBi() {
		this.setupEvents(false);
		this.buildPieChartData();
	}

	private setupEvents(forcar: boolean) {
		if (this.biDataLoadSubscription) {
			this.biDataLoadSubscription.unsubscribe();
		}
		const params = {
			inicio: this.formGroup.get("dataInicio").value,
			fim: this.formGroup.get("dataFim").value,
		};
		this.biDataLoadSubscription = this.agendaService
			.bi(forcar, {
				filtros: JSON.stringify(params),
			})
			.subscribe({
				next: (result) => {
					this.fetchDados(result);
				},
				error: (httpError) => {
					this.snotifyService.error(httpError.error.meta.message);
					console.error("Erro ao carregar dados: ", httpError);
				},
			});
	}

	public fetchDados(result) {
		if (result.biAulas) {
			this.agendaService.agenda = result.biAgenda;
			this.agendaService.graficosAula = result.biAulas;
			this.agendaService.update$.next(true);
			this.lastUpdate = result.ultimaAtualizacao;
			this.buildPieChartData();
		}
		this.cd.detectChanges();
	}

	get PactoIcon() {
		return PactoIcon;
	}

	pieFormatter = (v) => {
		return v + "H";
	};

	get comportamento() {
		return this.comportamentoFc.value;
	}

	openModalCenterAulas() {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_TOTALIZADOR_OCUPACAO_AULAS
		);
		this.buildModalHandler(relatorios.aulas, -1);
	}

	openModalCharPieAulas(event: any) {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_INDICADORES_GRAFICOS_OCUPACAO_AULAS
		);
		this.buildModalHandler(relatorios.aulas, event);
	}

	openModalCenterModalidades() {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_TOTALIZADOR_OCUPACAO_MODOLIDADES
		);
		this.buildModalHandler(relatorios.modalidades, -1);
	}

	openModalCharPieModalidades(event: any) {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_INDICADORES_GRAFICOS_OCUPACAO_MODALIDADES
		);
		this.buildModalHandler(relatorios.modalidades, event);
	}

	openModalCenterProfessores() {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_TOTALIZADOR_OCUPACAO_PROFESSOR
		);
		this.buildModalHandler(relatorios.professoresDashAgenda, -1);
	}

	openModalCharPieProfessores(event: any) {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_INDICADORES_GRAFICOS_OCUPACAO_PROFESSOR
		);
		this.buildModalHandler(relatorios.professoresDashAgenda, event);
	}

	openModalCenterFrequenciaAlunos() {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.CLIQUES_TOTALIZADOR_FREQUENCIA_ALUNOS
		);
		this.buildModalHandler(relatorios.frequenciaAlunos, -1);
	}

	openModalCharPieFrequenciaAlunos(event: any) {
		if (event === 0) {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.CLIQUES_INDICADORES_GRAFICOS_PRESENCA_ALUNOS
			);
		} else if (event === 1) {
			this.session.notificarRecursoEmpresa(
				RecursoSistema.CLIQUES_INDICADORES_GRAFICOS_FALTAS_ALUNOS
			);
		}
		this.buildModalHandler(relatorios.presencasAlunos, event);
	}

	private buildModalHandler(item: ModalRelatorioConfig, codigoRelatorio) {
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE,
			"modal-mxl"
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.telaId = item.title;
		this.relatorio.sessionService = this.session;
		const urlLog = "log/listar-log-exportacao/" + item.title;
		this.config = {
			endpointUrl: this.rest.buildFullUrl(item.endpoint),
			logUrl: this.rest.buildFullUrl(urlLog),
			pagination: true,
			quickSearch: true,
			exportButton: false,
			rowClick: false,
			columns: [],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				ordenavel: column.ordenavel,
				visible: true,
				valueTransform: null,
			};
			if (column.date) {
				columnConfig.valueTransform = (d) => moment(d).format("DD/MM/YYYY");
			}
			this.config.columns.push(columnConfig);
		});
		const inicio = this.formGroup.get("dataInicio").value;
		const fim = this.formGroup.get("dataFim").value;
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				codigoRelatorio,
				inicio,
				fim,
			},
		};
	}
}
