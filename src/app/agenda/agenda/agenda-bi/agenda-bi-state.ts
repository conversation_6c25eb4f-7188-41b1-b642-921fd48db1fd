import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable, of } from "rxjs";
import { map, tap } from "rxjs/operators";
import {
	TreinoApiBiService,
	TreinoBiAgenda,
	BiAula,
	BiAgenda,
} from "treino-api";

@Injectable()
export class AgendaBiStateService {
	constructor(private treinoBiService: TreinoApiBiService) {}

	agenda: TreinoBiAgenda;
	graficosAula: BiAula;
	cache: BiAgenda;
	update$: BehaviorSubject<any> = new BehaviorSubject(null);

	bi(forcar: boolean, params): Observable<BiAgenda> {
		if (this.cache && !forcar) {
			return of(this.cache);
		} else {
			return this.treinoBiService.biAulas(params).pipe(
				tap((result) => (this.cache = result)),
				map((response) => response)
			);
		}
	}
}
