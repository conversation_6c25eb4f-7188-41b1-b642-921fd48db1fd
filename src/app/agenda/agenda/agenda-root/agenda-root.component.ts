import { Component, OnInit } from "@angular/core";
import { Router, NavigationEnd } from "@angular/router";
import { PactoIcon } from "ui-kit";

@Component({
	selector: "pacto-agenda-root",
	templateUrl: "./agenda-root.component.html",
	styleUrls: ["./agenda-root.component.scss"],
})
export class AgendaRootComponent implements OnInit {
	turma = false;
	servico = false;

	constructor(private router: Router) {}

	get PactoIcon() {
		return PactoIcon;
	}

	ngOnInit() {
		this.router.events.subscribe((event) => {
			if (event instanceof NavigationEnd) {
				this.updateSelected();
			}
		});
		this.updateSelected();
	}

	clickHandler(event) {
		if (event === "agenda-turma") {
			this.router.navigate(["agenda", "turmas"]);
		} else if (event === "agenda-servico") {
			this.router.navigate(["agenda", "servicos"]);
		}
	}

	private updateSelected() {
		const turmas = this.router.url.match(/turmas/);
		const servicos = this.router.url.match(/servicos/);
		this.turma = turmas ? true : false;
		this.servico = servicos ? true : false;
	}
}
