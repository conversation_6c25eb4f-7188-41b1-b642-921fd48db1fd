import { Injectable } from "@angular/core";
import {
	AulaFiltro,
	TurmaZW,
	AgendaView,
	TreinoApiTurmaService,
	AgendaMetaCards,
} from "treino-api";
import {
	AgendaDiaAgrupamento,
	AgendaTurmaState,
	Aula,
} from "./agenda-turma-state.model";
import { ResultadoPorDia } from "@base-core/rest/rest.model";
import { DiscreteGridEvent } from "../agenda-time-grid/DiscreteGridManager";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import { TurmaCardDetailComponent } from "../agenda-turma/turma-card-detail/turma-card-detail.component";
import { TurmaCardComponent } from "../agenda-turma/turma-card/turma-card.component";

import { debounceTime, map } from "rxjs/operators";
import {
	BehaviorSubject,
	Observable,
	Observer,
	merge,
	Subscription,
} from "rxjs";
import { FormControl } from "@angular/forms";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";
import { AgendaCardsState } from "./agenda-cards-state.model";
import { AgendaCard } from "../../../../../projects/treino-api/src/lib/agenda-card.model";

declare var moment;

export interface AgendaTurmaConfig {
	agrupamento: AgendaDiaAgrupamento;
	view: AgendaView;
	filtroDia: AulaFiltro;
	filtroSemana: AulaFiltro;
}

@Injectable({ providedIn: "root" })
export class AgendaCardsStateService {
	private loading$: BehaviorSubject<boolean>;
	private state: AgendaCardsState;
	private loadDataSubscription: Subscription;
	closeTooltip$: BehaviorSubject<any> = new BehaviorSubject(false);
	forceLoad$: BehaviorSubject<any> = new BehaviorSubject(false);
	private forceUpdate$: BehaviorSubject<any> = new BehaviorSubject(false);
	private update$: BehaviorSubject<boolean> = new BehaviorSubject(false);
	private agendamentoRemovido$: BehaviorSubject<boolean> =
		new BehaviorSubject<boolean>(false);

	private turmaSelecionadaHandle: Observer<Aula>;
	modalidades: FormControl;
	search: FormControl = new FormControl("");
	turno: FormControl = new FormControl("todos");
	tipo: FormControl = new FormControl("TODAS");

	constructor(
		private util: AgendaUtilsService,
		private utilWindow: WindowUtilService,
		private turmasService: TreinoApiTurmaService
	) {
		this.loading$ = new BehaviorSubject(true);
		this.state = new AgendaCardsState();
		this.eventSetup();
	}

	get view(): AgendaView {
		return this.state.view$.value;
	}

	get agrupamento(): AgendaDiaAgrupamento {
		return this.state.agrupamento$.value;
	}

	/**
	 * aaaammdd
	 */
	get dia(): string {
		return this.state.dia$.value;
	}

	get loading() {
		return this.loading$.value;
	}

	/**
	 * emite um evento toda vez que o estado é atualizado
	 */
	get stateUpdate$(): Observable<boolean> {
		return this.update$.asObservable();
	}

	get turmaUpdate$(): Observable<boolean> {
		return this.forceUpdate$.asObservable();
	}

	get cards(): AgendaMetaCards {
		return this.state.cards;
	}

	get filtros(): AulaFiltro {
		if (this.state.viewDia) {
			return this.state.filtroDia$.value;
		} else if (this.state.viewSemana) {
			return this.state.filtroSemana$.value;
		}
	}

	getTurmaById(id: number) {
		const turmas = this.state.cards;
		let found;
		outer: for (const dia in turmas) {
			if (turmas.hasOwnProperty(dia)) {
				const turmasDia: Aula[] = turmas[dia];
				for (const horarioTurma of turmasDia) {
					if (horarioTurma.horarioTurmaId === id) {
						found = Object.assign({}, horarioTurma);
						break outer;
					}
				}
			}
		}
		return found;
	}

	public eventSetup() {
		const dataNeedsLoad$ = merge(
			this.state.dia$,
			this.state.filtroDia$,
			this.state.filtroSemana$,
			this.state.view$,
			this.forceLoad$
		);
		dataNeedsLoad$.pipe(debounceTime(25)).subscribe(() => {
			if (this.loadDataSubscription) {
				this.loadDataSubscription.unsubscribe();
			}
			this.loadDataSubscription = this.loadTurmas().subscribe();
		});

		merge(this.loading$, this.forceUpdate$).subscribe(() => {
			this.update$.next(this.loading);
		});
	}

	private loadTurmas(): Observable<boolean> {
		if (!this.loading) {
			this.loading$.next(true);
		}
		let dia = this.state.dia$.value;
		const viewDia = this.state.viewDia;

		/**
		 * View da semana usa segunda feira
		 * como dia de referencia.
		 */
		if (this.state.viewSemana) {
			const diaMoment = moment(dia, "YYYYMMDD");
			if (diaMoment.day() === 0) {
				diaMoment.day(-6);
			} else {
				diaMoment.day(1);
			}
			dia = diaMoment.format("YYYYMMDD");
		}

		const filtro = viewDia
			? this.state.filtroDia$.value
			: this.state.filtroSemana$.value;
		return this.turmasService.agendaCards(dia, this.view, filtro).pipe(
			map((result) => {
				this.state.cards = result;
				this.loading$.next(false);
				return true;
			})
		);
	}

	/**
	 *
	 * @param dia aaaammdd - 20190115 (15/01/2019)
	 */
	setDia(dia: string) {
		this.state.dia$.next(dia);
	}

	setModalidade(modalidadeId: number[]) {
		let source;
		if (this.state.viewDia) {
			source = Object.assign({}, this.state.filtroDia$.value);
		} else {
			source = Object.assign({}, this.state.filtroSemana$.value);
		}
		if (modalidadeId) {
			source.modalidadesIds = modalidadeId;
		} else {
			source.modalidadesIds = [];
		}
		this.state.filtroDia$.next(source);
		this.state.filtroSemana$.next(source);
	}

	setSearch(search: string) {
		let source;
		if (this.state.viewDia) {
			source = Object.assign({}, this.state.filtroDia$.value);
		} else {
			source = Object.assign({}, this.state.filtroSemana$.value);
		}

		source.search = search;

		this.state.filtroDia$.next(source);
		this.state.filtroSemana$.next(source);
	}

	setTurno(turno: string) {
		let source;
		if (this.state.viewDia) {
			source = Object.assign({}, this.state.filtroDia$.value);
		} else {
			source = Object.assign({}, this.state.filtroSemana$.value);
		}

		source.turno = turno;

		this.state.filtroDia$.next(source);
		this.state.filtroSemana$.next(source);
	}

	setTipo(tipo: string) {
		let source;
		if (this.state.viewDia) {
			source = Object.assign({}, this.state.filtroDia$.value);
		} else {
			source = Object.assign({}, this.state.filtroSemana$.value);
		}

		source.tipo = tipo;

		this.state.filtroDia$.next(source);
		this.state.filtroSemana$.next(source);
	}

	setView(view: AgendaView) {
		localStorage.setItem("view-agenda", "" + view);
		console.log(view);
		this.state.view$.next(view);
	}

	setFiltro(filtro: AulaFiltro) {
		this.state.filtroDia$.next(filtro);
		this.state.filtroSemana$.next(filtro);
	}

	convertTurmaIntoGrid(
		aula: Aula,
		component,
		hoverComponent
	): DiscreteGridEvent {
		const eventStart = this.util.convertTimeLabelIntoMinutes(
			aula.horarioInicio
		);
		const eventEnd = this.util.convertTimeLabelIntoMinutes(aula.horarioFim);
		let eventDuration = eventEnd - eventStart;
		if (eventDuration <= 15) {
			eventDuration = 15;
		} else {
			eventDuration = this.utilWindow.roundToMultipleOf(eventDuration, 15);
		}
		const event = new DiscreteGridEvent({
			start: this.convertIntoUnit(aula.horarioInicio),
			height: eventDuration / 15,
			payload: aula,
			hoverComponent,
			component,
		});
		return event;
	}

	updateTurma(turma: TurmaZW) {
		let found = false;
		try {
			if (this.state.cards && this.state.cards.itens) {
				this.state.cards.itens.forEach((h: any) => {
					if (turma.horarioInicio === h.horario) {
						const horariosDia: Array<AgendaCard> = h.dias[turma.dia];
						if (horariosDia) {
							horariosDia.forEach((t: AgendaCard) => {
								if (t.codigo == turma.horarioTurmaId) {
									t.ocupacao = turma.numeroAlunos;
									if (
										turma.professorSubstituto &&
										turma.professorSubstituto.nome &&
										turma.professorSubstituto.nome !== ""
									) {
										t.professor = turma.professorSubstituto.nome;
									} else {
										t.professor = turma.professor.nome;
									}
								}
							});
						}
					}
				});
			}
		} catch (e) {}

		if (!found) {
			this.eventSetup();
		}

		this.forceUpdate$.next(true);
	}

	updateAll() {
		this.forceLoad$.next(true);
		this.forceUpdate$.next(true);
	}

	private convertIntoUnit(time: string): number {
		const minutes = this.util.convertTimeLabelIntoMinutes(time);
		const roundedToMultiple = this.utilWindow.roundToMultipleOf(minutes, 15);
		return roundedToMultiple / 15;
	}

	startLoad() {
		this.loading$.next(true);
	}

	stopLoad() {
		this.loading$.next(false);
	}

	get agendamentoRemovido(): Observable<boolean> {
		return this.agendamentoRemovido$.asObservable();
	}

	notifyAgendamentoRemovido() {
		this.agendamentoRemovido$.next(true);
	}
}
