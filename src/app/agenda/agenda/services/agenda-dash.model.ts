export class DashAgenda {
	ResumoDaAgenda: {
		agendados: number;
		executados: number;
		faltas: number;
		cancelados: number;
	};
	DisponibilidadeXAgendamentos: {
		totalHoras: number;
		aguardando: number;
		confirmados: number;
		executados: number;
		faltas: number;
		cancelados: number;
		agendamentosPorTipoComportamento: {
			tipo: string;
			disponibilidade: number;
			executaram: number;
			cancelaram: number;
			faltaram: number;
			aguardandoConfirmacao: number;
			confirmado: number;
		}[];
	};
	ProfessoresXAgendamentos: {
		professores: number;
		disponibilidade: number;
		executados: number;
		ocupacao: number;
		treinosNovos: number;
		treinosRenovados: number;
		treinosRevisados: number;
		avaliacaoFisica: number;
	};
	AvaliacaoFisica: {
		totalDeAlunos: number;
		realizadas: number;
	};
	OcupacaoPorAulas: {
		aulas: number;
		menorQue25: number;
		entre25e50: number;
		entre50e75: number;
		maiorque75: number;
	};
	OcupacaoPorModalidades: {
		modalidades: number;
		menorQue25: number;
		entre25e50: number;
		entre50e75: number;
		maiorQue75: number;
	};
	OcupacaoPorProfessores: {
		professores: number;
		menorQue25: number;
		entre25e50: number;
		entre50e75: number;
		maiorque75: number;
	};
	FrequenciaDosAlunos: {
		alunos: number;
		presencas: number;
		faltas: number;
	};
}
