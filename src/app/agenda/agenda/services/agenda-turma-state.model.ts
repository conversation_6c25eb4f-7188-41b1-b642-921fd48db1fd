import { BehaviorSubject } from "rxjs";
import { AulaFiltro, TurmaZW, AgendaView } from "treino-api";
import { ResultadoPorDia } from "@base-core/rest/rest.model";

declare var moment;

export enum AgendaDiaAgrupamento {
	PROFESSOR = "PROFESSOR",
	AMBIENTE = "AMBIENTE",
	MODALIDADE = "MODALIDADE",
	SEM = "SEM",
}

export type Aula = TurmaZW;

export class AgendaTurmaState {
	agrupamento$: BehaviorSubject<AgendaDiaAgrupamento>;
	dia$: BehaviorSubject<string>;
	view$: BehaviorSubject<AgendaView>;
	filtroDia$: BehaviorSubject<AulaFiltro>;
	filtroSemana$: BehaviorSubject<AulaFiltro>;

	turmas: ResultadoPorDia<Aula> = null;

	get viewDia(): boolean {
		return this.view$.value === AgendaView.DIA;
	}

	get viewSemana(): boolean {
		return this.view$.value === AgendaView.SEMANA;
	}

	constructor() {
		this.agrupamento$ = new BehaviorSubject(AgendaDiaAgrupamento.SEM);
		const today = moment().format("YYYYMMDD");
		this.dia$ = new BehaviorSubject(today);
		this.view$ = new BehaviorSubject(AgendaView.DIA);
		this.filtroDia$ = new BehaviorSubject(new AulaFiltro());
		this.filtroSemana$ = new BehaviorSubject(new AulaFiltro());
	}

	updateTurma(turma: TurmaZW) {
		const dia = turma.dia;
		if (this.turmas[dia]) {
			this.turmas[dia].forEach((turmaUpdate: TurmaZW, index) => {
				if (turmaUpdate.horarioTurmaId === turma.horarioTurmaId) {
					this.turmas[dia][index] = turma;
				}
			});
		}
	}
}
