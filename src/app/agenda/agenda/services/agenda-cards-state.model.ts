import { BehaviorSubject } from "rxjs";
import { AulaFiltro, TurmaZW, AgendaView, AgendaMetaCards } from "treino-api";

declare var moment;

export enum AgendaDiaAgrupamento {
	PROFESSOR = "PROFESSOR",
	AMBIENTE = "AMBIENTE",
	MODALIDADE = "MODALIDADE",
	SEM = "SEM",
}

export type Aula = TurmaZW;

export class AgendaCardsState {
	agrupamento$: BehaviorSubject<AgendaDiaAgrupamento>;
	dia$: BehaviorSubject<string>;
	view$: BehaviorSubject<AgendaView>;
	filtroDia$: BehaviorSubject<AulaFiltro>;
	filtroSemana$: BehaviorSubject<AulaFiltro>;

	cards: AgendaMetaCards = null;

	get viewDia(): boolean {
		return this.view$.value === AgendaView.DIA;
	}

	get viewSemana(): boolean {
		return this.view$.value === AgendaView.SEMANA;
	}

	constructor() {
		this.agrupamento$ = new BehaviorSubject(AgendaDiaAgrupamento.SEM);
		const today = moment().format("YYYYMMDD");
		this.dia$ = new BehaviorSubject(today);
		let viewAgenda = localStorage.getItem("view-agenda");
		if (viewAgenda) {
			this.view$ = new BehaviorSubject(AgendaView[viewAgenda]);
		} else {
			this.view$ = new BehaviorSubject(AgendaView.DIA);
		}
		this.filtroDia$ = new BehaviorSubject(new AulaFiltro());
		this.filtroSemana$ = new BehaviorSubject(new AulaFiltro());
	}
}
