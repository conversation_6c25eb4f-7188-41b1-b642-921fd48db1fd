import { Injectable } from "@angular/core";
import {
	AulaFiltro,
	TurmaZW,
	AgendaView,
	TreinoApiTurmaService,
} from "treino-api";
import {
	AgendaDiaAgrupamento,
	AgendaTurmaState,
	Aula,
} from "./agenda-turma-state.model";
import { ResultadoPorDia } from "@base-core/rest/rest.model";
import { DiscreteGridEvent } from "../agenda-time-grid/DiscreteGridManager";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import { TurmaCardDetailComponent } from "../agenda-turma/turma-card-detail/turma-card-detail.component";
import { TurmaCardComponent } from "../agenda-turma/turma-card/turma-card.component";

import { debounceTime, map } from "rxjs/operators";
import {
	BehaviorSubject,
	Observable,
	Observer,
	merge,
	Subscription,
} from "rxjs";
import { FormControl } from "@angular/forms";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";
import { AgendaCardsStateService } from "./agenda-cards-state.service";

declare var moment;

export interface AgendaTurmaConfig {
	agrupamento: AgendaDiaAgrupamento;
	view: AgendaView;
	filtroDia: AulaFiltro;
	filtroSemana: AulaFiltro;
}

@Injectable()
export class AgendaTurmaStateService {
	private loading$: BehaviorSubject<boolean>;
	private state: AgendaTurmaState;
	private loadDataSubscription: Subscription;
	closeTooltip$: BehaviorSubject<any> = new BehaviorSubject(false);
	forceLoad$: BehaviorSubject<any> = new BehaviorSubject(false);
	private forceUpdate$: BehaviorSubject<any> = new BehaviorSubject(false);
	private update$: BehaviorSubject<boolean> = new BehaviorSubject(false);

	private turmaSelecionadaHandle: Observer<Aula>;
	modalidades: FormControl;

	constructor(
		private util: AgendaUtilsService,
		private utilWindow: WindowUtilService,
		private agendaService: AgendaCardsStateService,
		private turmasService: TreinoApiTurmaService
	) {
		this.loading$ = new BehaviorSubject(true);
		this.state = new AgendaTurmaState();
		this.eventSetup();
	}

	get view(): AgendaView {
		return this.state.view$.value;
	}

	get agrupamento(): AgendaDiaAgrupamento {
		return this.state.agrupamento$.value;
	}

	/**
	 * aaaammdd
	 */
	get dia(): string {
		return this.state.dia$.value;
	}

	get loading() {
		return this.loading$.value;
	}

	/**
	 * emite um evento toda vez que o estado é atualizado
	 */
	get stateUpdate$(): Observable<boolean> {
		return this.update$.asObservable();
	}

	get aulas(): ResultadoPorDia<Aula> {
		return this.state.turmas;
	}

	get filtros(): AulaFiltro {
		if (this.state.viewDia) {
			return this.state.filtroDia$.value;
		} else if (this.state.viewSemana) {
			return this.state.filtroSemana$.value;
		}
	}

	getTurmaById(id: number) {
		const turmas = this.state.turmas;
		let found;
		outer: for (const dia in turmas) {
			if (turmas.hasOwnProperty(dia)) {
				const turmasDia: Aula[] = turmas[dia];
				for (const horarioTurma of turmasDia) {
					if (horarioTurma.horarioTurmaId === id) {
						found = Object.assign({}, horarioTurma);
						break outer;
					}
				}
			}
		}
		return found;
	}

	private eventSetup() {
		const dataNeedsLoad$ = merge(
			this.state.dia$,
			this.state.filtroDia$,
			this.state.filtroSemana$,
			this.state.view$,
			this.forceLoad$
		);
		dataNeedsLoad$.pipe(debounceTime(25)).subscribe(() => {
			if (this.loadDataSubscription) {
				this.loadDataSubscription.unsubscribe();
			}
			this.loadDataSubscription = this.loadTurmas().subscribe();
		});

		merge(this.loading$, this.forceUpdate$).subscribe(() => {
			this.update$.next(this.loading);
		});
	}

	private loadTurmas(): Observable<boolean> {
		if (!this.loading) {
			this.loading$.next(true);
		}
		let dia = this.state.dia$.value;
		const viewDia = this.state.viewDia;

		/**
		 * View da semana usa segunda feira
		 * como dia de referencia.
		 */
		if (this.state.viewSemana) {
			const diaMoment = moment(dia, "YYYYMMDD");
			if (diaMoment.day() === 0) {
				diaMoment.day(-6);
			} else {
				diaMoment.day(1);
			}
			dia = diaMoment.format("YYYYMMDD");
		}

		const filtro = viewDia
			? this.state.filtroDia$.value
			: this.state.filtroSemana$.value;
		return this.turmasService.obterTurmas(dia, this.view, filtro).pipe(
			map((result) => {
				this.state.turmas = result;
				this.loading$.next(false);
				return true;
			})
		);
	}

	/**
	 *
	 * @param dia aaaammdd - 20190115 (15/01/2019)
	 */
	setDia(dia: string) {
		this.state.dia$.next(dia);
	}

	setModalidade(modalidadeId: number[]) {
		let source;
		if (this.state.viewDia) {
			source = Object.assign({}, this.state.filtroDia$.value);
		} else {
			source = Object.assign({}, this.state.filtroSemana$.value);
		}
		if (modalidadeId) {
			source.modalidadesIds = modalidadeId;
		} else {
			source.modalidadesIds = [];
		}
		this.state.filtroDia$.next(source);
		this.state.filtroSemana$.next(source);
	}

	setView(view: AgendaView) {
		this.state.view$.next(view);
		this.agendaService.setView(view);
	}

	setFiltro(filtro: AulaFiltro) {
		this.state.filtroDia$.next(filtro);
		this.state.filtroSemana$.next(filtro);
	}

	convertTurmaIntoGrid(
		aula: Aula,
		component,
		hoverComponent
	): DiscreteGridEvent {
		const eventStart = this.util.convertTimeLabelIntoMinutes(
			aula.horarioInicio
		);
		const eventEnd = this.util.convertTimeLabelIntoMinutes(aula.horarioFim);
		let eventDuration = eventEnd - eventStart;
		if (eventDuration <= 15) {
			eventDuration = 15;
		} else {
			eventDuration = this.utilWindow.roundToMultipleOf(eventDuration, 15);
		}
		const event = new DiscreteGridEvent({
			start: this.convertIntoUnit(aula.horarioInicio),
			height: eventDuration / 15,
			payload: aula,
			hoverComponent,
			component,
		});
		return event;
	}

	updateTurma(turma: TurmaZW) {
		this.state.updateTurma(turma);
		this.forceUpdate$.next(true);
	}

	updateAll() {
		this.forceLoad$.next(true);
		this.forceUpdate$.next(true);
	}

	private convertIntoUnit(time: string): number {
		const minutes = this.util.convertTimeLabelIntoMinutes(time);
		const roundedToMultiple = this.utilWindow.roundToMultipleOf(minutes, 15);
		return roundedToMultiple / 15;
	}

	startLoad() {
		this.loading$.next(true);
	}

	stopLoad() {
		this.loading$.next(false);
	}
}
