$grey-line: #dadada;
@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	height: 100px;
	display: flex;
	align-items: center;
}

.agenda-title {
	font-size: 28px;
	font-weight: 600;
	color: $pretoPri;
	margin-right: 15px;
}

.today {
	background-color: $branco;
	border: 1px solid $azulimPri;
	line-height: 34px;
	color: $azulimPri;
	text-align: center;
	margin-left: 30px;
	margin-right: 15px;
	padding: 3px 18px;
	cursor: pointer;
	font-weight: bold;
	border-radius: 4px;
	font-size: 14px;
	margin-right: 10px;
}

.time-controls {
	display: none;
	margin-right: 15px;
}

.agenda-icon {
	font-size: 20px;
	height: 30px;
	width: 30px;
	cursor: pointer;
	line-height: 36px;
	margin: 0px 4px;
	text-align: center;

	.pct {
		font-size: 24px;
		color: $pretoPri;
	}
}

.filler {
	flex-grow: 1;
}

.filter {
	display: block;
	margin: 0px 15px;
	max-width: 220px;

	&.view {
		width: 120px;
		min-width: 80px;
	}
}

@media (min-width: $bootstrap-breakpoint-xl) {
	.today {
		display: block;
	}
	.time-controls {
		display: flex;
	}
}
