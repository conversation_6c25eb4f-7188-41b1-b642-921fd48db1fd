<div class="agenda-title">{{ title }}</div>

<div
	(click)="goToTodayHandler()"
	class="today"
	i18n="@@agenda-aulas:hoje"
	id="visualizar-agenda-hoje">
	Hoje
</div>

<!-- CONTROLES DIA -->
<ng-container>
	<div class="time-controls">
		<div
			(click)="goBackwardsHandler(diaView ? 1 : 7)"
			class="agenda-icon"
			id="dia-anterior">
			<i class="pct pct-chevron-left"></i>
		</div>
		<div
			(click)="goFowardHandler(diaView ? 1 : 7)"
			class="agenda-icon"
			id="proximo-dia">
			<i class="pct pct-chevron-right"></i>
		</div>
	</div>
</ng-container>

<pacto-agenda-layout-v2-datepicker
	(dateSelect)="dateSelectHandler($event)"
	[dia]="dia"></pacto-agenda-layout-v2-datepicker>

<div class="filler"></div>

<pacto-cat-select
	[control]="viewFc"
	[id]="'filtro-periodo'"
	[items]="[
		{ id: 'DIA', label: 'Dia' },
		{ id: 'SEMANA', label: 'Semana' }
	]"
	class="filter view"></pacto-cat-select>
