import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Output,
	EventEmitter,
	Input,
	OnDestroy,
	OnChanges,
} from "@angular/core";
import { FormControl } from "@angular/forms";

import { Subscription } from "rxjs";
import { AgendaView } from "treino-api";
import { AgendaPeriodFilter } from "../agenda.model";

declare var moment;

@Component({
	selector: "pacto-agenda-layout-v2-header",
	templateUrl: "./agenda-layout-v2-header.component.html",
	styleUrls: ["./agenda-layout-v2-header.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaLayoutV2HeaderComponent
	implements OnInit, OnChanges, OnDestroy
{
	@Input() title: string;
	@Input() periodFilter: AgendaPeriodFilter = {
		date: moment().format("YYYYMMDD"),
		view: AgendaView.SEMANA,
	};
	@Output() periodFilterUpdate: EventEmitter<AgendaPeriodFilter> =
		new EventEmitter();

	viewFc = new FormControl();
	private viewChangeSubscription: Subscription;

	constructor() {}

	get diaView(): boolean {
		return this.periodFilter.view === AgendaView.DIA;
	}

	get dia() {
		const diaMoment = moment(this.periodFilter.date, "YYYYMMDD");
		return diaMoment.valueOf();
	}

	ngOnInit() {
		this.viewChangeSubscription = this.viewFc.valueChanges.subscribe((view) => {
			this.periodFilterUpdate.emit({
				view: view as AgendaView,
				date: this.periodFilter.date,
			});
		});
		this.viewFc.setValue(this.periodFilter.view, { emitEvent: false });
	}

	ngOnChanges() {
		this.viewFc.setValue(this.periodFilter.view, { emitEvent: false });
	}

	ngOnDestroy() {
		this.viewChangeSubscription.unsubscribe();
	}

	dateSelectHandler(event) {
		const diaMoment = moment(event);
		this.periodFilterUpdate.emit({
			view: this.periodFilter.view,
			date: diaMoment.format("YYYYMMDD"),
		});
	}

	goToTodayHandler() {
		const diaMoment = moment();
		const diaFormat = diaMoment.format("YYYYMMDD");
		this.periodFilterUpdate.emit({
			view: this.periodFilter.view,
			date: diaFormat,
		});
	}

	goFowardHandler(dias = 1) {
		const diaMoment = moment(this.periodFilter.date, "YYYYMMDD");
		diaMoment.add(dias, "days");
		this.periodFilterUpdate.emit({
			view: this.periodFilter.view,
			date: diaMoment.format("YYYYMMDD"),
		});
	}

	goBackwardsHandler(dias = 1) {
		const diaMoment = moment(this.periodFilter.date, "YYYYMMDD");
		diaMoment.subtract(dias, "days");
		this.periodFilterUpdate.emit({
			view: this.periodFilter.view,
			date: diaMoment.format("YYYYMMDD"),
		});
	}
}
