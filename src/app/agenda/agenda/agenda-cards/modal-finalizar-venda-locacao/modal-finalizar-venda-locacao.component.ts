import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	Optional,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmCoreApiVendaAvulsaService,
	ItemVendaAvulsa,
	VendaAvulsa,
} from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService } from "sdk";
import { SessionService } from "@base-core/client/session.service";
import { DetalhesLocacao } from "treino-api";
import { VendaAvulsaService } from "../../../../../../projects/adm/src/app/venda-avulsa/venda-avulsa.service";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-modal-finalizar-venda-locacao",
	templateUrl: "./modal-finalizar-venda-locacao.component.html",
	styleUrls: ["./modal-finalizar-venda-locacao.component.scss"],
})
export class ModalFinalizarVendaLocacaoComponent implements OnInit {
	@Input() detalheLocacaoFinanceiro: DetalhesLocacao;
	@Input() dtoMontado = null;

	constructor(
		private openModal: NgbActiveModal,
		private vendaAvulsaService: AdmCoreApiVendaAvulsaService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private service: VendaAvulsaService,
		private router: Router
	) {}

	ngOnInit() {}

	vender() {
		this.gravarVenda("vender");
	}

	receber() {
		this.gravarVenda("receber");
	}

	gravarVenda(acao) {
		this.vendaAvulsaService
			.save(this.dtoMontado === null ? this.montarDto() : this.dtoMontado)
			.subscribe(
				(venda) => {
					if (venda.error) {
						this.snotifyService.error(venda.error);
						return;
					}
					const id = venda.venda;
					this.snotifyService.success("Locação efetuada com sucesso!");
					this.cd.detectChanges();
					const usuarioOamd = this.sessionService.loggedUser.username;
					const empresa = this.sessionService.empresaId;

					if (acao === "receber") {
						this.clientDiscoveryService
							.linkZw(usuarioOamd, empresa)
							.subscribe((result) => {
								window.open(result + "&urlRedirect=pagamentoVenda_" + id);
								this.openModal.close();
							});
					} else {
						this.openModal.close();
						return this.router.navigate(["/agenda/painel/cards"]);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else {
						this.snotifyService.error(
							"Ocorreu um erro inesperado, tente novamente."
						);
					}
				}
			);
	}

	montarDto(): VendaAvulsa {
		const detalhe = this.detalheLocacaoFinanceiro;

		return {
			pessoa: detalhe.codigoPessoaClienteZW
				? detalhe.codigoPessoaClienteZW
				: null,
			nomeComprador: detalhe.aluno ? detalhe.aluno.nome : "",
			tipo: 0,
			lancamento: Date.now(),
			parcelas: 1,
			primeiraParcela: null,
			colaborador: false,
			descontoGeral: 0,
			itens: this.itens,
		};
	}

	get itens(): Array<ItemVendaAvulsa> {
		const detalhe = this.detalheLocacaoFinanceiro;
		const itensVenda: Array<ItemVendaAvulsa> = [];

		// Adiciona os produtos obrigatórios
		if (detalhe.horariosAdicionados) {
			detalhe.horariosAdicionados.forEach((horario) => {
				itensVenda.push({
					codigoProduto: detalhe.codigoProduto || 0,
					precoProduto: horario.valorTotal || 0,
					qtd: 1,
					valorParcial: horario.valorTotal || 0,
					descontoManual: 0,
					pontos: 0,
					descricaoProduto: detalhe.produto || "",
					pacoteEscolhido: null,
				});
			});
		}

		return itensVenda;
	}
}
