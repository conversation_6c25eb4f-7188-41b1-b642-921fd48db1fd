@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

$borda-padrao: 1px solid #eff1f7;
.agenda-container {
	overflow: hidden;
	width: 100%;
	height: 100%;
	position: relative;

	.breadcrumbs {
		margin: 24px;
	}

	&.semana-view {
		.slot-dia.selected {
			.diaSemana,
			.diaMes,
			.fechado {
				color: #1998fc;
			}
		}
	}

	&.servico-view {
		.diaSemana {
			padding: 16px 0px;
			font-size: 18px;
			font-weight: 600;
		}

		.diaMes {
			display: none;
		}
	}

	&.ambiente-view {
		.diaSemana {
			padding: 16px 0px;
			font-size: 18px;
			font-weight: 600;
		}

		.diaMes {
			display: none;
		}
	}

	&.modalidade-view {
		.diaSemana {
			padding: 16px 0px;
			font-size: 18px;
			font-weight: 600;
		}

		.diaMes {
			display: none;
		}
	}

	&.dia-view {
		.grid-header {
			grid-template-columns: 48px 1fr;
		}

		.agenda-scroll-container {
			.agenda-cards {
				.linha-agenda {
					grid-template-columns: 48px 1fr;
				}
			}
		}
	}

	.grid-header {
		display: grid;
		grid-template-columns: 48px repeat(7, 1fr);

		&.grid6 {
			grid-template-columns: 48px repeat(6, 1fr);
		}

		&.grid5 {
			grid-template-columns: 48px repeat(5, 1fr);
		}

		&.grid4 {
			grid-template-columns: 48px repeat(4, 1fr);
		}

		&.grid3 {
			grid-template-columns: 48px repeat(3, 1fr);
		}

		&.grid2 {
			grid-template-columns: 48px repeat(2, 1fr);
		}

		&.grid1 {
			grid-template-columns: 48px 1fr;
		}
	}

	.header-agenda-turma {
		box-shadow: 0 4px 4px 0 #0000000f;
		position: relative;
		z-index: 2;

		&.cabecalhoFechado {
			.fechado {
				display: block;
			}

			.diaMes,
			.diaSemana {
				display: none;
			}

			.liga_desliga {
				.pct-chevron-up {
					display: none;
				}

				.pct-chevron-down {
					display: inherit;
				}
			}
		}

		.fechado {
			display: none;
			font-size: 16px;
			line-height: 16px;
			text-align: center;
			padding: 8px;
			font-weight: 600;
		}

		.liga_desliga {
			.pct-chevron-down {
				display: none;
			}

			text-align: center;
			position: absolute;
			bottom: -24px;
			left: calc(50% + 20px);
			cursor: pointer;
			transform: translate(-50%, -50%);
			background: #ffffff;
			box-shadow: 0 2px 2px 0 #e4e5e6;
			width: 50px;
			height: 18px;
			border-radius: 0 0 8px 8px;
			color: #366ae2;
			line-height: 16px;

			i {
				font-size: 16px;
			}
		}

		&.fixo {
			position: absolute;
			top: 0;
			left: 24px;
			width: calc(100% - 59px);
			background-color: #ffffff;
			padding: 0 16px;

			&.hidden {
				display: none;
			}

			&.exibirNavegacaoColunas {
				.navegacaoColunas,
				.slot-navegacao {
					display: block;
				}

				.grid-header {
					grid-template-columns: 48px repeat(7, 1fr) 50px;
				}

				.linha-agenda {
					width: calc(100% - 50px);
				}
			}
		}

		.slot-dia {
			&:last-child {
				.divisorias {
					border-right: $borda-padrao;
				}
			}

			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			text-align: center;
			padding: 8px;
			position: relative;

			.diaMes {
				font-size: 32px;
				font-style: normal;
				font-weight: 600;
				line-height: 100%; /* 32px */
				padding: 8px;
			}

			.divisorias {
				border-left: $borda-padrao;
				border-bottom: $borda-padrao;
				width: 100%;
				height: 40%;
				position: absolute;
				bottom: 0;
				left: 0;
			}
		}
	}

	.agenda-scroll-container {
		overflow-y: auto;
		overflow-x: hidden;
		height: 100%;
		position: relative;

		.agenda-cards {
			.content-agenda {
				padding: 0 16px 16px 16px;
				min-height: calc(100vh - 302px);
			}

			&.exibirNavegacaoColunas {
				.navegacaoColunas,
				.slot-navegacao {
					display: block;
				}

				.grid-header {
					grid-template-columns: 48px repeat(7, 1fr) 50px;
				}

				.linha-agenda {
					width: calc(100% - 50px);
				}
			}

			background-color: #ffffff;
			color: #797d86;
			margin: 24px;
			border-radius: 6px;

			.slot-navegacao {
				display: none;
			}

			.slot-hora,
			.slot-navegacao {
				width: 48px;
				font-size: 12px;
				font-weight: 400;
				line-height: 16px;
				padding: 8px;
			}

			.linha-agenda {
				&.primeira-linha {
					border-top: none;
				}

				border-top: $borda-padrao;
				border-right: $borda-padrao;
				display: grid;
				grid-template-columns: 48px repeat(7, 1fr);

				&.grid6 {
					grid-template-columns: 48px repeat(6, 1fr);
				}

				&.grid5 {
					grid-template-columns: 48px repeat(5, 1fr);
				}

				&.grid4 {
					grid-template-columns: 48px repeat(4, 1fr);
				}

				&.grid3 {
					grid-template-columns: 48px repeat(3, 1fr);
				}

				&.grid2 {
					grid-template-columns: 48px repeat(2, 1fr);
				}

				&.grid1 {
					grid-template-columns: 48px 1fr;
				}

				.slot-dia {
					border-left: $borda-padrao;
					text-transform: capitalize;
					padding: 8px;
				}
			}
		}
	}
}

::ng-deep .modal-agenda-turma {
	.pacto-modal-wrapper .modal-titulo {
		.title {
			font-size: 16px;
			font-weight: 600;
		}

		.close-wrapper {
			top: 18px !important;
		}

		padding: 16px !important;
		line-height: 16px !important;
	}
}

::ng-deep .modal-agenda-turma .modal-dialog {
	width: 1000px !important;
	max-width: 1000px !important;
	height: 570px !important;
}

.hidden {
	display: none;
}

.content-filtros {
	margin-top: 16px;
	z-index: 3;
	position: relative;
}

.aviso-beta {
	align-items: center;
	display: flex;

	i {
		margin-right: 5px;
	}

	a {
		margin-left: 8px;
		color: #1e60fa;
		cursor: pointer;
		font-weight: 600;
		padding: 8px 13px;
		border-radius: 4px;

		&:hover {
			background: #b4cafd;
		}
	}
}

.navegacaoColunas {
	&.inativo {
		cursor: auto;

		.pct {
			color: #a1a5aa;
		}
	}

	margin-top: 5px;
	display: none;
	width: 40px;
	height: 40px;
	cursor: pointer;
	line-height: 48px;
	border-radius: 4px;
	background-color: $branco;
	text-align: center;

	.pct {
		font-size: 24px;
		color: $azulim05;
	}
}
