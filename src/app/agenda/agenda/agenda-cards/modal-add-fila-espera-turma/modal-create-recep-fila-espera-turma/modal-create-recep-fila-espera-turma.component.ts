import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { Router } from "@angular/router";
import { LocalizationService } from "@base-core/localization/localization.service";
import { regex as regexRules } from "@base-core/utils/regex";
import { TreinoApiTurmaService } from "treino-api";

@Component({
	selector: "pacto-modal-create-recep-fila-espera-turma",
	templateUrl: "./modal-create-recep-fila-espera-turma.component.html",
	styleUrls: ["./modal-create-recep-fila-espera-turma.component.scss"],
})
export class CreateRecepFilaEsperaTurmaModalComponent implements OnInit {
	phoneMask;
	maxLength;
	alunoAdd = false;
	alunoError = false;
	codigoHorarioTurma;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	formGroup: FormGroup = new FormGroup({
		nomeCompleto: new FormControl(null),
		celular: new FormControl(null),
		email: new FormControl(null),
	});

	constructor(
		private notificationService: SnotifyService,
		private turmaService: TreinoApiTurmaService,
		private router: Router,
		private openModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private localization: LocalizationService
	) {
		const validators = [];
		if (this.localization.locale === "es") {
			this.maxLength = "13";
			validators.push(Validators.pattern(regexRules.telefoneArgentina));
		}
	}

	ngOnInit() {
		this.phoneMask = this.localization.getPhoneLocaleMask();
	}

	salvar() {
		this.turmaService
			.inserirNaFilaTurmaCrm(
				this.codigoHorarioTurma,
				null,
				JSON.stringify(this.formGroup.getRawValue()),
				null
			)
			.subscribe((response) => {
				if (!response.erro) {
					this.alunoAdd = true;
					this.alunoError = false;
					this.notificationService.success(
						this.notificacoesTranslate.getLabel("success")
					);
				} else {
					this.alunoAdd = false;
					this.alunoError = true;
					this.notificationService.error(response.erro);
				}
				this.cd.detectChanges();
			});
	}

	public cancelar() {
		this.openModal.close(true);
		this.router.navigate(["agenda", "painel", "cards"]);
	}

	novamente() {
		this.alunoAdd = false;
		this.alunoError = false;
		this.formGroup.get("nomeCompleto").setValue(null);
		this.formGroup.get("celular").setValue(null);
		this.formGroup.get("email").setValue(null);
	}

	closeModalCreateReceoFilaTurmaCrm() {
		this.openModal.close(true);
	}
}
