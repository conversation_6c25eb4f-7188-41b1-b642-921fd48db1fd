<div class="pacto-modal">
	<div class="modal-titulo">
		<div class="title">Fila de espera para turmas</div>
		<div
			(click)="closeModalCreateReceoFilaTurmaCrm()"
			class="close-modal"
			id="manfse-close">
			<i class="pct pct-x" id="close-aba"></i>
		</div>
	</div>
	<div *ngIf="!alunoAdd && !alunoError" class="wrapper">
		<div class="row">
			<div class="col-md-12">
				<pacto-cat-form-input
					[control]="formGroup.get('nomeCompleto')"
					[errorMsg]="'Nome � obrigat�rio'"
					[id]="'input-nome'"
					[label]="'Nome Completo*'"
					[placeholder]="'Nome completo'"></pacto-cat-form-input>
			</div>
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="formGroup.get('celular')"
					[maxlength]="maxLength"
					[textMask]="
						phoneMask ? { mask: phoneMask, guide: false } : { mask: false }
					"
					i18n-label="@@integracao-pago-livre:label-telefone"
					i18n-placeholder="@@integracao-pago-livre:placeholder-telefone"
					id="celular"
					label="Celular*"
					placeholder="Celular"></pacto-cat-form-input>
			</div>
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="formGroup.get('email')"
					[errorMsg]="'E-mail � obrigat�rio'"
					[id]="'input-username'"
					[label]="'E-mail*'"
					[placeholder]="'E-mail'"></pacto-cat-form-input>
			</div>
		</div>
		<div class="actions">
			<pacto-cat-button
				(click)="cancelar()"
				[full]="true"
				[label]="'Voltar'"
				size="MEDIUM"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvar()"
				[full]="true"
				[label]="'Cadastrar e adicionar na fila de espera'"
				size="MEDIUM"></pacto-cat-button>
		</div>
	</div>
	<div *ngIf="alunoAdd || alunoError" class="wrapper align-center">
		<div *ngIf="alunoAdd && !alunoError" class="fila-sucess">
			<img src="pacto-ui/images/empty-state-turma.svg" />
			<div class="titulo">Aluno acrescentado na fila de espera!</div>
			<div>Iremos notificar assim que surgir a vaga na turma desejada.</div>
		</div>
		<div *ngIf="!alunoAdd && alunoError">
			<img src="assets/images/empty-state-sirene.svg" />
			<div class="aluno-error-text">
				Nao foi possivel acrescentar o aluno na fila de espera!
			</div>
			<div class="aluno-error-button">
				<pacto-cat-button
					(click)="novamente()"
					[full]="true"
					[label]="'Tentar novamente'"
					size="LARGE"></pacto-cat-button>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@add-fila-turma:success" xingling="success">
		Adicionado na fila com sucesso.
	</span>
</pacto-traducoes-xingling>
