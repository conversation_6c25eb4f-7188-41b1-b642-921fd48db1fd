@import "../../../../../../../projects/ui/assets/import";
@import "../../../../../../../projects/ui/assets/ds3/colors.var";
@import "../../../../../../../projects/ui/assets/ds3/fonts/fonts";

@media (min-width: 1050px) {
	::ng-deep .modal-integracao .modal-lg {
		max-width: 1050px;
	}
}

.pacto-modal {
	.modal-titulo {
		@extend .type-h6-bold;
		padding: 32px 32px 16px 32px;
		position: relative;
		line-height: 20px;
		color: $pretoPri;
		font-weight: 700;
		border-bottom: 0.5px solid $pretoPri;

		.title {
			line-height: 16px;
		}

		.close-modal {
			position: absolute;
			right: 26px;
			top: 26px;
			font-size: 20px;
			color: $pretoPri;
			cursor: pointer;
		}
	}
}

::ng-deep.nome.ng-star-inserted {
	color: #a6aab1 !important;
	font-weight: 400 !important;
	font-size: 14px !important;
	line-height: 2em !important;
}

::ng-deep#input-nome {
	margin-bottom: 8px !important;
}

.cad-aluno {
	text-align: right;
	width: 45%;
	display: inline-block;
}

pacto-cat-form-input,
pacto-cat-form-input-number {
	margin: 0px;
}

.wrapper {
	padding: 10px 30px 30px 30px;
}

::ng-deep.title.ng-star-inserted {
	line-height: 22px !important;
	font-size: 14px !important;
	color: #51555a !important;
	font-weight: 600 !important;
}

.btns-ativar-inativar {
	margin-top: 38px;
}

::ng-deep.modal-time-line {
	.modal-dialog {
		max-width: 78%;
	}
}

.margin-left-13 {
	margin-left: 13px;
}

.margin-left-3 {
	margin-left: 3px;
}

.todosDiasRight {
	flex: 1 0 16.66666667%;
	max-width: 23%;
	text-align: right;
}

.top10-border-bottom {
	margin-top: 10px;
	border-bottom: 1px solid #dcdddf;
}

.right5 {
	margin-right: 5px;
}

.width100 {
	width: 100%;
}

.top20 {
	margin-top: 20px;
}

.margin-top-40 {
	margin-top: 40px;
}

.cadastrar-ambiente {
	color: #0a64ff;
	font-size: 14px;
	font-weight: bold;
}

.actions {
	display: flex;
	justify-content: right;
	margin-top: 10px;

	pacto-cat-button {
		min-width: 100px;
		margin: 1rem 0rem 0rem 1rem;
	}
}

::ng-deep.ds3-button.ds3-text-button.ds3-btn-primary,
button.ds3-button.ds3-text-button.ds3-btn-primary {
	font-size: 13px !important;
	background-color: #ffffff !important;
	background: #ffffff !important;
}

.align-center {
	text-align: center !important;
}

.fila-sucess {
	.titulo {
		font-size: 16px;
		font-weight: 700;
		line-height: 18px;
		letter-spacing: 0.25px;
		color: #878a92;
		margin-top: 10px;
	}
}

.aluno-error-text {
	margin-top: 10px;
}

.aluno-error-button {
	margin-top: 10px !important;
	width: 50% !important;
	display: inline-block;
}
