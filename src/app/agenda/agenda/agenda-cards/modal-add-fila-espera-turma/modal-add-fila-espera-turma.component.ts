import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { Router } from "@angular/router";
import { CreateRecepFilaEsperaTurmaModalComponent } from "./modal-create-recep-fila-espera-turma/modal-create-recep-fila-espera-turma.component";
import { TreinoApiTurmaService } from "treino-api";

@Component({
	selector: "pacto-modal-add-fila-espera-turma",
	templateUrl: "./modal-add-fila-espera-turma.component.html",
	styleUrls: ["./modal-add-fila-espera-turma.component.scss"],
})
export class AddFilaEsperaTurmaModalComponent implements OnInit {
	alunoAdd = false;
	alunoError = false;
	codigoHorarioTurma;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	formGroup: FormGroup = new FormGroup({
		aluno: new FormControl(null),
	});

	constructor(
		private notificationService: SnotifyService,
		private rest: RestService,
		private router: Router,
		private openModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private ngbModal: NgbModal,
		private turmaService: TreinoApiTurmaService
	) {}

	ngOnInit() {}

	alunoParamBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	get alunoUrl() {
		return this.rest.buildFullUrl("alunos/fila-espera");
	}

	resposeParser = (result) => result.content;

	salvar() {
		this.turmaService
			.inserirNaFilaTurmaCrm(
				this.codigoHorarioTurma,
				this.formGroup.get("aluno").value.codigoCliente,
				null,
				this.formGroup.get("aluno").value.codigoPassivo
			)
			.subscribe((response) => {
				if (!response.erro) {
					this.alunoAdd = true;
					this.alunoError = false;
					this.notificationService.success(
						this.notificacoesTranslate.getLabel("success")
					);
				} else {
					this.alunoAdd = false;
					this.alunoError = true;
					this.notificationService.error(response.erro);
				}
				this.cd.detectChanges();
			});
	}

	novamente() {
		this.alunoAdd = false;
		this.alunoError = false;
		this.formGroup.get("alunoId").setValue(null);
	}

	public cancelar() {
		this.openModal.close(true);
		this.router.navigate(["agenda", "painel", "cards"]);
	}

	openModalCadReceptivoFilaTurma() {
		const ref = this.ngbModal.open(CreateRecepFilaEsperaTurmaModalComponent, {
			centered: true,
			size: "lg",
			windowClass: "modal-action-nfse-component",
		});
		ref.componentInstance.codigoHorarioTurma = this.codigoHorarioTurma;
		ref.result.then(
			(dto) => {
				this.openModal.close(true);
			},
			() => {}
		);
	}

	closeModalAddFilaTurmaCrm() {
		this.openModal.close(true);
	}
}
