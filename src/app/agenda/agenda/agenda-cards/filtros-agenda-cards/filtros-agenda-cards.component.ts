import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	OnChanges,
	Output,
	EventEmitter,
	ViewChild,
	ChangeDetectorRef,
	Input,
} from "@angular/core";
import { debounceTime, map } from "rxjs/operators";
import { FormControl } from "@angular/forms";

import {
	AgendaView,
	Ambiente,
	TreinoApiAmbienteService,
	TreinoApiColaboradorService,
	TreinoApiAlunosService,
	TreinoApiModalidadeService,
	TipoAula,
	AulaFiltro,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	SelectFilterResponseParser,
	SelectFilterParamBuilder,
	GridFilterConfig,
	FilterComponent,
	GridFilterType,
} from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { TurmaFilterModalComponent } from "../../agenda-turma/turma-filter-modal/turma-filter-modal.component";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { Observable, zip } from "rxjs";

declare var moment;

export enum AGENDA_VIEW {
	AULAS = "AULAS",
	AGENDAMENTOS = "AGENDAMENTOS",
	PROFESSOR = "PROFESSOR",
}

@Component({
	selector: "pacto-filtros-agenda-cards",
	templateUrl: "./filtros-agenda-cards.component.html",
	styleUrls: ["./filtros-agenda-cards.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FiltrosAgendaCardsComponent implements OnInit, OnChanges {
	@Input() filtros: AulaFiltro;

	@ViewChild("filtro", { static: false })
	filtro: FilterComponent;

	@ViewChild("traducoes", { static: true }) traducoes;

	tipoModalidadeFc: FormControl = new FormControl();
	professorFc = new FormControl();
	viewControl = new FormControl();
	turnoControl = new FormControl("todos");
	tipoControl = new FormControl("TODAS");
	searchControl = new FormControl("");
	agrupamentoControl = new FormControl();
	@Output() calendarState: EventEmitter<boolean> = new EventEmitter();
	searchOpen: boolean = false;

	ambientes: ApiResponseList<Ambiente> = {
		content: [],
	};
	professores: ApiResponseList<any> = {
		content: [],
	};
	alunos: ApiResponseList<any> = {
		content: [],
	};
	modalidades: ApiResponseList<any> = {
		content: [],
	};

	constructor(
		private router: Router,
		private session: SessionService,
		private modal: ModalService,
		private rest: RestService,
		private state: AgendaCardsStateService,
		private ambienteService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private alunoService: TreinoApiAlunosService,
		private modalidadeService: TreinoApiModalidadeService,
		private cd: ChangeDetectorRef
	) {}

	filterConfig: GridFilterConfig = {
		filters: [
			{
				name: "ambientes",
				label: "Ambiente",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "professores",
				label: "Professor",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "alunos",
				label: "Aluno",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "disponibilidades",
				label: "Disponibilidade",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "duracoes",
				label: "Duração",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "tiposAgendamento",
				label: "Tipo de Agendamento",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "modalidades",
				label: "Modalidade",
				type: GridFilterType.MANY,
				options: [],
			},
			{
				name: "situacaoHorario",
				label: "Situação Horário",
				type: GridFilterType.MANY,
				options: [],
				initialValue: ["AT"],
			},
		],
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	get _rest() {
		return this.rest;
	}

	get semanaView() {
		return this.state.view === AgendaView.SEMANA;
	}

	get diaView() {
		return this.state.view === AgendaView.DIA;
	}

	get dia(): number {
		const diaMoment = moment(this.state.dia, "YYYYMMDD");
		return diaMoment.valueOf();
	}

	openCalendarHandler(opened) {
		this.calendarState.emit(opened);
	}

	ngOnInit() {
		// zip(this.getAmbientes(),
		//     this.getProfessores(),
		//     this.getAlunos(),
		//     this.getModalidades()
		// ).subscribe(resultado => {
		//   this.configFilters();
		//   this.cd.detectChanges();
		// });
		zip(this.getProfessores(), this.getModalidades()).subscribe((resultado) => {
			this.configFilters();
			this.cd.detectChanges();
		});

		this.updateFormControls();

		if (this.state.search) {
			this.searchControl = this.state.search;
		}

		this.searchControl.valueChanges
			.pipe(debounceTime(500))
			.subscribe((value) => {
				this.state.search = this.searchControl;
				this.state.setSearch(value);
			});

		if (this.state.turno) {
			this.turnoControl = this.state.turno;
		}

		if (this.state.search) {
			this.searchControl = this.state.search;
			if (this.searchControl.value !== "") {
				this.searchOpen = true;
			}
		}

		this.turnoControl.valueChanges.subscribe((value) => {
			this.state.turno = this.turnoControl;
			this.state.setTurno(value);
		});

		if (this.state.tipo) {
			this.tipoControl = this.state.tipo;
		}

		this.tipoControl.valueChanges.subscribe((value) => {
			this.state.tipo = this.tipoControl;
			this.state.setTipo(value);
		});

		if (this.state.modalidades) {
			this.tipoModalidadeFc = this.state.modalidades;
		}

		this.tipoModalidadeFc.valueChanges.subscribe((value) => {
			this.state.modalidades = this.tipoModalidadeFc;
			this.state.setModalidade(this.getModalidadeIds(value));
		});

		this.viewControl.valueChanges.subscribe((value) => {
			if (value === "SERVICO") {
				this.tipoControl.setValue("SERVICO");
			}
			this.state.setView(value);
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: this.addFilter(),
		};
	}

	private addFilter() {
		return [
			//     {
			//   name: 'ambientes',
			//   label: 'Ambiente',
			//   type: GridFilterType.MANY,
			//   options: this.ambientes.content
			// },
			{
				name: "professores",
				label: "Professor",
				type: GridFilterType.MANY,
				options: this.professores.content,
			},
			// {
			//   name: 'alunos',
			//   label: 'Aluno',
			//   type: GridFilterType.MANY,
			//   options: this.alunos.content
			// },
			// {
			//   name: 'disponibilidades',
			//   label: 'Disponibilidade',
			//   type: GridFilterType.MANY,
			//   options: [
			//     {value: 1, label: 'Disponível'},
			//     {value: 2, label: 'Indisponível'}
			//   ]
			// },
			// {
			//   name: 'duracoes',
			//   label: 'Duração',
			//   type: GridFilterType.MANY,
			//   options: [
			//     {value: 1, label: 'Livre'},
			//     {value: 2, label: 'Pré-definido'},
			//     {value: 3, label: 'Play'},
			//     {value: 4, label: 'Intervalo de tempo'}
			//   ]
			// },
			{
				name: "modalidades",
				label: "Modalidade",
				type: GridFilterType.MANY,
				options: this.modalidades.content,
			},
			{
				name: "situacaoHorario",
				label: "Situação Horário",
				type: GridFilterType.MANY,
				options: [
					{ value: "AT", label: "Ativo" },
					{ value: "IN", label: "Inativo" },
				],
				initialValue: ["AT"],
			},
		];
	}

	private getAmbientes(): Observable<any> {
		return this.ambienteService.obterTodosAmbientes().pipe(
			map((result) => {
				result.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientes = result;
			})
		);
	}

	private getProfessores(): Observable<any> {
		return this.colaboradorService
			.obterTodosColaboradoresAptosAAula(false)
			.pipe(
				map((result) => {
					result.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = result;
				})
			);
	}

	private getModalidades(): Observable<any> {
		return this.modalidadeService.obterTodasModalidades().pipe(
			map((result) => {
				result.content.forEach((modalidade: any) => {
					modalidade.value = modalidade.id;
					modalidade.label = modalidade.nome;
				});
				this.modalidades = result;
			})
		);
	}

	private getAlunos(): Observable<any> {
		return this.alunoService.obterTodosAlunos().pipe(
			map((result) => {
				result.content.forEach((aluno: any) => {
					aluno.value = aluno.id;
					aluno.label = aluno.nome;
				});
				this.alunos = result;
			})
		);
	}

	onSearch(fil) {
		const filtro = Object.assign({}, this.filtros);
		filtro.tipo = this.getTipoAula(fil.filters.ambientes);
		filtro.professoresIds = fil.filters.professores;
		filtro.ambientesIds = fil.filters.ambientes;
		filtro.alunosIds = fil.filters.alunos;
		filtro.tipoDuracao = fil.filters.duracoes;
		filtro.disponibilidade = fil.filters.disponibilidades;
		filtro.modalidadesIds = fil.filters.modalidades;
		filtro.situacaoHorario = fil.filters.situacaoHorario;
		this.state.setFiltro(filtro);
	}

	private getTipoAula(value: string): TipoAula {
		if (value === TipoAula.TODAS) {
			return TipoAula.TODAS;
		} else if (value === TipoAula.TURMA_ZW) {
			return TipoAula.TURMA_ZW;
		} else if (value === TipoAula.AULA_COLETIVA) {
			return TipoAula.AULA_COLETIVA;
		}
	}

	ngOnChanges() {
		this.updateFormControls();
	}

	private updateFormControls() {
		this.viewControl.setValue(this.state.view, { emitEvent: false });
		this.agrupamentoControl.setValue(this.state.agrupamento, {
			emitEvent: false,
		});
	}

	openDatepickerHandler(open) {
		this.calendarState.emit(open);
	}

	dateSelectHandler(event) {
		const diaMoment = moment(event);
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	filterOpenHandler() {
		const modal = this.modal.open(
			this.traducoes.getLabel("filtrosAvancados"),
			TurmaFilterModalComponent
		);
		const filters: TurmaFilterModalComponent = modal.componentInstance;
		filters.filtros = this.state.filtros;
		modal.result.then(
			(result) => {
				this.state.setFiltro(result);
			},
			() => {}
		);
	}

	goBackHandler(days = 1) {
		const dia = this.dia;
		const diaMoment = moment(dia);
		diaMoment.subtract(days, "days");
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	goFowardHandler(days = 1) {
		const dia = this.dia;
		const diaMoment = moment(dia);
		diaMoment.add(days, "days");
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	get pactobr(): boolean {
		return this.session.isUsuarioPacto;
	}

	beta() {
		try {
			this.session.notificarRecursoEmpresa(RecursoSistema.VOLTAR_AGENDA_ANTIGA);
		} catch (e) {}
		this.router.navigate(["agenda", "painel", "turmas"]);
	}

	goToTodayHandler() {
		const diaMoment = moment();
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	closeSearch() {
		this.searchOpen = !this.searchOpen;
		if (this.searchControl && this.searchControl.value !== "") {
			this.searchControl.setValue("");
		}
	}

	loadTurnoItens() {
		if (this.traducoes.loaded) {
			return [
				{ id: "todos", label: "Dia todo" },
				{ id: "MANHA", label: this.traducoes.getLabel("turnoManha") },
				{ id: "TARDE", label: this.traducoes.getLabel("turnoTarde") },
				{ id: "NOITE", label: this.traducoes.getLabel("turnoNoite") },
			];
		} else {
			return [
				{ id: "todos", label: "Dia todo" },
				{ id: "MANHA", label: "Manhã" },
				{ id: "TARDE", label: "Tarde" },
				{ id: "NOITE", label: "Noite" },
			];
		}
	}

	loadTipoItens() {
		if (this.viewControl && this.viewControl.value === "SERVICO") {
			if (this.traducoes.loaded) {
				return [
					{ id: "SERVICO", label: this.traducoes.getLabel("tipoServico") },
				];
			} else {
				return [{ id: "SERVICO", label: "Serviço" }];
			}
		}

		if (this.traducoes.loaded) {
			return [
				{ id: "TODAS", label: "Todos" },
				{ id: "TURMA_AULA", label: this.traducoes.getLabel("tipoTurmaAula") },
				{ id: "ZW", label: this.traducoes.getLabel("tipoTurma") },
				{ id: "AC", label: this.traducoes.getLabel("tipoAula") },
				{ id: "SERVICO", label: this.traducoes.getLabel("tipoServico") },
				{ id: "LOCACAO", label: this.traducoes.getLabel("tipoLocacao") },
			];
		} else {
			return [
				{ id: "TODAS", label: "Todos" },
				{ id: "TURMA_AULA", label: "Turma + Aula" },
				{ id: "ZW", label: "Turma" },
				{ id: "AC", label: "Aula" },
				{ id: "SERVICO", label: "Serviço" },
				{ id: "LOCACAO", label: "Locação" },
			];
		}
	}

	modalidadeSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	loadPeriodoItens() {
		if (this.traducoes.loaded) {
			return [
				{ id: "DIA", label: this.traducoes.getLabel("periodoDia") },
				{ id: "SEMANA", label: this.traducoes.getLabel("periodoSemana") },
				{ id: "AMBIENTE", label: this.traducoes.getLabel("ambiente") },
				{ id: "SERVICO", label: this.traducoes.getLabel("servico") },
				{ id: "MODALIDADE", label: this.traducoes.getLabel("modalidade") },
			];
		} else {
			return [
				{ id: "DIA", label: "Dia" },
				{ id: "SEMANA", label: "Semana" },
				{ id: "AMBIENTE", label: "Ambiente" },
				{ id: "SERVICO", label: "Serviço" },
				{ id: "MODALIDADE", label: "Modalidade" },
			];
		}
	}

	getModalidadeIds(value: Array<any>): number[] {
		// tslint:disable-next-line: prefer-const
		let ids: Array<number> = new Array<number>();
		if (value) {
			value.forEach((v) => {
				ids.push(v.id);
			});
			return ids;
		}
		return null;
	}

	get logUrl() {
		return this.rest.buildFullUrl(`log/agendamentos/`);
	}
}
