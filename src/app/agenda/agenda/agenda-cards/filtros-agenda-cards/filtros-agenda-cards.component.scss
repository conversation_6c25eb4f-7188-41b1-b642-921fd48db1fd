$grey-line: #dadada;
@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.agenda-title {
	@extend .type-h2-bold;
	color: $pretoPri;
	margin-right: 15px;
}

.icon-search {
	border-radius: 3px;
	border: 1px solid #bcbfc7;
	padding: 12px 14px 12px 15px;
	line-height: 28px;
	cursor: pointer;
}

.semana-control-wrapper {
	display: flex;
	border-bottom: 1px solid $cinza01;
	height: 80px;

	.titulo-aulas {
		display: flex;
		align-items: center;
	}

	.filter {
		margin: 0px 8px 0px 0px;
	}

	.select-modalidades {
		pacto-cat-multi-select-filter-number::ng-deep,
		button {
			top: 20px;
			position: relative;
		}
	}
}

.today {
	background-color: $branco;
	border: 1px solid $geloPri;
	line-height: 34px;
	color: $azulimPri;
	text-align: center;
	padding: 0px 15px;
	cursor: pointer;
	@extend .type-btn-bold;
	margin: 0px 15px;
	display: none;
	@media (min-width: $bootstrap-breakpoint-lg) {
		display: block;
	}
}

.time-controls {
	display: flex;
	margin: 0px 15px;
}

.agenda-icon {
	&.filtro {
		margin-right: 16px;
		line-height: 44px;

		.pct {
			font-size: 16px;
		}
	}

	width: 40px;
	height: 40px;
	cursor: pointer;
	line-height: 48px;
	border-radius: 4px;
	background-color: $branco;
	border: 1px solid $azulim05;
	margin: 0px 8px;
	text-align: center;

	.pct {
		font-size: 24px;
		color: $azulim05;
	}
}

.filler {
	flex-grow: 1;
}

.filtersModal {
	font-size: 20px;
	color: $pretoPri;
	cursor: pointer;
	position: relative;
	top: 0px;
}

.filter {
	display: block;
	margin: 0px 15px;

	&.modalidade {
		width: 260px;
	}
}

.filtros-agenda-geral {
	margin-right: 16px;

	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}
}

pacto-cat-select {
	::ng-deep.pacto-label {
		line-height: 1.5;
	}
}
