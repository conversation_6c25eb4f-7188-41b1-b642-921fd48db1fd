<div class="semana-control-wrapper">
	<div class="titulo-aulas">
		<div
			(click)="goToTodayHandler()"
			class="today"
			i18n="@@agenda-aulas:hoje"
			id="visualizar-agenda-hoje">
			{{ semanaView ? "Semana atual" : "Dia atual" }}
		</div>
		<ng-container>
			<div class="time-controls">
				<div
					(click)="goBackHandler(semanaView ? 7 : 1)"
					class="agenda-icon"
					id="dia-anterior">
					<i class="pct pct-chevron-left"></i>
				</div>
				<pacto-agenda-layout-v2-datepicker
					(dateSelect)="dateSelectHandler($event)"
					(datepickerOpen)="openCalendarHandler($event)"
					[dia]="dia"></pacto-agenda-layout-v2-datepicker>
				<div
					(click)="goFowardHandler(semanaView ? 7 : 1)"
					class="agenda-icon"
					id="proximo-dia">
					<i class="pct pct-chevron-right"></i>
				</div>
				<pacto-log [url]="logUrl"></pacto-log>
			</div>
		</ng-container>
	</div>

	<div class="filler"></div>

	<div class="select-modalidades">
		<button
			(click)="beta()"
			*ngIf="pactobr"
			class="btn btn-secondary beta filter"
			title="Ir para uma versão nova da agenda">
			Voltar para versão antiga
		</button>
	</div>
	<div class="titulo-aulas">
		<pacto-cat-select
			[control]="tipoControl"
			[id]="'filtro-tipo'"
			[items]="loadTipoItens()"
			class="filter view"
			i18n-label="@@agenda-aulas:labelTipo"></pacto-cat-select>
	</div>

	<div class="titulo-aulas">
		<pacto-cat-select
			[control]="turnoControl"
			[id]="'filtro-turno'"
			[items]="loadTurnoItens()"
			class="filter view"
			i18n-label="@@agenda-aulas:labelTurno"></pacto-cat-select>
	</div>

	<div class="titulo-aulas">
		<pacto-cat-select
			[control]="viewControl"
			[id]="'filtro-periodo'"
			[items]="loadPeriodoItens()"
			class="filter view"
			i18n-label="@@agenda-aulas:labelPeriodo"></pacto-cat-select>

		<div (click)="closeSearch()" *ngIf="!searchOpen" class="filter">
			<i class="pct pct-search icon-search" title="Consultar"></i>
		</div>
		<div *ngIf="searchOpen" class="filter" style="width: 300px">
			<pacto-cat-form-input
				(iconClick)="closeSearch()"
				[control]="searchControl"
				[enableClearInput]="false"
				[icon]="'x'"
				i18np-placeholder="@@crud-turmas:input:nome:placeholder"
				placeholder="Busca Rápida..."></pacto-cat-form-input>
		</div>

		<div class="filter filtros-agenda-geral">
			<pacto-filter
				#filtro
				(filterChange)="onSearch($event)"
				[filterConfig]="filterConfig"></pacto-filter>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@agenda-aulas:periodoDia" xingling="periodoDia">Dia</span>
	<span i18n="@@agenda-aulas:periodoSemana" xingling="periodoSemana">
		Semana
	</span>
	<span i18n="@@agenda-aulas:servicos" xingling="servico">Serviço</span>
	<span i18n="@@agenda-aulas:modalidade" xingling="modalidade">Modalidade</span>
	<span i18n="@@agenda-aulas:ambiente" xingling="ambiente">Ambiente</span>
	<span i18n="@@agenda-aulas:manha" xingling="turnoManha">Manhã</span>
	<span i18n="@@agenda-aulas:tarde" xingling="turnoTarde">Tarde</span>
	<span i18n="@@agenda-aulas:noite" xingling="turnoNoite">Noite</span>
	<span i18n="@@agenda-aulas:tipoTurmaAula" xingling="tipoTurmaAula">
		Turma + Aula
	</span>
	<span i18n="@@agenda-aulas:tipoTurma" xingling="tipoTurma">Turma</span>
	<span i18n="@@agenda-aulas:tipoAula" xingling="tipoAula">Aula</span>
	<span i18n="@@agenda-aulas:tipoServico" xingling="tipoServico">Serviço</span>
	<span i18n="@@agenda-aulas:tipoLocacao" xingling="tipoLocacao">Locação</span>
	<span i18n="@@agenda-aulas:tituloFiltroAvancado" xingling="filtrosAvancados">
		Filtros Avançados
	</span>
</pacto-traducoes-xingling>
