import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import {
	AgendaAgendamento,
	AgendaCard,
	AgendaView,
	PerfilAcessoRecursoNome,
	TreinoApiAgendamentoService,
} from "treino-api";
import { Router } from "@angular/router";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { SessionService } from "@base-core/client/session.service";
import {
	AgendaServicoAgendamentoFilter,
	AgendaServicosStateService,
} from "../../agenda-servicos/agenda-servicos-state.service";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { AgendamentoEditModalComponent } from "../../agenda-servicos/agendamento-edit-modal/agendamento-edit-modal.component";
import { AgendaEvento } from "../../agenda-layout-v2/agenda-layout-v2.component";
import { AgendamentoCardComponent } from "../../agenda-servicos/agendamento-card/agendamento-card.component";
import { AgendamentoHoverCardComponent } from "../../agenda-servicos/agendamento-hover-card/agendamento-hover-card.component";
import { AgendamentoModalCardComponent } from "../../agenda-servicos/agendamento-modal-card/agendamento-modal-card.component";
import { ModalService } from "@base-core/modal/modal.service";
import { Subscription } from "rxjs";
import { AgendaTimeGridStateService } from "../../agenda-time-grid/agenda-time-grid-state.service";

@Component({
	selector: "pacto-card-servico",
	templateUrl: "./card-servico.component.html",
	styleUrls: ["./card-servico.component.scss"],
})
export class CardServicoComponent implements OnInit {
	@Input() servico: AgendaCard;
	private fetchDataSubscription: Subscription;
	eventos: AgendaEvento[][] = [];

	loading = false;

	constructor(
		private agendaServiceState: AgendaServicosStateService,
		private session: SessionService,
		private rest: RestService,
		private snotify: SnotifyService,
		private cd: ChangeDetectorRef,
		private modalService: ModalService,
		private agendamentoService: TreinoApiAgendamentoService,
		private router: Router,
		private agendaGridState: AgendaTimeGridStateService,
		private agendaStateService: AgendaCardsStateService
	) {}

	ngOnInit() {}

	get locacao(): boolean {
		return this.servico && this.servico.tipo === "agendamento_locacao";
	}

	clickTurma(turma: AgendaCard) {
		if (this.locacao) {
			try {
				const elemento = document.getElementById("cat-tooltip-slot-card-item");
				elemento.click();
				elemento.style.display = "none";
			} catch (e) {}

			if (turma.tipoHorario.toLowerCase() !== "play") {
				this.router.navigate([
					"agenda",
					"painel",
					"detalhamento-locacao",
					turma.codigo,
					turma.diaMes,
				]);
			} else {
				this.router.navigate(
					[
						"agenda",
						"painel",
						"detalhamento-locacao-play",
						turma.locacaoHorarioCodigo,
						turma.diaMes,
					],
					{
						queryParams: {
							ambienteId: turma.ambienteCodigo,
							ambiente: turma.ambiente,
							agendamentoLocacaoCodigo: turma.codigo,
						},
					}
				);
			}
		} else {
			this.editarServico();
		}
	}

	private openEditAgendamentoModal(agendamento: AgendaAgendamento) {
		const modal = this.modalService.open(
			"Editar Evento",
			AgendamentoEditModalComponent
		);
		modal.componentInstance.agendamento = agendamento;
		modal.result.then(
			(editDto) => {
				this.agendamentoService
					.editarAgendamento(agendamento.id, editDto)
					.subscribe((result) => {
						if (result && result.id) {
							this.snotify.success("Agendamento editado com sucesso");
						} else if (result === "horario_indisponivel") {
							this.snotify.error("Horário indisponível.");
						} else if (result === "validacaoalunoagendamento") {
							this.snotify.error(
								"O aluno já realizou o número limite de agendamentos dentro do período de dias informado no Tipo de Evento."
							);
						} else if (
							result === "validacaoalunoagendamentofaltaemintervalodias"
						) {
							this.snotify.error(
								"O aluno possui uma falta dentro do intervalo de dias configurado no Tipo de Evento."
							);
						} else if (
							result.toString().toLowerCase().includes("tempo mínimo")
						) {
							this.snotify.error(result.toString());
						} else if (
							result
								.toString()
								.toLowerCase()
								.includes("limite de agendamentos permitidos")
						) {
							this.snotify.error(result.toString());
						} else if (
							result
								.toString()
								.toLowerCase()
								.includes("falta(s) dentro do período")
						) {
							this.snotify.error(result.toString());
						} else if (
							result.toString().toLowerCase().includes("antecedência")
						) {
							this.snotify.error(`Alteração não permitida: ${result}`);
						} else if (
							result.toString().toLowerCase().includes("cancelamento")
						) {
							this.snotify.error(`Operação não permitida: ${result}`);
						} else {
							this.snotify.error(
								result && result.toString().trim()
									? `${result}`
									: "Erro ao editar agendamento. Verifique os dados informados."
							);
						}
						this.fetchData(this.agendaServiceState.currentFiltro);
						this.agendaStateService.eventSetup();
					});
			},
			() => {}
		);
	}

	private fetchData(agendamentoFiltro: AgendaServicoAgendamentoFilter) {
		if (this.fetchDataSubscription) {
			this.fetchDataSubscription.unsubscribe();
		}
		this.loading = true;
		this.cd.detectChanges();
		this.fetchDataSubscription = this.agendamentoService
			.obterAgendamentosPorDia(
				agendamentoFiltro.periodo.date,
				agendamentoFiltro.periodo.view,
				{
					professoresIds: agendamentoFiltro.professoresIds,
					status: agendamentoFiltro.status,
					tipoAgendamentoIds: agendamentoFiltro.tipoAgendamentoIds,
				}
			)
			.subscribe((agendamentosPorDia) => {
				this.loading = false;
				this.eventos = [];
				for (const dia in agendamentosPorDia) {
					const gridEvents: AgendaEvento[] = [];
					agendamentosPorDia[dia].forEach((agendamento) => {
						if (this.permissaoConsultarAgendamento(agendamento)) {
							gridEvents.push({
								inicio: agendamento.horarioInicial,
								fim: agendamento.horarioFinal,
								renderComponent: AgendamentoCardComponent,
								hoverComponent: AgendamentoHoverCardComponent,
								modalComponent: AgendamentoModalCardComponent,
								payload: agendamento,
							});
						}
					});
					this.eventos.push(gridEvents);
				}
				this.agendaGridState.resetModalState();
				this.cd.detectChanges();
			});
	}

	periodo(servico: AgendaCard) {
		return servico.inicio + " - " + servico.fim;
	}

	get diaView() {
		return this.agendaStateService.view === AgendaView.DIA;
	}

	editarServico() {
		const agendamento = this.servico.servicoAgendado;
		if (agendamento.tipoAgendamento.comportamentoEnum === "AVALIACAO_FISICA") {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.AVALIACAO_FISICA
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento(agendamento);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "CONTATO_INTERPESSOAL"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento(agendamento);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "PRESCRICAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.PRESCRICAO_TREINO
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento(agendamento);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "RENOVAR_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.RENOVAR_TREINO
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento(agendamento);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "REVISAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.REVISAO_TREINO
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento(agendamento);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		}
	}

	apresentarNotificacaoSemPermissao() {
		this.snotify.warning(
			"Seu usuário não possui permissão, procure seu administrador"
		);
	}

	editarAgendamento(agendamento) {
		this.agendaServiceState.editarEvento$.emit(agendamento);
		this.openEditAgendamentoModal(agendamento);
	}

	private permissaoConsultarAgendamento(agendamento) {
		if (agendamento.tipoAgendamento.comportamentoEnum === "AVALIACAO_FISICA") {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.AVALIACAO_FISICA
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "CONTATO_INTERPESSOAL"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "PRESCRICAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.PRESCRICAO_TREINO
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "RENOVAR_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.RENOVAR_TREINO
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "REVISAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.REVISAO_TREINO
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		}
		return false;
	}
}
