<span
	(click)="clickTurma(servico)"
	[ngClass]="{ 'dia-view': diaView }"
	[pactoCatTolltip]="tooltip"
	class="slot-turma"
	id="slot-card-item-">
	<span
		[ngStyle]="{ 'background-color': servico.cor }"
		class="cor-modalidade"></span>
	<span class="nome-turma">{{ servico.turma.toLowerCase() }}</span>
	<span>
		<i class="pct pct-bar-chart"></i>
		{{ (locacao ? servico.ambiente : servico.professor).toLowerCase() }}
	</span>
	<span class="spans-periodo-cancelamento">
		<span>
			<i class="pct pct-clock"></i>
			{{ periodo(servico) }}
		</span>
		<span *ngIf="servico.agendamentoLocacaoCancelada">
			<i class="pct pct-x-circle"></i>
			Cancelado
		</span>
	</span>

	<ng-template #tooltip>
		<div class="tooltip-agenda-pacto">
			<span class="nome-turma">{{ servico.turma.toLowerCase() }}</span>
			<span *ngIf="locacao">
				<i class="pct pct-user"></i>
				{{ servico.ambiente?.toLowerCase() }}
			</span>
			<span *ngIf="!locacao">
				<i class="pct pct-user"></i>
				{{ servico.professor?.toLowerCase() }}
			</span>
			<span *ngIf="!locacao">
				<i class="pct pct-user"></i>
				{{ servico.servicoAgendado.aluno?.nome.toLowerCase() }}
			</span>
			<span>
				<i class="pct pct-clock"></i>
				{{ periodo(servico) }}
			</span>
		</div>
	</ng-template>
</span>
