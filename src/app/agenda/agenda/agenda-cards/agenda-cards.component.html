<div
	*ngIf="agenda"
	[ngClass]="{
		'dia-view': diaView,
		'semana-view': !diaView,
		'servico-view': servicoView,
		'ambiente-view': ambienteView,
		'modalidade-view': modalidadeView
	}"
	class="agenda-container">
	<div (scroll)="onScroll($event)" class="agenda-scroll-container">
		<div class="breadcrumbs">
			<pacto-breadcrumbs
				[back]="false"
				[breadcrumbConfig]="{
					categoryName: '',
					categoryLink: [],
					menu: 'Agenda'
				}"></pacto-breadcrumbs>
		</div>

		<div
			[ngClass]="{ exibirNavegacaoColunas: exibirNavegacaoColunas }"
			class="agenda-cards">
			<div #filtrosAgendaCardsComponent class="content-filtros">
				<pacto-filtros-agenda-cards></pacto-filtros-agenda-cards>
			</div>

			<div *ngIf="!loading" class="content-agenda">
				<div #cabecalho_agenda class="header-agenda-turma">
					<div class="grid-header {{ gridSize }}">
						<span class="slot-hora">
							<div
								(click)="voltarColuna()"
								[ngClass]="{ inativo: indexColunas == 0 }"
								class="navegacaoColunas"
								id="voltar-coluna">
								<i class="pct pct-chevron-left"></i>
							</div>
						</span>
						<span
							*ngFor="let dia of colunas()"
							[ngClass]="{ selected: isToday(dia) }"
							class="slot-dia">
							<div class="diaSemana">{{ diaSemana(dia) }}</div>
							<div class="diaMes">{{ diaConverter(dia) }}</div>
							<div class="fechado">
								{{ diaSemana(dia) }} {{ diaConverter(dia) }}
							</div>
						</span>
						<span class="slot-navegacao">
							<div
								(click)="avancarColuna()"
								*ngIf="agenda?.dias"
								[ngClass]="{ inativo: indexColunas >= agenda.dias.length - 7 }"
								class="navegacaoColunas"
								id="avancar-coluna">
								<i class="pct pct-chevron-right"></i>
							</div>
						</span>
					</div>
					<div (click)="toggleCabecalho()" class="liga_desliga">
						<i
							[pactoCatTolltip]="'Retrair cabeçalho'"
							[position]="'bottom'"
							class="pct pct-chevron-up"
							pactoCatTolltip></i>
						<i
							[pactoCatTolltip]="'Expandir cabeçalho'"
							[position]="'bottom'"
							class="pct pct-chevron-down"
							pactoCatTolltip></i>
					</div>
				</div>

				<div>
					<div
						*ngFor="let hora of agenda.itens; let index = index"
						[ngClass]="{ 'primeira-linha': index === 0 }"
						class="linha-agenda {{ gridSize }}">
						<span class="slot-hora">{{ hora.horario }}</span>
						<span *ngFor="let diaSemana of colunas()" class="slot-dia">
							<ng-container *ngFor="let item of hora.dias[diaSemana]">
								<pacto-card-item-agenda
									*ngIf="item.tipo === 'aula'"
									[isFilaDeEsperaHabilitada]="isFilaDeEsperaHabilitada"
									[turma]="item"></pacto-card-item-agenda>
								<pacto-card-disponibilidade
									*ngIf="item.tipo === 'disponibilidades'"
									[day]="item.diaMes"
									[item]="item"></pacto-card-disponibilidade>

								<pacto-card-locacao
									*ngIf="item.tipo === 'locacao'"
									[day]="item.diaMes"
									[item]="item"></pacto-card-locacao>

								<pacto-card-servico
									*ngIf="
										item.tipo === 'servico' ||
										item.tipo === 'agendamento_locacao'
									"
									[servico]="item"></pacto-card-servico>
							</ng-container>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div
		#cabecalho_fixo
		[ngClass]="{ exibirNavegacaoColunas: exibirNavegacaoColunas }"
		class="fixo header-agenda-turma hidden">
		<div class="grid-header  {{ gridSize }}">
			<span class="slot-hora">
				<div
					(click)="voltarColuna()"
					[ngClass]="{ inativo: indexColunas == 0 }"
					class="navegacaoColunas"
					id="voltar-coluna-fix">
					<i class="pct pct-chevron-left"></i>
				</div>
			</span>
			<span
				*ngFor="let dia of colunas()"
				[ngClass]="{ selected: isToday(dia) }"
				class="slot-dia">
				<div class="diaSemana">{{ diaSemana(dia) }}</div>
				<div class="diaMes">{{ diaConverter(dia) }}</div>
				<div class="fechado">{{ diaSemana(dia) }} {{ diaConverter(dia) }}</div>
			</span>

			<span class="slot-navegacao">
				<div
					(click)="avancarColuna()"
					*ngIf="agenda?.dias"
					[ngClass]="{ inativo: indexColunas >= agenda.dias.length - 7 }"
					class="navegacaoColunas"
					id="avancar-coluna-fix">
					<i class="pct pct-chevron-right"></i>
				</div>
			</span>
		</div>
		<div (click)="toggleCabecalho()" class="liga_desliga">
			<i
				[pactoCatTolltip]="'Retrair cabeçalho'"
				[position]="'bottom'"
				class="pct pct-chevron-up"
				pactoCatTolltip></i>
			<i
				[pactoCatTolltip]="'Expandir cabeçalho'"
				[position]="'bottom'"
				class="pct pct-chevron-down"
				pactoCatTolltip></i>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #diaSemanaTexto>
	<span i18n="@treino-bi:dias:segunda" xingling="SEGUNDA_FEIRA">Segunda</span>
	<span i18n="@treino-bi:dias:terca" xingling="TERCA_FEIRA">Terça</span>
	<span i18n="@treino-bi:dias:quarta" xingling="QUARTA_FEIRA">Quarta</span>
	<span i18n="@treino-bi:dias:quinta" xingling="QUINTA_FEIRA">Quinta</span>
	<span i18n="@treino-bi:dias:sexta" xingling="SEXTA_FEIRA">Sexta</span>
	<span i18n="@treino-bi:dias:sabado" xingling="SABADO">Sábado</span>
	<span i18n="@treino-bi:dias:domingo" xingling="DOMINGO">Domingo</span>
</pacto-traducoes-xingling>
