import { Component, Input, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { AgendaCard, AgendaView } from "treino-api";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { AddFilaEsperaTurmaModalComponent } from "../modal-add-fila-espera-turma/modal-add-fila-espera-turma.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PermissaoService } from "pacto-layout";

@Component({
	selector: "pacto-card-item-agenda",
	templateUrl: "./card-item-agenda.component.html",
	styleUrls: ["./card-item-agenda.component.scss"],
})
export class CardItemAgendaComponent implements OnInit {
	@Input() turma: AgendaCard;
	@Input() isFilaDeEsperaHabilitada = false;

	constructor(
		private router: Router,
		private ngbModal: NgbModal,
		private agendaStateService: AgendaCardsStateService,
		private permissaoService: PermissaoService
	) {}

	ngOnInit() {}

	clickTurma(turma: AgendaCard) {
		try {
			document.getElementById("cat-tooltip-slot-card-item").click();
		} catch (e) {}
		this.router.navigate([
			"agenda",
			"painel",
			"detalhamento-aula",
			turma.codigo,
			turma.diaMes,
		]);
	}

	ocupacao(turma: AgendaCard) {
		let ocupacao = turma.ocupacao < 10 ? "0" : "";
		ocupacao += turma.ocupacao + "/";
		ocupacao += turma.capacidade < 10 ? "0" : "";
		ocupacao += turma.capacidade;
		return ocupacao;
	}

	periodo(turma: AgendaCard) {
		return turma.inicio + " - " + turma.fim;
	}

	get diaView() {
		return this.agendaStateService.view === AgendaView.DIA;
	}

	get isTurmaSomenteLimite() {
		return (
			this.turma &&
			this.turma.ocupacao >= this.turma.capacidade &&
			this.turma.bloquearMatriculasAcimaLimite
		);
	}

	openModalAddFilaTurma() {
		const ref = this.ngbModal.open(AddFilaEsperaTurmaModalComponent, {
			centered: true,
			size: null,
			windowClass: "modal-action-nfse-component",
		});
		ref.componentInstance.codigoHorarioTurma = this.turma.codigo;
		ref.result.then(
			(dto) => {
				this.router.navigate(["agenda", "painel", "cards"]);
			},
			() => {}
		);
	}

	get temPermissaoFila() {
		return this.permissaoService.temPermissaoAdm("10.11");
	}
}
