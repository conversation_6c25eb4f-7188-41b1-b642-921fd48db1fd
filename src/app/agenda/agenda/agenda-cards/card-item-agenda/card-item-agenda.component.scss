$borda-padrao: 1px solid #eff1f7;
.slot-turma {
	position: relative;
	display: grid;

	&.dia-view {
		grid-template-columns: 1fr 1fr 1fr 1fr;
		height: 40px;
		align-items: center;

		span {
			line-height: 40px;
		}

		.nome-turma {
			font-size: 14px;
		}
	}

	.cor-modalidade {
		position: absolute;
		top: 8px;
		left: 8px;
		width: 3px;
		height: calc(100% - 16px);
		border-radius: 4px;
		background-color: darkgrey;
	}

	i.adicionado {
		display: none;
	}

	cursor: pointer;
	box-sizing: border-box;

	flex-direction: row;
	align-items: flex-start;
	padding: 8px 8px 8px 21px;
	width: 100%;
	height: 80px;
	background: #fafafa;
	border: 1px solid #dcdddf;

	&.cheia {
		border: 1px solid #e23661;
		background-color: #fce9ed !important;
	}

	border-radius: 4px;
	margin-bottom: 5px;

	span {
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		color: #a1a5aa;
		text-transform: capitalize;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		i {
			margin-right: 5px;
		}
	}

	.nome-turma {
		font-style: normal;
		font-weight: 700;
		font-size: 12px;
		color: #51555a;
	}
}

.tooltip-agenda-pacto {
	min-width: 130px;
	display: block;
	color: #a1a5aa;
	font-size: 13px;
	margin-top: 5px;
	text-transform: capitalize;

	.nome-turma {
		color: var(--black-default, #51555a);
		font-size: 14px;
		font-style: normal;
		font-weight: 700;
		line-height: 125%;
		margin-bottom: 20px;
	}

	span {
		display: block;

		i {
			margin-right: 5px;
		}

		margin-bottom: 8px;
		color: #a1a5aa;
		font-weight: normal;
	}
}

.ajuste-btn-fila {
	text-align: right;
	margin-top: 3px;
}
