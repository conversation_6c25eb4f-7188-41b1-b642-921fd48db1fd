<span
	(click)="clickTurma(turma)"
	[ngClass]="{ 'dia-view': diaView, cheia: turma.ocupacao >= turma.capacidade }"
	[pactoCatTolltip]="tooltip"
	class="slot-turma"
	id="slot-card-item">
	<span
		[ngStyle]="{ 'background-color': turma.cor }"
		class="cor-modalidade"></span>
	<span class="nome-turma">{{ turma.turma.toLowerCase() }}</span>
	<span>
		<i class="pct pct-bar-chart"></i>
		{{ ocupacao(turma) }}
	</span>
	<span>
		<i class="pct pct-clock"></i>
		{{ periodo(turma) }}
	</span>

	<span class="ajuste-btn-fila" *ngIf="isFilaDeEsperaHabilitada">
		<pacto-cat-button
			(click)="openModalAddFilaTurma()"
			*ngIf="temPermissaoFila && isTurmaSomenteLimite && diaView"
			i18n-label="@@integracoes:label-btn-salvar-alteracoes"
			id="btn-fila"
			label="Adicionar na fila de espera"
			size="NORMAL"
			type="PRIMARY"></pacto-cat-button>
	</span>
	<ng-template #tooltip>
		<div class="tooltip-agenda-pacto">
			<span class="nome-turma">{{ turma.turma.toLowerCase() }}</span>
			<span>
				<i class="pct pct-user"></i>
				{{ turma.professor?.toLowerCase() }}
			</span>
			<span>
				<i class="pct pct-map-pin"></i>
				{{ turma.ambiente?.toLowerCase() }}
			</span>
			<span>
				<i class="pct pct-bar-chart"></i>
				{{ ocupacao(turma) }}
			</span>
			<span>
				<i class="pct pct-clock"></i>
				{{ periodo(turma) }}
			</span>
		</div>
	</ng-template>
</span>
