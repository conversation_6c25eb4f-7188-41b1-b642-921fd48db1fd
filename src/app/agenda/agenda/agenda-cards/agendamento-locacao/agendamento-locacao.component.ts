import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { Ambiente } from "treino-api";
import { TreinoApiLocacaoService } from "treino-api";
import { ActivatedRoute, Router } from "@angular/router";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";
import {
	AdmCoreApiVendaAvulsaService,
	ItemVendaAvulsa,
	VendaAvulsa,
} from "adm-core-api";
import { AdmRestService } from "../../../../../../projects/adm/src/app/adm-rest.service";
import { Api } from "sdk";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

declare var moment;

@Component({
	selector: "pacto-agendamento-locacao",
	templateUrl: "./agendamento-locacao.component.html",
	styleUrls: ["./agendamento-locacao.component.scss"],
})
export class AgendamentoLocacaoComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	locacao: any;
	id;
	data;
	cod;
	total = 0.0;
	totalLocacao = 0.0;
	totalAdicionais = 0.0;
	locacaoFg = new FormGroup({
		aluno: new FormControl("", [Validators.required]),
		responsavel: new FormControl(""),
		produto: new FormControl(""),
		dia: new FormControl(),
		valor: new FormControl(),
		ambiente: new FormControl("", [Validators.required]),
	});
	ambientes: Ambiente[];
	ambientePreDefinido = "";
	horarioInicial = "";
	locacaoNome = "";
	@ViewChild("tableHorarioComponent", { static: false })
	tableHorarioComponent: RelatorioComponent;
	@ViewChild("tableServicosComponent", { static: false })
	tableServicosComponent: RelatorioComponent;
	tableHorario: PactoDataGridConfig;
	tableServicos: PactoDataGridConfig;

	constructor(
		private rest: RestService,
		private route: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService,
		private treinoApiLocacaoService: TreinoApiLocacaoService,
		private agendaStateService: AgendaCardsStateService,
		private cd: ChangeDetectorRef,
		private util: AgendaUtilsService,
		private vendaAvulsaService: AdmCoreApiVendaAvulsaService,
		private snotifyService: SnotifyService,
		private admRest: AdmRestService,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.route.queryParams.subscribe((queryParams) => {
			if (!!queryParams.ambiente) {
				this.ambientePreDefinido = queryParams.ambiente;
			}
			if (!!queryParams.horarioInicial) {
				this.horarioInicial = queryParams.horarioInicial;
			}
			if (!!queryParams.locacaoNome) {
				this.locacaoNome = queryParams.locacaoNome;
			}
		});

		this.route.params.subscribe((params) => {
			if (params.id && params.dia) {
				this.id = params.id;
				this.data = params.dia;
				this.cod = params.cod;
				this.treinoApiLocacaoService
					.configAgendamento(params.id, params.cod, params.dia)
					.subscribe((response) => {
						this.locacao = response;
						this.ambientes = response.ambientes;
						this.totalLocacao = this.locacao.valorLocacao;
						this.total = this.locacao.valorTotal;
						if (this.ambientes.length === 1) {
							this.locacaoFg.get("ambiente").setValue(this.ambientes[0]);
						}
						if (this.ambientePreDefinido !== "") {
							this.locacaoFg
								.get("ambiente")
								.setValue(
									this.ambientes.filter(
										(it) => it.nome === this.ambientePreDefinido
									)[0]
								);
						}
						if (this.cod > 0) {
							this.locacaoFg.get("aluno").setValue(this.locacao.aluno);
							this.locacaoFg.get("ambiente").setValue(this.locacao.ambiente);
						}
						this.locacaoFg
							.get("responsavel")
							.setValue(this.locacao.responsavel);
						this.locacaoFg.get("produto").disable({ emitEvent: false });
						this.locacaoFg.get("produto").setValue(this.locacao.produto);
						this.locacaoFg.get("produto").disable({ emitEvent: false });
						this.locacaoFg
							.get("valor")
							.setValue(this.transformMoneyValue(this.locacao.valorHora));
						this.locacaoFg.get("valor").disable({ emitEvent: false });
						this.locacaoFg.get("dia").setValue(this.data);
						this.locacaoFg.get("dia").disable({ emitEvent: false });
						this.initTable();
						this.initTableServicos();
						this._listenChanges();
						setTimeout(() => {
							this.calcularTotal();
							this.cd.detectChanges();
						}, 1000);
					});
			}
		});
	}

	public initTable() {
		this.tableHorario = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				horaInicio: new FormControl(""),
				inicioFim: new FormControl(""),
				duracaoDescricao: new FormControl(""),
			}),
			dataAdapterFn: () => {
				const content = [];
				if (this.locacao.horariosAdicionados) {
					this.locacao.horariosAdicionados.forEach((hr: any) => {
						content.push({
							codigo: hr.codigo,
							diaSemana: hr.diaSemana,
							responsavel: hr.responsavel,
							horaInicio:
								this.locacao.tipoHorario === "LIVRE"
									? this.horarioInicial.length > 0
										? this.horarioInicial
										: hr.horaInicio
									: hr.horaInicio,
							horaFim: hr.horaFim,
							inicioFim: `${hr.horaInicio} - ${hr.horaFim}`,
							duracaoDescricao: hr.tempoMinimoMinutos,
							permiteAgendarPeloAppTreino: hr.permiteAgendarPeloAppTreino,
							ambientes: hr.ambientes,
							possuiAgendamentos: hr.possuiAgendamentos,
							ativo: hr.ativo,
						});
					});
				}
				return { content };
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmHorarios(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			showEdit: (row, isAdd) => this.showDelete(row, isAdd),
			columns: this.generateColumns(),
		});
		this.cd.detectChanges();
	}

	private _listenChanges() {
		if (this.locacao.tipoHorario === "LIVRE") {
			this.tableHorario.columns.find(
				(v) => v.nome === "duracaoDescricao"
			).editable = true;
		}

		this.tableHorario.formGroup
			.get("duracaoDescricao")
			.setValidators(
				Validators.min(
					this.tableHorario.formGroup.get("duracaoDescricao").value
				)
			);
	}

	generateColumns(): any[] {
		if (this.locacao.tipoHorario === "LIVRE") {
			return [
				{
					nome: "horaInicio",
					titulo: "Horário",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "text",
					inputTextMask: {
						mask: [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/],
						guide: false,
					},
					width: "50px",
				},
				{
					nome: "duracaoDescricao",
					titulo: "Duração",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					width: "13%",
					showSelectFilter: false,
					showAddSelectBtn: false,
					valueTransform: (v) => `${v} minutos`,
				},
			];
		} else {
			return [
				{
					nome: "inicioFim",
					titulo: "Horário",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					selectOptionChange: (option, form, row) => {
						row.codigo = option.codigo;
						row.horaInicio = option.horaInicio;
						row.horaFim = option.horaFim;
						row.inicioFim = option.inicioFim;
					},
					width: "50px",
					idSelectKey: "codigo",
					labelSelectKey: "horaInicio",
					objectAttrLabelName: "horaInicio",
					inputSelectData: this._filtrarHorariosDisponiveis(),
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
			];
		}
	}

	private getDuracaoEmMinutos(horaInicio: string, horaFim: string) {
		return (
			this.util.convertTimeLabelIntoMinutes(horaFim) -
			this.util.convertTimeLabelIntoMinutes(horaInicio)
		);
	}

	private _filtrarHorariosDisponiveis(): any[] {
		const horariosParaExcluir = this.locacao.horariosAdicionados.map(
			(it) => it.codigo
		);

		return this.locacao.horarios.filter(
			(it) =>
				!(horariosParaExcluir.includes(it.codigo) || it.possuiAgendamentos)
		);
	}

	public initTableServicos() {
		this.tableServicos = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: this.admRest.buildFullUrl(
				"produtos/com-estoque",
				false,
				Api.MSPRODUTO
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			formGroup: new FormGroup({
				nomeProduto: new FormControl(),
				quantidade: new FormControl(),
			}),
			dataAdapterFn: ({ content }: any) => {
				const produtos = [];
				if (this.locacao.produtosObrigatorios) {
					this.locacao.produtosObrigatorios.forEach((item: any) => {
						produtos.push({
							nomeProduto: item,
							valor: item.valor,
							obrigatorio: item.obrigatorio,
							quantidade: item.quantidade,
							valorTotal: item.quantidade.id * item.valor,
						});
					});
				}
				if (content.length > 0) {
					content.forEach((item: any) => {
						this.locacao.produtosSugeridos.push({
							codigo: item.codigo,
							nomeProduto: item.descricao,
							valor: item.valorFinal,
							obrigatorio: false,
							quantidade: { id: 1, label: "1" },
							produto: {
								codigo: item.codigo,
								descricao: item.descricao,
								valorFinal: item.valorFinal,
							},
						});
					});
				}
				return {
					content: produtos,
				};
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			showEdit: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "nomeProduto",
					titulo: "Nome",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					selectOptionChange: (option, form, row) => {
						form.get("quantidade").setValue({ id: 1, label: 1 });
						row.quantidade = { id: 1, label: 1 };
						row.valor = option.valor;
						row.valorTotal = option.valor;
					},
					idSelectKey: "codigo",
					labelSelectKey: "nomeProduto",
					objectAttrLabelName: "nomeProduto",
					inputSelectData: this.locacao.produtosSugeridos,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "quantidade",
					titulo: "Quantidade",
					inputType: "select",
					idSelectKey: "id",
					labelSelectKey: "label",
					objectAttrLabelName: "label",
					inputSelectData: this.qtds,
					selectOptionChange: (option, form, row) => {
						row.valorTotal = row.valor * option.id;
					},
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "valor",
					titulo: "Valor unitário",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "valorTotal",
					titulo: "Valor total",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
			],
		});
		this.cd.detectChanges();
	}

	get qtds() {
		const array = [];
		for (let i = 1; i <= 20; i++) {
			array.push({ id: i, label: `${i}` });
		}
		return array;
	}

	public isAddRowAvailable(): boolean {
		if (this.locacao.tipoHorario === "LIVRE") {
			return false;
		}

		const horariosAdicionados = this.locacao.horariosAdicionados.map(
			(it) => it.codigo
		);
		return (
			this.locacao.horarios.filter(
				(it) =>
					!it.possuiAgendamentos && !horariosAdicionados.includes(it.codigo)
			).length > 0
		);
	}

	beforeConfirmHorarios(row, form, data, rowIndex): boolean {
		if (this.locacao.tipoHorario === "LIVRE") {
			if (!this.validateHoraInicio(data)) {
				this.notificationService.warning("A Hora Inicio não pode ser vazia!");
				return false;
			}

			let duracaoMaxima = 0;

			if (typeof row.horaInicio === "object") {
				duracaoMaxima = this.getDuracaoEmMinutos(
					row.horaInicio.horaInicio,
					row.horaFim
				);
			} else {
				duracaoMaxima = this.getDuracaoEmMinutos(row.horaInicio, row.horaFim);
			}

			if (row.duracaoDescricao === null || row.duracaoDescricao === "") {
				row.duracaoDescricao = duracaoMaxima;
			}

			if (row.duracaoDescricao < this.locacao.tempoMinimoMinutos) {
				this.notificationService.warning(
					"O tempo mínimo para locação é de " +
						this.locacao.tempoMinimoMinutos +
						" minutos!"
				);
				return false;
			}

			if (row.duracaoDescricao > duracaoMaxima) {
				this.notificationService.warning(
					"O tempo máximo para locação é de " + duracaoMaxima + " minutos!"
				);
				return false;
			}
		} else {
			row.inicioFim = row.inicioFim.inicioFim;
		}

		this.tableServicosComponent.rawData.forEach((item: any) => {
			if (!!item.obrigatorio) {
				item.quantidade = {
					id: item.quantidade.id++,
					label: `${item.quantidade.id}`,
				};
			}
		});
		this._reapresentarOpcoesDoSelect(data, true);
		this.cd.detectChanges();
		this.calcularTotal();
		return true;
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this._reapresentarOpcoesDoSelect(data);
		return true;
	}

	validateHoraInicio(data: any[]): boolean {
		let validated = true;
		data.forEach((it: any) => {
			if (!it.horaInicio) {
				validated = false;
				return;
			}
		});
		return validated;
	}

	showDelete(row, isAdd): boolean {
		return !row.obrigatorio;
	}

	confirmHorario(event) {
		if (this.locacao.tipoHorario === "LIVRE") {
			this.calcularTotal(
				(this.locacao.valorHora / 60) * event.row.duracaoDescricao
			);
		} else {
			this.calcularTotal();
		}
	}

	removeHorario(event) {
		if (this.tableHorarioComponent.rawData) {
			this.tableHorarioComponent.rawData.splice(event.index, 1);
		}
		this.tableServicosComponent.rawData.forEach((item: any) => {
			if (!!item.obrigatorio) {
				item.quantidade = {
					id: item.quantidade.id,
					label: `${item.quantidade.id}`,
				};
			}
		});
		this.cd.detectChanges();
		this._reapresentarOpcoesDoSelect(event.data, true);
		this.calcularTotal();
	}

	private _reapresentarOpcoesDoSelect(
		horariosAdicionados: any[],
		modifyHorariosAdicionados = false
	) {
		this.tableHorario.columns
			.filter((it) => it.nome === "inicioFim")
			.map((it) => {
				if (modifyHorariosAdicionados) {
					this.locacao.horariosAdicionados = horariosAdicionados;
				}
				it.inputSelectData = this._filtrarHorariosDisponiveis();
			});
	}

	isEditingOrAdding($event: boolean) {}

	confirmServico({ row }: any) {
		this.locacao.produtosObrigatorios.push({
			...row,
			produto: { ...row.nomeProduto },
		});
		this.calcularTotal();
		this.cd.detectChanges();
	}

	removeServico(event) {
		if (this.tableServicosComponent.rawData) {
			this.tableServicosComponent.rawData.splice(event.index, 1);
		}
		this.calcularTotal();
	}

	isEditingOrAddingServico($event: boolean) {}

	resposeParser = (result) => result.content;

	alunoParamBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	get alunoUrl() {
		return this.rest.buildFullUrl("alunos");
	}

	salvar(receberAgora = false) {
		if (this.valid) {
			this.treinoApiLocacaoService.agendar(this.data, this.getDto()).subscribe(
				(response) => {
					if (response && !response.erro) {
						this.notificationService.success(
							"Agendamento confirmado com sucesso!"
						);

						this.gravarVenda(response.retorno, receberAgora);
					} else {
						const messageError = response.erro.split(":");
						this.notificationService.error(
							this.traducoes.getLabel(messageError[1].trim())
						);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning("Aluno e ambiente são obrigatórios!");
		}
	}

	adicionarMinutos(horario: string, minutos: number): string {
		const [hora, minuto] = horario.split(":").map(Number);
		const data = new Date();
		data.setHours(hora, minuto);
		data.setMinutes(data.getMinutes() + minutos);

		// Retorna no formato HH:mm
		return data.toTimeString().slice(0, 5);
	}

	private getDto() {
		const dto: any = {};
		dto.matricula = this.locacaoFg.get("aluno").value.matriculaZW;
		dto.ambiente = this.locacaoFg.get("ambiente").value.id;

		dto.horarios = this.tableHorarioComponent.rawData.map((item) => {
			let horaInicio;
			let horaFim;

			if (this.locacao.tipoHorario === "LIVRE") {
				horaInicio =
					typeof item.horaInicio === "object"
						? item.horaInicio.horaInicio
						: item.horaInicio;
				horaFim = this.adicionarMinutos(horaInicio, item.duracaoDescricao);
			} else {
				const [inicio, fim] = item.inicioFim.split("-");
				horaInicio = inicio.trim();
				horaFim = fim.trim();
			}

			return {
				codigo: item.codigo,
				diaSemana: item.diaSemana,
				responsavel: item.responsavel,
				horaInicio,
				horaFim,
				inicioFim: `${horaInicio} - ${horaFim}`,
				permiteAgendarPeloAppTreino: item.permiteAgendarPeloAppTreino,
				ambientes: item.ambientes,
				possuiAgendamentos: item.possuiAgendamentos,
				ativo: item.ativo,
				duracaoMinutos: item.duracaoDescricao,
			};
		});

		dto.valorTotal = this.total;
		dto.valorLocacao = this.totalLocacao;

		dto.servicos = [];
		this.tableServicosComponent.rawData.forEach((item) => {
			const obj = item.nomeProduto;
			obj.quantidade = item.quantidade;
			dto.servicos.push(obj);
		});

		return dto;
	}

	get valid() {
		return this.locacaoFg.valid;
	}

	public transformMoneyValue(valor: number) {
		const valorFormatado = Number(valor).toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
		return "R$ " + valorFormatado;
	}

	get valorLocacao() {
		return this.transformMoneyValue(this.totalLocacao);
	}

	get valorExtrasObrigatorios() {
		return this.transformMoneyValue(
			this.locacao ? this.locacao.valorExtrasObrigatorios : 0.0
		);
	}

	get valorTotal() {
		return this.transformMoneyValue(this.total);
	}

	get valorAdicionais() {
		return this.transformMoneyValue(this.totalAdicionais);
	}

	calcularTotal(valorLocacao = -1) {
		this.totalAdicionais = 0.0;
		this.tableServicosComponent.rawData.forEach((item) => {
			if (!item.obrigatorio) {
				this.totalAdicionais += item.valorTotal;
			}
		});
		let valorTotalObrigatorio = 0;
		this.tableServicosComponent.rawData.forEach((item: any) => {
			if (!!item.obrigatorio) {
				valorTotalObrigatorio += item.valor * Number(item.quantidade.label);
			}
		});
		this.locacao.valorExtrasObrigatorios = valorTotalObrigatorio;
		const valoresCalculados = this.tableHorarioComponent.rawData.map(
			(item) =>
				(this.locacao.valorHora / 60) *
				(!!item.duracaoDescricao
					? item.duracaoDescricao
					: this.getDuracaoEmMinutos(
							item.inicioFim.split("-")[0],
							item.inicioFim.split("-")[1]
					  ))
		);

		this.totalLocacao =
			valorLocacao > -1
				? valorLocacao
				: valoresCalculados.reduce((acc, val) => acc + val, 0);

		this.total = Number(
			this.totalLocacao +
				this.locacao.valorExtrasObrigatorios +
				this.totalAdicionais
		);
		this.cd.detectChanges();
	}

	private gravarVenda(agendamentoLocacaoId: number, receberAgora: boolean) {
		this.vendaAvulsaService.save(this.montarDto()).subscribe(
			(venda) => {
				if (venda.error) {
					this.snotifyService.error(venda.error);
					return;
				}

				const id = venda.venda;

				if (receberAgora) {
					const usuarioOamd = this.sessionService.loggedUser.username;
					const empresa = this.sessionService.empresaId;
					this.clientDiscoveryService
						.linkZw(usuarioOamd, empresa)
						.subscribe((result) => {
							this._sincronizarVendaAvulsa(
								agendamentoLocacaoId,
								id,
								() => {
									window.open(
										result +
											"&urlRedirect=caixaEmAberto_" +
											this.locacaoFg.get("aluno").value.nome
									);
									this._successAgendamento();
								},
								() =>
									this.snotifyService.error("Erro ao sincronizar venda avulsa!")
							);
						});
				} else {
					this._sincronizarVendaAvulsa(
						agendamentoLocacaoId,
						id,
						() => this._successAgendamento(),
						() => this.snotifyService.error("Erro ao sincronizar venda avulsa!")
					);
				}
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.snotifyService.error(err.meta.messageValue);
				} else {
					this.snotifyService.error(
						"Ocorreu um erro inesperado, tente novamente."
					);
				}
			}
		);
	}

	montarDto(): VendaAvulsa {
		const locacao = this.locacaoFg.value;

		return {
			pessoa: locacao.aluno.codigoPessoa,
			nomeComprador: locacao.aluno.nome,
			tipo: 0,
			lancamento: Date.now(),
			parcelas: 1,
			descricaoParcela: this.locacaoNome.toUpperCase(),
			primeiraParcela: new Date(
				`${this.data.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3")}T00:00:00`
			).getTime(),
			colaborador: false,
			descontoGeral: 0,
			itens: this.itens,
		};
	}

	get itens(): Array<ItemVendaAvulsa> {
		const locacao = this.locacao;
		const itensVenda: Array<ItemVendaAvulsa> = [];

		// Adiciona os produtos obrigatórios
		if (locacao.produtosObrigatorios) {
			locacao.produtosObrigatorios.forEach((produto) => {
				const qtd = produto.quantidade ? Number(produto.quantidade.label) : 1;
				itensVenda.push({
					codigoProduto: produto.produto ? produto.produto.codigo : 0,
					precoProduto: produto.valor || 0,
					qtd,
					valorParcial: produto.valor * qtd || 0,
					descontoManual: 0,
					pontos: 0,
					descricaoProduto: produto.produto ? produto.produto.descricao : "",
					pacoteEscolhido: null,
				});
			});
		}

		if (locacao.horariosAdicionados) {
			locacao.horariosAdicionados.forEach((horario: any) => {
				const tableHorario = this.tableHorarioComponent.rawData.filter(
					(item) => item.codigo === horario.codigo
				)[0];
				const minutosAgendados =
					locacao.tipoHorario === "LIVRE"
						? tableHorario.duracaoDescricao || horario.tempoMinimoMinutos || 0
						: this.getDuracaoEmMinutos(
								horario.inicioFim.split("-")[0],
								horario.inicioFim.split("-")[1]
						  ) || 0;
				const valorParcial =
					locacao.tipoHorario === "LIVRE"
						? (locacao.valorHora / 60) * minutosAgendados || 0
						: locacao.valorTotal;
				itensVenda.push({
					codigoProduto: locacao.codigoProduto || 0,
					precoProduto: valorParcial || 0,
					qtd: 1,
					valorParcial,
					descontoManual: 0,
					pontos: 0,
					descricaoProduto: locacao.produto || "",
					pacoteEscolhido: null,
				});
			});
		}

		return itensVenda;
	}

	private _sincronizarVendaAvulsa(
		agendamentoLocacaoId: number,
		vendaAvulsaId: number,
		next: () => void,
		error: () => void
	) {
		this.treinoApiLocacaoService
			.sincronizarVendaAvulsa(agendamentoLocacaoId, vendaAvulsaId)
			.subscribe({ next: () => next(), error: () => error() });
	}

	private _successAgendamento() {
		this.agendaStateService.forceLoad$.next(true);
		this.router.navigate(["agenda", "painel", "cards"]);
	}
}
