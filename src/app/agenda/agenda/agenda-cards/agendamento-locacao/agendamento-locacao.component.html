<pacto-cat-layout-v2 *ngIf="locacao">
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'AGENDA',
			categoryLink: ['agenda', 'painel', 'cards'],
			menu: 'Agenda locação'
		}"></pacto-breadcrumbs>
	<div *ngIf="locacao" class="table-wrapper pacto-shadow agendamento-locacao">
		<div class="row">
			<div class="col-md-6">
				<div class="input-nome">Alugar para*</div>
				<pacto-cat-select-filter
					[control]="locacaoFg.get('aluno')"
					[endpointUrl]="alunoUrl"
					[id]="'aluno-adicionar-agendamento'"
					[labelKey]="'nome'"
					[paramBuilder]="alunoParamBuilder"
					[resposeParser]="resposeParser"
					id="alunos-adicionar-agendamento"></pacto-cat-select-filter>
			</div>
			<div class="col-md-6">
				<div class="input-nome">Ambiente*</div>
				<pacto-cat-select-filter
					[control]="locacaoFg.get('ambiente')"
					[id]="'ambiente-adicionar-agendamento'"
					[labelKey]="'nome'"
					[options]="ambientes"
					[placeholder]="'Selecionar ambiente'"
					[resposeParser]="resposeParser"
					id="ambientes-adicionar-agendamento"></pacto-cat-select-filter>
			</div>
		</div>
		<div class="row">
			<div class="col-md-3">
				<div class="input-nome">Produto</div>
				<input [formControl]="locacaoFg.get('produto')" type="text" />
			</div>
			<div class="col-md-3">
				<div class="input-nome">
					Valor por hora
					<i
						[pactoCatTolltip]="
							'O cálculo é realizado com o valor da hora dividido pela quantidade de minutos.'
						"
						class="pct pct-help-circle"></i>
				</div>
				<input [formControl]="locacaoFg.get('valor')" type="text" />
			</div>
			<div class="col-md-3">
				<div class="input-nome">Responsável</div>
				<input [formControl]="locacaoFg.get('responsavel')" type="text" />
			</div>
			<div class="col-md-3">
				<div class="input-nome">Data</div>
				<pacto-cat-datepicker
					[formControl]="locacaoFg.get('dia')"></pacto-cat-datepicker>
			</div>
		</div>

		<div class="lista-alunos">
			<span class="titulo">Horários do agendamento</span>

			<pacto-cat-table-editable
				#tableHorarioComponent
				(confirm)="confirmHorario($event)"
				(delete)="removeHorario($event)"
				(isEditingOrAddingItem)="isEditingOrAdding($event)"
				[isEditable]="locacao.horarios.length > 0"
				[newLineTitle]="'Adicionar mais horários'"
				[showAddRow]="isAddRowAvailable()"
				[isAddRowAvailable]="isAddRowAvailable()"
				[table]="tableHorario"
				idSuffix="table-horario"></pacto-cat-table-editable>
		</div>

		<div class="lista-alunos">
			<span class="titulo">Produtos e serviços</span>
			<pacto-cat-table-editable
				#tableServicosComponent
				(confirm)="confirmServico($event)"
				(delete)="removeServico($event)"
				(isEditingOrAddingItem)="isEditingOrAddingServico($event)"
				[isEditable]="true"
				[newLineTitle]="'Adicionar mais'"
				[showAddRow]="true"
				[table]="tableServicos"
				idSuffix="table-servicos"></pacto-cat-table-editable>
		</div>

		<div class="lista-alunos valores">
			<div>
				<span class="label-small">Locação</span>
				<span>{{ valorLocacao }}</span>
			</div>
			<div>
				<span class="label-small">Extras obrigatórios</span>
				<span>{{ valorExtrasObrigatorios }}</span>
			</div>
			<div>
				<span class="label-small">Extras adicionais</span>
				<span>{{ valorAdicionais }}</span>
			</div>
			<div>
				<span class="label-small">Total</span>
				<span class="total">{{ valorTotal }}</span>
			</div>
		</div>

		<div class="error-block bg-light-warning">
			<div class="upper text-warning">
				<i class="pct pct-alert-triangle"></i>
				<span>
					O valor da locação ficará no caixa em aberto do cliente. Você pode
					receber agora clicando em
					<b>Confirmar e receber</b>
					ou receber depois clicando em
					<b>Confirmar agendamento</b>
					.
				</span>
			</div>
		</div>

		<div class="inner-div right">
			<pacto-cat-button
				(click)="salvar(true)"
				*ngIf="cod == 0"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-salvar-e-receber"
				label="Confirmar e receber"
				size="LARGE"
				class="mr-2"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvar()"
				*ngIf="cod == 0"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-salvar-agendamento"
				label="Confirmar agendamento"
				size="LARGE"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</div>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@agendamento:valid:ambiente:indisponivel"
		xingling="SEM_DISPONIBILIDADE_AMBIENTE">
		Sem disponibilidade de horario para o ambiente
	</span>
</pacto-traducoes-xingling>
