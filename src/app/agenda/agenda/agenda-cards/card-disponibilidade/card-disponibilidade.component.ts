import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { AgendaView } from "treino-api";
import { Router } from "@angular/router";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { AgendamentoCreateModalComponent } from "../../agenda-servicos/agendamento-create-modal/agendamento-create-modal.component";
import { AgendaServicosStateService } from "../../agenda-servicos/agenda-servicos-state.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { AgendaTimeGridStateService } from "../../agenda-time-grid/agenda-time-grid-state.service";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoRecursoNome } from "treino-api";
import { ModalServicosDisponiveisComponent } from "../modal-servicos-disponiveis/modal-servicos-disponiveis.component";

@Component({
	selector: "pacto-card-disponibilidade",
	templateUrl: "./card-disponibilidade.component.html",
	styleUrls: ["./card-disponibilidade.component.scss"],
})
export class CardDisponibilidadeComponent implements OnInit {
	@Input() day;
	@Input() item;

	constructor(
		private router: Router,
		private state: AgendaServicosStateService,
		private agendaStateService: AgendaCardsStateService,
		private cd: ChangeDetectorRef,
		private modalService: ModalService
	) {}

	ngOnInit() {}

	visualizar() {
		const modal = this.modalService.open(
			"Serviços disponíveis",
			ModalServicosDisponiveisComponent,
			PactoModalSize.LARGE,
			"modal-servicos-disponiveis"
		);
		modal.componentInstance.item = this.item;
	}

	get diaView() {
		return this.agendaStateService.view === AgendaView.DIA;
	}

	get servicoView() {
		return this.agendaStateService.view === AgendaView.SERVICO;
	}
}
