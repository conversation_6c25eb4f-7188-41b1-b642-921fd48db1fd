<div
	class="wrapper"
	*ngIf="
		!!tipoAgendamentoDuracao?.ambienteDisponivel;
		else ambienteIndisponivel
	">
	<div class="row">
		<div class="col-md-6">
			<div class="input-nome">Aluno*</div>
			<pacto-cat-select-filter
				[control]="agendamentoFg.get('aluno')"
				[endpointUrl]="alunoUrl"
				[id]="'aluno-adicionar-agendamento'"
				[labelKey]="'nome'"
				[paramBuilder]="alunoParamBuilder"
				[resposeParser]="resposeParser"
				(click)="onValueChange($event)"
				id="alunos-adicionar-agendamento"></pacto-cat-select-filter>
		</div>
		<div class="col-md-6">
			<div class="input-nome">Professor*</div>
			<pacto-cat-select-filter
				[control]="profFc"
				[id]="'professor-adicionar-agendamento'"
				[labelKey]="'nome'"
				[options]="professoresDisponiveis"
				[resposeParser]="resposeParser"
				id="professores-adicionar-agendamento"></pacto-cat-select-filter>
		</div>
	</div>
	<div class="row">
		<div class="col-md-4">
			<div class="input-nome">Horário inicial</div>
			<pacto-input
				[control]="agendamentoFg.get('horarioInicial')"
				[ngClass]="{
					error:
						!agendamentoFg.get('horarioInicial').valid &&
						agendamentoFg.get('horarioInicial').touched
				}"
				[textMask]="{ guide: true, mask: timeMask }"
				id="hora-inicial-adicionar-agendamento"
				type="text"></pacto-input>
		</div>
		<div class="col-md-4">
			<div class="input-nome">Duração (minutos)</div>
			<pacto-input
				[control]="agendamentoFg.get('duracao')"
				[ngClass]="{
					error:
						!agendamentoFg.get('duracao').valid &&
						agendamentoFg.get('duracao').touched
				}"
				[textMask]="{ guide: false, mask: duracaoMask }"
				id="duracao-adicionar-agendamento"
				type="text"></pacto-input>
		</div>
		<div class="col-md-4">
			<div class="input-nome">Horário final</div>
			<pacto-input
				[disabled]="'true'"
				[control]="agendamentoFg.get('horarioFinal')"
				type="text"></pacto-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6">
			<div class="input-nome">Comportamento</div>
			<pacto-input
				[disabled]="'true'"
				[control]="comportamentoFC"
				id="comportamento-agendamento"
				type="text"></pacto-input>
		</div>
		<div class="col-md-3">
			<div class="input-nome">Data</div>
			<pacto-cat-datepicker [formControl]="diaFC"></pacto-cat-datepicker>
		</div>
		<div class="col-md-3" *ngIf="renderRecorrentes">
			<div class="input-nome">Periodicidade</div>
			<pacto-select
				[id]="'tipotolerancia-select'"
				[nome]="'tipotolerancia'"
				[opcoes]="tiposPeriodicidade"
				[control]="agendamentoFg.get('opcPeriodicidade')"
				(change)="escolhaPeriodicidade()"></pacto-select>
		</div>
	</div>

	<div class="row">
		<div class="col-md-3" *ngIf="showRepetirACada">
			<div class="input-nome">Repetir a cada (Intervalo)</div>
			<ds3-number-field
				[class]="'number-input-grid'"
				[formControl]="agendamentoFg.get('nrVezes')"
				[min]="2"
				(decrease)="decreaseValue()"
				(increase)="increaseValue()"
				ds3Input></ds3-number-field>
		</div>

		<div class="col-md-3" *ngIf="showSemanaMes">
			<div class="input-nome">Semana/Mês</div>
			<pacto-select
				[id]="'semana-mes'"
				[nome]="'semana-mes'"
				[control]="agendamentoFg.get('opcSemanaOuMes')"
				[opcoes]="semanaMes"
				(change)="escolhaSemanaMes()"></pacto-select>
		</div>

		<div class="col-md-3" *ngIf="showOpcSemanaMes">
			<div class="input-nome">Semana do mês</div>
			<pacto-select
				[id]="'semana-do-mes'"
				[nome]="'semana-do-mes'"
				[control]="agendamentoFg.get('opcSemanaMes')"
				[opcoes]="tiposSemanaDoMes"></pacto-select>
		</div>
		<div class="col-md-6" *ngIf="showDiasDaSemana">
			<div class="input-nome">Sempre às (dia da semana que irá ocorrer)</div>
			<div class="form-check-inline">
				<div class="col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('seg')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('seg')"
						[label]="'Seg'"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-15 col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('ter')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('ter')"
						[label]="'Ter'"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-15 col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('qua')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('qua')"
						[label]="'Qua'"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-15 col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('qui')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('qui')"
						[label]="'Qui'"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-15 col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('sex')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('sex')"
						[label]="'Sex'"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-15 col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('sab')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('sab')"
						[label]="'Sab'"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-15 col-md-1">
					<pacto-cat-checkbox
						(click)="escolherTipoDeAutorizacao('dom')"
						[checkBlue]="true"
						[control]="agendamentoFg.get('dom')"
						[label]="'Dom'"></pacto-cat-checkbox>
				</div>
			</div>
		</div>
	</div>

	<div class="row" *ngIf="renderTerminaEm">
		<div class="col-md-12">
			<div class="input-nome" style="margin-bottom: -15px">Terminar em</div>
			<div style="display: flex">
				<div style="display: flex; align-items: center; margin-right: 70px">
					<input
						(click)="escolhaTerminarEm(1)"
						class="radio_dias"
						id="radio_ate"
						name="escolha"
						type="radio" />
					<label for="radio_ate" style="white-space: nowrap">
						<span class="label_dias">Até o final do contrato</span>
					</label>
				</div>
				<div style="display: flex; align-items: center">
					<input
						(click)="escolhaTerminarEm(2)"
						class="radio_dias"
						id="radio_em"
						name="escolha"
						type="radio" />
					<label for="radio_em">
						<span class="label_dias">Em</span>
					</label>
				</div>
				<div style="padding-top: 15px">
					<pacto-datepicker
						[disabled]="showDataTerminar"
						[control]="agendamentoFg.get('dataFim')"
						(keyup)="changeDate()"
						(click)="changeDate()"></pacto-datepicker>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<div class="actions">
				<span>
					<pacto-cat-button
						(click)="salvarHandlerRecorrente()"
						[disabled]="error || !valid || !erroFimContrato"
						[full]="true"
						[label]="'Salvar agendamento'"
						id="salvar-adicionar-agendamento"
						size="LARGE"></pacto-cat-button>
				</span>
			</div>
		</div>
	</div>

	<div>
		<div *ngIf="msgError(error)" class="error-block">
			<div *ngIf="msgError(error)" class="upper">
				<i class="pct pct-alert-triangle"></i>
				Erro
			</div>
			<div *ngIf="msgError(error)" class="error-msg">
				<span>
					{{ msgError(error) }}
				</span>
			</div>
		</div>

		<div
			*ngIf="
				!msgError(error) &&
				erroDuracao(agendamentoFg.get('duracao').value) &&
				agendamentoFg.get('duracao').touched
			"
			class="error-block">
			<div class="upper">
				<i class="pct pct-alert-triangle"></i>
				Erro
			</div>
			<div class="error-msg">
				<span>
					· Esse tipo de atividade deve ter entre
					{{ tipoAgendamentoDuracao.min }} e {{ tipoAgendamentoDuracao.max }}
					minutos !
				</span>
			</div>
		</div>
	</div>
</div>

<ng-template #ambienteIndisponivel>
	<div class="toast bg-light-warning mt-4">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Você não pode agendar este serviço, pois o ambiente onde ele seria
			realizado já está ocupado.
		</span>
	</div>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@agendamento-modal-mensagens:create:validacaoalunoagendamento"
		xingling="validacaoalunoagendamento">
		O aluno já realizou o número limite de agendamentos dentro do período de
		dias informado no Tipo de Evento.
	</span>
	<span
		i18n="
			@@agendamento-modal-mensagens:create:validacaoalunoagendamentofaltaemintervalodias"
		xingling="validacaoalunoagendamentofaltaemintervalodias">
		O aluno possui uma falta dentro do intervalo de dias configurado no Tipo de
		Evento.
	</span>
	<span
		i18n="@@agendamento:create:aguardando-confirmacao"
		xingling="AGUARDANDO_CONFIRMACAO">
		Aguardando Confirmação
	</span>
	<span i18n="@@agendamento:create:confirmado" xingling="CONFIRMADO">
		Confirmado
	</span>
	<span i18n="@@agendamento:create:executado" xingling="EXECUTADO">
		Executado
	</span>
	<span i18n="@@agendamento:create:faltou" xingling="FALTOU">Faltou</span>
	<span
		i18n="@@agendamento:valid:plano"
		xingling="VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO">
		O cliente não possui o plano de validação
	</span>
	<span
		i18n="@@agendamento:valid:produto"
		xingling="VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO">
		O cliente não possui o produto de validação
	</span>
	<span
		i18n="@@agendamento:valid:sem:disponibilidade:horario"
		xingling="SEM_DISPONIBILIDADE_HORARIO">
		O professor {{ professor }} não possui disponibilidade neste horário
	</span>
	<span
		i18n="@@agendamento:valid:intervalo:dias"
		xingling="VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS">
		Não foi possivel realizar o agendamento, intervalo de dias atingido
	</span>
	<span
		i18n="@@agendamento:valid:intervalo:dias:faltou"
		xingling="VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS_FALTOU">
		Não foi possivel realizar o agendamento, intervalo de dias por falta
		atingido
	</span>
	<span i18n="@@agendamento:erro:salvar" xingling="erro_salvar_agendamento">
		Erro ao salvar agendamento. Verifique os dados informados e tente novamente.
	</span>
</pacto-traducoes-xingling>
