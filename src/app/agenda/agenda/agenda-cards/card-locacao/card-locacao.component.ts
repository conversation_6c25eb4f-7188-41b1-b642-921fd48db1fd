import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { AgendaView } from "treino-api";
import { Router } from "@angular/router";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { AgendaServicosStateService } from "../../agenda-servicos/agenda-servicos-state.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalServicosDisponiveisComponent } from "../modal-servicos-disponiveis/modal-servicos-disponiveis.component";

@Component({
	selector: "pacto-card-locacao",
	templateUrl: "./card-locacao.component.html",
	styleUrls: ["./card-locacao.component.scss"],
})
export class CardLocacaoComponent implements OnInit {
	@Input() day;
	@Input() item;

	constructor(
		private router: Router,
		private state: AgendaServicosStateService,
		private agendaStateService: AgendaCardsStateService,
		private cd: ChangeDetectorRef,
		private modalService: ModalService
	) {}

	ngOnInit() {}

	visualizar() {
		const modal = this.modalService.open(
			"Locações disponíveis",
			ModalServicosDisponiveisComponent,
			PactoModalSize.LARGE,
			"modal-servicos-disponiveis"
		);
		modal.componentInstance.item = this.item;
	}

	get diaView() {
		return this.agendaStateService.view === AgendaView.DIA;
	}

	get servicoView() {
		return this.agendaStateService.view === AgendaView.SERVICO;
	}
}
