import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import {
	TreinoApiLocacaoService,
	DetalhesLocacao,
	LocacaoProdutoSugerido,
} from "treino-api";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalCancelarAgendamentoLocacaoComponent } from "../modal-cancelar-agendamento-locacao/modal-cancelar-agendamento-locacao.component";
import { ModalReagendarLocacaoComponent } from "../modal-reagendar-locacao/modal-reagendar-locacao.component";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { ModalFinalizarVendaLocacaoComponent } from "../modal-finalizar-venda-locacao/modal-finalizar-venda-locacao.component";
import { ItemVendaAvulsa, VendaAvulsa } from "adm-core-api";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";
import { TraducoesXinglingComponent } from "../../../../../../projects/ui/src/lib/components/traducoes-xingling/traducoes-xingling.component";
import { Api } from "sdk";
import { AdmRestService } from "../../../../../../projects/adm/src/app/adm-rest.service";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-detalhamento-locacao",
	templateUrl: "./detalhamento-locacao.component.html",
	styleUrls: ["./detalhamento-locacao.component.scss"],
})
export class DetalhamentoLocacaoComponent implements OnInit {
	@ViewChild("tableServicosComponent", { static: false })
	tableServicosComponent: RelatorioComponent;
	@ViewChild("tableHorariosComponent", { static: false })
	tableHorariosComponent: RelatorioComponent;
	@ViewChild("textRelacaoCinturaQuadril", { static: true })
	textRelacaoCinturaQuadril;
	traducoes: TraducoesXinglingComponent;

	codigosAgendamentoLocacao;
	detalheLocacao: DetalhesLocacao;
	totalAdicionais = 0.0;
	total = 0.0;
	totalLocacao = 0.0;
	tableHorarios: PactoDataGridConfig;
	tableServicos: PactoDataGridConfig;
	isTipoHorarioLivre = false;
	tempoHorarioLivre: string;
	id;
	data;

	constructor(
		private treinoApiLocacaoService: TreinoApiLocacaoService,
		private snotifyService: SnotifyService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private modalService: ModalService,
		private router: Router,
		private rest: RestService,
		private treinoConfigService: TreinoConfigCacheService,
		private util: AgendaUtilsService,
		private admRest: AdmRestService,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			if (params && params.cod) {
				this.id = params.cod;
				this.data = params.dia;
				this.codigosAgendamentoLocacao = [params.cod];
				this.loadDetalhesAgendamentoLocacao();
			}
		});
	}

	loadDetalhesAgendamentoLocacao() {
		this.treinoApiLocacaoService
			.detalhesAgendamentoLocacao(this.codigosAgendamentoLocacao[0], this.data)
			.subscribe({
				error: (error) => {
					this.snotifyService.error(error.error.meta.message);
				},
				next: (dados) => {
					this.detalheLocacao = dados;
					if (!this.detalheLocacao.aluno.imageUri) {
						this.detalheLocacao.aluno.imageUri = null;
					}
					this.totalLocacao = this.detalheLocacao.valorLocacao;
					this.total = this.detalheLocacao.valorTotal;
					this.filtrarProdutosDuplicados();
					this.isTipoHorarioLivre = this.detalheLocacao.tipoHorario === "LIVRE";
					this.initTableHorarios();
					this.initTableServicos();
					setTimeout(() => {
						this.calcularTotal();
						this.calcularDuracaoHorarioLivre();
						this.cd.detectChanges();
					}, 1000);
					this.cd.detectChanges();
				},
			});
	}

	filtrarProdutosDuplicados() {
		this.detalheLocacao.produtosObrigatorios = this.removerDuplicatas(
			this.detalheLocacao.produtosObrigatorios
		);
		this.detalheLocacao.produtosSugeridos =
			this.detalheLocacao.produtosSugeridos.filter(
				(sugerido) =>
					!this.detalheLocacao.produtosObrigatorios.some(
						(obrigatorio) => obrigatorio.codigo === sugerido.codigo
					)
			);
	}

	public initTableHorarios() {
		this.tableHorarios = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				horario: new FormControl(),
				duracao: new FormControl(),
			}),
			dataAdapterFn: (serverData) => {
				const content = [];
				if (this.detalheLocacao.horariosAdicionados) {
					this.detalheLocacao.horariosAdicionados.forEach((item) => {
						content.push({
							horario: item.horaInicio,
							duracao: this.getDuracaoEmMinutos(item.horaInicio, item.horaFim),
							locacaoValor: item.valorLocacao,
							adicionaisObrigatorios: item.valorExtrasObrigatorios,
							extrasAdicionais: item.valorExtrasAdicionais,
							total: item.valorTotal,
							status: item.finalizado,
							isHorarioExtra: false,
							vendaAvulsaCodigo: item.vendaAvulsaCodigo,
							showDelete: false,
							showEdit: false,
						});
					});
				}
				return { content };
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmHorarios(row, form, data, rowIndex),
			showDelete: (row, isAdd) => true,
			showEdit: (row, isAdd) => true,
			columns: [
				...this.generateColumns(),
				{
					nome: "locacaoValor",
					titulo: "Valor da locação",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "adicionaisObrigatorios",
					titulo: "Adicionais obrigatórios",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "extrasAdicionais",
					titulo: "Extras adicionais",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "total",
					titulo: "Total",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "status",
					titulo: "Status",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => (!!v ? "Finalizado" : "Em aberto"),
				},
			],
		});
		this.cd.detectChanges();
	}

	private generateColumns(): any[] {
		if (this.detalheLocacao.tipoHorario === "LIVRE") {
			return [
				{
					nome: "horario",
					titulo: "Horário",
					visible: true,
					editable: true,
					inputType: "text",
					inputTextMask: {
						mask: [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/],
						guide: false,
					},
					width: "150px",
				},
				{
					nome: "duracao",
					titulo: "Duração",
					inputType: "number",
					inputSelectData: this.duracaoHorarioLivre,
					visible: true,
					ordenavel: false,
					editable: true,
					width: "13%",
					showSelectFilter: false,
					showAddSelectBtn: false,
					valueTransform: (v) => `${v} minutos`,
				},
			];
		} else {
			return [
				{
					nome: "horario",
					titulo: "Horário",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					selectOptionChange: (option, form, row) => {
						row.codigo = option.codigo;
						row.horaInicio = option.horaInicio;
						row.horaFim = option.horaFim;
						row.inicioFim = option.inicioFim;
						row.duracaoDescricao = this.formatoDuracao(
							option.horaInicio,
							option.horaFim
						);
					},
					width: "150px",
					idSelectKey: "codigo",
					labelSelectKey: "horaInicio",
					objectAttrLabelName: "horaInicio",
					inputSelectData: this._filtrarHorariosDisponiveis(),
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "duracao",
					titulo: "Duração",
					inputType: "number",
					inputSelectData: this.duracaoHorarioLivre,
					visible: true,
					editable: false,
					valueTransform: (v) => `${v} minutos`,
				},
			];
		}
	}

	public initTableServicos() {
		this.tableServicos = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: this.admRest.buildFullUrl(
				"produtos/com-estoque",
				false,
				Api.MSPRODUTO
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			formGroup: new FormGroup({
				nomeProduto: new FormControl(),
				quantidade: new FormControl(),
			}),
			dataAdapterFn: ({ content }) => {
				const produtos = [];
				if (this.detalheLocacao.produtosObrigatorios) {
					this.detalheLocacao.produtosObrigatorios.forEach((item) => {
						produtos.push({
							nomeProduto: item,
							valor: item.valor,
							obrigatorio: item.obrigatorio,
							quantidade: item.quantidade,
							valorTotal: item.quantidade.id * item.valor,
							showDelete: false,
						});
					});
				}
				if (content.length > 0) {
					content.forEach((item: any) => {
						this.detalheLocacao.produtosSugeridos.push({
							codigo: item.codigo,
							nomeProduto: item.descricao,
							valor: item.valorFinal,
							obrigatorio: false,
							quantidade: { id: 1, label: "1" },
						});
					});
				}
				return { content: produtos };
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			showEdit: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "nomeProduto",
					titulo: "Nome",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					selectOptionChange: (option, form, row) => {
						form.get("quantidade").setValue({ id: 1, label: 1 });
						row.quantidade = { id: 1, label: 1 };
						row.valor = option.valor;
						row.valorTotal = option.valor;
					},
					idSelectKey: "codigo",
					labelSelectKey: "nomeProduto",
					objectAttrLabelName: "nomeProduto",
					inputSelectData: this.detalheLocacao.produtosSugeridos,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "quantidade",
					titulo: "Quantidade",
					inputType: "select",
					idSelectKey: "id",
					labelSelectKey: "label",
					objectAttrLabelName: "label",
					inputSelectData: this.qtds,
					selectOptionChange: (option, form, row) => {
						row.valorTotal = row.valor * option.id;
					},
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "valor",
					titulo: "Valor unitário",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "valorTotal",
					titulo: "Valor total",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
			],
		});
		this.cd.detectChanges();
	}

	get qtds() {
		const array = [];
		for (let i = 1; i <= 20; i++) {
			array.push({ id: i, label: `${i}` });
		}
		return array;
	}

	beforeConfirmHorarios(row, form, data, rowIndex): boolean {
		if (
			this.detalheLocacao.tipoHorario === "LIVRE" &&
			row.duracao < this.detalheLocacao.tempoMinimoMinutos
		) {
			this.snotifyService.warning(
				`O tempo minímo é de ${this.detalheLocacao.tempoMinimoMinutos} minutos.`
			);
			return;
		}
		if (row.duracao === null) {
			row.duracao = this.getDuracaoEmMinutos(row.horaInicio, row.horaFim);
		}
		row.showEdit = false;
		row.showDelete = true;

		this.tableServicosComponent.rawData.forEach((item: any) => {
			if (!!item.obrigatorio) {
				item.quantidade = {
					id: item.quantidade.id++,
					label: `${item.quantidade.id}`,
				};
			}
		});
		this._reapresentarOpcoesDoSelect();
		this.cd.detectChanges();
		return true;
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this._reapresentarOpcoesDoSelect();
		this.cd.detectChanges();
		this.calcularTotal();
		return true;
	}

	showDelete(row, isAdd): boolean {
		return row.showDelete;
	}

	showEdit(row, isAdd): boolean {
		return row.showEdit;
	}

	public transformMoneyValue(valor: number) {
		const valorFormatado = Number(valor).toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
		return "R$ " + valorFormatado;
	}

	private getDuracaoEmMinutos(horaInicio: string, horaFim: string) {
		return (
			this.util.convertTimeLabelIntoMinutes(horaFim) -
			this.util.convertTimeLabelIntoMinutes(horaInicio)
		);
	}

	normalizarProduto(item: any): LocacaoProdutoSugerido {
		if (!item || !item.nomeProduto || !item.nomeProduto.produto) {
			return {
				codigo: item.nomeProduto.codigo || 0,
				nomeProduto: item.nomeProduto.nomeProduto || "",
				produto: {
					codigo: item.nomeProduto.codigo || 0,
					descricao: item.nomeProduto.nomeProduto || "",
					valorFinal: item.nomeProduto.valor || 0,
				},
				valor: item.valorTotal,
				obrigatorio: false,
				quantidade: {
					id: item.quantidade.id,
					label: `${item.quantidade.label}`,
				},
			};
		}

		return {
			codigo: item.nomeProduto.codigo,
			nomeProduto: item.nomeProduto.nomeProduto,
			produto: {
				codigo: item.nomeProduto.produto.codigo,
				descricao: item.nomeProduto.produto.descricao,
				valorFinal: item.valor * (item.quantidade.id || 1),
			},
			valor: item.valor,
			obrigatorio: item.nomeProduto.obrigatorio,
			quantidade: item.quantidade,
		};
	}

	agendarHorario(novoHorario: any[] = []) {
		this.treinoApiLocacaoService
			.agendar(this.data, this.getDto(novoHorario))
			.subscribe(
				(response) => {
					this.codigosAgendamentoLocacao.push(response.retorno);
					if (response && !response.erro) {
						this.snotifyService.success("Horário locado com sucesso");
						this._reapresentarOpcoesDoSelect();
					} else {
						const message = response.erro.split(":");
						this.snotifyService.error(message[1].trim());

						this.tableHorariosComponent.rawData.splice(
							this.tableHorariosComponent.rawData.length - 1,
							1
						);
					}

					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					}
				}
			);
	}

	confirmHorario({ row }: any) {
		row.locacaoValor = this.calcularValorLocacao(
			this.detalheLocacao.valorHora,
			row.duracao
		);
		row.adicionaisObrigatorios = this.detalheLocacao.valorExtrasObrigatorios;
		row.total = row.locacaoValor + row.adicionaisObrigatorios;
		row.isHorarioExtra = true;
		row.showEdit = false;
		row.showDelete = true;

		this.agendarHorario([row]);

		this.cd.detectChanges();
	}

	removeHorario(event) {
		if (this.tableHorariosComponent.rawData) {
			this.cancelarAgendamentoLocacao(
				this.codigosAgendamentoLocacao[event.index]
			);
			this.tableHorariosComponent.rawData.splice(event.index, 1);

			this._reapresentarOpcoesDoSelect();
			this.calcularTotal();
			this.cd.detectChanges();
		}
	}

	confirmServico({ row }: any) {
		const produto = this.normalizarProduto(row);
		const produtoExiste = this.detalheLocacao.produtosObrigatorios.some(
			(p) => p.codigo === produto.codigo
		);
		if (produto.codigo !== 0 && !produtoExiste) {
			this.detalheLocacao.produtosObrigatorios.push(produto);
			this.detalheLocacao.produtosObrigatorios = this.removerDuplicatas(
				this.detalheLocacao.produtosObrigatorios
			);
			this.detalheLocacao.produtosSugeridos =
				this.detalheLocacao.produtosSugeridos.filter(
					(p) => p.codigo !== produto.codigo
				);
			this.tableServicos.columns.find(
				(column) => column.nome === "nomeProduto"
			).inputSelectData = this.detalheLocacao.produtosSugeridos;
			this.calcularTotal();
		}
		this.cd.detectChanges();
	}

	removerDuplicatas(produtos: any[]): any[] {
		return produtos.filter(
			(produto, index, self) =>
				index === self.findIndex((p) => p.codigo === produto.codigo)
		);
	}

	removeServico(event) {
		if (this.tableServicosComponent.rawData) {
			this.tableServicosComponent.rawData.splice(event.index, 1);
			const produtoRemovido = this.normalizarProduto(event.row);
			if (produtoRemovido.codigo !== 0) {
				this.detalheLocacao.produtosObrigatorios =
					this.detalheLocacao.produtosObrigatorios.filter(
						(p) => p.codigo !== produtoRemovido.codigo
					);

				this.detalheLocacao.produtosSugeridos.push({
					codigo: produtoRemovido.codigo,
					produto: produtoRemovido.produto,
					valor: produtoRemovido.valor,
					obrigatorio: produtoRemovido.obrigatorio,
					quantidade: { id: 1, label: "1" },
					nomeProduto: produtoRemovido.nomeProduto,
				});

				this.tableServicos.columns.find(
					(column) => column.nome === "nomeProduto"
				).inputSelectData = this.detalheLocacao.produtosSugeridos;
			}

			this.calcularTotal();
			this.cd.detectChanges();
		}
	}

	get urlLog(): string {
		return this.rest.buildFullUrl("log/locacoes");
	}

	get valorLocacao() {
		return this.transformMoneyValue(this.totalLocacao);
	}

	get valorExtrasObrigatorios() {
		return this.transformMoneyValue(
			this.detalheLocacao ? this.detalheLocacao.valorExtrasObrigatorios : 0.0
		);
	}

	public isAddRowAvailable(): boolean {
		const horariosParaExcluir = this.detalheLocacao.horariosAdicionados.map(
			(it) => it.codigo
		);

		if (this.detalheLocacao.tipoHorario === "LIVRE") {
			return true;
		} else {
			return (
				this.detalheLocacao.horarios.filter(
					(it) =>
						!(horariosParaExcluir.includes(it.codigo) || it.possuiAgendamentos)
				).length > 0
			);
		}
	}

	private _reapresentarOpcoesDoSelect() {
		this.tableHorarios.columns
			.filter((it) => it.nome === "horario")
			.map((it) => (it.inputSelectData = this._filtrarHorariosDisponiveis()));
	}

	_filtrarHorariosDisponiveis(): any[] {
		const horariosParaExcluir = this.detalheLocacao.horariosAdicionados.map(
			(it) => it.codigo
		);
		return this.detalheLocacao.horarios.filter(
			(it) =>
				!(horariosParaExcluir.includes(it.codigo) || it.possuiAgendamentos)
		);
	}

	get valorTotal() {
		return this.transformMoneyValue(this.total);
	}

	calcularTotal(): void {
		this.totalAdicionais = 0.0;
		let valorTotalObrigatorio = 0;

		this.tableServicosComponent.rawData.forEach(
			(item: LocacaoProdutoSugerido) => {
				if (!item.obrigatorio) {
					this.totalAdicionais += item.valor * (item.quantidade.id || 1);
				} else {
					valorTotalObrigatorio += item.valor * Number(item.quantidade.label);
				}
			}
		);

		this.detalheLocacao.valorExtrasObrigatorios = valorTotalObrigatorio;

		this.totalLocacao = this.detalheLocacao.valorLocacao || 0;
		this.total = Number(
			this.totalLocacao +
				(this.detalheLocacao.valorExtrasObrigatorios || 0) +
				this.totalAdicionais
		);

		this.detalheLocacao.valorTotal = this.total;
		this.cd.detectChanges();
	}

	cancelarAgendamentoLocacao(codigo = null) {
		const codigoParaCancelar =
			codigo === null ? this.codigosAgendamentoLocacao[0] : codigo;
		const titulo = "Cancelar locação";
		const horaAtual = new Date();
		const [hora, minuto] = this.detalheLocacao.horariosAdicionados[0].horaInicio
			.split(":")
			.map(Number);
		const [dia, mes, ano] = this.detalheLocacao.data.split("/").map(Number);
		const dataLocacao = new Date(ano, mes - 1, dia, hora, minuto, 0, 0);
		dataLocacao.setHours(hora, minuto, 0, 0);

		const antecedenciaMinimaEmMinutos = this.treinoConfigService
			.configuracoesGerais.minutos_cancelar_com_antecedencia
			? parseInt(
					this.treinoConfigService.configuracoesGerais
						.minutos_cancelar_com_antecedencia,
					10
			  )
			: 0;

		const diffEmMilissegundos = dataLocacao.getTime() - horaAtual.getTime();
		const diffEmMinutos = diffEmMilissegundos / 60000;

		if (diffEmMinutos < antecedenciaMinimaEmMinutos && diffEmMinutos >= 0) {
			this.snotifyService.error(
				`O cancelamento não é permitido. O tempo mínimo de antecedência para cancelar é de ${antecedenciaMinimaEmMinutos} minutos.`
			);
			return;
		}

		if (diffEmMinutos < 0) {
			this.snotifyService.error(
				"O cancelamento não é permitido. A locação já começou."
			);
			return;
		}

		const modal = this.modalService.open(
			titulo,
			ModalCancelarAgendamentoLocacaoComponent
		);
		modal.result.then((result) => {
			this.treinoApiLocacaoService
				.cancelarAgendamentoLocacao(codigoParaCancelar, result)
				.subscribe({
					next: (dados) => {
						this.detalheLocacao.cancelado = true;
						this.detalheLocacao.justificativa = dados.justificativa;
						this.detalheLocacao.usuarioCancelamento = dados.usuarioCancelamento;
						this.detalheLocacao.dataCancelamento = dados.data;
						this.cd.detectChanges();
						this.snotifyService.success("Locação cancelada com sucesso.");
					},
					error: (error) => {
						this.snotifyService.error(error.error.meta.message);
					},
				});
		});
	}

	reagendarLocacao() {
		const titulo = "Reagendar locação";
		const modal = this.modalService.open(
			titulo,
			ModalReagendarLocacaoComponent,
			PactoModalSize.LARGE,
			"modal-reagendar-locacao"
		);
		modal.result.then((result) => {
			if (result) {
				this.router.navigate([
					"agenda",
					"painel",
					"reagendar-locacao",
					this.codigosAgendamentoLocacao[0],
				]);
			}
		});
	}

	calcularDuracaoHorarioLivre() {
		const horario = this.detalheLocacao.horariosAdicionados[0];
		const horaInicio = horario.horaInicio;
		const horaFim = horario.horaFim;

		this.tempoHorarioLivre = this.formatoDuracao(horaInicio, horaFim);
		this.cd.detectChanges();
	}

	calcularValorLocacao(valorHora: number, minutos: number): number {
		return (valorHora / 60) * minutos;
	}

	get duracaoHorarioLivre(): any {
		return this.tempoHorarioLivre;
	}

	private formatoDuracao(startTime: string, endTime: string): string {
		const inicio = new Date(`1970-01-01T${startTime}:00Z`);
		const fim = new Date(`1970-01-01T${endTime}:00Z`);
		const duracao = (fim.getTime() - inicio.getTime()) / 60000;

		const horas = Math.floor(duracao / 60);
		const minutos = duracao % 60;

		return `${horas.toString().padStart(2, "0")}:${minutos
			.toString()
			.padStart(2, "0")}`;
	}

	finalizarAgendamentoLocacao() {
		//Verificação se o tipo da locação é LIVRE e se o tempo mínimo foi alcançado
		if (this.detalheLocacao.tipoHorario === "LIVRE") {
			const horaAtual = new Date();
			const horaInicioLocacao = new Date(
				`1970-01-01T${this.detalheLocacao.horariosAdicionados[0].horaInicio}:00Z`
			);
			const diffEmMilissegundos =
				horaAtual.getTime() - horaInicioLocacao.getTime();
			const diffEmMinutos = diffEmMilissegundos / 60000;

			if (diffEmMinutos < this.detalheLocacao.tempoMinimoMinutos) {
				this.snotifyService.error(
					`A locação só pode ser finalizada após ${this.detalheLocacao.tempoMinimoMinutos} minutos do início.`
				);
				return;
			}
		}

		const confirmModal = this.modalService.confirm(
			`Finalizar Locação`,
			"Tem certeza que deseja finalizar essa locação?",
			"Finalizar",
			"btn-primary"
		);

		confirmModal.result.then(async () => {
			let gerarVendas = [];
			let redirectToCaixaEmAberto = false;

			await Promise.all(
				this.codigosAgendamentoLocacao.map((codigo: any) =>
					this.treinoApiLocacaoService
						.finalizarAgendamentoLocacao(
							codigo,
							this.getDto(),
							this.detalheLocacao.data
						)
						.toPromise()
						.then((redirect: boolean) => {
							this.detalheLocacao.finalizado = true;
							gerarVendas.push(true);
							if (redirect === true) {
								redirectToCaixaEmAberto = redirect;
							}
						})
						.catch((error) => {
							gerarVendas.push(false);
							this.snotifyService.error(error.error.meta.message);
						})
				)
			);

			if (gerarVendas.length > 0 && gerarVendas.every((v) => v === true)) {
				this.snotifyService.success("Locação efetuada com sucesso!");
				if (redirectToCaixaEmAberto) {
					const usuarioOamd = this.sessionService.loggedUser.username;
					const empresa = this.sessionService.empresaId;
					this.clientDiscoveryService
						.linkZw(usuarioOamd, empresa)
						.subscribe((result) => {
							window.open(
								result +
									"&urlRedirect=caixaEmAberto_" +
									this.detalheLocacao.aluno.nome
							);
						});
				}
				this.router.navigate(["/agenda/painel/cards"]);
			} else {
				this.snotifyService.error("Erro ao gerar parcelas da locação!");
			}
		});
	}

	abrirModalFluxoFinanceiro() {
		const titulo = "Opções de pagamento";
		const modal = this.modalService.open(
			titulo,
			ModalFinalizarVendaLocacaoComponent,
			PactoModalSize.LARGE,
			"modal-finalizar-venda-locacao"
		);
		modal.componentInstance.detalheLocacaoFinanceiro = this.detalheLocacao;
		modal.componentInstance.dtoMontado = this.montarDto();
	}

	montarDto(): VendaAvulsa {
		return {
			pessoa: this.detalheLocacao.codigoPessoaClienteZW
				? this.detalheLocacao.codigoPessoaClienteZW
				: null,
			nomeComprador: this.detalheLocacao.aluno
				? this.detalheLocacao.aluno.nome
				: "",
			tipo: 0,
			lancamento: Date.now(),
			parcelas: 1,
			primeiraParcela: null,
			colaborador: false,
			descontoGeral: 0,
			itens: this.itens(),
		};
	}

	itens(): Array<ItemVendaAvulsa> {
		const itensVenda: Array<ItemVendaAvulsa> = [];

		// Adiciona os produtos obrigatórios
		if (this.detalheLocacao.produtosObrigatorios) {
			this.detalheLocacao.produtosObrigatorios.forEach((produto) => {
				const qtd = produto.quantidade ? Number(produto.quantidade.id) : 1;
				itensVenda.push({
					codigoProduto: produto.produto ? produto.produto.codigo : 0,
					precoProduto: produto.valor || 0,
					qtd,
					valorParcial: produto.valor * qtd || 0,
					descontoManual: 0,
					pontos: 0,
					descricaoProduto: produto.produto ? produto.produto.descricao : "",
					pacoteEscolhido: null,
				});
			});
		}

		if (this.detalheLocacao.horariosAdicionados) {
			this.detalheLocacao.horariosAdicionados.forEach((horario) => {
				const minutosAgendados =
					this.getDuracaoEmMinutos(horario.horaInicio, horario.horaFim) || 0;
				itensVenda.push({
					codigoProduto: this.detalheLocacao.codigoProduto || 0,
					precoProduto: this.detalheLocacao.valorLocacao || 0,
					qtd: 1,
					valorParcial:
						(this.detalheLocacao.valorHora / 60) * minutosAgendados || 0,
					descontoManual: 0,
					pontos: 0,
					descricaoProduto: this.detalheLocacao.produto || "",
					pacoteEscolhido: null,
				});
			});
		}

		return itensVenda;
	}

	adicionarMinutos(horario: string, minutos: number): string {
		const [hora, minuto] = horario.split(":").map(Number);
		const data = new Date();
		data.setHours(hora, minuto);
		data.setMinutes(data.getMinutes() + minutos);

		// Retorna no formato HH:mm
		return data.toTimeString().slice(0, 5);
	}

	private getDto(novoHorario: any[] = []) {
		const dto: any = {};
		dto.codigoPessoa = this.detalheLocacao.codigoPessoaClienteZW;
		dto.matricula = this.detalheLocacao.aluno.matriculaZW;
		dto.valorTotal = this.detalheLocacao.valorTotal;
		dto.valorLocacao = this.detalheLocacao.valorLocacao;
		dto.valorHora = this.detalheLocacao.valorHora;
		// @ts-ignore
		dto.ambiente = this.detalheLocacao.ambiente.id;
		dto.servicos = [];
		[...this.detalheLocacao.produtosObrigatorios].forEach((item) => {
			const obj: any = {
				codigo: item.codigo,
				produto: item.produto,
				valor: item.valor,
				obrigatorio: item.obrigatorio,
				quantidade: item.quantidade,
				nomeProduto: item.nomeProduto,
			};
			dto.servicos.push(obj);
		});

		dto.horarios = [];
		if (novoHorario.length === 0) {
			[...this.detalheLocacao.horariosAdicionados]
				.filter((item: any) => !!item.isHorarioExtra)
				.forEach((item) => dto.horarios.push(item));
		} else {
			if (this.detalheLocacao.tipoHorario === "LIVRE") {
				novoHorario[0] = {
					...novoHorario[0],
					codigo: this.detalheLocacao.horariosAdicionados[0].codigo,
					horaInicio: novoHorario[0].horario,
					horaFim: this.adicionarMinutos(
						novoHorario[0].horario,
						novoHorario[0].duracao
					),
				};
			}
			dto.horarios = novoHorario;
		}

		return dto;
	}
}
