<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'Agenda / Detalhes da locação',
			categoryLink: ['agenda', 'painel', 'cards'],
			menu: 'Detalhes da locação'
		}"></pacto-breadcrumbs>

	<div
		*ngIf="detalheLocacao"
		class="table-wrapper pacto-shadow agendamento-locacao">
		<div class="cabecalho-detalhe-locacao">
			<div>
				<span class="nome-locacao">
					{{ detalheLocacao?.nomeLocacao | captalize : true }}
				</span>
			</div>

			<div class="span-info">
				<span>
					<i class="pct pct-tag"></i>
					{{ detalheLocacao?.produto | captalize : true }}
				</span>
				<span>
					<i class="pct pct-map-pin"></i>
					{{ detalheLocacao?.ambiente.nome | captalize : true }}
				</span>
				<span>
					<i class="pct pct-calendar"></i>
					{{ detalheLocacao?.data }}
				</span>
				<span>
					<i class="pct pct-clock"></i>
					{{ detalheLocacao?.horariosAdicionados[0]?.inicioFim }}
				</span>
			</div>
		</div>

		<div class="body-detalhe-locacao">
			<div class="content-aluno">
				<div class="avatar-wrapper display-flex">
					<pacto-cat-person-avatar
						[diameter]="64"
						[uri]="detalheLocacao?.aluno.imageUri"></pacto-cat-person-avatar>

					<div class="info-aluno">
						<div class="div-titulo">
							<span class="titulo">Aluno</span>
						</div>
						<div>
							<span class="info">{{ detalheLocacao?.aluno.nome }}</span>
						</div>
					</div>
				</div>

				<div class="info-aluno">
					<div class="div-titulo">
						<span class="titulo">Matrícula</span>
					</div>
					<div>
						<span class="info">{{ detalheLocacao?.aluno.matriculaZW }}</span>
					</div>
				</div>

				<div class="info-aluno">
					<div class="div-titulo">
						<span class="titulo">Situação</span>
					</div>
					<div>
						<span
							*ngIf="detalheLocacao?.aluno.situacaoAluno === 'ATIVO'"
							class="situacao-verde">
							Ativo
						</span>
						<span
							*ngIf="detalheLocacao?.aluno.situacaoAluno === 'VISITANTE'"
							class="situacao-amarelo">
							Visitante
						</span>
						<span
							*ngIf="detalheLocacao?.aluno.situacaoAluno === 'INATIVO'"
							class="situacao-vermelho">
							Inativo
						</span>
					</div>
				</div>

				<div class="info-aluno ultimo">
					<div class="div-titulo">
						<span class="titulo">Contato</span>
					</div>
					<div>
						<span class="info">
							{{ detalheLocacao?.aluno?.fones[0]?.numero }}
							<i class="pct pct-whatsapp whatsapp"></i>
						</span>
					</div>
				</div>
			</div>

			<div class="content-produto-servicos">
				<div class="lista-alunos">
					<pacto-cat-table-editable
						#tableHorariosComponent
						(confirm)="confirmHorario($event)"
						(delete)="removeHorario($event)"
						[isEditable]="
							!(detalheLocacao?.finalizado || detalheLocacao?.cancelado)
						"
						[newLineTitle]="'Adicionar horário extra'"
						[isAddRowAvailable]="isAddRowAvailable()"
						[showAddRow]="
							!(detalheLocacao?.finalizado || detalheLocacao?.cancelado)
						"
						[table]="tableHorarios"
						idSuffix="table-horarios"></pacto-cat-table-editable>
				</div>
			</div>

			<div class="content-produto-servicos">
				<div class="lista-alunos">
					<span class="titulo">Produtos e serviços</span>
					<pacto-cat-table-editable
						#tableServicosComponent
						(confirm)="confirmServico($event)"
						(delete)="removeServico($event)"
						[isEditable]="!detalheLocacao?.finalizado"
						[newLineTitle]="'Adicionar mais'"
						[showAddRow]="!detalheLocacao?.finalizado"
						[table]="tableServicos"
						idSuffix="table-servicos"></pacto-cat-table-editable>
				</div>
			</div>
			<div *ngIf="!detalheLocacao?.cancelado" class="actions">
				<button
					(click)="finalizarAgendamentoLocacao()"
					*ngIf="!detalheLocacao?.finalizado"
					class="btn btn-primary"
					i18n="@@buttons:salvar">
					Finalizar locação
				</button>

				<button
					(click)="reagendarLocacao()"
					*ngIf="!detalheLocacao?.finalizado"
					class="btn btn-secundary"
					i18n="@@buttons:salvar">
					Reagendar
				</button>

				<button
					(click)="cancelarAgendamentoLocacao()"
					*ngIf="!detalheLocacao?.finalizado"
					class="btn btn-secundary"
					i18n="@@buttons:salvar">
					Cancelar Locação
				</button>

				<button
					*ngIf="detalheLocacao?.finalizado"
					class="btn btn-primary"
					disabled>
					Finalizado
				</button>
				<pacto-log
					*ngIf="detalheLocacao?.finalizado"
					[url]="urlLog"></pacto-log>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>
