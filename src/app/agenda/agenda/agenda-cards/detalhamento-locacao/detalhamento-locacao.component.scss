@import "src/assets/scss/pacto/plataforma-import.scss";

.cabecalho-detalhe-locacao {
	display: flex;
	justify-content: space-between;
	padding: 32px 16px;
	gap: 0px;
	border-bottom: 1px solid $cinza02;

	.nome-locacao {
		color: $pretoPri;
		font-family: Poppins;
		font-size: 14px;
		font-weight: 600;
		line-height: 17.5px;
		letter-spacing: 0.25px;
		text-align: left;
	}

	.span-info {
		color: $cinza06;
		font-family: Nunito Sans;
		font-size: 14px;
		font-weight: 400;
		line-height: 17.5px;

		i {
			margin-right: 4px;
		}

		span {
			margin-left: 32px;
		}
	}
}

.body-detalhe-locacao {
	padding: 32px 16px;

	.content-aluno {
		display: flex;
		justify-content: space-between;
		padding: 8px 16px 8px 16px;
		border-radius: 6px;
		border: 1px solid $cinza02;

		.info-aluno {
			margin-left: 16px;

			.div-titulo {
				margin-bottom: 4px;
			}

			.titulo {
				font-family: N<PERSON><PERSON> Sans;
				font-size: 14px;
				font-weight: 400;
				line-height: 17.5px;
				text-align: left;
				color: $preto05;
			}

			.info {
				font-family: Poppins;
				font-size: 14px;
				font-weight: 500;
				line-height: 14px;
				letter-spacing: 0.25px;
				text-align: left;
				color: $preto03;
			}
		}

		.ultimo {
			margin-right: 40px;
		}
	}

	.content-locacao {
		margin-top: 20px;
		padding: 8px 16px 8px 16px;
		border-radius: 6px;
		border: 1px solid $cinza02;

		.line-info-locacao {
			background-color: $mobile;
			height: 56px;
			display: flex;
			align-items: center;

			font-family: Nunito Sans;
			font-size: 12px;
			font-weight: 400;
			line-height: 12px;
			text-align: left;
			color: $cinza07;

			.icon-justificativa {
				font-size: 16px;
				color: #0380e3;
				margin: 0px 12px 0px 5px;
				cursor: pointer;

				i {
					position: relative;
					top: 2.5px;
				}
			}
		}

		.color-chuchuzinho05 {
			color: $chuchuzinho05;
		}

		.info-locacao {
			width: 100%;
		}

		.width-50 {
			width: 50% !important;
		}

		.title {
			font-family: Nunito Sans;
			font-size: 14px;
			font-weight: 700;
			line-height: 14px;
			text-align: center;
			color: $pretoPri;
			height: 56px;
			display: flex;
			align-items: center;
		}

		.info {
		}
	}

	.content-produto-servicos {
		margin-top: 20px;
		padding: 8px 16px 8px 16px;
		border-radius: 6px;
		border: 1px solid $cinza02;

		.titulo {
			font-family: Nunito Sans;
			font-size: 16px;
			font-weight: 700;
			line-height: 16px;
			text-align: left;
			color: $pretoPri;
		}
	}
}

.actions {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 30px;

	button {
		margin-left: 15px;
		background-color: $azulim05 !important;
		color: #fff !important;
		border: 1px solid $azulim05;

		&:hover {
			background: $azulim05 !important;
			color: #fff !important;

			span {
				color: #fff !important;
			}
		}
	}

	span {
		color: #fff !important;
	}

	.btn-secundary {
		background-color: #fff !important;
		color: $azulim05 !important;
	}
}

.justify-content-space-between {
	justify-content: space-between;
}

.display-flex {
	display: flex;
}

.padding-left-20 {
	padding-left: 20px;
}

.situacao-verde {
	padding: 5px 16px 5px 16px;
	gap: 4px;
	border-radius: 50px;
	background-color: $chuchuzinho01;
	color: $chuchuzinho05;
	font-size: 12px;
}

.situacao-amarelo {
	padding: 5px 16px 5px 16px;
	gap: 4px;
	border-radius: 50px;
	background-color: $pequizao01;
	color: $pequizao05;
	font-size: 12px;
}

.situacao-vermelho {
	padding: 5px 16px 5px 16px;
	gap: 4px;
	border-radius: 50px;
	background-color: $hellboy01;
	color: $hellboy05;
	font-size: 12px;
}

.whatsapp {
	color: #0380e3;
}
