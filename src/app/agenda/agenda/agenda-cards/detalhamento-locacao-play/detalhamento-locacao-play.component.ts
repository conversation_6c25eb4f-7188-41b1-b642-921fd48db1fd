import { ChangeDetectorR<PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { TreinoApiLocacaoService } from "treino-api";
import {
	LoaderService,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { ListaAlunosDetalhamentoLocacaoPlayComponent } from "./lista-alunos-detalhamento-locacao-play/lista-alunos-detalhamento-locacao-play.component";
import { ModalCancelarAgendamentoLocacaoComponent } from "../modal-cancelar-agendamento-locacao/modal-cancelar-agendamento-locacao.component";

declare var moment;

@Component({
	selector: "pacto-detalhamento-locacao-play",
	templateUrl: "./detalhamento-locacao-play.component.html",
	styleUrls: ["./detalhamento-locacao-play.component.scss"],
})
export class DetalhamentoLocacaoPlayComponent implements OnInit {
	aula: any;
	locacaoHorario: any;
	id;
	data;
	ambiente;
	ambienteId;
	alunoFc = new FormControl();
	podeRemoverAluno = false;
	podeInserirAluno = false;
	loading = false;
	integracaoZW = false;
	novaLocacao = false;
	agendamentoLocacaoCodigo;
	podeExcluirAulaCheia = false;
	podeEditarAulaCheia = false;
	podeSubstituirProfesor = false;
	@ViewChild("mensagemNotificacao", { static: true })
	mensagemNotificacao: TraducoesXinglingComponent;
	@ViewChild("listaAlunosDetalhamentoComponent", { static: false })
	listaAlunosDetalhamentoComponent: ListaAlunosDetalhamentoLocacaoPlayComponent;
	@ViewChild("vinculoAulaTraducao", { static: true })
	vinculoAulaTraducao: TraducoesXinglingComponent;

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private rest: RestService,
		private modalService: ModalService,
		private loader: LoaderService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private agendaStateService: AgendaCardsStateService,
		private treinoApiLocacaoService: TreinoApiLocacaoService
	) {}

	ngOnInit() {
		this.route.queryParams.subscribe((queryParams) => {
			if (!!queryParams.novaLocacao) {
				this.novaLocacao = queryParams.novaLocacao;
			}
			if (!!queryParams.agendamentoLocacaoCodigo) {
				this.agendamentoLocacaoCodigo = queryParams.agendamentoLocacaoCodigo;
			}
		});
		this.init();
	}

	init() {
		this.loader.show();
		this.id = this.route.snapshot.paramMap.get("id");
		this.data = this.route.snapshot.paramMap.get("dia");
		this.ambiente = this.route.snapshot.queryParams.ambiente;
		this.ambienteId = this.route.snapshot.queryParams.ambienteId;
		if (!!this.id && !!this.data) {
			if (!!this.novaLocacao || !!this.agendamentoLocacaoCodigo) {
				this.treinoApiLocacaoService
					.configAgendamento(
						this.id,
						this.agendamentoLocacaoCodigo || 0,
						this.data
					)
					.subscribe({
						next: (dados) => this._next(dados),
						error: (error) =>
							this.snotifyService.error(error.error.meta.message),
					});
			} else {
				this.treinoApiLocacaoService
					.detalhesAgendamentoLocacao(this.id, this.data)
					.subscribe({
						next: (dados) => this._next(dados),
						error: (error) =>
							this.snotifyService.error(error.error.meta.message),
					});
			}
		}
	}

	private _next(dados) {
		this.locacaoHorario = dados;
		this.locacaoHorario.id = this.id;
		this.locacaoHorario.dia = this.data;
		this.locacaoHorario.ambiente = this.ambiente;
		this.locacaoHorario.ambienteId = this.ambienteId;
		this.podeRemoverAluno = true;
		this.podeInserirAluno = true;
		this.integracaoZW = this.sessionService.integracaoZW;
		this.podeExcluirAulaCheia = true;
		this.podeEditarAulaCheia = true;
		this.podeSubstituirProfesor = true;
		this.alunoFcChanges();
		this.loader.hide();
		this.cd.detectChanges();
	}

	public getLocacaoStatus(): string {
		return this.locacaoHorario.finalizado
			? "finalizada"
			: this.locacaoHorario.cancelado
			? "cancelada"
			: "com problemas.";
	}

	private alunoFcChanges() {
		this.alunoFc.valueChanges.subscribe((aluno) => {
			this.addAlunoHandler(aluno);
			this.alunoFc.setValue(null, { emitEvent: false });
		});
	}

	get dia() {
		return moment(this.data).toDate();
	}

	alunoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
			permiteAlunoOutraEmpresa: "FALSE",
			incluirAutorizado: "TRUE",
		};
	};

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	private addAlunoHandler(aluno) {
		const dadosInserirAluno: any = {
			responsavelLancamento: this.sessionService.codigoUsuarioZw,
			locacaoHorario: this.locacaoHorario.id,
			cliente: aluno.id,
			empresa: this.sessionService.empresaId,
			ambiente: this.locacaoHorario.ambienteId,
			dia: this.locacaoHorario.dia,
		};
		this.treinoApiLocacaoService
			.addAluno(dadosInserirAluno)
			.subscribe((result) => {
				if (result.retorno) {
					this.agendamentoLocacaoCodigo =
						result.retorno.agendamentoLocacaoCodigo;
					this.listaAlunosDetalhamentoComponent.reload();
					this.snotifyService.success(
						this.vinculoAulaTraducao.getLabel("inseridoSucesso")
					);
					this.cd.detectChanges();
				} else if (result.erro) {
					this.snotifyService.error(
						this.vinculoAulaTraducao.getLabel(result.erro)
					);
				} else {
					this.snotifyService.error(
						this.vinculoAulaTraducao.getLabel("erroInesperado")
					);
				}
			});
	}

	get urlLog() {
		return this.rest.buildFullUrl(
			`log/aula/${this.locacaoHorario.id}/${this.data}`
		);
	}

	cancelarHandler() {
		const modal = this.modalService.open(
			"Cancelar modo play",
			ModalCancelarAgendamentoLocacaoComponent
		);
		modal.result.then((result) => {
			this.treinoApiLocacaoService
				.cancelarAgendamentoLocacao(this.agendamentoLocacaoCodigo, result)
				.subscribe({
					next: () => {
						this.locacaoHorario.cancelado = true;
						this.snotifyService.success("Locação cancelada com sucesso.");
						this.router.navigate(["agenda", "painel", "cards"]);
						this.agendaStateService.forceLoad$.next(true);
					},
					error: (error) => this.snotifyService.error(error.error.meta.message),
				});
		});
	}

	finalizarHandler() {
		const dto: any = {
			responsavelLancamento: this.sessionService.codigoUsuarioZw,

			locacaoHorario: this.locacaoHorario.id,
			dia: this.locacaoHorario.dia,
			finalizada: true,
			cancelada: this.locacaoHorario.cancelado,

			codigoPessoa: this.locacaoHorario.codigoPessoaClienteZW || null,
			matricula: !!this.locacaoHorario.aluno
				? this.locacaoHorario.aluno.matriculaZW
				: null,
			ambiente: this.locacaoHorario.ambienteId,
			horarios: this.locacaoHorario.horariosAdicionados,
		};
		const day = this.locacaoHorario.dia.substring(6, 8);
		const month = this.locacaoHorario.dia.substring(4, 6);
		const year = this.locacaoHorario.dia.substring(0, 4);
		const dataFormatada = `${day}/${month}/${year}`;

		this.treinoApiLocacaoService
			.finalizarAgendamentoLocacao(
				this.agendamentoLocacaoCodigo,
				dto,
				dataFormatada
			)
			.subscribe({
				next: () => {
					this.locacaoHorario.finalizado = true;
					this.snotifyService.success(
						this.vinculoAulaTraducao.getLabel("locacaoHorarioFinalizadaId")
					);
					this.cd.detectChanges();
				},
				error: () => {
					this.snotifyService.success(
						this.mensagemNotificacao.getLabel("erroInesperado")
					);
				},
			});
	}

	horarioEncerrou(): boolean {
		const agora = new Date();
		const [horas, minutos] = this.locacaoHorario.horariosAdicionados[0].horaFim
			.split(":")
			.map(Number);
		const ano = parseInt(this.locacaoHorario.dia.slice(0, 4), 10);
		const mes = parseInt(this.locacaoHorario.dia.slice(4, 6), 10) - 1;
		const dia = parseInt(this.locacaoHorario.dia.slice(6, 8), 10);
		const dataLocacao = new Date(ano, mes, dia);
		dataLocacao.setHours(horas, minutos, 0, 0);
		return dataLocacao < agora;
	}
}
