<pacto-cat-layout-v2 *ngIf="locacaoHorario">
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'AGENDA',
			categoryLink: ['agenda', 'painel', 'cards'],
			menu: 'Detalhes da locacao'
		}"></pacto-breadcrumbs>
	<div *ngIf="locacaoHorario" class="table-wrapper pacto-shadow detalhe-aula">
		<div class="info-aula">
			<span class="nome-aula">
				{{ locacaoHorario?.nomeLocacao }} - Modo play
			</span>
			<span>
				<i class="pct pct-user"></i>
				{{ locacaoHorario?.responsavel }}
			</span>
			<span>
				<i class="pct pct-map-pin"></i>
				{{ locacaoHorario.ambiente }}
			</span>
			<span>
				<i class="pct pct-calendar"></i>
				{{ dia | date : "shortDate" }}
			</span>
			<span>
				<i class="pct pct-clock"></i>
				{{
					locacaoHorario.horariosAdicionados[0].horaInicio +
						" às " +
						locacaoHorario.horariosAdicionados[0].horaFim
				}}
			</span>
		</div>

		<div class="insert-aluno">
			<span *ngIf="locacaoHorario.bloqueado" class="msg-bloqueio">
				Você não pode adicionar um aluno nesta locação porque o ambiente já está
				sendo utilizado.
			</span>
			<span
				*ngIf="
					(locacaoHorario.finalizado ||
						horarioEncerrou() ||
						locacaoHorario.cancelado) &&
					!locacaoHorario.bloqueado
				"
				class="msg-bloqueio">
				Você não pode adicionar um aluno nesta locação porque a mesma se
				encontra {{ getLocacaoStatus() }}.
			</span>
			<pacto-cat-select-filter
				*ngIf="
					!(
						locacaoHorario.finalizado ||
						horarioEncerrou() ||
						locacaoHorario.cancelado ||
						locacaoHorario.bloqueado
					)
				"
				[control]="alunoFc"
				[endpointUrl]="_rest.buildFullUrl('alunos/select')"
				[id]="'aluno-select'"
				[labelKey]="'nome'"
				[paramBuilder]="alunoSelectBuilder"
				[resposeParser]="responseParser"
				i18n-placeholder="@@agenda-detalhes-aula:holderInserir"
				placeholder="Inserir um aluno no horário"></pacto-cat-select-filter>
		</div>

		<pacto-lista-alunos-detalhamento-locacao-play
			#listaAlunosDetalhamentoComponent
			[aulaCheia]="true"
			[customEmpty]="custom"
			[dia]="data"
			[id]="id"
			[locacaoHorario]="
				locacaoHorario
			"></pacto-lista-alunos-detalhamento-locacao-play>

		<ng-template #custom>
			<div class="empty-turma">
				<img src="pacto-ui/images/empty-state-turma.svg" />
				<span class="titulo">{{ "locação" }} vazia</span>
				<span>
					Ainda não possuímos alunos nesta
					{{ "locação" }}, você pode inseri-los manualmente pelo campo de
					inserir na
					{{ "locação" }}
				</span>
			</div>
		</ng-template>

		<div class="inner-div right">
			<pacto-log [url]="urlLog"></pacto-log>
			<ng-container *ngIf="!!agendamentoLocacaoCodigo">
				<pacto-cat-button
					*ngIf="
						!locacaoHorario.finalizado &&
						!horarioEncerrou() &&
						!locacaoHorario.cancelado
					"
					(click)="cancelarHandler()"
					i18n-label="@@label-caixa-aberto-btn"
					label="Cancelar modo play"
					size="LARGE"
					type="OUTLINE"></pacto-cat-button>

				<pacto-cat-button
					*ngIf="
						!locacaoHorario.finalizado &&
						!horarioEncerrou() &&
						!locacaoHorario.cancelado
					"
					(click)="finalizarHandler()"
					i18n-label="@@label-caixa-aberto-btn"
					label="Finalizar modo play"
					size="LARGE"
					type="PRIMARY"></pacto-cat-button>
			</ng-container>
		</div>
	</div>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #vinculoAulaTraducao>
	<span
		i18n="@@agenda-detalhes-aula:inseridoSucesso"
		xingling="inseridoSucesso">
		Aluno inserido com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:errorValidacaoPlano"
		xingling="errorValidacaoPlano">
		O aluno não possui o plano necessário para ingressar na aula.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:errorValidacaoProduto"
		xingling="errorValidacaoProduto">
		O aluno não possui o produto necessario para ingressar na aula.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:locacaoHorarioCanceladoId"
		xingling="locacaoHorarioCanceladoId">
		Locação cancelada com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:locacaoHorarioNaoCancelado"
		xingling="locacaoHorarioNaoCancelado">
		Está locação já está cancelada.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:locacaoHorarioFinalizadaId"
		xingling="locacaoHorarioFinalizadaId">
		Locação finalizada com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:erroInesperado" xingling="erroInesperado">
		Um erro inesperado ocorreu, contate o administrador.
	</span>
</pacto-traducoes-xingling>
