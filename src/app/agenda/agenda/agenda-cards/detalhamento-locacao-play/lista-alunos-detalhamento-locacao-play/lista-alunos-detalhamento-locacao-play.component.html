<div class="lista-alunos">
	<span class="titulo">Participantes do modo play</span>

	<div class="table-wrapper">
		<pacto-relatorio
			#tableData
			[alternatingColors]="'first'"
			[customEmptyContent]="customEmpty"
			[dataFetchLoading]="true"
			[enableZebraStyle]="true"
			[showShare]="false"
			[table]="tableAlunos"
			actionTitulo="Ações"
			i18n-actionTitulo="@@acoes:table-column-acoes"></pacto-relatorio>
	</div>
</div>

<ng-template #nomeColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:cliente:title">Cliente</span>
</ng-template>

<ng-template #matriculaColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:matricula:title">
		Matrícula
	</span>
</ng-template>
<ng-template #nomeColumnContent let-item="item">
	<span class="capitalize">{{ item.nome.toLowerCase() }}</span>
</ng-template>
<ng-template #matriculaColumnContent let-item="item">
	<span>{{ item.matriculaZW }}</span>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:situacao:title">Situação</span>
</ng-template>
<ng-template #situacaoColumnContent let-item="item">
	<span class="situacao">
		<pacto-cat-situacao-aluno
			[situacaoAluno]="item.situacaoAluno"
			class="item"></pacto-cat-situacao-aluno>
		<pacto-cat-situacao-contrato
			[situacaoContrato]="item.situacaoContrato"
			class="item"></pacto-cat-situacao-contrato>
	</span>
</ng-template>
<ng-template #fotoColumnContent let-item="item">
	<span class="foto-aluno">
		<pacto-cat-person-avatar
			[diameter]="40"
			[uri]="item.imageUri"
			class="center-img"></pacto-cat-person-avatar>
	</span>
</ng-template>

<ng-template #acoesColumnName>
	<span i18n="@@lista-alunos-detalhamento:table:acoes:title">Ações</span>
</ng-template>

<ng-template #acoesColumnContent let-index="index" let-item="item">
	<div
		class="acoes"
		*ngIf="
			!(
				locacaoHorario.finalizado ||
				horarioEncerrou() ||
				locacaoHorario.cancelado
			)
		">
		<i
			(click)="checkinCheckoutHandler(item)"
			class="pct pct-check-circle"
			[ngClass]="{ confirmar: !!item.checkin, desconfirmar: !item.checkin }"
			i18n-title="@@agenda-detalhes-locacao:checkinAluno"
			id="acao-confirmar-presenca-{{ index }}"
			title="{{
				item.checkin === null || item.checkin === undefined
					? 'Checkin'
					: !item.checkin
					? 'Checkin'
					: 'Checkout'
			}}"></i>
		<i
			(click)="removerHandler(item)"
			*ngIf="!item.checkin"
			class="pct remover pct-trash-2"
			i18n-title="@@agenda-detalhes-locacao:desmarcAluno"
			id="acao-desmarcar-aluno-{{ index }}"
			title="Desmarcar aluno"></i>
	</div>
</ng-template>

<pacto-traducoes-xingling #vinculoAulaTraducao>
	<span i18n="@@agenda-detalhes-aula:checkinId" xingling="checkinId">
		Checkin realizado com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:checkoutId" xingling="checkoutId">
		Checkout realizado com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:removidoId" xingling="removidoId">
		Aluno removido com sucesso.
	</span>
</pacto-traducoes-xingling>
