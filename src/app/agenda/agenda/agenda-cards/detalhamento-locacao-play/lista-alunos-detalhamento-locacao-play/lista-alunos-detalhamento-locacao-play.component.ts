import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import {
	PerfilAcessoFuncionalidadeNome,
	TreinoApiLocacaoService,
} from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { TurmaDesmarcarModalComponent } from "../../../agenda-turma/turma-desmarcar-modal/turma-desmarcar-modal.component";
import moment from "moment";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-lista-alunos-detalhamento-locacao-play",
	templateUrl: "./lista-alunos-detalhamento-locacao-play.component.html",
	styleUrls: ["./lista-alunos-detalhamento-locacao-play.component.scss"],
})
export class ListaAlunosDetalhamentoLocacaoPlayComponent implements OnInit {
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("matriculaColumnName", { static: true }) matriculaColumnName;
	@ViewChild("nomeColumnContent", { static: true }) nomeColumnContent;
	@ViewChild("matriculaColumnContent", { static: true }) matriculaColumnContent;
	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("situacaoColumnContent", { static: true }) situacaoColumnContent;
	@ViewChild("fotoColumnContent", { static: true }) fotoColumnContent;
	@ViewChild("acoesColumnName", { static: true }) acoesColumnName;
	@ViewChild("acoesColumnContent", { static: true }) acoesColumnContent;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("vinculoAulaTraducao", { static: true })
	vinculoAulaTraducao: TraducoesXinglingComponent;

	@Input() id: number;
	@Input() dia: string;
	@Input() turma;
	@Input() locacaoHorario;
	@Input() customEmpty;
	@Input() aulaCheia: boolean;
	@Output() acao: EventEmitter<any> = new EventEmitter();
	tableAlunos: PactoDataGridConfig;
	integracaoZW = false;
	podeRemoverAluno = false;
	podeInserirAluno = false;
	podeExcluirAulaCheia = false;
	Loading: boolean = false;

	constructor(
		private cd: ChangeDetectorRef,
		private modalService: ModalService,
		private rest: RestService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private treinoApiLocacaoService: TreinoApiLocacaoService
	) {}

	get liberado() {
		return this.turma && this.turma.bloqueado === false;
	}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.podeRemoverAluno = this.permitirRemoverAluno();
		this.podeInserirAluno = true;
		this.podeExcluirAulaCheia = this.permitirExcluirAulaCheia();
		this.initTable();
	}

	reload() {
		if (this.tableData) {
			this.tableData.reloadData();
		}
		this.cd.detectChanges();
	}

	private permitirRemoverAluno() {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_ALUNO
		);
	}

	private permitirExcluirAulaCheia(): boolean {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_AULAS_DIA
		);
	}

	private initTable() {
		this.tableAlunos = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				`agenda-cards/locacao-play-detalhada/${this.locacaoHorario.id}/${this.locacaoHorario.ambienteId}/${this.dia}`
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 3,
			showFilters: false,
			columns: [
				{
					nome: "nome",
					titulo: "",
					visible: true,
					celula: this.fotoColumnContent,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					visible: true,
					celula: this.nomeColumnContent,
					ordenavel: true,
				},
				{
					nome: "matriculaZW",
					titulo: this.matriculaColumnName,
					visible: true,
					celula: this.matriculaColumnContent,
					ordenavel: true,
				},
				{
					nome: "situacao",
					titulo: this.situacaoColumnName,
					visible: true,
					celula: this.situacaoColumnContent,
					ordenavel: true,
				},
				{
					nome: "acoes",
					titulo: this.acoesColumnName,
					celula: this.acoesColumnContent,
					visible: true,
					ordenavel: true,
				},
			],
		});
	}

	checkinCheckoutHandler(aluno: any) {
		const dto: any = {
			checkin: !aluno.checkin,
			alunoLocacaoHorarioPlay: aluno.id,
			dataHora: moment(new Date()).format("YYYY/MM/DD HH:mm:ss"),
		};

		this.treinoApiLocacaoService
			.addAlunoHorarioPlayCheckinCheckout(dto)
			.subscribe((response) => {
				if (!!response.retorno) {
					this.snotifyService.success(
						this.vinculoAulaTraducao.getLabel(response.retorno)
					);
					this.reload();
				}
			});
	}

	removerHandler(aluno: any) {
		const titleModal = "Remover Aluno";
		const modal = this.modalService.open(
			titleModal,
			TurmaDesmarcarModalComponent
		);
		modal.componentInstance.aluno = aluno;
		modal.componentInstance.aulaCheia = true;
		modal.result.then(() => {
			this.treinoApiLocacaoService
				.deletarAlunoLocacaoHorarioPlay(aluno.id)
				.subscribe((response) => {
					if (response.content) {
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel(response.content)
						);
						this.reload();
					} else {
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("erroInterno")
						);
					}
				});
		});
	}

	horarioFuturo(): boolean {
		const hoje = new Date();
		const ano = parseInt(this.locacaoHorario.dia.slice(0, 4), 10);
		const mes = parseInt(this.locacaoHorario.dia.slice(4, 6), 10) - 1;
		const dia = parseInt(this.locacaoHorario.dia.slice(6, 8), 10);
		const data = new Date(ano, mes, dia);
		hoje.setHours(0, 0, 0, 0);
		data.setHours(0, 0, 0, 0);
		return data > hoje;
	}

	horarioEncerrou(): boolean {
		const agora = new Date();
		const [horas, minutos] = this.locacaoHorario.horariosAdicionados[0].horaFim
			.split(":")
			.map(Number);
		const ano = parseInt(this.locacaoHorario.dia.slice(0, 4), 10);
		const mes = parseInt(this.locacaoHorario.dia.slice(4, 6), 10) - 1;
		const dia = parseInt(this.locacaoHorario.dia.slice(6, 8), 10);
		const dataLocacao = new Date(ano, mes, dia);
		dataLocacao.setHours(horas, minutos, 0, 0);
		return dataLocacao < agora;
	}
}
