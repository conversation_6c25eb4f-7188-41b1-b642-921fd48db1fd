@import "src/assets/scss/pacto/plataforma-import.scss";

.detalhe-aula {
	padding-bottom: 32px;

	.info-aula {
		border-bottom: 1px solid #c9cbcf;
		padding: 32px 16px;
		display: flex;
		text-transform: capitalize;
		color: #494b50;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 125%;

		span {
			font-size: 13px;
			flex-grow: 1;

			i {
				margin-right: 8px;
			}
		}

		.nome-aula {
			font-size: 15px;
			color: #51555a;
			font-weight: 900;
			line-height: 125%;
			letter-spacing: 0.25px;
		}
	}

	.insert-aluno {
		margin-top: 16px;
		padding: 16px;
	}

	.msg-bloqueio {
		margin-left: 10px;
		font-size: 14px;
		color: $cinzaClaro05;
		font-weight: 900;
	}

	.vagas-disponiveis {
		margin: 16px 16px 8px;
		text-align: right;

		span {
			display: inline-flex;
			color: #90949a;
			font-size: 14px;
			line-height: 20px;
			padding: 8px 16px;
			background-color: #f3f3f4;
			border-radius: 18px;

			&.livre {
				color: #037d03;
				background-color: #b4fdb4;
			}
		}
	}

	.inner-div {
		margin-right: 16px;
		flex: 1;
		align-items: center;

		pacto-cat-button {
			margin-left: 10px;
		}

		::ng-deep pacto-log {
			.btn {
				background: #ffffff;
				border: 1px solid #1998fc;
				color: #1998fc;
				line-height: 39px;
				padding: 0 12px;
			}
		}
	}

	.left {
		text-align: left;
	}

	.right {
		text-align: right;
	}
}

.empty-turma {
	display: grid;
	place-items: center;
	gap: 16px;
	margin: 32px;
	text-align: center;
	font-size: 16px;
	font-weight: 400;
	line-height: 18px;

	span {
		width: 450px;
	}

	.titulo {
		font-size: 16px;
		font-weight: 600;
		line-height: 18px;
		letter-spacing: 0.25px;
	}
}
