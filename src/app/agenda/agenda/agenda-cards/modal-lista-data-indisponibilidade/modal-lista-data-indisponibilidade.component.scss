@import "~src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

.ds3-illustration {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 15px;
	img {
		margin: 24px 0 16px;
	}
	h2 {
		margin-bottom: 16px;
		@extend .pct-title4;
		color: $typeDefaultTitle;
	}
	p {
		@extend .pct-body1;
		color: $typeDefaultText;
	}
	span {
		font-size: 14px;
		font-weight: bold;
	}
}

.msg {
	padding: 16px, 16px, 16px, 10px;
	border-radius: 15px;
	margin-left: 16px;
	padding-left: 16px;
	padding-right: 16px;
	padding-top: 16px;
	padding-bottom: 10px;
	margin-right: 16px;
	background-color: #fee6e6;
	font-size: 14px;
	margin-bottom: 16px;
	display: flex;
	align-items: center;
	p {
		color: #e10505;
	}
}
