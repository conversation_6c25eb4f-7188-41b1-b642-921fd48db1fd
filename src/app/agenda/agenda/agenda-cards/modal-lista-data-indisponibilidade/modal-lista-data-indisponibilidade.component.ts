import { Component, Input, OnInit } from "@angular/core";
import { DetalhesLocacao } from "treino-api";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-lista-data-indisponibilidade",
	templateUrl: "./modal-lista-data-indisponibilidade.component.html",
	styleUrls: ["./modal-lista-data-indisponibilidade.component.scss"],
})
export class ModalListaDataIndisponibilidadeComponent implements OnInit {
	@Input() listaDatas: string;
	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}
	dismiss() {
		this.openModal.dismiss(false);
	}
}
