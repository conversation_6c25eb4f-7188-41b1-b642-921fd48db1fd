<pacto-cat-layout-v2 *ngIf="aula">
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'AGENDA',
			categoryLink: ['agenda', 'painel', 'cards'],
			menu: 'Detalhes da ' + (aula?.aulaCheia ? 'aula' : 'turma')
		}"></pacto-breadcrumbs>
	<div *ngIf="aula" class="table-wrapper pacto-shadow detalhe-aula">
		<div class="info-aula">
			<span class="nome-aula">{{ aula.nome.toLowerCase() }}</span>
			<span>
				<i class="pct pct-user"></i>
				{{
					aula?.professorSubstituto
						? aula?.professorSubstituto?.nome.toLowerCase()
						: aula?.professor?.nome.toLowerCase()
				}}
			</span>
			<span>
				<i class="pct pct-map-pin"></i>
				{{ aula.ambiente?.nome.toLowerCase() }}
			</span>
			<span>
				<i class="pct pct-tag"></i>
				{{ aula.modalidade?.nome.toLowerCase() }}
			</span>
			<span>
				<i class="pct pct-bar-chart"></i>
				{{ ocupacao }}
			</span>
			<span>
				<i class="pct pct-calendar"></i>
				{{ dia | date : "shortDate" }}
			</span>
			<span>
				<i class="pct pct-clock"></i>
				{{ periodo }}
			</span>
		</div>

		<div class="insert-aluno">
			<span *ngIf="!liberado" class="msg-bloqueio">
				Você não pode adicionar um aluno nesta aula porque o ambiente onde ela
				se realizaria já se encontra ocupado.
			</span>
			<pacto-cat-select-filter
				*ngIf="podeInserirAluno && liberado"
				[control]="alunoFc"
				[endpointUrl]="_rest.buildFullUrl('alunos/select')"
				[id]="'aluno-select'"
				[labelKey]="'nome'"
				[paramBuilder]="alunoSelectBuilder"
				[resposeParser]="responseParser"
				i18n-placeholder="@@agenda-detalhes-aula:holderInserir"
				placeholder="Inserir um aluno na aula"></pacto-cat-select-filter>
		</div>

		<div class="vagas-disponiveis">
			<span [ngClass]="{ livre: vagas > 0 }">
				{{ vagas }} {{ vagas > 1 ? "vagas disponíveis" : "vaga disponível" }}
			</span>
		</div>

		<pacto-lista-alunos-detalhamento
			#listaAlunosDetalhamentoComponent
			(acao)="atualizar($event)"
			[aulaCheia]="aula.aulaCheia"
			[customEmpty]="custom"
			[dia]="data"
			[id]="id"
			[turma]="aula"></pacto-lista-alunos-detalhamento>

		<ng-template #custom>
			<div class="empty-turma">
				<img src="pacto-ui/images/empty-state-turma.svg" />
				<span class="titulo">
					{{ aula?.aulaCheia ? "Aula" : "Turma" }} vazia
				</span>
				<span>
					Ainda não possuímos alunos nesta
					{{ aula?.aulaCheia ? "aula" : "turma" }}, você pode inseri-los
					manualmente pelo campo de inserir na
					{{ aula?.aulaCheia ? "aula" : "turma" }}
				</span>
			</div>
		</ng-template>

		<div *ngIf="aula?.aulaCheia" class="inner-div right">
			<pacto-log [url]="urlLog"></pacto-log>

			<pacto-cat-button
				(click)="removeHandler()"
				*ngIf="podeExcluirAulaCheia && aula && aula.aulaCheia"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-cancelar-aula"
				label="Cancelar aula"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<!--			<pacto-cat-button *ngIf="podeEditarAulaCheia && aula && aula.aulaCheia"-->
			<pacto-cat-button
				(click)="editAulaCheia()"
				*ngIf="aula && aula.aulaCheia && podeEditarAula"
				i18n-label="@@label-editar-aula-btn"
				id="btn-editar-aula"
				label="Editar aula"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="substituirProfessor()"
				*ngIf="podeSubstituirProfesor"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-substituir"
				label="Substituir professor"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="redirectAula()"
				*ngIf="aula && aula.aulaCheia && isPermiteCadastrarAulas"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-redirect-aula"
				label="Configurar Aula"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>
		</div>
	</div>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #vinculoAulaTraducao>
	<span i18n="@@agenda-detalhes-aula:turmaCheia" xingling="turmaCheia">
		A turma está cheia.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemcadastroZW"
		xingling="naoTemcadastroZW">
		O aluno não tem cadastro no ZW
	</span>
	<span
		i18n="@@agenda-detalhes-aula:editarReposicaoAula"
		xingling="editarReposicaoAula">
		Editar reposição de aula
	</span>
	<span i18n="@@agenda-detalhes-aula:marcacaoAula" xingling="marcacaoAula">
		Marcação de aula
	</span>
	<span
		i18n="@@agenda-detalhes-aula:editarExperimental"
		xingling="editarExperimental">
		Editar aula experimental
	</span>
	<span
		i18n="@@agenda-detalhes-aula:inseridoSucesso"
		xingling="inseridoSucesso">
		Aluno inserido com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:MATRICULADO" xingling="MATRICULADO">
		Matriculado
	</span>
	<span i18n="@@agenda-detalhes-aula:REPOSICAO" xingling="REPOSICAO">
		Reposição
	</span>
	<span i18n="@@agenda-detalhes-aula:REPOSICAO" xingling="INTEGRACAO">
		Integração
	</span>
	<span i18n="@@agenda-detalhes-aula:DEPENDENTE" xingling="DEPENDENTE">
		Dependente
	</span>
	<span i18n="@@agenda-detalhes-aula:DESMARCADO" xingling="DESMARCADO">
		Desmarcado
	</span>
	<span
		i18n="@@agenda-detalhes-aula:AULA_EXPERIMENTAL"
		xingling="AULA_EXPERIMENTAL">
		Aula experimental
	</span>
	<span i18n="@@agenda-detalhes-aula:DIARIA" xingling="DIARIA">Diaria</span>
	<span i18n="@@agenda-detalhes-aula:ESPERA" xingling="ESPERA">
		Fila de espera
	</span>
	<span i18n="@@agenda-detalhes-aula:DESAFIO" xingling="DESAFIO">Desafio</span>
	<span i18n="@@agenda-detalhes-aula:DIARIA_GYMPASS" xingling="DIARIA_GYMPASS">
		Diaria gympass
	</span>
	<span
		i18n="@@agenda-detalhes-aula:DIARIA_TOTALPASS"
		xingling="DIARIA_TOTALPASS">
		Diaria totalpass
	</span>
	<span i18n="@@agenda-detalhes-aula:MARCACAO" xingling="MARCACAO">
		Marcação
	</span>
	<span i18n="@@agenda-detalhes-aula:VISITANTE" xingling="VISITANTE">
		Visitante
	</span>
	<span i18n="@@agenda-detalhes-aula:VISITANTE" xingling="CONTRATO_FUTURO">
		Aluno com contrato futuro e não atende demais condições para ser inserido na
		aula ou turma
	</span>
</pacto-traducoes-xingling>
<pacto-traducoes-xingling #notificacoes>
	<span i18n="@@agenda-detalhes-aula:confirmSuccess" xingling="confirmSuccess">
		Confirmado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:confirmSuccess"
		xingling="confirmedSuccess">
		Alunos confirmados com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:desconfirmSuccess"
		xingling="desconfirmSuccess">
		Desconfirmado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:desmarcadoSuccess"
		xingling="desmarcadoSuccess">
		Desmarcado com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:validCreditos" xingling="validCreditos">
		O número de aulas não pode exceder a quantidade de créditos.
	</span>
	<span i18n="@@agenda-detalhes-aula:validPermissao" xingling="validPermissao">
		Ops! Essa turma não permite aula experimental ou diária.
	</span>
	<span i18n="@@agenda-detalhes-aula:soPodeMarcar" xingling="soPodeMarcar">
		Você só pode marcar
	</span>
	<span i18n="@@agenda-detalhes-aula:aposInicioAula" xingling="aposInicioAula">
		após o início da aula.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeMarcarAte"
		xingling="soPodeMarcarAte">
		Você só pode marcar até
	</span>
	<span
		i18n="@@agenda-detalhes-aula:antesInicioAula"
		xingling="antesInicioAula">
		antes do início da aula.
	</span>
	<span i18n="@@agenda-detalhes-aula:aulaEstaCheia" xingling="aulaEstaCheia">
		A aula já está cheia!
	</span>
	<span
		i18n="@@agenda-detalhes-aula:semPermissaoDiariaFreePass"
		xingling="semPermissaoDiariaFreePass">
		Você precisa ter as permissões: (2.51 - Lançar Free Pass), (4.01 - Diária),
		para realizar esta operação.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:jaMatriculouNaAula"
		xingling="jaMatriculouNaAula">
		Você já se matriculou nessa aula!
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemModalidadeExperimental"
		xingling="naoTemModalidadeExperimental">
		O aluno não tem essa modalidade, deseja usar uma de suas
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemModalidadeExperimental2"
		xingling="naoTemModalidadeExperimental2">
		aulas experimentais?
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeDesmarcarAulaAte"
		xingling="soPodeDesmarcarAulaAte">
		Você só pode desmarcar a aula com até
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeDesmarcarAulaAte2"
		xingling="soPodeDesmarcarAulaAte2">
		de antecedência
	</span>
	<span
		i18n="@@agenda-detalhes-aula:foraHorarioPlano"
		xingling="foraHorarioPlano">
		A aula não está dentro do horário do seu plano, deseja usar uma de suas
	</span>
	<span
		i18n="@@agenda-detalhes-aula:foraHorarioPlano2"
		xingling="foraHorarioPlano2">
		aulas experimentais?
	</span>
	<span
		i18n="@@agenda-detalhes-aula:aulaCheiaJustificada"
		xingling="aulaCheiaJustificada">
		Falta justificada com sucesso.
	</span>
</pacto-traducoes-xingling>
<pacto-traducoes-xingling #mensagemNotificacao>
	<span
		i18n="@@agenda-detalhes-aula:aulaCheiaExcluida"
		xingling="aulaCheiaExcluida">
		Aula excluida com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:substituirProfessor"
		xingling="substituirProfessor">
		Substituir Professor
	</span>
	<span
		i18n="@@agenda-detalhes-aula:substituirProfessorSucess"
		xingling="substituirProfessorSucess">
		Professor substituido com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:existeAlunosNaAula"
		xingling="existeAlunosNaAula">
		Você não pode excluir uma aula que tenha alunos marcados.
	</span>
</pacto-traducoes-xingling>
