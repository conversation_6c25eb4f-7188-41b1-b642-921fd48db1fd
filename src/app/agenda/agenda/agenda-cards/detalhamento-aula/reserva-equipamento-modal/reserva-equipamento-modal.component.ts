import { Component, OnInit, ViewChild } from "@angular/core";
import { FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";
import { TreinoApiTurmaService } from "treino-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-reserva-equipamento-modal",
	templateUrl: "./reserva-equipamento-modal.component.html",
	styleUrls: ["./reserva-equipamento-modal.component.scss"],
})
export class ReservaEquipamentoModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	formGroup: FormGroup = new FormGroup({});
	aula: any;
	alunoId: number;
	aluno: string = null;
	equipamentoSelecionado: string = "";

	constructor(
		private activeModal: NgbActiveModal,
		private snotifyService: SnotifyService,
		private turmaService: TreinoApiTurmaService
	) {}

	ngOnInit() {}

	concluir() {
		this.turmaService
			.incluirHorarioEquipamentoAluno(
				this.aula.horarioTurmaId,
				this.alunoId,
				this.equipamentoSelecionado,
				this.aula.dia
			)
			.subscribe((response) => {
				this.snotifyService.success(
					this.traducao.getLabel("salva-com-sucesso")
				);
				this.activeModal.close(response);
			});
	}

	handleSelectedMaps(selectedMaps) {
		const listaPosicoes = selectedMaps
			.map((item) => item.posicaoMapa)
			.filter(Boolean)
			.join(";");
		if (listaPosicoes) {
			this.equipamentoSelecionado = listaPosicoes;
		}
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close(this.aula.equipamentosOcupados);
		}
	}
}
