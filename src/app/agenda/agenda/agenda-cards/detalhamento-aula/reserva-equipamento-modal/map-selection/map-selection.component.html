<div class="map-selection-container">
	<div *ngIf="apresentacao" class="label-map">
		<span>Equipamentos Reservados</span>
	</div>
	<div class="title-map">
		<span>{{ title }}</span>
	</div>
	<div class="row" *ngFor="let row of maps; let rowIndex = index">
		<button
			title="{{ obterTitleEquipamento(mapFull[rowIndex][mapIndex].nome) }}"
			class="map"
			*ngFor="let map of row; let mapIndex = index"
			[ngClass]="
				map.value === 1
					? 'selected'
					: map.value === 3
					? 'selected editando-posicao-antiga'
					: map.value === 4
					? 'selected editando-posicao-nova'
					: map.value === 0
					? 'selected disabled'
					: 'selected ocupado'
			"
			(click)="toggleMap(rowIndex, mapIndex)"
			[disabled]="apresentacao || map.value === 2"
			[ngbTooltip]="getTooltip(map)"
			tabindex="-1">
			<div>
				<div
					*ngIf="map.aparelhoId !== null && map.aparelhoId > 0"
					class="icone-aparelho">
					<i class="{{ map.iconeAparelho }}"></i>
				</div>
				<div class="sigla-posicao">
					<div>
						<span>
							{{ obterPosicaoEquipamento(mapFull[rowIndex][mapIndex].nome) }}
						</span>
					</div>
					<div>
						<span *ngIf="map.siglaAparelho !== ''" style="font-weight: 400">
							{{ map.siglaAparelho }}
						</span>
					</div>
				</div>
			</div>
		</button>
	</div>
</div>
