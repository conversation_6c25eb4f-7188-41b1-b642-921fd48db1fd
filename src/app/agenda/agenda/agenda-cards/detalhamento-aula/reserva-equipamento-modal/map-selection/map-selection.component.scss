@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "dist/ui-kit/assets/import.scss";

.map-selection-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
	border: 1px solid hsla(225, 5%, 85%, 1);
	border-radius: 15px;
	margin: 20px 0;

	.label-map {
		width: 100%;
		margin-bottom: 10px;
		text-align: left;
		color: #43474b;
		font-size: 14px;
		font-style: normal;
		font-weight: 600;
	}

	.title-map {
		position: relative;
		width: 100%;
		margin-bottom: 20px;

		&:after {
			height: 1px;
			background-color: hsla(225, 5%, 85%, 1);
			width: 100%;
			top: 50%;
			position: absolute;
			content: "";
		}

		span {
			background-color: $supportGray01;
			color: $actionDefaultDisabled02;
			font-size: 12px;
			line-height: 16px;
			display: block;
			width: 180px;
			border-radius: 13px;
			padding: 4px 0;
			text-align: center;
			margin: auto;
			position: relative;
			z-index: 1;
		}
	}

	.row {
		display: flex;
		margin-bottom: 15px;
	}

	.map {
		width: 30px;
		height: 30px;
		margin: 3px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		border: 1px solid $supportGray05;
		border-radius: 50%;
		width: 60px;
		height: 60px;
		color: #007bff;
		background-color: #fff;
		font-size: 18px;
		transition: background-color 0.2s;
		margin: 5px 4px;
	}

	.map.selected {
		background-color: white;
		border: 1px solid #007bff;
		color: #007bff;
		font-size: 12px;
		font-family: $fontPoppins !important;
		font-weight: 600 !important;
	}

	.map.ocupado {
		background-color: $hellboy05 !important;
		border-color: $hellboy05 !important;
		color: white;
		font-size: 12px;
		font-family: $fontPoppins !important;
		font-weight: 600 !important;
	}

	.map.selected.editando-posicao-antiga {
		background-color: $cinzaPri !important;
		border-color: $cinzaPri !important;
		color: white;
	}

	.map.selected.editando-posicao-nova {
		background-color: $actionDefaultAble04 !important;
		border-color: $actionDefaultAble04 !important;
		color: white;
	}

	.map.selected.disabled {
		display: none;
	}

	.icone-aparelho {
		font-size: 16px;
		margin-bottom: -5px;
	}

	.sigla-posicao {
	}
}
