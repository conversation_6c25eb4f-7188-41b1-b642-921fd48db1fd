import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { MapaEquipamentoAparelho, ModelMap } from "treino-api";
import { TreinoConfigCacheService } from "../../../../../../base/configuracoes/configuration.service";

@Component({
	selector: "map-selection",
	templateUrl: "./map-selection.component.html",
	styleUrls: ["./map-selection.component.scss"],
})
export class MapSelectionComponent implements OnInit {
	@Input() totalRows: number = 8;
	@Input() mapsPerRow: number = 20;
	@Input() apresentacao: boolean = false;
	@Input() limiteEquipamentos: number = 1;
	@Input() listEquipamentosSelecionados: string = "";
	@Input() listEquipamentosOcupados: string = "";
	@Input() listaMapaEquipamentoAparelho: Array<MapaEquipamentoAparelho> = [];
	@Input() title: string = "Selecionar";
	@Input() aluno: string = null;
	@Output() selectedMapsChange = new EventEmitter<any>();

	maps: ModelMap[][] = [];
	mapFull: Array<any> = this.gerarEstrutura();
	// configuração de utilizar número sequencial como identificador do aparelho
	utilizarNumeracaoSequencial: boolean = false;
	listaMapaAuxiliar: Array<any> = [];

	gerarEstrutura() {
		const alphabetData: Array<Array<{ id: string; nome: string }>> = [];
		const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		for (const letter of alphabet) {
			const group: Array<{ id: string; nome: string }> = [];
			for (let i = 1; i <= this.mapsPerRow; i++) {
				const entry = { id: `${letter}-${i}`, nome: `${letter}-${i}` };
				group.push(entry);
			}
			alphabetData.push(group);
		}
		return alphabetData;
	}

	constructor(
		private snotifyService: SnotifyService,
		private treinoConfigService: TreinoConfigCacheService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initializeMaps(this.aluno);
		this.initializeConfigs();
		this.initializeMapAux();
	}

	initializeMapAux() {
		const mapaListaSelecionados = this.listEquipamentosSelecionados.split(";");
		for (const item of mapaListaSelecionados) {
			this.listaMapaAuxiliar.push(item.split("&")[0]);
		}
	}

	initializeConfigs() {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesAula &&
			this.treinoConfigService.configuracoesAula
				.utilizar_numeracao_sequencial_identificador_equipamento
		) {
			this.utilizarNumeracaoSequencial = true;
		} else {
			this.utilizarNumeracaoSequencial = false;
		}
		this.cd.detectChanges();
	}

	initializeMaps(aluno: string) {
		this.maps = [];
		if (!this.listEquipamentosSelecionados) {
			this.listEquipamentosSelecionados = "";
		}
		const mapaListaSelecionados = this.listEquipamentosSelecionados.split(";");
		const mapaListaOcupados = this.listEquipamentosOcupados.split(";");
		for (let i = 0; i < this.totalRows; i++) {
			const row = [];
			for (let j = 0; j < this.mapsPerRow; j++) {
				const nomePosicao = this.obterNomeEquipamento(this.mapFull[i][j].nome);
				if (mapaListaSelecionados.includes(nomePosicao)) {
					const obj = this.getEquipamentoAparelhoByPos(nomePosicao);
					if (
						mapaListaOcupados.some((a) => {
							if (a.split("&")[0] === nomePosicao) {
								this.mapFull[i][j].nome = a;
								return true;
							}
							return false;
						})
					) {
						if (
							aluno &&
							mapaListaOcupados.some((a) => {
								if (
									a.split("&")[0] === nomePosicao &&
									a.split("&")[1] === aluno
								) {
									this.mapFull[i][j].nome = a;
									return true;
								}
								return false;
							})
						) {
							row.push(this.setItemMapa(3, obj));
						} else {
							row.push(this.setItemMapa(2, obj));
						}
					} else {
						row.push(this.setItemMapa(1, obj));
					}
				} else {
					row.push(this.setItemMapa(0, null));
				}
			}
			this.maps.push(row);
		}
	}

	setItemMapa(value: number, obj: MapaEquipamentoAparelho): ModelMap {
		const itemMapa: ModelMap = {
			value: value,
			aparelhoId: obj && obj.codigoAparelho ? obj.codigoAparelho : null,
			siglaAparelho: obj && obj.siglaAparelho ? obj.siglaAparelho : "",
			iconeAparelho: obj && obj.iconeAparelho ? obj.iconeAparelho : "",
			nomeAparelho: obj && obj.nomeAparelho ? obj.nomeAparelho : "",
			posicaoMapa: obj && obj.posicaoMapa ? obj.posicaoMapa : "",
		};
		return itemMapa;
	}

	getEquipamentoAparelhoByPos(value): MapaEquipamentoAparelho {
		const obj: MapaEquipamentoAparelho = this.listaMapaEquipamentoAparelho.find(
			(item) => item.posicaoMapa === value
		);
		return obj;
	}

	toggleMap(row: number, map: number) {
		this.maps[row][map].value = this.maps[row][map].value === 1 ? 4 : 1;

		let qtSelecionados = 0;
		this.maps.forEach((item, rowIndex) => {
			item.forEach((pos, mapIndex) => {
				if (pos.value === 3 || pos.value === 4) {
					qtSelecionados++;
				}
			});
		});
		if (qtSelecionados > this.limiteEquipamentos) {
			this.snotifyService.error(
				"A quantidade de equipamentos selecionados não pode exceder a capacidade de " +
					this.limiteEquipamentos
			);
			this.maps[row][map].value = 1;
		} else {
			this.emitSelectedMaps();
		}
	}

	obterNomeEquipamento(label: string): string {
		return label.split("&")[0];
	}

	obterPosicaoEquipamento(label: string): string {
		const posicao = label.split("&")[0];
		if (!this.utilizarNumeracaoSequencial) {
			return posicao;
		} else {
			const index = this.listaMapaAuxiliar.indexOf(posicao);
			return (index + 1).toString();
		}
	}

	obterTitleEquipamento(label: string): string {
		return label.split("&")[1];
	}

	emitSelectedMaps() {
		const listSelecteds: Array<ModelMap> = [];
		this.maps.forEach((row, rowIndex) => {
			row.forEach((map, mapIndex) => {
				if (map.value === 4) {
					listSelecteds.push(map);
				}
			});
		});
		this.selectedMapsChange.emit(listSelecteds);
	}

	getTooltip(item: ModelMap): string {
		if (this.stringIsNotEmpty(item.nomeAparelho)) {
			return item.nomeAparelho;
		}
		return "";
	}

	valueIsNotNull(value): boolean {
		return value && value !== null && value !== undefined;
	}

	stringIsNotEmpty(value): boolean {
		if (!this.valueIsNotNull(value)) {
			return false;
		}
		return value.trim() !== "";
	}

	numberIsNotEmpty(value): boolean {
		if (!this.valueIsNotNull(value)) {
			return false;
		}
		return value > 0;
	}
}
