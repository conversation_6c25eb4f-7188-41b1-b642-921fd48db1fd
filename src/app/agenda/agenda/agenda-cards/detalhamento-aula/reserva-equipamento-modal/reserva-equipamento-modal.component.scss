@import "projects/ui/assets/import";
@import "projects/ui/assets/ds3/colors.var.scss";

@media (min-width: 1050px) {
	::ng-deep .modal-integracao .modal-lg {
		max-width: 1050px;
	}
}

pacto-cat-form-input,
pacto-cat-form-input-number {
	margin: 0px;
}

.main-content {
	padding: 27px 48px 27px 48px;
}

.btns-ativar-inativar {
	margin-top: 38px;
}

::ng-deep.modal-time-line {
	.modal-dialog {
		max-width: 78%;
	}
}

.margin-left-13 {
	margin-left: 13px;
}

.margin-left-3 {
	margin-left: 3px;
}

.todosDiasRight {
	flex: 1 0 16.66666667%;
	max-width: 23%;
	text-align: right;
}

.top10-border-bottom {
	margin-top: 10px;
	border-bottom: 1px solid #dcdddf;
}

.right5 {
	margin-right: 5px;
}

.top20 {
	margin-top: 20px;
}

.margin-top-40 {
	margin-top: 40px;
}

.cadastrar-ambiente {
	color: #0a64ff;
	font-size: 14px;
	font-weight: bold;
}

.line-danger-text-adjust {
	margin-left: 10px;
	margin-right: 5px;
}

.line-prof-inativo {
	background-color: #fdfdb4;
	height: 40px;
	line-height: 43px;
	margin-bottom: 15px;
}

.btn-primary {
	height: 40px;
	color: $branco;
	font-weight: bold;
	background-color: $azul !important;
	border: 1px solid $azul !important;
	width: 100px;
	font-size: 14px;
}

.box-info {
	display: flex;
	i {
		margin-right: 10px;
		display: flex;
		align-items: center;
	}
	* {
		color: $actionDefaultDisabled02;
	}
	p {
		font-size: 14px;
		margin-bottom: 0;
	}
}
