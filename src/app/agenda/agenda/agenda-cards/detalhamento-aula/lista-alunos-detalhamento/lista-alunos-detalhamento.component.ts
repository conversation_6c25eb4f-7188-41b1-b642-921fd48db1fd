import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import {
	PerfilAcessoFuncionalidadeNome,
	TreinoApiTurmaService,
	TreinoApiAulaService,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { TurmaJustificativaFaltaAulaCheiaModalComponent } from "../../../agenda-turma/turma-justificativa-falta-aula-cheia-modal/turma-justificativa-falta-aula-cheia-modal.component";
import {
	ModalService,
	PactoModalRef,
	PactoModalSize,
} from "@base-core/modal/modal.service";
import { TurmaDesmarcarModalComponent } from "../../../agenda-turma/turma-desmarcar-modal/turma-desmarcar-modal.component";
import { FixarAlunoComponent } from "./fixar-aluno/fixar-aluno.component";
import { HttpClient } from "@angular/common/http";
import { FormControl } from "@angular/forms";
import { EsperaDesmarcarModalComponent } from "../../../agenda-turma/filadeespera-desmarcar-modal/espera-desmarcar-modal-component";
import { AddFilaEsperaTurmaModalComponent } from "../../modal-add-fila-espera-turma/modal-add-fila-espera-turma.component";
import { PermissaoService } from "pacto-layout";
import { MapSelectionComponent } from "../reserva-equipamento-modal/map-selection/map-selection.component";
import { ReservaEquipamentoModalComponent } from "../reserva-equipamento-modal/reserva-equipamento-modal.component";
import { PerfilRecursoPermissoTipo } from "sdk";
import {
	AdmCoreApiClienteService,
	AdmCoreApiObservacaoService,
} from "adm-core-api";
import { map, tap } from "rxjs/operators";
import { DatePipe } from "@angular/common";
import { Observable, of } from "rxjs";
import { ObservacoesComponent } from "src/app/pessoas/perfil-cliente/perfil-cliente-header-v2/observacoes/observacoes.component";
import { TreinoConfigCacheService } from "../../../../../base/configuracoes/configuration.service";

declare var moment;

@Component({
	selector: "pacto-lista-alunos-detalhamento",
	templateUrl: "./lista-alunos-detalhamento.component.html",
	styleUrls: ["./lista-alunos-detalhamento.component.scss"],
})
export class ListaAlunosDetalhamentoComponent implements OnInit {
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("averageColumnName", { static: true }) averageColumnName;
	@ViewChild("caloriesColumnName", { static: true }) caloriesColumnName;
	@ViewChild("nomeColumnContent", { static: true }) nomeColumnContent;
	@ViewChild("averageColumnContent", { static: true }) averageColumnContent;
	@ViewChild("caloriesColumnContent", { static: true }) caloriesColumnContent;
	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("situacaoColumnContent", { static: true }) situacaoColumnContent;
	@ViewChild("posicaoRankingColumnContent", { static: true })
	posicaoRankingColumnContent;
	@ViewChild("fotoColumnContent", { static: true }) fotoColumnContent;
	@ViewChild("statusColumnName", { static: true }) statusColumnName;
	@ViewChild("statusColumnContent", { static: true }) statusColumnContent;
	@ViewChild("horarioColumnName", { static: true }) horarioColumnName;
	@ViewChild("horarioColumnContent", { static: true }) horarioColumnContent;
	@ViewChild("creditoColumnName", { static: true }) creditoColumnName;
	@ViewChild("creditoColumnContent", { static: true }) creditoColumnContent;
	@ViewChild("equipamentoReservadoColumnName", { static: true })
	equipamentoReservadoColumnName;
	@ViewChild("equipamentoReservadoColumnContent", { static: true })
	equipamentoReservadoColumnContent;
	@ViewChild("observacoesColumnName", { static: true }) observacoesColumnName;
	@ViewChild("observacoesColumnContent", { static: true })
	observacoesColumnContent;
	@ViewChild("acoesColumnName", { static: true }) acoesColumnName;
	@ViewChild("acoesColumnContent", { static: true }) acoesColumnContent;
	@ViewChild("presencaColumnName", { static: true }) presencaColumnName;
	@ViewChild("nomeColumnNameEspera", { static: true }) nomeColumnNameEspera;
	@ViewChild("nomeColumnContentEspera", { static: true })
	nomeColumnContentEspera;
	@ViewChild("situacaoColumnNameEspera", { static: true })
	situacaoColumnNameEspera;
	@ViewChild("situacaoColumnContentEspera", { static: true })
	situacaoColumnContentEspera;
	@ViewChild("fotoColumnContentEspera", { static: true })
	fotoColumnContentEspera;
	@ViewChild("statusColumnNameEspera", { static: true }) statusColumnNameEspera;
	@ViewChild("statusColumnContentEspera", { static: true })
	statusColumnContentEspera;
	@ViewChild("horarioColumnNameEspera", { static: true })
	horarioColumnNameEspera;
	@ViewChild("horarioColumnContentEspera", { static: true })
	horarioColumnContentEspera;
	@ViewChild("acoesColumnNameEspera", { static: true }) acoesColumnNameEspera;
	@ViewChild("acoesColumnContentEspera", { static: true })
	acoesColumnContentEspera;
	@ViewChild("presencaColumnNameEspera", { static: true })
	presencaColumnNameEspera;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("vinculoAulaTraducao", { static: true })
	vinculoAulaTraducao: TraducoesXinglingComponent;
	@ViewChild("notificacoes", { static: true })
	notificacoes: TraducoesXinglingComponent;
	@ViewChild("modalDesmarcarAluno", { static: true })
	modalDesmarcarAluno: TraducoesXinglingComponent;
	@ViewChild("modalDesmarcarAlunoEspera", { static: true })
	modalDesmarcarAlunoEspera: TraducoesXinglingComponent;
	@ViewChild("tableDataEspera", { static: false })
	tableDataEspera: RelatorioComponent;
	@ViewChild("ordemColumnContentEspera", { static: true })
	ordemColumnContentEspera;
	@ViewChild("ordemColumnNameEspera", { static: true }) ordemColumnNameEspera;
	@ViewChild("mapSelection", { static: false })
	mapSelectionComponent: MapSelectionComponent;
	@ViewChild("tableDataRankingSelfloops", { static: false })
	@ViewChild("parqPositivoColumnContent", { static: true })
	parqPositivoColumnContent;

	tableDataRankingSelfloops: RelatorioComponent;

	alunoASerRemovido: string;
	@Input() espera: boolean = false;
	@Input() id: number;
	@Input() dia: string;
	@Input() turma;
	@Input() customEmpty;
	@Input() customEmptyEspera;
	@Input() equipamentosOcupados = "";
	@Input() aulaCheia: boolean;
	@Output() acao: EventEmitter<any> = new EventEmitter();
	tableAlunos: PactoDataGridConfig;
	tableRankingSelfloops: PactoDataGridConfig;
	tableAlunosEspera: PactoDataGridConfig;
	integracaoZW = false;
	podeRemoverAluno = false;
	podeInserirAluno = false;
	podeFixar = false;
	podeExcluirAulaCheia = false;
	alunos: any[] = [];
	Loading: boolean = false;
	listaChamada: boolean = true;
	rankingSelfloops: boolean = false;
	integracaoSelfloopsAtivada: boolean = false;
	//Observações
	permissaoCliente2_36: any;
	canObservation: boolean = false;
	codigoPessoaSelecionada: number;
	matriculaSelecionada: number;
	observacoesCache = new Map<string, any[]>();
	observacoesV1: any[] = [];
	temAniversariante: boolean = true;

	utilizarNumeracaoSequencial: boolean = false;

	constructor(
		private cd: ChangeDetectorRef,
		private modalService: ModalService,
		private rest: RestService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private turmaService: TreinoApiTurmaService,
		private aulaService: TreinoApiAulaService,
		private http: HttpClient,
		private ngbModal: NgbModal,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private permissaoService: PermissaoService,
		private pactoModal: ModalService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private admCoreObsService: AdmCoreApiObservacaoService,
		private datePipe: DatePipe,
		private treinoConfigService: TreinoConfigCacheService
	) {}

	get liberado() {
		return this.turma && this.turma.bloqueado === false;
	}

	confirmarTodosHandler() {
		this.turmaService
			.confirmarTodasPresencas(this.turma.horarioTurmaId, this.turma.dia)
			.subscribe((response) => {
				this.reload();
				this.snotifyService.success(
					this.notificacoes.getLabel("confirmedSuccess")
				);
			});
	}

	visualizarRankingSelfloops() {
		this.listaChamada = false;
		this.rankingSelfloops = true;
	}

	visualizarListaChamada() {
		this.listaChamada = true;
		this.rankingSelfloops = false;
	}

	atualizarRankingSelfloops() {
		this.turmaService
			.atualizarRankingSelfloops(this.turma.horarioTurmaId, this.turma.dia)
			.subscribe((response) => {
				this.reloadRankingSelfloops();
				this.snotifyService.success(
					this.notificacoes.getLabel("atualizadoSuccess")
				);
			});
	}

	confirmarTodosHandlerEspera() {
		this.snotifyService.success(
			"Todos os alunos na fila de espera foram confirmados com sucesso."
		);
	}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.podeFixar = this.turma.permiteFixar;
		this.podeRemoverAluno = this.permitirRemoverAluno();
		this.podeInserirAluno = this.permitirInserirAluno();
		this.podeExcluirAulaCheia = this.permitirExcluirAulaCheia();
		this.espera = false;
		this.initTable();
		this.initTableEspera();
		this.initTableRankingSelfloops();
		this.validarIntegracaoSelfloops();

		this.initObservacoes();
		this.temAniversariante = false;
		this.initConfig();
	}

	initObservacoes() {
		this.permissaoCliente2_36 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.36"
			);
	}

	initConfig() {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesAula &&
			this.treinoConfigService.configuracoesAula
				.utilizar_numeracao_sequencial_identificador_equipamento
		) {
			this.utilizarNumeracaoSequencial = true;
		} else {
			this.utilizarNumeracaoSequencial = false;
		}
		this.cd.detectChanges();
	}

	validarIntegracaoSelfloops() {
		this.configTreinoService
			.getConfiguracoesIntegracoesListaSelfloops()
			.subscribe((result) => {
				if (result && result.length > 0) {
					result.forEach((config) => {
						if (this.sessionService.empresaId === config.empresa.toString()) {
							this.integracaoSelfloopsAtivada =
								config.code && config.empresaSelfloops ? true : false;
							this.cd.detectChanges();
						}
					});
				}
			});
	}

	reload() {
		if (this.tableData) {
			this.tableData.reloadData();
		}
		if (this.tableDataEspera) {
			this.tableDataEspera.reloadData();
		}
		this.cd.detectChanges();
	}

	reloadRankingSelfloops() {
		if (this.tableDataRankingSelfloops) {
			this.tableDataRankingSelfloops.reloadData();
		}
		this.cd.detectChanges();
	}

	acaoHandler(turma) {
		this.reload();
		this.acao.emit(turma);
	}

	private initTableEspera() {
		this.configTreinoService
			.getConfiguracoesAplicativo()
			.subscribe((config) => {
				if (config && config.habilitar_fila_espera) {
					this.espera = true;
					const codigoHorarioTurma = this.turma.horarioTurmaId;
					const dia = this.turma.dia;

					this.tableAlunosEspera = new PactoDataGridConfig({
						endpointUrl: this.rest.buildFullUrl(
							this.aulaCheia
								? `aulas/consultar-fila-espera/${codigoHorarioTurma}/${dia}`
								: `aulas/consultar-fila-espera-turma-crm/${codigoHorarioTurma}`
						),
						quickSearch: false,
						ghostLoad: true,
						ghostAmount: 3,
						showFilters: false,
						pagination: false,
						columns: [
							{
								nome: "ordem",
								titulo: "Posição na fila",
								celula: this.ordemColumnContentEspera,
								visible: true,
								ordenavel: false,
								campo: "ordem",
							},
							{
								nome: "nomeAluno",
								titulo: "",
								visible: true,
								celula: this.fotoColumnContentEspera,
								ordenavel: false,
							},
							{
								nome: "nomeAluno",
								titulo: this.nomeColumnNameEspera,
								visible: true,
								celula: this.nomeColumnContentEspera,
								ordenavel: true,
							},
							{
								nome: "situacao",
								titulo: this.situacaoColumnNameEspera,
								visible: true,
								celula: this.situacaoColumnContentEspera,
								ordenavel: false,
								campo: "situacaoAluno",
							},
							{
								nome: "dia",
								titulo: this.horarioColumnNameEspera,
								celula: this.horarioColumnContentEspera,
								visible: true,
								ordenavel: false,
							},
							{
								nome: "acoes",
								titulo: this.acoesColumnNameEspera,
								celula: this.acoesColumnContentEspera,
								visible: true,
								ordenavel: false,
							},
						],
					});
				} else {
					this.espera = false;
				}

				this.cd.detectChanges();
			});
	}

	getEquipamentosOcupados() {
		return this.turma.equipamentosOcupados;
	}

	private permitirRemoverAluno() {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_ALUNO
		);
	}

	private permitirExcluirAulaCheia(): boolean {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_AULAS_DIA
		);
	}

	private permitirInserirAluno() {
		const horarioInicio = this.turma.horarioInicio.split(":");
		const dataTurma = moment(this.turma.dia)
			.add(horarioInicio[0], "hours")
			.add(horarioInicio[1], "minutes")
			.toDate();
		const dataHoje = new Date();
		return (
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.INSERIR_ALUNO
			) &&
			(this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.INSERIR_ALUNO_AULA_INICIADA
			)
				? true
				: moment(dataHoje).isBefore(dataTurma))
		);
	}

	private initTable() {
		this.tableAlunos = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				`agenda-cards/aula-detalhada/${this.id}/${this.dia}`
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 3,
			showFilters: false,
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					visible: true,
					celula: this.fotoColumnContent,
					ordenavel: true,
				},
				{
					nome: "situacao",
					titulo: this.situacaoColumnName,
					visible: true,
					celula: this.situacaoColumnContent,
					ordenavel: true,
				},
				{
					nome: "vinculoComAula",
					titulo: this.statusColumnName,
					celula: this.statusColumnContent,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "horarioMarcacao",
					titulo: this.horarioColumnName,
					celula: this.horarioColumnContent,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "credito",
					titulo: this.creditoColumnName,
					celula: this.creditoColumnContent,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "equipamentoReservado",
					titulo: this.equipamentoReservadoColumnName,
					celula: this.equipamentoReservadoColumnContent,
					visible: this.turma.mapaEquipamentos,
					ordenavel: true,
				},
				{
					nome: "observacoes",
					titulo: this.observacoesColumnName,
					celula: this.observacoesColumnContent,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "acoes",
					titulo: this.acoesColumnName,
					celula: this.acoesColumnContent,
					visible: true,
					ordenavel: true,
				},
			],
		});

		this.turmaService.aulaDetalhada(this.id, this.dia).subscribe((result) => {
			this.alunos = result.alunos;
			console.log("resultado alunos");
			console.log(result.alunos);
		});
	}

	private initTableRankingSelfloops() {
		this.tableRankingSelfloops = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				`agenda-cards/aula-detalhada/${this.id}/${this.dia}`
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 3,
			showFilters: false,
			columns: [
				{
					nome: "nome",
					titulo: "",
					visible: true,
					celula: this.fotoColumnContent,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					visible: true,
					celula: this.posicaoRankingColumnContent,
					ordenavel: true,
				},
				{
					nome: "averagePower",
					titulo: this.averageColumnName,
					visible: true,
					celula: this.averageColumnContent,
					ordenavel: true,
				},
				{
					nome: "calories",
					titulo: this.caloriesColumnName,
					visible: true,
					celula: this.caloriesColumnContent,
					ordenavel: true,
				},
			],
		});
		this.tableRankingSelfloops.dataAdapterFn = (data) => {
			data.content.sort((a, b) => b.averagePower - a.averagePower);
			return data;
		};
	}

	confirmarHandler(aluno: any) {
		this.turmaService
			.confirmarPresencaTurma(
				this.turma.horarioTurmaId,
				this.turma.dia,
				aluno.matriculaZW,
				aluno.codigoPassivo,
				aluno.codigoIndicado,
				aluno.vinculoComAula,
				aluno.autorizadoGestaoRede,
				aluno.codAcessoAutorizado,
				aluno.matriculaAutorizado
			)
			.subscribe((response) => {
				this.acaoHandler(response);
				this.snotifyService.success(
					this.notificacoes.getLabel("confirmSuccess")
				);
			});
	}

	desconfirmarHandler(aluno: any) {
		this.turmaService
			.desconfirmarPresencaTurma(
				this.turma.horarioTurmaId,
				this.turma.dia,
				aluno.matriculaZW,
				aluno.vinculoComAula,
				aluno.autorizadoGestaoRede,
				aluno.codAcessoAutorizado,
				aluno.matriculaAutorizado
			)
			.subscribe((response) => {
				this.acaoHandler(response);
				this.snotifyService.success(
					this.notificacoes.getLabel("desconfirmSuccess")
				);
			});
	}

	justificarHandler(aluno: any) {
		if (this.integracaoZW && !this.turma.aulaCheia) {
			if (aluno.justificativa != null) {
				const ref = this.modalService.open(
					"Justificar falta",
					TurmaJustificativaFaltaAulaCheiaModalComponent
				);
				ref.componentInstance.texto = aluno.justificativa;
			}
		}
	}

	fixarAluno(aluno: any) {
		const ref = this.modalService.open(
			"Fixar aluno",
			FixarAlunoComponent,
			PactoModalSize.MEDIUM,
			"modal-fixar"
		);
		ref.componentInstance.aula = this.turma;
		ref.componentInstance.aluno = aluno;
		ref.componentInstance.dia = this.dia;
		ref.result.then((escolha) => {
			if (escolha) {
				this.turmaService
					.fixarAlunoAula(
						this.turma.horarioTurmaId,
						aluno.matriculaZW,
						escolha.tipo,
						this.dia,
						escolha.ate,
						false
					)
					.subscribe((response) => {
						if (!response || !response.erro) {
							this.snotifyService.success("Aluno fixado com sucesso!");
						}
						this.acaoHandler(this.turma);
						this.cd.detectChanges();
					});
			}
		});
	}

	desafixarAluno(aluno: any) {
		const ref = this.modalService.open("Desafixar aluno", FixarAlunoComponent);
		ref.componentInstance.aula = this.turma;
		ref.componentInstance.aluno = aluno;
		ref.componentInstance.desafixar = true;
		ref.result.then((escolha) => {
			if (escolha) {
				this.turmaService
					.desafixarAlunoAula(
						this.turma.horarioTurmaId,
						aluno.matriculaZW,
						this.dia
					)
					.subscribe((response) => {
						if (!response || !response.erro) {
							this.snotifyService.success("Aluno desafixado com sucesso!");
						}
						this.acaoHandler(this.turma);
						this.cd.detectChanges();
					});
			}
		});
	}

	editarEquipamento(aluno: any) {
		this.openModalReservaEquipamento(aluno);
	}

	private openModalReservaEquipamento(aluno: any) {
		const ref = this.modalService.open(
			"Mapa dos equipamentos",
			ReservaEquipamentoModalComponent,
			PactoModalSize.LARGE,
			"modal-reserva-equip"
		);
		ref.componentInstance.alunoId = aluno.id;
		ref.componentInstance.aula = this.turma;
		ref.componentInstance.aluno = aluno.nome;
		ref.result.then(
			(response) => {
				this.turma.equipamentosOcupados = response;
				this.mapSelectionComponent.listEquipamentosOcupados = response;
				this.mapSelectionComponent.initializeMaps(null);
				this.acaoHandler(this.turma);
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	removerHandler(aluno: any, isEspera: boolean) {
		if (isEspera) {
			this.removerHandlerEspera(aluno);
			return;
		}
		const titleModal = this.turma.aulaCheia
			? this.modalDesmarcarAluno.getLabel("title")
			: this.modalDesmarcarAluno.getLabel("title2");
		const modal = this.modalService.open(
			titleModal,
			TurmaDesmarcarModalComponent
		);
		modal.componentInstance.aluno = aluno;
		modal.componentInstance.aulaCheia = this.turma.aulaCheia;
		modal.result.then(() => {
			if (this.integracaoZW && !this.turma.aulaCheia) {
				const ref = this.modalService.open(
					"Justificar falta",
					TurmaJustificativaFaltaAulaCheiaModalComponent
				);
				ref.componentInstance.texto = aluno.justificativa;
				ref.result.then((result) => {
					this.turmaService
						.desmarcarAlunoTurma(
							this.turma.horarioTurmaId,
							this.turma.dia,
							aluno.vinculoComAula,
							aluno.matriculaZW,
							aluno.codigoPassivo,
							aluno.codigoIndicado,
							result
						)
						.subscribe((response) => {
							if (response.toString().includes("soPodeDesmarcarAulaAte")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("soPodeDesmarcarAulaAte") +
										response.toString().split(";")[1] +
										this.notificacoes.getLabel("soPodeDesmarcarAulaAte2")
								);
								return;
							} else if (typeof response === "string") {
								// ignore
							} else {
								this.acaoHandler(response);
								this.snotifyService.success(
									this.notificacoes.getLabel("desmarcadoSuccess")
								);
							}
						});
				});
			} else {
				this.turmaService
					.desmarcarAlunoTurma(
						this.turma.horarioTurmaId,
						this.turma.dia,
						aluno.vinculoComAula,
						aluno.matriculaZW,
						aluno.codigoPassivo,
						aluno.codigoIndicado,
						aluno.justificativa,
						aluno.autorizadoGestaoRede,
						aluno.codAcessoAutorizado,
						aluno.matriculaAutorizado
					)
					.subscribe((response) => {
						if (response.toString().includes("soPodeDesmarcarAulaAte")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("soPodeDesmarcarAulaAte") +
									response.toString().split(";")[1] +
									this.notificacoes.getLabel("soPodeDesmarcarAulaAte2")
							);
							return;
						} else if (typeof response === "string") {
							// ignore
						} else {
							if (response) {
								this.turma.equipamentosOcupados = response.equipamentosOcupados;

								if (response.equipamentosOcupados != undefined) {
									this.mapSelectionComponent.listEquipamentosOcupados =
										response.equipamentosOcupados;
								}
								if (this.mapSelectionComponent != undefined) {
									this.mapSelectionComponent.initializeMaps(null);
								}
							}
							this.acaoHandler(response);
							this.snotifyService.success(
								this.notificacoes.getLabel("desmarcadoSuccess")
							);
						}
					});
			}
		});
	}

	removerHandlerEspera(aluno: any) {
		const titleModal = this.modalDesmarcarAlunoEspera.getLabel("title");
		const modal = this.modalService.open(
			titleModal,
			EsperaDesmarcarModalComponent
		);
		modal.componentInstance.aluno = aluno;

		modal.result.then(() => {
			const codigoHorarioTurma = this.turma.horarioTurmaId;
			const dia = this.turma.dia;
			const codigoAluno = aluno.codigoAluno;
			const codigoUsuario = this.sessionService.codigoUsuarioZw;
			const passivo = aluno.passivo;
			this.aulaService
				.removerDaFilaEsperaV2(
					this.aulaCheia,
					codigoHorarioTurma,
					dia,
					codigoAluno,
					codigoUsuario,
					passivo
				)
				.subscribe(
					(response) => {
						this.snotifyService.success(
							"Aluno removido da fila de espera com sucesso."
						);
						this.reloadTableDataEspera();
					},
					(error) => {
						this.snotifyService.error(
							"Erro ao remover aluno da fila de espera."
						);
					}
				);
		});
	}

	aumentarOrdem(aluno: any): void {
		if (!this.temPermissaoOrdenarFila) {
			this.snotifyService.error(
				"Você não possui permissão 10.05 - Ordenar fila de espera turma."
			);
		} else {
			this.Loading = true;
			this.aulaService
				.ordenarFilaEspera(
					this.aulaCheia,
					this.turma.horarioTurmaId,
					this.turma.dia,
					aluno.matricula,
					aluno.passivo,
					"incremento"
				)
				.subscribe(
					() => {
						this.tableDataEspera.reloadData();
					},
					(error) => {
						console.error("Erro ao aumentar ordem:", error);
						this.Loading = false;
					}
				);
		}
	}

	diminuirOrdem(aluno: any): void {
		if (!this.temPermissaoOrdenarFila) {
			this.snotifyService.error(
				"Você não possui permissão 10.05 - Ordenar fila de espera turma."
			);
		} else {
			this.Loading = true;
			this.aulaService
				.ordenarFilaEspera(
					this.aulaCheia,
					this.turma.horarioTurmaId,
					this.turma.dia,
					aluno.matricula,
					aluno.passivo,
					"decremento"
				)
				.subscribe(
					() => {
						this.tableDataEspera.reloadData();
					},
					(error) => {
						console.error("Erro ao diminuir ordem:", error);
						this.Loading = false;
					}
				);
		}
	}

	alterarOrdemManual(aluno: any, novaOrdem: number): void {
		if (!this.temPermissaoOrdenarFila) {
			this.snotifyService.error(
				"Você não possui permissão 10.05 - Ordenar fila de espera turma."
			);
		} else if (event && event.type === "change") {
			const target = event.target as HTMLInputElement;
			const novaOrdem = Number(target.value);
			this.Loading = true;
			this.aulaService
				.ordenarFilaEspera(
					this.aulaCheia,
					this.turma.horarioTurmaId,
					this.turma.dia,
					aluno.matricula,
					aluno.passivo,
					"input",
					novaOrdem
				)
				.subscribe(
					() => {
						this.tableDataEspera.reloadData();
					},
					(error) => {
						console.error("Erro ao alterar ordem manualmente:", error);
						this.Loading = false;
					}
				);
		}
	}

	tableEsperaDataLoaded() {
		if (this.tableDataEspera.data && this.tableDataEspera.data.content) {
			this.tableDataEspera.data.content.forEach((item, index) => {
				item.index = index + 1;
				item.formControl = new FormControl(item.index);
			});
		}
	}

	private reloadTableDataEspera() {
		if (this.tableDataEspera) {
			this.tableDataEspera.reloadData();
			this.cd.detectChanges();
			this.tableEsperaDataLoaded();
		}
	}

	get isTurmaSomenteLimite() {
		return (
			this.turma &&
			!this.turma.aulaCheia &&
			this.turma.numeroAlunos >= this.turma.capacidade &&
			this.turma.bloquearMatriculasAcimaLimite
		);
	}

	openModalAddFilaTurma() {
		const ref = this.ngbModal.open(AddFilaEsperaTurmaModalComponent, {
			centered: true,
			size: null,
			windowClass: "modal-action-nfse-component",
		});
		ref.componentInstance.codigoHorarioTurma = this.turma.horarioTurmaId;
		ref.result.then(
			(dto) => {
				this.reloadTableDataEspera();
			},
			() => {}
		);
	}

	get temPermissaoFila() {
		return this.permissaoService.temPermissaoAdm("10.11");
	}

	get temPermissaoOrdenarFila() {
		return this.permissaoService.temPermissaoAdm("10.05");
	}

	isAniversario(dataNascimento: string): boolean {
		if (!dataNascimento) return false;

		const hojeStr = String(this.dia);
		const hoje = new Date(
			Number(hojeStr.substring(0, 4)),
			Number(hojeStr.substring(4, 6)) - 1,
			Number(hojeStr.substring(6, 8))
		);

		const partes = dataNascimento.split("/");
		if (partes.length !== 3) return false;

		const dataNasc = new Date(
			Number(partes[2]),
			Number(partes[1]) - 1,
			Number(partes[0])
		);
		const anivesariante =
			dataNasc.getDate() === hoje.getDate() &&
			dataNasc.getMonth() === hoje.getMonth();
		if (anivesariante) {
			this.temAniversariante = anivesariante;
		}
		return anivesariante;
	}

	hasAniversariante(anivesariante): boolean {
		return this.temAniversariante;
	}

	permiteVisualizarObservacao(): any {
		const permition = this.permissaoCliente2_36;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		this.canObservation = retor ? true : false;
		return retor;
	}
	openObservations(item: any) {
		if (!this.permiteVisualizarObservacao()) {
			this.snotifyService.error(
				'Você não possui a permissão "2.36 - Lançar observação para o cliente"',
				{
					timeout: undefined,
					bodyMaxLength: 300,
				}
			);
			return;
		}

		const dialogRef = this.pactoModal.open(
			"Observações",
			ObservacoesComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.cliente = String(item.codigoCliente);
		dialogRef.componentInstance.pessoa = String(item.codigoPessoa);
		dialogRef.componentInstance.matricula = String(item.matriculaZW);
		dialogRef.componentInstance.usuario = String(
			this.sessionService.codigoUsuarioZw
		);
		dialogRef.componentInstance.observacoesV1 = this.observacoesV1;

		dialogRef.componentInstance.update.subscribe(() => {
			const codigoPessoa = dialogRef.componentInstance.pessoa;
			const matriculaZW = dialogRef.componentInstance.matricula;

			if (codigoPessoa && matriculaZW) {
				const cacheKey = `${codigoPessoa}-${matriculaZW}`;

				if (this.observacoesCache.has(cacheKey)) {
					this.observacoesCache.delete(cacheKey);
				}

				this.tableAlunos = null;
				this.cd.detectChanges();

				setTimeout(() => {
					this.initTable();
					this.cd.detectChanges();
				});
			}
		});
	}
	// Observação
	getObservacoes(item: any): Observable<any[]> {
		const cacheKey = `${item.codigoPessoa}-${item.matriculaZW}`;

		if (this.observacoesCache.has(cacheKey)) {
			return of(this.observacoesCache.get(cacheKey)!);
		}

		return this.admCoreObsService
			.findObservacaoTelaCliente1(item.codigoPessoa, item.matriculaZW, {
				page: 0,
				size: 2,
			})
			.pipe(
				map((res: any) =>
					res.content.map((data) => ({
						...data,
						_dataCadastro: this.datePipe.transform(
							new Date(data.dataCadastro),
							"dd/MM/yyyy"
						),
					}))
				),
				tap((observacoes) => this.observacoesCache.set(cacheKey, observacoes))
			);
	}
	getEquipamentoAluno(item: any) {
		if (item.equipamentoReservado === "") {
			return "";
		}
		const mapaEquipamentos = this.turma.mapaEquipamentos.split(";");
		if (!this.utilizarNumeracaoSequencial) {
			return item.equipamentoReservado;
		} else {
			const index = mapaEquipamentos.indexOf(
				item.equipamentoReservado.split(" - ")[0]
			);
			const sigla = item.equipamentoReservado.split(" - ")[1];
			const equipamentoAluno =
				index + 1 + (sigla === "" || sigla === undefined ? "" : " - " + sigla);
			return equipamentoAluno;
		}
	}
}
