import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";
import { BUTTON_TYPE } from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TreinoApiTurmaService } from "treino-api";

@Component({
	selector: "pacto-fixar-aluno",
	templateUrl: "./fixar-aluno.component.html",
	styleUrls: ["./fixar-aluno.component.scss"],
})
export class FixarAlunoComponent implements OnInit {
	form: FormControl = new FormControl(null);
	formDataAte: FormControl = new FormControl(null);
	confirm = false;
	validado = false;
	aulasCheias;
	desafixar = false;
	aula;
	aluno;
	dia;

	constructor(
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private turmaService: TreinoApiTurmaService
	) {}

	ngOnInit() {}

	voltar() {
		this.modal.close();
	}

	confirmar() {
		if (this.desafixar || this.validado) {
			this.modal.close({
				tipo: this.form.value,
				ate: this.formDataAte.value,
			});
			return;
		}
		this.turmaService
			.fixarAlunoAula(
				this.aula.horarioTurmaId,
				this.aluno.matriculaZW,
				this.form.value,
				this.dia,
				this.formDataAte.value,
				true
			)
			.subscribe((response) => {
				if (response && response.aulasCheias) {
					this.aulasCheias = response.aulasCheias;
					this.validado = true;
					this.cd.detectChanges();
					return;
				}
				this.modal.close({
					tipo: this.form.value,
					ate: this.formDataAte.value,
				});
			});
	}

	fixarAluno() {
		this.confirm = true;
	}

	get notSelected(): boolean {
		return (
			!this.form.value ||
			(this.form.value === "DATA_DETERMINADA" && !this.formDataAte.value)
		);
	}

	get dataDeterminada(): boolean {
		return this.form.value === "DATA_DETERMINADA";
	}

	get tiposFixar(): any[] {
		const tipos = [
			{ id: "FIM_CONTRATO", nome: "Até o fim do contrato" },
			{ id: "DATA_DETERMINADA", nome: "Até uma data determinada" },
		];
		return tipos;
	}

	protected readonly BUTTON_TYPE = BUTTON_TYPE;
}
