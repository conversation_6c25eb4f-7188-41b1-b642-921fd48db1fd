<div *ngIf="desafixar" class="confirmar-fixar">
	<span>
		Tem certeza que deseja desafixar o aluno {{ aluno.nome }} na aula
		{{ aula.nome }} ?
	</span>

	<div class="btns">
		<pacto-cat-button
			(click)="voltar()"
			[full]="true"
			[id]="'btn-voltar-pagina-aula'"
			[label]="'Não, voltar para página da aula'"
			[type]="'OUTLINE'"></pacto-cat-button>
		<pacto-cat-button
			(click)="confirmar()"
			[full]="true"
			[id]="'btn-fixar-confirmar'"
			[label]="'Sim, confirmar desafixação '"></pacto-cat-button>
	</div>
</div>

<div *ngIf="confirm && !desafixar" class="confirmar-fixar">
	<span *ngIf="!dataDeterminada">
		Tem certeza que deseja fixar o aluno na {{ aula.nome }} até o fim do seu
		contrato?
	</span>
	<span *ngIf="dataDeterminada">
		Tem certeza que deseja fixar o aluno na {{ aula.nome }} até
		{{ formDataAte?.value | date : "dd/MM/yyyy" }}?
	</span>

	<div
		*ngIf="validado && aulasCheias && aulasCheias.length > 1"
		class="validado">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Infelizmente, não será possível reservar uma vaga para os dias
			{{ aulasCheias.join(", ") }}
			pois todas as vagas para essas aulas já estão preenchidas.
		</span>
	</div>

	<div
		*ngIf="validado && aulasCheias && aulasCheias.length == 1"
		class="validado">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Infelizmente, não será possível reservar uma vaga para o dia
			{{ aulasCheias }}
			pois todas as vagas para essa aula já estão preenchidas.
		</span>
	</div>

	<div class="btns">
		<pacto-cat-button
			(click)="voltar()"
			[full]="true"
			[id]="'btn-voltar-pagina-aula'"
			[label]="'Não, voltar para página da aula'"
			[type]="'OUTLINE'"></pacto-cat-button>
		<pacto-cat-button
			(click)="confirmar()"
			[full]="true"
			[id]="'btn-fixar-confirmar'"
			[label]="'Sim, confirmar fixação de aluno'"></pacto-cat-button>
	</div>
</div>
<div *ngIf="!confirm && !desafixar" class="como-fixar">
	<pacto-cat-select
		[control]="form"
		[idKey]="'id'"
		[id]="'select-how-fix'"
		[items]="tiposFixar"
		[labelKey]="'nome'"
		i18n-label="@@agenda-detalhes-aula:fixar"
		label="Como deseja fixar esse aluno?"></pacto-cat-select>

	<pacto-cat-datepicker
		*ngIf="dataDeterminada"
		[formControl]="formDataAte"
		[idInput]="'input-data-fixar-ate'"
		[id]="'data-fixar-ate'"
		[label]="'Data de fim'"></pacto-cat-datepicker>

	<div class="btns">
		<pacto-cat-button
			(click)="fixarAluno()"
			[disabled]="notSelected"
			[full]="false"
			[id]="'btn-fixar-aluno'"
			[label]="'Fixar aluno'"></pacto-cat-button>
	</div>
</div>
