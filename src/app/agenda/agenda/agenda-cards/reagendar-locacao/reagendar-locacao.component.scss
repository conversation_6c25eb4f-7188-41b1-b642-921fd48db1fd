@import "src/assets/scss/pacto/plataforma-import.scss";

.agendamento-locacao {
	padding: 15px;

	input {
		width: 100%;
		border-radius: 3px;
		border: none;
		padding: 0px 30px 0px 10px;
		line-height: 40px;
		color: #92959b;
		background-color: #eff2f7;
		outline: 0px !important;
	}

	.input-nome {
		font-size: 14px;
		font-weight: 700;
		line-height: 18px;
		color: #a1a5aa;
		margin-bottom: 5px;
	}

	.row {
		margin-top: 24px;
	}

	.lista-alunos {
		&.valores {
			.total {
				font-size: 20px;
				font-weight: 600;
				line-height: 20px;
				color: #28ab45;
			}

			font-size: 14px;
			font-weight: 600;
			line-height: 14px;

			div:last-child {
				justify-content: right;
			}

			div:first-child {
				justify-content: left;
			}

			div {
				display: flex;
				align-items: center;
				justify-content: center;

				span {
					margin-right: 10px;
				}
			}

			display: grid;
			grid-template-columns: 1fr 1fr 1fr 1fr;
		}

		border-radius: 5px;
		border: 1px solid #c9cbcf;
		padding: 16px;
		margin-top: 32px;

		::ng-deep.table-content {
			padding: 0;
		}

		.acoes {
			font-size: 18px;
			cursor: pointer;

			.pct {
				padding: 0px 5px;
			}

			.confirmar {
				color: $azulimPri;
			}

			.desconfirmar {
				color: $gelo03;
			}

			.remover {
				color: $hellboyPri;
			}
		}

		.titulo {
			display: block;
			color: #43474b;
			font-size: 14px;
			font-style: normal;
			font-weight: 600;
			line-height: 125%;
			letter-spacing: 0.25px;
			margin-bottom: 32px;
		}

		.situacao {
			display: flex;

			.item {
				display: block;
				margin: 5px;
			}
		}
	}

	.capitalize {
		text-transform: capitalize;
	}

	.transform-none {
		text-transform: none;
	}

	.inner-div.right {
		padding-top: 24px;
		text-align: right;
	}

	.label-small {
		font-size: 12px;
		font-weight: 400;
		line-height: 12px;
		color: #a1a5aa;
	}
}

.actions {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;

	button {
		margin-left: 15px;
		background-color: $azulim05 !important;
		color: #fff !important;
		border: 1px solid $azulim05;

		&:hover {
			background: $azulim05 !important;
			color: #fff !important;

			span {
				color: #fff !important;
			}
		}
	}

	span {
		color: #fff !important;
	}

	.btn-secundary {
		background-color: #fff !important;
		color: $azulim05 !important;
	}
}
