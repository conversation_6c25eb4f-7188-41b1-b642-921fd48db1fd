<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'Agenda / Detalhes da locação',
			categoryLink: ['agenda', 'painel', 'cards'],
			menu: 'Reagendar locação'
		}"></pacto-breadcrumbs>

	<div *ngIf="locacao" class="table-wrapper pacto-shadow agendamento-locacao">
		<div class="row">
			<div class="col-md-6">
				<div class="input-nome">Alugar para</div>
				<input [formControl]="locacaoFg.get('aluno')" disabled type="text" />
			</div>

			<div class="col-md-6">
				<div class="input-nome">Responsável</div>
				<input
					[formControl]="locacaoFg.get('responsavel')"
					disabled
					type="text" />
			</div>
		</div>

		<div class="row">
			<div class="col-md-3">
				<div class="input-nome">Data*</div>
				<pacto-cat-datepicker
					[formControl]="locacaoFg.get('dia')"></pacto-cat-datepicker>
			</div>

			<div class="col-md-3">
				<div class="input-nome">Ambiente*</div>
				<pacto-cat-select-filter
					[control]="locacaoFg.get('ambiente')"
					[id]="'ambiente-adicionar-agendamento'"
					[labelKey]="'nome'"
					[options]="ambientes"
					[placeholder]="'Selecionar ambiente'"
					[resposeParser]="resposeParser"
					id="ambientes-adicionar-agendamento"></pacto-cat-select-filter>
			</div>

			<div class="col-md-3">
				<div class="input-nome">Produto</div>
				<input [formControl]="locacaoFg.get('produto')" disabled type="text" />
			</div>

			<div class="col-md-3">
				<div class="input-nome">Valor</div>
				<input [formControl]="locacaoFg.get('valor')" disabled type="text" />
			</div>
		</div>

		<div class="lista-alunos">
			<span class="titulo">Horários do agendamento</span>

			<pacto-cat-table-editable
				#tableHorarioComponent
				(confirm)="confirmHorario($event)"
				(delete)="removeHorario($event)"
				(isEditingOrAddingItem)="isEditingOrAdding($event)"
				*ngIf="horariosDisponiveis && horariosDisponiveis.length > 0"
				[isEditable]="true"
				[newLineTitle]="'Adicionar mais horários'"
				[showAddRow]="true"
				[isAddRowAvailable]="isAddRowAvailable()"
				[table]="tableHorario"
				idSuffix="table-horario"></pacto-cat-table-editable>
		</div>

		<div class="actions">
			<button
				(click)="confirmarReagendamento()"
				[disabled]="!reagendamentoValidado"
				class="btn btn-primary"
				i18n="@@buttons:salvar">
				Confirmar Reagendamento
			</button>
		</div>
	</div>
</pacto-cat-layout-v2>
