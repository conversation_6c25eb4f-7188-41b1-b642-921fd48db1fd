import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Ambiente, TreinoApiLocacaoService } from "treino-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";

@Component({
	selector: "pacto-reagendar-locacao",
	templateUrl: "./reagendar-locacao.component.html",
	styleUrls: ["./reagendar-locacao.component.scss"],
})
export class ReagendarLocacaoComponent implements OnInit {
	@ViewChild("tableHorarioComponent", { static: false })
	tableHorarioComponent: RelatorioComponent;

	tableHorario: PactoDataGridConfig;
	locacao: any;
	horariosDisponiveis = [];
	total = 0.0;
	totalLocacao = 0.0;
	totalAdicionais = 0.0;
	codigoAgendamentoLocacao;
	ambientes: Ambiente[];
	reagendamentoValidado = false;
	locacaoFg = new FormGroup({
		aluno: new FormControl("", [Validators.required]),
		responsavel: new FormControl(""),
		produto: new FormControl(""),
		dia: new FormControl(),
		valor: new FormControl(),
		ambiente: new FormControl("", [Validators.required]),
	});

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private treinoApiLocacaoService: TreinoApiLocacaoService,
		private snotifyService: SnotifyService,
		private agendaStateService: AgendaCardsStateService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			if (params.cod) {
				this.codigoAgendamentoLocacao = params.cod;
				this.loadDetalhesAgendamentoLocacao();
			}
		});

		this.locacaoFg.get("dia").valueChanges.subscribe((newDate) => {
			this.onDateChange(newDate);
		});
	}

	onDateChange(newDate: any) {
		if (this.tableHorarioComponent && this.tableHorarioComponent.rawData) {
			this.tableHorarioComponent.rawData = [];
		}
		this.horariosDisponiveis = this.locacao.horarios;
		this.cd.detectChanges();
	}

	loadDetalhesAgendamentoLocacao() {
		this.treinoApiLocacaoService
			.detalhesAgendamentoLocacao(this.codigoAgendamentoLocacao)
			.subscribe((response) => {
				this.locacao = response;
				this.ambientes = response.ambientes;
				this.totalLocacao = this.locacao.valorLocacao;
				this.total = this.locacao.valorTotal;
				this.locacaoFg.get("aluno").setValue(this.locacao.aluno.nome);
				this.locacaoFg.get("responsavel").setValue(this.locacao.responsavel);
				this.locacaoFg.get("produto").setValue(this.locacao.produto);
				this.locacaoFg
					.get("valor")
					.setValue(this.transformMoneyValue(this.locacao.valorLocacao));
				this.locacaoFg
					.get("dia")
					.setValue(this.converterDataParaTimestamp(this.locacao.data));
				this.locacaoFg.get("ambiente").setValue(this.locacao.ambiente);
				this.initTable();
				this.validarDataReagendamento(
					this.converterDataParaTimestamp(this.locacao.data)
				);
				this.locacaoFg.get("dia").valueChanges.subscribe((value) => {
					this.validarDataReagendamento(value);
				});
				setTimeout(() => {
					this.calcularTotal();
					this.cd.detectChanges();
				}, 1000);
			});
	}

	public converterDataParaTimestamp(data: string): number {
		// converter 'DD/MM/YYYY' para timestamp
		const partes = data.split("/");
		const dia = parseInt(partes[0], 10);
		const mes = parseInt(partes[1], 10) - 1;
		const ano = parseInt(partes[2], 10);
		const dataObjeto = new Date(ano, mes, dia);
		return dataObjeto.getTime();
	}

	public formatarData(data: string): string {
		// formatar data de dd/mm/yyyy para yyyymmdd que é o esperado pelo componente de pacto-cat-datepicker
		const partes = data.split("/");
		const ano = partes[2];
		const mes = partes[1];
		const dia = partes[0];
		return `${ano}${mes}${dia}`;
	}

	public transformMoneyValue(valor: number) {
		const valorFormatado = Number(valor).toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
		return "R$ " + valorFormatado;
	}

	resposeParser = (result) => result.content;

	public initTable() {
		this.tableHorario = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				inicioFim: new FormControl(""),
			}),
			dataAdapterFn: (serverData) => {
				return {
					content: [],
				};
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => true,
			showEdit: (row, isAdd) => true,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "inicioFim",
					titulo: "Horário",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					selectOptionChange: (option, form, row) => {
						row.codigo = option.codigo;
						row.inicioFim = option.inicioFim;
						row.duracaoDescricao = this.formatoDuracao(
							option.horaInicio,
							option.horaFim
						);
					},
					width: "300px",
					idSelectKey: "codigo",
					labelSelectKey: "inicioFim",
					objectAttrLabelName: "inicioFim",
					inputSelectData: this._filtrarHorariosDisponiveis(),
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
				{
					nome: "duracaoDescricao",
					titulo: "Duração",
					visible: true,
					ordenavel: false,
					editable: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
				},
			],
		});
		this.cd.detectChanges();
	}

	private _filtrarHorariosDisponiveis(): any[] {
		const horariosParaExcluir = this.locacao.horariosAdicionados.map(
			(it) => it.codigo
		);

		return this.horariosDisponiveis.filter(
			(it) => !horariosParaExcluir.includes(it.codigo)
		);
	}

	private formatoDuracao(startTime: string, endTime: string): string {
		const inicio = new Date(`1970-01-01T${startTime}:00Z`);
		const fim = new Date(`1970-01-01T${endTime}:00Z`);
		const duracao = (fim.getTime() - inicio.getTime()) / 60000;

		const horas = Math.floor(duracao / 60);
		const minutos = duracao % 60;

		return `${horas.toString().padStart(2, "0")}:${minutos
			.toString()
			.padStart(2, "0")}`;
	}

	public isAddRowAvailable(): boolean {
		const horariosAdicionados = this.locacao.horariosAdicionados.map(
			(it) => it.codigo
		);
		return (
			this.horariosDisponiveis.filter(
				(it) => !horariosAdicionados.includes(it.codigo)
			).length !== 0
		);
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this._reapresentarOpcoesDoSelect(data);
		return true;
	}

	showDelete(row, isAdd): boolean {
		return !row.obrigatorio;
	}

	confirmHorario(event) {
		this.calcularTotal();
	}

	calcularTotal() {
		if (this.locacao && this.horariosDisponiveis) {
			const horariosAdicionados = this.tableHorarioComponent.rawData.length;
			this.totalLocacao = this.locacao.valorLocacao * horariosAdicionados;
			this.total = Number(this.totalLocacao);
			this.locacaoFg
				.get("valor")
				.setValue(this.transformMoneyValue(this.totalLocacao));
			this.cd.detectChanges();
		}
	}

	isEditingOrAdding($event: boolean) {}

	removeHorario(event) {
		if (this.tableHorarioComponent.rawData) {
			this.tableHorarioComponent.rawData.splice(event.index, 1);
		}
		this._reapresentarOpcoesDoSelect(event.data);
		this.calcularTotal();
	}

	private _reapresentarOpcoesDoSelect(horariosAdicionados: any[]) {
		this.tableHorario.columns
			.filter((it) => it.nome === "inicioFim")
			.map((it) => {
				this.locacao.horariosAdicionados = horariosAdicionados;
				it.inputSelectData = this._filtrarHorariosDisponiveis();
			});
	}

	validarDataReagendamento(novaData) {
		this.treinoApiLocacaoService
			.validarDataReagendamento(this.codigoAgendamentoLocacao, novaData)
			.subscribe({
				error: (error) => {
					this.horariosDisponiveis = [];
					this.recarregarTabelaHorarios();
					this.reagendamentoValidado = false;
					this.snotifyService.error(error.error.meta.message);
					this.cd.detectChanges();
				},
				next: (dados) => {
					this.horariosDisponiveis = dados;
					this.recarregarTabelaHorarios();
					this.reagendamentoValidado = true;
					this.cd.detectChanges();
				},
			});
	}

	private recarregarTabelaHorarios() {
		this.initTable();
	}

	confirmarReagendamento() {
		const dto = this.getDto();
		if (
			!this.reagendamentoValidado ||
			dto.horarios === undefined ||
			dto.horarios.length < 1 ||
			!dto.horarios[0].horaInicio
		) {
			this.snotifyService.error(
				"Para prosseguir selecione uma data e horário válidos para reagendar."
			);
		} else {
			this.treinoApiLocacaoService
				.reagendarLocacao(
					this.codigoAgendamentoLocacao,
					this.locacaoFg.get("dia").value,
					dto
				)
				.subscribe({
					error: (error) => {
						this.snotifyService.error(error.error.meta.message);
					},
					next: (dados) => {
						this.snotifyService.success("Locação reagendada com sucesso.");
						this.agendaStateService.forceLoad$.next(true);
						this.router.navigate(["agenda", "painel", "cards"]);
					},
				});
		}
	}

	private getDto() {
		const dto: any = {};
		dto.matricula = this.locacaoFg.get("aluno").value.matriculaZW;
		dto.ambiente = this.locacaoFg.get("ambiente").value.id;
		dto.horarios = [];
		this.tableHorarioComponent.rawData.forEach((item) => {
			dto.horarios.push(item.inicioFim);
		});
		dto.valorTotal = this.total;
		dto.valorLocacao = this.totalLocacao;
		dto.servicos = [];
		return dto;
	}
}
