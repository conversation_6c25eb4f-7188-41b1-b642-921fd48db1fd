import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-cancelar-agendamento-locacao",
	templateUrl: "./modal-cancelar-agendamento-locacao.component.html",
	styleUrls: ["./modal-cancelar-agendamento-locacao.component.scss"],
})
export class ModalCancelarAgendamentoLocacaoComponent implements OnInit {
	@ViewChild("validCampo", { static: true })
	validCampo: TraducoesXinglingComponent;
	justificativa: FormControl = new FormControl("", [Validators.required]);

	constructor(
		private openModal: NgbActiveModal,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {}

	excluir() {
		this.justificativa.markAsTouched();
		if (this.justificativa.valid) {
			this.openModal.close(this.justificativa.value);
		} else {
			this.snotifyService.error(this.validCampo.getLabel("campoObrigatorio"));
		}
	}
}
