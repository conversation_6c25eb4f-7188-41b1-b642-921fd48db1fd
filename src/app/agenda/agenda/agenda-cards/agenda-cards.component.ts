import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnDestroy,
	OnInit,
	Renderer2,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";
import { AgendaMetaCards, AgendaView } from "treino-api";
import { AgendaCardsStateService } from "../services/agenda-cards-state.service";
import { Subscription } from "rxjs";
import { TreinoApiConfiguracoesTreinoService } from "treino-api";

declare var moment;

@Component({
	selector: "pacto-agenda-cards",
	templateUrl: "./agenda-cards.component.html",
	styleUrls: ["./agenda-cards.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaCardsComponent implements OnInit, OnDestroy {
	private subscriptions: Subscription = new Subscription();
	loading = false;
	indexColunas = 0;
	agenda: AgendaMetaCards = null;
	cabecalhoAberto = true;
	@ViewChild("diaSemanaTexto", { static: true })
	xingling: TraducoesXinglingComponent;
	@ViewChild("cabecalho_agenda", { static: false }) cabecalhoAgenda: ElementRef;
	@ViewChild("cabecalho_fixo", { static: false }) cabecalhoFixo: ElementRef;
	@ViewChild("filtrosAgendaCardsComponent", { static: false })
	filtrosAgendaCardsComponent: ElementRef;
	public isFilaDeEsperaHabilitada = false;

	constructor(
		private cd: ChangeDetectorRef,
		private renderer: Renderer2,
		private agendaStateService: AgendaCardsStateService,
		public dialog: NgbActiveModal,
		private configTreinoService: TreinoApiConfiguracoesTreinoService
	) {}

	ngOnInit() {
		this.carregarAgenda();
		this.subscriptions.add(
			this.agendaStateService.agendamentoRemovido.subscribe(() => {
				this.agendaStateService.updateAll();
			})
		);
		this._initFilaDeEsperaConfig();
		setTimeout(() => {
			this.onScroll(null);
		}, 1000);
	}

	ngOnDestroy() {
		this.subscriptions.unsubscribe();
	}

	carregarAgenda() {
		this.agendaStateService.stateUpdate$.subscribe((loading) => {
			this.loading = loading;
			if (!loading) {
				this.agenda = this.agendaStateService.cards;
			}
			this.hideTooltip();
			this.cd.detectChanges();
		});
	}

	hideTooltip() {
		try {
			const elemento = document.getElementById("cat-tooltip-slot-card-item");
			elemento.style.display = "none";
		} catch (e) {}
	}

	isToday(weekday) {
		if (
			this.servicoView ||
			this.servicoView ||
			this.modalidadeView ||
			this.ambienteView
		) {
			return false;
		}
		const day = moment().format("YYYYMMDD");
		return moment(weekday).format("YYYYMMDD") === day;
	}

	diaConverter(diaStr: string): string {
		if (
			this.servicoView ||
			this.servicoView ||
			this.modalidadeView ||
			this.ambienteView
		) {
			return "";
		}
		return moment(diaStr).format("DD");
	}

	get diaView() {
		return this.agendaStateService.view === AgendaView.DIA;
	}

	get servicoView() {
		return this.agendaStateService.view === AgendaView.SERVICO;
	}

	get modalidadeView() {
		return this.agendaStateService.view === AgendaView.MODALIDADE;
	}

	get ambienteView() {
		return this.agendaStateService.view === AgendaView.AMBIENTE;
	}

	isElementVisible(element: HTMLElement): boolean {
		const rect = element.getBoundingClientRect();
		const windowHeight =
			window.innerHeight || document.documentElement.clientHeight;
		const windowWidth =
			window.innerWidth || document.documentElement.clientWidth;

		return (
			rect.top >= 0 &&
			rect.left >= 0 &&
			rect.bottom <= windowHeight &&
			rect.right <= windowWidth
		);
	}

	diaSemana(diaStr): string {
		if (this.servicoView || this.modalidadeView || this.ambienteView) {
			return diaStr;
		}
		const diasSemana = [
			"Domingo",
			"Segunda",
			"Terça",
			"Quarta",
			"Quinta",
			"Sexta",
			"Sábado",
		];
		return diasSemana[moment(diaStr).toDate().getDay()];
	}

	onScroll(event): void {
		if (this.cabecalhoAgenda) {
			const div1Visible = this.isElementVisible(
				this.cabecalhoAgenda.nativeElement
			);
			if (div1Visible) {
				this.renderer.addClass(this.cabecalhoFixo.nativeElement, "hidden");
			} else {
				this.renderer.removeClass(this.cabecalhoFixo.nativeElement, "hidden");
			}
		}
	}

	toggleCabecalho() {
		this.cabecalhoAberto = !this.cabecalhoAberto;
		if (this.cabecalhoAberto) {
			this.renderer.removeClass(
				this.cabecalhoFixo.nativeElement,
				"cabecalhoFechado"
			);
			this.renderer.removeClass(
				this.cabecalhoAgenda.nativeElement,
				"cabecalhoFechado"
			);
			this.renderer.removeClass(
				this.filtrosAgendaCardsComponent.nativeElement,
				"hidden"
			);
		} else {
			this.renderer.addClass(
				this.cabecalhoFixo.nativeElement,
				"cabecalhoFechado"
			);
			this.renderer.addClass(
				this.cabecalhoAgenda.nativeElement,
				"cabecalhoFechado"
			);
			this.renderer.addClass(
				this.filtrosAgendaCardsComponent.nativeElement,
				"hidden"
			);
		}
	}

	get exibirNavegacaoColunas(): boolean {
		return this.agenda && this.agenda.dias && this.agenda.dias.length > 7;
	}

	colunas() {
		if (this.agenda && this.agenda.dias) {
			return this.agenda.dias.length < 8
				? this.agenda.dias
				: this.agenda.dias.slice(this.indexColunas, 7 + this.indexColunas);
		}
		return [];
	}

	voltarColuna() {
		if (this.indexColunas > 0) {
			this.indexColunas = this.indexColunas - 1;
			this.cd.detectChanges();
		}
	}

	avancarColuna() {
		if (
			this.agenda &&
			this.agenda.dias &&
			this.indexColunas < this.agenda.dias.length - 7
		) {
			this.indexColunas = this.indexColunas + 1;
			this.cd.detectChanges();
		}
	}

	get gridSize(): string {
		return "grid" + this.colunas().length;
	}

	private _initFilaDeEsperaConfig() {
		this.configTreinoService
			.getConfiguracoesAplicativo()
			.subscribe((config) => {
				this.isFilaDeEsperaHabilitada = config && config.habilitar_fila_espera;
			});
	}
}
