@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.title-row {
	display: flex;
	align-items: center;
	cursor: pointer;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	user-select: none;

	.title-label {
		font-size: 16px;
		line-height: 28px;
		font-weight: 600;
		color: $pretoPri;
		flex-grow: 1;
	}

	i.pct {
		color: $pretoPri;
		font-size: 22px;
	}

	&:focus {
		outline: 2px solid #007bff;
		outline-offset: 2px;
	}

	&:active {
		background-color: rgba(0, 0, 0, 0.05);
	}
}

.command-buttons-filter {
	margin-top: 15px;
	display: flex;
	justify-content: space-between;

	.command {
		line-height: 24px;
		border: 1px solid #a1a5aa;
		border-radius: 4px;
		padding: 0px 5px;
		color: #a1a5aa;
		font-size: 12px;
		cursor: pointer;
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;

		&:hover {
			background-color: rgba(243, 243, 243, 0.5);
		}

		&:focus {
			outline: 2px solid #007bff;
			outline-offset: 2px;
		}

		&:active {
			background-color: rgba(0, 0, 0, 0.1);
			transform: scale(0.98);
		}

		&.light {
			color: #b4b7bb;
			border-color: #b4b7bb;
		}

		i {
			font-size: 12px;
		}
	}
}

.options {
	margin-top: 12px;

	.option {
		display: flex;
		align-items: center;
		margin-top: 14px;
		cursor: pointer;
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
		min-height: 44px; /* Tamanho mínimo recomendado para touch no iOS */
		padding: 5px 0;

		&:focus {
			outline: 2px solid #007bff;
			outline-offset: 2px;
		}

		&:active {
			background-color: rgba(0, 0, 0, 0.05);
			transform: scale(0.98);
		}

		&:hover {
			background-color: rgba(0, 0, 0, 0.02);
		}
	}

	.checkmark {
		color: $branco;
		border-radius: 2px;
		width: 13px;
		height: 13px;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 7px;
		font-size: 12px;
		border: 1px solid #51555a;
	}

	.icon-circle {
		width: 18px;
		height: 18px;
		flex-shrink: 0;
		font-size: 12px;
		border-radius: 9px;
		padding-left: 0.5px;
		margin-right: 3px;
		color: $branco;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.option-label {
		font-size: 13.8px;
		font-weight: 400;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		text-transform: capitalize;
	}
}
