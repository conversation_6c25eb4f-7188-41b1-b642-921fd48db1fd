import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
} from "@angular/core";

export interface FilterOption {
	id: any;
	label: string;
	color?: string;
	/**
	 * Example: 'pct-eye'
	 */
	pactoIcon?: string;
}

@Component({
	selector: "pacto-agenda-layout-v2-filter",
	templateUrl: "./agenda-layout-v2-filter.component.html",
	styleUrls: ["./agenda-layout-v2-filter.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaLayoutV2FilterComponent implements OnInit, OnChanges {
	@Input() titulo = "Título dos Filtros";
	@Input() open = true;
	@Input() options: FilterOption[] = [];
	@Input() selected: any[] = [];
	@Output() optionsChanged: EventEmitter<any[]> = new EventEmitter();

	private lastClickTime = 0;
	private clickDelay = 300;

	constructor() {}

	ngOnInit() {}

	ngOnChanges(changes) {}

	verTodosHandler(event?: Event) {
		if (this.preventDuplicateClick(event)) return;

		const current = [];
		this.options.forEach((option) => current.push(option.id));
		this.optionsChanged.emit(current);
	}

	limparHandler(event?: Event) {
		if (this.preventDuplicateClick(event)) return;

		this.optionsChanged.emit([]);
	}

	toggleOptionHandler(id: any, event?: Event) {
		if (this.preventDuplicateClick(event)) return;

		const current = Object.assign([], this.selected);
		if (this.isSelected(id)) {
			const indexToRemove = this.selected.findIndex((i) => i === id);
			current.splice(indexToRemove, 1);
		} else {
			current.push(id);
		}
		this.optionsChanged.emit(current);
	}

	isSelected(id: any): boolean {
		const match = this.selected.findIndex((i) => i === id) >= 0;
		return match;
	}

	getColor(id: any) {
		const option = this.options.find((i) => i.id === id);
		return option.color ? option.color : "#51555A";
	}

	toggleHandler(event?: Event) {
		if (this.preventDuplicateClick(event)) return;

		this.open = !this.open;
	}

	private preventDuplicateClick(event?: Event): boolean {
		if (event) {
			event.preventDefault();
			event.stopPropagation();
		}

		const now = Date.now();
		if (now - this.lastClickTime < this.clickDelay) {
			return true;
		}
		this.lastClickTime = now;
		return false;
	}
}
