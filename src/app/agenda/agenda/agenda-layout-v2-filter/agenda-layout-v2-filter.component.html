<div
	(click)="toggleHandler($event)"
	(touchstart)="toggleHandler($event)"
	class="title-row"
	role="button"
	tabindex="0"
	[attr.aria-expanded]="open"
	(keydown.enter)="toggleHandler($event)"
	(keydown.space)="toggleHandler($event)">
	<div class="title-label">{{ titulo }}</div>
	<i *ngIf="open" class="pct pct-chevron-down"></i>
	<i *ngIf="!open" class="pct pct-chevron-up"></i>
</div>

<div *ngIf="open" class="body">
	<div class="command-buttons-filter">
		<div
			(click)="verTodosHandler($event)"
			(touchstart)="verTodosHandler($event)"
			class="command"
			role="button"
			tabindex="0"
			(keydown.enter)="verTodosHandler($event)"
			(keydown.space)="verTodosHandler($event)"
			i18n="@@agenda-layout:ver-todos">
			<i class="pct pct-eye"></i>
			Ver todos
		</div>
		<div
			(click)="limparHandler($event)"
			(touchstart)="limparHandler($event)"
			class="command light"
			role="button"
			tabindex="0"
			(keydown.enter)="limparHandler($event)"
			(keydown.space)="limparHandler($event)"
			i18n="@@agenda-layout:limpar-selecao">
			<i class="pct pct-trash-2"></i>
			Limpar seleção
		</div>
	</div>

	<div class="options">
		<div
			(click)="toggleOptionHandler(option.id, $event)"
			(touchstart)="toggleOptionHandler(option.id, $event)"
			*ngFor="let option of options"
			class="option"
			role="button"
			tabindex="0"
			[attr.aria-pressed]="isSelected(option.id)"
			(keydown.enter)="toggleOptionHandler(option.id, $event)"
			(keydown.space)="toggleOptionHandler(option.id, $event)">
			<div
				[ngClass]="{ selected: isSelected(option.id) }"
				[ngStyle]="{
					borderColor: getColor(option.id),
					backgroundColor: isSelected(option.id)
						? getColor(option.id)
						: '#ffffff'
				}"
				class="checkmark">
				<i *ngIf="isSelected(option.id)" class="pct pct-check"></i>
			</div>
			<div
				*ngIf="option.pactoIcon"
				[ngStyle]="{
					backgroundColor: getColor(option.id)
				}"
				class="icon-circle">
				<i class="pct {{ option.pactoIcon }}"></i>
			</div>
			<div
				[ngStyle]="{
					color: getColor(option.id)
				}"
				class="option-label">
				{{ option.label }}
			</div>
		</div>
	</div>
</div>
