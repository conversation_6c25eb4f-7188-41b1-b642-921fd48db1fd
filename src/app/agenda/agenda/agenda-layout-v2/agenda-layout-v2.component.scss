@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: flex;
	flex-direction: column;
	height: calc(100vh - 88px);
	z-index: 0;
	background-color: #eff2f7;
	padding: 0px 40px;
}

.top-row {
	display: flex;
	align-items: center;

	pacto-agenda-layout-v2-header {
		flex-grow: 1;
	}

	.extra-buttons {
		flex-shrink: 0;
	}
}

.panel-agenda {
	background-color: $branco;
	border-top-left-radius: 6px;
	border-top-right-radius: 6px;
	padding-right: 20px;
	flex-grow: 1;
	display: flex;
}

.filtros-agenda {
	width: 200px;
	margin-left: 32px;
	margin-right: 60px;
	flex-grow: 0;
	flex-shrink: 0;
}

//
// DIAS DA SEMANA
.dias-semana-wrapper {
	border-bottom: 1px solid #dcdddf;
}

.dias-semana {
	display: flex;
	margin-left: 70px;

	.dia-semana {
		text-align: center;
		height: 100px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		flex-direction: column;

		.circle {
			width: 84px;
			height: 84px;
			border-radius: 42px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}

		&.selected {
			.circle {
				background-color: $azulimPri;
			}

			.mes,
			.semana {
				color: $branco;
			}
		}

		.mes {
			font-size: 28px;
			padding-top: 8px;
			color: $pretoPri;
			line-height: 22px;
		}

		.semana {
			font-size: 12px;
			color: #b4b7bb;
		}
	}
}

//
// MARCADOR DE HORAS
.marcador-horas {
	padding: 29px 0px;
	border-right: 1px solid #dcdddf;
	width: 70px;
}

.hora {
	display: flex;
	justify-content: flex-end;
	align-items: center;

	.hora-label {
		@extend .type-p-small;
		font-size: 14px !important;
		text-align: center;
		margin-right: 10px;
		line-height: 60px;
		color: $gelo04;
	}

	.hora-tick-mark {
		border-bottom: 2px solid #dcdddf;
		width: 15px;
		height: 0px;
	}
}

//
// CONTEUDO
.conteudo-agenda {
	flex-grow: 1;
	position: relative;
}

.content-wrapper {
	width: 100%;
	display: flex;
	overflow-y: clip !important;
}

.loading-container {
	position: absolute;
	left: 0px;
	right: 0px;
	top: 100px;
	bottom: 0px;
	background-color: #3333331f;
	display: flex;
	justify-content: center;
	align-items: center;

	.message {
		color: $pretoPri;
		font-size: 14px;
	}
}

.inner-wrapper-container {
	display: flex;
	width: 100%;
	position: relative;

	&.loading {
		filter: blur(1px);
		pointer-events: none;
	}
}

.time-grid-wrapper {
	flex-grow: 1;
	display: flex;
}

.eventos-dia-column {
	padding-right: 1%;
	z-index: 1;

	&:hover {
		z-index: 2;
	}

	&.row-modal-open {
		z-index: 3;
	}
}

.hour-marker-wrapper {
	position: absolute;
	top: 0px;
	left: 70px;
	right: 0px;
	display: flex;
	pointer-events: none;

	.marker-column {
		width: 14.2%;
		border-right: 2px solid #dcdddf;

		&.selected {
			.hour-marker {
				background-color: $azulimPastel;
			}
		}

		.hour-marker {
			width: 100%;
			height: 60px;
			background-color: #f3f3f4;
			border-bottom: 2px solid #dcdddf;
		}
	}
}
