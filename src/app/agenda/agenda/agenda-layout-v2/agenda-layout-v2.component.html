<div class="top-row">
	<pacto-agenda-layout-v2-header
		(periodFilterUpdate)="periodFilterUpdate.emit($event)"
		[periodFilter]="periodFilter"
		[title]="title"></pacto-agenda-layout-v2-header>
	<ng-content select="[extra-buttons]"></ng-content>
</div>

<div class="panel-agenda">
	<div class="filtros-agenda">
		<ng-content select="[side-filters]"></ng-content>
	</div>

	<div class="conteudo-agenda">
		<div class="dias-semana-wrapper">
			<div class="dias-semana">
				<div
					(click)="goToDay(day)"
					*ngFor="let day of days"
					[ngClass]="{ selected: isToday(day) }"
					[ngStyle]="{
						width: widthOfEachDay()
					}"
					class="dia-semana">
					<div class="circle">
						<div class="mes">{{ day | date : "dd" }}</div>
						<div class="semana">{{ day | date : "EEE" }}</div>
					</div>
				</div>
			</div>
		</div>

		<div
			[maxHeight]="'calc(100vh - 289px)'"
			[style.overflow]="'visible'"
			class="content-wrapper agenda-scroll-container"
			pactoCatSmoothScroll>
			<div [ngClass]="{ loading: loading }" class="inner-wrapper-container">
				<div class="marcador-horas">
					<div *ngFor="let hora of horas" class="hora">
						<div *ngIf="hora <= 12" class="hora-label">{{ hora }} AM</div>
						<div *ngIf="hora > 12" class="hora-label">{{ hora }} PM</div>
						<div class="hora-tick-mark"></div>
					</div>
				</div>

				<div class="time-grid-wrapper">
					<div
						(reloadEvent)="reloadAgenda()"
						*ngFor="let day of days; let index = index"
						[clicavel]="true"
						[context]="gridContext"
						[dayColumn]="index"
						[day]="day"
						[events]="getEventsByIndex(index)"
						[ngClass]="{ 'row-modal-open': columnIndexWithModalOpen === index }"
						[ngStyle]="{ width: widthOfEachDay() }"
						class="eventos-dia-column"
						pacto-agenda-time-grid></div>

					<div class="hour-marker-wrapper">
						<div
							*ngFor="let day of days"
							[ngClass]="{ selected: isToday(day) }"
							[ngStyle]="{ width: widthOfEachDay() }"
							class="marker-column">
							<div *ngFor="let item of blocos" class="hour-marker"></div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div *ngIf="loading" class="loading-container"></div>
	</div>
</div>
