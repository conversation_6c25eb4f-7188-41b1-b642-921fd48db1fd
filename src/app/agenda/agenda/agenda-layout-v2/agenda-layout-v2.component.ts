import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostListener,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	Output,
	SimpleChange,
	SimpleChanges,
} from "@angular/core";

import { Moment } from "moment";

import { AgendaView } from "treino-api";
import { AgendaPeriodFilter } from "../agenda.model";
import { DiscreteGridEvent } from "../agenda-time-grid/DiscreteGridManager";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import { AgendaTimeGridStateService } from "../agenda-time-grid/agenda-time-grid-state.service";
import { Subscription } from "rxjs";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";

declare var moment;

export interface AgendaEvento {
	/**
	 * HH:MM
	 *
	 * 8:00 AM => 08:00
	 * 4:00 PM => 16:00
	 */
	inicio: string;
	fim: string;
	renderComponent?: any;
	hoverComponent?: any;
	modalComponent?: any;
	/**
	 * Data to feed into the render and over components.
	 *
	 * This will be avaiable as an @Input slot of type DiscreteGridSlot
	 */
	payload?: any;
}

@Component({
	selector: "pacto-agenda-layout-v2",
	templateUrl: "./agenda-layout-v2.component.html",
	styleUrls: ["./agenda-layout-v2.component.scss"],
})
export class AgendaLayoutV2Component implements OnInit, OnChanges, OnDestroy {
	@Input() title = "Agenda v2 Layout";
	@Input() loading = false;
	@Input() periodFilter: AgendaPeriodFilter = {
		date: moment().format("YYYYMMDD"),
		view: AgendaView.DIA,
	};
	@Input() eventos: AgendaEvento[][];
	@Output() periodFilterUpdate: EventEmitter<AgendaPeriodFilter> =
		new EventEmitter();
	@Output() reloadEvent: EventEmitter<any> = new EventEmitter();

	horas = Array(23).fill(0);
	blocos = Array(24).fill(0);
	discreteGridevents: DiscreteGridEvent[][] = [];
	gridContext = { forceUpdate$: new EventEmitter() };
	columnIndexWithModalOpen: number;

	private modalOpenSub: Subscription;

	constructor(
		private util: AgendaUtilsService,
		private cd: ChangeDetectorRef,
		private agendaGridState: AgendaTimeGridStateService,
		private windowUtil: WindowUtilService
	) {
		this.horas.forEach((v, i) => {
			this.horas[i] = i + 1;
		});
		this.blocos.forEach((v, i) => {
			this.blocos[i] = i + 1;
		});
	}

	@HostListener("document:click", ["$event"])
	public clickHandler($event: MouseEvent) {
		this.agendaGridState.closeAllModal$.emit(true);
	}

	ngOnInit() {
		this.convertEvents();
		this.modalOpenSub = this.agendaGridState.modalOpen$.subscribe((open) => {
			if (open) {
				this.columnIndexWithModalOpen = open.column;
			} else {
				this.columnIndexWithModalOpen = -1;
			}
			this.cd.detectChanges();
		});
	}

	ngOnDestroy() {
		this.modalOpenSub.unsubscribe();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.eventos) {
			this.convertEvents();
		}
	}

	widthOfEachDay() {
		if (this.days.length) {
			return `${100 / this.days.length}%`;
		} else {
			return "100%";
		}
	}

	reloadAgenda() {
		this.reloadEvent.emit();
	}

	getEventsByIndex(index: number) {
		if (this.discreteGridevents && this.discreteGridevents[index]) {
			return this.discreteGridevents[index];
		} else {
			return [];
		}
	}

	get days() {
		const result = [];
		if (this.periodFilter.view === AgendaView.DIA) {
			const day = moment(this.periodFilter.date, "YYYYMMDD");
			result.push(day.valueOf());
			return result;
		} else if (this.periodFilter.view === AgendaView.SEMANA) {
			const firstDay = this.getMondayOfWeek(this.periodFilter.date);
			result.push(firstDay.valueOf());
			for (let index = 1; index <= 6; index++) {
				firstDay.add(1, "days");
				result.push(firstDay.valueOf());
			}
			return result;
		} else {
			return [];
		}
	}

	/**
	 * Monday of week within which the
	 * reference day resides.
	 *
	 * @param day YYYYMMDD
	 */
	getMondayOfWeek(day: string): Moment {
		const referenceDay = moment(day, "YYYYMMDD");
		if (referenceDay.day() === 0) {
			referenceDay.day(-6);
		} else {
			referenceDay.day(1);
		}
		return referenceDay;
	}

	goToDay(day: number) {
		const dia = moment(day);
		this.periodFilterUpdate.emit({
			view: AgendaView.DIA,
			date: dia.format("YYYYMMDD"),
		});
	}

	isToday(day: number) {
		const today = moment().format("YYYYMMDD");
		const dayFormat = moment(day).format("YYYYMMDD");
		return dayFormat === today;
	}

	private convertEvents() {
		this.discreteGridevents = [];
		this.eventos.forEach((eventosDia) => {
			const eventosDiaGrid: DiscreteGridEvent[] = [];
			eventosDia.forEach((evento) => {
				eventosDiaGrid.push(
					this.createDiscreteGridEvent(
						evento.inicio,
						evento.fim,
						evento.payload,
						evento.renderComponent,
						evento.hoverComponent,
						evento.modalComponent
					)
				);
			});
			this.discreteGridevents.push(eventosDiaGrid);
		});
	}

	/**
	 *
	 * @param inicio HH:mm
	 * @param fim HH:mm
	 */
	private createDiscreteGridEvent(
		inicio: string,
		fim: string,
		payload: any,
		component: any,
		hoverComponent: any,
		modalComponent: any
	): DiscreteGridEvent {
		const start = this.util.convertTimeLabelIntoMinutes(inicio);
		const end = this.util.convertTimeLabelIntoMinutes(fim);
		const duration = end - start;

		/**
		 * Duration
		 */
		let durationUnit;
		if (duration <= 15) {
			durationUnit = 1;
		} else {
			durationUnit = this.windowUtil.roundToMultipleOf(duration, 15) / 15;
		}

		/**
		 * Start
		 */
		const startUnit = this.windowUtil.roundToMultipleOf(start, 15) / 15;

		return new DiscreteGridEvent({
			start: startUnit,
			height: durationUnit,
			payload,
			hoverComponent,
			modalComponent,
			component,
		});
	}
}
