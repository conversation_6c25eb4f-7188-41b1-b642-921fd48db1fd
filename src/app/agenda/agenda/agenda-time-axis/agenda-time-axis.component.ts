import {
	Component,
	OnInit,
	ViewChild,
	ChangeDetectorRef,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";

@Component({
	// tslint:disable-next-line:component-selector
	selector: "[pacto-agenda-time-axis]",
	templateUrl: "./agenda-time-axis.component.html",
	styleUrls: ["./agenda-time-axis.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaTimeAxisComponent implements OnInit {
	@ViewChild("scrollHandle", { static: false }) scrollHandle;
	@Input() horaInicial;
	@Input() horaFinal;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {}

	get horas() {
		const result = [];
		const inicial = this.horaInicial;
		const final = this.horaFinal;
		for (let i = inicial; i <= final; i++) {
			result.push(i);
		}
		return result;
	}

	setScroll(height) {
		this.scrollHandle.nativeElement.scrollTop = height;
	}
}
