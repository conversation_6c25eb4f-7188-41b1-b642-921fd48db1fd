import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	HostListener,
	ElementRef,
	ViewChild,
	Output,
	EventEmitter,
	ChangeDetectorRef,
	HostBinding,
} from "@angular/core";
import { WindowUtilService } from "@base-core/utils/window-util.service";

@Component({
	selector: "pacto-agenda-layout-v2-datepicker",
	templateUrl: "./agenda-layout-v2-datepicker.component.html",
	styleUrls: ["./agenda-layout-v2-datepicker.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaLayoutV2DatepickerComponent implements OnInit {
	@ViewChild("calendar", { read: ElementRef, static: false }) calendar;
	@Input() dia: number;
	@Output() dateSelect: EventEmitter<any> = new EventEmitter();
	@Output() datepickerOpen: EventEmitter<boolean> = new EventEmitter();
	@HostBinding("id") identifyDatePicker: string;
	opened = false;

	constructor(
		private ref: ElementRef,
		private cd: ChangeDetectorRef,
		private util: WindowUtilService
	) {}

	ngOnInit() {
		this.identifyDatePicker = "agenda-datepicker";
	}

	get _dia() {
		return new Date(this.dia);
	}

	selected($event) {
		this.dateSelect.emit($event.valueOf());
		this.datepickerOpen.emit(false);
		setTimeout(() => {
			this.opened = false;
			this.cd.detectChanges();
		});
	}

	@HostListener("document:click", ["$event.target"])
	clickHandler(target) {
		const button = this.isButtonClick(target);
		if (button) {
			this.opened = !this.opened;
			this.datepickerOpen.emit(this.opened);
		}
	}

	private innerClick(target) {
		return this.util.isDescendant(this.ref.nativeElement, target);
	}

	private isButtonClick(target) {
		const innerClick = this.innerClick(target);
		if (this.calendar) {
			const calendarClick = this.util.isDescendant(
				this.calendar.nativeElement,
				target
			);
			return innerClick && !calendarClick;
		} else {
			return innerClick;
		}
	}
}
