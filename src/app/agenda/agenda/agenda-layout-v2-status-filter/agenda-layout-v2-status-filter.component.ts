import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FilterOption } from "../agenda-layout-v2-filter/agenda-layout-v2-filter.component";
import { AgendaAgendamentoStatus } from "treino-api";

@Component({
	selector: "pacto-agenda-layout-v2-status-filter",
	templateUrl: "./agenda-layout-v2-status-filter.component.html",
	styleUrls: ["./agenda-layout-v2-status-filter.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaLayoutV2StatusFilterComponent implements OnInit {
	@Input() open = true;
	@Input() selected: AgendaAgendamentoStatus[] = [];
	@Output() optionsChanged: EventEmitter<AgendaAgendamentoStatus[]> =
		new EventEmitter();

	constructor() {}

	statusFilterOptions: FilterOption[] = [
		{
			id: AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO,
			label: "Aguardando Confirmação",
			pactoIcon: "pct-more-horizontal",
			color: "#51555A",
		},
		{
			id: AgendaAgendamentoStatus.CONFIRMADO,
			label: "Confirmado",
			color: "#0380E3",
			pactoIcon: "pct-check",
		},
		{
			id: AgendaAgendamentoStatus.EXECUTADO,
			label: "Executado",
			color: "#28AB45",
			pactoIcon: "pct-check",
		},
		{
			id: AgendaAgendamentoStatus.FALTOU,
			label: "Faltou",
			color: "#D6A10F",
			pactoIcon: "pct-minus",
		},
	];

	ngOnInit() {}
}
