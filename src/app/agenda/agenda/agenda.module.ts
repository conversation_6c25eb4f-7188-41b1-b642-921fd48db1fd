// tslint:disable-next-line: import-spacing
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";

import { AgendaTimeGridComponent } from "./agenda-time-grid/agenda-time-grid.component";
import { AgendaTimeAxisComponent } from "./agenda-time-axis/agenda-time-axis.component";
import { AgendaTurmaComponent } from "./agenda-turma/agenda-turma.component";
import { TurmaDiaComponent } from "./agenda-turma/turma-dia/turma-dia.component";
import { TurmaSemanaComponent } from "./agenda-turma/turma-semana/turma-semana.component";
import { TurmaFilterModalComponent } from "./agenda-turma/turma-filter-modal/turma-filter-modal.component";
import { AgendaControlComponent } from "./agenda-turma/agenda-control/agenda-control.component";
import { TurmaCardComponent } from "./agenda-turma/turma-card/turma-card.component";
import { TurmaCardDetailComponent } from "./agenda-turma/turma-card-detail/turma-card-detail.component";
import { TimeGridContainerComponent } from "./agenda-time-grid/time-grid-container/time-grid-container.component";
import { AgendaTurmaViewComponent } from "./agenda-turma/agenda-turma-view/agenda-turma-view.component";
import { SumarioComponent } from "./agenda-turma/agenda-turma-view/sumario/sumario.component";
import { TurmaReposicaoModalComponent } from "./agenda-turma/turma-reposicao-modal/turma-reposicao-modal.component";
import { TurmaAulaExperimentalModalComponent } from "./agenda-turma/turma-aula-experimental-modal/turma-aula-experimental-modal.component";
import { TurmaMarcacaoModalComponent } from "./agenda-turma/turma-marcacao-modal/turma-marcacao-modal.component";
import { TurmaDesmarcarModalComponent } from "./agenda-turma/turma-desmarcar-modal/turma-desmarcar-modal.component";
import { TurmaSubstituirProfessorModalComponent } from "./agenda-turma/turma-substituir-professor-modal/turma-substituir-professor-modal.component";
import { AgendamentoCardComponent } from "./agenda-servicos/agendamento-card/agendamento-card.component";
import { TurmaRemoverAulaCheiaModalComponent } from "./agenda-turma/turma-remover-aula-cheia-modal/turma-remover-aula-cheia-modal.component";
import { TurmaJustificativaFaltaAulaCheiaModalComponent } from "./agenda-turma/turma-justificativa-falta-aula-cheia-modal/turma-justificativa-falta-aula-cheia-modal.component";
import { AgendamentoEditModalComponent } from "./agenda-servicos/agendamento-edit-modal/agendamento-edit-modal.component";
import { ServicoAgendamentosComponent } from "./agenda-servicos/agendamentos/agendamentos.component";
import { AgendaLayoutV2DatepickerComponent } from "./agenda-layout-v2-datepicker/agenda-layout-v2-datepicker.component";
import { CatPersonAvatarComponent, CatTolltipModule } from "ui-kit";
import { AgendaBiComponent } from "./agenda-bi/agenda-bi.component";
import { TreinoBiModule } from "../../treino/treino-bi/treino-bi.module";
import { AgendaBiStateService } from "./agenda-bi/agenda-bi-state";

import { AgendaLayoutV2HeaderComponent } from "./agenda-layout-v2-header/agenda-layout-v2-header.component";
import { AgendaLayoutV2Component } from "./agenda-layout-v2/agenda-layout-v2.component";
import { AgendaLayoutV2FilterComponent } from "./agenda-layout-v2-filter/agenda-layout-v2-filter.component";
import { AgendaLayoutV2StatusFilterComponent } from "./agenda-layout-v2-status-filter/agenda-layout-v2-status-filter.component";
import { ServicoDisponibilidadesComponent } from "./agenda-servicos/disponibilidades/disponibilidades.component";
import { AgendaServicosStateService } from "./agenda-servicos/agenda-servicos-state.service";
import { AgendamentoHoverCardComponent } from "./agenda-servicos/agendamento-hover-card/agendamento-hover-card.component";
import { AgendamentoModalCardComponent } from "./agenda-servicos/agendamento-modal-card/agendamento-modal-card.component";
import { AgendamentoDeleteModalComponent } from "./agenda-servicos/agendamento-delete-modal/agendamento-delete-modal.component";
// tslint:disable-next-line:max-line-length
import { AgendaDisponibilidadeFiltroComponent } from "./agenda-servicos/disponibilidades/agenda-disponibilidade-filtro/agenda-disponibilidade-filtro.component";
import { DisponibilidadeCardComponent } from "./agenda-servicos/disponibilidade-card/disponibilidade-card.component";
import { DisponibilidadeModalCardComponent } from "./agenda-servicos/disponibilidade-modal-card/disponibilidade-modal-card.component";
import { DisponibilidadeDeleteModalComponent } from "./agenda-servicos/disponibilidade-delete-modal/disponibilidade-delete-modal.component";
import { DisponibilidadeEditModalComponent } from "./agenda-servicos/disponibilidade-edit-modal/disponibilidade-edit-modal.component";
// tslint:disable-next-line:max-line-length
import { DisponibilidadeWeekdaysConfigComponent } from "./agenda-servicos/disponibilidade-weekdays-config/disponibilidade-weekdays-config.component";
import { AgendamentoCreateModalComponent } from "./agenda-servicos/agendamento-create-modal/agendamento-create-modal.component";
import { DisponibilidadesGuard } from "@base-core/guards/disponibilidades.guard";
import { AgendaRootComponent } from "./agenda-root/agenda-root.component";
import { AgendaCardsComponent } from "./agenda-cards/agenda-cards.component";
import { CardItemAgendaComponent } from "./agenda-cards/card-item-agenda/card-item-agenda.component";
import { DetalhamentoAulaComponent } from "./agenda-cards/detalhamento-aula/detalhamento-aula.component";
import { ListaAlunosDetalhamentoComponent } from "./agenda-cards/detalhamento-aula/lista-alunos-detalhamento/lista-alunos-detalhamento.component";
import { FiltrosAgendaCardsComponent } from "./agenda-cards/filtros-agenda-cards/filtros-agenda-cards.component";
import { CabecalhoAgendaComponent } from "./agenda-cards/cabecalho-agenda/cabecalho-agenda.component";
import { CardDisponibilidadeComponent } from "./agenda-cards/card-disponibilidade/card-disponibilidade.component";
import { CardServicoComponent } from "./agenda-cards/card-servico/card-servico.component";
import { ModalServicosDisponiveisComponent } from "./agenda-cards/modal-servicos-disponiveis/modal-servicos-disponiveis.component";
import { ModalAgendamentoComponent } from "./agenda-cards/modal-agendamento/modal-agendamento.component";
import { CardLocacaoComponent } from "./agenda-cards/card-locacao/card-locacao.component";
import { AgendamentoLocacaoComponent } from "./agenda-cards/agendamento-locacao/agendamento-locacao.component";
import { FixarAlunoComponent } from "./agenda-cards/detalhamento-aula/lista-alunos-detalhamento/fixar-aluno/fixar-aluno.component";
import { EsperaDesmarcarModalComponent } from "./agenda-turma/filadeespera-desmarcar-modal/espera-desmarcar-modal-component";
import { DetalhamentoLocacaoComponent } from "./agenda-cards/detalhamento-locacao/detalhamento-locacao.component";
import { ModalCancelarAgendamentoLocacaoComponent } from "./agenda-cards/modal-cancelar-agendamento-locacao/modal-cancelar-agendamento-locacao.component";
import { ModalJustificativaCancelamentoAgendamentoLocacaoComponent } from "./agenda-cards/modal-justificativa-cancelamento-agendamento-locacao/modal-justificativa-cancelamento-agendamento-locacao.component";
import { ModalReagendarLocacaoComponent } from "./agenda-cards/modal-reagendar-locacao/modal-reagendar-locacao.component";
import { ReagendarLocacaoComponent } from "./agenda-cards/reagendar-locacao/reagendar-locacao.component";
import { ModalFinalizarVendaLocacaoComponent } from "./agenda-cards/modal-finalizar-venda-locacao/modal-finalizar-venda-locacao.component";
import { ModalAgendaEditarAulaComponent } from "./agenda-turma/modal-agenda-editar-aula/modal-agenda-editar-aula.component";
import { NgxMaskModule } from "ngx-mask";
import { ModalAgendaEditarAulaSuccessComponent } from "./agenda-turma/modal-agenda-editar-aula/modal-agenda-editar-aula-success/modal-agenda-editar-aula-success.component";
import { ModalAgendaReplicarAlteracoesAulaComponent } from "./agenda-turma/modal-agenda-editar-aula/modal-agenda-replicar-alteracoes-aula/modal-agenda-replicar-alteracoes-aula.component";
import { HomePageModule } from "pacto-layout";
import { HomeComponent } from "./home/<USER>";
import { AddFilaEsperaTurmaModalComponent } from "./agenda-cards/modal-add-fila-espera-turma/modal-add-fila-espera-turma.component";
import { CreateRecepFilaEsperaTurmaModalComponent } from "./agenda-cards/modal-add-fila-espera-turma/modal-create-recep-fila-espera-turma/modal-create-recep-fila-espera-turma.component";
import { ReservaEquipamentoModalComponent } from "./agenda-cards/detalhamento-aula/reserva-equipamento-modal/reserva-equipamento-modal.component";
import { MapSelectionComponent } from "./agenda-cards/detalhamento-aula/reserva-equipamento-modal/map-selection/map-selection.component";
import { DetalhamentoLocacaoPlayComponent } from "./agenda-cards/detalhamento-locacao-play/detalhamento-locacao-play.component";
import { ListaAlunosDetalhamentoLocacaoPlayComponent } from "./agenda-cards/detalhamento-locacao-play/lista-alunos-detalhamento-locacao-play/lista-alunos-detalhamento-locacao-play.component";
import { ModalListaDataIndisponibilidadeComponent } from "./agenda-cards/modal-lista-data-indisponibilidade/modal-lista-data-indisponibilidade.component";

const routes: Routes = [
	{
		path: "",
		component: AgendaRootComponent,
		children: [
			{
				path: "home",
				component: HomeComponent,
			},
			{
				path: "bi",
				component: AgendaBiComponent,
			},
			{
				path: "turmas",
				component: AgendaTurmaComponent,
			},
		],
	},
	{
		path: "cards",
		component: AgendaCardsComponent,
	},
	{
		path: "detalhamento-aula/:id/:dia",
		component: DetalhamentoAulaComponent,
	},
	{
		path: "detalhamento-locacao-play/:id/:dia",
		component: DetalhamentoLocacaoPlayComponent,
	},
	{
		path: "locacao/:id/:dia/:cod",
		component: AgendamentoLocacaoComponent,
	},
	{
		path: "detalhamento-locacao/:cod/:dia",
		component: DetalhamentoLocacaoComponent,
	},
	{
		path: "reagendar-locacao/:cod",
		component: ReagendarLocacaoComponent,
	},
	{
		path: "turmas",
		component: AgendaTurmaComponent,
	},
	{
		path: "servicos/agendamentos",
		component: ServicoAgendamentosComponent,
	},
	{
		path: "servicos/disponibilidades",
		component: ServicoDisponibilidadesComponent,
		canActivate: [DisponibilidadesGuard],
	},
];

@NgModule({
	imports: [
		FormsModule,
		CommonModule,
		BaseSharedModule,
		RouterModule.forChild(routes),
		ReactiveFormsModule,
		TreinoBiModule,
		CatTolltipModule,
		NgxMaskModule,
		HomePageModule,
	],
	declarations: [
		TurmaDiaComponent,
		TurmaSemanaComponent,
		AgendaTurmaComponent,
		AgendaTimeGridComponent,
		AgendaTimeAxisComponent,
		AgendaControlComponent,
		TurmaFilterModalComponent,
		TurmaCardComponent,
		TurmaCardDetailComponent,
		TimeGridContainerComponent,
		AgendaTurmaViewComponent,
		SumarioComponent,
		TurmaReposicaoModalComponent,
		TurmaAulaExperimentalModalComponent,
		TurmaMarcacaoModalComponent,
		TurmaDesmarcarModalComponent,
		TurmaSubstituirProfessorModalComponent,
		AgendaDisponibilidadeFiltroComponent,
		AgendaLayoutV2HeaderComponent,
		AgendamentoCardComponent,
		AgendamentoCreateModalComponent,
		DisponibilidadeModalCardComponent,
		DisponibilidadeCardComponent,
		TurmaRemoverAulaCheiaModalComponent,
		TurmaJustificativaFaltaAulaCheiaModalComponent,
		DisponibilidadeEditModalComponent,
		AgendamentoEditModalComponent,
		AgendamentoDeleteModalComponent,
		DisponibilidadeDeleteModalComponent,
		ServicoAgendamentosComponent,
		ServicoDisponibilidadesComponent,
		AgendaLayoutV2DatepickerComponent,
		AgendaLayoutV2Component,
		AgendaLayoutV2FilterComponent,
		AgendaLayoutV2StatusFilterComponent,
		AgendamentoHoverCardComponent,
		AgendamentoModalCardComponent,
		DisponibilidadeWeekdaysConfigComponent,
		AgendaBiComponent,
		AgendaRootComponent,
		AgendaCardsComponent,
		CardItemAgendaComponent,
		DetalhamentoAulaComponent,
		DetalhamentoLocacaoPlayComponent,
		DetalhamentoLocacaoComponent,
		ModalCancelarAgendamentoLocacaoComponent,
		ModalReagendarLocacaoComponent,
		ModalFinalizarVendaLocacaoComponent,
		ModalJustificativaCancelamentoAgendamentoLocacaoComponent,
		ListaAlunosDetalhamentoComponent,
		ListaAlunosDetalhamentoLocacaoPlayComponent,
		FiltrosAgendaCardsComponent,
		CabecalhoAgendaComponent,
		CardDisponibilidadeComponent,
		CardServicoComponent,
		ModalServicosDisponiveisComponent,
		ModalAgendamentoComponent,
		CardLocacaoComponent,
		AgendamentoLocacaoComponent,
		FixarAlunoComponent,
		EsperaDesmarcarModalComponent,
		ModalAgendaEditarAulaComponent,
		ModalAgendaEditarAulaSuccessComponent,
		ModalAgendaReplicarAlteracoesAulaComponent,
		HomeComponent,
		ReagendarLocacaoComponent,
		AddFilaEsperaTurmaModalComponent,
		CreateRecepFilaEsperaTurmaModalComponent,
		ReservaEquipamentoModalComponent,
		MapSelectionComponent,
		ModalListaDataIndisponibilidadeComponent,
	],
	entryComponents: [
		TurmaFilterModalComponent,
		CatPersonAvatarComponent,
		TurmaCardDetailComponent,
		TurmaAulaExperimentalModalComponent,
		AgendamentoEditModalComponent,
		AgendamentoDeleteModalComponent,
		AgendamentoCreateModalComponent,
		DisponibilidadeDeleteModalComponent,
		DisponibilidadeEditModalComponent,
		AgendamentoCardComponent,
		DisponibilidadeCardComponent,
		TurmaMarcacaoModalComponent,
		TurmaReposicaoModalComponent,
		DisponibilidadeModalCardComponent,
		TurmaCardComponent,
		TurmaDesmarcarModalComponent,
		TurmaSubstituirProfessorModalComponent,
		TurmaRemoverAulaCheiaModalComponent,
		TurmaJustificativaFaltaAulaCheiaModalComponent,
		AgendamentoHoverCardComponent,
		ModalCancelarAgendamentoLocacaoComponent,
		ModalReagendarLocacaoComponent,
		ModalFinalizarVendaLocacaoComponent,
		ModalJustificativaCancelamentoAgendamentoLocacaoComponent,
		AgendamentoModalCardComponent,
		ModalServicosDisponiveisComponent,
		ModalAgendamentoComponent,
		FixarAlunoComponent,
		EsperaDesmarcarModalComponent,
		ModalAgendaEditarAulaComponent,
		ModalAgendaEditarAulaSuccessComponent,
		ModalAgendaReplicarAlteracoesAulaComponent,
		AddFilaEsperaTurmaModalComponent,
		CreateRecepFilaEsperaTurmaModalComponent,
		ReservaEquipamentoModalComponent,
		ModalListaDataIndisponibilidadeComponent,
	],
	providers: [
		AgendaServicosStateService,
		DisponibilidadeCardComponent,
		TurmaRemoverAulaCheiaModalComponent,
		TurmaJustificativaFaltaAulaCheiaModalComponent,
		AgendaBiStateService,
	],
})
export class AgendaModule {}
