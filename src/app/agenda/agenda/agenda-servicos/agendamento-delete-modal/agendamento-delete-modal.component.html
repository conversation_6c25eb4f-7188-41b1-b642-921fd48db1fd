<div class="tipo-agendamento">
	<div class="label">Tipo de evento</div>
	<div class="value">{{ agendamento?.tipoAgendamento?.nome }}</div>
</div>

<div class="row block-a">
	<div class="col-md-6">
		<pacto-cat-datepicker
			[formControl]="agendamentoFg.get('dia')"
			[label]="'Data'"></pacto-cat-datepicker>
	</div>
	<div class="col-md-6">
		<div class="input-nome">Duração (minutos)</div>
		<input
			[formControl]="agendamentoFg.get('duracao')"
			id="duracao-excluir-agendamento"
			type="text" />
	</div>
</div>

<div class="row block-a">
	<div class="col-md-6">
		<div class="input-nome">Horário inicial</div>
		<input [formControl]="agendamentoFg.get('horarioInicial')" type="text" />
	</div>
	<div class="col-md-6">
		<div class="input-nome"><PERSON><PERSON><PERSON><PERSON> final</div>
		<input [formControl]="agendamentoFg.get('horarioFinal')" type="text" />
	</div>
</div>

<div class="content-block">
	<div class="label">Aluno(a)</div>
	<div class="content-wrapper">
		<div [ngStyle]="{ backgroundImage: alunoUrl }" class="image-wrapper">
			<i *ngIf="!alunoUrl" class="pct pct-user"></i>
		</div>
		<div class="value-content">{{ agendamento?.aluno?.nome }}</div>
	</div>
</div>

<div class="content-block">
	<div class="label">Professor(a)</div>
	<div class="content-wrapper">
		<div [ngStyle]="{ backgroundImage: professorUrl }" class="image-wrapper">
			<i *ngIf="!professorUrl" class="pct pct-user"></i>
		</div>
		<div class="value-content">{{ agendamento?.professor?.nome }}</div>
	</div>
</div>

<div class="content-block">
	<div class="label">Status do agendamento</div>
	<div class="content-wrapper">
		<div class="value-content">
			<ng-container [ngSwitch]="agendamento.status" class="status">
				<span *ngSwitchCase="AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO">
					Aguardando Confirmação
				</span>
				<span *ngSwitchCase="AgendaAgendamentoStatus.CANCELADO">Cancelado</span>
				<span *ngSwitchCase="AgendaAgendamentoStatus.CONFIRMADO">
					Confirmado
				</span>
				<span *ngSwitchCase="AgendaAgendamentoStatus.EXECUTADO">Executado</span>
				<span *ngSwitchCase="AgendaAgendamentoStatus.FALTOU">Faltou</span>
				<span *ngSwitchCase="AgendaAgendamentoStatus.REAGENDADO">
					Reagendado
				</span>
			</ng-container>
		</div>
	</div>
</div>

<div
	*ngIf="!showObservacao"
	class="observacao-label"
	id="adicionar-observacao-excluir-agendamento">
	<span (click)="adicionarObsHandler()" id="adicionar-observacao-delete">
		<i class="pct pct-plus"></i>
		Adicionar observação
	</span>
</div>

<pacto-cat-form-textarea
	*ngIf="showObservacao"
	[control]="agendamentoFg.get('observacao')"
	[id]="'input-observacao'"
	[label]="'Observação'"></pacto-cat-form-textarea>

<div class="actions">
	<pacto-cat-button
		(click)="cancelarHandler()"
		[full]="true"
		[label]="'Cancelar'"
		[type]="'OUTLINE'"
		id="btn-cancelar-exclusao-agendamento"></pacto-cat-button>
	<pacto-cat-button
		(click)="salvarHandler()"
		[full]="true"
		[label]="'Confirmar exclusão'"
		[type]="'ALERT'"
		id="btn-confirmar-exclusao-agendamento"></pacto-cat-button>
</div>
