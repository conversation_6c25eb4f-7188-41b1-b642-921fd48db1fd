import {
	Component,
	OnInit,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnDestroy,
} from "@angular/core";

import {
	AgendaServicoAgendamentoFilter,
	AgendaServicosStateService,
} from "../agenda-servicos-state.service";
import { AgendaDisponibilidade, TreinoApiAgendamentoService } from "treino-api";
import { AgendaPeriodFilter } from "../../agenda.model";
import { Subscription } from "rxjs";
import { AgendaEvento } from "../../agenda-layout-v2/agenda-layout-v2.component";
import { FormControl } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { FilterOption } from "../../agenda-layout-v2-filter/agenda-layout-v2-filter.component";
import { DisponibilidadeCardComponent } from "../disponibilidade-card/disponibilidade-card.component";
import { DisponibilidadeModalCardComponent } from "../disponibilidade-modal-card/disponibilidade-modal-card.component";
import { ModalService } from "@base-core/modal/modal.service";
import { DisponibilidadeDeleteModalComponent } from "../disponibilidade-delete-modal/disponibilidade-delete-modal.component";
import { SnotifyService } from "ng-snotify";
import { DisponibilidadeEditModalComponent } from "../disponibilidade-edit-modal/disponibilidade-edit-modal.component";
import { AgendamentoCreateModalComponent } from "../agendamento-create-modal/agendamento-create-modal.component";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

@Component({
	selector: "pacto-servico-agendamentos",
	templateUrl: "./disponibilidades.component.html",
	styleUrls: ["./disponibilidades.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ServicoDisponibilidadesComponent implements OnInit, OnDestroy {
	constructor(
		private cd: ChangeDetectorRef,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private agendamentoService: TreinoApiAgendamentoService,
		private restService: RestService,
		private state: AgendaServicosStateService,
		private session: SessionService
	) {}

	private subscriptions: Subscription[] = [];
	private fetchDataSubscription: Subscription;
	private fetchDisponibilidadeConfigSub: Subscription;

	loading = false;
	eventos: AgendaEvento[][] = [];
	professoresFc = new FormControl();
	tiposEventos: FilterOption[] = [];
	resposeParser = (data) => data.content;
	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	get periodFilter(): AgendaPeriodFilter {
		return this.state.periodFilter;
	}

	get tiposFilter(): number[] {
		return this.state.tipoFilter;
	}

	get disponibilidadeFiltro(): "DISPONIVEIS" | "TODOS" {
		return this.state.currentFiltro.disponibilidadeFiltro;
	}

	get professoresUrl() {
		return this.restService.buildFullUrl("colaboradores");
	}

	tiposChangedHandler(tipos: any[]) {
		this.state.updateFiltroTiposAgendamento(tipos as number[]);
	}

	ngOnInit() {
		this.session.notificarRecursoEmpresa(
			RecursoSistema.AGENDAMENTO_DE_SERVICO_PELA_TELA_DISPONIBILIDADE
		);
		this.fetchTiposAgendamentos();

		const professorFcSub = this.professoresFc.valueChanges.subscribe(
			(professores) => {
				this.state.updateFiltroProfessores(professores);
			}
		);

		const professorSub = this.state.professoresFilterUpdate$.subscribe(
			(professores) => {
				this.professoresFc.setValue(professores, { emitEvent: false });
			}
		);

		const updateSub = this.state.update$.subscribe((agedamentoFiltro) => {
			this.fetchData(agedamentoFiltro);
		});

		const deleteSub = this.state.deleteDisponibilidade$.subscribe(
			(disponibilidade) => {
				this.deleteDisponibilidadeHandler(disponibilidade);
			}
		);

		const editSub = this.state.editDisponibilidade$.subscribe(
			(disponibilidade) => {
				this.editDisponibilidadeHandler(disponibilidade);
			}
		);

		const addSub = this.state.adicionarEvento$.subscribe((disponibilidade) => {
			this.adicionarEventoHandler(disponibilidade);
		});

		this.subscriptions.push(
			professorFcSub,
			professorSub,
			deleteSub,
			editSub,
			addSub,
			updateSub
		);
	}

	disponibilidadeFiltroChangedHandler(filtro) {
		this.state.updateDisponibilidadeFiltro(filtro);
	}

	periodFilterUpdateHandler(periodFilter: AgendaPeriodFilter) {
		this.state.updatePeriodFilter(periodFilter);
	}

	ngOnDestroy() {
		if (this.fetchDataSubscription) {
			this.fetchDataSubscription.unsubscribe();
		}
		if (this.fetchDisponibilidadeConfigSub) {
			this.fetchDisponibilidadeConfigSub.unsubscribe();
		}
		this.subscriptions.forEach((subscription) => {
			if (subscription) {
				subscription.unsubscribe();
			}
		});
	}

	private deleteDisponibilidadeHandler(disponibilidade: AgendaDisponibilidade) {
		if (this.fetchDisponibilidadeConfigSub) {
			this.fetchDisponibilidadeConfigSub.unsubscribe();
		}
		const id = disponibilidade.id;
		this.fetchDisponibilidadeConfigSub = this.agendamentoService
			.obterDisponibilidadeConfig(id)
			.subscribe((seRepete) => {
				const modal = this.modalService.open(
					"Excluir Disponibilidade",
					DisponibilidadeDeleteModalComponent
				);
				modal.componentInstance.disponibilidade = disponibilidade;
				modal.componentInstance.seRepete = seRepete;
				modal.result.then(
					(repete) => {
						this.agendamentoService
							.excluirDisponibilidade(disponibilidade.id, repete)
							.subscribe((sucess) => {
								if (sucess) {
									this.snotifyService.success("Removido com sucesso");
								} else {
									this.snotifyService.error("Erro ao remover disponibilidade");
								}
								this.fetchData(this.state.currentFiltro);
							});
					},
					() => {}
				);
			});
	}

	private editDisponibilidadeHandler(disponibilidade: AgendaDisponibilidade) {
		if (this.fetchDisponibilidadeConfigSub) {
			this.fetchDisponibilidadeConfigSub.unsubscribe();
		}

		this.fetchDisponibilidadeConfigSub = this.agendamentoService
			.obterDisponibilidadeConfig(disponibilidade.id)
			.subscribe((seRepete) => {
				const modal = this.modalService.open(
					"Editar Disponibilidade",
					DisponibilidadeDeleteModalComponent
				);
				this.session.notificarRecursoEmpresa(
					RecursoSistema.EDICAO_DE_AGENDAMENTO_DE_SERVICO
				);
				modal.componentInstance.editar = true;
				modal.componentInstance.disponibilidade = disponibilidade;
				modal.componentInstance.seRepete = seRepete;
				modal.result.then(
					(repete) => {
						this.agendamentoService
							.editDisponibilidade(disponibilidade.id, repete)
							.subscribe((sucess) => {
								if (sucess) {
									this.snotifyService.success("Editado com sucesso");
								} else {
									this.snotifyService.error("Erro ao editar disponibilidade");
								}
								this.fetchData(this.state.currentFiltro);
							});
					},
					() => {}
				);
			});
	}

	private adicionarEventoHandler(disponibilidade: AgendaDisponibilidade) {
		const modal = this.modalService.open(
			"Adicionar Agendamento",
			AgendamentoCreateModalComponent
		);
		modal.componentInstance.disponibilidade = disponibilidade;
		modal.componentInstance.origemDisponibilidade = true;
		modal.result.then(
			(dto) => {
				this.fetchData(this.state.currentFiltro);
			},
			() => {}
		);
	}

	addDisponibilidadeHandler() {
		const perfil = this.session.recursos.get(
			PerfilAcessoRecursoNome.AGENDA_DISPONIBILIDADE
		);
		if (perfil.incluir) {
			const modal = this.modalService.open(
				"Adicionar Disponibilidade",
				DisponibilidadeEditModalComponent
			);
			modal.result.then(
				(dto) => {
					this.fetchData(this.state.currentFiltro);
				},
				() => {}
			);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	private fetchTiposAgendamentos() {
		this.subscriptions.push(
			this.state.obterTiposAgendamento().subscribe((tipos) => {
				this.tiposEventos = tipos.map((tipo) => {
					return {
						id: tipo.id,
						label: tipo.nome,
						color: tipo.cor,
					};
				});
				const codsTipos = new Array<any>();
				this.tiposEventos.forEach((t) => {
					codsTipos.push(t.id);
				});
				this.state.updateFiltroTiposAgendamento(codsTipos);
				this.cd.detectChanges();
			})
		);
	}

	private fetchData(agendamentoFiltro: AgendaServicoAgendamentoFilter) {
		if (this.fetchDataSubscription) {
			this.fetchDataSubscription.unsubscribe();
		}
		this.loading = true;
		this.cd.detectChanges();
		this.fetchDataSubscription = this.agendamentoService
			.obterDisponibilidadePorDia(
				agendamentoFiltro.periodo.date,
				agendamentoFiltro.periodo.view,
				{
					professoresIds: agendamentoFiltro.professoresIds,
					disponibilidadeFiltro: agendamentoFiltro.disponibilidadeFiltro,
					tipoAgendamentoIds: agendamentoFiltro.tipoAgendamentoIds,
				}
			)
			.subscribe((eventosPorDia) => {
				this.loading = false;
				this.eventos = [];
				for (const dia in eventosPorDia) {
					const gridEvents: AgendaEvento[] = [];
					eventosPorDia[dia].forEach((evento) => {
						gridEvents.push({
							inicio: evento.horarioInicial,
							fim: evento.horarioFinal,
							renderComponent: DisponibilidadeCardComponent,
							modalComponent: DisponibilidadeModalCardComponent,
							payload: evento,
						});
					});
					this.eventos.push(gridEvents);
				}
				this.cd.detectChanges();
			});
	}

	get urlLog() {
		return this.restService.buildFullUrl(`log/disponibilidades`);
	}
}
