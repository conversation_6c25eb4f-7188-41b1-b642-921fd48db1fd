<div (click)="toggleHandler()" class="title-row">
	<div class="title-label" i18n="@@agenda-disponibilidade-filtro:mostra-apenas">
		Mostrar Apenas
	</div>
	<i *ngIf="open" class="pct pct-chevron-down"></i>
	<i *ngIf="!open" class="pct pct-chevron-up"></i>
</div>

<div *ngIf="open" class="body">
	<div
		(click)="setValue('DISPONIVEIS')"
		[ngClass]="{ selected: !todos }"
		class="radio-option">
		<div class="radio-btn">
			<div *ngIf="!todos" class="radio-checked"></div>
		</div>
		<div
			class="radio-value"
			i18n="@@agenda-disponibilidade-filtro:horarios-disponiveis">
			Horários disponíveis
		</div>
	</div>
	<div
		(click)="setValue('TODOS')"
		[ngClass]="{ selected: todos }"
		class="radio-option">
		<div class="radio-btn">
			<div *ngIf="todos" class="radio-checked"></div>
		</div>
		<div
			class="radio-value"
			i18n="@@agenda-disponibilidade-filtro:todos-os-horarios">
			Todos os horários
		</div>
	</div>
</div>
