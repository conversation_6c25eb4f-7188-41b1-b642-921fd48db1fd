@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	margin-top: 30px;
}

.title-row {
	display: flex;
	align-items: center;
	cursor: pointer;

	.title-label {
		font-size: 16px;
		line-height: 28px;
		font-weight: 600;
		color: $pretoPri;
		flex-grow: 1;
	}

	i.pct {
		color: $pretoPri;
		font-size: 22px;
	}
}

.radio-btn {
	width: 14px;
	height: 14px;
	border-radius: 7px;
	border: 1px solid #b4b7bb;
	color: #b4b7bb;
	display: flex;
	justify-content: center;
	margin-right: 10px;
	align-items: center;

	.radio-checked {
		width: 4px;
		height: 4px;
		border-radius: 2px;
		background-color: $branco;
	}
}

.radio-option {
	display: flex;
	align-items: center;
	margin: 10px 0px;
	cursor: pointer;
}

.radio-option.selected {
	.radio-btn {
		color: #51555a;
		border-color: #51555a;
		background-color: #51555a;
	}

	.radio-value {
		color: #51555a;
	}
}

.radio-value {
	color: $cinzaPri;
	font-size: 14px;
	font-weight: 400;
}
