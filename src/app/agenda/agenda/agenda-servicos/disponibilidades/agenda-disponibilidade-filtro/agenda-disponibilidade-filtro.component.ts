import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
} from "@angular/core";

export interface FilterOption {
	id: any;
	label: string;
	color?: string;
	/**
	 * Example: 'pct-eye'
	 */
	pactoIcon?: string;
}

@Component({
	selector: "pacto-agenda-disponibilidade-filtro",
	templateUrl: "./agenda-disponibilidade-filtro.component.html",
	styleUrls: ["./agenda-disponibilidade-filtro.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaDisponibilidadeFiltroComponent implements OnInit, OnChanges {
	@Output() optionsChanged: EventEmitter<"TODOS" | "DISPONIVEIS"> =
		new EventEmitter();
	@Input() option: "TODOS" | "DISPONIVEIS";

	get todos() {
		return this.option === "TODOS";
	}

	open = true;

	constructor() {}

	ngOnInit() {}

	ngOnChanges(changes) {}

	toggleHandler() {
		this.open = !this.open;
	}

	setValue(value) {
		this.optionsChanged.emit(value);
	}
}
