<pacto-agenda-layout-v2
	(periodFilterUpdate)="periodFilterUpdateHandler($event)"
	[eventos]="eventos"
	[loading]="loading"
	[periodFilter]="periodFilter"
	i18n-title="@@titulo-agenda-servicos"
	title="Agenda de serviços">
	<ng-container side-filters>
		<div class="filter-container">
			<div class="professores-label" i18n="@@agenda-servicos:professores">
				Professor<PERSON>
			</div>
			<pacto-cat-multi-select-filter
				[control]="professoresFc"
				[endpointUrl]="professoresUrl"
				[imageKey]="'uriImagem'"
				[labelKey]="'nome'"
				[paramBuilder]="paramBuilder"
				[resposeParser]="resposeParser"
				id="professores-disponibilidade-filter"></pacto-cat-multi-select-filter>

			<pacto-agenda-disponibilidade-filtro
				(optionsChanged)="disponibilidadeFiltroChangedHandler($event)"
				[option]="disponibilidadeFiltro"></pacto-agenda-disponibilidade-filtro>

			<!-- Tipos de eventos -->
			<pacto-agenda-layout-v2-filter
				(optionsChanged)="tiposChangedHandler($event)"
				[options]="tiposEventos"
				[selected]="tiposFilter"
				[titulo]="'Tipo de evento'"
				class="filtro-block"
				id="tipo-de-eventos-disponibilidade-filter"></pacto-agenda-layout-v2-filter>

			<pacto-log [url]="urlLog"></pacto-log>
		</div>
	</ng-container>

	<ng-container extra-buttons>
		<div
			(click)="addDisponibilidadeHandler()"
			class="adicionar control-btn"
			i18n="@@agenda-disponibilidade:adicionar-disponibilidade"
			id="adicionar-disponibilidade">
			Adicionar Disponibilidade
		</div>
		<div
			[routerLink]="['/agenda/painel/servicos/agendamentos']"
			class="agendamentos control-btn"
			i18n="@@agenda-disponibilidade:ver-agendamentos"
			id="ver-agendamentos">
			Ver Agendamentos
		</div>
	</ng-container>
</pacto-agenda-layout-v2>
