import {
	ChangeDetectionStrategy,
	Component,
	Input,
	On<PERSON><PERSON>roy,
	OnInit,
} from "@angular/core";
import {
	AgendaDisponibilidadeConfig,
	AgendaDisponibilidadeDiaSemana,
	TreinoApiAgendamentoService,
} from "treino-api";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";

declare var moment;

@Component({
	selector: "pacto-disponibilidade-edit-modal",
	templateUrl: "./disponibilidade-edit-modal.component.html",
	styleUrls: ["./disponibilidade-edit-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DisponibilidadeEditModalComponent implements OnInit, OnDestroy {
	@Input() disponibilidadeConfig: AgendaDisponibilidadeConfig;

	fg = new FormGroup({
		dataInicial: new FormControl(new Date().getTime(), [Validators.required]),
		dataFinal: new FormControl(""),
		professores: new FormControl("", [Validators.required]),
		tipos: new FormControl("", [Validators.required]),
		horarioInicial: new FormControl("", [
			Validators.required,
			Validators.minLength(5),
		]),
		horarioFinal: new FormControl("", [
			Validators.required,
			Validators.minLength(5),
		]),
	});
	todos = true;
	repeatFc = new FormControl("NAO");
	diasFc = new FormControl("SIM");
	days: AgendaDisponibilidadeDiaSemana[] = [];

	resposeParser = (data) => data.content;
	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	setTodos(value) {
		this.todos = value;
	}

	get valid() {
		return this.fg.valid;
	}

	get vaiRepetir() {
		return this.repeatFc.value === "SIM";
	}

	get vaiTodosDias() {
		return this.diasFc.value === "NAO";
	}

	get professoresUrl() {
		return this.restService.buildFullUrl("colaboradores");
	}

	get tiposUrl() {
		return this.restService.buildFullUrl("tipos-agendamento");
	}

	get timeMask() {
		return (value) => {
			const result = [/[0-2]/, null, ":", /[0-5]/, /[0-9]/];
			const firstD = parseInt(value[0], 10);

			/* digito 2 */
			if (!isNaN(firstD) && firstD === 2) {
				result[1] = /[0-3]/;
			} else {
				result[1] = /[0-9]/;
			}
			return result;
		};
	}

	get duracaoMask() {
		return [/[0-9]/, /[0-9]/];
	}

	ngOnInit() {
		if (this.disponibilidadeConfig) {
			this.fillOutForm();
		}
	}

	ngOnDestroy() {}

	private fillOutForm() {
		this.fg.patchValue(
			{
				dataInicial: this.disponibilidadeConfig.dataInicial,
				dataFinal: this.disponibilidadeConfig.dataFinal,
				professores: this.disponibilidadeConfig.professores,
				tipos: this.disponibilidadeConfig.tiposAtividades,
				horarioInicial: this.disponibilidadeConfig.dataInicial,
				horarioFinal: this.disponibilidadeConfig.horarioFinal,
			},
			{ emitEvent: false }
		);
		this.days = this.disponibilidadeConfig.diasSemana;
	}

	constructor(
		private openModal: NgbActiveModal,
		private agendamentoService: TreinoApiAgendamentoService,
		private snotifyService: SnotifyService,
		private restService: RestService,
		private sessionService: SessionService,
		private agendaStateService: AgendaCardsStateService
	) {}

	updateDaysHandler(days) {
		this.days = days;
	}

	cancelarHandler() {
		this.openModal.dismiss();
	}

	private getDto() {
		const dto = this.fg.getRawValue();
		dto.dataInicialFormatada = this._timestampToYYYYMMDD(dto.dataInicial);
		if (dto.dataFinal) {
			dto.dataFinalFormatada = this._timestampToYYYYMMDD(dto.dataFinal);
		} else {
			dto.dataFinalFormatada = "";
		}
		if (dto.professores && dto.professores.length) {
			dto.professores = dto.professores.map((p) => p.id);
		}
		if (dto.tipos && dto.tipos.length) {
			dto.tipos = dto.tipos.map((t) => t.id);
		}
		dto.diasSemana = this.days;
		if (this.disponibilidadeConfig && this.disponibilidadeConfig.seRepete) {
			dto.todos = this.todos;
		}
		return dto;
	}

	salvarHandler() {
		if (this.valid) {
			const dto = this.getDto();
			if (this.getDto().professores.length > 1) {
				this.sessionService.notificarRecursoEmpresa(
					RecursoSistema.CADASTRO_DE_DISPONIBILIDADE_PARA_MAIS_DE_UM_PROFESSOR
				);
			} else {
				this.sessionService.notificarRecursoEmpresa(
					RecursoSistema.CADASTRO_DE_DISPONIBILIDADE_PARA_UM_PROFESSOR
				);
			}
			if (this.getDto().tipos.length > 1) {
				this.sessionService.notificarRecursoEmpresa(
					RecursoSistema.CADASTRO_DE_DISPONIBILIDADE_PARA_MAIS_DE_UM_TIPO_DE_SERVICO
				);
			} else {
				this.sessionService.notificarRecursoEmpresa(
					RecursoSistema.CADASTRO_DE_DISPONIBILIDADE_PARA_UM_TIPO_DE_SERVICO
				);
			}
			this.agendamentoService.criarDisponibilidade(dto).subscribe(
				(ok) => {
					this.snotifyService.success("Disponibilidade criada com sucesso.");
					this.openModal.close(dto);
					this.agendaStateService.forceLoad$.next(true);
				},
				(httpError) => {
					this.snotifyService.error(httpError.error.meta.message);
				}
			);
		} else {
			this.fg.get("dataInicial").markAsTouched();
			this.fg.get("dataFinal").markAsTouched();
			this.fg.get("professores").markAsTouched();
			this.fg.get("tipos").markAsTouched();
			this.fg.get("horarioInicial").markAsTouched();
			this.fg.get("horarioFinal").markAsTouched();
		}
	}

	private _timestampToYYYYMMDD(timestamp: number): string {
		const date = new Date(timestamp);
		const year = date.getFullYear();
		const month = `${date.getMonth() + 1}`.padStart(2, "0"); // mês é zero-based
		const day = `${date.getDate()}`.padStart(2, "0");
		return `${year}${month}${day}`;
	}
}
