@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 0px 30px 45px;
}

.block-a {
	margin-top: 15px;
	margin-bottom: 15px;
}

.actions {
	display: flex;
	justify-content: space-between;
	margin-top: 35px;

	pacto-cat-button {
		width: 200px;
	}
}

.professores-label {
	font-weight: 400;
	font-size: 16px;
	margin-top: 22px;
	color: #a6aab1;
}

.tipos-label {
	font-weight: 400;
	font-size: 16px;
	margin-top: 22px;
	color: #a6aab1;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.input-nome {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}

.edit-options {
	background-color: #eff2f7;
	border-radius: 4px;
	padding: 15px 20px;
	margin-top: 30px;
}

.options-titulo {
	color: $azulimPri;
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 15px;
}

.radio-btn {
	width: 14px;
	height: 14px;
	border-radius: 7px;
	border: 1px solid #b4b7bb;
	color: #b4b7bb;
	display: flex;
	justify-content: center;
	margin-right: 10px;
	align-items: center;

	.radio-checked {
		width: 4px;
		height: 4px;
		border-radius: 2px;
		background-color: $branco;
	}
}

.radio-option {
	display: flex;
	align-items: center;
	margin: 10px 0px;
	cursor: pointer;
}

.radio-option.selected {
	.radio-btn {
		color: #51555a;
		border-color: #51555a;
		background-color: #51555a;
	}

	.radio-value {
		color: #51555a;
	}
}

.radio-value {
	color: $cinzaPri;
	font-size: 14px;
	font-weight: 400;
}
