<!-- PROFESSORES -->
<div class="professores-label">Professor<PERSON></div>
<pacto-cat-multi-select-filter
	[control]="fg.get('professores')"
	[endpointUrl]="professoresUrl"
	[id]="'professor-adicionar-disponibilidade'"
	[imageKey]="'uriImagem'"
	[labelKey]="'nome'"
	[paramBuilder]="paramBuilder"
	[resposeParser]="resposeParser"
	id="professores-adicionar-disponibilidade"></pacto-cat-multi-select-filter>

<div class="tipos-label" i18n="@@disponibilidade-edit-modal:tipo-de-atividade">
	Tipo de atividade
</div>
<pacto-cat-multi-select-filter
	[control]="fg.get('tipos')"
	[endpointUrl]="tiposUrl"
	[id]="'tipo-atividade-adicionar-disponibilidade'"
	[labelKey]="'nome'"
	[resposeParser]="resposeParser"
	id="tipos-atividade-adicionar-disponibilidade"></pacto-cat-multi-select-filter>

<div class="row block-a">
	<div class="col-md-6">
		<div class="input-nome" i18n="@@disponibilidade-edit-modal:horario-inicial">
			Horário inicial
		</div>
		<input
			[formControl]="fg.get('horarioInicial')"
			[ngClass]="{
				error:
					!fg.get('horarioInicial').valid && fg.get('horarioInicial').touched
			}"
			[textMask]="{ guide: true, mask: timeMask }"
			id="horario-inicio-adicionar-disponibilidade"
			type="text" />
	</div>
	<div class="col-md-6">
		<div class="input-nome" i18n="@@disponibilidade-edit-modal:horario-final">
			Horário final
		</div>
		<input
			[formControl]="fg.get('horarioFinal')"
			[ngClass]="{
				error: !fg.get('horarioFinal').valid && fg.get('horarioFinal').touched
			}"
			[textMask]="{ guide: true, mask: timeMask }"
			id="horario-final-adicionar-disponibilidade"
			type="text" />
	</div>
</div>

<div class="row block-a">
	<div class="col-md-6">
		<pacto-cat-datepicker
			[formControl]="fg.get('dataInicial')"
			[idInput]="'input-data-adicionar-disponibilidade'"
			[id]="'data-adicionar-disponibilidade'"
			[label]="
				vaiRepetir === true ? 'Data inicial' : 'Data'
			"></pacto-cat-datepicker>
	</div>
	<div class="col-md-6">
		<pacto-cat-select
			[control]="repeatFc"
			[id]="'repetir-disponibilidade'"
			[items]="[
				{ id: 'SIM', label: 'Sim', i18n: '@@disponibilidade-edit-modal:sim' },
				{ id: 'NAO', label: 'Não', i18n: '@@disponibilidade-edit-modal:nao' }
			]"
			class="filter view"
			i18n-label="@@disponibilidade-edit-modal:repetir-disponibilidade"
			label="'Repetir disponibilidade?'"></pacto-cat-select>
	</div>
</div>

<div *ngIf="vaiRepetir === true" class="row block-a">
	<div class="col-md-6">
		<pacto-cat-datepicker
			[formControl]="fg.get('dataFinal')"
			[idInput]="'input-data-repetir-adicionar-disponibilidade'"
			[id]="'data-repetir-adicionar-disponibilidade'"
			[label]="'Repetir até'"></pacto-cat-datepicker>
	</div>
	<div class="col-md-6">
		<pacto-cat-select
			[control]="diasFc"
			[id]="'repetir-todos-os-dias'"
			[items]="[
				{ id: 'SIM', label: 'Sim', i18n: '@@disponibilidade-edit-modal:sim' },
				{ id: 'NAO', label: 'Não', i18n: '@@disponibilidade-edit-modal:nao' }
			]"
			[label]="'Todos os dias?'"
			class="filter view"></pacto-cat-select>
	</div>
</div>

<pacto-disponibilidade-weekdays-config
	(update)="updateDaysHandler($event)"
	*ngIf="vaiTodosDias === true"
	[days]="days"
	id="dias-da-semana-adicionar-disponibilidade"></pacto-disponibilidade-weekdays-config>

<div *ngIf="disponibilidadeConfig?.seRepete" class="edit-options">
	<div class="options-titulo">
		Esta disponibilidade se repete em outras datas
	</div>
	<div
		(click)="setTodos(false)"
		[ngClass]="{ selected: !todos }"
		class="radio-option">
		<div class="radio-btn">
			<div *ngIf="!todos" class="radio-checked"></div>
		</div>
		<div class="radio-value">Atualizar apenas esta disponibilidade</div>
	</div>
	<div
		(click)="setTodos(true)"
		[ngClass]="{ selected: todos }"
		class="radio-option">
		<div class="radio-btn">
			<div *ngIf="todos" class="radio-checked"></div>
		</div>
		<div class="radio-value">Atualizar esta e as próximas</div>
	</div>
</div>

<div class="actions">
	<pacto-cat-button
		(click)="cancelarHandler()"
		[full]="true"
		[label]="'Cancelar'"
		[type]="'OUTLINE'"
		id="cancelar-adicionar-disponibilidade"></pacto-cat-button>
	<pacto-cat-button
		(click)="salvarHandler()"
		[disabled]="!valid"
		[full]="true"
		[label]="'Salvar'"
		id="salvar-adicionar-disponibilidade"></pacto-cat-button>
</div>
