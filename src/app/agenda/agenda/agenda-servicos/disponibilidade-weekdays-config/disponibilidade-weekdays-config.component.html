<div class="repetir-label"><PERSON><PERSON>r apenas nestes dias da semana</div>
<div class="blocks">
	<div
		(click)="toggle('SEGUNDA')"
		[ngClass]="{ on: isActive('SEGUNDA') }"
		class="day"
		id="segunda">
		S
	</div>
	<div
		(click)="toggle('TERCA')"
		[ngClass]="{ on: isActive('TERCA') }"
		class="day"
		id="terca">
		T
	</div>
	<div
		(click)="toggle('QUARTA')"
		[ngClass]="{ on: isActive('QUARTA') }"
		class="day"
		id="quarta">
		Q
	</div>
	<div
		(click)="toggle('QUINTA')"
		[ngClass]="{ on: isActive('QUINTA') }"
		class="day"
		id="quinta">
		Q
	</div>
	<div
		(click)="toggle('SEXTA')"
		[ngClass]="{ on: isActive('SEXTA') }"
		class="day"
		id="sexta">
		S
	</div>
	<div
		(click)="toggle('SABADO')"
		[ngClass]="{ on: isActive('SABADO') }"
		class="day"
		id="sabado">
		S
	</div>
	<div
		(click)="toggle('DOMINGO')"
		[ngClass]="{ on: isActive('DOMINGO') }"
		class="day"
		id="domingo">
		D
	</div>
</div>
