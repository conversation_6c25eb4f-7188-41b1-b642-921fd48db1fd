import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { AgendaDisponibilidadeDiaSemana } from "treino-api";

@Component({
	selector: "pacto-disponibilidade-weekdays-config",
	templateUrl: "./disponibilidade-weekdays-config.component.html",
	styleUrls: ["./disponibilidade-weekdays-config.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DisponibilidadeWeekdaysConfigComponent implements OnInit {
	@Input() days: AgendaDisponibilidadeDiaSemana[] = [];
	@Output() update: EventEmitter<AgendaDisponibilidadeDiaSemana[]> =
		new EventEmitter();

	constructor() {}

	ngOnInit() {}

	isActive(day) {
		return this.days.includes(day);
	}

	toggle(day) {
		const current = Object.assign([], this.days);
		if (current.includes(day)) {
			const indexToRemove = current.findIndex((item) => item === day);
			current.splice(indexToRemove, 1);
		} else {
			current.push(day);
		}
		this.update.emit(current);
	}
}
