import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
} from "@angular/core";
import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { AgendaAgendamento, AgendaAgendamentoStatus } from "treino-api";

@Component({
	selector: "pacto-agendamento-hover-card",
	templateUrl: "./agendamento-hover-card.component.html",
	styleUrls: ["./agendamento-hover-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendamentoHoverCardComponent implements OnInit {
	@Input() slot: DiscreteGridSlot<AgendaAgendamento>;

	constructor() {}

	ngOnInit() {}

	get agendamento(): AgendaAgendamento {
		return this.slot.event.payload;
	}

	get tipoAgendamento(): string {
		return this.agendamento.tipoAgendamento.nome;
	}

	get horario(): string {
		return `${this.agendamento.horarioInicial} - ${this.agendamento.horarioFinal}`;
	}

	get professor(): string {
		return this.agendamento.professor.nome;
	}

	get aluno(): string {
		return this.agendamento.aluno.nome;
	}

	get cor(): string {
		return this.agendamento.tipoAgendamento.cor;
	}

	get status(): AgendaAgendamentoStatus {
		return this.agendamento.status;
	}

	get AgendaAgendamentoStatus() {
		return AgendaAgendamentoStatus;
	}
}
