<div [ngStyle]="{ color: cor }" class="tipo-agendamento">
	{{ tipoAgendamento }}
</div>
<div class="horario">{{ horario }}</div>
<div class="professor">{{ professor }}</div>
<div class="aluno">> {{ aluno }}</div>
<div [ngSwitch]="status" class="status">
	<span
		*ngSwitchCase="AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO"
		i18n="@@agenda-servicos:agendamento-hover:aguardando-confirmacao">
		Aguardando Confirmação
	</span>
	<span *ngSwitchCase="AgendaAgendamentoStatus.CANCELADO">Cancelado</span>
	<span *ngSwitchCase="AgendaAgendamentoStatus.CONFIRMADO">Confirmado</span>
	<span *ngSwitchCase="AgendaAgendamentoStatus.EXECUTADO">Executado</span>
	<span *ngSwitchCase="AgendaAgendamentoStatus.FALTOU">Faltou</span>
	<span *ngSwitchCase="AgendaAgendamentoStatus.REAGENDADO">Reagendado</span>
</div>
