import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnD<PERSON>roy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import {
	AgendaDisponibilidade,
	AgendamentoTipoDuracao,
	ValidacaoAgendamentoErro,
	TreinoApiAgendamentoService,
	ProfessorSimples,
} from "treino-api";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { merge, Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { AgendaServicosStateService } from "../agenda-servicos-state.service";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";

declare var moment;

@Component({
	selector: "pacto-agendamento-create-modal",
	templateUrl: "./agendamento-create-modal.component.html",
	styleUrls: ["./agendamento-create-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendamentoCreateModalComponent implements OnInit, OnDestroy {
	@Input() disponibilidade: AgendaDisponibilidade;
	@ViewChild("traducoes", { static: true }) traducoes;

	error: ValidacaoAgendamentoErro;
	step = 0;
	dia;
	hora;
	professoresDisponiveis: ProfessorSimples[];
	origemDisponibilidade = false;

	private horarioSub: Subscription;
	private validateTriggerSub: Subscription;
	private validateSub: Subscription;
	private tipoSub: Subscription;
	private profSub: Subscription;

	horarioDisponivelFc = new FormControl();
	agendamentoFg = new FormGroup({
		duracao: new FormControl("", [
			Validators.required,
			(fc) => {
				if (this.erroDuracao(fc.value)) {
					return { duracao: true };
				} else {
					return null;
				}
			},
		]),
		horarioInicial: new FormControl("", [
			Validators.required,
			Validators.pattern(/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/),
		]),
		horarioFinal: new FormControl(),
		status: new FormControl("AGUARDANDO_CONFIRMACAO", [Validators.required]),
		aluno: new FormControl("", [Validators.required]),
		observacao: new FormControl(),
	});

	showObservacao = false;
	tipoFc = new FormControl();
	profFc = new FormControl();
	tipoAgendamentoDuracao;
	resposeParser = (result) => result.content;

	alunoParamBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	erroDuracao(value) {
		if (!this.tipoAgendamentoDuracao) {
			return false;
		}
		if (
			this.tipoAgendamentoDuracao.tipo ===
			AgendamentoTipoDuracao.INTERVALO_DE_TEMPO
		) {
			return (
				value < this.tipoAgendamentoDuracao.min ||
				value > this.tipoAgendamentoDuracao.max
			);
		} else {
			return false;
		}
	}

	get tipoUrl() {
		return this.rest.buildFullUrl("tipos-agendamento");
	}

	get alunoUrl() {
		return this.rest.buildFullUrl("alunos");
	}

	get professorImage() {
		const uri = this.disponibilidade.professor.imageUri;
		return uri ? `url(${uri})` : null;
	}

	get day() {
		return moment(this.disponibilidade.dia, "YYYYMMDD");
	}

	get professor() {
		if (this.disponibilidade && this.disponibilidade.professor) {
			return this.disponibilidade.professor.nome;
		}
		return "";
	}

	msgError(error: string) {
		if (error) {
			if (error === "SEM_DISPONIBILIDADE_DATA") {
				return (
					"· O professor " +
					this.professor +
					" não possui disponibilidade nesta data"
				);
			} else if (error === "SEM_DISPONIBILIDADE_HORARIO") {
				return (
					"· O professor " +
					this.professor +
					" não possui disponibilidade neste horário"
				);
			} else if (error === "VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO") {
				return "· O cliente não possui o plano de validação";
			} else if (error === "VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO") {
				return "· O cliente não possui o produto de validação";
			} else if (error === "VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO") {
				return "· O cliente não possui o produto de validação";
			} else if (error === "validacaoalunoagendamento") {
				return this.traducoes.getLabel("validacaoalunoagendamento");
			} else if (error === "validacaoalunoagendamento.carteira") {
				return this.traducoes.getLabel("validacaoalunoagendamento.carteira");
			} else if (error === "validacaoalunoagendamentofaltaemintervalodias") {
				return this.traducoes.getLabel(
					"validacaoalunoagendamentofaltaemintervalodias"
				);
			}
		}
		return "";
	}

	get selectedTipoNome() {
		if (this.tipoFc.value) {
			return this.tipoFc.value.nome;
		} else {
			return "";
		}
	}

	get valid() {
		return this.agendamentoFg.valid;
	}

	get timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	get duracaoMask() {
		return [/[0-9]/, /[0-9]/, /[0-9]/];
	}

	ngOnInit() {
		if (this.disponibilidade) {
			this.fillOutForm();
			this.setupEvents();
		}
		if (this.hora) {
			const horaInicio = (this.hora > 9 ? "" : "0") + this.hora + ":00";
			this.agendamentoFg.get("horarioInicial").setValue(horaInicio);
			this.updateHorarioFinal();
		}
	}

	ngOnDestroy() {
		this.horarioSub.unsubscribe();
		this.validateTriggerSub.unsubscribe();
		this.tipoSub.unsubscribe();
		if (this.validateSub) {
			this.validateSub.unsubscribe();
		}
	}

	private fillOutForm() {
		this.horarioDisponivelFc.setValue(
			`${this.disponibilidade.horarioInicial} - ${this.disponibilidade.horarioFinal}`,
			{ emitEvent: false }
		);
		if (
			this.disponibilidade.horarioInicial !== undefined &&
			!this.origemDisponibilidade
		) {
			const horaInicial = this.disponibilidade.horarioInicial.split(":");
			const horaInicialCompleta = Number(horaInicial[0] + "" + horaInicial[1]);
			const horaClicada = Number(this.hora + "00");
			if (horaInicialCompleta > horaClicada) {
				this.agendamentoFg
					.get("horarioInicial")
					.setValue(this.disponibilidade.horarioInicial);
				this.updateHorarioFinal();
			}
		} else if (this.origemDisponibilidade) {
			this.agendamentoFg
				.get("horarioInicial")
				.setValue(this.disponibilidade.horarioInicial);
			this.updateHorarioFinal();
		}
		this.horarioDisponivelFc.disable({ emitEvent: false });
		this.agendamentoFg.get("horarioFinal").disable({ emitEvent: false });
	}

	private setupTipo(tipoAtividade) {
		this.agendamentoService
			.tipoAgendamentoDuracao(tipoAtividade.id, tipoAtividade.tipoEvento, {})
			.subscribe((tipoDuracao) => {
				if (tipoDuracao) {
					this.tipoAgendamentoDuracao = tipoDuracao;
					if (tipoDuracao.tipo === AgendamentoTipoDuracao.DURACAO_PREDEFINIDA) {
						this.agendamentoFg.get("duracao").disable({ emitEvent: false });
						this.agendamentoFg
							.get("duracao")
							.setValue(tipoDuracao.fixo, { emitEvent: false });
						if (this.hora) {
							this.updateHorarioFinal();
						}
					}
					if (this.disponibilidade.professor) {
						this.step = 1;
					} else {
						this.state
							.obterProfessoresDisponiveis(
								this.dia,
								this.hora,
								tipoAtividade.id
							)
							.subscribe((tipos) => {
								this.professoresDisponiveis = tipos;
							});
					}
				}
				this.updateHorarioFinal();
				this.cd.detectChanges();
			});
	}

	private setupEvents() {
		if (
			this.disponibilidade &&
			this.disponibilidade.tiposAtividades &&
			this.disponibilidade.tiposAtividades.length === 1
		) {
			this.tipoFc.setValue(this.disponibilidade.tiposAtividades[0]);
			this.setupTipo(this.disponibilidade.tiposAtividades[0]);
		}
		this.tipoSub = this.tipoFc.valueChanges.subscribe((tipo) => {
			if (tipo) {
				this.setupTipo(tipo);
			}
		});

		this.profSub = this.profFc.valueChanges.subscribe((prof) => {
			this.disponibilidade.professor = prof;
			this.setupTipo(this.tipoFc.value);
			this.state
				.detalhesDisponibilidade(
					this._timestampToYYYYMMDD(this.dia),
					this.hora,
					this.tipoFc.value.id,
					prof.id
				)
				.subscribe((detalhes) => {
					this.disponibilidade.dia = detalhes.dia;
					this.disponibilidade.horarioInicial = detalhes.horarioInicial;
					this.disponibilidade.horarioFinal = detalhes.horarioFinal;
					this.disponibilidade.horarioDisponibilidadeCod =
						detalhes.horarioDisponibilidadeCod;
					this.fillOutForm();
					this.cd.detectChanges();
				});
		});

		this.horarioSub = merge(
			this.agendamentoFg.get("horarioInicial").valueChanges,
			this.agendamentoFg.get("duracao").valueChanges
		).subscribe(() => {
			this.updateHorarioFinal();
		});

		this.validateTriggerSub = merge(
			this.agendamentoFg.get("horarioInicial").valueChanges,
			this.agendamentoFg.get("duracao").valueChanges
		)
			.pipe(debounceTime(350))
			.subscribe(() => {
				const fg = this.agendamentoFg;
				if (fg.get("horarioInicial").valid) {
					this.validate();
				}
			});
	}

	private _timestampToYYYYMMDD(timestamp: number): string {
		const date = new Date(timestamp);
		const year = date.getFullYear();
		const month = `${date.getMonth() + 1}`.padStart(2, "0"); // mês é zero-based
		const day = `${date.getDate()}`.padStart(2, "0");
		return `${year}${month}${day}`;
	}

	private validate() {
		const dto = this.getDto();
		if (this.validateSub) {
			this.validateSub.unsubscribe();
		}
		this.cd.detectChanges();
		this.validateSub = this.agendamentoService
			.validarCriarAgendamento(dto)
			.subscribe((result) => {
				this.error = result ? result.error : null;
				this.cd.detectChanges();
			});
	}

	private getDto() {
		const dto = this.agendamentoFg.getRawValue();

		if (this.isTreinoIndependente()) {
			dto.professor = this.disponibilidade.professor
				? this.disponibilidade.professor.id
				: null;
		} else {
			dto.professor = this.disponibilidade.professor
				? this.disponibilidade.professor.codigoColaborador
				: null;
		}

		dto.data = this.disponibilidade.dia;
		dto.tipo = this.tipoFc.value.id;
		dto.tipoEvento = this.tipoFc.value.tipoEvento;
		dto.alunoId = dto.aluno ? dto.aluno.id : null;
		dto.horarioDisponibilidadeCod =
			this.disponibilidade.horarioDisponibilidadeCod;
		return dto;
	}

	private updateHorarioFinal() {
		const inicioLabel = this.agendamentoFg.get("horarioInicial").value;
		const duracao = parseInt(this.agendamentoFg.get("duracao").value, 10);
		const inicio = this.agendaUtil.convertTimeLabelIntoMinutes(inicioLabel);
		if (!isNaN(inicio) && !isNaN(duracao)) {
			const final = inicio + duracao;
			const finalLabel = this.agendaUtil.convertMinutesIntoTimeLabel(final);
			this.agendamentoFg
				.get("horarioFinal")
				.setValue(finalLabel, { emitEvent: false });
		}
	}

	constructor(
		private openModal: NgbActiveModal,
		private agendamentoService: TreinoApiAgendamentoService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private agendaUtil: AgendaUtilsService,
		private sessionService: SessionService,
		private state: AgendaServicosStateService
	) {}

	adicionarObsHandler() {
		this.showObservacao = true;
	}

	cancelarHandler() {
		this.step = 0;
		this.error = null;
		this.tipoFc.reset(null, { emitEvent: false });
	}

	salvarHandler() {
		if (this.valid) {
			const dto = this.getDto();
			this.agendamentoService.criarAgendamento(dto).subscribe((retorno) => {
				if (retorno.status === true) {
					if (retorno.error === "obrigatorio.alunoSemSituacaoCompativel") {
						this.snotifyService.error(
							"Aluno(a) inativo/visitante  - Verifique a configuração " +
								'"Prescrição de treino para aluno".'
						);
					} else if (retorno.error && retorno.error.trim()) {
						this.snotifyService.error(retorno.error);
					} else {
						this.snotifyService.error(
							this.traducoes.getLabel("erro_salvar_agendamento")
						);
					}
					this.cd.detectChanges();
				} else {
					// Sucesso
					this.openModal.close(dto);
					this.snotifyService.success("Agendamento criado com sucesso");
				}
			});
		} else {
			this.agendamentoFg.get("duracao").markAsTouched();
			this.agendamentoFg.get("horarioInicial").markAsTouched();
			this.agendamentoFg.get("aluno").markAsTouched();
			this.agendamentoFg.get("status").markAsTouched();
		}
	}

	isTreinoIndependente() {
		return !this.sessionService.integracaoZW;
	}
}
