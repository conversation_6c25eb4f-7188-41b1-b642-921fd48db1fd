<div class="wrapper">
	<div *ngIf="disponibilidade?.professor" class="top-row">
		<div
			[ngStyle]="{
				'background-image': professorImage
			}"
			class="professor-circle">
			<i *ngIf="!professorImage" class="pct pct-user default-icon"></i>
		</div>
		<div class="professor-nome">{{ professor }}</div>
		<div class="data">
			<ng-container
				*ngIf="this.disponibilidade.dia === undefined; else dayValue">
				<div class="custom-spinner" role="status"></div>
			</ng-container>
			<ng-template #dayValue>{{ day.format("DD/MM/YYYY") }}</ng-template>
		</div>
	</div>

	<div *ngIf="step === 0" class="step-0">
		<pacto-cat-select-filter
			[control]="tipoFc"
			[id]="'tipo-evento-adicionar-agendamento'"
			[labelKey]="'nome'"
			[options]="disponibilidade?.tiposAtividades"
			[resposeParser]="resposeParser"
			id="tipos-evento-adicionar-agendamento"
			label="Tipo de evento"></pacto-cat-select-filter>
	</div>

	<div
		*ngIf="step === 0 && !disponibilidade?.professor && tipoAgendamentoDuracao"
		class="step-0 mgt">
		<pacto-cat-select-filter
			[control]="profFc"
			[id]="'professor-adicionar-agendamento'"
			[labelKey]="'nome'"
			[options]="professoresDisponiveis"
			[resposeParser]="resposeParser"
			id="professores-adicionar-agendamento"
			label="Professor(a)"></pacto-cat-select-filter>
	</div>

	<div *ngIf="step === 1" class="step-1">
		<div class="tipo-agendamento">
			<div class="label">Tipo de evento</div>
			<div class="value">{{ selectedTipoNome }}</div>
		</div>

		<div class="row block-a">
			<div class="col-md-6">
				<div class="input-nome">Horário disponível</div>
				<input [formControl]="horarioDisponivelFc" type="text" />
			</div>

			<div class="col-md-6">
				<div class="input-nome">Duração (minutos)</div>
				<input
					[formControl]="agendamentoFg.get('duracao')"
					[ngClass]="{
						error:
							!agendamentoFg.get('duracao').valid &&
							agendamentoFg.get('duracao').touched
					}"
					[textMask]="{ guide: false, mask: duracaoMask }"
					id="duracao-adicionar-agendamento"
					type="text" />
			</div>
		</div>

		<div class="row block-a">
			<div class="col-md-6">
				<div class="input-nome">Horário inicial</div>
				<input
					[formControl]="agendamentoFg.get('horarioInicial')"
					[ngClass]="{
						error:
							!agendamentoFg.get('horarioInicial').valid &&
							agendamentoFg.get('horarioInicial').touched
					}"
					[textMask]="{ guide: true, mask: timeMask }"
					id="hora-inicial-adicionar-agendamento"
					type="text" />
			</div>
			<div class="col-md-6">
				<div class="input-nome">Horário final</div>
				<input [formControl]="agendamentoFg.get('horarioFinal')" type="text" />
			</div>
		</div>

		<pacto-cat-select-filter
			[control]="agendamentoFg.get('aluno')"
			[endpointUrl]="alunoUrl"
			[id]="'aluno-adicionar-agendamento'"
			[imageKey]="'imageUri'"
			[labelKey]="'nome'"
			[label]="'Aluno'"
			[paramBuilder]="alunoParamBuilder"
			[resposeParser]="resposeParser"
			class="aluno-select"
			id="alunos-adicionar-agendamento"></pacto-cat-select-filter>

		<div class="row">
			<div class="col-md-12">
				<pacto-cat-select
					[control]="agendamentoFg.get('status')"
					[id]="'select-status'"
					[items]="[
						{ id: 'AGUARDANDO_CONFIRMACAO', label: 'Aguardando Confirmação' },
						{ id: 'CONFIRMADO', label: 'Confirmado' },
						{ id: 'EXECUTADO', label: 'Executado' },
						{ id: 'CANCELADO', label: 'Cancelado' },
						{ id: 'FALTOU', label: 'Faltou' }
					]"
					[label]="'Status do Agendamento'"
					class="status-select"></pacto-cat-select>
			</div>
		</div>

		<div *ngIf="!showObservacao" class="observacao-label">
			<span (click)="adicionarObsHandler()" id="adicionar-observacao-create">
				<i class="pct pct-plus"></i>
				Adicionar observação
			</span>
		</div>

		<pacto-cat-form-textarea
			*ngIf="showObservacao"
			[control]="agendamentoFg.get('observacao')"
			[id]="'input-observacao'"
			[label]="'Observação'"
			id="adicionar-observacao"></pacto-cat-form-textarea>

		<div *ngIf="msgError(error)" class="error-block">
			<div *ngIf="msgError(error)" class="upper">
				<i class="pct pct-alert-triangle"></i>
				Erro
			</div>
			<div *ngIf="msgError(error)" class="error-msg">
				<span>
					{{ msgError(error) }}
				</span>
			</div>
		</div>

		<div
			*ngIf="
				!msgError(error) &&
				erroDuracao(agendamentoFg.get('duracao').value) &&
				agendamentoFg.get('duracao').touched
			"
			class="error-block">
			<div class="upper">
				<i class="pct pct-alert-triangle"></i>
				Erro
			</div>
			<div class="error-msg">
				<span>
					· Esse tipo de atividade deve ter entre
					{{ tipoAgendamentoDuracao.min }} e
					{{ tipoAgendamentoDuracao.max }} minutos !
				</span>
			</div>
		</div>

		<div class="actions">
			<pacto-cat-button
				(click)="cancelarHandler()"
				[full]="true"
				[label]="'Voltar'"
				[type]="'OUTLINE'"
				id="voltar-adicionar-agendamento"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvarHandler()"
				[disabled]="error || !valid"
				[full]="true"
				[label]="'Salvar'"
				id="salvar-adicionar-agendamento"></pacto-cat-button>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@agendamento-modal-mensagens:create:validacaoalunoagendamento"
		xingling="validacaoalunoagendamento">
		O aluno já realizou o número limite de agendamentos dentro do período de
		dias informado no Tipo de Evento.
	</span>
	<span
		i18n="
			@@agendamento-modal-mensagens:create:validacaoalunoagendamentofaltaemintervalodias"
		xingling="validacaoalunoagendamentofaltaemintervalodias">
		O aluno possui uma falta dentro do intervalo de dias configurado no Tipo de
		Evento.
	</span>
	<span
		i18n="
			@@agendamento-modal-mensagens:create:validacaoalunoagendamento.carteira"
		xingling="validacaoalunoagendamento.carteira">
		O aluno não faz parte da carteira do professor.
	</span>
	<span
		i18n="@@agendamento:create:aguardando-confirmacao"
		xingling="AGUARDANDO_CONFIRMACAO">
		Aguardando Confirmação
	</span>
	<span i18n="@@agendamento:create:confirmado" xingling="CONFIRMADO">
		Confirmado
	</span>
	<span i18n="@@agendamento:create:executado" xingling="EXECUTADO">
		Executado
	</span>
	<span i18n="@@agendamento:create:faltou" xingling="FALTOU">Faltou</span>
	<span
		i18n="@@agendamento:valid:plano"
		xingling="VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO">
		O cliente não possui o plano de validação
	</span>
	<span
		i18n="@@agendamento:valid:produto"
		xingling="VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO">
		O cliente não possui o produto de validação
	</span>
	<span
		i18n="@@agendamento:valid:sem:disponibilidade:horario"
		xingling="SEM_DISPONIBILIDADE_HORARIO">
		O professor {{ professor }} não possui disponibilidade neste horário
	</span>
	<span
		i18n="@@agendamento:valid:intervalo:dias"
		xingling="VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS">
		Não foi possivel realizar o agendamento, intervalo de dias atingido
	</span>
	<span
		i18n="@@agendamento:valid:intervalo:dias:faltou"
		xingling="VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS_FALTOU">
		Não foi possivel realizar o agendamento, intervalo de dias por falta
		atingido
	</span>
	<span i18n="@@agendamento:erro:salvar" xingling="erro_salvar_agendamento">
		Erro ao salvar agendamento. Verifique os dados informados e tente novamente.
	</span>
</pacto-traducoes-xingling>
