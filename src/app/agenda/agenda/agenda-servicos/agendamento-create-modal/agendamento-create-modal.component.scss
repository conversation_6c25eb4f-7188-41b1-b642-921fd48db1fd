@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 30px;
}

.wrapper.validating {
	opacity: 0.2;
	position: relative;
	pointer-events: none;
}

.mgt {
	margin-top: 20px;
}

.tipo-agendamento {
	.label {
		color: $cinzaPri;
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 5px;
	}

	.value {
		font-size: 16px;
		color: $pretoPri;
		font-weight: 600;
	}
}

.block-a {
	margin-top: 15px;
	margin-bottom: 15px;
}

.aluno-select,
.status-select {
	margin: 10px 0px;
}

.content-block {
	margin: 20px 0px;

	.label {
		font-size: 14px;
		font-weight: 600;
		color: $cinzaPri;
		margin-bottom: 8px;
	}

	.content-wrapper {
		display: flex;
		align-items: center;

		.image-wrapper {
			display: flex;
			align-items: center;
			justify-content: center;

			i {
				color: $branco;
				font-size: 17px;
			}

			width: 30px;
			height: 30px;
			border-radius: 15px;
			background-size: contain;
			background-position: center;
			background-color: #4387b5;
		}
	}

	.value-content {
		margin-left: 6px;
		font-weight: 600;
		font-size: 16px;
		color: $pretoPri;
	}
}

.observacao-label {
	font-size: 12px;
	margin-top: 20px;
	cursor: pointer;
	margin-bottom: 20px;
	color: $pretoPri;
}

.actions {
	display: flex;
	justify-content: space-between;
	margin-top: 30px;

	pacto-cat-button {
		width: 200px;
	}
}

.error-block {
	width: 100%;
	background-color: #fcedee;
	padding: 14px;
	border-radius: 4px;
	margin-bottom: 20px;

	.upper {
		display: flex;
		align-items: center;
		color: #db2c3d;
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 7px;

		i {
			font-size: 20px;
			padding-right: 8px;
		}
	}

	.error-msg {
		font-size: 12px;
		font-weight: 400;

		.professor {
			font-weight: 700;
		}
	}
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;

		&:focus {
			box-shadow: 0 0 0 0.2rem rgba($hellboyPri, 0.5);
		}
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.input-nome {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}

.top-row {
	display: flex;
	align-items: center;
	margin-bottom: 27px;
}

.professor-nome {
	font-size: 24px;
	font-weight: 600;
	margin-left: 15px;
	color: $pretoPri;
	flex-grow: 1;
}

.data {
	background-color: #eff2f7;
	color: #b4b7bb;
	font-size: 20px;
	font-weight: 400;
	line-height: 40px;
	border-radius: 4px;
	border: 1px solid #d3d5d7;
	text-align: center;
	padding: 0px 14px;
}

.professor-circle {
	width: 44px;
	height: 44px;
	border-radius: 22px;
	background-color: #1b4166;
	color: $branco;
	display: flex;
	align-items: center;
	justify-content: center;
	background-position: center;
	background-size: contain;
}

.custom-spinner {
	width: 2rem;
	height: 2rem;
	border: 0.4rem solid rgba(0, 123, 255, 0.2); // cor de fundo da borda
	border-top-color: #007bff; // cor da parte giratória
	border-radius: 50%;
	animation: spin 0.6s linear infinite;
	display: flex;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}
