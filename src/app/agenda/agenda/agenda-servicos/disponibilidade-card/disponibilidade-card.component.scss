@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	background-color: $branco;
	height: 100%;
	cursor: pointer;
	border: 1px solid #83888f;
	border-top-left-radius: 10px;
	border-bottom-left-radius: 10px;
	color: #80858c;
	overflow: hidden;
	position: relative;
}

.professor-circle {
	width: 20px;
	height: 20px;
	border-radius: 10px;
	background-color: #1b4166;
	color: $branco;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	left: 0px;
	background-position: center;
	background-size: contain;
	top: 0px;
}

.card-content {
	margin-left: 24px;
}

.professor-nome {
	color: #80858c;
	font-size: 12px;
	font-weight: 700;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.horario {
	color: #80858c;
	font-size: 12px;
	line-height: 1.4em;
	font-weight: 400;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.atividades {
	color: #80858c;
	font-size: 12px;
	font-weight: 700;
	white-space: nowrap;
	text-overflow: ellipsis;
}
