import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { AgendaDisponibilidade } from "treino-api";

@Component({
	selector: "pacto-disponibilidade-card",
	templateUrl: "./disponibilidade-card.component.html",
	styleUrls: ["./disponibilidade-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DisponibilidadeCardComponent implements OnInit {
	@Input() slot: DiscreteGridSlot<AgendaDisponibilidade>;

	constructor() {}

	ngOnInit() {}

	get disponibilidade(): AgendaDisponibilidade {
		return this.slot.event.payload;
	}

	get professor() {
		return this.disponibilidade.professor.nome;
	}

	get professorImage() {
		const uri = this.disponibilidade.professor.imageUri;
		return uri ? `url(${uri})` : null;
	}

	get horarioInicio(): string {
		return `${this.disponibilidade.horarioInicial}`;
	}

	get horario(): string {
		return `${this.disponibilidade.horarioInicial} - ${this.disponibilidade.horarioFinal}`;
	}

	get nOfAtividades() {
		return this.disponibilidade.tiposAtividades.length;
	}
}
