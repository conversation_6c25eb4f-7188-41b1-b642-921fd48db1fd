@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	width: 230px;
	padding: 24px 20px;
	position: relative;
}

.close-icon {
	cursor: pointer;
	position: absolute;
	right: 10px;
	top: 14px;
	font-size: 14px;
	color: $pretoPri;
}

.month-day {
	font-size: 24px;
	font-weight: bold;
	color: $pretoPri;
	line-height: 1em;
}

.month-name {
	font-size: 18px;
	font-weight: 400;
	color: $pretoPri;
}

.year {
	font-size: 18px;
	font-weight: 400;
	margin-bottom: 10px;
}

.body-block {
	margin-bottom: 10px;
}

.body-label {
	font-size: 12px;
	font-weight: 400;
	color: #bdc0c3;
}

.icon-circle {
	background-color: $pretoPri;
	width: 18px;
	height: 18px;
	border-radius: 9px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: $branco;
	margin-right: 4px;

	i {
		padding-bottom: 1px;
		padding-left: 1px;
	}
}

.body-value {
	font-size: 12px;
	font-weight: 400;

	&.body-bold {
		font-weight: bold;
	}
}

// Status
.status .body-value {
	display: flex;
}

.modal-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 5px;
	align-items: center;
	margin-bottom: 15px;

	.modal-action {
		cursor: pointer;
		line-height: 32px;
		padding: 0px 28px;
		color: $branco;
		border-radius: 4px;
		font-size: 12px;
		font-weight: 700;
		background-color: #b4b7bb;

		&.blue {
			background-color: $azulimPri;
		}
	}
}
