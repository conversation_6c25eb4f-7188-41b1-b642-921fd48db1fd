import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
	EventEmitter,
	Output,
} from "@angular/core";
import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { AgendaAgendamento, AgendaAgendamentoStatus } from "treino-api";

import { Moment } from "moment";
import { AgendaServicosStateService } from "../agenda-servicos-state.service";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";

declare var moment;

@Component({
	selector: "pacto-agendamento-modal-card",
	templateUrl: "./agendamento-modal-card.component.html",
	styleUrls: ["./agendamento-modal-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendamentoModalCardComponent implements OnInit {
	@Input() slot: DiscreteGridSlot<AgendaAgendamento>;
	@Output() closeModal: EventEmitter<MouseEvent> = new EventEmitter();

	constructor(
		private agendaServiceState: AgendaServicosStateService,
		private session: SessionService,
		private rest: RestService,
		private snotify: SnotifyService
	) {}

	get icon(): string {
		const status = this.agendamento.status;
		if (status === AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO) {
			return "pct-clock";
		} else if (status === AgendaAgendamentoStatus.CONFIRMADO) {
			return "pct-thumbs-up";
		} else if (status === AgendaAgendamentoStatus.FALTOU) {
			return "pct-alert-circle";
		} else if (status === AgendaAgendamentoStatus.EXECUTADO) {
			return "pct-check-circle";
		} else {
			return null;
		}
	}

	get status(): AgendaAgendamentoStatus {
		return this.agendamento.status;
	}

	get agendamento(): AgendaAgendamento {
		return this.slot.event.payload;
	}

	get AgendaAgendamentoStatus() {
		return AgendaAgendamentoStatus;
	}

	get tipoNome() {
		return this.agendamento.tipoAgendamento.nome;
	}

	get professorNome() {
		return this.agendamento.professor.nome;
	}

	get alunoNome() {
		return this.agendamento.aluno.nome;
	}

	get alunoTelefone() {
		const aluno = this.agendamento.aluno;
		if (aluno && aluno.telefones && aluno.telefones.length) {
			return aluno.telefones[0].numero;
		} else {
			return "";
		}
	}

	get date(): Moment {
		return moment(this.agendamento.data, "YYYYMMDD");
	}

	ngOnInit() {}

	excluirHandler($event) {
		if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "AVALIACAO_FISICA"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.AVALIACAO_FISICA
			);
			if (permissao !== undefined && permissao.excluir) {
				this.excluirAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum ===
			"CONTATO_INTERPESSOAL"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
			);
			if (permissao !== undefined && permissao.excluir) {
				this.excluirAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "PRESCRICAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.PRESCRICAO_TREINO
			);
			if (permissao !== undefined && permissao.excluir) {
				this.excluirAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "RENOVAR_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.RENOVAR_TREINO
			);
			if (permissao !== undefined && permissao.excluir) {
				this.excluirAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "REVISAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.REVISAO_TREINO
			);
			if (permissao !== undefined && permissao.excluir) {
				this.excluirAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		}
	}

	excluirAgendamento($event) {
		this.agendaServiceState.deleteEvento$.emit(this.agendamento);
		this.closeModal.emit($event);
	}

	apresentarNotificacaoSemPermissao() {
		this.snotify.warning(
			"Seu usuário não possui permissão, procure seu administrador"
		);
	}

	closeHandler($event) {
		this.closeModal.emit($event);
	}

	editarHandler($event) {
		if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "AVALIACAO_FISICA"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.AVALIACAO_FISICA
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum ===
			"CONTATO_INTERPESSOAL"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "PRESCRICAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.PRESCRICAO_TREINO
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "RENOVAR_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.RENOVAR_TREINO
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		} else if (
			this.agendamento.tipoAgendamento.comportamentoEnum === "REVISAO_TREINO"
		) {
			const permissao = this.session.recursos.get(
				PerfilAcessoRecursoNome.REVISAO_TREINO
			);
			if (permissao !== undefined && permissao.editar) {
				this.editarAgendamento($event);
			} else {
				this.apresentarNotificacaoSemPermissao();
			}
		}
	}

	editarAgendamento($event) {
		this.agendaServiceState.editarEvento$.emit(this.agendamento);
		this.closeModal.emit($event);
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/agendamentos/${this.agendamento.id}`);
	}
}
