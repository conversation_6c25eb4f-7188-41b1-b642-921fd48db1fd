<i (click)="closeHandler($event)" class="pct pct-x close-icon"></i>

<div class="month-day">{{ date.format("D") }}</div>
<div class="month-name">de {{ date.format("MMMM") }}</div>
<div class="year">{{ date.format("YYYY") }}</div>

<div class="body-block">
	<div class="body-label">Evento</div>
	<div class="body-value">{{ tipoNome }}</div>
</div>

<div class="body-block">
	<div class="body-label">Professor(a)</div>
	<div class="body-value body-bold">{{ professorNome }}</div>
</div>

<div class="body-block">
	<div class="body-label">Aluno(a)</div>
	<div class="body-value body-bold">{{ alunoNome }}</div>
	<div class="body-value">{{ alunoTelefone }}</div>
</div>

<div class="body-block status">
	<div class="body-label">Status</div>
	<div class="body-value body-bold">
		<div class="icon-circle">
			<i *ngIf="icon" class="pct {{ icon }}"></i>
		</div>
		<ng-container [ngSwitch]="status">
			<span
				*ngSwitchCase="AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO"
				i18n="@@agenda-servicos:agendamento-modal:aguardando-confirmacao">
				Aguardando Confirmação
			</span>
			<span *ngSwitchCase="AgendaAgendamentoStatus.CANCELADO">Cancelado</span>
			<span *ngSwitchCase="AgendaAgendamentoStatus.CONFIRMADO">Confirmado</span>
			<span *ngSwitchCase="AgendaAgendamentoStatus.EXECUTADO">Executado</span>
			<span *ngSwitchCase="AgendaAgendamentoStatus.FALTOU">Faltou</span>
			<span *ngSwitchCase="AgendaAgendamentoStatus.REAGENDADO">Reagendado</span>
		</ng-container>
	</div>
</div>

<div class="modal-actions">
	<div
		(click)="excluirHandler($event)"
		class="modal-action"
		id="btn-excluir-agendamento">
		Excluir
	</div>
	<div
		(click)="editarHandler($event)"
		class="modal-action blue"
		id="btn-editar-agendamento">
		Editar
	</div>
</div>
<pacto-log [url]="urlLog"></pacto-log>
