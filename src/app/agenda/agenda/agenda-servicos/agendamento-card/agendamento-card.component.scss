@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	height: 100%;
	cursor: pointer;
	border: 1px solid $branco;
	border-top-left-radius: 7px;
	border-bottom-left-radius: 7px;
	color: $branco;
	overflow: hidden;
	position: relative;
}

.normal-size {
	padding-left: 18px;

	.icon-circle {
		width: 14px;
		height: 14px;
		position: absolute;
		border-radius: 7px;
		left: -1px;
		background-color: $pretoPri;
		color: $branco;
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1px solid $branco;
	}

	.professor-nome {
		padding-top: 1px;
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 12px;
		white-space: nowrap;
		font-weight: 700;
	}

	.horario {
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 12px;
		white-space: nowrap;
		font-weight: 700;
	}
}

.small-size {
	padding-left: 18px;

	.icon-circle {
		width: 14px;
		height: 14px;
		position: absolute;
		border-radius: 7px;
		left: -1px;
		background-color: $pretoPri;
		color: $branco;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.info {
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 12px;
		white-space: nowrap;
		position: relative;
		top: -1.5px;

		.professor {
			font-weight: 700;
		}
	}
}
