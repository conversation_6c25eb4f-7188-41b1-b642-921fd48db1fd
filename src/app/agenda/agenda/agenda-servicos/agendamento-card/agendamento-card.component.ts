import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
	EventEmitter,
	HostBinding,
} from "@angular/core";
import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { AgendaAgendamento, AgendaAgendamentoStatus } from "treino-api";

@Component({
	selector: "pacto-agendamento-card",
	templateUrl: "./agendamento-card.component.html",
	styleUrls: ["./agendamento-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendamentoCardComponent implements OnInit {
	@Input() slot: DiscreteGridSlot<AgendaAgendamento>;
	@Input() context: { forceUpdate$: EventEmitter<any> };

	constructor() {}

	ngOnInit() {}

	@HostBinding("style.backgroundColor") get backgroundColor() {
		return this.cor;
	}

	get normal() {
		return this.slot.height > 1;
	}

	get agendamento(): AgendaAgendamento {
		return this.slot.event.payload;
	}

	get professor(): string {
		return this.agendamento.professor.nome;
	}

	get horarioInicio(): string {
		return `${this.agendamento.horarioInicial}`;
	}

	get horario(): string {
		return `${this.agendamento.horarioInicial} - ${this.agendamento.horarioFinal}`;
	}

	get cor(): string {
		return this.agendamento.tipoAgendamento.cor;
	}

	get icon(): string {
		const status = this.agendamento.status;
		if (status === AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO) {
			return "pct-more-horizontal";
		} else if (status === AgendaAgendamentoStatus.CONFIRMADO) {
			return "pct-check";
		} else if (status === AgendaAgendamentoStatus.FALTOU) {
			return "pct-minus";
		} else if (status === AgendaAgendamentoStatus.EXECUTADO) {
			return "pct-check";
		} else {
			return null;
		}
	}

	corStatus(): string {
		const status = this.agendamento.status;
		if (status === AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO) {
			return "#51555A";
		} else if (status === AgendaAgendamentoStatus.CONFIRMADO) {
			return "#0380E3";
		} else if (status === AgendaAgendamentoStatus.FALTOU) {
			return "#D6A10F";
		} else if (status === AgendaAgendamentoStatus.EXECUTADO) {
			return "#28AB45";
		} else {
			return null;
		}
	}
}
