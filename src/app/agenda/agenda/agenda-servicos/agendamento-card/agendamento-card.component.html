<div *ngIf="normal" class="normal-size">
	<div [ngStyle]="{ backgroundColor: corStatus() }" class="icon-circle">
		<i *ngIf="icon" class="pct {{ icon }}"></i>
	</div>
	<div class="professor-nome">{{ professor }}</div>
	<div class="horario">{{ horario }}</div>
</div>

<div *ngIf="!normal" class="small-size">
	<div [ngStyle]="{ backgroundColor: corStatus() }" class="icon-circle">
		<i *ngIf="icon" class="pct {{ icon }}"></i>
	</div>
	<div class="info">
		{{ horarioInicio }} -
		<span class="professor">{{ professor }}</span>
	</div>
</div>
