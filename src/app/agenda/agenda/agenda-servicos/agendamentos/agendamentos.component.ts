import {
	Compo<PERSON>,
	OnInit,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	On<PERSON><PERSON>roy,
} from "@angular/core";

import {
	AgendaServicoAgendamentoFilter,
	AgendaServicosStateService,
} from "../agenda-servicos-state.service";
import {
	AgendaAgendamento,
	AgendaAgendamentoStatus,
	TreinoApiAgendamentoService,
} from "treino-api";
import { AgendaPeriodFilter } from "../../agenda.model";
import { Subscription } from "rxjs";
import { AgendaEvento } from "../../agenda-layout-v2/agenda-layout-v2.component";
import { AgendamentoCardComponent } from "../agendamento-card/agendamento-card.component";
import { FormControl } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { FilterOption } from "../../agenda-layout-v2-filter/agenda-layout-v2-filter.component";
import { AgendamentoHoverCardComponent } from "../agendamento-hover-card/agendamento-hover-card.component";
import { AgendamentoModalCardComponent } from "../agendamento-modal-card/agendamento-modal-card.component";
import { AgendaTimeGridStateService } from "../../agenda-time-grid/agenda-time-grid-state.service";
import { ModalService } from "@base-core/modal/modal.service";
import { AgendamentoEditModalComponent } from "../agendamento-edit-modal/agendamento-edit-modal.component";
import { SnotifyService } from "ng-snotify";
import { AgendamentoDeleteModalComponent } from "../agendamento-delete-modal/agendamento-delete-modal.component";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { PerfilAcessoRecursoNome } from "treino-api";
import { PerfilRecursoPermissoTipo } from "treino-api";

declare var moment;

@Component({
	selector: "pacto-servico-agendamentos",
	templateUrl: "./agendamentos.component.html",
	styleUrls: ["./agendamentos.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ServicoAgendamentosComponent implements OnInit, OnDestroy {
	constructor(
		private cd: ChangeDetectorRef,
		private snotify: SnotifyService,
		private agendamentoService: TreinoApiAgendamentoService,
		private modalService: ModalService,
		private restService: RestService,
		private state: AgendaServicosStateService,
		private agendaGridState: AgendaTimeGridStateService,
		private sessionService: SessionService
	) {}

	private subscriptions: Subscription[] = [];
	private fetchDataSubscription: Subscription;

	loading = false;
	eventos: AgendaEvento[][] = [];
	professoresFc = new FormControl();
	tiposEventos: FilterOption[] = [];
	resposeParser = (data) => data.content;
	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	get statusFilter(): AgendaAgendamentoStatus[] {
		return this.state.statusFilter;
	}

	get periodFilter(): AgendaPeriodFilter {
		return this.state.periodFilter;
	}

	get tiposFilter(): number[] {
		return this.state.tipoFilter;
	}

	get professoresUrl() {
		return this.restService.buildFullUrl("colaboradores/all-simple");
	}

	statusOptionsChangedHandler(status: AgendaAgendamentoStatus[]) {
		this.state.updateFiltroStatus(status);
	}

	tiposChangedHandler(tipos: any[]) {
		this.state.updateFiltroTiposAgendamento(tipos as number[]);
	}

	ngOnInit() {
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.AGENDAMENTO_DE_SERVICO_PELA_TELA_AGENDAMENTOS
		);
		this.fetchTiposAgendamentos();

		const editEventSub = this.state.editarEvento$.subscribe((agendamento) => {
			this.openEditAgendamentoModal(agendamento);
		});

		const deleteEventSub = this.state.deleteEvento$.subscribe((agendamento) => {
			console.log("Recebendo deleteEvento$ para", agendamento);
			this.openRemoveAgendamentoModal(agendamento);
		});

		const professorFcSub = this.professoresFc.valueChanges.subscribe(
			(professores) => {
				this.state.updateFiltroProfessores(professores);
			}
		);

		const professorSub = this.state.professoresFilterUpdate$.subscribe(
			(professores) => {
				this.professoresFc.setValue(professores, { emitEvent: false });
			}
		);

		const updateSub = this.state.update$.subscribe((agedamentoFiltro) => {
			this.fetchData(agedamentoFiltro);
		});

		this.subscriptions.push(
			professorFcSub,
			professorSub,
			updateSub,
			editEventSub,
			deleteEventSub
		);
	}

	periodFilterUpdateHandler(periodFilter: AgendaPeriodFilter) {
		this.state.updatePeriodFilter(periodFilter);
	}

	ngOnDestroy() {
		if (this.fetchDataSubscription) {
			this.fetchDataSubscription.unsubscribe();
		}
		this.subscriptions.forEach((subscription) => {
			if (subscription) {
				subscription.unsubscribe();
			}
		});
	}

	private openEditAgendamentoModal(agendamento: AgendaAgendamento) {
		const tipoAgendamento = agendamento.tipoAgendamento.comportamentoEnum;
		let permissao;

		switch (tipoAgendamento) {
			case "AVALIACAO_FISICA":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.AVALIACAO_FISICA
				);
				break;
			case "CONTATO_INTERPESSOAL":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
				);
				break;
			case "PRESCRICAO_TREINO":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.PRESCRICAO_TREINO
				);
				break;
			case "RENOVAR_TREINO":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.RENOVAR_TREINO
				);
				break;
			case "REVISAO_TREINO":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.REVISAO_TREINO
				);
				break;
			default:
				return;
		}

		if (
			permissao &&
			(permissao.permissao.includes(PerfilRecursoPermissoTipo.EDITAR) ||
				permissao.permissao.includes(
					PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
				) ||
				permissao.permissao.includes(PerfilRecursoPermissoTipo.TOTAL) ||
				permissao.permissao.includes(
					PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			const modal = this.modalService.open(
				"Editar Evento",
				AgendamentoEditModalComponent
			);
			modal.componentInstance.agendamento = agendamento;
			modal.result.then(
				(editDto) => {
					this.agendamentoService
						.editarAgendamento(agendamento.id, editDto)
						.subscribe((result) => {
							if (result && result.id) {
								this.snotify.success("Agendamento editado com sucesso");
							} else if (result === "horario_indisponivel") {
								this.snotify.error("Horário indisponível.");
							} else if (result === "validacaoalunoagendamento") {
								this.snotify.error(
									"O aluno já realizou o número limite de agendamentos dentro do período de dias informado no Tipo de Evento."
								);
							} else if (
								result === "validacaoalunoagendamentofaltaemintervalodias"
							) {
								this.snotify.error(
									"O aluno possui uma falta dentro do intervalo de dias configurado no Tipo de Evento."
								);
							} else if (
								result.toString().toLowerCase().includes("tempo mínimo")
							) {
								this.snotify.error(result.toString());
							} else if (
								result
									.toString()
									.toLowerCase()
									.includes("limite de agendamentos permitidos")
							) {
								this.snotify.error(result.toString());
							} else if (
								result
									.toString()
									.toLowerCase()
									.includes("falta(s) dentro do período")
							) {
								this.snotify.error(result.toString());
							} else if (
								result.toString().toLowerCase().includes("antecedência")
							) {
								this.snotify.error(`Alteração não permitida: ${result}`);
							} else if (
								result.toString().toLowerCase().includes("cancelamento")
							) {
								this.snotify.error(`Operação não permitida: ${result}`);
							} else {
								this.snotify.error(
									result && result.toString().trim()
										? `${result}`
										: "Erro ao editar agendamento. Verifique os dados informados."
								);
							}
							this.fetchData(this.state.currentFiltro);
						});
				},
				() => {}
			);
		} else {
			this.snotify.warning(
				"Seu usuário não tem permissão para editar este agendamento. Procure o seu administrador."
			);
		}
	}

	private openRemoveAgendamentoModal(agendamento: AgendaAgendamento) {
		const tipoAgendamento = agendamento.tipoAgendamento.comportamentoEnum;
		let permissao;

		switch (tipoAgendamento) {
			case "AVALIACAO_FISICA":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.AVALIACAO_FISICA
				);
				break;
			case "CONTATO_INTERPESSOAL":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
				);
				break;
			case "PRESCRICAO_TREINO":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.PRESCRICAO_TREINO
				);
				break;
			case "RENOVAR_TREINO":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.RENOVAR_TREINO
				);
				break;
			case "REVISAO_TREINO":
				permissao = this.sessionService.recursos.get(
					PerfilAcessoRecursoNome.REVISAO_TREINO
				);
				break;
			default:
				return;
		}
		if (
			permissao &&
			(permissao.permissao.includes(PerfilRecursoPermissoTipo.EXCLUIR) ||
				permissao.permissao.includes(PerfilRecursoPermissoTipo.TOTAL))
		) {
			const modal = this.modalService.open(
				"Remover Agendamento",
				AgendamentoDeleteModalComponent
			);
			modal.componentInstance.agendamento = agendamento;
			modal.result.then(
				(observacao) => {
					this.agendamentoService
						.removerAgendamento(agendamento.id, observacao)
						.subscribe((result) => {
							if (result) {
								this.snotify.success("Agendamento removido com sucesso!");
							} else {
								this.snotify.error("Erro ao remover agendamento!");
							}
							this.fetchData(this.state.currentFiltro);
						});
				},
				() => {}
			);
		} else {
			this.snotify.warning(
				"Seu usuário não tem permissão para remover este agendamento. Procure o seu administrador."
			);
		}
	}

	reload() {
		this.fetchData(this.state.currentFiltro);
	}

	private fetchTiposAgendamentos() {
		this.subscriptions.push(
			this.state.obterTiposAgendamento().subscribe((tipos) => {
				this.tiposEventos = tipos.map((tipo) => {
					return {
						id: tipo.id,
						label: tipo.nome,
						color: tipo.cor,
					};
				});
				const codsTipos = new Array<any>();
				this.tiposEventos.forEach((t) => {
					codsTipos.push(t.id);
				});
				this.state.updateFiltroTiposAgendamento(codsTipos);
				this.cd.detectChanges();
			})
		);
	}

	private fetchData(agendamentoFiltro: AgendaServicoAgendamentoFilter) {
		if (this.fetchDataSubscription) {
			this.fetchDataSubscription.unsubscribe();
		}
		this.loading = true;
		this.eventos = [];
		this.cd.detectChanges();
		this.fetchDataSubscription = this.agendamentoService
			.obterAgendamentosPorDia(
				agendamentoFiltro.periodo.date,
				agendamentoFiltro.periodo.view,
				{
					professoresIds: agendamentoFiltro.professoresIds,
					status: agendamentoFiltro.status,
					tipoAgendamentoIds: agendamentoFiltro.tipoAgendamentoIds,
				}
			)
			.subscribe((agendamentosPorDia) => {
				this.loading = false;
				this.eventos = [];
				for (const dia in agendamentosPorDia) {
					const gridEvents: AgendaEvento[] = [];
					agendamentosPorDia[dia].forEach((agendamento) => {
						if (this.permissaoConsultarAgendamento(agendamento)) {
							gridEvents.push({
								inicio: agendamento.horarioInicial,
								fim: agendamento.horarioFinal,
								renderComponent: AgendamentoCardComponent,
								hoverComponent: AgendamentoHoverCardComponent,
								modalComponent: AgendamentoModalCardComponent,
								payload: agendamento,
							});
						}
					});
					this.eventos.push(gridEvents);
				}
				this.agendaGridState.resetModalState();
				this.cd.detectChanges();
			});
	}

	private permissaoConsultarAgendamento(agendamento) {
		if (agendamento.tipoAgendamento.comportamentoEnum === "AVALIACAO_FISICA") {
			const permissao = this.sessionService.recursos.get(
				PerfilAcessoRecursoNome.AVALIACAO_FISICA
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "CONTATO_INTERPESSOAL"
		) {
			const permissao = this.sessionService.recursos.get(
				PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "PRESCRICAO_TREINO"
		) {
			const permissao = this.sessionService.recursos.get(
				PerfilAcessoRecursoNome.PRESCRICAO_TREINO
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "RENOVAR_TREINO"
		) {
			const permissao = this.sessionService.recursos.get(
				PerfilAcessoRecursoNome.RENOVAR_TREINO
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		} else if (
			agendamento.tipoAgendamento.comportamentoEnum === "REVISAO_TREINO"
		) {
			const permissao = this.sessionService.recursos.get(
				PerfilAcessoRecursoNome.REVISAO_TREINO
			);
			if (permissao !== undefined && permissao.consultar) {
				return true;
			}
		}
		return false;
	}

	get urlLog() {
		return this.restService.buildFullUrl(`log/agendamentos`);
	}
}
