<pacto-agenda-layout-v2
	(periodFilterUpdate)="periodFilterUpdateHandler($event)"
	(reloadEvent)="reload()"
	[eventos]="eventos"
	[loading]="loading"
	[periodFilter]="periodFilter"
	i18n-title="@@agenda-servicos:titulo"
	title="Agenda de serviços">
	<ng-container side-filters>
		<div class="filter-container">
			<div class="professores-label" i18n="@@agenda-servicos:professores">
				Professores
			</div>
			<pacto-cat-multi-select-filter
				[control]="professoresFc"
				[endpointUrl]="professoresUrl"
				[imageKey]="'uriImagem'"
				[labelKey]="'nome'"
				[paramBuilder]="paramBuilder"
				[resposeParser]="resposeParser"></pacto-cat-multi-select-filter>

			<!-- Status -->
			<pacto-agenda-layout-v2-status-filter
				(optionsChanged)="statusOptionsChangedHandler($event)"
				[selected]="statusFilter"
				class="filtro-block"></pacto-agenda-layout-v2-status-filter>

			<!-- Tipos de eventos -->
			<pacto-agenda-layout-v2-filter
				(optionsChanged)="tiposChangedHandler($event)"
				[options]="tiposEventos"
				[selected]="tiposFilter"
				[titulo]="'Tipo de evento'"
				class="filtro-block"></pacto-agenda-layout-v2-filter>

			<pacto-log [url]="urlLog"></pacto-log>
		</div>
	</ng-container>

	<ng-container extra-buttons>
		<!-- <div class="adicionar control-btn">
      Adicionar Disponibilidade
    </div> -->
		<div
			[routerLink]="['/agenda/painel/servicos/disponibilidades']"
			class="agendamentos control-btn"
			i18n="@@agenda-servicos:disponibilidade"
			id="ver-disponibilidades">
			Ver Disponibilidades
		</div>
	</ng-container>
</pacto-agenda-layout-v2>
