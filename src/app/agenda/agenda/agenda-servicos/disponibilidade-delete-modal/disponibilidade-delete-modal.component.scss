@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 30px;
	position: relative;
}

.top-row {
	display: flex;
	align-items: center;
	margin-bottom: 27px;
}

.professor-nome {
	font-size: 24px;
	font-weight: 600;
	margin-left: 15px;
	color: $pretoPri;
	flex-grow: 1;
}

.data {
	background-color: #eff2f7;
	color: #b4b7bb;
	font-size: 20px;
	font-weight: 400;
	line-height: 40px;
	border-radius: 4px;
	border: 1px solid #d3d5d7;
	text-align: center;
	padding: 0px 14px;
}

.tipos-atividade-label {
	color: #b4b7bb;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 8px;
}

.horario-label {
	color: #b4b7bb;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 8px;
	margin-top: 16px;
}

.tipo-atividade {
	font-weight: 600;
	font-size: 16px;
	color: $pretoPri;
	margin: 10px 0px;
}

.professor-circle {
	width: 44px;
	height: 44px;
	border-radius: 22px;
	background-color: #1b4166;
	color: $branco;
	display: flex;
	align-items: center;
	justify-content: center;
	background-position: center;
	background-size: contain;
}

.actions {
	display: flex;
	justify-content: space-between;
	margin-top: 30px;

	pacto-cat-button {
		width: 200px;
	}
}

.delete-options {
	background-color: #eff2f7;
	border-radius: 4px;
	padding: 15px 20px;
	margin-top: 30px;
}

.options-titulo {
	color: $azulimPri;
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 15px;
}

.radio-btn {
	width: 14px;
	height: 14px;
	border-radius: 7px;
	border: 1px solid #b4b7bb;
	color: #b4b7bb;
	display: flex;
	justify-content: center;
	margin-right: 10px;
	align-items: center;

	.radio-checked {
		width: 4px;
		height: 4px;
		border-radius: 2px;
		background-color: $branco;
	}
}

.radio-option {
	display: flex;
	align-items: center;
	margin: 10px 0px;
	cursor: pointer;
}

.radio-option.selected {
	.radio-btn {
		color: #51555a;
		border-color: #51555a;
		background-color: #51555a;
	}

	.radio-value {
		color: #51555a;
	}
}

.radio-value {
	color: $cinzaPri;
	font-size: 14px;
	font-weight: 400;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}
