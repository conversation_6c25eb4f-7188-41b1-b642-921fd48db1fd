import {
	ChangeDetectionStrategy,
	Component,
	Input,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
} from "@angular/core";
import { AgendaDisponibilidade } from "treino-api";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

declare var moment;

@Component({
	selector: "pacto-disponibilidade-delete-modal",
	templateUrl: "./disponibilidade-delete-modal.component.html",
	styleUrls: ["./disponibilidade-delete-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DisponibilidadeDeleteModalComponent implements OnInit, OnDestroy {
	@Input() disponibilidade: AgendaDisponibilidade;
	fg = new FormGroup({
		horarioInicial: new FormControl("", [
			Validators.required,
			Validators.minLength(5),
		]),
		horarioFinal: new FormControl("", [
			Validators.required,
			Validators.minLength(5),
		]),
	});
	seRepete = false;
	todos = true;
	editar = false;

	get professorImage() {
		const uri = this.disponibilidade.professor.imageUri;
		return uri ? `url(${uri})` : null;
	}

	get day() {
		return moment(this.disponibilidade.dia);
	}

	get professor() {
		return this.disponibilidade.professor.nome;
	}

	get atividades() {
		return this.disponibilidade.tiposAtividades;
	}

	setTodos(value) {
		this.todos = value;
	}

	ngOnInit() {
		if (this.disponibilidade) {
			this.fillOutForm();
		}
	}

	private fillOutForm() {
		this.fg.patchValue(
			{
				horarioInicial: this.disponibilidade.horarioInicial,
				horarioFinal: this.disponibilidade.horarioFinal,
			},
			{ emitEvent: false }
		);
	}

	ngOnDestroy() {}

	private getDto() {
		const dto = this.fg.getRawValue();
		dto.repetir = this.seRepete ? this.todos : false;
		return dto;
	}

	salvarHandler() {
		if (this.editar) {
			this.openModal.close(this.getDto());
		} else {
			const option = this.seRepete ? this.todos : null;
			this.openModal.close(option);
		}
	}

	cancelarHandler() {
		this.openModal.dismiss();
	}

	constructor(private openModal: NgbActiveModal) {}

	get timeMask() {
		return (value) => {
			const result = [/[0-2]/, null, ":", /[0-5]/, /[0-9]/];
			const firstD = parseInt(value[0], 10);

			/* digito 2 */
			if (!isNaN(firstD) && firstD === 2) {
				result[1] = /[0-3]/;
			} else {
				result[1] = /[0-9]/;
			}
			return result;
		};
	}

	get valid() {
		return this.fg.valid;
	}
}
