<div class="top-row">
	<div
		[ngStyle]="{
			'background-image': professorImage
		}"
		class="professor-circle">
		<i *ngIf="!professorImage" class="pct pct-user default-icon"></i>
	</div>
	<div class="professor-nome">{{ professor }}</div>
	<div class="data">{{ day.format("DD/MM/YYYY") }}</div>
</div>

<div class="tipos-atividade-label">Tipo da atividade</div>
<div *ngFor="let atividade of atividades" class="tipo-atividade">
	{{ atividade.nome }}
</div>

<div *ngIf="!editar" class="horario-label">Hor<PERSON><PERSON> da disponibilidade</div>
<div *ngIf="!editar" class="horario">
	{{ disponibilidade.horarioInicial }} - {{ disponibilidade.horarioFinal }}
</div>

<div *ngIf="editar" class="row block-a">
	<div class="col-md-6">
		<div class="horario-label">Hor<PERSON>rio inicial</div>
		<input
			[formControl]="fg.get('horarioInicial')"
			[textMask]="{ guide: true, mask: timeMask }"
			id="horario-inicial-editar-disponibilidade"
			type="text" />
	</div>
	<div class="col-md-6">
		<div class="horario-label">Horário final</div>
		<input
			[formControl]="fg.get('horarioFinal')"
			[textMask]="{ guide: true, mask: timeMask }"
			id="horario-final-editar-disponibilidade"
			type="text" />
	</div>
</div>

<div *ngIf="seRepete" class="delete-options">
	<div class="options-titulo">
		Esta disponibilidade se repete em outras datas
	</div>
	<div
		(click)="setTodos(false)"
		[ngClass]="{ selected: !todos }"
		class="radio-option"
		id="{{ editar ? 'editar-esta' : 'excluir-esta' }}">
		<div class="radio-btn">
			<div *ngIf="!todos" class="radio-checked"></div>
		</div>
		<div class="radio-value">
			{{ editar ? "Editar" : "Excluir" }} apenas esta disponibilidade
		</div>
	</div>
	<div
		(click)="setTodos(true)"
		[ngClass]="{ selected: todos }"
		class="radio-option"
		id="{{ editar ? 'editar-proximas' : 'excluir-proxima' }}">
		<div class="radio-btn">
			<div *ngIf="todos" class="radio-checked"></div>
		</div>
		<div class="radio-value">
			{{ editar ? "Editar" : "Excluir" }} esta e as próximas
		</div>
	</div>
</div>

<div class="actions">
	<pacto-cat-button
		(click)="cancelarHandler()"
		[full]="true"
		[label]="'Cancelar'"
		[type]="'OUTLINE'"
		id="cancelar-editar-disponibilidade"></pacto-cat-button>
	<pacto-cat-button
		(click)="salvarHandler()"
		*ngIf="!editar"
		[full]="true"
		[label]="'Confirmar exclusão'"
		[type]="'ALERT'"
		id="confirmar-exclusao-disponibilidade"></pacto-cat-button>
	<pacto-cat-button
		(click)="salvarHandler()"
		*ngIf="editar"
		[disabled]="!valid"
		[full]="true"
		[label]="'Salvar'"
		id="salvar-editar-disponibilidade"></pacto-cat-button>
</div>
