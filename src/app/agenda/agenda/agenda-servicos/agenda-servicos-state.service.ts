import { EventEmitter, Injectable } from "@angular/core";

import {
	AgendaView,
	AgendaAgendamento,
	AgendaAgendamentoStatus,
	AgendaDisponibilidade,
	TipoAgendamento,
	ProfessorSimples,
	TreinoApiAgendaAgendamentoService,
} from "treino-api";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import { AgendaPeriodFilter } from "../agenda.model";
import { BehaviorSubject, combineLatest, Observable, of } from "rxjs";
import { map } from "rxjs/operators";
import { ColaboradorBase } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

export interface AgendaServicoAgendamentoFilter {
	periodo: AgendaPeriodFilter;
	professoresIds: number[];
	status: AgendaAgendamentoStatus[];
	tipoAgendamentoIds: number[];
	disponibilidadeFiltro: "DISPONIVEIS" | "TODOS";
}

@Injectable()
export class AgendaServicosStateService {
	editarEvento$: EventEmitter<AgendaAgendamento> = new EventEmitter();
	deleteEvento$: EventEmitter<AgendaAgendamento> = new EventEmitter();
	deleteDisponibilidade$: EventEmitter<AgendaDisponibilidade> =
		new EventEmitter();
	editDisponibilidade$: EventEmitter<AgendaDisponibilidade> =
		new EventEmitter();
	adicionarEvento$: EventEmitter<AgendaDisponibilidade> = new EventEmitter();

	/**
	 * State
	 */
	private filtroPeriodo$: BehaviorSubject<AgendaPeriodFilter> =
		new BehaviorSubject({
			view: AgendaView.DIA,
			date: moment().format("YYYYMMDD"),
		});
	private filtroProfessores$: BehaviorSubject<ColaboradorBase[]> =
		new BehaviorSubject([]);
	private filtroTipoServico$: BehaviorSubject<number[]> = new BehaviorSubject(
		[]
	);
	private filtroStatus$: BehaviorSubject<AgendaAgendamentoStatus[]> =
		new BehaviorSubject([
			AgendaAgendamentoStatus.AGUARDANDO_CONFIRMACAO,
			AgendaAgendamentoStatus.CONFIRMADO,
			AgendaAgendamentoStatus.FALTOU,
			AgendaAgendamentoStatus.EXECUTADO,
		]);
	private filtroDisponibilidade$: BehaviorSubject<"TODOS" | "DISPONIVEIS"> =
		new BehaviorSubject("DISPONIVEIS");

	constructor(
		private agendaAgendamentoService: TreinoApiAgendaAgendamentoService,
		private windowUtil: WindowUtilService,
		private sessionService: SessionService
	) {
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.ACESSO_AGENDA_DE_SERVICOS
		);
	}

	obterTiposAgendamento(): Observable<TipoAgendamento[]> {
		return this.agendaAgendamentoService
			.obterListaTipoAgendamento(true)
			.pipe(map((response) => response.content));
	}

	obterTiposDisponiveis(dia, hora): Observable<TipoAgendamento[]> {
		return this.agendaAgendamentoService
			.obterTiposDisponiveis(dia, hora)
			.pipe(map((response) => response.content));
	}

	obterProfessoresDisponiveis(dia, hora, tipo): Observable<ProfessorSimples[]> {
		return this.agendaAgendamentoService
			.obterProfessoresDisponiveis(dia, hora, tipo)
			.pipe(map((response) => response.content));
	}

	detalhesDisponibilidade(
		dia,
		hora,
		tipo,
		professor
	): Observable<AgendaDisponibilidade> {
		return this.agendaAgendamentoService
			.detalheDisponibilidade(dia, hora, tipo, professor)
			.pipe(map((response) => response));
	}

	get periodFilter(): AgendaPeriodFilter {
		return this.filtroPeriodo$.value;
	}

	get statusFilter(): AgendaAgendamentoStatus[] {
		return this.filtroStatus$.value;
	}

	get tipoFilter(): number[] {
		return this.filtroTipoServico$.value;
	}

	get professoresFilter() {
		return this.filtroProfessores$.value;
	}

	get professoresFilterUpdate$(): Observable<any[]> {
		return this.filtroProfessores$.asObservable();
	}

	get update$(): Observable<AgendaServicoAgendamentoFilter> {
		return combineLatest([
			this.filtroPeriodo$,
			this.filtroProfessores$,
			this.filtroStatus$,
			this.filtroTipoServico$,
			this.filtroDisponibilidade$,
		]).pipe(
			map((data) => {
				return {
					periodo: data[0],
					professoresIds: data[1].map((professor) =>
						parseInt(professor.id, 10)
					),
					status: data[2],
					tipoAgendamentoIds: data[3],
					disponibilidadeFiltro: data[4],
				};
			})
		);
	}

	get currentFiltro(): AgendaServicoAgendamentoFilter {
		return {
			periodo: this.filtroPeriodo$.value,
			professoresIds: this.filtroProfessores$.value.map((p) =>
				parseInt(p.id, 10)
			),
			status: this.filtroStatus$.value,
			tipoAgendamentoIds: this.filtroTipoServico$.value,
			disponibilidadeFiltro: this.filtroDisponibilidade$.value,
		};
	}

	updateDisponibilidadeFiltro(filtro) {
		this.filtroDisponibilidade$.next(filtro);
	}

	updatePeriodFilter(periodFilter: AgendaPeriodFilter) {
		const changed =
			JSON.stringify(periodFilter) !==
			JSON.stringify(this.filtroPeriodo$.value);
		if (changed) {
			this.filtroPeriodo$.next(periodFilter);
		}
	}

	updateFiltroTiposAgendamento(tipoIds: number[]) {
		this.filtroTipoServico$.next(tipoIds);
	}

	updateFiltroStatus(status: AgendaAgendamentoStatus[]) {
		this.filtroStatus$.next(status);
	}

	updateFiltroProfessores(professores: any[]) {
		this.filtroProfessores$.next(professores);
	}
}
