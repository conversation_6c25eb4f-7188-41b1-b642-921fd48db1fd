<i (click)="closeHandler($event)" class="pct pct-x close-icon"></i>

<div class="month-day">{{ date.format("D") }}</div>
<div class="month-name">de {{ date.format("MMMM") }}</div>
<div class="year">{{ date.format("YYYY") }}</div>

<div class="body-block">
	<div class="body-label">Professor(a)</div>
	<div class="body-value body-bold">{{ professorN<PERSON> }}</div>
</div>

<div class="body-block">
	<div class="body-label"><PERSON><PERSON><PERSON><PERSON> disponível</div>
	<div class="body-value">{{ horario }}</div>
</div>

<div class="body-block">
	<div class="body-label">Atividade(s)</div>
	<div
		*ngFor="let atividade of atividades"
		[ngStyle]="{ color: atividade.cor ? atividade.cor : null }"
		class="body-value body-bold">
		{{ atividade.nome }}
	</div>
</div>

<div class="modal-actions">
	<div
		(click)="incluirAgendamentoHandler($event)"
		*ngIf="!todos"
		class="modal-action blue"
		id="incluir-agendamento-disponibilidade">
		Incluir agendamento
	</div>
	<div class="lower-row">
		<div
			(click)="excluirHandler($event)"
			class="modal-action"
			id="excluir-agendamento-disponibilidade">
			Excluir
		</div>
		<div
			(click)="editarHandler($event)"
			class="modal-action blue"
			id="editar-agendamento-disponibilidade">
			Editar
		</div>
	</div>
	<pacto-log [url]="urlLog"></pacto-log>
</div>
