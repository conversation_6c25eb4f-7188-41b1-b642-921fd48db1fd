import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
	EventEmitter,
	Output,
} from "@angular/core";
import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { AgendaAgendamentoStatus, AgendaDisponibilidade } from "treino-api";

import { AgendaServicosStateService } from "../agenda-servicos-state.service";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";

declare var moment;

@Component({
	selector: "pacto-disponibilidade-modal-card",
	templateUrl: "./disponibilidade-modal-card.component.html",
	styleUrls: ["./disponibilidade-modal-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DisponibilidadeModalCardComponent implements OnInit {
	@Input() slot: DiscreteGridSlot<AgendaDisponibilidade>;
	@Output() closeModal: EventEmitter<MouseEvent> = new EventEmitter();

	constructor(
		private agendaServiceState: AgendaServicosStateService,
		private session: SessionService,
		private rest: RestService,
		private snotifyService: SnotifyService
	) {}

	get disponibilidade(): AgendaDisponibilidade {
		return this.slot.event.payload;
	}

	get AgendaAgendamentoStatus() {
		return AgendaAgendamentoStatus;
	}

	get todos() {
		return (
			this.agendaServiceState.currentFiltro.disponibilidadeFiltro === "TODOS"
		);
	}

	get date() {
		return moment(this.disponibilidade.dia);
	}

	get horario() {
		return `${this.disponibilidade.horarioInicial} - ${this.disponibilidade.horarioFinal}`;
	}

	get professorNome() {
		return this.disponibilidade.professor.nome;
	}

	get atividades() {
		return this.disponibilidade.tiposAtividades
			? this.disponibilidade.tiposAtividades
			: [];
	}

	ngOnInit() {
		this.validarPermissaoIncluirAgendamento();
	}

	incluirAgendamentoHandler($event) {
		this.agendaServiceState.adicionarEvento$.emit(this.disponibilidade);
		this.closeModal.emit($event);
	}

	validarPermissaoIncluirAgendamento() {
		for (let i = 0; i < this.disponibilidade.tiposAtividades.length; i++) {
			if (
				this.disponibilidade.tiposAtividades[i].comportamentoEnum ===
				"AVALIACAO_FISICA"
			) {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.AVALIACAO_FISICA
				);
				if (!permissao.incluir) {
					this.disponibilidade.tiposAtividades.splice(i, 1);
					i--;
				}
			} else if (
				this.disponibilidade.tiposAtividades[i].comportamentoEnum ===
				"CONTATO_INTERPESSOAL"
			) {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
				);
				if (!permissao.incluir) {
					this.disponibilidade.tiposAtividades.splice(i, 1);
					i--;
				}
			} else if (
				this.disponibilidade.tiposAtividades[i].comportamentoEnum ===
				"PRESCRICAO_TREINO"
			) {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.PRESCRICAO_TREINO
				);
				if (!permissao.incluir) {
					this.disponibilidade.tiposAtividades.splice(i, 1);
					i--;
				}
			} else if (
				this.disponibilidade.tiposAtividades[i].comportamentoEnum ===
				"RENOVAR_TREINO"
			) {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.RENOVAR_TREINO
				);
				if (!permissao.incluir) {
					this.disponibilidade.tiposAtividades.splice(i, 1);
					i--;
				}
			} else if (
				this.disponibilidade.tiposAtividades[i].comportamentoEnum ===
				"REVISAO_TREINO"
			) {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.REVISAO_TREINO
				);
				if (!permissao.incluir) {
					this.disponibilidade.tiposAtividades.splice(i, 1);
					i--;
				}
			}
		}
	}

	excluirHandler($event) {
		const perfil = this.session.recursos.get(
			PerfilAcessoRecursoNome.AGENDA_DISPONIBILIDADE
		);
		if (perfil.excluir) {
			this.agendaServiceState.deleteDisponibilidade$.emit(this.disponibilidade);
			this.closeModal.emit($event);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	closeHandler($event) {
		this.closeModal.emit($event);
	}

	editarHandler($event) {
		const perfil = this.session.recursos.get(
			PerfilAcessoRecursoNome.AGENDA_DISPONIBILIDADE
		);
		if (perfil.editar) {
			this.agendaServiceState.editDisponibilidade$.emit(this.disponibilidade);
			this.closeModal.emit($event);
		} else {
			this.snotifyService.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	get urlLog() {
		return this.rest.buildFullUrl(
			`log/disponibilidades/${this.disponibilidade.id}`
		);
	}
}
