<div class="wrapper">
	<div class="tipo-agendamento">
		<div class="label">Tipo de evento</div>
		<div class="value">{{ agendamento?.tipoAgendamento?.nome }}</div>
	</div>

	<div class="row block-a">
		<div class="col-md-6">
			<pacto-cat-datepicker
				[formControl]="agendamentoFg.get('dia')"
				[label]="'Data'"></pacto-cat-datepicker>
		</div>
		<div class="col-md-6">
			<div class="input-nome">Duração (minutos)</div>
			<input
				[formControl]="agendamentoFg.get('duracao')"
				[textMask]="{ guide: false, mask: duracaoMask }"
				id="duracao-editar-agendamento"
				type="text" />
		</div>
	</div>

	<div class="row block-a">
		<div class="col-md-6">
			<div class="input-nome">Ho<PERSON><PERSON><PERSON> inicial</div>
			<input
				[formControl]="agendamentoFg.get('horarioInicial')"
				[textMask]="{ guide: true, mask: timeMask }"
				type="text" />
		</div>
		<div class="col-md-6">
			<div class="input-nome">Horário final</div>
			<input [formControl]="agendamentoFg.get('horarioFinal')" type="text" />
		</div>
	</div>

	<div class="content-block">
		<div class="label">Aluno(a)</div>
		<div class="content-wrapper">
			<div [ngStyle]="{ backgroundImage: alunoUrl }" class="image-wrapper">
				<i *ngIf="!alunoUrl" class="pct pct-user"></i>
			</div>
			<div class="value-content">{{ agendamento?.aluno?.nome }}</div>
		</div>
	</div>

	<div class="content-block">
		<div class="label">Professor(a)</div>
		<div class="content-wrapper">
			<div [ngStyle]="{ backgroundImage: professorUrl }" class="image-wrapper">
				<i *ngIf="!professorUrl" class="pct pct-user"></i>
			</div>
			<div class="value-content">{{ agendamento?.professor?.nome }}</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<pacto-cat-select
				[control]="agendamentoFg.get('status')"
				[id]="'select-status'"
				[items]="[
					{ id: 'AGUARDANDO_CONFIRMACAO', label: 'Aguardando Confirmação' },
					{ id: 'CONFIRMADO', label: 'Confirmado' },
					{ id: 'EXECUTADO', label: 'Executado' },
					{ id: 'CANCELADO', label: 'Cancelado' },
					{ id: 'FALTOU', label: 'Faltou' }
				]"
				[label]="'Status do Agendamento'"></pacto-cat-select>
		</div>
	</div>

	<div *ngIf="!showObservacao" class="observacao-label">
		<span (click)="adicionarObsHandler()" id="adicionar-observacao-edit">
			<i class="pct pct-plus"></i>
			Adicionar observação
		</span>
	</div>

	<pacto-cat-form-textarea
		*ngIf="showObservacao"
		[control]="agendamentoFg.get('observacao')"
		[id]="'input-observacao'"
		[label]="'Observação'"></pacto-cat-form-textarea>

	<div *ngIf="error" class="error-block">
		<div class="upper">
			<i class="pct pct-alert-triangle"></i>
			Erro
		</div>
		<div class="error-msg">
			<span *ngIf="error === 'SEM_DISPONIBILIDADE_DATA'">
				· O professor {{ agendamento?.professor?.nome }} não possui
				disponibilidade nesta data
			</span>
			<span *ngIf="error === 'SEM_DISPONIBILIDADE_HORARIO'">
				· O professor {{ agendamento?.professor?.nome }} não possui
				disponibilidade neste horário
			</span>
		</div>
	</div>

	<div class="actions">
		<pacto-log [url]="urlLog"></pacto-log>
		<pacto-cat-button
			(click)="excluirHandler($event)"
			[disabled]="isDeleting"
			[full]="true"
			[label]="'Excluir'"
			id="btn-excluir-agendamento"></pacto-cat-button>
		<pacto-cat-button
			(click)="cancelarHandler()"
			[full]="true"
			[label]="'Cancelar'"
			[type]="'OUTLINE'"
			id="btn-cancelar-edicao-agendamento"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvarHandler()"
			[disabled]="error || !valid"
			[full]="true"
			[label]="'Salvar'"
			id="btn-salvar-edicao-agendamento"></pacto-cat-button>
	</div>

	<pacto-traducoes-xingling #traducao>
		<span
			i18n="@@agendamento:edit:aguardando-confirmacao"
			xingling="AGUARDANDO_CONFIRMACAO">
			Aguardando Confirmação
		</span>
		<span i18n="@@agendamento:edit:confirmado" xingling="CONFIRMADO">
			Confirmado
		</span>
		<span i18n="@@agendamento:edit:executado" xingling="EXECUTADO">
			Executado
		</span>
		<span i18n="@@agendamento:edit:faltou" xingling="FALTOU">Faltou</span>
	</pacto-traducoes-xingling>
</div>
