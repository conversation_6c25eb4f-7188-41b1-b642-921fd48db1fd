import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	AgendaAgendamento,
	AgendamentoTipoDuracao,
	AgendamentoEdit,
	ValidacaoAgendamentoErro,
	TreinoApiAgendamentoService,
	PerfilAcessoRecursoNome,
} from "treino-api";
import { AgendamentoHorario } from "../../services/agendamento.service.model";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { merge, Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { TraducoesXinglingComponent } from "ui-kit";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { AgendaServicosStateService } from "../agenda-servicos-state.service";
import { AgendamentoDeleteModalComponent } from "../agendamento-delete-modal/agendamento-delete-modal.component";
import { PerfilRecursoPermissoTipo } from "../../../../../../projects/treino-api/src/lib/perfil-acesso-recurso.model";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";

declare var moment;

@Component({
	selector: "pacto-agendamento-edit-modal",
	templateUrl: "./agendamento-edit-modal.component.html",
	styleUrls: ["./agendamento-edit-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendamentoEditModalComponent implements OnInit, OnDestroy {
	private deleteEventSub: Subscription;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() agendamento: AgendaAgendamento;
	@Output() closeModal: EventEmitter<MouseEvent> = new EventEmitter();

	error: ValidacaoAgendamentoErro;

	private horarioSub: Subscription;
	private validateTriggerSub: Subscription;
	private validateSub: Subscription;
	horarioInicialAtual;
	horarioFinalAtual;
	isDeleting = false;

	agendamentoFg = new FormGroup({
		dia: new FormControl("", [Validators.required]),
		duracao: new FormControl("", [Validators.required]),
		horarioInicial: new FormControl("", [
			Validators.required,
			Validators.pattern(/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/),
		]),
		horarioFinal: new FormControl(),
		status: new FormControl(),
		observacao: new FormControl(),
	});

	showObservacao = false;

	get valid() {
		return this.agendamentoFg.valid;
	}

	get alunoUrl() {
		const uri = this.agendamento.aluno.imageUri;
		return uri ? `url(${uri})` : null;
	}

	get professorUrl() {
		const uri = this.agendamento.professor.imageUri;
		return uri ? `url(${uri})` : null;
	}

	get timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	get duracaoMask() {
		return [/[0-9]/, /[0-9]/, /[0-9]/];
	}

	ngOnInit() {
		this.showObservacao = this.agendamento.observacao ? true : false;
		this.fillOutForm();
		this.setupEvents();
	}

	ngOnDestroy() {
		this.horarioSub.unsubscribe();
		this.validateTriggerSub.unsubscribe();
		if (this.validateSub) {
			this.validateSub.unsubscribe();
		}
	}

	private fillOutForm() {
		this.horarioInicialAtual = this.agendamento.horarioInicial;
		this.horarioFinalAtual = this.agendamento.horarioFinal;
		this.agendamentoFg.patchValue({
			dia: this.agendamento.data,
			horarioInicial: this.agendamento.horarioInicial,
			horarioFinal: this.agendamento.horarioFinal,
			status: this.agendamento.status,
			observacao: this.agendamento.observacao,
			duracao: this.getDuracao(),
		});
		this.agendamentoFg.get("horarioFinal").disable({ emitEvent: false });
		if (
			this.agendamento.tipoAgendamento.tipo_duracao ===
			AgendamentoTipoDuracao.DURACAO_PREDEFINIDA
		) {
			this.agendamentoFg.get("duracao").disable({ emitEvent: false });
		}
	}

	private setupEvents() {
		this.horarioSub = merge(
			this.agendamentoFg.get("horarioInicial").valueChanges,
			this.agendamentoFg.get("duracao").valueChanges
		).subscribe(() => {
			this.updateHorarioFinal();
		});

		this.validateTriggerSub = merge(
			this.agendamentoFg.get("horarioInicial").valueChanges,
			this.agendamentoFg.get("duracao").valueChanges,
			this.agendamentoFg.get("dia").valueChanges
		)
			.pipe(debounceTime(350))
			.subscribe(() => {
				this.validate();
			});
	}

	private validate() {
		const dto = this.getValidateDto();
		if (dto) {
			if (this.validateSub) {
				this.validateSub.unsubscribe();
			}
			this.cd.detectChanges();
			this.validateSub = this.agendamentoService
				.validarEdicaoAgendamento(this.agendamento.id, dto)
				.subscribe((result) => {
					this.error = result ? result.error : null;
					this.cd.detectChanges();
				});
		}
	}

	private getValidateDto(): AgendamentoHorario {
		const date = this.agendamentoFg.get("dia").value;
		const duracao = this.agendamentoFg.get("duracao").value;
		const horarioInicial = this.agendamentoFg.get("horarioInicial").value;
		const horarioFinal = this.agendamentoFg.get("horarioFinal").value;
		if (!this.valid) {
			return null;
		}
		return {
			data: moment(date).format("YYYYMMDD"),
			duracao: parseInt(duracao, 10),
			horarioInicial,
			horarioFinal,
			professor: this.agendamento.professor.id,
			tipo: this.agendamento.tipoAgendamento.id,
		};
	}

	private getEditDto(): AgendamentoEdit {
		const date = this.agendamentoFg.get("dia").value;
		const duracao = parseInt(this.agendamentoFg.get("duracao").value, 10);
		const horarioInicial = this.agendamentoFg.get("horarioInicial").value;
		const horarioFinal = this.agendamentoFg.get("horarioFinal").value;
		const observacao = this.agendamentoFg.get("observacao").value;
		const status = this.agendamentoFg.get("status").value;
		const horarioAlterado =
			this.horarioInicialAtual !== horarioInicial ||
			this.horarioFinalAtual !== horarioFinal;
		if (!this.valid) {
			return null;
		}
		return {
			data: moment(date).format("YYYYMMDD"),
			horarioInicial,
			horarioFinal,
			duracao,
			status,
			observacao,
			horarioAlterado,
		};
	}

	private updateHorarioFinal() {
		const inicioLabel = this.agendamentoFg.get("horarioInicial").value;
		const duracao = parseInt(this.agendamentoFg.get("duracao").value, 10);
		const inicio = this.agendaUtil.convertTimeLabelIntoMinutes(inicioLabel);
		if (!isNaN(inicio) && !isNaN(duracao)) {
			const final = inicio + duracao;
			const finalLabel = this.agendaUtil.convertMinutesIntoTimeLabel(final);
			this.agendamentoFg
				.get("horarioFinal")
				.setValue(finalLabel, { emitEvent: false });
		}
	}

	private getDuracao() {
		const finalMinutos = this.agendaUtil.convertTimeLabelIntoMinutes(
			this.agendamento.horarioFinal
		);
		const inicioMinutos = this.agendaUtil.convertTimeLabelIntoMinutes(
			this.agendamento.horarioInicial
		);
		return finalMinutos - inicioMinutos;
	}

	constructor(
		private openModal: NgbActiveModal,
		private agendamentoService: TreinoApiAgendamentoService,
		private cd: ChangeDetectorRef,
		private agendaUtil: AgendaUtilsService,
		private rest: RestService,
		private session: SessionService,
		private snotify: SnotifyService,
		private agendaServiceState: AgendaServicosStateService,
		private modalService: ModalService,
		private AgendaCardState: AgendaCardsStateService
	) {}

	adicionarObsHandler() {
		this.showObservacao = true;
	}

	cancelarHandler() {
		this.openModal.close();
	}

	salvarHandler() {
		const dto = this.getEditDto();
		if (dto) {
			this.openModal.close(dto);
		}
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/agendamentos/${this.agendamento.id}`);
	}

	excluirHandler($event) {
		if (this.isDeleting) {
			return;
		}
		this.isDeleting = true;
		if (!this.agendamento) {
			this.isDeleting = false;
			return;
		}
		const permissao = this.obterPermissaoParaExcluir();
		if (permissao !== undefined && permissao.excluir) {
			this.openRemoveAgendamentoModal(this.agendamento)
				.then(() => {
					this.isDeleting = false;
				})
				.catch(() => {
					this.isDeleting = false;
				});
		} else {
			this.apresentarNotificacaoSemPermissao();
			this.isDeleting = false;
		}
	}

	private obterPermissaoParaExcluir() {
		const tipoAgendamento = this.agendamento.tipoAgendamento.comportamentoEnum;
		switch (tipoAgendamento) {
			case "AVALIACAO_FISICA":
				return this.session.recursos.get(
					PerfilAcessoRecursoNome.AVALIACAO_FISICA
				);
			case "CONTATO_INTERPESSOAL":
				return this.session.recursos.get(
					PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
				);
			case "PRESCRICAO_TREINO":
				return this.session.recursos.get(
					PerfilAcessoRecursoNome.PRESCRICAO_TREINO
				);
			case "RENOVAR_TREINO":
				return this.session.recursos.get(
					PerfilAcessoRecursoNome.RENOVAR_TREINO
				);
			case "REVISAO_TREINO":
				return this.session.recursos.get(
					PerfilAcessoRecursoNome.REVISAO_TREINO
				);
			default:
				return undefined;
		}
	}

	apresentarNotificacaoSemPermissao() {
		this.snotify.warning(
			"Seu usuário não possui permissão, procure seu administrador"
		);
	}

	private openRemoveAgendamentoModal(
		agendamento: AgendaAgendamento
	): Promise<any> {
		return new Promise((resolve, reject) => {
			const modal = this.modalService.open(
				"Remover Agendamento",
				AgendamentoDeleteModalComponent
			);
			modal.componentInstance.agendamento = agendamento;
			modal.result
				.then((observacao) => {
					this.agendamentoService
						.removerAgendamento(agendamento.id, observacao)
						.subscribe((result) => {
							if (result) {
								this.snotify.success("Agendamento removido com sucesso!");
								this.AgendaCardState.notifyAgendamentoRemovido();
								this.openModal.close();
								resolve(result);
							} else {
								this.snotify.error("Erro ao remover agendamento!");
								reject();
							}
						});
				})
				.catch(() => {
					reject();
				});
		});
	}
}
