@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 30px;
}

.wrapper.validating {
	opacity: 0.2;
	position: relative;
	pointer-events: none;
}

.tipo-agendamento {
	.label {
		color: $cinzaPri;
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 5px;
	}

	.value {
		font-size: 16px;
		color: $pretoPri;
		font-weight: 600;
	}
}

.block-a {
	margin-top: 15px;
	margin-bottom: 15px;
}

.content-block {
	margin: 20px 0px;

	.label {
		font-size: 14px;
		font-weight: 600;
		color: $cinzaPri;
		margin-bottom: 8px;
	}

	.content-wrapper {
		display: flex;
		align-items: center;

		.image-wrapper {
			display: flex;
			align-items: center;
			justify-content: center;

			i {
				color: $branco;
				font-size: 17px;
			}

			width: 30px;
			height: 30px;
			border-radius: 15px;
			background-size: contain;
			background-position: center;
			background-color: #4387b5;
		}
	}

	.value-content {
		margin-left: 6px;
		font-weight: 600;
		font-size: 16px;
		color: $pretoPri;
	}
}

.observacao-label {
	font-size: 12px;
	margin-top: 20px;
	cursor: pointer;
	margin-bottom: 20px;
	color: $pretoPri;
}

.actions {
	display: flex;
	justify-content: space-evenly; /* Espaço igual entre todos os botões */
	gap: 10px; /* Aumenta o espaço entre os botões */
	margin-top: 30px;
}

pacto-cat-button,
pacto-log {
	flex-grow: 1;
	text-align: center;
}

.actions > * {
	flex: 1;
	text-align: center;
}

pacto-log {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%; /* Garante que o botão de log ocupe toda a largura disponível */
	height: 100%; /* Garante que o botão de log tenha a mesma altura que os outros botões */
}

#btn-excluir-agendamento + pacto-log {
	margin-left: 5px; /* Espaçamento entre Excluir e Log */
}

#btn-cancelar-edicao-agendamento + #btn-salvar-edicao-agendamento {
	margin-left: 5px; /* Espaçamento entre Cancelar e Salvar */
}

.error-block {
	width: 100%;
	background-color: #fcedee;
	padding: 14px;
	border-radius: 4px;
	margin-bottom: 20px;

	.upper {
		display: flex;
		align-items: center;
		color: #db2c3d;
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 7px;

		i {
			font-size: 20px;
			padding-right: 8px;
		}
	}

	.error-msg {
		font-size: 12px;
		font-weight: 400;

		.professor {
			font-weight: 700;
		}
	}
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.input-nome {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}
