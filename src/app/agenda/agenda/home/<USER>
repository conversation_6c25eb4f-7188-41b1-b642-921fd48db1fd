<pacto-home-page>
	<div class="adm-container" pactoBanner>
		<div
			[style.overflow]="itemsCampanha.length > 0 ? 'hidden' : 'auto'"
			class="adm-marketing">
			<pct-image-carousel [animationDuration]="4000" [selectedIndex]="0">
				<ng-container *ngIf="itemsCampanha.length > 0">
					<ng-container *ngFor="let itemCampanha of itemsCampanha">
						<pct-image-carousel-item>
							<iframe
								*ngIf="itemCampanha.tag === tagsEnum.IFRAME"
								[src]="itemCampanha.safeLink"
								style="height: 100%; width: 100%"></iframe>
							<div
								(click)="openLinkInNewTab(itemCampanha.link)"
								*ngIf="itemCampanha.tag === tagsEnum.SLIDER_LINK"
								class="slide-link">
								<img
									*ngIf="itemCampanha.urlImagem"
									[src]="itemCampanha.urlImagem"
									class="banner_bg" />
								<img
									*ngIf="itemCampanha.urlImagem"
									[src]="itemCampanha.urlImagem"
									class="banner_frente" />
							</div>
						</pct-image-carousel-item>
					</ng-container>
				</ng-container>
			</pct-image-carousel>
		</div>
	</div>
</pacto-home-page>
