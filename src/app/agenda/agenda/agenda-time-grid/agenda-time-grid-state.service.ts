import { ElementRef, EventEmitter, Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { debounceTime } from "rxjs/operators";

@Injectable({
	providedIn: "root",
})
export class AgendaTimeGridStateService {
	closeAllModal$: EventEmitter<any> = new EventEmitter();
	eventClicked$: EventEmitter<ElementRef> = new EventEmitter();

	private openModals: { id: number; column: number }[] = [];
	modalOpen$: BehaviorSubject<{ id: number; column: number }> =
		new BehaviorSubject(null);
	private updateModalOpenThrottle: BehaviorSubject<any> = new BehaviorSubject(
		null
	);

	constructor() {
		this.updateModalOpenThrottle.pipe(debounceTime(25)).subscribe(() => {
			this.updateOpenModalStatus();
		});
	}

	get modalOpen() {
		return this.modalOpen$.value;
	}

	resetModalState() {
		this.openModals = [];
		this.modalOpen$.next(null);
	}

	openModal(id: number, column: number) {
		const exists = this.openModals.findIndex((i) => i.id === id);
		if (exists < 0) {
			this.openModals.push({ id, column });
		}
		this.updateModalOpenThrottle.next(true);
	}

	closeModal(id: number) {
		const exists = this.openModals.findIndex((i) => i.id === id);
		if (exists >= 0) {
			this.openModals.splice(exists, 1);
		}
		this.updateModalOpenThrottle.next(true);
	}

	private updateOpenModalStatus() {
		this.modalOpen$.next(this.openModals.length ? this.openModals[0] : null);
	}
}
