@import "src/assets/scss/pacto/plataforma-import.scss";

$border: 10px;

:host {
	position: relative;
	display: block;
	height: 100%;
}

.component-container {
	position: absolute;

	&.hover {
		pointer-events: none;
	}

	.arrow-holder {
		width: 100%;
		display: flex;
		position: relative;
		pointer-events: none;
		z-index: 2;
	}

	&.first-quadrant {
		top: 75%;
		left: calc(50% - 12px);

		.arrow-holder {
			top: -3px;
		}

		.tooltip-content {
			top: -3px;
		}

		.arrow-img {
			position: relative;
			top: 2px;
		}
	}

	&.second-quadrant {
		top: 50%;
		right: calc(50% - 12px);

		.arrow-holder {
			justify-content: flex-end;
			top: -3px;
		}

		.tooltip-content {
			top: -3px;
		}

		.arrow-img {
			position: relative;
			top: 2px;
		}
	}

	&.third-quadrant {
		top: 20%;
		right: calc(50% - 12px);

		.arrow-holder {
			justify-content: flex-end;
			top: 3px;
		}

		.tooltip-content {
			top: 3px;
		}

		.arrow-img {
			position: relative;
			bottom: 2px;
		}
	}

	&.fourth-quadrant {
		top: 1%;
		left: calc(50% - 12px);

		.arrow-holder {
			top: 3px;
		}

		.tooltip-content {
			top: 3px;
		}

		.arrow-img {
			position: relative;
			bottom: 2px;
		}
	}
}

.component-content {
	background-color: $branco;
	box-shadow: 0px 0px 2px 2px #cccccc87;
	position: relative;
	border-radius: 5px;
	cursor: initial;
}

.arrow-img {
	width: 25px;
	height: 16px;
}
