<ng-container #wrapperContainerRef></ng-container>

<div
	[hidden]="!hoverInstance"
	[ngClass]="{
		'first-quadrant': quadrant === Quadrant.FIRST,
		'second-quadrant': quadrant === Quadrant.SECOND,
		'third-quadrant': quadrant === Quadrant.THIRD,
		'fourth-quadrant': quadrant === Quadrant.FOURTH
	}"
	class="component-container hover">
	<div *ngIf="hoverInstance" class="arrow-holder">
		<img class="arrow-img" src="assets/images/arrow-top.svg" />
	</div>
	<div class="component-content">
		<ng-container #hoverWrapperContainerRef></ng-container>
	</div>
	<!--    <div class="arrow-holder" *ngIf="hoverInstance && arrowBelow">-->
	<!--        <img class="arrow-img" src="assets/images/arrow-bottom.svg">-->
	<!--    </div>-->
</div>

<div
	[hidden]="!modalInstance"
	[ngClass]="{
		'first-quadrant': modalQuadrant === Quadrant.FIRST,
		'second-quadrant': modalQuadrant === Quadrant.SECOND,
		'third-quadrant': modalQuadrant === Quadrant.THIRD,
		'fourth-quadrant': modalQuadrant === Quadrant.FOURTH
	}"
	class="component-container">
	<div *ngIf="modalInstance" class="arrow-holder">
		<img class="arrow-img" src="assets/images/arrow-top.svg" />
	</div>
	<div class="component-content">
		<ng-container #modalWrapperContainerRef></ng-container>
	</div>
	<!--    <div class="arrow-holder" *ngIf="modalInstance && modalArrowBelow">-->
	<!--        <img class="arrow-img" src="assets/images/arrow-bottom.svg">-->
	<!--    </div>-->
</div>
