import {
	Component,
	OnInit,
	Input,
	ViewChild,
	ViewContainerRef,
	ComponentFactoryResolver,
	ChangeDetectionStrategy,
	HostListener,
	ElementRef,
	OnDestroy,
	ChangeDetectorRef,
	EventEmitter,
	Output,
	HostBinding,
} from "@angular/core";
import { Subscription } from "rxjs";
import { AgendaTimeGridStateService } from "../agenda-time-grid-state.service";
import { DiscreteGridSlot } from "../DiscreteGridManager";

interface Point2D {
	x: number;
	y: number;
}

export enum Quadrant {
	FIRST = "FIRST",
	SECOND = "SECOND",
	THIRD = "THIRD",
	FOURTH = "FOURTH",
}

@Component({
	selector: "pacto-time-grid-container",
	templateUrl: "./time-grid-container.component.html",
	styleUrls: ["./time-grid-container.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TimeGridContainerComponent implements OnInit, On<PERSON><PERSON>roy {
	@ViewChild("wrapperContainerRef", { read: ViewContainerRef, static: true })
	wrapperContainerRef;
	@ViewChild("hoverWrapperContainerRef", {
		read: ViewContainerRef,
		static: true,
	})
	hoverWrapperContainerRef;
	@ViewChild("modalWrapperContainerRef", {
		read: ViewContainerRef,
		static: true,
	})
	modalWrapperContainerRef;
	@Input() slot: DiscreteGridSlot;
	@Input() ID: number;
	@Input() context: any;
	@Input() column: any;

	instance;
	hoverInstance;
	modalInstance;
	quadrant: Quadrant;
	modalQuadrant: Quadrant;
	hoverTimeoutHandle;

	private closeAllSub: Subscription;
	private eventClickedSub: Subscription;
	private modalCloseSub: Subscription;

	constructor(
		private componentFactoryResolver: ComponentFactoryResolver,
		private agendaGridState: AgendaTimeGridStateService,
		private cd: ChangeDetectorRef,
		private ref: ElementRef
	) {}

	@HostBinding("style.z-index") zIndex;

	@HostListener("mouseenter", ["$event"])
	mouseHoverOnHandler($event: MouseEvent) {
		if (this.agendaGridState.modalOpen$.value) {
			return;
		}
		if (this.hoverTimeoutHandle) {
			clearTimeout(this.hoverTimeoutHandle);
		}
		this.hoverTimeoutHandle = setTimeout(() => {
			this.openTooltip();
		}, 50);
	}

	@HostListener("click", ["$event"])
	clickHandler($event: MouseEvent) {
		$event.stopPropagation();
		this.agendaGridState.eventClicked$.emit(this.ref);
		if (!this.modalInstance) {
			this.openModal();
		}
	}

	@HostListener("mouseleave", ["$event"])
	mouseHoverOutHandler($event: MouseEvent) {
		this.closeTooltip();
		if (this.hoverTimeoutHandle) {
			clearTimeout(this.hoverTimeoutHandle);
		}
	}

	get Quadrant() {
		return Quadrant;
	}

	get arrowAbove() {
		return (
			this.quadrant === Quadrant.FIRST || this.quadrant === Quadrant.SECOND
		);
	}

	get arrowBelow() {
		return (
			this.quadrant === Quadrant.THIRD || this.quadrant === Quadrant.FOURTH
		);
	}

	get modalArrowAbove() {
		return (
			this.modalQuadrant === Quadrant.FIRST ||
			this.modalQuadrant === Quadrant.SECOND
		);
	}

	get modalArrowBelow() {
		return (
			this.modalQuadrant === Quadrant.THIRD ||
			this.modalQuadrant === Quadrant.FOURTH
		);
	}

	ngOnInit() {
		this.eventClickedSub = this.agendaGridState.eventClicked$.subscribe(
			(ref) => {
				if (this.ref !== ref) {
					this.closeModal();
				}
			}
		);

		this.closeAllSub = this.agendaGridState.closeAllModal$.subscribe(() => {
			if (this.modalInstance) {
				this.closeModal();
			}
		});

		const component = this.slot.event.component;
		if (component) {
			const factory =
				this.componentFactoryResolver.resolveComponentFactory(component);
			this.wrapperContainerRef.clear();
			this.instance =
				this.wrapperContainerRef.createComponent(factory).instance;
			this.instance.slot = this.slot;
			this.instance.context = this.context;
			this.cd.detectChanges();
		}
	}

	ngOnDestroy() {
		this.closeAllSub.unsubscribe();
		this.eventClickedSub.unsubscribe();
		if (this.modalCloseSub) {
			this.modalCloseSub.unsubscribe();
		}
		if (this.hoverTimeoutHandle) {
			clearTimeout(this.hoverTimeoutHandle);
		}
	}

	closeModal($event?: MouseEvent) {
		if ($event) {
			$event.stopPropagation();
		}
		this.modalWrapperContainerRef.clear();
		this.modalInstance = null;
		this.agendaGridState.closeModal(this.ID);
		this.cd.detectChanges();
	}

	openModal() {
		const modalComponent = this.slot.event.modalComponent;
		if (modalComponent) {
			this.closeTooltip();

			// Emit event
			this.agendaGridState.openModal(this.ID, this.column);

			const factory =
				this.componentFactoryResolver.resolveComponentFactory(modalComponent);
			this.modalWrapperContainerRef.clear();
			this.modalInstance =
				this.modalWrapperContainerRef.createComponent(factory).instance;
			this.modalInstance.slot = this.slot;

			if (this.modalInstance.closeModal) {
				this.modalCloseSub = this.modalInstance.closeModal.subscribe(
					(mouseEvent) => {
						this.closeModal(mouseEvent);
					}
				);
			}
			const center = this.getCenterPointOfSlot();
			this.modalQuadrant = this.getPointQuadrant(center);
			this.cd.detectChanges();
		}
	}

	openTooltip() {
		const hoverComponent = this.slot.event.hoverComponent;
		if (hoverComponent) {
			const factory =
				this.componentFactoryResolver.resolveComponentFactory(hoverComponent);
			this.hoverWrapperContainerRef.clear();
			this.hoverInstance =
				this.hoverWrapperContainerRef.createComponent(factory).instance;
			this.hoverInstance.slot = this.slot;
			const center = this.getCenterPointOfSlot();
			this.quadrant = this.getPointQuadrant(center);
			this.cd.detectChanges();
		}
	}

	closeTooltip() {
		this.hoverWrapperContainerRef.clear();
		this.hoverInstance = null;
	}

	private getPointQuadrant(point: Point2D): Quadrant {
		const width = window.innerWidth;
		const height = window.innerHeight;
		if (point.x <= width / 2 && point.y <= height / 2) {
			return Quadrant.FIRST;
		} else if (point.x > width / 2 && point.y <= height / 2) {
			return Quadrant.SECOND;
		} else if (point.x >= width / 2 && point.y > height / 2) {
			return Quadrant.THIRD;
		} else {
			return Quadrant.FOURTH;
		}
	}

	/**
	 * Get the two-dimensional point relative to
	 * the beggining of the scrollable grid area of the agenda.
	 * Does not consider the column where the time increments
	 * reside.
	 */
	private getCenterPointOfSlot(): Point2D {
		const box: Element = this.ref.nativeElement;
		const rect = box.getBoundingClientRect();
		const point = {
			x: Math.trunc(rect.left + rect.width / 2),
			y: Math.trunc(rect.top + rect.height / 2),
		};
		point.x = point.x;
		point.y = point.y;
		return point;
	}
}
