import { DiscreteGridEvent } from "./DiscreteGridManager";

export class EventBlob {
	constructor() {
		this.events = [];
	}

	events: DiscreteGridEvent[];
	min: number;
	max: number;

	static mergeBlogs(blobs: EventBlob[]): EventBlob {
		let minMax: number;
		let maxMax: number;
		const events: DiscreteGridEvent[] = [];

		blobs.forEach((blob) => {
			// Finding min
			if (minMax === undefined || blob.min < minMax) {
				minMax = blob.min;
			}

			// Finding max
			if (maxMax === undefined || blob.max > maxMax) {
				maxMax = blob.max;
			}

			const copy = Object.assign([], blob.events);
			events.push(...copy);
		});

		const finalBlob = new EventBlob();
		finalBlob.min = minMax;
		finalBlob.max = maxMax;
		finalBlob.events = events;
		return finalBlob;
	}

	intersect(event: DiscreteGridEvent): boolean {
		if (!this.events.length) {
			return false;
		}
		const eventoInicio = event.start;
		const eventoFim = event.start + event.height;
		const blobInicio = this.min;
		const blobFim = this.max;
		const case_a = eventoInicio >= blobInicio && eventoInicio <= blobFim;
		const case_b = eventoFim >= blobInicio && eventoFim <= blobFim;
		const case_c = eventoInicio < blobInicio && eventoFim > blobFim;
		return case_a || case_b || case_c;
	}

	insert(event: DiscreteGridEvent) {
		if (this.min === undefined || event.start < this.min) {
			this.min = event.start;
		}
		if (this.max === undefined || event.start + event.height > this.max) {
			this.max = event.start + event.height;
		}
		this.events.push(event);
	}
}
