<div class="discrete-grid-wrapper">
	<ng-container *ngIf="clicavel">
		<div
			(click)="selectHour(item)"
			*ngFor="let item of blocos"
			class="hour-marker-click"></div>
	</ng-container>

	<ng-container *ngFor="let grid of gridManagers; let gridIndex = index">
		<ng-container
			*ngFor="let column of grid?.columns(); let columnIndex = index">
			<div
				*ngFor="let slot of column; let i = index"
				[ngClass]="{
					'pct-event-open': slot.event.ID === currentEventIdentifier
				}"
				[ngStyle]="{
					top: getTopOffset(slot) + 'px',
					left: getLeftOffsetAsPercent(columnIndex + 1, gridIndex) + '%',
					height: getHeight(slot) + 'px',
					width: getWidthAsPercent(gridIndex, columnIndex) + '%'
				}"
				class="discrete-agenda-event type-btn-small-bold"
				id="{{ slot.event.ID }}">
				<pacto-time-grid-container
					[ID]="slot.event.ID"
					[column]="dayColumn"
					[context]="context"
					[slot]="slot"></pacto-time-grid-container>
			</div>
		</ng-container>
	</ng-container>
</div>
<span #naoExisteDisp [hidden]="true" i18n="@@agenda:nao-existe-disponibilidade">
	Não existe disponibilidade para este horário.
</span>
