/**
 * Represents an discrete event positioned in a slot,
 * with a particular with, height and column and row.
 */
export class DiscreteGridSlot<P = any> {
	width: number;
	height: number;
	/**
	 * Position relative to the top left corner.
	 * Starting with 1.
	 */
	row: number;
	/**
	 * Position relative to the top left corner.
	 * Starting with 1.
	 */
	column: number;
	event: DiscreteGridEvent<P>;

	constructor(dto: any) {
		this.width = dto.width;
		this.height = dto.height;
		this.row = dto.row;
		this.column = dto.column;
		this.event = dto.event;
	}

	get end() {
		return this.row + this.height - 1;
	}

	overlapsWith(event: DiscreteGridEvent): boolean {
		const case_a = event.start >= this.row && event.start <= this.end;
		const case_b = event.end >= this.row && event.end <= this.end;
		const case_c = event.start < this.row && event.end > this.end;
		return case_a || case_b || case_c;
	}
}

export interface DiscreteGridEvent<P = any> {
	ID?: number;
	start: number;
	height: number;
	component?: any;
	payload?: P;
	hoverComponent?: any;
	modalComponent?: any;
}

export class DiscreteGridEvent {
	constructor(dto: Partial<DiscreteGridEvent>) {
		this.height = dto.height;
		this.start = dto.start;
		this.payload = dto.payload;
		this.component = dto.component;
		this.hoverComponent = dto.hoverComponent;
		this.modalComponent = dto.modalComponent;
	}

	get end() {
		return this.start + this.height - 1;
	}
}

export type DiscreteGridColumn = DiscreteGridSlot[];
export type DiscreteGrid = DiscreteGridColumn[];

export class DiscreteGridManager {
	private grid: DiscreteGrid = [];

	public insertEvent(event: DiscreteGridEvent) {
		if (this.grid.length === 0) {
			return this.insertIntoNewColumn(event);
		}

		/**
		 * Find column where it fits.
		 */
		let fits = -1;
		for (let column = 1; column <= this.nOfColumns; column++) {
			if (this.fitsInColumn(event, column)) {
				fits = column;
				break;
			}
		}

		if (fits === -1) {
			return this.insertIntoNewColumn(event);
		} else {
			return this.insertIntoColumn(event, fits);
		}
	}

	private insertIntoNewColumn(event: DiscreteGridEvent) {
		const column = this.grid.length;
		const slot = new DiscreteGridSlot({
			width: 1,
			event,
			height: event.height,
			row: event.start,
			column: column + 1,
		});
		this.grid.push([slot]);
	}

	/**
	 * Inserts event into a slot in the column. Mantains
	 * the adequate order of the slots.
	 *
	 * @warning Assumes it fits in the column
	 */
	private insertIntoColumn(event: DiscreteGridEvent, columnNumber) {
		const slot = new DiscreteGridSlot({
			width: 1,
			event,
			height: event.height,
			row: event.start,
			column: columnNumber,
		});
		const column = this.column(columnNumber);

		/**
		 * Three cases:
		 *
		 * a) new slot will be first
		 * b) new slot will be last
		 * c) new slot will be surronded by two other
		 */

		if (column.length === 0) {
			return column.push(slot);
		} else if (this.isFirst(event, columnNumber)) {
			return column.unshift(slot);
		} else if (this.isLast(event, columnNumber)) {
			return column.push(slot);
		} else {
			const slots = column;
			let targetIndex = null;
			for (let slotNumber = 1; slotNumber < slots.length - 1; slotNumber++) {
				const current = slots[slotNumber];
				const next = slots[slotNumber + 1];
				const afterCurrent = event.start > current.end;
				const beforeNext = event.end < next.row;
				if (afterCurrent && beforeNext) {
					targetIndex = slotNumber + 1;
					break;
				}
			}

			/**
			 * Splice new slot into proper position.
			 */
			slots.splice(targetIndex, 0, slot);
			return true;
		}
	}

	private isFirst(event: DiscreteGridEvent, columnNumber): boolean {
		const firstSlot = this.column(columnNumber)[0];
		return event.end < firstSlot.row;
	}

	private isLast(event: DiscreteGridEvent, columnNumber): boolean {
		const numberOfSlots = this.column(columnNumber).length;
		const lastSlot = this.column(columnNumber)[numberOfSlots - 1];
		return event.start > lastSlot.end;
	}

	/**
	 * Event fits if there is no overlap with any of the
	 * events.
	 *
	 * @param index - One indexed
	 */
	private fitsInColumn(event: DiscreteGridEvent, column): boolean {
		const slots: DiscreteGridColumn = this.column(column);
		return slots.every((slot) => {
			return !slot.overlapsWith(event);
		});
	}

	columns(): DiscreteGridColumn[] {
		return this.grid;
	}

	get nOfColumns() {
		if (this.grid) {
			return this.grid.length;
		} else {
			return 0;
		}
	}

	/**
	 * One indexed.
	 */
	column(column): DiscreteGridColumn {
		return this.grid[column - 1];
		return null;
	}
}
