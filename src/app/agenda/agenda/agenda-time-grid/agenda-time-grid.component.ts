import {
	Component,
	OnInit,
	Input,
	SimpleChanges,
	OnChanges,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Output,
	EventEmitter,
	HostListener,
	ViewChild,
} from "@angular/core";
import {
	DiscreteGridManager,
	DiscreteGridSlot,
	DiscreteGridEvent,
} from "./DiscreteGridManager";
import { TurmaCardComponent } from "../agenda-turma/turma-card/turma-card.component";
import { AgendaTimeGridStateService } from "./agenda-time-grid-state.service";
import { EventBlob } from "./EventBlob";
import { AgendamentoCreateModalComponent } from "../agenda-servicos/agendamento-create-modal/agendamento-create-modal.component";
import { ModalService } from "@base-core/modal/modal.service";
import { AgendaServicosStateService } from "../agenda-servicos/agenda-servicos-state.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SessionService } from "@base-core/client/session.service";

/**
 * Grids elements of width 1u (unit) and height 'x'
 * where 'x' is an integer.
 */
@Component({
	// tslint:disable-next-line:component-selector
	selector: "[pacto-agenda-time-grid]",
	templateUrl: "./agenda-time-grid.component.html",
	styleUrls: ["./agenda-time-grid.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaTimeGridComponent implements OnInit, OnChanges {
	@Input() events: DiscreteGridEvent[] = [];
	@Input() context: any = {};
	@Input() dayColumn: number;
	@Input() day: any;
	@Input() clicavel = true;
	@ViewChild("naoExisteDisp", { static: true }) naoExisteDisp;
	gridManagers: DiscreteGridManager[] = [];
	blocos = Array(24).fill(0);
	componentTypeExpression = TurmaCardComponent;
	@Output() reloadEvent: EventEmitter<any> = new EventEmitter();

	private idCounter = 1;

	ngOnInit() {
		this.agendaGridState.modalOpen$.subscribe(() => {
			this.cd.detectChanges();
		});
	}

	constructor(
		private cd: ChangeDetectorRef,
		private modalService: ModalService,
		private agendaGridState: AgendaTimeGridStateService,
		private state: AgendaServicosStateService,
		private snotifyService: SnotifyService,
		private session: SessionService
	) {
		this.blocos.forEach((v, i) => {
			this.blocos[i] = i;
		});
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes.events && changes.events.currentValue) {
			this.makeGrid(changes.events.currentValue);
			setTimeout(() => {
				this.cd.detectChanges();
			});
		}
	}

	get currentEventIdentifier() {
		if (this.agendaGridState.modalOpen$.value) {
			return this.agendaGridState.modalOpen$.value.id;
		} else {
			return -1;
		}
	}

	getEventID() {
		const id = this.idCounter + 1;
		this.idCounter = this.idCounter + 1;
		return id;
	}

	validarPermissaoIncluirTiposAgendamentos(tipos) {
		const retorno = [];
		tipos.forEach((tipo) => {
			if (tipo.comportamentoEnum === "AVALIACAO_FISICA") {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.AVALIACAO_FISICA
				);
				if (permissao !== undefined && permissao.incluir) {
					retorno.push(tipo);
				}
			} else if (tipo.comportamentoEnum === "CONTATO_INTERPESSOAL") {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.CONTATO_INTERPESSOAL
				);
				if (permissao !== undefined && permissao.incluir) {
					retorno.push(tipo);
				}
			} else if (tipo.comportamentoEnum === "PRESCRICAO_TREINO") {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.PRESCRICAO_TREINO
				);
				if (permissao !== undefined && permissao.incluir) {
					retorno.push(tipo);
				}
			} else if (tipo.comportamentoEnum === "RENOVAR_TREINO") {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.RENOVAR_TREINO
				);
				if (permissao !== undefined && permissao.incluir) {
					retorno.push(tipo);
				}
			} else if (tipo.comportamentoEnum === "REVISAO_TREINO") {
				const permissao = this.session.recursos.get(
					PerfilAcessoRecursoNome.REVISAO_TREINO
				);
				if (permissao !== undefined && permissao.incluir) {
					retorno.push(tipo);
				}
			}
		});
		if (retorno.length === 0 && tipos.length > 0) {
			retorno.push(-1);
		}
		return retorno;
	}

	selectHour(hora) {
		this.state.obterTiposDisponiveis(this.day, hora).subscribe((tipos) => {
			tipos = this.validarPermissaoIncluirTiposAgendamentos(tipos);
			const tiposEventos = tipos.map((tipo) => {
				return {
					id: tipo.id,
					nome: tipo.nome,
					tipoEvento: tipo.tipoEvento,
				};
			});
			if (tiposEventos && tiposEventos.length > 0 && tipos[0] !== -1) {
				const modal = this.modalService.open(
					"Adicionar Agendamento",
					AgendamentoCreateModalComponent
				);
				modal.componentInstance.dia = this.day;
				modal.componentInstance.hora = hora;
				modal.componentInstance.disponibilidade = {
					tiposAtividades: tiposEventos,
				};
				modal.result.then(
					(dto) => {
						this.reloadEvent.emit();
					},
					() => {}
				);
			} else if (tipos[0] === -1) {
				this.snotifyService.warning(
					"Seu usuário não possui permissão, procure seu administrador"
				);
			} else {
				const naoExisteDisp = this.naoExisteDisp.nativeElement.innerHTML;
				this.snotifyService.error(naoExisteDisp);
			}
		});
	}

	private separateIntoBlobs(events: DiscreteGridEvent[]): EventBlob[] {
		let blobs: EventBlob[] = [];

		events.forEach((event) => {
			const intersections = [];
			blobs.forEach((blob, index) => {
				if (blob.intersect(event)) {
					intersections.push(index);
				}
			});

			// Sem interseção
			if (intersections.length === 0) {
				const newBlob = new EventBlob();
				newBlob.insert(event);
				blobs.push(newBlob);
			}

			// Apenas um
			if (intersections.length === 1) {
				blobs[intersections[0]].insert(event);
			}

			// Dois ou mais
			if (intersections.length >= 2) {
				blobs[intersections[0]].insert(event);
				const intersectedBlobs = blobs.filter((blob, index) =>
					intersections.includes(index)
				);
				const mergedBlob = EventBlob.mergeBlogs(intersectedBlobs);
				blobs = blobs.filter((blob, index) => !intersections.includes(index));
				blobs.push(mergedBlob);
			}
		});

		return blobs;
	}

	private makeGrid(events) {
		this.gridManagers = [];
		this.idCounter = 0;
		if (!events || !events.length) {
			return;
		}
		const blobs = this.separateIntoBlobs(events);
		blobs.forEach((blob) => {
			const gridManager = new DiscreteGridManager();
			blob.events.forEach((event) => {
				event.ID = this.getEventID();
				gridManager.insertEvent(event);
			});
			this.gridManagers.push(gridManager);
		});
	}

	getTopOffset(slot: DiscreteGridSlot) {
		return slot.row * 15;
	}

	getLeftOffsetAsPercent(columnIndex: number, gridIndex) {
		return (columnIndex - 1) * this.getBaseWidth(gridIndex);
	}

	getHeight(slot: DiscreteGridSlot) {
		return slot.height * 15;
	}

	getBaseWidth(gridIndex) {
		const numberOfColumns = this.gridManagers[gridIndex].nOfColumns;
		return 100 / numberOfColumns;
	}

	getWidthAsPercent(gridIndex, columnIndex) {
		const base = this.getBaseWidth(gridIndex);
		const numberOfColumns = this.gridManagers[gridIndex].nOfColumns;
		const last = columnIndex + 1 === numberOfColumns;
		return base * (last ? 1.0 : 1.6);
	}
}
