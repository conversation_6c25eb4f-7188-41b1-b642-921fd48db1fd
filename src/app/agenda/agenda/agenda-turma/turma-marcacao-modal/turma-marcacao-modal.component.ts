import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

declare var moment;

@Component({
	selector: "pacto-turma-marcacao-modal",
	templateUrl: "./turma-marcacao-modal.component.html",
	styleUrls: ["./turma-marcacao-modal.component.scss"],
})
export class TurmaMarcacaoModalComponent implements OnInit {
	@Input() aluno;
	@Input() turma;
	@Input() aulasMarcada;
	@Input() saldoCredito;
	@Input() marcacao: boolean;

	get dia() {
		return moment(this.turma.dia).toDate();
	}

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	save() {
		this.openModal.close();
	}
}
