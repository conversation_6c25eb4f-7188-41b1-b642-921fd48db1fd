<div class="row-info-aluno">
	<pacto-cat-person-avatar
		[diameter]="65"
		[uri]="aluno.imageUri"></pacto-cat-person-avatar>
	<div class="dados-aluno">
		<div class="type-h5">{{ aluno?.nome }}</div>
		<div class="type-caption">MAT: {{ aluno?.matriculaZW }}</div>
	</div>
</div>
<div class="row-info">
	<div>
		<div class="type-h6">Aula de marcação</div>
		<div class="text-style-2">{{ dia | date : "shortDate" }}</div>
	</div>
	<div *ngIf="marcacao">
		<div class="type-h6">Aulas já marcadas</div>
		<div class="text-style-2">{{ aulasMarcada }}</div>
	</div>
</div>
<div class="block-info">
	<div class="type-h6"><PERSON><PERSON> de créditos</div>
	<div class="text-style-2">{{ saldoCredito }}</div>
</div>
<div class="block-info">
	<pacto-cat-button
		(click)="save()"
		[full]="true"
		[label]="'ok'"></pacto-cat-button>
</div>
