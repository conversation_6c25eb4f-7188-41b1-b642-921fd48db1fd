import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@angular/core";

import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { TurmaZW } from "treino-api";

export enum Quadrant {
	FIRST = "FIRST",
	SECOND = "SECOND",
	THIRD = "THIRD",
	FOURTH = "FOURTH",
}

@Component({
	selector: "pacto-turma-card-detail",
	templateUrl: "./turma-card-detail.component.html",
	styleUrls: ["./turma-card-detail.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaCardDetailComponent implements OnInit, OnDestroy {
	@Input() slot: DiscreteGridSlot;

	constructor() {}

	ngOnInit() {}

	ngOnDestroy() {}

	get turma(): TurmaZW {
		if (this.slot && this.slot.event) {
			return this.slot.event.payload as Turma<PERSON><PERSON>;
		} else {
			return null;
		}
	}

	click<PERSON><PERSON><PERSON>(event: MouseEvent) {
		event.stopPropagation();
	}

	get professorNome() {
		if (this.turma.professorSubstituto) {
			return this.turma.professorSubstituto.nome;
		} else if (this.turma.professor) {
			return this.turma.professor.nome;
		} else {
			return "";
		}
	}
}
