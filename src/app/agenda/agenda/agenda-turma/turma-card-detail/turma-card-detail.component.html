<div (click)="clickHandler($event)" *ngIf="turma" class="wrapper">
	<div class="header-row">
		<div class="detail-title">{{ turma?.nome }}</div>
	</div>

	<div class="body-wrapper">
		<div class="info-bit">
			<div class="info-value">
				<i class="pct pct-user"></i>
				<span [max]="25" [text]="professorNome" pacto-cat-text></span>
			</div>
		</div>
		<div class="info-bit">
			<div class="info-value">
				<i class="pct pct-clock"></i>
				{{ turma?.horarioInicio }} ás {{ turma?.horarioFim }}
			</div>
		</div>
		<div class="info-bit">
			<div class="info-value">
				<i class="pct pct-bar-chart"></i>
				{{ turma?.numeroAlunos }} / {{ turma?.capacidade }}
			</div>
		</div>
		<div class="info-bit">
			<div class="info-value">
				<i class="pct pct-map-pin"></i>
				{{ turma?.ambiente?.nome }}
			</div>
		</div>
	</div>
</div>
