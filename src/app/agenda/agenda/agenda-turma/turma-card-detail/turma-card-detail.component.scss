@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.wrapper {
	overflow: hidden;

	.header-row {
		border-bottom: 1px solid $cinzaClaro02;
		color: $pretoPri;
		position: relative;

		.detail-title {
			@extend .type-btn-bold;
			line-height: 30px;
			padding: 0px 10px;
			white-space: nowrap;
		}
	}

	.body-wrapper {
		padding: 10px;
	}

	.info-bit {
		margin: 2px 0px;

		.pct {
			font-size: 14px;
			color: $azulimPri;
			position: relative;
			top: 1px;
			padding-right: 5px;
		}

		.info-value {
			@extend .type-caption;
			color: $pretoPri;
			white-space: nowrap;
		}
	}
}
