import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-espera-desmarcar-modal",
	templateUrl: "./espera-desmarcar-modal.component.html",
	styleUrls: ["./espera-desmarcar-modal-component..scss"],
})
export class EsperaDesmarcarModalComponent implements OnInit {
	@Input() aluno;
	@Input() espera;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	confirm() {
		this.openModal.close(true);
	}
}
