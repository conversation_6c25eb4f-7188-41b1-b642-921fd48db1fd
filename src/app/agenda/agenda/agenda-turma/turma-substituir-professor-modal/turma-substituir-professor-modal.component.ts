import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { ColaboradorSelect } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

@Component({
	selector: "pacto-turma-substituir-professor-modal",
	templateUrl: "./turma-substituir-professor-modal.component.html",
	styleUrls: ["./turma-substituir-professor-modal.component.scss"],
})
export class TurmaSubstituirProfessorModalComponent implements OnInit {
	@Input() professores: Array<ColaboradorSelect>;

	@ViewChild("camposObrigatorio", { static: true })
	camposObrigatorio: TraducoesXinglingComponent;

	formGroup: FormGroup = new FormGroup({
		professorId: new FormControl(null, [Validators.required]),
		justificativa: new FormControl("", [Validators.required]),
	});

	constructor(
		private openModal: NgbActiveModal,
		private snotifyService: SnotifyService,
		private sessionService: SessionService
	) {}

	ngOnInit() {}

	save() {
		if (this.formGroup.valid) {
			this.openModal.close(this.formGroup.getRawValue());
			this.sessionService.notificarRecursoEmpresa(
				RecursoSistema.SUBSTITUIU_PROFESSOR_AULACOLETIVA_NTO
			);
		} else {
			this.snotifyService.error(
				this.camposObrigatorio.getLabel("camposObrigatorio")
			);
		}
	}
}
