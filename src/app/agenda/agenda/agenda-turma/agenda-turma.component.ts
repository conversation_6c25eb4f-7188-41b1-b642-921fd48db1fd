import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";

import { AgendaTurmaStateService } from "../services/agenda-turma-state.service";
import { AgendaView } from "treino-api";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-agenda-turma",
	templateUrl: "./agenda-turma.component.html",
	styleUrls: ["./agenda-turma.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [AgendaTurmaStateService],
})
export class AgendaTurmaComponent implements OnInit {
	constructor(
		private agendaState: AgendaTurmaStateService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.ACESSO_AGENDA_AULAS_NTR
		);
	}

	get dia() {
		return this.agendaState.view === AgendaView.DIA;
	}

	get semana() {
		return this.agendaState.view === AgendaView.SEMANA;
	}
}
