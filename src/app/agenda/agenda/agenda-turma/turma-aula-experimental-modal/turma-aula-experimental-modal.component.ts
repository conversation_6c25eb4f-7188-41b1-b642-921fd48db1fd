import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { TraducoesXinglingComponent } from "ui-kit";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

export enum TipoProdutoZW {
	FREEPASS = "FREEPASS",
	DIARIA = "DIARIA",
}

@Component({
	selector: "pacto-turma-aula-experimental-modal",
	templateUrl: "./turma-aula-experimental-modal.component.html",
	styleUrls: ["./turma-aula-experimental-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaAulaExperimentalModalComponent implements OnInit {
	@Input() aluno;
	produtosZW: Array<any> = [];
	@ViewChild("tipoProdutoTraducao", { static: true })
	tipoProdutoTraducao: TraducoesXinglingComponent;
	@ViewChild("validacoesTraducao", { static: true })
	validacoesTraducao: TraducoesXinglingComponent;

	produtoZWControl: FormControl = new FormControl();
	tipoProdutoZWControl: FormControl = new FormControl();
	tiposProdutoZW: Array<{ id: string; nome: string }> = [];
	produtosZWSelect: Array<{ id: string; nome: string }> = [];
	bodyAulaCheiaExperimental: string;

	constructor(
		private openModal: NgbActiveModal,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.tipoProdutoZWControl.valueChanges.subscribe(() => {
			this.setSelectProdutoZw();
		});
		this.tipoProdutoZWControl.setValue(TipoProdutoZW.FREEPASS);
		this.cd.detectChanges();
	}

	save() {
		if (this.bodyAulaCheiaExperimental) {
			this.openModal.close();
		} else if (this.produtoZWControl.value) {
			this.openModal.close(this.produtoZWControl.value);
		} else {
			this.snotifyService.error(
				this.validacoesTraducao.getLabel("produtoObrigatorio")
			);
		}
	}

	carregarProdutos(produtos, tiposProduto: Array<any>) {
		this.produtosZW = produtos;
		setTimeout(() => {
			tiposProduto.filter((tipoProduto) => {
				tipoProduto.nome = this.tipoProdutoTraducao.getLabel(tipoProduto.id);
			});
			this.tiposProdutoZW = tiposProduto;
			this.cd.detectChanges();
		});
	}

	carregarBodyAulaCheiaExperimental(conteudo: string) {
		this.bodyAulaCheiaExperimental = conteudo;
	}

	private setSelectProdutoZw() {
		this.produtosZWSelect = [];
		this.produtosZW.forEach((produto) => {
			if (produto.tipo === this.tipoProdutoZWControl.value) {
				this.produtosZWSelect.push({ id: produto.id, nome: produto.nome });
			}
		});
		if (this.produtosZWSelect && this.produtosZWSelect.length > 0) {
			this.produtoZWControl.setValue(this.produtosZWSelect[0].id);
		} else {
			this.produtoZWControl.setValue(null);
		}
		this.cd.detectChanges();
	}
}
