<div class="row-info">
	<pacto-cat-person-avatar
		[diameter]="65"
		[uri]="aluno.imageUri"></pacto-cat-person-avatar>
	<div class="dados-aluno">
		<div class="type-h5">{{ aluno?.nome }}</div>
		<div class="type-caption">MAT: {{ aluno?.matriculaZW }}</div>
	</div>
</div>
<div *ngIf="!bodyAulaCheiaExperimental" class="block-info">
	<pacto-cat-select
		[control]="tipoProdutoZWControl"
		[idKey]="'id'"
		[items]="tiposProdutoZW"
		[labelKey]="'nome'"
		i18n-label="@@agenda-detalhes-experimental-modal:tipoProduto"
		label="Tipo de produto"></pacto-cat-select>
	<pacto-cat-select
		[control]="produtoZWControl"
		[idKey]="'id'"
		[items]="produtosZWSelect"
		[labelKey]="'nome'"
		i18n-label="@@agenda-detalhes-experimental-modal:produto"
		label="Produto"></pacto-cat-select>
</div>
<div *ngIf="bodyAulaCheiaExperimental" class="block-info">
	{{ bodyAulaCheiaExperimental }}
</div>
<div class="block-info">
	<pacto-cat-button
		(click)="save()"
		[full]="true"
		[label]="
			bodyAulaCheiaExperimental
				? traducoes.getLabel('confirmar')
				: traducoes.getLabel('salvar')
		"></pacto-cat-button>
</div>

<pacto-traducoes-xingling #tipoProdutoTraducao>
	<span i18n="@@agenda-detalhes-experimental-modal:DIARIA" xingling="DIARIA">
		Diaria
	</span>
	<span
		i18n="@@agenda-detalhes-experimental-modal:FREEPASS"
		xingling="FREEPASS">
		FreePass
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@agenda-detalhes-experimental-modal:confirmar"
		xingling="confirmar">
		confirmar
	</span>
	<span i18n="@@agenda-detalhes-experimental-modal:salvar" xingling="salvar">
		salvar
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #validacoesTraducao>
	<span
		i18n="@@agenda-detalhes-experimental-modal:produtoObrigatorio"
		xingling="produtoObrigatorio">
		Ops! O campo produto é obrigatório.
	</span>
</pacto-traducoes-xingling>
