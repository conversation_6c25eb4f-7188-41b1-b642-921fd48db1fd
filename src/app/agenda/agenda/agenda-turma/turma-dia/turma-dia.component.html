<ng-container *ngIf="!turmaDetalhes">
	<pacto-agenda-control
		(calendarState)="calendarStateChange($event)"></pacto-agenda-control>
	<div class="apanel-wrapper">
		<div *ngIf="overlay" class="apanel-overlay"></div>
		<div
			[ngClass]="{
				'loading-blur': loading
			}"
			class="current-date">
			<div class="weekday">{{ date | date : "EEEE" }}</div>
			<div class="day">{{ date | date : "dd" }}</div>
		</div>

		<!-- CONTENT -->
		<div
			#painelScroll
			[ngClass]="{ 'loading-blur': loading }"
			class="content-wrapper agenda-scroll-container"
			id="painelScroll">
			<div class="inner-wrapper-container">
				<div [horaFinal]="23" [horaInicial]="1" pacto-agenda-time-axis></div>
				<div class="time-grid-wrapper">
					<!-- Events -->
					<div
						[clicavel]="false"
						[context]="gridContext"
						[events]="_events"
						class="time-grid"
						pacto-agenda-time-grid></div>

					<!-- Time markings -->
					<div [ngStyle]="{ width: '100%' }" class="dia-semana-coluna">
						<div class="hour-marker-wrapper">
							<div *ngFor="let item of hourArray" class="hour-marker"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</ng-container>

<ng-container *ngIf="turmaDetalhes">
	<pacto-agenda-turma-view
		(closed)="turmaDetalhes = null"
		[turma]="turmaDetalhes"></pacto-agenda-turma-view>
</ng-container>
