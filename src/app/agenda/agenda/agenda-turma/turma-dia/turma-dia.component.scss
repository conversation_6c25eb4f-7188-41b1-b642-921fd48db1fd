@import "src/assets/scss/pacto/plataforma-import.scss";

$grey-line: #dadada;

.apanel-wrapper {
	position: relative;

	.apanel-overlay {
		position: absolute;
		left: 0px;
		top: 0px;
		right: 0px;
		bottom: 0px;
		z-index: 2;
		opacity: 0.5;
		background-color: $pretoPri;
	}
}

.loading-blur {
	filter: blur(2px);
	pointer-events: none;
}

.agenda-wrapper {
	height: 100%;
}

.current-date {
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1px solid $gelo02;

	.weekday {
		@extend .type-caption;
		text-transform: uppercase;
		color: $gelo02;
	}

	.day {
		@extend .type-h3;
		color: $pretoPri;
		margin: 0px 15px;
	}
}

.content-wrapper {
	width: 100%;
	display: flex;
}

.inner-wrapper-container {
	display: flex;
	width: 100%;
}

.time-grid-wrapper {
	flex-grow: 1;
}

.agenda-time-grid {
	top: 0px;
	left: 0px;
	right: 0px;
}

.hour-marker {
	width: 100%;
	height: 30px;
	border-bottom: 1px solid $grey-line;

	&:nth-child(2n + 1) {
		border-bottom: 1px dashed $grey-line;
	}
}

.hour-marker-wrapper {
	top: 0px;
	left: 0px;
	right: 0px;
}

.agenda-scroll-container {
	max-height: calc(100vh - 248px);
	overflow: auto;
}

::-webkit-scrollbar {
	width: 5px;
}

::-webkit-scrollbar-track {
	top: 0;
	right: 0;
	width: 8px;
	height: 100%;
	position: absolute;
	opacity: 0;
	z-index: 1;
	background: rgba(222, 222, 222, 0.75);
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-transition: opacity 0.5s 0.5s ease-out;
	transition: opacity 0.5s 0.5s ease-out;
}

::-webkit-scrollbar-thumb {
	position: absolute;
	top: 0;
	left: 0;
	width: 8px;
	height: 8px;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}
