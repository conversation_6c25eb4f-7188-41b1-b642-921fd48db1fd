import {
	Component,
	OnInit,
	ViewChild,
	ChangeDetector<PERSON>ef,
	OnDestroy,
	ChangeDetectionStrategy,
	EventEmitter,
} from "@angular/core";
import { AgendaTurmaStateService } from "../../services/agenda-turma-state.service";
import { DiscreteGridEvent } from "../../agenda-time-grid/DiscreteGridManager";
import { Aula } from "../../services/agenda-turma-state.model";

import { TurmaZW } from "treino-api";
import { Subscription } from "rxjs";
import { TurmaCardDetailComponent } from "../turma-card-detail/turma-card-detail.component";
import { TurmaCardComponent } from "../turma-card/turma-card.component";

declare var moment;

@Component({
	selector: "pacto-turma-dia",
	templateUrl: "./turma-dia.component.html",
	styleUrls: ["./turma-dia.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaDiaComponent implements On<PERSON>nit, OnD<PERSON>roy {
	@ViewChild("columnWrapper", { static: false }) columnWrapper;
	@ViewChild("timeAxis", { static: false }) timeAxis;
	@ViewChild("painelScroll", { static: false }) painelScroll;

	_events = [];
	turmaDetalhes: TurmaZW;
	subscriptions: Subscription[] = [];
	loading = false;
	overlay;

	gridContext = {
		selectTurma$: new EventEmitter(),
	};

	constructor(
		private agendaStateService: AgendaTurmaStateService,
		private cd: ChangeDetectorRef
	) {}

	get date() {
		const momentDia = moment(this.agendaStateService.dia, "YYYYMMDD");
		return momentDia.valueOf();
	}

	calendarStateChange(state) {
		this.overlay = state;
		this.cd.detectChanges();
	}

	ngOnInit() {
		const sub = this.agendaStateService.stateUpdate$.subscribe((loading) => {
			this.loading = loading;
			if (loading === false) {
				const day = this.agendaStateService.dia;
				const aulas = this.agendaStateService.aulas[day];
				this.setScroll(this.buscaProximaAula(aulas).menor);
				if (aulas) {
					aulas.sort((aula1, aula2) =>
						aula1.nome < aula2.nome ? -1 : aula1.nome > aula2.nome ? 1 : 0
					);
					this._events = this.convertTurmasIntoDiscreteGridEvent(aulas);
				} else {
					this._events = [];
				}

				if (this.turmaDetalhes) {
					const found = this.agendaStateService.getTurmaById(
						this.turmaDetalhes.horarioTurmaId
					);
					this.turmaDetalhes = found;
				}
			}
			this.cd.detectChanges();
		});
		this.subscriptions.push(sub);

		this.subscriptions.push(
			this.gridContext.selectTurma$.subscribe((turma) => {
				this.turmaDetalhes = turma;
			})
		);
	}

	ngOnDestroy() {
		this.subscriptions.forEach((subscription) => {
			subscription.unsubscribe();
		});
	}

	get hourArray() {
		return new Array(48).fill(0);
	}

	private convertTurmasIntoDiscreteGridEvent(
		aulas: Aula[]
	): DiscreteGridEvent[] {
		return aulas.map((item) => {
			return this.agendaStateService.convertTurmaIntoGrid(
				item,
				TurmaCardComponent,
				TurmaCardDetailComponent
			);
		});
	}

	setScroll(height) {
		if (this.painelScroll !== undefined) {
			this.painelScroll.nativeElement.scrollTop = height;
		}
	}

	convHoras(horarioInicio) {
		const hora = parseInt(horarioInicio.slice(0, 2), 10);
		const minutos = parseInt(horarioInicio.slice(3, 5), 10);
		return hora * 60 + minutos - 40; // 40 pixeis para ficar bonito
	}

	buscaProximaAula(aulas) {
		let menor = 1000000; // numero grande nao alcancavel
		let menorPos;
		let aux;
		for (let i = 0; i < aulas.length; i++) {
			aux = this.convHoras(aulas[i].horarioInicio);
			if (aux < menor) {
				menor = aux;
				menorPos = i;
			}
		}
		if (menor === 1000000) {
			menorPos = menor = 0;
			return { menorPos, menor }; // retorna no inicio
		}
		return { menorPos, menor };
	}
}
