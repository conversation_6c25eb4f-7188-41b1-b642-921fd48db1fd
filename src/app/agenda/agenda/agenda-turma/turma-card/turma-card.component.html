<div
	#boudingBox
	(click)="clickHandler()"
	[ngClass]="{
		full: turma?.numeroAlunos >= turma?.capacidade || turma?.bloqueado
	}"
	class="wrapper">
	<ng-container #container></ng-container>

	<div
		[ngClass]="{
			full: turma?.numeroAlunos >= turma?.capacidade
		}"
		[ngStyle]="{
			'background-color': turma.modalidade.cor
		}"
		class="slip-marker"></div>

	<div *ngIf="small" class="small">
		<div class="modalidade-nome type-btn-small-bold">
			{{ turma?.nome }} -
			<span class="type-overline">
				{{ turma?.numeroAlunos }} / {{ turma?.capacidade }}
			</span>
		</div>
	</div>

	<div *ngIf="medium" class="medium">
		<div class="modalidade-nome type-btn-small-bold">
			{{ turma?.nome }}
		</div>
		<div class="type-overline">
			{{ turma?.numeroAlunos }} / {{ turma?.capacidade }}
		</div>
	</div>

	<div *ngIf="large" class="large">
		<div class="dados">
			<span class="type-btn-bold">{{ turma?.nome }}</span>
			<div class="type-overline">
				{{
					turma?.professorSubstituto
						? turma.professorSubstituto.nome
						: turma?.professor?.nome
				}}
			</div>
			<div class="type-overline">
				{{ turma?.numeroAlunos }} / {{ turma?.capacidade }}
			</div>
		</div>
	</div>
</div>
