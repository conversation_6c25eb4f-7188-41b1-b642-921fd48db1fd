@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	width: 100%;
	height: 100%;
	display: block;
}

.wrapper {
	position: relative;
	height: 100%;
	width: 100%;
	display: flex;
	background-color: $geloPri;
	border-radius: 4px;
	border: 1px solid $gelo02;
	color: $preto01;
	cursor: pointer;

	&.full {
		& * {
			color: $branco !important;
		}

		background-color: $hellboyPri;
	}
}

.slip-marker {
	width: 4px;
	height: 100%;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
	flex-shrink: 0;

	&.full {
		background-color: $hellboyPri !important;
	}
}

.modalidade-nome {
	white-space: nowrap;
}

.small {
	display: flex;
	align-items: center;
	padding-left: 5px;
	overflow: hidden;
	white-space: nowrap;
}

.medium {
	white-space: nowrap;
	padding-left: 5px;
	overflow: hidden;
}

.large {
	white-space: nowrap;
	overflow: hidden;
	margin-left: 5px;
	margin-top: 5px;
	display: flex;

	pacto-cat-person-avatar {
		display: block;
		margin: 0px 5px;
	}
}
