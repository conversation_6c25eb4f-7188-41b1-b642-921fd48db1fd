import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ViewContainerRef,
	ViewChild,
	ElementRef,
	Input,
	EventEmitter,
	HostBinding,
} from "@angular/core";
import { DiscreteGridSlot } from "../../agenda-time-grid/DiscreteGridManager";
import { TurmaZW, TreinoApiTurmaService } from "treino-api";
import { AgendaTurmaStateService } from "../../services/agenda-turma-state.service";

interface Point2D {
	x: number;
	y: number;
}

@Component({
	selector: "pacto-turma-card",
	templateUrl: "./turma-card.component.html",
	styleUrls: ["./turma-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaCardComponent implements OnInit {
	@ViewChild("container", { read: ViewContainerRef, static: true }) container;
	@ViewChild("boudingBox", { static: true }) boudingBox: ElementRef;
	@Input() slot: DiscreteGridSlot;
	@Input() context: { selectTurma$: EventEmitter<any> };

	@HostBinding("class") identifyTeste: string;

	constructor(
		private turmaStateService: AgendaTurmaStateService,
		private turmaService: TreinoApiTurmaService
	) {}

	ngOnInit() {
		this.identifyTeste =
			(this.turma.aulaCheia ? "aula-" : "turma-") +
			(this.turma.nome + "-" + this.turma.horarioInicio.replace(":", ""));
	}

	get turma(): TurmaZW {
		return this.slot.event.payload;
	}

	/**
	 * 15 minutes
	 */
	get small() {
		return this.slot.height === 1;
	}

	/**
	 * 30/45 minutes
	 */
	get medium() {
		return !this.small && this.slot.height <= 3;
	}

	/**
	 * 1 hour
	 */
	get large() {
		return this.slot.height >= 4;
	}

	clickHandler() {
		this.turmaStateService.startLoad();
		this.turmaService
			.aulaDetalhada(this.turma.horarioTurmaId, this.turma.dia)
			.subscribe((response) => {
				if (this.context && this.context.selectTurma$) {
					this.context.selectTurma$.emit(response);
				}
				this.turmaStateService.updateTurma(response);
				this.turmaStateService.stopLoad();
			});
	}
}
