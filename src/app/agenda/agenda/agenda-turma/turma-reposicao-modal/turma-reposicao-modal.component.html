<div class="row-info">
	<pacto-cat-person-avatar
		[diameter]="65"
		[uri]="aluno.imageUri"></pacto-cat-person-avatar>
	<div class="dados-aluno">
		<div class="type-h5">{{ aluno?.nome }}</div>
		<div class="type-caption">MAT: {{ aluno?.matriculaZW }}</div>
	</div>
</div>
<div class="block-info">
	<div class="type-h6">Aula de reposição</div>
	<div class="text-style-2">{{ dia | date : "shortDate" }}</div>
</div>
<div *ngIf="aulasDesmarcada?.length" class="block-info">
	<div class="type-h6">Aulas desmarcadas</div>
	<pacto-cat-select
		[control]="control"
		[idKey]="'id'"
		[items]="aulasDesmarcada"
		[labelKey]="'nome'"></pacto-cat-select>
</div>
<div
	*ngIf="!aulasDesmarcada || aulasDesmarcada?.length === 0"
	class="block-info">
	<pacto-cat-select
		*ngIf="aulasParaDesmarcarDia?.length > 0"
		[control]="aulaASerDesmarcada"
		[idKey]="'id'"
		[items]="aulasParaDesmarcarDia"
		[labelKey]="'nome'"
		[label]="'Aula a ser desmarcada'"></pacto-cat-select>
	<div
		*ngIf="
			(!aulasDesmarcada || aulasDesmarcada?.length === 0) &&
			(!aulasParaDesmarcarDia || aulasParaDesmarcarDia?.length === 0)
		"
		class="info-not-aula">
		O aluno não possui nenhuma aula para repor ou desmarcar.
	</div>
</div>
<div class="block-info">
	<pacto-cat-button
		(click)="save()"
		[disabled]="
			(!aulasDesmarcada || aulasDesmarcada?.length === 0) &&
			(!aulasParaDesmarcarDia || aulasParaDesmarcarDia?.length === 0)
		"
		[full]="true"
		[label]="'salvar'"></pacto-cat-button>
</div>
