import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiTurmaService } from "treino-api";

declare var moment;

@Component({
	selector: "pacto-turma-reposicao-modal",
	templateUrl: "./turma-reposicao-modal.component.html",
	styleUrls: ["./turma-reposicao-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaReposicaoModalComponent implements OnInit {
	@Input() aluno;
	@Input() turma;
	aulasDesmarcada: Array<any>;
	aulasParaDesmarcarDia: Array<any>;

	control: FormControl = new FormControl();
	aulaASerDesmarcada: FormControl = new FormControl();

	get dia() {
		return moment(this.turma.dia).toDate();
	}

	constructor(
		private openModal: NgbActiveModal,
		private turmaService: TreinoApiTurmaService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	save() {
		const retorno: any = {};
		if (this.aulasDesmarcada && this.aulasDesmarcada.length > 0) {
			const controlId = this.control.value.split("-", 1);
			retorno.id = controlId[0];
			this.aulasDesmarcada.forEach((aula) => {
				if (aula.id === this.control.value) {
					retorno.dia = aula.dia;
				}
			});
		} else {
			retorno.id = this.aulaASerDesmarcada.value;
			this.aulasParaDesmarcarDia.forEach((aula) => {
				if (aula.id === this.aulaASerDesmarcada.value) {
					retorno.dia = aula.dia;
				}
			});
		}
		this.openModal.close(retorno);
	}

	setup(aulasDesmarcada: Array<any>) {
		if (aulasDesmarcada && aulasDesmarcada.length > 0) {
			this.aulasDesmarcada = aulasDesmarcada;
			this.control.setValue(aulasDesmarcada[0].id);
		} else {
			this.turmaService
				.obterTurmasZWPorDia(this.aluno.matriculaZW, null)
				.subscribe((response) => {
					this.aulasParaDesmarcarDia = [];
					response.forEach((aula) => {
						const dia = moment(aula.data).toDate();
						this.aulasParaDesmarcarDia.push({
							id: aula.id,
							nome:
								aula.modalidadeNome +
								" - " +
								moment(dia).format("DD/MM/YYYY") +
								" | " +
								aula.horarioInicio,
							dia: aula.data,
						});
					});
					if (this.aulasParaDesmarcarDia.length > 0) {
						this.aulaASerDesmarcada.setValue(this.aulasParaDesmarcarDia[0].id);
					}
					this.cd.detectChanges();
				});
		}
	}
}
