import {
	Component,
	OnInit,
	ViewChild,
	Input,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	<PERSON>Destroy,
	EventEmitter,
	AfterViewChecked,
} from "@angular/core";

import { AgendaTurmaStateService } from "../../services/agenda-turma-state.service";
import { Subscription } from "rxjs";

import { Moment } from "moment";
import { Aula } from "../../services/agenda-turma-state.model";
import { AgendaView } from "treino-api";
import { TurmaCardComponent } from "../turma-card/turma-card.component";
import { TurmaCardDetailComponent } from "../turma-card-detail/turma-card-detail.component";

declare var moment;

@Component({
	selector: "pacto-turma-semana",
	templateUrl: "./turma-semana.component.html",
	styleUrls: ["./turma-semana.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaSemanaComponent
	implements OnInit, AfterViewChecked, <PERSON><PERSON><PERSON>roy
{
	@ViewChild("columnWrapper", { static: false }) columnWrapper;
	@ViewChild("timeAxis", { static: false }) timeAxis;
	@ViewChild("diaSemana", { static: false }) diaSemana;
	@ViewChild("painelScroll", { static: false }) painelScroll;
	@Input() firstWeekDay = 1553472000001;

	_events: any[][] = [];
	turmaSelecionada: Aula;
	subscriptions: Subscription[] = [];
	loading = false;
	overlay = false;
	aulasGlobal = [];

	private fetchEventSubscription: Subscription;

	/**
	 * Reference day as YYYYMMDD
	 */
	get referenceDay(): string {
		return this.agendaStateService.dia;
	}

	/**
	 * Monday of week within which the
	 * reference day resides.
	 */
	get mondayOfWeek(): Moment {
		const referenceDay = moment(this.referenceDay, "YYYYMMDD");
		if (referenceDay.day() === 0) {
			referenceDay.day(-6);
		} else {
			referenceDay.day(1);
		}
		return referenceDay;
	}

	gridContext = {
		selectTurma$: new EventEmitter(),
	};

	calendarStateChange(state) {
		this.overlay = state;
		this.cd.detectChanges();
	}

	isToday(weekday) {
		const day = moment().format("YYYYMMDD");
		return moment(weekday).format("YYYYMMDD") === day;
	}

	get hourArray() {
		return new Array(48).fill(0);
	}

	get weekdays(): number[] {
		const result = [];
		const referenceDay = this.mondayOfWeek;
		result.push(referenceDay.valueOf());
		for (let index = 1; index <= 6; index++) {
			referenceDay.add(1, "days");
			result.push(referenceDay.valueOf());
		}
		return result;
	}

	constructor(
		private agendaStateService: AgendaTurmaStateService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.subscriptions.push(
			this.agendaStateService.stateUpdate$.subscribe((loading) => {
				this.loading = loading;
				if (!loading) {
					this.processEvents();
				}
				this.cd.detectChanges();
			})
		);

		this.subscriptions.push(
			this.gridContext.selectTurma$.subscribe((turma) => {
				this.turmaSelecionada = turma;
			})
		);
	}

	goToDay(day) {
		const dia = moment(day);
		this.agendaStateService.setDia(dia.format("YYYYMMDD"));
		this.agendaStateService.setView(AgendaView.DIA);
	}

	private processEvents() {
		this._events = [];
		const weekdays = this.weekdays;
		weekdays.forEach((weekday, index) => {
			const dayFormat = moment(weekday).format("YYYYMMDD");
			this._events.push([]);
			let aulas;
			if (this.agendaStateService.aulas[dayFormat]) {
				aulas = this.agendaStateService.aulas[dayFormat];
				aulas.forEach((aula) => {
					this.aulasGlobal.push(aula);
					const grid = this.agendaStateService.convertTurmaIntoGrid(
						aula,
						TurmaCardComponent,
						TurmaCardDetailComponent
					);
					this._events[index].push(grid);
				});
				this.setScroll(this.buscaProximaAula(this.aulasGlobal).menor);
			}
		});
	}

	ngOnDestroy() {
		this.subscriptions.forEach((subscription) => {
			subscription.unsubscribe();
		});
	}

	ngAfterViewChecked() {
		this.aulasGlobal = []; // limpar array global
	}

	setScroll(height) {
		if (this.painelScroll !== undefined) {
			this.painelScroll.nativeElement.scrollTop = height;
		}
	}

	convHoras(horarioInicio) {
		const hora = parseInt(horarioInicio.slice(0, 2), 10);
		const minutos = parseInt(horarioInicio.slice(3, 5), 10);
		return hora * 60 + minutos - 40; // 40 pixeis para ficar bonito
	}

	buscaProximaAula(aulas) {
		let menor = 1000000; // numero grande nao alcancavel
		let menorPos;
		let aux;
		for (let i = 0; i < aulas.length; i++) {
			aux = this.convHoras(aulas[i].horarioInicio);
			if (aux < menor) {
				menor = aux;
				menorPos = i;
			}
		}
		if (menor === 1000000) {
			menorPos = menor = 0;
			return { menorPos, menor };
		}
		return { menorPos, menor };
	}
}
