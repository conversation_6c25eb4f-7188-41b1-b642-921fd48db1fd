<div *ngIf="!turmaSelecionada" class="agenda-wrapper">
	<pacto-agenda-control
		(calendarState)="calendarStateChange($event)"></pacto-agenda-control>

	<div class="apanel-wrapper">
		<div *ngIf="overlay" class="apanel-overlay"></div>

		<!-- DIAS DA SEMANA -->
		<div [ngClass]="{ 'loading-blur': loading }" class="dias-semana-wrapper">
			<div class="dias-semana">
				<div
					(click)="goToDay(weekday)"
					*ngFor="let weekday of weekdays"
					[ngClass]="{ selected: isToday(weekday) }"
					class="dia-semana">
					<div class="mes">{{ weekday | date : "dd" }}</div>
					<div class="semana">{{ weekday | date : "EEEE" }}</div>
					<div class="lower-border"></div>
				</div>
			</div>
		</div>

		<!-- CONTENT -->
		<div
			#painelScroll
			[ngClass]="{ 'loading-blur': loading }"
			class="content-wrapper agenda-scroll-container"
			id="painelScrollSemana">
			<div class="inner-wrapper-container">
				<div [horaFinal]="23" [horaInicial]="1" pacto-agenda-time-axis></div>

				<div class="time-grid-wrapper">
					<!-- Events -->
					<div
						*ngFor="let day of weekdays; let index = index"
						[clicavel]="false"
						[context]="gridContext"
						[events]="_events[index]"
						[ngClass]="{ selected: isToday(day) }"
						class="eventos-dia-column"
						pacto-agenda-time-grid></div>

					<!-- Time markings -->
					<div class="hour-marker-wrapper">
						<div *ngFor="let day of weekdays" class="marker-column">
							<div *ngFor="let item of hourArray" class="hour-marker"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-agenda-turma-view
	(closed)="turmaSelecionada = null"
	*ngIf="turmaSelecionada"
	[turma]="turmaSelecionada"></pacto-agenda-turma-view>
