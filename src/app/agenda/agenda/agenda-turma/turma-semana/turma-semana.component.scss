@import "src/assets/scss/pacto/plataforma-import.scss";

$grey-line: #dadada;

.apanel-wrapper {
	position: relative;

	.apanel-overlay {
		position: absolute;
		left: 0px;
		top: 0px;
		right: 0px;
		bottom: 0px;
		z-index: 2;
		opacity: 0.5;
		background-color: $pretoPri;
	}
}

.loading-blur {
	filter: blur(2px);
	pointer-events: none;
}

.agenda-wrapper {
	height: 100%;
}

.dias-semana-wrapper {
	border-bottom: 1px solid $grey-line;
}

.dias-semana {
	display: flex;
	margin-left: 96px;

	.dia-semana {
		width: 14.2%;
		text-align: center;
		height: 100px;
		cursor: pointer;

		&.selected {
			.mes,
			.semana {
				color: $azulimPri;
			}
		}

		&:hover {
			background-color: #f8f8f8;
		}

		.mes {
			@extend .type-h3-bold;
			color: $pretoPri;
			line-height: 1.2em;
			margin-top: 15px;
		}

		.semana {
			@extend .type-caption;
			color: $gelo04;
			text-transform: uppercase;
			margin-bottom: 15px;
		}

		.lower-border {
			height: 21px;
			width: 100%;
			border-left: 1px solid $grey-line;
		}
	}
}

.current-date {
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1px solid $gelo02;

	.weekday {
		@extend .type-caption;
		text-transform: uppercase;
		color: $gelo02;
	}

	.day {
		@extend .type-h3;
		color: $pretoPri;
		margin: 0px 15px;
	}
}

.content-wrapper {
	width: 100%;
	display: flex;
}

.inner-wrapper-container {
	display: flex;
	width: 100%;
}

.time-grid-wrapper {
	flex-grow: 1;
	display: flex;
}

.agenda-time-grid {
	top: 0px;
	left: 0px;
	right: 0px;
}

/* 
    Colunas de semana
*/
.eventos-dia-column {
	width: 14.2%;
	padding-right: 1px;

	&:hover {
		z-index: 20;
	}

	&.selected {
		background-color: $azulimPastel;
	}
}

/*  
    Hour markers
*/
.hour-marker-wrapper {
	position: absolute;
	top: 71px;
	left: 96px;
	right: 0px;
	display: flex;

	.marker-column {
		width: 14.2%;
		border-right: 1px solid $grey-line;

		.hour-marker {
			width: 100%;
			height: 30px;
			border-bottom: 1px solid $grey-line;

			&:nth-child(2n + 1) {
				border-bottom: 1px dashed $grey-line;
			}
		}
	}
}

::-webkit-scrollbar {
	width: 5px;
}

::-webkit-scrollbar-track {
	top: 0;
	right: 0;
	width: 8px;
	height: 100%;
	position: absolute;
	opacity: 0;
	z-index: 1;
	background: rgba(222, 222, 222, 0.75);
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-transition: opacity 0.5s 0.5s ease-out;
	transition: opacity 0.5s 0.5s ease-out;
}

::-webkit-scrollbar-thumb {
	position: absolute;
	top: 0;
	left: 0;
	width: 8px;
	height: 8px;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}
