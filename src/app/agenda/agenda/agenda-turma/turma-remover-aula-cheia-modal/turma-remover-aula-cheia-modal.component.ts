import {
	ChangeDetectionStrategy,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-turma-remover-aula-cheia-modal",
	templateUrl: "./turma-remover-aula-cheia-modal.component.html",
	styleUrls: ["./turma-remover-aula-cheia-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaRemoverAulaCheiaModalComponent implements OnInit {
	@ViewChild("validCampo", { static: true })
	validCampo: TraducoesXinglingComponent;
	justificativa: FormControl = new FormControl("", [Validators.required]);
	titlelabel: string = "Justificativa";
	buttonLabel: string = "Excluir";

	constructor(
		private openModal: NgbActiveModal,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {}

	excluir() {
		this.justificativa.markAsTouched();
		if (this.justificativa.valid) {
			this.openModal.close(this.justificativa.value);
		} else {
			this.snotifyService.error(this.validCampo.getLabel("campoObrigatorio"));
		}
	}
}
