@import "src/assets/scss/pacto/plataforma-import.scss";

.center-aux {
	width: calc(100vw - 460px);
	max-width: 1230px;
	@media (max-width: $plataforma-breakpoint-large) {
		width: calc(100vw - 260px);
	}
}

.turma-view-wrapper {
	position: fixed;
	left: 0px;
	top: 0px;
	width: 100vw;
	height: 100vh;
	background-color: white;
	z-index: 100;
	color: $pretoPri;
	display: flex;
	flex-direction: column;
}

.view-header {
	border-bottom: 1px solid $cinza02;
	height: 210px;
	display: flex;
	justify-content: center;
	padding: 60px 0px 30px 0px;

	.pct {
		font-size: 28px;
	}

	.header {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.details-row {
		line-height: 2;
		display: flex;
		justify-content: space-between;

		.name-turma {
			display: flex;
			width: 100%;
			margin-top: 2px;

			.cursor-area {
				display: contents;
				cursor: pointer;
			}
		}

		.icon-delete {
			justify-content: flex-start;
			margin-left: 10px;
			display: flex;
			width: 100%;
			max-width: 100px;

			i {
				font-size: 24px;
				color: $hellboyPri;
				margin-top: 8px;
			}
		}

		pacto-cat-select-filter {
			display: block;
			width: 300px;
			position: relative;
		}
	}

	.close-link span {
		cursor: pointer;
	}
}

.situacao {
	display: flex;
	justify-content: center;

	.item {
		display: block;
		margin: 5px;
	}
}

.body-row {
	display: flex;
	justify-content: center;
	flex-grow: 1;

	.column-wrapper {
		display: flex;
	}

	::-webkit-scrollbar {
		width: 0px;
		height: 0px;
	}

	*:hover::-webkit-scrollbar {
		padding: 11px 0 11px 11px;
		width: 11px;
		height: 18px;
	}

	::-webkit-scrollbar-thumb {
		height: 3px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	::-webkit-scrollbar-button {
		width: 0;
		height: 0;
		display: none;
	}

	::-webkit-scrollbar-corner {
		background-color: transparent;
	}

	pacto-sumario {
		border-right: 1px solid $gelo02;
		flex-shrink: 0;
		width: 300px;
		display: grid;
		max-height: calc(100vh - 230px);
		overflow: auto;
	}

	.scroll-wrapper {
		width: 100%;
		flex-grow: 1;
	}

	.aluno-table {
		width: 100%;
		flex-grow: 1;
		margin: 0px 20px 0px;
		color: $gelo04;

		thead {
			color: $pretoPri;
			text-transform: uppercase;
		}

		th,
		td {
			text-align: center;
			@extend .type-p-small-rounded;
		}

		tr {
			line-height: 50px;
			border-bottom: 1px solid $cinzaPri;

			.tr-flex {
				display: flex;
				padding-top: 5px;
			}
		}

		td {
			&:first-child,
			&:last-child {
				padding: 0px 30px;
			}
		}

		.center-img {
			display: flex;
			align-items: center;
			text-align: center;
			@extend .type-p-small-rounded;
		}

		.info-aluno {
			display: grid;

			&:first-child,
			&:last-child {
				padding: 5px 0px 0px 30px;
			}
		}

		.line-cell {
			line-height: 1;
			text-align: left;
			text-transform: lowercase;
			font-weight: 400;
		}

		.line-cell::first-line {
			text-transform: capitalize;
		}

		.color-matricula {
			color: $cinzaPri;
		}

		.acoes {
			font-size: 18px;
			cursor: pointer;

			.pct {
				padding: 0px 5px;
			}

			.confirmar {
				color: $azulimPri;
			}

			.desconfirmar {
				color: $gelo03;
			}

			.remover {
				color: $hellboyPri;
			}
		}
	}
}

.msg-bloqueio {
	font-size: 14px;
	color: $cinzaClaro05;
	font-weight: 900;
}

.div-funcoes-auxiliares {
	display: flex;

	.btn-confirmar-todos {
		.icon-confirmar-todos {
			font-size: 16px;
			vertical-align: sub;
		}

		box-shadow: none;
		border-radius: 4px;
		line-height: 32px;
		cursor: pointer;
		position: relative;
		outline: 0;
		background-color: #1998fc;
		border: 1px solid #1998fc;
		color: #fff;
		font-family: "Nunito Sans", sans-serif;
		font-weight: 600;
		font-size: 12px;
		padding: 0 16px;
		width: 144px;
		height: 42px;
		margin-left: 12px;
	}
}
