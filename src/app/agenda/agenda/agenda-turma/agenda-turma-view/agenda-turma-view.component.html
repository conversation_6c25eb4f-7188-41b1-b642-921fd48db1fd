<div class="turma-view-wrapper">
	<div class="view-header">
		<div class="header center-aux">
			<div class="type-h2-bold close-link">
				<span (click)="closeHandler()" id="btn-fechar-detalhes">
					<i class="pct pct-x"></i>
					<span *ngIf="turma?.aulaCheia" i18n="@@agenda-detalhes-aula:titulo">
						Detalhes da Aula
					</span>
					<span *ngIf="!turma?.aulaCheia" i18n="@@agenda-detalhes-aula:titulo2">
						Detalhes da Turma
					</span>
				</span>
			</div>
			<div class="details-row">
				<div class="type-h5-bold name-turma">
					<div (click)="deleteTurma()" class="cursor-area">
						<div style="margin-top: 3px">
							{{ turma.nome }}
						</div>
						<div
							*ngIf="podeExcluirAulaCheia && turma?.aulaCheia"
							class="icon-delete"
							title="Excluir Aula">
							<i class="pct pct-trash-2"></i>
						</div>
					</div>
				</div>
				<div class="div-funcoes-auxiliares">
					<div id="buscar-aluno-aula-turma">
						<span *ngIf="!liberado" class="msg-bloqueio">
							Você não pode adicionar um aluno nesta aula porque o ambiente onde
							ela se realizaria já se encontra ocupado.
						</span>

						<pacto-cat-select-filter
							*ngIf="podeInserirAluno && liberado"
							[control]="alunoFc"
							[endpointUrl]="_rest.buildFullUrl('alunos')"
							[id]="'aluno-select'"
							[labelKey]="'nome'"
							[paramBuilder]="alunoSelectBuilder"
							[resposeParser]="responseParser"
							i18n-placeholder="@@agenda-detalhes-aula:holderInserir"
							placeholder="Inserir um aluno...."></pacto-cat-select-filter>
					</div>
					<button
						(click)="confirmarTodosHandler()"
						*ngIf="liberado"
						class="btn-confirmar-todos"
						id="confirmar-todos">
						<i class="pct confirmar pct-check-circle icon-confirmar-todos"></i>
						Confirmar Todos
					</button>
				</div>
			</div>
		</div>
	</div>

	<div class="body-row">
		<div class="center-aux column-wrapper">
			<pacto-sumario #sumario [turma]="turma"></pacto-sumario>
			<div
				[maxHeight]="'calc(100vh - 230px)'"
				class="scroll-wrapper"
				pactoCatSmoothScroll>
				<table class="aluno-table">
					<thead class="type-btn-bold">
						<tr>
							<th i18n="@@agenda-detalhes-aula:matricula">CLIENTE</th>
							<th i18n="@@agenda-detalhes-aula:nomeCliente">SITUAÇÃO</th>
							<th *ngIf="integracaoZW" i18n="@@agenda-detalhes-aula:status">
								STATUS
							</th>
							<th i18n="@@agenda-detalhes-aula:acoes">AÇÕES</th>
						</tr>
					</thead>
					<tbody>
						<tr *ngFor="let aluno of turma?.alunos; let index = index">
							<td class="tr-flex">
								<pacto-cat-person-avatar
									[diameter]="40"
									[uri]="aluno.imageUri"
									class="center-img"></pacto-cat-person-avatar>
								<div class="info-aluno">
									<div class="line-cell">
										{{ aluno.nome }}
									</div>
									<div
										*ngIf="
											!aluno.vinculoComAula ||
											aluno.vinculoComAula != 'INTEGRACAO'
										"
										class="line-cell color-matricula">
										Matrícula: {{ aluno.matriculaZW }}
									</div>

									<div
										*ngIf="
											aluno.vinculoComAula &&
											aluno.vinculoComAula == 'INTEGRACAO'
										"
										class="line-cell color-matricula">
										Unidade: {{ aluno.unidade }}
									</div>
								</div>
							</td>
							<td>
								<span class="situacao">
									<pacto-cat-situacao-aluno
										[situacaoAluno]="aluno.situacaoAluno"
										class="item"></pacto-cat-situacao-aluno>
									<pacto-cat-situacao-contrato
										[situacaoContrato]="aluno.situacaoContrato"
										class="item"></pacto-cat-situacao-contrato>
								</span>
							</td>
							<td *ngIf="integracaoZW">
								{{ vinculoAulaTraducao.getLabel(aluno.vinculoComAula) }}
							</td>
							<td>
								<div class="acoes">
									<i
										(click)="confirmarHandler(aluno)"
										*ngIf="
											!aluno.confirmado && aluno.vinculoComAula !== 'DESMARCADO'
										"
										class="pct desconfirmar pct-check-circle"
										i18n-title="@@agenda-detalhes-aula:confirmPresenca"
										id="acao-confirmar-presenca-{{ index }}"
										title="Confirmar presença"></i>
									<i
										(click)="desconfirmarHandler(aluno)"
										*ngIf="
											aluno.confirmado && aluno.vinculoComAula !== 'DESMARCADO'
										"
										class="pct confirmar pct-check-circle"
										i18n-title="@@agenda-detalhes-aula:desconfirmPresenca"
										id="acao-desconfirmar-presenca-{{ index }}"
										title="Desconfirmar presença"></i>
									<i
										(click)="removerHandler(aluno)"
										*ngIf="
											!aluno.confirmado &&
											aluno.vinculoComAula !== 'DESMARCADO' &&
											podeRemoverAluno
										"
										class="pct remover pct-trash-2"
										i18n-title="@@agenda-detalhes-aula:desmarcAluno"
										id="acao-desmarcar-aluno-{{ index }}"
										title="Desmarcar aluno"></i>
									<i
										(click)="justificarHandler(aluno)"
										*ngIf="
											!aluno.confirmado &&
											aluno.vinculoComAula == 'DESMARCADO' &&
											integracaoZW &&
											!turma?.aulaCheia &&
											aluno.justificativa != null &&
											aluno.justificativa != ''
										"
										class="fa fa-pencil"
										i18n-title="@@agenda-detalhes-aula:justificativaAluno"
										id="acao-justificativa-aluno-{{ index }}"
										title="Justificar falta aluno"></i>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #modalDesmarcarAluno>
	<span i18n="@@agenda-detalhes-modalDesmarcar:title" xingling="title">
		Desmarcar aula
	</span>
	<span i18n="@@agenda-detalhes-modalDesmarcar:title2" xingling="title2">
		Desmarcar turma
	</span>
	<span xingling="body">
		Deseja realmente remover {{ alunoASerRemovido }} desta aula?
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #notificacoes>
	<span i18n="@@agenda-detalhes-aula:confirmSuccess" xingling="confirmSuccess">
		Confirmado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:confirmSuccess"
		xingling="confirmedSuccess">
		Alunos confirmados com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:desconfirmSuccess"
		xingling="desconfirmSuccess">
		Desconfirmado com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:desmarcadoSuccess"
		xingling="desmarcadoSuccess">
		Desmarcado com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:validCreditos" xingling="validCreditos">
		O número de aulas não pode exceder a quantidade de créditos.
	</span>
	<span i18n="@@agenda-detalhes-aula:validPermissao" xingling="validPermissao">
		Ops! Essa turma não permite aula experimental ou diária.
	</span>
	<span i18n="@@agenda-detalhes-aula:soPodeMarcar" xingling="soPodeMarcar">
		Você só pode marcar
	</span>
	<span i18n="@@agenda-detalhes-aula:aposInicioAula" xingling="aposInicioAula">
		após o início da aula.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeMarcarAte"
		xingling="soPodeMarcarAte">
		Você só pode marcar até
	</span>
	<span
		i18n="@@agenda-detalhes-aula:antesInicioAula"
		xingling="antesInicioAula">
		antes do início da aula.
	</span>
	<span i18n="@@agenda-detalhes-aula:aulaEstaCheia" xingling="aulaEstaCheia">
		A aula já está cheia!
	</span>
	<span
		i18n="@@agenda-detalhes-aula:semPermissaoDiariaFreePass"
		xingling="semPermissaoDiariaFreePass">
		Você precisa ter as permissões: (2.51 - Lançar Free Pass), (4.01 - Diária),
		para realizar esta operação.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:jaMatriculouNaAula"
		xingling="jaMatriculouNaAula">
		Você já se matriculou nessa aula!
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemModalidadeExperimental"
		xingling="naoTemModalidadeExperimental">
		O aluno não tem essa modalidade, deseja usar uma de suas
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemModalidadeExperimental2"
		xingling="naoTemModalidadeExperimental2">
		aulas experimentais?
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeDesmarcarAulaAte"
		xingling="soPodeDesmarcarAulaAte">
		Você só pode desmarcar a aula com até
	</span>
	<span
		i18n="@@agenda-detalhes-aula:soPodeDesmarcarAulaAte2"
		xingling="soPodeDesmarcarAulaAte2">
		de antecedência
	</span>
	<span
		i18n="@@agenda-detalhes-aula:foraHorarioPlano"
		xingling="foraHorarioPlano">
		A aula não está dentro do horário do seu plano, deseja usar uma de suas
	</span>
	<span
		i18n="@@agenda-detalhes-aula:foraHorarioPlano2"
		xingling="foraHorarioPlano2">
		aulas experimentais?
	</span>
	<span
		i18n="@@agenda-detalhes-aula:aulaCheiaJustificada"
		xingling="aulaCheiaJustificada">
		Falta justificada com sucesso.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #vinculoAulaTraducao>
	<span i18n="@@agenda-detalhes-aula:turmaCheia" xingling="turmaCheia">
		A turma está cheia.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:naoTemcadastroZW"
		xingling="naoTemcadastroZW">
		O aluno não tem cadastro no ZW
	</span>
	<span
		i18n="@@agenda-detalhes-aula:editarReposicaoAula"
		xingling="editarReposicaoAula">
		Editar reposição de aula
	</span>
	<span i18n="@@agenda-detalhes-aula:marcacaoAula" xingling="marcacaoAula">
		Marcação de aula
	</span>
	<span
		i18n="@@agenda-detalhes-aula:editarExperimental"
		xingling="editarExperimental">
		Editar aula experimental
	</span>
	<span
		i18n="@@agenda-detalhes-aula:inseridoSucesso"
		xingling="inseridoSucesso">
		Aluno inserido com sucesso.
	</span>
	<span i18n="@@agenda-detalhes-aula:MATRICULADO" xingling="MATRICULADO">
		Matriculado
	</span>
	<span i18n="@@agenda-detalhes-aula:REPOSICAO" xingling="REPOSICAO">
		Reposição
	</span>
	<span i18n="@@agenda-detalhes-aula:REPOSICAO" xingling="INTEGRACAO">
		Integração
	</span>
	<span i18n="@@agenda-detalhes-aula:DEPENDENTE" xingling="DEPENDENTE">
		Dependente
	</span>
	<span i18n="@@agenda-detalhes-aula:DESMARCADO" xingling="DESMARCADO">
		Desmarcado
	</span>
	<span
		i18n="@@agenda-detalhes-aula:AULA_EXPERIMENTAL"
		xingling="AULA_EXPERIMENTAL">
		Aula experimental
	</span>
	<span i18n="@@agenda-detalhes-aula:DIARIA" xingling="DIARIA">Diaria</span>
	<span i18n="@@agenda-detalhes-aula:ESPERA" xingling="ESPERA">
		Fila de espera
	</span>
	<span i18n="@@agenda-detalhes-aula:DESAFIO" xingling="DESAFIO">Desafio</span>
	<span i18n="@@agenda-detalhes-aula:DIARIA_GYMPASS" xingling="DIARIA_GYMPASS">
		Diaria gympass
	</span>
	<span
		i18n="@@agenda-detalhes-aula:DIARIA_TOTALPASS"
		xingling="DIARIA_TOTALPASS">
		Diaria totalpass
	</span>
	<span i18n="@@agenda-detalhes-aula:MARCACAO" xingling="MARCACAO">
		Marcação
	</span>
	<span i18n="@@agenda-detalhes-aula:VISITANTE" xingling="VISITANTE">
		Visitante
	</span>
	<span i18n="@@agenda-detalhes-aula:VISITANTE" xingling="CONTRATO_FUTURO">
		Aluno com contrato futuro e não atende demais condições para ser inserido na
		aula ou turma
	</span>
</pacto-traducoes-xingling>
