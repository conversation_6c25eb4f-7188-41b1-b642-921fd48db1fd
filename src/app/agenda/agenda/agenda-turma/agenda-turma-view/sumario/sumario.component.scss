@import "src/assets/scss/pacto/plataforma-import.scss";

.professor {
	display: flex;
	margin: 20px 10px 20px 0px;
	align-items: center;
	cursor: pointer;

	pacto-cat-person-avatar {
		display: block;
		margin-right: 10px;
		margin-top: 5px;
	}

	.prof-label {
		@extend .type-h6;
		color: $cinza05;
	}

	.prof-value {
		@extend .type-p-small-rounded;
		color: $pretoPri;
		text-transform: uppercase;
	}

	.icon-replace {
		justify-content: flex-end;
		display: flex;
		width: 100%;
		max-width: 100px;

		i {
			font-size: 24px;
			color: $azulim05;
			margin-top: 8px;
		}
	}
}

.info-bit {
	display: flex;
	margin: 10px 10px 10px 0px;

	.icon {
		color: $cinza03;
		padding: 10px 10px 10px 0px;
		font-size: 20px;
		width: 30px;
	}

	.bit-label {
		@extend .type-h6;
		color: $cinza05;
	}

	.bit-value {
		@extend .type-p-small-rounded;
		color: $pretoPri;
		text-transform: uppercase;
	}
}

.action-button {
	padding-right: 15px;
	height: calc(100vh - 737px);

	.action-button-child {
		padding: 8px 0px 8px 0px;
	}
}

::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

*:hover::-webkit-scrollbar {
	padding: 11px 0 11px 11px;
	width: 11px;
	height: 18px;
}

::-webkit-scrollbar-thumb {
	height: 3px;
	border: 4px solid rgba(0, 0, 0, 0);
	background-clip: padding-box;
	border-radius: 3px;
	box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
		inset 1px 1px 0px rgba(0, 0, 0, 0.05);
	background-color: #6f747b;
}

::-webkit-scrollbar-button {
	width: 0;
	height: 0;
	display: none;
}

::-webkit-scrollbar-corner {
	background-color: transparent;
}
