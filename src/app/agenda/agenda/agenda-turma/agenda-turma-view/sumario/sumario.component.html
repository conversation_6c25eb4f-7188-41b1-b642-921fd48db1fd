<div (click)="substituirProfessor()" class="professor">
	<pacto-cat-person-avatar
		[diameter]="42"
		[uri]="
			turma?.professorSubstituto
				? turma?.professorSubstituto?.imageUri
				: turma?.professor?.imageUri
		"></pacto-cat-person-avatar>
	<div>
		<div class="prof-label" i18n="@@agenda-detalhes-aula:professor">
			Professor
		</div>
		<div class="prof-value">
			{{
				turma?.professorSubstituto
					? turma.professorSubstituto.nome
					: turma?.professor?.nome
			}}
		</div>
	</div>
	<div
		*ngIf="podeSubstituirProfesor && turma?.aulaCheia"
		class="icon-replace"
		title="Substituir Professor">
		<i class="pct pct-repeat" id="substituir-professor"></i>
	</div>
</div>

<div class="info-bit">
	<div class="icon"><i class="pct pct-calendar"></i></div>
	<div>
		<div class="bit-label" i18n="@@agenda-detalhes-aula:data">Data</div>
		<div class="bit-value">
			{{ dia | date : "EEEE" }}, {{ dia | date : "shortDate" }}
		</div>
	</div>
</div>
<div class="info-bit">
	<div class="icon"><i class="pct pct-clock"></i></div>
	<div>
		<div class="bit-label" i18n="@@agenda-detalhes-aula:horario">Horário</div>
		<div class="bit-value">
			{{ turma?.horarioInicio }} - {{ turma?.horarioFim }}
		</div>
	</div>
</div>
<div class="info-bit">
	<div class="icon"><i class="pct pct-map-pin"></i></div>
	<div>
		<div class="bit-label" i18n="@@agenda-detalhes-aula:ambiente">Ambiente</div>
		<div class="bit-value">{{ turma?.ambiente?.nome }}</div>
	</div>
</div>
<div class="info-bit">
	<div class="icon"><i class="pct pct-tag"></i></div>
	<div>
		<div class="bit-label" i18n="@@agenda-detalhes-aula:modalidade">
			Modalidade
		</div>
		<div class="bit-value">{{ turma.modalidade.nome }}</div>
	</div>
</div>
<div *ngIf="integracaoZW && !turma?.aulaCheia" class="info-bit">
	<div class="icon"><i class="pct pct-bar-chart"></i></div>
	<div>
		<div class="bit-label" i18n="@@agenda-detalhes-aula:nivel">Nível</div>
		<div class="bit-value">{{ turma?.nivel?.nome }}</div>
	</div>
</div>
<div class="info-bit">
	<div class="icon"><i class="pct pct-bar-chart"></i></div>
	<div id="capacidade-turma">
		<div class="bit-label" i18n="@@agenda-detalhes-aula:capacidade">
			Capacidade
		</div>
		<div class="bit-value">
			{{ turma.numeroAlunos }}/{{ turma?.capacidade }}
		</div>
	</div>
</div>
<div *ngIf="turma?.aulaCheia" class="action-button">
	<pacto-log [url]="urlLog"></pacto-log>
</div>
<pacto-traducoes-xingling #mensagemNotificacao>
	<span
		i18n="@@agenda-detalhes-aula:aulaCheiaExcluida"
		xingling="aulaCheiaExcluida">
		Aula excluida com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:substituirProfessor"
		xingling="substituirProfessor">
		Substituir Professor
	</span>
	<span
		i18n="@@agenda-detalhes-aula:substituirProfessorSucess"
		xingling="substituirProfessorSucess">
		Professor substituido com sucesso.
	</span>
	<span
		i18n="@@agenda-detalhes-aula:existeAlunosNaAula"
		xingling="existeAlunosNaAula">
		Você não pode excluir uma aula que tenha alunos marcados.
	</span>
</pacto-traducoes-xingling>
