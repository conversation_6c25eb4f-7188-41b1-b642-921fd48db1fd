import { SumarioComponent } from "./sumario/sumario.component";
import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import {
	TurmaZW,
	AcaoRequeridaMarcarAluno,
	DadosInserirAluno,
	StatusMarcarAlunoEnum,
	TreinoApiTurmaService,
} from "treino-api";
import { AgendaTurmaStateService } from "../../services/agenda-turma-state.service";
import { FormControl } from "@angular/forms";
import { ModalService } from "@base-core/modal/modal.service";
import { TurmaReposicaoModalComponent } from "../turma-reposicao-modal/turma-reposicao-modal.component";
import { SnotifyService } from "ng-snotify";
import { TurmaMarcacaoModalComponent } from "../turma-marcacao-modal/turma-marcacao-modal.component";
import {
	TipoProdutoZW,
	TurmaAulaExperimentalModalComponent,
} from "../turma-aula-experimental-modal/turma-aula-experimental-modal.component";
import { TurmaDesmarcarModalComponent } from "../turma-desmarcar-modal/turma-desmarcar-modal.component";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { SituacaoAlunoEnum } from "src/app/base/alunos/components/alunos-lista/alunos-lista.component";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { TurmaRemoverAulaCheiaModalComponent } from "../turma-remover-aula-cheia-modal/turma-remover-aula-cheia-modal.component";
import { TurmaJustificativaFaltaAulaCheiaModalComponent } from "../turma-justificativa-falta-aula-cheia-modal/turma-justificativa-falta-aula-cheia-modal.component";
import { Router } from "@angular/router";

declare var moment;

@Component({
	selector: "pacto-agenda-turma-view",
	templateUrl: "./agenda-turma-view.component.html",
	styleUrls: ["./agenda-turma-view.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaTurmaViewComponent implements OnInit {
	@Input() turma: TurmaZW;
	@Output() closed: EventEmitter<any> = new EventEmitter();
	@ViewChild("notificacoes", { static: true })
	notificacoes: TraducoesXinglingComponent;
	@ViewChild("modalDesmarcarAluno", { static: true })
	modalDesmarcarAluno: TraducoesXinglingComponent;
	@ViewChild("vinculoAulaTraducao", { static: true })
	vinculoAulaTraducao: TraducoesXinglingComponent;
	@ViewChild("sumario", { static: true }) sumario: SumarioComponent;

	alunoASerRemovido: string;
	alunoFc = new FormControl();
	integracaoZW = false;
	podeRemoverAluno = false;
	podeInserirAluno = false;
	podeExcluirAulaCheia = false;
	forcarAtualizar = false;

	alunoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
			permiteAlunoOutraEmpresa:
				this.turma.permiteAlunoOutraEmpresa || this.turma.aulaCheia
					? "TRUE"
					: "FALSE",
			incluirAutorizado: this.turma.aulaCheia ? "TRUE" : "FALSE",
		};
	};

	constructor(
		private turmaStateService: AgendaTurmaStateService,
		private turmaService: TreinoApiTurmaService,
		private rest: RestService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private router: Router,
		private turmaState: AgendaTurmaStateService
	) {}

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.podeRemoverAluno = this.permitirRemoverAluno();
		this.podeInserirAluno = this.permitirInserirAluno();
		this.podeExcluirAulaCheia = this.permitirExcluirAulaCheia();
		this.forcarAtualizar = this.turma.atualizarAgenda;
		this.alunoFc.valueChanges.subscribe((aluno) => {
			if (this.turma.aulaCheia) {
				this.addAlunoHandler(aluno);
			} else if (
				this.turma.permitirAulaExperimental ||
				(!this.turma.permitirAulaExperimental &&
					aluno.situacaoAluno === SituacaoAlunoEnum.ATIVO)
			) {
				this.addAlunoHandler(aluno);
			} else if (
				!this.turma.aulaCheia &&
				!this.turma.permitirAulaExperimental &&
				aluno.situacaoAluno !== SituacaoAlunoEnum.ATIVO
			) {
				this.snotifyService.error(this.notificacoes.getLabel("validPermissao"));
			}
			this.alunoFc.setValue(null, { emitEvent: false });
		});
	}

	closeHandler() {
		this.closed.emit(true);
		if (this.forcarAtualizar === true) {
			this.turmaStateService.updateAll();
		}
	}

	private addAlunoHandler(aluno) {
		const dadosInserirAluno: DadosInserirAluno = {
			matricula: aluno.matriculaZW,
			dia: this.turma.dia,
			autorizado: aluno.autorizado,
			acao: AcaoRequeridaMarcarAluno.SELECIONAR_ALUNO,
		};
		this.turmaService
			.marcarAlunoTurma(this.turma.horarioTurmaId, dadosInserirAluno)
			.subscribe((result) => {
				if (result.messageID) {
					if (result.messageID.includes("aulaEstaCheia")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("aulaEstaCheia")
						);
						return;
					}
					if (result.messageID.includes("jaMatriculouNaAula")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("jaMatriculouNaAula")
						);
						return;
					}

					if (result.messageID.includes("aposInicioAula")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("soPodeMarcar") +
								result.messageValue +
								this.notificacoes.getLabel("aposInicioAula")
						);
						return;
					}
					if (result.messageID.includes("antesInicioAula")) {
						this.snotifyService.error(
							this.notificacoes.getLabel("soPodeMarcarAte") +
								result.messageValue +
								this.notificacoes.getLabel("antesInicioAula")
						);
						return;
					}
				}
				switch (result.status) {
					case StatusMarcarAlunoEnum.SUCESSO:
						this.turmaStateService.updateTurma(result.conteudo.turma);
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("inseridoSucesso")
						);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
						);
						break;
					case StatusMarcarAlunoEnum.TURMA_CHEIA:
						this.snotifyService.error(
							this.vinculoAulaTraducao.getLabel("turmaCheia")
						);
						break;
					case StatusMarcarAlunoEnum.MARCAR_REPOSICAO:
						this.openReposicaoModalHandler(
							dadosInserirAluno,
							aluno,
							result.conteudo
						);
						break;
					case StatusMarcarAlunoEnum.CREDITOS_PARA_MARCACAO:
						this.openMarcacaoModalHandler(
							dadosInserirAluno,
							aluno,
							result.conteudo,
							true
						);
						break;
					case StatusMarcarAlunoEnum.CREDITOS_EXTRA:
						this.openMarcacaoModalHandler(
							dadosInserirAluno,
							aluno,
							result.conteudo,
							false
						);
						break;
					case StatusMarcarAlunoEnum.ALUNO_SEM_CONTRATO:
						this.openAulaExperimentalProdutoZWModal(dadosInserirAluno, aluno);
						break;
					case StatusMarcarAlunoEnum.AULA_EXPERIMENTAL:
						this.openAulaCheiaExperimental(dadosInserirAluno, aluno, result);
						break;
					case StatusMarcarAlunoEnum.ALUNO_SEM_CADASTRO_ZW:
						this.snotifyService.error(
							this.vinculoAulaTraducao.getLabel("naoTemcadastroZW")
						);
						break;
					case StatusMarcarAlunoEnum.CONTRATO_FUTURO:
						this.snotifyService.error(
							this.vinculoAulaTraducao.getLabel("CONTRATO_FUTURO")
						);
						break;
					case StatusMarcarAlunoEnum.DIARIA_CONTRATO_FUTURO:
						this.openAulaExperimentalProdutoZWModal(dadosInserirAluno, aluno);
						break;
				}
			});
	}

	confirmarHandler(aluno: any) {
		this.hideLoader();
		this.turmaService
			.confirmarPresencaTurma(
				this.turma.horarioTurmaId,
				this.turma.dia,
				aluno.matriculaZW,
				aluno.codigoPassivo,
				aluno.codigoIndicado,
				aluno.vinculoComAula
			)
			.subscribe((response) => {
				this.turmaStateService.updateTurma(response);
				this.snotifyService.success(
					this.notificacoes.getLabel("confirmSuccess")
				);
				this.showLoader();
			});
	}

	hideLoader() {
		const painelLoader = document.getElementsByTagName(
			"pacto-loader"
		) as HTMLCollectionOf<HTMLElement>;
		if (painelLoader.length !== 0) {
			painelLoader[0].setAttribute("style", "display:none!important");
		}
	}

	showLoader() {
		const painelLoader = document.getElementsByTagName(
			"pacto-loader"
		) as HTMLCollectionOf<HTMLElement>;
		if (painelLoader.length !== 0) {
			painelLoader[0].setAttribute("style", "display:flex!important");
		}
	}

	confirmarTodosHandler() {
		this.turmaService
			.confirmarTodasPresencas(this.turma.horarioTurmaId, this.turma.dia)
			.subscribe((response) => {
				this.turmaStateService.updateTurma(response);
				this.snotifyService.success(
					this.notificacoes.getLabel("confirmedSuccess")
				);
			});
	}

	desconfirmarHandler(aluno: any) {
		this.hideLoader();
		this.turmaService
			.desconfirmarPresencaTurma(
				this.turma.horarioTurmaId,
				this.turma.dia,
				aluno.matriculaZW,
				aluno.vinculoComAula
			)
			.subscribe((response) => {
				this.turmaStateService.updateTurma(response);
				this.snotifyService.success(
					this.notificacoes.getLabel("desconfirmSuccess")
				);
				this.showLoader();
			});
	}

	justificarHandler(aluno: any) {
		if (this.integracaoZW && !this.turma.aulaCheia) {
			if (aluno.justificativa != null) {
				const ref = this.modalService.open(
					"Justificar falta",
					TurmaJustificativaFaltaAulaCheiaModalComponent
				);
				ref.componentInstance.texto = aluno.justificativa;
			}
		}
	}

	removerHandler(aluno: any) {
		const titleModal = this.turma.aulaCheia
			? this.modalDesmarcarAluno.getLabel("title")
			: this.modalDesmarcarAluno.getLabel("title2");
		const modal = this.modalService.open(
			titleModal,
			TurmaDesmarcarModalComponent
		);
		modal.componentInstance.aluno = aluno;
		modal.componentInstance.aulaCheia = this.turma.aulaCheia;
		modal.result.then(() => {
			if (this.integracaoZW && !this.turma.aulaCheia) {
				const ref = this.modalService.open(
					"Justificar falta",
					TurmaJustificativaFaltaAulaCheiaModalComponent
				);
				ref.componentInstance.texto = aluno.justificativa;
				ref.result.then((result) => {
					this.turmaService
						.desmarcarAlunoTurma(
							this.turma.horarioTurmaId,
							this.turma.dia,
							aluno.vinculoComAula,
							aluno.matriculaZW,
							aluno.codigoPassivo,
							aluno.codigoIndicado,
							result
						)
						.subscribe((response) => {
							if (response.toString().includes("soPodeDesmarcarAulaAte")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("soPodeDesmarcarAulaAte") +
										response.toString().split(";")[1] +
										this.notificacoes.getLabel("soPodeDesmarcarAulaAte2")
								);
								return;
							} else if (typeof response === "string") {
								// ignore
							} else {
								this.snotifyService.success(
									this.notificacoes.getLabel("desmarcadoSuccess")
								);
							}
							this.turmaStateService.updateTurma(response);
						});
				});
			} else {
				this.turmaService
					.desmarcarAlunoTurma(
						this.turma.horarioTurmaId,
						this.turma.dia,
						aluno.vinculoComAula,
						aluno.matriculaZW,
						aluno.codigoPassivo,
						aluno.codigoIndicado,
						aluno.justificativa
					)
					.subscribe((response) => {
						if (response.toString().includes("soPodeDesmarcarAulaAte")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("soPodeDesmarcarAulaAte") +
									response.toString().split(";")[1] +
									this.notificacoes.getLabel("soPodeDesmarcarAulaAte2")
							);
							return;
						} else if (typeof response === "string") {
							// ignore
						} else {
							this.snotifyService.success(
								this.notificacoes.getLabel("desmarcadoSuccess")
							);
						}
						this.turmaStateService.updateTurma(response);
					});
			}
		});
	}

	deleteTurma() {
		if (this.podeExcluirAulaCheia && this.turma && this.turma.aulaCheia) {
			this.sumario.removeHandler();
		}
	}

	private permitirRemoverAluno() {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_ALUNO
		);
	}

	private permitirExcluirAulaCheia(): boolean {
		return this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.EXCLUIR_AULAS_DIA
		);
	}

	private permitirInserirAluno() {
		const horarioInicio = this.turma.horarioInicio.split(":");
		const dataTurma = moment(this.turma.dia)
			.add(horarioInicio[0], "hours")
			.add(horarioInicio[1], "minutes")
			.toDate();
		const dataHoje = new Date();
		return (
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.INSERIR_ALUNO
			) &&
			(this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.INSERIR_ALUNO_AULA_INICIADA
			)
				? true
				: moment(dataHoje).isBefore(dataTurma))
		);
	}

	get liberado() {
		return this.turma && this.turma.bloqueado === false;
	}

	private openReposicaoModalHandler(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any,
		conteudo: any
	) {
		const modal = this.modalService.open(
			this.vinculoAulaTraducao.getLabel("editarReposicaoAula"),
			TurmaReposicaoModalComponent
		);
		modal.componentInstance.aluno = aluno;
		modal.componentInstance.turma = this.turma;
		const aulaApresentar = [];
		if (conteudo) {
			conteudo.aulasDesmarcadas.forEach((aula) => {
				const dia = moment(aula.data).toDate();
				aulaApresentar.push({
					id: aula.id + "-" + moment(dia).format("YYYYMMDD"),
					nome:
						aula.modalidadeNome +
						" - " +
						moment(dia).format("DD/MM/YYYY") +
						" | " +
						aula.horarioInicio,
					dia: moment(dia).format("YYYYMMDD"),
				});
			});
		}
		modal.componentInstance.setup(aulaApresentar);
		modal.result.then((retorno) => {
			dadosInserirAluno.aulaDesmarcarId = retorno.id;
			dadosInserirAluno.diaAulaDesmarcar = retorno.dia;
			dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.REPOSICAO;
			this.turmaService
				.marcarAlunoTurma(this.turma.horarioTurmaId, dadosInserirAluno)
				.subscribe((resultReposicao) => {
					if (resultReposicao.messageID) {
						if (resultReposicao.messageID.includes("aulaEstaCheia")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("aulaEstaCheia")
							);
							return;
						}
						if (resultReposicao.messageID.includes("jaMatriculouNaAula")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("jaMatriculouNaAula")
							);
							return;
						}
					}
					if (resultReposicao.status === StatusMarcarAlunoEnum.SUCESSO) {
						this.turmaStateService.updateTurma(resultReposicao.conteudo.turma);
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("inseridoSucesso")
						);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
						);
					}
				});
		});
	}

	private openMarcacaoModalHandler(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any,
		conteudo: any,
		marcacao: boolean
	) {
		if (conteudo.aulasMarcada >= conteudo.saldoCredito) {
			this.snotifyService.error(this.notificacoes.getLabel("validCreditos"));
		} else {
			const modal = this.modalService.open(
				this.vinculoAulaTraducao.getLabel("marcacaoAula"),
				TurmaMarcacaoModalComponent
			);
			modal.componentInstance.aluno = aluno;
			modal.componentInstance.turma = this.turma;
			modal.componentInstance.aulasMarcada = conteudo.aulasMarcada;
			modal.componentInstance.saldoCredito = conteudo.saldoCredito;
			modal.componentInstance.marcacao = marcacao;
			modal.result.then(() => {
				dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.MARCACAO;
				this.turmaService
					.marcarAlunoTurma(this.turma.horarioTurmaId, dadosInserirAluno)
					.subscribe((resultMarcacao) => {
						if (resultMarcacao.messageID) {
							if (resultMarcacao.messageID.includes("aulaEstaCheia")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("aulaEstaCheia")
								);
								return;
							}
							if (resultMarcacao.messageID.includes("jaMatriculouNaAula")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("jaMatriculouNaAula")
								);
								return;
							}
						}
						if (resultMarcacao.status === StatusMarcarAlunoEnum.SUCESSO) {
							this.turmaStateService.updateTurma(resultMarcacao.conteudo.turma);
							this.snotifyService.success(
								this.vinculoAulaTraducao.getLabel("inseridoSucesso")
							);
							this.sessionService.notificarRecursoEmpresa(
								RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
							);
						}
					});
			});
		}
	}

	private openAulaExperimentalProdutoZWModal(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any
	) {
		this.turmaService.obterProdutosZW().subscribe((produtos) => {
			const modal = this.modalService.open(
				this.vinculoAulaTraducao.getLabel("editarExperimental"),
				TurmaAulaExperimentalModalComponent
			);
			modal.componentInstance.aluno = aluno;
			modal.componentInstance.carregarProdutos(produtos, [
				{ id: TipoProdutoZW.DIARIA },
				{ id: TipoProdutoZW.FREEPASS },
			]);
			modal.result.then((produtoId) => {
				dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.AULA_EXPERIMENTAL;
				dadosInserirAluno.produtoId = produtoId;
				this.turmaService
					.marcarAlunoTurma(this.turma.horarioTurmaId, dadosInserirAluno)
					.subscribe((resultAulaExperimental) => {
						if (resultAulaExperimental.messageID) {
							if (resultAulaExperimental.messageID.includes("aulaEstaCheia")) {
								this.snotifyService.error(
									this.notificacoes.getLabel("aulaEstaCheia")
								);
								return;
							}
							if (
								resultAulaExperimental.messageID.includes("jaMatriculouNaAula")
							) {
								this.snotifyService.error(
									this.notificacoes.getLabel("jaMatriculouNaAula")
								);
								return;
							}
						}
						if (
							resultAulaExperimental.conteudo &&
							resultAulaExperimental.conteudo.messageID &&
							resultAulaExperimental.conteudo.messageID.includes(
								"semPermissaoDiariaFreePass"
							)
						) {
							this.snotifyService.error(
								this.notificacoes.getLabel("semPermissaoDiariaFreePass")
							);
							return;
						}
						if (
							resultAulaExperimental.status === StatusMarcarAlunoEnum.SUCESSO
						) {
							this.turmaStateService.updateTurma(
								resultAulaExperimental.conteudo.turma
							);
							this.snotifyService.success(
								this.vinculoAulaTraducao.getLabel("inseridoSucesso")
							);
							this.sessionService.notificarRecursoEmpresa(
								RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
							);
						}
					});
			});
		});
	}

	private openAulaCheiaExperimental(
		dadosInserirAluno: DadosInserirAluno,
		aluno: any,
		result: any
	) {
		if (!result.conteudo.messageID) {
			this.openAulaExperimentalProdutoZWModal(dadosInserirAluno, aluno);
			return;
		}
		const modal = this.modalService.open(
			this.vinculoAulaTraducao.getLabel("editarExperimental"),
			TurmaAulaExperimentalModalComponent
		);
		modal.componentInstance.aluno = aluno;
		if (
			result.conteudo.messageID.toString().includes("aulaExperimentalHorario")
		) {
			modal.componentInstance.carregarBodyAulaCheiaExperimental(
				this.notificacoes.getLabel("foraHorarioPlano") +
					result.conteudo.messageValue +
					this.notificacoes.getLabel("foraHorarioPlano2")
			);
		}
		if (
			result.conteudo.messageID
				.toString()
				.includes("aulaExperimentalModalidade")
		) {
			modal.componentInstance.carregarBodyAulaCheiaExperimental(
				this.notificacoes.getLabel("naoTemModalidadeExperimental") +
					result.conteudo.messageValue +
					this.notificacoes.getLabel("naoTemModalidadeExperimental2")
			);
		}
		modal.result.then(() => {
			dadosInserirAluno.acao = AcaoRequeridaMarcarAluno.AULA_EXPERIMENTAL;
			this.turmaService
				.marcarAlunoTurma(this.turma.horarioTurmaId, dadosInserirAluno)
				.subscribe((resultAulaExperimental) => {
					if (resultAulaExperimental.messageID) {
						if (resultAulaExperimental.messageID.includes("aulaEstaCheia")) {
							this.snotifyService.error(
								this.notificacoes.getLabel("aulaEstaCheia")
							);
							return;
						}
						if (
							resultAulaExperimental.messageID.includes("jaMatriculouNaAula")
						) {
							this.snotifyService.error(
								this.notificacoes.getLabel("jaMatriculouNaAula")
							);
							return;
						}
					}
					if (resultAulaExperimental.status === StatusMarcarAlunoEnum.SUCESSO) {
						this.turmaStateService.updateTurma(
							resultAulaExperimental.conteudo.turma
						);
						this.snotifyService.success(
							this.vinculoAulaTraducao.getLabel("inseridoSucesso")
						);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.AGENDOU_ALUNO_AULA_AGENDA_NTO
						);
					}
				});
		});
	}
}
