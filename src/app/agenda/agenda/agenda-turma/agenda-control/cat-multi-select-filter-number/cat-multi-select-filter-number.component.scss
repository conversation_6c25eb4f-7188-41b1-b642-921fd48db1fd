@import "projects/ui/assets/import.scss";

:host {
	display: flex;
	width: 100%;
}

.host-wrapper {
	display: flex;
	flex-direction: column;
	width: 100%;
}

.pacto-label {
	@extend .type-h6;
	margin-top: 4px;
	color: $gelo04;
	line-height: 28px;
}

.double-arrow {
	position: absolute;
	right: 16px;
	bottom: 13px;
}

.current-value {
	cursor: pointer;
	@extend .type-caption;
	background-color: $branco;
	border: 1px solid $gelo03;
	padding: 2px 36px 0px 3px;
	white-space: nowrap;
	overflow-y: auto;
	border-radius: 4px;
	line-height: 40px;
	min-height: 41px;
	color: $pretoPri;
	display: flex;
	position: relative;
	justify-content: space-between;

	&.error {
		border-color: $hellboyPri;
	}

	&.error.opened {
		border-color: $hellboyPri;
	}

	&.opened {
		border-color: $pretoPri;
	}
}

.current-value {
	cursor: pointer;
	@extend .type-caption;
	background-color: $branco;
	border: 1px solid $gelo03;
	padding: 2px 36px 0px 3px;
	white-space: nowrap;
	overflow-y: auto;
	border-radius: 4px;
	line-height: 40px;
	min-height: 41px;
	color: $pretoPri;
	display: flex;
	position: relative;
	justify-content: space-between;

	&.error {
		border-color: $hellboyPri;
	}

	&.error.opened {
		border-color: $hellboyPri;
	}

	&.opened {
		border-color: $pretoPri;
	}
}

.current-value-wrap {
	cursor: pointer;
	@extend .type-caption;
	background-color: $branco;
	border: 1px solid $gelo03;
	padding: 2px 36px 0px 3px;
	white-space: nowrap;
	overflow-y: auto;
	border-radius: 4px;
	line-height: 40px;
	min-height: 42px;
	color: $pretoPri;
	flex-wrap: wrap;
	display: flex;
	position: relative;
	z-index: 4;

	&.error {
		border-color: $hellboyPri;
	}

	&.error.opened {
		border-color: $hellboyPri;
	}

	&.opened {
		border-color: $pretoPri;
	}
}

.selected-option {
	line-height: 28px;
	overflow: hidden;
	word-break: break-word;

	pacto-cat-person-avatar {
		margin-right: 7.5px;
	}

	.value {
		flex-grow: 1;
		margin-right: 3px;
		overflow: hidden;
		color: $pretoPri;
		text-overflow: ellipsis;
	}

	.pct {
		color: $pretoPri;
		cursor: pointer;
		font-size: 14px;
		padding: 2px;
	}
}

.text-option {
	line-height: 28px;
	border: 1px solid $pretoPri;
	border-radius: 16px;
	overflow: hidden;
	margin: 3px;
	padding: 0px 8px 0px 6px;
	word-break: break-word;
	display: flex;
	align-items: center;
}

.well-footer {
	height: 38px;
	font-family: Nunito Sans;
	font-weight: bold;
	font-size: 18px;
	color: $azulimPri;
	position: relative;
	bottom: 1px;
}

input.filter-input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;
	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

input.cat-select-filter:disabled + .current-value {
	border-color: $cinzaClaroPri;
	background-color: $cinzaClaroPri;
	cursor: not-allowed;
}

.empty-state {
	@extend .type-caption;
	color: $cinzaClaro05;
	margin-top: 15px;
}

.select-placeholder {
	line-height: 32px;
	padding-left: 5px;
}

.footer-multi-select {
	flex-basis: 100%;
	position: relative;
	flex-basis: 1px;
}

.clear-icon {
	position: absolute;
	right: 16px;
	bottom: 14px;
	cursor: pointer;
	color: $pretoPri;
	font-size: 16px;
	z-index: 4;
}

.scroll-container {
	border-bottom: 1px solid $cinzaClaroPri;
	margin-top: 2px;
}

.options {
	position: absolute;
	z-index: 10;
	box-shadow: 0 1px 2px 1px #c0c5d0;
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
	padding: 10px;
	top: 1px;
	left: 3px;
	right: 3px;
	background-color: $branco;

	.option {
		@extend .type-p-small;
		cursor: pointer;
		padding: 8px;
		color: $pretoPri;
		white-space: normal;
		display: flex;
		line-height: 24px;

		pacto-cat-person-avatar {
			margin-right: 15px;
		}

		&:hover {
			background-color: $cinza01;
		}
	}
}
