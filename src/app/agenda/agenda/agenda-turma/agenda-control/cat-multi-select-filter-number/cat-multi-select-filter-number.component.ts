import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	PactoUtilService,
} from "ui-kit";
import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
	ViewChild,
	ElementRef,
	HostListener,
	ChangeDetectorRef,
} from "@angular/core";
import { trigger, style, transition, animate } from "@angular/animations";
import { HttpClient } from "@angular/common/http";
import { FormControl } from "@angular/forms";

import { debounceTime, map, catchError } from "rxjs/operators";
import { Subscription, Observable, of } from "rxjs";

@Component({
	selector: "pacto-cat-multi-select-filter-number",
	templateUrl: "./cat-multi-select-filter-number.component.html",
	styleUrls: ["./cat-multi-select-filter-number.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	animations: [
		trigger("inOut", [
			transition("void => *", [
				animate(
					0,
					style({
						transform: "translateX(-25px) scale(1)",
						opacity: 0,
					})
				),
				animate(
					"100ms 0ms ease-in-out",
					style({
						transform: "translateX(0px) scale(1)",
						opacity: 1,
					})
				),
			]),
			transition("* => void", [
				animate(
					"100ms 0ms ease-in-out",
					style({
						transform: "translateX(25px) scale(1)",
						opacity: 0,
					})
				),
			]),
		]),
	],
})
export class CatMultiSelectFilterNumberComponent implements OnInit {
	@Input() id;
	/**
	 * Full url of endpoint without any GET params.
	 *
	 */
	@Input() endpointUrl;
	@Input() options: any[] = [];
	@Input() control: FormControl;
	@Input() paramBuilder: SelectFilterParamBuilder;
	@Input() resposeParser: SelectFilterResponseParser;
	@Input() placeholder = "-";
	@Input() label;
	@Input() idKey = "id";
	@Input() labelKey = "label";
	@Input() maximo = 2;
	@Input() imageKey;
	@Input() disabled = false;

	@ViewChild("currentElRef", { static: true }) currentElRef: ElementRef;
	@ViewChild("selectArea", { static: true }) selectArea: ElementRef;
	@ViewChild("filter", { static: true }) filter;

	private searchSubscription: Subscription;

	filterFC: FormControl = new FormControl("");
	optionsOpen = false;
	loading = false;

	constructor(
		private compRef: ElementRef,
		private http: HttpClient,
		private cd: ChangeDetectorRef,
		private util: PactoUtilService
	) {}

	get isInvalid() {
		return this.control && this.control.touched && !this.control.valid;
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get currentOptions(): any[] {
		if (this.control && this.control.value && this.control.value.length) {
			return this.control.value;
		} else {
			return [];
		}
	}

	get nofOptions() {
		return this.currentOptions.length;
	}

	get nofOptionsMore() {
		return this.currentOptions.length - this.maximo;
	}

	get filteredOptions() {
		/**
		 * In case of server-side filtering
		 */
		if (this.endpointUrl) {
			const options = Object.assign([], this.options);
			return options.filter((item) => {
				return !this.isSelected(item);
			});

			/**
			 * In case of pre-loaded options
			 */
		} else {
			const options = Object.assign([], this.options);

			return options
				.filter((item) => {
					return this.itemMatchesFilter(item);
				})
				.filter((item) => {
					return !this.isSelected(item);
				});
		}
	}

	private itemMatchesFilter(item): boolean {
		const token = item[this.labelKey];
		let filter = this.filterFC.value;
		filter = filter ? filter : "";
		const regex = new RegExp(`${filter}`, "gi");
		return regex.test(token);
	}

	private isSelected(item): boolean {
		const id = item[this.idKey];
		return this.currentOptions.find((itemI) => {
			return `${itemI[this.idKey]}` === `${id}`;
		});
	}

	ngOnInit() {
		if (!this.control) {
			this.control = new FormControl();
		}
		if (!this.options) {
			this.options = [];
		}
		this.validateId();
		this.control.registerOnDisabledChange((disabled) => {
			if (disabled) {
				this.optionsOpen = false;
			}
		});

		/**
		 * Cancel result callback
		 */
		this.filterFC.valueChanges.subscribe(() => {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
		});

		/**
		 * Trigger search after delay
		 */
		this.filterFC.valueChanges.pipe(debounceTime(500)).subscribe((term) => {
			this.search(term);
		});
		this.control.valueChanges.subscribe(() => {
			this.cd.detectChanges();
		});
	}

	removeOptionHandler(index, $event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();

		if (this.currentOptions[index]) {
			const current: any[] = this.control.value;
			current.splice(index, 1);
			this.control.setValue(current);
		}
	}

	pillClickHandler($event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
	}

	currentClickHandler($event: MouseEvent) {
		if (this.isSelectAreaClick($event) && this.control.enabled) {
			if (!this.optionsOpen) {
				this.open();
			} else {
				this.optionsOpen = false;
			}
		}
	}

	clearAllHandler($event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
		this.control.setValue([]);
	}

	selectOptionHandler(option, $event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
		this.setValue(option);
	}

	@HostListener("document:click", ["$event"])
	clickDocumentHandler($event) {
		if (!this.isComponentClick($event)) {
			this.optionsOpen = false;
		}
	}

	private setValue(value) {
		const current = this.control.value;
		if (current && current.length) {
			current.push(value);
			this.control.setValue(current);
		} else {
			this.control.setValue([value]);
		}
	}

	private open() {
		this.filterFC.setValue("", { emitEvent: false });
		this.search(null);
		this.optionsOpen = true;
		setTimeout(() => {
			this.filter.nativeElement.focus();
		});
	}

	private search(term) {
		if (this.endpointUrl) {
			this.loading = true;
			this.cd.detectChanges();
			this.searchSubscription = this.fetchData(term).subscribe((result) => {
				this.loading = false;
				this.options = result;
				this.cd.detectChanges();
			});
		}
	}

	private fetchData(term: string): Observable<any> {
		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};
		const data = this.http.get(url, { params });

		return data.pipe(
			map((result) => {
				if (this.resposeParser) {
					return this.resposeParser(result);
				} else {
					return result;
				}
			}),
			catchError(() => {
				if (this.resposeParser) {
					return of(this.resposeParser([]));
				} else {
					return of([]);
				}
			})
		);
	}

	private validateId() {
		if (!this.id) {
			this.id = "cat-select-filter-id-" + Math.trunc(Math.random() * 1000);
		}
	}

	private isSelectAreaClick($event: MouseEvent) {
		return this.isComponentClick($event) && !this.isOptionAreaClick($event);
	}

	private isComponentClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.compRef.nativeElement,
			$event.target as Element
		);
	}

	private isOptionAreaClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.selectArea.nativeElement,
			$event.target as Element
		);
	}
}
