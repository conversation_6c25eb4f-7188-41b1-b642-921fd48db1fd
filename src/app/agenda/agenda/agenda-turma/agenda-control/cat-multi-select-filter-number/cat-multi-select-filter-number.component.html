<input [formControl]="control" class="cat-select-filter" type="hidden" />

<div class="host-wrapper">
	<!-- LABEL -->
	<div *ngIf="label" class="pacto-label">
		{{ label }}
	</div>

	<div
		#currentElRef
		(click)="currentClickHandler($event)"
		[ngClass]="optionsOpen === true ? 'current-value-wrap' : 'current-value'"
		id="{{ id }}">
		<img
			*ngIf="!nofOptions"
			class="double-arrow"
			src="pacto-ui/images/double-arrow.svg" />

		<div
			(click)="pillClickHandler($event)"
			*ngFor="let option of currentOptions; let index = index"
			[@inOut]
			class="selected-option">
			<div
				(click)="removeOptionHandler(index, $event)"
				*ngIf="(index < maximo && !optionsOpen) || optionsOpen"
				class="text-option">
				<pacto-cat-person-avatar
					*ngIf="imageKey"
					[diameter]="22"
					[uri]="option[imageKey]"></pacto-cat-person-avatar>
				<div class="value">
					{{ option[labelKey] }}
				</div>
				<i class="pct pct-x"></i>
			</div>
		</div>

		<div [hidden]="optionsOpen || nofOptions < maximo + 1" class="well-footer">
			+{{ nofOptionsMore }}
		</div>

		<div *ngIf="!currentOptions.length" class="select-placeholder">
			{{ placeholder }}
		</div>
	</div>

	<div class="footer-multi-select">
		<i
			(click)="clearAllHandler($event)"
			*ngIf="nofOptions"
			class="pct pct-trash-2 clear-icon"></i>

		<!-- OPTIONS -->
		<div #selectArea [hidden]="!optionsOpen" class="options">
			<input
				#filter
				[formControl]="filterFC"
				class="filter-input"
				id="{{ this.id + '-filter' }}"
				placeholder="Filtro..." />

			<div
				*ngIf="options && options.length"
				[maxHeight]="'250px'"
				class="scroll-container"
				pactoCatSmoothScroll>
				<div
					(click)="selectOptionHandler(option, $event)"
					*ngFor="let option of filteredOptions; let index = index"
					[@inOut]
					class="option"
					id="{{ this.id + '-' + index }}">
					<pacto-cat-person-avatar
						*ngIf="imageKey"
						[diameter]="24"
						[uri]="option[imageKey]"></pacto-cat-person-avatar>
					<span>{{ option[labelKey] }}</span>
				</div>
			</div>

			<div *ngIf="!loading && !filteredOptions.length" class="empty-state">
				Nenhum resultado.
			</div>
		</div>
	</div>
</div>
