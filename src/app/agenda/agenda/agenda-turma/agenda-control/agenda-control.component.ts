import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	OnChanges,
	Output,
	EventEmitter,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";

import { AgendaTurmaStateService } from "../../services/agenda-turma-state.service";
import { AgendaView } from "treino-api";
import { TurmaFilterModalComponent } from "../turma-filter-modal/turma-filter-modal.component";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { SelectFilterResponseParser, SelectFilterParamBuilder } from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";

declare var moment;

export enum AGENDA_VIEW {
	AULAS = "AULAS",
	AGENDAMENTOS = "AGENDAMENTOS",
	PROFESSOR = "PROFESSOR",
}

@Component({
	selector: "pacto-agenda-control",
	templateUrl: "./agenda-control.component.html",
	styleUrls: ["./agenda-control.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AgendaControlComponent implements OnInit, OnChanges {
	@ViewChild("traducoes", { static: true }) traducoes;

	tipoModalidadeFc: FormControl = new FormControl();
	professorFc = new FormControl();
	viewControl = new FormControl();
	agrupamentoControl = new FormControl();
	@Output() calendarState: EventEmitter<boolean> = new EventEmitter();

	modalidadeSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	constructor(
		private router: Router,
		private session: SessionService,
		private modal: ModalService,
		private rest: RestService,
		private state: AgendaTurmaStateService
	) {}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	get _rest() {
		return this.rest;
	}

	get semanaView() {
		return this.state.view === AgendaView.SEMANA;
	}

	get diaView() {
		return this.state.view === AgendaView.DIA;
	}

	get dia() {
		const diaMoment = moment(this.state.dia, "YYYYMMDD");
		return diaMoment.valueOf();
	}

	openCalendarHandler(opened) {
		this.calendarState.emit(opened);
	}

	ngOnInit() {
		this.updateFormControls();

		if (this.state.modalidades) {
			this.tipoModalidadeFc = this.state.modalidades;
		}

		this.tipoModalidadeFc.valueChanges.subscribe((value) => {
			this.state.modalidades = this.tipoModalidadeFc;
			this.state.setModalidade(this.getModalidadeIds(value));
		});

		this.viewControl.valueChanges.subscribe((value) => {
			this.state.setView(value);
		});
	}

	ngOnChanges() {
		this.updateFormControls();
	}

	private updateFormControls() {
		this.viewControl.setValue(this.state.view, { emitEvent: false });
		this.agrupamentoControl.setValue(this.state.agrupamento, {
			emitEvent: false,
		});
	}

	openDatepickerHandler(open) {
		this.calendarState.emit(open);
	}

	dateSelectHandler(event) {
		const diaMoment = moment(event);
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	filterOpenHandler() {
		const modal = this.modal.open(
			this.traducoes.getLabel("filtrosAvancados"),
			TurmaFilterModalComponent
		);
		const filters: TurmaFilterModalComponent = modal.componentInstance;
		filters.filtros = this.state.filtros;
		modal.result.then(
			(result) => {
				this.state.setFiltro(result);
			},
			() => {}
		);
	}

	goBackHandler(days = 1) {
		const dia = this.dia;
		const diaMoment = moment(dia);
		diaMoment.subtract(days, "days");
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	goFowardHandler(days = 1) {
		const dia = this.dia;
		const diaMoment = moment(dia);
		diaMoment.add(days, "days");
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	goToTodayHandler() {
		const diaMoment = moment();
		this.state.setDia(diaMoment.format("YYYYMMDD"));
	}

	loadPeriodoItens() {
		if (this.traducoes.loaded) {
			return [
				{ id: "DIA", label: this.traducoes.getLabel("periodoDia") },
				{ id: "SEMANA", label: this.traducoes.getLabel("periodoSemana") },
			];
		} else {
			return [
				{ id: "DIA", label: "Dia" },
				{ id: "SEMANA", label: "Semana" },
			];
		}
	}

	getModalidadeIds(value: Array<any>): number[] {
		// tslint:disable-next-line: prefer-const
		let ids: Array<number> = new Array<number>();
		if (value) {
			value.forEach((v) => {
				ids.push(v.id);
			});
			console.log(ids);
			return ids;
		}
		return null;
	}

	beta() {
		try {
			this.session.notificarRecursoEmpresa(RecursoSistema.NOVA_AGENDA);
		} catch (e) {}
		this.router.navigate(["agenda", "painel", "cards"]);
	}
}
