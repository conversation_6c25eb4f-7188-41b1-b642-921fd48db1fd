<div style="text-align: center">
	<span class="fa fa-exclamation-triangle line-color-icon-danger">
		<span class="line-danger-text-adjust">
			Orientamos que utilize a nova tela de agenda de aulas porque essa tela
			será descontinuada a partir do dia 01/12/2023 e não poderá mais ser
			acessada.
		</span>
	</span>
</div>
<div class="semana-control-wrapper">
	<div class="titulo-aulas">
		<div class="agenda-title" i18n="@@agenda-aulas:title">Aulas</div>

		<div
			(click)="goToTodayHandler()"
			class="today"
			i18n="@@agenda-aulas:hoje"
			id="visualizar-agenda-hoje">
			HOJE
		</div>

		<pacto-agenda-layout-v2-datepicker
			(dateSelect)="dateSelectHandler($event)"
			(datepickerOpen)="openCalendarHandler($event)"
			[dia]="dia"></pacto-agenda-layout-v2-datepicker>

		<!-- TIME CONTROLS DIA-->
		<ng-container *ngIf="diaView">
			<div class="time-controls">
				<div (click)="goBackHandler()" class="agenda-icon" id="dia-anterior">
					<i class="pct pct-chevron-left"></i>
				</div>
				<div (click)="goFowardHandler()" class="agenda-icon" id="proximo-dia">
					<i class="pct pct-chevron-right"></i>
				</div>
			</div>
		</ng-container>

		<!-- TIME CONTROLS SEMANA -->
		<ng-container *ngIf="semanaView">
			<div class="time-controls">
				<div
					(click)="goBackHandler(7)"
					class="agenda-icon"
					id="semana-anterior">
					<i class="pct pct-chevron-left"></i>
				</div>
				<div
					(click)="goFowardHandler(7)"
					class="agenda-icon"
					id="proxima-semana">
					<i class="pct pct-chevron-right"></i>
				</div>
			</div>
		</ng-container>
	</div>

	<div class="filler"></div>

	<div class="select-modalidades">
		<button
			(click)="beta()"
			class="btn btn-secondary beta"
			title="Ir para uma versão nova da agenda">
			Experimentar nova versão
		</button>
	</div>

	<div class="select-modalidades">
		<pacto-cat-multi-select-filter-number
			[control]="tipoModalidadeFc"
			[endpointUrl]="_rest.buildFullUrl('modalidades')"
			[id]="'filtro-modalidade'"
			[labelKey]="'nome'"
			[paramBuilder]="modalidadeSelectBuilder"
			[placeholder]="'Modalidades'"
			[resposeParser]="responseParser"
			class="filter modalidade"
			i18n-label="
				@@agenda-aulas:labelModalidade"></pacto-cat-multi-select-filter-number>
	</div>

	<div class="titulo-aulas">
		<pacto-cat-select
			[control]="viewControl"
			[id]="'filtro-periodo'"
			[items]="loadPeriodoItens()"
			class="filter view"
			i18n-label="@@agenda-aulas:labelPeriodo"></pacto-cat-select>

		<i
			(click)="filterOpenHandler()"
			class="filtersModal pct pct-sliders"
			id="filter-agenda"></i>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@agenda-aulas:periodoDia" xingling="periodoDia">Dia</span>
	<span i18n="@@agenda-aulas:periodoSemana" xingling="periodoSemana">
		Semana
	</span>
	<span i18n="@@agenda-aulas:tituloFiltroAvancado" xingling="filtrosAvancados">
		Filtros Avançados
	</span>
</pacto-traducoes-xingling>
