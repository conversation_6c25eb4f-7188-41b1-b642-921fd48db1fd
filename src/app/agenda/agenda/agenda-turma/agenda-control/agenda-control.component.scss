$grey-line: #dadada;
@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.line-color-icon-danger {
	background-color: #f2dcde;
	color: #e26062;
	padding: 10px 10px 10px 10px;
	border-radius: 5px;
	margin-top: 15px;
	margin-bottom: 15px;
}

.line-danger-text-adjust {
	margin-left: 5px;
	margin-right: 5px;
	font-size: 14px;
	font-weight: bold;
}

.agenda-title {
	@extend .type-h2-bold;
	color: $pretoPri;
	margin-right: 15px;
}

.semana-control-wrapper {
	display: flex;
	border-bottom: 1px solid #cacaca;
	background-color: $cinzaClaroPri;
	height: 80px;
	padding: 0px 30px;

	.titulo-aulas {
		display: flex;
		align-items: center;
	}

	.select-modalidades {
		pacto-cat-multi-select-filter-number::ng-deep,
		button {
			top: 20px;
			position: relative;
		}
	}
}

.today {
	background-color: $branco;
	border: 1px solid $geloPri;
	line-height: 34px;
	color: $azulimPri;
	text-align: center;
	padding: 0px 15px;
	cursor: pointer;
	@extend .type-btn-bold;
	margin: 0px 15px;
	display: none;
	@media (min-width: $bootstrap-breakpoint-lg) {
		display: block;
	}
}

.time-controls {
	display: flex;
	margin: 0px 15px;
}

.agenda-icon {
	width: 30px;
	height: 30px;
	cursor: pointer;
	line-height: 39px;
	border-radius: 15px;
	background-color: $branco;
	border: 1px solid $gelo02;
	margin: 0px 4px;
	text-align: center;

	.pct {
		font-size: 24px;
		color: $gelo02;
	}
}

.filler {
	flex-grow: 1;
}

.filtersModal {
	font-size: 20px;
	color: $pretoPri;
	cursor: pointer;
	position: relative;
	top: 0px;
}

.filter {
	display: block;
	margin: 0px 15px;

	&.modalidade {
		width: 260px;
	}
}

pacto-cat-select {
	::ng-deep.pacto-label {
		line-height: 1.5;
	}
}
