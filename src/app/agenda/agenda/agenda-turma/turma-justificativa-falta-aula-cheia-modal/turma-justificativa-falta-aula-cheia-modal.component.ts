import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-turma-justificativa-falta-aula-cheia-modal",
	templateUrl: "./turma-justificativa-falta-aula-cheia-modal.component.html",
	styleUrls: ["./turma-justificativa-falta-aula-cheia-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TurmaJustificativaFaltaAulaCheiaModalComponent implements OnInit {
	@ViewChild("validCampo", { static: true })
	validCampo: TraducoesXinglingComponent;
	justificativa: FormControl = new FormControl("", [Validators.required]);
	@Input() texto: string;

	constructor(
		private openModal: NgbActiveModal,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {}

	salvar() {
		this.justificativa.markAsTouched();
		if (this.justificativa.valid) {
			this.openModal.close(this.justificativa.value);
		} else {
			this.snotifyService.error(this.validCampo.getLabel("campoObrigatorio"));
		}
	}
}
