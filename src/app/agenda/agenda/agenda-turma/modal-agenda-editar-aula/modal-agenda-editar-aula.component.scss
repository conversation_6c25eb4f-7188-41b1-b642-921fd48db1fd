@import "projects/ui/assets/ds3/functions";

.mdl-agenda-editar-aula {
	display: block;
	padding: px-to-rem(16px) px-to-rem(16px) px-to-rem(8px) px-to-rem(16px);

	.mdl-agenda-editar-aula-action-row {
		display: flex;
		align-items: center;
		justify-content: flex-end;

		/*
    Desecomentar caso vá habilitar o botão Aula ativa
    ds3-switch {
      margin-right: auto;
    }*/

		button {
			&:not(:last-of-type) {
				margin-right: px-to-rem(8px);
			}
		}
	}

	[class^="col-"] {
		padding-right: unset;
		padding-left: px-to-rem(16px);
	}

	.row {
		margin-right: unset;
		margin-left: unset;
		align-items: center;
		margin-bottom: px-to-rem(16px);

		[class^="col-"] {
			&:first-of-type {
				padding-left: unset;
			}
		}
	}
}

.mdl-agenda-editar-aula-window {
	.modal-dialog {
		&.modal-lg {
			max-width: 1070px;
			width: 1070px;
		}
	}
}
.campo-nome-ajustado input {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 0.875rem;
}
