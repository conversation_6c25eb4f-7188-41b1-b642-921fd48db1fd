<div
	class="mdl-agenda-rplc-alt-aula-title typography-title-4 text-color-typography-default-title">
	<span>Replicar alterações</span>
	<button (click)="dialogRef.close()" ds3-icon-button>
		<i class="pct pct-x"></i>
	</button>
</div>

<form (submit)="save()" [formGroup]="formGroup">
	<ds3-form-field>
		<ds3-field-label>
			Em quais aulas deseja aplicar as alterações
		</ds3-field-label>
		<ds3-select
			[formControl]="formGroup.controls['tipoAula']"
			[options]="optionsTiposAulas"
			ds3Input></ds3-select>
	</ds3-form-field>
	<ds3-form-field
		*ngIf="formGroup.controls['tipoAula'].value === 'DETERMINADA_DATA'">
		<ds3-field-label>*Aplicar mudanças até o dia</ds3-field-label>
		<ds3-input-date
			[control]="formGroup.controls['dataAplicacao']"
			dateType="datepicker"
			ds3Input></ds3-input-date>
	</ds3-form-field>

	<div class="mdl-agenda-replicar-alt-aula-row">
		<button ds3-flat-button>Salvar alterações</button>
	</div>
</form>
