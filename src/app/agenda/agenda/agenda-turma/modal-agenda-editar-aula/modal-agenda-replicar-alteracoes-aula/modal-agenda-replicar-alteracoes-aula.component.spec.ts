import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalAgendaReplicarAlteracoesAulaComponent } from "./modal-agenda-replicar-alteracoes-aula.component";

describe("ModalAgendaReplicarAlteracoesAulaComponent", () => {
	let component: ModalAgendaReplicarAlteracoesAulaComponent;
	let fixture: ComponentFixture<ModalAgendaReplicarAlteracoesAulaComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalAgendaReplicarAlteracoesAulaComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			ModalAgendaReplicarAlteracoesAulaComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
