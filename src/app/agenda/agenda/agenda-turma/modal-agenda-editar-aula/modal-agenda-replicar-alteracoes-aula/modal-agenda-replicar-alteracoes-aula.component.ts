import {
	ChangeDetectionStrategy,
	Component,
	HostBinding,
	Inject,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { AulaCreateEdit, TreinoApiAulaService } from "treino-api";
import { DatePipe } from "@angular/common";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-agenda-replicar-alteracoes-aula",
	templateUrl: "./modal-agenda-replicar-alteracoes-aula.component.html",
	styleUrls: ["./modal-agenda-replicar-alteracoes-aula.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalAgendaReplicarAlteracoesAulaComponent implements OnInit {
	@HostBinding("class.mdl-agenda-replicar-alteracoes-aula")
	enableEncapsulation = true;

	formGroup: FormGroup;
	optionsTiposAulas: Array<{ name: string; value: string }> = new Array<{
		name: string;
		value: string;
	}>();
	aulaCreateEdit: AulaCreateEdit;
	aulaId: number;
	dataAula;

	constructor(
		public dialogRef: MatDialogRef<ModalAgendaReplicarAlteracoesAulaComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any,
		private aulaService: TreinoApiAulaService,
		private datePipe: DatePipe,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.createFormGroup();
		this.populateTiposAulas();
		this.aulaCreateEdit = this.data.aulaCreateEdit;
		this.aulaId = this.data.aulaId;
		this.dataAula = this.data.dataAula;
	}

	private createFormGroup() {
		this.formGroup = new FormGroup({
			tipoAula: new FormControl(null, Validators.required),
			dataAplicacao: new FormControl(),
		});
		this.formGroup.get("tipoAula").valueChanges.subscribe((value) => {
			if (value === "DETERMINADA_DATA") {
				this.formGroup.get("dataAplicacao").setValidators(Validators.required);
			} else {
				this.formGroup.get("dataAplicacao").clearValidators();
			}

			this.formGroup.get("dataAplicacao").updateValueAndValidity();
		});
	}

	private populateTiposAulas() {
		this.optionsTiposAulas = new Array<{ name: string; value: string }>(
			{
				name: "Todas as próximas aulas",
				value: "TODAS",
			},
			{
				name: "Apenas essa e a próxima aula",
				value: "ESSA_PROXIMA",
			},
			{
				name: "Aulas até uma determinada data",
				value: "DETERMINADA_DATA",
			}
		);
	}

	save() {
		if (this.formGroup.invalid) {
			this.snotifyService.error("Há dados obrigatórios não informados");
			return;
		}
		if (!this.formGroup.value.dataAplicacao) {
			this.aulaCreateEdit.tipoEscolhaEdicao =
				this.formGroup.get("tipoAula").value;
			if (this.formGroup.get("tipoAula").value === "DETERMINADA_DATA") {
				this.aulaCreateEdit.diaLimiteTurmaEdicao = this.datePipe.transform(
					this.formGroup.value.dataAplicacao,
					"yyyy/MM/dd"
				);
			}
			this.dialogRef.close(this.formGroup.value);
			return;
		}

		const data1 = new Date(
			this.datePipe.transform(this.formGroup.value.dataAplicacao, "yyyy/MM/dd")
		);
		const rawData2 = this.aulaCreateEdit.diaHorarioTurmaEdicao;
		const data2Formatted = rawData2.replace(
			/(\d{4})(\d{2})(\d{2})/,
			"$1-$2-$3"
		);
		const data2Date = new Date(data2Formatted);
		if (data1 < data2Date) {
			this.snotifyService.error(
				"Não é permitido salvar uma data anterior a data da aula"
			);
			return;
		} else {
			this.aulaCreateEdit.tipoEscolhaEdicao =
				this.formGroup.get("tipoAula").value;
			if (this.formGroup.get("tipoAula").value === "DETERMINADA_DATA") {
				this.aulaCreateEdit.diaLimiteTurmaEdicao = this.datePipe.transform(
					this.formGroup.value.dataAplicacao,
					"yyyy/MM/dd"
				);
			}
			this.dialogRef.close(this.formGroup.value);
		}
	}
}
