@import "projects/ui/assets/scss/cores.vars";
@import "projects/ui/assets/ds3/functions";

.mdl-agenda-replicar-alteracoes-aula {
	display: block;
	width: px-to-rem(600px);

	form {
		padding: px-to-rem(16px);

		ds3-form-field {
			display: block;
			margin-bottom: px-to-rem(16px);
		}
	}

	.mdl-agenda-rplc-alt-aula-title {
		border-bottom: 1px solid $cinza03;
		padding: px-to-rem(13px) px-to-rem(13px) px-to-rem(16px) px-to-rem(16px);
		display: flex;
		align-items: center;

		button {
			margin-left: auto;
		}
	}

	.mdl-agenda-replicar-alt-aula-row {
		display: flex;
		justify-content: center;
	}
}

.mdl-agenda-replicar-alteracoes-aula-window {
	.mat-dialog-container {
		padding: unset;
	}
}
