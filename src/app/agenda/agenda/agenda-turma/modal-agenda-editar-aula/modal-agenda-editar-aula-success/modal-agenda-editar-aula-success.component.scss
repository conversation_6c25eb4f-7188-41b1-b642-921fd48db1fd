@import "projects/ui/assets/ds3/functions";

.mdl-agenda-editar-aula-success {
	display: block;
	padding: px-to-rem(24px) px-to-rem(16px);
	width: px-to-rem(574px);

	.mdl-agenda-feedback {
		padding: px-to-rem(8px) px-to-rem(16px);
		display: flex;
		align-items: center;
		border-radius: 5px;

		.mdl-agenda-feedback-icon {
			margin-right: px-to-rem(8.28px);
		}

		.mdl-agenda-feedback-message {
		}
	}

	.mdl-agenda-feedback-info {
		margin-top: px-to-rem(16px);
		margin-bottom: px-to-rem(16px);
		text-align: center;

		.mdl-agenda-feedback-info-message-title {
			margin-top: px-to-rem(16px);
			margin-bottom: px-to-rem(16px);
		}
	}

	.mdl-agenda-feedback-btn-row {
		display: flex;

		button {
			width: 100%;

			&:not(:last-of-type) {
				margin-right: px-to-rem(16px);
			}
		}
	}
}

.modal-agenda-editar-aula-success-window {
	.mat-dialog-container {
		padding: unset;
	}
}

.mdl-agenda-rplc-alt-aula-title {
	border-bottom: 1px solid #c7c9cc;
	padding-bottom: 10px;
	display: flex;
	align-items: center;

	button {
		margin-left: auto;
	}
}
.typography-title-4 {
}
