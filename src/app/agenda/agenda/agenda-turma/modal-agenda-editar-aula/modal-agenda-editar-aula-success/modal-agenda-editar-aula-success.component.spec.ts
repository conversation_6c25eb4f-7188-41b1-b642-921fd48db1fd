import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalAgendaEditarAulaSuccessComponent } from "./modal-agenda-editar-aula-success.component";

describe("ModalAgendaEditarAulaSuccessComponent", () => {
	let component: ModalAgendaEditarAulaSuccessComponent;
	let fixture: ComponentFixture<ModalAgendaEditarAulaSuccessComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalAgendaEditarAulaSuccessComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalAgendaEditarAulaSuccessComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
