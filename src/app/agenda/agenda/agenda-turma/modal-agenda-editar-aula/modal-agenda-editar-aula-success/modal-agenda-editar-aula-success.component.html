<div
	class="mdl-agenda-rplc-alt-aula-title typography-title-4 text-color-typography-default-title">
	<button (click)="dialogRef.close()" ds3-icon-button>
		<i class="pct pct-x"></i>
	</button>
</div>

<div
	class="mdl-agenda-feedback text-color-feedback-alert-3 background-color-feedback-alert-1">
	<div class="mdl-agenda-feedback-icon">
		<i class="pct pct-alert-triangle"></i>
	</div>
	<div class="mdl-agenda-feedback-message typography-body-2">
		Com as mudan<PERSON><PERSON>, alguns alunos agendados podem não atender mais aos
		critérios. No entanto, manteremos esses alunos nas aulas porque já estavam
		agendados.
	</div>
</div>
<br />
<div
	class="mdl-agenda-feedback text-color-feedback-alert-3 background-color-feedback-alert-1">
	<div class="mdl-agenda-feedback-icon">
		<i class="pct pct-alert-triangle"></i>
	</div>
	<div class="mdl-agenda-feedback-message typography-body-2">
		A Alteração do professor não reflete no relatório de “Professores
		Substituídos”.
	</div>
</div>

<div class="mdl-agenda-feedback-info">
	<div class="mdl-agenda-feedback-info-image">
		<img
			alt="Uma imagem de uma prancheta"
			height="112"
			src="pacto-ui/images/empty-state-turma.svg"
			width="112" />
	</div>
	<div class="mdl-agenda-feedback-info-message-title typography-title-4">
		<!--		Aula alterada com sucesso!!-->
	</div>
	<div class="mdl-agenda-feedback-info-message typography-body-1">
		Você deseja aplicar essa edição nas próximas aulas também?
	</div>
</div>

<div class="mdl-agenda-feedback-btn-row">
	<button (click)="aplicarAulasFuturas()" ds3-outlined-button size="sm">
		Sim, aplicar nas próximas aulas
	</button>
	<button (click)="aplicarApenasNestaAula()" ds3-flat-button size="sm">
		Não, deixar a edição apenas nesta aula
	</button>
</div>
