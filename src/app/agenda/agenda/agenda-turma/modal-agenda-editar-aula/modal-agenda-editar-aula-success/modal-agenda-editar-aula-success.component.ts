import {
	AfterViewInit,
	ChangeDetectionStrategy,
	Component,
	HostBinding,
	Inject,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";
import {
	MAT_DIALOG_DATA,
	MatDialog,
	MatDialogRef,
} from "@angular/material/dialog";
import { ModalAgendaReplicarAlteracoesAulaComponent } from "../modal-agenda-replicar-alteracoes-aula/modal-agenda-replicar-alteracoes-aula.component";

@Component({
	selector: "pacto-modal-agenda-editar-aula-success",
	templateUrl: "./modal-agenda-editar-aula-success.component.html",
	styleUrls: ["./modal-agenda-editar-aula-success.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalAgendaEditarAulaSuccessComponent
	implements OnInit, AfterViewInit
{
	@HostBinding("class.mdl-agenda-editar-aula-success")
	enableEncapsulation = true;

	constructor(
		public dialogRef: MatDialogRef<ModalAgendaEditarAulaSuccessComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any,
		private matDialog: MatDialog
	) {}

	ngOnInit() {}

	ngAfterViewInit() {}

	aplicarAulasFuturas() {
		const dialogRef = this.matDialog.open(
			ModalAgendaReplicarAlteracoesAulaComponent,
			{
				panelClass: "mdl-agenda-replicar-alteracoes-aula-window",
				data: this.data,
			}
		);
		dialogRef.afterClosed().subscribe((result) => {
			this.dialogRef.close({ acao: "aplicarAulasFuturas", content: result });
		});
	}

	aplicarApenasNestaAula() {
		this.dialogRef.close({ acao: "aplicarApenasNestaAula" });
	}

	cancel() {
		this.dialogRef.close({ acao: "cancelarEdicao" });
	}
}
