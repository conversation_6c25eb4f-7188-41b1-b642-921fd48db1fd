import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
	AulaFiltro,
	TipoAula,
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
	TreinoApiAlunosService,
	TreinoApiModalidadeService,
} from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { Observable, zip, of } from "rxjs";
import { map } from "rxjs/operators";
import { SelectFilterParamBuilder, SelectFilterResponseParser } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";

@Component({
	selector: "pacto-turma-filter-modal",
	templateUrl: "./turma-filter-modal.component.html",
	styleUrls: ["./turma-filter-modal.component.scss"],
})
export class TurmaFilterModalComponent implements OnInit {
	@ViewChild("traducoes", { static: true }) traducoes;

	@Input() filtros: AulaFiltro;

	fg = new FormGroup({
		professor: new FormControl(),
		ambiente: new FormControl(),
		aluno: new FormControl(),
		tipoDuracao: new FormControl([0]),
		disponibilidade: new FormControl([0]),
		tipo: new FormControl(),
		modalidade: new FormControl(),
	});
	tipoDuracoes = [
		{ id: 0, label: "Todos" },
		{ id: 1, label: "Livre" },
		{ id: 2, label: "Pré-definido" },
		{ id: 3, label: "Play" },
		{ id: 4, label: "Intervalo de tempo" },
	];
	disponibilidades = [
		{ id: 0, label: "Todos" },
		{ id: 1, label: "Disponível" },
		{ id: 2, label: "Indisponível" },
	];
	professorSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	alunoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	ambienteSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	modalidadeSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	constructor(
		private modal: NgbActiveModal,
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private ambienteService: TreinoApiAmbienteService,
		private alunoService: TreinoApiAlunosService,
		private modalidadeService: TreinoApiModalidadeService
	) {}

	ngOnInit() {
		const profId = this.filtros ? this.filtros.professoresIds : [];
		const ambId = this.filtros ? this.filtros.ambientesIds : [];
		const aluId = this.filtros ? this.filtros.alunosIds : [];
		const modalidadeId = this.filtros ? this.filtros.modalidadesIds : [];
		const tipos = this.filtros ? this.filtros.tipo : TipoAula.TODAS;
		let professor;
		let ambiente;
		let aluno;
		let modalidade;

		const todosTipos = tipos === TipoAula.TODAS || tipos === null;
		const professor$ = this.fetchProfessor(profId.length ? profId[0] : null);
		const ambiente$ = this.fetchAmbiente(ambId.length ? ambId[0] : null);
		const aluno$ = this.fetchAluno(aluId.length ? aluId[0] : null);
		const modalidade$ = this.fetchModalidade(
			modalidadeId.length ? modalidadeId[0] : null
		);
		return zip(professor$, ambiente$, aluno$, modalidade$).subscribe(
			(resultado) => {
				professor = resultado[0];
				ambiente = resultado[1];
				aluno = resultado[2];
				modalidade = resultado[3];

				this.fg.setValue({
					professor: profId.length ? professor : null,
					ambiente: ambId.length ? ambiente : null,
					aluno: aluId.length ? aluno : null,
					tipoDuracao:
						this.filtros &&
						this.filtros.tipoDuracao &&
						this.filtros.tipoDuracao[0] > 0
							? this.filtros.tipoDuracao
							: [0],
					disponibilidade:
						this.filtros &&
						this.filtros.disponibilidade &&
						this.filtros.disponibilidade[0] > 0
							? this.filtros.disponibilidade
							: [0],
					tipo: todosTipos ? TipoAula.TODAS : this.getTipoAula(tipos),
					modalidade: modalidadeId.length ? modalidade : null,
				});
			}
		);
	}

	private fetchProfessor(id): Observable<any> {
		if (id) {
			return this.colaboradorService.obterColaborador(id, false);
		} else {
			return of(null);
		}
	}

	private fetchAluno(id): Observable<any> {
		if (id) {
			return this.alunoService.obterAluno(id);
		} else {
			return of(null);
		}
	}

	private fetchModalidade(id): Observable<any> {
		if (id) {
			return this.modalidadeService.obterModalidade(id);
		} else {
			return of(null);
		}
	}

	private fetchAmbiente(id): Observable<any> {
		if (id) {
			return this.ambienteService.obterTodosAmbientes().pipe(
				map((response) => {
					return response.content.find((ambiente) => ambiente.id === id);
				})
			);
		} else {
			return of(null);
		}
	}

	confirmHandler() {
		const filtro = Object.assign({}, this.filtros);
		const state = this.fg.getRawValue();
		filtro.tipo = this.getTipoAula(state.tipo);
		filtro.professoresIds = state.professor ? [state.professor.id] : [];
		filtro.ambientesIds = state.ambiente ? [state.ambiente.id] : [];
		filtro.alunosIds = state.aluno ? [state.aluno.id] : [];
		filtro.tipoDuracao = state.tipoDuracao ? [state.tipoDuracao] : [0];
		filtro.disponibilidade = state.disponibilidade
			? [state.disponibilidade]
			: [0];
		filtro.modalidadesIds = state.modalidade ? [state.modalidade.id] : [];
		this.modal.close(filtro);
	}

	private getTipoAula(value: string) {
		if (value === TipoAula.TODAS) {
			return TipoAula.TODAS;
		} else if (value === TipoAula.TURMA_ZW) {
			return TipoAula.TURMA_ZW;
		} else if (value === TipoAula.AULA_COLETIVA) {
			return TipoAula.AULA_COLETIVA;
		}
	}

	loadTipoItens() {
		if (this.traducoes.loaded) {
			return [
				{ id: "TODAS", label: this.traducoes.getLabel("tipoTodos") },
				{ id: "ZW", label: this.traducoes.getLabel("tipoTurma") },
				{ id: "AC", label: this.traducoes.getLabel("tipoAula") },
			];
		} else {
			return [
				{ id: "TODAS", label: "Todos" },
				{ id: "ZW", label: "Turma" },
				{ id: "AC", label: "Aula" },
			];
		}
	}
}
