<pacto-cat-select-filter
	[control]="fg.get('ambiente')"
	[endpointUrl]="_rest.buildFullUrl('ambientes')"
	[id]="'filtro-ambiente'"
	[labelKey]="'nome'"
	[paramBuilder]="ambienteSelectBuilder"
	[resposeParser]="responseParser"
	class="filtro"
	i18n-label="@@agenda-turma-filter:ambiente"
	label="Ambiente"></pacto-cat-select-filter>

<pacto-cat-select-filter
	[control]="fg.get('professor')"
	[endpointUrl]="_rest.buildFullUrl('colaboradores/professores-aulas/all')"
	[id]="'filtro-professor'"
	[labelKey]="'nome'"
	[paramBuilder]="professorSelectBuilder"
	[resposeParser]="responseParser"
	class="filtro"
	i18n-label="@@agenda-turma-filter:professor"
	label="Professor"></pacto-cat-select-filter>

<pacto-cat-select-filter
	[control]="fg.get('aluno')"
	[endpointUrl]="_rest.buildFullUrl('alunos')"
	[id]="'filtro-aluno'"
	[labelKey]="'nome'"
	[paramBuilder]="alunoSelectBuilder"
	[resposeParser]="responseParser"
	class="filtro"
	i18n-label="@@agenda-turma-filter:aluno"
	label="Aluno"></pacto-cat-select-filter>

<pacto-cat-select
	[control]="fg.get('disponibilidade')"
	[id]="'filtro-disponibilidade'"
	[items]="disponibilidades"
	class="filtro"
	i18n-label="@@agenda-turma-filter:disponibilidade"
	label="Disponibilidade"></pacto-cat-select>

<pacto-cat-select
	[control]="fg.get('tipoDuracao')"
	[id]="'filtro-duracao'"
	[items]="tipoDuracoes"
	class="filtro"
	i18n-label="@@agenda-turma-filter:duracoes"
	label="Duração"></pacto-cat-select>

<pacto-cat-select
	[control]="fg.get('tipo')"
	[id]="'filtro-tipo'"
	[items]="loadTipoItens()"
	class="filtro"
	i18n-label="@@agenda-turma-filter:tipo"
	label="Tipo de agendamento"></pacto-cat-select>

<pacto-cat-select-filter
	[control]="fg.get('modalidade')"
	[endpointUrl]="_rest.buildFullUrl('modalidades')"
	[id]="'filtro-modalidade'"
	[labelKey]="'nome'"
	[paramBuilder]="modalidadeSelectBuilder"
	[resposeParser]="responseParser"
	class="filtro"
	i18n-label="@@agenda-turma-filter:modalidade"
	label="Modalidade"></pacto-cat-select-filter>

<div class="action-wrapper">
	<pacto-cat-button
		(click)="confirmHandler()"
		[id]="'btn-aplicar-filtro'"
		i18n-label="@@agenda-turma-filter:aplicarFiltro"
		label="Aplicar Filtro"></pacto-cat-button>
</div>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@agenda-turma-filter:tipoTodos" xingling="tipoTodos">Todos</span>
	<span i18n="@@agenda-turma-filter:tipoTurma" xingling="tipoTurma">Turma</span>
	<span i18n="@@agenda-turma-filter:tipoAula" xingling="tipoAula">Aula</span>
</pacto-traducoes-xingling>
