import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { TipoAgendamentoListaComponent } from "./components/tipo-agendamento-lista/tipo-agendamento-lista.component";
import { TipoAgendamentoEditComponent } from "./components/tipo-agendamento-edit/tipo-agendamento-edit.component";
import { TipoAgendamentoFiltroComponent } from "./components/tipo-agendamento-filtro/tipo-agendamento-filtro.component";
import { TipoAgendamentoColorModalComponent } from "./components/tipo-agendamento-color-modal/tipo-agendamento-color-modal.component";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";

const recurso = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.TIPO_EVENTO, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: TipoAgendamentoListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: TipoAgendamentoEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":novo",
		component: TipoAgendamentoEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), CommonModule, BaseSharedModule],
	entryComponents: [TipoAgendamentoColorModalComponent],
	declarations: [
		TipoAgendamentoListaComponent,
		TipoAgendamentoEditComponent,
		TipoAgendamentoFiltroComponent,
		TipoAgendamentoColorModalComponent,
	],
})
export class TipoAgendamentoModule {}
