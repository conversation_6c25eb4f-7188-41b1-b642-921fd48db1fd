<ng-template #cardTitle>
	<span
		*ngIf="entity"
		i18n="@@crud-tipos-Agendamento:criar-tipo-agendamento:title">
		Criar Tipo Agendamento
	</span>
	<span
		*ngIf="!entity"
		i18n="@@crud-tipos-Agendamento:editar-tipo-agendamento:title">
		Editar Tipo Agendamento
	</span>
</ng-template>
<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'TIPO-AGENDAMENTO',
			menuLink: ['agenda', 'tipo-agendamento']
		}"></pacto-breadcrumbs>
	<pacto-title-card [title]="cardTitle">
		<div class="row">
			<div class="col-md-12">
				<pacto-input
					[control]="formGroup.get('nome')"
					[id]="'nome-tipo-agendamento-input'"
					i18n-label="@@crud-tipo-agendamento:edit:nome"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:nome-mensagem:label"
					label="Nome do Tipo Agendamento"
					mensagem="Informe um nome para o timpo de agendamento"
					name="tpAgendamento"></pacto-input>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-input
					[control]="formGroup.get('nrAgendamentos')"
					[id]="'nr-tipo-agendamento-input'"
					i18n-label="@@crud-tipos-Agendamento:create-edit:nragendamentos:label"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:nragendamentos-mensagem:label"
					label="Número de agendamentos"
					name="nrAgendamentos"
					type="number"></pacto-input>
				<pacto-input
					[control]="formGroup.get('dias')"
					[id]="'dias-tipo-agendamento-input'"
					i18n-label="@@crud-tipos-Agendamento:create-edit:dias:label"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:dias-mensagem:label"
					label="Em dias"
					mensagem="Informe o número de dias"
					name="dias"
					type="number"></pacto-input>
				<pacto-input
					[control]="formGroup.get('intervaloMinimoFalta')"
					[id]="'min-falta-tipo-agendamento-input'"
					i18n-label="@@crud-tipos-Agendamento:create-edit:faltas:label"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:faltas-mensagem:label"
					label="Intervalo mínimo em dias em caso de falta"
					mensagem="Informe o número de faltas"
					name="intMinFalta"
					type="number"></pacto-input>
				<div
					[ngClass]="{
						'has-danger': showError
					}"
					class="form-group">
					<label
						class="control-label"
						i18n="@@crud-tipos-Agendamento:create-edit:duracao:label">
						Duração
					</label>
					<select
						[formControl]="formGroup.get('duracao')"
						class="form-control form-control-sm"
						id="select-tipo-duracao">
						<option
							*ngFor="let tipoDuracao of TipoDuracaoEvento"
							value="{{ tipoDuracao.valor }}">
							<ng-container
								*ngTemplateOutlet="
									traducaoTipoDuracoes;
									context: { traducaoTipoDuracao: tipoDuracao.id }
								"></ng-container>
						</option>
					</select>
					<small
						*ngIf="showError"
						class="form-control-feedback"
						i18n="@@crud-tipos-Agendamento:create-edit:duracao-mensagem:label">
						Informe uma duração
					</small>
				</div>
				<pacto-input
					*ngIf="formGroup.get('duracao').value === 'DURACAO_PREDEFINIDA'"
					[control]="formGroup.get('duracaoFixa')"
					[id]="'input-duracao-pre-definida'"
					[textMask]="{ mask: timeMask, guide: true }"
					i18n-label="@@crud-tipos-Agendamento:create-edit:tempo:label"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:tempo-mensagem:label"
					label="Tempo em horas e minutos"
					mensagem="Informe um tempo"
					name="Duração"></pacto-input>
				<pacto-input
					*ngIf="formGroup.get('duracao').value === 'INTERVALO_DE_TEMPO'"
					[control]="formGroup.get('duracaoMinima')"
					[textMask]="{ mask: timeMask, guide: true }"
					i18n-label="@@crud-tipos-Agendamento:create-edit:minimo:label"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:tempo-mensagem:label"
					label="Mínimo em horas e minutos"
					mensagem="Informe um tempo"
					name="Minimo"></pacto-input>
				<pacto-input
					*ngIf="formGroup.get('duracao').value === 'INTERVALO_DE_TEMPO'"
					[control]="formGroup.get('duracaoMaxima')"
					[textMask]="{ mask: timeMask, guide: true }"
					i18n-label="@@crud-tipos-Agendamento:create-edit:maximo:label"
					i18n-mensagem="
						@@crud-tipos-Agendamento:create-edit:tempo-mensagem:label"
					label="Máximo em horas e minutos"
					mensagem="Informe um tempo"
					name="Maximo"></pacto-input>
			</div>
			<div class="col-md-6">
				<div
					[ngClass]="{
						'has-danger': showErrorComportamento
					}"
					class="form-group">
					<label
						class="control-label"
						i18n="@@crud-tipos-Agendamento:create-edit:comportamento:label">
						Comportamentos
					</label>
					<select
						[formControl]="formGroup.get('comportamento')"
						class="form-control form-control-sm"
						id="select-comportamento">
						<option
							*ngFor="let tipoAgendamento of TipoAgendamentoEnum"
							value="{{ tipoAgendamento.id }}">
							<ng-container
								*ngTemplateOutlet="
									traducaoTipoAgendamentos;
									context: { traducaoTipoAgendamento: tipoAgendamento.id }
								"></ng-container>
						</option>
					</select>
					<small
						*ngIf="showErrorComportamento"
						class="form-control-feedback"
						i18n="
							@@crud-tipos-Agendamento:create-edit:comportamento-mensagem:label">
						Informe um comportamento
					</small>
				</div>
				<div class="row row-form">
					<button
						(click)="changeColor()"
						class="btn btn-secondary botaoSelectCor"
						id="btn-selecione-cor">
						<div class="row">
							<div
								*ngIf="colorSelected"
								[ngStyle]="{ 'background-color': colorSelected }"
								class="campo-cor icon-color"></div>
							<span
								i18n="@@crud-tipos-Agendamento:create-edit:button-color:label">
								Selecione uma cor
							</span>
						</div>
					</button>
				</div>
				<div class="form-group row-form-check-box">
					<div class="form-check">
						<input
							[formControl]="formGroup.get('apenasAlunosCarteira')"
							class="form-check-input"
							id="defaultCheck2"
							type="checkbox" />
						<label
							class="form-check-label"
							for="defaultCheck2"
							i18n="@@crud-tipos-Agendamento:create-edit:carteira:label">
							Só alunos na carteira do professor
						</label>
					</div>
				</div>
				<div class="form-group row-form-check-box">
					<div class="form-check">
						<input
							[formControl]="formGroup.get('ativo')"
							class="form-check-input"
							id="status-tipo-agendamento"
							type="checkbox" />
						<label
							class="form-check-label"
							for="status-tipo-agendamento"
							i18n="@@crud-tipos-Agendamento:create-edit:ativo:label">
							Ativo
						</label>
					</div>
				</div>
				<div class="form-group row-form-check-box">
					<div class="form-check">
						<input
							(click)="changeDuracao()"
							[formControl]="formGroup.get('permitirApp')"
							class="form-check-input"
							id="permitir-app"
							type="checkbox" />
						<label class="form-check-label" for="permitir-app">
							Permite aluno marcar pelo app.
						</label>
					</div>
				</div>
			</div>
		</div>
		<br />
		<div class="actions">
			<button
				(click)="submitHandler()"
				class="btn btn-primary"
				i18n="@@buttons:salvar"
				id="btn-add-tipo-agendamento">
				Salvar
			</button>
			<button
				(click)="calcelHandler()"
				class="btn btn-secondary"
				i18n="@@buttons:cancelar">
				Cancelar
			</button>
			<pacto-log *ngIf="entry?.id" [url]="urlLog"></pacto-log>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<span
	#createSuccess
	[hidden]="true"
	i18n="@@crud-tipos-agendamentos:createSuccess">
	Tipo agendamento criado com sucesso.
</span>
<span #editSuccess [hidden]="true" i18n="@@crud-tipos-agendamentos:editSuccess">
	Tipo agendamento editado com sucesso.
</span>
<span
	#selecioneUmaCor
	[hidden]="true"
	i18n="@@crud-tipos-agendamentos:selecioneCor">
	Selecione uma cor.
</span>
<span
	#validMensage
	[hidden]="true"
	i18n="@@crud-tipos-agendamentos:validMensage">
	Campos obrigatórios não preenchido.
</span>
<span
	#horarioInvalid
	[hidden]="true"
	i18n="@@crud-tipos-agendamentos:horario-invalid">
	Horário inválido.
</span>

<ng-template
	#traducaoTipoDuracoes
	let-traducaoTipoDuracao="traducaoTipoDuracao">
	<ng-container [ngSwitch]="traducaoTipoDuracao">
		<span
			*ngSwitchCase="'DURACAO_LIVRE'"
			i18n="@@crud-tipos-agendamentos:option-select:duracao-livre">
			Duração livre
		</span>
		<span
			*ngSwitchCase="'DURACAO_PREDEFINIDA'"
			i18n="@@crud-tipos-agendamentos:option-select:duracao-predefinida">
			Duração predefinida
		</span>
		<span
			*ngSwitchCase="'INTERVALO_DE_TEMPO'"
			i18n="@@crud-tipos-agendamentos:option-select:intervalo-tempo">
			Intervalo de tempo
		</span>
	</ng-container>
</ng-template>

<ng-template
	#traducaoTipoAgendamentos
	let-traducaoTipoAgendamento="traducaoTipoAgendamento">
	<ng-container [ngSwitch]="traducaoTipoAgendamento">
		<span
			*ngSwitchCase="'CONTATO_INTERPESSOAL'"
			i18n="@@crud-tipos-agendamentos:option-select:contato-interpessoal">
			Contato interpessoal
		</span>
		<span
			*ngSwitchCase="'PRESCRICAO_TREINO'"
			i18n="@@crud-tipos-agendamentos:option-select:prescricao-treino">
			Prescrição treino
		</span>
		<span
			*ngSwitchCase="'REVISAO_TREINO'"
			i18n="@@crud-tipos-agendamentos:option-select:revisao">
			Revisão
		</span>
		<span
			*ngSwitchCase="'RENOVAR_TREINO'"
			i18n="@@crud-tipos-agendamentos:option-select:renovar-treino">
			Renovar treino
		</span>
		<span
			*ngSwitchCase="'AVALIACAO_FISICA'"
			i18n="@@crud-tipos-agendamentos:option-select:avaliacao-fisica">
			Avaliação fisica
		</span>
	</ng-container>
</ng-template>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span
		i18n="@@crud-tipo-agendamento:exite_agendamento_futuros"
		xingling="exite_agendamento_futuros">
		Não é possível inativar esse tipo de agendamento pois existem agendamentos
		futuros.
	</span>
	<span
		i18n="@@crud-tipo-agendamento:erro_tentar_atualizar_tipo_agendamento"
		xingling="erro_tentar_atualizar_tipo_agendamento">
		Erro ao editar tipo de agendamento.
	</span>
</pacto-traducoes-xingling>
