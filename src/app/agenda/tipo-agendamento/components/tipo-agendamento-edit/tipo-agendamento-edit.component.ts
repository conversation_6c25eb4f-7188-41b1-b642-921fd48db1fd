import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { TipoAgendamentoColorModalComponent } from "../tipo-agendamento-color-modal/tipo-agendamento-color-modal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	FormGroup,
	FormControl,
	Validators,
	AbstractControl,
	ValidationErrors,
} from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { TipoAgendamento, TreinoApiAgendaAgendamentoService } from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-tipo-agendamento-edit",
	templateUrl: "./tipo-agendamento-edit.component.html",
	styleUrls: ["./tipo-agendamento-edit.component.scss"],
})
export class TipoAgendamentoEditComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("selecioneUmaCor", { static: true }) selecioneUmaCor;
	@ViewChild("validMensage", { static: true }) validMensage;
	@ViewChild("horarioInvalid", { static: true }) horarioInvalid;

	operation = "create";
	TipoAgendamentoEnum: Array<any> = [
		{ id: "CONTATO_INTERPESSOAL", nome: "Contato interpessoal" },
		{ id: "PRESCRICAO_TREINO", nome: "Prescrição treino" },
		{ id: "REVISAO_TREINO", nome: "Revisão" },
		{ id: "RENOVAR_TREINO", nome: "Renovar treino" },
		{ id: "AVALIACAO_FISICA", nome: "Avaliação fisica" },
	];

	TipoDuracaoEvento: Array<any> = [
		{ id: "DURACAO_LIVRE", nome: "Duração livre", valor: "DURACAO_LIVRE" },
		{
			id: "DURACAO_PREDEFINIDA",
			nome: "Duração predefinida",
			valor: "DURACAO_PREDEFINIDA",
		},
		{
			id: "INTERVALO_DE_TEMPO",
			nome: "intervalo de tempo",
			valor: "INTERVALO_DE_TEMPO",
		},
	];

	hasColorSelected = false;
	colorSelected;
	entity = true;
	entry: TipoAgendamento;
	tipoAgendamento: TipoAgendamento = {};
	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		duracaoMaxima: new FormControl(null, [validadorIntervaloTempoMax]),
		duracaoMinima: new FormControl(null, [validadorIntervaloTempoMin]),
		duracaoFixa: new FormControl(null, [validadorDuracaoFixa]),
		ativo: new FormControl(false),
		comportamento: new FormControl("CONTATO_INTERPESSOAL", Validators.required),
		duracao: new FormControl("DURACAO_LIVRE", Validators.required),
		nrAgendamentos: new FormControl(null),
		dias: new FormControl(null),
		intervaloMinimoFalta: new FormControl(null),
		apenasAlunosCarteira: new FormControl(false),
		permitirApp: new FormControl(false),
	});

	timeMask = [/\d/, /\d/, ":", /\d/, /\d/];

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private modal: NgbModal,
		private service: TreinoApiAgendaAgendamentoService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.formGroup.get("duracao").valueChanges.subscribe((value) => {
			if (this.formGroup.get("permitirApp").value) {
				this.formGroup
					.get("duracao")
					.patchValue("DURACAO_LIVRE", { emitEvent: false });
				this.snotifyService.error(
					"Apenas o modo Duração Livre é permitido, pois a opção “Permite aluno marcar pelo app” está ativa"
				);
			} else {
				this.formGroup.get("duracaoFixa").updateValueAndValidity();
				this.formGroup.get("duracaoMaxima").updateValueAndValidity();
				this.formGroup.get("duracaoMinima").updateValueAndValidity();
			}
		});

		this.route.params.subscribe((params) => {
			if (params.id && params.id !== "novo") {
				this.operation = "edit";
				this.loadEditedEntity(params.id);
			} else {
				this.operation = "create";
			}
		});
	}

	changeColor() {
		const modalref = this.modal.open(TipoAgendamentoColorModalComponent);
		modalref.componentInstance.selectedColor = null;
		modalref.result.then((result) => {
			this.colorSelected = result.valor;
			this.hasColorSelected = !this.hasColorSelected;
		});
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/tipo-agendamento/${this.entry.id}`);
	}

	changeDuracao() {
		if (!this.formGroup.get("permitirApp").value) {
			this.formGroup.get("duracao").setValue("DURACAO_LIVRE");
		}
	}

	calcelHandler() {
		this.router.navigate(["agenda", "tipo-agendamento"]);
	}

	submitHandler() {
		this.crateHandler();
	}

	private crateHandler() {
		this.markAsToched();
		if (this.colorSelected) {
			if (this.formGroup.valid) {
				if (this.validHoursFormControl()) {
					this.setTipoAgendamento();
					if (this.entry) {
						this.updateHandler();
					} else {
						this.createHandler();
					}
				} else {
					const horarioInvalid = this.horarioInvalid.nativeElement.innerHTML;
					this.snotifyService.error(horarioInvalid);
				}
			} else {
				const validMensage = this.validMensage.nativeElement.innerHTML;
				this.snotifyService.error(validMensage);
			}
		} else {
			const message = this.selecioneUmaCor.nativeElement.innerHTML;
			this.snotifyService.error(message);
		}
	}

	private validHoursFormControl() {
		let hora = "";
		if (this.formGroup.get("duracao").value === "DURACAO_PREDEFINIDA") {
			hora = this.formGroup
				.get("duracaoFixa")
				.value.toString()
				.replace(/_/g, "");
			if (hora.length < 5) {
				return false;
			}
		} else if (this.formGroup.get("duracao").value === "INTERVALO_DE_TEMPO") {
			hora = this.formGroup
				.get("duracaoMinima")
				.value.toString()
				.replace(/_/g, "");
			if (hora.length < 5) {
				return false;
			} else {
				hora = this.formGroup
					.get("duracaoMaxima")
					.value.toString()
					.replace(/_/g, "");
				if (hora.length < 5) {
					return false;
				}
			}
		}
		return true;
	}

	loadEditedEntity(id) {
		this.entity = false;
		this.service.obterTipoAgendamento(id).subscribe((result) => {
			this.entry = result;
			this.formGroup.get("nome").setValue(result.nome);
			this.formGroup
				.get("duracaoMaxima")
				.setValue(this.getDuracao(result.duracao_intervalo_superior));
			this.formGroup
				.get("duracaoMinima")
				.setValue(this.getDuracao(result.duracao_intervalo_inferior));
			this.formGroup
				.get("duracaoFixa")
				.setValue(this.getDuracao(result.duracao_fixa));
			this.formGroup.get("ativo").setValue(result.ativo);
			this.formGroup.get("comportamento").setValue(result.comportamentoEnum);
			this.formGroup.get("duracao").setValue(result.tipo_duracao);
			this.formGroup.get("nrAgendamentos").setValue(result.numero_agendamentos);
			this.formGroup.get("dias").setValue(result.dias);
			this.formGroup
				.get("intervaloMinimoFalta")
				.setValue(result.intervalo_minimo_caso_falta);
			this.formGroup
				.get("apenasAlunosCarteira")
				.setValue(result.somente_carteira_professor);
			this.formGroup.get("permitirApp").setValue(result.permitir_app);
			this.colorSelected = result.cor;
			this.cd.detectChanges();
		});
	}

	getDuracao(totalMinutos: number) {
		if (totalMinutos != null && totalMinutos.toString() !== "0") {
			const minTotal = totalMinutos;
			const horas = Math.trunc(minTotal / 60);
			const minutos = minTotal - horas * 60;
			let horasStr;
			let minutosStr;
			horas < 10 ? (horasStr = "0" + horas.toString()) : (horasStr = horas);
			minutos < 10
				? (minutosStr = "0" + minutos.toString())
				: (minutosStr = minutos);

			return "" + horasStr + ":" + minutosStr + "";
		} else {
			return null;
		}
	}

	setTipoAgendamento() {
		const dto: any = this.formGroup.getRawValue();
		const duracaoIgualIntervalo = dto.duracao === "INTERVALO_DE_TEMPO";
		const retornaTempo = dto.duracaoMinima
			? dto.duracaoMinima
			: dto.duracaoMaxima;
		this.tipoAgendamento.nome = dto.nome;
		this.tipoAgendamento.duracao_intervalo_superior = duracaoIgualIntervalo
			? dto.duracaoMaxima
			: null;
		this.tipoAgendamento.duracao_intervalo_inferior = duracaoIgualIntervalo
			? retornaTempo
			: null;
		this.tipoAgendamento.duracao_fixa =
			dto.duracao === "DURACAO_PREDEFINIDA" ? dto.duracaoFixa : null;
		this.tipoAgendamento.ativo = dto.ativo;
		this.tipoAgendamento.comportamento = dto.comportamento;
		this.tipoAgendamento.tipo_duracao = dto.duracao;
		this.tipoAgendamento.numero_agendamentos = parseInt(dto.nrAgendamentos, 0);
		this.tipoAgendamento.dias = parseInt(dto.dias, 0);
		this.tipoAgendamento.intervalo_minimo_caso_falta = parseInt(
			dto.intervaloMinimoFalta,
			0
		);
		this.tipoAgendamento.somente_carteira_professor = dto.apenasAlunosCarteira;
		this.tipoAgendamento.cor = this.colorSelected;
		this.tipoAgendamento.permitir_app = dto.permitirApp;
	}

	createHandler() {
		const message = this.createSuccess.nativeElement.innerHTML;
		this.service
			.criarTipoAgendamento(this.tipoAgendamento)
			.subscribe((result) => {
				this.snotifyService.success(message);
				this.router.navigate(["agenda", "tipo-agendamento"]);
			});
	}

	updateHandler() {
		const message = this.editSuccess.nativeElement.innerHTML;
		this.tipoAgendamento.id = this.entry.id;
		this.service.atualizarTipoAgendamento(this.tipoAgendamento).subscribe(
			(result) => {
				this.snotifyService.success(message);
				this.router.navigate(["agenda", "tipo-agendamento"]);
			},
			(httpErrorResponse) => {
				this.snotifyService.error(
					this.notificacoesTranslate.getLabel(
						httpErrorResponse.error.meta.error
					)
				);
			}
		);
	}

	get showError() {
		if (this.formGroup.get("duracao")) {
			return (
				!this.formGroup.get("duracao").valid &&
				this.formGroup.get("duracao").touched
			);
		} else {
			return false;
		}
	}

	get showErrorComportamento() {
		if (this.formGroup.get("comportamento")) {
			return (
				!this.formGroup.get("comportamento").valid &&
				this.formGroup.get("comportamento").touched
			);
		} else {
			return false;
		}
	}

	markAsToched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("comportamento").markAsTouched();
		this.formGroup.get("duracao").markAsTouched();
		this.formGroup.get("nrAgendamentos").markAsTouched();
		this.formGroup.get("dias").markAsTouched();
		this.formGroup.get("intervaloMinimoFalta").markAsTouched();
		this.formGroup.get("duracaoMaxima").markAsTouched();
		this.formGroup.get("duracaoMinima").markAsTouched();
		this.formGroup.get("duracaoFixa").markAsTouched();
	}
}

function validadorDuracaoFixa(control: AbstractControl): ValidationErrors {
	if (control.parent) {
		if (
			control.parent.get("duracao").value === "DURACAO_PREDEFINIDA" &&
			!control.parent.get("duracaoFixa").value
		) {
			const valid: ValidationErrors = {
				valid: false,
				status: "INVALID",
			};
			return valid;
		} else {
			return null;
		}
	}
}

function validadorIntervaloTempoMax(
	control: AbstractControl
): ValidationErrors {
	if (control.parent) {
		if (
			control.parent.get("duracao").value === "INTERVALO_DE_TEMPO" &&
			!control.parent.get("duracaoMaxima").value
		) {
			const valid: ValidationErrors = {
				valid: false,
				status: "INVALID",
			};
			return valid;
		} else {
			return null;
		}
	}
}

function validadorIntervaloTempoMin(
	control: AbstractControl
): ValidationErrors {
	if (control.parent) {
		if (
			control.parent.get("duracao").value === "INTERVALO_DE_TEMPO" &&
			!control.parent.get("duracaoMinima").value
		) {
			const valid: ValidationErrors = {
				valid: false,
				status: "INVALID",
			};
			return valid;
		} else {
			return null;
		}
	}
}
