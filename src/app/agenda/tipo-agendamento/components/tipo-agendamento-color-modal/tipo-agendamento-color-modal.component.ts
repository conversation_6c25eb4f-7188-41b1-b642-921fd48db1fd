import { Component, OnInit } from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiModalidadeService, ModalidadeCor } from "treino-api";

@Component({
	selector: "pacto-tipo-agendamento-color-modal",
	templateUrl: "./tipo-agendamento-color-modal.component.html",
	styleUrls: ["./tipo-agendamento-color-modal.component.scss"],
})
export class TipoAgendamentoColorModalComponent implements OnInit {
	modalidadeCores: Array<ModalidadeCor> = [];
	selectedColor;
	selectedColorHex;

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal,
		private modalidadeService: TreinoApiModalidadeService
	) {}

	ngOnInit() {
		this.setCoresPreDefinidas();
	}

	dismiss() {
		this.openModal.dismiss();
	}

	private setCoresPreDefinidas() {
		this.modalidadeService.obterModalidadeCores().subscribe((dados) => {
			this.modalidadeCores = dados;
		});
	}

	selectColorHandler(valor: ModalidadeCor) {
		this.openModal.close(valor);
	}

	close() {}

	private createEntity() {}
}
