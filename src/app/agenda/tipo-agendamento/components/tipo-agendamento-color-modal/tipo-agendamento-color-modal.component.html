<div class="modal-header">
	<h4 class="modal-title">
		<span i18n="@@crud-modalidade:modal:create:title">Selecione uma cor</span>
	</h4>
	<button
		(click)="dismiss()"
		aria-label="Close"
		class="close modal-item"
		type="button">
		<span aria-hidden="true">&times;</span>
	</button>
</div>
<div class="modal-body">
	<div class="form-group">
		<div class="container-cores">
			<div
				(click)="selectColorHandler(cor)"
				*ngFor="let cor of modalidadeCores"
				[ngClass]="{ 'selected-color': selectedColor === cor.id }"
				[ngStyle]="{ 'background-color': cor.valor }"
				class="quadro-cores"
				id="cor-{{ cor.id }}">
				<div class="triangle"></div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<button
		(click)="dismiss()"
		class="btn btn-secondary modal-item"
		i18n="@@buttons:cancelar"
		type="button">
		Cancelar
	</button>
</div>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crud-modalidade:mensagem:campos-obrigatorio">
	Campos obrigatorio não preenchido!
</span>
