<pacto-cat-layout-v2>
	<!-- <pacto-breadcrumbs [breadcrumbConfig]="{categoryName: 'CADASTRO', menu: 'TIPO_AGENDAMENTO'}"></pacto-breadcrumbs> -->
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="addClick($event)"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="rowClickHandler($event)"
			[sessionService]="session"
			[tableDescription]="descricao"
			[tableTitle]="titulo"
			[table]="table"
			telaId="tiposAgendamentos"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--Translate section-->

<!--Tables columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-tipo-agendamento:nome">Nome</span>
</ng-template>
<ng-template #corColumnName>
	<span i18n="@@crud-tipo-agendamento:cor">Cor</span>
</ng-template>
<ng-template #comportamentoColumnName>
	<span i18n="@@crud-tipo-agendamento:comportamento">Comportamento</span>
</ng-template>
<ng-template #ativoColumnName>
	<span i18n="@@crud-tipos-Agendamento:create-edit:ativo:label">ativo</span>
</ng-template>
<ng-template #corColumn let-item="item">
	<div
		[ngStyle]="{ 'background-color': item.cor }"
		class="campo-cor icon-color"></div>
</ng-template>
<ng-template #comportamentoCelula let-item="item">
	<ng-container
		*ngTemplateOutlet="
			traducaoComportamentos;
			context: { traducaoComportamento: item.comportamentoEnum }
		"></ng-container>
</ng-template>
<ng-template #statusColumn let-item="item">
	<span *ngIf="item.ativo" i18n="@@crud-tipo-agendamento:sim">Sim</span>
	<span *ngIf="!item.ativo" i18n="@@crud-tipo-agendamento:nao">Não</span>
</ng-template>
<!--End Tables columns-->

<!--Buttons-->
<ng-template #addLabel>
	<span i18n="@@crud-tipo-agendamento:addBtn" id="adicionar-tipo-agendamento">
		Adicionar
	</span>
</ng-template>
<!--End Buttons -->

<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span #tooltipEditar [hidden]="true" i18n="@@crud-alunos:inativar:tooltip-icon">
	Editar
</span>

<!--Title-->
<ng-template #titulo>
	<span i18n="@@crud-tipo-agendamento:title">Tipos de Agendamentos</span>
</ng-template>
<ng-template #descricao>
	<span i18n="@@crud-tipo-agendamento:description">
		Gerencie os tipos de agendamentos
	</span>
</ng-template>
<!--End title-->

<!--Notify-->
<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-tipo-agendamento:remove-modal:title">
	Remover tipo de agendamento
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud-tipo-agendamento:remove-modal:body">
	Deseja remover tipo de agendamento {{ nomeTipoAgendamento }}?
</span>
<span
	#removeModalSuccess
	[hidden]="true"
	i18n="@@crud-tipo-agendamento:remove:success">
	Tipo de agendamento removido com sucesso.
</span>
<span
	#removeError
	[hidden]="true"
	i18n="@@crud-tipo-agendamento:mensage-remove-error">
	Não foi possivel excluir o tipo de agendamento pois ele tem vínculo!
</span>
<!--End notify-->

<!--translator comportamento-->
<ng-template
	#traducaoComportamentos
	let-traducaoComportamento="traducaoComportamento">
	<ng-container [ngSwitch]="traducaoComportamento">
		<span
			*ngSwitchCase="'CONTATO_INTERPESSOAL'"
			i18n="@@crud-tipos-agendamentos:option-select:contato-interpessoal">
			Contato interpessoal
		</span>
		<span
			*ngSwitchCase="'PRESCRICAO_TREINO'"
			i18n="@@crud-tipos-agendamentos:option-select:prescricao-treino">
			Prescrição treino
		</span>
		<span
			*ngSwitchCase="'REVISAO_TREINO'"
			i18n="@@crud-tipos-agendamentos:option-select:revisao">
			Revisão
		</span>
		<span
			*ngSwitchCase="'RENOVAR_TREINO'"
			i18n="@@crud-tipos-agendamentos:option-select:renovar-treino">
			Renovar treino
		</span>
		<span
			*ngSwitchCase="'AVALIACAO_FISICA'"
			i18n="@@crud-tipos-agendamentos:option-select:avaliacao-fisica">
			Avaliação fisica
		</span>
	</ng-container>
</ng-template>
<!--end translator comportamento-->
