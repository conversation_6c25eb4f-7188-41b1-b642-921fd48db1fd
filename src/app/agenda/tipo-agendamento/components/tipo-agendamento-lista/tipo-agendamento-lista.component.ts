import { Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { ModalService } from "@base-core/modal/modal.service";
import { TipoAgendamento, TreinoApiAgendaAgendamentoService } from "treino-api";

import { ApiResponseList } from "@base-core/rest/rest.model";
import { Router } from "@angular/router";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-tipo-agendamento-lista",
	templateUrl: "./tipo-agendamento-lista.component.html",
	styleUrls: ["./tipo-agendamento-lista.component.scss"],
})
export class TipoAgendamentoListaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeModalSuccess", { static: true }) removeModalSuccess;
	@ViewChild("removeError", { static: true }) removeError;

	@ViewChild("addLabel", { static: true }) addLabel;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("corColumnName", { static: true }) corColumnName;
	@ViewChild("comportamentoColumnName", { static: true })
	comportamentoColumnName;
	@ViewChild("comportamentoCelula", { static: true }) comportamentoCelula;
	@ViewChild("ativoColumnName", { static: true }) ativoColumnName;
	@ViewChild("corColumn", { static: true }) corColumn;
	@ViewChild("statusColumn", { static: true }) statusColumn;
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;

	constructor(
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private router: Router,
		private rest: RestService,
		private entityService: TreinoApiAgendaAgendamentoService,
		public session: SessionService
	) {}

	nomeTipoAgendamento = "";
	permissoesAgendamento;
	table: PactoDataGridConfig;
	data: ApiResponseList<TipoAgendamento> = undefined;

	ngOnInit() {
		this.loadAllow();
		this.configTable();
	}

	loadAllow() {
		this.permissoesAgendamento = this.session.recursos.get(
			PerfilAcessoRecursoNome.TIPO_EVENTO
		);
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(
				"tipos-agendamento/all-ativos-inativos"
			),
			logUrl: this.rest.buildFullUrl("log/tipo-agendamento"),
			quickSearch: true,
			rowClick: this.permissoesAgendamento.editar,
			buttons: !this.permissoesAgendamento.incluir
				? null
				: {
						conteudo: this.addLabel,
						nome: "add",
						id: "btn-novo-tipo-agendamento",
				  },
			columns: [
				{
					nome: "cor",
					titulo: this.corColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.corColumn,
				},
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "comportamento",
					titulo: this.comportamentoColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.comportamentoCelula,
				},
				{
					nome: "ativo",
					titulo: this.ativoColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.statusColumn,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
					showIconFn: (row) => this.permissoesAgendamento.editar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.permissoesAgendamento.excluir,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.rowClickHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.remove($event.row);
		}
	}

	rowClickHandler(row: any) {
		this.router.navigate(["agenda", "tipo-agendamento", row.id]);
	}

	addClick($event) {
		this.router.navigate(["agenda", "tipo-agendamento", "novo"]);
	}

	private remove(item) {
		this.nomeTipoAgendamento = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeModalSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			handler.result
				.then(() => {
					this.entityService
						.removerTipoAgendamento(item.id)
						.subscribe((result) => {
							if (result === "erro_registro_esta_sendo_utilizado") {
								this.snotifyService.error(removeError);
							} else {
								this.tableData.reloadData();
								this.snotifyService.success(removeSuccess);
							}
						});
				})
				.catch(() => {});
		});
	}
}
