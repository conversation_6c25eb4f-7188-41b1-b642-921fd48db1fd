import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { TraducoesXinglingComponent } from "ui-kit";

import {
	<PERSON><PERSON>,
	TipoAmbiente,
} from "../../../../../../projects/treino-api/src/lib/turma.model";
import { Ambiente } from "../../../../../../projects/treino-api/src/lib/aula.model";
import { TreinoApiAmbienteService, TreinoApiTurmaService } from "treino-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-cad-ambiente-horario-modal",
	templateUrl: "./cad-ambiente-horario-modal.component.html",
	styleUrls: ["./cad-ambiente-horario-modal.component.scss"],
})
export class AmbienteHorarioModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	formGroup: FormGroup = new FormGroup({
		descricao: new FormControl(""),
		coletorId: new FormControl(null),
		codigo: new FormControl(null),
		tipoAmbienteId: new FormControl(null),
		capacidade: new FormControl(null),
	});

	isChecked: boolean = true;
	coletores: Array<Coletor>;
	tiposAmbiente: Array<TipoAmbiente>;

	constructor(
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private ambienteService: TreinoApiAmbienteService,
		private turmaService: TreinoApiTurmaService,
		private openModal: NgbActiveModal,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.getTiposAmbiente();
		this.getColetores();
		this.cd.detectChanges();
	}

	private getTiposAmbiente() {
		this.ambienteService.obterTodosTiposAmbiente().subscribe((dados) => {
			this.tiposAmbiente = dados.content;
		});
	}

	private getColetores() {
		this.ambienteService.obterTodosColetoresAmbiente().subscribe((dados) => {
			this.coletores = dados.content;
		});
	}

	salvarAmbiente() {
		const ambienteDTO: Ambiente = new Ambiente();
		ambienteDTO.nome = this.formGroup.get("descricao").value;
		if (!ambienteDTO.nome) {
			this.notificationService.error("Nome não pode estar vazio.");
		} else {
			ambienteDTO.tipoAmbiente = this.formGroup.get("tipoAmbienteId").value;
			ambienteDTO.situacao = this.isChecked ? 1 : 0;
			ambienteDTO.coletor = this.formGroup.get("coletorId").value;
			this.turmaService
				.criarAmbienteHorarioTurma(ambienteDTO)
				.subscribe((response) => {
					if (response.retorno) {
						this.openModal.close(response.retorno);
					} else if (response.erro) {
						this.notificationService.error(response.erro);
					} else if (response.message) {
						this.notificationService.error(response.message);
					} else {
						this.notificationService.error("Falha ao salvar ambiente");
					}
				});
		}
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
