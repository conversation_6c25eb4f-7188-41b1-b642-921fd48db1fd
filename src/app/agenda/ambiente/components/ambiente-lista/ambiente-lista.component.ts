import { Component, OnInit, ViewChild } from "@angular/core";

import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { AmbienteEditModalComponent } from "../ambiente-edit-modal/ambiente-edit-modal.component";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { ModalService } from "@base-core/modal/modal.service";
import { Nivel } from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { Ambiente, TreinoApiAmbienteService } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";

import {
	RelatorioComponent,
	PactoDataGridConfig,
	GridFilterConfig,
} from "ui-kit";

@Component({
	selector: "pacto-ambiente-lista",
	templateUrl: "./ambiente-lista.component.html",
	styleUrls: ["./ambiente-lista.component.scss"],
})
export class AmbienteListaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("successCreate", { static: true }) successCreate;
	@ViewChild("successEdit", { static: true }) successEdit;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("removeErro", { static: true }) removeErro;

	@ViewChild("ambienteColumnName", { static: true }) ambienteColumnName;
	@ViewChild("capacidadeColumnName", { static: true }) capacidadeColumnName;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("camposObrigatorios", { static: false }) camposObrigatorios;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	ambiente: Ambiente = new (class implements Ambiente {
		id: string;
		nome: string;
		capacidade: string;
	})();

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});

	constructor(
		private modal: NgbModal,
		private rest: RestService,
		private modalService: ModalService,
		private ambienteService: TreinoApiAmbienteService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService
	) {}

	data: ApiResponseList<Nivel> = {
		content: [],
	};

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;

	itemToRemove;
	loading = false;
	nomeAmbiente = "";
	ready = false;
	integracaoZW = false;

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.configTable();
		this.ready = true;
	}

	btnClickHandler() {
		const modalRef = this.modal.open(AmbienteEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue("");
		modalRef.componentInstance.formGroup.get("capacidade").setValue("");
		modalRef.result.then((result) => {
			const successCreate = this.successCreate.nativeElement.innerHTML;
			this.ambienteService.criarAmbiente(result).subscribe((retorno) => {
				this.snotifyService.success(successCreate);
				this.fetchData();
			});
		});
	}

	btnEditHandler(item) {
		const modalRef = this.modal.open(AmbienteEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue(item.nome);
		modalRef.componentInstance.formGroup
			.get("capacidade")
			.setValue(item.capacidade);
		modalRef.result.then((result) => {
			const successEdit = this.successEdit.nativeElement.innerHTML;
			this.ambienteService
				.atualizarAmbiente(result, item.id)
				.subscribe((retorno) => {
					this.snotifyService.success(successEdit);
					this.fetchData();
				});
		});
	}

	removeHandler(item) {
		this.nomeAmbiente = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeErro = this.removeErro.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					const idToRemove = this.data.content.findIndex(
						(itemI) => itemI.id === item.id
					);
					this.ambienteService.removerAmbiente(item.id).subscribe((result) => {
						if (result === "existe_cadastros_vinculado") {
							this.snotifyService.error(removeErro);
						} else {
							this.snotifyService.success(removeSuccess);
							this.data.content.splice(idToRemove, 1);
							this.fetchData();
						}
					});
				})
				.catch(() => {});
		});
	}

	searchHandler(value) {
		this.fetchData();
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("ambientes"),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/ambientescadastrados"
			),
			quickSearch: true,
			rowClick: !this.integracaoZW,
			buttons: this.integracaoZW
				? null
				: {
						conteudo: this.buttonName,
						nome: "add",
						id: "adicionarAmbiente",
				  },
			columns: [
				{
					nome: "nome",
					titulo: this.ambienteColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "capacidade",
					titulo: this.capacidadeColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "capacidade",
				},
			],
			actions: this.integracaoZW
				? []
				: [
						{
							nome: "edit",
							iconClass: "fa fa-pencil",
						},
						{
							nome: "remove",
							iconClass: "fa fa-trash-o",
							tooltipText: tooltipRemover,
						},
				  ],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}
}
