<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'AMBIENTES'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="false"
			[sessionService]="sessionService"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="ambientescadastrados"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud-atividade:title">Ambientes Cadastrados</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-ambientes:description">
		Gere<PERSON>ie os ambientes cadastrados.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #ambienteColumnName>
	<span i18n="@@crud-ambientes:table:ambiente:title">Ambiente</span>
</ng-template>
<ng-template #capacidadeColumnName>
	<span i18n="@@crud-ambientes:table:capacidade:title">Capacidade</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-ambientes:table:acoes:title">Ações</span>
</ng-template>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>
<span #successCreate [hidden]="true" i18n="@@crud-ambientes:success:create">
	Ambiente criado com sucesso.
</span>
<span #successEdit [hidden]="true" i18n="@@crud-ambientes:success:edit">
	Ambiente editado com sucesso.
</span>
<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-ambientes:remove-modal:title">
	Remover Ambiente?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud-ambientes:remove-modal:body">
	Deseja remover o ambiente {{ nomeAmbiente }}?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud-ambientes:remove-modal:success">
	Ambiente removido com sucesso.
</span>
<span #removeErro [hidden]="true">
	Existe cadastros vinculados a este ambiente.
</span>
