import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { Ambiente } from "treino-api";

@Component({
	selector: "pacto-ambiente-edit-modal",
	templateUrl: "./ambiente-edit-modal.component.html",
	styleUrls: ["./ambiente-edit-modal.component.scss"],
})
export class AmbienteEditModalComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;
	@ViewChild("mensagemValidacao", { static: true }) mensagemValidacao;

	statusFormulario = false;
	verificado = false;
	ambiente: Ambiente = new Ambiente();
	timerMask = [/[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/];

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		capacidade: new FormControl("", [
			Validators.required,
			Validators.minLength(1),
		]),
	});

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {}

	verificaTitle() {
		if (!this.verificado) {
			this.verificado = true;

			this.statusFormulario = this.formGroup.get("nome").value === "";
		}

		return this.statusFormulario;
	}

	dismiss() {
		this.verificado = false;
		this.openModal.dismiss();
	}

	close() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("capacidade").markAsTouched();
		if (this.formGroup.valid && this.validarCapacidade()) {
			this.verificado = false;
			this.openModal.close(this.createEntity());
		} else if (!this.formGroup.valid) {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
		}
	}

	get Validators() {
		return Validators;
	}

	validarCapacidade() {
		if (this.formGroup.get("capacidade").value < 1) {
			const mensagemValidacao = this.mensagemValidacao.nativeElement.innerHTML;
			this.snotifyService.error(mensagemValidacao);
			return false;
		} else {
			return true;
		}
	}

	private createEntity() {
		this.ambiente.nome = this.formGroup.get("nome").value;
		this.ambiente.capacidade = this.formGroup.get("capacidade").value;

		return this.ambiente;
	}
}
