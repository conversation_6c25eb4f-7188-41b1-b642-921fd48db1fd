<div class="modal-header">
	<h4 class="modal-title">
		<span *ngIf="verificaTitle()" i18n="@@crud-ambientes:modal:create:title">
			Criar Ambiente
		</span>
		<span *ngIf="!verificaTitle()" i18n="@@crud-ambientes:modal:edit:title">
			Editar Ambiente
		</span>
	</h4>
	<button
		(click)="dismiss()"
		aria-label="Close"
		class="close modal-item"
		type="button">
		<span aria-hidden="true">&times;</span>
	</button>
</div>
<div class="modal-body">
	<pacto-input
		[control]="formGroup.get('nome')"
		[id]="'input-nome-ambiente'"
		[name]="'nome'"
		i18n-label="@@crud-ambientes:input:nome:label"
		i18n-mensagem="@@crud-ambientes:input:nome:description"
		i18n-placeholder="@@crud-ambientes:input:nome:placeholder"
		label="Nome"
		mensagem="Fornecer um nome com pelo menos 3 digitos."
		placeholder="Nome do ambiente"></pacto-input>

	<pacto-input
		[control]="formGroup.get('capacidade')"
		[id]="'input-capacidade-ambiente'"
		[name]="'capacidade'"
		[textMask]="{ mask: timerMask, guide: false }"
		i18n-label="@@crud-ambientes:input:capacidade:label"
		i18n-mensagem="@@crud-ambientes:input:capacidade:mensagem"
		i18n-placeholder="@@crud-ambientes:input:capacidade:placeholder"
		label="Capacidade"
		mensagem="Fornecer uma capacidade com pelo menos 1 digito."
		placeholder="Capacidade do ambiente"></pacto-input>
</div>

<div class="modal-footer">
	<button
		(click)="dismiss()"
		class="btn btn-secondary modal-item"
		i18n="@@buttons:cancelar"
		type="button">
		Cancelar
	</button>
	<button
		(click)="close()"
		class="btn btn-primary"
		i18n="@@buttons:salvar"
		id="btn-salvar-ambiente"
		type="button">
		Salvar
	</button>
</div>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crud-ambientes:mensagem:campos-obrigatorio">
	Campos obrigatorio não preenchido.
</span>
<span
	#mensagemValidacao
	[hidden]="true"
	i18n="@@crud-ambientes:mensagem:mensagem-validacao:capacidade">
	Capacidade tem que ser maior que 0.
</span>
