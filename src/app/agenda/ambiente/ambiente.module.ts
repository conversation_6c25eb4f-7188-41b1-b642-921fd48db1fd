import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { AmbienteListaComponent } from "./components/ambiente-lista/ambiente-lista.component";
import { AmbienteEditModalComponent } from "./components/ambiente-edit-modal/ambiente-edit-modal.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { AmbienteEditModalModule } from "./components/ambiente-edit-modal/ambiente-edit-modal.module";
import { AmbienteHorarioModalComponent } from "./components/cad-ambiente-horario-modal/cad-ambiente-horario-modal.component";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { AmbienteHorarioModalModule } from "./components/cad-ambiente-horario-modal/cad-ambiente-horario-module";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.AMBIENTES,
	true
);

const funcionalidadesModulo = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.UTILIZAR_MODULO_AGENDA,
	true
);

const routes: Routes = [
	{
		path: "",
		component: AmbienteListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades && funcionalidadesModulo,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		CommonModule,
		BaseSharedModule,
		AmbienteEditModalModule,
		AmbienteHorarioModalModule,
	],
	entryComponents: [AmbienteEditModalComponent, AmbienteHorarioModalComponent],
	declarations: [AmbienteListaComponent],
})
export class AmbienteModule {}
