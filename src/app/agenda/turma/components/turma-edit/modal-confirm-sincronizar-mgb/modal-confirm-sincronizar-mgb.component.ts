import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-confirm-sincronizar-mgb",
	templateUrl: "./modal-confirm-sincronizar-mgb.component.html",
	styleUrls: ["./modal-confirm-sincronizar-mgb.component.scss"],
})
export class ModalConfirmSincronizarMgbComponent implements OnInit {
	@Input() title: string;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	dismiss() {
		this.openModal.dismiss(false);
	}

	estouCiente() {
		this.openModal.close(true);
	}
}
