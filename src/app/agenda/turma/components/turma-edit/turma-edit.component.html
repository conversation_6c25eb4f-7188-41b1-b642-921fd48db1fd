<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		(click)="cancelHandler(true)"
		[breadcrumbConfig]="{
			categoryName: !entity ? 'Turma' : 'Turma ',
			menu: !entity ? 'Editar Turma' : 'Cadastrar turma',
			menuLink: []
		}"
		class="first"></pacto-breadcrumbs>

	<pacto-cat-card-plain *ngIf="operation === 'create'">
		<pacto-cat-stepper class="force-header-start" style="margin-bottom: 10px">
			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Informações</ng-template>
				<div class="row">
					<div class="col-md-12">
						<pacto-cat-form-input
							[control]="formGroup.get('nome')"
							[id]="'nome-turma-input'"
							i18n-label="@@crud-turmas:input:nome:label"
							i18n-mensagem="@@crud-turmas:input:nome:mensagem"
							i18np-placeholder="@@crud-turmas:input:nome:placeholder"
							label="Nome da turma*"
							mensagem="Definir um nome com pelo menos 3 caracteres."
							placeholder="Informe o nome da turma"></pacto-cat-form-input>
					</div>
				</div>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('modalidade')"
							[id]="'modalidade-select'"
							[items]="modalidades"
							i18n-label="@@crud-turmas:select:modalidade:label"
							i18n-mensagem="@@crud-turmas:select:modalidade:mensagem"
							idKey="id"
							label="Modalidade*"
							labelKey="nome"
							mensagem="Selecione uma modalidade."></pacto-cat-form-select>
					</div>

					<div class="col-md-6">
						<pacto-cat-input-color [formControl]="formGroup.get('cor')">
							<label class="label-cor-turma">Cor da turma*</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="row">
					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMinima')"
							[id]="'idadeMinima'"
							i18n-label="@@crud-turmas:select:IdadeMinima:label"
							i18n-mensagem="@@crud-turmas:select:idadeMinima:mensagem"
							label="Idade mínima (anos)"
							mensagem="Informe uma idade."></pacto-cat-form-input-number>
					</div>

					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMinimaMeses')"
							[id]="'idadeMinimaMeses'"
							i18n-label="@@crud-turmas:select:idadeMinimaMeses:label"
							i18n-mensagem="@@crud-turmas:select:idadeMinima:mensagem"
							label="(meses)"
							mensagem="Informe a quantidade de meses."></pacto-cat-form-input-number>
					</div>
					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMaxima')"
							[id]="'idadeMaxima'"
							i18n-label="@@crud-turmas:select:idadeMaxima:label"
							i18n-mensagem="@@crud-turmas:select:idadeMaxima:mensagem"
							label="Idade máxima (anos)*"
							mensagem="Informe uma idade válida."></pacto-cat-form-input-number>
						<div
							*ngIf="
								formGroup.get('idadeMaxima').invalid &&
								formGroup.get('idadeMaxima').touched
							"
							class="error-message">
							<span *ngIf="formGroup.get('idadeMaxima').errors.required">
								Idade máxima é obrigatória.
							</span>
							<span *ngIf="formGroup.get('idadeMaxima').errors.min">
								Idade máxima deve ser maior que 0.
							</span>
						</div>
					</div>

					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMaximaMeses')"
							[id]="'idadeMaximaMeses'"
							i18n-label="@@crud-turmas:select:idadeMaximaMeses:label"
							i18n-mensagem="@@crud-turmas:select:idadeMaxima:mensagem"
							label="(meses)"
							mensagem="Informe a quantidade de meses."></pacto-cat-form-input-number>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-6">
						<pacto-input
							[control]="formGroupVideosUri.get('linkVideo')"
							[id]="'input-link'"
							label="Video (link no YouTube)"
							placeholder="link"></pacto-input>
					</div>
					<div class="col-lg-6">
						<div class="form-link-video">
							<span [ds3Tooltip]="tooltipProfessor" [tooltipPosition]="'top'">
								<input
									[formControl]="formGroupVideosUri.get('professor')"
									class="form-check-input"
									id="checkProfessor"
									type="checkbox"
									value="" />
								<label
									class="form-check-label"
									for="checkProfessor"
									i18n="@@crud-atividade:ativa:label">
									Professor?
								</label>
							</span>

							<div class="btn-adicionar-link-video">
								<button
									(click)="adicionarHandler()"
									[disabled]="!acaoHabilitada"
									class="pct pct-plus btn btn-sm btn-primary"
									i18n="@@crud-atividade:imagem:button"
									id="id-adicionar-link-video">
									<label>Adicionar</label>
								</button>
							</div>
						</div>
					</div>
				</div>

				<div *ngIf="linkVideos.length > 0" class="row">
					<div class="col-lg-6">
						<div class="list-wrapper">
							<table class="table">
								<thead>
									<tr>
										<th>Link</th>
										<th class="table-item">Professor</th>
										<th class="table-item">Excluir</th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let link of linkVideos; let index = index">
										<td>{{ link.linkVideo }}</td>
										<td class="table-item">
											<input
												(click)="checkCheckBoxProfessor(link)"
												[checked]="link.professor"
												class="form-check-input"
												type="checkbox" />
										</td>
										<td class="table-item">
											<i (click)="deletButon(index)" class="fa fa-trash-o"></i>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input
							[control]="formGroup.get('identificador')"
							[id]="'identificador'"
							i18n-label="@@crud-turmas:select:identificador:label"
							i18n-mensagem="@@crud-turmas:select:identificador:mensagem"
							label="Identificador*"
							mensagem="Informe um identificador para turma."
							placeholder="Adicione uma sigla para identificar esta turma Exemplo: NAT2022"></pacto-cat-form-input>
					</div>
					<div class="col-md-6">
						<pacto-cat-form-input
							[control]="formGroup.get('id')"
							[id]="'codigoTurma'"
							i18n-label="@@crud-turmas:select:codigoTurma:label"
							i18n-mensagem="@@crud-turmas:select:codigoTurma:mensagem"
							label="Código da turma"></pacto-cat-form-input>
					</div>
				</div>

				<div class="actions">
					<button
						[disabled]="!isFormGroupValid"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula1"
						pactoCatStepNext>
						Avançar
						<span class="pct pct-chevron-right"></span>
					</button>
				</div>
			</pacto-cat-step>

			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Data e horários</ng-template>
				<div class="row margin-bottom-40">
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataInicial')"
							i18n-label="@@turma-form:label-data-inicial"
							label="Data inicial"></pacto-cat-datepicker>
					</div>
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataFinal')"
							i18n-label="@@turma-form:label-data-final"
							label="Data final"></pacto-cat-datepicker>
					</div>
				</div>

				<pacto-title-card [title]="'Horários cadastrados'">
					<pacto-relatorio
						#tableData
						(btnClick)="btnClickHandler()"
						(iconClick)="actionClickHandler($event)"
						(rowClick)="btnEditHandler($event)"
						[filterConfig]="filterConfig"
						[table]="table"></pacto-relatorio>
				</pacto-title-card>

				<div class="actions">
					<button
						[disabled]="!isFormGroupValid"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula2"
						pactoCatStepNext>
						Avançar
						<span class="pct pct-chevron-right"></span>
					</button>

					<button
						class="btn btn-secondary"
						i18n="@@buttons:cancelar"
						pactoCatStepPrevious>
						<span class="pct pct-chevron-left"></span>
						Voltar
					</button>
				</div>
			</pacto-cat-step>

			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Adicionais</ng-template>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('minutosAntecedenciaDesmarcarAula')"
							[id]="'minutosAntecedenciaDesmarcarAula'"
							i18n-label="
								@@crud-turmas:select:minutosAntecedenciaDesmarcarAula:label"
							i18n-mensagem="
								@@crud-turmas:select:minutosAntecedenciaDesmarcarAula:mensagem"
							label="Quantidade minima de minutos de antecedência para desmarcar aula"
							mensagem="Informe uma quantidade."></pacto-cat-form-input-number>
					</div>
					<div class="col-md-6">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('minutosAposInicioApp')"
							[id]="'minutosAposInicioApp'"
							i18n-label="@@crud-turmas:select:minutosAposInicioApp:label"
							i18n-mensagem="@@crud-turmas:select:minutosAposInicioApp:mensagem"
							label="Tolerância em minutos após início da aula para apresentar no App Treino"
							mensagem="Informe uma tolerância."></pacto-cat-form-input-number>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('tipoAntecedenciaMarcarAula')"
							[id]="'tiposAntecedenciaMarcar-select'"
							[items]="tiposAntecedenciaMarcar"
							i18n-label="@@crud-turmas:select:tiposAntecedenciaMarcar:label"
							i18n-mensagem="
								@@crud-turmas:select:tiposAntecedenciaMarcar:mensagem"
							idKey="id"
							label="Antecedência marcar aula"
							labelKey="nome"
							mensagem="Selecione um tipo de antecedência."></pacto-cat-form-select>
						<pacto-cat-form-input-number
							*ngIf="formGroup.get('tipoAntecedenciaMarcarAula').value == 2"
							[formControl]="formGroup.get('minutosAntecedenciaMarcarAula')"
							[id]="'qualquerDia'"
							i18n-label="@@crud-turmas:select:qualquerDia:label"
							i18n-mensagem="@@crud-turmas:select:qualquerDia:mensagem"
							label="Com até quantos minutos de antecedência ?"
							mensagem="Informe algum minuto para antecedência."></pacto-cat-form-input-number>
						<pacto-cat-form-input-number
							*ngIf="formGroup.get('tipoAntecedenciaMarcarAula').value == 3"
							[formControl]="formGroup.get('minutosAntecedenciaMarcarAula')"
							[id]="'somenteNoDia'"
							i18n-label="@@crud-turmas:select:somenteNoDia:label"
							i18n-mensagem="@@crud-turmas:select:somenteNoDia:mensagem"
							label="Com até quantos minutos de antecedência ?"
							mensagem="Informe algum minuto para antecedência."></pacto-cat-form-input-number>

						<pacto-cat-checkbox
							[control]="formGroup.get('bloquearMatriculasAcimaLimite')"
							[label]="
								'Bloquear matrículas acima do limite'
							"></pacto-cat-checkbox>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('permitirDesmarcarReposicoes')"
								[label]="'Permitir desmarcar reposições'"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('permiteAlunoOutraEmpresa')"
								[label]="
									'Permitir marcar aula experimental para alunos de outras unidades'
								"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('monitorada')"
								[label]="
									'Monitorar presenças, faltas e reposições'
								"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('bloquearReposicaoAcimaLimite')"
								[label]="
									'Bloquear reposições acima do limite'
								"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('permitirAulaExperimental')"
								[label]="'Permitir incluir visitantes'"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('bloquearLotacaoFutura')"
								[label]="
									'Bloquear matrículas com possibilidade de lotação futura'
								"></pacto-cat-checkbox>
						</div>
					</div>
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('qtdeNivelOcupacao')"
							[id]="'niveisDescontoOcupacao-select'"
							[items]="niveisDescontoOcupacao"
							i18n-label="@@crud-turmas:select:niveisDescontoOcupacao:label"
							i18n-mensagem="
								@@crud-turmas:select:niveisDescontoOcupacao:mensagem"
							idKey="id"
							label="Níveis de desconto por ocupação"
							labelKey="nome"
							mensagem="Selecione um nível de ocupação."></pacto-cat-form-select>
						<div class="row">
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(1)"
									[formControl]="formGroup.get('percDescOcupacaoNivel1')"
									[id]="'percDescOcupacaoNivel1'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel1:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel1:mensagem"
									label="{{ labelNivel1desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>

							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(2)"
									[formControl]="formGroup.get('percDescOcupacaoNivel2')"
									[id]="'percDescOcupacaoNivel2'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel2:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel2:mensagem"
									label="{{ labelNivel2desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(3)"
									[formControl]="formGroup.get('percDescOcupacaoNivel3')"
									[id]="'percDescOcupacaoNivel3'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel3:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel3:mensagem"
									label="{{ labelNivel3desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(4)"
									[formControl]="formGroup.get('percDescOcupacaoNivel4')"
									[id]="'percDescOcupacaoNivel4'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel4:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel4:mensagem"
									label="{{ labelNivel4desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(5)"
									[formControl]="formGroup.get('percDescOcupacaoNivel5')"
									[id]="'percDescOcupacaoNivel5'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel1:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel1:mensagem"
									label="{{ labelNivel5desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
						</div>
					</div>
				</div>

				<div class="actions">
					<button
						(click)="submitHandler()"
						[disabled]="!isFormGroupValid"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula3">
						Concluir agendamento
					</button>

					<button
						class="btn btn-secondary"
						i18n="@@buttons:cancelar"
						pactoCatStepPrevious>
						<span class="pct pct-chevron-left"></span>
						Voltar
					</button>
				</div>
			</pacto-cat-step>
		</pacto-cat-stepper>
	</pacto-cat-card-plain>

	<pacto-cat-tabs-transparent *ngIf="operation === 'edit'">
		<ng-template label="Informações" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row">
					<div class="col-md-12">
						<pacto-cat-form-input
							[control]="formGroup.get('nome')"
							[id]="'nome-turma-input'"
							i18n-label="@@crud-turmas:input:nome:label"
							i18n-mensagem="@@crud-turmas:input:nome:mensagem"
							i18np-placeholder="@@crud-turmas:input:nome:placeholder"
							label="Nome da turma*"
							mensagem="Definir um nome com pelo menos 3 caracteres."
							placeholder="Informe o nome da turma"></pacto-cat-form-input>
					</div>
				</div>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('modalidade')"
							[id]="'modalidade-select'"
							[items]="modalidades"
							i18n-label="@@crud-turmas:select:modalidade:label"
							i18n-mensagem="@@crud-turmas:select:modalidade:mensagem"
							idKey="id"
							label="Modalidade*"
							labelKey="nome"
							mensagem="Selecione uma modalidade."></pacto-cat-form-select>
					</div>

					<div class="col-md-6">
						<pacto-cat-input-color [formControl]="formGroup.get('cor')">
							<label class="label-cor-turma">Cor da turma*</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="row">
					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMinima')"
							[id]="'idadeMinima'"
							i18n-label="@@crud-turmas:select:IdadeMinima:label"
							i18n-mensagem="@@crud-turmas:select:idadeMinima:mensagem"
							label="Idade mínima (anos)"
							mensagem="Informe um idade."></pacto-cat-form-input-number>
					</div>

					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMinimaMeses')"
							[id]="'idadeMinimaMeses'"
							i18n-label="@@crud-turmas:select:idadeMinimaMeses:label"
							i18n-mensagem="@@crud-turmas:select:idadeMinima:mensagem"
							label="(meses)"
							mensagem="Informe a quantidade de meses."></pacto-cat-form-input-number>
					</div>
					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMaxima')"
							[id]="'idadeMaxima'"
							i18n-label="@@crud-turmas:select:idadeMaxima:label"
							i18n-mensagem="@@crud-turmas:select:idadeMaxima:mensagem"
							label="Idade máxima (anos)*"
							mensagem="Informe uma idade válida."></pacto-cat-form-input-number>
						<div
							*ngIf="
								formGroup.get('idadeMaxima').invalid &&
								formGroup.get('idadeMaxima').touched
							"
							class="error-message">
							<span *ngIf="formGroup.get('idadeMaxima').errors.required">
								Idade máxima é obrigatória.
							</span>
							<span *ngIf="formGroup.get('idadeMaxima').errors.min">
								Idade máxima deve ser maior que 0.
							</span>
						</div>
					</div>

					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('idadeMaximaMeses')"
							[id]="'idadeMaximaMeses'"
							i18n-label="@@crud-turmas:select:idadeMaximaMeses:label"
							i18n-mensagem="@@crud-turmas:select:idadeMaxima:mensagem"
							label="(meses)"
							mensagem="Informe a quantidade de meses."></pacto-cat-form-input-number>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-6">
						<pacto-input
							[control]="formGroupVideosUri.get('linkVideo')"
							[id]="'input-link'"
							label="Video (link no YouTube)"
							placeholder="link"></pacto-input>
					</div>
					<div class="col-lg-6">
						<div class="form-link-video">
							<span [ds3Tooltip]="tooltipProfessor" [tooltipPosition]="'top'">
								<input
									[formControl]="formGroupVideosUri.get('professor')"
									class="form-check-input"
									id="checkProfessor"
									type="checkbox"
									value="" />
								<label
									class="form-check-label"
									for="checkProfessor"
									i18n="@@crud-atividade:ativa:label">
									Professor?
								</label>
							</span>

							<div class="btn-adicionar-link-video">
								<button
									(click)="adicionarHandler()"
									[disabled]="!acaoHabilitada"
									class="pct pct-plus btn btn-sm btn-primary"
									i18n="@@crud-atividade:imagem:button"
									id="id-adicionar-alterar-link-video">
									<label>Adicionar</label>
								</button>
							</div>
						</div>
					</div>
				</div>

				<div *ngIf="linkVideos.length > 0" class="row">
					<div class="col-lg-6">
						<div class="list-wrapper">
							<table class="table">
								<thead>
									<tr>
										<th>Link</th>
										<th class="table-item">Professor</th>
										<th class="table-item">Excluir</th>
									</tr>
								</thead>
								<tbody>
									<tr *ngFor="let link of linkVideos; let index = index">
										<td>{{ link.linkVideo }}</td>
										<td class="table-item">
											<input
												(click)="checkCheckBoxProfessor(link)"
												[checked]="link.professor"
												class="form-check-input"
												type="checkbox" />
										</td>
										<td class="table-item">
											<i (click)="deletButon(index)" class="fa fa-trash-o"></i>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input
							[control]="formGroup.get('identificador')"
							[id]="'identificador'"
							i18n-label="@@crud-turmas:select:identificador:label"
							i18n-mensagem="@@crud-turmas:select:identificador:mensagem"
							label="Identificador*"
							mensagem="Informe um identificador para turma."
							placeholder="Adicione uma sigla para identificar esta turma Exemplo: NAT2022"></pacto-cat-form-input>
					</div>
					<div class="col-md-6">
						<pacto-cat-form-input
							[control]="formGroup.get('id')"
							[id]="'codigoTurma'"
							i18n-label="@@crud-turmas:select:codigoTurma:label"
							i18n-mensagem="@@crud-turmas:select:codigoTurma:mensagem"
							label="Código da turma"></pacto-cat-form-input>
					</div>
				</div>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>

					<button
						(click)="sincronizarTurmaMgb()"
						*ngIf="showBtnSyncTurmaMgb"
						class="btn btn-primary">
						Sincronizar com MGB
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
		<ng-template label="Data e horários" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row margin-bottom-40">
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataInicial')"
							i18n-label="@@turma-form:label-data-inicial"
							label="Data inicial"></pacto-cat-datepicker>
					</div>
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataFinal')"
							i18n-label="@@turma-form:label-data-final"
							label="Data final"></pacto-cat-datepicker>
					</div>
				</div>

				<pacto-title-card [title]="'Horários cadastrados'">
					<pacto-relatorio
						#tableData
						(btnClick)="btnClickHandler()"
						(iconClick)="actionClickHandler($event)"
						(rowClick)="btnEditHandler($event)"
						[filterConfig]="filterConfig"
						[table]="table"
						actionTitulo="Ações"></pacto-relatorio>
				</pacto-title-card>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
		<ng-template label="Adicionais" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('minutosAntecedenciaDesmarcarAula')"
							[id]="'minutosAntecedenciaDesmarcarAula'"
							i18n-label="
								@@crud-turmas:select:minutosAntecedenciaDesmarcarAula:label"
							i18n-mensagem="
								@@crud-turmas:select:minutosAntecedenciaDesmarcarAula:mensagem"
							label="Quantidade minima de minutos de antecedência para desmarcar aula"
							mensagem="Informe uma quantidade."></pacto-cat-form-input-number>
					</div>
					<div class="col-md-6">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('minutosAposInicioApp')"
							[id]="'minutosAposInicioApp'"
							i18n-label="@@crud-turmas:select:minutosAposInicioApp:label"
							i18n-mensagem="@@crud-turmas:select:minutosAposInicioApp:mensagem"
							label="Tolerância em minutos após início da aula para apresentar no App Treino"
							mensagem="Informe uma tolerância."></pacto-cat-form-input-number>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('tipoAntecedenciaMarcarAula')"
							[id]="'tiposAntecedenciaMarcar-select'"
							[items]="tiposAntecedenciaMarcar"
							i18n-label="@@crud-turmas:select:tiposAntecedenciaMarcar:label"
							i18n-mensagem="
								@@crud-turmas:select:tiposAntecedenciaMarcar:mensagem"
							idKey="id"
							label="Antecedência marcar aula"
							labelKey="nome"
							mensagem="Selecione um tipo de antecedência."></pacto-cat-form-select>
						<pacto-cat-form-input-number
							*ngIf="formGroup.get('tipoAntecedenciaMarcarAula').value == 2"
							[formControl]="formGroup.get('minutosAntecedenciaMarcarAula')"
							[id]="'qualquerDia'"
							i18n-label="@@crud-turmas:select:qualquerDia:label"
							i18n-mensagem="@@crud-turmas:select:qualquerDia:mensagem"
							label="Com até quantos minutos de antecedência ?"
							mensagem="Informe algum minuto para antecedência."></pacto-cat-form-input-number>
						<pacto-cat-form-input-number
							*ngIf="formGroup.get('tipoAntecedenciaMarcarAula').value == 3"
							[formControl]="formGroup.get('minutosAntecedenciaMarcarAula')"
							[id]="'somenteNoDia'"
							i18n-label="@@crud-turmas:select:somenteNoDia:label"
							i18n-mensagem="@@crud-turmas:select:somenteNoDia:mensagem"
							label="Com até quantos minutos de antecedência ?"
							mensagem="Informe algum minuto para antecedência."></pacto-cat-form-input-number>

						<pacto-cat-checkbox
							[control]="formGroup.get('bloquearMatriculasAcimaLimite')"
							[label]="
								'Bloquear matrículas acima do limite'
							"></pacto-cat-checkbox>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('permitirDesmarcarReposicoes')"
								[label]="'Permitir desmarcar reposições'"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('permiteAlunoOutraEmpresa')"
								[label]="
									'Permitir marcar aula experimental para alunos de outras unidades'
								"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('monitorada')"
								[label]="
									'Monitorar presenças, faltas e reposições'
								"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('bloquearReposicaoAcimaLimite')"
								[label]="
									'Bloquear reposições acima do limite'
								"></pacto-cat-checkbox>
						</div>
						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('permitirAulaExperimental')"
								[label]="'Permitir incluir visitantes'"></pacto-cat-checkbox>
						</div>

						<div class="margin-top-20">
							<pacto-cat-checkbox
								[control]="formGroup.get('bloquearLotacaoFutura')"
								[label]="
									'Bloquear matrículas com possibilidade de lotação futura'
								"></pacto-cat-checkbox>
						</div>
					</div>
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('qtdeNivelOcupacao')"
							[id]="'niveisDescontoOcupacao-select'"
							[items]="niveisDescontoOcupacao"
							i18n-label="@@crud-turmas:select:niveisDescontoOcupacao:label"
							i18n-mensagem="
								@@crud-turmas:select:niveisDescontoOcupacao:mensagem"
							idKey="id"
							label="Níveis de desconto por ocupação"
							labelKey="nome"
							mensagem="Selecione um nível de ocupação."></pacto-cat-form-select>

						<div class="row">
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(1)"
									[formControl]="formGroup.get('percDescOcupacaoNivel1')"
									[id]="'percDescOcupacaoNivel1'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel1:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel1:mensagem"
									label="{{ labelNivel1desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>

							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(2)"
									[formControl]="formGroup.get('percDescOcupacaoNivel2')"
									[id]="'percDescOcupacaoNivel2'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel2:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel2:mensagem"
									label="{{ labelNivel2desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(3)"
									[formControl]="formGroup.get('percDescOcupacaoNivel3')"
									[id]="'percDescOcupacaoNivel3'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel3:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel3:mensagem"
									label="{{ labelNivel3desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(4)"
									[formControl]="formGroup.get('percDescOcupacaoNivel4')"
									[id]="'percDescOcupacaoNivel4'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel4:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel4:mensagem"
									label="{{ labelNivel4desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
							<div class="col-md-6">
								<pacto-cat-form-input-number
									*ngIf="nivelOcupacaoXOuMaior(5)"
									[formControl]="formGroup.get('percDescOcupacaoNivel5')"
									[id]="'percDescOcupacaoNivel5'"
									[maskOptions]="maskOptionsInputPercent"
									decimal="true"
									i18n-label="@@crud-turmas:select:percDescOcupacaoNivel1:label"
									i18n-mensagem="
										@@crud-turmas:select:percDescOcupacaoNivel1:mensagem"
									label="{{ labelNivel5desconto }}"
									max="100"
									maxLength="5"
									mensagem="Informe uma porcentagem."></pacto-cat-form-input-number>
							</div>
						</div>
					</div>
				</div>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
	</pacto-cat-tabs-transparent>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@crud-aulas:create-success" xingling="createSuccess">
		Turma criada com sucesso.
	</span>
	<span i18n="@@crud-aulas:create-edit" xingling="editSuccess">
		Turma editada com sucesso.
	</span>
	<span i18n="@@crud-aulas:campo-obrigatorio" xingling="campoObrigatorio">
		Campos obrigatórios não preenchido.
	</span>
	<span
		i18n="@@crud-aulas:campo-obrigatorio:inserir-horario"
		xingling="inserirHorario">
		Inserir pelo menos um horário na lista
	</span>
	<span i18n="@@crud-aulas:validacao:data" xingling="validacaoData">
		Data inicial não pode ser superior a data final
	</span>
	<span i18n="@@crud-aulas:validacao:horario" xingling="validacaoHorario">
		Horário cadastrado invalido, tente novamente
	</span>
	<span i18n="@@crud-aulas:validacao:dia semana" xingling="validacaoDiaSemana">
		Selecione ao menos um dia da semana.
	</span>
	<span xingling="horarionaoPodeSerRemovido">
		Não é possível excluir o horário, contém aula confirmada.
	</span>
	<span xingling="erro_aula_url_virtual">Informe a URL da aula virtual.</span>
	<span i18n="@@crud-aulas:erro_incluir_aula" xingling="erro_incluir_aula">
		Ocorreu um erro ao incluir a aula, tente novamente em alguns instantes!
	</span>
	<span i18n="@@crud-ambientes:success:create" xingling="successCreate">
		Ambiente criado com sucesso.
	</span>
	<span
		i18n="@@crud-modalidade:create-modal:success"
		xingling="mensagemCreateSuccess">
		Modalidade criada com sucesso.
	</span>
</pacto-traducoes-xingling>

<!--table columns-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar horário</span>
</ng-template>
<ng-template #codigoColumnName>
	<span i18n="@@crud-turma-edit:codigo:title:table">Código</span>
</ng-template>
<ng-template #professorColumnName>
	<span i18n="@@crud-turma-edit:professor:title:table">Professor</span>
</ng-template>
<ng-template #duracaoColumnName>
	<span i18n="@@crud-turma-edit:duracao:title:table">Duração</span>
</ng-template>
<ng-template #diaColumnName>
	<span i18n="@@crud-turma-edit:dia:title:table">Dia</span>
</ng-template>
<ng-template #maxAlunosColumnName>
	<span i18n="@@crud-turma-edit:maxAlunos:title:table">Nº máx. alunos</span>
</ng-template>
<ng-template #horarioTurmaColumnName>
	<span i18n="@@crud-turma-edit:horarioTurma:title:table">
		Horário da Turma
	</span>
</ng-template>
<ng-template #ambienteColumnName>
	<span i18n="@@crud-turma-edit:ambiente:title:table">Ambiente</span>
</ng-template>
<ng-template #toleranciaMinColumnName>
	<span i18n="@@crud-turma-edit:toleranciaMin:title:table">
		Tôlerancia(min)
	</span>
</ng-template>
<ng-template #nivelTurmaColumnName>
	<span i18n="@@crud-turma-edit:nivelTurma:title:table">Nível Turma</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-atividades:table:status">Ações</span>
</ng-template>
<!--End table columns-->

<!--tooltip icons-->
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<!--End tooltip icons-->

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-turmas-horariosTurma:remove-modal:title">
	Inativar horario turma ?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud-turmas-horariosTurma:remove:success">
	Horario turma inativado com sucesso.
</span>
<span
	#removeError
	[hidden]="true"
	i18n="@@crud-turmas-horariosTurma:remove:error">
	Não foi possível excluir, existem alunos matriculados
</span>
<ng-template #tooltipProfessor>
	<div class="itens-tooltip">
		Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o
		professor
	</div>
</ng-template>
