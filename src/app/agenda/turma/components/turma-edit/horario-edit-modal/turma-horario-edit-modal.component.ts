import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";
import { UsuarioBase } from "sdk";
import { Ambiente, TreinoApiTurmaService } from "treino-api";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { AmbienteHorarioModalComponent } from "../../../../ambiente/components/cad-ambiente-horario-modal/cad-ambiente-horario-modal.component";
import {
	CategoriaCapacidade,
	HorarioTurma,
	NivelTurma,
	TurmaCreateEdit,
} from "../../../../../../../projects/treino-api/src/lib/turma.model";
import { NivelTurmaHorarioModalComponent } from "./cad-nivelTurma-horario-modal/cad-nivelTurma-horario-modal.component";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig } from "../../../../../cobranca/components/relatorio-cobranca/data-grid.model";
import { GridFilterConfig } from "../../../../../cobranca/components/relatorio-cobranca/data-grid-filter.model";
import { RelatorioComponent } from "../../../../../../../projects/ui/src/lib/components/relatorio/relatorio.component";
import { PermissaoService } from "pacto-layout";
import { getTipoCategoriaText } from "../../../../../../../projects/adm/src/app/cadastro-auxliar/enums/TipoCategoria";

@Component({
	selector: "pacto-turma-horario-edit-modal",
	templateUrl: "./turma-horario-edit-modal.component.html",
	styleUrls: ["./turma-horario-edit-modal.component.scss"],
})
export class TurmaHorarioModalComponent implements OnInit, AfterViewInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("traducaoTipoCategoria", { static: true })
	traducaoTipoCategoria: TraducoesXinglingComponent;
	@ViewChild("tipoCategoriaCelula", { static: true }) tipoCategoriaCelula;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	timerMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	addCategCapacidade: boolean = true;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	categoriasCapacidadeList: Array<CategoriaCapacidade> = [];
	categoriasCapacidadeData = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	itensPerPage = [{ id: 3, label: "3" }];
	page = 1;
	size = 3;
	getTipoCategoriaText: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>();
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		professorId: new FormControl(null),
		ambienteId: new FormControl(null),
		nivelTurmaId: new FormControl(null),
		horaInicial: new FormControl(),
		horaFinal: new FormControl(),
		maxAlunos: new FormControl(0),
		toleranciaMin: new FormControl(0),
		toleranciaAposMin: new FormControl(0),
		dom: new FormControl(false),
		seg: new FormControl(false),
		ter: new FormControl(false),
		qua: new FormControl(false),
		qui: new FormControl(false),
		sex: new FormControl(false),
		sab: new FormControl(false),
		todosDias: new FormControl(false),
		liberadoMarcacaoApp: new FormControl(true),
		horarioDisponivelVenda: new FormControl(true),
		dataSaiuTurma: new FormControl(null),
		duracao: new FormControl(),
		tipoCategoria: new FormControl(null),
		maxAlunosCategoria: new FormControl(0),
	});
	edicao: boolean = false;

	professores: Array<UsuarioBase>;
	ambientes: Array<Ambiente>;
	niveisTurma: Array<NivelTurma>;
	turmaEdit: TurmaCreateEdit;
	horario: HorarioTurma;

	constructor(
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private modalService: ModalService,
		private turmaService: TreinoApiTurmaService,
		private notificationService: SnotifyService,
		private openModal: NgbActiveModal,
		private permissaoService: PermissaoService
	) {}

	ngOnInit() {
		this.calcDuracao();
		if (this.temConfigHorarioCapacidadeCategoria) {
			this.configTable();
			this.createPageObject();
		}
	}

	ngAfterViewInit() {
		if (this.temConfigHorarioCapacidadeCategoria) {
			this.initTipoCategoriaArray();
		}
	}

	dto(): HorarioTurma {
		return {
			codigo:
				this.formGroup.get("codigo") && this.formGroup.get("codigo").value
					? this.formGroup.get("codigo").value
					: null,
			professorId: this.formGroup.get("professorId").value,
			ambienteId: this.formGroup.get("ambienteId").value,
			nivelTurmaId: this.formGroup.get("nivelTurmaId").value,
			horaInicial: this.formGroup.get("horaInicial").value,
			horaFinal: this.formGroup.get("horaFinal").value,
			maxAlunos: this.formGroup.get("maxAlunos").value,
			toleranciaMin: this.formGroup.get("toleranciaMin").value,
			toleranciaAposMin: this.formGroup.get("toleranciaAposMin").value,
			liberadoMarcacaoApp: this.formGroup.get("liberadoMarcacaoApp").value,
			horarioDisponivelVenda: this.formGroup.get("horarioDisponivelVenda")
				.value,
			dataSaiuTurma: this.formGroup.get("dataSaiuTurma").value,
			situacao: "AT",
			turma: this.turmaEdit.id,
			identificadorTurma: this.turmaEdit.identificador,
			horarioCapacidadeCategoria: this.categoriasCapacidadeList,
		};
	}

	validaProfInativo(): boolean {
		if (this.edicao) {
			let inativo = true;
			this.professores.forEach((p) => {
				if (p.id == this.formGroup.get("professorId").value) {
					inativo = false;
				}
			});
			return inativo;
		} else {
			return false;
		}
	}

	private initTipoCategoriaArray() {
		this.getTipoCategoriaText = getTipoCategoriaText(
			this.traducaoTipoCategoria
		);
		this.cd.detectChanges();
	}

	salvarIntegracao() {
		if (this.temConfigHorarioCapacidadeCategoria) {
			if (
				this.categoriasCapacidadeList !== null &&
				this.categoriasCapacidadeList.length > 0
			) {
				const somaCapacidade = this.categoriasCapacidadeList.reduce(
					(acc, curr) => {
						return acc + curr.capacidade;
					},
					0
				);

				if (somaCapacidade > Number(this.formGroup.get("maxAlunos").value)) {
					this.notificationService.error(
						"A soma das capacidades por tipo de categoria não pode ser maior que número maximo de aluno da turma"
					);
					return;
				}
			}
		}

		const ambienteIdSelecionado = this.formGroup.get("ambienteId").value;
		this.formGroup.get("ambienteId").setValue(null);
		this.ambientes.forEach((a) => {
			if (Number(a.id) === Number(ambienteIdSelecionado)) {
				this.formGroup.get("ambienteId").setValue(ambienteIdSelecionado);
			}
		});
		if (
			this.formGroup.get("horaInicial").value == null ||
			this.formGroup.get("horaInicial").value === "" ||
			this.formGroup.get("horaFinal").value == null ||
			this.formGroup.get("horaFinal").value === "" ||
			this.formGroup.get("horaInicial").value.includes("_") ||
			this.formGroup.get("horaFinal").value.includes("_")
		) {
			this.notificationService.error(
				"Hora inicial e hora final tem que ser informadas."
			);
		} else if (
			(this.formGroup.get("professorId") &&
				this.formGroup.get("professorId").value == null) ||
			(this.formGroup.get("ambienteId") &&
				this.formGroup.get("ambienteId").value == null) ||
			(this.formGroup.get("nivelTurmaId") &&
				this.formGroup.get("nivelTurmaId").value == null)
		) {
			this.notificationService.error(
				"Nenhum dos itens de seleção pode estar vazio! (Professor, Nível da Turma ou Ambiente)."
			);
		} else if (this.formGroup.get("maxAlunos").value <= 0) {
			this.notificationService.error(
				"O número máximo de alunos precisa ser maior do que 0."
			);
		} else if (
			this.formGroup.get("dom").value === false &&
			this.formGroup.get("seg").value === false &&
			this.formGroup.get("ter").value === false &&
			this.formGroup.get("qua").value === false &&
			this.formGroup.get("qui").value === false &&
			this.formGroup.get("sex").value === false &&
			this.formGroup.get("sab").value === false
		) {
			this.notificationService.error(
				"É necessário escolher pelo menos 1 dia da semana."
			);
		} else {
			const horarios = Array<HorarioTurma>();
			if (
				this.formGroup.get("todosDias") &&
				this.formGroup.get("todosDias").value
			) {
				if (this.dto().codigo && this.dto().codigo > 0) {
					this.notificationService.error(
						"Para edição individual de horario é inválido marcar 'todos os dias'"
					);
				} else {
					let diaSemanaNumero = 1;
					["DM", "SG", "TR", "QA", "QI", "SX", "SB"].forEach((dia) => {
						const horario = this.dto();

						horario.diaSemanaNumero = diaSemanaNumero;
						horario.dia = dia;
						horarios.push(horario);
						diaSemanaNumero = diaSemanaNumero++;
					});
				}
			} else {
				if (this.formGroup.get("dom") && this.formGroup.get("dom").value) {
					const horarioD = this.dto();
					if (this.validoAdicioanrHorario(horarioD, horarios)) {
						horarioD.diaSemanaNumero = 1;
						horarioD.dia = "DM";
						horarios.push(horarioD);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"DOM será removido."
						);
					}
				}
				if (this.formGroup.get("seg") && this.formGroup.get("seg").value) {
					const horarioS = this.dto();
					if (this.validoAdicioanrHorario(horarioS, horarios)) {
						horarioS.diaSemanaNumero = 2;
						horarioS.dia = "SG";
						horarios.push(horarioS);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"SEG será removido."
						);
					}
				}
				if (this.formGroup.get("ter") && this.formGroup.get("ter").value) {
					const horarioT = this.dto();
					if (this.validoAdicioanrHorario(horarioT, horarios)) {
						horarioT.diaSemanaNumero = 3;
						horarioT.dia = "TR";
						horarios.push(horarioT);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"TER será removido."
						);
					}
				}
				if (this.formGroup.get("qua") && this.formGroup.get("qua").value) {
					const horarioQA = this.dto();
					if (this.validoAdicioanrHorario(horarioQA, horarios)) {
						horarioQA.diaSemanaNumero = 4;
						horarioQA.dia = "QA";
						horarios.push(horarioQA);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"QUA será removido."
						);
					}
				}
				if (this.formGroup.get("qui") && this.formGroup.get("qui").value) {
					const horarioQI = this.dto();
					if (this.validoAdicioanrHorario(horarioQI, horarios)) {
						horarioQI.diaSemanaNumero = 5;
						horarioQI.dia = "QI";
						horarios.push(horarioQI);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"QUI será removido."
						);
					}
				}
				if (this.formGroup.get("sex") && this.formGroup.get("sex").value) {
					const horarioSE = this.dto();
					if (this.validoAdicioanrHorario(horarioSE, horarios)) {
						horarioSE.diaSemanaNumero = 6;
						horarioSE.dia = "SX";
						horarios.push(horarioSE);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"SEX será removido."
						);
					}
				}
				if (this.formGroup.get("sab") && this.formGroup.get("sab").value) {
					const horarioSA = this.dto();
					if (this.validoAdicioanrHorario(horarioSA, horarios)) {
						horarioSA.diaSemanaNumero = 7;
						horarioSA.dia = "SB";
						horarios.push(horarioSA);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
								"SAB será removido."
						);
					}
				}
			}
			if (horarios.length > 0) {
				this.turmaService
					.saveOrUpdateHorario(horarios)
					.subscribe((response) => {
						this.openModal.close({
							turmaId: this.turmaEdit.id,
							amb: this.ambientes,
							nvs: this.niveisTurma,
						});
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("createSuccess")
						);
					});
			} else {
				this.openModal.close({
					turmaId: this.turmaEdit.id,
					amb: this.ambientes,
					nvs: this.niveisTurma,
				});
			}
		}
	}

	validoAdicioanrHorario(
		horario: HorarioTurma,
		horarios: HorarioTurma[]
	): boolean {
		if (horario.codigo && horario.codigo > 0 && horarios.length > 0) {
			return false;
		}
		return true;
	}

	cadastrarAmbiente() {
		this.openModalAdicionarAmbientes(
			"Cadastrar novo ambiente",
			AmbienteHorarioModalComponent
		);
	}

	cadastrarNivelTurma() {
		this.openModalAdicionarNiveisTurma(
			"Cadastrar novo nível de turma",
			NivelTurmaHorarioModalComponent
		);
	}

	openModalAdicionarAmbientes(title, modalComponent: any) {
		const ref = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-adc-ambiente"
		);
		ref.result.then(
			(dto) => {
				dto.value = dto.id;
				dto.label = dto.nome;
				this.ambientes = [...this.ambientes, dto];
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	openModalAdicionarNiveisTurma(title, modalComponent: any) {
		const ref = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-adc-nivel"
		);
		ref.result.then(
			(dto) => {
				dto.value = dto.id;
				dto.label = dto.nome;
				this.niveisTurma = [...this.niveisTurma, dto];
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	marcarTodosDiasSemana() {
		this.formGroup.get("dom").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("seg").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("ter").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("qua").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("qui").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("sex").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("sab").setValue(!this.formGroup.get("todosDias").value);
	}

	validarTodosDiasSemanaMarcados(dia: string) {
		if (
			("seg" === dia
				? !this.formGroup.get("seg").value
				: this.formGroup.get("seg").value) &&
			("ter" === dia
				? !this.formGroup.get("ter").value
				: this.formGroup.get("ter").value) &&
			("qua" === dia
				? !this.formGroup.get("qua").value
				: this.formGroup.get("qua").value) &&
			("qui" === dia
				? !this.formGroup.get("qui").value
				: this.formGroup.get("qui").value) &&
			("sex" === dia
				? !this.formGroup.get("sex").value
				: this.formGroup.get("sex").value) &&
			("sab" === dia
				? !this.formGroup.get("sab").value
				: this.formGroup.get("sab").value) &&
			("dom" === dia
				? !this.formGroup.get("dom").value
				: this.formGroup.get("dom").value)
		) {
			this.formGroup.get("todosDias").setValue(true);
		} else {
			this.formGroup.get("todosDias").setValue(false);
		}
	}

	calcDuracao() {
		this.formGroup.get("duracao").disable();
		const inicio = new Date(
			"1111-11-11T" + this.formGroup.get("horaInicial").value
		);
		const fim = new Date("1111-11-11T" + this.formGroup.get("horaFinal").value);
		const diferenca = new Date(fim.getTime() - inicio.getTime());

		let resultado = diferenca.getUTCHours() + "h ";
		resultado += diferenca.getUTCMinutes() + "m ";
		this.formGroup.get("duracao").setValue(resultado);
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return this.categoriasCapacidadeData;
			},
			pagination: true,
			columns: [
				{
					nome: "tipoCategoria",
					titulo: "Tipo Categoria",
					visible: true,
					ordenavel: true,
					celula: this.tipoCategoriaCelula,
				},
				{
					nome: "capacidade",
					titulo: "Capacidade",
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					showIconFn: (row) => true,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.categoriasCapacidadeList = this.categoriasCapacidadeList.filter(
				(h) => h !== $event.row
			);
			if (this.tableData) {
				this.tableData.reloadData();
			}
			if (this.categoriasCapacidadeList.length === 0) {
				this.addCategCapacidade = true;
			}
			this.createPageObject();
			this.cd.detectChanges();
		}
	}

	get temConfigHorarioCapacidadeCategoria(): boolean {
		return (
			this.permissaoService &&
			this.permissaoService.temConfiguracaoEmpresaAdm(
				"horariocapacidadeporcategoria"
			)
		);
	}

	validarDadosCategoriasCapacidade(): boolean {
		if (
			this.formGroup.get("maxAlunos") === null ||
			this.formGroup.get("maxAlunos").value === null ||
			this.formGroup.get("maxAlunos").value === undefined ||
			this.formGroup.get("maxAlunos").value === "" ||
			Number(this.formGroup.get("maxAlunos").value) === 0
		) {
			this.categoriasCapacidadeList = [];
			this.createPageObject();
			this.notificationService.error(
				"Informe o número máximo de alunos da turma"
			);
			return false;
		}
		if (
			this.formGroup.get("maxAlunosCategoria") === null ||
			this.formGroup.get("maxAlunosCategoria").value === null ||
			this.formGroup.get("maxAlunosCategoria").value === "" ||
			Number(this.formGroup.get("maxAlunosCategoria").value) === 0
		) {
			this.notificationService.error("Capacidade não pode ser 0");
			return false;
		}

		const index = this.categoriasCapacidadeList.findIndex(
			(c) => c.tipoCategoria === this.formGroup.get("tipoCategoria").value
		);
		if (index > -1) {
			this.notificationService.error(
				"Categoria já adicionada, selecione outra categoria"
			);
			return false;
		}

		const somaCapacidade = this.categoriasCapacidadeList.reduce((acc, curr) => {
			return acc + curr.capacidade;
		}, 0);

		if (
			somaCapacidade + Number(this.formGroup.get("maxAlunosCategoria").value) >
			this.formGroup.get("maxAlunos").value
		) {
			this.notificationService.error(
				"A soma das capacidades por tipo de categoria não pode ser maior que número maximo de aluno da turma"
			);
			return false;
		}

		return true;
	}

	salvarCategoriasCapacidade() {
		if (this.validarDadosCategoriasCapacidade()) {
			const obj = {
				capacidade: Number(this.formGroup.get("maxAlunosCategoria").value),
				tipoCategoria: this.formGroup.get("tipoCategoria").value,
			};

			if (this.categoriasCapacidadeList == null) {
				this.categoriasCapacidadeList = [];
			}

			this.categoriasCapacidadeList.push(obj);
			if (this.tableData) {
				this.tableData.reloadData();
			}
			if (this.categoriasCapacidadeList.length > 0) {
				this.addCategCapacidade = false;
			}
			this.formGroup.get("maxAlunosCategoria").setValue(0);
			this.formGroup.get("tipoCategoria").setValue(null);
			this.createPageObject();
			this.cd.detectChanges();
		}
	}

	createPageObject(page = 1, size = 3, reloadData = true) {
		this.categoriasCapacidadeData.totalElements =
			this.categoriasCapacidadeList.length;
		this.categoriasCapacidadeData.size = size;
		this.categoriasCapacidadeData.totalPages = Math.ceil(
			+(
				this.categoriasCapacidadeData.totalElements /
				this.categoriasCapacidadeData.size
			)
		);
		this.categoriasCapacidadeData.first = page === 0 || page === 1;
		this.categoriasCapacidadeData.last =
			page === this.categoriasCapacidadeData.totalPages;
		this.categoriasCapacidadeData.content = this.categoriasCapacidadeList.slice(
			size * page - size,
			size * page
		);
		this.tableData.showBtnAdd = false;
		if (reloadData) {
			this.tableData.reloadData();
		}
		this.tableData.ngbPage = this.page;
	}

	ordenarItens(eventSort) {
		this.categoriasCapacidadeData.content = this.sortList(
			this.categoriasCapacidadeData.content,
			eventSort.columnName,
			eventSort.direction
		);
		this.tableData.reloadData();
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (a[columnName] > b[columnName]) {
					return 1;
				} else if (a[columnName] < b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (a[columnName] < b[columnName]) {
					return 1;
				} else if (a[columnName] > b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	pageChangeEvent(page) {
		this.page = page;
		this.createPageObject(page);
	}

	pageSizeChange(size) {
		this.createPageObject(this.page, size);
	}
}
