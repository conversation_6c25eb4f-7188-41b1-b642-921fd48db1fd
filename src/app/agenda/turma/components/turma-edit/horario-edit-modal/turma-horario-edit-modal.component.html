<div class="main-content">
	<div *ngIf="validaProfInativo()" class="line-prof-inativo">
		<span class="pct pct-alert-triangle line-danger-text-adjust"></span>
		O professor se encontra inativo, por favor realize a troca de professor
	</div>
	<div class="row">
		<div class="col-md-4 mr-auto">
			<pacto-cat-select
				[control]="formGroup.get('professorId')"
				[id]="'professor-select'"
				[items]="professores"
				i18n-label="@@crud-turma-horarios:select:professor:label"
				i18n-mensagem="@@crud-turmas:select:professor:mensagem"
				label="Professor*"
				mensagem="Selecione um professor."></pacto-cat-select>
		</div>
		<div class="col-md-4 mr-auto">
			<pacto-cat-select
				[control]="formGroup.get('ambienteId')"
				[id]="'ambiente-select'"
				[items]="ambientes"
				i18n-label="@@crud-turma-horarios:select:ambiente:label"
				i18n-mensagem="@@crud-turmas:select:ambiente:mensagem"
				label="Ambiente*"
				mensagem="Selecione um ambiente."></pacto-cat-select>
			<!--            <p class='cadastrar-ambiente' (click)='cadastrarAmbiente()'>Cadastrar novo ambiente</p>-->
		</div>
		<div class="col-md-4 mr-auto">
			<pacto-cat-select
				[control]="formGroup.get('nivelTurmaId')"
				[id]="'nivel-select'"
				[items]="niveisTurma"
				i18n-label="@@crud-turma-nivelTurma:select:nivelTurma:label"
				i18n-mensagem="@@crud-turmas:select:nivelTurma:mensagem"
				label="Nível*"
				mensagem="Selecione um professor."></pacto-cat-select>
			<!--            <p class='cadastrar-ambiente' (click)='cadastrarNivelTurma()'>Cadastrar novo nível da turma</p>-->
		</div>
	</div>

	<div class="row top20">
		<div (change)="calcDuracao()" class="col-md-4">
			<pacto-cat-form-input
				[control]="formGroup.get('horaInicial')"
				[errorMsg]="'Campo obrigatório'"
				[id]="'input-horaInicial'"
				[label]="'Hora inicial*'"
				[placeholder]="'00:00'"
				[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
		</div>

		<div (change)="calcDuracao()" class="col-md-4">
			<pacto-cat-form-input
				[control]="formGroup.get('horaFinal')"
				[errorMsg]="'Campo obrigatório'"
				[id]="'input-horaFinal'"
				[label]="'Hora final*'"
				[placeholder]="'00:00'"
				[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
		</div>

		<div
			class="col-md-4"
			*ngIf="!formGroup.get('duracao').value.includes('NaN')">
			<pacto-cat-form-input
				[control]="formGroup.get('duracao')"
				[id]="'input-horaFinal'"
				[label]="'Duração'"
				[placeholder]="'0h 0m'"></pacto-cat-form-input>
		</div>
	</div>

	<div class="row top20">
		<div class="col-md-4">
			<pacto-cat-form-input-number
				[formControl]="formGroup.get('maxAlunos')"
				id="input-maxAlunos"
				label="Número máx. alunos*"></pacto-cat-form-input-number>
		</div>

		<div class="col-md-4">
			<pacto-cat-form-input-number
				[formControl]="formGroup.get('toleranciaMin')"
				id="input-toleranciaMin"
				label="Tolerância antes do início (minutos)"></pacto-cat-form-input-number>
		</div>

		<div class="col-md-4">
			<pacto-cat-form-input-number
				[formControl]="formGroup.get('toleranciaAposMin')"
				id="input-toleranciaAposMin"
				label="Tolerância depois do início (minutos)"></pacto-cat-form-input-number>
		</div>
	</div>

	<label>Dias da semana*</label>
	<div class="row">
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('seg')"
				[control]="formGroup.get('seg')"
				[label]="'Seg'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('ter')"
				[control]="formGroup.get('ter')"
				[label]="'Ter'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('qua')"
				[control]="formGroup.get('qua')"
				[label]="'Qua'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('qui')"
				[control]="formGroup.get('qui')"
				[label]="'Qui'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('sex')"
				[control]="formGroup.get('sex')"
				[label]="'Sex'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('sab')"
				[control]="formGroup.get('sab')"
				[label]="'Sab'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-3">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('dom')"
				[control]="formGroup.get('dom')"
				[label]="'Dom'"></pacto-cat-checkbox>
		</div>
		<div class="col-md-2 todosDiasRight">
			<pacto-cat-checkbox
				(click)="marcarTodosDiasSemana()"
				[control]="formGroup.get('todosDias')"
				[label]="'Todos os dias'"></pacto-cat-checkbox>
		</div>
	</div>

	<div class="row top20" *ngIf="temConfigHorarioCapacidadeCategoria">
		<div class="col-md-4">
			<pacto-cat-select
				[control]="formGroup.get('tipoCategoria')"
				[items]="getTipoCategoriaText"
				label="Tipo Categoria"></pacto-cat-select>
		</div>

		<div class="col-md-4">
			<pacto-cat-form-input-number
				[formControl]="formGroup.get('maxAlunosCategoria')"
				label="Número máx. alunos"></pacto-cat-form-input-number>
		</div>

		<button
			ds3-text-button
			class="margin-top-47"
			(click)="salvarCategoriasCapacidade()">
			Adicionar capacidade por categoria
		</button>
	</div>
	<div
		class="col-md-9-adjust top10-border-top"
		*ngIf="temConfigHorarioCapacidadeCategoria">
		<pacto-relatorio
			#tableData
			[table]="table"
			class="tableHours"
			[showShare]="false"
			(pageChangeEvent)="pageChangeEvent($event)"
			(pageSizeChange)="pageSizeChange($event)"
			(iconClick)="actionClickHandler($event)"
			(sortEvent)="ordenarItens($event)"
			[itensPerPage]="itensPerPage"
			actionTitulo="Ações"></pacto-relatorio>
	</div>

	<div class="row top10-border-bottom"></div>

	<div class="row top20">
		<div class="col-md-5 mr-auto">
			<pacto-cat-checkbox
				[control]="formGroup.get('liberadoMarcacaoApp')"
				[label]="'Mostra aula no App Treino'"></pacto-cat-checkbox>
		</div>
	</div>
	<div class="row top20">
		<div class="col-md-5 mr-auto">
			<pacto-cat-checkbox
				[control]="formGroup.get('horarioDisponivelVenda')"
				[label]="'Horário disponível na venda'"
				class="margin-top-40"></pacto-cat-checkbox>
		</div>
	</div>
	<div class="row justify-content-end btns-ativar-inativar">
		<button
			(click)="salvarIntegracao()"
			class="btn btn-primary"
			id="btn-salvar">
			Salvar horário
		</button>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@crud-horarios:create-success" xingling="createSuccess">
		Horario criado com sucesso.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:falha-salvar-integracao"
		xingling="falha-salvar-integracao">
		Falha ao tentar salvar integração!
	</span>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducaoTipoCategoria>
	<span i18n="@@categoriaCapacidade:naoSocio" xingling="NS">Não Sócio</span>
	<span i18n="@@categoriaCapacidade:aluno" xingling="AL">Aluno</span>
	<span i18n="@@categoriaCapacidade:socio" xingling="SO">Sócio</span>
	<span i18n="@@categoriaCapacidade:comerciario" xingling="CO">
		Comerciário
	</span>
	<span i18n="@@categoriaCapacidade:dependente" xingling="DE">Dependente</span>
	<span i18n="@@categoriaCapacidade:aluno" xingling="US">Usuário</span>
	<span i18n="@@categoriaCapacidade:eventos" xingling="EV">Eventos</span>
</pacto-traducoes-xingling>

<ng-template #tipoCategoriaCelula let-item="item">
	{{ traducaoTipoCategoria.getLabel(item.tipoCategoria) }}
</ng-template>
