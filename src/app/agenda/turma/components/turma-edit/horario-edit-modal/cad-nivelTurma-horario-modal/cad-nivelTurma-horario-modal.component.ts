import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { TraducoesXinglingComponent } from "ui-kit";
import { TreinoApiTurmaService } from "treino-api";
import { NivelTurma } from "../../../../../../../../projects/treino-api/src/lib/turma.model";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-cad-nivelTurma-horario-modal",
	templateUrl: "./cad-nivelTurma-horario-modal.component.html",
	styleUrls: ["./cad-nivelTurma-horario-modal.component.scss"],
})
export class NivelTurmaHorarioModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	formGroup: FormGroup = new FormGroup({
		descricao: new FormControl(""),
		codigo: new FormControl(null),
		nivelMgb: new FormControl(null),
	});

	niveisMgb: Array<string>;

	constructor(
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private turmaService: TreinoApiTurmaService,
		private openModal: NgbActiveModal,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.cd.detectChanges();
	}

	salvarCadastroNivel() {
		const nivelTurmaDTO: NivelTurma = new NivelTurma();
		nivelTurmaDTO.nome = this.formGroup.get("descricao").value;
		if (!nivelTurmaDTO.nome) {
			this.notificationService.error("Nome não pode estar vazio.");
		} else {
			this.turmaService
				.criarNivelTurmaHorarioTurma(nivelTurmaDTO)
				.subscribe((response) => {
					if (response.retorno) {
						this.openModal.close(response.retorno);
					} else {
						this.notificationService.error(
							"Já existe um nivel turma com este mesmo nome."
						);
					}
				});
		}
	}

	salvar(labelMensagemSucesso?: string) {
		console.log("salvei");
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
