import {
	AfterContentChecked,
	ChangeDetector<PERSON>ef,
	Component,
	EventEmitter,
	Inject,
	LOCALE_ID,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import {
	SnotifyButton,
	SnotifyPosition,
	SnotifyService,
	SnotifyToastConfig,
} from "ng-snotify";
import { SnotifyType } from "ng-snotify/snotify/types/snotify.type";
import { SnotifyAnimate } from "ng-snotify/snotify/interfaces/SnotifyAnimate.interface";
import { LocalizationService } from "@base-core/localization/localization.service";
import {
	Modalidade,
	TreinoApiTurmaService,
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
	TreinoApiModalidadeService,
} from "treino-api";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import {
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SafeHtml } from "@angular/platform-browser";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { DatePipe } from "@angular/common";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { Ambiente } from "../../../../../../projects/treino-api/src/lib/aula.model";
import {
	TurmaCreateEdit,
	NivelTurma,
} from "../../../../../../projects/treino-api/src/lib/turma.model";
import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";
import { UsuarioBase } from "sdk";
import { TurmaHorarioModalComponent } from "./horario-edit-modal/turma-horario-edit-modal.component";
import { ConfirmTurmaEditModalComponent } from "./modal-confirm-turma-edit/confirm-turma-edit-modal.component";
import { AgendaCardsStateService } from "../../../agenda/services/agenda-cards-state.service";
import { ConfirmEditHorarioTurmaModalComponent } from "./modal-confirm-edit-horario-turma/confirm-edit-horario-turma-modal.component";
import { ModalConfirmSincronizarMgbComponent } from "./modal-confirm-sincronizar-mgb/modal-confirm-sincronizar-mgb.component";

export enum TipoAntecedenciaMarcar {
	"Nenhum",
	"Não Validar",
	"Qualquer dia",
	"Somente no dia",
}

export enum NiveisDescontoOcupacao {
	"Sem desconto",
	"1(um) nível de desconto",
	"2(dois) níveis de desconto",
	"3(três) níveis de desconto",
	"4(quatro) níveis de desconto",
	"5(cinco) níveis de desconto",
}

export class SelectItemPactoCatSelect {
	value: string;
	label: string;
}

export class SelectItemPactoSelect {
	id: string;
	nome: string;
}

@Component({
	selector: "pacto-turma-edit",
	templateUrl: "./turma-edit.component.html",
	styleUrls: ["./turma-edit.component.scss"],
})
export class TurmaEditComponent implements OnInit, AfterContentChecked {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@Output() reloadEvent: EventEmitter<any> = new EventEmitter();
	linkVideos = [];
	modalidades: Array<Modalidade> = [];
	turmaEditDps: TurmaCreateEdit;
	turmaEdit: TurmaCreateEdit;
	operation: string;
	entity = true;
	tiposAntecedenciaMarcar: Array<SelectItemPactoSelect>;
	niveisDescontoOcupacao: Array<SelectItemPactoSelect>;
	labelNivel1desconto: string = "";
	labelNivel2desconto: string = "";
	labelNivel3desconto: string = "";
	labelNivel4desconto: string = "";
	labelNivel5desconto: string = "";
	isFormGroupValid: boolean = false;
	urlVideoYoutube: boolean = false;
	maskOptionsInputPercent = {
		align: "left",
		allowNegative: false,
		allowZero: false,
		nullable: false,
		precision: 2,
		suffix: "",
		decimal: ",",
		thousands: ".",
		prefix: "",
		max: "100",
	};

	professores: ApiResponseList<UsuarioBase> = {
		content: [],
	};
	ambientes: ApiResponseList<Ambiente> = {
		content: [],
	};
	niveisTurma: ApiResponseList<NivelTurma> = {
		content: [],
	};

	configSnotify: SnotifyToastConfig = new (class implements SnotifyToastConfig {
		animation: SnotifyAnimate;
		backdrop: number;
		bodyMaxLength: number;
		buttons: SnotifyButton[];
		closeOnClick: boolean;
		html: string | SafeHtml;
		icon: string;
		iconClass: string;
		pauseOnHover: boolean;
		placeholder: string;
		position: SnotifyPosition;
		showProgressBar: boolean;
		timeout: number;
		titleMaxLength: number;
		type: SnotifyType;
	})();

	formGroup: FormGroup = new FormGroup({
		id: new FormControl(0),
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		cor: new FormControl("#1B4166", [Validators.required]),
		modalidade: new FormControl(null, [Validators.required]),
		idadeMinima: new FormControl(0),
		idadeMinimaMeses: new FormControl(0),
		idadeMaxima: new FormControl(null, [
			Validators.required,
			Validators.min(1),
		]),
		idadeMaximaMeses: new FormControl(0),
		identificador: new FormControl(null, [Validators.required]),
		dataInicial: new FormControl(new Date().getTime(), [Validators.required]),
		dataFinal: new FormControl(new Date().getTime(), [Validators.required]),
		minutosAntecedenciaDesmarcarAula: new FormControl(0),
		minutosAposInicioApp: new FormControl(0),
		tipoAntecedenciaMarcarAula: new FormControl(
			TipoAntecedenciaMarcar["Não Validar"]
		),
		minutosAntecedenciaMarcarAula: new FormControl(0),
		qtdeNivelOcupacao: new FormControl(NiveisDescontoOcupacao["Sem desconto"]),
		percDescOcupacaoNivel1: new FormControl(0),
		percDescOcupacaoNivel2: new FormControl(0),
		percDescOcupacaoNivel3: new FormControl(0),
		percDescOcupacaoNivel4: new FormControl(0),
		percDescOcupacaoNivel5: new FormControl(0),
		bloquearMatriculasAcimaLimite: new FormControl(false),
		permitirDesmarcarReposicoes: new FormControl(false),
		permiteAlunoOutraEmpresa: new FormControl(false),
		monitorada: new FormControl(false),
		bloquearReposicaoAcimaLimite: new FormControl(false),
		permitirAulaExperimental: new FormControl(false),
		bloquearLotacaoFutura: new FormControl(false),
	});
	formGroupVideosUri: FormGroup = new FormGroup({
		id: new FormControl(),
		linkVideo: new FormControl("", [Validators.required]),
		professor: new FormControl(false),
	});
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("diaColumnName", { static: true }) diaColumnName;
	@ViewChild("codigoColumnName", { static: true }) codigoColumnName;
	@ViewChild("professorColumnName", { static: true }) professorColumnName;
	@ViewChild("duracaoColumnName", { static: true }) duracaoColumnName;
	@ViewChild("horarioTurmaColumnName", { static: true }) horarioTurmaColumnName;
	@ViewChild("ambienteColumnName", { static: true }) ambienteColumnName;
	@ViewChild("maxAlunosColumnName", { static: true }) maxAlunosColumnName;
	@ViewChild("toleranciaMinColumnName", { static: true })
	toleranciaMinColumnName;
	@ViewChild("nivelTurmaColumnName", { static: true }) nivelTurmaColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("horarioTurmaCelula", { static: true }) horarioTurmaCelula;
	@ViewChild("professorCelula", { static: true }) professorCelula;
	@ViewChild("codigoCelula", { static: true }) codigoCelula;
	@ViewChild("ambienteCelula", { static: true }) ambienteCelula;
	@ViewChild("duracaoCelula", { static: true }) duracaoCelula;
	@ViewChild("diasCelula", { static: true }) diasCelula;
	@ViewChild("maxAlunosCelula", { static: true }) maxAlunosCelula;
	@ViewChild("toleranciaMinCelula", { static: true }) toleranciaMinCelula;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("nivelTurma", { static: true }) nivelTurmaCelula;

	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;

	table: PactoDataGridConfig;
	filterConfig: any;
	mgb: any;
	showBtnSyncTurmaMgb = false;

	constructor(
		@Inject(LOCALE_ID) private locale,
		private localization: LocalizationService,
		private modalidadeService: TreinoApiModalidadeService,
		private notificationService: SnotifyService,
		private turmaService: TreinoApiTurmaService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private ambienteService: TreinoApiAmbienteService,
		private niveisTurmaService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private modal: NgbModal,
		private configCache: TreinoConfigCacheService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private session: SessionService,
		private ngbModal: NgbModal,
		private modalService: ModalService,
		private agendaStateService: AgendaCardsStateService
	) {}

	ngOnInit() {
		this.configTable();
		this.configFilters();

		this.tiposAntecedenciaMarcar = this.getSelectItemsPactoSelect([
			{
				id: TipoAntecedenciaMarcar["Não Validar"],
				nome: TipoAntecedenciaMarcar[TipoAntecedenciaMarcar["Não Validar"]],
			},
			{
				id: TipoAntecedenciaMarcar["Qualquer dia"],
				nome: TipoAntecedenciaMarcar[TipoAntecedenciaMarcar["Qualquer dia"]],
			},
			{
				id: TipoAntecedenciaMarcar["Somente no dia"],
				nome: TipoAntecedenciaMarcar[TipoAntecedenciaMarcar["Somente no dia"]],
			},
		]);

		this.niveisDescontoOcupacao = this.getSelectItemsPactoSelect([
			{
				id: NiveisDescontoOcupacao["Sem desconto"],
				nome: NiveisDescontoOcupacao[NiveisDescontoOcupacao["Sem desconto"]],
			},
			{
				id: NiveisDescontoOcupacao["1(um) nível de desconto"],
				nome: NiveisDescontoOcupacao[
					NiveisDescontoOcupacao["1(um) nível de desconto"]
				],
			},
			{
				id: NiveisDescontoOcupacao["2(dois) níveis de desconto"],
				nome: NiveisDescontoOcupacao[
					NiveisDescontoOcupacao["2(dois) níveis de desconto"]
				],
			},
			{
				id: NiveisDescontoOcupacao["3(três) níveis de desconto"],
				nome: NiveisDescontoOcupacao[
					NiveisDescontoOcupacao["3(três) níveis de desconto"]
				],
			},
			{
				id: NiveisDescontoOcupacao["4(quatro) níveis de desconto"],
				nome: NiveisDescontoOcupacao[
					NiveisDescontoOcupacao["4(quatro) níveis de desconto"]
				],
			},
			{
				id: NiveisDescontoOcupacao["5(cinco) níveis de desconto"],
				nome: NiveisDescontoOcupacao[
					NiveisDescontoOcupacao["5(cinco) níveis de desconto"]
				],
			},
		]);

		this.route.params.subscribe((params) => {
			this.formGroup.get("id").disable();
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
				this.table.endpointUrl = this.rest.buildFullUrl(
					`turmas/horarioTurma/${id}`
				);
				this.table.logUrl = this.rest.buildFullUrl(`log/horarioTurma/${id}`);
			} else {
				this.operation = "create";
			}

			zip(
				this.getAmbientes(),
				this.getNiveisTurma(),
				this.getProfessor()
			).subscribe(() => {
				this.obterModalidades();
				this.configFilters();
			});

			this.tableData.reloadData();
		});
		this.isMgbAtivo();

		this.cd.detectChanges();
	}

	ngAfterContentChecked() {
		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

	private obterModalidades() {
		this.modalidadeService.obterTodasModalidadesTurma().subscribe((dados) => {
			this.modalidades = dados.content;
			if (
				this.turmaEdit &&
				this.turmaEdit.modalidade &&
				this.turmaEdit.modalidade.id
			) {
				const regex = new RegExp(this.turmaEdit.modalidade.id, "i");
				if (
					this.modalidades.find((item) => regex.test(item.id)) !== undefined
				) {
					this.formGroup
						.get("modalidade")
						.setValue(this.turmaEdit.modalidade.id);
				}
				this.cd.detectChanges();
			}
		});
	}

	private loadEntities(id) {
		if (id) {
			this.turmaService.obterTurma(id).subscribe((dados) => {
				if (dados) {
					this.turmaEdit = dados;
					this.entity = false;

					dados.linkVideos.forEach((link) => {
						this.linkVideos.push({
							id: link.id,
							linkVideo: link.linkVideo,
							professor: link.professor,
						});
					});

					this.loadForm();
				} else {
					console.log("erro");
				}
			});
		}
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	removeHandler(item) {
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, "");
			handler.result
				.then(() => {
					this.turmaService
						.removerHorarioTurma(item.codigo)
						.subscribe((resultRemove) => {
							if (resultRemove !== "sucess") {
								this.snotifyService.error("" + resultRemove);
							} else {
								this.snotifyService.success(removeSuccess);
								this.tableData.reloadData();
							}
						});
				})
				.catch(() => {
					this.snotifyService.error(removeError);
				});
		});
	}

	btnClickHandler() {
		this.prepararModalAdicionarHorarios();
	}

	btnEditHandler(item) {
		this.turmaService.validarHorarioTurma(item.codigo).subscribe((result) => {
			if (result === "EXCLUIR") {
				this.openModalEditarHorarios(
					"Edição de horário",
					TurmaHorarioModalComponent,
					item
				);
			} else {
				let modal = this.modalService.open(
					"Edição de horário",
					ConfirmEditHorarioTurmaModalComponent,
					PactoModalSize.MEDIUM
				);
				modal.result.then(
					(ok) => {
						if (ok) {
							this.openModalEditarHorarios(
								"Edição de horário",
								TurmaHorarioModalComponent,
								item
							);
						}
					},
					() => {}
				);
			}
		});
	}

	openModalEditarHorarios(title, modalComponent: any, item: any) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = true;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.niveisTurma = this.niveisTurma.content;
		modal.componentInstance.professores = this.professores.content;

		modal.componentInstance.formGroup.get("codigo").setValue(item.codigo);
		modal.componentInstance.formGroup
			.get("professorId")
			.setValue(item.professorId);
		modal.componentInstance.formGroup
			.get("ambienteId")
			.setValue(item.ambienteId);
		modal.componentInstance.formGroup
			.get("nivelTurmaId")
			.setValue(item.nivelTurmaId);
		modal.componentInstance.formGroup
			.get("horaInicial")
			.setValue(item.horaInicial);
		modal.componentInstance.formGroup.get("horaFinal").setValue(item.horaFinal);
		modal.componentInstance.formGroup.get("maxAlunos").setValue(item.maxAlunos);
		modal.componentInstance.formGroup
			.get("toleranciaMin")
			.setValue(item.toleranciaMin);
		modal.componentInstance.formGroup
			.get("toleranciaAposMin")
			.setValue(item.toleranciaAposMin);
		modal.componentInstance.formGroup
			.get("dataSaiuTurma")
			.setValue(item.dataSaiuTurma);
		if (item.dia === "DM") {
			modal.componentInstance.formGroup.get("dom").setValue(true);
		}
		if (item.dia === "SG") {
			modal.componentInstance.formGroup.get("seg").setValue(true);
		}
		if (item.dia === "TR") {
			modal.componentInstance.formGroup.get("ter").setValue(true);
		}
		if (item.dia === "QA") {
			modal.componentInstance.formGroup.get("qua").setValue(true);
		}
		if (item.dia === "QI") {
			modal.componentInstance.formGroup.get("qui").setValue(true);
		}
		if (item.dia === "SX") {
			modal.componentInstance.formGroup.get("sex").setValue(true);
		}
		if (item.dia === "SB") {
			modal.componentInstance.formGroup.get("sab").setValue(true);
		}
		modal.componentInstance.formGroup
			.get("horarioDisponivelVenda")
			.setValue(item.horarioDisponivelVenda);
		modal.componentInstance.formGroup
			.get("liberadoMarcacaoApp")
			.setValue(item.liberadoMarcacaoApp);
		modal.componentInstance.turmaEdit = this.turmaEdit;
		modal.componentInstance.categoriasCapacidadeList =
			item.horarioCapacidadeCategoria;
		modal.result.then(
			(ambNvs) => {
				this.turmaEdit.id = ambNvs.turmaId;
				this.formGroup.get("id").setValue(this.turmaEdit.id);
				this.entity = false;
				this.ambientes.content = ambNvs.amb;
				this.niveisTurma.content = ambNvs.nvs;
				this.configTable();
				this.configFilters();
				this.table.endpointUrl = this.rest.buildFullUrl(
					`turmas/horarioTurma/${this.turmaEdit.id}`
				);
				this.table.logUrl = this.rest.buildFullUrl(
					`log/horarioTurma/${this.turmaEdit.id}`
				);
				this.cd.detectChanges();
				this.tableData.reloadData();
			},
			() => {}
		);
	}

	prepararModalAdicionarHorarios() {
		if (this.validarCamposInformacoes()) {
			if (this.formGroup.valid && !this.turmaEdit) {
				this.turmaEdit = this.formGroup.getRawValue();
			}
			if (
				this.turmaEdit &&
				(this.turmaEdit.id === null ||
					this.turmaEdit.id === undefined ||
					this.turmaEdit.id === 0)
			) {
				this.turmaEdit.modalidade = { id: this.turmaEdit.modalidade };
				this.turmaEdit.empresa = { codigo: Number(this.session.codigoEmpresa) };
				this.turmaService.criarTurma(this.turmaEdit).subscribe((result) => {
					if (result.retorno && result.retorno.id > 0) {
						this.turmaEdit.id = result.retorno.id;
						this.getModalAdicionarHorario(
							"Cadastro de horário",
							TurmaHorarioModalComponent
						);
					}
				});
			} else {
				this.getModalAdicionarHorario(
					"Cadastro de horário",
					TurmaHorarioModalComponent
				);
			}
		}
	}

	getModalAdicionarHorario(title, modalComponent: any) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.professores = this.professores.content;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.niveisTurma = this.niveisTurma.content;
		modal.componentInstance.turmaEdit = this.turmaEdit;
		modal.result.then(
			(ambNvs) => {
				this.turmaEdit.id = ambNvs.turmaId;
				this.formGroup.get("id").setValue(this.turmaEdit.id);
				this.entity = false;
				this.ambientes.content = ambNvs.amb;
				this.niveisTurma.content = ambNvs.nvs;
				this.configTable();
				this.configFilters();
				this.table.endpointUrl = this.rest.buildFullUrl(
					`turmas/horarioTurma/${this.turmaEdit.id}`
				);
				this.table.logUrl = this.rest.buildFullUrl(
					`log/horarioTurma/${this.turmaEdit.id}`
				);
				this.cd.detectChanges();
				this.tableData.reloadData();
			},
			() => {}
		);
	}

	private getProfessor(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradoresZW(false, false)
			.pipe(
				map((dados) => {
					dados.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = dados;
					return true;
				})
			);
		return professores$;
	}

	private getAmbientes(): Observable<any> {
		const ambientes$ = this.ambienteService.obterTodosAmbientesAtivo().pipe(
			map((dados) => {
				dados.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientes = dados;
				return true;
			})
		);
		return ambientes$;
	}

	private getNiveisTurma(): Observable<any> {
		const niveisTurma$ = this.ambienteService
			.obterTodosNiveisTurmaAmbiente()
			.pipe(
				map((dados) => {
					dados.content.forEach((nivelTurma: any) => {
						nivelTurma.value = nivelTurma.id;
						nivelTurma.label = nivelTurma.nome;
					});
					this.niveisTurma = dados;
					return true;
				})
			);
		return niveisTurma$;
	}

	private getSelectItemsPactoCatSelect(
		items: any
	): Array<SelectItemPactoCatSelect> {
		const selectItems = new Array<SelectItemPactoCatSelect>();
		items.forEach((d) => {
			const selectItem = new SelectItemPactoCatSelect();
			selectItem.value = d;
			selectItem.label = d;
			selectItems.push(selectItem);
		});
		return selectItems;
	}

	private getSelectItemsPactoSelect(items: any): Array<SelectItemPactoSelect> {
		const selectItems = new Array<SelectItemPactoSelect>();
		items.forEach((d) => {
			const selectItem = new SelectItemPactoSelect();
			selectItem.id = d.id;
			selectItem.nome = d.nome;
			selectItems.push(selectItem);
		});
		return selectItems;
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "situacao",
					label: "Situação",
					type: GridFilterType.MANY,
					options: [
						{ value: "ATIVO", label: "Ativo" },
						{ value: "INATIVO", label: "Inativo" },
					],
					initialValue: ["ATIVO"],
				},
				{
					name: "professor",
					label: this.professorColumnName,
					type: GridFilterType.MANY,
					options: this.professores.content,
				},
				{
					name: "ambiente",
					label: this.ambienteColumnName,
					type: GridFilterType.MANY,
					options: this.ambientes.content,
				},
				{
					name: "nivelTurma",
					label: this.nivelTurmaColumnName,
					type: GridFilterType.MANY,
					options: this.niveisTurma.content,
				},
				{
					name: "dias",
					label: this.diaColumnName,
					type: GridFilterType.MANY,
					options: this.getSelectItemsPactoCatSelect([
						"SG",
						"TR",
						"QA",
						"QI",
						"SX",
						"SB",
						"DM",
					]),
				},
			],
		};
	}

	private configTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			buttons: {
				conteudo: this.buttonName,
				nome: "add",
				id: "btn-nova-aula",
			},
			columns: [
				{
					nome: "codigo",
					titulo: this.codigoColumnName,
					buscaRapida: false,
					visible: false,
					ordenavel: true,
					defaultVisible: false,
					campo: "codigo",
					celula: this.codigoCelula,
				},
				{
					nome: "professor",
					titulo: this.professorColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "professor",
					celula: this.professorCelula,
				},
				{
					nome: "ambiente",
					titulo: this.ambienteColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "ambiente",
					celula: this.ambienteCelula,
				},
				{
					nome: "horarioTurma",
					titulo: this.horarioTurmaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "horarioTurma",
					celula: this.horarioTurmaCelula,
				},
				{
					nome: "duracao",
					titulo: this.duracaoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "duracao",
					celula: this.duracaoCelula,
				},
				{
					nome: "nivelTurma",
					titulo: this.nivelTurmaColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nivelTurma",
					celula: this.nivelTurmaCelula,
				},
				{
					nome: "diasemana",
					titulo: this.diaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "dia",
					celula: this.diasCelula,
				},
				{
					nome: "nrmaximoaluno",
					titulo: this.maxAlunosColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "maxAlunos",
					celula: this.maxAlunosCelula,
				},
				{
					nome: "toleranciaentradaminutos",
					titulo: this.toleranciaMinColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "toleranciaMin",
					celula: this.toleranciaMinCelula,
				},
				{
					nome: "horarioDisponivelVenda",
					titulo: "Hórario Disp. (venda)",
					buscaRapida: false,
					visible: false,
					ordenavel: false,
					defaultVisible: false,
					valueTransform: (v) => (v ? "Sim" : "Não"),
					campo: "horarioDisponivelVenda",
				},
				{
					nome: "liberadoMarcacaoApp",
					titulo: "Liberado Marcação App",
					buscaRapida: false,
					visible: false,
					ordenavel: false,
					defaultVisible: false,
					valueTransform: (v) => (v ? "Sim" : "Não"),
					campo: "liberadoMarcacaoApp",
				},
				{
					nome: "dataSaiuTurma",
					titulo: "Data Saiu Turma",
					buscaRapida: false,
					visible: false,
					ordenavel: false,
					defaultVisible: false,
					campo: "dataSaiuTurma",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipInativar,
				},
			],
		});
	}

	nivelOcupacaoXOuMaior(number: number): boolean {
		const ret =
			this.formGroup.get("qtdeNivelOcupacao") &&
			this.formGroup.get("qtdeNivelOcupacao").value &&
			this.formGroup.get("qtdeNivelOcupacao").value !== 0 &&
			this.formGroup.get("qtdeNivelOcupacao").value >= number;
		if (ret) {
			this.tratarLabelNivelOcupacao(
				Number(this.formGroup.get("qtdeNivelOcupacao").value)
			);
		}
		return ret;
	}

	tratarLabelNivelOcupacao(nivel: number) {
		if (nivel === 1) {
			this.labelNivel1desconto = "De 0.00% à 100.00%";
		} else if (nivel === 2) {
			this.labelNivel1desconto = "De 0.00% à 50.00%";
			this.labelNivel2desconto = "De 50.01% à 100.00%";
		} else if (nivel === 3) {
			this.labelNivel1desconto = "De 0.00% à 33.33%";
			this.labelNivel2desconto = "De 33.34% à 66.67%";
			this.labelNivel3desconto = "De 66.68% à 100.00%";
		} else if (nivel === 4) {
			this.labelNivel1desconto = "De 0.00% à 25.00%";
			this.labelNivel2desconto = "De 25.01% à 50.00%";
			this.labelNivel3desconto = "De 50.01% à 75.00%";
			this.labelNivel4desconto = "De 75.01% à 100.00%";
		} else if (nivel === 5) {
			this.labelNivel1desconto = "De 0.00% à 20.00%";
			this.labelNivel2desconto = "De 20.01% à 40.00%";
			this.labelNivel3desconto = "De 40.01% à 60.00%";
			this.labelNivel4desconto = "De 60.01% à 80.00%";
			this.labelNivel5desconto = "De 80.01% à 100.00%";
		}
		this.maskOptionsInputPercent = {
			align: "left",
			allowNegative: false,
			allowZero: false,
			nullable: false,
			precision: 2,
			suffix: "",
			decimal: ",",
			thousands: ".",
			prefix: "",
			max: "100",
		};
	}

	private loadForm() {
		zip(
			this.getAmbientes(),
			this.getNiveisTurma(),
			this.getProfessor()
		).subscribe(() => {
			this.obterModalidades();
			this.configFilters();
		});

		this.formGroup.get("id").setValue(this.turmaEdit.id);
		this.formGroup.get("nome").setValue(this.turmaEdit.nome);
		this.formGroup.get("cor").setValue(this.turmaEdit.cor);
		const regex = new RegExp(this.turmaEdit.modalidade.id, "i");
		if (this.modalidades.find((item) => regex.test(item.id)) !== undefined) {
			this.formGroup.get("modalidade").setValue(this.turmaEdit.modalidade.id);
		}
		this.formGroup.get("idadeMinima").setValue(this.turmaEdit.idadeMinima);
		this.formGroup
			.get("idadeMinimaMeses")
			.setValue(this.turmaEdit.idadeMinimaMeses);
		this.formGroup.get("idadeMaxima").setValue(this.turmaEdit.idadeMaxima);
		this.formGroup
			.get("idadeMaximaMeses")
			.setValue(this.turmaEdit.idadeMaximaMeses);
		this.formGroup.get("identificador").setValue(this.turmaEdit.identificador);
		this.formGroup
			.get("dataInicial")
			.setValue(new Date(this.turmaEdit.dataInicial).getTime());
		this.formGroup
			.get("dataFinal")
			.setValue(new Date(this.turmaEdit.dataFinal).getTime());
		this.formGroup
			.get("minutosAntecedenciaDesmarcarAula")
			.setValue(this.turmaEdit.minutosAntecedenciaDesmarcarAula);
		this.formGroup
			.get("minutosAposInicioApp")
			.setValue(this.turmaEdit.minutosAposInicioApp);
		this.formGroup
			.get("tipoAntecedenciaMarcarAula")
			.setValue(this.turmaEdit.tipoAntecedenciaMarcarAula);
		this.formGroup
			.get("minutosAntecedenciaMarcarAula")
			.setValue(this.turmaEdit.minutosAntecedenciaMarcarAula);
		this.formGroup
			.get("qtdeNivelOcupacao")
			.setValue(this.turmaEdit.qtdeNivelOcupacao);
		this.formGroup
			.get("percDescOcupacaoNivel1")
			.setValue(this.turmaEdit.percDescOcupacaoNivel1);
		this.formGroup
			.get("percDescOcupacaoNivel2")
			.setValue(this.turmaEdit.percDescOcupacaoNivel2);
		this.formGroup
			.get("percDescOcupacaoNivel3")
			.setValue(this.turmaEdit.percDescOcupacaoNivel3);
		this.formGroup
			.get("percDescOcupacaoNivel4")
			.setValue(this.turmaEdit.percDescOcupacaoNivel4);
		this.formGroup
			.get("percDescOcupacaoNivel5")
			.setValue(this.turmaEdit.percDescOcupacaoNivel5);
		this.formGroup
			.get("bloquearMatriculasAcimaLimite")
			.setValue(this.turmaEdit.bloquearMatriculasAcimaLimite);
		this.formGroup
			.get("permitirDesmarcarReposicoes")
			.setValue(this.turmaEdit.permitirDesmarcarReposicoes);
		this.formGroup
			.get("permiteAlunoOutraEmpresa")
			.setValue(this.turmaEdit.permiteAlunoOutraEmpresa);
		this.formGroup.get("monitorada").setValue(this.turmaEdit.monitorada);
		this.formGroup
			.get("bloquearReposicaoAcimaLimite")
			.setValue(this.turmaEdit.bloquearReposicaoAcimaLimite);
		this.formGroup
			.get("permitirAulaExperimental")
			.setValue(this.turmaEdit.permitirAulaExperimental);
		this.formGroup
			.get("bloquearLotacaoFutura")
			.setValue(this.turmaEdit.bloquearLotacaoFutura);
		this.cd.detectChanges();
	}

	submitHandler() {
		this.markAsTouched();
		if (!this.entity) {
			this.editHandler();
		} else {
			this.createHandler();
		}
	}

	markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("cor").markAsTouched();
		this.formGroup.get("modalidade").markAsTouched();
		this.formGroup.get("idadeMinima").markAsTouched();
		this.formGroup.get("idadeMinimaMeses").markAsTouched();
		this.formGroup.get("idadeMaxima").markAsTouched();
		this.formGroup.get("idadeMaximaMeses").markAsTouched();
		this.formGroup.get("identificador").markAsTouched();
		this.formGroup.get("dataInicial").markAsTouched();
		this.formGroup.get("dataFinal").markAsTouched();
	}

	private createHandler() {
		if (this.formGroup.valid) {
			this.turmaEdit = this.formGroup.getRawValue();
			this.turmaEdit.modalidade = { id: this.turmaEdit.modalidade };
			this.turmaEdit.empresa = { codigo: Number(this.session.codigoEmpresa) };
			if (this.formGroupVideosUri.get("linkVideo").value != "") {
				this.linkVideos.push({
					linkVideo: this.formGroupVideosUri.get("linkVideo").value,
					professor: this.formGroupVideosUri.get("professor").value,
				});
			}
			this.turmaEdit.linkVideos = this.linkVideos;
			this.turmaService.criarTurma(this.turmaEdit).subscribe((result) => {
				if (result.retorno) {
					this.turmaEdit.id = result.retorno.id;
					this.notificationService.success(
						this.notificacoesTranslate.getLabel("createSuccess")
					);
					this.cancelHandler(false);
					this.agendaStateService.forceLoad$.next(true);
				} else {
					this.notificationService.error(
						this.notificacoesTranslate.getLabel(result.erro)
					);
				}
			});
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	private editHandler() {
		if (this.formGroup.valid) {
			this.turmaEdit = this.formGroup.getRawValue();
			this.turmaEdit.modalidade = { id: this.turmaEdit.modalidade };
			this.turmaEdit.empresa = { codigo: Number(this.session.codigoEmpresa) };
			if (this.formGroupVideosUri.get("linkVideo").value != "") {
				this.linkVideos.push({
					linkVideo: this.formGroupVideosUri.get("linkVideo").value,
					professor: this.formGroupVideosUri.get("professor").value,
				});
			}
			this.turmaEdit.linkVideos = this.linkVideos;
			this.turmaService.editarTurma(this.turmaEdit).subscribe((result) => {
				if (result.retorno) {
					this.notificationService.success(
						this.notificacoesTranslate.getLabel("editSuccess")
					);
					this.cancelHandler(false);
					this.agendaStateService.forceLoad$.next(true);
				} else {
					this.notificationService.error(result.erro);
				}
			});
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	cancelHandler(value: boolean) {
		if (value && this.formGroup.valid && this.turmaEdit) {
			this.turmaEditDps = this.formGroup.getRawValue();
			if (this.isEquivalent(this.turmaEdit, this.turmaEditDps)) {
				this.router.navigate(["agenda", "turma"]);
			} else {
				this.openModalConfirmTurmaEdit(
					"Alterações não salvas",
					ConfirmTurmaEditModalComponent
				);
			}
		} else {
			this.router.navigate(["agenda", "turma"]);
		}
	}

	isEquivalent(a, b) {
		const aProps = Object.getOwnPropertyNames(a);
		const bProps = Object.getOwnPropertyNames(b);
		if (aProps.length !== bProps.length + 1) {
			return false;
		}

		for (let i = 0; i < aProps.length; i++) {
			const propName = aProps[i];
			if (
				!(
					propName === "modalidade" ||
					propName === "integracaoSpiv" ||
					propName === "dataInicial" ||
					propName === "dataFinal"
				) &&
				a[propName] !== b[propName]
			) {
				return false;
			}
		}
		return true;
	}

	openModalConfirmTurmaEdit(title, modalComponent: any) {
		const ref = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.MEDIUM
		);
		ref.result.then(
			(value) => {
				if (value) {
					this.router.navigate(["agenda", "turma"]);
				}
			},
			() => {}
		);
	}

	verificaCamposValidos() {
		this.isFormGroupValid = this.formGroup.status === "VALID";
	}

	validarCamposInformacoes(): boolean {
		if (
			(this.formGroup.get("nome") &&
				(this.formGroup.get("nome").value === null ||
					this.formGroup.get("nome").value === "")) ||
			(this.formGroup.get("cor") &&
				(this.formGroup.get("cor").value === null ||
					this.formGroup.get("cor").value === "")) ||
			(this.formGroup.get("identificador") &&
				(this.formGroup.get("identificador").value === null ||
					this.formGroup.get("identificador").value === "")) ||
			(this.formGroup.get("modalidade") &&
				this.formGroup.get("modalidade").value == null)
		) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
			return false;
		}
		return true;
	}

	get acaoHabilitada() {
		return this.formGroupVideosUri.get("linkVideo").value;
	}

	adicionarHandler() {
		this.linkVideos.push({
			linkVideo: this.formGroupVideosUri.get("linkVideo").value,
			professor: this.formGroupVideosUri.get("professor").value,
		});
		this.formGroupVideosUri.get("linkVideo").setValue("");
		this.formGroupVideosUri.get("professor").setValue(false);
	}

	deletButon(index) {
		this.linkVideos.splice(index, 1);
	}

	checkCheckBoxProfessor(objeto) {
		if (objeto.professor) {
			objeto.professor = false;
		} else {
			objeto.professor = true;
		}
	}

	sincronizarTurmaMgb() {
		const ref = this.modalService.open(
			"Atenção",
			ModalConfirmSincronizarMgbComponent,
			PactoModalSize.LARGE,
			"modal-confirm-sincronizar-turma-mgb"
		);
		ref.result.then(
			(value) => {
				if (value) {
					this.turmaService.syncTurmaMgb(this.turmaEdit.id).subscribe({
						error: (error) => {
							if (
								error.error.meta.message.includes(
									"ERRO_HORARIO_COM_AMBIENTE_INCORRETO"
								)
							) {
								this.snotifyService.error(
									"Um dos horários está com ambiente incorreto, " +
										"para sincronização o ambiente deve estar vinculado a uma piscina MGB."
								);
							} else if (
								error.error.meta.message.includes(
									"ERRO_HORARIO_COM_NIVEL_INCORRETO"
								)
							) {
								this.snotifyService.error(
									"Um dos horários está com o nível incorreto, " +
										"para sincronização o nível do horário deve estar vinculado a um nível do MGB."
								);
							} else if (
								error.error.meta.message.includes(
									"ERRO_PROFESSOR_NAO_LOCALIZADO_SINCRONIZADO"
								)
							) {
								this.snotifyService.error(
									"Um dos professores não foi localizado " +
										"ou sincronizado corretamente com o MGB, tente novamente."
								);
							} else if (
								error.error.meta.message.includes(
									"ERRO_SINCRONIZAR_PROFESSOR_POST_MGB"
								)
							) {
								const mensagem = error.error.meta.message.split("NOME:");
								const nome = mensagem[1];
								this.snotifyService.error(
									"Falha na Sincronização MGB do professor " + nome
								);
							} else if (
								error.error.meta.message.includes("ERRO_PROFESSOR_SEM_EMAIL")
							) {
								const mensagem = error.error.meta.message.split("NOME:");
								const nome = mensagem[1];
								this.snotifyService.error(
									"O professor " +
										nome +
										"precisa ter um email válido para ser sincronizado no MGB."
								);
							} else if (
								error.error.meta.message.includes(
									"ERRO_VERIFICAR_TURMA_COM_PUBLICID"
								)
							) {
								this.snotifyService.error(
									"Ocorreu um erro ao tentar verificar se já existe uma " +
										"turma mgb com o publicId"
								);
							} else {
								this.snotifyService.error(error.error.meta.message);
							}
						},
						next: (dados) => {
							this.snotifyService.warning(
								"A sincronização foi iniciada e pode levar alguns minutos. Acompanhe as turmas sincronizadas na plataforma do MGB."
							);
						},
					});
				}
			},
			() => {}
		);
	}

	private gerarMensagemAlertaSincronizacao(
		total: number,
		totalSucesso: number,
		totalErro: number
	): string {
		const inicio = `${total > 1 ? "dos" : "de"} ${total} ${
			total > 1 ? "horários" : "horário"
		}`;
		const meio = `apenas ${totalSucesso} ${
			totalSucesso > 1 ? "foram" : "foi"
		} sincronizado${totalSucesso > 1 ? "s" : ""}`;
		const fim = `e ${totalErro} não atend${
			totalErro > 1 ? "em" : "e"
		} às condições`;
		return `Atenção, ${inicio}, ${meio} ${fim}.`;
	}

	private isMgbAtivo() {
		this.mgb = this.configCache.configuracoesIntegracoesListaMGB;
		const find = this.mgb.find(
			(x) => x.empresa === Number(this.session.empresaId)
		);
		const tokenMgb = find ? find.token : null;
		this.showBtnSyncTurmaMgb = tokenMgb !== null && tokenMgb.length > 0;
		this.cd.detectChanges();
	}
}
