import {
	Component,
	OnInit,
	Input,
	ViewChild,
	TemplateRef,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "ccl-confirm-modal",
	templateUrl: "./confirm-turma-edit-modal.component.html",
	styleUrls: ["./confirm-turma-edit-modal.component.scss"],
})
export class ConfirmTurmaEditModalComponent implements OnInit {
	@Input() title: string;
	@Input() body: string;
	@Input() bodyRef: TemplateRef<any>;
	@ViewChild("actionDefault", { static: true }) actionDefault;
	@Input() actionLabel;
	@Input() cancelLabel;
	@Input() showCancelButton;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {
		if (!this.actionLabel) {
			this.actionLabel = this.actionDefault.nativeElement.innerHTML;
		}
		if (!this.cancelLabel) {
			this.cancelLabel = "Cancelar";
		}
		if (this.showCancelButton === null || this.showCancelButton === undefined) {
			this.showCancelButton = true;
		}
	}

	dismiss() {
		this.openModal.dismiss(false);
	}

	close() {
		this.openModal.close(true);
	}
}
