@import "src/assets/scss/pacto/plataforma-import.scss";

.sort-icon {
	padding-left: 10px;
}

.iconFeedBackText {
	margin-right: 5px;
}

.feedBackText {
	margin-top: 20px;
	margin-left: 30px;
}

.margin-top-20 {
	margin-top: 20px;
}

.feedback {
	cursor: pointer;
	font-weight: 600;
	padding: 8px 13px;
	border-radius: 4px;
	text-align: right;

	a {
		color: #1e60fa;
	}
}

.actions-row {
	width: 100px;
	text-align: center;

	i {
		cursor: pointer;
		padding-right: 10px;
	}
}

.order {
	cursor: pointer;

	.button-direction {
		display: flex;

		.title-table {
			bottom: 0px;
			left: 0px;
		}

		.icon-order {
			.fa-caret-up {
				display: block;
				height: 10px;
			}

			.fa-caret-down {
				display: block;
				height: 10px;
			}
		}
	}
}

.fa-exclamation-triangle {
	color: $pequizaoPri;
}

.loading-state,
.empty-state {
	text-align: center;
	padding-top: 60px;
	height: 150px;
}

.logs-gympass {
	margin-top: -80px;
	padding: 0 30px;
	height: 50px;
	display: block;

	pacto-cat-button {
		margin-right: 10px;
	}
}

.preview-object {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	border: 1px solid #c7c7c7;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}

::ng-deep pacto-relatorio {
	.pacto-table-title-block {
		.search {
			input {
				width: 408px;
				padding-left: 50px;
				color: #c7c9cc;
			}
		}
	}
}
