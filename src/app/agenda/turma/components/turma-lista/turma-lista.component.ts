import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { FormControl, FormGroup } from "@angular/forms";

import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	TreinoApiAulaService,
	AulaBase,
	Ambiente,
	Modalidade,
	TreinoApiColaboradorService,
	UsuarioBase,
	TreinoApiAmbienteService,
	TreinoApiModalidadeService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	GridFilterType,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { DatePipe } from "@angular/common";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";

@Component({
	selector: "pacto-turma-lista",
	templateUrl: "./turma-lista.component.html",
	styleUrls: ["./turma-lista.component.scss"],
})
export class TurmaListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("buttonName", { static: true }) buttonName;

	@ViewChild("empresaColumnName", { static: true }) empresaColumnName;
	@ViewChild("modalidadeColumnName", { static: true }) modalidadeColumnName;
	@ViewChild("identificadorColumnName", { static: true })
	identificadorColumnName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("codigoColumnName", { static: true }) codigoColumnName;
	@ViewChild("idadeMinimaColumnName", { static: true }) idadeMinimaColumnName;
	@ViewChild("idadeMaximaColumnName", { static: true }) idadeMaximaColumnName;
	@ViewChild("integracaoSpivColumnName", { static: true })
	integracaoSpivColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("identificadorCelula", { static: true }) identificadorCelula;
	@ViewChild("codigoCelula", { static: true }) codigoCelula;
	@ViewChild("nomeCelula", { static: true }) nomeCelula;
	@ViewChild("modalidadeCelula", { static: true }) modalidadeCelula;
	@ViewChild("empresaCelula", { static: true }) empresaCelula;
	@ViewChild("idadeMinimaCelula", { static: true }) idadeMinimaCelula;
	@ViewChild("idadeMaximaCelula", { static: true }) idadeMaximaCelula;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("integracaoSpivCelula", { static: true }) integracaoSpivCelula;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;

	data: ApiResponseList<AulaBase> = {
		content: [],
	};

	itemToRemove;
	loading = false;
	nomeAula = "";
	ready = false;

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});
	professores: ApiResponseList<UsuarioBase> = {
		content: [],
	};
	ambientes: ApiResponseList<Ambiente> = {
		content: [],
	};
	modalidades: ApiResponseList<Modalidade> = {
		content: [],
	};

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private ambienteService: TreinoApiAmbienteService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private configCache: TreinoConfigCacheService,
		private modalidadeService: TreinoApiModalidadeService,
		private turmaService: TreinoApiAulaService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService
	) {}

	table: PactoDataGridConfig;
	filterConfig: any;
	integracaoZW = false;
	pactobr = false;
	integradoBookingGympass: any;

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.pactobr =
			this.sessionService.loggedUser.username.toLowerCase() === "pactobr";
		this.integradoBookingGympass =
			this.configCache.configuracoesIntegracoes.usar_gympass_booking;
		zip(
			this.getAmbientes(),
			this.getProfessor(),
			this.getModalidade()
		).subscribe(() => {
			this.ready = true;
			this.configTable();
			this.configFilters();
			this.cd.detectChanges();
		});

		this.sessionService.notificarRecursoEmpresa(RecursoSistema.NOVA_TELA_TURMA);
	}

	btnClickHandler() {
		this.router.navigate(["agenda", "turma", "adicionar"]);
	}

	btnEditHandler(item) {
		this.router.navigate(["agenda", "turma", item.id]);
	}

	private configTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("turmas"),
			logUrl: this.rest.buildFullUrl("log/turma"),
			quickSearch: true,
			columns: [
				{
					nome: "id",
					titulo: this.codigoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "id",
					celula: this.codigoCelula,
				},
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
					celula: this.nomeCelula,
				},
				{
					nome: "identificador",
					titulo: this.identificadorColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "identificador",
					celula: this.identificadorCelula,
				},
				{
					nome: "modalidade.nome",
					titulo: this.modalidadeColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "modalidade",
					celula: this.modalidadeCelula,
				},
				{
					nome: "empresa.nome",
					titulo: this.empresaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "empresa",
					celula: this.empresaCelula,
				},
				{
					nome: "idadeMinima",
					titulo: this.idadeMinimaColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "idadeMinima",
					celula: this.idadeMinimaCelula,
				},
				{
					nome: "idadeMaxima",
					titulo: this.idadeMaximaColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "idadeMaxima",
					celula: this.idadeMaximaCelula,
				},
				{
					nome: "integracaospiv",
					titulo: this.integracaoSpivColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "integracaospiv",
					celula: this.integracaoSpivCelula,
				},
			],
			buttons: {
				conteudo: this.buttonName,
				nome: "add",
				id: "btn-nova-aula",
			},
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipInativar,
					showIconFn: (row) => this.isTurmaVigente(row),
				},
			],
		});
	}

	isTurmaVigente(turma) {
		if (turma.dataFinal) {
			return new Date(turma.dataFinal).getTime() > new Date().getTime();
		}
		return false;
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "vigencia",
					label: "Vigência",
					type: GridFilterType.MANY,
					options: [
						{ value: "VIGENTE", label: "Vigente" },
						{ value: "NAO_VIGENTE", label: "Não vigente" },
					],
					initialValue: ["VIGENTE"],
				},
				{
					name: "modalidadeIds",
					label: this.modalidadeLabel,
					type: GridFilterType.MANY,
					options: this.modalidades.content,
				},
			],
		};
	}

	private getProfessor(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradores(true)
			.pipe(
				map((dados) => {
					dados.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = dados;
					return true;
				})
			);
		return professores$;
	}

	private getAmbientes(): Observable<any> {
		const ambientes$ = this.ambienteService.obterAmbientes().pipe(
			map((dados) => {
				dados.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientes = dados;
				return true;
			})
		);
		return ambientes$;
	}

	private getModalidade(): Observable<any> {
		return this.modalidadeService.obterTodasModalidadesTurma().pipe(
			map((dados) => {
				dados.content.forEach((modalidade: any) => {
					modalidade.value = modalidade.id;
					modalidade.label = modalidade.nome;
				});
				this.modalidades = dados;
				return true;
			})
		);
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.sessionService.chave,
					this.sessionService.loggedUser.usuarioZw,
					this.sessionService.empresaId,
					"Turma",
					"5.12 - Turmas",
					"PERMISSAOTOTAL"
				)
				.subscribe(
					() => {
						this.removeHandler($event.row);
					},
					() => {
						this.snotifyService.warning(
							this.notificacoesTranslate.getLabel("semPermissao")
						);
					},
					() => this.cd.detectChanges()
				);
		}
	}

	removeHandler(item) {
		this.nomeAula = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					const idToRemove = this.data.content.findIndex(
						(itemI) => itemI.id === item.id
					);
					this.turmaService.removerAula(item.id).subscribe((resultRemove) => {
						if (resultRemove === "aulaTemAlunoNaoExcluir") {
							this.snotifyService.error(removeError);
						} else {
							this.snotifyService.success(removeSuccess);
							this.tableData.reloadData();
						}
					});
				})
				.catch(() => {});
		});
	}

	empresaId(): string {
		if (this.sessionService && this.sessionService.empresaId) {
			return this.sessionService.empresaId.toString();
		}
		return "";
	}

	voltarAgenda() {
		this.router.navigate(["agenda"]);
	}

	get urlLog(): string {
		return this.rest.buildFullUrl("log");
	}

	usuarioPactoBrUriTurma(): string {
		if (this.sessionService && this.sessionService.isUsuarioPacto) {
			return "uriTurma";
		} else {
			return null;
		}
	}
}
