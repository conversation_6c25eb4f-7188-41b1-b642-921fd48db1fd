<!-- <div class="row">
	<div class="col-md-10 feedBackText">
		<i class="pct pct-alert-triangle iconFeedBackText"></i>
		<span>
			Você está visualizando a nova tela de cadastro de turma, e seu feedback
			será muito importante para construção de melhorias nela.
		</span>
	</div>
	<div class="feedback margin-top-20">
		<a href="https://forms.gle/cFzabaJEiLrQ7UUA9" target="_blank">
			Enviar feedback
		</a>
	</div>
</div> -->
<adm-layout
	(goBack)="voltarAgenda()"
	[empresaId]="empresaId()"
	[showBtnPlanoAntigo]="false"
	appStickyFooter
	i18n-modulo="@@turma:modulo"
	i18n-pageTitle="@@turma:title"
	modulo="Agenda"
	pageTitle="Turma">
	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="filterConfig"
			[quickSearchPlaceHolderCustom]="'Busque pelo nome da turma'"
			[sessionService]="sessionService"
			[showBtnAdd]="false"
			[showShare]="true"
			[table]="table"
			actionTitulo="Ações"
			telaId="turmaCadastrada"></pacto-relatorio>
	</div>
</adm-layout>
<!--table columns-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Cadastrar nova turma</span>
</ng-template>
<ng-template #codigoColumnName>
	<span i18n="@@crud-aulas:input:codigo:label">Código</span>
</ng-template>
<ng-template #nomeColumnName>
	<span i18n="@@crud-aulas:input:nome:label">Nome</span>
</ng-template>
<ng-template #identificadorColumnName>
	<span i18n="@@crud-turmas:select:identificador:label">Identificador</span>
</ng-template>
<ng-template #modalidadeColumnName>
	<span i18n="@@crud-turmas:table:modalidade:title">Modalidade</span>
</ng-template>
<ng-template #empresaColumnName>
	<span i18n="@@crud-turmas:table:empresa:title">Empresa</span>
</ng-template>
<ng-template #idadeMinimaColumnName>
	<span i18n="@@crud-turmas:input:idadeMinima:label">Idade Mínima</span>
</ng-template>
<ng-template #idadeMaximaColumnName>
	<span i18n="@@crud-turmas:input:idadeMaxima:label">Idade Máxima</span>
</ng-template>
<ng-template #integracaoSpivColumnName>
	<span i18n="@@crud-turmas:input:integracaoSpiv:label">
		Integração com SPIV
	</span>
</ng-template>

<!--label filter-->
<ng-template #modalidadeLabel>
	<span i18n="@@crud-aulas:select:modalidade:label">Modalidade</span>
</ng-template>

<!--Celulas para formatação-->
<ng-template #codigoCelula let-item="item">
	{{ item.id }}
</ng-template>
<ng-template #nomeCelula let-item="item">
	{{ item.nome }}
</ng-template>
<ng-template #identificadorCelula let-item="item">
	{{ item.identificador }}
</ng-template>
<ng-template #modalidadeCelula let-item="item">
	{{ item.modalidade.nome }}
</ng-template>
<ng-template #empresaCelula let-item="item">
	{{ item.empresa.nome }}
</ng-template>
<ng-template #idadeMinimaCelula let-item="item">
	{{ item.idadeMinima }}
</ng-template>
<ng-template #idadeMaximaCelula let-item="item">
	{{ item.idadeMaxima }}
</ng-template>
<ng-template #integracaoSpivCelula let-item="item">
	{{ item.integracaospiv ? "SIM" : "NÃO" }}
</ng-template>
<!--fim-->

<!--tooltip icons-->
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipEditar [hidden]="true" i18n="@@crud-alunos:inativar:tooltip-icon">
	Editar
</span>
<!-- FILTER LABELS -->
<ng-template
	#dataInicioLabel
	i18n="@@relatorio-atividade-professores:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template
	#dataFimLabel
	i18n="@@relatorio-atividade-professores:data-fim:filtro">
	Data Fim
</ng-template>

<span #removeModalTitle [hidden]="true" i18n="@@crud-aulas:remove-modal:title">
	Inativar Turma ?
</span>
<span #removeModalBody [hidden]="true" i18n="@@crud-aulas:remove-modal:body">
	Deseja inativar a turma {{ nomeAula }}?
</span>
<span #removeSuccess [hidden]="true" i18n="@@crud-aulas:remove:success">
	Turma inativada com sucesso.
</span>
<span #removeError [hidden]="true" i18n="@@crud-aulas:remove:error">
	Não foi possível excluir, existem alunos matriculados
</span>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="semPermissao">
		Seu usuário não possui permissão, procure seu administrador.
	</span>
</pacto-traducoes-xingling>
