import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { TreinoApiAulaService } from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

import { AmbienteEditModalModule } from "../ambiente/components/ambiente-edit-modal/ambiente-edit-modal.module";
import { ModalidadeEditModalModule } from "../modalidade/components/modalidade-edit-modal/modalidade-edit-modal.module";
import { TurmaListaComponent } from "./components/turma-lista/turma-lista.component";
import { TurmaEditComponent } from "./components/turma-edit/turma-edit.component";
import { TurmaHorarioModalComponent } from "./components/turma-edit/horario-edit-modal/turma-horario-edit-modal.component";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { NivelTurmaHorarioModalComponent } from "./components/turma-edit/horario-edit-modal/cad-nivelTurma-horario-modal/cad-nivelTurma-horario-modal.component";
import { ConfirmTurmaEditModalComponent } from "./components/turma-edit/modal-confirm-turma-edit/confirm-turma-edit-modal.component";
import { ConfirmEditHorarioTurmaModalComponent } from "./components/turma-edit/modal-confirm-edit-horario-turma/confirm-edit-horario-turma-modal.component";
import { AmbienteHorarioModalModule } from "../ambiente/components/cad-ambiente-horario-modal/cad-ambiente-horario-module";
import { LayoutModule } from "../../../../projects/adm/src/app/layout/layout.module";

import { PerfilAcessoRecurso } from "sdk";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "../../../../projects/adm/src/app/perfil-acesso/perfil-acesso-recurso.model";
import { ModalConfirmSincronizarMgbComponent } from "./components/turma-edit/modal-confirm-sincronizar-mgb/modal-confirm-sincronizar-mgb.component";

const routes: Routes = [
	{
		path: "",
		component: TurmaListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.TURMA, [
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: "adicionar",
		component: TurmaEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.TURMA, [
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
	{
		path: ":id",
		component: TurmaEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: new PerfilAcessoRecurso(PerfilAcessoRecursoNome.TURMA, [
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]),
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		AmbienteEditModalModule,
		ModalidadeEditModalModule,
		MatSlideToggleModule,
		AmbienteHorarioModalModule,
		LayoutModule,
	],
	declarations: [
		TurmaListaComponent,
		TurmaEditComponent,
		TurmaHorarioModalComponent,
		NivelTurmaHorarioModalComponent,
		ConfirmTurmaEditModalComponent,
		ConfirmEditHorarioTurmaModalComponent,
		ModalConfirmSincronizarMgbComponent,
	],
	entryComponents: [
		TurmaHorarioModalComponent,
		NivelTurmaHorarioModalComponent,
		ConfirmTurmaEditModalComponent,
		ModalConfirmSincronizarMgbComponent,
		ConfirmEditHorarioTurmaModalComponent,
	],
	providers: [TreinoApiAulaService],
})
export class TurmaModule {}
