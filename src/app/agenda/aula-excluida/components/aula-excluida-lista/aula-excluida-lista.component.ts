import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService } from "@base-core/modal/modal.service";
import { SnotifyService } from "ng-snotify";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { AulaExcluida } from "@base-core/aula-excluida/aula-excluida.model";
import { AulaExcluidaService } from "@base-core/aula-excluida/aula-excluida.service";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { TreinoApiColaboradorService } from "treino-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-aula-excluida-lista",
	templateUrl: "./aula-excluida-lista.component.html",
	styleUrls: ["./aula-excluida-lista.component.scss"],
})
export class AulaExcluidaListaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeErro", { static: true }) removeErro;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;

	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("professorColumnName", { static: true }) professorColumnName;
	@ViewChild("responsavelColumnName", { static: true }) responsavelColumnName;
	@ViewChild("dataAulaColumnName", { static: true }) dataAulaColumnName;
	@ViewChild("dataExclusaoColumnName", { static: true }) dataExclusaoColumnName;
	@ViewChild("professorCelula", { static: true }) professorCelula;
	@ViewChild("dataAulaCelula", { static: true }) dataAulaCelula;
	@ViewChild("dataExclusaoCelula", { static: true }) dataExclusaoCelula;
	@ViewChild("professorLabel", { static: true }) professorLabel;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	constructor(
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private modal: NgbModal,
		private rest: RestService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private aulaExcluidaService: AulaExcluidaService,
		private colaboradorService: TreinoApiColaboradorService
	) {}

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;

	private professores: Array<any>;

	data: ApiResponseList<AulaExcluida> = {
		content: [],
	};

	incluirProfessoresInativos = false;
	ready = false;
	itemToRemove;
	nomeAula = "";

	ngOnInit() {
		this.ready = true;
		this.configTable();
		this.fetchFilterData().subscribe(() => {
			this.configFilters();
		});
		this.cd.detectChanges();
	}

	removeHandler(item) {
		this.nomeAula = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeErro = this.removeErro.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					const idToRemove = this.data.content.findIndex(
						(itemI) => itemI.codigo === item.codigo
					);
					this.aulaExcluidaService
						.removerAmbiente(item.codigo)
						.subscribe((result) => {
							this.snotifyService.success(removeSuccess);
							this.data.content.splice(idToRemove, 1);
							this.fetchData();
						});
				})
				.catch(() => {});
		});
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("aula-excluida"),
			logUrl: this.rest.buildFullUrl("log/listar-log-exportacao/aulaexcluida"),
			quickSearch: false,
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "professor.nome",
					titulo: this.professorColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "professor",
					celula: this.professorCelula,
				},
				{
					nome: "nomeUsuario",
					titulo: this.responsavelColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "nomeUsuario",
				},
				{
					nome: "dataAulaDia",
					titulo: this.dataAulaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "dataAulaDia",
					celula: this.dataAulaCelula,
				},
				{
					nome: "dataExclusao",
					titulo: this.dataExclusaoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "dataExclusao",
					celula: this.dataExclusaoCelula,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
				},
			],
		});
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradores(this.incluirProfessoresInativos)
			.pipe(
				map((data) => {
					data.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = data.content;
					return true;
				})
			);
		return professores$;
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dataInicio",
					label: this.dataInicioLabel,
					type: GridFilterType.DATE_POINT,
				},
				{
					name: "dataFim",
					label: this.dataFimLabel,
					type: GridFilterType.DATE_POINT,
				},
				{
					name: "professoresIds",
					label: this.professorLabel,
					type: GridFilterType.MANY,
					options: this.professores,
				},
			],
		};
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}
}
