<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'RELATORIOS',
			menu: 'AULASEXCLUIDAS'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(iconClick)="actionClickHandler($event)"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="aulaexcluida"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<!--title table-->
<ng-template #titulo>
	<span i18n="@@relatorio-aula-excluida:title">Aulas Excluídas</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@relatorio-aula-excluida:description">
		<PERSON><PERSON><PERSON>ie as aulas excluídas.
	</span>
</ng-template>
<!--End title table-->
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@relatorio-aula-excluida:table:nomeAula:title">Aula</span>
</ng-template>
<ng-template #professorColumnName>
	<span i18n="@@relatorio-aula-excluida:table:professor:title">Professor</span>
</ng-template>
<ng-template #responsavelColumnName>
	<span i18n="@@relatorio-aula-excluida:table:responsavel:title">
		Responsável Ação
	</span>
</ng-template>
<ng-template #dataAulaColumnName>
	<span i18n="@@relatorio-aula-excluida:table:dataAula:title">Data Aula</span>
</ng-template>
<ng-template #dataExclusaoColumnName>
	<span i18n="@@relatorio-aula-excluida:table:dataExclusao:title">
		Data Exclusão
	</span>
</ng-template>

<!--Celulas para formatação-->
<ng-template #professorCelula let-item="item">
	{{ item.professor?.nome }}
</ng-template>
<ng-template #dataAulaCelula let-item="item">
	{{ item.dataAulaDia | date : "dd/MM/yyyy" : "UTC" }}
</ng-template>
<ng-template #dataExclusaoCelula let-item="item">
	{{ item.dataExclusao | date : "dd/MM/yyyy HH:mm" }}
</ng-template>
<!--fim-->

<ng-template
	#dataInicioLabel
	i18n="@@relatorio-aula-excluida:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template #dataFimLabel i18n="@@relatorio-aula-excluida:data-fim:filtro">
	Data Fim
</ng-template>
<ng-template
	#professorLabel
	i18n="@@@@relatorio-aula-excluida:professor-label:filtro">
	Professor
</ng-template>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@relatorio-aula-excluida:remover:tooltip-icon">
	Desfazer
</span>

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@relatorio-aula-excluida:remove-modal:title">
	Desfazer Exclusão ?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@relatorio-aula-excluida:remove-modal:body">
	Deseja desfazer a exclusão da aula {{ nomeAula }}?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@relatorio-aula-excluida:remove:success">
	Aula reinserida com sucesso.
</span>
<span #removeErro [hidden]="true" i18n="@@relatorio-aula-excluida:remove:error">
	Não foi possível desfazer a exclusão
</span>
