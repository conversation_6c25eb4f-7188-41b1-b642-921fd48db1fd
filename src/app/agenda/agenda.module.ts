import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { IndicadoresAgendaComponent } from "../../app/treino/treino-gestao/indicadores-agenda/indicadores-agenda.component";

@NgModule({
	declarations: [
		//   IndicadoresAgendaComponent
	],
	exports: [],
	imports: [
		CommonModule,
		RouterModule.forChild([]),
		BaseSharedModule,

		//   IndicadoresAgendaComponent
	],
	entryComponents: [],
	// bootstrap: [IndicadoresAgendaComponent]
})
export class AgendaModule {}
