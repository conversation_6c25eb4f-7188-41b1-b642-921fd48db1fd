import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { GraduacaoModule } from "../graduacao/graduacao.module";
import { IndicadoresAgendaComponent } from "../treino/treino-gestao/indicadores-agenda/indicadores-agenda.component";
import { AlunosFaltososComponent } from "./alunos-faltosos/components/alunos-faltosos/alunos-faltosos.component";

export const routes: Routes = [
	{
		path: "tipo-agendamento",
		loadChildren: () =>
			import("./tipo-agendamento/tipo-agendamento.module").then(
				(m) => m.TipoAgendamentoModule
			),
	},
	{
		path: "locacao",
		loadChildren: () =>
			import("./locacao/locacao.module").then((m) => m.LocacaoModule),
	},
	{
		path: "ambiente",
		loadChildren: () =>
			import("./ambiente/ambiente.module").then((m) => m.AmbienteModule),
	},
	{
		path: "aula-excluida",
		loadChildren: () =>
			import("./aula-excluida/aula-excluida.module").then(
				(m) => m.AulaExcluidaModule
			),
	},
	{
		path: "alunos-faltosos",
		loadChildren: () =>
			import("./alunos-faltosos/alunos-faltosos.module").then(
				(m) => m.AlunosFaltososModule
			),
	},
	{
		path: "professores-substituidos",
		loadChildren: () =>
			import("./professores-substituidos/professores-substituidos.module").then(
				(m) => m.ProfessoresSubstituidosModule
			),
	},
	{
		path: "modalidade",
		loadChildren: () =>
			import("./modalidade/modalidade.module").then((m) => m.ModalidadeModule),
	},
	{
		path: "aula",
		loadChildren: () => import("./aula/aula.module").then((m) => m.AulaModule),
	},
	{
		path: "turma",
		loadChildren: () =>
			import("./turma/turma.module").then((m) => m.TurmaModule),
	},
	{
		path: "disponibilidade",
		loadChildren: () =>
			import("./disponibilidade/disponibilidade.module").then(
				(m) => m.DisponibilidadeModule
			),
	},
	{
		path: "painel",
		loadChildren: () =>
			import("./agenda/agenda.module").then((m) => m.AgendaModule),
	},
	{
		path: "indicadores-agenda",
		loadChildren: () =>
			import(
				"../treino/treino-gestao/indicadores-agenda/indicador.agenda.module"
			).then((m) => m.IndicadoresAgendaModule),
	},
	{ path: "", redirectTo: "painel/home" },
];

@NgModule({
	imports: [GraduacaoModule, BaseSharedModule, RouterModule.forChild(routes)],
	exports: [BaseSharedModule],
})
export class AgendaRoutingModule {}
