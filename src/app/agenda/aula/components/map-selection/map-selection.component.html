<div class="row row-select-aparelho">
	<div class="col-md-3">
		<pacto-select
			[id]="'aparelho-reserva-equipamento-select'"
			[nome]="'Selecionar equipamento'"
			[opcoes]="aparelhos"
			[control]="formGroup.get('aparelhoReservaEquipamento')"
			(change)="changeSelectAparelhoReservaEquipamento()"
			label="Selecionar equipamento"></pacto-select>
	</div>
</div>

<div class="map-selection-container">
	<div class="title-map">
		<span>{{ title }}</span>
	</div>

	<div *ngFor="let row of maps; let rowIndex = index" class="row">
		<button
			(click)="toggleMap(rowIndex, mapIndex)"
			*ngFor="let map of row; let mapIndex = index"
			[ngClass]="{ selected: map.value === 1 }"
			class="map"
			tabindex="-1"
			[ngbTooltip]="getTooltip(map)">
			<div *ngIf="map.value === 1">
				<div>
					<span>{{ getPos(rowIndex, mapIndex, map) }}</span>
				</div>
				<div>
					<span>{{ map.siglaAparelho }}</span>
				</div>
			</div>

			<span *ngIf="map.value === 0" class="unselected">
				{{ getSiglaPos(rowIndex, mapIndex, map) }}
			</span>
		</button>
	</div>

	<div class="desmarcar-container">
		<span (click)="uncheckAllPositionsMap()">Desmarcar todos</span>
	</div>
</div>
