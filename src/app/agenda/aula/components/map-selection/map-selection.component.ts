import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { <PERSON><PERSON><PERSON><PERSON>, MapaEquipamentoAparelho, ModelMap } from "treino-api";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";

@Component({
	selector: "map-selection",
	templateUrl: "./map-selection.component.html",
	styleUrls: ["./map-selection.component.scss"],
})
export class MapSelectionComponent implements OnInit {
	@Input() totalRows: number = 8;
	@Input() mapsPerRow: number = 20;
	@Input() limiteEquipamentos: number = 0;
	@Input() listEquipamentosSelecionados: string = "";
	@Input() aparelhos: Array<Aparelho> = [];
	@Input() listaMapaEquipamentoAparelho: Array<MapaEquipamentoAparelho> = [];
	@Input() title: string = "Selecionar";
	@Input() showBtnUncheckAll: boolean = false;
	@Output() selectedMapsChange = new EventEmitter<any>();

	maps: ModelMap[][] = [];

	mapFull: Array<any> = this.gerarEstrutura();
	formGroup: FormGroup = new FormGroup({
		aparelhoReservaEquipamento: new FormControl(null),
	});
	// configuração de utilizar número sequencial como identificador do aparelho
	utilizarNumeracaoSequencial: boolean = false;
	listaMapaAuxiliar: Array<any> = [];

	gerarEstrutura() {
		const alphabetData: Array<Array<{ id: string; nome: string }>> = [];
		const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		for (const letter of alphabet) {
			const group: Array<{ id: string; nome: string }> = [];
			for (let i = 1; i <= this.mapsPerRow; i++) {
				const entry = { id: `${letter}-${i}`, nome: `${letter}-${i}` };
				group.push(entry);
			}
			alphabetData.push(group);
		}
		return alphabetData;
	}

	constructor(
		private snotifyService: SnotifyService,
		private treinoConfigService: TreinoConfigCacheService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initializeMaps();
		this.initializeConfigs();
		this.initializeMapAux();
	}

	initializeMapAux() {
		const mapaListaSelecionados = this.listEquipamentosSelecionados.split(";");
		for (const item of mapaListaSelecionados) {
			this.listaMapaAuxiliar.push(item.split("&")[0]);
		}
	}

	initializeConfigs() {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesAula &&
			this.treinoConfigService.configuracoesAula
				.utilizar_numeracao_sequencial_identificador_equipamento
		) {
			this.utilizarNumeracaoSequencial = true;
		} else {
			this.utilizarNumeracaoSequencial = false;
		}
		this.cd.detectChanges();
	}

	initializeMaps() {
		this.maps = [];
		if (!this.listEquipamentosSelecionados) {
			this.listEquipamentosSelecionados = "";
		}
		const mapaListaSelecionados = this.listEquipamentosSelecionados.split(";");
		for (let i = 0; i < this.totalRows; i++) {
			const row = [];
			for (let j = 0; j < this.mapsPerRow; j++) {
				if (mapaListaSelecionados.includes(this.mapFull[i][j].nome)) {
					const obj = this.getEquipamentoAparelhoByPos(this.mapFull[i][j].nome);
					row.push(this.setItemMapa(1, obj));
				} else {
					row.push(this.setItemMapa(0, null));
				}
			}
			this.maps.push(row);
		}
		this.emitSelectedMaps();
	}

	setItemMapa(value: number, obj: MapaEquipamentoAparelho): ModelMap {
		const itemMapa: ModelMap = {
			value: value === 0 ? 0 : 1,
			aparelhoId: obj && obj.codigoAparelho ? obj.codigoAparelho : null,
			siglaAparelho: obj && obj.siglaAparelho ? obj.siglaAparelho : "",
			nomeAparelho: obj && obj.nomeAparelho ? obj.nomeAparelho : "",
			posicaoMapa: obj && obj.posicaoMapa ? obj.posicaoMapa : "",
		};
		return itemMapa;
	}

	getEquipamentoAparelhoByPos(value): MapaEquipamentoAparelho {
		const obj: MapaEquipamentoAparelho = this.listaMapaEquipamentoAparelho.find(
			(item) => item.posicaoMapa === value
		);
		return obj;
	}

	toggleMap(row: number, map: number) {
		if (this.maps[row][map].value === 0) {
			this.maps[row][map].value = 1;
			this.maps[row][map].posicaoMapa = this.mapFull[row][map].nome;
			const idAparelho = this.formGroup.get("aparelhoReservaEquipamento").value;
			if (this.numberIsNotEmpty(idAparelho)) {
				const aparelho: Aparelho = this.aparelhos.find(
					(item) => Number(item.id) === Number(idAparelho)
				);
				this.maps[row][map].aparelhoId = Number(aparelho.id);
				this.maps[row][map].siglaAparelho = aparelho.sigla;
				this.maps[row][map].nomeAparelho = aparelho.nome;
			}
		} else {
			this.maps[row][map].value = 0;
			this.maps[row][map].aparelhoId = null;
			this.maps[row][map].siglaAparelho = "";
			this.maps[row][map].nomeAparelho = "";
			this.maps[row][map].posicaoMapa = "";
		}

		this.listaMapaAuxiliar = [];
		for (const mapRow of this.maps) {
			for (const mapItem of mapRow) {
				if (mapItem.value === 1) {
					this.listaMapaAuxiliar.push(mapItem.posicaoMapa);
				}
			}
		}

		this.emitSelectedMaps();
	}

	emitSelectedMaps() {
		const listSelecteds: Array<ModelMap> = [];
		this.maps.forEach((row, rowIndex) => {
			row.forEach((map, mapIndex) => {
				if (map.value === 1) {
					listSelecteds.push(map);
				}
			});
		});
		this.selectedMapsChange.emit(listSelecteds);
	}

	uncheckAllPositionsMap() {
		this.maps = [];
		for (let i = 0; i < this.totalRows; i++) {
			const row = [];
			for (let j = 0; j < this.mapsPerRow; j++) {
				const pos: ModelMap = {
					value: 0,
					aparelhoId: 0,
					siglaAparelho: "",
					nomeAparelho: "",
				};
				row.push(pos);
			}
			this.maps.push(row);
		}
		this.emitSelectedMaps();
	}

	changeSelectAparelhoReservaEquipamento() {}

	valueIsNotNull(value): boolean {
		return value && value !== null && value !== undefined;
	}

	stringIsNotEmpty(value): boolean {
		if (!this.valueIsNotNull(value)) {
			return false;
		}
		return value.trim() !== "";
	}

	numberIsNotEmpty(value): boolean {
		if (!this.valueIsNotNull(value)) {
			return false;
		}
		return value > 0;
	}

	getSiglaPos(rowIndex, mapIndex, map) {
		if (this.stringIsNotEmpty(map.siglaAparelho)) {
			return map.siglaAparelho;
		}
		return this.mapFull[rowIndex][mapIndex].nome;
	}

	getPos(rowIndex, mapIndex, map) {
		const posicao = this.mapFull[rowIndex][mapIndex].nome;
		if (!this.utilizarNumeracaoSequencial) {
			return posicao;
		} else {
			const index = this.listaMapaAuxiliar.indexOf(posicao);
			return (index + 1).toString();
		}
	}

	getTooltip(item: ModelMap): string {
		if (this.stringIsNotEmpty(item.nomeAparelho)) {
			return item.nomeAparelho;
		}
		return "";
	}
}
