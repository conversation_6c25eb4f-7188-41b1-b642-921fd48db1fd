@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

.btn-uncheck-all {
	margin-top: 1rem;
}

.map-selection-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
	border: 1px solid hsla(225, 5%, 85%, 1);
	border-radius: 15px;
	margin: 20px 0;

	.title-map {
		position: relative;
		width: 100%;
		margin-bottom: 20px;

		&:after {
			height: 1px;
			background-color: hsla(225, 5%, 85%, 1);
			width: 100%;
			top: 50%;
			position: absolute;
			content: "";
		}

		span {
			background-color: $supportGray01;
			color: $actionDefaultDisabled02;
			font-size: 12px;
			line-height: 16px;
			display: block;
			width: 180px;
			border-radius: 13px;
			padding: 4px 0;
			text-align: center;
			margin: auto;
			position: relative;
			z-index: 1;
		}
	}

	.row {
		display: flex;
		margin-bottom: 5px;
	}

	.map {
		width: 30px;
		height: 30px;
		margin: 3px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		border: 1px solid $supportGray05;
		border-radius: 50%;
		width: 45px;
		height: 45px;
		color: #007bff;
		background-color: #fff;
		font-size: 18px;
		transition: background-color 0.2s;
		margin: 5px 10px;

		.unselected {
			color: $supportGray05;
			font-size: 12px;
			font-family: "Poppins", sans-serif !important;
			font-weight: 600 !important;
		}
	}

	.map.selected {
		background-color: $actionDefaultAble04;
		color: white;
		font-size: 12px;
		font-family: $fontPoppins !important;
		font-weight: 600 !important;
	}
}

.row-select-aparelho {
	margin-top: 25px;
}

.desmarcar-container {
	margin-top: 30px;
	margin-bottom: 10px;

	span {
		font-size: 14px;
		font-weight: 600;
		line-height: 14px;
		letter-spacing: 0.25px;
		color: $actionDefaultAble04;
		cursor: pointer;
	}
}
