<pacto-cat-layout-v2>
	<header class="cabecalho-principal">
		<div class="breadcrumbs">
			<div class="mod">Agenda</div>
			<i class="pct pct-chevron-right"></i>
			<div class="mod">Cadastros</div>
			<i class="pct pct-chevron-right"></i>
			<div class="mod">Aula</div>
			<i class="pct pct-chevron-right"></i>
		</div>
		<div class="navegate">
			<div class="seta" (click)="backList()">
				<i class="pct pct-arrow-left ng-star-inserted"></i>
			</div>
			<div *ngIf="entity" class="titulo">Adicionar Aula</div>
			<div *ngIf="!entity" class="titulo">Editar Aula</div>
		</div>
	</header>

	<pacto-cat-card-plain *ngIf="operation === 'create'">
		<pacto-cat-stepper class="force-header-start" style="margin-bottom: 10px">
			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Informações</ng-template>

				<form [formGroup]="formGroup">
					<div class="row">
						<ds3-form-field class="col-6">
							<ds3-field-label>Nome da aula*</ds3-field-label>
							<input
								[id]="'nome-aula-input'"
								ds3Input
								formControlName="identificador"
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<ds3-form-field class="col-6">
							<ds3-field-label>Cor da aula</ds3-field-label>
							<ds3-color-picker
								ds3Input
								[formControl]="formGroup.get('cor')"></ds3-color-picker>
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-3">
							<ds3-field-label>Modalidade*</ds3-field-label>
							<ds3-select
								[id]="'modalidade-select'"
								[formControl]="formGroup.get('modalidadeId')"
								[options]="modalidades"
								ds3Input></ds3-select>
						</ds3-form-field>
						<ds3-form-field class="col-3">
							<ds3-field-label>Níveis</ds3-field-label>
							<ds3-select-multi
								[formControl]="formGroup.get('niveis')"
								[id]="'niveis-select'"
								[nameKey]="'nome'"
								[valueKey]="'id'"
								[options]="listaNiveis"
								ds3Input></ds3-select-multi>
						</ds3-form-field>
						<ds3-form-field class="col-3">
							<ds3-field-label>
								Tolerância para realizar check-in*
							</ds3-field-label>
							<input
								[id]="'tolerancia-aula-input'"
								ds3Input
								formControlName="toleranciaMin"
								mask="9999"
								type="number"
								placeholder="00" />
							<span class="typography-overline-bold-2" ds3Suffix>min</span>
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label></ds3-field-label>
							<ds3-select
								[id]="'tipotolerancia-select'"
								[formControl]="formGroup.get('tipoTolerancia')"
								[options]="tiposTolerancia"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-6 container-bonificacao">
							<ds3-field-label>Bonificação</ds3-field-label>

							<div class="form-check form-check-inline">
								<input
									class="form-check-input"
									id="bonificacao-sim"
									formControlName="usarBonificacao"
									name="bonificacao"
									type="radio"
									value="sim"
									(change)="alternarBonificacao('sim')" />
								<label class="form-check-label" for="bonificacao-sim">
									Sim
								</label>

								<input
									class="form-check-input"
									id="bonificacao-nao"
									formControlName="usarBonificacao"
									name="bonificacao"
									type="radio"
									value="nao"
									(change)="alternarBonificacao('nao')"
									style="margin-left: 24px" />
								<label class="form-check-label" for="bonificacao-nao">
									Não
								</label>
							</div>
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>Meta de ocupação</ds3-field-label>
							<input
								[id]="'meta-ocupacao'"
								ds3Input
								formControlName="meta"
								type="number"
								placeholder="00"
								mask="999" />
							<span class="typography-overline-bold-2" ds3Suffix>%</span>
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>Valor da bonificação</ds3-field-label>
							<input
								[id]="'valor-ocupacao'"
								[options]="{
									align: 'left',
									prefix: 'R$ ',
									thousands: '.',
									decimal: ','
								}"
								currencyMask
								ds3Input
								formControlName="bonificacao"
								placeholder="R$ 0,00" />
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-3">
							<ds3-field-label>Idade mínima (anos)</ds3-field-label>
							<input
								[id]="'idade-minima-anos'"
								ds3Input
								formControlName="idadeMinimaAnos"
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>(meses)</ds3-field-label>
							<input
								[id]="'idade-minima-meses'"
								ds3Input
								formControlName="idadeMinimaMeses"
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>Idade máxima (anos)</ds3-field-label>
							<input
								[id]="'idade-maxima-anos'"
								ds3Input
								formControlName="idadeMaximaAnos"
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>(meses)</ds3-field-label>
							<input
								[id]="'idade-maxima-meses'"
								ds3Input
								formControlName="idadeMaximaMeses"
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-field-label>
								Reserva de equipamento
								<i
									class="pct pct-info info-reserva-equipamento"
									ngbTooltip="Deixe com a opção 'Livre' caso sua aula não tenha equipamentos ou não tenha necessidade de reservas de equipamentos."></i>
							</ds3-field-label>
							<ds3-select
								[id]="'select-tipo-reserva-equipamento'"
								[formControl]="formGroup.get('tipoReservaEquipamento')"
								[options]="equipamentos"
								(valueChanges)="changeSelectReservaEquipamento()"
								ds3Input></ds3-select>
						</ds3-form-field>

						<div class="col-md-12 row-margin-top" *ngIf="showMapaEquipamentos">
							<label class="control-label">Mapa dos equipamentos</label>
							<div class="box-info">
								<i class="pct pct-info"></i>
								<p>
									Selecione a posição dos equipamentos no ambiente em relação à
									posição do professor. Caso nenhuma marcação estiver
									selecionada, a posição dos equipamentos ficará à livre
									escolha.
								</p>
							</div>

							<map-selection
								[title]="'Professor(a)'"
								[listEquipamentosSelecionados]="aulaEdit?.mapaEquipamentos"
								[aparelhos]="aparelhosReservaEquipamento"
								[listaMapaEquipamentoAparelho]="
									aulaEdit?.listaMapaEquipamentoAparelho
								"
								[limiteEquipamentos]="getLimiteReservaEquipamento"
								[showBtnUncheckAll]="true"
								(selectedMapsChange)="
									handleSelectedMaps($event)
								"></map-selection>
						</div>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-6">
							<ds3-field-label>Vídeo no youtube</ds3-field-label>
							<input
								ds3Input
								[formControl]="formGroupVideosUri.get('linkVideo')"
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label></ds3-field-label>
							<ds3-checkbox
								[formControl]="formGroupVideosUri.get('professor')"
								style="margin-top: 12px"
								[title]="
									'Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o professor'
								">
								Mostrar apenas para professor
							</ds3-checkbox>
						</ds3-form-field>

						<ds3-form-field class="col-2">
							<ds3-field-label></ds3-field-label>
							<button
								id="id-adicionar-alterar-link-video"
								ds3-outlined-button
								(click)="adicionarHandler()"
								type="button">
								Adicionar
							</button>
						</ds3-form-field>
					</div>

					<div class="row row-margin-top" *ngIf="linkVideos.length > 0">
						<div class="col-lg-12">
							<div class="list-wrapper">
								<table class="table">
									<thead>
										<tr>
											<th>Link</th>
											<th class="table-item">Professor</th>
											<th class="table-item">Excluir</th>
										</tr>
									</thead>
									<tbody>
										<tr *ngFor="let link of linkVideos; let index = index">
											<td>{{ link.linkVideo }}</td>
											<td class="table-item">
												<input
													class="form-check-input"
													[checked]="link.professor"
													type="checkbox"
													(click)="checkCheckBoxProfessor(link)"
													[pactoCatTolltip]="tooltipProfessor"
													[position]="'bottom'" />
											</td>
											<td class="table-item">
												<i
													class="fa fa-trash-o"
													(click)="deletButon(index)"></i>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-field-label>Descrição</ds3-field-label>
							<input
								[id]="'descricao-aula-input'"
								ds3Input
								formControlName="descricao"
								type="text"
								placeholder="Descreva sobre o que é e qual o objetivo dessa aula" />
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-field-label class="mb-1">Imagem</ds3-field-label>
							<ds3-upload
								[control]="formGroup.get('arquivo.dados')"
								[disabled]="false"
								[isColumn]="false"
								[nomeControl]="formGroup.get('arquivo.nome')"
								[urlImage]="formGroup.get('imageUrl')?.value"
								class="w-100 d-block"
								formatos="jpeg, jpg, png"></ds3-upload>
						</ds3-form-field>
					</div>

					<div
						class="row row-margin-top"
						*ngIf="integradoBookingGympass && isProductGymPassLoaded">
						<ds3-form-field class="col-4" *ngIf="utilizarSelectProdutoGympass">
							<ds3-field-label>Produto WellHub</ds3-field-label>
							<ds3-select
								[id]="'produto-gympass-select'"
								[formControl]="formGroup.get('produtoGymPass')"
								[options]="productsGymPass"
								ds3Input></ds3-select>
						</ds3-form-field>

						<ds3-form-field class="col-4" *ngIf="!utilizarSelectProdutoGympass">
							<ds3-field-label>Produto WellHub</ds3-field-label>
							<input
								[id]="'produto-gympass-input'"
								ds3Input
								formControlName="produtoGymPass"
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<ds3-form-field class="col-5">
							<ds3-field-label>Url turma virtual</ds3-field-label>
							<input
								[id]="'url-turma-input'"
								ds3Input
								formControlName="urlTurmaVirtual"
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<div class="col-3">
							<div class="idGymPass-label" *ngIf="idClasseGymPass">
								<div>
									<label>
										{{ "( Id na GymPass: " + idClasseGymPass + ")" }}
									</label>
								</div>

								<button
									*ngIf="exibirBotaoRemover()"
									id="btn-aulas-na-gympass"
									ds3-outlined-button
									(click)="removerProdutoGymPass()"
									type="button">
									Remover produto
									<i class="pct pct-trash"></i>
								</button>
							</div>
						</div>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'validar-marcacao-check'"
								formControlName="validarRestricoesMarcacao"
								[title]="
									'Com essa opção marcada o sistema irá contabilizar check-in para a aula, ' +
									'essa configuração só se aplica á planos com controle de check-in'
								">
								Controlar check-in
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'nao-validar-modalidade-contrato'"
								formControlName="naoValidarModalidadeContrato"
								[title]="
									'Com essa opção marcada o sistema não irá considerar a configuração ' +
									'(Validar Modalidade). Ex: Se você fizer um aulão, e não quer restrição por modalidade.'
								">
								Não validar modalidade de contrato
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'aula-disponivel-totalpass-check'"
								formControlName="visualizarProdutosTotalpass"
								[title]="
									'Ao marcar esta configuração, os alunos TotalPass que realizaram ' +
									'check-in na sua academia poderão marcar aulas pelo App ' +
									(showAbasAppPersonalizado
										? aplicativo_personalizado_nome + ' ou Agenda'
										: ' Treino ou Agenda')
								">
								Aula disponível para alunos TotalPass?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'aula-disponivel-gympass-check'"
								formControlName="visualizarProdutosGympass"
								[title]="
									'Ao marcar esta configuração, os alunos WellHub que realizaram check-in ' +
									'na sua academia poderão marcar aulas pelo App ' +
									(showAbasAppPersonalizado
										? aplicativo_personalizado_nome + ' ou Agenda'
										: ' Treino ou Agenda')
								">
								Aula disponível para alunos WellHub?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'aula-selfloops'"
								formControlName="aulaIntegracaoSelfloops"
								[title]="
									'Ao marcar esta configuração, o sistema irá enviar para a integração selfloops os dados dos desta aula e alunos que forem incluidos nela.'
								">
								Enviar aula para integração selfloops?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'fixar-aula-check'"
								formControlName="permiteFixar"
								[title]="
									'Ao marcar esta configuração, o sistema permitirá que os alunos sejam fixados na aula, até o fim do seu contrato ou até determinada data.'
								">
								Permite a fixação de alunos?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="mdl-agenda-editar-aula-action-row">
						<button
							(click)="cancelHandler()"
							id="btn-criar-aula-cancelar"
							ds3-outlined-button
							type="button">
							Cancelar
						</button>

						<button
							id="btn-criar-aula-avancar"
							ds3-flat-button
							pactoCatStepNext
							class="margin-left-10px"
							(click)="avancar()"
							[disabled]="!formGroup.valid">
							Avançar
							<span class="pct pct-chevron-right margin-left-10px"></span>
						</button>
					</div>
				</form>
			</pacto-cat-step>

			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Data e horários</ng-template>

				<form [formGroup]="formGroup">
					<div class="row">
						<ds3-form-field class="col-6">
							<ds3-field-label>Data inicial</ds3-field-label>
							<ds3-input-date
								[id]="'data-inicial-aula'"
								dateType="datepicker"
								[control]="formGroup.get('dataInicio')"></ds3-input-date>
						</ds3-form-field>

						<ds3-form-field class="col-6">
							<ds3-field-label>Data final</ds3-field-label>
							<ds3-input-date
								[id]="'data-final-aula'"
								dateType="datepicker"
								[control]="formGroup.get('dataFinal')"></ds3-input-date>
						</ds3-form-field>
					</div>

					<div class="row">
						<div class="col-12 horarios">
							<div class="table-wrapper tabela-horarios-aula">
								<div class="title-table">Horários cadastrados</div>
								<pacto-relatorio
									#tableData
									(btnAddClick)="btnClickHandler()"
									(iconClick)="actionClickHandler($event)"
									(pageChangeEvent)="pageChangeEvent($event)"
									(pageSizeChange)="pageSizeChange($event)"
									(rowClick)="btnEditHandler($event)"
									(sortEvent)="ordenarHorarios($event)"
									[enableDs3]="true"
									[filterConfig]="filterConfig"
									[labelBtnAdd]="'Adicionar horário'"
									[itensPerPage]="itensPerPage"
									[showBtnAdd]="true"
									[table]="table"
									actionTitulo="Ações"
									telaId="lista-horarios-aula-coletiva"></pacto-relatorio>
							</div>
						</div>
					</div>

					<div class="mdl-agenda-editar-aula-action-row">
						<button
							id="btn-criar-aula-voltar"
							ds3-outlined-button
							type="button"
							pactoCatStepPrevious>
							<span
								class="pct pct-chevron-left"
								style="margin-right: 5px"></span>
							Voltar
						</button>

						<button
							id="btn-salvar-aula"
							ds3-flat-button
							pactoCatStepNext
							class="margin-left-10px"
							(click)="salvarAula()"
							[disabled]="!formGroup.valid">
							Salvar aula
						</button>
					</div>
				</form>
			</pacto-cat-step>
		</pacto-cat-stepper>
	</pacto-cat-card-plain>

	<pacto-cat-tabs-transparent *ngIf="operation === 'edit'">
		<ng-template label="Informações" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div>
					<div class="row">
						<ds3-form-field class="col-6">
							<ds3-field-label>Nome da aula*</ds3-field-label>
							<input
								[id]="'nome-aula-input'"
								[formControl]="formGroup.get('identificador')"
								ds3Input
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<ds3-form-field class="col-6">
							<ds3-field-label>Cor da aula</ds3-field-label>
							<ds3-color-picker
								ds3Input
								[formControl]="formGroup.get('cor')"></ds3-color-picker>
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-3">
							<ds3-field-label>Modalidade*</ds3-field-label>
							<ds3-select
								[id]="'modalidade-select'"
								[formControl]="formGroup.get('modalidadeId')"
								[options]="modalidades"
								ds3Input></ds3-select>
						</ds3-form-field>
						<ds3-form-field class="col-3">
							<ds3-field-label>Níveis</ds3-field-label>
							<ds3-select-multi
								[formControl]="formGroup.get('niveis')"
								[id]="'niveis-select'"
								[nameKey]="'nome'"
								[valueKey]="'id'"
								[options]="listaNiveis"
								ds3Input></ds3-select-multi>
						</ds3-form-field>
						<ds3-form-field class="col-3">
							<ds3-field-label>
								Tolerância para realizar check-in*
							</ds3-field-label>
							<input
								[id]="'tolerancia-aula-input'"
								[formControl]="formGroup.get('toleranciaMin')"
								ds3Input
								mask="9999"
								type="number"
								placeholder="00" />
							<span class="typography-overline-bold-2" ds3Suffix>min</span>
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label></ds3-field-label>
							<ds3-select
								[id]="'tipotolerancia-select'"
								[formControl]="formGroup.get('tipoTolerancia')"
								[options]="tiposTolerancia"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-6 container-bonificacao">
							<ds3-field-label>Bonificação</ds3-field-label>

							<div class="form-check form-check-inline">
								<input
									[formControl]="formGroup.get('usarBonificacao')"
									class="form-check-input"
									id="bonificacao-sim"
									name="bonificacao"
									type="radio"
									value="sim"
									(change)="alternarBonificacao('sim')" />
								<label class="form-check-label" for="bonificacao-sim">
									Sim
								</label>

								<input
									[formControl]="formGroup.get('usarBonificacao')"
									class="form-check-input"
									id="bonificacao-nao"
									name="bonificacao"
									type="radio"
									value="nao"
									(change)="alternarBonificacao('nao')"
									style="margin-left: 24px" />
								<label class="form-check-label" for="bonificacao-nao">
									Não
								</label>
							</div>
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>Meta de ocupação</ds3-field-label>
							<input
								[id]="'meta-ocupacao'"
								[formControl]="formGroup.get('meta')"
								[options]="{
									align: 'left',
									prefix: '',
									thousands: '.',
									decimal: ','
								}"
								currencyMask
								ds3Input
								placeholder="R$ 0,00" />
							<span class="typography-overline-bold-2" ds3Suffix>%</span>
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>Valor da bonificação</ds3-field-label>
							<input
								[id]="'valor-ocupacao'"
								[formControl]="formGroup.get('bonificacao')"
								[options]="{
									align: 'left',
									prefix: 'R$ ',
									thousands: '.',
									decimal: ','
								}"
								currencyMask
								ds3Input
								placeholder="R$ 0,00" />
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-3">
							<ds3-field-label>Idade mínima (anos)</ds3-field-label>
							<input
								[id]="'idade-minima-anos'"
								[formControl]="formGroup.get('idadeMinimaAnos')"
								ds3Input
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>(meses)</ds3-field-label>
							<input
								[id]="'idade-minima-meses'"
								[formControl]="formGroup.get('idadeMinimaMeses')"
								ds3Input
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>Idade máxima (anos)</ds3-field-label>
							<input
								[id]="'idade-maxima-anos'"
								[formControl]="formGroup.get('idadeMaximaAnos')"
								ds3Input
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label>(meses)</ds3-field-label>
							<input
								[id]="'idade-maxima-meses'"
								[formControl]="formGroup.get('idadeMaximaMeses')"
								ds3Input
								type="number"
								placeholder="0"
								mask="999" />
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-field-label>
								Reserva de equipamento
								<i
									class="pct pct-info info-reserva-equipamento"
									ngbTooltip="Deixe com a opção 'Livre' caso sua aula não tenha equipamentos ou não tenha necessidade de reservas de equipamentos."></i>
							</ds3-field-label>
							<ds3-select
								[id]="'select-tipo-reserva-equipamento'"
								[formControl]="formGroup.get('tipoReservaEquipamento')"
								[options]="equipamentos"
								(valueChanges)="changeSelectReservaEquipamento()"
								ds3Input></ds3-select>
						</ds3-form-field>

						<div class="col-md-12 row-margin-top" *ngIf="showMapaEquipamentos">
							<label class="control-label">Mapa dos equipamentos</label>
							<div class="box-info">
								<i class="pct pct-info"></i>
								<p>
									Selecione a posição dos equipamentos no ambiente em relação à
									posição do professor. Caso nenhuma marcação estiver
									selecionada, a posição dos equipamentos ficará à livre
									escolha.
								</p>
							</div>

							<map-selection
								[title]="'Professor(a)'"
								[listEquipamentosSelecionados]="aulaEdit.mapaEquipamentos"
								[aparelhos]="aparelhosReservaEquipamento"
								[listaMapaEquipamentoAparelho]="
									aulaEdit.listaMapaEquipamentoAparelho
								"
								[limiteEquipamentos]="getLimiteReservaEquipamento"
								[showBtnUncheckAll]="true"
								(selectedMapsChange)="
									handleSelectedMaps($event)
								"></map-selection>
						</div>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-6">
							<ds3-field-label>Vídeo no youtube</ds3-field-label>
							<input
								ds3Input
								[formControl]="formGroupVideosUri.get('linkVideo')"
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<ds3-form-field class="col-3">
							<ds3-field-label></ds3-field-label>
							<ds3-checkbox
								[formControl]="formGroupVideosUri.get('professor')"
								style="margin-top: 12px"
								[title]="
									'Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o professor'
								">
								Mostrar apenas para professor
							</ds3-checkbox>
						</ds3-form-field>

						<ds3-form-field class="col-2">
							<ds3-field-label></ds3-field-label>
							<button
								id="id-adicionar-alterar-link-video"
								ds3-outlined-button
								(click)="adicionarHandler()"
								type="button">
								Adicionar
							</button>
						</ds3-form-field>
					</div>

					<div class="row row-margin-top" *ngIf="linkVideos.length > 0">
						<div class="col-lg-12">
							<div class="list-wrapper">
								<table class="table">
									<thead>
										<tr>
											<th>Link</th>
											<th class="table-item">Professor</th>
											<th class="table-item">Excluir</th>
										</tr>
									</thead>
									<tbody>
										<tr *ngFor="let link of linkVideos; let index = index">
											<td>{{ link.linkVideo }}</td>
											<td class="table-item">
												<input
													class="form-check-input"
													[checked]="link.professor"
													type="checkbox"
													(click)="checkCheckBoxProfessor(link)"
													[pactoCatTolltip]="tooltipProfessor"
													[position]="'bottom'" />
											</td>
											<td class="table-item">
												<i
													class="fa fa-trash-o"
													(click)="deletButon(index)"></i>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-field-label>Descrição</ds3-field-label>
							<input
								[id]="'descricao-aula-input'"
								[formControl]="formGroup.get('descricao')"
								ds3Input
								type="text"
								placeholder="Descreva sobre o que é e qual o objetivo dessa aula" />
						</ds3-form-field>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-field-label class="mb-1">Imagem</ds3-field-label>
							<ds3-upload
								(removed)="handleImageRemoval()"
								[control]="formGroup.get('arquivo.dados')"
								[disabled]="false"
								[isColumn]="false"
								[nomeControl]="formGroup.get('arquivo.nome')"
								[urlImage]="formGroup.get('imageUrl')?.value"
								class="w-100 d-block"
								formatos="jpeg, jpg, png"></ds3-upload>
						</ds3-form-field>
					</div>

					<div
						class="row row-margin-top"
						*ngIf="integradoBookingGympass && isProductGymPassLoaded">
						<ds3-form-field class="col-4" *ngIf="utilizarSelectProdutoGympass">
							<ds3-field-label>Produto WellHub</ds3-field-label>
							<ds3-select
								[id]="'produto-gympass-select'"
								[formControl]="formGroup.get('produtoGymPass')"
								[options]="productsGymPass"
								ds3Input></ds3-select>
						</ds3-form-field>

						<ds3-form-field class="col-4" *ngIf="!utilizarSelectProdutoGympass">
							<ds3-field-label>Produto WellHub</ds3-field-label>
							<input
								[id]="'produto-gympass-input'"
								[formControl]="formGroup.get('produtoGymPass')"
								ds3Input
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<ds3-form-field class="col-5">
							<ds3-field-label>Url turma virtual</ds3-field-label>
							<input
								[id]="'url-turma-input'"
								[formControl]="formGroup.get('urlTurmaVirtual')"
								ds3Input
								type="text"
								placeholder=" - " />
						</ds3-form-field>

						<div class="col-3">
							<div class="idGymPass-label" *ngIf="idClasseGymPass">
								<div>
									<label>
										{{ "( Id na GymPass: " + idClasseGymPass + ")" }}
									</label>
								</div>

								<button
									*ngIf="exibirBotaoRemover()"
									id="btn-aulas-na-gympass"
									ds3-outlined-button
									(click)="removerProdutoGymPass()"
									type="button">
									Remover produto
									<i class="pct pct-trash"></i>
								</button>
							</div>
						</div>
					</div>

					<div class="row row-margin-top">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'validar-marcacao-check'"
								[formControl]="formGroup.get('validarRestricoesMarcacao')"
								[title]="
									'Com essa opção marcada o sistema irá contabilizar check-in para a aula, ' +
									'essa configuração só se aplica á planos com controle de check-in'
								">
								Controlar check-in
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'nao-validar-modalidade-contrato'"
								[formControl]="formGroup.get('naoValidarModalidadeContrato')"
								[title]="
									'Com essa opção marcada o sistema não irá considerar a configuração ' +
									'(Validar Modalidade). Ex: Se você fizer um aulão, e não quer restrição por modalidade.'
								">
								Não validar modalidade de contrato
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'aula-disponivel-totalpass-check'"
								[formControl]="formGroup.get('visualizarProdutosTotalpass')"
								[title]="
									'Ao marcar esta configuração, os alunos TotalPass que realizaram ' +
									'check-in na sua academia poderão marcar aulas pelo App ' +
									(showAbasAppPersonalizado
										? aplicativo_personalizado_nome + ' ou Agenda'
										: ' Treino ou Agenda')
								">
								Aula disponível para alunos TotalPass?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'aula-disponivel-gympass-check'"
								[formControl]="formGroup.get('visualizarProdutosGympass')"
								[title]="
									'Ao marcar esta configuração, os alunos WellHub que realizaram check-in ' +
									'na sua academia poderão marcar aulas pelo App ' +
									(showAbasAppPersonalizado
										? aplicativo_personalizado_nome + ' ou Agenda'
										: ' Treino ou Agenda')
								">
								Aula disponível para alunos WellHub?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'aula-selfloops'"
								[formControl]="formGroup.get('aulaIntegracaoSelfloops')"
								[title]="
									'Ao marcar esta configuração, o sistema irá enviar para a integração selfloops os dados dos desta aula e alunos que forem incluidos nela.'
								">
								Enviar aula para integração selfloops?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="row">
						<ds3-form-field class="col-12">
							<ds3-checkbox
								[id]="'fixar-aula-check'"
								[formControl]="formGroup.get('permiteFixar')"
								[title]="
									'Ao marcar esta configuração, o sistema permitirá que os alunos sejam fixados na aula, até o fim do seu contrato ou até determinada data.'
								">
								Permite a fixação de alunos?
							</ds3-checkbox>
						</ds3-form-field>
					</div>

					<div class="mdl-agenda-editar-aula-action-row">
						<button
							(click)="cancelHandler()"
							id="btn-criar-aula-cancelar"
							ds3-outlined-button
							type="button">
							Cancelar
						</button>

						<button
							id="btn-salvar-alteracoes-informacoes"
							ds3-flat-button
							class="margin-left-10px"
							(click)="salvarAula()">
							Salvar alterações
						</button>
					</div>
				</div>
			</pacto-cat-card-plain>
		</ng-template>

		<ng-template label="Horários" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div>
					<div class="row">
						<ds3-form-field class="col-6">
							<ds3-field-label>Data inicial</ds3-field-label>
							<ds3-input-date
								[id]="'data-inicial-aula'"
								dateType="datepicker"
								[control]="formGroup.get('dataInicio')"></ds3-input-date>
						</ds3-form-field>

						<ds3-form-field class="col-6">
							<ds3-field-label>Data final</ds3-field-label>
							<ds3-input-date
								[id]="'data-final-aula'"
								dateType="datepicker"
								[control]="formGroup.get('dataFinal')"></ds3-input-date>
						</ds3-form-field>
					</div>

					<div class="row">
						<div class="col-12 horarios">
							<div class="table-wrapper tabela-horarios-aula">
								<div class="title-table">Horários cadastrados</div>
								<pacto-relatorio
									#tableData
									(btnAddClick)="btnClickHandler()"
									(iconClick)="actionClickHandler($event)"
									(pageChangeEvent)="pageChangeEvent($event)"
									(pageSizeChange)="pageSizeChange($event)"
									(rowClick)="btnEditHandler($event)"
									(sortEvent)="ordenarHorarios($event)"
									[enableDs3]="true"
									[filterConfig]="filterConfig"
									[labelBtnAdd]="'Adicionar horário'"
									[itensPerPage]="itensPerPage"
									[showBtnAdd]="true"
									[table]="table"
									actionTitulo="Ações"
									telaId="lista-horarios-aula-coletiva"></pacto-relatorio>
							</div>
						</div>
					</div>

					<div class="mdl-agenda-editar-aula-action-row">
						<button
							(click)="cancelHandler()"
							id="btn-criar-aula-voltar"
							ds3-outlined-button
							type="button">
							Cancelar
						</button>

						<button
							(click)="salvarAula()"
							id="btn-salvar-alteracoes-horarios"
							ds3-flat-button
							class="margin-left-10px">
							Salvar alterações
						</button>
					</div>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
	</pacto-cat-tabs-transparent>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="createSuccess" i18n="@@crud-aulas:create-success">
		Aula criada com sucesso.
	</span>
	<span xingling="editSuccess" i18n="@@crud-aulas:create-edit">
		Aula editada com sucesso.
	</span>
	<span xingling="campoObrigatorio" i18n="@@crud-aulas:campo-obrigatorio">
		Campos obrigatórios não preenchidos.
	</span>
	<span
		xingling="inserirHorario"
		i18n="@@crud-aulas:campo-obrigatorio:inserir-horario">
		Inserir pelo menos um horário na lista
	</span>
	<span xingling="validacaoData" i18n="@@crud-aulas:validacao:data">
		Data inicial não pode ser superior a data final
	</span>
	<span
		xingling="validacaoQtEquipamentosReserva"
		i18n="@@crud-aulas:validacao:qtEquipamentosReserva">
		A quantidade mínima de equipamentos para reserva ainda não foi atingida,
		mínimo: {{ getLimiteReservaEquipamento }}
	</span>
	<span xingling="validacaoHorario" i18n="@@crud-aulas:validacao:horario">
		Horário cadastrado invalido, tente novamente
	</span>
	<span
		xingling="validacaoHorarioCadastrado"
		i18n="@@crud-aulas:validacao:horario">
		Horário já cadastrado
	</span>
	<span xingling="validacaoDiaSemana" i18n="@@crud-aulas:validacao:dia semana">
		Selecione ao menos um dia da semana.
	</span>
	<span xingling="horarionaoPodeSerRemovido">
		Não é possível excluir o horário, contém aula confirmada.
	</span>
	<span xingling="erro_aula_url_virtual">Informe a URL da aula virtual.</span>
	<span xingling="erro_incluir_aula" i18n="@@crud-aulas:erro_incluir_aula">
		Ocorreu um erro ao incluir a aula, tente novamente em alguns instantes!
	</span>
	<span i18n="@@crud-ambientes:success:create" xingling="successCreate">
		Ambiente criado com sucesso.
	</span>
	<span
		i18n="@@crud-modalidade:create-modal:success"
		xingling="mensagemCreateSuccess">
		Modalidade criada com sucesso.
	</span>
</pacto-traducoes-xingling>

<ng-template #tooltipProfessor>
	<div class="itens-tooltip">
		Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o
		professor
	</div>
</ng-template>

<!--table columns-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar horário</span>
</ng-template>
<ng-template #codigoColumnName>
	<span i18n="@@crud-turma-edit:codigo:title:table">Código</span>
</ng-template>
<ng-template #professorColumnName>
	<span i18n="@@crud-turma-edit:professor:title:table">Professor</span>
</ng-template>
<ng-template #duracaoColumnName>
	<span i18n="@@crud-turma-edit:duracao:title:table">Duração</span>
</ng-template>
<ng-template #diaColumnName>
	<span i18n="@@crud-turma-edit:dia:title:table">Dia</span>
</ng-template>
<ng-template #maxAlunosColumnName>
	<span i18n="@@crud-turma-edit:maxAlunos:title:table">Nº máx. alunos</span>
</ng-template>
<ng-template #horarioColumnName>
	<span i18n="@@crud-turma-edit:horarioTurma:title:table">Horário</span>
</ng-template>
<ng-template #ambienteColumnName>
	<span i18n="@@crud-turma-edit:ambiente:title:table">Ambiente</span>
</ng-template>
<ng-template #toleranciaMinColumnName>
	<span i18n="@@crud-turma-edit:toleranciaMin:title:table">
		Tôlerancia(min)
	</span>
</ng-template>
<!--<ng-template #nivelTurmaColumnName>-->
<!--	<span i18n="@@crud-turma-edit:nivelTurma:title:table">Nível</span>-->
<!--</ng-template>-->
<ng-template #acaoColumnName>
	<span i18n="@@crud-atividades:table:status">Ações</span>
</ng-template>
<!--End table columns-->

<!--Celulas para formatação-->
<ng-template #professorCelula let-item="item">
	{{ item.professor }}
</ng-template>
<ng-template #maxAlunosCelula let-item="item">
	{{ item.maxAlunos }}
</ng-template>
<ng-template #horarioCelula let-item="item">
	{{
		item.horaInicial.replace(":", "h") +
			" às " +
			item.horaFinal.replace(":", "h")
	}}
</ng-template>
<ng-template #duracaoCelula let-item="item">
	{{ obterDuracao(item) }}
</ng-template>
<ng-template #diaSemanaCelula let-item="item">
	{{ obterDiaSemana(item) }}
</ng-template>

<span
	#actionNoUndone
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:actionNoUndone">
	Essa ação não poderá ser desfeita
</span>
