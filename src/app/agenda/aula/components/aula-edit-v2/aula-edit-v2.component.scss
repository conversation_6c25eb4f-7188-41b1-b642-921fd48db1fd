@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

.cabecalho-principal {
	display: flex;
	flex-direction: column;

	.seta {
		cursor: pointer;
		color: #51555a;
		font-size: 1.5rem;
		font-weight: 400;
		margin-right: 12px;
		width: 25px;
	}

	.breadcrumbs {
		display: flex;
		grid-column: 2 / 3;
		grid-row: 1 / 2;
		align-items: center;
		margin-bottom: 10px;
		margin-left: 36px;

		.mod,
		.sessao {
			font-family: Poppins !important;
			color: #797d86;
			font-size: 12px;
			font-weight: 600;
			line-height: 12px;
		}

		i {
			margin: 0 12px;
		}
	}

	.navegate {
		display: flex;
		margin-bottom: 24px;
	}

	.titulo {
		grid-column: 2 / 3;
		grid-row: 2 / 3;
		font-family: Poppins;
		font-size: 22px;
		font-weight: 600;
		line-height: 27.5px;
		letter-spacing: 0.25px;
		color: #55585e;
		display: flex;
		align-items: center;
	}
}

:host ::ng-deep .force-header-start > .pct-stepper-navigation-bar {
	display: flex;
	justify-content: flex-start;
}

.row-margin-top {
	margin-top: 25px;
}

.form-check {
	.radio-nao {
		margin-left: 23px !important;
	}
}

.container-bonificacao::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 15px;
	right: 15px;
	height: 2px;
	background: #c9cbcf;
}

.mdl-agenda-editar-aula-action-row {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-top: 35px;

	//button {
	//	&:not(:last-of-type) {
	//		margin-right: px-to-rem(8px);
	//	}
	//}
}

.margin-left-10px {
	margin-left: 10px;
}

label.control-label {
	font-weight: 600;
}

.map-selection-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
	border: 1px solid hsla(225, 5%, 85%, 1);
	border-radius: 15px;
	margin: 20px 0;

	.title-map {
		position: relative;
		width: 100%;
		margin-bottom: 20px;

		&:after {
			height: 1px;
			background-color: hsla(225, 5%, 85%, 1);
			width: 100%;
			top: 50%;
			position: absolute;
			content: "";
		}

		span {
			background-color: $supportGray01;
			color: $actionDefaultDisabled02;
			font-size: 12px;
			line-height: 16px;
			display: block;
			width: 150px;
			border-radius: 13px;
			padding: 4px 0;
			text-align: center;
			margin: auto;
			position: relative;
			z-index: 1;
		}
	}

	.row {
		display: flex;
		margin-bottom: 5px;
	}

	.map {
		width: 30px;
		height: 30px;
		margin: 3px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		border: 1px solid $supportGray05;
		border-radius: 50%;
		width: 44px;
		height: 44px;
		color: #007bff;
		background-color: #fff;
		font-size: 18px;
		transition: background-color 0.2s;
		margin: 5px 10px;

		.pct-plus {
			color: $supportGray05;
			font-size: 16px;
		}
	}

	.map.selected {
		background-color: $actionDefaultAble04;
		color: white;
		font-size: 14px;
		font-family: $fontPoppins !important;
		font-weight: 600 !important;
	}
}

.box-info {
	display: flex;

	i {
		margin-right: 10px;
		display: flex;
		align-items: center;
	}

	* {
		color: $actionDefaultDisabled02;
	}

	p {
		font-size: 14px;
		margin-bottom: 0;
	}
}

.info-reserva-equipamento {
	color: $azul;
	margin-left: 8px;
}

.idGymPass-label {
	label {
		margin-bottom: var(--spacing-1, 0.5rem);
		color: hsla(223, 5%, 30%, 1);
		font-family: Poppins, sans-serif;
		font-size: 14px;
		line-height: 125%;
		letter-spacing: 0.25px;
		font-weight: 600;
	}
}

.tabela-horarios-aula {
	border: 1px solid #c9cbcf;
	margin-top: 45px;

	.title-table {
		position: absolute;
		margin-top: 24px;
		margin-left: 20px;
		color: hsla(223, 5%, 30%, 1);
		font-family: Poppins, sans-serif;
		font-size: 14px;
		line-height: 125%;
		letter-spacing: 0.25px;
		font-weight: 600;
	}
}

.table-wrapper::ng-deep {
	.pct-edit {
		color: $azul;
	}
	.pct-trash-2 {
		color: #fa1e1e;
	}
}

::ng-deep .modal-horario-aula .modal-dialog {
	width: 1000px !important;
	max-width: 1000px !important;
	height: 570px !important;
}
