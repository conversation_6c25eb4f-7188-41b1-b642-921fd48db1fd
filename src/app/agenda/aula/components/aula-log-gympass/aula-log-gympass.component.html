<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'AULAS',
			menuLink: ['agenda', 'aula']
		}"></pacto-breadcrumbs>

	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="filterConfig"
			[tableDescription]="'Log de Booking Gympass'"
			[tableTitle]="'Log de Booking'"
			[table]="table"></pacto-relatorio>

		<pacto-relatorio
			#tableDataSinc
			(iconClick)="actionClickHandler($event)"
			[tableDescription]="'Log de Sincronização de aulas Gympass'"
			[tableTitle]="'Log de Sincronia de aulas'"
			[table]="tableSinc"></pacto-relatorio>

		<pacto-cat-button
			(click)="atualizar()"
			[icon]="'pct pct-refresh-cw'"
			[id]="'btn-atualizar-bi'"
			i18n-label="@@treino-bi:btn-atualizar-new"
			label="atualizar"></pacto-cat-button>
	</div>
</pacto-cat-layout-v2>

<ng-template #registro let-item="item">
	{{ item.dataRegistro | date : "dd/MM/yyyy HH:mm" }}
</ng-template>

<ng-template #sucesso let-item="item">
	<i *ngIf="item.sucesso" class="fa fa-check"></i>
	<i *ngIf="!item.sucesso" class="fa fa-exclamation-triangle"></i>
</ng-template>

<ng-template #logcel let-item="item">
	<div class="log">
		{{ item.evento }}
	</div>
</ng-template>
