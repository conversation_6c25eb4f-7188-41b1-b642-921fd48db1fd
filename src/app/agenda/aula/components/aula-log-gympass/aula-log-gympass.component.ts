import { Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-aula-log-gympass",
	templateUrl: "./aula-log-gympass.component.html",
	styleUrls: ["./aula-log-gympass.component.scss"],
})
export class AulaLogGympassComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("tableDataSinc", { static: false })
	tableDataSinc: RelatorioComponent;
	table: PactoDataGridConfig;
	tableSinc: PactoDataGridConfig;
	filterConfig: any;
	@ViewChild("registro", { static: true }) registro;
	@ViewChild("sucesso", { static: true }) sucesso;
	@ViewChild("logcel", { static: true }) log;

	constructor(
		private rest: RestService,
		private router: Router,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.configTable();
	}

	atualizar() {
		this.tableData.reloadData();
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("aulas/gympass/log"),
			quickSearch: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "codigo",
				},
				{
					nome: "sucesso",
					titulo: "-",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "sucesso",
					celula: this.sucesso,
				},
				{
					nome: "dataRegistro",
					titulo: "Hora",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "dataRegistro",
					celula: this.registro,
				},
				{
					nome: "bookingNumber",
					titulo: "Booking Number",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "bookingNumber",
				},
				{
					nome: "evento",
					titulo: "Evento",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "evento",
				},
				{
					nome: "aluno",
					titulo: "Aluno",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "aluno",
				},
				{
					nome: "email",
					titulo: "Email",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "email",
				},
			],
			actions: [],
		});

		this.tableSinc = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("aulas/gympass/log/sinc"),
			quickSearch: false,
			columns: [
				{
					nome: "codigo",
					titulo: "ID DA TURMA",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "classId",
				},
				{
					nome: "sucesso",
					titulo: "-",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "sucesso",
					celula: this.sucesso,
				},
				{
					nome: "dataRegistro",
					titulo: "Hora",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "dataRegistro",
					celula: this.registro,
				},
				{
					nome: "log",
					titulo: "log",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "log",
					celula: this.log,
				},
			],
			actions: [
				{
					nome: "copy",
					iconClass: "fa fa-copy",
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "copy") {
			const selBox = document.createElement("textarea");
			selBox.style.position = "fixed";
			selBox.style.left = "0";
			selBox.style.top = "0";
			selBox.style.opacity = "0";
			selBox.value = $event.row.evento;
			document.body.appendChild(selBox);
			selBox.focus();
			selBox.select();
			document.execCommand("copy");
			document.body.removeChild(selBox);
			this.notificationService.success("log copiado!");
		} else {
			this.notificationService.success("simulado!");
		}
	}

	btnEditHandler(item) {
		this.router.navigate(["agenda", "aula", "gympass", "log", item.codigo]);
	}
}
