@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "src/assets/scss/pacto/plataforma-import.scss";

.actions {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;

	button {
		margin-left: 15px;
	}
}

.input-row {
	display: flex;

	.form-group {
		padding-right: 15px;
		margin-bottom: 0px;
		flex-grow: 1;
	}

	.btn-position {
		height: 34px;
		margin-top: 30px;
	}
}

table {
	i {
		cursor: pointer;
	}

	.action-column {
		width: 50px;
		text-align: center;
	}
}

.position-validar {
	margin: 35px 35px 35px 0px;
}

.btn-cadastros-position {
	height: 34px;
	margin-top: 30px;
	margin-left: -10px;
}

.check-marcacao {
	margin-top: 35px;
	margin-left: 20px;
}

.check-aula-disponivel {
	margin-top: 1px;
	margin-left: 5px;
}

.check-moda-contrato {
	margin-left: 25px;
	margin-top: 35px;
}

.idGymPass-label {
	margin-top: 35px;
	margin-left: 20px;
}

label.control-label {
	font-weight: 600;
}

pacto-cat-multi-select-filter-number::ng-deep {
	.double-arrow {
		bottom: 9px;
	}

	.text-option {
		line-height: 20px;
	}

	.current-value {
		justify-content: flex-start;
		border: 1px solid #ced4da;
	}

	.clear-icon {
		bottom: 9px;
	}

	.pacto-label {
		font-weight: 600;
		line-height: 1.5;
		margin-top: 0;
		margin-bottom: 0.5rem;
		font-size: 1rem;
		color: #67757c;
	}

	.current-value,
	.current-value-wrap {
		height: 33px;
		min-height: 33px;
	}
}

pacto-cat-form-input-number::ng-deep {
	margin: 0px;

	.nome {
		color: #67757c;
		font-weight: 600;
		display: inline-block;
		font-size: 1rem;
		line-height: 1.5;
	}

	input {
		padding: 0.25rem 0.5rem;
		font-size: 0.875rem;
		line-height: 1.5;
		border-radius: 0.2rem;
	}

	.pct {
		display: none;
	}
}

.button-remover {
	font-size: 12px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-family: "Nunito Sans", sans-serif;
	font-weight: 700;
	color: blue;
	cursor: pointer;
	padding-left: 1px;
}

.row-meta-container {
	pacto-cat-form-input-number::ng-deep {
		.nome {
			white-space: nowrap;
		}
	}
}

.header-row {
	display: flex;
	justify-content: space-between;
	padding-bottom: 10px;
}

.images-wrapper {
	margin-top: 10px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	border-top: 1px solid #d1d1d1;
	padding-top: 15px;

	.imagem {
		margin: 5px;
		width: 150px;

		.imagem-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f5f5f5;
			width: 150px;
			height: 150px;
		}

		.control-footer {
			display: flex;

			.name {
				width: calc(100% - 20px);
				padding-top: 5px;
				padding-right: 5px;
				word-break: break-all;
				overflow: hidden;
			}

			.icon {
				padding-top: 5px;
				flex-grow: 1;
				cursor: pointer;
			}
		}

		img {
			max-width: 150px;
			max-height: 150px;
		}
	}
}

.btn.btn-primary {
	margin-left: 10px;

	label {
		margin-bottom: 0;
		padding-left: 5px;
	}
}

.table-item {
	width: 50px;
	text-align: center;
}

.form-link-video {
	display: grid;
	grid-template-columns: 0.5fr 1.1fr 1.3fr 1fr;
	padding-top: 40px;
	padding-left: 30px;

	.btn-adicionar-link-video {
		text-align: left;
	}
}

::ng-deep pacto-input.error .form-control {
	border-color: red !important;
	background-color: #ffe6e6 !important;
}

::ng-deep pacto-select.error .form-control {
	border-color: red !important;
	background-color: #ffe6e6 !important;
}

::ng-deep pacto-input-number.error .form-control {
	border-color: red !important;
	background-color: #ffe6e6 !important;
}

::ng-deep pacto-datepicker.error .form-control {
	border-color: red !important;
	background-color: #ffe6e6 !important;
}

.pct-error-msg {
	color: red;
	font-size: 0.875em;
	margin-top: -0.8rem;
	margin-bottom: 0.25rem;
}

.map-selection-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
	border: 1px solid hsla(225, 5%, 85%, 1);
	border-radius: 15px;
	margin: 20px 0;

	.title-map {
		position: relative;
		width: 100%;
		margin-bottom: 20px;

		&:after {
			height: 1px;
			background-color: hsla(225, 5%, 85%, 1);
			width: 100%;
			top: 50%;
			position: absolute;
			content: "";
		}

		span {
			background-color: $supportGray01;
			color: $actionDefaultDisabled02;
			font-size: 12px;
			line-height: 16px;
			display: block;
			width: 150px;
			border-radius: 13px;
			padding: 4px 0;
			text-align: center;
			margin: auto;
			position: relative;
			z-index: 1;
		}
	}

	.row {
		display: flex;
		margin-bottom: 5px;
	}

	.map {
		width: 30px;
		height: 30px;
		margin: 3px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		border: 1px solid $supportGray05;
		border-radius: 50%;
		width: 44px;
		height: 44px;
		color: #007bff;
		background-color: #fff;
		font-size: 18px;
		transition: background-color 0.2s;
		margin: 5px 10px;

		.pct-plus {
			color: $supportGray05;
			font-size: 16px;
		}
	}

	.map.selected {
		background-color: $actionDefaultAble04;
		color: white;
		font-size: 14px;
		font-family: $fontPoppins !important;
		font-weight: 600 !important;
	}
}

.box-info {
	display: flex;

	i {
		margin-right: 10px;
		display: flex;
		align-items: center;
	}

	* {
		color: $actionDefaultDisabled02;
	}

	p {
		font-size: 14px;
		margin-bottom: 0;
	}
}

.info-reserva-equipamento {
	color: $azul;
	margin-left: 8px;
}
