import {
	ChangeDetector<PERSON>ef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import {
	SnotifyButton,
	SnotifyPosition,
	SnotifyService,
	SnotifyToastConfig,
} from "ng-snotify";
import { SnotifyType } from "ng-snotify/snotify/types/snotify.type";
import { SnotifyAnimate } from "ng-snotify/snotify/interfaces/SnotifyAnimate.interface";
import { isNullOrUndefinedOrEmpty } from "sdk";
import { LocalizationService } from "@base-core/localization/localization.service";
import {
	TreinoApiAulaService,
	Aula,
	AulaCreateEdit,
	Ambiente,
	Modalidade,
	TreinoApiColaboradorService,
	UsuarioBase,
	TreinoApiAmbienteService,
	TreinoApiModalidadeService,
	ProductGymPass,
	TreinoApiAparelhoService,
	Aparelho,
	ModelMap,
	TurmaMapaEquipamentoAparelho,
} from "treino-api";
import { AmbienteEditModalComponent } from "../../../../agenda/ambiente/components/ambiente-edit-modal/ambiente-edit-modal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalidadeEditModalComponent } from "../../../../agenda/modalidade/components/modalidade-edit-modal/modalidade-edit-modal.component";
import { SessionService } from "@base-core/client/session.service";
import {
	ConfirmDialogDeleteComponent,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SafeHtml } from "@angular/platform-browser";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { DatePipe } from "@angular/common";
import { RestService } from "@base-core/rest/rest.service";
import { SeletorImagemAvancadoComponent } from "../../../../treino/atividade/components/seletor-imagem-avancado/seletor-imagem-avancado.component";
import { AgendaCardsStateService } from "../../../agenda/services/agenda-cards-state.service";
import { DataUrl, NgxImageCompressService } from "ngx-image-compress-legacy";
import { ApiResponseList } from "@base-core/rest/rest.model";

enum ImageType {
	UPLOAD = "UPLOAD",
}

interface ImagemConfig {
	type?: ImageType;
	id?: string;
	uri: string;
	nome?: string;
	data?: any;
}

@Component({
	selector: "pacto-aula-edit",
	templateUrl: "./aula-edit.component.html",
	styleUrls: ["./aula-edit.component.scss"],
})
export class AulaEditComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	linkVideos = [];
	modalidades: Array<Modalidade> = [];
	productsGymPass: Array<ProductGymPass> = [];
	professores: Array<UsuarioBase> = [];
	ambientes: Array<Ambiente> = [];
	aparelhosReservaEquipamento: Array<Aparelho> = [];
	tiposTolerancia = [
		{ id: "1", nome: "Após o início da aula" },
		{ id: "2", nome: "Antes do início da aula" },
	];
	horarios: Array<any> = [];
	showAbasAppPersonalizado: boolean;
	aula: Aula = {
		tipoReservaEquipamento: "",
		mapaEquipamentos: "",
	};
	aulaEdit: AulaCreateEdit;
	timerMask = [
		/[0-2]/,
		/[0-9]/,
		":",
		/[0-5]/,
		/[0-9]/,
		" ",
		"-",
		" ",
		/[0-2]/,
		/[0-9]/,
		":",
		/[0-5]/,
		/[0-9]/,
	];
	percentMask = [/[0-9]/, /[0-9]/, "%"];
	textMask = { mask: false };
	textMask1 = { mask: false };
	operation: string;
	entity = true;
	integracaoZw;
	utilizarSelectProdutoGympass = true;

	images: Array<ImagemConfig> = [];
	loading = false;

	configSnotify: SnotifyToastConfig = new (class implements SnotifyToastConfig {
		animation: SnotifyAnimate;
		backdrop: number;
		bodyMaxLength: number;
		buttons: SnotifyButton[];
		closeOnClick: boolean;
		html: string | SafeHtml;
		icon: string;
		iconClass: string;
		pauseOnHover: boolean;
		placeholder: string;
		position: SnotifyPosition;
		showProgressBar: boolean;
		timeout: number;
		titleMaxLength: number;
		type: SnotifyType;
	})();

	controlHorario: FormControl = new FormControl("");
	formGroup: FormGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		ocupacao: new FormControl(null),
		modalidadeId: new FormControl(null, [Validators.required]),
		niveis: new FormControl(),
		professorId: new FormControl(null, [Validators.required]),
		ambienteId: new FormControl(null, [Validators.required]),
		toleranciaMin: new FormControl(null, [Validators.required]),
		tipoTolerancia: new FormControl(null, [Validators.required]),
		dataInicio: new FormControl(null, [Validators.required]),
		dataFinal: new FormControl(null, [Validators.required]),
		capacidade: new FormControl(null, [Validators.required]),
		limiteVagasAgregados: new FormControl(0),
		meta: new FormControl(null),
		idadeMinimaAnos: new FormControl(null),
		idadeMinimaMeses: new FormControl(null),
		idadeMaximaAnos: new FormControl(null),
		idadeMaximaMeses: new FormControl(null),
		qtdeMaximaAlunoExperimental: new FormControl(null),
		pontuacaoBonus: new FormControl(null),
		bonificacao: new FormControl(null),
		mensagem: new FormControl(null),
		urlVideoYoutube: new FormControl(null),
		diasSemana: new FormGroup(
			{
				domingo: new FormControl(null),
				segunda: new FormControl(null),
				terca: new FormControl(null),
				quarta: new FormControl(null),
				quinta: new FormControl(null),
				sexta: new FormControl(null),
				sabado: new FormControl(null),
			},
			[Validators.required]
		),
		validarRestricoesMarcacao: new FormControl(false),
		visualizarProdutosGympass: new FormControl(false),
		visualizarProdutosTotalpass: new FormControl(false),
		permiteFixar: new FormControl(false),
		aulaIntegracaoSelfloops: new FormControl(false),
		naoValidarModalidadeContrato: new FormControl(false),
		produtoGymPass: new FormControl({ value: null, disabled: false }),
		urlTurmaVirtual: new FormControl({ value: null, disabled: false }),
		tipoReservaEquipamento: new FormControl(null, [Validators.required]),
	});
	formGroupVideosUri: FormGroup = new FormGroup({
		id: new FormControl(),
		linkVideo: new FormControl("", [Validators.required]),
		professor: new FormControl(false),
	});
	integradoBookingGympass: any;
	validarModalidadeContrato: any;
	idClasseGymPass: any;
	aplicativo_personalizado_nome: any;
	pactobr = false;
	salvando: boolean = false;
	imgAulaDataUrl: DataUrl = "";
	manterFoto: boolean = true;

	equipamentos: Array<any> = [
		{ id: "LIVRE", nome: "Livre" },
		{ id: "CAPACIDADE_AULA", nome: "Mapa de equipamentos" },
	];
	selectedMaps = "";
	showMapaEquipamentos = false;
	mapaReservaEquipaementoSelecionados: Array<ModelMap> = [];
	listaTurmaMapaEquipamentoAparelho: Array<TurmaMapaEquipamentoAparelho> = [];

	constructor(
		@Inject(LOCALE_ID) private locale,
		private localization: LocalizationService,
		private modalidadeService: TreinoApiModalidadeService,
		private aparelhoService: TreinoApiAparelhoService,
		private notificationService: SnotifyService,
		private aulaService: TreinoApiAulaService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private ambienteService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private modal: NgbModal,
		private configCache: TreinoConfigCacheService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private imageCompress: NgxImageCompressService,
		private session: SessionService,
		private ngbModal: NgbModal,
		private treinoConfigService: TreinoConfigCacheService,
		private agendaStateService: AgendaCardsStateService
	) {}

	ngOnInit() {
		this.integracaoZw = this.session.integracaoZW;
		this.configuracoesTreino();
		this.integradoBookingGympass =
			this.configCache.configuracoesIntegracoes.usar_gympass_booking;
		this.validarModalidadeContrato =
			this.configCache.configuracoesAula.validar_modalidade;

		this.setSelects();
		this.textMask.mask = this.localization.getPorcentagemNumberMask();
		this.textMask1.mask = this.localization.getCurrencyMask();

		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
				this.formGroup.get("produtoGymPass").enable();
			}
		});
		this.pactobr = this.session.loggedUser.username.toLowerCase() === "pactobr";
	}

	private loadEntities(id) {
		if (id) {
			this.aulaService.obterAula(id).subscribe((dados) => {
				if (dados) {
					this.aula = dados;
					if (!this.aula.mapaEquipamentos) {
						this.aula.mapaEquipamentos = "";
						this.selectedMaps = "";
					} else {
						this.selectedMaps = this.aula.mapaEquipamentos;
					}
					this.entity = false;
					dados.linkVideos.forEach((link) => {
						this.linkVideos.push({
							id: link.id,
							linkVideo: link.linkVideo,
							professor: link.professor,
						});
					});
					this.loadForm();
					if (this.aula.idClasseGymPass != null) {
						this.idClasseGymPass = this.aula.idClasseGymPass;
						this.cd.detectChanges();
					}
				} else {
					console.log("erro");
				}
			});
		} else {
			this.formGroup.get("tipoReservaEquipamento").setValue("LIVRE");
			this.formGroup.get("capacidade").setValue(0);
			this.formGroup.get("limiteVagasAgregados").setValue(0);
			this.changedCapacidade();
			this.cd.detectChanges();
		}
	}

	private getTimeNumber(timeString: string): number {
		const hours = parseInt(timeString.substring(0, 3), 10);
		const minutes = parseInt(timeString.substring(3, 5), 10);

		return hours * 60 + minutes;
	}

	private getEndTime(timeString: string, minutosAdicionar: string): string {
		const hours = parseInt(timeString.substring(0, 3), 10);
		const minutes = parseInt(timeString.substring(3, 5), 10);

		const minutosTotal = hours * 60 + minutes;
		const timeFinal = minutosTotal + this.getTimeNumber(minutosAdicionar);

		const horasFinal = (timeFinal / 60).toFixed(0);
		const minutosFInal = timeFinal % 60;

		const horasFormated =
			horasFinal.length === 1 ? "0" + horasFinal : horasFinal.toString();
		const minutosFormated =
			minutosFInal < 10 ? "0" + minutosFInal : minutosFInal.toString();

		return timeString + " - " + horasFormated + ":" + minutosFormated;
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.aula.nome);
		this.formGroup.get("ocupacao").setValue(this.aula.ocupacao);
		const regex = new RegExp(this.aula.modalidade.id, "i");
		if (this.modalidades.find((item) => regex.test(item.id)) !== undefined) {
			this.formGroup.get("modalidadeId").setValue(this.aula.modalidade.id);
		}
		this.formGroup.get("professorId").setValue(this.aula.professor.id);
		this.formGroup.get("ambienteId").setValue(this.aula.ambiente.id);
		this.formGroup.get("toleranciaMin").setValue(this.aula.toleranciaMin);
		this.formGroup.get("tipoTolerancia").setValue(this.aula.tipoTolerancia);
		this.formGroup.get("dataInicio").setValue(this.aula.dataInicio);
		this.formGroup.get("dataFinal").setValue(this.aula.dataFinal);
		this.formGroup.get("capacidade").setValue(this.aula.capacidade);
		this.formGroup
			.get("limiteVagasAgregados")
			.setValue(this.aula.limiteVagasAgregados);
		this.formGroup.get("meta").setValue(this.aula.meta);
		this.formGroup.get("pontuacaoBonus").setValue(this.aula.pontuacaoBonus);
		this.formGroup.get("bonificacao").setValue(this.aula.bonificacao);
		this.formGroup.get("mensagem").setValue(this.aula.mensagem);
		this.formGroup.get("urlVideoYoutube").setValue(this.aula.urlVideoYoutube);
		this.formGroup
			.get("validarRestricoesMarcacao")
			.setValue(this.aula.validarRestricoesMarcacao);
		this.formGroup
			.get("visualizarProdutosGympass")
			.setValue(this.aula.visualizarProdutosGympass);
		this.formGroup
			.get("visualizarProdutosTotalpass")
			.setValue(this.aula.visualizarProdutosTotalpass);
		this.formGroup.get("permiteFixar").setValue(this.aula.permiteFixar);
		this.formGroup
			.get("aulaIntegracaoSelfloops")
			.setValue(this.aula.aulaIntegracaoSelfloops);
		this.formGroup.get("idadeMaximaAnos").setValue(this.aula.idadeMaximaAnos);
		this.formGroup.get("idadeMaximaMeses").setValue(this.aula.idadeMaximaMeses);
		this.formGroup.get("idadeMinimaAnos").setValue(this.aula.idadeMinimaAnos);
		this.formGroup.get("idadeMinimaMeses").setValue(this.aula.idadeMinimaMeses);
		this.formGroup.get("niveis").setValue(this.aula.niveis);
		if (this.formGroup.get("naoValidarModalidadeContrato") != null) {
			this.formGroup
				.get("naoValidarModalidadeContrato")
				.setValue(this.aula.naoValidarModalidadeContrato);
		}
		this.formGroup
			.get("produtoGymPass")
			.setValue(this.aula.produtoGymPass === 0 ? "" : this.aula.produtoGymPass);
		this.formGroup.get("urlTurmaVirtual").setValue(this.aula.urlTurmaVirtual);
		this.horarios = this.aula.horarios;
		if (this.aula.imageUrl) {
			this.images.push(this.obterImagem());
		}
		if (
			this.aula.tipoReservaEquipamento !== "LIVRE" &&
			this.aula.tipoReservaEquipamento !== undefined &&
			this.aula.tipoReservaEquipamento !== null
		) {
			this.formGroup
				.get("tipoReservaEquipamento")
				.setValue(this.aula.tipoReservaEquipamento);
		} else {
			this.formGroup.get("tipoReservaEquipamento").setValue("LIVRE");
		}
		this.showMapaEquipamentos =
			this.formGroup.get("tipoReservaEquipamento").value !== "LIVRE";
		this.changedCapacidade();
		this.cd.detectChanges();
		this.loadDiasSemana();
	}

	private obterImagem() {
		const img = {
			uri: this.aula.imageUrl,
		};
		return img;
	}

	private loadDiasSemana() {
		const diasSemana = this.formGroup.get("diasSemana").value;

		for (const dia in diasSemana) {
			if (Object.prototype.hasOwnProperty.call(diasSemana, dia)) {
				for (const diaSelecionado of this.aula.diasSemana) {
					if (dia === diaSelecionado) {
						this.formGroup.get("diasSemana." + dia).setValue(true);
					}
				}
			}
		}
	}
	private configuracoesTreino() {
		const configuracoes = this.treinoConfigService.configuracoesManutencao;
		this.showAbasAppPersonalizado = configuracoes.aplicativo_personalizado;
		this.aplicativo_personalizado_nome =
			configuracoes.aplicativo_personalizado_nome;
	}

	private setModalidades() {
		this.modalidadeService.obterTodasModalidades().subscribe((dados) => {
			this.modalidades = dados.content;
		});
	}
	private setAmbientes() {
		this.ambienteService.obterTodosAmbientes().subscribe((dados) => {
			this.ambientes = dados.content;
		});
	}

	private setSelects() {
		this.modalidadeService.obterTodasModalidades().subscribe((dados) => {
			this.modalidades = dados.content;
			if (this.aula && this.aula.modalidade) {
				const regex = new RegExp(this.aula.modalidade.id, "i");
				if (
					this.modalidades.find((item) => regex.test(item.id)) !== undefined
				) {
					this.formGroup.get("modalidadeId").setValue(this.aula.modalidade.id);
				}
			}
			this.cd.detectChanges();
		});

		this.colaboradorService
			.obterTodosColaboradoresAptosAAula()
			.subscribe((dados) => {
				this.professores = dados.content;
				this.cd.detectChanges();
			});

		this.ambienteService.obterTodosAmbientes().subscribe((dados) => {
			this.ambientes = dados.content;
			this.cd.detectChanges();
		});

		this.aulaService.obterProdutosGympass().subscribe({
			error: (error) => {
				this.utilizarSelectProdutoGympass = false;
				console.log(
					"Erro ao consultar produtos gympass: ",
					error.error.meta.message
				);
				// this.snotifyService.error('Não foi possível carregar os produtos gympass');
			},
			next: (dados) => {
				this.povoarProdutoGymPass(dados);
			},
		});

		this.aparelhoService.obterAparelhosParaReservaEquipamentos().subscribe({
			error: (error) => {
				this.snotifyService.error(error.error.meta.message);
				this.aparelhosReservaEquipamento = [];
				this.cd.detectChanges();
			},
			next: (dados) => {
				this.povoarAparelhosReservaEquipamento(dados);
			},
		});
	}

	private povoarProdutoGymPass(produtos) {
		this.productsGymPass.push({ id: undefined, nome: "" });
		for (const produto of produtos) {
			this.productsGymPass.push({
				id: produto.id,
				nome: produto.nome + " (Código: " + produto.id + ")",
			});
		}
		this.cd.detectChanges();
	}

	private povoarAparelhosReservaEquipamento(aparelhos) {
		this.aparelhosReservaEquipamento.push({
			id: null,
			nome: "",
			sigla: "",
			icone: "",
			usarEmReservaEquipamentos: true,
			sensorSelfloops: null,
		});

		aparelhos.forEach((aparelho) => {
			this.aparelhosReservaEquipamento.push({
				id: aparelho.id,
				nome: aparelho.nome,
				sigla: aparelho.sigla,
				icone: aparelho.icone,
				usarEmReservaEquipamentos: aparelho.usarEmReservaEquipamentos,
				sensorSelfloops: aparelho.sensorSelfloops,
			});
		});

		this.cd.detectChanges();
	}

	addHorarioHandler(index?: number) {
		if (this.controlHorario.value) {
			this.controlHorario.setValue(
				this.controlHorario.value.replace(/_/g, "0")
			);
			const horario: any = {};

			horario.inicio = this.controlHorario.value.substring(0, 5).trim();
			horario.fim = this.controlHorario.value.substring(8).trim();

			if (this.horarioJaCadastrado(horario.inicio, horario.fim, index)) {
				this.notificationService.error(
					this.notificacoesTranslate.getLabel("validacaoHorarioCadastrado")
				);
			} else if (
				horario.inicio !== horario.fim &&
				this.validarHorario(horario.inicio, horario.fim)
			) {
				if (index !== undefined) {
					this.horarios[index] = horario;
				} else {
					this.horarios.push(horario);
				}
			} else {
				this.notificationService.error(
					this.notificacoesTranslate.getLabel("validacaoHorario")
				);
			}

			this.controlHorario.reset("");
		}
	}

	private horarioJaCadastrado(
		hrInicio: string,
		hrFim: string,
		index?: number
	): boolean {
		const duplicado = this.horarios.some(
			(horario, i) =>
				i !== index &&
				horario.inicio.trim() === hrInicio.trim() &&
				horario.fim.trim() === hrFim.trim()
		);
		return duplicado;
	}

	private validarHorario(hrInicio, hrFim) {
		const primeiroDigitoHorarioInicial = parseInt(hrInicio.substring(0, 1), 10);
		const segundoDigitoHorarioInicial = parseInt(hrInicio.substring(1, 2), 10);

		const primeiroDigitoHorarioFinal = parseInt(hrFim.substring(0, 1), 10);
		const segundoDigitoHorarioFinal = parseInt(hrFim.substring(1, 2), 10);

		if (
			(primeiroDigitoHorarioInicial === 2 && segundoDigitoHorarioInicial > 3) ||
			(primeiroDigitoHorarioFinal === 2 && segundoDigitoHorarioFinal > 3)
		) {
			return false;
		} else if (
			primeiroDigitoHorarioInicial > primeiroDigitoHorarioFinal ||
			(primeiroDigitoHorarioInicial === primeiroDigitoHorarioFinal &&
				segundoDigitoHorarioInicial > segundoDigitoHorarioFinal)
		) {
			return false;
		} else {
			return true;
		}
	}

	removeHorarioHandler(index) {
		this.horarios.splice(index, 1);
	}

	btnClickHandlerAmbiente() {
		const modalRef = this.modal.open(AmbienteEditModalComponent);
		modalRef.componentInstance.formGroup.get("nome").setValue("");
		modalRef.componentInstance.formGroup.get("capacidade").setValue("");
		modalRef.result.then((result) => {
			this.ambienteService
				.criarAmbiente(result)
				.subscribe((result1: Ambiente) => {
					this.snotifyService.success(
						this.notificacoesTranslate.getLabel("successCreate")
					);
					this.formGroup.get("ambienteId").setValue(result1.id);
					this.setAmbientes();
				});
		});
	}

	btnClickHandlerModalidade() {
		const modalref = this.modal.open(ModalidadeEditModalComponent);
		modalref.componentInstance.formControl.setValue("");
		modalref.componentInstance.selectedColor = null;
		modalref.result.then((result) => {
			this.modalidadeService
				.criarModalidade(result)
				.subscribe((result1: Modalidade) => {
					this.snotifyService.success(
						this.notificacoesTranslate.getLabel("mensagemCreateSuccess")
					);
					this.formGroup.get("modalidadeId").setValue(result1.id);
					this.setModalidades();
				});
		});
	}

	openLog() {
		const transform = this.datepipe.transform(Date.now(), "ddMMyyyy");
		window.open(
			this.rest.buildFullUrl(
				`gympass/${this.session.chave}/horarios/${this.session.empresaId}/${transform}?idClasse=${this.aula.id}`,
				true
			),
			"_blank"
		);
	}

	submitHandler() {
		this.markAsTouched();
		if (!this.entity) {
			this.editHandler();
		} else {
			this.createHandler();
			this.session.notificarRecursoEmpresa(
				RecursoSistema.CRIOU_AULACOLETIVA_NTO
			);
		}
	}

	markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("modalidadeId").markAsTouched();
		this.formGroup.get("professorId").markAsTouched();
		this.formGroup.get("ambienteId").markAsTouched();
		this.formGroup.get("toleranciaMin").markAsTouched();
		this.formGroup.get("tipoTolerancia").markAsTouched();
		this.formGroup.get("dataInicio").markAsTouched();
		this.formGroup.get("dataFinal").markAsTouched();
		this.formGroup.get("capacidade").markAsTouched();
		this.formGroup.get("limiteVagasAgregados").markAsTouched();
		this.formGroup.get("diasSemana").markAsTouched();
	}

	private createHandler() {
		if (this.formGroup.valid) {
			this.aulaEdit = this.formGroup.getRawValue();
			this.aulaEdit.diasSemana = this.createListaDiasSemana();
			this.aulaEdit.horarios = this.horarios;
			if (this.formGroupVideosUri.get("linkVideo").value !== "") {
				this.linkVideos.push({
					linkVideo: this.formGroupVideosUri.get("linkVideo").value,
					professor: this.formGroupVideosUri.get("professor").value,
				});
			}
			this.aulaEdit.linkVideos = this.linkVideos;

			if (this.selectedMaps && this.selectedMaps.startsWith(";")) {
				this.selectedMaps = this.selectedMaps.slice(1);
			}
			if (this.formGroup.get("tipoReservaEquipamento").value === "LIVRE") {
				this.aula.mapaEquipamentos = "";
				this.selectedMaps = "";
				this.listaTurmaMapaEquipamentoAparelho = [];
			}
			this.aulaEdit.turmaMapaEquipamentoAparelho =
				this.listaTurmaMapaEquipamentoAparelho;
			this.aulaEdit.mapaEquipamentos = this.aula.mapaEquipamentos;

			if (
				!this.aulaEdit.limiteVagasAgregados ||
				this.aulaEdit.limiteVagasAgregados < 0
			) {
				this.aulaEdit.limiteVagasAgregados = 0;
			}

			this.addImage();
			if (this.validarCamposPreenchidosCorretamente()) {
				this.salvando = true;
				this.aulaService.criarAula(this.aulaEdit).subscribe((result) => {
					this.salvando = false;
					if (result.retorno) {
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("createSuccess")
						);
						this.cancelHandler();
						this.agendaStateService.forceLoad$.next(true);
					} else {
						this.notificationService.error(
							this.notificacoesTranslate.getLabel(result.erro)
						);
					}
				});
			}
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	private editHandler() {
		if (this.formGroup.valid) {
			this.aulaEdit = this.formGroup.getRawValue();
			this.aulaEdit.diasSemana = this.createListaDiasSemana();
			this.aulaEdit.horarios = this.horarios;
			this.aulaEdit.idClasseGymPass = this.aula.idClasseGymPass;

			if (this.selectedMaps && this.selectedMaps.startsWith(";")) {
				this.selectedMaps = this.selectedMaps.slice(1);
			}
			if (this.formGroup.get("tipoReservaEquipamento").value === "LIVRE") {
				this.aula.mapaEquipamentos = "";
				this.selectedMaps = "";
				this.listaTurmaMapaEquipamentoAparelho = [];
			}
			this.aulaEdit.turmaMapaEquipamentoAparelho =
				this.listaTurmaMapaEquipamentoAparelho;
			this.aulaEdit.mapaEquipamentos = this.aula.mapaEquipamentos;

			if (this.formGroupVideosUri.get("linkVideo").value !== "") {
				this.linkVideos.push({
					linkVideo: this.formGroupVideosUri.get("linkVideo").value,
					professor: this.formGroupVideosUri.get("professor").value,
				});
			}

			if (
				!this.aulaEdit.limiteVagasAgregados ||
				this.aulaEdit.limiteVagasAgregados < 0
			) {
				this.aulaEdit.limiteVagasAgregados = 0;
			}

			this.aulaEdit.linkVideos = this.linkVideos;
			this.addImage();
			if (this.validarCamposPreenchidosCorretamente()) {
				this.aulaEdit.manterFotoAnterior = this.manterFoto;
				this.aulaService
					.atualizarAula(this.aula.id, this.aulaEdit)
					.subscribe((result) => {
						if (result.retorno) {
							this.notificationService.success(
								this.notificacoesTranslate.getLabel("editSuccess")
							);
							this.cancelHandler();
							this.agendaStateService.forceLoad$.next(true);
						} else if (result.erro === "erro_aluno_matriculado") {
							this.configSnotify.html = `<div class="snotifyToast__body notify-error">${result.message}</div>
                         <div class="snotify-icon snotify-icon--error"></div>`;
							this.notificationService.error(
								result.message,
								this.configSnotify
							);
						} else {
							this.notificationService.error(
								this.notificacoesTranslate.getLabel(result.erro)
							);
						}
					});
			}
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	private addImage() {
		this.images.forEach((img) => {
			this.aulaEdit.image = img.data;
			this.aulaEdit.imageUrl = img.uri;
		});
	}

	private validarCamposPreenchidosCorretamente(): boolean {
		let valid = true;
		if (this.aulaEdit.diasSemana.length === 0) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("validacaoDiaSemana")
			);
			valid = false;
		}
		if (this.aulaEdit.horarios.length === 0) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("inserirHorario")
			);
			valid = false;
		}
		if (this.aulaEdit.dataInicio > this.aulaEdit.dataFinal) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("validacaoData")
			);
			valid = false;
		}

		const listEquipamentos =
			this.selectedMaps === "" ? [] : this.selectedMaps.split(";");
		if (
			this.formGroup.get("tipoReservaEquipamento").value !== "LIVRE" &&
			listEquipamentos.length < this.getLimiteReservaEquipamento
		) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("validacaoQtEquipamentosReserva")
			);
			valid = false;
		}

		return valid;
	}

	private createListaDiasSemana() {
		const diasSemana = this.formGroup.get("diasSemana").value;
		const diasEscolhidos: Array<string> = [];
		for (const dia in diasSemana) {
			if (diasSemana[dia]) {
				diasEscolhidos.push(dia);
			}
		}
		return diasEscolhidos;
	}

	cancelHandler() {
		this.router.navigate(["agenda", "aula"]);
	}
	private exibirBotaoRemover() {
		return !isNullOrUndefinedOrEmpty(this.aula.produtoGymPass);
	}

	removerProdutoGymPass() {
		const id = this.aula.id === 0 ? "" : this.aula.id;
		const gympass = this.aula.produtoGymPass;
		const dialogRef = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmação",
		});
		dialogRef.componentInstance.texto = `Tem certeza que deseja remover esta aula da Gympass ?`;
		dialogRef.componentInstance.textoAlerta = `Caso execute esta ação os alunos já marcados terão suas aulas canceladas e o mesmo sumirá do aplicativo da Gympass`;
		dialogRef.componentInstance.titulo = `Remover aula na Gympass`;
		dialogRef.componentInstance.actionLabel = `Remover`;
		if (dialogRef.result) {
			dialogRef.result
				.then((excluir) => {
					if (excluir) {
						this.aulaService.removerProduto(id).subscribe((response) => {
							this.formGroup.get("produtoGymPass").setValue(undefined);
							this.aula.produtoGymPass = undefined;
							this.idClasseGymPass = undefined;
							this.notificationService.success("Código Gympass excluido");
						});
					}
				})
				.catch((error) => {});
		}
		this.cd.detectChanges();
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/aula/${this.aula.id}`);
	}

	adicionarImagemHandler() {
		const tamanhoMaxImg = 1024 * 1024 * 1; // 1MB
		const modal = this.modal.open(SeletorImagemAvancadoComponent, {
			size: "lg",
		});
		modal.componentInstance.apresentarCatalogo = false;
		modal.result.then((image: ImagemConfig) => {
			this.imgAulaDataUrl = "";
			if (this.isImageGif(image)) {
				this.snotifyService.error(
					"Formato da imagem não suportado. Por favor, selecione uma imagem JPG ou PNG."
				);
			} else {
				if (this.isTamanhoImagemPermitido(image, tamanhoMaxImg)) {
					this.handleUploadImage(image);
				} else {
					this.obterDataUrlImagem(image)
						.then(() => {
							this.comprimirImagem(tamanhoMaxImg);
							this.handleUploadImage(image);
						})
						.catch((error) => {
							this.handleUploadImage(image);
						});
				}
			}
		});
	}

	obterDataUrlImagem(image: ImagemConfig): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			const file: File = image.data;
			const reader = new FileReader();
			reader.onload = () => {
				this.imgAulaDataUrl = reader.result as string;
				resolve();
			};
			reader.onerror = (error) => reject(error);
			if (file) {
				reader.readAsDataURL(file);
			}
		});
	}

	comprimirImagem(tamanhoMaxImg) {
		console.log(
			"Tamanho da imagem antes de ser comprimida: ",
			this.imgAulaDataUrl.length
		);
		// DOC: https://github.com/dfa1234/ngx-image-compress
		// Devido versão do angular, foi necessário utilizar outra versão mas com os mesmos comandos do link anterior:
		// https://www.npmjs.com/package/ngx-image-compress-legacy
		if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
			this.imageCompress
				.compressFile(this.imgAulaDataUrl, 1, 50, 50)
				.then((result: DataUrl) => {
					this.imgAulaDataUrl = result;
					if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
						this.comprimirImagem(tamanhoMaxImg);
					}
				});
		}
	}

	private isImageGif(image: ImagemConfig) {
		const regex: RegExp = /\.gif$/i;
		return regex.test(image.nome);
	}

	private isTamanhoImagemPermitido(image: ImagemConfig, tamanhoMax: number) {
		const file: File = image.data;
		if (file) {
			if (file.size > tamanhoMax) {
				return false;
			}
		}
		return true;
	}

	private handleUploadImage(image: ImagemConfig) {
		this.loading = true;
		this.images = [];
		const reader: FileReader = new FileReader();
		reader.onload = (event: any) => {
			if (this.imgAulaDataUrl !== undefined && this.imgAulaDataUrl !== "") {
				console.log(
					"Tamanho da imagem após ser comprimida: ",
					this.imgAulaDataUrl.length
				);
				image.uri = this.imgAulaDataUrl;
				image.data = this.imgAulaDataUrl.split(",")[1];
			} else {
				image.uri = event.target.result;
				const resultString: string = reader.result as string;
				image.data = resultString.split(",")[1];
			}
			this.cd.detectChanges();
		};
		reader.readAsDataURL(image.data);
		this.images.push(image);
		this.loading = false;
	}

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	removeImageHandle(index) {
		this.images.splice(index, 1);
		this.manterFoto = false;
	}

	get acaoHabilitada() {
		return this.formGroupVideosUri.get("linkVideo").value;
	}

	adicionarHandler() {
		this.linkVideos.push({
			linkVideo: this.formGroupVideosUri.get("linkVideo").value,
			professor: this.formGroupVideosUri.get("professor").value,
		});
		this.formGroupVideosUri.get("linkVideo").setValue("");
		this.formGroupVideosUri.get("professor").setValue(false);
	}

	deletButon(index) {
		this.linkVideos.splice(index, 1);
	}

	checkCheckBoxProfessor(objeto) {
		if (objeto.professor) {
			objeto.professor = false;
		} else {
			objeto.professor = true;
		}
	}

	handleSelectedMaps(selectedMaps) {
		this.mapaReservaEquipaementoSelecionados = selectedMaps;
		const listaPosicoes = this.mapaReservaEquipaementoSelecionados
			.map((item) => item.posicaoMapa)
			.filter(Boolean)
			.join(";");

		this.selectedMaps = listaPosicoes;
		this.aula.mapaEquipamentos = listaPosicoes;
		this.aula.listaMapaEquipamentoAparelho =
			this.mapaReservaEquipaementoSelecionados;
		this.listaTurmaMapaEquipamentoAparelho =
			this.transformarModelMapParaTurmaMapaEquipamentoAparelho(
				this.mapaReservaEquipaementoSelecionados
			);
	}

	transformarModelMapParaTurmaMapaEquipamentoAparelho(
		modelMaps: ModelMap[]
	): TurmaMapaEquipamentoAparelho[] {
		const agrupadosPorAparelhoId = new Map<number, string[]>();
		for (const model of modelMaps) {
			if (
				model.aparelhoId !== undefined &&
				model.posicaoMapa &&
				model.aparelhoId > 0
			) {
				if (!agrupadosPorAparelhoId.has(model.aparelhoId)) {
					agrupadosPorAparelhoId.set(model.aparelhoId, []);
				}
				agrupadosPorAparelhoId.get(model.aparelhoId).push(model.posicaoMapa);
			}
		}
		const resultado: TurmaMapaEquipamentoAparelho[] = [];
		agrupadosPorAparelhoId.forEach((posicoes, aparelhoId) => {
			resultado.push({
				codigo_aparelhotreino: aparelhoId,
				mapaequipamento: posicoes.join(";"),
			});
		});

		return resultado;
	}

	changeSelectReservaEquipamento() {
		this.showMapaEquipamentos =
			this.formGroup.get("tipoReservaEquipamento").value !== "LIVRE";
		this.cd.detectChanges();
	}

	changedCapacidade() {
		this.equipamentos = [
			{ id: "LIVRE", nome: "Livre" },
			{ id: "CAPACIDADE_AULA", nome: "Mapa de equipamentos" },
		];
		this.validarValorDigitadoVagasAgregados();
		this.cd.detectChanges();
	}

	get getLimiteReservaEquipamento() {
		let limite = 0;
		if (
			this.formGroup.get("tipoReservaEquipamento").value === "CAPACIDADE_AULA"
		) {
			limite = Number(this.formGroup.get("capacidade").value);
		}
		return limite;
	}

	validarValorDigitadoVagasAgregados() {
		let limiteVagas = this.formGroup.get("limiteVagasAgregados").value;
		if (
			limiteVagas !== null &&
			limiteVagas !== undefined &&
			Number(limiteVagas) > 0
		) {
			limiteVagas = limiteVagas.toString().replace(".", "");
			limiteVagas = limiteVagas.replace(",", "");
			limiteVagas = Number(limiteVagas);
		} else {
			limiteVagas = 0;
		}

		let capacidade = this.formGroup.get("capacidade").value;
		if (
			capacidade !== null &&
			capacidade !== undefined &&
			Number(capacidade) > 0
		) {
			capacidade = capacidade.toString().replace(".", "");
			capacidade = capacidade.replace(",", "");
			capacidade = Number(capacidade);
		} else {
			capacidade = 0;
		}

		if (limiteVagas > capacidade) {
			this.formGroup.get("limiteVagasAgregados").setValue(capacidade);
		}
	}
}
