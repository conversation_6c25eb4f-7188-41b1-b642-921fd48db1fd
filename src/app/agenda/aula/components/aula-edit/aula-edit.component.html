<ng-template #cardTitle>
	<span *ngIf="entity" i18n="@@crud-aulas:criar-aula:title">Cria<PERSON>la</span>
	<span *ngIf="!entity" i18n="@@crud-aulas:editar-aula:title">Editar Aula</span>
</ng-template>

<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'AULAS',
			menuLink: ['agenda', 'aula']
		}"></pacto-breadcrumbs>

	<pacto-title-card [title]="cardTitle">
		<div class="row">
			<div class="col-md-12">
				<pacto-input
					[id]="'nome-aula-input'"
					[name]="'nome'"
					label="Nome"
					i18n-label="@@crud-aulas:input:nome:label"
					placeholder="Informe o nome da aula"
					i18np-placeholder="@@crud-aulas:input:nome:placeholder"
					mensagem="Definir um nome com pelo menos 3 caracteres."
					i18n-mensagem="@@crud-aulas:input:nome:mensagem"
					[control]="formGroup.get('nome')"></pacto-input>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-select
					[id]="'modalidade-select'"
					[nome]="'modalidade'"
					label="Modalidade"
					i18n-label="@@crud-aulas:select:modalidade:label"
					mensagem="Selecione uma modalidade."
					i18n-mensagem="@@crud-aulas:select:modalidade:mensagem"
					[opcoes]="modalidades"
					[control]="formGroup.get('modalidadeId')"></pacto-select>
			</div>
			<div class="col-md-1" *ngIf="!integracaoZw">
				<button
					class="btn btn-sm btn-secondary btn-cadastros-position"
					id="btn-add-modalidade"
					i18n="@@buttons:adicionar"
					(click)="btnClickHandlerModalidade()">
					Adicionar
				</button>
			</div>
			<div class="col-md-6">
				<pacto-cat-multi-select-filter-number
					[control]="formGroup.get('niveis')"
					[id]="'niveis-aula'"
					[maximo]="20"
					i18n-label="@@agenda-aulas:niveisAula"
					[endpointUrl]="_rest.buildFullUrl('niveis')"
					[labelKey]="'nome'"
					[label]="'Níveis'"
					[resposeParser]="responseParser"
					[placeholder]="''"></pacto-cat-multi-select-filter-number>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-select
					[id]="'professor-select'"
					[nome]="'professor'"
					label="Professor"
					i18n-label="@@crud-aulas:select:professor:label"
					mensagem="Selecione um professor."
					i18n-mensagem="@@crud-aulas:select:professor:mensagem"
					[opcoes]="professores"
					[control]="formGroup.get('professorId')"></pacto-select>
			</div>
			<div class="col-md-6">
				<pacto-select
					[id]="'ambiente-select'"
					[nome]="'ambiente'"
					label="Ambiente"
					i18n-label="@@crud-aulas:select:ambiente:label"
					mensagem="Selecione um ambiente."
					i18n-mensagem="@@crud-aulas:select:ambiente:mensagem"
					[opcoes]="ambientes"
					[control]="formGroup.get('ambienteId')"></pacto-select>
			</div>
			<div class="col-md-1">
				<button
					*ngIf="!integracaoZw"
					class="btn btn-sm btn-secondary btn-cadastros-position"
					id="btn-add-ambiente"
					i18n="@@buttons:adicionar"
					(click)="btnClickHandlerAmbiente()">
					Adicionar
				</button>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<div class="row">
					<div class="col-md-12">
						<label
							i18n="@@crud-aulas:input:tolerancia:label"
							class="control-label">
							Tolerância para o aluno realizar check in
						</label>
					</div>
					<div class="col-md-1">Até</div>
					<div class="col-md-4">
						<pacto-input-number
							[id]="'tolerancia-aula-input'"
							mensagem="Informe o tempo de tolerância."
							[maxLength]="5"
							[control]="formGroup.get('toleranciaMin')"></pacto-input-number>
					</div>
					<div class="col-md-2" *ngIf="integracaoZw">minutos</div>
					<div class="col-md-5">
						<pacto-select
							mensagem="Informe o tipo de tolerância."
							i18n-mensagem="@@crud-aulas:select:professor:tipotolerancia"
							[id]="'tipotolerancia-select'"
							[nome]="'tipotolerancia'"
							[opcoes]="tiposTolerancia"
							[control]="formGroup.get('tipoTolerancia')"></pacto-select>
					</div>
				</div>
			</div>
			<div class="col-md-3">
				<pacto-input-number
					[id]="'capacidade-aula-input'"
					label="Capacidade*"
					i18n-label="@@crud-aulas:input:capacidade:label"
					[maxLength]="9"
					[control]="formGroup.get('capacidade')"
					[allowDecimal]="false"
					(change)="changedCapacidade()"></pacto-input-number>
			</div>

			<div class="col-md-3">
				<div>
					<label class="control-label">Limite agregados</label>
					<i
						class="pct pct-info info-reserva-equipamento"
						ngbTooltip="Limite o agendamento de aulas por agregadores (WellHub e Totalpass). Preencha o campo com a quantidade de vagas desejada ou deixe 0 caso não queira aplicar a limitação."></i>
				</div>
				<pacto-input-number
					[id]="'limite-vagas-agregados-aula-input'"
					i18n-label="@@crud-aulas:input:limite-vagas-agregados:label"
					[control]="formGroup.get('limiteVagasAgregados')"
					[allowDecimal]="false"
					(input)="validarValorDigitadoVagasAgregados()"></pacto-input-number>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<div class="form-group">
					<label class="control-label" i18n="@@crud-aulas:dias:label">
						Dias
					</label>
					<div class="row">
						<div class="col-md-6">
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.domingo')"
									type="checkbox"
									value="domingo"
									id="domingo-check" />
								<label
									i18n="@@crud-aulas:dia-semana:domingo"
									class="form-check-label"
									for="domingo-check">
									Domingo
								</label>
							</div>
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.segunda')"
									type="checkbox"
									value="segunda"
									id="segunda-check" />
								<label
									i18n="@@crud-aulas:dia-semana:segunda"
									class="form-check-label"
									for="segunda-check">
									Segunda
								</label>
							</div>
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.terca')"
									type="checkbox"
									value="terca"
									id="terca-check" />
								<label
									i18n="@@crud-aulas:dia-semana:terca"
									class="form-check-label"
									for="terca-check">
									Terça
								</label>
							</div>
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.quarta')"
									type="checkbox"
									value="quarta"
									id="quarta-check" />
								<label
									i18n="@@crud-aulas:dia-semana:quarta"
									class="form-check-label"
									for="quarta-check">
									Quarta
								</label>
							</div>
						</div>
						<div class="col-md-6">
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.quinta')"
									type="checkbox"
									value="quinta"
									id="quinta-check" />
								<label
									i18n="@@crud-aulas:dia-semana:quinta"
									class="form-check-label"
									for="quinta-check">
									Quinta
								</label>
							</div>
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.sexta')"
									type="checkbox"
									value="sexta"
									id="sexta-check" />
								<label
									i18n="@@crud-aulas:dia-semana:sexta"
									class="form-check-label"
									for="sexta-check">
									Sexta
								</label>
							</div>
							<div class="form-check">
								<input
									class="form-check-input"
									[formControl]="formGroup.get('diasSemana.sabado')"
									type="checkbox"
									value="sabado"
									id="sabado-check" />
								<label
									i18n="@@crud-aulas:dia-semana:sabado"
									class="form-check-label"
									for="sabado-check">
									Sábado
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-12"></div>
			<div class="col-md-12">
				<div class="input-row">
					<div class="form-group">
						<pacto-input
							#listaHorarios
							[id]="'horario-aula-input'"
							label="Horários"
							i18n-label="@@crud-aulas:horarios:title"
							[control]="controlHorario"
							[textMask]="{ mask: timerMask, guide: true }"
							[name]="'horario'"
							placeholder="__:__ - __:__"
							(keydown.enter)="addHorarioHandler()"></pacto-input>
					</div>
					<button
						class="btn btn-sm btn-secondary btn-position"
						(click)="addHorarioHandler()"
						id="btn-add-horario"
						i18n="@@buttons:adicionar">
						Adicionar
					</button>
				</div>
				<table class="table" *ngIf="horarios.length > 0">
					<thead>
						<tr>
							<th>
								<span i18n="@@crud-aulas:table:inicio:title:edit">Início</span>
							</th>
							<th>
								<span i18n="@@crud-aulas:table:final:title">Final</span>
							</th>
							<th>
								<span i18n="@@crud-aulas:table:acoes:title:edit">Ações</span>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr *ngFor="let horario of horarios; let index = index">
							<td>{{ horario.inicio }}</td>
							<td>{{ horario.fim }}</td>
							<td class="action-column" (click)="removeHorarioHandler(index)">
								<i class="fa fa-trash-o"></i>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="row">
			<div class="col-md-3">
				<pacto-datepicker
					[id]="'data-inicio'"
					label="Data de início"
					i18n-label="@@crud-aulas:data-inicio"
					[required]="true"
					[control]="formGroup.get('dataInicio')"></pacto-datepicker>
			</div>
			<div class="col-md-3">
				<pacto-datepicker
					[id]="'data-fim'"
					label="Data final"
					i18n-label="@@crud-aulas:data-final"
					[required]="true"
					[control]="formGroup.get('dataFinal')"></pacto-datepicker>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<div>
					<label class="control-label">Reserva de Equipamento</label>
					<i
						class="pct pct-info info-reserva-equipamento"
						ngbTooltip="Deixe com a opção 'Livre' caso sua aula não tenha equipamentos ou não tenha necessidade de reservas de equipamentos."></i>
				</div>
				<pacto-select
					[id]="'reserva-equipamento'"
					[nome]="'reserva-equipamento'"
					i18n-label="@@crud-aulas:select:reserva-equipamento:label"
					mensagem="Selecione um equipamento."
					i18n-mensagem="@@crud-aulas:select:reserva-equipamento:mensagem"
					[opcoes]="equipamentos"
					[control]="formGroup.get('tipoReservaEquipamento')"
					(change)="changeSelectReservaEquipamento()"></pacto-select>
			</div>
			<div class="col-md-12" *ngIf="showMapaEquipamentos">
				<label class="control-label">Mapa dos equipamentos</label>
				<div class="box-info">
					<i class="pct pct-info"></i>
					<p>
						Selecione a posição dos equipamentos no ambiente em relação à
						posição do professor. Caso nenhuma marcação estiver selecionada, a
						posição dos equipamentos ficará à livre escolha.
					</p>
				</div>

				<map-selection
					[title]="'Professor(a)'"
					[listEquipamentosSelecionados]="aula.mapaEquipamentos"
					[aparelhos]="aparelhosReservaEquipamento"
					[listaMapaEquipamentoAparelho]="aula.listaMapaEquipamentoAparelho"
					[limiteEquipamentos]="getLimiteReservaEquipamento"
					[showBtnUncheckAll]="true"
					(selectedMapsChange)="handleSelectedMaps($event)"></map-selection>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-input
					[id]="'mensagem-aula-input'"
					label="Mensagem"
					i18n-label="@@crud-aulas:input:mensagem:label"
					placeholder="Informe uma mensagem para ser impressa na ficha do aluno."
					[maxLength]="50"
					i18n-placeholder="@@crud-aulas:input:mensagem:placeholder"
					[control]="formGroup.get('mensagem')"></pacto-input>
			</div>
			<div class="col-md-6" *ngIf="integracaoZw">
				<div class="row">
					<div class="check-marcacao">
						<pacto-cat-checkbox
							[title]="
								'Com essa opção marcada o sistema irá contabilizar check-in para a aula, essa configuração só se aplica á planos com controle de check-in'
							"
							[id]="'validar-marcacao-check'"
							[label]="'Controlar check-in'"
							[control]="
								formGroup.get('validarRestricoesMarcacao')
							"></pacto-cat-checkbox>
					</div>
					<div class="check-moda-contrato" *ngIf="validarModalidadeContrato">
						<pacto-cat-checkbox
							[title]="
								'Com essa opção marcada o sistema não irá considerar a configuração (Validar Modalidade). Ex: Se você fizer um aulão, e não quer restrição por modalidade.'
							"
							[id]="'nao-validar-modalidade-contrato'"
							label="Não validar modalidade do contrato"
							i18n-label="@@crud-aulas:checkbox:nao-validar-modalidade-contrato"
							[control]="
								formGroup.get('naoValidarModalidadeContrato')
							"></pacto-cat-checkbox>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-md-3">
				<pacto-cat-form-input-number
					[label]="'Idade mínima (anos)'"
					[formControl]="formGroup.get('idadeMinimaAnos')"
					maxlength="3"
					max="100"
					[decimalPrecision]="0"
					decimal="false"></pacto-cat-form-input-number>
			</div>
			<div class="col-md-3">
				<pacto-cat-form-input-number
					[label]="'(meses)'"
					[formControl]="formGroup.get('idadeMinimaMeses')"
					maxlength="3"
					max="100"
					[decimalPrecision]="0"
					decimal="false"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-3">
				<pacto-cat-form-input-number
					[label]="'Idade máxima (anos)'"
					[formControl]="formGroup.get('idadeMaximaAnos')"
					maxlength="3"
					max="100"
					[decimalPrecision]="0"
					decimal="false"></pacto-cat-form-input-number>
			</div>
			<div class="col-md-3">
				<pacto-cat-form-input-number
					[label]="'(meses)'"
					[formControl]="formGroup.get('idadeMaximaMeses')"
					maxlength="3"
					max="100"
					[decimalPrecision]="0"
					decimal="false"></pacto-cat-form-input-number>
			</div>
		</div>
		<div class="row row-meta-container">
			<div class="col-md-3">
				<pacto-cat-form-input-number
					[label]="'Meta de ocupação (%)'"
					[formControl]="formGroup.get('meta')"
					maxlength="3"
					max="100"
					[decimalPrecision]="0"
					decimal="false"></pacto-cat-form-input-number>
			</div>
			<div class="col-md-3">
				<pacto-cat-form-input-number
					[label]="'Bonificação para meta atingida (R$)'"
					[formControl]="formGroup.get('bonificacao')"
					maxlength="6"
					max="9999"
					decimal="true"></pacto-cat-form-input-number>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-6">
				<pacto-input
					[id]="'input-link'"
					label="Video (link no YouTube)"
					placeholder="link"
					[control]="formGroupVideosUri.get('linkVideo')"></pacto-input>
			</div>
			<div class="col-lg-6">
				<div class="form-link-video">
					<span [ds3Tooltip]="tooltipProfessor" [tooltipPosition]="'top'">
						<input
							class="form-check-input"
							[formControl]="formGroupVideosUri.get('professor')"
							type="checkbox"
							value=""
							id="checkProfessor" />
						<label
							i18n="@@crud-atividade:ativa:label"
							class="form-check-label"
							for="checkProfessor">
							Professor?
						</label>
					</span>

					<div class="btn-adicionar-link-video">
						<button
							id="id-adicionar-alterar-link-video"
							class="pct pct-plus btn btn-sm btn-primary"
							(click)="adicionarHandler()"
							i18n="@@crud-atividade:imagem:button"
							[disabled]="!acaoHabilitada">
							<label>Adicionar</label>
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="row" *ngIf="linkVideos.length > 0">
			<div class="col-lg-6">
				<div class="list-wrapper">
					<table class="table">
						<thead>
							<tr>
								<th>Link</th>
								<th class="table-item">Professor</th>
								<th class="table-item">Excluir</th>
							</tr>
						</thead>
						<tbody>
							<tr *ngFor="let link of linkVideos; let index = index">
								<td>{{ link.linkVideo }}</td>
								<td class="table-item">
									<input
										class="form-check-input"
										[checked]="link.professor"
										type="checkbox"
										(click)="checkCheckBoxProfessor(link)"
										[pactoCatTolltip]="tooltipProfessor"
										[position]="'bottom'" />
								</td>
								<td class="table-item">
									<i class="fa fa-trash-o" (click)="deletButon(index)"></i>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>

		<div class="check-aula-disponivel">
			<pacto-cat-checkbox
				[title]="
					'Ao marcar esta configuração, os alunos TotalPass que realizaram check-in na sua academia poderão marcar aulas pelo App ' +
					(showAbasAppPersonalizado
						? aplicativo_personalizado_nome + ' ou Agenda'
						: ' Treino ou Agenda')
				"
				[id]="'aula-disponivel-check'"
				[label]="'Aula disponível para alunos TotalPass?'"
				[control]="
					formGroup.get('visualizarProdutosTotalpass')
				"></pacto-cat-checkbox>
		</div>
		<div class="check-aula-disponivel">
			<pacto-cat-checkbox
				[title]="
					'Ao marcar esta configuração, os alunos WellHub que realizaram check-in na sua academia poderão marcar aulas pelo App ' +
					(showAbasAppPersonalizado
						? aplicativo_personalizado_nome + ' ou Agenda'
						: ' Treino ou Agenda')
				"
				[id]="'aula-disponivel-check'"
				[label]="'Aula disponível para alunos WellHub?'"
				[control]="
					formGroup.get('visualizarProdutosGympass')
				"></pacto-cat-checkbox>
		</div>
		<div class="check-aula-disponivel">
			<pacto-cat-checkbox
				[title]="
					'Ao marcar esta configuração, o sistema permitirá que os alunos sejam fixados na aula, até o fim do seu contrato ou até determinada data.'
				"
				[id]="'fixar-aula-check'"
				[label]="'Permite a fixação de alunos?'"
				[control]="formGroup.get('permiteFixar')"></pacto-cat-checkbox>
		</div>
		<div class="check-aula-disponivel">
			<pacto-cat-checkbox
				[title]="
					'Ao marcar esta configuração, o sistema irá enviar para a integração selfloops os dados dos desta aula e alunos que forem incluidos nela.'
				"
				[id]="'aula-selfloops'"
				[label]="'Enviar aula para integração selfloops?'"
				[control]="
					formGroup.get('aulaIntegracaoSelfloops')
				"></pacto-cat-checkbox>
		</div>
		<div class="row" *ngIf="integradoBookingGympass">
			<div
				class="col-md-6"
				title="Verificar com a WellHub o código do produto a ser utilizado.">
				<ng-container *ngIf="utilizarSelectProdutoGympass">
					<pacto-select
						[id]="'produto-gympass-select'"
						[nome]="'Produto WellHub'"
						label="Produto WellHub"
						i18n-label="@@crud-aulas:select:produto-gympass:label"
						mensagem="Selecione um produto."
						i18n-mensagem="@@crud-aulas:select:produto-gympass:mensagem"
						[opcoes]="productsGymPass"
						[control]="formGroup.get('produtoGymPass')"></pacto-select>
				</ng-container>
				<ng-container *ngIf="!utilizarSelectProdutoGympass">
					<pacto-input-number
						[id]="'produto-gympass-input'"
						label="Produto WellHub"
						i18n-label="@@crud-aulas:input:produto:label"
						[control]="formGroup.get('produtoGymPass')"
						[allowDecimal]="true"></pacto-input-number>
				</ng-container>
			</div>
			<div class="idGymPass-label" *ngIf="idClasseGymPass">
				{{ "( Id na WellHub: " + idClasseGymPass + ")" }}

				<pacto-cat-button
					*ngIf="exibirBotaoRemover()"
					id="btn-aulas-na-gympass"
					label="Remover produto"
					i18n-label="@crud-aula:gympass"
					[type]="'OUTLINE'"
					(click)="removerProdutoGymPass()"
					icon="pct pct-trash"></pacto-cat-button>
			</div>
			<div class="col-md-6" title="URL da aula virtual.">
				<pacto-input
					[id]="'url-turma-input'"
					label="URL turma virtual"
					i18n-label="@@crud-aulas:input:url:label"
					[control]="formGroup.get('urlTurmaVirtual')"></pacto-input>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-12">
				<div class="form-group images-wrapper">
					<div class="header-row">
						<label class="control-label" i18n="@@crud-atividade:imagem:label">
							Imagem
						</label>
						<button
							id="btnAddImagem"
							class="btn btn-sm btn-secondary"
							(click)="adicionarImagemHandler()"
							i18n="@@crud-atividade:imagem:button">
							Adicionar Imagem
						</button>
					</div>
					<div class="image-list">
						<div
							class="imagem"
							*ngFor="let imagem of images; let index = index">
							<div class="imagem-wrapper">
								<img src="{{ imagem.uri }}" />
							</div>
							<div class="control-footer">
								<div class="name">
									{{ imagem.nome }}
								</div>
								<div class="icon">
									<i
										_ngcontent-c22=""
										class="fa fa-trash-o"
										(click)="removeImageHandle(index)"></i>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="actions">
			<button
				class="btn btn-primary"
				i18n="@@buttons:salvar"
				(click)="submitHandler()"
				id="btn-add-aula"
				[disabled]="salvando">
				Salvar
			</button>
			<button
				*ngIf="pactobr && idClasseGymPass"
				class="btn btn-secondary"
				(click)="openLog()">
				Horários na WellHub
			</button>
			<button
				class="btn btn-secondary"
				i18n="@@buttons:cancelar"
				(click)="cancelHandler()">
				Cancelar
			</button>
			<pacto-log *ngIf="aula?.id" [url]="urlLog"></pacto-log>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="createSuccess" i18n="@@crud-aulas:create-success">
		Aula criada com sucesso.
	</span>
	<span xingling="editSuccess" i18n="@@crud-aulas:create-edit">
		Aula editada com sucesso.
	</span>
	<span xingling="campoObrigatorio" i18n="@@crud-aulas:campo-obrigatorio">
		Campos obrigatórios não preenchido.
	</span>
	<span
		xingling="inserirHorario"
		i18n="@@crud-aulas:campo-obrigatorio:inserir-horario">
		Inserir pelo menos um horário na lista
	</span>
	<span xingling="validacaoData" i18n="@@crud-aulas:validacao:data">
		Data inicial não pode ser superior a data final
	</span>
	<span
		xingling="validacaoQtEquipamentosReserva"
		i18n="@@crud-aulas:validacao:qtEquipamentosReserva">
		A quantidade mínima de equipamentos para reserva ainda não foi atingida,
		mínimo: {{ getLimiteReservaEquipamento }}
	</span>
	<span xingling="validacaoHorario" i18n="@@crud-aulas:validacao:horario">
		Horário cadastrado invalido, tente novamente
	</span>
	<span
		xingling="validacaoHorarioCadastrado"
		i18n="@@crud-aulas:validacao:horario">
		Horário já cadastrado
	</span>
	<span xingling="validacaoDiaSemana" i18n="@@crud-aulas:validacao:dia semana">
		Selecione ao menos um dia da semana.
	</span>
	<span xingling="horarionaoPodeSerRemovido">
		Não é possível excluir o horário, contém aula confirmada.
	</span>
	<span xingling="erro_aula_url_virtual">Informe a URL da aula virtual.</span>
	<span xingling="erro_incluir_aula" i18n="@@crud-aulas:erro_incluir_aula">
		Ocorreu um erro ao incluir a aula, tente novamente em alguns instantes!
	</span>
	<span i18n="@@crud-ambientes:success:create" xingling="successCreate">
		Ambiente criado com sucesso.
	</span>
	<span
		i18n="@@crud-modalidade:create-modal:success"
		xingling="mensagemCreateSuccess">
		Modalidade criada com sucesso.
	</span>
</pacto-traducoes-xingling>
<ng-template #tooltipProfessor>
	<div class="itens-tooltip">
		Caso esteja marcado, esta mídia aparecerá no aplicativo apenas para o
		professor
	</div>
</ng-template>
