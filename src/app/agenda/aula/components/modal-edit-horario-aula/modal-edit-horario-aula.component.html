<div class="main-content">
	<div class="line-prof-inativo" *ngIf="validaProfInativo()">
		<span class="pct pct-alert-triangle line-danger-text-adjust"></span>
		O professor se encontra inativo, por favor realize a troca de professor
	</div>

	<div class="row">
		<div class="col-md-4">
			<div class="margin-top-15">
				<ds3-form-field>
					<ds3-field-label>Professor*</ds3-field-label>
					<ds3-select
						[id]="'modalidade-select'"
						[formControl]="formGroup.get('responsavel')"
						[options]="selectProfessores"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>

			<div class="margin-top-15">
				<ds3-form-field>
					<ds3-field-label>Ambiente*</ds3-field-label>
					<ds3-select
						[id]="'ambiente-select'"
						[formControl]="formGroup.get('ambiente')"
						[options]="selectAmbientes"
						ds3Input></ds3-select>
				</ds3-form-field>
			</div>

			<div class="margin-top-15">
				<ds3-form-field>
					<ds3-field-label>Número máximo de alunos*</ds3-field-label>
					<input
						[id]="'capacidade-horario'"
						[formControl]="formGroup.get('capacidade')"
						(change)="validarValorDigitadoVagasAgregados()"
						ds3Input
						type="number"
						placeholder="0" />
				</ds3-form-field>
			</div>

			<div class="margin-top-15">
				<ds3-form-field>
					<ds3-field-label>
						Limite agregados*
						<i
							class="pct pct-info info-reserva-equipamento"
							ngbTooltip="Limite o agendamento de aulas por agregadores (Wellhub e Totalpass). Preencha o campo com a quantidade de vagas desejada ou deixe 0 caso não queira aplicar a limitação."></i>
					</ds3-field-label>
					<input
						[id]="'limiete-agregados-horario'"
						ds3Input
						[formControl]="formGroup.get('limiteVagasAgregados')"
						(input)="validarValorDigitadoVagasAgregados()"
						type="number"
						placeholder="0" />
				</ds3-form-field>
			</div>

			<div class="margin-top-15">
				<ds3-form-field>
					<ds3-field-label>
						Limite alunos experimentais*
						<i
							class="pct pct-info info-reserva-equipamento"
							ngbTooltip="Limite máximo de alunos experimentais permitidos para esta aula. Preencha o campo com a quantidade de vagas desejada ou deixe 0 caso não queira aplicar a limitação."></i>
					</ds3-field-label>
					<input
						[id]="'limiete-agregados-horario'"
						ds3Input
						[formControl]="formGroup.get('qtdeMaximaAlunoExperimental')"
						(input)="validarValorDigitadoVagasAgregados()"
						type="number"
						placeholder="0" />
				</ds3-form-field>
			</div>
		</div>

		<div class="col-md-8">
			<div class="row">
				<div class="col-md-12 margin-top-15">
					<ds3-field-label *ngIf="podeSelecionarMaisDeUmDia">
						Dias da semana*
					</ds3-field-label>

					<div class="row" *ngIf="podeSelecionarMaisDeUmDia">
						<div class="col-12 row-dias">
							<ds3-form-field class="item-dia">
								<ds3-checkbox
									[id]="'dia-aula-seg-check'"
									[formControl]="formGroup.get('seg')"
									[title]="'Segunda'"
									(click)="validarTodosDiasSemanaMarcados('seg')">
									Seg
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="item-dia">
								<ds3-checkbox
									[id]="'dia-aula-ter-check'"
									[formControl]="formGroup.get('ter')"
									[title]="'Terça'"
									(click)="validarTodosDiasSemanaMarcados('ter')">
									Ter
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="item-dia">
								<ds3-checkbox
									[id]="'dia-aula-qua-check'"
									[formControl]="formGroup.get('qua')"
									[title]="'Quarta'"
									(click)="validarTodosDiasSemanaMarcados('qua')">
									Qua
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="item-dia">
								<ds3-checkbox
									[id]="'dia-aula-qui-check'"
									[formControl]="formGroup.get('qui')"
									[title]="'Quinta'"
									(click)="validarTodosDiasSemanaMarcados('qui')">
									Qui
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="item-dia">
								<ds3-checkbox
									[id]="'dia-aula-sex-check'"
									[formControl]="formGroup.get('sex')"
									[title]="'Sexta'"
									(click)="validarTodosDiasSemanaMarcados('sex')">
									Sex
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="item-dia">
								<ds3-checkbox
									[id]="'dia-aula-sab-check'"
									[formControl]="formGroup.get('sab')"
									[title]="'Sábado'"
									(click)="validarTodosDiasSemanaMarcados('sab')">
									Sab
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="m-dia">
								<ds3-checkbox
									[id]="'dia-aula-dom-check'"
									[formControl]="formGroup.get('dom')"
									[title]="'Domingo'"
									(click)="validarTodosDiasSemanaMarcados('dom')">
									Dom
								</ds3-checkbox>
							</ds3-form-field>

							<ds3-form-field class="margin-left-28">
								<ds3-checkbox
									[id]="'todos-dias-aula-check'"
									[formControl]="formGroup.get('todosDias')"
									[title]="'Todos os dias'"
									(click)="marcarTodosDiasSemana()">
									Todos os dias
								</ds3-checkbox>
							</ds3-form-field>
						</div>
					</div>
				</div>

				<div class="col-md-12" style="margin-top: 22.5px">
					<div class="row">
						<ds3-form-field class="col-md-4">
							<ds3-field-label>Horário inicial*</ds3-field-label>
							<input
								[textMask]="{ mask: timerMask, guide: true }"
								ds3Input
								[formControl]="formGroup.get('horaInicio')"
								placeholder="00h00"
								required />
							<i class="pct pct-clock" ds3Suffix></i>
						</ds3-form-field>

						<!-- TODO: não permitir que seja digitado horario -->
						<ds3-form-field class="col-md-4">
							<ds3-field-label>Horário final*</ds3-field-label>
							<input
								[textMask]="{ mask: timerMask, guide: true }"
								ds3Input
								[formControl]="formGroup.get('horaFim')"
								placeholder="00h00"
								required
								(input)="
									formGroup
										.get('horaFim')
										.setValue(validarHoraFim($event.target.value))
								" />
							<i class="pct pct-clock" ds3Suffix></i>
						</ds3-form-field>

						<button
							(click)="adicionarListaHorarios()"
							[disabled]="isBtnAddHorarioDisabled()"
							ds3-text-button
							class="margin-top-37">
							Adicionar horários
						</button>
					</div>
				</div>

				<div class="col-md-12 margin-top-15">
					<div class="top10-border-top">
						<pacto-relatorio
							#tableData
							[table]="table"
							class="tableHours"
							[showShare]="false"
							(iconClick)="actionClickHandler($event)"
							actionTitulo="Ação"
							[filterConfig]="filterConfig"></pacto-relatorio>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- se existem alunos futuros no horario pode apenas visualizar e não editar -->
	<div class="row justify-content-end btns-ativar-inativar">
		<button
			class="btn btn-primary"
			*ngIf="!apenasVisualizacao"
			[disabled]="addHorario"
			(click)="concluirEdicaoHorariosAula()">
			Salvar dias e horários
		</button>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="createSuccess" i18n="@@crud-horarios:create-success">
		Horario criado com sucesso.
	</span>
</pacto-traducoes-xingling>
