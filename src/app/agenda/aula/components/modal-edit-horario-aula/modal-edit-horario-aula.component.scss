@import "src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/import";

@media (min-width: 1050px) {
	::ng-deep .modal-integracao .modal-lg {
		max-width: 1050px;
	}
}

pacto-cat-form-input,
pacto-cat-form-input-number {
	margin: 0px;
}

.main-content {
	padding: 27px 68px 27px 68px;
}

.btns-ativar-inativar {
	margin-top: 38px;
}

::ng-deep.modal-time-line {
	.modal-dialog {
		max-width: 78%;
	}
}

.margin-left-13 {
	margin-left: 13px;
}

.margin-left-3 {
	margin-left: 3px;
}

.margin-left-28 {
	margin-left: 28px;
}

.margin-left-45 {
	margin-left: 45px;
}

.todosDiasRight {
	flex: 1 0 16.66666667%;
	text-align: right;
}

.top10-border-top {
	//margin-left: 16px;
	border-top: 3px solid #dcdddf;
}

.tableHours {
	display: inline-block;
	width: 627px;
	margin-left: -30px !important;
}

.right5 {
	margin-right: 5px;
}

.top20 {
	margin-top: 20px;
}

.margin-top-40 {
	margin-top: 40px;
}

.margin-top-15 {
	margin-top: 15px;
}

.margin-top-10 {
	margin-top: 10px;
}

.col-md-3-adjust {
	flex: 0 0 25%;
	max-width: 20%;
	margin-left: 15px;
}

.col-md-9-adjust {
	flex: 0 0 75%;
	max-width: 64%;
	margin-left: 15px;
}

.margin-top-37 {
	margin-top: 37px;
	margin-left: 10px;
}

.labelDefault {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
	font-family: "Nunito Sans", sans-serif;
}

.cadastrar-ambiente {
	color: #0a64ff;
	font-size: 14px;
	font-weight: bold;
}

.line-danger-text-adjust {
	margin-left: 10px;
	margin-right: 5px;
}

.line-prof-inativo {
	background-color: #fdfdb4;
	height: 40px;
	line-height: 43px;
	margin-bottom: 15px;
}

.coluna-botao {
	width: 400px;
	padding-top: 19px;
	text-align: right;
	padding-right: 10px;
}

.btn-primary {
	height: 40px;
	color: $branco;
	font-weight: bold;
	background-color: $azul !important;
	border: 1px solid $azul !important;
	font-size: 14px;
}

::ng-deep .current-value {
	max-height: 42px !important;
}

.row-dias {
	display: flex;
	margin-top: 8px;

	.item-dia {
		margin-right: 10px;
	}
}

//.row-dias {
//	display: flex;
//	margin-top: 8px;
//
//	.item-dia {
//		margin-right: 10px;
//	}
//}
