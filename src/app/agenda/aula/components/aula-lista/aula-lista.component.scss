@import "src/assets/scss/pacto/plataforma-import.scss";

.sort-icon {
	padding-left: 10px;
}

.actions-row {
	width: 100px;
	text-align: center;

	i {
		cursor: pointer;
		padding-right: 10px;
	}
}

.order {
	cursor: pointer;

	.button-direction {
		display: flex;

		.title-table {
			bottom: 0px;
			left: 0px;
		}

		.icon-order {
			.fa-caret-up {
				display: block;
				height: 10px;
			}

			.fa-caret-down {
				display: block;
				height: 10px;
			}
		}
	}
}

.fa-exclamation-triangle {
	color: $pequizaoPri;
}

.loading-state,
.empty-state {
	text-align: center;
	padding-top: 60px;
	height: 150px;
}

.logs-gympass {
	margin-top: -80px;
	padding: 0 30px;
	height: 50px;
	display: block;

	pacto-cat-button {
		margin-right: 10px;
	}
}

.logs-gympass-sem-aula {
	width: 350px;
	height: 50px;
	display: block;

	pacto-cat-button {
		margin-right: 10px;
	}
}

.preview-object {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	border: 1px solid #c7c7c7;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}

.table-wrapper::ng-deep {
	.pct-copy {
		color: $azul;
		margin-right: 6px !important;
		margin-left: 6px !important;
	}
	.pct-edit {
		color: $azul;
		margin-right: 16px !important;
		margin-left: 16px !important;
	}
	.pct-trash-2 {
		color: #fa1e1e;
		margin-right: 6px !important;
		margin-left: 6px !important;
	}
}

.container-programa {
	display: grid;
	padding: 18px;

	.aula-lista {
		display: block;

		.grid-aula {
			display: grid;
			grid-template-columns: 1.4fr 0.8fr 1.5fr 1fr 1.4fr;
		}

		min-height: 110px;
		margin-bottom: 16px;
		padding: 16px;
		border: 1px solid $cinza03;
		border-radius: 8px;
		padding-right: 0px;

		.column-dados {
			min-height: 76px;
			display: grid;
			color: $preto05;
			font-size: 14px;
			font-weight: 400;
			line-height: 18px;

			.aula {
				font-size: 14px;
				font-weight: bold;
				margin-top: -10px;
			}

			.nome {
				font-size: 14px;
				text-transform: capitalize;
				line-height: 22px;
				color: $cinza05;
			}

			.codigo {
				color: $cinza05;
			}

			.titulo {
				font-size: 16px;
			}
		}

		.dados-pessoais {
			.column-dados {
				margin-left: 16px;
				padding: 12px;
			}

			display: flex;

			.column-foto {
				padding-top: 7px;

				.foto-prescricao {
					width: 64px;
					height: 64px;
					border-radius: 32px;
					border: 1px solid #c7c7c7;
					background-repeat: no-repeat;
					background-position: center;
					background-size: cover;
				}
			}
		}

		.coluna-botao {
			width: 400px;
			padding-top: 19px;
			text-align: right;
			padding-right: 10px;

			.revisar {
				margin-right: 10px;
			}
		}
	}

	.btn-secundary {
		color: $azulim05;
		border: 1px solid $azulim05;
		background-color: $branco;
		height: 40px;
		width: 100px;
		font-size: 14px;
		margin-right: 10px;
		font-weight: bold;
	}

	.btn-primary {
		height: 40px;
		color: $branco;
		font-weight: bold;
		background-color: $azul !important;
		border: 1px solid $azul !important;
		width: 100px;
		font-size: 14px;
	}

	.btn-excluir {
		color: #fa1e1e;
		border: 1px solid $branco;
		background-color: $branco;
		height: 40px;
		width: 100px;
		font-size: 14px;
		margin-right: 10px;
		font-weight: bold;
	}
}

.filtros {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1.3fr;
	padding: 24px 16px 16px 16px;

	.filtro1 {
		padding-right: 10%;
	}

	.search {
		position: relative;
		flex-grow: 1;
		margin-left: -15px;

		input {
			width: 70%;
			height: 41px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}

	pacto-cat-select {
		width: 70%;
	}

	.compartilhar {
		text-align: right;
		min-width: 500px;
	}

	::ng-deep {
		button.btn.pacto-primary {
			margin-right: 0px;
		}

		pacto-share-button {
			padding-right: 10px;

			.btn.pacto-primary {
				color: $azulim05;
				background-color: $branco;
				border: 1px solid $azulim05;

				.icon-drop {
					border-left: 1px solid $azulim05;
				}
			}
		}
	}
}

.footer-row {
	position: relative;
	display: flex;
	margin-right: 16px;
	flex-direction: row-reverse;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}

	.div-show-and-select {
		display: flex;

		> * {
			margin-left: 20px;
		}
	}
}

.simple-total-row {
	background-color: #f9f9f9;
	border-top: 1px solid #ededed;
	border-bottom: 1px solid #ededed;
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 30px;
	margin-top: 14px;
	color: #7f7f7f;
}

.filtros-atividade {
	margin-right: 10px;

	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}
}

.footer-row-personalizada {
	display: grid;
	grid-template-columns: 1.4fr 1fr 1fr 2fr;
	padding: 24px 16px 16px 16px;

	.grupo-btn {
		pacto-cat-button {
			padding-right: 10px;
		}
	}

	.footer-row {
		position: relative;
		display: flex;
		margin-right: 16px;
		flex-direction: row-reverse;

		> * {
			display: block;
			margin-left: 20px;
		}

		pacto-select .form-group {
			margin-bottom: 0px;
		}

		.div-show-and-select {
			display: flex;

			> * {
				margin-left: 20px;
			}
		}
	}
}

::ng-deep pacto-log.primary {
	margin-right: -20px;
	height: 40px;

	.btn {
		height: 40px;
		color: $azul;
		padding: 4px 10px;
		margin-right: 8px;
		background: $branco;
		border: 1px solid $azulimPri;
	}

	.btn:hover {
		color: $azul;
		background: $branco;
		border: 1px solid $azul;
	}

	.btn.btn-primary:focus,
	.show > .btn-primary.dropdown-toggle {
		color: $azul;
		background: $branco;
		box-shadow: 0 0 0 -0.2rem rgb(3, 127, 226);
	}

	.btn-primary:focus,
	.btn-primary.focus {
		box-shadow: 0 0 0 0.2rem rgba(3, 127, 226, 0.5);
	}
}
