import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { FormControl, FormGroup } from "@angular/forms";

import { Observable, zip } from "rxjs";
import { debounceTime, map, tap } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	TreinoApiAulaService,
	AulaBase,
	Ambiente,
	Modalidade,
	TreinoApiColaboradorService,
	UsuarioBase,
	TreinoApiAmbienteService,
	TreinoApiModalidadeService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	GridFilterType,
	RelatorioComponent,
	GridFilterConfig,
	FilterComponent,
	PactoDataGridColumnConfig,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { DatePipe } from "@angular/common";
import { DataFiltro } from "../../../../../../projects/ui/src/lib/components/relatorio/data-grid.model";
import { isNullOrUndefinedOrEmpty } from "sdk";
import { NgbDropdown, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmRestService } from "../../../../../../projects/adm/src/app/adm-rest.service";

@Component({
	selector: "pacto-aula-lista",
	templateUrl: "./aula-lista.component.html",
	styleUrls: ["./aula-lista.component.scss"],
})
export class AulaListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("cloneModalTitle", { static: true }) cloneModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("cloneModalBody", { static: true }) cloneModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("tooltipClonar", { static: true }) tooltipClonar;

	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("fimColumnName", { static: true }) fimColumnName;
	@ViewChild("diasSemanaColumnName", { static: true }) diasSemanaColumnName;
	@ViewChild("inicioColumnName", { static: true }) inicioColumnName;
	@ViewChild("ambienteColumnName", { static: true }) ambienteColumnName;
	@ViewChild("professorColumnName", { static: true }) professorColumnName;
	@ViewChild("modalidadeColumnName", { static: true }) modalidadeColumnName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("professorCelula", { static: true }) professorCelula;
	@ViewChild("modalidadeCelula", { static: true }) modalidadeCelula;
	@ViewChild("nomeCelula", { static: true }) nomeCelula;
	@ViewChild("ambienteCelula", { static: true }) ambienteCelula;
	@ViewChild("diasSemanaCelula", { static: true }) diasSemanaCelula;
	@ViewChild("dataInicioCelula", { static: true }) dataInicioCelula;
	@ViewChild("dataFimCelula", { static: true }) dataFimCelula;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("imageCelula", { static: true }) imageCelula;
	@ViewChild("imagemColumnName", { static: true }) imagemColumnName;
	@ViewChild("situacaoColumnName", { static: true }) situacaoColumnName;
	@ViewChild("situacaoTranslator", { static: true }) situacaoTranslator;
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("filtroButton", { static: false }) filtroButton: FilterComponent;

	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();

	baseFilter: DataFiltro = {};
	listaAulas: Array<AulaBase> = [];
	listaLog: Array<any> = [];
	searchControl = new FormControl();
	private filtro;
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	filterConfig: GridFilterConfig = { filters: [] };
	ngbPage = 1;
	pageSizeControl: FormControl = new FormControl();
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];
	private temporaryFilters;
	itemToRemove;
	loading = false;
	nomeAula = "";
	ready = false;
	flagLogGympassClass = false;

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});
	professores: ApiResponseList<UsuarioBase> = {
		content: [],
	};
	ambientes: ApiResponseList<Ambiente> = {
		content: [],
	};
	modalidades: ApiResponseList<Modalidade> = {
		content: [],
	};

	ordenacao: ApiResponseList<Modalidade> = {
		content: [],
	};

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private aulaService: TreinoApiAulaService,
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private ambienteService: TreinoApiAmbienteService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		public sessionService: SessionService,
		private configCache: TreinoConfigCacheService,
		private modalidadeService: TreinoApiModalidadeService,
		public admRestService: AdmRestService
	) {}

	table: PactoDataGridConfig;
	integracaoZW = false;
	pactobr = false;
	integradoBookingGympass: any;

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.pactobr =
			this.sessionService.loggedUser.username.toLowerCase() === "pactobr";
		this.integradoBookingGympass =
			this.configCache.configuracoesIntegracoes.usar_gympass_booking;
		zip(
			this.getAmbientes(),
			this.getProfessor(),
			this.getModalidade()
		).subscribe(() => {
			this.ready = true;
			this.initTable();
			this.initFilter();
			// this.configFilters();
			this.cd.detectChanges();
		});

		// this.configFilters();
		// this.initAll();
		// this.listarAulas();
	}

	private initTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipClonar = this.tooltipClonar.nativeElement.innerHTML;
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.rest.buildFullUrl("aulas"),
				logUrl: this.rest.buildFullUrl("log/aula"),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "id",
						titulo: "Código",
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nome",
						titulo: this.nomeColumnName,
						buscaRapida: true,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "nome",
						celula: this.nomeCelula,
					},
					{
						nome: "modalidade.nome",
						titulo: this.modalidadeColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "modalidade",
						celula: this.modalidadeCelula,
					},
					{
						nome: "dias",
						titulo: this.diasSemanaColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: false,
						defaultVisible: true,
						campo: "dias",
						celula: this.diasSemanaCelula,
					},
					{
						nome: "dataInicio",
						titulo: this.inicioColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "dataInicio",
						celula: this.dataInicioCelula,
						date: true,
					},
					{
						nome: "dataFim",
						titulo: this.fimColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "dataFinal",
						celula: this.dataFimCelula,
						date: true,
					},
				],
				actions: [
					{
						nome: "copy",
						iconClass: "pct pct-copy",
						tooltipText: tooltipClonar,
					},
					{
						nome: "edit",
						iconClass: "pct pct-edit",
						tooltipText: tooltipEditar,
					},
					{
						nome: "remove",
						iconClass: "pct pct-trash-2",
						tooltipText: tooltipInativar,
						showIconFn: (row) => this.isAulaVigente(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	private initFilter() {
		const aulaListaFilterTableString = localStorage.getItem(
			"aulaListaFilterTable"
		);
		const aulaListaFilterTable = aulaListaFilterTableString
			? JSON.parse(aulaListaFilterTableString)
			: null;
		const localStorageFilters = aulaListaFilterTable
			? JSON.parse(aulaListaFilterTable.filters)
			: [];
		if (!this.table) {
			this.table = new PactoDataGridConfig({
				showFilters: true,
			});
		}
		this.filterConfig = {
			filters: [
				{
					name: "vigencia",
					label: "Vigência",
					type: GridFilterType.DS3_SELECT_MANY,
					options: [
						{ value: "VIGENTE", label: "Vigente" },
						{ value: "NAO_VIGENTE", label: "Não vigente" },
					],
					initialValue: localStorageFilters["vigencia"] || ["VIGENTE"],
				},
				{
					name: "gympass",
					label: "Wellhub",
					type: GridFilterType.DS3_SELECT_MANY,
					options: [
						{ value: "INTEGRADO", label: "Integrado" },
						{ value: "NAO_INTEGRADO", label: "Não integrado" },
					],
					initialValue: localStorageFilters["gympass"] || [],
				},
				{
					name: "professor",
					label: "Professor",
					type: GridFilterType.DS3_SELECT_MANY,
					options: this.professores.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["professor"] || [],
				},
				// { caso for utilizar aplicar mesma lógica de filtragem pelo professor
				// 	name: "ambiente",
				// 	label: "Ambiente",
				// 	type: GridFilterType.DS3_SELECT_MANY,
				// 	options: this.ambientes.content.map((item) => ({
				// 		value: item.id,
				// 		label: item.nome,
				// 	})),
				// 	initialValue: localStorageFilters["ambiente"] || [],
				// },
				{
					name: "modalidadeIds",
					label: "Modalidade",
					type: GridFilterType.DS3_SELECT_MANY,
					options: this.modalidades.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["modalidadeIds"] || [],
				},
			],
		};
		this.cd.detectChanges();
	}

	getImagemAula(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	btnClickHandler() {
		/*
		 * tela antiga será mantida para treino independente
		 * pois a estrutura será alterada apenas para quem tem integração com zw
		 */
		if (this.isTreinoIndependente()) {
			this.router.navigate(["agenda", "aula", "adicionar"]);
		} else {
			this.router.navigate(["agenda", "aula", "adicionar-nova-aula"]);
		}
	}

	btnEditHandler(item) {
		/*
		 * tela antiga será mantida para treino independente
		 * pois a estrutura será alterada apenas para quem tem integração com zw
		 */
		if (this.isTreinoIndependente()) {
			this.router.navigate(["agenda", "aula", item.id]);
		} else {
			this.router.navigate(["agenda", "aula", "editar", item.id]);
		}
	}

	isTreinoIndependente() {
		return !this.sessionService.integracaoZW;
	}

	isAulaVigente(aula) {
		if (!isNullOrUndefinedOrEmpty(aula)) {
			this.logGympassClass();
			this.cd.detectChanges();
		}
		if (aula.dataFinal) {
			return aula.dataFinal > new Date().getTime();
		}
		return false;
	}

	logGympassClass() {
		this.flagLogGympassClass = true;
	}

	private configFilters() {
		const aulaListaFilterTableString = localStorage.getItem(
			"aulaListaFilterTable"
		);
		const aulaListaFilterTable = aulaListaFilterTableString
			? JSON.parse(aulaListaFilterTableString)
			: null;
		const localStorageFilters = aulaListaFilterTable
			? JSON.parse(aulaListaFilterTable.filters)
			: [];

		this.filterConfig = {
			filters: [
				{
					name: "vigencia",
					label: "Vigência",
					type: GridFilterType.MANY,
					options: [
						{ value: "VIGENTE", label: "Vigente" },
						{ value: "NAO_VIGENTE", label: "Não vigente" },
					],
					initialValue: localStorageFilters["vigencia"] || ["VIGENTE"],
				},
				{
					name: "gympass",
					label: "Wellhub",
					type: GridFilterType.MANY,
					options: [
						{ value: "INTEGRADO", label: "Integrado" },
						{ value: "NAO_INTEGRADO", label: "Não integrado" },
					],
					initialValue: localStorageFilters["gympass"] || [],
				},
				{
					name: "professor",
					label: "Professor",
					type: GridFilterType.MANY,
					options: this.professores.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["professor"] || [],
				},
				{
					name: "ambiente",
					label: "Ambiente",
					type: GridFilterType.MANY,
					options: this.ambientes.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["ambiente"] || [],
				},
				{
					name: "modalidadeIds",
					label: "Modalidade",
					type: GridFilterType.MANY,
					options: this.modalidades.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["modalidadeIds"] || [],
				},
				{
					name: "ordenacaoAula",
					label: "Ordenação",
					type: GridFilterType.MANY,
					options: [
						{ value: "asc", label: "A|Z" },
						{ value: "desc", label: "Z|A" },
					],
				},
			],
		};
		this.cd.detectChanges();
	}

	private getProfessor(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradores(true)
			.pipe(
				map((dados) => {
					dados.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = dados;
					return true;
				})
			);
		return professores$;
	}

	private getAmbientes(): Observable<any> {
		const ambientes$ = this.ambienteService.obterAmbientes().pipe(
			map((dados) => {
				dados.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientes = dados;
				return true;
			})
		);
		return ambientes$;
	}

	private getModalidade(): Observable<any> {
		return this.modalidadeService.obterTodasModalidades().pipe(
			map((dados) => {
				dados.content.forEach((modalidade: any) => {
					modalidade.value = modalidade.id;
					modalidade.label = modalidade.nome;
				});
				this.modalidades = dados;
				return true;
			})
		);
	}

	removeHandler(item) {
		this.nomeAula = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const modalButton = this.tooltipInativar.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				modalButton
			);
			handler.result.then(() => {
				this.aulaService.removerAula(item.id).subscribe((resultRemove) => {
					if (resultRemove === "aulaTemAlunoNaoExcluir") {
						this.snotifyService.error(removeError);
					} else {
						this.snotifyService.success(removeSuccess);
						// this.listarAulas();
						this.tableData.reloadData();
					}
				});
			});
		});
	}

	logClickHandler() {
		this.router.navigate(["agenda", "aula", "gympass", "log"]);
	}

	verHorarios() {
		const transform = this.datepipe.transform(Date.now(), "ddMMyyyy");
		window.open(
			this.rest.buildFullUrl(
				`gympass/${this.sessionService.chave}/horarios/${this.sessionService.empresaId}/${transform}`,
				true
			),
			"_blank"
		);
	}

	verAulas() {
		window.open(
			this.rest.buildFullUrl(
				`gympass/${this.sessionService.chave}/aulas/${this.sessionService.empresaId}`,
				true
			),
			"_blank"
		);
	}

	get urlLog(): string {
		return this.rest.buildFullUrl("log/aula");
	}

	cloneHandler(item) {
		this.nomeAula = item.nome;
		setTimeout(() => {
			const modalTitle = this.cloneModalTitle.nativeElement.innerHTML;
			const modalBody = this.cloneModalBody.nativeElement.innerHTML;
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				"Clonar"
			);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					this.aulaService.clonarAula(item.id).subscribe({
						error: (error) => {
							this.snotifyService.error(error.error.meta.message);
						},
						next: (dados) => {
							this.snotifyService.success(
								"A aula " + item.nome + " foi clonada com sucesso!"
							);
							// this.listarAulas();
							this.tableData.reloadData();
						},
					});
				})
				.catch(() => {});
		});
	}

	filtrar() {
		this.ngbPage = 1;
		// this.listarAulas();
	}

	initAll() {
		this.pageSizeControl.setValue(5);
		this.data.size = 5;
		this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
			this.filtrar();
		});
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "edit") {
			this.btnEditHandler(event.row);
		} else if (event.iconName === "remove") {
			this.removeHandler(event.row);
		} else if (event.iconName === "copy") {
			this.cloneHandler(event.row);
		}
	}
}
