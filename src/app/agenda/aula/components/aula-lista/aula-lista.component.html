<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'Aula'
		}"
		class="first"></pacto-breadcrumbs>

	<div class="table-wrapper" *ngIf="ready">
		<pacto-relatorio
			#tableData
			(btnAddClick)="btnClickHandler()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="btnEditHandler($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[labelBtnAdd]="'Adicionar'"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			[showTooltipDS3Status]="false"
			actionTitulo="Ações"
			telaId="lista-aula-coletiva"></pacto-relatorio>
	</div>

	<div
		[ngClass]="{
			'logs-gympass': flagLogGympassClass,
			'logs-gympass-sem-aula': !flagLogGympassClass
		}"
		*ngIf="pactobr">
		<pacto-cat-button
			id="btn-log-aula"
			[type]="'OUTLINE'"
			label="Log"
			(click)="logClickHandler()"></pacto-cat-button>

		<pacto-cat-button
			id="btn-aulas-na-gympass"
			label="Aulas na Wellhub"
			i18n-label="@crud-aula:wellhub"
			[type]="'OUTLINE'"
			(click)="verAulas()"></pacto-cat-button>

		<pacto-cat-button
			id="btn-horarios-na-gympass"
			label="Horários na Wellhub"
			i18n-label="@crud-aula:horario-wellhub"
			[type]="'OUTLINE'"
			(click)="verHorarios()"></pacto-cat-button>
	</div>
</pacto-cat-layout-v2>

<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-aulas:input:nome:label">Nome</span>
</ng-template>
<ng-template #professorColumnName>
	<span i18n="@@crud-aulas:select:professor:label">Professor</span>
</ng-template>
<ng-template #modalidadeColumnName>
	<span i18n="@@crud-aulas:select:professor:label">Modalidade</span>
</ng-template>
<ng-template #ambienteColumnName>
	<span i18n="@@crud-aulas:table:ambiente:title">Ambiente</span>
</ng-template>
<ng-template #diasSemanaColumnName>
	<span i18n="@@crud-aulas:table:diasSemana:title">Dias</span>
</ng-template>
<ng-template #inicioColumnName>
	<span i18n="@@crud-aulas:table:inicio:title">Início</span>
</ng-template>
<ng-template #fimColumnName>
	<span i18n="@@crud-aulas:table:fim:title">Fim</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-atividades:table:status">Ações</span>
</ng-template>

<!--label filter-->
<ng-template #modalidadeLabel>
	<span i18n="@@crud-aulas:select:modalidade:label">Modalidade</span>
</ng-template>

<ng-template #imageCelula let-item="item">
	<div
		[ngStyle]="{
			'background-image': getImagemAula(
				item.imageUrl ? item.imageUrl : 'assets/images/pct-aula-cheia-2.svg'
			),
			'background-size': item.imageUrl ? 'cover' : 'auto'
		}"
		class="preview-object"></div>
</ng-template>

<!--Celulas para formatação-->
<ng-template #nomeCelula let-item="item">
	{{ item.nome }}

	<i
		*ngIf="
			integradoBookingGympass &&
			item.produtoGymPass > 0 &&
			item.idClasseGymPass == 0
		"
		class="fa fa-exclamation-triangle"
		title="Esta aula não está sincronizada com a Wellhub. Salve novamente a aula e caso o problema persista, entre em contato com a Wellhub."></i>
</ng-template>
<ng-template #professorCelula let-item="item">
	{{ item.professor?.nome }}
</ng-template>
<ng-template #modalidadeCelula let-item="item">
	{{ item.modalidade?.nome }}
</ng-template>
<ng-template #ambienteCelula let-item="item">
	{{ item.ambiente?.nome }}
</ng-template>
<ng-template #dataInicioCelula let-item="item">
	{{ item.dataInicio | date : "dd/MM/yyyy" }}
</ng-template>
<ng-template #dataFimCelula let-item="item">
	{{ item.dataFinal | date : "dd/MM/yyyy" }}
</ng-template>
<!--fim-->

<!--tooltip icons-->
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipEditar [hidden]="true" i18n="@@crud-alunos:inativar:tooltip-icon">
	Editar
</span>
<span #tooltipClonar [hidden]="true" i18n="@@crud-alunos:clonar:tooltip-icon">
	Clonar
</span>
<!-- FILTER LABELS -->
<ng-template
	#dataInicioLabel
	i18n="@@relatorio-atividade-professores:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template
	#dataFimLabel
	i18n="@@relatorio-atividade-professores:data-fim:filtro">
	Data Fim
</ng-template>

<span #removeModalTitle [hidden]="true" i18n="@@crud-aulas:remove-modal:title">
	Remover Aula ?
</span>
<span #cloneModalTitle [hidden]="true" i18n="@@crud-aulas:clonar-modal:title">
	Clonar a Aula?
</span>
<span #removeModalBody [hidden]="true" i18n="@@crud-aulas:remove-modal:body">
	Deseja remover a aula {{ nomeAula }}?
</span>
<span #cloneModalBody [hidden]="true" i18n="@@crud-aulas:clonar-modal:body">
	Deseja clonar a aula {{ nomeAula }}?
</span>
<span #removeSuccess [hidden]="true" i18n="@@crud-aulas:remove:success">
	Aula removida com sucesso.
</span>
<span #removeError [hidden]="true" i18n="@@crud-aulas:remove:error">
	Não foi possível excluir, existem alunos matriculados
</span>
