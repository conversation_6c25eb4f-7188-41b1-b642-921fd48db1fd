<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'LOGS',
			menuLink: ['agenda', 'aula', 'gympass', 'log']
		}"></pacto-breadcrumbs>

	<div *ngIf="log" class="table-wrapper pacto-shadow">
		<span class="card-title">
			{{ log.logGymPass.aluno }}({{ log.logGymPass.matricula }})
		</span>

		<div>email: {{ log.logGymPass.email }}</div>
		<div>id: {{ log.logGymPass.bookingNumber }}</div>
		<div>
			hora: {{ log.logGymPass.dataRegistro | date : "dd/MM/yyyy HH:mm" }}
		</div>
		<div>turma: {{ log.dados.turma }} ({{ log.dados.idturma }})</div>
		<div>evento: {{ log.logGymPass.evento }}</div>
		<div class="json">
			json:
			<i>{{ log.logGymPass.booking }}</i>
		</div>

		<span class="card-title">ÚLTIMAS AULAS MARCADAS NO SISTEMA PELO ALUNO</span>
		<div *ngFor="let a of log.dados.aulas" class="json">
			<div>turma: {{ a.identificadorturma }}</div>
			<div>bookingid: {{ a.bookingid }}</div>
			<div>dia: {{ a.dia | date : "dd/MM/yyyy" }}</div>
			<div>hora: {{ a.horainicial }}</div>
		</div>

		<span class="card-title">EVENTOS</span>

		<div *ngFor="let l of log.eventos" class="json">
			<div>operacao: {{ l.operacao }}</div>
			<div>clientesintetico: {{ l.clienteSintetico }}</div>
			<div>sucesso: {{ l.sucesso }}</div>
			<div>cancelarBooking: {{ l.cancelarBooking }}</div>
			<div>json: {{ l.booking }}</div>
		</div>

		<span class="card-title">LOGS</span>

		<div *ngFor="let l of log.logs" class="json">
			<div>hora: {{ l.dataRegistro | date : "dd/MM/yyyy HH:mm" }}</div>
			<div>log: {{ l.log }}</div>
		</div>
	</div>
</pacto-cat-layout-v2>
