import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { TreinoApiAulaService } from "treino-api";
import { ActivatedRoute } from "@angular/router";

@Component({
	selector: "pacto-aula-log-gympass-detalhe",
	templateUrl: "./aula-log-gympass-detalhe.component.html",
	styleUrls: ["./aula-log-gympass-detalhe.component.scss"],
})
export class AulaLogGympassDetalheComponent implements OnInit {
	constructor(
		private aulaService: TreinoApiAulaService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef
	) {}

	log: any;

	ngOnInit() {
		this.fetchData();
	}

	fetchData() {
		this.route.params.subscribe((params) => {
			this.aulaService.obterDetalheLogGympass(params.id).subscribe((dados) => {
				this.log = dados;
				this.cd.detectChanges();
			});
		});
	}
}
