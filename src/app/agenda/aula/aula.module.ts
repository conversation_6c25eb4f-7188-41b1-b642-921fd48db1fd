import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { AulaListaComponent } from "./components/aula-lista/aula-lista.component";
import { AulaEditComponent } from "./components/aula-edit/aula-edit.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
	TreinoApiAulaService,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

import { AmbienteEditModalModule } from "../../agenda/ambiente/components/ambiente-edit-modal/ambiente-edit-modal.module";
import { ModalidadeEditModalModule } from "../../agenda/modalidade/components/modalidade-edit-modal/modalidade-edit-modal.module";
import { AulaLogGympassComponent } from "./components/aula-log-gympass/aula-log-gympass.component";
import { AulaLogGympassDetalheComponent } from "./components/aula-log-gympass-detalhe/aula-log-gympass-detalhe.component";
import { CatTolltipModule } from "ui-kit";
import { MapSelectionComponent } from "./components/map-selection/map-selection.component";
import { AulaEditV2Component } from "./components/aula-edit-v2/aula-edit-v2.component";
import { NgxMaskModule } from "ngx-mask";
import { NgxCurrencyModule } from "ngx-currency";
import { ModalEditHorarioAulaComponent } from "./components/modal-edit-horario-aula/modal-edit-horario-aula.component";
import { ModalRemoveEditHorarioAulaComponentComponent } from "./components/modal-remove-edit-horario-aula-component/modal-remove-edit-horario-aula-component.component";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS,
	true
);

const routes: Routes = [
	{
		path: "",
		component: AulaListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "gympass/log",
		component: AulaLogGympassComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "gympass/log/:id",
		component: AulaLogGympassDetalheComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "adicionar", // será mantido devido treino independente
		component: AulaEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "adicionar-nova-aula",
		component: AulaEditV2Component,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: ":id", // será mantido devido treino independente
		component: AulaEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "editar/:id",
		component: AulaEditV2Component,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		AmbienteEditModalModule,
		ModalidadeEditModalModule,
		CatTolltipModule,
		NgxMaskModule,
		NgxCurrencyModule,
	],
	declarations: [
		AulaListaComponent,
		AulaEditComponent,
		AulaLogGympassComponent,
		AulaLogGympassDetalheComponent,
		MapSelectionComponent,
		AulaEditV2Component,
		ModalEditHorarioAulaComponent,
		ModalRemoveEditHorarioAulaComponentComponent,
	],
	entryComponents: [
		ModalEditHorarioAulaComponent,
		ModalRemoveEditHorarioAulaComponentComponent,
	],
	exports: [MapSelectionComponent],
	providers: [TreinoApiAulaService],
})
export class AulaModule {}
