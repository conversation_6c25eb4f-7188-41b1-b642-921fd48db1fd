<div class="main-content">
	<div *ngIf="validaProfInativo()" class="line-prof-inativo">
		<span class="pct pct-alert-triangle line-danger-text-adjust"></span>
		O professor se encontra inativo, por favor realize a troca de professor
	</div>
	<div class="row" style="align-items: baseline">
		<div class="col-md-6 mr-auto">
			<pacto-cat-form-multi-select-filter
				[autocomplete]="'off'"
				[control]="formGroup.get('ambientes')"
				[idKey]="'id'"
				[id]="'ambiente-select'"
				[labelKey]="'nome'"
				[options]="ambientes"
				i18n-label="@@crud-locacao:select:ambientes:label"
				label="Ambiente*"></pacto-cat-form-multi-select-filter>
		</div>
		<div class="col-md-6 mr-auto">
			<pacto-cat-select-filter
				[control]="formGroup.get('responsavel')"
				[endpointUrl]="_rest.buildFullUrlTreino('usuarios/colaboradores')"
				[id]="'responsavel-select'"
				[labelKey]="'nome'"
				[paramBuilder]="paramBuilder"
				i18n-label="@@crud-turma-horarios:select:responsavel:label"
				i18n-mensagem="@@crud-turmas:select:responsavel:mensagem"
				label="Responsável*"
				mensagem="Selecione um responsável."></pacto-cat-select-filter>
		</div>
	</div>

	<div class="row margin-top-40">
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="formGroup.get('horaInicio')"
				[errorMsg]="'Campo obrigatório'"
				[id]="'input-horaInicial'"
				[label]="'Hora inicial*'"
				[placeholder]="'00h00'"
				[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
		</div>

		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="formGroup.get('horaFim')"
				[errorMsg]="'Campo obrigatório'"
				[id]="'input-horaFinal'"
				[label]="'Hora final*'"
				[placeholder]="'00h00'"
				[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
		</div>
	</div>

	<label *ngIf="!variosHorarios" class="margin-top-40 labelDefault">
		Dias da semana*
	</label>
	<div *ngIf="!variosHorarios" class="row">
		<div class="margin-left-10 col-md-1 text">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('seg')"
				[checkBlue]="true"
				[control]="formGroup.get('seg')"
				[label]="'Seg'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('ter')"
				[checkBlue]="true"
				[control]="formGroup.get('ter')"
				[label]="'Ter'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('qua')"
				[checkBlue]="true"
				[control]="formGroup.get('qua')"
				[label]="'Qua'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('qui')"
				[checkBlue]="true"
				[control]="formGroup.get('qui')"
				[label]="'Qui'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('sex')"
				[checkBlue]="true"
				[control]="formGroup.get('sex')"
				[label]="'Sex'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-1">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('sab')"
				[checkBlue]="true"
				[control]="formGroup.get('sab')"
				[label]="'Sab'"></pacto-cat-checkbox>
		</div>
		<div class="margin-left-3 col-md-3">
			<pacto-cat-checkbox
				(click)="validarTodosDiasSemanaMarcados('dom')"
				[checkBlue]="true"
				[control]="formGroup.get('dom')"
				[label]="'Dom'"></pacto-cat-checkbox>
		</div>
		<div class="col-md-2 todosDiasRight">
			<pacto-cat-checkbox
				(click)="marcarTodosDiasSemana()"
				[checkBlue]="true"
				[control]="formGroup.get('todosDias')"
				[label]="'Todos os dias'"></pacto-cat-checkbox>
		</div>
	</div>

	<div class="row top10-border-bottom"></div>

	<div class="row top20">
		<div class="col-md-5 mr-auto">
			<pacto-cat-checkbox
				[checkBlue]="true"
				[control]="formGroup.get('permiteAgendarPeloAppTreino')"
				[label]="'Permitir agendar pelo App Treino'"></pacto-cat-checkbox>
		</div>
	</div>
	<div class="row justify-content-end btns-ativar-inativar">
		<pacto-cat-button
			(click)="salvarHorarioLocacao()"
			i18n-label="@@integracoes:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar horário"
			size="LARGE"
			style="margin-right: 15px; margin-left: 17px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@crud-horarios:create-success" xingling="createSuccess">
		Horario criado com sucesso.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:falha-salvar-integracao"
		xingling="falha-salvar-integracao">
		Falha ao tentar salvar integração!
	</span>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
</pacto-traducoes-xingling>
