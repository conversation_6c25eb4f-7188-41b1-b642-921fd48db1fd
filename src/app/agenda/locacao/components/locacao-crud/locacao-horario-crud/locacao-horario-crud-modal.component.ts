import { ChangeDetectorR<PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SelectFilterResponseParser, TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService } from "adm-core-api";
import { UsuarioBase } from "sdk";
import {
	Ambiente,
	HorarioDisponibilidade,
	TreinoApiTurmaService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";

@Component({
	selector: "pacto-locacao-horario-crud-modal",
	templateUrl: "./locacao-horario-crud-modal.component.html",
	styleUrls: ["./locacao-horario-crud-modal.component.scss"],
})
export class LocacaoHorarioCrudModalComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	timerMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		responsavel: new FormControl(null),
		ambientes: new FormControl(null),
		horaInicio: new FormControl(),
		horaFim: new FormControl(),
		diaSemana: new FormControl(null),
		dom: new FormControl(false),
		seg: new FormControl(false),
		ter: new FormControl(false),
		qua: new FormControl(false),
		qui: new FormControl(false),
		sex: new FormControl(false),
		sab: new FormControl(false),
		todosDias: new FormControl(false),
		permiteAgendarPeloAppTreino: new FormControl(false),
	});
	edicao: boolean = false;
	variosHorarios: boolean = false;

	professores: Array<UsuarioBase>;
	ambientes: Array<Ambiente>;

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private modalService: ModalService,
		private turmaService: TreinoApiTurmaService,
		private notificationService: SnotifyService,
		private rest: RestService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {}

	dto(): any {
		const dto = {
			codigo:
				this.formGroup.get("codigo") && this.formGroup.get("codigo").value
					? this.formGroup.get("codigo").value
					: null,
			responsavel: {
				codigo: this.formGroup.get("responsavel").value.id,
				nome: this.formGroup.get("responsavel").value.nome,
			},
			ambientes: [],
			horaInicio: this.formGroup.get("horaInicio").value,
			horaFim: this.formGroup.get("horaFim").value,
			permiteAgendarPeloAppTreino: this.formGroup.get(
				"permiteAgendarPeloAppTreino"
			).value,
		};
		this.formGroup.get("ambientes").value.forEach((a) => {
			dto.ambientes.push({
				codigo: a.codigo,
				ambiente: a.ambiente
					? { codigo: a.ambiente.id, nome: a.ambiente.nome }
					: { codigo: a.id, nome: a.nome },
			});
		});
		return dto;
	}

	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	validaProfInativo(): boolean {
		if (this.edicao && this.professores) {
			let inativo = true;
			this.professores.forEach((p) => {
				if (p.id === this.formGroup.get("responsavel").value.codigo) {
					inativo = false;
				}
			});
			return inativo;
		} else {
			return false;
		}
	}

	validoAdicioanrHorario(
		horario: HorarioDisponibilidade,
		horarios: HorarioDisponibilidade[]
	): boolean {
		if (horario.codigo && horario.codigo > 0 && horarios.length > 0) {
			return false;
		}
		return true;
	}

	salvarHorarioLocacao() {
		if (
			(this.formGroup.get("responsavel") &&
				(this.formGroup.get("responsavel").value === undefined ||
					this.formGroup.get("responsavel").value === null)) ||
			(this.formGroup.get("ambientes") &&
				(this.formGroup.get("ambientes").value === undefined ||
					this.formGroup.get("ambientes").value === null ||
					this.formGroup.get("ambientes").value.length === 0))
		) {
			this.notificationService.error(
				"Nenhum dos itens de seleção pode estar vazio! (Responsável ou Ambiente)."
			);
		} else if (this.isHorarioNaoPreenchido()) {
			this.notificationService.error(
				"A hora inicial ou hora final não foram preenchidas corretamente."
			);
		} else if (this.isDiaSemanaNaoPreenchido()) {
			this.notificationService.error(
				"É necessário selecionar pelo menos um dia da semana para prosseguir."
			);
		} else {
			const dto = this.dto();
			if (this.variosHorarios) {
				this.openModal.close(dto);
			} else {
				const horarios = Array<HorarioDisponibilidade>();
				if (
					this.formGroup.get("todosDias") &&
					this.formGroup.get("todosDias").value
				) {
					if (dto.codigo && dto.codigo > 0) {
						this.notificationService.error(
							"Para edição individual de horario é inválido marcar 'todos os dias'"
						);
					} else {
						let diaSemanaNumero = 1;
						["DM", "SG", "TR", "QA", "QI", "SX", "SB"].forEach((dia) => {
							const horario = this.dto();

							horario.diaSemanaNumero = diaSemanaNumero;
							horario.diaSemana = dia;
							horarios.push(horario);
							diaSemanaNumero = diaSemanaNumero++;
						});
					}
				} else {
					if (this.formGroup.get("dom") && this.formGroup.get("dom").value) {
						const horarioD = this.dto();
						if (this.validoAdicioanrHorario(horarioD, horarios)) {
							horarioD.diaSemanaNumero = 1;
							horarioD.diaSemana = "DM";
							horarios.push(horarioD);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"DOM será removido."
							);
						}
					}
					if (this.formGroup.get("seg") && this.formGroup.get("seg").value) {
						const horarioS = this.dto();
						if (this.validoAdicioanrHorario(horarioS, horarios)) {
							horarioS.diaSemanaNumero = 2;
							horarioS.diaSemana = "SG";
							horarios.push(horarioS);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"SEG será removido."
							);
						}
					}
					if (this.formGroup.get("ter") && this.formGroup.get("ter").value) {
						const horarioT = this.dto();
						if (this.validoAdicioanrHorario(horarioT, horarios)) {
							horarioT.diaSemanaNumero = 3;
							horarioT.diaSemana = "TR";
							horarios.push(horarioT);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"TER será removido."
							);
						}
					}
					if (this.formGroup.get("qua") && this.formGroup.get("qua").value) {
						const horarioQA = this.dto();
						if (this.validoAdicioanrHorario(horarioQA, horarios)) {
							horarioQA.diaSemanaNumero = 4;
							horarioQA.diaSemana = "QA";
							horarios.push(horarioQA);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"QUA será removido."
							);
						}
					}
					if (this.formGroup.get("qui") && this.formGroup.get("qui").value) {
						const horarioQI = this.dto();
						if (this.validoAdicioanrHorario(horarioQI, horarios)) {
							horarioQI.diaSemanaNumero = 5;
							horarioQI.diaSemana = "QI";
							horarios.push(horarioQI);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"QUI será removido."
							);
						}
					}
					if (this.formGroup.get("sex") && this.formGroup.get("sex").value) {
						const horarioSE = this.dto();
						if (this.validoAdicioanrHorario(horarioSE, horarios)) {
							horarioSE.diaSemanaNumero = 6;
							horarioSE.diaSemana = "SX";
							horarios.push(horarioSE);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"SEX será removido."
							);
						}
					}
					if (this.formGroup.get("sab") && this.formGroup.get("sab").value) {
						const horarioSA = this.dto();
						if (this.validoAdicioanrHorario(horarioSA, horarios)) {
							horarioSA.diaSemanaNumero = 7;
							horarioSA.diaSemana = "SB";
							horarios.push(horarioSA);
						} else {
							this.notificationService.warning(
								"Para edição individual de horario é inválido marcar 'mais de um dia da semana', dia: " +
									"SAB será removido."
							);
						}
					}
				}
				if (horarios.length > 0) {
					this.openModal.close(this.edicao ? horarios[0] : horarios);
					if (!this.edicao) {
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("createSuccess")
						);
					}
				} else {
					this.openModal.close();
				}
			}
		}
	}

	isHorarioNaoPreenchido(): boolean {
		return (
			this.formGroup.get("horaInicio").value == null ||
			this.formGroup.get("horaInicio").value === "" ||
			this.formGroup.get("horaFim").value == null ||
			this.formGroup.get("horaFim").value === "" ||
			this.formGroup.get("horaInicio").value.includes("_") ||
			this.formGroup.get("horaFim").value.includes("_")
		);
	}

	isDiaSemanaNaoPreenchido(): boolean {
		return !(
			this.formGroup.get("dom").value ||
			this.formGroup.get("seg").value ||
			this.formGroup.get("ter").value ||
			this.formGroup.get("qua").value ||
			this.formGroup.get("qui").value ||
			this.formGroup.get("sex").value ||
			this.formGroup.get("sab").value
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	marcarTodosDiasSemana() {
		this.formGroup.get("dom").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("seg").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("ter").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("qua").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("qui").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("sex").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("sab").setValue(!this.formGroup.get("todosDias").value);
	}

	validarTodosDiasSemanaMarcados(dia: string) {
		if (
			("seg" === dia
				? !this.formGroup.get("seg").value
				: this.formGroup.get("seg").value) &&
			("ter" === dia
				? !this.formGroup.get("ter").value
				: this.formGroup.get("ter").value) &&
			("qua" === dia
				? !this.formGroup.get("qua").value
				: this.formGroup.get("qua").value) &&
			("qui" === dia
				? !this.formGroup.get("qui").value
				: this.formGroup.get("qui").value) &&
			("sex" === dia
				? !this.formGroup.get("sex").value
				: this.formGroup.get("sex").value) &&
			("sab" === dia
				? !this.formGroup.get("sab").value
				: this.formGroup.get("sab").value) &&
			("dom" === dia
				? !this.formGroup.get("dom").value
				: this.formGroup.get("dom").value)
		) {
			this.formGroup.get("todosDias").setValue(true);
		} else {
			this.formGroup.get("todosDias").setValue(false);
		}
	}

	get _rest() {
		return this.rest;
	}
}
