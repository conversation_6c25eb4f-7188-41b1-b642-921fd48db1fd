<div class="main-content">
	<div class="line-prof-inativo" *ngIf="validaProfInativo()">
		<span class="pct pct-alert-triangle line-danger-text-adjust"></span>
		O professor se encontra inativo, por favor realize a troca de professor
	</div>
	<div class="row" style="align-items: baseline">
		<div class="col-md-4">
			<pacto-cat-select-filter
				[id]="'responsavel-select'"
				label="Responsável*"
				i18n-label="@@crud-turma-horarios:select:responsavel:label"
				mensagem="Selecione um responsável."
				i18n-mensagem="@@crud-turmas:select:responsavel:mensagem"
				[endpointUrl]="_rest.buildFullUrlTreino('usuarios/colaboradores')"
				[paramBuilder]="paramBuilder"
				[labelKey]="'nome'"
				[ngbTooltip]="'Responsável pela entrega do ambiente ou professor'"
				[control]="formGroup.get('responsavel')"></pacto-cat-select-filter>
		</div>
		<div class="col-md-8">
			<label class="labelDefault" *ngIf="!variosHorarios">
				Dias da semana*
			</label>
			<div class="row" *ngIf="!variosHorarios">
				<div class="margin-left-3 col-md-1 text">
					<pacto-cat-checkbox
						[label]="'Seg'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('seg')"
						[control]="formGroup.get('seg')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-3 col-md-1">
					<pacto-cat-checkbox
						[label]="'Ter'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('ter')"
						[control]="formGroup.get('ter')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-3 col-md-1">
					<pacto-cat-checkbox
						[label]="'Qua'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('qua')"
						[control]="formGroup.get('qua')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-3 col-md-1">
					<pacto-cat-checkbox
						[label]="'Qui'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('qui')"
						[control]="formGroup.get('qui')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-3 col-md-1">
					<pacto-cat-checkbox
						[label]="'Sex'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('sex')"
						[control]="formGroup.get('sex')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-3 col-md-1">
					<pacto-cat-checkbox
						[label]="'Sab'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('sab')"
						[control]="formGroup.get('sab')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-3 col-md-2">
					<pacto-cat-checkbox
						[label]="'Dom'"
						[checkBlue]="true"
						(click)="validarTodosDiasSemanaMarcados('dom')"
						[control]="formGroup.get('dom')"></pacto-cat-checkbox>
				</div>
				<div class="margin-left-28 col-md-3">
					<pacto-cat-checkbox
						[label]="'Todos os dias'"
						[checkBlue]="true"
						(click)="marcarTodosDiasSemana()"
						[control]="formGroup.get('todosDias')"></pacto-cat-checkbox>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-4">
			<pacto-cat-form-multi-select-filter
				[id]="'ambiente-select'"
				label="Ambiente*"
				i18n-label="@@crud-locacao:select:ambientes:label"
				[options]="ambientes"
				[idKey]="'id'"
				[labelKey]="'nome'"
				[control]="formGroup.get('ambientes')"
				[autocomplete]="'off'"></pacto-cat-form-multi-select-filter>
		</div>
		<div class="col-md-3-adjust margin-top-15">
			<pacto-cat-form-input
				[label]="'Hora inicial*'"
				[id]="'input-horaInicial'"
				[placeholder]="'00h00'"
				[textMask]="{ mask: timerMask, guide: true }"
				[control]="formGroup.get('horaInicio')"
				[errorMsg]="'Campo obrigatório'"></pacto-cat-form-input>
		</div>
		<div class="col-md-3-adjust margin-top-15">
			<pacto-cat-form-input
				[label]="'Hora final*'"
				[id]="'input-horaFinal'"
				[placeholder]="'00h00'"
				[textMask]="{ mask: timerMask, guide: true }"
				[control]="formGroup.get('horaFim')"
				[errorMsg]="'Campo obrigatório'"></pacto-cat-form-input>
		</div>

		<button ds3-text-button class="margin-top-47" (click)="adicionarLista()">
			Adicionar dias e horário
		</button>
	</div>

	<div class="row">
		<div class="col-md-4">
			<pacto-cat-checkbox
				[label]="'Permitir agendar pelo App Treino'"
				[checkBlue]="true"
				[control]="
					formGroup.get('permiteAgendarPeloAppTreino')
				"></pacto-cat-checkbox>
		</div>
		<div class="col-md-9-adjust top10-border-top">
			<pacto-relatorio
				#tableData
				[table]="table"
				class="tableHours"
				[showShare]="false"
				(iconClick)="actionClickHandler($event)"
				actionTitulo="Ações"
				[filterConfig]="filterConfig"></pacto-relatorio>
		</div>
	</div>

	<div class="row justify-content-end btns-ativar-inativar">
		<button
			class="btn btn-primary"
			[disabled]="addHorario"
			(click)="salvarHorarioLocacao()">
			Salvar dias e horários
		</button>
	</div>
</div>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="createSuccess" i18n="@@crud-horarios:create-success">
		Horario criado com sucesso.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducao>
	<span xingling="salva-com-sucesso" i18n="@@integracoes:salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		xingling="falha-salvar-integracao"
		i18n="@@integracoes:falha-salvar-integracao">
		Falha ao tentar salvar integração!
	</span>
	<span xingling="ativada-com-sucesso" i18n="@@integracoes:ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		xingling="inativada-com-sucesso"
		i18n="@@integracoes:inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
</pacto-traducoes-xingling>
