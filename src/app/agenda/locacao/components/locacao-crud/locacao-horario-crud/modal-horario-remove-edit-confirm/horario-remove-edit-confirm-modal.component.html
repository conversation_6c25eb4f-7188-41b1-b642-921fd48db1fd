<div style="font-weight: bold">
	<h4 class="modal-title">
		{{ title }}
	</h4>
	<div class="modal-body">
		<div *ngIf="exclusao" class="line-color-actions">
			Deseja realmente inativar esse horário?
		</div>
		<div *ngIf="!exclusao" class="line-color-actions">
			Deseja realmente alterar esse horário?
		</div>

		<div class="line-color-icon-danger">
			<div
				class="fa fa-exclamation-triangle {{
					possuiAlunoAgendado
						? 'icon-adjust-top-large'
						: 'icon-adjust-top-simple'
				}}"></div>
			<div class="line-danger-text-adjust">
				{{ body }}
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button
			(click)="close()"
			class="btn btn-secondary modal-item"
			type="button">
			Cancelar
		</button>
		<button
			(click)="dismiss()"
			class="btn btn-primary modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			{{ exclusao ? "Confirmar exclusão" : "Confirmar alteração" }}
		</button>
	</div>
</div>
