import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { UsuarioBase } from "sdk";
import { Ambiente, HorarioDisponibilidade } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { Util } from "../../../../../../../projects/adm/src/app/util/Util";

@Component({
	selector: "pacto-locacao-horario-crud-v2-modal",
	templateUrl: "./locacao-horario-crud-v2-modal.component.html",
	styleUrls: ["./locacao-horario-crud-v2-modal.component.scss"],
})
export class LocacaoHorarioCrudV2ModalComponent
	implements OnInit, AfterViewInit
{
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	addHorario: boolean = true;
	timerMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	horariosData: {
		content: Array<{
			dias: string;
			horarioInicial: string;
			horarioFinal: string;
			horarios: Array<HorarioDisponibilidade>;
			ambientes: any[];
			professor: any;
		}>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<{
			dias: string;
			horarioInicial: string;
			horarioFinal: string;
			horarios: Array<HorarioDisponibilidade>;
			ambientes: any[];
			professor: any;
		}>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	horaFinalObrigatoria: boolean = true;
	duracaoEmMinutos: number = 0;
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		responsavel: new FormControl(null),
		ambientes: new FormControl(null),
		horaInicio: new FormControl(),
		horaFim: new FormControl({
			value: "",
			disabled: this.horaFinalObrigatoria,
		}),
		diaSemana: new FormControl(null),
		dom: new FormControl(false),
		seg: new FormControl(false),
		ter: new FormControl(false),
		qua: new FormControl(false),
		qui: new FormControl(false),
		sex: new FormControl(false),
		sab: new FormControl(false),
		todosDias: new FormControl(false),
		permiteAgendarPeloAppTreino: new FormControl(false),
	});
	edicao: boolean = false;
	variosHorarios: boolean = false;

	professores: Array<UsuarioBase>;
	ambientes: Array<Ambiente>;

	constructor(
		private activeModal: NgbActiveModal,
		private notificationService: SnotifyService,
		private rest: RestService,
		private openModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.configTable();
		this._listenChanges();
	}

	ngAfterViewInit() {
		this.formGroup
			.get("horaFim")
			[this.horaFinalObrigatoria ? "enable" : "disable"]();
	}

	private _listenChanges(): void {
		this.formGroup.get("horaInicio").valueChanges.subscribe((value) => {
			if (!this.horaFinalObrigatoria) {
				this.formGroup
					.get("horaFim")
					.patchValue(Util.calcularHoraFim(value, this.duracaoEmMinutos));
			}
		});
	}

	dto(): any {
		const dto = {
			codigo:
				this.formGroup.get("codigo") && this.formGroup.get("codigo").value
					? this.formGroup.get("codigo").value
					: null,
			responsavel: {
				codigo: this.formGroup.get("responsavel").value.id,
				nome: this.formGroup.get("responsavel").value.nome,
			},
			ambientes: [],
			horaInicio: this.formGroup.get("horaInicio").value,
			horaFim: this.formGroup.get("horaFim").value,
			permiteAgendarPeloAppTreino: this.formGroup.get(
				"permiteAgendarPeloAppTreino"
			).value,
		};
		this.formGroup.get("ambientes").value.forEach((a) => {
			dto.ambientes.push({
				codigo: a.codigo,
				ambiente: a.ambiente
					? { codigo: a.ambiente.id, nome: a.ambiente.nome }
					: { codigo: a.id, nome: a.nome },
			});
		});
		return dto;
	}

	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	validaProfInativo(): boolean {
		if (this.edicao && this.professores) {
			let inativo = true;
			this.professores.forEach((p) => {
				if (p.id === this.formGroup.get("responsavel").value.codigo) {
					inativo = false;
				}
			});
			return inativo;
		} else {
			return false;
		}
	}

	adicionarLista() {
		if (
			(this.formGroup.get("responsavel") &&
				(this.formGroup.get("responsavel").value === undefined ||
					this.formGroup.get("responsavel").value === null)) ||
			(this.formGroup.get("ambientes") &&
				(this.formGroup.get("ambientes").value === undefined ||
					this.formGroup.get("ambientes").value === null ||
					this.formGroup.get("ambientes").value.length === 0))
		) {
			this.notificationService.error(
				"Nenhum dos itens de seleção pode estar vazio! (Responsável ou Ambiente)."
			);
		} else if (this.isHorarioNaoPreenchido()) {
			this.notificationService.error(
				"A hora inicial ou hora final não foram preenchidas corretamente."
			);
		} else if (this.isDiaSemanaNaoPreenchido()) {
			this.notificationService.error(
				"É necessário selecionar pelo menos um dia da semana para prosseguir."
			);
		} else if (!this.isHoraInicialMenorQueHoraFinal()) {
			this.notificationService.error(
				"Por favor, defina uma hora inicial anterior à hora final."
			);
		} else {
			let diasString = "";
			const dto = this.dto();
			const horarios = Array<HorarioDisponibilidade>();
			if (
				this.formGroup.get("todosDias") &&
				this.formGroup.get("todosDias").value
			) {
				if (dto.codigo && dto.codigo > 0) {
					this.notificationService.warning(
						"Para edição individual de horario é inválido marcar 'todos os dias'"
					);
					return;
				} else {
					let diaSemanaNumero = 1;
					["DM", "SG", "TR", "QA", "QI", "SX", "SB"].forEach((dia) => {
						const horario = this.dto();

						horario.diaSemanaNumero = diaSemanaNumero;
						horario.diaSemana = dia;
						horarios.push(horario);
						diaSemanaNumero = diaSemanaNumero++;
					});
				}
				diasString = "Dom, Seg, Ter, Qua, Qui, Sex, Sab";
			} else {
				if (this.formGroup.get("dom") && this.formGroup.get("dom").value) {
					const horarioD = this.dto();
					if (this.validoAdicionarHorario(horarioD, horarios)) {
						diasString += ", Dom";
						horarioD.diaSemanaNumero = 1;
						horarioD.diaSemana = "DM";
						horarios.push(horarioD);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
				if (this.formGroup.get("seg") && this.formGroup.get("seg").value) {
					const horarioS = this.dto();
					if (this.validoAdicionarHorario(horarioS, horarios)) {
						diasString += ", Seg";
						horarioS.diaSemanaNumero = 2;
						horarioS.diaSemana = "SG";
						horarios.push(horarioS);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
				if (this.formGroup.get("ter") && this.formGroup.get("ter").value) {
					const horarioT = this.dto();
					if (this.validoAdicionarHorario(horarioT, horarios)) {
						diasString += ", Ter";
						horarioT.diaSemanaNumero = 3;
						horarioT.diaSemana = "TR";
						horarios.push(horarioT);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
				if (this.formGroup.get("qua") && this.formGroup.get("qua").value) {
					diasString += ", Qua";
					const horarioQA = this.dto();
					if (this.validoAdicionarHorario(horarioQA, horarios)) {
						horarioQA.diaSemanaNumero = 4;
						horarioQA.diaSemana = "QA";
						horarios.push(horarioQA);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
				if (this.formGroup.get("qui") && this.formGroup.get("qui").value) {
					const horarioQI = this.dto();
					if (this.validoAdicionarHorario(horarioQI, horarios)) {
						diasString += ", Qui";
						horarioQI.diaSemanaNumero = 5;
						horarioQI.diaSemana = "QI";
						horarios.push(horarioQI);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
				if (this.formGroup.get("sex") && this.formGroup.get("sex").value) {
					const horarioSE = this.dto();
					if (this.validoAdicionarHorario(horarioSE, horarios)) {
						diasString += ", Sex";
						horarioSE.diaSemanaNumero = 6;
						horarioSE.diaSemana = "SX";
						horarios.push(horarioSE);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
				if (this.formGroup.get("sab") && this.formGroup.get("sab").value) {
					const horarioSA = this.dto();
					if (this.validoAdicionarHorario(horarioSA, horarios)) {
						diasString += ", Sab";
						horarioSA.diaSemanaNumero = 7;
						horarioSA.diaSemana = "SB";
						horarios.push(horarioSA);
					} else {
						this.notificationService.warning(
							"Para edição individual de horario é inválido marcar 'mais de um dia da semana'."
						);
						return;
					}
				}
			}
			if (!this.horariosData.content) {
				this.horariosData.content = [];
			}
			let hrs = Array<HorarioDisponibilidade>();
			hrs = horarios;
			if (diasString.startsWith(",")) {
				diasString = diasString.slice(1);
				diasString = diasString.trim();
			}
			this.horariosData.content.forEach((h) => {
				if (
					diasString.includes(h.dias.trim()) &&
					((h.horarioInicial === dto.horaInicio &&
						h.horarioFinal === dto.horaFim) ||
						(dto.horaInicio >= h.horarioInicial &&
							dto.horaInicio < h.horarioFinal) ||
						(dto.horaFim > h.horarioInicial && dto.horaFim <= h.horarioFinal) ||
						(dto.horaInicio <= h.horarioInicial &&
							dto.horaFim >= h.horarioFinal)) &&
					h.ambientes.filter((item1) =>
						dto.ambientes.some(
							(item2) => item1.ambiente.codigo === item2.ambiente.codigo
						)
					).length > 0 &&
					h.professor.codigo === dto.responsavel.codigo
				) {
					diasString = "";
					this.notificationService.error(
						"Existem horários que já foram adicionados!"
					);
					return;
				}
			});
			if (diasString) {
				this.horariosData.content.push({
					dias: diasString,
					horarioInicial: dto.horaInicio,
					horarioFinal: dto.horaFim,
					horarios: hrs,
					ambientes: dto.ambientes,
					professor: dto.responsavel,
				});
			}
			this.tableData.reloadData();
			if (this.horariosData.content.length > 0) {
				this.addHorario = false;
			}
			this.cd.detectChanges();
		}
	}

	isHoraInicialMenorQueHoraFinal(): boolean {
		const horaInicio: string = this.formGroup.get("horaInicio").value;
		const horaFim: string = this.formGroup.get("horaFim").value;

		// separar hora e minuto
		const [horaInicioH, horaInicioM]: number[] = horaInicio
			.split(":")
			.map(Number);
		const [horaFimH, horaFimM]: number[] = horaFim.split(":").map(Number);

		// convertendo para minutos
		const inicioEmMinutos: number = horaInicioH * 60 + horaInicioM;
		const fimEmMinutos: number = horaFimH * 60 + horaFimM;

		// comparar as horas
		return inicioEmMinutos < fimEmMinutos;
	}

	validoAdicionarHorario(
		horario: HorarioDisponibilidade,
		horarios: HorarioDisponibilidade[]
	): boolean {
		if (horario.codigo && horario.codigo > 0 && horarios.length > 0) {
			return false;
		}
		return true;
	}

	salvarHorarioLocacao() {
		const horarios = Array<HorarioDisponibilidade>();
		this.horariosData.content.forEach((h) => {
			h.horarios.forEach((horario) => {
				horarios.push(horario);
			});
		});
		if (horarios.length > 0) {
			this.openModal.close(this.edicao ? horarios[0] : horarios);
			if (!this.edicao) {
				this.notificationService.success(
					this.notificacoesTranslate.getLabel("createSuccess")
				);
			}
		} else {
			this.openModal.close();
		}
	}

	isHorarioNaoPreenchido(): boolean {
		return (
			this.formGroup.get("horaInicio").value == null ||
			this.formGroup.get("horaInicio").value === "" ||
			this.formGroup.get("horaFim").value == null ||
			this.formGroup.get("horaFim").value === "" ||
			this.formGroup.get("horaInicio").value.includes("_") ||
			this.formGroup.get("horaFim").value.includes("_")
		);
	}

	isDiaSemanaNaoPreenchido(): boolean {
		return !(
			this.formGroup.get("dom").value ||
			this.formGroup.get("seg").value ||
			this.formGroup.get("ter").value ||
			this.formGroup.get("qua").value ||
			this.formGroup.get("qui").value ||
			this.formGroup.get("sex").value ||
			this.formGroup.get("sab").value
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	marcarTodosDiasSemana() {
		this.formGroup.get("dom").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("seg").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("ter").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("qua").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("qui").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("sex").setValue(!this.formGroup.get("todosDias").value);
		this.formGroup.get("sab").setValue(!this.formGroup.get("todosDias").value);
	}

	validarTodosDiasSemanaMarcados(dia: string) {
		if (
			("seg" === dia
				? !this.formGroup.get("seg").value
				: this.formGroup.get("seg").value) &&
			("ter" === dia
				? !this.formGroup.get("ter").value
				: this.formGroup.get("ter").value) &&
			("qua" === dia
				? !this.formGroup.get("qua").value
				: this.formGroup.get("qua").value) &&
			("qui" === dia
				? !this.formGroup.get("qui").value
				: this.formGroup.get("qui").value) &&
			("sex" === dia
				? !this.formGroup.get("sex").value
				: this.formGroup.get("sex").value) &&
			("sab" === dia
				? !this.formGroup.get("sab").value
				: this.formGroup.get("sab").value) &&
			("dom" === dia
				? !this.formGroup.get("dom").value
				: this.formGroup.get("dom").value)
		) {
			this.formGroup.get("todosDias").setValue(true);
		} else {
			this.formGroup.get("todosDias").setValue(false);
		}
	}

	get _rest() {
		return this.rest;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => this.horariosData,
			pagination: false,
			columns: [
				{
					nome: "dias",
					titulo: "Dias",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "horarioInicial",
					titulo: "Horário Inicial",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "horarioFinal",
					titulo: "Horário Final",
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					showIconFn: (row) => true,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.horariosData.content = this.horariosData.content.filter(
				(h) => h !== $event.row
			);
			this.tableData.reloadData();
			if (this.horariosData.content.length === 0) {
				this.addHorario = true;
			}
			this.cd.detectChanges();
		}
	}
}
