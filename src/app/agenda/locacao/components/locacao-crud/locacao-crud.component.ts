import {
	AfterContentChecked,
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Inject,
	LOCALE_ID,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";
import { LocalizationService } from "@base-core/localization/localization.service";
import {
	Modalidade,
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
	TreinoApiLocacaoService,
	TreinoApiModalidadeService,
	Ambiente,
	NivelTurma,
	Locacao,
	LocacaoHorario,
	TipoHorarioLocacaoEnum,
	TipoValidacaoEnum,
	LocacaoProdutoSugerido,
} from "treino-api";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	TraducoesXinglingComponent,
	SelectFilterResponseParser,
	CustomDataGridFilterComponent,
	CatTableEditableComponent,
} from "ui-kit";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { DatePipe, DecimalPipe } from "@angular/common";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";
import { ConfirmLocacaoCrudModalComponent } from "./modal-confirm-locacao-edit/confirm-locacao-crud-modal.component";
import { HorarioRemoveEditConfirmModalComponent } from "./locacao-horario-crud/modal-horario-remove-edit-confirm/horario-remove-edit-confirm-modal.component";
import { TableData } from "../../../../share/share.component";
import { ItemValidacao } from "../../../../../../projects/treino-api/src/lib/disponibilidade.model";
import { LocacaoHorarioCrudV2ModalComponent } from "./locacao-horario-crud/locacao-horario-crud-v2-modal.component";

export class SelectItemPactoCatSelect {
	value: string;
	label: string;
}

export class SelectItemPactoSelect {
	id: string;
	nome: string;
}

@Component({
	selector: "pacto-locacao-crud",
	templateUrl: "./locacao-crud.component.html",
	styleUrls: ["./locacao-crud.component.scss"],
})
export class LocacaoCrudComponent
	implements OnInit, AfterViewInit, AfterContentChecked
{
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@Output() reloadEvent: EventEmitter<any> = new EventEmitter();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("tableProdutos", { static: false })
	tableProdutos: CatTableEditableComponent;

	@ViewChild("filter", { static: false })
	filter: CustomDataGridFilterComponent;

	timerMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	formGroupFilter: FormGroup = new FormGroup({
		vigencia: new FormControl([{ id: true, nome: "Ativo" }]),
		responsavel: new FormControl(),
		ambiente: new FormControl(),
		segFc: new FormControl(false),
		terFc: new FormControl(false),
		quaFc: new FormControl(false),
		quiFc: new FormControl(false),
		sexFc: new FormControl(false),
		sabFc: new FormControl(false),
		domFc: new FormControl(false),
		horaInicial: new FormControl(),
		horaFinal: new FormControl(),
	});

	modalidades: Array<Modalidade> = [];
	horariosNovosAdd: LocacaoHorario[];
	locacaoCreateEditDps: Locacao;
	locacaoCreateEdit: Locacao;
	operation: string;
	entity = true;
	isFormGroupValid: boolean = false;
	tipoHorarioEnum = TipoHorarioLocacaoEnum;
	tipoValidacaoEnum = TipoValidacaoEnum;
	urlImagem: string = null;

	tipoValidacoes: Array<any> = [
		{ id: TipoValidacaoEnum.PLANO, nome: "Plano" },
		{ id: TipoValidacaoEnum.PRODUTO, nome: "Produto" },
	];

	tipoHorarios: Array<any> = [
		{ id: TipoHorarioLocacaoEnum.LIVRE, nome: "Livre" },
		{ id: TipoHorarioLocacaoEnum.PLAY, nome: "Play" },
		{ id: TipoHorarioLocacaoEnum.PRE_DEFINIDO, nome: "Pré-definido" },
	];

	vigencia: Array<any> = [
		{ id: true, nome: "Ativo" },
		{ id: false, nome: "Inativo" },
	];
	ambientes: ApiResponseList<Ambiente> = {
		content: [],
	};
	niveisTurma: ApiResponseList<NivelTurma> = {
		content: [],
	};

	formsCheckHorarios: Array<{
		horarioLocacao: LocacaoHorario;
		form: FormControl;
	}> = new Array<{ horarioLocacao: LocacaoHorario; form: FormControl }>();
	formCheckAll = new FormControl(false);
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		ativo: new FormControl(true, Validators.required),
		cor: new FormControl("#1B4166", [Validators.required]),
		horarios: new FormControl(null),
		tipoHorario: new FormControl(0),
		duracaoMinutos: new FormControl(0),
		tempoMinimoMinutos: new FormControl(0),
		tipoValidacao: new FormControl(1),
		produto: new FormControl(null, [Validators.required]),
		itensValidacao: new FormControl(null),
		produtosSugeridos: new FormControl(null),
		descricao: new FormControl("", [
			Validators.required,
			Validators.minLength(3),
		]),
		base64Imagem: new FormControl(""),
		formControlNomeImagem: new FormControl(""),
		dataInicial: new FormControl(new Date().getTime(), [Validators.required]),
		dataFinal: new FormControl(new Date().getTime(), [Validators.required]),
	});

	@ViewChild("fileInputComponent", { static: false }) fileInputComponent;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("diaColumnName", { static: true }) diaColumnName;
	@ViewChild("codigoColumnName", { static: true }) codigoColumnName;
	@ViewChild("columnCodigoProdSugeName", { static: true })
	columnCodigoProdSugeName;
	@ViewChild("columnProdutoName", { static: true }) columnProdutoName;
	@ViewChild("columnValorProdutoName", { static: true }) columnValorProdutoName;
	@ViewChild("columnObrigatorioName", { static: true }) columnObrigatorioName;
	@ViewChild("professorColumnName", { static: true }) professorColumnName;
	@ViewChild("disponibilidadeColumnName", { static: true })
	disponibilidadeColumnName;
	@ViewChild("funcionamentoColumnName", { static: true })
	funcionamentoColumnName;
	@ViewChild("ambienteColumnName", { static: true }) ambienteColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("horarioTurmaCelula", { static: true }) horarioTurmaCelula;
	@ViewChild("codigoCelula", { static: true }) codigoCelula;
	@ViewChild("professorCelula", { static: true }) professorCelula;
	@ViewChild("ambienteCelula", { static: true }) ambienteCelula;
	@ViewChild("disponibilidadeCelula", { static: true }) disponibilidadeCelula;
	@ViewChild("funcionamentoCelula", { static: true }) funcionamentoCelula;
	@ViewChild("diasCelula", { static: true }) diasCelula;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;

	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("actionNoUndone", { static: true }) actionNoUndone;
	@ViewChild("editOrExcludeHorarioDispLarge", { static: true })
	editOrExcludeHorarioDispLarge;
	tableProdSuge: PactoDataGridConfig;
	table: PactoDataGridConfig;
	horariosData: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();
	filterConfig: any;

	locacaoProdutosSugeridosArray: {
		content: Array<LocacaoProdutoSugerido>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<LocacaoProdutoSugerido>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	pageProdSuge = 1;
	sizeProdSuge = 5;
	itensPerPageProdSuge = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	diasSemana: { [key: number]: string } = {
		0: "DM", // Domingo
		1: "SG", // Segunda-feira
		2: "TR", // Terça-feira
		3: "QA", // Quarta-feira
		4: "QI", // Quinta-feira
		5: "SX", // Sexta-fera
		6: "SB", // Sábado
	};

	constructor(
		@Inject(LOCALE_ID) private locale,
		private localization: LocalizationService,
		private modalidadeService: TreinoApiModalidadeService,
		private notificationService: SnotifyService,
		private treinoApiLocacaoService: TreinoApiLocacaoService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private ambienteService: TreinoApiAmbienteService,
		private niveisTurmaService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private modal: NgbModal,
		private configCache: TreinoConfigCacheService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private session: SessionService,
		private ngbModal: NgbModal,
		private decimalPipe: DecimalPipe,
		private modalService: ModalService
	) {}

	ngOnInit() {
		this._listenChanges();
	}

	ngAfterContentChecked() {
		this.verificaCamposValidos();
		if (this.fileInputComponent) {
			this.urlImagem = this.fileInputComponent.urlImage;
		}
	}

	ngAfterViewInit() {
		this.configTable();
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}

			setTimeout(() => {
				zip(this.getAmbientes()).subscribe(() => {
					this.povoarHorarios();
					this.initTableProdSuge();
				});
				this.formCheckAll.valueChanges.subscribe((v: boolean) => {
					this.checkAll(v);
				});
				this.horariosNovosAdd = [];
			}, 1000);
		});
	}

	private _listenChanges() {
		this.formGroup.get("tipoHorario").valueChanges.subscribe({
			next: (value: string) => {
				const tipoValidacaoControl = this.formGroup.get("tipoValidacao");
				tipoValidacaoControl.setValidators(
					value === this.tipoHorarioEnum.PLAY.toString()
						? [Validators.required]
						: null
				);
				tipoValidacaoControl.updateValueAndValidity();
			},
		});
	}

	get produtoLocacaoUrl() {
		return this.rest.buildFullUrl(
			`disponibilidade/2/itensValidacao?tiposProduto='LC'`
		);
	}

	get itensValidacaoUrl() {
		if (
			this.formGroup.get("tipoValidacao") &&
			this.formGroup.get("tipoValidacao").value
		) {
			return this.rest.buildFullUrl(
				`disponibilidade/${
					this.formGroup.get("tipoValidacao").value
				}/itensValidacao`
			);
		}
		return this.rest.buildFullUrl("disponibilidade/0/itensValidacao");
	}

	responseParserProduto: SelectFilterResponseParser = (
		response: ApiResponseList<ItemValidacao>
	) => {
		if (response && response.content) {
			response.content.forEach((p) => {
				(p.codigo = p.produto),
					(p.descricao = p.descricao + " - R$ " + p.valorFinal);
			});
		}
		return response.content;
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	private loadEntities(id) {
		if (id) {
			this.treinoApiLocacaoService.consultarPorCodigo(id).subscribe((dados) => {
				if (dados) {
					this.locacaoCreateEdit = dados;
					this.entity = false;
					this.loadForm();
				} else {
					console.log("erro");
				}
			});
		}
	}

	povoarHorarios() {
		if (this.locacaoCreateEdit && this.locacaoCreateEdit.horarios) {
			let count = 1;
			const qtdHorarios = this.locacaoCreateEdit.horarios.length;
			this.locacaoCreateEdit.horarios.forEach((h) => {
				this.createHorariosPageObject(
					this.page,
					this.size,
					count === qtdHorarios
				);
				count++;
			});
		}
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	removeHandler(item) {
		const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
		const removeError = this.removeError.nativeElement.innerHTML;
		if (this.locacaoCreateEdit && this.locacaoCreateEdit.horarios) {
			const index = this.locacaoCreateEdit.horarios.indexOf(item, 0);
			if (index > -1) {
				this.treinoApiLocacaoService
					.deletarLocacaoHorario(item.codigo)
					.subscribe(
						(resp) => {
							if (resp) {
								this.snotifyService.success(removeSuccess);
								this.locacaoCreateEdit.horarios[index].ativo = false;
								if (this.tableData) {
									this.tableData.reloadData();
								}
							} else {
								this.snotifyService.error(removeError);
							}
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta && err.meta.messageValue) {
								this.snotifyService.error(err.meta.messageValue);
							}
						}
					);
			}
		} else {
			const index = this.formGroup.get("horarios").value.indexOf(item, 0);
			if (index > -1) {
				this.formGroup.get("horarios").value[index].ativo = false;
				this.snotifyService.success(removeSuccess);
				this.createHorariosPageObject(this.page, this.size, true);
			}
		}
	}

	btnClickHandler() {
		this.getModalAdicionarHorario(
			"Cadastro de dias e horários",
			LocacaoHorarioCrudV2ModalComponent
		);
	}

	editarHorariosSelecionados() {
		const horariosEditar = new Array<LocacaoHorario>();
		const horariosNaoEditar = new Array<LocacaoHorario>();
		this.formsCheckHorarios.forEach((f) => {
			if (f.form.value && f.horarioLocacao.ativo) {
				horariosEditar.push(f.horarioLocacao);
			} else {
				horariosNaoEditar.push(f.horarioLocacao);
			}
		});
		if (horariosEditar.length > 0) {
			this.openModalEditarHorarios(
				"Edição de dias e horário",
				LocacaoHorarioCrudV2ModalComponent,
				horariosEditar,
				horariosNaoEditar
			);
		} else {
			this.snotifyService.error("Nenhum horário selecionado!");
		}
	}

	btnEditHandler(item) {
		if (item.ativo) {
			this.openModalEditarHorario(
				"Edição de dias e horário",
				LocacaoHorarioCrudV2ModalComponent,
				item
			);
		}
	}

	createHorariosPageObject(
		page = 1,
		size = 5,
		reloadData = true,
		array = null,
		emitFilters: boolean = true
	) {
		const arrays = array
			? array
			: this.locacaoCreateEdit && this.locacaoCreateEdit.horarios
			? this.locacaoCreateEdit.horarios
			: this.formGroup.get("horarios") && this.formGroup.get("horarios").value
			? this.formGroup.get("horarios").value
			: [];
		this.horariosData.totalElements = arrays.length;
		this.horariosData.size = size;
		this.horariosData.totalPages = Math.ceil(
			+(this.horariosData.totalElements / this.horariosData.size)
		);
		this.horariosData.first = page === 0 || page === 1;
		this.horariosData.last = page === this.horariosData.totalPages;
		this.horariosData.content = arrays.slice(size * page - size, size * page);
		if (this.tableData) {
			if (reloadData) {
				this.tableData.reloadData(emitFilters);
			}
		}
	}

	ordenarHorarios(eventSort) {
		this.locacaoCreateEdit.horarios = this.sortList(
			this.locacaoCreateEdit.horarios,
			eventSort.columnName,
			eventSort.direction
		);
		this.createHorariosPageObject(this.page, this.size, true);
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createHorariosPageObject(this.page, this.size, true);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createHorariosPageObject(this.page, this.size, true);
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (
					a[columnName] > b[columnName] ||
					a[columnName].nome > b[columnName].nome
				) {
					return 1;
				} else if (
					a[columnName] < b[columnName] ||
					a[columnName].nome < b[columnName].nome
				) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (
					a[columnName] < b[columnName] ||
					a[columnName].nome < b[columnName].nome
				) {
					return 1;
				} else if (
					a[columnName] > b[columnName] ||
					a[columnName].nome > b[columnName].nome
				) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	openModalEditarHorario(title, modalComponent: any, item: any) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = true;
		modal.componentInstance.variosHorarios = false;
		modal.componentInstance.horaFinalObrigatoria =
			this.formGroup.get("tipoHorario").value !=
			TipoHorarioLocacaoEnum.PRE_DEFINIDO;
		modal.componentInstance.duracaoEmMinutos =
			this.formGroup.get("duracaoMinutos").value;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.formGroup.get("codigo").setValue(item.codigo);
		modal.componentInstance.formGroup.get("responsavel").setValue({
			id: item.responsavel.codigo,
			nome: item.responsavel.nome,
		});
		modal.componentInstance.formGroup.get("ambientes").setValue([]);
		item.ambientes.forEach((a) => {
			modal.componentInstance.formGroup.get("ambientes").value.push({
				codigo: a.codigo,
				ambiente: { id: a.ambiente.codigo, nome: a.ambiente.nome },
				nome: a.ambiente.nome,
			});
		});
		modal.componentInstance.formGroup
			.get("horaInicio")
			.setValue(item.horaInicio);
		modal.componentInstance.formGroup.get("horaFim").setValue(item.horaFim);
		modal.componentInstance.formGroup.get("diaSemana").setValue(item.diaSemana);
		modal.componentInstance.formGroup
			.get("permiteAgendarPeloAppTreino")
			.setValue(item.permiteAgendarPeloAppTreino);
		if (item.diaSemana === "DM") {
			modal.componentInstance.formGroup.get("dom").setValue(true);
		}
		if (item.diaSemana === "SG") {
			modal.componentInstance.formGroup.get("seg").setValue(true);
		}
		if (item.diaSemana === "TR") {
			modal.componentInstance.formGroup.get("ter").setValue(true);
		}
		if (item.diaSemana === "QA") {
			modal.componentInstance.formGroup.get("qua").setValue(true);
		}
		if (item.diaSemana === "QI") {
			modal.componentInstance.formGroup.get("qui").setValue(true);
		}
		if (item.diaSemana === "SX") {
			modal.componentInstance.formGroup.get("sex").setValue(true);
		}
		if (item.diaSemana === "SB") {
			modal.componentInstance.formGroup.get("sab").setValue(true);
		}
		modal.result.then(
			(dto) => {
				if (dto.codigo) {
					if (dto) {
						item.responsavel = dto.responsavel;
						item.ambientes = dto.ambientes;
						item.horaInicio = dto.horaInicio;
						item.horaFim = dto.horaFim;
						item.diaSemana = dto.diaSemana;
						item.permiteAgendarPeloAppTreino = dto.permiteAgendarPeloAppTreino;
						this.locacaoCreateEdit.horarios.forEach((h) => {
							if (h.codigo === item.codigo) {
								h = item;
							}
						});
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("horarioEditSuccess")
						);
					}
				} else {
					setTimeout(() => {
						const handler = this.modalService.open(
							"Editar Horário",
							HorarioRemoveEditConfirmModalComponent,
							PactoModalSize.MEDIUM
						);
						handler.componentInstance.exclusao = false;
						handler.componentInstance.body =
							this.actionNoUndone.nativeElement.innerHTML;
						handler.componentInstance.possuiAlunoAgendado = false;
						handler.result.then(
							(confirm: boolean) => {
								if (confirm) {
									if (dto) {
										item.responsavel = dto.responsavel;
										item.ambientes = dto.ambientes;
										item.horaInicio = dto.horaInicio;
										item.horaFim = dto.horaFim;
										item.diaSemana = dto.diaSemana;
										item.permiteAgendarPeloAppTreino =
											dto.permiteAgendarPeloAppTreino;
										this.locacaoCreateEdit.horarios.forEach((h) => {
											if (h.codigo === item.codigo) {
												h = item;
											}
										});
										this.notificationService.success(
											this.notificacoesTranslate.getLabel("horarioEditSuccess")
										);
									}
								}
							},
							() => {}
						);
					});
				}
			},
			() => {}
		);
	}

	openModalEditarHorarios(
		title,
		modalComponent: any,
		itens: LocacaoHorario[],
		naoItens: LocacaoHorario[]
	) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = true;
		modal.componentInstance.variosHorarios = true;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.formGroup.get("codigo").setValue(itens[0].codigo);
		modal.componentInstance.formGroup.get("responsavel").setValue({
			id: itens[0].responsavel.codigo,
			nome: itens[0].responsavel.nome,
		});
		modal.componentInstance.formGroup.get("ambientes").setValue([]);
		itens[0].ambientes.forEach((a) => {
			modal.componentInstance.formGroup.get("ambientes").value.push({
				codigo: a.codigo,
				ambiente: { id: a.ambiente.codigo, nome: a.ambiente.nome },
				nome: a.ambiente.nome,
			});
		});
		modal.componentInstance.formGroup
			.get("horaInicio")
			.setValue(itens[0].horaInicio);
		modal.componentInstance.formGroup.get("horaFim").setValue(itens[0].horaFim);
		modal.componentInstance.formGroup
			.get("diaSemana")
			.setValue(itens[0].diaSemana);
		modal.componentInstance.formGroup
			.get("permiteAgendarPeloAppTreino")
			.setValue(itens[0].permiteAgendarPeloAppTreino);
		modal.result.then(
			(dto) => {
				if (dto) {
					dto.ambientes.forEach((a) => (a.codigo = null));
					itens.forEach((t) => {
						t.responsavel = dto.responsavel;
						t.ambientes = dto.ambientes;
						t.horaInicio = dto.horaInicio;
						t.horaFim = dto.horaFim;
						t.permiteAgendarPeloAppTreino = dto.permiteAgendarPeloAppTreino;
					});
					this.horariosData.content = itens;
					naoItens.forEach((nI) => {
						this.horariosData.content.push(nI);
					});
					this.formCheckAll.setValue(false);
					this.formCheckAll.valueChanges.subscribe((v: boolean) => {
						this.checkAll(v);
					});
					this.notificationService.success(
						this.notificacoesTranslate.getLabel("horarioEditSuccess")
					);
				}
			},
			() => {}
		);
	}

	getModalAdicionarHorario(title, modalComponent: any) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = false;
		modal.componentInstance.variosHorarios = false;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.horaFinalObrigatoria =
			this.formGroup.get("tipoHorario").value !=
			TipoHorarioLocacaoEnum.PRE_DEFINIDO;
		modal.componentInstance.duracaoEmMinutos =
			this.formGroup.get("duracaoMinutos").value;
		modal.result.then(
			(dto) => {
				if (dto) {
					const horarios = [];
					dto.forEach((d) => {
						horarios.push({
							codigo: null,
							responsavel: d.responsavel,
							ambientes: d.ambientes,
							horaInicio: d.horaInicio,
							horaFim: d.horaFim,
							diaSemana: d.diaSemana,
							ativo: true,
							permiteAgendarPeloAppTreino: d.permiteAgendarPeloAppTreino,
						});
					});
					if (
						this.locacaoCreateEdit &&
						this.locacaoCreateEdit.codigo &&
						this.locacaoCreateEdit.codigo > 0
					) {
						horarios.forEach((h) => {
							this.locacaoCreateEdit.horarios.push(h);
						});
						this.createHorariosPageObject(1, 5, true, null, true);
					} else {
						horarios.forEach((h) => {
							this.horariosNovosAdd.push(h);
						});
						this.formGroup.get("horarios").setValue(this.horariosNovosAdd);
						this.createHorariosPageObject(
							1,
							5,
							true,
							this.horariosNovosAdd,
							true
						);
					}
				}
			},
			() => {}
		);
	}

	private getAmbientes(): Observable<any> {
		const ambientes$ = this.ambienteService.obterTodosAmbientes(true).pipe(
			map((dados) => {
				dados.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientes = dados;
				return true;
			})
		);
		return ambientes$;
	}

	btnClickFiltrarTabela() {
		const diasSemana = [];
		if (this.formGroupFilter.get("segFc").value) {
			diasSemana.push("SG");
		}
		if (this.formGroupFilter.get("terFc").value) {
			diasSemana.push("TR");
		}
		if (this.formGroupFilter.get("quaFc").value) {
			diasSemana.push("QA");
		}
		if (this.formGroupFilter.get("quiFc").value) {
			diasSemana.push("QI");
		}
		if (this.formGroupFilter.get("sexFc").value) {
			diasSemana.push("SX");
		}
		if (this.formGroupFilter.get("sabFc").value) {
			diasSemana.push("SB");
		}
		if (this.formGroupFilter.get("domFc").value) {
			diasSemana.push("DM");
		}
		this.tableData.addFilter(
			"filtrosNew",
			JSON.stringify({
				ativo: this.formGroupFilter.get("vigencia").value,
				responsavel: this.formGroupFilter.get("responsavel").value,
				ambiente: this.formGroupFilter.get("ambiente").value,
				diaSemana: diasSemana,
				horaInicial: this.formGroupFilter.get("horaInicial").value,
				horaFinal: this.formGroupFilter.get("horaFinal").value,
			})
		);
		this.tableData.reloadData();
		this.filter.close();
	}

	private configTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.table = new PactoDataGridConfig({
			ghostLoad: true,
			ghostAmount: 5,
			dataAdapterFn: (serverData) => {
				return this.horariosData;
			},
			pagination: true,
			state: this.state,
			columns: [
				{
					nome: "codigo",
					titulo: this.codigoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "codigo",
					celula: this.codigoCelula,
				},
				{
					nome: "responsavel",
					titulo: this.professorColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "responsavel",
					celula: this.professorCelula,
				},
				{
					nome: "ambiente",
					titulo: this.ambienteColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "ambiente",
					celula: this.ambienteCelula,
				},
				{
					nome: "disponibilidade",
					titulo: this.disponibilidadeColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "disponibilidade",
					celula: this.disponibilidadeCelula,
				},
				{
					nome: "duracao",
					titulo: this.funcionamentoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "tipoHorario",
					celula: this.funcionamentoCelula,
				},
				{
					nome: "diaSemana",
					titulo: this.diaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "diaSemana",
					celula: this.diasCelula,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					showIconFn: (row) => row.ativo,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipInativar,
					showIconFn: (row) => row.ativo,
				},
			],
		});
		if (this.tableData) {
			this.tableData.pageSizeControl.setValue(5);
		}
	}

	private loadForm() {
		this.formGroup.get("codigo").setValue(this.locacaoCreateEdit.codigo);
		this.formGroup.get("nome").setValue(this.locacaoCreateEdit.nome);
		this.formGroup.get("ativo").setValue(!!this.locacaoCreateEdit.ativo);
		this.formGroup.get("cor").setValue(this.locacaoCreateEdit.cor);
		this.formGroup.get("horarios").setValue(this.locacaoCreateEdit.horarios);
		this.formGroup
			.get("tipoHorario")
			.setValue(this.locacaoCreateEdit.tipoHorario);
		this.formGroup
			.get("duracaoMinutos")
			.setValue(this.locacaoCreateEdit.duracaoMinutos);
		this.formGroup
			.get("tempoMinimoMinutos")
			.setValue(this.locacaoCreateEdit.tempoMinimoMinutos);
		this.formGroup
			.get("tipoValidacao")
			.setValue(this.locacaoCreateEdit.tipoValidacao);
		if (this.locacaoCreateEdit.produto) {
			this.formGroup.get("produto").setValue({
				codigo: this.locacaoCreateEdit.produto.codigo,
				descricao:
					this.locacaoCreateEdit.produto.descricao +
					" - R$ " +
					this.locacaoCreateEdit.produto.valorFinal,
			});
		}
		this.formGroup
			.get("itensValidacao")
			.setValue(this.locacaoCreateEdit.itensValidacao);
		this.formGroup.get("descricao").setValue(this.locacaoCreateEdit.descricao);
		this.formGroup
			.get("base64Imagem")
			.setValue(this.locacaoCreateEdit.base64Imagem);
		this.formGroup
			.get("formControlNomeImagem")
			.setValue(this.locacaoCreateEdit.formControlNomeImagem);
		this.formGroup
			.get("dataInicial")
			.setValue(new Date(this.locacaoCreateEdit.dataInicial).getTime());
		this.formGroup
			.get("dataFinal")
			.setValue(new Date(this.locacaoCreateEdit.dataFinal).getTime());
		this.urlImagem = this.locacaoCreateEdit.urlImagem;
		this.cd.detectChanges();
	}

	submitHandler() {
		this.markAsTouched();
		if (!this.entity) {
			this.editHandler();
		} else {
			this.createHandler();
		}
	}

	markAsTouched() {
		this.formGroup.get("codigo").markAsTouched();
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("cor").markAsTouched();
		this.formGroup.get("horarios").markAsTouched();
		this.formGroup.get("tipoHorario").markAsTouched();
		this.formGroup.get("tempoMinimoMinutos").markAsTouched();
		this.formGroup.get("tipoValidacao").markAsTouched();
		this.formGroup.get("produto").markAsTouched();
		this.formGroup.get("itensValidacao").markAsTouched();
		this.formGroup.get("descricao").markAsTouched();
		this.formGroup.get("base64Imagem").markAsTouched();
		this.formGroup.get("formControlNomeImagem").markAsTouched();
		this.formGroup.get("dataInicial").markAsTouched();
		this.formGroup.get("dataFinal").markAsTouched();
	}

	private createHandler() {
		if (this.formGroup.valid) {
			this.formGroup
				.get("produtosSugeridos")
				.setValue(this.locacaoCreateEdit.produtosSugeridos);
			this.locacaoCreateEdit = this.formGroup.getRawValue();
			this.locacaoCreateEdit.urlImagem = this.urlImagem;

			const disponibilidades: Array<LocacaoHorario> = [];

			this.locacaoCreateEdit.horarios.forEach((h) => {
				disponibilidades.push({
					diaSemana: h.diaSemana,
					horaInicio: h.horaInicio,
					horaFim: h.horaFim,
					disponibilidade: h.disponibilidade,
					responsavel: h.responsavel,
					ambientes: h.ambientes,
					permiteAgendarPeloAppTreino: h.permiteAgendarPeloAppTreino,
					ativo: true,
					finalizado: false,
					valorLocacao: h.valorLocacao,
					valorExtrasObrigatorios: h.valorExtrasObrigatorios,
					valorExtrasAdicionais: h.valorExtrasAdicionais,
					valorTotal: h.valorTotal,
					vendaAvulsaCodigo: h.vendaAvulsaCodigo,
				});
			});

			this.locacaoCreateEdit.horarios = disponibilidades;

			if (this.locacaoCreateEdit.horarios) {
				let todosInativo = true;
				this.locacaoCreateEdit.horarios.forEach((h) => {
					if (h.ativo) {
						todosInativo = false;
					}
					if (h.ativo === null || h.ativo === undefined) {
						h.ativo = true;
					}
				});
				if (todosInativo) {
					this.notificationService.error("Nenhum horário adicionado!");
					return;
				}
			} else {
				this.notificationService.error("Nenhum horário adicionado!");
				return;
			}
			if (
				this.locacaoCreateEdit.produto &&
				this.locacaoCreateEdit.produto.produto
			) {
				this.locacaoCreateEdit.produto = {
					codigo: this.locacaoCreateEdit.produto.produto,
					descricao: this.locacaoCreateEdit.produto.descricao,
				};
			}
			this.treinoApiLocacaoService
				.cadastrar(this.locacaoCreateEdit)
				.subscribe((result) => {
					if (result.retorno) {
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("createSuccess")
						);
						this.cancelHandler(false);
					} else {
						this.notificationService.error(result.erro);
					}
				});
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	private editHandler() {
		if (this.formGroup.valid) {
			this.formGroup
				.get("produtosSugeridos")
				.setValue(this.locacaoCreateEdit.produtosSugeridos);
			this.locacaoCreateEdit = this.formGroup.getRawValue();
			this.locacaoCreateEdit.urlImagem = this.urlImagem;

			const disponibilidades: Array<LocacaoHorario> = [];

			this.formGroup.get("horarios").value.forEach((h: any) => {
				disponibilidades.push({
					codigo: h.codigo,
					diaSemana: h.diaSemana,
					horaInicio: h.horaInicio,
					horaFim: h.horaFim,
					disponibilidade: h.disponibilidade,
					responsavel: h.responsavel,
					ambientes: h.ambientes,
					permiteAgendarPeloAppTreino: h.permiteAgendarPeloAppTreino,
					ativo: true,
					finalizado: false,
					valorLocacao: h.valorLocacao,
					valorExtrasObrigatorios: h.valorExtrasObrigatorios,
					valorExtrasAdicionais: h.valorExtrasAdicionais,
					valorTotal: h.valorTotal,
					vendaAvulsaCodigo: h.vendaAvulsaCodigo,
				});
			});

			this.locacaoCreateEdit.horarios = disponibilidades;

			this.locacaoCreateEdit.horarios.forEach((h) => {
				if (!h.codigo && (h.ativo === null || h.ativo === undefined)) {
					h.ativo = true;
				}
			});
			if (this.locacaoCreateEdit.horarios) {
				let todosInativo = true;
				this.locacaoCreateEdit.horarios.forEach((h) => {
					if (h.ativo) {
						todosInativo = false;
					}
				});
				if (todosInativo) {
					this.notificationService.error("Nenhum horário adicionado!");
					return;
				}
			} else {
				this.notificationService.error("Nenhum horário adicionado!");
				return;
			}
			if (
				this.locacaoCreateEdit.produto &&
				this.locacaoCreateEdit.produto.produto
			) {
				this.locacaoCreateEdit.produto = {
					codigo: this.locacaoCreateEdit.produto.produto,
					descricao: this.locacaoCreateEdit.produto.descricao,
				};
			}
			this.treinoApiLocacaoService
				.alterar(this.locacaoCreateEdit)
				.subscribe((result) => {
					if (result.retorno) {
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("editSuccess")
						);
						this.cancelHandler(false);
					} else {
						this.notificationService.error(result.erro);
					}
				});
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	limparItensValidacao() {
		this.formGroup.get("itensValidacao").setValue(null);
	}

	limparValoresTipoHorario() {
		this.formGroup.get("duracaoMinutos").setValue(null);
		this.formGroup.get("tempoMinimoMinutos").setValue(null);
		this.formGroup.get("produto").setValue(null);
		this.formGroup.get("tipoValidacao").setValue(null);
		this.formGroup.get("itensValidacao").setValue(null);
	}

	cancelHandler(value: boolean) {
		if (value && this.formGroup.valid && this.locacaoCreateEdit) {
			this.locacaoCreateEditDps = this.formGroup.getRawValue();
			if (
				this.isEquivalent(this.locacaoCreateEdit, this.locacaoCreateEditDps)
			) {
				this.router.navigate(["agenda", "locacao"]);
			} else {
				this.openModalConfirmTurmaEdit(
					"Alterações não salvas",
					ConfirmLocacaoCrudModalComponent
				);
			}
		} else {
			this.router.navigate(["agenda", "locacao"]);
		}
	}

	isEquivalent(a, b) {
		const aProps = Object.getOwnPropertyNames(a);
		const bProps = Object.getOwnPropertyNames(b);
		if (aProps.length !== bProps.length - 1) {
			return false;
		}

		for (let i = 0; i < aProps.length; i++) {
			const propName = aProps[i];
			if (
				!(
					propName === "dias" ||
					propName === "urlImagem" ||
					propName === "produto" ||
					propName === "produtosSugeridos" ||
					propName === "tipoHorarioDescricao"
				) &&
				a[propName] !== b[propName]
			) {
				return false;
			}
		}
		return true;
	}

	openModalConfirmTurmaEdit(title, modalComponent: any) {
		const ref = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.MEDIUM
		);
		ref.result.then(
			(value) => {
				if (value) {
					this.router.navigate(["agenda", "locacao"]);
				}
			},
			() => {}
		);
	}

	verificaCamposValidos() {
		this.isFormGroupValid = this.formGroup.status === "VALID" ? true : false;
	}

	tipoHorarioName(item: number): string {
		if (item !== undefined || item !== null) {
			const i = this.tipoHorarios.find((t) => t.id === item);
			if (i) {
				return i.nome;
			} else {
				return "";
			}
		}
		return "";
	}

	convertDiaSemanaString(d: string): string {
		let ret = "";
		if (d !== undefined || d !== null || d !== "") {
			if (d === "SG") {
				ret = "Seg";
			} else if (d === "TR") {
				ret = "Ter";
			} else if (d === "QA") {
				ret = "Qua";
			} else if (d === "QI") {
				ret = "Qui";
			} else if (d === "SX") {
				ret = "Sex";
			} else if (d === "SB") {
				ret = "Sab";
			} else if (d === "DM") {
				ret = "Dom";
			}
		}
		return ret;
	}

	clearVigencia() {
		this.formGroupFilter.get("vigencia").setValue(null);
	}

	clearProfessores() {
		this.formGroupFilter.get("responsavel").setValue(null);
	}

	clearAmbientes() {
		this.formGroupFilter.get("ambiente").setValue(null);
	}

	clearHorarios() {
		this.formGroupFilter.get("horaInicial").setValue(null);
		this.formGroupFilter.get("horaFinal").setValue(null);
	}

	limparFiltros() {
		this.formGroupFilter.reset();
	}

	clearDiasSemana() {
		this.formGroupFilter.get("segFc").setValue(null);
		this.formGroupFilter.get("terFc").setValue(null);
		this.formGroupFilter.get("quaFc").setValue(null);
		this.formGroupFilter.get("quiFc").setValue(null);
		this.formGroupFilter.get("sexFc").setValue(null);
		this.formGroupFilter.get("sabFc").setValue(null);
		this.formGroupFilter.get("domFc").setValue(null);
	}

	filterChangeEvent(event: any) {
		event.page = event.page === 0 ? 1 : event.page + 1;
		let filters = null;
		if (!(event.filters === "{}")) {
			filters = JSON.parse(JSON.parse(event.filters).filtrosNew);
		} else {
			this.formGroupFilter.get("vigencia").setValue([
				{
					id: true,
					nome: "Ativo",
				},
			]);
			filters = {
				ativo: [
					{
						id: true,
						nome: "Ativo",
					},
				],
			};
		}
		if (
			filters.horaInicial ||
			filters.horaFinal ||
			(filters.ambiente && filters.ambiente.length > 0) ||
			(filters.responsavel && filters.responsavel.length > 0) ||
			(filters.diaSemana && filters.diaSemana.length > 0) ||
			(filters.ativo && filters.ativo.length > 0)
		) {
			const horariosPercorrer =
				this.locacaoCreateEdit && this.locacaoCreateEdit.horarios
					? this.locacaoCreateEdit.horarios
					: this.formGroup.get("horarios").value;
			const horarios = new Set<LocacaoHorario>();
			horariosPercorrer.forEach((h) => {
				if (filters.horaInicial) {
					if (h.horaInicio === filters.horaInicial) {
						if (filters.ativo && filters.ativo.length > 0) {
							if (
								filters.ativo.length === 1 &&
								h.ativo === filters.ativo[0].id
							) {
								horarios.add(h);
							} else if (filters.ativo.length === 2) {
								horarios.add(h);
							}
						} else {
							horarios.add(h);
						}
					}
				}
				if (filters.horaFinal) {
					if (h.horaFim === filters.horaFinal) {
						if (filters.ativo && filters.ativo.length > 0) {
							if (
								filters.ativo.length === 1 &&
								h.ativo === filters.ativo[0].id
							) {
								horarios.add(h);
							} else if (filters.ativo.length === 2) {
								horarios.add(h);
							}
						} else {
							horarios.add(h);
						}
					}
				}
				if (filters.ambiente && filters.ambiente.length > 0) {
					filters.ambiente.forEach((a) => {
						h.ambientes.forEach((hA) => {
							if (hA.ambiente.codigo === a.id) {
								if (filters.ativo && filters.ativo.length > 0) {
									if (
										filters.ativo.length === 1 &&
										h.ativo === filters.ativo[0].id
									) {
										horarios.add(h);
									} else if (filters.ativo.length === 2) {
										horarios.add(h);
									}
								} else {
									horarios.add(h);
								}
							}
						});
					});
				}
				if (filters.responsavel && filters.responsavel.length > 0) {
					filters.responsavel.forEach((p) => {
						if (h.responsavel.codigo === p.id) {
							if (filters.ativo && filters.ativo.length > 0) {
								if (
									filters.ativo.length === 1 &&
									h.ativo === filters.ativo[0].id
								) {
									horarios.add(h);
								} else if (filters.ativo.length === 2) {
									horarios.add(h);
								}
							} else {
								horarios.add(h);
							}
						}
					});
				}
				if (filters.diaSemana && filters.diaSemana.length > 0) {
					filters.diaSemana.forEach((d) => {
						if (h.diaSemana === d) {
							if (filters.ativo && filters.ativo.length > 0) {
								if (
									filters.ativo.length === 1 &&
									h.ativo === filters.ativo[0].id
								) {
									horarios.add(h);
								} else if (filters.ativo.length === 2) {
									horarios.add(h);
								}
							} else {
								horarios.add(h);
							}
						}
					});
				}
				if (
					!filters.horaInicial &&
					!filters.horaFinal &&
					!(filters.diaSemana && filters.diaSemana.length > 0) &&
					!(filters.responsavel && filters.responsavel.length > 0) &&
					!(filters.ambiente && filters.ambiente.length > 0)
				) {
					filters.ativo.forEach((ativo) => {
						if (h.ativo === ativo.id) {
							horarios.add(h);
						} else if (filters.ativo.length === 2) {
							horarios.add(h);
						}
					});
				}
			});
			const newHorarios = [];
			horarios.forEach((h) => {
				newHorarios.push(h);
			});
			if (newHorarios.length <= event.size) {
				event.page = 1;
			}
			this.createHorariosPageObject(
				event.page,
				event.size,
				true,
				newHorarios,
				false
			);
		} else {
			this.createHorariosPageObject(event.page, event.size, true, null, false);
		}
		this.initFormsHorariosCheck();
		this.formCheckAll.setValue(false);
		this.formCheckAll.valueChanges.subscribe((v: boolean) => {
			this.checkAll(v);
		});
	}

	initFormsHorariosCheck() {
		this.formsCheckHorarios = new Array<{
			horarioLocacao: LocacaoHorario;
			form: FormControl;
		}>();
		if (this.horariosData.content) {
			this.horariosData.content.forEach((lh) =>
				this.formsCheckHorarios.push({
					horarioLocacao: lh,
					form: new FormControl(),
				})
			);
		}
	}

	private checkAll(v: boolean) {
		this.formsCheckHorarios.forEach((f) => {
			f.form.setValue(v);
		});
	}

	getFormCheckHorario(horarioLocacao: LocacaoHorario): FormControl {
		if (this.formsCheckHorarios.length === 0) {
			this.initFormsHorariosCheck();
		}
		let formControl: FormControl;
		this.formsCheckHorarios.forEach((f) => {
			if (f.horarioLocacao === horarioLocacao) {
				formControl = f.form;
			}
		});
		if (formControl === undefined) {
			formControl = new FormControl();
			this.formsCheckHorarios.push({ horarioLocacao, form: new FormControl() });
		}
		return formControl;
	}

	deleteProdSugeridos(event) {
		if (this.locacaoCreateEdit.produtosSugeridos) {
			let index;
			if (event.row.codigo) {
				const excecao = this.locacaoCreateEdit.produtosSugeridos.find(
					(ex, i) => {
						if (ex.codigo === event.row.codigo) {
							index = i;
							return ex;
						}
					}
				);
				if (excecao && index !== undefined) {
					this.locacaoCreateEdit.produtosSugeridos.splice(index, 1);
				}
			} else {
				if (event.row.codigo === "") {
					this.locacaoProdutosSugeridosArray.content.splice(index, 1);
				} else if (event.row.codigo === null) {
					const excecao = this.locacaoCreateEdit.produtosSugeridos.find(
						(ex, i) => {
							if (ex.produto.descricao === event.row.produto.descricao) {
								index = i;
								return ex;
							}
						}
					);
					if (excecao && index !== undefined) {
						this.locacaoCreateEdit.produtosSugeridos.splice(index, 1);
					}
				}
			}
			this.createPageObjectProdSugeridos();
		}
	}

	pageChangeEventProdSugeridos(page) {
		if (!isNaN(page)) {
			this.pageProdSuge = page;
		}
		this.createPageObjectProdSugeridos(this.pageProdSuge, this.sizeProdSuge);
	}

	pageSizeChangeProdSugeridos(size) {
		if (!isNaN(size)) {
			this.sizeProdSuge = size;
			this.pageProdSuge = 1;
		}
		this.createPageObjectProdSugeridos(this.pageProdSuge, this.sizeProdSuge);
	}

	createPageObjectProdSugeridos(page = 1, size = 5) {
		this.locacaoProdutosSugeridosArray.totalElements =
			this.locacaoCreateEdit.produtosSugeridos.length;
		this.locacaoProdutosSugeridosArray.size = size;
		this.locacaoProdutosSugeridosArray.totalPages = Math.ceil(
			+(
				this.locacaoProdutosSugeridosArray.totalElements /
				this.locacaoProdutosSugeridosArray.size
			)
		);
		this.locacaoProdutosSugeridosArray.first = page === 0 || page === 1;
		this.locacaoProdutosSugeridosArray.last =
			page === this.locacaoProdutosSugeridosArray.totalPages;
		this.locacaoProdutosSugeridosArray.content =
			this.locacaoCreateEdit.produtosSugeridos.slice(
				size * page - size,
				size * page
			);
		if (this.tableProdutos) {
			this.tableProdutos.ngbPage = this.page;
			this.tableProdutos.reloadData();
		}
		this.cd.detectChanges();
	}

	sortEventProdSuge(sortEvent: {
		columnName: any;
		direction: any;
		column: any;
	}) {
		this.locacaoCreateEdit.produtosSugeridos =
			this.locacaoCreateEdit.produtosSugeridos.sort((a, b) => {
				if (sortEvent.direction === "ASC") {
					if (a.produto.descricao > b.produto.descricao) {
						return 1;
					} else if (a.produto.descricao < b.produto.descricao) {
						return -1;
					} else {
						return 0;
					}
				} else {
					if (a.produto.descricao < b.produto.descricao) {
						return 1;
					} else if (a.produto.descricao > b.produto.descricao) {
						return -1;
					} else {
						return 0;
					}
				}
			});
		this.createPageObjectProdSugeridos();
	}

	initTableProdSuge() {
		this.tableProdSugerido();
		if (!this.locacaoCreateEdit) {
			this.locacaoCreateEdit = new Locacao();
			this.locacaoCreateEdit.produtosSugeridos =
				new Array<LocacaoProdutoSugerido>();
		} else if (!this.locacaoCreateEdit.produtosSugeridos) {
			this.locacaoCreateEdit.produtosSugeridos =
				new Array<LocacaoProdutoSugerido>();
		}
		this.tableProdSuge.dataAdapterFn = (serverData): TableData<any> => {
			serverData = this.locacaoProdutosSugeridosArray;
			return serverData;
		};
		this.createPageObjectProdSugeridos();
	}

	private tableProdSugerido() {
		this.tableProdSuge = new PactoDataGridConfig({
			pagination: true,
			state: this.state,
			formGroup: new FormGroup({
				produto: new FormControl(null, Validators.required),
				valor: new FormControl(0, Validators.required),
				obrigatorio: new FormControl(false),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigoProdSugeName,
					visible: true,
					ordenavel: true,
					width: "20%",
				},
				{
					nome: "produto",
					titulo: this.columnProdutoName,
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "select",
					width: "20%",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					objectAttrLabelName: "descricao",
					endpointUrl: this.rest.buildFullUrl(
						`disponibilidade/2/itensValidacao?tiposProduto='PE','SE'`
					),
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
							}),
						};
					},
					selectOptionChange: (option, form, row) => {
						form.get("produto").value.codigo = option.produto;
						form.get("valor").setValue(option.valorFinal);
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
					isDisabled: (row) => this.isProductDisabled(row),
				},
				{
					nome: "valor",
					titulo: this.columnValorProdutoName,
					visible: true,
					ordenavel: true,
					editable: true,
					width: "20%",
					inputType: "decimal",
					decimal: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
				},
				{
					nome: "obrigatorio",
					titulo: this.columnObrigatorioName,
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "checkbox",
					width: "20%",
					valueTransform: (v) => (v ? "Sim" : "Não"),
					isDisabled: (row) => this.isProductDisabled(row),
				},
			],
		});
		if (this.tableProdutos) {
			this.tableProdutos.ngbPage = 1;
		}
		this.cd.detectChanges();
	}

	private beforeConfirm(row: any, form: any, data: any, indexRow: any) {
		const exists = this.verifyIfNewExists(row, data, indexRow);
		if (!exists) {
			const produto = form.get("produto").value;
			const valor = form.get("valor").value;
			const obrigatorio = form.get("obrigatorio").value;
			if (obrigatorio === undefined || obrigatorio === null) {
				form.get("obrigatorio").setValue(false);
				row.obrigatorio = false;
			}
			if (
				produto === null ||
				produto === undefined ||
				produto === "" ||
				valor === null ||
				valor === undefined ||
				valor === ""
			) {
				this.notificationService.error("Linha vazia!");
				return false;
			}
			if (
				this.locacaoCreateEdit &&
				this.locacaoCreateEdit.produtosSugeridos.find(
					(d, index) =>
						index !== indexRow &&
						d.produto.codigo === form.get("produto").value.codigo
				)
			) {
				this.notificationService.error("Produto duplicado!");
				return false;
			} else if (
				(row.codigo === null || row.codigo === "") &&
				this.locacaoCreateEdit &&
				this.locacaoCreateEdit.produtosSugeridos
			) {
				this.locacaoCreateEdit.produtosSugeridos.push({
					codigo: null,
					produto: form.get("produto").value,
					valor: form.get("valor").value,
					obrigatorio: form.get("obrigatorio").value,
				});
			}
		} else {
			this.notificationService.error("Produto duplicado!");
			return false;
		}
		this.createPageObjectProdSugeridos();
		return true;
	}

	showDelete(row, isAdd) {
		return true;
	}

	isProductDisabled(row) {
		return false;
	}

	verifyIfNewExists(row, dataTable, rowIndex) {
		let exists = false;
		dataTable.find((data, index) => {
			if (index !== rowIndex) {
				exists = this.compareObjects(row, data, "codigo", "edit", "codigo");
				if (exists) {
					return;
				}
			}
		});
		return exists;
	}

	compareObjects(obj1, obj2, uniqueKey?, ...keysAvoidCompare) {
		let equals = true;
		Object.keys(obj1).forEach((key) => {
			if (keysAvoidCompare.find((v) => v === key)) {
				return;
			}
			if (
				obj1[key] !== undefined &&
				obj2[key] !== undefined &&
				obj1[key] !== null &&
				obj2[key] !== null
			) {
				if (typeof obj1[key] !== "object") {
					if (obj1[key].toString() !== obj2[key].toString()) {
						equals = false;
						return;
					}
				} else {
					if (uniqueKey) {
						if (obj1[key][uniqueKey] !== obj2[key][uniqueKey]) {
							equals = false;
							return;
						}
					} else {
						equals = this.compareObjects(obj1[key], obj2[key]);
					}
				}
			}
		});
		return equals;
	}

	ambientesConcatenado(itens: any[]): string {
		let ret = "";
		let limit = 0;
		itens.forEach((a) => {
			if (limit === 2) {
				ret = ret + " ...";
			} else if (limit < 2) {
				if (ret === "") {
					ret = a.ambiente.nome;
					limit++;
				} else {
					ret = ret + ", " + a.ambiente.nome;
					limit++;
				}
			}
		});
		return ret;
	}

	get _rest() {
		return this.rest;
	}
}
