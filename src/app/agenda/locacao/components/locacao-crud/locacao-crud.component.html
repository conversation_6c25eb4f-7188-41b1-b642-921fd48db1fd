<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		(click)="cancelHandler(true)"
		[breadcrumbConfig]="{
			categoryName: !entity ? 'Locação' : 'Locação ',
			menu: !entity ? 'Editar locação' : 'Cadastrar locação',
			menuLink: []
		}"
		class="first"></pacto-breadcrumbs>

	<pacto-cat-card-plain *ngIf="operation === 'create'">
		<pacto-cat-stepper class="force-header-start" style="margin-bottom: 10px">
			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Informações</ng-template>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('nome')"
							[id]="'nome-locacao-input'"
							i18n-label="@@crud-locacao:input:nome:label"
							i18np-placeholder="@@crud-locacao:input:nome:placeholder"
							label="Nome da locação*"
							placeholder="Adicione um nome para sua disponibilidade"></pacto-cat-form-input>
					</div>

					<div class="col-md-6">
						<pacto-cat-input-color [formControl]="formGroup.get('cor')">
							<label class="label-cor-disponibilidade">Cor da locação*</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="row">
					<div class="col-md-3">
						<pacto-cat-form-select
							(change)="limparValoresTipoHorario()"
							[control]="formGroup.get('tipoHorario')"
							[id]="'tipoHorario-select'"
							[items]="tipoHorarios"
							i18n-label="@@crud-locacao:select:tipoHorario:label"
							idKey="id"
							label="Tipo de horário*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.PRE_DEFINIDO
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('duracaoMinutos')"
							[id]="'duracao'"
							i18n-label="@@crud-locacao:select:duracao:label"
							label="Duração em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.LIVRE
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('tempoMinimoMinutos')"
							[id]="'duracaoMinima'"
							i18n-label="@@crud-locacao:select:tempoMinimo:label"
							label="Tempo mínimo em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.PLAY
						"
						class="col-md-3">
						<pacto-cat-form-select
							(change)="limparItensValidacao()"
							[control]="formGroup.get('tipoValidacao')"
							[id]="'tipoValidacao-select'"
							[items]="tipoValidacoes"
							i18n-label="@@crud-locacao:select:tipoValidacao:label"
							idKey="id"
							label="Tipo de validação*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.PRE_DEFINIDO ||
							formGroup.get('tipoHorario').value == tipoHorarioEnum.LIVRE
						"
						class="col-md-6">
						<pacto-cat-form-select-filter
							[control]="formGroup.get('produto')"
							[endpointUrl]="produtoLocacaoUrl"
							[id]="'produto-select'"
							[paramBuilder]="paramBuilder"
							[resposeParser]="responseParserProduto"
							i18n-label="@@crud-locacao:select:itensValidacao:label"
							idKey="codigo"
							label="Produto*"
							labelKey="descricao"></pacto-cat-form-select-filter>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoValidacao').value &&
							formGroup.get('tipoValidacao').value != tipoValidacaoEnum.LIVRE &&
							formGroup.get('tipoHorario').value &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.PLAY
						"
						class="col-md-6">
						<pacto-cat-form-multi-select-filter
							[autocomplete]="'off'"
							[control]="formGroup.get('itensValidacao')"
							[endpointUrl]="itensValidacaoUrl"
							[idKey]="
								formGroup.get('tipoValidacao').value == tipoValidacaoEnum.PLANO
									? 'plano'
									: 'produto'
							"
							[id]="'itensValidacao-select'"
							[labelKey]="'descricao'"
							[paramBuilder]="paramBuilder"
							[resposeParser]="responseParser"
							i18n-label="@@crud-locacao:select:itensValidacao:label"
							label="Itens de validação*"></pacto-cat-form-multi-select-filter>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('descricao')"
							[id]="'descricao'"
							i18n-label="@@crud-locacao:select:descricao:label"
							i18np-placeholder="@@crud-disponibilidade:input:descricao:placeholder"
							label="Descrição*"
							placeholder="Adicione informações importantes referente à locação do ambiente..."></pacto-cat-form-input>
					</div>
				</div>

				<div class="row" style="margin-bottom: 96px">
					<div class="col-md-12">
						<label>Imagem do Banner</label>
						<pacto-cat-file-input
							#fileInputComponent
							[control]="formGroup.get('base64Imagem')"
							[formatosValidos]="'(jpeg|jpg|png)$'"
							[formatos]="'.JPG, .JPEG, .PNG'"
							[imageHeight]="150"
							[imageWidth]="150"
							[nomeControl]="formGroup.get('formControlNomeImagem')"
							[urlImage]="urlImagem"
							label="Imagem"></pacto-cat-file-input>
					</div>
				</div>

				<div class="actions">
					<button
						[disabled]="!isFormGroupValid"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula1"
						pactoCatStepNext>
						Avançar
						<span class="pct pct-chevron-right"></span>
					</button>

					<button
						class="btn btn-secondary"
						i18n="@@buttons:cancelar"
						pactoCatStepPrevious>
						<span class="pct pct-chevron-left"></span>
						Voltar
					</button>
				</div>
			</pacto-cat-step>

			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Data e horários</ng-template>
				<div class="row margin-bottom-40">
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataInicial')"
							i18n-label="@@disponibilidade-form:label-data-inicial"
							label="Data inicial"></pacto-cat-datepicker>
					</div>
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataFinal')"
							i18n-label="@@disponibilidade-form:label-data-final"
							label="Data final"></pacto-cat-datepicker>
					</div>
				</div>

				<pacto-title-card [title]="'Horários cadastrados'">
					<pacto-relatorio
						#tableData
						(actionLinkFooterClick)="editarHorariosSelecionados()"
						(filterChangeEvent)="filterChangeEvent($event)"
						(iconClick)="actionClickHandler($event)"
						(pageChangeEvent)="pageChangeEvent($event)"
						(pageSizeChange)="pageSizeChange($event)"
						(sortEvent)="ordenarHorarios($event)"
						[actionTitulo]="'Ações'"
						[customActionsRight]="customFilter"
						[iconActionLinkFooter]="'pct-edit'"
						[itensPerPage]="itensPerPage"
						[showShare]="true"
						[table]="table"></pacto-relatorio>
				</pacto-title-card>

				<div class="actions">
					<button
						[disabled]="!isFormGroupValid"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula2"
						pactoCatStepNext>
						Avançar
						<span class="pct pct-chevron-right"></span>
					</button>
				</div>
			</pacto-cat-step>
			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Adicionais</ng-template>
				<pacto-cat-card-plain>
					<div class="row">
						<span style="font-weight: 600">Produtos e serviços</span>
					</div>
					<pacto-cat-table-editable
						#tableProdutos
						(delete)="deleteProdSugeridos($event)"
						(isEditingOrAddingItem)="isEditinOrAdding.emit($event)"
						(pageChangeEvent)="pageChangeEventProdSugeridos($event)"
						(pageSizeChange)="pageSizeChangeProdSugeridos($event)"
						(sortEvent)="sortEventProdSuge($event)"
						[actionTitle]="'Ações'"
						[isEditable]="true"
						[itensPerPage]="itensPerPageProdSuge"
						[newLineTitle]="'Adicionar produto/serviço'"
						[showAddRow]="true"
						[table]="tableProdSuge"
						idSuffix="table-produto"></pacto-cat-table-editable>

					<div class="actions">
						<button
							(click)="submitHandler()"
							[disabled]="!isFormGroupValid"
							class="btn btn-primary"
							i18n="@@buttons:salvar"
							id="btn-add-aula3">
							Concluir locação
						</button>

						<button
							class="btn btn-secondary"
							i18n="@@buttons:cancelar"
							pactoCatStepPrevious>
							<span class="pct pct-chevron-left"></span>
							Voltar
						</button>
					</div>
				</pacto-cat-card-plain>
			</pacto-cat-step>
		</pacto-cat-stepper>
	</pacto-cat-card-plain>

	<pacto-cat-tabs-transparent *ngIf="operation === 'edit'">
		<ng-template label="Informações" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('nome')"
							[id]="'nome-locacao-input'"
							i18n-label="@@crud-locacao:input:nome:label"
							i18np-placeholder="@@crud-locacao:input:nome:placeholder"
							label="Nome da locação*"
							placeholder="Adicione um nome para sua disponibilidade"></pacto-cat-form-input>
					</div>

					<div class="col-md-6">
						<pacto-cat-input-color [formControl]="formGroup.get('cor')">
							<label class="label-cor-disponibilidade">Cor da locação*</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="row">
					<div class="col-md-3">
						<pacto-cat-form-select
							(change)="limparValoresTipoHorario()"
							[control]="formGroup.get('tipoHorario')"
							[id]="'tipoHorario-select'"
							[items]="tipoHorarios"
							i18n-label="@@crud-locacao:select:tipoHorario:label"
							idKey="id"
							label="Tipo de horário*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.PRE_DEFINIDO
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('duracaoMinutos')"
							[id]="'duracao'"
							i18n-label="@@crud-locacao:select:duracao:label"
							label="Duração em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.LIVRE
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('tempoMinimoMinutos')"
							[id]="'duracaoMinima'"
							i18n-label="@@crud-locacao:select:tempoMinimo:label"
							label="Tempo mínimo em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.PLAY
						"
						class="col-md-3">
						<pacto-cat-form-select
							(change)="limparItensValidacao()"
							[control]="formGroup.get('tipoValidacao')"
							[id]="'tipoValidacao-select'"
							[items]="tipoValidacoes"
							i18n-label="@@crud-locacao:select:tipoValidacao:label"
							idKey="id"
							label="Tipo de validação*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.PRE_DEFINIDO ||
							formGroup.get('tipoHorario').value == tipoHorarioEnum.LIVRE
						"
						class="col-md-6">
						<pacto-cat-form-select-filter
							[control]="formGroup.get('produto')"
							[endpointUrl]="produtoLocacaoUrl"
							[id]="'produto-select'"
							[paramBuilder]="paramBuilder"
							[resposeParser]="responseParserProduto"
							i18n-label="@@crud-locacao:select:itensValidacao:label"
							idKey="codigo"
							label="Produto*"
							mensagem="Informe uma idade válida."
							labelKey="descricao"></pacto-cat-form-select-filter>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoValidacao').value &&
							formGroup.get('tipoValidacao').value != tipoValidacaoEnum.LIVRE &&
							formGroup.get('tipoHorario').value &&
							formGroup.get('tipoHorario').value == tipoHorarioEnum.PLAY
						"
						class="col-md-6">
						<pacto-cat-form-multi-select-filter
							[autocomplete]="'off'"
							[control]="formGroup.get('itensValidacao')"
							[endpointUrl]="itensValidacaoUrl"
							[idKey]="
								formGroup.get('tipoValidacao').value == tipoValidacaoEnum.PLANO
									? 'plano'
									: 'produto'
							"
							[id]="'itensValidacao-select'"
							[labelKey]="'descricao'"
							[paramBuilder]="paramBuilder"
							[resposeParser]="responseParser"
							i18n-label="@@crud-locacao:select:itensValidacao:label"
							label="Itens de validação*"></pacto-cat-form-multi-select-filter>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('descricao')"
							[id]="'descricao'"
							i18n-label="@@crud-locacao:select:descricao:label"
							i18np-placeholder="@@crud-disponibilidade:input:descricao:placeholder"
							label="Descrição*"
							placeholder="Adicione informações importantes referente à locação do ambiente..."></pacto-cat-form-input>
					</div>
				</div>

				<div class="row" style="margin-bottom: 96px">
					<div class="col-md-12">
						<label>Imagem do Banner</label>
						<pacto-cat-file-input
							#fileInputComponent
							[control]="formGroup.get('base64Imagem')"
							[formatosValidos]="'(jpeg|jpg|png)$'"
							[formatos]="'.JPG, .JPEG, .PNG'"
							[imageHeight]="150"
							[imageWidth]="150"
							[nomeControl]="formGroup.get('formControlNomeImagem')"
							[urlImage]="urlImagem"
							label="Imagem"></pacto-cat-file-input>
					</div>
				</div>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
		<ng-template label="Data e horários" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row margin-bottom-40">
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataInicial')"
							i18n-label="@@disponibilidade-form:label-data-inicial"
							label="Data inicial"></pacto-cat-datepicker>
					</div>
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataFinal')"
							i18n-label="@@disponibilidade-form:label-data-final"
							label="Data final"></pacto-cat-datepicker>
					</div>
				</div>

				<pacto-title-card [title]="'Horários cadastrados'">
					<pacto-relatorio
						#tableData
						(actionLinkFooterClick)="editarHorariosSelecionados()"
						(filterChangeEvent)="filterChangeEvent($event)"
						(iconClick)="actionClickHandler($event)"
						(pageChangeEvent)="pageChangeEvent($event)"
						(pageSizeChange)="pageSizeChange($event)"
						(sortEvent)="ordenarHorarios($event)"
						[actionTitulo]="'Ações'"
						[customActionsRight]="customFilter"
						[iconActionLinkFooter]="'pct-edit'"
						[itensPerPage]="itensPerPage"
						[showShare]="true"
						[table]="table"></pacto-relatorio>
				</pacto-title-card>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>

		<ng-template label="Adicionais" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row">
					<span style="font-weight: 600">Produtos e serviços</span>
				</div>
				<pacto-cat-table-editable
					#tableProdutos
					(delete)="deleteProdSugeridos($event)"
					(isEditingOrAddingItem)="isEditinOrAdding.emit($event)"
					(pageChangeEvent)="pageChangeEventProdSugeridos($event)"
					(pageSizeChange)="pageSizeChangeProdSugeridos($event)"
					(sortEvent)="sortEventProdSuge($event)"
					[actionTitle]="'Ações'"
					[isEditable]="true"
					[itensPerPage]="itensPerPageProdSuge"
					[newLineTitle]="'Adicionar produto/serviço'"
					[showAddRow]="true"
					[table]="tableProdSuge"
					idSuffix="table-produto"></pacto-cat-table-editable>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
	</pacto-cat-tabs-transparent>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@crud-disponibilidades:create-success" xingling="createSuccess">
		Locação criada com sucesso.
	</span>
	<span i18n="@@crud-disponibilidades:create-edit" xingling="editSuccess">
		Locação editada com sucesso.
	</span>
	<span
		i18n="@@crud-disponibilidades:campo-obrigatorio"
		xingling="campoObrigatorio">
		Campos obrigatórios não preenchido.
	</span>
	<span
		i18n="@@crud-disponibilidades:campo-obrigatorio:inserir-horario"
		xingling="inserirHorario">
		Inserir pelo menos um horário na lista
	</span>
	<span i18n="@@crud-disponibilidades:validacao:data" xingling="validacaoData">
		Data inicial não pode ser superior a data final
	</span>
	<span
		i18n="@@crud-disponibilidades:validacao:horario"
		xingling="validacaoHorario">
		Horário cadastrado invalido, tente novamente
	</span>
	<span
		i18n="@@crud-disponibilidades:validacao:dia semana"
		xingling="validacaoDiaSemana">
		Selecione ao menos um dia da semana.
	</span>
	<span xingling="horarionaoPodeSerRemovido">
		Não é possível excluir o horário, contém aula confirmada.
	</span>
	<span xingling="erro_aula_url_virtual">Informe a URL da aula virtual.</span>
	<span i18n="@@crud-aulas:erro_incluir_aula" xingling="erro_incluir_aula">
		Ocorreu um erro ao incluir a aula, tente novamente em alguns instantes!
	</span>
	<span
		i18n="@@crud-ambientes-disponibilidades:success:create"
		xingling="successCreate">
		Ambiente criado com sucesso.
	</span>
	<span i18n="@@crud-horarios:edit-success" xingling="horarioEditSuccess">
		Horario editado com sucesso.
	</span>
</pacto-traducoes-xingling>

<ng-template #customFilter>
	<pacto-custom-data-grid-filter #filter>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearVigencia()"
			[showClearFilter]="formGroupFilter.get('vigencia').value">
			<span>Situação</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroupFilter.get('vigencia')"
					[idKey]="'id'"
					[labelKey]="'nome'"
					[options]="vigencia"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearProfessores()"
			[showClearFilter]="formGroupFilter.get('responsavel').value">
			<span>Vinculos</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroupFilter.get('responsavel')"
					[endpointUrl]="_rest.buildFullUrlTreino('usuarios/colaboradores')"
					[labelKey]="'nome'"
					label="Responsável"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearAmbientes()"
			[showClearFilter]="formGroupFilter.get('ambiente').value">
			<span>Local</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroupFilter.get('ambiente')"
					[labelKey]="'nome'"
					[options]="ambientes.content"
					label="Ambiente"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearDiasSemana()"
			[showClearFilter]="
				formGroupFilter.get('segFc').value ||
				formGroupFilter.get('terFc').value ||
				formGroupFilter.get('quaFc').value ||
				formGroupFilter.get('quiFc').value ||
				formGroupFilter.get('sexFc').value ||
				formGroupFilter.get('sabFc').value ||
				formGroupFilter.get('domFc').value
			">
			<span>Dia da semana</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('segFc')"
					[label]="'Seg'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('terFc')"
					[label]="'Ter'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('quaFc')"
					[label]="'Qua'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('quiFc')"
					[label]="'Qui'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('sexFc')"
					[label]="'Sex'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('sabFc')"
					[label]="'Sab'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('domFc')"
					[label]="'Dom'"></pacto-cat-checkbox>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearHorarios()"
			[showClearFilter]="
				formGroupFilter.get('horaInicial').value ||
				formGroupFilter.get('horaFinal').value
			">
			<span>Horário</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-form-input
					[control]="formGroupFilter.get('horaInicial')"
					[id]="'input-horaInicial'"
					[label]="'Hora inicial'"
					[placeholder]="'00h00'"
					[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
				<pacto-cat-form-input
					[control]="formGroupFilter.get('horaFinal')"
					[id]="'input-horaFinal'"
					[label]="'Hora final'"
					[placeholder]="'00h00'"
					[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<div class="buttons-filter-border">
			<pacto-cat-button
				(click)="limparFiltros()"
				[type]="'SECUNDARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Limpar filtros"></pacto-cat-button>
			<pacto-cat-button
				(click)="btnClickFiltrarTabela($event)"
				[type]="'PRIMARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Consultar"></pacto-cat-button>
		</div>
	</pacto-custom-data-grid-filter>

	<pacto-cat-button
		(click)="btnClickHandler()"
		[icon]="'pct pct-plus'"
		class="btnCadastrar"
		label="Adicionar horário"
		size="LARGE"
		type="PRIMARY_ADD"></pacto-cat-button>
</ng-template>

<!--table columns-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar horário</span>
</ng-template>
<ng-template #codigoColumnName>
	<span i18n="@@crud-locacao:codigo:title:table">Código</span>
</ng-template>
<ng-template #professorColumnName>
	<span i18n="@@crud-locacao:professor:title:table">Responsável</span>
</ng-template>
<ng-template #disponibilidadeColumnName>
	<span i18n="@@crud-locacao:disponibilidade:title:table">Disponibilidade</span>
</ng-template>
<ng-template #ambienteColumnName>
	<span i18n="@@crud-locacao:ambiente:title:table">Ambiente</span>
</ng-template>
<ng-template #funcionamentoColumnName>
	<span i18n="@@crud-locacao:funcionamento:title:table">Duração</span>
</ng-template>
<ng-template #diaColumnName>
	<span i18n="@@crud-locacao:dia:title:table">Dias</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-locacao:table:status">Ações</span>
</ng-template>

<ng-template #columnCodigoProdSugeName>
	<span i18n="@@crud-locacao:tableProdSuge:codigo">Código</span>
</ng-template>
<ng-template #columnProdutoName>
	<span i18n="@@crud-locacao:tableProdSuge:produto">Produto</span>
</ng-template>
<ng-template #columnValorProdutoName>
	<span i18n="@@crud-locacao:tableProdSuge:valorProduto">Valor Produto</span>
</ng-template>
<ng-template #columnObrigatorioName>
	<span i18n="@@crud-locacao:tableProdSuge:campoObrigatorio">
		Campo Obrigatorio
	</span>
</ng-template>
<!--End table columns-->

<!--Celulas para formatação-->
<ng-template #codigoCelula let-item="item">
	{{ item.codigo }}
</ng-template>
<ng-template #professorCelula let-item="item">
	{{ item.responsavel?.nome }}
</ng-template>
<ng-template #ambienteCelula let-item="item">
	{{ ambientesConcatenado(item.ambientes) }}
</ng-template>
<ng-template #disponibilidadeCelula let-item="item">
	{{
		item.horaInicio.replace(":", "h") + " às " + item.horaFim.replace(":", "h")
	}}
</ng-template>
<ng-template #funcionamentoCelula>
	{{
		tipoHorarioName(
			locacaoCreateEdit && locacaoCreateEdit.tipoHorario
				? locacaoCreateEdit.tipoHorario
				: formGroup.get("tipoHorario").value
		)
	}}
</ng-template>
<ng-template #diasCelula let-item="item">
	{{ convertDiaSemanaString(item.diaSemana) }}
</ng-template>
<!--fim-->

<!--tooltip icons-->
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<!--End tooltip icons-->

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove-modal:title">
	Inativar Horario Disponibilidade ?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:success">
	Horario disponibilidade inativado com sucesso.
</span>
<span
	#removeError
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:error">
	Não foi possível inativar, existem alunos matriculados
</span>
<span
	#actionNoUndone
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:actionNoUndone">
	Essa ação não poderá ser desfeita
</span>
<span
	#editOrExcludeHorarioDispLarge
	[hidden]="true"
	i18n="
		@@crud-turmas-horariosDisponibilidade:remove:editOrExcludeHorarioDispLarge">
	Existem alunos confirmados já nesse horário e eles serão mantidos, caso queira
	retirar esses alunos é preciso ir na agenda e removê-los manualmente ou
	editá-los para um novo horário
</span>
