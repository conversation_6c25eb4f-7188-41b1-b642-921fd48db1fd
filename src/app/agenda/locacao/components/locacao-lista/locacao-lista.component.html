<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'AGENDA',
			menu: 'Loca<PERSON>'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[customActionsRight]="customFilter"
			[quickSearchPlaceHolderCustom]="'Busque pelo nome da locação'"
			[showBtnAdd]="false"
			[showShare]="true"
			[table]="table"
			actionTitulo="Ações"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-locacao:input:nome:label">Nome</span>
</ng-template>
<ng-template #descricaoColumnName>
	<span i18n="@@crud-locacao:select:descricao:label">Descrição</span>
</ng-template>
<ng-template #tipoHorarioColumnName>
	<span i18n="@@crud-locacao:table:tipoHorario:title">Tipo de horário</span>
</ng-template>
<ng-template #diasSemanaColumnName>
	<span i18n="@@crud-locacao:input:diasSemana:label">Dias</span>
</ng-template>

<ng-template #customFilter>
	<pacto-custom-data-grid-filter #filter>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearVigencia()"
			[showClearFilter]="formGroup.get('vigencia').value">
			<span>Vigência</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('vigencia')"
					[idKey]="'id'"
					[labelKey]="'nome'"
					[options]="vigencia"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearTipoHorarios()"
			[showClearFilter]="
				formGroup.get('tipoHorarioPreDefinidoFc').value ||
				formGroup.get('tipoHorarioLivreFc').value ||
				formGroup.get('tipoHorarioPlayFc').value
			">
			<span>Tipo de horário</span>
			<ng-container pactoDataGridFilterOptionBody>
				<!--        <pacto-cat-checkbox-->
				<!--            [label]="'Play'"-->
				<!--            [control]="formGroup.get('tipoHorarioPlayFc')"-->
				<!--            [checkBlue]="true"-->
				<!--        ></pacto-cat-checkbox>-->
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('tipoHorarioLivreFc')"
					[label]="'Livre'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('tipoHorarioPreDefinidoFc')"
					[label]="'Pré-definido'"></pacto-cat-checkbox>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearDiasSemana()"
			[showClearFilter]="
				formGroup.get('segFc').value ||
				formGroup.get('terFc').value ||
				formGroup.get('quaFc').value ||
				formGroup.get('quiFc').value ||
				formGroup.get('sexFc').value ||
				formGroup.get('sabFc').value ||
				formGroup.get('domFc').value
			">
			<span>Dia da semana</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('segFc')"
					[label]="'Segunda'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('terFc')"
					[label]="'Terça'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('quaFc')"
					[label]="'Quarta'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('quiFc')"
					[label]="'Quinta'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('sexFc')"
					[label]="'Sexta'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('sabFc')"
					[label]="'Sábado'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('domFc')"
					[label]="'Domingo'"></pacto-cat-checkbox>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<div class="buttons-filter-border">
			<pacto-cat-button
				(click)="limparFiltros()"
				[type]="'SECUNDARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Limpar filtros"></pacto-cat-button>
			<pacto-cat-button
				(click)="btnClickFiltrarTabela()"
				[type]="'PRIMARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Consultar"></pacto-cat-button>
		</div>
	</pacto-custom-data-grid-filter>

	<pacto-cat-button
		(click)="btnClickHandler()"
		[icon]="'pct pct-plus'"
		class="btnCadastrar"
		label="Cadastrar locação"
		size="LARGE"
		type="PRIMARY_ADD"></pacto-cat-button>
</ng-template>

<!--Celulas para formatação-->
<ng-template #nomeCelula let-item="item">
	{{ item.nome }}
</ng-template>
<ng-template #descricaoCelula let-item="item">
	{{ item.descricao }}
</ng-template>
<ng-template #tipoHorarioCelula let-item="item">
	{{ item.tipoHorarioDescricao }}
</ng-template>
<ng-template #diasSemanaCelula let-item="item">
	{{ item.dias }}
</ng-template>
<!--fim-->

<!--tooltip icons-->
<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-locacao:remover:tooltip-icon">
	Remover
</span>
<!-- FILTER LABELS -->
<ng-template #dataInicioLabel i18n="@@crud-locacao:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template #dataFimLabel i18n="@@crud-locacao:data-fim:filtro">
	Data Fim
</ng-template>

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-locacao:remove-modal:title">
	Remover Locação ?
</span>
<span #removeModalBody [hidden]="true" i18n="@@crud-locacao:remove-modal:body">
	Deseja remover a locação?
</span>
<span #removeSuccess [hidden]="true" i18n="@@crud-locacao:remove:success">
	Locação removida com sucesso.
</span>
<span #removeError [hidden]="true" i18n="@@crud-locacao:remove:error">
	Não foi possível excluir, existem alunos matriculados
</span>
