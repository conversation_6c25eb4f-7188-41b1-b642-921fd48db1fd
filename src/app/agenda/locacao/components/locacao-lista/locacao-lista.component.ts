import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { FormControl, FormGroup } from "@angular/forms";

import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
	TreinoApiLocacaoService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	SelectFilterResponseParser,
	CustomDataGridFilterComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { DatePipe } from "@angular/common";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";

@Component({
	selector: "pacto-locacao-lista",
	templateUrl: "./locacao-lista.component.html",
	styleUrls: ["./locacao-lista.component.scss"],
})
export class LocacaoListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;

	@ViewChild("tipoHorarioColumnName", { static: true }) tipoHorarioColumnName;
	@ViewChild("tipoValidacaoColumnName", { static: true })
	tipoValidacaoColumnName;
	@ViewChild("descricaoColumnName", { static: true }) descricaoColumnName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("diasSemanaColumnName", { static: true }) diasSemanaColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("descricaoCelula", { static: true }) descricaoCelula;
	@ViewChild("nomeCelula", { static: true }) nomeCelula;
	@ViewChild("tipoValidacaoCelula", { static: true }) tipoValidacaoCelula;
	@ViewChild("tipoHorarioCelula", { static: true }) tipoHorarioCelula;
	@ViewChild("diasSemanaCelula", { static: true }) diasSemanaCelula;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;

	@ViewChild("filter", { static: false })
	filter: CustomDataGridFilterComponent;

	loading = false;
	ready = false;

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private ambienteService: TreinoApiAmbienteService,
		private modalService: ModalService,
		private treinoApiLocacaoService: TreinoApiLocacaoService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService
	) {}

	table: PactoDataGridConfig;

	formGroup: FormGroup = new FormGroup({
		vigencia: new FormControl(),
		comportamentoFc: new FormControl(),
		tipoValidacaoFc: new FormControl(),
		itemValidacaoFc: new FormControl(),
		tipoHorarioPreDefinidoFc: new FormControl(false),
		tipoHorarioLivreFc: new FormControl(false),
		tipoHorarioPlayFc: new FormControl(false),
		segFc: new FormControl(false),
		terFc: new FormControl(false),
		quaFc: new FormControl(false),
		quiFc: new FormControl(false),
		sexFc: new FormControl(false),
		sabFc: new FormControl(false),
		domFc: new FormControl(false),
	});

	vigencia: Array<any> = [
		{ id: "VIGENTE", nome: "Vigente" },
		{ id: "NAO_VIGENTE", nome: "Não vigente" },
	];

	ngOnInit() {
		this.ready = true;
		this.configTable();
		this.cd.detectChanges();
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.NOVA_TELA_DISPONIBILIDADE
		);
	}

	btnClickFiltrarTabela() {
		this.tableData.addFilter("filtrosNew", this.formGroup.getRawValue());
		this.tableData.reloadData();
		this.filter.close();
	}

	btnClickHandler() {
		this.router.navigate(["agenda", "locacao", "adicionar"]);
	}

	btnEditHandler(item) {
		this.router.navigate(["agenda", "locacao", item.codigo]);
	}

	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("locacoes"),
			logUrl: this.rest.buildFullUrl("log/locacoes"),
			quickSearch: true,
			showFilters: false,
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
					celula: this.nomeCelula,
				},
				{
					nome: "descricao",
					titulo: this.descricaoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "descricao",
					celula: this.descricaoCelula,
				},
				{
					nome: "tipoHorarioDescricao",
					titulo: this.tipoHorarioColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "tipoHorarioDescricao",
					celula: this.tipoHorarioCelula,
				},
				{
					nome: "diaSemana",
					titulo: this.diasSemanaColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "dias",
					celula: this.diasSemanaCelula,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.validarVigencia(row.dataFinal),
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	validarVigencia(fim): boolean {
		const fimDate = new Date(fim);
		const endOfFimDay = new Date(
			fimDate.getFullYear(),
			fimDate.getMonth(),
			fimDate.getDate(),
			23,
			59,
			59,
			999
		);
		let ativo = true;
		if (new Date().getTime() > endOfFimDay.getTime()) {
			ativo = false;
		}
		return ativo;
	}

	removeHandler(item) {
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			handler.result
				.then(() => {
					this.treinoApiLocacaoService.deletar(item.codigo).subscribe(
						(resp) => {
							if (resp) {
								this.snotifyService.success(removeSuccess);
								if (this.tableData) {
									this.tableData.reloadData();
								}
							} else {
								this.snotifyService.error(removeError);
							}
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta && err.meta.messageValue) {
								this.snotifyService.error(err.meta.messageValue);
							}
						}
					);
				})
				.catch(() => {});
		});
	}

	clearVigencia() {
		this.formGroup.get("vigencia").setValue(null);
	}

	clearTipoHorarios() {
		this.formGroup.get("tipoHorarioPreDefinidoFc").setValue(null);
		this.formGroup.get("tipoHorarioLivreFc").setValue(null);
		this.formGroup.get("tipoHorarioPlayFc").setValue(null);
	}

	clearDiasSemana() {
		this.formGroup.get("segFc").setValue(null);
		this.formGroup.get("terFc").setValue(null);
		this.formGroup.get("quaFc").setValue(null);
		this.formGroup.get("quiFc").setValue(null);
		this.formGroup.get("sexFc").setValue(null);
		this.formGroup.get("sabFc").setValue(null);
		this.formGroup.get("domFc").setValue(null);
	}

	limparFiltros() {
		this.formGroup.reset();
	}
}
