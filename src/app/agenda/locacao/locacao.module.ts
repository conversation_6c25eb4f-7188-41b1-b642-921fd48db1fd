import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
	TreinoApiAulaService,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { LayoutModule } from "../../../../projects/adm/src/app/layout/layout.module";
import { LocacaoListaComponent } from "./components/locacao-lista/locacao-lista.component";
import { LocacaoCrudComponent } from "./components/locacao-crud/locacao-crud.component";
import { LocacaoHorarioCrudModalComponent } from "./components/locacao-crud/locacao-horario-crud/locacao-horario-crud-modal.component";
import { ConfirmLocacaoCrudModalComponent } from "./components/locacao-crud/modal-confirm-locacao-edit/confirm-locacao-crud-modal.component";
import { HorarioRemoveEditConfirmModalComponent } from "./components/locacao-crud/locacao-horario-crud/modal-horario-remove-edit-confirm/horario-remove-edit-confirm-modal.component";
import { LocacaoHorarioCrudV2ModalComponent } from "./components/locacao-crud/locacao-horario-crud/locacao-horario-crud-v2-modal.component";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS,
	true
);

const routes: Routes = [
	{
		path: "",
		component: LocacaoListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "adicionar",
		component: LocacaoCrudComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: ":id",
		component: LocacaoCrudComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		MatSlideToggleModule,
		LayoutModule,
	],
	declarations: [
		LocacaoListaComponent,
		LocacaoCrudComponent,
		LocacaoHorarioCrudModalComponent,
		LocacaoHorarioCrudV2ModalComponent,
		ConfirmLocacaoCrudModalComponent,
		HorarioRemoveEditConfirmModalComponent,
	],
	entryComponents: [
		LocacaoHorarioCrudModalComponent,
		LocacaoHorarioCrudV2ModalComponent,
		ConfirmLocacaoCrudModalComponent,
		HorarioRemoveEditConfirmModalComponent,
	],
	providers: [TreinoApiAulaService],
})
export class LocacaoModule {}
