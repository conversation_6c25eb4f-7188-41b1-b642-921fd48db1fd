import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { ModalidadeListaComponent } from "./components/modalidade-lista/modalidade-lista.component";
import { ModalidadeEditModalComponent } from "./components/modalidade-edit-modal/modalidade-edit-modal.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { ModalidadeEditModalModule } from "./components/modalidade-edit-modal/modalidade-edit-modal.module";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.MODALIDADES,
	true
);

const routes: Routes = [
	{
		path: "",
		component: ModalidadeListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	imports: [
		BaseSharedModule,
		RouterModule.forChild(routes),
		CommonModule,
		ModalidadeEditModalModule,
	],
	entryComponents: [ModalidadeEditModalComponent],
	declarations: [ModalidadeListaComponent],
})
export class ModalidadeModule {}
