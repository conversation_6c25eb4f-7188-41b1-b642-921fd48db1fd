.container-cores {
	display: flex;
	flex-wrap: wrap;

	.quadro-cores {
		width: 40px;
		height: 40px;
		margin: 5px 5px 5px 0px;
		cursor: pointer;
		position: relative;

		&:last-child {
			margin-right: 0px;
		}

		&.selected-color {
			border: 2px solid #ffffff;
			box-shadow: 0px 0px 2px 2px #c7c7c7;

			.triangle {
				position: absolute;
				right: -4px;
				bottom: -1px;
				width: 0;
				height: 0;
				transform: rotate(135deg);
				border-left: 6px solid transparent;
				border-right: 6px solid transparent;
				border-bottom: 6px solid #ffffff;
			}
		}
	}
}

.label-modal {
	display: block;
	width: 100%;
	border-bottom: 1px dotted #c2c2c2;
	padding-bottom: 5px;
}
