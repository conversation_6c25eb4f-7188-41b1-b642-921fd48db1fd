<div class="modal-header">
	<h4 class="modal-title">
		<span *ngIf="verificaTitle()" i18n="@@crud-modalidade:modal:create:title">
			Criar Modalidade
		</span>
		<span *ngIf="!verificaTitle()" i18n="@@crud-modalidade:modal:edit:title">
			Editar Modalidade
		</span>
	</h4>
	<button
		(click)="dismiss()"
		aria-label="Close"
		class="close modal-item"
		type="button">
		<span aria-hidden="true">&times;</span>
	</button>
</div>
<div class="modal-body">
	<pacto-input
		[control]="formControl"
		[disabled]="integracaoZW"
		[id]="'input-nome-modalidade'"
		[name]="'nome'"
		i18n-label="@@crud-modalidade:input:nome:label"
		i18n-mensagemn="@@crud-modalidade:input:nome:mensagem"
		i18n-placeholder="@@crud-modalidade:input:nome:placeholder"
		label="Nome"
		mensagem="Fornecer um nome com pelo menos 3 digitos."
		placeholder="Nome da modalidade"></pacto-input>

	<div class="form-group">
		<label
			class="control-label label-modal"
			i18n="@@crud-modalidade:cores:label">
			Cores
		</label>
		<div class="container-cores">
			<div
				(click)="selectColorHandler(cor)"
				*ngFor="let cor of modalidadeCores"
				[ngClass]="{ 'selected-color': selectedColor === cor.id }"
				[ngStyle]="{ 'background-color': cor.valor }"
				class="quadro-cores"
				id="cor-id-{{ cor.id }}">
				<div class="triangle"></div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<button
		(click)="dismiss()"
		class="btn btn-secondary modal-item"
		i18n="@@buttons:cancelar"
		type="button">
		Cancelar
	</button>
	<button
		(click)="close()"
		class="btn btn-primary"
		i18n="@@buttons:salvar"
		id="btn-salvar-modalidade"
		type="button">
		Salvar
	</button>
</div>

<span
	#camposObrigatorios
	[hidden]="true"
	i18n="@@crud-modalidade:mensagem:campos-obrigatorio">
	Campos obrigatorio não preenchido!
</span>
<span
	#selecioneCor
	[hidden]="true"
	i18n="@@crud-modalidade:mensagem:selecione-cor">
	Selecione ao menos uma cor.
</span>
