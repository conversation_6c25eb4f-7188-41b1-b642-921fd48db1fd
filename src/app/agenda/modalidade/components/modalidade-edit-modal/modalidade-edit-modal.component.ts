import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	TreinoApiModalidadeService,
	ModalidadeCor,
	ModalidadeCreateEdit,
} from "treino-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modalidade-edit-modal",
	templateUrl: "./modalidade-edit-modal.component.html",
	styleUrls: ["./modalidade-edit-modal.component.scss"],
})
export class ModalidadeEditModalComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;
	@ViewChild("selecioneCor", { static: true }) selecioneCor;

	modalidadeCores: Array<ModalidadeCor> = [];
	statusFormulario = false;
	verificado = false;

	selectedColor;
	modalidade: ModalidadeCreateEdit = new (class
		implements ModalidadeCreateEdit
	{
		corId: string;
		nome: string;
	})();

	formControl: FormControl = new FormControl("", [
		Validators.required,
		Validators.minLength(3),
	]);
	integracaoZW = false;

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal,
		private modalidadeService: TreinoApiModalidadeService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.setCoresPreDefinidas();
	}

	verificaTitle() {
		if (!this.verificado) {
			this.verificado = true;

			this.statusFormulario = this.formControl.value === "";
		}

		return this.statusFormulario;
	}

	dismiss() {
		this.verificado = false;
		this.openModal.dismiss();
	}

	private setCoresPreDefinidas() {
		this.modalidadeService.obterModalidadeCores().subscribe((dados) => {
			this.modalidadeCores = dados;
		});
	}

	selectColorHandler(valor: ModalidadeCor) {
		this.selectedColor = valor.id;
	}

	close() {
		this.formControl.markAsTouched();
		if (this.formControl.valid && this.selectedColor) {
			this.verificado = false;
			this.openModal.close(this.createEntity());
		} else {
			if (!this.formControl.valid) {
				const camposObrigatorios =
					this.camposObrigatorios.nativeElement.innerHTML;
				this.snotifyService.error(camposObrigatorios);
			}
			if (!this.selectedColor) {
				const selecioneCor = this.selecioneCor.nativeElement.innerHTML;
				this.snotifyService.error(selecioneCor);
			}
		}
	}

	get Validators() {
		return Validators;
	}

	private createEntity() {
		this.modalidade.nome = this.formControl.value;
		this.modalidade.corId = this.selectedColor;

		return this.modalidade;
	}
}
