<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'MODALIDADE'
		}"></pacto-breadcrumbs>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="editHandler($event)"
			[sessionService]="sessionService"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="modalidade"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud-modalidade:title">Modalidade</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud-modalidade:description">
		Gere<PERSON>ie as modalidades cadastradas
	</span>
</ng-template>
<!--title table end-->
<!--buttons-->
<ng-template #buttonAdicionar>
	<span i18n="@@crud-modalidade:adicionar">Adicionar</span>
</ng-template>
<!--buttons end-->
<!--tooltip icons-->
<span #tooltipEdit [hidden]="true" i18n="@@crud-modalidade:tooltip-editar">
	Editar modalidade
</span>
<span #tooltipRemove [hidden]="true" i18n="@@crud-modalidade:tooltip-remover">
	Remover modalidade
</span>
<!--tooltip icons end-->
<!--columns table-->
<ng-template #columnNome>
	<span i18n="@@crud-modalidade:table:nome:title">Nome</span>
</ng-template>
<ng-template #columnCor>
	<span>Cor</span>
</ng-template>
<!--column table end-->
<!--result column celula-->
<ng-template #celulaCor let-item="item">
	<div
		[ngStyle]="{ 'background-color': item.cor.valor }"
		class="campo-cor"></div>
</ng-template>
<!--result column celula end-->

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-modalidade:remove-modal:title">
	Remover Modalidade ?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud-modalidade:remove-modal:body">
	Deseja remover a modalidade {{ nomeModalidade }}?
</span>
<span
	#removeModalSuccess
	[hidden]="true"
	i18n="@@crud-modalidade:remove-modal:success">
	Modalidade removida com sucesso.
</span>

<span
	#MensagemCreateSuccess
	[hidden]="true"
	i18n="@@crud-modalidade:create-modal:success">
	Modalidade criada com sucesso.
</span>
<span
	#MensagemEditSuccess
	[hidden]="true"
	i18n="@@crud-modalidade:edit-modal:success">
	Modalidade editado com sucesso.
</span>
