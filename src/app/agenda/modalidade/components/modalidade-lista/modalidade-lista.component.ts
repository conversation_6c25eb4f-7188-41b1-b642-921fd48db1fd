import { Component, OnInit, ViewChild } from "@angular/core";

import { SnotifyService } from "ng-snotify";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

import { ModalidadeEditModalComponent } from "../modalidade-edit-modal/modalidade-edit-modal.component";
import { TreinoApiModalidadeService, Modalidade } from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { PactoDataGridConfig, GridFilterConfig } from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-modalidade-lista",
	templateUrl: "./modalidade-lista.component.html",
	styleUrls: ["./modalidade-lista.component.scss"],
})
export class ModalidadeListaComponent implements OnInit {
	@ViewChild("filtro", { static: false }) filtro;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeModalSuccess", { static: true }) removeModalSuccess;
	@ViewChild("MensagemCreateSuccess", { static: true }) MensagemCreateSuccess;
	@ViewChild("MensagemEditSuccess", { static: true }) MensagemEditSuccess;

	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("buttonAdicionar", { static: true }) buttonAdicionar;
	@ViewChild("columnNome", { static: true }) columnNome;
	@ViewChild("columnCor", { static: true }) columnCor;
	@ViewChild("celulaCor", { static: true }) celulaCor;
	@ViewChild("tooltipEdit", { static: true }) tooltipEdit;
	@ViewChild("tooltipRemove", { static: true }) tooltipRemove;

	modalidades: Array<Modalidade> = [];
	loading = false;
	itemToRemove;
	nomeModalidade = "";

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	integracaoZW = false;

	constructor(
		private modal: NgbModal,
		private rest: RestService,
		private snotifyService: SnotifyService,
		private modalidadeService: TreinoApiModalidadeService,
		private modalService: ModalService,
		public sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.configTable();
	}

	btnClickHandler() {
		const modalref = this.modal.open(ModalidadeEditModalComponent);
		modalref.componentInstance.formControl.setValue("");
		modalref.componentInstance.selectedColor = null;
		modalref.result.then((result) => {
			this.modalidadeService.criarModalidade(result).subscribe(() => {
				const MensagemCreateSuccess =
					this.MensagemCreateSuccess.nativeElement.innerHTML;
				this.snotifyService.success(MensagemCreateSuccess);
				this.fetchData();
			});
		});
	}

	editHandler(item) {
		const modalRef = this.modal.open(ModalidadeEditModalComponent);
		modalRef.componentInstance.formControl.setValue(item.nome);
		modalRef.componentInstance.selectedColor = item.cor.id;
		modalRef.result.then((result) => {
			this.modalidadeService
				.atualizarModalidade(result, item.id)
				.subscribe(() => {
					const MensagemEditSuccess =
						this.MensagemEditSuccess.nativeElement.innerHTML;
					this.snotifyService.success(MensagemEditSuccess);
					this.fetchData();
				});
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		} else if ($event.iconName === "edit") {
			this.editHandler($event.row);
		}
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	private configTable() {
		const tooltipEdit = this.tooltipEdit.nativeElement.innerHTML;
		const tooltipRemove = this.tooltipRemove.nativeElement.innerHTML;

		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("modalidades"),
			logUrl: this.rest.buildFullUrl("log/listar-log-exportacao/modalidade"),
			quickSearch: true,
			buttons: this.integracaoZW
				? null
				: {
						conteudo: this.buttonAdicionar,
						nome: "add",
						id: "nova-modalidade",
				  },
			columns: [
				{
					nome: "cor",
					titulo: this.columnCor,
					mostrarTitulo: false,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.celulaCor,
					campo: "cor",
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: this.integracaoZW
				? [
						{
							nome: "edit",
							iconClass: "fa fa-pencil",
							tooltipText: tooltipEdit,
						},
				  ]
				: [
						{
							nome: "edit",
							iconClass: "fa fa-pencil",
							tooltipText: tooltipEdit,
						},
						{
							nome: "remove",
							iconClass: "fa fa-trash-o",
							tooltipText: tooltipRemove,
						},
				  ],
		});
	}

	removeHandler(item) {
		this.nomeModalidade = item.nome;
		setTimeout(() => {
			const removeModalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const removeModalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeModalSuccess =
				this.removeModalSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(
				removeModalTitle,
				removeModalBody
			);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					this.modalidadeService.removerModalidade(item.id).subscribe(() => {
						this.snotifyService.success(removeModalSuccess);
						this.fetchData();
					});
				})
				.catch(() => {});
		});
	}
}
