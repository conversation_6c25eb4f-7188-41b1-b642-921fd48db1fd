import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { FormControl, FormGroup } from "@angular/forms";

import { SnotifyService } from "ng-snotify";

import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
	TreinoApiDisponibilidadeService,
	ComportamentoEnum,
	TipoHorarioEnum,
	TipoValidacaoEnum,
} from "treino-api";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	SelectFilterResponseParser,
	CustomDataGridFilterComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { DatePipe } from "@angular/common";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { DisponibilidadeRemoveConfirmModalComponent } from "./modal-disponibilidade-remove-confirm/disponibilidade-remove-confirm-modal.component";

@Component({
	selector: "pacto-disponibilidade-lista",
	templateUrl: "./disponibilidade-lista.component.html",
	styleUrls: ["./disponibilidade-lista.component.scss"],
})
export class DisponibilidadeListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeModalBody2", { static: true }) removeModalBody2;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;

	@ViewChild("tipoHorarioColumnName", { static: true }) tipoHorarioColumnName;
	@ViewChild("tipoValidacaoColumnName", { static: true })
	tipoValidacaoColumnName;
	@ViewChild("comportamentoColumnName", { static: true })
	comportamentoColumnName;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("diasSemanaColumnName", { static: true }) diasSemanaColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("comportamentoCelula", { static: true }) comportamentoCelula;
	@ViewChild("nomeCelula", { static: true }) nomeCelula;
	@ViewChild("tipoValidacaoCelula", { static: true }) tipoValidacaoCelula;
	@ViewChild("tipoHorarioCelula", { static: true }) tipoHorarioCelula;
	@ViewChild("diasSemanaCelula", { static: true }) diasSemanaCelula;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;

	@ViewChild("filter", { static: false })
	filter: CustomDataGridFilterComponent;

	loading = false;
	ready = false;

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		private ambienteService: TreinoApiAmbienteService,
		private modalService: ModalService,
		private disponibilidadeService: TreinoApiDisponibilidadeService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService
	) {}

	tipoValidacaoEnum = TipoValidacaoEnum;
	table: PactoDataGridConfig;

	formGroup: FormGroup = new FormGroup({
		vigencia: new FormControl(),
		comportamentoFc: new FormControl(),
		tipoValidacaoFc: new FormControl(),
		itemValidacaoFc: new FormControl(),
		tipoHorarioPreDefinidoFc: new FormControl(false),
		tipoHorarioLivreFc: new FormControl(false),
		tipoHorarioIntervaloTempoFc: new FormControl(false),
		segFc: new FormControl(false),
		terFc: new FormControl(false),
		quaFc: new FormControl(false),
		quiFc: new FormControl(false),
		sexFc: new FormControl(false),
		sabFc: new FormControl(false),
		domFc: new FormControl(false),
	});

	tipoValidacoes: Array<any> = [
		{ id: TipoValidacaoEnum.LIVRE, nome: "Livre" },
		{ id: TipoValidacaoEnum.PLANO, nome: "Plano" },
		{ id: TipoValidacaoEnum.PRODUTO, nome: "Produto" },
	];

	comportamentos: Array<any> = [
		{
			id: ComportamentoEnum.CONTATO_INTERPESSOAL,
			nome: "Contato interpessoal",
		},
		{ id: ComportamentoEnum.PRESCRICAO_TREINO, nome: "Prescrição de treino" },
		{ id: ComportamentoEnum.REVISAO_TREINO, nome: "Revisão de treino" },
		{ id: ComportamentoEnum.RENOVAR_TREINO, nome: "Renovar treino" },
		{ id: ComportamentoEnum.AVALIACAO_FISICA, nome: "Avaliação física" },
	];

	vigencia: Array<any> = [
		{ id: "VIGENTE", nome: "Vigente" },
		{ id: "NAO_VIGENTE", nome: "Não vigente" },
	];

	tipoHorarios: Array<any> = [
		{ id: TipoHorarioEnum.DURACAO_LIVRE, nome: "Livre" },
		{ id: TipoHorarioEnum.DURACAO_PREDEFINIDA, nome: "Pré-definido" },
		{ id: TipoHorarioEnum.INTERVALO_DE_TEMPO, nome: "Intervalo de tempo" },
	];

	ngOnInit() {
		this.ready = true;
		this.configTable();
		this.cd.detectChanges();
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.NOVA_TELA_DISPONIBILIDADE
		);
	}

	btnClickFiltrarTabela() {
		this.tableData.addFilter("filtrosNew", this.formGroup.getRawValue());
		this.tableData.reloadData();
		this.filter.close();
	}

	btnClickHandler() {
		this.router.navigate(["agenda", "disponibilidade", "adicionar"]);
	}

	btnEditHandler(item) {
		this.router.navigate(["agenda", "disponibilidade", item.codigo]);
	}

	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	get itensValidacaoUrl() {
		if (
			this.formGroup.get("tipoValidacaoFc") &&
			this.formGroup.get("tipoValidacaoFc").value
		) {
			return this.rest.buildFullUrl(
				`disponibilidade/${
					this.formGroup.get("tipoValidacaoFc").value.id
				}/itensValidacao`
			);
		}
		return this.rest.buildFullUrl("disponibilidade/0/itensValidacao");
	}

	get tiposEventoUrl() {
		return this.rest.buildFullUrl("disponibilidade/tiposEvento");
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("disponibilidade/all"),
			quickSearch: true,
			showFilters: false,
			logUrl: this.rest.buildFullUrl(`log/disponibilidades/`),
			columns: [
				{
					nome: "obj.nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
					celula: this.nomeCelula,
				},
				{
					nome: "obj.tipoEvento.nome",
					titulo: this.comportamentoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "comportamento",
					celula: this.comportamentoCelula,
				},
				{
					nome: "obj.tipoValidacao",
					titulo: this.tipoValidacaoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "tipoValidacao",
					celula: this.tipoValidacaoCelula,
				},
				{
					nome: "obj.tipoHorario",
					titulo: this.tipoHorarioColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "tipoHorario",
					celula: this.tipoHorarioCelula,
				},
				{
					nome: "obj.diasSemana",
					titulo: this.diasSemanaColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "diasSemana",
					celula: this.diasSemanaCelula,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.validarVigencia(row.dataFinal),
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	validarVigencia(fim): boolean {
		let ativo = true;
		if (fim < new Date().getTime()) {
			ativo = false;
		}
		return ativo;
	}

	removeHandler(item) {
		const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
		const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
		const removeError = this.removeError.nativeElement.innerHTML;
		this.disponibilidadeService
			.existeAgendamentoAlunoHorarioDisponibilidade(0, item.horarios)
			.subscribe((ret) => {
				if (ret && ret.retorno) {
					setTimeout(() => {
						const handler = this.modalService.open(
							modalTitle,
							DisponibilidadeRemoveConfirmModalComponent,
							PactoModalSize.MEDIUM
						);
						handler.componentInstance.possuiAlunoAgendado = true;
						handler.componentInstance.body =
							this.removeModalBody2.nativeElement.innerHTML;
						handler.result
							.then((confirm: boolean) => {
								if (confirm) {
									this.disponibilidadeService
										.removerDisponibilidade(item)
										.subscribe((resp) => {
											if (resp.retorno && resp.retorno === "OK") {
												this.snotifyService.success(removeSuccess);
												if (this.tableData) {
													this.tableData.reloadData();
												}
											} else {
												this.snotifyService.error(removeError);
											}
										});
								}
							})
							.catch(() => {});
					});
				} else {
					setTimeout(() => {
						const handler = this.modalService.open(
							modalTitle,
							DisponibilidadeRemoveConfirmModalComponent,
							PactoModalSize.MEDIUM
						);
						handler.componentInstance.possuiAlunoAgendado = false;
						handler.componentInstance.body =
							this.removeModalBody.nativeElement.innerHTML;
						handler.result
							.then((confirm: boolean) => {
								if (confirm) {
									this.disponibilidadeService
										.removerDisponibilidade(item)
										.subscribe((resp) => {
											if (resp.retorno && resp.retorno === "OK") {
												this.snotifyService.success(removeSuccess);
												if (this.tableData) {
													this.tableData.reloadData();
												}
											} else {
												this.snotifyService.error(removeError);
											}
										});
								}
							})
							.catch(() => {});
					});
				}
			});
	}

	limparItensValidacao() {
		this.formGroup.get("itemValidacaoFc").setValue(null);
	}

	clearVigencia() {
		this.formGroup.get("vigencia").setValue(null);
	}

	clearComportamento() {
		this.formGroup.get("comportamentoFc").setValue(null);
	}

	clearTipoValidacao() {
		this.formGroup.get("tipoValidacaoFc").setValue(null);
		this.formGroup.get("itemValidacaoFc").setValue(null);
	}

	clearTipoHorarios() {
		this.formGroup.get("tipoHorarioPreDefinidoFc").setValue(null);
		this.formGroup.get("tipoHorarioLivreFc").setValue(null);
		this.formGroup.get("tipoHorarioIntervaloTempoFc").setValue(null);
	}

	clearDiasSemana() {
		this.formGroup.get("segFc").setValue(null);
		this.formGroup.get("terFc").setValue(null);
		this.formGroup.get("quaFc").setValue(null);
		this.formGroup.get("quiFc").setValue(null);
		this.formGroup.get("sexFc").setValue(null);
		this.formGroup.get("sabFc").setValue(null);
		this.formGroup.get("domFc").setValue(null);
	}

	limparFiltros() {
		this.formGroup.reset();
	}

	tipoHorariosName(item: number): string {
		if (item !== undefined || item !== null) {
			const i = this.tipoHorarios.find((t) => t.id === item);
			if (i) {
				return i.nome;
			} else {
				return "";
			}
		}
		return "";
	}

	convertDiaSemanaString(diasSemana: string): string {
		let ret = "";
		if (diasSemana !== undefined || diasSemana !== null || diasSemana !== "") {
			diasSemana.split(",").forEach((d) => {
				if (d === "SG") {
					ret = ret + ", Seg";
				} else if (d === "TR") {
					ret = ret + ", Ter";
				} else if (d === "QA") {
					ret = ret + ", Qua";
				} else if (d === "QI") {
					ret = ret + ", Qui";
				} else if (d === "SX") {
					ret = ret + ", Sex";
				} else if (d === "SB") {
					ret = ret + ", Sab";
				} else if (d === "DM") {
					ret = ret + ", Dom";
				}
			});
		}
		return ret.replace(/,/, "");
	}

	tipoValidacaoName(item: number): string {
		if (item !== undefined || item !== null) {
			const i = this.tipoValidacoes.find((t) => t.id === item);
			if (i) {
				return i.nome;
			} else {
				return "";
			}
		}
		return "";
	}

	comportamentoName(item: number): string {
		if (item !== undefined || item !== null) {
			const i = this.comportamentos.find((t) => t.id === item);
			if (i) {
				return i.nome;
			} else {
				return "";
			}
		}
		return "";
	}
}
