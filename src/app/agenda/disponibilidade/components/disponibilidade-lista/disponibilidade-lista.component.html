<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'AGENDA',
			menu: 'Disponibilidade de serviço'
		}"
		class="first"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[customActionsRight]="customFilter"
			[customEmptyContent]="customEmptyDisponibilidade"
			[quickSearchPlaceHolderCustom]="'Busque pelo nome do serviço'"
			[showBtnAdd]="false"
			[showShare]="true"
			[table]="table"
			actionTitulo="Ações"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-disponibilidades:input:nome:label">Nome</span>
</ng-template>
<ng-template #comportamentoColumnName>
	<span i18n="@@crud-disponibilidades:select:comportamento:label">
		Comportamento
	</span>
</ng-template>
<ng-template #tipoValidacaoColumnName>
	<span i18n="@@crud-disponibilidades:table:tipoValidacao:title">
		Tipo de validação
	</span>
</ng-template>
<ng-template #tipoHorarioColumnName>
	<span i18n="@@crud-disponibilidades:table:tipoHorario:title">
		Tipo de horário
	</span>
</ng-template>
<ng-template #diasSemanaColumnName>
	<span i18n="@@crud-disponibilidades:input:diasSemana:label">Dias</span>
</ng-template>

<ng-template #customFilter>
	<pacto-custom-data-grid-filter #filter>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearVigencia()"
			[showClearFilter]="formGroup.get('vigencia').value">
			<span>Vigência</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('vigencia')"
					[idKey]="'id'"
					[labelKey]="'nome'"
					[options]="vigencia"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearComportamento()"
			[showClearFilter]="formGroup.get('comportamentoFc').value">
			<span>Tipo de serviço</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('comportamentoFc')"
					[labelKey]="'nome'"
					[options]="comportamentos"
					label="Comportamento"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearTipoValidacao()"
			[showClearFilter]="
				formGroup.get('tipoValidacaoFc').value ||
				formGroup.get('itemValidacaoFc').value
			">
			<span>Validação</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-select-filter
					(click)="limparItensValidacao()"
					[control]="formGroup.get('tipoValidacaoFc')"
					[labelKey]="'nome'"
					[options]="tipoValidacoes"
					label="Tipo de validação"></pacto-cat-select-filter>
				<pacto-cat-multi-select-filter
					*ngIf="
						formGroup.get('tipoValidacaoFc') &&
						formGroup.get('tipoValidacaoFc').value &&
						formGroup.get('tipoValidacaoFc').value.id != tipoValidacaoEnum.LIVRE
					"
					[control]="formGroup.get('itemValidacaoFc')"
					[endpointUrl]="itensValidacaoUrl"
					[idKey]="
						formGroup.get('tipoValidacaoFc').value.id == tipoValidacaoEnum.PLANO
							? 'plano'
							: 'produto'
					"
					[labelKey]="'descricao'"
					[paramBuilder]="paramBuilder"
					[resposeParser]="responseParser"
					label="Item de validação"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearTipoHorarios()"
			[showClearFilter]="
				formGroup.get('tipoHorarioPreDefinidoFc').value ||
				formGroup.get('tipoHorarioLivreFc').value ||
				formGroup.get('tipoHorarioIntervaloTempoFc').value
			">
			<span>Tipo de horário</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('tipoHorarioPreDefinidoFc')"
					[label]="'Pré-definido'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('tipoHorarioLivreFc')"
					[label]="'Livre'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('tipoHorarioIntervaloTempoFc')"
					[label]="'Intervalo de tempo'"></pacto-cat-checkbox>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearDiasSemana()"
			[showClearFilter]="
				formGroup.get('segFc').value ||
				formGroup.get('terFc').value ||
				formGroup.get('quaFc').value ||
				formGroup.get('quiFc').value ||
				formGroup.get('sexFc').value ||
				formGroup.get('sabFc').value ||
				formGroup.get('domFc').value
			">
			<span>Dia da semana</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('segFc')"
					[label]="'Segunda'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('terFc')"
					[label]="'Terça'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('quaFc')"
					[label]="'Quarta'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('quiFc')"
					[label]="'Quinta'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('sexFc')"
					[label]="'Sexta'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('sabFc')"
					[label]="'Sábado'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroup.get('domFc')"
					[label]="'Domingo'"></pacto-cat-checkbox>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<div class="buttons-filter-border">
			<pacto-cat-button
				(click)="limparFiltros()"
				[type]="'SECUNDARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Limpar filtros"></pacto-cat-button>
			<pacto-cat-button
				(click)="btnClickFiltrarTabela()"
				[type]="'PRIMARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Consultar"></pacto-cat-button>
		</div>
	</pacto-custom-data-grid-filter>

	<pacto-cat-button
		(click)="btnClickHandler()"
		[icon]="'pct pct-plus'"
		class="btnCadastrar"
		label="Cadastrar disponibilidade"
		size="LARGE"
		type="PRIMARY_ADD"></pacto-cat-button>
</ng-template>

<ng-template #customEmptyDisponibilidade>
	<div class="empty-turma">
		<img src="pacto-ui/images/empty-state-turma.svg" />
		<span class="titulo">Lista de disponibilidades vazia</span>
		<span>Ainda não possui nenhuma disponibilidade cadastrada</span>
	</div>
</ng-template>

<!--Celulas para formatação-->
<ng-template #nomeCelula let-item="item">
	{{ item.nome }}
</ng-template>
<ng-template #comportamentoCelula let-item="item">
	{{ comportamentoName(item.comportamento) }}
</ng-template>
<ng-template #tipoValidacaoCelula let-item="item">
	{{ tipoValidacaoName(item.tipoValidacao) }}
</ng-template>
<ng-template #tipoHorarioCelula let-item="item">
	{{ tipoHorariosName(item.tipoHorario) }}
</ng-template>
<ng-template #diasSemanaCelula let-item="item">
	{{ convertDiaSemanaString(item.diasSemana) }}
</ng-template>
<!--fim-->

<!--tooltip icons-->
<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-disponibilidade:remover:tooltip-icon">
	Inativar
</span>
<!-- FILTER LABELS -->
<ng-template #dataInicioLabel i18n="@@crud-disponibilidade:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template #dataFimLabel i18n="@@crud-disponibilidade:data-fim:filtro">
	Data Fim
</ng-template>

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-disponibilidade:remove-modal:title">
	Inativar Disponibilidade ?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud-disponibilidade:remove-modal:body">
	Essa ação não poderá ser desfeita?
</span>
<span
	#removeModalBody2
	[hidden]="true"
	i18n="@@crud-disponibilidade:remove-modal:body2">
	Existem alunos confirmados em horários desta disponibilidade e eles serão
	mantidos, caso queira retirar esses alunos é preciso ir na agenda e removê-los
	manualmente ou editá-los para um novo horário de uma outra disponibilidade
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud-disponibilidade:remove:success">
	Disponibilidade inativada com sucesso.
</span>
<span #removeError [hidden]="true" i18n="@@crud-disponibilidade:remove:error">
	Não foi possível excluir, existem alunos matriculados
</span>
