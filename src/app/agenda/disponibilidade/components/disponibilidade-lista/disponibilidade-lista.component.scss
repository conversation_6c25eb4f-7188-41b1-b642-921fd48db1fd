@import "src/assets/scss/pacto/plataforma-import.scss";

.btnCadastrar {
	margin-left: 10px;
}

:host {
	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}

	::ng-deep.pacto-primary,
	::ng-deep.btn.pacto-primary:hover,
	::ng-deep.show > .btn.pacto-primary.dropdown-toggle.btn-share-label {
		font-weight: bold;
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		::ng-deep.icon-drop {
			border-left: 1px solid $azulim05;
		}
	}

	::ng-deep.pacto-primary,
	::ng-deep.btn.pacto-primary:hover,
	::ng-deep.btn.pacto-primary.dropdown-toggle {
		::ng-deep.icon-drop {
			border-left: 1px solid $azulim05 !important;
		}
	}

	::ng-deep.icon-drop {
		width: 35px !important;
		display: inline-block !important;
	}
}

.buscar-mais-filtros::ng-deep {
	.pacto-button.v2.pacto-primary {
		margin-top: 5px;
		width: 48%;
		margin-left: 5px;
		color: #fff !important;
		background-color: #1e5ff8 !important;
	}

	.pacto-button.v2.pacto-secundary {
		margin-top: 20px;
		width: 48%;
		margin-left: 5px;
		border: 1px solid #1e5ff8;
		color: #1e5ff8;
	}
}

.buttons-filter-border {
	border-top: 1px solid #e6e6e6;
	margin-top: 10px;
}

.sort-icon {
	padding-left: 10px;
}

.iconFeedBackText {
	margin-right: 5px;
}

.feedBackText {
	margin-top: 20px;
	margin-left: 30px;
}

.margin-top-20 {
	margin-top: 20px;
}

.feedback {
	cursor: pointer;
	font-weight: 600;
	padding: 8px 13px;
	border-radius: 4px;
	text-align: right;

	a {
		color: #1e60fa;
	}
}

.actions-row {
	width: 100px;
	text-align: center;

	i {
		cursor: pointer;
		padding-right: 10px;
	}
}

::ng-deep pacto-relatorio .fa-pencil {
	margin-right: 10px;
}

.order {
	cursor: pointer;

	.button-direction {
		display: flex;

		.title-table {
			bottom: 0px;
			left: 0px;
		}

		.icon-order {
			.fa-caret-up {
				display: block;
				height: 10px;
			}

			.fa-caret-down {
				display: block;
				height: 10px;
			}
		}
	}
}

.fa-exclamation-triangle {
	color: $pequizaoPri;
}

.loading-state,
.empty-state {
	text-align: center;
	padding-top: 60px;
	height: 150px;
}

.logs-gympass {
	margin-top: -80px;
	padding: 0 30px;
	height: 50px;
	display: block;

	pacto-cat-button {
		margin-right: 10px;
	}
}

.preview-object {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	border: 1px solid #c7c7c7;
	background-repeat: no-repeat;
	background-position: center;
	background-size: cover;
}

::ng-deep pacto-relatorio {
	.pacto-table-title-block {
		.search {
			input {
				width: 408px;
				padding-left: 50px;
				color: #c7c9cc;
			}
		}
	}
}

.empty-turma {
	display: grid;
	place-items: center;
	gap: 16px;
	margin: 32px;
	text-align: center;
	font-size: 16px;
	font-weight: 400;
	line-height: 18px;

	span {
		width: 450px;
	}

	.titulo {
		font-size: 16px;
		font-weight: 600;
		line-height: 18px;
		letter-spacing: 0.25px;
	}
}
