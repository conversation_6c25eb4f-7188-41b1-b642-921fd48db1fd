<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		(click)="cancelHandler(true)"
		[breadcrumbConfig]="{
			categoryName: !entity ? 'Disponibilidade' : 'Disponibilidade ',
			menu: !entity
				? 'Editar disponibilidade de serviço'
				: 'Nova disponibilidade de serviço',
			menuLink: []
		}"
		class="first"></pacto-breadcrumbs>

	<pacto-cat-card-plain *ngIf="operation === 'create'">
		<pacto-cat-stepper class="force-header-start" style="margin-bottom: 10px">
			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Informações</ng-template>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('nome')"
							[id]="'nome-disponibilidade-input'"
							i18n-label="@@crud-disponibilidade:input:nome:label"
							i18np-placeholder="@@crud-disponibilidade:input:nome:placeholder"
							label="Nome do serviço*"
							placeholder="Adicione um nome para sua disponibilidade"></pacto-cat-form-input>
					</div>

					<div class="col-md-6">
						<pacto-cat-input-color [formControl]="formGroup.get('cor')">
							<label class="label-cor-disponibilidade">Cor do serviço*</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="row baseline">
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('comportamento')"
							[id]="'comportamento-select'"
							[items]="comportamentos"
							i18n-label="@@crud-disponibilidade:select:comportamento:label"
							idKey="id"
							label="Comportamento*"
							labelKey="nome"></pacto-cat-form-select>
					</div>

					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('intervaloDiasEntreAgendamentos')"
							[id]="'intervaloDiasAgendamento'"
							i18n-label="
								@@crud-disponibilidade:select:intervaloDiasAgendamento:label"
							label="Intervalo de dias entre agendamentos"></pacto-cat-form-input-number>
					</div>
					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('intervaloMinimoDiasCasoFalta')"
							[id]="'intervaloMinimoFalta'"
							i18n-label="
								@@crud-disponibilidade:select:intervaloMinimoFalta:label"
							label="Intervalo minimo de dias em caso de falta"></pacto-cat-form-input-number>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							(change)="limparValoresTipoHorario()"
							[control]="formGroup.get('tipoHorario')"
							[id]="'tipoHorario-select'"
							[items]="tipoHorarios"
							i18n-label="@@crud-disponibilidade:select:tipoHorario:label"
							idKey="id"
							label="Tipo de horário*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.DURACAO_PREDEFINIDA
						"
						class="col-md-6">
						<pacto-cat-form-input
							[control]="formGroup.get('duracao')"
							[id]="'duracao'"
							i18n-label="@@crud-disponibilidade:select:duracao:label"
							label="Duração em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.INTERVALO_DE_TEMPO
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('duracaoMinima')"
							[id]="'duracaoMinima'"
							i18n-label="@@crud-disponibilidade:select:duracao:label"
							label="Duração mínima em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.INTERVALO_DE_TEMPO
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('duracaoMaxima')"
							[id]="'duracaoMaxima'"
							i18n-label="@@crud-disponibilidade:select:duracao:label"
							label="Duração máxima em minutos"></pacto-cat-form-input>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							(change)="limparItensValidacao()"
							[control]="formGroup.get('tipoValidacao')"
							[id]="'tipoValidacao-select'"
							[items]="tipoValidacoes"
							i18n-label="@@crud-disponibilidade:select:tipoValidacao:label"
							idKey="id"
							label="Tipo de validação*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoValidacao') &&
							formGroup.get('tipoValidacao').value != tipoValidacaoEnum.LIVRE
						"
						class="col-md-6">
						<pacto-cat-form-multi-select-filter
							[autocomplete]="'off'"
							[control]="formGroup.get('itensValidacao')"
							[endpointUrl]="itensValidacaoUrl"
							[idKey]="
								formGroup.get('tipoValidacao').value == tipoValidacaoEnum.PLANO
									? 'plano'
									: 'produto'
							"
							[id]="'itensValidacao-select'"
							[labelKey]="'descricao'"
							[paramBuilder]="paramBuilder"
							[resposeParser]="responseParser"
							i18n-label="@@crud-disponibilidade:select:itensValidacao:label"
							label="Itens de validação"></pacto-cat-form-multi-select-filter>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('descricao')"
							[id]="'descricao'"
							i18n-label="@@crud-disponibilidade:select:descricao:label"
							i18np-placeholder="@@crud-disponibilidade:input:descricao:placeholder"
							label="Descrição"
							placeholder="Descreva o que é e qual é o objetivo desta disponibilidade"></pacto-cat-form-input>
					</div>
				</div>

				<div class="row" style="margin-bottom: 96px">
					<div class="col-md-12">
						<label>Imagem do Banner</label>
						<pacto-cat-file-input
							#fileInputComponent
							[control]="formGroup.get('base64Imagem')"
							[formatosValidos]="'(jpeg|jpg|png)$'"
							[formatos]="'.JPG, .JPEG, .PNG'"
							[imageHeight]="150"
							[imageWidth]="150"
							[nomeControl]="formGroup.get('formControlNomeImagem')"
							[urlImage]="urlImagem"
							label="Imagem"></pacto-cat-file-input>
					</div>
				</div>

				<div class="actions">
					<button
						[disabled]="!isFormGroupValid"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula1"
						pactoCatStepNext>
						Avançar
						<span class="pct pct-chevron-right"></span>
					</button>
				</div>
			</pacto-cat-step>

			<pacto-cat-step>
				<ng-template pactoCatStepLabel>Data e horários</ng-template>
				<div class="row margin-bottom-40">
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataInicial')"
							i18n-label="@@disponibilidade-form:label-data-inicial"
							label="Data inicial"></pacto-cat-datepicker>
					</div>
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataFinal')"
							i18n-label="@@disponibilidade-form:label-data-final"
							label="Data final"></pacto-cat-datepicker>
					</div>
				</div>

				<pacto-title-card [title]="'Horários cadastrados'">
					<pacto-relatorio
						#tableData
						(actionLinkFooterClick)="editarHorariosSelecionados()"
						(filterChangeEvent)="filterChangeEvent($event)"
						(iconClick)="actionClickHandler($event)"
						(pageChangeEvent)="pageChangeEvent($event)"
						(pageSizeChange)="pageSizeChange($event)"
						(sortEvent)="ordenarHorarios($event)"
						[actionTitulo]="'Ações'"
						[customActionsRight]="customFilter"
						[iconActionLinkFooter]="'pct-edit'"
						[itensPerPage]="itensPerPage"
						[labelActionLinkFooter]="'Editar todos os selecionados'"
						[showShare]="true"
						[table]="table"></pacto-relatorio>
				</pacto-title-card>

				<div class="actions">
					<button
						(click)="submitHandler()"
						[disabled]="
							!isFormGroupValid || !formGroup.get('horarios').value?.length
						"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula3">
						Concluir disponibilidade
					</button>

					<button
						class="btn btn-secondary"
						i18n="@@buttons:cancelar"
						pactoCatStepPrevious>
						<span class="pct pct-chevron-left"></span>
						Voltar
					</button>
				</div>
			</pacto-cat-step>
		</pacto-cat-stepper>
	</pacto-cat-card-plain>

	<pacto-cat-tabs-transparent *ngIf="operation === 'edit'">
		<ng-template label="Informações" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('nome')"
							[id]="'nome-disponibilidade-input'"
							i18n-label="@@crud-disponibilidade:input:nome:label"
							i18np-placeholder="@@crud-disponibilidade:input:nome:placeholder"
							label="Nome do serviço*"
							placeholder="Adicione um nome para sua disponibilidade"></pacto-cat-form-input>
					</div>

					<div class="col-md-6">
						<pacto-cat-input-color [formControl]="formGroup.get('cor')">
							<label class="label-cor-disponibilidade">Cor do serviço*</label>
						</pacto-cat-input-color>
					</div>
				</div>

				<div class="row baseline">
					<div class="col-md-6">
						<pacto-cat-form-select
							[control]="formGroup.get('comportamento')"
							[id]="'comportamento-select'"
							[items]="comportamentos"
							i18n-label="@@crud-disponibilidade:select:comportamento:label"
							idKey="id"
							label="Comportamento*"
							labelKey="nome"></pacto-cat-form-select>
					</div>

					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('intervaloDiasEntreAgendamentos')"
							[id]="'intervaloDiasAgendamento'"
							i18n-label="
								@@crud-disponibilidade:select:intervaloDiasAgendamento:label"
							label="Intervalo de dias entre agendamentos"></pacto-cat-form-input-number>
					</div>
					<div class="col-md-3">
						<pacto-cat-form-input-number
							[formControl]="formGroup.get('intervaloMinimoDiasCasoFalta')"
							[id]="'intervaloMinimoFalta'"
							i18n-label="
								@@crud-disponibilidade:select:intervaloMinimoFalta:label"
							label="Intervalo minimo de dias em caso de falta"></pacto-cat-form-input-number>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							(change)="limparValoresTipoHorario()"
							[control]="formGroup.get('tipoHorario')"
							[id]="'tipoHorario-select'"
							[items]="tipoHorarios"
							i18n-label="@@crud-disponibilidade:select:tipoHorario:label"
							idKey="id"
							label="Tipo de horário*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.DURACAO_PREDEFINIDA
						"
						class="col-md-6">
						<pacto-cat-form-input
							[control]="formGroup.get('duracao')"
							[id]="'duracao'"
							i18n-label="@@crud-disponibilidade:select:duracao:label"
							label="Duração em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.INTERVALO_DE_TEMPO
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('duracaoMinima')"
							[id]="'duracaoMinima'"
							i18n-label="@@crud-disponibilidade:select:duracao:label"
							label="Duração mínima em minutos"></pacto-cat-form-input>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoHorario') &&
							formGroup.get('tipoHorario').value ==
								tipoHorarioEnum.INTERVALO_DE_TEMPO
						"
						class="col-md-3">
						<pacto-cat-form-input
							[control]="formGroup.get('duracaoMaxima')"
							[id]="'duracaoMaxima'"
							i18n-label="@@crud-disponibilidade:select:duracao:label"
							label="Duração máxima em minutos"></pacto-cat-form-input>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6">
						<pacto-cat-form-select
							(change)="limparItensValidacao()"
							[control]="formGroup.get('tipoValidacao')"
							[id]="'tipoValidacao-select'"
							[items]="tipoValidacoes"
							i18n-label="@@crud-disponibilidade:select:tipoValidacao:label"
							idKey="id"
							label="Tipo de validação*"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div
						*ngIf="
							formGroup.get('tipoValidacao') &&
							formGroup.get('tipoValidacao').value != tipoValidacaoEnum.LIVRE
						"
						class="col-md-6">
						<pacto-cat-form-multi-select-filter
							[autocomplete]="'off'"
							[control]="formGroup.get('itensValidacao')"
							[endpointUrl]="itensValidacaoUrl"
							[idKey]="
								formGroup.get('tipoValidacao').value == tipoValidacaoEnum.PLANO
									? 'plano'
									: 'produto'
							"
							[id]="'itensValidacao-select'"
							[labelKey]="'descricao'"
							[paramBuilder]="paramBuilder"
							[resposeParser]="responseParser"
							i18n-label="@@crud-disponibilidade:select:itensValidacao:label"
							label="Itens de validação"></pacto-cat-form-multi-select-filter>
					</div>
				</div>

				<div class="row">
					<div class="col-md-12">
						<pacto-cat-form-input
							[autocomplete]="'off'"
							[control]="formGroup.get('descricao')"
							[id]="'descricao'"
							i18n-label="@@crud-disponibilidade:select:descricao:label"
							i18np-placeholder="@@crud-disponibilidade:input:descricao:placeholder"
							label="Descrição"
							placeholder="Descreva o que é e qual é o objetivo desta disponibilidade"></pacto-cat-form-input>
					</div>
				</div>

				<div class="row" style="margin-bottom: 96px">
					<div class="col-md-12">
						<label>Imagem do Banner</label>
						<pacto-cat-file-input
							#fileInputComponent
							[control]="formGroup.get('base64Imagem')"
							[formatosValidos]="'(jpeg|jpg|png)$'"
							[formatos]="'.JPG, .JPEG, .PNG'"
							[imageHeight]="150"
							[imageWidth]="150"
							[nomeControl]="formGroup.get('formControlNomeImagem')"
							[urlImage]="urlImagem"
							label="Imagem"></pacto-cat-file-input>
					</div>
				</div>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
		<ng-template label="Data e horários" pactoTabTransparent>
			<pacto-cat-card-plain>
				<div class="row margin-bottom-40">
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataInicial')"
							i18n-label="@@disponibilidade-form:label-data-inicial"
							label="Data inicial"></pacto-cat-datepicker>
					</div>
					<div class="col-md-6">
						<pacto-cat-datepicker
							[className]="'datePicker'"
							[formControl]="formGroup.get('dataFinal')"
							i18n-label="@@disponibilidade-form:label-data-final"
							label="Data final"></pacto-cat-datepicker>
					</div>
				</div>

				<pacto-title-card [title]="'Horários cadastrados'">
					<pacto-relatorio
						#tableData
						(actionLinkFooterClick)="editarHorariosSelecionados()"
						(filterChangeEvent)="filterChangeEvent($event)"
						(iconClick)="actionClickHandler($event)"
						(pageChangeEvent)="pageChangeEvent($event)"
						(pageSizeChange)="pageSizeChange($event)"
						(sortEvent)="ordenarHorarios($event)"
						[actionTitulo]="'Ações'"
						[customActionsRight]="customFilter"
						[iconActionLinkFooter]="'pct-edit'"
						[itensPerPage]="itensPerPage"
						[labelActionLinkFooter]="'Editar todos os selecionados'"
						[showShare]="true"
						[table]="table"></pacto-relatorio>
				</pacto-title-card>

				<div class="actions">
					<button
						(click)="submitHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar">
						Salvar Alterações
					</button>
				</div>
			</pacto-cat-card-plain>
		</ng-template>
	</pacto-cat-tabs-transparent>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span i18n="@@crud-disponibilidades:create-success" xingling="createSuccess">
		Disponibilidade criada com sucesso.
	</span>
	<span i18n="@@crud-disponibilidades:create-edit" xingling="editSuccess">
		Disponibilidade editada com sucesso.
	</span>
	<span
		i18n="@@crud-disponibilidades:campo-obrigatorio"
		xingling="campoObrigatorio">
		Campos obrigatórios não preenchido.
	</span>
	<span
		i18n="@@crud-disponibilidades:campo-obrigatorio:inserir-horario"
		xingling="inserirHorario">
		Inserir pelo menos um horário na lista
	</span>
	<span i18n="@@crud-disponibilidades:validacao:data" xingling="validacaoData">
		Data inicial não pode ser superior a data final
	</span>
	<span
		i18n="@@crud-disponibilidades:validacao:horario"
		xingling="validacaoHorario">
		Horário cadastrado invalido, tente novamente
	</span>
	<span
		i18n="@@crud-disponibilidades:validacao:dia semana"
		xingling="validacaoDiaSemana">
		Selecione ao menos um dia da semana.
	</span>
	<span xingling="horarionaoPodeSerRemovido">
		Não é possível excluir o horário, contém aula confirmada.
	</span>
	<span xingling="erro_aula_url_virtual">Informe a URL da aula virtual.</span>
	<span i18n="@@crud-aulas:erro_incluir_aula" xingling="erro_incluir_aula">
		Ocorreu um erro ao incluir a aula, tente novamente em alguns instantes!
	</span>
	<span
		i18n="@@crud-ambientes-disponibilidades:success:create"
		xingling="successCreate">
		Ambiente criado com sucesso.
	</span>
	<span i18n="@@crud-horarios:edit-success" xingling="horarioEditSuccess">
		Horario editado com sucesso.
	</span>
</pacto-traducoes-xingling>

<ng-template #customFilter>
	<pacto-custom-data-grid-filter #filter>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearVigencia()"
			[showClearFilter]="formGroupFilter.get('vigencia').value">
			<span>Situação</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroupFilter.get('vigencia')"
					[idKey]="'id'"
					[labelKey]="'nome'"
					[options]="vigencia"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>
		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearProfessores()"
			[showClearFilter]="formGroupFilter.get('professor').value">
			<span>Vinculos</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroupFilter.get('professor')"
					[labelKey]="'nome'"
					[options]="professores.content"
					label="Professor"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearAmbientes()"
			[showClearFilter]="formGroupFilter.get('ambiente').value">
			<span>Local</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-multi-select-filter
					[control]="formGroupFilter.get('ambiente')"
					[labelKey]="'nome'"
					[options]="ambientes.content"
					label="Ambiente"></pacto-cat-multi-select-filter>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearDiasSemana()"
			[showClearFilter]="
				formGroupFilter.get('segFc').value ||
				formGroupFilter.get('terFc').value ||
				formGroupFilter.get('quaFc').value ||
				formGroupFilter.get('quiFc').value ||
				formGroupFilter.get('sexFc').value ||
				formGroupFilter.get('sabFc').value ||
				formGroupFilter.get('domFc').value
			">
			<span>Dia da semana</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('segFc')"
					[label]="'Seg'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('terFc')"
					[label]="'Ter'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('quaFc')"
					[label]="'Qua'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('quiFc')"
					[label]="'Qui'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('sexFc')"
					[label]="'Sex'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('sabFc')"
					[label]="'Sab'"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[checkBlue]="true"
					[control]="formGroupFilter.get('domFc')"
					[label]="'Dom'"></pacto-cat-checkbox>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<pacto-custom-data-grid-filter-option
			(clearFilter)="clearHorarios()"
			[showClearFilter]="
				formGroupFilter.get('horaInicial').value ||
				formGroupFilter.get('horaFinal').value
			">
			<span>Horário</span>
			<ng-container pactoDataGridFilterOptionBody>
				<pacto-cat-form-input
					[control]="formGroupFilter.get('horaInicial')"
					[id]="'input-horaInicial'"
					[label]="'Hora inicial'"
					[placeholder]="'00h00'"
					[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
				<pacto-cat-form-input
					[control]="formGroupFilter.get('horaFinal')"
					[id]="'input-horaFinal'"
					[label]="'Hora final'"
					[placeholder]="'00h00'"
					[textMask]="{ mask: timerMask, guide: true }"></pacto-cat-form-input>
			</ng-container>
		</pacto-custom-data-grid-filter-option>

		<div class="buttons-filter-border">
			<pacto-cat-button
				(click)="limparFiltros()"
				[type]="'SECUNDARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Limpar filtros"></pacto-cat-button>
			<pacto-cat-button
				(click)="btnClickFiltrarTabela($event)"
				[type]="'PRIMARY'"
				[v2]="true"
				class="buscar-mais-filtros"
				label="Consultar"></pacto-cat-button>
		</div>
	</pacto-custom-data-grid-filter>

	<pacto-cat-button
		(click)="btnClickHandler()"
		[icon]="'pct pct-plus'"
		class="btnCadastrar"
		label="Adicionar horário"
		size="LARGE"
		type="PRIMARY_ADD"></pacto-cat-button>
</ng-template>

<!--table columns-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar horário</span>
</ng-template>
<ng-template #codigoColumnName>
	<div class="column-check">
		<div style="margin-top: -4px">
			<pacto-cat-checkbox
				[checkBlue]="true"
				[control]="formCheckAll"
				id="check-all-horarios"></pacto-cat-checkbox>
		</div>
	</div>
</ng-template>
<ng-template #professorColumnName>
	<span i18n="@@crud-disponibilidade:professor:title:table">Professor</span>
</ng-template>
<ng-template #disponibilidadeColumnName>
	<span i18n="@@crud-disponibilidade:disponibilidade:title:table">
		Disponibilidade
	</span>
</ng-template>
<ng-template #funcionamentoColumnName>
	<span i18n="@@crud-disponibilidade:funcionamento:title:table">
		Funcionamento
	</span>
</ng-template>
<ng-template #diaColumnName>
	<span i18n="@@crud-disponibilidade:dia:title:table">Dia</span>
</ng-template>
<ng-template #ambienteColumnName>
	<span i18n="@@crud-disponibilidade:ambiente:title:table">Ambiente</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-atividades:table:status">Ações</span>
</ng-template>
<!--End table columns-->

<!--Celulas para formatação-->
<ng-template #codigoCelula let-item="item">
	<div class="column-check">
		<div>
			<pacto-cat-checkbox
				[checkBlue]="true"
				[control]="getFormCheckHorario(item)"></pacto-cat-checkbox>
		</div>
	</div>
</ng-template>
<ng-template #professorCelula let-item="item">
	{{ item.professor?.nome }}
</ng-template>
<ng-template #ambienteCelula let-item="item">
	{{ item.ambiente?.nome }}
</ng-template>
<ng-template #disponibilidadeCelula let-item="item">
	{{ item.horaInicial + " às " + item.horaFinal }}
</ng-template>
<ng-template #funcionamentoCelula>
	{{
		tipoHorarioName(
			disponibilidadeCreateEdit
				? disponibilidadeCreateEdit.tipoHorario
				: formGroup.get("tipoHorario").value
		)
	}}
</ng-template>
<ng-template #diasCelula let-item="item">
	{{ convertDiaSemanaString(item.diaSemana) }}
</ng-template>
<!--fim-->

<!--tooltip icons-->
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<!--End tooltip icons-->

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove-modal:title">
	Inativar Horario Disponibilidade ?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:success">
	Horario disponibilidade inativado com sucesso.
</span>
<span
	#removeError
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:error">
	Não foi possível inativar, existem alunos matriculados
</span>
<span
	#actionNoUndone
	[hidden]="true"
	i18n="@@crud-turmas-horariosDisponibilidade:remove:actionNoUndone">
	Essa ação não poderá ser desfeita
</span>
<span
	#editOrExcludeHorarioDispLarge
	[hidden]="true"
	i18n="
		@@crud-turmas-horariosDisponibilidade:remove:editOrExcludeHorarioDispLarge">
	Existem alunos confirmados já nesse horário e eles serão mantidos, caso queira
	retirar esses alunos é preciso ir na agenda e removê-los manualmente ou
	editá-los para um novo horário
</span>
