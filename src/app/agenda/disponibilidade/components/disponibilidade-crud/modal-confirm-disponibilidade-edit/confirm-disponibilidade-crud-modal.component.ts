import {
	Component,
	OnInit,
	Input,
	ViewChild,
	TemplateRef,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "ccl-confirm-modal",
	templateUrl: "./confirm-disponibilidade-crud-modal.component.html",
	styleUrls: ["./confirm-disponibilidade-crud-modal.component.scss"],
})
export class ConfirmDisponibilidadeCrudModalComponent implements OnInit {
	@Input() title: string;
	@Input() body: string;
	@Input() bodyRef: TemplateRef<any>;
	@ViewChild("actionDefault", { static: true }) actionDefault;
	@Input() actionLabel;
	@Input() cancelLabel;
	@Input() showCancelButton;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {
		if (!this.actionLabel && this.actionDefault) {
			this.actionLabel = this.actionDefault.nativeElement.innerHTML;
		}
		if (!this.cancelLabel) {
			this.cancelLabel = "Cancelar";
		}
		if (this.showCancelButton === null || this.showCancelButton === undefined) {
			this.showCancelButton = true;
		}
	}

	dismiss() {
		this.openModal.dismiss(false);
	}

	close() {
		this.openModal.close(true);
	}
}
