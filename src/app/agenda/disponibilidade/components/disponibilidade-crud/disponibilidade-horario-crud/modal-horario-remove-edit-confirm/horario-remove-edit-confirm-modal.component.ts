import {
	Component,
	OnInit,
	Input,
	ViewChild,
	TemplateRef,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "ccl-confirm-modal",
	templateUrl: "./horario-remove-edit-confirm-modal.component.html",
	styleUrls: ["./horario-remove-edit-confirm-modal.component.scss"],
})
export class HorarioRemoveEditConfirmModalComponent implements OnInit {
	@Input() title: string;
	@Input() exclusao: boolean = false;
	@Input() possuiAlunoAgendado: boolean = false;
	@Input() body: string;
	@Input() bodyRef: TemplateRef<any>;
	@ViewChild("actionDefault", { static: true }) actionDefault;
	@Input() actionLabel;
	@Input() cancelLabel;
	@Input() showCancelButton;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {
		if (!this.actionLabel && this.actionDefault) {
			this.actionLabel = this.actionDefault.nativeElement.innerHTML;
		}
		if (!this.cancelLabel) {
			this.cancelLabel = "Cancelar";
		}
		if (this.showCancelButton === null || this.showCancelButton === undefined) {
			this.showCancelButton = true;
		}
	}

	dismiss() {
		this.openModal.close(true);
	}

	close() {
		this.openModal.close(false);
	}
}
