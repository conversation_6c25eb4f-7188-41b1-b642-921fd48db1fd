@import "src/assets/scss/pacto/plataforma-import.scss";

.baseline {
	align-items: baseline;
}

:host {
	::ng-deep .force-header-start > .pct-stepper-navigation-bar {
		display: flex;
		justify-content: flex-start;
	}

	::ng-deep.btn-primary,
	::ng-deep.btn.btn-primary:hover,
	::ng-deep.show > .btn-primary.dropdown-toggle {
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		.pct {
			color: $azulim05 !important;
		}
	}

	::ng-deep.pacto-primary,
	::ng-deep.btn.pacto-primary:hover,
	::ng-deep.show > .btn.pacto-primary.dropdown-toggle.btn-share-label {
		font-weight: bold;
		border: 1px solid $azulim05 !important;
		background-color: #ffffff !important;
		color: $azulim05 !important;

		::ng-deep.icon-drop {
			border-left: 1px solid $azulim05;
		}
	}

	::ng-deep.pacto-primary,
	::ng-deep.btn.pacto-primary:hover,
	::ng-deep.btn.pacto-primary.dropdown-toggle {
		::ng-deep.icon-drop {
			border-left: 1px solid $azulim05 !important;
		}
	}

	::ng-deep.icon-drop {
		width: 35px !important;
		display: inline-block !important;
	}
}

.buscar-mais-filtros::ng-deep {
	.pacto-button.v2.pacto-primary {
		margin-top: 5px;
		width: 48%;
		margin-left: 5px;
		color: #fff !important;
		background-color: #1e5ff8 !important;
	}

	.pacto-button.v2.pacto-secundary {
		margin-top: 20px;
		width: 48%;
		margin-left: 5px;
		border: 1px solid #1e5ff8;
		color: #1e5ff8;
	}
}

.btnCadastrar {
	margin-left: 10px;
}

.actions {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 15px;

	button {
		margin-left: 15px;
		background-color: #0380e3 !important;
		color: #fff !important;
		border: 1px solid #0380e3;

		&:hover {
			background: #0380e3 !important;
			color: #fff !important;

			span {
				color: #fff !important;
			}
		}
	}

	span {
		color: #fff !important;
	}
}

::ng-deep .pacto-tabs-wrapper {
	.tabs {
		margin-bottom: 30px;
		border-bottom: 1px solid #d1d4dc;
	}
}

.margin-bottom-40 {
	margin-bottom: 40px;
}

.input-row {
	display: flex;

	.form-group {
		padding-right: 15px;
		margin-bottom: 0px;
		flex-grow: 1;
	}

	.btn-position {
		height: 34px;
		margin-top: 30px;
	}
}

table {
	i {
		cursor: pointer;
	}

	.action-column {
		width: 50px;
		text-align: center;
	}
}

.position-validar {
	margin: 35px 35px 35px 0px;
}

.btn-cadastros-position {
	height: 34px;
	margin-top: 30px;
	margin-left: -10px;
}

.check-marcacao {
	margin-top: 35px;
	margin-left: 20px;
}

.check-aula-disponivel {
	margin-top: 1px;
	margin-left: 5px;
}

.check-moda-contrato {
	margin-left: 25px;
	margin-top: 35px;
}

.idGymPass-label {
	margin-top: 35px;
	margin-left: 20px;
}

label.control-label {
	font-weight: 600;
}

pacto-cat-form-input-number::ng-deep {
	margin: 0px;

	.nome {
		color: #67757c;
		font-weight: 600;
		display: inline-block;
		font-size: 1rem;
	}

	input {
		padding: 0.25rem 0.5rem;
		font-size: 0.875rem;
		line-height: 1.5;
		border-radius: 0.2rem;
	}

	.pct {
		display: none;
	}
}

.button-remover {
	font-size: 12px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-family: "Nunito Sans", sans-serif;
	font-weight: 700;
	color: blue;
	cursor: pointer;
	padding-left: 1px;
}

.row-meta-container {
	pacto-cat-form-input-number::ng-deep {
		.nome {
			white-space: nowrap;
		}
	}
}

.header-row {
	display: flex;
	justify-content: space-between;
	padding-bottom: 10px;
}

.images-wrapper {
	margin-top: 10px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	border-top: 1px solid #d1d1d1;
	padding-top: 15px;

	.imagem {
		margin: 5px;
		width: 150px;

		.imagem-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f5f5f5;
			width: 150px;
			height: 150px;
		}

		.control-footer {
			display: flex;

			.name {
				width: calc(100% - 20px);
				padding-top: 5px;
				padding-right: 5px;
				word-break: break-all;
				overflow: hidden;
			}

			.icon {
				padding-top: 5px;
				flex-grow: 1;
				cursor: pointer;
			}
		}

		img {
			max-width: 150px;
			max-height: 150px;
		}
	}
}

.cadastro-nivel-turma {
	margin-top: -22px;
	color: #0a64ff;
	font-weight: bold;
	font-size: 14px;
}

.table-horario {
	margin: 0px -30px;
}

.label-cor-disponibilidade {
	margin-bottom: 0px;
	margin-top: 18px;
	color: #797d86;
}

pacto-cat-datepicker::ng-deep {
	.nome {
		color: #797d86;
		font-weight: 600;
	}
}

pacto-cat-form-select::ng-deep {
	span {
		color: #797d86;
		font-weight: 600;
	}
}

pacto-cat-form-multi-select-filter::ng-deep {
	span {
		color: #797d86;
		font-weight: 600;
	}
}

pacto-cat-form-input::ng-deep {
	.nome {
		color: #797d86;
		font-weight: 600;
	}
}

pacto-cat-form-input-number::ng-deep {
	.nome {
		color: #797d86;
		font-weight: 600;
	}

	input {
		padding: 0.56rem 0.5rem;
	}
}

::ng-deep .cat-input-color-wrapper {
	.cat-input-color-field {
		width: auto !important;
		height: 43px !important;
	}
}

.alignleft {
	text-align: left;
}

.margin-top-20 {
	margin-top: 20px;
}

::ng-deep .modal-agenda-turma-horario .modal-dialog {
	width: 1000px !important;
	max-width: 1000px !important;
	height: 570px !important;
}

.column-check {
	display: flex;
}
