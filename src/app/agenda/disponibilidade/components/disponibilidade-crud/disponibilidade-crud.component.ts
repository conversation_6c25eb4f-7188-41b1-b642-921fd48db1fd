import {
	AfterContentChe<PERSON>,
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Inject,
	LOCALE_ID,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import {
	SnotifyButton,
	SnotifyPosition,
	SnotifyService,
	SnotifyToastConfig,
} from "ng-snotify";
import { SnotifyType } from "ng-snotify/snotify/types/snotify.type";
import { SnotifyAnimate } from "ng-snotify/snotify/interfaces/SnotifyAnimate.interface";
import { LocalizationService } from "@base-core/localization/localization.service";
import {
	Modalidade,
	TreinoApiColaboradorService,
	TreinoApiAmbienteService,
	TreinoApiDisponibilidadeService,
	TreinoApiModalidadeService,
	Ambiente,
	NivelTurma,
	HorarioDisponibilidade,
	ComportamentoEnum,
	DisponibilidadeCreateEdit,
	TipoHorarioEnum,
	TipoValidacaoEnum,
} from "treino-api";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	TraducoesXinglingComponent,
	SelectFilterResponseParser,
	CustomDataGridFilterComponent,
} from "ui-kit";
import { SafeHtml } from "@angular/platform-browser";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { DatePipe } from "@angular/common";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";
import { UsuarioBase } from "sdk";
import { DisponibilidadeHorarioCrudModalComponent } from "./disponibilidade-horario-crud/disponibilidade-horario-crud-modal.component";
import { ConfirmDisponibilidadeCrudModalComponent } from "./modal-confirm-disponibilidade-edit/confirm-disponibilidade-crud-modal.component";
import { HorarioRemoveEditConfirmModalComponent } from "./disponibilidade-horario-crud/modal-horario-remove-edit-confirm/horario-remove-edit-confirm-modal.component";
import { Location } from "@angular/common";

export class SelectItemPactoCatSelect {
	value: string;
	label: string;
}

export class SelectItemPactoSelect {
	id: string;
	nome: string;
}

@Component({
	selector: "pacto-disponibilidade-crud",
	templateUrl: "./disponibilidade-crud.component.html",
	styleUrls: ["./disponibilidade-crud.component.scss"],
})
export class DisponibilidadeCrudComponent
	implements OnInit, AfterViewInit, AfterContentChecked
{
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@Output() reloadEvent: EventEmitter<any> = new EventEmitter();

	@ViewChild("filter", { static: false })
	filter: CustomDataGridFilterComponent;

	timerMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	formGroupFilter: FormGroup = new FormGroup({
		vigencia: new FormControl([{ id: true, nome: "Ativo" }]),
		professor: new FormControl(),
		ambiente: new FormControl(),
		segFc: new FormControl(false),
		terFc: new FormControl(false),
		quaFc: new FormControl(false),
		quiFc: new FormControl(false),
		sexFc: new FormControl(false),
		sabFc: new FormControl(false),
		domFc: new FormControl(false),
		horaInicial: new FormControl(),
		horaFinal: new FormControl(),
	});

	modalidades: Array<Modalidade> = [];
	horariosNovosAdd: HorarioDisponibilidade[];
	disponibilidadeCreateEditDps: DisponibilidadeCreateEdit;
	disponibilidadeCreateEdit: DisponibilidadeCreateEdit;
	operation: string;
	entity = true;
	isFormGroupValid: boolean = false;
	tipoHorarioEnum = TipoHorarioEnum;
	tipoValidacaoEnum = TipoValidacaoEnum;
	urlImagem: string = null;
	isModalOpen = false;

	tipoValidacoes: Array<any> = [
		{ id: TipoValidacaoEnum.LIVRE, nome: "Livre" },
		{ id: TipoValidacaoEnum.PLANO, nome: "Plano" },
		{ id: TipoValidacaoEnum.PRODUTO, nome: "Produto" },
	];

	comportamentos: Array<any> = [
		{
			id: ComportamentoEnum.CONTATO_INTERPESSOAL,
			nome: "Contato interpessoal",
		},
		{ id: ComportamentoEnum.PRESCRICAO_TREINO, nome: "Prescrição de treino" },
		{ id: ComportamentoEnum.REVISAO_TREINO, nome: "Revisão de treino" },
		{ id: ComportamentoEnum.RENOVAR_TREINO, nome: "Renovar treino" },
		{ id: ComportamentoEnum.AVALIACAO_FISICA, nome: "Avaliação física" },
	];

	tipoHorarios: Array<any> = [
		{ id: TipoHorarioEnum.DURACAO_LIVRE, nome: "Livre" },
		{ id: TipoHorarioEnum.DURACAO_PREDEFINIDA, nome: "Pré-definido" },
		{ id: TipoHorarioEnum.INTERVALO_DE_TEMPO, nome: "Intervalo de tempo" },
	];

	vigencia: Array<any> = [
		{ id: true, nome: "Ativo" },
		{ id: false, nome: "Inativo" },
	];

	professores: ApiResponseList<UsuarioBase> = {
		content: [],
	};
	ambientes: ApiResponseList<Ambiente> = {
		content: [],
	};
	niveisTurma: ApiResponseList<NivelTurma> = {
		content: [],
	};

	configSnotify: SnotifyToastConfig = new (class implements SnotifyToastConfig {
		animation: SnotifyAnimate;
		backdrop: number;
		bodyMaxLength: number;
		buttons: SnotifyButton[];
		closeOnClick: boolean;
		html: string | SafeHtml;
		icon: string;
		iconClass: string;
		pauseOnHover: boolean;
		placeholder: string;
		position: SnotifyPosition;
		showProgressBar: boolean;
		timeout: number;
		titleMaxLength: number;
		type: SnotifyType;
	})();

	formsCheckHorarios: Array<{
		horarioDisponibilidade: HorarioDisponibilidade;
		form: FormControl;
	}> = new Array<{
		horarioDisponibilidade: HorarioDisponibilidade;
		form: FormControl;
	}>();
	formCheckAll = new FormControl(false);
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		cor: new FormControl("#1B4166", [Validators.required]),
		horarios: new FormControl(null),
		comportamento: new FormControl(0),
		intervaloDiasEntreAgendamentos: new FormControl(0),
		intervaloMinimoDiasCasoFalta: new FormControl(0),
		tipoValidacao: new FormControl(0),
		itensValidacao: new FormControl(null),
		tipoHorario: new FormControl(0),
		duracao: new FormControl(0),
		duracaoMinima: new FormControl(0),
		duracaoMaxima: new FormControl(0),
		descricao: new FormControl(""),
		base64Imagem: new FormControl(""),
		formControlNomeImagem: new FormControl(""),
		dataInicial: new FormControl(new Date().getTime(), [Validators.required]),
		dataFinal: new FormControl(new Date().getTime(), [Validators.required]),
	});

	@ViewChild("fileInputComponent", { static: false }) fileInputComponent;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("diaColumnName", { static: true }) diaColumnName;
	@ViewChild("codigoColumnName", { static: true }) codigoColumnName;
	@ViewChild("professorColumnName", { static: true }) professorColumnName;
	@ViewChild("disponibilidadeColumnName", { static: true })
	disponibilidadeColumnName;
	@ViewChild("funcionamentoColumnName", { static: true })
	funcionamentoColumnName;
	@ViewChild("ambienteColumnName", { static: true }) ambienteColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("horarioTurmaCelula", { static: true }) horarioTurmaCelula;
	@ViewChild("codigoCelula", { static: true }) codigoCelula;
	@ViewChild("professorCelula", { static: true }) professorCelula;
	@ViewChild("ambienteCelula", { static: true }) ambienteCelula;
	@ViewChild("disponibilidadeCelula", { static: true }) disponibilidadeCelula;
	@ViewChild("funcionamentoCelula", { static: true }) funcionamentoCelula;
	@ViewChild("diasCelula", { static: true }) diasCelula;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;

	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("actionNoUndone", { static: true }) actionNoUndone;
	@ViewChild("editOrExcludeHorarioDispLarge", { static: true })
	editOrExcludeHorarioDispLarge;

	table: PactoDataGridConfig;
	horariosData: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();
	filterConfig: any;

	constructor(
		@Inject(LOCALE_ID) private locale,
		private localization: LocalizationService,
		private modalidadeService: TreinoApiModalidadeService,
		private notificationService: SnotifyService,
		private disponibilidadeService: TreinoApiDisponibilidadeService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private ambienteService: TreinoApiAmbienteService,
		private niveisTurmaService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private modal: NgbModal,
		private configCache: TreinoConfigCacheService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private session: SessionService,
		private ngbModal: NgbModal,
		private modalService: ModalService,
		private location: Location
	) {}

	ngOnInit() {
		const voltarIcone = document.querySelector(
			"pacto-breadcrumbs .first span i"
		);
		if (voltarIcone) {
			voltarIcone.addEventListener("click", () => {
				this.router.navigate(["agenda", "disponibilidade"]);
			});
		}
	}

	ngAfterContentChecked() {
		this.verificaCamposValidos();
		if (this.fileInputComponent) {
			this.urlImagem = this.fileInputComponent.urlImage;
		}
	}

	ngAfterViewInit() {
		this.configTable();
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}

			zip(this.getAmbientes(), this.getProfessor()).subscribe(() => {
				this.povoarHorarios();
			});
			this.formCheckAll.valueChanges.subscribe((v: boolean) => {
				this.checkAll(v);
			});
			this.horariosNovosAdd = [];
		});
	}

	get itensValidacaoUrl() {
		if (
			this.formGroup.get("tipoValidacao") &&
			this.formGroup.get("tipoValidacao").value
		) {
			return this.rest.buildFullUrl(
				`disponibilidade/${
					this.formGroup.get("tipoValidacao").value
				}/itensValidacao`
			);
		}
		return this.rest.buildFullUrl("disponibilidade/0/itensValidacao");
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	paramBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	private loadEntities(id) {
		if (id) {
			this.disponibilidadeService
				.obterDisponibilidade(id)
				.subscribe((dados) => {
					if (dados) {
						this.disponibilidadeCreateEdit = dados;
						this.entity = false;
						this.loadForm();
					} else {
						console.log("erro");
					}
				});
		}
	}

	povoarHorarios() {
		if (
			this.disponibilidadeCreateEdit &&
			this.disponibilidadeCreateEdit.horarios
		) {
			let count = 1;
			const qtdHorarios = this.disponibilidadeCreateEdit.horarios.length;
			this.disponibilidadeCreateEdit.horarios.forEach((h) => {
				this.createHorariosPageObject(
					this.page,
					this.size,
					count === qtdHorarios
				);
				count++;
			});
		}
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	removeHandler(item) {
		const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
		if (item.codigo) {
			this.disponibilidadeService
				.existeAgendamentoAlunoHorarioDisponibilidade(item.codigo)
				.subscribe((ret) => {
					setTimeout(() => {
						const handler = this.modalService.open(
							"Inativar Horário",
							HorarioRemoveEditConfirmModalComponent,
							PactoModalSize.MEDIUM
						);
						handler.componentInstance.exclusao = true;
						handler.componentInstance.body =
							this.actionNoUndone.nativeElement.innerHTML;
						if (ret && ret.retorno) {
							handler.componentInstance.possuiAlunoAgendado = true;
							handler.componentInstance.body =
								this.editOrExcludeHorarioDispLarge.nativeElement.innerHTML;
						}
						handler.result.then(
							(confirm: boolean) => {
								if (confirm) {
									this.disponibilidadeCreateEdit.horarios.forEach((h) => {
										if (h.codigo && h.codigo === item.codigo) {
											h.ativo = false;
											this.snotifyService.success(removeSuccess);
										}
									});
									this.createHorariosPageObject(this.page, this.size, true);
								}
							},
							() => {}
						);
					});
				});
		} else {
			if (this.disponibilidadeCreateEdit) {
				const index = this.disponibilidadeCreateEdit.horarios.indexOf(item, 0);
				if (index > -1) {
					this.disponibilidadeCreateEdit.horarios[index].ativo = false;
					this.snotifyService.success(removeSuccess);
					this.createHorariosPageObject(this.page, this.size, true);
				}
			} else {
				const index = this.formGroup.get("horarios").value.indexOf(item, 0);
				if (index > -1) {
					this.formGroup.get("horarios").value[index].ativo = false;
					this.snotifyService.success(removeSuccess);
					this.createHorariosPageObject(this.page, this.size, true);
				}
			}
		}
	}

	btnClickHandler() {
		if (this.isModalOpen) {
			return;
		}
		this.isModalOpen = true;
		this.getModalAdicionarHorario(
			"Cadastro de horário",
			DisponibilidadeHorarioCrudModalComponent
		);
	}

	editarHorariosSelecionados() {
		const horariosEditar = new Array<HorarioDisponibilidade>();
		const horariosNaoEditar = new Array<HorarioDisponibilidade>();
		this.formsCheckHorarios.forEach((f) => {
			if (f.form.value && f.horarioDisponibilidade.ativo) {
				horariosEditar.push(f.horarioDisponibilidade);
			} else {
				horariosNaoEditar.push(f.horarioDisponibilidade);
			}
		});
		if (horariosEditar.length > 0) {
			this.openModalEditarHorarios(
				"Edição de horários",
				DisponibilidadeHorarioCrudModalComponent,
				horariosEditar,
				horariosNaoEditar
			);
		} else {
			this.snotifyService.error("Nenhum horário selecionado!");
		}
	}

	btnEditHandler(item) {
		if (item.ativo) {
			this.openModalEditarHorario(
				"Edição de horário",
				DisponibilidadeHorarioCrudModalComponent,
				item
			);
		}
	}

	createHorariosPageObject(
		page = 1,
		size = 5,
		reloadData = true,
		array = null,
		emitFilters: boolean = true
	) {
		const arrays = array
			? array
			: this.disponibilidadeCreateEdit
			? this.disponibilidadeCreateEdit.horarios
			: this.formGroup.get("horarios") && this.formGroup.get("horarios").value
			? this.formGroup.get("horarios").value
			: [];
		this.horariosData.totalElements = arrays.length;
		this.horariosData.size = size;
		this.horariosData.totalPages = Math.ceil(
			+(this.horariosData.totalElements / this.horariosData.size)
		);
		this.horariosData.first = page === 0 || page === 1;
		this.horariosData.last = page === this.horariosData.totalPages;
		this.horariosData.content = arrays.slice(size * page - size, size * page);
		if (this.tableData) {
			if (reloadData) {
				this.tableData.reloadData(emitFilters);
			}
		}
	}

	ordenarHorarios(eventSort) {
		this.disponibilidadeCreateEdit.horarios = this.sortList(
			this.disponibilidadeCreateEdit.horarios,
			eventSort.columnName,
			eventSort.direction
		);
		this.createHorariosPageObject(this.page, this.size, true);
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createHorariosPageObject(this.page, this.size, true);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createHorariosPageObject(this.page, this.size, true);
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (
					a[columnName] > b[columnName] ||
					a[columnName].nome > b[columnName].nome
				) {
					return 1;
				} else if (
					a[columnName] < b[columnName] ||
					a[columnName].nome < b[columnName].nome
				) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (
					a[columnName] < b[columnName] ||
					a[columnName].nome < b[columnName].nome
				) {
					return 1;
				} else if (
					a[columnName] > b[columnName] ||
					a[columnName].nome > b[columnName].nome
				) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	openModalEditarHorario(title, modalComponent: any, item: any) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = true;
		modal.componentInstance.variosHorarios = false;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.professores = this.professores.content;
		modal.componentInstance.formGroup.get("codigo").setValue(item.codigo);
		modal.componentInstance.formGroup.get("professor").setValue(item.professor);
		modal.componentInstance.formGroup.get("ambiente").setValue(item.ambiente);
		modal.componentInstance.formGroup
			.get("horaInicial")
			.setValue(item.horaInicial);
		modal.componentInstance.formGroup.get("horaFinal").setValue(item.horaFinal);
		modal.componentInstance.formGroup.get("diaSemana").setValue(item.diaSemana);
		modal.componentInstance.formGroup
			.get("permitirAgendarAppTreino")
			.setValue(item.permieAgendarAppTreino);
		modal.componentInstance.formGroup
			.get("apenasAlunosCarteira")
			.setValue(item.apenasAlunosCarteira);
		if (item.diaSemana === "DM") {
			modal.componentInstance.formGroup.get("dom").setValue(true);
		}
		if (item.diaSemana === "SG") {
			modal.componentInstance.formGroup.get("seg").setValue(true);
		}
		if (item.diaSemana === "TR") {
			modal.componentInstance.formGroup.get("ter").setValue(true);
		}
		if (item.diaSemana === "QA") {
			modal.componentInstance.formGroup.get("qua").setValue(true);
		}
		if (item.diaSemana === "QI") {
			modal.componentInstance.formGroup.get("qui").setValue(true);
		}
		if (item.diaSemana === "SX") {
			modal.componentInstance.formGroup.get("sex").setValue(true);
		}
		if (item.diaSemana === "SB") {
			modal.componentInstance.formGroup.get("sab").setValue(true);
		}
		modal.result.then(
			(dto) => {
				if (dto.codigo) {
					this.disponibilidadeService
						.existeAgendamentoAlunoHorarioDisponibilidade(dto.codigo)
						.subscribe((ret) => {
							setTimeout(() => {
								const handler = this.modalService.open(
									"Editar Horário",
									HorarioRemoveEditConfirmModalComponent,
									PactoModalSize.MEDIUM
								);
								handler.componentInstance.exclusao = false;
								handler.componentInstance.body =
									this.actionNoUndone.nativeElement.innerHTML;
								handler.componentInstance.possuiAlunoAgendado = false;
								if (ret && ret.retorno) {
									handler.componentInstance.possuiAlunoAgendado = true;
									handler.componentInstance.body =
										this.editOrExcludeHorarioDispLarge.nativeElement.innerHTML;
								}
								handler.result.then(
									(confirm: boolean) => {
										if (confirm) {
											if (dto) {
												item.professor = dto.professor;
												item.ambiente = dto.ambiente;
												item.horaInicial = dto.horaInicial;
												item.horaFinal = dto.horaFinal;
												item.diaSemana = dto.diaSemana;
												item.permieAgendarAppTreino =
													dto.permitirAgendarAppTreino;
												item.apenasAlunosCarteira = dto.apenasAlunosCarteira;
												this.disponibilidadeCreateEdit.horarios.forEach((h) => {
													if (h.codigo === item.codigo) {
														h = item;
													}
												});
												this.notificationService.success(
													this.notificacoesTranslate.getLabel(
														"horarioEditSuccess"
													)
												);
											}
										}
									},
									() => {}
								);
							});
						});
				} else {
					setTimeout(() => {
						const handler = this.modalService.open(
							"Editar Horário",
							HorarioRemoveEditConfirmModalComponent,
							PactoModalSize.MEDIUM
						);
						handler.componentInstance.exclusao = false;
						handler.componentInstance.body =
							this.actionNoUndone.nativeElement.innerHTML;
						handler.componentInstance.possuiAlunoAgendado = false;
						handler.result.then(
							(confirm: boolean) => {
								if (confirm) {
									if (dto) {
										item.professor = dto.professor;
										item.ambiente = dto.ambiente;
										item.horaInicial = dto.horaInicial;
										item.horaFinal = dto.horaFinal;
										item.diaSemana = dto.diaSemana;
										item.permieAgendarAppTreino = dto.permitirAgendarAppTreino;
										item.apenasAlunosCarteira = dto.apenasAlunosCarteira;
										this.disponibilidadeCreateEdit.horarios.forEach((h) => {
											if (h.codigo === item.codigo) {
												h = item;
											}
										});
										this.notificationService.success(
											this.notificacoesTranslate.getLabel("horarioEditSuccess")
										);
									}
								}
							},
							() => {}
						);
					});
				}
			},
			() => {}
		);
	}

	openModalEditarHorarios(
		title,
		modalComponent: any,
		itens: HorarioDisponibilidade[],
		naoItens: HorarioDisponibilidade[]
	) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = true;
		modal.componentInstance.variosHorarios = true;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.professores = this.professores.content;
		modal.componentInstance.formGroup.get("codigo").setValue(itens[0].codigo);
		modal.componentInstance.formGroup
			.get("professor")
			.setValue(itens[0].professor);
		modal.componentInstance.formGroup
			.get("ambiente")
			.setValue(itens[0].ambiente);
		modal.componentInstance.formGroup
			.get("horaInicial")
			.setValue(itens[0].horaInicial);
		modal.componentInstance.formGroup
			.get("horaFinal")
			.setValue(itens[0].horaFinal);
		modal.componentInstance.formGroup
			.get("diaSemana")
			.setValue(itens[0].diaSemana);
		modal.componentInstance.formGroup
			.get("permitirAgendarAppTreino")
			.setValue(itens[0].permieAgendarAppTreino);
		modal.componentInstance.formGroup
			.get("apenasAlunosCarteira")
			.setValue(itens[0].apenasAlunosCarteira);
		modal.result.then(
			(dto) => {
				this.disponibilidadeService
					.existeAgendamentoAlunoHorarioDisponibilidade(0, itens)
					.subscribe((ret) => {
						setTimeout(() => {
							const handler = this.modalService.open(
								"Editar Horário",
								HorarioRemoveEditConfirmModalComponent,
								PactoModalSize.MEDIUM
							);
							handler.componentInstance.exclusao = false;
							handler.componentInstance.body =
								this.actionNoUndone.nativeElement.innerHTML;
							handler.componentInstance.possuiAlunoAgendado = false;
							if (ret && ret.retorno) {
								handler.componentInstance.possuiAlunoAgendado = true;
								handler.componentInstance.body =
									this.editOrExcludeHorarioDispLarge.nativeElement.innerHTML;
							}
							handler.result.then(
								(confirm: boolean) => {
									if (confirm) {
										if (dto) {
											itens.forEach((t) => {
												t.professor = dto.professor;
												t.ambiente = dto.ambiente;
												t.horaInicial = dto.horaInicial;
												t.horaFinal = dto.horaFinal;
												t.permieAgendarAppTreino = dto.permitirAgendarAppTreino;
												t.apenasAlunosCarteira = dto.apenasAlunosCarteira;
											});
											this.horariosData.content = itens;
											naoItens.forEach((nI) => {
												this.horariosData.content.push(nI);
											});
											this.formCheckAll.setValue(false);
											this.formCheckAll.valueChanges.subscribe((v: boolean) => {
												this.checkAll(v);
											});
											this.notificationService.success(
												this.notificacoesTranslate.getLabel(
													"horarioEditSuccess"
												)
											);
										}
									}
								},
								() => {}
							);
						});
					});
			},
			() => {}
		);
	}

	getModalAdicionarHorario(title, modalComponent: any) {
		let modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-agenda-turma-horario"
		);
		modal.componentInstance.edicao = false;
		modal.componentInstance.variosHorarios = false;
		modal.componentInstance.ambientes = this.ambientes.content;
		modal.componentInstance.professores = this.professores.content;
		modal.componentInstance.tipoHorario =
			this.formGroup.get("tipoHorario").value;
		modal.result.then(
			(dto) => {
				this.isModalOpen = false;
				if (dto) {
					const horarios = [];
					dto.forEach((d) => {
						horarios.push({
							codigo: null,
							professor: d.professor,
							ambiente: d.ambiente,
							horaInicial: d.horaInicial,
							horaFinal: d.horaFinal,
							diaSemana: d.diaSemana,
							ativo: true,
							permieAgendarAppTreino: d.permitirAgendarAppTreino,
							apenasAlunosCarteira: d.apenasAlunosCarteira,
						});
					});
					if (
						this.disponibilidadeCreateEdit &&
						this.disponibilidadeCreateEdit.codigo &&
						this.disponibilidadeCreateEdit.codigo > 0
					) {
						horarios.forEach((h) => {
							this.disponibilidadeCreateEdit.horarios.push(h);
						});
						this.createHorariosPageObject(1, 5, true, null, true);
					} else {
						horarios.forEach((h) => {
							this.horariosNovosAdd.push(h);
						});
						this.formGroup.get("horarios").setValue(this.horariosNovosAdd);
						this.createHorariosPageObject(
							1,
							5,
							true,
							this.horariosNovosAdd,
							true
						);
					}
				}
			},
			() => {
				this.isModalOpen = false;
			}
		);
	}

	private getProfessor(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradores(false, true, true)
			.pipe(
				map((dados) => {
					dados.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professores = dados;
					return true;
				})
			);
		return professores$;
	}

	private getAmbientes(): Observable<any> {
		const ambientes$ = this.ambienteService.obterTodosAmbientes(true).pipe(
			map((dados) => {
				dados.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientes = dados;
				return true;
			})
		);
		return ambientes$;
	}

	private getSelectItemsPactoCatSelect(
		items: any
	): Array<SelectItemPactoCatSelect> {
		const selectItems = new Array<SelectItemPactoCatSelect>();
		items.forEach((d) => {
			const selectItem = new SelectItemPactoCatSelect();
			selectItem.value = d;
			selectItem.label = d;
			selectItems.push(selectItem);
		});
		return selectItems;
	}

	private getSelectItemsPactoSelect(items: any): Array<SelectItemPactoSelect> {
		const selectItems = new Array<SelectItemPactoSelect>();
		items.forEach((d) => {
			const selectItem = new SelectItemPactoSelect();
			selectItem.id = d.id;
			selectItem.nome = d.nome;
			selectItems.push(selectItem);
		});
		return selectItems;
	}

	btnClickFiltrarTabela() {
		const diasSemana = [];
		if (this.formGroupFilter.get("segFc").value) {
			diasSemana.push("SG");
		}
		if (this.formGroupFilter.get("terFc").value) {
			diasSemana.push("TR");
		}
		if (this.formGroupFilter.get("quaFc").value) {
			diasSemana.push("QA");
		}
		if (this.formGroupFilter.get("quiFc").value) {
			diasSemana.push("QI");
		}
		if (this.formGroupFilter.get("sexFc").value) {
			diasSemana.push("SX");
		}
		if (this.formGroupFilter.get("sabFc").value) {
			diasSemana.push("SB");
		}
		if (this.formGroupFilter.get("domFc").value) {
			diasSemana.push("DM");
		}
		this.tableData.addFilter(
			"filtrosNew",
			JSON.stringify({
				ativo: this.formGroupFilter.get("vigencia").value,
				professor: this.formGroupFilter.get("professor").value,
				ambiente: this.formGroupFilter.get("ambiente").value,
				diaSemana: diasSemana,
				horaInicial: this.formGroupFilter.get("horaInicial").value,
				horaFinal: this.formGroupFilter.get("horaFinal").value,
			})
		);
		this.tableData.reloadData();
		this.filter.close();
	}

	private configTable() {
		const tooltipInativar = this.tooltipInativar.nativeElement.innerHTML;
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.table = new PactoDataGridConfig({
			ghostLoad: true,
			ghostAmount: 5,
			logUrl: this.rest.buildFullUrl(
				`log/disponibilidades/${
					this.disponibilidadeCreateEdit
						? this.disponibilidadeCreateEdit.codigo
						: ""
				}`
			),
			dataAdapterFn: (serverData) => {
				return this.horariosData;
			},
			pagination: true,
			state: this.state,
			columns: [
				{
					nome: "codigo",
					titulo: this.codigoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "codigo",
					celula: this.codigoCelula,
				},
				{
					nome: "professor",
					titulo: this.professorColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "professor",
					celula: this.professorCelula,
				},
				{
					nome: "ambiente",
					titulo: this.ambienteColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "ambiente",
					celula: this.ambienteCelula,
				},
				{
					nome: "disponibilidade",
					titulo: this.disponibilidadeColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "disponibilidade",
					celula: this.disponibilidadeCelula,
				},
				{
					nome: "funcionamento",
					titulo: this.funcionamentoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "tipoHorario",
					celula: this.funcionamentoCelula,
				},
				{
					nome: "diaSemana",
					titulo: this.diaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "dia",
					celula: this.diasCelula,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					showIconFn: (row) => row.ativo,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipInativar,
					showIconFn: (row) => row.ativo,
				},
			],
		});
		if (this.tableData) {
			this.tableData.pageSizeControl.setValue(5);
		}
	}

	private loadForm() {
		this.formGroup
			.get("codigo")
			.setValue(this.disponibilidadeCreateEdit.codigo);
		this.formGroup.get("nome").setValue(this.disponibilidadeCreateEdit.nome);
		this.formGroup.get("cor").setValue(this.disponibilidadeCreateEdit.cor);
		this.formGroup
			.get("comportamento")
			.setValue(this.disponibilidadeCreateEdit.comportamento);
		this.formGroup
			.get("intervaloDiasEntreAgendamentos")
			.setValue(this.disponibilidadeCreateEdit.intervaloDiasEntreAgendamentos);
		this.formGroup
			.get("intervaloMinimoDiasCasoFalta")
			.setValue(this.disponibilidadeCreateEdit.intervaloMinimoDiasCasoFalta);
		this.formGroup
			.get("horarios")
			.setValue(this.disponibilidadeCreateEdit.horarios);
		this.formGroup
			.get("tipoValidacao")
			.setValue(this.disponibilidadeCreateEdit.tipoValidacao);
		this.formGroup
			.get("itensValidacao")
			.setValue(this.disponibilidadeCreateEdit.itensValidacao);
		this.formGroup
			.get("tipoHorario")
			.setValue(this.disponibilidadeCreateEdit.tipoHorario);
		this.formGroup
			.get("duracao")
			.setValue(this.disponibilidadeCreateEdit.duracao);
		this.formGroup
			.get("duracaoMinima")
			.setValue(this.disponibilidadeCreateEdit.duracaoMinima);
		this.formGroup
			.get("duracaoMaxima")
			.setValue(this.disponibilidadeCreateEdit.duracaoMaxima);
		this.formGroup
			.get("descricao")
			.setValue(this.disponibilidadeCreateEdit.descricao);
		this.formGroup
			.get("base64Imagem")
			.setValue(this.disponibilidadeCreateEdit.base64Imagem);
		this.formGroup
			.get("formControlNomeImagem")
			.setValue(this.disponibilidadeCreateEdit.formControlNomeImagem);
		this.formGroup
			.get("dataInicial")
			.setValue(new Date(this.disponibilidadeCreateEdit.dataInicial).getTime());
		this.formGroup
			.get("dataFinal")
			.setValue(new Date(this.disponibilidadeCreateEdit.dataFinal).getTime());
		this.urlImagem = this.disponibilidadeCreateEdit.urlImagem;
		this.cd.detectChanges();
	}

	submitHandler() {
		this.markAsTouched();
		if (!this.entity) {
			this.editHandler();
		} else {
			this.createHandler();
		}
	}

	markAsTouched() {
		this.formGroup.get("codigo").markAsTouched();
		this.formGroup.get("nome").markAsTouched();
		this.formGroup.get("cor").markAsTouched();
		this.formGroup.get("comportamento").markAsTouched();
		this.formGroup.get("intervaloDiasEntreAgendamentos").markAsTouched();
		this.formGroup.get("intervaloMinimoDiasCasoFalta").markAsTouched();
		this.formGroup.get("horarios").markAsTouched();
		this.formGroup.get("tipoValidacao").markAsTouched();
		this.formGroup.get("tipoHorario").markAsTouched();
		this.formGroup.get("descricao").markAsTouched();
		this.formGroup.get("dataInicial").markAsTouched();
		this.formGroup.get("dataFinal").markAsTouched();
	}

	private createHandler() {
		if (this.formGroup.valid) {
			this.disponibilidadeCreateEdit = this.formGroup.getRawValue();
			this.disponibilidadeCreateEdit.horarios.forEach((h) => {
				if (h.ativo === null || h.ativo === undefined) {
					h.ativo = true;
				}
			});

			if (
				this.formGroup.get("tipoHorario").value == 1 &&
				this.formGroup.get("duracao").value == null
			) {
				this.notificationService.error(
					'O tipo de horário "Pré-definido" requer preenchimento do campo "Duração em minutos"'
				);
				return;
			}
			if (
				this.formGroup.get("tipoHorario").value == 2 &&
				(this.formGroup.get("duracaoMinima").value == null ||
					this.formGroup.get("duracaoMaxima").value == null)
			) {
				this.notificationService.error(
					'O tipo de horário "Intervalo de tempo" requer preenchimento dos campos "Duração mínima em minutos" e "Duração máxima em minutos"'
				);
				return;
			}
			this.disponibilidadeService
				.saveOrUpdate(this.disponibilidadeCreateEdit)
				.subscribe((result) => {
					if (result.retorno) {
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("createSuccess")
						);
						this.cancelHandler(false);
					} else {
						this.notificationService.error(result.erro);
					}
				});
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	private editHandler() {
		if (this.formGroup.valid) {
			this.disponibilidadeCreateEdit = this.formGroup.getRawValue();
			this.disponibilidadeCreateEdit.urlImagem = this.urlImagem;
			this.disponibilidadeCreateEdit.horarios.forEach((h) => {
				if (!h.codigo && (h.ativo === null || h.ativo === undefined)) {
					h.ativo = true;
				}
			});
			if (
				this.formGroup.get("tipoHorario").value == 1 &&
				this.formGroup.get("duracao").value == null
			) {
				this.notificationService.error(
					'O tipo de horário "Pré-definido" requer preenchimento do campo "Duração em minutos"'
				);
				return;
			}
			this.disponibilidadeService
				.saveOrUpdate(this.disponibilidadeCreateEdit)
				.subscribe(
					(result) => {
						if (result.retorno) {
							this.notificationService.success(
								this.notificacoesTranslate.getLabel("editSuccess")
							);
							this.cancelHandler(false);
						} else {
							this.notificationService.error(result.erro);
						}
					},
					(error) => {
						// Verificação manual no lugar do operador de encadeamento opcional
						const errorMessage =
							error &&
							error.error &&
							error.error.meta &&
							error.error.meta.message
								? error.error.meta.message
								: "Erro ao atualizar a disponibilidade.";
						this.notificationService.error(errorMessage);
					}
				);
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	limparItensValidacao() {
		this.formGroup.get("itensValidacao").setValue(null);
	}

	limparValoresTipoHorario() {
		this.formGroup.get("duracao").setValue(null);
		this.formGroup.get("duracaoMinima").setValue(null);
		this.formGroup.get("duracaoMaxima").setValue(null);
	}

	cancelHandler(value: boolean) {
		if (value && this.formGroup.valid && this.disponibilidadeCreateEdit) {
			this.disponibilidadeCreateEditDps = this.formGroup.getRawValue();
			if (
				this.isEquivalent(
					this.disponibilidadeCreateEdit,
					this.disponibilidadeCreateEditDps
				)
			) {
				this.router.navigate(["agenda", "disponibilidade"]);
			} else {
				this.openModalConfirmTurmaEdit(
					"Alterações não salvas",
					ConfirmDisponibilidadeCrudModalComponent
				);
			}
		} else {
			this.router.navigate(["agenda", "disponibilidade"], { replaceUrl: true });
			this.location.replaceState("/agenda/disponibilidade");
		}
	}

	isEquivalent(a, b) {
		const aProps = Object.getOwnPropertyNames(a);
		const bProps = Object.getOwnPropertyNames(b);
		if (aProps.length !== bProps.length + 2) {
			return false;
		}

		for (let i = 0; i < aProps.length; i++) {
			const propName = aProps[i];
			if (
				!(propName === "diasSemana" || propName === "urlImagem") &&
				a[propName] !== b[propName]
			) {
				return false;
			}
		}
		return true;
	}

	openModalConfirmTurmaEdit(title, modalComponent: any) {
		const ref = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.MEDIUM
		);
		ref.result.then(
			(value) => {
				if (value) {
					this.router.navigate(["agenda", "disponibilidade"]);
				}
			},
			() => {}
		);
	}

	verificaCamposValidos() {
		this.isFormGroupValid = this.formGroup.status === "VALID" ? true : false;
	}

	tipoHorarioName(item: number): string {
		if (item !== undefined || item !== null) {
			const i = this.tipoHorarios.find((t) => t.id == item);
			if (i) {
				return i.nome;
			} else {
				return "";
			}
		}
		return "";
	}

	convertDiaSemanaString(d: string): string {
		let ret = "";
		if (d !== undefined || d !== null || d !== "") {
			if (d === "SG") {
				ret = "Seg";
			} else if (d === "TR") {
				ret = "Ter";
			} else if (d === "QA") {
				ret = "Qua";
			} else if (d === "QI") {
				ret = "Qui";
			} else if (d === "SX") {
				ret = "Sex";
			} else if (d === "SB") {
				ret = "Sab";
			} else if (d === "DM") {
				ret = "Dom";
			}
		}
		return ret;
	}

	clearVigencia() {
		this.formGroupFilter.get("vigencia").setValue(null);
	}

	clearProfessores() {
		this.formGroupFilter.get("professor").setValue(null);
	}

	clearAmbientes() {
		this.formGroupFilter.get("ambiente").setValue(null);
	}

	clearHorarios() {
		this.formGroupFilter.get("horaInicial").setValue(null);
		this.formGroupFilter.get("horaFinal").setValue(null);
	}

	limparFiltros() {
		this.formGroupFilter.reset();
	}

	clearDiasSemana() {
		this.formGroupFilter.get("segFc").setValue(null);
		this.formGroupFilter.get("terFc").setValue(null);
		this.formGroupFilter.get("quaFc").setValue(null);
		this.formGroupFilter.get("quiFc").setValue(null);
		this.formGroupFilter.get("sexFc").setValue(null);
		this.formGroupFilter.get("sabFc").setValue(null);
		this.formGroupFilter.get("domFc").setValue(null);
	}

	filterChangeEvent(event: any) {
		event.page = event.page === 0 ? 1 : event.page + 1;
		let filters = null;

		if (event.filters && event.filters !== "{}") {
			try {
				filters = JSON.parse(JSON.parse(event.filters).filtrosNew);
			} catch (e) {
				console.error("Erro ao parsear os filtros", e);
			}
		} else {
			this.formGroupFilter.get("vigencia").setValue([
				{
					id: true,
					nome: "Ativo",
				},
			]);
			filters = {
				ativo: [
					{
						id: true,
						nome: "Ativo",
					},
				],
			};
		}
		if (
			filters.horaInicial ||
			filters.horaFinal ||
			(filters.ambiente && filters.ambiente.length > 0) ||
			(filters.professor && filters.professor.length > 0) ||
			(filters.diaSemana && filters.diaSemana.length > 0) ||
			(filters.ativo && filters.ativo.length > 0)
		) {
			const horariosPercorrer =
				this.disponibilidadeCreateEdit &&
				this.disponibilidadeCreateEdit.horarios
					? this.disponibilidadeCreateEdit.horarios
					: this.formGroup.get("horarios").value;
			const horarios = new Set<HorarioDisponibilidade>();
			horariosPercorrer.forEach((h) => {
				if (filters.horaInicial) {
					if (h.horaInicial === filters.horaInicial) {
						if (filters.ativo && filters.ativo.length > 0) {
							if (
								filters.ativo.length === 1 &&
								h.ativo === filters.ativo[0].id
							) {
								horarios.add(h);
							} else if (filters.ativo.length === 2) {
								horarios.add(h);
							}
						} else {
							horarios.add(h);
						}
					}
				}
				if (filters.horaFinal) {
					if (h.horaFinal === filters.horaFinal) {
						if (filters.ativo && filters.ativo.length > 0) {
							if (
								filters.ativo.length === 1 &&
								h.ativo === filters.ativo[0].id
							) {
								horarios.add(h);
							} else if (filters.ativo.length === 2) {
								horarios.add(h);
							}
						} else {
							horarios.add(h);
						}
					}
				}
				if (filters.ambiente && filters.ambiente.length > 0) {
					filters.ambiente.forEach((a) => {
						if (h.ambiente.id === a.id) {
							if (filters.ativo && filters.ativo.length > 0) {
								if (
									filters.ativo.length === 1 &&
									h.ativo === filters.ativo[0].id
								) {
									horarios.add(h);
								} else if (filters.ativo.length === 2) {
									horarios.add(h);
								}
							} else {
								horarios.add(h);
							}
						}
					});
				}
				if (filters.professor && filters.professor.length > 0) {
					filters.professor.forEach((p) => {
						if (h.professor.id === p.id) {
							if (filters.ativo && filters.ativo.length > 0) {
								if (
									filters.ativo.length === 1 &&
									h.ativo === filters.ativo[0].id
								) {
									horarios.add(h);
								} else if (filters.ativo.length === 2) {
									horarios.add(h);
								}
							} else {
								horarios.add(h);
							}
						}
					});
				}
				if (filters.diaSemana && filters.diaSemana.length > 0) {
					filters.diaSemana.forEach((d) => {
						if (h.diaSemana === d) {
							if (filters.ativo && filters.ativo.length > 0) {
								if (
									filters.ativo.length === 1 &&
									h.ativo === filters.ativo[0].id
								) {
									horarios.add(h);
								} else if (filters.ativo.length === 2) {
									horarios.add(h);
								}
							} else {
								horarios.add(h);
							}
						}
					});
				}
				if (
					!filters.horaInicial &&
					!filters.horaFinal &&
					!(filters.diaSemana && filters.diaSemana.length > 0) &&
					!(filters.professor && filters.professor.length > 0) &&
					!(filters.ambiente && filters.ambiente.length > 0)
				) {
					filters.ativo.forEach((ativo) => {
						if (h.ativo === ativo.id) {
							horarios.add(h);
						} else if (filters.ativo.length === 2) {
							horarios.add(h);
						}
					});
				}
			});
			const newHorarios = [];
			horarios.forEach((h) => {
				newHorarios.push(h);
			});
			if (newHorarios.length <= event.size) {
				event.page = 1;
			}
			this.createHorariosPageObject(
				event.page,
				event.size,
				true,
				newHorarios,
				false
			);
		} else {
			this.createHorariosPageObject(event.page, event.size, true, null, false);
		}
		this.initFormsHorariosCheck();
		this.formCheckAll.setValue(false);
		this.formCheckAll.valueChanges.subscribe((v: boolean) => {
			this.checkAll(v);
		});
	}

	initFormsHorariosCheck() {
		this.formsCheckHorarios = new Array<{
			horarioDisponibilidade: HorarioDisponibilidade;
			form: FormControl;
		}>();
		if (this.horariosData.content) {
			this.horariosData.content.forEach((lh) =>
				this.formsCheckHorarios.push({
					horarioDisponibilidade: lh,
					form: new FormControl(),
				})
			);
		}
	}

	private checkAll(v: boolean) {
		this.formsCheckHorarios.forEach((f) => {
			f.form.setValue(v);
		});
	}

	getFormCheckHorario(
		horarioDisponibilidade: HorarioDisponibilidade
	): FormControl {
		if (this.formsCheckHorarios.length === 0) {
			this.initFormsHorariosCheck();
		}
		let formControl: FormControl;
		this.formsCheckHorarios.forEach((f) => {
			if (f.horarioDisponibilidade === horarioDisponibilidade) {
				formControl = f.form;
			}
		});
		if (formControl === undefined) {
			formControl = new FormControl();
			this.formsCheckHorarios.push({
				horarioDisponibilidade,
				form: new FormControl(),
			});
		}
		return formControl;
	}
}
