import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
	TreinoApiAulaService,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { LayoutModule } from "../../../../projects/adm/src/app/layout/layout.module";
import { DisponibilidadeListaComponent } from "./components/disponibilidade-lista/disponibilidade-lista.component";
import { DisponibilidadeCrudComponent } from "./components/disponibilidade-crud/disponibilidade-crud.component";
import { DisponibilidadeHorarioCrudModalComponent } from "./components/disponibilidade-crud/disponibilidade-horario-crud/disponibilidade-horario-crud-modal.component";
import { ConfirmDisponibilidadeCrudModalComponent } from "./components/disponibilidade-crud/modal-confirm-disponibilidade-edit/confirm-disponibilidade-crud-modal.component";
import { HorarioRemoveEditConfirmModalComponent } from "./components/disponibilidade-crud/disponibilidade-horario-crud/modal-horario-remove-edit-confirm/horario-remove-edit-confirm-modal.component";
import { DisponibilidadeRemoveConfirmModalComponent } from "./components/disponibilidade-lista/modal-disponibilidade-remove-confirm/disponibilidade-remove-confirm-modal.component";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS,
	true
);

const routes: Routes = [
	{
		path: "",
		component: DisponibilidadeListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "adicionar",
		component: DisponibilidadeCrudComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: ":id",
		component: DisponibilidadeCrudComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		BaseSharedModule,
		CommonModule,
		MatSlideToggleModule,
		LayoutModule,
	],
	declarations: [
		DisponibilidadeListaComponent,
		DisponibilidadeCrudComponent,
		DisponibilidadeHorarioCrudModalComponent,
		ConfirmDisponibilidadeCrudModalComponent,
		HorarioRemoveEditConfirmModalComponent,
		DisponibilidadeRemoveConfirmModalComponent,
	],
	entryComponents: [
		DisponibilidadeHorarioCrudModalComponent,
		ConfirmDisponibilidadeCrudModalComponent,
		HorarioRemoveEditConfirmModalComponent,
		DisponibilidadeRemoveConfirmModalComponent,
	],
	providers: [TreinoApiAulaService],
})
export class DisponibilidadeModule {}
