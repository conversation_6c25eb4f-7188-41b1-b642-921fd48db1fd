import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { HomeComponent } from "./home/<USER>";
import { RouterModule, Routes } from "@angular/router";
import { BaseSharedModule } from "@base-shared/base-shared.module";

const routes: Routes = [
	{
		path: "",
		component: HomeComponent,
	},
];

@NgModule({
	declarations: [HomeComponent],
	imports: [CommonModule, RouterModule.forChild(routes), BaseSharedModule],
})
export class ConfiguracaoModule {}
