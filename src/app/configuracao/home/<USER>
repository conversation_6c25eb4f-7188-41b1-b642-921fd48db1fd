@import "~src/assets/scss/pacto/plataforma-import.scss";

.card {
	background: #ffffff;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	margin: 24px 20px 24px 20px;
	align-items: center;
	justify-content: center;
	padding-bottom: 60px;
	padding-top: 75px;
}

.icone-lampada {
	width: 112px;
	height: 112px;
	padding-bottom: 21px;
}

.container-descricao {
	display: flex;
	align-items: baseline;
}

.container-descricao > img {
	font-size: 52px;
	width: 32px;
	height: 32px;
	color: #d7d8db;
	margin-right: 22px;
}

.container-descricao > span {
	font-weight: 600;
	font-size: 14px;
	line-height: 125%;
	letter-spacing: 0.25px;
	color: #55585e;
}

button {
	margin-top: 20px;
	color: #fff;
	background-color: #6c757d;
	border-color: #6c757d;
}

.container-descricao-info {
	display: flex;
	align-items: baseline;
	padding-top: 20px;
	text-align: center;

	.info {
		font-family: <PERSON><PERSON><PERSON>;
		font-size: 14px;
		font-weight: 400;
		color: #797d86;
	}
}

.nav-aux {
	display: grid;
	grid-template-columns: 1fr 1fr;
	align-items: center;
	flex-basis: 100%;
	margin: 24px 20px 24px 20px;
	font-size: 32px;
	font-weight: 400;
	line-height: 44px;

	:first-child {
		justify-self: start; /* Alinhamento esquerdo */
	}

	:last-child {
		justify-self: end; /* Alinhamento direito */
	}

	a {
		color: $pretoPri;
	}

	i {
		margin-right: 12px;
	}

	.acesso {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $preto02;
		margin-left: 45px;
	}

	#voltar-alunos {
		cursor: pointer;
	}
}

.toast-beta {
	background-color: #fee6e6;
	border-radius: 5px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin: 32px 16px 0px 16px;
	padding: 8px;

	.toast-text {
		color: #e10505;
		display: flex;

		span {
			.icone {
				font-size: 18px;
				padding: 8px;
			}

			strong {
				//styleName: Button/Default/2;
				font-family: Poppins;
				font-size: 12px;
				font-weight: 600;
				line-height: 12px;
				letter-spacing: 0.25px;
				text-align: left;
			}

			//styleName: Overline/2;
			font-family: Nunito Sans;
			font-size: 14px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
		}
	}

	.toast-button {
		padding: 6px;
	}
}
