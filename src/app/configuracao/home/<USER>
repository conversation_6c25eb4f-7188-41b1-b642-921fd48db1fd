import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { FeatureManagerService } from "sdk";
import { PlataformaModulo } from "src/app/microservices/client-discovery/client-discovery.model";
import { AdmCoreApiNegociacaoService } from "adm-core-api";

@Component({
	selector: "pacto-home",
	templateUrl: "./home.component.html",
	styleUrls: ["./home.component.scss"],
})
export class HomeComponent implements OnInit {
	novaTelaConfig = false;
	recurso = "CONFIGURACOES";
	recursoPadraoEmpresa: boolean = true;
	apresentarTela: boolean = false;

	constructor(
		private featureMangerService: FeatureManagerService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private router: Router,
		private navigationService: LayoutNavigationService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService
	) {}

	ngOnInit() {
		this.carregarRecursoPadraoEmpresa();
		this.admCoreApiNegociacaoService.recursoHabilitado(this.recurso).subscribe({
			next: (response) => {
				this.novaTelaConfig = response;
				if (this.novaTelaConfig) {
					this.acessarNovaVersao();
				} else {
					this.apresentarTela = true;
				}
				this.cd.detectChanges();
			},
			error: (err) => {
				console.log(err);
				this.apresentarTela = true;
			},
		});
	}

	carregarRecursoPadraoEmpresa() {
		this.admCoreApiNegociacaoService
			.recursoPadraoEmpresa(this.recurso)
			.subscribe(
				(response) => {
					this.recursoPadraoEmpresa = response;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
					this.apresentarTela = true;
					this.recursoPadraoEmpresa = false;
					this.cd.detectChanges();
				}
			);
	}

	acessarNovaVersao() {
		this.router.navigate(["adm", "configuracao", "v2"]);
	}

	get apresentarUsarNovaVersao(): boolean {
		return (
			!this.recursoPadraoEmpresa &&
			(this.sessionService.isModuloHabilitado(PlataformaModulo.ZW) ||
				this.sessionService.isModuloHabilitado(PlataformaModulo.NZW))
		);
	}
}
