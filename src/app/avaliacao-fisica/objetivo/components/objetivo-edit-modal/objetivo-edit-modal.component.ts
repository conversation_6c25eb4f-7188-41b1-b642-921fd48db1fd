import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-objetivo-edit-modal",
	templateUrl: "./objetivo-edit-modal.component.html",
	styleUrls: ["./objetivo-edit-modal.component.scss"],
})
export class ObjetivoEditModalComponent implements OnInit {
	@ViewChild("campoObrigatorio", { static: true }) campoObrigatorio;

	control: FormControl = new FormControl("", [
		Validators.required,
		Validators.minLength(3),
	]);

	editando = false;

	constructor(
		private openModal: NgbActiveModal,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		if (this.control.value) {
			this.editando = true;
		}
	}

	dismiss() {
		this.openModal.dismiss();
	}

	close() {
		this.control.markAsTouched();
		if (this.control.valid) {
			this.openModal.close(this.control.value);
		} else {
			const campoObrigatorio = this.campoObrigatorio.nativeElement.innerHTML;
			this.snotifyService.error(campoObrigatorio);
		}
	}
}
