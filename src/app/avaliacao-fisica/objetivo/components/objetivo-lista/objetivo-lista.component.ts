import { Component, OnInit, ViewChild } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import {
	Objetivo,
	PerfilAcessoRecursoNome,
	TreinoApiObjetivoService,
} from "treino-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { ObjetivoEditModalComponent } from "../objetivo-edit-modal/objetivo-edit-modal.component";

@Component({
	selector: "pacto-objetivo-lista",
	templateUrl: "./objetivo-lista.component.html",
	styleUrls: ["./objetivo-lista.component.scss"],
})
export class ObjetivoListaComponent implements OnInit {
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;

	@ViewChild("addLabel", { static: true }) addLabel;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;

	@ViewChild("modalTitle", { static: true }) modalTitle;
	@ViewChild("modalBody", { static: true }) modalBody;
	@ViewChild("modalMensageSuccess", { static: true }) modalMensageSuccess;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("removeError", { static: true }) removeError;
	@ViewChild("creatEditError", { static: true }) creatEditError;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;

	constructor(
		private modalService: ModalService,
		private objetivoService: TreinoApiObjetivoService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private openModal: NgbModal,
		public session: SessionService
	) {}

	nomeObjetivo = "";
	table: PactoDataGridConfig;
	permissoesObjetivos;

	ngOnInit() {
		this.loadAllow();
		this.configTable();
	}

	loadAllow() {
		this.permissoesObjetivos = this.session.recursos.get(
			PerfilAcessoRecursoNome.OBJETIVOS
		);
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("avaliacao-objetivos"),
			logUrl: this.rest.buildFullUrl("log/avaliacao-objetivos"),
			quickSearch: true,
			rowClick: this.permissoesObjetivos.editar,
			buttons: !this.permissoesObjetivos.incluir
				? null
				: {
						conteudo: this.addLabel,
						nome: "add",
						id: "novo-objetivo",
				  },
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.permissoesObjetivos.excluir,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.remove($event.row);
		}
	}

	rowClickHandler(item: any) {
		const editSuccess = this.editSuccess.nativeElement.innerHTML;
		const creatEditError = this.creatEditError.nativeElement.innerHTML;
		const handler = this.openModal.open(ObjetivoEditModalComponent);
		handler.componentInstance.control.setValue(item.nome);
		handler.result.then((result) => {
			const objetivo: Objetivo = { id: item.id, nome: result };
			this.objetivoService
				.atualizarObjetivo(objetivo, item.id)
				.subscribe((resultEdit) => {
					if (resultEdit === "registro_duplicado") {
						this.snotifyService.error(creatEditError);
					} else {
						this.tableData.reloadData();
						this.snotifyService.success(editSuccess);
					}
				});
		});
	}

	addClick() {
		const createSuccess = this.createSuccess.nativeElement.innerHTML;
		const creatEditError = this.creatEditError.nativeElement.innerHTML;
		const handler = this.openModal.open(ObjetivoEditModalComponent);
		handler.result.then((result) => {
			const objetivo: Objetivo = { nome: result };
			this.objetivoService
				.cadastrarObjetivo(objetivo)
				.subscribe((resultCreate) => {
					if (resultCreate === "registro_duplicado") {
						this.snotifyService.error(creatEditError);
						this.addClick();
					} else {
						this.tableData.reloadData();
						this.snotifyService.success(createSuccess);
					}
				});
		});
	}

	private remove(item) {
		this.nomeObjetivo = item.nome;
		setTimeout(() => {
			const modalTitle = this.modalTitle.nativeElement.innerHTML;
			const modalBody = this.modalBody.nativeElement.innerHTML;
			const modalMensageSuccess =
				this.modalMensageSuccess.nativeElement.innerHTML;
			const removeError = this.removeError.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			handler.result.then(() => {
				this.objetivoService.removerObjetivo(item.id).subscribe((result) => {
					if (result === "objetivo_em_uso") {
						this.snotifyService.error(removeError);
					} else {
						this.snotifyService.success(modalMensageSuccess);
						this.tableData.reloadData();
					}
				});
			});
		});
	}
}
