<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="addClick()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="rowClickHandler($event)"
			[sessionService]="session"
			[tableDescription]="descricao"
			[tableTitle]="titulo"
			[table]="table"
			telaId="objetivo"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--Tables columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud-objetivos:table:nome">Nome</span>
</ng-template>
<!--End Tables columns-->

<!--Buttons-->
<ng-template #addLabel>
	<span i18n="@@crud-objetivos:addBtn">Adicionar</span>
</ng-template>
<!--End Buttons -->

<!--Title-->
<ng-template #titulo>
	<span i18n="@@crud-objetivos:title">Objetivos</span>
</ng-template>
<ng-template #descricao>
	<span i18n="@@crud-objetivos:description">
		Gerencie os objetivos cadastrados
	</span>
</ng-template>
<!--End title-->

<!--Modal remove-->
<span #modalTitle [hidden]="true" i18n="@@crud-objetivos:modal:remove:title">
	Remover Objetivo
</span>
<span #modalBody [hidden]="true" i18n="@@crud-objetivos:modal:remove:body">
	Deseja remover o {{ nomeObjetivo }}?
</span>
<span
	#modalMensageSuccess
	[hidden]="true"
	i18n="@@crud-objetivos:modal:remove:mensage-success">
	Objetivo removido com sucesso.
</span>
<span
	#removeError
	[hidden]="true"
	i18n="@@crud-objetivos:modal:remove:mensage-error">
	Não foi possível excluir o objetivo pois ele tem vínculo!
</span>
<!--End modal remove-->

<!--tooltip icons-->
<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-objetivos:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipEditar
	[hidden]="true"
	i18n="@@crud-objetivos:remover:tooltip-icon">
	Editar
</span>
<!--end tooltip icons-->
<!--Modal create/edit-->
<span
	#createSuccess
	[hidden]="true"
	i18n="@@crud-objetivos:modal:create:mensage-success">
	Objetivo criado com sucesso.
</span>
<span
	#editSuccess
	[hidden]="true"
	i18n="@@crud-objetivos:modal:edit:mensage-success">
	Objetivo editado com sucesso.
</span>
<span
	#creatEditError
	[hidden]="true"
	i18n="@@crud-objetivos:modal:create-edit:mensage-error">
	Já existe um cadastro com esse nome !
</span>
<!--End modal create/edit-->
<!--Title icone ações-->
<span #tooltipIcon [hidden]="true" i18n="@@crud-objetivos:tooltip-icon">
	Excluir
</span>
