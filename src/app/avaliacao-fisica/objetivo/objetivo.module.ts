import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ObjetivoListaComponent } from "./components/objetivo-lista/objetivo-lista.component";
import { ObjetivoEditModalComponent } from "./components/objetivo-edit-modal/objetivo-edit-modal.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Routes } from "@angular/router";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";

const recurso = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.OBJETIVOS, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: ObjetivoListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), CommonModule, BaseSharedModule],
	declarations: [ObjetivoListaComponent, ObjetivoEditModalComponent],
	entryComponents: [ObjetivoEditModalComponent],
})
export class ObjetivoModule {}
