<div *ngIf="!loading && !sucesso && !falhaLeitura" class="modal-bio-content">
	<div class="titulo">Para uma medida precisa, siga os passos abaixo:</div>
	<div class="descricao">
		<div class="content">
			<p>- Tenha a alimentação leve nas últimas 3 horas;</p>
			<p>- Esteja vestindo roupas leves;</p>
			<p>- Retire os calçados e meias;</p>
			<p>- Retire acessórios como brincos, relógios, pulseiras e etc;</p>
			<p>- Não esteja com fones de ouvido, carteira, chaves e etc;</p>
			<p>- Permaneça imóvel até a balança terminar a leitura;</p>
			<p>- Aguarde 2 bipes e que a luz verde permaneça acesa.</p>
		</div>
	</div>
	<div class="modal-bio-button">
		<div class="check-box">
			<pacto-cat-checkbox
				[control]="fg.get('checked')"
				[label]="
					'Eu li todas as recomendações e estou pronto para iniciar a avaliação.'
				"></pacto-cat-checkbox>
		</div>
		<div class="botao-ligar">
			<pacto-cat-button
				(click)="ligarBalanca()"
				[icon]="'power'"
				[label]="'ligar balança '"></pacto-cat-button>
		</div>
	</div>
</div>

<div *ngIf="loading" class="loading" id="loading">
	<div class="container">
		<div class="box">
			<div class="spinner">
				<div class="bar1"></div>
				<div class="bar2"></div>
				<div class="bar3"></div>
				<div class="bar4"></div>
				<div class="bar5"></div>
				<div class="bar6"></div>
				<div class="bar7"></div>
				<div class="bar8"></div>
				<div class="bar9"></div>
				<div class="bar10"></div>
				<div class="bar11"></div>
				<div class="bar12"></div>
			</div>
		</div>
	</div>
	<div class="container-text">
		<div class="text-box">
			Aguarde enquanto a balança esta fazendo a leitura.
		</div>
	</div>
</div>

<div *ngIf="sucesso" class="sucesso" id="sucesso">
	<div class="container">
		<div class="box">
			<i class="pct pct-check"></i>
		</div>
	</div>
	<div class="container-sucesso">
		<div class="text-box">Leitura estabelecida com sucesso!</div>
	</div>
	<div class="container-text">
		<div class="text-box">
			<pacto-cat-button
				(click)="dismiss()"
				[label]="'Concluir'"></pacto-cat-button>
		</div>
	</div>
</div>

<div *ngIf="falhaLeitura" class="falhaLeitura" id="falhaLeitura">
	<div class="container">
		<div class="box">
			<i class="pct pct-x"></i>
		</div>
	</div>
	<div class="container-sucesso">
		<div class="text-box">Falha na leitura. Repita o procedimento.</div>
	</div>
	<div class="container-text">
		<div class="text-box">
			<pacto-cat-button
				(click)="dismiss()"
				[label]="'cancelar leitura'"
				[type]="'OUTLINE'"
				class="cancelarLeitura"></pacto-cat-button>
			<pacto-cat-button
				(click)="tentarLigarBalanca()"
				[label]="'repetir leitura'"></pacto-cat-button>
		</div>
	</div>
</div>

<span #erroLigarBalanca [hidden]="true">
	Não foi possível ligar a balança, recomenda-se tentar novamente em torno de 60
	segundos.
</span>
<span #erroComunicarBalanca [hidden]="true">
	Não foi possível comunicar com a balança, recomenda-se tentar novamente em
	torno de 60 segundos.
</span>
<span #checkBoxErro [hidden]="true">
	Selecione se leu as recomendações para continuar.
</span>
