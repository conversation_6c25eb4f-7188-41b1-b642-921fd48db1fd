import {
	Component,
	OnInit,
	ViewChild,
	Input,
	Output,
	EventEmitter,
} from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiAvaliacaoFisicaService } from "treino-api";
import { FormGroup, FormControl } from "@angular/forms";
import { map, retryWhen, catchError } from "rxjs/operators";
import { genericRetryStrategy } from "@avaliacao-fisica-core/avaliacao/rxjs-retry";
import { of } from "rxjs";

declare var $;

@Component({
	selector: "pacto-modal-balanca",
	templateUrl: "./modal-balanca.component.html",
	styleUrls: ["./modal-balanca.component.scss"],
})
export class ModalBalancaComponent implements OnInit {
	fg: FormGroup = new FormGroup({
		checked: new FormControl(),
	});
	alunoId: number;
	idade: number;
	sexo: number;
	altura: number;
	atleta: string;
	nivelAtividade: string;
	loading: boolean;
	sucesso: boolean;
	falhaLeitura: boolean;
	@Input() avaliacaoFormGroup: FormGroup;
	@Input() pesoFormGroup: FormGroup;
	@ViewChild("checkBoxErro", { static: true }) checkBoxErro;
	@ViewChild("erroLigarBalanca", { static: true }) ligarBalacaErro;
	@ViewChild("erroComunicarBalanca", { static: true }) erroComunicarBalanca;
	URL_LIGAR_BALANCA =
		"http://localhost:1717/ControladorBalanca/ControleBalanca.LigarBalanca";
	URL_PEGAR_RETORNO =
		"http://localhost:1717/ControladorBalanca/ControleBalanca.PegarRetornoBalanca";
	aguarda60sec: boolean;
	ONE_MIN = 60 * 1000;
	delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal,
		private avaliacaoService: TreinoApiAvaliacaoFisicaService
	) {}

	ngOnInit() {}

	dismiss() {
		this.loading = false;
		this.sucesso = false;
		this.falhaLeitura = false;
		this.openModal.dismiss();
	}

	ligarBalanca() {
		if (this.fg.valid) {
			const dto = this.fg.getRawValue();
			if (dto.checked) {
				this.enviarInfoBalanca(
					this.alunoId,
					this.idade,
					this.sexo,
					this.altura,
					this.atleta,
					this.nivelAtividade
				);
			} else {
				const checkBoxErro = this.checkBoxErro.nativeElement.innerHTML;
				this.snotifyService.error(checkBoxErro);
			}
		}
	}

	getAltura(altura: any) {
		altura = altura.replace(",", "");
		altura = altura.replace(".", "");

		// tslint:disable-next-line: radix
		return parseInt(altura);
	}

	tentarLigarBalanca(
		id: any,
		idade: any,
		sexo: number,
		altura: number,
		atleta: any,
		nivelAtividade: any
	) {
		this.loading = true;
		this.falhaLeitura = false;
		this.sucesso = false;

		const json = {
			UserProfile: {
				Id: id,
				Idade: idade,
				Sexo: sexo,
				Altura: altura,
				Atleta: atleta,
				NivelAtividade: nivelAtividade,
			},
		};
		if (this.aguarda60sec) {
			this.loading = false;
			this.sucesso = false;
			this.falhaLeitura = true;
			const erroComunicarBalanca =
				this.erroComunicarBalanca.nativeElement.innerHTML;
			this.snotifyService.error(erroComunicarBalanca);
		} else {
			this.timer60Sec();
			localStorage.setItem(
				"tempoModalBalanca",
				(new Date().getTime() + this.ONE_MIN).toString()
			);
			this.avaliacaoService
				.ligarBalanca(json, this.URL_LIGAR_BALANCA)
				.subscribe(
					(resposta) => {
						if (resposta["Result"]) {
							setTimeout(() => {
								this.avaliacaoService
									.salvarInformacao(this.alunoId, this.URL_PEGAR_RETORNO)
									.pipe(
										retryWhen(genericRetryStrategy()),
										catchError((error) => of(error))
									)
									.subscribe((retorno) => {
										const jsonRetorno = retorno["Retorno"];
										if (jsonRetorno) {
											this.loading = false;
											this.falhaLeitura = false;
											this.sucesso = true;
											this.pesoFormGroup.parent
												.get("peso")
												.setValue(
													jsonRetorno["Peso"].toString().replace(".", ",")
												);
											this.avaliacaoFormGroup
												.get("logBalanca")
												.setValue(JSON.stringify(jsonRetorno));
											this.avaliacaoFormGroup
												.get("bioimpedancia.imc")
												.setValue(jsonRetorno["IMC"]);
											this.avaliacaoFormGroup
												.get("bioimpedancia.massaMagra")
												.setValue(jsonRetorno["PesoNaoGordura"]);
											this.avaliacaoFormGroup
												.get("bioimpedancia.massaGorda")
												.setValue(jsonRetorno["PesoGordura"]);
											this.avaliacaoFormGroup
												.get("bioimpedancia.percentMassaGorda")
												.setValue(jsonRetorno["PercentagemGordura"] * 100);
											this.avaliacaoFormGroup
												.get("bioimpedancia.ossos")
												.setValue(jsonRetorno["PesoOssos"]);
											this.avaliacaoFormGroup
												.get("bioimpedancia.percentAgua")
												.setValue(jsonRetorno["PercentagemAgua"] * 100);
											this.avaliacaoFormGroup
												.get("bioimpedancia.musculos")
												.setValue(jsonRetorno["PesoMusculos"]);
											this.avaliacaoFormGroup
												.get("bioimpedancia.idadeMetabolica")
												.setValue(jsonRetorno["IdadeMetabolica"]);
											this.avaliacaoFormGroup
												.get("bioimpedancia.gorduraVisceral")
												.setValue(jsonRetorno["GorduraVisceral"]);
											if (this.sexo === 2) {
												this.avaliacaoFormGroup
													.get("bioimpedancia.residuos")
													.setValue(jsonRetorno["Peso"] * 0.24);
											} else {
												this.avaliacaoFormGroup
													.get("bioimpedancia.residuos")
													.setValue(jsonRetorno["Peso"] * 0.21);
											}
											this.avaliacaoFormGroup
												.get("bioimpedancia.percentMassaMagra")
												.setValue(
													(jsonRetorno["PesoNaoGordura"] * 100) /
														jsonRetorno["Peso"]
												);
											this.dismiss();
										} else {
											this.loading = false;
											this.sucesso = false;
											this.falhaLeitura = true;
											const erroComunicarBalanca =
												this.erroComunicarBalanca.nativeElement.innerHTML;
											this.snotifyService.error(erroComunicarBalanca);
										}
									});
							}, 15000);
						} else {
							this.loading = false;
							this.sucesso = false;
							this.falhaLeitura = true;
							const ligarBalacaErro =
								this.ligarBalacaErro.nativeElement.innerHTML;
							this.snotifyService.error(ligarBalacaErro);
						}
						console.log(resposta);
					},
					(error) => {
						this.loading = false;
						this.sucesso = false;
						this.falhaLeitura = true;
						const ligarBalacaErro =
							this.ligarBalacaErro.nativeElement.innerHTML;
						this.snotifyService.error(ligarBalacaErro);
					}
				);
		}
		return true;
	}

	validarCamposObrigatorios(idade, sexo, atleta, nivelAtividade) {
		return true;
	}

	enviarInfoBalanca(id, idade, sexo, altura, atleta, nivelAtividade) {
		this.tentarLigarBalanca(id, idade, sexo, altura, atleta, nivelAtividade);
	}

	timer60Sec = async () => {
		this.aguarda60sec = true;
		await this.delay(60000);
		this.aguarda60sec = false;
	};
}
