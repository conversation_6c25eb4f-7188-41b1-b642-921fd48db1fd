@import "~src/assets/scss/pacto/plataforma-import.scss";

.modal-bio-content {
	padding: 0 100px 30px 100px;

	.descricao {
		border: 1px solid #d1d4dc;
		border-radius: 4px;

		.content {
			padding: 25px 60px 10px 60px;
			font-family: "Nunito Sans", sans-serif;
			font-style: normal;
			font-weight: normal;
			font-size: 16px;
			color: #b4b7bb;
		}

		.content p {
			margin-bottom: 0.5em;
		}
	}
}

.modal-bio-button {
	.botao-ligar {
		padding: 20px 0 0 40%;
	}

	.check-box {
		padding-top: 20px;
	}
}

.container {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;

	.pct-loader {
		color: $azulimPri;
	}

	.pct-check {
		color: $chuchuzinhoPri;
	}

	.pct-x {
		color: $hellboyPri;
	}

	.pct {
		font-size: 100px;
	}
}

.container-text {
	padding-bottom: 75px;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;

	.text-box {
		font-size: 20px;
	}
}

.container-sucesso {
	padding-bottom: 35px;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;

	.text-box {
		font-size: 20px;
	}
}

.cancelarLeitura {
	padding-right: 30px;
}

.titulo {
	padding: 20px 0 20px 0;
	font-weight: 600;
	font-family: "Nunito Sans", sans-serif;
	font-size: 20px;
}

.loading {
	padding: 87.5px 0 87.01px 0;

	div.spinner {
		position: relative;
		width: 120px;
		height: 150px;
		display: inline-block;
		border-radius: 10px;
		margin: 0;
	}

	div.spinner div {
		width: 6%;
		height: 16%;
		background: $azulimPri;
		position: absolute;
		left: 49%;
		top: 43%;
		opacity: 0;
		border-radius: 50px;
		box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
		animation: fade 1s linear infinite;
	}

	@keyframes fade {
		from {
			opacity: 1;
		}
		to {
			opacity: 0.25;
		}
	}

	div.spinner div.bar1 {
		transform: rotate(0deg) translate(0, -130%);
		animation-delay: 0s;
	}

	div.spinner div.bar2 {
		transform: rotate(30deg) translate(0, -130%);
		animation-delay: -0.9167s;
	}

	div.spinner div.bar3 {
		transform: rotate(60deg) translate(0, -130%);
		animation-delay: -0.833s;
	}

	div.spinner div.bar4 {
		transform: rotate(90deg) translate(0, -130%);
		animation-delay: -0.7497s;
	}

	div.spinner div.bar5 {
		transform: rotate(120deg) translate(0, -130%);
		animation-delay: -0.667s;
	}

	div.spinner div.bar6 {
		transform: rotate(150deg) translate(0, -130%);
		animation-delay: -0.5837s;
	}

	div.spinner div.bar7 {
		transform: rotate(180deg) translate(0, -130%);
		animation-delay: -0.5s;
	}

	div.spinner div.bar8 {
		transform: rotate(210deg) translate(0, -130%);
		animation-delay: -0.4167s;
	}

	div.spinner div.bar9 {
		transform: rotate(240deg) translate(0, -130%);
		animation-delay: -0.333s;
	}

	div.spinner div.bar10 {
		transform: rotate(270deg) translate(0, -130%);
		animation-delay: -0.2497s;
	}

	div.spinner div.bar11 {
		transform: rotate(300deg) translate(0, -130%);
		animation-delay: -0.167s;
	}

	div.spinner div.bar12 {
		transform: rotate(330deg) translate(0, -130%);
		animation-delay: -0.0833s;
	}
}

.sucesso {
	padding: 80px 0 80.34px 0;
}

.falhaLeitura {
	padding: 80px 0 80.34px 0;
}
