import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AvaliacaoFisicaRoutingModule } from "./avaliacao-fisica.routing";
import { AvaliacaoFisicaCoreModule } from "@avaliacao-fisica-core/avaliacao-fisica-core.module";

import { AvaliacaoInputNumberComponent } from "./avaliacoes-aluno/avaliacao-input-number/avaliacao-input-number.component";
import { AvaliacaoFisicaModalComponent } from "./avaliacoes-aluno/avaliacao-fisica-modal/avaliacao-fisica-modal.component";
import { AvaliacaoFisicaHistoricoModalComponent } from "./avaliacoes-aluno/avaliacao-fisica-historico-modal/avaliacao-fisica-historico-modal.component";
import { AvaliacoesAlunoComponent } from "./avaliacoes-aluno/avaliacoes-aluno.component";
import { AvaliacoesAlunoTopComponent } from "./avaliacoes-aluno/avaliacoes-aluno-top/avaliacoes-aluno-top.component";
import { AvaliacaoEvolucaov2Component } from "./avaliacao-evolucao/components/avaliacao-evolucaov2.component";
import { TreinoBiStateService } from "../treino/treino-bi/components/treino-bi-home-v2/treino-bi-state.service";
import { NgbPopoverModule } from "@ng-bootstrap/ng-bootstrap";
import { AvaliacaoResultadoModalComponent } from "./avaliacoes-aluno/avaliacao-resultado-modal/avaliacao-resultado-modal.component";
import { CardIndiceComponent } from "./avaliacao-evolucao/components/card-indice/card-indice.component";
import { ReferenciaImcComponent } from "../base/alunos/perfil-aluno/components/avaliacoes/referencia-imc/referencia-imc.component";
import { AlunosModule } from "../base/alunos/alunos.module";
import { AvaliacaoEditorDobrasComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-dobras/avaliacao-editor-dobras.component";
import { AvaliacaoEditorComposicaoComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-composicao/avaliacao-editor-composicao.component";
import { AvaliacaoEditorFlexibilidadeComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-flexibilidade/avaliacao-editor-flexibilidade.component";
import { AvaliacaoEditorRmlComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-rml/avaliacao-editor-rml.component";
import { AvaliacaoEditorVo2maxComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-vo2max/avaliacao-editor-vo2max.component";
import { AvaliacaoEditorSomopatiaComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-somopatia/avaliacao-editor-somopatia.component";
import { AvaliacaoEditorMetaRecomComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-meta-recom/avaliacao-editor-meta-recom.component";
import { AvaliacaoEditorPosturaComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-postura/avaliacao-editor-postura.component";
import { AvaliacaoEditorPesoAlturaComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-peso-altura/avaliacao-editor-peso-altura.component";
import { AvaliacaoEditorPerimetriaComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-perimetria/avaliacao-editor-perimetria.component";
import { AvaliacaoInfoModalComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-info-modal/avaliacao-info-modal.component";
import { ModalBalancaComponent } from "./modal-balanca/modal-balanca.component";
import { AvaliacaoIntegradaComponent } from "./avaliacao-integrada/avaliacao-integrada.component";
import { HomeComponent } from "./home/<USER>";
import { HomePageModule } from "pacto-layout";
import { AvaliacaoEditorImportacaoBiosannyComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-importacao-biosanny/avaliacao-editor-importacao-biosanny.component";
import { ModalValidacaoDadosImportacaoBiosannyComponent } from "./avaliacoes-aluno/avaliacao-partes/avaliacao-editor-importacao-biosanny/components/modal-validacao-dados-importacao-biosanny/modal-validacao-dados-importacao-biosanny.component";

@NgModule({
	imports: [
		AvaliacaoFisicaRoutingModule,
		CommonModule,
		// AnamneseModule,
		BaseSharedModule,
		AvaliacaoFisicaCoreModule,
		NgbPopoverModule,
		AlunosModule,
		HomePageModule,
	],
	entryComponents: [
		AvaliacaoFisicaModalComponent,
		AvaliacaoFisicaHistoricoModalComponent,
		AvaliacaoResultadoModalComponent,
		ReferenciaImcComponent,
		AvaliacaoInfoModalComponent,
		ModalBalancaComponent,
		ModalValidacaoDadosImportacaoBiosannyComponent,
	],
	declarations: [
		AvaliacaoInputNumberComponent,
		AvaliacaoFisicaModalComponent,
		AvaliacaoFisicaHistoricoModalComponent,
		AvaliacaoFisicaHistoricoModalComponent,
		AvaliacoesAlunoComponent,
		AvaliacoesAlunoTopComponent,
		AvaliacaoEvolucaov2Component,
		AvaliacaoResultadoModalComponent,
		CardIndiceComponent,
		AvaliacaoEditorPesoAlturaComponent,
		AvaliacaoEditorPerimetriaComponent,
		AvaliacaoEditorDobrasComponent,
		AvaliacaoEditorComposicaoComponent,
		AvaliacaoEditorFlexibilidadeComponent,
		AvaliacaoEditorRmlComponent,
		AvaliacaoEditorVo2maxComponent,
		AvaliacaoEditorSomopatiaComponent,
		AvaliacaoEditorMetaRecomComponent,
		AvaliacaoEditorImportacaoBiosannyComponent,
		ModalValidacaoDadosImportacaoBiosannyComponent,
		AvaliacaoEditorPosturaComponent,
		AvaliacaoInputNumberComponent,
		AvaliacaoFisicaModalComponent,
		AvaliacaoFisicaHistoricoModalComponent,
		AvaliacaoFisicaHistoricoModalComponent,
		AvaliacoesAlunoComponent,
		AvaliacoesAlunoTopComponent,
		AvaliacaoEvolucaov2Component,
		CardIndiceComponent,
		AvaliacaoInfoModalComponent,
		ModalBalancaComponent,
		AvaliacaoIntegradaComponent,
		HomeComponent,
	],
	providers: [TreinoBiStateService],
})
export class AvaliacaoFisicaModule {}
