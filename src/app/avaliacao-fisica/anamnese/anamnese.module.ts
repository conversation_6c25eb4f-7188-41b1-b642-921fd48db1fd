import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

import { AnamneseListaComponent } from "./components/anamnese-lista/anamnese-lista.component";
import { AnamnesePerguntaWrapperComponent } from "./components/anamnese-pergunta-wrapper/anamnese-pergunta-wrapper.component";
import { AnamnesePerguntaEscolhaComponent } from "./components/anamnese-pergunta-escolhas/anamnese-pergunta-escolhas.component";
import { AnamneseEditorComponent } from "./components/anamnese-editor/anamnese-editor.component";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { Routes, RouterModule } from "@angular/router";
import { AnamneseEditorGuard } from "./anamnese-editor.guard";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { ModuleName } from "@base-core/modulo/modulo.model";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { CatTolltipModule } from "ui-kit";

const recurso = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.ANAMNESE, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: AnamneseListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		canDeactivate: [AnamneseEditorGuard],
		component: AnamneseEditorComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [
		NgbModule,
		CommonModule,
		RouterModule.forChild(routes),
		BaseSharedModule,
		DragDropModule,
		CatTolltipModule,
	],
	declarations: [
		AnamneseListaComponent,
		AnamneseEditorComponent,
		AnamnesePerguntaEscolhaComponent,
		AnamnesePerguntaWrapperComponent,
	],
	providers: [{ provide: AnamneseEditorGuard, useClass: AnamneseEditorGuard }],
})
export class AnamneseModule {}
