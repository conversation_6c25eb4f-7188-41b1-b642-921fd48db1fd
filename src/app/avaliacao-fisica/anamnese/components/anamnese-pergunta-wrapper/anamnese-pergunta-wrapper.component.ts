import {
	Component,
	OnInit,
	Input,
	Output,
	EventEmitter,
	ViewEncapsulation,
} from "@angular/core";

import { AnamneseTipoPergunta } from "treino-api";
import { FormGroup, FormArray } from "@angular/forms";

@Component({
	selector: "pacto-anamnese-pergunta-wrapper",
	templateUrl: "./anamnese-pergunta-wrapper.component.html",
	styleUrls: ["./anamnese-pergunta-wrapper.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class AnamnesePerguntaWrapperComponent implements OnInit {
	@Input() selected = false;
	@Input() emUso = true;
	@Input() group: FormGroup;
	@Input() indexPergunta: number;

	@Output() clicked: EventEmitter<boolean> = new EventEmitter();
	@Output() removerPergunta: EventEmitter<any> = new EventEmitter();
	@Output() adicionarPergunta: EventEmitter<any> = new EventEmitter();

	constructor() {}

	ngOnInit() {
		if (!this.canChange) {
			this.group.get("tipo").disable();
		}
	}

	get canChange() {
		return !this.emUso || (this.emUso && !this.group.get("id").value);
	}

	adicionarPerguntaHandler() {
		this.adicionarPergunta.emit();
	}

	get opcoesFormArray(): FormArray {
		return this.group.get("opcoes") as FormArray;
	}

	get AnamneseTipoPergunta() {
		return AnamneseTipoPergunta;
	}
}
