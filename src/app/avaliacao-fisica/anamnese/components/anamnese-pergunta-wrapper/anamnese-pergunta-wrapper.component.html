<div
	(click)="clicked.emit(true)"
	[ngClass]="{ selected: selected }"
	class="anamnese-pergunta-wrapper">
	<div class="ribbon"></div>

	<div class="title-row">
		<pacto-inline-text
			[control]="group.get('pergunta')"
			[id]="'descricao-pergunta-' + indexPergunta"
			[nome]="'titulo'"
			class="pergunta"
			i18n-placeholder="@@crud-anamnese:pergunta:descricao-pergunta:placeholder"
			placeholder="Descrição"></pacto-inline-text>

		<select
			*ngIf="selected"
			[formControl]="group.get('tipo')"
			class="form-control form-control-sm tipo"
			id="tipo-pergunta-select-{{ indexPergunta }}">
			<option
				i18n="@@crud-anamnese:pergunta:tipo-pergunta:multipla"
				value="{{ AnamneseTipoPergunta.ESCOLHA_MULTIPLA }}">
				Multipla escolha
			</option>
			<option
				i18n="@@crud-anamnese:pergunta:tipo-pergunta:unica"
				value="{{ AnamneseTipoPergunta.ESCOLHA_UNICA }}">
				Escolha única
			</option>
			<option
				i18n="@@crud-anamnese:pergunta:tipo-pergunta:sim-nao"
				value="{{ AnamneseTipoPergunta.SIM_NAO }}">
				Sim/Não
			</option>
			<option
				i18n="@@crud-anamnese:pergunta:tipo-pergunta:texto"
				value="{{ AnamneseTipoPergunta.TEXTO }}">
				Texto
			</option>
		</select>
	</div>

	<div [ngSwitch]="group.get('tipo').value" class="pergunta-corpo">
		<pacto-anamnese-pergunta-escolhas
			#escolhasComponent
			*ngSwitchCase="AnamneseTipoPergunta.ESCOLHA_MULTIPLA"
			[canChange]="canChange"
			[indexPergunta]="indexPergunta"
			[multipla]="true"
			[opcoesFormArray]="opcoesFormArray"
			[selected]="selected"></pacto-anamnese-pergunta-escolhas>

		<pacto-anamnese-pergunta-escolhas
			#escolhasComponent
			*ngSwitchCase="AnamneseTipoPergunta.ESCOLHA_UNICA"
			[canChange]="canChange"
			[indexPergunta]="indexPergunta"
			[multipla]="false"
			[opcoesFormArray]="opcoesFormArray"
			[selected]="selected"></pacto-anamnese-pergunta-escolhas>

		<div *ngSwitchCase="AnamneseTipoPergunta.TEXTO" class="text-body">
			<textarea class="form-control" disabled rows="1"></textarea>
		</div>

		<div *ngSwitchCase="AnamneseTipoPergunta.SIM_NAO" class="sim-nao-body">
			<div class="option">
				<input disabled type="radio" />
				<label i18n="@@crud-anamnese:pergunta:sim">Sim</label>
			</div>
			<div class="option">
				<input disabled type="radio" />
				<label i18n="@@crud-anamnese:pergunta:nao">Não</label>
			</div>
		</div>
	</div>

	<div *ngIf="selected" class="pergunta-rodape">
		<i
			(click)="removerPergunta.emit(true)"
			*ngIf="canChange"
			class="fa fa-trash-o"></i>
		<i
			(click)="adicionarPerguntaHandler()"
			class="fa fa-plus-square-o"
			id="icon-add-pergunta-{{ indexPergunta }}"></i>
	</div>
</div>
