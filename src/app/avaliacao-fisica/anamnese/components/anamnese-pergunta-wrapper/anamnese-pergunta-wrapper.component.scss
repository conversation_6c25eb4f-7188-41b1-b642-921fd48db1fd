@import "src/assets/scss/variable.scss";
@import "~src/assets/scss/pacto/plataforma-import.scss";

.anamnese-pergunta-wrapper {
	background-color: #ffffff;
	position: relative;
	margin: 0px;
	padding: 20px 20px 10px 20px;
	border-top: 1px dashed #999;
	cursor: pointer;

	&.selected {
		border-top: 0px;
		border-bottom: 0px;
		position: relative;
		z-index: 2;
		box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.26);

		.ribbon {
			display: block;
		}
	}

	.ribbon {
		position: absolute;
		display: none;
		left: 0px;
		top: 0px;
		width: 5px;
		height: 100%;
		background-color: $pacto-treino-primary;
	}

	.title-row {
		display: flex;

		.pergunta {
			flex-grow: 1;

			input {
				font-size: 15px !important;
			}
		}

		pacto-inline-text .form-group {
			margin-bottom: 10px;
		}

		.tipo {
			max-width: 250px;
		}
	}

	.sim-nao-body {
		.option {
			margin-bottom: 5px;
		}

		label {
			font-weight: 600;
			font-size: 14px;
			margin-left: 9px;
			margin-bottom: 0px;
			color: #333333;
		}

		input[type="radio"] {
			position: relative;
			top: 2px;
		}
	}

	.pergunta-rodape {
		height: 40px;
		display: flex;
		flex-direction: row-reverse;

		i {
			cursor: pointer;
			font-size: 20px;
			margin: 10px 10px 0px;
		}
	}

	.pct-drag {
		cursor: move;
		font-size: 32px;
		line-height: 2;
	}

	.drag-handler {
		display: flex;
	}

	.anamnese-pergunta-wrapper:hover {
		border-left: solid 5px $azulimPri;
		background-color: $cinza01;
	}
}
