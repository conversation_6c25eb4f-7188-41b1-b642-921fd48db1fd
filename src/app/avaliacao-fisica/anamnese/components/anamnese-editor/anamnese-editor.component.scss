@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";

.title-editor-block {
	display: flex;

	.fa {
		position: relative;
		top: 10px;
	}
}

.limited-change-msg {
	font-weight: 600;
	padding: 0px 20px 5px 20px;
	color: primaryColor(pacto, base);
}

.header-card {
	background-color: #ffffff;
	margin-bottom: 10px;

	.upper-part {
		padding: 20px 20px 20px 20px;

		pacto-inline-text {
			.form-group {
				margin-bottom: 0px;
				margin-left: 0px !important;
			}

			input {
				font-size: 22px !important;
			}
		}
	}

	.pacto-card-actions {
		display: flex;
		// border-bottom: 1px solid #ececec;
		padding: 10px 20px 10px 20px;
		flex-direction: row-reverse;

		.ativo-check {
			margin-bottom: 10px;
			margin-top: 15px;
			flex-grow: 1;
		}

		.btns-wrapper {
			button {
				margin-left: 10px;
			}
		}
	}

	.saving {
		img {
			width: 30px;
		}
	}
}

.draggable-list-wrapper {
	padding-bottom: 100px;
}

.drop-zone {
	height: 80px;
	margin-bottom: 10px;
	background-color: aqua;
	border: 1px dashed 888;
	position: absolute;
	bottom: 0px;
}

.draggable-wrapper-reference {
	position: relative;
}

.fa.fa-plus {
	margin-right: 10px;
}
