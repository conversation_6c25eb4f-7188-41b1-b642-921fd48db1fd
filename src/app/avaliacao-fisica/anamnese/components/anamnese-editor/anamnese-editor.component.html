<pacto-cat-layout-v2>
	<div class="anamnese-editor-wrapper">
		<div
			(click)="perguntaSelecionadaIndex = null"
			class="header-card pacto-shadow">
			<div class="upper-part">
				<div class="row">
					<div class="col-md-6">
						<div class="title-editor-block">
							<pacto-inline-text
								[control]="formGroup.get('nome')"
								[id]="'nome-anamnese-input'"
								[nome]="'nome'"
								i18n-placeholder="
									@@crud-anamnese:editor:nome-anamnese:placeholder"
								placeholder="Nome da Anamnese"></pacto-inline-text>
							<i class="fa fa-pencil"></i>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-12">
					<div
						*ngIf="anamnese && anamnese.emUso"
						class="limited-change-msg"
						i18n="@@crud-anamnese:editor:anamnese-em-uso:label">
						Essa anamnese já foi utilizada em alguma avaliação fisica e portanto
						algumas ações de edição não são permitidas (por exemplo, remover
						perguntas e opções).
					</div>
				</div>
			</div>

			<div class="pacto-card-actions">
				<div class="btns-wrapper">
					<button
						(click)="adicionarPerguntaHandler()"
						class="btn btn-secondary"
						id="btn-add-pergunta">
						<i class="fa fa-plus"></i>
						<span i18n="@@crud-anamnese:editor:adicionar-pergunta">
							Adicionar Pergunta
						</span>
					</button>
					<pacto-log *ngIf="edit" [url]="urlLog"></pacto-log>
					<button
						(click)="cancelAnamneseHandler()"
						class="btn btn-secondary"
						i18n="@@buttons:cancelar">
						Cancelar
					</button>
					<button
						(click)="salvarHandler()"
						class="btn btn-primary"
						id="btn-add-anamnese">
						<span
							*ngIf="anamnese && anamnese.id"
							i18n="@@crud-anamnese:editor:salvar-anamnese">
							Salvar Anamnese
						</span>
						<span
							*ngIf="!anamnese || !anamnese.id"
							i18n="@@crud-anamnese:editor:criar-anamnese">
							Criar Anamnese
						</span>
					</button>
				</div>
				<div class="form-group form-check ativo-check">
					<input
						[formControl]="formGroup.get('ativa')"
						class="form-check-input"
						id="ativo-checkbox"
						type="checkbox" />
					<label
						class="form-check-label"
						for="ativo-checkbox"
						i18n="@@crud-anamnese:editor:ativar-anamnese">
						Ativo (quando inativas não podem ser utilizadas.)
					</label>
				</div>
			</div>
		</div>

		<div (cdkDropListDropped)="drop($event)" cdkDropList>
			<pacto-anamnese-pergunta-wrapper
				(adicionarPergunta)="adicionarPerguntaHandler(index + 1)"
				(clicked)="perguntaSelecionadaIndex = index"
				(removerPergunta)="removerPerguntaHandler(index)"
				*ngFor="let pergunta of perguntasControl.controls; let index = index"
				[emUso]="anamnese && anamnese.emUso"
				[group]="perguntasControl.at(index)"
				[indexPergunta]="index"
				[selected]="index === perguntaSelecionadaIndex"
				cdkDrag></pacto-anamnese-pergunta-wrapper>
		</div>
	</div>

	<span
		#errorNomeEmpty
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-deve-possuir-nome">
		A anamnese deve possuir um nome.
	</span>
	<span
		#success
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-foi-criada">
		A anamnese foi criada com sucesso.
	</span>
	<span
		#successEdit
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-foi-editada">
		A anamnese foi editada com sucesso.
	</span>
	<span
		#successAtivada
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-ativada">
		Anamnese ativada com sucesso.
	</span>
	<span
		#successDesativada
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-ativada">
		Anamnese desativada com sucesso.
	</span>
	<span
		#errorNomeUsed
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-duplicada">
		Esse nome já é utilizado por outra anamnese.
	</span>
	<span
		#maxCaracteresPerg
		[hidden]="true"
		i18n="@@crud-anamnese:editor:anamnese-max-characters">
		Quantidade de caracteres para pergunta excedido. Máximo permitido: 255.
	</span>
	<span #errorDescricaoPerguntaEmpty [hidden]="true">
		Todas perguntas devem ter uma descrição.
	</span>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span xingling="erro-avaliacao-integrada">
		Não é possivel editar uma anamnese do tipo integrada através do treino novo.
	</span>
</pacto-traducoes-xingling>
