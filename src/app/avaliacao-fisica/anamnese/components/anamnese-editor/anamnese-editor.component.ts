import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import {
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import {
	Anamnese,
	AnamneseTipoPergunta,
	TreinoApiAnamneseService,
} from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";

declare var window;

@Component({
	selector: "pacto-anamnese-editor",
	templateUrl: "./anamnese-editor.component.html",
	styleUrls: ["./anamnese-editor.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class AnamneseEditorComponent implements OnInit {
	@ViewChild("pergunta", { static: false }) pergunta;
	@ViewChild("errorNomeEmpty", { static: true }) errorNomeEmpty;
	@ViewChild("errorNomeUsed", { static: true }) errorNomeUsed;
	@ViewChild("maxCaracteresPerg", { static: true }) maxCaracteresPerg;
	@ViewChild("success", { static: true }) success;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("successEdit", { static: true }) successEdit;
	@ViewChild("errorDescricaoPerguntaEmpty", { static: true })
	errorDescricaoPerguntaEmpty;
	@ViewChild("successAtivada", { static: true }) successAtivada;
	@ViewChild("successDesativada", { static: true }) successDesativada;

	anamnese: Anamnese;
	perguntaSelecionadaIndex: number = null;
	ativaChanged: boolean;
	otherField: boolean;

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl(),
		ativa: new FormControl(true),
		perguntas: new FormArray([]),
		tipo: new FormControl(),
	});

	ativaCheckboxValue = this.formGroup.get("ativa");

	get perguntasControl(): FormArray {
		return this.formGroup.get("perguntas") as FormArray;
	}

	constructor(
		private anamneseService: TreinoApiAnamneseService,
		private snotifyService: SnotifyService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private rest: RestService,
		private sessionService: SessionService
	) {}

	get edit() {
		return this.anamnese && this.anamnese.id;
	}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			if (params.id === "novo") {
				this.setupCreate();
			} else {
				this.setupEdit(params);
			}
		});
		this.formGroup.get("ativa").valueChanges.subscribe((value) => {
			this.ativaCheckboxValue = value;
			this.ativaChanged = true;
		});
		this.formGroup.get("nome").valueChanges.subscribe((value) => {
			this.otherField = true;
		});
		this.formGroup.get("tipo").valueChanges.subscribe((value) => {
			this.otherField = true;
		});
	}

	salvarHandler() {
		if (!this.sessionService.isMormaii) {
			this.formGroup.removeControl("tipo");
		}
		let dto = this.formGroup.getRawValue();
		const errorEmpty = this.errorNomeEmpty.nativeElement.innerHTML;
		const errorNameUsed = this.errorNomeUsed.nativeElement.innerHTML;
		const maxCaracteresPerg = this.maxCaracteresPerg.nativeElement.innerHTML;
		const success = this.success.nativeElement.innerHTML;
		const successEdit = this.successEdit.nativeElement.innerHTML;
		const successAtivada = this.successAtivada.nativeElement.innerHTML;
		const successDesativada = this.successDesativada.nativeElement.innerHTML;
		const errorDescricaoEmpty =
			this.errorDescricaoPerguntaEmpty.nativeElement.innerHTML;

		dto = this.sanitizeDTO(dto);

		if (dto === true) {
			this.snotifyService.error(errorDescricaoEmpty);
			return false;
		}

		if (!dto.nome) {
			this.snotifyService.error(errorEmpty);
			return false;
		}

		let prosseguir = true;
		dto.perguntas.forEach((p) => {
			if (p.pergunta.length > 255) {
				prosseguir = false;
				this.snotifyService.error(maxCaracteresPerg);
			}
		});

		if (prosseguir) {
			if (this.edit) {
				this.anamneseService.atualizarAnamnese(this.anamnese.id, dto).subscribe(
					(result) => {
						if (this.ativaChanged) {
							if (this.ativaCheckboxValue) {
								this.snotifyService.success(successAtivada);
							} else {
								this.snotifyService.success(successDesativada);
							}
						}
						if (this.otherField) {
							this.snotifyService.success(successEdit);
						}

						this.router.navigate(["avaliacao", "anamneses"]);
					},
					(httpError) => {
						this.snotifyService.error(errorNameUsed);
					}
				);
			} else {
				this.anamneseService.criarAnamnese(dto).subscribe((result) => {
					if (result === "validacao_anamnese_ja_existe") {
						this.snotifyService.error(errorNameUsed);
					} else {
						this.snotifyService.success(success);
						this.router.navigate(["avaliacao", "anamneses"]);
					}
				});
			}
		}
	}

	private sanitizeDTO(dto) {
		let questionEmpty = false;

		dto.perguntas.forEach((pergunta, index) => {
			if (!pergunta.pergunta) {
				questionEmpty = true;
			}
			pergunta.ordem = index;

			if (
				pergunta.tipo === AnamneseTipoPergunta.SIM_NAO ||
				pergunta.tipo === AnamneseTipoPergunta.TEXTO
			) {
				pergunta.opcoes = [];
			}
		});

		if (dto.nome) {
			dto.nome = (dto.nome as string).trim();
		} else {
			dto.nome = "";
		}

		if (questionEmpty) {
			return true;
		}
		return dto;
	}

	private setupCreate() {}

	private setupEdit(params) {
		this.anamneseService.obterAnamnese(params.id).subscribe((dados) => {
			this.anamnese = dados;
			this.formGroup
				.get("nome")
				.setValue(this.anamnese.nome, { emitEvent: false });
			this.formGroup
				.get("ativa")
				.setValue(this.anamnese.ativa, { emitEvent: false });
			this.formGroup
				.get("tipo")
				.setValue(this.anamnese.tipo, { emitEvent: false });
			this.anamnese.perguntas.forEach((pergunta) => {
				const perguntaFG = new FormGroup({
					id: new FormControl(pergunta.id),
					tipo: new FormControl(pergunta.tipo),
					pergunta: new FormControl(pergunta.pergunta),
					opcoes: new FormArray([]),
				});
				pergunta.opcoes.forEach((opcao) => {
					(perguntaFG.get("opcoes") as FormArray).push(
						new FormGroup({
							id: new FormControl(opcao.id),
							nome: new FormControl(opcao.nome),
						})
					);
				});
				(this.formGroup.get("perguntas") as FormArray).push(perguntaFG);
				this.cd.detectChanges();
			});
		});
	}

	cancelAnamneseHandler() {
		this.router.navigate(["avaliacao", "anamneses"]);
	}

	removerPerguntaHandler(index) {
		this.perguntasControl.removeAt(index);
	}

	adicionarPerguntaHandler(index = 0) {
		this.perguntasControl.insert(
			index,
			new FormGroup({
				id: new FormControl(),
				pergunta: new FormControl(),
				tipo: new FormControl(AnamneseTipoPergunta.TEXTO),
				opcoes: new FormArray([]),
			})
		);
		setTimeout(() => {
			this.perguntaSelecionadaIndex = index;
		});
	}

	get urlLog(): string {
		return this.rest.buildFullUrl(`log/anamneses/${this.anamnese.id}`);
	}

	drop(event: CdkDragDrop<string[]>) {
		moveItemInArray(
			this.perguntasControl.controls,
			event.previousIndex,
			event.currentIndex
		);
		this.persistirOrdem(event.previousIndex, event.currentIndex);
	}

	private persistirOrdem(previousIndex: number, currentIndex: number) {
		const perguntaForm = this.perguntasControl.at(currentIndex);
		const idDaPerguntaMovida = perguntaForm.get("id").value;

		this.anamnese.perguntas.forEach((pergunta, index) => {
			if (pergunta.id === idDaPerguntaMovida) {
				pergunta.ordem = currentIndex;
			} else {
				if (previousIndex < currentIndex) {
					if (
						pergunta.ordem > previousIndex &&
						pergunta.ordem <= currentIndex
					) {
						pergunta.ordem--;
					}
				} else {
					if (
						pergunta.ordem < previousIndex &&
						pergunta.ordem >= currentIndex
					) {
						pergunta.ordem++;
					}
				}
			}
		});

		this.anamnese.perguntas.sort((perg1, perg2) => perg1.ordem - perg2.ordem);
		this.cd.detectChanges();
	}
}
