<div class="wrapper">
	<div class="options-wrapper">
		<div
			*ngFor="let opcao of opcoesFormArray.controls; let index = index"
			class="option">
			<input
				class="wrapper-input"
				disabled
				type="{{ multipla ? 'checkbox' : 'radio' }}" />
			<pacto-inline-text
				(enter)="enterHandler(index)"
				[control]="opcao.get('nome')"
				[nome]="'nome'"
				i18n-placeholder="@@crud-anamnese:escolha-multipla:item-opcao"
				placeholder="Opção {{ index + 1 }}"></pacto-inline-text>
			<i
				(click)="removerOpcaoHandler(index)"
				*ngIf="selected && opcoesFormArray.controls.length > 1 && canChange"
				class="wrapper-remove fa fa-times"></i>
		</div>
	</div>

	<div *ngIf="selected" class="option add-option">
		<input disabled type="{{ multipla ? 'checkbox' : 'radio' }}" />
		<label
			(click)="addHandler()"
			class="add-link"
			i18n="@@crud-anamnese:escolha-multipla:adicionar-opcao"
			id="add-opcao-check-{{ indexPergunta }}">
			Adicionar opção
		</label>
	</div>
</div>

<span
	#label
	[hidden]="true"
	i18n="@@crud-anamnese:escolha-multipla:pergunta-opcao:label">
	Opção
</span>
