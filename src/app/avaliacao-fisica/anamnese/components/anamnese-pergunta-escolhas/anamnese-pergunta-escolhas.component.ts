import {
	Component,
	OnInit,
	Input,
	Output,
	EventEmitter,
	ViewChild,
	ElementRef,
	ViewEncapsulation,
	ViewChildren,
	QueryList,
} from "@angular/core";
import { FormControl, FormArray, FormGroup } from "@angular/forms";

import { AnamneseTipoPergunta } from "treino-api";
import { InlineTextComponent } from "old-ui-kit";

@Component({
	selector: "pacto-anamnese-pergunta-escolhas",
	templateUrl: "./anamnese-pergunta-escolhas.component.html",
	styleUrls: ["./anamnese-pergunta-escolhas.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class AnamnesePerguntaEscolhaComponent implements OnInit {
	@ViewChild("label", { static: true }) labelHtml: ElementRef<any>;
	@ViewChildren(InlineTextComponent)
	opcoesInline: QueryList<InlineTextComponent> = new QueryList();

	@Input() selected = false;
	@Input() multipla = false;
	@Input() canChange;
	@Input() indexPergunta: number;
	@Input() opcoesFormArray: FormArray = new FormArray([]);
	@Output() removerOpcao: EventEmitter<any> = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	removerOpcaoHandler(index) {
		this.opcoesFormArray.removeAt(index);
	}

	enterHandler(index) {
		if (this.opcoesFormArray.length - 1 === index) {
			this.addHandler();
			setTimeout(() => {
				this.opcoesInline.toArray()[index + 1].focus();
			});
		}
	}

	private getNewFormGroup(nome: string) {
		return new FormGroup({
			id: new FormControl(),
			nome: new FormControl(nome),
		});
	}

	addHandler() {
		const nextNumber = this.opcoesFormArray.length + 1;
		const label = `${this.labelHtml.nativeElement.textContent} ${nextNumber}`;
		this.opcoesFormArray.push(this.getNewFormGroup(label));
	}

	get AnamneseTipoPergunta() {
		return AnamneseTipoPergunta;
	}
}
