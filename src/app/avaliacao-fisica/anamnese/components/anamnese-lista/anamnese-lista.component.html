<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableInstance
			(btnClick)="btnClickHandler($event)"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="rowClickHandler($event)"
			[filterConfig]="filter"
			[sessionService]="session"
			[tableDescription]="tableDesc"
			[tableTitle]="tableTitle"
			[table]="table"
			telaId="anamneses"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #addLabel>
	<span i18n="@@crud-anamnese:adicionar-label">Adicionar</span>
</ng-template>
<ng-template #nomeColumnName>
	<span i18n="@@crud-anamnese:nome:column">Nome</span>
</ng-template>
<ng-template #statusColumnName>
	<span i18n="@@crud-anamnese:status:column">Status</span>
</ng-template>
<ng-template #dataLancamentoColumnName>
	<span i18n="@@crud-anamnese:data-lancamento:column">Data de criação</span>
</ng-template>
<ng-template #dataLancamentoCelula let-item="item">
	<span
		*ngIf="item.datalancamento.length == 0"
		[ds3Tooltip]="tooltipInfoData"
		[tooltipPosition]="'top'"
		class="margin-left-20px">
		<a color="primary" ds3-icon-button size="lg">
			<i class="pct pct-info"></i>
		</a>
	</span>
	<span *ngIf="item.datalancamento.length > 0">{{ item.datalancamento }}</span>
</ng-template>
<ng-template #statusCell let-item="item">
	<span
		*ngIf="item.ativa"
		class="status-cell ativo"
		i18n="@@crud-anamnese:situacao-ativo">
		Ativo
	</span>
	<span
		*ngIf="!item.ativa"
		class="status-cell"
		i18n="@@crud-anamnese:situacao-inativo">
		Inativo
	</span>
</ng-template>

<ng-template #tableTitle>
	<span i18n="@@crud-anamnese:anamneses:title">Anamneses</span>
</ng-template>
<ng-template #tableDesc>
	<span i18n="@@crud-anamnese:anamneses:decription">
		Questionários customizáveis.
	</span>
</ng-template>

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud-anamnese:modal-remove:title">
	Remover Anamnese ?
</span>
<span #removeModalBody [hidden]="true" i18n="@@crud-anamnese:modal-remove:body">
	Deseja remover a anamnese {{ nome }}?
</span>
<span
	#successMsg
	[hidden]="true"
	i18n="@@crud-anamnese:modal-remove:mensagem-success">
	Anamnese removida com sucesso.
</span>
<span
	#errorMsg
	[hidden]="true"
	i18n="@@crud-anamnese:modal-remove:mensagem-error">
	Não é possível remover uma anamnese em uso.
</span>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-anmanese:remover:tooltip-icon">
	Remover
</span>

<ng-template #statusLabel>Status</ng-template>
<ng-template #filterTranslator let-item="name">
	<ng-container [ngSwitch]="item">
		<span *ngSwitchCase="'ATIVO'" i18n="@@crud-anamnese:situacao-ativo">
			Ativo
		</span>
		<span *ngSwitchCase="'INATIVO'" i18n="@@crud-anamnese:situacao-inativo">
			Inativo
		</span>
	</ng-container>
</ng-template>
<ng-template #tooltipInfoData>
	<div class="itens-tooltip">
		Não foi possível recuperar data. Anamnese criada antes da nova versão
	</div>
</ng-template>
