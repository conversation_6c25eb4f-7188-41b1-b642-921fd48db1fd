import { Component, OnInit, ViewChild } from "@angular/core";

import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	TreinoApiAnamneseService,
} from "treino-api";
import {
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";

@Component({
	selector: "pacto-anamnese-lista",
	templateUrl: "./anamnese-lista.component.html",
	styleUrls: ["./anamnese-lista.component.scss"],
})
export class AnamneseListaComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("filterTranslator", { static: true }) filterTranslator;
	@ViewChild("statusLabel", { static: true }) statusLabel;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("successMsg", { static: true }) successMsg;
	@ViewChild("addLabel", { static: true }) addLabel;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("dataLancamentoColumnName", { static: true })
	dataLancamentoColumnName;
	@ViewChild("dataLancamentoCelula", { static: true }) dataLancamentoCelula;
	@ViewChild("errorMsg", { static: true }) errorMsg;
	@ViewChild("statusColumnName", { static: true }) statusColumnName;
	@ViewChild("statusCell", { static: true }) statusCell;
	@ViewChild("tableInstance", { static: true })
	tableInstance: RelatorioComponent;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;

	constructor(
		private anamneseService: TreinoApiAnamneseService,
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private rest: RestService,
		private router: Router,
		public session: SessionService
	) {}

	table: PactoDataGridConfig;
	loading = false;
	filter = null;
	nome;

	recursoAnam: PerfilAcessoRecurso;

	permissoesAnamnese;

	ngOnInit() {
		this.carregarPermissoes();
		this.configTable();
		this.configFilter();
	}

	carregarPermissoes() {
		this.permissoesAnamnese = this.session.recursos.get(
			PerfilAcessoRecursoNome.ANAMNESE
		);
	}

	actionClickHandler($event) {
		if ($event.iconName === "remove") {
			this.nome = $event.row.nome;
			setTimeout(() => {
				const title = this.removeModalTitle.nativeElement.innerHTML;
				const body = this.removeModalBody.nativeElement.innerHTML;
				const success = this.successMsg.nativeElement.innerHTML;
				const error = this.errorMsg.nativeElement.innerHTML;
				const modal = this.modalService.confirm(title, body);
				modal.result
					.then(() => {
						this.anamneseService
							.removerAnamnese($event.row.id)
							.subscribe((status) => {
								if (status === "erro_registro_esta_sendo_utilizado") {
									this.snotifyService.error(error);
								} else if (status === true) {
									this.snotifyService.success(success);
									this.tableInstance.reloadData();
								}
							});
					})
					.catch(() => {});
			});
		}
	}

	rowClickHandler($event) {
		this.router.navigate(["avaliacao", "anamneses", $event.id]);
	}

	btnClickHandler($event) {
		if ($event === "add") {
			this.router.navigate(["avaliacao", "anamneses", "novo"]);
		}
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("anamneses"),
			logUrl: this.rest.buildFullUrl("log/anamneses"),
			quickSearch: true,
			rowClick: this.permissoesAnamnese.editar,
			buttons: !this.permissoesAnamnese.incluir
				? null
				: {
						conteudo: this.addLabel,
						nome: "add",
						id: "btn-novo-anamnese",
				  },
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "status",
					titulo: this.statusColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.statusCell,
				},
				{
					nome: "datalancamento",
					titulo: this.dataLancamentoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.dataLancamentoCelula,
					campo: "datalancamento",
				},
			],
			actions: !this.permissoesAnamnese.excluir
				? []
				: [
						{
							nome: "remove",
							iconClass: "fa fa-trash-o",
							tooltipText: tooltipRemover,
						},
				  ],
		});
	}

	private configFilter() {
		this.filter = {
			filters: [
				{
					name: "status",
					type: GridFilterType.MANY,
					label: this.statusLabel,
					options: [
						{ value: "ATIVO", label: "ativo" },
						{ value: "INATIVO", label: "inativo" },
					],
					translator: this.filterTranslator,
				},
			],
		};
	}
}
