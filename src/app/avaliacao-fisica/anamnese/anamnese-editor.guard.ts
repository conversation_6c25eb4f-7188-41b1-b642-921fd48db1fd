import { Injectable } from "@angular/core";
import {
	ActivatedRouteSnapshot,
	RouterStateSnapshot,
	CanDeactivate,
} from "@angular/router";
import { Observable } from "rxjs";

import { AnamneseEditorComponent } from "./components/anamnese-editor/anamnese-editor.component";
import { ModalService } from "@base-core/modal/modal.service";

@Injectable({
	providedIn: AnamneseEditorComponent,
})
export class AnamneseEditorGuard
	implements CanDeactivate<AnamneseEditorComponent>
{
	public constructor(private modalService: ModalService) {}

	canDeactivate(
		component: AnamneseEditorComponent,
		next: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> | Promise<boolean> | boolean {
		return true;
		// if (component.pendingChanges) {
		//     this.modalService.inform(component.title, component.body);
		//     return false;
		// } else {
		//   return true;
		// }
	}
}
