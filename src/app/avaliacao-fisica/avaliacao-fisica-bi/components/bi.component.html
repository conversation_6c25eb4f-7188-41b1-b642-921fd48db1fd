<pacto-cat-layout-v2>
	<div>
		<div class="row filtros">
			<div class="col-lg-4">
				<div
					*ngIf="permissaoVerBiOutrosProfessor"
					class="label-avaliacao"
					i18n="@@avaliacao-bi:responsavel-avaliacao:label">
					Professor<PERSON>
				</div>
				<pacto-cat-select-filter
					*ngIf="permissaoVerBiOutrosProfessor"
					[control]="formGroup.get('selectProfessor')"
					[id]="'filtro-professor-bi'"
					[labelKey]="'nome'"
					[options]="selectFiltroBiProfessores"
					[placeholder]="'Selecione um professor'"
					[size]="'SMALL'"
					class="filter"></pacto-cat-select-filter>
			</div>

			<div class="col-lg-4">
				<div
					*ngIf="permissaoVerBiOutrosProfessor"
					class="label-avaliacao"
					i18n="@@avaliacao-bi:professor-avaliador:label">
					Avaliador(a)
				</div>
				<pacto-cat-select-filter
					*ngIf="permissaoVerBiOutrosProfessor"
					[control]="formGroup.get('selectAvaliador')"
					[id]="'filtro-avaliador-bi'"
					[labelKey]="'nome'"
					[options]="selectFiltroBiAvaliadores"
					[placeholder]="'Selecione...'"
					[size]="'SMALL'"
					class="filter"></pacto-cat-select-filter>
			</div>

			<div class="col-lg-4 date-filtro">
				<div class="label-org" id="label-org-left">
					<div class="label-periodo" i18n="@@avaliacao-bi:periodo:label">
						Período:
					</div>
					<pacto-datepicker
						[control]="formGroup.get('dataInicio')"></pacto-datepicker>
				</div>

				<div class="label-org" id="label-org-right">
					<div class="label-periodo" i18n="@@avaliacao-bi:periodo-ate:label">
						Até
					</div>
					<pacto-datepicker
						[control]="formGroup.get('dataFim')"
						[direction]="'right'"
						[minDate]="dataMinima"></pacto-datepicker>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-4">
				<pacto-title-card
					[title]="traducoes.getLabel('avaliacoesRealizadasPeriodoTitle')">
					<div
						(click)="cardClickHandler('avaliacoesRealizadas')"
						class="result-obtido-principal">
						<div class="title-number">{{ avaliacaoFisicaBi.avaliacoes }}</div>
						<div class="title" i18n="@@avaliacao-bi:avaliacoes">Avaliações</div>
					</div>
					<div class="info-results">
						<div
							(click)="cardClickHandler('avaliacoesNovasRealizadas')"
							class="result-obtido"
							style="border-right: 1px solid #dedede">
							<div class="subtitle-number">{{ avaliacaoFisicaBi.novas }}</div>
							<div class="subtitle" i18n="@@avaliacao-bi:novas">Novas</div>
						</div>
						<div
							(click)="cardClickHandler('reavaliacoesRealizadas')"
							class="result-obtido">
							<div class="subtitle-number">
								{{ avaliacaoFisicaBi.reavaliacoes }}
							</div>
							<div class="subtitle" i18n="@@avaliacao-bi:reavaliacoes">
								Reavaliações
							</div>
						</div>
					</div>
				</pacto-title-card>
			</div>
			<div class="col-lg-4">
				<pacto-title-card
					i18n-title="@@avaliacao-bi:previsao-reavaliacao"
					title="Previsão de reavaliação no período">
					<div
						(click)="cardClickHandler('previsaoReavaliacoes')"
						class="result-obtido-principal">
						<div class="title-number">{{ avaliacaoFisicaBi.previstas }}</div>
						<div class="title" i18n="@@avaliacao-bi:previstas">Previstas</div>
					</div>
					<div class="info-results">
						<div
							(click)="cardClickHandler('reavaliacaoPrevistaRealizadas')"
							class="result-obtido"
							style="border-right: 1px solid #dedede">
							<div class="subtitle-number">
								{{ avaliacaoFisicaBi.realizadas }}
							</div>
							<div class="subtitle" i18n="@@avaliacao-bi:realizados">
								Realizadas
							</div>
						</div>
						<div
							(click)="cardClickHandler('reavaliacaoPrevistaAtrasadas')"
							class="result-obtido"
							style="border-right: 1px solid #dedede">
							<div class="subtitle-number">
								{{ avaliacaoFisicaBi.atrasadas }}
							</div>
							<div class="subtitle" i18n="@@avaliacao-bi:atrasados">
								Atrasadas
							</div>
						</div>
						<div
							(click)="cardClickHandler('reavaliacaoPrevistaFuturas')"
							class="result-obtido">
							<div class="subtitle-number">{{ avaliacaoFisicaBi.futuras }}</div>
							<div class="subtitle" i18n="@@avaliacao-bi:futuras">Futuras</div>
						</div>
					</div>
				</pacto-title-card>
			</div>
			<div class="col-lg-4">
				<pacto-title-card
					i18n-title="@@avaliacao-bi:alunos-total"
					title="Alunos - Total">
					<div
						(click)="cardClickHandler('ativosSemAvaliacao')"
						class="result-obtido-principal">
						<div class="title-number">{{ avaliacaoFisicaBi.semAvaliacao }}</div>
						<div class="title" i18n="@@avaliacao-bi:ativos-sem-avaliacao">
							Ativos sem avaliação
						</div>
					</div>
					<div class="info-results">
						<div
							(click)="cardClickHandler('ativosComAvaliacaoAtrasada')"
							class="result-obtido">
							<div class="subtitle-number">
								{{ avaliacaoFisicaBi.ativosAtrasada }}
							</div>
							<div
								class="subtitle"
								i18n="@@avaliacao-bi:ativos-com-avaliacao-atrasada">
								Ativos com avaliação atrasada
							</div>
						</div>
					</div>
				</pacto-title-card>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-6">
				<div class="card-wrapper pacto-shadow">
					<div class="row">
						<div
							(click)="cardClickHandler('alunosPerderamPercGordura')"
							class="col-lg-6 info-alunos-superior">
							<div class="title" i18n="@@avaliacao-bi:alunos-perderam-gordura">
								Alunos que perderam % de gordura - Total
							</div>
							<div class="title-number">
								{{ avaliacaoFisicaBi.perderamGordura }}
							</div>
							<div class="title" i18n="@@avaliacao-bi:alunos">Alunos</div>
						</div>
						<div
							(click)="cardClickHandler('alunosPerderamPeso')"
							class="col-lg-6 info-alunos-superior"
							style="border-bottom: 1px solid #dedede">
							<div class="title" i18n="@@avaliacao-bi:alunos-perderam-peso">
								Alunos que perderam peso - Total
							</div>
							<div class="title-number">
								{{ avaliacaoFisicaBi.perderamPeso }}
							</div>
							<div class="title" i18n="@@avaliacao-bi:alunos">Alunos</div>
						</div>
					</div>
					<div class="row">
						<div
							(click)="cardClickHandler('alunosGanharamMassaMagra')"
							class="col-lg-6 info-alunos-inferior">
							<div
								class="title"
								i18n="@@avaliacao-bi:alunos-ganharam-massa-magra">
								Alunos que ganharam massa magra - Total
							</div>
							<div class="title-number">
								{{ avaliacaoFisicaBi.ganharamMassaMagra }}
							</div>
							<div class="title" i18n="@@avaliacao-bi:alunos">Alunos</div>
						</div>
						<div
							(click)="cardClickHandler('alunosParqPositivo')"
							class="col-lg-6 info-alunos-inferior">
							<div class="title" i18n="@@avaliacao-bi:alunos-par-q">
								Alunos Par-Q positivo
							</div>
							<div class="title-number">{{ avaliacaoFisicaBi.alunosParq }}</div>
							<div class="title" i18n="@@avaliacao-bi:alunos">Alunos</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-lg-6">
				<pacto-title-card
					i18n-title="@@avaliacao-bi:objetivos-alunos"
					title="Objetivos dos alunos - Total">
					<div
						[style.height.px]="324"
						[style.width.%]="100"
						id="objetivo"></div>
				</pacto-title-card>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>

<ng-template #professorTemplate let-item="item">
	<div class="user-item-wrapper">
		<div *ngIf="item.imageUri" class="image">
			<img src="{{ item.imageUri }}" />
		</div>
		<div class="name">
			{{ item.nome }}
		</div>
	</div>
</ng-template>

<div
	#optionTodosProfessores
	[hidden]="true"
	i18n="@@avaliacao-bi:todos-professores">
	*Todos professores
</div>
<div
	#optionAlunosSemVinculo
	[hidden]="true"
	i18n="@@avaliacao-bi:alunos-sem-vinculo">
	*Alunos sem vínculo
</div>
<div
	#optionTodosProfessoresInativos
	[hidden]="true"
	i18n="@@avaliacao-bi:professores-inativos">
	*Todos professores inativos
</div>
<div #objetivoTitleGrafic [hidden]="true" i18n="@@avaliacao-bi:objetivo">
	Objetivo
</div>

<div
	#optionTodosAvaliadores
	[hidden]="true"
	i18n="@@avaliacao-bi:todos-professores">
	*Todos Avaliadores
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@avaliacao-bi:modal:avaliacao-realizada-periodo"
		xingling="avaliacoesRealizadasPeriodoTitle">
		Avaliações realizadas no periodo
	</span>
	<span xingling="reavaliacoesRealizadasPeriodoTitle">
		Reavaliações realizadas no periodo
	</span>
	<span xingling="avaliacoesNovasRealizadasPeriodoTitle">
		Avaliações novas realizadas no periodo
	</span>
	<span
		i18n-title="@@avaliacao-bi:previsao-reavaliacao"
		xingling="previsaoReavaliacoesTitle">
		Previsão de reavaliação no período
	</span>
	<span xingling="reavaliacaoPrevistaRealizadasTitle">
		Previsão de reavaliação realizadas no período
	</span>
	<span xingling="reavaliacaoPrevistaAtrasadasTitle">
		Previsão de reavaliação atrasadas no período
	</span>
	<span xingling="reavaliacaoPrevistaFuturasTitle">
		Previsão de reavaliação futuras no período
	</span>
	<span
		i18n="@@avaliacao-bi:ativos-sem-avaliacao"
		xingling="ativosSemAvaliacaoTitle">
		Ativos sem avaliação
	</span>
	<span
		i18n="@@avaliacao-bi:ativos-com-avaliacao-atrasada"
		xingling="ativosComAvaliacaoAtrasadaTitle">
		Ativos com avaliação atrasada
	</span>
	<span
		i18n="@@avaliacao-bi:alunos-perderam-gordura"
		xingling="alunosPerderamPercGorduraTitle">
		Alunos que perderam % de gordura
	</span>
	<span
		i18n="@@avaliacao-bi:alunos-perderam-peso"
		xingling="alunosPerderamPesoTitle">
		Alunos que perderam peso
	</span>
	<span
		i18n="@@avaliacao-bi:alunos-ganharam-massa-magra"
		xingling="alunosGanharamMassaMagraTitle">
		Alunos que ganharam massa magra
	</span>
	<span i18n="@@avaliacao-bi:alunos-par-q" xingling="alunosParqPositivoTitle">
		Alunos Par-Q positivo
	</span>
	<span
		i18n="@@avaliacao-bi:modal:objetivos:title"
		xingling="objetivoAlunosTitle">
		Objetivos do aluno
	</span>

	<ng-template i18n="@@avaliacao-bi:modal:matricula" xingling="matricula">
		Matrícula
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:matricula" xingling="modalidade">
		Modalidade
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:aluno" xingling="nome">
		Aluno
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:dia" xingling="dataAvaliacao">
		Dia
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:dia" xingling="dataProxima">
		Próxima Avaliação
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:avaliador" xingling="avaliador">
		Avaliador
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:professor" xingling="professor">
		Professor
	</ng-template>
	<ng-template i18n="@@avaliacao-bi:modal:detalhe" xingling="obs">
		Detalhe
	</ng-template>
	<ng-template xingling="diff">Diferença</ng-template>
</pacto-traducoes-xingling>
