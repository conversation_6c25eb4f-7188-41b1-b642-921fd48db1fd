export interface ModalRelatorioConfig {
	title: string;
	pagination?: boolean;
	endpoint: string;
	indicador: string;
	objetivo?: string;
	columns: {
		value: any;
		date?: boolean;
		kilo?: boolean;
		percentual?: boolean;
		ordenavel?: boolean;
	}[];
}

export const relatorios: { [relatorio: string]: ModalRelatorioConfig } = {
	avaliacoesRealizadas: {
		pagination: true,
		title: "avaliacoesRealizadasPeriodoTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_TODAS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataAvaliacao", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	reavaliacoesRealizadas: {
		pagination: true,
		title: "reavaliacoesRealizadasPeriodoTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_REAVALIACOES",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataAvaliacao", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	avaliacoesNovasRealizadas: {
		pagination: true,
		title: "avaliacoesNovasRealizadasPeriodoTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_NOVAS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataAvaliacao", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	previsaoReavaliacoes: {
		pagination: true,
		title: "previsaoReavaliacoesTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_PREVISTAS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataAvaliacao", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	reavaliacaoPrevistaRealizadas: {
		pagination: true,
		title: "reavaliacaoPrevistaRealizadasTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_REALIZADAS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataAvaliacao", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	reavaliacaoPrevistaAtrasadas: {
		pagination: true,
		title: "reavaliacaoPrevistaAtrasadasTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_ATRASADAS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataProxima", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	reavaliacaoPrevistaFuturas: {
		pagination: true,
		title: "reavaliacaoPrevistaFuturasTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_FUTURAS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "dataProxima", date: true },
			{ value: "avaliador" },
			{ value: "professor" },
		],
	},
	ativosSemAvaliacao: {
		pagination: true,
		title: "ativosSemAvaliacaoTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_ATIVOS_SEM",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "modalidade", ordenavel: false },
		],
	},
	ativosComAvaliacaoAtrasada: {
		pagination: true,
		title: "ativosComAvaliacaoAtrasadaTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_ATIVOS_AVALIACAO_ATRASADA",
		columns: [{ value: "matricula", ordenavel: false }, { value: "nome" }],
	},
	alunosPerderamPercGordura: {
		pagination: true,
		title: "alunosPerderamPercGorduraTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_PERDERAM_PERCENTUAL",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "diff", percentual: true },
			{ value: "obs", ordenavel: false },
		],
	},
	alunosPerderamPeso: {
		pagination: true,
		title: "alunosPerderamPesoTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_PERDERAM_PESO",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "diff", kilo: true },
			{ value: "obs", ordenavel: false },
		],
	},
	alunosGanharamMassaMagra: {
		pagination: true,
		title: "alunosGanharamMassaMagraTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_GANHARAM_MASSA",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "diff", kilo: true },
			{ value: "obs", ordenavel: false },
		],
	},
	alunosParqPositivo: {
		pagination: true,
		title: "alunosParqPositivoTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_PARQ_POSITIVO",
		columns: [{ value: "matricula", ordenavel: false }, { value: "nome" }],
	},
	objetivoAlunos: {
		pagination: true,
		title: "objetivoAlunosTitle",
		endpoint: "avaliacao-fisica-bi/avaliacoes",
		indicador: "IND_AF_OBJETIVOS",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "obs", ordenavel: false },
		],
	},
};
