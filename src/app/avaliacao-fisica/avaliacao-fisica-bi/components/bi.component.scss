@import "~src/assets/scss/pacto/plataforma-import.scss";

.filtros {
	justify-content: space-between;

	.date-filtro {
		display: flex;
		justify-content: space-between;
	}
}

.label-periodo {
	color: #333333;
	font-weight: 600;
	flex-grow: 1;
}

.time-controls {
	display: flex;
	justify-content: space-between;
	margin: 0px 15px;
	padding: 0px 10px 0px 0px;
}

.time-controls-content.dataInicio {
	margin-left: -20px;
}

.time-controls-content.ate {
	margin-right: 8px;
	margin-top: 4px;
}

.time-controls-content.dataFim {
	margin-right: 20px;
}

.label-avaliacao {
	color: #333333;
	margin-left: -10px;
	font-weight: 600;
	flex-grow: 1;
	color: #333333;
	padding: 0px 10px 0px 0px;
}

.label-org {
	display: block;
}

#label-org-left {
	float: left;
	margin-right: 10px;
}

#label-org-right {
	float: left;
}

.user-item-wrapper {
	display: flex;

	.image {
		img {
			width: 30px;
			height: 30px;
			border-radius: 15px;
		}
	}

	.name {
		font-size: 14px;
		line-height: 30px;
		margin-left: 10px;
		font-weight: 600;
		color: #333333;
	}
}

.result-obtido-principal {
	text-align: center;
	border-bottom: 1px solid #dedede;
	cursor: pointer;
}

.info-results {
	display: flex;
	justify-content: space-between;
	border-bottom: 1px solid #dedede;

	.result-obtido {
		text-align: center;
		width: 100%;
		cursor: pointer;
	}
}

.card-wrapper {
	padding: 15px 15px 30px 15px;
	background-color: #ffffff;
	width: 100%;
	margin-bottom: 20px;
	position: relative;
}

.info-alunos-superior {
	border-bottom: 1px solid #dedede;
	cursor: pointer;

	.title {
		margin-bottom: 10px;
		height: 48px;
	}

	&:first-child {
		border-right: 1px solid #dedede;
	}
}

.info-alunos-inferior {
	cursor: pointer;

	&:first-child {
		border-right: 1px solid #dedede;
	}

	.title {
		padding-top: 10px;
		margin-bottom: 0px;
		height: 48px;
	}
}

.title {
	font-size: 16px;
	font-weight: 600;
	flex-grow: 1;
	color: #333333;
	margin-bottom: 30px;
	text-align: center;
}

.title-number {
	font-size: 50px;
	font-weight: 600;
	text-align: center;
}

.subtitle {
	font-size: 16px;
	font-weight: 600;
	flex-grow: 1;
	color: #333333;
	margin-bottom: 10px;
	text-align: center;
}

.subtitle-number {
	font-size: 25px;
	font-weight: 600;
	text-align: center;
	padding-top: 10px;
}

.row {
	padding-bottom: 15px;
	padding-top: 15px;
}

.button-compartilhar {
	display: -webkit-inline-box;
	font-size: small;
}

.btn-primary {
	color: $branco;
	background-color: $azulimPri !important;
	border-color: $azulimPri !important;
}

.loading-blur {
	filter: blur(5px);
	pointer-events: none;
}

.loading-text {
	@extend .type-h6-bold;
	color: $azulPacto04;
	position: absolute;
	width: 168px;
	height: 16px;
	left: calc(50% - 168px / 2);
	top: calc(50% + 35px / 2);
}

.pct-refresh-cw {
	color: $azulPacto04;
	font-size: 30px;
	line-height: 30px;
	font-style: normal;
}

.sk-fading-circle {
	position: absolute;
	width: 30px;
	height: 30px;
	left: calc(50% - 30px / 2);
	top: calc(50% - 30px / 2);
	-webkit-animation-name: spin;
	-webkit-animation-duration: 2000ms;
	-webkit-animation-iteration-count: infinite;
	-webkit-animation-timing-function: linear;
	-moz-animation-name: spin;
	-moz-animation-duration: 2000ms;
	-moz-animation-iteration-count: infinite;
	-moz-animation-timing-function: linear;

	animation-name: spin;
	animation-duration: 2000ms;
	animation-iteration-count: infinite;
	animation-timing-function: linear;
}

@-ms-keyframes spin {
	from {
		-ms-transform: rotate(0deg);
	}
	to {
		-ms-transform: rotate(359deg);
	}
}

@-moz-keyframes spin {
	from {
		-moz-transform: rotate(0deg);
	}
	to {
		-moz-transform: rotate(359deg);
	}
}

@-webkit-keyframes spin {
	from {
		-webkit-transform: rotate(0deg);
	}
	to {
		-webkit-transform: rotate(359deg);
	}
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(359deg);
	}
}

@media only screen and (max-width: 991px) {
	.label-periodo {
		padding: 15px 10px 0px 0px;
	}

	.label-avaliacao {
		padding: 15px 10px 0px 0px;
	}
}
