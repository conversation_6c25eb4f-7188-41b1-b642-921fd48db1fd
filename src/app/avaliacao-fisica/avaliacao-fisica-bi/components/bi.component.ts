import {
	Component,
	OnInit,
	ViewChild,
	TemplateRef,
	ChangeDetectorRef,
} from "@angular/core";
import {
	AvaliacaoFisicaBi,
	GraficoAvaliacaoFisicaBi,
	TipoBIAvaliacaoEnum,
	AvaliacaoFisicaBiFiltro,
	TreinoApiAvaliacaoFisicaService,
	TreinoApiObjetivoService,
	TreinoApiColaboradorService,
	UsuarioBase,
	ProfessorSimples,
} from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { AmChart, AmChartsService } from "@amcharts/amcharts3-angular";
import { NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import { Observable, Subject, Subscription, of } from "rxjs";
import {
	debounceTime,
	distinctUntilChanged,
	map,
	takeUntil,
	switchMap,
	catchError,
} from "rxjs/operators";
import {
	ModalRelatorioConfig,
	relatorios,
} from "../../../avaliacao-fisica/avaliacao-fisica-bi/components/relatorios.config";
import {
	RelatorioComponent,
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { ShareComponent } from "src/app/share/share.component";
import { RecursoSistema } from "../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { Router } from "@angular/router";

declare var moment;

@Component({
	selector: "pacto-bi",
	templateUrl: "./bi.component.html",
	styleUrls: ["./bi.component.scss"],
})
export class BiComponent implements OnInit {
	@ViewChild("optionTodosProfessores", { static: true }) optionTodosProfessores;

	@ViewChild("optionTodosAvaliadores", { static: true }) optionTodosAvaliadores;

	@ViewChild("optionAlunosSemVinculo", { static: true }) optionAlunosSemVinculo;
	@ViewChild("optionTodosProfessoresInativos", { static: true })
	optionTodosProfessoresInativos;
	@ViewChild("objetivoTitleGrafic", { static: true }) objetivoTitleGrafic;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	avaliacaoFisicaBi: AvaliacaoFisicaBi = new (class
		implements AvaliacaoFisicaBi
	{
		alunosParq: number;
		ativosAtrasada: number;
		atrasadas: number;
		avaliacoes: number;
		futuras: number;
		ganharamMassaMagra: number;
		grafico: Array<GraficoAvaliacaoFisicaBi>;
		novas: number;
		perderamGordura: number;
		perderamPeso: number;
		previstas: number;
		realizadas: number;
		reavaliacoes: number;
		semAvaliacao: number;
	})();
	private ocupacaoChart: AmChart;
	_objetivosData: Array<any> = [];
	selectFiltroBiProfessores: Array<any> = [];
	selectFiltroBiAvaliadores: Array<any> = [];
	dataMinima: NgbDateStruct = null;
	loading = false;
	formGroup: FormGroup = new FormGroup({
		dataInicio: new FormControl(new Date().setDate(1)),
		dataFim: new FormControl(new Date().getTime()),
		selectProfessor: new FormControl({ value: -1, disabled: false }),
		selectAvaliador: new FormControl({ value: -1, disabled: false }),
	});

	permissaoVerBiOutrosProfessor;
	endpointShare: string;
	relModal: ModalRelatorioConfig;
	private subscriptions: Subscription[] = [];
	private unsubscribe: Subject<void> = new Subject<void>();
	private isLoading: boolean = false;

	constructor(
		private AmCharts: AmChartsService,
		private avaliacaoService: TreinoApiAvaliacaoFisicaService,
		private objetivoService: TreinoApiObjetivoService,
		private colaboradorService: TreinoApiColaboradorService,
		private cd: ChangeDetectorRef,
		public sessionService: SessionService,
		private modal: ModalService,
		private rest: RestService,
		private router: Router
	) {}

	ngOnInit() {
		this.loading = true;
		this.carregarPermissoes();
		const dataInicio = new Date(this.formGroup.get("dataInicio").value);
		this.dataMinima = {
			day: dataInicio.getDate(),
			month: dataInicio.getMonth() + 1,
			year: dataInicio.getFullYear(),
		};

		const formDataChanges$ = this.formGroup.valueChanges.pipe(
			debounceTime(300),
			distinctUntilChanged()
		);

		this.subscriptions.push(
			formDataChanges$.subscribe(() => {
				this.loading = true;
				this.loadData();
			})
		);

		this.subscriptions.push(
			this.formGroup
				.get("dataInicio")
				.valueChanges.pipe(debounceTime(300), distinctUntilChanged())
				.subscribe((value: number) => {
					const controlDataInicio = new Date(value);
					this.dataMinima = {
						day: controlDataInicio.getDate(),
						month: controlDataInicio.getMonth() + 1,
						year: controlDataInicio.getFullYear(),
					};
				})
		);

		this.subscriptions.push(
			this.setSelect()
				.pipe(debounceTime(300), distinctUntilChanged())
				.subscribe(() => {
					this.loading = true;
					this.loadData();
				})
		);

		this.sessionService.notificarRecursoEmpresa(RecursoSistema.DASH_AVALIACAO);
		this.getListaProfessores();
	}

	ngAfterViewInit() {
		if (!this.permissaoVerBiOutrosProfessor) {
			this.formGroup.get("selectProfessor").disable({ emitEvent: false });
			this.formGroup.get("selectAvaliador").disable({ emitEvent: false });
		}
	}

	ngOnDestroy() {
		this.subscriptions.forEach((sub) => sub.unsubscribe());
		this.unsubscribe.next();
		this.unsubscribe.complete();
	}

	private loadData() {
		if (this.isLoading) {
			return;
		}
		this.isLoading = true;
		const dto = this.permissaoVerBiOutrosProfessor
			? this.getDto()
			: this.getDtoProfessorLogado();
		this.gerarBi(dto).add(() => {
			this.isLoading = false;
			if (!this.avaliacaoFisicaBi || !this.avaliacaoFisicaBi.grafico.length) {
				console.warn("Nenhum dado retornado para o BI.");
			}
		});
	}

	private gerarBi(dto: any) {
		return this.avaliacaoService
			.gerarBi(dto)
			.pipe(takeUntil(this.unsubscribe))
			.subscribe(
				(result) => {
					this.avaliacaoFisicaBi = result;
					this.popularGrafico();
					this.loading = false;
					this.cd.detectChanges();
					this.sessionService.notificarRecursoEmpresa(
						RecursoSistema.ENTROU_DASHBOARD_AVALIACAO_NTO
					);
				},
				(error) => {
					console.error("Erro ao gerar BI:", error);
					this.loading = false;
					this.cd.detectChanges();
				}
			);
	}

	private popularGrafico() {
		this.processarDadosGraficoOriginal();

		if (
			!this.avaliacaoFisicaBi ||
			!this.avaliacaoFisicaBi.grafico ||
			this.avaliacaoFisicaBi.grafico.length === 0
		) {
			const dtoGrafico = this.permissaoVerBiOutrosProfessor
				? this.getDto()
				: this.getDtoProfessorLogado();

			this.avaliacaoService
				.gerarBi(dtoGrafico)
				.pipe(takeUntil(this.unsubscribe))
				.subscribe(
					(resultFiltrado) => {
						if (resultFiltrado.grafico.length > 0) {
							this.montarDadosGrafico(resultFiltrado.grafico);
						} else {
							this.objetivoService.listarTodosObjetivos().subscribe({
								next: (todos) => {
									const fallback = todos.map((o) => ({
										nome: o.nome,
										nomeAbv:
											o.nome.length > 6
												? o.nome.substring(0, 6) + "..."
												: o.nome,
										valor: 0,
									}));
									this.montarDadosGrafico(fallback);
								},
								error: (err) => {
									console.error("Erro ao buscar objetivos:", err);
								},
							});
						}
					},
					(error) => console.error("Erro ao gerar dados para o gráfico:", error)
				);
		}
	}

	private processarDadosGraficoOriginal() {
		const grafico =
			this.avaliacaoFisicaBi && this.avaliacaoFisicaBi.grafico
				? this.avaliacaoFisicaBi.grafico
				: [];

		if (grafico.length > 0) {
			this.montarDadosGrafico(grafico);
		}
	}

	private montarDadosGrafico(dados: any[]) {
		const limiteColunasGrafico = 8;
		this._objetivosData = [];

		const copia = [...dados];
		for (let i = 0; i < limiteColunasGrafico && copia.length > 0; i++) {
			const indexMaior = this.retornaIndexMaiorValor(copia);
			const obj = copia.splice(indexMaior, 1)[0];
			obj.nomeAbv =
				obj.nomeAbv ||
				(obj.nome.length > 6 ? obj.nome.substring(0, 6) + "..." : obj.nome);
			this._objetivosData.push(obj);
		}

		setTimeout(() => this.buildFrequenciaGraph(), 50);
	}

	private retornaIndexMaiorValor(lista: Array<any>): number {
		let maiorValor = 0;
		let indexMaiorValor = 0;
		if (lista.length > 1) {
			lista.forEach((objetivo, index) => {
				if (objetivo.valor > maiorValor) {
					maiorValor = objetivo.valor;
					indexMaiorValor = index;
				}
			});
		}
		return indexMaiorValor;
	}

	private getDto() {
		const dto: AvaliacaoFisicaBiFiltro = new (class
			implements AvaliacaoFisicaBiFiltro
		{
			codigoProfessor: number;
			dataFim: number;
			dataInicio: number;
			tipoAvaliacao: TipoBIAvaliacaoEnum;
			codigoAvaliador: number;
		})();
		if (
			this.formGroup.get("dataInicio").value >
			this.formGroup.get("dataFim").value
		) {
			const novaData: any = new Date(this.formGroup.get("dataInicio").value);
			novaData.setDate(novaData.getDate() + 30);
			this.formGroup.get("dataFim").setValue(novaData.getTime());
		}
		dto.dataInicio = this.formGroup.get("dataInicio").value;
		dto.dataFim = this.formGroup.get("dataFim").value;

		dto.codigoProfessor = -1;
		dto.codigoAvaliador = -1;
		dto.tipoAvaliacao = null;

		if (this.permissaoVerBiOutrosProfessor) {
			if (this.formGroup.get("selectProfessor").value) {
				const tipoEnum = isNaN(this.formGroup.get("selectProfessor").value.id);
				if (tipoEnum) {
					dto.tipoAvaliacao = this.formGroup.get("selectProfessor").value.id;
				} else {
					dto.codigoProfessor = this.formGroup.get("selectProfessor").value.id;
				}
			}
			if (this.formGroup.get("selectAvaliador").value) {
				const tipoEnum = isNaN(this.formGroup.get("selectAvaliador").value.id);
				if (tipoEnum) {
					if (
						this.formGroup.get("selectAvaliador").value.id ===
						"ALUNOS_SEM_VINCULO"
					) {
						dto.codigoAvaliador = -3;
					} else if (
						this.formGroup.get("selectAvaliador").value.id ===
						"TODOS_PROFESSORES_INATIVOS"
					) {
						dto.codigoAvaliador = -2;
					} else {
						dto.codigoAvaliador = -1;
					}
				} else {
					dto.codigoAvaliador = this.formGroup.get("selectAvaliador").value.id;
				}
			}
		} else {
			dto.codigoProfessor = this.sessionService.loggedUser.professorResponse.id;
		}
		return dto;
	}

	setSelect(): Observable<boolean> {
		const optionTodosProfessores =
			this.optionTodosProfessores.nativeElement.innerHTML;
		const optionAlunosSemVinculo =
			this.optionAlunosSemVinculo.nativeElement.innerHTML;
		const optionTodosProfessoresInativos =
			this.optionTodosProfessoresInativos.nativeElement.innerHTML;
		const optionTodosAvaliadores =
			this.optionTodosAvaliadores.nativeElement.innerHTML;

		return this.colaboradorService.obterTodosColaboradores(false, true).pipe(
			map((result) => {
				this.selectFiltroBiProfessores = result.content;
				this.selectFiltroBiAvaliadores = result.content.slice(0);
				this.adicionarOpcoesIniciais(
					optionAlunosSemVinculo,
					optionTodosProfessoresInativos,
					optionTodosProfessores,
					optionTodosAvaliadores
				);
				return true;
			}),
			switchMap(() => this.verificarPermissoes()),
			catchError((error) => {
				console.error("Erro ao carregar colaboradores", error);
				return of(false);
			})
		);
	}

	private adicionarOpcoesIniciais(
		optionAlunosSemVinculo: string,
		optionTodosProfessoresInativos: string,
		optionTodosProfessores: string,
		optionTodosAvaliadores: string
	): void {
		this.selectFiltroBiAvaliadores.unshift({
			id: "TODOS_COLABORADORES",
			nome: "*Todos colaboradores",
		});
		this.selectFiltroBiProfessores.unshift(
			{ id: "ALUNOS_SEM_VINCULO", nome: optionAlunosSemVinculo },
			{
				id: "TODOS_PROFESSORES_INATIVOS",
				nome: optionTodosProfessoresInativos,
			},
			{ id: "TODOS_PROFESSORES", nome: optionTodosProfessores }
		);
	}

	private verificarPermissoes(): Observable<boolean> {
		if (this.permissaoVerBiOutrosProfessor) {
			this.formGroup.get("selectProfessor").setValue({
				id: "TODOS_PROFESSORES",
				nome: this.optionTodosProfessores.nativeElement.innerHTML,
			});
			this.formGroup
				.get("selectAvaliador")
				.setValue({ id: -1, nome: "*Todos colaboradores" });
			return of(true);
		} else {
			this.adicionarSemPermissao();
			return this.colaboradorService
				.consultarPorUsuario(this.sessionService.loggedUser.id)
				.pipe(
					map((response) => {
						this.atualizarSelectsComUsuario(response);
						return true;
					})
				);
		}
	}

	private adicionarSemPermissao(): void {
		this.selectFiltroBiProfessores.unshift({ id: "SEM_PERMISSAO", nome: "" });
		this.selectFiltroBiAvaliadores.unshift({ id: "SEM_PERMISSAO", nome: "" });
		this.formGroup
			.get("selectProfessor")
			.setValue([{ id: "SEM_PERMISSAO", nome: "" }]);
		this.formGroup
			.get("selectAvaliador")
			.setValue([{ id: "SEM_PERMISSAO", nome: "" }]);
	}

	private atualizarSelectsComUsuario(response: any): void {
		this.selectFiltroBiProfessores.forEach((professor) => {
			if (professor.codigoPessoa === response.codigoPessoa) {
				this.formGroup
					.get("selectProfessor")
					.setValue([{ id: professor.id, nome: response.nome }]);
			}
		});
		this.selectFiltroBiAvaliadores.forEach((professor) => {
			if (professor.codigoPessoa === response.codigoPessoa) {
				this.formGroup
					.get("selectAvaliador")
					.setValue([{ id: professor.id, nome: response.nome }]);
			}
		});
	}

	private buildFrequenciaGraph() {
		const objetivo = this.objetivoTitleGrafic.nativeElement.innerHTML;
		this.ocupacaoChart = this.AmCharts.makeChart("objetivo", {
			type: "serial",
			theme: "none",
			fontFamily: "'Nunito Sans', sans-serif",
			dataProvider: this._objetivosData,
			listeners: [
				{
					event: "clickGraphItem",
					method: (objeto) => {
						const item = relatorios.objetivoAlunos;
						const data = this._objetivosData[objeto.index];
						item.objetivo = data.nome;
						this.openModalHandler(item);
					},
				},
			],
			valueAxes: [
				{
					stackType: "regular",
					axisAlpha: 0.3,
					gridAlpha: 0.2,
				},
			],
			graphs: [
				{
					balloonFunction: (valueText) => {
						return (
							"<b>" +
							objetivo +
							'</b><br><span style="font-size:14px">' +
							valueText.dataContext.nome +
							": <b>" +
							valueText.dataContext.valor +
							"</b></span>"
						);
					},
					fillAlphas: 1,
					image: "https://randomuser.me/api/portraits/women/71.jpg",
					labelText: "[[value]]",
					lineAlpha: 0,
					fillColors: "#D4145A",
					title: objetivo,
					fontSize: "14",
					type: "column",
					color: "#fff",
					valueField: "valor",
				},
			],
			categoryField: "nomeAbv",
			categoryAxis: {
				gridPosition: "start",
				axisAlpha: 0.2,
				gridAlpha: 0.2,
				position: "left",
			},
		});
	}

	private carregarPermissoes() {
		this.permissaoVerBiOutrosProfessor =
			this.sessionService.perfilUsuario.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.VER_BI_OUTROS_PROFESSORES
			);
	}

	cardClickHandler(name) {
		try {
			const rota = this.router.url;
			localStorage.removeItem("config_table__" + rota);
		} catch (e) {
			console.log("problema ao limpar filtro dash");
		}
		this.atualizarSelectAvaliador();
		this.relModal = relatorios[name];
		this.openModalHandler(relatorios[name]);
	}

	atualizarSelectAvaliador() {
		if (
			this.permissaoVerBiOutrosProfessor &&
			!this.formGroup.get("selectAvaliador").value
		) {
			this.formGroup
				.get("selectAvaliador")
				.setValue({ id: -1, nome: "*Todos colaboradores" });
		}
	}

	private openModalHandler(item: ModalRelatorioConfig) {
		this.endpointShare = this.rest.buildFullUrl(item.endpoint);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		const relatorio: RelatorioComponent = pctModal.componentInstance;
		relatorio.rowClick.subscribe(($item) => {
			window
				.open("pessoas/perfil-v2/" + $item.matricula + "/contratos", "_blank")
				.focus();
		});
		switch (item.title) {
			case "avaliacoesRealizadasPeriodoTitle":
				relatorio.telaId = "avaliacoesRealizadasPeriodo";
				break;
			case "avaliacoesNovasRealizadasPeriodoTitle":
				relatorio.telaId = "avaliacoesNovasRealizadasPeriodo";
				break;
			case "reavaliacoesRealizadasPeriodoTitle":
				relatorio.telaId = "reavaliacoesRealizadasPeriodo";
				break;
			case "previsaoReavaliacoesTitle":
				relatorio.telaId = "previsaoReavaliacoes";
				break;
			case "reavaliacaoPrevistaRealizadasTitle":
				relatorio.telaId = "reavaliacaoPrevistaRealizadas";
				break;
			case "reavaliacaoPrevistaAtrasadasTitle":
				relatorio.telaId = "reavaliacaoPrevistaAtrasadas";
				break;
			case "reavaliacaoPrevistaFuturasTitle":
				relatorio.telaId = "reavaliacaoPrevistaFuturas";
				break;
			case "ativosSemAvaliacaoTitle":
				relatorio.telaId = "ativosSemAvaliacao";
				break;
			case "ativosComAvaliacaoAtrasadaTitle":
				relatorio.telaId = "ativosComAvaliacaoAtrasada";
				break;
			case "alunosPerderamPercGorduraTitle":
				relatorio.telaId = "alunosPerderamPercGordura";
				break;
			case "alunosPerderamPesoTitle":
				relatorio.telaId = "alunosPerderamPeso";
				break;
			case "alunosGanharamMassaMagraTitle":
				relatorio.telaId = "alunosGanharamMassaMagra";
				break;
			case "alunosParqPositivoTitle":
				relatorio.telaId = "alunosParqPositivo";
				break;
			case "objetivoAlunosTitle":
				relatorio.telaId = "objetivoAlunos";
				break;
		}

		relatorio.sessionService = this.sessionService;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.rest.buildFullUrl(item.endpoint),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/" + relatorio.telaId
			),
			pagination: item.pagination,
			quickSearch: true,
			columns: [],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: false,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
				date: false,
			};
			if (column.date) {
				columnConfig.date = true;
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			if (column.percentual) {
				columnConfig.valueTransform = (d) => d.concat(" %");
			}
			if (column.kilo) {
				columnConfig.valueTransform = (d) => d.concat(" kg");
			}
			config.columns.push(columnConfig);
		});
		relatorio.table = new PactoDataGridConfig(config);
		const dto = this.getDto();
		relatorio.baseFilter = {
			filters: {
				dataInicio: dto.dataInicio,
				dataFim: dto.dataFim,
				professorId: dto.codigoProfessor,
				avaliadorId: dto.codigoAvaliador,
				indicador: item.indicador,
				objetivo: item.objetivo,
				tipoAvaliacao: dto.tipoAvaliacao,
			},
		};
	}

	shareClickHandler() {
		this.openModalShare(this.relModal);
	}

	private openModalShare(item: ModalRelatorioConfig) {
		const pctModal = this.modal.open(
			"Compartilhar",
			ShareComponent,
			PactoModalSize.MEDIUM
		);
		const relatorio: RelatorioComponent = pctModal.componentInstance;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.endpointShare,
			pagination: item.pagination,
			columns: [],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: false,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (column.date) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			if (column.percentual) {
				columnConfig.valueTransform = (d) => d.concat(" %");
			}
			if (column.kilo) {
				columnConfig.valueTransform = (d) => d.concat(" kg");
			}
			config.columns.push(columnConfig);
		});
		relatorio.table = new PactoDataGridConfig(config);
		relatorio.baseFilter = {
			filters: {
				dataInicio: this.getDto().dataInicio,
				dataFim: this.getDto().dataFim,
				professorId: this.getDto().codigoProfessor,
				indicador: item.indicador,
				objetivo: item.objetivo,
			},
		};
	}

	private getListaProfessores() {
		this.fetchProfessores().subscribe((professores) => {});
	}

	private fetchProfessores(): Observable<UsuarioBase[]> {
		return this.colaboradorService.obterProfessoresDadosBasicos(0).pipe(
			map((result) => {
				this.selectFiltroBiProfessores = result.content;
				this.selectFiltroBiProfessores.unshift({
					id: "TODOS_PROFESSORES",
					nome: this.optionTodosProfessores.nativeElement.innerHTML,
				});
				return result.content;
			})
		);
	}
	private getDtoProfessorLogado(): AvaliacaoFisicaBiFiltro {
		const dto: AvaliacaoFisicaBiFiltro = new (class
			implements AvaliacaoFisicaBiFiltro
		{
			codigoProfessor: number;
			dataFim: number;
			dataInicio: number;
			codigoAvaliador: number;
		})();

		dto.dataInicio = this.formGroup.get("dataInicio").value;
		dto.dataFim = this.formGroup.get("dataFim").value;
		dto.codigoProfessor = this.sessionService.loggedUser.professorResponse.id;
		dto.codigoAvaliador = this.sessionService.codUsuarioZW;

		return dto;
	}
}
