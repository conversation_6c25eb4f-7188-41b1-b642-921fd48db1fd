import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { BiComponent } from "./components/bi.component";
import { RouterModule, Routes } from "@angular/router";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { RelatorioComponent } from "ui-kit";

const recursos = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.GESTAO, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: BiComponent,
	},
];

@NgModule({
	imports: [
		NgbModule,
		CommonModule,
		BaseSharedModule,
		RouterModule.forChild(routes),
	],
	declarations: [BiComponent],
	entryComponents: [RelatorioComponent],
})
export class BiModule {}
