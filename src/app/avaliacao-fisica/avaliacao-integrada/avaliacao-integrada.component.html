<div class="perfil-wrapper">
	<div [ngClass]="{ 'loading-blur': loading }">
		<div *ngIf="mostrarTopo" class="top-aux">
			<pacto-avaliacoes-aluno-top
				[aluno]="aluno"
				[criando]="criando"
				[isAvIntegrada]="true"
				[rota]="['/cadastros', 'alunos', 'perfil', aluno?.id]"
				i18n-rotaLabel="@@avaliacao-fisica:avaliacoes-aluno"
				rotaLabel="Avaliação Integrada do aluno"></pacto-avaliacoes-aluno-top>
		</div>

		<div class="body-width">
			<div class="content-tab">
				<div *ngIf="avaliacaoAtual">
					<div class="type-h3 avaliacao-nome">
						<span
							*ngIf="!avaliacaoAtual.id"
							i18n="@@avaliacao-fisica:avaliacoes-aluno:criar">
							Criar Avaliação Integrada
						</span>
						<span *ngIf="avaliacaoAtual.id">
							<ng-container i18n="@@avaliacao-fisica:avaliacoes-aluno:editar">
								Editar Avaliação Integrada
							</ng-container>
							#{{ avaliacaoAtual.id }} -
							{{ avaliacaoAtual.dataLancamento | date : "shortDate" }}
						</span>
					</div>
				</div>
			</div>

			<div *ngIf="avaliacaoAtual" class="content-aux">
				<div class="content-card">
					<div class="content-title">
						<div class="type-h5 categoria">
							<ng-container>{{ traducoes.getLabel("ANAMNESE") }}</ng-container>
						</div>
						<div
							(click)="cancelarHandler()"
							*ngIf="criando || changes"
							class="button-avaliacao"
							id="cancelar-avaliacao-fisica">
							CANCELAR
						</div>
						<div
							(click)="salvarHandler()"
							*ngIf="criando || changes"
							class="button-avaliacao"
							id="salvar-avaliacao-fisica">
							SALVAR
						</div>
					</div>
					<div class="content-body">
						<!-- ANAMNESE -->
						<ng-container>
							<div class="row">
								<div class="col-md-6">
									<pacto-datepicker
										[control]="avaliacaoFormGroup.get('dataAvaliacao')"
										i18n-label="@@catalogo-alunos:data-avaliacao:label"
										label="Data da Avaliação"></pacto-datepicker>
								</div>
							</div>

							<div class="row">
								<div class="col-md-6">
									<pacto-select
										*ngIf="!avaliacaoAtual || !avaliacaoAtual.id"
										[control]="anamneseFormControl"
										[opcoes]="anamnesesAtivas"
										i18n-label="@@catalogo-alunos:anamnese-selecionado:label"
										label="Anamnese Selecionada"></pacto-select>
									<div *ngIf="avaliacaoAtual?.id" class="type-h5">
										{{ anamneseSelecionada?.nome }}
									</div>
								</div>
							</div>

							<pacto-cat-anamnese-input
								[anamnese]="anamneseSelecionada"
								[formControl]="
									avaliacaoFormGroup.get('anamneseRespostas')
								"></pacto-cat-anamnese-input>

							<div class="av-movimento-3d">
								<div class="cabecalho-av-movimento-3d">
									<div class="titulo box-title">Avaliação De Movimento 3D</div>
									<div class="titulo margin-vertical-16">
										Objetivo em analisar mobilidade, estabilidade e controle do
										movimento identificando a funcionalidade das articulações e
										desequilíbrios nas cadeias. Foco na prevenção de dores.
									</div>
									<div>
										PLACAR 0 - Apresenta dor para executar o movimento, consegue
										executar com dor ou não consegue executar. Dor em qualquer
										articulação da cadeia representa um placar 0.
									</div>
									<div>
										PLACAR 1 - Executa o movimento com dificuldade, não consegue
										chegar em todo os padrões estabelecidos ou apresenta falta
										de controle do movimento.
									</div>
									<div>
										PLACAR 2 - Executa o movimento com boa amplitude e controle.
									</div>
									<div>
										PLACAR 3 - Executa o movimento com excelência de controle e
										passa as amplitudes estabelecidas.
									</div>
								</div>

								<div class="row">
									<div class="col-md-6">
										<div class="box-av-movimento">
											<div class="row title">
												<div class="col-md-6">Mobilidade</div>
												<div class="col-md-3">Esq.</div>
												<div class="col-md-3">Dir.</div>
											</div>
											<div class="row border-bottom-line">
												<div class="col-md-6">Cadeia anterior</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cAnteriorEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cAnteriorDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>

											<div class="row border-bottom-line">
												<div class="col-md-6">Cadeia posterior</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cPosteriorEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cPosteriorDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>

											<div class="row border-bottom-line">
												<div class="col-md-6">Cadeia lateral</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cLateralEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cLateralDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>

											<div class="row no-border-bottom-line">
												<div class="col-md-6">Cadeia rotacional</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cRotacionalEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarMobilidade()"
														[control]="fgPlacarMobilidade.get('cRotacionalDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>
										</div>
									</div>

									<div class="col-md-6">
										<div class="box-av-movimento">
											<div class="row title">
												<div class="col-md-6">Estabilidade e controle</div>
												<div class="col-md-3">Esq.</div>
												<div class="col-md-3">Dir.</div>
											</div>
											<div class="row border-bottom-line">
												<div class="col-md-6">Controle</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarEstCont()"
														[control]="fgPlacarEstContr.get('controleEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarEstCont()"
														[control]="fgPlacarEstContr.get('controleDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>
											<div class="row border-bottom-line">
												<div class="col-md-6">Cadeia de fechamento</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarEstCont()"
														[control]="fgPlacarEstContr.get('cFechamentoEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarEstCont()"
														[control]="fgPlacarEstContr.get('cFechamentoDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>
											<div class="row border-bottom-line">
												<div class="col-md-6">Cadeia de abertura</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarEstCont()"
														[control]="fgPlacarEstContr.get('cAberturaEsq')"
														[opcoes]="placar"></pacto-select>
												</div>
												<div class="col-md-3 placar">
													<pacto-select
														(change)="updateSomaMediaPlacarEstCont()"
														[control]="fgPlacarEstContr.get('cAberturaDir')"
														[opcoes]="placar"></pacto-select>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div class="row">
									<div class="col-md-6">
										<div class="box-soma-media">
											<div class="row border-bottom-line background-soma-media">
												<div class="col-md-6">Placar (soma)</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarSomaMobEsq }}
												</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarSomaMobDir }}
												</div>
											</div>
											<div class="row border-bottom-line background-soma-media">
												<div class="col-md-6">Placar (média)</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarMediaMobEsq }}
												</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarMediaMobDir }}
												</div>
											</div>
										</div>
									</div>

									<div class="col-md-6">
										<div class="box-soma-media">
											<div class="row border-bottom-line background-soma-media">
												<div class="col-md-6">Placar (soma)</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarSomaEstConEsq }}
												</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarSomaEstConDir }}
												</div>
											</div>
											<div class="row border-bottom-line background-soma-media">
												<div class="col-md-6">Placar (média)</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarMediaEstConEsq }}
												</div>
												<div class="col-md-3 hidden-text-overplus">
													{{ placarMediaEstConDir }}
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</ng-container>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@avaliacoes:avaliacao-aluno:anamnese" xingling="ANAMNESE">
		Anamnese
	</span>
	<span xingling="VALIDACAOSEXO">
		Data de nascimento e sexo são obrigatórios
	</span>
	<span xingling="VALIDACAOASTRAND">
		Ops! Para o cálculo do teste de astrand é necessário informar o peso do
		aluno
	</span>
	<span xingling="MENSSAGEM_SUCESSO">
		Avaliação integrada salva com sucesso.
	</span>
	<span xingling="MENSSAGEM_TEMPO">
		O tempo informado na aba vo2max deve ser preenchido de acordo com a
		formatação.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:VALIDAR_PRODUTO"
		xingling="VALIDAR_PRODUTO">
		O aluno não tem um produto de Avaliação Física vigente pago.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:PRODUTO_ULTILIZADO"
		xingling="PRODUTO_ULTILIZADO">
		O produto vigente já foi usado.
	</span>
	<span xingling="VALIDAR_ANAMNESE">
		É necessário selecionar uma anamnese para prosseguir.
	</span>

	<span #popupErro [hidden]="true">
		Verifique se o seu Pop-ups nao esta bloqueado e tente novamente.
	</span>
	<span #imprimirErro [hidden]="true">
		Não foi possível imprimir esta avaliação.
	</span>
</pacto-traducoes-xingling>
