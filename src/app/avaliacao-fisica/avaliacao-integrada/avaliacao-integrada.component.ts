import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AlunoBase,
	Anamnese,
	AnamnesePergunta,
	AnamnesePerguntaResposta,
	AvaliacaoFisica,
	AvaliacaoIntegrada,
	PerfilAcessoRecursoNome,
	TreinoApiAlunosService,
	TreinoApiAnamneseService,
	TreinoApiAvaliacaoCatalogoService,
	TreinoApiAvaliacaoFisicaService,
} from "treino-api";
import { buildAvaliacaoForm } from "../avaliacoes-aluno/avaliacao-form-group";
import { FormControl, FormGroup } from "@angular/forms";
import { AnamnesePerguntaDto } from "@base-shared/cat-anamnese-pergunta-input/cat-anamnese-pergunta-input.component";
import { Observable, Subscription, zip } from "rxjs";
import { map } from "rxjs/operators";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { ModalService } from "@base-core/modal/modal.service";
import { TreinoConfigCacheService } from "../../base/configuracoes/configuration.service";
import { SessionService } from "@base-core/client/session.service";
import { TraducoesXinglingComponent } from "../../../../projects/ui/src/lib/components/traducoes-xingling/traducoes-xingling.component";
import { RecursoSistema } from "../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { defaultAvaliacaoDto } from "../avaliacoes-aluno/avaliacao-form-group-default";
import { AlunoSexo } from "../../../../projects/treino-api/src/lib/aluno.model";

@Component({
	selector: "pacto-avaliacao-integrada",
	templateUrl: "./avaliacao-integrada.component.html",
	styleUrls: ["./avaliacao-integrada.component.scss"],
})
export class AvaliacaoIntegradaComponent implements OnInit, AfterViewInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	aluno: AlunoBase;
	avaliacaoFormGroup: FormGroup;
	anamneseFormControl: FormControl = new FormControl();
	anamneses: Anamnese[];
	anamnesesAtivas: Anamnese[];
	anamneseSelecionada: Anamnese;
	mostrarTopo = false;
	criando = true;
	changes = false;
	avaliacaoOnChangeSubscription: Subscription;
	integracaoZW = false;
	produtoAvaliacao: any;
	isProdutoValido: any;
	avaliacaoAtual: AvaliacaoIntegrada;
	alunoAvaliacoes: AvaliacaoFisica[];
	loading = false;
	placar = [
		{ id: 0, nome: 0 },
		{ id: 1, nome: 1 },
		{ id: 2, nome: 2 },
		{ id: 3, nome: 3 },
	];
	placarSomaMobEsq = 0;
	placarSomaMobDir = 0;
	placarMediaMobEsq = 0;
	placarMediaMobDir = 0;
	placarSomaEstConEsq = 0;
	placarSomaEstConDir = 0;
	placarMediaEstConEsq = 0;
	placarMediaEstConDir = 0;
	fgPlacarMobilidade = new FormGroup({
		cAnteriorEsq: new FormControl(0),
		cAnteriorDir: new FormControl(0),
		cPosteriorEsq: new FormControl(0),
		cPosteriorDir: new FormControl(0),
		cLateralEsq: new FormControl(0),
		cLateralDir: new FormControl(0),
		cRotacionalEsq: new FormControl(0),
		cRotacionalDir: new FormControl(0),
	});
	fgPlacarEstContr = new FormGroup({
		controleEsq: new FormControl(0),
		controleDir: new FormControl(0),
		cFechamentoEsq: new FormControl(0),
		cFechamentoDir: new FormControl(0),
		cAberturaEsq: new FormControl(0),
		cAberturaDir: new FormControl(0),
	});

	// objetivos: AvaliacaoObjetivo[] = [];

	constructor(
		private router: Router,
		private snotify: SnotifyService,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private alunoService: TreinoApiAlunosService,
		private modal: ModalService,
		private anamneseService: TreinoApiAnamneseService,
		private treinoConfigService: TreinoConfigCacheService,
		private avaliacaoFisicaService: TreinoApiAvaliacaoFisicaService,
		private avaliacaoCatalogoService: TreinoApiAvaliacaoCatalogoService,
		private catalogoService: TreinoApiAvaliacaoCatalogoService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
	}

	ngAfterViewInit() {
		this.avaliacaoFormGroup = buildAvaliacaoForm();
		this.setupOnAnamneseChange();
		// this.prepareAvaliacaoSections();
		this.loadInitialData().subscribe(() => {
			this.mostrarTopo = true;
			this.cd.detectChanges();
			this.route.params.subscribe((params) => {
				this.handleRoute(params);
			});
		});
	}

	private setupOnAnamneseChange() {
		this.anamneseFormControl.valueChanges.subscribe((value) => {
			if (value) {
				const found = this.anamneses.find((anamnese) => {
					return anamnese.id === parseInt(value, 10);
				});
				this.anamneseSelecionada = found;

				this.avaliacaoFormGroup
					.get("anamneseRespostas")
					.setValue(
						this.createEmptyAnamneseRespostas(
							this.anamneseSelecionada.perguntas
						)
					);
			}
		});
	}

	private createEmptyAnamneseRespostas(
		perguntas: AnamnesePergunta[]
	): AnamnesePerguntaDto[] {
		const result = [];
		perguntas.forEach((pergunta) => {
			result.push({
				anamnesePerguntaId: pergunta.id,
				resposta: null,
				observacao: null,
			});
		});
		return result;
	}

	private loadInitialData(): Observable<any> {
		const alunoId = this.route.snapshot.params.alunoId;
		const aluno$ = this.alunoService.obterAluno(alunoId);
		const anamneses$ = this.anamneseService.obterTodasAnamnesesIntegradas();
		return zip(aluno$, anamneses$).pipe(
			map((resultado) => {
				this.aluno = resultado[0];
				this.anamneses = resultado[1];
				this.anamnesesAtivas = this.getAnamnesesAtivas(resultado[1]);
				return true;
			})
		);
	}

	private getAnamnesesAtivas(anamneses) {
		const listAnamneses = [];
		anamneses.forEach((item) => {
			if (item.ativa) {
				listAnamneses.push(item);
			}
		});
		return listAnamneses;
	}

	private handleRoute(params) {
		const avaliacaoId = params.avaliacaoId;
		const novo = avaliacaoId === "novo";
		this.criando = novo;
		this.changes = false;

		if (this.avaliacaoOnChangeSubscription) {
			this.avaliacaoOnChangeSubscription.unsubscribe();
		}

		if (novo) {
			this.validarPermissaoProduto();
			this.routeHandleNew();
		} else if (!isNaN(parseInt(avaliacaoId, 10))) {
			this.routeHandleSelected(avaliacaoId);
		} else {
			this.routeHandleUnselected();
		}
	}

	validarPermissaoProduto() {
		this.alunoService.obterAluno(this.aluno.id).subscribe((alunoResponse) => {
			this.aluno = alunoResponse;
			if (this.validarProduto && this.integracaoZW) {
				this.produtoAvaliacao = JSON.parse(
					this.treinoConfigService.configuracoesAvaliacao.produto_avaliacao
				);
				this.catalogoService
					.isProdutoVigente(this.aluno.id, this.produtoAvaliacao)
					.subscribe((result) => {
						this.isProdutoValido = result;
						const stringValue = result;
						this.isProdutoValido = /true/i.test(stringValue);
						if (!this.isProdutoValido) {
							if (stringValue.includes("usado.")) {
								this.snotify.error(
									this.traducoes.getLabel("PRODUTO_ULTILIZADO")
								);
								this.router.navigate([
									"/cadastros",
									"alunos",
									"perfil",
									this.aluno.id,
								]);
							} else {
								this.snotify.error(this.traducoes.getLabel("VALIDAR_PRODUTO"));
								this.router.navigate([
									"/cadastros",
									"alunos",
									"perfil",
									this.aluno.id,
								]);
							}
						}
					});
			}
		});
	}

	get validarProduto() {
		return this.treinoConfigService.configuracoesAvaliacao
			.validar_produto_avaliacao;
	}

	private routeHandleNew() {
		this.avaliacaoFormGroup = buildAvaliacaoForm();
		const cleanDto = new AvaliacaoFisica();
		cleanDto.dataAvaliacao = new Date().valueOf();
		this.avaliacaoAtual = cleanDto;

		// this.fillOutAvaliacaoForm(cleanDto);
		this.cd.detectChanges();
	}

	private routeHandleSelected(avaliacaoId) {
		this.loadAvaliacao(avaliacaoId).subscribe((avaliacao) => {
			this.fillOutAvaliacaoForm(avaliacao);
		});
		setTimeout(() => {
			this.setupOnAvaliacaoChange();
		});
		this.cd.detectChanges();
	}

	private setupOnAvaliacaoChange() {
		if (this.avaliacaoOnChangeSubscription) {
			this.avaliacaoOnChangeSubscription.unsubscribe();
		}
		this.avaliacaoOnChangeSubscription =
			this.avaliacaoFormGroup.valueChanges.subscribe((v) => {
				this.changes = true;
				this.cd.detectChanges();
			});
	}

	private loadAvaliacao(avaliacaoId): Observable<any> {
		return this.avaliacaoFisicaService
			.obterAvaliacaoIntegrada(avaliacaoId)
			.pipe(
				map((avaliacao) => {
					this.avaliacaoAtual = avaliacao;
					return avaliacao;
				})
			);
	}

	private routeHandleUnselected() {
		const hasOne = this.alunoAvaliacoes && this.alunoAvaliacoes.length;
		if (hasOne) {
			const id = this.alunoAvaliacoes[0].id;
			this.routeHandleSelected(id);
		} else {
			this.avaliacaoAtual = null;
		}
	}

	salvarHandler() {
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if (!this.anamneseSelecionada) {
			this.snotify.warning(this.traducoes.getLabel("VALIDAR_ANAMNESE"));
		}
		if (
			this.aluno.sexo !== AlunoSexo.MASCULINO &&
			this.aluno.sexo !== AlunoSexo.FEMININO &&
			!this.aluno.dataNascimento
		) {
			this.snotify.warning(this.traducoes.getLabel("VALIDACAOSEXO"));
		} else {
			const adjustedDto = this.getAdjustedDto();
			if (this.isFormValid(adjustedDto)) {
				this.changes = false;
				if (!this.avaliacaoAtual.id) {
					if (permissao && permissao.incluir) {
						this.criarNovaHandler();
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.CRIOU_AVALIACAO_NTO
						);
					} else {
						this.snotify.warning(
							"Seu usuário não possui permissão, procure seu administrador"
						);
					}
				} else {
					if (permissao && permissao.editar) {
						this.atualizarHandler();
					} else {
						this.snotify.warning(
							"Seu usuário não possui permissão, procure seu administrador"
						);
					}
				}
			}
		}
	}

	private atualizarHandler() {
		const adjustedDto = this.getAdjustedDto();
		adjustedDto.id = this.avaliacaoAtual.id;
		this.loading = true;
		this.avaliacaoFisicaService
			.saveOrUpdateAvaliacaoIntegrada(this.aluno.id, adjustedDto)
			.subscribe((update) => {
				this.loading = false;
				this.snotify.success(this.traducoes.getLabel("MENSSAGEM_SUCESSO"));
				this.routeHandleSelected(update);
			});
	}

	private criarNovaHandler() {
		const adjustedDto = this.getAdjustedDto();
		this.loading = true;
		this.avaliacaoFisicaService
			.saveOrUpdateAvaliacaoIntegrada(this.aluno.id, adjustedDto)
			.subscribe((idAvIntegrada) => {
				this.loading = false;
				this.snotify.success(this.traducoes.getLabel("MENSSAGEM_SUCESSO"));
				this.router.navigate([
					"avaliacao",
					"avaliacao-integrada-aluno",
					this.aluno.id,
					"avaliacao",
					idAvIntegrada,
				]);
				this.cd.detectChanges();
			});
	}

	private isFormValid(dto) {
		if (!dto.dataAvaliacao) {
			this.snotify.error("Defina uma data");
			return false;
		}
		return true;
	}

	private getAdjustedDto() {
		const dto = this.avaliacaoFormGroup.getRawValue();
		dto.anamneseRespostas.forEach((result, index) => {
			if (typeof result === "object" && result.resposta) {
				dto.anamneseRespostas[index].resposta = result.resposta.toString();
			}
		});
		dto.anamneseSelecionadaId = parseInt(this.anamneseFormControl.value, 10);
		const avIntegradaDto: any = {};
		avIntegradaDto.id = null;
		avIntegradaDto.anamneseSelecionadaId = dto.anamneseSelecionadaId;
		avIntegradaDto.anamneseRespostas = dto.anamneseRespostas;
		avIntegradaDto.dataAvaliacao = dto.dataAvaliacao;
		avIntegradaDto.mobilidade = this.getAdjustedMobilidade3D();
		avIntegradaDto.estabilidade = this.getAdjustedEstabilidade3D();
		avIntegradaDto.somaMobilidadeDir = this.placarSomaMobDir;
		avIntegradaDto.somaEstabilidadeDir = this.placarSomaEstConDir;
		avIntegradaDto.mediaMobilidadeDir = this.placarMediaMobDir;
		avIntegradaDto.mediaEstabilidadeDir = this.placarMediaEstConDir;
		avIntegradaDto.somaMobilidadeEsq = this.placarSomaMobEsq;
		avIntegradaDto.somaEstabilidadeEsq = this.placarSomaEstConEsq;
		avIntegradaDto.mediaMobilidadeEsq = this.placarMediaMobEsq;
		avIntegradaDto.mediaEstabilidadeEsq = this.placarMediaEstConEsq;
		return avIntegradaDto;
	}

	private getAdjustedMobilidade3D() {
		const dto = this.fgPlacarMobilidade.getRawValue();
		return [
			{
				codigo: null,
				item: null,
				movimento: 0,
				esquerda: Number(dto.cAnteriorEsq),
				direita: Number(dto.cAnteriorDir),
			},
			{
				codigo: null,
				item: null,
				movimento: 1,
				esquerda: Number(dto.cPosteriorEsq),
				direita: Number(dto.cPosteriorDir),
			},
			{
				codigo: null,
				item: null,
				movimento: 2,
				esquerda: Number(dto.cLateralEsq),
				direita: Number(dto.cLateralDir),
			},
			{
				codigo: null,
				item: null,
				movimento: 3,
				esquerda: Number(dto.cRotacionalEsq),
				direita: Number(dto.cRotacionalDir),
			},
		];
	}

	private getAdjustedEstabilidade3D() {
		const dto = this.fgPlacarEstContr.getRawValue();
		return [
			{
				codigo: null,
				item: null,
				movimento: 4,
				esquerda: Number(dto.controleEsq),
				direita: Number(dto.controleDir),
			},
			{
				codigo: null,
				item: null,
				movimento: 5,
				esquerda: Number(dto.cFechamentoEsq),
				direita: Number(dto.cFechamentoDir),
			},
			{
				codigo: null,
				item: null,
				movimento: 6,
				esquerda: Number(dto.cAberturaEsq),
				direita: Number(dto.cAberturaDir),
			},
		];
	}

	cancelarHandler() {
		if (this.avaliacaoOnChangeSubscription) {
			this.avaliacaoOnChangeSubscription.unsubscribe();
		}
		this.changes = false;
		this.router.navigate(["cadastros", "alunos", "perfil", this.aluno.id]);
	}

	updateSomaMediaPlacarMobilidade() {
		this.placarSomaMobEsq =
			Number(this.fgPlacarMobilidade.get("cAnteriorEsq").value) +
			Number(this.fgPlacarMobilidade.get("cPosteriorEsq").value) +
			Number(this.fgPlacarMobilidade.get("cLateralEsq").value) +
			Number(this.fgPlacarMobilidade.get("cRotacionalEsq").value);

		this.placarSomaMobDir =
			Number(this.fgPlacarMobilidade.get("cAnteriorDir").value) +
			Number(this.fgPlacarMobilidade.get("cPosteriorDir").value) +
			Number(this.fgPlacarMobilidade.get("cLateralDir").value) +
			Number(this.fgPlacarMobilidade.get("cRotacionalDir").value);

		this.placarMediaMobEsq = this.placarSomaMobEsq / 4;
		this.placarMediaMobDir = this.placarSomaMobDir / 4;
		this.cd.detectChanges();
	}

	updateSomaMediaPlacarEstCont() {
		this.placarSomaEstConEsq =
			Number(this.fgPlacarEstContr.get("controleEsq").value) +
			Number(this.fgPlacarEstContr.get("cFechamentoEsq").value) +
			Number(this.fgPlacarEstContr.get("cAberturaEsq").value);

		this.placarSomaEstConDir =
			Number(this.fgPlacarEstContr.get("controleDir").value) +
			Number(this.fgPlacarEstContr.get("cFechamentoDir").value) +
			Number(this.fgPlacarEstContr.get("cAberturaDir").value);

		this.placarMediaEstConEsq = this.placarSomaEstConEsq / 3;
		this.placarMediaEstConDir = this.placarSomaEstConDir / 3;
		this.cd.detectChanges();
	}

	private fillOutAvaliacaoForm(avaliacao: AvaliacaoIntegrada) {
		const ops = {};
		const fg = this.avaliacaoFormGroup;

		/**
		 * Resetar para valores padrões.
		 */
		fg.setValue(defaultAvaliacaoDto, ops);

		fg.get("dataAvaliacao").setValue(avaliacao.dataLancamento);

		/**
		 * Anamnese
		 */
		if (avaliacao.id && avaliacao.anamneseSelecionada) {
			const anamneseId = avaliacao.anamneseSelecionada.id;
			const anmnese = this.anamneses.find((item) => {
				return item.id === anamneseId;
			});
			this.anamneseFormControl.setValue(anamneseId);
			this.anamneseSelecionada = anmnese;
			fg.get("anamneseRespostas").setValue(
				this.convertPerguntaToPerguntaDto(avaliacao.anamneseRespostas)
			);
		} else if (this.anamneses && this.anamneses.length) {
			this.anamneseFormControl.setValue(this.anamneses[0].id);
			this.anamneseSelecionada = this.anamneses[0];
			fg.get("anamneseRespostas").setValue(
				this.createEmptyAnamneseRespostas(this.anamneseSelecionada.perguntas),
				ops
			);
		}

		if (avaliacao.mobilidade) {
			avaliacao.mobilidade.forEach((mobilidade) => {
				switch (mobilidade.movimento) {
					case 0:
						this.fgPlacarMobilidade
							.get("cAnteriorEsq")
							.setValue(mobilidade.esquerda);
						this.fgPlacarMobilidade
							.get("cAnteriorDir")
							.setValue(mobilidade.direita);
						break;
					case 1:
						this.fgPlacarMobilidade
							.get("cPosteriorEsq")
							.setValue(mobilidade.esquerda);
						this.fgPlacarMobilidade
							.get("cPosteriorDir")
							.setValue(mobilidade.direita);
						break;
					case 2:
						this.fgPlacarMobilidade
							.get("cLateralEsq")
							.setValue(mobilidade.esquerda);
						this.fgPlacarMobilidade
							.get("cLateralDir")
							.setValue(mobilidade.direita);
						break;
					case 3:
						this.fgPlacarMobilidade
							.get("cRotacionalEsq")
							.setValue(mobilidade.esquerda);
						this.fgPlacarMobilidade
							.get("cRotacionalDir")
							.setValue(mobilidade.direita);
						break;
				}
			});
		}

		if (avaliacao.estabilidade) {
			avaliacao.estabilidade.forEach((estabilidade) => {
				switch (estabilidade.movimento) {
					case 4:
						this.fgPlacarEstContr
							.get("controleEsq")
							.setValue(estabilidade.esquerda);
						this.fgPlacarEstContr
							.get("controleDir")
							.setValue(estabilidade.direita);
						break;
					case 5:
						this.fgPlacarEstContr
							.get("cFechamentoEsq")
							.setValue(estabilidade.esquerda);
						this.fgPlacarEstContr
							.get("cFechamentoDir")
							.setValue(estabilidade.direita);
						break;
					case 6:
						this.fgPlacarEstContr
							.get("cAberturaEsq")
							.setValue(estabilidade.esquerda);
						this.fgPlacarEstContr
							.get("cAberturaDir")
							.setValue(estabilidade.direita);
						break;
				}
			});
		}

		this.placarSomaMobEsq = avaliacao.somaMobilidadeEsq;
		this.placarSomaMobDir = avaliacao.somaMobilidadeDir;
		this.placarMediaMobEsq = avaliacao.mediaMobilidadeEsq;
		this.placarMediaMobDir = avaliacao.mediaMobilidadeDir;
		this.placarSomaEstConEsq = avaliacao.somaEstabilidadeEsq;
		this.placarSomaEstConDir = avaliacao.somaEstabilidadeDir;
		this.placarMediaEstConEsq = avaliacao.mediaEstabilidadeEsq;
		this.placarMediaEstConDir = avaliacao.mediaEstabilidadeDir;
		this.cd.detectChanges();
	}

	private convertPerguntaToPerguntaDto(
		perguntas: AnamnesePerguntaResposta[]
	): AnamnesePerguntaDto[] {
		const result = [];
		perguntas.forEach((pergunta) => {
			result.push({
				anamnesePerguntaId: pergunta.perguntaAnamneseId,
				resposta: pergunta.resposta,
				observacao: pergunta.observacao,
			});
		});
		return result;
	}
}
