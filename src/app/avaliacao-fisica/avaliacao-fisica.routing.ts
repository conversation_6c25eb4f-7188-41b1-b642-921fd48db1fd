import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";
import { AvaliacoesAlunoComponent } from "./avaliacoes-aluno/avaliacoes-aluno.component";
import { AvaliacaoEvolucaov2Component } from "./avaliacao-evolucao/components/avaliacao-evolucaov2.component";
import { ModuleName } from "@base-core/modulo/modulo.model";
import { AvaliacaoIntegradaComponent } from "./avaliacao-integrada/avaliacao-integrada.component";
import { HomeComponent } from "./home/<USER>";

const routes: Routes = [
	{
		path: "",
		redirectTo: "home",
		data: { module: ModuleName.AVALIACAO },
	},
	{
		path: "home",
		component: HomeComponent,
		data: { module: ModuleName.AVALIACAO },
	},
	{
		path: "anamneses",
		loadChildren: () =>
			import("./anamnese/anamnese.module").then((m) => m.AnamneseModule),
		data: { module: ModuleName.AVALIACAO },
	},
	{
		path: "bi",
		loadChildren: () =>
			import("./avaliacao-fisica-bi/bi.module").then((m) => m.BiModule),
		data: { module: ModuleName.AVALIACAO },
	},
	{
		path: "cadastros/objetivos",
		loadChildren: () =>
			import("./objetivo/objetivo.module").then((m) => m.ObjetivoModule),
		data: { module: ModuleName.AVALIACAO },
	},
	{
		path: "avaliacao-evolucao/:id",
		component: AvaliacaoEvolucaov2Component,
	},
	{
		path: "avaliacoes-aluno/:alunoId",
		children: [
			{
				path: "avaliacao/:avaliacaoId",
				component: AvaliacoesAlunoComponent,
			},
			{
				path: "avaliacao",
				redirectTo: "avaliacao/",
			},
		],
	},
	{
		path: "avaliacao-integrada-aluno/:alunoId",
		children: [
			{
				path: "avaliacao/:avaliacaoId",
				component: AvaliacaoIntegradaComponent,
			},
			{
				path: "avaliacao",
				redirectTo: "avaliacao/",
			},
		],
	},
];

@NgModule({
	imports: [CommonModule, RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class AvaliacaoFisicaRoutingModule {}
