@import "src/assets/scss/pacto/plataforma-import.scss";

.perfil-wrapper {
	position: relative;
	background-color: $cinzaClaroPri;

	.top-aux {
		width: 100%;
		background-color: $branco;
		justify-content: center;
		display: flex;

		pacto-avaliacoes-aluno-top {
			@include plataformaV2LarguraConteudo();
		}
	}

	.body-width {
		display: flex;
		flex-direction: column;
		align-items: center;

		> * {
			@include plataformaV2LarguraConteudo();
		}
	}

	.content-tab {
		margin: 30px 0px;
		background-color: $cinzaClaroPri;
		color: $pretoPri;
	}

	.content-aux {
		background-color: $cinzaClaroPri;
		color: $pretoPri;
		display: flex;
		align-items: center;
		flex-direction: column;
		padding-bottom: 130px;
	}

	.avaliacao-nome {
		margin-bottom: 30px;
		text-align: left;
	}

	.content-card {
		background-color: $branco;
		box-shadow: 2px 5px 8px 2px #dadbe1;
		width: 100%;
		margin-bottom: 30px;
		border-radius: 10px;

		.content-title {
			display: flex;
			align-items: center;
			box-shadow: 0 1px 4px 0 #e5e9f2;
			background-color: #00426b;
			padding: 11px;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
		}

		pacto-cat-select {
			flex-basis: 200px;
		}

		.categoria {
			flex-grow: 1;
			color: $branco;
			font-size: 16px;
			font-weight: 600;
		}

		.content-body {
			padding: 10px 30px 30px 30px;
		}
	}
}

.block-resultado {
	text-align: center;

	.label-resultado {
		font-size: 14px;
	}

	.info-resultado {
		font-weight: 600;
		font-size: 22px;
	}
}

.loading-blur {
	filter: blur(2px);
	pointer-events: none;
}

.carregando-dados {
	top: 45%;
	left: 45%;
}

.button-avaliacao {
	font-size: 12px;
	font-family: "Nunito Sans", sans-serif;
	font-weight: 700;
	color: $branco;
	cursor: pointer;
	padding-left: 20px;
}
