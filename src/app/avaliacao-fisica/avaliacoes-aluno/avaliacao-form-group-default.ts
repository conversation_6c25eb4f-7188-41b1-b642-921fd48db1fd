import {
	AvaliacaoDobraProtocolo,
	AnamneseAssimetriaOmbro,
	AnamneseAssimetriaQuadril,
	Vo2MaxProtocolo,
} from "treino-api";

export const defaultAvaliacaoDto = {
	dataAvaliacao: null,
	dataProxima: null,
	objetivos: [],
	pesoAltura: {
		peso: null,
		altura: null,
		pressaoArterial: null,
		pressaoDiastolica: null,
		pressaoSistolica: null,
		frequencia: null,
		frequenciaMax: null,
	},
	dobras: {
		logBalanca: null,
		protocolo: AvaliacaoDobraProtocolo.POLLOCK_3_DOBRAS,
		abdominal: null,
		peitoral: null,
		coxaMedial: null,
		subescapular: null,
		supraEspinhal: null,
		supraIliaca: null,
		triceps: null,
		biceps: null,
		axilarMedia: null,
		panturrilha: null,
		bioimpedancia: {
			imc: null,
			massaGorda: null,
			percentMassaMagra: null,
			residuos: null,
			gorduraIdeal: null,
			reatancia: null,
			necFisica: null,
			necCalorica: null,
			gorduraVisceral: null,
			percentMassaGorda: null,
			massaMagra: null,
			ossos: null,
			musculos: null,
			resistencia: null,
			percentAgua: null,
			tmb: null,
			idadeMetabolica: null,
		},
	},
	perimetria: {
		pescoco: null,
		ombro: null,
		toraxBustoRelaxado: null,
		bracoRelaxadoEsq: null,
		bracoRelaxadoDir: null,
		bracoContraidoEsq: null,
		bracoContraidoDir: null,
		antebracoEsq: null,
		antebracoDir: null,
		cintura: null,
		circunferenciaAbdominal: null,
		quadril: null,
		coxaProximalEsq: null,
		coxaProximalDir: null,
		coxaMediaEsq: null,
		coxaMediaDir: null,
		coxaDistalEsq: null,
		coxaDistalDir: null,
		panturrilhaEsq: null,
		panturrilhaDir: null,
		diametroPunho: null,
		diametroJoelho: null,
		diametroCotovelo: null,
		diametroTornozelo: null,
	},
	flexibilidade: {
		alcanceMaximo: null,
		observacao: null,
		observacaoOmbro: null,
		observacaoJoelho: null,
		observacaoQuadril: null,
		observacaoTornozelo: null,
		mobilidadeOmbroEsquerdo: null,
		mobilidadeOmbroDireito: null,
		mobilidadeQuadrilEsquerdo: null,
		mobilidadeQuadrilDireito: null,
		mobilidadeJoelhoEsquerdo: null,
		mobilidadeJoelhoDireito: null,
		mobilidadeTornozeloEsquerdo: null,
		mobilidadeTornozeloDireito: null,
	},
	postura: {
		frenteImageId: null,
		direitaImageId: null,
		esquerdaImageId: null,
		costasImageId: null,
		frenteImageUpload: null,
		direitaImageUpload: null,
		esquerdaImageUpload: null,
		costasImageUpload: null,
		visaoLateral: {
			anterversaoQuadril: false,
			hipercifoseToracica: false,
			hiperlordoseCervical: false,
			hiperlordoseLombar: false,
			joelhoFlexo: false,
			joelhoRecurvado: false,
			protusaoAbdominal: false,
			peCalcaneo: false,
			peCavo: false,
			peEquino: false,
			pePlano: false,
			retificacaoCervical: false,
			retificacaoLombar: false,
			retroversaoQuadril: false,
			rotacaoInternaOmbros: false,
		},
		visaoPosterior: {
			depressaoEscapular: false,
			encurtamentoTrapezio: false,
			escolioseCervical: false,
			escolioseLombar: false,
			escolioseToracica: false,
			protacaoEscapular: false,
			peValgo: false,
			peVaro: false,
			retracaoEscapular: false,
		},
		visaoAnterior: {
			joelhoValgo: false,
			joelhoVaro: false,
			peAbduto: false,
			peAduto: false,
		},
		assimetrias: {
			ombrosAssimetricos: AnamneseAssimetriaOmbro.NENHUMA_ELEVACAO,
			assimetriaQuadril: AnamneseAssimetriaQuadril.NENHUMA_ELEVACAO,
		},
		observacao: "",
	},
	rml: {
		flexoesBracos: null,
		abdominais: null,
	},
	vo2: {
		protocolo: Vo2MaxProtocolo.VO_CAMINHADA_CORRIDA_12_MINUTOS,
		vo2Max: 0,
		limiarVentilatorioI: 0,
		limiarVentilatorioII: 0,
		caminhadaCorridaDistancia: null,
		teste2400MetrosTempo: null,
		aerobicoBancoFrequencia: null,
		astrandFrequencia4: null,
		astrandFrequencia5: null,
		collegeFrequencia: null,
		astrandCarga: null,
	},
	somatotipia: {
		dobraTriceps: null,
		dobraSupraEspinhal: null,
		dobraSubescapular: null,
		dobraPanturrilha: null,
		perimetroPanturrilhaDireita: null,
		perimetroBracoContraidoDireito: null,
		diametroPunho: null,
		diametroJoelho: null,
		diametroCotovelo: null,
		diametroTornozelo: null,
	},
	metaRecomendacoes: {
		percentualGorduraProxima: null,
		observacoesAvaliador: null,
	},
	anamneseRespostas: [],
	parQRespostas: [],
};
