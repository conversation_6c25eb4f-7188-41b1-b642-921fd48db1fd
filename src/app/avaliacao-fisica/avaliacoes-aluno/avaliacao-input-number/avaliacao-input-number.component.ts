import { Component, OnInit, Input } from "@angular/core";
import { FormControl } from "@angular/forms";
import { LocalizationService } from "@base-core/localization/localization.service";
import createNumberMask from "text-mask-addons/dist/createNumberMask";

@Component({
	selector: "pacto-avaliacao-input-number",
	templateUrl: "./avaliacao-input-number.component.html",
	styleUrls: ["./avaliacao-input-number.component.scss"],
})
export class AvaliacaoInputNumberComponent implements OnInit {
	@Input() decimal = true;
	@Input() control: FormControl;
	@Input() largeInput = false;
	@Input() id: string;
	textMask = { mask: false, guide: false };

	constructor(private localization: LocalizationService) {}

	ngOnInit() {
		if (!this.control) {
			throw new Error("Missing input: control.");
		}
		this.control.markAsPristine();
		this.configureMask();
		this.control.valueChanges.subscribe((value: string) => {
			this.process(value);
		});
		setTimeout(() => {
			if (this.control.value) {
				this.process(this.control.value);
			}
		});
	}

	selectLine($event) {
		$event.target.select();
	}

	private process(value) {
		if (this.control.pristine) {
			this.processInitialDataSetup(value);
			this.control.setValue(parseFloat(value), {
				emitEvent: false,
				emitModelToViewChange: false,
			});
		} else {
			this.processView(value);
		}
	}

	private processInitialDataSetup(value) {
		const brazil = this.localization.getLocaleDecimalSymbol() === ",";
		if (brazil) {
			const valueString = `${value}`;
			const newValue = valueString.replace(".", ",");
			this.control.setValue(newValue, {
				emitEvent: false,
				emitViewToModelChange: false,
			});
		}
	}

	private processView(value) {
		value = `${value}`;
		const thousand = this.localization.getLocalethousandsSeparatorSymbol();
		let removeRegex;

		if (thousand === ".") {
			removeRegex = /\./;
		} else {
			removeRegex = new RegExp(`${thousand}`, "g");
		}
		let clean = value.replace(removeRegex, "");
		/**
		 * In case there is a ',' leftover replace with
		 * period '.'
		 */
		clean = clean.replace(/,/, ".");
		const finalValue = clean ? parseFloat(clean) : null;

		this.control.setValue(finalValue, {
			emitEvent: false,
			emitModelToViewChange: false,
		});
	}

	private configureMask() {
		const thousands = this.localization.getLocalethousandsSeparatorSymbol();
		const decimals = this.localization.getLocaleDecimalSymbol();
		this.textMask.mask = createNumberMask({
			prefix: "",
			includeThousandsSeparator: true,
			integerLimit: 6,
			allowDecimal: this.decimal,
			thousandsSeparatorSymbol: thousands,
			decimalSymbol: decimals,
		});
	}
}
