<div class="modal-body">
	<div class="container">
		<div class="row">
			<div class="col">
				<div class="card-imc">
					<div class="titulo-box">IMC</div>
					<div
						class="sub-titulo-box"
						i18n="@@perfil-aluno-modal:avaliacoes:sub-titulo-box">
						Índice de massa corporal
					</div>
					<div class="pct-value">
						{{ avaliacaoAtual?.alunoBI.imc ? avaliacaoAtual.alunoBI.imc : "0" }}
					</div>
					<div class="descricao">
						<div>
							<div class="sub-titulo">{{ altura }} m</div>
							<div
								class="sub-descricao"
								i18n="@@perfil-aluno-modal:avaliacoes:altura">
								Altura
							</div>
						</div>
						<div>
							<div class="sub-titulo">{{ peso }} kg</div>
							<div
								class="sub-descricao"
								i18n="@@perfil-aluno-modal:avaliacoes:peso">
								Peso
							</div>
						</div>
					</div>
					<div class="footer-titulo">
						<span (click)="imcReferenciaHandler()">
							{{
								imc.getLabel(avaliacaoAtual?.imcNota)
									? imc.getLabel(avaliacaoAtual?.imcNota)
									: "Nenhum"
							}}
							<i class="pct pct-info"></i>
						</span>
					</div>
				</div>
			</div>
			<div class="col">
				<div class="card-imc">
					<div
						class="titulo-box"
						i18n="@@perfil-aluno-modal:avaliacoes:titulo-box">
						Composição corporal
					</div>
					<div class="chart">
						<pacto-cat-pie-chart
							[centerValue]="percentualGordura"
							[id]="'composicao-pie-avaliacao'"
							[labelCenter]="'Gordura'"
							[labelFormatterBorder]="composicaoLabelBorderFn()"
							[labelFormatter]="composicaoLabelFn()"
							[series]="composicaoSeries"
							[simbol]="'kg'"></pacto-cat-pie-chart>
					</div>
					<div class="footer-titulo">
						<span (click)="composicaoReferenciaHandler()">
							{{ composicao.getLabel(avaliacaoAtual?.composicaoNota) }}
							<i class="pct pct-info"></i>
						</span>
					</div>
				</div>
			</div>
			<div class="col">
				<div class="card-imc">
					<div
						class="titulo-box"
						i18n="@@perfil-aluno-modal:avaliacoes:titulo-box-cardio">
						Risco Cardiovascular
					</div>
					<div class="sub-titulo-box">Circunferência abdominal</div>
					<div class="pct-value">
						{{ avaliacaoAtual?.perimetria.circunferenciaAbdominal }}
					</div>
					<div class="descricao-cardio">
						<div
							class="sub-descricao cardio-descricao"
							i18n="@@perfil-aluno-modal:avaliacoes:centimetros">
							centímetros
						</div>
					</div>
					<div>
						<div class="container">
							<div class="descricao"></div>
							<div class="footer-titulo">
								<span (click)="cardioReferenciaHandler()">
									{{ avaliacaoAtual?.cardioNota }}
									<i class="pct pct-info"></i>
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #imc>
	<span i18n="@@perfil-aluno-modal:avaliacoes:NENHUM" xingling="NENHUM">
		Nenhum
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:BAIXO" xingling="BAIXO">
		Abaixo do peso normal
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:NORMAL" xingling="NORMAL">
		Peso normal
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:SOBREPESO" xingling="SOBREPESO">
		Sobrepeso
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:OBESIDADE_CLASSE_I"
		xingling="OBESIDADE_CLASSE_I">
		Obesidade grau I
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:OBESIDADE_CLASSE_II"
		xingling="OBESIDADE_CLASSE_II">
		Obesidade grau II
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:OBESIDADE_CLASSE_III"
		xingling="OBESIDADE_CLASSE_III">
		Obesidade grau III
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #composicaoLabels>
	<span i18n="@@perfil-aluno-modal:avaliacoes:gordura" xingling="gordura">
		Gordura
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:residuos" xingling="residuos">
		Resíduos
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:musculos" xingling="musculos">
		Músculos
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:ossos" xingling="ossos">
		Ossos
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:naoInformado"
		xingling="naoInformado">
		Não informado
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoPorcentualMassaMagra"
		xingling="composicaoPorcentualMassaMagra">
		% Massa Magra
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoPorcentualGordura"
		xingling="composicaoPorcentualGordura">
		% Massa Gorda
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoMassaMagra"
		xingling="composicaoMassaMagra">
		Massa Magra
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoMassaGorda"
		xingling="composicaoMassaGorda">
		Massa Gorda
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #composicao>
	<span i18n="@@perfil-aluno-modal:avaliacoes:EXCELENTE" xingling="EXCELENTE">
		Excelente
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:BOM" xingling="BOM">Bom</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:ACIMA_MEDIA"
		xingling="ACIMA_MEDIA">
		Acima da média
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:MEDIA" xingling="MEDIA">
		Média
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:ABAIXO_MEDIA"
		xingling="ABAIXO_MEDIA">
		Abaixo da média
	</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:RUIM" xingling="RUIM">Ruim</span>
	<span i18n="@@perfil-aluno-modal:avaliacoes:MUITO_RUIM" xingling="MUITO_RUIM">
		Muito ruim
	</span>
</pacto-traducoes-xingling>
