@import "~src/assets/scss/pacto/plataforma-import.scss";

.text-center {
	display: flex;
	justify-content: center;
}

.titulo-box {
	color: $pretoPri;
	margin-top: 5%;
	@extend .type-h5;
	@extend .text-center;
}

.sub-titulo-box {
	font-size: 15px;
	margin-top: 25px;
	color: $gelo04;
	@extend .type-p-small-rounded;
	@extend .type-caption;
	@extend .text-center;
}

.pct-value {
	color: $pretoPri !important;
	font-size: 50px;
	margin-top: 15px;
	@extend .type-h5-bold;
	@extend .text-center;
}

.descricao {
	display: flex;
	margin: 14px 35px 0 35px;
	justify-content: space-between;
}

.sub-titulo {
	color: $pretoPri;
	font-weight: 600;
	font-size: 16px;
	@extend .type-caption;
	@extend .type-h6;
}

.sub-descricao {
	font-size: 15px !important;
	color: $gelo04;
	@extend .type-p-small-rounded;
	@extend .type-caption;
	@extend .text-center;
}

.footer-titulo {
	span {
		cursor: pointer;
	}

	color: black;
	padding: 18px;
	@extend .text-center;
	@extend .type-h6;
}

.footer-cardio {
	display: flex;
}

.footer-titulo-cardio {
	color: black;
	margin: 15px 0 0 56px;
	@extend .text-center;
	@extend .type-h6;
}

.icon-space {
	@extend .text-center;
	margin: 4px 0 7px 5px;
}

.descricao-cardio {
	@extend .text-center;
}

.chart {
	margin-top: 15px;
}

.cardio-descricao {
	margin-bottom: 25px;
}
