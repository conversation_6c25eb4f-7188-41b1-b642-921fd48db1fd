import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { PieChartSet, TraducoesXinglingComponent } from "ui-kit";
import {
	AvaliacaoFisica,
	AlunoBase,
	TreinoApiAvaliacaoFisicaService,
} from "treino-api";
import { ReferenciaImcComponent } from "../../../base/alunos/perfil-aluno/components/avaliacoes/referencia-imc/referencia-imc.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ReferenciaPercentualGorduraComponent } from "../../../base/alunos/perfil-aluno/components/avaliacoes/referencia-percentual-gordura/referencia-percentual-gordura.component";
import { ReferenciaCardioComponent } from "../../../base/alunos/perfil-aluno/components/avaliacoes/referencia-cardio/referencia-cardio.component";

@Component({
	selector: "pacto-avaliacao-resultado-modal",
	templateUrl: "./avaliacao-resultado-modal.component.html",
	styleUrls: ["./avaliacao-resultado-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoResultadoModalComponent implements OnInit {
	@Input() aluno: AlunoBase;
	@ViewChild("composicaoLabels", { static: true })
	composicaoLabels: TraducoesXinglingComponent;

	composicaoSeries: PieChartSet[] = [];
	avaliacaoAtual: AvaliacaoFisica;
	todas: AvaliacaoFisica[];
	percentualGordura;

	constructor(
		private avaliacaoService: TreinoApiAvaliacaoFisicaService,
		private cd: ChangeDetectorRef,
		private modal: NgbModal
	) {}

	ngOnInit() {
		if (this.aluno) {
			setTimeout(() => {
				this.prepareComposicaoChart();
				this.cd.detectChanges();
			});
		}
	}

	private prepareComposicaoChart() {
		this.composicaoSeries = [];
		if (this.avaliacaoAtual) {
			const bi = this.avaliacaoAtual.alunoBI;
			const keys = this.graficoBioImpedanciaManual(this.avaliacaoAtual)
				? ["composicaoMassaMagra", "composicaoMassaGorda"]
				: ["gordura", "musculos", "residuos", "ossos"];
			if (bi.naoInformado > 0.0) {
				keys.push("naoInformado");
			}
			this.percentualGordura = bi.composicaoPorcentualGordura
				? Math.trunc(bi.composicaoPorcentualGordura)
				: 0.0;
			keys.forEach((key) => {
				this.composicaoSeries.push({
					name: this.composicaoLabels.getLabel(key),
					data: bi[key],
				});
			});
		}
		this.cd.detectChanges();
	}

	private graficoBioImpedanciaManual(avaliacaoAtual: AvaliacaoFisica) {
		if (
			avaliacaoAtual.alunoBI &&
			avaliacaoAtual.alunoBI.composicaoMassaGorda > 0
		) {
			return true;
		}
	}

	get altura() {
		const avaliacao = this.avaliacaoAtual;
		return (avaliacao && avaliacao.alunoBI && avaliacao.alunoBI.altura) || "0";
	}

	get peso() {
		const avaliacao = this.avaliacaoAtual;
		return (avaliacao && avaliacao.alunoBI && avaliacao.alunoBI.peso) || "0";
	}

	composicaoLabelFn() {
		return (value) => {
			return `${value}%`;
		};
	}

	composicaoLabelBorderFn() {
		return (value) => {
			let arredondado;
			arredondado = Math.round(value * 100) / 100;
			return `${arredondado} kg`;
		};
	}

	get informacoesComposicaoDisponiveis() {
		if (!this.avaliacaoAtual) {
			return false;
		} else {
			const gordura = !isNaN(
				this.avaliacaoAtual.alunoBI.composicaoPorcentualGordura
			);
			const ossos = !isNaN(
				this.avaliacaoAtual.alunoBI.composicaoPorcentualOssos
			);
			const residuos = !isNaN(
				this.avaliacaoAtual.alunoBI.composicaoPorcentualResiduos
			);
			const musculo = !isNaN(
				this.avaliacaoAtual.alunoBI.composicaoPorcentualMusculos
			);
			const peso = this.avaliacaoAtual.alunoBI.peso;
			return peso && gordura && ossos && residuos && musculo;
		}
	}

	imcReferenciaHandler() {
		this.modal.open(ReferenciaImcComponent, { centered: true, size: "lg" });
	}

	composicaoReferenciaHandler() {
		const instance: ReferenciaPercentualGorduraComponent = this.modal.open(
			ReferenciaPercentualGorduraComponent,
			{ centered: true, size: "lg" }
		).componentInstance;
		instance.sexo = this.aluno.sexo;
	}

	cardioReferenciaHandler() {
		this.modal.open(ReferenciaCardioComponent, { centered: true, size: "lg" });
	}
}
