import {
	Component,
	OnInit,
	Input,
	SimpleChanges,
	OnChanges,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Output,
	EventEmitter,
	ViewChild,
} from "@angular/core";
import {
	AvaliacaoFisica,
	AlunoBase,
	TreinoApiAvaliacaoCatalogoService,
	TreinoApiAlunosService,
} from "treino-api";
import { FormControl } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { RestService } from "@base-core/rest/rest.service";
import { PerfilAcessoRecursoNome } from "treino-api";

declare var moment;

@Component({
	selector: "pacto-avaliacoes-aluno-top",
	templateUrl: "./avaliacoes-aluno-top.component.html",
	styleUrls: ["./avaliacoes-aluno-top.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacoesAlunoTopComponent implements OnInit, OnChanges {
	@Output() enviar: EventEmitter<boolean> = new EventEmitter();
	@Output() imprimir: EventEmitter<boolean> = new EventEmitter();
	@Output() historico: EventEmitter<boolean> = new EventEmitter();
	@Output() remover: EventEmitter<boolean> = new EventEmitter();
	@Input() rota: any[];
	@Input() aluno: AlunoBase;
	@Input() rotaLabel;
	@Input() reload: boolean;
	@Input() parqPositivo: boolean;
	@Input() avaliacoes: AvaliacaoFisica[];
	@Input() criando: boolean;
	@Input() avaliacaoAtualId;
	@Input() options = [];
	@Input() isAvIntegrada = false;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	selectFc = new FormControl();
	exibindoPopOver: boolean;
	alunoId = 0;
	avaliacaoId = 0;
	validarProduto: any;
	produtoAvaliacao: any;
	isProdutoValido: any;
	clienteMensagemAviso: string;

	constructor(
		private catalogoService: TreinoApiAvaliacaoCatalogoService,
		private alunoService: TreinoApiAlunosService,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private router: Router,
		private route: ActivatedRoute,
		private snotify: SnotifyService,
		private treinoConfigService: TreinoConfigCacheService,
		private rest: RestService
	) {}

	get empresa() {
		return this.session.currentEmpresa;
	}

	ngOnInit() {
		this.getClienteMensagemAviso();
		this.route.params.subscribe((params) => {
			this.alunoId = params.alunoId;
			this.avaliacaoId = params.avaliacaoId;
		});
		this.selectFc.valueChanges.subscribe((id) => {
			this.router.navigate([
				"avaliacao",
				"avaliacoes-aluno",
				this.aluno.id,
				"avaliacao",
				id,
			]);
		});
	}

	get urlLog(): string {
		return this.rest.buildFullUrl(
			`log/avaliacoes-aluno/${this.aluno.id}/${this.avaliacaoId}`
		);
	}

	get avatarUrl() {
		if (this.aluno && this.aluno.imageUri) {
			return this.aluno.imageUri;
		} else {
			return "assets/images/default-user-icon.png";
		}
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.avaliacoes && changes.avaliacoes.currentValue) {
			this.prepareSelect(changes.avaliacoes.currentValue);
		}
		if (changes.avaliacaoAtualId && changes.avaliacaoAtualId.currentValue) {
			this.selectFc.setValue(changes.avaliacaoAtualId.currentValue);
		}
	}

	criarHandler() {
		const permissao = this.session.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if (permissao && permissao.incluir) {
			this.validarProduto = JSON.parse(
				this.treinoConfigService.configuracoesAvaliacao
					.validar_produto_avaliacao
			);
			if (this.validarProduto) {
				this.produtoAvaliacao = JSON.parse(
					this.treinoConfigService.configuracoesAvaliacao.produto_avaliacao
				);
				this.catalogoService
					.isProdutoVigente(this.aluno.id, this.produtoAvaliacao)
					.subscribe((result) => {
						this.isProdutoValido = result;
						const stringValue = result;
						this.isProdutoValido = /true/i.test(stringValue);
						if (this.isProdutoValido) {
							if (
								this.aluno.sexo === undefined ||
								this.aluno.idade === undefined
							) {
								this.snotify.warning(
									this.traducoes.getLabel("CAMPOS_OBRIGATORIOS")
								);
							} else {
								this.router.navigate([
									"/avaliacao",
									"avaliacoes-aluno",
									this.aluno.id,
									"avaliacao",
									"novo",
								]);
							}
						} else {
							if (stringValue.includes("usado.")) {
								this.snotify.error(
									this.traducoes.getLabel("PRODUTO_ULTILIZADO")
								);
								return false;
							} else {
								this.snotify.error(this.traducoes.getLabel("VALIDAR_PRODUTO"));
								return false;
							}
						}
					});
			} else {
				if (!this.aluno.sexo) {
					this.snotify.warning(this.traducoes.getLabel("CAMPOS_OBRIGATORIOS"));
				} else {
					this.router.navigate([
						"/avaliacao",
						"avaliacoes-aluno",
						this.aluno.id,
						"avaliacao",
						"novo",
					]);
				}
			}
		} else {
			this.snotify.warning(
				"Seu usuário não possui permissão, procure seu administrador"
			);
		}
	}

	private prepareSelect(avaliacoes: AvaliacaoFisica[]) {
		this.options = [];
		avaliacoes.forEach((avaliacao) => {
			const dia = moment(avaliacao.dataAvaliacao).format("DD/MM/YYYY");
			this.options.push({
				id: avaliacao.id,
				label: `${avaliacao.id} - ${dia}`,
			});
		});
		this.cd.detectChanges();
	}

	getClienteMensagemAviso() {
		this.alunoService
			.obterClienteMensagem(this.aluno.id, "AM")
			.subscribe((result) => {
				this.clienteMensagemAviso = result.toString().replace(/&nbsp;/g, " ");
				this.cd.detectChanges();
			});
	}

	get validarConfigVisualizarMensagemAviso(): boolean {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino.visualizar_mensagem_aviso
		) {
			return true;
		} else {
			return false;
		}
	}
}
