@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	padding-bottom: 30px;
	width: 100%;
	display: block;
	color: $pretoPri;
}

pacto-cat-person-avatar {
	display: none;
}

.nav-wrapper {
	.pct {
		font-size: 28px;
	}

	margin: 20px 0px 20px;
	cursor: pointer;
}

.center-aux {
	display: flex;
}

.top-navigation {
	color: $pretoPri;

	i {
		font-size: 28px;
	}

	@extend .type-h2;
}

.right-aux {
	margin-left: 30px;
	display: flex;
	flex-grow: 1;
	flex-wrap: wrap;
}

.name-aux {
	display: inline-flex;
	justify-content: space-between;
	flex-basis: 100%;
	padding-bottom: 15px;
	margin-bottom: 15px;
	border-bottom: 1px solid $gelo02;

	.matricula {
		@extend .type-caption;
		color: $gelo05;
	}
}

.titulo-aviso-msg {
	display: flex;
	align-items: end;
	font-size: 16px;
	font-family: "Nunito Sans";
	color: #db2c3d;

	i {
		margin-right: 10px;
		margin-bottom: 4.5px;
	}

	span::first-letter {
		text-transform: uppercase;
	}
}

@media (min-width: $bootstrap-breakpoint-md) {
	pacto-cat-person-avatar {
		display: block;
	}
}

@media (min-width: $bootstrap-breakpoint-xl) {
	.empresa-aux,
	.avaliacoes-aux {
		flex-basis: 33% !important;
	}
}

.empresa-aux {
	border-right: 1px solid $gelo02;
	flex-basis: 50%;
}

.avaliacoes-aux {
	flex-basis: 50%;
	padding-left: 30px;

	.top-aux {
		display: flex;
		justify-content: space-between;
	}

	.pct {
		font-size: 20px;
	}
}

.value-item {
	margin-bottom: 15px;

	.value-label {
		@extend .type-h6;
		color: $gelo04;
	}

	.value-content {
		@extend .type-p-small-rounded;
		color: $pretoPri;
	}
}

.value-evolucao {
	display: flex;
	justify-content: space-between;
	height: 32px;
	border-radius: 4px;
}

.evolucao-label {
}

.pct-popover {
	width: 109.7px;
	height: 129px;
	border-radius: 5px;
	margin: -7px 0px 0px 17px;
	box-shadow: 0 2px 4px 0 $cinzaClaro02;
	border: solid 1px $gelo03;
	background-color: $branco;
}

.arrow {
	top: calc(-1 * 0.6875rem + 1px);
	left: calc(50% - 0.6875rem);
}

.btn-over {
	cursor: pointer;
	@extend .type-caption;
	margin-bottom: 20px;
	color: $gelo05;
	font-size: 15px;
}

.position-btn {
	margin: 10px 0 10px 5px;
}

.botoes-aux {
	display: flex;
	flex-wrap: wrap;

	> * {
		margin: 19px 25px 0px 0px;
	}
}

::ng-deep pacto-log.pequeno {
	.btn {
		padding: 4px 10px;
		margin-right: 8px;
		background: $azulimPri;
		border: 1px solid $azulimPri;
	}

	.btn:hover {
		background: $azulimPri;
		border: 1px solid $azulimPri;
	}
}
