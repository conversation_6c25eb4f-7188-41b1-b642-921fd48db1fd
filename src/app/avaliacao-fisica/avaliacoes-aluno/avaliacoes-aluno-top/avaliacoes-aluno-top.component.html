<div class="nav-wrapper">
	<div>
		<a [routerLink]="rota" class="top-navigation" id="voltar-alunos">
			<i class="pct pct-arrow-left"></i>
			<span class="type-h2">{{ rotaLabel }}</span>
		</a>
	</div>
</div>

<div class="center-aux">
	<pacto-cat-person-avatar
		[diameter]="117"
		[parqPositivo]="parqPositivo"
		[uri]="avatarUrl"></pacto-cat-person-avatar>

	<div class="right-aux">
		<div class="name-aux">
			<div>
				<div class="type-h5">{{ aluno?.nome }}</div>
				<div class="matricula">MAT: {{ aluno?.matriculaZW }}</div>
			</div>
			<div
				*ngIf="validarConfigVisualizarMensagemAviso && clienteMensagemAviso"
				class="titulo-aviso-msg">
				<i class="pct pct-alert-triangle"></i>
				<span>{{ clienteMensagemAviso }}</span>
			</div>
		</div>

		<div class="empresa-aux">
			<div class="value-item">
				<div class="value-label">Empresa</div>
				<div class="value-content">{{ empresa?.nome }}</div>
			</div>
			<div class="value-item">
				<div
					class="value-label"
					i18n="@@avaliacao-fisica:avaliacoes-aluno:professor">
					Professor
				</div>
				<div class="value-content">{{ aluno?.professor?.nome }}</div>
			</div>

			<div *ngIf="avaliacoes?.length > 1" class="value-evolucao">
				<pacto-cat-button
					[icon]="'bar-chart-2'"
					[label]="'VER EVOLUÇÃO FÍSICA'"
					[routerLink]="['/avaliacao/avaliacao-evolucao', aluno?.matriculaZW]"
					id="ver-evolucao"></pacto-cat-button>
			</div>
		</div>

		<div *ngIf="!criando && !isAvIntegrada" class="avaliacoes-aux">
			<div class="top-aux">
				<div
					class="name type-h5"
					i18n="@@avaliacao-fisica:avaliacoes-aluno:gerenciar">
					Gerenciar Avaliações
				</div>
				<div *ngIf="avaliacaoAtualId">
					<i
						(click)="exibindoPopOver = !exibindoPopOver"
						[ngbPopover]="templateRef"
						class="pct pct-more-vertical"
						placement="right"></i>
				</div>
			</div>

			<pacto-cat-select
				[control]="selectFc"
				[id]="'avaliacoes-select'"
				[items]="options"
				i18n-label="@@avaliacao-fisica:avaliacoes-aluno:lista:label"
				label="Avaliações do Aluno"></pacto-cat-select>

			<div class="botoes-aux">
				<pacto-cat-button
					(click)="criarHandler()"
					i18n-label="@@avaliacao-fisica:avaliacoes-aluno:nova"
					id="nova-avaliacao"
					label="NOVA AVALIAÇÃO"></pacto-cat-button>
				<pacto-cat-button
					(click)="historico.emit(true)"
					*ngIf="avaliacoes?.length > 1"
					[label]="'COMPARAR'"
					id="comparar-avaliacao"></pacto-cat-button>
				<pacto-log [url]="urlLog" class="pequeno"></pacto-log>
			</div>
		</div>
		<ng-template #templateRef>
			<!--         <div class="pct-popover">-->
			<div class="position-btn">
				<div
					(click)="enviar.emit(true)"
					class="btn-over"
					i18n="@@avaliacao-fisica:avaliacoes-aluno:enviar">
					Enviar
				</div>
				<div
					(click)="imprimir.emit(true)"
					class="btn-over"
					i18n="@@avaliacao-fisica:avaliacoes-aluno:imprimir">
					Imprimir
				</div>
				<div
					(click)="historico.emit(true)"
					class="btn-over"
					i18n="@@avaliacao-fisica:avaliacoes-aluno:historico">
					Ver histórico
				</div>
				<div (click)="remover.emit(true)" class="btn-over">Remover</div>
			</div>
			<!--        </div>-->
		</ng-template>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span xingling="CAMPOS_OBRIGATORIOS">
		Data de nascimento e sexo são obrigatórios.
	</span>
	<span xingling="VALIDAR_PRODUTO">
		O aluno não tem um produto de Avaliação Física vigente pago.
	</span>
	<span xingling="PRODUTO_ULTILIZADO">O produto vigente já foi usado.</span>
</pacto-traducoes-xingling>
