import { Activated<PERSON><PERSON><PERSON>, Router } from "@angular/router";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { SafeHtml } from "@angular/platform-browser";

import { map } from "rxjs/operators";
import { Observable, Subscription, zip } from "rxjs";
import { SnotifyAnimate } from "ng-snotify/snotify/interfaces/SnotifyAnimate.interface";
import { SnotifyType } from "ng-snotify/snotify/types/snotify.type";
import {
	SnotifyButton,
	SnotifyPosition,
	SnotifyService,
	SnotifyToastConfig,
} from "ng-snotify";

import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import {
	AlunoBase,
	Anamnese,
	AnamnesePergunta,
	AnamnesePerguntaResposta,
	AvaliacaoDobraProtocolo,
	AvaliacaoFisica,
	AvaliacaoObjetivo,
	PerfilAcessoRecursoNome,
	RMLConfig,
	TreinoApiAlunosService,
	TreinoApiAnamneseService,
	TreinoApiAvaliacaoCatalogoService,
	TreinoApiAvaliacaoFisicaService,
} from "treino-api";
import { buildAvaliacaoForm } from "./avaliacao-form-group";
import { defaultAvaliacaoDto } from "./avaliacao-form-group-default";
import { AnamnesePerguntaDto } from "@base-shared/cat-anamnese-pergunta-input/cat-anamnese-pergunta-input.component";
import { AvaliacaoFisicaModalComponent } from "./avaliacao-fisica-modal/avaliacao-fisica-modal.component";
import { AvaliacaoFisicaHistoricoModalComponent } from "./avaliacao-fisica-historico-modal/avaliacao-fisica-historico-modal.component";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { AvaliacaoResultadoModalComponent } from "./avaliacao-resultado-modal/avaliacao-resultado-modal.component";
import { AvaliacaoEditorPosturaComponent } from "./avaliacao-partes/avaliacao-editor-postura/avaliacao-editor-postura.component";
import {
	CatTabsTransparentComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { AlunoSexo } from "../../../../projects/treino-api/src/lib/aluno.model";

export enum AvaliacaoEstado {
	LOADING_AVALIACAO = "LOADING_AVALIACAO",
	SEM_AVALIACAO = "SEM_AVALIACAO",
	VIEW = "VIEW_ATUAL",
	EDIT = "EDIT_ATUAL",
	CREATING = "CREATING",
}

enum AvaliacaoAba {
	ANAMNESE = "ANAMNESE",
	PARQ = "PARQ",
	PESO_ALTURA = "PESO_ALTURA",
	DOBRAS = "DOBRAS",
	PERIMETRIA = "PERIMETRIA",
	COMPOSICAO = "COMPOSICAO",
	FLEXIBILIDADE = "FLEXIBILIDADE",
	POSTURA = "POSTURA",
	RML = "RML",
	VO2MAX = "VO2MAX",
	SOMATOTIPIA = "SOMATOTIPIA",
	META_RECOM = "META_RECOM",
	IMPORTACAO_BIOSANNY = "IMPORTACAO_BIOSANNY",
}

declare var moment;

@Component({
	selector: "pacto-avaliacoes-aluno",
	templateUrl: "./avaliacoes-aluno.component.html",
	styleUrls: ["./avaliacoes-aluno.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacoesAlunoComponent implements OnInit, AfterViewInit {
	@ViewChild("avaliacaoTabs", { static: true })
	avaliacaoTabs: CatTabsTransparentComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("verificaEmailTelefone", { static: true }) verificaEmailTelefone;
	@ViewChild("removeSuccessMsg", { static: true }) removeSuccessMsg;
	@ViewChild("posturaTab", { static: false })
	posturaTab: AvaliacaoEditorPosturaComponent;
	@ViewChild("popupErro", { static: true }) popupErro;
	@ViewChild("imprimirErro", { static: true }) imprimirErro;

	@Input() questionarioParq: boolean;
	@Input() respostasFA: FormArray;

	objectKeys = Object.keys;

	camposObrigatoriosMsgErro = "";

	abasJson = [
		{
			nomeConfiguracao: "cfg_objetivos_anamnese",
			id: "ANAMNESE",
		},
		{
			nomeConfiguracao: "cfg_peso_altura_pa_fc",
			id: "PESO_ALTURA",
		},
		{
			nomeConfiguracao: "cfg_dobras_cutaneas",
			id: "DOBRAS",
		},
		{
			nomeConfiguracao: "cfg_perimetria",
			id: "PERIMETRIA",
		},
		{
			nomeConfiguracao: "cfg_flexibilidade",
			id: "FLEXIBILIDADE",
		},
		{
			nomeConfiguracao: "cfg_postura",
			id: "POSTURA",
		},
		{
			nomeConfiguracao: "cfg_rml",
			id: "RML",
		},
		{
			nomeConfiguracao: "cfg_vo2max",
			id: "VO2MAX",
		},
		{
			nomeConfiguracao: "cfg_somatotipia",
			id: "SOMATOTIPIA",
		},
		{
			nomeConfiguracao: "cfg_recomendacoes",
			id: "META_RECOM",
		},
		{
			nomeConfiguracao: "cfg_importacao_biosanny",
			id: "IMPORTACAO_BIOSANNY",
		},
	];

	abasJsonIndependente = [
		{
			nomeConfiguracao: "cfg_objetivos_anamnese",
			id: "ANAMNESE",
		},
		{
			nomeConfiguracao: "cfg_parq",
			id: "PARQ",
		},
		{
			nomeConfiguracao: "cfg_peso_altura_pa_fc",
			id: "PESO_ALTURA",
		},
		{
			nomeConfiguracao: "cfg_dobras_cutaneas",
			id: "DOBRAS",
		},
		{
			nomeConfiguracao: "cfg_perimetria",
			id: "PERIMETRIA",
		},
		{
			nomeConfiguracao: "cfg_flexibilidade",
			id: "FLEXIBILIDADE",
		},
		{
			nomeConfiguracao: "cfg_postura",
			id: "POSTURA",
		},
		{
			nomeConfiguracao: "cfg_rml",
			id: "RML",
		},
		{
			nomeConfiguracao: "cfg_vo2max",
			id: "VO2MAX",
		},
		{
			nomeConfiguracao: "cfg_somatotipia",
			id: "SOMATOTIPIA",
		},
		{
			nomeConfiguracao: "cfg_recomendacoes",
			id: "META_RECOM",
		},
		{
			nomeConfiguracao: "cfg_importacao_biosanny",
			id: "IMPORTACAO_BIOSANNY",
		},
	];

	/**
	 * Dados configurados apenas uma vez.
	 */
	objetivos: AvaliacaoObjetivo[] = [];
	parq: AnamnesePergunta[];
	rmlConfig: RMLConfig;
	anamneses: Anamnese[];
	avaliacaoCategorias: any[] = [];
	aluno: AlunoBase;
	atualizar = false;
	options = [];
	resultadoParq;

	/**
	 * Estado interno.
	 */
	alunoAvaliacoes: AvaliacaoFisica[];
	avaliacaoAtual: AvaliacaoFisica;
	avaliacaoCategoriaFc = new FormControl();
	anamneseSelecionada: Anamnese;
	anamneseFormControl: FormControl = new FormControl();
	avaliacaoFormGroup: FormGroup;
	criando = true;
	changes = false;
	avaliacaoOnChangeSubscription: Subscription;

	mostrarTopo = false;
	loading = false;

	catalogoAvaliacaoEstado: AvaliacaoEstado = null;

	configSnotify: SnotifyToastConfig = new (class implements SnotifyToastConfig {
		animation: SnotifyAnimate;
		backdrop: number;
		bodyMaxLength: number;
		buttons: SnotifyButton[];
		closeOnClick: boolean;
		html: string | SafeHtml;
		icon: string;
		iconClass: string;
		pauseOnHover: boolean;
		placeholder: string;
		position: SnotifyPosition;
		showProgressBar: boolean;
		timeout: number;
		titleMaxLength: number;
		type: SnotifyType;
	})();
	produtoAvaliacao: any;
	integracaoZW = false;
	isProdutoValido: any;
	anamnesesAtivas: Anamnese[];

	constructor(
		private router: Router,
		private snotify: SnotifyService,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private alunoService: TreinoApiAlunosService,
		private modal: ModalService,
		private anamneseService: TreinoApiAnamneseService,
		private treinoConfigService: TreinoConfigCacheService,
		private avaliacaoFisicaService: TreinoApiAvaliacaoFisicaService,
		private avaliacaoCatalogoService: TreinoApiAvaliacaoCatalogoService,
		private catalogoService: TreinoApiAvaliacaoCatalogoService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
	}

	validarPermissaoProduto() {
		this.alunoService.obterAluno(this.aluno.id).subscribe((alunoResponse) => {
			this.aluno = alunoResponse;
			if (this.validarProduto && this.integracaoZW) {
				this.produtoAvaliacao = JSON.parse(
					this.treinoConfigService.configuracoesAvaliacao.produto_avaliacao
				);
				this.catalogoService
					.isProdutoVigente(this.aluno.id, this.produtoAvaliacao)
					.subscribe((result) => {
						this.isProdutoValido = result;
						const stringValue = result;
						this.isProdutoValido = /true/i.test(stringValue);
						if (!this.isProdutoValido) {
							if (stringValue.includes("usado.")) {
								this.snotify.error(
									this.traducoes.getLabel("PRODUTO_ULTILIZADO")
								);
								this.router.navigate([
									"/cadastros",
									"alunos",
									"perfil",
									this.aluno.id,
								]);
							} else {
								this.snotify.error(this.traducoes.getLabel("VALIDAR_PRODUTO"));
								this.router.navigate([
									"/cadastros",
									"alunos",
									"perfil",
									this.aluno.id,
								]);
							}
						}
					});
			}
		});
	}

	ngAfterViewInit() {
		this.avaliacaoFormGroup = buildAvaliacaoForm();
		this.setupOnAnamneseChange();
		this.prepareAvaliacaoSections();
		this.loadInitialData().subscribe(() => {
			this.mostrarTopo = true;
			this.cd.detectChanges();
			this.route.params.subscribe((params) => {
				this.handleRoute(params);
				this.resultado();
			});
		});
	}

	resultado() {
		this.respostasFA = this.avaliacaoFormGroup.get("parQRespostas").value;
		if (this.respostasFA) {
			this.validResult(this.respostasFA);
			this.avaliacaoFormGroup
				.get("parQRespostas")
				.valueChanges.subscribe((respostas) => {
					this.validResult(respostas);
				});
		}
	}

	validResult(questoes) {
		this.resultadoParq = false;
		questoes.forEach((questao) => {
			if (questao.resposta === "SIM") {
				this.resultadoParq = true;
			}
		});
	}

	get validarProduto() {
		return this.treinoConfigService.configuracoesAvaliacao
			.validar_produto_avaliacao;
	}

	get categoriaIndex() {
		const categoria = this.avaliacaoCategoriaFc.value;
		const index = this.avaliacaoCategorias.findIndex((item) => {
			return item.id === categoria;
		});
		if (index >= 0) {
			return index;
		} else {
			return null;
		}
	}

	get configuracoesAvaliacao() {
		return this.treinoConfigService.configuracoesAvaliacao;
	}

	btnEnviarHandler() {
		if (
			this.avaliacaoAtual.emails.length !== 0 ||
			this.avaliacaoAtual.telefones.length !== 0
		) {
			const modalRef = this.modal.open(
				"Enviar avaliações física ao aluno",
				AvaliacaoFisicaModalComponent
			);
			modalRef.componentInstance.avaliacaoId = this.avaliacaoAtual.id;
			modalRef.componentInstance.listaEmails = this.avaliacaoAtual.emails;
			modalRef.componentInstance.listaTelefones = this.avaliacaoAtual.telefones;
		} else {
			const errorMessage = this.verificaEmailTelefone.nativeElement.innerHTML;
			this.snotify.error(errorMessage);
		}
	}

	btnImprimirHandler() {
		this.avaliacaoFisicaService.obterLinkPdf(this.avaliacaoAtual.id).subscribe(
			(result) => {
				const newWin = window.open(result);
				if (!newWin || newWin.closed || typeof newWin.closed === "undefined") {
					const erroPopup = this.popupErro.nativeElement.innerHTML;
					this.snotify.warning(erroPopup);
				}
			},
			(error1) => {
				const imprimir = this.imprimirErro.nativeElement.innerHTML;
				this.snotify.error(imprimir);
			}
		);
		this.sessionService.notificarRecursoEmpresa(
			RecursoSistema.IMPRIMIU_AVALIACAO_NTO
		);
	}

	btnHistoricoHandler() {
		this.avaliacaoFisicaService
			.obterAvaliacoesPorCliente(this.aluno.id)
			.subscribe((dados) => {
				const modalRef = this.modal.open(
					"Histórico de avaliações física",
					AvaliacaoFisicaHistoricoModalComponent,
					PactoModalSize.LARGE
				);
				modalRef.componentInstance.listaAvaliacaoFisica = dados.content;

				modalRef.componentInstance.avaliacaoId = this.avaliacaoAtual.id;
				modalRef.componentInstance.alunoId = this.aluno.id;
			});
	}

	removeHandler() {
		const sucessMessage = this.removeSuccessMsg.nativeElement.innerHTML;
		this.catalogoAvaliacaoEstado = AvaliacaoEstado.LOADING_AVALIACAO;
		this.avaliacaoFisicaService
			.removerAvaliacaoFisica(this.avaliacaoAtual.id)
			.subscribe(() => {
				this.configSnotify.html = `<div class="snotifyToast__body notify-success">${sucessMessage}</div>
                <div class="snotify-icon snotify-icon--success"></div>`;
				this.snotify.success(sucessMessage, this.configSnotify);
				const alunoId = this.aluno.id;
				this.loadAlunoAvaliacoes(this.aluno.id).subscribe(() => {
					this.router.navigate([
						"/cadastros",
						"alunos",
						"perfil",
						this.aluno.id,
					]);
					this.cd.detectChanges();
				});
				this.loadMostRecentAvaliacaoAlunoSelecionado(alunoId).subscribe(
					() => {}
				);
			});
	}

	private loadMostRecentAvaliacaoAlunoSelecionado(id): Observable<boolean> {
		this.avaliacaoAtual = null;
		this.catalogoAvaliacaoEstado = AvaliacaoEstado.LOADING_AVALIACAO;
		return this.catalogoService.obterUltimaAvaliacaoAluno(id).pipe(
			map((avaliacao) => {
				this.avaliacaoAtual = avaliacao;
				if (avaliacao) {
					this.catalogoAvaliacaoEstado = AvaliacaoEstado.VIEW;
					this.fillOutAvaliacaoForm(avaliacao);
				} else {
					this.catalogoAvaliacaoEstado = AvaliacaoEstado.SEM_AVALIACAO;
				}
				return true;
			})
		);
	}

	private setupOnAnamneseChange() {
		this.anamneseFormControl.valueChanges.subscribe((value) => {
			if (value) {
				const found = this.anamneses.find((anamnese) => {
					return anamnese.id === parseInt(value, 10);
				});
				this.anamneseSelecionada = found;

				this.avaliacaoFormGroup
					.get("anamneseRespostas")
					.setValue(
						this.createEmptyAnamneseRespostas(
							this.anamneseSelecionada.perguntas
						)
					);
			}
		});
	}

	private setupOnAvaliacaoChange() {
		if (this.avaliacaoOnChangeSubscription) {
			this.avaliacaoOnChangeSubscription.unsubscribe();
		}
		this.avaliacaoOnChangeSubscription =
			this.avaliacaoFormGroup.valueChanges.subscribe((v) => {
				this.changes = true;
				this.cd.detectChanges();
			});
	}

	private loadAlunoAvaliacoes(alunoId): Observable<any> {
		return this.avaliacaoCatalogoService
			.obterTodasAvaliacoesAluno(alunoId)
			.pipe(
				map((avaliacoes) => {
					this.alunoAvaliacoes = avaliacoes;
					return true;
				})
			);
	}

	private loadAvaliacao(avaliacaoId): Observable<any> {
		return this.avaliacaoFisicaService.obterAvaliacaoFisica(avaliacaoId).pipe(
			map((avaliacao) => {
				this.avaliacaoAtual = avaliacao;
				return avaliacao;
			})
		);
	}

	private handleRoute(params) {
		const avaliacaoId = params.avaliacaoId;
		const novo = avaliacaoId === "novo";
		this.criando = novo;
		this.changes = false;

		if (this.avaliacaoOnChangeSubscription) {
			this.avaliacaoOnChangeSubscription.unsubscribe();
		}

		if (novo) {
			this.validarPermissaoProduto();
			this.routeHandleNew();
		} else if (!isNaN(parseInt(avaliacaoId, 10))) {
			this.routeHandleSelected(avaliacaoId);
		} else {
			this.routeHandleUnselected();
		}
	}

	/**
	 * Loads the categories that are enabled in the configuration.
	 * And pre-selects one of them.
	 */
	private prepareAvaliacaoSections() {
		const maps: { [configuracao: string]: AvaliacaoAba } = {
			cfg_objetivos_anamnese: AvaliacaoAba.ANAMNESE,
			cfg_parq: AvaliacaoAba.PARQ,
			cfg_peso_altura_pa_fc: AvaliacaoAba.PESO_ALTURA,
			cfg_dobras_cutaneas: AvaliacaoAba.DOBRAS,
			cfg_perimetria: AvaliacaoAba.PERIMETRIA,
			cfg_composicao_corporal: AvaliacaoAba.COMPOSICAO,
			cfg_flexibilidade: AvaliacaoAba.FLEXIBILIDADE,
			cfg_postura: AvaliacaoAba.POSTURA,
			cfg_rml: AvaliacaoAba.RML,
			cfg_vo2max: AvaliacaoAba.VO2MAX,
			cfg_somatotipia: AvaliacaoAba.SOMATOTIPIA,
			cfg_recomendacoes: AvaliacaoAba.META_RECOM,
			cfg_importacao_biosanny: AvaliacaoAba.IMPORTACAO_BIOSANNY,
		};
		this.avaliacaoCategorias = [];
		Object.keys(maps).forEach((config) => {
			const enabled = this.treinoConfigService.configuracoesAvaliacao[config];
			if (enabled) {
				this.avaliacaoCategorias.push({
					id: maps[config],
					label: this.traducoes.getLabel(maps[config]),
				});
			}
		});
		if (this.avaliacaoCategorias.length) {
			this.avaliacaoCategoriaFc.setValue(this.avaliacaoCategorias[0].id);
		}
	}

	obrigatorioCamposDobrasBioimpedancia(): boolean {
		return this.treinoConfigService.configuracoesAvaliacao
			.obrigar_campos_dobras_bioimpedancia;
	}

	salvarHandler() {
		if (
			this.avaliacaoFormGroup.get("dataProxima").value !== null &&
			this.avaliacaoFormGroup.get("dataProxima").value <=
				this.avaliacaoFormGroup.get("dataAvaliacao").value
		) {
			this.snotify.warning(this.traducoes.getLabel("VALIDACAOPROXIMADATA"));
			return;
		}
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if (
			(this.aluno.sexo !== AlunoSexo.MASCULINO &&
				this.aluno.sexo !== AlunoSexo.FEMININO) ||
			!this.aluno.dataNascimento
		) {
			this.snotify.warning(this.traducoes.getLabel("VALIDACAOSEXO"));
		} else {
			const adjustedDto = this.getAdjustedDto();
			if (this.isFormValid(adjustedDto) && this.validarAstrand(adjustedDto)) {
				if (this.obrigatorioCamposDobrasBioimpedancia() && adjustedDto.dobras) {
					const camposPreenchidos =
						this.validarCamposObrigatoriosBioimpedanciaEPollockSeteDobras(
							adjustedDto
						);
					if (!camposPreenchidos) {
						this.snotify.error(this.camposObrigatoriosMsgErro);
						return false;
					}
				}
				this.changes = false;
				if (!this.avaliacaoAtual.id) {
					if (permissao && permissao.incluir) {
						this.criarNovaHandler();
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.CRIOU_AVALIACAO_NTO
						);
					} else {
						this.snotify.warning(
							"Seu usuário não possui permissão, procure seu administrador"
						);
					}
				} else {
					if (permissao && permissao.editar) {
						this.atualizarHandler();
					} else {
						this.snotify.warning(
							"Seu usuário não possui permissão, procure seu administrador"
						);
					}
				}
			}
		}
	}

	validarCamposObrigatoriosBioimpedanciaEPollockSeteDobras(adjustedDto) {
		if (
			adjustedDto.dobras.protocolo === AvaliacaoDobraProtocolo.BIOIMPEDANCIA
		) {
			if (
				adjustedDto.dobras.bioimpedancia &&
				(adjustedDto.dobras.bioimpedancia.massaGorda == null ||
					adjustedDto.dobras.bioimpedancia.massaGorda == 0 ||
					adjustedDto.dobras.bioimpedancia.massaMagra == null ||
					adjustedDto.dobras.bioimpedancia.massaMagra == 0 ||
					adjustedDto.dobras.bioimpedancia.percentMassaGorda == null ||
					adjustedDto.dobras.bioimpedancia.percentMassaGorda == 0 ||
					adjustedDto.dobras.bioimpedancia.percentMassaMagra == null ||
					adjustedDto.dobras.bioimpedancia.percentMassaMagra == 0 ||
					adjustedDto.dobras.bioimpedancia.gorduraIdeal == null ||
					adjustedDto.dobras.bioimpedancia.gorduraIdeal == 0 ||
					adjustedDto.dobras.bioimpedancia.musculos == null ||
					adjustedDto.dobras.bioimpedancia.musculos == 0 ||
					adjustedDto.dobras.bioimpedancia.tmb == null ||
					adjustedDto.dobras.bioimpedancia.tmb == 0)
			) {
				this.camposObrigatoriosMsgErro =
					"Informe os campos obrigatórios para o protocolo de bioimpedância";
				return false;
			}
		}
		if (
			adjustedDto.dobras.protocolo === AvaliacaoDobraProtocolo.POLLOCK_7_DOBRAS
		) {
			if (
				adjustedDto.dobras.abdominal == null ||
				adjustedDto.dobras.abdominal === 0 ||
				adjustedDto.dobras.supraIliaca == null ||
				adjustedDto.dobras.supraIliaca === 0 ||
				adjustedDto.dobras.peitoral == null ||
				adjustedDto.dobras.peitoral === 0 ||
				adjustedDto.dobras.triceps == null ||
				adjustedDto.dobras.triceps === 0 ||
				adjustedDto.dobras.coxaMedial == null ||
				adjustedDto.dobras.coxaMedial === 0 ||
				adjustedDto.dobras.subescapular == null ||
				adjustedDto.dobras.subescapular === 0 ||
				adjustedDto.dobras.axilarMedia == null ||
				adjustedDto.dobras.axilarMedia === 0
			) {
				this.camposObrigatoriosMsgErro =
					"Informe os campos obrigatórios para o protocolo de pollock sete dobras";
				return false;
			}
		}
		return true;
	}

	validarAstrand(adjustedDto) {
		if (
			adjustedDto.vo2.astrandCarga ||
			adjustedDto.vo2.astrandFrequencia4 ||
			adjustedDto.vo2.astrandFrequencia5
		) {
			if (!adjustedDto.pesoAltura.peso) {
				this.snotify.warning(this.traducoes.getLabel("VALIDACAOASTRAND"));
				return false;
			} else {
				return true;
			}
		} else {
			return true;
		}
	}

	cancelarHandler() {
		if (this.avaliacaoOnChangeSubscription) {
			this.avaliacaoOnChangeSubscription.unsubscribe();
		}
		this.changes = false;
		if (this.alunoAvaliacoes.length === 0) {
			this.router.navigate(["cadastros", "alunos", "perfil", this.aluno.id]);
		} else if (this.criando) {
			this.router.navigate([
				"avaliacao",
				"avaliacoes-aluno",
				this.aluno.id,
				"avaliacao",
			]);
		} else {
			this.routeHandleSelected(this.avaliacaoAtual.id);
		}
	}

	resultadoAvaliacao() {
		const modalRef = this.modal.open(
			"Resultado da avaliação",
			AvaliacaoResultadoModalComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.aluno = this.aluno;
		modalRef.componentInstance.avaliacaoAtual = this.avaliacaoAtual;
	}

	goFowardHandler() {
		const next = this.avaliacaoCategorias[this.categoriaIndex + 1];
		this.avaliacaoCategoriaFc.setValue(next.id);
	}

	goBacwardHandler() {
		const next = this.avaliacaoCategorias[this.categoriaIndex - 1];
		this.avaliacaoCategoriaFc.setValue(next.id);
	}

	private atualizarHandler() {
		const adjustedDto = this.getAdjustedDto();
		this.loading = true;
		this.avaliacaoFisicaService
			.atualizarAvaliacaoFisica(this.avaliacaoAtual.id, adjustedDto)
			.subscribe((update) => {
				this.avaliacaoAtual = update;
				this.loading = false;
				this.snotify.success(this.traducoes.getLabel("MENSSAGEM_SUCESSO"));
				this.loadAlunoAvaliacoes(this.aluno.id).subscribe(() => {
					this.router.navigate([
						"avaliacao",
						"avaliacoes-aluno",
						this.aluno.id,
						"avaliacao",
						update.id,
					]);
					this.cd.detectChanges();
				});
				this.routeHandleSelected(this.avaliacaoAtual.id);
			});
	}

	private criarNovaHandler() {
		const adjustedDto = this.getAdjustedDto();
		this.loading = true;
		if (this.validarProduto && this.integracaoZW) {
			this.produtoAvaliacao = JSON.parse(
				this.treinoConfigService.configuracoesAvaliacao.produto_avaliacao
			);
			const idProduto = parseInt(this.produtoAvaliacao, 10);
			this.avaliacaoFisicaService
				.criarAvaliacaoFisicaComProduto(this.aluno.id, idProduto, adjustedDto)
				.subscribe((nova) => {
					if (nova.id) {
						this.avaliacaoAtual = nova;
						this.loading = false;
						this.snotify.success(this.traducoes.getLabel("MENSSAGEM_SUCESSO"));
						this.loadAlunoAvaliacoes(this.aluno.id).subscribe(() => {
							this.router.navigate([
								"avaliacao",
								"avaliacoes-aluno",
								this.aluno.id,
								"avaliacao",
								nova.id,
							]);
							this.cd.detectChanges();
						});
					} else {
						if (nova === "aluno.nao.produto.vigente.avaliacao") {
							this.snotify.error(this.traducoes.getLabel("VALIDAR_PRODUTO"));
						} else if (nova === "produto.vigente.usado") {
							this.snotify.error(this.traducoes.getLabel("PRODUTO_ULTILIZADO"));
						} else {
							this.snotify.error("Ocorreu um erro ao salvar a avaliação.");
						}
						this.loading = false;
						this.cd.detectChanges();
					}
				});
		} else {
			this.avaliacaoFisicaService
				.criarAvaliacaoFisica(this.aluno.id, adjustedDto)
				.subscribe({
					next: (nova) => {
						this.avaliacaoAtual = nova;
						this.loading = false;
						this.snotify.success(this.traducoes.getLabel("MENSSAGEM_SUCESSO"));
						this.loadAlunoAvaliacoes(this.aluno.id).subscribe(() => {
							this.router.navigate([
								"avaliacao",
								"avaliacoes-aluno",
								this.aluno.id,
								"avaliacao",
								nova.id,
							]);
							this.cd.detectChanges();
						});
					},
					error: (error) => {
						this.snotify.error(this.traducoes.getLabel("MENSSAGEM_ERRO"));
						this.loading = false;
						this.cd.detectChanges();
					},
				});
		}
	}

	private isFormValid(dto) {
		if (!dto.dataAvaliacao) {
			this.snotify.error("Defina uma data");
			return false;
		}
		if (this.avaliacaoFormGroup.get("vo2").status === "INVALID") {
			this.snotify.error(this.traducoes.getLabel("MENSSAGEM_TEMPO"));
			return false;
		}
		return true;
	}

	private loadInitialData(): Observable<any> {
		const alunoId = this.route.snapshot.params.alunoId;
		const aluno$ = this.alunoService.obterAluno(alunoId);
		const anamneses$ = this.anamneseService.obterTodasAnamneses();
		const avaliacoes$ = this.loadAlunoAvaliacoes(alunoId);
		const objetivos$ =
			this.avaliacaoFisicaService.obterTodosObjetivosDeAvaliacao();
		const parq$ = this.avaliacaoFisicaService.obterParqPerguntas();
		const rml$ = this.avaliacaoFisicaService.obterValoresRML(alunoId);
		return zip(aluno$, avaliacoes$, objetivos$, anamneses$, parq$, rml$).pipe(
			map((resultado) => {
				this.aluno = resultado[0];
				this.objetivos = resultado[2];
				this.anamneses = resultado[3];
				this.parq = resultado[4];
				this.rmlConfig = resultado[5];

				this.anamnesesAtivas = this.anamneses.filter(
					(anamnese) => anamnese.ativa
				);

				return true;
			})
		);
	}

	private routeHandleNew() {
		this.avaliacaoTabs.tabId = "ANAMNESE";
		this.avaliacaoTabs.tabIndex = 0;
		this.avaliacaoFormGroup = buildAvaliacaoForm();
		const cleanDto = new AvaliacaoFisica();
		cleanDto.dataAvaliacao = new Date().valueOf();
		this.avaliacaoAtual = cleanDto;

		this.fillOutAvaliacaoForm(cleanDto);
		this.cd.detectChanges();
	}

	private routeHandleSelected(avaliacaoId) {
		this.loadAvaliacao(avaliacaoId).subscribe((avaliacao) => {
			this.fillOutAvaliacaoForm(avaliacao);
		});

		setTimeout(() => {
			this.setupOnAvaliacaoChange();
		});
		this.cd.detectChanges();
	}

	/**
	 * Convert AnamnesePerguntaResposta[] to AnamnesePerguntaDto
	 *
	 */
	private convertPerguntaToPerguntaDto(
		perguntas: AnamnesePerguntaResposta[]
	): AnamnesePerguntaDto[] {
		const result = [];
		perguntas.forEach((pergunta) => {
			result.push({
				anamnesePerguntaId: pergunta.perguntaAnamneseId,
				resposta: pergunta.resposta,
				observacao: pergunta.observacao,
			});
		});
		return result;
	}

	private routeHandleUnselected() {
		const hasOne = this.alunoAvaliacoes && this.alunoAvaliacoes.length;
		if (hasOne) {
			const id = this.alunoAvaliacoes[0].id;
			this.routeHandleSelected(id);
		} else {
			this.avaliacaoAtual = null;
		}
	}

	private createEmptyAnamneseRespostas(
		perguntas: AnamnesePergunta[]
	): AnamnesePerguntaDto[] {
		const result = [];
		perguntas.forEach((pergunta) => {
			result.push({
				anamnesePerguntaId: pergunta.id,
				resposta: null,
				observacao: null,
			});
		});
		return result;
	}

	private getAdjustedDto() {
		const dto = this.avaliacaoFormGroup.getRawValue();
		const listIndexPerguntasNaoPespondidas = [];
		dto.anamneseRespostas.forEach((result, index) => {
			if (typeof result === "object" && result.resposta) {
				dto.anamneseRespostas[index].resposta = result.resposta.toString();
			} else if (result.observacao) {
				if (result.resposta === undefined) {
					dto.anamneseRespostas[index].resposta = "";
				}
			} else {
				listIndexPerguntasNaoPespondidas.push(index);
			}
		});

		// Remover perguntas não respondidas
		let numeroItensRemovidos = 0;
		listIndexPerguntasNaoPespondidas.forEach((result) => {
			const indexAtualizado = result - numeroItensRemovidos;
			if (
				dto.anamneseRespostas[indexAtualizado].resposta === undefined ||
				dto.anamneseRespostas[indexAtualizado].resposta === null
			) {
				dto.anamneseRespostas.splice(indexAtualizado, 1);
				numeroItensRemovidos = numeroItensRemovidos + 1;
			}
		});

		if (typeof dto.pesoAltura.altura === "string") {
			dto.pesoAltura.altura = parseFloat(
				dto.pesoAltura.altura.replace(",", ".")
			);
		}
		if (typeof dto.pesoAltura.peso === "string") {
			dto.pesoAltura.peso = parseFloat(dto.pesoAltura.peso.replace(",", "."));
		}
		dto.anamneseSelecionadaId = parseInt(this.anamneseFormControl.value, 10);
		// Copies
		dto.somatotipia.dobraSupraEspinhal = dto.dobras.supraEspinhal;
		dto.somatotipia.dobraTriceps = dto.dobras.triceps;
		dto.somatotipia.dobraSubescapular = dto.dobras.subescapular;
		dto.somatotipia.dobraPanturrilha = dto.dobras.panturrilha;
		dto.somatotipia.diametroCotovelo = dto.perimetria.diametroCotovelo;
		dto.somatotipia.diametroTornozelo = dto.perimetria.diametroTornozelo;
		dto.somatotipia.diametroJoelho = dto.perimetria.diametroJoelho;
		dto.somatotipia.diametroPunho = dto.perimetria.diametroPunho;
		dto.origemAvaliacao = "Manual";
		return dto;
	}

	/**
	 * Responsible for fillout every form control needed
	 * in the page.
	 */
	private fillOutAvaliacaoForm(avaliacao: AvaliacaoFisica) {
		const ops = {};
		const fg = this.avaliacaoFormGroup;

		/**
		 * Resetar para valores padrões.
		 */
		fg.patchValue(defaultAvaliacaoDto, ops);

		/**
		 * Preencher com dados da avaliação.
		 */
		if (avaliacao && avaliacao.id) {
			fg.markAsPristine();
			fg.patchValue(avaliacao, ops);
			fg.get("postura.visaoLateral").patchValue(avaliacao.postura.visaoLateral);
			fg.get("postura.visaoPosterior").patchValue(
				avaliacao.postura.visaoPosterior
			);
			fg.get("postura.visaoAnterior").patchValue(
				avaliacao.postura.visaoAnterior
			);
			fg.get("postura.observacao").patchValue(avaliacao.postura.observacao);
		} else {
			fg.get("dataAvaliacao").setValue(new Date().valueOf(), ops);
		}

		/**
		 * Anamnese
		 */
		if (avaliacao.id && avaliacao.anamneseSelecionada) {
			const anamneseId = avaliacao.anamneseSelecionada.id;
			const anmnese = this.anamneses.find((item) => {
				return item.id === anamneseId;
			});
			this.anamneseFormControl.setValue(anamneseId);
			this.anamneseSelecionada = anmnese;
			fg.get("anamneseRespostas").setValue(
				this.convertPerguntaToPerguntaDto(avaliacao.anamneseRespostas)
			);
		} else if (this.anamneses && this.anamneses.length) {
			const anamnesesAtivas = this.anamneses.filter(
				(anamnese) => anamnese.ativa
			);
			if (anamnesesAtivas.length > 0) {
				this.anamneseFormControl.setValue(anamnesesAtivas[0].id);
				this.anamneseSelecionada = anamnesesAtivas[0];
				fg.get("anamneseRespostas").setValue(
					this.createEmptyAnamneseRespostas(this.anamneseSelecionada.perguntas),
					ops
				);
			}
		}

		/**
		 * Parq
		 */
		if (avaliacao.id) {
			fg.get("parQRespostas").setValue(
				this.convertPerguntaToPerguntaDto(avaliacao.parQRespostas),
				ops
			);
		} else {
			this.avaliacaoFormGroup
				.get("parQRespostas")
				.setValue(this.createEmptyAnamneseRespostas(this.parq), ops);
		}

		/**
		 * Imagens
		 */
		if (avaliacao.id) {
			this.avaliacaoFormGroup
				.get("postura.frenteImageId")
				.setValue(avaliacao.postura.frenteImageUri.url, ops);
			this.avaliacaoFormGroup
				.get("postura.direitaImageId")
				.setValue(avaliacao.postura.direitaImageUri.url, ops);
			this.avaliacaoFormGroup
				.get("postura.esquerdaImageId")
				.setValue(avaliacao.postura.esquerdaImageUri.url, ops);
			this.avaliacaoFormGroup
				.get("postura.costasImageId")
				.setValue(avaliacao.postura.costasImageUri.url, ops);
			this.avaliacaoFormGroup
				.get("postura.frenteImageUpload")
				.setValue(null, ops);
			this.avaliacaoFormGroup
				.get("postura.direitaImageUpload")
				.setValue(null, ops);
			this.avaliacaoFormGroup
				.get("postura.esquerdaImageUpload")
				.setValue(null, ops);
			this.avaliacaoFormGroup
				.get("postura.costasImageUpload")
				.setValue(null, ops);
		}
	}

	get anamnese() {
		return this.avaliacaoTabs.tabId === "ANAMNESE";
	}

	get parQ() {
		return this.avaliacaoTabs.tabId === "PARQ";
	}

	get pesoEAltura() {
		return this.avaliacaoTabs.tabId === "PESO_ALTURA";
	}

	get dobras() {
		return this.avaliacaoTabs.tabId === "DOBRAS";
	}

	get flexibilidade() {
		return this.avaliacaoTabs.tabId === "FLEXIBILIDADE";
	}

	get posturaSelect() {
		return this.avaliacaoTabs.tabId === "POSTURA";
	}

	get rml() {
		return this.avaliacaoTabs.tabId === "RML";
	}

	get vo2MAX() {
		return this.avaliacaoTabs.tabId === "VO2MAX";
	}

	get somatotipia() {
		return this.avaliacaoTabs.tabId === "SOMATOTIPIA";
	}

	get metaERec() {
		return this.avaliacaoTabs.tabId === "META_RECOM";
	}

	get importacaoBiosanny() {
		return this.avaliacaoTabs.tabId === "IMPORTACAO_BIOSANNY";
	}

	get perimetria() {
		return this.avaliacaoTabs.tabId === "PERIMETRIA";
	}

	get rota() {
		return ["/cadastros", "alunos", "perfil", this.aluno ? this.aluno.id : 0];
	}
}
