import { Component, OnInit, ViewChild } from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiAvaliacaoFisicaService } from "treino-api";

@Component({
	selector: "pacto-avaliacao-fisica-modal",
	templateUrl: "./avaliacao-fisica-modal.component.html",
	styleUrls: ["./avaliacao-fisica-modal.component.scss"],
})
export class AvaliacaoFisicaModalComponent implements OnInit {
	avaliacaoId: number;
	@ViewChild("enviadoComSucesso", { static: true }) enviadoComSucesso;
	@ViewChild("envioEmailErro", { static: true }) envioEmailErro;
	@ViewChild("envioWppErro", { static: true }) envioWppErro;
	@ViewChild("popupErro", { static: true }) popupErro;
	listaTelefones;
	listaEmails;
	telefoneAtual: string;
	emailAtual: string;

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal,
		private avaliacaoService: TreinoApiAvaliacaoFisicaService
	) {}

	ngOnInit() {
		this.popularSelect();
	}

	popularSelect() {
		this.telefoneAtual = this.listaTelefones[0] ? this.listaTelefones[0] : "";
		this.emailAtual = this.listaEmails[0] ? this.listaEmails[0] : "";
	}

	dismiss() {
		this.openModal.dismiss();
	}

	enviarEmail() {
		this.avaliacaoService
			.enviarEmailAvaliacaoFisica(this.avaliacaoId, this.emailAtual)
			.subscribe(
				(avaliacao) => {
					const enviadoComSucesso =
						this.enviadoComSucesso.nativeElement.innerHTML;
					this.snotifyService.success(enviadoComSucesso);
				},
				(erro) => {
					const envioEmailErro = this.envioEmailErro.nativeElement.innerHTML;
					this.snotifyService.error(envioEmailErro);
				}
			);
	}

	enviarWpp() {
		this.avaliacaoService
			.enviarWppAvaliacaoFisica(this.avaliacaoId, this.telefoneAtual)
			.subscribe(
				(result) => {
					const newWin = window.open(result);

					if (
						!newWin ||
						newWin.closed ||
						typeof newWin.closed === "undefined"
					) {
						const erroPopup = this.popupErro.nativeElement.innerHTML;
						this.snotifyService.warning(erroPopup);
					}
				},
				(erro) => {
					const envioWppErro = this.envioWppErro.nativeElement.innerHTML;
					this.snotifyService.error(envioWppErro);
				}
			);
	}
}
