@import "../../../../assets/scss/variable";
@import "../../../../assets/scss/functions";
@import "../../../../assets/scss/pacto/plataforma-import";

.tabela-grupos {
	display: flex;
	height: 48px;

	.colum {
		margin: 12px 24px 0px 24px;
		@extend .type-h6;
	}

	.colum-1 {
		width: 240px;
	}

	.colum-2 {
		margin-left: 111px;
	}

	.block-column {
		display: flex;
	}
}

.posicao-tabela {
	margin-left: 50px;
}

.colunas {
	display: flex;
	margin: 10px 30px 27px 50px;

	.coluna-03 {
		margin-left: 265px;
	}
}

.titulo {
	margin-left: 50px;
	width: 100px;
	height: 12px;
	color: $pretoPri;
	font-size: 17px;
	font-weight: 600;
}

.posicao-btn {
	margin: 0 30px 10px 25px;
	justify-content: space-between;
	display: flex;
}

table.table {
	margin-bottom: 30px;

	tr {
		@extend .type-p-small;
		color: $gelo04;
		cursor: pointer;
	}

	.column-cell.hover-cell:hover {
		cursor: pointer;
		text-decoration: underline;
	}
}
