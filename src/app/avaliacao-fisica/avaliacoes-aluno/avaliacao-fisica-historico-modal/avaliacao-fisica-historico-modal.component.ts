import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AvaliacaoFisica,
	TreinoApiAvaliacaoFisicaService,
	TreinoApiAlunosService,
} from "treino-api";
import { FormArray, FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-avaliacao-fisica-historico-modal",
	templateUrl: "./avaliacao-fisica-historico-modal.component.html",
	styleUrls: ["./avaliacao-fisica-historico-modal.component.scss"],
})
export class AvaliacaoFisicaHistoricoModalComponent implements OnInit {
	@ViewChild("popupErro", { static: true }) popupErro;
	@ViewChild("imprimirErro", { static: true }) imprimirErro;
	@ViewChild("envioWppErro", { static: true }) envioWppErro;

	avaliacaoId: number;
	alunoId: number;
	listaAvaliacaoFisica: Array<AvaliacaoFisica>;
	linkPdf;
	objectoId: { id: number };
	listaIds: Array<any> = [];
	telefone: FormControl = new FormControl();

	formGroup: FormGroup = new FormGroup({});

	constructor(
		private avaliacaoService: TreinoApiAvaliacaoFisicaService,
		private alunoService: TreinoApiAlunosService,
		private formBuilder: FormBuilder,
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.formGroup = this.formBuilder.group({
			listaAvaliacaoFisica: this.buildAvaliacao(),
		});
	}

	buildAvaliacao() {
		const values = this.listaAvaliacaoFisica.map((v) => new FormControl(false));
		return this.formBuilder.array(values);
	}

	get formData() {
		return this.formGroup.get("listaAvaliacaoFisica") as FormArray;
	}

	dismiss() {
		this.openModal.dismiss();
	}

	selecionar(index: any) {
		const formValue = this.formGroup.get("listaAvaliacaoFisica").value[index];
		this.formGroup.get("listaAvaliacaoFisica").value[index] = !formValue;
		this.formData.setValue(this.formGroup.get("listaAvaliacaoFisica").value);
	}

	imprimirHandler() {
		let valueSubmit = Object.assign({}, this.formGroup.value);
		valueSubmit = Object.assign(valueSubmit, {
			listaAvaliacaoFisica: valueSubmit.listaAvaliacaoFisica
				.map((v, i) => (v ? this.listaAvaliacaoFisica[i] : null))
				.filter((v) => v !== null),
		});
		if (valueSubmit.listaAvaliacaoFisica.length === 1) {
			valueSubmit.listaAvaliacaoFisica.forEach((avaliacaoAtual) => {
				this.avaliacaoService.obterLinkPdf(avaliacaoAtual.id).subscribe(
					(result) => {
						const newWin = window.open(result);
						if (
							!newWin ||
							newWin.closed ||
							typeof newWin.closed === "undefined"
						) {
							const erroPopup = this.popupErro.nativeElement.innerHTML;
							this.snotifyService.warning(erroPopup);
						}
					},
					(error1) => {
						const imprimir = this.imprimirErro.nativeElement.innerHTML;
						this.snotifyService.error(imprimir);
					}
				);
			});
		} else {
			this.listaIds = [];
			valueSubmit.listaAvaliacaoFisica.forEach((avaliacaoId) => {
				this.objectoId = avaliacaoId.id;
				this.listaIds.push(this.objectoId);
			});

			if (this.listaIds.length > 1 && this.listaIds.length <= 5) {
				this.avaliacaoService
					.obterLinkPdfComparativo(this.listaIds)
					.subscribe((result) => {
						const newWin = window.open(result);
						if (
							!newWin ||
							newWin.closed ||
							typeof newWin.closed === "undefined"
						) {
							const erroPopup = this.popupErro.nativeElement.innerHTML;
							this.snotifyService.warning(erroPopup);
						}
					});
			} else if (this.listaIds.length === 0) {
				this.snotifyService.error(
					"Selecione mais de uma avaliação para imprimir!"
				);
			} else {
				this.snotifyService.error(
					"Selecione no máximo 5 avaliações para prosseguir!"
				);
			}
		}
	}

	enviarEmailComparativo() {
		let valueSubmit = Object.assign({}, this.formGroup.value);
		valueSubmit = Object.assign(valueSubmit, {
			listaAvaliacaoFisica: valueSubmit.listaAvaliacaoFisica
				.map((v, i) => (v ? this.listaAvaliacaoFisica[i] : null))
				.filter((v) => v !== null),
		});
		this.listaIds = [];
		valueSubmit.listaAvaliacaoFisica.forEach((avaliacaoId) => {
			this.objectoId = avaliacaoId.id;
			this.listaIds.push(this.objectoId);
		});
		if (this.listaIds.length > 1 && this.listaIds.length <= 5) {
			this.avaliacaoService
				.enviarEmailPdfComparativo(this.listaIds)
				.subscribe((result) => {
					this.snotifyService.success("Email enviado com sucesso!");
				});
		} else if (this.listaIds.length === 0) {
			this.snotifyService.error("Selecione mais de uma avaliação para enviar!");
		} else {
			this.snotifyService.error(
				"Selecione no máximo 5 avaliações para prosseguir!"
			);
		}
	}

	enviarWppComparativo() {
		let valueSubmit = Object.assign({}, this.formGroup.value);
		valueSubmit = Object.assign(valueSubmit, {
			listaAvaliacaoFisica: valueSubmit.listaAvaliacaoFisica
				.map((v, i) => (v ? this.listaAvaliacaoFisica[i] : null))
				.filter((v) => v !== null),
		});
		this.listaIds = [];
		valueSubmit.listaAvaliacaoFisica.forEach((avaliacaoId) => {
			this.objectoId = avaliacaoId.id;
			this.listaIds.push(this.objectoId);
		});
		if (this.listaIds.length > 1 && this.listaIds.length <= 5) {
			this.avaliacaoService
				.enviarWppAvaliacaoFisicaComparativo(this.listaIds, this.alunoId)
				.subscribe(
					(result1) => {
						const newWin = window.open(result1);
						if (
							!newWin ||
							newWin.closed ||
							typeof newWin.closed === "undefined"
						) {
							const erroPopup = this.popupErro.nativeElement.innerHTML;
							this.snotifyService.warning(erroPopup);
						}
					},
					(erro) => {
						const envioWppErro = this.envioWppErro.nativeElement.innerHTML;
						this.snotifyService.error(envioWppErro);
					}
				);
		} else if (this.listaIds.length === 0) {
			this.snotifyService.error("Selecione mais de uma avaliação para enviar!");
		} else {
			this.snotifyService.error(
				"Selecione no máximo 5 avaliações para prosseguir!"
			);
		}
	}
}
