<div class="modal-body">
	<div [maxHeight]="'300px'" class="tabela-scroll" pactoCatSmoothScroll>
		<table class="table">
			<div class="colunas">
				<div class="titulo">Professor</div>
				<div class="titulo coluna-03">Lançada em</div>
			</div>

			<tbody>
				<tr *ngFor="let item of formData?.controls; let i = index">
					<div class="tabela-grupos">
						<div class="colum col-check">
							<pacto-cat-checkbox
								[control]="item"
								[id]="i"></pacto-cat-checkbox>
						</div>
						<div (click)="selecionar(i)" class="block-column">
							<div class="colum colum-1">
								{{
									listaAvaliacaoFisica[i].avaliador
										? listaAvaliacaoFisica[i].avaliador.nome
										: ""
								}}
							</div>
							<div class="colum colum-2">
								{{
									listaAvaliacaoFisica[i].dataAvaliacao | date : "dd/MM/yyyy"
								}}
							</div>
						</div>
					</div>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="posicao-btn">
		<div>
			<pacto-cat-button
				(click)="imprimirHandler()"
				[icon]="'printer'"
				[label]="'IMPRIMIR COMPARAÇÃO'"></pacto-cat-button>
		</div>
		<div>
			<pacto-cat-button
				(click)="enviarEmailComparativo()"
				[icon]="'send'"
				[label]="'ENVIAR POR E-MAIL'"></pacto-cat-button>
		</div>
		<div>
			<pacto-cat-button
				(click)="enviarWppComparativo()"
				[icon]="'phone-forwarded'"
				[label]="'ENVIAR PELO WHATSAPP'"></pacto-cat-button>
		</div>
	</div>
</div>
<span #popupErro [hidden]="true">
	Verifique se o seu Pop-ups nao esta bloqueado e tente novamente.
</span>
<span #imprimirErro [hidden]="true">
	Não foi possível imprimir esta avaliação. Selecione mais de uma avaliação.
</span>
<span #envioWppErro [hidden]="true">
	Menssagem nao enviada verifique o telefone cadastrado.
</span>
