import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	Vo2MaxAerobicoBancoColuna,
	Vo2MaxAerobicoBancoLinha,
	Vo2MaxCaminhadaCorrida12Coluna,
	Vo2MaxCaminhadaCorrida12Linha,
	Vo2MaxProtocolo,
	Vo2MaxTeste2400Coluna,
	Vo2MaxTeste2400Linha,
	AvaliacaoFisica,
	TreinoApiAvaliacaoFisicaService,
	TreinoApiAvaliacaoCatalogoService,
} from "treino-api";
import { Observable, Subscription } from "rxjs";
import { PerfilAcessoRecursoNome } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { map } from "rxjs/operators";
import { Router } from "@angular/router";
import { TraducoesXinglingComponent } from "ui-kit";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";

@Component({
	selector: "pacto-avaliacao-editor-vo2max",
	templateUrl: "./avaliacao-editor-vo2max.component.html",
	styleUrls: ["./avaliacao-editor-vo2max.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoEditorVo2maxComponent implements OnInit, OnDestroy {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;
	@Input() aluno;
	@Input() configuracao;
	@Input() formGroupAvaliacao: FormGroup;

	avaliacaoFormGroup: FormGroup = new FormGroup({
		astrandFrequencia4: new FormControl(0),
		astrandFrequencia5: new FormControl(0),
		astrandCarga: new FormControl(0),
	});
	alunoAvaliacoes: AvaliacaoFisica[];
	produtoAvaliacao: any;

	get refCaminhadaCol() {
		if (this.avaliacao && this.avaliacao.vo2) {
			return this.avaliacao.vo2.caminhadaCorridaColuna;
		}
	}

	get refCaminhadaLin() {
		if (this.avaliacao && this.avaliacao.vo2) {
			return this.avaliacao.vo2.caminhadaCorridaLinha;
		}
	}

	get refTeste2400Col() {
		if (this.avaliacao && this.avaliacao.vo2) {
			return this.avaliacao.vo2.teste2400MetrosColuna;
		}
	}

	get refTeste2400Lin() {
		if (this.avaliacao && this.avaliacao.vo2) {
			return this.avaliacao.vo2.teste2400MetrosLinha;
		}
	}

	get refAerobicoCol() {
		if (this.avaliacao && this.avaliacao.vo2) {
			return this.avaliacao.vo2.aerobicobancoColuna;
		}
	}

	get refAerobicoLin() {
		if (this.avaliacao && this.avaliacao.vo2) {
			return this.avaliacao.vo2.aerobicoBancoLinha;
		}
	}

	caminhadaReferencia = {
		colunas: [
			{
				labelKey: "coluna13a19",
				value: Vo2MaxCaminhadaCorrida12Coluna.ENTRE_13_19,
			},
			{
				labelKey: "coluna20a29",
				value: Vo2MaxCaminhadaCorrida12Coluna.ENTREO_20_29,
			},
			{
				labelKey: "coluna30a39",
				value: Vo2MaxCaminhadaCorrida12Coluna.ENTREO_30_39,
			},
			{
				labelKey: "coluna40a49",
				value: Vo2MaxCaminhadaCorrida12Coluna.ENTREO_40_49,
			},
			{
				labelKey: "coluna50a59",
				value: Vo2MaxCaminhadaCorrida12Coluna.ENTREO_50_59,
			},
			{
				labelKey: "colunaMaior60",
				value: Vo2MaxCaminhadaCorrida12Coluna.MAIOR_60,
			},
		],
		linhas: [
			{
				labelKey: "muitoFraca",
				value: Vo2MaxCaminhadaCorrida12Linha.MUITA_FRACA,
			},
			{ labelKey: "fraca", value: Vo2MaxCaminhadaCorrida12Linha.FRACA },
			{ labelKey: "media", value: Vo2MaxCaminhadaCorrida12Linha.MEDIA },
			{ labelKey: "boa", value: Vo2MaxCaminhadaCorrida12Linha.BOA },
			{ labelKey: "excelente", value: Vo2MaxCaminhadaCorrida12Linha.EXCELENTE },
			{ labelKey: "superior", value: Vo2MaxCaminhadaCorrida12Linha.SUPERIOR },
		],
		conteudo: [],
	};

	teste2400Referencia = {
		colunas: [
			{ labelKey: "testeMenor30", value: Vo2MaxTeste2400Coluna.MENOR_30 },
			{ labelKey: "teste30e39", value: Vo2MaxTeste2400Coluna.ENTRE_30_39 },
			{ labelKey: "teste40e49", value: Vo2MaxTeste2400Coluna.ENTRE_40_49 },
			{ labelKey: "testeMaior50", value: Vo2MaxTeste2400Coluna.MAIOR_50 },
		],
		linhas: [
			{ labelKey: "testeMuitoFraca", value: Vo2MaxTeste2400Linha.MUITA_FRACA },
			{ labelKey: "testeFraca", value: Vo2MaxTeste2400Linha.FRACA },
			{ labelKey: "testeMedia", value: Vo2MaxTeste2400Linha.MEDIA },
			{ labelKey: "testeBoa", value: Vo2MaxTeste2400Linha.BOA },
			{ labelKey: "testeExcelente", value: Vo2MaxTeste2400Linha.EXCELENTE },
		],
		conteudo: [
			["+16:30", "+17:30", "+18:30", "+19:00"],
			["16:30 - 14:31", "17:30 - 15:31", "18:30 - 16:31", "19:00 - 17:01"],
			["14:30 - 12:01", "15:30 - 13:01", "16:30 - 14:01", "17:00 - 14:31"],
			["12:00 - 10:16", "13:00 - 11:01", "14:00 - 11:39", "14:30 - 12:01"],
			["-10:15", "-11:00", "-11:38", "-12:00"],
		],
	};

	aerobicoReferencia = {
		colunas: [
			{
				labelKey: "aerobico20e29",
				value: Vo2MaxAerobicoBancoColuna.ENTREO_20_29,
			},
			{
				labelKey: "aerobico30e39",
				value: Vo2MaxAerobicoBancoColuna.ENTREO_30_39,
			},
			{
				labelKey: "aerobico40e49",
				value: Vo2MaxAerobicoBancoColuna.ENTREO_40_49,
			},
			{
				labelKey: "aerobico50e59",
				value: Vo2MaxAerobicoBancoColuna.ENTREO_50_59,
			},
			{
				labelKey: "aerobicoMaior60",
				value: Vo2MaxAerobicoBancoColuna.MAIOR_60,
			},
		],
		linhas: [
			{
				labelKey: "aerobicoAtletica",
				value: Vo2MaxAerobicoBancoLinha.CONDICAO_ATLETICA,
			},
			{
				labelKey: "aerobicoRecomendavel",
				value: Vo2MaxAerobicoBancoLinha.FAIXA_RECOMENDAVEL,
			},
			{
				labelKey: "aerobicoBaixaAptidao",
				value: Vo2MaxAerobicoBancoLinha.BAIXA_APTIDAO,
			},
			{
				labelKey: "aerobicoRisco",
				value: Vo2MaxAerobicoBancoLinha.CONDICAO_RISCO,
			},
		],
		conteudo: [],
	};

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private snotify: SnotifyService,
		private avaliacaoFisicaService: TreinoApiAvaliacaoFisicaService,
		private avaliacaoCatalogoService: TreinoApiAvaliacaoCatalogoService,
		private treinoConfigService: TreinoConfigCacheService,
		private sessionService: SessionService
	) {}

	changeSubscription: Subscription;

	ngOnInit() {
		this.popularCaminhadaReferencia();
		this.formGroup.get("collegeFrequencia").valueChanges.subscribe((result) => {
			this.formGroup
				.get("collegeFrequencia")
				.setValue(result, { emitEvent: false });
		});
	}

	ngOnDestroy() {}

	get Vo2MaxProtocolo() {
		return Vo2MaxProtocolo;
	}

	popularCaminhadaReferencia() {
		if (this.aluno.sexo === "M") {
			this.caminhadaReferencia.conteudo.push(
				["2090(m)", "1960(m)", "1900(m)", "1830(m)", "1660(m)", "1400(m)"],
				[
					"2091 2200(m)",
					"1961 2110(m)",
					"1901 2090(m)",
					"1831 1990(m)",
					"1661 1870(m)",
					"1401 1640(m)",
				],
				[
					"2201 2510(m)",
					"2111 2400(m)",
					"2091 2400(m)",
					"1991 2240(m)",
					"1871 2090(m)",
					"1641 1930(m)",
				],
				[
					"2511 2770(m)",
					"2401 2640(m)",
					"2401 2510(m)",
					"2241 2460(m)",
					"2091 2320(m)",
					"1931 2120(m)",
				],
				[
					"2771 3000(m)",
					"2641 2830(m)",
					"2511 2720(m)",
					"2461 2660(m)",
					"2321 2540(m)",
					"2121 2490(m)",
				],
				["3000+(m)", "2830+(m)", "2720+(m)", "2660+(m)", "2540+(m)", "2490+(m)"]
			);
			this.aerobicoReferencia.conteudo.push(
				[">=49", ">=48", ">=45", ">=42", ">=39"],
				["42-48", "40-47", "38-44", "35-41", "31-38"],
				["38-41", "36-39", "34-37", "31-34", "27-30"],
				["<=37", "<=35", "<=33", "<=30", "<=26"]
			);
		} else {
			this.caminhadaReferencia.conteudo.push(
				["1610(m)", "1550(m)", "1510(m)", "1420(m)", "1350(m)", "1260(m)"],
				[
					"1611 1900(m)",
					"1551 1790(m)",
					"1511 1690(m)",
					"1421 1580(m)",
					"1351 1500(m)",
					"1261 1390(m)",
				],
				[
					"1901 2080(m)",
					"1791 1970(m)",
					"1691 1960(m)",
					"1581 1790(m)",
					"1501 1690(m)",
					"1391 1590(m)",
				],
				[
					"2081 2300(m)",
					"1971 2160(m)",
					"1961 2080(m)",
					"1791 2000(m)",
					"1691 1900(m)",
					"1591 1750(m)",
				],
				[
					"2301 2430(m)",
					"2161 2330(m)",
					"2081 2240(m)",
					"2001 2160(m)",
					"1901 2090(m)",
					"1751 1900(m)",
				],
				["2430+(m)", "2330+(m)", "2240+(m)", "2160+(m)", "2090+(m)", "1900+(m)"]
			);

			this.aerobicoReferencia.conteudo.push(
				[">=42", ">=40", ">=37", ">=33", ">=32"],
				["35-41", "33-39", "31-36", "28-32", "26-31"],
				["32-34", "30-32", "28-30", "25-27", "24-25"],
				["<=31", "<=29", "<=27", "<=24", "<=23"]
			);
		}
	}

	calcularHandler() {
		const permissao = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.LANCAR_AVALICAO_RETROATIVA
		);
		if (this.avaliacao.id != null) {
			if (permissao && permissao.editar) {
				this.avaliacaoFormGroup
					.get("astrandFrequencia4")
					.setValue(this.formGroup.get("astrandFrequencia4").value);
				this.avaliacaoFormGroup
					.get("astrandFrequencia5")
					.setValue(this.formGroup.get("astrandFrequencia5").value);
				this.avaliacaoFormGroup
					.get("astrandCarga")
					.setValue(this.formGroup.get("astrandCarga").value);
				const dto = this.avaliacaoFormGroup.getRawValue();
				this.avaliacaoFisicaService
					.calcularVo2AvaliacaoFisica(this.avaliacao.id, dto)
					.subscribe((update) => {
						this.avaliacao = update;
						this.loadAlunoAvaliacoes(this.aluno.id).subscribe(() => {
							this.router.navigate([
								"avaliacao",
								"avaliacoes-aluno",
								this.aluno.id,
								"avaliacao",
								update.id,
							]);
							this.cd.detectChanges();
						});
					});
			} else {
				this.snotify.warning(
					"Seu usuário não possui permissão, procure seu administrador"
				);
			}
		} else {
			if (permissao && permissao.incluir) {
				const dto = this.formGroupAvaliacao.getRawValue();
				if (this.isFormValid(dto) && this.validarAstrand(dto)) {
					this.produtoAvaliacao = JSON.parse(
						this.treinoConfigService.configuracoesAvaliacao.produto_avaliacao
					);
					const idProduto = parseInt(this.produtoAvaliacao, 10);
					this.avaliacaoFisicaService
						.criarAvaliacaoFisicaComProduto(this.aluno.id, idProduto, dto)
						.subscribe((nova) => {
							if (nova.id) {
								this.avaliacao = nova;
								this.loadAlunoAvaliacoes(this.aluno.id).subscribe(() => {
									this.router.navigate([
										"avaliacao",
										"avaliacoes-aluno",
										this.aluno.id,
										"avaliacao",
										nova.id,
									]);
									this.cd.detectChanges();
								});
							}
						});
				}
			} else {
				this.snotify.warning(
					"Seu usuário não possui permissão, procure seu administrador"
				);
			}
		}
	}

	private loadAlunoAvaliacoes(alunoId): Observable<any> {
		return this.avaliacaoCatalogoService
			.obterTodasAvaliacoesAluno(alunoId)
			.pipe(
				map((avaliacoes) => {
					this.alunoAvaliacoes = avaliacoes;
					return true;
				})
			);
	}

	validarAstrand(adjustedDto) {
		if (
			adjustedDto.vo2.astrandCarga ||
			adjustedDto.vo2.astrandFrequencia4 ||
			adjustedDto.vo2.astrandFrequencia5
		) {
			if (!adjustedDto.pesoAltura.peso) {
				this.snotify.warning(this.traducoes.getLabel("VALIDACAOASTRAND"));
				return false;
			} else {
				return true;
			}
		} else {
			return true;
		}
	}

	private isFormValid(dto) {
		if (!dto.dataAvaliacao) {
			this.snotify.error("Defina uma data");
			return false;
		}
		if (this.formGroupAvaliacao.get("vo2").status === "INVALID") {
			this.snotify.error(this.traducoes.getLabel("MENSSAGEM_TEMPO"));
			return false;
		}
		return true;
	}
}
