.editor-vo2max-wrapper {
	padding: 20px;
	background-color: #fff;
}

.teste-submaximo {
	margin-top: 20px;
}

.category {
	margin-bottom: 30px;

	.category-title {
		font-weight: 600;
		font-size: 17px;
		color: #777;
		border-bottom: 1px solid rgb(170, 170, 170);
		margin-bottom: 5px;
		padding-bottom: 5px;
	}

	.category-descrition {
		font-size: 14px;
		color: #444;
		padding: 10px;
		text-align: justify;
	}

	.category-button {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 1rem;
	}

	.items .item {
		display: flex;
		background-color: #efefef;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		.item-label {
			flex-grow: 1;
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
			padding-left: 20px;

			input {
				width: 60px;
			}
		}
	}

	.item.header {
		display: flex;
		flex-direction: row-reverse;

		.header-cel {
			font-weight: 600;
			font-size: 17px;
			width: 100px;
			text-align: center;
		}
	}
}

.reference-table {
	font-size: 17px;

	.linha {
		display: flex;
		background-color: #efefef;
		line-height: 42px;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		&:first-child {
			font-weight: 600;
		}

		.item.active {
			background-color: #6f6f6f;
			color: #fff;
			font-weight: 600;
		}

		.item {
			width: 16.6%;
			flex-grow: 0;
			text-align: center;
			flex-shrink: 0;
		}
	}

	&.caminhada .linha .item {
		width: 14.2%;
	}

	&.teste2400 .linha .item {
		width: 20%;
	}

	&.aerobico .linha .item {
		width: 16.6%;
	}
}
