<div class="editor-vo2max-wrapper">
	<div *ngIf="configuracao.cfg_ventilometria" class="category">
		<div class="category-title" i18n="@@editor-vo2max:ventilometria-vo2">
			VENTILOMETRIA VO2
		</div>
		<div class="items">
			<div class="item">
				<div class="item-label" i18n="@@editor-vo2max:vo2max">Vo2 Max</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('vo2Max')"
						[id]="'vo2Max'"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-vo2max:limiar-ventilatorio-1">
					Limiar ventilatório I
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('limiarVentilatorioI')"
						[id]="'limiarVentilatorioI'"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-vo2max:limiar-ventilatorio-2">
					Limiar ventilatório II
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('limiarVentilatorioII')"
						[id]="'limiarVentilatorioII'"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>

	<!-- PROTOCOLO -->
	<div class="protocolo-wrapper">
		<div class="category">
			<div
				*ngIf="configuracao.cfg_testes_campo"
				class="category-title"
				i18n="@@editor-vo2max:ventilometria-vo2">
				PROTOCOLOS DE CAMPO
			</div>

			<!-- Seletor de protocolo -->
			<div *ngIf="configuracao.cfg_testes_campo" class="row">
				<div class="form-group col-md-6">
					<label class="control-label" i18n="@@editor-vo2max:teste">
						Teste:
					</label>
					<select
						[formControl]="formGroup.get('protocolo')"
						class="form-control form-control-sm">
						<option
							i18n="@@editor-vo2max:caminhada-ou-corida"
							value="{{ Vo2MaxProtocolo.VO_CAMINHADA_CORRIDA_12_MINUTOS }}">
							Caminhada ou corrida de 12 minutos
						</option>
						<option
							i18n="@@editor-vo2max:teste-2400-metros"
							value="{{ Vo2MaxProtocolo.VO_CAMINHADA_2400_M }}">
							Teste de 2400 metros
						</option>
						<option
							i18n="@@editor-vo2max:aerobico-banco"
							value="{{ Vo2MaxProtocolo.VO_AEROBICO_DE_BANCO }}">
							Aeróbico de banco
						</option>
					</select>
				</div>
			</div>

			<ng-container [ngSwitch]="formGroup.get('protocolo').value">
				<!-- Caminhada -->
				<div *ngIf="configuracao.cfg_testes_campo">
					<div
						*ngSwitchCase="Vo2MaxProtocolo.VO_CAMINHADA_CORRIDA_12_MINUTOS"
						class="protocolo">
						<div class="items">
							<div class="item">
								<div
									class="item-label"
									i18n="@@editor-vo2max:distancia-percorrida">
									Distância percorrida (m):
								</div>
								<div class="input-column">
									<pacto-avaliacao-input-number
										[control]="formGroup.get('caminhadaCorridaDistancia')"
										[id]="
											'caminhadaCorridaDistancia'
										"></pacto-avaliacao-input-number>
								</div>
							</div>
							<div class="item">
								<div class="item-label" i18n="@@editor-vo2max:vo2-maximo">
									VO2 Máximo (ml/kg/min):
								</div>
								<div class="input-column">
									{{ avaliacao?.vo2?.caminhadaCorridaVo2Max }}
								</div>
							</div>
						</div>
						<!-- Referência caminhada -->
						<div class="reference-table caminhada">
							<div class="linha">
								<div class="item" i18n="@@editor-vo2max:categoria">
									Categoria
								</div>
								<div
									*ngFor="let coluna of caminhadaReferencia.colunas"
									class="item">
									<ng-container
										*ngTemplateOutlet="
											tradutor;
											context: { item: coluna.labelKey }
										"></ng-container>
								</div>
							</div>
							<div
								*ngFor="
									let linha of caminhadaReferencia.linhas;
									let linhaIndex = index
								"
								class="linha">
								<div class="item">
									<ng-container
										*ngTemplateOutlet="
											tradutor;
											context: {
												item: caminhadaReferencia.linhas[linhaIndex].labelKey
											}
										"></ng-container>
								</div>
								<div
									*ngFor="
										let coluna of caminhadaReferencia.colunas;
										let colunaIndex = index
									"
									[ngClass]="{
										active:
											refCaminhadaCol === coluna.value &&
											refCaminhadaLin === linha.value
									}"
									class="item">
									{{ caminhadaReferencia.conteudo[linhaIndex][colunaIndex] }}
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Referencia protocolo: 2400 -->
				<div
					*ngSwitchCase="Vo2MaxProtocolo.VO_CAMINHADA_2400_M"
					class="protocolo">
					<!-- Caminhada -->
					<div class="items">
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:tempo-percorrido">
								Tempo percorrido (hr:min):
							</div>
							<div class="input-column">
								<pacto-input-time
									[formControl]="formGroup.get('teste2400MetrosTempo')"
									[id]="'teste2400MetrosTempo'"
									[inputId]="'tempoVo2Max'"></pacto-input-time>
							</div>
						</div>
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:vo2-maximo">
								VO2 Máximo (ml/kg/min):
							</div>
							<div class="input-column">
								{{ avaliacao?.vo2?.teste2400MetrosVo2Max }}
							</div>
						</div>
					</div>
					<!-- Referência teste 2400 -->
					<div class="reference-table teste2400">
						<div class="linha">
							<div class="item" i18n="@@editor-vo2max:categoria">Categoria</div>
							<div
								*ngFor="let coluna of teste2400Referencia.colunas"
								class="item">
								<ng-container
									*ngTemplateOutlet="
										tradutor;
										context: { item: coluna.labelKey }
									"></ng-container>
							</div>
						</div>
						<div
							*ngFor="
								let linha of teste2400Referencia.linhas;
								let linhaIndex = index
							"
							class="linha">
							<div class="item">
								<ng-container
									*ngTemplateOutlet="
										tradutor;
										context: {
											item: teste2400Referencia.linhas[linhaIndex].labelKey
										}
									"></ng-container>
							</div>
							<div
								*ngFor="
									let coluna of teste2400Referencia.colunas;
									let colunaIndex = index
								"
								[ngClass]="{
									active:
										refTeste2400Col === coluna.value &&
										refTeste2400Lin === linha.value
								}"
								class="item">
								{{ teste2400Referencia.conteudo[linhaIndex][colunaIndex] }}
							</div>
						</div>
					</div>
				</div>

				<div
					*ngSwitchCase="Vo2MaxProtocolo.VO_AEROBICO_DE_BANCO"
					class="protocolo">
					<!-- Caminhada -->
					<div class="items">
						<div class="item">
							<div
								class="item-label"
								i18n="@@editor-vo2max:frequencia-cardiaca">
								Frequência cardíaca (bpm):
							</div>
							<div class="input-column">
								<pacto-avaliacao-input-number
									[control]="formGroup.get('collegeFrequencia')"
									[id]="'collegeFrequencia'"></pacto-avaliacao-input-number>
							</div>
						</div>
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:vo2-maximo">
								VO2 Máximo (ml/kg/min):
							</div>
							<div class="input-column">
								{{ avaliacao?.vo2?.collegeVo2Maximo }}
							</div>
						</div>
					</div>
					<!-- Referência aerobico -->
					<div class="reference-table aerobico">
						<div class="linha">
							<div class="item" i18n="@@editor-vo2max:categoria">Categoria</div>
							<div
								*ngFor="let coluna of aerobicoReferencia.colunas"
								class="item">
								<ng-container
									*ngTemplateOutlet="
										tradutor;
										context: { item: coluna.labelKey }
									"></ng-container>
							</div>
						</div>
						<div
							*ngFor="
								let linha of aerobicoReferencia.linhas;
								let linhaIndex = index
							"
							class="linha">
							<div class="item">
								<ng-container
									*ngTemplateOutlet="
										tradutor;
										context: {
											item: aerobicoReferencia.linhas[linhaIndex].labelKey
										}
									"></ng-container>
							</div>
							<div
								*ngFor="
									let coluna of aerobicoReferencia.colunas;
									let colunaIndex = index
								"
								[ngClass]="{
									active:
										refAerobicoCol === coluna.value &&
										refAerobicoLin === linha.value
								}"
								class="item">
								{{ aerobicoReferencia.conteudo[linhaIndex][colunaIndex] }}
							</div>
						</div>
					</div>
				</div>

				<div
					*ngIf="configuracao.cfg_teste_bike"
					class="category teste-submaximo">
					<div
						class="category-title"
						i18n="@@editor-vo2max:teste-submaximo-astrand">
						TESTE SUBMÁXIMO DE ASTRAND EM CICLOERGÔMETRO (BICICLETA)
					</div>
					<div
						class="category-descrition"
						i18n="@@editor-vo2max:descrition:teste-submaximo-astrand">
						VO2 máximo é a quantidade máxima de oxigênio que seu organismo
						consegue absorver a cada respiração. Também chamado de potência
						aeróbica máxima, esse é um indicador da aptidão aeróbica de um
						atleta.
						<br />
						<b>
							<strong>
								Para o sexo masculino a carga deve variar entre 100 a 150 Watts
								e para mulheres entre 50 a 100 Watts.
							</strong>
						</b>
						<br />
						O avaliado deverá pedalar durante 5 minutos; registra-se a FC do 4º
						e 5º minutos, e se obtém o valor médio. A FC de carga deverá estar
						entre 120 e 170 bpm e , preferencialmente acima de 140 para os
						jovens.
					</div>
					<div class="items">
						<div class="item">
							<div
								class="item-label"
								i18n="@@editor-vo2max:frequencia-cardiaca">
								Frequência cardíaca no 4º minuto (bpm)
							</div>
							<div class="input-column">
								<pacto-avaliacao-input-number
									[control]="formGroup.get('astrandFrequencia4')"
									[id]="'astrandFrequencia4'"></pacto-avaliacao-input-number>
							</div>
						</div>
						<div class="item">
							<div
								class="item-label"
								i18n="@@editor-vo2max:frequencia-cardiaca">
								Frequência cardíaca no 5º minuto (bpm)
							</div>
							<div class="input-column">
								<pacto-avaliacao-input-number
									[control]="formGroup.get('astrandFrequencia5')"
									[id]="'astrandFrequencia5'"></pacto-avaliacao-input-number>
							</div>
						</div>
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:carga">
								Carga (kg/watts) 1 kg = 50 watts
							</div>
							<div class="input-column">
								<pacto-avaliacao-input-number
									[control]="formGroup.get('astrandCarga')"
									[id]="'astrandCarga'"></pacto-avaliacao-input-number>
							</div>
						</div>
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:vo2-estimado">
								VO2 estimado (l):
							</div>
							<div class="input-column">
								{{ avaliacao?.vo2?.astrandVo2Estimado }}
							</div>
						</div>
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:vo2-maximo">
								VO2 Máximo (ml/kg/min):
							</div>
							<div class="input-column">
								{{ avaliacao?.vo2?.astrandVo2Maximo }}
							</div>
						</div>
					</div>
					<div class="category-button">
						<pacto-cat-button
							(click)="calcularHandler()"
							i18n-label="@@avaliacao-fisica:avaliacoes-aluno:calcular-astrand"
							id="calcular-astrand"
							label="CALCULAR"></pacto-cat-button>
					</div>
				</div>
				<div
					*ngIf="configuracao.cfg_teste_queens"
					class="category teste-submaximo">
					<div
						class="category-title"
						i18n="@@editor-vo2max:teste-submaximo-astrand">
						TESTE SUBMÁXIMO DE QUEENS COLLEGE
					</div>
					<div class="items">
						<div class="item">
							<div
								class="item-label"
								i18n="@@editor-vo2max:frequencia-cardiaca">
								Frequência cardíaca (bpm)
							</div>
							<div class="input-column">
								<pacto-avaliacao-input-number
									[control]="formGroup.get('collegeFrequencia')"
									[id]="'collegeFrequencia'"></pacto-avaliacao-input-number>
							</div>
						</div>
						<div class="item">
							<div class="item-label" i18n="@@editor-vo2max:vo2-maximo">
								VO2 Máximo (ml/kg/min):
							</div>
							<div class="input-column">
								{{ avaliacao?.vo2?.collegeVo2Maximo }}
							</div>
						</div>
					</div>
				</div>
			</ng-container>
		</div>
	</div>
</div>

<ng-template #tradutor let-item="item">
	<ng-container [ngSwitch]="item">
		<!-- Referencia Protocolo: Caminhada -->
		<span *ngSwitchCase="'coluna13a19'" i18n="@@editor-vo2max:13-19-anos">
			13 - 19 anos
		</span>
		<span *ngSwitchCase="'coluna20a29'" i18n="@@editor-vo2max:20-29-anos">
			20 - 29 anos
		</span>
		<span *ngSwitchCase="'coluna30a39'" i18n="@@editor-vo2max:30-39-anos">
			30 - 39 anos
		</span>
		<span *ngSwitchCase="'coluna40a49'" i18n="@@editor-vo2max:40-49-anos">
			40 - 49 anos
		</span>
		<span *ngSwitchCase="'coluna50a59'" i18n="@@editor-vo2max:50-59-anos">
			50 - 59 anos
		</span>
		<span *ngSwitchCase="'colunaMaior60'" i18n="@@editor-vo2max:maior-60">
			Maior que 60
		</span>
		<span *ngSwitchCase="'muitoFraca'" i18n="@@editor-vo2max:muito-fraca">
			Muita Fraca
		</span>
		<span *ngSwitchCase="'fraca'" i18n="@@editor-vo2max:fraca">Fraca</span>
		<span *ngSwitchCase="'media'" i18n="@@editor-vo2max:media">Média</span>
		<span *ngSwitchCase="'boa'" i18n="@@editor-vo2max:boa">Boa</span>
		<span *ngSwitchCase="'excelente'" i18n="@@editor-vo2max:excelente">
			Excelente
		</span>
		<span *ngSwitchCase="'superior'" i18n="@@editor-vo2max:superior">
			Superior
		</span>

		<!-- Referencia Protocolo: teste 2400 -->
		<span *ngSwitchCase="'testeMenor30'" i18n="@@editor-vo2max:menor-30-anos">
			Menor que 30 anos
		</span>
		<span *ngSwitchCase="'teste30e39'" i18n="@@editor-vo2max:30-39-anos">
			30 - 39 anos
		</span>
		<span *ngSwitchCase="'teste40e49'" i18n="@@editor-vo2max:40-49-anos">
			40 - 49 anos
		</span>
		<span *ngSwitchCase="'testeMaior50'" i18n="@@editor-vo2max:maior-50-anos">
			Maior que 50 anos
		</span>
		<span *ngSwitchCase="'testeMuitoFraca'" i18n="@@editor-vo2max:muito-fraca">
			Muito Fraca
		</span>
		<span *ngSwitchCase="'testeFraca'" i18n="@@editor-vo2max:fraca">Fraca</span>
		<span *ngSwitchCase="'testeMedia'" i18n="@@editor-vo2max:media">Média</span>
		<span *ngSwitchCase="'testeBoa'" i18n="@@editor-vo2max:boa">Boa</span>
		<span *ngSwitchCase="'testeExcelente'" i18n="@@editor-vo2max:excelente">
			Excelente
		</span>

		<!-- Referencia Protocolo: Aeróbico -->
		<span *ngSwitchCase="'aerobico20e29'" i18n="@@editor-vo2max:20-29-anos">
			20 - 29 anos
		</span>
		<span *ngSwitchCase="'aerobico30e39'" i18n="@@editor-vo2max:30-39-anos">
			30 - 39 anos
		</span>
		<span *ngSwitchCase="'aerobico40e49'" i18n="@@editor-vo2max:40-49-anos">
			40 - 49 anos
		</span>
		<span *ngSwitchCase="'aerobico50e59'" i18n="@@editor-vo2max:50-59-anos">
			50 - 59 anos
		</span>
		<span *ngSwitchCase="'aerobicoMaior60'" i18n="@@editor-vo2max:maior-60">
			maior que 60 anos
		</span>

		<span *ngSwitchCase="'aerobicoRisco'" i18n="@@editor-vo2max:condicao-risco">
			Condição de risco
		</span>
		<span
			*ngSwitchCase="'aerobicoBaixaAptidao'"
			i18n="@@editor-vo2max:baixa-optidao">
			Baixa aptidão
		</span>
		<span
			*ngSwitchCase="'aerobicoRecomendavel'"
			i18n="@@editor-vo2max:faixa-recomendavel">
			Faixa recomendável
		</span>
		<span
			*ngSwitchCase="'aerobicoAtletica'"
			i18n="@@editor-vo2max:condicao-atletica">
			Condição atlética
		</span>
	</ng-container>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span xingling="VALIDACAOASTRAND">
		Ops! Para o cálculo do teste de astrand é necessário informar o peso do
		aluno
	</span>
	<span xingling="MENSSAGEM_TEMPO">
		O tempo informado na aba vo2max deve ser preenchido de acordo com a
		formatação.
	</span>
</pacto-traducoes-xingling>
