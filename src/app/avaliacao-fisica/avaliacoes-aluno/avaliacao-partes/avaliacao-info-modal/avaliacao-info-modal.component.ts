import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-avaliacao-info-modal",
	templateUrl: "./avaliacao-info-modal.component.html",
	styleUrls: ["./avaliacao-info-modal.component.scss"],
})
export class AvaliacaoInfoModalComponent implements OnInit {
	content: TemplateRef<any>;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	close() {
		this.openModal.close();
	}
}
