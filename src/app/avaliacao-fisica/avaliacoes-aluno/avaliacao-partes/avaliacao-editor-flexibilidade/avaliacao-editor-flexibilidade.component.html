<div class="avaliacao-editor-flexibilidade-wrapper">
	<div
		class="flexibilidade-title"
		i18n="@@editor-flexibilidade:banco-wells-dilon">
		BANCO DE WELLS & DILON
	</div>

	<div class="row">
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:alcance-maximo-obtido">
					Alcance máximo obtido (cm)
				</label>
				<pacto-avaliacao-input-number
					[control]="formGroup.get('alcanceMaximo')"
					[decimal]="false"
					id="alcance-minimo-obtido-flexbilidade"></pacto-avaliacao-input-number>
			</div>
			<div class="form-group">
				<label class="control-label" i18n="@@editor-flexibilidade:observacao">
					Observação
				</label>
				<textarea
					[formControl]="formGroup.get('observacao')"
					class="form-control form-control-sm"
					id="alcance-observacao-flexibilidade"
					rows="3"
					title="Para impressão da avaliação física considerar o limite de 280 caracteres"></textarea>
			</div>
		</div>

		<div class="col-md-6">
			<div class="referencia">
				<div
					class="referencia-title"
					i18n="@@editor-flexibilidade:tabela-referencia">
					Tabela Referência
				</div>

				<div
					class="item {{
						referenciaItem === FlexibilidadeIndicadorColuna.FRACA
							? 'selected'
							: ''
					}}">
					<div class="ref-label" i18n="@@editor-flexibilidade:fraca">Fraca</div>
					<div class="valor" i18n="@@editor-flexibilidade:menor-24">
						menor que 24
					</div>
				</div>
				<div
					class="item {{
						referenciaItem === FlexibilidadeIndicadorColuna.REGULAR
							? 'selected'
							: ''
					}}">
					<div class="ref-label" i18n="@@editor-flexibilidade:regular">
						Regular
					</div>
					<div class="valor" i18n="@@editor-flexibilidade:24-28">24 - 28</div>
				</div>
				<div
					class="item {{
						referenciaItem === FlexibilidadeIndicadorColuna.MEDIA
							? 'selected'
							: ''
					}}">
					<div class="ref-label" i18n="@@editor-flexibilidade:media">Média</div>
					<div class="valor" i18n="@@editor-flexibilidade:29-33">29 - 33</div>
				</div>
				<div
					class="item {{
						referenciaItem === FlexibilidadeIndicadorColuna.BOA
							? 'selected'
							: ''
					}}">
					<div class="ref-label" i18n="@@editor-flexibilidade:boa">Boa</div>
					<div class="valor" i18n="@@editor-flexibilidade:34-38">34 - 38</div>
				</div>
				<div
					class="item {{
						referenciaItem === FlexibilidadeIndicadorColuna.EXCELENTE
							? 'selected'
							: ''
					}}">
					<div class="ref-label" i18n="@@editor-flexibilidade:excelente">
						Excelente
					</div>
					<div class="valor" i18n="@@editor-flexibilidade:maior-38">
						maior que 38
					</div>
				</div>
			</div>
		</div>
	</div>

	<div
		class="flexibilidade-title flexibilidade-title-margin-top-20"
		i18n="@@editor-flexibilidade:mobilidade-de-ombro">
		MOBILIDADE DO OMBRO
	</div>

	<div class="row">
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-ombro-esquerdo">
					Ombro Esquerdo
				</label>
				<select
					[formControl]="formGroup.get('mobilidadeOmbroEsquerdo')"
					class="form-control form-control-sm"
					id="select-mobilidade-ombro-esquerdo">
					<option
						i18n="@@editor-flexibilidade:fraco"
						value="{{ FlexibilidadeIndicadorColuna.FRACA }}">
						Fraca
					</option>
					<option
						i18n="@@editor-flexibilidade:regular"
						value="{{ FlexibilidadeIndicadorColuna.REGULAR }}">
						Regular
					</option>
					<option
						i18n="@@editor-flexibilidade:bom"
						value="{{ FlexibilidadeIndicadorColuna.BOA }}">
						Boa
					</option>
					<option
						i18n="@@editor-flexibilidade:excelente"
						value="{{ FlexibilidadeIndicadorColuna.EXCELENTE }}">
						Excelente
					</option>
				</select>
			</div>
		</div>

		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-ombro-direito">
					Ombro Direito
				</label>
				<select
					[formControl]="formGroup.get('mobilidadeOmbroDireito')"
					class="form-control form-control-sm"
					id="select-mobilidade-ombro-direito">
					<option
						i18n="@@editor-flexibilidade:fraco"
						value="{{ FlexibilidadeIndicadorColuna.FRACA }}">
						Fraca
					</option>
					<option
						i18n="@@editor-flexibilidade:regular"
						value="{{ FlexibilidadeIndicadorColuna.REGULAR }}">
						Regular
					</option>
					<option
						i18n="@@editor-flexibilidade:bom"
						value="{{ FlexibilidadeIndicadorColuna.BOA }}">
						Boa
					</option>
					<option
						i18n="@@editor-flexibilidade:excelente"
						value="{{ FlexibilidadeIndicadorColuna.EXCELENTE }}">
						Excelente
					</option>
				</select>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:observacao-ombro">
					Observação
				</label>
				<textarea
					[formControl]="formGroup.get('observacaoOmbro')"
					class="form-control form-control-sm"
					id="observacao-ombro"
					rows="3"
					title="Para impressão da avaliação física considerar o limite de 280 caracteres"></textarea>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="referencia">
				<div
					class="referencia-title"
					i18n="@@editor-flexibilidade:mobilidade-ombro-tabela-referencia">
					Tabela Referência
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:fraca">Fraca</div>
					<div class="valor">Dedos médios não conseguem se tocar</div>
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:regular">
						Regular
					</div>
					<div class="valor">Dedos médios se tocam</div>
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:boa">Boa</div>
					<div class="valor">Dedos médios se sobrepõe de 1 a 12 cm</div>
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:excelente">
						Excelente
					</div>
					<div class="valor">Dedos médios se sobrepõe acima de 12,1 cm</div>
				</div>
			</div>
		</div>
	</div>

	<div
		class="flexibilidade-title flexibilidade-title-margin-top-20"
		i18n="@@editor-flexibilidade:mobilidade-quadril-thomas">
		MOBILIDADE DO QUADRIL (TESTE DE THOMAS)
	</div>

	<div class="row">
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-quadril-esquerdo">
					Lado Esquerdo
				</label>
				<select
					[formControl]="formGroup.get('mobilidadeQuadrilEsquerdo')"
					class="form-control form-control-sm"
					id="select-mobilidade-quadril-esquerdo">
					<option
						i18n="@@editor-flexibilidade:normal"
						value="{{ FlexibilidadeIndicadorColuna.NORMAL }}">
						Normal
					</option>
					<option
						i18n="@@editor-flexibilidade:positivo"
						value="{{ FlexibilidadeIndicadorColuna.POSITIVA }}">
						Positiva
					</option>
				</select>
			</div>
		</div>

		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-quadril-direito">
					Lado Direito
				</label>
				<select
					[formControl]="formGroup.get('mobilidadeQuadrilDireito')"
					class="form-control form-control-sm"
					id="select-mobilidade-quadril-direito">
					<option
						i18n="@@editor-flexibilidade:normal"
						value="{{ FlexibilidadeIndicadorColuna.NORMAL }}">
						Normal
					</option>
					<option
						i18n="@@editor-flexibilidade:positivo"
						value="{{ FlexibilidadeIndicadorColuna.POSITIVA }}">
						Positiva
					</option>
				</select>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:observacao-quadril">
					Observação
				</label>
				<textarea
					[formControl]="formGroup.get('observacaoQuadril')"
					class="form-control form-control-sm"
					id="observacao-quadril"
					rows="3"
					title="Para impressão da avaliação física considerar o limite de 280 caracteres"></textarea>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="referencia">
				<div
					class="referencia-title"
					i18n="
						@@editor-flexibilidade:mobilidade-quadril-thomas-tabela-referencia">
					Tabela Referência
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:normal">
						Normal
					</div>
					<div class="valor">
						Não há flexão da coxa que permanece apoiada na mesa durante esta
						manobra
					</div>
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:positiva">
						Positivo
					</div>
					<div class="valor">
						Há flexão da coxa, enquanto o paciente realiza a flexão do quadril
						oposto
					</div>
				</div>
			</div>
		</div>
	</div>

	<div
		class="flexibilidade-title flexibilidade-title-margin-top-20"
		i18n="@@editor-flexibilidade:mobilidade-joelho">
		MOBILIDADE DO JOELHO (STEP DOWN TEST)
	</div>

	<div class="row">
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-joelho-esquerdo">
					Joelho Esquerdo
				</label>
				<select
					[formControl]="formGroup.get('mobilidadeJoelhoEsquerdo')"
					class="form-control form-control-sm"
					id="select-mobilidade-joelho-esquerdo">
					<option
						value="{{
							FlexibilidadeIndicadorColuna.VALGO_DINAMICO_MEMBRO_DOMINANTE
						}}">
						Valgo dinâmico em membro dominante
					</option>
					<option
						value="{{
							FlexibilidadeIndicadorColuna.VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE
						}}">
						Valgo dinâmico em membro não dominante
					</option>
					<option
						value="{{ FlexibilidadeIndicadorColuna.VALGO_DINAMICO_BILATERAL }}">
						Valgo dinâmico bilateral
					</option>
				</select>
			</div>
		</div>

		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-joelho-direito">
					Joelho Direito
				</label>
				<select
					[formControl]="formGroup.get('mobilidadeJoelhoDireito')"
					class="form-control form-control-sm"
					id="select-mobilidade-joelho-direito">
					<option
						value="{{
							FlexibilidadeIndicadorColuna.VALGO_DINAMICO_MEMBRO_DOMINANTE
						}}">
						Valgo dinâmico em membro dominante
					</option>
					<option
						value="{{
							FlexibilidadeIndicadorColuna.VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE
						}}">
						Valgo dinâmico em membro não dominante
					</option>
					<option
						value="{{ FlexibilidadeIndicadorColuna.VALGO_DINAMICO_BILATERAL }}">
						Valgo dinâmico bilateral
					</option>
				</select>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:observacao-joelho">
					Observação
				</label>
				<textarea
					[formControl]="formGroup.get('observacaoJoelho')"
					class="form-control form-control-sm"
					id="observacao-joelho"
					rows="3"
					title="Para impressão da avaliação física considerar o limite de 280 caracteres"></textarea>
			</div>
		</div>
	</div>

	<div
		class="flexibilidade-title flexibilidade-title-margin-top-20"
		i18n="@@editor-flexibilidade:mobilidade-tornozelo">
		MOBILIDADE DO TORNOZELO (LUNG TEST)
	</div>

	<div class="row">
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-tornozelo-esquerdo-cm">
					Mobilidade Tornozelo Esquerdo (cm)
				</label>
				<pacto-avaliacao-input-number
					[control]="formGroup.get('mobilidadeTornozeloEsquerdo')"
					[decimal]="false"
					id="mobilidade-tornozelo-esquerdo-cm"></pacto-avaliacao-input-number>
			</div>
		</div>

		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:mobilidade-tornozelo-direito-cm">
					Mobilidade Tornozelo Direito (cm)
				</label>
				<pacto-avaliacao-input-number
					[control]="formGroup.get('mobilidadeTornozeloDireito')"
					[decimal]="false"
					id="mobilidade-tornozelo-direito-cm"></pacto-avaliacao-input-number>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-flexibilidade:observacao-tornozelo">
					Observação
				</label>
				<textarea
					[formControl]="formGroup.get('observacaoTornozelo')"
					class="form-control form-control-sm"
					id="observacao-tornozelo"
					rows="3"
					title="Para impressão da avaliação física considerar o limite de 280 caracteres"></textarea>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="referencia">
				<div
					class="referencia-title"
					i18n="@@editor-flexibilidade:mobilidade-tornozelo-tabela-referencia">
					Tabela Referência
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:abaixo-media">
						Abaixo da média
					</div>
					<div class="valor">
						Distância do dedão do pé para a parede < de 6cm
					</div>
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:normal">
						Normal
					</div>
					<div class="valor">
						Distância do dedão do pé para a parede entre 10 e 12 cm
					</div>
				</div>

				<div class="item">
					<div class="ref-label" i18n="@@editor-flexibilidade:hipermobilidade">
						Hipermobilidade
					</div>
					<div class="valor">Distância do dedão do pé para a parede > 12cm</div>
				</div>
			</div>
		</div>
	</div>
</div>
