import { Component, OnInit, Input } from "@angular/core";
import { AvaliacaoFisica, FlexibilidadeIndicadorColuna } from "treino-api";
import { FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-avaliacao-editor-flexibilidade",
	templateUrl: "./avaliacao-editor-flexibilidade.component.html",
	styleUrls: ["./avaliacao-editor-flexibilidade.component.scss"],
})
export class AvaliacaoEditorFlexibilidadeComponent implements OnInit {
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;

	constructor() {}

	get referenciaItem(): FlexibilidadeIndicadorColuna {
		if (this.avaliacao && this.avaliacao.flexibilidade) {
			return this.avaliacao.flexibilidade.indicadorColuna;
		}
	}

	get FlexibilidadeIndicadorColuna() {
		return FlexibilidadeIndicadorColuna;
	}

	ngOnInit() {}
}
