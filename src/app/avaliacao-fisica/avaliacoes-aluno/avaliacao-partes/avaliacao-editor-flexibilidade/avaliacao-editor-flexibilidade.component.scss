.avaliacao-editor-flexibilidade-wrapper {
	padding: 20px;
	background-color: #fff;

	input {
		width: 100px;
		display: block;
	}
}

.flexibilidade-title {
	font-weight: 600;
	font-size: 17px;
	color: #777;
	border-bottom: 1px solid rgb(170, 170, 170);
	margin-bottom: 20px;
	padding-bottom: 5px;
}

.flexibilidade-title-margin-top-20 {
	margin-top: 20px;
}

.referencia {
	.referencia-title {
		font-size: 18px;
		margin-bottom: 10px;
		padding-bottom: 5px;
		border-bottom: 1px solid #dcdcdc;
	}

	.item {
		display: flex;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		&.selected {
			background-color: #d4d4d4;
		}

		.ref-label {
			flex-grow: 1;
			color: #777;
			font-weight: 600;
		}
	}
}
