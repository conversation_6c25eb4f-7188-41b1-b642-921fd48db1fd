<div *ngIf="avaliacao" class="editor-composicao-wrapper">
	<div class="heads-up-row">
		<!-- Peso Gordura -->
		<div class="block">
			<div class="title" i18n="@@editor-composicao:peso-gordura">
				Peso de gordura
			</div>
			<div class="input-block">{{ pesoGordura | number : "1.0-2" }}</div>
			<div class="unit-block" i18n="@@editor-composicao:kg">kg</div>
		</div>

		<!-- Peso residual -->
		<div class="block">
			<div class="title" i18n="@@editor-composicao:peso-residual">
				Peso residual
			</div>
			<div class="input-block">{{ pesoResidual | number : "1.0-2" }}</div>
			<div class="unit-block" i18n="@@editor-composicao:kg">kg</div>
		</div>

		<!-- Peso muscular -->
		<div class="block">
			<div class="title" i18n="@@editor-composicao:peso-muscular">
				Peso muscular
			</div>
			<div class="input-block">{{ pesoMuscular | number : "1.0-2" }}</div>
			<div class="unit-block" i18n="@@editor-composicao:kg">kg</div>
		</div>

		<!-- Peso ósseo -->
		<div class="block">
			<div class="title" i18n="@@editor-composicao:peso-osseo">Peso ósseo</div>
			<div class="input-block">{{ pesoOsseo | number : "1.0-2" }}</div>
			<div class="unit-block" i18n="@@editor-composicao:kg">kg</div>
		</div>
	</div>
</div>
