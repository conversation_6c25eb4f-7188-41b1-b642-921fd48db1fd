import { Component, OnInit, Input } from "@angular/core";
import { AvaliacaoFisica } from "treino-api";

@Component({
	selector: "pacto-avaliacao-editor-composicao",
	templateUrl: "./avaliacao-editor-composicao.component.html",
	styleUrls: ["./avaliacao-editor-composicao.component.scss"],
})
export class AvaliacaoEditorComposicaoComponent implements OnInit {
	@Input() avaliacao: AvaliacaoFisica;

	constructor() {}

	ngOnInit() {}

	get pesoGordura() {
		const value = this.avaliacao.composicao.pesoGordura;
		if (value !== null) {
			return value;
		} else {
			return "-";
		}
	}

	get pesoResidual() {
		const value = this.avaliacao.composicao.pesoResidual;
		if (value !== null) {
			return value;
		} else {
			return "-";
		}
	}

	get pesoMuscular() {
		const value = this.avaliacao.composicao.pesoMuscular;
		if (value !== null) {
			return value;
		} else {
			return "-";
		}
	}

	get pesoOsseo() {
		const value = this.avaliacao.composicao.pesoOsseo;
		if (value !== null) {
			return value;
		} else {
			return "-";
		}
	}
}
