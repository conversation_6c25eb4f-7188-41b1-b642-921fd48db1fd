.avaliacao-editor-rml-wrapper {
	padding: 20px;
	background-color: #fff;
}

.category {
	margin-bottom: 30px;

	.items .item {
		display: flex;
		background-color: #efefef;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		.item-label {
			flex-grow: 1;
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
		}
	}
}

.reference-table {
	font-size: 17px;

	.linha {
		display: flex;
		background-color: #efefef;
		line-height: 42px;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		&:first-child {
			font-weight: 600;
		}

		&.active .item.active {
			background-color: #6f6f6f;
			color: #fff;
			font-weight: 600;
		}

		.item {
			width: 16.6%;
			flex-grow: 0;
			text-align: center;
			flex-shrink: 0;
		}
	}
}
