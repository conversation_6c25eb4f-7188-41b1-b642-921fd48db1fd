<div *ngIf="avaliacao" class="avaliacao-editor-rml-wrapper">
	<div class="category">
		<div class="items">
			<div class="item">
				<div class="item-label" i18n="@@editor-rml:flexoes-rml-bracos">
					Flexões - RML de Braços
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('flexoesBracos')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>

		<div class="reference-table">
			<div class="linha">
				<div class="item" i18n="@@editor-rml:idade">Idade</div>
				<div class="item" i18n="@@editor-rml:excelente">Excelente</div>
				<div class="item" i18n="@@editor-rml:acima-media">Aci<PERSON> da média</div>
				<div class="item" i18n="@@editor-rml:media">Média</div>
				<div class="item" i18n="@@editor-rml:abaixo-media">Abaixo da média</div>
				<div class="item" i18n="@@editor-rml:fraco">Fraco</div>
			</div>
			<div
				*ngFor="let valores of rmlConfig.bracos"
				[ngClass]="{ active: referenciaFlexaoLinha === valores.linha }"
				class="linha">
				<div class="item">{{ valores.faixaIdade }}</div>
				<div
					[ngClass]="{
						active: referenciaFlexaoColuna === RMLFlexoesColuna.EXCELENTE
					}"
					class="item">
					{{ valores.excelente }}
				</div>
				<div
					[ngClass]="{
						active: referenciaFlexaoColuna === RMLFlexoesColuna.ACIMA_MEDIA
					}"
					class="item">
					{{ valores.acimaMedia }}
				</div>
				<div
					[ngClass]="{
						active: referenciaFlexaoColuna === RMLFlexoesColuna.MEDIA
					}"
					class="item">
					{{ valores.media }}
				</div>
				<div
					[ngClass]="{
						active: referenciaFlexaoColuna === RMLFlexoesColuna.ABAIXO_MEDIA
					}"
					class="item">
					{{ valores.abaixoMedia }}
				</div>
				<div
					[ngClass]="{
						active: referenciaFlexaoColuna === RMLFlexoesColuna.FRACO
					}"
					class="item">
					{{ valores.fraco }}
				</div>
			</div>
		</div>
	</div>

	<div class="category">
		<div class="items">
			<div class="item">
				<div class="item-label" i18n="@@editor-rml-abdominais-rml-abdomen">
					Abdominais - RML de Abdômen
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('abdominais')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>

		<div class="reference-table">
			<div class="linha">
				<div class="item" i18n="@@editor-rml:idade">Idade</div>
				<div class="item" i18n="@@editor-rml:excelente">Excelente</div>
				<div class="item" i18n="@@editor-rml:acima-media">Acima da média</div>
				<div class="item" i18n="@@editor-rml:media">Média</div>
				<div class="item" i18n="@@editor-rml:abaixo-media">Abaixo da média</div>
				<div class="item" i18n="@@editor-rml:fraco">Fraco</div>
			</div>
			<div
				*ngFor="let valores of rmlConfig.abdomen"
				[ngClass]="{ active: referenciaAbsLinha === valores.linha }"
				class="linha">
				<div class="item">{{ valores.faixaIdade }}</div>
				<div
					[ngClass]="{
						active: referenciaAbsColuna === RMLAbdominaisColuna.EXCELENTE
					}"
					class="item">
					{{ valores.excelente }}
				</div>
				<div
					[ngClass]="{
						active: referenciaAbsColuna === RMLAbdominaisColuna.ACIMA_MEDIA
					}"
					class="item">
					{{ valores.acimaMedia }}
				</div>
				<div
					[ngClass]="{
						active: referenciaAbsColuna === RMLAbdominaisColuna.MEDIA
					}"
					class="item">
					{{ valores.media }}
				</div>
				<div
					[ngClass]="{
						active: referenciaAbsColuna === RMLAbdominaisColuna.ABAIXO_MEDIA
					}"
					class="item">
					{{ valores.abaixoMedia }}
				</div>
				<div
					[ngClass]="{
						active: referenciaAbsColuna === RMLAbdominaisColuna.FRACO
					}"
					class="item">
					{{ valores.fraco }}
				</div>
			</div>
		</div>
	</div>
</div>
