import { Component, OnInit, Input } from "@angular/core";
import { FormGroup } from "@angular/forms";
import {
	RMLFlexoesLinha,
	RMLFlexoesColuna,
	RMLAbdominaisLinha,
	RMLAbdominaisColuna,
	AvaliacaoFisica,
	RMLConfig,
} from "treino-api";

@Component({
	selector: "pacto-avaliacao-editor-rml",
	templateUrl: "./avaliacao-editor-rml.component.html",
	styleUrls: ["./avaliacao-editor-rml.component.scss"],
})
export class AvaliacaoEditorRmlComponent implements OnInit {
	@Input() formGroup: FormGroup;
	@Input() rmlConfig: RMLConfig;
	@Input() avaliacao: AvaliacaoFisica;

	constructor() {}

	ngOnInit() {}

	get RMLFlexoesLinha() {
		return RMLFlexoesLinha;
	}

	get RMLFlexoesColuna() {
		return RMLFlexoesColuna;
	}

	get RMLAbdominaisLinha() {
		return RMLAbdominaisLinha;
	}

	get RMLAbdominaisColuna() {
		return RMLAbdominaisColuna;
	}

	get referenciaFlexaoLinha() {
		if (this.avaliacao.rml) {
			return this.avaliacao.rml.rmlFlexoesLinha;
		}
	}

	get referenciaFlexaoColuna() {
		if (this.avaliacao.rml) {
			return this.avaliacao.rml.rmlFlexoesColuna;
		}
	}

	get referenciaAbsLinha() {
		if (this.avaliacao.rml) {
			return this.avaliacao.rml.rmlAbominaisLinha;
		}
	}

	get referenciaAbsColuna() {
		if (this.avaliacao.rml) {
			return this.avaliacao.rml.rmlAbominaisColuna;
		}
	}
}
