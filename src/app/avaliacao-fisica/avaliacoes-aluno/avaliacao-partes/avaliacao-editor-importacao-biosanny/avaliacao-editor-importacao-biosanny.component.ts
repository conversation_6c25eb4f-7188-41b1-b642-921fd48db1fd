import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
	AvaliacaoFisica,
	TreinoApiAlunosService,
	AvaliacaoBioimpedancia,
} from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { DialogService, PactoModalSize } from "ui-kit";
import { ModalValidacaoDadosImportacaoBiosannyComponent } from "./components/modal-validacao-dados-importacao-biosanny/modal-validacao-dados-importacao-biosanny.component";

declare var moment;

@Component({
	selector: "pacto-avaliacao-editor-importacao-biosanny",
	templateUrl: "./avaliacao-editor-importacao-biosanny.component.html",
	styleUrls: ["./avaliacao-editor-importacao-biosanny.component.scss"],
})
export class AvaliacaoEditorImportacaoBiosannyComponent implements OnInit {
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;

	formGroupImportacao = new FormGroup({
		fileImportacaoBiosanny: new FormControl(),
		nomeArquivoImportacaoBiosanny: new FormControl(),
	});

	constructor(
		public treinoApiAlunoService: TreinoApiAlunosService,
		private notify: SnotifyService,
		private readonly dialogService: DialogService
	) {}

	ngOnInit() {
		this.formGroupImportacao
			.get("fileImportacaoBiosanny")
			.valueChanges.subscribe((file) => {
				if (file) {
					this.treinoApiAlunoService
						.importarAvaliacaoBioimpedancia({ arquivoUpload: file })
						.subscribe(
							(response: AvaliacaoBioimpedancia) => {
								this.exibirModal(response);
							},
							(error) => {
								this.notify.error(error.message);
							}
						);
				}
			});
	}

	exibirModal(response: AvaliacaoBioimpedancia) {
		const dialogRef = this.dialogService.open(
			"Importacao Biosanny - Validacao dos dados",
			ModalValidacaoDadosImportacaoBiosannyComponent,
			PactoModalSize.LARGE
		);

		dialogRef.componentInstance.avaliacaoBioimpedancia = response;

		dialogRef.componentInstance.importar.subscribe(() => {
			this.atualizarInformacoes(response);
			this.setarTemImportacaoBiosanny();
			this.notify.warning(
				"Dados inseridos! Salve para persistir a avaliacao!."
			);
			dialogRef.close({ confirm: true });
		});
	}

	atualizarInformacoes(avaliacao: AvaliacaoBioimpedancia) {
		if (avaliacao.data) {
			this.formGroup
				.get("dataAvaliacao")
				.setValue(moment(avaliacao.data, "DD/MM/YYYY").toDate().valueOf());
		}
		this.formGroup.get("pesoAltura").get("altura").setValue(avaliacao.altura);
		this.formGroup.get("pesoAltura").get("peso").setValue(avaliacao.peso);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("resistencia")
			.setValue(avaliacao.resistencia);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("reatancia")
			.setValue(avaliacao.reatancia);
		this.formGroup
			.get("perimetria")
			.get("circunferenciaAbdominal")
			.setValue(avaliacao.perimetroAbdominal);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("percentAgua")
			.setValue(avaliacao.aguaCorporalPercentual);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("massaMagra")
			.setValue(avaliacao.massaLivreKg);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("percentMassaMagra")
			.setValue(avaliacao.massaLivrePercentual);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("massaGorda")
			.setValue(avaliacao.gorduraCorporalKg);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("percentMassaGorda")
			.setValue(avaliacao.gorduraCorporalPercentual);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("musculos")
			.setValue(avaliacao.massaMuscularEsqueleticaKg);
		this.formGroup
			.get("dobras")
			.get("bioimpedancia")
			.get("imc")
			.setValue(avaliacao.imc);
	}

	setarTemImportacaoBiosanny() {
		this.formGroup.get("temImportacaoBiosanny").setValue(true);
	}
}
