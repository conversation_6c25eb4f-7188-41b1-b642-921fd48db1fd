<div class="container">
	<pacto-relatorio [showShare]="false" [table]="table"></pacto-relatorio>

	<ng-template #colunaSituacao let-item="item">
		<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
			<ng-container *ngIf="item.situacao === 'SUCESSO'">Sucesso</ng-container>
			<ng-container *ngIf="item.situacao === 'ERRO'">Erro</ng-container>
		</div>
	</ng-template>

	<div class="section-importar-informacoes">
		<div class="sii-message">
			Caso concorde com as informações a serem importadas, clique ao lado.
		</div>
		<div>
			<pacto-cat-button
				(click)="importarEvent()"
				label="Importar"></pacto-cat-button>
		</div>
	</div>
</div>
