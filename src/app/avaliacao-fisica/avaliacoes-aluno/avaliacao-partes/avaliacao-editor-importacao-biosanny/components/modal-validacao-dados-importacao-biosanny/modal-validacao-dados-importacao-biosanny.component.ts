import { DatePipe } from "@angular/common";
import {
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "ui-kit";
import { AvaliacaoBioimpedancia } from "treino-api";

@Component({
	selector: "pacto-modal-validacao-dados-importacao-biosanny",
	templateUrl: "./modal-validacao-dados-importacao-biosanny.component.html",
	styleUrls: ["./modal-validacao-dados-importacao-biosanny.component.scss"],
})
export class ModalValidacaoDadosImportacaoBiosannyComponent implements OnInit {
	avaliacaoBioimpedancia: AvaliacaoBioimpedancia;
	table: PactoDataGridConfig;

	@ViewChild("colunaSituacao", { static: true })
	colunaSituacao: TemplateRef<any>;
	@Output()
	importar = new EventEmitter<any>();

	constructor(
		private dialog: NgbModal,
		private sessionService: SessionService,
		private datePipe: DatePipe
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.converterDadosAvaliacaoBioimpedancia(),
				};
			},
			pagination: false,
			showFilters: false,
			columns: [
				{
					nome: "campo",
					titulo: "Campo",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: false,
					celula: this.colunaSituacao,
				},
			],
		});
	}

	converterDadosAvaliacaoBioimpedancia() {
		const dados = [];

		for (const campo in this.avaliacaoBioimpedancia) {
			const valor = this.avaliacaoBioimpedancia[campo];

			dados.push({
				campo: this.obterNomeCampo(campo),
				valor,
				situacao: valor != null ? "SUCESSO" : "ERRO",
			});
		}

		return dados;
	}

	obterNomeCampo(campo: string) {
		switch (campo) {
			case "data":
				return "Data da avaliação";
			case "altura":
				return "Altura (m)";
			case "peso":
				return "Peso (kg)";
			case "resistencia":
				return "Resistência (ohm)";
			case "reatancia":
				return "Reatância (ohm)";
			case "perimetroAbdominal":
				return "Perimetro abdominal (cm)";
			case "aguaCorporalPercentual":
				return "Água corporal total (%)";
			case "massaLivreKg":
				return "Massa livre de gordura (kg)";
			case "massaLivrePercentual":
				return "Massa livre de gordura (%)";
			case "gorduraCorporalKg":
				return "Gordura corporal (kg)";
			case "gorduraCorporalPercentual":
				return "Gordura corporal (%)";
			case "massaMuscularEsqueleticaKg":
				return "Massa muscular esquelética (kg)";
			case "imc":
				return "IMC";
			default:
				return "Campo não reconhecido";
		}
	}

	importarEvent() {
		this.importar.emit();
	}
}
