<div class="editor-dobras-wrapper">
	<ng-container [ngSwitch]="modo">
		<div class="row" id="divDobrasProtocolo">
			<div class="col-md-4">
				<div class="form-group">
					<label class="control-label" i18n="@@editor-dobras:protocolo:label">
						Protocolo:
					</label>
					<select
						[formControl]="protocoloFormControl"
						class="form-control form-control-sm"
						id="selectProtocolo">
						<option
							i18n="@@editor-dobras:pollock-tres-dobras"
							value="{{ _AvaliacaoDobraProtocolo.POLLOCK_3_DOBRAS }}">
							Pollock três dobras
						</option>
						<option
							i18n="@@editor-dobras:pollock-sete-dobras"
							value="{{ _AvaliacaoDobraProtocolo.POLLOCK_7_DOBRAS }}">
							Pollock sete dobras
						</option>
						<option
							i18n="@@editor-dobras:guedes"
							value="{{ _AvaliacaoDobraProtocolo.GUEDES }}">
							Guedes
						</option>
						<option
							i18n="@@editor-dobras:faukner-quatro-dobras"
							value="{{ _AvaliacaoDobraProtocolo.FAULKNER_DOBRAS }}">
							Faukner quatro dobras
						</option>
						<option
							i18n="@@editor-dobras:bioimpedancia"
							value="{{ _AvaliacaoDobraProtocolo.BIOIMPEDANCIA }}">
							Bioimpedância
						</option>
						<option
							i18n="@@editor-dobras:weltman-para-obesos"
							value="{{ _AvaliacaoDobraProtocolo.WELTMAN_OBESO }}">
							Weltman para obesos
						</option>
						<option
							i18n="@@editor-dobras:pollock-adolescente"
							value="{{ _AvaliacaoDobraProtocolo.POLLOCK_ADOLESCENTE }}">
							Pollock adolescente
						</option>
						<option
							i18n="@@editor-dobras:slaughter-adolescente"
							value="{{ _AvaliacaoDobraProtocolo.SLAUGHTER }}">
							Slaughter (adolescente)
						</option>
						<option
							i18n="@@editor-dobras:yuhasz-seis-dobras"
							value="{{ _AvaliacaoDobraProtocolo.YUHASZ }}">
							Yuhasz (seis dobras)
						</option>
						<option
							i18n="@@editor-dobras:t6-lohman-duas-dobras"
							value="{{ _AvaliacaoDobraProtocolo.TG_LOHMAN }}">
							T.G. Lohman duas dobras
						</option>
					</select>
				</div>
			</div>

			<div *ngSwitchCase="MODO_VIEW.BIOIMPEDANCIA" class="col-md-2">
				<div *ngIf="integradoBalanca" class="form-group">
					<pacto-cat-form-input
						[control]="alturasGroup"
						[enableClearInput]="false"
						[errorMsg]="'Informe a altura.'"
						[label]="'Altura:'"
						[textMask]="
							textMask ? { mask: textMask, guide: false } : { mask: false }
						"
						class="altura"></pacto-cat-form-input>
				</div>
			</div>
			<div *ngSwitchCase="MODO_VIEW.BIOIMPEDANCIA" class="col-md-2">
				<div *ngIf="integradoBalanca" class="form-group">
					<label class="control-label">O aluno é atleta?</label>
					<form [formGroup]="formAtletas">
						<select
							class="form-control form-control-sm"
							formControlName="atletaControl"
							id="selectAtleta"
							name="atleta">
							<option *ngFor="let atleta of atletas" [ngValue]="atleta">
								{{ atleta.nome }}
							</option>
						</select>
					</form>
				</div>
			</div>
			<div *ngSwitchCase="MODO_VIEW.BIOIMPEDANCIA" class="col-md-4">
				<div *ngIf="integradoBalanca" class="form-group">
					<label class="control-label">Nível de atividade:</label>
					<form [formGroup]="formAtividades">
						<select
							class="form-control form-control-sm"
							formControlName="atividadesControl"
							id="selectAtividade"
							name="atividade">
							<option
								*ngFor="let atividade of atividades"
								[ngValue]="atividade">
								{{ atividade.nome }}
							</option>
						</select>
					</form>
				</div>
			</div>
		</div>

		<!-- Dobras -->
		<div *ngSwitchCase="MODO_VIEW.DOBRAS" class="content-dobras">
			<div class="items">
				<div
					*ngFor="let dobra of dobras"
					[ngClass]="{ required: isItemRequired(dobra.dobrasEnum) }"
					class="item">
					<div class="item-label">
						{{ dobrasLabels.getLabel(dobra.dobrasEnum) }}
					</div>
					<div class="input-column">
						<pacto-avaliacao-input-number
							[control]="
								dobrasFormGroup.get(getDobrasFormGroup(dobra.dobrasEnum, false))
							"
							[id]="
								getDobrasFormGroup(dobra.dobrasEnum, true)
							"></pacto-avaliacao-input-number>
					</div>
				</div>
			</div>
		</div>

		<!-- Bioimpedância -->
		<div *ngSwitchCase="MODO_VIEW.BIOIMPEDANCIA" class="content-dobras">
			<div class="items-bio-impar">
				<div class="item-content">
					<div class="item-wrapper">
						<div class="item-label" i18n="@@editor-dobras:imc:bioimpedancia">
							IMC
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.imc')"
								[id]="'imc'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:massa-magra:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("Massa magra") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.massaMagra')"
								[id]="'massaMagra'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-par">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:massa-gorda:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("Massa Gorda") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.massaGorda')"
								[id]="'massaGorda'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:perc-massa-gorda:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("% M. gorda") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="
									dobrasFormGroup.get('bioimpedancia.percentMassaGorda')
								"
								[id]="'percentMassaGorda'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-impar">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:perc-massa-magra:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("% M. magra") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="
									dobrasFormGroup.get('bioimpedancia.percentMassaMagra')
								"
								[id]="'percentMassaMagra'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div class="item-label" i18n="@@editor-dobras:ossos:bioimpedancia">
							Ossos
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.ossos')"
								[id]="'ossos'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-par">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:residuos:bioimpedancia">
							Resíduos
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.residuos')"
								[id]="'residuos'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:musculos:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("Músculos") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.musculos')"
								[id]="'musculos'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-impar">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:gordura-ideal:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("Gordura ideal") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.gorduraIdeal')"
								[id]="'gorduraIdeal'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:resistencia:bioimpedancia">
							Resistência
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.resistencia')"
								[id]="'resistencia'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-par">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:reatancia:bioimpedancia">
							Reatância
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.reatancia')"
								[id]="'reatancia'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:perc-agua:bioimpedancia">
							% Água
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.percentAgua')"
								[id]="'percentAgua'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-impar">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:nec-fisica:bioimpedancia">
							Nec. física
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.necFisica')"
								[id]="'necFisica'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div class="item-label" i18n="@@editor-dobras:tmb:bioimpedancia">
							{{ obterDescricaoValidandoObrigatorio("TMB") }}
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.tmb')"
								[id]="'tmb'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-par">
				<div class="item-content">
					<div class="item-wrapper">
						<div class="item-label" i18n="@@editor-dobras:nec-bioimpedancia">
							Nec. calórica
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.necCalorica')"
								[id]="'necCalorica'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:idade-metabolica:bioimpedancia">
							Idade metabólica
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.idadeMetabolica')"
								[id]="'idadeMetabolica'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
			</div>
			<div class="items-bio-impar">
				<div class="item-content">
					<div class="item-wrapper">
						<div
							class="item-label"
							i18n="@@editor-dobras:gordura-visceral:bioimpedancia">
							Gordura visceral
						</div>
						<div class="input-column">
							<pacto-avaliacao-input-number
								[control]="dobrasFormGroup.get('bioimpedancia.gorduraVisceral')"
								[id]="'gorduraVisceral'"></pacto-avaliacao-input-number>
						</div>
					</div>
				</div>
				<div class="item-content">
					<div class="item-wrapper"></div>
				</div>
			</div>
		</div>
		<!-- Sem dobras -->
		<div
			*ngSwitchCase="MODO_VIEW.WELTMAN_OBESOS"
			class="content"
			i18n="@@editor-dobras:sem-dobras">
			Este protocolo não se utiliza de dobras cutâneas, mas das medidas da
			circunferência abdominal(cm), da altura (cm) e da massa (Kg). Preencha a
			perimetria Circunferência abdominal com a média de duas medidas da
			circunferência do Abdômen para obter o percentual de gordura do avaliado.
		</div>
		<div *ngIf="integradoBalanca">
			<div *ngSwitchCase="MODO_VIEW.BIOIMPEDANCIA" class="button-balanca">
				<div class="button-content">
					<pacto-cat-button
						(click)="openModalHandler()"
						[icon]="'plus-circle'"
						[label]="'iniciar balança'"></pacto-cat-button>

					<pacto-cat-button
						(click)="copyLog()"
						*ngIf="logBalanca"
						[icon]="'copy'"
						[label]="'copiar log'"></pacto-cat-button>
				</div>
			</div>
		</div>

		<pacto-traducoes-xingling #dobrasLabels>
			<span xingling="ABDOMINAL">Abdominal</span>
			<span xingling="SUPRA_ILIACA">Supra-ilíaca</span>
			<span xingling="PEITORAL">Peitoral</span>
			<span xingling="TRICEPS">Tríceps</span>
			<span xingling="COXA_MEDIAL">Coxa Média</span>
			<span xingling="BICEPS">Bíceps</span>
			<span xingling="SUBESCAPULAR">Subescapular</span>
			<span xingling="AXILARMEDIA">Axilar média</span>
			<span xingling="SUPRA_ESPINHAL">Supra Espinhal</span>
			<span xingling="PANTURRILHA">Panturrilha</span>
		</pacto-traducoes-xingling>
	</ng-container>
	<span #erroComunicarBalanca [hidden]="true">
		Aguarde 60 segundos para ligar a balança novamente.
	</span>
</div>
