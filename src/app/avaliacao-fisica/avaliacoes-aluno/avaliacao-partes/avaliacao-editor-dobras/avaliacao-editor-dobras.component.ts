import {
	Component,
	OnInit,
	Input,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnDestroy,
	ViewChild,
} from "@angular/core";
import {
	FormGroup,
	FormControl,
	FormBuilder,
	Validators,
} from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { Subscription } from "rxjs";

import {
	AvaliacaoFisica,
	AvaliacaoDobraProtocolo,
	AvaliacaoCatalogoAluno,
	AlunoSexo,
	ConfiguracoesDobras,
} from "treino-api";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { PactoModalSize, ModalService } from "@base-core/modal/modal.service";
import { ModalBalancaComponent } from "src/app/avaliacao-fisica/modal-balanca/modal-balanca.component";
import { LocalizationService } from "@base-core/localization/localization.service";

enum MODO_VIEW {
	DOBRAS,
	BIOIMPEDANCIA,
	WELTMAN_OBESOS,
}

@Component({
	selector: "pacto-avaliacao-editor-dobras",
	templateUrl: "./avaliacao-editor-dobras.component.html",
	styleUrls: ["./avaliacao-editor-dobras.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoEditorDobrasComponent implements OnInit, OnDestroy {
	textMask;
	@Input() avaliacao: AvaliacaoFisica;
	@Input() aluno: AvaliacaoCatalogoAluno;
	@Input() formGroupPesoAltura: FormGroup;
	@Input() avaliacaoFormGroup: FormGroup;
	@Input() decimal = true;
	@ViewChild("erroComunicarBalanca", { static: true }) ligarBalacaErro;
	errorControl = new FormControl("", [
		Validators.required,
		Validators.minLength(10),
	]);

	atividades = [
		{ id: "0", nome: "Sedentário: 0h de atividade na semanal" },
		{ id: "1", nome: "Muitíssimo Leve: 30min a cada duas semanas" },
		{ id: "2", nome: "Muito Leve: 1h a cada duas semanas" },
		{ id: "3", nome: "Leve: 3h de atividade na semana" },
		{ id: "4", nome: "Moderado: 4h de atividade na semana" },
		{ id: "5", nome: "Intenso: 5h de atividade na semana" },
		{ id: "6", nome: "Muito intenso: +5h de atividade na semana" },
	];

	formAtividades = new FormGroup({
		atividade: new FormControl(this.atividades[0]),
	});

	atletas = [
		{ id: "0", nome: "Não" },
		{ id: "1", nome: "Sim" },
	];

	formAtletas = new FormGroup({
		atleta: new FormControl(this.atletas[0]),
	});

	dobras: ConfiguracoesDobras[] = [];
	changesSubscription: Subscription;
	integradoBalanca: boolean;
	dataAbrirModal: Date;

	get alturasGroup() {
		return this.formGroupPesoAltura.get("altura");
	}

	get dobrasFormGroup() {
		return this.avaliacaoFormGroup.get("dobras");
	}

	get protocoloFormControl() {
		return this.avaliacaoFormGroup.get("dobras.protocolo");
	}

	modo: MODO_VIEW;

	constructor(
		private snotifyService: SnotifyService,
		private localization: LocalizationService,
		private cd: ChangeDetectorRef,
		private fb: FormBuilder,
		private treinoConfigService: TreinoConfigCacheService,
		private modal: ModalService
	) {}

	get MODO_VIEW() {
		return MODO_VIEW;
	}

	get _AvaliacaoDobraProtocolo() {
		return AvaliacaoDobraProtocolo;
	}

	get editing() {
		return this.dobrasFormGroup.enabled;
	}

	ngOnDestroy() {
		this.avaliacaoFormGroup = new FormGroup({
			id: new FormControl(),
		});
		if (this.changesSubscription) {
			this.changesSubscription.unsubscribe();
		}
	}

	ngOnInit() {
		this.integradoBalanca =
			this.treinoConfigService.configuracoesIntegracoes.usar_integracao_bioimpedancia;
		this.errorControl.markAsTouched();
		this.alturasGroup.setValidators([
			Validators.required,
			Validators.maxLength(4),
			Validators.minLength(4),
		]);
		this.formAtletas = this.fb.group({
			atletaControl: this.atletas[0],
		});
		this.formAtividades = this.fb.group({
			atividadesControl: this.atividades[0],
		});
		if (this.changesSubscription) {
			this.changesSubscription.unsubscribe();
		}
		this.changesSubscription = this.avaliacaoFormGroup.valueChanges.subscribe(
			() => {
				setTimeout(() => {
					this.cd.detectChanges();
				});
			}
		);
		setTimeout(() => {
			this.cd.detectChanges();
		});
		this.updateFormMode();
		this.protocoloFormControl.valueChanges.subscribe(() => {
			this.updateFormMode();
		});
		this.dobras = JSON.parse(
			this.treinoConfigService.configuracoesAvaliacao.ordens_dobras
		);
		this.textMask = this.localization.getAlturaMask();
	}

	enable() {
		this.dobrasFormGroup.enable();
	}

	disable() {
		this.dobrasFormGroup.disable();
	}

	isItemRequired(field: string): boolean {
		const map = {
			POLLOCK_3_DOBRAS: {
				M: ["ABDOMINAL", "COXA_MEDIAL", "PEITORAL"],
				F: ["TRICEPS", "SUPRA_ILIACA", "COXA_MEDIAL"],
			},
			POLLOCK_7_DOBRAS: {
				M: [
					"ABDOMINAL",
					"PEITORAL",
					"COXA_MEDIAL",
					"SUBESCAPULAR",
					"SUPRA_ILIACA",
					"TRICEPS",
					"AXILARMEDIA",
				],
				F: [
					"ABDOMINAL",
					"PEITORAL",
					"COXA_MEDIAL",
					"SUBESCAPULAR",
					"SUPRA_ILIACA",
					"TRICEPS",
					"AXILARMEDIA",
				],
			},
			GUEDES: {
				M: ["TRICEPS", "SUPRA_ILIACA", "ABDOMINAL"],
				F: ["SUBESCAPULAR", "SUPRA_ILIACA", "COXA_MEDIAL"],
			},
			FAULKNER_DOBRAS: {
				M: ["TRICEPS", "SUBESCAPULAR", "SUPRA_ILIACA", "ABDOMINAL"],
				F: ["TRICEPS", "SUBESCAPULAR", "SUPRA_ILIACA", "ABDOMINAL"],
			},
			POLLOCK_ADOLESCENTE: {
				M: ["COXA_MEDIAL", "TRICEPS"],
				F: ["TRICEPS", "COXA_MEDIAL"],
			},
			SLAUGHTER: {
				M: ["PANTURRILHA", "TRICEPS"],
				F: ["TRICEPS", "PANTURRILHA"],
			},
			YUHASZ: {
				M: [
					"ABDOMINAL",
					"COXA_MEDIAL",
					"PEITORAL",
					"TRICEPS",
					"SUPRA_ILIACA",
					"SUBESCAPULAR",
				],
				F: [
					"ABDOMINAL",
					"COXA_MEDIAL",
					"PEITORAL",
					"TRICEPS",
					"SUPRA_ILIACA",
					"SUBESCAPULAR",
				],
			},
			TG_LOHMAN: {
				M: ["COXA_MEDIAL", "TRICEPS"],
				F: ["COXA_MEDIAL", "TRICEPS"],
			},
		};
		const protocolo = this.protocoloFormControl.value;
		let sexo = this.aluno.sexo;
		if (!sexo || (sexo !== "M" && sexo !== "F")) {
			sexo = AlunoSexo.MASCULINO;
		}
		return map[protocolo][sexo].includes(field);
	}

	private updateFormMode() {
		const protocolo = this.protocoloFormControl.value;
		switch (protocolo) {
			case AvaliacaoDobraProtocolo.POLLOCK_3_DOBRAS:
			case AvaliacaoDobraProtocolo.POLLOCK_7_DOBRAS:
			case AvaliacaoDobraProtocolo.GUEDES:
			case AvaliacaoDobraProtocolo.FAULKNER_DOBRAS:
			case AvaliacaoDobraProtocolo.POLLOCK_ADOLESCENTE:
			case AvaliacaoDobraProtocolo.SLAUGHTER:
			case AvaliacaoDobraProtocolo.TG_LOHMAN:
			case AvaliacaoDobraProtocolo.YUHASZ:
				this.modo = MODO_VIEW.DOBRAS;
				break;
			case AvaliacaoDobraProtocolo.BIOIMPEDANCIA:
				this.modo = MODO_VIEW.BIOIMPEDANCIA;
				break;
			case AvaliacaoDobraProtocolo.WELTMAN_OBESO:
				this.modo = MODO_VIEW.WELTMAN_OBESOS;
				break;
		}
	}

	public getDobrasFormGroup(value: string, inputId: boolean): string {
		const map = {
			ABDOMINAL: "abdominal",
			SUPRA_ILIACA: "supraIliaca",
			PEITORAL: "peitoral",
			TRICEPS: "triceps",
			COXA_MEDIAL: "coxaMedial",
			BICEPS: "biceps",
			SUBESCAPULAR: "subescapular",
			AXILARMEDIA: "axilarMedia",
			SUPRA_ESPINHAL: "supraEspinhal",
			PANTURRILHA: "panturrilha",
		};
		if (inputId) {
			return "idInput" + map[value];
		} else {
			return map[value];
		}
	}

	getCodigoSexo(sexo) {
		return sexo === "F" ? 1 : 2;
	}

	getAltura(altura: string) {
		altura = String(altura);
		altura = altura.replace(",", "");
		altura = altura.replace(".", "");

		return parseInt(altura, 10);
	}

	openModalHandler() {
		this.dataAbrirModal = new Date(+localStorage.getItem("tempoModalBalanca"));
		const esperar60sec = this.dataAbrirModal >= new Date();
		if (esperar60sec) {
			const ligarBalacaErro = this.ligarBalacaErro.nativeElement.innerHTML;
			this.snotifyService.error(ligarBalacaErro);
		} else {
			if (this.alturasGroup.valid) {
				const jsonAtleta: JSON = JSON.parse(
					JSON.stringify(this.formAtletas.get("atletaControl").value)
				);
				const jsonAtividade: JSON = JSON.parse(
					JSON.stringify(this.formAtividades.get("atividadesControl").value)
				);
				const modalRef = this.modal.open(
					"Medição",
					ModalBalancaComponent,
					PactoModalSize.LARGE
				);
				modalRef.componentInstance.alunoId = this.aluno.id;
				modalRef.componentInstance.idade = this.aluno.idade;
				modalRef.componentInstance.sexo = this.getCodigoSexo(this.aluno.sexo);
				modalRef.componentInstance.altura = this.getAltura(
					this.formGroupPesoAltura.get("altura").value
				);
				modalRef.componentInstance.atleta = Number(jsonAtleta["id"]);
				modalRef.componentInstance.nivelAtividade = Number(jsonAtividade["id"]);
				modalRef.componentInstance.avaliacaoFormGroup =
					this.avaliacaoFormGroup.get("dobras");
				modalRef.componentInstance.pesoFormGroup =
					this.formGroupPesoAltura.get("peso");
			} else {
				this.alturasGroup.markAsTouched();
			}
		}
	}

	get logBalanca() {
		return this.avaliacaoFormGroup.get("dobras.logBalanca").value;
	}

	copyLog() {
		const selBox = document.createElement("textarea");
		selBox.style.position = "fixed";
		selBox.style.left = "0";
		selBox.style.top = "0";
		selBox.style.opacity = "0";
		selBox.value = this.avaliacaoFormGroup.get("dobras.logBalanca").value;
		document.body.appendChild(selBox);
		selBox.focus();
		selBox.select();
		document.execCommand("copy");
		document.body.removeChild(selBox);
		this.snotifyService.success("log copiado!");
	}

	obterDescricaoValidandoObrigatorio(value: string): string {
		if (
			this.treinoConfigService.configuracoesAvaliacao
				.obrigar_campos_dobras_bioimpedancia
		) {
			return value + "*";
		}
		return value;
	}
}
