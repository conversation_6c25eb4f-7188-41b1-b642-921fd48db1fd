@import "src/assets/scss/pacto/plataforma-import.scss";

select.form-control-sm:not([size]):not([multiple]) {
	height: 42px;
}

.editor-dobras-wrapper {
	background-color: $branco;
	padding: 0 20px 5px 20px;

	.row {
		padding-top: 20px;
	}

	.control-label {
		color: $gelo04;
		font-family: "Nunito Sans", sans-serif;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		border-radius: 6px;
	}

	.form-group div,
	.select-selected {
		color: #ffffff;
		padding: 8px 16px;
		border: 1px solid transparent;
		border-color: transparent transparent rgba(0, 0, 0, 0.1) transparent;
		cursor: pointer;
	}

	.content-dobras {
		width: 100%;
		display: table;
	}

	.button-balanca {
		border-top: 1px solid #d1d4dc;
		margin-top: 25px;
		padding-top: 25px;

		.button-content {
			float: right;
			margin-bottom: 25px;
		}
	}

	.items {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
	}

	.items .item {
		flex-basis: 100%;
		display: flex;
		flex-shrink: 0;
		background-color: #efefef;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		&.required .item-label {
			font-weight: 600;

			&::before {
				content: "*";
			}
		}

		.item-label {
			font-family: "Nunito Sans", sans-serif;
			flex-grow: 1;
			font-style: normal;
			font-weight: normal;
			font-size: 16px;
			line-height: 24px;
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
		}
	}

	.items-bio-impar {
		display: table-row;
		flex-wrap: wrap;
		width: 100%;
		background: #eff2f7;
	}

	.items-bio-par {
		display: table-row;
		flex-wrap: wrap;
		width: 100%;
	}

	.items-bio-impar .item-content {
		flex-basis: 100%;
		width: 50%;
		display: table-cell;
		flex-shrink: 0;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: $pretoPri;

		&.required .item-label {
			font-weight: 600;

			&::before {
				content: "*";
			}
		}

		.item-label {
			flex-grow: 1;
			font-family: "Nunito Sans", sans-serif;
			font-style: normal;
			font-weight: normal;
			font-size: 16px;
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
		}

		.item-wrapper {
			display: flex;
		}
	}

	.items-bio-par .item-content {
		flex-basis: 100%;
		width: 50%;
		display: table-cell;
		flex-shrink: 0;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		&.required .item-label {
			font-weight: 600;

			&::before {
				content: "*";
			}
		}

		.item-label {
			flex-grow: 1;
			font-family: "Nunito Sans", sans-serif;
			font-style: normal;
			font-weight: normal;
			font-size: 16px;
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
		}

		.item-wrapper {
			display: flex;
		}
	}
}

.check-icon {
	right: 50px;
	position: absolute;
	top: 2px;
}

.times-icon {
	right: 20px;
	position: absolute;
	top: 2px;
}

.icon-input {
	padding-right: 60px !important;
}

.altura {
	margin: 0;
}

pacto-cat-button {
	margin-left: 10px;
}
