<div *ngIf="somatotipiaFG" class="editor-somopatia-wrapper">
	<div class="heads-up-row">
		<div class="block">
			<div class="title" i18n="@@editor-somopatia:endomorfia">Endomorfia</div>
			<div class="input-block">
				{{ endomorfia === null ? "-" : endomorfia }}
			</div>
		</div>
		<div class="block">
			<div class="title" i18n="@@editor-somopatia:mesamorfia">Mesomorfia</div>
			<div class="input-block">
				{{ mesomorfia === null ? "-" : mesomorfia }}
			</div>
		</div>
		<div class="block">
			<div class="title" i18n="@@editor-somopatia:ectamorfia">Ectomorfia</div>
			<div class="input-block">
				{{ ectomorfia === null ? "-" : ectomorfia }}
			</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title" i18n="@@editor-somopatia:dobras-cutaneas">
			DOBRAS CUTÂNEAS
		</div>
		<div class="items">
			<div class="item odd">
				<div class="item-label" i18n="@@editor-somopatia:triceps">Tríceps</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							avaliacaoFormGroup.get('dobras.triceps')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item odd">
				<div class="item-label" i18n="@@editor-somopatia:supra-espinhal">
					Supra-espinhal
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							avaliacaoFormGroup.get('dobras.supraEspinhal')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-somopatia:subescapular">
					Subescapular
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							avaliacaoFormGroup.get('dobras.subescapular')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-somopatia:panturrilha">
					Panturrilha
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							avaliacaoFormGroup.get('dobras.panturrilha')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title" i18n="@@editor-somopatia:perimetros">
			PERÍMETROS
		</div>
		<div class="items">
			<div class="item">
				<div class="item-label" i18n="@@editor-somopatia:panturrilha-direita">
					Panturrilha direita
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							somatotipiaFG.get('perimetroPanturrilhaDireita')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div
					class="item-label"
					i18n="@@editor-somopatia:braco-contraido-direito">
					Braço contraído direito
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							somatotipiaFG.get('perimetroBracoContraidoDireito')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title" i18n="@@editor-somopatia:diametros-osseos">
			DIÂMETROS ÓSSEOS
		</div>
		<div class="items">
			<div class="item odd">
				<div class="item-label" i18n="@@editor-somopatia:diametro-punho">
					Diâmetro do punho (biestilóide)
					<i
						(click)="btnClickHandler('textDiametroPunho')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							perimetriaFG.get('diametroPunho')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item odd">
				<div class="item-label" i18n="@@editor-somopatia:diametro-joelho">
					Diâmetro do joelho (bicondiliano femural)
					<i
						(click)="btnClickHandler('textDiametroJoelho')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							perimetriaFG.get('diametroJoelho')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-somopatia:diametro-cotovelo">
					Diâmetro do cotovelo (biepicondiliano umeral)
					<i
						(click)="btnClickHandler('textDiametroCotovelo')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							perimetriaFG.get('diametroCotovelo')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-somopatia:diametro-tornozelo">
					Diâmetro do tornozelo (bimaleolar)
					<i
						(click)="btnClickHandler('textDiametroTornozelo')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							perimetriaFG.get('diametroTornozelo')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>
</div>

<ng-template #textDiametroPunho>
	<h2>Diâmetro Punho</h2>
	<div>
		<b>Local de coleta -</b>
		distância entre os procesos estiloides do rádio e da ulna.
		<br />
		<b>Critérios na mensuração -</b>
		estando o testado sentado com o cotovelo em 90 graus de flexão, o punho em
		flexão de 90 graus e o ante-braço pronado.
		<br />
		<b>Coleta -</b>
		o diâmetro deve ser feito com as astes do Paquimetro nas duas estruturas sem
		usar muita força. Deve ser sentido a estrutura óssea.
		<br />
	</div>
</ng-template>

<ng-template #textDiametroCotovelo>
	<h2>Diâmetro Cotovelo</h2>
	<div>
		<b>Local de coleta -</b>
		distância entre os epicôndilos medial e lateral do úmero
		<br />
		<b>Coleta -</b>
		com o indivíduo em posição ortostáticam braço flexionado em 90 graus com o
		tronco e o antebraço formando 90 graus com o braço.
		<br />
	</div>
</ng-template>

<ng-template #textDiametroJoelho>
	<h2>Diâmetro Joelho</h2>
	<div>
		<b>Local de coleta -</b>
		diatância entro os côndilos medial e laterla do fêmur.
		<br />
		<b>Critérios na mensuração -</b>
		estando o testado sentado com os pés, apoiados no chão, a coxa formando um
		ângulo de 90 graus com o tronco e a perna formando ângulo de 90 graus com a
		coxa.
		<br />
		<b>Coleta -</b>
		o diâmetro deve ser feito com as astes do Paquimetro nas duas estruturas sem
		usar muita força. Deve ser sentido a estrutura óssea.
		<br />
	</div>
</ng-template>

<ng-template #textDiametroTornozelo>
	<h2>Diâmetro Tornozelo</h2>
	<div>
		<b>Local de entrada -</b>
		distância entre os dois maléolos (medial e lateral)
		<br />
		<b>Coleta -</b>
		estando o testado sentado e com os pés apoiados no chão.
		<br />
	</div>
</ng-template>
