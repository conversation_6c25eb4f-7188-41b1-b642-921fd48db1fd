import { Component, OnInit, Input, ViewChild } from "@angular/core";
import { AvaliacaoFisica } from "treino-api";
import { FormGroup, FormControl } from "@angular/forms";
import { AvaliacaoInfoModalComponent } from "../avaliacao-info-modal/avaliacao-info-modal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Observable } from "rxjs";

@Component({
	selector: "pacto-avaliacao-editor-somopatia",
	templateUrl: "./avaliacao-editor-somopatia.component.html",
	styleUrls: ["./avaliacao-editor-somopatia.component.scss"],
})
export class AvaliacaoEditorSomopatiaComponent implements OnInit {
	@Input() avaliacaoFormGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;

	@ViewChild("textDiametroPunho", { static: true }) textDiametroPunho;
	@ViewChild("textDiametroCotovelo", { static: true }) textDiametroCotovelo;
	@ViewChild("textDiametroJoelho", { static: true }) textDiametroJoelho;
	@ViewChild("textDiametroTornozelo", { static: true }) textDiametroTornozelo;

	endomorfia = 0;
	mesomorfia = 0;
	ectomorfia = 0;

	resultEndomorfia;
	resultMesomorfia;
	resultEctomorfia;

	get somatotipiaFG() {
		if (this.avaliacaoFormGroup && this.avaliacao) {
			return this.avaliacaoFormGroup.get("somatotipia");
		}
	}

	get dobrasFG() {
		if (this.avaliacaoFormGroup) {
			return this.avaliacaoFormGroup.get("dobras");
		}
	}

	get perimetriaFG() {
		if (this.avaliacaoFormGroup) {
			return this.avaliacaoFormGroup.get("perimetria");
		}
	}

	constructor(private modal: NgbModal) {}

	ngOnInit() {
		if (this.avaliacao && this.avaliacao.somatotipia) {
			this.loadResult();
		}
	}

	btnClickHandler(value) {
		const modalRef = this.modal.open(AvaliacaoInfoModalComponent);
		modalRef.componentInstance.content = this[value];
	}

	loadResult() {
		this.resultEndomorfia =
			Math.round(this.avaliacao.somatotipia.endomorfia * 1000) / 1000;
		this.resultMesomorfia =
			Math.round(this.avaliacao.somatotipia.mesomorfia * 1000) / 1000;
		this.resultEctomorfia =
			Math.round(this.avaliacao.somatotipia.ectomorfia * 1000) / 1000;

		this.endomorfia = this.resultEndomorfia ? this.resultEndomorfia : 0;
		this.mesomorfia = this.resultMesomorfia ? this.resultMesomorfia : 0;
		this.ectomorfia = this.resultEctomorfia ? this.resultEctomorfia : 0;
	}
}
