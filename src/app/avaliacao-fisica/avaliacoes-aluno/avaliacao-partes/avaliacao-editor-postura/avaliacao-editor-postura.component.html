<div class="avaliacao-editor-postura-wrapper">
	<div class="image-selector-wrapper">
		<div class="seletor-container">
			<div class="title" i18n="@@editor-postura:frente:title">Frente</div>
			<pacto-seletor-imagem
				#seletorFrente
				(cleared)="clearedImageHandler('frenteImageId')"
				[control]="formGroup.get('frenteImageUpload')"
				[height]="220"
				[nome]="'frente'"
				[width]="180"></pacto-seletor-imagem>
		</div>
		<div class="seletor-container">
			<div class="title" i18n="@@editor-postura:direita:title">Direita</div>
			<pacto-seletor-imagem
				#seletorDireita
				(cleared)="clearedImageHandler('direitaImageId')"
				[control]="formGroup.get('direitaImageUpload')"
				[height]="220"
				[nome]="'direita'"
				[width]="180"></pacto-seletor-imagem>
		</div>
		<div class="seletor-container">
			<div class="title" i18n="@@editor-postura:esquerda:title">Esquerda</div>
			<pacto-seletor-imagem
				#seletorEsquerda
				(cleared)="clearedImageHandler('esquerdaImageId')"
				[control]="formGroup.get('esquerdaImageUpload')"
				[height]="220"
				[nome]="'esquerda'"
				[width]="180"></pacto-seletor-imagem>
		</div>
		<div class="seletor-container">
			<div class="title" i18n="@@editor-postura:costas:title">Costas</div>
			<pacto-seletor-imagem
				#seletorCostas
				(cleared)="clearedImageHandler('costasImageId')"
				[control]="formGroup.get('costasImageUpload')"
				[height]="220"
				[nome]="'costas'"
				[width]="180"></pacto-seletor-imagem>
		</div>
	</div>

	<div class="postura-title" i18n="@@editor-postura:visao-lateral:title">
		VISÃO LATERAL
	</div>
	<div class="input-group-wrapper">
		<div *ngFor="let item of configVisaoLateral" class="form-check">
			<input
				[formControl]="formGroup.get(item)"
				class="form-check-input"
				id="{{ item }}"
				type="checkbox" />
			<label class="form-check-label" for="{{ item }}">
				<ng-container
					*ngTemplateOutlet="
						configTraducoes;
						context: { item: item }
					"></ng-container>
			</label>
		</div>
	</div>

	<div class="postura-title" i18n="@@editor-postura:visao-posterior:title">
		VISÃO POSTERIOR
	</div>
	<div class="input-group-wrapper">
		<div *ngFor="let item of configVisaoPosterior" class="form-check">
			<input
				[formControl]="formGroup.get(item)"
				class="form-check-input"
				id="{{ item }}"
				type="checkbox" />
			<label class="form-check-label" for="{{ item }}">
				<ng-container
					*ngTemplateOutlet="
						configTraducoes;
						context: { item: item }
					"></ng-container>
			</label>
		</div>
	</div>

	<div class="postura-title" i18n="@@editor-postura:visao-anterior:title">
		VISÃO ANTERIOR
	</div>
	<div class="input-group-wrapper">
		<div *ngFor="let item of configVisaoAnterior" class="form-check">
			<input
				[formControl]="formGroup.get(item)"
				class="form-check-input"
				id="{{ item }}"
				type="checkbox" />
			<label class="form-check-label" for="{{ item }}">
				<ng-container
					*ngTemplateOutlet="
						configTraducoes;
						context: { item: item }
					"></ng-container>
			</label>
		</div>
	</div>

	<div class="postura-title" i18n="@@editor-postura:assimetrias:title">
		ASSIMETRIAS
	</div>
	<div class="row">
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-postura:assimetrias:ombros-assimetricos:label">
					Ombros assimétricos:
				</label>
				<select
					[formControl]="formGroup.get('assimetrias.ombrosAssimetricos')"
					class="form-control form-control-sm">
					<option
						i18n="
							@@editor-postura:assimetrias:ombros-assimetricos:nenhuma-elevacao:option"
						value="NENHUMA_ELEVACAO">
						Nenhuma elevação
					</option>
					<option
						i18n="
							@@editor-postura:assimetrias:ombros-assimetricos:elevacao-ombro-direito:option"
						value="ELEVECAO_DIREITO">
						Elevação ombro direito
					</option>
					<option
						i18n="
							@@editor-postura:assimetrias:ombros-assimetricos:elevacao-ombro-esquerdo:option"
						value="ELEVECAO_ESQUERDO">
						Elevação ombro esquerdo
					</option>
				</select>
			</div>
		</div>
		<div class="col-md-6">
			<div class="form-group">
				<label
					class="control-label"
					i18n="@@editor-postura:assimetrias:assimetria-quadril:label">
					Assimetria de quadril:
				</label>
				<select
					[formControl]="formGroup.get('assimetrias.assimetriaQuadril')"
					class="form-control form-control-sm">
					<option
						i18n="
							@@editor-postura:assimetrias:assimetria-quadril:nenhuma-elevacao:option"
						value="NENHUMA_ELEVACAO">
						Nenhuma elevação
					</option>
					<option
						i18n="
							@@editor-postura:assimetrias:assimetria-quadril:elevacao-pelve-direito:option"
						value="ELEVECAO_PELVE_DIREITA">
						Elevação pelve direito
					</option>
					<option
						i18n="
							@@editor-postura:assimetrias:assimetria-quadril:elevacao-pelve-esquerdo:option"
						value="ELEVECAO_PELVE_ESQUERDA">
						Elevação pelve esquerdo
					</option>
				</select>
			</div>
		</div>
	</div>

	<div
		class="postura-title"
		i18n="@@editor-postura:avaliador-espaco-recomendacoes:title">
		O avaliador deve usar esse espaço para fazer recomendações ao aluno e ao
		professor que irá prescrever o programa de treino
	</div>
	<div class="form-group">
		<textarea
			[formControl]="formGroup.get('observacao')"
			class="form-control form-control-sm"
			rows="3"></textarea>
	</div>
</div>

<ng-template #configTraducoes let-item="item">
	<ng-container [ngSwitch]="item">
		<!-- VISÃO LATERAL -->
		<span
			*ngSwitchCase="'visaoLateral.anterversaoQuadril'"
			i18n="@@editor-postura:anterversao-quadril">
			Anterversão de quadril
		</span>
		<span
			*ngSwitchCase="'visaoLateral.hipercifoseToracica'"
			i18n="@@editor-postura:hipercifose-toracica">
			Hipercifose torácica
		</span>
		<span
			*ngSwitchCase="'visaoLateral.hiperlordoseCervical'"
			i18n="@@editor-postura:hiperlordose-cervical">
			Hiperlordose cervical
		</span>
		<span
			*ngSwitchCase="'visaoLateral.hiperlordoseLombar'"
			i18n="@@editor-postura:hiperlordose-lombar">
			Hiperlordose lombar
		</span>
		<span
			*ngSwitchCase="'visaoLateral.joelhoFlexo'"
			i18n="@@editor-postura:joelho-flexo">
			Joelho flexo
		</span>
		<span
			*ngSwitchCase="'visaoLateral.joelhoRecurvado'"
			i18n="@@editor-postura:joelho-recurvado">
			Joelho recurvado
		</span>
		<span
			*ngSwitchCase="'visaoLateral.protusaoAbdominal'"
			i18n="@@editor-postura:protusao-abdominal">
			Protusão abdominal
		</span>
		<span
			*ngSwitchCase="'visaoLateral.peCalcaneo'"
			i18n="@@editor-postura:pe-calcaneo">
			Pé calcâneo
		</span>
		<span *ngSwitchCase="'visaoLateral.peCavo'" i18n="@@editor-postura:pe-cavo">
			Pé cavo
		</span>
		<span
			*ngSwitchCase="'visaoLateral.peEquino'"
			i18n="@@editor-postura:pe-equino">
			Pé equino
		</span>
		<span
			*ngSwitchCase="'visaoLateral.pePlano'"
			i18n="@@editor-postura:pe-plano">
			Pé plano
		</span>
		<span
			*ngSwitchCase="'visaoLateral.retificacaoCervical'"
			i18n="@@editor-postura:retificado-cervical">
			Retificação cervical
		</span>
		<span
			*ngSwitchCase="'visaoLateral.retificacaoLombar'"
			i18n="@@editor-postura:retificacao-lombar">
			Retificação lombar
		</span>
		<span
			*ngSwitchCase="'visaoLateral.retroversaoQuadril'"
			i18n="@@editor-postura:retroversao-de-quadril">
			Retroversão de quadril
		</span>
		<span
			*ngSwitchCase="'visaoLateral.rotacaoInternaOmbros'"
			i18n="@@editor-postura:rotacao-interna-dos-ombros">
			Rotação interna dos ombros
		</span>
		<!-- POSTERIOR -->
		<span
			*ngSwitchCase="'visaoPosterior.depressaoEscapular'"
			i18n="@@editor-postura:depressao-escapular">
			Depressão escapular
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.encurtamentoTrapezio'"
			i18n="@@editor-postura:encurtamento-de-trapezio">
			Encurtamento de trapézio
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.escolioseCervical'"
			i18n="@@editor-postura:escoliose-cervical">
			Escoliose cervical
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.escolioseLombar'"
			i18n="@@editor-postura:escoliose-lombar">
			Escoliose lombar
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.escolioseToracica'"
			i18n="@@editor-postura:escoliose-toracica">
			Escoliose torácica
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.protacaoEscapular'"
			i18n="@@editor-postura:protacao-escapular">
			Protação escapular
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.peValgo'"
			i18n="@@editor-postura:pe-valao">
			Pé valgo
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.peVaro'"
			i18n="@@editor-postura:pe-varo">
			Pé varo
		</span>
		<span
			*ngSwitchCase="'visaoPosterior.retracaoEscapular'"
			i18n="@@editor-postura:retracao-escapular">
			Retração escapular
		</span>
		<!-- ANTERIOR -->
		<span
			*ngSwitchCase="'visaoAnterior.joelhoValgo'"
			i18n="@@editor-postura:joelho-valgo">
			Joelho valgo
		</span>
		<span
			*ngSwitchCase="'visaoAnterior.joelhoVaro'"
			i18n="@@editor-postura:joelho-varo">
			Joelho varo
		</span>
		<span
			*ngSwitchCase="'visaoAnterior.peAbduto'"
			i18n="@@editor-postura:pe-abduto">
			Pé abduto
		</span>
		<span
			*ngSwitchCase="'visaoAnterior.peAduto'"
			i18n="@@editor-postura:pe-aduto">
			Pé aduto
		</span>
	</ng-container>
</ng-template>
