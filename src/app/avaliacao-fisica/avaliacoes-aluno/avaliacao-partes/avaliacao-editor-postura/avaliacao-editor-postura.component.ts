import {
	Component,
	OnInit,
	Input,
	ViewChild,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnDestroy,
} from "@angular/core";
import { FormGroup } from "@angular/forms";

import { AvaliacaoFisica } from "treino-api";
import {
	configVisaoAnterior,
	configVisaoLateral,
	configVisaoPosterior,
} from "./configuracao-checkboxes";
import { Subscription } from "rxjs";
import { SeletorImagemComponent } from "old-ui-kit";

@Component({
	selector: "pacto-avaliacao-editor-postura",
	templateUrl: "./avaliacao-editor-postura.component.html",
	styleUrls: ["./avaliacao-editor-postura.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoEditorPosturaComponent implements OnInit, OnDestroy {
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;
	@ViewChild("seletorFrente", { static: true })
	seletorFrente: SeletorImagemComponent;
	@ViewChild("seletorDireita", { static: true })
	seletorDireita: SeletorImagemComponent;
	@ViewChild("seletorEsquerda", { static: true })
	seletorEsquerda: SeletorImagemComponent;
	@ViewChild("seletorCostas", { static: true })
	seletorCostas: SeletorImagemComponent;

	configVisaoAnterior;
	configVisaoLateral;
	configVisaoPosterior;
	changesSubscription: Subscription;

	constructor(private cd: ChangeDetectorRef) {}

	clearedImageHandler(image) {
		this.formGroup.get(image).setValue(null);
		this.clearedImageUrl(image);
	}

	clearedImageUrl(image) {
		if (image === "frenteImageId") {
			this.avaliacao.postura.frenteImageUri.url = null;
			this.seletorFrente.setUrl(null);
		}
		if (image === "costasImageId") {
			this.avaliacao.postura.costasImageUri.url = null;
			this.seletorCostas.setUrl(null);
		}
		if (image === "direitaImageId") {
			this.avaliacao.postura.direitaImageUri.url = null;
			this.seletorDireita.setUrl(null);
		}
		if (image === "esquerdaImageId") {
			this.avaliacao.postura.esquerdaImageUri.url = null;
			this.seletorEsquerda.setUrl(null);
		}
		this.cd.detectChanges();
	}

	setImages(frenteUri, direitaUri, esquerdaUri, costasUri) {
		this.seletorFrente.setUrl(frenteUri);
		this.seletorDireita.setUrl(direitaUri);
		this.seletorEsquerda.setUrl(esquerdaUri);
		this.seletorCostas.setUrl(costasUri);
		this.cd.detectChanges();
	}

	ngOnInit() {
		this.configVisaoAnterior = configVisaoAnterior;
		this.configVisaoLateral = configVisaoLateral;
		this.configVisaoPosterior = configVisaoPosterior;
		if (this.changesSubscription) {
			this.changesSubscription.unsubscribe();
		}
		this.changesSubscription = this.formGroup.valueChanges.subscribe((c) => {
			this.displayPosturaImagem();
			this.cd.detectChanges();
		});
		this.displayPosturaImagem();
		this.cd.detectChanges();
	}

	ngOnDestroy() {
		if (this.changesSubscription) {
			this.changesSubscription.unsubscribe();
		}
	}

	private displayPosturaImagem() {
		if (
			this.avaliacao &&
			this.avaliacao.postura &&
			!this.verificarAlteracaoImgPostura()
		) {
			this.setImages(
				this.avaliacao.postura.frenteImageUri.url,
				this.avaliacao.postura.direitaImageUri.url,
				this.avaliacao.postura.esquerdaImageUri.url,
				this.avaliacao.postura.costasImageUri.url
			);
		}
	}

	private verificarAlteracaoImgPostura() {
		if (
			this.formGroup.controls.frenteImageUpload.value !== null ||
			this.formGroup.controls.direitaImageUpload.value !== null ||
			this.formGroup.controls.esquerdaImageUpload.value !== null ||
			this.formGroup.controls.costasImageUpload.value !== null
		) {
			return true;
		}
		return false;
	}
}
