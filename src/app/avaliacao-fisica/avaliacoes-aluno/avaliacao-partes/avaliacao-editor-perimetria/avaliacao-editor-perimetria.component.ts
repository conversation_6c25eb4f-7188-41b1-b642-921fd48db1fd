import {
	Component,
	OnInit,
	Input,
	ViewChild,
	ChangeDetectorRef,
} from "@angular/core";
import { AlunoBase, AvaliacaoFisica } from "treino-api";
import { FormControl, FormGroup } from "@angular/forms";
import { AvaliacaoInfoModalComponent } from "../avaliacao-info-modal/avaliacao-info-modal.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import createNumberMask from "text-mask-addons/dist/createNumberMask";
import { LocalizationService } from "@base-core/localization/localization.service";

@Component({
	selector: "pacto-avaliacao-editor-perimetria",
	templateUrl: "./avaliacao-editor-perimetria.component.html",
	styleUrls: ["./avaliacao-editor-perimetria.component.scss"],
})
export class AvaliacaoEditorPerimetriaComponent implements OnInit {
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;
	@Input() aluno: AlunoBase;

	@ViewChild("textCircunAbdominal", { static: true }) textCircunAbdominal;
	@ViewChild("textDiametroJoelho", { static: true }) textDiametroJoelho;
	@ViewChild("textDiametroCotovelo", { static: true }) textDiametroCotovelo;
	@ViewChild("textDiametroTornozelo", { static: true }) textDiametroTornozelo;
	@ViewChild("textDiametroPunho", { static: true }) textDiametroPunho;
	@ViewChild("textRelacaoCinturaQuadril", { static: true })
	textRelacaoCinturaQuadril;

	parametro: string;
	textMask = { mask: false, guide: false };
	fcResultadoRCQ: FormControl = new FormControl(0);
	faixaIdadeAluno = "";
	faixaCalculoRCQ = "";
	tipoClassificacao = "";
	tabelaReferenciaRCQ = [];
	referenciaMasculinaRCQ = [
		{
			idade: "De 20 a 29",
			baixo: "< 0,83",
			moderado: "0,83 a 0,88",
			alto: "0,89 a 0,94",
			muitoAlto: "> 0,94",
		},
		{
			idade: "De 30 a 39",
			baixo: "< 0,84",
			moderado: "0,84 a 0,91",
			alto: "0,92 a 0,96",
			muitoAlto: "> 0,96",
		},
		{
			idade: "De 40 a 49",
			baixo: "< 0,88",
			moderado: "0,88 a 0,95",
			alto: "0,96 a 1,00",
			muitoAlto: "> 1,00",
		},
		{
			idade: "De 50 a 59",
			baixo: "< 0,90",
			moderado: "0,90 a 0,96",
			alto: "0,97 a 1,02",
			muitoAlto: "> 1,02",
		},
		{
			idade: "De 60 a 69",
			baixo: "< 0,91",
			moderado: "0,91 a 0,98",
			alto: "0,99 a 1,03",
			muitoAlto: "> 1,03",
		},
	];
	referenciaFemininaRCQ = [
		{
			idade: "De 20 a 29",
			baixo: "< 0,71",
			moderado: "0,71 a 0,77",
			alto: "0,78 a 0,82",
			muitoAlto: "> 0,82",
		},
		{
			idade: "De 30 a 39",
			baixo: "< 0,72",
			moderado: "0,72 a 0,78",
			alto: "0,79 a 0,84",
			muitoAlto: "> 0,84",
		},
		{
			idade: "De 40 a 49",
			baixo: "< 0,73",
			moderado: "0,73 a 0,79",
			alto: "0,80 a 0,87",
			muitoAlto: "> 0,87",
		},
		{
			idade: "De 50 a 59",
			baixo: "< 0,74",
			moderado: "0,74 a 0,81",
			alto: "0,82 a 0,88",
			muitoAlto: "> 0,88",
		},
		{
			idade: "De 60 a 69",
			baixo: "< 0,76",
			moderado: "0,76 a 0,83",
			alto: "0,84 a 0,90",
			muitoAlto: "> 0,90",
		},
	];

	constructor(
		private modal: NgbModal,
		private cd: ChangeDetectorRef,
		private localization: LocalizationService
	) {}

	ngOnInit() {
		this.configureMask();
		this.iniciarDependenciasRCQ();
	}

	btnClickHandler(value) {
		const modalRef = this.modal.open(AvaliacaoInfoModalComponent);
		modalRef.componentInstance.content = this[value];
	}

	private configureMask() {
		const thousands = this.localization.getLocalethousandsSeparatorSymbol();
		const decimals = this.localization.getLocaleDecimalSymbol();
		this.textMask.mask = createNumberMask({
			prefix: "",
			includeThousandsSeparator: true,
			integerLimit: 6,
			allowDecimal: false,
			thousandsSeparatorSymbol: thousands,
			decimalSymbol: decimals,
		});
	}

	iniciarDependenciasRCQ() {
		this.validarTipoClassificacaoRCQ();
		this.calcularFaixaIdadeAlunoRCQ();
		this.calcularRCQ();
	}

	validarTipoClassificacaoRCQ() {
		if (this.aluno.sexo.toUpperCase() === "M") {
			this.tipoClassificacao = "- Classificação Masculina";
			this.tabelaReferenciaRCQ = this.referenciaMasculinaRCQ;
		} else {
			this.tipoClassificacao = "- Classificação Feminina";
			this.tabelaReferenciaRCQ = this.referenciaFemininaRCQ;
		}
		this.cd.detectChanges();
	}

	calcularRCQ() {
		if (
			this.formGroup.get("quadril").value !== undefined &&
			this.formGroup.get("quadril").value > 0 &&
			this.formGroup.get("cintura").value !== undefined &&
			this.formGroup.get("cintura").value > 0
		) {
			const resultado =
				this.formGroup.get("cintura").value /
				this.formGroup.get("quadril").value;
			this.fcResultadoRCQ.setValue(resultado);
			this.calcularFaixaRCQ();
		} else {
			this.fcResultadoRCQ.setValue(0);
			this.faixaCalculoRCQ = "";
		}
		this.cd.detectChanges();
	}

	calcularFaixaIdadeAlunoRCQ() {
		if (this.aluno.idade <= 29) {
			this.faixaIdadeAluno = "De 20 a 29";
		} else if (this.aluno.idade >= 30 && this.aluno.idade <= 39) {
			this.faixaIdadeAluno = "De 30 a 39";
		} else if (this.aluno.idade >= 40 && this.aluno.idade <= 49) {
			this.faixaIdadeAluno = "De 40 a 49";
		} else if (this.aluno.idade >= 50 && this.aluno.idade <= 59) {
			this.faixaIdadeAluno = "De 50 a 59";
		} else if (this.aluno.idade >= 60) {
			this.faixaIdadeAluno = "De 60 a 69";
		}
		this.cd.detectChanges();
	}

	calcularFaixaRCQ() {
		if (this.aluno.sexo.toUpperCase() === "M") {
			// CALCULOS PARA REFERENCIA MASCULINO ****************************************************
			if (this.faixaIdadeAluno === "De 20 a 29") {
				if (this.fcResultadoRCQ.value < 0.83) {
					this.faixaCalculoRCQ = "< 0,83";
				} else if (
					this.fcResultadoRCQ.value >= 0.83 &&
					this.fcResultadoRCQ.value < 0.89
				) {
					this.faixaCalculoRCQ = "0,83 a 0,88";
				} else if (
					this.fcResultadoRCQ.value >= 0.89 &&
					this.fcResultadoRCQ.value <= 0.94
				) {
					this.faixaCalculoRCQ = "0,89 a 0,94";
				} else if (this.fcResultadoRCQ.value > 0.94) {
					this.faixaCalculoRCQ = "> 0,94";
				}
			} else if (this.faixaIdadeAluno === "De 30 a 39") {
				if (this.fcResultadoRCQ.value < 0.84) {
					this.faixaCalculoRCQ = "< 0,84";
				} else if (
					this.fcResultadoRCQ.value >= 0.84 &&
					this.fcResultadoRCQ.value < 0.92
				) {
					this.faixaCalculoRCQ = "0,84 a 0,91";
				} else if (
					this.fcResultadoRCQ.value >= 0.92 &&
					this.fcResultadoRCQ.value <= 0.96
				) {
					this.faixaCalculoRCQ = "0,92 a 0,96";
				} else if (this.fcResultadoRCQ.value > 0.96) {
					this.faixaCalculoRCQ = "> 0,96";
				}
			} else if (this.faixaIdadeAluno === "De 40 a 49") {
				if (this.fcResultadoRCQ.value < 0.88) {
					this.faixaCalculoRCQ = "< 0,88";
				} else if (
					this.fcResultadoRCQ.value >= 0.88 &&
					this.fcResultadoRCQ.value < 0.96
				) {
					this.faixaCalculoRCQ = "0,88 a 0,95";
				} else if (
					this.fcResultadoRCQ.value >= 0.96 &&
					this.fcResultadoRCQ.value <= 1.0
				) {
					this.faixaCalculoRCQ = "0,96 a 1,00";
				} else if (this.fcResultadoRCQ.value > 1.0) {
					this.faixaCalculoRCQ = "> 1,00";
				}
			} else if (this.faixaIdadeAluno === "De 50 a 59") {
				if (this.fcResultadoRCQ.value < 0.9) {
					this.faixaCalculoRCQ = "< 0,90";
				} else if (
					this.fcResultadoRCQ.value >= 0.9 &&
					this.fcResultadoRCQ.value < 0.97
				) {
					this.faixaCalculoRCQ = "0,90 a 0,96";
				} else if (
					this.fcResultadoRCQ.value >= 0.97 &&
					this.fcResultadoRCQ.value <= 1.02
				) {
					this.faixaCalculoRCQ = "0,97 a 1,02";
				} else if (this.fcResultadoRCQ.value > 1.02) {
					this.faixaCalculoRCQ = "> 1,02";
				}
			} else if (this.faixaIdadeAluno === "De 60 a 69") {
				if (this.fcResultadoRCQ.value < 0.91) {
					this.faixaCalculoRCQ = "< 0,91";
				} else if (
					this.fcResultadoRCQ.value >= 0.91 &&
					this.fcResultadoRCQ.value < 0.99
				) {
					this.faixaCalculoRCQ = "0,91 a 0,98";
				} else if (
					this.fcResultadoRCQ.value >= 0.99 &&
					this.fcResultadoRCQ.value <= 1.03
				) {
					this.faixaCalculoRCQ = "0,99 a 1,03";
				} else if (this.fcResultadoRCQ.value > 1.03) {
					this.faixaCalculoRCQ = "> 1,03";
				}
			}
		} else {
			// CALCULOS PARA REFERENCIA FEMININA ****************************************************
			if (this.faixaIdadeAluno === "De 20 a 29") {
				if (this.fcResultadoRCQ.value < 0.71) {
					this.faixaCalculoRCQ = "< 0,71";
				} else if (
					this.fcResultadoRCQ.value >= 0.71 &&
					this.fcResultadoRCQ.value < 0.78
				) {
					this.faixaCalculoRCQ = "0,71 a 0,77";
				} else if (
					this.fcResultadoRCQ.value >= 0.78 &&
					this.fcResultadoRCQ.value <= 0.82
				) {
					this.faixaCalculoRCQ = "0,78 a 0,82";
				} else if (this.fcResultadoRCQ.value > 0.82) {
					this.faixaCalculoRCQ = "> 0,82";
				}
			} else if (this.faixaIdadeAluno === "De 30 a 39") {
				if (this.fcResultadoRCQ.value < 0.72) {
					this.faixaCalculoRCQ = "< 0,72";
				} else if (
					this.fcResultadoRCQ.value >= 0.72 &&
					this.fcResultadoRCQ.value < 0.79
				) {
					this.faixaCalculoRCQ = "0,72 a 0,78";
				} else if (
					this.fcResultadoRCQ.value >= 0.79 &&
					this.fcResultadoRCQ.value <= 0.84
				) {
					this.faixaCalculoRCQ = "0,79 a 0,84";
				} else if (this.fcResultadoRCQ.value > 0.84) {
					this.faixaCalculoRCQ = "> 0,84";
				}
			} else if (this.faixaIdadeAluno === "De 40 a 49") {
				if (this.fcResultadoRCQ.value < 0.73) {
					this.faixaCalculoRCQ = "< 0,73";
				} else if (
					this.fcResultadoRCQ.value >= 0.73 &&
					this.fcResultadoRCQ.value < 0.8
				) {
					this.faixaCalculoRCQ = "0,73 a 0,79";
				} else if (
					this.fcResultadoRCQ.value >= 0.8 &&
					this.fcResultadoRCQ.value <= 0.87
				) {
					this.faixaCalculoRCQ = "0,80 a 0,87";
				} else if (this.fcResultadoRCQ.value > 0.87) {
					this.faixaCalculoRCQ = "> 0,87";
				}
			} else if (this.faixaIdadeAluno === "De 50 a 59") {
				if (this.fcResultadoRCQ.value < 0.74) {
					this.faixaCalculoRCQ = "< 0,74";
				} else if (
					this.fcResultadoRCQ.value >= 0.74 &&
					this.fcResultadoRCQ.value < 0.82
				) {
					this.faixaCalculoRCQ = "0,74 a 0,81";
				} else if (
					this.fcResultadoRCQ.value >= 0.82 &&
					this.fcResultadoRCQ.value <= 0.88
				) {
					this.faixaCalculoRCQ = "0,82 a 0,88";
				} else if (this.fcResultadoRCQ.value > 0.88) {
					this.faixaCalculoRCQ = "> 0,88";
				}
			} else if (this.faixaIdadeAluno === "De 60 a 69") {
				if (this.fcResultadoRCQ.value < 0.76) {
					this.faixaCalculoRCQ = "< 0,76";
				} else if (
					this.fcResultadoRCQ.value >= 0.76 &&
					this.fcResultadoRCQ.value < 0.84
				) {
					this.faixaCalculoRCQ = "0,76 a 0,83";
				} else if (
					this.fcResultadoRCQ.value >= 0.84 &&
					this.fcResultadoRCQ.value <= 0.9
				) {
					this.faixaCalculoRCQ = "0,84 a 0,90";
				} else if (this.fcResultadoRCQ.value > 0.9) {
					this.faixaCalculoRCQ = "> 0,90";
				}
			}
		}
		this.cd.detectChanges();
	}
}
