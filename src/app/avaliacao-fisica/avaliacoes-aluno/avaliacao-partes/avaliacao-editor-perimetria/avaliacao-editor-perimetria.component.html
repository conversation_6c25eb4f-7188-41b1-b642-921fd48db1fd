<div class="editor-perimetria-wrapper">
	<div class="category">
		<div class="category-title" i18n="@@editor-perimetria:perimetros">
			PERÍMETROS
		</div>
		<div class="items">
			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:pescoco">Pescoço</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('pescoco')"
						[id]="'inputPescoco'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:ombro">Ombro</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('ombro')"
						[id]="'inputOmbro'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:torax-busto-relaxado">
					Tórax / Busto relaxado
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('toraxBustoRelaxado')"
						[id]="'inputTorax'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:braco-relaxado">
					Braço
					<span class="bold">esquerdo</span>
					relaxado
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('bracoRelaxadoEsq')"
						[id]="'inputBracoRelaxEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:braco-contraido">
					Braço
					<span class="bold">esquerdo</span>
					contraído
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('bracoContraidoEsq')"
						[id]="'inputBracoContraidoEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:antebraco">
					Antebraço
					<span class="bold">esquerdo</span>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('antebracoEsq')"
						[id]="'inputAntBracoEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:braco-relaxado">
					Braço
					<span class="bold">direito</span>
					relaxado
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('bracoRelaxadoDir')"
						[id]="'inputBracoRelaxDir'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:braco-contraido">
					Braço
					<span class="bold">direito</span>
					contraído
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('bracoContraidoDir')"
						[id]="'inputBracoContraidoDir'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:antebraco">
					Antebraço
					<span class="bold">direito</span>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('antebracoDir')"
						[id]="'inputAntBracoDir'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:cintura">Cintura</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						(keyup)="calcularRCQ()"
						[control]="formGroup.get('cintura')"
						[id]="'inputCintura'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div
					class="item-label"
					i18n="@@editor-perimetria:circunferencia-abdominal">
					Circunferência abdominal
					<i
						(click)="btnClickHandler('textCircunAbdominal')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('circunferenciaAbdominal')"
						[id]="
							'inputCircunferenciaAbdominal'
						"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:quadril">Quadril</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						(keyup)="calcularRCQ()"
						[control]="formGroup.get('quadril')"
						[id]="'inputQuadril'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:coxa-proximal">
					Coxa
					<span class="bold">esquerda</span>
					proximal
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('coxaProximalEsq')"
						[id]="'inputCoxaProximalEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:coxa-media">
					Coxa
					<span class="bold">esquerda</span>
					média
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('coxaMediaEsq')"
						[id]="'inputCoxaMediaEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:coxa-distal">
					Coxa
					<span class="bold">esquerda</span>
					distal
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('coxaDistalEsq')"
						[id]="'inputCoxaDistalEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:panturrilha">
					Panturrilha
					<span class="bold">esquerda</span>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('panturrilhaEsq')"
						[id]="'inputPanturrilhaEsq'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:coxa-proximal">
					Coxa
					<span class="bold">direita</span>
					proximal
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('coxaProximalDir')"
						[id]="'inputCoxaProximalDir'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:coxa-media">
					Coxa
					<span class="bold">direita</span>
					média
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('coxaMediaDir')"
						[id]="'inputCoxaMediaDir'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:coxa-distal">
					Coxa
					<span class="bold">direita</span>
					distal
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('coxaDistalDir')"
						[id]="'inputCoxaDistalDir'"></pacto-avaliacao-input-number>
				</div>
			</div>

			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:panturrilha">
					Panturrilha
					<span class="bold">direita</span>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="formGroup.get('panturrilhaDir')"
						[id]="'inputPanturrilhaDir'"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title" i18n="@@editor-perimetria:diametros-osseos">
			RELAÇÃO CINTURA QUADRIL (RCQ) {{ tipoClassificacao }}
		</div>
		<div class="items">
			<div class="item item-resultado-rcq">
				<div class="item-label" i18n="@@editor-perimetria:quadril">
					Resultado
					<i
						(click)="btnClickHandler('textRelacaoCinturaQuadril')"
						class="pct pct-help-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<input
						[formControl]="fcResultadoRCQ"
						class="input-personalizado"
						disabled
						id="inputResultadoRCQ" />
				</div>
			</div>
		</div>

		<div class="reference-table">
			<div class="linha">
				<div class="item" i18n="@@editor-rml:idade">Idade</div>
				<div class="item" i18n="@@editor-rml:excelente">Baixo</div>
				<div class="item" i18n="@@editor-rml:acima-media">Moderado</div>
				<div class="item" i18n="@@editor-rml:media">Alto</div>
				<div class="item" i18n="@@editor-rml:abaixo-media">Muito Alto</div>
			</div>
			<div
				*ngFor="let item of tabelaReferenciaRCQ"
				[ngClass]="{ active: faixaIdadeAluno === item.idade }"
				class="linha">
				<div class="item">{{ item.idade }}</div>
				<div
					[ngClass]="{ active: faixaCalculoRCQ === item.baixo }"
					class="item">
					{{ item.baixo }}
				</div>
				<div
					[ngClass]="{ active: faixaCalculoRCQ === item.moderado }"
					class="item">
					{{ item.moderado }}
				</div>
				<div [ngClass]="{ active: faixaCalculoRCQ === item.alto }" class="item">
					{{ item.alto }}
				</div>
				<div
					[ngClass]="{ active: faixaCalculoRCQ === item.muitoAlto }"
					class="item">
					{{ item.muitoAlto }}
				</div>
			</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title" i18n="@@editor-perimetria:diametros-osseos">
			DIÂMETROS ÓSSEOS
		</div>
		<div class="items">
			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:diametro-punho">
					Diâmetro do punho (biestilóide)
					<i
						(click)="btnClickHandler('textDiametroPunho')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>

				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('diametroPunho')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:diametro-joelho">
					Diâmetro do joelho (bicondiliano femural)
					<i
						(click)="btnClickHandler('textDiametroJoelho')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('diametroJoelho')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:diametro-cotovelo">
					Diâmetro do cotovelo (biepicondiliano umeral)
					<i
						(click)="btnClickHandler('textDiametroCotovelo')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('diametroCotovelo')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@editor-perimetria:diametro-tornozelo">
					Diâmetro do tornozelo (bimaleolar)
					<i
						(click)="btnClickHandler('textDiametroTornozelo')"
						class="fas fa-question-circle"
						ngbTooltip="Click"></i>
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('diametroTornozelo')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>
</div>

<ng-template #textCircunAbdominal>
	<h2>Circunferência Abdominal</h2>
	<div>
		<b>Informação -</b>
		Medida realizada no plano transverso. Estando o avaliado em pé, em posição
		ortostática, posicionar a fita métrica sobre a cicatriz umbilical.
		<br />
	</div>
</ng-template>

<ng-template #textDiametroJoelho>
	<h2>Diâmetro Joelho</h2>
	<div>
		<b>Local de coleta -</b>
		diatância entro os côndilos medial e laterla do fêmur.
		<br />
		<b>Critérios na mensuração -</b>
		estando o testado sentado com os pés, apoiados no chão, a coxa formando um
		ângulo de 90 graus com o tronco e a perna formando ângulo de 90 graus com a
		coxa.
		<br />
		<b>Coleta -</b>
		o diâmetro deve ser feito com as astes do Paquimetro nas duas estruturas sem
		usar muita força. Deve ser sentido a estrutura óssea.
		<br />
	</div>
</ng-template>
<ng-template #textDiametroCotovelo>
	<h2>Diâmetro Cotovelo</h2>
	<div>
		<b>Local de coleta -</b>
		distância entre os epicôndilos medial e lateral do úmero
		<br />
		<b>Coleta -</b>
		com o indivíduo em posição ortostáticam braço flexionado em 90 graus com o
		tronco e o antebraço formando 90 graus com o braço.
		<br />
	</div>
</ng-template>
<ng-template #textDiametroTornozelo>
	<h2>Diâmetro Tornozelo</h2>
	<div>
		<b>Local de entrada -</b>
		distância entre os dois maléolos (medial e lateral)
		<br />
		<b>Coleta -</b>
		estando o testado sentado e com os pés apoiados no chão.
		<br />
	</div>
</ng-template>
<ng-template #textDiametroPunho>
	<h2>Diâmetro Punho</h2>
	<div>
		<b>Local de coleta -</b>
		distância entre os procesos estiloides do rádio e da ulna.
		<br />
		<b>Critérios na mensuração -</b>
		estando o testado sentado com o cotovelo em 90 graus de flexão, o punho em
		flexão de 90 graus e o ante-braço pronado.
		<br />
		<b>Coleta -</b>
		o diâmetro deve ser feito com as astes do Paquimetro nas duas estruturas sem
		usar muita força. Deve ser sentido a estrutura óssea.
		<br />
	</div>
</ng-template>
<ng-template #textRelacaoCinturaQuadril>
	<h2>Relação Cintura Quadril (RCQ)</h2>
	<div>Fórmula para cálculo: RCQ = cintura / quadril</div>
</ng-template>
