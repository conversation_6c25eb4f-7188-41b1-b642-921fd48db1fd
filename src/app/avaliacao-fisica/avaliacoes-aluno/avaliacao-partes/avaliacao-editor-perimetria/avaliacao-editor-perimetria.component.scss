@import "src/assets/scss/pacto/plataforma-import.scss";

.editor-perimetria-wrapper {
	padding: 20px;
	background-color: #fff;
}

.category {
	margin-bottom: 20px;

	.category-title {
		font-weight: 600;
		font-size: 17px;
		color: #777;
		border-bottom: 1px solid rgb(170, 170, 170);
		margin-bottom: 5px;
		padding-bottom: 5px;
	}

	.item-resultado-rcq {
		background: #efefef !important;
	}

	.items .item {
		display: flex;
		background-color: #efefef;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		.item-label {
			flex-grow: 1;

			i {
				padding-left: 6px;

				&:hover {
					cursor: pointer;
				}
			}
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
			padding-left: 20px;

			input {
				width: 60px;
			}
		}
	}

	.item.header {
		display: flex;
		flex-direction: row-reverse;

		.header-cel {
			font-weight: 600;
			font-size: 17px;
			width: 100px;
			text-align: center;
		}
	}
}

.sapamText {
	white-space: pre;
}

.my-custom-class .tooltip-inner {
	background-color: darkgreen;
	font-size: 125%;
}

.my-custom-class .arrow::before {
	border-top-color: darkgreen;
}

.bold {
	font-weight: bold;
	color: $preto07;
}

.reference-table {
	font-size: 17px;

	.linha {
		display: flex;
		background-color: #efefef;
		line-height: 42px;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		&:first-child {
			font-weight: 600;
		}

		&.active .item.active {
			background-color: #6f6f6f;
			color: #fff;
			font-weight: 600;
		}

		.item {
			width: 20%;
			flex-grow: 0;
			text-align: center;
			flex-shrink: 0;
		}
	}
}

.input-personalizado {
	border-radius: 6px;
	min-height: 20px;
	color: #67757c;
	display: initial;
	padding: 0.25rem 0.5rem;
	font-size: 0.875rem;
	line-height: 1.5;
	width: 100% !important;
	background-color: #ffffff;
	background-clip: padding-box;
	border: 1px solid #ced4da;
}
