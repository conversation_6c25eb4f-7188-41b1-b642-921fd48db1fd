.editor-peso-altura-wrapper {
	background-color: #fff;
	padding: 20px;
}

.heads-up-row {
	display: flex;

	.block {
		border: 1px solid #aaa;
		padding: 10px;
		width: 50%;

		&:first-child {
			border-right: 0px;
		}
	}

	.title {
		font-size: 18px;
		font-weight: 600;
		border-bottom: 1px solid #d2d2d2;
		margin-bottom: 20px;
		padding-bottom: 10px;
	}

	.input-block {
		display: flex;
		justify-content: center;
	}

	.unit-block {
		text-align: center;
		font-weight: 16px;
	}
}

.category {
	margin-top: 40px;

	.category-title {
		font-weight: 600;
		font-size: 17px;
		color: #777;
		border-bottom: 1px solid rgb(170, 170, 170);
		margin-bottom: 5px;
		padding-bottom: 5px;
	}

	.items .item {
		display: flex;
		background-color: #efefef;
		line-height: 42px;
		padding: 0px 10px;
		font-size: 16px;
		color: #444;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		.item-label {
			flex-grow: 1;
		}

		.input-column {
			width: 100px;
			flex-grow: 0;
			flex-shrink: 0;
		}
	}
}

.reference-table {
	font-size: 17px;

	.linha {
		display: flex;
		background-color: #efefef;
		line-height: 42px;

		&:nth-child(2n + 1) {
			background-color: #e2e2e2;
		}

		&:first-child {
			font-weight: 600;
		}

		.item {
			width: 11.11%;
			flex-grow: 0;
			text-align: center;
			flex-shrink: 0;
		}
	}
}

i.pct {
	margin-left: 10px;
}

.category-title-fc {
	display: flex;
}

.tooltipFrequenciaCardiaca {
	position: relative;
}

.tooltipFrequenciaCardiaca .tooltipFCText {
	visibility: hidden;
	background-color: #e8e8e8;
	color: #444;
	border-radius: 6px;
	position: absolute;
	z-index: 1;
	bottom: 150%;
	margin-left: -500px;
	box-shadow: 0px 6px 15px -5px #616161;
	transition: 0.5s;
	opacity: 0;
	font-size: 12px;
	text-align: left;
	padding: 12px;
	width: 990px;
	font-weight: 100;
}

.tooltipFrequenciaCardiaca .tooltipFCText::after {
	content: "";
	position: absolute;
	top: 100%;
	left: 50%;
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: #e8e8e8 transparent transparent transparent;
}

.tooltipFrequenciaCardiaca:hover .tooltipFCText {
	opacity: 1;
	visibility: visible;
	-webkit-transition: 0.5s ease-in;
	-moz-transition: 0.5s ease-in;
	-o-transition: 0.5s ease-in;
	transition: 0.5s ease-in;
}

.tooltipFCText strong {
	font-weight: bold;
	color: #000000;
}

@media (max-width: 1550px) {
	.tooltipFrequenciaCardiaca:hover .tooltipFCText {
		left: -100px;
	}
}

@media (max-width: 1440px) {
	.tooltipFrequenciaCardiaca:hover .tooltipFCText {
		left: -200px;
	}
}

@media (max-width: 1340px) {
	.tooltipFrequenciaCardiaca:hover .tooltipFCText {
		left: -239px;
	}
}
