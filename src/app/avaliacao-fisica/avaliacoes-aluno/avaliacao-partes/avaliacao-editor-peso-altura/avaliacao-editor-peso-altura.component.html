<div class="editor-peso-altura-wrapper">
	<div class="heads-up-row">
		<div class="block">
			<div class="title" i18n="@@edit-peso-altura:peso">Peso</div>
			<div class="input-block">
				<pacto-avaliacao-input-number
					[control]="formGroup.get('peso')"
					[id]="'inputPeso'"
					[largeInput]="true"></pacto-avaliacao-input-number>
			</div>
			<div class="unit-block" i18n="@@edit-peso-altura:kg">kg</div>
		</div>
		<div class="block">
			<div class="title" i18n="@@edit-peso-altura:altura">Altura</div>
			<div class="input-block">
				<pacto-avaliacao-input-number
					[control]="formGroup.get('altura')"
					[id]="'inputAltura'"
					[largeInput]="true"></pacto-avaliacao-input-number>
			</div>
			<div class="unit-block">m</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title" i18n="@@edit-peso-altura:pressao-frequencia">
			PRESSÃO E FREQUÊNCIA CARDÍACA EM REPOUSO
		</div>
		<div class="items">
			<div *ngIf="usarPressaoArticular" class="item">
				<div class="item-label" i18n="@@edit-peso-altura:pessao-arterial">
					Pressão arterial
				</div>
				<div class="input-column">
					<input
						(click)="selectLine($event)"
						[formControl]="formGroup.get('pressaoArterial')"
						class="form-control form-control-sm" />
				</div>
			</div>
			<div *ngIf="!usarPressaoArticular" class="item">
				<div class="item-label">Pressão arterial sistólica</div>
				<div class="input-column">
					<input
						(click)="selectLine($event)"
						[formControl]="formGroup.get('pressaoSistolica')"
						class="form-control form-control-sm" />
				</div>
			</div>
			<div *ngIf="!usarPressaoArticular" class="item">
				<div class="item-label">Pressão arterial diastólica</div>
				<div class="input-column">
					<input
						(click)="selectLine($event)"
						[formControl]="formGroup.get('pressaoDiastolica')"
						class="form-control form-control-sm" />
				</div>
			</div>
			<div class="item">
				<div class="item-label" i18n="@@edit-peso-altura:frequencia-cardiaca">
					Frequência cardíaca em repouso
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('frequencia')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>
	</div>

	<div class="category">
		<div class="category-title category-title-fc">
			<p i18n="@@edit-peso-altura:frequencia-cardiaca-intensidade:titulo">
				FREQUÊNCIA CARDÍACA POR INTENSIDADE DO TREINO (BPM) -
				{{ tituloFrequenciaCardiaca }}
			</p>
			<p class="tooltipFrequenciaCardiaca">
				<i class="pct pct-info"></i>
				<span
					class="tooltipFCText"
					i18n="@@edit-peso-altura:frequencia-cardiaca-intensidade:descricao">
					A frequência cardíaca máxima é sugerida pelo sistema através do método
					<strong>Astrand</strong>
					, que consiste na seguinte fórmula:
					<br />
					Nas mulheres,
					<strong>226 - a idade</strong>
					<br />
					Nos homens,
					<strong>220 - a idade</strong>
					<br />
					A tabela de frequência cardíaca do treino é calculada seguindo o
					método proposto por
					<strong>Karvonen</strong>
					, cuja fórmula é: FCtreino = FCrepouso + ((FCmax - FCrepouso) x
					(intensidade/100)).
					<br />
				</span>
			</p>
		</div>
		<div class="items">
			<div class="item">
				<div
					class="item-label"
					i18n="@@edit-peso-altura:frequencia-cardiaca-maxima">
					Frequência cardíaca máxima (bpm)
				</div>
				<div class="input-column">
					<pacto-avaliacao-input-number
						[control]="
							formGroup.get('frequenciaMax')
						"></pacto-avaliacao-input-number>
				</div>
			</div>
		</div>

		<div class="reference-table">
			<div class="linha">
				<div class="item">55%</div>
				<div class="item">60%</div>
				<div class="item">65%</div>
				<div class="item">70%</div>
				<div class="item">75%</div>
				<div class="item">80%</div>
				<div class="item">85%</div>
				<div class="item">90%</div>
				<div class="item">95%</div>
			</div>
			<div class="linha">
				<div class="item">{{ calcularPorcentagemFrequencia(0.55) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.6) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.65) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.7) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.75) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.8) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.85) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.9) }}</div>
				<div class="item">{{ calcularPorcentagemFrequencia(0.95) }}</div>
			</div>
		</div>
	</div>
</div>
