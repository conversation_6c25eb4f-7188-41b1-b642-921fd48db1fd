import { Component, OnInit, Input } from "@angular/core";
import { AvaliacaoFisica, AlunoBase } from "treino-api";
import { FormGroup } from "@angular/forms";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";

@Component({
	selector: "pacto-avaliacao-editor-peso-altura",
	templateUrl: "./avaliacao-editor-peso-altura.component.html",
	styleUrls: ["./avaliacao-editor-peso-altura.component.scss"],
})
export class AvaliacaoEditorPesoAlturaComponent implements OnInit {
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;
	@Input() alunoDados: AlunoBase;
	entity = true;
	usarPressaoArticular = false;
	tituloFrequenciaCardiaca: any;
	sexo: any;

	constructor(private treinoConfigService: TreinoConfigCacheService) {}

	get alturaMask() {
		return [/[0-9]/, ",", /[0-9]/, /[0-9]/];
	}

	ngOnInit() {
		const configuracoesAvaliacao =
			this.treinoConfigService.configuracoesAvaliacao;
		if (JSON.parse(configuracoesAvaliacao.usar_pressao_sistolica_diastolica)) {
			this.usarPressaoArticular = false;
		} else {
			this.usarPressaoArticular = true;
		}
		this.loadTitleFrequencia();
	}

	private loadTitleFrequencia() {
		this.sexo =
			this.alunoDados.sexo.toLocaleLowerCase() === "m"
				? "Masculino"
				: "Feminino";
		this.tituloFrequenciaCardiaca = this.sexo
			.concat(" - ")
			.concat(this.alunoDados.idade.toString())
			.concat(" anos");
	}

	get frequenciaMax(): number {
		const value = this.formGroup.get("frequenciaMax").value;
		if (value) {
			return parseInt(this.formGroup.get("frequenciaMax").value, 10);
		} else {
			return null;
		}
	}

	selectLine($event) {
		$event.target.select();
	}

	disable() {
		this.formGroup.disable();
	}

	enable() {
		this.formGroup.enable();
	}

	calcularPorcentagemFrequencia(porcentagem: number) {
		if (this.frequenciaMax) {
			return Math.trunc(this.frequenciaMax * porcentagem);
		} else {
			return "";
		}
	}
}
