.avaliacao-editor-meta-recom-wrapper {
	padding: 20px;
	background-color: #fff;
}

.heads-up-row {
	display: flex;

	.block {
		border: 1px solid #aaa;
		padding: 10px;
		width: 50%;

		&:first-child {
			border-right: 0px;
		}
	}

	.title {
		font-size: 18px;
		font-weight: 600;
		border-bottom: 1px solid #d2d2d2;
		margin-bottom: 20px;
		padding-bottom: 10px;
	}

	.input-block {
		display: flex;
		justify-content: center;

		input {
			width: 200px !important;
		}
	}

	.unit-block {
		text-align: center;
		font-weight: 16px;
	}
}

.obs-title {
	font-weight: 600;
	font-size: 17px;
	color: #777;
	border-bottom: 1px solid rgb(170, 170, 170);
	margin-bottom: 10px;
	margin-top: 20px;
	padding-bottom: 5px;
}
