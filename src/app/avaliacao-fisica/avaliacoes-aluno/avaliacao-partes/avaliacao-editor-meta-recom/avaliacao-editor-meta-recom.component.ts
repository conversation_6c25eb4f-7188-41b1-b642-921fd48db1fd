import { Component, OnInit, Input } from "@angular/core";
import { AvaliacaoFisica } from "treino-api";
import { FormGroup, FormControl } from "@angular/forms";

@Component({
	selector: "pacto-avaliacao-editor-meta-recom",
	templateUrl: "./avaliacao-editor-meta-recom.component.html",
	styleUrls: ["./avaliacao-editor-meta-recom.component.scss"],
})
export class AvaliacaoEditorMetaRecomComponent implements OnInit {
	@Input() formGroup: FormGroup;
	@Input() avaliacao: AvaliacaoFisica;

	constructor() {}

	ngOnInit() {}
}
