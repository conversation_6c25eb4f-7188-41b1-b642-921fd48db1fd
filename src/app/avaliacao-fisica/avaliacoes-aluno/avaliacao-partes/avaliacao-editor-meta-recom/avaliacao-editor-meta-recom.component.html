<div *ngIf="avaliacao" class="avaliacao-editor-meta-recom-wrapper">
	<div class="heads-up-row">
		<div class="block">
			<div class="title" i18n="@@editor-meta:meta-percentual-gordura">
				Meta de percentual de gordura anterior
			</div>
			<div class="input-block">
				{{
					avaliacao.metaRecomendacoes.percentualGorduraAnterior !== null
						? avaliacao.metaRecomendacoes.percentualGorduraAnterior
						: "-"
				}}
			</div>
			<div class="unit-block">%</div>
		</div>
		<div class="block">
			<div
				class="title"
				i18n="@@editor-meta:meta-percentual-gordura-proxima-avaliacao">
				Meta de percentual de gordura para a próxima avaliação
			</div>
			<div class="input-block" id="meta-percentual-gordura-prox">
				<pacto-avaliacao-input-number
					[control]="
						formGroup.get('percentualGorduraProxima')
					"></pacto-avaliacao-input-number>
			</div>
			<div class="unit-block">%</div>
		</div>
	</div>

	<div class="obs-title" i18n="@@editor-meta:observacao">OBSERVAÇÃO</div>
	<div class="form-group" id="observacao-meta-percentual-gordura">
		<textarea
			[formControl]="formGroup.get('observacoesAvaliador')"
			class="form-control form-control-sm"
			rows="3"></textarea>
	</div>
</div>
