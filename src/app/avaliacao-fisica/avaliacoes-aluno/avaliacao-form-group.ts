import { FormGroup, FormControl, Validators } from "@angular/forms";

export function buildAvaliacaoForm() {
	return new FormGroup({
		dataAvaliacao: new FormControl(),
		dataProxima: new FormControl(),
		objetivos: new FormControl(),
		anamneseRespostas: new FormControl(),
		parQRespostas: new FormControl(),
		pesoAltura: buildFormAlturaPeso(),
		dobras: buildFormDobras(),
		perimetria: buildFormPerimetria(),
		flexibilidade: buildFormFlexibilidade(),
		postura: buildFormPostura(),
		rml: buildFormRml(),
		vo2: buildFormVo2Max(),
		somatotipia: buildFormSomopatia(),
		metaRecomendacoes: buildFormMeta(),
		temImportacaoBiosanny: new FormControl(),
	});
}

function buildFormMeta() {
	return new FormGroup({
		percentualGorduraProxima: new FormControl(),
		observacoesAvaliador: new FormControl(),
	});
}

function buildFormSomopatia() {
	return new FormGroup({
		dobraTriceps: new FormControl(),
		dobraSupraEspinhal: new FormControl(),
		dobraSubescapular: new FormControl(),
		dobraPanturrilha: new FormControl(),
		perimetroPanturrilhaDireita: new FormControl(),
		perimetroBracoContraidoDireito: new FormControl(),
		diametroPunho: new FormControl(),
		diametroJoelho: new FormControl(),
		diametroCotovelo: new FormControl(),
		diametroTornozelo: new FormControl(),
	});
}

function buildFormVo2Max() {
	return new FormGroup({
		protocolo: new FormControl(),
		vo2Max: new FormControl(0),
		limiarVentilatorioI: new FormControl(0),
		limiarVentilatorioII: new FormControl(0),
		caminhadaCorridaDistancia: new FormControl(0),
		teste2400MetrosTempo: new FormControl(0, [Validators.pattern(/[0-9]+/)]),
		aerobicoBancoFrequencia: new FormControl(0),
		astrandFrequencia4: new FormControl(0),
		astrandFrequencia5: new FormControl(0),
		collegeFrequencia: new FormControl(0),
		astrandCarga: new FormControl(0),
	});
}

function buildFormRml() {
	return new FormGroup({
		flexoesBracos: new FormControl(),
		abdominais: new FormControl(),
	});
}

function buildFormPostura() {
	return new FormGroup({
		frenteImageId: new FormControl(),
		direitaImageId: new FormControl(),
		esquerdaImageId: new FormControl(),
		costasImageId: new FormControl(),
		frenteImageUpload: new FormControl(),
		direitaImageUpload: new FormControl(),
		esquerdaImageUpload: new FormControl(),
		costasImageUpload: new FormControl(),
		visaoLateral: new FormGroup({
			anterversaoQuadril: new FormControl(false),
			hipercifoseToracica: new FormControl(false),
			hiperlordoseCervical: new FormControl(false),
			hiperlordoseLombar: new FormControl(false),
			joelhoFlexo: new FormControl(false),
			joelhoRecurvado: new FormControl(false),
			protusaoAbdominal: new FormControl(false),
			peCalcaneo: new FormControl(false),
			peCavo: new FormControl(false),
			peEquino: new FormControl(false),
			pePlano: new FormControl(false),
			retificacaoCervical: new FormControl(false),
			retificacaoLombar: new FormControl(false),
			retroversaoQuadril: new FormControl(false),
			rotacaoInternaOmbros: new FormControl(false),
		}),
		visaoPosterior: new FormGroup({
			depressaoEscapular: new FormControl(false),
			encurtamentoTrapezio: new FormControl(false),
			escolioseCervical: new FormControl(false),
			escolioseLombar: new FormControl(false),
			escolioseToracica: new FormControl(false),
			protacaoEscapular: new FormControl(false),
			peValgo: new FormControl(false),
			peVaro: new FormControl(false),
			retracaoEscapular: new FormControl(false),
		}),
		visaoAnterior: new FormGroup({
			joelhoValgo: new FormControl(false),
			joelhoVaro: new FormControl(false),
			peAbduto: new FormControl(false),
			peAduto: new FormControl(false),
		}),
		assimetrias: new FormGroup({
			ombrosAssimetricos: new FormControl(),
			assimetriaQuadril: new FormControl(),
		}),
		observacao: new FormControl(),
	});
}

function buildFormAlturaPeso() {
	return new FormGroup({
		peso: new FormControl(),
		altura: new FormControl(),
		pressaoArterial: new FormControl(),
		pressaoDiastolica: new FormControl(),
		pressaoSistolica: new FormControl(),
		frequencia: new FormControl(),
		frequenciaMax: new FormControl(),
	});
}

function buildFormFlexibilidade() {
	return new FormGroup({
		alcanceMaximo: new FormControl(),
		observacao: new FormControl(),
		observacaoOmbro: new FormControl(),
		observacaoJoelho: new FormControl(),
		observacaoQuadril: new FormControl(),
		observacaoTornozelo: new FormControl(),
		mobilidadeOmbroEsquerdo: new FormControl(),
		mobilidadeOmbroDireito: new FormControl(),
		mobilidadeQuadrilEsquerdo: new FormControl(),
		mobilidadeQuadrilDireito: new FormControl(),
		mobilidadeJoelhoEsquerdo: new FormControl(),
		mobilidadeJoelhoDireito: new FormControl(),
		mobilidadeTornozeloEsquerdo: new FormControl(),
		mobilidadeTornozeloDireito: new FormControl(),
	});
}

function buildFormDobras() {
	return new FormGroup({
		logBalanca: new FormControl(),
		protocolo: new FormControl(),
		abdominal: new FormControl(),
		peitoral: new FormControl(),
		coxaMedial: new FormControl(),
		subescapular: new FormControl(),
		supraEspinhal: new FormControl(),
		supraIliaca: new FormControl(),
		triceps: new FormControl(),
		biceps: new FormControl(),
		axilarMedia: new FormControl(),
		panturrilha: new FormControl(),
		bioimpedancia: new FormGroup({
			imc: new FormControl(),
			massaGorda: new FormControl(),
			percentMassaMagra: new FormControl(),
			residuos: new FormControl(),
			gorduraIdeal: new FormControl(),
			reatancia: new FormControl(),
			necFisica: new FormControl(),
			necCalorica: new FormControl(),
			gorduraVisceral: new FormControl(),
			percentMassaGorda: new FormControl(),
			massaMagra: new FormControl(),
			ossos: new FormControl(),
			musculos: new FormControl(),
			resistencia: new FormControl(),
			percentAgua: new FormControl(),
			tmb: new FormControl(),
			idadeMetabolica: new FormControl(),
		}),
	});
}

function buildFormPerimetria() {
	return new FormGroup({
		pescoco: new FormControl(),
		ombro: new FormControl(),
		toraxBustoRelaxado: new FormControl(),
		bracoRelaxadoEsq: new FormControl(),
		bracoRelaxadoDir: new FormControl(),
		bracoContraidoEsq: new FormControl(),
		bracoContraidoDir: new FormControl(),
		antebracoEsq: new FormControl(),
		antebracoDir: new FormControl(),
		cintura: new FormControl(),
		circunferenciaAbdominal: new FormControl(),
		quadril: new FormControl(),
		coxaProximalEsq: new FormControl(),
		coxaProximalDir: new FormControl(),
		coxaMediaEsq: new FormControl(),
		coxaMediaDir: new FormControl(),
		coxaDistalEsq: new FormControl(),
		coxaDistalDir: new FormControl(),
		panturrilhaEsq: new FormControl(),
		panturrilhaDir: new FormControl(),
		diametroPunho: new FormControl(),
		diametroJoelho: new FormControl(),
		diametroCotovelo: new FormControl(),
		diametroTornozelo: new FormControl(),
	});
}
