<div class="perfil-wrapper">
	<div [ngClass]="{ 'loading-blur': loading }">
		<div *ngIf="mostrarTopo" class="top-aux">
			<pacto-avaliacoes-aluno-top
				(enviar)="btnEnviarHandler()"
				(historico)="btnHistoricoHandler()"
				(imprimir)="btnImprimirHandler()"
				(remover)="removeHandler()"
				[aluno]="aluno"
				[avaliacaoAtualId]="avaliacaoAtual?.id"
				[avaliacoes]="alunoAvaliacoes"
				[criando]="criando"
				[parqPositivo]="aluno?.resultadoParq"
				[reload]="atualizar"
				[rota]="rota"
				i18n-rotaLabel="@@avaliacao-fisica:avaliacoes-aluno"
				rotaLabel="Avaliações do aluno"></pacto-avaliacoes-aluno-top>
		</div>

		<div class="body-width">
			<div [hidden]="!mostrarTopo" class="content-tab">
				<div *ngIf="avaliacaoAtual">
					<div class="type-h3 avaliacao-nome">
						<span
							*ngIf="!avaliacaoAtual.id"
							i18n="@@avaliacao-fisica:avaliacoes-aluno:criar">
							Criar Avaliação
						</span>
						<span *ngIf="avaliacaoAtual.id">
							<ng-container i18n="@@avaliacao-fisica:avaliacoes-aluno:editar">
								Editar Avaliação
							</ng-container>
							#{{ avaliacaoAtual.id }} -
							{{ avaliacaoAtual.dataAvaliacao | date : "shortDate" }}
						</span>
					</div>
				</div>
				<pacto-cat-tabs-transparent #avaliacaoTabs>
					<ng-container *ngIf="integracaoZW">
						<div *ngFor="let item of abasJson">
							<ng-template
								*ngIf="configuracoesAvaliacao[item.nomeConfiguracao]"
								label="{{ traducoes.getLabel(item.id) }}"
								pactoTabTransparent="{{ item.id }}"></ng-template>
						</div>
					</ng-container>

					<ng-container *ngIf="!integracaoZW">
						<div *ngFor="let item of abasJsonIndependente">
							<ng-template
								*ngIf="configuracoesAvaliacao[item.nomeConfiguracao]"
								label="{{ traducoes.getLabel(item.id) }}"
								pactoTabTransparent="{{ item.id }}"></ng-template>
						</div>
					</ng-container>
				</pacto-cat-tabs-transparent>
			</div>

			<div *ngIf="avaliacaoAtual" class="content-aux">
				<div class="content-card">
					<div class="content-title">
						<div class="type-h5 categoria">
							<ng-container *ngIf="anamnese">
								{{ traducoes.getLabel("ANAMNESE") }}
							</ng-container>
							<ng-container *ngIf="parQ">
								{{ traducoes.getLabel("PARQ") }}
							</ng-container>
							<ng-container *ngIf="pesoEAltura">
								{{ traducoes.getLabel("PESO_ALTURA") }}
							</ng-container>
							<ng-container *ngIf="dobras">
								{{ traducoes.getLabel("DOBRAS") }}
							</ng-container>
							<ng-container *ngIf="perimetria">
								{{ traducoes.getLabel("PERIMETRIA") }}
							</ng-container>
							<ng-container *ngIf="flexibilidade">
								{{ traducoes.getLabel("FLEXIBILIDADE") }}
							</ng-container>
							<ng-container *ngIf="posturaSelect">
								{{ traducoes.getLabel("POSTURA") }}
							</ng-container>
							<ng-container *ngIf="rml">
								{{ traducoes.getLabel("RML") }}
							</ng-container>
							<ng-container *ngIf="vo2MAX">
								{{ traducoes.getLabel("VO2MAX") }}
							</ng-container>
							<ng-container *ngIf="somatotipia">
								{{ traducoes.getLabel("SOMATOTIPIA") }}
							</ng-container>
							<ng-container *ngIf="metaERec">
								{{ traducoes.getLabel("META_RECOM") }}
							</ng-container>
							<ng-container *ngIf="importacaoBiosanny">
								{{ traducoes.getLabel("IMPORTACAO_BIOSANNY") }}
							</ng-container>
						</div>
						<div
							(click)="cancelarHandler()"
							*ngIf="criando || changes"
							class="button-avaliacao"
							id="cancelar-avaliacao-fisica">
							CANCELAR
						</div>
						<div
							(click)="salvarHandler()"
							*ngIf="criando || changes"
							class="button-avaliacao"
							id="salvar-avaliacao-fisica">
							SALVAR
						</div>
						<div
							(click)="resultadoAvaliacao()"
							*ngIf="!criando"
							class="button-avaliacao"
							id="resultado-avaliacao-fisica">
							RESULTADO AVALIAÇÃO
						</div>
					</div>
					<div class="content-body">
						<!-- ANAMNESE -->
						<ng-container *ngIf="anamnese">
							<div class="row">
								<div class="col-md-3">
									<pacto-datepicker
										[control]="avaliacaoFormGroup.get('dataAvaliacao')"
										i18n-label="@@catalogo-alunos:data-avaliacao:label"
										label="Data da Avaliação"></pacto-datepicker>
								</div>
								<div class="col-md-3">
									<pacto-datepicker
										[control]="avaliacaoFormGroup.get('dataProxima')"
										i18n-label="@@catalogo-alunos:data-proxima:label"
										label="Próxima Avaliação"></pacto-datepicker>
								</div>
								<div class="col-md-6">
									<pacto-cat-avaliacao-objetivos-input
										[formControl]="avaliacaoFormGroup.get('objetivos')"
										[objetivos]="
											objetivos
										"></pacto-cat-avaliacao-objetivos-input>
								</div>
							</div>

							<div class="row">
								<div class="col-md-6">
									<pacto-select
										*ngIf="!avaliacaoAtual || !avaliacaoAtual.id"
										[control]="anamneseFormControl"
										[opcoes]="anamnesesAtivas"
										i18n-label="@@catalogo-alunos:anamnese-selecionado:label"
										label="Anamnese Selecionado"></pacto-select>
									<div *ngIf="avaliacaoAtual?.id" class="type-h5">
										{{ anamneseSelecionada?.nome }}
									</div>
								</div>
							</div>

							<pacto-cat-anamnese-input
								[anamnese]="anamneseSelecionada"
								[formControl]="
									avaliacaoFormGroup.get('anamneseRespostas')
								"></pacto-cat-anamnese-input>
						</ng-container>
						<!-- PARQ -->
						<ng-container *ngIf="parQ">
							<div class="block-resultado">
								<div
									class="label-resultado"
									i18n="@@resposta-anamnese:resultado">
									Resultado:
								</div>
								<div
									*ngIf="!resultadoParq"
									class="info-resultado"
									i18n="@@resposta-anamnese:resultado-negativo">
									Negativo
								</div>
								<div
									*ngIf="resultadoParq"
									class="info-resultado"
									i18n="@@resposta-anamnese:resultado-positivo">
									Positivo
								</div>
							</div>
							<pacto-cat-anamnese-input
								[anamnese]="{ perguntas: parq }"
								[formControl]="
									avaliacaoFormGroup.get('parQRespostas')
								"></pacto-cat-anamnese-input>
						</ng-container>
						<!-- pesoEAltura -->
						<ng-container *ngIf="pesoEAltura">
							<pacto-avaliacao-editor-peso-altura
								[alunoDados]="aluno"
								[avaliacao]="avaliacaoAtual"
								[formGroup]="
									avaliacaoFormGroup.get('pesoAltura')
								"></pacto-avaliacao-editor-peso-altura>
						</ng-container>
						<!-- dobras -->
						<ng-container *ngIf="dobras">
							<pacto-avaliacao-editor-dobras
								[aluno]="aluno"
								[avaliacaoFormGroup]="avaliacaoFormGroup"
								[avaliacao]="avaliacaoAtual"
								[formGroupPesoAltura]="
									avaliacaoFormGroup.get('pesoAltura')
								"></pacto-avaliacao-editor-dobras>
						</ng-container>
						<!-- flexibilidade -->
						<ng-container *ngIf="flexibilidade">
							<pacto-avaliacao-editor-flexibilidade
								[avaliacao]="avaliacaoAtual"
								[formGroup]="
									avaliacaoFormGroup.get('flexibilidade')
								"></pacto-avaliacao-editor-flexibilidade>
						</ng-container>
						<!-- postura -->
						<ng-container *ngIf="posturaSelect">
							<pacto-avaliacao-editor-postura
								[avaliacao]="avaliacaoAtual"
								[formGroup]="
									avaliacaoFormGroup.get('postura')
								"></pacto-avaliacao-editor-postura>
						</ng-container>
						<!-- rml -->
						<ng-container *ngIf="rml">
							<pacto-avaliacao-editor-rml
								[avaliacao]="avaliacaoAtual"
								[formGroup]="avaliacaoFormGroup.get('rml')"
								[rmlConfig]="rmlConfig"></pacto-avaliacao-editor-rml>
						</ng-container>
						<!-- vo2MAX -->
						<ng-container *ngIf="vo2MAX">
							<pacto-avaliacao-editor-vo2max
								[aluno]="aluno"
								[avaliacao]="avaliacaoAtual"
								[configuracao]="configuracoesAvaliacao"
								[formGroupAvaliacao]="avaliacaoFormGroup"
								[formGroup]="
									avaliacaoFormGroup.get('vo2')
								"></pacto-avaliacao-editor-vo2max>
						</ng-container>
						<!-- somatotipia -->
						<ng-container *ngIf="somatotipia">
							<pacto-avaliacao-editor-somopatia
								[avaliacaoFormGroup]="avaliacaoFormGroup"
								[avaliacao]="avaliacaoAtual"></pacto-avaliacao-editor-somopatia>
						</ng-container>
						<!-- metaERec -->
						<ng-container *ngIf="metaERec">
							<pacto-avaliacao-editor-meta-recom
								[avaliacao]="avaliacaoAtual"
								[formGroup]="
									avaliacaoFormGroup.get('metaRecomendacoes')
								"></pacto-avaliacao-editor-meta-recom>
						</ng-container>
						<ng-container *ngIf="importacaoBiosanny">
							<pacto-avaliacao-editor-importacao-biosanny
								[avaliacao]="avaliacaoAtual"
								[formGroup]="
									avaliacaoFormGroup
								"></pacto-avaliacao-editor-importacao-biosanny>
						</ng-container>
						<ng-container *ngIf="perimetria">
							<pacto-avaliacao-editor-perimetria
								[aluno]="aluno"
								[avaliacao]="avaliacaoAtual"
								[formGroup]="
									avaliacaoFormGroup.get('perimetria')
								"></pacto-avaliacao-editor-perimetria>
						</ng-container>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div *ngIf="loading" class="carregando-dados">
	<div class="icon-loding">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="info-carregando-dados">Carregando os dados...</div>
</div>

<span
	#verificaEmailTelefone
	[hidden]="true"
	i18n="@@catalogo-alunos:avaliacao-remove:success">
	Este aluno não possui e-mail e telefone celular cadastrado.
</span>
<span
	#removeSuccessMsg
	[hidden]="true"
	i18n="@@catalogo-alunos:avaliacao-remove:success">
	Avaliação removida com sucesso.
</span>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@avaliacoes:avaliacao-aluno:anamnese" xingling="ANAMNESE">
		Anamnese
	</span>
	<span xingling="PARQ">ParQ</span>
	<span i18n="@@avaliacoes:avaliacao-aluno:peso-altura" xingling="PESO_ALTURA">
		Peso e Altura
	</span>
	<span i18n="@@avaliacoes:avaliacao-aluno:dobras" xingling="DOBRAS">
		Dobras
	</span>
	<span i18n="@@avaliacoes:avaliacao-aluno:perimetria" xingling="PERIMETRIA">
		Perimetria
	</span>
	<span xingling="COMPOSICAO">Composição</span>
	<span
		i18n="@@avaliacoes:avaliacao-aluno:flexibilidade"
		xingling="FLEXIBILIDADE">
		Flexibilidade/Mobilidade
	</span>
	<span xingling="POSTURA">Postura</span>
	<span xingling="RML">Rml</span>
	<span xingling="VO2MAX">Vo2MAX</span>
	<span i18n="@@avaliacoes:avaliacao-aluno:somatotipia" xingling="SOMATOTIPIA">
		Somatotipia
	</span>
	<span i18n="@@avaliacoes:avaliacao-aluno:meta" xingling="META_RECOM">
		Meta / Recomendações
	</span>
	<span i18n="@@avaliacoes:avaliacao-aluno:meta" xingling="IMPORTACAO_BIOSANNY">
		BioSanny
	</span>
	<span xingling="VALIDACAOSEXO">
		Data de nascimento e sexo são dados obrigatórios
	</span>
	<span xingling="VALIDACAOPROXIMADATA">
		A data da próxima avaliação não pode ser anterior à data da avaliação atual
	</span>
	<span xingling="VALIDACAOASTRAND">
		Ops! Para o cálculo do teste de astrand é necessário informar o peso do
		aluno
	</span>
	<span xingling="MENSSAGEM_SUCESSO">Avaliação física salva com sucesso.</span>
	<span xingling="MENSSAGEM_ERRO">
		Não foi possível salvar esta avaliação física, tente novamente.
	</span>
	<span xingling="MENSSAGEM_TEMPO">
		O tempo informado na aba vo2max deve ser preenchido de acordo com a
		formatação.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:VALIDAR_PRODUTO"
		xingling="VALIDAR_PRODUTO">
		O aluno não tem um produto de Avaliação Física vigente pago.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:PRODUTO_ULTILIZADO"
		xingling="PRODUTO_ULTILIZADO">
		O produto vigente já foi usado.
	</span>

	<span #popupErro [hidden]="true">
		Verifique se o seu Pop-ups nao esta bloqueado e tente novamente.
	</span>
	<span #imprimirErro [hidden]="true">
		Não foi possível imprimir esta avaliação.
	</span>
</pacto-traducoes-xingling>
