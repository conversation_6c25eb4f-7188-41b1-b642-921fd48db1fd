@import "~src/assets/scss/pacto/plataforma-import.scss";

.box-left {
	height: 283px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.titulo-box {
	height: 20px;
	line-height: 1;
	text-align: center;
	padding-top: 27px;
	@extend .type-h5;
}

.pct-value {
	text-align: center;
	line-height: 1em;
	margin-top: 50px;
	height: 56px;
	font-size: 56px;
	font-weight: 600;
	color: $pretoPri;
}

.icone-posicao {
	height: 40px;
	font-size: 48px;
	margin-top: 24px;
	text-align: center;
	line-height: 0.83;
	color: $azulimPri;
}

.descricao {
	display: flex;
	justify-content: space-between;
	margin: 6px 30px;
}

.sub-titulo {
	height: 16px;
	margin-top: 9px;
	@extend .type-h5;
}

.sub-descricao {
	margin-top: 15px;
	height: 14px;
	font-family: "N<PERSON>to <PERSON>s", sans-serif;
	font-size: 14px;
	line-height: 1;
	color: $gelo04;
}

.centralizar {
	justify-content: center;
}
