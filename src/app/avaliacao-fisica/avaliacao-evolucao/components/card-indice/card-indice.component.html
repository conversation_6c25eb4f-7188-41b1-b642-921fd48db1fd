<div *ngIf="modeloApresentar === 'PRIMEIRA_ULTIMA'" class="box-left">
	<div class="titulo-box">{{ titulo }}</div>
	<div class="pct-value">{{ percentual || 0 }}%</div>
	<div class="icone-posicao">
		<i *ngIf="icone && !semIcon" class="pct pct-trending-up"></i>
		<i *ngIf="!icone && !semIcon" class="pct pct-trending-down"></i>
	</div>

	<div class="descricao">
		<div>
			<div class="sub-titulo">{{ percentualInicial || 0 }}kg</div>
			<div class="sub-descricao">Primeira</div>
		</div>
		<div>
			<div class="sub-titulo">{{ percentualFinal || 0 }}kg</div>
			<div class="sub-descricao">Última</div>
		</div>
	</div>
</div>

<div *ngIf="modeloApresentar === 'PRIMEIRA_PENULTIMA_ULTIMA'" class="box-left">
	<div class="titulo-box">{{ titulo }}</div>
	<div class="pct-value">{{ percentual || 0 }}%</div>

	<div class="descricao">
		<div>
			<div class="sub-titulo">{{ percentualInicial || 0 }}kg</div>
			<div class="sub-descricao">Primeira</div>
		</div>
		<div>
			<div class="sub-titulo">{{ percentualFinal || 0 }}kg</div>
			<div class="sub-descricao">Última</div>
		</div>
	</div>
	<div
		*ngIf="percentualPenultima && percentualPenultima !== 0"
		[ngClass]="{ centralizar: true }"
		class="descricao">
		<div>
			<div class="sub-titulo">{{ percentualPenultima || 0 }}kg</div>
			<div class="sub-descricao">Penúltima</div>
		</div>
	</div>
</div>
