import { Component, Input, OnInit } from "@angular/core";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";

@Component({
	selector: "pacto-card-indice",
	templateUrl: "./card-indice.component.html",
	styleUrls: ["./card-indice.component.scss"],
})
export class CardIndiceComponent implements OnInit {
	@Input() titulo;
	@Input() icone = false;
	@Input() semIcon = false;
	@Input() percentual = 0;
	@Input() percentualInicial = 0;
	@Input() percentualPenultima = 0;
	@Input() percentualFinal = 0;

	constructor(private treinoConfigService: TreinoConfigCacheService) {}

	get modeloApresentar() {
		return this.treinoConfigService.configuracoesAvaliacao
			.modelo_evolucao_fisica;
	}

	ngOnInit() {}
}
