<div class="top-aux">
	<pacto-avaliacoes-aluno-top
		[aluno]="aluno"
		[criando]="true"
		[rotaLabel]="'Evolução do aluno'"
		[rota]="[
			'/avaliacao',
			'avaliacoes-aluno',
			aluno?.id,
			'avaliacao'
		]"></pacto-avaliacoes-aluno-top>
</div>
<div *ngIf="ready" class="content-aux">
	<div class="type-h3 avaliacao-nome">
		<span>Evolução Física</span>
	</div>

	<div class="bb">
		<div class="box-left avaliacoes">
			<div class="titulo-box">Avaliações Físicas</div>
			<div class="pct-value">{{ evolucaoFisica?.numeroAvaliacoes }}</div>
			<div class="descricao-label">
				Avaliações no período de
				{{ evolucaoFisica?.numeroDiasConsiderados }} dias
			</div>
		</div>

		<div class="gordura">
			<pacto-card-indice
				[icone]="evolucaoFisica.percentualGorduraAumentado"
				[percentualFinal]="evolucaoFisica.massaGordaFinal"
				[percentualInicial]="evolucaoFisica.massaGordaInicial"
				[percentualPenultima]="evolucaoFisica.massaGordaPenultima"
				[percentual]="evolucaoFisica.percentualGordura"
				[titulo]="'Gordura'"></pacto-card-indice>
		</div>
		<div class="massa-magra">
			<pacto-card-indice
				[icone]="evolucaoFisica.percentualMassaMagraAumentando"
				[percentualFinal]="evolucaoFisica.massaMagraFinal"
				[percentualInicial]="evolucaoFisica.massaMagraInicial"
				[percentualPenultima]="evolucaoFisica.massaMagraPenultima"
				[percentual]="evolucaoFisica.percentualMassaMagra"
				[titulo]="'Massa Magra'"></pacto-card-indice>
		</div>

		<div class="box-right-1 exercicios">
			<div class="resumo-grupos">
				<div class="top-row">
					<div class="title">Grupos musculares</div>
					<div class="desc-label">
						<span i18n="@@treino-container:treinos-executados">
							Treinos executados no período:
						</span>
						{{ evolucaoFisica?.treinosPeriodos }}
					</div>
				</div>
				<div class="select-grupos">
					<pacto-cat-select
						*ngIf="gruposItens.length"
						[control]="gruposFc"
						[items]="gruposItens"
						[size]="'SMALL'"></pacto-cat-select>
				</div>
			</div>
			<div>
				<div class="display-flex">
					<div class="col-md-6 componente-corpo-grupo-muscular">
						<div class="frontal">
							<pacto-corpo-frontal-grupo-muscular
								*ngIf="allGruposMusculares"
								[listaAtiva]="gruposMuscularesSelecionados"
								[listaCompletaGrupoMuscular]="allGruposMusculares"
								[podeSelecionarGrupoMuscular]="
									false
								"></pacto-corpo-frontal-grupo-muscular>
						</div>

						<div class="posterior">
							<pacto-corpo-posterior-grupo-muscular
								*ngIf="allGruposMusculares"
								[listaAtiva]="gruposMuscularesSelecionados"
								[listaCompletaGrupoMuscular]="allGruposMusculares"
								[podeSelecionarGrupoMuscular]="
									false
								"></pacto-corpo-posterior-grupo-muscular>
						</div>
					</div>

					<div class="col-md-6">
						<div class="table-grupos">
							<div
								[maxHeight]="'290px'"
								class="tabela-scroll"
								pactoCatSmoothScroll>
								<table class="table">
									<div *ngIf="tableGrupos.length === 0" class="empty-state">
										Sem dados disponíveis.
									</div>
									<div *ngFor="let valor of tableGrupos" class="tabela-grupos">
										<div class="colum colum-1">{{ valor.nome }}</div>
										<div class="colum colum-2">
											{{ valor.exercicios }} Exercicíos
										</div>
										<div class="colum colum-3">{{ valor.porcentagem }}%</div>
									</div>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="box-right-2 perimetria">
			<div class="resumo-perimetria">
				<div class="top-row">
					<div class="title">Perimetria</div>
					<div class="desc-label">
						{{ traducoes.getLabel(perimetriaFc.value) }}
					</div>
				</div>
				<div class="select-perimetria">
					<pacto-cat-select
						*ngIf="perimetriaItens.length"
						[control]="perimetriaFc"
						[idKey]="'id'"
						[items]="perimetriaItens"
						[labelKey]="'label'"
						[size]="'SMALL'"></pacto-cat-select>
				</div>
			</div>
			<div class="center-row-perimetria">
				<pacto-cat-column-chart
					*ngIf="series"
					[colors]="chartColorsPerimetria"
					[height]="280"
					[series]="series"
					[tooltipFormatter]="tooltipFormatter"
					[xAxisLabels]="labelsPerimeria"
					[yAxisTitle]="'( Valores em centímetros )'"></pacto-cat-column-chart>
			</div>
		</div>

		<div class="box-right-2 peso-x-massa">
			<div class="resumo-perimetria">
				<div class="top-row">
					<div class="title">Peso x Percentual De Gordura</div>
				</div>
			</div>
			<div class="center-row-perimetria">
				<div class="chart-peso">
					<pacto-cat-column-chart
						*ngIf="seriesPeso"
						[colors]="chartColorsPeso"
						[height]="280"
						[series]="seriesPeso"
						[tooltipFormatter]="tooltipFormatterKg"
						[xAxisLabels]="labelsPeso"
						[yAxisTitle]="'( Valores em kg )'"></pacto-cat-column-chart>
				</div>
			</div>
		</div>

		<div class="box-right-3 dobras">
			<div class="resumo-dobras">
				<div class="top-row-dobras">
					<div class="title">Dobras cutâneas</div>
					<div class="select-dobras">
						<pacto-cat-select
							*ngIf="dobrasItens.length"
							[control]="dobrasFc"
							[items]="dobrasItens"
							[size]="'SMALL'"></pacto-cat-select>
					</div>
				</div>
				<div class="center-row-dobras">
					<pacto-cat-line-chart
						*ngIf="seriesDobras"
						[series]="seriesDobras"
						[tooltipFormatter]="tooltipFormatterMm"
						[xAxisLabels]="labelsDobras"
						[yAxisTitle]="'(Medidas em mm)'"></pacto-cat-line-chart>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span xingling="pescoco">Pescoço</span>
	<span xingling="ombro">Ombro</span>
	<span xingling="braco_esquerdo">Braço esquerdo</span>
	<span xingling="braco_direito">Braço direito</span>
	<span xingling="antebraco_direito">Antebraço Direito</span>
	<span xingling="antebraco_esquerdo">Antebraço esq.</span>
	<span xingling="toraxBusto">Torax busto</span>
	<span xingling="cintura">Cintura</span>
	<span xingling="circunferenciaAbdominal">Abdominal</span>
	<span xingling="quadril">Quadril</span>
	<span xingling="gluteo">Gluteo</span>
	<span xingling="coxa_medial">Coxa Média</span>
	<span xingling="coxa_proximal_direita">Coxa P/ direita</span>
	<span xingling="coxa_medial_direita">Coxa M/ direita</span>
	<span xingling="coxa_distal_direita">Coxa D/ direita</span>
	<span xingling="coxa_proximal_esquerda">Coxa P/ esquerda</span>
	<span xingling="coxa_medial_esquerda">Coxa M/ esquerda</span>
	<span xingling="coxa_distal_esquerda">Coxa D/ esquerda</span>
	<span xingling="panturrilha_direita">Panturrilha direita</span>
	<span xingling="panturrilha_esquerda">Panturrilha esq.</span>
	<span xingling="no_periodo">No período</span>
	<span xingling="no_programa">No programa atual</span>
</pacto-traducoes-xingling>
