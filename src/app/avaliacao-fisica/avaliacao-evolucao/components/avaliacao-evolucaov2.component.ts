import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ColumnChartSet } from "ui-kit";
import {
	EvolucaoFisica,
	TreinoApiEvolucaoFisicaService,
	AlunoBase,
	TreinoApiAlunosService,
	TreinoApiGrupoMuscularService,
	GrupoMuscular,
} from "treino-api";
import { FormControl } from "@angular/forms";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { formatDate } from "@angular/common";
import { locale } from "moment";
import { PactoColor } from "ui-kit";

@Component({
	selector: "pacto-avaliacao-evolucaov2",
	templateUrl: "./avaliacao-evolucaov2.component.html",
	styleUrls: ["./avaliacao-evolucaov2.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoEvolucaov2Component implements OnInit {
	@ViewChild("traducoes", { static: true }) traducoes;

	evolucaoFisica: EvolucaoFisica;
	labelsPerimeria: any = [];
	labelsDobras: any = [];
	labelsPeso: any = [];
	perimetriaItens: any[] = [];
	gruposItens: any[] = [];
	dobrasItens: any[] = [];
	tableGrupos: any = [];

	perimetriaFc = new FormControl("antebraco_direito");
	dobrasFc = new FormControl("Peitoral");
	gruposFc = new FormControl("programaAtual");
	ready = false;

	dadosPerimetria;
	aluno: AlunoBase;

	chartColorsPerimetria: PactoColor[] = [PactoColor.BUBBALOO_PRI];
	chartColorsPeso: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.PEQUIZAO_PRI,
	];

	allGruposMusculares: Array<GrupoMuscular> = [];
	gruposMuscularesSelecionados: any[] = [];

	constructor(
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private router: Router,
		private evolucaoFisicaService: TreinoApiEvolucaoFisicaService,
		private alunoService: TreinoApiAlunosService,
		private grupoMuscularService: TreinoApiGrupoMuscularService
	) {}

	series: ColumnChartSet[];
	seriesPeso: ColumnChartSet[];
	seriesDobras: ColumnChartSet[];

	ngOnInit() {
		this.route.params.subscribe((params: any) => {
			if (params.id) {
				this.obterEvolucaoFsica(params.id).subscribe(() => {
					this.ready = true;
					this.cd.detectChanges();
					this.loadInitialData();
					this.setupSelect();
					this.buildFromControl();
					this.carregarTodosGruposMusculares();
				});
			}
		});
	}

	buildFromControl() {
		this.perimetriaFc.valueChanges.subscribe(() => {
			this.setupChartPerimetria();
		});
		this.gruposFc.valueChanges.subscribe(() => {
			this.buildDataGrupos();
		});
		this.dobrasFc.valueChanges.subscribe(() => {
			this.setupChartDobras();
		});
	}

	carregarTodosGruposMusculares() {
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.allGruposMusculares = dados;
				this.cd.detectChanges();
			});
	}

	private loadInitialData() {
		this.buildDataGrupos();
		this.setupChartDobras();
		const matriculaAluno = this.route.snapshot.params.id;
		const aluno$ =
			this.alunoService.obterAlunoCompletoPorMatricula(matriculaAluno);
		aluno$.subscribe((resultado) => {
			this.aluno = resultado;
			this.cd.detectChanges();
		});
	}

	private setupChartPerimetria() {
		this.series = [];
		this.labelsPerimeria = [];
		const dia = [];
		this.dadosPerimetria =
			this.evolucaoFisica.perimetria[this.perimetriaFc.value];
		this.dadosPerimetria.valores.forEach((result) => {
			dia.push(result.valor);
			const data = new Date(result.data);
			this.labelsPerimeria.push(formatDate(data, "MM/yyyy", locale()));
		});
		this.series = [
			{ name: this.traducoes.getLabel(this.perimetriaFc.value), data: dia },
		];
	}

	private setupChartDobras() {
		let dadosDobras;
		this.labelsDobras = [];
		const dobras = [];
		if (this.dobrasFc) {
			dadosDobras = this.evolucaoFisica.dobras.find((i) => {
				return i.nome === this.dobrasFc.value;
			});
		}
		dadosDobras.pontos.forEach((result) => {
			dobras.push(result.valor);
			const data = new Date(result.data);
			this.labelsDobras.push(formatDate(data, "MM/yyyy", locale()));
		});
		this.seriesDobras = [{ name: dadosDobras.nome, data: dobras }];
	}

	buildDataGrupos() {
		if (this.evolucaoFisica) {
			this.tableGrupos =
				this.evolucaoFisica.gruposTrabalhados[this.gruposFc.value];
			this.buildGruposMuscularesAtivos();
		}
	}

	buildGruposMuscularesAtivos() {
		this.gruposMuscularesSelecionados = [];
		this.tableGrupos.forEach((item) => {
			this.gruposMuscularesSelecionados.push({
				id: 0,
				nome: item.nome,
			});
		});
		this.cd.detectChanges();
	}

	private setupChartPeso() {
		this.labelsPeso = [];
		const peso = [];
		const gordura = [];
		this.evolucaoFisica.proporcaoPesoGordura.forEach((result) => {
			peso.push(result.peso);
			gordura.push(result.massaGorda);
			const data = new Date(result.data);
			this.labelsPeso.push(formatDate(data, "MM/yyyy", locale()));
		});
		this.seriesPeso = [
			{ name: "Peso", data: peso },
			{ name: "Gordura", data: gordura },
		];
	}

	setupSelect() {
		// tslint:disable-next-line:forin
		for (const perimetriaKey in this.evolucaoFisica.perimetria) {
			this.perimetriaItens.push({
				id: perimetriaKey,
				label: this.traducoes.getLabel(perimetriaKey),
			});
		}
		this.evolucaoFisica.dobras.forEach((dobra) => {
			this.dobrasItens.push({
				id:
					dobra.nome.toLowerCase() === "coxa medial".toLowerCase()
						? "Coxa Média"
						: dobra.nome,
				label:
					dobra.nome.toLowerCase() === "coxa medial".toLowerCase()
						? "Coxa Média"
						: dobra.nome,
			});
		});
		this.cd.detectChanges();
		this.gruposItens.push(
			{
				id: "durantePeriodo",
				label: this.traducoes.getLabel("no_periodo"),
			},
			{
				id: "programaAtual",
				label: this.traducoes.getLabel("no_programa"),
			}
		);
	}

	tooltipFormatter() {
		return (val) => {
			return `${val} cm`;
		};
	}

	tooltipFormatterKg() {
		return (val) => {
			return `${val} kg`;
		};
	}

	tooltipFormatterMm() {
		return (val) => {
			return `${val} mm`;
		};
	}

	private obterEvolucaoFsica(id?: number): Observable<any> {
		return this.evolucaoFisicaService.obterEvolucaoFisica(id).pipe(
			map((result) => {
				if (result === "error") {
					this.router.navigate(["catalogo-alunos", `${id}`]);
				}
				this.evolucaoFisica = result;
				this.setupChartPerimetria();
				this.setupChartPeso();
				return true;
			})
		);
	}
}
