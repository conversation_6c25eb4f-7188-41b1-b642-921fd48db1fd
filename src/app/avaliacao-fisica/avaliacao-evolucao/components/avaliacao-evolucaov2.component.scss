@import "~src/assets/scss/pacto/plataforma-import.scss";

.top-aux {
	width: 100%;
	background-color: $branco;
	justify-content: center;
	display: flex;
}

pacto-avaliacoes-aluno-top {
	@include plataformaV2LarguraConteudo();
}

.content-aux {
	background-color: $cinzaClaroPri;
	color: $pretoPri;
	display: flex;
	align-items: center;
	flex-direction: column;

	> * {
		@include plataformaV2LarguraConteudo();
	}
}

.avaliacao-nome {
	margin: 30px 0px;
	text-align: left;
}

.tabela-scroll {
	height: calc(100vh - 315px);
	position: relative;
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

/** Configuracoes das divs(conteudo) **/

.titulo-box {
	height: 20px;
	@extend .type-h5;
	line-height: 1;
	color: $pretoPri;
	text-align: center;
	padding-top: 71px;
}

.pct-value {
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	text-align: center;
	line-height: 1em;
	@extend .type-hero-bold;
	height: 56px;
	padding-top: 69px;
}

.descricao-label {
	text-align: center;
	margin-top: 44px;
	padding: 0 20%;
	font-family: "Nunito Sans", sans-serif;
	font-size: 14px;
	line-height: 1;
	color: $gelo04;
}

.resumo-grupos {
	display: flex;
	justify-content: space-between;
}

.title {
	@extend .type-h5;
	color: $pretoPri;
}

.desc-label {
	@extend .type-p-small-rounded;
	color: $cinza05;
	text-align: left;
	margin-top: 3px;
}

.select-grupos {
	width: 168px;
	height: 32px;
	margin-top: 40px;
	margin-right: 40px;
}

.select-dobras {
	width: 168px;
	height: 32px;
	margin-right: 50px;
}

.select-perimetria {
	margin-top: 35px;
	width: 150px;
	height: 32px;
	margin-right: 30px;
}

.top-row {
	margin-left: 30px;
	margin-top: 27px;
}

.top-row-dobras {
	display: flex;
	@extend .top-row;
	justify-content: space-between;
}

.center-row-perimetria {
	margin-top: 20px;
	margin-right: 30px;
}

.tabela-grupos {
	display: flex;
	justify-content: space-between;
	height: 56px;

	.colum {
		margin: 12px 14px 0px 8px;
		@extend .type-h6;
	}

	.colum-2 {
		color: $gelo04;
		width: 115px;
	}
}

.colum-1 {
	width: 163px;
}

.tabela-grupos:nth-child(odd) {
	background-color: #fafafa;
}

.center-row-dobras {
	margin-top: 20px;
	margin-right: 30px;
}

.resumo-perimetria {
	display: flex;
	justify-content: space-between;
}

//Css Bootstrap

.bb {
	display: grid;
	grid-template-columns: 1fr 1.5fr 1.5fr;
	grid-template-rows: auto;
	grid-gap: 30px;
	@media (max-width: 1200px) {
		grid-template-columns: 0.5fr 0.5fr 0.5fr 0.5fr 0.5fr 0.5fr;
		grid-template-areas:
			"avaliacoes avaliacoes gordura gordura  massa-magra  massa-magra"
			"exercicios exercicios exercicios exercicios exercicios exercicios"
			"perimetria perimetria perimetria peso-x-massa peso-x-massa peso-x-massa"
			"dobras dobras dobras dobras dobras dobras";
	}
	@media (max-width: 970px) {
		grid-template-columns: 2fr;
		grid-template-areas:
			"avaliacoes" "gordura" "massa-magra"
			"exercicios" "perimetria" "peso-x-massa" "dobras";
	}
	@media (max-width: 750px) {
		grid-template-columns: 0.5fr;
		grid-template-areas:
			"avaliacoes" "gordura" "massa-magra"
			"exercicios" "perimetria" "peso-x-massa" "dobras";
	}
	grid-template-areas:
		"avaliacoes exercicios exercicios"
		"avaliacoes exercicios exercicios"
		"gordura exercicios exercicios"
		"gordura perimetria peso-x-massa"
		"massa-magra perimetria peso-x-massa"
		"massa-magra perimetria peso-x-massa"
		"dobras dobras dobras"
		"dobras dobras dobras"
		"dobras dobras dobras";
}

.dobras {
	grid-area: dobras;
}

.avaliacoes {
	grid-area: avaliacoes;
}

.gordura {
	grid-area: gordura;
}

.massa-magra {
	grid-area: massa-magra;
}

.exercicios {
	grid-area: exercicios;
}

.perimetria {
	grid-area: perimetria;
}

.peso-x-massa {
	grid-area: peso-x-massa;
}

.box-left {
	height: 283px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-1 {
	height: 442px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-2 {
	height: 437px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-3 {
	height: 437px;
	border-radius: 10px;
	margin-bottom: 50px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.caixa {
	border: 1px dashed red;
	min-height: 60px;
}

.chart-peso {
	margin-top: 50px;
}

.table-grupos {
	position: center;
	margin: 23px 26px 0px 0px;
	height: 315px;
	overflow: auto;
}

.display-flex {
	display: flex;
	margin-top: 16px;
}

.componente-corpo-grupo-muscular {
	display: flex;
	justify-content: space-around;

	::ng-deep .grupo-muscular-frontal {
		max-height: 306px;
		max-width: 186px;
	}

	::ng-deep .grupo-muscular-posterior {
		max-height: 306px;
		max-width: 186px;
	}
}
