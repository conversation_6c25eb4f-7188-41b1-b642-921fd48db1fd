import { mergeMap, finalize } from "rxjs/operators";
import { Observable, timer } from "rxjs";

export const genericRetryStrategy =
	({
		maxRetryAttempts = 7,
		scalingDuration = 1000,
		excludedStatusCodes = [],
	}: {
		maxRetryAttempts?: number;
		scalingDuration?: number;
		excludedStatusCodes?: number[];
	} = {}) =>
	(attempts: Observable<any>) => {
		return attempts.pipe(
			mergeMap((error, i) => {
				const retryAttempt = i + 1;
				// Se o numero de tentativas chegou ao máximo
				// ou resposta é em um status de código que não queremos tentar novamente, laça erro.
				if (
					retryAttempt > maxRetryAttempts ||
					excludedStatusCodes.find((e) => e === error.status)
				) {
					throw new Error(error);
				}
				console.log(
					`Tentativa ${retryAttempt}: tentando novamente em: ${
						retryAttempt * scalingDuration
					}ms`
				);
				return timer(retryAttempt * scalingDuration);
			}),
			finalize(() => console.log("Tentativas finalizadas!"))
		);
	};
