import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { delay } from "rxjs/operators";

import { RestService } from "@base-core/rest/rest.service";
import {
	GraduacaoAvaliacaoLivreSimple,
	GraduacaoAvaliacaoLivre,
	AvaliacaoLivreStatus,
} from "./avaliacao-livre.model";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { AvaliacaoGraduacaoCriterio } from "../avaliacao-progresso/avaliacao.model";

export interface FiltroAvaliacaoLivre {
	alunoId?: number;
	fichaId?: number;
}

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class AvaliacaoLivreService {
	constructor(private http: HttpClient, private rest: RestService) {}

	listarAvaliacaoLivre(
		filtro?: FiltroAvaliacaoLivre
	): Observable<GraduacaoAvaliacaoLivreSimple[]> {
		return of([avaliacaoSimple]);
	}

	cadastrarAvaliacaoLivre(dto: any): Observable<GraduacaoAvaliacaoLivre> {
		return of(avaliacao);
	}

	buscarAvaliacaoLivre(id: number): Observable<GraduacaoAvaliacaoLivre> {
		return of(avaliacao);
	}

	obterAlunosAvaliacaoLivre(id: number): Observable<number[]> {
		return of([1, 2, 3, 4]);
	}

	editarAvaliacaoLivre(
		id: number,
		dto: any
	): Observable<GraduacaoAvaliacaoLivre> {
		return of(null);
	}

	inserirAlunos(id: number, alunoIds: number[]): Observable<boolean> {
		return of(true).pipe(delay(350));
	}

	removerAlunos(id: number, alunos: number[]): Observable<boolean> {
		return of(true).pipe(delay(200));
	}

	concluir(id: number, dto): Observable<GraduacaoAvaliacaoLivre> {
		return of(avaliacao).pipe(delay(1000));
	}
}

const avaliacaoSimple: GraduacaoAvaliacaoLivreSimple = {
	id: 3,
	ficha: {
		id: 4,
		nome: "Ficha AAAAAAAAAAAA",
		nOfNiveis: 3,
	},
	colaborador: undefined,
	avaliadorId: 49,
	data: 1634698800000,
	nOfAtividades: 4,
	criterio: AvaliacaoGraduacaoCriterio.CONCEITOS,
	status: AvaliacaoLivreStatus.ABERTA,
};

const avaliacao: GraduacaoAvaliacaoLivre = {
	id: 3,
	ficha: {
		id: 4,
		nome: "Ficha AAAAAAAAAAAA",
		nOfNiveis: 3,
	},
	avaliadorId: 49,
	data: 1634698800000,
	atividades: [
		{ id: 45, imageUri: null, nome: "Xablau carpado" },
		{ id: 4, imageUri: null, nome: "Xablau carpado" },
		{ id: 56, imageUri: null, nome: "Xablau carpado" },
		{ id: 98, imageUri: null, nome: "Xablau carpado" },
		{ id: 53, imageUri: null, nome: "Xablau carpado" },
	],
	criterio: AvaliacaoGraduacaoCriterio.CONCEITOS,
	status: AvaliacaoLivreStatus.CONCLUIDA,
};
