import { FichaSimple } from "src/app/microservices/graduacao/ficha/ficha.model";
import { AtividadeGraduacao } from "src/app/microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { Colaborador } from "../../personagem/colaborador/colaborador.model";
import { AvaliacaoGraduacaoCriterio } from "../avaliacao-progresso/avaliacao.model";

export interface GraduacaoAvaliacaoLivre {
	id: number;
	ficha: FichaSimple;
	avaliadorId: number;
	data: number;
	atividades: AtividadeGraduacao[];
	criterio: AvaliacaoGraduacaoCriterio;
	status: AvaliacaoLivreStatus;
}

export interface GraduacaoAvaliacaoLivreSimple {
	id: number;
	ficha: FichaSimple;
	avaliadorId: number;
	colaborador: Colaborador;
	data: number;
	nOfAtividades: number;
	criterio?: AvaliacaoGraduacaoCriterio;
	status: AvaliacaoLivreStatus;
}

export enum AvaliacaoLivreStatus {
	ABERTA = "ABERTA",
	CONCLUIDA = "CONCLUIDA",
}
