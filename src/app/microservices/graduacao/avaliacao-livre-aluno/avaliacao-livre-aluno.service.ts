import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { delay } from "rxjs/operators";

import { RestService } from "@base-core/rest/rest.service";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { AvaliacaoLivreAluno } from "./avaliacao-livre-aluno.model";
import { AvaliacaoAlunoResposta } from "src/app/microservices/graduacao/graduacao.model";

export interface AvaliacaoLivreAlunoFilter {
	avaliacaoLivreId?: number;
}

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class AvaliacaoLivreAlunoService {
	constructor(private http: HttpClient, private rest: RestService) {}

	listar(filter: AvaliacaoLivreAlunoFilter): Observable<AvaliacaoLivreAluno[]> {
		return of([fake]);
	}

	cadastrar(dto): Observable<AvaliacaoLivreAluno> {
		return of(fake).pipe(delay(500));
	}

	obterPorId(id: number): Observable<AvaliacaoLivreAluno> {
		return of(fake);
	}

	obterRespostasAvaliacaoAluno(
		id: number
	): Observable<AvaliacaoAlunoResposta[]> {
		return of([
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
			resposta,
		]);
	}

	editar(id, dto): Observable<AvaliacaoLivreAluno> {
		return of(fake).pipe(delay(300));
	}
}

const fake = {
	id: 533,
	timestamp: 1634698800000,
	alunoId: 4,
	avaliacaoLivreId: 44,
	observacaoGeral: "xablau",
};

const resposta = {
	id: 76,
	atividade: {
		id: 54,
		imageUri: "",
		nome: "Voadeira",
		descricao: "faf ",
	},
	resposta: 3,
	observacao: "",
};
