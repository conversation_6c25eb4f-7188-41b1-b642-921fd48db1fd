import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { delay, map } from "rxjs/operators";

import { RestService } from "@base-core/rest/rest.service";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { AvaliacaoProgressoAluno } from "./avaliacao-progresso-aluno.model";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";
import {
	AvaliacaoAlunoResposta,
	ConsultaRespostasAvaliacaoGrupo,
} from "src/app/microservices/graduacao/graduacao.model";
import { AvaliacaoGraduacaoCriterio } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { AtividadeGraduacao } from "../atividade-graduacao/atividade-graduacao.model";

export interface RespostaAvaliacaoProgressoDto {
	respostas: AvaliacaoAlunoResposta[];
	criterio: AvaliacaoGraduacaoCriterio;
	atividades: AtividadeGraduacao[];
}

export interface AvaliacaoProgressoAlunoFilter {
	avaliacaoProgressoId?: number;
}

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class AvaliacaoProgressoAlunoService {
	constructor(private http: HttpClient, private rest: RestService) {}

	obterPorId(id: number): Observable<AvaliacaoProgressoAluno> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso-aluno/avaliacaoProgressoAlunoRespondidaId/${id}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<AvaliacaoProgressoAluno>) => {
				return response.content;
			})
		);
	}

	cadastrar(dto): Observable<AvaliacaoProgressoAluno> {
		const url = this.rest.buildFullUrlGraduacao("avaliacoes-progresso-aluno");
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<AvaliacaoProgressoAluno>) => {
				return response.content;
			})
		);
	}

	cadastrarAvaliacaoProgressoEmGrupo(dto): Observable<AvaliacaoProgressoAluno> {
		const url = this.rest.buildFullUrlGraduacao(
			"avaliacoes-progresso-aluno/cadastrar-avaliacao-progresso-grupo"
		);
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<AvaliacaoProgressoAluno>) => {
				return response.content;
			})
		);
	}

	editar(id, dto): Observable<AvaliacaoProgressoAluno> {
		dto.avaliacaoProgressoId = id;
		const url = this.rest.buildFullUrlGraduacao(
			"avaliacoes-progresso-aluno/alterar"
		);
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<AvaliacaoProgressoAluno>) => {
				return response.content;
			})
		);
	}

	editarAvaliacaoProgressoEmGrupo(
		id,
		dto
	): Observable<AvaliacaoProgressoAluno> {
		dto.avaliacaoProgressoId = id;
		const url = this.rest.buildFullUrlGraduacao(
			"avaliacoes-progresso-aluno/alterar-avaliacao-progresso-grupo"
		);
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<AvaliacaoProgressoAluno>) => {
				return response.content;
			})
		);
	}

	listar(
		filter: AvaliacaoProgressoAlunoFilter
	): Observable<AvaliacaoProgressoAluno[]> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso-aluno?avaliacaoProgressoId=${filter.avaliacaoProgressoId}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<AvaliacaoProgressoAluno>) => {
				return response.content;
			})
		);
	}

	obterRespostasAvaliacaoAluno(
		avaliacaoAlunoId: number
	): Observable<RespostaAvaliacaoProgressoDto> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso-aluno/${avaliacaoAlunoId}/respostas`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<RespostaAvaliacaoProgressoDto>) => {
				return response.content;
			})
		);
	}

	obterRespostasAvaliacaoEmGrupo(
		avaliacaoId: number
	): Observable<ConsultaRespostasAvaliacaoGrupo> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso-aluno/${avaliacaoId}/respostas-avaliacao-grupo`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<ConsultaRespostasAvaliacaoGrupo>) => {
				return response.content;
			})
		);
	}

	obterInformacoesAluno(
		matricula: number,
		alunoId: number,
		fichaId: number
	): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(
			`aluno/${matricula}/${alunoId}/${fichaId}/obter-informacoes-aluno`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	gerarPdfAvaliacao(
		chave: string,
		matricula: number,
		alunoId: number,
		fichaId: number,
		avaliacaoAlunoId: number
	): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(
			`aluno/gerar-pdf-avaliacao-progresso/${chave}/${matricula}/${alunoId}/${fichaId}/${avaliacaoAlunoId}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}
}

const fake = {
	id: 533,
	timestamp: 1634698800000,
	alunoId: 4,
	avaliacaoProgressoId: 44,
	observacaoGeral: "xablau",
};
