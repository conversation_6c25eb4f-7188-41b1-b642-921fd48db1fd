import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import {
	ApiRequestQueries,
	ApiResponseList,
	ApiResponseSingle,
} from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { Modalidade } from "treino-api";
import { NivelSimple } from "..//nivel/nivel.model";
import { Ficha, FichaGraduacaoAluno } from "./ficha.model";

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class FichaService {
	constructor(private http: HttpClient, private rest: RestService) {}

	obterFichas(
		filter?: string,
		colaboradorId?: number,
		pageSort?: ApiRequestQueries
	): Observable<Array<Ficha>> {
		const url = this.rest.buildFullUrlGraduacao("fichas");
		return this.http.get(url).pipe(
			map((response: ApiResponseList<Ficha>) => {
				return response.content;
			})
		);
	}

	criarFicha(dto: any): Observable<Ficha> {
		const url = this.rest.buildFullUrlGraduacao("fichas");
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<Ficha>) => {
				return response.content;
			})
		);
	}

	editarFicha(dto: any, id: number): Observable<Ficha> {
		const url = this.rest.buildFullUrlGraduacao(`fichas/${id}`);
		return this.http.put(url, dto).pipe(
			map((response: ApiResponseSingle<Ficha>) => {
				return response.content;
			})
		);
	}

	removerFichaTecnica(fichaId: number): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(`fichas/${fichaId}`);
		return this.http.delete(url).pipe(
			map((response: ApiResponseSingle<any>) => {
				return true;
			}),
			catchError((err, caught) => {
				if (err.error.meta.error === "ficha_possui_avaliacao") {
					return new Observable((o) => {
						o.next("ficha_possui_avaliacao");
						o.complete();
					});
				} else if (
					err.error.meta.error === "ficha_possui_vinculo_colaborador"
				) {
					return new Observable((o) => {
						o.next("ficha_possui_vinculo_colaborador");
						o.complete();
					});
				} else if (err.error.meta.error === "ficha_possui_vinculo_modalidade") {
					return new Observable((o) => {
						o.next("ficha_possui_vinculo_modalidade");
						o.complete();
					});
				} else if (err.error.meta.error === "ficha_possui_vinculo_nivel") {
					return new Observable((o) => {
						o.next("ficha_possui_vinculo_nivel");
						o.complete();
					});
				}
			})
		);
	}

	obterFichaPorId(id): Observable<Ficha> {
		const url = this.rest.buildFullUrlGraduacao(`fichas/${id}`);
		return this.http.get<ApiResponseSingle<Ficha>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	obterFIltrosFicha(id): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(`aluno/filtros/${id}`);
		return this.http.get<ApiResponseSingle<Ficha>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	obterFichasDeAluno(
		alunoid: number,
		key: string
	): Observable<ApiResponseList<FichaGraduacaoAluno>> {
		const url = this.rest.buildFullUrlGraduacao(
			`fichas/${alunoid}/fichasaluno`
		);
		return this.http.get<ApiResponseList<FichaGraduacaoAluno>>(url, {
			params: { key },
		});
	}

	reordenarNiveis(fichaId: number, idsNiveis: number[]): Observable<Ficha> {
		const url = this.rest.buildFullUrlGraduacao(
			`fichas/${fichaId}/reordenar-niveis`
		);
		return this.http.post(url, idsNiveis).pipe(
			map((response: ApiResponseSingle<Ficha>) => {
				return response.content;
			})
		);
	}

	modalidades(): Observable<Array<Modalidade>> {
		const url = this.rest.buildFullUrlGraduacao("modalidades");
		return this.http.get(url).pipe(
			map((response: ApiResponseList<Modalidade>) => {
				return response.content;
			})
		);
	}

	obterColaboradoresDaFicha(
		fichaId: number,
		filter?: any
	): Observable<number[]> {
		const url = this.rest.buildFullUrlGraduacao(
			`fichas/${fichaId}/colaboradores`
		);
		return this.http.get(url, { params: filter }).pipe(
			map((response: ApiResponseList<number>) => {
				return response.content;
			})
		);
	}

	obterAlunosDaFicha(
		fichaId: number,
		filter: { "max-nivel-index"?: any } = {}
	): Observable<Array<number>> {
		const url = this.rest.buildFullUrlGraduacao(`fichas/${fichaId}/alunos`);
		return this.http.get(url, { params: filter }).pipe(
			map((response: ApiResponseList<number>) => {
				return response.content;
			})
		);
	}

	/**
	 * Lista os níveis de todos os alunos associados a uma ficha.
	 */
	obterNiveisDosAlunos(
		fichaId: number
	): Observable<{ [alunoId: number]: NivelSimple }> {
		const url = this.rest.buildFullUrlGraduacao(
			`fichas/${fichaId}/alunos-niveis`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<{ [alunoId: number]: NivelSimple }>) => {
				return response.content;
			})
		);
	}

	inserirAlunosFicha(fichaId: number, dto): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao("inserir-alunos");
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return true;
			})
		);
	}

	inserirAlunosFichaTodos(nivel: number, filtros: any): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao(
			`inserir-alunos/todos/${nivel}`
		);
		const params = this.getParams(filtros);
		return this.http.post(url, { params }).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return response.content;
			})
		);
	}

	private getParams(filtros): any {
		if (filtros.filters) {
			filtros.filters = JSON.stringify(filtros.filters);
		}
		if (filtros.configs) {
			filtros.configs = JSON.stringify(filtros.configs);
		}
		return filtros;
	}
}
