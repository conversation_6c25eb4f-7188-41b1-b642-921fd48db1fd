import { FichaSimple } from "src/app/microservices/graduacao/ficha/ficha.model";
import { NivelSimple } from "src/app/microservices/graduacao/nivel/nivel.model";

export interface GraduacaoAvaliacaoProgresso {
	id: number;
	ficha: FichaSimple;
	nivel: NivelSimple;
	avaliadorId: number;
	data: number;
	criterio: AvaliacaoGraduacaoCriterio;
	status: AvaliacaoProgressoStatus;
}

export enum AvaliacaoGraduacaoCriterio {
	DE_1_A_5 = "DE_1_A_5",
	SIM_NAO = "SIM_NAO",
	CONCEITOS = "CONCEITOS",
}

export enum AvaliacaoProgressoStatus {
	ABERTA = "ABERTA",
	CONCLUIDA = "CONCLUIDA",
}
