import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";

import { RestService } from "@base-core/rest/rest.service";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import {
	AvaliacaoGraduacaoCriterio,
	AvaliacaoProgressoStatus,
	GraduacaoAvaliacaoProgresso,
} from "./avaliacao.model";
import { AvaliacaoProgressoAluno } from "src/app/microservices/graduacao/avaliacao-progresso-aluno/avaliacao-progresso-aluno.model";
import { catchError, map } from "rxjs/operators";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class AvaliacaoProgressoService {
	constructor(private http: HttpClient, private rest: RestService) {}

	listarAvaliacaoProgresso(filtro: {
		alunoId?: any;
		fichaId?: any;
	}): Observable<GraduacaoAvaliacaoProgresso[]> {
		const url = this.rest.buildFullUrlGraduacao("avaliacoes-progresso");
		return this.http.get(url, { params: filtro }).pipe(
			map((response: ApiResponseList<GraduacaoAvaliacaoProgresso>) => {
				return response.content;
			})
		);
	}

	cadastrarAvaliacaoProgresso(
		dto: any
	): Observable<GraduacaoAvaliacaoProgresso> {
		const url = this.rest.buildFullUrlGraduacao("avaliacoes-progresso");
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<GraduacaoAvaliacaoProgresso>) => {
				return response.content;
			})
		);
	}

	buscarAvaliacaoProgresso(
		id: number
	): Observable<GraduacaoAvaliacaoProgresso> {
		const url = this.rest.buildFullUrlGraduacao(`avaliacoes-progresso/${id}`);
		return this.http
			.get<ApiResponseSingle<GraduacaoAvaliacaoProgresso>>(url)
			.pipe(
				map((result) => {
					return result.content;
				})
			);
	}

	listarAvaliacoesProgressoAluno(
		avaliacaoProgressoId: number
	): Observable<AvaliacaoProgressoAluno[]> {
		return of([avaliacaoProgressoAluno]);
	}

	editarAvaliacao(id: number, dto): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(`avaliacoes-progresso/${id}`);
		return this.http.put(url, dto).pipe(
			map((response: ApiResponseSingle<GraduacaoAvaliacaoProgresso>) => {
				return response.content;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.next(error.error.meta);
					observer.complete();
				});
			})
		);
	}

	obterAlunosAvaliacaoProgresso(id: number): Observable<number[]> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso/${id}/alunos`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<number>) => {
				return response.content;
			})
		);
	}

	inserirAlunos(avaliacaoId: number, alunoIds: number[]): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso/${avaliacaoId}/alunos`
		);
		return this.http.post(url, alunoIds).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return response.content;
			})
		);
	}

	inserirAlunosTodos(avaliacaoId: number, filtros: any): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso/${avaliacaoId}/alunos/todos`
		);
		const params = this.getParams(filtros);
		return this.http.post(url, { params }).pipe(
			map((response: ApiResponseSingle<boolean>) => {
				return response.content;
			})
		);
	}

	private getParams(filtros): any {
		if (filtros.filters) {
			filtros.filters = JSON.stringify(filtros.filters);
		}
		if (filtros.configs) {
			filtros.configs = JSON.stringify(filtros.configs);
		}
		return filtros;
	}

	removerAlunos(avaliacaoId: number, alunoIds: number[]): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso/${avaliacaoId}/alunos?ids=${alunoIds}`
		);
		return this.http.delete(url).pipe(
			map(() => {
				return true;
			})
		);
	}

	concluir(
		avaliacaoProgressoId: number,
		dto,
		dataAvaliacao: number
	): Observable<GraduacaoAvaliacaoProgresso> {
		const url = this.rest.buildFullUrlGraduacao(
			`avaliacoes-progresso/${avaliacaoProgressoId}/concluir/${dataAvaliacao}`
		);
		return this.http.put(url, dto).pipe(
			map((response: ApiResponseSingle<GraduacaoAvaliacaoProgresso>) => {
				return response.content;
			})
		);
	}
}

const avaliacaoProgressoAluno: AvaliacaoProgressoAluno = {
	id: 5682,
	timestamp: 1634698800000,
	alunoId: 23,
	avaliacaoProgressoId: 4355,
	observacaoGeral: "Sei demais uai",
};

const avaliacaoProgresso: GraduacaoAvaliacaoProgresso = {
	id: 3,
	ficha: {
		id: 4,
		nome: "Ficha AAAAAAAAAAAA",
		nOfNiveis: 3,
	},
	avaliadorId: 34454,
	nivel: {
		id: 4343,
		ativo: true,
		index: 1,
		fotoUri: "",
		nome: "Nivel 1",
		tecnica: "",
		objeto: "",
		cor: "",
		nOfAtividades: 4,
	},
	data: 1634698800000,
	criterio: AvaliacaoGraduacaoCriterio.CONCEITOS,
	status: AvaliacaoProgressoStatus.CONCLUIDA,
};
