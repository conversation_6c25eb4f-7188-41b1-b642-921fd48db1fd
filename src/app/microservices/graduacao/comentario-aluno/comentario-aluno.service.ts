import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";

import { RestService } from "@base-core/rest/rest.service";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { GraduacaoAlunoComentario } from "./comentario-aluno.model";
import { catchError, map } from "rxjs/operators";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class ComentarioAlunoService {
	constructor(private http: HttpClient, private rest: RestService) {}

	criarComentario(
		fichaId: number,
		alunoId: number,
		dto
	): Observable<GraduacaoAlunoComentario> {
		const url = this.rest.buildFullUrlGraduacao(
			`comentarios/${alunoId}/${fichaId}`
		);
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<GraduacaoAlunoComentario>) => {
				return response.content;
			})
		);
	}

	obterComentariosAluno(
		fichaId: number,
		alunoId: number
	): Observable<GraduacaoAlunoComentario[]> {
		const url = this.rest.buildFullUrlGraduacao(
			`comentarios/${alunoId}/${fichaId}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<GraduacaoAlunoComentario>) => {
				return response.content;
			})
		);
	}

	removerComentario(id: number): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(`comentarios/${id}/remover`);
		return this.http.delete(url).pipe(
			map((response: ApiResponseSingle<any>) => {
				return true;
			}),
			catchError((error) => {
				return of({ error });
			})
		);
	}
}
