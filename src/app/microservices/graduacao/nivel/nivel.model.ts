import { AtividadeGraduacao } from "../atividade-graduacao/atividade-graduacao.model";

export interface Nivel {
	id?: number;
	ativo?: boolean;
	index?: number;
	fotoUri?: string;
	nome?: string;
	tecnica?: string;
	objeto?: string;
	objetivo?: string;
	cor?: string;
	nrAlunos?: string;
	alunoIds?: number[];
	atividades?: AtividadeGraduacao[];
	exigeAulas?: boolean;
	quantidadeMinimaAulas?: number;
	nOfAtividades?: number;
}

export class NivelSimple {
	id?: number;
	ativo?: boolean;
	index?: number;
	fotoUri?: string;
	nome?: string;
	tecnica?: string;
	objeto?: string;
	objetivo?: string;
	cor?: string;
	nrAlunos?: string;
	nOfAtividades?: number;
	quantidadeMinimaAulas?: number;
}
