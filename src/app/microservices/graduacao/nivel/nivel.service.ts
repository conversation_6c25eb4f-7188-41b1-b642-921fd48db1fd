import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { RestService } from "@base-core/rest/rest.service";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { Nivel } from "./nivel.model";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";
import { Cliente } from "../../personagem/cliente/cliente.model";
import { FiltroNivel } from "treino-api";

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class NivelService {
	constructor(private http: HttpClient, private rest: RestService) {}

	criarNivel(fichaId: number, dto: any): Observable<Nivel> {
		const url = this.rest.buildFullUrlGraduacao(`fichas/${fichaId}/niveis`);
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<Nivel>) => {
				return response.content;
			})
		);
	}

	obterNivelPorId(id): Observable<Nivel> {
		const url = this.rest.buildFullUrlGraduacao(`niveis/${id}`);
		return this.http.get<ApiResponseSingle<Nivel>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	obterAlunosNivel(nivel: number): Observable<Array<number>> {
		const url = this.rest.buildFullUrlGraduacao(`niveis/${nivel}/alunos`);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<number>) => {
				return response.content;
			})
		);
	}

	obterAlunosFullNivelGraduacao(nivel: number): Observable<Array<Cliente>> {
		const url = this.rest.buildFullUrlGraduacao(
			"niveis/" + nivel + "/alunos-full"
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<Cliente>) => {
				return response.content;
			})
		);
	}

	obtertotalAulaNivel(
		idAluno: number,
		idMtr: number,
		nivel: number
	): Observable<Array<number>> {
		const url = this.rest.buildFullUrlGraduacao(
			`niveis/${idAluno}/aulas/${idMtr}/${nivel}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<number>) => {
				return response.content;
			})
		);
	}

	deletarNivel(id: number): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao(`niveis/${id}`);
		return this.http.delete(url).pipe(
			map(() => {
				return true;
			})
		);
	}

	updateNivel(id: number, dto: any): Observable<Nivel> {
		const url = this.rest.buildFullUrlGraduacao(`niveis/${id}`);
		return this.http.put(url, dto).pipe(
			map((response: ApiResponseSingle<Nivel>) => {
				return response.content;
			}),
			catchError((error, caught) => {
				if (
					error.error.meta.message ===
					"atividade_com_vinculo_resposta_avaliacao"
				) {
					return new Observable((o) => {
						o.next("atividade_com_vinculo_resposta_avaliacao");
						o.complete();
					});
				}
			})
		);
	}

	updateStatus(id: number, status: boolean): Observable<boolean> {
		const url = this.rest.buildFullUrlGraduacao(`niveis/${id}`);
		return this.http.patch(url, status).pipe(
			map(() => {
				return true;
			})
		);
	}

	removerNivelAluno(id: number, idAluno: number): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(`niveis/${id}/${idAluno}`);
		return this.http.delete(url).pipe(
			map((responses: ApiResponseSingle<any>) => {
				return true;
			}),
			catchError((error, caught) => {
				if (error.error.meta.message === "Aluno_possui_avaliacao") {
					return new Observable((o) => {
						o.next("Aluno_possui_avaliacao");
						o.complete();
					});
				} else if (error.error.meta.message === "Nenhum_aluno_excluido") {
					return new Observable((o) => {
						o.next("Nenhum_aluno_excluido");
						o.complete();
					});
				}
			})
		);
	}

	removerAlunos(
		nivelId: number,
		alunoIds: number[],
		todos: boolean
	): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(
			`niveis/${nivelId}/alunos?ids=${alunoIds}` + (todos ? `&todos=true` : ``)
		);
		return this.http.delete(url).pipe(
			map((responses: ApiResponseSingle<any>) => {
				return true;
			}),
			catchError((error, caught) => {
				if (error.error.meta.message === "Aluno_possui_avaliacao") {
					return new Observable((o) => {
						o.next("Aluno_possui_avaliacao");
						o.complete();
					});
				}
			})
		);
	}

	obterNiveis(filtros?: FiltroNivel): Observable<ApiResponseList<Nivel>> {
		const url = this.rest.buildFullUrl("niveis");
		const params = {};
		for (const key in filtros) {
			if (filtros.hasOwnProperty(key) && filtros[key]) {
				params[key] = filtros[key];
			}
		}
		return this.http
			.get(url, {
				params,
			})
			.pipe(
				map((response: ApiResponseList<Nivel>) => {
					return response;
				})
			);
	}

	alterarNivelAluno(nivelId: number, clienteDTO: any): Observable<any> {
		const url = this.rest.buildFullUrlGraduacao(
			`nivel-aluno/${nivelId}/alterar-nivel-aluno`
		);
		return this.http.post(url, clienteDTO).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}
}
