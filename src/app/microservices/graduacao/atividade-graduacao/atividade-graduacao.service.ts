import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { catchError, map } from "rxjs/operators";

import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import {
	AtividadeGraduacao,
	SubAtividades,
} from "src/app/microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";
import { Nivel } from "../nivel/nivel.model";

@Injectable({
	providedIn: GraduacaoMsModule,
})
export class AtividadeGraduacaoService {
	constructor(private restService: RestService, private http: HttpClient) {}

	listarAtividades(): Observable<Array<AtividadeGraduacao>> {
		const url = this.restService.buildFullUrlGraduacao("atividades");
		return this.http.get(url).pipe(
			map((response: ApiResponseList<AtividadeGraduacao>) => {
				return response.content;
			})
		);
	}

	atividadesAtivas(): Observable<Array<AtividadeGraduacao>> {
		const url = this.restService.buildFullUrlGraduacao("atividades/ativas");
		return this.http.get(url).pipe(
			map((response: ApiResponseList<AtividadeGraduacao>) => {
				return response.content;
			})
		);
	}

	criarAtividade(dto: any): Observable<AtividadeGraduacao> {
		const url = this.restService.buildFullUrlGraduacao("atividades");
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<AtividadeGraduacao>) => {
				return response.content;
			})
		);
	}

	buscarAtividadePorId(id: number): Observable<AtividadeGraduacao> {
		const url = this.restService.buildFullUrlGraduacao(`atividades/${id}`);
		return this.http.get<ApiResponseSingle<Nivel>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	editarAtividade(id: number, dto: any): Observable<AtividadeGraduacao> {
		const url = this.restService.buildFullUrlGraduacao(`atividades/${id}`);
		return this.http.put(url, dto).pipe(
			map((response: ApiResponseSingle<AtividadeGraduacao>) => {
				return response.content;
			})
		);
	}

	obterSubAtividades(atividadeId: number): Observable<SubAtividades> {
		const url = this.restService.buildFullUrlGraduacao(
			`sub-atividade/obter-por-atividade/${atividadeId}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<SubAtividades>) => {
				return response.content;
			})
		);
	}

	criarSubAtividade(dto: any): Observable<AtividadeGraduacao> {
		const url = this.restService.buildFullUrlGraduacao(
			"sub-atividade/cadastrar"
		);
		return this.http.post(url, dto).pipe(
			map((response: ApiResponseSingle<AtividadeGraduacao>) => {
				return response.content;
			})
		);
	}

	editarSubAtividade(dto: any): Observable<AtividadeGraduacao> {
		const url = this.restService.buildFullUrlGraduacao(
			`sub-atividade/alterar/${dto.id}`
		);
		return this.http.put(url, dto).pipe(
			map((response: ApiResponseSingle<AtividadeGraduacao>) => {
				return response.content;
			})
		);
	}

	removerSubAtividade(id: number): Observable<any> {
		const url = this.restService.buildFullUrlGraduacao(
			`sub-atividade/remover/${id}`
		);
		return this.http.delete(url).pipe(
			map((response: ApiResponseSingle<any>) => {
				return true;
			}),
			catchError((error) => {
				return of({ error });
			})
		);
	}

	reordenarSubAtividades(
		subAtividadeId: number,
		idsSubAtividades: number[]
	): Observable<SubAtividades> {
		const url = this.restService.buildFullUrlGraduacao(
			`sub-atividade/obter-por-atividade/${subAtividadeId}/reordenar-subatividade`
		);
		return this.http.post(url, idsSubAtividades).pipe(
			map((response: ApiResponseSingle<SubAtividades>) => {
				return response.content;
			})
		);
	}
}
