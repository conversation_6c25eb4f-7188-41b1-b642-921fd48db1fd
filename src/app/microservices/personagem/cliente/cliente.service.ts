import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";

import { PersonagemMsModule } from "../personagem-ms.module";
import { Cliente } from "./cliente.model";
import { map } from "rxjs/operators";
import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";

@Injectable({
	providedIn: PersonagemMsModule,
})
export class ClienteService {
	constructor(private http: HttpClient, private rest: RestService) {}

	obterClientes(filter?: {
		alunoids?: any;
		alunoidsexcluir?: any;
		alunoNome?: any;
	}): Observable<Array<Cliente>> {
		const url = this.rest.buildFullUrlPersonagem("clientes");
		if (filter && filter.alunoids) {
			return this.http.get(url, { params: filter }).pipe(
				map((response: ApiResponseList<Cliente>) => {
					return response.content;
				})
			);
		} else if (filter && filter.alunoNome) {
			return this.http.get(url, { params: filter }).pipe(
				map((response: ApiResponseList<Cliente>) => {
					return response.content;
				})
			);
		} else {
			return this.http.get(url).pipe(
				map((response: ApiResponseList<Cliente>) => {
					return response.content;
				})
			);
		}
	}

	obterCliente(id: number): Observable<Cliente> {
		const url = this.rest.buildFullUrlPersonagem(`clientes/cliente/${id}`);
		return this.http.get<ApiResponseSingle<Cliente>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	obterParcelasAbertoCliente(chave: string, pessoa: number): Observable<any> {
		const url = this.rest.buildFullUrlApiPacto(
			`cliente/${chave}/obterParcelasEmAberto?pessoa=${pessoa}`
		);
		return this.http.get<ApiResponseSingle<any>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}
}

const aluno = {
	id: 4353,
	nome: "Pezzoa",
	imageUri: null,
	matricula: 2259203424,
};
