import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { map, catchError, delay } from "rxjs/operators";

import { ApiResponseList, ApiResponseSingle } from "@base-core/rest/rest.model";
import { ClientDiscoveryService } from "../../client-discovery/client-discovery.service";
import { Colaborador } from "./colaborador.model";

import { PersonagemMsModule } from "../personagem-ms.module";
import { Ficha } from "../../graduacao/ficha/ficha.model";
import { RestService } from "@base-core/rest/rest.service";

@Injectable({
	providedIn: PersonagemMsModule,
})
export class ColaboradorService {
	constructor(private http: HttpClient, private rest: RestService) {}

	obterColaboradores(filter: { ids?: any }): Observable<Array<Colaborador>> {
		const url = this.rest.buildFullUrlPersonagem("colaboradores");
		return this.http.get(url, { params: filter }).pipe(
			map((response: ApiResponseList<Colaborador>) => {
				return response.content;
			})
		);
	}

	colaboradores(): Observable<Array<Colaborador>> {
		const url = this.rest.buildFullUrlPersonagem("colaboradores");
		return this.http.get(url).pipe(
			map((response: ApiResponseList<Colaborador>) => {
				return response.content;
			})
		);
	}

	obterColaborador(id?: number): Observable<Colaborador> {
		const url = this.rest.buildFullUrlPersonagem(`colaboradores/${id}`);
		return this.http.get<ApiResponseSingle<Colaborador>>(url).pipe(
			map((result) => {
				return result.content;
			})
		);
	}

	adiconarAlunosEmAvaliacaoProgresso(ids): Observable<boolean> {
		return of(true).pipe(delay(350));
	}
}
