import { EmpresaFinanceiro } from "canal-cliente/app/shared/empresa-financeiro.model";

export enum PlataformaModulo {
	/**
	 * NTR - T<PERSON><PERSON> (versão nova plataforma)
	 * NTW - ZillyonWeb (versão nova plataforma)
	 * NCR - Crossfit (versão nova plataforma)
	 * NAV - Avaliação Física (versão nova plataforma)
	 * GRD - Graduação (versão nova plataforma)
	 * CCL - Canal do Cliente
	 * AGN - Agenda
	 * PAY - PactoPay
	 */
	NTR = "NTR",
	NCR = "NCR",
	NAV = "NAV",
	GRD = "GRD",
	NZW = "NZW",
	CCL = "CANAL",
	AGN = "AGN",
	AGENDA = "AGENDA",
	AGE = "AGE",
	PAY = "PAY",
	/**
	 * SLC - Aula Cheia
	 * TR - Treino antigo
	 * CRM - Crm
	 * FIN - Financeiro
	 * GOR - Game of Results
	 * ZW - Adm
	 * UCP - Ucp
	 * NF - Nota fiscal
	 */
	SLC = "SLC",
	TR = "TR",
	CRM = "CRM",
	NCRM = "NCRM",
	FIN = "FIN",
	GOR = "GOR",
	ZW = "ZW",
	UCP = "UCP",
	NF = "NF",
	EST = "EST",
	GP = "GP",
	SBX = "SBX",
	ZAA = "ZAA",
	CE = "CE",
	NVO = "NVO",
	FAC = "FAC",
	NBIS = "NBIS",
	ZWB = "ZWB",
	SEC = "SEC",
}

export interface ClientDiscoveryData {
	modulosHabilitados?: PlataformaModulo[];
	empresas?: { id: number; nome: string }[];
	financeiroEmpresas?: EmpresaFinanceiro[];
	serviceUrls?: ServiceMap;
	utilizarMoviDesk?: boolean;
	utilizarChatMoviDesk?: boolean;
	grupoChatMovidesk?: string;
	utilizarOctadesk?: boolean;
	utilizarGymbot?: boolean;
}

export interface ServiceMap {
	alunoMsUrl?: string;
	loginMsUrl?: string;
	colaboradorMsUrl?: string;
	graduacaoMsUrl?: string;
	treinoApiUrl?: string;
	treinoUrl?: string;
	integracaoGympassMsUrl?: string;
	loginAppUrl?: string;
	oamdUrl?: string;
	zwUrl?: string;
	personagemMsUrl?: string;
	autenticacaoUrl?: string;
	frontPersonal?: string;
	planoMsUrl?: string;
	produtoMsUrl?: string;
	cadastroAuxiliarUrl?: string;
	clubeVantagensMsUrl?: string;
	relatorioMsUrl?: string;
	acessoSistemaMsUrl?: string;
	biMsUrl?: string;
	relatorioFull?: string;
	zwFrontUrl?: string;
	treinoFrontUrl?: string;
	zwUrlFull?: string;
	apiZwUrl?: string;
	urlPlano?: string;
	admCoreUrl?: string;
	loginFrontUrl?: string;
	pactoPayDashUrl?: string;
	contatoMsUrl?: string;
	pactoPayMsUrl?: string;
	crmMsUrl?: string;
	admMsUrl?: string;
	urlMidiaSocialMs?: string;
	urlMarketingMs?: string;
	pessoaMsUrl?: string;
	notificacaoMs?: string;
	financeiroMsUrl?: string;
	urlTreinoPersonal?: string;
	recursoMsUrl?: string;
	zwBack?: string;
}
