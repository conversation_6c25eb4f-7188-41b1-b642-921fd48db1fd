import { Injectable } from "@angular/core";
import {
	<PERSON>ttpInterceptor,
	HttpRequest,
	HttpHandler,
	HttpEvent,
} from "@angular/common/http";

import { Observable } from "rxjs";

import { environment } from "src/environments/environment";

@Injectable({
	providedIn: "root",
})
export class TokenInterceptor implements HttpInterceptor {
	/**
	 * www.crossfit.com
	 */
	private blacklistedUrls: string[] = ["www.cloudflare.com"];

	constructor() {}

	intercept(
		request: HttpRequest<any>,
		next: <PERSON>ttpHandler
	): Observable<HttpEvent<any>> {
		const alreadyExists = request.headers.get("Authorization");

		if (this.urlAllowed(request) && !alreadyExists) {
			const token = localStorage.getItem("apiToken");
			const requisicao = request.clone({
				headers: request.headers.set("Authorization", `Bearer ${token}`),
			});
			return next.handle(requisicao);
		} else {
			return next.handle(request);
		}
	}

	private urlAllowed(request: HttpRequest<any>): boolean {
		const url = request.url;
		const discovery = request.url.includes(environment.discoveryMsUrl);
		const oamd =
			(url.includes("oamd") ||
				url.includes("app/prest/auth/usuario") ||
				url.includes("app/prest/canalCliente") ||
				url.includes("prest/pactopay") ||
				url.includes("prest/integracao") ||
				url.includes("prest/obter-links-apps-pacto") ||
				url.includes("/insec/") ||
				url.includes("/ip/v2.php") ||
				url.includes("/oid")) &&
			!url.includes("personal");
		return !oamd && !discovery && !this.blacklisted(url);
	}

	private blacklisted(url) {
		let found = false;
		this.blacklistedUrls.forEach((item) => {
			if (url.includes(item)) {
				found = true;
			}
		});
		return found;
	}
}
