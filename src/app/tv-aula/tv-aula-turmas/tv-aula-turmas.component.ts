import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	OnDestroy,
} from "@angular/core";
import * as moment from "moment";
import { TreinoApiTvAulaTurmaService, TvAulaTurma } from "treino-api";
import {
	trigger,
	state,
	style,
	transition,
	animate,
} from "@angular/animations";
import { SessionService } from "@base-core/client/session.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-tv-aula-turmas",
	templateUrl: "./tv-aula-turmas.component.html",
	styleUrls: ["./tv-aula-turmas.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	animations: [
		trigger("scroll", [
			state(
				"normal",
				style({
					right: "0px",
				})
			),
			state(
				"scrolled",
				style({
					right: "calc(33% - 10px)",
				})
			),
			transition("normal => scrolled", [animate("0s 0.3s ease-in-out")]),
			transition("scrolled => normal", []),
		]),
	],
})
export class TvAulaTurmasComponent implements OnInit, OnDestroy {
	constructor(
		private cd: ChangeDetectorRef,
		private tvAulaService: TreinoApiTvAulaTurmaService,
		private localStorageSession: LocalStorageSessionService,
		private router: Router,
		private sessionService: SessionService
	) {}

	turmas: TvAulaTurma[] = [];
	isScrolled = false;
	TEMPO_DESLIZAR_IMAGEM = 5; // segundos
	TEMPO_ATUALIZAR_DADOS_BACKEND = 120; // segundos

	private loopHandle;
	private todasTurmas: TvAulaTurma[] = [];
	private index = 1;
	private started = false;
	private clockHandle;
	private updateDataHandle;

	ngOnInit() {
		if (!this.sessionService.loggedUser) {
			const urlParam: any = this.localStorageSession.getLocalStorageParams();
			urlParam.redirect = "/tv-aula";
			this.router.navigate(["adicionarConta"], {
				queryParams: urlParam,
			});
		}

		this.setupClockUpdate();
		this.updateData();
		this.updateDataHandle = setInterval(() => {
			this.updateData();
		}, this.TEMPO_ATUALIZAR_DADOS_BACKEND * 1000);
	}

	ngOnDestroy() {
		this.clearClockUpdate();
		clearInterval(this.updateDataHandle);
		this.stopRotation();
	}

	scrollDone(event) {
		if (this.started && event.toState === "scrolled") {
			this.isScrolled = false;
			this.index = this.index + 1;
			if (this.index === this.todasTurmas.length + 1) {
				this.index = 0;
			}
			this.turmas = this.getSlice();
		}
		this.cd.detectChanges();
	}

	private updateData() {
		this.tvAulaService.obterTvAulaTurmas().subscribe((turmas) => {
			turmas.forEach((turma) => {
				const inicio = moment();
				inicio.set("hour", parseInt(turma.horarioInicio.split(":")[0], 10));
				inicio.set("minute", parseInt(turma.horarioInicio.split(":")[1], 10));
				turma.horarioInicio = inicio.valueOf().toString();

				const fim = moment();
				fim.set("hour", parseInt(turma.horarioFim.split(":")[0], 10));
				fim.set("minute", parseInt(turma.horarioFim.split(":")[1], 10));
				turma.horarioFim = fim.valueOf().toString();
			});
			this.todasTurmas = turmas;
			if (turmas.length > 3) {
				this.startRotation();
			} else {
				this.stopRotation();
				this.turmas = turmas;
				this.cd.detectChanges();
			}
		});
	}

	private stopRotation() {
		if (this.loopHandle) {
			clearInterval(this.loopHandle);
		}
		this.index = 0;
	}

	private startRotation() {
		if (this.loopHandle) {
			clearInterval(this.loopHandle);
		}
		this.index = 0;
		this.turmas = this.getSlice();
		this.cd.detectChanges();
		this.loopHandle = setInterval(() => {
			this.isScrolled = true;
			this.started = true;
			this.cd.detectChanges();
		}, this.TEMPO_DESLIZAR_IMAGEM * 1000);
	}

	private getSlice(): TvAulaTurma[] {
		const clone = Object.assign([], this.todasTurmas);
		const slice = clone.slice(this.index, this.index + 4);
		if (slice.length === 3) {
			slice.push(null);
		} else if (slice.length === 2) {
			slice.push(null);
			slice.push(clone[0]);
		} else if (slice.length === 1) {
			slice.push(null);
			slice.push(clone[0]);
			slice.push(clone[1]);
		} else if (slice.length === 0) {
			slice.push(null);
			slice.push(clone[0]);
			slice.push(clone[1]);
			slice.push(clone[2]);
		}
		return slice;
	}

	private setupClockUpdate() {
		this.clockHandle = setInterval(() => {
			this.cd.detectChanges();
		}, 60 * 1000);
	}

	private clearClockUpdate() {
		clearInterval(this.clockHandle);
	}

	get time() {
		const now = moment();
		return now.format("HH:mm");
	}
}
