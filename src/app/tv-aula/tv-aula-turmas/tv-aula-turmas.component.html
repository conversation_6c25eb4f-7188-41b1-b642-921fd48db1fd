<div class="tv-aula-header">
	<!-- LEFT -->
	<div class="left-section">
		<img src="assets/images/tv-aula-header.png" />
		<div class="overlay">
			<img
				[routerLink]="['/treino']"
				class="logo"
				src="assets/images/logo-icon-pacto-branca.svg" />
			<span class="tv-aula-title" i18n="@@tv-aula:titulo">TV Aula</span>
		</div>
	</div>

	<!-- CENTER -->
	<div class="center" i18n="@@tv-aula:proximasAulas">Próximas Aulas</div>

	<!-- RIGHT -->
	<div class="right-section">
		<i class="pct pct-clock"></i>
		<div class="time">{{ time }}</div>
	</div>
</div>

<div
	[ngStyle]="{
		'background-image': 'url(assets/images/tv-aula-bg.jpg)'
	}"
	class="tv-aula-body">
	<div
		(@scroll.done)="scrollDone($event)"
		[@scroll]="isScrolled ? 'scrolled' : 'normal'"
		class="scroll-content">
		<ng-container *ngFor="let turma of turmas">
			<div *ngIf="!turma"></div>
			<pacto-tv-aula-turma-card
				*ngIf="turma"
				[horarioFim]="turma?.horarioFim"
				[horarioInicio]="turma?.horarioInicio"
				[nomeAula]="turma?.nome"
				[nomeSala]="turma?.ambiente?.nome"
				[professorImageUri]="turma?.professor?.imageUri"
				[professorNome]="turma?.professor?.nome"
				[routerLink]="[
					'/tv-aula',
					turma.aulaHorarioId
				]"></pacto-tv-aula-turma-card>
		</ng-container>
	</div>
</div>
