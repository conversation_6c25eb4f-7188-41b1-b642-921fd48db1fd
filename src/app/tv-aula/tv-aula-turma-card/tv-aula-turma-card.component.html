<div class="aula-title">
	{{ nomeAula }}
</div>

<div class="sala">
	<i class="pct pct-map-pin"></i>
	{{ nomeSala }}
</div>

<div class="horario">
	<i class="pct pct-clock"></i>
	{{ horarioInicio | date : "HH:mm" }}
	<span i18n="@@tv-aula-card:as">às</span>
	{{ horarioFim | date : "HH:mm" }}
</div>

<div class="professor">
	<pacto-cat-person-avatar
		[diameter]="80"
		[uri]="professorImageUri"></pacto-cat-person-avatar>

	<div class="detalhes">
		<div class="type-h6-bold" i18n="@@tv-aula-card:professor">Professor</div>
		<div class="type-h3-bold">{{ professorNome }}</div>
	</div>
</div>

<div *ngIf="andamento" class="em-andamento" i18n="@@tv-aula-card:emAndamento">
	EM ANDAMENTO
</div>
<div *ngIf="!andamento && !proximo" class="placeholder"></div>
<div *ngIf="proximo" class="proximo">
	<span i18n="@@tv-aula-card:comeca">COMEÇA EM</span>
	<span>
		{{ faltaMinutos }}
	</span>
	<span i18n="@@tv-aula-card:comeca2">MIN</span>
</div>
