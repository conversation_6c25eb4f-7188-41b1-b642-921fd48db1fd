import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
	HostBinding,
} from "@angular/core";

declare var moment;

@Component({
	selector: "pacto-tv-aula-turma-card",
	templateUrl: "./tv-aula-turma-card.component.html",
	styleUrls: ["./tv-aula-turma-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TvAulaTurmaCardComponent implements OnInit {
	@Input() nomeAula;
	@Input() nomeSala;
	/**
	 * Timestamp
	 */
	@Input() horarioInicio;
	/**
	 * Timestamp
	 */
	@Input() horarioFim;
	@Input() professorImageUri;
	@Input() professorNome;

	constructor() {}

	ngOnInit() {}

	@HostBinding("class.andamento")
	get andamento() {
		const now = moment().valueOf();
		return now >= this.horarioInicio && this.horarioFim >= now;
	}

	get proximo() {
		const now = moment().valueOf();
		const hora = 60 * 60 * 1000;
		return now < this.horarioInicio && now >= this.horarioInicio - hora;
	}

	get faltaMinutos() {
		const now = new Date().valueOf();
		const deltaMili = this.horarioInicio - now;
		let result = Math.round(deltaMili / (1000 * 60));
		result = result >= 0 ? result : 0;
		return result;
	}
}
