@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	border-radius: 24px;
	background-color: $azulPacto05;
	overflow: hidden;
	display: block;
	width: 100%;
	height: 100%;
	color: $branco;
	padding: 30px;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	justify-content: space-between;
	border: 10px solid $azulPacto05;
	cursor: pointer;

	&.andamento {
		border-color: $azulimPri;
	}
}

.aula-title {
	@extend .type-hero-bold;
	line-height: 1.1em;
	color: $branco;
}

.sala,
.horario {
	@extend .type-h3-bold;
	color: $branco;

	.pct {
		font-size: 24px;
	}
}

.detalhes {
	text-align: left;
	margin-left: 15px;
	margin-top: 10px;
	line-height: 1.5em;

	> div {
		color: $branco !important;
	}
}

.professor {
	display: flex;
}

.em-andamento {
	@extend .type-h3;
	color: $branco;
	background-color: $azulimPri;
	text-transform: uppercase;
	border-radius: 100px;
	text-align: center;
	line-height: 72px;
	padding: 0px 30px;
}

.proximo {
	@extend .type-h3;
	color: $branco;
	border: 5px solid $azulimPri;
	text-transform: uppercase;
	border-radius: 100px;
	text-align: center;
	line-height: 72px;
	padding: 0px 30px;
	white-space: nowrap;
	overflow-x: hidden;
}

.placeholder {
	height: 72px;
}
