import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { Routes, RouterModule } from "@angular/router";
import { TvAulaTurmasComponent } from "./tv-aula-turmas/tv-aula-turmas.component";
import { TvAulaTurmaCardComponent } from "./tv-aula-turma-card/tv-aula-turma-card.component";
import { TvAulaTurmaViewComponent } from "./tv-aula-turma-view/tv-aula-turma-view.component";
import { TvAulaAlunoViewComponent } from "./tv-aula-aluno-view/tv-aula-aluno-view.component";

const routes: Routes = [
	{
		path: "",
		component: TvAulaTurmasComponent,
	},
	{
		path: ":turmaId",
		component: TvAulaTurmaViewComponent,
	},
];

@NgModule({
	imports: [CommonModule, BaseSharedModule, RouterModule.forChild(routes)],
	declarations: [
		TvAulaTurmasComponent,
		TvAulaTurmaCardComponent,
		TvAulaTurmaViewComponent,
		TvAulaAlunoViewComponent,
	],
})
export class TvAulaModule {}
