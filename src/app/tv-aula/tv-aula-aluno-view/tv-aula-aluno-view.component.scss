@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.nome-aluno {
	@extend .type-h4-bold;
	white-space: nowrap;
	margin-top: 10px;
	text-align: center;
	color: $branco;
}

.avatar-wrapper {
	position: relative;
	margin-left: 17.5%;
	height: 0;
	width: 65%;
	padding-bottom: 65%;

	.status-icon {
		position: absolute;
		right: 0px;
		bottom: 0px;
		background-color: $branco;
		box-shadow: inset 0px 10px 12px #d3d5d7;
		width: 48px;
		height: 48px;
		border-radius: 24px;
		line-height: 60px;
		text-align: center;

		.pct {
			font-size: 30px;
			color: $azulimPri;
		}
	}
}
