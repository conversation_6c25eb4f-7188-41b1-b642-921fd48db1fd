import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ViewChild,
	ChangeDetectorRef,
	OnDestroy,
} from "@angular/core";
import { trigger, style, transition, animate } from "@angular/animations";

import { fromEvent, Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { TvAulaModoAcesso } from "treino-api";

@Component({
	selector: "pacto-tv-aula-aluno-view",
	templateUrl: "./tv-aula-aluno-view.component.html",
	styleUrls: ["./tv-aula-aluno-view.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TvAulaAlunoViewComponent implements OnInit, OnDestroy {
	@Input() nome;
	@Input() imageUri;
	@Input() modoAcesso: TvAulaModoAcesso;
	@ViewChild("wrapper", { static: true }) wrapper;
	radius = 80;
	resizeSubscription: Subscription;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.resize();
		this.resizeSubscription = fromEvent(window, "resize")
			.pipe(debounceTime(250))
			.subscribe(() => {
				this.resize();
			});
	}

	ngOnDestroy() {
		this.resizeSubscription.unsubscribe();
	}

	private resize() {
		const wrapper = this.wrapper.nativeElement;
		if (wrapper) {
			this.radius = wrapper.offsetWidth;
			this.cd.detectChanges();
		}
	}

	get iconClass() {
		let out;
		switch (this.modoAcesso) {
			case TvAulaModoAcesso.FINGER_PRINT:
				out = "pct-digital";
				break;
			case TvAulaModoAcesso.KEYBOARD:
				out = "pct-keyboard";
				break;
			case TvAulaModoAcesso.MOBILE:
				out = "pct-smartphone";
				break;
			case TvAulaModoAcesso.RECONHECIMENTO_FACIAL:
				out = "pct-smile";
				break;
		}
		return out;
	}
}
