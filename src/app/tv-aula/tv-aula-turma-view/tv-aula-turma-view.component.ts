import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	<PERSON><PERSON><PERSON><PERSON>,
	ChangeDetectorRef,
	ViewChild,
} from "@angular/core";
import { TreinoApiTvAulaTurmaService, TvAulaTurmaDetalhes } from "treino-api";

import { ActivatedRoute } from "@angular/router";
import {
	trigger,
	style,
	transition,
	animate,
	query,
	stagger,
} from "@angular/animations";
import { Observable } from "rxjs";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-tv-aula-turma-view",
	templateUrl: "./tv-aula-turma-view.component.html",
	styleUrls: ["./tv-aula-turma-view.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	animations: [
		trigger("comeIn", [
			transition("* => *", [
				query(":enter", [
					style({ opacity: 0, transform: "scale(0.9)" }),
					stagger("25ms", [
						animate("50ms", style({ opacity: 1, transform: "scale(1.05)" })),
						animate("50ms", style({ opacity: 1, transform: "scale(1)" })),
					]),
				]),
			]),
			// transition(':enter', [
			//   style( {
			//      opacity: 0,
			//      transform: 'scale(.9)'
			//   } ),
			//   animate( '0s 100ms ease-in', style({
			//     opacity: 1,
			//     transform: 'scale(1.05)'
			//   })),
			//   animate( '0s 50ms ease-in', style({
			//     opacity: 1,
			//     transform: 'scale(1)'
			//   }))
			// ])
		]),
	],
})
export class TvAulaTurmaViewComponent implements OnInit, OnDestroy {
	@ViewChild("notificacaoTranslate", { static: true })
	notificacaoTranslate: TraducoesXinglingComponent;
	PAGE_CHANGE_DELAY = 10; // seconds
	NUMBER_ITEMS_PER_PAGE = 18;
	turma: TvAulaTurmaDetalhes;
	alunosPage: any[] = [];
	currentPage = 0;

	private pageLoopHandle;

	constructor(
		private tvAulaService: TreinoApiTvAulaTurmaService,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const id = params.turmaId;
			if (id) {
				this.startLoop(id);
			}
		});
	}

	ngOnDestroy() {
		this.stopLoop();
	}

	private startLoop(id: number) {
		this.stopLoop();
		this.fetchTurma(id).subscribe((turma) => {
			if (turma !== "ambiente_sem_coletor") {
				this.turma = turma;
				this.startPageLoop(id);
			} else {
				this.snotifyService.error(
					this.notificacaoTranslate.getLabel("ambienteSemColetor")
				);
			}
		});
	}

	private startPageLoop(id: number) {
		this.currentPage = 0;
		this.displayPage();
		this.pageLoopHandle = setInterval(() => {
			if (this.moreData()) {
				this.currentPage = this.currentPage + 1;
				this.displayPage();
			} else {
				this.startLoop(id);
			}
		}, this.PAGE_CHANGE_DELAY * 1000);
	}

	private displayPage() {
		const start = this.currentPage * this.NUMBER_ITEMS_PER_PAGE;
		const end = start + this.NUMBER_ITEMS_PER_PAGE;
		this.alunosPage = this.turma.alunos.slice(start, end);
		this.cd.detectChanges();
	}

	private stopLoop() {
		if (this.pageLoopHandle) {
			clearInterval(this.pageLoopHandle);
		}
	}

	private moreData(): boolean {
		let retorno = false;
		if (this.turma.alunos && this.turma.alunos.length > 0) {
			const current = this.currentPage + 1;
			retorno = current !== this.numberOfPages;
		}
		return retorno;
	}

	getArray(size) {
		return new Array(size).fill(0);
	}

	get numberOfPages(): number {
		if (this.turma && this.turma.alunos) {
			const nofAlunos = this.turma.alunos.length;
			return Math.ceil(nofAlunos / this.NUMBER_ITEMS_PER_PAGE);
		} else {
			return 0;
		}
	}

	private fetchTurma(id): Observable<any> {
		return this.tvAulaService.obterTvAulaTurmaDetalhes(id);
	}
}
