@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	width: 100%;
	height: 100vh;
	display: block;
}

.tv-aula-header {
	height: 112px;
	background-color: $azulPacto05;
	overflow: hidden;
	display: flex;
}

.left-section {
	width: 400px;
	position: relative;

	.overlay {
		position: absolute;
		align-items: center;
		display: flex;
		bottom: 0px;
		right: 0px;
		left: 0px;
		top: 0px;
	}

	.logo {
		margin-left: 30px;
		cursor: pointer;
	}
}

.center {
	flex-grow: 1;
	display: flex;
	align-items: center;
	margin: 0px 30px;
}

.professor {
	display: flex;
	margin-right: 45px;

	.detalhes {
		display: none;
		margin-top: 5px;
		margin-left: 15px;
		line-height: 1.7em;
	}

	.professor-label {
		@extend .type-h6-bold;
		color: $azulPacto01;
	}

	.nome-professor {
		@extend .type-h3-bold;
		white-space: nowrap;
		color: $branco;
	}
}

@media (min-width: 1440px) {
	.professor .detalhes {
		display: block;
	}
	.nome-aula {
		font-size: 40px;
	}
}

.nome-aula {
	@extend .type-h2-bold;
	color: $branco;
}

.tv-aula-title {
	@extend .type-h2;
	color: $branco;
	margin-left: 45px;
	cursor: pointer;

	.pct {
		position: relative;
		top: 3px;
	}
}

.right-section {
	width: 400px;
	display: flex;
	flex-direction: row-reverse;
	padding-right: 30px;
	align-items: center;

	.pct {
		color: $branco;
		font-size: 40px;
	}

	.time {
		@extend .type-h2;
		line-height: 40px;
		padding-left: 15px;
		color: $branco;
	}
}

.tv-aula-body {
	height: calc(100vh - 112px);
	padding: 30px;
	display: flex;

	.aluno-grid {
		height: 100%;
		display: grid;
		gap: 15px;
		grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
		grid-template-rows: 1fr 1fr 1fr;
		width: 100%;
	}

	.control-area {
		height: 100%;
		margin-left: 15px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.page-counter {
		width: 18px;
		height: 18px;
		border-radius: 9px;
		background-color: $azulimPastel;
		margin: 6px 0px;

		&.current {
			background-color: $azulimPri;
		}
	}
}

.item {
	overflow: hidden;
}
