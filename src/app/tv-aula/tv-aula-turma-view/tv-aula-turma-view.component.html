<div class="tv-aula-header">
	<!-- LEFT -->
	<div class="left-section">
		<img src="assets/images/tv-aula-header.png" />
		<div class="overlay">
			<img
				[routerLink]="['/treino']"
				class="logo"
				src="assets/images/logo-icon-pacto-branca.svg" />
			<span
				[routerLink]="['/tv-aula']"
				class="tv-aula-title"
				i18n="@@tv-aula:ver-todas">
				<i class="pct pct-arrow-left"></i>
				Ver Todas
			</span>
		</div>
	</div>

	<!-- CENTER -->
	<div class="center">
		<div class="professor">
			<pacto-cat-person-avatar
				[diameter]="80"
				[uri]="turma?.professor?.imageUri"></pacto-cat-person-avatar>
			<div class="detalhes">
				<div class="professor-label">Profissional</div>
				<div
					[max]="14"
					[text]="turma?.professor?.nome"
					class="nome-professor"
					pacto-cat-text></div>
			</div>
		</div>
		<div class="nome-aula">{{ turma?.nome }}</div>
	</div>

	<!-- RIGHT -->
	<div class="right-section">
		<div class="time">
			{{ turma?.horarioInicio }} às {{ turma?.horarioFim }}
		</div>
		<i class="pct pct-clock"></i>
	</div>
</div>

<div
	[ngStyle]="{
		'background-image': 'url(assets/images/tv-aula-bg.jpg)'
	}"
	class="tv-aula-body">
	<div [@comeIn]="alunosPage.length" class="aluno-grid">
		<div *ngFor="let aluno of alunosPage" class="item">
			<pacto-tv-aula-aluno-view
				[imageUri]="aluno.imageUri"
				[modoAcesso]="aluno.tipoAcesso"
				[nome]="aluno.nome"></pacto-tv-aula-aluno-view>
		</div>
	</div>

	<div *ngIf="numberOfPages > 1" class="control-area">
		<div
			*ngFor="let item of getArray(numberOfPages); let index = index"
			[ngClass]="{ current: currentPage === index }"
			class="page-counter"></div>
	</div>
</div>

<pacto-traducoes-xingling #notificacaoTranslate>
	<span i18n="@@tv-aula:turma:descricao" xingling="ambienteSemColetor">
		O ambiente não tem nenhum coletor registrado, configure um coletor para o
		ambiente!
	</span>
</pacto-traducoes-xingling>
