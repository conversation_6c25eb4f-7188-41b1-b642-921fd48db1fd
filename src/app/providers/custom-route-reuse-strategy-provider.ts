import {
	ActivatedRouteSnapshot,
	DetachedRouteHandle,
	RouteReuseStrategy,
} from "@angular/router";
import { MenuV2RootComponent } from "../menu-v2-root/menu-v2-root.component";

export class CustomRouteReuseStrategy implements RouteReuseStrategy {
	private storedRouteHandles = new Map<string, DetachedRouteHandle>();

	shouldReuseRoute(
		future: ActivatedRouteSnapshot,
		curr: ActivatedRouteSnapshot
	): boolean {
		return future.routeConfig === curr.routeConfig;
	}

	shouldDetach(route: ActivatedRouteSnapshot): boolean {
		return (
			route.routeConfig && route.routeConfig.component === MenuV2RootComponent
		);
	}

	store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle): void {
		const path = this.getRoutePath(route);
		this.storedRouteHandles.set(path, handle);
	}

	shouldAttach(route: ActivatedRouteSnapshot): boolean {
		const path = this.getRoutePath(route);
		const handle = this.storedRouteHandles.get(path);
		return !!handle && this.isRouteSnapshotCompatible(route, handle);
	}

	retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {
		const path = this.getRoutePath(route);
		const handle = this.storedRouteHandles.get(path);
		return handle && this.isRouteSnapshotCompatible(route, handle)
			? handle
			: null;
	}

	private getRoutePath(route: ActivatedRouteSnapshot): string {
		return route.pathFromRoot
			.map((snapshot) =>
				snapshot.url.map((segment) => segment.toString()).join("/")
			)
			.join("/");
	}

	private isRouteSnapshotCompatible(
		route: ActivatedRouteSnapshot,
		handle: DetachedRouteHandle
	): boolean {
		return true;
	}
}
