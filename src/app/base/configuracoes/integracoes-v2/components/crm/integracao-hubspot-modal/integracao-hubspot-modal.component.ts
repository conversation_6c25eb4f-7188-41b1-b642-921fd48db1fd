import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";

import { SessionService } from "sdk";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";

@Component({
	selector: "pacto-integracao-hubspot-modal",
	templateUrl: "./integracao-hubspot-modal.component.html",
	styleUrls: ["./integracao-hubspot-modal.component.scss"],
})
export class IntegracaoHubspotModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;

	formGroup: FormGroup = new FormGroup({
		responsavelPadrao: new FormControl(undefined, Validators.required),
		horaLimite: new FormControl("23:59"),
		acaoObjecao: new FormControl(),
		appId: new FormControl(),
		token: new FormControl(),
		clientId: new FormControl(),
		clientSecret: new FormControl(),
		url_instalacao: new FormControl(),
		showCallBackHubsPot: new FormControl(),
		urlWebHookHubspot: new FormControl(),
	});
	acoesObjecoesLead = [
		{ codigo: 0, descricao: "Não atualizar" },
		{ codigo: 1, descricao: "Atualizar com qualquer objeção" },
		{ codigo: 2, descricao: "Atualizar apenas com objeção definitiva" },
	];

	constructor(
		private cd: ChangeDetectorRef,
		public restService: RestService,
		public admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		public sessionService: SessionService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal
	) {}

	ngOnInit() {
		if (this.integracao.configuracao) {
			this.formGroup.patchValue({
				responsavelPadrao: this.integracao.configuracao.responsavelPadrao,
				horaLimite: this.integracao.configuracao.horaLimite,
				acaoObjecao: this.integracao.configuracao.acaoObjecao,
				appId: this.integracao.configuracao.appId,
				token: this.integracao.configuracao.token,
				clientId: this.integracao.configuracao.clientId,
				clientSecret: this.integracao.configuracao.clientSecret,
				url_instalacao: this.integracao.configuracao.url_instalacao,
				showCallBackHubsPot: this.integracao.configuracao.showCallBackHubsPot,
				urlWebHookHubspot: this.integracao.configuracao.urlWebHookHubspot,
			});
			this.formGroup.get("showCallBackHubsPot").disable();
			this.formGroup.get("urlWebHookHubspot").disable();
		} else {
			this.notificationService.error("Falha ao obter configuração Hubspot!");
		}
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	aprovarIntegracaoHubSpot() {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		this.admCoreApiIntegracoesService
			.aprovarIntegracaoHubSpot(this.integracao.configuracao)
			.subscribe(
				(response) => {
					this.integracao.configuracao = response.content;
					this.notificationService.success(
						this.traducao.getLabel("aprovada-com-sucesso")
					);
					this.cd.detectChanges();
					setTimeout(() => {
						window.open(this.integracao.configuracao.url_instalacao, "_blank");
					}, 1000);
				},
				(httpErrorResponse) => {
					this.integracao.configuracao.empresaUsaHub = this.integracao.ativa;
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.empresaUsaHub = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.empresaUsaHub = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoEmpresaHubSpot(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	copyText(value: string) {
		if (navigator.clipboard) {
			navigator.clipboard
				.writeText(value)
				.then(() => {
					this.notificationService.success(
						this.traducao.getLabel("copy-sucess")
					);
				})
				.catch(() => {
					this.notificationService.error(this.traducao.getLabel("copy-error"));
				});
		} else {
			this.notificationService.error(this.traducao.getLabel("copy-error"));
		}
	}

	iconCopyClickEvent(event) {
		this.copyText(event.formControl.value);
	}
}
