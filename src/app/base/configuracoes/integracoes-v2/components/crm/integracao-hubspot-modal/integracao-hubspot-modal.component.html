<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[addtionalFilters]="getAditionalFiltersUsuariosMeta()"
				[control]="formGroup.get('responsavelPadrao')"
				[endpointUrl]="restService.buildFullUrlAdmCore('usuarios/meta')"
				[paramBuilder]="selectBuilder"
				i18n-label="@@integracao-hubspot:label-responsave-padrao"
				id="responsavel-padrao"
				idKey="codigo"
				label="Responsável padrão"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-5">
			<pacto-cat-form-input
				[control]="formGroup.get('horaLimite')"
				[maxlength]="5"
				[textMask]="{ mask: timeMask(), guide: false }"
				i18n-label="@@integracao-hubspot:placeholder-hora-limite"
				i18n-label="@@integracao-hubspot:label-hora-limite"
				id="hora-limite"
				label="Hora limite atualização da meta"
				placeholder="00:00"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select
				[control]="formGroup.get('acaoObjecao')"
				[items]="acoesObjecoesLead"
				i18n-label="@@integracao-hubspot:label-atualizar-lead-objecao"
				id="acao-objecao"
				idKey="codigo"
				label="Tipo objeção para atualizar o Lead	"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div class="col-md-5 ml-auto"></div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('token')"
				i18n-label="@@integracao-hubspot:label-token"
				i18n-placeholder="@@integracao-hubspot:placeholder-access-token"
				id="hub-token"
				label="Access Token"
				placeholder="Digite o access token"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 ml-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('clientSecret')"
				i18n-label="@@integracao-hubspot:label-client-secret"
				i18n-placeholder="
					@@integracao-hubspot:placeholder-client-secret-hubspot"
				id="client-secret"
				label="Client Secret"
				placeholder="Digite o client secret"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<pacto-cat-form-input
				(iconClick)="iconCopyClickEvent($event)"
				[control]="formGroup.get('urlWebHookHubspot')"
				i18n-label="@@integracao-hubspot:label-url-webhook"
				i18n-placeholder="@@integracao-hubspot:placeholder-webhook-hubspot"
				icon="copy cor-azulim-pri"
				iconToolTip="'Copiar para área de transferência'"
				id="url-webhook"
				label="URL Webhook"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-hubspot:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:aprovada-com-sucesso"
		xingling="aprovada-com-sucesso">
		Integração aprovada com sucesso!
	</span>
	<span i18n="@@integracao-wordpress:copy-sucess" xingling="copy-sucess">
		Link copiado para área de transferencia!
	</span>
	<span i18n="@@integracao:copy-error" xingling="copy-error">
		Falha ao tentar copiar Link!
	</span>
</pacto-traducoes-xingling>
