import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";

import { SnotifyService } from "ng-snotify";

import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { Integracao, AdmCoreApiIntegracoesService } from "adm-core-api";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-integracao-word-press-modal",
	templateUrl: "./integracao-word-press-modal.component.html",
	styleUrls: ["./integracao-word-press-modal.component.scss"],
})
export class IntegracaoWordPressModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	@Input() empresaSelecionada;

	formGroup: FormGroup = new FormGroup({
		responsavelPadrao: new FormControl(undefined, Validators.required),
		horaLimite: new FormControl("23:59"),
		acaoObjecao: new FormControl(),
		formularioPadraoMensagem: new FormControl(),
		corpoPadraoMensagem: new FormControl(),
		emailDestino: new FormControl(),
	});

	acoesObjecoesLead = [
		{ codigo: 0, descricao: "Não atualizar" },
		{ codigo: 1, descricao: "Atualizar com qualquer objeção" },
		{ codigo: 2, descricao: "Atualizar apenas com objeção definitiva" },
	];

	constructor(
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private activeModal: NgbActiveModal,
		public restService: RestService,
		private admCoreApiIntegracoesService: AdmCoreApiIntegracoesService
	) {}

	ngOnInit() {
		this.initForm();
	}

	initForm() {
		this.formGroup.patchValue({
			responsavelPadrao: this.integracao.configuracao.responsavelPadrao,
			horaLimite: this.integracao.configuracao.horaLimite,
			acaoObjecao: this.integracao.configuracao.acaoObjecao,
			formularioPadraoMensagem:
				this.integracao.configuracao.formularioPadraoMensagem,
			corpoPadraoMensagem: this.integracao.configuracao.corpoPadraoMensagem,
			emailDestino: this.integracao.configuracao.emailDestino,
		});
		this.formGroup.get("formularioPadraoMensagem").disable();
		this.formGroup.get("corpoPadraoMensagem").disable();
		this.formGroup.get("emailDestino").disable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	iconClickEvent(event) {
		this.copyText(event.formControl.value);
	}

	async copyText(text) {
		const textarea = document.createElement("textarea");
		textarea.textContent = text;
		document.body.appendChild(textarea);
		const selection = document.getSelection();
		const range = document.createRange();
		range.selectNode(textarea);
		selection.removeAllRanges();
		selection.addRange(range);
		console.log("copy success", document.execCommand("copy"));
		selection.removeAllRanges();
		document.body.removeChild(textarea);
		this.notificationService.success(this.traducao.getLabel("copy-sucess"));
	}

	async delay(ms: number) {
		await new Promise((resolve) => setTimeout(() => resolve(), ms)).then();
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoWordPress(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}
}
