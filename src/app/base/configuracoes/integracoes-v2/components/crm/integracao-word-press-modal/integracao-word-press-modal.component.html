<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[addtionalFilters]="getAditionalFiltersUsuariosMeta()"
				[control]="formGroup.get('responsavelPadrao')"
				[endpointUrl]="restService.buildFullUrlAdmCore('usuarios/meta')"
				[paramBuilder]="selectBuilder"
				i18n-label="@@integracao-wordpress:label-responsave-padrao"
				id="responsavel-padrao"
				idKey="codigo"
				label="Responsável padrão"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-5">
			<pacto-cat-form-input
				[control]="formGroup.get('horaLimite')"
				[maxlength]="5"
				[textMask]="{ mask: timeMask(), guide: false }"
				i18n-label="@@integracao-wordpress:placeholder-hora-limite"
				i18n-label="@@integracao-wordpress:label-hora-limite"
				id="hora-limite"
				label="Hora limite atualização da meta"
				placeholder="00:00"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select
				[control]="formGroup.get('acaoObjecao')"
				[items]="acoesObjecoesLead"
				i18n-label="@@integracao-wordpress:label-atualizar-lead-objecao"
				id="acao-objecao"
				idKey="codigo"
				label="Tipo objeção para atualizar o Lead	"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-textarea
				(iconClick)="iconClickEvent($event)"
				[control]="toFormControl(formGroup.get('formularioPadraoMensagem'))"
				[disabled]="true"
				[rows]="5"
				i18n-label="@@integracao-wordpress:label-formulario-padrao"
				icon="copy cor-azulim-pri"
				iconToolTip="Copiar para área de transferência"
				id="formulario-padrao-mensagem"
				label="Formulário padrão da mensagem"></pacto-cat-form-textarea>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-textarea
				(iconClick)="iconClickEvent($event)"
				[control]="toFormControl(formGroup.get('corpoPadraoMensagem'))"
				[disabled]="true"
				[rows]="5"
				i18n-label="@@integracao-wordpress:label-corpo-padrao"
				icon="copy cor-azulim-pri"
				iconToolTip="Copiar para área de transferência"
				id="corpo-padrao-mensagem"
				label="Corpo padrão da mensagem"></pacto-cat-form-textarea>
		</div>
	</div>
	<div class="row row-check-box-cpf">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				(iconClick)="iconClickEvent($event)"
				[control]="toFormControl(formGroup.get('emailDestino'))"
				i18n-label="@@integracao-wehelp:label-token-empresa"
				i18n-placeholder="@@integracao-wehelp:placeholder-token-empresa"
				icon="copy cor-azulim-pri"
				iconToolTip="'Copiar para área de transferência'"
				id="token-empresa"
				label="Token da empresa"
				placeholder="Digite o nome Token da empresa"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-rdstation:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
</pacto-traducoes-xingling>
