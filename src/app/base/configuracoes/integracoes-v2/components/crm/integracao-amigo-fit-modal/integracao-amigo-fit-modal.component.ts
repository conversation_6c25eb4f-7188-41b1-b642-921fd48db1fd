import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { switchMap } from "rxjs/operators";
import { TreinoConfigCacheService } from "../../../../configuration.service";

@Component({
	selector: "pacto-integracao-amigo-fit-modal",
	templateUrl: "./integracao-amigo-fit-modal.component.html",
	styleUrls: ["./integracao-amigo-fit-modal.component.scss"],
})
export class IntegracaoAmigoFitModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;

	formGroup: FormGroup = new FormGroup({
		nomeUsuarioAmigoFit: new FormControl(),
		senhaUsuarioAmigoFit: new FormControl(),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private configCache: TreinoConfigCacheService,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.formGroup.patchValue({
			nomeUsuarioAmigoFit: this.integracao.configuracao.nomeUsuarioAmigoFit,
			senhaUsuarioAmigoFit: this.integracao.configuracao.senhaUsuarioAmigoFit,
		});
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	salvarIntegracao(acao?: string) {
		this.integracao.configuracao.nomeUsuarioAmigoFit = this.formGroup.get(
			"nomeUsuarioAmigoFit"
		).value;
		this.integracao.configuracao.senhaUsuarioAmigoFit = this.formGroup.get(
			"senhaUsuarioAmigoFit"
		).value;
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		let save$;
		save$ = this.admCoreApiIntegracoes.salvarConfiguracaoIntegracaoAmigoFit(
			this.integracao.configuracao
		);
		const update$ = this.configCache.loadTreinoConfigIntegracoes();
		return save$.pipe(switchMap(() => update$)).subscribe(
			() => {
				this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
				this.activeModal.close();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notify.error(err.meta.messageValue);
				}
			}
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
