import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";

import { SnotifyService } from "ng-snotify";

import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService } from "adm-core-api";

@Component({
	selector: "pacto-integracao-generica-leads-modal",
	templateUrl: "./integracao-generica-leads-modal.component.html",
	styleUrls: ["./integracao-generica-leads-modal.component.scss"],
})
export class IntegracaoGenericaLeadsModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	@Input() empresaSelecionada;

	formGroup: FormGroup = new FormGroup({
		responsavelPadrao: new FormControl(undefined, Validators.required),
		horaLimite: new FormControl("23:59"),
		acaoObjecao: new FormControl(),
		urlWebhookGenerico: new FormControl(),
		formularioJson: new FormControl(),
	});

	acoesObjecoesLead = [
		{ codigo: 0, descricao: "Não atualizar" },
		{ codigo: 1, descricao: "Atualizar com qualquer objeção" },
		{ codigo: 2, descricao: "Atualizar apenas com objeção definitiva" },
	];

	constructor(
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private activeModal: NgbActiveModal,
		public restService: RestService,
		private admCoreApiIntegracoesService: AdmCoreApiIntegracoesService
	) {}

	ngOnInit() {
		this.initForm();
	}

	initForm() {
		this.formGroup.patchValue({
			responsavelPadrao: this.integracao.configuracao.responsavelPadrao,
			horaLimite: this.integracao.configuracao.horaLimite,
			acaoObjecao: this.integracao.configuracao.acaoObjecao,
			urlWebhookGenerico: this.integracao.configuracao.urlWebhookGenerico,
			formularioJson:
				"{\n" +
				'        "nome" : "J-da-oi",\n' +
				'        "email" :"<EMAIL>",\n' +
				'        "telefone" : "(11) 9999-9999",\n' +
				'        "mensagem": "inf"\n' +
				"}",
		});

		this.formGroup.get("urlWebhookGenerico").disable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoGenericaLeads(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	copyText(value: string) {
		if (navigator.clipboard) {
			navigator.clipboard
				.writeText(value)
				.then(() => {
					this.notificationService.success(
						this.traducao.getLabel("copy-sucess")
					);
				})
				.catch(() => {
					this.notificationService.error(this.traducao.getLabel("copy-error"));
				});
		} else {
			this.notificationService.error(this.traducao.getLabel("copy-error"));
		}
	}

	iconCopyClickEvent(event) {
		this.copyText(event.formControl.value);
	}
}
