@import "projects/ui/assets/import.scss";

@media (min-width: 1050px) {
	::ng-deep .modal-integracao .modal-lg {
		max-width: 1050px;
	}
}

pacto-cat-form-select,
pacto-cat-form-select-filter,
pacto-cat-form-input {
	margin: 0px;
}

.main-content {
	padding: 0.5rem 1rem;
}

.aba-container {
	width: 100%;
	padding: 1rem;
	min-height: 350px;
}

.alert-container {
	padding: 0.5rem 1rem;
	border-radius: 10px;
	width: 100%;
	margin-top: 1rem;
}

.alert-title {
	font-size: 1.2rem;
	font-weight: 700;
}

.danger {
	background-color: #fdeded;
	color: #6d1917;
}

.success {
	background-color: #edf7ed;
	color: #0a4326;
}

.alert-title {
	font-size: 1rem;
}

.row {
	align-items: center;
}

.btns-ativar-inativar {
	margin-top: 38px;
}

.api-link {
	font-family: "Nunito Sans";
	font-style: normal;
	font-weight: 600;
	font-size: 16px;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	color: $preto01;
	width: 100%;
	height: 40px;
	border-bottom: solid 1px $cinza03;
	margin-bottom: 10px;

	.btn-api {
		margin-bottom: 15px;
	}
}
