import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";

import { SessionService } from "@base-core/client/session.service";
import {
	AdmCoreApiIntegracoesService,
	ConfiguracaoEmpresaRdstation,
	Integracao,
	Empresa,
} from "adm-core-api";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { ClientDiscoveryService } from "../../../../../../microservices/client-discovery/client-discovery.service";
import { Modulo } from "../../../classes/modulos.enum";

@Component({
	selector: "pacto-integracao-rd-station-modal",
	templateUrl: "./integracao-rd-station-modal.component.html",
	styleUrls: ["./integracao-rd-station-modal.component.scss"],
})
export class IntegracaoRdStationModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;
	selectedTab: "crm" | "marketing" = "crm";

	formGroup: FormGroup = new FormGroup({
		responsavelPadrao: new FormControl(undefined, Validators.required),
		horaLimite: new FormControl("23:59"),
		acaoObjecao: new FormControl(),
		eventWeebHook: new FormControl("WEBHOOK.MARKED_OPPORTUNITY"),
		configAtualizarAlunoRdStationMarketing: new FormControl(),
	});
	gatilhos = [
		{ id: "WEBHOOK.MARKED_OPPORTUNITY", descricao: "Oportunidade" },
		{ id: "WEBHOOK.CONVERTED", descricao: "Conversão" },
	];
	acoesObjecoesLead = [
		{ codigo: 0, descricao: "Não atualizar" },
		{ codigo: 1, descricao: "Atualizar com qualquer objeção" },
		{ codigo: 2, descricao: "Atualizar apenas com objeção definitiva" },
	];
	exibirTokenAcesso: boolean = false;

	constructor(
		private cd: ChangeDetectorRef,
		public restService: RestService,
		public admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		public sessionService: SessionService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private discoveryService: ClientDiscoveryService
	) {}

	ngOnInit() {
		if (this.integracao.configuracao) {
			this.formGroup.patchValue({
				responsavelPadrao: this.integracao.configuracao.responsavelPadrao,
				horaLimite: this.integracao.configuracao.horaLimite,
				acaoObjecao: this.integracao.configuracao.acaoObjecao,
				eventWeebHook: this.integracao.configuracao.eventWeebHook,
				configAtualizarAlunoRdStationMarketing:
					this.integracao.configuracao.configAtualizarAlunoRdStationMarketing,
			});
		} else {
			this.notificationService.error("Falha ao obter configuração RD Station!");
		}
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	showToken() {
		this.exibirTokenAcesso = !this.exibirTokenAcesso;
	}

	tabClickHandler(event: { index: number; previous: string; next: string }) {
		this.selectedTab = event.index === 0 ? "crm" : "marketing";
		this.cd.detectChanges();
	}

	atualizarInformacoesIntegracao() {
		this.admCoreApiIntegracoesService
			.getConfiguracoesIntegracoes(
				this.empresaSelecionada.empresazw,
				Modulo.CRM
			)
			.subscribe(
				(response) => {
					this.integracao.configuracao =
						response.configuracaoIntegracaoRDStation;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					} else {
						this.notificationService.error(
							"Falha ao atualizar infos da config rd station "
						);
					}
				}
			);
	}

	autenticarApiMarketing() {
		const urlBase = "https://api.rd.services/auth/";
		const chaveEmpresa = this.sessionService.chave;
		const codigoEmpresa = this.sessionService.empresaId;
		let apiZwUrl = this.discoveryService.getUrlMap().apiZwUrl;
		if (!apiZwUrl.endsWith("/prest")) {
			apiZwUrl += "/prest";
		}
		const endpoint = "/lead/mktAuthRedirect";
		const clientId = "e00fd90a-6267-4744-8320-dcda19573c03";

		const state = `${codigoEmpresa}-${chaveEmpresa}`;
		const rdStationUrl = `${urlBase}dialog?client_id=${clientId}&redirect_uri=${apiZwUrl}${endpoint}&state=${state}`;

		const authWindow = window.open(rdStationUrl, "_blank");

		const checkWindowClosed = setInterval(() => {
			if (authWindow && authWindow.closed) {
				clearInterval(checkWindowClosed);
				this.atualizarInformacoesIntegracao();
			}
		}, 500);
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	aprovarIntegracaoRDStation() {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		this.admCoreApiIntegracoesService
			.aprovarIntegracaoRDStation(this.integracao.configuracao)
			.subscribe(
				(response) => {
					this.integracao.configuracao = response.content;
					this.notificationService.success(
						this.traducao.getLabel("aprovada-com-sucesso")
					);
					this.cd.detectChanges();
					setTimeout(() => {
						window.open(this.integracao.configuracao.urlRdStation, "_blank");
					}, 1000);
				},
				(httpErrorResponse) => {
					this.integracao.configuracao.empresaUsaRd = this.integracao.ativa;
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						alert(JSON.stringify(err.meta));
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.empresaUsaRd = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.empresaUsaRd = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoEmpresaRdStation(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	copyLinkApi13() {
		this.copyText(this.integracao.configuracao.apiV13Link);
	}

	copyUrlWebhook() {
		this.copyText(this.integracao.configuracao.urlWebHookRd);
	}

	copyText(value: string) {
		if (navigator.clipboard) {
			navigator.clipboard
				.writeText(value)
				.then(() => {
					this.notificationService.success(
						this.traducao.getLabel("copy-sucess")
					);
				})
				.catch(() => {
					this.notificationService.error(this.traducao.getLabel("copy-error"));
				});
		} else {
			this.notificationService.error(this.traducao.getLabel("copy-error"));
		}
	}
}
