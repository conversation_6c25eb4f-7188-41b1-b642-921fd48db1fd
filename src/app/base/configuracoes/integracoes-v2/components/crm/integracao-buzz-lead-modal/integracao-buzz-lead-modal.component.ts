import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { SessionService } from "sdk";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-integracao-buzz-lead-modal",
	templateUrl: "./integracao-buzz-lead-modal.component.html",
	styleUrls: ["./integracao-buzz-lead-modal.component.scss"],
})
export class IntegracaoBuzzLeadModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;

	formGroup: FormGroup = new FormGroup({
		urlWebHook: new FormControl(),
		tokenBuzzLead: new FormControl(),
		tokenAcessoZw: new FormControl(),
		responsavelPadrao: new FormControl(),
		horaLimite: new FormControl("23:59"),
		acaoObjecao: new FormControl(),
	});
	acoesObjecoesLead = [
		{ codigo: 0, descricao: "Não atualizar" },
		{ codigo: 1, descricao: "Atualizar com qualquer objeção" },
		{ codigo: 2, descricao: "Atualizar apenas com objeção definitiva" },
	];

	constructor(
		private cd: ChangeDetectorRef,
		public admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private sessionService: SessionService,
		public restService: RestService
	) {}

	ngOnInit() {
		this.formGroup.patchValue({
			urlWebHook: this.integracao.configuracao.urlWebHook,
			tokenAcessoZw: this.integracao.configuracao.tokenAcessoZw,
			tokenBuzzLead: this.integracao.configuracao.tokenBuzzLead,
			responsavelPadrao: this.integracao.configuracao.responsavelPadrao,
			horaLimite: this.integracao.configuracao.horaLimite,
			acaoObjecao: this.integracao.configuracao.acaoObjecao,
		});
		this.formGroup.get("urlWebHook").disable();
		this.formGroup.get("tokenAcessoZw").disable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoBuzzLead(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}
}
