<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[addtionalFilters]="getAditionalFiltersUsuariosMeta()"
				[control]="formGroup.get('responsavelPadrao')"
				[endpointUrl]="restService.buildFullUrlAdmCore('usuarios/meta')"
				[paramBuilder]="selectBuilder"
				i18n-label="@@integracao-buzzlead:label-responsave-padrao"
				id="responsavel-padrao"
				idKey="codigo"
				label="Responsável padrão"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-5">
			<pacto-cat-form-input
				[control]="formGroup.get('horaLimite')"
				[maxlength]="5"
				[textMask]="{ mask: timeMask(), guide: false }"
				i18n-label="@@integracao-buzzlead:placeholder-hora-limite"
				i18n-label="@@integracao-buzzlead:label-hora-limite"
				id="hora-limite"
				label="Hora limite atualização da meta"
				placeholder="00:00"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select
				[control]="formGroup.get('acaoObjecao')"
				[items]="acoesObjecoesLead"
				i18n-label="@@integracao-buzzlead:label-atualizar-lead-objecao"
				id="acao-objecao"
				idKey="codigo"
				label="Tipo objeção para atualizar o Lead	"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('urlWebHook'))"
				i18n-label="@@integracao-buzzlead:label-url-webhook"
				id="url-webhook"
				label="URL WebHook"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('tokenBuzzLead'))"
				i18n-label="@@integracao-buzzlead:label-token-buzzlead"
				i18n-placeholder="@@integracao-buzzlead:placeholder-token-buzzlead"
				id="token-buzzlead"
				label="Token privado Buzzlead"
				placeholder="Digite o token privado"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row row-check-box-cpf">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('tokenAcessoZw'))"
				i18n-label="@@integracao-buzzlead:label-token-acesso-zw"
				id="token-acesso-zw"
				label="Token acesso ZW"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-buzzlead:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
</pacto-traducoes-xingling>
