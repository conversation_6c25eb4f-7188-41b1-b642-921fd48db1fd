import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";

@Component({
	selector: "pacto-integracao-we-help-modal",
	templateUrl: "./integracao-we-help-modal.component.html",
	styleUrls: ["./integracao-we-help-modal.component.scss"],
})
export class IntegracaoWeHelpModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;

	formGroup: FormGroup = new FormGroup({
		nomeEmpresa: new FormControl(),
		idEmpresa: new FormControl(),
		tokenEmpresa: new FormControl(),
		cpfCodigoInternoWeHelp: new FormControl(),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.initForm();
	}

	initForm() {
		this.formGroup.patchValue({
			nomeEmpresa: this.integracao.configuracao.empresa.nome,
			idEmpresa: this.integracao.configuracao.empresa.codigo,
			tokenEmpresa: this.integracao.configuracao.chave,
			cpfCodigoInternoWeHelp:
				this.integracao.configuracao.cpfCodigoInternoWeHelp,
		});
		this.formGroup.get("nomeEmpresa").disable();
		this.formGroup.get("idEmpresa").disable();
		this.formGroup.get("tokenEmpresa").disable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoWeHelp(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notify.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
