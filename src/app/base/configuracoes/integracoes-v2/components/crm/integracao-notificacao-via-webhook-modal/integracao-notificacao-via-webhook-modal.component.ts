import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { switchMap } from "rxjs/operators";
import { TreinoConfigCacheService } from "../../../../configuration.service";

@Component({
	selector: "pacto-integracao-notificacao-via-webhook-modal",
	templateUrl: "./integracao-notificacao-via-webhook-modal.component.html",
	styleUrls: ["./integracao-notificacao-via-webhook-modal.component.scss"],
})
export class IntegracaoNotificacaoViaWebhookModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	urlWebhookNotificar: FormControl = new FormControl();

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private configCache: TreinoConfigCacheService,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.urlWebhookNotificar.setValue(
			this.integracao.configuracao.urlWebhookNotificar
		);
	}

	salvarIntegracao(acao?: string) {
		this.integracao.configuracao.urlWebhookNotificar =
			this.urlWebhookNotificar.value;
		if (acao === "inativar") {
			this.integracao.configuracao.notificarWebhook = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.notificarWebhook = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		let save$;
		save$ =
			this.admCoreApiIntegracoes.salvarConfiguracaoIntegracaoNotificarWebhook(
				this.integracao.configuracao
			);
		const update$ = this.configCache.loadTreinoConfigIntegracoes();
		return save$.pipe(switchMap(() => update$)).subscribe(
			() => {
				this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
				this.activeModal.close();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notify.error(err.meta.messageValue);
				}
			}
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
