<div class="main-content">
	<div class="row alinhamento-row">
		<div class="col-md-6 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('descricaoFluxo')"
				i18n-label="@@integracao-gymbotpro:label-descricao-fluxo"
				i18n-placeholder="@@integracao-gymbotpro:placeholder-descricao-fluxo"
				id="descricaoFluxo"
				label="Identificador*"
				maxlength="30"
				placeholder="Digite a descrição do Identificador"></pacto-cat-form-input>
		</div>
		<div class="col-md-6 mr-auto">
			<pacto-cat-form-select
				[control]="toFormControl(formGroup.get('tipoFluxo'))"
				[items]="tipoFluxolist"
				i18n-label="@@integracao-gymbotpro:label-tipo-fluxo"
				id="acao-tipoFluxo"
				idKey="codigo"
				label="Local*"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>

	<div *ngIf="isFaseCrm()" class="row">
		<div class="col">
			<pacto-cat-form-select
				[control]="formGroup.get('acaoFaseCrm')"
				[items]="acaoFaseCrm"
				i18n-label="@@integracao-gymbotpro:label-acao-fase"
				id="acao-fase"
				idKey="codigo"
				label="Fase do Crm*"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>
	<div class="row alinhamento-row">
		<div class="col-md-6 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('token')"
				i18n-placeholder="@@integracao-gymbotpro:placeholder-token"
				id="token"
				label="Token*"
				placeholder="Digite o token"></pacto-cat-form-input>
		</div>
		<div class="col-md-6 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('idFluxo')"
				i18n-label="@@integracao-gymbotpro:label-id-fluxo"
				i18n-placeholder="@@integracao-gymbotpro:placeholder-id-fluxo"
				id="idFluxo"
				label="ID de integração*"
				pattern="[0-9]*"
				placeholder="Digite o ID de integração"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row alinhamento-row">
		<div class="col-md-6 mr-auto"></div>
		<div class="col-md-6 mr-auto alinhamento-right-top">
			<pacto-cat-button
				(click)="adicionarGymbotpro()"
				i18n-label="@@integracao-gymbotpro:label-btn-adicionar"
				id="btn-adicionar"
				label="Adicionar"
				size="LARGE"
				type="OUTLINE_GRAY"></pacto-cat-button>
		</div>
	</div>

	<pacto-relatorio
		#tableDataComponent
		(iconClick)="actionClickHandler($event)"
		(pageChangeEvent)="pageChangeEvent($event)"
		(pageSizeChange)="pageSizeChange($event)"
		(sortEvent)="ordenarGymbotpro($event)"
		[actionTitulo]="'Ações'"
		[itensPerPage]="itensPerPage"
		[showShare]="false"
		[table]="tableData"
		style="background: #0f46c8"></pacto-relatorio>

	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-gymbotpro:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração incluida com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span i18n="@@integracoes:error-ao-salvar" xingling="error-ao-salvar">
		Erro ao efetuar a operação
	</span>
</pacto-traducoes-xingling>

<ng-template #columnAtivo>
	<span i18n="@@integracao-gymbotpro:column-ativo">Status</span>
</ng-template>

<ng-template #columnCodigo>
	<span i18n="@@integracao-gymbotpro:column-codigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@integracao-gymbotpro:column-descricao">Identificador</span>
</ng-template>

<ng-template #columnDisponibilidade>
	<span i18n="@@integracao-gymbotpro:column-disponibilidade">Local</span>
</ng-template>

<ng-template #columnFase>
	<span i18n="@@integracao-gymbotpro:column-fase">Fase do crm</span>
</ng-template>

<!--tooltip icons-->
<span #tooltipAtivar [hidden]="true" i18n="@@crud-alunos:ativar:tooltip-icon">
	Ativar
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipEditar [hidden]="true">Editar</span>
<!--end tooltip icons-->

<span #inativarModalBody [hidden]="true">
	Deseja inativar o identificador: {{ nomeIdentificador }} ?
</span>
<span #ativarModalBody [hidden]="true">
	Deseja ativar o identificador: {{ nomeIdentificador }} ?
</span>

<span #inativarModalTitle [hidden]="true">Inativar Identificador ?</span>
<span #inativarModalMsg [hidden]="true">
	Identificador inativado com sucesso.
</span>
<span #ativarModalTitle [hidden]="true">Ativar Identificador ?</span>
<span #ativarModalMsg [hidden]="true">Identificador ativada com sucesso.</span>
