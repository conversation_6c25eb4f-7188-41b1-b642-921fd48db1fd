import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { SessionService } from "sdk";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-integracao-bitrix24-lead-modal",
	templateUrl: "./integracao-bitrix24-lead-modal.component.html",
	styleUrls: ["./integracao-bitrix24-lead-modal.component.scss"],
})
export class IntegracaoBitrix24LeadModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;

	formGroup: FormGroup = new FormGroup({
		url: new FormControl(),
		urlWebHook: new FormControl(),
		responsavelPadrao: new FormControl(),
		acaoobjecao: new FormControl(),
	});
	acoesObjecoesLead = [
		{ codigo: 0, descricao: "Não atualizar" },
		{ codigo: 1, descricao: "Atualizar com qualquer objeção" },
		{ codigo: 2, descricao: "Atualizar apenas com objeção definitiva" },
	];

	constructor(
		private cd: ChangeDetectorRef,
		public admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private sessionService: SessionService,
		public restService: RestService
	) {}

	ngOnInit() {
		console.log(this.integracao.configuracao);
		this.formGroup.patchValue({
			url: this.integracao.configuracao.url,
			responsavelPadrao: this.integracao.configuracao.responsavelPadrao,
			acaoobjecao: this.integracao.configuracao.acaoobjecao,
			urlWebHook: this.integracao.configuracao.urlWebHookBitrix,
		});

		this.formGroup.get("urlWebHook").disable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoBitrix24Lead(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}
}
