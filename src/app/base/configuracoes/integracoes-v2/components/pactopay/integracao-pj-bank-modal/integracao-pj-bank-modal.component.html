<div class="main-content">
	<pacto-integracao-pjbank-form-nova-credencial
		(credencialGeradaEvent)="closeModal()"
		*ngIf="!possuiConvenios()"
		[empresa]="
			this.integracao.configuracao.empresa
		"></pacto-integracao-pjbank-form-nova-credencial>

	<ng-container *ngIf="possuiConvenios()">
		<div class="col-md-5 mr-auto">
			<h3>Convênios de Cobrança PJBank</h3>
		</div>
		<div id="table-convenios">
			<pacto-relatorio
				#tableDataComponent
				(pageChangeEvent)="pageChangeEvent($event)"
				(pageSizeChange)="pageSizeChange($event)"
				(sortEvent)="ordenarItens($event)"
				[showShare]="false"
				[table]="tableDataGridConfig"></pacto-relatorio>
		</div>
	</ng-container>
</div>

<ng-template #columnCodigo>
	<span i18n="@@integracao-pjbank:column-codigo">Cód.</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@integracao-pjbank:column-descricao">Descrição</span>
</ng-template>
<ng-template #columnChavePjBank>
	<span i18n="@@integracao-pjbank:column-chave-pjbank">Chave PJBank</span>
</ng-template>
<ng-template #columnCredencialPjBank>
	<span i18n="@@integracao-pjbank:column-credencia-pjbank">
		Credencial PJBank
	</span>
</ng-template>
<ng-template #columnSituacao>
	<span i18n="@@integracao-pjbank:column-situacao">Situação</span>
</ng-template>

<ng-template #situacao let-convenioCobranca="item">
	<pacto-cat-switch
		[control]="getFormSituacao(convenioCobranca)"
		type="PRIMARY"></pacto-cat-switch>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:situacao-convenio-alterado-com-sucesso"
		xingling="situacao-convenio-alterado-com-sucesso">
		Situação alterada com sucesso!
	</span>
</pacto-traducoes-xingling>
