import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import { isNullOrUndefinedOrEmpty } from "sdk";

import { MsPactopayApiIntegracoesService, Empresa } from "ms-pactopay-api";
import {
	CadastroAuxApiContaCorrenteService,
	ContaCorrente,
} from "cadastro-aux-api";
import { PJBankNovaCredencial } from "ms-pactopay-api";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-integracao-pjbank-form-nova-credencial",
	templateUrl: "./integracao-pjbank-form-nova-credencial.component.html",
	styleUrls: ["./integracao-pjbank-form-nova-credencial.component.scss"],
})
export class IntegracaoPjbankFormNovaCredencialComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() empresa: Empresa;
	@Output() credencialGeradaEvent: EventEmitter<any> = new EventEmitter();
	formNovaCredencial: FormGroup = new FormGroup({
		nomeEmpresa: new FormControl(),
		cnpj: new FormControl(),
		contaCorrente: new FormControl(),
		ddd: new FormControl("", Validators.required),
		telefone: new FormControl("", Validators.required),
		email: new FormControl("", Validators.required),
	});
	optionsContas;
	contas = Array<ContaCorrente>();

	constructor(
		private cadastroAuxApiContaCorrenteService: CadastroAuxApiContaCorrenteService,
		private cd: ChangeDetectorRef,
		private notify: SnotifyService,
		private msPactopayApiIntegracoesService: MsPactopayApiIntegracoesService
	) {}

	ngOnInit() {
		this.initForm();
		this.initOptionContas();
	}

	private initForm() {
		this.formNovaCredencial.patchValue({
			nomeEmpresa: this.empresa.nome,
			cnpj: this.empresa.cnpj,
		});
		this.formNovaCredencial.get("nomeEmpresa").disable();
		this.formNovaCredencial.get("cnpj").disable();
	}

	initOptionContas() {
		if (isNullOrUndefinedOrEmpty(this.empresa)) {
			this.notify.error("Falha ao carregar dados da empresa");
			return;
		}
		setTimeout(() => {
			this.optionsContas = new Array<any>();
			this.cadastroAuxApiContaCorrenteService
				.findAll({ codigoEmpresa: this.empresa.codigo })
				.subscribe(
					(contas) => {
						contas.forEach((cc) => {
							this.optionsContas.push({
								codigo: cc.codigo,
								descricao: `AG: ${cc.agencia}-${cc.agenciaDv} CC: ${cc.contaCorrente}-${cc.contaCorrenteDv}`,
							});
							this.contas.push(cc);
						});
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								"Falha ao consultar conta corrente! " + err.meta.message
							);
						}
					}
				);
		});
	}

	gerarCredencial() {
		const pjBankNovaCredencial = new PJBankNovaCredencial();
		pjBankNovaCredencial.empresa = this.empresa;
		pjBankNovaCredencial.ddd = this.formNovaCredencial.get("ddd").value;
		pjBankNovaCredencial.telefone =
			this.formNovaCredencial.get("telefone").value;
		pjBankNovaCredencial.email = this.formNovaCredencial.get("email").value;
		pjBankNovaCredencial.contaCorrente = this.getContaCorrenteSelecionada();

		if (this.validarCampos(pjBankNovaCredencial)) {
			this.msPactopayApiIntegracoesService
				.gerarCredencialPJBank(pjBankNovaCredencial)
				.subscribe(
					() => {
						this.notify.success(
							this.traducao.getLabel("integracao-realizar-com-sucesso")
						);
						this.credencialGeradaEvent.emit();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								this.traducao.getLabel("falha-realizar-integracao") +
									err.meta.message
							);
						}
					}
				);
		}
	}

	getContaCorrenteSelecionada() {
		const codigo = this.formNovaCredencial.get("contaCorrente").value;
		if (isNullOrUndefinedOrEmpty(codigo)) {
			return undefined;
		}
		for (const cc of this.contas) {
			if (cc.codigo === parseInt(codigo, 10)) {
				return cc;
			}
		}
	}

	validarCampos(pjBankNovaCredencial: PJBankNovaCredencial) {
		if (isNullOrUndefinedOrEmpty(pjBankNovaCredencial.contaCorrente)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pjbank-validacao-conta")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pjBankNovaCredencial.ddd)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pjbank-validacao-ddd")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pjBankNovaCredencial.telefone)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pjbank-validacao-telefone")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pjBankNovaCredencial.email)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pjbank-validacao-email")
			);
			return false;
		}
		return true;
	}
}
