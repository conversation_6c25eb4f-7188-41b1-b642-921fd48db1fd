import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { AbstractControl, FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import {
	DialogService,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

import { Integracao } from "adm-core-api";
import { IntegracaoKobanaFormCadastroComponent } from "./integracao-kobana-form-cadastro/integracao-kobana-form-cadastro.component";
import { PactoModalSize } from "@base-core/modal/modal.service";
import { Empresa } from "ms-pactopay-api";
import { ZwServletKobanaApiService } from "zw-servlet-api";
import { IntegracaoKobanaFormDadosComponent } from "./integracao-kobana-form-dados/integracao-kobana-form-dados.component";

@Component({
	selector: "pacto-integracao-kobana-modal",
	templateUrl: "./integracao-kobana-modal.component.html",
	styleUrls: ["./integracao-kobana-modal.component.scss"],
})
export class IntegracaoKobanaModalComponent implements OnInit {
	@ViewChild("tooltipVerDadosBancarios", { static: true })
	tooltipVerDadosBancarios;
	@ViewChild("tableContasConectadas", { static: false })
	tableContasConectadas: RelatorioComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;
	@Input() empresa: Empresa;
	dadosIntegracaoKobana: any;

	table: PactoDataGridConfig;
	itensPerPage = [{ id: 10, label: "10" }];
	kobanaDTO = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	constructor(
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private modalService: DialogService,
		private zwServletKobanaApiService: ZwServletKobanaApiService
	) {}

	ngOnInit() {
		if (this.possuiIntegracaoAtiva() || this.possuiIntegracaoInativada()) {
			this.createKobanaPageObject();
		}
		this.configTable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	abrirModalNovaIntegracaoKobana() {
		const modal = this.modalService.open(
			"Nova integração para uso de Lote de Pagamentos",
			IntegracaoKobanaFormCadastroComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.empresa = this.integracao.configuracao.empresa;
		this.activeModal.close();
	}

	inativarIntegracaoKobana() {
		this.zwServletKobanaApiService
			.inativarConta(
				this.integracao.configuracao.empresa.codigo,
				this.integracao.configuracao.ambiente
			)
			.subscribe({
				next: () => {
					this.notificationService.success("Integração inativada com sucesso!");
					this.tableContasConectadas.reloadData();
					this.cd.detectChanges();
					this.configTable();
					this.activeModal.close();
				},
				error: (error) => {
					this.notificationService.error(error.error.meta.message);
				},
			});
	}

	reativarIntegracaoKobana() {
		this.zwServletKobanaApiService
			.reativarConta(
				this.integracao.configuracao.empresa.codigo,
				this.integracao.configuracao.ambiente
			)
			.subscribe({
				next: () => {
					this.notificationService.success("Integração reativada com sucesso!");
					this.tableContasConectadas.reloadData();
					this.cd.detectChanges();
					this.configTable();
					this.activeModal.close();
				},
				error: (error) => {
					this.notificationService.error(error.error.meta.message);
				},
			});
	}

	configTable() {
		const tooltipVerDadosBancarios =
			this.tooltipVerDadosBancarios.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.kobanaDTO;
			},
			pagination: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Cód.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "business_legal_name",
					titulo: "Razão Social",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nickname",
					titulo: "Nome",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "business_cnpj",
					titulo: "CNPJ",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "email",
					titulo: "E-mail",
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "verDados",
					iconClass: "pct pct-file-text cor-azulim05",
					tooltipText: tooltipVerDadosBancarios,
				},
			],
		});
	}

	createKobanaPageObject(page = 1, size = 10) {
		this.kobanaDTO.totalElements = 1;
		this.kobanaDTO.size = 1;
		this.kobanaDTO.totalPages = 1;
		this.kobanaDTO.first = true;
		this.kobanaDTO.last = false;
		this.kobanaDTO.content.push(this.integracao.configuracao);
	}

	possuiIntegracaoAtiva(): boolean {
		return this.integracao.configuracao && this.integracao.configuracao.ativo;
	}

	possuiIntegracaoInativada(): boolean {
		return (
			!this.integracao.configuracao.ativo &&
			this.integracao.configuracao.codigo !== 0
		);
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "verDados") {
			this.verDadosIntegracaoKobana();
		}
	}

	verDadosIntegracaoKobana() {
		this.zwServletKobanaApiService
			.obterDados(
				this.integracao.configuracao.empresa.codigo,
				this.integracao.configuracao.ambiente
			)
			.subscribe(
				(response) => {
					this.dadosIntegracaoKobana = response;
					if (response) {
						const modal = this.modalService.open(
							"Dados Integração",
							IntegracaoKobanaFormDadosComponent,
							PactoModalSize.LARGE
						);
						modal.componentInstance.empresa =
							this.integracao.configuracao.empresa;
						modal.componentInstance.dadosIntegracaoKobana = response;
						this.activeModal.close();
					}
				},
				(error) => {
					this.notificationService.error(error.error.meta.message);
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
