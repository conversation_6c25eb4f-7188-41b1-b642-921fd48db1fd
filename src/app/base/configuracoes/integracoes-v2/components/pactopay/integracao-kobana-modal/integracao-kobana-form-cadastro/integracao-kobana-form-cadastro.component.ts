import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import { isNullOrUndefinedOrEmpty } from "sdk";

import { Empresa } from "ms-pactopay-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { ConfiguracaoIntegracaoKobana } from "adm-core-api";
import { ZwServletKobanaApiService } from "zw-servlet-api";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { NotifyChangesService } from "../../../../services/notify-changes.service";
import {
	CadastroAuxApiContaCorrenteService,
	ContaCorrente,
} from "cadastro-aux-api";

@Component({
	selector: "pacto-integracao-kobana-form-cadastro",
	templateUrl: "./integracao-kobana-form-cadastro.component.html",
	styleUrls: ["./integracao-kobana-form-cadastro.component.scss"],
})
export class IntegracaoKobanaFormCadastroComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() empresa: Empresa;
	@Input() ambiente: number;
	formNovaConta: FormGroup = new FormGroup({
		ambiente: new FormControl(),
		business_legal_name: new FormControl(),
		nickname: new FormControl(),
		cnpj: new FormControl(),
		email: new FormControl("", Validators.required),
		contaCorrente: new FormControl(),
	});
	optionsContas;
	contas = Array<ContaCorrente>();
	ambientes: Array<{ codigo: number; descricao: string }> = [
		{ codigo: 1, descricao: "PRODUÇÃO" },
		{ codigo: 2, descricao: "HOMOLOGAÇÃO / SANDBOX" },
	];

	constructor(
		private cadastroAuxApiContaCorrenteService: CadastroAuxApiContaCorrenteService,
		private cd: ChangeDetectorRef,
		private notify: SnotifyService,
		private zwServletKobanaApiService: ZwServletKobanaApiService,
		private activeModal: NgbActiveModal,
		private notifyChangesService: NotifyChangesService
	) {}

	ngOnInit() {
		this.initForm();
		this.initOptionContas();
	}

	private initForm() {
		this.formNovaConta.patchValue({
			ambiente: undefined,
			business_legal_name: this.empresa.razaoSocial,
			nickname: this.empresa.nome,
			cnpj: this.empresa.cnpj,
			email: this.empresa.email,
		});
		this.formNovaConta.get("business_legal_name").disable();
		this.formNovaConta.get("cnpj").disable();
		this.formNovaConta.get("nickname").disable();
		this.formNovaConta.get("email").disable();
	}

	initOptionContas() {
		if (isNullOrUndefinedOrEmpty(this.empresa)) {
			this.notify.error("Falha ao carregar dados da empresa");
			return;
		}
		setTimeout(() => {
			this.optionsContas = new Array<any>();
			this.cadastroAuxApiContaCorrenteService
				.findAll({ codigoEmpresa: this.empresa.codigo })
				.subscribe(
					(contas) => {
						contas.forEach((cc) => {
							this.optionsContas.push({
								codigo: cc.codigo,
								descricao: `AG: ${cc.agencia}-${cc.agenciaDv} CC: ${cc.contaCorrente}-${cc.contaCorrenteDv}`,
							});
							this.contas.push(cc);
						});
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								"Falha ao consultar conta corrente! " + err.meta.message
							);
						}
					}
				);
		});
	}

	getContaCorrenteSelecionada() {
		const codigo = this.formNovaConta.get("contaCorrente").value;
		if (isNullOrUndefinedOrEmpty(codigo)) {
			return undefined;
		}
		for (const cc of this.contas) {
			if (cc.codigo === parseInt(codigo, 10)) {
				return cc;
			}
		}
	}

	gerarSubconta() {
		const configuracaoIntegracaoKobana = new ConfiguracaoIntegracaoKobana();
		configuracaoIntegracaoKobana.empresa = this.empresa.codigo;
		configuracaoIntegracaoKobana.business_cnpj =
			this.formNovaConta.get("cnpj").value;
		configuracaoIntegracaoKobana.business_legal_name = this.formNovaConta.get(
			"business_legal_name"
		).value;
		configuracaoIntegracaoKobana.nickname =
			this.formNovaConta.get("nickname").value;
		configuracaoIntegracaoKobana.email = this.formNovaConta.get("email").value;
		configuracaoIntegracaoKobana.contaCorrente =
			this.getContaCorrenteSelecionada();
		configuracaoIntegracaoKobana.ambiente =
			this.formNovaConta.get("ambiente").value;

		if (this.validarCampos(configuracaoIntegracaoKobana)) {
			this.zwServletKobanaApiService
				.criarConta(configuracaoIntegracaoKobana)
				.subscribe(
					() => {
						this.notify.success(
							this.traducao.getLabel("integracao-realizada-com-sucesso")
						);
						this.notifyChangesService.notifyChanges();
						this.activeModal.close();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								this.traducao.getLabel("falha-realizar-integracao") +
									err.meta.message
							);
						}
					}
				);
		}
	}

	validarCampos(configuracaoIntegracaoKobana: ConfiguracaoIntegracaoKobana) {
		if (isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.ambiente)) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-ambiente")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.empresa)) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-codEmpresa")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.nickname)) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-nomeEmpresa")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.business_cnpj)) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-cnpj")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.email)) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-email")
			);
			return false;
		}
		if (
			isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.business_legal_name)
		) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-razaoEmpresa")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(configuracaoIntegracaoKobana.contaCorrente)) {
			this.notify.error(
				this.traducao.getLabel("integracao-kobana-validacao-conta")
			);
			return false;
		}
		return true;
	}
}
