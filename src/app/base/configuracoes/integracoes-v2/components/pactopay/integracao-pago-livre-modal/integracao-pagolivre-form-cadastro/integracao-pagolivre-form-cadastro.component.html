<div class="row">
	<ng-container *ngIf="logindingMerchant">
		<div class="loader">
			<img src="assets/images/loading.svg" />
			<span i18n="@@integracao-acesso:loading-message">
				Carregando dados merchant {{ getNomeApresentar() }}...
			</span>
		</div>
	</ng-container>
</div>

<ng-container *ngIf="!logindingMerchant">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select
				[control]="formGroup.get('ambiente')"
				[disabled]="merchantId?.length > 0"
				[items]="ambientes"
				i18n-label="@@integracao-pago-livre:label-ambiente"
				id="ambiente"
				idKey="codigo"
				label="Ambiente"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('nomeEmpresa')"
				i18n-label="@@integracao-pago-livre:label-nome-empresa"
				i18n-placeholder="@@integracao-pago-livre:placeholder-nome-empresa"
				id="nome-empresa"
				label="Nome da empresa"
				placeholder="Digite a nome da empresa"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('cnpj')"
				i18n-label="@@integracao-pago-livre:label-cnpj-empresa"
				id="cnpj-empresa"
				label="CNPJ da empresa"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('email')"
				i18n-label="@@integracao-pago-livre:label-email-empresa"
				i18n-placeholder="@@integracao-pago-livre:placeholder-email-empresa"
				id="email-empresa"
				label="Email da empresa"
				placeholder="Digite a email da empresa"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('emailUser')"
				i18n-label="@@integracao-pago-livre:label-usuario-portal-email"
				id="usuario-portal-email"
				label="Usuário do portal (email)"></pacto-cat-form-input>
		</div>
		<div style="width: 65px">
			<pacto-cat-form-input
				[control]="formGroup.get('ddd')"
				[maxlength]="2"
				i18n-label="@@integracao-pagolivre:label-ddd"
				i18n-placeholder="@@integracao-pagolivre:placeholder-ddd"
				id="ddd"
				label="DDD"
				placeholder="ddd"></pacto-cat-form-input>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="formGroup.get('telefone')"
				[maxlength]="9"
				i18n-label="@@integracao-pago-livre:label-telefone"
				i18n-placeholder="@@integracao-pago-livre:placeholder-telefone"
				id="telefone"
				label="Telefone"
				placeholder="Digite o telefone"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('nomeUser')"
				i18n-label="@@integracao-pago-livre:label-nome-usuario-portal"
				i18n-placeholder="
					@@integracao-pago-livre:placeholder-nome-usuario-portal"
				id="nome-usuario-portal"
				label="Nome do usuário (Portal {{ getNomeApresentar() }})"
				placeholder="Digite o usuário"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-select
				[control]="formContaCorrente"
				[disabled]="merchantId?.length > 0"
				[items]="optionsContas"
				i18n-label="@@integracao-pago-livre:label-conta-recebimento"
				id="conta"
				idKey="codigo"
				label="Conta para recebimento"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>
	<div class="row justify-content-end btn-cadastrar">
		<pacto-cat-button
			(click)="salvarAlteracoes()"
			*ngIf="!isCadastrar()"
			i18n-label="@@integracoes-pago-livre:btn-alterar"
			id="btn-alterar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"></pacto-cat-button>
		<pacto-cat-button
			(click)="cadastrarMerchant()"
			*ngIf="isCadastrar()"
			i18n-label="@@integracoes-pago-livre:btn-cadastrar"
			id="btn-cadastrar"
			label="Cadastrar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>
	</div>
</ng-container>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@integracao-pagolivre:falha-realizar-integracao"
		xingling="falha-realizar-integracao">
		Falha ao realizar integração!
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-ambiente"
		xingling="integracao-pagolivre-validacao-ambiente">
		O campo "Ambiente" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-tipoIntegracao"
		xingling="integracao-pagolivre-validacao-tipoIntegracao">
		O campo "Tipo Integracao" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-nomeempresa"
		xingling="integracao-pagolivre-validacao-nomeempresa">
		O campo "Nome da empresa" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-cnpj"
		xingling="integracao-pagolivre-validacao-cnpj">
		O campo "CNPJ da empresa" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-email"
		xingling="integracao-pagolivre-validacao-email">
		O campo "E-mail da empresa" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-emailuser"
		xingling="integracao-pagolivre-validacao-emailuser">
		O campo "Usuário do portal (e-mail)" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-ddd"
		xingling="integracao-pagolivre-validacao-ddd">
		O campo "DDD" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-telefone"
		xingling="integracao-pagolivre-validacao-telefone">
		O campo "Telefone" deve ser informado.
	</span>
	<span
		i18n="@@integracao-pagolivre:integracao-pagolivre-validacao-conta"
		xingling="integracao-pagolivre-validacao-conta">
		O campo "Conta" deve ser informado.
	</span>
</pacto-traducoes-xingling>
