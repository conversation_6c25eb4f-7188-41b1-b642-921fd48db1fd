import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { isNullOrUndefinedOrEmpty, sortList, valueToBoolean } from "sdk";

import { CadastroAuxApiContaCorrenteService } from "cadastro-aux-api";
import { Integracao } from "adm-core-api";
import {
	ConvenioCobranca,
	MsPactopayApiConvenioCobrancaService,
	MsPactopayApiIntegracoesService,
	PagoLivreMerchant,
} from "ms-pactopay-api";

@Component({
	selector: "pacto-integracao-pago-livre-modal",
	templateUrl: "./integracao-pago-livre-modal.component.html",
	styleUrls: ["./integracao-pago-livre-modal.component.scss"],
})
export class IntegracaoPagoLivreModalComponent implements OnInit {
	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnMerchantId", { static: true })
	columnMerchantId: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("tableDataComponent", { static: true })
	tableDataComponent: RelatorioComponent;
	@ViewChild("situacao", { static: true }) situacao: TemplateRef<any>;

	state: PactoDataGridState = new PactoDataGridState();
	tableDataGridConfig: PactoDataGridConfig;
	conveniosData = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	formsSituacao = new Array<{ id: number; form: FormControl }>();

	constructor(
		private activeModal: NgbActiveModal,
		private cadastroAuxApiContaCorrenteService: CadastroAuxApiContaCorrenteService,
		private cd: ChangeDetectorRef,
		private msPactoPayApiIntegracoes: MsPactopayApiIntegracoesService,
		private msPactopayApiConvenioCobrancaService: MsPactopayApiConvenioCobrancaService,
		private msPactopayApiIntegracoesService: MsPactopayApiIntegracoesService,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		if (this.possuiConvenios()) {
			this.createPageObject();
			this.initTable();
		}
	}

	private initTable() {
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.tableDataGridConfig = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return this.conveniosData;
			},
			state: this.state,
			pagination: true,
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
					width: "25%",
				},
				{
					nome: "codigoAutenticacao01",
					titulo: this.columnMerchantId,
					visible: true,
					ordenavel: true,
					width: "20%",
				},
				{
					nome: "situacao",
					titulo: this.columnSituacao,
					visible: true,
					ordenavel: false,
					celula: this.situacao,
					width: "10%",
				},
			],
		});
		this.cd.detectChanges();
	}

	createPageObject(page = 1, size = 5, reloadData = true) {
		this.conveniosData.totalElements =
			this.integracao.configuracao.convenios.length;
		this.conveniosData.size = size;
		this.conveniosData.totalPages = Math.ceil(
			+(this.conveniosData.totalElements / this.conveniosData.size)
		);
		this.conveniosData.first = page === 0 || page === 1;
		this.conveniosData.last = page === this.conveniosData.totalPages;
		this.conveniosData.content = this.integracao.configuracao.convenios.slice(
			size * page - size,
			size * page
		);
		if (this.tableDataComponent) {
			if (reloadData) {
				this.tableDataComponent.reloadData();
			}
			this.tableDataComponent.ngbPage = this.page;
		}
	}

	ordenarItens(eventSort) {
		this.integracao.configuracao.convenios = sortList(
			this.integracao.configuracao.convenios,
			eventSort.columnName,
			eventSort.direction
		);
		this.createPageObject();
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createPageObject(this.page, this.size, true);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createPageObject(this.page, this.size, true);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	closeModal() {
		this.activeModal.dismiss();
	}

	possuiConvenios(): boolean {
		return this.integracao.configuracao
			? this.integracao.configuracao.convenios.length > 0
			: false;
	}

	getFormSituacao(convenioCobranca: ConvenioCobranca) {
		const index = this.formsSituacao.findIndex(
			(f) => f.id === convenioCobranca.codigo
		);
		if (index === -1) {
			const form = new FormControl();
			form.setValue(valueToBoolean(convenioCobranca.situacao));
			this.formsSituacao.push({ id: convenioCobranca.codigo, form });
			form.valueChanges.subscribe((v) => {
				convenioCobranca.situacao = v ? 1 : 0;
				this.alterSituacaoConvenio(convenioCobranca);
			});
			return form;
		} else {
			return this.formsSituacao[index].form;
		}
	}

	alterSituacaoConvenio(convenio: ConvenioCobranca) {
		this.msPactopayApiConvenioCobrancaService
			.alterarSituacao(convenio)
			.subscribe(
				() => {
					this.notify.success(
						this.traducao.getLabel("situacao-convenio-alterado-com-sucesso")
					);
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notify.error(err.meta.messageValue);
					} else if (err.meta && err.meta.message) {
						this.notify.error(
							"Falha ao tentar alterar situação convênio! " + err.meta.message
						);
					}
				}
			);
	}

	getMerchantId(): string {
		if (this.possuiConvenios()) {
			return this.integracao.configuracao.convenios[0].codigoAutenticacao01;
		} else {
			return "";
		}
	}

	getAmbiente(): number {
		if (this.possuiConvenios()) {
			return this.integracao.configuracao.convenios[0].ambiente;
		} else {
			return 0;
		}
	}

	getTipoIntegracao(): number {
		// 1 = PagoLivre
		// 2 = FacilitePay
		if (this.integracao.id === "FACILITEPAY") {
			return 2;
		} else {
			return 1;
		}
	}

	getNomeApresentar(): string {
		// 1 = PagoLivre
		// 2 = FacilitePay
		if (this.getTipoIntegracao() === 2) {
			return "FacilitePay";
		} else {
			return "PagoLivre";
		}
	}
}
