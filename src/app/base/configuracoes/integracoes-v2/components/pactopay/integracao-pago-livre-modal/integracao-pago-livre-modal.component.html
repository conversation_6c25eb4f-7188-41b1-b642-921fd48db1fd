<div class="main-content">
	<pacto-integracao-pagolivre-form-cadastro
		(merchantCadastradoEvent)="closeModal()"
		*ngIf="!possuiConvenios()"
		[ambiente]="getAmbiente()"
		[empresa]="integracao.configuracao.empresa"
		[merchantId]="getMerchantId()"
		[tipoIntegracao]="
			getTipoIntegracao()
		"></pacto-integracao-pagolivre-form-cadastro>

	<pacto-cat-tabs-transparent
		#tabsMenus
		*ngIf="possuiConvenios()"
		[tabIndex]="0"
		id="menus-modulos">
		<ng-template label="Cadastro" pactoTabTransparent>
			<div style="margin-top: 15px">
				<pacto-integracao-pagolivre-form-cadastro
					(merchantCadastradoEvent)="closeModal()"
					[ambiente]="getAmbiente()"
					[empresa]="integracao.configuracao.empresa"
					[merchantId]="getMerchantId()"
					[tipoIntegracao]="
						getTipoIntegracao()
					"></pacto-integracao-pagolivre-form-cadastro>
			</div>
		</ng-template>

		<ng-template label="Convênio Cobrança" pactoTabTransparent>
			<div class="col-md-5 mr-auto" style="margin-top: 15px">
				<h3>Convênios de Cobrança {{ getNomeApresentar() }}</h3>
			</div>
			<div id="table-convenios">
				<pacto-relatorio
					#tableDataComponent
					(pageChangeEvent)="pageChangeEvent($event)"
					(pageSizeChange)="pageSizeChange($event)"
					(sortEvent)="ordenarItens($event)"
					[showShare]="false"
					[table]="tableDataGridConfig"></pacto-relatorio>
			</div>
		</ng-template>
	</pacto-cat-tabs-transparent>
</div>

<ng-template #columnCodigo>
	<span i18n="@@integracao-pagolivre:column-codigo">Cód.</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@integracao-pagolivre:column-descricao">Descrição</span>
</ng-template>
<ng-template #columnMerchantId>
	<span i18n="@@integracao-pagolivre:column-merchantid">MerchantId</span>
</ng-template>
<ng-template #columnSituacao>
	<span i18n="@@integracao-pagolivre:column-situacao">Situação</span>
</ng-template>

<ng-template #situacao let-convenioCobranca="item">
	<pacto-cat-switch
		[control]="getFormSituacao(convenioCobranca)"
		type="PRIMARY"></pacto-cat-switch>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@integracoes:situacao-convenio-alterado-com-sucesso"
		xingling="situacao-convenio-alterado-com-sucesso">
		Situação alterada com sucesso!
	</span>
</pacto-traducoes-xingling>
