import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import { isNullOrUndefinedOrEmpty } from "sdk";
import { TraducoesXinglingComponent } from "ui-kit";

import {
	Empresa,
	MsPactopayApiIntegracoesService,
	PagoLivreMerchant,
} from "ms-pactopay-api";
import {
	CadastroAuxApiContaCorrenteService,
	ContaCorrente,
} from "cadastro-aux-api";

@Component({
	selector: "pacto-integracao-pagolivre-form-cadastro",
	templateUrl: "./integracao-pagolivre-form-cadastro.component.html",
	styleUrls: ["./integracao-pagolivre-form-cadastro.component.scss"],
})
export class IntegracaoPagolivreFormCadastroComponent implements OnInit {
	@Input() empresa: Empresa;
	@Input() merchantId: string;
	@Input() ambiente: number;
	@Input() tipoIntegracao: number;
	@Input() pagoLivreMerchant: PagoLivreMerchant;

	@Output() merchantCadastradoEvent: EventEmitter<any> = new EventEmitter();

	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	formContaCorrente = new FormControl("");
	formGroup: FormGroup = new FormGroup({
		ambiente: new FormControl(),
		nomeEmpresa: new FormControl(),
		cnpj: new FormControl(),
		email: new FormControl(),
		ddd: new FormControl(),
		telefone: new FormControl(),
		emailUser: new FormControl(),
		nomeUser: new FormControl(),
	});
	ambientes: Array<{ codigo: number; descricao: string }> = [
		{ codigo: 1, descricao: "PRODUÇÃO" },
		{ codigo: 2, descricao: "HOMOLOGAÇÃO / SANDBOX" },
	];
	optionsContas = new Array<any>();
	contas = Array<ContaCorrente>();
	logindingMerchant = true;

	constructor(
		private cadastroAuxApiContaCorrenteService: CadastroAuxApiContaCorrenteService,
		private cd: ChangeDetectorRef,
		private notify: SnotifyService,
		private msPactopayApiIntegracoesService: MsPactopayApiIntegracoesService
	) {}

	ngOnInit() {
		this.initOptionContas();
		if (
			!isNullOrUndefinedOrEmpty(this.merchantId) &&
			!isNullOrUndefinedOrEmpty(this.ambiente) &&
			!isNullOrUndefinedOrEmpty(this.tipoIntegracao) &&
			isNullOrUndefinedOrEmpty(this.pagoLivreMerchant)
		) {
			this.msPactopayApiIntegracoesService
				.consultarMerchantPagoLivre(
					this.merchantId,
					this.ambiente,
					this.tipoIntegracao
				)
				.subscribe(
					(response) => {
						this.pagoLivreMerchant = response.content;
						this.logindingMerchant = false;
						this.atualizarForm();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								"Falha ao consultar merchant! " + err.meta.message
							);
						}
					}
				);
		} else {
			this.logindingMerchant = false;
			this.initForm();
		}
	}

	initOptionContas() {
		if (isNullOrUndefinedOrEmpty(this.empresa)) {
			this.notify.error("Falha ao carregar dados da empresa");
			return;
		}
		setTimeout(() => {
			this.optionsContas = new Array<any>();
			this.cadastroAuxApiContaCorrenteService
				.findAll({ codigoEmpresa: this.empresa.codigo })
				.subscribe(
					(contas) => {
						contas.forEach((cc) => {
							this.optionsContas.push({
								codigo: cc.codigo,
								descricao: `AG: ${cc.agencia}-${cc.agenciaDv} CC: ${cc.contaCorrente}-${cc.contaCorrenteDv}`,
							});
							this.contas.push(cc);
						});
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								"Falha ao consultar conta corrente! " + err.meta.message
							);
						}
					}
				);
		});
	}

	initForm() {
		this.formGroup.patchValue({
			ambiente: undefined,
			nomeEmpresa: this.empresa.nome,
			cnpj: this.empresa.cnpj,
			email: "",
			ddd: "",
			telefone: "",
			emailUser: "",
			nomeUser: "",
		});
		this.formContaCorrente.setValue(
			this.optionsContas.length > 0 ? this.optionsContas[0].codigo : undefined
		);
	}

	cadastrarMerchant() {
		const pagoLivreNovaMerchant = new PagoLivreMerchant();
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			pagoLivreNovaMerchant[key] = this.formGroup.getRawValue()[key];
		});
		pagoLivreNovaMerchant.contaCorrente = this.getContaCorrenteSelecionada();
		pagoLivreNovaMerchant.empresa = this.empresa;
		pagoLivreNovaMerchant.tipoIntegracao = this.tipoIntegracao;
		if (this.validarCampos(pagoLivreNovaMerchant)) {
			this.msPactopayApiIntegracoesService
				.cadastrarMerchantPagoLivre(pagoLivreNovaMerchant)
				.subscribe(
					(response) => {
						this.notify.success(response.content);
						this.merchantCadastradoEvent.emit();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								this.traducao.getLabel("falha-realizar-integracao") +
									err.meta.message
							);
						}
					}
				);
		}
	}

	salvarAlteracoes() {
		const pagoLivreMerchant = new PagoLivreMerchant();
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			pagoLivreMerchant[key] = this.formGroup.getRawValue()[key];
		});
		pagoLivreMerchant.contaCorrente = this.getContaCorrenteSelecionada();
		pagoLivreMerchant.empresa = this.empresa;
		pagoLivreMerchant.tipoIntegracao = this.tipoIntegracao;
		if (this.validarCampos(pagoLivreMerchant)) {
			this.msPactopayApiIntegracoesService
				.salvarAlteracoes(this.merchantId, pagoLivreMerchant)
				.subscribe(
					(response) => {
						this.notify.success("Dados alterados com sucesso.");
						this.merchantCadastradoEvent.emit();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						} else if (err.meta && err.meta.message) {
							this.notify.error(
								this.traducao.getLabel("falha-salvar-alteracoes") +
									err.meta.message
							);
						}
					}
				);
		}
	}

	getContaCorrenteSelecionada() {
		const codigo = this.formContaCorrente.value;
		if (isNullOrUndefinedOrEmpty(codigo)) {
			return undefined;
		}
		for (const cc of this.contas) {
			if (cc.codigo === parseInt(codigo, 10)) {
				return cc;
			}
		}
	}

	validarCampos(pagoLivreMerchant: PagoLivreMerchant) {
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.ambiente)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-ambiente")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.tipoIntegracao)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-tipoIntegracao")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.nomeEmpresa)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-nomeempresa")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.cnpj)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-cnpj")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.email)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-email")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.emailUser)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-emailuser")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.ddd)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-ddd")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.telefone)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-telefone")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.nomeUser)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-nomeuser")
			);
			return false;
		}
		if (isNullOrUndefinedOrEmpty(pagoLivreMerchant.contaCorrente)) {
			this.notify.error(
				this.traducao.getLabel("integracao-pagolivre-validacao-conta")
			);
			return false;
		}
		return true;
	}

	isCadastrar() {
		return isNullOrUndefinedOrEmpty(this.merchantId);
	}

	atualizarForm() {
		this.formGroup.patchValue({
			ambiente: this.pagoLivreMerchant.ambiente,
			tipoIntegracao: this.pagoLivreMerchant.tipoIntegracao,
			nomeEmpresa: this.pagoLivreMerchant.nomeEmpresa,
			cnpj: this.pagoLivreMerchant.cnpj,
			email: this.pagoLivreMerchant.email,
			ddd: this.pagoLivreMerchant.ddd,
			telefone: this.pagoLivreMerchant.telefone,
			emailUser: this.pagoLivreMerchant.emailUser,
			nomeUser: this.pagoLivreMerchant.nomeUser,
		});
		if (this.pagoLivreMerchant.contaCorrente) {
			this.formContaCorrente.setValue(
				this.pagoLivreMerchant.contaCorrente.codigo
			);
		}
		this.formGroup.get("emailUser").disable();
		this.formGroup.get("nomeUser").disable();
		this.formGroup.get("cnpj").disable();
		this.cd.detectChanges();
	}

	getNomeApresentar(): string {
		// 1 = PagoLivre
		// 2 = Fypay
		if (this.tipoIntegracao === 2) {
			return "Fypay";
		} else {
			return "PagoLivre";
		}
	}
}
