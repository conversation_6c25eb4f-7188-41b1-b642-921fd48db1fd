import {
	ChangeDetectorRef,
	Component,
	Injectable,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { HttpClient } from "@angular/common/http";
import { ClientDiscoveryService } from "sdk";
import { SessionService } from "@base-core/client/session.service";
import { ZwServletApiPluggyService } from "zw-servlet-api";
import { RestService } from "@base-core/rest/rest.service";
import { EmpresaFinanceiro } from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { IntegracaoPluggyQrcodeModalComponent } from "./integracao-pluggy-qrcode-modal.component";
import { MatDialog } from "@angular/material/dialog";

@Injectable({
	providedIn: "root",
})
@Component({
	selector: "integracao-pluggy-modal",
	templateUrl: "./integracao-pluggy-modal.component.html",
	styleUrls: ["./integracao-pluggy-modal.component.scss"],
})
export class IntegracaoPluggyModalComponent implements OnInit {
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipAtualizar", { static: true }) tooltipAtualizar;
	@ViewChild("tooltipQrCode", { static: true }) tooltipQrCode;
	@ViewChild("xinglingRemoverContaConectada", { static: true })
	xinglingRemoverContaConectada: TraducoesXinglingComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("imageCelula", { static: true }) imageCelula;
	@ViewChild("tableContasConectadas", { static: false })
	tableContasConectadas: RelatorioComponent;
	@Input() integracao: Integracao;
	@Input() empresaSelecionada: EmpresaFinanceiro;
	inicializado = false;

	formGroup: FormGroup = new FormGroup({});

	table: PactoDataGridConfig;
	itensPerPage = [{ id: 10, label: 10 }];

	pluggyConnect: any; // Variável de instância para armazenar o widget Pluggy
	scriptCarregado: boolean = false; // Flag para garantir que o script foi carregado

	constructor(
		private zwServletApiPluggyService: ZwServletApiPluggyService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private session: SessionService,
		private readonly rest: RestService,
		private pactoModal: ModalService,
		private dialog: MatDialog
	) {}

	ngOnInit() {
		this.configTable();
		this.carregarJSPluggyConnectWidget();
	}

	// não remover o ondestroy, ele é usado sim
	ngOnDestroy() {
		this.removeScript(); // Garante que o script seja removido quando o componente for destruído
		this.destroyWidget(); // Garante que o widget seja destruído quando o componente for destruído
	}

	carregarJSPluggyConnectWidget() {
		if (!this.inicializado) {
			const script = document.createElement("script");
			script.src =
				"https://cdn.pluggy.ai/pluggy-connect/v2.6.1/pluggy-connect.js";
			script.onload = () => {
				this.scriptCarregado = true;
				this.inicializado = true;
			};
			script.onerror = () => {
				console.error("Erro ao carregar o script do PluggyConnect.");
			};
			document.body.appendChild(script);
		} else {
			this.scriptCarregado = true;
		}
	}

	destroyWidget() {
		if (this.pluggyConnect) {
			try {
				this.pluggyConnect.destroy(); // Método para destruir o widget, se disponível
			} catch (e) {
				console.error("Erro ao destruir o widget PluggyConnect:", e);
			}
			this.pluggyConnect = null;
		}
	}

	removeScript() {
		const script = document.querySelector(
			'script[src="https://cdn.pluggy.ai/pluggy-connect/v2.6.1/pluggy-connect.js"]'
		);
		if (script) {
			document.body.removeChild(script);
		}
	}

	createOrUpdateConnector(updatingItem?: boolean, idItem?: string) {
		let qtdLimiteContasAtingida: boolean = false;

		if (!this.scriptCarregado) {
			console.error("Script PluggyConnect ainda não foi carregado.");
			return;
		}

		// Limpar qualquer instância anterior e garantir que não há conflitos
		this.destroyWidget();

		// Verifica se o limite de contas de conciliação conectadas para a empresa selecionada não foi excedido
		this.zwServletApiPluggyService
			.validarQtdLmtContasConectadas(
				this.session.chave,
				this.empresaSelecionada.empresazw.toString()
			)
			.subscribe({
				next: (response) => {
					qtdLimiteContasAtingida = response.content;

					if (qtdLimiteContasAtingida && !updatingItem) {
						this.notificationService.error(
							"Quantidade limite de Contas de Conciliação foi atingida! Não é possível cadastrar novas contas."
						);
						return;
					}
				},
			});

		// Obter o token do backend
		this.zwServletApiPluggyService
			.obterTokenIntegracaoPluggy(this.session.chave, updatingItem, idItem)
			.subscribe({
				next: (response) => {
					const token = JSON.parse(response.content).accessToken;

					// Garantir que o widget seja criado corretamente
					// @ts-ignore
					this.pluggyConnect = new PluggyConnect({
						updateItem: updatingItem ? idItem : null,
						connectToken: token,
						includeSandbox: true,
						allowConnectInBackground: false,
						onSuccess: (itemData) => {
							console.log("Yay! Pluggy connect success!", itemData);
							// Gravar os dados no zw
							this.zwServletApiPluggyService
								.updateOrCreateConnector(
									this.session.chave,
									itemData.item,
									this.empresaSelecionada.empresazw.toString(),
									updatingItem
								)
								.subscribe({
									next: () => {
										this.notificationService.success(
											updatingItem
												? "Conexão atualizada com sucesso!"
												: "Conta conectada com sucesso!"
										);
										this.tableContasConectadas.reloadData();
										this.cd.detectChanges();
									},
									error: (error) => {
										this.notificationService.error(
											"Não foi possível gravar as informações no zw! Entre em contato com a Pacto"
										);
									},
									complete: () => {
										// Fechar modal somente após sucesso na gravação
										this.pluggyConnect.hide();
										this.destroyWidget(); // Limpar widget após uso
									},
								});
						},
						onError: (error) => {
							console.log(error);
							if (error.data.item.error.code === "USER_AUTHORIZATION_PENDING") {
								this.zwServletApiPluggyService
									.updateOrCreateConnector(
										this.session.chave,
										error.data.item,
										this.empresaSelecionada.empresazw.toString(),
										updatingItem
									)
									.subscribe({
										next: () => {
											this.notificationService.success(
												"Agora você precisa autorizar o dispositivo!"
											);
											this.tableContasConectadas.reloadData();
											this.cd.detectChanges();
										},
										complete: () => {
											// Fechar modal somente após tentar autorização
											this.pluggyConnect.hide();
											this.destroyWidget(); // Limpar widget após uso
										},
									});
							} else if (
								error.data.item.error.code === "ACCOUNT_NEEDS_ACTION"
							) {
								this.notificationService.error(
									"A Conta precisa de uma ação: " +
										error.data.item.error.message
								);
								// Não fecha o modal aqui
							} else {
								this.notificationService.error(
									updatingItem
										? "Não foi possível atualizar a conta!"
										: "Não foi possível conectar a conta!"
								);
								this.notificationService.error(
									"Não foi possível conectar a conta!"
								);
								console.error("Whoops! Pluggy Connect error... ", error);
								// Não fecha o modal aqui
							}
						},
						onClose: () => {
							console.log("Modal fechado");
						},
					});
					if (!qtdLimiteContasAtingida || updatingItem) {
						this.pluggyConnect.init(); // Inicia o widget PluggyConnect
					}
				},
				error: (error) => {
					this.notificationService.error(
						"Não foi possível obter o token de acesso!"
					);
					console.log(error);
				},
			});
	}

	private configTable() {
		const tooltipInativar = this.tooltipRemover.nativeElement.innerHTML;
		const tooltipAtualizar = this.tooltipAtualizar.nativeElement.innerHTML;
		const tooltipQrCode = this.tooltipQrCode.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlZw(
				`integracao/pluggy?key=${this.session.chave}&empresa=${this.empresaSelecionada.empresazw}&op=obterContasConectadas`
			),
			quickSearch: true,
			columns: [
				{
					nome: "imageUrl",
					titulo: "",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "imageUrl",
					celula: this.imageCelula,
				},
				{
					nome: "id",
					titulo: "ID",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "idItem",
				},
				{
					nome: "banco",
					titulo: "Banco",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "name",
				},
				{
					nome: "createdAt",
					titulo: "Criado em",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "createdAtApresentar",
				},
				{
					nome: "updatedAt",
					titulo: "Últ. Atualização",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "updatedAtApresentar",
				},
				{
					nome: "status",
					titulo: "Status",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					campo: "statusApresentar",
				},
			],
			actions: [
				{
					nome: "delete",
					iconClass: "pct pct-trash-2 cor-laranjinha05",
					tooltipText: tooltipInativar,
				},
				{
					nome: "update",
					iconClass: "pct pct-refresh-ccw",
					tooltipText: tooltipAtualizar,
				},
				{
					nome: "qrcode",
					iconClass: "pct pct-qr-code",
					tooltipText: tooltipQrCode,
					showIconFn: (row) =>
						row.name.toLowerCase().includes("caixa") &&
						row.statusApresentar
							.toUpperCase()
							.includes("PENDENTE DE AUTORIZAÇÃO NO APP"),
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "delete") {
			this.inativarItem($event.row);
		} else if ($event.iconName === "update") {
			this.createOrUpdateConnector(true, $event.row.idItem);
		} else if ($event.iconName === "qrcode") {
			this.abrirModalQrCode();
		}
	}

	inativarItem(row) {
		const xingling = this.xinglingRemoverContaConectada;
		const modal = this.pactoModal.confirm(
			xingling.getLabel("titulo"),
			xingling.getLabel("body")
		);
		modal.result.then(() => {
			this.zwServletApiPluggyService
				.inativarContaConectada(this.session.chave, null, row.idItem)
				.subscribe({
					next: () => {
						this.notificationService.success("Conta excluída com sucesso!");
						this.tableContasConectadas.reloadData();
						this.cd.detectChanges();
					},
					error: (error) => {
						this.notificationService.error(error.error.meta.message);
					},
				});
		});
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	abrirModalQrCode(): void {
		const url = this.rest.buildFullUrlZw(
			`integracao/pluggy?key=${this.session.chave}&empresa=${this.empresaSelecionada.empresazw}&op=obterContasConectadas`
		);

		const dialogRef = this.dialog.open(IntegracaoPluggyQrcodeModalComponent, {
			width: "320px",
			height: "420px",
			data: {
				url: url,
			},
		});

		// Aguarda o fechamento do modal para atualizar a tabela
		dialogRef.afterClosed().subscribe(() => {
			this.tableContasConectadas.reloadData();
		});
	}
}
