import { HttpClient } from "@angular/common/http";
import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
	selector: "integracao-pluggy-qrcode-modal",
	templateUrl: "./integracao-pluggy-qrcode-modal.component.html",
	styleUrls: ["./integracao-pluggy-qrcode-modal.component.scss"],
})
export class IntegracaoPluggyQrcodeModalComponent implements OnInit {
	qrCodes: string[] = [];
	imgSrc = "";
	intervalo: any;
	indexAtual = 0;

	constructor(
		public dialogRef: MatDialogRef<IntegracaoPluggyQrcodeModalComponent>,
		@Inject(MAT_DIALOG_DATA) public data: { url: string },
		private http: HttpClient
	) {}

	ngOnInit(): void {
		if (this.data.url) {
			this.http.get<any>(this.data.url).subscribe((res) => {
				const qrCodesBrutos = res.content[0].error.qrCodeCaixa;

				if (Array.isArray(qrCodesBrutos) && qrCodesBrutos.length > 0) {
					this.qrCodes = qrCodesBrutos
						.filter((q) => q && q.trim())
						.map((q) => q.trim());

					this.imgSrc = "data:image/png;base64," + this.qrCodes[0];
					this.iniciarRotacao();
				} else {
					this.imgSrc = "";
				}
			});
		}
	}

	ngOnDestroy() {
		if (this.intervalo) {
			clearInterval(this.intervalo);
		}
	}

	iniciarRotacao() {
		this.intervalo = setInterval(() => {
			this.indexAtual = (this.indexAtual + 1) % this.qrCodes.length;
			this.imgSrc = "data:image/png;base64," + this.qrCodes[this.indexAtual];
		}, 5000);
	}

	fechar(): void {
		this.dialogRef.close();
	}
}
