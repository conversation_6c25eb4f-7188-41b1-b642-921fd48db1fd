import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TraducoesXinglingComponent } from "ui-kit";

import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";

@Component({
	selector: "pacto-integracao-sistema-contabil-alterdata-modal",
	templateUrl: "./integracao-sistema-contabil-alterdata-modal.component.html",
	styleUrls: ["./integracao-sistema-contabil-alterdata-modal.component.scss"],
})
export class IntegracaoSistemaContabilAlterdataModalComponent
	implements OnInit
{
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;

	constructor(
		private admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService
	) {}

	ngOnInit() {}

	salvarIntegracao(acao?: string) {
		if (acao === "inativar") {
			this.integracao.configuracao.habilitarExportacaoAlterData = false;
			this.salvar("inativada-com-sucesso");
		} else {
			this.integracao.configuracao.habilitarExportacaoAlterData = true;
			this.salvar("ativada-com-sucesso");
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoSistemaContabilAlterData(
				this.integracao.configuracao
			)
			.subscribe(
				() => {
					this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notify.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
