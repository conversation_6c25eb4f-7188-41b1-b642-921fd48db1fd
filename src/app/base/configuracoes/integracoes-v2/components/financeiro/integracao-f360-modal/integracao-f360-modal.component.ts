import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";

@Component({
	selector: "pacto-integracao-f360-modal",
	templateUrl: "./integracao-f360-modal.component.html",
	styleUrls: ["./integracao-f360-modal.component.scss"],
})
export class IntegracaoF360ModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;

	formGroup: FormGroup = new FormGroup({
		ftpServer: new FormControl("", Validators.required),
		ftpPort: new FormControl("", Validators.required),
		user: new FormControl("", Validators.required),
		password: new FormControl("", Validators.required),
		dir: new FormControl("", Validators.required),
		quinzenal: new FormControl(false),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.formGroup.patchValue({
			ftpServer: this.integracao.configuracao.ftpServer,
			ftpPort: this.integracao.configuracao.ftpPort,
			user: this.integracao.configuracao.user,
			password: "**********", // Campo fict�cio somente para visualiza��o, a senha fica somente no backend e � uma senha padr�o
			dir: this.integracao.configuracao.dir,
			quinzenal: this.integracao.configuracao.quinzenal,
		});
		this.formGroup.get("ftpServer").disable();
		this.formGroup.get("ftpPort").disable();
		this.formGroup.get("user").disable();
		this.formGroup.get("password").disable();
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoF360(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
