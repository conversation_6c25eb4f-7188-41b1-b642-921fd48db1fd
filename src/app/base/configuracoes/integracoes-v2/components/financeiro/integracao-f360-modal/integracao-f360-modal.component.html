<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('ftpServer'))"
				i18n-label="@@integracao-f360:label-ftp-server"
				i18n-placeholder="@@integracao-f360:placeholder-ftp-server"
				id="ftp-server"
				label="FTP server"
				placeholder="Este campo não pode ser editado"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input-number
				[formControl]="toFormControl(formGroup.get('ftpPort'))"
				i18n-label="@@integracao-ftp-porta:label-ftp-porta"
				i18n-placeholder="@@integracao-f360:placeholder-ftp-porta"
				id="ftp-porta"
				label="FTP porta"
				maxlength="9"
				placeholder="Este campo não pode ser editado"></pacto-cat-form-input-number>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('user'))"
				i18n-label="@@integracao-f360:label-usuario"
				i18n-placeholder="@@integracao-f360:placeholder-usuario"
				id="usuario"
				label="Usuário"
				placeholder="Este campo não pode ser editado"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('password'))"
				i18n-label="@@integracao-ftp-porta:label-senha"
				i18n-placeholder="@@integracao-f360:placeholder-senha"
				id="senha"
				label="Senha"
				placeholder="Este campo não pode ser editado"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('dir'))"
				i18n-label="@@integracao-f360:label-ftp-diretorio"
				i18n-placeholder="@@integracao-f360:placeholder-ftp-diretorio"
				id="FTP diretório"
				label="FTP diretório"
				placeholder="Digite o FTP diretório, não esqueça de deixar o '/files/' no início. Ex: /files/NOME_DA_ACADEMIA. Se possui mais de uma empresa no mesmo banco de dados, NÃO é necessário criar um diretório para cada empresa, neste caso você pode configurar para que todas elas enviem para o mesmo diretório, pois o nosso sitema já criará pastas automaticamente para cada empresa. Configure por exemplo neste caso: /files/NOME_DO_GRUPO em todas as unidades, ou seja, a configuração fica igual em todas"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-checkbox
				[control]="toFormControl(formGroup.get('quinzenal'))"
				i18n-label="@@integracao-f360:label-quinzenal"
				id="quinzenal"
				label="Gerar relatório quinzenal"></pacto-cat-checkbox>
		</div>
	</div>
	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-mywellness:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
</pacto-traducoes-xingling>
