import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TraducoesXinglingComponent } from "ui-kit";
import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";

@Component({
	selector: "pacto-integracao-conciliadora-modal",
	templateUrl: "./integracao-conciliadora-modal.component.html",
	styleUrls: ["./integracao-conciliadora-modal.component.scss"],
})
export class IntegracaoConciliadoraModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: Integracao;

	formGroup: FormGroup = new FormGroup({
		empresaConciliadora: new FormControl("", Validators.required),
		senhaConciliadora: new FormControl("", Validators.required),
		periodoDe: new FormControl(),
		periodoAte: new FormControl(),
		codigoRecibo: new FormControl(0),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.formGroup.patchValue({
			empresaConciliadora: this.integracao.configuracao.empresaConciliadora,
			senhaConciliadora: this.integracao.configuracao.senhaConciliadora,
		});
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	salvarIntegracao(acao?: string) {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.integracao.configuracao[key] = this.formGroup.getRawValue()[key];
		});
		if (acao === "inativar") {
			this.integracao.configuracao.usarConciliadora = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.usarConciliadora = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoConciliadora(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	processarConciliadora() {
		const filtros = {
			codigoEmpresa: parseInt(this.integracao.configuracao.empresa.codigo, 10),
			codigoRecibo: parseInt(this.formGroup.get("codigoRecibo").value, 10),
			dataInicial: this.formGroup.get("periodoDe").value
				? new Date(this.formGroup.get("periodoDe").value).getTime()
				: 0,
			dataFinal: this.formGroup.get("periodoAte").value
				? new Date(this.formGroup.get("periodoAte").value).getTime()
				: 0,
			reprocessar: true,
			manual: false,
		};
		this.admCoreApiIntegracoes.processarConciliadora(filtros).subscribe(
			() => {
				this.notificationService.success(
					this.traducao.getLabel("processo-conciliadora-executado-com-sucesso")
				);
				this.cd.detectChanges();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	estornarReciboNaConciliadora() {
		this.admCoreApiIntegracoes
			.estornarReciboNaConciliadora(
				parseInt(this.formGroup.get("codigoRecibo").value, 10)
			)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(
							"processo-estorno-conciliadora-executado-com-sucesso"
						)
					);
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
