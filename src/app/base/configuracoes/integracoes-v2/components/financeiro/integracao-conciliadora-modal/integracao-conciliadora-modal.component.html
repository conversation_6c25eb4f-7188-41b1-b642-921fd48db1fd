<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('empresaConciliadora'))"
				errorMsg="Forneça o ID Empresa."
				i18n-errorMsg="@@integracao-conciliadora:error-msg-idempresa"
				i18n-label="@@integracao-conciliadora:label-id-empresa"
				i18n-placeholder="@@integracao-conciliadora:placeholder-id-empresa"
				id="id-empresa"
				label="ID Empresa"
				placeholder="Digite o ID da empresa"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 col-auto">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('senhaConciliadora'))"
				errorMsg="Forneça a senha."
				i18n-errorMsg="@@integracao-conciliadora:error-msg-senha"
				i18n-label="@@integracao-conciliadora:label-senha"
				i18n-placeholder="@@integracao-conciliadora:placeholder-senha"
				id="senha"
				label="Senha"
				placeholder="Digite a senha"></pacto-cat-form-input>
		</div>
	</div>

	<div *ngIf="this.integracao.ativa">
		<div class="row" id="title-reprocessar">
			<div class="col-md-12">
				<h3>Reprocessar Cociliadora</h3>
				<div class="line"></div>
			</div>
		</div>

		<div class="row">
			<div class="col-md-3 mr-auto">
				<h3>Período reprocessar:</h3>
			</div>
			<div class="col-md-4 col-auto">
				<pacto-cat-form-datepicker
					[control]="toFormControl(formGroup.get('periodoDe'))"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@integracao-conciliadora:error-msg-de"
					i18n-label="@@integracao-conciliadora:label-de"
					label="Dê"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="toFormControl(formGroup.get('periodoAte'))"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@integracao-conciliadora:error-msg-ate"
					i18n-label="@@integracao-conciliadora:label-ate"
					label="Até"></pacto-cat-form-datepicker>
			</div>
		</div>

		<div class="row">
			<div class="col-md-5 mr-auto">
				<pacto-cat-form-input-number
					[formControl]="toFormControl(formGroup.get('codigoRecibo'))"
					[maxlength]="'8'"
					i18n-label="@@integracao-conciliadora:label-recibo"
					id="recibo"
					label="Recibo"
					placeholder="0"></pacto-cat-form-input-number>
			</div>
		</div>

		<div class="row justify-content-md-start">
			<pacto-cat-button
				(click)="processarConciliadora()"
				i18n-label="@@integracao-conciliadora:label-btn-processar-conciliadora"
				id="btn-processar-conciliadora"
				label="Processar conciliadora"
				size="LARGE"
				style="margin-left: 15px"
				type="OUTLINE_GRAY"></pacto-cat-button>
			<pacto-cat-button
				(click)="estornarReciboNaConciliadora()"
				i18n-label="
					@@integracao-conciliadora:label-btn-estornar-recibo-conciliadora"
				id="btn-estornar-recibo-conciliadora"
				label="Estornar recibo na conciliadora"
				size="LARGE"
				style="margin-left: 15px"
				type="ALERT_DELETE"></pacto-cat-button>
		</div>
	</div>

	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracoes:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:processo-conciliadora-executado-com-sucesso"
		xingling="processo-conciliadora-executado-com-sucesso">
		Processo conciliadora executado com sucesso!
	</span>
	<span
		i18n="@@integracoes:processo-estorno-conciliadora-executado-com-sucesso"
		xingling="processo-estorno-conciliadora-executado-com-sucesso">
		Processo estorno conciliadora executado com sucesso!
	</span>
</pacto-traducoes-xingling>
