import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmCoreApiIntegracoesService } from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-integracao-sesc-df-modal",
	templateUrl: "./integracao-sesc-df-modal.component.html",
	styleUrls: ["./integracao-sesc-df-modal.component.scss"],
})
export class IntegracaoSescDfModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	@Input() empresaSelecionada: any;
	token: FormControl = new FormControl();

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.token.setValue(this.integracao.configuracao.token);
	}

	salvarIntegracao() {
		const isAtivo = this.token.value !== "";
		this.integracao.configuracao.usarSescDf = isAtivo;

		if (isAtivo) {
			this.salvar("salva-com-sucesso");
		} else {
			this.token.setValue(null);
			this.salvar("inativada-com-sucesso");
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.integracao.configuracao.token = this.token.value;

		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoSescDf(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
