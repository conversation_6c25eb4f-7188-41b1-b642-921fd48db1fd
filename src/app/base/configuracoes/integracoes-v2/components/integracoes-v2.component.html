<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'Configurações',
			menu: 'Integrações'
		}"
		class="day"></pacto-breadcrumbs>
	<div id="main-container">
		<pacto-cat-tabs-transparent
			#tabsMenus
			(action)="openModalAlterarEmpresa()"
			(activateTab)="tabClickHandler($event)"
			[actionIcon]="'pct pct-refresh-ccw'"
			[actionId]="'btn-alterar-empresa'"
			[actionLabel]="'Alterar empresa'"
			[showAction]="possuiVariasUnidades()"
			[tabIndex]="3"
			id="menus-modulos">
			<ng-template
				label="ADM"
				pactoTabTransparent="{{ Modulo.ADM }}"
				tabImage="./assets/images/logo-adm-whitebg.svg"></ng-template>
			<ng-template
				label="CRM"
				pactoTabTransparent="{{ Modulo.CRM }}"
				tabImage="./assets/images/genteCRM.png"></ng-template>
			<ng-template
				label="FIN"
				pactoTabTransparent="{{ Modulo.FINANCEIRO }}"
				tabImage="./assets/images/genteFIN.png"></ng-template>
			<ng-template
				label="TREINO"
				pactoTabTransparent="{{ Modulo.TREINO }}"
				tabImage="./assets/images/logo-treino-whitebg.svg"></ng-template>
			<ng-template
				label="PACTO PAY"
				pactoTabTransparent="{{ Modulo.PACTO_PAY }}"
				tabImage="./assets/images/logo-pactopay-whitebg.svg"></ng-template>
			<ng-template
				*ngIf="isApresentarFacilitePay()"
				label="FYPAY"
				pactoTabTransparent="{{ Modulo.FACILITE_PAY }}"
				tabImage="./assets/images/logo-icon-facilitepay.png"></ng-template>
			<ng-template
				*ngIf="isApresentarSistemaSesc()"
				label="SISTEMA S"
				pactoTabTransparent="{{ Modulo.SISTEMA_SESC }}"
				tabImage="./assets/images/logo-adm-whitebg.svg"></ng-template>
		</pacto-cat-tabs-transparent>
		<div class="cards-integracoes">
			<ng-container *ngFor="let integracao of integracoesModulo">
				<pacto-cat-card-plain
					*ngIf="getApresentar(integracao)"
					class="card-integracao"
					id="{{ integracao.id.toLowerCase() }}">
					<div (click)="openModalConfgIntegracao(integracao)" class="row">
						<div class="col-md-9 mr-auto">
							<p>{{ integracao.titulo }}</p>
						</div>
						<div
							*ngIf="
								!(integracoesEnum.GUPSHUP === integracao.id) &&
								getApresentarSituacao(integracao)
							"
							class="situacao col-auto">
							<span
								*ngIf="atualizandoDadosIntegracoes"
								class="carregando-situacao">
								-
							</span>
							<span
								*ngIf="
									!atualizandoDadosIntegracoes &&
									integracao.titulo !== 'Gympass1' &&
									!integracao.pendente
								"
								[ngClass]="
									integracao.ativa ? 'situacao-ativo' : 'situacao-inativo'
								">
								{{ integracao.ativa ? "ATIVA" : "INATIVA" }}
							</span>

							<span
								*ngIf="
									!atualizandoDadosIntegracoes &&
									integracao.titulo === 'Gympass1' &&
									integracao.pendente
								"
								[ngClass]="'situacao-pendente'">
								PENDENTE
							</span>
						</div>
					</div>
				</pacto-cat-card-plain>
			</ng-container>
		</div>
	</div>
</pacto-cat-layout-v2>
