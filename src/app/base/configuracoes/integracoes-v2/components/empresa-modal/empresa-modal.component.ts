import { Component, Input, OnInit } from "@angular/core";
import { AbstractControl, FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";

import { SelectFilterParamBuilder } from "ui-kit";
import { TreinoApiEmpresaService } from "treino-api";

@Component({
	selector: "pacto-empresa-modal",
	templateUrl: "./empresa-modal.component.html",
	styleUrls: ["./empresa-modal.component.scss"],
})
export class EmpresaModalComponent implements OnInit {
	@Input() empresaSelecionada;
	empresaForm: FormControl = new FormControl();
	empresasTreino = new Array();

	constructor(
		private activeModal: NgbActiveModal,
		public sessionService: SessionService,
		private treinoApiEmpresaService: TreinoApiEmpresaService
	) {}

	ngOnInit() {
		this.empresaForm.setValue(this.empresaSelecionada);
		this.treinoApiEmpresaService.obterTodasEmpresas().subscribe((response) => {
			this.empresasTreino = response;
		});
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quickSearchValue: term,
			}),
		};
	};

	selecionarEmpresa() {
		this.activeModal.close(this.empresaForm.value);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close(this.empresaForm.value);
		}
	}
}
