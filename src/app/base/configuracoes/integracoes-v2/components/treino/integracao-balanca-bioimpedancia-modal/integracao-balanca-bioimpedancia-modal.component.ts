import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { switchMap } from "rxjs/operators";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import { TreinoApiConfiguracoesTreinoService } from "treino-api";

@Component({
	selector: "pacto-integracao-balanca-bioimpedancia-modal",
	templateUrl: "./integracao-balanca-bioimpedancia-modal.component.html",
	styleUrls: ["./integracao-balanca-bioimpedancia-modal.component.scss"],
})
export class IntegracaoBalancaBioimpedanciaModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	configuracoesIntegracao: any;
	tipoBalanca: FormControl = new FormControl();
	tiposBalancas = [{ codigo: 1, descricao: "Tanita BC 1000" }];

	constructor(
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.configuracoesIntegracao = this.configCache.configuracoesIntegracoes;
		this.tipoBalanca.setValue({ codigo: 1, descricao: "Tanita BC 1000" });
		this.tipoBalanca.disable();
		this.cd.detectChanges();
	}

	ativarInativarIntegracao() {
		let save$;
		this.integracao.ativa = !this.integracao.ativa;
		this.configuracoesIntegracao.usar_integracao_bioimpedancia =
			this.integracao.ativa;
		save$ = this.configTreinoService.updateConfiguracoesIntegracoes(
			this.configuracoesIntegracao
		);
		const update$ = this.configCache.loadTreinoConfigIntegracoes();
		return save$.pipe(switchMap(() => update$)).subscribe(() => {
			if (this.integracao.ativa) {
				this.notify.success(this.traducao.getLabel("ativada-com-sucesso"));
			} else {
				this.notify.success(this.traducao.getLabel("inativada-com-sucesso"));
			}
			this.activeModal.close();
		});
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
