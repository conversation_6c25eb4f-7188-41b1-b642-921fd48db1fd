import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";

import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import { AdmCoreApiIntegracoesService } from "adm-core-api";

@Component({
	selector: "pacto-integracao-spivi-modal",
	templateUrl: "./integracao-spivi-modal.component.html",
	styleUrls: ["./integracao-spivi-modal.component.scss"],
})
export class IntegracaoSpiviModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	listaConfiguracoesMgbTreino: any;
	formGroup: FormGroup = new FormGroup({
		sourceName: new FormControl(""),
		siteId: new FormControl(""),
		spiviPassword: new FormControl(""),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.formGroup.patchValue({
			sourceName: this.integracao.configuracao.sourceName,
			siteId: this.integracao.configuracao.siteId,
			spiviPassword: this.integracao.configuracao.spiviPassword,
		});
	}

	salvarIntegracao(acao?: string) {
		this.integracao.configuracao.sourceName =
			this.formGroup.get("sourceName").value;
		this.integracao.configuracao.siteId = this.formGroup.get("siteId").value;
		this.integracao.configuracao.spiviPassword =
			this.formGroup.get("spiviPassword").value;
		if (acao === "inativar") {
			this.integracao.configuracao.habilitada = false;
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.integracao.configuracao.habilitada = true;
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		let save$;
		save$ = this.admCoreApiIntegracoes.salvarConfiguracaoIntegracaoSpivi(
			this.integracao.configuracao
		);
		const update$ = this.configCache.loadTreinoConfigIntegracoes();
		return save$.pipe(switchMap(() => update$)).subscribe(
			() => {
				this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
				this.activeModal.close();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notify.error(err.meta.messageValue);
				}
			}
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
