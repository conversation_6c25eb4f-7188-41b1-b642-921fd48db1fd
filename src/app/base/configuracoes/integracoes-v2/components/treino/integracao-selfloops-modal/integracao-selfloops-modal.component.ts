import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";

import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import {
	ConfiguracoesIntegracoesListaSelfloops,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";
import { environment } from "../../../../../../../environments/environment";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-integracao-selfloops-modal",
	templateUrl: "./integracao-selfloops-modal.component.html",
	styleUrls: ["./integracao-selfloops-modal.component.scss"],
})
export class IntegracaoSelfloopsModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	listaConfiguracoesSelfloopsTreino: any;
	empresaSelfloops: FormControl = new FormControl();
	loading = false;

	constructor(
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.empresaSelfloops.setValue(
			this.integracao.configuracao.empresaSelfloops
		);
		if (
			this.integracao &&
			this.integracao.configuracao &&
			!this.integracao.configuracao.code
		) {
			this.configTreinoService
				.getConfiguracoesIntegracoesListaSelfloops()
				.subscribe((result) => {
					if (result && result.length > 0) {
						result.forEach((config) => {
							if (this.integracao.empresaId == config.empresa) {
								this.integracao.configuracao.code = config.code;
								this.integracao.configuracao.teams = config.teams;
							}
						});
						this.cd.detectChanges();
					}
				});
		}
	}

	autenticarIntegracao() {
		let urlApiOauthSelfloops = "https://stage.selfloops.com";
		if (environment.production) {
			urlApiOauthSelfloops = "https://selfloops.com/";
		}
		const context = "/oauth/authorize?";
		const param1 = "client_id=" + this.integracao.configuracao.pactoClientId;
		const param2 = "&response_type=code";
		const param3 =
			"&state=" + this.integracao.chave + "-" + this.integracao.empresaId;
		window.open(
			urlApiOauthSelfloops + context + param1 + param2 + param3,
			"_blank"
		);
		this.notify.success(this.traducao.getLabel("salva-com-sucesso"));
		this.activeModal.close();
	}

	salvarIntegracao(operacao: string) {
		if (operacao === "salvar") {
			this.integracao.configuracao.empresaSelfloops =
				this.empresaSelfloops.value;
			this.salvar();
		} else if (operacao === "inativar") {
			this.integracao.configuracao.empresaSelfloops = null;
			this.integracao.configuracao.code = null;
			this.salvar();
		}
	}

	salvar() {
		this.loading = true;
		this.listaConfiguracoesSelfloopsTreino =
			new Array<ConfiguracoesIntegracoesListaSelfloops>();
		this.listaConfiguracoesSelfloopsTreino.push(this.integracao.configuracao);
		let save$;
		save$ =
			this.configTreinoService.updateConfiguracoesIntegracoesListaSelfloops(
				this.listaConfiguracoesSelfloopsTreino
			);
		const update$ = this.configCache.loadTreinoConfigIntegracoes();
		return save$.pipe(switchMap(() => update$)).subscribe(
			() => {
				this.notify.success(this.traducao.getLabel("salva-com-sucesso"));
				this.loading = false;
				this.activeModal.close();
			},
			() => {
				this.notify.error(this.traducao.getLabel("falha-salvar-integracao"));
				this.loading = false;
			}
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
