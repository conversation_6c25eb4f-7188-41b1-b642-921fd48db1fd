import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";

import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import {
	ConfiguracoesIntegracoesListaMQV,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";

@Component({
	selector: "pacto-integracao-mqv-modal",
	templateUrl: "./integracao-mqv-modal.component.html",
	styleUrls: ["./integracao-mqv-modal.component.scss"],
})
export class IntegracaoMqvModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	listaConfiguracoesMqvTreino: any;
	token: FormControl = new FormControl();

	constructor(
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.token.setValue(this.integracao.configuracao.token);
	}

	salvarIntegracao(acao?: string) {
		if (acao === "inativar") {
			this.token.setValue("");
			this.salvar("inativada-com-sucesso");
		} else {
			if (acao === "ativar") {
				this.salvar("ativada-com-sucesso");
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.integracao.configuracao.token = this.token.value;

		this.listaConfiguracoesMqvTreino =
			new Array<ConfiguracoesIntegracoesListaMQV>();
		this.listaConfiguracoesMqvTreino.push(this.integracao.configuracao);
		let save$;
		save$ = this.configTreinoService.updateConfiguracoesIntegracoesListaMQV(
			this.listaConfiguracoesMqvTreino
		);
		const update$ = this.configCache.loadTreinoConfigIntegracoes();
		return save$.pipe(switchMap(() => update$)).subscribe(
			() => {
				this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
				this.activeModal.close();
			},
			() => {
				this.notify.error(this.traducao.getLabel("falha-salvar-integracao"));
			}
		);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
