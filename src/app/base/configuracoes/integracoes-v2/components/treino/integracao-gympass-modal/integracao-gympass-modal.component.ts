import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { debounceTime, switchMap } from "rxjs/operators";
import { IntegracaoGympassApiGymIdService } from "integracao-gympass-api";
import { SessionService } from "@base-core/client/session.service";
import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import {
	TreinoApiConfiguracoesTreinoService,
	ConfiguracoesIntegracoesLista,
	EmpresaFinanceiro,
} from "treino-api";
import { AdmCoreApiIntegracoesService } from "adm-core-api";
import { <PERSON>du<PERSON> } from "../../../classes/modulos.enum";

@Component({
	selector: "pacto-integracao-gympass-modal",
	templateUrl: "./integracao-gympass-modal.component.html",
	styleUrls: ["./integracao-gympass-modal.component.scss"],
})
export class IntegracaoGympassModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	@Input() empresaSelecionada: EmpresaFinanceiro;
	configuracoesGymPassTreinoList: any;
	selectedTab = "gympass";

	formGroup: FormGroup = new FormGroup({
		gymId: new FormControl(""),
		tokenApiGympass: new FormControl(""),
		codigoGympass: new FormControl(""),
		permitirWod: new FormControl(""),
		limiteDeAcessosPorDia: new FormControl(""),
		limiteDeAulasPorDia: new FormControl(""),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private integracaoGympassService: IntegracaoGympassApiGymIdService,
		private session: SessionService
	) {}

	ngOnInit() {
		this.initForms();
	}

	initForms() {
		this.formGroup.patchValue({
			gymId:
				this.integracao.configuracao.integracaoGymPassConfigTreino
					.codigoGymPass,
			limiteDeAcessosPorDia:
				this.integracao.configuracao.integracaoGymPassConfigTreino
					.limiteDeAcessosPorDia,
			permitirWod:
				this.integracao.configuracao.integracaoGymPassConfigTreino.permitirWod,
			limiteDeAulasPorDia:
				this.integracao.configuracao.integracaoGymPassConfigTreino
					.limiteDeAulasPorDia,
			codigoGympass:
				this.integracao.configuracao.integracaoGymPassConfigAdm.codigoGympass,
			tokenApiGympass:
				this.integracao.configuracao.integracaoGymPassConfigAdm.tokenApiGympass,
		});
	}

	tabClickHandler(event: { index: number; previous: string; next: string }) {
		this.selectedTab = event.index === 0 ? "gympass" : "pacto";
		this.cd.detectChanges();
	}

	salvar() {
		this.integracao.configuracao.integracaoGymPassConfigTreino.codigoGymPass =
			this.formGroup.get("gymId").value;
		this.integracao.configuracao.integracaoGymPassConfigTreino.limiteDeAcessosPorDia =
			this.formGroup.get("limiteDeAcessosPorDia").value;
		this.integracao.configuracao.integracaoGymPassConfigTreino.limiteDeAulasPorDia =
			this.formGroup.get("limiteDeAulasPorDia").value;
		this.integracao.configuracao.integracaoGymPassConfigTreino.permitirWod =
			this.formGroup.get("permitirWod").value;
		this.integracao.configuracao.integracaoGymPassConfigAdm.codigoGympass =
			this.formGroup.get("gymId").value;
		this.integracao.configuracao.integracaoGymPassConfigAdm.limiteDeAcessosPorDia =
			this.formGroup.get("limiteDeAcessosPorDia").value;
		this.integracao.configuracao.integracaoGymPassConfigAdm.limiteDeAulasPorDia =
			this.formGroup.get("limiteDeAulasPorDia").value;
		this.configuracoesGymPassTreinoList =
			new Array<ConfiguracoesIntegracoesLista>();
		this.configuracoesGymPassTreinoList.push(
			this.integracao.configuracao.integracaoGymPassConfigTreino
		);
		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoGympass(
				this.integracao.configuracao.integracaoGymPassConfigAdm
			)
			.subscribe(() => {
				let saveConfgGympassTreino$;
				saveConfgGympassTreino$ =
					this.configTreinoService.updateConfiguracoesIntegracoesLista(
						this.configuracoesGymPassTreinoList
					);
				const update$ = this.configCache.loadTreinoConfigIntegracoes();
				return saveConfgGympassTreino$
					.pipe(switchMap(() => update$))
					.subscribe(() => {
						const labelMensagemSucesso = "salva-com-sucesso";
						this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
						this.salvarConfigsMSGympass(
							this.configuracoesGymPassTreinoList,
							labelMensagemSucesso
						);
						this.activeModal.close();
					});
			});
	}

	private salvarConfigsMSGympass(dto, labelMensagemSucesso) {
		// salva o gymid no banco do treino e depois no banco do ms integracoes-gympass
		this.integracaoGympassService
			.gravarGymIdTodasEmpresas(this.montarListGymDTO(dto))
			.subscribe({
				error: (error) => {
					this.notify.error(error.error.meta.message);
				},
				next: () => {
					this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
				},
			});
	}

	private montarListGymDTO(dto) {
		const gymDTOs = [];
		for (const item of dto) {
			gymDTOs.push({
				chave: this.session.chave,
				gymid: item.codigoGymPass,
				empresa: item.empresa,
				nome: item.nome,
			});
		}
		return gymDTOs;
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
