<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				id="token-academy-id"
				i18n-label="@@integracao-gympass:label-gymid"
				label="Token Academia"
				i18n-placeholder="@@integracao-tokenacademy:placeholder-tokenacademy"
				placeholder="Digite o Token da Academia"
				[control]="formGroup.get('tokenAcademy')"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 mr-auto"></div>
	</div>

	<div class="row justify-content-end btns-ativar-inativar">
		<pacto-cat-button
			id="btn-salvar"
			size="LARGE"
			type="PRIMARY"
			i18n-label="@@integracao-parceiro-fidelidade:label-btn-excluir-tabela"
			label="Salvar alterações"
			style="margin-right: 15px; margin-left: 17px"
			(click)="salvar()"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span xingling="salva-com-sucesso" i18n="@@integracoes:salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		xingling="inativada-com-sucesso"
		i18n="@@integracoes:inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span xingling="ativada-com-sucesso" i18n="@@integracoes:ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		xingling="gogood-id-nao-informado"
		i18n="@@integracoes:gogood-id-nao-informado">
		Para utilizar a Gogood, o campo "Token Academia" deve ser informado!
	</span>
	<span
		xingling="falha-consulta-configs-gogood-adm"
		i18n="@@integracoes:falha-consulta-configs-gogood-adm">
		Falha ao tentar consultar as configurações da Gogood!
	</span>
	<span
		xingling="falha-salvar-configs-gogood"
		i18n="@@integracoes:falha-salvar-configs-gogood">
		Falha ao salvar Gogood!
	</span>
	<span
		xingling="nenhuma-configuracao-informada-para-ativar"
		i18n="@@integracoes:nenhuma-configuracao-informada-para-ativar">
		O campo "Token Academia" deve ser informado!
	</span>
</pacto-traducoes-xingling>
