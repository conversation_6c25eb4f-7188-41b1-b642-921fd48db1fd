import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";
import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import {
	ConfiguracoesIntegracoesListaGoGood,
	EmpresaFinanceiro,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";
import { AdmCoreApiIntegracoesService } from "adm-core-api";

@Component({
	selector: "pacto-integracao-gogood-modal",
	templateUrl: "./integracao-gogood-modal.component.html",
	styleUrls: ["./integracao-gogood-modal.component.scss"],
})
export class IntegracaoGogoodModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	@Input() empresaSelecionada: EmpresaFinanceiro;
	configuracoesGoGoodTreinoList: any;

	formGroup: FormGroup = new FormGroup({
		tokenAcademy: new FormControl(""),
	});

	constructor(
		private configCache: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService
	) {}

	ngOnInit() {
		this.initForms();
	}

	initForms() {
		this.formGroup.patchValue({
			tokenAcademy:
				this.integracao.configuracao.integracaoGoGoodConfigTreino
					.tokenAcademyGoGood,
		});
	}

	salvar() {
		this.integracao.configuracao.integracaoGoGoodConfigTreino.tokenAcademyGoGood =
			this.formGroup.get("tokenAcademy").value;
		this.integracao.configuracao.integracaoGoGoodConfigAdm.tokenAcademyGoGood =
			this.formGroup.get("tokenAcademy").value;
		this.configuracoesGoGoodTreinoList =
			new Array<ConfiguracoesIntegracoesListaGoGood>();
		this.configuracoesGoGoodTreinoList.push(
			this.integracao.configuracao.integracaoGoGoodConfigTreino
		);
		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoGoGood(
				this.integracao.configuracao.integracaoGoGoodConfigAdm
			)
			.subscribe(() => {
				let saveConfgGympassTreino$;
				saveConfgGympassTreino$ =
					this.configTreinoService.updateConfiguracoesIntegracoesListaGoGood(
						this.configuracoesGoGoodTreinoList
					);
				const update$ = this.configCache.loadTreinoConfigIntegracoes();
				return saveConfgGympassTreino$
					.pipe(switchMap(() => update$))
					.subscribe(() => {
						const labelMensagemSucesso = "salva-com-sucesso";
						this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
						this.activeModal.close();
					});
			});
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}
}
