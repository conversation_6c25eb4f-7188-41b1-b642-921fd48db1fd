import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	FormControl,
	FormGroup,
	Validators,
	FormsModule,
	ReactiveFormsModule,
	FormBuilder,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";
import { TraducoesXinglingComponent } from "ui-kit";

import { TreinoConfigCacheService } from "../../../../configuration.service";
import {
	ConfiguracoesIntegracoesListaTotalPass,
	TreinoApiConfiguracoesTreinoService,
} from "treino-api";
import { AdmCoreApiIntegracoesService } from "adm-core-api";

@Component({
	selector: "pacto-integracao-totalpass-modal",
	templateUrl: "./integracao-totalpass-modal.component.html",
	styleUrls: ["./integracao-totalpass-modal.component.scss"],
})
export class IntegracaoTotalpassModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() integracao: any;
	configuracoesTotalPassTreinoList: any;
	isChecked: boolean = false;
	token: FormControl = new FormControl();
	formGroup: FormGroup = new FormGroup({
		apiKey: new FormControl(""),
		codigoTotalPass: new FormControl(""),
		limiteDeAcessosPorDia: new FormControl(""),
		limiteDeAulasPorDia: new FormControl(""),
		ativo: new FormControl(""),
		permitirWod: new FormControl(""),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService
	) {}

	ngOnInit() {
		this.isChecked =
			!this.integracao.configuracao.integracaoTotalPassConfigAdm.inativo;
		this.formGroup.patchValue({
			limiteDeAcessosPorDia:
				this.integracao.configuracao.integracaoTotalPassConfigAdm
					.limiteDeAcessosPorDia,
			limiteDeAulasPorDia:
				this.integracao.configuracao.integracaoTotalPassConfigAdm
					.limiteDeAulasPorDia,
			codigoTotalPass:
				this.integracao.configuracao.integracaoTotalPassConfigAdm
					.codigoTotalpass,
			apiKey: this.integracao.configuracao.integracaoTotalPassConfigAdm.apiKey,
			permitirWod:
				this.integracao.configuracao.integracaoTotalPassConfigAdm.permitirWod,
		});
	}

	salvarIntegracao(acao?: string) {
		if (acao === "inativar") {
			this.formGroup.get("codigoTotalPass").setValue("");
			this.formGroup.get("apiKey").setValue("");
			this.salvar("inativada-com-sucesso");
		} else if (this.validarFormularios()) {
			if (acao === "ativar") {
				if (this.validarFormulariosAtivacao()) {
					this.salvar("ativada-com-sucesso");
				}
			} else {
				this.salvar("salva-com-sucesso");
			}
		}
	}

	salvar(labelMensagemSucesso?: string) {
		this.integracao.configuracao.integracaoTotalPassConfigAdm.permitirWod =
			this.formGroup.get("permitirWod").value;
		this.integracao.configuracao.integracaoTotalPassConfigAdm.inativo =
			!this.isChecked;
		this.integracao.configuracao.integracaoTotalPassConfigAdm.codigoTotalpass =
			this.formGroup.get("codigoTotalPass").value;
		this.integracao.configuracao.integracaoTotalPassConfigAdm.apiKey =
			this.formGroup.get("apiKey").value;
		this.integracao.configuracao.integracaoTotalPassConfigAdm.limiteDeAcessosPorDia =
			this.formGroup.get("limiteDeAcessosPorDia").value;
		this.integracao.configuracao.integracaoTotalPassConfigAdm.limiteDeAulasPorDia =
			this.formGroup.get("limiteDeAulasPorDia").value;
		console.log(this.integracao.configuracao.integracaoTotalPassConfigAdm);
		this.configuracoesTotalPassTreinoList =
			new Array<ConfiguracoesIntegracoesListaTotalPass>();
		this.configuracoesTotalPassTreinoList.push(
			this.integracao.configuracao.integracaoTotalPassConfig
		);

		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoTotalpass(
				this.integracao.configuracao.integracaoTotalPassConfigAdm
			)
			.subscribe(() => {
				this.notify.success(this.traducao.getLabel(labelMensagemSucesso));
				this.activeModal.close();
			});
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	tabClickHandler($event: { index: number; previous: string; next: string }) {}

	possuiVariasUnidades() {}

	openModalAlterarEmpresa() {}

	activateTabHandler($event: {
		index: number;
		previous: string;
		next: string;
	}) {}

	validarFormularios(): boolean {
		if (
			this.formGroup.get("apiKey").value !== "" &&
			this.formGroup.get("codigoTotalPass").value === ""
		) {
			this.notify.warning(
				this.traducao.getLabel("codigo-totalpass-nao-informado")
			);
			return false;
		}
		if (
			this.formGroup.get("codigoTotalPass").value !== "" &&
			this.formGroup.get("apiKey").value === ""
		) {
			this.notify.warning(
				this.traducao.getLabel("token-api-totalpass-nao-informado")
			);
			return false;
		}
		return true;
	}

	validarFormulariosAtivacao(): boolean {
		if (
			this.formGroup.get("codigoTotalPass").value === "" &&
			this.formGroup.get("apiKey").value === ""
		) {
			this.notify.warning(
				this.traducao.getLabel("nenhuma-configuracao-informada-para-ativar")
			);
			return false;
		}
		return true;
	}
}
