<div class="main-content">
	<div class="row">
		<div class="tabs-totalpass">
			<pacto-cat-tabs-transparent
				#tabs
				(activateTab)="activateTabHandler($event)"
				class="tabs-totalpass">
				<ng-template
					label="Configuração TotalPass"
					pactoTabTransparent="confTotalPass">
					<div class="row">
						<div class="col-md">
							<pacto-cat-form-input
								[control]="formGroup.get('apiKey')"
								i18n-label="@@integracao-totapass:label-apikey"
								i18n-placeholder="@@integracao-totalpass:placeholder-apikey"
								label="API KEY"
								placeholder="-"></pacto-cat-form-input>
						</div>
						<div class="col-md">
							<pacto-cat-form-input
								[control]="formGroup.get('codigoTotalPass')"
								i18n-label="@@integracao-totapass:label-codigototalpass"
								i18n-placeholder="
									@@integracao-totalpass:placeholder-codigototalpass"
								label="Código"
								placeholder="-"></pacto-cat-form-input>
						</div>
					</div>
					<div class="row">
						<div class="col-md">
							<mat-slide-toggle [(ngModel)]="isChecked">
								Ativar
							</mat-slide-toggle>
						</div>
					</div>
				</ng-template>
				<ng-template label="Configuração Pacto" pactoTabTransparent="confPacto">
					<div class="warning">
						<mat-icon class="warning-icon"></mat-icon>
						<p class="highlight-text">
							As configurações dessa aba serão utilizadas apenas dentro do
							sistema Pacto.
						</p>
					</div>

					<div class="row">
						<div class="col-md">
							<pacto-cat-form-input-number
								[formControl]="formGroup.get('limiteDeAcessosPorDia')"
								label="Limite de acessos por dia"
								placeholder="0"></pacto-cat-form-input-number>
						</div>
						<div class="col-md">
							<pacto-cat-form-input-number
								[formControl]="formGroup.get('limiteDeAulasPorDia')"
								label="Limite de aulas por dia"
								placeholder="0"></pacto-cat-form-input-number>
						</div>
					</div>
					<div class="form-check form-check-inline">
						<pacto-cat-checkbox
							[control]="formGroup.get('permitirWod')"></pacto-cat-checkbox>
						<label class="form-check-label" style="color: #51555a">
							Permitir TotalPass executar o WOD
						</label>
					</div>
				</ng-template>
			</pacto-cat-tabs-transparent>
		</div>
	</div>
	<div class="row justify-content-end btns-ativar-inativar">
		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-totalpass:label-btn-excluir-tabela"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px; margin-left: 17px"
			type="OUTLINE_GRAY"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:token-api-totalpass-nao-informado"
		xingling="token-api-totalpass-nao-informado">
		O campo "Código TotalPass" foi preenchido, então o campo "Token API Total
		Pass" também deve ser informado!
	</span>
	<span
		i18n="@@integracoes:codigo-totalpass-nao-informado"
		xingling="codigo-totalpass-nao-informado">
		O campo "Token API TotalPass" foi preenchido, então o campo "Código
		TotalPass" também deve ser informado!
	</span>
	<span
		i18n="@@integracoes:falha-salvar-configs-gympass"
		xingling="falha-salvar-configs-gympass">
		Falha ao salvar Total Pass!
	</span>
	<span
		i18n="@@integracoes:nenhuma-configuracao-informada-para-ativar"
		xingling="nenhuma-configuracao-informada-para-ativar">
		O campo "Token API Total Pass" e "Código Total Pass" devem ser informados!
	</span>
</pacto-traducoes-xingling>
