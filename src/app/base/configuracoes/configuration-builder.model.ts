import { FormControl } from "@angular/forms";
import { TemplateRef } from "@angular/core";
import { ConfigItemType } from "treino-api";

export type ConfigItem =
	| ConfigItemBase
	| ConfigItemCheckbox
	| ConfigItemRadio
	| ConfigItemSelect;

export interface ConfigItemBase {
	name: string;
	titleKey: string;
	descriptionKey?: string;
	translator: TemplateRef<any>;
	formControl?: FormControl;
	type: ConfigItemType;
	mask?: Array<any>[];
	childItems?: Array<any>;
}

export interface ConfigItemCheckbox extends ConfigItemBase {
	checkbox: true;
}

export interface ConfigItemInput extends ConfigItemBase {
	// https://github.com/text-mask/text-mask/blob/master/componentDocumentation.md#readme
	mask: any;
}

export interface ConfigItemRadio extends ConfigItemBase {
	options: ConfigItemRadioOption[];
}

export interface ConfigItemSelect extends ConfigItemBase {
	options: ConfigItemSelectOption[];
}

export interface ConfigItemButton extends ConfigItemBase {
	typeDescription: string;
}

/**
 * OPTIONS
 */
export interface ConfigItemSelectOption {
	value: any;
	label: string;
}

export interface ConfigItemRadioOption {
	name: string;
	value: any;
}
