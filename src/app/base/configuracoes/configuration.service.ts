import { Injectable } from "@angular/core";
import {
	ConfiguracoesAplicativos,
	ConfiguracoesAulas,
	ConfiguracoesAvaliacao,
	ConfiguracoesEmpresa,
	ConfiguracoesGerais,
	ConfiguracoesGestao,
	ConfiguracoesIntegracoes,
	ConfiguracoesIntegracoesLista,
	ConfiguracoesIntegracoesListaGoGood,
	ConfiguracoesIntegracoesListaMGB,
	ConfiguracoesIntegracoesListaMQV,
	ConfiguracoesIntegracoesListaTotalPass,
	ConfiguracoesIntegracoesListaSelfloops,
	ConfiguracoesNotificacao,
	ConfiguracoesTreino,
	TreinoApiConfiguracoesTreinoService,
	ConfiguracoesIa,
} from "treino-api";

import { Observable, of, zip } from "rxjs";
import { tap } from "rxjs/operators";
import { PlataformaModulo } from "src/app/microservices/client-discovery/client-discovery.model";
import { SessionService } from "@base-core/client/session.service";
import { ConfiguracoesManutencao } from "../../../../projects/treino-api/src/lib/configuracoes-treino.model";

@Injectable({
	providedIn: "root",
})
export class TreinoConfigCacheService {
	configuracoesGerais: ConfiguracoesGerais = {};
	configuracoesAula: ConfiguracoesAulas = {};
	configuracoesAplicativo: ConfiguracoesAplicativos = {};
	configuracoesGestao: ConfiguracoesGestao = {};
	configuracoesTreino: ConfiguracoesTreino = {};
	configuracoesAvaliacao: ConfiguracoesAvaliacao = {};
	configuracoesIntegracoes: ConfiguracoesIntegracoes = {};
	configuracoesIntegracoesLista: ConfiguracoesIntegracoesLista = {};
	configuracoesEmpresa: ConfiguracoesEmpresa = {};
	configuracoesNotificacao: ConfiguracoesNotificacao = {};
	configuracoesIntegracoesListaMGB: ConfiguracoesIntegracoesListaMGB = {};
	configuracoesIntegracoesListaMQV: ConfiguracoesIntegracoesListaMQV = {};
	configuracoesIntegracoesListaGoGood: ConfiguracoesIntegracoesListaGoGood = {};
	configuracoesIntegracoesListaTotalPass: ConfiguracoesIntegracoesListaTotalPass =
		{};
	configuracoesIntegracoesListaSelfloops: ConfiguracoesIntegracoesListaSelfloops =
		{};
	configuracoesManutencao: ConfiguracoesManutencao = {};
	configuracoesIa: ConfiguracoesIa = {};

	temp;

	constructor(
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private sessionService: SessionService
	) {}

	loadTreinoConfigIntegracoes(): Observable<any> {
		if (this.sessionService.isModuloHabilitado(PlataformaModulo["ZW"])) {
			return zip(
				this.configTreinoService.getConfiguracoesIntegracoes(),
				this.configTreinoService.getConfiguracoesIntegracoesLista(),
				this.configTreinoService.getConfiguracoesIntegracoesListaMGB(),
				this.configTreinoService.getConfiguracoesIntegracoesListaGoGood(),
				this.configTreinoService.getConfiguracoesIntegracoesListaTotalPass(),
				this.configTreinoService.getConfiguracoesIntegracoesListaSelfloops()
			).pipe(
				tap((result) => {
					this.configuracoesIntegracoes = result[0];
					this.configuracoesIntegracoesLista = result[1];
					this.configuracoesIntegracoesListaMGB = result[2];
					this.configuracoesIntegracoesListaGoGood = result[3];
					this.configuracoesIntegracoesListaTotalPass = result[4];
					this.configuracoesIntegracoesListaSelfloops = result[5];
				})
			);
		}
	}

	loadTreinoConfigCache(): Observable<any> {
		if (!this.sessionService.isModuloHabilitado(PlataformaModulo["ZW"])) {
			return zip(
				this.configTreinoService.getConfiguracoesAula(),
				this.configTreinoService.getConfiguracoesAplicativo(),
				this.configTreinoService.getConfiguracoesGestao(),
				this.configTreinoService.getConfiguracoesTreino(),
				this.configTreinoService.getConfiguracoesAvaliacao(),
				this.configTreinoService.getConfiguracoesIntegracoes(),
				this.configTreinoService.getConfiguracoesEmpresa(),
				this.configTreinoService.getConfiguracoesNotificacao(),
				this.configTreinoService.getConfiguracoesIntegracoesListaMQV(),
				this.configTreinoService.getConfiguracoesManutencao(),
				this.configTreinoService.getConfiguracoesIa()
			).pipe(
				tap((result) => {
					this.configuracoesAula = result[0];
					this.configuracoesAplicativo = result[1];
					this.configuracoesGestao = result[2];
					this.configuracoesTreino = result[3];
					this.configuracoesAvaliacao = result[4];
					this.configuracoesIntegracoes = result[5];
					this.configuracoesEmpresa = result[6];
					this.configuracoesNotificacao = result[7];
					this.configuracoesIntegracoesListaMQV = result[8];
					this.configuracoesManutencao = result[9];
					this.configuracoesIa = result[10];
				})
			);
		} else {
			return zip(
				this.configTreinoService.getConfiguracoesGerais(),
				this.configTreinoService.getConfiguracoesAula(),
				this.configTreinoService.getConfiguracoesAplicativo(),
				this.configTreinoService.getConfiguracoesGestao(),
				this.configTreinoService.getConfiguracoesTreino(),
				this.configTreinoService.getConfiguracoesAvaliacao(),
				this.configTreinoService.getConfiguracoesNotificacao(),
				this.configTreinoService.getConfiguracoesIntegracoes(),
				this.configTreinoService.getConfiguracoesIntegracoesLista(),
				this.configTreinoService.getConfiguracoesIntegracoesListaMGB(),
				this.configTreinoService.getConfiguracoesIntegracoesListaMQV(),
				this.configTreinoService.getConfiguracoesManutencao(),
				this.configTreinoService.getConfiguracoesIntegracoesListaGoGood(),
				this.configTreinoService.getConfiguracoesIntegracoesListaTotalPass(),
				this.configTreinoService.getConfiguracoesIntegracoesListaSelfloops(),
				this.configTreinoService.getConfiguracoesIa()
			).pipe(
				tap((result) => {
					this.configuracoesGerais = result[0];
					this.configuracoesAula = result[1];
					this.configuracoesAplicativo = result[2];
					this.configuracoesGestao = result[3];
					this.configuracoesTreino = result[4];
					this.configuracoesAvaliacao = result[5];
					this.configuracoesNotificacao = result[6];
					this.configuracoesIntegracoes = result[7];
					this.configuracoesIntegracoesLista = result[8];
					this.configuracoesIntegracoesListaMGB = result[9];
					this.configuracoesIntegracoesListaMQV = result[10];
					this.configuracoesManutencao = result[11];
					this.configuracoesIntegracoesListaGoGood = result[12];
					this.configuracoesIntegracoesListaTotalPass = result[13];
					this.configuracoesIntegracoesListaSelfloops = result[14];
					this.configuracoesIa = result[15];
				})
			);
		}
	}

	getConfiguracoesGerais$(): Observable<ConfiguracoesGerais> {
		return this.cache("configuracoesGerais", () =>
			this.configTreinoService.getConfiguracoesGerais()
		);
	}

	getConfiguracoesAula$(): Observable<ConfiguracoesAulas> {
		return this.cache("configuracoesAula", () =>
			this.configTreinoService.getConfiguracoesAula()
		);
	}

	getConfiguracoesAplicativo$(): Observable<ConfiguracoesAplicativos> {
		return this.cache("configuracoesAplicativo", () =>
			this.configTreinoService.getConfiguracoesAplicativo()
		);
	}

	getConfiguracoesGestao$(): Observable<ConfiguracoesGestao> {
		return this.cache("configuracoesGestao", () =>
			this.configTreinoService.getConfiguracoesGestao()
		);
	}

	getConfiguracoesTreino$(): Observable<ConfiguracoesTreino> {
		return this.cache("configuracoesTreino", () =>
			this.configTreinoService.getConfiguracoesTreino()
		);
	}

	getConfiguracoesAvaliacao$(): Observable<ConfiguracoesAvaliacao> {
		return this.cache("configuracoesAvaliacao", () =>
			this.configTreinoService.getConfiguracoesAvaliacao()
		);
	}

	getConfiguracoesIntegracoes$(): Observable<ConfiguracoesIntegracoes> {
		return this.cache("configuracoesIntegracoes", () =>
			this.configTreinoService.getConfiguracoesIntegracoes()
		);
	}

	getConfiguracoesManutencao$(): Observable<ConfiguracoesManutencao> {
		return this.cache("ConfiguracoesManutencao", () =>
			this.configTreinoService.getConfiguracoesManutencao()
		);
	}

	getConfiguracoesIa$(): Observable<ConfiguracoesIa> {
		console.log(this.configTreinoService.getConfiguracoesIa());
		return this.cache("configuracoesIa", () =>
			this.configTreinoService.getConfiguracoesIa()
		);
	}

	private cache<T>(name: string, getter$: () => Observable<T>): Observable<T> {
		if (this[name]) {
			return of(this[name]);
		} else {
			return getter$().pipe(
				tap((data) => {
					if (data) {
						this[name] = data;
					}
				})
			);
		}
	}
}
