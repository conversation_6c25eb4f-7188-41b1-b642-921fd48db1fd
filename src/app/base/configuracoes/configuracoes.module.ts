import { IntegracoesModule } from "./integracoes/integracoes.module";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { ConfiguracoesRootComponent } from "./components/configuracoes-root/configuracoes-root.component";
import { ConfiguracoesEmpresaComponent } from "./components/configuracoes-empresa/configuracoes-empresa.component";
import { ConfiguracoesNotificacaoComponent } from "./components/configuracoes-notificacao/configuracoes-notificacao.component";
import { ConfiguracoesGuard } from "@base-core/guards/configuracoes.guard";
import { IntegracoesV2Module } from "./integracoes-v2/integracoes-v2.module";
import { ConfiguracoesExampleComponent } from "./components/configuracoes-example/configuracoes-example.component";
import { ModuleFilterPipe } from "./components/configuracoes-root/module-filter.pipe";

const routes: Routes = [
	{
		path: "",
		component: ConfiguracoesRootComponent,
		// loadChildren: () => import('./components/configuracoes-root/configuracoes-root.component').then(m => m.ConfiguracoesRootComponent),
		canActivate: [ConfiguracoesGuard],
		children: [
			{
				path: "exampleTab",
				component: ConfiguracoesExampleComponent,
			},
		],
	},
];

@NgModule({
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		IntegracoesModule,
		IntegracoesV2Module,
		BaseSharedModule,
	],
	declarations: [
		ConfiguracoesRootComponent,
		ConfiguracoesEmpresaComponent,
		ConfiguracoesNotificacaoComponent,
		ConfiguracoesExampleComponent,
		ModuleFilterPipe,
	],
})
export class ConfiguracoesModule {}
