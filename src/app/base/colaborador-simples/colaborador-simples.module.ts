import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { ColaboradorSimplesEditComponent } from "./components/colaborador-simples-edit/colaborador-simples-edit.component";
import { ColaboradorSimplesListaComponent } from "./components/colaborador-simples-lista/colaborador-simples-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { AlterarEmailUsuarioComponent } from "./components/alterar-email-usuario/alterar-email-usuario.component";

const recursos = new PerfilAcessoRecurso(PerfilAcessoRecursoNome.USUARIOS, [
	PerfilRecursoPermissoTipo.CONSULTAR,
	PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
	PerfilRecursoPermissoTipo.TOTAL,
	PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
]);

const routes: Routes = [
	{
		path: "",
		component: ColaboradorSimplesListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: recursos,
		},
	},
	{
		path: "user/:id",
		component: ColaboradorSimplesEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: recursos,
		},
	},
	{
		path: "user/adicionar",
		component: ColaboradorSimplesEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: recursos,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), BaseSharedModule, CommonModule],
	declarations: [
		ColaboradorSimplesEditComponent,
		ColaboradorSimplesListaComponent,
		AlterarEmailUsuarioComponent,
	],
	entryComponents: [AlterarEmailUsuarioComponent],
})
export class ColaboradorSimplesModule {}
