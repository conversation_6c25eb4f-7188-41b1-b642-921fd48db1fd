<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'COLABORADOR'
		}"></pacto-breadcrumbs>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[tableDescription]="subTitulo"
			[tableTitle]="titulo"
			[table]="table"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--titulo table-->
<ng-template #titulo>
	<span i18n="@@colaborador-simples:usuario-cadastrado:title">
		Usuários Cadastrados
	</span>
</ng-template>
<ng-template #subTitulo>
	<span i18n="@@colaborador-simples:usuario-cadastrado:description">
		Gere<PERSON>ie os usuários cadastrados.
	</span>
</ng-template>
<!--end titulo table-->
<!--table columns-->
<ng-template #columnNome>
	<span i18n="@@colaborador-simples:table:nome">Nome</span>
</ng-template>
<ng-template #columnUsuario>
	<span i18n="@@colaborador-simples:table:nome-usuario">Nome de usuário</span>
</ng-template>
<!--end table columns-->
<!--button create-->
<ng-template #buttonAdicionar>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--button create end-->

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@colaborador-simples:tooltip-editar" xingling="tooltipEditar">
		Editar
	</span>
</pacto-traducoes-xingling>
