import { Component, OnInit, ViewChild } from "@angular/core";

import {
	ApiRequestQueries,
	ApiResponseList,
	SortDirection,
} from "@base-core/rest/rest.model";
import { UsuarioColaboradorList } from "treino-api";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoRecursoNome } from "treino-api";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-colaborador-simples-lista",
	templateUrl: "./colaborador-simples-lista.component.html",
	styleUrls: ["./colaborador-simples-lista.component.scss"],
})
export class ColaboradorSimplesListaComponent implements OnInit {
	@ViewChild("columnNome", { static: true }) columnNome;
	@ViewChild("columnUsuario", { static: true }) columnUsuario;
	@ViewChild("buttonAdicionar", { static: true }) buttonAdicionar;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	data: ApiResponseList<UsuarioColaboradorList> = {
		content: [],
	};
	queries: ApiRequestQueries = new ApiRequestQueries({
		sortField: "nome",
		sortDirection: SortDirection.ASC,
		pageSize: 30,
		currentPage: 0,
	});

	loading = false;
	table: PactoDataGridConfig;
	integracaoZW = false;
	permissoesUsuario;

	constructor(
		private router: Router,
		private rest: RestService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.carregarPermissoes();
		this.configTable();
	}

	btnClickHandler() {
		this.router.navigate(["colaboradores", "user", "adicionar"]);
	}

	btnEditHandler(item) {
		this.router.navigate(["colaboradores", "user", item.id]);
	}

	private addButtonTable() {
		let result = null;

		if (!this.integracaoZW && this.permissoesUsuario.incluir) {
			result = {
				conteudo: this.buttonAdicionar,
				nome: "add",
			};
		}
		return result;
	}

	private addIconTable() {
		const result = [];

		if (this.permissoesUsuario.editar) {
			result.push({
				nome: "edit",
				iconClass: "fa fa-pencil",
				tooltipText: this.traducoes.getLabel("tooltipEditar"),
			});
		}
		return result;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("usuarios/colaboradores"),
			logUrl: this.rest.buildFullUrl("log/colaboradoresUsiario"),
			quickSearch: true,
			buttons: this.addButtonTable(),
			rowClick: this.permissoesUsuario.editar,
			columns: [
				{
					nome: "nome",
					titulo: this.columnNome,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "userName",
					titulo: this.columnUsuario,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "appUserName",
				},
			],
			actions: this.addIconTable(),
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
	}

	private carregarPermissoes() {
		this.permissoesUsuario = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.USUARIOS
		);
	}
}
