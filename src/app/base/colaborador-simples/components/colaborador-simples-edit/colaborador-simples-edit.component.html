<ng-template #cardTitle>
	<span *ngIf="entity" i18n="@@colaborador-simples:criar-usuario:title">
		Criar <PERSON>
	</span>
	<span *ngIf="!entity" i18n="@@colaborador-simples:editar-usuario:title">
		Editar Usuário
	</span>
</ng-template>
<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'HOME',
			menu: 'COLABORADOR'
		}"></pacto-breadcrumbs>
	<pacto-title-card [title]="cardTitle">
		<div class="row">
			<div class="col-md-6">
				<div class="row">
					<div class="col-md-12">
						<div class="position-image">
							<pacto-seletor-imagem-user
								#seletorImagem
								[disabled]="integracaoZW"
								[nome]="'imagem'"></pacto-seletor-imagem-user>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<pacto-input
							[control]="formGroup.get('appUserName')"
							[disabled]="integracaoZW"
							[id]="'appUserName'"
							[name]="'appUserName'"
							i18n-label="@@colaborador-simples:usuario:label"
							i18n-mensagem="@@colaborador-simples:usuario:mensagem"
							label="Usuário"
							mensagem="Definir um nome de usuário, que não comece com número, e que tenha pelo menos 3 caracteres."></pacto-input>
					</div>
				</div>
			</div>
			<div class="col-md-6">
				<div class="row">
					<div class="col-md-12">
						<h1 *ngIf="!entity">{{ nomeColaborador }}</h1>
						<h4 *ngIf="!entity && usuarioColaborador.empresas">
							<b>Unidades:</b>
							{{ usuarioColaborador.empresas }}
						</h4>
						<label *ngIf="entity" class="control-label">Colaborador</label>
						<pacto-cat-select-filter
							*ngIf="!integracaoZW && selectColaborador"
							[control]="colaboradorControl"
							[id]="'pacto-select-professor'"
							[labelKey]="'nome'"
							[options]="colaboradores"
							[placeholder]="'Selecione o colaborador'"
							[size]="'SMALL'"
							message="Colaborador é obrigatório"></pacto-cat-select-filter>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<label
							class="control-label"
							i18n-label="@@colaborador-simples:perfil-usuario:label">
							Perfil do usuário
						</label>
						<pacto-select
							[control]="formGroup.get('perfilUsuarioPermissoes')"
							[nome]="'perfilUsuario'"
							[opcoes]="perfis"
							i18n-label="
								@@colaborador-simples:perfil-usuario:label"></pacto-select>
					</div>
				</div>
				<div class="row">
					<div
						*ngIf="
							!sessionService.verificarUsuarioPacto(
								formGroup.get('appUserName').value
							)
						"
						class="col-md-6">
						<pacto-input
							[control]="formGroup.get('email')"
							[disabled]="!entity"
							[id]="'email'"
							[name]="'email'"
							i18n-label="@@colaborador-simples:usuario:label"
							i18n-mensagem="@@colaborador-simples:usuario:mensagem"
							label="E-mail"
							mensagem="E-mail inválido!"></pacto-input>

						<button
							(click)="alterarEmail()"
							*ngIf="!entity && !integracaoZW"
							class="btn btn-outline-warning"
							i18n="@@buttons:alterar-email">
							{{
								formGroup.get("email").value
									? "Alterar e-mail"
									: "Informar e-mail"
							}}
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="actions">
			<button
				(click)="submitHandler()"
				class="btn btn-primary"
				i18n="@@buttons:salvar">
				Salvar
			</button>
			<button
				(click)="solicitarNovaSenha()"
				*ngIf="
					this.usuarioColaborador.emailVerificado &&
					this.formGroup.get('email').value &&
					!entity &&
					!integracaoZW
				"
				class="btn btn-outline-warning"
				i18n="@@buttons:solicitar-nova-senha">
				Solicitar Nova Senha
			</button>
			<button
				(click)="desvincularUsuario()"
				*ngIf="
					this.formGroup.get('email').value &&
					!entity &&
					!integracaoZW &&
					permissaoDesvincularUsuario
				"
				class="btn btn-outline-warning"
				i18n="@@buttons:desvincular-usuario">
				Desvincular usuário
			</button>
			<button
				(click)="cancelHandler()"
				class="btn btn-secondary"
				i18n="@@buttons:cancelar">
				Cancelar
			</button>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<ng-template #colaboradorTemplate let-item="item">
	<div class="user-item-wrapper">
		<div *ngIf="item.imageUri" class="image">
			<img src="{{ item.imageUri }}" />
		</div>
		<div class="name">
			{{ item.nome }}
		</div>
	</div>
</ng-template>

<span
	#editSuccess
	[hidden]="true"
	i18n="@@colaborador-simples:mensagem:edit-success">
	Usuário editado com sucesso.
</span>
<span
	#createSuccess
	[hidden]="true"
	i18n="@@colaborador-simples:mensagem:create-success">
	Usuário criado com sucesso. Acesse seu e-mail para definir sua senha!
</span>
<span
	#validCamposObrigatorios
	[hidden]="true"
	i18n="@@colaborador-simples:mensagem:valid-campos-obrigatorios">
	Campos obrigatórios não preenchidos.
</span>
<span
	#validNameUser
	[hidden]="true"
	i18n="@@crud-colaborador:mensagem:valid-name-user">
	Nome de usuário duplicado.
</span>

<ng-template
	#traducaoTipoUsuarios
	let-traducaoTipoUsuario="traducaoTipoUsuario">
	<ng-container [ngSwitch]="traducaoTipoUsuario">
		<span
			*ngSwitchCase="'CONSULTOR'"
			i18n="@@colaborador-simples:tipo-usuario:span-option-consultor">
			Consultor
		</span>
		<span
			*ngSwitchCase="'COORDENADOR'"
			i18n="@@colaborador-simples:tipo-usuario:span-option-coordenador">
			Coordenador
		</span>
		<span
			*ngSwitchCase="'PROFESSOR'"
			i18n="@@colaborador-simples:tipo-usuario:span-option-professor">
			Professor
		</span>
	</ng-container>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@colaborador-simples:email-recuperacao-senha"
		xingling="email-recuperacao-senha">
		Enviado link no seu e-mail para alterar sua senha!
	</span>
	<span
		i18n="@@colaborador-simples:email-recuperacao-enviado"
		xingling="email-recuperacao-enviado">
		E-mail para recuperação de senha enviado com sucesso!
	</span>
	<span
		i18n="@@colaborador-simples:usuario-desvinculado-com-sucesso"
		xingling="usuario-desvinculado-com-sucesso">
		Usuário desvinculado com sucesso!
	</span>
	<span
		i18n="@@colaborador-simples:confirmar-remocao-acesso"
		xingling="confirmar-remocao-acesso">
		Remover acesso do usuário à academia
	</span>
	<span
		i18n="@@colaborador-simples:usuario_sem_permissao"
		xingling="usuario_sem_permissao">
		Usuário sem permissão para a funcionalidade "Desvincular Usuário"
	</span>
	<span
		i18n="@@colaborador-simples:email-nao-verificado"
		xingling="email-nao-verificado">
		E-mail do usuário não foi verificado!
	</span>
</pacto-traducoes-xingling>

<ng-template #desvincularUsuarioConfirma>
	<div>
		<span>
			Confirmar remoção de acesso do usuário {{ nomeColaborador }} a academia.
		</span>
	</div>
	<br />
	<div>
		<strong style="color: red">
			Esta ação fará com que o usuário selecionado perca o acesso a todas as
			unidades da academia e irá remover os dados de acesso de e-mail e senha!
		</strong>
	</div>
</ng-template>
