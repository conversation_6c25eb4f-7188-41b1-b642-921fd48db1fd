import {
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
	TemplateRef,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import {
	TreinoApiPerfilAcessoService,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { SeletorImagemUserComponent } from "old-ui-kit";
import {
	TreinoApiColaboradorService,
	ColaboradorSelect,
	TreinoApiUserService,
	ColaboradorTipoUsuario,
	SituacaoUsuarioEnum,
	UsuarioColaborador,
	Perfil,
} from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { AlterarEmailUsuarioComponent } from "../alterar-email-usuario/alterar-email-usuario.component";
import { DialogService, TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-colaborador-simples-edit",
	templateUrl: "./colaborador-simples-edit.component.html",
	styleUrls: ["./colaborador-simples-edit.component.scss"],
})
export class ColaboradorSimplesEditComponent implements OnInit {
	@ViewChild("editSuccess", { static: true }) editSuccess;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("validCamposObrigatorios", { static: true })
	validCamposObrigatorios;
	@ViewChild("seletorImagem", { static: true })
	seletorImagem: SeletorImagemUserComponent;
	@ViewChild("validNameUser", { static: true }) validNameUser;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("desvincularUsuarioConfirma", { static: true })
	confirmarUsuarioTemplate: TemplateRef<any>;

	tipoUsuarios = [
		{ id: "CONSULTOR", nome: "Consultor" },
		{ id: "COORDENADOR", nome: "Coordenador" },
		{ id: "PROFESSOR", nome: "Professor" },
	];
	colaboradores: Array<ColaboradorSelect> = [];
	perfis: Array<Perfil> = [];
	usuarioColaborador: UsuarioColaborador = {} as UsuarioColaborador;
	nomeColaborador: string;
	operation: string;
	entity = true;
	formGroup: FormGroup = new FormGroup({
		appUserName: new FormControl("", [
			Validators.required,
			Validators.minLength(3),
		]),
		email: new FormControl("", [Validators.required, Validators.email]),
		tipoUsuario: new FormControl(null),
		perfilUsuarioPermissoes: new FormControl(null, [Validators.required]),
	});

	colaboradorControl: FormControl = new FormControl(null, [
		Validators.required,
	]);
	integracaoZW = false;
	permissaoAlterarSenhaUsuario;
	permissaoDesvincularUsuario;
	idColaborador;
	selectColaborador = true;

	constructor(
		private usuarioService: TreinoApiUserService,
		private route: ActivatedRoute,
		private router: Router,
		private snotifyService: SnotifyService,
		private perfilService: TreinoApiPerfilAcessoService,
		private colaboradorService: TreinoApiColaboradorService,
		public sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private dialogService: DialogService
	) {}

	ngOnInit() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.carregarPermissoes();
		this.setSelect();
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
				this.selectColaborador = false;
			} else {
				this.operation = "create";
			}
		});
		this.colaboradorControl.valueChanges.subscribe(() => {
			this.changeSetImage();
		});
	}

	private loadEntities(id) {
		if (id) {
			this.usuarioService.obterColaborador(id).subscribe((dados) => {
				this.usuarioColaborador = dados;
				this.entity = false;
				this.loadForm();
				this.cd.detectChanges();
			});
		}
	}

	private loadForm() {
		this.nomeColaborador = this.usuarioColaborador.colaborador.nome;
		this.formGroup
			.get("appUserName")
			.setValue(this.usuarioColaborador.appUserName);
		this.formGroup.get("email").setValue(this.usuarioColaborador.email);
		this.formGroup
			.get("tipoUsuario")
			.setValue(this.usuarioColaborador.tipoUsuario);
		this.formGroup
			.get("perfilUsuarioPermissoes")
			.setValue(this.usuarioColaborador.perfilUsuarioPermissoes.id);
		this.colaboradorControl.setValue([this.usuarioColaborador.colaborador]);
		if (this.usuarioColaborador.colaborador.imageUri) {
			this.seletorImagem.uriImagem =
				this.usuarioColaborador.colaborador.imageUri;
			this.cd.detectChanges();
		}
		if (this.sessionService.isTreinoIndependente()) {
			this.formGroup.get("perfilUsuarioPermissoes").enable();
		}
	}

	cancelHandler() {
		this.router.navigate(["colaboradores"]);
	}

	submitHandler() {
		this.markAsTouched();
		if (this.entity) {
			this.createHandler();
		} else {
			this.updateHandler();
		}
	}

	createHandler() {
		if (this.formGroup.valid && this.colaboradorControl.valid) {
			this.idColaborador = this.colaboradorControl.value.id;
			const createSuccess = this.createSuccess.nativeElement.innerHTML;
			this.usuarioService
				.cadastrarColaborador(this.convertForm())
				.subscribe((result) => {
					if (result === "usuario_duplicado") {
						const validNameUser = this.validNameUser.nativeElement.innerHTML;
						this.snotifyService.error(validNameUser);
					} else {
						this.snotifyService.success(createSuccess);
						this.cancelHandler();
					}
				});
		} else {
			const validCamposObrigatorios =
				this.validCamposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(validCamposObrigatorios);
		}
	}

	updateHandler() {
		if (this.integracaoZW) {
			this.formGroup.get("email").clearValidators();
			this.formGroup.get("email").updateValueAndValidity();
		}
		if (this.formGroup.valid && this.colaboradorControl.valid) {
			const editSuccess = this.editSuccess.nativeElement.innerHTML;
			this.idColaborador = this.colaboradorControl.value[0].id;
			this.usuarioService
				.atualizarColaborador(this.usuarioColaborador.id, this.convertForm())
				.subscribe((result) => {
					if (result === "usuario_duplicado") {
						const validNameUser = this.validNameUser.nativeElement.innerHTML;
						this.snotifyService.error(validNameUser);
					} else {
						if (
							result.appUserName === this.sessionService.loggedUser.username
						) {
							this.sessionService.loggedUser.imageUri =
								result.colaborador.imageUri;
						}
						this.snotifyService.success(editSuccess);
						this.cancelHandler();
					}
				});
		} else {
			const validCamposObrigatorios =
				this.validCamposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(validCamposObrigatorios);
		}
	}

	get showError() {
		if (this.formGroup.get("tipoUsuario")) {
			return (
				!this.formGroup.get("tipoUsuario").valid &&
				this.formGroup.get("tipoUsuario").touched
			);
		} else {
			return false;
		}
	}

	markAsTouched() {
		this.colaboradorControl.markAsTouched();
		this.formGroup.get("appUserName").markAsTouched();
		this.formGroup.get("email").markAsTouched();
		this.formGroup.get("perfilUsuarioPermissoes").markAsTouched();
		this.formGroup.get("tipoUsuario").markAsTouched();
	}

	changeSetImage() {
		const colaborador = this.colaboradorControl.value;
		if (colaborador[0].imageUri) {
			this.seletorImagem.uriImagem = colaborador[0].imageUri;
		} else {
			this.seletorImagem.uriImagem = "assets/images/default-user-icon.png";
			this.seletorImagem.imagemData = null;
			this.seletorImagem.tipoImagem = null;
		}
	}

	private convertForm() {
		const formulario = this.formGroup.getRawValue();
		formulario.id = this.usuarioColaborador.id;
		formulario.colaboradorId = this.idColaborador;
		formulario.imagemData = this.seletorImagem.imagemData;
		formulario.extensaoImagem = this.seletorImagem.tipoImagem;
		formulario.situacao = this.usuarioColaborador
			? this.usuarioColaborador.situacao
			: SituacaoUsuarioEnum.ATIVO;

		return formulario;
	}

	private setSelect() {
		this.perfilService.obterTodosPerfis().subscribe((dados) => {
			this.perfis = dados;
			this.cd.detectChanges();
		});

		this.colaboradorService
			.obterColaboradorIsNullUsuario()
			.subscribe((result) => {
				this.colaboradores = result;
			});
	}

	private carregarPermissoes() {
		this.permissaoAlterarSenhaUsuario = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.ALTERAR_SENHA_USUARIOS
		);
		this.permissaoDesvincularUsuario = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.DESVINCULAR_USUARIO
		);
	}

	solicitarNovaSenha() {
		if (this.usuarioColaborador.emailVerificado && !this.entity) {
			this.usuarioService
				.recuperarSenha(this.usuarioColaborador.id)
				.subscribe((response) => {
					this.snotifyService.success(
						this.traducao.getLabel("email-recuperacao-enviado")
					);
				});
		}
	}

	alterarEmail() {
		const dialogRef = this.dialogService.open(
			"Alterar e-mail",
			AlterarEmailUsuarioComponent
		);
		dialogRef.componentInstance.idUsuario = this.usuarioColaborador.id;

		dialogRef.result.then((result) => {
			if (result.novoEmail) {
				this.loadEntities(this.usuarioColaborador.id);
			}
		});
	}

	desvincularUsuario() {
		if (this.formGroup.get("email").value && !this.entity) {
			const dialogRef = this.dialogService.confirmBodyRef(
				`${this.traducao.getLabel("confirmar-remocao-acesso")} ${
					this.nomeColaborador
				}`,
				this.confirmarUsuarioTemplate,
				"Desvincular"
			);
			dialogRef.result
				.then((result) => {
					this.usuarioService
						.desvincularUsuario(this.usuarioColaborador.id)
						.subscribe(
							(response) => {
								this.snotifyService.success(
									this.traducao.getLabel("usuario-desvinculado-com-sucesso")
								);
								this.loadEntities(this.usuarioColaborador.id);
							},
							(httpResponseError) => {
								const error = httpResponseError.error;
								console.log(error);
								if (error && error.meta) {
									if (error.meta.error) {
										this.snotifyService.error(
											this.traducao.getLabel(error.meta.error)
										);
									}
								}
							}
						);
				})
				.catch((error) => console.log(error));
		}
	}
}
