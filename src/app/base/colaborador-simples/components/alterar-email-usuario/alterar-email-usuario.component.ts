import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiUserService } from "treino-api";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-alterar-email-usuario",
	templateUrl: "./alterar-email-usuario.component.html",
	styleUrls: ["./alterar-email-usuario.component.scss"],
})
export class AlterarEmailUsuarioComponent implements OnInit {
	@ViewChild("traducao", { static: false })
	traducao: TraducoesXinglingComponent;
	token: string;
	showFormCodigo: boolean;
	idUsuario: number;
	form: FormGroup = new FormGroup({
		email: new FormControl("", [Validators.required, Validators.email]),
	});

	formCodigo: FormGroup = new FormGroup({
		codigoVerificacao: new FormControl("", Validators.required),
	});

	constructor(
		private dialog: NgbActiveModal,
		private userService: TreinoApiUserService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	verificarEmail() {}

	cancelar() {
		this.dialog.dismiss("cancelado");
	}

	solicitarTrocaEmail() {
		if (this.form.invalid) {
			this.notificationService.error(this.traducao.getLabel("email-invalido"));
			return;
		}
		if (!this.idUsuario) {
			this.notificationService.error(
				this.traducao.getLabel("usuario-nao-informado")
			);
			return;
		}
		if (this.form.valid && this.idUsuario) {
			this.userService
				.solicitarTrocaEmail(this.idUsuario, this.form.get("email").value)
				.subscribe(
					(response) => {
						this.showFormCodigo = true;
						this.token = response.content.token;
						this.notificationService.success(
							this.traducao.getLabel("codigo-verificacao-enviado")
						);
						this.cd.detectChanges();
					},
					(error) => {
						this.showFormCodigo = false;
						this.cd.detectChanges();
						if (error.error) {
							if (error.error.meta && error.error.meta.error) {
								this.notificationService.error(error.error.meta.error);
							} else if (error.error.meta.messageValue) {
								this.notificationService.error(error.error.meta.messageValue);
							} else {
								this.notificationService.error(error.error.meta.message);
							}
						}
					}
				);
		}
	}

	validarCodigodeVerificacao() {
		if (this.formCodigo.invalid) {
			this.notificationService.error(this.traducao.getLabel("codigo-invalido"));
			return;
		}

		this.userService
			.validarCodigo(
				this.idUsuario,
				this.token,
				this.formCodigo.get("codigoVerificacao").value
			)
			.subscribe(
				(response) => {
					this.showFormCodigo = false;
					this.dialog.close({ novoEmail: this.form.get("email").value });
					this.notificationService.success("E-mail alterado com sucesso!");
				},
				(error) => {
					if (error.error) {
						if (error.error.meta && error.error.meta.error) {
							this.notificationService.error(error.error.meta.error);
						} else if (error.error.meta.messageValue) {
							this.notificationService.error(error.error.meta.messageValue);
						} else {
							this.notificationService.error(error.error.meta.message);
						}
					}
				}
			);
	}
}
