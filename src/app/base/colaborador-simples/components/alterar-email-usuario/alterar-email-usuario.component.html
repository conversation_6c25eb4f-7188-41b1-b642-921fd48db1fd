<form [formGroup]="form">
	<pacto-cat-form-input
		*ngIf="!showFormCodigo"
		[control]="form.get('email')"
		errorMsg="E-mail inválido"
		i18n-errorMsg="@@error-email-invalido"
		i18n-label="@@label-novo-email"
		label="Informe o novo e-mail"
		placeholder="<EMAIL>"></pacto-cat-form-input>

	<pacto-cat-form-input
		*ngIf="showFormCodigo"
		[control]="formCodigo.get('codigoVerificacao')"
		errorMsg="Código de verificação inválido"
		i18n-errorMsg="@@error-codigo-verificacao-invalido"
		i18n-label="@@label-codigo-verificacao"
		label="Informe o código de verificacao enviado no e-mail informado"
		placeholder="123456"></pacto-cat-form-input>

	<div class="btn-row">
		<pacto-cat-button
			(click)="cancelar()"
			i18n-label="@@btn-cancelar"
			label="Cancelar"
			size="LARGE"
			type="OUTLINE_ACTION"></pacto-cat-button>
		<pacto-cat-button
			(click)="solicitarTrocaEmail()"
			*ngIf="!showFormCodigo"
			i18n-label="@@btn-alterar-email"
			label="Alterar e-mail"
			size="LARGE"></pacto-cat-button>
		<pacto-cat-button
			(click)="validarCodigodeVerificacao()"
			*ngIf="showFormCodigo"
			i18n-label="@@btn-validar-codigo-label"
			label="Validar código de verificação"
			size="LARGE"></pacto-cat-button>
	</div>
</form>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@alterar-email:email-invalido" xingling="email-invalido">
		E-mail inválido!
	</span>
	<span
		i18n="@@alterar-email:usuario-nao-informado"
		xingling="usuario-nao-informado">
		Usuário não informado!
	</span>
	<span
		i18n="@@alterar-email:codigo-verificacao-enviado"
		xingling="codigo-verificacao-enviado">
		Código de verificação enviado ao e-mail informado!
	</span>
</pacto-traducoes-xingling>
