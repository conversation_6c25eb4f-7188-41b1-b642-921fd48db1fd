import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Route, RouterModule } from "@angular/router";
import { PerfilAcessoListaComponent } from "./components/perfil-acesso-lista/perfil-acesso-lista.component";
import { PerfilAcessoEditComponent } from "./components/perfil-acesso-edit/perfil-acesso-edit.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { PerfilPermissaoItemComponent } from "./components/perfil-permissao-item/perfil-permissao-item.component";
import { PerfilFuncionalidadeItemComponent } from "./components/perfil-funcionalidade-item/perfil-funcionalidade-item.component";
import { PerfilAcessoCreateModalComponent } from "./components/perfil-acesso-create-modal/perfil-acesso-create-modal.component";
import {
	PerfilAcessoRecurso,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const recursos = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.PERFIL_USUARIO,
	[
		PerfilRecursoPermissoTipo.CONSULTAR,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const recursoEdit = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.PERFIL_USUARIO,
	[
		PerfilRecursoPermissoTipo.EDITAR,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const routes: Route[] = [
	{
		path: "",
		component: PerfilAcessoListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: recursos,
		},
	},
	{
		path: ":id",
		component: PerfilAcessoEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso: recursoEdit,
		},
	},
];

@NgModule({
	declarations: [
		PerfilAcessoListaComponent,
		PerfilAcessoEditComponent,
		PerfilPermissaoItemComponent,
		PerfilFuncionalidadeItemComponent,
		PerfilAcessoCreateModalComponent,
	],
	entryComponents: [PerfilAcessoCreateModalComponent],
	imports: [RouterModule.forChild(routes), BaseSharedModule, CommonModule],
	schemas: [NO_ERRORS_SCHEMA],
})
export class PerfilAcessoModule {}
