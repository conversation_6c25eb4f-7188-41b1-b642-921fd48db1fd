export const permissaoCategoria = {
	agenda: {
		recursos: [
			"avaliacao_fisica",
			"contato_interpessoal",
			"agenda_disponibilidade",
			"prescricao_treino",
			"renovar_treino",
			"revisao_treino",
		],
		funcionalidades: [
			"utilizar_modulo_agenda",
			"ver_agenda_outros_professores",
			"bi_agenda",
			"agenda_de_aulas",
			"agenda_de_servicos",
			"relatorios_agenda",
			"cadastros_agenda",
		],
	},
	aluno: {
		recursos: ["alunos", "programa_treino"],
		funcionalidades: [
			"alterar_professor_treino",
			"visualizar_dados_contato_aluno",
			"ver_alunos_outras_carteiras",
			"trocar_professor",
			"atribuir_programa_treino_pre_definido",
			"prescricao_de_treino_por_ia",
		],
	},
	aula: {
		recursos: [],
		funcionalidades: [
			"cadastro_aulas",
			"excluir_aulas_dia",
			"editar_aulas_dia",
			"inserir_aluno",
			"excluir_aluno",
			"inserir_aluno_aula_iniciada",
			"alterar_professor_aula",
			"modalidades",
			"ambientes",
			"alterar_professor_aula_iniciada",
			"usuario_marcar_antecedencia",
			"visualizar_todas_aulas_app",
		],
	},
	"cadastro-auxiliares": {
		recursos: [
			// 'musculos',
			// 'grupos_musculares',
			"aparelhos",
			"atividades",
			"fichas_pre_definidas",
			"programas_predefinidos",
			"categoria_atividade",
			"categoria_fichas",
			"niveis",
			"objetivos",
			"imagens",
			"tipo_evento",
			"anamnese",
		],
		funcionalidades: [],
	},
	crossfit: {
		recursos: [],
		funcionalidades: [
			"utilizar_modulo_cross",
			"benchmark",
			"tipo_benchmark",
			"wod",
			"ranking_wod",
			"cadastrar_tipo_wod",
			"atividades_wod",
			"aparelhos_wod",
			"visualizar_wod_outras_unidades",
			"bi_cross",
			"monitor",
		],
	},
	geral: {
		recursos: [
			"perfil_usuario",
			"usuarios",
			"configuracoes_empresa",
			"lancar_avalicao_retroativa",
		],
		funcionalidades: [
			"tornar_ficha_predefinida",
			"tornar_programa_predefinido",
			"alterar_senha_usuarios",
			"colaboradores",
			"ver_bi_outros_professores",
			"ranking_professores",
			"configuracoes_do_ranking",
			"ver_gestao_geral",
			"excluir_anexo_aluno",
			"tela_prescricao_treino",
			"avaliacao_media_alunos",
			"enviar_treino_em_massa",
			"treino_em_casa",
			"prescricao_de_treino",
		],
	},
	gestao_personal: {
		recursos: [],
		funcionalidades: [
			"acompanhar_personal",
			"alterar_horario_inicio_check_personal",
			"editar_excluir_aula",
			"fazer_check_out",
			"liberar_check_in",
			"gestao_personal",
			"gestao_creditos_personal",
		],
	},
	graduacao: {
		recursos: [],
		funcionalidades: [
			"utilizar_modulo_graduacao",
			"atividades_graduacao",
			"ficha_tecnica_graduacao",
			"avaliacao_de_progresso_graduacao",
		],
	},
	avaliacao_fisica: {
		recursos: [],
		funcionalidades: [
			"utilizar_modulo_avaliacao_fisica",
			"bi_avaliacao_fisica",
			"cadastros_avaliacao_fisica",
		],
	},
	treino: {
		recursos: [],
		funcionalidades: ["cadastros_treino", "relatorios_treino"],
	},
};

export const permissaoCategoriaIndependente = {
	agenda: {
		recursos: [
			"avaliacao_fisica",
			"contato_interpessoal",
			"agenda_disponibilidade",
			"prescricao_treino",
			"renovar_treino",
			"revisao_treino",
		],
		funcionalidades: ["ver_agenda_outros_professores"],
	},
	aluno: {
		recursos: ["alunos", "programa_treino"],
		funcionalidades: [
			"alterar_professor_treino",
			"visualizar_dados_contato_aluno",
			"ver_alunos_outras_carteiras",
			"trocar_professor",
		],
	},
	aula: {
		recursos: [],
		funcionalidades: [
			"cadastro_aulas",
			"editar_aulas_dia",
			"excluir_aulas_dia",
			"inserir_aluno",
			"excluir_aluno",
			"inserir_aluno_aula_iniciada",
			"alterar_professor_aula",
			"alterar_professor_aula_iniciada",
			"modalidades",
			"ambientes",
			"usuario_marcar_antecedencia",
			"visualizar_todas_aulas_app",
		],
	},
	"cadastro-auxiliares": {
		recursos: [
			// 'musculos',
			// 'grupos_musculares',
			"aparelhos",
			"atividades",
			"fichas_pre_definidas",
			"programas_predefinidos",
			"categoria_atividade",
			"categoria_fichas",
			"niveis",
			"objetivos",
			"imagens",
			"tipo_evento",
			"empresa",
			"anamnese",
		],
		funcionalidades: [],
	},
	crossfit: {
		recursos: [],
		funcionalidades: [
			"benchmark",
			"tipo_benchmark",
			"wod",
			"ranking_wod",
			"cadastrar_tipo_wod",
			"atividades_wod",
			"aparelhos_wod",
		],
	},
	geral: {
		recursos: [
			"perfil_usuario",
			"usuarios",
			"configuracoes_empresa",
			"lancar_avalicao_retroativa",
		],
		funcionalidades: [
			"tornar_ficha_predefinida",
			"tornar_programa_predefinido",
			"colaboradores",
			"alterar_senha_usuarios",
			"ver_bi_outros_professores",
			"ranking_professores",
			"configuracoes_do_ranking",
			"ver_gestao_geral",
			"excluir_anexo_aluno",
			"desvincular_usuario",
			"tela_prescricao_treino",
			"avaliacao_media_alunos",
		],
	},
};
