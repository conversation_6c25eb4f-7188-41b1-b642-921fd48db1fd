<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'PERFIL'
		}"></pacto-breadcrumbs>
	<div class="table-wrapper pacto-shadow pct-relatorio-component-perfil-acesso">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[sessionService]="sessionService"
			[tableDescription]="subTitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="perfilDeAcesso"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@perfil-acesso:span-perfil-acesso">Perfil de Acesso</span>
</ng-template>
<ng-template #subTitulo i18n="@@perfil-acesso:gerencie-permissao">
	<span>Gerencie as permissões do usuário</span>
</ng-template>
<!--title table end-->
<!--name columns-->
<ng-template #nomeColumnName>
	<span i18n="@@perfil-acesso-edit:perfil-acesso-nome">Nome</span>
</ng-template>
<ng-template #tipoPerfilColumnName>
	<span i18n="@@perfil-acesso-edit:perfil-acesso-tipo">Tipo de perfil</span>
</ng-template>
<!--name columns end-->

<ng-template #buttonAdd>
	<span i18n="@@perfil-acesso-edit:perfil-acesso-button">Adicionar</span>
</ng-template>

<pacto-traducoes-xingling #traducoesModalRemove>
	<span xingling="title">Remover perfil de acesso?</span>
	<span xingling="body">Deseja remover o perfil {{ nomePerfil }}?</span>
	<span i18n="@@perfil-acesso-lista:perfil-removido" xingling="notifySuccess">
		Perfil removido com sucesso.
	</span>
	<span xingling="notifyAlert">
		Não e possível excluir perfis vinculados a usuários.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducoesTooltipIcons>
	<span xingling="remove">Remover</span>
	<span xingling="edit">Editar</span>
</pacto-traducoes-xingling>

<!--tooltip icons-->
<span
	#tooltipEditar
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:editar:tooltip-icon">
	Editar
</span>
<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:remover:tooltip-icon">
	Remover
</span>
<span
	#tooltipVisualizarProfessores
	[hidden]="true"
	i18n="@@crud-fichas-predefinidas:remover:tooltip-icon">
	Visualizar usuários com este perfil
</span>
<!--end tooltip icons-->

<pacto-traducoes-xingling #traducoesModalCreate>
	<span i18n="@@perfil-acesso-lista:perfil-criado" xingling="createSuccess">
		Perfil criado com sucesso.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducoes>
	<span xingling="verProfessoresDestePerfil">Professores deste perfil</span>

	<ng-template xingling="id">Colaborador</ng-template>
	<ng-template xingling="nome">Nome</ng-template>
</pacto-traducoes-xingling>
<div #conflito [hidden]="true" id="help-message-duplicated-entity-name">
	Já existe um cadastro com esse mesmo nome
</div>
