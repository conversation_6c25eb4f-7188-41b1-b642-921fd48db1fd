import { Component, OnInit, ViewChild } from "@angular/core";
import {
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Router } from "@angular/router";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import {
	TreinoApiPerfilAcessoService,
	PerfilAcessoRecursoNome,
} from "treino-api";
import { SnotifyService } from "ng-snotify";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PerfilAcessoCreateModalComponent } from "../perfil-acesso-create-modal/perfil-acesso-create-modal.component";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { relatorios } from "../../../../treino/treino-bi/components/indicadores-grid/relatorios.config";

@Component({
	selector: "pacto-perfil-acesso-lista",
	templateUrl: "./perfil-acesso-lista.component.html",
	styleUrls: ["./perfil-acesso-lista.component.scss"],
})
export class PerfilAcessoListaComponent implements OnInit {
	@ViewChild("traducoesModalRemove", { static: true }) traducoesModalRemove;
	@ViewChild("traducoesTooltipIcons", { static: true }) traducoesTooltipIcons;
	@ViewChild("traducoesModalCreate", { static: true })
	traducoesModalCreate: TraducoesXinglingComponent;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("tipoPerfilColumnName", { static: true }) tipoPerfilColumnName;
	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("buttonAdd", { static: true }) buttonAdd;
	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipVisualizarProfessores", { static: true })
	tooltipVisualizarProfessores;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	config: PactoDataGridConfigDto;
	@ViewChild("conflito", { static: true }) conflito;
	relatorio: RelatorioComponent;

	constructor(
		private router: Router,
		private modalService: ModalService,
		public perfilService: TreinoApiPerfilAcessoService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private modal: NgbModal,
		public sessionService: SessionService
	) {}

	table: PactoDataGridConfig;
	nomePerfil = "";

	ngOnInit() {
		this.configTable();
	}

	tipoPerfis: Array<any> = [
		{ id: "PR", nome: "Professor" },
		{ id: "PT", nome: "Personal Trainer" },
		{ id: "OR", nome: "Orientador" },
		{ id: "CO", nome: "Consultor" },
		{ id: "PI", nome: "Personal Interno" },
		{ id: "PE", nome: "Personal Externo" },
		{ id: "TE", nome: "Terceirizado" },
		{ id: "FO", nome: "Fornecedor" },
		{ id: "CR", nome: "Coordenador" },
		{ id: "MD", nome: "Médico" },
		{ id: "FC", nome: "Funcionário" },
		{ id: "AD", nome: "Administrador" },
	];

	btnClickHandler() {
		const modalRef = this.modal.open(PerfilAcessoCreateModalComponent);
		modalRef.result.then((result) => {
			const dto: any = {
				nome: result.nome,
				tipo: result.tipoPerfil,
			};
			this.perfilService.cadastrarPerfil(dto).subscribe((response) => {
				this.snotifyService.success(
					this.traducoesModalCreate.getLabel("createSuccess")
				);
				if (
					this.sessionService.recursos.get(
						PerfilAcessoRecursoNome.PERFIL_USUARIO
					).editar
				) {
					this.router.navigate(["pessoas", "perfil-acesso", response.id]);
				} else {
					this.tableData.reloadData();
				}
			});
		});
	}

	btnEditHandler(perfil) {
		this.router.navigate(["pessoas", "perfil-acesso", perfil.id]);
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		} else if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		} else if ($event.iconName === "view") {
			this.visualizarProfessores($event.row);
		}
	}

	private removeHandler(item) {
		this.nomePerfil = item.nome;
		setTimeout(() => {
			const handler = this.modalService.confirm(
				this.traducoesModalRemove.getLabel("title"),
				this.traducoesModalRemove.getLabel("body")
			);
			handler.result.then(() => {
				this.perfilService.removerPerfil(item.id).subscribe((response) => {
					if (response === "registro_esta_sendo_utilizado") {
						this.snotifyService.warning(
							this.traducoesModalRemove.getLabel("notifyAlert")
						);
					} else {
						this.tableData.reloadData();
						this.snotifyService.success(
							this.traducoesModalRemove.getLabel("notifySuccess")
						);
					}
				});
			});
		});
	}

	private visualizarProfessores(itemPerfil) {
		this.nomePerfil = itemPerfil.nome;
		const itemRelatorio = relatorios["professoresPorPerfilAcesso"];
		const pctModal = this.modalService.open(
			"Usuários com o perfil de acesso " + itemPerfil.nome,
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		this.relatorio = pctModal.componentInstance;
		this.config = {
			endpointUrl: this.rest.buildFullUrl(
				itemRelatorio.endpoint + "/" + itemPerfil.id
			),
			pagination: itemRelatorio.pagination,
			quickSearch: true,
			exportButton: false,
			rowClick: true,
			columns: [],
		};
		itemRelatorio.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				perfilId: itemPerfil.id,
			},
		};
	}

	private addButtonTable() {
		let result = null;
		if (
			this.sessionService.recursos.get(PerfilAcessoRecursoNome.PERFIL_USUARIO)
				.incluir
		) {
			result = {
				conteudo: this.buttonAdd,
				nome: "add",
				id: "adicionar-novo_perfil",
			};
		}
		return result;
	}

	private addIconTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		const tooltipView =
			this.tooltipVisualizarProfessores.nativeElement.innerHTML;
		const result = [];
		if (
			this.sessionService.recursos.get(PerfilAcessoRecursoNome.PERFIL_USUARIO)
				.editar
		) {
			result.push(
				{
					nome: "view",
					iconClass: "pct pct-eye",
					tooltipText: tooltipView,
				},
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
				}
			);
		}
		if (
			this.sessionService.recursos.get(PerfilAcessoRecursoNome.PERFIL_USUARIO)
				.excluir
		) {
			result.push({
				nome: "remove",
				iconClass: "fa fa-trash-o",
				tooltipText: tooltipRemover,
			});
		}
		return result;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("perfis-acesso"),
			logUrl: this.rest.buildFullUrl("log/log-perfil-acesso/PERFIL/null/null"),
			quickSearch: true,
			buttons: this.addButtonTable(),
			rowClick: this.sessionService.recursos.get(
				PerfilAcessoRecursoNome.PERFIL_USUARIO
			).editar,
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
				{
					nome: "tipo",
					titulo: this.tipoPerfilColumnName,
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "tipo",
					valueTransform: (v) => this.transformTipoValue(v),
				},
			],
			actions: this.addIconTable(),
		});
	}

	private transformTipoValue(v: any): string {
		let retValue = "";
		this.tipoPerfis.forEach((t) => {
			if (t.id == v) {
				retValue = t.nome;
			}
		});
		return retValue;
	}
}
