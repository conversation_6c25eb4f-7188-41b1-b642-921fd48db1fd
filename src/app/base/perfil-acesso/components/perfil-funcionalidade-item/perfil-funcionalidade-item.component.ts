import {
	ChangeDetectionStrategy,
	Component,
	forwardRef,
	Input,
	OnInit,
} from "@angular/core";
import { FormControl, FormGroup, NG_VALUE_ACCESSOR } from "@angular/forms";
import { BaseControlValueAccessor } from "@base-shared/BaseControlValueAccessor";

@Component({
	selector: "pacto-perfil-funcionalidade-item",
	templateUrl: "./perfil-funcionalidade-item.component.html",
	styleUrls: ["./perfil-funcionalidade-item.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => PerfilFuncionalidadeItemComponent),
			multi: true,
		},
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PerfilFuncionalidadeItemComponent
	extends BaseControlValueAccessor<boolean>
	implements OnInit
{
	@Input() label: string;
	@Input() id: string;

	constructor() {
		super();
	}

	_fg: FormGroup = new FormGroup({
		habilitado: new FormControl(false),
		desabilitado: new FormControl(false),
	});

	ngOnInit() {
		this._fg.get("habilitado").valueChanges.subscribe((value) => {
			const desabilitado = this._fg.get("desabilitado").value;
			this.onChange(this.value);
			if (desabilitado !== !value) {
				this._fg.get("desabilitado").setValue(!value);
			}
		});
		this._fg.get("desabilitado").valueChanges.subscribe((value) => {
			this._fg.get("habilitado").setValue(!value);
			this.onChange(this.value);
		});
	}

	get value() {
		return this._fg.get("habilitado").value;
	}

	set value(value) {
		this._fg.get("habilitado").setValue(value);
		this._fg.get("desabilitado").setValue(!value);
		this.onChange(value);
	}
}
