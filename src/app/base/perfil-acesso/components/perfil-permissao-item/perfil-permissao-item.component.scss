@import "src/assets/scss/pacto/plataforma-import.scss";

.row-item-permissao {
	position: relative;
	display: flex;
	flex-direction: row-reverse;
	justify-content: space-between;
	padding: 23px 21px 23px 21px;
	border-top: 1px solid $geloPri;

	.label-permissao {
		@extend .type-p-small;
		min-width: 180px;
		flex-basis: 180px;
		word-break: break-word;
	}

	.tipo-permissao {
		display: flex;
		flex-wrap: wrap;

		pacto-cat-toggle-input {
			padding-right: 10px;
		}
	}
}

.overlayer {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0.6;
	background: $branco;
}
