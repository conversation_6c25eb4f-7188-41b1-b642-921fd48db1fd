<div class="row-item-permissao">
	<div *ngIf="loading" class="overlayer"></div>
	<div class="tipo-permissao">
		<pacto-cat-toggle-input
			#consultarInput
			[control]="_fg.get(PermissaoTipo.CONSULTAR)"
			[id]="'consultar-' + id"
			label="Consultar"></pacto-cat-toggle-input>
		<pacto-cat-toggle-input
			#incluirInput
			[control]="_fg.get(PermissaoTipo.INCLUIR)"
			[id]="'incluir-' + id"
			label="Incluir"></pacto-cat-toggle-input>
		<pacto-cat-toggle-input
			#editarInput
			[control]="_fg.get(PermissaoTipo.EDITAR)"
			[id]="'editar-' + id"
			label="Editar"></pacto-cat-toggle-input>
		<pacto-cat-toggle-input
			#excluirInput
			[control]="_fg.get(PermissaoTipo.EXCLUIR)"
			[id]="'excluir-' + id"
			i18n-label="@@perfil-acesso-edit:label-excluir"
			label="Excluir"></pacto-cat-toggle-input>
		<pacto-cat-toggle-input
			#totalExcetoExcluirInput
			[control]="_fg.get(PermissaoTipo.TOTAL_EXCETO_EXCLUIR)"
			[id]="'total-exceto-excluir-' + id"
			i18n-label="@@perfil-acesso-edit:label-exceto-excluir"
			label="Total exceto excluir"></pacto-cat-toggle-input>
		<pacto-cat-toggle-input
			#totalInput
			[control]="_fg.get(PermissaoTipo.TOTAL)"
			[id]="'total-' + id"
			label="Total"></pacto-cat-toggle-input>
	</div>
	<div class="label-permissao">{{ label }}</div>
</div>
