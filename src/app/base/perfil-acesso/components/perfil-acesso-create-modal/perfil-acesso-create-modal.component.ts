import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-perfil-acesso-create-modal",
	templateUrl: "./perfil-acesso-create-modal.component.html",
	styleUrls: ["./perfil-acesso-create-modal.component.scss"],
})
export class PerfilAcessoCreateModalComponent implements OnInit {
	@ViewChild("notificacao", { static: true })
	notificacao: TraducoesXinglingComponent;

	control = new FormGroup({
		nome: new FormControl("", [Validators.required]),
		tipoPerfil: new FormControl("", [Validators.required]),
	});

	tipoPerfis: Array<any> = [
		{ id: "PR", nome: "Professor" },
		{ id: "PT", nome: "Personal Trainer" },
		{ id: "OR", nome: "Orientador" },
		{ id: "CO", nome: "Consultor" },
		{ id: "PI", nome: "Personal Interno" },
		{ id: "PE", nome: "Personal Externo" },
		{ id: "TE", nome: "Terceirizado" },
		{ id: "FO", nome: "Fornecedor" },
		{ id: "CR", nome: "Coordenador" },
		{ id: "MD", nome: "Médico" },
		{ id: "FC", nome: "Funcionário" },
		{ id: "AD", nome: "Administrador" },
	];

	constructor(
		private snotifyService: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {}

	dismiss() {
		this.openModal.dismiss();
	}

	clickHandler() {
		this.control.markAsTouched();
		if (this.control.valid) {
			this.openModal.close(this.control.value);
		} else {
			this.snotifyService.error(
				this.notificacao.getLabel("camposObrigatorios")
			);
		}
	}
}
