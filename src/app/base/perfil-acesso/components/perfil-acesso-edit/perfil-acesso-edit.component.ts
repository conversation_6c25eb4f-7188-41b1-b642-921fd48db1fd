import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { TreinoApiPerfilAcessoService, PefilAcessoDetalhe } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import {
	permissaoCategoria,
	permissaoCategoriaIndependente,
} from "src/app/base/perfil-acesso/components/config.model";
import {
	CatTabsTransparentComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-perfil-acesso-edit",
	templateUrl: "./perfil-acesso-edit.component.html",
	styleUrls: ["./perfil-acesso-edit.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PerfilAcessoEditComponent implements OnInit {
	@ViewChild("notificacoes", { static: true })
	notificacoes: TraducoesXinglingComponent;
	@ViewChild("tabs", { static: true }) tabs: CatTabsTransparentComponent;

	constructor(
		private session: SessionService,
		private perfilService: TreinoApiPerfilAcessoService,
		private snotifyService: SnotifyService,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private rest: RestService
	) {}

	recursoFormGroups: { [key: string]: FormGroup } = {};
	funcionalidadeFormGroups: { [key: string]: FormGroup } = {};

	controlNamePerfil = new FormGroup({
		nome: new FormControl("", [Validators.required]),
		tipoPerfil: new FormControl("", [Validators.required]),
	});

	tipoPerfis: Array<any> = [
		{ id: "PR", nome: "Professor" },
		{ id: "PT", nome: "Personal Trainer" },
		{ id: "OR", nome: "Orientador" },
		{ id: "CO", nome: "Consultor" },
		{ id: "PI", nome: "Personal Interno" },
		{ id: "PE", nome: "Personal Externo" },
		{ id: "TE", nome: "Terceirizado" },
		{ id: "FO", nome: "Fornecedor" },
		{ id: "CR", nome: "Coordenador" },
		{ id: "MD", nome: "Médico" },
		{ id: "FC", nome: "Funcionário" },
		{ id: "AD", nome: "Administrador" },
	];
	perfil: PefilAcessoDetalhe;
	categoriaRecursoJsonInit: string;
	nomeSendoModificado = false;
	saveRecursoSuccess = false;
	saveFuncionalidadeSuccess = false;
	createFormGroups = false;
	recursoSendoModificado = false;
	funcionalidadeSendoModificada = false;
	apresentarRecurso = false;
	apresentarFuncionalidade = false;
	tabAtiva: string;
	categoriaRecursoEnumAtivo: string;
	tipoPerfilNaoSelecionado: boolean = false;

	get permissaoCategoria() {
		if (this.session.integracaoZW) {
			return permissaoCategoria;
		} else {
			console.log(permissaoCategoriaIndependente);
			return permissaoCategoriaIndependente;
		}
	}

	get logUrl() {
		if (this.perfil && this.categoriaRecursoEnumAtivo) {
			return this.rest.buildFullUrl(
				`log/log-perfil-acesso/PERFIL/${this.perfil.id}/${this.categoriaRecursoEnumAtivo}`
			);
		}
	}

	ngOnInit() {
		this.createFormGroups = false;
		this.createFormGroupStructure();

		this.activatedRoute.params.subscribe((params) => {
			this.loadEntity(params.id);
		});
	}

	clickCancelNomeHandler() {
		this.controlNamePerfil.get("nome").setValue(this.perfil.nome);
		this.controlNamePerfil.get("tipoPerfil").setValue(this.perfil.tipo);
	}

	loadTabHandler($event: { previous: string; next: string }) {
		this.tabAtiva = $event.next;
		this.tratarNomeTabAtiva();
		if (
			this.createFormGroups &&
			this.permissaoCategoria[$event.next] &&
			this.permissaoCategoria[$event.next].recursos.length > 0
		) {
			this.apresentarRecurso = true;
		} else {
			this.apresentarRecurso = false;
		}
		if (
			this.createFormGroups &&
			this.permissaoCategoria[$event.next] &&
			this.permissaoCategoria[$event.next].funcionalidades.length > 0
		) {
			this.apresentarFuncionalidade = true;
		} else {
			this.apresentarFuncionalidade = false;
		}
		if (this.recursoFormGroups[$event.previous]) {
			this.recursoFormGroups[$event.previous].patchValue(this.perfil.recursos);
			this.recursoSendoModificado = false;
		}
		if (this.recursoFormGroups[$event.next]) {
			this.categoriaRecursoJsonInit = JSON.stringify(
				this.recursoFormGroups[$event.next].getRawValue()
			);
		}
		if (this.funcionalidadeFormGroups[$event.previous]) {
			this.funcionalidadeFormGroups[$event.previous].patchValue(
				this.perfil.funcionalidades
			);
			this.funcionalidadeSendoModificada = false;
		}
	}

	tratarNomeTabAtiva() {
		switch (this.tabAtiva) {
			case "agenda":
				this.tabAtiva = "Agenda";
				this.categoriaRecursoEnumAtivo = "AGENDA";
				break;
			case "aluno":
				this.tabAtiva = "Aluno";
				this.categoriaRecursoEnumAtivo = "ALUNO";
				break;
			case "aula":
				this.tabAtiva = "Aula";
				this.categoriaRecursoEnumAtivo = "AULAS";
				break;
			case "cadastro-auxiliares":
				this.tabAtiva = "Cadastros Auxiliares";
				this.categoriaRecursoEnumAtivo = "CADASTROS_AUXILIARES";
				break;
			case "crossfit":
				this.tabAtiva = "Cross";
				this.categoriaRecursoEnumAtivo = "CROSSFIT";
				break;
			case "geral":
				this.tabAtiva = "Geral";
				this.categoriaRecursoEnumAtivo = "GERAL";
				break;
			case "gestao_personal":
				this.tabAtiva = "Gestão de Personal";
				this.categoriaRecursoEnumAtivo = "PERSONAL";
				break;
			case "graduacao":
				this.tabAtiva = "Graduação";
				this.categoriaRecursoEnumAtivo = "GRADUACAO";
				break;
			case "avaliacao_fisica":
				this.tabAtiva = "Avaliação Física";
				this.categoriaRecursoEnumAtivo = "AVALIACAO_FISICA";
				break;
			case "treino":
				this.tabAtiva = "Treino";
				this.categoriaRecursoEnumAtivo = "TREINO";
				break;
		}
	}

	recursoCancelHandler($event: MouseEvent, categoria) {
		$event.stopPropagation();
		this.recursoFormGroups[categoria].patchValue(this.perfil.recursos);
		this.recursoSendoModificado = false;
	}

	funcionalidadeCancelHandler($event: MouseEvent, categoria) {
		$event.stopPropagation();
		this.funcionalidadeFormGroups[categoria].patchValue(
			this.perfil.funcionalidades
		);
		this.funcionalidadeSendoModificada = false;
	}

	clickSaveRecursoHandler($event: MouseEvent, nameAba) {
		$event.stopPropagation();
		if (this.perfil && this.perfil.id) {
			if (!this.perfil.tipo) {
				this.tipoPerfilNaoSelecionado = true;
				this.snotifyService.warning(
					this.notificacoes.getLabel("alertTipoPerfil")
				);
			} else {
				const dto: any = {};
				dto.recursos = this.recursoFormGroups[nameAba].getRawValue();
				this.perfilService.alterarPerfilAcesso(this.perfil.id, dto).subscribe({
					error: (error) => {
						this.tipoPerfilNaoSelecionado = true;
						this.snotifyService.error(error.error.meta.message);
						this.activatedRoute.params.subscribe((params) => {
							this.loadEntity(params.id);
						});
					},
					next: (response) => {
						this.perfil = response;
						this.recursoSendoModificado = false;
						this.categoriaRecursoJsonInit = JSON.stringify(
							this.recursoFormGroups[this.tabs.tabId].getRawValue()
						);
						this.tipoPerfilNaoSelecionado = false;
						this.cd.detectChanges();
						this.snotifyService.success(
							this.notificacoes.getLabel("editSuccess")
						);
					},
				});
			}
		}
	}

	clickSaveFuncionalidadeHandler($event: MouseEvent, nameAba) {
		$event.stopPropagation();
		if (this.perfil && this.perfil.id) {
			if (!this.perfil.tipo) {
				this.tipoPerfilNaoSelecionado = true;
				this.snotifyService.warning(
					this.notificacoes.getLabel("alertTipoPerfil")
				);
			} else {
				const dto: any = {};
				dto.funcionalidades =
					this.funcionalidadeFormGroups[nameAba].getRawValue();
				this.saveFuncionalidadeSuccess = true;
				this.perfilService.alterarPerfilAcesso(this.perfil.id, dto).subscribe({
					error: (error) => {
						this.tipoPerfilNaoSelecionado = true;
						this.snotifyService.error(error.error.meta.message);
						this.saveFuncionalidadeSuccess = false;
						this.activatedRoute.params.subscribe((params) => {
							this.loadEntity(params.id);
						});
						this.cd.detectChanges();
					},
					next: (response) => {
						this.funcionalidadeSendoModificada = false;
						this.perfil = response;
						this.saveFuncionalidadeSuccess = false;
						this.snotifyService.success(
							this.notificacoes.getLabel("editSuccess")
						);
						this.tipoPerfilNaoSelecionado = false;
						this.cd.detectChanges();
					},
				});
			}
		}
	}

	retornaListagemHandler() {
		this.router.navigate(["perfil-acesso"]);
	}

	clickSaveNomeHandler() {
		if (this.perfil && this.perfil.id) {
			this.controlNamePerfil.get("nome").markAsTouched();
			this.controlNamePerfil.get("tipoPerfil").markAsTouched();
			if (this.controlNamePerfil.valid) {
				const perfil: any = {};
				perfil.nome = this.controlNamePerfil.get("nome").value;
				perfil.tipo = this.controlNamePerfil.get("tipoPerfil").value;
				this.saveHandlerDto(perfil).subscribe(() => {
					this.nomeSendoModificado = false;
					this.activatedRoute.params.subscribe((params) => {
						this.loadEntity(params.id);
					});
					this.cd.detectChanges();
				});
			} else {
				this.snotifyService.error(this.notificacoes.getLabel("validCampus"));
			}
		}
	}

	private loadEntity(perfilId) {
		this.perfilService
			.obterPerfil(perfilId)
			.subscribe((response: PefilAcessoDetalhe) => {
				this.perfil = response;
				this.loadFormEdit();
			});
	}

	private loadFormEdit() {
		this.controlNamePerfil.get("nome").setValue(this.perfil.nome);
		this.controlNamePerfil.get("tipoPerfil").setValue(this.perfil.tipo);
		this.controlNamePerfil.get("nome").valueChanges.subscribe(() => {
			this.verificandoModificacaoNome();
		});
		this.controlNamePerfil.get("tipoPerfil").valueChanges.subscribe(() => {
			this.verificandoModificacaoTipo();
		});
		for (const categoria of Object.keys(this.recursoFormGroups)) {
			this.recursoFormGroups[categoria].patchValue(this.perfil.recursos);
			this.recursoFormGroups[categoria].valueChanges.subscribe((value) => {
				this.verifiqueModificacaoRecurso(value);
			});
		}
		for (const categoria of Object.keys(this.funcionalidadeFormGroups)) {
			this.funcionalidadeFormGroups[categoria].patchValue(
				this.perfil.funcionalidades
			);
			this.funcionalidadeFormGroups[categoria].valueChanges.subscribe(
				(value) => {
					this.verifiqueModificacaoFuncionalidade(value);
				}
			);
		}
		this.cd.detectChanges();
	}

	private saveHandlerDto(dto: any): Observable<any> {
		return this.perfilService.alterarPerfilAcesso(this.perfil.id, dto).pipe(
			map((response: any) => {
				this.perfil = response;
				this.snotifyService.success(this.notificacoes.getLabel("editSuccess"));
				this.tipoPerfilNaoSelecionado = false;
				return true;
			})
		);
	}

	private createFormGroupStructure() {
		if (this.session.integracaoZW) {
			for (const categoria of Object.keys(permissaoCategoria)) {
				/**
				 * Criando form group para recursos
				 */
				if (
					permissaoCategoria[categoria] &&
					permissaoCategoria[categoria].recursos.length > 0
				) {
					const formGroupRecurso = new FormGroup({});
					permissaoCategoria[categoria].recursos.forEach((recurso) => {
						formGroupRecurso.addControl(recurso, new FormControl());
					});
					this.recursoFormGroups[categoria] = formGroupRecurso;
				}

				/**
				 * Criando form group para funcionalidades
				 */
				if (
					permissaoCategoria[categoria] &&
					permissaoCategoria[categoria].funcionalidades.length > 0
				) {
					const formGroupFuncionalidade = new FormGroup({});
					permissaoCategoria[categoria].funcionalidades.forEach((item) => {
						formGroupFuncionalidade.addControl(item, new FormControl());
					});
					this.funcionalidadeFormGroups[categoria] = formGroupFuncionalidade;
				}
			}
		} else {
			for (const categoria of Object.keys(permissaoCategoriaIndependente)) {
				/**
				 * Criando form group para recursos
				 */
				if (
					permissaoCategoriaIndependente[categoria] &&
					permissaoCategoriaIndependente[categoria].recursos.length > 0
				) {
					const formGroupRecurso = new FormGroup({});
					permissaoCategoriaIndependente[categoria].recursos.forEach(
						(recurso) => {
							formGroupRecurso.addControl(recurso, new FormControl());
						}
					);
					this.recursoFormGroups[categoria] = formGroupRecurso;
				}

				/**
				 * Criando form group para funcionalidades
				 */
				if (
					permissaoCategoriaIndependente[categoria] &&
					permissaoCategoriaIndependente[categoria].funcionalidades.length > 0
				) {
					const formGroupFuncionalidade = new FormGroup({});
					permissaoCategoriaIndependente[categoria].funcionalidades.forEach(
						(item) => {
							console.log(item);
							formGroupFuncionalidade.addControl(item, new FormControl());
						}
					);
					this.funcionalidadeFormGroups[categoria] = formGroupFuncionalidade;
				}
			}
		}
		this.createFormGroups = true;
	}

	private verifiqueModificacaoRecurso(formGroup) {
		const form = JSON.stringify(formGroup);
		if (form === this.categoriaRecursoJsonInit) {
			this.recursoSendoModificado = false;
		} else {
			this.recursoSendoModificado = true;
		}
	}

	private verifiqueModificacaoFuncionalidade(formGroup) {
		for (const control in formGroup) {
			if (formGroup[control] !== this.perfil.funcionalidades[control]) {
				this.funcionalidadeSendoModificada = true;
				break;
			}
		}
	}

	private verificandoModificacaoNome() {
		if (
			this.controlNamePerfil.get("nome").valid &&
			this.controlNamePerfil.get("nome").value &&
			this.controlNamePerfil.get("nome").value.toString().trim() !==
				this.perfil.nome &&
			this.controlNamePerfil.get("nome").value.toString().trim() !== ""
		) {
			this.nomeSendoModificado = true;
		} else {
			this.nomeSendoModificado = false;
		}
	}

	private verificandoModificacaoTipo() {
		if (
			this.controlNamePerfil.get("tipoPerfil").valid &&
			this.controlNamePerfil.get("tipoPerfil").value &&
			this.controlNamePerfil.get("tipoPerfil").value.toString() !==
				this.perfil.tipo &&
			this.controlNamePerfil.get("tipoPerfil").value.toString() !== ""
		) {
			this.nomeSendoModificado = true;
		} else {
			this.nomeSendoModificado = false;
		}
	}
}
