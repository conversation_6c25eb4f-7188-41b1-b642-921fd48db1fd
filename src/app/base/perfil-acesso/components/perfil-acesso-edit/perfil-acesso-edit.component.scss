@import "src/assets/scss/pacto/plataforma-import.scss";

pacto-cat-accordion {
	display: block;
	margin-top: 30px;
}

pacto-cat-toggle-input {
	display: block;
	margin: 10px 5px;
}

.topo-layout {
	@extend .type-p-small;
	margin-bottom: 30px;
	cursor: pointer;
	display: inline-block;
}

.form-identify {
	display: flex;
	justify-content: flex-start;

	.input-nome {
		flex-grow: 1;
		max-width: 375px;
	}

	.action-form {
		padding: 5px 0px 5px 14px;
	}

	.action-save-perfil {
		@extend .type-btn-bold;
		text-transform: uppercase;
		color: $azulimPri;
		cursor: pointer;
		padding-right: 7px;
	}

	.action-cancel-perfil {
		@extend .type-btn-bold;
		text-transform: uppercase;
		color: $gelo04;
		cursor: pointer;
		padding-left: 7px;
	}

	i {
		font-size: 18px;
		position: relative;
		top: 2px;
	}
}

.container-permissoes {
	padding: 23px 21px 0px 21px;
}

.status-modify {
	@extend .type-caption;
	display: flex;
	justify-content: flex-end;

	.info-status-modify {
		display: flex;
		padding-right: 20px;

		i {
			font-size: 18px;
			padding-right: 12px;
		}
	}

	.action-status {
		padding-right: 10px;
		display: flex;

		.action-status-modify {
			text-transform: uppercase;
			padding-right: 8px;
		}
	}
}

.row-tabs {
	display: flex;
	flex-wrap: wrap;
	border-bottom: 1px solid $gelo02;
}

::ng-deep.tabs {
	border-bottom: 0px !important;
}

.tipo-perfil-nao-selecionado ::ng-deep #input-tipo-perfil {
	border: 1px solid red;
}
