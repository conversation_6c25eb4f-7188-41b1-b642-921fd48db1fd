<pacto-cat-layout-v2>
	<div
		(click)="retornaListagemHandler()"
		class="topo-layout"
		id="retornar-listagem-perfil-acesso">
		<span *ngIf="perfil" i18n="@@perfil-acesso-edit:perfil-acesso">
			Perfil de acesso > {{ perfil.nome }}
		</span>
	</div>

	<div class="form-identify">
		<div class="row" style="align-items: baseline">
			<div class="col-md-6 input-nome">
				<pacto-cat-form-input
					[control]="controlNamePerfil.get('nome')"
					[enableClearInput]="false"
					[errorMsg]="'Nome do perfil deve ser preenchido'"
					[id]="'input-nome-perfil'"
					[placeholder]="'Digite o nome do perfil'"></pacto-cat-form-input>
			</div>
			<div
				[ngClass]="{ 'tipo-perfil-nao-selecionado': tipoPerfilNaoSelecionado }"
				class="col-md-6">
				<pacto-cat-select
					[control]="controlNamePerfil.get('tipoPerfil')"
					[id]="'input-tipo-perfil'"
					[items]="tipoPerfis"
					idKey="id"
					labelKey="nome"></pacto-cat-select>
			</div>
		</div>
		<div *ngIf="nomeSendoModificado" class="action-form">
			<span
				(click)="clickSaveNomeHandler()"
				class="action-save-perfil"
				id="btn-save-input-nome-perfil">
				salvar
				<i class="pct pct-check"></i>
			</span>
			<span
				(click)="clickCancelNomeHandler()"
				class="action-cancel-perfil"
				id="btn-cancel-input-nome-perfil">
				cancelar
				<i class="pct pct-check"></i>
			</span>
		</div>
	</div>

	<div class="row-tabs justify-content-between">
		<div>
			<pacto-cat-tabs-transparent #tabs (activateTab)="loadTabHandler($event)">
				<ng-template label="Agenda" pactoTabTransparent="agenda"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:aluno-edit"
					label="Aluno"
					pactoTabTransparent="aluno"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:aula"
					label="Aula"
					pactoTabTransparent="aula"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:castrados-auxiliares"
					label="Cadastros Auxiliares"
					pactoTabTransparent="cadastro-auxiliares"></ng-template>
				<ng-template label="Cross" pactoTabTransparent="crossfit"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:geral"
					label="Geral"
					pactoTabTransparent="geral"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:gestao-personal"
					label="Gestão de personal"
					pactoTabTransparent="gestao_personal"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:graduacao"
					label="Graduação"
					pactoTabTransparent="graduacao"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:avaliacao-fisica"
					label="Avaliação Física"
					pactoTabTransparent="avaliacao_fisica"></ng-template>
				<ng-template
					i18n-label="@@perfil-acesso-edit:treino"
					label="Treino"
					pactoTabTransparent="treino"></ng-template>
			</pacto-cat-tabs-transparent>
		</div>

		<div>
			<pacto-log [titulo]="nomeLog" [url]="logUrl"></pacto-log>
			<ng-template #nomeLog>
				<span>
					categoria "{{ tabAtiva }}" do perfil
					{{ controlNamePerfil.value.nome }}
				</span>
			</ng-template>
		</div>
	</div>

	<pacto-cat-accordion
		#accordionPermissoes
		*ngIf="apresentarRecurso"
		[id]="'permissao'"
		i18n-title="@@perfil-acesso-edit:title-personalidade"
		title="Permissões">
		<accordion-header>
			<div class="status-modify">
				<div *ngIf="saveRecursoSuccess" class="info-status-modify">
					<i class="pct pct-check-circle"></i>
					<div class="label-info-status">Alterações foram salvas</div>
				</div>

				<div class="action-status">
					<div
						*ngIf="!accordionPermissoes.open"
						class="action-status-modify"
						id="btn-editar-permissao">
						editar
					</div>

					<div
						(click)="clickSaveRecursoHandler($event, tabs.tabId)"
						*ngIf="accordionPermissoes.open && recursoSendoModificado"
						class="action-status-modify"
						i18n="@@perfil-acesso-edit:btn-salvar"
						id="btn-salvar-permissao">
						salvar
					</div>

					<div
						(click)="recursoCancelHandler($event, tabs.tabId)"
						*ngIf="accordionPermissoes.open && recursoSendoModificado"
						class="action-status-modify"
						id="btn-cancelar-permissao">
						cancelar
					</div>
				</div>
			</div>
		</accordion-header>
		<accordion-body>
			<pacto-perfil-permissao-item
				*ngFor="let recurso of permissaoCategoria[tabs.tabId].recursos"
				[formControl]="recursoFormGroups[tabs.tabId].get(recurso)"
				[id]="recurso"
				[label]="
					labelsPermissao.getLabel(recurso)
				"></pacto-perfil-permissao-item>
		</accordion-body>
	</pacto-cat-accordion>

	<pacto-cat-accordion
		#accordionFuncionalidades
		*ngIf="apresentarFuncionalidade"
		[id]="'funcionalidade'"
		i18n-title="@@perfil-acesso-edit:title-funcionalidade-edit"
		title="Funcionalidade">
		<accordion-header>
			<div class="status-modify">
				<div *ngIf="saveFuncionalidadeSuccess" class="info-status-modify">
					<i class="pct pct-check-circle"></i>
					<div class="label-info-status">Alterações foram salvas</div>
				</div>
				<div class="action-status">
					<div
						*ngIf="!accordionFuncionalidades.open"
						class="action-status-modify"
						id="btn-editar-funcionalidade">
						editar
					</div>
					<div
						(click)="clickSaveFuncionalidadeHandler($event, tabs.tabId)"
						*ngIf="
							accordionFuncionalidades.open && funcionalidadeSendoModificada
						"
						class="action-status-modify"
						id="btn-salvar-funcionalidade">
						salvar
					</div>
					<div
						(click)="funcionalidadeCancelHandler($event, tabs.tabId)"
						*ngIf="
							accordionFuncionalidades.open && funcionalidadeSendoModificada
						"
						class="action-status-modify"
						id="btn-cancelar-funcionalidade">
						cancelar
					</div>
				</div>
			</div>
		</accordion-header>
		<accordion-body>
			<pacto-perfil-funcionalidade-item
				*ngFor="
					let funcionalidade of permissaoCategoria[tabs.tabId].funcionalidades
				"
				[formControl]="funcionalidadeFormGroups[tabs.tabId].get(funcionalidade)"
				[id]="funcionalidade"
				[label]="
					funcionalidadesLabel.getLabel(funcionalidade)
				"></pacto-perfil-funcionalidade-item>
		</accordion-body>
	</pacto-cat-accordion>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #notificacoes>
	<span i18n="@@perfil-acesso-edit:perfil-criado" xingling="createSuccess">
		Perfil criado com sucesso.
	</span>
	<span i18n="@@perfil-acesso-edit:perfil-editado" xingling="editSuccess">
		Perfil editado com sucesso.
	</span>
	<span
		i18n="@@perfil-acesso-edit:alert-tipo-perfil"
		xingling="alertTipoPerfil">
		Para salvar a alteração é necessário que o tipo de perfil tenha sido
		selecionado.
	</span>
	<span i18n="@@perfil-acesso-edit:perfil-validCampus" xingling="validCampus">
		Campos obrigatórios não preenchidos.
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #labelsPermissao>
	<!--    <span xingling="musculos" i18n="@@perfil-acesso-edit:cadastro-musculos">Cadastro de Músculos</span>-->
	<!--    <span xingling="grupos_musculares" i18n="@@perfil-acesso-edit:grupos-musculares">Grupos Musculares</span>-->
	<span i18n="@@perfil-acesso-edit:aparelhos" xingling="aparelhos">
		Aparelhos
	</span>
	<span i18n="@@perfil-acesso-edit:atividades" xingling="atividades">
		Atividades do módulo Treino
	</span>
	<span
		i18n="@@perfil-acesso-edit:ficha-pre-definidas"
		xingling="fichas_pre_definidas">
		Fichas predefinidas
	</span>
	<span xingling="programas_predefinidos">Programas predefinidos</span>
	<span
		i18n="@@perfil-acesso-edit:categoria-atividade"
		xingling="categoria_atividade">
		Categoria Atividade
	</span>
	<span i18n="@@perfil-acesso-edit:categoria-ficha" xingling="categoria_fichas">
		Categoria Ficha
	</span>
	<span i18n="@@perfil-acesso-edit:niveis" xingling="niveis">Níveis</span>
	<span
		i18n="@@perfil-acesso-edit:objetivos-pre-definidos"
		xingling="objetivos">
		Objetivos predefinidos
	</span>
	<span i18n="@@perfil-acesso-edit:imagens" xingling="imagens">Imagens</span>
	<span i18n="@@perfil-acesso-edit:label-aluno" xingling="alunos">Alunos</span>
	<span i18n="@@perfil-acesso-edit:notificacao" xingling="notificacoes">
		Notificações
	</span>
	<span i18n="@@perfil-acesso-edit:acompanhar" xingling="acompanhar">
		Acompanhar
	</span>
	<span
		i18n="@@perfil-acesso-edit:configuracoes-empresa"
		xingling="configuracoes_empresa">
		Configurações Empresa
	</span>
	<span
		i18n="@@perfil-acesso:alterar-avaliacao-fisica"
		xingling="lancar_avalicao_retroativa">
		Alterar Avaliação Física
	</span>
	<span i18n="@@perfil-acesso-edit:adcionar-aluno" xingling="add_aluno">
		Adicionar aluno
	</span>
	<span i18n="@@perfil-acesso-edit:tipo-agendamento" xingling="tipo_evento">
		Tipo de Agendamento
	</span>
	<span i18n="@@perfil-acesso-edit:perfil acesso" xingling="perfil_usuario">
		Perfil de acesso
	</span>
	<span
		i18n="@@perfil-acesso-edit:disponibilidade"
		xingling="agenda_disponibilidade">
		Disponibilidade
	</span>
	<span
		i18n="@@perfil-acesso-edit:contato-interpessoal"
		xingling="contato_interpessoal">
		Contato Interpessoal
	</span>
	<span
		i18n="@@perfil-acesso-edit:prescricao-treino"
		xingling="prescricao_treino">
		Prescrição de Treino
	</span>
	<span i18n="@@perfil-acesso-edit:renovar-treino" xingling="renovar_treino">
		Renovar Treino
	</span>
	<span i18n="@@perfil-acesso-edit:revisao-treino" xingling="revisao_treino">
		Revisão de Treino
	</span>
	<span
		i18n="@@perfil-acesso-edit:avaliacao-fisica"
		xingling="avaliacao_fisica">
		Avaliação Física
	</span>
	<span i18n="@@perfil-acesso-edit:usuario" xingling="usuarios">Usuários</span>
	<span
		i18n="@@perfil-acesso-edit:programa-de-treino"
		xingling="programa_treino">
		Programa de treino
	</span>
	<span i18n="@@perfil-acesso-edit:span-empresa" xingling="empresa">
		Empresa
	</span>
	<span i18n="@@perfil-acesso-edit:anamnese" xingling="anamnese">Anamnese</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #funcionalidadesLabel>
	<span
		xingling="alterar_professor_treino"
		i18n="@@perfil-acesso-edit:alterar-professor">
		Alterar professor Montou Treino
	</span>
	<span
		xingling="visualizar_dados_contato_aluno"
		i18n="@@perfil-acesso-edit:visualizar-contato">
		Visualizar dados de contato do aluno
	</span>
	<span
		xingling="ver_alunos_outras_carteiras"
		i18n="@@perfil-acesso-edit:alunos-outras-carteira">
		Visualizar alunos de outras carteiras
	</span>
	<span
		xingling="ver_agenda_outros_professores"
		i18n="@@perfil-acesso:visualizar-agenda">
		Visualizar a Agenda de outros professores
	</span>
	<span
		xingling="ver_auditoria_entidade"
		i18n="@@perfil-acesso-edit:visualizar-log">
		Visualizar Log de Alterações
	</span>
	<span xingling="gestao_personal" i18n="@@perfil-acesso-edit:gestao-personal">
		Gestão de Personal
	</span>
	<span xingling="liberar_check_in" i18n="@@perfil-acesso-edit:forcar-check">
		Forçar Check-In
	</span>
	<span
		xingling="acompanhar_personal"
		i18n="@@perfil-acesso-edit:acompanhar-personal">
		Acompanhar personal
	</span>
	<span
		xingling="gestao_creditos_personal"
		i18n="@@perfil-acesso-edit:visualizar-gestao-credito">
		Visualizar a gestão de créditos
	</span>
	<span xingling="editar_excluir_aula" i18n="@@perfil-acesso-edit:excluir-aula">
		Editar / excluir aula
	</span>
	<span
		xingling="tornar_ficha_predefinida"
		i18n="@@perfil-acesso:tornar-ficha-predefinida">
		Tornar uma Ficha Predefinida
	</span>
	<span
		xingling="tela_prescricao_treino"
		i18n="@@perfil-acesso:tela_prescricao_treino">
		Prescrever treino para colaboradores
	</span>
	<span
		xingling="tornar_programa_predefinido"
		i18n="@@perfil-acesso:tornar-programa-predefinido">
		Tornar um Programa Predefinido
	</span>
	<span xingling="fazer_check_out" i18n="@@perfil-acesso-edit:fazer-check">
		Fazer Check-out
	</span>
	<span xingling="cadastro_aulas" i18n="@@perfil-acesso:casdastrar aulas">
		Cadastrar aulas
	</span>
	<span xingling="excluir_aulas_dia" i18n="@@perfil-acesso:excluir-aula">
		Excluir aula diária
	</span>
	<span xingling="inserir_aluno" i18n="@@perfil-acesso:inserir-aluno">
		Inserir aluno na aula
	</span>
	<span xingling="excluir_aluno" i18n="@@perfil-acesso:excluir-aluno">
		Excluir aluno da aula
	</span>
	<span xingling="agenda" i18n="@@perfil-acesso-edit:agenda-aulas">
		Agenda de aulas
	</span>
	<span
		xingling="inserir_aluno_aula_iniciada"
		i18n="@@perfil-acesso:inserir-aluno-iniciada">
		Inserir aluno numa aula que já está iniciada/realizada
	</span>

	<span xingling="editar_aulas_dia" i18n="@@perfil-acesso:editar_aulas_dia">
		Editar aula diária
	</span>
	<span
		xingling="gestao_aula_cheia"
		i18n="@@perfil-acesso-edit:gestao-aula-cheia">
		Gestão Aula Cheia
	</span>
	<span
		xingling="alterar_professor_aula"
		i18n="@@perfil-acesso:alterar-professor-aula">
		Alterar professor aula
	</span>
	<span
		xingling="alterar_professor_aula_iniciada"
		i18n="@@perfil-acesso:professor-aula-iniciada">
		Alterar professor aula iniciada
	</span>
	<span
		xingling="ver_gestao_geral"
		i18n="@@perfil-acesso-edit:visualizar-gestao">
		Visualizar gestão geral
	</span>
	<span xingling="trocar_professor" i18n="@@perfil-acesso:trocar-professor">
		Trocar professor do aluno
	</span>
	<span
		xingling="atribuir_programa_treino_pre_definido"
		i18n="@@perfil-acesso-edit:atribuir-programa-de-treino-pre-definido">
		Atribuir somente programa pré-definido
	</span>
	<span
		xingling="alterar_senha_usuarios"
		i18n="@@perfil-acesso:alterar-senha-usuarios">
		Alterar senha Usuários
	</span>
	<span xingling="colaboradores">Colaboradores</span>
	<span xingling="permissao_excluir_aluno">Excluir Cadastro do Aluno</span>
	<span
		xingling="ver_bi_outros_professores"
		i18n="@@perfil-acesso:permitir-bi-professores">
		Permitir ver BI de outros Professores
	</span>
	<span
		xingling="editar_nome_atividade_ficha"
		i18n="@@perfil-acesso:permitir-edicao-ficha">
		Permitir a edição do nome da atividade na ficha
	</span>
	<span xingling="grafico_bi" i18n="@@perfil-acesso:permitir-grafico">
		Permitir a visualização do Gráfico de Gestão
	</span>
	<span
		xingling="ranking_professores"
		i18n="@@perfil-acesso:permitir-visualizacao-raking">
		Permitir a visualização do Ranking de professores
	</span>
	<span
		xingling="configuracoes_do_ranking"
		i18n="@@perfil-acesso:configuracoes_do_ranking">
		Permitir acessar as configurações do Ranking
	</span>
	<span
		xingling="ver_gestao_geral"
		i18n="@@perfil-acesso:visualizar-gestao-geral">
		Visualizar gestão geral
	</span>
	<span xingling="tvgestor" i18n="@@perfil-acesso:permitir-tv-gestor">
		Permitir a visualização da TV Gestor
	</span>
	<span xingling="benchmark">Benchmark</span>
	<span xingling="tipo_benchmark">Tipo Benchmark</span>
	<span xingling="wod">WOD</span>
	<span xingling="ranking_wod">Ranking Cross</span>
	<span
		xingling="alterar_horario_inicio_check_personal"
		i18n="@@perfil-acesso:alterar-data-hora">
		Alterar data/horário de início/fim do check-in/checkout
	</span>
	<span xingling="cadastrar_tipo_wod" i18n="@@perfil-acesso:cadastrar-tipo">
		Cadastrar Tipo WOD
	</span>
	<span xingling="excluir_anexo_aluno" i18n="@@perfil-acesso:excluir-anexos">
		Excluir anexos
	</span>
	<span xingling="atividades_wod" i18n="@@perfil-acesso:cadastrar-atividades">
		Cadastrar Atividades WOD
	</span>
	<span xingling="aparelhos_wod" i18n="@@perfil-acesso:cadastrar wod">
		Cadastrar Aparelho WOD
	</span>
	<span
		xingling="permissao_excluir_aluno_vinculado"
		i18n="@@perfil-acesso:excluir-cadastro-ignorando">
		Excluir Cadastro do Aluno ignorando vínculo
	</span>
	<span
		xingling="trocar_metodo_execucao"
		i18n="@@perfil-acesso:metodo-execucao">
		Método de execução
	</span>
	<span xingling="modalidades" i18n="@@perfil-acesso:cadastrar-modalidades">
		Cadastrar modalidades
	</span>
	<span xingling="serie" i18n="@@perfil-acesso:cadastrar-series">
		Cadastrar series
	</span>
	<span xingling="ambientes" i18n="@@perfil-acesso:cadastrar-ambientes">
		Cadastrar ambientes
	</span>
	<span
		xingling="visualizar_todas_aulas_app"
		i18n="@@perfil-acesso:visualizar-professor-app">
		Visualizar aulas e turmas de todos os professores no APP
	</span>
	<span
		xingling="usuario_marcar_antecedencia"
		i18n="@@perfil-acesso:marcar-aula-antecedencia">
		Marcar e desmarcar aula com antecedência, sem validar configuração do
		sistema
	</span>
	<span
		xingling="desvincular_usuario"
		i18n="@@perfil-acesso:desvincular_usuario">
		Desvincular usuário
	</span>
	<span
		xingling="avaliacao_media_alunos"
		i18n="@@perfil-acesso:avaliacao_media_aluno">
		Visualizar detalhes de “Avaliação do treino do aluno”
	</span>
	<span
		xingling="visualizar_wod_outras_unidades"
		i18n="@@perfil-acesso:avaliacao_media_aluno">
		Visualizar WOD de outras unidades
	</span>
	<span xingling="bi_cross" i18n="@@perfil-acesso:bi_cross">
		BI do módulo Cross
	</span>
	<span xingling="monitor" i18n="@@perfil-acesso:monitor">Monitor</span>
	<span
		xingling="atividades_graduacao"
		i18n="@@perfil-acesso:atividades_graduacao">
		Atividades do módulo Graduação
	</span>
	<span
		xingling="ficha_tecnica_graduacao"
		i18n="@@perfil-acesso:ficha_tecnica_graduacao">
		Ficha técnica
	</span>
	<span
		xingling="avaliacao_de_progresso_graduacao"
		i18n="@@perfil-acesso:avaliacao_de_progresso_graduacao">
		Avaliação de progresso
	</span>
	<span xingling="bi_agenda" i18n="@@perfil-acesso:bi_agenda">
		BI do módulo Agenda
	</span>
	<span xingling="agenda_de_aulas" i18n="@@perfil-acesso:agenda_de_aulas">
		Agenda de Aulas
	</span>
	<span xingling="agenda_de_servicos" i18n="@@perfil-acesso:agenda_de_servicos">
		Agenda de Serviços
	</span>
	<span xingling="relatorios_agenda" i18n="@@perfil-acesso:relatorios_agenda">
		Relatórios do módulo Agenda
	</span>
	<span xingling="cadastros_agenda" i18n="@@perfil-acesso:cadastros_agenda">
		Cadastros do módulo Agenda
	</span>
	<span
		xingling="bi_avaliacao_fisica"
		i18n="@@perfil-acesso:bi_avaliacao_fisica">
		BI do módulo Avaliação Física
	</span>
	<span
		xingling="cadastros_avaliacao_fisica"
		i18n="@@perfil-acesso:cadastros_avaliacao_fisica">
		Cadastros do módulo Avaliação Física
	</span>
	<span
		xingling="criar_avaliacao_fisica"
		i18n="@@perfil-acesso:criar_avaliacao_fisica">
		Criar avaliação física
	</span>
	<span
		xingling="visualizar_avaliacao_fisica"
		i18n="@@perfil-acesso:visualizar_avaliacao_fisica">
		Visualizar avaliação física
	</span>
	<span xingling="relatorios_treino" i18n="@@perfil-acesso:relatorios_treino">
		Relatórios
	</span>
	<span xingling="cadastros_treino" i18n="@@perfil-acesso:cadastros_treino">
		Cadastros
	</span>
	<span
		xingling="utilizar_modulo_cross"
		i18n="@@perfil-acesso-edit:utilizar_modulo_cross">
		Utilizar módulo Cross
	</span>
	<span
		xingling="utilizar_modulo_graduacao"
		i18n="@@perfil-acesso-edit:utilizar_modulo_graduacao">
		Utilizar módulo Graduação
	</span>
	<span
		xingling="utilizar_modulo_agenda"
		i18n="@@perfil-acesso-edit:utilizar_modulo_agenda">
		Utilizar módulo Agenda
	</span>
	<span
		xingling="utilizar_modulo_avaliacao_fisica"
		i18n="@@perfil-acesso:utilizar-modulo-avaliacao-fisica">
		Utilizar módulo Avaliação Física
	</span>
	<span
		xingling="enviar_treino_em_massa"
		i18n="@@perfil-acesso:avaliacao_media_aluno">
		Enviar treino em massa
	</span>
	<span xingling="treino_em_casa" i18n="@@perfil-acesso:treino_em_casa">
		Treino em casa
	</span>
	<span
		xingling="prescricao_de_treino"
		i18n="@@perfil-acesso:prescricao_de_treino">
		Prescrição de treino
	</span>
	<span
		xingling="prescricao_de_treino_por_ia"
		i18n="@@perfil-acesso:prescricao_de_treino_por_ia">
		Permitir prescrição de treino por IA
	</span>
</pacto-traducoes-xingling>
