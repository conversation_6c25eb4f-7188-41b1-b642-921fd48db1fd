import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

import { GrupoMuscularRoutingModule } from "./grupo-muscular.routing.module";
import { BaseSharedModule } from "@base-shared/base-shared.module";

import { GrupoMuscularListaComponent } from "./components/grupo-muscular-lista/grupo-muscular-lista.component";
import { GrupoMuscularEditComponent } from "./components/grupo-muscular-edit/grupo-muscular-edit.component";

@NgModule({
	imports: [
		CommonModule,
		NgbModule,
		BaseSharedModule,
		GrupoMuscularRoutingModule,
	],
	declarations: [GrupoMuscularListaComponent, GrupoMuscularEditComponent],
	entryComponents: [GrupoMuscularEditComponent],
})
export class GrupoMuscularModule {}
