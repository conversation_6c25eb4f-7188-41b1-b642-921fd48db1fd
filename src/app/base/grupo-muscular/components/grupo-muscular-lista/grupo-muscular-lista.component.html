<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'GRUPO_MUSCULAR'
		}"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="false"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@crud:grupos-musculares:titulo">
		Grupos Musculares Cadastradas
	</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@crud:grupos-musculares:descricao">
		Gerencie os grupos musculares.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span i18n="@@crud:grupos-musculares:tabela:nome">Nome</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-tables:acoes">Ações</span>
</ng-template>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>

<span
	#removeModalTitle
	[hidden]="true"
	i18n="@@crud:grupos-musculares:remove-modal:title">
	Remover Grupo Muscular ?
</span>
<span
	#removeModalBody
	[hidden]="true"
	i18n="@@crud:grupos-musculares:remove-modal:body">
	Deseja remover o grupo muscular {{ nomeGrupoMuscular }}?
</span>
<span
	#removeSuccess
	[hidden]="true"
	i18n="@@crud:grupos-musculares:remove-message">
	Grupo muscular removido com sucesso!
</span>
