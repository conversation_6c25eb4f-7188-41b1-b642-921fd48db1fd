import { Component, OnInit, ViewChild } from "@angular/core";

import { ApiResponseList } from "@base-core/rest/rest.model";
import { TreinoApiGrupoMuscularService } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { ModalService } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	GridFilterConfig,
	RelatorioComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoRecursoNome, Nivel } from "treino-api";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-grupo-muscular-lista",
	templateUrl: "./grupo-muscular-lista.component.html",
	styleUrls: ["./grupo-muscular-lista.component.scss"],
})
export class GrupoMuscularListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;

	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	constructor(
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private rest: RestService,
		private router: Router
	) {}

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});
	data: ApiResponseList<Nivel> = {
		content: [],
	};

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	permissoesGruposMusculares;

	itemToRemove;
	loading = false;
	ready = false;
	nomeGrupoMuscular: "";
	integracaoZW = false;

	ngOnInit() {
		this.carrgarPermissoes();
		this.integracaoZW = this.sessionService.integracaoZW;
		this.configTable();
		this.ready = true;
	}

	carrgarPermissoes() {
		this.permissoesGruposMusculares = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.GRUPOS_MUSCULARES
		);
	}

	removeHandler(item) {
		this.nomeGrupoMuscular = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					const idToRemove = this.data.content.findIndex(
						(itemI) => itemI.id === item.id
					);
					this.grupoMuscularService
						.removerGrupoMuscular(item.id)
						.subscribe(() => {
							this.snotifyService.success(removeSuccess);
							this.data.content.splice(idToRemove, 1);
							this.fetchData();
						});
				})
				.catch(() => {});
		});
	}

	btnClickHandler() {
		this.router.navigate(
			["treino", "cadastros", "grupo-muscular", "adicionar"],
			{ queryParamsHandling: "merge" }
		);
	}

	btnEditHandler(item) {
		this.router.navigate(["treino", "cadastros", "grupo-muscular", item.id], {
			queryParamsHandling: "merge",
		});
	}

	private configTable() {
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("grupos-musculares"),
			logUrl: this.rest.buildFullUrl("log/grupos-musculares"),
			quickSearch: true,
			rowClick: this.permissoesGruposMusculares.editar,
			buttons: !this.permissoesGruposMusculares.incluir
				? null
				: {
						conteudo: this.buttonName,
						nome: "add",
						id: "adicionarGruposMusculares",
				  },
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					showIconFn: (row) => this.permissoesGruposMusculares.editar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.permissoesGruposMusculares.excluir,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	private fetchData() {
		this.tableData.reloadData();
	}
}
