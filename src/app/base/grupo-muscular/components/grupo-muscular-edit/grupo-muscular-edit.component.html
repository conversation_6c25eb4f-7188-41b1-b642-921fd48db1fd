<ng-template #pageTitle i18n="@@crud:grupos-musculares:create-edit:title">
	{operation, select, edit {Editar Grupo Muscular} create {Criar Grupo
	Muscular}}
</ng-template>

<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'GRUPO_MUSCULAR',
			menuLink: ['treino', 'cadastros', 'grupo-muscular']
		}"></pacto-breadcrumbs>

	<pacto-title-card [title]="pageTitle">
		<div class="wrapper">
			<div class="row">
				<div class="col-md-6">
					<pacto-input
						[id]="'nome-grupo-muscular-input'"
						[name]="'nome'"
						[pactoFormGroup]="formGroup"
						[validators]="[Validators.required, Validators.minLength(3)]"
						i18n-label="@@crud:grupos-musculares:create-edit:nome"
						i18n-mensagem="@@crud:grupos-musculares:create-edit:nome:erro"
						i18n-placeholder="
							@@crud:grupos-musculares:create-edit:nome:placeholder"
						label="Nome"
						mensagem="Fornecer um nome com pelo menos 3 digitos."
						placeholder="Nome do grupo muscular"></pacto-input>
				</div>
				<div class="col-md-6">
					<pacto-lista-insert-select-filter
						#atividadesComp
						[id]="'select-atividade'"
						[items]="atividades"
						[labelKey]="'nome'"
						i18n-placeholder="
							@@crud:grupos-musculares:create-edit:atividades:placeholder"
						i18n-title="@@crud:grupos-musculares:create-edit:atividades:titulo"
						placeholder="Relacione outras atividades"
						title="Atividades Relacionados"></pacto-lista-insert-select-filter>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<div class="header-row">
							<div class="title">
								<label
									i18n="@@crud:grupos-musculares:perimetros-relacionados:label">
									Perímetros Relacionados
								</label>
								<span *ngIf="perimetros && perimetros.length > 0">
									({{ perimetros.length }})
								</span>
							</div>
							<div
								(click)="clearSelection()"
								class="action"
								i18n="@@crud:grupos-musculares:perimetros-relacionados:limpar">
								Limpar
							</div>
						</div>
						<select
							(change)="addPerimetroHandler()"
							[formControl]="perimetroControl"
							class="form-control form-control-sm">
							<option
								i18n="
									@@crud:grupos-musculares:option-enum:relacione-outros-perimetros"
								value="">
								Relacione outros perimetros
							</option>
							<option
								*ngFor="let perimetro of perimetroEnums"
								value="{{ perimetro.id }}">
								<ng-container
									*ngTemplateOutlet="
										optionsPerimetro;
										context: { optionPerimetro: perimetro.id }
									"></ng-container>
							</option>
						</select>
					</div>
					<div class="list-wrapper">
						<div
							[maxHeight]="'200px'"
							[ngClass]="{ empty: !perimetros || perimetros.length === 0 }"
							pactoCatSmoothScroll>
							<table class="table">
								<tbody>
									<tr *ngFor="let perimetro of perimetros; let index = index">
										<td>
											<ng-container
												*ngTemplateOutlet="
													optionsPerimetro;
													context: { optionPerimetro: perimetro.id }
												"></ng-container>
										</td>
										<td
											(click)="removerPerimetroHandler(index)"
											class="action-column">
											<i class="fa fa-trash-o"></i>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="actions">
			<button
				(click)="submitHandler()"
				class="btn btn-primary"
				i18n="@@buttons:salvar"
				id="btn-add-grupo-muscular">
				Salvar
			</button>
			<button
				(click)="cancelHandler()"
				class="btn btn-secondary"
				i18n="@@buttons:cancelar">
				Cancelar
			</button>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<span
	#mensagemSucesso
	[hidden]="true"
	i18n="@@crud:grupos-musculares:create-edit:mensagem:sucesso">
	Grupo muscular configurado com sucesso.
</span>

<ng-template #optionsPerimetro let-optionPerimetro="optionPerimetro">
	<ng-container [ngSwitch]="optionPerimetro">
		<option
			*ngSwitchCase="'ANTEBRACO_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:antebraco-esquerdo">
			Antebraço esquerdo
		</option>
		<option
			*ngSwitchCase="'ANTEBRACO_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:antebraco-direito">
			Antebraço direito
		</option>
		<option
			*ngSwitchCase="'BRACO_RELAXADO_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:braco-relaxado-direito">
			Braço relaxado esquerdo
		</option>
		<option
			*ngSwitchCase="'BRACO_RELAXADO_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:braco-relaxado-esquerdo">
			Braço relaxado direito
		</option>
		<option
			*ngSwitchCase="'BRACO_CONTRAIDO_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:braco-contraido-esquerdo">
			Braço contraido esquerdo
		</option>
		<option
			*ngSwitchCase="'BRACO_CONTRAIDO_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:braco-contraido-direito">
			Braço contraido direito
		</option>
		<option
			*ngSwitchCase="'COXA_DISTAL_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:coxa-distal-direita">
			Coxa distal direita
		</option>
		<option
			*ngSwitchCase="'COXA_DISTAL_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:coxa-distal-esquerda">
			Coxa distal esquerda
		</option>
		<option
			*ngSwitchCase="'COXA_MEDIAL_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:coxa-medial-direita">
			Coxa média direita
		</option>
		<option
			*ngSwitchCase="'COXA_MEDIAL_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:coxa-medial-esquerda">
			Coxa média esquerda
		</option>
		<option
			*ngSwitchCase="'COXA_PROXIMAL_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:coxa-proximal-direita">
			Coxa proximal direita
		</option>
		<option
			*ngSwitchCase="'COXA_PROXIMAL_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:coxa-proximal-esquerda">
			Coxa proximal esquerda
		</option>
		<option
			*ngSwitchCase="'PANTURRILHA_DIR'"
			i18n="@@crud:grupos-musculares:option-enum:panturrilha-direita">
			Panturrilha direita
		</option>
		<option
			*ngSwitchCase="'PANTURRILHA_ESQ'"
			i18n="@@crud:grupos-musculares:option-enum:panturrilha-esquerda">
			Panturrilha esquerda
		</option>
		<option
			*ngSwitchCase="'PESCOCO'"
			i18n="@@crud:grupos-musculares:option-enum:pescoco">
			Pescoço
		</option>
		<option
			*ngSwitchCase="'OMBRO'"
			i18n="@@crud:grupos-musculares:option-enum:ombro">
			Ombro
		</option>
		<option
			*ngSwitchCase="'TORAX'"
			i18n="@@crud:grupos-musculares:option-enum:torax">
			Torax
		</option>
		<option
			*ngSwitchCase="'QUADRIL'"
			i18n="@@crud:grupos-musculares:option-enum:quadril">
			Quadril
		</option>
		<option
			*ngSwitchCase="'CINTURA'"
			i18n="@@crud:grupos-musculares:option-enum:cintura">
			Cintura
		</option>
		<option
			*ngSwitchCase="'CIRCUNFERENCIA_ABDOMINAL'"
			i18n="@@crud:grupos-musculares:option-enum:circunferencia-abdominal">
			Circunferência abdominal
		</option>
		<option
			*ngSwitchCase="'GLUTEO'"
			i18n="@@crud:grupos-musculares:option-enum:gluteo">
			Gluteo
		</option>
	</ng-container>
</ng-template>
<span #nomeError [hidden]="true">Campos obrigatórios não preenchidos.</span>
