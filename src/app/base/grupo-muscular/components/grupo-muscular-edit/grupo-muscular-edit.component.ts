import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { InputComponent, ListaInsertSelectFilterComponent } from "old-ui-kit";
import {
	TreinoApiAtividadeService,
	TreinoApiGrupoMuscularService,
	GrupoMuscularEdit,
	AtividadeBase,
	GrupoMuscular,
	Musculo,
	TreinoApiMusculoService,
} from "treino-api";

@Component({
	selector: "pacto-grupo-muscular-edit",
	templateUrl: "./grupo-muscular-edit.component.html",
	styleUrls: ["./grupo-muscular-edit.component.scss"],
})
export class GrupoMuscularEditComponent implements OnInit {
	@ViewChild("mensagemSucesso", { static: true }) mensagemSucesso;
	@ViewChild("inputNome", { static: false }) inputNome: InputComponent;
	@ViewChild("atividadesComp", { static: true })
	atividadesComp: ListaInsertSelectFilterComponent;
	@ViewChild("musculosComp", { static: false })
	musculosComp: ListaInsertSelectFilterComponent;
	@ViewChild("perimetrosComp", { static: false })
	perimetrosComp: ListaInsertSelectFilterComponent;
	@ViewChild("nomeError", { static: true }) nomeError;

	formGroup: FormGroup = new FormGroup({});
	perimetroControl: FormControl = new FormControl("");
	entity: GrupoMuscular;
	operation: string;
	perimetros = [];

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private snotifyService: SnotifyService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private atividadesService: TreinoApiAtividadeService,
		private musculoService: TreinoApiMusculoService
	) {}

	musculos: Array<Musculo> = [];
	atividades: Array<AtividadeBase> = [];

	perimetroEnums: Array<any> = [
		{ id: "ANTEBRACO_ESQ", nome: "Antebraço esquerdo" },
		{ id: "ANTEBRACO_DIR", nome: "Antebraço direito" },
		{ id: "BRACO_RELAXADO_ESQ", nome: "Braço relaxado esquerdo" },
		{ id: "BRACO_RELAXADO_DIR", nome: "Braço relaxado direito" },
		{ id: "BRACO_CONTRAIDO_ESQ", nome: "Braço contraido esquerdo" },
		{ id: "BRACO_CONTRAIDO_DIR", nome: "Braço contraido direito" },
		{ id: "COXA_DISTAL_DIR", nome: "Coxa distal direita" },
		{ id: "COXA_DISTAL_ESQ", nome: "Coxa distal esquerda" },
		{ id: "COXA_MEDIAL_DIR", nome: "Coxa média direita" },
		{ id: "COXA_MEDIAL_ESQ", nome: "Coxa média esquerda" },
		{ id: "COXA_PROXIMAL_DIR", nome: "Coxa proximal direita" },
		{ id: "COXA_PROXIMAL_ESQ", nome: "Coxa proximal esquerda" },
		{ id: "PANTURRILHA_DIR", nome: "Panturrilha direita" },
		{ id: "PANTURRILHA_ESQ", nome: "Panturrilha esquerda" },
		{ id: "PESCOCO", nome: "Pescoço" },
		{ id: "OMBRO", nome: "Ombro" },
		{ id: "TORAX", nome: "Torax" },
		{ id: "QUADRIL", nome: "Quadril" },
		{ id: "CINTURA", nome: "Cintura" },
		{ id: "CIRCUNFERENCIA_ABDOMINAL", nome: "Circunferência abdominal" },
		{ id: "GLUTEO", nome: "Gluteo" },
	];

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}
		});
	}

	private loadEntities(id) {
		this.musculoService.obterTodosMusculos().subscribe((dados) => {
			this.musculos = dados;
		});
		this.atividadesService.obterTodasAtividades("false").subscribe((dados) => {
			this.atividades = dados;
			this.atividades.map((atividade: any, index: number) => {
				atividade.index = index;
			});
		});
		if (id) {
			this.grupoMuscularService.obterGrupoMuscular(id).subscribe((dado) => {
				this.entity = dado;
				this.loadForm();
			});
		}
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.entity.nome);
		this.atividadesComp.setSelectedItens(this.entity.atividades);
		this.populandoPerimetrosSelecionados();
	}

	cancelHandler() {
		this.router.navigate(["treino", "cadastros", "grupo-muscular"], {
			queryParamsHandling: "merge",
		});
	}

	submitHandler() {
		this.markAsTouched();
		if (this.entity) {
			this.udpateHandler();
		} else {
			this.createHandler();
		}
	}

	private udpateHandler() {
		const mensagem = this.mensagemSucesso.nativeElement.innerHTML;
		if (this.formGroup.valid) {
			const data = this.fetchFormData();
			this.grupoMuscularService.atualizarGrupoMuscular(data).subscribe(() => {
				this.snotifyService.success(mensagem);
				this.router.navigate(["treino", "cadastros", "grupo-muscular"], {
					queryParamsHandling: "merge",
				});
			});
		} else {
			const nomeError = this.nomeError.nativeElement.innerHTML;
			this.snotifyService.error(nomeError);
		}
	}

	private createHandler() {
		const mensagem = this.mensagemSucesso.nativeElement.innerHTML;
		if (this.formGroup.valid) {
			const data = this.fetchFormData();
			this.grupoMuscularService.criarGrupoMuscular(data).subscribe(() => {
				this.snotifyService.success(mensagem);
				this.router.navigate(["treino", "cadastros", "grupo-muscular"], {
					queryParamsHandling: "merge",
				});
			});
		} else {
			const nomeError = this.nomeError.nativeElement.innerHTML;
			this.snotifyService.error(nomeError);
		}
	}

	private fetchFormData() {
		const formData: GrupoMuscularEdit = {
			nome: this.formGroup.get("nome").value,
			atividadeIds: this.atividadesComp.getSelectedIds(),
			perimetros: this.getSelectedPerimetro(),
			musculoIds: [],
		};

		if (this.entity) {
			formData.id = this.entity.id;
		}

		return formData;
	}

	getSelectedPerimetro() {
		const perimetros: Array<string> = [];
		for (const perimetro of this.perimetros) {
			perimetros.push(perimetro.id);
		}
		return perimetros;
	}

	addPerimetroHandler() {
		for (let i = 0; i < this.perimetroEnums.length; i++) {
			if (this.perimetroEnums[i].id === this.perimetroControl.value) {
				this.perimetros.push(this.perimetroEnums[i]);
				this.perimetroEnums.splice(i, 1);
				this.perimetroControl.setValue("");
			}
		}
	}

	removerPerimetroHandler(index) {
		this.perimetroEnums.push(this.perimetros[index]);
		this.perimetros.splice(index, 1);
		this.perimetroControl.setValue("");
	}

	clearSelection() {
		for (const perimetro of this.perimetros) {
			this.perimetroEnums.push(perimetro);
		}
		this.perimetros = [];
		this.perimetroControl.setValue("");
	}

	populandoPerimetrosSelecionados() {
		for (const perimetroBack of this.entity.perimetros) {
			for (let i = 0; i < this.perimetroEnums.length; i++) {
				if (perimetroBack === this.perimetroEnums[i].id) {
					this.perimetros.push(this.perimetroEnums[i]);
					this.perimetroEnums.splice(i, 1);
				}
			}
		}
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
	}

	get Validators() {
		return Validators;
	}
}
