import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { GrupoMuscularListaComponent } from "./components/grupo-muscular-lista/grupo-muscular-lista.component";
import { GrupoMuscularEditComponent } from "./components/grupo-muscular-edit/grupo-muscular-edit.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const recurso = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.GRUPOS_MUSCULARES,
	[
		PerfilRecursoPermissoTipo.CONSULTAR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const routes: Routes = [
	{
		path: "",
		component: GrupoMuscularListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: GrupoMuscularEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "adicionar",
		component: GrupoMuscularEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class GrupoMuscularRoutingModule {}
