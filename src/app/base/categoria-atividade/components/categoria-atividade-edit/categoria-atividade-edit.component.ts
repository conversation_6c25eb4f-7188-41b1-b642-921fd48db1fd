import { Component, OnInit, ViewChild } from "@angular/core";
import { FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";
import { InputComponent } from "old-ui-kit";
import {
	CategoriaAtividade,
	CategoriaAtividadeEdit,
	TreinoApiCategoriaAtividadeService,
} from "treino-api";

@Component({
	selector: "pacto-categoria-atividade-edit",
	templateUrl: "./categoria-atividade-edit.component.html",
	styleUrls: ["./categoria-atividade-edit.component.scss"],
})
export class CategoriaAtividadeEditComponent implements OnInit {
	@ViewChild("mensagemSucesso", { static: true }) mensagemSucesso;
	@ViewChild("inputNome", { static: false }) inputNome: InputComponent;
	@ViewChild("nomeError", { static: true }) nomeError;

	formGroup: FormGroup = new FormGroup({});
	entity: CategoriaAtividade;
	operation: string;

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private snotifyService: SnotifyService,
		private categoriaAtividadeService: TreinoApiCategoriaAtividadeService
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
			}
		});
	}

	private loadEntities(id) {
		if (id) {
			this.categoriaAtividadeService
				.obterCategoriaAtividade(id)
				.subscribe((dado) => {
					this.entity = dado;
					console.log(this.entity);
					this.loadForm();
				});
		}
	}

	private loadForm() {
		this.formGroup.get("nome").setValue(this.entity.nome);
	}

	cancelHandler() {
		this.router.navigate(["treino", "cadastros", "categoria-atividades"], {
			queryParamsHandling: "merge",
		});
	}

	submitHandler() {
		this.markAsTouched();
		if (this.entity) {
			this.udpateHandler();
		} else {
			this.createHandler();
		}
	}

	private udpateHandler() {
		if (this.formGroup.valid) {
			const data = this.fetchFormData();
			this.categoriaAtividadeService
				.atualizarCategoriaAtividade(data)
				.subscribe(
					(response) => {
						const mensagem = this.mensagemSucesso.nativeElement.innerHTML;
						this.snotifyService.success(mensagem);
						this.atualizarPagina();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta.error && err.meta.message) {
							this.snotifyService.error(err.meta.message);
						}
					}
				);
		} else {
			const nomeError = this.nomeError.nativeElement.innerHTML;
			this.snotifyService.error(nomeError);
		}
	}

	private createHandler() {
		if (this.formGroup.valid) {
			const data = this.fetchFormData();
			this.categoriaAtividadeService.criarCategoriaAtividade(data).subscribe(
				(response) => {
					const mensagem = this.mensagemSucesso.nativeElement.innerHTML;
					this.snotifyService.success(mensagem);
					this.atualizarPagina();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta.error && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
		} else {
			const nomeError = this.nomeError.nativeElement.innerHTML;
			this.snotifyService.error(nomeError);
		}
	}

	private fetchFormData() {
		const formData: CategoriaAtividadeEdit = {
			nome: this.formGroup.get("nome").value,
		};

		if (this.entity) {
			formData.id = this.entity.id;
		}

		return formData;
	}

	private markAsTouched() {
		this.formGroup.get("nome").markAsTouched();
	}

	get Validators() {
		return Validators;
	}

	atualizarPagina() {
		this.router.navigate(["adm"]).then(() => {
			this.router.navigate(["treino", "cadastros", "categoria-atividades"]);
		});
	}
}
