<ng-template #pageTitle>
	{operation, select, edit {Editar Categoria de Atividade} create {Criar
	Categoria de Atividade}}
</ng-template>

<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'CATEGORIA_ATIVIDADE',
			menuLink: ['treino', 'cadastros', 'categoria-atividades']
		}"></pacto-breadcrumbs>

	<pacto-title-card [title]="pageTitle">
		<div class="wrapper">
			<div class="row">
				<div class="col-md-12">
					<pacto-input
						[id]="'nome-categoria-atividade-input'"
						[name]="'nome'"
						[pactoFormGroup]="formGroup"
						[validators]="[Validators.required, Validators.minLength(3)]"
						label="Nome"
						mensagem="Fornecer um nome com pelo menos 3 digitos."
						placeholder="Nome da categoria de atividade"></pacto-input>
				</div>
			</div>
		</div>
		<div class="actions">
			<button
				(click)="submitHandler()"
				class="btn btn-primary"
				i18n="@@buttons:salvar"
				id="btn-add-categoria-atividade">
				Salvar
			</button>
			<button
				(click)="cancelHandler()"
				class="btn btn-secondary"
				i18n="@@buttons:cancelar">
				Cancelar
			</button>
		</div>
	</pacto-title-card>
</pacto-cat-layout-v2>

<span #mensagemSucesso [hidden]="true">
	Categoria de Atividade configurada com sucesso.
</span>

<span #nomeError [hidden]="true">Campos obrigatórios não preenchidos.</span>
