import { TreinoApiCategoriaAtividadeService } from "treino-api";
import { Component, OnInit, ViewChild } from "@angular/core";

import { ApiResponseList } from "@base-core/rest/rest.model";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { ModalService } from "@base-core/modal/modal.service";
import {
	PactoDataGridConfig,
	GridFilterConfig,
	RelatorioComponent,
} from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoRecursoNome, Nivel } from "treino-api";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-categoria-atividade-lista",
	templateUrl: "./categoria-atividade-lista.component.html",
	styleUrls: ["./categoria-atividade-lista.component.scss"],
})
export class CategoriaAtividadeListaComponent implements OnInit {
	@ViewChild("removeModal", { static: false }) removeModal;
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;

	@ViewChild("tooltipRemover", { static: true }) tooltipRemover;
	@ViewChild("tooltipEditar", { static: true }) tooltipEditar;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	constructor(
		private categoriaAtividadeService: TreinoApiCategoriaAtividadeService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private modalService: ModalService,
		private rest: RestService,
		private router: Router
	) {}

	formGroup: FormGroup = new FormGroup({
		filtroNome: new FormControl(""),
	});
	data: ApiResponseList<Nivel> = {
		content: [],
	};

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	permissoesCategoriaAtividades;

	itemToRemove;
	loading = false;
	ready = false;
	nomeCategoriaAtividade: "";
	integracaoZW = false;

	ngOnInit() {
		this.carrgarPermissoes();
		this.integracaoZW = this.sessionService.integracaoZW;
		this.configTable();
		this.ready = true;
	}

	carrgarPermissoes() {
		this.permissoesCategoriaAtividades = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.CATEGORIA_ATIVIDADE
		);
	}

	removeHandler(item) {
		this.nomeCategoriaAtividade = item.nome;
		setTimeout(() => {
			const modalTitle = this.removeModalTitle.nativeElement.innerHTML;
			const modalBody = this.removeModalBody.nativeElement.innerHTML;
			const removeSuccess = this.removeSuccess.nativeElement.innerHTML;
			const handler = this.modalService.confirm(modalTitle, modalBody);
			this.itemToRemove = item;
			handler.result
				.then(() => {
					const idToRemove = this.data.content.findIndex(
						(itemI) => itemI.id === item.id
					);
					this.categoriaAtividadeService
						.removerCategoriaAtividade(item.id)
						.subscribe(() => {
							this.snotifyService.success(removeSuccess);
							this.data.content.splice(idToRemove, 1);
							this.fetchData();
						});
				})
				.catch(() => {});
		});
	}

	btnClickHandler() {
		this.router.navigate(
			["treino", "cadastros", "categoria-atividades", "adicionar"],
			{ queryParamsHandling: "merge" }
		);
	}

	btnEditHandler(item) {
		this.router.navigate(
			["treino", "cadastros", "categoria-atividades", item.id],
			{
				queryParamsHandling: "merge",
			}
		);
	}

	private configTable() {
		const tooltipEditar = this.tooltipEditar.nativeElement.innerHTML;
		const tooltipRemover = this.tooltipRemover.nativeElement.innerHTML;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("categoria-atividade"),
			quickSearch: true,
			logUrl: this.rest.buildFullUrl("log/categoria-atividades"),
			rowClick: this.permissoesCategoriaAtividades.editar,
			buttons: !this.permissoesCategoriaAtividades.incluir
				? null
				: {
						conteudo: this.buttonName,
						nome: "add",
						id: "adicionarCategoriaAtividades",
				  },
			columns: [
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nome",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: tooltipEditar,
					showIconFn: (row) => this.permissoesCategoriaAtividades.editar,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: tooltipRemover,
					showIconFn: (row) => this.permissoesCategoriaAtividades.excluir,
				},
			],
		});
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.removeHandler($event.row);
		}
	}

	private fetchData() {
		this.tableData.reloadData();
	}
}
