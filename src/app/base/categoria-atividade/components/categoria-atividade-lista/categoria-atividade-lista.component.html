<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'CADASTRO',
			menu: 'CATEGORIA_ATIVIDADE'
		}"></pacto-breadcrumbs>

	<div *ngIf="ready" class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="btnEditHandler($event)"
			[filterConfig]="false"
			[sessionService]="sessionService"
			[tableDescription]="subtitulo"
			[tableTitle]="titulo"
			[table]="table"
			telaId="categoriaAtividade"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!--title table-->
<ng-template #titulo>
	<span i18n="@@cadastro:categoria-de-atividades:titulo">
		Categoria de Atividades Cadastradas
	</span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@cadastro:categoria-de-atividades:descricao">
		<PERSON><PERSON><PERSON><PERSON> as categorias de atividades.
	</span>
</ng-template>
<!--End title table-->
<ng-template #buttonName>
	<span i18n="@@buttons:adicionar">Adicionar</span>
</ng-template>
<!--table columns-->
<ng-template #nomeColumnName>
	<span>Nome</span>
</ng-template>
<ng-template #acaoColumnName>
	<span i18n="@@crud-tables:acoes">Ações</span>
</ng-template>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>
<span #tooltipEditar [hidden]="true">Editar</span>

<span #removeModalTitle [hidden]="true">Remover Categoria de Atividade?</span>
<span #removeModalBody [hidden]="true">
	Deseja remover a categoria {{ nomeCategoriaAtividade }}?
</span>
<span #removeSuccess [hidden]="true">Categoria removida com sucesso!</span>
