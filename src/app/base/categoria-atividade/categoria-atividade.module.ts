import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

import { BaseSharedModule } from "@base-shared/base-shared.module";

import { CategoriaAtividadeListaComponent } from "./components/categoria-atividade-lista/categoria-atividade-lista.component";
import { CategoriaAtividadeEditComponent } from "./components/categoria-atividade-edit/categoria-atividade-edit.component";
import { CategoriaAtividadeRoutingModule } from "./categoria-atividade.routing.module";

@NgModule({
	imports: [
		CommonModule,
		NgbModule,
		BaseSharedModule,
		CategoriaAtividadeRoutingModule,
	],
	declarations: [
		CategoriaAtividadeListaComponent,
		CategoriaAtividadeEditComponent,
	],
	entryComponents: [CategoriaAtividadeEditComponent],
})
export class CategoriaAtividadeModule {}
