import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { CategoriaAtividadeListaComponent } from "./components/categoria-atividade-lista/categoria-atividade-lista.component";
import { CategoriaAtividadeEditComponent } from "./components/categoria-atividade-edit/categoria-atividade-edit.component";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";

const recurso = new PerfilAcessoRecurso(
	PerfilAcessoRecursoNome.CATEGORIA_ATIVIDADE,
	[
		PerfilRecursoPermissoTipo.CONSULTAR,
		PerfilRecursoPermissoTipo.TOTAL,
		PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
		PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
	]
);

const routes: Routes = [
	{
		path: "",
		component: CategoriaAtividadeListaComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: ":id",
		component: CategoriaAtividadeEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
	{
		path: "adicionar",
		component: CategoriaAtividadeEditComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			recurso,
		},
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class CategoriaAtividadeRoutingModule {}
