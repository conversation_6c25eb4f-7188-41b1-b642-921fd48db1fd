import { PerfilAcessoRecurso, PerfilAcessoFuncionalidade } from "treino-api";

export interface TreinoMenuConfig {
	parentMenuId?: string;
	menuId: string;
	link?: string[];
	icon?: string;
	iconImage?: string;
	usuariosComPermissao?: string[];
	recursoObrigatorio?: PerfilAcessoRecurso;
	funcionalidadeObrigatorio?: PerfilAcessoFuncionalidade;
	desabilitarIndependente?: boolean;
	newTab?: boolean;
	queryParams?: {
		[name: string]: any;
	};
}

export interface TreinoFullMenuItem {
	menuId: string;
	link?: string[];
	icon?: string;
	iconImage?: string;
	submenus?: TreinoSubMenu[];
	newTab?: boolean;
}

export interface TreinoSubMenu {
	subMenuId: string;
	link?: string[];
	newTab?: boolean;
}
