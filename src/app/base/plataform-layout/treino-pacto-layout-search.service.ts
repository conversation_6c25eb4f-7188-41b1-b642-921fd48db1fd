import { Injectable } from "@angular/core";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";

import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	TreinoApiAlunosService,
} from "treino-api";
import {
	PlatformSearchPerson,
	PactoLayoutSearchService,
	PermissaoService,
} from "pacto-layout";
import { AdmMsApiClientesService, TipoColaborador } from "adm-ms-api";
import { SessionService } from "@base-core/client/session.service";
import { PlataformaModulo } from "src/app/microservices/client-discovery/client-discovery.model";
import { PerfilRecursoPermissoTipo } from "sdk";

@Injectable({
	providedIn: "root",
})
export class TreinoPactoLayoutSearchService
	implements PactoLayoutSearchService
{
	constructor(
		private alunoService: TreinoApiAlunosService,
		private clienteService: AdmMsApiClientesService,
		private sessionService: SessionService,
		private permissaoService: PermissaoService
	) {}

	search(term: string): Observable<Array<PlatformSearchPerson>> {
		const hasZwModule =
			this.sessionService.isModuloHabilitado(PlataformaModulo.ZW) ||
			this.sessionService.isModuloHabilitado(PlataformaModulo.NZW);
		if (hasZwModule) {
			const temPermissaoConsultarAlunosTodasUnidades =
				this.permissaoService.temPermissaoAdm("9.50");
			const temPermissaoCPFBusca =
				this.permissaoService.temPermissaoAdm("13.15");
			const temFuncionalidadedeConsultarAlunosOutasCarteiras =
				this.permissaoService.temFuncionalidadeTreino(
					PerfilAcessoFuncionalidadeNome.VER_ALUNOS_OUTRAS_CARTEIRAS
				);

			let empresa = null;
			if (!temPermissaoConsultarAlunosTodasUnidades) {
				empresa = this.sessionService.currentEmpresa.codigo;
			}
			return this.clienteService
				.pesquisar(term, empresa, null, null, 0, 35)
				.pipe(
					map((clientes) => {
						return clientes.map((cliente) => {
							const fotoUrl =
								cliente && cliente.pessoa && cliente.pessoa.fotoUrl
									? cliente.pessoa.fotoUrl
									: "assets/images/default-user-icon.png";
							return {
								id: cliente.codigo,
								label: cliente.pessoa.nome,
								avatarUri: fotoUrl,
								permiteAcessar: this.permiteAcessar(
									this.permiteVisualizarCliente(),
									temPermissaoConsultarAlunosTodasUnidades,
									temFuncionalidadedeConsultarAlunosOutasCarteiras,
									cliente
								),
								url:
									"/cadastros/alunos/perfil/" +
									cliente.matricula +
									"?origem=globalsearch",
								registration: cliente.matricula ? cliente.matricula : null,
								cpf:
									temPermissaoCPFBusca && cliente.pessoa.cpf
										? cliente.pessoa.cpf
										: null,
								status:
									cliente.situacao && cliente.situacao.codigo
										? cliente.situacao.codigo
										: null,
								company: {
									id:
										cliente.empresa && cliente.empresa.codigo
											? cliente.empresa.codigo
											: null,
									label:
										cliente.empresa && cliente.empresa.nome
											? cliente.empresa.nome
											: null,
								},
							};
						});
					})
				);
		} else {
			return this.alunoService.obterAlunosPesquisa(term).pipe(
				map((alunos) => {
					return alunos.map((aluno) => {
						return {
							id: aluno.matricula,
							label: aluno.nome,
							avatarUri: aluno.imageUri,
							url:
								"/cadastros/alunos/perfil/" +
								aluno.matricula +
								"?origem=globalsearch",
						};
					});
				})
			);
		}
	}

	permiteAcessar(
		permiteCliente,
		permiteTodasEmpresas,
		verOutrasCarteiras,
		cliente
	) {
		// (colaborador === null || (cliente.vinculos && cliente.vinculos.filter(x => x.colaborador.codigo === colaborador).length > 0))
		const temPermissaoAluno = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.ALUNOS
		);
		if (permiteCliente || temPermissaoAluno.consultar) {
			if (
				!permiteTodasEmpresas &&
				cliente.empresa &&
				cliente.empresa.codigo !== this.sessionService.currentEmpresa.codigo
			) {
				return (
					'Você não tem a permissão "9.50 - Consultar alunos e caixa em aberto de todas as empresas" para ver alunos de outras unidades. ' +
					"Por favor, peça ao administrador para liberar essa permissão " +
					"em 'Perfil de Acesso ADM' > '9.50 - Consultar alunos e caixa em aberto de todas as empresas'."
				);
			}
			if (verOutrasCarteiras) {
				return "";
			} else if (this.possuiVinculoProfessorTW(cliente)) {
				return "";
			} else {
				return (
					"Você não tem permissão para Acessar alunos de outras carteiras. " +
					"Por favor, solicite ao administrador a liberação dessa permissão, " +
					"que está localizada em 'Perfil de acesso treino' > 'Aba aluno' > 'Grupo funcionalidade'."
				);
			}
		} else {
			return (
				'Você não possui a permissão "2.04 - Clientes". ' +
				"Por favor, entre em contato com o administrador para solicitar a liberação dessa permissão. " +
				"Você pode encontrá-la em 'Perfil de Acesso ADM' > '2.04 - Clientes'."
			);
		}
	}

	possuiVinculoProfessorTW(cliente): boolean {
		// o valor de id retornado pelo validatetoken do treino é o codigoColaborador do professor
		return (
			(cliente.vinculos || []).filter(
				(x) =>
					String(x.colaborador.codigo) ===
					String(this.sessionService.loggedUser.professorResponse.id)
			).length > 0
		);
	}

	permiteVisualizarCliente(): any {
		const permition =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.04"
			);
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	permiteConsultarTodasEmpresas(): any {
		const permition =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "9.50"
			);
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.CONSULTAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	acessos(tipo: any, limite: any): Observable<Array<any>> {
		return this.alunoService.listaRapidaAcessos(tipo, limite);
	}

	mudarConfigNotificacao(tipo, valor): Observable<any> {
		return this.alunoService.configRapidaAcessosNotificacao(tipo, valor);
	}
}
