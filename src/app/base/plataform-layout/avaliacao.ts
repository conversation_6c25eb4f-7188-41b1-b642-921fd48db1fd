import { TreinoMenuConfig } from "./treino-navigation-menu.model";
import {
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoRecurso,
} from "treino-api";

export const avaliacaoMenusConfig: TreinoMenuConfig[] = [
	{
		menuId: "dashboard",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "dashboard",
		menuId: "bi-avaliacao",
		link: ["/avaliacao", "bi"],
	},
	{
		menuId: "cadastros",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "cadastros",
		menuId: "anamneses",
		link: ["/avaliacao", "anamneses"],
		icon: "pct pct-file-text",
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.ANAMNESE,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "objetivos",
		link: ["/avaliacao", "cadastros", "objetivos"],
		icon: "pct pct-crosshair",
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.OBJETIVOS,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
];
