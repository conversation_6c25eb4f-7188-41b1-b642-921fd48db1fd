import { TreinoMenuConfig } from "./treino-navigation-menu.model";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoFuncionalidade,
} from "treino-api";

export const crossMenusConfig: TreinoMenuConfig[] = [
	{
		menuId: "dashboard",
		icon: "pct pct-pie-chart",
	},
	{
		parentMenuId: "dashboard",
		menuId: "dashboard-cross",
		link: ["/cross", "bi"],
	},
	{
		menuId: "operacoes",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "operacoes",
		menuId: "wod",
		link: ["/cross", "cadastros", "wods"],
		iconImage: "assets/images/pct-wod.svg",
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.WOD,
			true
		),
	},
	{
		parentMenuId: "operacoes",
		menuId: "monitor",
		link: ["/cross", "monitor", "preview"],
		icon: "pct pct-monitor",
	},
	{
		menuId: "cadastros",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "cadastros",
		menuId: "aparelhos",
		link: ["/cross", "cadastros", "aparelhos"],
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.APARELHOS_WOD,
			true
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "atividades",
		link: ["/cross", "cadastros", "atividades-cross"],
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.ATIVIDADES_WOD,
			true
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "benchmarks",
		link: ["/cross", "cadastros", "benchmarks"],
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.BENCHMARK,
			true
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "tipos-benchmark",
		link: ["/cross", "cadastros", "tipos-benchmark"],
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.TIPO_BENCHMARK,
			true
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "tipos-wod",
		link: ["/cross", "cadastros", "tipos-wod"],
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.CADASTRAR_TIPO_WOD,
			true
		),
	},
];
