import { TreinoMenuConfig } from "./treino-navigation-menu.model";

export const pessoasMenusConfig: TreinoMenuConfig[] = [
	{
		menuId: "relatorios",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "relatorios",
		menuId: "clientes",
		link: ["/redirectADM"],
		desabilitarIndependente: true,
		queryParams: {
			redirectUri: "uriClientes",
		},
	},
	{
		parentMenuId: "relatorios",
		menuId: "alunos",
		link: ["/cadastros", "alunos", "listagem"],
	},
	{
		menuId: "cadastros",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "cadastros",
		menuId: "colaborador",
		link: ["/redirectADM"],
		queryParams: {
			redirectUri: "uriColaborador",
			openAsPopup: true,
			windowTitle: "Colaborador",
			windowHeight: 595,
			windowWidth: 1090,
		},
		desabilitarIndependente: true,
	},
	{
		parentMenuId: "cadastros",
		menuId: "usuario",
		link: ["/redirectADM"],
		desabilitarIndependente: true,
		queryParams: {
			redirectUri: "uriUsuario",
			openAsPopup: true,
			windowTitle: "Usuário",
			windowWidth: 1070,
			windowHeight: 600,
		},
	},
	{
		parentMenuId: "cadastros",
		menuId: "perfil-de-acesso",
		link: ["/redirectADM"],
		desabilitarIndependente: true,
		queryParams: {
			redirectUri: "uriPerfil",
			openAsPopup: true,
			windowTitle: "Perfil de Acesso",
			windowWidth: 1070,
			windowHeight: 600,
		},
	},
	{
		parentMenuId: "cadastros",
		menuId: "pessoa",
		link: ["/redirectADM"],
		desabilitarIndependente: true,
		queryParams: {
			redirectUri: "uriPessoa",
			openAsPopup: true,
			windowTitle: "Pessoa",
			windowWidth: 800,
			windowHeight: 595,
		},
	},
	{
		parentMenuId: "cadastros",
		menuId: "colaboradores",
		link: ["/cadastros", "colaboradores"],
	},
	{
		parentMenuId: "cadastros",
		menuId: "usuarios",
		link: ["/colaboradores"],
	},
	{
		parentMenuId: "cadastros",
		menuId: "perfis-acesso",
		link: ["/pessoas/perfil-acesso"],
	},
	// {
	//     menuId: 'gympass',
	//     link: ['/pessoas', 'perfil-v2', 'gympass']
	// },
	// {
	//     menuId: 'boletim-visita',
	//     link: ['/pessoas', 'perfil-v2', 'boletim-visita']
	// }
];
