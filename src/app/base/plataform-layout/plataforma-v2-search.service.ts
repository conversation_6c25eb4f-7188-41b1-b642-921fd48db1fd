import { Injectable } from "@angular/core";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";

import { TreinoApiAlunosService } from "treino-api";
import { PlataformaPersonSearchResult, PlataformaSearchService } from "ui-kit";
import { TreinoConfigCacheService } from "../configuracoes/configuration.service";

@Injectable({
	providedIn: "root",
})
export class PlataformaV2SearchService implements PlataformaSearchService {
	constructor(
		private alunoService: TreinoApiAlunosService,
		private configurationService: TreinoConfigCacheService
	) {}

	searchPeople(term: string): Observable<PlataformaPersonSearchResult[]> {
		return this.alunoService.obterAlunosPesquisa(term).pipe(
			map((alunos) => {
				return alunos.map((aluno) => {
					return {
						id: aluno.matricula,
						nome: aluno.nome,
						avatarUri: aluno.imageUri,
						url:
							"/cadastros/alunos/perfil/" +
							aluno.matricula +
							"?origem=globalsearch",
					};
				});
			})
		);
	}
}
