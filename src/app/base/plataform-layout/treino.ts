import { TreinoMenuConfig } from "./treino-navigation-menu.model";
import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoFuncionalidade,
	PerfilAcessoRecurso,
} from "treino-api";

export const treinoMenusConfig: TreinoMenuConfig[] = [
	{
		menuId: "dashboard",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "dashboard",
		menuId: "dashboard-treino",
		link: ["/treino", "bi", "dashboard"],
	},
	{
		parentMenuId: "dashboard",
		menuId: "dashboard-treino-personalizado",
		link: ["/treino", "bi", "personalizado"],
	},
	{
		menuId: "prescricao",
		link: ["/treino", "montagem-programa", "prescricao"],
		icon: "pct pct-award",
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.TELA_PRESCRICAO_TREINO,
			true
		),
	},
	{
		menuId: "operacoes",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "operacoes",
		menuId: "home-fit",
		link: ["/treino", "home-fit", "lista-treinos"],
		icon: "pct pct-award",
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO,
			[]
		),
	},
	{
		menuId: "relatorios",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "relatorios",
		menuId: "alunosapp",
		link: ["/treino", "bi", "alunos-app"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "carteira",
		link: ["/treino", "gestao", "carteira-professores"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "atividade-professores",
		link: ["/treino", "gestao", "atividade-professores"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "professores-alunos-aviso-medico",
		link: ["/treino", "gestao", "professores-alunos-aviso-medico"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "professores-substituidos",
		link: ["/treino", "gestao", "professores-substituidos"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "andamento-programas",
		link: ["/treino", "gestao", "andamento-programas"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "execucoes-treino",
		link: ["/treino", "gestao", "execucoes-treino"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "personais",
		link: ["/treino", "gestao", "personais"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "gestao-credito",
		link: ["/treino", "gestao", "gestao-credito"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "ranking-professores",
		link: ["/treino", "gestao", "ranking-professores"],
	},
	{
		menuId: "cadastros",
		icon: "pct pct-edit",
	},
	{
		parentMenuId: "cadastros",
		menuId: "aparelhos",
		link: ["/treino", "cadastros", "aparelhos"],
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.APARELHOS,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "atividades",
		link: ["/treino", "cadastros", "atividades"],
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.ATIVIDADES,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "categoria-atividades",
		link: ["/treino", "cadastros", "categoria-atividades"],
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.CATEGORIA_ATIVIDADE,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "fichas-predefinidas",
		link: ["/treino", "cadastros", "fichas-predefinidas", "list"],
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.FICHAS_PRE_DEFINIDAS,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "programas-predefinidos",
		link: ["/treino", "cadastros", "programas-predefinidos", "list"],
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.PROGRAMAS_PREDEFINIDOS,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "niveis",
		link: ["/treino", "cadastros", "niveis"],
	},
	{
		menuId: "personal",
		icon: "pct pct-edit",
	},
	{
		parentMenuId: "personal",
		menuId: "acompanhamento-personal",
		link: ["/treino", "gestao", "acompanhamento-personal"],
	},
	{
		parentMenuId: "personal",
		menuId: "colaboradores-personal",
		link: ["/treino", "gestao", "colaboradores-personal"],
	},
	{
		parentMenuId: "personal",
		menuId: "gestao-credito",
		link: ["/treino", "gestao", "gestao-credito"],
	},
	{
		parentMenuId: "personal",
		menuId: "personais",
		link: ["/treino", "gestao", "personais"],
	},
];
