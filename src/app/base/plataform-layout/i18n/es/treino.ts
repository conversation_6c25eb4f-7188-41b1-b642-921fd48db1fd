import { NavModuleI18n } from "ui-kit";

export const treinoEs: NavModuleI18n = {
	dashboard: {
		name: "Dashboard",
		description: "Indicadores do Treino.",
		searchTokens: "dashboard; informes; bis; indicadores",
	},
	"dashboard-treino": {
		name: "Dashboard treino",
		description: "Indicadores do Treino.",
		searchTokens: "dashboard; relatorios; bis; indicadores",
	},
	"dashboard-treino-personalizado": {
		name: "Dashboard treino personalizado",
		description: "Indicadores personalizados do Treino.",
		searchTokens: "dashboard; relatorios; bis; indicadores",
	},
	operacoes: {
		name: "Operações",
		description: "",
		searchTokens: "operacoes;",
	},
	relatorios: {
		name: "Informes",
		description: "Relatórios do Treino.",
		searchTokens: "informes;",
	},
	alunosapp: {
		name: "Aplicaciones Activas",
		description: "Gráfico de utilização de aplicativos.",
		searchTokens: "alumnosapp; alumnos; app; aplicativo; grafico;",
	},
	carteira: {
		name: "<PERSON><PERSON>",
		description: "Indicadores de cartera de profesores",
		searchTokens: "cartera; indicadores; profesores;",
	},
	"atividade-professores": {
		name: "Actividades de profesores",
		description: "Indicadores de las actividades de profesores.",
		searchTokens: "actividades; profesores; indicadores;",
	},
	"professores-alunos-aviso-medico": {
		name: "Profesores estudiantes aviso medico",
		description: "Profesores estudiantes aviso medico.",
		searchTokens: "profesores; aviso; medico;",
	},
	"professores-substituidos": {
		name: "Profesores sustituido",
		description: "Lista de professores substituídos",
		searchTokens: "profesores; sustituido;",
	},
	"ranking-professores": {
		name: "Ranking",
		description: "Ranking de professores.",
		searchTokens: "ranking; profesores;",
	},
	"indicadores-agenda": {
		name: "Serviços Agendados",
		description: "Serviços Agendados.",
		searchTokens: "serviços; agenda;",
	},
	"andamento-programas": {
		name: "Andamiento",
		description: "Gestión de Andamiento de los Programas.",
		searchTokens: "andamiento; programas;",
	},
	"execucoes-treino": {
		name: "Execuções de treino",
		description: "Execuções de treino.",
		searchTokens: "execução; execuções; treino;",
	},
	cadastros: {
		name: "Registros",
		description: "Cadastre atividades e outros",
		searchTokens: "registro;",
	},
	aparelhos: {
		name: "Aparatos",
		description: "Administración de Aparatos de tu Gimnasio.",
		searchTokens: "aparatos; administracion; academia;",
	},
	atividades: {
		name: "Actividades",
		description: "Administración de Alumnos registrados.",
		searchTokens: "actividades; administracion; acedemia;",
	},
	"categoria-atividades": {
		name: "Categoría de Actividades",
		description: "Gestionar las categorías de actividades.",
		searchTokens: "categoria; actividades; gestionar; academia;",
	},
	musculo: {
		name: "Músculos",
		description: "Maneja tus músculos.",
		searchTokens: "grupo; musculo; administracion;",
	},
	"grupo-muscular": {
		name: "Grupos Musculares",
		description: "Gerencie os grupos musculares.",
		searchTokens: "grupo; muscular; administracion;",
	},
	"fichas-predefinidas": {
		name: "Fichas Predefinidas",
		description: "Gerencie as fichas predefinidas.",
		searchTokens: "fichas; predefinidas; administracion;",
	},
	programa: {
		name: "Programas Predefinidos",
		description: "Gestionar programas predefinidos",
		searchTokens: "programa; programa predefinido; treino",
	},
	personal: {
		name: "Gestión personal",
		description: "Gerencie os programas predefinidos",
		searchTokens: "programa; programa predefinido; treino",
	},
	personais: {
		name: "Personais",
		description: "Gerencie os personais.",
		searchTokens: "personal; gestão de personal; personais",
	},
	"acompanhamento-personal": {
		name: "Acompanhamento de Personal",
		description: "Gerencie os personais.",
		searchTokens: "personal; acompanhamento de personal; personais",
	},
	"gestao-credito": {
		name: "Gestão de Créditos",
		description: "Créditos de personais.",
		searchTokens: "personal; gestao de credito; personais",
	},
	"colaboradores-personal": {
		name: "Colaboradores",
		description: "Colaboradores",
		searchTokens: "personal; gestao de credito; personais; colaboradores",
	},
	"home-fit": {
		name: "Entreno en casa",
		description: "",
		searchTokens: "home-fit;",
	},
	prescricao: {
		name: "Prescripción",
		description: "",
		searchTokens: "prescripción;",
	},
	niveis: {
		name: "Niveles",
		description: "Administración de Alumnos registrados.",
		searchTokens: "registro; niveles; alumnos; administracion;",
	},
};
