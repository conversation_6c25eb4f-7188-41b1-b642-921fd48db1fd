import { NavModuleI18n } from "ui-kit";

export const treinoPt: NavModuleI18n = {
	dashboard: {
		name: "Business Intelligence",
		description: "Dashboards do Treino.",
		searchTokens: "dashboard; relatorios; bis; indicadores",
	},
	"dashboard-treino": {
		name: "BI Treino",
		description: "Indicadores do Treino.",
		searchTokens: "dashboard; relatorios; bis; indicadores",
	},
	"dashboard-treino-personalizado": {
		name: "BI Personalizado",
		description: "Indicadores personalizados do Treino.",
		searchTokens: "dashboard; relatorios; bis; indicadores",
	},
	operacoes: {
		name: "Operações",
		description: "",
		searchTokens: "operacoes;",
	},
	relatorios: {
		name: "Relat<PERSON><PERSON><PERSON>",
		description: "Relatórios do Treino.",
		searchTokens: "relatorios;",
	},
	alunosapp: {
		name: "Aplicativos Ativos",
		description: "Gráfico de utilização de aplicativos.",
		searchTokens: "alunosapp; alunos; app; aplicativo; grafico;",
	},
	carteira: {
		name: "<PERSON><PERSON><PERSON>",
		description: "Indicadores da carteira dos professores",
		searchTokens: "carteira; indicadores; professores;",
	},
	"atividade-professores": {
		name: "Atividades dos professores",
		description: "Atividades dos professores.",
		searchTokens: "atividade; professores; incidadores;",
	},
	"professores-alunos-aviso-medico": {
		name: "Alunos com aviso médico por professor",
		description: "Alunos com aviso médico por professor.",
		searchTokens: "professores; aviso; medico;",
	},
	"professores-substituidos": {
		name: "Professores substituídos",
		description: "Lista de professores substituídos",
		searchTokens: "professores; substituidos;",
	},
	"ranking-professores": {
		name: "Ranking",
		description: "Ranking de professores.",
		searchTokens: "ranking; professores;",
	},
	"indicadores-agenda": {
		name: "Serviços Agendados",
		description: "Serviços Agendados.",
		searchTokens: "serviços; agenda;",
	},
	"andamento-programas": {
		name: "Andamento",
		description: "Gestão de Andamento dos Programas.",
		searchTokens: "andamento; programas;",
	},
	"execucoes-treino": {
		name: "Execuções de treino",
		description: "Execuções de treino.",
		searchTokens: "execução; execuções; treino;",
	},
	"alunos-faltosos": {
		name: "Alunos faltosos",
		description: "Alunos faltosos",
		searchTokens: "alunos, profesores; aluno; faltosos;",
	},
	cadastros: {
		name: "Cadastros",
		description: "Cadastre atividades e outros",
		searchTokens: "cadastros;",
	},
	aparelhos: {
		name: "Aparelhos",
		description: "Gerencie os aparelhos da sua academia.",
		searchTokens: "aparelhos; gerencie; academia;",
	},
	atividades: {
		name: "Atividades",
		description: "Gerencie as atividades da sua academia.",
		searchTokens: "atividades; gerencie; academia;",
	},
	"atividades-ia": {
		name: "Atividades Treino por IA",
		description: "Gerencie as atividades do Treino por IA da sua academia.",
		searchTokens: "atividades; IA; inteligencia; artificial;",
	},
	"categoria-atividades": {
		name: "Categoria de Atividades",
		description: "Gerencie as categorias de atividades da sua academia.",
		searchTokens: "categoria; atividades; gerencie; academia;",
	},
	musculo: {
		name: "Músculos",
		description: "Gerencie os músculos.",
		searchTokens: "grupo; musculo; gerencie;",
	},
	"grupo-muscular": {
		name: "Grupos Musculares",
		description: "Gerencie os grupos musculares.",
		searchTokens: "grupo; muscular; gerencie;",
	},
	"fichas-predefinidas": {
		name: "Fichas Predefinidas",
		description: "Gerencie as fichas predefinidas.",
		searchTokens: "fichas; predefinidas; gerencie;",
	},
	programa: {
		name: "Programas Predefinidos",
		description: "Gerencie os programas predefinidos.",
		searchTokens: "programa; programa predefinido; treino",
	},
	personal: {
		name: "Gestão de personal",
		description: "Gerencie os personais.",
		searchTokens: "personal; gestão de personal; personais",
	},
	personais: {
		name: "Personais",
		description: "Gerencie os personais.",
		searchTokens: "personal; gestão de personal; personais",
	},
	"acompanhamento-personal": {
		name: "Acompanhamento de Personal",
		description: "Gerencie os personais.",
		searchTokens: "personal; acompanhamento de personal; personais",
	},
	"gestao-credito": {
		name: "Gestão de Créditos",
		description: "Créditos de personais.",
		searchTokens: "personal; gestao de credito; personais",
	},
	"colaboradores-personal": {
		name: "Colaboradores",
		description: "Colaboradores",
		searchTokens: "personal; gestao de credito; personais; colaboradores",
	},
	"home-fit": {
		name: "Treino em casa",
		description: "",
		searchTokens: "home-fit;",
	},
	prescricao: {
		name: "Prescrição de treino",
		description: "",
		searchTokens: "treino;prescricao de treino;",
	},
	niveis: {
		name: "Níveis",
		description: "Cadastre niveis de alunos.",
		searchTokens: "cadastro; niveis; alunos; gerencie;",
	},
};
