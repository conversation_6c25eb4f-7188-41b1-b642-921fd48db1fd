import { NavModuleI18n } from "ui-kit";

export const alunoPt: NavModuleI18n = {
	alunos: {
		name: "<PERSON><PERSON><PERSON>",
		description: "<PERSON>erir cadastros de alunos.",
		searchTokens: "alunos; cadastros; ",
	},
	colaboradores: {
		name: "Colabor<PERSON>",
		description: "<PERSON><PERSON><PERSON> professores e coordenadores.",
		searchTokens: "professores; coodenadores; cadastros; ",
	},
	usuarios: {
		name: "Usu<PERSON>rio<PERSON>",
		description: "Editar usuários.",
		searchTokens: "editar; usuarios; ",
	},
	"perfis-acesso": {
		name: "<PERSON><PERSON><PERSON> de acesso",
		description: "<PERSON><PERSON><PERSON> perfis de acesso.",
		searchTokens: "perfis; acesso; cadastros; ",
	},
	acessos: {
		name: "<PERSON><PERSON><PERSON>",
		description: "Ace<PERSON><PERSON>.",
		searchTokens: "acessos; ",
	},
	"senha-de-acesso": {
		name: "<PERSON><PERSON> de acesso",
		description: "<PERSON>ha de acesso.",
		searchTokens: "Senha de acesso; ",
	},
	"acesso-manual": {
		name: "Registar acesso manual",
		description: "Registar acesso manual",
		searchTokens: "Registar acesso manual",
	},
	gympass: {
		name: "GymPass",
		description: "Histórico GymPass.",
		searchTokens: "histórico; gympass;",
	},
	"boletim-visita": {
		name: "Boletim Visita",
		description: "Boletim de Visitas.",
		searchTokens: "bv; boletim; visita;",
	},
	"log-atividade": {
		name: "Log de Atividades",
		description: "Log de Atividades.",
		searchTokens: "log; ",
	},
	"historico-indicacoes": {
		name: "Histórico de indicações",
		description: "Histórico de indicações.",
		searchTokens: "indicações; histórico; ",
	},
};
