import { NavModuleI18n } from "ui-kit";

export const pessoasPt: NavModuleI18n = {
	alunos: {
		name: "<PERSON><PERSON><PERSON>",
		description: "<PERSON>erir cadastros de alunos.",
		searchTokens: "alunos; cadastros; ",
	},
	colaboradores: {
		name: "<PERSON><PERSON><PERSON> (Treino)",
		description: "<PERSON><PERSON>r professores e coordenadores.",
		searchTokens: "professores; coodenadores; cadastros; ",
	},
	usuarios: {
		name: "<PERSON><PERSON><PERSON><PERSON> (Treino)",
		description: "Editar usuários.",
		searchTokens: "editar; usuarios; ",
	},
	"perfis-acesso": {
		name: "<PERSON><PERSON><PERSON> de acesso (Treino)",
		description: "<PERSON><PERSON>r perfis de acesso.",
		searchTokens: "perfis; acesso; cadastros; ",
	},
	"perfis-acesso-pessoas": {
		name: "Perfi<PERSON> de acesso",
		description: "<PERSON><PERSON>r perfi<PERSON> de acesso.",
		searchTokens: "perfis; acesso; cadastros; ",
	},
	"log-atividade": {
		name: "Log de Atividades",
		description: "Log de Atividades",
		searchTokens: "log; atividade; ",
	},
	operacoes: {
		name: "Operações",
		description: "Operações de pessoas",
	},
	relatorios: {
		name: "Relatórios",
		description: "Relatórios de pessoas",
	},
	cadastros: {
		name: "Cadastros",
		description: "Cadastros de pessoas",
	},
	clientes: {
		name: "Pessoas",
		description: "Clientes ADM",
	},

	colaborador: {
		name: "Colaborador",
		description: "Colaborador ADM",
	},
	usuario: {
		name: "Usuário",
		description: "Usuário ADM",
	},
	"categoria-de-clientes": {
		name: "Categoria de clientes",
		description: "Categoria de clientes ADM",
	},
	"perfil-de-acesso": {
		name: "Perfil de acesso",
		description: "Perfil de acesso ADM",
	},
	pessoa: {
		name: "Pessoa",
		description: "Pessoa ADM",
	},
	gympass: {
		name: "GymPass",
		description: "Histórico GymPass.",
		searchTokens: "histórico; gympass;",
	},
	"boletim-visita": {
		name: "Boletim Visita",
		description: "Boletim de Visitas.",
		searchTokens: "bv; boletim; visita;",
	},
};
