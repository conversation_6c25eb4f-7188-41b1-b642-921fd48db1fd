import { NavModuleI18n } from "ui-kit";

export const configPt: NavModuleI18n = {
	config: {
		name: "Tre<PERSON>",
		description: "Treino.",
		searchTokens: "config; configurações; treino",
	},
	integracoes: {
		name: "Integrações",
		description: "Integrações",
		searchTokens: "integracao; integrações; integração",
	},
	"config-treino": {
		name: "Treino",
		description: "Treino",
	},
	"config-adm": {
		name: "Administrativo",
		description: "Configurações do módulo Administrativo",
	},
	"config-crm": {
		name: "CRM",
		description: "Configurações do módulo CRM",
	},
	"config-fin": {
		name: "Financeiro",
		description: "Configurações do módulo Financeiro",
	},

	// integracoesV2: {
	//     name: 'Integrações',
	//     description: 'Integrações',
	//     searchTokens: 'integracao; integrações; integração'
	// }
};
