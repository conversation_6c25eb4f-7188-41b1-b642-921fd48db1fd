import { NavModuleI18n } from "ui-kit";

export const pactopayPt: NavModuleI18n = {
	bi: {
		name: "Business Intelligence",
		description: "Indicadores Financeiros.",
		searchTokens: "bi",
	},
	cartao: {
		name: "Cartão de crédito",
		description: "Indicadores de cartão de crédito.",
		searchTokens: "cartão de crédito",
	},
	parcelas: {
		name: "Parcelas em aberto",
		description: "Consulta de parcelas.",
		searchTokens: "parcelas",
	},
	relatorios: {
		name: "Relatórios",
		description: "Relatórios financeiros.",
		searchTokens: "relatorios",
	},
	transacoes: {
		name: "Transações",
		description: "Transações.",
		searchTokens: "transacoes",
	},
	"credito-online": {
		name: "Cartão de crédito online",
		description: "Cartão de crédito online",
		searchTokens: "credito-online",
	},
	// 'credito-edi': {
	//   name: 'Cartão de crédito EDI',
	//   description: 'Cartão de crédito EDI',
	//   searchTokens: 'credito-edi',
	// },
	// 'boleto-tradicional': {
	//   name: 'Boleto tradicional',
	//   description: 'Boleto tradicional',
	//   searchTokens: 'boleto-tradicional',
	// },
	// 'boleto-pjbank': {
	//   name: 'Boleto PjBank',
	//   description: 'Boleto PjBank',
	//   searchTokens: 'boleto-pjbank',
	// },
	// dco: {
	//   name: 'DCO',
	//   description: 'DCO',
	//   searchTokens: 'dco',
	// },
	pix: {
		name: "PIX",
		description: "PIX",
		searchTokens: "pix",
	},
	regua: {
		name: "Régua de cobrança",
		description: "Automatização de Cobranças.",
		searchTokens: "regua",
	},
	dashboard_da_regua: {
		name: "BI Régua",
		description: "Automatização de Cobranças.",
		searchTokens: "regua",
	},
	configuracao_de_email: {
		name: "Configurações",
		description: "Configurações.",
		searchTokens: "regua",
	},
	configuracao_de_fases: {
		name: "Configuração de fases",
		description: "Configuração de fases.",
		searchTokens: "regua",
	},
};
