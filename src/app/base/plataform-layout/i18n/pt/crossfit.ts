import { NavModuleI18n } from "ui-kit";

export const crossfitPt: NavModuleI18n = {
	dashboard: {
		name: "Business Intelligence",
		description: "Dashboard da agenda",
	},
	"dashboard-cross": {
		name: "BI Cross",
		description: "Indicadores do Cross.",
		searchTokens: "dashboard; relatorios; bis; indicadores",
	},
	wod: {
		name: "Wod",
		description: "Cadastre o WOD do dia.",
		searchTokens: "cadastro, novo, adicionar, wods, workout, treino",
	},
	"objetivo-crossfit": {
		name: "Objetivo",
		description: "Gerencie os objetivos.",
		searchTokens: "cadastro, novo, adicionar, objetivos",
	},
	"tipos-wod": {
		name: "Tipos de Wod",
		description: "Configure os WODs.",
		searchTokens: "crossfit, cadastros, tipo wod",
	},
	periodizacao: {
		name: "Periodização",
		description: "<PERSON><PERSON><PERSON><PERSON> as periodizações.",
		searchTokens: "periodização, periodizações",
	},
	monitor: {
		name: "Monitor",
		description: "Apresentar WOD na televisão.",
		searchTokens: "monitor, ranking, wod, tv, wokout, resultados, televisão",
	},
	cadastros: {
		name: "Cadastros",
		description: "Gerenciar aparelhos, atividades e outros.",
	},
	aparelhos: {
		name: "Aparelhos",
		description: "Configure os aparelhos.",
		searchTokens: "cadastro, novo, adicionar, aparelhos, equipamento",
	},
	atividades: {
		name: "Atividades",
		description: "Configure os atividades.",
		searchTokens: "crossfit, cadastros, atividades crossfit",
	},
	benchmarks: {
		name: "Benchmarks",
		description: "Crie os benchmarks.",
		searchTokens: "crossfit, cadastros, benchmarks",
	},
	"tipos-benchmark": {
		name: "Tipos de benchmark",
		description: "Configure os benchmarks.",
		searchTokens: "cadastros, tipo, tipos benchmark",
	},
	operacoes: {
		name: "Operações",
		description: "Operações do cross",
	},
	relatorios: {
		name: "Relatórios",
		description: "Relatórios do cross",
	},
};
