import { TreinoMenuConfig } from "./treino-navigation-menu.model";

export const configMenuAdm: TreinoMenuConfig = {
	menuId: "config-adm",
	link: [`/redirectADM`],
	queryParams: {
		funcionalidadeNome: "CONFIG_ZW",
		windowTitle: "Configurações do Sistema",
		windowHeight: 768,
		windowWidth: 1024,
		openAsPopup: true,
	},
};

export const configMenuCRM: TreinoMenuConfig = {
	menuId: "config-crm",
	link: [`/redirectADM`],
	queryParams: {
		funcionalidadeNome: "CONFIG_CRM",
		windowTitle: "Configurações do Sistema",
		windowHeight: 768,
		windowWidth: 1024,
		openAsPopup: true,
	},
};
export const configMenuFin: TreinoMenuConfig = {
	menuId: "config-fin",
	link: [`/redirectADM`],
	queryParams: {
		funcionalidadeNome: "CONFIG_FIN",
		windowTitle: "Configurações do Sistema",
		windowHeight: 768,
		windowWidth: 1024,
		openAsPopup: true,
	},
};

export const configMenuTreino: TreinoMenuConfig = {
	menuId: "config",
	link: ["/config"],
	icon: "pct pct-edit",
};
export const configMenuIntegracoes: TreinoMenuConfig = {
	menuId: "integracoes",
	link: ["/config", "integracoes"],
	icon: "pct pct-edit",
};

export const configMenusConfig: TreinoMenuConfig[] = [
	configMenuAdm,
	configMenuCRM,
	configMenuFin,
	configMenuIntegracoes,
	configMenuTreino,
];
