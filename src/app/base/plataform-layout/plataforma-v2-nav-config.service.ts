import { Injectable } from "@angular/core";

import { BehaviorSubject, Observable } from "rxjs";

import { SessionService } from "@base-core/client/session.service";
import { debounceTime } from "rxjs/operators";
import { BETA_MODULES_CONFIG, LocalStorageSessionService } from "sdk";
import { PlataformaModulo } from "src/app/microservices/client-discovery/client-discovery.model";
import { EmpresaFinanceiro } from "treino-api";
import {
	PlataformaModule,
	PlataformaModuleItem,
	PlataformaNavConfigService,
} from "ui-kit";
import { ClientDiscoveryService } from "../../microservices/client-discovery/client-discovery.service";
import { graduacaoMenus } from "./graduacao-v2";
import { TreinoMenuNavigationService } from "./treino-menu-navigation.service";
import { TreinoMenuConfig } from "./treino-navigation-menu.model";

@Injectable({
	providedIn: "root",
})
export class PlataformaV2NavConfigService
	implements PlataformaNavConfigService
{
	localStorageParams;
	private linkZw: string;
	linkGOR: string;
	private orderedModules = new BehaviorSubject<PlataformaModule[]>([]);
	private isBeta: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
		false
	);

	constructor(
		private treinoNavigationService: TreinoMenuNavigationService,
		private sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		private localStorageService: LocalStorageSessionService
	) {
		this.localStorageParams = this.localStorageService.getLocalStorageParams();
		this.linkZw = this.clientDiscoveryService.getUrlMap().zwUrlFull;
	}

	isBetaTester(): Observable<boolean> {
		return this.isBeta.asObservable();
	}

	getModules(): Observable<PlataformaModule[]> {
		const empresa: EmpresaFinanceiro | undefined =
			this.sessionService.currentEmpresa;
		const modules: PlataformaModule[] = [
			{
				orderTopbar: 1,
				moduleId: "adm",
				icon: "pct-logo-pacto",
				logoUri: "./assets/images/logos/pct-icone-fundo-administrativo.svg",
				baseColor: "#1997FB",
				link: "/adm/home",
				showOnMenuTaskbar: true,
			},
			{
				orderTopbar: 3,
				orderTaskbar: 2,
				moduleId: "treino",
				icon: "pct-logo-pacto",
				logoUri: "./assets/images/logos/pct-icone-fundo-novo-treino.svg",
				baseColor: "#ff4c2b",
				link: "/treino/home",
				menus: this.getTreinoModuleItems(),
				showOnMenuTaskbar: true,
			},
			{
				orderTopbar: 4,
				moduleId: "crm",
				logoUri: "./assets/images/logos/pct-icone-fundo-crm.svg",
				baseColor: "#F0B924",
				link: `/redirectADM`,
				queryParams: {
					redirectUri: "uriCRM",
				},
			},
			{
				orderTopbar: 5,
				moduleId: "financeiro",
				icon: "pct-logo-pacto",
				logoUri: "./assets/images/logos/pct-icone-fundo-financeiro.svg",
				baseColor: "#7BE542",
				link: `/redirectADM`,
				queryParams: {
					redirectUri: "uriFinan",
				},
			},
			{
				orderTopbar: 6,
				orderTaskbar: 6,
				moduleId: "pactopay",
				icon: "pct-logo-pacto",
				logoUri: "./assets/images/logos/pct-icone-fundo-pactopay.svg",
				baseColor: "#1d7e33",
				link: "/pactopay/home",
				menus: this.getPactoPayModuleItems(),
				showOnMenuTaskbar: true,
			},
			{
				orderTopbar: 7,
				orderTaskbar: 3,
				moduleId: "avaliacao",
				icon: "pct-logo-pacto",
				logoUri: "./assets/images/logos/pct-icone-fundo-avaliacao.svg",
				baseColor: "#914391",
				menus: this.getAvaliacaoModuleItems(),
				link: "/avaliacao",
				showOnMenuTaskbar: true,
			},
			{
				orderTopbar: 8,
				moduleId: "canal",
				logoUri: "./assets/images/logos/pct-icone-fundo-canal-do-cliente.svg",
				baseColor: "#158C51",
				link: `/redirectADM`,
				queryParams: {
					redirectUri: "uriCanal",
				},
			},
			{
				orderTopbar: 9,
				orderTaskbar: 4,
				moduleId: "crossfit",
				icon: "pct-logo-pacto",
				logoUri: "./assets/images/logos/pct-icone-fundo-cross.svg",
				baseColor: "#0a0a0a",
				link: "/cross",
				menus: this.getCrossfitModuleItems(),
				showOnMenuTaskbar: true,
			},
			{
				orderTopbar: 10,
				moduleId: "gor",
				logoUri: "./assets/images/logos/pct-icone-fundo-game-of-results.svg",
				baseColor: "#64C1BE",
				link: `/redirectADM`,
				queryParams: {
					moduleId: "GOR",
				},
			},
			{
				orderTopbar: 11,
				orderTaskbar: 5,
				moduleId: "graduacao",
				icon: "pct-logo-pacto",
				baseColor: "#4486b5",
				logoUri: "./assets/images/logos/pct-icone-fundo-graduacao.svg",
				link: "/graduacao",
				menus: graduacaoMenus,
				showOnMenuTaskbar: true,
			},
			{
				orderTopbar: 12,
				moduleId: "nf",
				logoUri: "./assets/images/logos/pct-icone-fundo-nota-fiscal.svg",
				baseColor: "#158C51",
				link: `/redirectADM`,
				queryParams: {
					redirectUri: "uriNota",
				},
				linkAsHref: true,
			},
			{
				orderTopbar: 13,
				moduleId: "pre_cadastro",
				logoUri: "./assets/images/pct-user-plus-prin.svg",
				icon: "pct-user-plus",
				showAsIcon: true,
				link: `/redirectADM`,
				queryParams: {
					redirectUri: "uriPrecadastro",
				},
			},
			{
				orderTopbar: 13,
				moduleId: "aluno",
				icon: "pct-users",
				logoUri: "./assets/images/pct-users-prin.svg",
				menus: this.getAlunoModuleItems(),
				hideTaskBar: true,
				link: "/pessoas/perfil-v2",
				showOnMenuTopbar: false,
			},
			{
				orderTopbar: 14,
				moduleId: "pessoas",
				icon: "pct-users",
				showAsIcon: true,
				logoUri: "./assets/images/pct-users-prin.svg",
				menus: this.getPessoasModuleItems(),
				link: "/cadastros/alunos/listagem",
			},
			{
				orderTopbar: 15,
				moduleId: "agenda",
				icon: "pct-calendar",
				logoUri: "./assets/images/agenda-icon.svg",
				showAsIcon: true,
				menus: this.getAgendaModuleItems(),
				link: "/agenda",
			},
			{
				orderTopbar: 16,
				moduleId: "config",
				icon: "pct-settings",
				showAsIcon: true,
				bottomSection: true,
				onlyLoadMenu: true,
				logoUri: "./assets/images/pct-settings-prin.svg",
				menus: this.getConfigModuleItems(),
				baseColor: "#1998FC",
			},
		];

		let _modules;
		this.userIsBetaTester(this.sessionService.loggedUser);

		this.isBeta.pipe(debounceTime(300)).subscribe((isBeta) => {
			if (isBeta) {
				const config = modules.find(
					(module) => module.moduleId === "config"
				) as PlataformaModule;
				if (config !== undefined) {
					config.bottomSection = true;
				}
				const modulesConfig = modules.map((module) => {
					const _module = BETA_MODULES_CONFIG.find(
						(betaModule) => betaModule.moduleId === module.moduleId
					);
					if (_module !== undefined) {
						return { ...module, ..._module };
					}

					return module;
				});

				_modules = this.checkModuleAvailability(modulesConfig);
				this.orderedModules.next(_modules);
				// Será adicionado ordenação em outro ticket
				// this.updatedModulesOrder();
			} else {
				_modules = this.checkModuleAvailability(modules);
				this.orderedModules.next(_modules);
			}
		});

		return this.orderedModules.asObservable();
	}

	private getAvaliacaoModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("avaliacao")
			.map((i) => this.convert(i));
	}

	private getPactoPayModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("pactopay")
			.map((i) => this.convert(i));
	}

	private getAgendaModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("agenda")
			.map((i) => this.convert(i));
	}

	private getCrossfitModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("crossfit")
			.map((i) => this.convert(i));
	}

	private getTreinoModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("treino")
			.map((i) => this.convert(i));
	}

	private getPessoasModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("pessoas")
			.map((i) => this.convert(i));
	}

	private getConfigModuleItems(): PlataformaModuleItem[] {
		const configMenus = this.treinoNavigationService.getAllMenuConfig("config");
		return configMenus.map((i) => this.convert(i));
	}

	private getAlunoModuleItems(): PlataformaModuleItem[] {
		return this.treinoNavigationService
			.getAllMenuConfig("aluno")
			.map((i) => this.convert(i));
	}

	private convert(menuConfig: TreinoMenuConfig): PlataformaModuleItem {
		return {
			id: menuConfig.menuId,
			link: menuConfig.link ? menuConfig.link.join("/") : null,
			parent: menuConfig.parentMenuId,
			newTab: menuConfig.newTab,
			queryParams: menuConfig.queryParams,
		};
	}

	private createUrlNovoAdm(moduleId: string, empresa: EmpresaFinanceiro) {
		const token = this.sessionService.apiToken;
		let sessionId;
		const siglaNovaPlataforma = empresa.siglaNovaPlataforma
			? empresa.siglaNovaPlataforma
			: "pt";
		if (!token || token === "") {
			sessionId = this.localStorageParams.sessionId;
		}
		let url = `${
			this.clientDiscoveryService.getUrlMap().zwFrontUrl
		}/${siglaNovaPlataforma}/adicionarConta?`;
		url += sessionId ? `sessionId=${sessionId}` : `token=${token}`;
		url += `&moduleId=${moduleId}`;
		url += `&idiomabanco=${this.localStorageParams.idiomabanco}`;
		url += `&modulos=${this.localStorageParams.modulos}`;
		url += `&integracaoZW=${this.localStorageParams.integracaoZW}`;
		url += `&empresaId=${this.localStorageParams.empresaId}`;
		url += `&urladm=${this.localStorageParams.urladm}`;
		url += `&urlapi=${this.localStorageParams.urlapi}`;
		url += `&usuarioOamd=${this.localStorageParams.usuarioOamd}`;
		return encodeURI(url);
	}

	private filterUsuarioComPermissao(menu: PlataformaModuleItem): boolean {
		if (menu.usuariosComPermissao === undefined) {
			return true;
		} else if (
			menu.usuariosComPermissao !== undefined &&
			menu.usuariosComPermissao.includes(
				this.sessionService.loggedUser.username
			)
		) {
			return true;
		} else {
			return false;
		}
	}

	updatedModulesOrder(): void {
		const modules: PlataformaModule[] = this.orderedModules.value;
		this.sessionService.getModulesOrder().subscribe({
			next: (resp) => {
				const modulesOrder = resp.content;
				if (modulesOrder) {
					modules.forEach((modulo) => {
						const index = modulesOrder.findIndex(
							(m) => modulo.moduleId === m.modulo
						);

						if (index !== -1 && modulo.showOnMenuTaskbar) {
							// mutates the module property
							modulo.orderTopbar = modulesOrder[index].ordem;
						}
					});
				}
				this.orderedModules.next(modules);
			},
			error: (e) => {
				this.orderedModules.next(modules);
			},
		});
	}

	userIsBetaTester(loggedUser: any): void {
		if (
			loggedUser &&
			loggedUser.username &&
			loggedUser.username.toLowerCase() === "pactobr"
		) {
			this.isBeta.next(true);
		}
	}

	checkModuleAvailability(modules: PlataformaModule[]): PlataformaModule[] {
		return modules.filter((modulo) => {
			const moduleMap = {
				adm_legado: PlataformaModulo.ZW,
				pre_cadastro: PlataformaModulo.ZW,
				adm: PlataformaModulo.NZW,
				treino: PlataformaModulo.NTR,
				graduacao: PlataformaModulo.GRD,
				crossfit: PlataformaModulo.NCR,
				agenda: PlataformaModulo.AGN,
				avaliacao: PlataformaModulo.NAV,
				aluno: PlataformaModulo.NTR,
				pessoas: PlataformaModulo.NTR,
				config: PlataformaModulo.NTR,
				pactopay: PlataformaModulo.PAY,
				financeiro: PlataformaModulo.FIN,
				crm: PlataformaModulo.CRM,
				gor: PlataformaModulo.GOR,
				nf: PlataformaModulo.NF,
				canal: PlataformaModulo.CCL,
			};
			return this.sessionService.isModuloHabilitado(moduleMap[modulo.moduleId]);
		});
	}
}
