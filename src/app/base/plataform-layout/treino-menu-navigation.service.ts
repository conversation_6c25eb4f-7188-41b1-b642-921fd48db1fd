import { Injectable } from "@angular/core";

import { Observable, of } from "rxjs";

import { SessionService } from "@base-core/client/session.service";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";

import {
	TreinoFullMenuItem,
	TreinoMenuConfig,
} from "./treino-navigation-menu.model";

import { treinoMenusConfig } from "./treino";
import { crossMenusConfig } from "./cross";
import { avaliacaoMenusConfig } from "./avaliacao";
import { agendaMenusConfig } from "./agenda";
import { pessoasMenusConfig } from "./pessoas";
import { configMenusConfig } from "./config";
import { pactopayMenusConfig } from "./pactopay-config";
import { alunoMenusConfig } from "./aluno";

@Injectable({
	providedIn: "root",
})
export class TreinoMenuNavigationService {
	constructor(
		private session: SessionService,
		private treinoConfigCache: TreinoConfigCacheService
	) {}

	getFullTreinoMenu(): Observable<TreinoFullMenuItem[]> {
		return this.buildFullMenu(treinoMenusConfig);
	}

	getFullAvaliacaoMenu(): Observable<TreinoFullMenuItem[]> {
		return this.buildFullMenu(avaliacaoMenusConfig);
	}

	getFullCrossMenu(): Observable<TreinoFullMenuItem[]> {
		const menus: TreinoFullMenuItem[] = this.fetchParentMenus(crossMenusConfig)
			.map((item) => this.ajustCrossfitMenus(item))
			.map((config) => {
				return {
					menuId: config.menuId,
					link: config.link,
					icon: config.icon,
					iconImage: config.iconImage,
				};
			});
		menus.forEach((menu) => {
			const submenus = this.fetchSubmenus(crossMenusConfig, menu.menuId)
				.map((item) => this.ajustCrossfitMenus(item))
				.map((item) => {
					return {
						subMenuId: item.menuId,
						link: item.link,
					};
				});
			if (submenus && submenus.length) {
				menu.submenus = submenus;
			}
		});
		return of(menus);
	}

	getAllMenuConfig(module): TreinoMenuConfig[] {
		let configs: TreinoMenuConfig[] = [];
		if (module === "avaliacao") {
			configs = avaliacaoMenusConfig;
		} else if (module === "crossfit") {
			configs = crossMenusConfig;
		} else if (module === "agenda") {
			configs = agendaMenusConfig;
		} else if (module === "treino") {
			configs = treinoMenusConfig;
		} else if (module === "pessoas") {
			configs = pessoasMenusConfig;
		} else if (module === "config") {
			configs = configMenusConfig;
		} else if (module === "pactopay") {
			configs = pactopayMenusConfig;
		} else if (module === "aluno") {
			configs = alunoMenusConfig;
		}

		return configs
			.filter((item) => this.filterUsuarioComPermissao(item))
			.filter((item) => this.filterTreinoIndependente(item))
			.filter((item) => this.filterFuncionalidade(item))
			.filter((item) => this.filterPermission(item));
	}

	private buildFullMenu(
		configs: TreinoMenuConfig[]
	): Observable<TreinoFullMenuItem[]> {
		const menus: TreinoFullMenuItem[] = this.fetchParentMenus(configs).map(
			(config) => {
				return {
					menuId: config.menuId,
					link: config.link,
					icon: config.icon,
					iconImage: config.iconImage,
					newTab: config.newTab,
				};
			}
		);
		menus.forEach((menu) => {
			const submenus = this.fetchSubmenus(configs, menu.menuId).map((item) => {
				return {
					subMenuId: item.menuId,
					link: item.link,
					newTab: item.newTab,
				};
			});
			if (submenus && submenus.length) {
				menu.submenus = submenus;
			}
		});
		return of(menus);
	}

	private fetchParentMenus(configs: TreinoMenuConfig[]): TreinoMenuConfig[] {
		return configs
			.filter((config) => config.parentMenuId === undefined)
			.filter((item) => this.filterUsuarioComPermissao(item))
			.filter((item) => this.filterTreinoIndependente(item))
			.filter((item) => this.filterFuncionalidade(item))
			.filter((item) => this.filterPermission(item));
	}

	/**
	 * Gets submenus while applying permission rules
	 */
	private fetchSubmenus(
		configs: TreinoMenuConfig[],
		menuId: string
	): TreinoMenuConfig[] {
		return configs
			.filter((item) => item.parentMenuId === menuId)
			.filter((item) => this.filterUsuarioComPermissao(item))
			.filter((item) => this.filterTreinoIndependente(item))
			.filter((item) => this.filterFuncionalidade(item))
			.filter((item) => this.filterPermission(item));
	}

	private filterUsuarioComPermissao(menu: TreinoMenuConfig): boolean {
		if (menu.usuariosComPermissao === undefined) {
			return true;
		} else if (
			menu.usuariosComPermissao !== undefined &&
			menu.usuariosComPermissao.includes(this.session.loggedUser.username)
		) {
			return true;
		} else {
			return false;
		}
	}

	private filterTreinoIndependente(menu: TreinoMenuConfig): boolean {
		if (menu.desabilitarIndependente && !this.session.integracaoZW) {
			return false;
		} else {
			return true;
		}
	}

	private filterFuncionalidade(menu: TreinoMenuConfig): boolean {
		const perfil = this.session.perfilUsuario;
		if (menu.funcionalidadeObrigatorio) {
			return perfil.funcionalidades.get(
				menu.funcionalidadeObrigatorio.funcionalidade
			);
		} else {
			return true;
		}
	}

	private filterPermission(menu: TreinoMenuConfig): boolean {
		const perfil = this.session.perfilUsuario;
		if (!menu.recursoObrigatorio) {
			return true;
		} else if (menu.recursoObrigatorio.permissao.length === 0) {
			return true;
		} else {
			const recursoUsuario = perfil.recursos.get(
				menu.recursoObrigatorio.recurso
			);
			const recursoNecessario = menu.recursoObrigatorio;
			if (!recursoUsuario) {
				return true;
			}
			const userHasAtLeastOne = recursoNecessario.permissao.find((tipo) => {
				return recursoUsuario.permissao.includes(tipo);
			});
			return !!userHasAtLeastOne;
		}
	}

	/**
	 * Caso a configuração de ajustas nomenclatura dos termos de crossfit
	 * seja usado, alterar os termos.
	 */
	private ajustCrossfitMenus(menu: TreinoMenuConfig): TreinoMenuConfig {
		const trocarNomes =
			this.treinoConfigCache.configuracoesGerais.troca_nomenclatura_crossfit;
		const map = {
			wod: "objetivo-crossfit",
			"tipos-wod": "periodizacao",
		};
		if (trocarNomes && map[menu.menuId]) {
			menu.menuId = map[menu.menuId];
		}
		return menu;
	}
}
