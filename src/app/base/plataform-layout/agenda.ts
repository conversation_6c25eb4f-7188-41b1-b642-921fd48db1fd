import {
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	PerfilRecursoPermissoTipo,
	PerfilAcessoFuncionalidade,
	PerfilAcessoRecurso,
} from "treino-api";
import { TreinoMenuConfig } from "./treino-navigation-menu.model";

export const agendaMenusConfig: TreinoMenuConfig[] = [
	{
		menuId: "dashboard",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "dashboard",
		menuId: "dash",
		link: ["/agenda", "painel", "bi"],
	},
	{
		parentMenuId: "dashboard",
		menuId: "tv-aula",
		link: ["/tv-aula"],
		newTab: true,
	},
	{
		parentMenuId: "dashboard",
		menuId: "tv-gestor",
		link: ["/tv-gestor"],
		newTab: true,
	},
	{
		menuId: "operacoes",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "operacoes",
		menuId: "agenda-aulas",
		link: ["/agenda", "painel", "turmas"],
	},
	{
		parentMenuId: "operacoes",
		menuId: "agenda-servicos",
		link: ["/agenda", "painel", "servicos", "agendamentos"],
	},
	{
		menuId: "relatorios",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "relatorios",
		menuId: "ambiente",
		link: ["/agenda", "ambiente"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "modalidade",
		link: ["/agenda", "modalidade"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "indicadoresAgenda",
		link: ["/agenda", "indicadores-agenda"],
	},
	{
		parentMenuId: "relatorios",
		menuId: "aula-excluida",
		link: ["/agenda", "aula-excluida"],
	},
	{
		menuId: "cadastros",
		icon: "pct pct-file",
	},
	{
		parentMenuId: "cadastros",
		menuId: "aulas",
		link: ["/agenda", "aula"],
		funcionalidadeObrigatorio: new PerfilAcessoFuncionalidade(
			PerfilAcessoFuncionalidadeNome.CADASTRO_AULAS,
			true
		),
	},
	{
		parentMenuId: "cadastros",
		menuId: "tipoAgendamento",
		link: ["/agenda", "tipo-agendamento"],
		recursoObrigatorio: new PerfilAcessoRecurso(
			PerfilAcessoRecursoNome.TIPO_EVENTO,
			[
				PerfilRecursoPermissoTipo.CONSULTAR,
				PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR,
				PerfilRecursoPermissoTipo.TOTAL,
				PerfilRecursoPermissoTipo.EDITAR,
				PerfilRecursoPermissoTipo.INCLUIR,
				PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR,
			]
		),
	},
];
