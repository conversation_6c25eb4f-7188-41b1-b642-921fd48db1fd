import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { ActivatedRoute } from "@angular/router";
import { AdmCoreApiClienteService } from "adm-core-api";
import { TreinoApiAlunosService } from "treino-api";
import { TabelaLogComponent } from "ui-kit";
import { switchMap } from "rxjs/operators";

@Component({
	selector: "pacto-log-atividade",
	templateUrl: "./log-atividade.component.html",
	styleUrls: ["./log-atividade.component.scss"],
})
export class LogAtividadeComponent implements OnInit, AfterViewInit {
	url;
	matricula;
	aluno;

	@ViewChild("tabelaLog", { static: false })
	tabelaLog: TabelaLogComponent;

	constructor(
		private rest: RestService,
		private activatedRoute: ActivatedRoute,
		private alunoService: TreinoApiAlunosService,
		private clienteService: AdmCoreApiClienteService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.matricula =
			this.activatedRoute.snapshot.paramMap.get("aluno-matricula");
		this.alunoService
			.obterAlunoMatricula(this.matricula)
			.pipe(
				switchMap((aluno: any) => {
					return this.alunoService.obterAluno(aluno.id);
				})
			)
			.subscribe(
				(resp) => {
					this.aluno = resp;
					this.carregarDados();
					this.cd.detectChanges();
				},
				(error) => {
					this.cd.detectChanges();
				}
			);
	}

	ngAfterViewInit() {
		this.carregarDados();
	}

	carregarDados() {
		const idAluno = this.aluno ? this.aluno.id : 0;
		this.clienteService.dadosPessoais(this.matricula).subscribe((aluno) => {
			this.url = this.rest.buildLogAtividade(
				aluno.codigoCliente,
				aluno.codigoPessoa,
				idAluno
			);
			if (this.tabelaLog && this.tabelaLog.tableData) {
				this.tabelaLog.table.endpointUrl = this.url;
				this.tabelaLog.tableData.reloadData();
			}
			this.cd.detectChanges();
		});
	}
}
