import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { LogAtividadeComponent } from "./components/log-atividade.component";
import { Route, RouterModule } from "@angular/router";
import { BaseSharedModule } from "@base-shared/base-shared.module";

const routes: Route[] = [
	{
		path: "",
		component: LogAtividadeComponent,
		data: {},
	},
];

@NgModule({
	imports: [CommonModule, RouterModule.forChild(routes), BaseSharedModule],
})
export class LogAtividadeModule {}
