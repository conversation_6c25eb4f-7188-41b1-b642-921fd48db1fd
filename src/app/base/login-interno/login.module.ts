import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { BaseModule } from "../base.module";
import { LoginInternoComponent } from "./login-interno.component";

const routes: Routes = [
	{
		path: ":chave",
		component: LoginInternoComponent,
	},
];

@NgModule({
	declarations: [LoginInternoComponent],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		BaseSharedModule,
		BaseModule,
	],
	entryComponents: [],
	schemas: [NO_ERRORS_SCHEMA],
})
export class LoginModule {}
