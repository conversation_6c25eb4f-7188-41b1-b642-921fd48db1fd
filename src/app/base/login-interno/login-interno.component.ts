import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { LoginService } from "@base-core/rest/login.service";

@Component({
	selector: "pacto-login-interno",
	templateUrl: "./login-interno.component.html",
	styleUrls: ["./login-interno.component.scss"],
})
export class LoginInternoComponent implements OnInit {
	formGroup: FormGroup = new FormGroup({
		username: new FormControl("PACTOBR", [Validators.required]),
		empresa: new FormControl(1, [Validators.required]),
		senha: new FormControl("123", [Validators.required]),
	});
	loading = true;
	chave: string;
	url: string;
	items: Array<any>;

	constructor(
		private localStorageSession: LocalStorageSessionService,
		private session: SessionService,
		private loginService: LoginService,
		private notify: SnotifyService,
		private route: ActivatedRoute,
		private router: Router
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			this.chave = params.chave;
			this.loginService.discovery(this.chave).subscribe((dados) => {
				this.url = dados.serviceUrls.treinoApiUrl;
				this.items = new Array<any>();
				dados.empresas.forEach((emp) => {
					this.items.push({
						id: emp.codigo,
						label: emp.nome,
					});
				});
			});
		});
	}

	loginHandler() {
		for (const key in this.formGroup.controls) {
			if (this.formGroup.controls.hasOwnProperty(key)) {
				const element = this.formGroup.controls[key];
				element.markAsTouched();
			}
		}

		const data = this.formGroup.getRawValue();

		if (this.formGroup.valid) {
			this.formGroup.disable();
			this.loginService
				.login(this.chave, this.url, data.username, data.senha)
				.subscribe(
					(token) => {
						this.redirectToSystem(data.empresa, token);
						this.formGroup.enable();
					},
					(fail) => {
						this.formGroup.enable();
						if (fail.error.meta.error === "usuario_cancelado") {
							this.notify.error("Usuário cancelado.");
						} else if (fail.error.meta.message === "usuario-nao-encontrado") {
							this.notify.error("Usuário não encontrado.");
						} else {
							this.notify.error("Erro ao logar. Verifique as credenciais.");
						}
					}
				);
		}
	}

	private redirectToSystem(empresa, tkn) {
		const url = ["adicionarConta"];
		const module = "NTR";

		const queries = {
			token: tkn,
			empresaId: empresa,
			moduleId: module,
			modulos: "NTR",
		};

		this.router.navigate(url, {
			queryParams: queries,
		});
	}
}
