import { Injectable } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { Observable, of } from "rxjs";
import {
	AdmLegadoApiConfig,
	AdmLegadoApiConfigProviderBase,
} from "adm-legado-api";
import { ClientDiscoveryService } from "../microservices/client-discovery/client-discovery.service";

@Injectable()
export class AdmLegadoApiConfigProviderService extends AdmLegadoApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<AdmLegadoApiConfig> {
		const baseUrl = this.discoveryService.getUrlMap().zwUrl;
		return of({
			baseUrl,
		});
	}

	getApiConfigZWBoot(): Observable<AdmLegadoApiConfig> {
		const baseUrl = this.discoveryService.getUrlMap().zwBack;
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
