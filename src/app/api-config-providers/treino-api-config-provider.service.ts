import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";

import { TreinoApiConfigProviderBase, TreinoApiConfig } from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class TreinoApiConfigProvider extends TreinoApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<TreinoApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.discoveryService.getUrlMap().treinoApiUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
