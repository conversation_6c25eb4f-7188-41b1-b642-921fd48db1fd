import { Injectable } from "@angular/core";
import {
	IntegracaoGympassApiConfig,
	IntegracaoGympassApiConfigProviderBase,
} from "integracao-gympass-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";
import { Observable, of } from "rxjs";

@Injectable()
export class IntegracaoGympassApiConfigProvider extends IntegracaoGympassApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<IntegracaoGympassApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.discoveryService.getUrlMap().integracaoGympassMsUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
