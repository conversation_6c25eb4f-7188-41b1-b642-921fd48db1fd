import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../microservices/client-discovery/client-discovery.service";
import { Observable, of } from "rxjs";
import { PlanoApiConfig, PlanoApiConfigProviderBase } from "plano-api";

@Injectable()
export class PlanoApiConfigProviderService extends PlanoApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<PlanoApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().planoMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			locale: this.locale,
		});
	}
}
