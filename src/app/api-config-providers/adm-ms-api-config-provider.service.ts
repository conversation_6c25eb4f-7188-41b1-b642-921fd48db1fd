import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { AdmMsApiConfig, AdmMsApiConfigProviderBase } from "adm-ms-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService } from "sdk";

@Injectable()
export class AdmMsApiConfigProviderService extends AdmMsApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<AdmMsApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().admMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
			zwJSId: this.sessionService.zwJSId,
		});
	}
}
