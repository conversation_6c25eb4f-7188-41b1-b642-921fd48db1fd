import { Injectable } from "@angular/core";
import {
	MidiaSocialApiConfigProviderBase,
	MidiaSocialApiConfig,
} from "midia-social-api";
import { Observable, of } from "rxjs";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../microservices/client-discovery/client-discovery.service";

@Injectable()
export class MidiaSocialApiConfigProviderService extends MidiaSocialApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<MidiaSocialApiConfig> {
		console.log(this.discoveryService.getUrlMap());
		const empresaId = this.sessionService.empresaId;
		const token = localStorage.getItem("apiToken");
		console.log(this.discoveryService);
		return of({
			// baseUrl: 'http://d889-2804-1b1-bf00-8bcc-60b6-49c8-bc0b-ebfb.ngrok.io/midia-social-ms'
			baseUrl: this.discoveryService.getUrlMap().urlMidiaSocialMs,
			authToken: token,
			empresaId: empresaId ? `${empresaId}` : null,
			// acceptLanguage: this.locale
			"Accept-Language": "pt-BR",
		});
	}
}
