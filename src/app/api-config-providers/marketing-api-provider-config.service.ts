import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import {
	MarketingApiConfig,
	MarketingApiConfigProviderBase,
} from "marketing-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService } from "sdk";

@Injectable()
export class MarketingApiProviderConfigService extends MarketingApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private readonly locale,
		private readonly discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<MarketingApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().urlMarketingMs,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
