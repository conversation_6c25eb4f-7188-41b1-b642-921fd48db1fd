import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";

import {
	ZwPactopayApiConfigProviderBase,
	ZwPactopayApiConfig,
} from "zw-pactopay-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class ZwPactopayApiConfigProvider extends ZwPactopayApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<ZwPactopayApiConfig> {
		const baseUrl = `${this.discoveryService.getUrlMap().zwUrl}/prest/pactopay`;
		const key = this.sessionService.chave;
		const origin = window.location.host;
		const empresa = this.sessionService.empresaId;
		const username = this.sessionService.loggedUser.username;

		return of({
			baseUrl,
			key,
			origin,
			empresa,
			username,
		});
	}
}
