import { Inject, Injectable, LOCALE_ID } from "@angular/core";

import { Observable, of } from "rxjs";

import {
	CadastroAuxApiConfig,
	CadastroAuxApiConfigProviderBase,
} from "cadastro-aux-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class CadastroAuxApiConfigProviderService extends CadastroAuxApiConfigProviderBase {
	constructor(
		private discoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<CadastroAuxApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.cadastroAuxiliarUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
