import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { Observable, of } from "rxjs";

import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

import { ProdutoApiConfigProviderBase, ProdutoApiConfig } from "produto-api";

@Injectable()
export class ProdutoApiConfigProviderService extends ProdutoApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<ProdutoApiConfig> {
		const urlMap = this.discoveryService.getUrlMap();
		return of({
			baseUrl: urlMap.produtoMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: this.sessionService.empresaId,
			acceptLanguage: this.locale,
		});
	}
}
