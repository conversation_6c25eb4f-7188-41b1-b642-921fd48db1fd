import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";

import { PactoApiConfig, PactoApiConfigProviderBase } from "pacto-api";
import { ClientDiscoveryService } from "../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Injectable()
export class PactoApiConfigProviderService extends PactoApiConfigProviderBase {
	constructor(
		private readonly discoveryService: ClientDiscoveryService,
		private sessionService: SessionService
	) {
		super();
	}

	getApiConfig(): Observable<PactoApiConfig> {
		const baseUrl = `${this.discoveryService.getUrlMap().apiZwUrl}`;
		const authToken = this.sessionService.apiToken;

		return of({
			authToken,
			baseUrl,
		});
	}
}
