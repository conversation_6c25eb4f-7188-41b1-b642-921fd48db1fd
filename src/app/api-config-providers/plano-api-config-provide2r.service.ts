import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { Observable, of } from "rxjs";

import { PlanoApiConfig, PlanoApiConfigProviderBase } from "plano-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class PlanoApiConfigProvide2rService extends PlanoApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<PlanoApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().planoMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			locale: this.locale,
		});
	}
}
