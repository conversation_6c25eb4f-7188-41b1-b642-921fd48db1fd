import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { Observable, of } from "rxjs";

import { AdmCoreApiConfig, AdmCoreApiConfigProviderBase } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class AdmCoreApiConfigProvider extends AdmCoreApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<AdmCoreApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().admCoreUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
