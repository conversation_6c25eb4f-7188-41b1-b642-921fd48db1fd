import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { ClientDiscoveryService } from "sdk";
import { SessionService } from "@base-core/client/session.service";
import { Observable, of } from "rxjs";
import {
	PessoaMsApiConfigProviderBase,
	PessoaMsApiConfig,
} from "pessoa-ms-api";

@Injectable()
export class PessoaMsApiConfigProviderService extends PessoaMsApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {
		super();
	}

	getApiConfig(): Observable<PessoaMsApiConfig> {
		const empresaId = this.sessionService.empresaId;

		return of({
			baseUrl: this.discoveryService.getUrlMap().pessoaMsUrl,
			authToken: localStorage.getItem("apiToken"),
			empresaId: empresaId ? `${empresaId}` : null,
			acceptLanguage: this.locale,
		});
	}
}
