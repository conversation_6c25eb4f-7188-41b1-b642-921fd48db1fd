import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";

import { PactopayApiConfigProviderBase, PactopayApiConfig } from "pactopay-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class PactopayApiConfigProvider extends PactopayApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<PactopayApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.discoveryService.getUrlMap().pactoPayDashUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
