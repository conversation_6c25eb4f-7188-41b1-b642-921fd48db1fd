@import "src/assets/scss/pacto/plataforma-import.scss";

.hint-container-geral {
	display: inline-block;
}

.hint {
	color: $azulPactoPri;
	font-size: 20px;
	cursor: pointer;
	position: absolute;
	top: 0;
	left: 0;
}

.direita {
	top: -8px;
	left: 30px;
}

.hint-pacto:hover {
	#toolTip {
		display: block;
	}
}

.hint-pacto {
	position: relative;
	display: block;
	height: 20px;

	#toolTip {
		display: none;
		z-index: 999999;
		min-width: 160px;
		position: absolute;
		font-size: 14px;
		background-color: $branco;
		border: 1px solid $cinza05;
		border-radius: 6px;
		-moz-border-radius: 6px;
		-webkit-border-radius: 6px;
		box-shadow: 0px 0px 8px -1px $cinza05;
		-moz-box-shadow: 0px 0px 8px -1px $cinza05;
		-webkit-box-shadow: 0px 0px 8px -1px $cinza05;

		.verMais {
			padding: 10px;
			padding-top: 0;
			display: flex;
			color: #51555a;
			cursor: pointer;
			text-transform: none;

			span {
				text-decoration: underline;
				padding: 0;
			}

			i {
				margin-left: 3px;
				font-size: 15px;
				margin-top: 2px;
			}
		}
	}

	#toolTip span {
		padding: 10px;
		padding-bottom: 0;
		display: block;
		min-height: 10px;
		text-transform: none;
	}

	#tailShadow {
		background-color: transparent;
		width: 4px;
		height: 4px;
		position: absolute;
		top: 16px;
		left: -8px;
		z-index: -10;
		box-shadow: 0px 0px 8px 1px $cinza05;
		-moz-box-shadow: 0px 0px 8px 1px $cinza05;
		-webkit-box-shadow: 0px 0px 8px 1px $cinza05;
	}

	#tail1 {
		width: 0px;
		height: 0px;
		border: 10px solid;
		border-color: transparent $cinza05 transparent transparent;
		position: absolute;
		top: 8px;
		left: -20px;
	}

	#tail2 {
		width: 0px;
		height: 0px;
		border: 10px solid;
		border-color: transparent #ffffff transparent transparent;
		position: absolute;
		left: -18px;
		top: 8px;
	}
}
