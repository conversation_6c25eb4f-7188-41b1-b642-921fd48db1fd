import { Component, Input, OnInit } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-hint",
	templateUrl: "./hint.component.html",
	styleUrls: ["./hint.component.scss"],
})
export class HintComponent implements OnInit {
	@Input() id: string;
	@Input() username: string;
	tip;

	constructor(
		private activatedRoute: ActivatedRoute,
		private session: SessionService
	) {}

	ngOnInit() {
		this.obterConteudo();
	}

	verMais() {
		if (this.tip.verMais === "?") {
			document
				.getElementById("topbar-central-ajuda")
				.querySelector("i")
				.click();
		} else if (this.tip.verMais.startsWith("?")) {
			localStorage.setItem(
				"conhecimento-especifico",
				this.tip.verMais.replace("?", "")
			);
			document
				.getElementById("topbar-central-ajuda")
				.querySelector("i")
				.click();
		} else if (this.tip.verMais.startsWith("http")) {
			window.open(this.tip.verMais, "_blank");
		} else {
			window.open(
				"https://pactosolucoes.com.br/ajuda/search/" + this.tip.verMais,
				"_blank"
			);
		}
	}

	obterConteudo() {
		try {
			const item = JSON.parse(localStorage.getItem("tooltips"));
			const identificador =
				this.activatedRoute.snapshot["_routerState"].url.replace("/", "") +
				"_" +
				this.id;
			this.tip = item[identificador];
		} catch (e) {
			console.log(e);
		}
	}

	get mostrarPontoInterrogacaoNovo(): boolean {
		return (
			this.session &&
			this.session.chave &&
			(this.session.chave === "2ca9a679d8a65459cc7403412ed54317" ||
				this.session.chave === "384acd1173d8511f56c4b26a337499e6" ||
				this.session.chave === "810f691394b9cf79d33459a08861f3ed" ||
				this.session.chave === "aca438e8c9e947e64db2236bb2f1f7a9" ||
				this.session.chave === "b25c87c8369dbd7255724e185eddbb52" ||
				this.session.chave === "e82cdafdf149e399e18ae3eb096bd873" ||
				this.session.chave === "628519d73cf98a91c81fa2492e0a9189" ||
				this.session.chave === "f4bc544c4f6d26707d2e3a3efa9e8893" ||
				this.session.chave === "1c81b0d9bb3b592b05ee1558e154137c" ||
				this.session.chave === "704ee44b0ad0da5b7c1e195fd0c6dc32" ||
				this.session.chave === "48cdbf3f086c9fba71013394d13a3284" ||
				this.session.chave === "c8a81a93526bd96d745f4ddc0821d3e" ||
				this.session.chave === "bf71f05b219d99ed921ee0883a8fa555")
		);
	}

	get mostrarHint(): boolean {
		return (
			this.tip || (this.username && this.username.toLowerCase() === "pactobr")
		);
	}
}
