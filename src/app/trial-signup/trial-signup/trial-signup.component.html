<!--
  trial-signup-image.jpeg
  trial-signup-logo.png
-->

<div
	[ngStyle]="{
		backgroundImage: backgroundImage
	}"
	class="image"></div>

<div class="form-container">
	<img class="logo" src="assets/images/trial-signup-logo.png" />

	<div class="transforme">TRANSFORME SUAS CONSULTORIAS AGORA!</div>

	<div class="conheca">
		Conheça todos os recursos disponíveis que o App Treino Personal tem pra você
		e seus alunos
		<span class="destaque">gratuitamente</span>
		.
	</div>

	<div
		(@inOut.done)="inOutDoneHandler($event)"
		*ngIf="!loading"
		[@inOut]
		class="form-wrapper">
		<div class="preencha">
			Preencha os dados abaixo, baixe o app e utilize
			<span class="destaque">gr<PERSON>tis por 15 dias!</span>
		</div>
		<pacto-cat-form-input
			[control]="fg.get('nome')"
			[errorMsg]="'Forneça um nome'"
			[id]="'trial-signup-nome'"
			[label]="'Seu Nome'"
			[placeholder]="'Seu nome'"></pacto-cat-form-input>
		<pacto-cat-form-input
			[control]="fg.get('nomeapp')"
			[errorMsg]="'Forneça um nome para seu app'"
			[id]="'trial-signup-nome'"
			[label]="'Nome do seu app'"
			[placeholder]="'Nome do seu app'"></pacto-cat-form-input>
		<pacto-cat-form-input
			[control]="fg.get('email')"
			[errorMsg]="'Forneça um e-mail válido'"
			[id]="'trial-signup-email'"
			[label]="'E-mail'"
			[placeholder]="'E-mail'"></pacto-cat-form-input>
		<pacto-cat-form-input
			[control]="fg.get('celular')"
			[errorMsg]="'Celular no formato (62) 95845-1244'"
			[id]="'trial-signup-celular'"
			[label]="'Celular'"
			[placeholder]="'Digite seu celular'"
			[textMask]="{ mask: celMask, guide: false }"></pacto-cat-form-input>
		<div class="row">
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="fg.get('senha')"
					[errorMsg]="'Forneça uma senha com pelo menos 5 characteres'"
					[id]="'trial-signup-senha'"
					[label]="'Senha'"
					[placeholder]="'Informe a senha'"
					[type]="'password'"></pacto-cat-form-input>
			</div>
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="fg.get('codIndicacao')"
					[id]="'trial-signup-codIndicacao'"
					[label]="'Código de indicação'"
					[placeholder]="'Código de indicação'"></pacto-cat-form-input>
			</div>
		</div>

		<div class="preencha">
			Informe os dados de pagamento.
			<span class="destaque">
				Fique tranquilo, não haverá cobrança no período gratuito.
			</span>
		</div>

		<div class="row">
			<div class="col-md-12">
				<pacto-cat-form-input
					[control]="fg.get('numerocartao')"
					[errorMsg]="'Informe o nome assim como está impresso no cartão'"
					[id]="'numerocartao'"
					[label]="'Número do cartão'"
					[placeholder]="'Número do cartão'"
					[textMask]="{
						mask: mascaracartao,
						guide: true
					}"></pacto-cat-form-input>
				<pacto-bandeira-cartao
					[numero]="getNumeroCartao()"></pacto-bandeira-cartao>
			</div>
		</div>

		<pacto-cat-form-input
			[control]="fg.get('nomecartao')"
			[errorMsg]="'Informe o nome assim como está impresso no cartão'"
			[id]="'nomecartao'"
			[label]="'Nome impresso no cartão'"
			[placeholder]="
				'Preencha assim como está impresso no cartão'
			"></pacto-cat-form-input>

		<div class="row">
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="fg.get('validade')"
					[errorMsg]="'Informe a validade do cartão'"
					[id]="'validade'"
					[label]="'Validade'"
					[placeholder]="'MM/AA'"
					[textMask]="{
						mask: mascaravencimento,
						guide: true
					}"></pacto-cat-form-input>
			</div>
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="fg.get('cvv')"
					[id]="'cvv'"
					[label]="'Código de segurança'"
					[placeholder]="'CVV'"
					[textMask]="{
						mask: getMaskCVV(),
						guide: true
					}"></pacto-cat-form-input>
			</div>
		</div>
	</div>

	<div class="btn-wrapper">
		<button
			(click)="salvarHandler()"
			[@grow]="state"
			[ngClass]="{
				invalid: !validForm || loading
			}"
			class="criar-conta-btn">
			<span *ngIf="!loading">Criar Conta Gratuita</span>
			<span *ngIf="loading">
				Criando seu ambiente
				<img class="loading-icon" src="assets/images/loading_white.svg" />
			</span>
		</button>
	</div>

	<div *ngIf="!loading">
		<div class="seguros">
			<i class="pct pct-check"></i>
			Seus dados estão seguros, não compartilhamos com terceiros. Não fazemos
			spam!
		</div>
		<div *ngIf="showError" class="erros-form">
			Um ou mais campos possuem um erro. Verifique e tente novamente.
		</div>
	</div>
</div>

<pacto-traducoes-xingling #notificacoes>
	<span xingling="campos-obrigatorios">
		Campos obrigatórios não foram preenchidos.
	</span>
	<span xingling="empresa-duplicada">
		Já existe uma conta, cadastrada com esse mesmo email.
	</span>
	<span xingling="inicializar-empresa">
		Não foi possível criar a sua conta, favor aguardar alguns minutos, que
		íremos solucionar o problema.
	</span>
</pacto-traducoes-xingling>
