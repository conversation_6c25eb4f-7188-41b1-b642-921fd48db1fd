import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
} from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";

import { TrialSignupService, CreatedClientDto } from "../trial-signup.service";
import { ActivatedRoute, Router } from "@angular/router";
import {
	trigger,
	transition,
	animate,
	style,
	state,
} from "@angular/animations";
import { environment } from "src/environments/environment";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService } from "../../microservices/client-discovery/client-discovery.service";
// import {ReCaptchaV3Service} from 'ng-recaptcha';

declare var grecaptcha;

@Component({
	selector: "pacto-trial-signup",
	templateUrl: "./trial-signup.component.html",
	styleUrls: [
		"./trial-signup.component.scss",
		"./trial-signup.sm.component.scss",
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
	animations: [
		trigger("inOut", [
			transition("* => void", [
				animate(
					"250ms 0ms ease-in-out",
					style({
						height: "0px",
						opacity: 0,
					})
				),
			]),
		]),
		trigger("grow", [
			state(
				"grown",
				style({
					"line-height": "50px",
					"margin-top": "50px",
					"font-size": "14px",
					"background-color": "#ff416c",
				})
			),
			transition("* => grown", [animate("250ms 0ms ease-in-out")]),
		]),
	],
})
export class TrialSignupComponent implements OnInit {
	@ViewChild("notificacoes", { static: true })
	notificacoes: TraducoesXinglingComponent;
	fg = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		nomeapp: new FormControl("", [
			Validators.required,
			Validators.minLength(3),
		]),
		email: new FormControl("", [Validators.required, Validators.email]),
		celular: new FormControl("", [
			Validators.required,
			Validators.minLength(15),
		]),
		senha: new FormControl("", [Validators.required, Validators.minLength(5)]),
		codIndicacao: new FormControl(""),
		// cartao
		numerocartao: new FormControl("", [Validators.required]),
		nomecartao: new FormControl("", [Validators.required]),
		validade: new FormControl("", [Validators.required]),
		cvv: new FormControl("", [Validators.required]),
	});
	loading = false;
	dadosCampanha: any = {};
	state = "normal";
	private token;
	mascaracartao = [
		/\d/,
		/\d/,
		/\d/,
		/\d/,
		" ",
		/\d/,
		/\d/,
		/\d/,
		/\d/,
		" ",
		/\d/,
		/\d/,
		/\d/,
		/\d/,
		" ",
		/\d/,
		/\d/,
		/\d/,
		/\d/,
	];
	mascaravencimento = [/\d/, /\d/, "/", /\d/, /\d/];
	celMask = [
		"(",
		/[0-9]/,
		/[0-9]/,
		")",
		" ",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];

	constructor(
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private trialService: TrialSignupService,
		private discover: ClientDiscoveryService,
		private router: Router,
		private snotifyService: SnotifyService
	) {
		this.route.queryParams.subscribe((params) => {
			this.dadosCampanha.utm_source = params["utm_source"];
			this.dadosCampanha.utm_medium = params["utm_medium"];
			this.dadosCampanha.utm_campaign = params["utm_campaign"];
			if (params["indicacao"]) {
				this.fg.get("codIndicacao").setValue(params["indicacao"]);
			}
		});
		this.discover.discover().subscribe();
	}

	ngOnInit() {
		grecaptcha.ready(() => {
			grecaptcha
				.execute(environment.googleReCaptchaSiteKey, { action: "signup" })
				.then((token) => {
					this.token = token;
				});
		});
	}

	get showError() {
		return this.fg.touched && !this.fg.valid;
	}

	get validForm() {
		return this.fg.valid;
	}

	get backgroundImage() {
		return `url(${"assets/images/trial-signup-image.jpeg"})`;
	}

	inOutDoneHandler(event) {
		if (event.toState === "void") {
			this.state = "grown";
		}
	}

	salvarHandler() {
		this.markAsTouched();
		if (this.validForm) {
			const dto = this.fg.getRawValue();
			dto.captcha = this.token;
			dto.utmSource = this.dadosCampanha.utm_source;
			dto.utmMedium = this.dadosCampanha.utm_medium;
			dto.utmCampaign = this.dadosCampanha.utm_campaign;
			this.loading = true;
			this.cd.detectChanges();
			this.trialService.creatClient(dto).subscribe((client: any) => {
				this.loading = false;
				this.cd.detectChanges();
				if (client === "erro_empresa_duplicada") {
					this.snotifyService.error(
						this.notificacoes.getLabel("empresa-duplicada")
					);
				} else if (
					client === "erro_ativar_empresa" ||
					client === "erro_inesperado"
				) {
					this.snotifyService.error(
						this.notificacoes.getLabel("inicializar-empresa")
					);
				} else {
					this.cd.detectChanges();
					this.router.navigate(["adicionarConta"], { queryParams: client });
				}
			});
		} else {
			this.snotifyService.error(
				this.notificacoes.getLabel("campos-obrigatorios")
			);
		}
	}

	getNumeroCartao(): string {
		return this.fg.get("numerocartao").value
			? this.fg.get("numerocartao").value.replace(/ /g, "")
			: "";
	}

	private markAsTouched() {
		const controls = this.fg.controls;
		for (const control in controls) {
			if (controls.hasOwnProperty(control)) {
				this.fg.controls[control].markAsTouched();
			}
		}
	}

	getMaskCVV() {
		if (this.fg.get("numerocartao").value != null) {
			return sessionStorage.getItem("BAND") &&
				sessionStorage.getItem("BAND").includes("AMERICAN-EXPRESS")
				? [/\d/, /\d/, /\d/, /\d/]
				: [/\d/, /\d/, /\d/];
		} else {
			return [/\d/, /\d/, /\d/];
		}
	}
}
