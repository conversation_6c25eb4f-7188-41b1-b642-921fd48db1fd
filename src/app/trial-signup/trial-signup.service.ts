import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";

import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

export interface CreateClientDto {
	nome?: string;
	email?: string;
	celular?: string;
	senha?: string;
	captcha?: string;
	codIndicacao?: string;
	numerocartao?: string;
	nomecartao?: string;
	validade?: string;
	cvv?: string;
	utmSource?: string;
	utmMedium?: string;
	utmCampaign?: string;
}

export interface CreatedClientDto {
	token?: string;
	empresaId?: string;
	moduleId?: string;
	/**
	 * PlataformaModulo separados por virgula,
	 * exemplo?: "TR,NTR,NCR,NAV".
	 */
	modulos?: string;
	urlapi?: string;
}

@Injectable({
	providedIn: TrialSignupService,
})
export class TrialSignupService {
	constructor(
		private http: HttpClient,
		private discover: ClientDiscoveryService
	) {}

	creatClient(dto: CreateClientDto): Observable<CreatedClientDto> {
		const oamd = this.discover.getUrlMap().oamdUrl;
		const url = oamd + "/prest/empresa/ativarTrialNovoTreino";
		return this.http.post(url, dto).pipe(
			map((response: any) => {
				return response.return;
			}),
			catchError((error) => {
				return new Observable((observer) => {
					observer.next(error.error.meta.erro);
					observer.complete();
				});
			})
		);
	}
}
