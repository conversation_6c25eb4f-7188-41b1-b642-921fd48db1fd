import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Route } from "@angular/router";
import { TrialSignupComponent } from "./trial-signup/trial-signup.component";
import { TrialSignupService } from "./trial-signup.service";
import { BandeiraCartaoComponent } from "./bandeira-cartao/bandeira-cartao.component";

const routes: Route[] = [
	{
		path: "",
		component: TrialSignupComponent,
	},
];

@NgModule({
	imports: [CommonModule, BaseSharedModule, RouterModule.forChild(routes)],
	declarations: [TrialSignupComponent, BandeiraCartaoComponent],
	exports: [BandeiraCartaoComponent],
	providers: [TrialSignupService],
})
export class TrialSignupModule {}
