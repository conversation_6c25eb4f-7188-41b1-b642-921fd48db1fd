import {
	Component,
	OnInit,
	ViewChild,
	Input,
	TemplateRef,
	ViewEncapsulation,
	EventEmitter,
	Output,
	AfterViewInit,
	ElementRef,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { regex } from "@base-core/utils/regex";

import { Subscription, Observable } from "rxjs";
import { map, debounceTime } from "rxjs/operators";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import {
	DataGridService,
	DataFiltro,
	GridFilterConfig,
	PactoDataGridConfig,
	DataGridFilterComponent,
	PactoActionConfig,
	RelatorioExportarFormato,
} from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { HttpClient } from "@angular/common/http";
import { LocalizationService } from "@base-core/localization/localization.service";
import { RestService } from "@base-core/rest/rest.service";

export interface TableData<T> {
	/**
	 * Size of each page.
	 */
	size?: number;
	/**
	 * Data to be displayed.
	 */
	content?: Array<T>;
	/**
	 * Number of total elements.
	 */
	totalElements?: number;
	/**
	 * Number of current page, where zero is the first one.
	 */
	number?: number; // Zero-indexed
}

export enum PactoDataGridOrdenacaoDirecao {
	ASC = "ASC",
	DESC = "DESC",
}

declare var $;

@Component({
	selector: "pacto-share",
	templateUrl: "./share.component.html",
	styleUrls: ["./share.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [DataGridService],
})
export class ShareComponent implements OnInit, AfterViewInit {
	public selecionado: boolean;
	isShow = false;
	public link: string;
	public formatoAtual: RelatorioExportarFormato;
	inputMask;
	private isValidaMask = true;

	@Input() tableTitle: TemplateRef<any> | string;
	@Input() tableDescription: TemplateRef<any>;
	@Input() baseFilter: DataFiltro = {};
	@Input() filterConfig: GridFilterConfig;
	@Input() apiReturnProperty;
	@Input() table: PactoDataGridConfig = new PactoDataGridConfig({
		state: null,
		buttons: null,
		columns: [],
	});

	@Output() iconClick: EventEmitter<{ row: any; iconName: string }> =
		new EventEmitter();
	@Output() cellClick: EventEmitter<{ row: any; column: any }> =
		new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() btnClick: EventEmitter<any> = new EventEmitter();
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();

	@ViewChild("envioWppEmail", { static: false }) envioWppEmail;
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("dataGridFilter", { static: false })
	dataGridFilter: DataGridFilterComponent;
	@ViewChild("filterToggleButton", { static: false })
	filterToggleButton: ElementRef;
	@ViewChild("validaDocUrl", { static: true }) validaDocUrl;
	@ViewChild("validEmailUser", { static: true }) validEmailUser;

	ngbPage;
	private dataFetchSubscription: Subscription;
	private temporaryFilters;

	pageSizeControl: FormControl = new FormControl(999999);
	validaDocUrlControl: FormControl = new FormControl();
	whatsEmailControl: FormControl = new FormControl();
	dataFetchLoading = false;
	rawData = [];
	data: any = {
		content: [],
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		number: 0,
	};
	rawDataIcons: Array<Array<PactoActionConfig>> = [];
	quickSearchControl: FormControl = new FormControl();

	get RelatorioExportarFormato() {
		return RelatorioExportarFormato;
	}

	constructor(
		private dataService: DataGridService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private http: HttpClient,
		private localization: LocalizationService,
		private rest: RestService
	) {}

	ngOnInit() {}

	ngAfterViewInit() {}

	enviarEmail(
		endpointUrl: string,
		email: string,
		msgEmail: string,
		formato: RelatorioExportarFormato
	): Observable<string> {
		const url = `${this.rest.buildFullUrlTreino("", true)}enviar-email/send`;
		const params: any = {
			email,
			msgEmail,
			format: formato,
		};
		return this.http.get(url, { params }).pipe(
			map((result: any) => {
				return result.content;
			})
		);
	}

	private getParams(filtros): any {
		if (filtros.filters) {
			filtros.filters = JSON.stringify(filtros.filters);
		}
		if (filtros.configs) {
			filtros.configs = JSON.stringify(filtros.configs);
		}
		return filtros;
	}

	obterRelatorio(
		endpointUrl: string,
		filtros: DataFiltro,
		formato: RelatorioExportarFormato
	): Observable<string> {
		const url = `${endpointUrl}/exportar`;
		const paramsFiltro = this.getParams(filtros);
		const urlTreino = this.rest.buildFullUrlTreino("", true);
		const params: any = {
			filtros: filtros.filters,
			urlTreino,
			format: formato,
		};
		Object.assign(params, paramsFiltro);
		return this.http
			.get(url, {
				params,
			})
			.pipe(
				map((result: any) => {
					return result.content;
				})
			);
	}

	relatorioHandler(format: RelatorioExportarFormato) {
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));
		this.obterRelatorio(this.table.endpointUrl, baseFilter, format).subscribe(
			(link) => {
				this.link = link;
				if (this.link === undefined || this.link === null) {
					const validaDocUrl = this.validaDocUrl.nativeElement.innerHTML;
					this.notificationService.error(validaDocUrl);
				} else {
					this.selecionado = true;
					this.formatoAtual = format;
					this.cd.detectChanges();
				}
			}
		);
	}

	copyMessage() {
		const selBox = document.createElement("textarea");
		selBox.style.position = "fixed";
		selBox.style.left = "0";
		selBox.style.top = "0";
		selBox.style.opacity = "0";
		selBox.value = this.link;
		document.body.appendChild(selBox);
		selBox.focus();
		selBox.select();
		document.execCommand("copy");
		document.body.removeChild(selBox);
		this.toggleDisplay();
	}

	salvarDocumento() {
		window.open(this.link, "_blank");
	}

	async delay(ms: number) {
		await new Promise((resolve) => setTimeout(() => resolve(), ms)).then();
	}

	async toggleDisplay() {
		this.isShow = !this.isShow;
		this.delay(3000).then((any) => {
			this.isShow = false;
			this.cd.detectChanges();
		});
	}

	enviar() {
		const valorInserido = this.envioWppEmail.nativeElement.value;
		const valorInput = Number(
			valorInserido
				.replace("(", "")
				.replace(")", "")
				.replace(" ", "")
				.replace("-", "")
		);
		const enviarEmail = isNaN(valorInput);
		const valorValido = this.validarValor(valorInserido, enviarEmail);
		if (valorValido) {
			if (enviarEmail) {
				this.enviarEmail(
					this.table.endpointUrl,
					valorInserido,
					this.link,
					this.formatoAtual
				).subscribe((link) => {
					this.notificationService.success("E-mail enviado com sucesso");
				});
			} else {
				const target =
					"https://api.whatsapp.com/send?phone=" +
					"55" +
					valorInput +
					"&text=" +
					this.link;
				window.open(target, "_blank");
			}
		}
	}

	validarValor(valorInserido: any, telefone: boolean) {
		if (valorInserido === "" || valorInserido.size < 3) {
			this.notificationService.error("Digite um E-mail ou WhatsApp válido");
			return false;
		}
		if (telefone) {
			if (!this.isValidEmail(valorInserido)) {
				this.emailAlunoInvalid();
				return false;
			}
		}
		return true;
	}

	public isValidEmail(email) {
		if (regex.email.test(email)) {
			return true;
		}
		return false;
	}

	keyPress(event: any) {
		const valorInserido = this.envioWppEmail.nativeElement.value;
		const length = valorInserido.length;
		if (length === 0) {
			this.isValidaMask = true;
			this.inputMask = null;
			this.cd.detectChanges();
		}
		if (length > 3) {
			if (this.isValidaMask) {
				const valorInput = Number(valorInserido);
				const isNumber = !isNaN(valorInput);
				if (isNumber) {
					this.inputMask = this.localization.getPhoneLocaleMask();
					this.isValidaMask = false;
					this.cd.detectChanges();
				}
			}
		}
	}

	emailAlunoInvalid() {
		const validEmailUser = this.validEmailUser.nativeElement.innerHTML;
		this.notificationService.error(validEmailUser);
	}
}
