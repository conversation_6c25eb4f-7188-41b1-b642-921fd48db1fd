<div *ngIf="!selecionado" class="description">
	<p>O que deseja compartilhar?</p>
</div>
<div *ngIf="!selecionado && !dataFetchLoading" class="inputWrapper">
	<div class="selectFormatWrapper">
		<div
			(click)="relatorioHandler(RelatorioExportarFormato.PDF)"
			class="selectPdf">
			<i class="pct pct-file"></i>
			<p>PDF</p>
		</div>
		<div
			(click)="relatorioHandler(RelatorioExportarFormato.XLS)"
			class="selectExcel">
			<i class="pct pct-file-text"></i>
			<p>EXCEL</p>
		</div>
	</div>
</div>
<div *ngIf="dataFetchLoading" class="loading-state">
	<span i18n="@@component-relatorio:carregando-dados" id="carregando_dados">
		Carregando dados...
	</span>
	<img class="loading-data-icon" />
</div>
<div *ngIf="selecionado" class="buttonsWrapper">
	<div (click)="copyMessage()" class="linkWrapper">
		<i class="pct pct-link"></i>
		Copiar link
	</div>
	<div *ngIf="isShow" class="div-sucesso">
		<i class="pct pct-check-circle"></i>
		Link copiado com sucesso
	</div>
	<div (click)="salvarDocumento()" class="linkWrapper">
		<i class="pct pct-save"></i>
		Salvar
	</div>
</div>

<div *ngIf="selecionado" class="inputWrapper">
	<div class="input">
		<input
			#envioWppEmail
			(keydown.enter)="enviar()"
			(keypress)="keyPress($event)"
			[textMask]="
				inputMask ? { mask: inputMask, guide: false } : { mask: false }
			"
			class="form-control"
			id="input-busca-rapida"
			placeholder="Digite seu WhatsApp ou E-mail"
			type="text" />
		<i class="pct pct-search"></i>
	</div>
</div>
<div *ngIf="selecionado" class="linkEmailWrapper">
	<pacto-cat-button
		(click)="enviar()"
		[icon]="'send'"
		[id]="'btn-enviar-relatorios'"
		[label]="'ENVIAR'"></pacto-cat-button>
</div>
<div class="table-content">
	<span
		#validaDocUrl
		[hidden]="true"
		i18n="@@relatorios:mensagem:valid-url-documento">
		Não foi possível criar o documento, tente novamente.
	</span>
	<span
		#validEmailUser
		[hidden]="true"
		i18n="@@crud-alunos:mensagem:valid-email-user">
		O Formato do email informado é inválido.
	</span>
</div>
