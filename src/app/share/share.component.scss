@import "~src/assets/scss/pacto/plataforma-import.scss";

.inputWrapper {
	position: relative;
	padding: 30px 30px 15px;
	display: -webkit-box;
	display: flex;
}

.linkEmailWrapper {
	padding: 5px 30px 15px;
	width: 100%;
	text-align: center;
	text-align: -webkit-right;
}

.linkWrapper {
	float: left;
	padding: 10px 30px 10px;
	cursor: pointer;

	.pct-link {
		padding-right: 5px;
		color: $azulimPri;
	}

	.pct-save {
		padding-right: 5px;
		color: $azulimPri;
	}
}

.enviarWrapper {
	float: right;
	padding: 0px 30px 15px;
}

.inputWrapper {
	justify-content: space-evenly;
	position: relative;
	padding: 15px 30px 10px;

	.title-aux-wrapper {
		margin-right: 30px;
	}

	.table-title {
		font-size: 16px;
		font-weight: 700;
		color: rgb(51, 51, 51);
	}

	.table-description {
		font-size: 13px;
		font-weight: rgb(165, 165, 165);
		font-weight: 300;
	}

	.input {
		position: relative;
		flex-grow: 1;

		input {
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}
}

@media (max-width: 1024px) {
	.input {
		display: none;
	}

	.inputWrapper {
		justify-content: space-between;
	}
}

.description {
	padding: 0px 30px 15px;
}

.selectPdf {
	cursor: pointer;
	float: left;
	padding: 0px 30px 15px;
	font-weight: 600;

	.pct-file {
		font-size: 40px;
	}
}

.selectExcel {
	cursor: pointer;
	float: right;
	padding: 0px 30px 15px;
	font-weight: 600;

	.pct-file-text {
		font-size: 40px;
	}
}

.selectImprimir {
	cursor: pointer;
	float: right;
	padding: 0px 30px 15px;
	font-weight: 600;

	.pct-printer {
		font-size: 40px;
		padding-left: 25%;
	}
}

.procedure-details {
	max-height: 100px;
	transition: 0.8s;
	overflow: hidden;
}

.procedure-details.hidden {
	max-height: 0;
}

.div-sucesso {
	background: $branco;
	color: $pretoPri;
	box-shadow: 0px 1px 4px $cinza05;
	border-radius: 4px;
	position: fixed;
	will-change: transform;
	padding: 5px;
	top: 24%;
	left: 5%;
	transform: translate3d(24px, 136px, 0px);

	.pct-check-circle {
		color: $chuchuzinhoPri;
	}
}

.selectFormatWrapper {
	width: 100%;
	text-align: center;
	display: contents;
}

.buttonsWrapper {
	display: flex;
	border-bottom: 1px solid $cinzaPri;
	justify-content: space-around;
}

.loading-state {
	padding: 0px 30px 15px;
	font-size: x-large;
	font-weight: 600;
}
