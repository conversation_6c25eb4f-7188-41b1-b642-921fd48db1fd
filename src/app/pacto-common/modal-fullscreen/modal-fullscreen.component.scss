@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: flex;
	flex-direction: column;
	align-items: center;
	overflow-y: auto;
	overflow-x: hidden;
	background-color: #eff2f7;
}

.border-box {
	height: 1px;
	width: 100vw;
	border-bottom: 1px solid $cinza02;
}

.body-modal {
	display: flex;
	justify-content: center;
	flex-shrink: 0;
	width: 100%;
	background-color: white;
}

@media (max-width: 1200px) {
	.title-row {
		min-height: 204px !important;
	}
}

.title-row {
	min-height: 150px;
	display: flex;
	align-items: center;
	flex-shrink: 0;
	@include plataformaConteudoLargura;

	.row {
		flex-grow: 1;
	}

	.pct-x {
		font-size: 28px;
		color: $pretoPri;
		cursor: pointer;
		position: relative;
		top: 10px;
	}

	._title {
		@extend .type-h2;
		$color: $pretoPri;
	}
}

.form-body {
	@include plataformaConteudoLargura;
}
