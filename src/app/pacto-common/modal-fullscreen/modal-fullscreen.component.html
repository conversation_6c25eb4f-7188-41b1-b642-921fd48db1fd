<div class="title-row">
	<div class="row">
		<div *ngIf="!headerUseComponent" class="col-lg-2 col-md-1">
			<i (click)="closeHandler()" class="pct pct-x"></i>
		</div>

		<div class="col-lg-10 col-md-11">
			<span *ngIf="!header || !headerUseComponent" class="_title">
				{{ title }}
			</span>
			<div #headerRef></div>
			<ng-content select="modal-fullscreen-header"></ng-content>
		</div>
	</div>
</div>

<div class="border-box"></div>

<div class="body-modal">
	<div class="form-body">
		<div class="row">
			<div class="col-md-12 col-lg-12">
				<!-- PASS IN CONTENT THROUGH CONTROLLER -->
				<div #wrapperContainerRef></div>

				<!-- PASS IN CONTENT THROUGH TEMPLATE -->
				<ng-content select="modal-fullscreen-body"></ng-content>
			</div>
		</div>
	</div>
</div>
