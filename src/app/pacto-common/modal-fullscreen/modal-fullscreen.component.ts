import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ContentChild,
	Directive,
	ComponentFactoryResolver,
	ViewChild,
	ViewContainerRef,
	Output,
	EventEmitter,
	TemplateRef,
} from "@angular/core";

@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "modal-fullscreen-header",
})
// tslint:disable-next-line:directive-class-suffix
export class ModalFullscreenHeader {}

@Component({
	selector: "pacto-modal-fullscreen",
	templateUrl: "./modal-fullscreen.component.html",
	styleUrls: ["./modal-fullscreen.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalFullscreenComponent implements OnInit {
	@Input() title: string;
	@Output() dismiss: EventEmitter<boolean> = new EventEmitter();
	// tslint:disable-next-line: no-output-native
	@Output() close: EventEmitter<any> = new EventEmitter();
	@ViewChild("wrapperContainerRef", { read: ViewContainerRef, static: true })
	wrapperContainerRef;
	@ViewChild("headerRef", { read: ViewContainerRef, static: true }) headerRef;
	@ContentChild(ModalFullscreenHeader, { static: false }) header;

	componentRef: any;
	headerComponentRef: any;
	headerUseComponent = false;

	constructor(private componentFactoryResolver: ComponentFactoryResolver) {}

	ngOnInit() {}

	injectBodyComponent(bodyComponent) {
		const factory =
			this.componentFactoryResolver.resolveComponentFactory(bodyComponent);
		this.wrapperContainerRef.clear();
		this.componentRef =
			this.wrapperContainerRef.createComponent(factory).instance;
		return this.componentRef;
	}

	injectHeaderComponent(headerComponent) {
		this.headerUseComponent = true;
		const factory =
			this.componentFactoryResolver.resolveComponentFactory(headerComponent);
		this.headerRef.clear();
		this.headerComponentRef = this.headerRef.createComponent(factory).instance;
		return this.headerComponentRef;
	}

	closeHandler() {
		this.dismiss.emit(true);
	}
}
