import {
	Component,
	OnInit,
	ComponentFactoryResolver,
	ViewChild,
	ViewContainerRef,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { BehaviorSubject } from "rxjs";

@Component({
	selector: "pacto-modal-wrapper",
	templateUrl: "./modal-wrapper.component.html",
	styleUrls: ["./modal-wrapper.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalWrapperComponent implements OnInit {
	private stepNumber$: BehaviorSubject<number> = new BehaviorSubject(0);
	@ViewChild("wrapperContainerRef", { read: ViewContainerRef, static: true })
	wrapperContainerRef;
	private componentRef: any;
	title: any;

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private componentFactoryResolver: ComponentFactoryResolver
	) {}

	get firstStep() {
		return this.stepNumber$.value === 0;
	}

	ngOnInit() {
		this.stepNumber$.subscribe(() => this.cd.detectChanges());
	}

	goBackHandler() {
		const current = this.stepNumber$.value;
		this.stepNumber$.next(current > 0 ? current - 1 : 0);
	}

	loadModal(title, component): any {
		this.title = title;
		const factory =
			this.componentFactoryResolver.resolveComponentFactory(component);
		this.wrapperContainerRef.clear();
		this.componentRef =
			this.wrapperContainerRef.createComponent(factory).instance;
		this.componentRef.stepNumber$ = this.stepNumber$;
		return this.componentRef;
	}

	closeHandler() {
		this.activeModal.dismiss();
	}
}
