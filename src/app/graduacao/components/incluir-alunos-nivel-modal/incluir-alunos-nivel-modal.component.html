<div class="body">
	<ng-container *ngIf="step === 0">
		<pacto-cat-form-select-filter
			[control]="fg.get('nivel')"
			[errorMsg]="'Defina um nível'"
			[imageKey]="'fotoUri'"
			[labelKey]="'nome'"
			[label]="'Nível'"
			[options]="niveis ? niveis : []"></pacto-cat-form-select-filter>
	</ng-container>

	<div class="footer">
		<pacto-cat-button
			(click)="proximoHandler()"
			[disabled]="!nivelSelecionado"
			[icon]="'pct pct-arrow-right'"
			[label]="'Selecionar Alunos'"></pacto-cat-button>
	</div>
</div>
