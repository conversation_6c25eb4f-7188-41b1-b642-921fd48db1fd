@import "src/assets/scss/pacto/plataforma-import.scss";

.body {
	padding: 0px 30px;
}

.footer {
	padding: 30px 0px;
	border-top: 1px solid $cinza02;
}

.nome-filter {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	margin: 15px 0px;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

.header {
	display: flex;

	.item {
		line-height: 48px;
		@extend .type-btn-bold;
		text-transform: uppercase;
	}

	.todos {
		cursor: pointer;
	}

	.nome {
		flex-basis: 80%;
	}
}

.scroll-aux {
	height: 365px;
}

.aluno {
	display: flex;
	align-items: center;
	border-bottom: 1px solid $cinzaPri;

	.nome {
		flex-basis: 80%;
		@extend .type-caption-rounded;
		color: $cinza03;
		text-transform: uppercase;
		line-height: 44px;
	}
}

.color-error {
	color: #ca0000;
}

.div-info-aluno-lista {
	padding-top: 15px;
	height: 348px;
}

.font-size-11 {
	font-size: 11px;
}

.modal-lista-atividade {
	padding: 0 30px 30px 30px;
}

.modal-lista-atividade tr {
	display: flex;
	align-items: center;
	padding: 12px;
}

.modal-lista-atividade tr:nth-child(odd) {
	background: #eff2f7;
}

.modal-lista-atividade pacto-cat-person-avatar {
	margin-right: 10px;
}

.titulo-atividades {
	margin: 0 0 10px 10px;
}

.footer {
	display: flex;
	justify-content: flex-end;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	::ng-deep .modal-incluir-aluno-nivel {
		pacto-modal-wrapper {
			pacto-relatorio {
				.pacto-table-title-block {
					padding: 10px 10px;
					display: block;

					.command {
						font-size: 10px;
					}

					.search {
						margin: 12px 0;
						display: block;

						input {
							width: 100%;
						}
					}

					.actions {
						justify-content: end;
					}
				}

				.table-content {
					overflow: auto;

					.table {
						width: 1000px;
						max-width: 1000px;
					}
				}
			}
		}
	}
}
