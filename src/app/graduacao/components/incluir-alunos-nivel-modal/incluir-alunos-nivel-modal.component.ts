import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { Colaborador } from "src/app/microservices/personagem/colaborador/colaborador.model";
import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { ClienteService } from "../../../microservices/personagem/cliente/cliente.service";
import { BehaviorSubject } from "rxjs";
import { NivelSimple } from "../../../microservices/graduacao/nivel/nivel.model";
import { AtividadeGraduacaoService } from "../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.service";
import { IncluirAlunosFichaModalComponent } from "../incluir-alunos-ficha-modal/incluir-alunos-ficha-modal.component";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { SnotifyService } from "ng-snotify";
import { AtividadeGraduacao } from "../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";

@Component({
	selector: "pacto-incluir-alunos-nivel-modal",
	templateUrl: "./incluir-alunos-nivel-modal.component.html",
	styleUrls: ["./incluir-alunos-nivel-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IncluirAlunosNivelModalComponent implements OnInit {
	@ViewChild("alunoInserido", { static: true }) alunoInserido;
	ficha: Ficha;
	stepNumber$: BehaviorSubject<number>;
	loading = false;
	niveis: NivelSimple[];
	alunos: Cliente[] = [];
	atividades: AtividadeGraduacao;
	selectedFg = new FormGroup({});
	fg: FormGroup = new FormGroup({
		data: new FormControl(new Date().valueOf(), (fc) => {
			const value = fc.value;
			if (value === null || value === false) {
				return { error: true };
			} else {
				return null;
			}
		}),
		nivel: new FormControl(null, Validators.required),
	});
	stop: any;

	get step() {
		return this.stepNumber$.value;
	}

	constructor(
		private modal: NgbActiveModal,
		private nivelService: NivelService,
		private clientService: ClienteService,
		private cd: ChangeDetectorRef,
		private atividadeService: AtividadeGraduacaoService,
		private modalService: ModalService,
		private snotify: SnotifyService
	) {}

	atividadeFc: FormControl = new FormControl(null);

	formGroup = new FormGroup({
		nome: new FormControl(null, [Validators.required, Validators.minLength(3)]),
		tecnica: new FormControl(),
		objeto: new FormControl(),
		ativo: new FormControl(true),
		cor: new FormControl(),
	});

	proximoHandler() {
		if (this.fg.get("nivel").value.id > 0) {
			const classSizeModal =
				window.innerWidth < 580
					? "modal-incluir-aluno-nivel modal-sm"
					: "modal-mxl";
			const ref = this.modalService.open(
				"Incluir Alunos",
				IncluirAlunosFichaModalComponent,
				PactoModalSize.LARGE,
				classSizeModal
			);
			const modal: IncluirAlunosFichaModalComponent = ref.componentInstance;
			modal.loadData(this.ficha, this.fg.get("nivel").value.id, null, "nivel");
			ref.result.then((dto) => {
				this.modal.close(dto);
			});
		}
	}

	get nivelSelecionado(): boolean {
		return (
			this.fg &&
			this.fg.get("nivel") &&
			this.fg.get("nivel").value &&
			this.fg.get("nivel").value.id > 0
		);
	}

	loadData(atividades: any[], ficha: Ficha, niveis: NivelSimple[]) {
		this.niveis = niveis;
		this.ficha = ficha;
		this.cd.detectChanges();
	}

	ngOnInit() {
		this.stepNumber$.subscribe(() => {
			this.cd.detectChanges();
		});
		this.selectedFg.valueChanges.subscribe(() => {
			this.cd.detectChanges();
		});
	}

	private getDto() {
		return {
			// avaliadorId: this.fg.get('avaliador').value.id,
			nivel: this.fg.get("nivel").value.id,
			// alunoIds: this.getSelectedIds()
		};
	}

	private getSelectedIds() {
		const raw = this.selectedFg.getRawValue();
		const result = [];
		for (const id in raw) {
			// eslint-disable-next-line no-prototype-builtins
			if (raw.hasOwnProperty(id)) {
				if (raw[id]) {
					result.push(id);
				}
			}
		}
		return result;
	}

	private markAsTouched() {
		this.fg.get("nivel").markAsTouched();
	}

	private buildFc() {
		this.alunos.forEach((aluno) => {
			this.selectedFg.addControl(`${aluno.id}`, new FormControl());
		});
	}
}
