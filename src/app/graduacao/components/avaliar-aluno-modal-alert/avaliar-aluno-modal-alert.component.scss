@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: flex;
	padding: 30px;
	height: 470px;
	flex-direction: column;
	position: relative;
	align-items: center;
}

.body {
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.alert {
	font-size: 120px;
	color: $hellboyPri;
	margin: 0px;
}

.title {
	@extend .type-h1-bold;
	color: $cinza05;
}

.description {
	@extend .type-h5;
	color: $cinza05;
	margin: 15px 0px;
	text-align: center;
}

.footer {
	display: flex;
	justify-content: space-between;
}

.close-icon {
	position: absolute;
	font-size: 20px;
	cursor: pointer;
	right: 30px;
	top: 30px;
}

.footer {
	display: flex;
	flex-direction: row-reverse;
	justify-content: center;

	pacto-cat-button {
		margin: 0px 7.5px;
		width: 120px;
	}
}

::ng-deep .modal-alert-avaliacao {
	background: rgba(0, 0, 0, 0.5);
}
