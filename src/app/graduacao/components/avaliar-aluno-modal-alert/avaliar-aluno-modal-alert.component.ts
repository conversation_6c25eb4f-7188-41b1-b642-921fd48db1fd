import { Component, OnInit, ChangeDetectionStrategy } from "@angular/core";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-avaliar-aluno-modal-alert",
	templateUrl: "./avaliar-aluno-modal-alert.component.html",
	styleUrls: ["./avaliar-aluno-modal-alert.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliarAlunoModalAlertComponent implements OnInit {
	constructor(private modal: NgbActiveModal) {}

	ngOnInit() {}

	dismissHandler() {
		this.modal.dismiss();
	}

	confirmHandler() {
		this.modal.close(true);
	}
}
