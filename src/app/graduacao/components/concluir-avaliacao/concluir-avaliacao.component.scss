@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.body {
	padding: 15px 30px 15px;

	.scroll-aux {
		height: 300px;
	}

	.header-row {
		display: flex;
		align-items: center;
		padding: 0px 15px;

		.header {
			@extend .type-h6;
			color: $pretoPri;
			line-height: 64px;
			flex-grow: 1;
			text-transform: uppercase;
		}
	}

	.aluno-row {
		display: flex;
		align-items: center;
		padding: 0px 15px;

		&:nth-child(2n + 1) {
			background-color: $cinza01;
		}
	}

	.aluno {
		display: flex;
		flex-grow: 1;

		.nome {
			@extend .type-h6;
			color: $preto04;
			line-height: 64px;
			display: flex;
			align-items: center;
		}

		pacto-cat-person-avatar {
			display: flex;
			margin-right: 15px;
			flex-direction: column;
			justify-content: center;
		}
	}

	.nivel {
		flex-basis: 300px;
	}

	.msg {
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		height: 364px;

		.desc {
			margin: 15px 0px;
			text-align: center;
			width: 350px;
		}

		i.pct {
			font-size: 120px;
			color: $chuchuzinhoPri;
		}
	}

	.div-data-troca {
		margin-right: 86px;
	}
}

.footer {
	border-top: 1px solid $cinza02;
	display: flex;
	justify-content: center;
	padding: 30px 0px;

	pacto-cat-button {
		width: 200px;
	}
}

.titulo {
	@extend .type-h6;
	color: $preto04;
	line-height: 40px;
	display: flex;
	align-items: center;
}

.row-titulo-datas {
	margin-top: 8px;
}

.row-titulo-listagem {
	margin-top: 20px;
}

@media (max-width: 580px) {
	.row-titulo-datas {
		display: block !important;

		> * {
			padding: 0;
		}

		.div-data-troca {
			margin-top: 12px;
		}
	}

	.div-alunos-conclusao {
		overflow: auto;

		> * {
			width: 600px;
		}
	}

	.msg {
		.title {
			text-align: center;
		}

		.desc {
			text-align: center;
		}
	}
}
