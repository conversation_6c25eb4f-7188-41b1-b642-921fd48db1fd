<div class="body">
	<ng-container *ngIf="!done">
		<div class="d-flex justify-content-between row-titulo-datas">
			<div class="col-md-4">
				<div>
					<span class="titulo">DATA DA AVALIAÇÃO</span>
					<pacto-cat-datepicker
						[className]="'datePicker'"
						[formControl]="dataAvaliacao"></pacto-cat-datepicker>
				</div>
			</div>
			<div class="col-md-4 div-data-troca">
				<span class="titulo">DATA DA TROCA DE NÍVEL</span>
				<pacto-cat-datepicker
					[className]="'datePicker'"
					[formControl]="dataTrocaDeNivel"></pacto-cat-datepicker>
			</div>
		</div>

		<div class="div-alunos-conclusao">
			<div class="d-flex justify-content-between row-titulo-listagem">
				<div class="col-md-4">
					<span class="titulo">ALUNO</span>
				</div>
				<div class="col-md-4 div-data-troca">
					<span class="titulo">CONCLUSÃO</span>
				</div>
			</div>

			<div [maxHeight]="'450px'" class="scroll-aux" pactoCatSmoothScroll>
				<div *ngFor="let aluno of alunos" class="aluno-row">
					<div class="aluno">
						<pacto-cat-person-avatar
							[diameter]="32"
							[uri]="aluno.imageUri"></pacto-cat-person-avatar>
						<div class="nome">{{ aluno.nome }}</div>
					</div>
					<div class="nivel">
						<pacto-cat-select-filter
							[control]="getNivelFc(aluno.id)"
							[imageKey]="'fotoUri'"
							[labelFn]="labelFnFactory(aluno.id)"
							[labelKey]="'nome'"
							[options]="niveis"></pacto-cat-select-filter>
					</div>
				</div>
			</div>
		</div>
	</ng-container>

	<ng-container *ngIf="done">
		<div class="msg">
			<i class="pct pct-check"></i>
			<div class="title type-h1-bold cor-cinza05">Avaliação Concluída</div>
			<div class="desc type-h5 cor-cinza05">
				Todos alunos foram avaliados e a avaliação está encerrada.
			</div>
		</div>
	</ng-container>
</div>

<div class="footer">
	<pacto-cat-button
		(click)="proximoHandler()"
		*ngIf="!done"
		[disabled]="loading"
		[full]="true"
		[label]="'Confirmar'"></pacto-cat-button>
	<pacto-cat-button
		(click)="confirmarHandler()"
		*ngIf="done"
		[full]="true"
		[label]="'confirmar e sair'"></pacto-cat-button>
</div>
