import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { NivelSimple } from "src/app/microservices/graduacao/nivel/nivel.model";
import { AvaliacaoProgressoService } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.service";
import { AvaliacaoLivreService } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.service";
import { GraduacaoAvaliacaoTipo } from "src/app/microservices/graduacao/graduacao.model";

export interface ConcluirAvaliacaoAluno {
	aluno: Cliente;
	nivelSugeridoId: number;
	nivelAtualId: number;
}

@Component({
	selector: "pacto-concluir-avaliacao",
	templateUrl: "./concluir-avaliacao.component.html",
	styleUrls: ["./concluir-avaliacao.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConcluirAvaliacaoComponent implements OnInit {
	done = false;
	loading = false;
	niveis: NivelSimple[] = [];
	alunos: Cliente[] = [];
	niveisFg: FormGroup;
	tipo: GraduacaoAvaliacaoTipo;
	avaliacaoId: number;
	niveisAtuais: { [alunoId: number]: number } = {};
	dataAvaliacao = new FormControl();
	dataTrocaDeNivel = new FormControl();

	constructor(
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private avaliacaoLivreService: AvaliacaoLivreService,
		private avaliacaoProgressoService: AvaliacaoProgressoService
	) {}

	ngOnInit() {}

	loadData(
		alunosConfig: ConcluirAvaliacaoAluno[],
		niveis: NivelSimple[],
		tipo: GraduacaoAvaliacaoTipo,
		avaliacaoId: number,
		dataAvaliacao: number
	) {
		this.niveis = niveis;
		this.alunos = alunosConfig.map((item) => item.aluno);
		this.niveisFg = new FormGroup({});
		this.avaliacaoId = avaliacaoId;
		this.tipo = tipo;
		this.dataAvaliacao.setValue(dataAvaliacao);
		this.dataTrocaDeNivel.setValue(new Date().valueOf());

		alunosConfig.forEach((item) => {
			this.niveisAtuais[item.aluno.id] = item.nivelAtualId;
			const nivel = this.niveis.find(
				(findItem) => findItem.id === item.nivelSugeridoId
			);
			this.niveisFg.addControl(
				item.aluno.id.toString(),
				new FormControl(nivel)
			);
		});
		this.cd.detectChanges();
	}

	labelFnFactory(alunoId) {
		const nivelId = this.niveisAtuais[alunoId];
		return (nivel: NivelSimple) => {
			const current = nivel.id === nivelId;
			return current ? `${nivel.nome} (nível atual)` : nivel.nome;
		};
	}

	getNivelFc(alunoId: number) {
		if (this.niveisFg.get(`${alunoId}`)) {
			return this.niveisFg.get(`${alunoId}`);
		} else {
			return new FormControl();
		}
	}

	proximoHandler() {
		if (this.loading) {
			return false;
		}
		const dto = this.getDto();
		if (this.tipo === GraduacaoAvaliacaoTipo.PROGRESSO) {
			this.loading = true;
			this.cd.detectChanges();
			this.avaliacaoProgressoService
				.concluir(this.avaliacaoId, dto, this.dataAvaliacao.value)
				.subscribe(() => {
					this.done = true;
					this.loading = false;
					this.cd.detectChanges();
				});
		} else if (this.tipo === GraduacaoAvaliacaoTipo.LIVRE) {
			this.loading = true;
			this.cd.detectChanges();
			this.avaliacaoLivreService
				.concluir(this.avaliacaoId, dto)
				.subscribe(() => {
					this.done = true;
					this.loading = false;
					this.cd.detectChanges();
				});
		}
	}

	confirmarHandler() {
		this.activeModal.close();
	}

	private getDto() {
		const raw = this.niveisFg.getRawValue();
		const out = [];
		for (const id in raw) {
			out.push({
				alunoId: parseInt(id, 10),
				nivelId: raw[id].id,
				dataTrocaDeNivel: this.dataTrocaDeNivel.value,
				aprovado: this.definirResultadoAvaliacaoAluno(
					parseInt(id, 10),
					raw[id].id
				),
			});
		}
		return out;
	}

	private definirResultadoAvaliacaoAluno(alunoId, novoNivelId) {
		// Definição de resultado da avaliação de progresso;
		// true: aprovado; false: reprovado;
		// Utiliza o index do nível para identificar se o aluno foi aprovado ou não;
		if (this.niveis.length === 0 || this.niveis.length === 1) {
			return null;
		} else {
			const ultimoNivel = this.niveis[this.niveis.length - 1];
			const nivelId = this.niveisAtuais[alunoId];
			const nivelAtual = this.niveis.find((x) => x.id === nivelId);
			const novoNivel = this.niveis.find((x) => x.id === novoNivelId);
			if (ultimoNivel.id === novoNivelId && ultimoNivel.id === nivelAtual.id) {
				return null;
			}
			return novoNivel.index > nivelAtual.index;
		}
	}
}
