import { Component, Input, OnInit } from "@angular/core";
import { FormArray, FormGroup } from "@angular/forms";
import {
	AtividadeGraduacao,
	SubAtividades,
} from "../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { Cliente } from "../../../microservices/personagem/cliente/cliente.model";
import { AvaliacaoGraduacaoCriterio } from "../../../microservices/graduacao/avaliacao-progresso/avaliacao.model";

@Component({
	selector: "pacto-avaliar-todos-alunos-modal-resposta",
	templateUrl: "./avaliar-todos-alunos-modal-resposta.component.html",
	styleUrls: ["./avaliar-todos-alunos-modal-resposta.component.scss"],
})
export class AvaliarTodosAlunosModalRespostaComponent implements OnInit {
	@Input() uri;
	@Input() name;

	@Input() formGroup: FormGroup;
	@Input() description;
	@Input() answerOptions: string[] = ["1", "2", "3", "4", "5"];
	@Input() observacao: string;

	@Input() atividade: AtividadeGraduacao;
	@Input() subAtividades: SubAtividades[] = [];
	@Input() avaliados: Cliente[] = [];
	@Input() criterio: AvaliacaoGraduacaoCriterio;
	criterioSimNao = false;

	constructor() {}

	ngOnInit() {
		if (!this.uri) {
			this.uri = "pacto-ui/images/default-user-icon.png";
		}
		this.validarTipoCriterio();
	}

	private validarTipoCriterio() {
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				this.criterioSimNao = true;
				break;
			default:
				this.criterioSimNao = false;
				this.montarAnswerOptions();
				break;
		}
	}

	disabled(avaliado) {
		return avaliado.get("resposta").disabled;
	}

	verificarResposta(index, avaliado) {
		let classeSelecionado = "nao-selecionado";
		const resposta = this.answer(avaliado);
		if (index === resposta) {
			classeSelecionado = resposta === 0 ? "selected-sim" : "selected-nao";
		}
		return classeSelecionado;
	}

	answer(avaliado) {
		return avaliado.get("resposta").value;
	}

	get subAtividadesControl() {
		return (this.formGroup.get("subAtividades") as FormArray).controls;
	}

	get avaliadosControl() {
		return (this.formGroup.get("avaliados") as FormArray).controls;
	}

	avaliadosSubAtividadeControl(subAtividade) {
		return (subAtividade.get("avaliados") as FormArray).controls;
	}

	getAvaliadoPorId(id) {
		return this.avaliados.find((avaliado) => avaliado.id === id);
	}

	getSubAtividadePorId(id) {
		return this.subAtividades.find((subAtividade) => subAtividade.id === id);
	}

	selectOptionHandler(index, avaliado) {
		if (!this.disabled(avaliado)) {
			avaliado.get("resposta").setValue(index);
		}
	}

	montarAnswerOptions() {
		const retorno: any[] = [];
		for (const answer of this.answerOptions) {
			// tratamento para problema do index 0 das respostas que fazia com que o botão de salvar não fosse habilitado
			retorno.push({
				id: this.answerOptions.findIndex((item) => item === answer) + 1,
				label: answer,
			});
		}
		this.answerOptions = retorno;
	}
}
