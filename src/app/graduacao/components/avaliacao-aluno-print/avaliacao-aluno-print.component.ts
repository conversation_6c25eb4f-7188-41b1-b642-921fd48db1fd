import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { FormGroup, FormControl, FormArray, Validators } from "@angular/forms";

import { Observable, zip } from "rxjs";
import { switchMap, tap } from "rxjs/operators";

import {
	AtividadeGraduacao,
	SubAtividades,
} from "src/app/microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { Colaborador } from "src/app/microservices/personagem/colaborador/colaborador.model";
import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { ClienteService } from "src/app/microservices/personagem/cliente/cliente.service";
import { ColaboradorService } from "src/app/microservices/personagem/colaborador/colaborador.service";
import {
	AvaliacaoAlunoResposta,
	GraduacaoAvaliacao,
} from "src/app/microservices/graduacao/graduacao.model";
import { AvaliacaoLivreService } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.service";
import { AvaliacaoLivreAlunoService } from "src/app/microservices/graduacao/avaliacao-livre-aluno/avaliacao-livre-aluno.service";
import { AvaliacaoProgressoService } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.service";
import { AvaliacaoProgressoAlunoService } from "src/app/microservices/graduacao/avaliacao-progresso-aluno/avaliacao-progresso-aluno.service";
import { AvaliacaoGraduacaoCriterio } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { NivelService } from "../../../microservices/graduacao/nivel/nivel.service";

@Component({
	selector: "pacto-avaliacao-aluno-print",
	templateUrl: "./avaliacao-aluno-print.component.html",
	styleUrls: ["./avaliacao-aluno-print.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoAlunoPrintComponent implements OnInit {
	avaliador: Colaborador;
	avaliado: Cliente;
	avaliacao: GraduacaoAvaliacao;
	perguntas: AtividadeGraduacao[] = [];
	respostas: AvaliacaoAlunoResposta[] = [];
	criterio: AvaliacaoGraduacaoCriterio;
	answerOptions = [];
	loading = true;
	canEdit;
	atividades: AtividadeGraduacao[] = [];

	respostasFg: FormGroup = new FormGroup({});
	observacaoGeralFc = new FormControl(null);

	get perguntaBlocks() {
		if (this.perguntas && this.perguntas.length) {
			const nOfBlocks = Math.ceil(this.perguntas.length / 2);
			return new Array(nOfBlocks).fill(0);
		} else {
			return [];
		}
	}

	constructor(
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private clienteService: ClienteService,
		private colaboradorService: ColaboradorService,
		private avaliacaoLivreAlunoService: AvaliacaoLivreAlunoService,
		private avaliacaoLivreService: AvaliacaoLivreService,
		private avaliacaoProgressoService: AvaliacaoProgressoService,
		private avaliacaoProgressoAlunoService: AvaliacaoProgressoAlunoService,
		private nivelService: NivelService
	) {}

	ngOnInit() {
		this.route.params.subscribe((params) => {
			const avaliacaoId = params.avaliacaoId;
			const avaliacaoAlunoId = params.avaliacaoAlunoId;
			const tipo = this.route.snapshot.queryParams.tipo;
			let loadData$;
			if (tipo === "livre") {
				loadData$ = this.loadDataAvaliacaoLivre(avaliacaoId, avaliacaoAlunoId);
			} else if (tipo === "progresso") {
				loadData$ = this.loadDataAvaliacaoProgresso(
					avaliacaoId,
					avaliacaoAlunoId
				);
			}
			loadData$.subscribe(() => {
				this.buildFormGroup(this.perguntas);
				this.filloutForm();
				this.loading = false;
				this.setOptions();
				this.cd.detectChanges();
				window.parent.postMessage("loaded", window.origin);
			});
		});
	}

	filloutForm() {
		// this.respostas.forEach(resposta => {
		//   this.respostasFg.get(`${resposta.atividade.id}`).get('resposta').setValue(resposta.resposta);
		//   this.respostasFg.get(`${resposta.atividade.id}`).get('observacao').setValue(resposta.observacao);
		// });
	}

	getRespostaFg(perguntaId: number) {
		return this.respostasFg.get(`${perguntaId}`);
	}

	private buildFormGroup(perguntas: AtividadeGraduacao[]) {
		this.respostasFg = new FormGroup({});
		// perguntas.forEach(pergunta => {
		//   this.respostasFg.addControl(`${pergunta.id}`, new FormGroup({
		//     resposta: new FormControl(null),
		//     observacao: new FormControl(null)
		//   }));
		// });

		perguntas.forEach((atividade) => {
			const possuiSubAtividade =
				atividade.subAtividades && atividade.subAtividades.length > 0;
			const subAtividadesFa: FormArray = this.buildSubAtividadesForm(
				atividade.id,
				atividade.subAtividades
			);
			let avaliadoFa = null;
			if (!possuiSubAtividade) {
				avaliadoFa = this.buildAvaliadosForm(atividade.id, null);
			}
			this.respostasFg.addControl(
				`${atividade.id}`,
				new FormGroup({
					atividadeId: new FormControl(atividade.id),
					possuiSubAtividade: new FormControl(possuiSubAtividade),
					subAtividades: possuiSubAtividade
						? subAtividadesFa
						: new FormControl(null),
					avaliados: possuiSubAtividade ? new FormControl(null) : avaliadoFa,
				})
			);
		});
	}

	buildSubAtividadesForm(atividadeId, subAtividades: SubAtividades[]) {
		const subAtividadesFa = new FormArray([]);
		subAtividades.forEach((subAtividade) => {
			const avaliadosFa: FormArray = this.buildAvaliadosForm(
				atividadeId,
				subAtividade.id
			);
			subAtividadesFa.controls.push(
				new FormGroup({
					subAtividadeId: new FormControl(subAtividade.id),
					avaliados: avaliadosFa,
				})
			);
		});
		return subAtividadesFa;
	}

	buildAvaliadosForm(atividadeId, subAtividadeId) {
		const avaliadosFa = new FormArray([]);

		const respostaAluno = this.getRespostaAluno(
			atividadeId,
			subAtividadeId,
			this.avaliado.id
		);
		const respostaId =
			respostaAluno !== null ? respostaAluno.respostaAvaliacaoId : null;
		const resposta = respostaAluno !== null ? respostaAluno.resposta : null;
		const observacao = respostaAluno !== null ? respostaAluno.observacao : null;
		avaliadosFa.controls.push(
			new FormGroup({
				avaliadoId: new FormControl(this.avaliado.id),
				respostaId: new FormControl(respostaId),
				resposta: new FormControl(resposta, [Validators.required]),
				observacao: new FormControl(observacao),
			})
		);

		return avaliadosFa;
	}

	getRespostaAluno(atividadeId, subAtividadeId, alunoId) {
		let resposta;
		if (subAtividadeId !== null) {
			resposta = this.respostas.find(
				(item) =>
					item.atividade.id === atividadeId &&
					item.subAtividade.id === subAtividadeId
			);
		} else {
			resposta = this.respostas.find(
				(item) => item.atividade.id === atividadeId
			);
		}
		return resposta !== undefined ? resposta : null;
	}

	private loadDataAvaliacaoLivre(
		avaliacaoId: number,
		avaliacaoAlunoId: number
	): Observable<any> {
		const avaliacao$ = this.avaliacaoLivreService
			.buscarAvaliacaoLivre(avaliacaoId)
			.pipe(
				switchMap((avaliacao) => {
					this.avaliacao = avaliacao;
					return this.colaboradorService
						.obterColaborador(avaliacao.avaliadorId)
						.pipe(
							tap((colaborador) => {
								this.avaliador = colaborador;
							})
						);
				})
			);
		const avaliacaoAluno$ = this.avaliacaoLivreAlunoService
			.obterPorId(avaliacaoAlunoId)
			.pipe(
				switchMap((avaliacaoAluno) => {
					return this.clienteService.obterCliente(avaliacaoAluno.alunoId).pipe(
						tap((aluno) => {
							this.avaliado = aluno;
						})
					);
				})
			);
		const respostas$ = this.avaliacaoLivreAlunoService
			.obterRespostasAvaliacaoAluno(avaliacaoAlunoId)
			.pipe(
				tap((respostas) => {
					this.respostas = respostas;
					this.perguntas = respostas.map((resposta) => resposta.atividade);
				})
			);
		return zip(avaliacao$, avaliacaoAluno$, respostas$);
	}

	private setOptions() {
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				this.answerOptions = ["1", "2", "3", "4", "5"];
				break;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				this.answerOptions = ["Sim", "Não"];
				break;
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				this.answerOptions = [
					"Excelente",
					"Ótimo",
					"Muito bom",
					"Bom",
					"Regular",
					"Não executou",
				];
				break;
		}
	}

	private loadDataAvaliacaoProgresso(
		avaliacaoId: number,
		avaliacaoAlunoId: number
	): Observable<any> {
		const avaliacao$ = this.avaliacaoProgressoService
			.buscarAvaliacaoProgresso(avaliacaoId)
			.pipe(
				switchMap((avaliacao) => {
					this.avaliacao = avaliacao;
					this.nivelService
						.obterNivelPorId(avaliacao.nivel.id)
						.subscribe((response) => {
							this.perguntas = response.atividades;
						});
					return this.colaboradorService
						.obterColaborador(avaliacao.avaliadorId)
						.pipe(
							tap((colaborador) => {
								this.avaliador = colaborador;
							})
						);
				})
			);
		const avaliacaoAluno$ = this.avaliacaoProgressoAlunoService
			.obterPorId(avaliacaoAlunoId)
			.pipe(
				switchMap((avaliacaoAluno) => {
					this.observacaoGeralFc.setValue(avaliacaoAluno.observacaoGeral);
					return this.clienteService.obterCliente(avaliacaoAluno.alunoId).pipe(
						tap((aluno) => {
							this.avaliado = aluno;
						})
					);
				})
			);
		const respostas$ = this.avaliacaoProgressoAlunoService
			.obterRespostasAvaliacaoAluno(avaliacaoAlunoId)
			.pipe(
				tap((respostas) => {
					this.respostas = respostas.respostas;
					this.criterio = respostas.criterio;
					// this.perguntas = respostas.respostas.map(resposta => resposta.atividade);
					// this.perguntas = respostas.respostas;
				})
			);
		return zip(avaliacao$, avaliacaoAluno$, respostas$);
	}
}
