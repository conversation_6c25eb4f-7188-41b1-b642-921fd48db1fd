<ng-container *ngIf="!loading">
	<div class="header">
		<div class="nome">
			<div class="type-h2 cor-preto-pri">Avaliação</div>
			<div class="nome-ficha type-h4 cor-cinza04">
				{{ avaliacao?.ficha?.nome }}
			</div>
		</div>
		<div class="time">
			<i class="pct pct-calendar cor-azulim-pri"></i>
			<span class="type-h4 cor-cinza04">
				{{ avaliacao?.data | date : "shortDate" }}
			</span>
			<i class="pct pct-clock cor-azulim-pri"></i>
			<span class="type-h4 cor-cinza04">
				{{ avaliacao?.data | date : "HH:mm" }}
			</span>
		</div>

		<div class="upper">
			<div *ngIf="avaliacao?.nivel" class="block">
				<div class="block-label type-h6">Nível</div>
				<div class="block-aux">
					<div class="block-value type-h6">{{ avaliacao?.nivel.nome }}</div>
				</div>
			</div>
			<div class="block">
				<div class="block-label type-h6">Avaliador</div>
				<div class="block-aux">
					<div class="block-value type-h6">{{ avaliador?.nome }}</div>
				</div>
			</div>
			<div class="block">
				<div class="block-label type-h6" i18n="@@geral:avaliado">Avaliado</div>
				<div class="block-aux">
					<div class="block-value type-h6">{{ avaliado?.nome }}</div>
				</div>
			</div>
		</div>
	</div>

	<div class="perguntas">
		<div
			*ngFor="let block of perguntaBlocks; let index = index"
			class="pergunta-block">
			<pacto-avaliar-aluno-modal-resposta
				[answerOptions]="answerOptions"
				[atividade]="perguntas[index * 2]"
				[criterio]="criterio"
				[description]="perguntas[index * 2].descricao"
				[formGroup]="getRespostaFg(perguntas[index * 2].id)"
				[name]="perguntas[index * 2].nome"
				[subAtividades]="perguntas[index * 2].subAtividades"
				[uri]="
					perguntas[index * 2].imageUri
				"></pacto-avaliar-aluno-modal-resposta>
			<pacto-avaliar-aluno-modal-resposta
				*ngIf="perguntas[index * 2 + 1]"
				[answerOptions]="answerOptions"
				[atividade]="perguntas[index * 2 + 1]"
				[criterio]="criterio"
				[description]="perguntas[index * 2 + 1].descricao"
				[formGroup]="getRespostaFg(perguntas[index * 2 + 1].id)"
				[name]="perguntas[index * 2 + 1].nome"
				[subAtividades]="perguntas[index * 2 + 1].subAtividades"
				[uri]="
					perguntas[index * 2 + 1].imageUri
				"></pacto-avaliar-aluno-modal-resposta>
		</div>
	</div>

	<div class="print-footer">
		<img src="pacto-ui/images/logo-azul.svg" />
	</div>

	<div class="observacao-wrapper">
		<div class="header">
			<div class="nome">
				<div class="type-h2 cor-preto-pri">Avaliação</div>
				<div class="nome-ficha type-h4 cor-cinza04">
					{{ avaliacao?.ficha?.nome }}
				</div>
			</div>
			<div class="time">
				<i class="pct pct-calendar cor-azulim-pri"></i>
				<span class="type-h4 cor-cinza04">
					{{ avaliacao?.data | date : "shortDate" }}
				</span>
				<i class="pct pct-clock cor-azulim-pri"></i>
				<span class="type-h4 cor-cinza04">
					{{ avaliacao?.data | date : "HH:mm" }}
				</span>
			</div>
			<div class="upper">
				<div *ngIf="avaliacao?.nivel" class="block">
					<div class="block-label type-h6">Nível</div>
					<div class="block-aux">
						<div class="block-value type-h6">{{ avaliacao?.nivel.nome }}</div>
					</div>
				</div>
				<div class="block">
					<div class="block-label type-h6">Avaliador</div>
					<div class="block-aux">
						<div class="block-value type-h6">{{ avaliador?.nome }}</div>
					</div>
				</div>
				<div class="block">
					<div class="block-label type-h6" i18n="@@geral:avaliado">
						Avaliado
					</div>
					<div class="block-aux">
						<div class="block-value type-h6">{{ avaliado?.nome }}</div>
					</div>
				</div>
			</div>
		</div>
		<div
			class="observacao type-h4 cor-preto-pri"
			i18n="@@geral:observacao-geral">
			Observação Geral
		</div>
		<textarea
			[formControl]="observacaoGeralFc"
			class="observacao-geral"
			i18n-placeholder="@@geral:alguma-obervacao"
			placeholder="Alguma observação geral sobre a avaliação"
			rows="3"></textarea>
	</div>
</ng-container>
