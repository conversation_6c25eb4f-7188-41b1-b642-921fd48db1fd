import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-select-aluno-modal",
	templateUrl: "./select-aluno-modal.component.html",
	styleUrls: ["./select-aluno-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectAlunoModalComponent implements OnInit {
	alunos: any[] = [];
	alunosFg: FormGroup = new FormGroup({});
	selectAllFc = new FormControl(false);

	constructor(private cd: ChangeDetectorRef, private modal: NgbActiveModal) {}

	ngOnInit() {
		this.selectAllFc.valueChanges.subscribe((selected) => {
			for (const aluno in this.alunosFg.controls) {
				this.alunosFg.get(aluno).setValue(selected, { emitEvent: false });
			}
		});
	}

	loadData(alunos: any[]) {
		this.alunos = alunos;
		this.alunosFg = new FormGroup({});
		alunos.forEach((aluno) => {
			this.alunosFg.addControl(aluno.id, new FormControl(null));
		});
		this.cd.detectChanges();
	}

	confirmarHandler() {
		const dto = this.getDto();
		this.modal.close(dto);
	}

	private getDto() {
		const raw = this.alunosFg.getRawValue();
		const result = [];
		for (const id in raw) {
			if (raw[id]) {
				result.push(parseInt(id, 10));
			}
		}
		return result;
	}
}
