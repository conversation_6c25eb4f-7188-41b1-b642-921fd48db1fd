@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.data {
	display: flex;
	justify-content: flex-end;
	@extend .type-h6;
	color: $cinza03;

	i.pct {
		font-size: 20px;
		position: relative;
		padding-right: 5px;
		color: $azulimPri;
		top: -1px;
	}
}

.body {
	margin: 15px 30px;
}

.footer {
	display: flex;
	padding: 30px 0px;
	border-top: 1px solid $geloPri;
	justify-content: center;

	pacto-cat-button {
		width: 300px;
	}
}

::ng-deep pacto-select {
	.form-group {
		.control-label {
			color: #a6aab1;
			line-height: 2em;
			font-family: "Nunito Sans", sans-serif;
			font-size: 16px;
			font-weight: 400;
		}

		#comentario-ficha-select {
			height: 42px;
		}
	}
}
