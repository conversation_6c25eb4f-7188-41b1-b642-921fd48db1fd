<div class="body">
	<div class="data">
		<i class="pct pct-calendar"></i>
		{{ date | date : "longDate" }}
	</div>

	<ng-container *ngIf="listFichas">
		<pacto-select
			(change)="selecionarFichaId()"
			[control]="fgFicha"
			[id]="'comentario-ficha-select'"
			[nome]="'fichaId'"
			[opcoes]="listFichas"
			label="Selecione uma Ficha"
			mensagem="Selecione uma ficha."></pacto-select>
	</ng-container>

	<pacto-cat-form-input
		[control]="fg.get('nome')"
		[errorMsg]="'O comentário deve ser no mínimo 3 characteres.'"
		[label]="'Nome'"
		idSuffix="grd-input-nome-coment"></pacto-cat-form-input>

	<pacto-cat-form-textarea
		[control]="fg.get('descricao')"
		[label]="'Descrição'"
		idSuffix="grd-txtar-descricao-coment"></pacto-cat-form-textarea>
</div>

<div class="footer">
	<pacto-cat-button
		(click)="criarHandler()"
		[disabled]="!ready"
		[full]="true"
		[label]="'confirmar'"
		idSuffix="grd-btn-confirm-coment"></pacto-cat-button>
</div>
