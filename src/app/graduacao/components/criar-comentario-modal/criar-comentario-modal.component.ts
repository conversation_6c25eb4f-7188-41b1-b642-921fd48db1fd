import { Component, OnInit, ChangeDetectionStrategy } from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { ComentarioAlunoService } from "src/app/microservices/graduacao/comentario-aluno/comentario-aluno.service";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";

@Component({
	selector: "pacto-criar-comentario-modal",
	templateUrl: "./criar-comentario-modal.component.html",
	styleUrls: ["./criar-comentario-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CriarComentarioModalComponent implements OnInit {
	alunoId: number;
	fichaId: number;
	commentId: number;
	listFichas: Array<any>;

	fg = new FormGroup({
		nome: new FormControl(null, [Validators.required]),
		descricao: new FormControl(null),
		data: new FormControl(new Date().valueOf()),
	});

	fgFicha = new FormControl(null, [Validators.required]);

	get date() {
		return this.fg.get("data").value;
	}

	get ready() {
		if (this.listFichas == null) {
			return this.fg.valid || this.commentId;
		} else {
			return (this.fg.valid || this.commentId) && this.fichaId != null;
		}
	}

	constructor(
		private modal: NgbActiveModal,
		private notify: SnotifyService,
		private comentarioService: ComentarioAlunoService,
		private sessionService: SessionService
	) {}

	criarHandler() {
		if (this.commentId) {
			this.modal.close();
		} else if (this.ready) {
			const dto = this.fg.getRawValue();
			this.comentarioService
				.criarComentario(this.fichaId, this.alunoId, dto)
				.subscribe((result) => {
					if (result) {
						this.notify.success("Comentário adicionado com sucesso");
						this.modal.close(result);
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.REGISTROU_COMENTARIO_ALUNO_GRD
						);
					} else {
						this.notify.error("Erro inesperado");
						this.modal.close();
					}
				});
		}
	}

	ngOnInit() {
		if (this.commentId) {
			setTimeout(() => {
				this.fg.disable();
			});
		}
	}

	selecionarFichaId() {
		this.fichaId = this.fgFicha.value;
	}
}
