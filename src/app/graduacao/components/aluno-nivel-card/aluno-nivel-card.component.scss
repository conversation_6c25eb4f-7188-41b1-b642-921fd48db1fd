@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	margin: 15px;
	width: 100%;
}

.title {
	@extend .type-h5;
	color: $pretoPri;
}

.block {
	margin-bottom: 20px;

	.nome {
		@extend .type-h6;
		color: $cinza03;
	}

	.value {
		@extend .type-p-small-rounded;
		color: $pretoPri;
	}

	&.proximo-nivel {
		margin: 20px 20px;
	}

	&.proximo-nivel .value {
		display: flex;
		align-items: center;

		.nivel-nome {
			line-height: 35px;
			margin-left: 15px;
		}
	}
}

.last {
	margin-bottom: 0 !important;
}

.box-left {
	width: 80%;
}

.grid {
	display: grid;
	grid-template-columns: 1fr;
	margin: 30px;
	gap: 40px;
}

.loading {
	text-align: center;
	line-height: 300px;
}

.first.column {
	display: flex;
	flex-wrap: wrap;

	.title {
		flex-basis: 100%;
		display: block;
		float: right;
		margin-right: 10px;
	}

	.left {
		flex-grow: 1;
		margin-top: 30px;
	}

	.right {
		flex-basis: 120px;
		flex-shrink: 1;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.box {
		background: #eff2f7;
		padding: 16px 20px;
		display: inline-flex;
		width: 100%;
	}

	.avatar {
		margin-inline: auto;
		alignment: left;
		padding: 0;
	}
}

.second.column {
	display: flex;
	flex-wrap: wrap;

	.title {
		flex-grow: 1;
	}
}

.comment-list {
	margin-top: 30px;
	flex-basis: 100%;
	height: 240px;

	.comment {
		padding: 7px 15px;
		cursor: pointer;

		&:nth-child(2n + 1) {
			background-color: $cinzaPri;
		}

		.pct {
			color: $azulimPri;
			font-size: 20px;
			position: relative;
			top: 3px;
		}
	}

	.nome {
		@extend .type-h6;
		color: $pretoPri;
	}

	.date {
		@extend .type-caption;
		padding-left: 5px;
		color: $cinza03;
	}
}

.avaliacoes-list {
	margin-top: 30px;
	flex-basis: 100%;
	height: 240px;

	.avaliacao {
		padding: 7px 15px;
		cursor: pointer;

		&:nth-child(2n + 1) {
			background-color: $cinzaPri;
		}

		.pct {
			color: $azulimPri;
			font-size: 20px;
			position: relative;
			top: 3px;
		}
	}

	.nome {
		@extend .type-h6;
		color: $pretoPri;
	}

	.date {
		@extend .type-caption;
		padding-left: 5px;
		color: $cinza03;
	}
}

@media (min-width: $bootstrap-breakpoint-lg) {
	.grid {
		grid-template-columns: 1fr 1fr;
	}
}

@media (min-width: $bootstrap-breakpoint-xl) {
	.grid {
		grid-template-columns: 1fr 1fr 1fr;
	}
}
