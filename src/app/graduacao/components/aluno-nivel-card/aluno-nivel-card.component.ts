import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ViewChild,
	ChangeDetectorRef,
	SimpleChanges,
	OnChanges,
} from "@angular/core";

import { Observable, zip } from "rxjs";
import { tap, delay } from "rxjs/operators";

import { CatTabsComponent } from "ui-kit";
import {
	Ficha,
	FichaGraduacaoAluno,
} from "src/app/microservices/graduacao/ficha/ficha.model";
import { GraduacaoAlunoComentario } from "src/app/microservices/graduacao/comentario-aluno/comentario-aluno.model";
import { GraduacaoAvaliacaoProgresso } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { ComentarioAlunoService } from "src/app/microservices/graduacao/comentario-aluno/comentario-aluno.service";
import { AvaliacaoProgressoService } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.service";
import { NivelSimple } from "src/app/microservices/graduacao/nivel/nivel.model";
import { ModalService } from "@base-core/modal/modal.service";
import { CriarComentarioModalComponent } from "src/app/graduacao/components/criar-comentario-modal/criar-comentario-modal.component";
import { ActivatedRoute } from "@angular/router";
import { NivelService } from "../../../microservices/graduacao/nivel/nivel.service";

declare var moment;

@Component({
	selector: "pacto-aluno-nivel-card",
	templateUrl: "./aluno-nivel-card.component.html",
	styleUrls: ["./aluno-nivel-card.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AlunoNivelCardComponent implements OnInit, OnChanges {
	@Input() fichasDoAluno: FichaGraduacaoAluno[];
	@Input() alunoId: number;
	@Input() matriculaZW: number;
	@ViewChild("tabs", { static: false }) tabs: CatTabsComponent;

	ficha: Ficha;
	alunoIdMat: number;
	origem;
	totalAula;
	aula: string;
	nivelAtual: NivelSimple;
	proximoNivel: NivelSimple;
	comentarios: GraduacaoAlunoComentario[];
	avaliacoes: GraduacaoAvaliacaoProgresso[];
	fichaDoAluno: FichaGraduacaoAluno;
	loading = false;

	constructor(
		private cd: ChangeDetectorRef,
		private modal: ModalService,
		private nivelService: NivelService,
		private fichaService: FichaService,
		private avaliacaoService: AvaliacaoProgressoService,
		private comentarioService: ComentarioAlunoService,
		private route: ActivatedRoute
	) {}

	ngOnInit() {}

	ngOnChanges(changes: SimpleChanges) {
		const fichaValue = changes["fichasDoAluno"];
		if (fichaValue.currentValue !== undefined) {
			if (fichaValue.currentValue.length > 0) {
				this.activateTab(0);
			}
		}
	}

	get duration() {
		const duration = this.fichaDoAluno.dataIngresso - new Date().valueOf();
		const durationObject = moment.duration(duration);
		return durationObject.locale("pt-br").humanize(true);
	}

	get aulasRestantes() {
		const aulasRestantes =
			this.nivelAtual.quantidadeMinimaAulas - this.totalAula;
		return aulasRestantes >= 0 ? aulasRestantes : 0;
	}

	criarComentarioGraduacaoHandler() {
		const ref = this.modal.open("Comentário", CriarComentarioModalComponent);
		const modal: CriarComentarioModalComponent = ref.componentInstance;
		modal.alunoId = this.alunoId;
		modal.fichaId = this.ficha.id;
		modal.listFichas = null;
		ref.result.then(
			(comment) => {
				this.comentarios.unshift(comment);
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	visualizarComentarioHandler(comment: GraduacaoAlunoComentario) {
		const ref = this.modal.open("Comentário", CriarComentarioModalComponent);
		const modal: CriarComentarioModalComponent = ref.componentInstance;
		modal.alunoId = this.alunoId;
		modal.fichaId = this.ficha.id;
		modal.commentId = comment.id;
		modal.fg.setValue({
			nome: comment.nome,
			descricao: comment.descricao,
			data: comment.data,
		});
		ref.result.then(
			() => {},
			() => {}
		);
	}

	activateTab(index) {
		this.loading = true;
		this.cd.detectChanges();
		this.fichaDoAluno = this.fichasDoAluno[index];
		const fichaId = this.fichaDoAluno.ficha.id;
		this.fetchData(fichaId).subscribe(() => {
			this.loading = false;
			this.cd.detectChanges();
		});
		this.nivelService
			.obtertotalAulaNivel(
				this.alunoId,
				this.matriculaZW,
				this.fichaDoAluno.nivelId
			)
			.subscribe((data) => {
				this.totalAula = data;
				this.totalAula > 1 ? (this.aula = " aulas") : (this.aula = " aula");
			});
	}

	comentarioClickHandler(comentario) {
		console.log(comentario);
	}

	fetchData(fichaId: number): Observable<any> {
		return zip(
			this.fichaService.obterFichaPorId(fichaId),
			this.comentarioService.obterComentariosAluno(fichaId, this.alunoId),
			this.avaliacaoService.listarAvaliacaoProgresso({
				alunoId: this.alunoId,
				fichaId,
			})
		).pipe(
			delay(1200),
			tap((result) => {
				this.ficha = result[0];
				this.comentarios = result[1];
				this.avaliacoes = result[2];
				this.nivelAtual = this.ficha.niveis.find((nivel) => {
					return nivel.id === this.fichaDoAluno.nivelId;
				});
				this.proximoNivel = this.ficha.niveis.find((nivel) => {
					return nivel.index === this.nivelAtual.index + 1;
				});
			})
		);
	}
}
