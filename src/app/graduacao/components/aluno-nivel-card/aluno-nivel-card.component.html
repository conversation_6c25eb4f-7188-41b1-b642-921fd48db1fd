<pacto-cat-tabs
	#tabs
	(activateTab)="activateTab($event)"
	*ngIf="fichasDoAluno && fichasDoAluno.length">
	<ng-container *ngFor="let item of fichasDoAluno; let index = index">
		<ng-template [pactoTab]="index" label="{{ item.ficha.nome }}">
			<div *ngIf="loading" class="loading">carregando...</div>

			<div *ngIf="!loading" class="grid">
				<!-- NIVEL ATUAL -->
				<div class="first column">
					<div class="title">{{ item.ficha.nome }}</div>
					<div class="left">
						<div class="box">
							<div class="box-left">
								<div class="block">
									<div class="nome">Nível Atual</div>
									<div class="value">{{ nivelAtual?.nome }}</div>
								</div>
								<div class="block">
									<div class="nome">Data de inclusão no nível</div>
									<div *ngIf="duration" class="value">
										{{ fichaDoAluno.dataIngresso | date : "dd/MM/yyyy" }} ({{
											duration
										}})
									</div>
								</div>
								<div *ngIf="totalAula > 0" class="block">
									<div class="nome">Aulas realizadas nesse nível</div>
									<div class="value">{{ totalAula + aula }}</div>
								</div>
								<div
									*ngIf="nivelAtual.quantidadeMinimaAulas > 0"
									class="block last">
									<div class="nome">Aulas restantes no nível</div>
									<div class="value">{{ aulasRestantes }}</div>
								</div>
							</div>
							<div class="avatar">
								<pacto-cat-person-avatar
									[diameter]="90"
									[uri]="nivelAtual?.fotoUri"></pacto-cat-person-avatar>
							</div>
						</div>
						<div class="block proximo-nivel">
							<div class="nome">Próximo nível</div>
							<div class="value">
								<pacto-cat-person-avatar
									*ngIf="proximoNivel"
									[diameter]="35"
									[uri]="proximoNivel.fotoUri"></pacto-cat-person-avatar>
								<div class="nivel-nome">
									{{ proximoNivel ? proximoNivel.nome : "-" }}
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- COMENTÁRIOS DO ALUNO -->
				<div class="second column">
					<div class="title">Comentários</div>
					<pacto-cat-button
						(click)="criarComentarioGraduacaoHandler()"
						[icon]="'message-circle'"
						[label]="'novo comentário'"></pacto-cat-button>
					<div [maxHeight]="'240px'" class="comment-list" pactoCatSmoothScroll>
						<div
							(click)="visualizarComentarioHandler(comment)"
							*ngFor="let comment of comentarios"
							class="comment"
							title="{{ 'Descrição: ' + comment.descricao }}">
							<div class="nome">{{ comment.nome }}</div>
							<div>
								<i class="pct pct-calendar"></i>
								<span class="date">{{ comment.data | date : "longDate" }}</span>
							</div>
						</div>
					</div>
				</div>

				<!-- AVALIAÇÕES DO ALUNO -->
				<div class="third column">
					<div class="title">Avaliações do aluno</div>
					<div
						[maxHeight]="'240px'"
						class="avaliacoes-list"
						pactoCatSmoothScroll>
						<div
							*ngFor="let avaliacao of avaliacoes"
							[queryParams]="{ alunoId: alunoId }"
							[routerLink]="['/graduacao/avaliacoes-progresso', avaliacao.id]"
							class="avaliacao">
							<div class="nome">
								Avaliação de Progresso - {{ avaliacao.nivel.nome }}
							</div>
							<div>
								<i class="pct pct-calendar"></i>
								<span class="date">
									{{ avaliacao.data | date : "longDate" }}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</ng-template>
	</ng-container>
</pacto-cat-tabs>
