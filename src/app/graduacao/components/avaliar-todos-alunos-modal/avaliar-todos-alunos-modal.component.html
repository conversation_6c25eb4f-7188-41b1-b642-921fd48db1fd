<div class="header">
	<div class="header-nome-avaliacao">
		<i (click)="closeHandler()" class="close-icon pct pct-x"></i>
		<div class="nome">
			<div class="type-h2 cor-preto-pri">Avaliação</div>
			<div class="nome-ficha type-h4 cor-cinza04">
				{{ avaliacao?.ficha?.nome }}
			</div>
		</div>
	</div>
	<div class="time">
		<i class="pct pct-calendar cor-azulim-pri"></i>
		<span class="type-h4 cor-cinza04">
			{{ avaliacao?.data | date : "shortDate" }}
		</span>
	</div>
</div>

<div class="body">
	<div class="upper">
		<div *ngIf="nivel" class="block">
			<div class="block-label type-h6">Nível</div>
			<div class="block-aux">
				<pacto-cat-person-avatar
					[diameter]="32"
					[uri]="nivel?.fotoUri"></pacto-cat-person-avatar>
				<div class="block-value type-h6">{{ nivel?.nome }}</div>
			</div>
		</div>
		<div class="block">
			<div class="block-label type-h6">Avaliador</div>
			<div class="block-aux">
				<pacto-cat-person-avatar
					[diameter]="32"
					[uri]="avaliador?.imageUri"></pacto-cat-person-avatar>
				<div class="block-value type-h6">{{ avaliador?.nome }}</div>
			</div>
		</div>
	</div>

	<div class="perguntas">
		<pacto-avaliar-todos-alunos-modal-resposta
			*ngFor="let atividade of atividades"
			[answerOptions]="answerOptions"
			[atividade]="atividade"
			[avaliados]="avaliados"
			[criterio]="criterio"
			[description]="atividade.descricao"
			[formGroup]="getRespostaFg(atividade.id)"
			[name]="atividade.nome"
			[subAtividades]="atividade.subAtividades"
			[uri]="atividade.imageUri"></pacto-avaliar-todos-alunos-modal-resposta>
	</div>

	<div class="observacao type-h4 cor-preto-pri" i18n="@@geral:observacao-geral">
		Observação Geral
	</div>
	<textarea
		[formControl]="observacaoGeralFc"
		class="observacao-geral"
		i18n-placeholder="@@geral:alguma-obervacao"
		placeholder="Alguma observação geral sobre a avaliação"
		rows="3"></textarea>

	<div class="action-footer">
		<pacto-cat-button
			(click)="salvarHandler()"
			*ngIf="canEdit"
			[disabled]="!avaliacaoValida"
			[full]="true"
			[label]="'salvar'"></pacto-cat-button>
		<pacto-cat-button
			(click)="closeHandler()"
			[full]="true"
			[label]="'cancelar'"
			[type]="'OUTLINE'"></pacto-cat-button>
	</div>

	<iframe
		#printFrame
		*ngIf="injectPrintFrame"
		[attr.src]="printFrameUrl"
		id="print-frame"></iframe>
</div>
