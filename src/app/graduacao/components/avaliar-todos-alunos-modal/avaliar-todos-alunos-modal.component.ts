import {
	ChangeDetectorRef,
	Component,
	OnInit,
	Optional,
	ViewChild,
} from "@angular/core";
import { AvaliarAlunoModalAlertComponent } from "../avaliar-aluno-modal-alert/avaliar-aluno-modal-alert.component";
import { Colaborador } from "../../../microservices/personagem/colaborador/colaborador.model";
import { Cliente } from "../../../microservices/personagem/cliente/cliente.model";
import {
	AvaliacaoAlunoResposta,
	GraduacaoAvaliacao,
	RespostaAvaliacaoGrupo,
} from "../../../microservices/graduacao/graduacao.model";
import {
	AtividadeGraduacao,
	SubAtividades,
} from "../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { AvaliacaoGraduacaoCriterio } from "../../../microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { DomSanitizer } from "@angular/platform-browser";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-avaliar-todos-alunos-modal",
	templateUrl: "./avaliar-todos-alunos-modal.component.html",
	styleUrls: ["./avaliar-todos-alunos-modal.component.scss"],
})
export class AvaliarTodosAlunosModalComponent implements OnInit {
	@ViewChild("printFrame", { static: false }) printFrame;
	avaliador: Colaborador;
	avaliados: Cliente[] = [];
	avaliacao: GraduacaoAvaliacao;
	atividades: AtividadeGraduacao[] = [];
	respostas: AvaliacaoAlunoResposta[] = [];
	respostaAvaliacaoGrupo: RespostaAvaliacaoGrupo[] = [];
	criterio: AvaliacaoGraduacaoCriterio;
	injectPrintFrame = false;
	printLoading = false;
	avaliacaoAlunoId: number;
	printFrameUrl;
	canEdit;

	avaliavaoGrupoFg: FormGroup = new FormGroup({});
	observacaoGeralFc = new FormControl(null);
	answerOptions = [];

	alunos: any[] = [];

	constructor(
		private domSanitizer: DomSanitizer,
		private cd: ChangeDetectorRef,
		private modalService: NgbModal,
		@Optional() private modal: NgbActiveModal
	) {}

	ngOnInit() {}

	loadData(
		avaliador: any,
		avaliados: any[],
		avaliacao: GraduacaoAvaliacao,
		atividades: AtividadeGraduacao[],
		respostas: AvaliacaoAlunoResposta[],
		criterio: AvaliacaoGraduacaoCriterio,
		canEdit: boolean,
		avaliacaoAlunoId: number,
		observacaoGeral: string,
		respostaAvaliacaoGrupo: RespostaAvaliacaoGrupo[]
	) {
		this.avaliador = avaliador;
		this.avaliados = avaliados;
		this.avaliacao = avaliacao;
		this.atividades = atividades;
		this.respostas = respostas;
		this.respostaAvaliacaoGrupo = respostaAvaliacaoGrupo;
		this.criterio = criterio;
		this.canEdit = canEdit;
		this.buildavAliavaoGrupoFormGroup(atividades);
		this.avaliacaoAlunoId = avaliacaoAlunoId;
		this.observacaoGeralFc.setValue(observacaoGeral);
		this.filloutForm();

		this.setOptions();
		this.cd.detectChanges();
	}

	filloutForm() {
		this.respostaAvaliacaoGrupo.forEach((resposta) => {
			if (!this.canEdit) {
				this.avaliavaoGrupoFg.get(`${resposta.atividadeId}`).disable();
				this.avaliavaoGrupoFg.get(`${resposta.atividadeId}`).disable();
			}
		});
		if (!this.canEdit) {
			this.observacaoGeralFc.disable();
		}
	}

	private buildavAliavaoGrupoFormGroup(atividades: AtividadeGraduacao[]) {
		this.avaliavaoGrupoFg = new FormGroup({});
		atividades.forEach((atividade) => {
			const possuiSubAtividade = atividade.subAtividades.length > 0;
			const subAtividadesFa: FormArray = this.buildSubAtividadesForm(
				atividade.id,
				atividade.subAtividades
			);
			let avaliadosFa = null;
			if (!possuiSubAtividade) {
				avaliadosFa = this.buildAvaliadosForm(atividade.id, null);
			}
			this.avaliavaoGrupoFg.addControl(
				`${atividade.id}`,
				new FormGroup({
					atividadeId: new FormControl(atividade.id),
					possuiSubAtividade: new FormControl(possuiSubAtividade),
					subAtividades: possuiSubAtividade
						? subAtividadesFa
						: new FormControl(null),
					avaliados: possuiSubAtividade ? new FormControl(null) : avaliadosFa,
				})
			);
		});
	}

	buildSubAtividadesForm(atividadeId, subAtividades: SubAtividades[]) {
		const subAtividadesFa = new FormArray([]);
		subAtividades.forEach((subAtividade) => {
			const avaliadosFa: FormArray = this.buildAvaliadosForm(
				atividadeId,
				subAtividade.id
			);
			subAtividadesFa.controls.push(
				new FormGroup({
					subAtividadeId: new FormControl(subAtividade.id),
					avaliados: avaliadosFa,
				})
			);
		});
		return subAtividadesFa;
	}

	buildAvaliadosForm(atividadeId, subAtividadeId) {
		const avaliadosFa = new FormArray([]);
		this.avaliados.forEach((avaliado) => {
			const respostaAluno = this.getRespostaAluno(
				atividadeId,
				subAtividadeId,
				avaliado.id
			);
			const respostaId =
				respostaAluno !== null ? respostaAluno.respostaAvaliacaoId : null;
			let resposta = respostaAluno !== null ? respostaAluno.resposta : null;
			resposta = resposta == null ? null : this.tratarRespostaSomar(resposta);
			const observacao =
				respostaAluno !== null ? respostaAluno.observacao : null;
			avaliadosFa.controls.push(
				new FormGroup({
					avaliadoId: new FormControl(avaliado.id),
					respostaId: new FormControl(respostaId),
					resposta: new FormControl(resposta, [Validators.required]),
					observacao: new FormControl(observacao),
				})
			);
		});
		return avaliadosFa;
	}

	getRespostaAluno(atividadeId, subAtividadeId, alunoId) {
		let resposta;
		if (subAtividadeId !== null) {
			resposta = this.respostaAvaliacaoGrupo.find(
				(item) =>
					item.atividadeId === atividadeId &&
					item.subAtividadeId === subAtividadeId &&
					item.alunoId === alunoId
			);
		} else {
			resposta = this.respostaAvaliacaoGrupo.find(
				(item) => item.atividadeId === atividadeId && item.alunoId === alunoId
			);
		}
		return resposta !== undefined ? resposta : null;
	}

	private setOptions() {
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				this.answerOptions = ["1", "2", "3", "4", "5"];
				break;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				this.answerOptions = ["Sim", "Não"];
				break;
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				this.answerOptions = [
					"Excelente",
					"Ótimo",
					"Muito bom",
					"Bom",
					"Regular",
					"Não executou",
				];
				break;
		}
	}

	closeHandler() {
		if (!this.creating) {
			this.modal.dismiss();
		} else {
			const ref = this.modalService.open(AvaliarAlunoModalAlertComponent, {
				centered: true,
				windowClass: "modal-alert-avaliacao",
			});
			ref.result.then(
				(result) => {
					if (result) {
						this.modal.dismiss();
					}
				},
				() => {}
			);
		}
	}

	get creating() {
		return !this.respostas || !this.respostas.length;
	}

	get nivel() {
		const avaliacao: any = this.avaliacao;
		if (avaliacao.nivel) {
			return avaliacao.nivel;
		} else {
			return undefined;
		}
	}

	getRespostaFg(perguntaId: number) {
		return this.avaliavaoGrupoFg.get(`${perguntaId}`);
	}

	salvarHandler() {
		if (this.avaliacaoValida) {
			const dto = this.getDto();
			this.modal.close(dto);
		}
	}

	private getDto() {
		const respostas = this.avaliavaoGrupoFg.getRawValue();
		const avaliacaoGrupo: any = {
			avaliacaoProgressoId: this.avaliacao.id,
			observacaoGeral: this.observacaoGeralFc.value,
			timestamp: new Date().valueOf(),
			atividades: [],
		};
		for (const atv in respostas) {
			const atividadeFg = respostas[atv];
			const listSubAtividades: any = [];
			if (atividadeFg.possuiSubAtividade) {
				for (const subAtv of atividadeFg.subAtividades) {
					const listAvaliados: any = [];
					for (const aluno of subAtv.avaliados) {
						listAvaliados.push({
							respostaAvaliacaoId: aluno.respostaId,
							alunoId: aluno.avaliadoId,
							resposta: this.tratarRespostaSubtrair(aluno.resposta),
							observacao: aluno.observacao,
						});
					}
					listSubAtividades.push({
						subAtividadeId: subAtv.subAtividadeId,
						avaliados: listAvaliados,
					});
				}
				avaliacaoGrupo.atividades.push({
					atividadeId: atividadeFg.atividadeId,
					possuiSubAtividade: true,
					subAtividades: listSubAtividades,
				});
			} else {
				const listAvaliados: any = [];
				for (const aluno of atividadeFg.avaliados) {
					listAvaliados.push({
						respostaAvaliacaoId: aluno.respostaId,
						alunoId: aluno.avaliadoId,
						resposta: this.tratarRespostaSubtrair(aluno.resposta),
						observacao: aluno.observacao,
					});
				}
				avaliacaoGrupo.atividades.push({
					atividadeId: atividadeFg.atividadeId,
					possuiSubAtividade: false,
					avaliados: listAvaliados,
				});
			}
		}
		return avaliacaoGrupo;
	}

	get avaliacaoValida() {
		let valid = true;
		const avaliacao = this.avaliavaoGrupoFg.getRawValue();
		for (const atv in avaliacao) {
			const atividadeFg = avaliacao[atv];
			if (atividadeFg.possuiSubAtividade) {
				for (const subAtv of atividadeFg.subAtividades) {
					for (const aluno of subAtv.avaliados) {
						valid = valid && aluno.resposta !== null;
					}
				}
			} else {
				for (const aluno of atividadeFg.avaliados) {
					valid = valid && aluno.resposta !== null;
				}
			}
		}
		return valid;
	}

	tratarRespostaSubtrair(resposta) {
		// tratamento para problema do index 0 das respostas que fazia com que o botão de salvar não fosse habilitado
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				return resposta - 1;
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				return resposta - 1;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				return resposta;
		}
	}

	tratarRespostaSomar(resposta) {
		// tratamento para problema do index 0 das respostas que fazia com que o botão de salvar não fosse habilitado
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				return resposta + 1;
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				return resposta + 1;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				return resposta;
		}
	}
}
