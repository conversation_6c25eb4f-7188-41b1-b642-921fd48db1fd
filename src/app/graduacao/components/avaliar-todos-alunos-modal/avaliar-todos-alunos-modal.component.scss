@import "src/assets/scss/pacto/plataforma-import.scss";

#print-frame {
	width: 0px;
	height: 0px;
	border: 0px;
}

.header {
	padding: 100px 150px 5px;
	align-items: flex-end;
	position: relative;
	display: flex;
	justify-content: space-between;

	.header-nome-avaliacao {
		align-items: flex-end;
		position: relative;
		display: flex;
	}

	.nome {
		flex-grow: 1;
		position: relative;
		left: -30px;
	}

	.close-icon {
		position: relative;
		left: -50px;
		top: -46px;
		cursor: pointer;
		color: $pretoPri;
		font-size: 28px;
	}

	.time {
		display: flex;
		align-items: center;

		.pct {
			font-size: 24px;
		}

		span {
			padding: 0px 15px;
		}
	}
}

.body {
	border-top: 1px solid $cinza02;
	padding: 0px 150px 100px;
}

.upper {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	margin: 30px 0px;
	gap: 30px;
}

.block-aux {
	display: flex;
	align-items: center;
}

.block-label {
	color: $cinza04;
	padding-bottom: 3px;
}

.block-value {
	color: $pretoPri;
	padding-left: 15px;
}

.block {
	flex-grow: 1;
}

.perguntas {
	gap: 45px;
	display: grid;
	margin: 70px 0px 30px;
	grid-template-columns: 1fr;
}

.observacao {
	margin: 45px 0px 7px;
}

textarea.observacao-geral {
	border-radius: 3px;
	border: 1px solid $gelo03;
	color: $gelo05;
	padding: 10px;
	width: 100%;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

.action-footer {
	display: flex;
	flex-direction: row-reverse;
	margin-top: 30px;

	pacto-cat-button {
		margin-left: 30px;
		width: 150px;
	}
}

@media (max-width: 1400px) {
	.perguntas,
	.upper {
		grid-template-columns: 1fr;
	}
}

@media (max-width: $bootstrap-breakpoint-lg) {
	.perguntas,
	.upper {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.header {
		padding: 20px 20px !important;
		display: block;

		.pct-x {
			top: -39px;
			left: 88%;
		}

		.nome {
			.type-h2 {
				font-size: 28px;
			}

			.nome-ficha {
				font-size: 20px;
			}
		}

		.time {
			justify-content: end;

			.pct-calendar {
				font-size: 20px;
			}

			.type-h4 {
				font-size: 20px;
			}
		}
	}

	.body {
		padding: 0 24px 100px;

		.upper {
			border-bottom: $cinza02 solid 1px;
			padding-bottom: 20px;
		}

		.perguntas {
			> * {
				border-bottom: $cinza02 solid 1px;
				padding-bottom: 20px;
			}

			::ng-deep pacto-avaliar-aluno-modal-resposta {
				.upper {
					height: auto;

					.info {
						.type-h4 {
							font-size: 20px;
						}

						.description {
							font-size: 12px;
							margin-top: 8px;
						}
					}
				}
			}
		}

		.action-footer {
			justify-content: space-between;

			pacto-cat-button {
				margin: 0;
			}
		}
	}
}
