import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
} from "@angular/core";
import { FormArray, FormGroup } from "@angular/forms";
import {
	AtividadeGraduacao,
	SubAtividades,
} from "../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { AvaliacaoGraduacaoCriterio } from "../../../microservices/graduacao/avaliacao-progresso/avaliacao.model";

@Component({
	selector: "pacto-avaliar-aluno-modal-resposta",
	templateUrl: "./avaliar-aluno-modal-resposta.component.html",
	styleUrls: [
		"./avaliar-aluno-modal-resposta.component.scss",
		"./avaliar-aluno-modal-resposta.component.print.scss",
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliarAlunoModalRespostaComponent implements OnInit {
	@Input() uri;
	@Input() name;
	@Input() formGroup: FormGroup;
	@Input() description;
	@Input() answerOptions: string[] = ["1", "2", "3", "4", "5"];
	@Input() observacao: string;

	@Input() atividade: AtividadeGraduacao;
	@Input() subAtividades: SubAtividades[] = [];
	@Input() criterio: AvaliacaoGraduacaoCriterio;
	@Input() possuiSubAtividade: boolean;
	criterioSimNao = false;

	constructor() {}

	ngOnInit() {
		if (!this.uri) {
			this.uri = "pacto-ui/images/default-user-icon.png";
		}
		this.validarTipoCriterio();
	}

	private validarTipoCriterio() {
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				this.criterioSimNao = true;
				break;
			default:
				this.criterioSimNao = false;
				this.montarAnswerOptions();
				break;
		}
	}

	montarAnswerOptions() {
		const retorno: any[] = [];
		for (const answer of this.answerOptions) {
			// tratamento para problema do index 0 das respostas que fazia com que o botão de salvar não fosse habilitado
			retorno.push({
				id: this.answerOptions.findIndex((item) => item === answer) + 1,
				label: answer,
			});
		}
		this.answerOptions = retorno;
	}

	disabled(avaliado) {
		return avaliado.get("resposta").disabled;
	}

	verificarResposta(index, avaliado) {
		let classeSelecionado = "nao-selecionado";
		const resposta = this.answer(avaliado);
		if (index === resposta) {
			classeSelecionado = resposta === 0 ? "selected-sim" : "selected-nao";
		}
		return classeSelecionado;
	}

	answer(avaliado) {
		return avaliado.get("resposta").value;
	}

	selectOptionHandler(index, avaliado) {
		if (!this.disabled(avaliado)) {
			avaliado.get("resposta").setValue(index);
		}
	}

	get subAtividadesControl() {
		return (this.formGroup.get("subAtividades") as FormArray).controls;
	}

	get avaliadosControl() {
		return (this.formGroup.get("avaliados") as FormArray).controls;
	}

	getSubAtividadePorId(id) {
		return this.subAtividades.find((subAtividade) => subAtividade.id === id);
	}

	avaliadosSubAtividadeControl(subAtividade) {
		return (subAtividade.get("avaliados") as FormArray).controls;
	}
}
