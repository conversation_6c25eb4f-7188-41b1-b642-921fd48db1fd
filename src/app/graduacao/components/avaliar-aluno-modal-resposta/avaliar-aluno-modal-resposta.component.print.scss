@import "src/assets/scss/pacto/plataforma-import.scss";

@media print {
	:host {
		display: block;
	}

	.upper {
		display: grid;
		grid-template-columns: 94px 1fr;
		//height: 150px;
		gap: 15px;
		margin-bottom: 35px;
	}

	input {
		width: 100%;
		border-radius: 3px;
		border: 1px solid $gelo03;
		padding: 0px 30px 0px 10px;
		line-height: 40px;
		color: $gelo05;

		outline: 0px !important;

		&:focus {
			box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
		}
	}

	.info {
		overflow: hidden;
	}

	.name {
		color: $pretoPri;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}

	.description {
		color: $pretoPri;
	}

	.nota-label {
		color: $pretoPri;
		line-height: 2em;
	}

	.options {
		display: flex;
		//border: 1px solid $cinza02;
		margin-bottom: 15px;
		align-items: center;
		height: 48px;
		padding: 3px;

		.option {
			flex-shrink: 0;
			flex-grow: 0;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 5px;
			border-radius: 4px;

			.option-lbl {
				line-height: 1em;
				color: $pretoPri;
				text-align: center;
				font-size: 13px;
			}

			&:hover {
				background-color: $cinzaClaroPri;
			}

			&.selected-sim {
				background-color: $chuchuzinhoPri;

				.option-lbl {
					color: $branco;
				}
			}

			&.selected-nao {
				background-color: $hellboyPri;

				.option-lbl {
					color: $branco;
				}
			}

			&.nao-selecionado {
				border: 1px solid $cinzaPri;

				.option-lbl {
					color: $cinzaPri;
				}
			}
		}
	}

	.input-label {
		color: $pretoPri;
		line-height: 2em;
	}
}

.atividade-avatar {
	width: 67px;
	height: 67px;
	border-radius: 47px;
	border: 2px solid $cinza01;
}

.div-atividade-geral {
	margin-bottom: 70px;

	.div-sub-atividades {
		display: grid;
		grid-template-columns: 1fr;
		gap: 45px;

		.title-sub-atividade {
			font-weight: 600;
			font-size: 16px;
			color: $preto02;
		}

		.row-aluno {
			margin-top: 12px;
			margin-bottom: 12px;

			.column-row-aluno {
				display: flex;
				align-items: center;

				.celula-nome-aluno {
					margin-left: 13px;
					font-size: 14px;
					color: $preto06;
					font-weight: 400;
				}
			}
		}
	}
}
