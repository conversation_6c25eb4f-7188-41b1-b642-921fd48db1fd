import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { SnotifyService } from "ng-snotify";
import { TranslateService } from "@ngx-translate/core";
import { AvaliacaoProgressoAlunoService } from "../../../microservices/graduacao/avaliacao-progresso-aluno/avaliacao-progresso-aluno.service";
import { SessionService } from "@base-core/client/session.service";
import { ShareService } from "ui-kit";
import { TreinoApiAlunosService } from "treino-api";

@Component({
	selector: "pacto-share-pdf-button",
	templateUrl: "./avaliacao-progresso-share-button.component.html",
	styleUrls: ["./avaliacao-progresso-share-button.component.scss"],
})
export class AvaliacaoProgressoShareButtonComponent implements OnInit {
	@ViewChild("inputFone", { static: false }) inputFone;
	@ViewChild("inputEmail", { static: false }) inputEmail;
	public link: string;
	public tipo = "PDF";
	public loading = false;

	@Input() matricula: number;
	@Input() avaliadoId: number;
	@Input() fichaId: number;
	@Input() avaliacaoAlunoId: number;

	constructor(
		private service: ShareService,
		private alunosService: TreinoApiAlunosService,
		private notificationService: SnotifyService,
		private translateService: TranslateService,
		private avaliacaoProgressoAlunoService: AvaliacaoProgressoAlunoService,
		private session: SessionService
	) {}

	ngOnInit() {}

	onTipoChange(value) {
		this.tipo = value;
	}

	exportar(destino): void {
		let chave = this.session.tokenChave;
		if (!chave) {
			chave = this.session.chave;
		}
		const dados: any = {
			titulo: "Avaliação de Progresso",
			destino,
			tipo: this.tipo,
			email: this.inputEmail.nativeElement.value,
		};

		this.alunosService
			.gerarPdfAvaliacaoProgresso(
				this.matricula,
				this.avaliadoId,
				this.fichaId,
				this.avaliacaoAlunoId
			)
			.subscribe({
				error: (error) => {
					this.notificationService.error(
						"Não foi possível gerar o pdf para o envio."
					);
				},
				next: (response) => {
					if (destino === "email") {
						dados.pdf = response.content;
						this.service.sendPdf(dados).subscribe((retorno) => {
							this.notificationService.success(
								this.translateService.instant("relatorio.emailEnviadoSucesso")
							);
						});
					} else {
						const valorInserido = this.inputFone.nativeElement.value;
						const msg =
							this.session.loggedUser.nome +
							" compartilhou um arquivo com você através do *Sistema Pacto*.\n Para visualizar o arquivo, acesse o link abaixo: " +
							response.content;
						const valorInput = Number(
							valorInserido
								.replace("(", "")
								.replace(")", "")
								.replace(" ", "")
								.replace("-", "")
						);
						const target =
							"https://api.whatsapp.com/send?phone=" +
							"55" +
							valorInput +
							"&text=" +
							msg;
						window.open(target, "_blank");
					}
				},
			});
	}

	enviarWhatsApp() {
		this.exportar("whatsapp");
	}

	enviarEmail(): void {
		this.exportar("email");
	}
}
