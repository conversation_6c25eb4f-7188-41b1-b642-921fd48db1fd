import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Optional,
	ViewChild,
} from "@angular/core";
import { FormGroup, FormControl, Validators, FormArray } from "@angular/forms";

import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";

import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { Colaborador } from "src/app/microservices/personagem/colaborador/colaborador.model";
import {
	AtividadeGraduacao,
	SubAtividades,
} from "src/app/microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { AvaliarAlunoModalAlertComponent } from "src/app/graduacao/components/avaliar-aluno-modal-alert/avaliar-aluno-modal-alert.component";
import {
	GraduacaoAvaliacao,
	AvaliacaoAlunoResposta,
} from "src/app/microservices/graduacao/graduacao.model";
import { AvaliacaoGraduacaoCriterio } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { DomSanitizer } from "@angular/platform-browser";
import { AvaliacaoProgressoAlunoService } from "../../../microservices/graduacao/avaliacao-progresso-aluno/avaliacao-progresso-aluno.service";
import { SessionService } from "@base-core/client/session.service";
import { TreinoApiAlunosService } from "treino-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-avaliar-aluno-modal",
	templateUrl: "./avaliar-aluno-modal.component.html",
	styleUrls: ["./avaliar-aluno-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliarAlunoModalComponent implements OnInit {
	@ViewChild("printFrame", { static: false }) printFrame;
	avaliador: Colaborador;
	avaliado: Cliente;
	avaliacao: GraduacaoAvaliacao;
	perguntas: AtividadeGraduacao[] = [];
	respostas: AvaliacaoAlunoResposta[] = [];
	criterio: AvaliacaoGraduacaoCriterio;
	injectPrintFrame = false;
	printLoading = false;
	avaliacaoAlunoId: number;
	printFrameUrl;
	canEdit;

	avaliacaoGrupoFg: FormGroup = new FormGroup({});
	observacaoGeralFc = new FormControl(null);
	answerOptions = [];
	frequenciasAluno: any[] = [];
	historicoAluno: any[] = [];
	fichaId: number;

	constructor(
		private domSanitizer: DomSanitizer,
		private cd: ChangeDetectorRef,
		private modalService: NgbModal,
		private avaliacaoProgressoAlunoService: AvaliacaoProgressoAlunoService,
		private alunosService: TreinoApiAlunosService,
		private snotifyService: SnotifyService,
		@Optional() private modal: NgbActiveModal,
		private session: SessionService
	) {}

	ngOnInit() {
		if (!this.creating) {
			this.setupPrinting();
		}
		this.loadInformacoesAluno();
	}

	loadInformacoesAluno() {
		this.avaliacaoProgressoAlunoService
			.obterInformacoesAluno(
				this.avaliado.matricula,
				this.avaliado.id,
				this.fichaId
			)
			.subscribe((response) => {
				this.frequenciasAluno = response.frequencia;
				this.historicoAluno = response.historico;
				this.cd.detectChanges();
			});
	}

	get nivel() {
		const avaliacao: any = this.avaliacao;
		if (avaliacao.nivel) {
			return avaliacao.nivel;
		} else {
			return undefined;
		}
	}

	get creating() {
		return !this.respostas || !this.respostas.length;
	}

	printHandler() {
		let chave = this.session.tokenChave;
		if (!chave) {
			chave = this.session.chave;
		}
		this.alunosService
			.gerarPdfAvaliacaoProgresso(
				this.avaliado.matricula,
				this.avaliado.id,
				this.fichaId,
				this.avaliacaoAlunoId
			)
			.subscribe({
				error: (error) => {
					this.snotifyService.error(
						"Não foi possível gerar o pdf para impressão."
					);
				},
				next: (response) => {
					window.open(response.content, "_blank");
				},
			});
	}

	closeHandler() {
		if (!this.creating) {
			this.modal.dismiss();
		} else {
			const ref = this.modalService.open(AvaliarAlunoModalAlertComponent, {
				centered: true,
				windowClass: "modal-alert-avaliacao",
			});
			ref.result.then(
				(result) => {
					if (result) {
						this.modal.dismiss();
					}
				},
				() => {}
			);
		}
	}

	loadData(
		avaliador: any,
		avaliado: any,
		avaliacao: GraduacaoAvaliacao,
		perguntas: AtividadeGraduacao[],
		respostas: AvaliacaoAlunoResposta[],
		criterio: AvaliacaoGraduacaoCriterio,
		canEdit: boolean,
		avaliacaoAlunoId: number,
		observacaoGeral: string,
		fichaId: number
	) {
		this.fichaId = fichaId;
		this.avaliador = avaliador;
		this.avaliado = avaliado;
		this.avaliacao = avaliacao;
		this.perguntas = perguntas;
		this.respostas = respostas;
		this.criterio = criterio;
		this.canEdit = canEdit;
		this.buildFormGroup(perguntas);
		this.avaliacaoAlunoId = avaliacaoAlunoId;
		this.observacaoGeralFc.setValue(observacaoGeral);
		if (this.respostas && this.respostas.length) {
			this.filloutForm();
		}
		this.filloutForm();
		this.setOptions();
		this.cd.detectChanges();
	}

	get avaliacaoValida() {
		let valid = true;
		const avaliacao = this.avaliacaoGrupoFg.getRawValue();
		for (const atv in avaliacao) {
			const atividadeFg = avaliacao[atv];
			if (atividadeFg.possuiSubAtividade) {
				for (const subAtv of atividadeFg.subAtividades) {
					for (const aluno of subAtv.avaliados) {
						valid = valid && aluno.resposta !== null;
					}
				}
			} else {
				for (const aluno of atividadeFg.avaliados) {
					valid = valid && aluno.resposta !== null;
				}
			}
		}
		return valid;
	}

	filloutForm() {
		this.respostas.forEach((resposta) => {
			if (!this.canEdit) {
				this.avaliacaoGrupoFg.get(`${resposta.atividade.id}`).disable();
				this.avaliacaoGrupoFg.get(`${resposta.atividade.id}`).disable();
			}
		});
		if (!this.canEdit) {
			this.observacaoGeralFc.disable();
		}
	}

	getRespostaFg(perguntaId: number) {
		return this.avaliacaoGrupoFg.get(`${perguntaId}`);
	}

	getPossuiSubAtividade(perguntaId: number) {
		return this.avaliacaoGrupoFg.get(`${perguntaId}`).value.possuiSubAtividade;
	}

	salvarHandler() {
		if (this.avaliacaoValida) {
			const dto = this.getDto();
			this.modal.close(dto);
		}
	}

	private setOptions() {
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				this.answerOptions = ["1", "2", "3", "4", "5"];
				break;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				this.answerOptions = ["Sim", "Não"];
				break;
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				this.answerOptions = [
					"Excelente",
					"Ótimo",
					"Muito bom",
					"Bom",
					"Regular",
					"Não executou",
				];
				break;
		}
	}

	private setupPrinting() {
		const tipo = this.avaliacao.hasOwnProperty("nivel") ? "progresso" : "livre";
		const url = `graduacao/avaliacoes/${this.avaliacao.id}/avaliacao-aluno/${this.avaliacaoAlunoId}/print?tipo=${tipo}`;
		this.printFrameUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(url);
		window.addEventListener(
			"message",
			(event) => {
				if (event.data === "loaded") {
					this.printLoading = false;
					this.cd.detectChanges();
					setTimeout(() => {
						this.print();
					}, 100);
				}
			},
			false
		);
	}

	private print() {
		const frame = this.printFrame.nativeElement;
		const win = frame.contentWindow;
		if (win) {
			const previousTitle = document.title;
			const title = `${this.avaliado.nome} (${this.avaliado.matricula}) - ${this.avaliacao.ficha.nome}`;
			document.title = title;
			win.focus();
			win.print();
			document.title = previousTitle;
		}
	}

	private getDto() {
		const respostas = this.avaliacaoGrupoFg.getRawValue();
		const avaliacaoGrupo: any = {
			avaliacaoProgressoId: this.avaliacao.id,
			observacaoGeral: this.observacaoGeralFc.value,
			timestamp: new Date().valueOf(),
			atividades: [],
		};
		for (const atv in respostas) {
			const atividadeFg = respostas[atv];
			const listSubAtividades: any = [];
			if (atividadeFg.possuiSubAtividade) {
				for (const subAtv of atividadeFg.subAtividades) {
					const listAvaliados: any = [];
					for (const aluno of subAtv.avaliados) {
						listAvaliados.push({
							respostaAvaliacaoId: aluno.respostaId,
							alunoId: aluno.avaliadoId,
							resposta: this.tratarRespostaSubtrair(aluno.resposta),
							observacao: aluno.observacao,
						});
					}
					listSubAtividades.push({
						subAtividadeId: subAtv.subAtividadeId,
						avaliados: listAvaliados,
					});
				}
				avaliacaoGrupo.atividades.push({
					atividadeId: atividadeFg.atividadeId,
					possuiSubAtividade: true,
					subAtividades: listSubAtividades,
				});
			} else {
				const listAvaliados: any = [];
				for (const aluno of atividadeFg.avaliados) {
					listAvaliados.push({
						respostaAvaliacaoId: aluno.respostaId,
						alunoId: aluno.avaliadoId,
						resposta: this.tratarRespostaSubtrair(aluno.resposta),
						observacao: aluno.observacao,
					});
				}
				avaliacaoGrupo.atividades.push({
					atividadeId: atividadeFg.atividadeId,
					possuiSubAtividade: false,
					avaliados: listAvaliados,
				});
			}
		}
		return avaliacaoGrupo;
	}

	tratarRespostaSubtrair(resposta) {
		// tratamento para problema do index 0 das respostas que fazia com que o botão de salvar não fosse habilitado
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				return resposta - 1;
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				return resposta - 1;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				return resposta;
		}
	}

	tratarRespostaSomar(resposta) {
		// tratamento para problema do index 0 das respostas que fazia com que o botão de salvar não fosse habilitado
		switch (this.criterio) {
			case AvaliacaoGraduacaoCriterio.CONCEITOS:
				return resposta + 1;
			case AvaliacaoGraduacaoCriterio.DE_1_A_5:
				return resposta + 1;
			case AvaliacaoGraduacaoCriterio.SIM_NAO:
				return resposta;
		}
	}

	private buildFormGroup(atividades: AtividadeGraduacao[]) {
		this.avaliacaoGrupoFg = new FormGroup({});
		atividades.forEach((atividade) => {
			let possuiSubAtividade;
			if (this.canEdit) {
				possuiSubAtividade = atividade.subAtividades.length > 0;
			} else {
				possuiSubAtividade = this.respostaTemSubAtividade(atividade.id);
			}
			this.buildFormGroupGeneric(atividade, possuiSubAtividade);
		});
	}

	// validação necessária pois no momento que a AV foi concluída as atividades poderiam não ter subatividades
	private respostaTemSubAtividade(atividadeId: number) {
		const resposta = this.respostas.find(
			(item) => item.atividade.id === atividadeId
		);
		if (resposta) {
			return resposta.subAtividade !== undefined;
		}
		return false;
	}

	private buildFormGroupGeneric(
		atividade: AtividadeGraduacao,
		possuiSubAtividade: boolean
	) {
		let subAtividadesFa = null;
		let avaliadoFa = null;
		if (possuiSubAtividade) {
			subAtividadesFa = this.buildSubAtividadesForm(
				atividade.id,
				atividade.subAtividades
			);
		} else {
			avaliadoFa = this.buildAvaliadosForm(atividade.id, null);
		}
		this.avaliacaoGrupoFg.addControl(
			`${atividade.id}`,
			new FormGroup({
				atividadeId: new FormControl(atividade.id),
				possuiSubAtividade: new FormControl(possuiSubAtividade),
				subAtividades: possuiSubAtividade
					? subAtividadesFa
					: new FormControl(null),
				avaliados: possuiSubAtividade ? new FormControl(null) : avaliadoFa,
			})
		);
	}

	buildSubAtividadesForm(atividadeId, subAtividades: SubAtividades[]) {
		const subAtividadesFa = new FormArray([]);
		subAtividades.forEach((subAtividade) => {
			const avaliadosFa: FormArray = this.buildAvaliadosForm(
				atividadeId,
				subAtividade.id
			);
			subAtividadesFa.controls.push(
				new FormGroup({
					subAtividadeId: new FormControl(subAtividade.id),
					avaliados: avaliadosFa,
				})
			);
		});
		return subAtividadesFa;
	}

	buildAvaliadosForm(atividadeId, subAtividadeId) {
		const avaliadosFa = new FormArray([]);

		const respostaAluno = this.getRespostaAluno(
			atividadeId,
			subAtividadeId,
			this.avaliado.id
		);
		const respostaId =
			respostaAluno !== null ? respostaAluno.respostaAvaliacaoId : null;
		let resposta = respostaAluno !== null ? respostaAluno.resposta : null;
		resposta = resposta == null ? null : this.tratarRespostaSomar(resposta);
		const observacao = respostaAluno !== null ? respostaAluno.observacao : null;
		avaliadosFa.controls.push(
			new FormGroup({
				avaliadoId: new FormControl(this.avaliado.id),
				respostaId: new FormControl(respostaId),
				resposta: new FormControl(resposta, [Validators.required]),
				observacao: new FormControl(observacao),
			})
		);

		return avaliadosFa;
	}

	getRespostaAluno(atividadeId, subAtividadeId, alunoId) {
		let resposta;
		if (subAtividadeId !== null) {
			for (const res of this.respostas) {
				if (
					res.atividade &&
					res.subAtividade &&
					res.atividade.id === atividadeId &&
					res.subAtividade.id === subAtividadeId
				) {
					resposta = res;
				}
			}
		} else {
			resposta = this.respostas.find(
				(item) => item.atividade.id === atividadeId
			);
		}
		return resposta !== undefined ? resposta : null;
	}
}
