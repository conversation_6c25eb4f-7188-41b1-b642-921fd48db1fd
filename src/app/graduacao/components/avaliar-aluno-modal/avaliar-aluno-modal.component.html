<!-- alterações de front avaliação individual -->
<div class="header">
	<div class="header-nome-avaliacao">
		<i (click)="closeHandler()" class="close-icon pct pct-x"></i>
		<div class="nome">
			<div class="type-h2 cor-preto-pri">Avaliação</div>
			<div class="nome-ficha type-h4 cor-cinza04">
				{{ avaliacao?.ficha?.nome }}
			</div>
		</div>
	</div>
	<div class="time">
		<i class="pct pct-calendar cor-azulim-pri"></i>
		<span class="type-h4 cor-cinza04">
			{{ avaliacao?.data | date : "shortDate" }}
		</span>
	</div>
</div>

<div class="body">
	<div class="upper">
		<div *ngIf="nivel" class="block">
			<div class="block-label type-h6">Nível</div>
			<div class="block-aux">
				<pacto-cat-person-avatar
					[diameter]="32"
					[uri]="nivel?.fotoUri"></pacto-cat-person-avatar>
				<div class="block-value type-h6">{{ nivel?.nome }}</div>
			</div>
		</div>
		<div class="block">
			<div class="block-label type-h6">Avaliador</div>
			<div class="block-aux">
				<pacto-cat-person-avatar
					[diameter]="32"
					[uri]="avaliador?.imageUri"></pacto-cat-person-avatar>
				<div class="block-value type-h6">{{ avaliador?.nome }}</div>
			</div>
		</div>
		<div class="block">
			<div class="block-label type-h6" i18n="@@geral:avaliado">Avaliado</div>
			<div class="block-aux">
				<pacto-cat-person-avatar
					[diameter]="32"
					[uri]="avaliado?.imageUri"></pacto-cat-person-avatar>
				<div class="block-value type-h6">{{ avaliado?.nome }}</div>
			</div>
		</div>
	</div>

	<div class="perguntas">
		<pacto-avaliar-aluno-modal-resposta
			*ngFor="let pergunta of perguntas"
			[answerOptions]="answerOptions"
			[atividade]="pergunta"
			[criterio]="criterio"
			[description]="pergunta.descricao"
			[formGroup]="getRespostaFg(pergunta.id)"
			[name]="pergunta.nome"
			[possuiSubAtividade]="getPossuiSubAtividade(pergunta.id)"
			[subAtividades]="pergunta.subAtividades"
			[uri]="pergunta.imageUri"></pacto-avaliar-aluno-modal-resposta>
	</div>

	<div class="observacao" i18n="@@geral:observacao-geral">Observação Geral</div>
	<textarea
		[formControl]="observacaoGeralFc"
		class="observacao-geral"
		i18n-placeholder="@@geral:alguma-obervacao"
		maxlength="512"
		placeholder="Alguma observação geral sobre a avaliação"
		rows="3"></textarea>

	<div>
		<div class="titulo-secao" i18n="@@geral:frequencia-em-aulas">
			Frequência em aulas
		</div>
		<div class="frequencias-aluno">
			<div class="coluna-titulos">
				<div class="titulo-linha border-bottom">Mês</div>
				<div class="titulo-linha border-bottom">Presença</div>
				<div class="titulo-linha" i18n="@@geral:aulas-previstas">
					Aulas Previstas
				</div>
			</div>
			<div class="div-meses">
				<div *ngFor="let frequencia of frequenciasAluno" class="coluna-info">
					<div class="celula-cabecalho">
						{{ frequencia.mes }}
					</div>
					<div class="celula-info border-bottom">
						{{ frequencia.presenca }}
					</div>
					<div class="celula-info">
						{{ frequencia.aulasPrevistas }}
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="secao-historico">
		<div class="titulo-secao type-h4" i18n="@@geral:historico">Histórico</div>
		<div class="row-historico">
			<div *ngFor="let historico of historicoAluno" class="col-md-3 historico">
				<div class="div-item">
					<div class="col-md-6 titulo-linha">Mês / Ano</div>
					<div class="col-md-6 texto-info-historito">
						{{ historico.mesAno }}
					</div>
				</div>
				<div class="div-item">
					<div class="col-md-6 titulo-linha">Professor (a)</div>
					<div class="col-md-6 texto-info-historito">
						{{ historico.professores }}
					</div>
				</div>
				<div class="div-item">
					<div class="col-md-6 titulo-linha">Horário</div>
					<div *ngIf="historico.horarios" class="col-md-6 texto-info-historito">
						<div *ngFor="let horario of historico.horarios">{{ horario }}</div>
					</div>
				</div>
				<div class="div-item">
					<div class="col-md-6 titulo-linha">Dias</div>
					<div class="col-md-6 texto-info-historito">{{ historico.dias }}</div>
				</div>
				<div class="div-item-sem-borda">
					<div class="col-md-6 titulo-linha">Nível</div>
					<div class="col-md-6 texto-info-historito">{{ historico.nivel }}</div>
				</div>
			</div>
		</div>
	</div>

	<div class="action-footer">
		<pacto-cat-button
			(click)="salvarHandler()"
			*ngIf="canEdit"
			[disabled]="!avaliacaoValida"
			[full]="true"
			[label]="'salvar'"></pacto-cat-button>
		<pacto-cat-button
			(click)="closeHandler()"
			[full]="true"
			[label]="'cancelar'"
			[type]="'OUTLINE'"></pacto-cat-button>

		<pacto-cat-button
			(click)="printHandler()"
			*ngIf="!canEdit"
			[disabled]="printLoading"
			[full]="true"
			[label]="printLoading ? 'carregando...' : 'imprimir'"
			[type]="'OUTLINE'"></pacto-cat-button>

		<pacto-share-pdf-button
			*ngIf="!canEdit"
			[avaliacaoAlunoId]="avaliacaoAlunoId"
			[avaliadoId]="avaliado.id"
			[fichaId]="fichaId"
			[matricula]="avaliado.matricula"></pacto-share-pdf-button>
	</div>

	<iframe
		#printFrame
		*ngIf="injectPrintFrame"
		[attr.src]="printFrameUrl"
		id="print-frame"></iframe>
</div>
