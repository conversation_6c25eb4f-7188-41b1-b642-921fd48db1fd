import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";

import { BehaviorSubject, Observable } from "rxjs";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { ClienteService } from "../../../microservices/personagem/cliente/cliente.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { map } from "rxjs/operators";
import { FichaService } from "../../../microservices/graduacao/ficha/ficha.service";
import { ActivatedRoute } from "@angular/router";
import { NivelService } from "../../../microservices/graduacao/nivel/nivel.service";
import { AvaliacaoProgressoService } from "../../../microservices/graduacao/avaliacao-progresso/avaliacao.service";

@Component({
	selector: "pacto-incluir-alunos-ficha-modal",
	templateUrl: "./incluir-alunos-ficha-modal.component.html",
	styleUrls: ["./incluir-alunos-ficha-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IncluirAlunosFichaModalComponent implements OnInit {
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("aulaColumnName", { static: true }) aulaColumnName;
	@ViewChild("matriculaZwColumnName", { static: false }) matriculaZwColumnName;
	@ViewChild("turmaColumnName", { static: false }) turmaColumnName;
	@ViewChild("professorColumnName", { static: false }) professorColumnName;
	@ViewChild("niveltColumnName", { static: false }) niveltColumnName;
	@ViewChild("ambienteColumnName", { static: false }) ambienteColumnName;
	@ViewChild("imagemColumnName", { static: true }) imagemColumnName;
	@ViewChild("nivelColumnName", { static: true }) nivelColumnName;
	@ViewChild("situacaoColumnName", { static: false }) situacaoColumnName;
	@ViewChild("nivelCelula", { static: false }) nivelCelula;
	@ViewChild("imageCelula", { static: true }) imageCelula;
	@ViewChild("nomeCelula", { static: true }) nomeCelula;
	@ViewChild("aulaCelula", { static: true }) aulaCelula;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("xinglingFilterIndividual", { static: false })
	xinglingFilterIndividual: TraducoesXinglingComponent;
	@ViewChild("xinglingFilterIntegrado", { static: false })
	xinglingFilterIntegrado: TraducoesXinglingComponent;
	@ViewChild("xinglingFilterTreino", { static: false })
	xinglingFilterTreino: TraducoesXinglingComponent;
	@ViewChild("statusClienteLabel", { static: false }) statusClienteLabel;
	@ViewChild("alunoInserido", { static: true }) alunoInserido;
	@ViewChild("nenhum", { static: true }) nenhum;
	ready = false;
	ativos = true;
	fichaId: number;
	ficha: Ficha;
	nivelId;
	avaliacaoId;
	insert;
	stepNumber$: BehaviorSubject<number>;
	alunos: Cliente[] = [];
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	filtros: any;
	stop: any;

	get step() {
		return this.stepNumber$.value;
	}

	constructor(
		private modal: NgbActiveModal,
		private clientService: ClienteService,
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private route: ActivatedRoute,
		private nivelService: NivelService,
		private fichaService: FichaService,
		private avaliacaoProgressoService: AvaliacaoProgressoService
	) {}

	ngOnInit() {
		setTimeout(() => {
			this.getFiltros().subscribe(() => {
				this.configFilters();
				this.configTable(this.insert === "avaliacao");
				this.ready = true;
				this.cd.detectChanges();
			});
		}, 100);
	}

	private getFiltros(): Observable<any> {
		return this.fichaService.obterFIltrosFicha(this.ficha.id).pipe(
			map((dados) => {
				this.filtros = dados;
				return true;
			})
		);
	}

	private configFilters() {
		this.filterConfig = {
			filters: this.addFilter(),
		};
	}

	loadData(ficha: Ficha, nivel: number, avaliacao: number, insert: string) {
		this.nivelId = nivel;
		this.avaliacaoId = avaliacao;
		this.ficha = ficha;
		this.insert = insert;
		this.cd.detectChanges();
	}

	openAluno(matricula) {
		window
			.open("pessoas/perfil-v2/" + matricula + "/contratos", "_blank")
			.focus();
	}

	inserirAlunosNivel() {
		const openModals: { id: number; column: number }[] = [];
		if (
			this.table.allCheck === false &&
			(this.insert === "nivel" || this.insert === "ficha-aluno")
		) {
			this.fichaService
				.inserirAlunosFicha(this.ficha.id, this.getDto(this.table.checkeds))
				.subscribe(() => {
					const alunoInserido = this.alunoInserido.nativeElement.innerHTML;
					this.modal.close(alunoInserido);
				});
		}
		if (
			this.table.allCheck === true &&
			(this.insert === "nivel" || this.insert === "ficha-aluno")
		) {
			this.fichaService
				.inserirAlunosFichaTodos(this.nivelId, this.tableData.allFilters())
				.subscribe(() => {
					const alunoInserido = this.alunoInserido.nativeElement.innerHTML;
					this.modal.close(alunoInserido);
				});
		}
		if (this.insert === "avaliacao") {
			this.modal.close({
				alunos: this.table.allCheck ? "todos" : "marcados",
				marcados: this.table.checkeds,
				filtros: this.tableData.allFilters(),
			});
		}
	}

	private getDto(alunosId) {
		return {
			nivel: this.nivelId,
			alunoIds: alunosId,
		};
	}

	private configTable(avaliacao: boolean) {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlGraduacao(
				avaliacao
					? `aluno/avaliacao/${this.nivelId}/${this.ficha.id}/${this.avaliacaoId}`
					: `aluno/${this.nivelId}`
			),
			quickSearch: true,
			valueRowCheck: "id",
			columns: [
				{
					nome: "imageUri",
					titulo: this.imagemColumnName,
					mostrarTitulo: false,
					visible: true,
					ordenavel: false,
					celula: this.imageCelula,
					campo: "imageUri",
				},
				{
					nome: "nome",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					celula: this.nomeCelula,
					defaultVisible: true,
				},
				{
					nome: "aulas",
					titulo: this.aulaColumnName,
					buscaRapida: false,
					visible: avaliacao,
					ordenavel: false,
					celula: this.aulaCelula,
					defaultVisible: true,
				},
				{
					nome: "professor",
					titulo: this.professorColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "turma",
					titulo: this.turmaColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "nivel",
					titulo: this.niveltColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "ambiente",
					titulo: this.ambienteColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
			],
		});
	}

	private getOptionsFilter() {
		const options = [];
		options.push(
			{
				value: "AT",
				label: this.xinglingFilterIntegrado.getLabel("ativo_normal"),
			},
			{
				value: "IN",
				label: this.xinglingFilterIntegrado.getLabel("inativo_cancelado"),
			},
			{ value: "VI", label: this.xinglingFilterIntegrado.getLabel("visitante") }
		);
		return options;
	}

	private addFilter() {
		const result = [];
		result.push({
			name: "turmasIds",
			label: this.turmaColumnName,
			type: GridFilterType.MANY,
			options: this.filtros.turmas,
		});
		result.push({
			name: "professoresIds",
			label: this.professorColumnName,
			type: GridFilterType.MANY,
			options: this.filtros.professores,
		});
		result.push({
			name: "niveisIds",
			label: this.nivelColumnName,
			type: GridFilterType.MANY,
			options: this.filtros.niveis,
		});
		result.push({
			name: "ambientesIds",
			label: this.ambienteColumnName,
			type: GridFilterType.MANY,
			options: this.filtros.ambientes,
		});
		result.push({
			name: "situacoesEnuns",
			label: this.situacaoColumnName,
			type: GridFilterType.MANY,
			options: this.getOptionsFilter(),
			initialValue: ["AT"],
		});
		return result;
	}
}
