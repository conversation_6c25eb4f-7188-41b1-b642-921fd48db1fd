@import "src/assets/scss/pacto/plataforma-import.scss";

.body {
	padding: 0px 30px;
}

.footer {
	padding: 30px;
	border-top: 1px solid $cinza02;
}

.tabela-incluir-alunos {
	.btn-add {
		display: block;
		float: right;
		margin: 0 30px 30px 0;
	}
}

.nome-filter {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	margin: 15px 0px;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

.header {
	display: flex;

	.item {
		line-height: 48px;
		@extend .type-btn-bold;
		text-transform: uppercase;
	}

	.todos {
		cursor: pointer;
	}

	.nome {
		flex-basis: 80%;
	}
}

.scroll-aux {
	height: 365px;
}

.aluno {
	display: flex;
	align-items: center;
	border-bottom: 1px solid $cinzaPri;

	.nome {
		flex-basis: 80%;
		@extend .type-caption-rounded;
		color: $cinza03;
		text-transform: uppercase;
		line-height: 44px;
	}
}

.color-error {
	color: #ca0000;
}

.div-info-aluno-lista {
	padding-top: 15px;
	height: 348px;
}

.font-size-11 {
	font-size: 11px;
}

.content-wrapper {
	padding-top: 0 !important;
}

.table-content {
	padding-bottom: 12px;
}

.nomealuno {
	cursor: pointer;
	position: relative;

	.matricula {
		font-size: 12px;
		color: #b4b7bb;
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	::ng-deep .modal-incluir-aluno-avaliacao-progresso {
		::ng-deep pacto-relatorio {
			.pacto-table-title-block {
				padding: 10px;
				display: block;

				.command-buttons {
					.command {
						font-size: 10px;
					}
				}

				.search {
					display: block;
					margin: 12px 0;

					input {
						width: 100%;
					}
				}

				.actions {
					justify-content: flex-end;
				}
			}

			.table-content {
				overflow: auto;

				.table {
					width: 1000px;
					max-width: 1000px;
				}
			}
		}
	}

	::ng-deep .modal-incluir-aluno-nivel-ficha {
		::ng-deep pacto-relatorio {
			.pacto-table-title-block {
				padding: 10px;
				display: block;

				.command-buttons {
					.command {
						font-size: 10px;
					}
				}

				.search {
					display: block;
					margin: 12px 0;

					input {
						width: 100%;
					}
				}

				.actions {
					justify-content: flex-end;
				}
			}
		}
	}
}
