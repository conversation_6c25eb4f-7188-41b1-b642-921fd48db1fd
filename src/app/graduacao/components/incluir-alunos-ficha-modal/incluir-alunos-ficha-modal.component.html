<pacto-content-layout>
	<div *ngIf="ready" class="table-wrapper tabela-incluir-alunos">
		<pacto-relatorio
			#tableData
			[filterConfig]="filterConfig"
			[showShare]="false"
			[table]="table"></pacto-relatorio>
		<div class="div-button btn-add">
			<pacto-cat-button
				(click)="inserirAlunosNivel()"
				[icon]="'pct pct-user-plus'"
				label="Confirmar"></pacto-cat-button>
		</div>
	</div>
</pacto-content-layout>

<pacto-traducoes-xingling #xinglingFilterIntegrado>
	<span xingling="ativo_normal">Ativo</span>
	<span xingling="inativo_cancelado">Inativo</span>
	<span xingling="visitante">Visitante</span>
</pacto-traducoes-xingling>

<ng-template #imagemColumnName>
	<span>Foto</span>
</ng-template>

<ng-template #nomeColumnName>
	<span>Nome</span>
</ng-template>
<ng-template #aulaColumnName>
	<span>Aulas</span>
</ng-template>
<ng-template #matriculaZwColumnName>
	<span>Matricula</span>
</ng-template>
<ng-template #professorColumnName>
	<span>Professor</span>
</ng-template>

<ng-template #turmaColumnName>
	<span>Turma</span>
</ng-template>

<ng-template #niveltColumnName>
	<span>Nível da turma</span>
</ng-template>

<ng-template #ambienteColumnName>
	<span>Ambiente</span>
</ng-template>

<ng-template #nivelColumnName>
	<span i18n="@@crud-alunos:nivel:title:table">Nivel</span>
</ng-template>
<ng-template #situacaoColumnName>
	<span i18n="@@crud-alunos:situacao:title:table">Situação</span>
</ng-template>
<ng-template #nivelCelula let-item="item">
	{{ item.nivel?.nome }}
</ng-template>

<ng-template #imageCelula let-item="item">
	<pacto-cat-person-avatar
		[diameter]="30"
		[uri]="item.imageUri"></pacto-cat-person-avatar>
</ng-template>

<ng-template #nomeCelula let-item="item">
	<span (click)="openAluno(item.matricula)" class="nomealuno">
		<div>{{ item.nome }}</div>
		<div class="matricula">Matrícula {{ item.matricula }}</div>
	</span>
</ng-template>

<ng-template #aulaCelula let-item="item">
	<span>{{ item.aulas }} / {{ item.aulasNecessarias }}</span>
</ng-template>

<ng-template>
	<span>Nome</span>
</ng-template>

<span #alunoInserido [hidden]="true" i18n="@@crud-delete:mensagem:nivel-vazio">
	Alunos inseridos com sucesso.
</span>
<span #nenhum [hidden]="true">Selecione um Aluno.</span>
