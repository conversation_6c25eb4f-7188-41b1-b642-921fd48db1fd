import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { AvaliacaoLivreStatus } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.model";
import { CriarAvaliacaoLivreModalComponent } from "src/app/graduacao/avaliacoes-livres/criar-avaliacao-modal/criar-avaliacao-modal.component";
import { AvaliacaoLivreService } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.service";

declare var moment;

@Component({
	selector: "pacto-avaliacao-lista",
	templateUrl: "./avaliacao-lista.component.html",
	styleUrls: ["./avaliacao-lista.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoLivreListaComponent implements OnInit {
	@ViewChild("add", { static: true }) add;
	@ViewChild("status", { static: true }) status;
	@ViewChild("tableData", { static: true }) table: RelatorioComponent;
	@ViewChild("ficha", { static: true }) ficha;

	tableConfig: PactoDataGridConfig;

	constructor(
		private snotify: SnotifyService,
		private modalService: ModalService,
		private restService: RestService,
		private router: Router,
		private avaliacaoLivreService: AvaliacaoLivreService
	) {}

	ngOnInit() {
		this.configTable();
	}

	rowClickHandler(item) {
		this.router.navigate(["graduacao", "avaliacoes-livre", item.id]);
	}

	btnClickHandler() {
		const handle = this.modalService.open(
			"Criar Avaliação Livre",
			CriarAvaliacaoLivreModalComponent
		);
		handle.result.then(
			(res) => {
				this.avaliacaoLivreService
					.cadastrarAvaliacaoLivre(res)
					.subscribe((ok) => {
						if (ok) {
							this.snotify.success("Avaliação livre criada com sucesso.");
							this.router.navigate(["graduacao", "avaliacoes-livre", ok.id]);
						}
					});
			},
			() => {}
		);
	}

	private configTable() {
		this.tableConfig = new PactoDataGridConfig({
			// endpointUrl: this.restService.buildFullUrlGraduacao('avaliacoes-progresso'),
			dataAdapterFn: (data) => {
				return {
					size: 10,
					content: [
						{
							id: 3,
							ficha: {
								id: 56,
								nome: "Natação Avançada",
								nOfNiveis: 56,
							},
							avaliadorId: 507,
							data: 1579489200000,
							nOfAtividades: 76,
							status: AvaliacaoLivreStatus.ABERTA,
						},
					],
					totalElementes: 12,
					number: 0,
				};
			},
			quickSearch: true,
			rowClick: true,
			buttons: {
				id: "add",
				conteudo: this.add,
				nome: "add",
			},
			columns: [
				{
					nome: "ficha",
					titulo: "Ficha Técnica",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.ficha,
				},
				{
					nome: "nOfAtividades",
					titulo: "Qnt. Atividades",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "qtd-alunos",
					titulo: "Qnt. Alunos",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "data",
					titulo: "Data da avaliação",
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					valueTransform: (v) => moment(v).format("L"),
				},
				{
					nome: "status",
					titulo: "Status",
					visible: true,
					defaultVisible: true,
					celula: this.status,
				},
			],
		});
	}
}
