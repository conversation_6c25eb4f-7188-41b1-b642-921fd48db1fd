<pacto-content-layout>
	<div class="page-title">Avaliações Livre</div>

	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="btnClickHandler()"
			(rowClick)="rowClickHandler($event)"
			[tableTitle]="'Avaliações Livres'"
			[table]="tableConfig"></pacto-relatorio>
	</div>
</pacto-content-layout>

<ng-template #add>Adicionar</ng-template>

<!-- Nome da ficha -->
<ng-template #ficha let-item="item">{{ item.ficha.nome }}</ng-template>

<ng-template #status let-item="item">
	<ng-container [ngSwitch]="item.status">
		<div *ngSwitchCase="'ABERTA'" class="avaliacao-status">
			<div class="marker aberta"></div>
			<div>Em Andamento</div>
		</div>
		<div *ngSwitchCase="'CONCLUIDA'" class="avaliacao-status">
			<div class="marker concluida"></div>
			<div>Concluída</div>
		</div>
	</ng-container>
</ng-template>
