import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";

import { Observable, zip } from "rxjs";
import { tap, switchMap } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";

import { SelectAlunoModalComponent } from "../../components/select-aluno-modal/select-aluno-modal.component";
import { AvaliarAlunoModalComponent } from "../../components/avaliar-aluno-modal/avaliar-aluno-modal.component";
import { ConcluirAvaliacaoComponent } from "src/app/graduacao/components/concluir-avaliacao/concluir-avaliacao.component";
import { GraduacaoAvaliacaoTipo } from "src/app/microservices/graduacao/graduacao.model";

import { EditarAvaliacaoLivreModalComponent } from "src/app/graduacao/avaliacoes-livres/editar-avaliacao-livre-modal/editar-avaliacao-livre-modal.component";
import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { ColaboradorService } from "src/app/microservices/personagem/colaborador/colaborador.service";
import {
	ModalService,
	PactoModalSize,
} from "src/app/base/base-core/modal/modal.service";
import { ClienteService } from "src/app/microservices/personagem/cliente/cliente.service";
import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { AvaliacaoLivreService } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.service";
import {
	GraduacaoAvaliacaoLivre,
	AvaliacaoLivreStatus,
} from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.model";
import { AvaliacaoLivreAlunoService } from "src/app/microservices/graduacao/avaliacao-livre-aluno/avaliacao-livre-aluno.service";
import { AvaliacaoLivreAluno } from "src/app/microservices/graduacao/avaliacao-livre-aluno/avaliacao-livre-aluno.model";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";

@Component({
	selector: "pacto-avaliacao-detalhes",
	templateUrl: "./avaliacao-livre-detalhes.component.html",
	styleUrls: ["./avaliacao-livre-detalhes.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoLivreDetalhesComponent implements OnInit {
	avaliador: any = {};
	alunos: any[] = [];
	ficha: Ficha;
	avaliacao: GraduacaoAvaliacaoLivre;
	avaliacoesAlunos: { [alunoId: number]: AvaliacaoLivreAluno } = {};

	constructor(
		private route: ActivatedRoute,
		private snotify: SnotifyService,
		private modal: ModalService,
		private cd: ChangeDetectorRef,
		private colaboradorService: ColaboradorService,
		private avaliacaoLivreAlunoService: AvaliacaoLivreAlunoService,
		private avaliacaoLivreService: AvaliacaoLivreService,
		private fichaService: FichaService,
		private clientService: ClienteService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.loadData().subscribe(() => {});
	}

	get avaliacaoDone() {
		return (
			this.avaliacao && this.avaliacao.status === AvaliacaoLivreStatus.CONCLUIDA
		);
	}

	removerAlunoHandler(aluno) {
		const id = this.avaliacao.id;
		this.avaliacaoLivreService.removerAlunos(id, [aluno.id]).subscribe(() => {
			this.loadData().subscribe(() => {
				this.snotify.success("Aluno removido com sucesso.");
			});
		});
	}

	concluirHandler() {
		const ref = this.modal.open(
			"Conclusão de avaliação livre",
			ConcluirAvaliacaoComponent,
			PactoModalSize.LARGE
		);
		const modal: ConcluirAvaliacaoComponent = ref.componentInstance;
		const niveis = Object.assign([], this.ficha.niveis);
		const alunosNiveis = [];
		this.alunos.forEach((aluno) => {
			alunosNiveis.push({
				aluno: Object.assign({}, aluno),
				nivelSugeridoId: -1,
			});
		});
		modal.loadData(
			alunosNiveis,
			niveis,
			GraduacaoAvaliacaoTipo.LIVRE,
			this.avaliacao.id,
			this.avaliacao.data
		);
		ref.result.then(
			() => {
				this.loadData().subscribe();
			},
			() => {}
		);
	}

	adicionarAlunosHandler() {
		if (this.avaliacaoDone) {
			return true;
		}
		const modalRef = this.modal.open(
			"Adicionar Alunos",
			SelectAlunoModalComponent,
			PactoModalSize.LARGE
		);
		const modal: SelectAlunoModalComponent = modalRef.componentInstance;
		this.clientService.obterClientes().subscribe((alunos) => {
			modal.loadData(alunos);
		});
		modalRef.result.then(
			(selectedIds) => {
				this.avaliacaoLivreService
					.inserirAlunos(this.avaliacao.id, selectedIds)
					.subscribe(() => {
						this.snotify.success(
							`${selectedIds.length} aluno(s) inseridos com sucesso.`
						);
						this.loadData().subscribe();
						this.sessionService.notificarRecursoEmpresa(
							RecursoSistema.INCLUIU_ALUNO_AVALIACAO_LIVRE_TROCA_NIVEL_GRD
						);
					});
			},
			() => {}
		);
	}

	alterarHandler() {
		if (this.avaliacaoDone) {
			return true;
		}
		const ref = this.modal.open(
			"Alterar Avaliação",
			EditarAvaliacaoLivreModalComponent
		);
		const modal: EditarAvaliacaoLivreModalComponent = ref.componentInstance;

		const colaboradoresFicha$ = this.fichaService
			.obterColaboradoresDaFicha(this.avaliacao.ficha.id)
			.pipe(
				switchMap((ids) => {
					return this.colaboradorService.obterColaboradores({ ids });
				})
			);

		colaboradoresFicha$.subscribe((colaboradores) => {
			modal.loadData(this.avaliacao, colaboradores);
		});

		ref.result.then(
			(dto) => {
				this.avaliacaoLivreService
					.editarAvaliacaoLivre(this.avaliacao.id, dto)
					.subscribe(() => {
						this.snotify.success("Avaliação Livre Alterada");
						this.loadData().subscribe(() => {});
					});
			},
			() => {}
		);
	}

	verAvaliacaoHandler(aluno: Cliente) {
		/**
		 * CARREGAR RESPOSTAS
		 */
		const avaliacaoAlunoId = this.avaliacoesAlunos[aluno.id].id;
		this.avaliacaoLivreAlunoService
			.obterRespostasAvaliacaoAluno(avaliacaoAlunoId)
			.subscribe((respostas) => {
				const ref = this.modal.openCustomFullscreen(AvaliarAlunoModalComponent);
				const modal: AvaliarAlunoModalComponent = ref.componentInstance;
				const atividades = respostas.map((resposta) =>
					Object.assign({}, resposta.atividade)
				);
				modal.loadData(
					this.avaliador,
					aluno,
					this.avaliacao,
					atividades,
					respostas,
					this.avaliacao.criterio,
					!this.avaliacaoDone,
					this.avaliacoesAlunos[aluno.id].id,
					null,
					null
				);
				ref.result.then(
					(dto) => {
						const id = dto.avaliacaoProgressoId;
						delete dto.avaliacaoProgressoId;
						this.avaliacaoLivreAlunoService.editar(id, dto).subscribe(() => {
							this.snotify.success("Avaliação Editada com sucesso");
						});
					},
					() => {}
				);
			});
	}

	avaliarHandler(aluno: Cliente) {
		const ref = this.modal.openCustomFullscreen(AvaliarAlunoModalComponent);
		const modal: AvaliarAlunoModalComponent = ref.componentInstance;
		modal.loadData(
			this.avaliador,
			aluno,
			this.avaliacao,
			this.avaliacao.atividades,
			[],
			null,
			true,
			null,
			null,
			null
		);
		ref.result.then(
			(dto) => {
				this.avaliacaoLivreAlunoService.cadastrar(dto).subscribe(() => {
					this.snotify.success("Aluno Avaliado com Sucesso");
					this.loadData().subscribe(() => {});
				});
			},
			() => {}
		);
	}

	private loadData(): Observable<any> {
		const id = this.route.snapshot.params.id;
		const avaliacao$ = this.avaliacaoLivreService.buscarAvaliacaoLivre(id).pipe(
			switchMap((avaliacao) => {
				this.avaliacao = avaliacao;
				const ficha$ = this.fichaService.obterFichaPorId(avaliacao.ficha.id);
				const avaliador$ = this.colaboradorService.obterColaborador(
					avaliacao.avaliadorId
				);
				return zip(ficha$, avaliador$);
			}),
			tap((result) => {
				this.ficha = result[0];
				this.avaliador = result[1];
			})
		);
		const alunos$ = this.avaliacaoLivreService
			.obterAlunosAvaliacaoLivre(id)
			.pipe(
				switchMap((ids) => {
					return this.clientService.obterClientes({ alunoids: ids });
				}),
				tap((clientes) => {
					this.alunos = clientes;
				})
			);
		const avaliacoesAlunos$ = this.avaliacaoLivreAlunoService
			.listar({ avaliacaoLivreId: id })
			.pipe(
				tap((avaliacoes) => {
					this.avaliacoesAlunos = this.convertIntoObject(avaliacoes);
				})
			);
		return zip(avaliacao$, alunos$, avaliacoesAlunos$).pipe(
			tap(() => {
				this.cd.detectChanges();
			})
		);
	}

	private convertIntoObject(data: AvaliacaoLivreAluno[]) {
		const result = {};
		data.forEach((item) => {
			result[item.alunoId] = item;
		});
		return result;
	}
}
