<pacto-content-layout>
	<div class="header-row">
		<div class="page-title">
			<i
				[routerLink]="['/graduacao', 'avaliacoes-livre']"
				class="pct pct-arrow-left"></i>
			Avaliações Livre
		</div>
	</div>

	<div class="body">
		<pacto-cat-card-plain class="info">
			<!-- Row 1 -->
			<div class="first-row">
				<div class="avaliador">
					<div class="label">Avaliador</div>
					<div class="value">
						<pacto-cat-person-avatar
							[diameter]="32"
							[uri]="avaliador?.imageUri"></pacto-cat-person-avatar>
						<div class="block-value">{{ avaliador?.nome }}</div>
					</div>
				</div>
				<pacto-cat-button
					(click)="alterarHandler()"
					[disabled]="avaliacaoDone"
					[icon]="'edit-2'"
					[label]="'ALTERAR'"></pacto-cat-button>
			</div>

			<!-- Row 2 -->
			<div *ngIf="avaliacao" class="second-row">
				<div class="block">
					<div class="label">Data</div>
					<div class="block-value">
						{{ avaliacao.data | date : "shortDate" }}
					</div>
				</div>
				<div class="block">
					<div class="label" i18n="@@geral:criterio-de-avaliacao">
						Critério de Avaliação
					</div>
					<div [ngSwitch]="avaliacao.criterio" class="block-value">
						<span *ngSwitchCase="'DE_1_A_5'" i18n="@@geral:um-a-cinco">
							> 1 à 5
						</span>
						<span *ngSwitchCase="'SIM_NAO'">Sim ou Não</span>
						<span *ngSwitchCase="'CONCEITOS'" i18n="@@geral:conceitos">
							Conceitos
						</span>
					</div>
				</div>
			</div>

			<!-- Row 3 -->
			<div class="third-row">
				<div class="block">
					<div class="label">Ficha Técnica</div>
					<div class="block-value">{{ avaliacao?.ficha.nome }}</div>
				</div>
			</div>
		</pacto-cat-card-plain>

		<pacto-cat-card-plain class="alunos">
			<div *ngIf="!alunos.length" class="empty">
				<i class="pct pct-users"></i>
				<p class="description">Nenhum aluno selecionado para ser avaliado.</p>
				<pacto-cat-button
					(click)="adicionarAlunosHandler()"
					[disabled]="avaliacaoDone"
					[icon]="'plus-circle'"
					[label]="'INSERIR ALUNOS'"></pacto-cat-button>
			</div>

			<div *ngIf="alunos.length" class="list">
				<div class="upper-row">
					<div
						class="type-h6 cor-cinza04"
						i18n="@@avaliacao-detalhes:alunos-serao-avaliados">
						Alunos que serão avaliados
					</div>
					<pacto-cat-button
						(click)="adicionarAlunosHandler()"
						[disabled]="avaliacaoDone"
						[icon]="'plus-circle'"
						[label]="'INSERIR ALUNOS'"></pacto-cat-button>
				</div>
				<div [maxHeight]="'587px'" class="scroll-wrapper" pactoCatSmoothScroll>
					<div *ngFor="let aluno of alunos" class="aluno">
						<pacto-cat-person-avatar
							[diameter]="32"
							[uri]="aluno.imageUri"></pacto-cat-person-avatar>
						<div class="nome type-h6 cor-preto04">{{ aluno.nome }}</div>
						<pacto-cat-button
							(click)="avaliarHandler(aluno)"
							*ngIf="!avaliacoesAlunos[aluno.id]"
							[icon]="'evaluation'"
							[label]="'avaliar'"
							[size]="'SMALL'"
							[type]="'DARK'"></pacto-cat-button>
						<pacto-cat-button
							(click)="verAvaliacaoHandler(aluno)"
							*ngIf="avaliacoesAlunos[aluno.id]"
							[icon]="'evaluation'"
							[label]="'avaliado'"
							[size]="'SMALL'"
							[type]="'OUTLINE'"></pacto-cat-button>
						<i
							(click)="removerAlunoHandler(aluno)"
							*ngIf="!avaliacaoDone"
							class="pct pct-trash-2"></i>
					</div>
				</div>
			</div>
		</pacto-cat-card-plain>

		<pacto-cat-card-plain class="atividades">
			<div class="label">Atividades do Nível</div>
			<div [maxHeight]="'300px'" class="atividades" pactoCatSmoothScroll>
				<div *ngFor="let atividade of avaliacao?.atividades" class="atividade">
					<pacto-cat-person-avatar
						[diameter]="34"
						[uri]="atividade.imageUri"></pacto-cat-person-avatar>
					<div class="value">{{ atividade.nome }}</div>
				</div>
			</div>
		</pacto-cat-card-plain>
	</div>

	<div class="footer">
		<pacto-cat-button
			(click)="concluirHandler()"
			*ngIf="!avaliacaoDone"
			[label]="'CONCLUIR AVALIAÇÃO'"></pacto-cat-button>
		<pacto-cat-button
			*ngIf="avaliacaoDone"
			[label]="'voltar'"
			[routerLink]="['/graduacao/avaliacoes-livre']"></pacto-cat-button>
	</div>
</pacto-content-layout>
