import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { GraduacaoAvaliacaoLivre } from "src/app/microservices/graduacao/avaliacao-livre/avaliacao-livre.model";
import { Colaborador } from "src/app/microservices/personagem/colaborador/colaborador.model";

@Component({
	selector: "pacto-editar-avaliacao-livre-modal",
	templateUrl: "./editar-avaliacao-livre-modal.component.html",
	styleUrls: ["./editar-avaliacao-livre-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditarAvaliacaoLivreModalComponent implements OnInit {
	colaboradores: Colaborador[] = [];
	avaliacao: GraduacaoAvaliacaoLivre;

	fg: FormGroup = new FormGroup({
		ficha: new FormControl(null),
		avaliador: new FormControl(null, Validators.required),
		data: new FormControl(null, Validators.required),
		criterio: new FormControl(null),
	});

	constructor(private modal: NgbActiveModal, private cd: ChangeDetectorRef) {}

	get ready() {
		return this.fg.get("data").valid;
	}

	loadData(avaliacao: GraduacaoAvaliacaoLivre, colaboradores: Colaborador[]) {
		this.avaliacao = avaliacao;
		this.colaboradores = colaboradores;
		this.fillOutForm();
		this.cd.detectChanges();
	}

	salvarHandler() {
		this.fg.get("data").markAsTouched();
		if (this.ready) {
			this.modal.close(this.getDto());
		}
	}

	ngOnInit() {}

	private fillOutForm() {
		this.fg.patchValue({
			ficha: this.avaliacao.ficha,
			data: this.avaliacao.data,
			criterio: this.avaliacao.criterio,
		});
		this.fg.get("ficha").disable();
	}

	private getDto() {
		const dto = this.fg.getRawValue();
		const result = {
			avaliadorId: dto.avaliador ? dto.avaliador.id : null,
			data: dto.data,
			criterio: dto.criterio,
		};
		return result;
	}
}
