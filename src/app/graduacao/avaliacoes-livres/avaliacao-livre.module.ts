import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { AvaliacaoLivreListaComponent } from "./avaliacao-lista/avaliacao-lista.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { PersonagemMsModule } from "src/app/microservices/personagem/personagem-ms.module";
import { GraduacaoModule } from "src/app/graduacao/graduacao.module";
import { CriarAvaliacaoLivreModalComponent } from "./criar-avaliacao-modal/criar-avaliacao-modal.component";
import { AvaliacaoLivreDetalhesComponent } from "./avaliacao-detalhes/avaliacao-livre-detalhes.component";
import { EditarAvaliacaoLivreModalComponent } from "./editar-avaliacao-livre-modal/editar-avaliacao-livre-modal.component";
import { ModuleName } from "@base-core/modulo/modulo.model";

export const routes: Routes = [
	{
		path: "",
		data: { module: ModuleName.GRADUACAO },
		children: [
			{
				path: "",
				component: AvaliacaoLivreListaComponent,
			},
			{
				path: ":id",
				component: AvaliacaoLivreDetalhesComponent,
			},
		],
	},
];

@NgModule({
	declarations: [
		AvaliacaoLivreListaComponent,
		AvaliacaoLivreDetalhesComponent,
		EditarAvaliacaoLivreModalComponent,
		CriarAvaliacaoLivreModalComponent,
	],
	imports: [
		CommonModule,
		GraduacaoModule,
		GraduacaoMsModule,
		PersonagemMsModule,
		RouterModule.forChild(routes),
		BaseSharedModule,
	],
	entryComponents: [
		CriarAvaliacaoLivreModalComponent,
		EditarAvaliacaoLivreModalComponent,
	],
})
export class AvaliacoesLivreModule {}
