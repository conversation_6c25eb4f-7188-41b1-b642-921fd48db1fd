import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ChangeDetectorRef,
} from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";

import { tap, switchMap } from "rxjs/operators";
import { Observable, BehaviorSubject, zip } from "rxjs";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { ColaboradorService } from "src/app/microservices/personagem/colaborador/colaborador.service";
import { AtividadeGraduacaoService } from "src/app/microservices/graduacao/atividade-graduacao/atividade-graduacao.service";

@Component({
	selector: "pacto-criar-avaliacao-modal",
	templateUrl: "./criar-avaliacao-modal.component.html",
	styleUrls: ["./criar-avaliacao-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CriarAvaliacaoLivreModalComponent implements OnInit {
	@Input() stepNumber$: BehaviorSubject<number>;

	avaliadores = [];
	fichas = [];
	atividades = [];

	fg: FormGroup = new FormGroup({
		avaliador: new FormControl(null, Validators.required),
		data: new FormControl(null, [
			(form) => {
				const value = form.value;
				if (value !== false && value) {
					return null;
				} else {
					return { error: true };
				}
			},
		]),
		ficha: new FormControl(null, Validators.required),
		atividades: new FormControl(null, Validators.required),
	});

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private fichaService: FichaService,
		private atividadeService: AtividadeGraduacaoService,
		private colaboradorService: ColaboradorService
	) {}

	ngOnInit() {
		this.fetchData().subscribe();
		this.fg.get("avaliador").disable();
		this.fg.get("data").setValue(new Date().valueOf());
		this.setupEvents();
	}

	get step() {
		return this.stepNumber$.value;
	}

	get readyForNext() {
		const step = this.stepNumber$.value;
		if (step === 0) {
			const ficha = this.fg.get("ficha").valid;
			const avaliador = this.fg.get("avaliador").valid;
			const data = this.fg.get("data").valid;
			return avaliador && data && ficha;
		} else if (step === 1) {
			const value = this.fg.get("atividades").value;
			return value && value.length;
		}
	}

	nextHandler() {
		this.markAsTouched();
		if (!this.readyForNext) {
			return false;
		}
		if (this.step === 0) {
			this.goToStep2();
			this.cd.detectChanges();
		} else if (this.step === 1) {
			const dto = this.getDto();
			this.activeModal.close(dto);
		}
	}

	private goToStep2() {
		this.stepNumber$.next(1);
	}

	private markAsTouched() {
		this.fg.get("ficha").markAsTouched();
		this.fg.get("avaliador").markAsTouched();
		this.fg.get("data").markAsTouched();
	}

	private setupEvents() {
		this.fg.get("ficha").valueChanges.subscribe((ficha) => {
			if (ficha) {
				this.fg.get("avaliador").disable();
				this.fg.get("avaliador").setValue(null);
				const colaboradores$ = this.fichaService
					.obterColaboradoresDaFicha(ficha.id)
					.pipe(
						switchMap((ids) => {
							return this.colaboradorService.obterColaboradores({ ids });
						})
					);
				colaboradores$.subscribe((colaboradores) => {
					this.avaliadores = colaboradores;
					this.fg.get("avaliador").enable();
				});
			}
		});
	}

	private fetchData(): Observable<any> {
		const ficha$ = this.fichaService.obterFichas();
		const atividades$ = this.atividadeService.listarAtividades();
		return zip(ficha$, atividades$).pipe(
			tap((result) => {
				this.fichas = result[0];
				this.atividades = result[1];
			})
		);
	}

	private getDto() {
		const dto = this.fg.getRawValue();
		const ids = [];
		dto.atividades.forEach((atividade) => {
			ids.push(atividade.id);
		});
		return {
			fichaId: dto.ficha.id,
			avaliadorId: dto.avaliador.id,
			data: dto.data,
			atividadeIds: ids,
		};
	}
}
