<div class="body">
	<ng-container *ngIf="step === 0">
		<pacto-cat-form-select-filter
			[control]="fg.get('ficha')"
			[errorMsg]="'Selecione uma ficha'"
			[labelKey]="'nome'"
			[label]="'Ficha Técnica'"
			[options]="fichas"></pacto-cat-form-select-filter>

		<pacto-cat-form-select-filter
			[control]="fg.get('avaliador')"
			[errorMsg]="'Selecione um avaliador'"
			[imageKey]="'imageUri'"
			[labelKey]="'nome'"
			[label]="'Avaliador'"
			[options]="avaliadores"></pacto-cat-form-select-filter>

		<pacto-cat-form-datepicker
			[control]="fg.get('data')"
			[errorMsg]="'Selecione uma data valida'"
			[label]="'Data'"></pacto-cat-form-datepicker>
	</ng-container>

	<ng-container *ngIf="step === 1">
		<pacto-cat-form-multi-select-filter
			[control]="fg.get('atividades')"
			[imageKey]="'imageUri'"
			[labelKey]="'nome'"
			[label]="'Atividades'"
			[options]="atividades"></pacto-cat-form-multi-select-filter>
	</ng-container>
</div>

<div class="footer">
	<pacto-cat-button
		(click)="nextHandler()"
		[disabled]="!readyForNext"
		[full]="true"
		[label]="'PRÓXIMO'"
		[size]="'LARGE'"></pacto-cat-button>
</div>
