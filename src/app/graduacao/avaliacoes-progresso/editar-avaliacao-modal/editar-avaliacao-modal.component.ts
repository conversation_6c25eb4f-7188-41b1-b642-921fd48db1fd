import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { GraduacaoAvaliacaoProgresso } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { Colaborador } from "src/app/microservices/personagem/colaborador/colaborador.model";

@Component({
	selector: "pacto-editar-avaliacao-modal",
	templateUrl: "./editar-avaliacao-modal.component.html",
	styleUrls: ["./editar-avaliacao-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditarAvaliacaoModalComponent implements OnInit {
	colaboradores: Colaborador[] = [];
	avaliacao: GraduacaoAvaliacaoProgresso;

	fg: FormGroup = new FormGroup({
		ficha: new FormControl(null),
		avaliador: new FormControl(null, Validators.required),
		data: new FormControl(null, Validators.required),
		criterio: new FormControl(null),
	});

	constructor(private modal: NgbActiveModal, private cd: ChangeDetectorRef) {}

	get ready() {
		return this.fg.get("data").valid;
	}

	loadData(
		avaliacao: GraduacaoAvaliacaoProgresso,
		colaboradores: Colaborador[]
	) {
		this.avaliacao = avaliacao;
		this.colaboradores = colaboradores;
		this.fillOutForm();
		this.cd.detectChanges();
	}

	salvarHandler() {
		this.fg.get("data").markAsTouched();
		if (this.ready) {
			this.modal.close(this.getDto());
		}
	}

	ngOnInit() {}

	private fillOutForm() {
		const avaliador = this.colaboradores.find(
			(item) => item.id === this.avaliacao.avaliadorId
		);
		this.fg.patchValue({
			ficha: this.avaliacao.ficha,
			data: this.avaliacao.data,
			avaliador: avaliador ? avaliador : null,
			criterio: this.avaliacao.criterio,
		});
		this.fg.get("ficha").disable();
	}

	private getDto() {
		const dto = this.fg.getRawValue();
		const result = {
			avaliadorId: dto.avaliador ? dto.avaliador.id : null,
			data: dto.data,
			criterio: dto.criterio,
		};
		return result;
	}
}
