import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ChangeDetectorRef,
} from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";

import { tap, switchMap } from "rxjs/operators";
import { Observable, BehaviorSubject } from "rxjs";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { ColaboradorService } from "src/app/microservices/personagem/colaborador/colaborador.service";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";

@Component({
	selector: "pacto-criar-avaliacao-modal",
	templateUrl: "./criar-avaliacao-modal.component.html",
	styleUrls: ["./criar-avaliacao-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CriarAvaliacaoModalComponent implements OnInit {
	@Input() stepNumber$: BehaviorSubject<number>;

	avaliadores = [];
	fichas = [];
	niveis = [];
	atividadesNivel = [];

	fg: FormGroup = new FormGroup({
		avaliador: new FormControl(null, Validators.required),
		data: new FormControl(null, [
			(form) => {
				const value = form.value;
				if (value !== false && value) {
					return null;
				} else {
					return { error: true };
				}
			},
		]),
		ficha: new FormControl(null, Validators.required),
		nivel: new FormControl(null, Validators.required),
		criterio: new FormControl("DE_1_A_5"),
	});

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private nivelService: NivelService,
		private fichaService: FichaService,
		private colaboradorService: ColaboradorService
	) {}

	ngOnInit() {
		this.fetchData().subscribe();
		this.fg.get("avaliador").disable();
		this.fg.get("data").setValue(new Date().valueOf());
		this.setupEvents();
	}

	get step() {
		return this.stepNumber$.value;
	}

	get readyForNext() {
		const step = this.stepNumber$.value;
		if (step === 0) {
			const ficha = this.fg.get("ficha").valid;
			const avaliador = this.fg.get("avaliador").valid;
			const data = this.fg.get("data").valid;
			return avaliador && data && ficha;
		} else if (step === 1) {
			return this.fg.get("nivel").value;
		}
	}

	nextHandler() {
		this.markAsTouched();
		if (!this.readyForNext) {
			return false;
		}
		if (this.step === 0) {
			this.goToStep2();
		} else if (this.step === 1) {
			const dto = this.getDto();
			this.activeModal.close(dto);
		}
	}

	private goToStep2() {
		this.stepNumber$.next(1);
		this.niveis = [];
		this.fg.get("nivel").disable({ emitEvent: false });
		const id = this.fg.get("ficha").value.id;
		this.fichaService.obterFichaPorId(id).subscribe((ficha) => {
			this.niveis = ficha.niveis;
			this.fg.get("nivel").enable({ emitEvent: false });
			this.cd.detectChanges();
		});
	}

	private markAsTouched() {
		this.fg.get("ficha").markAsTouched();
		this.fg.get("avaliador").markAsTouched();
		this.fg.get("data").markAsTouched();
	}

	private setupEvents() {
		this.fg.get("ficha").valueChanges.subscribe((ficha) => {
			if (ficha) {
				this.fg.get("avaliador").disable();
				this.fg.get("avaliador").setValue(null);
				const colaboradores$ = this.fichaService
					.obterColaboradoresDaFicha(ficha.id)
					.pipe(
						switchMap((ids) => {
							return this.colaboradorService.obterColaboradores({ ids });
						})
					);
				colaboradores$.subscribe((colaboradores) => {
					this.avaliadores = colaboradores;
					this.fg.get("avaliador").enable();
				});
			}
		});

		this.fg.get("nivel").valueChanges.subscribe((nivel) => {
			if (nivel) {
				this.atividadesNivel = [];
				this.nivelService.obterNivelPorId(nivel.id).subscribe((result) => {
					this.atividadesNivel = result.atividades;
					this.cd.detectChanges();
				});
			}
		});
	}

	private fetchData(): Observable<any> {
		return this.fichaService
			.obterFichas()
			.pipe(tap((fichas) => (this.fichas = fichas)));
	}

	private getDto() {
		const dto = this.fg.getRawValue();
		return {
			fichaId: dto.ficha.id,
			avaliadorId: dto.avaliador.id,
			data: dto.data,
			criterio: dto.criterio,
			nivelId: dto.nivel.id,
		};
	}
}
