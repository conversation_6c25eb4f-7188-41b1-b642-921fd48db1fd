<div class="body div-criar-avaliacao-modal">
	<ng-container *ngIf="step === 0">
		<pacto-cat-form-select-filter
			[control]="fg.get('ficha')"
			[errorMsg]="'Selecione uma ficha'"
			[labelKey]="'nome'"
			[label]="'Ficha Técnica'"
			[options]="fichas"></pacto-cat-form-select-filter>

		<pacto-cat-form-select-filter
			[control]="fg.get('avaliador')"
			[errorMsg]="'Selecione um avaliador'"
			[imageKey]="'imageUri'"
			[labelKey]="'nome'"
			[label]="'Avaliador'"
			[options]="avaliadores"></pacto-cat-form-select-filter>

		<pacto-cat-form-datepicker
			[control]="fg.get('data')"
			[errorMsg]="'Selecione uma data valida'"
			[label]="'Data'"></pacto-cat-form-datepicker>

		<pacto-cat-form-select
			[control]="fg.get('criterio')"
			[items]="[
				{ id: 'DE_1_A_5', label: 'De 1 à 5' },
				{ id: 'SIM_NAO', label: 'Sim / Não' },
				{ id: 'CONCEITOS', label: 'Conceitos' }
			]"
			i18n-label="@@geral:criterio-de-avaliacao"
			label="'Critério de Avaliação'"></pacto-cat-form-select>
	</ng-container>

	<ng-container *ngIf="step === 1">
		<pacto-cat-form-select-filter
			[control]="fg.get('nivel')"
			[imageKey]="'fotoUri'"
			[labelKey]="'nome'"
			[label]="'Nível'"
			[options]="niveis"></pacto-cat-form-select-filter>

		<!-- EMPTY STATE -->
		<div *ngIf="!atividadesNivel.length" class="atividades-empty">
			<i class="pct pct-level"></i>
			<p class="msg">
				Nível selecionado irá definir quais atividades serão avaliadas
			</p>
		</div>

		<!-- ATIVIDADES DO NIVEL -->
		<div *ngIf="atividadesNivel.length" class="atividades-title">
			Atividades do Nível
		</div>
		<div
			*ngIf="atividadesNivel.length"
			[maxHeight]="'250px'"
			class="atividades"
			pactoCatSmoothScroll>
			<div *ngFor="let atividade of atividadesNivel" class="atividade">
				<pacto-cat-person-avatar
					[diameter]="34"
					[uri]="atividade.imageUri"></pacto-cat-person-avatar>
				<div class="nome cor-cinza04 type-p-small">{{ atividade.nome }}</div>
			</div>
		</div>
	</ng-container>
</div>

<div class="footer">
	<pacto-cat-button
		(click)="nextHandler()"
		*ngIf="step === 0"
		[disabled]="!readyForNext"
		[full]="true"
		[label]="'PRÓXIMO'"
		[size]="'LARGE'"></pacto-cat-button>

	<pacto-cat-button
		(click)="nextHandler()"
		*ngIf="step === 1"
		[disabled]="!readyForNext"
		[full]="true"
		[label]="'CONFIRMAR'"
		[size]="'LARGE'"></pacto-cat-button>
</div>
