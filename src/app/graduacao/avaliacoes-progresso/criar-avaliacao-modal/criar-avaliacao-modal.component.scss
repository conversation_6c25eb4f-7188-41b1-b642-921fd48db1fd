@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.body {
	padding: 0px 30px;
	min-height: 420px;
	display: flex;
	flex-direction: column;
}

.footer {
	border-top: 1px solid $cinza02;
	padding: 40px;
}

.atividades-empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: $cinza02;

	.pct {
		font-size: 48px;
	}

	.msg {
		margin: 15px 0px;
		@extend .type-h5;
		color: $cinza02;
		text-align: center;
	}
}

.atividades-title {
	color: $pretoPri;
	padding-left: 7.5px;
	@extend .type-h6;
}

.atividades {
	margin: 15px 0px;
}

.atividade {
	display: flex;
	align-items: center;
	line-height: 54px;
	padding-left: 7.5px;

	&:nth-child(2n + 1) {
		background-color: $cinza01;
	}

	.nome {
		padding: 0px 15px;
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.div-criar-avaliacao-modal {
		.nome {
			line-height: 22px;
			padding: 12px 15px;
		}
	}
}
