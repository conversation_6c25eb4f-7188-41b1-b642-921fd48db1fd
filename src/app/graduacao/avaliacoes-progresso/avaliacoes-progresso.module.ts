import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AvaliacaoListaComponent } from "./avaliacao-lista/avaliacao-lista.component";
import { AvaliacaoDetalhesComponent } from "./avaliacao-detalhes/avaliacao-detalhes.component";
import { EditarAvaliacaoModalComponent } from "./editar-avaliacao-modal/editar-avaliacao-modal.component";
import { CriarAvaliacaoModalComponent } from "src/app/graduacao/avaliacoes-progresso/criar-avaliacao-modal/criar-avaliacao-modal.component";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { PersonagemMsModule } from "src/app/microservices/personagem/personagem-ms.module";
import { GraduacaoModule } from "src/app/graduacao/graduacao.module";
import { ModuleName } from "@base-core/modulo/modulo.model";

export const routes: Routes = [
	{
		path: "",
		data: { module: ModuleName.GRADUACAO },
		children: [
			{
				path: "",
				component: AvaliacaoListaComponent,
			},
			{
				path: ":id",
				component: AvaliacaoDetalhesComponent,
			},
		],
	},
];

@NgModule({
	declarations: [
		AvaliacaoListaComponent,
		AvaliacaoDetalhesComponent,
		CriarAvaliacaoModalComponent,
		EditarAvaliacaoModalComponent,
	],
	imports: [
		CommonModule,
		GraduacaoModule,
		GraduacaoMsModule,
		PersonagemMsModule,
		RouterModule.forChild(routes),
		BaseSharedModule,
	],
	entryComponents: [
		EditarAvaliacaoModalComponent,
		CriarAvaliacaoModalComponent,
	],
})
export class AvaliacoesProgressoModule {}
