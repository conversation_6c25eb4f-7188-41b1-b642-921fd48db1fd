import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { ModalService } from "@base-core/modal/modal.service";
import { CriarAvaliacaoModalComponent } from "src/app/graduacao/avaliacoes-progresso/criar-avaliacao-modal/criar-avaliacao-modal.component";
import { AvaliacaoProgressoService } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.service";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";

declare var moment;

@Component({
	selector: "pacto-avaliacao-lista",
	templateUrl: "./avaliacao-lista.component.html",
	styleUrls: ["./avaliacao-lista.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoListaComponent implements OnInit {
	@ViewChild("add", { static: true }) add;
	@ViewChild("status", { static: true }) status;
	@ViewChild("tableData", { static: true }) table: RelatorioComponent;
	@ViewChild("ficha", { static: true }) ficha;
	@ViewChild("nivel", { static: true }) nivel;
	@ViewChild("colaboradorcolunm", { static: true }) colaboradorcolunm;

	tableConfig: PactoDataGridConfig;

	constructor(
		private snotify: SnotifyService,
		private modalService: ModalService,
		private restService: RestService,
		private router: Router,
		private gradAvaliacaoService: AvaliacaoProgressoService,
		public sessionService: SessionService
	) {}

	ngOnInit() {
		this.configTable();
	}

	rowClickHandler(item) {
		this.router.navigate(["graduacao", "avaliacoes-progresso", item.id]);
	}

	btnClickHandler() {
		const handle = this.modalService.open(
			"Criar Avaliação",
			CriarAvaliacaoModalComponent
		);
		handle.result.then(
			(res) => {
				this.gradAvaliacaoService
					.cadastrarAvaliacaoProgresso(res)
					.subscribe((ok) => {
						if (ok) {
							this.snotify.success(
								"Avaliação de progresso criada com sucesso."
							);
							this.router.navigate([
								"graduacao",
								"avaliacoes-progresso",
								ok.id,
							]);
							this.sessionService.notificarRecursoEmpresa(
								RecursoSistema.CRIOU_AVALIACAO_GRD
							);
						}
					});
			},
			() => {}
		);
	}

	private configTable() {
		this.tableConfig = new PactoDataGridConfig({
			endpointUrl: this.restService.buildFullUrlGraduacao(
				"avaliacoes-progresso"
			),
			quickSearch: true,
			rowClick: true,
			buttons: {
				id: "add",
				conteudo: this.add,
				nome: "add",
			},
			columns: [
				{
					nome: "ficha",
					titulo: "Ficha Técnica",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.ficha,
				},
				{
					nome: "nivel",
					titulo: "Nível",
					visible: true,
					defaultVisible: true,
					celula: this.nivel,
				},
				{
					nome: "qtdAlunos",
					titulo: "Qnt. Alunos",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "colaborador",
					titulo: "Avaliador",
					visible: true,
					ordenavel: false,
					celula: this.colaboradorcolunm,
				},
				{
					nome: "data",
					titulo: "Data da avaliação",
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					valueTransform: (v) => moment(v).format("L"),
					date: true,
				},
				{
					nome: "status",
					titulo: "Status",
					visible: true,
					defaultVisible: true,
					celula: this.status,
				},
			],
		});
	}
}
