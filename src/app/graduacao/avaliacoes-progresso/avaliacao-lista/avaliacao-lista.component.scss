@import "src/assets/scss/pacto/plataforma-import.scss";

.page-title {
	@extend .type-h2;
	color: $pretoPri;
	margin-bottom: 30px;
}

.avaliacao-status {
	display: flex;
	align-items: center;

	.marker {
		margin: 0px 10px;
		width: 20px;
		height: 20px;
		border-radius: 10px;

		&.concluida {
			background-color: $laranjinhaPri;
		}

		&.aberta {
			background-color: $verdinhoPri;
		}
	}
}

.nivel-wp {
	display: flex;
	align-items: center;

	.nome {
		padding: 0px 15px;
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.div-lista-avaliacoes-progresso {
		.page-title {
			font-size: 24px;
			text-align: center;
			margin-bottom: 12px;
		}

		::ng-deep pacto-relatorio {
			.pacto-table-title-block {
				padding: 12px;
				display: block;

				.search {
					display: block;
					margin-bottom: 12px;

					input {
						width: 100%;
					}
				}

				.actions {
					display: block;

					.div-actions-btn {
						margin-bottom: 12px;
						justify-content: space-between;

						.div-share-button .btn {
							margin: 0;
						}
					}

					.div-table-button {
						margin-bottom: 12px;

						.btn {
							margin-left: 0;
							width: 100%;
						}
					}
				}
			}

			.table-content {
				overflow: auto;

				.table {
					width: 1000px;
					max-width: 1000px;
				}

				.footer-row {
					display: block;

					.div-pagination {
						margin: 0;
						display: flex;
						justify-content: center;
					}

					.div-show-and-select {
						margin: 0;
						justify-content: space-between;

						> * {
							margin: 0;
						}
					}
				}
			}
		}
	}
}
