<pacto-cat-layout-v2>
	<div class="div-lista-avaliacoes-progresso">
		<div class="page-title" i18n="@@avaliacao-lista:titulo">
			Avaliações de Progresso
		</div>

		<div class="table-wrapper pacto-shadow">
			<pacto-relatorio
				#tableData
				(btnClick)="btnClickHandler()"
				(rowClick)="rowClickHandler($event)"
				[sessionService]="sessionService"
				[tableTitle]="'Avaliações'"
				[table]="tableConfig"
				telaId="avaliacoesDeProgresso "></pacto-relatorio>
		</div>
	</div>
</pacto-cat-layout-v2>

<ng-template #add>Adicionar</ng-template>
<!--Avaliador -->
<ng-template #colaboradorcolunm let-item="item">
	<div class="nivel-wp">
		<pacto-cat-person-avatar
			[diameter]="24"
			[uri]="item?.colaborador?.imageUri"></pacto-cat-person-avatar>
		<span class="nome">{{ item?.colaborador?.nome }}</span>
	</div>
</ng-template>

<!-- Nome da ficha -->
<ng-template #ficha let-item="item">{{ item.ficha.nome }}</ng-template>

<!-- Nivel -->
<ng-template #nivel let-item="item">
	<div class="nivel-wp">
		<pacto-cat-person-avatar
			[diameter]="24"
			[uri]="item.nivel.fotoUri"></pacto-cat-person-avatar>
		<span class="nome">{{ item.nivel.nome }}</span>
	</div>
</ng-template>

<ng-template #status let-item="item">
	<ng-container [ngSwitch]="item.status">
		<div *ngSwitchCase="'ABERTA'" class="avaliacao-status">
			<div class="marker aberta"></div>
			<div>Em Andamento</div>
		</div>
		<div *ngSwitchCase="'CONCLUIDA'" class="avaliacao-status">
			<div class="marker concluida"></div>
			<div>Concluída</div>
		</div>
	</ng-container>
</ng-template>
