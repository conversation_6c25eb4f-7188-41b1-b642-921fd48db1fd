<pacto-cat-layout-v2>
	<div class="div-avaliacao-progresso">
		<div class="pct-layout-header">
			<pacto-cat-migalhas>
				<a
					[routerLink]="['/graduacao/avaliacoes-progresso']"
					class="pct-migalha">
					Avaliacoes de Progresso
				</a>
			</pacto-cat-migalhas>

			<pacto-cat-page-title [link]="['/graduacao/avaliacoes-progresso']">
				<span class="pct-page-title">Avaliação de Progresso</span>
			</pacto-cat-page-title>
			<pacto-cat-button
				(click)="concluirHandler()"
				*ngIf="!avaliacaoDone"
				[label]="'CONCLUIR AVALIAÇÃO'"
				class="buttonCriar"></pacto-cat-button>

			<div [hidden]="true" class="btn-log">
				<pacto-log [url]="urlLog"></pacto-log>
			</div>
		</div>

		<div class="body">
			<pacto-cat-card-plain class="info">
				<!-- Row 1 -->
				<div class="first-row">
					<div class="avaliador">
						<div class="label" i18n="@@avaliacao-detalhes:avaliador">
							Avaliador
						</div>
						<div class="value">
							<pacto-cat-person-avatar
								[diameter]="32"
								[uri]="avaliador?.imageUri"></pacto-cat-person-avatar>
							<div class="block-value">{{ avaliador?.nome }}</div>
						</div>
					</div>
					<pacto-cat-button
						(click)="alterarHandler()"
						[disabled]="avaliacaoDone"
						[icon]="'edit-2'"
						[label]="'ALTERAR'"></pacto-cat-button>
				</div>

				<!-- Row 2 -->
				<div *ngIf="avaliacao" class="second-row">
					<div class="block">
						<div class="label">Data</div>
						<div class="block-value">
							{{ avaliacao.data | date : "shortDate" }}
						</div>
					</div>
					<div class="block">
						<div class="label" i18n="@@geral:criterio-de-avaliacao">
							Critério de Avaliação
						</div>
						<div [ngSwitch]="avaliacao.criterio" class="block-value">
							<span *ngSwitchCase="'DE_1_A_5'" i18n="@@geral:um-a-cinco">
								1 à 5
							</span>
							<span *ngSwitchCase="'SIM_NAO'">Sim ou Não</span>
							<span *ngSwitchCase="'CONCEITOS'" i18n="@@geral:conceitos">
								Conceitos
							</span>
						</div>
					</div>
				</div>

				<!-- Row 3 -->
				<div class="third-row">
					<div class="block">
						<div class="label">Ficha Técnica</div>
						<div class="block-value">{{ avaliacao?.ficha.nome }}</div>
					</div>
				</div>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain class="alunos">
				<div *ngIf="!alunos.length" class="empty">
					<i class="pct pct-users"></i>
					<p class="description">Nenhum aluno selecionado para ser avaliado.</p>
					<pacto-cat-button
						(click)="adicionarAlunosHandler()"
						[disabled]="avaliacaoDone"
						[icon]="'plus-circle'"
						i18n-label="@@avaliacao-detalhes:inserir-alunos"
						label="'INSERIR ALUNOS'"></pacto-cat-button>
				</div>

				<div *ngIf="alunos.length" class="list">
					<div class="upper-row">
						<div
							class="type-h6 cor-cinza04"
							i18n="@@avaliacao-detalhes:alunos-serao-avaliados">
							Alunos que serão avaliados
						</div>
						<pacto-cat-button
							(click)="adicionarAlunosHandler()"
							[disabled]="avaliacaoDone"
							[icon]="'plus-circle'"
							i18n-label="@@avaliacao-detalhes:inserir-alunos"
							label="'INSERIR ALUNOS'"></pacto-cat-button>
					</div>
					<div
						[maxHeight]="'535px'"
						class="scroll-wrapper"
						pactoCatSmoothScroll>
						<div *ngFor="let aluno of alunos" class="aluno">
							<pacto-cat-person-avatar
								[diameter]="32"
								[uri]="aluno.imageUri"></pacto-cat-person-avatar>
							<div class="nome type-h6 cor-preto04">
								<div class="aluno-nome">{{ aluno.nome }}</div>
								<div class="aluno-aulas">
									{{ aulasRealizadas(aluno) }}
									<span i18n="@@avaliacao-detalhes:aulas-realizadas">
										aulas realizadas
									</span>
									<span class="barra-divisor">/</span>
									{{ aulasRestantes(aluno) }}
									<span i18n="@@avaliacao-detalhes:aulas-restantes">
										aulas restantes
									</span>
								</div>
							</div>
							<pacto-cat-button
								(click)="avaliarHandler(aluno)"
								*ngIf="!avaliacoesAlunos[aluno.id]"
								[icon]="'evaluation'"
								[label]="'avaliar'"
								[size]="'SMALL'"
								[type]="'DARK'"></pacto-cat-button>
							<pacto-cat-button
								(click)="verAvaliacaoHandler(aluno)"
								*ngIf="avaliacoesAlunos[aluno.id]"
								[icon]="'evaluation'"
								[label]="'avaliado'"
								[size]="'SMALL'"
								[type]="'OUTLINE'"></pacto-cat-button>
							<i
								(click)="removerAlunoHandler(aluno)"
								*ngIf="!avaliacaoDone"
								class="pct pct-trash-2"></i>
						</div>
					</div>

					<div *ngIf="alunos.length" class="div-btn-inicar-avaliacao">
						<pacto-cat-button
							(click)="iniciarAvaliacao()"
							[disabled]="avaliacaoDone"
							i18n-label="@@avaliacao-detalhes:iniciar-avaliacao"
							label="'INICIAR AVALIAÇÃO'"></pacto-cat-button>
					</div>
				</div>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain class="nivel">
				<div class="nivel-block d-flex justify-content-between">
					<div class="nivel-titulo">
						<div class="label">Nível</div>
						<div class="d-flex">
							<pacto-cat-person-avatar
								[diameter]="34"
								[uri]="avaliacao?.nivel.fotoUri"></pacto-cat-person-avatar>
							<div class="block-value">
								{{ avaliacao?.nivel.nome }}
							</div>
						</div>
					</div>
					<div>
						<div
							class="quantidade-aulas-titulo"
							i18n="@@avaliacao-detalhes:aulas-exigidas">
							Aulas exigidas
						</div>
						<div class="quantidade-aulas-valor">
							{{
								avaliacao?.nivel.quantidadeMinimaAulas === undefined
									? 0
									: avaliacao?.nivel.quantidadeMinimaAulas
							}}
						</div>
					</div>
				</div>

				<div class="label" i18n="@@avaliacao-detalhes:atividades-do-nivel">
					Atividades do Nível
				</div>
				<div [maxHeight]="'200px'" class="atividades" pactoCatSmoothScroll>
					<div *ngFor="let atividade of nivel?.atividades" class="atividade">
						<pacto-cat-person-avatar
							[diameter]="34"
							[uri]="atividade.imageUri"></pacto-cat-person-avatar>
						<div class="value">{{ atividade.nome }}</div>
					</div>
				</div>
			</pacto-cat-card-plain>
		</div>

		<div class="footer">
			<pacto-cat-button
				*ngIf="avaliacaoDone"
				[label]="'voltar'"
				[routerLink]="['/graduacao/avaliacoes-progresso']"></pacto-cat-button>
		</div>
	</div>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #warningMsg>
	<span xingling="warningMsg">
		Ainda há avaliações individuais pendentes, finalize elas para poder concluir
		a avaliação de progresso.
	</span>
</pacto-traducoes-xingling>
