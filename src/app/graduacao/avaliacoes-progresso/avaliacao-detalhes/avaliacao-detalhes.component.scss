@import "src/assets/scss/pacto/plataforma-import.scss";

.body {
	display: grid;
	grid-template-columns: 5fr 7fr;
	grid-template-rows: 260px 390px;
	gap: 30px;
}

.alunos {
	grid-row: 1 / 3;
	grid-column: 2;
}

.footer {
	display: flex;
	flex-direction: row-reverse;
	margin: 30px 0px;
}

@media (max-width: $plataforma-breakpoint-small) {
	.body {
		grid-template-columns: 1fr;
		grid-template-rows: 260px 390px 680px;
	}

	.info {
		grid-row: 1 / 1;
		grid-column: 1;
	}

	.alunos {
		grid-row: 3 / 3;
		grid-column: 1;
	}

	.nivel {
		grid-row: 2 / 2;
		grid-column: 1;
	}
}

.label {
	@extend .type-h6;
	margin-bottom: 6px;
	color: $cinza04;
}

.block-value {
	@extend .type-h5-bold;
	color: $pretoPri;
}

.quantidade-aulas-titulo {
	margin-top: -12px;
	@extend .type-h6;
	color: $cinza04;
}

.quantidade-aulas-valor {
	font-family: "Nunito Sans", sans-serif;
	font-weight: 600;
	font-size: 20px;
	color: $pretoPri;
}

// INFO BLOCK
.info {
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.first-row {
		display: flex;
		align-items: flex-start;

		.avaliador {
			flex-grow: 1;
		}

		.avaliador .value {
			display: flex;
			align-items: center;
		}

		pacto-cat-person-avatar {
			margin-right: 15px;
		}
	}

	.second-row,
	.third-row {
		display: flex;
	}

	.block {
		flex-basis: 50%;
		flex-grow: 1;
	}
}

// NIVEL BLOCK
.nivel {
	display: flex;
	flex-direction: column;

	.nivel-titulo {
		padding-right: 12px;
	}

	.nivel-block {
		display: flex;
		align-items: center;
		margin-bottom: 15px;

		.block-value {
			margin-left: 15px;
		}
	}

	.atividades {
		margin-top: 15px;
	}

	.atividade {
		display: flex;
		align-items: center;
		line-height: 54px;
		padding-left: 15px;

		pacto-cat-person-avatar {
			margin-right: 15px;
		}

		&:nth-child(2n + 1) {
			background-color: $cinza01;
		}
	}
}

// ALUNOS BLOCK
.alunos {
	.empty {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
	}

	.description {
		@extend .type-h5;
		text-align: center;
		width: 300px;
		margin: 15px 0px;
		color: $cinza02;
	}

	.pct {
		font-size: 48px;
		color: $cinza02;
	}

	.list .upper-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 7.5px;
	}

	.list .aluno {
		display: flex;
		align-items: center;
		padding: 0px 8px;

		&:nth-child(2n + 1) {
			background-color: $cinza01;
		}

		pacto-cat-person-avatar {
			margin-right: 7.5px;
		}

		.nome {
			flex-grow: 1;
			padding: 12px 0;
		}

		.pct {
			font-size: 18px;
			color: $hellboyPri;
			cursor: pointer;
			padding: 0px 25px 0px 15px;
		}

		.aluno-aulas {
			color: $cinzaPri;
			font-size: 12px;
		}

		.barra-divisor {
			margin: 0 4px;
		}
	}

	.div-btn-inicar-avaliacao {
		display: flex;
		justify-content: center;
		margin-top: 20px;

		::ng-deep pacto-cat-button {
			button {
				width: 178px;
			}
		}
	}
}

.btn-log {
	text-align: right;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.div-avaliacao-progresso {
		.pct-layout-header {
			margin-bottom: 20px;

			pacto-cat-page-title {
				margin-bottom: 16px;

				.pct-arrow-left {
					font-size: 22px;
				}

				.pct-page-title {
					font-size: 22px;
				}
			}
		}

		.body {
			grid-template-rows: 324px 400px 500px;

			pacto-cat-card-plain {
				padding: 12px;
				width: 70vw;

				.block-value {
					font-size: 16px;
				}

				.first-row {
					display: block;

					::ng-deep pacto-cat-button {
						button {
							width: 100%;
							margin-top: 12px;
						}
					}
				}

				.second-row {
					display: block;

					:first-child {
						margin-bottom: 12px;
					}
				}
			}

			::ng-deep .nivel {
				.nivel-block {
					display: block !important;

					.nivel-titulo {
						margin-bottom: 20px;

						.block-value {
							font-size: 16px;
						}
					}
				}

				.scroll-content {
					width: 1000px;
				}
			}

			::ng-deep .list {
				.upper-row {
					display: block;

					pacto-cat-button {
						.pacto-button {
							width: 100%;
							margin: 12px 0;
						}
					}
				}

				.scroll-wrapper {
					.scroll-content {
						.aluno {
							width: 500px;
						}
					}
				}
			}
		}
	}
}

::ng-deep pacto-cat-page-title {
	display: inline-block !important;
}

.buttonCriar {
	float: right;
}
