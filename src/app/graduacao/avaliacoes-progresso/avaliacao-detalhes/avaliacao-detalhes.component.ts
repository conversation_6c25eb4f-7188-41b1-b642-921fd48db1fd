import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";

import { Observable, zip, of } from "rxjs";
import { tap, switchMap } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";

import { SelectAlunoModalComponent } from "../../components/select-aluno-modal/select-aluno-modal.component";
import { EditarAvaliacaoModalComponent } from "src/app/graduacao/avaliacoes-progresso/editar-avaliacao-modal/editar-avaliacao-modal.component";
import { AvaliarAlunoModalComponent } from "../../components/avaliar-aluno-modal/avaliar-aluno-modal.component";
import { ConcluirAvaliacaoComponent } from "src/app/graduacao/components/concluir-avaliacao/concluir-avaliacao.component";
import { GraduacaoAvaliacaoTipo } from "src/app/microservices/graduacao/graduacao.model";

import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { ColaboradorService } from "src/app/microservices/personagem/colaborador/colaborador.service";
import { AvaliacaoProgressoAluno } from "src/app/microservices/graduacao/avaliacao-progresso-aluno/avaliacao-progresso-aluno.model";
import {
	GraduacaoAvaliacaoProgresso,
	AvaliacaoProgressoStatus,
} from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import {
	Nivel,
	NivelSimple,
} from "src/app/microservices/graduacao/nivel/nivel.model";
import {
	ModalService,
	PactoModalSize,
} from "src/app/base/base-core/modal/modal.service";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { AvaliacaoProgressoService } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.service";
import {
	AvaliacaoProgressoAlunoService,
	RespostaAvaliacaoProgressoDto,
} from "src/app/microservices/graduacao/avaliacao-progresso-aluno/avaliacao-progresso-aluno.service";
import { ClienteService } from "src/app/microservices/personagem/cliente/cliente.service";
import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { AvaliacaoGraduacaoCriterio } from "src/app/microservices/graduacao/avaliacao-progresso/avaliacao.model";
import { SessionService } from "@base-core/client/session.service";
import { RecursoSistema } from "../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { TraducoesXinglingComponent } from "ui-kit";
import { IncluirAlunosFichaModalComponent } from "../../components/incluir-alunos-ficha-modal/incluir-alunos-ficha-modal.component";
import { RestService } from "@base-core/rest/rest.service";
import { AvaliarTodosAlunosModalComponent } from "../../components/avaliar-todos-alunos-modal/avaliar-todos-alunos-modal.component";
import { AtividadeGraduacao } from "../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";

@Component({
	selector: "pacto-avaliacao-detalhes",
	templateUrl: "./avaliacao-detalhes.component.html",
	styleUrls: ["./avaliacao-detalhes.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvaliacaoDetalhesComponent implements OnInit {
	@ViewChild("warningMsg", { static: true })
	warningMsg: TraducoesXinglingComponent;
	avaliador: any = {};
	alunos: any[] = [];
	alunosJaAdicionados: any[] = [];
	avaliacao: GraduacaoAvaliacaoProgresso;
	ficha: Ficha;
	nivel: Nivel;
	niveisDosAlunos: { [alunoId: number]: NivelSimple };
	avaliacoesAlunos: { [alunoId: number]: AvaliacaoProgressoAluno } = {};
	quantidadeAlunosAvaliados = 0;

	constructor(
		private route: ActivatedRoute,
		private snotify: SnotifyService,
		private nivelService: NivelService,
		private modal: ModalService,
		private cd: ChangeDetectorRef,
		private colaboradorService: ColaboradorService,
		private avaliacaoProgresoService: AvaliacaoProgressoService,
		private avaliacaoProgressoAlunoService: AvaliacaoProgressoAlunoService,
		private fichaService: FichaService,
		private rest: RestService,
		private clientService: ClienteService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.loadData().subscribe(() => {});
	}

	get avaliacaoDone() {
		return (
			this.avaliacao &&
			this.avaliacao.status === AvaliacaoProgressoStatus.CONCLUIDA
		);
	}

	aulasRealizadas(aluno) {
		return aluno.aulasRealizadas === undefined ? 0 : aluno.aulasRealizadas;
	}

	aulasRestantes(aluno) {
		return aluno.aulasRestantes === undefined || aluno.aulasRestantes <= 0
			? 0
			: aluno.aulasRestantes;
	}

	removerAlunoHandler(aluno) {
		const id = this.avaliacao.id;
		this.avaliacaoProgresoService
			.removerAlunos(id, [aluno.id])
			.subscribe(() => {
				this.loadData().subscribe(() => {
					this.snotify.success("Aluno removido com sucesso.");
				});
			});
	}

	concluirHandler() {
		this.quantidadeAlunosAvaliados = 0;
		for (let i = 0; this.alunos.length > i; i++) {
			if (
				this.avaliacoesAlunos[this.alunos[i].id] !== undefined &&
				this.avaliacoesAlunos[this.alunos[i].id] !== null
			) {
				if (this.avaliacoesAlunos[this.alunos[i].id].alunoId) {
					this.quantidadeAlunosAvaliados++;
				}
			}
		}
		if (this.quantidadeAlunosAvaliados === this.alunos.length) {
			const ref = this.modal.open(
				"Conclusão de avaliação de progresso",
				ConcluirAvaliacaoComponent,
				PactoModalSize.LARGE
			);
			const modal: ConcluirAvaliacaoComponent = ref.componentInstance;
			const niveis = Object.assign([], this.ficha.niveis);
			const alunosNiveis = [];
			this.alunos.forEach((aluno) => {
				alunosNiveis.push({
					aluno: Object.assign({}, aluno),
					nivelSugeridoId: this.nivel.id,
					nivelAtualId: this.niveisDosAlunos[aluno.id]
						? this.niveisDosAlunos[aluno.id].id
						: null,
				});
			});
			modal.loadData(
				alunosNiveis,
				niveis,
				GraduacaoAvaliacaoTipo.PROGRESSO,
				this.avaliacao.id,
				this.avaliacao.data
			);
			ref.result.then(
				() => {
					this.loadData().subscribe();
				},
				() => {}
			);
		} else {
			this.snotify.warning(this.warningMsg.getLabel("warningMsg"));
		}
	}

	adicionarAlunosHandler() {
		if (!this.avaliacaoDone) {
			const classSizeModal =
				window.innerWidth < 580
					? "modal-incluir-aluno-avaliacao-progresso modal-sm"
					: "modal-mxl";
			const modalRef = this.modal.open(
				"Adicionar alunos",
				IncluirAlunosFichaModalComponent,
				PactoModalSize.LARGE,
				classSizeModal
			);
			const modal: IncluirAlunosFichaModalComponent =
				modalRef.componentInstance;
			modal.loadData(this.ficha, this.nivel.id, this.avaliacao.id, "avaliacao");

			modalRef.result.then(
				(retorno) => {
					if (retorno.alunos === "todos") {
						this.avaliacaoProgresoService
							.inserirAlunosTodos(this.avaliacao.id, retorno.filtros)
							.subscribe(() => {
								this.snotify.success(`Alunos inseridos com sucesso.`);
								this.loadData().subscribe();
								this.sessionService.notificarRecursoEmpresa(
									RecursoSistema.INCLUIU_ALUNO_AVALIACAO_PROGRESSO_TROCA_NIVEL_GRD
								);
							});
					} else {
						this.avaliacaoProgresoService
							.inserirAlunos(this.avaliacao.id, retorno.marcados)
							.subscribe(() => {
								this.snotify.success(
									`${retorno.marcados.length} aluno(s) inseridos com sucesso.`
								);
								this.loadData().subscribe();
								this.sessionService.notificarRecursoEmpresa(
									RecursoSistema.INCLUIU_ALUNO_AVALIACAO_PROGRESSO_TROCA_NIVEL_GRD
								);
							});
					}
				},
				() => {}
			);
		}
	}

	alterarHandler() {
		if (this.avaliacaoDone) {
			return true;
		}
		const ref = this.modal.open(
			"Alterar Avaliação",
			EditarAvaliacaoModalComponent
		);
		const modal: EditarAvaliacaoModalComponent = ref.componentInstance;
		const colaboradoresFicha$ = this.fichaService
			.obterColaboradoresDaFicha(this.avaliacao.ficha.id)
			.pipe(
				switchMap((ids) => {
					return this.colaboradorService.obterColaboradores({ ids });
				})
			);

		colaboradoresFicha$.subscribe((colaboradores) => {
			modal.loadData(this.avaliacao, colaboradores);
		});

		ref.result.then(
			(dto) => {
				this.avaliacaoProgresoService
					.editarAvaliacao(this.avaliacao.id, dto)
					.subscribe((response) => {
						if (response.error) {
							this.snotify.error(response.message);
						} else {
							this.snotify.success("Avaliação de Progresso Alterada");
							this.loadData().subscribe(() => {
								this.cd.detectChanges();
							});
						}
					});
			},
			() => {}
		);
	}

	verAvaliacaoHandler(aluno: Cliente) {
		/**
		 * CARREGAR RESPOSTAS
		 */
		const avaliacaoAlunoId = this.avaliacoesAlunos[aluno.id].id;
		this.avaliacaoProgressoAlunoService
			.obterRespostasAvaliacaoAluno(avaliacaoAlunoId)
			.subscribe((respostasDto) => {
				const ref = this.modal.openCustomFullscreen(AvaliarAlunoModalComponent);
				const modal: AvaliarAlunoModalComponent = ref.componentInstance;

				const atividades = respostasDto.respostas.map((resposta) =>
					Object.assign({}, resposta.atividade)
				);
				modal.loadData(
					this.avaliador,
					aluno,
					this.avaliacao,
					this.avaliacaoDone
						? this.montarPerguntasRespondidas(respostasDto)
						: this.unirPerguntasERespostas(
								respostasDto.atividades,
								this.nivel.atividades
						  ),
					respostasDto.respostas,
					this.avaliacao.criterio || AvaliacaoGraduacaoCriterio.DE_1_A_5,
					!this.avaliacaoDone,
					this.avaliacoesAlunos[aluno.id].id,
					this.avaliacoesAlunos[aluno.id].observacaoGeral,
					this.ficha.id
				);
				ref.result.then(
					(dto) => {
						const id = dto.avaliacaoProgressoId;
						delete dto.avaliacaoProgressoId;
						this.avaliacaoProgressoAlunoService
							.editarAvaliacaoProgressoEmGrupo(id, dto)
							.subscribe(() => {
								this.snotify.success("Avaliação Editada com sucesso");
								this.loadData().subscribe(() => {});
							});
					},
					() => {}
				);
			});
	}

	// Se a AV foi concluída não terá a possibilidade de responder possíveis novas atividades inseridas no nível;
	private montarPerguntasRespondidas(
		respostaDTO: RespostaAvaliacaoProgressoDto
	) {
		const listJoin: AtividadeGraduacao[] = [];
		respostaDTO.respostas.forEach((item) => {
			if (!listJoin.find((x) => x.id === item.atividade.id)) {
				listJoin.push(item.atividade);
			}
		});

		return listJoin;
	}

	/*
	 * Se a AV ainda não foi concluída mas já respondida,
	 * tem a possibilidade de responder novas atividades que foram inseridas no nível
	 */
	private unirPerguntasERespostas(
		respostasAtividades: AtividadeGraduacao[],
		nivelAtividadesAtuais: AtividadeGraduacao[]
	) {
		const listJoin: AtividadeGraduacao[] = [];
		respostasAtividades.forEach((item) => {
			listJoin.push(item);
		});

		nivelAtividadesAtuais.forEach((item) => {
			if (!listJoin.find((x) => x.id === item.id)) {
				listJoin.push(item);
			}
		});

		return listJoin;
	}

	avaliarHandler(aluno: Cliente) {
		const ref = this.modal.openCustomFullscreen(
			AvaliarAlunoModalComponent,
			"avaliacao-progresso-aluno"
		);
		const modal: AvaliarAlunoModalComponent = ref.componentInstance;
		modal.loadData(
			this.avaliador,
			aluno,
			this.avaliacao,
			this.nivel.atividades,
			[],
			this.avaliacao.criterio,
			true,
			null,
			null,
			this.ficha.id
		);
		ref.result.then(
			(dto) => {
				this.avaliacaoProgressoAlunoService.cadastrar(dto).subscribe(() => {
					this.snotify.success("Aluno Avaliado com Sucesso");
					this.loadData().subscribe(() => {
						this.cd.detectChanges();
					});
				});
			},
			() => {}
		);
	}

	private loadData(): Observable<any> {
		const id = this.route.snapshot.params.id;
		const avaliacao$ = this.avaliacaoProgresoService
			.buscarAvaliacaoProgresso(id)
			.pipe(
				switchMap((avaliacao) => {
					this.avaliacao = avaliacao;
					const nivel$ = this.nivelService.obterNivelPorId(avaliacao.nivel.id);
					const ficha$ = this.fichaService.obterFichaPorId(avaliacao.ficha.id);
					const avaliador$ = this.colaboradorService.obterColaborador(
						avaliacao.avaliadorId
					);
					const niveisAlunos$ = this.fichaService.obterNiveisDosAlunos(
						avaliacao.ficha.id
					);
					return zip(nivel$, ficha$, avaliador$, niveisAlunos$);
				}),
				tap((result) => {
					this.nivel = result[0];
					this.ficha = result[1];
					this.avaliador = result[2];
					this.niveisDosAlunos = result[3] ? result[3] : {};
				})
			);
		const alunos$ = this.avaliacaoProgresoService
			.obterAlunosAvaliacaoProgresso(id)
			.pipe(
				tap((clientes) => {
					this.alunos = clientes;
				})
			);
		const avaliacoesAlunos$ = this.avaliacaoProgressoAlunoService
			.listar({ avaliacaoProgressoId: id })
			.pipe(
				tap((avaliacoes) => {
					this.avaliacoesAlunos = this.convertIntoObject(avaliacoes);
				})
			);
		return zip(avaliacao$, alunos$, avaliacoesAlunos$).pipe(
			tap(() => {
				this.cd.detectChanges();
			})
		);
	}

	private convertIntoObject(data: AvaliacaoProgressoAluno[]) {
		const result = {};
		data.forEach((item) => {
			result[item.alunoId] = item;
		});
		return result;
	}

	get urlLog() {
		if (this.avaliacao) {
			return this.rest.buildFullUrlGraduacao(
				`log/avaliacoes-progresso/${this.avaliacao.id}`
			);
		}
	}

	iniciarAvaliacao() {
		if (!this.avaliacaoDone) {
			if (this.existeAlunoAvaliado()) {
				this.editarAvaliacaoEmGrupo();
			} else {
				this.novaAvaliacaoEmGrupo();
			}
		}
	}

	existeAlunoAvaliado() {
		for (let i = 0; this.alunos.length > i; i++) {
			if (
				this.avaliacoesAlunos[this.alunos[i].id] !== undefined &&
				this.avaliacoesAlunos[this.alunos[i].id] !== null
			) {
				if (this.avaliacoesAlunos[this.alunos[i].id].alunoId) {
					return true;
				}
			}
		}
		return false;
	}

	novaAvaliacaoEmGrupo() {
		const ref = this.modal.openCustomFullscreen(
			AvaliarTodosAlunosModalComponent,
			"avaliacao-progresso-aluno"
		);
		const modal: AvaliarTodosAlunosModalComponent = ref.componentInstance;
		modal.loadData(
			this.avaliador,
			this.alunos,
			this.avaliacao,
			this.nivel.atividades,
			[],
			this.avaliacao.criterio,
			true,
			null,
			null,
			[]
		);
		ref.result.then(
			(dto) => {
				this.avaliacaoProgressoAlunoService
					.cadastrarAvaliacaoProgressoEmGrupo(dto)
					.subscribe(() => {
						this.snotify.success("Avaliação em grupo realizada com sucesso");
						this.loadData().subscribe(() => {
							this.cd.detectChanges();
						});
					});
			},
			() => {}
		);
	}

	editarAvaliacaoEmGrupo() {
		this.avaliacaoProgressoAlunoService
			.obterRespostasAvaliacaoEmGrupo(this.avaliacao.id)
			.subscribe((respostasDto) => {
				const ref = this.modal.openCustomFullscreen(
					AvaliarTodosAlunosModalComponent,
					"avaliacao-progresso-aluno"
				);
				const modal: AvaliarTodosAlunosModalComponent = ref.componentInstance;
				modal.loadData(
					this.avaliador,
					this.alunos,
					this.avaliacao,
					this.nivel.atividades,
					[],
					this.avaliacao.criterio,
					!this.avaliacaoDone,
					null,
					respostasDto.observacaoGeral,
					respostasDto.respostas
				);
				ref.result.then(
					(dto) => {
						const id = dto.avaliacaoProgressoId;
						this.avaliacaoProgressoAlunoService
							.editarAvaliacaoProgressoEmGrupo(id, dto)
							.subscribe(() => {
								this.snotify.success("Avaliação em grupo editada com sucesso");
								this.loadData().subscribe(() => {
									this.cd.detectChanges();
								});
							});
					},
					() => {}
				);
			});
	}
}
