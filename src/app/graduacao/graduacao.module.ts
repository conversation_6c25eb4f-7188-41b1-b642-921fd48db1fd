import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { GraduacaoMsModule } from "../microservices/graduacao/graduacao-ms.module";
import { PersonagemMsModule } from "../microservices/personagem/personagem-ms.module";
import { SelectAlunoModalComponent } from "./components/select-aluno-modal/select-aluno-modal.component";
import { AvaliarAlunoModalComponent } from "./components/avaliar-aluno-modal/avaliar-aluno-modal.component";
import { AvaliarAlunoModalRespostaComponent } from "./components/avaliar-aluno-modal-resposta/avaliar-aluno-modal-resposta.component";
import { AvaliarAlunoModalAlertComponent } from "./components/avaliar-aluno-modal-alert/avaliar-aluno-modal-alert.component";
import { ConcluirAvaliacaoComponent } from "./components/concluir-avaliacao/concluir-avaliacao.component";
import { IncluirAlunosFichaModalComponent } from "./components/incluir-alunos-ficha-modal/incluir-alunos-ficha-modal.component";
import { AlunoNivelCardComponent } from "./components/aluno-nivel-card/aluno-nivel-card.component";
import { CriarComentarioModalComponent } from "./components/criar-comentario-modal/criar-comentario-modal.component";
import { AvaliacaoAlunoPrintComponent } from "./components/avaliacao-aluno-print/avaliacao-aluno-print.component";
import { AtividadeEditModalComponent } from "./atividade-graduacao/components/atividade-edit/atividade-edit-modal.component";
import { IncluirAlunosNivelModalComponent } from "./components/incluir-alunos-nivel-modal/incluir-alunos-nivel-modal.component";
import { SubAtividadeEditComponent } from "./atividade-graduacao/components/sub-atividade-edit/sub-atividade-edit.component";
import { SubAtividadeDeleteComponent } from "./atividade-graduacao/components/sub-atividade-delete/sub-atividade-delete.component";
import { AvaliarTodosAlunosModalComponent } from "./components/avaliar-todos-alunos-modal/avaliar-todos-alunos-modal.component";
import { AvaliarTodosAlunosModalRespostaComponent } from "./components/avaliar-todos-alunos-modal-resposta/avaliar-todos-alunos-modal-resposta.component";
import { AvaliacaoProgressoShareButtonComponent } from "./components/avaliacao-progresso-share-button/avaliacao-progresso-share-button.component";
import { TranslateModule } from "@ngx-translate/core";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { HomeComponent } from "./home/<USER>";
import { HomePageModule } from "pacto-layout";

@NgModule({
	declarations: [
		AvaliarAlunoModalComponent,
		AvaliarTodosAlunosModalComponent,
		AvaliacaoAlunoPrintComponent,
		AvaliarAlunoModalRespostaComponent,
		AvaliarTodosAlunosModalRespostaComponent,
		SelectAlunoModalComponent,
		AvaliarAlunoModalAlertComponent,
		ConcluirAvaliacaoComponent,
		IncluirAlunosFichaModalComponent,
		AlunoNivelCardComponent,
		AtividadeEditModalComponent,
		SubAtividadeEditComponent,
		SubAtividadeDeleteComponent,
		CriarComentarioModalComponent,
		IncluirAlunosNivelModalComponent,
		AvaliacaoProgressoShareButtonComponent,
		HomeComponent,
	],
	exports: [
		AvaliarAlunoModalComponent,
		AvaliarTodosAlunosModalComponent,
		AvaliarAlunoModalRespostaComponent,
		AvaliarTodosAlunosModalRespostaComponent,
		SelectAlunoModalComponent,
		AvaliarAlunoModalAlertComponent,
		ConcluirAvaliacaoComponent,
		IncluirAlunosFichaModalComponent,
		IncluirAlunosNivelModalComponent,
		CriarComentarioModalComponent,
		AtividadeEditModalComponent,
		SubAtividadeEditComponent,
		SubAtividadeDeleteComponent,
		AlunoNivelCardComponent,
		BaseSharedModule,
	],
	imports: [
		CommonModule,
		RouterModule.forChild([]),
		BaseSharedModule,
		GraduacaoMsModule,
		PersonagemMsModule,
		TranslateModule,
		DragDropModule,
		HomePageModule,
	],
	entryComponents: [
		IncluirAlunosFichaModalComponent,
		IncluirAlunosNivelModalComponent,
		AvaliarAlunoModalAlertComponent,
		CriarComentarioModalComponent,
		ConcluirAvaliacaoComponent,
		AvaliarAlunoModalComponent,
		AvaliarTodosAlunosModalComponent,
		AtividadeEditModalComponent,
		SubAtividadeEditComponent,
		SubAtividadeDeleteComponent,
		SelectAlunoModalComponent,
	],
})
export class GraduacaoModule {}
