import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Input,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { BehaviorSubject } from "rxjs";

import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { FichaService } from "../../../../microservices/graduacao/ficha/ficha.service";
import { ColaboradorService } from "../../../../microservices/personagem/colaborador/colaborador.service";
import { TreinoApiColaboradorService } from "treino-api";

@Component({
	selector: "pacto-ficha-edit-modal",
	templateUrl: "./ficha-edit-modal.component.html",
	styleUrls: ["./ficha-edit-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FichaEditModalComponent implements OnInit {
	@Input() stepNumber$: BehaviorSubject<number>;
	private ficha: Ficha;
	formGroup: FormGroup = new FormGroup({
		nome: new FormControl(null, [Validators.required, Validators.minLength(3)]),
		colaboradores: new FormControl(null),
		modalidades: new FormControl(null),
	});

	constructor(
		private cd: ChangeDetectorRef,
		private colaboradorService: ColaboradorService,
		private fichaService: FichaService,
		private colaboradorServiceTreinoApi: TreinoApiColaboradorService,
		private modal: NgbActiveModal
	) {}

	colaboradores: any[];
	modalidades: any[];

	ngOnInit() {
		this.stepNumber$.subscribe((step) => {
			console.log(step);
		});
	}

	saveHandler() {
		const controls = this.formGroup.controls;
		for (const control in controls) {
			if (controls.hasOwnProperty(control)) {
				controls[control].markAsTouched();
			}
		}

		if (this.valid) {
			const dto = this.getDto();
			if (this.ficha) {
				this.modal.close({
					dto,
					fichaId: this.ficha.id,
				});
			} else {
				this.modal.close(dto);
			}
		}
	}

	get valid() {
		return this.formGroup.valid;
	}

	startModal() {
		this.ficha = null;
		this.formGroup.reset();

		this.colaboradorServiceTreinoApi
			.listarTodosColaboradoresPorEmpresa()
			.subscribe((dados) => {
				this.colaboradores = dados;
			});

		this.fichaService.modalidades().subscribe((dados) => {
			this.modalidades = dados;
		});
	}

	loadModal(ficha: Ficha) {
		this.ficha = ficha;
		this.formGroup.patchValue({
			nome: ficha.nome,
			colaboradores: [],
			modalidades: [],
		});
		this.montarColaboradores(ficha);
		this.montarModalidades(ficha);
	}

	private montarColaboradores(ficha: Ficha) {
		this.colaboradorServiceTreinoApi
			.listarTodosColaboradoresPorEmpresa()
			.subscribe((dados) => {
				this.colaboradores = dados;
				dados.forEach((col) => {
					if (ficha.colaboradorIds.some((x) => x === col.id)) {
						this.formGroup.get("colaboradores").value.push(col);
					}
				});
				this.cd.detectChanges();
			});
	}

	private montarModalidades(ficha: Ficha) {
		this.fichaService.modalidades().subscribe((dados) => {
			this.modalidades = dados;
			dados.forEach((mol) => {
				if (ficha.modalidadeIds.some((x) => x === mol.id)) {
					this.formGroup.get("modalidades").value.push(mol);
				}
			});
			this.cd.detectChanges();
		});
	}

	private getDto(): any {
		const dto = this.formGroup.getRawValue();
		if (dto.colaboradores) {
			dto.colaboradorIds = [];
			dto.colaboradores.forEach((colaborador, index) => {
				dto.colaboradorIds.push(colaborador.id);
			});
		}
		delete dto.colaboradores;
		if (dto.modalidades) {
			dto.modalidadeIds = [];
			dto.modalidades.forEach((modalidade, index) => {
				dto.modalidadeIds.push(modalidade.id);
			});
		}
		delete dto.modalidades;
		return dto;
	}
}
