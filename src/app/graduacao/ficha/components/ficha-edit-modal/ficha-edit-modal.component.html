<pacto-cat-form-input
	[control]="formGroup.get('nome')"
	[errorMsg]="'Definir um nome com ao menos 3 characteres.'"
	[placeholder]="'Nome'"
	i18n-label="@@crud-edit-label:input:nome"
	label="Nome"></pacto-cat-form-input>

<pacto-cat-form-multi-select-filter
	[control]="formGroup.get('colaboradores')"
	[errorMsg]="'Selecionar ao menos um colaborador.'"
	[labelKey]="'nome'"
	[options]="colaboradores"
	i18n-label="@@crud-edit-label:input:colaboradores"
	i18n-placeholder="@@crud-edit-placeholder:input:colaboradores"
	label="Colaboradores"
	placeholder="'Selecionar colaboradores'"></pacto-cat-form-multi-select-filter>

<pacto-cat-form-multi-select-filter
	[control]="formGroup.get('modalidades')"
	[errorMsg]="'Selecionar ao menos uma modalidade.'"
	[labelKey]="'nome'"
	[options]="modalidades"
	i18n-label="@@crud-edit-label:input:modalidade"
	i18n-placeholder="@@crud-edit-placeholder:input:modalidade"
	label="Modalidades"
	placeholder="Selecionar modalidade(s)"></pacto-cat-form-multi-select-filter>

<div class="button-modal">
	<pacto-cat-button
		(click)="saveHandler()"
		i18n-label="@@buttons:salvar"
		label="Salvar"></pacto-cat-button>
</div>
