import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Cliente } from "../../../../microservices/personagem/cliente/cliente.model";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-ficha-nivel-aluno-edit-modal",
	templateUrl: "./ficha-nivel-aluno-edit-modal.component.html",
	styleUrls: ["./ficha-nivel-aluno-edit-modal.component.scss"],
})
export class FichaNivelAlunoEditModalComponent implements OnInit {
	nivelId: number;
	aluno: Cliente;
	numberMask = [/[0-9]/, /[0-9]/, /[0-9]/];

	fc = new FormGroup({
		lancamento: new FormControl(null, Validators.required),
		aulasRealizadas: new FormControl(0),
	});

	constructor(
		private nivelService: NivelService,
		private notify: SnotifyService,
		private modal: NgbActiveModal
	) {}

	ngOnInit() {}

	loadData(aluno, nivelId) {
		let dataLancamento = aluno.lancamento;
		if (dataLancamento) {
			if (typeof dataLancamento === "number") {
				dataLancamento = new Date(dataLancamento);
			} else if (typeof dataLancamento === "string") {
				dataLancamento = new Date(dataLancamento);
			}
		} else {
			dataLancamento = new Date();
		}
		this.fc.get("lancamento").setValue(dataLancamento);
		this.fc.get("aulasRealizadas").setValue(aluno.aulasRealizadas || 0);
		this.aluno = aluno;
		this.nivelId = nivelId;
	}

	salvar() {
		if (!this.fc.valid) {
			this.fc.markAllAsTouched();
			this.notify.error("Preencha todos os campos obrigatórios.");
			return;
		}

		this.nivelService.alterarNivelAluno(this.nivelId, this.getDto()).subscribe(
			(result) => {
				if (result === "sucesso") {
					this.notify.success("Dados do nível do aluno editados com sucesso.");

					const dadosAtualizados = this.getDto();
					this.aluno.lancamento = dadosAtualizados.lancamento;
					this.aluno.aulasRealizadas = dadosAtualizados.aulasRealizadas;

					this.modal.close({
						nivelAluno: result,
						novo: true,
						dadosAtualizados: this.aluno,
					});
				}
			},
			(error) => {
				this.notify.error("Erro ao salvar dados do aluno. Tente novamente.");
				console.error("Erro ao alterar nível do aluno:", error);
			}
		);
	}

	private getDto() {
		const dto: any = this.fc.getRawValue();

		if (dto.lancamento) {
			dto.lancamento = new Date(dto.lancamento).getTime();
		}

		dto.aulasRealizadas =
			dto.aulasRealizadas === undefined || dto.aulasRealizadas === ""
				? 0
				: dto.aulasRealizadas;
		dto.id = this.aluno.id;
		dto.nome = this.aluno.nome;
		dto.matricula = this.aluno.matricula;
		dto.nivelId = this.aluno.nivelId;

		return dto;
	}
}
