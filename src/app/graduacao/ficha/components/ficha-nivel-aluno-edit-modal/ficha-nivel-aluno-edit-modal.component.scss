@import "src/assets/scss/pacto/plataforma-import.scss";

.div-main {
	margin: 22px 24px 33px;
}

::ng-deep#btn-salvar-alteracao-aluno-nivel {
	width: 100% !important;
}

::ng-deep .modal-editar-nivel-aluno {
	.modal-content {
		width: 560px;

		pacto-modal-wrapper {
			width: 560px;
		}
	}
}

.nome-aluno {
	align-items: center;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: center;
	margin-left: 15px;
	font-weight: 400;
	font-size: 20px;
	color: $pretoPri;
}

.mensagem {
	display: flex;
	margin-top: -19px;
	margin-bottom: 28px;
	color: #5f9bc4;

	.icon-info {
		margin-right: 4px;
	}

	.texto {
		font-family: "Roboto";
		font-weight: 400;
		font-size: 14px;

		.negrito {
			font-weight: bold;
		}
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	::ng-deep .modal-editar-nivel-aluno {
		.modal-content {
			width: 100%;

			pacto-modal-wrapper {
				width: 100%;

				.mensagem .texto {
					text-align: justify;
				}
			}
		}
	}
}
