<div class="div-main">
	<div class="d-flex">
		<div>
			<pacto-cat-person-avatar
				[diameter]="90"
				[uri]="aluno?.imageUri"></pacto-cat-person-avatar>
		</div>
		<div class="nome-aluno">{{ aluno.nome }}</div>
	</div>

	<div class="row">
		<div class="col-md-6">
			<pacto-cat-form-datepicker
				[control]="fc.get('lancamento')"
				[errorMsg]="'Forneça uma data válida.'"
				[id]="'input-data-inicio'"
				[label]="'Data de inclusão no nível'"></pacto-cat-form-datepicker>
		</div>

		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="fc.get('aulasRealizadas')"
				[id]="'input-aulas-realizadas-nivel'"
				[label]="'Aulas realizadas no nível'"
				[textMask]="{ guide: false, mask: numberMask }"></pacto-cat-form-input>
		</div>
	</div>

	<div class="mensagem">
		<i class="pct pct-alert-circle icon-info"></i>
		<span class="texto">
			Defina acima qual o dia em que seu aluno
			<span class="negrito">começou</span>
			o nível e quantas aulas já foram
			<span class="negrito">concluídas</span>
			dentro do mesmo.
		</span>
	</div>

	<div class="row">
		<div class="col-lg-12">
			<pacto-cat-button
				(click)="salvar()"
				[id]="'btn-salvar-alteracao-aluno-nivel'"
				[label]="'Salvar'"
				[size]="'LARGE'"></pacto-cat-button>
		</div>
	</div>
</div>
