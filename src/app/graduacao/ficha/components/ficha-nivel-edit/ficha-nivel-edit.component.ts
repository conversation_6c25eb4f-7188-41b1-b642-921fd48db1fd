import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";
import { Observable } from "rxjs";
import { tap } from "rxjs/operators";

import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { SeletorImagemUserComponent } from "old-ui-kit";
import { AtividadeEditModalComponent } from "src/app/graduacao/atividade-graduacao/components/atividade-edit/atividade-edit-modal.component";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { CatTabsComponent, TraducoesXinglingComponent } from "ui-kit";
import { AtividadeGraduacaoService } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.service";
import { ColorPickerModalComponent } from "../color-picker-modal/color-picker-modal.component";

@Component({
	selector: "pacto-ficha-nivel-edit",
	templateUrl: "./ficha-nivel-edit.component.html",
	styleUrls: ["./ficha-nivel-edit.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FichaNivelEditComponent implements OnInit {
	nivelId;
	@ViewChild("seletorImagem", { static: false })
	seletorImagem: SeletorImagemUserComponent;
	@ViewChild("warningAtividade", { static: true })
	warningAtividade: TraducoesXinglingComponent;
	@ViewChild("tabs", { static: false }) tabs: CatTabsComponent;
	fichaId;
	loaded = false;
	edit;
	imageUri;
	atividades: any[];
	existeFicha: boolean;
	valorAntigoMinimoAulas: number;
	hiddenInputMinimoAulas: boolean;
	formAtualizado: boolean;

	constructor(
		private modal: ModalService,
		private router: Router,
		private route: ActivatedRoute,
		private snotify: SnotifyService,
		private atividadeService: AtividadeGraduacaoService,
		private nivelService: NivelService,
		private rest: RestService,
		private cd: ChangeDetectorRef,
		private notify: SnotifyService,
		private snotifyService: SnotifyService
	) {}

	atividadeFc: FormControl = new FormControl(null);

	formGroup = new FormGroup({
		nome: new FormControl(null, [Validators.required, Validators.minLength(3)]),
		tecnica: new FormControl(),
		objeto: new FormControl(),
		objetivo: new FormControl(),
		atividades: new FormControl([]),
		ativo: new FormControl(true),
		cor: new FormControl(),
		exigeAulas: new FormControl("0"),
		quantidadeMinimaAulas: new FormControl("0"),
	});

	numberMask = [/[0-9]/, /[0-9]/, /[0-9]/];

	ngOnInit() {
		const params = this.route.snapshot.params;
		this.fichaId = params.fichaId;
		this.atividadeService
			.atividadesAtivas()
			.subscribe((dados) => (this.atividades = dados));
		if (params.id !== "adicionar") {
			this.edit = true;
			this.loaded = true;
			this.nivelId = params.id;
			this.fillOutForm(parseInt(params.id, 10)).subscribe();
		} else {
			this.edit = false;
			this.loaded = true;
			this.cd.detectChanges();
		}
		this.setUpEvents();
		setTimeout(() => {
			this.cd.detectChanges();
		});

		this.formGroup.valueChanges.subscribe((value) => {
			this.formAtualizado = true;
		});
	}

	changeExigeAulas() {
		if (this.formGroup.value.exigeAulas === "0") {
			this.hiddenInputMinimoAulas = false;
			this.formGroup
				.get("quantidadeMinimaAulas")
				.setValue(
					this.valorAntigoMinimoAulas === 0 ? 1 : this.valorAntigoMinimoAulas
				);
		} else {
			this.hiddenInputMinimoAulas = true;
			this.formGroup.get("quantidadeMinimaAulas").setValue("0");
		}
		this.cd.detectChanges();
	}

	changeInputMinimoAulas() {
		if (this.formGroup.get("quantidadeMinimaAulas").value === "0") {
			this.formGroup.get("quantidadeMinimaAulas").setValue("1");
		}
		setTimeout(() => {
			if (this.formGroup.get("quantidadeMinimaAulas").value === "") {
				this.formGroup.get("quantidadeMinimaAulas").setValue("1");
			}
		}, 3000);
	}

	criarAtividadeHandler() {
		const modal = this.modal.open(
			"Criar Atividade",
			AtividadeEditModalComponent,
			PactoModalSize.MEDIUM
		);
		modal.result.then(
			(content) => {
				this.atividadeService.criarAtividade(content).subscribe((atividade) => {
					this.atividadeService
						.atividadesAtivas()
						.subscribe((dados) => (this.atividades = dados));
					this.snotify.success("Atividade criada com sucesso.");
					this.adicionarAtividadeLista(atividade);
					this.cd.detectChanges();
				});
			},
			() => {}
		);
	}

	get selectedAtividades() {
		return this.formGroup.get("atividades").value;
	}

	private setUpEvents() {
		this.atividadeFc.valueChanges.subscribe((atividade) => {
			this.adicionarAtividadeLista(atividade);
			this.atividadeFc.setValue(null, { emitEvent: false });
		});
	}

	private adicionarAtividadeLista(atividade) {
		const fc = this.formGroup.get("atividades");
		const current = fc.value;
		if (fc.value.length === 0) {
			current.push(atividade);
			fc.setValue(current, { emitValue: false });
		} else {
			if (!this.existeAtividade(atividade, fc)) {
				current.push(atividade);
				fc.setValue(current, { emitValue: false });
			} else {
				this.snotifyService.warning(
					this.warningAtividade.getLabel("warningAtividade")
				);
			}
		}
	}

	private existeAtividade(atividade, fc) {
		for (let i = 0; fc.value.length > i; i++) {
			if (fc.value[i].id === atividade.id) {
				return true;
			}
		}
		return false;
	}

	private fillOutForm(id): Observable<any> {
		return this.nivelService.obterNivelPorId(id).pipe(
			tap((nivel) => {
				nivel.quantidadeMinimaAulas =
					nivel.quantidadeMinimaAulas === undefined ||
					nivel.quantidadeMinimaAulas === null
						? 0
						: nivel.quantidadeMinimaAulas;
				this.hiddenInputMinimoAulas = nivel.exigeAulas === true ? false : true;
				this.formGroup.patchValue(
					{
						nome: nivel.nome,
						tecnica: nivel.tecnica,
						objeto: nivel.objeto,
						objetivo: nivel.objetivo,
						ativo: nivel.ativo,
						cor: nivel.cor,
						exigeAulas: nivel.exigeAulas === true ? "0" : "1",
						quantidadeMinimaAulas: nivel.quantidadeMinimaAulas,
					},
					{ emitEvent: false }
				);
				this.valorAntigoMinimoAulas = nivel.quantidadeMinimaAulas;
				if (nivel.atividades) {
					nivel.atividades.forEach((atividade) => {
						this.formGroup.get("atividades").value.push(atividade);
					});
				}
				if (nivel.fotoUri) {
					this.imageUri = nivel.fotoUri;
				}

				this.loaded = true;
				this.cd.detectChanges();
			})
		);
	}

	removeAtividadeHandler(index) {
		const fc = this.formGroup.get("atividades");
		const current = fc.value;
		current.splice(index, 1);
		fc.setValue(current, { emitValue: false });
	}

	pickColorHandler() {
		const modal = this.modal.open(
			"Selecionar uma cor",
			ColorPickerModalComponent,
			PactoModalSize.SMALL
		);
		modal.result.then(
			(cor) => {
				this.formGroup.get("cor").setValue(cor);
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	salvarHandler() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			const dto = this.getDto();
			dto.exigeAulas = this.formGroup.value.exigeAulas === "0" ? true : false;
			if (this.edit) {
				this.nivelService.updateNivel(this.nivelId, dto).subscribe((result) => {
					if (result === "atividade_com_vinculo_resposta_avaliacao") {
						this.snotify.error(
							"Não foi possível salvar as alterações pois uma das atividades possui vínculo com uma resposta de avaliação de progresso."
						);
					} else {
						this.router.navigate([
							"graduacao",
							"ficha",
							this.fichaId,
							"niveis",
						]);
						if (this.formAtualizado) {
							this.notify.success("Nivel editado com sucesso.");
						}
					}
				});
			} else {
				this.nivelService.criarNivel(this.fichaId, dto).subscribe(() => {
					this.router.navigate(["graduacao", "ficha", this.fichaId, "niveis"]);
					this.notify.success("Nivel criado com sucesso.");
				});
			}
		}
	}

	cancelHandler() {
		this.router.navigate(["graduacao", "ficha", this.fichaId, "niveis"]);
	}

	get urlLog() {
		return this.rest.buildFullUrlGraduacao(`log/niveis/${this.nivelId}`);
	}

	private getDto() {
		const dto: any = this.formGroup.getRawValue();
		if (this.seletorImagem.imagemData) {
			dto.fotoData = this.seletorImagem.imagemData;
		}
		if (dto.atividades && dto.atividades.length) {
			dto.atividadeIds = [];
			dto.atividades.forEach((atividade) => {
				dto.atividadeIds.push(atividade.id);
			});
		} else {
			dto.atividadeIds = [];
		}
		delete dto.atividades;
		return dto;
	}

	get tabsShowButton() {
		const currentTab = this.tabs.tabId;
		if (this.tabs) {
			return currentTab === "edit" || currentTab === "alunos";
		} else {
			return null;
		}
	}
}
