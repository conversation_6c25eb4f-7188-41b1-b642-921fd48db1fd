@import "~src/assets/scss/pacto/plataforma-import.scss";

.nivel-edit-title {
	@extend .type-h2;
	color: #2c343b;
	margin-bottom: 30px;

	span {
		cursor: pointer;
	}

	.pct {
		font-size: 28px;
	}
}

.nivel-titulo-card {
	@extend .type-h5-bold;
}

.content-aux {
	display: flex;
	flex-wrap: wrap;
	margin: 30px;

	.photo-column {
		width: 116px;
		margin-right: 30px;
	}

	.row {
		flex-grow: 1;
	}
}

pacto-seletor-imagem-user {
	margin-top: 47px;
}

.seletor-imagem {
	margin: 15px 0px;

	.upper {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;

		.modelo-label {
			flex-grow: 1;
		}
	}
}

.color-selector {
	margin: 15px 0px;

	.label-color {
		@extend .type-h6;
		line-height: 32px;
		color: $gelo04;
		padding-left: 3px;
	}

	.input-color {
		color: $gelo04;
		line-height: 36px;
		font-size: 36px;
		cursor: pointer;
		border: 1px solid $gelo04;
		border-radius: 20px;
		width: 40px;
		text-align: center;
		height: 40px;
	}
}

.actions-row {
	display: flex;
	justify-content: flex-end;
	margin: 30px;

	button {
		margin-left: 10px;
	}
}

.atividade-label {
	line-height: 2em;
	@extend .type-h6;
	color: $gelo04;
}

.selected-atividades {
	.selected-atividade {
		display: flex;
		align-items: center;
		border-bottom: 1px solid $canetaBicPastel;
		padding: 15px 7.5px;

		&:last-child() {
			border-bottom: 0px;
		}
	}

	.nome {
		flex-grow: 1;
		margin-left: 15px;
	}

	.remove .pct {
		color: $hellboyPri;
		font-size: 20px;
		cursor: pointer;
	}
}

@media (max-width: $bootstrap-breakpoint-lg) {
	.photo-column {
		flex-basis: 100%;
		display: flex;
		justify-content: center;
	}
}

.no-margin-top-bottom {
	margin: -15px 0;
}

.no-margin-right-left {
	margin: 0 -15px;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.div-ficha-nivel-edit {
		.nivel-edit-title {
			font-size: 28px;
		}

		.content-aux {
			margin: 20px;
			display: block;
		}

		.div-photo-column {
			display: flex;

			.photo-column {
				margin: 0;

				pacto-seletor-imagem-user {
					margin-top: 16px;

					::ng-deep.view-imagem {
						width: 90px !important;
						height: 90px !important;

						img {
							width: 90px !important;
						}
					}
				}
			}
		}

		::ng-deep .tab {
			padding: 0 12px !important;
		}
	}
}
