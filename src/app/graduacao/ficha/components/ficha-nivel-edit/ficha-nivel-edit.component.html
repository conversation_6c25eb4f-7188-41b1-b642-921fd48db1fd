<pacto-cat-layout-v2>
	<div class="div-ficha-nivel-edit">
		<div *ngIf="loaded && !edit" class="nivel-edit-title">
			<span [routerLink]="['/graduacao', 'ficha', fichaId, 'niveis']">
				<i class="pct pct-arrow-left"></i>
				Cadastro de Nível
			</span>
		</div>
		<div *ngIf="loaded && edit" class="nivel-edit-title">
			<span [routerLink]="['/graduacao', 'ficha', fichaId, 'niveis']">
				<i class="pct pct-arrow-left"></i>
				Edição de Nível
			</span>
		</div>

		<pacto-cat-tabs #tabs *ngIf="loaded">
			<ng-template label="Dados do Nível" pactoTab="edit">
				<div class="content-aux">
					<div class="div-photo-column">
						<div class="photo-column">
							<pacto-seletor-imagem-user
								#seletorImagem
								[control]="formGroup.get('file')"
								[diameter]="116"
								[uriImagem]="imageUri"></pacto-seletor-imagem-user>
						</div>
					</div>

					<div class="row">
						<div class="col-12 col-lg-6 col-md-12">
							<pacto-cat-form-input
								[control]="formGroup.get('nome')"
								[errorMsg]="'Defina um nome com pelo menos 3 characteres.'"
								[label]="'Nome'"
								[placeholder]="'Nome do Nível'"></pacto-cat-form-input>
							<pacto-cat-form-textarea
								[control]="formGroup.get('tecnica')"
								[maxlength]="255"
								[placeholder]="'Descrição da Técnica Aplicada'"
								i18n-label="@@ficha-nivel-edit:descricao-tecnica-aplicada"
								label="'Descrição da Técnica Aplicada'"></pacto-cat-form-textarea>
							<pacto-cat-form-input
								[control]="formGroup.get('objeto')"
								[label]="'Objeto'"
								[placeholder]="
									'O nível premia com algum objeto especial'
								"></pacto-cat-form-input>
							<pacto-cat-form-input
								[control]="formGroup.get('objetivo')"
								[label]="'Objetivo'"
								[maxlength]="255"
								[placeholder]="'Objetivo do nível'"></pacto-cat-form-input>
							<div class="row color-selector no-margin-right-left">
								<div class="col-lg-3 col-md-2">
									<div class="label-color">Cor</div>
									<div
										(click)="pickColorHandler()"
										[ngStyle]="{
											backgroundColor: formGroup.get('cor')
												? formGroup.get('cor').value
												: null
										}"
										class="input-color">
										<span *ngIf="!formGroup.get('cor').value">+</span>
									</div>
								</div>
								<div class="col-lg-4 col-md-5">
									<pacto-cat-select
										(change)="changeExigeAulas()"
										[control]="formGroup.get('exigeAulas')"
										[items]="[
											{ id: '0', label: 'Sim' },
											{ id: '1', label: 'Não' }
										]"
										[label]="'Exige mínimo de aulas?'"></pacto-cat-select>
								</div>
								<div
									[hidden]="hiddenInputMinimoAulas"
									class="col-lg-5 col-md-5 no-margin-top-bottom">
									<pacto-cat-form-input
										(keyup)="changeInputMinimoAulas()"
										[control]="formGroup.get('quantidadeMinimaAulas')"
										[label]="'Mínimo de aulas exigidas'"
										[textMask]="{
											guide: false,
											mask: numberMask
										}"></pacto-cat-form-input>
								</div>
							</div>
						</div>
						<div class="col-lg-6 col-md-12">
							<div class="seletor-imagem">
								<div class="upper">
									<div class="atividade-label">Atividades</div>
									<pacto-cat-button
										(click)="criarAtividadeHandler()"
										[label]="'criar atividade'"
										[size]="'SMALL'"
										[type]="'DARK'"></pacto-cat-button>
								</div>
								<pacto-cat-select-filter
									[control]="atividadeFc"
									[labelKey]="'nome'"
									[options]="atividades"
									placeholder="ATIVIDADES AVALIADAS PARA O NÍVEL"></pacto-cat-select-filter>

								<div class="selected-atividades">
									<div
										*ngFor="
											let selected of selectedAtividades;
											let index = index
										"
										class="selected-atividade">
										<pacto-cat-person-avatar
											[diameter]="34"
											[uri]="selected.imageUri"></pacto-cat-person-avatar>
										<div class="nome type-p-small cor-preto-pri">
											{{ selected.nome }}
										</div>
										<div (click)="removeAtividadeHandler(index)" class="remove">
											<i class="pct pct-trash-2"></i>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="actions-row">
					<pacto-log [hidden]="true" [url]="urlLog"></pacto-log>
					<button
						(click)="cancelHandler()"
						class="btn btn-secondary"
						i18n="@@buttons:cancelar">
						Cancelar
					</button>
					<button
						(click)="salvarHandler()"
						class="btn btn-primary"
						i18n="@@buttons:salvar"
						id="btn-add-aula">
						Salvar
					</button>
				</div>
			</ng-template>
			<ng-template
				*ngIf="loaded && edit"
				label="Alunos do Nível"
				pactoTab="alunos">
				<pacto-ficha-aluno-list></pacto-ficha-aluno-list>
			</ng-template>
		</pacto-cat-tabs>
	</div>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #warningAtividade>
	<span xingling="warningAtividade">
		A atividade informada já foi inserida, por favor, insira outra.
	</span>
</pacto-traducoes-xingling>
