import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";

import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { Nivel } from "src/app/microservices/graduacao/nivel/nivel.model";
import { of, zip } from "rxjs";
import { ClienteService } from "../../../../microservices/personagem/cliente/cliente.service";

@Component({
	selector: "pacto-ficha-nivel-modal",
	templateUrl: "./ficha-nivel-modal.component.html",
	styleUrls: ["./ficha-nivel-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FichaNivelModalComponent implements OnInit {
	nivel: Nivel;
	alunos: any[] = [];

	constructor(
		private cd: ChangeDetectorRef,
		private nivelService: NivelService
	) {}

	ngOnInit() {}

	loadNivel(id: number) {
		const nivel$ = this.nivelService.obterNivelPorId(id);
		const alunos$ = this.nivelService.obterAlunosFullNivelGraduacao(id);
		zip(nivel$).subscribe((resultado) => {
			this.nivel = resultado[0];
			this.cd.detectChanges();
		});

		zip(alunos$).subscribe((resultado) => {
			this.alunos = resultado[0];
			this.cd.detectChanges();
		});
	}
}
