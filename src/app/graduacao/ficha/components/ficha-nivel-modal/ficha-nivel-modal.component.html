<div class="row">
	<div class="conteudo-left col-md-4">
		<div class="header-row">
			<pacto-cat-person-avatar
				[diameter]="60"
				[uri]="nivel?.fotoUri"></pacto-cat-person-avatar>
			<div class="nome-nivel">
				{{ nivel?.nome }}
			</div>
		</div>
		<div class="descricao">
			{{ nivel?.tecnica }}
		</div>
		<div class="nivel-objeto">
			<div class="objeto">Objeto</div>
			<div class="format-objeto">{{ nivel?.objeto }}</div>
		</div>
		<div class="nivel-objeto">
			<div class="objeto">Objetivo</div>
			<div class="format-objeto">{{ nivel?.objetivo }}</div>
		</div>
		<div class="nivel-aulas">
			<div class="aulas">Aulas para passar de nível</div>
			<div class="format-aulas">
				{{
					nivel?.quantidadeMinimaAulas === undefined
						? 0
						: nivel?.quantidadeMinimaAulas
				}}
			</div>
		</div>
	</div>

	<div class="column-right col-md-8">
		<pacto-cat-tabs>
			<ng-template label="Atividades" pactoTab="'atividades'">
				<div class="atividades-wrapper">
					<div
						[maxHeight]="'400px'"
						class="atividades-scroll-wrapper"
						pactoCatSmoothScroll>
						<!-- Content Tab A -->
						<div
							*ngFor="let atividade of nivel?.atividades"
							class="atividade-item">
							<pacto-cat-person-avatar
								[diameter]="50"
								[uri]="atividade.imageUri"></pacto-cat-person-avatar>
							<div class="colum-nome">{{ atividade.nome }}</div>
							<div class="colum-descricao">
								{{ atividade.descricao }}
							</div>
						</div>
					</div>
				</div>
			</ng-template>

			<ng-template label="Alunos" pactoTab="'alunos'">
				<div class="alunos-wrapper">
					<div
						[maxHeight]="'400px'"
						class="alunos-scroll-wrapper"
						pactoCatSmoothScroll>
						<!-- Content Tab B -->
						<div *ngFor="let aluno of alunos" class="aluno-item">
							<pacto-cat-person-avatar
								[diameter]="50"
								[uri]="aluno.imageUri"></pacto-cat-person-avatar>
							<div>
								<div class="nome-aluno">
									{{ aluno.nome }}
								</div>
								<div class="aulas-aluno">
									{{ aluno.aulasRealizadas }} aulas realizadas
									<span class="barra-divisor">/</span>
									{{ aluno.aulasRestantes }} aulas restantes
								</div>
							</div>
						</div>
					</div>
				</div>
			</ng-template>
		</pacto-cat-tabs>
	</div>
</div>
