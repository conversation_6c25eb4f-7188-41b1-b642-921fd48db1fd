@import "src/assets/scss/pacto/plataforma-import";

:host {
	display: block;
	padding: 30px;
}

.conteudo-left {
	flex-wrap: wrap;

	.header-row {
		display: flex;
		align-items: center;
	}

	.nome-nivel {
		flex-grow: 1;
		@extend .type-h5;
		color: $pretoPri;
		margin-left: 15px;
	}

	.descricao {
		flex-basis: 100%;
		@extend .type-p-small;
		margin: 15px 0px;
		color: $cinza05;
	}

	.nivel-objeto {
		flex-basis: 100%;
		margin: 15px 0px;

		.objeto {
			@extend .type-h6;
			color: $gelo04;
		}

		.format-objeto {
			@extend .type-p-small-rounded;
			color: $pretoPri;
		}
	}

	.nivel-aulas {
		flex-basis: 100%;
		margin: 15px 0px;

		.aulas {
			@extend .type-h6;
			color: $gelo04;
		}

		.format-aulas {
			@extend .type-p-small-rounded;
			color: $pretoPri;
		}
	}
}

.column-right {
	.atividades-wrapper,
	.alunos-wrapper {
		box-shadow: 1px 2px 2px 0px #eee, -1px 2px 2px 0px #eee;
		border-radius: 0px 0px 5px 5px;
		border-top: 1px solid #f1f1f1;
	}

	.atividades-scroll-wrapper,
	.alunos-scroll-wrapper {
		padding-bottom: 5px;
	}

	.atividade-item {
		display: flex;
		align-items: center;
		border-bottom: 1px solid rgb(214, 217, 218);
		margin: 0px 15px;
		padding: 15px 0px;

		&:last-child() {
			border-bottom: 0px;
		}

		.colum-nome {
			margin: 0px 15px;
			@extend .type-h5;
			color: $pretoPri;
		}

		.colum-descricao {
			@extend .type-p-small;
			flex-basis: 280px;
			color: $cinza05;
			max-height: 80px;
			overflow: hidden;
		}
	}

	.aluno-item {
		display: flex;
		align-items: center;
		border-bottom: 1px solid rgb(214, 217, 218);
		margin: 0px 15px;
		padding: 15px 0px;

		&:last-child() {
			border-bottom: 0px;
		}

		.nome-aluno {
			margin: 0px 15px;
			@extend .type-h5;
			color: $pretoPri;
		}

		.aulas-aluno {
			margin: 0px 15px;
			color: $cinzaPri;
			font-size: 12px;
		}

		.barra-divisor {
			margin: 0 4px;
		}
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.atividade-item {
		display: block !important;
		text-align: center;

		::ng-deep pacto-cat-person-avatar {
			display: flex;
			justify-content: center;
			margin-bottom: 12px;
		}

		.colum-nome {
			margin-bottom: 12px;
		}

		.colum-descricao {
			margin-bottom: 12px;
		}
	}
}
