import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-color-picker-modal",
	templateUrl: "./color-picker-modal.component.html",
	styleUrls: ["./color-picker-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ColorPickerModalComponent implements OnInit {
	private currentColor: string;
	cores: {
		nome: string;
		cor: string;
	}[] = [
		{ nome: "Branco", cor: "#FFFFFF" },
		{ nome: "Azul", cor: "#42AAFC" },
		{ nome: "Verde", cor: "#9FEC75" },
		{ nome: "Amarelo", cor: "#F2C54B" },
		{ nome: "Rosa", cor: "#FF4F8B" },
		{ nome: "Cinza 2", cor: "#DADBE1" },
		{ nome: "Azul 2", cor: "#178BE6" },
		{ nome: "Verde 2", cor: "#70D13C" },
		{ nome: "Amarelo 2", cor: "#DBA921" },
		{ nome: "Rosa 2", cor: "#E82766" },
		{ nome: "Cinza 3", cor: "#7C8083" },
		{ nome: "Azul 3", cor: "#446381" },
		{ nome: "Verde 3", cor: "#2A9660" },
		{ nome: "Laranja 3", cor: "#FF6B52" },
		{ nome: "Roxo", cor: "#A565A5" },
		{ nome: "Cinza 4", cor: "#51555A" },
		{ nome: "Azul 4", cor: "#1B4166" },
		{ nome: "Verde 4", cor: "#137343" },
		{ nome: "Laranja 4", cor: "#D13D23" },
		{ nome: "Roxo 2 4", cor: "#843D84" },
	];

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	isCurrentColor(color: string): boolean {
		return color === this.currentColor;
	}

	loadColor(color: string) {
		this.currentColor = color;
		this.cd.detectChanges();
	}

	pickColorHandler(color: string) {
		this.currentColor = color;
	}

	salvarHandler() {
		this.activeModal.close(this.currentColor);
	}
}
