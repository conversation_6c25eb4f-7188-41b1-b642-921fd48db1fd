<pacto-cat-layout-v2>
	<div class="titulo-card-wraper">
		<div class="titulo-card-ficha">Fichas Técnicas</div>
	</div>

	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#tableData
			(btnClick)="novaFicha()"
			(iconClick)="actionClickHandler($event)"
			(rowClick)="fichaClickHandler($event)"
			[sessionService]="sessionService"
			[tableTitle]="'Fichas Técnicas'"
			[table]="tableConfig"
			telaId="fichasTecnicas"></pacto-relatorio>
	</div>
	<div [hidden]="true" class="btn-log">
		<pacto-log [url]="urlLog"></pacto-log>
	</div>
</pacto-cat-layout-v2>

<ng-template #novaFichaBtn>Nova Ficha</ng-template>

<ng-template #colunaNiveis let-item="item">
	<div class="image-grid">
		<div *ngFor="let nivel of item.niveis | slice : 0 : 3" class="image">
			<pacto-cat-person-avatar
				[borderRadius]="18"
				[diameter]="36"
				[uri]="nivel?.fotoUri"></pacto-cat-person-avatar>
		</div>
		<div *ngIf="item.niveis?.length > 3" class="image-total">
			+{{ item.niveis.length - 3 }}
		</div>
	</div>
</ng-template>

<ng-template #colunaModalidades let-item="item">
	<span>{{ item.modalidadeIds?.length || 0 }}</span>
</ng-template>
