@import "src/assets/scss/pacto/plataforma-import";

.ficha-wrapper {
	gap: 30px;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr;
	@media (max-width: 1020px) {
		grid-template-columns: 1fr 1fr 1fr;
	}
	@media (max-width: 790px) {
		grid-template-columns: 1fr 1fr;
	}
	@media (max-width: 580px) {
		grid-template-columns: 1fr;
	}
}

::ng-deep pacto-relatorio .fa-pencil {
	margin-right: 15px;
}
.titulo-card-wraper {
	display: flex;
	justify-content: space-between;
	margin-bottom: 30px;
}

.titulo-card-ficha {
	@extend .type-h2;
}

.titulo-card-actions {
	display: flex;
	justify-content: space-between;
}

.titulo-button {
	margin: 5px 0 0 0;
}

.titulo-input {
	margin: 0 40px;
}

.title-ficha {
	color: $cinzaPri;
	margin: 0 15px;
	@extend .type-h6;
	text-overflow: ellipsis;
	width: 270px;
	white-space: nowrap;
	overflow: hidden;
}

.position-input {
	display: flex;
	margin: 0;
}

.title-wraper {
	display: flex;
	justify-content: space-between;
	padding: 0 10px 0 0;
}

.scroll-content .scroll-content1 {
	-webkit-transform: inherit !important;
	transform: inherit !important;
}

.body {
	border-bottom: 1px solid $cinza01;
	height: 33.33%;
	display: flex;
}

.body-content {
	padding: 12px;
}

.titulo {
	color: #a6aab1;
	font-size: 13px !important;
	@extend .type-h6;
}

.content {
	font-size: 12px;
	@extend .type-h6-bold;
}

.position-icon {
	align-items: center;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: center;
	margin-left: 20px;
}

.image-grid {
	margin-left: 6px;
	display: flex;
}

.image {
	box-shadow: 0 5px 4px #d3d5d7;
	border-radius: 75px;
	margin-left: -7px;
	background-color: $cinzaPri;
	position: relative;
	background-position: center;
	background-size: cover;
}

.image-total {
	width: 35px;
	height: 35px;
	border-radius: 75px;
	box-shadow: 0 5px 4px #d3d5d7;
	margin-left: -7px;
	position: relative;
	color: $cinzaPri;
	background-color: $azulPactoPri;

	align-items: center;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: center;
	font-size: 13px !important;
	@extend .type-h6;
}

.tooltip-action {
	font-family: "Nunito Sans", sans-serif;
	text-transform: uppercase;
	font-weight: 600;
	font-size: 12px;
	color: $pretoPri;
	margin: 2px 0px;
	cursor: pointer;

	.pct {
		padding-right: 5px;
		font-size: 12px;
	}
}

.open-tooltip {
	padding: 5px;
}

pacto-cat-grid-card {
	cursor: pointer;
}

.options-ficha {
	::ng-deep.arrow {
		right: 0em !important;
	}
}

.btn-log {
	text-align: right;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.titulo-card-wraper {
		display: block;

		.titulo-card-actions {
			display: block;

			.titulo-input {
				margin: 0;

				::ng-deep pacto-cat-form-input {
					.aux-wrapper {
						width: 100%;
					}
				}
			}

			.titulo-button {
				margin: 0;

				::ng-deep button {
					width: 100%;
					margin-top: 12px;
				}
			}
		}
	}

	.titulo-card-ficha {
		font-size: 28px;
		text-align: center;
		margin-bottom: 8px;
	}

	.ficha-wrapper pacto-cat-grid-card {
		width: calc(100vw - 120px);
	}

	.btn-log {
		margin-top: 16px;
	}
}
