import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router } from "@angular/router";

import { Subscription } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { NgbPopover } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { FichaEditModalComponent } from "../ficha-edit-modal/ficha-edit-modal.component";
import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-ficha-list",
	templateUrl: "./ficha-list.component.html",
	styleUrls: ["./ficha-list.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FichaListComponent implements OnInit {
	@ViewChild("novaFichaBtn", { static: true }) novaFichaBtn;
	@ViewChild("colunaNiveis", { static: true }) colunaNiveis;
	@ViewChild("colunaModalidades", { static: true }) colunaModalidades;
	@ViewChild("acoesColuna", { static: true }) acoesColuna;
	@ViewChild("tableData", { static: true }) table: RelatorioComponent;

	tableConfig: PactoDataGridConfig;
	clickedFicha: Ficha;

	constructor(
		private fichaService: FichaService,
		private modalService: ModalService,
		private notify: SnotifyService,
		private rest: RestService,
		private router: Router,
		public sessionService: SessionService
	) {}

	ngOnInit() {
		this.configTable();
	}

	private configTable() {
		this.tableConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlGraduacao("fichas"),
			quickSearch: true,
			rowClick: true,
			buttons: {
				id: "novaFichaBtn",
				nome: "NOVA FICHA",
				conteudo: this.novaFichaBtn,
			},
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil",
					tooltipText: "Editar",
					showIconFn: () => true,
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: "Remover",
					showIconFn: () => true,
				},
			],
			columns: [
				{
					nome: "nome",
					titulo: "Nome",
					ordenavel: true,
					buscaRapida: true,
					defaultVisible: true,
				},
				{
					nome: "niveis",
					titulo: "Níveis",
					celula: this.colunaNiveis,
					defaultVisible: true,
					ordenavel: false,
				},
				{
					nome: "nOfAlunos",
					titulo: "Total de alunos",
					defaultVisible: true,
					ordenavel: false,
				},
				{
					nome: "modalidadeIds",
					titulo: "Modalidades",
					celula: this.colunaModalidades,
					defaultVisible: true,
					ordenavel: false,
				},
			],
		});
	}

	actionClickHandler(event: { iconName: string; row: Ficha }) {
		this.clickedFicha = event.row;

		if (event.iconName === "edit") {
			this.editFicha(event.row);
		}

		if (event.iconName === "remove") {
			this.removerFicha(event.row);
		}
	}

	novaFicha() {
		const modal = this.modalService.open(
			"Criar Ficha Técnica",
			FichaEditModalComponent
		);
		modal.componentInstance.startModal();
		modal.result.then(
			(dto) => {
				this.fichaService.criarFicha(dto).subscribe((data) => {
					this.notify.success("Ficha criada com sucesso.");
					this.router.navigate(["graduacao", "ficha", data.id, "niveis"]);
				});
			},
			() => {}
		);
	}

	editFicha(item: Ficha) {
		const modalRef = this.modalService.open(
			"Editar Ficha Técnica",
			FichaEditModalComponent
		);
		modalRef.componentInstance.loadModal(item);
		modalRef.result.then(
			(result: { dto: any; fichaId: number }) => {
				this.fichaService
					.editarFicha(result.dto, result.fichaId)
					.subscribe(() => {
						this.notify.success("Ficha Técnica editada com sucesso.");
					});
			},
			() => {}
		);
	}

	removerFicha(item: Ficha) {
		const confirmModal = this.modalService.confirm(
			`Remover ficha '${item.nome}'`,
			"Tem certeza que deseja remover essa ficha ?"
		);
		confirmModal.result.then(() => {
			this.fichaService.removerFichaTecnica(item.id).subscribe((result) => {
				switch (result) {
					case "ficha_possui_avaliacao":
						this.notify.error(
							"Já existem avaliações registradas para esta ficha."
						);
						break;
					case "ficha_possui_vinculo_colaborador":
						this.notify.error("Existem colaboradores vinculados.");
						break;
					case "ficha_possui_vinculo_modalidade":
						this.notify.error("Existem modalidades vinculadas.");
						break;
					case "ficha_possui_vinculo_nivel":
						this.notify.error("Existem níveis vinculados.");
						break;
					default:
						this.notify.success("Ficha Técnica removida com sucesso.");
						break;
				}
			});
		});
	}
	fichaClickHandler(item: Ficha) {
		this.router.navigate(["graduacao", "ficha", item.id, "niveis"]);
	}

	get urlLog() {
		return this.rest.buildFullUrlGraduacao(`log/ficha-tecnica`);
	}
}
