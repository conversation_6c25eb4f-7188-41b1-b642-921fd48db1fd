import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { switchMap, tap } from "rxjs/operators";
import { zip } from "rxjs";
import { ClienteService } from "../../../../microservices/personagem/cliente/cliente.service";
import { NivelService } from "../../../../microservices/graduacao/nivel/nivel.service";
import {
	Nivel,
	NivelSimple,
} from "../../../../microservices/graduacao/nivel/nivel.model";
import { SnotifyService } from "ng-snotify";
import { IncluirAlunosFichaModalComponent } from "../../../components/incluir-alunos-ficha-modal/incluir-alunos-ficha-modal.component";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { Ficha } from "../../../../microservices/graduacao/ficha/ficha.model";
import { Colaborador } from "../../../../microservices/personagem/colaborador/colaborador.model";
import { FichaService } from "../../../../microservices/graduacao/ficha/ficha.service";
import { SessionService } from "@base-core/client/session.service";
import { Cliente } from "../../../../microservices/personagem/cliente/cliente.model";
import { ColaboradorService } from "../../../../microservices/personagem/colaborador/colaborador.service";
import { ActivatedRoute } from "@angular/router";
import { FichaDeleteModalComponent } from "../ficha-delete-modal/ficha-delete-modal.component";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { FichaNivelAlunoEditModalComponent } from "../ficha-nivel-aluno-edit-modal/ficha-nivel-aluno-edit-modal.component";

declare var moment;

@Component({
	selector: "pacto-ficha-aluno-list",
	templateUrl: "./ficha-aluno-list.component.html",
	styleUrls: ["./ficha-aluno-list.component.scss"],
})
export class FichaAlunoListComponent implements OnInit {
	@ViewChild("nivelVazio", { static: true }) nivelVazio;
	@ViewChild("alunoAvaliacao", { static: true }) alunoAvaliacao;
	@ViewChild("alunosAvaliacao", { static: true }) alunosAvaliacao;
	@ViewChild("alunoRemovido", { static: true }) alunoRemovido;
	@ViewChild("alunonremov", { static: true }) alunonremov;
	@ViewChild("alunoInserido", { static: true }) alunoInserido;
	@ViewChild("alunosRemovidos", { static: true }) alunosRemovidos;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("fotoCelula", { static: true }) fotoCelula;
	nivel: Nivel;
	nivelId: number;
	niveis: NivelSimple[] = [];
	fichaId: Ficha;
	ficha: Ficha;
	colaboradores: Colaborador[] = [];
	aluno: Cliente[];
	table: PactoDataGridConfig;
	loading = true;

	constructor(
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private clienteService: ClienteService,
		private nivelService: NivelService,
		private fichaService: FichaService,
		private snotify: SnotifyService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private colaboradorService: ColaboradorService,
		private rest: RestService
	) {}

	ngOnInit() {
		this.atualizarGrid();
		// tslint:disable-next-line:no-shadowed-variable
		this.route.params.subscribe((params: any) => {
			if (params.fichaId) {
				this.loadData(params.fichaId).subscribe();
			}
		});
	}

	atualizarGrid() {
		const params = this.route.snapshot.params;
		this.fichaId = params.fichaId;
		this.nivelId = params.id;
		const nivel$ = this.nivelService.obterNivelPorId(this.nivelId);
		zip(nivel$).subscribe((resultado) => {
			this.nivel = resultado[0];
			this.cd.detectChanges();
		});
		this.configTable();
		this.cd.detectChanges();
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlGraduacao(
				"niveis/" + this.nivelId + "/alunos-full"
			),
			pagination: false,
			ghostLoad: true,
			valueRowCheck: "id",
			quickSearch: false,
			columns: [
				{
					nome: "nome",
					titulo: "Nome",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					defaultVisible: true,
					celula: this.fotoCelula,
				},
				{
					nome: "lancamento",
					titulo: "Data De Inclusão No Nível",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					styleClass: "center",
					defaultVisible: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "aulasRealizadas",
					titulo: "Aulas Realizadas",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					styleClass: "center",
					defaultVisible: true,
				},
				{
					nome: "aulasRestantes",
					titulo: "Aulas Restantes",
					mostrarTitulo: true,
					buscaRapida: false,
					ordenavel: false,
					visible: true,
					styleClass: "center",
					defaultVisible: true,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "pct pct-edit-3",
				},
				{
					nome: "remove",
					iconClass: "pct pct-trash-2",
				},
			],
		});
	}

	private loadData(id) {
		const ficha$ = this.fichaService.obterFichaPorId(id);
		const colaboradores$ = this.fichaService.obterColaboradoresDaFicha(id).pipe(
			switchMap((ids) => {
				return this.colaboradorService.obterColaboradores({ ids });
			})
		);
		this.atualizarGrid();
		this.niveis = [];
		return zip(ficha$, colaboradores$).pipe(
			tap((result) => {
				result[0].niveis.forEach((nivel, index) => {
					const fichaIdNivel$ = result[0].niveis[index].id;
					const nivelId$ = +this.nivelId;
					if (fichaIdNivel$ === nivelId$) {
						this.niveis.push(result[0].niveis[index]);
					}
				});
				this.ficha = result[0];
				this.colaboradores = result[1];
				this.loading = false;
				this.cd.detectChanges();
			})
		);
	}

	incluirAlunosHandler() {
		if (!this.loading) {
			const classSizeModal =
				window.innerWidth < 580
					? "modal-incluir-aluno-nivel-ficha modal-sm"
					: "modal-mxl";
			const ref = this.modalService.open(
				"Adicionar alunos",
				IncluirAlunosFichaModalComponent,
				PactoModalSize.LARGE,
				classSizeModal
			);
			const modal: IncluirAlunosFichaModalComponent = ref.componentInstance;
			modal.loadData(this.ficha, this.nivelId, null, "ficha-aluno");
			ref.result.then(
				(dto) => {
					const alunoInserido = this.alunoInserido.nativeElement.innerHTML;
					this.snotify.success(alunoInserido);
					this.loadData(this.ficha.id).subscribe();
					this.tableData.reloadData();
					this.sessionService.notificarRecursoEmpresa(
						RecursoSistema.INSERIU_ALUNO_FICHATECNICA_GRD
					);
				},
				() => {}
			);
		}
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			const alunosRemover = new Array<any>();
			alunosRemover.push($event.row);
			const ref = this.modalService.open(
				"Exclusão de Aluno",
				FichaDeleteModalComponent,
				PactoModalSize.MEDIUM,
				"modal-remover-aluno-nivel"
			);
			const modal: FichaDeleteModalComponent = ref.componentInstance;
			modal.loadData(
				alunosRemover,
				this.nivel,
				this.fichaId,
				this.table.allCheck === true
			);
			this.cd.detectChanges();
			ref.result.then((dto) => {
				this.removerAlunos(dto);
			});
		}
		if ($event.iconName === "edit") {
			const ref = this.modalService.open(
				"Editar aluno",
				FichaNivelAlunoEditModalComponent,
				PactoModalSize.MEDIUM,
				"modal-editar-nivel-aluno"
			);
			const modal: FichaNivelAlunoEditModalComponent = ref.componentInstance;
			modal.loadData($event.row, this.nivel.id);
			ref.result.then(
				(dto) => {
					this.atualizarGrid();
					setTimeout(() => {
						this.tableData.reloadData();
						this.cd.detectChanges();
					}, 500);
				},
				() => {}
			);
		}
	}

	removerAlunos(alunosRemover) {
		const id = this.nivel.id;
		const $idalunos: number[] = [];
		alunosRemover.forEach((aluno, index) => {
			$idalunos[index] = aluno.id;
		});
		this.nivelService
			.removerAlunos(id, $idalunos, this.table.allCheck === true)
			.subscribe((result) => {
				this.loadData(this.fichaId).subscribe(() => {
					if (result === "Aluno_possui_avaliacao") {
						const alunosAvaliacao =
							this.alunosAvaliacao.nativeElement.innerHTML;
						this.snotify.error(alunosAvaliacao);
					} else {
						const alunosRemovidos =
							this.alunosRemovidos.nativeElement.innerHTML;
						this.tableData.reloadData();
						this.snotify.success(alunosRemovidos);
					}
				});
			});
	}

	abrirModalRemover() {
		if (!this.nenhumSelecionado) {
			const alunosRemover = new Array<any>();
			this.tableData.rawData.forEach((item) => {
				if (this.table.checkeds.includes(item.id)) {
					alunosRemover.push(item);
				}
			});
			const ref = this.modalService.open(
				"Exclusão de Aluno",
				FichaDeleteModalComponent,
				PactoModalSize.MEDIUM,
				"modal-remover-aluno-nivel"
			);
			const modal: FichaDeleteModalComponent = ref.componentInstance;
			modal.loadData(
				alunosRemover,
				this.nivel,
				this.fichaId,
				this.table.allCheck === true
			);
			this.cd.detectChanges();
			ref.result.then((dto) => {
				this.removerAlunos(dto);
			});
		}
	}

	get nenhumSelecionado() {
		return (
			(!this.table.checkeds || this.table.checkeds.length === 0) &&
			this.table.allCheck === false
		);
	}
}
