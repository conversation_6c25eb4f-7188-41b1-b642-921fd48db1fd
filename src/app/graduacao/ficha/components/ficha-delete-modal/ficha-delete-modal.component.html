<div class="p">
	<p>Você selecionou para exclusão do Nivel</p>
</div>
<div *ngFor="let aluno of alunosModal" class="aluno-item">
	<div class="nome-aluno">
		{{ aluno }}
	</div>
</div>

<div *ngIf="todos === true" class="aluno-item">
	<div class="nome-aluno">Todos os alunos</div>
</div>
<div *ngIf="qtAlunos > 0" class="qtAlunos">+{{ qtAlunos }} alunos</div>
<div class="p">Tem certeza que deseja remover?</div>
<div class="aviso">ATENÇÃO: esta ação não poderá ser revertida</div>
<div class="div-button">
	<button
		(click)="cancelar()"
		class="primary-button btn btn-cancelar"
		type="button">
		Cancelar
	</button>
</div>
<div class="div-button">
	<button
		(click)="salvarHandler()"
		class="btn-primary primary-button btn btn-excluir"
		type="button">
		Confirmar Exclusão
	</button>
</div>
