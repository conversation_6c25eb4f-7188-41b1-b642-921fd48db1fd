import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Nivel } from "../../../../microservices/graduacao/nivel/nivel.model";
import { Ficha } from "../../../../microservices/graduacao/ficha/ficha.model";

@Component({
	selector: "pacto-ficha-delete-modal",
	templateUrl: "./ficha-delete-modal.component.html",
	styleUrls: ["./ficha-delete-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FichaDeleteModalComponent implements OnInit {
	nivel: Nivel;
	ficha: Ficha;
	alunosModal: any[] = [];
	alunos: any[] = [];
	qtAlunos = 0;
	todos = false;

	constructor(private modal: NgbActiveModal, private cd: ChangeDetectorRef) {}

	ngOnInit() {}

	loadData(alunos, nivel, ficha, todos) {
		this.todos = todos;
		if (todos === true) {
			this.qtAlunos = 0;
		} else {
			this.qtAlunos = alunos.length - 5;
			this.alunos = alunos;
		}
		this.nivel = nivel;
		alunos.forEach((aluno, index) => {
			if (index <= 4) {
				this.alunosModal[index] = aluno.nome;
			}
		});
		this.cd.detectChanges();
	}

	salvarHandler() {
		this.modal.close(this.alunos);
	}

	cancelar() {
		this.modal.close();
	}
}
