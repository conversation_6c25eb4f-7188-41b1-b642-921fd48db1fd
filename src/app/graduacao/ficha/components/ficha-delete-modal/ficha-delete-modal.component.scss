@import "projects/ui/assets/import.scss";

.primary-button {
	align: left;
	height: 42px;
	width: 200px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-family: "Nunito Sans";
	font-style: normal;
	font-size: 16px;
	font-weight: bold;
	line-height: 10px;
	border: none;
}

.div-button {
	float: left;
	padding-top: 20px;
	margin-left: 35px;
	margin-bottom: 20px;
}

.btn-cancelar {
	background: #ffffff;
	color: #25598b;
	outline: none !important;
	box-shadow: none;
}

.btn-cancelar:hover {
	box-shadow: 0px 4px 6px 0px #e4e5e6;
}

.btn-excluir {
	background: #db2c3d;
	outline: none;
	button: hover;
}

.btn-excluir:hover {
	box-shadow: 0px 4px 6px 0px #cdcecf;
}

.div {
	position: absolute;
	width: 500px;
	height: 488px;
	left: 0px;
	top: 0px;
}

.nome-aluno {
	margin-left: 35px;
	color: #696969;
}

.p {
	font-family: Nunito Sans;
	font-style: normal;
	font-weight: normal;
	font-size: 14px;
	line-height: 26px;
	margin-left: 35px;
	margin-top: 30px;
}

.qtAlunos {
	font-size: 18px;
	color: #db2c3d;
	margin-left: 35px;
}

.aviso {
	margin-top: 0.5px;
	font-size: 14px;
	color: #db2c3d;
	margin-left: 35px;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	::ng-deep .modal-remover-aluno-nivel {
		.modal-conteudo {
			text-align: center;

			.p {
				margin-left: 0;
			}

			.aviso {
				margin-left: 0;
			}

			.div-button {
				margin-left: 0;
				width: 100%;
				max-width: 100%;

				.primary-button {
					justify-content: center;
					display: initial;
					width: 90%;
				}
			}

			.nome-aluno {
				margin-left: 0;
			}
		}
	}
}
