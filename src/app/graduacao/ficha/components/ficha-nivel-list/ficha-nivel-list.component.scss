@import "src/assets/scss/pacto/plataforma-import";

.titulo-card-wrapper {
	display: flex;
}

.actions-menu {
	display: grid;
	grid-template-columns: 1fr;
	margin-bottom: 30px;
	gap: 30px;

	.action {
		padding: 30px;
		display: flex;
		border-radius: 4px;
		box-shadow: 0 0 4px 0px #e5e9f2;
		border: solid 1px #dddee1;
		background-color: $branco;
		cursor: pointer;

		&.disabled {
			cursor: not-allowed;
			background-color: $cinzaClaroPri;
		}
	}

	.icon-box {
		width: 70px;
		height: 70px;
		border-radius: 35px;
		background-color: $azulPacto01;
		line-height: 70px;
		padding-left: 20px;
		color: $branco;
		flex-shrink: 0;
		font-size: 32px;
		margin-right: 15px;
	}

	.action-title {
		@extend .type-h5;
		color: $pretoPri;
		margin-bottom: 5px;
	}

	.action-description {
		@extend .type-p-small;
		color: $cinza03;
	}
}

@media (min-width: $bootstrap-breakpoint-lg) {
	.actions-menu {
		grid-template-columns: 1fr 1fr;
	}
}

@media (min-width: $bootstrap-breakpoint-xl) {
	.actions-menu {
		grid-template-columns: 1fr 1fr 1fr;
	}
}

.titulo-card-ficha {
	@extend .type-h2;
	flex-grow: 1;
	align-items: center;
	margin-bottom: 30px;

	span {
		cursor: pointer;
	}

	.pct {
		font-size: 28px;
	}
}

.plain-card-wrapper {
	border-radius: 4px;
	background-color: $branco;
	box-shadow: 0 0 4px 0px $geloPri;
	border: solid 1px #dddee1;
}

.header-row {
	display: flex;
	@extend .type-h6-bold;
	padding: 30px 0px;

	> * {
		flex-basis: 25%;
	}

	color: $pretoPri;

	.pct {
		font-size: 18px;
	}

	.drag-handler {
		padding: 0px 30px;
		flex-basis: 76px;
		flex-grow: 0;
	}
}

.content-row {
	display: flex;
	align-items: center;
	cursor: pointer;
	border-top: 1px solid $canetaBicPastel;
	color: $pretoPri;
	@extend .type-h6-bold;

	> * {
		flex-basis: 25%;
	}

	.drag-handler {
		cursor: grab;
		height: 117px;
		padding: 0px 30px;
		flex-basis: 50px;
		flex-grow: 0;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.nivel-image {
		display: flex;
		line-height: 60px;

		.nivel-nome {
			margin-left: 15px;
		}
	}

	.acoes {
		display: flex;

		.pct {
			font-size: 18px;
			cursor: pointer;
		}

		.pct-edit {
			position: relative;
			top: 7px;
		}

		.pct-toggle-left,
		.pct-toggle-right {
			top: 2px;
			position: relative;
			padding-right: 5px;
		}

		.toggle-box {
			margin-left: 15px;
			color: $verdinho05;
			@extend .type-h6;
			padding: 5px 0px;

			&.icon-inativo {
				color: $hellboyPri;
			}
		}
	}
}

.cdk-drag-preview {
	border-radius: 2px;
	box-sizing: border-box;
	opacity: 0.8;
	background-color: $branco;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
		0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
	opacity: 1;
	background-color: $cinzaClaroPri;
}

.cdk-drag-animating {
	transition: transform 150ms cubic-bezier(0, 0, 0.2, 1);
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	::ng-deep .pct-content {
		.nivel-nome {
			line-height: 1;
		}
	}

	.titulo-card-wrapper {
		display: block;

		.titulo-card-ficha {
			font-size: 28px;
			margin-bottom: 8px;
		}

		::ng-deep pacto-cat-button .pacto-button {
			top: 6px;
			width: 100%;
			margin-bottom: 20px;
		}
	}

	.actions-menu {
		.action {
			display: block;
			padding: 20px;
		}

		.div-icon-box {
			display: flex;
			justify-content: center;
			align-items: center;

			.icon-box {
				width: 55px;
				height: 55px;
				border-radius: 35px;
				background-color: #2b68a3;
				line-height: 58px;
				padding-left: 17px;
				color: #ffffff;
				flex-shrink: 0;
				font-size: 26px;
				margin-right: 15px;
			}
		}

		.aux {
			.action-title {
				font-size: 20px;
				text-align: center;
			}

			.action-description {
				text-align: justify;
			}
		}
	}

	.div-table-niveis {
		overflow: auto;

		.plain-card-wrapper {
			width: 1000px;
		}
	}
}
