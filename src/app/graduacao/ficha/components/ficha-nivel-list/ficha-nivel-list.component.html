<pacto-cat-layout-v2>
	<div class="titulo-card-wrapper">
		<div class="titulo-card-ficha">
			<span [routerLink]="['/graduacao', 'ficha']">
				<i class="pct pct-arrow-left"></i>
				{{ ficha?.nome }}
			</span>
		</div>
		<pacto-cat-button
			(click)="novoNivel()"
			[icon]="'pct pct-plus-circle'"
			[label]="'NOVO NIVEL'"></pacto-cat-button>
	</div>

	<div class="actions-menu">
		<div [routerLink]="['/graduacao/avaliacoes-progresso']" class="action">
			<div class="div-icon-box">
				<div class="icon-box">
					<i class="pct pct-evaluation"></i>
				</div>
			</div>
			<div class="aux">
				<div class="action-title">Avaliar Progresso</div>
				<div class="action-description">
					Avaliar o progresso dos alunos que já estão na ficha visando-os
					avançar para o próximo nível da ficha
				</div>
			</div>
		</div>
		<div class="action disabled">
			<div class="div-icon-box">
				<div class="icon-box">
					<i class="pct pct-modality"></i>
				</div>
			</div>
			<div class="aux">
				<div class="action-title">Avaliar Livremente</div>
				<div class="action-description">
					Avaliar de forma livre os alunos com a opção de avançar ou não para um
					nível. Pode ser usado para nivelar novos alunos.
				</div>
			</div>
		</div>
		<div (click)="incluirAlunosHandler()" class="action">
			<div class="div-icon-box">
				<div class="icon-box">
					<i class="pct pct-user-plus"></i>
				</div>
			</div>
			<div class="aux">
				<div class="action-title">Incluir Alunos</div>
				<div class="action-description">
					Incluir e definir os níveis de novos alunos de maneira rápida e
					direta. Sem avaliações
				</div>
			</div>
		</div>
	</div>

	<div class="div-table-niveis">
		<div
			(cdkDropListDropped)="drop($event)"
			cdkDropList
			class="plain-card-wrapper">
			<div class="header-row">
				<div class="drag-handler"></div>
				<div>
					<i class="pct pct-level"></i>
					Nível
				</div>
				<div>
					<i class="pct pct-skill"></i>
					Atividades
				</div>
				<div>
					<i class="pct pct-users"></i>
					Alunos
				</div>
				<div>
					<i class="pct pct-settings"></i>
					Ações
				</div>
			</div>

			<ng-container *ngFor="let nivel of niveis">
				<div (click)="infoHandler(nivel)" cdkDrag class="content-row">
					<div cdkDragHandle class="drag-handler">
						<div class="colum-icon">
							<i class="pct pct-grid"></i>
						</div>
					</div>
					<div class="nivel-image">
						<pacto-cat-person-avatar
							[diameter]="60"
							[uri]="nivel.fotoUri"></pacto-cat-person-avatar>
						<div class="nivel-nome">
							{{ nivel?.nome }}
						</div>
					</div>
					<div>{{ nivel.nOfAtividades }}</div>
					<div>{{ nivel.nrAlunos }}</div>
					<div>
						<div class="acoes">
							<div>
								<i (click)="editNivelHandler(nivel)" class="pct pct-edit"></i>
							</div>
							<div
								(click)="toggleSituacaoNivel($event, nivel)"
								*ngIf="!nivel?.ativo"
								class="toggle-box icon-inativo">
								<i class="pct pct-toggle-left" title="Ativar"></i>
								Inativo
							</div>
							<div
								(click)="toggleSituacaoNivel($event, nivel)"
								*ngIf="nivel?.ativo"
								class="toggle-box icon-ativo">
								<i class="pct pct-toggle-right" title="Desativar"></i>
								Ativo
							</div>
						</div>
					</div>
				</div>
			</ng-container>
		</div>
	</div>
</pacto-cat-layout-v2>

<span #alunoInserido [hidden]="true">Alunos inseridos com sucesso.</span>
