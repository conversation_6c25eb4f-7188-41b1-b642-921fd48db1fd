import { CdkDragDrop } from "@angular/cdk/drag-drop";
import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";
import { Subscription, zip } from "rxjs";
import { tap } from "rxjs/operators";

import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { Ficha } from "src/app/microservices/graduacao/ficha/ficha.model";
import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { NivelSimple } from "src/app/microservices/graduacao/nivel/nivel.model";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { Cliente } from "src/app/microservices/personagem/cliente/cliente.model";
import { ClienteService } from "src/app/microservices/personagem/cliente/cliente.service";
import { Colaborador } from "src/app/microservices/personagem/colaborador/colaborador.model";
import { ColaboradorService } from "src/app/microservices/personagem/colaborador/colaborador.service";
import { RecursoSistema } from "../../../../base/base-core/recurso-sistema/recurso-sistema-enum.model";
import { AtividadeGraduacaoService } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.service";
import { IncluirAlunosNivelModalComponent } from "../../../components/incluir-alunos-nivel-modal/incluir-alunos-nivel-modal.component";
import { FichaNivelModalComponent } from "../ficha-nivel-modal/ficha-nivel-modal.component";

@Component({
	selector: "pacto-ficha-nivel-list",
	templateUrl: "./ficha-nivel-list.component.html",
	styleUrls: ["./ficha-nivel-list.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FichaNivelListComponent implements OnInit, OnDestroy {
	@ViewChild("alunoInserido", { static: true }) alunoInserido;
	niveis: NivelSimple[];
	ficha: Ficha;
	atividades: any;
	colaboradores: Colaborador[] = [];
	alunos: Cliente[];
	private reorderSubscription: Subscription;

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private colaboradorService: ColaboradorService,
		private clienteService: ClienteService,
		private fichaService: FichaService,
		private nivelService: NivelService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private atividadeService: AtividadeGraduacaoService
	) {}

	ngOnInit() {
		this.route.params.subscribe((params: any) => {
			if (params.id) {
				this.loadData(params.id).subscribe();
			}
		});
	}

	incluirAlunosHandler() {
		const refnivel = this.modalService.open(
			"Incluir Alunos",
			IncluirAlunosNivelModalComponent
		);
		const modalnivel: IncluirAlunosNivelModalComponent =
			refnivel.componentInstance;
		this.atividadeService
			.atividadesAtivas()
			.subscribe((dados) => (this.atividades = dados));
		modalnivel.loadData(
			this.atividades,
			this.ficha,
			this.niveis.filter((i) => i.ativo === true)
		);
		refnivel.result.then((mens) => {
			this.loadData(this.ficha.id).subscribe();
			this.cd.detectChanges();
			this.snotifyService.success(mens);
			this.sessionService.notificarRecursoEmpresa(
				RecursoSistema.INSERIU_ALUNO_FICHATECNICA_GRD
			);
		});
	}

	ngOnDestroy() {
		if (this.reorderSubscription) {
			this.reorderSubscription.unsubscribe();
		}
	}

	drop(event: CdkDragDrop<string[]>) {
		this.reordenarArray(event.previousIndex, event.currentIndex);
		this.persistOrder();
	}

	private persistOrder() {
		if (this.reorderSubscription) {
			this.reorderSubscription.unsubscribe();
		}
		const idsInOrder = [];
		this.niveis.forEach((nivel) => {
			idsInOrder.push(nivel.id);
		});
		this.reorderSubscription = this.fichaService
			.reordenarNiveis(this.ficha.id, idsInOrder)
			.subscribe(() => {
				this.snotifyService.success("Níveis reordenados com sucesso.");
			});
	}

	private reordenarArray(previous: number, current: number) {
		const removed = this.niveis.splice(previous, 1)[0];
		this.niveis.splice(current, 0, removed);
		this.cd.detectChanges();
	}

	private loadData(id) {
		const ficha$ = this.fichaService.obterFichaPorId(id);
		return zip(ficha$).pipe(
			tap((result) => {
				this.niveis = result[0].niveis;
				this.ficha = result[0];
				this.cd.detectChanges();
			})
		);
	}

	toggleSituacaoNivel($event: MouseEvent, item: NivelSimple) {
		$event.stopPropagation();
		this.nivelService.updateStatus(item.id, !item.ativo).subscribe(() => {
			if (item.ativo === true) {
				this.snotifyService.success("Nível desativado com sucesso.");
			} else {
				this.snotifyService.success("Nível ativado com sucesso.");
			}
			this.loadData(this.ficha.id).subscribe();
		});
	}

	infoHandler(nivel) {
		const modalRef = this.modalService.open(
			"Detalhes do Nível",
			FichaNivelModalComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.loadNivel(nivel.id);
	}

	novoNivel() {
		this.route.params.subscribe((resp) => {
			const id = resp.id;
			this.router.navigate(["graduacao", "ficha", id, "niveis", "adicionar"]);
		});
	}

	editNivelHandler(nivel: NivelSimple) {
		const id = this.ficha.id;
		this.router.navigate(["graduacao", "ficha", id, "niveis", nivel.id]);
	}
}
