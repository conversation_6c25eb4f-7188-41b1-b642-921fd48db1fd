import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { FichaRoutingModule } from "./ficha-routing.module";
import { FichaListComponent } from "./components/ficha-list/ficha-list.component";
import { FichaEditModalComponent } from "./components/ficha-edit-modal/ficha-edit-modal.component";
import { FichaNivelListComponent } from "./components/ficha-nivel-list/ficha-nivel-list.component";
import { FichaNivelEditComponent } from "./components/ficha-nivel-edit/ficha-nivel-edit.component";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { FichaNivelModalComponent } from "./components/ficha-nivel-modal/ficha-nivel-modal.component";
import { ColorPickerModalComponent } from "./components/color-picker-modal/color-picker-modal.component";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { PersonagemMsModule } from "src/app/microservices/personagem/personagem-ms.module";
import { GraduacaoModule } from "src/app/graduacao/graduacao.module";
import { FichaDeleteModalComponent } from "./components/ficha-delete-modal/ficha-delete-modal.component";
import { FichaAlunoListComponent } from "./components/ficha-aluno-list/ficha-aluno-list.component";
import { FichaNivelAlunoEditModalComponent } from "./components/ficha-nivel-aluno-edit-modal/ficha-nivel-aluno-edit-modal.component";

@NgModule({
	declarations: [
		FichaListComponent,
		FichaEditModalComponent,
		FichaNivelListComponent,
		FichaNivelEditComponent,
		FichaNivelModalComponent,
		ColorPickerModalComponent,
		FichaAlunoListComponent,
		FichaDeleteModalComponent,
		FichaNivelAlunoEditModalComponent,
	],
	imports: [
		CommonModule,
		RouterModule,
		GraduacaoModule,
		GraduacaoMsModule,
		PersonagemMsModule,
		BaseSharedModule,
		FichaRoutingModule,
		DragDropModule,
	],
	entryComponents: [
		FichaEditModalComponent,
		ColorPickerModalComponent,
		FichaNivelModalComponent,
		FichaDeleteModalComponent,
		FichaNivelAlunoEditModalComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class FichaModule {}
