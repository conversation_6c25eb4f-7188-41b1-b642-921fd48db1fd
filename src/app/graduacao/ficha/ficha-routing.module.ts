import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { FichaListComponent } from "./components/ficha-list/ficha-list.component";
import { FichaNivelListComponent } from "./components/ficha-nivel-list/ficha-nivel-list.component";
import { FichaNivelEditComponent } from "./components/ficha-nivel-edit/ficha-nivel-edit.component";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { ModuleName } from "@base-core/modulo/modulo.model";

export const routes: Routes = [
	{
		path: "",
		data: { module: ModuleName.GRADUACAO },
		children: [
			{
				path: "",
				component: FichaListComponent,
			},
			{
				path: ":id/niveis",
				component: FichaNivelListComponent,
			},
			{
				path: ":fichaId/niveis/:id",
				component: FichaNivelEditComponent,
			},
		],
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes), GraduacaoMsModule],
})
export class FichaRoutingModule {}
