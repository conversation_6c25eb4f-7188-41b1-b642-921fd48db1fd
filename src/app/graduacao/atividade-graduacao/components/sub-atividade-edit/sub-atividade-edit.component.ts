import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AtividadeGraduacaoService } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.service";
import { SnotifyService } from "ng-snotify";
import { SubAtividades } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";

@Component({
	selector: "pacto-sub-atividade-edit",
	templateUrl: "./sub-atividade-edit.component.html",
	styleUrls: ["./sub-atividade-edit.component.scss"],
})
export class SubAtividadeEditComponent implements OnInit {
	editAtividade = false;
	editSubAtividade = false;
	atividadeId: number;
	subAtividades: Array<SubAtividades>;
	index: number;
	formGroup = new FormGroup({
		id: new FormControl(""),
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		atividadeId: new FormControl(""),
	});

	constructor(
		private openModal: NgbActiveModal,
		private atividadeGradService: AtividadeGraduacaoService,
		private snotify: SnotifyService
	) {}

	ngOnInit() {}

	loadSubAtividade(item) {
		this.editAtividade = true;
		this.formGroup.setValue({
			id: item.id,
			nome: item.nome,
			atividadeId: item.atividadeId,
		});
	}

	saveHandler() {
		if (this.getDto().id) {
			this.editarSubAtividade();
		} else {
			this.criarSubAtividade();
		}
	}

	incluirHandler() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			const dto = this.getDto();
			this.openModal.close(dto);
		}
	}

	criarSubAtividade() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			const dto = this.getDto();
			dto.atividadeId = this.atividadeId;
			this.atividadeGradService.criarSubAtividade(dto).subscribe(() => {
				this.snotify.success("SubAtividade criada com sucesso.");
				this.openModal.close(dto);
			});
		}
	}

	editarSubAtividade() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			this.atividadeGradService
				.editarSubAtividade(this.getDto())
				.subscribe(() => {
					this.snotify.success("SubAtividade editada com sucesso.");
					this.openModal.close(this.getDto());
				});
		}
	}

	private getDto() {
		const dto = this.formGroup.getRawValue();
		return dto;
	}
}
