<div class="edit-sub-atividade">
	<pacto-cat-form-input
		[control]="formGroup.get('nome')"
		[errorMsg]="'Defina um nome com ao menos 3 caracteres'"
		i18n-label="@@atividade-edit:form:nome"
		label="Nome"
		placeholder="Nome da SubAtividade"></pacto-cat-form-input>

	<div *ngIf="!editSubAtividade" class="button-modal">
		<button
			(click)="saveHandler()"
			*ngIf="editAtividade"
			class="btn btn-primary"
			i18n="@@buttons:salvar"
			id="btn-edit-sub-atividade">
			Salvar
		</button>
		<button
			(click)="incluirHandler()"
			*ngIf="!editAtividade"
			class="btn btn-primary"
			i18n="@@buttons:salvar"
			id="btn-incluir-sub-atividade">
			Incluir
		</button>
	</div>
	<div *ngIf="editSubAtividade" class="button-modal">
		<button
			(click)="incluirHandler()"
			class="btn btn-primary"
			i18n="@@buttons:salvar"
			id="btn-alterar-sub-atividade">
			Alterar
		</button>
	</div>
</div>
