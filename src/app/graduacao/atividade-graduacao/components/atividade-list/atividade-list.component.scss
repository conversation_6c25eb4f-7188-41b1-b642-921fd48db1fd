@import "src/assets/scss/pacto/plataforma-import.scss";

.page-title {
	@extend .type-h2;
	color: $pretoPri;
	margin-bottom: 30px;
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.div-atividade-graduacao {
		.page-title {
			font-size: 28px;
			text-align: center;
			margin-bottom: 8px;
		}

		::ng-deep pacto-relatorio {
			.pacto-table-title-block {
				display: block;
				padding: 10px;

				.actions {
					display: block;
					margin-top: 12px;

					.div-actions-btn {
						justify-content: space-between;

						.div-share-button .btn {
							margin: 0;
						}
					}

					.div-table-button {
						.btn {
							margin: 0;
							width: 100%;
							margin-top: 12px;
						}
					}
				}
			}

			.table-content {
				overflow: auto;
			}
		}

		::ng-deep pacto-cat-layout-v2 {
			pacto-share-button {
				.exportar-dropdown {
					position: absolute !important;
					top: initial !important;
					left: initial !important;
					transform: initial !important;
				}
			}

			.table-content {
				padding: 0px 12px 30px;

				.table {
					width: 1000px;
					max-width: 1000px;
				}
			}

			.footer-row {
				display: block;

				.div-pagination {
					margin-left: 0;

					ngb-pagination {
						justify-content: center !important;
					}
				}

				.div-show-and-select {
					margin-left: 0;
					justify-content: space-evenly;

					> * {
						margin: 0;
					}
				}
			}
		}
	}
}

.margin-left-20px {
	margin-left: 20px;
}
