import {
	ChangeDetectionStrategy,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";

import { SnotifyService } from "ng-snotify";

import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { AtividadeGraduacaoService } from "src/app/microservices/graduacao/atividade-graduacao/atividade-graduacao.service";
import { AtividadeEditModalComponent } from "../atividade-edit/atividade-edit-modal.component";
import { SessionService } from "@base-core/client/session.service";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-atividade-list",
	templateUrl: "./atividade-list.component.html",
	styleUrls: ["./atividade-list.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AtividadeListComponent implements OnInit {
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;
	@ViewChild("statusColumnName", { static: true }) statusColumnName;
	@ViewChild("descricaoColumnName", { static: true }) descricaoColumnName;
	@ViewChild("atividadeColumnName", { static: true }) atividadeColumnName;
	@ViewChild("dataCriacaoColumnName", { static: true }) dataCriacaoColumnName;
	@ViewChild("dataCriacaoCelula", { static: true }) dataCriacaoCelula;

	constructor(
		private atividadeGradService: AtividadeGraduacaoService,
		private snotify: SnotifyService,
		private rest: RestService,
		private modalService: ModalService,
		public sessionService: SessionService
	) {}

	table: PactoDataGridConfig;

	ngOnInit() {
		this.configTable();
	}

	iconClickHandler($event: any) {
		const action = $event.iconName;
		if (action === "ativar") {
			this.updateStatusHandler($event.row, true);
		} else if (action === "desativar") {
			this.updateStatusHandler($event.row, false);
		}
	}

	btnClickHandler($event) {
		if ($event === "add") {
			const modal = this.modalService.open(
				"Criar Atividade",
				AtividadeEditModalComponent,
				PactoModalSize.MEDIUM
			);
			modal.result.then(
				(content) => {
					this.atividadeGradService.criarAtividade(content).subscribe(() => {
						this.snotify.success("Atividade criada com sucesso.");
						this.tableData.reloadData();
					});
				},
				() => {}
			);
		}
	}

	rowClickHandler(item) {
		const modal = this.modalService.open(
			"Editar Atividade",
			AtividadeEditModalComponent,
			PactoModalSize.MEDIUM
		);
		const instance: AtividadeEditModalComponent = modal.componentInstance;
		instance.loadAtividade(item);
		modal.result.then(
			(content) => {
				this.atividadeGradService
					.editarAtividade(item.id, content)
					.subscribe(() => {
						this.snotify.success("Atividade editada com sucesso.");
						this.tableData.reloadData();
					});
			},
			() => {}
		);
	}

	private updateStatusHandler(item: any, status: boolean) {
		const dto = {
			nome: item.nome,
			descricao: item.descricao,
			ativa: status,
		};
		this.modalService
			.confirm(
				status ? "Habilitar Atividade" : "Desabilitar Atividade",
				`Tem certeza que deseja ${
					status ? "habilitar" : "desabilitar"
				} a atividade: '${item.nome}' ?`,
				status ? "Ativar" : "Desativar"
			)
			.result.then(
				() => {
					console.log(dto);
					this.atividadeGradService
						.editarAtividade(item.id, dto)
						.subscribe(() => {
							this.snotify.success(
								"Atividade " + (status ? "habilitada" : "desabilitada")
							);
							this.tableData.reloadData();
						});
				},
				() => {}
			);
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlGraduacao("atividades"),
			quickSearch: true,
			rowClick: true,
			buttons: {
				id: "nova-atividade",
				conteudo: this.buttonName,
				nome: "add",
			},
			columns: [
				{
					nome: "nome",
					titulo: this.atividadeColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "descricao",
					titulo: this.descricaoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "status",
					titulo: this.statusColumnName,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					valueTransform: (ativo) => (ativo ? "Ativo" : "Inativo"),
					campo: "ativa",
				},
				{
					nome: "dataCriacao",
					titulo: this.dataCriacaoColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					celula: this.dataCriacaoCelula,
					campo: "dataCriacao",
				},
			],
			actions: [
				{
					nome: "desativar",
					iconClass: "fa fa-minus-square-o",
					tooltipText: "Desativar",
					showIconFn: (row) => row.ativa === true,
				},
				{
					nome: "ativar",
					iconClass: "fa fa-check-square-o",
					tooltipText: "Ativar",
					showIconFn: (row) => row.ativa === false,
				},
			],
		});
	}
}
