import { Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { TreinoApiProgramaService } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { AtividadeGraduacaoService } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.service";

@Component({
	selector: "pacto-sub-atividade-delete",
	templateUrl: "./sub-atividade-delete.component.html",
	styleUrls: ["./sub-atividade-delete.component.scss"],
})
export class SubAtividadeDeleteComponent implements OnInit {
	subAtividadeId: number;
	subAtividadeNome: string;

	constructor(
		private modal: NgbActiveModal,
		private programaService: TreinoApiProgramaService,
		private snotify: SnotifyService,
		private atividadeService: AtividadeGraduacaoService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {}

	excluirHandler() {
		if (this.subAtividadeId) {
			this.atividadeService
				.removerSubAtividade(this.subAtividadeId)
				.subscribe((response) => {
					if (response.error) {
						if (
							response.error.error.meta.message ===
							"sub_atividade_com_vinculo_resposta_avaliacao"
						) {
							this.snotify.error(
								"Não foi possível remover a subAtividade pois a mesma possui vínculo com uma resposta de avaliação de progresso."
							);
						} else {
							this.snotify.error(response.error.error.meta.message);
						}
					} else {
						this.snotify.success("SubAtividade removida com sucesso!");
					}
					this.openModal.close();
				});
		} else {
			this.openModal.close(true);
		}
	}

	cancelarHandler() {
		this.modal.dismiss();
	}
}
