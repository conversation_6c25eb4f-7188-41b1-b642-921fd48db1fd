import {
	ChangeDete<PERSON><PERSON><PERSON>,
	<PERSON>mponent,
	<PERSON><PERSON><PERSON>roy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { TraducoesXinglingComponent } from "ui-kit";
import { SeletorImagemUserComponent } from "old-ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { SubAtividadeEditComponent } from "../sub-atividade-edit/sub-atividade-edit.component";
import { AtividadeGraduacaoService } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.service";
import { SnotifyService } from "ng-snotify";
import { SubAtividades } from "../../../../microservices/graduacao/atividade-graduacao/atividade-graduacao.model";
import { SubAtividadeDeleteComponent } from "../sub-atividade-delete/sub-atividade-delete.component";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { Subscription } from "rxjs";

@Component({
	selector: "pacto-atividade-edit",
	templateUrl: "./atividade-edit-modal.component.html",
	styleUrls: ["./atividade-edit-modal.component.scss"],
})
export class AtividadeEditModalComponent implements OnInit, OnDestroy {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("seletorImagem", { static: true })
	seletorImagem: SeletorImagemUserComponent;
	edit = false;
	id;

	constructor(
		private openModal: NgbActiveModal,
		private rest: RestService,
		private modalService: ModalService,
		private atividadeService: AtividadeGraduacaoService,
		private cd: ChangeDetectorRef,
		private snotify: SnotifyService
	) {}

	formGroup = new FormGroup({
		nome: new FormControl("", [Validators.required, Validators.minLength(3)]),
		descricao: new FormControl(""),
		ativa: new FormControl(false),
		dataCriacao: new FormControl(""),
		subAtividades: new FormControl([]),
	});
	subAtividades: Array<SubAtividades> = [];
	subs: SubAtividades[];
	private reorderSubscription: Subscription;

	listSubAtividades = [
		{
			id: 1,
			nome: "teste 01",
			atividade_id: 1,
		},
		{
			id: 2,
			nome: "teste 02",
			atividade_id: 2,
		},
		{
			id: 3,
			nome: "teste 03",
			atividade_id: 3,
		},
		{
			id: 4,
			nome: "teste 04",
			atividade_id: 4,
		},
	];

	ngOnInit() {
		this.loadSubAtividades();
	}

	ngOnDestroy() {
		if (this.reorderSubscription) {
			this.reorderSubscription.unsubscribe();
		}
	}

	loadSubAtividades() {
		if (this.id) {
			this.atividadeService
				.obterSubAtividades(this.id)
				.subscribe((response) => {
					this.formGroup.get("subAtividades").setValue(response);
					this.cd.detectChanges();
				});
		}
		this.subs = this.formGroup.get("subAtividades").value;
	}

	saveHandler() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			const dto = this.getDto();
			this.openModal.close(dto);
		}
	}

	loadAtividade(item) {
		this.edit = true;
		this.id = item.id;
		this.formGroup.setValue({
			nome: item.nome,
			descricao: item.descricao,
			ativa: item.ativa,
			dataCriacao: item.dataCriacao,
			subAtividades: [],
		});
		if (item.imageUri) {
			this.seletorImagem.uriImagem = item.imageUri;
		}
	}

	private getDto() {
		const dto = this.formGroup.getRawValue();
		if (this.seletorImagem.imagemData) {
			dto.image = this.seletorImagem.imagemData;
		}
		return dto;
	}

	get urlLog() {
		return this.rest.buildFullUrlGraduacao(`log/atividades/${this.id}`);
	}

	get getSubAtividades() {
		this.subs = this.formGroup.get("subAtividades").value;
		return this.formGroup.get("subAtividades").value;
	}

	adicionarSubAtividade() {
		this.formGroup.get("nome").markAsTouched();
		if (this.formGroup.valid) {
			const modal = this.modalService.open(
				"Criar SubAtividade",
				SubAtividadeEditComponent,
				PactoModalSize.MEDIUM,
				"editar-sub-atividade"
			);
			const instance: SubAtividadeEditComponent = modal.componentInstance;
			instance.atividadeId = this.id;
			if (this.id) {
				instance.editAtividade = true;
			}
			modal.result.then(
				(response) => {
					if (this.id) {
						this.loadSubAtividades();
					} else {
						this.subAtividades.push(response);
						this.formGroup.get("subAtividades").setValue(this.subAtividades);
						this.subs = this.formGroup.get("subAtividades").value;
						this.cd.detectChanges();
					}
				},
				() => {}
			);
		} else {
			this.snotify.warning("Preencha o nome da atividade para prosseguir.");
		}
	}

	editarSubAtividade(item, index) {
		const modal = this.modalService.open(
			"Editar SubAtividade",
			SubAtividadeEditComponent,
			PactoModalSize.MEDIUM,
			"editar-sub-atividade"
		);
		const instance: SubAtividadeEditComponent = modal.componentInstance;
		if (this.id) {
			instance.loadSubAtividade(item);
		} else {
			instance.index = index;
			instance.editSubAtividade = true;
			instance.loadSubAtividade(item);
		}
		modal.result.then(
			(response) => {
				this.formGroup.get("subAtividades").value[index] = response;
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	removerSubAtividade(subAtividade, index) {
		const modal = this.modalService.open(
			"Remover SubAtividade",
			SubAtividadeDeleteComponent,
			PactoModalSize.MEDIUM,
			"remover-sub-atividade"
		);
		const instance: SubAtividadeDeleteComponent = modal.componentInstance;
		instance.subAtividadeId = subAtividade.id;
		instance.subAtividadeNome = subAtividade.nome;
		modal.result.then(
			(response) => {
				if (response) {
					this.formGroup.get("subAtividades").value.splice(index, 1);
					this.cd.detectChanges();
				} else {
					this.loadSubAtividades();
				}
			},
			() => {}
		);
	}

	drop(event: CdkDragDrop<string[]>) {
		this.reordenarArray(event.previousIndex, event.currentIndex);
		this.persistOrder(event.currentIndex);
	}

	private reordenarArray(previous: number, current: number) {
		const removed = this.subs.splice(previous, 1)[0];
		this.subs.splice(current, 0, removed);
		this.cd.detectChanges();
	}

	private persistOrder(current: number) {
		if (this.reorderSubscription) {
			this.reorderSubscription.unsubscribe();
		}
		const idsInOrder = [];
		this.subs.forEach((subAtividade) => {
			idsInOrder.push(subAtividade.id);
		});
		this.reorderSubscription = this.atividadeService
			.reordenarSubAtividades(this.subs[current].id, idsInOrder)
			.subscribe(() => {});
		console.log(this.subs);
	}
}
