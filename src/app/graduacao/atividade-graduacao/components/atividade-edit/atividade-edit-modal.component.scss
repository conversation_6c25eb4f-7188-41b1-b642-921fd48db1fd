@import "~src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 20px;
}

.button-modal {
	margin: 20px 0px;
	display: flex;
	justify-content: flex-end;

	.btn-primary {
		margin-left: 10px;
	}
}

.titulo-subatividade {
	display: flex;
	justify-content: space-between;
	margin: 0 -20px;
	padding: 8px 20px;
	border-bottom: 1px solid #e9e9e9;

	.titulo {
		color: $preto02;
		font-size: 16px;
		font-weight: 600;
		font-family: "Nunito Sans";
		line-height: 21.82px;
	}

	::ng-deep pacto-cat-button {
		button {
			background-color: $branco;
			border: 1px solid $cinzaPri;
			color: $cinzaPri;

			i {
				color: $cinzaPri !important;
			}

			.content {
				text-transform: none !important;
			}
		}
	}
}

.sub-atividades {
	margin: 0 -20px;

	.row-sub-atividade {
		padding: 8px 20px 8px 5px;
		display: flex;
		justify-content: space-between;
		border-bottom: 1px solid $cinza01;

		.pct-drag {
			cursor: move;
			font-size: 32px;
			line-height: 2;
		}

		.nome-sub-atividade {
			padding-left: 10px;
		}

		.drag-handler {
			display: flex;
		}

		.btn-remover-sub-atividade {
			.icone {
				.pct-edit {
					cursor: pointer;
					margin-right: 5px;
				}

				.pct-trash-2 {
					cursor: pointer;
					color: $hellboyPri;
				}
			}
		}
	}

	.row-sub-atividade:hover {
		border-left: solid 5px $azulimPri;
		background-color: $cinza01;
	}
}

.content-lista-atividade {
	label {
		color: $preto02;
		font-size: 12px;
		line-height: 16px;
		font-weight: normal;
		margin: 0;
	}
}
