import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AtividadeListComponent } from "./components/atividade-list/atividade-list.component";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { PersonagemMsModule } from "src/app/microservices/personagem/personagem-ms.module";
import { GraduacaoModule } from "src/app/graduacao/graduacao.module";
import { ModuleName } from "@base-core/modulo/modulo.model";

export const routes: Routes = [
	{
		path: "",
		data: { module: ModuleName.GRADUACAO },
		children: [
			{
				path: "",
				component: AtividadeListComponent,
			},
		],
	},
];

@NgModule({
	declarations: [AtividadeListComponent],
	imports: [
		CommonModule,
		GraduacaoModule,
		BaseSharedModule,
		RouterModule.forChild(routes),
		GraduacaoMsModule,
		PersonagemMsModule,
		BaseSharedModule,
	],
})
export class AtividadeModule {}
