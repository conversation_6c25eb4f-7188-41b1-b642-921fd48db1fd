import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { GraduacaoModule } from "src/app/graduacao/graduacao.module";
import { GraduacaoMsModule } from "../microservices/graduacao/graduacao-ms.module";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { AvaliacaoAlunoPrintComponent } from "src/app/graduacao/components/avaliacao-aluno-print/avaliacao-aluno-print.component";
import { HomeComponent } from "./home/<USER>";

export const routes: Routes = [
	{
		path: "",
		redirectTo: "home",
	},
	{
		path: "home",
		component: HomeComponent,
	},
	{
		path: "atividade",
		loadChildren: () =>
			import("./atividade-graduacao/atividade.module").then(
				(m) => m.AtividadeModule
			),
	},
	{
		path: "avaliacoes-progresso",
		loadChildren: () =>
			import("./avaliacoes-progresso/avaliacoes-progresso.module").then(
				(m) => m.AvaliacoesProgressoModule
			),
	},
	{
		path: "avaliacoes-livre",
		loadChildren: () =>
			import("./avaliacoes-livres/avaliacao-livre.module").then(
				(m) => m.AvaliacoesLivreModule
			),
	},
	{
		path: "avaliacoes/:avaliacaoId/avaliacao-aluno/:avaliacaoAlunoId/print",
		component: AvaliacaoAlunoPrintComponent,
		data: { fullscreen: true },
	},
	{
		path: "ficha",
		loadChildren: () =>
			import("./ficha/ficha.module").then((m) => m.FichaModule),
	},
];

@NgModule({
	declarations: [],
	imports: [
		GraduacaoModule,
		GraduacaoMsModule,
		BaseSharedModule,
		RouterModule.forChild(routes),
	],
	exports: [BaseSharedModule],
})
export class GraduacaoRoutingModule {}
