import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

const routes: Routes = [
	{
		path: "perfil-v2",
		loadChildren: () =>
			import("./perfil-cliente/perfil-cliente.module").then(
				(m) => m.PerfilClienteModule
			),
	},
	{
		path: "lista-v2",
		loadChildren: () =>
			import("./lista-v2/lista-v2.module").then((m) => m.ListaV2Module),
	},
	{
		path: "perfil-acesso",
		loadChildren: () =>
			import("src/app/base/perfil-acesso/perfil-acesso.module").then(
				(m) => m.PerfilAcessoModule
			),
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class PessoasRoutingModule {}
