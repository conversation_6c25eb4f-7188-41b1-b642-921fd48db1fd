import {
	CUSTOM_ELEMENTS_SCHEMA,
	NO_ERRORS_SCHEMA,
	NgModule,
} from "@angular/core";
import { CommonModule } from "@angular/common";

import { ListaV2RoutingModule } from "./lista-v2-routing.module";
import { CatTolltipModule, UiModule } from "ui-kit";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { ListaV2Component } from "./lista-v2.component";

@NgModule({
	declarations: [ListaV2Component],
	imports: [
		CommonModule,
		ListaV2RoutingModule,
		UiModule,
		BaseSharedModule,
		CatTolltipModule,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ListaV2Module {}
