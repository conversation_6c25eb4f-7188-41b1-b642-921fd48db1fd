<div class="nav-aux">
	<a
		[routerLink]="['/pessoas', 'perfil-v2', matricula]"
		class="top-navigation"
		id="voltar-alunos">
		<i class="pct pct-arrow-left"></i>
		<span i18n="@@tela-do-cliente:title:gympass">Gympass</span>
	</a>
	<div class="acesso">
		<span i18n="@@tela-cliente-gympass:last-access">Último acesso:</span>
		01/07/2021 - 18:45:22
	</div>
</div>
<div class="row align-items-center user-info">
	<div class="custom-width">
		<pacto-cat-card-plain
			*ngIf="dadosPessoais$ | async as dadosPessoais"
			class="user-info__card">
			<pacto-cat-person-avatar
				[uri]="
					dadosPessoais ? dadosPessoais.urlFoto : null
				"></pacto-cat-person-avatar>
			<div class="user-info__detail">
				<h2>
					{{ dadosPessoais?.nome }}
				</h2>
				<div class="user-info__status">
					<p>
						<strong i18n="@@tela-cliente-gympass:matricula">Matrícula:</strong>
						{{ dadosPessoais?.matricula }}
					</p>
					<pacto-cat-situacao-aluno
						[situacaoAluno]="dadosPessoais.situacao"></pacto-cat-situacao-aluno>
					<pacto-cat-situacao-contrato
						[situacaoContrato]="
							(dadosPlano$ | async)?.situacao
						"></pacto-cat-situacao-contrato>
				</div>
			</div>
		</pacto-cat-card-plain>
	</div>
	<div class="col-md-3 custom-margin">
		<pacto-cat-form-input
			[control]="tokenControl"
			class="dark-text"
			i18n-label="@@tela-cliente-gympass:cadastre-token"
			label="Cadastre o token"></pacto-cat-form-input>
	</div>
	<div class="add-button">
		<pacto-cat-button
			(click)="saveToken()"
			i18n="@@tela-cliente-gympass:save"
			label="Salvar"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
<pacto-cat-card-plain>
	<pacto-relatorio
		#tableData
		[filterConfig]="filterConfig"
		[table]="table"
		i18n-tableTitle="@@tela-cliente-gympass:table-title"
		tableTitle="Histórico do gympass"></pacto-relatorio>
</pacto-cat-card-plain>
<ng-template #columnDate>
	<span i18n="@@tela-cliente-gympass:date">Data</span>
</ng-template>
<ng-template #columnCodigo>
	<span i18n="@@tela-cliente-gympass:codigo">Código</span>
</ng-template>
<ng-template #columnToken>
	<span i18n="@@tela-cliente-gympass:token">Token</span>
</ng-template>
<ng-template #columnLegenda>
	<span i18n="@@tela-cliente-gympass:legenda">Legenda</span>
</ng-template>
<ng-template #filterStatus>
	<span i18n="@@tela-cliente-gympass:filtro-situacao">Situação</span>
</ng-template>
<ng-template #filterDate>
	<span i18n="@@tela-cliente-gympass:filtro-data">Data</span>
</ng-template>
<ng-template #situacaoTranslator let-name="name">
	<ng-container [ngSwitch]="name">
		<span
			*ngSwitchCase="true"
			class="ativo"
			i18n="@@tela-cliente-gympass:situacao:ativo">
			Ativo
		</span>
		<span
			*ngSwitchCase="false"
			class="inativo"
			i18n="@@tela-cliente-gympass:situacao:inativo">
			Inativo
		</span>
	</ng-container>
</ng-template>

<ng-template #celulaSituacao let-data="item">
	<span
		*ngIf="data?.legenda; else inactive"
		class="circle"
		data-cicle-variant="active"
		i18n-title="@@tela-cliente-gympass:situacao:ativo"
		title="Ativo">
		<span class="vh" i18n="@@tela-cliente-gympass:situacao:ativo">Ativo</span>
	</span>
	<ng-template #inactive>
		<span
			class="circle"
			data-cicle-variant="inactive"
			i18n-title="@@tela-cliente-gympass:situacao:inativo"
			title="Inativo">
			<span class="vh" i18n="@@tela-cliente-gympass:situacao:inativo">
				Inativo
			</span>
		</span>
	</ng-template>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@tela-cliente-gympass:token-error" xingling="token-error-msg">
		Desculpe, ocorreu um erro ao tentar salvar o token.
	</span>
	<span
		i18n="@@tela-cliente-gympass:token-success"
		xingling="token-success-msg">
		Token salvo com sucesso.
	</span>
	<span
		i18n="@@tela-cliente-gympass:token-deletion-success"
		xingling="token-deleted-sucessfully-msg">
		Token deletado com sucesso.
	</span>
	<span
		i18n="@@tela-cliente-gympass:permission-error"
		xingling="permission-error-msg">
		Seu usuário não tem permissão para registrar token gymPass. Entre em contato
		com o administrador do sistema.
	</span>
	<span
		i18n="@@tela-cliente-gympass:get-student-error"
		xingling="get-aluno-error">
		Erro ao buscar dados do aluno.
	</span>
	<span
		i18n="@@tela-cliente-gympass:invalid-student-error"
		xingling="aluno-invalido">
		Desculpe, não foi possível encontrar informações referentes ao aluno com
		essa matrícula.
	</span>
	<span i18n="@@tela-cliente-gympass:redirect-msg" xingling="redirect-msg">
		Em instantes vocês será redirecionado para a tela de listagem.
	</span>
</pacto-traducoes-xingling>
