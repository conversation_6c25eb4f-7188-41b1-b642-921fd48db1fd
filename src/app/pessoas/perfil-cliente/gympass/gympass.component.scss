@import "~src/assets/scss/pacto/plataforma-import.scss";

.nav-aux {
	flex-basis: 100%;
	margin: 20px 0px 20px 0px;
	font-size: 32px;
	font-weight: 400;
	line-height: 44px;

	a {
		color: $pretoPri;
	}

	i {
		margin-right: 12px;
	}

	.acesso {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $preto02;
		margin-left: 45px;
	}
}

.user-info {
	margin-bottom: 1.5rem;

	&__card,
	&__status {
		display: flex;
		align-items: center;

		> * + * {
			margin-left: 1.5rem;
		}
	}

	&__detail {
		h2 {
			font-family: Nunito Sans;
			font-weight: 600;
			font-size: 1.2rem;
			margin-bottom: 0;
		}

		p {
			margin-bottom: 0;
		}
	}

	&__status {
		> * + * {
			margin-left: 1rem;
		}
	}
}

.custom-margin {
	margin-left: auto;
}

.add-button {
	margin-right: 15px;
}

.circle {
	width: 16px;
	height: 16px;
	border-radius: 16px;
	display: inline-block;

	&[data-cicle-variant="active"] {
		background-color: $chuchuzinhoPri;
	}

	&[data-cicle-variant="inactive"] {
		background-color: $hellboyPri;
	}
}

.vh {
	clip: rect(0 0 0 0);
	clip-path: inset(50%);
	height: 1px;
	overflow: hidden;
	position: absolute;
	white-space: nowrap;
	width: 1px;
}

.custom-width {
	width: 100%;
	max-width: 33rem;
	min-width: 30rem;
	padding: 15px;

	@supports (width: max(30rem, 32rem)) {
		max-width: none;
		min-width: 0;
		width: max(30rem, 32rem);
	}
}
