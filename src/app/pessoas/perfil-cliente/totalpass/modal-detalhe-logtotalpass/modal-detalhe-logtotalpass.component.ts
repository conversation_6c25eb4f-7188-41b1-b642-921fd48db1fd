import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChange,
	SimpleChanges,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AdmCoreApiMovParcelaService,
	ClienteDadosPessoais,
} from "adm-core-api";

import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-detalhe-parcelas",
	templateUrl: "./modal-detalhe-logtotalpass.component.html",
	styleUrls: ["./modal-detalhe-logtotalpass.component.scss"],
	providers: [CurrencyPipe],
})
export class ModalDetalheLogtotalpassComponent implements OnInit {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() logTotalPass: any;

	@ViewChild("columnJson", { static: true })
	columnJson: TemplateRef<any>;

	constructor() {}

	ngOnInit() {
		var dadosLog = this.logTotalPass;
	}
}
