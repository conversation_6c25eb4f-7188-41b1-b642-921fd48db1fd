<div class="modal-content-wrapper">
	<div class="row">
		<div class="col-2 entry">
			<span class="entry-label">Tipo</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.origem || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Data Registro</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.dataregistro | date : "dd/MM/yyyy-HH:mm:ss" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Usuário</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.usuarioDTO?.nome || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Tempo Resposta</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.tempoResposta + "ms" || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Resposta</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.resposta || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Status</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.status || "-" }}
			</span>
		</div>
	</div>
	<hr />
	<div class="row">
		<div class="col-2 entry">
			<span class="entry-label">Cód</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.codigo || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Pessoa</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.pessoa }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">URL</span>
			<span class="entry-value" style="word-wrap: break-word">
				{{ logTotalPass?.row?.uri || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">ApiKey</span>
			<span class="entry-value" style="word-wrap: break-word">
				{{ logTotalPass?.row?.apikey || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Json</span>
			<span class="entry-value" style="word-wrap: break-word">
				{{ logTotalPass?.row?.json || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Empresa</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.empresa || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">IP</span>
			<span class="entry-value">
				{{ logTotalPass?.row?.ip || "-" }}
			</span>
		</div>
	</div>
</div>
