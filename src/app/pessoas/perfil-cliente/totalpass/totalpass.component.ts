import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiClienteService,
	AdmCoreApiIntegracoesService,
	ClienteDadosPessoais,
	ConfiguracaoIntegracaoTotalPass,
} from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	PactoModalSize,
	DialogService,
	ConfirmDialogDeleteComponent,
} from "ui-kit";
import { ModalDetalheLogtotalpassComponent } from "./modal-detalhe-logtotalpass/modal-detalhe-logtotalpass.component";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";

declare var moment;

@Component({
	selector: "pacto-totalpass",
	templateUrl: "./totalpass.component.html",
	styleUrls: ["./totalpass.component.scss"],
})
export class TotalpassComponent implements OnInit {
	@Input()
	dadosPessoais: ClienteDadosPessoais;

	historico: PactoDataGridConfig;
	matricula: string;
	configuracao: ConfiguracaoIntegracaoTotalPass;
	empresaTemConfiguracao: boolean = false;
	alunoTemDados: boolean = false;
	carregouCliente = false;
	carregouEmpresa = false;
	@ViewChild("historicoRef", { static: false })
	historicoRef: RelatorioComponent;
	@ViewChild("columnTipo", { static: true }) columnTipo: TemplateRef<any>;
	@ViewChild("columnTempoResposta", { static: true })
	columnTempoResposta: TemplateRef<any>;
	@ViewChild("columnDate", { static: true }) columnDate: TemplateRef<any>;
	@ViewChild("columnUsuario", { static: true }) columnUsuario: TemplateRef<any>;
	@ViewChild("columnStatus", { static: true }) columnStatus: TemplateRef<any>;
	@ViewChild("celulaSituacao", { static: true })
	celulaSituacao: TemplateRef<any>;

	constructor(
		private route: ActivatedRoute,
		private snotifyService: SnotifyService,
		private sessionService: SessionService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private msIntegracoesService: AdmCoreApiIntegracoesService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private readonly dialogService: DialogService,
		private readonly cd: ChangeDetectorRef,
		private rest: RestService
	) {}

	ngOnInit() {
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe(
			(dados) => {
				this.dadosPessoais = dados;
				this.consultarConfiguracao();
				this.initTableHistorico();
				this.initDadosTotalCliente();
				this.cd.detectChanges();
			},
			(error) => {
				this.snotifyService.error(error.error.meta.message);
			}
		);
		this.cd.detectChanges();
	}

	initDadosTotalCliente() {
		this.carregouCliente = false;
		this.alunoTemDados = false;
		this.msAdmCoreService
			.historicoTotalPass(this.dadosPessoais.codigoPessoa)
			.subscribe(
				(response) => {
					this.carregouCliente = true;
					if (response.totalElements) {
						this.alunoTemDados = response.totalElements > 0;
					} else {
						this.alunoTemDados = false;
					}
					this.cd.detectChanges();
				},
				(error) => {
					console.error(error);
					this.carregouCliente = true;
					this.snotifyService.error(error.error.meta.message);
				}
			);
	}

	consultarConfiguracao() {
		this.carregouEmpresa = false;
		this.msIntegracoesService
			.configuracaoIntegracaoTotalPass(this.sessionService.empresaId)
			.subscribe(
				(config) => {
					this.configuracao = config;
					this.empresaTemConfiguracao = !this.configuracao.inativo;
					this.carregouEmpresa = true;
					this.cd.detectChanges();
				},
				(error) => {
					console.error(error);
					this.carregouEmpresa = true;
					this.snotifyService.error(error.error.meta.message);
				}
			);
	}

	detalharLog(objetoLista) {
		const dialogRef = this.dialogService.open(
			"Detalhes de acesso TotalPass",
			ModalDetalheLogtotalpassComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		dialogRef.componentInstance.logTotalPass = objetoLista;
		this.cd.detectChanges();
	}

	initTableHistorico(): void {
		setTimeout(() => {
			this.historico = new PactoDataGridConfig({
				endpointUrl: this.rest.buildFullUrlAdmCore(
					`clientes/${this.dadosPessoais.codigoPessoa}/historico-acessos-totalpass`
				),
				quickSearch: false,
				ghostLoad: false,
				ghostAmount: 5,
				showFilters: false,
				columns: [
					{
						nome: "origem",
						titulo: this.columnTipo,
						visible: true,
						width: "20%",
						ordenavel: true,
						styleClass: "center",
					},
					{
						nome: "dataregistro",
						titulo: this.columnDate,
						visible: true,
						ordenavel: true,
						width: "20%",
						valueTransform: (v) => moment(v).format("DD/MM/YYYY-HH:mm:ss"),
						orderColumn: "dataregistro",
					},
					{
						nome: "usuarioDTO",
						titulo: this.columnUsuario,
						visible: true,
						ordenavel: true,
						width: "20%",
						styleClass: "center",
						valueTransform: (v) => (v ? v.nome : ""),
					},
					{
						nome: "tempoResposta",
						titulo: this.columnTempoResposta,
						visible: true,
						ordenavel: true,
						width: "20%",
						styleClass: "center",
						valueTransform: (v) => v + "ms",
					},
					{
						nome: "status",
						titulo: this.columnStatus,
						visible: true,
						ordenavel: true,
						width: "10%",
						celula: this.celulaSituacao,
						styleClass: "center",
					},
				],
				actions: [
					{
						nome: "verDetalhamento",
						iconClass: "pct pct-zoom-in cor-azulim05",
						tooltipText: "Detalhamento do Histórico",
					},
				],
			});
		});
	}

	autorizarTotalpass() {
		this.telaClienteService
			.validarTotalPass(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw
			)
			.subscribe(
				(response) => {
					this.snotifyService.success("TotalPass lançado com sucesso!");
					this.initDadosTotalCliente();
					this.historicoRef.reloadData();
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					this.snotifyService.error(httpResponseError.error.meta.message);
				}
			);
	}

	autorizar(): void {
		const dadosCliente = {
			matricula: this.dadosPessoais.matricula,
			pessoa: this.dadosPessoais.codigoPessoa,
			empresa: this.sessionService.empresaId,
		};
		this.msAdmCoreService
			.autorizarAcessoTotalPass(dadosCliente)
			.subscribe((response) => {
				if (!response.error) {
					this.snotifyService.success(response);
				} else {
					this.snotifyService.error(response.error.meta.message);
				}
				this.initDadosTotalCliente();
				this.historicoRef.reloadData();
				this.cd.detectChanges();
			});
	}

	isCarregou(): boolean {
		return this.carregouEmpresa && this.carregouCliente;
	}

	desvincularAluno(): void {
		const dialogRef = this.dialogService.open(
			"Desvincular aluno da TotalPass",
			ConfirmDialogDeleteComponent,
			PactoModalSize.MEDIUM,
			"modal-desvinculacao-totalpass"
		);

		const nomeAluno = this.dadosPessoais.nome || "";
		const tituloLimpo = `Desvincular o aluno ${nomeAluno} da TotalPass?`;
		dialogRef.componentInstance.titulo = tituloLimpo;
		dialogRef.componentInstance.texto = "";
		dialogRef.componentInstance.textoAlerta =
			"⚠️ Essa ação vai retirar a tag TotalPass do aluno";
		dialogRef.componentInstance.actionLabel = "Sim, confirmar";
		dialogRef.componentInstance.actionButtonWith = "140px";

		dialogRef.result.then(
			(confirmado) => {
				if (confirmado) {
					this.executarDesvinculacao();
				}
			},
			() => {}
		);
	}

	private executarDesvinculacao(): void {
		this.telaClienteService
			.desvincularAlunoTotalpass(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw
			)
			.subscribe(
				(response) => {
					this.snotifyService.success(
						"Aluno desvinculado da TotalPass com sucesso!"
					);
					this.gravarLogDesvinculacao();
					this.initDadosTotalCliente();
					this.historicoRef.reloadData();
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const errorMessage =
						httpResponseError.error.meta.message ||
						httpResponseError.message ||
						"Erro ao desvincular aluno da TotalPass";
					this.snotifyService.error(errorMessage);
				}
			);
	}

	private gravarLogDesvinculacao(): void {
		const logData = {
			acao: "Desvinculação TotalPass",
			codigoAluno: this.dadosPessoais.codigoPessoa,
			nomeAluno: this.dadosPessoais.nome,
			usuario: this.sessionService.loggedUser.usuarioZw,
			timestamp: new Date().toISOString(),
			empresa: this.sessionService.empresaId,
		};
	}
}
