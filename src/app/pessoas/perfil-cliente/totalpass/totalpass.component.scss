@import "~src/assets/scss/pacto/plataforma-import.scss";

.nav-aux {
	flex-basis: 100%;
	margin: 20px 0px 20px 0px;
	font-size: 32px;
	font-weight: 400;
	line-height: 44px;

	a {
		color: $pretoPri;
	}

	i {
		margin-right: 12px;
	}

	.acesso {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $preto02;
		margin-left: 45px;
	}
}

.user-info {
	margin-bottom: 1.5rem;

	&__card,
	&__status {
		display: flex;
		align-items: center;

		> * + * {
			margin-left: 1.5rem;
		}
	}

	&__detail {
		h2 {
			font-family: Nunito Sans;
			font-weight: 600;
			font-size: 1.2rem;
			margin-bottom: 0;
		}

		p {
			margin-bottom: 0;
		}
	}

	&__status {
		> * + * {
			margin-left: 1rem;
		}
	}
}

.custom-margin {
	margin-left: auto;
}

.add-button {
	margin-right: 15px;
}

.circle {
	width: 16px;
	height: 16px;
	border-radius: 16px;
	display: inline-block;

	&[data-cicle-variant="active"] {
		background-color: $chuchuzinhoPri;
	}

	&[data-cicle-variant="inactive"] {
		background-color: $hellboyPri;
	}
}

.vh {
	clip: rect(0 0 0 0);
	clip-path: inset(50%);
	height: 1px;
	overflow: hidden;
	position: absolute;
	white-space: nowrap;
	width: 1px;
}

.custom-width {
	width: 50%;
	max-width: 33rem;
	min-width: 30rem;
	padding: 15px;

	@supports (width: max(30rem, 32rem)) {
		max-width: none;
		min-width: 0;
		width: max(30rem, 32rem);
	}
}

.situacao {
	width: 107px;
	padding: 5px 28px;
	border-radius: 50px;
	font-size: 12px;
	font-weight: 400;
	line-height: 15px;
	text-align: center;
	color: $cinza05;
	background-color: $cinza01;

	&.liberado {
		color: $chuchuzinho05;
		background-color: $chuchuzinho01;
	}

	&.negado {
		color: $hellboy05;
		background-color: $hellboy01;
	}
}

.text {
	font-family: "Nunito Sans";
	font-weight: 700;
	font-size: 18px;
	line-height: 24px;
	color: $pretoPri;
	margin-bottom: 32px;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.botaoAutorizar {
	font-family: "Nunito Sans";
	font-weight: 700;
	font-size: 18px;
	line-height: 24px;
	color: $pretoPri;
	margin-bottom: 32px;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.icone-historico {
	width: 112px;
	height: 112px;
	padding-bottom: 21px;
	display: block;
	margin: 0 auto;
}

.botoes-acao-totalpass {
	display: flex;
	gap: 15px;
	align-items: center;
}

// Estilo customizado para alerta amarelo na modal de desvinculação TotalPass
::ng-deep .modal-dialog .mensagem-confirmacao span {
	display: inline-block;
	padding: 12px;
	background-color: #fff3cd;
	border: 1px solid #ffeaa7;
	border-radius: 4px;
	color: #856404 !important;
	font-size: 14px;
	line-height: 1.4;
	margin-top: 8px;
	width: 100%;
	box-sizing: border-box;
}

// Ocultar o ícone X do título na modal de desvinculação TotalPass
::ng-deep .modal-desvinculacao-totalpass .btn-close div i.pct-x,
::ng-deep .modal-desvinculacao-totalpass .btn-close div i#btn-close,
::ng-deep .modal-desvinculacao-totalpass .btn-close div i.pct.pct-x {
	display: none !important;
}

.text-warning {
	color: #f39c12;
	font-style: italic;
	margin-top: 8px;
}

.alert-message {
	display: flex;
	align-items: flex-start;
	gap: 8px;
	padding: 12px;
	background-color: #fff3cd;
	border: 1px solid #ffeaa7;
	border-radius: 4px;
	margin-top: 8px;

	i {
		color: #856404;
		font-size: 16px;
		margin-top: 2px;
	}

	span {
		color: #856404;
		font-size: 14px;
		line-height: 1.4;
	}
}
