import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { MdlClienteResumoContratoComponent } from "./mdl-cliente-resumo-contrato.component";

describe("MdlClienteResumoContratoComponent", () => {
	let component: MdlClienteResumoContratoComponent;
	let fixture: ComponentFixture<MdlClienteResumoContratoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [MdlClienteResumoContratoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(MdlClienteResumoContratoComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
