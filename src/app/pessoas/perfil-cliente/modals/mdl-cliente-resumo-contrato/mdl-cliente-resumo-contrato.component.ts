import { Component, Input, OnInit } from "@angular/core";
import { ClienteDadosPessoais } from "adm-core-api";

@Component({
	selector: "pacto-mdl-cliente-resumo-contrato",
	templateUrl: "./mdl-cliente-resumo-contrato.component.html",
	styleUrls: ["./mdl-cliente-resumo-contrato.component.scss"],
})
export class MdlClienteResumoContratoComponent implements OnInit {
	@Input() contratosAtivos: any;
	@Input() dadosPessoais: ClienteDadosPessoais;

	tabs = [];
	selectedTabIndex = 0;

	constructor() {}

	ngOnInit() {
		this.initTabs();
	}

	private initTabs() {
		this.contratosAtivos.forEach((c) =>
			this.tabs.push(`${c.codigo} - ${c.descricaoPlano}`)
		);
	}

	onTabChanged(tabIndex: number) {
		this.selectedTabIndex = tabIndex;
	}
}
