header {
	display: flex;
	justify-content: space-between;
	padding: 1rem 1.25rem;
	border-bottom: 1px solid #d3d5d7;
	color: #51555a;
	font-size: 14px;

	.title {
		font-weight: 700;
	}

	.close {
		.pct-x {
			font-weight: 400;
		}
	}
}

main {
	padding: 1.5rem;
}

:host {
	::ng-deep {
		pacto-relatorio {
			.table-content {
				padding: 0;
			}

			.pacto-table-title-block {
				padding: 0;
				padding-bottom: 1.5rem;
			}

			.table-content table.table {
				th {
					font-size: 14px;
					font-weight: 700;
				}

				td {
					font-size: 12px;

					i {
						font-size: 16px;
					}
				}

				.action-cell i {
					margin-right: 8px;
				}
			}
		}
	}
}
