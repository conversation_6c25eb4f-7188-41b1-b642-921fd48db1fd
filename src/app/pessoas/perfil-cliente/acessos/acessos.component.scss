pacto-cat-card-plain {
	display: flex;
	flex-direction: column;
	gap: 2rem;
	padding: 1rem;

	&:last-of-type {
		padding-bottom: 1px;
	}

	&:not(:last-child) {
		margin-bottom: 1.25rem;
	}

	header {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;

		h3 {
			color: #51555a;
			font-size: 0.875rem;
			font-weight: 700;
		}

		.acoes {
			display: flex;
			gap: 1rem;
		}
	}

	.graficos {
		width: 100%;
		display: flex;
		gap: 1rem;

		.esquerda,
		.direita {
			width: 100%;
		}

		.divisao {
			width: 1px;
			background-color: #ece9f1;
		}
	}

	.indicadores {
		width: 100%;
		display: flex;
		gap: 1rem;

		.indicador {
			width: 100%;
			display: flex;
			padding: 1rem 1.5rem;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 1.375rem;

			border-radius: 0.625rem;
			border: 1px solid #dcdddf;
			background-color: #fff;

			.titulo {
				font-size: 0.875rem;
			}

			span {
				font-size: 1.875rem;
			}

			.titulo,
			span {
				color: #51555a;
				text-align: center;
				font-weight: 700;
			}
		}
	}

	.situacao {
		width: 6rem;
		padding: 0.3125rem 1rem;
		border-radius: 3.125rem;
		font-size: 0.75rem;
		font-weight: 400;
		text-align: center;

		&.bloqueado {
			background-color: #f3b4ba;
			color: #ba202f;
		}

		&.liberado {
			background-color: #b7efc3;
			color: #28ab45;
		}
	}
}

:host {
	::ng-deep {
		pacto-relatorio {
			.table-content {
				padding: 0;
			}

			.pacto-table-title-block {
				padding: 0;
				padding-bottom: 1.5rem;

				.table-title {
					font-size: 0.875rem;
				}
			}

			.table-content table.table {
				th {
					font-size: 14px;
					font-weight: 700;
				}

				td {
					font-size: 12px;

					i {
						font-size: 16px;
					}
				}

				.action-cell i {
					margin-right: 8px;
				}
			}
		}
	}
}
