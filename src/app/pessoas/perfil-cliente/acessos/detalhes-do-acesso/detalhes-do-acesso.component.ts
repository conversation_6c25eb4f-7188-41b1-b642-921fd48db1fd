import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { BLOQUEIO } from "./bloqueio.model";

@Component({
	selector: "pacto-detalhes-do-acesso",
	templateUrl: "./detalhes-do-acesso.component.html",
	styleUrls: ["./detalhes-do-acesso.component.scss"],
})
export class DetalhesDoAcessoComponent implements OnInit {
	@Input()
	public detalhes: any;

	@Input()
	public codigoPessoa: number;

	@Input()
	public verAcessosComponent;

	public bloqueio = BLOQUEIO;

	constructor(
		private readonly ngbModal: NgbModal,
		private readonly activeModal: NgbActiveModal
	) {}

	ngOnInit() {}

	public async closeHandler() {
		const modal = this.ngbModal.open(this.verAcessosComponent, {
			centered: true,
			size: "lg",
		});
		modal.componentInstance.codigoPessoa = this.codigoPessoa;
		this.activeModal.close();
	}

	bloqueioAp(situacao) {
		if (situacao === "RV_LIBACESSOAUTORIZADO") {
			return "-";
		} else {
			return this.bloqueio[situacao];
		}
	}
}
