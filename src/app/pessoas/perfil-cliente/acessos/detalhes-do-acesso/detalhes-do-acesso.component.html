<header>
	<div class="title">Detalhes</div>
	<div (click)="closeHandler()" class="close"><i class="pct pct-x"></i></div>
</header>
<main>
	<!-- <pre>{{ detalhes | json }}</pre> -->
	<div class="container">
		<table>
			<thead>
				<th>Bloqueio</th>
				<th>Liberado por</th>
				<th>Ticket</th>
				<th>Data de registro</th>
			</thead>
			<tbody>
				<tr>
					<td>{{ bloqueioAp(detalhes.situacao) }}</td>
					<td>{{ detalhes.usuario?.nome || "-" }}</td>
					<td>{{ detalhes.ticket || "-" }}</td>
					<td>{{ detalhes.dataRegistro | date : "dd/MM/yyyy HH:mm" }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</main>
