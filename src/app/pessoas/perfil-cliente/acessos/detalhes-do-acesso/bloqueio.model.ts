export enum BLOQUEIO {
	RV_BLOQALUNONAOCADASTRADO = "Cartão de aluno não cadastrado.",
	RV_BLOQALUNOMATNAOCADASTRADO = "Matrícula de aluno não cadastrada.",
	RV_BLOQCOLABORADORNAOCADASTRADO = "Cartão de colaborador não cadastrado.",
	RV_BLOQCOLABORADORCODNAOCADASTRADO = "Código de colaborador não cadastrado.",
	RV_BLOQEMPRESANAOCONFERE = "Cartão inválido para esta empresa.",
	RV_BLOQTAMCARTAOINVALIDO = "Transação inválida pelo tamanho",
	RV_BLOQFORAHORARIO = "Fora do horário do Plano.",
	RV_BLOQFORAHORARIOTURMA = "Fora do horário da turma.",
	RV_BLOQCONTRATOTRANCADO = "Contrato trancado.",
	RV_BLOQCONTRATOFERIAS = "Contrato em férias.",
	RV_BLOQCONTATOVENCIDO = "Contrato vencido.",
	RV_BLOQCONTRATONAOINICIOU = "Contrato ainda não iniciado.",
	RV_BLOQEXAMEVENCIDO = "Avaliação ou exame médico vencido.",
	RV_BLOQMSGPERSONALIZADA = "Bloqueio por mensagem personalizada.",
	RV_BLOQACESSOSSEGUIDOS = "Bloqueio por acessos seguidos.",
	RV_BLOQCONTRATOATESTADOM = "Contrato em atestado médico.",
	RV_BLOQSTATUSALUNO = "Aluno não encontra-se ativo.",
	RV_BLOQSEMAUTORIZACAO = "Aluno não possui autorização de acesso.",
	RV_BLOQPLANOEMPRESA = "O plano do aluno não permite acesso a essa unidade.",
	RV_BLOQDVNAOCONFERE = "Dígito verificador não confere.",
	RV_LIBACESSOAUTORIZADO = "Acesso autorizado",
	RV_BLOQREGRA_LIBERACAO = "Bloqueio por regra de validação do terminal.",
	RV_BLOQPERSONAL = "Verificar Controle do Personal.",
	RV_BLOQCOLABORADORINATIVO = "Colaborador Inativo.",
	RV_BLOQPESSOASENHAINVALIDA = "Senha inválida.",
	RV_BLOQALUNOPARCELAABERTA = "Por favor, compareça à Recepção",
	RV_BLOQALUNOFREQUENCIAPLANO = "Quantidade máxima de frequência atingida",
	RV_BLOQCARTEIRINHAVENCIDA = "Carteirinha vencida.",
	RV_LIBACESSOAUTORIZADOTOTALPASS = "Acesso autorizado por Total Pass",
	RV_BLOQTOTALPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO = "Limite de acessos diários Totalpass atingido",
	RV_LIBACESSOAUTORIZADOGYMPASS = "Acesso autorizado por Gympass",
	RV_BLOQGYMPASS5 = "O usuário já visitou esta academia hoje",
	RV_BLOQGYMPASS11 = "Token Diário Inválido",
	RV_BLOQGYMPASS12 = "Validador não autorizado. Um passe só é válido na academia onde foi comprado",
	RV_BLOQGYMPASS13 = "Token já foi usado hoje",
	RV_BLOQGYMPASS14 = "Item não está disponível devido a problemas com o pagamento",
	RV_BLOQGYMPASS15 = "Passe já foi completamente usado",
	RV_BLOQGYMPASS16 = "Passe já expirou e não pode mais ser usado",
	RV_BLOQGYMPASS21 = "Número de Token Diário Inválido",
	RV_BLOQGYMPASS22 = "Cartão não habilitado",
	RV_BLOQGYMPASS23 = "O aluno não fez checkin",
	RV_BLOQGYMPASS24 = "Sem permissão para validar passes diários",
	RV_BLOQGYMPASS26 = "Não há créditos",
	RV_BLOQGYMPASS27 = "Pessoa bloqueada",
	RV_BLOQGYMPASS28 = "Erro ao aprovar o cartão bancário",
	RV_BLOQGYMPASS29 = "Cartão desabilitado",
	RV_BLOQGYMPASS30 = "Cartão expirado",
	RV_BLOQGYMPASS32 = "Esta pessoa não tem passes disponíveis para essa academia",
	RV_BLOQGYMPASS33 = "Academia bloqueada",
	RV_BLOQGYMPASS34 = "Token diário desativado",
	RV_BLOQGYMPASS35 = "Token Diário expirou",
	RV_BLOQGYMPASS38 = "Pessoa não está na lista de permitidos para essa academia",
	RV_BLOQGYMPASS39 = "Número máximo permitido de vezes na semana foi excedido",
	RV_BLOQGYMPASS40 = "Número máximo permitido de vezes este mês foi excedido",
	RV_BLOQGYMPASS41 = "Nenhuma reserva foi encontrada para esta pessoa",
	RV_BLOQGYMPASS42 = "É muito cedo para validar esta reserva",
	RV_BLOQGYMPASS43 = "É tarde demais para validar esta reserva",
	RV_BLOQGYMPASS45 = "O usuário ainda não fez check-in",
	RV_BLOQGYMPASS46 = "Usuário fez check-in em outra academia",
	RV_BLOQGYMPASS47 = "User Check In para essa academia expirou",
	RV_BLOQGYMPASSGENERICO = "Token Gympass não foi validado",
	RV_GYMPASS_AGUARDANDO_RESPOSTA = "Aguardando resposta da Gympass",
	RV_BLOQGYMPASS_LIMITE_ACESSOS_DIARIOS_ATINGIDO = "Limite de acessos diários Gympass atingido",
	RV_BLOQALUNOSEMASSINATURA = "Verificar Assinatura Digital",
	RV_BLOQALUNOCAPACIDADESIMULTANEA = "Academia está lotada",
	RV_BLOQSEMCARTAOVACINA = "Sem comprovante de vacinação apresentado",
	RV_BLOQCREFVENCIDO = "CREF do colaborador está vencido",
}
