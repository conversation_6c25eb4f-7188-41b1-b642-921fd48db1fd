header {
	display: flex;
	justify-content: space-between;
	padding: 1rem 1.25rem;
	border-bottom: 1px solid #d3d5d7;
	color: #51555a;
	font-size: 1rem;

	.title {
		font-weight: 700;
	}

	.close {
		.pct-x {
			font-weight: 400;
		}
	}
}

main {
	box-sizing: border-box;
	padding: 1rem 2rem 2rem 2rem;

	.container {
		background-color: #fafafa;
		padding: 0.5rem;

		table {
			width: 100%;

			thead,
			tbody {
				color: #51555a;
				font-size: 0.875rem;
			}

			thead {
				font-weight: 700;
			}

			tbody {
				font-weight: 400;
			}
		}
	}
}
