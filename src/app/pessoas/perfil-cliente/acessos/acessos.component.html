<pacto-cat-card-plain>
	<header>
		<h3>Detalhes de acesso</h3>

		<div class="acoes">
			<pacto-cat-button
				(click)="verRelatorioDeAcessos()"
				[disabled]="!permiteVerRelatorioDeAcessos"
				[label]="'Lista de acessos'"
				[size]="'LARGE'"
				[type]="'OUTLINE'"
				id="btn-lista-acesso"></pacto-cat-button>
			<pacto-cat-button
				(click)="verAcessos()"
				[label]="'Ver acessos'"
				[size]="'LARGE'"
				id="btn-ver-acesso"></pacto-cat-button>
		</div>
	</header>

	<div *ngIf="!data" class="d-flex flex-column align-items-center">
		<img class="icon-empty" src="assets/images/empty-access.svg" />
		<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
			O aluno ainda não possui nenhum registro de acesso!
		</div>
	</div>

	<section [hidden]="!data" class="graficos">
		<div
			[style.height.rem]="15.84248"
			class="esquerda"
			id="graficoUltimosAcessosNaSemana"></div>
		<div class="divisao"></div>
		<div
			[style.height.rem]="15.84248"
			class="direita"
			id="graficoUltimosAcessosNoMes"></div>
	</section>

	<section *ngIf="informacoesDeAcessos" [hidden]="!data" class="indicadores">
		<div class="indicador">
			<h6 class="titulo">Último acesso</h6>
			<span>{{ informacoesDeAcessos.dataUltimoAcesso | date }}</span>
		</div>
		<div class="indicador">
			<h6 class="titulo">Média de frequência nas últimas 4 semanas</h6>
			<span>{{ informacoesDeAcessos.mediaUltimasSemanas }}</span>
		</div>
		<div class="indicador">
			<h6 class="titulo">Média de frequência nas últimos 4 mês</h6>
			<span>{{ informacoesDeAcessos.mediaUltimosMeses }}</span>
		</div>
	</section>
</pacto-cat-card-plain>

<pacto-cat-card-plain [hidden]="!data">
	<pacto-relatorio
		[showShare]="false"
		idSuffix="tbl-periodo-acesso"></pacto-relatorio>
	<ng-template #cellSituacao let-item="item">
		<div [ngClass]="['situacao', item.legenda ? 'liberado' : 'bloqueado']">
			{{ item.legenda ? "Liberado" : "Bloqueado" }}
		</div>
	</ng-template>
</pacto-cat-card-plain>
