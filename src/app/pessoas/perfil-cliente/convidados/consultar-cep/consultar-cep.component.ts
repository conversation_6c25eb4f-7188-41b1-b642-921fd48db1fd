import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	CadastroAuxApiCidadeService,
	CadastroAuxApiEstadoService,
	CadastroAuxApiPaisService,
} from "cadastro-aux-api";

@Component({
	selector: "pacto-consultar-cep",
	templateUrl: "./consultar-cep.component.html",
	styleUrls: ["./consultar-cep.component.scss"],
})
export class ConsultarCepComponent implements OnInit {
	form: FormGroup = new FormGroup({
		estado: new FormControl(),
		cidade: new FormControl(),
		bairro: new FormControl(),
		logradouro: new FormControl(),
	});

	constructor(
		private EstadoService: CadastroAuxApiEstadoService,
		private cidadeService: CadastroAuxApiCidadeService
	) {}

	optionsEstados;
	optionsCidades;

	ngOnInit() {
		this.EstadoService.findAllCodName(1).subscribe((data) => {
			this.optionsEstados = data.content;
		});
		this.form.get("estado").valueChanges.subscribe((data) => {
			// this.cidadeService.find(data.codigo).subscribe(data=>{
			//   this.optionsCidades = data.content
			// })
		});
	}

	getCepByEndereço(estado, cidade, bairro, logradouro) {
		console.log(estado, cidade, bairro, logradouro);
	}
}
