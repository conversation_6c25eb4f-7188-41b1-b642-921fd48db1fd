import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import {
	DialogService,
	PactoModalRef,
	PactoModalSize,
	SelectFilterParamBuilder,
} from "ui-kit";
import { ConsultarCepComponent } from "../consultar-cep/consultar-cep.component";
import { RestService } from "@base-core/rest/rest.service";
import { Api } from "@base-core/rest/rest.model";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { PactoApiCepService } from "pacto-api";
import { ModalService } from "@base-core/modal/modal.service";
import { ModalConvidadosExistentesComponent } from "../modal-convidados-existentes/modal-convidados-existentes.component";

@Component({
	selector: "pacto-cadastrar-convidado",
	templateUrl: "./cadastrar-convidado.component.html",
	styleUrls: ["./cadastrar-convidado.component.scss"],
})
export class CadastrarConvidadoComponent implements OnInit {
	matricula;
	dadosPessoais: ClienteDadosPessoais;
	convidado;
	mostrarEnderecoComercial = false;
	public cpfMask = [
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];
	public cepMask = [
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];
	form: FormGroup = new FormGroup({
		consultor: new FormControl(),
		evento: new FormControl(),
		cpf: new FormControl(),
		rne: new FormControl(),
		nomeCompleto: new FormControl(),
		nascimento: new FormControl(),
		sexo: new FormControl(),
		alunoEstrangeiro: new FormControl(false),
		email: new FormControl(),
		telefoneCelular: new FormControl(),
		telefoneFixo: new FormControl(),
		cpfResponsavel: new FormControl(),
		nomeResponsavel: new FormControl(),
		emailResponsavel: new FormControl(),
		nascimentoResponsavel: new FormControl(),
		endereco: new FormGroup({
			cep: new FormControl(),
			pais: new FormControl(),
			estado: new FormControl(),
			cidade: new FormControl(),
			endereco: new FormControl(),
			bairro: new FormControl(),
			complemento: new FormControl(),
			numero: new FormControl(),
		}),
		enderecoComercial: new FormGroup({
			cep: new FormControl(),
			pais: new FormControl(),
			estado: new FormControl(),
			cidade: new FormControl(),
			endereco: new FormControl(),
			bairro: new FormControl(),
			complemento: new FormControl(),
			numero: new FormControl(),
		}),
	});
	cidadeUrl;
	estadoUrl;
	cidadeComercialUrl;
	estadoComercialUrl;
	paisUrl = this.restService.buildFullUrlPessoaMs("pais");
	colaborators = [];
	colaboradoresUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/colaboradores"
	);

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private pactoModal: ModalService,
		private route: ActivatedRoute,
		private msAdmCoreService: AdmCoreApiClienteService,
		private dialogService: DialogService,
		private restService: RestService,
		private sessionService: SessionService,
		private enderecoService: PactoApiCepService,
		private notificationService: SnotifyService,
		private readonly admLegadoService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {
		this.convidado = null;
		this.matricula =
			this.route.snapshot.paramMap.get("aluno-matricula") ||
			sessionStorage.getItem("pacto-aluno-matricula");

		if (!this.matricula) {
			this.router.navigate(["/cadastros", "alunos", "listagem"]);
		}

		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe((aluno) => {
			this.dadosPessoais = aluno;
			this.form.get("cpf").valueChanges.subscribe((valor) => {
				setTimeout(() => {
					if (this.convidado == null && valor && valor !== "") {
						const valorSemMasc = valor.replace(/[^0-9]/g, "");
						if (valorSemMasc.length === 11) {
							this.buscarConvidadoCPF(valorSemMasc);
						}
					}
				}, 500);
			});
			this.cd.detectChanges();
		});
		this.populateEstadoCidadeUrl();
		this.cd.detectChanges();
	}

	buscarConvidadoCPF(valorSemMasc) {
		this.admLegadoService
			.consultarConvidadosCPF(this.sessionService.chave, {
				cliente: this.dadosPessoais.codigoCliente,
				empresa: this.dadosPessoais.empresa.codigo,
				cpfBusca: valorSemMasc,
			})
			.subscribe((resp) => {
				if (resp.content && resp.content.length > 0) {
					this.abrirModalClientes(resp.content);
				}
			});
	}

	abrirModalClientes(clientes) {
		const modal: PactoModalRef = this.pactoModal.open(
			"Pessoas com mesmo CPF",
			ModalConvidadosExistentesComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.clientes = clientes;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "NOVO_CONVIDADO") {
				this.form = new FormGroup({
					consultor: new FormControl(),
					evento: new FormControl(),
					cpf: new FormControl(),
					rne: new FormControl(),
					nomeCompleto: new FormControl(),
					nascimento: new FormControl(),
					sexo: new FormControl(),
					alunoEstrangeiro: new FormControl(false),
					email: new FormControl(),
					telefoneCelular: new FormControl(),
					telefoneFixo: new FormControl(),
					cpfResponsavel: new FormControl(),
					nomeResponsavel: new FormControl(),
					emailResponsavel: new FormControl(),
					nascimentoResponsavel: new FormControl(),
					endereco: new FormGroup({
						cep: new FormControl(),
						pais: new FormControl(),
						estado: new FormControl(),
						cidade: new FormControl(),
						endereco: new FormControl(),
						bairro: new FormControl(),
						complemento: new FormControl(),
						numero: new FormControl(),
					}),
					enderecoComercial: new FormGroup({
						cep: new FormControl(),
						pais: new FormControl(),
						estado: new FormControl(),
						cidade: new FormControl(),
						endereco: new FormControl(),
						bairro: new FormControl(),
						complemento: new FormControl(),
						numero: new FormControl(),
					}),
				});
				this.cd.detectChanges();
			} else {
				this.convidado = res;
				this.form.get("nomeCompleto").setValue(this.convidado.nomeCompleto);
				this.form.get("nomeCompleto").disable();
				this.form.get("cpf").setValue(this.convidado.cpf);
				this.form.get("cpf").disable();
				this.cd.detectChanges();
			}
		});
	}

	adicionarEnderecoComercial(): void {
		this.mostrarEnderecoComercial = true;
	}

	removerEnderecoComercial(): void {
		this.mostrarEnderecoComercial = false;
		this.form.get(`enderecoComercial.cep`).setValue("");
		this.form.get(`enderecoComercial.pais`).setValue(null);
		this.form.get(`enderecoComercial.estado`).setValue(null);
		this.form.get(`enderecoComercial.cidade`).setValue(null);
		this.form.get(`enderecoComercial.endereco`).setValue("");
		this.form.get(`enderecoComercial.bairro`).setValue("");
		this.form.get(`enderecoComercial.complemento`).setValue("");
		this.form.get(`enderecoComercial.numero`).setValue("");
	}

	// consultarCep(): void {
	// 	this.dialogService.open(
	// 		'Consultar CEP',
	// 		ConsultarCepComponent,
	// 		PactoModalSize.MEDIUM
	// 	);
	// }

	eventoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				searchTerm: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	getUrlEvento() {
		return this.restService.buildFullUrl(`eventos`, true, Api.MSADMCORE);
	}

	labelFnNomeEvento = (item) => {
		return !item.descricao ? "-" : item.descricao;
	};

	novoConvidado() {
		this.convidado = null;
		this.form.get("nomeCompleto").setValue("");
		this.form.get("cpf").setValue("");
		this.form.get("nomeCompleto").enable();
		this.form.get("cpf").enable();
		this.cd.detectChanges();
	}

	salvarClienteExiste() {
		this.admLegadoService
			.salvarConvidado(this.sessionService.chave, {
				cliente: this.dadosPessoais.codigoCliente,
				usuario: this.sessionService.codUsuarioZW,
				convidado: this.convidado.codigo,
			})
			.subscribe({
				next: (resp) => {
					console.log("salvarClienteExiste", { resp });
					this.notificationService.success("Convite enviado com sucesso.");
					this.router.navigate([
						"pessoas",
						"perfil-v2",
						this.convidado.matricula,
						"contratos",
					]);
				},
				error: (error) => {
					const errorMessage =
						error.error !== undefined &&
						error.error.meta !== undefined &&
						error.error.meta.message
							? error.error.meta.message
							: "";
					const messageBody =
						"Ocorreu um erro ao tentar enviar o convite. " + errorMessage;
					this.notificationService.error(messageBody);
				},
			});
	}

	salvar() {
		this.admLegadoService
			.incluirConvidadoLancarConvite(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codUsuarioZW,
				this.dadosPessoais.codigoCliente,
				this.form.getRawValue()
			)
			.subscribe(
				(res) => {
					this.notificationService.success("Convidado cadastrado");
					this.router.navigate([
						"pessoas",
						"perfil-v2",
						res.content,
						"contratos",
					]);
				},
				({ error }) => {
					console.log(error);
					this.notificationService.error(error.meta.message);
				}
			);
	}

	paisParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	estadoParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	cidadeParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	private populateEstadoCidadeUrl() {
		this.form.get("endereco.pais").valueChanges.subscribe((pais) => {
			if (pais) {
				this.estadoUrl = this.restService.buildFullUrlPessoaMs(
					`estado/${pais.codigo}`
				);
				if (this.form.get("endereco.estado").disabled) {
					this.form.get("endereco.estado").enable();
				}
			}
		});
		this.form.get("endereco.estado").valueChanges.subscribe((estado) => {
			if (estado) {
				this.cidadeUrl = this.restService.buildFullUrlPessoaMs(
					`cidade/${estado.codigo}`
				);
				if (this.form.get("endereco.cidade").disabled) {
					this.form.get("endereco.cidade").enable();
				}
			}
		});

		this.form.get("enderecoComercial.pais").valueChanges.subscribe((pais) => {
			if (pais) {
				this.estadoComercialUrl = this.restService.buildFullUrlPessoaMs(
					`estado/${pais.codigo}`
				);
				if (this.form.get("enderecoComercial.estado").disabled) {
					this.form.get("enderecoComercial.estado").enable();
				}
			}
		});
		this.form
			.get("enderecoComercial.estado")
			.valueChanges.subscribe((estado) => {
				if (estado) {
					this.cidadeComercialUrl = this.restService.buildFullUrlPessoaMs(
						`cidade/${estado.codigo}`
					);
					if (this.form.get("enderecoComercial.cidade").disabled) {
						this.form.get("enderecoComercial.cidade").enable();
					}
				}
			});
	}

	paramBuilder = (nome) => {
		return {
			filters: JSON.stringify({
				nome: nome ? nome : "",
				empresa: this.sessionService.empresaId,
				tipoColaborador: "CO",
			}),
		};
	};

	responseParser = (result) => {
		if (result && result.content && Array.isArray(result.content)) {
			const formattedResult = result.content.map((r) => r);
			this.colaborators = formattedResult;
			return formattedResult;
		}
		return [];
	};

	consultaCep(campo) {
		let valorCep = this.form.get(`${campo}.cep`).value.replace("-", "");
		if (valorCep !== "") {
			this.enderecoService
				.buscaCEP(this.sessionService.chave, valorCep)
				.subscribe((data) => {
					const dadosCep = data.content;
					this.form.get(`${campo}.endereco`).setValue(dadosCep.endereco);
					this.form.get(`${campo}.bairro`).setValue(dadosCep.bairro);
					this.form.get(`${campo}.complemento`).setValue(dadosCep.complemento);
					this.form.get(`${campo}.pais`).setValue({
						codigo: dadosCep.pais_codigo,
						nome: dadosCep.pais_nome,
					});
					this.form.get(`${campo}.estado`).setValue({
						codigo: dadosCep.estado_codigo,
						descricao: dadosCep.estado_nome,
					});
					this.form.get(`${campo}.cidade`).setValue({
						codigo: dadosCep.cidade_codigo,
						nome: dadosCep.cidade_nome,
					});
					this.cd.detectChanges();
				});
		} else {
			this.notificationService.error("CEP não informado");
		}
	}
}
