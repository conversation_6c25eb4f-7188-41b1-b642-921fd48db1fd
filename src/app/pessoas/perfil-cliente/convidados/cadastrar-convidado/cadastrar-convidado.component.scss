@import "~src/assets/scss/pacto/plataforma-import.scss";

.cabecalho-principal {
	margin-bottom: 1.37rem;

	a {
		display: flex;
		gap: 0.75rem;
		width: 35ch;
	}

	.seta {
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		color: #51555a;
		text-align: center;
		font-size: 1.5rem;
		font-weight: 400;
		width: 1.5rem;
	}

	.breadcrumbs {
		color: #6f747b;
		font-size: 0.75rem;

		font-weight: 600;
		margin-left: 2.5rem;
	}

	.titulo {
		color: #51555a;
		font-size: 1.87rem;
		font-weight: 700;
	}
}

.text-label {
	display: block;
	margin-bottom: 0.75rem;
	color: #a6aab1;
	min-height: 2rem;
	line-height: 2em;
	padding-left: 3px;
	font-weight: 500;
}

.sexo-biologico-radio-group {
	margin-bottom: 1rem;
}

.cep {
	p {
		margin-top: revert;
		margin-bottom: revert;
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	button {
		all: unset;
		color: $azulim05;
		font-weight: 600;
		cursor: pointer;
		padding: 12px 8px;
		border-radius: 4px;
		gap: 8px;

		&:hover {
			background-color: rgba($azulim05, 0.15);
		}
	}
}

h3 {
	font-weight: 600;
}

pacto-cat-card-plain {
	::ng-deep .custom-button .pacto-no-border {
		font-weight: 700;
		color: $azulim05;

		&:hover {
			background-color: rgba($azulim05, 0.15);
		}
	}
}

.div-btn-convidado-existe {
	display: flex;
	gap: 25px;

	.div-btn-convidado-existe-left {
		justify-content: left;
	}

	.div-btn-convidado-existe-right {
		text-align: right;
	}
}
