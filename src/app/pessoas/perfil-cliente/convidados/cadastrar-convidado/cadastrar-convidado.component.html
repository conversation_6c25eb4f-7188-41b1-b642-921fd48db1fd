<section aria-label="header" class="cabecalho-principal">
	<div class="breadcrumbs">Clientes / Perfil do cliente</div>
	<a [routerLink]="['..', 'contratos']">
		<div class="seta">
			<i class="pct pct-arrow-left"></i>
		</div>
		<div class="titulo" i18n="@@contract-detail:info-title">
			Cadastrar convidado
		</div>
	</a>
</section>
<pacto-cat-card-plain>
	<h3 class="h6">
		Adicionar convidado de {{ dadosPessoais?.nome | captalize }}
	</h3>
	<div class="row" *ngIf="convidado == null">
		<div class="col-md-4">
			<pacto-cat-form-select-filter
				[control]="form.get('consultor')"
				[endpointUrl]="colaboradoresUrl"
				[paramBuilder]="paramBuilder"
				[resposeParser]="responseParser"
				i18n-label="@@cadastrar-convidado:consultor"
				idKey="codigo"
				label="Colaborador"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[control]="form.get('evento')"
				[endpointUrl]="getUrlEvento()"
				[labelFn]="labelFnNomeEvento"
				[paramBuilder]="eventoSelectBuilder"
				class="select-edicao"
				i18n-errorMsg="@@cadastrar-convidado:evento-error"
				i18n-label="@@cadastrar-convidado:evento"
				idKey="descricao"
				label="Evento"></pacto-cat-form-select-filter>
		</div>
	</div>
	<hr />
	<div class="row align-items-center">
		<div
			*ngIf="!form.get('alunoEstrangeiro').value; else estrangeiro"
			class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('cpf')"
				[textMask]="{ mask: cpfMask }"
				label="CPF"></pacto-cat-form-input>
		</div>
		<ng-template #estrangeiro>
			<div class="col-md-4">
				<pacto-cat-form-input
					[control]="form.get('rne')"
					label="RNE (preencher somente se estrangeiro)"></pacto-cat-form-input>
			</div>
		</ng-template>
		<div class="col-md-4" style="height: 0" *ngIf="convidado == null">
			<pacto-cat-checkbox
				[control]="form.get('alunoEstrangeiro')"
				label="Aluno estrangeiro"></pacto-cat-checkbox>
		</div>
	</div>
	<div class="row align-items-center">
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('nomeCompleto')"
				label="Nome completo"></pacto-cat-form-input>
		</div>
		<div class="col-md-4" style="height: 104px" *ngIf="convidado == null">
			<pacto-cat-form-datepicker
				[control]="form.get('nascimento')"
				errorMsg="Por favor, forneça uma data válida."
				label="Data de nascimento"></pacto-cat-form-datepicker>
		</div>
		<div class="col-md-4" *ngIf="convidado == null">
			<span class="text-label">Sexo biológico</span>

			<div class="sexo-biologico-radio-group">
				<pacto-cat-radio-group [formControl]="form.get('sexo')">
					<pacto-cat-radio value="M">
						<label>Masculino</label>
					</pacto-cat-radio>
					<pacto-cat-radio value="F">
						<label>Feminino</label>
					</pacto-cat-radio>
				</pacto-cat-radio-group>
			</div>
		</div>
	</div>
	<div class="row align-items-center" *ngIf="convidado == null">
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('email')"
				label="Email"></pacto-cat-form-input>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('telefoneCelular')"
				label="Tel. Celular"></pacto-cat-form-input>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('telefoneFixo')"
				label="Tel. Fixo"></pacto-cat-form-input>
		</div>
	</div>

	<hr *ngIf="convidado == null" />

	<div class="row align-items-center" *ngIf="convidado == null">
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('cpfResponsavel')"
				[textMask]="{ mask: cpfMask }"
				label="CPF do responsável"></pacto-cat-form-input>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('nomeResponsavel')"
				label="Nome do responsável"></pacto-cat-form-input>
		</div>
		<div class="col-md-4" style="height: 104px">
			<pacto-cat-form-datepicker
				[control]="form.get('nascimentoResponsavel')"
				errorMsg="Por favor, forneça uma data válida."
				label="Data de nascimento do responsável"></pacto-cat-form-datepicker>
		</div>
	</div>

	<div class="row" *ngIf="convidado == null">
		<div class="col-md-12">
			<pacto-cat-form-input
				[control]="form.get('emailResponsavel')"
				label="E-mail do responsavel"></pacto-cat-form-input>
		</div>
	</div>

	<hr *ngIf="convidado == null" />

	<div class="row align-items-center" *ngIf="convidado == null">
		<div class="col-md-4">
			<pacto-cat-form-input
				[control]="form.get('endereco.cep')"
				[textMask]="{ mask: cepMask }"
				label="CEP*"></pacto-cat-form-input>
		</div>
		<div class="col-md-6 cep" style="height: 10px">
			<pacto-cat-button
				(click)="consultaCep('endereco')"
				class="custom-button"
				label="Consulte o CEP"
				size="LARGE"
				type="NO_BORDER"></pacto-cat-button>
		</div>
	</div>

	<div class="row" *ngIf="convidado == null">
		<div class="col-md-4">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[control]="form.get('endereco.pais')"
				[endpointUrl]="paisUrl"
				[label]="'País'"
				[paramBuilder]="paisParamBuilder"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@cadastrar-convidado:pais-required"
				i18n-label="@@cadastrar-convidado:pais"
				idKey="codigo"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[control]="form.get('endereco.estado')"
				[endpointUrl]="estadoUrl"
				[label]="'Estado'"
				[paramBuilder]="estadoParamBuilder"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@cadastrar-convidado:estado-required"
				i18n-label="@@cadastrar-convidado:estado"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-4">
			<pacto-cat-form-select-filter
				[addEmptyOption]="true"
				[control]="form.get('endereco.cidade')"
				[endpointUrl]="cidadeUrl"
				[label]="'Cidade'"
				[paramBuilder]="cidadeParamBuilder"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@cadastrar-convidado:cidade-required"
				i18n-label="@@cadastrar-convidado:cidade"
				idKey="codigo"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
	</div>

	<div class="row align-items-center" *ngIf="convidado == null">
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="form.get('endereco.endereco')"
				label="Endereço"></pacto-cat-form-input>
		</div>
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="form.get('endereco.bairro')"
				label="Bairro"></pacto-cat-form-input>
		</div>
	</div>

	<div class="row" *ngIf="convidado == null">
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="form.get('endereco.complemento')"
				label="Complemento"></pacto-cat-form-input>
		</div>
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="form.get('endereco.numero')"
				label="Número"></pacto-cat-form-input>
		</div>
	</div>

	<pacto-cat-button
		class="custom-button"
		label="Adicionar endereço comercial"
		size="LARGE"
		type="NO_BORDER"
		(click)="adicionarEnderecoComercial()"
		*ngIf="!mostrarEnderecoComercial && convidado == null"></pacto-cat-button>
	<pacto-cat-button
		class="custom-button"
		label="Remover endereço comercial"
		size="LARGE"
		type="NO_BORDER"
		*ngIf="mostrarEnderecoComercial && convidado == null"
		(click)="removerEnderecoComercial()"></pacto-cat-button>

	<ng-container *ngIf="mostrarEnderecoComercial && convidado == null">
		<hr />

		<div class="row align-items-center">
			<div class="col-md-4">
				<pacto-cat-form-input
					[control]="form.get('enderecoComercial.cep')"
					[textMask]="{ mask: cepMask }"
					label="CEP*"></pacto-cat-form-input>
			</div>
			<div class="col-md-6 cep" style="height: 10px">
				<pacto-cat-button
					(click)="consultaCep('enderecoComercial')"
					class="custom-button"
					label="Consulte o CEP"
					size="LARGE"
					type="NO_BORDER"></pacto-cat-button>
			</div>
		</div>

		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[addEmptyOption]="true"
					[control]="form.get('enderecoComercial.pais')"
					[endpointUrl]="paisUrl"
					[label]="'País'"
					[paramBuilder]="paisParamBuilder"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@cadastrar-convidado:pais-required"
					i18n-label="@@cadastrar-convidado:pais"
					idKey="codigo"
					labelKey="nome"></pacto-cat-form-select-filter>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[addEmptyOption]="true"
					[control]="form.get('enderecoComercial.estado')"
					[endpointUrl]="estadoComercialUrl"
					[label]="'Estado'"
					[paramBuilder]="estadoParamBuilder"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@cadastrar-convidado:estado-required"
					i18n-label="@@cadastrar-convidado:estado"
					idKey="codigo"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[addEmptyOption]="true"
					[control]="form.get('enderecoComercial.cidade')"
					[endpointUrl]="cidadeComercialUrl"
					[label]="'Cidade'"
					[paramBuilder]="cidadeParamBuilder"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@cadastrar-convidado:cidade-required"
					i18n-label="@@cadastrar-convidado:cidade"
					idKey="codigo"
					labelKey="nome"></pacto-cat-form-select-filter>
			</div>
		</div>

		<div class="row align-items-center">
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="form.get('enderecoComercial.endereco')"
					label="Endereço"></pacto-cat-form-input>
			</div>
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="form.get('enderecoComercial.bairro')"
					label="Bairro"></pacto-cat-form-input>
			</div>
		</div>

		<div class="row">
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="form.get('enderecoComercial.complemento')"
					label="Complemento"></pacto-cat-form-input>
			</div>
			<div class="col-md-6">
				<pacto-cat-form-input
					[control]="form.get('enderecoComercial.numero')"
					label="Número"></pacto-cat-form-input>
			</div>
		</div>
	</ng-container>

	<div class="d-flex justify-content-end" *ngIf="convidado == null">
		<pacto-cat-button
			label="Lançar convidado"
			size="LARGE"
			(click)="salvar()"></pacto-cat-button>
	</div>
	<div class="div-btn-convidado-existe" *ngIf="convidado != null">
		<div class="div-btn-convidado-existe-right">
			<pacto-cat-button
				label="Lançar convite"
				size="LARGE"
				(click)="salvarClienteExiste()"></pacto-cat-button>
		</div>
		<div class="div-btn-convidado-existe-left">
			<pacto-cat-button
				label="Novo convidado"
				size="LARGE"
				type="SECUNDARY"
				(click)="novoConvidado()"></pacto-cat-button>
		</div>
	</div>
</pacto-cat-card-plain>
