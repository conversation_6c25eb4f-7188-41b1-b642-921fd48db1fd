import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
} from "ui-kit";
import { ConsultarCepComponent } from "../consultar-cep/consultar-cep.component";
import { RestService } from "@base-core/rest/rest.service";
import { Api } from "@base-core/rest/rest.model";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { PactoApiCepService } from "pacto-api";
import { catchError, map } from "rxjs/operators";
import { of } from "rxjs";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-convidados-existentes",
	templateUrl: "./modal-convidados-existentes.component.html",
	styleUrls: ["./modal-convidados-existentes.component.scss"],
})
export class ModalConvidadosExistentesComponent implements OnInit {
	dadosPessoais: ClienteDadosPessoais;
	clientes;
	@Output()
	response = new EventEmitter<any>();

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private msAdmCoreService: AdmCoreApiClienteService,
		private dialogService: DialogService,
		private restService: RestService,
		private sessionService: SessionService,
		private enderecoService: PactoApiCepService,
		private notificationService: SnotifyService,
		private dialog: NgbActiveModal,
		private readonly admLegadoService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {}

	selecionarCliente(item) {
		this.response.emit(item);
		this.dialog.close();
	}

	cancelar() {
		this.response.emit("NOVO_CONVIDADO");
		this.dialog.close();
	}
}
