@import "~src/assets/scss/pacto/plataforma-import.scss";

.pacto-modal-convidado-tela-cliente {
	.convidados-autocomplete-container {
		position: relative;
		margin-bottom: 2rem;

		.pacto-cat-autocomplete {
			width: 100%;
			max-height: 25ch;
		}
	}

	.modal-convidados-content {
		padding: 1.4rem;
		padding-top: 0;

		.text-content {
			padding: 32px 0;

			.head {
				@extend .type-h6-bold;
			}

			.text {
				@extend .type-h6;
			}
		}

		.buttons {
			display: flex;
			justify-content: flex-end;
			gap: 16px;
		}
	}

	pacto-cat-form-input {
		margin: 0;
	}

	pacto-cat-form-input .nome {
		display: none;
	}

	pacto-cat-form-input .pct-error-msg:empty {
		display: none;
	}

	pacto-cat-option {
		padding: 1rem 1rem;
	}

	.no-hover:hover {
		background-color: transparent;
		cursor: auto;
	}
}

.div-modal-convi {
	display: grid;
	grid-template-columns: 3fr 1fr;
	align-items: center;
	justify-content: center;
	margin: 10px;
	border: 1px solid #c7c7c7;
	border-radius: 8px;
	padding: 7px;

	.div-modal-convi-item {
		//display: grid;
		//grid-template-columns: 1fr 1fr;
		//text-align: left;

		.label-item {
			font-weight: 400;
		}

		.text-item {
		}
	}

	.div-modal-convi-sel {
		display: grid;
		justify-content: center;
	}
}
