import {
	Component,
	ElementRef,
	HostBinding,
	OnInit,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogService, PactoModalSize } from "ui-kit";
import { HistoricoConvidadosComponent } from "../convidados/historico-convidados/historico-convidados.component";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { BehaviorSubject, Observable, Subject, of } from "rxjs";
import { catchError, debounceTime, map, switchMap, tap } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-convidados",
	templateUrl: "./modal-convidados.component.html",
	styleUrls: ["./modal-convidados.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ModalConvidadosComponent implements OnInit {
	@HostBinding("class.pacto-modal-convidado-tela-cliente")
	styleEncapsulation = true;
	@ViewChild("autoCompleteContainer", { static: true })
	autoCompleteContainer: ElementRef<HTMLElement>;

	autoCompleteOptionsWidth$ = new BehaviorSubject<number>(200);
	busca = new FormControl("");
	_busca$ = new Subject<string>();
	convidado;
	displayEmptyMessage = false;
	showDropdown = false;

	matricula;
	cliente;
	chave;
	usuarioLogado;
	clienteEmpresa;
	permissaoConsultarAlunosCaixaAbertoTodasEmpresas;

	convitesInfo$: Observable<any>;
	_convitesInfo;
	consultarConvidado$ = this._busca$.pipe(
		debounceTime(300),
		switchMap((search) =>
			this.admLegadoService
				.consultarConvidados(this.chave, {
					cliente: this.cliente,
					empresa: this.clienteEmpresa,
					consultarAlunosCaixaAbertoTodasEmpresas:
						this.permissaoConsultarAlunosCaixaAbertoTodasEmpresas,
					clienteEmpresa: this.clienteEmpresa,
					busca: search,
				})
				.pipe(
					map((resp) => {
						if (resp && resp.content) {
							return resp.content;
						} else {
							return [];
						}
					}),
					catchError(() => of([]))
				)
		)
	);

	constructor(
		private router: Router,
		private modal: NgbActiveModal,
		private dialog: DialogService,
		private admLegadoService: AdmLegadoTelaClienteService,
		private snotify: SnotifyService
	) {}

	ngOnInit() {
		if (
			this.cliente == null ||
			this.chave == null ||
			this.usuarioLogado == null
		) {
			this.snotify.error("Ocorreu um erro no carregamento dos dados.");
		}

		this.convitesInfo$ = this.admLegadoService
			.convidadosInfo(this.chave, this.cliente)
			.pipe(
				map((resp) => {
					if (resp && resp.content) {
						return resp.content;
					}
					return resp;
				}),
				tap((resp) => {
					this._convitesInfo = resp;
				})
			);

		this.busca.valueChanges.subscribe((search) => {
			this.showDropdown = !!search;
			this._busca$.next(search);
			this.convidado = null;
		});
	}

	cadastrarConvidado() {
		this.modal.close();
		this.router.navigate([
			"pessoas",
			"perfil-v2",
			this.matricula,
			"cadastrar-convidado",
		]);
	}

	lancarConvidado() {
		if (!this.convidado) {
			this.snotify.error("Nenhum convidado selecionado.");
			return;
		}
		if (this._convitesInfo && this._convitesInfo.disponivel === 0) {
			this.snotify.error("Esse cliente não possui convites disponíveis.");
			return;
		}

		this.admLegadoService
			.salvarConvidado(this.chave, {
				cliente: this.cliente,
				usuario: this.usuarioLogado,
				convidado: this.convidado.codigo,
			})
			.subscribe({
				next: (resp) => {
					console.log("save", { resp });
					this.snotify.success("Convite enviado com sucesso.");
					this.modal.close();
				},
				error: (error) => {
					const errorMessage =
						error.error !== undefined &&
						error.error.meta !== undefined &&
						error.error.meta.message
							? error.error.meta.message
							: "";
					const messageBody =
						"Ocorreu um erro ao tentar enviar o convite. " + errorMessage;
					this.snotify.error(messageBody);
				},
			});
	}

	abrirHistorico(): void {
		this.modal.close();
		const dialogRef = this.dialog.open(
			"Histórico de convidados",
			HistoricoConvidadosComponent,
			PactoModalSize.LARGE
		);

		dialogRef.componentInstance.matricula = this.matricula;
		dialogRef.componentInstance.cliente = this.cliente;
		dialogRef.componentInstance.chave = this.chave;
		dialogRef.componentInstance.usuarioLogado = this.usuarioLogado;
	}

	selecionarConvidado(convidado): void {
		console.log("heeey", convidado);
		this.busca.patchValue(convidado.nome);
		this.convidado = convidado;
		this.showDropdown = false;
	}
}
