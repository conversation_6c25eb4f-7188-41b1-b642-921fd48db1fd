<div class="modal-convidados-content">
	<div class="text-content">
		<span class="head">Convidados utilizados:</span>
		<br />
		<ng-container *ngIf="convitesInfo$ | async as convitesInfo">
			<span class="text">
				{{ convitesInfo?.usados || 0 }} de
				{{ convitesInfo?.direito || 0 }} disponíveis por mês
			</span>
		</ng-container>
	</div>

	<div #autoCompleteContainer class="convidados-autocomplete-container">
		<pacto-cat-form-input
			[control]="busca"
			id="mc-inpt-nome-convidade"
			label="Nome do convidado"
			placeholder="Busca rápida..."
			type="text"></pacto-cat-form-input>

		<div #autocomplete *ngIf="showDropdown" class="pacto-cat-autocomplete">
			<ng-container *ngIf="consultarConvidado$ | async as consultarConvidado">
				<pacto-cat-option
					(click)="selecionarConvidado(convidado)"
					*ngFor="let convidado of consultarConvidado; let index = index"
					[id]="'mc-atcmpt-convidado-' + index">
					<div class="convidado-option">
						{{ convidado.nome }}
					</div>
				</pacto-cat-option>
				<pacto-cat-option
					*ngIf="consultarConvidado.length === 0"
					class="no-hover"
					id="mc-atcmpt-convidado-no-results">
					<div>Nenhum convidado encontrado.</div>
				</pacto-cat-option>
			</ng-container>
		</div>
	</div>

	<div class="buttons">
		<pacto-cat-button
			(click)="abrirHistorico()"
			id="historicoConvidado"
			label="Histórico"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="cadastrarConvidado()"
			id="cadastrarConvidado"
			label="Cadastrar convidado"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="lancarConvidado()"
			id="lancarConvidado"
			label="Lançar convidado"
			size="LARGE"></pacto-cat-button>
	</div>
</div>
