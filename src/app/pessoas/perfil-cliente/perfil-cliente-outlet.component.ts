import { Component, OnInit } from "@angular/core";
import { PerfilClienteService } from "./perfil-cliente.service";

@Component({
	selector: "pacto-perfil-cliente-outlet",
	template: `
		<pacto-cat-layout-v2>
			<router-outlet></router-outlet>
		</pacto-cat-layout-v2>
	`,
})
export class PerfilClienteOutletComponent implements OnInit {
	constructor(private perfilClienteService: PerfilClienteService) {}

	ngOnInit() {
		this.perfilClienteService.loadData();
	}
}
