@import "projects/ui/assets/import.scss";

.nav-aux {
	flex-basis: 100%;
	margin: 20px 0px 20px 0px;
	font-size: 32px;
	font-weight: 400;
	line-height: 44px;

	a {
		color: $pretoPri;
	}

	i {
		margin-right: 12px;
	}

	.acesso {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $preto02;
		margin-left: 45px;
	}
}

.profile {
	display: flex;
	align-content: center;
	justify-content: flex-start;
	align-items: center;
	margin: 10px 35px;

	.profileInfo {
		margin: 20px 0 0 20px;

		.nomeProfile {
			@extend .type-h5-bold;
			font-weight: 700;
			color: $azulPactoPri;
		}

		.contratoProfileLabel {
			@extend .type-p-small-rounded;
			font-weight: 700;
		}

		.contratoProfileNumber {
			@extend .type-p-small-rounded;
		}

		.contratoProfile {
			padding: 15px 0 0 0;
		}
	}
}
