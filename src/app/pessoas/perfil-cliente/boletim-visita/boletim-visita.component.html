<pacto-cat-layout-v2>
	<div class="nav-aux">
		<a
			[routerLink]="['/cadastros', 'alunos', 'perfil', matricula]"
			class="top-navigation"
			id="voltar-alunos">
			<i class="pct pct-arrow-left"></i>
			<span i18n="@@boletim-visita:page:boletim-visita-title">
				Boletim de visita
			</span>
		</a>
		<div class="acesso">
			<span i18n="@@boletim-visita:page:ultimo-acesso">Último acesso:</span>
			01/07/2021 - 18:45:22
		</div>
	</div>
	<pacto-cat-card-plain>
		<div *ngIf="profileData" class="profile">
			<pacto-cat-person-avatar
				[diameter]="95"
				[uri]="profileData.urlFoto"
				class="profileAvatar"></pacto-cat-person-avatar>
			<div class="profileInfo">
				<span class="nomeProfile">{{ profileData.nome }}</span>
				<p class="contratoProfile">
					<span
						class="contratoProfileLabel"
						i18n="@@boletim-visita:page:contrato">
						Contrato:
					</span>
					<span class="contratoProfileNumber">
						{{ profileData?.matricula }}
					</span>
				</p>
			</div>
		</div>
		<pacto-relatorio
			#tableData
			(iconClick)="iconClickFn($event)"
			[showShare]="false"
			[table]="table"
			actionTitle="Ações"></pacto-relatorio>
	</pacto-cat-card-plain>
</pacto-cat-layout-v2>
<ng-template #columnNomeDoQuestionario>
	<span i18n="@@boletim-visita:tableColumn:NomeDoQuestionario">
		Nome do questionário
	</span>
</ng-template>
<ng-template #columnDataDeCadastro>
	<span i18n="@@boletim-visita:tableColumn:DataDeCadastro">
		Data de cadastro
	</span>
</ng-template>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@boletim-visita:invalid-student-error" xingling="aluno-invalido">
		Desculpe, não foi possível encontrar informações referentes ao aluno com
		essa matrícula.
	</span>
	<span i18n="@@boletim-visita:redirect-msg" xingling="redirect-msg">
		Em instantes vocês será redirecionado para a tela de listagem.
	</span>
	<span i18n="@@boletim-visita:acesse-aluno-v2" xingling="acesse-aluno-v2">
		É nescessario acessar boletim de visitas a partir de aluno V2 ou ter
		acessado a pagina aluno V2 anteriormente, retornando para listagem de alunos
	</span>
</pacto-traducoes-xingling>
