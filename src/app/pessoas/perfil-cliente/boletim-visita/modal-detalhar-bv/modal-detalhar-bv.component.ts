import { Component, OnInit, Input, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { AdmCoreApiBoletimVisitaService } from "adm-core-api";
import { Observable, of } from "rxjs";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { Api } from "@base-core/rest/rest.model";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-detalhar-bv",
	templateUrl: "./modal-detalhar-bv.component.html",
	styleUrls: ["./modal-detalhar-bv.component.scss"],
})
export class ModalDetalharBvComponent implements OnInit {
	constructor(
		public restApi: RestService,
		private boletimVisitaService: AdmCoreApiBoletimVisitaService,
		private notificationService: SnotifyService
	) {}

	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@Input() rowData: any;
	@Input() codigoCliente: any;
	@Input() rowIndex: any;
	@Input() type: "Editar" | "Detalhar";
	formGroup = new FormGroup({
		consultor: new FormControl(),
		evento: new FormControl(),
		observacoes: new FormControl(),
		ultimaAtualizacao: new FormControl(new Date()),
	});

	ngOnInit() {
		this.getQuestionario().subscribe((response) => {
			this.formGroup.get("consultor").patchValue(this.rowData.consultor);
			this.formGroup.get("evento").patchValue(this.rowData.evento);
			this.formGroup.get("observacoes").patchValue(this.rowData.observacao);
			response.content.questionarioPerguntaCliente.forEach((element) => {
				this.addControlToGroup(element);
			});
		});
	}

	consultorSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				searchTerm: term,
				quicksearchFields: ["nome"],
				tipoColaborador: ["CO"],
			}),
		};
	};

	eventoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				searchTerm: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	labelFnNomeColaboradorPessoa = (item: any) => {
		return !item.pessoa.nome ? "-" : item.pessoa.nome;
	};
	labelFnNomeEvento = (item) => {
		return !item.descricao ? "-" : item.descricao;
	};

	getQuestionario(): Observable<{ content }> {
		return of({ content: this.rowData });
	}

	getUrlConsultores() {
		return this.restApi.buildFullUrl(
			`colaboradores/ativos`,
			true,
			Api.MSADMCORE
		);
	}

	getUrlEvento() {
		return this.restApi.buildFullUrl(`eventos`, true, Api.MSADMCORE);
	}

	addControlToGroup(campoDaVez) {
		const isDisabled = this.type === "Detalhar" ? true : false;
		switch (campoDaVez.perguntaCliente.tipoPergunta) {
			case "ME":
				const formAuxiliarME = new FormGroup({});
				campoDaVez.perguntaCliente.respostaPergCliente.forEach((data) => {
					formAuxiliarME.addControl(
						data.codigo.toString(),
						new FormControl({
							value: data.respostaOpcao,
							disabled: isDisabled,
						})
					);
				});

				this.formGroup.addControl(
					campoDaVez.perguntaCliente.codigo,
					formAuxiliarME
				);

				break;
			case "SE":
				const controlAuxiliarSE = new FormControl({});
				campoDaVez.perguntaCliente.respostaPergCliente.forEach((resposta) => {
					if (resposta.respostaOpcao) {
						controlAuxiliarSE.patchValue(resposta);
					}
				});
				this.formGroup.addControl(
					campoDaVez.perguntaCliente.codigo.toString(),
					controlAuxiliarSE
				);

				break;
			case "TE":
				const formAuxiliarTE = new FormGroup({});

				campoDaVez.perguntaCliente.respostaPergCliente.forEach((data) => {
					formAuxiliarTE.addControl(
						data.codigo.toString(),
						new FormControl({
							value: data.respostaTextual,
							disabled: isDisabled,
						})
					);
				});
				this.formGroup.addControl(
					campoDaVez.perguntaCliente.codigo,
					formAuxiliarTE
				);
				break;
		}
	}

	getFormControl(perguntaCod, respostaCod) {
		return this.formGroup.controls[perguntaCod.toString()].get(
			respostaCod.toString()
		);
	}

	getFormControlSE(perguntaCod) {
		return this.formGroup.controls[perguntaCod.toString()];
	}

	salvar() {
		const auxEditRow = this.rowData;
		auxEditRow.questionarioPerguntaCliente.forEach(
			(pergunta, indexPergunta) => {
				switch (pergunta.perguntaCliente.tipoPergunta) {
					case "ME":
						pergunta.perguntaCliente.respostaPergCliente.forEach(
							(resposta, indexResposta) => {
								auxEditRow.questionarioPerguntaCliente[
									indexPergunta
								].perguntaCliente.respostaPergCliente[
									indexResposta
								].respostaOpcao = this.getFormControl(
									pergunta.perguntaCliente.codigo.toString(),
									resposta.codigo
								).value;
							}
						);
						break;
					case "TE":
						pergunta.perguntaCliente.respostaPergCliente.forEach(
							(resposta, indexResposta) => {
								auxEditRow.questionarioPerguntaCliente[
									indexPergunta
								].perguntaCliente.respostaPergCliente[
									indexResposta
								].respostaTextual = this.getFormControl(
									pergunta.perguntaCliente.codigo.toString(),
									resposta.codigo
								).value;
							}
						);
						break;
					case "SE":
						pergunta.perguntaCliente.respostaPergCliente.forEach((resposta) => {
							if (
								this.getFormControlSE(pergunta.perguntaCliente.codigo).value
									.codigo === resposta.codigo
							) {
								resposta.respostaOpcao = true;
							} else {
								resposta.respostaOpcao = false;
							}
						});
						break;
				}
			}
		);

		auxEditRow.consultor =
			this.formGroup.get("consultor").value !== undefined
				? this.formGroup.get("consultor").value
				: this.rowData.consultor;
		auxEditRow.evento =
			this.formGroup.get("evento").value !== undefined
				? this.formGroup.get("evento").value
				: this.rowData.evento;
		auxEditRow.observacao =
			this.formGroup.get("observacoes").value !== undefined
				? this.formGroup.get("observacoes").value
				: this.rowData.observacoes;
		auxEditRow.ultimaAtualizacao = new Date().getTime();

		this.boletimVisitaService.editarQuestionario(auxEditRow).subscribe(
			(response) => {
				this.notificationService.success(
					this.traducao.getLabel("saved-success")
				);
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}
}
