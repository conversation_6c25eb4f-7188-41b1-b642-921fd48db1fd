@import "projects/ui/assets/import.scss";

.title-question {
	font-family: Nunito Sans;
	font-size: 12px;
	font-weight: 400;
	line-height: 16px;
	letter-spacing: 0px;
	text-align: left;

	.bold {
		@extend .type-caption-rounded;
		font-weight: bold;
		color: $canetaBicPri;
	}
}

.modal-content {
	padding: 21px 34px;
	border: none;
}

.questions-section {
	padding: 32px 43px;
}

.questions-section:nth-child(odd) {
	border-right: $cinza02 1px solid;
}

hr.divider {
	width: 100%;
	border-bottom: $cinza02 1px solid;
}

hr.question-divider {
	margin-right: 15%;
	border-bottom: $cinza02 1px solid;
}

.checkbox-pergunta::ng-deep div label .text,
.textarea-pergunta::ng-deep div span,
.radio-pergunta-label {
	@extend .type-caption-rounded;
	font-size: 9.6px;
}

.select-edicao {
	margin: 0;
}
