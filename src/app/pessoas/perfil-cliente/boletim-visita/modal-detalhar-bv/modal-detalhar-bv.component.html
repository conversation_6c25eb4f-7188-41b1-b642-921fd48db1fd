<section class="section-dados">
	<div class="modal-content">
		<h1 class="title-questionario" i18n="@@boletim-visita:modal:questionario">
			Questionário
		</h1>
		<div class="row">
			<ng-container
				*ngFor="let questionario of rowData.questionarioPerguntaCliente">
				<div class="questions-section col-md-12 col-xl-6">
					<div class="title-question">
						<h4 class="bold">{{ questionario.perguntaCliente.descricao }}</h4>
						<hr class="question-divider" />
					</div>
					<ng-container [ngSwitch]="questionario.perguntaCliente.tipoPergunta">
						<ng-container *ngSwitchCase="'ME'">
							<div class="row">
								<div
									*ngFor="
										let resposta of questionario.perguntaCliente
											.respostaPergCliente
									"
									class="col-md-6 col-lg-4 col-xl-3">
									<pacto-cat-checkbox
										[control]="
											getFormControl(
												questionario.perguntaCliente.codigo,
												resposta.codigo
											)
										"
										[label]="resposta.descricaoRespota"
										class="checkbox-pergunta"></pacto-cat-checkbox>
								</div>
							</div>
						</ng-container>
						<ng-container *ngSwitchCase="'TE'">
							<div class="row">
								<div class="col-12">
									<pacto-cat-form-textarea
										[control]="
											getFormControl(
												questionario.perguntaCliente.codigo,
												questionario.perguntaCliente.respostaPergCliente[0]
													.codigo
											)
										"
										class="textarea-pergunta"
										placeholder="Insira uma descrição para esta resposta"
										rows="5"></pacto-cat-form-textarea>
								</div>
							</div>
						</ng-container>
						<ng-container *ngSwitchCase="'SE'">
							<pacto-cat-radio-group
								[formControl]="
									getFormControlSE(questionario.perguntaCliente.codigo)
								">
								<pacto-cat-radio
									*ngFor="
										let resposta of questionario.perguntaCliente
											.respostaPergCliente
									"
									[id]="questionario.perguntaCliente.codigo"
									[value]="resposta">
									<label [for]="resposta.codigo" class="radio-pergunta-label">
										{{ resposta.descricaoRespota }}
									</label>
								</pacto-cat-radio>
							</pacto-cat-radio-group>
						</ng-container>
					</ng-container>
				</div>
			</ng-container>
		</div>
	</div>
</section>
<section *ngIf="type == 'Editar'" class="section-salvar">
	<hr class="divider" />
	<div class="modal-content">
		<div class="row">
			<div class="col-6">
				<pacto-cat-form-select-filter
					[addEmptyOption]="false"
					[control]="formGroup.get('consultor')"
					[endpointUrl]="getUrlConsultores()"
					[labelFn]="labelFnNomeColaboradorPessoa"
					[paramBuilder]="consultorSelectBuilder"
					class="select-edicao"
					i18n-label="@@boletim-visita:modal:input-colaborador"
					idKey="codigo"
					label="Consultor"></pacto-cat-form-select-filter>
				<pacto-cat-form-select-filter
					[addEmptyOption]="false"
					[control]="formGroup.get('evento')"
					[endpointUrl]="getUrlEvento()"
					[labelFn]="labelFnNomeEvento"
					[paramBuilder]="eventoSelectBuilder"
					class="select-edicao"
					i18n-label="@@boletim-visita:modal:input-evento"
					idKey="descricao"
					label="Evento"></pacto-cat-form-select-filter>
			</div>
			<div class="col-6">
				<pacto-cat-form-textarea
					[control]="formGroup.get('observacoes')"
					i18n-label="@@boletim-visita:modal:input-observacoes"
					label="Observações"></pacto-cat-form-textarea>
			</div>
		</div>
		<div class="row justify-content-end">
			<pacto-cat-button
				(click)="salvar()"
				[iconPosition]="'before'"
				[icon]="'pct pct-save'"
				[label]="'salvar'"
				[size]="'LARGE'"
				[type]="'PRIMARY'"
				i18n-label="@@boletim-visita:modal:button-salvar"
				style="margin: 0px 15px 32px 0px"></pacto-cat-button>
		</div>
	</div>
</section>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@boletim-visita:modal:questionario" xingling="saved-success">
		Questionário Salvo com sucesso!
	</span>
</pacto-traducoes-xingling>
