import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { Api } from "@base-core/rest/rest.model";
import { ActivatedRoute, Router } from "@angular/router";
import { Observable, of } from "rxjs";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import { ModalDetalharBvComponent } from "./modal-detalhar-bv/modal-detalhar-bv.component";
import { SnotifyService } from "ng-snotify";
import { catchError, tap } from "rxjs/operators";

declare var moment;

@Component({
	selector: "pacto-boletim-visita",
	templateUrl: "./boletim-visita.component.html",
	styleUrls: ["./boletim-visita.component.scss"],
})
export class BoletimVisitaComponent implements OnInit {
	@ViewChild("columnNomeDoQuestionario", { static: true })
	columnNomeDoQuestionario: TemplateRef<any>;
	@ViewChild("columnDataDeCadastro", { static: true })
	columnDataDeCadastro: TemplateRef<any>;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	table: PactoDataGridConfig;

	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;

	matricula: number;
	profileData: any;

	estados: {
		content: [];
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: [],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	ngbModal: any;
	codCliente;

	constructor(
		public restApi: RestService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private dialogService: DialogService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private router: Router,
		private snotify: SnotifyService
	) {}

	ngOnInit() {
		this.initTable();
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");

		if (!this.matricula) {
			this.router.navigate(["/cadastros", "alunos", "listagem"]);
		}

		if (this.matricula) {
			this.msAdmCoreService.dadosPessoais(this.matricula).subscribe(
				(data) => {
					this.profileData = data;
					this.codCliente = data.codigoCliente;
					this.table.endpointUrl = this.restApi.buildFullUrl(
						`clientes/${this.codCliente}/historico-bvs`,
						true,
						Api.MSADMCORE
					);
					this.tableData.reloadData();
				},
				(error) => {
					console.error(error);
					this.handleInvalidUserError();
					return of(null);
				}
			);
			this.cd.detectChanges();
		}
	}

	private handleInvalidUserError(): void {
		this.snotify.error(this.traducao.getLabel("aluno-invalido"), {
			timeout: 3000,
		});

		setTimeout(() => {
			this.snotify.info(this.traducao.getLabel("redirect-msg"), {
				timeout: 3000,
			});
		}, 3000);

		setTimeout(() => {
			this.router.navigate(["/cadastros", "alunos", "listagem"]);
		}, 6500);
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "questionario",
						titulo: this.columnNomeDoQuestionario,
						visible: true,
						ordenavel: true,
						valueTransform: (d) => d.nomeInterno,
					},
					{
						nome: "data",
						titulo: this.columnDataDeCadastro,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					},
				],
				actions: [
					{
						nome: "editar",
						iconClass: "pct pct-edit cor-azulim05",
						tooltipText: "editar",
						actionFn: (row, index) => this.editar(row, index),
					},
					{
						nome: "detalhar",
						iconClass: "pct pct-eye cor-azulim05",
						tooltipText: "detalhar",
						actionFn: (row, index) => this.detalhar(row, index),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	iconClickFn(event: { row: any; iconName: string; rowIndex: number }) {
		if (event.iconName === "editar") {
			this.editar(event.row, event.rowIndex);
		} else if (event.iconName === "detalhar") {
			this.detalhar(event.row, event.rowIndex);
		}
	}

	editar(rowData, rowIndex) {
		this.openModal(rowData, "Editar", rowIndex);
	}

	detalhar(rowData, rowIndex) {
		this.openModal(rowData, "Detalhar", rowIndex);
	}

	openModal(rowData, type, rowIndex) {
		const dialogRef = this.dialogService.open(
			`${rowData.questionario.nomeInterno} - ${type}`,
			ModalDetalharBvComponent,
			PactoModalSize.LARGE,
			"modal-detalhar-bv"
		);
		dialogRef.componentInstance.rowData = rowData;
		dialogRef.componentInstance.type = type;
		dialogRef.componentInstance.codigoCliente = this.codCliente;
		dialogRef.componentInstance.rowIndex = rowIndex;
	}
}
