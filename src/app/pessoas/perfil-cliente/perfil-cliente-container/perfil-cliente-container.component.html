<!-- <div class="toast-beta" *ngIf="!recursoTelaAlunoPadraoEmpresa">
	<div class="toast-text">
		<span>
			<i class="pct pct-info icone"></i>
			Você está experimentando a nova versão aproveite e teste as melhor<PERSON>.
			Você poderá nos enviar feedbacks e ajudar a construir um sistema melhor
			para o seu dia a dia.
		</span>
	</div>
	<div class="toast-button">
		<pacto-cat-button
			label="Enviar Feedback"
			type="OUTLINE_NO_BORDER"
			size="LARGE"
			(click)="openFeedbackForm()"></pacto-cat-button>
	</div>
</div> -->

<div class="nav-aux">
	<a class="top-navigation" id="voltar-alunos" (click)="back()">
		<i class="pct pct-arrow-left"></i>
		<span i18n="@@perfil-aluno-dados-pessoal:title:perfil-aluno">
			Perfil do aluno
		</span>
	</a>
	<pacto-recurso-padrao
		class="recurso"
		[recurso]="recurso"
		(acao)="voltarAntiga($event)"></pacto-recurso-padrao>
</div>

<pacto-perfil-cliente-header-v2></pacto-perfil-cliente-header-v2>

<div class="tabs" *ngIf="abaHabilitada()" [ngClass]="selecionado">
	<div
		*ngFor="let aba of abas; let index = index"
		(click)="clickAba(aba.route)"
		id="{{ aba.id }}"
		class="{{ aba.class }}"
		routerLink="{{ aba.route }}">
		{{ aba.nome }}
	</div>
</div>

<div *ngIf="!abaHabilitada()" class="div-empty">
	<div class="div-interna-empty">
		<img class="cadeado-empty" src="assets/images/cadeado.svg" />
		<div class="titulo-empty">Não foi possível acessar as informações</div>
		<div class="text-empty">
			Com o intuito de assegurar a proteção dos dados dos clientes, determinadas
			informações devem ser restringidas conforme definido pelo gestor ou
			administrador. Se houver necessidade de acesso, solicite que eles liberem
			as permissões para seu perfil de acesso.
		</div>
	</div>
</div>

<div *ngIf="!reload && abaHabilitada()" class="tab-content">
	<router-outlet></router-outlet>
</div>
