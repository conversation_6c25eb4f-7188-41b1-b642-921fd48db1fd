@import "~src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

.tabs {
	display: flex;
	flex-direction: row;
	margin: 20px 0px;
	border-bottom: 1px solid $cinza03;

	.tab {
		@extend .pct-btn-default2;
		color: $supportBlack06 !important;
		margin: 0px;
		padding: 10px 20px;
		cursor: pointer;
		position: relative;
	}

	&.contratos .contratos,
	&.financeiro .financeiro,
	&.pactopay .pactopay,
	&.acessos .acessos,
	&.treinos .treinos,
	&.avaliacao .avaliacao,
	&.graduacao .graduacao,
	&.crm .crm,
	&.notafiscal .notafiscal,
	&.documentos .documentos,
	&.produtos .produtos {
		color: $pretoPri;
		position: relative;

		&::after {
			content: "";
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 3px;
			background-color: $actionDefaultAble04;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
		}
	}
}

.nav-aux {
	display: grid;
	grid-template-columns: 1fr 1fr;
	align-items: center;
	flex-basis: 100%;
	margin: 20px 0px 20px 0px;
	font-size: 32px;
	font-weight: 400;
	line-height: 44px;

	:first-child {
		justify-self: start; /* Alinhamento esquerdo */
	}

	:last-child {
		justify-self: end; /* Alinhamento direito */
	}

	a {
		color: $pretoPri;
	}

	i {
		margin-right: 12px;
	}

	.acesso {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $preto02;
		margin-left: 45px;
	}

	#voltar-alunos {
		cursor: pointer;
	}
}

.toast-warning {
	background-color: #ffffff;
	border-radius: 8px;
	padding: 16px;
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 20px;

	.toast-text {
		font-family: Poppins, sans-serif;
		font-size: 12px;
		font-weight: 400;
		line-height: 15px;
		color: #7d7d03;

		span {
			display: flex;
			align-items: center;
			gap: 8px;

			.icone {
				width: 24px;
				height: 24px;
				color: #7d7d03;

				&.pct-alert-triangle {
					font-size: 24px;
				}

				&.pct-help-circle {
					width: 14.66px;
					height: 14.66px;
					font-size: 18px;
					color: #7d7d03;
					position: relative;
					top: -2px;
				}
			}

			a {
				color: #1e60fa; /* Azul para o link */
				text-decoration: none;
				font-weight: 600;
				font-size: 15px; /* Ajuste para manter o alinhamento */

				&:hover {
					text-decoration: underline; /* Sublinha ao passar o mouse */
				}
			}
		}
	}
}

.toast-beta {
	background-color: #fafafa;
	border-radius: 5px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;

	.toast-text {
		color: #797d86;

		span {
			.icone {
				font-size: 18px;
				padding: 8px;
			}

			strong {
				//styleName: Button/Default/2;
				font-family: Poppins;
				font-size: 12px;
				font-weight: 600;
				line-height: 12px;
				letter-spacing: 0.25px;
				text-align: left;
			}

			//styleName: Overline/2;
			font-family: Nunito Sans;
			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
		}
	}

	.toast-button {
		padding: 6px;
	}
}

.btn-voltar-legado {
	font-family: "Nunito Sans", sans-serif;
	font-size: 16px;
	border: 1px solid #b4b7bb;
	border-radius: 5px;
	cursor: pointer;
	color: #b4b7bb !important;
	font-weight: 600;
	padding: 0 10px;
}

.div-empty {
	margin: 5px 0;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 #e5e9f2;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	width: 100%;
	margin-top: 20px;
	padding: 24px 16px 25px 22px;
	align-items: center;

	.div-interna-empty {
		width: 450px;
		text-align: center;
		justify-content: center;

		.cadeado-empty {
			width: 112px;
			height: 112px;
		}

		.titulo-empty {
			padding-top: 20px;
			font-size: 14px;
			color: #55585e;
			font-weight: 600;
		}

		.text-empty {
			font-size: 14px;
			color: #797d86;
			font-weight: 400;
			padding-top: 15px;
		}
	}
}
