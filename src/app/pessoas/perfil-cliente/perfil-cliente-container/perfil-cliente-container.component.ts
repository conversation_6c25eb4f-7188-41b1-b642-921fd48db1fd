import {
	AfterViewInit,
	ChangeDetector<PERSON>ef,
	<PERSON>mponent,
	On<PERSON><PERSON>roy,
	OnInit,
} from "@angular/core";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import {
	LayoutNavigationService,
	PlataformModuleConfig,
	PermissaoService,
} from "pacto-layout";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import {
	AdmCoreApiColaboradorService,
	AdmCoreApiNegociacaoService,
} from "adm-core-api";
import { Subscription } from "rxjs";

@Component({
	selector: "pacto-perfil-cliente-container",
	templateUrl: "./perfil-cliente-container.component.html",
	styleUrls: ["./perfil-cliente-container.component.scss"],
})
export class PerfilClienteContainerComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	padraoEmpresa = true;
	private routeSubscription: Subscription;
	selecionado: string;
	reload = false;
	recurso = "TELA_ALUNO";
	recursoTelaAlunoPadraoEmpresa: boolean = true;
	permissaoAbaContrato: boolean = false;
	permissaoAbaProduto: boolean = false;
	permissaoAbaFinanceiro: boolean = false;
	permissaoAbaPactoPay: boolean = false;
	permissaoAbaAcesso: boolean = false;
	permissaoAbaTreino: boolean = false;
	permissaoAbaAvaliacaoFisica: boolean = false;
	permissaoAbaGraduacao: boolean = false;
	permissaoAbaCrm: boolean = false;
	permissaoAbaNotaFiscal: boolean = false;
	permissaoAbaDocumento: boolean = false;
	abas: Array<{
		id?: string;
		nome?: string;
		class?: string;
		apresentar?: boolean;
		route?: string;
		ordemCO?: number;
		ordemTW?: number;
	}> = new Array<{
		id?: string;
		nome?: string;
		class?: string;
		apresentar?: boolean;
		route?: string;
		ordemCO?: number;
		ordemTW?: number;
	}>();

	constructor(
		private router: Router,
		private sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService,
		private route: ActivatedRoute,
		private colaboradorService: AdmCoreApiColaboradorService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private localStorageService: LocalStorageSessionService,
		private layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.init(false);
		this.routeSubscription = this.route.params.subscribe((params) => {
			this.reload = true;
			this.cd.detectChanges();
			setTimeout(() => {
				this.reload = false;
				this.cd.detectChanges();
				this.init(true);
			}, 200);
		});
	}

	private init(reinit) {
		this.abas = new Array<{
			id?: string;
			nome?: string;
			class?: string;
			apresentar?: boolean;
			route?: string;
			ordemCO?: number;
			ordemTW?: number;
		}>();
		this.carregarRecursoPadraoEmpresa();
		this.carregarPermissoes();
		this.carregarAbas();
		this.abrirAba();
		this.saveStudentId();
		if (!reinit) {
			this.fecharMenu();
		}
		this.layoutNavigationService.addModuleNavigationHistory(
			PlataformModuleConfig.ALUNO
		);
		this.verificarRecurso();
	}

	ngOnDestroy() {
		// Cancele a inscrição para evitar vazamento de memória
		if (this.routeSubscription) {
			this.routeSubscription.unsubscribe();
		}
	}

	carregarRecursoPadraoEmpresa() {
		this.admCoreApiNegociacaoService
			.recursoPadraoEmpresa(this.recurso)
			.subscribe(
				(response) => {
					this.recursoTelaAlunoPadraoEmpresa = response;
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
					this.recursoTelaAlunoPadraoEmpresa = false;
				}
			);
	}

	carregarPermissoes() {
		this.permissaoAbaContrato = this.permissaoService.temPermissaoAdm("13.00");
		this.permissaoAbaAvaliacaoFisica =
			this.permissaoService.temPermissaoAdm("13.01");
		this.permissaoAbaAcesso = this.permissaoService.temPermissaoAdm("13.02");
		this.permissaoAbaDocumento = this.permissaoService.temPermissaoAdm("13.03");
		this.permissaoAbaFinanceiro =
			this.permissaoService.temPermissaoAdm("13.04");
		this.permissaoAbaGraduacao = this.permissaoService.temPermissaoAdm("13.05");
		this.permissaoAbaPactoPay = this.permissaoService.temPermissaoAdm("13.06");
		this.permissaoAbaProduto = this.permissaoService.temPermissaoAdm("13.07");
		this.permissaoAbaTreino = this.permissaoService.temPermissaoAdm("13.08");
		this.permissaoAbaCrm = this.permissaoService.temPermissaoAdm("13.09");
		this.permissaoAbaNotaFiscal =
			this.permissaoService.temPermissaoAdm("13.10");
	}

	carregarAbas() {
		if (this.permissaoAbaContrato) {
			this.abas.push({
				id: "pcc-tab-contrato",
				nome: "Contratos",
				class: "tab contratos",
				apresentar: this.permissaoAbaContrato,
				route: "contratos",
				ordemCO: 1,
				ordemTW: 6,
			});
		}
		if (this.permissaoAbaProduto) {
			this.abas.push({
				id: "pcc-tab-produto",
				nome: "Produtos",
				class: "tab produtos",
				apresentar: this.permissaoAbaProduto,
				route: "produtos",
				ordemCO: 2,
				ordemTW: 7,
			});
		}
		if (this.permissaoAbaFinanceiro) {
			this.abas.push({
				id: "pcc-tab-financeiro",
				nome: "Financeiro",
				class: "tab financeiro",
				apresentar: this.permissaoAbaFinanceiro,
				route: "financeiro",
				ordemCO: 3,
				ordemTW: 8,
			});
		}
		if (this.permissaoAbaPactoPay) {
			this.abas.push({
				id: "pcc-tab-pactopay",
				nome: "PactoPay",
				class: "tab pactopay",
				apresentar: this.permissaoAbaPactoPay,
				route: "pactopay",
				ordemCO: 4,
				ordemTW: 9,
			});
		}
		if (this.permissaoAbaAcesso) {
			this.abas.push({
				id: "pcc-tab-acesso",
				nome: "Acessos",
				class: "tab acessos",
				apresentar: this.permissaoAbaAcesso,
				route: "acessos",
				ordemCO: 5,
				ordemTW: 10,
			});
		}
		if (this.permissaoAbaTreino) {
			this.abas.push({
				id: "pcc-tab-treino",
				nome: "Treino",
				class: "tab treinos",
				apresentar: this.permissaoAbaTreino,
				route: "treinos",
				ordemCO: 7,
				ordemTW: 1,
			});
		}
		if (this.permissaoAbaAvaliacaoFisica) {
			this.abas.push({
				id: "pcc-tab-avaliacao",
				nome: "Avaliação Física",
				class: "tab avaliacao",
				apresentar: this.permissaoAbaAvaliacaoFisica,
				route: "avaliacao",
				ordemCO: 8,
				ordemTW: 2,
			});
		}
		if (this.permissaoAbaGraduacao) {
			this.abas.push({
				id: "pcc-tab-graduacao",
				nome: "Graduação",
				class: "tab graduacao",
				apresentar: this.permissaoAbaGraduacao,
				route: "graduacao",
				ordemCO: 9,
				ordemTW: 3,
			});
		}
		if (this.permissaoAbaCrm) {
			this.abas.push({
				id: "pcc-tab-crm",
				nome: "CRM",
				class: "tab crm",
				apresentar: this.permissaoAbaCrm,
				route: "crm",
				ordemCO: 6,
				ordemTW: 4,
			});
		}
		if (this.permissaoAbaNotaFiscal) {
			this.abas.push({
				id: "pcc-tab-notafiscal",
				nome: "Notas Fiscais",
				class: "tab notafiscal",
				apresentar: this.permissaoAbaNotaFiscal,
				route: "notafiscal",
				ordemCO: 11,
				ordemTW: 11,
			});
		}
		if (this.permissaoAbaDocumento) {
			this.abas.push({
				id: "pcc-tab-documento",
				nome: "Documentos",
				class: "tab documentos",
				apresentar: this.permissaoAbaDocumento,
				route: "documentos",
				ordemCO: 10,
				ordemTW: 5,
			});
		}
	}

	ngAfterViewInit() {
		try {
			this.sessionService.notificarRecursoEmpresa(
				RecursoSistema.NOVA_TELA_ALUNO_CARREGOU
			);
		} catch (e) {
			console.error(e);
		}
		this.limparPactoCatTolltip();
	}

	fecharMenu() {
		try {
			const menu = document.getElementById("sidebar-menu-toggle");
			if (menu.classList.contains("opened")) {
				menu.click();
			}
		} catch (e) {
			console.log(e);
		}
	}

	private abrirAba() {
		this.abas.sort((a, b) => (a.ordemCO < b.ordemCO ? -1 : 1));
		this.colaboradorService
			.findTipoColaborador(0, this.sessionService.codigoUsuarioZw)
			.subscribe((response) => {
				if (
					response.filter((x) => x === "CO").length === 0 &&
					response.filter((x) => x === "TW").length > 0
				) {
					this.abas.sort((a, b) => (a.ordemTW < b.ordemTW ? -1 : 1));
				}
				const split = this.router.url.split("?")[0].split("/");
				if (split.length > 4) {
					this.selecionado = split.pop();
				}
				if (this.selecionado) {
					// verificar se existe a aba na lista das abas disponíveis
					if (
						this.abas.filter((x) => x.route === this.selecionado).length === 0
					) {
						this.selecionado = this.abas[0].route;
					}
				} else {
					this.selecionado = this.abas[0].route;
				}
				this.router.navigate([
					"pessoas",
					"perfil-v2",
					this.route.snapshot.params["aluno-matricula"],
					this.selecionado,
				]);
			});
	}

	clickAba(aba) {
		this.selecionado = aba;
	}

	get matricula() {
		return this.route.snapshot.params["aluno-matricula"] + "?origem=bi";
	}

	back(): void {
		this.router.navigate(["pessoas", "lista-v2"]);
	}

	saveStudentId(): void {
		const studentId = this.route.snapshot.params["aluno-matricula"];
		sessionStorage.setItem("pacto-aluno-matricula", studentId);
	}

	openFeedbackForm() {
		window.open("https://forms.gle/NJDD8BRfHMo6gXFw9", "_blank");
	}

	voltarAntiga(event) {
		if (event.origem === "ZW") {
			this.abrirAntigaTelaClienteZW();
		} else {
			this.abrirAntigaTelaClienteTR();
		}
	}

	abrirAntigaTelaClienteZW() {
		try {
			this.sessionService.notificarRecursoEmpresa(
				RecursoSistema.NOVA_TELA_ALUNO_VOLTAR_ZW
			);
		} catch (e) {
			console.error(e);
		}
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				const url = `${urlZw}&urlRedirect=uriCliente&matriculaCliente=${this.matricula}&menu=true`;
				window.open(url, "_self");
			});
	}

	abrirAntigaTelaClienteTR() {
		try {
			this.sessionService.notificarRecursoEmpresa(
				RecursoSistema.NOVA_TELA_ALUNO_VOLTAR_TR
			);
		} catch (e) {
			console.error(e);
		}
		this.router.navigateByUrl(
			`/cadastros/alunos/perfil/${this.route.snapshot.params["aluno-matricula"]}%3Forigem%3Dbi`
		);
	}

	abaHabilitada() {
		return (
			this.permissaoAbaContrato ||
			this.permissaoAbaProduto ||
			this.permissaoAbaFinanceiro ||
			this.permissaoAbaPactoPay ||
			this.permissaoAbaAcesso ||
			this.permissaoAbaTreino ||
			this.permissaoAbaAvaliacaoFisica ||
			this.permissaoAbaGraduacao ||
			this.permissaoAbaCrm ||
			this.permissaoAbaNotaFiscal ||
			this.permissaoAbaDocumento
		);
	}

	limparPactoCatTolltip() {
		try {
			const elementsByClassName =
				document.getElementsByClassName("pacto-cat-tolltip");
			const array = Array.from(elementsByClassName);
			for (const element of array) {
				document.getElementById(element.id).style.visibility = "hidden";
			}
		} catch (e) {
			console.log(e);
		}
	}

	verificarRecurso() {
		this.admCoreApiNegociacaoService
			.recursoPadraoEmpresa(this.recurso)
			.subscribe(
				(response) => {
					this.padraoEmpresa = response;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
					this.padraoEmpresa = false;
					this.cd.detectChanges();
				}
			);
	}
}
