import { TraducoesXinglingComponent } from "ui-kit";

export const getTipoAutorizacaoByCodigo = (
	id: number,
	traducao: TraducoesXinglingComponent
) => {
	return getTipoAutorizacao(traducao).find((ti) => +ti.id === +id);
};

export const getTipoAutorizacao = (traducao) => {
	return new Array<{ id: number; label: string }>(
		{ id: 0, label: traducao.getLabel("tipo_autorizacao_0") },
		{ id: 1, label: traducao.getLabel("tipo_autorizacao_1") },
		{ id: 2, label: traducao.getLabel("tipo_autorizacao_2") },
		{ id: 3, label: traducao.getLabel("tipo_autorizacao_3") },
		{ id: 4, label: traducao.getLabel("tipo_autorizacao_4") }
	);
};

export const getTipoParcelaCobrarByCodigo = (
	id: number,
	traducao: TraducoesXinglingComponent
) => {
	return getTipoParcelaCobrar(traducao).find((ti) => +ti.id === +id);
};

export const getTipoParcelaCobrar = (traducao) => {
	return new Array<{ id: number; label: string }>(
		{ id: 0, label: traducao.getLabel("tipo_parcela_0") },
		{ id: 1, label: traducao.getLabel("tipo_parcela_1") },
		{ id: 2, label: traducao.getLabel("tipo_parcela_2") },
		{ id: 3, label: traducao.getLabel("tipo_parcela_3") },
		{ id: 4, label: traducao.getLabel("tipo_parcela_4") }
	);
};

export const getTipoCobrancaByCodigo = (
	id: number,
	traducao: TraducoesXinglingComponent
) => {
	return getTipoCobranca(traducao).find((ti) => +ti.id === +id);
};

export const getTipoCobranca = (traducao) => {
	return new Array<{ id: number; label: string; hint: string }>(
		{
			id: 0,
			label: traducao.getLabel("tipo_cobranca_0"),
			hint: traducao.getLabel("tipo_cobranca_0_hint"),
		},
		{
			id: 1,
			label: traducao.getLabel("tipo_cobranca_1"),
			hint: traducao.getLabel("tipo_cobranca_1_hint"),
		},
		{
			id: 2,
			label: traducao.getLabel("tipo_cobranca_2"),
			hint: traducao.getLabel("tipo_cobranca_2_hint"),
		},
		{
			id: 3,
			label: traducao.getLabel("tipo_cobranca_3"),
			hint: traducao.getLabel("tipo_cobranca_3_hint"),
		},
		{
			id: 4,
			label: traducao.getLabel("tipo_cobranca_4"),
			hint: traducao.getLabel("tipo_cobranca_4_hint"),
		},
		{
			id: 5,
			label: traducao.getLabel("tipo_cobranca_5"),
			hint: traducao.getLabel("tipo_cobranca_5_hint"),
		},
		{
			id: 6,
			label: traducao.getLabel("tipo_cobranca_6"),
			hint: traducao.getLabel("tipo_cobranca_6_hint"),
		}
	);
};
