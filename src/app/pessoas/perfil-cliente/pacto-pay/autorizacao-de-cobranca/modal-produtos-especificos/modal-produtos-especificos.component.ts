import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDualMultiSelectConfig } from "./../dual-multi-select/pacto-dual-multi-select.model";
import { Component, OnInit, Input } from "@angular/core";

@Component({
	selector: "pacto-modal-produtos-especificos",
	templateUrl: "./modal-produtos-especificos.component.html",
	styleUrls: ["./modal-produtos-especificos.component.scss"],
})
export class ModalProdutosEspecificosComponent implements OnInit {
	@Input() dataConfig: PactoDualMultiSelectConfig;

	constructor(private activeModal: NgbActiveModal) {}

	ngOnInit() {}

	sendDataProd(event) {
		this.activeModal.close(event);
	}
}
