@import "dist/ui-kit/assets/import.scss";

.wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 2rem;
	text-align: center;
	color: #43474b;
}

.modal-image {
	max-width: 150px;
	height: auto;
	margin-bottom: 2rem;
}

#type {
	font-size: 140px;
	margin-bottom: 1rem;
}

.title {
	font-family: "Nunito Sans", sans-serif;
	font-weight: 600;
	font-size: 20px;
	line-height: 1.4;
	margin-bottom: 1rem;
	color: #333;
}

.subtitle {
	font-family: "Nunito Sans", sans-serif;
	font-weight: 400;
	font-size: 16px;
	line-height: 1.5;
	color: #555;
	max-width: 80%;
}

.body {
	background-color: #f8f8f8;
	border-radius: 8px;
	padding: 1rem;
	margin: 1rem 0;
	width: 80%;
	display: flex;
	align-items: center;
	justify-content: center;

	i {
		margin-right: 10px;
		color: #0a64ff;
	}

	span {
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		color: #555;
	}
}

.actions {
	margin-top: 1rem;
	width: 100%;
	display: flex;
	justify-content: center;

	pacto-cat-button {
		min-width: 150px;
	}
}

.pct-check {
	color: $verdinho05;
}

.pct-alert-triangle {
	color: $pequizaoPri;
}

.pct-x-circle {
	color: $hellboy04;
}

.pct-info {
	color: #0a64ff;
}

:host ::ng-deep pacto-cat-button .pacto-button {
	text-transform: none;
}
