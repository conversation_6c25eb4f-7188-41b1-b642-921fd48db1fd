<div class="wrapper">
	<img
		*ngIf="imagemUrl"
		[src]="imagemUrl"
		alt="Ilustração"
		class="modal-image" />
	<i *ngIf="!imagemUrl && type" [ngClass]="['pct', type]" id="type"></i>
	<span *ngIf="title" class="title">{{ title }}</span>
	<span *ngIf="subtitle" class="subtitle">{{ subtitle }}</span>
	<div *ngIf="body && isTemplate(body)" class="body">
		<ng-container *ngTemplateOutlet="body"></ng-container>
	</div>
	<div *ngIf="body && !isTemplate(body)" class="body" [innerHTML]="body"></div>
	<div *ngIf="actions && actions.length > 0" class="actions">
		<pacto-cat-button
			(click)="action.clickHandler()"
			*ngFor="let action of actions"
			[disabled]="action.disabled"
			[full]="action.full"
			[icon]="action.icon"
			[id]="action.id"
			[label]="action.label"
			[loading]="action.loading"
			[size]="action.size"
			[type]="action.type"
			[v2]="action.v2"></pacto-cat-button>
	</div>
</div>
