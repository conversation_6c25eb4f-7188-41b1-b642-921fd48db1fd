<div class="wrapper">
	<div *ngIf="autorizada" style="width: 100%">
		<pacto-cat-form-select
			[control]="formulario.get('tipo')"
			[errorMsg]="'Informe o tipo de bloqueio'"
			[id]="'tipo-de-bloqueio'"
			[items]="tipos"
			[label]="'Tipo de bloqueio'"
			class="col-12"
			idKey="codigo"
			labelKey="descricao"></pacto-cat-form-select>
		<pacto-cat-form-datepicker
			*ngIf="formulario.get('tipo').value === '1'"
			[control]="formulario.get('data')"
			[label]="'Data'"
			class="col-12"></pacto-cat-form-datepicker>
	</div>
	<div *ngIf="!autorizada">Autorizar cobrança automática?</div>
</div>
<hr />
<div class="actions">
	<pacto-cat-button
		(click)="cancelar()"
		[full]="true"
		[label]="'Cancelar'"
		size="MEDIUM"
		type="OUTLINE"></pacto-cat-button>
	<pacto-cat-button
		(click)="clickHandler()"
		[full]="true"
		[label]="'Confirmar'"
		size="MEDIUM"></pacto-cat-button>
</div>
