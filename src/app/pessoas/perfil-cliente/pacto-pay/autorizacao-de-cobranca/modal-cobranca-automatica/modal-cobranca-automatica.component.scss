@import "dist/ui-kit/assets/import.scss";

.wrapper {
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	color: #43474b;
	min-height: 200px;
	padding: 1rem;
}

hr {
	border: none;
	border-top: 1px dotted $cinzaPri;
	color: #fff;
	background-color: #fff;
	height: 1px;
	margin-top: 0px;
	margin-bottom: 0px;
}

.actions {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 1rem;

	pacto-cat-button {
		min-width: 100px;
		margin: 1rem;
	}
}

:host ::ng-deep {
	pacto-cat-button .pacto-button {
		text-transform: none;
	}

	pacto-cat-form-select {
		margin: 0;
		padding-left: 0;
	}

	.ds3-modal-cobranca-automatica {
		pacto-modal-cobranca-automatica {
			.wrapper {
				min-height: unset;
			}
		}
	}
}

::ng-deep {
	.ds3-modal-cobranca-automatica {
		pacto-modal-cobranca-automatica {
			.wrapper {
				min-height: unset;
			}
		}

		pacto-cat-form-datepicker {
			.pct-error-msg {
				display: none;
			}
		}

		pacto-cat-form-select {
			.pacto-label {
				font-size: 14px;
			}

			.pct-error-msg:empty {
				display: none;
			}
		}

		.pacto-modal-wrapper .modal-titulo .title {
			font-size: 14px !important;
		}

		.actions {
			padding: 0 16px;
			justify-content: end;
		}
	}
}
