import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment";
import { MsPactoPayApiCobrancaService } from "ms-pactopay-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-cobranca-automatica",
	templateUrl: "./modal-cobranca-automatica.component.html",
	styleUrls: ["./modal-cobranca-automatica.component.scss"],
})
export class ModalCobrancaAutomaticaComponent implements OnInit {
	@Output() cobrancaAutomatica: EventEmitter<any> = new EventEmitter();
	@Input() autorizada = false;
	@Input() pessoa: number;
	public tipos = [];

	public formulario: FormGroup;

	constructor(
		private msPactoPayApiCobranca: MsPactoPayApiCobrancaService,
		private formBuilder: FormBuilder,
		private notify: SnotifyService,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.formulario = this.formBuilder.group({
			tipo: this.formBuilder.control("", [Validators.required]),
			data: this.formBuilder.control(""),
		});

		this.tipos = [
			{ codigo: 0, descricao: "Todas as parcelas" },
			{ codigo: 1, descricao: "Vencimento maior que" },
		];

		this.formulario.get("tipo").valueChanges.subscribe((v) => {
			if (v === "1") {
				this.formulario.get("data").setValidators([Validators.required]);
			} else {
				this.formulario.get("data").clearValidators();
			}
		});
	}

	public clickHandler() {
		if (this.autorizada) {
			this.formulario.markAllAsTouched();
			if (this.formulario.valid) {
				const { tipo, data } = this.formulario.value;
				const dataFormatada = data ? moment(data).format("YYYYMMDD") : null;
				const dataParaHint = data ? moment(data).format("DD/MM/YYYY") : "";
				this.msPactoPayApiCobranca
					.bloquearAutorizacao({
						pessoa: this.pessoa,
						tipo,
						data: dataFormatada,
					})
					.subscribe(
						(res) => {
							this.notify.success(`Cobrança automática bloqueada com sucesso`);
							// Emitindo o status e o hint Exibir juntos
							const emitData = {
								status: "bloqueada",
								data,
								hintExibir:
									dataFormatada == null
										? "TODAS as parcelas estão bloqueadas para cobrança automática."
										: `TODAS as parcelas com vencimento superior ao dia ${dataParaHint} estão bloqueadas para cobrança automática.`,
							};

							this.cobrancaAutomatica.emit(emitData);
							this.openModal.close(true);
						},
						(httpResponseError) => {
							this.notify.error(httpResponseError.error.meta.message);
							return;
						}
					);
			}
		} else {
			this.msPactoPayApiCobranca
				.desbloquearAutorizacao({ pessoa: this.pessoa })
				.subscribe(
					(res) => {
						this.notify.success(
							"Cobrança automática desbloqueada com sucesso."
						);
						const emitData = {
							status: "desbloqueada",
							hintExibir: "A cobrança automática está liberada.",
						};
						this.cobrancaAutomatica.emit(emitData);
						this.openModal.close(true);
					},
					(httpResponseError) => {
						this.notify.error(httpResponseError.error.meta.message);
						return;
					}
				);
		}
	}

	public cancelar() {
		this.openModal.close(true);
	}
}
