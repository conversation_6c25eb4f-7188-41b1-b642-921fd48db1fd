import { EventEmitter } from "@angular/core";
import { DualMultiSelectServiceService } from "./dual-multi-select-service.service";
import { PactoDualMultiSelectConfig } from "./pacto-dual-multi-select.model";
import {
	Component,
	OnInit,
	Input,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	Output,
} from "@angular/core";

interface PendingSelection {
	[key: string]: boolean;
}

@Component({
	selector: "pacto-dual-multi-select",
	templateUrl: "./dual-multi-select.component.html",
	styleUrls: ["./dual-multi-select.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [DualMultiSelectServiceService],
})
export class DualMultiSelectComponent implements OnInit {
	@Input() dataConfig: PactoDualMultiSelectConfig;
	public data: Array<any>;
	public resultData: Array<any>;
	public filterData: Array<any>;
	public pendingSelection: PendingSelection;
	public selectedData: Array<any>;
	public unselectedData: Array<any>;
	@Output() sendDataProduto: EventEmitter<any> = new EventEmitter();

	constructor(
		private dataService: DualMultiSelectServiceService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.data = this.dataConfig.data;
		this.filterData = this.filterDataArray();
		this.unselectedData = this.filterData
			.slice()
			.sort(this.sortContactOperator);
		this.selectedData = [];
		this.pendingSelection = Object.create(null);
	}

	public addToSelectedData(data?: any): void {
		const changeData = data
			? [data]
			: this.getPendingSelectionFromCollection(this.unselectedData);
		this.pendingSelection = Object.create(null);

		this.unselectedData = this.removeDataFromCollection(
			this.unselectedData,
			changeData
		);
		this.selectedData = changeData.concat(this.selectedData);
	}

	public removeFromSelectedData(data?: any): void {
		const changeData = data
			? [data]
			: this.getPendingSelectionFromCollection(this.selectedData);
		this.pendingSelection = Object.create(null);

		this.selectedData = this.removeDataFromCollection(
			this.selectedData,
			changeData
		);
		this.unselectedData = changeData
			.concat(this.unselectedData)
			.sort(this.sortContactOperator);
	}

	public togglePendingSelection(data: any): void {
		this.pendingSelection[data.id] = !this.pendingSelection[data.id];
	}

	private getPendingSelectionFromCollection(
		collection: Array<any>
	): Array<any> {
		const selectionFromCollection = collection.filter((data) => {
			return data.id in this.pendingSelection;
		});

		return selectionFromCollection;
	}

	enviar() {
		this.resultData = this.unfilterDataArray();
		this.sendDataProduto.emit(this.resultData);
	}

	private removeDataFromCollection(
		collection: Array<any>,
		dataToRemove: Array<any>
	): Array<any> {
		const collectionWithoutData = collection.filter((data) => {
			return !dataToRemove.includes(data);
		});

		return collectionWithoutData;
	}

	private filterDataArray(): Array<any> {
		// tslint:disable-next-line: prefer-const
		let array = [];
		this.data.map((data) => {
			array.push({
				id: data[this.dataConfig.id],
				value: data[this.dataConfig.value],
			});
		});
		return array;
	}

	private unfilterDataArray(): Array<any> {
		const array = [];
		this.selectedData.map((data) => {
			array.push({
				[this.dataConfig.id]: data.id,
				[this.dataConfig.value]: data.value,
			});
		});
		return array;
	}

	private sortContactOperator(a: any, b: any): number {
		return a.value.localeCompare(b.value);
	}
}
