@import "~src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	font-size: 18px;
}

.dual-select {
	display: flex;
	height: 400px;
	justify-content: center;

	&__left,
	&__right {
		flex: 0 0 auto;
		width: 307px;
	}

	&__controls {
		justify-content: center;
		display: flex;
		flex: 0 0 auto;
		flex-direction: column;
		padding: 0px 10px 0px 10px;
	}

	&__control {
		font-size: 20px;
		height: 40px;
		width: 50px;
		margin: 10px 0px 10px 0px;
		cursor: pointer;
		border: 0px;
		padding: 0px;
		box-shadow: none;
		text-transform: uppercase;
		border-radius: 4px;
		line-height: 32px;
		cursor: pointer;
		position: relative;
		outline: 0;
		@extend .type-btn-bold;
		background-color: $azulimPri;
		border: 1px solid $azulimPri;
		color: $branco;

		.pct {
			color: $branco;
		}

		&:hover {
			font-weight: bold;
		}
	}

	&__items {
		border: 1px solid #cccccc;
		height: 100%;
		list-style-type: none;
		margin: 0px 0px 0px 0px;
		overflow: auto;
		overscroll-behavior: contain;
		padding: 0px 0px 0px 0px;
	}

	&__item {
		border-bottom: 1px solid #cccccc;
		cursor: pointer;
		margin: 0px 0px 0px 0px;
		padding: 0px 0px 0px 0px;
		user-select: none;
		-moz-user-select: none;
		-webkit-user-select: none;

		&:last-child {
			border-bottom-width: 0px;
		}

		&--selected {
			background-color: $azulim01;
		}

		&--new {
			animation-duration: 2s;
			animation-name: dual-select-item-new-fade-in;
			animation-timing-function: ease-out;
		}
	}
}

@keyframes dual-select-item-new-fade-in {
	0% {
		background-color: #ffffff;
	}
	25% {
		background-color: $azulim01;
	}
	100% {
		background-color: #ffffff;
	}
}

.data {
	padding: 8px 10px 8px 10px;

	&__value {
		font-size: 18px;
		//font-weight: bold;
		line-height: 22px;
		overflow: hidden;
		text-overflow: ellipsis;
		//white-space: nowrap;
	}
}

.note {
	font-size: 16px;
}

.area-button {
	display: flex;
	justify-content: center;
	margin-top: 16px;
}

::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

*:hover::-webkit-scrollbar {
	padding: 11px 0 11px 11px;
	width: 11px;
	height: 18px;
}

::-webkit-scrollbar-thumb {
	height: 3px;
	border: 4px solid rgba(0, 0, 0, 0);
	background-clip: padding-box;
	border-radius: 3px;
	box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
		inset 1px 1px 0px rgba(0, 0, 0, 0.05);
	background-color: #6f747b;
}

::-webkit-scrollbar-button {
	width: 0;
	height: 0;
	display: none;
}

::-webkit-scrollbar-corner {
	background-color: transparent;
}
