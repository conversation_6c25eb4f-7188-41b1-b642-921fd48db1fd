export class PactoDualMultiSelectConfig {
	data?: any;
	id: string;
	value: string;

	// endpointUrl?: string;
	constructor(config: PactoDualMultiSelectConfig) {
		this.data = config.data ? config.data : null;
		this.id = config.id;
		this.value = config.value;
		// this.endpointUrl = config.endpointUrl ? config.endpointUrl : null;
	}
}

export interface PactoDualMultiSelectConfig {
	data?: any;
	id: string;
	value: string;
	// endpointUrl?: string;
}
