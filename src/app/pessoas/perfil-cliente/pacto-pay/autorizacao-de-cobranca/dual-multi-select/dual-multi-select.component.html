<pacto-cat-layout-v2>
	<div class="dual-select">
		<div class="dual-select__left">
			<ul class="dual-select__items">
				<li
					(click)="togglePendingSelection(data)"
					(dblclick)="addToSelectedData(data)"
					*ngFor="let data of unselectedData"
					[class.dual-select__item--selected]="pendingSelection[data.id]"
					class="dual-select__item">
					<div class="data">
						<div class="data__value">
							{{ data.value }}
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="dual-select__controls">
			<button (click)="addToSelectedData()" class="dual-select__control">
				<i class="pct pct-chevron-right"></i>
			</button>

			<button (click)="removeFromSelectedData()" class="dual-select__control">
				<i class="pct pct-chevron-left"></i>
			</button>
		</div>
		<div class="dual-select__right">
			<ul class="dual-select__items">
				<li
					(click)="togglePendingSelection(data)"
					(dblclick)="removeFromSelectedData(data)"
					*ngFor="let data of selectedData"
					[class.dual-select__item--selected]="pendingSelection[data.id]"
					class="dual-select__item dual-select__item--new">
					<div class="data">
						<div class="data__value">
							{{ data.value }}
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<div class="area-button">
		<pacto-cat-button
			(click)="enviar()"
			[label]="'Selecionar'"
			[size]="'LARGE'"
			[type]="'PRIMARY_NO_TEXT_TRANSFORM'"></pacto-cat-button>
	</div>
</pacto-cat-layout-v2>
<!--<p class="note">
  You have
  <strong>{{ selectedData.length }} of {{ data.length }}</strong>
  datas selected.
</p>-->
