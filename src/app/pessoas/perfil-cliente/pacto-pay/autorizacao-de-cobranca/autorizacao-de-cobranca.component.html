<div [hidden]="dataCobranca">
	<div class="title-text-empty">Dados de cobrança</div>
	<div class="d-flex flex-column align-items-center mb-3">
		<img class="icon-empty" src="assets/images/empty-state-m-card.svg" />
		<div class="text-empty mt-3 body-text-empty mb-3">
			O aluno ainda não possui nenhuma forma de pagamento cadastrada!
		</div>
		<button
			id="pay-btn-new-forma-cobranca"
			class="btn btn-primary"
			style="color: #fff"
			(click)="verificarConveniosAntesDeAbrir()">
			Adicionar forma de cobrança
		</button>
	</div>
</div>

<div [hidden]="!dataCobranca">
	<pacto-relatorio-cobranca
		#relatorio
		[showShare]="false"
		[table]="relatorioGridConfig"
		[tableTitle]="'Dados de cobrança'"
		[customActionsTop]="cobrancaAutomatica"
		[customActions]="novaFormaDeCobranca"
		[accordion]="accordionEditarAutorizacao"></pacto-relatorio-cobranca>

	<ng-template #tipoAutorizacao let-cobranca="item">
		<span *ngIf="cobranca.tipo === 1 && !cobranca.utilizandoIdVindiPessoa">
			Cartão de crédito
		</span>
		<div
			*ngIf="cobranca.tipo === 1 && cobranca.utilizandoIdVindiPessoa"
			[ds3Tooltip]="tooltipVindi">
			<span>Autorização utilizando IdVindi</span>
			<br />
			<span>IdVindi: {{ dadosPessoais?.idVindi }}</span>
		</div>
		<span *ngIf="cobranca.tipo === 2">Débito em conta</span>
		<span *ngIf="cobranca.tipo === 3">Boleto bancário</span>
	</ng-template>

	<ng-template #tipoDeParcela let-cobranca="item">
		<span *ngIf="cobranca.tipoCobrar === 1">Em Aberto</span>
		<span *ngIf="cobranca.tipoCobrar === 2">Planos</span>
		<span *ngIf="cobranca.tipoCobrar === 3">Produtos Especificados</span>
		<span *ngIf="cobranca.tipoCobrar === 4">Contratos Autorrenováveis</span>
	</ng-template>

	<ng-template #cobrancaAutomatica>
		<div class="cobranca-automatica">
			<!-- <pacto-cat-button (click)="openModalCobrancaAutomatica()" size="LARGE"
            [label]="autorizada?'Bloquear cobrança automática':'Desbloquear cobrança automática'"></pacto-cat-button> -->
			<!-- <div class="t-status t-status-paid" *ngIf="autorizada">
            <span>AUTORIZADA</span>
        </div> -->
			<!-- tooltipClass="my-custom-class" -->
			<!--    <div [ngbTooltip]="tipContent" class="t-status t-status-error" *ngIf="!autorizada">-->
			<!-- <div class="t-status t-status-error" *ngIf="!autorizada">
            <span>BLOQUEADA</span>
        </div> -->
		</div>

		<ng-template #tipContent>
			<!-- <b>Todas</b> as parcelas com vencimento superior ao dia
    <b>{{ aluno.dataBloqueioCobrancaAutomatica.split(' ')[0] }}</b> estão bloqueadas para cobrança automática.<br />
    <b>CLIQUE PARA DESBLOQUEAR</b> -->
		</ng-template>
	</ng-template>

	<ng-template #novaFormaDeCobranca>
		<button
			id="pay-btn-new-forma-cobranca"
			class="btn btn-primary"
			style="color: #fff; margin-bottom: 0.5rem"
			(click)="verificarConveniosAntesDeAbrir()">
			Nova forma de cobrança
		</button>
	</ng-template>

	<ng-template #accordionEditarAutorizacao let-cobranca="item">
		<pacto-forma-de-cobranca
			[cobranca]="cobranca"
			(sendModificacao)="reiniciarRelatorio($event)"
			[idVindi]="dadosPessoais?.idVindi"
			[possuiIdVindi]="dadosPessoais?.possuiIdVindi"
			[pessoa]="aluno?.pessoa"
			[empresa]="dadosPessoais?.empresa?.codigo"
			[editar]="true"></pacto-forma-de-cobranca>
	</ng-template>
</div>
<ng-template #tooltipVindi>
	A transação será enviada utilizando somente o IdVindi do cliente.
	<br />
	A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente
	tiver cadastrado na Vindi.
</ng-template>
