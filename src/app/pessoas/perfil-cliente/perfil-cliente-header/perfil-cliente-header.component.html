<div *ngIf="aberto" class="dados-pessoais">
	<strong
		[routerLink]="['../configuracoes-cliente', matricula]"
		class="title-1">
		DADOS PESSOAIS
		<i class="pct pct-edit"></i>
	</strong>
	<!-- <div>
	{{ dadosPessoais?.codigoCliente}}<br>
<span>Usuario Verificado?</span>{{ usuarioVerificacao?.usuarioVerificacao}}<br>
<span>Usuario Biometria?</span>{{ assinatura?.codigoCliente}}<br>
	<span>Usuario Movel dados:</span>{{ usuarioMovel?.usuarioMovel}}
</div> -->
	<p>
		<strong>Aniversário:</strong>
		<span *ngIf="dadosPessoais?.nascimento">
			{{ dadosPessoais?.nascimento | date : "shortDate" : "UTC" }}
		</span>
		<span *ngIf="!dadosPessoais?.nascimento">-</span>
	</p>
	<p>
		<strong>Idade:</strong>
		{{ dadosPessoais?.idade }} anos
	</p>
	<p class="separador">
		<strong>Sexo:</strong>
		<span
			*ngIf="dadosPessoais?.sexo === 'M'"
			i18n="@@perfil-aluno-dados-pessoal:info:sexo-masc">
			Masculino
		</span>
		<span
			*ngIf="dadosPessoais?.sexo === 'F'"
			i18n="@@perfil-aluno-dados-pessoal:info:sexo-fem">
			Feminino
		</span>
		<span *ngIf="!dadosPessoais?.sexo">-</span>
	</p>
	<p>
		<strong>Código de pessoa:</strong>
		{{ dadosPessoais?.codigoPessoa }}
	</p>
	<p class="separador">
		<strong>Código de cliente:</strong>
		{{ dadosPessoais?.codigoCliente }}
	</p>
	<p><strong>Empresa:</strong></p>
	<p class="separador">{{ dadosPessoais?.empresa?.nome?.toLowerCase() }}</p>
	<p><strong>Contatos:</strong></p>
	<p *ngFor="let telefone of dadosPessoais?.telefones">
		{{ telefone.numero }}
		<i *ngIf="telefone.whatsapp === true" class="pct pct-whatsapp"></i>
	</p>
	<p *ngFor="let email of dadosPessoais?.emails" class="email">
		{{ email }}
		<i class="pct pct-mail"></i>
	</p>
	<!-- <pacto-cat-toggle-input
            label="Liberação da catacra"
            [id]="catacra-habilitada"
            [control]="form.get('habilitado')"
    ></pacto-cat-toggle-input> -->
	<pacto-cat-button
		(click)="verificarDados()"
		*ngIf="false"
		class="btn-verificar-dados"
		i18n-label="@@perfil-cliente:btn-verificar"
		icon="pct pct-check-circle"
		label="Verificar dados"
		style="margin-right: 10px"
		type="OUTLINE_MODIFY"></pacto-cat-button>
</div>
<div *ngIf="aberto" class="operacoes">
	<div class="foto-acoes">
		<div class="icones">
			<i *ngIf="usuarioMovel?.usuarioMovel" class="pct pct-smartphone">
				<span class="tooltip-icon">Usuário móvel cadastrado</span>
			</i>
			<i *ngIf="assinatura?.assinaturaBiometriaFacial" class="pct pct-smile">
				<span class="tooltip-icon">Amostra facial cadastrada</span>
			</i>
			<i *ngIf="assinatura?.assinaturaDigitalBiometria" class="pct pct-digital">
				<span class="tooltip-icon">Amostra digital cadastrada</span>
			</i>
			<i
				*ngIf="usuarioVerificacao?.usuarioVerificacao === 2"
				class="pct pct-check-circle">
				<span class="tooltip-icon">Usuário Verificado</span>
			</i>
		</div>
		<pacto-cat-person-avatar
			[diameter]="200"
			[parqPositivo]="false"
			[uri]="dadosPessoais?.urlFoto"></pacto-cat-person-avatar>
		<div *ngIf="dadosPessoais?.parqPositivo === true" class="acao parq">
			<i>
				<svg
					height="50px"
					version="1.1"
					viewBox="0 0 56 56"
					width="50px"
					xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink">
					<g
						fill="none"
						fill-rule="evenodd"
						id="Dashboard"
						stroke="none"
						stroke-width="1">
						<g id="Group-16">
							<rect
								fill="#A62F1A"
								height="56"
								id="Rectangle"
								rx="28"
								width="56"
								x="0"
								y="0"></rect>
							<g
								fill-rule="nonzero"
								id="Group-18"
								transform="translate(7.000000, 12.000000)">
								<path
									d="M21.0025901,2.4000902 C22.9514628,0.843650202 25.3348678,4.26325641e-13 27.8450527,4.26325641e-13 C30.8260674,4.26325641e-13 33.6285812,1.18991131 35.7364742,3.35038235 C40.0878419,7.81039577 40.0878419,15.0675471 35.7364742,19.5276619 L22.0514371,33.5546068 C21.76188,33.8515018 21.3822318,34 21.0025836,34 C20.6229354,34 20.2433861,33.8515018 19.95373,33.5546068 L6.2687918,19.5276619 C4.16089874,17.3670895 3,14.4945368 3,11.4390221 C3,8.38350749 4.16089874,5.51095476 6.2687918,3.35038235 C8.37658597,1.18991131 11.1791987,4.26325641e-13 14.1601145,4.26325641e-13 C16.6702032,4.26325641e-13 19.0537096,0.843550719 21.0025836,2.39999404 Z"
									fill="#FFFFFF"
									id="Combined-Shape"></path>
								<path
									d="M21,15.9179332 L21.8751905,17.3852252 L24.6367527,11.8292556 C24.8677775,11.3643581 25.3217751,11.0528598 25.8365112,11.0061601 C26.3514462,10.9596603 26.8532592,11.1841591 27.1633136,11.6000568 L29.6982249,15.0000383 L40.5088757,15.0000383 C41.332374,15.0000383 42,15.6716346 42,16.5000301 C42,17.3284255 41.332374,18.0000219 40.5088757,18.0000219 L28.9526627,18.0000219 C28.4832568,18.0000219 28.041387,17.7777231 27.7597633,17.4000251 L26.2250982,15.3416364 L23.3277444,21.1708045 C23.0843929,21.6605019 22.5951053,21.9779001 22.0512426,21.9989 C22.0321562,21.9997 22.012871,22 21.993884,22 C21.6225941,22 21.2700923,21.8603008 21,21.6171021 C20.8901538,21.5182026 20.7936284,21.4027033 20.7154935,21.271704 L16.1485775,13.6148458 L14.3810982,17.1708264 C14.1284024,17.6790236 13.6121751,18.0000219 13.0473373,18.0000219 L1.49112426,18.0000219 C0.667626036,18.0000219 0,17.3284255 0,16.5000301 C0,15.6716346 0.667626036,15.0000383 1.49112426,15.0000383 L12.1257231,15.0000383 L14.6958249,9.82936652 C14.9392757,9.3396692 15.4284639,9.02227093 15.9723266,9.00127105 C16.5158911,8.97867117 17.0282414,9.25886964 17.3081751,9.72836707 L21,15.9179332 Z"
									fill="#A62F1A"
									id="Combined-Shape"></path>
							</g>
						</g>
					</g>
				</svg>
				<span class="tooltip-icon tooltip-parq">Problema cardíaco</span>
			</i>
		</div>
		<div (click)="openTimeLine()" class="acao linha">
			<i class="pct pct-clock">
				<span class="tooltip-icon">Linha do tempo</span>
			</i>
		</div>
		<div
			(click)="openCobrancaAutomatica()"
			[style.backgroundColor]="
				permiteCobrancaAutomatica ? '#2ec750' : '#db2c3d'
			"
			class="acao payment">
			<i class="pct pct-dollar-sign">
				<span class="tooltip-icon">Bloquear cobrança automática</span>
			</i>
		</div>
	</div>
	<span class="nome-aluno">{{ dadosPessoais?.nome?.toLowerCase() }}</span>
	<span class="matricula-situacao">
		<span class="matricula">
			<strong>
				<i class="pct pct-edit"></i>
				Matrícula:
			</strong>
			<span>{{ dadosPessoais?.matricula }}</span>
		</span>
		<pacto-cat-situacao-aluno
			[situacaoAluno]="dadosPessoais?.situacao"
			class="item"></pacto-cat-situacao-aluno>
		<pacto-cat-situacao-contrato
			[situacaoContrato]="dadosPlano?.situacao"
			class="item"></pacto-cat-situacao-contrato>
	</span>

	<pacto-painel-saldo-pontos
		[aberto]="aberto"
		[pontos]="pontos"
		[saldo]="saldo"></pacto-painel-saldo-pontos>
</div>
<div *ngIf="aberto" class="dados-plano">
	<div class="box-dados">
		<strong class="title-1">DADOS DO PLANO</strong>
		<p><strong>Plano atual:</strong></p>
		<p *ngIf="dadosPlano?.nomePlano">{{ dadosPlano?.nomePlano }}</p>
		<p *ngIf="!dadosPlano?.nomePlano">-</p>
		<p>
			<strong>Cadastro:</strong>
			<span *ngIf="dadosPlano?.dataCadastro">
				{{ dadosPlano?.dataCadastro | date : "shortDate" : "UTC" }}
			</span>
			<span *ngIf="!dadosPlano?.dataCadastro">-</span>
		</p>
		<p
			*ngIf="dadosPlano?.dataMatricula"
			[ngClass]="{ separador: !dadosPlano?.dataRematricula }">
			<strong>Matrícula:</strong>
			<span *ngIf="dadosPlano?.dataMatricula">
				{{ dadosPlano?.dataMatricula | date : "shortDate" : "UTC" }}
			</span>
			<span *ngIf="!dadosPlano?.dataMatricula">-</span>
		</p>
		<p *ngIf="dadosPlano?.dataRematricula" class="separador">
			<strong>Rematrícula:</strong>
			<span>
				{{ dadosPlano?.dataRematricula | date : "shortDate" : "UTC" }}
			</span>
		</p>
	</div>
	<div class="box-dados">
		<strong class="title-1">
			VÍNCULOS
			<i class="pct pct-edit"></i>
		</strong>
		<p *ngFor="let vinculo of dadosPlano?.vinculos">
			<strong>
				<ng-container
					*ngTemplateOutlet="
						traducaoItems;
						context: { traducaoItem: vinculo.tipoVinculo }
					"></ng-container>
				:
			</strong>
			{{ abreviarNome(vinculo.colaborador).toLowerCase() }}
		</p>
		<div class="avisos-observacoes">
			<p (click)="abrirAviso()" class="text-avisos">
				<i class="pct pct-alert-triangle"></i>
				Avisos ({{ dadosPlano?.avisos }})
			</p>
			<p (click)="redirectToClient()" class="text-observacoes">
				<i class="pct pct-alert-circle"></i>
				Observações ({{ dadosPlano?.observacoes }})
			</p>
		</div>
	</div>
	<div (click)="fechar()" *ngIf="aberto" class="control">
		<i class="pct pct-chevron-up"></i>
	</div>
	<div (click)="abrir()" *ngIf="!aberto" class="control">
		<i class="pct pct-chevron-down"></i>
	</div>
</div>

<div *ngIf="!aberto" class="fechado">
	<div class="operacoes-simples">
		<div class="foto-acoes">
			<pacto-cat-person-avatar
				[diameter]="88"
				[parqPositivo]="false"
				[uri]="dadosPessoais?.urlFoto"></pacto-cat-person-avatar>
			<div *ngIf="dadosPessoais?.parqPositivo === true" class="acao parq">
				<svg
					fill="none"
					height="17"
					viewBox="0 0 14 12"
					width="21"
					xmlns="http://www.w3.org/2000/svg">
					<path
						clip-rule="evenodd"
						d="M12.9338 6.3142C13.1881 6.01034 13.3855 5.67617 13.526 5.3117C13.7174 4.82966 13.8131 4.34299 13.8131 3.85168C13.8131 3.36037 13.7174 2.87833 13.526 2.40556C13.3437 1.93279 13.0658 1.50637 12.6921 1.1263C12.3275 0.746234 11.9082 0.463499 11.4342 0.2781C10.9694 0.0926999 10.4954 0 10.0124 0C9.5293 0 9.05534 0.0926999 8.59049 0.2781C8.12565 0.463499 7.70638 0.746234 7.33268 1.1263L7.1276 1.33488L6.92253 1.1263C6.54883 0.746234 6.12956 0.463499 5.66471 0.2781C5.19987 0.0926999 4.72591 0 4.24284 0C3.75977 0 3.28125 0.0926999 2.80729 0.2781C2.34245 0.463499 1.92773 0.746234 1.56315 1.1263C1.18945 1.4971 0.906901 1.92352 0.715495 2.40556C0.533203 2.87833 0.442057 3.36037 0.442057 3.85168C0.442057 4.34299 0.533203 4.82966 0.715495 5.3117C0.863054 5.67617 1.06478 6.01034 1.32068 6.3142H0.5775C0.258556 6.3142 0 6.57717 0 6.90155C0 7.22593 0.258555 7.4889 0.5775 7.4889H2.4597L6.71745 11.8192C6.82682 11.9397 6.96354 12 7.1276 12C7.29167 12 7.42839 11.9397 7.53776 11.8192L11.7955 7.4889H13.4225C13.7414 7.4889 14 7.22593 14 6.90155C14 6.57717 13.7414 6.3142 13.4225 6.3142H12.9338ZM12.446 2.85052C12.3184 2.5168 12.127 2.22016 11.8717 1.9606C11.6165 1.70104 11.3249 1.50637 10.9967 1.37659C10.6777 1.24681 10.3496 1.18192 10.0124 1.18192C9.67513 1.18192 9.347 1.24681 9.02799 1.37659C8.70898 1.50637 8.41732 1.70104 8.15299 1.9606L7.53776 2.58633C7.42839 2.70684 7.29167 2.76709 7.1276 2.76709C6.96354 2.76709 6.82682 2.70684 6.71745 2.58633L6.10221 1.9606C5.83789 1.70104 5.54622 1.50637 5.22721 1.37659C4.9082 1.24681 4.58008 1.18192 4.24284 1.18192C3.9056 1.18192 3.57292 1.24681 3.24479 1.37659C2.92578 1.50637 2.63867 1.70104 2.38346 1.9606C2.12825 2.22016 1.93685 2.5168 1.80924 2.85052C1.68164 3.17497 1.61784 3.50869 1.61784 3.85168C1.61784 4.19467 1.68164 4.53302 1.80924 4.86674C1.93685 5.19119 2.12825 5.4832 2.38346 5.74276L2.94533 6.3142H3.8071L4.74583 3.75759C4.82795 3.53394 5.03501 3.3831 5.26963 3.37599C5.50426 3.36887 5.71978 3.50691 5.81485 3.72518L7.18559 6.87227L8.14408 4.97225C8.24489 4.77242 8.44892 4.64855 8.66971 4.65314C8.8905 4.65774 9.08938 4.78999 9.18207 4.99385L9.78237 6.3142H11.3099L11.8717 5.74276C12.127 5.4832 12.3184 5.19119 12.446 4.86674C12.5736 4.53302 12.6374 4.19467 12.6374 3.85168C12.6374 3.50869 12.5736 3.17497 12.446 2.85052ZM10.1549 7.4889H9.41316C9.18805 7.4889 8.98346 7.35587 8.88898 7.14806L8.63093 6.58048L7.66119 8.5028C7.55961 8.70415 7.35332 8.82824 7.1309 8.82179C6.90847 8.81534 6.70948 8.67949 6.61936 8.47257L5.33241 5.51786L4.7489 7.10703C4.66458 7.33666 4.44891 7.4889 4.20789 7.4889H4.10033L7.1276 10.5678L10.1549 7.4889Z"
						fill="#ffffff"
						fill-rule="evenodd" />
				</svg>
			</div>
			<div (click)="openTimeLine()" class="acao linha">
				<i class="pct pct-clock"></i>
			</div>
		</div>
		<div class="dados-pessoais-simples">
			<span class="nome-aluno">{{ dadosPessoais?.nome?.toLowerCase() }}</span>
			<span class="matricula-situacao">
				<span class="matricula">
					<strong>Matrícula:</strong>
					<span>{{ dadosPessoais?.matricula }}</span>
				</span>
				<pacto-cat-situacao-aluno
					[situacaoAluno]="dadosPessoais?.situacao"
					class="item fechado"></pacto-cat-situacao-aluno>
				<pacto-cat-situacao-contrato
					[situacaoContrato]="dadosPlano?.situacao"
					class="item fechado"></pacto-cat-situacao-contrato>
			</span>
		</div>
	</div>
	<div class="saldo-pontos">
		<pacto-painel-saldo-pontos
			[aberto]="aberto"
			[pontos]="pontos"
			[saldo]="saldo"
			class="fechado-acao"></pacto-painel-saldo-pontos>
	</div>
	<div class="dados-pessoais">
		<div class="dados-contato">
			<p><strong>Contatos:</strong></p>
			<p *ngFor="let telefone of dadosPessoais?.telefones" class="telephone">
				{{ telefone.numero }}
				<i *ngIf="telefone.whatsapp === true" class="pct pct-whatsapp"></i>
			</p>
			<p
				*ngFor="let email of dadosPessoais?.emails; let i = index"
				class="email email-{{ i }}">
				{{ email }}
				<i class="pct pct-mail"></i>
			</p>
		</div>
		<div class="avisos-observacoes">
			<p class="text-avisos">
				<i class="pct pct-alert-triangle"></i>
				Avisos ({{ dadosPlano?.avisos }})
			</p>
			<p class="text-observacoes">
				<i class="pct pct-alert-circle"></i>
				Observações ({{ dadosPlano?.observacoes }})
			</p>
		</div>
	</div>
	<div (click)="fechar()" *ngIf="aberto" class="control">
		<i class="pct pct-chevron-up"></i>
	</div>
	<div (click)="abrir()" *ngIf="!aberto" class="control">
		<i class="pct pct-chevron-down"></i>
	</div>
</div>

<ng-template #traducaoItems let-traducaoItem="traducaoItem">
	<ng-container [ngSwitch]="traducaoItem">
		<span *ngSwitchCase="'PR'" i18n="@@perfil-cliente:professor:vinculos">
			Professor
		</span>
		<span
			*ngSwitchCase="'TW'"
			i18n="@@perfil-cliente:professor-treino:vinculos">
			Professor (Treino Web)
		</span>
		<span
			*ngSwitchCase="'PT'"
			i18n="@@perfil-cliente:personal-trainer:vinculos">
			Personal trainer
		</span>
		<span *ngSwitchCase="'OR'" i18n="@@perfil-cliente:orientador:vinculos">
			Orientador
		</span>
		<span *ngSwitchCase="'CO'" i18n="@@perfil-cliente:consultor:vinculos">
			Consultor
		</span>
		<span
			*ngSwitchCase="'PI'"
			i18n="@@perfil-cliente:personal-interno:vinculos">
			Personal interno
		</span>
		<span
			*ngSwitchCase="'PE'"
			i18n="@@perfil-cliente:personal-externo:vinculos">
			Personal externo
		</span>
		<span *ngSwitchCase="'TE'" i18n="@@perfil-cliente:terceirizado:vinculos">
			Terceirizado
		</span>
		<span *ngSwitchCase="'ES'" i18n="@@perfil-cliente:estudio:vinculos">
			Estúdio
		</span>
		<span *ngSwitchCase="'FO'" i18n="@@perfil-cliente:fornecedor:vinculos">
			Fornecedor
		</span>
		<span *ngSwitchCase="'CR'" i18n="@@perfil-cliente:coordenador:vinculos">
			Coordenador
		</span>
		<span *ngSwitchCase="'MD'" i18n="@@perfil-cliente:medico:vinculos">
			Médico
		</span>
		<span *ngSwitchCase="'FC'" i18n="@@perfil-cliente:funcionario:vinculos">
			Funcionário
		</span>
		<span *ngSwitchCase="'AD'" i18n="@@perfil-cliente:administrador:vinculos">
			Administrador
		</span>
	</ng-container>
</ng-template>
