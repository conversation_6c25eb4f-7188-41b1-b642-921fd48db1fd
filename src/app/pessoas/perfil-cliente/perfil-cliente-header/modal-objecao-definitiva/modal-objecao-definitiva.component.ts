import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-objecao-definitiva",
	templateUrl: "./modal-objecao-definitiva.component.html",
	styleUrls: ["./modal-objecao-definitiva.component.scss"],
})
export class ModalObjecaoDefinitivaComponent implements OnInit {
	dadosPessoais;
	@Output() update = new EventEmitter<string>();

	constructor(
		private modal: NgbActiveModal,
		private admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {}

	fecharHandler() {
		this.modal.close();
	}

	removerObjecao() {
		this.admLegadoTelaClienteService
			.alterarObjecaoCliente(
				this.sessionService.chave,
				this.dadosPessoais.codigoCliente,
				this.sessionService.codUsuarioZW,
				0
			)
			.subscribe(
				(response) => {
					if (!response.content) {
						this.snotifyService.error(response.meta.message);
					} else {
						this.snotifyService.success("Objeção removida.");
						this.dadosPessoais.objecao = null;
						this.modal.close(true);
					}
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta) {
						if (error.meta.error) {
							this.snotifyService.error(error.meta.message);
						}
					}
				}
			);
	}
}
