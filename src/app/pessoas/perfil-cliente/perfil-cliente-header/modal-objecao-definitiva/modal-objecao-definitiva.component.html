<div class="geral">
	<div class="row superior">
		<div class="col-6 cliente">
			<div class="titulo">Cliente</div>
			<div class="row dados">
				<div class="col-2 avatar">
					<pacto-cat-person-avatar
						[diameter]="32"
						[parqPositivo]="false"
						[uri]="
							dadosPessoais?.urlFoto ? dadosPessoais.urlFoto : null
						"></pacto-cat-person-avatar>
				</div>
				<div class="col-10 nome">
					{{ dadosPessoais?.nome | captalize }}
				</div>
			</div>
		</div>
		<div class="col-6 objecao">
			<div class="titulo">Objeção</div>
			<div class="dados">
				<span class="nome">
					{{ dadosPessoais?.objecao | captalize : true }}
				</span>
			</div>
		</div>
	</div>
	<div class="col-12 centro">
		<span><i class="pct pct-alert-triangle icon"></i></span>
		<span>
			Após remover a objeção definitiva, o aluno irá voltar a entrar nas metas
			do CRM.
		</span>
	</div>
	<div class="col-12 inferior text-lg-right">
		<pacto-cat-button
			(click)="fecharHandler()"
			id="obj-btn-cancelar"
			label="Cancelar"
			size="NORMAL"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			(click)="removerObjecao()"
			id="obj-btn-remover"
			label="Remover"
			size="NORMAL"
			style="padding-left: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
