<span class="painel-saldo-pontos">
	<div *ngIf="false" class="lado">
		<span class="box1">
			Saldo:
			<span [ngClass]="{ red: saldo < 0.0, green: saldo >= 0.0 }">
				{{ saldoFormatado }}
			</span>
		</span>
		<span class="option">
			<i class="pct pct-sliders"></i>
			Ajustar
		</span>
		<span class="option">
			<i class="pct pct-dollar-sign"></i>
			Receber
		</span>
	</div>
	<div *ngIf="false" class="lado">
		<span class="box1">
			Pontuação:
			<span class="blue">{{ pontos }}</span>
		</span>
		<span class="option">
			<i class="pct pct-sliders"></i>
			Ajustar
		</span>
		<span class="option">
			<i class="pct pct-gift"></i>
			Brindes
		</span>
	</div>
	<div>
		<div (click)="toggleAcao()" *ngIf="acaoAberta" class="actions-layer"></div>
		<span class="acao-principal">
			<div
				*ngIf="acaoAberta"
				[ngClass]="{ aberto: aberto, fechado: !aberto }"
				class="actions-buttons">
				<div *ngIf="false">Novo orçamento</div>
				<div (click)="criarAvaliacao()">Nova avaliação física</div>
				<div (click)="criarProgramaHandler()">Novo programa de treino</div>
				<div (click)="adicionarConvidado()">Convidados</div>
				<div *ngIf="false">Novo contrato</div>
				<div [routerLink]="['../acessos/acesso-manual']">
					Registrar Acesso Manual
				</div>
				<div (click)="openArmario()">Armario</div>
			</div>
			<i
				(click)="toggleAcao()"
				[ngClass]="{ 'pct-minus': acaoAberta, 'pct-plus': !acaoAberta }"
				class="pct"></i>
		</span>
	</div>
</span>
