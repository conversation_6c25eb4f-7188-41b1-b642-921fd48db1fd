import { Component, Input, OnInit } from "@angular/core";
import { TreinoContainerComponent } from "../../treino/treino-container/treino-container.component";
import { UltimaAvaliacaoComponent } from "../../avaliacao/ultima-avaliacao/ultima-avaliacao.component";
import { ActivatedRoute, Router } from "@angular/router";
import { Observable, zip } from "rxjs";
import { tap } from "rxjs/operators";
import { AlunoBase, TreinoApiAlunosService } from "treino-api";
import { PactoModalRef, PactoModalSize, DialogService } from "ui-kit";
import { ModalService } from "@base-core/modal/modal.service";
import { ModalConvidadosComponent } from "../../modal-convidados/modal-convidados.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalArmarioComponent } from "../modal-armario/modal-armario.component";

@Component({
	providers: [TreinoContainerComponent, UltimaAvaliacaoComponent],
	selector: "pacto-painel-saldo-pontos",
	templateUrl: "./painel-saldo-pontos.component.html",
	styleUrls: ["./painel-saldo-pontos.component.scss"],
})
export class PainelSaldoPontosComponent implements OnInit {
	@Input() aberto: boolean;
	acaoAberta = false;
	@Input() pontos = 0;
	@Input() saldo = 0.0;
	matricula: string;
	aluno: AlunoBase;

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private alunoService: TreinoApiAlunosService,
		private treinoComponent: TreinoContainerComponent,
		private ultimaAvaliacaoComponent: UltimaAvaliacaoComponent,
		private pactoModal: ModalService,
		private ngbModal: NgbModal,
		private modalService: DialogService
	) {}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.loadAluno().subscribe();
	}

	private loadAluno(): Observable<any> {
		const $aluno = this.alunoService.obterAlunoCompletoPorMatricula(
			this.matricula
		);
		const dataToLoad = [$aluno];
		return zip(...dataToLoad).pipe(
			tap((result) => {
				this.aluno = result[0];
			})
		);
	}

	toggleAcao() {
		this.acaoAberta = !this.acaoAberta;
		if (!this.acaoAberta) {
			if (this.isAbaTreinoActive()) {
				this.reloadTreino();
			}
		}
	}

	private isAbaTreinoActive() {
		const url = this.route.snapshot["_routerState"].url;
		const aba = "/treinos";
		return url.indexOf(aba, url.length - aba.length) !== -1;
	}

	private reloadTreino() {
		this.router.routeReuseStrategy.shouldReuseRoute = () => {
			return false;
		};
		const currentUrl = this.route.snapshot["_routerState"].url;
		this.router.navigateByUrl(currentUrl).then(() => {
			this.router.navigated = false;
			this.router.navigate([this.router.url]);
		});
	}

	get saldoFormatado(): string {
		const valorFormatado = Number(this.saldo).toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
		return "R$ " + valorFormatado;
	}

	public criarProgramaHandler(): void {
		this.treinoComponent.callCriarPrograma();
	}

	public criarAvaliacao(): void {
		this.ultimaAvaliacaoComponent.criarPrimeiraAvaliacao(
			this.matricula,
			this.aluno
		);
	}

	registrarAcessoManual() {
		this.router.navigate(["acessos", "acesso-manual"]);
	}

	adicionarConvidado() {
		const modal: PactoModalRef = this.pactoModal.open(
			"Adicionar convidado",
			ModalConvidadosComponent,
			PactoModalSize.SMALL,
			"convidado-modal"
		);
		modal.componentInstance.matricula = this.matricula;
	}

	openArmario() {}
}
