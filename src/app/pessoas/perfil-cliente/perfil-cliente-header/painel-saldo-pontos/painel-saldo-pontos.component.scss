@import "~src/assets/scss/pacto/plataforma-import.scss";

.painel-saldo-pontos {
	position: relative;
	text-align: center;
	margin: 0px 10px;
	border: 0px solid $cinza02;
	border-radius: 8px;
	margin-top: 30px;
	height: 75px;
	display: grid;
	grid-template-columns: 1fr 1fr;

	.lado {
		display: grid;
		grid-auto-rows: 25px;

		&:nth-child(1) {
			margin-right: 22%;
			padding-left: 10px;
		}

		&:nth-child(2) {
			margin-left: 22%;
			padding-right: 2px;
		}

		.box1 {
			grid-column-start: 1;
			grid-column-end: 3;
		}

		.option {
			background-color: #fafafa;
			padding: 6px 0;
			line-height: 14px;
			margin-right: 5px;
			width: auto;
			cursor: pointer;

			i {
				margin-right: 5px;
			}
		}

		text-align: center;
		padding: 14px 0;
		font-size: 14px;
		line-height: 16px;
		color: $azulPactoPri;

		.red {
			color: $hellboy05;
		}

		.green {
			color: $chuchuzinho07;
		}

		.blue {
			color: $azulim05;
		}
	}

	overflow: visible;

	.acao-principal {
		position: absolute;
		z-index: 9;
		width: 82px;
		height: 82px;
		left: calc(50% - 41px);
		top: -5px;
		line-height: 98px;
		border-radius: 50%;
		background-color: $azulim05;
		color: $branco;
		display: flex;
		text-align: center;
		align-items: center;
		justify-content: center;
		font-size: 42px;
		cursor: pointer;
		z-index: 999;

		i {
			margin-left: 0;
		}

		.actions-buttons {
			position: absolute;
			left: -66px;

			&.aberto {
				top: -100px;
			}

			&.fechado {
				bottom: -208px;
			}

			div {
				border-radius: 4px;
				cursor: pointer;
				margin-bottom: 8px;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				padding: 12px 0px;
				width: 220px;
				height: 42px;
				background: #fafafa;
				color: $azulPactoPri;
				font-size: 16px;
				font-weight: 700;
				line-height: 18px;
			}
		}
	}
}

:host(.fechado-acao) {
	.acao-principal {
		top: -7px;
	}
}

.actions-layer {
	background-color: rgba(0, 0, 0, 0.5);
	width: 100vw;
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 999;
}

@media screen and (max-width: 1400px) {
	.painel-saldo-pontos {
		.acao-principal {
			left: calc(50% - 37px);
		}

		.lado {
			&:nth-child(2) {
				margin-left: 25%;
				padding-right: 5px;
			}
		}
	}
}
