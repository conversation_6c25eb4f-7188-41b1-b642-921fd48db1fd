@import "~src/assets/scss/pacto/plataforma-import.scss";

::ng-deep.modal-time-line {
	.modal-dialog {
		max-width: 635px;
		width: 100%;
		position: fixed;
		bottom: 0;
		right: 0;
		height: 100%;
		margin: 0px;

		.modal-content {
			height: 100%;
			position: absolute;

			pacto-modal-wrapper {
				max-height: 100%;
				height: 100%;
				position: relative;
				overflow-y: auto;

				.title {
					font-size: 32px;
					margin-top: 35px;
					margin-bottom: 12px;
					margin-left: 38px;
				}

				.close-wrapper {
					position: absolute !important;
					top: 62px !important;
					left: 36px !important;
					font-size: 28px !important;
				}
			}
		}
	}
}

:host {
	position: relative;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
	display: flex;
	width: 100%;

	p {
		margin-bottom: 0;

		&.separador {
			margin-bottom: 10px;
		}
	}

	i {
		margin-left: 8px;
	}
}

.pct-whatsapp {
	color: $chuchuzinhoPri;
}

.pct-mail {
	color: $azulim06;
}

strong {
	font-weight: 700;
	line-height: 22px;
	font-size: 14px;

	&.title-1 {
		i {
			margin-left: 12px;
		}

		display: block;
		margin-bottom: 10px;
		color: $azulPactoPri;
		font-weight: 600;
		font-size: 18px;
		line-height: 24px;
	}
}

.dados-pessoais {
	strong,
	.email {
		text-transform: none;
	}

	text-transform: capitalize;
	line-height: 25px;
	width: 30%;
	color: $pretoPri;
	font-size: 14px;
	font-weight: 400;
	padding: 32px 0px 32px 32px;

	strong {
		i {
			cursor: pointer;
		}
	}
}

.operacoes {
	width: 40%;
	text-align: center;
	padding: 32px 0px;
	display: grid;

	.foto-acoes {
		position: relative;
		margin: 0 auto;
		display: flex;

		.icones {
			position: absolute;
			top: 36px;
			left: -50px;

			i {
				display: block;
				margin-bottom: 14px;
				position: relative;

				.tooltip-icon {
					position: absolute;
					left: 50%;
					bottom: 100%;
					opacity: 0;
					margin-bottom: 1em;
					padding: 5px 20px;
					background-color: #555;
					border-radius: 6px;
					color: #fff;
					font-size: 12px;
					line-height: 1.6;
					text-align: left;
					white-space: nowrap;
					transform: translate(-50%, 1em);
					transition: all 0.15s ease-in-out;
					font-family: "Nunito Sans", sans-serif;
					z-index: 999;
				}

				&:hover {
					.tooltip-icon {
						opacity: 1;
						transform: translate(-50%, 0);

						&:before {
							content: "";
							position: absolute;
							top: 100%;
							left: 50%;
							width: 0;
							height: 0;
							border: 0.5em solid transparent;
							border-top-color: #555;
							transform: translate(-50%, 0);
						}
					}
				}
			}

			.pct-digital {
				font-size: 28px;
			}

			.pct-check-circle {
				color: $chuchuzinhoPri;
			}

			.pct-smile {
				color: $cinzaPri;
			}

			color: $canetaBicPri;
			margin-right: 21px;
			font-size: 25px;
		}

		.acao {
			cursor: pointer;
			position: absolute;
			width: 48px;
			height: 48px;
			text-align: center;
			border-radius: 50%;
			color: $branco;
			font-size: 33px;

			i {
				display: block;
				margin-top: 6px;
				margin-left: 0;
			}

			svg {
				margin-top: -7px;
			}

			&.parq {
				top: 15px;
				right: 0px;
				background-color: $hellboyPri;
			}

			&.linha {
				top: 76px;
				right: -24px;
				background-color: $azulim05;
			}

			&.payment {
				top: 137px;
				right: 0px;
				background-color: $chuchuzinhoPri;
			}
		}
	}

	.nome-aluno {
		font-size: 28px;
		font-weight: 700;
		line-height: 42px;
		letter-spacing: 0px;
		text-align: center;
		padding: 8px 0;
		text-transform: capitalize;
		border-bottom: 1px solid $cinza02;
	}
}

.matricula-situacao {
	display: inline-flex;
	justify-content: center;

	.item {
		display: inline-flex;
		margin-top: 8px;
		margin-right: 10px;
	}

	.fechado {
		width: 32px;
	}

	pacto-cat-situacao-contrato {
		display: inline-block;
	}

	pacto-cat-situacao-aluno {
		display: inline-block;
	}

	.matricula {
		display: inline-block;

		strong {
			font-size: 22px;
		}

		i {
			margin-right: 10px;
			font-size: 16px;
		}

		font-size: 22px;
		color: #51555a;
		margin-top: 10px;
		padding-right: 30px;
	}
}

.dados-plano {
	line-height: 25px;
	width: 28%;
	text-align: right;
	padding: 32px 0;
	color: $pretoPri;
	font-size: 14px;
	font-weight: 400;
	text-transform: capitalize;
}

.fechado {
	display: flex;
	width: 100%;
	grid-template-columns: repeat(auto-fit, 30% 36% 40%);
	//height: 152px;
	text-align: center;

	.matricula {
		width: 160px;
	}

	.control {
		left: calc(50% - 70px);
	}

	.saldo-pontos {
		margin-top: 35px;

		::ng-deep .painel-saldo-pontos {
			margin: 0;
			height: 70px;
		}
	}

	.matricula-situacao {
		.item {
			margin-right: 10px;
		}
	}

	.operacoes-simples {
		width: 30%;
	}

	.saldo-pontos {
		width: 33%;
	}

	.nome-aluno {
		padding-bottom: 0;
	}

	.dados-pessoais {
		strong,
		.email {
			text-transform: none;
		}

		display: flex;
		text-transform: capitalize;
		line-height: 25px;
		width: 37%;
		padding: 32px 20px 15px 22px;
		text-align: left;

		.avisos-observacoes {
			margin-top: 0;
			font-size: 12px;

			.text-avisos {
				margin-right: 10px;
			}

			i {
				margin-left: 0px;
			}

			p {
				display: inline-block;
				margin-right: 0;
				background-color: #fafafa;
			}
		}

		.dados-contato {
			width: 90%;
		}
	}

	.telephone {
		display: none;

		&:nth-of-type(2) {
			display: inline-block;
		}
	}

	.email-1,
	.email-2 {
		display: none;
	}
}

.control {
	background-color: $branco;
	color: $azulPactoPri;
	border-radius: 5px;
	box-shadow: 0px 2px 2px 0px #e4e5e6;
	font-weight: 600;
	height: 24px;
	width: 33px;
	line-height: 33px;
	font-size: 20px;
	text-align: center;
	position: absolute;
	bottom: -12px;
	left: calc(50% - 18px);

	i {
		margin-left: 0;
		display: block;
	}

	cursor: pointer;
}

.operacoes-simples {
	padding: 32px 10px;
	display: flex;

	.foto-acoes {
		position: relative;
		width: 88px;
		height: 88px;

		.acao {
			position: absolute;
			cursor: pointer;
			width: 32px;
			height: 32px;
			text-align: center;
			border-radius: 50%;
			color: $branco;
			font-size: 20px;

			i {
				display: block;
				margin-top: 5px;
				margin-left: 0;
			}

			svg {
				margin-top: 9px;
			}

			&.parq {
				top: 0px;
				right: -16px;
				background-color: $hellboyPri;
			}

			&.linha {
				bottom: 0px;
				right: -16px;
				background-color: $azulim05;
			}
		}
	}

	.dados-pessoais-simples {
		text-transform: capitalize;

		strong {
			text-transform: none;
		}

		display: block;
		text-align: left;
		margin-left: 30px;
		width: calc(89% - 88px);

		.nome-aluno {
			font-size: 24px;
			font-weight: 600;
			padding-bottom: 10px;
			border-bottom: 1px solid $cinza02;
			display: block;
		}
	}

	.matricula-situacao {
		justify-content: stretch;

		.matricula {
			strong {
				font-size: 16px;
			}

			font-size: 14px;
		}
	}
}

i {
	.tooltip-icon {
		position: absolute;
		left: 50%;
		bottom: 100%;
		opacity: 0;
		margin-bottom: 1em;
		padding: 5px 20px;
		background-color: #555;
		border-radius: 6px;
		color: #fff;
		font-size: 12px;
		line-height: 1.6;
		text-align: left;
		white-space: nowrap;
		transform: translate(-50%, 1em);
		transition: all 0.15s ease-in-out;
		font-family: "Nunito Sans", sans-serif;
		z-index: 999;
	}

	.tooltip-parq {
		left: 90%;
		font-style: normal;
	}

	&:hover {
		.tooltip-icon {
			opacity: 1;
			transform: translate(-50%, 0);

			&:before {
				content: "";
				position: absolute;
				top: 100%;
				left: 50%;
				width: 0;
				height: 0;
				border: 0.5em solid transparent;
				border-top-color: #555;
				transform: translate(-50%, 0);
			}
		}
	}
}

.avisos-observacoes {
	font-size: 13px;
	margin-top: 20px;

	.text-avisos {
		color: #db2c3d;
		margin-bottom: 10px;
	}

	.text-avisos {
		margin-right: 10px;
		cursor: pointer;
	}

	.text-observacoes {
		color: #1b4166;
	}

	p {
		display: inline;
		font-weight: 600;
		background-color: #fafafa;
		padding: 5px 12px;

		.pct {
			padding-right: 5px;
		}
	}
}

.box-dados {
	margin-bottom: 25px;
}

@media (max-width: 1400px) {
	.fechado {
		zoom: 0.9;
		display: flex;

		.control {
			left: calc(50% + 2px);
		}

		.avisos-observacoes {
			margin-top: 10px;
		}

		.operacoes-simples {
			padding: 25px 10px;
			width: 33%;

			.nome-aluno {
				font-size: 19px;
				padding-bottom: 5px;
			}
		}

		.matricula-situacao {
			.matricula {
				width: 160px;
			}
		}

		.saldo-pontos {
			width: 38%;
		}

		.dados-pessoais {
			display: block;
			padding: 20px;
			width: 30%;
		}

		.dados-pessoais-simples {
			width: calc(89% - 78px);
		}
	}
	.matricula-situacao {
		.matricula {
			width: 260px;
		}
	}
}

pacto-cat-button {
	margin-top: 10px;
	display: block;
}
