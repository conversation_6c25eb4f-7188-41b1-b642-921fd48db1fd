import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TreinoApiAlunosService } from "treino-api";

@Component({
	selector: "pacto-modal-aviso-aluno",
	templateUrl: "./modal-aviso-aluno.component.html",
	styleUrls: ["./modal-aviso-aluno.component.scss"],
})
export class ModalAvisoAlunoComponent implements OnInit {
	public readonly aviso = new FormControl("");

	private matricula;

	@Output()
	private readonly update = new EventEmitter<void>();

	constructor(
		private readonly modal: NgbActiveModal,
		private readonly alunoService: TreinoApiAlunosService,
		private readonly snotifyService: SnotifyService
	) {}

	public ngOnInit(): void {}

	public fecharHandler(): void {
		this.modal.close();
	}

	public deleteAviso(): void {
		this.alunoService
			.deleteClienteMensagemMatricula(this.matricula, "AM")
			.subscribe(() => {
				this.snotifyService.success("Aviso removido!");
				this.update.emit();
				this.modal.close("delete");
			});
	}

	public salvarAviso(): void {
		if (!this.aviso.value || this.aviso.value.trim() === "") {
			this.deleteAviso();
		} else {
			this.alunoService
				.cadastrarAvisoAluno(this.matricula, "AM", this.aviso.value)
				.subscribe(() => {
					this.snotifyService.success("Aviso registrado com sucesso!");
					this.update.emit();
					this.modal.close("insert");
				});
		}
	}

	get isFormValid(): boolean {
		return !!this.aviso.value;
	}
}
