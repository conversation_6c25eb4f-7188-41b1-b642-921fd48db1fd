<div class="content">
	<div class="content">
		<div *ngFor="let item of carteirinhaData; let i = index">
			<div class="d-flex" *ngIf="i === 0">
				<div class="col-3">
					<p class="head">Código</p>
				</div>
				<div class="col-3">
					<p class="head">Dt. Lançamento</p>
				</div>
				<div class="col-3">
					<p class="head">Dt. Impressão</p>
				</div>
				<div class="col-3">
					<p class="head">Dt. Validade</p>
				</div>
				<div class="col-3">
					<p class="head">Via</p>
				</div>
			</div>

			<div class="d-flex">
				<div class="col-3">
					<p class="text">{{ item.codigo }}</p>
				</div>
				<div class="col-3">
					<p class="text">{{ formatarData(item.dataLancamento) }}</p>
				</div>
				<div class="col-3">
					<p class="text">{{ formatarData(item.dataImpressao) }}</p>
				</div>
				<div class="col-3">
					<p class="text">{{ formatarData(item.dataValidadeCarteirinha) }}</p>
				</div>
				<div class="col-3">
					<p class="text">{{ item.via }}</p>
				</div>
			</div>
		</div>
		<div *ngIf="!carteirinhaData?.length">
			<p>Não há carteirinhas registradas.</p>
		</div>
	</div>
</div>
