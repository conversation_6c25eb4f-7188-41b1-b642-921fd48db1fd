import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
} from "@angular/core";

@Component({
	selector: "pacto-modal-armario",
	templateUrl: "./modal-historico-carteira.component.html",
	styleUrls: ["./modal-historico-carteira.component.scss"],
})
export class ModalHistoricoCarteiraComponent implements OnInit, OnChanges {
	@Input() carteirinhaData: any[]; // Recebe o histórico como input

	constructor(public cd: ChangeDetectorRef) {}

	ngOnInit(): void {
		this.cd.detectChanges();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes.carteirinhaData && changes.carteirinhaData.currentValue) {
			console.log("Dados da carteirinha atualizados:", this.carteirinhaData);
			this.cd.detectChanges();
		}
	}

	formatarData(data: string): string {
		if (data === "sem registro" || data === "" || data === null) {
			return "-";
		}
		const dataObj = new Date(data);

		const dia = String(dataObj.getDate()).padStart(2, "0");
		const mes = String(dataObj.getMonth() + 1).padStart(2, "0");
		const ano = dataObj.getFullYear();

		return `${dia}/${mes}/${ano}`;
	}
}
