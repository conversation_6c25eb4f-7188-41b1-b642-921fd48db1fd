import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import {
	ModalService,
	PactoModalRef,
	PactoModalSize,
} from "@base-core/modal/modal.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { AlunoPactoPay, MsPactoPayApiCobrancaService } from "ms-pactopay-api";
import { TimeLineComponent } from "../../../base/alunos/perfil-aluno/components/time-line/time-line.component";
import { ModalCobrancaAutomaticaComponent } from "../pacto-pay/autorizacao-de-cobranca/modal-cobranca-automatica/modal-cobranca-automatica.component";
import {
	AdmCoreApiClienteService,
	Biometria,
	ClienteDadosPessoais,
	ClienteDadosPlano,
	Importacao,
	UsuarioMovel,
} from "adm-core-api";
import { FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { AdmMsApiAlunosFavoritosService } from "adm-ms-api";
import { ModalAvisoAlunoComponent } from "./modal-aviso-aluno/modal-aviso-aluno.component";
import { TreinoApiAlunosService } from "treino-api";
import { TreinoBiStateService } from "../../../treino/treino-bi/components/treino-bi-home-v2/treino-bi-state.service";
import { switchMap } from "rxjs/operators";
import { Observable, throwError } from "rxjs";
import { DialogService } from "ui-kit";
import { UnderConstructionComponent } from "pacto-layout";

@Component({
	selector: "pacto-perfil-cliente-header",
	templateUrl: "./perfil-cliente-header.component.html",
	styleUrls: ["./perfil-cliente-header.component.scss"],
})
export class PerfilClienteHeaderComponent implements OnInit {
	private alunoPayment: AlunoPactoPay;
	public permiteCobrancaAutomatica = true;
	matricula: string;
	codigoCliente: string;
	pontos = 0;
	saldo = 0.0;
	aberto = true;
	dadosPessoais: ClienteDadosPessoais;
	usuarioMovel: UsuarioMovel;
	usuarioVerificacao: Importacao;
	assinatura: Biometria;

	dadosPlano: ClienteDadosPlano;
	form: FormGroup = new FormGroup({
		liberacaoCatacra: new FormControl(true),
	});

	constructor(
		private route: ActivatedRoute,
		private pactoModal: ModalService,
		private localStorageService: LocalStorageSessionService,
		private msPactopayApiCobranca: MsPactoPayApiCobrancaService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private cd: ChangeDetectorRef,
		private alunoService: TreinoApiAlunosService,
		private notificationService: SnotifyService,
		private alunoFavoritoRecenteService: AdmMsApiAlunosFavoritosService,
		private router: Router,
		private state: TreinoBiStateService,
		private dialogService: DialogService
	) {}

	ngOnInit() {
		// ajuste para recarregar o componente sempre que houver alteração no parametro na url
		this.route.paramMap
			.pipe(
				switchMap((params: ParamMap) => {
					return this.verificarAlteracoesParam(params);
				})
			)
			.subscribe(() => {});
	}

	verificarAlteracoesParam(params: ParamMap): Observable<any> {
		this.loadInit();
		return new Observable<any>();
	}

	loadInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.marcarAlunoRecente();
		if (this.localStorageService.getLocalStorageItem("infoAlunoAberto")) {
			this.aberto =
				this.localStorageService.getLocalStorageItem("infoAlunoAberto") ===
				"true";
		}
		if (this.localStorageService.getLocalStorageItem("infoAlunoAberto")) {
			this.aberto =
				this.localStorageService.getLocalStorageItem("infoAlunoAberto") ===
				"true";
		}

		this.msPactopayApiCobranca
			.obterInformacoesPessoa(this.matricula)
			.subscribe((aluno) => {
				this.permiteCobrancaAutomatica =
					aluno.data_bloqueio_cobranca_automatica === "";
				this.alunoPayment = aluno;
				this.cd.detectChanges();
			});
		this.loadData();
	}

	fechar() {
		this.aberto = false;
		this.localStorageService.setLocalStorageItem("infoAlunoAberto", false);
	}

	abrir() {
		this.aberto = true;
		this.localStorageService.setLocalStorageItem("infoAlunoAberto", true);
	}

	openTimeLine() {
		const modal = this.pactoModal.open(
			"Linha do tempo",
			TimeLineComponent,
			PactoModalSize.SMALL,
			"modal-time-line"
		);
		const timeLine: TimeLineComponent = modal.componentInstance;
		timeLine.matriculaAluno = this.matricula;
	}

	public openCobrancaAutomatica(): void {
		const modal: PactoModalRef = this.pactoModal.open(
			this.permiteCobrancaAutomatica
				? "Bloquear cobrança automática"
				: "Autorizar cobrança automática",
			ModalCobrancaAutomaticaComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.autorizada = this.permiteCobrancaAutomatica;
		modal.componentInstance.pessoa = this.alunoPayment.pessoa;
		modal.componentInstance.cobrancaAutomatica.subscribe((res) => {
			this.permiteCobrancaAutomatica = res.status !== "bloqueada";
			this.cd.detectChanges();
		});
	}

	loadData() {
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe((aluno) => {
			this.dadosPessoais = aluno;
			sessionStorage.setItem(
				"codPessoa",
				JSON.stringify(this.dadosPessoais.codigoPessoa)
			);
			sessionStorage.setItem(
				"codCliente",
				JSON.stringify(this.dadosPessoais.codigoCliente)
			);
			console.log("this.dadosPessoais", this.dadosPessoais);
			this.cd.detectChanges();

			this.msAdmCoreService
				.usuarioMovelDados(this.dadosPessoais.codigoPessoa)
				.subscribe(
					(usuarioMovel) => {
						this.usuarioMovel = usuarioMovel;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.usuarioMovel = undefined;
					}
				);
			this.msAdmCoreService
				.usuarioVerificado(this.dadosPessoais.codigoPessoa)
				.subscribe(
					(usuarioVerificacao) => {
						this.usuarioVerificacao = usuarioVerificacao;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.usuarioVerificacao = undefined;
					}
				);
			this.msAdmCoreService
				.usuarioBiometria(this.dadosPessoais.codigoPessoa)
				.subscribe(
					(assinatura) => {
						this.assinatura = assinatura;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						this.assinatura = undefined;
					}
				);
		});
		this.msAdmCoreService.dadosPlano(this.matricula).subscribe((plano) => {
			this.dadosPlano = plano;
			this.cd.detectChanges();
		});
		this.msAdmCoreService.totalPontos(this.matricula).subscribe((pontos) => {
			this.pontos = pontos;
			this.cd.detectChanges();
		});
		this.msAdmCoreService
			.saldoContaCorrente(this.matricula)
			.subscribe((saldo) => {
				this.saldo = saldo;
				this.cd.detectChanges();
			});
	}

	abreviarNome(nome): string {
		try {
			return nome.split(" ").slice(0, 2).join(" ");
		} catch (e) {
			return nome;
		}
	}

	marcarAlunoRecente() {
		this.alunoFavoritoRecenteService
			.marcarCliente(Number(this.matricula), "RE")
			.subscribe((response) => {
				console.log(response);
			});
	}

	abrirAviso() {
		this.alunoService
			.obterClienteMensagemMatricula(this.matricula, "AM")
			.subscribe((result) => {
				const modal: PactoModalRef = this.pactoModal.open(
					"Aviso médico",
					ModalAvisoAlunoComponent,
					PactoModalSize.MEDIUM
				);
				const aviso = result.toString().replace(/&nbsp;/g, " ");
				modal.componentInstance.matricula = this.matricula;
				modal.componentInstance.aviso.setValue(aviso);
				modal.componentInstance.countChars(aviso);
				modal.result.then(
					(resultModal) => {
						this.state.update$.next(true);
						this.dadosPlano.avisos = resultModal === "delete" ? 0 : 1;
						this.cd.detectChanges();
					},
					() => {}
				);
			});
	}

	redirectToClient() {
		const dialogRef = this.dialogService.open(
			"",
			UnderConstructionComponent,
			undefined,
			"under-construction-modal"
		);
		dialogRef.componentInstance.asDialog = true;
		dialogRef.result
			.then((result) => {
				this.router.navigate(["/redirectADM"], {
					queryParams: {
						redirectUri: "uriCliente",
						matriculaCliente: this.matricula,
						openInNewTab: true,
					},
				});
			})
			.catch((err) => throwError(err));
	}
}
