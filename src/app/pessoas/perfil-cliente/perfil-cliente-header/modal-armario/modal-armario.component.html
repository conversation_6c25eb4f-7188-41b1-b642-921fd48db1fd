<div class="content">
	<div class="content">
		<div>
			<pacto-relatorio
				#tableData
				[showShare]="false"
				[table]="table"
				idSuffix="ma-tbl-armario"
				tableTitle="Armários alugados"></pacto-relatorio>
		</div>
		<div *ngIf="detailContratoData">
			<div class="col-12">
				<p class="section-head">Histórico de contrato</p>
			</div>
			<div class="d-flex">
				<div class="col-3">
					<p class="head">Armário:</p>
					<p class="text">{{ detailContratoData?.armario }}</p>
				</div>
				<div class="col-3">
					<p class="head">Aluguel do armário:</p>
					<p class="text">{{ detailContratoData?.aluguelDoArmario }}</p>
				</div>
				<div class="col-3">
					<p class="head">Responsável pelo registro:</p>
					<p class="text">{{ detailContratoData?.responsavelPeloRegistro }}</p>
				</div>
				<div class="col-3">
					<p class="head">Data do registro:</p>
					<p class="text">
						{{
							detailContratoData?.dataDoRegistro | date : "dd/MM/yyyy HH:mm:ss"
						}}
					</p>
				</div>
			</div>
		</div>
	</div>

	<ng-template #columnArmario>
		<span class="columnTitle">Armário</span>
	</ng-template>
	<ng-template #columnRegistroDaLocacao>
		<span class="columnTitle">Dt. Lançamento</span>
	</ng-template>
	<ng-template #columnInicioDaLocacao>
		<span class="columnTitle">Início locação</span>
	</ng-template>
	<ng-template #columnVencimento>
		<span class="columnTitle">Vencimento locação</span>
	</ng-template>
	<ng-template #columnChaveDevolvida>
		<span class="columnTitle">Chave devolvida</span>
	</ng-template>
	<ng-template #columnStatus>
		<span class="columnTitle">Status</span>
	</ng-template>
	<ng-template #columnContrato>
		<span class="columnTitle">Contrato</span>
	</ng-template>

	<ng-template #cellStatus let-index="index" let-status="item">
		<i
			[ngClass]="
				status
					? 'pct pct-circle cor-azulim-pri'
					: 'pct pct-check-circle cor-azulim-pri'
			"></i>
	</ng-template>
	<ng-template #cellChaveDevolvida let-index="index" let-item="item">
		<i
			(click)="alterarChaveDevolvida(item)"
			*ngIf="!item?.chaveDevolvida"
			[ngClass]="'pct pct-circle cor-azulim-pri'"></i>
		<i
			*ngIf="item?.chaveDevolvida"
			[ngClass]="'pct pct-check-circle cor-azulim-pri'"></i>
	</ng-template>
	<ng-template #cellContratoAssinado let-index="index" let-item="item">
		<i
			(click)="alterarContratoAssinado(item)"
			[ngClass]="
				item?.contratoAssinado
					? 'pct pct-check-circle cor-azulim-pri'
					: 'pct pct-circle cor-azulim-pri'
			"></i>
	</ng-template>
	<ng-template #cellAcoes let-index="index" let-item="item">
		<a
			(click)="detailContrato(item)"
			[darkTheme]="true"
			[pactoCatTolltip]="'Ver detalhes'">
			<i class="pct pct-search cor-azulim-pri"></i>
		</a>
	</ng-template>
</div>
