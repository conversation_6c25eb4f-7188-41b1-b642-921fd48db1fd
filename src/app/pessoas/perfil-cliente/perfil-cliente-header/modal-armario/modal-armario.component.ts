import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { AdmRestService } from "projects/adm/src/app/adm-rest.service";
import moment from "moment";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-armario",
	templateUrl: "./modal-armario.component.html",
	styleUrls: ["./modal-armario.component.scss"],
})
export class ModalArmarioComponent implements OnInit {
	constructor(
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private admRest: AdmRestService
	) {}

	ngOnInit() {
		this.loadData();
	}

	@Input() alunoData: any;
	@Input() configuracoesSistemaData: any;
	detailContratoData;
	habilitadoGestaoArmarios: boolean = false;

	table: PactoDataGridConfig;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("columnArmario", { static: true }) columnArmario: TemplateRef<any>;
	@ViewChild("columnRegistroDaLocacao", { static: true })
	columnRegistroDaLocacao: TemplateRef<any>;
	@ViewChild("columnInicioDaLocacao", { static: true })
	columnInicioDaLocacao: TemplateRef<any>;
	@ViewChild("columnVencimento", { static: true })
	columnVencimento: TemplateRef<any>;
	@ViewChild("columnStatus", { static: true }) columnStatus: TemplateRef<any>;
	@ViewChild("columnContrato", { static: true })
	columnContrato: TemplateRef<any>;
	@ViewChild("cellContrato", { static: true }) cellContrato;
	@ViewChild("cellStatus", { static: true }) cellStatus;
	@ViewChild("cellAcoes", { static: true }) cellAcoes;
	@ViewChild("columnChaveDevolvida", { static: true })
	columnChaveDevolvida: TemplateRef<any>;
	@ViewChild("cellChaveDevolvida", { static: true }) cellChaveDevolvida;
	@ViewChild("columnContratoAssinado", { static: true })
	columnContratoAssinado: TemplateRef<any>;
	@ViewChild("cellContratoAssinado", { static: true }) cellContratoAssinado;

	loadData() {
		this.habilitadoGestaoArmarios =
			this.configuracoesSistemaData &&
			this.configuracoesSistemaData.content.habilitargestaoarmarios
				? this.configuracoesSistemaData.content.habilitargestaoarmarios
				: false;
		this.initTable();
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrlAdmCore(
					`aluguel-armario/by-pessoa/${this.alunoData.codigoPessoa}`
				),
				quickSearch: false,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: false,
				columns: [
					{
						nome: "armario",
						titulo: this.columnArmario,
						visible: true,
						ordenavel: true,
						valueTransform: (v: any) => v.descricao,
					},
					{
						nome: "dataCadastro",
						titulo: this.columnRegistroDaLocacao,
						visible: true,
						ordenavel: true,
						inputType: "date",
						valueTransform: (v: any) => moment(v).format("DD/MM/YYYY"),
					},
					{
						nome: "dataInicio",
						titulo: this.columnInicioDaLocacao,
						visible: true,
						ordenavel: true,
						inputType: "date",
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					},
					{
						nome: "fimOriginal",
						titulo: this.columnVencimento,
						visible: true,
						ordenavel: true,
						inputType: "date",
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					},
					{
						nome: "chaveDevolvida",
						titulo: this.columnChaveDevolvida,
						visible: true,
						ordenavel: true,
						celula: this.cellChaveDevolvida,
					},
					{
						nome: "contratoAssinado",
						titulo: "Contrato Assinado",
						visible: this.habilitadoGestaoArmarios,
						ordenavel: true,
						celula: this.cellContratoAssinado,
					},
					{
						nome: "acoes",
						titulo: "",
						visible: true,
						ordenavel: false,
						celula: this.cellAcoes,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	detailContrato(row) {
		this.detailContratoData = {
			armario: row.armario.codigo,
			aluguelDoArmario: row.armario.aluguelAtual,
			responsavelPeloRegistro: row.responsavelCadastro.nome,
			dataDoRegistro: row.dataCadastro,
		};
	}

	iconClickFn(event: { row: any; iconName: string }) {
		this.detailContrato(event.row);
	}

	alterarChaveDevolvida(row) {
		this.limparPactoCatTolltip();
		const codAluguelArmario = row.codigo;
		const chaveDevolvida = !row.chaveDevolvida;

		this.admLegadoTelaClienteService
			.alterarChaveDevolvidaArmario(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				codAluguelArmario,
				chaveDevolvida
			)
			.subscribe(
				(resp) => {
					this.notificationService.success("Chave devolvida alterada");
					this.tableData.reloadData();
					this.cd.detectChanges();
					setTimeout(() => {
						this.limparPactoCatTolltip();
					}, 500);
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notificationService.error(err.meta.message);
					}
				}
			);
	}

	alterarContratoAssinado(row) {
		this.limparPactoCatTolltip();
		const codAluguelArmario = row.codigo;
		const contratoAssinado = !row.contratoAssinado;

		this.admLegadoTelaClienteService
			.alterarContratoAssinadoArmario(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				codAluguelArmario,
				contratoAssinado
			)
			.subscribe(
				(resp) => {
					this.notificationService.success("Contrato assinado alterado");
					this.tableData.reloadData();
					this.cd.detectChanges();
					setTimeout(() => {
						this.limparPactoCatTolltip();
					}, 500);
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notificationService.error(err.meta.message);
					}
				}
			);
	}

	limparPactoCatTolltip() {
		try {
			const elementsByClassName =
				document.getElementsByClassName("pacto-cat-tolltip");
			const array = Array.from(elementsByClassName);
			for (const element of array) {
				document.getElementById(element.id).style.visibility = "hidden";
			}
		} catch (e) {
			console.log(e);
		}
	}
}
