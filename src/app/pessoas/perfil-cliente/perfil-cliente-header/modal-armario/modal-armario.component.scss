@import "~src/assets/scss/pacto/plataforma-import.scss";

.content {
	//margin: 26px 32px 32px 32px;

	.section-head,
	.head {
		@extend .type-p-small;
		font-size: 14px;
		font-weight: 600;
		line-height: normal;
	}

	.text {
		@extend .type-p-small;

		i {
			font-size: 16px;
		}
	}
}

#detalhe-modal-content {
	display: none;
}

#icon-detalhe-hover:hover > #detalhe-modal-content {
	position: absolute;
	display: flex;
	z-index: 2;
}

.cancelbutton {
	padding: 0 16px 0 0;
}

.add-method {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	flex-direction: row;

	i {
		padding-left: 13px;
		color: $azulimPri;

		&:hover {
			cursor: pointer;
		}
	}
}

.selectjustificativa {
	width: 444px;
}

.pill-status {
	width: 75px;
	height: 24px;
	border-radius: 100px;
	text-transform: capitalize;
	background: $cinza02;

	&.ativo {
		background: $azulim01;
		padding: 2px 20px;
		color: $azulim03;
	}

	&.inativo {
		background: $cinza02;
		padding: 2px 16px;
		color: $cinza05;
	}
}
