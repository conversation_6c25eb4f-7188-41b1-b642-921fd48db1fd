import {
	ChangeDetector<PERSON>ef,
	Component,
	OnInit,
	OnDestroy,
	ViewChild,
	Input,
	Output,
	EventEmitter,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { CrmApiGenericoService } from "crm-api";
import { DataService } from "../notify/data.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";
import { SnotifyService } from "ng-snotify";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ModalScriptComponent } from "../modals/modal-script/modal-script.component";
import { ModalObjecaoComponent } from "../modals/modal-objecao/modal-objecao.component";
import { ModalAgendarAulaExperimentalComponent } from "../modals/modal-agendar-aula-experimental/modal-agendar-aula-experimental.component";
import { ModalAgendarVisitaComponent } from "../modals/modal-agendar-visita/modal-agendar-visita.component";
import { ModalAgendarLigacaoComponent } from "../modals/modal-agendar-ligacao/modal-agendar-ligacao.component";
import { ClienteDadosPessoais } from "adm-core-api";
import { ModalCrmAgendarComponent } from "../modals/modal-crm-agendar/modal-crm-agendar.component";
import { Contato } from "../../../../../../projects/crm-api/src/lib/contato/contato.models";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-crm-whatsapp",
	templateUrl: "./crm-whatsapp.component.html",
	styleUrls: ["./crm-whatsapp.component.scss"],
})
export class CrmWhatsappComponent implements OnInit {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Output() reloadHist: EventEmitter<any> = new EventEmitter<any>();
	@Input() traducao: TraducoesXinglingComponent;
	@ViewChild("traducoesCrmWhatsapp", { static: true })
	traducoesCrmWhatsapp: TraducoesXinglingComponent;
	matricula: string;
	celulares = [];

	celMask = [
		"(",
		/[0-9]/,
		/[0-9]/,
		")",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
	];

	form: FormGroup = new FormGroup({
		pesquisa: new FormControl(),
		telefoneCelular: new FormControl("", [
			Validators.required,
			Validators.pattern(/^\(\d{2}\)\d{9}$/gm),
		]),
		observacao: new FormControl("", [Validators.required]),
	});
	contato: Contato = {};

	constructor(
		private crmService: CrmApiGenericoService,
		private dataService: DataService,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private notify: SnotifyService,
		private readonly sessionService: SessionService,
		private rest: RestService,
		private modalService: DialogService
	) {}

	get _rest() {
		return this.rest;
	}

	limparForm() {
		this.form = new FormGroup({
			pesquisa: new FormControl(""),
			observacao: new FormControl("", [Validators.required]),
		});
	}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.getFones();
		this.cd.detectChanges();
		this.form.get("pesquisa").valueChanges.subscribe((v) => {
			console.log(v);
			this.form
				.get("observacao")
				.setValue(`${v.textoinicio}\n    \n${v.textofim}`);
			this.cd.detectChanges();
		});
		this.cd.detectChanges();
	}

	getFones() {
		this.crmService.getFoneCel(this.matricula).subscribe((v) => {
			if (v) {
				v.forEach((tel) => {
					this.celulares.push(tel);
				});
				this.form.get("telefoneCelular").setValue(v[0].numero);
				this.cd.detectChanges();
			}
		});
		this.cd.detectChanges();
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	openScript() {
		const modal = this.modalService.open(
			"Script",
			ModalScriptComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.selectedContract = this.form.get("pesquisa");
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.contato = this.getContato();
		modal.componentInstance.traducao = this.traducao;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "reloadHist") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
			}
		});
	}

	openModalObjecao() {
		const modal = this.modalService.open(
			"Objeção",
			ModalObjecaoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.contato = this.getContato();
		modal.componentInstance.traducao = this.traducao;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "reloadHist") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
			}
		});
	}

	selecionouPesquisa() {
		const pesquisa = this.form.get("pesquisa").value;
		if (pesquisa && pesquisa.codigo && pesquisa.codigo > 0) {
			this.crmService
				.geraLinkPesquisa(
					pesquisa.codigo,
					this.dadosPessoais.codigoCliente,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe(
					(ret) => {
						const obs = this.form.get("observacao").value;
						this.form.get("observacao").setValue(obs + ret[0].url);
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
		}
	}

	getContato() {
		this.contato.dia = new Date().getTime().toString();
		this.contato.cliente = this.dadosPessoais.codigoCliente;
		this.contato.responsavelcadastro = this.sessionService.codigoUsuarioZw;
		this.contato.tipocontato = "TE";
		this.contato.fase = "";
		this.contato.contatoavulso = true;

		// Ag.Ligação: 22/01/2022 09:00
		// Ag.Visita: 23/08/2021 09:00
		// Ag.Aula Experimental: 11/05/2024 12:00
		// Simples Registro
		// Objeção: MUDOU DE BAIRRO, CIDADE OU PAIS
		this.contato.resultado = "Simples Registro";
		this.contato.observacao = this.form.get("observacao").value;
		return this.contato;
	}

	simplesRegistro() {
		this.crmService.saveSimplesContato(this.getContato()).subscribe(
			(response) => {
				this.reloadHist.emit();
				this.limparForm();
				this.notify.success(
					this.traducao.getLabel("contato-salvo-com-sucesso")
				);
			},
			(httpErrorResponse) => {
				if (
					httpErrorResponse.error &&
					httpErrorResponse.error.meta &&
					httpErrorResponse.error.meta.message
				) {
					this.notify.error(httpErrorResponse.error.meta.message);
				} else {
					this.notify.error(
						this.traducao.getLabel("falha-ao-tentar-salvar-contato")
					);
				}
			}
		);
	}

	openModalAgendar(tipoAgendamento: number) {
		// tipoAgendamento
		// 0 = Ligacao
		// 1 = Aula Experimental
		// 2 = Visita

		let titulo = "";
		if (tipoAgendamento === 0) {
			titulo = "Agendar ligação";
		} else if (tipoAgendamento === 1) {
			titulo = "Agendar aula experimental";
		} else if (tipoAgendamento === 2) {
			titulo = "Agendar visita";
		}
		const modal = this.modalService.open(
			titulo,
			ModalCrmAgendarComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.tipoAgendamento = tipoAgendamento;
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.componentInstance.contato = this.getContato();
		modal.componentInstance.traducao = this.traducao;
		modal.componentInstance.response.subscribe((res) => {
			if (res === "reloadHist") {
				this.reloadHist.emit();
				this.limparForm();
				this.cd.detectChanges();
			}
		});
	}
}
