<div class="row">
	<div class="col-12 div-explicacao-item-crm">
		<i class="pct pct-info div-explicacao-item-crm-icon"></i>
		<span class="div-explicacao-item-crm-text">
			Essa aba é destinada ao envio de contatos para este aluno através de SMS.
		</span>
	</div>
	<div class="col-12">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('pesquisa')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/questionario/tipoquestionario')
			"
			[paramBuilder]="selectBuilder"
			idKey="codigo"
			label="Selecionar pesquisa"
			labelKey="nomeinterno"></pacto-cat-form-select-filter>
	</div>
	<div class="col-12">
		<pacto-cat-form-select
			[control]="form.get('telefoneCelular')"
			[items]="getTelefonesCelular()"
			[label]="'Celular'"
			idKey="id"
			labelKey="label"></pacto-cat-form-select>
	</div>
	<div class="col-12">
		<pacto-cat-select-filter
			[addEmptyOption]="true"
			[control]="form.get('modeloMensagem')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/modeloMensagem/modeloSms')
			"
			[idKey]="'codigo'"
			[labelKey]="'titulo'"
			[label]="'Modelo de mensagem'"
			[paramBuilder]="selectBuilder"
			[placeholder]="'Selecione o modelo de mensagem'"
			[resposeParser]="responseParser"></pacto-cat-select-filter>
	</div>
	<div class="col-12">
		<pacto-cat-form-textarea
			[control]="form.get('observacao')"
			[label]="'Mensagem'"
			[placeholder]="'Digite aqui a mensagem'"
			[rows]="'5'"></pacto-cat-form-textarea>
	</div>

	<div class="col-12 actions">
		<button (click)="limpar()" ds3-outlined-button>Limpar</button>
		<button (click)="enviar()" ds3-flat-button>Enviar SMS</button>
	</div>
</div>
