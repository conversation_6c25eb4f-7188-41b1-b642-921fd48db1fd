import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { CrmApiGenericoService } from "crm-api";
import { DataService } from "../notify/data.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";
import { RestService } from "@base-core/rest/rest.service";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { TipoPerguntaEnum } from "../classes/TipoPerguntaEnum";
import { SnotifyService } from "ng-snotify";
import { debounceTime } from "rxjs/operators";
import { ModalScriptComponent } from "../modals/modal-script/modal-script.component";
import { ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { Contato } from "../../../../../../projects/crm-api/src/lib/contato/contato.models";

@Component({
	selector: "pacto-crm-app",
	templateUrl: "./crm-app.component.html",
	styleUrls: ["./crm-app.component.scss"],
})
export class CrmAppComponent implements OnInit {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Output() reloadHist: EventEmitter<any> = new EventEmitter<any>();
	@Input() traducao: TraducoesXinglingComponent;
	@ViewChild("traducoesCrmApp", { static: true })
	traducoesCrmApp: TraducoesXinglingComponent;
	form: FormGroup;
	tipoMensagem: string;
	contato: Contato = {};
	urlLink: string;
	adicionarResposta3 = false;
	tamanhoMaximo = 255;
	countCaracters = new FormControl();
	mensagemAuxiliar: string;

	constructor(
		private cd: ChangeDetectorRef,
		private dataService: DataService,
		private route: ActivatedRoute,
		private notify: SnotifyService,
		private rest: RestService,
		private modalService: DialogService,
		private crmService: CrmApiGenericoService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService
	) {}

	ngOnInit() {
		this.createForm();
		this.form.get("pesquisa").valueChanges.subscribe((value) => {
			if (!value) {
				return;
			}
			this.selecionouPesquisa();
		});
	}

	// notifierSubscription: Subscription = this.dataService.subjectNotifier.subscribe(notified => {
	// 	this.ngOnInit();
	// });

	get _rest() {
		return this.rest;
	}

	modeloMensagemSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	selecionouPesquisa() {
		const pesquisa = this.form.get("pesquisa").value;
		if (pesquisa && pesquisa.codigo && pesquisa.codigo > 0) {
			this.crmService
				.geraLinkPesquisa(
					pesquisa.codigo,
					this.dadosPessoais.codigoCliente,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe(
					(ret) => {
						const obs = this.form.get("observacao").value;
						this.form.get("observacao").setValue(obs + ret[0].url);
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
		}
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	createForm() {
		this.form = new FormGroup({
			pesquisa: new FormControl(),
			tipoMensagem: new FormControl(
				{ codigo: 2, descricao: "Mensagem Simples" },
				[Validators.required]
			),
			titulo: new FormControl("", [Validators.required]),
			observacao: new FormControl("", [
				Validators.required,
				Validators.maxLength(255),
			]),
			resposta1: new FormControl("Sim", [Validators.required]),
			resposta2: new FormControl("Não", [Validators.required]),
			resposta3: new FormControl(""),
		});
		this.form.get("tipoMensagem").valueChanges.subscribe((value) => {
			if (value && value.codigo === TipoPerguntaEnum.OBJETIVA) {
				this.adicionarResposta3 = false;
				this.form.get("resposta1").setValue("Sim");
				this.form.get("resposta2").setValue("Não");
				this.form.get("resposta3").setValue("");
				this.cd.detectChanges();
			}
		});
		this.countCaracters.setValue(this.tamanhoMaximo);
		this.countCaracters.disable();
		this.form
			.get("observacao")
			.valueChanges.pipe(debounceTime(100))
			.subscribe((value) => {
				if (value !== this.mensagemAuxiliar) {
					if (value.length <= this.tamanhoMaximo) {
						this.countCaracters.setValue(this.tamanhoMaximo - value.length);
					} else {
						this.countCaracters.setValue(0);
						value = value.substring(0, this.tamanhoMaximo);
					}
					this.mensagemAuxiliar = value;
					this.cd.detectChanges();
				}
			});
	}

	adicionarRemoverResposta3() {
		this.adicionarResposta3 = !this.adicionarResposta3;
		if (!this.adicionarResposta3) {
			this.form.get("resposta3").setValue("");
		} else {
			this.form.get("resposta3").setValue(null);
		}
	}

	getTitleAdicionarRemoverResposta3(): string {
		return !this.adicionarResposta3
			? "Adicionar resposta extra"
			: "Remover resposta extra";
	}

	getIconAdicionarRemoverResposta3(): string {
		return !this.adicionarResposta3 ? "pct-plus-circle" : "pct-minus-circle";
	}

	receberLink(urlLink) {
		if (urlLink.length <= this.tamanhoMaximo) {
			this.form.patchValue({ mensagem: urlLink });
		} else {
			this.notify.error(
				this.traducoesCrmApp.getLabel("tamanho-maximo-atingido-link")
			);
		}
	}

	isDisertativa(): boolean {
		return (
			this.form.get("tipoMensagem").value.codigo ===
			TipoPerguntaEnum.DISSERTATIVA
		);
	}

	isObjetiva(): boolean {
		return (
			this.form.get("tipoMensagem").value.codigo === TipoPerguntaEnum.OBJETIVA
		);
	}

	isSimples(): boolean {
		return (
			this.form.get("tipoMensagem").value.codigo === TipoPerguntaEnum.SIMPLES
		);
	}

	getLabelTextArea(): string {
		return this.isDisertativa() ? "Pergunta" : "Mensagem";
	}

	getPlaceHolderTextArea(): string {
		return this.isDisertativa()
			? "Digite aqui sua pergunta"
			: "Digite aqui sua mensagem";
	}

	checkScript() {
		let modalCheck = this.modalService.open(
			"Script",
			ModalScriptComponent,
			PactoModalSize.LARGE
		);
		modalCheck.componentInstance.selectedContract = this.form.get("pesquisa");
	}

	limpar() {
		this.createForm();
	}

	getContato() {
		this.contato.fase = "";
		this.contato.contatoavulso = true;
		this.contato.dia = new Date().getTime().toString();
		this.contato.cliente = this.dadosPessoais.codigoCliente;
		this.contato.responsavelcadastro = this.sessionService.codigoUsuarioZw;
		this.contato.tipocontato = "AP";
		this.contato.titulo = this.form.get("titulo").value;
		this.contato.observacao = this.form.get("observacao").value;
		this.contato.tipoMensagemApp = this.form.get("tipoMensagem").value.codigo;
		this.contato.resposta1 = this.form.get("resposta1").value;
		this.contato.resposta2 = this.form.get("resposta2").value;
		if (this.adicionarResposta3) {
			this.contato.resposta3 = this.form.get("resposta3").value;
		}
		return this.contato;
	}

	validarDados() {
		if (!this.form.get("titulo").valid) {
			this.notify.error("Informe um título");
			return true;
		}
		if (!this.form.get("observacao").valid) {
			this.notify.error(
				"Não é possível enviar uma notificação com mais de 255 caracteres"
			);
			return true;
		}
		return false;
	}

	enviar() {
		if (this.validarDados()) {
			return;
		}
		this.admLegadoTelaClienteService
			.salvarHistoricoContato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente,
				this.getContato()
			)
			.subscribe(
				(resp) => {
					this.reloadHist.emit();
					this.notify.success("Notificação enviada");
					this.limpar();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notify.error(err.meta.message);
					}
				}
			);
	}

	getTipoMensagemApp() {
		const tipos = [];
		tipos.push({ codigo: undefined, label: "-" });
		tipos.push({ codigo: 0, label: "Pergunta Objetiva" });
		tipos.push({ codigo: 1, label: "Pergunta Dissertativa" });
		tipos.push({ codigo: 2, label: "Mensagem Simples" });
		return tipos;
	}
}
