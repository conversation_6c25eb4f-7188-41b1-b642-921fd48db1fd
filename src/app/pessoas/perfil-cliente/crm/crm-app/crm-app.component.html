<div class="row">
	<div class="col-12 div-explicacao-item-crm">
		<i class="pct pct-info div-explicacao-item-crm-icon"></i>
		<span class="div-explicacao-item-crm-text">
			Essa aba é destinada ao envio de contatos para este aluno através do
			aplicativo. Você pode enviar uma mensagem simples ou formular uma pergunta
			para promover a interação.
		</span>
	</div>
	<div class="col-12">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('pesquisa')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/questionario/tipoquestionario')
			"
			[paramBuilder]="selectBuilder"
			idKey="codigo"
			label="Selecionar pesquisa"
			labelKey="nomeinterno"></pacto-cat-form-select-filter>
	</div>
	<!--    <div class="col-2 apply-button">-->
	<!--        <button-->
	<!--             ds3-outlined-button-->
	<!--             (click)="checkScript()"-->
	<!--        ><i class="pct pct-file-text"></i>-->
	<!--            Visualizar script de contato</button>-->
	<!--    </div>-->
	<div class="col-12 div-tipoContato-app">
		<pacto-cat-select-filter
			[control]="form.get('tipoMensagem')"
			[idKey]="'codigo'"
			[labelKey]="'label'"
			[label]="'Tipo de contato'"
			[options]="getTipoMensagemApp()"></pacto-cat-select-filter>
	</div>
	<div *ngIf="isObjetiva()" class="col-12">
		<div class="col-6">
			<pacto-cat-form-input
				[control]="form.get('resposta1')"
				[id]="'resposta1'"
				[label]="'Resposta 01'"
				[maxlength]="'32'"></pacto-cat-form-input>
		</div>
		<div class="col-6">
			<pacto-cat-form-input
				[control]="form.get('resposta2')"
				[id]="'resposta2'"
				[label]="'Resposta 02'"
				[maxlength]="'32'"></pacto-cat-form-input>
		</div>
		<div class="col-6">
			<pacto-cat-form-input
				*ngIf="adicionarResposta3"
				[control]="form.get('resposta3')"
				[id]="'resposta3'"
				[label]="'Resposta 03'"
				[maxlength]="'32'"></pacto-cat-form-input>
			<button
				(click)="adicionarRemoverResposta3()"
				[darkTheme]="true"
				[pactoCatTolltip]="getTitleAdicionarRemoverResposta3()"
				class="iconAddResp3">
				<i class="pct {{ getIconAdicionarRemoverResposta3() }}"></i>
			</button>
		</div>
	</div>

	<div class="col-12">
		<pacto-cat-form-input
			[control]="form.get('titulo')"
			[id]="'titulo'"
			[label]="'Título'"
			[maxlength]="'32'"
			[placeholder]="'Digite um título'"></pacto-cat-form-input>
	</div>
	<div class="col-12">
		<pacto-cat-form-textarea
			[control]="form.get('observacao')"
			[label]="'Descrição de contato'"
			[placeholder]="'Digite aqui a descrição'"
			[rows]="'5'"></pacto-cat-form-textarea>
		<div
			class="character-counter"
			style="text-align: right; font-size: 0.9em; color: #666">
			{{ form.get("observacao").value?.length || 0 }} / 255 caracteres
		</div>
		<div class="col-12 actions">
			<div class="col-12 actions">
				<button (click)="limpar()" ds3-outlined-button>Limpar</button>
				<button (click)="enviar()" ds3-flat-button>Enviar no App</button>
			</div>
		</div>
	</div>
</div>
