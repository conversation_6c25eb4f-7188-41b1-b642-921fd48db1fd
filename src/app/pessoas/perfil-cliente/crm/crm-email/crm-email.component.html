<div class="row">
	<div class="col-12 div-explicacao-item-crm">
		<i class="pct pct-info div-explicacao-item-crm-icon"></i>
		<span class="div-explicacao-item-crm-text">
			Essa aba é dedicada ao envio rápido de e-mails para esse cliente. Você
			pode selecionar um modelo já pronto ou criar um.
		</span>
	</div>
	<div class="col-12">
		<pacto-cat-form-select
			[control]="form.get('email')"
			[items]="getEmails()"
			idKey="id"
			label="Para:"
			labelKey="label"></pacto-cat-form-select>
	</div>
	<div class="col-12">
		<pacto-cat-form-input
			[control]="form.get('titulo')"
			label="Titulo"></pacto-cat-form-input>
	</div>
	<div class="col-12">
		<pacto-cat-select-filter
			[control]="form.get('modeloMensagem')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/modeloMensagem/modeloEmail')
			"
			[idKey]="'codigo'"
			[id]="'codigo'"
			[labelKey]="'titulo'"
			[label]="'Modelo de mensagem'"
			[paramBuilder]="selectBuilder"
			[placeholder]="'Selecione o modelo de mensagem'"
			[resposeParser]="responseParser"></pacto-cat-select-filter>
	</div>
	<div class="col-12">
		<pacto-cat-editor
			[control]="form.get('observacao')"
			[id]="'observacao'"
			[placeHolder]="'Digite aqui sua mensagem'"
			[style]="{ height: '800px' }"></pacto-cat-editor>
	</div>
	<div class="col-12 tags">
		<div (click)="copyTag(tag)" *ngFor="let tag of tags" class="tag">
			{{ tag }}
			<i class="pct pct-copy cor-action-default-able04"></i>
		</div>
	</div>
	<div class="col-12 actions">
		<button (click)="limpar()" ds3-outlined-button>Limpar</button>
		<button (click)="enviar()" ds3-flat-button>Enviar email</button>
	</div>
</div>
