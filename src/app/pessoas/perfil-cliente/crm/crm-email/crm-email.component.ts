import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { CrmApiGenericoService } from "crm-api";
import { DataService } from "../notify/data.service";
import { ActivatedRoute } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	DialogService,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ToastrService } from "ngx-toastr";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { Subscription } from "rxjs";
import { ClienteDadosPessoais } from "adm-core-api";
import { Contato } from "../../../../../../projects/crm-api/src/lib/contato/contato.models";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-crm-email",
	templateUrl: "./crm-email.component.html",
	styleUrls: ["./crm-email.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CrmEmailComponent implements OnInit, OnDestroy {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Output() reloadHist: EventEmitter<any> = new EventEmitter<any>();
	@Input() traducao: TraducoesXinglingComponent;
	@ViewChild("action", { static: true }) action;
	contato: Contato = {};
	tags = [
		"ENDERECO_TAG",
		"TELEFONE_TAG",
		"NOME_EMPRESA",
		"CIDADE_ESTADO_TAG",
		"WEB_SITE_TAG",
		"TAG_NOME ",
		"TAG_PNOME",
		"TAG_PESQUISA",
		"TAG_PAGONLINE",
	];
	form: FormGroup = new FormGroup({
		email: new FormControl(""),
		titulo: new FormControl("", [Validators.required]),
		observacao: new FormControl("", [Validators.required]),
		modeloMensagem: new FormControl(),
	});
	notifierSubscription: Subscription =
		this.dataService.subjectNotifier.subscribe((notified) => {
			this.setMessate();
		});

	constructor(
		private cd: ChangeDetectorRef,
		private dataService: DataService,
		private route: ActivatedRoute,
		private notify: SnotifyService,
		private rest: RestService,
		private modalService: DialogService,
		private crmService: CrmApiGenericoService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService
	) {}

	get _rest() {
		return this.rest;
	}

	setMessate() {
		this.ngOnInit();
	}

	ngOnInit() {
		this.form.get("modeloMensagem").valueChanges.subscribe((v) => {
			if (!v) {
				return;
			}
			this.form.get("observacao").setValue(v.mensagem);
		});
	}

	copyTag(v) {
		if (window.navigator.clipboard) {
			window.navigator.clipboard
				.writeText(v)
				.then(() => {
					this.notify.success(`Tag ${v} foi copiada`);
				})
				.catch(() => {
					this.notify.error("Falha ao copiar tag");
				});
		}
	}

	ngOnDestroy() {
		this.notifierSubscription.unsubscribe();
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		if (!response) {
			return [];
		}
		return response.content;
	};

	limpar() {
		this.form.get("email").setValue("");
		this.form.get("titulo").setValue("");
		this.form.get("observacao").setValue("");
		this.form.get("modeloMensagem").setValue(null);
	}

	getContato() {
		this.contato.fase = "";
		this.contato.contatoavulso = true;
		this.contato.dia = new Date().getTime().toString();
		this.contato.cliente = this.dadosPessoais.codigoCliente;
		this.contato.responsavelcadastro = this.sessionService.codigoUsuarioZw;
		this.contato.tipocontato = "EM";
		this.contato.titulo = this.form.get("titulo").value;
		this.contato.observacao = this.form.get("observacao").value;
		this.contato.email = this.form.get("email").value;
		return this.contato;
	}

	validarDados() {
		if (this.form.get("email").value.length <= 0) {
			this.notify.error("Selecione um email");
			return true;
		}
		if (!this.form.get("titulo").valid) {
			this.notify.error("Informe um título");
			return true;
		}
		if (!this.form.get("observacao").valid) {
			this.notify.error("Informe uma descrição");
			return true;
		}
		return false;
	}

	enviar() {
		if (this.validarDados()) {
			return;
		}
		this.admLegadoTelaClienteService
			.salvarHistoricoContato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente,
				this.getContato()
			)
			.subscribe(
				(resp) => {
					this.reloadHist.emit();
					this.notify.success("Agendado com sucesso");
					this.limpar();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notify.error(err.meta.message);
					}
				}
			);
	}

	getEmails() {
		const emails = [];
		emails.push({ id: "", label: "-" });
		for (let i = 0; this.dadosPessoais.emails.length > i; i++) {
			const email = this.dadosPessoais.emails[i];
			emails.push({ id: email, label: email });
		}
		return emails;
	}
}
