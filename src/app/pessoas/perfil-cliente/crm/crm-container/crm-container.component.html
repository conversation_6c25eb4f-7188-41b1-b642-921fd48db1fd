<div class="div-contato" *ngIf="permissaoAbaCrmRealizarContato">
	<pacto-cat-card-plain>
		<div class="tabs">
			<ds3-tabs
				[tabs]="[
					'Ligação',
					'Whatsapp',
					'Contato Pessoal',
					'App',
					'E-mail',
					'Sms'
				]"
				(selectedTabEvent)="selectedTab = $event"></ds3-tabs>
			<ng-container [ngSwitch]="selectedTab">
				<div class="tab-ligacao" *ngSwitchCase="0">
					<pacto-crm-telefone
						[dadosPessoais]="dadosPessoais"
						[traducao]="traducao"
						[tipoContato]="'TE'"
						(reloadHist)="reloadHist()"></pacto-crm-telefone>
				</div>
				<div class="tab-whatsapp" *ngSwitchCase="1">
					<pacto-crm-telefone
						[dadosPessoais]="dadosPessoais"
						[traducao]="traducao"
						[tipoContato]="'WA'"
						(reloadHist)="reloadHist()"></pacto-crm-telefone>
				</div>
				<div class="tab-contatoPessoal" *ngSwitchCase="2">
					<pacto-crm-telefone
						[dadosPessoais]="dadosPessoais"
						[traducao]="traducao"
						[tipoContato]="'PE'"
						(reloadHist)="reloadHist()"></pacto-crm-telefone>
				</div>
				<div class="tab-app" *ngSwitchCase="3">
					<pacto-crm-app
						*ngIf="isAppAllowed; else notAppAllowed"
						[dadosPessoais]="dadosPessoais"
						[traducao]="traducao"
						(reloadHist)="reloadHist()"></pacto-crm-app>
					<ng-template #notAppAllowed>
						<div class="div-nao-tem-recurso">
							<p>Cliente não utiliza App.</p>
						</div>
					</ng-template>
				</div>
				<div class="tab-eMail" *ngSwitchCase="4">
					<pacto-crm-email
						[dadosPessoais]="dadosPessoais"
						[traducao]="traducao"
						(reloadHist)="reloadHist()"></pacto-crm-email>
				</div>
				<div class="tab-sms" *ngSwitchCase="5">
					<pacto-crm-sms
						*ngIf="isSmsAllowed; else notSmsAllowed"
						[dadosPessoais]="dadosPessoais"
						[traducao]="traducao"
						(reloadHist)="reloadHist()"></pacto-crm-sms>
					<ng-template #notSmsAllowed>
						<div class="div-nao-tem-recurso">
							<p>Sua empresa não possui o pacote de SMS.</p>
						</div>
					</ng-template>
				</div>
			</ng-container>
		</div>
	</pacto-cat-card-plain>
</div>
<pacto-cat-card-plain>
	<div class="row">
		<div class="col-12">
			<div class="d-flex flex-column">
				<div class="title-text-hist-contato">Histórico de contato</div>
				<div class="slider-container d-flex justify-content-end">
					<span>Expandir todo conteúdo</span>
					<label class="switch">
						<input
							type="checkbox"
							[(ngModel)]="expandirTudo"
							(change)="toggleExpandirTudo()"
							[ngClass]="{ checked: expandirTudo }" />
						<span class="slider round"></span>
					</label>
				</div>
			</div>
		</div>

		<div class="div-empty" *ngIf="historico && historico.length == 0">
			<img class="icon-empty" src="assets/images/empty-state-comments.svg" />
			<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
				O aluno ainda não possui nenhum histórico de contato!
			</div>
		</div>

		<div class="historico" *ngIf="historico && historico.length > 0">
			<ng-container *ngFor="let entry of historico; let index = index">
				<div *ngIf="entry.isTheFirstOfTheMonth" class="isTheFirstOfTheMonth">
					{{ entry?.dia | date : "MMMM y" }}
				</div>
				<div class="row entry">
					<div class="col-3">
						<p class="entry-label">Fase</p>
						<span class="entry-body">
							{{
								entry?.faseDescricao + " - " + entry?.tipoContatoCRM?.descricao
							}}
						</span>
					</div>
					<div class="col-3">
						<p class="entry-label">Resultado</p>
						<span class="entry-body">{{ entry?.resultado }}</span>
					</div>
					<div class="col-3">
						<p class="entry-label">Data e hora</p>
						<span class="entry-body">
							{{ entry?.dia | date : "dd/MM/yyyy - HH:mm:ss" }}
						</span>
					</div>
					<div class="col-2">
						<p class="entry-label">Usuário</p>
						<span class="entry-body">
							{{ entry?.responsavelcadastroByUsuario?.nome }}
						</span>
					</div>
					<div class="col-1 actions">
						<button ds3-icon-button (click)="toggleItem(index)">
							<i
								class="pct"
								[ngClass]="{
									'pct-arrow-up': openItems.has(index),
									'pct-arrow-down': !openItems.has(index)
								}"></i>
						</button>
					</div>
					<div
						class="col-12 cursor-default"
						[hidden]="!openItems.has(index)"
						[ngSwitch]="entry.tipoContatoCRM.sigla"
						(click)="$event.stopPropagation()">
						<span
							*ngSwitchDefault
							class="script"
							[innerHTML]="entry?.observacao"></span>

						<div
							(click)="$event.stopPropagation()"
							*ngSwitchCase="'WA'"
							class="historico-chat cursor-default">
							<span
								*ngIf="
									!entry?.observacaoApresentarWA ||
									entry?.observacaoApresentarWA?.length == 0
								"
								class="script"
								[innerHTML]="entry?.observacao"></span>

							<div
								(click)="$event.stopPropagation()"
								class="message-container cursor-default"
								*ngFor="
									let final of entry?.observacaoApresentarWA;
									let index = index
								">
								<div
									(click)="$event.stopPropagation()"
									*ngIf="final?.sender === 'IA'"
									class="ia-message-format">
									<span
										(click)="$event.stopPropagation()"
										class="message ia-message message-text cursor-default"
										[innerHTML]="final?.content"></span>
								</div>

								<div
									(click)="$event.stopPropagation()"
									*ngIf="final?.sender === 'USER'"
									class="user-message-format">
									<span
										(click)="$event.stopPropagation()"
										class="message user-message message-text cursor-default"
										[innerHTML]="final?.content"></span>
								</div>

								<div
									(click)="$event.stopPropagation()"
									*ngIf="final?.sender === 'SISTEMA'"
									class="sistem-message-format">
									<span
										(click)="$event.stopPropagation()"
										class="message sistem-message message-text cursor-default"
										[innerHTML]="final?.content"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</ng-container>
		</div>

		<div class="actions" *ngIf="historico && historico.length > 0">
			<button ds3-text-button (click)="carregarMais()">Carregar mais</button>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducao>
	<span
		xingling="telefone-celular-obrigatorio"
		i18n="@@tela-cliente-crm:telefone-celular-obrigatorio">
		Nenhum telefone celular selecionado
	</span>
	<span
		xingling="email-obrigatorio"
		i18n="@@tela-cliente-crm:email-obrigatorio">
		O campo "Para" deve ser informado
	</span>
	<span
		xingling="titulo-obrigatorio"
		i18n="@@tela-cliente-crm:titulo-obrigatorio">
		O campo "Assunto" deve ser informado
	</span>
	<span
		xingling="menssagem-obrigatoria"
		i18n="@@tela-cliente-crm:menssagem-obrigatoria">
		O campo "Messagem" deve ser informado
	</span>

	<span
		xingling="contato-salvo-com-sucesso"
		i18n="@@tela-cliente-crm:contato-salvo-com-sucesso">
		Contato salvo com sucesso
	</span>
	<span
		xingling="falha-ao-tentar-salvar-contato"
		i18n="@@tela-cliente-crm:falha-ao-tentar-salvar-contato">
		Falha ao tentar salvar contato
	</span>

	<span
		xingling="objecao-salva-com-sucesso"
		i18n="@@tela-cliente-crm:objecao-salva-com-sucesso">
		Objeção salva com sucesso
	</span>
	<span
		xingling="falha-ao-tentar-salvar-objecao"
		i18n="@@tela-cliente-crm:falha-ao-tentar-salvar-objecao">
		Falha ao tentar salvar objeção
	</span>
	<span
		xingling="tipo-de-objecao-nao-informado"
		i18n="@@tela-cliente-crm:tipo-de-objecao-nao-informado">
		O campo "Tipo de objeção" deve ser informado
	</span>

	<span
		xingling="email-agendado-com-sucesso"
		i18n="@@tela-cliente-crm:email-agendado-com-sucesso">
		E-mail agendado com sucesso
	</span>
	<span
		xingling="falha-ao-agendar-email"
		i18n="@@tela-cliente-crm:falha-ao-agendar-email">
		Falha ao tentar agendar e-mail.
	</span>
	<span
		xingling="cliente-nao-possui-email-cadastrado"
		i18n="@@tela-cliente-crm:cliente-nao-possui-email-cadastrado">
		Cliente não possui e-mail cadastrado.
	</span>

	<span
		xingling="sms-enviado-com-sucesso"
		i18n="@@tela-cliente-crm:sms-enviado-com-sucesso">
		Enviado com sucesso
	</span>
	<span
		xingling="falha-ao-tentar-enviar-sms"
		i18n="@@tela-cliente-crm:falha-ao-tentar-enviar-sms">
		Falha ao tentar enviar SMS
	</span>
</pacto-traducoes-xingling>
