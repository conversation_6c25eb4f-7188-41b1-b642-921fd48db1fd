import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { CrmApiGenericoService } from "crm-api";
import { SnotifyService } from "ng-snotify";
import {
	CatTabsTransparentComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import { AlunoAppInfo } from "../../../../../../projects/treino-api/src/lib/aluno.model";
import { TreinoApiAlunosService } from "treino-api";
import { PermissaoService } from "pacto-layout";

@Component({
	selector: "pacto-crm-container",
	templateUrl: "./crm-container.component.html",
	styleUrls: ["./crm-container.component.scss"],
})
export class CrmContainerComponent implements OnInit {
	matricula: string;
	selectedTab = 0;
	formGroup = new FormGroup({
		control: new FormControl(),
	});
	page = 1;
	size = 15;
	showIndex = null;
	historico = [];
	dadosPessoais: ClienteDadosPessoais;
	alunoAppInfo: AlunoAppInfo;
	possuiSmsHabilitado = false;
	permissaoAbaCrmRealizarContato: boolean = false;
	@ViewChild("tabs", { static: true }) tabs: CatTabsTransparentComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	expandirTudo: boolean = false;
	openItems: Set<number> = new Set();

	constructor(
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		public notify: SnotifyService,
		private crmapi: CrmApiGenericoService,
		private alunoService: TreinoApiAlunosService,
		private permissaoService: PermissaoService,
		private readonly admCoreApiClienteService: AdmCoreApiClienteService
	) {}

	ngOnInit() {
		this.permissaoAbaCrmRealizarContato =
			this.permissaoService.temPermissaoAdm("7.23");
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		if (this.matricula) {
			this.admCoreApiClienteService
				.dadosPessoais(this.matricula)
				.subscribe((response) => {
					this.dadosPessoais = response;
					this.alunoService
						.obterInfoAlunoApp(
							null,
							this.dadosPessoais.codigoPessoa,
							this.dadosPessoais.codigoCliente
						)
						.subscribe(
							(infoAlunoApp) => {
								this.alunoAppInfo = infoAlunoApp;
								this.cd.detectChanges();
							},
							(httpErrorResponse) => {
								this.alunoAppInfo = undefined;
							}
						);
				});
		}
		this.crmapi.possuiConfigSms().subscribe((response) => {
			this.possuiSmsHabilitado = response.content;
			this.cd.detectChanges();
		});
		this.getHistorico();
		this.cd.detectChanges();
	}

	carregarMais() {
		this.page = this.page + 1;
		this.size = this.page * 15;
		this.getHistorico();
	}

	getHistorico() {
		return this.crmapi
			.getHistoricoContato(this.matricula, 0, this.size)
			.subscribe((v) => {
				let lastMonthInUsage = 0;
				const contentedSorted = v.content.sort((a, b) => {
					// let diaA = new Date(a.dia);
					// let diaB = new Date(b.dia);
					// if (diaA < diaB) {
					// 	return -1;
					// }
					// if (diaA > diaB) {
					// 	return 1;
					// }
					return 0;
				});

				contentedSorted.forEach((element) => {
					const actualMonth = new Date(element.dia).getMonth();
					if (actualMonth !== lastMonthInUsage) {
						lastMonthInUsage = actualMonth;
						element.isTheFirstOfTheMonth = true;
					} else {
						element.isTheFirstOfTheMonth = false;
					}
				});
				this.historico = contentedSorted;

				if (this.expandirTudo) {
					contentedSorted.forEach((_, index) =>
						this.openItems.add(
							index + this.historico.length - contentedSorted.length
						)
					);
				}

				this.cd.detectChanges();
			});
	}

	get isSmsAllowed() {
		return true;
	}

	get isAppAllowed() {
		return true;
	}

	reloadHist() {
		this.getHistorico();
	}

	toggleExpandirTudo() {
		if (this.expandirTudo) {
			this.historico.forEach((_, index) => this.openItems.add(index));
		} else {
			this.openItems.clear();
		}
		this.cd.detectChanges();
	}

	toggleItem(index: number) {
		if (this.openItems.has(index)) {
			this.openItems.delete(index);
		} else {
			this.openItems.add(index);
		}
	}
}
