import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { CrmApiGenericoService } from "crm-api";
import { DataService } from "../notify/data.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";
import { RestService } from "@base-core/rest/rest.service";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalScriptComponent } from "../modals/modal-script/modal-script.component";
import { ModalAgendarAulaExperimentalComponent } from "../modals/modal-agendar-aula-experimental/modal-agendar-aula-experimental.component";
import { ModalAgendarVisitaComponent } from "../modals/modal-agendar-visita/modal-agendar-visita.component";
import { ModalAgendarLigacaoComponent } from "../modals/modal-agendar-ligacao/modal-agendar-ligacao.component";
import { ModalObjecaoComponent } from "../modals/modal-objecao/modal-objecao.component";
import { ClienteDadosPessoais } from "adm-core-api";

@Component({
	selector: "pacto-crm-pessoal",
	templateUrl: "./crm-pessoal.component.html",
	styleUrls: ["./crm-pessoal.component.scss"],
})
export class CrmPessoalComponent implements OnInit {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Output() reloadHist: EventEmitter<any> = new EventEmitter<any>();
	@Input() traducao: TraducoesXinglingComponent;
	form: FormGroup = new FormGroup({
		pesquisa: new FormControl(),
		observacao: new FormControl("", [Validators.required]),
	});

	constructor(
		private crmService: CrmApiGenericoService,
		private dataService: DataService,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private rest: RestService,
		private modalService: DialogService
	) {}

	get _rest() {
		return this.rest;
	}

	matricula: string;

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.cd.detectChanges();
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	checkScript() {
		let modalCheck = this.modalService.open(
			"Script",
			ModalScriptComponent,
			PactoModalSize.LARGE
		);
		modalCheck.componentInstance.selectedContract = this.form.get("pesquisa");
	}

	openModalObjecao() {
		let modalObjecao = this.modalService.open(
			"Objeção",
			ModalObjecaoComponent,
			PactoModalSize.LARGE
		);
	}

	simplesRegistro() {}

	openModalAulaExperimental() {
		let modalAulaExperimental = this.modalService.open(
			"Agendar aula experimental",
			ModalAgendarAulaExperimentalComponent,
			PactoModalSize.LARGE
		);
	}

	openModalVisita() {
		let modalVisita = this.modalService.open(
			"Agendar visita",
			ModalAgendarVisitaComponent,
			PactoModalSize.LARGE
		);
	}

	openModalLigacao() {
		let modalLigacao = this.modalService.open(
			"Agendar ligação",
			ModalAgendarLigacaoComponent,
			PactoModalSize.LARGE
		);
	}
}
