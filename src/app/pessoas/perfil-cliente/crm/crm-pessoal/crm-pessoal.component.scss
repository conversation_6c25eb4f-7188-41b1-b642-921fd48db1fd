// .action-button{
//   margin-top: 20px;
// }
@import "projects/ui/assets/ui-kit.scss";

.row {
	div {
		padding: 0px;
	}
}

.actions {
	display: flex;
	justify-content: flex-end;

	button {
		margin: 8px 0 0 8px;
	}

	i.icon-drop {
		margin-top: 8px;
	}
}

.apply-button {
	display: flex;
	flex-direction: row;
	padding: 8px !important;
	align-items: center;
	justify-content: center;

	button {
		i.pct {
			padding-right: 8px;
		}
	}
}

button[ds3-outlined-button] {
	&:has(+ .icon-drop) {
		border-radius: 4px 0px 0px 4px;
	}

	& + .icon-drop {
		height: 42px;
		padding: 5.5px 4px 5px 3px;
		border-radius: 0 4px 4px 0;
		border-top: 1px solid var(--color-action-default-able-4);
		border-left: none;
		border-right: 1px solid var(--color-action-default-able-4);
		border-bottom: 1px solid var(--color-action-default-able-4);
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--color-action-default-able-4);
		margin-left: -2px;
	}
}

button[ds3-flat-button] {
	&:has(+ .icon-drop) {
		border-radius: 4px 0px 0px 4px;
	}

	& + .icon-drop {
		height: 42px;
		padding: 5.5px 4px 5px 3px;
		border-radius: 0 4px 4px 0;
		border-top: 0px;
		border-left: 1px solid var(--color-action-bgdark-able-3);
		border-right: 0px;
		border-bottom: 0px;
		display: flex;
		background-color: var(--color-action-default-able-4);
		align-items: center;
		justify-content: center;
		color: var(--color-action-bgdark-able-3);
		margin-left: -2px;
	}
}
