<div class="row">
	<div class="col-12">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('pesquisa')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/questionario/tipoquestionario')
			"
			[paramBuilder]="selectBuilder"
			idKey="codigo"
			label="Selecionar pesquisa"
			labelKey="nomeinterno"></pacto-cat-form-select-filter>
	</div>
	<!--    <div class="col-2 apply-button">-->
	<!--        <button-->
	<!--             ds3-outlined-button-->
	<!--             (click)="checkScript()"-->
	<!--        ><i class="pct pct-file-text"></i>-->
	<!--            Visualizar script de contato</button>-->
	<!--    </div>-->
	<div class="col-12">
		<pacto-cat-form-textarea
			[control]="this.form.get('observacao')"
			[label]="'Descrição de contato'"
			[placeholder]="'Digite aqui a descrição'"
			[rows]="'5'"></pacto-cat-form-textarea>
	</div>
	<div class="col-12 actions">
		<button (click)="openModalObjecao()" ds3-outlined-button>
			<i class="pct pct-file-text"></i>
			Objeção
		</button>
		<button (click)="simplesRegistro()" ds3-outlined-button>
			Simples registro
		</button>

		<button [matMenuTriggerFor]="dropAgendar" ds3-flat-button>Agendar</button>
		<i class="pct pct-chevron-down icon-drop"></i>
		<mat-menu #dropAgendar="matMenu">
			<button (click)="openModalAulaExperimental()" mat-menu-item>
				Aula Experimental
			</button>
			<button (click)="openModalVisita()" mat-menu-item>Visita</button>
			<button (click)="openModalLigacao()" mat-menu-item>Ligação</button>
		</mat-menu>
	</div>
</div>
