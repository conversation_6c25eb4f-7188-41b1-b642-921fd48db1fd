<div class="row">
	<div class="col-md-12" style="margin-top: 15px">
		<pacto-cat-select-filter
			[control]="this.form.get('tipoObjecao')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/comum/objecao?responseLegacy=true')
			"
			[id]="'codigo'"
			[labelKey]="'descricao'"
			[label]="'Tipo de objeção'"
			[paramBuilder]="modeloMensagemSelectBuilder"
			[placeholder]="'Tipo de objeção'"
			[resposeParser]="responseParser"></pacto-cat-select-filter>
	</div>
</div>
<pacto-cat-form-textarea
	[control]="this.form.get('observacao')"
	[id]="'input-objecao-contato'"
	[label]="'Descrição de contato'"
	[placeholder]="'Digite aqui a descrição'"
	[rows]="'5'"></pacto-cat-form-textarea>
<div class="action-button">
	<pacto-crm-action
		[contatoForm]="this.form"
		[dropfase]="true"
		[indicar]="true"
		[salvar]="true"
		[script]="true"
		[tipoContato]="'OB'"></pacto-crm-action>
</div>
