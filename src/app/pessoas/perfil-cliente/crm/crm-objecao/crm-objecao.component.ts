import { ChangeDetectorRef, Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SelectFilterParamBuilder, SelectFilterResponseParser } from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { CrmApiGenericoService } from "crm-api";
import { DataService } from "../notify/data.service";
import { ActivatedRoute } from "@angular/router";
import { Subscription } from "rxjs";

@Component({
	selector: "pacto-crm-objecao",
	templateUrl: "./crm-objecao.component.html",
	styleUrls: ["./crm-objecao.component.scss"],
})
export class CrmObjecaoComponent implements OnInit, OnDestroy {
	constructor(
		private crmService: CrmApiGenericoService,
		private dataService: DataService,
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private rest: RestService
	) {}

	get _rest() {
		return this.rest;
	}

	form: FormGroup;

	matricula: string;
	notifierSubscription: Subscription =
		this.dataService.subjectNotifier.subscribe((notified) => {
			this.setMessate();
		});
	urlLink: string;

	setMessate() {
		this.ngOnInit();
	}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.createForm();

		this.crmService.getFone(this.matricula).subscribe((ret) => {
			if (ret !== undefined && ret.length > 0) {
				this.form.patchValue({ telefone: ret[0].numero });
			}
		});

		this.crmService.getFoneCel(this.matricula).subscribe((ret) => {
			if (ret !== undefined && ret.length > 0) {
				this.form.patchValue({ telefoneCelular: ret[0].numero });
			}
		});
	}

	createForm() {
		this.form = new FormGroup({
			tipoObjecao: new FormControl("", [Validators.required]),
			observacao: new FormControl("", [Validators.required]),
		});
		this.cd.detectChanges();
	}

	ngOnDestroy() {
		this.notifierSubscription.unsubscribe();
	}

	modeloMensagemSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};
	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};
}
