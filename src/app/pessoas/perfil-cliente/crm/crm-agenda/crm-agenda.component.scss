:host {
	display: flex;
	flex-direction: column;
	gap: 1rem;

	.crm-agendar-tipo-de-controle {
		margin-top: 1.5rem;
	}

	.gap-1 {
		gap: 1rem;
	}

	.columns-3 > * {
		width: 100%;
		display: block;
	}

	::ng-deep {
		.crm-agendar-tipo-de-controle .nome,
		.crm-agendar-mensagem-textarea .textarea-label {
			display: none;
		}

		pacto-cat-form-select-filter,
		pacto-cat-form-datepicker,
		pacto-cat-form-input,
		pacto-cat-form-textarea {
			margin: 0;
			font-size: 14px;

			.nome,
			.textarea-label {
				font-size: 14px;
			}

			.pct-error-msg:empty {
				display: none;
			}
		}

		pacto-cat-button .pacto-button.large {
			font-size: 14px;
		}
	}
}
