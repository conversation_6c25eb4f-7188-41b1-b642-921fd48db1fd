<pacto-cat-form-select-filter
	[control]="form.get('tipoContrato')"
	class="crm-agendar-tipo-de-controle"
	id="crm-agn-slct-tipo-contrato"
	placeholder="Tipo de contrato"></pacto-cat-form-select-filter>

<div class="d-flex gap-1">
	<pacto-cat-form-select-filter
		[control]="form.get('tipoAgendamento')"
		id="crm-agn-slct-tipo-agendamento"
		label="Tipo de agendamento"></pacto-cat-form-select-filter>

	<pacto-cat-form-select-filter
		[control]="form.get('tipoModalidade')"
		id="crm-agn-slct-tipo-modalidade"
		label="Tipo de modalidade"></pacto-cat-form-select-filter>
</div>

<div class="d-flex gap-1">
	<pacto-cat-form-select-filter
		[control]="form.get('colaborador')"
		id="crm-agn-slct-colaborador"
		label="Colaborador"></pacto-cat-form-select-filter>

	<pacto-cat-form-select-filter
		[control]="form.get('tipoProfessor')"
		id="crm-agn-slct-tipo-professor"
		label="Tipo de professor"></pacto-cat-form-select-filter>
</div>

<div class="d-flex gap-1 align-items-center columns-3">
	<pacto-cat-form-select-filter
		[control]="form.get('professor')"
		id="crm-agn-slct-professor"
		label="Professor"></pacto-cat-form-select-filter>

	<pacto-cat-form-datepicker
		[control]="form.get('data')"
		id="crm-agn-date-data"
		label="Data"></pacto-cat-form-datepicker>
	<pacto-cat-form-input
		[control]="form.get('horario')"
		[textMask]="{ mask: hourMask }"
		id="crm-agn-input-horario"
		label="Horário"
		placeholder="00:00"></pacto-cat-form-input>
</div>

<pacto-cat-form-textarea
	[control]="form.get('mensagem')"
	[rows]="5"
	class="crm-agendar-mensagem-textarea"
	id="input-mensagem"
	placeholder="Digite aqui sua mensagem"></pacto-cat-form-textarea>

<div class="d-flex justify-content-end gap-1">
	<pacto-cat-button
		(click)="clear()"
		id="crm-agn-btn-clear"
		label="Limpar"
		size="LARGE"
		type="OUTLINE"></pacto-cat-button>

	<pacto-cat-button
		id="crm-agn-btn-send"
		label="Enviar"
		size="LARGE"></pacto-cat-button>
</div>
