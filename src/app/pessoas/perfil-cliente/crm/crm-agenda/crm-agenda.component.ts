import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-crm-agenda",
	templateUrl: "./crm-agenda.component.html",
	styleUrls: ["./crm-agenda.component.scss"],
})
export class CrmAgendaComponent implements OnInit {
	form: FormGroup = new FormGroup({
		tipoContrato: new FormControl(),
		tipoAgendamento: new FormControl(),
		tipoModalidade: new FormControl(),
		colaborador: new FormControl(),
		tipoProfessor: new FormControl(),
		professor: new FormControl(),
		data: new FormControl(),
		horario: new FormControl(),
		mensagem: new FormControl(),
	});

	hourMask = [/[0-9]/, /[0-9]/, ":", /[0-9]/, /[0-9]/];

	constructor() {}

	ngOnInit() {}

	clear(): void {
		this.form.reset();
	}
}
