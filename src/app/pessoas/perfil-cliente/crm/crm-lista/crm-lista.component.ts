import {
	ChangeDetectorR<PERSON>,
	Component,
	OnDestroy,
	OnInit,
	Optional,
	ViewChild,
} from "@angular/core";

import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { ActivatedRoute } from "@angular/router";
import { DataService } from "../notify/data.service";
import { Subscription } from "rxjs";

@Component({
	selector: "pacto-crm-lista",
	templateUrl: "./crm-lista.component.html",
	styleUrls: ["./crm-lista.component.scss"],
})
export class CrmListaComponent implements OnInit, OnDestroy {
	@ViewChild("relatorio", { static: true }) relatorio: RelatorioComponent;

	notifierSubscription: Subscription =
		this.dataService.subjectNotifier.subscribe((notified) => {
			this.configTable();
			this.relatorio.reloadData();
		});

	constructor(
		private rest: RestService,
		private route: ActivatedRoute,
		private dataService: DataService,
		private cd: ChangeDetectorRef
	) {}

	table: PactoDataGridConfig;
	matricula: string;

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.configTable();
	}

	private configTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.rest.buildFullUrlCrmCore(
					`v1/avulso/historico/cliente-matricula/${this.matricula}?responseLegacy=true`
				),
				quickSearch: false,
				customFilterTab: true,
				endpintUrlGroup: this.rest.buildFullUrlCrmCore(
					`v1/avulso/historico/agrupadoPorTipoContato/matricula-cliente/${this.matricula}?responseLegacy=false`
				),
				ghostLoad: true,
			});
			this.cd.detectChanges();
		});
	}

	ngOnDestroy() {
		this.notifierSubscription.unsubscribe();
	}
}
