<div class="row">
	<div class="col-12 div-explicacao-item-crm">
		<i class="pct pct-info div-explicacao-item-crm-icon"></i>
		<span *ngIf="isTelefone()" class="div-explicacao-item-crm-text">
			Esta aba serve para registrar uma ligação feita para o aluno. Você pode
			registrar um agendamento ou uma objeção levantada durante a ligação. Caso
			não haja nenhum resultado, registre a chamada como um simples registro.
		</span>
		<span *ngIf="isWhatsApp()" class="div-explicacao-item-crm-text">
			Esta aba serve para registrar ou iniciar um contato via WhatsApp. Ao
			selecionar a opção "Entrar em Contato", você será direcionado para o
			WhatsApp e a interação será automaticamente registrada como “Simples
			registro”. Para registrar uma objeção ou agendamento que ocorreu pelo
			WhatsApp, basta selecionar uma das opções de resultado disponíveis.
		</span>
		<span *ngIf="isPessoal()" class="div-explicacao-item-crm-text">
			Esta aba destina-se ao registro de contatos pessoais realizados. Aqui,
			você pode registrar um agendamento ou objeção resultante desse contato. Em
			caso de ausência de resultados significativos, apenas faça um simples
			registro.
		</span>
	</div>
	<div class="col-12">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('pesquisa')"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/questionario/tipoquestionario')
			"
			[paramBuilder]="selectBuilder"
			idKey="codigo"
			label="Selecionar pesquisa"
			labelKey="nomeinterno"></pacto-cat-form-select-filter>
	</div>
	<div *ngIf="isWhatsApp()" class="col-12">
		<pacto-cat-form-select
			[control]="form.get('telefoneCelular')"
			[items]="getTelefonesCelular()"
			idKey="id"
			label="WhatsApp"
			labelKey="label"></pacto-cat-form-select>
	</div>
	<div class="col-12">
		<pacto-cat-form-textarea
			[control]="form.get('observacao')"
			[id]="'input-telefone-contato'"
			[label]="isWhatsApp() ? 'Mensagem' : 'Descrição de contato'"
			[placeholder]="'Digite aqui a descrição'"
			[rows]="'5'"></pacto-cat-form-textarea>
	</div>
	<div *ngIf="isTelefone()" class="col-12 contatos">
		<span class="contantos-header">Contatos</span>
		<div
			*ngIf="dadosPessoais?.telefones && dadosPessoais?.telefones.length > 0"
			class="contatos-grid">
			<div *ngFor="let fone of dadosPessoais?.telefones" class="fone">
				<p *ngIf="fone?.tipo === 'CE'" class="fone-label">Celular</p>
				<p *ngIf="fone?.tipo === 'CO'" class="fone-label">Comercial</p>
				<p *ngIf="fone?.tipo === 'EM'" class="fone-label">Emergência</p>
				<p *ngIf="fone?.tipo === 'RE'" class="fone-label">Residencial</p>
				<p *ngIf="fone?.tipo === 'RC'" class="fone-label">Recado</p>
				<span class="fone-number">{{ fone?.numero }}</span>
			</div>
		</div>
	</div>
	<div class="col-12 actions">
		<button
			(click)="simplesRegistro()"
			*ngIf="isWhatsApp()"
			ds3-outlined-button>
			<i class="pct pct-whatsapp" style="margin-right: 5px"></i>
			Entrar em contato
		</button>
		<button (click)="openModalObjecao()" ds3-outlined-button>Objeção</button>
		<button
			(click)="simplesRegistro()"
			*ngIf="isPessoal() || isTelefone()"
			ds3-outlined-button>
			Simples registro
		</button>
		<button [matMenuTriggerFor]="dropAgendar" ds3-flat-button>Agendar</button>
		<i class="pct pct-chevron-down icon-drop"></i>
		<mat-menu #dropAgendar="matMenu">
			<button (click)="openModalAgendar('AE')" mat-menu-item>
				Aula Experimental
			</button>
			<button (click)="openModalAgendar('VI')" mat-menu-item>Visita</button>
			<button (click)="openModalAgendar('LI')" mat-menu-item>Ligação</button>
		</mat-menu>
	</div>
</div>
