import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { DataService } from "../notify/data.service";
import { FormControl } from "@angular/forms";
import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CrmApiGenericoService, Contato } from "crm-api";
import { DatePipe } from "@angular/common";
import { SessionService } from "@base-core/client/session.service";
import { ActivatedRoute } from "@angular/router";
import { TipoPerguntaEnum } from "../classes/TipoPerguntaEnum";

@Component({
	selector: "pacto-crm-action",
	templateUrl: "./crm-action.component.html",
	styleUrls: ["./crm-action.component.scss"],
})
export class CrmActionComponent implements OnInit {
	constructor(
		private dataService: DataService,
		private rest: RestService,
		private notify: SnotifyService,
		private crmService: CrmApiGenericoService,
		private modal: NgbModal,
		private datePipe: DatePipe,
		private sessionService: SessionService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef
	) {}

	get _rest() {
		return this.rest;
	}

	@Input() salvar;
	@Input() contato;
	@Input() enviar;
	@Input() script;
	@Input() indicar;
	@Input() dropfase;
	@Input() dropPesquisa;
	@Input() contatoForm;
	@Input() tipoContato;
	@Input() enviarEmailBoasVindas;
	@ViewChild("configSuccess", { static: true }) configSuccess;
	@ViewChild("emailAgendado", { static: true }) emailAgendado;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;

	@Output() linkPesquisas = new EventEmitter();
	clienteid: number;
	matricula: string;
	obse: string;
	itemContato: Contato = {};
	private mywindow;
	modeloFases = new FormControl(null);
	modeloScript = new FormControl(null);

	ngOnInit(): void {
		this.matricula = this.route.snapshot.params["aluno-matricula"];

		this.crmService.getPessoa(this.matricula).subscribe((ret) => {
			if (ret && Array.isArray(ret)) {
				this.clienteid = ret[0].codigo;
			}
		});
	}

	contatos(
		dia: string,
		cliente: number,
		passivo: number,
		indicado: number,
		maladireta: number,
		observacao: string,
		tipooperacao: string,
		responsavelcadastro: number,
		objecao: number,
		agenda: number,
		fase: string,
		resultado: string,
		tipocontato: string,
		grausatisfacao: string,
		contatoavulso: boolean,
		resposta: string,
		opcoes: string,
		codigonotificacao: number,
		conviteaulaexperimental: number,
		dataproximoenvio: string,
		wagienvi: boolean,
		codigoObjecao: number,
		codigoAgenda: number,
		tipooresultado: string,
		modelo: number,
		titulo: string,
		resposta1: string,
		resposta2: string,
		resposta3: string,
		empresa: number,
		telefoneCelular: string,
		email: string
	) {
		this.itemContato.dia = dia;
		this.itemContato.cliente = cliente;
		this.itemContato.passivo = passivo;
		this.itemContato.indicado = indicado;
		this.itemContato.maladireta = maladireta;
		this.itemContato.observacao = observacao;
		this.itemContato.tipooperacao = tipooperacao;
		this.itemContato.responsavelcadastro = responsavelcadastro;
		this.itemContato.objecao = objecao;
		this.itemContato.agenda = agenda;
		this.itemContato.fase = fase;
		this.itemContato.resultado = resultado;
		this.itemContato.tipocontato = tipocontato;
		this.itemContato.grausatisfacao = grausatisfacao;
		this.itemContato.contatoavulso = contatoavulso;
		this.itemContato.opcoes = opcoes;
		this.itemContato.codigonotificacao = codigonotificacao;
		this.itemContato.conviteaulaexperimental = conviteaulaexperimental;
		this.itemContato.dataproximoenvio = dataproximoenvio;
		this.itemContato.wagienvi = wagienvi;
		this.itemContato.codigoObjecao = codigoObjecao;
		this.itemContato.codigoAgenda = codigoAgenda;
		this.itemContato.tiporesultado = tipooresultado;
		this.itemContato.titulo = titulo;
		this.itemContato.resposta1 = resposta1;
		this.itemContato.resposta2 = resposta2;
		this.itemContato.resposta3 = resposta3;
		this.itemContato.modelo = modelo;
		this.itemContato.empresa = empresa;
		this.itemContato.telefoneCelular = telefoneCelular;
		this.itemContato.email = email;

		switch (this.itemContato.tipocontato) {
			case "TE": // Telefone
				this.crmService.saveSimplesContato(this.itemContato).subscribe(
					(response) => {
						this.notify.success(
							this.traducao.getLabel("contato-salvo-com-sucesso")
						);
						this.notifyForChange();
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
				break;
			case "WA": // WhatsApp
				this.crmService
					.saveWhatsAppContato(this.itemContato, this.matricula)
					.subscribe(
						(response) => {
							this.notify.success(
								this.traducao.getLabel("contato-salvo-com-sucesso")
							);
							this.notifyForChange();

							if (this.mywindow) {
								this.mywindow.close();
							}
							this.mywindow = window.open(
								response.content.url,
								"_blank",
								"resizable=no, toolbar=no, scrollbars=no, menubar=no, " +
									"status=no, directories=no, location=no, width=1000, height=600, top=100 "
							);
						},
						(httpErrorResponse) => {
							if (
								httpErrorResponse.error &&
								httpErrorResponse.error.meta &&
								httpErrorResponse.error.meta.message
							) {
								this.notify.error(httpErrorResponse.error.meta.message);
							} else {
								this.notify.error(
									this.traducao.getLabel("falha-ao-tentar-salvar-contato")
								);
							}
						}
					);
				break;
			case "OB": // Objeção
				this.itemContato.tipo = "CLIENTE";
				this.itemContato.codigoClienteIndicadoPassivo =
					this.itemContato.cliente;
				this.crmService.saveObjecaoContato(this.itemContato).subscribe(
					(response) => {
						this.notify.success(
							this.traducao.getLabel("objecao-salva-com-sucesso")
						);
						this.notifyForChange();
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-objecao")
							);
						}
					}
				);
				break;
			case "PE": // Pessoal
				this.crmService.saveSimplesContato(this.itemContato).subscribe(
					(response) => {
						this.notify.success(
							this.traducao.getLabel("contato-salvo-com-sucesso")
						);
						this.notifyForChange();
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
				break;
			case "AP": // Aplicativo
				this.crmService.saveAppContato(this.itemContato).subscribe(
					(response) => {
						this.notify.success(
							this.traducao.getLabel("contato-salvo-com-sucesso")
						);
						this.notifyForChange();
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-salvar-contato")
							);
						}
					}
				);
				break;
			case "EM": // E-mail
				this.crmService.saveEmailContato(this.itemContato).subscribe(
					(response) => {
						this.notify.success(
							this.traducao.getLabel("email-agendado-com-sucesso")
						);
						this.notifyForChange();
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-agendar-email")
							);
						}
					}
				);
				break;

			case "CS":
				this.crmService.saveSmsContato(this.itemContato).subscribe(
					(response) => {
						this.notify.success(
							this.traducao.getLabel("sms-enviado-com-sucesso")
						);
						this.notifyForChange();
					},
					(httpErrorResponse) => {
						if (
							httpErrorResponse.error &&
							httpErrorResponse.error.meta &&
							httpErrorResponse.error.meta.message
						) {
							this.notify.error(httpErrorResponse.error.meta.message);
						} else {
							this.notify.error(
								this.traducao.getLabel("falha-ao-tentar-enviar-sms")
							);
						}
					}
				);
				break;
		}
	}

	modeloMensagemSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		if (!response || (Array.isArray(response) && response.length === 0)) {
			return [];
		}

		return response.content;
	};

	responseParsers: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		if (!response || (Array.isArray(response) && response.length === 0)) {
			return [];
		}

		return response.content;
	};

	onchangeGenerico() {
		if (this.modeloScript.value != null) {
			this.crmService
				.geraLinkPesquisa(
					this.modeloScript.value.codigo,
					this.clienteid,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe((ret) => {
					this.linkPesquisas.emit(ret[0].url);
				});
		}
	}

	onchangeFase() {
		if (this.modeloFases.value != null) {
			this.contato.fase = this.modeloFases.value;
		} else {
			if (!this.contato) {
				return;
			}

			this.contato.fase = " ";
		}
	}

	notifyForChange() {
		this.dataService.notifyAboutChange();
	}

	get tipoObjecao(): any {
		return this.contatoForm.get("tipoObjecao") === undefined ||
			this.contatoForm.get("tipoObjecao") == null
			? null
			: this.contatoForm.get("tipoObjecao").value;
	}

	get telefone(): any {
		return this.contatoForm.get("telefone");
	}

	get observacao(): any {
		return this.contatoForm.get("observacao");
	}

	get telefoneCelular(): any {
		return this.contatoForm.get("telefoneCelular");
	}

	get mensagem(): any {
		return this.contatoForm.get("mensagem");
	}

	get modeloMensagem(): any {
		return this.contatoForm.get("modeloMensagem");
	}

	get para(): any {
		return this.contatoForm.get("para");
	}

	get assunto(): any {
		return this.contatoForm.get("assunto");
	}

	get titulo(): any {
		return this.contatoForm.get("titulo");
	}

	get observacaoApp(): any {
		return this.contatoForm.get("mensagem");
	}

	get resposta1(): any {
		return this.contatoForm.get("resposta1") !== null
			? this.contatoForm.get("resposta1")
			: "";
	}

	get resposta2(): any {
		return this.contatoForm.get("resposta2") !== null
			? this.contatoForm.get("resposta2")
			: "";
	}

	get resposta3(): any {
		return this.contatoForm.get("resposta3") != null
			? this.contatoForm.get("resposta3")
			: "";
	}

	get tipoMensagem(): any {
		return this.contatoForm.get("tipoMensagem");
	}

	salvarContato() {
		if (this.contatoForm.status === "INVALID") {
			switch (this.tipoContato) {
				case "TE":
					this.isValidTelefonico();
					break;
				case "WA":
					this.isValidCelular();
					break;
				case "OB":
					this.isValidObjecao();
					break;
				case "PE":
					this.isValidPessoal();
					break;
				case "PE":
					this.isValidPessoal();
					break;
				case "AP":
					this.isValidApp();
					break;
				case "EM":
					this.isValidEmail();
					break;
				case "CS":
					this.isValidCelular();
					break;
			}
		} else {
			this.contatos(
				new Date().getTime().toString(),
				this.clienteid,
				null,
				null,
				null,
				this.observacao === undefined || this.observacao === null
					? this.observacaoApp.value
					: this.observacao.value,
				"",
				this.sessionService.codigoUsuarioZw,
				this.tipoObjecao != null ? this.tipoObjecao.codigo : this.tipoObjecao,
				null,
				this.modeloFases.value !== null ? this.modeloFases.value.sigla : "",
				"",
				this.tipoContato,
				"",
				true,
				"",
				"",
				null,
				null,
				"",
				false,
				this.tipoObjecao != null ? this.tipoObjecao.codigo : this.tipoObjecao,
				0,
				"SR",
				this.tipoMensagem !== undefined && this.tipoMensagem !== null
					? this.tipoMensagem.value.codigo
					: "",
				this.titulo !== null ? this.titulo.value : "",
				this.resposta1 !== "" ? this.resposta1.value : "",
				this.resposta2 !== "" ? this.resposta2.value : "",
				this.resposta3 !== "" ? this.resposta3.value : "",
				parseInt(this.sessionService.empresaId, 10),
				this.telefoneCelular !== null ? this.telefoneCelular.value : "",
				this.para !== null ? this.para.value : ""
			);
		}
	}

	isValidTelefonico() {
		if (this.contatoForm.status === "INVALID") {
			if (this.telefoneCelular.value === "") {
				this.telefoneCelular.markAsTouched();
			}
			if (this.telefone.value === "") {
				this.telefone.markAsTouched();
			}
			if (this.observacao.value === "") {
				this.observacao.markAsTouched();
			}
		}
	}

	isValidCelular() {
		if (this.contatoForm.status === "INVALID") {
			if (this.telefoneCelular.value === "") {
				this.notify.error(
					this.traducao.getLabel("telefone-celular-obrigatorio")
				);
				this.telefoneCelular.markAsTouched();
			}
			if (this.observacao.value === "") {
				this.observacao.markAsTouched();
			}
		}
	}

	isValidEmail() {
		if (this.contatoForm.status === "INVALID") {
			if (this.para.value === "") {
				this.notify.error(this.traducao.getLabel("email-obrigatorio"));
				this.para.markAsTouched();
			}
			if (this.titulo.value === "") {
				this.notify.error(this.traducao.getLabel("titulo-obrigatorio"));
				this.titulo.markAsTouched();
			}
			if (this.observacao.value === "") {
				this.notify.error(this.traducao.getLabel("mensagem-obrigatoria"));
				this.observacao.markAsTouched();
			}
		}
	}

	isValidPessoal() {
		if (this.observacao.value === "") {
			this.notify.error("Digite uma observação");
		}
	}

	isValidObjecao() {
		if (this.contatoForm.status === "INVALID") {
			if (this.tipoObjecao === null || this.tipoObjecao === "") {
				this.contatoForm.get("tipoObjecao").markAsTouched();
				this.notify.error(
					this.traducao.getLabel("tipo-de-objecao-nao-informado")
				);
			}
			if (this.observacao.value === "") {
				this.observacao.markAsTouched();
			}
		}
	}

	isValidApp() {
		if (this.tipoMensagem.value == null) {
			this.notify.error("Selecione uma mensagem");
		}
		if (this.contatoForm.status === "INVALID") {
			if (this.mensagem.value == null) {
				this.mensagem.markAsTouched();
				if (this.contatoForm.tipoMensagem === TipoPerguntaEnum.SIMPLES) {
					this.notify.error("Mensagem é um campo obrigatório");
				} else {
					this.notify.error("Pergunta é um campo obrigatório");
				}
			}
		}
		if (this.contatoForm.status === "INVALID") {
			if (this.titulo.value === "") {
				this.titulo.markAsTouched();
				this.notify.error("Título é um campo obrigatório");
			}
		}
		if (
			this.contatoForm.status === "INVALID" &&
			this.contatoForm.tipoMensagem === TipoPerguntaEnum.OBJETIVA
		) {
			if (this.resposta1.value === "") {
				this.resposta1.markAsTouched();
				this.notify.error("O campo resposta 1 é obrigatórioo");
			}
		}
		if (
			this.contatoForm.status === "INVALID" &&
			this.contatoForm.tipoMensagem === TipoPerguntaEnum.OBJETIVA
		) {
			if (this.resposta2.value === "") {
				this.resposta2.markAsTouched();
				this.notify.error("O campo resposta 2 é obrigatórioo");
			}
		}
		if (
			this.contatoForm.status === "INVALID" &&
			this.contatoForm.tipoMensagem === TipoPerguntaEnum.OBJETIVA
		) {
			if (this.resposta3.value === "") {
				this.resposta3.markAsTouched();
				this.notify.error("O campo resposta 3 é obrigatórioo");
			}
		}

		if (this.contatoForm.message) {
			if (this.mensagem.value.length > 255) {
				this.notify.error(
					"Não é possível enviar uma notificação com mais de 255 caracteres"
				);
			}
		}
	}

	descartarContato() {
		switch (this.tipoContato) {
			case "TE":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				break;
			case "WA":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				break;
			case "OB":
				this.modeloFases.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				break;
			case "PE":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				break;
			case "PE":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				break;
			case "AP":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("titulo").setValue("");
				this.contatoForm.get("mensagem").setValue("");
				this.contatoForm.get("resposta1").setValue("");
				this.contatoForm.get("resposta2").setValue("");
				this.contatoForm.get("resposta3").setValue("");
				break;
			case "EM":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				this.contatoForm.get("assunto").setValue("");
				break;
			case "CS":
				this.modeloFases.setValue(null);
				this.modeloScript.setValue(null);
				this.contatoForm.get("observacao").setValue("");
				break;
		}
		this.cd.detectChanges();
	}

	enviarEmailsDeBoasVindas() {}
}
