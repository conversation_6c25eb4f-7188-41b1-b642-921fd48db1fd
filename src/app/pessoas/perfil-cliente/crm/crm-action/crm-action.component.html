<div class="row row-action-crm">
	<!--
    <div *ngIf="script" class="col-md-1"  style="margin-top: -10px">
        <button class="btn btn-outline" style="padding-left: -5px;
                    border: 1px solid #D3D5D7;border-radius: 4px;
                    padding-right: 30px !important;" (click)="">
            <i class="pct pct-paperclip"></i>
            Script
        </button>

    </div>

    <div *ngIf="indicar" class="col-md-2"  style="margin-top: -10px">
        <button class="btn btn-outline" style=" border: 1px solid #D3D5D7;
             border-radius: 4px; margin-left: 30px" (click)="">
            <i class="pct pct-users"></i>
            Indicação
        </button>
    </div>
    -->

	<div *ngIf="dropPesquisa" class="col-3">
		<pacto-cat-select-filter
			(click)="onchangeGenerico()"
			[control]="modeloScript"
			[endpointUrl]="
				_rest.buildFullUrlCrmCore('v1/generico/questionario/tipoquestionario')
			"
			[id]="'codigo'"
			[labelKey]="'nomeinterno'"
			[paramBuilder]="modeloMensagemSelectBuilder"
			[placeholder]="'Selecione uma pesquisa'"
			[resposeParser]="responseParsers"
			i18n-label="@@tela-cliente-crm:label-pesquisa-satisfacao"
			label="Pesquisa de satisfação"></pacto-cat-select-filter>
	</div>

	<div *ngIf="dropfase" class="col-2 mr-auto">
		<pacto-cat-select-filter
			(click)="onchangeFase()"
			[control]="modeloFases"
			[endpointUrl]="_rest.buildFullUrlCrmCore('v1/avulso/historico/fase-crm')"
			[id]="'sigla'"
			[labelKey]="'descricao'"
			[paramBuilder]="modeloMensagemSelectBuilder"
			[placeholder]="'Fase Atual'"
			[resposeParser]="responseParser"
			i18n-label="@@tela-cliente-crm:label-fase-atual"
			label="Fase atual"></pacto-cat-select-filter>
	</div>
</div>

<div class="col-auto d-flex justify-content-end gap-1">
	<pacto-cat-button
		*ngIf="enviarEmailBoasVindas"
		i18n-label="@@tela-cliente-crm:btn-whatsapp"
		label="E-mail de boas vindas"
		size="LARGE"
		type="OUTLINE"></pacto-cat-button>

	<pacto-cat-button
		(click)="descartarContato()"
		i18n-label="@@tela-cliente-crm:btn-descartar"
		id="crm-btn-descartar"
		label="Descartar"
		size="LARGE"
		type="OUTLINE"></pacto-cat-button>

	<!-- <pacto-cat-button
            i18n-label="@@tela-cliente-crm:btn-whatsapp"
            label="Entre em contato"
            size="LARGE"
            icon="pct pct-whatsapp"
            (click)="salvarContato()"
            *ngIf="contato"
        >
        </pacto-cat-button> -->

	<pacto-cat-button
		(click)="salvarContato()"
		*ngIf="salvar"
		i18n-label="@@tela-cliente-crm:btn-salvar"
		id="crm-btn-save"
		label="Salvar"
		size="LARGE"
		type="PRIMARY"></pacto-cat-button>

	<pacto-cat-button
		(click)="salvarContato()"
		*ngIf="enviar"
		i18n-label="@@tela-cliente-crm:btn-enviar"
		id="crm-btn-send"
		label="Enviar"
		size="LARGE"
		type="PRIMARY"></pacto-cat-button>
</div>
<span
	#configSuccess
	[hidden]="true"
	i18n="@@configuration-sitema-aula:config-salva">
	Configurações salvas com sucesso.
</span>
<span
	#emailAgendado
	[hidden]="true"
	i18n="@@configuration-sitema-aula:email-agendado">
	E-mail Agendado com Sucesso.
</span>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@tela-cliente-crm:telefone-celular-obrigatorio"
		xingling="telefone-celular-obrigatorio">
		Nenhum telefone celular selecionado
	</span>
	<span
		i18n="@@tela-cliente-crm:email-obrigatorio"
		xingling="email-obrigatorio">
		O campo "Para" deve ser informado
	</span>
	<span
		i18n="@@tela-cliente-crm:titulo-obrigatorio"
		xingling="titulo-obrigatorio">
		O campo "Assunto" deve ser informado
	</span>
	<span
		i18n="@@tela-cliente-crm:menssagem-obrigatoria"
		xingling="menssagem-obrigatoria">
		O campo "Messagem" deve ser informado
	</span>

	<span
		i18n="@@tela-cliente-crm:contato-salvo-com-sucesso"
		xingling="contato-salvo-com-sucesso">
		Contato salvo com sucesso
	</span>
	<span
		i18n="@@tela-cliente-crm:falha-ao-tentar-salvar-contato"
		xingling="falha-ao-tentar-salvar-contato">
		Falha ao tentar salvar contato
	</span>

	<span
		i18n="@@tela-cliente-crm:objecao-salva-com-sucesso"
		xingling="objecao-salva-com-sucesso">
		Objeção salva com sucesso
	</span>
	<span
		i18n="@@tela-cliente-crm:falha-ao-tentar-salvar-objecao"
		xingling="falha-ao-tentar-salvar-objecao">
		Falha ao tentar salvar objeção
	</span>
	<span
		i18n="@@tela-cliente-crm:tipo-de-objecao-nao-informado"
		xingling="tipo-de-objecao-nao-informado">
		O campo "Tipo de objeção" deve ser informado
	</span>

	<span
		i18n="@@tela-cliente-crm:email-agendado-com-sucesso"
		xingling="email-agendado-com-sucesso">
		E-mail agendado com sucesso
	</span>
	<span
		i18n="@@tela-cliente-crm:falha-ao-agendar-email"
		xingling="falha-ao-agendar-email">
		Falha ao tentar agendar e-mail.
	</span>
	<span
		i18n="@@tela-cliente-crm:cliente-nao-possui-email-cadastrado"
		xingling="cliente-nao-possui-email-cadastrado">
		Cliente não possui e-mail cadastrado.
	</span>

	<span
		i18n="@@tela-cliente-crm:sms-enviado-com-sucesso"
		xingling="sms-enviado-com-sucesso">
		Enviado com sucesso
	</span>
	<span
		i18n="@@tela-cliente-crm:falha-ao-tentar-enviar-sms"
		xingling="falha-ao-tentar-enviar-sms">
		Falha ao tentar enviar SMS
	</span>
</pacto-traducoes-xingling>
