import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-modal-indicacao",
	templateUrl: "./modal-indicacao.component.html",
	styleUrls: ["./modal-indicacao.component.scss"],
})
export class ModalIndicacaoComponent implements OnInit {
	constructor() {}

	formGroup: FormGroup = new FormGroup({
		nome: new FormControl(),
		cpf: new FormControl(),
		clienteOuColaboradorQueIndicou: new FormControl(),
		evento: new FormControl(),
		telefoneCelular: new FormControl(),
		telefoneResidencial: new FormControl(),
		email: new FormControl(),
		observacao: new FormControl(),
	});

	ngOnInit() {}
}
