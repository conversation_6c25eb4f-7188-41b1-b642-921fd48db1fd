import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-agendar-ligacao",
	templateUrl: "./modal-agendar-ligacao.component.html",
	styleUrls: ["./modal-agendar-ligacao.component.scss"],
})
export class ModalAgendarLigacaoComponent implements OnInit {
	formGroup: FormGroup = new FormGroup({
		colaborador: new FormControl(),
		data: new FormControl(),
		horario: new FormControl(),
	});

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {}

	cancelar() {
		this.activeModal.close();
	}

	gravar() {}
}
