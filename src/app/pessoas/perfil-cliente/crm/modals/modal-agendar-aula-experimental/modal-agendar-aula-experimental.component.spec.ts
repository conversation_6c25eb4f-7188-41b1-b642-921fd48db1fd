import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalAgendarAulaExperimentalComponent } from "./modal-agendar-aula-experimental.component";

describe("ModalAgendarAulaExperimentalComponent", () => {
	let component: ModalAgendarAulaExperimentalComponent;
	let fixture: ComponentFixture<ModalAgendarAulaExperimentalComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalAgendarAulaExperimentalComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			ModalAgendarAulaExperimentalComponent,
			PactoModalSize.LARGE
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
