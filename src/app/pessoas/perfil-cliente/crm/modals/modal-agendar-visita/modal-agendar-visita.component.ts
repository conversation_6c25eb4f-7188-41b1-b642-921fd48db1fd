import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { ClienteDadosPessoais } from "adm-core-api";
import { Contato, CrmApiGenericoService } from "crm-api";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-modal-agendar-visita",
	templateUrl: "./modal-agendar-visita.component.html",
	styleUrls: ["./modal-agendar-visita.component.scss"],
})
export class ModalAgendarVisitaComponent implements OnInit {
	formGroup: FormGroup = new FormGroup({
		colaborador: new FormControl(),
		data: new FormControl(),
		horario: new FormControl(),
	});
	dadosPessoais: ClienteDadosPessoais;
	contato: Contato;
	traducao: TraducoesXinglingComponent;
	@Output() response: EventEmitter<any> = new EventEmitter<any>();

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private crmService: CrmApiGenericoService,
		private notify: SnotifyService,
		private rest: RestService
	) {}

	ngOnInit() {}

	get _rest() {
		return this.rest;
	}

	close() {
		this.activeModal.close();
	}

	getContato() {
		const contato = this.contato;
		contato.dia = new Date().getTime().toString();
		contato.objecao = this.formGroup.get("objecao").value.codigo;
		contato.resultado =
			"Objeção: " + this.formGroup.get("objecao").value.descricao;
		return contato;
	}

	salvar() {
		this.crmService.saveObjecaoContato(this.getContato()).subscribe(
			(response) => {
				this.response.emit("reloadHist");
				this.notify.success(
					this.traducao.getLabel("contato-salvo-com-sucesso")
				);
				this.close();
			},
			(httpErrorResponse) => {
				if (
					httpErrorResponse.error &&
					httpErrorResponse.error.meta &&
					httpErrorResponse.error.meta.message
				) {
					this.notify.error(httpErrorResponse.error.meta.message);
				} else {
					this.notify.error(
						this.traducao.getLabel("falha-ao-tentar-salvar-contato")
					);
				}
			}
		);
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	get timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}
}
