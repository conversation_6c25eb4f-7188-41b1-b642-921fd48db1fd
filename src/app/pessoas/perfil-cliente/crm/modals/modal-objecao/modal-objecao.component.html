<div class="col-md-12">
	<div class="col-12">
		<pacto-cat-select-filter
			[addEmptyOption]="false"
			[control]="formGroup.get('tipoObjecao')"
			[options]="tipoOptions"
			label="Tipo de objeção"></pacto-cat-select-filter>
	</div>
	<div class="col-12">
		<pacto-cat-select-filter
			[addEmptyOption]="true"
			[control]="formGroup.get('objecao')"
			[options]="objecaoOptions"
			[id]="'codigo'"
			[labelKey]="'descricao'"
			[label]="'Objeção'"
			[paramBuilder]="modeloMensagemSelectBuilder"
			[resposeParser]="responseParser"></pacto-cat-select-filter>
	</div>
	<div class="col-12">
		<div class="alert">
			<i class="pct pct-info icon"></i>
			<span>
				É recomendável que as objeções permanentes sejam casos onde temos
				certeza que o cliente não será contactado mais, pois ele não poderá
				retornar ao Meta diária. Ex: Falecimento, Mudança de País...
			</span>
		</div>
	</div>

	<div class="col-12 actions">
		<pacto-cat-button
			(click)="close()"
			label="Cancelar"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvar()"
			label="Salvar Objeção"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
