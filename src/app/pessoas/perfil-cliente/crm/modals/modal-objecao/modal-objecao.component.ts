import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { ClienteDadosPessoais } from "adm-core-api";
import { Contato, CrmApiGenericoService } from "crm-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-objecao",
	templateUrl: "./modal-objecao.component.html",
	styleUrls: ["./modal-objecao.component.scss"],
})
export class ModalObjecaoComponent implements OnInit {
	tipoOptions = [];
	objecaoOptions = [];
	formGroup: FormGroup = new FormGroup({
		tipoObjecao: new FormControl(),
		objecao: new FormControl(),
	});
	dadosPessoais: ClienteDadosPessoais;
	contato: Contato;
	traducao: TraducoesXinglingComponent;
	@Output() response: EventEmitter<any> = new EventEmitter<any>();

	constructor(
		private activeModal: NgbActiveModal,
		private crmService: CrmApiGenericoService,
		private cd: ChangeDetectorRef,
		private notify: SnotifyService,
		private rest: RestService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService
	) {}

	ngOnInit() {
		this.tipoOptions = [
			{
				id: "OB",
				label: "Objeção",
			},
			{
				id: "OD",
				label: "Objeção Definitiva",
			},
		];
		this.formGroup.get("tipoObjecao").setValue(this.tipoOptions[0]);
		this.atualizaObjecaoOptions(this.formGroup.get("tipoObjecao").value.id);
		this.formGroup.get("tipoObjecao").valueChanges.subscribe((value) => {
			if (value) {
				this.atualizaObjecaoOptions(value.id);
			}
		});
		this.cd.detectChanges();
	}

	atualizaObjecaoOptions(tipo: string) {
		this.crmService.getObjecaoPorTipo(tipo).subscribe((result) => {
			this.objecaoOptions = result.map((objecao) => {
				return { codigo: objecao.codigo, descricao: objecao.descricao };
			});
		});
	}

	get _rest() {
		return this.rest;
	}

	close() {
		this.activeModal.close();
	}

	getContato() {
		const contato = this.contato;
		contato.dia = new Date().getTime().toString();
		contato.objecao = this.formGroup.get("objecao").value.codigo;
		return contato;
	}

	validarDados() {
		if (
			!this.formGroup.get("objecao").value ||
			this.formGroup.get("objecao").value <= 0
		) {
			this.notify.error("Selecione uma objeção");
			return true;
		}
		return false;
	}

	salvar() {
		if (this.validarDados()) {
			return;
		}
		this.admLegadoTelaClienteService
			.salvarHistoricoContato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente,
				this.getContato()
			)
			.subscribe(
				(resp) => {
					if (this.getTipoSel() === "OD") {
						this.response.emit("reloadCliente");
					} else {
						this.response.emit("reloadHist");
					}
					this.notify.success("Objeção gravada com sucesso");
					this.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notify.error(err.meta.message);
						return;
					}
				}
			);
	}

	modeloMensagemSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	getTipoSel() {
		const val = this.formGroup.get("tipoObjecao").value;
		return val.id;
	}
}
