import { Component, Input, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-modal-script",
	templateUrl: "./modal-script.component.html",
	styleUrls: ["./modal-script.component.scss"],
})
export class ModalScriptComponent implements OnInit {
	script: FormControl = new FormControl("");
	@Input() selectedContract;

	constructor() {}

	ngOnInit() {
		this.script.setValue(
			`${this.selectedContract.value.textoinicio}   ${this.selectedContract.value.textofim}`
		);
	}
}
