import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import { Contato, CrmApiGenericoService } from "crm-api";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-crm-agendar",
	templateUrl: "./modal-crm-agendar.component.html",
	styleUrls: ["./modal-crm-agendar.component.scss"],
})
export class ModalCrmAgendarComponent implements OnInit {
	formGroup: FormGroup = new FormGroup({
		modalidade: new FormControl(),
		professor: new FormControl(),
		colaborador: new FormControl(),
		data: new FormControl(),
		horario: new FormControl(),
	});
	professoresOptions = [];
	consultoresOptions = [];
	// tipoAgendamento
	// LI = Ligacao
	// AE = Aula Experimental
	// VI = Visita
	tipoAgendamento: string;
	dadosPessoais: ClienteDadosPessoais;
	contato: Contato;
	traducao: TraducoesXinglingComponent;
	@Output() response: EventEmitter<any> = new EventEmitter<any>();

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private crmService: CrmApiGenericoService,
		private notify: SnotifyService,
		private rest: RestService,
		private sessionService: SessionService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private admCoreApiClienteService: AdmCoreApiClienteService
	) {}

	ngOnInit() {
		this.admCoreApiClienteService
			.obterConsultoresAtivos()
			.subscribe((result) => {
				this.consultoresOptions = result.content.map((consultor: any) => {
					return { id: consultor.codigo, label: consultor.pessoa.nome };
				});
			});
		this.admCoreApiClienteService
			.obterProfessoresAtivos()
			.subscribe((result) => {
				this.professoresOptions = result.content.map((classificacao: any) => {
					return { id: classificacao.codigo, label: classificacao.pessoa.nome };
				});
			});
		this.cd.detectChanges();
	}

	get _rest() {
		return this.rest;
	}

	close() {
		this.activeModal.close();
	}

	getContato() {
		const contato = this.contato;
		contato.dia = new Date().getTime().toString();
		contato.dataAgendamento = this.formGroup.get("data").value;
		contato.horaMinutoAgendamento = this.formGroup.get("horario").value;
		contato.tipoAgendamento = this.tipoAgendamento;

		if (this.isAulaExperimental()) {
			contato.modalidade = this.formGroup.get("modalidade").value.codigo;
			contato.colaborador = this.formGroup.get("professor").value.id;
		} else {
			contato.colaborador = this.formGroup.get("colaborador").value.id;
		}
		return contato;
	}

	salvar() {
		this.admLegadoTelaClienteService
			.salvarHistoricoContato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.dadosPessoais.codigoCliente,
				this.getContato()
			)
			.subscribe(
				(resp) => {
					this.notify.success("Agendamento realizado");
					this.response.emit("reloadHist");
					this.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notify.error(err.meta.message);
					}
				}
			);
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	get timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	isLigacao() {
		return this.tipoAgendamento === "LI";
	}

	isAulaExperimental() {
		return this.tipoAgendamento === "AE";
	}

	isVisita() {
		return this.tipoAgendamento === "VI";
	}
}
