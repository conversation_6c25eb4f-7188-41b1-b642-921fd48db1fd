import { Injectable } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Subject } from "rxjs";

export interface ConfigCampos {
	mostrar?: boolean;
	nome?: string;
	obrigatorio?: boolean;
	pendente?: boolean;
	validarCatraca?: boolean;
	visitante?: boolean;
	indicator?: string;
	cadastroDinamicoDTO?: { codigo: number; nomeTabela: string };
	campoObrigatorio?: boolean;
	codigo?: number;
	labelCampo?: string;
	mostrarCampo?: boolean;
	nomeCampo?: string;
}

@Injectable({
	providedIn: "root",
})
export class ConfigCliFormValidationService {
	private _configuracoesCampoCliente: Array<ConfigCampos> =
		new Array<ConfigCampos>();
	private _configuracoesCampoVisitante: Array<ConfigCampos> =
		new Array<ConfigCampos>();
	private _configuracoesCampoColaborador: Array<ConfigCampos> =
		new Array<ConfigCampos>();
	isColabOnly: boolean;
	isColabAndClient: boolean;
	isClientOnly: boolean;
	situacaoCliente: string;
	private _update: Subject<boolean> = new Subject<boolean>();

	get configuracoesCampoCliente(): Array<ConfigCampos> {
		return this._configuracoesCampoCliente;
	}

	set configuracoesCampoCliente(value: Array<ConfigCampos>) {
		this._configuracoesCampoCliente = value;
	}

	get configuracoesCampoVisitante(): Array<ConfigCampos> {
		return this._configuracoesCampoVisitante;
	}

	set configuracoesCampoVisitante(value: Array<ConfigCampos>) {
		this._configuracoesCampoVisitante = value;
	}

	get configuracoesCampoColaborador(): Array<ConfigCampos> {
		return this._configuracoesCampoColaborador;
	}

	set configuracoesCampoColaborador(value: Array<ConfigCampos>) {
		this._configuracoesCampoColaborador = value;
	}

	fieldsKeysForPF = [
		"cpf",
		"rg",
		"nacionalidade",
		"naturalidade",
		"sexo",
		"genero",
		"estadoCivil",
		"profissao",
		"grauInstrucao",
	];

	configCampoClienteObj: { [campo: string]: ConfigCampos } = {
		bairro: {
			nome: "Bairro",
		},
		categoria: {
			nome: "Categoria",
		},
		cep: {
			nome: "Cep",
		},
		cidade: {
			nome: "Cidade",
		},
		contatoEmergencia: {
			nome: "Contato Emergência",
		},
		cpf: {
			nome: "CPF",
		},
		cnpj: {
			nome: "CNPJ",
		},
		cnpjClienteSesi: {
			nome: "CNPJ Sesi Indústria",
		},
		cpfMae: {
			nome: "CPF Mãe",
		},
		cpfPai: {
			nome: "CPF Pai",
		},
		dataNasc: {
			nome: "Data Nascimento",
		},
		descricaoTelefone: {
			nome: "Descrição Telefone",
		},
		email: {
			nome: "Email",
		},
		endereco: {
			nome: "Endereço",
		},
		complemento: {
			nome: "Endereço Complemento",
		},
		estado: {
			nome: "Estado",
		},
		estadoCivil: {
			nome: "Estado Civil",
		},
		genero: {
			nome: "Gênero",
		},
		grauInstrucao: {
			nome: "Grau de Instrução",
		},
		matricula: {
			nome: "Matrícula",
		},
		/*nome: {
      nome: 'Nome'
    },*/
		nomeMae: {
			nome: "Nome Mãe ou Responsável",
		},
		nomePai: {
			nome: "Nome Pai ou Responsável",
		},
		numero: {
			nome: "Número",
		},
		pais: {
			nome: "País",
		},
		profissao: {
			nome: "Profissão",
		},
		rg: {
			nome: "RG",
		},
		rgMae: {
			nome: "RG Mãe",
		},
		rgPai: {
			nome: "RG Pai",
		},
		sexo: {
			nome: "Sexo",
		},
		telefone: {
			nome: "Telefone",
		},
		telefoneEmergencia: {
			nome: "Telefone Emergência",
		},
		webPage: {
			nome: "Web Page",
		},
	};

	configCampoVisitanteObj: { [campo: string]: ConfigCampos } = {
		bairro: {
			nome: "Bairro",
		},
		categoria: {
			nome: "Categoria",
		},
		cep: {
			nome: "Cep",
		},
		cidade: {
			nome: "Cidade",
		},
		contatoEmergencia: {
			nome: "Contato Emergência",
		},
		cpf: {
			nome: "CPF",
		},
		cnpj: {
			nome: "CNPJ",
		},
		cnpjClienteSesi: {
			nome: "CNPJ Sesi Indústria",
		},
		cpfMae: {
			nome: "CPF Mãe",
		},
		cpfPai: {
			nome: "CPF Pai",
		},
		dataNasc: {
			nome: "Data Nascimento",
		},
		descricaoTelefone: {
			nome: "Descrição Telefone",
		},
		email: {
			nome: "Email",
		},
		endereco: {
			nome: "Endereço",
		},
		complemento: {
			nome: "Endereço Complemento",
		},
		estado: {
			nome: "Estado",
		},
		estadoCivil: {
			nome: "Estado Civil",
		},
		genero: {
			nome: "Gênero",
		},
		grauInstrucao: {
			nome: "Grau de Instrução",
		},
		matricula: {
			nome: "Matrícula",
		},
		/*nome: {
      nome: 'Nome'
    },*/
		nomeMae: {
			nome: "Nome Mãe ou Responsável",
		},
		nomePai: {
			nome: "Nome Pai ou Responsável",
		},
		numero: {
			nome: "Número",
		},
		pais: {
			nome: "País",
		},
		profissao: {
			nome: "Profissão",
		},
		rg: {
			nome: "RG",
		},
		rgMae: {
			nome: "RG Mãe",
		},
		rgPai: {
			nome: "RG Pai",
		},
		sexo: {
			nome: "Sexo",
		},
		telefone: {
			nome: "Telefone",
		},
		telefoneEmergencia: {
			nome: "Telefone Emergência",
		},
		webPage: {
			nome: "Web Page",
		},
	};

	configCampoColaboradorObj: { [campo: string]: ConfigCampos } = {
		autenticarGoogle: {
			nomeCampo: "Autenticar Google",
		},
		bairro: {
			nomeCampo: "Bairro",
		},
		cep: {
			nomeCampo: "CEP",
		},
		cpf: {
			nomeCampo: "CPF",
		},
		cnpj: {
			nomeCampo: "CNPJ",
		},
		cref: {
			nomeCampo: "CREF",
		},
		cidade: {
			nomeCampo: "Cidade",
		},
		complemento: {
			nomeCampo: "Complemento",
		},
		contatoEmergencia: {
			nomeCampo: "Contato para Emergência",
		},
		cpfMae: {
			nomeCampo: "Cpf da Mãe ou Responsável",
		},
		dataCadastro: {
			nomeCampo: "Data Cadastro",
		},
		email: {
			nomeCampo: "Email",
		},
		endereco: {
			nomeCampo: "Endereço",
		},
		enderecoCorrespondencia: {
			nomeCampo: "Endereço para Correspondência",
		},
		estado: {
			nomeCampo: "Estado",
		},
		estadoCivil: {
			nomeCampo: "Estado Civil",
		},
		genero: {
			nomeCampo: "Gênero",
		},
		nacionalidade: {
			nomeCampo: "Nacionalidade",
		},
		naturalidade: {
			nomeCampo: "Naturalidade",
		},
		nomeMae: {
			nomeCampo: "Nome da Mãe ou Responsável",
		},
		nomePai: {
			nomeCampo: "Nome do Pai ou Responsável",
		},
		numero: {
			nomeCampo: "Número",
		},
		pais: {
			nomeCampo: "País",
		},
		porcentagemComissao: {
			nomeCampo: "Porcentagem Comissão",
		},
		produtoDefaultPersonal: {
			nomeCampo: "Produto Default do Personal trainer",
		},
		profissao: {
			nomeCampo: "Profissão",
		},
		rg: {
			nomeCampo: "RG",
		},
		sexo: {
			nomeCampo: "Sexo",
		},
		telefone: {
			nomeCampo: "Telefone",
		},
		telefoneEmergencia: {
			nomeCampo: "Telefone Emergência",
		},
		tipoEndereco: {
			nomeCampo: "Tipo Endereço",
		},
		webPage: {
			nomeCampo: "Web Page",
		},
		orgaoEmissor: {
			nomeCampo: "Orgão Emissor",
		},
	};
	configFormFields: { [campo: string]: any } = {
		categoria: {
			nome: "Categoria",
		},
		cpfPai: {
			nome: "CPF Pai",
		},
		dataNasc: {
			nome: "Data Nascimento",
		},
		descricaoTelefone: {
			nome: "Descrição Telefone",
		},
		grauInstrucao: {
			nome: "Grau de Instrução",
		},
		matricula: {
			nome: "Matrícula",
		},
		/*nome: {
      nome: 'Nome'
    },*/
		numero: {
			nome: "Número",
			labelCampo: "Número",
		},
		rgMae: {
			nome: "RG Mãe",
		},
		rgPai: {
			nome: "RG Pai",
		},
		bairro: {
			nome: "Bairro",
			labelCampo: "Bairro",
		},
		cep: {
			nome: "Cep",
			labelCampo: "Cep",
		},
		cidade: {
			nome: "Cidade",
			labelCampo: "Cidade",
		},
		cpf: {
			nome: "CPF",
			labelCampo: "CPF",
		},
		cnpj: {
			nome: "CNPJ",
			labelCampo: "CNPJ",
		},
		cnpjClienteSesi: {
			nome: "CNPJ Sesi Indústria",
		},
		contatoEmergencia: {
			nome: "Contato Emergência",
			labelCampo: "Contato para Emergência",
		},
		cpfMae: {
			nome: "CPF Mãe",
			labelCampo: "CPF Mãe ou Responsável",
		},
		email: {
			nome: "Email",
			labelCampo: "Email",
		},
		endereco: {
			nome: "Endereço",
			labelCampo: "Endereço",
		},
		complemento: {
			labelCampo: "Complemento",
			nome: "Endereço Complemento",
		},
		estado: {
			nome: "Estado",
			labelCampo: "Estado",
		},
		estadoCivil: {
			nome: "Estado Civil",
			labelCampo: "Estado Civil",
		},
		genero: {
			nome: "Gênero",
			labelCampo: "Gênero",
		},
		nomeMae: {
			nome: "Nome Mãe ou Responsável",
			labelCampo: "Nome Mãe ou Responsável",
		},
		nomePai: {
			nome: "Nome Pai ou Responsável",
			labelCampo: "Nome Pai ou Responsável",
		},
		pais: {
			nome: "País",
			labelCampo: "País",
		},
		profissao: {
			nome: "Profissão",
			labelCampo: "Produto Default do Personal trainer",
		},
		rg: {
			nome: "RG",
			labelCampo: "RG",
		},
		sexo: {
			nome: "Sexo",
			labelCampo: "Sexo",
		},
		telefone: {
			labelCampo: "Telefone",
			nome: "Telefone",
		},
		telefoneEmergencia: {
			nome: "Telefone Emergência",
			labelCampo: "Telefone Emergência",
		},
		webPage: {
			nome: "Web Page",
			labelCampo: "Web Page",
		},
		autenticarGoogle: {
			labelCampo: "Autenticar Google",
		},
		cref: {
			labelCampo: "CREF",
		},
		dataCadastro: {
			labelCampo: "Data Cadastro",
		},
		enderecoCorrespondencia: {
			labelCampo: "Endereço para Correspondência",
		},
		nacionalidade: {
			labelCampo: "Nacionalidade",
		},
		naturalidade: {
			labelCampo: "Naturalidade",
		},
		porcentagemComissao: {
			labelCampo: "Porcentagem Comissão",
		},
		produtoDefaultPersonal: {
			labelCampo: "Produto Default do Personal trainer",
		},
		tipoEndereco: {
			labelCampo: "Tipo Endereço",
		},
		orgaoEmissor: {
			labelCampo: "Orgão Emissor",
		},
	};

	configForms: { [campo: string]: ConfigCampos } = {};

	constructor() {}

	get updated$() {
		return this._update.asObservable();
	}

	get isVisitante(): boolean {
		return (
			this.situacaoCliente === "VISITANTE" || this.situacaoCliente === "VI"
		);
	}

	userIsOnlyClient(form): void {
		let tiposDePessoa;
		if (form.get("abaDadosBasicos")) {
			tiposDePessoa = form.get("abaDadosBasicos").get("tipoDePessoa").value;
		} else if (form.get("tipoDePessoa")) {
			tiposDePessoa = form.get("tipoDePessoa").value;
		} else {
			throw Error("The passed form does not have the basic data form controls");
		}
		if (tiposDePessoa) {
			this.isClientOnly = true;
			if (tiposDePessoa.length === 2) {
				this.isColabAndClient = true;
				// this.isClientOnly = this.isColabOnly = false;
			} else if (tiposDePessoa.filter((v) => v.sigla === "CI").length === 1) {
				// this.isClientOnly = true;
				this.isColabAndClient = this.isColabOnly = false;
			} else if (tiposDePessoa.filter((v) => v.sigla === "CO").length === 1) {
				// this.isColabOnly = true;
				this.isColabAndClient = this.isClientOnly = false;
			}
		}
	}

	verifyFormsToBeLoaded(form, formDadosBasicos?) {
		if (formDadosBasicos) {
			this.userIsOnlyClient(formDadosBasicos);
		} else {
			this.userIsOnlyClient(form);
		}
		this.configuracoesCampoCliente.forEach((v) => {
			Object.keys(this.configCampoClienteObj).forEach((key) => {
				if (
					this.configCampoClienteObj[key] &&
					this.configCampoClienteObj[key].nome === v.nome.trim()
				) {
					Object.assign(this.configCampoClienteObj[key], v);
				}
			});
		});
		this.configuracoesCampoVisitante.forEach((v) => {
			Object.keys(this.configCampoVisitanteObj).forEach((key) => {
				if (
					this.configCampoVisitanteObj[key] &&
					this.configCampoVisitanteObj[key].nome === v.nome.trim()
				) {
					Object.assign(this.configCampoVisitanteObj[key], v);
				}
			});
		});

		this.configuracoesCampoColaborador.forEach((v) => {
			Object.keys(this.configCampoColaboradorObj).forEach((key) => {
				if (
					this.configCampoColaboradorObj[key] &&
					this.configCampoColaboradorObj[key].nomeCampo === v.labelCampo.trim()
				) {
					Object.assign(this.configCampoColaboradorObj[key], v);
				}
			});
		});
		if (this.isClientOnly) {
			if (this.isVisitante) {
				Object.keys(this.configCampoVisitanteObj).forEach((key) => {
					this.verifyForms(form, key);
				});
			} else {
				Object.keys(this.configCampoClienteObj).forEach((key) => {
					this.verifyForms(form, key);
				});
			}
			return;
		}
		if (this.isColabAndClient) {
			if (this.isVisitante) {
				Object.keys(this.configCampoVisitanteObj).forEach((key) => {
					this.verifyForms(form, key);
				});
			} else {
				Object.keys(this.configCampoClienteObj).forEach((key) => {
					this.verifyForms(form, key);
				});
			}
			Object.keys(this.configCampoColaboradorObj).forEach((key) => {
				this.verifyForms(form, key);
			});
			return;
		}

		if (this.isColabOnly) {
			Object.keys(this.configCampoColaboradorObj).forEach((key) => {
				this.verifyForms(form, key);
			});
			return;
		}
	}

	verifyForm(form, key: string, formGroupKey?) {
		if (formGroupKey) {
			form = form.get(formGroupKey) as FormGroup;
		}
		if (form) {
			if (form.get(key)) {
				if (this.isClientOnly) {
					if (this.isVisitante) {
						this.verifyIndicator(this.configCampoVisitanteObj, key, form);
						this.configForms = this.configCampoVisitanteObj;
					} else {
						this.verifyIndicator(this.configCampoClienteObj, key, form);
						this.configForms = this.configCampoClienteObj;
					}
				} else if (this.isColabOnly) {
					this.verifyIndicator(this.configCampoColaboradorObj, key, form);
					this.configForms = this.configCampoColaboradorObj;
				} else if (this.isColabAndClient) {
					this.verifyIndicatorAll(key, form);
					this.configForms = this.configFormFields;
				}
			}
		}
	}

	public noWhitespaceValidator(control: FormControl) {
		const value: string = control.value || "";
		if (typeof value === "string") {
			return value.trim().length !== 0 ? null : { whitespace: true };
		}
		return null;
	}

	updated(updated: boolean) {
		this._update.next(updated);
	}

	apresentarCampo(campo: string): boolean {
		if (this.situacaoCliente) {
			const configCampos = this.isVisitante
				? this.configuracoesCampoVisitante
				: this.configuracoesCampoCliente;
			for (const config of configCampos) {
				if (config.nome === campo) {
					return config.mostrar;
				}
			}
		}
		return false;
	}

	private verifyForms(form: FormGroup, key) {
		this.verifyForm(form, key);
		this.verifyForm(form, key, "abaDadosBasicos");
		this.verifyForm(form, key, "abaAcessoCatraca");
		this.verifyForm(form, key, "abaDadosFinanceiros");
		this.verifyForm(form, key, "abaFamiliares");
		this.verifyForm(form, key, "abaAcesso");
		this.verifyForm(form, key, "abaVinculos");
		if (!this.isClientOnly) {
			this.verifyForm(form, key, "abaRh");
		}
	}

	private verifyIndicator(
		config: { [campo: string]: ConfigCampos },
		key: string,
		form: FormGroup
	) {
		if (config.hasOwnProperty(key) && config[key]) {
			config[key].indicator = undefined;
			if (form.contains(key) || form.get(key)) {
				form.get(key).clearValidators();
				if (config[key].obrigatorio || config[key].campoObrigatorio) {
					config[key].indicator = "*";
					form
						.get(key)
						.setValidators([Validators.required, this.noWhitespaceValidator]);
				}
				form.get(key).updateValueAndValidity();
			}
			if (config[key].pendente) {
				config[key].indicator = "**";
			}
		}
	}

	private verifyIndicatorAll(key: string, form: FormGroup) {
		if (
			this.configFormFields.hasOwnProperty(key) &&
			this.configFormFields[key]
		) {
			let obrigatorio;
			let pendente;
			this.configFormFields[key].indicator = undefined;
			if (this.situacaoCliente === "VI") {
				if (
					this.configCampoVisitanteObj[key] &&
					this.configCampoVisitanteObj[key].visitante
				) {
					obrigatorio =
						this.configCampoVisitanteObj[key].obrigatorio ||
						this.configCampoColaboradorObj[key].campoObrigatorio;

					pendente =
						(this.configCampoVisitanteObj[key] &&
							this.configCampoVisitanteObj[key].pendente) ||
						(this.configCampoColaboradorObj[key] &&
							this.configCampoColaboradorObj[key].pendente);
				}
			} else {
				obrigatorio =
					(this.configCampoClienteObj[key] &&
						this.configCampoClienteObj[key].obrigatorio) ||
					(this.configCampoColaboradorObj[key] &&
						this.configCampoColaboradorObj[key].campoObrigatorio);

				pendente =
					(this.configCampoClienteObj[key] &&
						this.configCampoClienteObj[key].pendente) ||
					(this.configCampoColaboradorObj[key] &&
						this.configCampoColaboradorObj[key].pendente);
			}
			if (form.contains(key) || form.get(key)) {
				form.get(key).clearValidators();
				if (obrigatorio) {
					this.configFormFields[key].indicator = "*";
					form
						.get(key)
						.setValidators([Validators.required, this.noWhitespaceValidator]);
				}
				form.get(key).updateValueAndValidity();
			}
			if (pendente) {
				this.configFormFields[key].indicator = "**";
			}
		}
	}
}
