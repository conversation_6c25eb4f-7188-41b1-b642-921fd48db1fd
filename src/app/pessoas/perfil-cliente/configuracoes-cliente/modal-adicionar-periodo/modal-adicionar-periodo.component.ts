import { Component } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-adicionar-periodo",
	templateUrl: "./modal-adicionar-periodo.component.html",
	styleUrls: ["./modal-adicionar-periodo.component.scss"],
})
export class ModalAdicionarPeriodoComponent {
	form: FormGroup = new FormGroup({
		dtInicio: new FormControl(new Date()),
		dtFim: new FormControl(new Date()),
		substituto: new FormControl(),
		motivo: new FormControl(),
	});

	colaborators = [];

	colaboradoresUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/colaboradores"
	);

	constructor(
		private activeModal: NgbActiveModal,
		private restService: RestService
	) {}

	paramBuilder = (nome) => {
		return {
			filters: JSON.stringify({
				nome: nome ? nome : "",
				tipoColaborador: "",
				validarTipo: false,
			}),
		};
	};

	responseParser = (result) => {
		if (result && result.content && Array.isArray(result.content)) {
			const formattedResult = result.content.map((r) => r);
			this.colaborators = formattedResult;
			return formattedResult;
		}
		return [];
	};

	add(): void {
		this.activeModal.close(this.form.value);
	}
}
