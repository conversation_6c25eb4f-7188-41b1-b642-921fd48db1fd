import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	Renderer2,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { AcessoCatracaService } from "pessoa-ms-api";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";
import { FormGroupAcessoCatraca } from "../forms/acesso-catraca-form";

@Component({
	selector: "pacto-pacto",
	templateUrl: "./pacto.component.html",
	styleUrls: ["./pacto.component.scss"],
})
export class PactoComponent implements OnInit, AfterViewInit {
	@Output() saveConfig = new EventEmitter<void>();
	@Input() form: FormGroup = FormGroupAcessoCatraca;
	@Input() permiteEditar: boolean;
	DISABLED = "disabled";
	APP_DISABLED = "app-disabled";
	TAB_INDEX = "tabindex";
	TAG_ANCHOR = "a";

	constructor(
		private eleRef: ElementRef,
		private renderer: Renderer2,
		private cd: ChangeDetectorRef,
		private acessoCatracaService: AcessoCatracaService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		public configCliFormValidation: ConfigCliFormValidationService
	) {}

	ngOnInit() {}

	ngAfterViewInit(): void {
		this.disableElement(this.eleRef.nativeElement);
	}

	private disableElement(element: any) {
		if (this.permiteEditar) {
			if (element.hasAttribute(this.APP_DISABLED)) {
				if (element.getAttribute("disabled") !== "") {
					element.removeAttribute(this.DISABLED);
				}
				element.removeAttribute(this.APP_DISABLED);
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					element.removeAttribute(this.TAB_INDEX);
				}
			}
		} else {
			if (!element.hasAttribute(this.DISABLED)) {
				this.renderer.setAttribute(element, this.APP_DISABLED, "");
				this.renderer.setAttribute(element, this.DISABLED, "true");

				// disabling anchor tab keyboard event
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					this.renderer.setAttribute(element, this.TAB_INDEX, "-1");
				}
			}
		}
		if (element.children) {
			for (let ele of element.children) {
				this.disableElement(ele);
			}
		}
	}
}
