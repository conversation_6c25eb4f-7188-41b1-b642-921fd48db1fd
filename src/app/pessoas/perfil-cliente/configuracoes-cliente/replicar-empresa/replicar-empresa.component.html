<pacto-cat-card-plain>
	<div class="d-flex gap-1">
		<pacto-cat-card-plain class="full d-flex justify-content-center">
			<p class="text-align-center">
				<span class="title">00</span>
				<span class="subtitle" i18n="">Unidades</span>
			</p>
		</pacto-cat-card-plain>
		<pacto-cat-card-plain class="full d-flex justify-content-center">
			<p class="text-align-center">
				<span class="title color-green-pri">00</span>
				<span class="subtitle" i18n="">Replicadas</span>
			</p>
		</pacto-cat-card-plain>
		<pacto-cat-card-plain class="full d-flex justify-content-center">
			<p class="text-align-center">
				<span class="title color-red-2">00</span>
				<span class="subtitle" i18n="">Não replicadas</span>
			</p>
		</pacto-cat-card-plain>
	</div>

	<hr />

	<pacto-relatorio
		#docTableData
		(cellClick)="handleClick($event)"
		[customActions]="customAction"
		[enableZebraStyle]="true"
		[showShare]="false"
		[table]="table"
		actionTitulo="Ações"
		i18n-actionTitulo="@@label-acoes"></pacto-relatorio>
</pacto-cat-card-plain>

<ng-template #customAction>
	<div class="d-flex justify-content-end gap-1">
		<pacto-cat-button
			i18n-label="@@aba-replicar-empresa:replicar-selecionadas"
			label="Replicar todas selecionadas"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			i18n-label="@@aba-replicar-empresa:replicar-todas"
			label="Replicar todas"
			size="LARGE"></pacto-cat-button>
	</div>
</ng-template>
<ng-template #vinculoAction let-item="item">
	<pacto-cat-button
		*ngIf="item.temVinculo; else noVinculo"
		i18n-label="@@aba-replicar-empresa:retirar-vinculo"
		label="Retirar vinculo"
		size="LARGE"
		type="OUTLINE"></pacto-cat-button>
	<ng-template #noVinculo>
		<pacto-cat-button
			i18n-label="@@aba-replicar-empresa:aplicar-vinculo"
			label="Aplicar vinculo"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
	</ng-template>
</ng-template>

<ng-template #selectAll>
	<input
		[formControl]="form.get('selectAll')"
		aria-label="select All"
		type="checkbox" />
</ng-template>

<ng-template #selectCompany let-item="item">
	<input
		[formControl]="empresas.controls[item.id]"
		aria-label="select company"
		type="checkbox" />
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n-label="@@aba-replicar-empresa:delete-doc"
		xingling="delete-tooltip">
		Deletar documento
	</span>
	<span
		i18n-label="@@aba-replicar-empresa:download-doc"
		xingling="download-tooltip">
		Fazer download do documento
	</span>
	<span
		i18n-label="@@aba-replicar-empresa:delete-period"
		xingling="delete-period-tooltip">
		Deletar período
	</span>
	<span
		i18n-label="@@aba-replicar-empresa:edit-period"
		xingling="edit-period-tooltip">
		Editar item
	</span>
</pacto-traducoes-xingling>

<ng-template #nomeUnidade>
	<span i18n="@@aba-replicar-empresa:nome-unidade">Nome da unidade</span>
</ng-template>
<ng-template #chave>
	<span i18n="@@aba-replicar-empresa:chave">Chave</span>
</ng-template>
<ng-template #mensagem>
	<span i18n="@@aba-replicar-empresa:messagem">Mensagem</span>
</ng-template>
<ng-template #status>
	<span i18n="@@aba-replicar-empresa:status">Status</span>
</ng-template>
<ng-template #acoes>
	<span i18n="@@label-acoes">Ações</span>
</ng-template>
