import {
	AfterViewInit,
	Component,
	HostBinding,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { debounceTime } from "rxjs/operators";

import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-replicar-empresa",
	templateUrl: "./replicar-empresa.component.html",
	styleUrls: ["./replicar-empresa.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ReplicarEmpresaComponent implements OnInit, AfterViewInit {
	table: PactoDataGridConfig;

	@HostBinding("class.pessoa-config-replicar-empresa")
	styleEncapsulation = true;

	@ViewChild("nomeUnidade", { static: true })
	nomeUnidade: TemplateRef<any>;

	@ViewChild("chave", { static: true })
	chave: TemplateRef<any>;

	@ViewChild("mensagem", { static: true })
	mensagem: TemplateRef<any>;

	@ViewChild("status", { static: true })
	status: TemplateRef<any>;

	@ViewChild("acoes", { static: true })
	acoes: TemplateRef<any>;

	@ViewChild("selectAll", { static: true })
	selectAll: TemplateRef<any>;

	@ViewChild("selectCompany", { static: true })
	selectCompany: TemplateRef<any>;

	@ViewChild("vinculoAction", { static: true })
	vinculoAction: TemplateRef<any>;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	form: FormGroup = new FormGroup({
		empresas: new FormArray([]),
		selectAll: new FormControl(),
	});
	selectedCompanies: Map<number, boolean> = new Map();
	isSingleChange = false;

	constructor() {}

	get empresas(): FormArray {
		return this.form.get("empresas") as FormArray;
	}

	ngOnInit() {
		this.form.get("selectAll").valueChanges.subscribe((v) => {
			if (this.isSingleChange) {
				return;
			}

			console.log("heeey");

			for (let control of this.empresas.controls) {
				control.patchValue(v);
			}
		});

		this.form
			.get("empresas")
			.valueChanges.pipe(debounceTime(100))
			.subscribe((_empresas) => {
				const allAreChecked = (_empresas as Array<boolean>).every((v, i) => {
					this.selectedCompanies.set(i, v);
					return v === true;
				});

				if (allAreChecked !== true) {
					this.isSingleChange = true;
					this.form.get("selectAll").patchValue(false);
				}
				this.isSingleChange = false;
			});
	}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			quickSearch: true,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			// valueRowCheck: 'selection',
			dataAdapterFn: () => {
				const data = Array.from({ length: 3 }, (_, index) => ({
					id: index,
					descricaoDocumento: "lorem ipsum dolor sit",
					nomeArquivo: "lorem ipsum dolor sit",
					usuario: "lorem ipsum dolor sit",
					dataLancamento: "20/02/2022 07:10:15",
					temVinculo: index === 1 ? false : true,
				}));

				data.forEach((d) => {
					this.selectedCompanies.set(d.id, false);
					this.empresas.push(new FormControl());
				});

				return {
					content: data,
				};
			},
			columns: [
				{
					nome: "selection",
					titulo: this.selectAll,
					visible: true,
					ordenavel: false,
					celula: this.selectCompany,
				},
				{
					nome: "descricaoDocumento",
					titulo: this.nomeUnidade,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeArquivo",
					titulo: this.chave,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "usuario",
					titulo: this.mensagem,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataLancamento",
					titulo: this.status,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "",
					titulo: this.acoes,
					visible: true,
					ordenavel: false,
					celula: this.vinculoAction,
					styleClass: "right",
				},
			],
			actions: [],
		});
	}

	handleClick(event): void {
		console.log(event);
	}
}
