import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostBinding,
	Input,
	OnInit,
	Output,
	Renderer2,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormGroupAcessoCatraca } from "../forms/acesso-catraca-form";
import { AcessoCatracaService } from "pessoa-ms-api";
import { catchError } from "rxjs/operators";
import { of } from "rxjs";
import { BasePagination, PaginationInterface } from "../base/base-pagination";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";

@Component({
	selector: "pacto-acesso-catraca",
	templateUrl: "./acesso-catraca.component.html",
	styleUrls: ["./acesso-catraca.component.scss"],
})
export class AcessoCatracaComponent
	extends BasePagination
	implements OnInit, AfterViewInit
{
	@HostBinding("class.pessoa-config-acesso-catraca") styleEncapsulation = true;
	@Output() saveConfig = new EventEmitter<void>();

	@Input() form: FormGroup = FormGroupAcessoCatraca;
	@Input() permiteEditar: boolean;
	DISABLED = "disabled";
	APP_DISABLED = "app-disabled";
	TAB_INDEX = "tabindex";
	TAG_ANCHOR = "a";

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	@ViewChild("columnDescricaoVeiculo", { static: true })
	columnDescricaoVeiculo: TemplateRef<any>;

	@ViewChild("columnPlaca", { static: true })
	columnPlaca: TemplateRef<any>;

	table: PactoDataGridConfig;

	categories$ = this.acessoCatracaService
		.getCategorias()
		.pipe(catchError(() => of([])));
	permissaoEditarMatricula: boolean = false;
	itensPerPage = [
		{ id: 3, label: "3" },
		{ id: 6, label: "6" },
		{ id: 9, label: "9" },
	];
	newLineTitle = "Adicionar veículo";

	// por solicitação do Moisés a matrícula ficará desabilitada por enquanto
	// assim que voltar a edição esse control pode ser substituído no html por form.get('matricula')
	controlMatriculaTemp = new FormControl();

	constructor(
		private eleRef: ElementRef,
		private renderer: Renderer2,
		private cd: ChangeDetectorRef,
		private acessoCatracaService: AcessoCatracaService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		public configCliFormValidation: ConfigCliFormValidationService
	) {
		super();
		this.pageSize = 3;
		this.currentPage = 1;
	}

	ngOnInit(): void {
		this.data = {
			content: this.form.get("acessoVeicular").value || [],
			// content: Array.from({length: 10}, (_, i) => ({
			//   placa: `placa ${i}`,
			//   descricaoVeiculo: `Descricao`
			// }))
		};
		this.getPermissaoEditarMatricula();
		this.bloquearTemplates();
	}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			rowClick: false,
			dataAdapterFn: (serverData) => {
				return this.data;
			},
			formGroup: new FormGroup({
				placa: new FormControl(null, Validators.required),
				descricaoVeiculo: new FormControl(null, Validators.required),
			}),
			columns: [
				{
					nome: "placa",
					titulo: this.columnPlaca,
					visible: true,
					ordenavel: true,
					inputType: "text",
					editable: true,
					maxlength: 10,
				},
				{
					nome: "descricaoVeiculo",
					titulo: this.columnDescricaoVeiculo,
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "text",
					maxlength: 50,
				},
			],
		});
		this.form.get("matricula").disable();
		this.disableElement(this.eleRef.nativeElement);
		this.bloquearTemplates();
		this.cd.detectChanges();
	}

	confirm(event): void {
		if (!event.row.codigo && !event.row.edit) {
			this.addItemToData(event.row);
		} else {
			let familiarIndex = this.data.fullArray.findIndex(
				(v) => v.codigo === event.row.codigo
			);
			if (familiarIndex !== -1) {
				this.data.fullArray[familiarIndex] = event.row;
			}
		}
	}

	itemAddedEvent(): void {
		this.tableData.reloadData();
		this.form.get("acessoVeicular").patchValue(this.data.fullArray);
	}

	delete(event): void {
		const rowIsEmpty = Object.entries(event.row).every(([key, value]: any) => {
			// ignore the edit property
			if (key === "edit" && value === true) {
				return true;
			}

			return value === "";
		});

		// delete empty row
		if (rowIsEmpty) {
			event.data[event.index].edit = false;
			event.data.splice(event.index, 1);
			return;
		}

		// call API to delete item;
		if (!event.row["edit"]) {
			this.deleteItemFromData(event.index);
		}
	}

	itemDeletedEvent(): void {
		this.tableData.reloadData();
		this.form.get("acessoVeicular").patchValue(this.data.fullArray);
	}

	pageChangedEvent(): void {
		this.tableData.reloadData();
	}

	sizeChangedEvent(): void {
		this.tableData.reloadData();
	}

	getPermissaoEditarMatricula() {
		this.controlMatriculaTemp.setValue(this.form.get("matricula").value);
		this.controlMatriculaTemp.disable();
		this.form.get("matricula").disable();
		/*
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				this.sessionService.codigoEmpresa,
				'AlterarMatricula',
				'2.59 - Alterar número de matrícula do cliente'
			)
			.subscribe(
				responseAutorizacao => {
					this.permissaoEditarMatricula = true;
					if(this.form.get('matricula').disabled){
						this.form.get('matricula').enable()
					}
					this.cd.detectChanges();
				},
				error => {
					this.permissaoEditarMatricula = false;
					this.form.get('matricula').disable();
				}
			);*/
	}

	private disableElement(element: any) {
		if (this.permiteEditar) {
			if (element.hasAttribute(this.APP_DISABLED)) {
				if (element.getAttribute("disabled") !== "") {
					element.removeAttribute(this.DISABLED);
				}
				element.removeAttribute(this.APP_DISABLED);
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					element.removeAttribute(this.TAB_INDEX);
				}
			}
		} else {
			if (!element.hasAttribute(this.DISABLED)) {
				this.renderer.setAttribute(element, this.APP_DISABLED, "");
				this.renderer.setAttribute(element, this.DISABLED, "true");

				// disabling anchor tab keyboard event
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					this.renderer.setAttribute(element, this.TAB_INDEX, "-1");
				}
			}
		}
		if (element.children) {
			for (let ele of element.children) {
				this.disableElement(ele);
			}
		}
		this.bloquearTemplates();
	}

	bloquearTemplates() {
		this.form.get("templateDigital").disable();
		this.form.get("templateFacial").disable();
		this.form.get("codigoAcesso").disable();
	}
}
