<pacto-cat-card-plain>
	<div class="row">
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				[control]="form.get('codigoAcesso')"
				[readonly]="true"
				i18n-label="@@acesso-catraca:codigo-acesso"
				label="Código de acesso"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				[control]="form.get('codigoAcessoAlternativo')"
				i18n-label="@@acesso-catraca:codigo-acesso-alternativo"
				label="Código de acesso alternativo"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				[control]="form.get('templateDigital')"
				[readonly]="true"
				i18n-label="@@acesso-catraca:template-digital"
				label="Template digital"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				[control]="form.get('templateFacial')"
				[readonly]="true"
				i18n-label="@@acesso-catraca:template-facial"
				label="Template facial"></pacto-cat-form-input>
		</div>
	</div>

	<hr />

	<h2 class="h6" i18n="@@acesso-catraca:acesso-veicular">Acesso veícular</h2>
	<pacto-cat-table-editable
		#tableData
		(confirm)="confirm($event)"
		(delete)="delete($event)"
		(pageChangeEvent)="changePage($event)"
		(pageSizeChange)="changeSize($event)"
		[enableZebraStyle]="true"
		[isEditable]="permiteEditar"
		[itensPerPage]="itensPerPage"
		[newLineTitle]="newLineTitle"
		[showAddRow]="permiteEditar"
		[showShare]="false"
		[table]="table"
		actionTitle="Ações"
		emptyStateMessage="Nenhum veículo cadastrado."
		i18n-actionTitle="@@label-acoes"
		i18n-emptyStateMessage="@@acesso-catraca:acesso-veicular-empty"
		idSuffix="table-familiar"></pacto-cat-table-editable>

	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="saveConfig.emit()"
			[hidden]="!configCliFormValidation.isClientOnly"
			i18n-label="@@config-pessoa:btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<ng-template #columnPlaca>
	<span i18n="@@acesso-catraca:placa-veiculo">Placa do veículo</span>
</ng-template>
<ng-template #columnDescricaoVeiculo>
	<span i18n="@@acesso-catraca:descricao-veiculo">Descrição do veículo</span>
</ng-template>
