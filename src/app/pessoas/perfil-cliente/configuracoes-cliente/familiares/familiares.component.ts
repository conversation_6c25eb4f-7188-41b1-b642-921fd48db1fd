import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	Renderer2,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { Recurso } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { FamiliaresService } from "pessoa-ms-api";
import { BehaviorSubject } from "rxjs";
import { PerfilRecursoPermissoTipo } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { BasePagination } from "../base/base-pagination";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";

@Component({
	selector: "pacto-familiares",
	templateUrl: "./familiares.component.html",
	styleUrls: ["./familiares.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FamiliaresComponent
	extends BasePagination
	implements OnInit, AfterViewInit
{
	@Input() form: FormGroup;
	@Input() permiteEditar: boolean;
	DISABLED = "disabled";
	APP_DISABLED = "app-disabled";
	TAB_INDEX = "tabindex";
	TAG_ANCHOR = "a";

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	@ViewChild("columnFamiliar", { static: true })
	columnFamiliar: TemplateRef<any>;

	@ViewChild("columnGrauParentesco", { static: true })
	columnGrauParentesco: TemplateRef<any>;

	@ViewChild("columnCodigoAcesso", { static: true })
	columnCodigoAcesso: TemplateRef<any>;

	@ViewChild("columnObservacoes", { static: true })
	columnObservacoes: TemplateRef<any>;

	@ViewChild("traducao", { static: true })
	private traducao: TraducoesXinglingComponent;

	table: PactoDataGridConfig;
	parentesco = [];
	searchTerm$ = new BehaviorSubject<string>("");

	@Output() saveConfig = new EventEmitter<void>();
	@Input() recursoFamiliar: Recurso = undefined;
	podeAdicionarFamiliar;
	newLineTitle = "Adicionar familiares";

	constructor(
		private eleRef: ElementRef,
		private renderer: Renderer2,
		private cd: ChangeDetectorRef,
		private restService: RestService,
		private familiaresService: FamiliaresService,
		public configCliFormValidation: ConfigCliFormValidationService,
		private notification: SnotifyService,
		private sessionService: SessionService
	) {
		super();
		this.pageSize = 10;
		this.currentPage = 1;
	}

	ngOnInit(): void {
		this.podeAdicionarFamiliar =
			this.recursoFamiliar &&
			this.recursoFamiliar.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		this.data = {
			content: this.form.get("familiares").value || [],
		};
	}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			rowClick: false,
			dataAdapterFn: (serverData) => {
				return this.data;
			},
			formGroup: new FormGroup({
				familiar: new FormControl(null, Validators.required),
				parentesco: new FormControl(null, Validators.required),
				identificador: new FormControl(""),
			}),
			beforeConfirm: (row: any, form: any, data: any, indexRow: any) =>
				this.beforeConfirm(row, form, data, indexRow),
			showEdit: (row) => {
				return (
					!row.codigo ||
					(this.recursoFamiliar &&
						this.recursoFamiliar.tipoPermissoes &&
						!!this.recursoFamiliar.tipoPermissoes.find(
							(tp) =>
								tp === PerfilRecursoPermissoTipo.EDITAR ||
								tp === PerfilRecursoPermissoTipo.TOTAL ||
								tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
								tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
						))
				);
			},
			showDelete: (row) => {
				return (
					!row.codigo ||
					(this.recursoFamiliar &&
						this.recursoFamiliar.tipoPermissoes &&
						!!this.recursoFamiliar.tipoPermissoes.find(
							(tp) =>
								tp === PerfilRecursoPermissoTipo.EXCLUIR ||
								tp === PerfilRecursoPermissoTipo.TOTAL ||
								tp === PerfilRecursoPermissoTipo.EDITAR
						))
				);
			},
			columns: [
				{
					nome: "familiar",
					titulo: this.columnFamiliar,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "familiar",
					labelSelectKey: "nome",
					showAddSelectBtn: false,
					showSelectFilter: true,
					infiniteScrollEnabled: true,
					infiniteScrollElementsSize: 50,
					selectParamBuilder: (term) => {
						this.searchTerm$.next(term);
						return {
							filters: JSON.stringify({
								nome: term,
							}),
						};
					},
					selectOptionChange: (option, form, row) =>
						this.onFamiliarChange(option, form, row),
					endpointUrl: this.restService.buildFullUrlPessoaMs(
						"alunoColaboradorUsuario/familiares/" +
							this.sessionService.empresaId
					),
					width: "19rem",
				},
				{
					nome: "parentesco",
					titulo: this.columnGrauParentesco,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					showAddSelectBtn: false,
					showSelectFilter: false,
					endpointUrl: this.restService.buildFullUrlPessoaMs(
						"alunoColaboradorUsuario/grausParentesco"
					),
				},
				{
					nome: "familiar",
					titulo: this.columnCodigoAcesso,
					visible: true,
					ordenavel: false,
					valueTransform: (value, row) => {
						if (typeof value === "object") {
							return value.codAcesso;
						}
						if (row && row.familiar) {
							return row.familiar.codAcesso;
						}
					},
				},
				{
					nome: "identificador",
					titulo: this.columnObservacoes,
					visible: true,
					ordenavel: false,
					inputType: "text",
					editable: true,
					maxlength: 5,
				},
			],
		});

		this.disableElement(this.eleRef.nativeElement);
		this.cd.detectChanges();
	}

	private onFamiliarChange(option, form, row) {
		const codAcesso = option.codAcesso;
		if (row) {
			if (typeof row.familiar === "string" || row.familiar === "") {
				row.familiar = {} as any;
			}
			row.familiar.codAcesso = codAcesso;
		}
	}

	confirm(event): void {
		const obj = {
			codigo:
				event.row.codigo && event.row.codigo > 0 ? event.row.codigo : null,
			familiar: {
				familiar: event.row.familiar.familiar,
				nome: event.row.familiar.nome,
				codAcesso: event.row.familiar.codAcesso,
			},
			nome: event.row.familiar.nome,
			codAcesso: event.row.familiar.codAcesso,
			identificador: event.row.identificador,
			parentesco: {
				codigo: event.row.parentesco.codigo,
				descricao: event.row.parentesco.descricao,
			},
		};
		if (!obj.codigo && !event.row.edit) {
			this.addItemToData(obj);
		} else {
			let familiarIndex = this.data.fullArray.findIndex(
				(v) => v.codigo === obj.codigo
			);
			if (familiarIndex !== -1) {
				this.data.fullArray[familiarIndex] = obj;
			}
		}
	}

	itemAddedEvent(): void {
		this.tableData.reloadData();
		this.form.get("familiares").patchValue(this.data.fullArray);
	}

	delete(event): void {
		const rowIsEmpty = Object.entries(event.row).every(([key, value]: any) => {
			// ignore the edit property
			if (key === "edit" && value === true) {
				return true;
			}

			return value === "";
		});

		// delete empty row
		if (rowIsEmpty) {
			event.data[event.index].edit = false;
			event.data.splice(event.index, 1);
			return;
		}

		// call API to delete item;
		if (!event.row.edit) {
			this.deleteItemFromData(event.index);
		}
	}

	itemDeletedEvent(): void {
		this.tableData.reloadData();
		this.form.get("familiares").patchValue(this.data.fullArray);
	}

	pageChangedEvent(): void {
		this.tableData.data = this.data;
		this.tableData.reloadData();
	}

	sizeChangedEvent(): void {
		this.tableData.data = this.data;
		this.tableData.reloadData();
	}

	private beforeConfirm(row: any, form: any, data: any, indexRow: any) {
		if (form.get("familiar").invalid) {
			this.notification.error(this.traducao.getLabel("familiar-obrigatorio"), {
				timeout: 2000,
				bodyMaxLength: 300,
			});
			return false;
		}
		if (data) {
			if (
				data.find(
					(v, index) =>
						(v.familiar === form.get("familiar").value.familiar ||
							v.familiar.familiar === form.get("familiar").value.familiar) &&
						index != indexRow
				)
			) {
				this.notification.error(this.traducao.getLabel("familiar-duplicado"), {
					timeout: 2000,
					bodyMaxLength: 300,
				});
				return false;
			}
		}
		if (form.get("parentesco").invalid) {
			this.notification.error(
				this.traducao.getLabel("parentesco-obrigatorio"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
		if (!row.familiar.codAcesso) {
			this.notification.error(
				this.traducao.getLabel("familiar-sem-cod-acesso"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
		return true;
	}

	onEdit(event: any) {}

	private disableElement(element: any) {
		if (this.permiteEditar) {
			if (element.hasAttribute(this.APP_DISABLED)) {
				if (element.getAttribute("disabled") !== "") {
					element.removeAttribute(this.DISABLED);
				}
				element.removeAttribute(this.APP_DISABLED);
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					element.removeAttribute(this.TAB_INDEX);
				}
			}
		} else {
			if (!element.hasAttribute(this.DISABLED)) {
				this.renderer.setAttribute(element, this.APP_DISABLED, "");
				this.renderer.setAttribute(element, this.DISABLED, "true");

				// disabling anchor tab keyboard event
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					this.renderer.setAttribute(element, this.TAB_INDEX, "-1");
				}
			}
		}
		if (element.children) {
			for (let ele of element.children) {
				this.disableElement(ele);
			}
		}
	}
}
