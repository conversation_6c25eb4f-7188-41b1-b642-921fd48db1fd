@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts";

.pessoa-config-dados-basicos {
	pacto-cat-card-plain {
		margin-top: 1.4rem;
	}

	pacto-cat-form-input {
		margin: 15px 0 !important;
	}

	pacto-cat-form-input,
	pacto-cat-form-multi-select-filter,
	pacto-cat-form-datepicker,
	pacto-cat-form-select-filter {
		.nome {
			color: $typeDefaultText;

			span {
				@extend .pct-title4;
			}
		}
	}

	pacto-cat-form-select {
		.pacto-label {
			color: $typeDefaultText;

			span {
				@extend .pct-title4;
			}
		}
	}

	pacto-datepicker {
		display: block;
		margin: 15px 0;
	}

	pacto-datepicker {
		.form-group.edit-datepicker {
			.form-control-sm {
				line-height: 33px;
			}

			.width-datepicker {
				width: 100%;
			}

			.wrapper {
				width: 100%;

				.toggle-icon {
					top: 13px !important;
				}
			}

			label {
				color: #a6aab1;
			}
		}
	}

	.link {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.link {
		all: unset;
		color: #0380e3;
		text-decoration: underline;
		font-size: 0.87rem;
		font-weight: 600;
		cursor: pointer;
	}

	.top-button {
		margin-top: 1.5rem;
	}

	.photo-buttons {
		margin-top: 1.5rem;
		margin-bottom: 1.5rem;
		gap: 1rem;
	}
}

.separator {
	width: 100%;
	height: 0px;
	border: 1px solid #c7c9cc;
}

.sesi-title {
	color: $typeDefaultTitle;
	@extend .pct-title4;
}

.row.col-12 {
	padding: 0px;
}

.actionable {
	:hover {
		cursor: pointer;
	}
}
