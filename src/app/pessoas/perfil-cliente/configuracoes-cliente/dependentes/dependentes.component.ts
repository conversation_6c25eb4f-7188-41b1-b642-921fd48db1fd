import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	Renderer2,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { BasePagination } from "../base/base-pagination";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";

@Component({
	selector: "pacto-dependentes",
	templateUrl: "./dependentes.component.html",
	styleUrls: ["./dependentes.component.scss"],
})
export class DependentesComponent
	extends BasePagination
	implements OnInit, AfterViewInit
{
	@Input() form: FormGroup;
	@Input() permiteEditar: boolean;
	DISABLED = "disabled";
	APP_DISABLED = "app-disabled";
	TAB_INDEX = "tabindex";
	TAG_ANCHOR = "a";

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	@ViewChild("columnCliente", { static: false })
	columnCliente: TemplateRef<any>;

	@ViewChild("columnGrauParentesco", { static: false })
	columnGrauParentesco: TemplateRef<any>;
	table: PactoDataGridConfig;
	parentesco = [];
	@Output() saveConfig = new EventEmitter<void>();
	@ViewChild("traducao", { static: true })
	private traducao: TraducoesXinglingComponent;

	constructor(
		private eleRef: ElementRef,
		private renderer: Renderer2,
		private cd: ChangeDetectorRef,
		private restService: RestService,
		public configCliFormValidation: ConfigCliFormValidationService,
		private notification: SnotifyService,
		private sessionService: SessionService
	) {
		super();
		this.pageSize = 10;
		this.currentPage = 1;
	}

	ngOnInit(): void {
		this.data = {
			content: this.form.get("dependentes").value || [],
		};
		this.data.content.forEach((d) => {
			if (d.cliente) {
				d.cliente.codigoCliente = d.cliente.codigo;
				if (d.cliente.pessoaDTO) {
					d.cliente.nome = d.cliente.pessoaDTO.nome;
				}
			}
		});
	}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.data;
			},
			formGroup: new FormGroup({
				codigoCliente: new FormControl(),
				cliente: new FormControl(),
				parentesco: new FormControl(),
				dependente: new FormControl(),
			}),
			beforeConfirm: (row: any, form: any, data: any, indexRow: any) =>
				this.beforeConfirm(row, form, data, indexRow),
			showEdit: (row) => {
				return (
					(row.clienteTitular && row.codigo !== row.clienteTitular.codigo) ||
					!row.clienteTitular
				);
			},
			showDelete: (row) => {
				return (
					(row.clienteTitular && row.codigo !== row.clienteTitular.codigo) ||
					!row.clienteTitular
				);
			},
			columns: [
				{
					nome: "cliente",
					titulo: this.columnCliente,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigoCliente",
					labelSelectKey: "nome",
					objectAttrLabelName: "nome",
					showAddSelectBtn: false,
					showSelectFilter: true,
					infiniteScrollEnabled: true,
					infiniteScrollElementsSize: 30,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
							}),
						};
					},
					selectOptionChange: (option, form, row) =>
						this.onDepChange(option, form, row),
					valueTransform: (value, row) => {
						if (value && value.pessoaDTO) {
							return value.pessoaDTO.nome;
						}
						return value;
					},
					endpointUrl: this.restService.buildFullUrlAdmCore("clientes/simples"),
				},
				{
					nome: "parentesco",
					titulo: this.columnGrauParentesco,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					showAddSelectBtn: false,
					showSelectFilter: false,
					endpointUrl: this.restService.buildFullUrlPessoaMs(
						"alunoColaboradorUsuario/grausParentesco"
					),
				},
			],
		});

		this.disableElement(this.eleRef.nativeElement);
		this.cd.detectChanges();
	}

	confirm(event): void {
		let obj = event.row;
		if (!obj.codigo) {
			obj = {
				codigo:
					event.row.codigo && event.row.codigo > 0 ? event.row.codigo : null,
				cliente: {
					codigo: event.row.cliente.codigoCliente,
					nome: event.row.cliente.nome,
				},
				parentesco: {
					codigo: event.row.parentesco.codigo,
					descricao: event.row.parentesco.descricao,
				},
			};
		} else {
			if (!obj.cliente.codigo) {
				obj.cliente.codigo = event.row.cliente.codigoCliente;
			}
		}
		if (!obj.codigo && !event.row.edit) {
			this.addItemToData(obj);
		} else {
			let dependenteIndex = this.data.fullArray.findIndex(
				(v) => v.codigo === obj.codigo
			);
			if (dependenteIndex !== -1) {
				this.data.fullArray[dependenteIndex] = obj;
			}
		}
	}

	itemAddedEvent(): void {
		this.tableData.reloadData();
		this.form.get("dependentes").patchValue(this.data.fullArray);
	}

	delete(event): void {
		const rowIsEmpty = Object.entries(event.row).every(([key, value]: any) => {
			// ignore the edit property
			if (key === "edit" && value === true) {
				return true;
			}

			return value === "";
		});

		// delete empty row
		if (rowIsEmpty) {
			event.data[event.index].edit = false;
			event.data.splice(event.index, 1);
			return;
		}

		// call API to delete item;
		if (!event.row.edit) {
			this.deleteItemFromData(event.index);
		}
	}

	itemDeletedEvent(): void {
		this.tableData.reloadData();
		this.form.get("dependentes").patchValue(this.data.fullArray);
	}

	pageChangedEvent(): void {
		this.tableData.data = this.data;
		this.tableData.reloadData();
	}

	sizeChangedEvent(): void {
		this.tableData.data = this.data;
		this.tableData.reloadData();
	}

	private onDepChange(option, form, row) {
		try {
			if (row) {
				if (typeof row.dependente === "string" || row.dependente === "") {
					// row.dependente = {} as any;
				}
			}
		} catch (e) {
			console.log(e);
		}
	}

	private beforeConfirm(row: any, form: any, data: any, indexRow: any) {
		if (form.get("cliente").invalid) {
			this.notification.error(
				this.traducao.getLabel("dependente-obrigatorio"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
		if (data) {
			const duplicatedIndex = data.findIndex((v, index) => {
				if (index !== indexRow) {
					if (v.codigoCliente) {
						if (v.codigoCliente === form.get("cliente").value.codigoCliente) {
							return true;
						}
						if (v.codigoCliente === form.get("cliente").value.codigo) {
							return true;
						}
					}
					if (v.cliente && v.cliente.codigo) {
						if (v.cliente.codigo === form.get("cliente").value.codigoCliente) {
							return true;
						}
						if (v.cliente.codigo === form.get("cliente").value.codigo) {
							return true;
						}
					}
				}
				return false;
			});
			if (duplicatedIndex !== -1) {
				this.notification.error(
					this.traducao.getLabel("dependente-duplicado"),
					{
						timeout: 2000,
						bodyMaxLength: 300,
					}
				);
				return false;
			}
		}
		if (form.get("parentesco").invalid) {
			this.notification.error(
				this.traducao.getLabel("parentesco-obrigatorio"),
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
		return true;
	}

	onEdit(event: any) {}

	private disableElement(element: any) {
		if (this.permiteEditar) {
			if (element.hasAttribute(this.APP_DISABLED)) {
				if (element.getAttribute("disabled") !== "") {
					element.removeAttribute(this.DISABLED);
				}
				element.removeAttribute(this.APP_DISABLED);
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					element.removeAttribute(this.TAB_INDEX);
				}
			}
		} else {
			if (!element.hasAttribute(this.DISABLED)) {
				this.renderer.setAttribute(element, this.APP_DISABLED, "");
				this.renderer.setAttribute(element, this.DISABLED, "true");

				// disabling anchor tab keyboard event
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					this.renderer.setAttribute(element, this.TAB_INDEX, "-1");
				}
			}
		}
		if (element.children) {
			for (let ele of element.children) {
				this.disableElement(ele);
			}
		}
	}
}
