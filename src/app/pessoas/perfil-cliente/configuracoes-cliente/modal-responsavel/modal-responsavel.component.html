<div *ngFor="let entry of data" class="row">
	<div class="col-6">
		<div class="entry">
			<span class="entry-label">matricula:</span>
			<span class="entry-value">{{ entry?.codigoMatricula }}</span>
		</div>
		<div class="entry">
			<span class="entry-label">Nome:</span>
			<span class="entry-value">{{ entry?.pessoaDTO.nome }}</span>
		</div>
		<div class="entry">
			<span class="entry-label">Cpf:</span>
			<span class="entry-value">{{ entry?.pessoaDTO.cfp }}</span>
		</div>
		<div class="entry">
			<span class="entry-label">Empresa:</span>
			<span class="entry-value">{{ entry?.empresaDTO.nome }}</span>
		</div>
	</div>
	<div class="col-3">
		<div class="entry">
			<span class="entry-label">Dependentes</span>
			<span *ngFor="let dependente of entry.dependentes" class="entry-value">
				{{ dependente.nome }}
			</span>
		</div>
	</div>
	<div class="col-3 buttons">
		<pacto-cat-button
			(click)="adicionarAForm(entry.pessoaDTO)"
			label="Adicionar responsável"></pacto-cat-button>
		<pacto-cat-button
			(click)="goToPerfil(entry.codigoMatricula)"
			label="Visualizar este perfil"></pacto-cat-button>
	</div>
</div>
