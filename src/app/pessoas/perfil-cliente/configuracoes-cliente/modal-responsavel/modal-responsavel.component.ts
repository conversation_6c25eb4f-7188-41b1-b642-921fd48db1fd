import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-responsavel",
	templateUrl: "./modal-responsavel.component.html",
	styleUrls: ["./modal-responsavel.component.scss"],
})
export class ModalResponsavelComponent implements OnInit {
	@Input() data: any;
	@Input() form: FormGroup;

	constructor(private router: Router, private ngbModal: NgbActiveModal) {}

	ngOnInit() {}

	adicionarAForm(pessoaData) {
		let formGroupMontado = new FormGroup({
			codigo: new FormControl(pessoaData.codigo),
			nome: new FormControl(pessoaData.nome),
			cfp: new FormControl(pessoaData.cfp),
		});
		this.form.addControl("pessoaResponsavel", formGroupMontado);
		this.ngbModal.close();
	}

	goToPerfil(matricula) {
		this.ngbModal.close();
		this.router.navigate(["/pessoas", "perfil-v2", matricula]);
	}
}
