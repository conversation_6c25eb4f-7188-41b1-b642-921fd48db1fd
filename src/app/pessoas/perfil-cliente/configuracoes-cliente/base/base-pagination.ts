export class Pagination {
	content: Array<any>;
	totalPages?: number;
	number?: number;
	first?: boolean;
	last?: boolean;
	totalElements?: number;
	fullArray?: Array<any>;
	size?: number;
}

export interface PaginationInterface {
	data: Pagination;
	sizeChangedEvent?: () => void;
	pageChangedEvent?: () => void;
	itemAddedEvent?: () => void;
	itemDeletedEvent?: () => void;
}

const DEFAULT_VALUES: Pagination = {
	content: [],
	totalPages: 0,
	number: 0,
	last: true,
	first: true,
	totalElements: 0,
	fullArray: [],
	size: 0,
};

export class BasePagination implements PaginationInterface {
	private _data: Pagination;
	private _pageSize: number;
	private _currentPage: number;

	public get data(): Pagination {
		return this.paginate(
			// array
			this._data.content,
			this.pageSize,
			this.currentPage
		);
	}

	public set data(value: Pagination) {
		this._data = value;
	}

	public get currentPage(): number {
		return this._currentPage;
	}

	public set currentPage(value: number) {
		this._currentPage = value;
	}

	public get pageSize(): number {
		return this._pageSize;
	}

	public set pageSize(value: number) {
		this._pageSize = value;
	}

	public changeSize(event: number): void {
		this.pageSize = event;
		this.currentPage = 1;
		this.sizeChangedEvent();
	}

	public changePage(event: number): void {
		this.currentPage = event;
		this.pageSize = this.pageSize;
		this.pageChangedEvent();
	}

	sizeChangedEvent(): void {}

	pageChangedEvent(): void {}

	itemAddedEvent(): void {}

	itemDeletedEvent(): void {}

	public addItemToData(newItem: any): void {
		const _tempData = { ...this.data };
		//  o backupData contem todos os itens do array,
		// e foi adicionado para evitar a paginação de quebrar,
		// pois this._data contém apenas os
		// itens paginados, não contém todos os valores do array
		_tempData.content = [..._tempData.fullArray, newItem];

		// ↓ reminds the component about the pagination config
		this.pageSize = this.pageSize;
		this.currentPage = this.currentPage;

		// ↓ updates the table data
		this.data = _tempData;

		this.itemAddedEvent();
	}

	public deleteItemFromData(indexOfItemToDelete: number): void {
		// ↓ prevent alterations to the this._data directly
		const _tempData = { ...this.data };
		const tempArray = [..._tempData.fullArray];

		// ↓ updates list
		const prevArray = _tempData.content;
		prevArray.splice(indexOfItemToDelete, 1);
		tempArray.splice(indexOfItemToDelete, 1);
		_tempData.content = tempArray;

		// // ↓ reminds the component about the pagination config
		if (prevArray.length === 0 && tempArray.length > 0) {
			this.currentPage = this.currentPage - 1;
		} else {
			this.currentPage = this.currentPage;
		}
		this.pageSize = this.pageSize;

		// ↓ updates the table data
		this.data = _tempData;
		this.itemDeletedEvent();
	}

	paginate(array: any[], page_size: number, page_number: number): Pagination {
		const pages = {};

		if (!array || !Array.isArray(array)) {
			return { ...DEFAULT_VALUES, size: page_size };
		}

		let arrayIndex = 0;
		for (let i = 0; i < array.length; i += page_size) {
			const chunk = array.slice(i, i + page_size);
			pages[++arrayIndex] = chunk;
		}

		const _pagesLength = Object.keys(pages).length;

		if (_pagesLength === 0) {
			return { ...DEFAULT_VALUES, content: [], size: page_size };
		}

		this.currentPage = page_number;
		this.pageSize = page_size;
		return {
			content: pages[page_number],
			first: page_number === 1,
			last: _pagesLength === page_number,
			number: page_number,
			totalElements: array.length,
			totalPages: _pagesLength,
			size: page_size,
			fullArray: array,
		};
	}
}
