.config-client-dados-acessos {
	pacto-cat-card-plain {
		margin-top: 25px;
	}

	.pct-table-zebra {
		td {
			border: 0;
		}
	}

	pacto-cat-button {
		margin-top: 50px;
	}

	button {
		all: unset;
		color: #0380e3;
		// text-decoration: underline;
		font-size: 0.87rem;
		font-weight: 600;
		cursor: pointer;
	}

	h2 {
		margin-top: 1.5rem;
	}

	pacto-cat-table-editable {
		.pacto-table-title-block {
			padding-top: 0;
			padding-left: 0;
		}

		&[data-is-editing="true"] {
			form.row-clickable:not(.row-editable) {
				.pct-edit {
					color: #b8d9f3 !important;
				}

				.pct-trash-2 {
					color: #efbac0 !important;
				}

				.action-cell {
					pointer-events: none;
				}
			}
		}
	}

	.custom-table-wrapper {
		position: relative;

		.add-row-button {
			position: absolute;
			bottom: 46px;
			left: 30px;
		}
	}

	.custom-col {
		flex-direction: column;
		margin-top: 1rem;
		margin-bottom: 1rem;
		gap: 0.5rem;

		button {
			margin-top: 1rem;
		}
	}

	.h6 {
		margin-top: 1.5rem;
		margin-bottom: 0.8rem;
	}

	.custom-multi-select {
		.nome,
		.pct-error-msg {
			display: none;
		}
	}
}

.top30 {
	margin-top: 30px;
}
