<pacto-cat-card-plain>
	<div class="row">
		<div class="col-sm-12 col-lg-3 col-md-3 align-self-center">
			<pacto-cat-checkbox
				[control]="form.get('usuarioAcessaWeb')"
				i18n-label="@@config-pessoa-acesso:usuario-acesso-web"
				label="Usuário terá acesso web"></pacto-cat-checkbox>
		</div>
	</div>

	<div class="row align-items-center">
		<div class="col-sm-12 col-lg-3 col-md-3">
			<pacto-cat-form-input
				[control]="form.get('usuarioEmailDTO.email')"
				i18n-label="@@config-pessoa-acesso:email-acesso"
				label="E-mail de acesso"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-3 col-md-3">
			<button i18n="@@config-pessoa-acesso:alterar-email-acesso">
				Alterar e-mail de acesso
			</button>
		</div>
		<div class="col-sm-12 col-lg-3 col-md-3">
			<pacto-cat-form-input
				[control]="form.get('usuarioTelefoneDTO.numero')"
				i18n-label="@@config-pessoa-acesso:telefone-acesso"
				label="Telefone de acesso"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-3 col-md-3">
			<button i18n="@@config-pessoa-acesso:verificar-telefone-acesso">
				Verificar telefone de acesso
			</button>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12 col-lg-3 col-md-3">
			<button
				(click)="alterarSenha()"
				i18n="@@config-pessoa-acesso:alterar-senha-acesso">
				Alterar senha de acesso
			</button>
		</div>
	</div>

	<div class="row">
		<div class="col-sm-12 col-lg-12 col-md-12 top30">
			<pacto-cat-select
				[control]="form.get('horarioAcessoSistemaDTO.codigo')"
				[idKey]="'codigo'"
				[items]="horarios"
				[labelKey]="'descricao'"
				i18n-label="@@configuracoes-pessoa-acesso:horario"
				label="Horário de acesso"></pacto-cat-select>
		</div>
	</div>

	<pacto-cat-card-plain class="custom-table-wrapper">
		<pacto-cat-table-editable
			#tableData
			(confirm)="confirm($event)"
			(delete)="delete($event)"
			(isEditingOrAddingItem)="isEditingFn($event)"
			[attr.data-is-editing]="isEditing"
			[enableZebraStyle]="true"
			[isEditable]="true"
			[showAddRow]="false"
			[showShare]="false"
			[table]="table"
			actionTitle="Ações"
			i18n-actionTitle="@@label-acoes"
			i18n-tableTitle="@@config-pessoa-acesso:perfil-acesso"
			idSuffix="table-acesso"
			tableTitle="Perfil de acesso"></pacto-cat-table-editable>

		<button
			(click)="addRow()"
			class="add-row-button"
			i18n="@@config-pessoa-acesso:adicionar-perfil-acesso">
			Adicionar perfil de acesso
		</button>
	</pacto-cat-card-plain>

	<h2 class="h6" i18n-label="@@config-pessoa-acesso:assinatura-digital">
		Assinatura digital
	</h2>

	<div class="row">
		<div class="col-sm-12 col-lg-12 col-md-12">
			<pacto-cat-file-input
				[control]="form.get('base64Assinatura')"
				[imageHeight]="150"
				[imageWidth]="650"
				[nomeControl]="formGroup.get('fileNameControl')"
				[urlImage]="form.get('urlImagemAssinatura').value"
				formatos=".JPG, .JPEG, .PNG"
				formatosValidos="(jpeg|jpg|png)$"></pacto-cat-file-input>
		</div>
	</div>

	<div class="d-flex custom-col">
		<pacto-cat-checkbox
			[control]="form.get('permiteExcluirClientesVinculosNoBanco')"
			i18n-label="@@config-pessoa-acesso:permite-excluir-vinculo"
			label="Permite excluir clientes com vínculos no banco de dados"></pacto-cat-checkbox>

		<pacto-cat-checkbox
			[control]="form.get('usarSenhaMenosVezesDuranteOperacoes')"
			i18n-label="@@config-pessoa-acesso:usar-senha-menos-vezes"
			label="Usar senha menos vezes durante as operações"></pacto-cat-checkbox>

		<button
			(click)="abrirHistoricoAcesso()"
			i18n="@@config-pessoa-acesso:visualizar-relatorio-acesso">
			Visualizar relatório de acesso ao sistema
		</button>
	</div>

	<div class="d-flex justify-content-end">
		<pacto-cat-button
			(click)="saveConfig.emit()"
			[hidden]="!configClienteFormValidation.isClientOnly"
			i18n-label="@@config-pessoa:btn-salvar"
			label="Salvar"
			size="LARGE"
			style="margin-right: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<ng-template #columnNomeEmpresa>
	<span i18n="@@config-pessoa-acesso:nome-empresa">Nome da empresa</span>
</ng-template>

<ng-template #columnPerfilAcesso>
	<span i18n="@@config-pessoa-acesso:perfil-acesso">Perfil de acesso</span>
</ng-template>

<ng-template #columnHorarioAcesso>
	<span i18n="@@config-pessoa-acesso:horario-acesso">Horário de acesso</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@config-pessoa-acesso:alterar-senha" xingling="alterar-senha">
		Alterar senha
	</span>
	<span
		i18n="@@config-pessoa-acesso:ultimos-acessos-sistema"
		xingling="ultimos-acessos-sistema">
		Ultimos acessos ao sistema
	</span>
	<span i18n="@@config-pessoa-acesso:add-row-error" xingling="add-row-error">
		Verifique se todos os campos estão preenchidos
	</span>
</pacto-traducoes-xingling>
