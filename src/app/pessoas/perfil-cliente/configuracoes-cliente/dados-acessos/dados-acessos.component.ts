import {
	Component,
	OnInit,
	ViewChild,
	TemplateRef,
	EventEmitter,
	Output,
	ChangeDetectorRef,
	AfterViewInit,
	ViewEncapsulation,
	HostBinding,
	Input,
} from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import {
	CatTableEditableComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import { BehaviorSubject } from "rxjs";
import { ActivatedRoute } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";
import { ModalAlteracaoSenhaComponent } from "../modal-alteracao-senha/modal-alteracao-senha.component";
import { ModalHistoricoAcessoComponent } from "../modal-historico-acesso/modal-historico-acesso.component";
import { AcessoService } from "pessoa-ms-api";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";

@Component({
	selector: "pacto-dados-acessos",
	templateUrl: "./dados-acessos.component.html",
	styleUrls: ["./dados-acessos.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class DadosAcessosComponent implements OnInit, AfterViewInit {
	@HostBinding("class.config-client-dados-acessos") styleEncapsulation = true;
	table: PactoDataGridConfig;
	@Output() saveConfig = new EventEmitter<void>();

	@Input() form: FormGroup;

	matricula: string;
	codigoCliente: string;
	perfis$: BehaviorSubject<Array<any>> = new BehaviorSubject([]);

	formGroup: FormGroup = new FormGroup({
		fileNameControl: new FormControl(),
	});

	horarios = [];

	isEditing = false;

	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("columnNomeEmpresa", { static: true })
	columnNomeEmpresa: TemplateRef<any>;

	@ViewChild("columnPerfilAcesso", { static: true })
	columnPerfilAcesso: TemplateRef<any>;

	@ViewChild("columnHorarioAcesso", { static: true })
	columnHorarioAcesso: TemplateRef<any>;

	@ViewChild("tableData", { static: true })
	tableData: CatTableEditableComponent;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	constructor(
		private fb: FormBuilder,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private admCoreClienteService: AdmCoreApiClienteService,
		private restService: RestService,
		private dialogService: DialogService,
		private acessoService: AcessoService,
		public configClienteFormValidation: ConfigCliFormValidationService
	) {}

	ngOnInit() {
		this.cd.detectChanges();
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");
		console.log("matricula: ", this.matricula);
		if (this.matricula) {
			this.admCoreClienteService
				.dadosPessoais(this.matricula)
				.subscribe((dados: ClienteDadosPessoais) => {
					if (!dados.matricula) {
						this.handleInvalidUserError();
						return;
					}
				});
		}
		if (
			this.form.get("usuarioPerfilAcessoDTO") &&
			this.form.get("usuarioPerfilAcessoDTO").value
		) {
			this.perfis$.next(this.form.get("usuarioPerfilAcessoDTO").value);
		}

		this.form.get("usuarioEmailDTO").enable();
		if (
			this.form.get("usuarioEmailDTO") &&
			this.form.get("usuarioEmailDTO").value &&
			this.form.get("usuarioEmailDTO").value.codigo > 0
		) {
			this.form.get("usuarioEmailDTO").disable();
		}

		this.form.get("usuarioTelefoneDTO").enable();
		if (
			this.form.get("usuarioTelefoneDTO") &&
			this.form.get("usuarioTelefoneDTO").value &&
			this.form.get("usuarioTelefoneDTO").value.codigo > 0
		) {
			this.form.get("usuarioTelefoneDTO").disable();
		}
	}

	ngAfterViewInit() {
		this.acessoService.getHorariosAcesso().subscribe((resp) => {
			this.horarios = resp;
		});
		this.table = new PactoDataGridConfig({
			showFilters: false,
			dataAdapterFn: () => {
				return {
					size: this.perfis$.value.length,
					content: this.perfis$.value,
					totalElements: this.perfis$.value.length,
					number: 0,
				};
			},
			formGroup: new FormGroup({
				empresa: new FormControl(),
				perfilAcesso: new FormControl(),
			}),
			beforeConfirm: (row: any, form: any, data: any, indexRow: any) =>
				this.beforeConfirm(row, form, data, indexRow),
			columns: [
				{
					nome: "empresa",
					titulo: this.columnNomeEmpresa,
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "select",
					objectAttrLabelName: "label",
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					showAddSelectBtn: false,
					showSelectFilter: false,
					endpointUrl: this.restService.buildFullUrlPessoaMs(
						"alunoColaboradorUsuario/empresasPerfilAcesso"
					),
					width: "150px",
				},
				{
					nome: "perfilAcesso",
					titulo: this.columnPerfilAcesso,
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "select",
					objectAttrLabelName: "label",
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					showAddSelectBtn: false,
					showSelectFilter: false,
					endpointUrl: this.restService.buildFullUrlPessoaMs(
						"alunoColaboradorUsuario/perfisAcesso"
					),
					width: "150px",
				},
			],
			actions: [],
		});

		this.cd.detectChanges();
	}

	isEditingFn(event) {
		this.isEditing = event;
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		console.log(Object.entries(row));
		const invalidData = Object.entries(row)
			.filter(([key, _]) => !["edit", "codigo"].includes(key))
			.every(([key, value]) => value === null);

		if (invalidData) {
			this.notificationService.error(this.traducoes.getLabel("add-row-error"), {
				timeout: 2000,
				bodyMaxLength: 300,
			});
			return false;
		}

		return true;
	}

	confirm(event) {
		if (event.row.codigo === null || event.row.codigo === undefined) {
			this.form.get("usuarioPerfilAcessoDTO").value.push({
				empresa: event.row.empresa,
				perfilAcesso: event.row.perfilAcesso,
			});
		} else {
			this.form.get("usuarioPerfilAcessoDTO").value.forEach((u) => {
				if (u.codigo === event.row.codigo) {
					u.empresa = event.row.empresa;
					u.perfilAcesso = event.row.perfilAcesso;
				}
			});
		}
		console.log("evento edit", event);
	}

	delete(event) {
		const data = event.data;
		data.splice(event.index, 1);
		console.log("evento delete", event);
	}

	private handleInvalidUserError(): void {
		this.notificationService.error(this.traducoes.getLabel("aluno-invalido"), {
			timeout: 2000,
		});

		setTimeout(() => {
			this.notificationService.info(this.traducoes.getLabel("redirect-msg"), {
				timeout: 2000,
			});
		}, 3000);
	}

	addRow(): void {
		this.isEditing = true;
		const previousData = this.perfis$.value;
		const isEditing = previousData.findIndex((d) => d.edit);

		if (isEditing !== -1) {
			return;
		}

		this.perfis$.next([
			...previousData,
			{
				edit: true,
			},
		]);

		this.tableData.reloadData();
	}

	alterarSenha(): void {
		this.dialogService.open(
			this.traducoes.getLabel("alterar-senha"),
			ModalAlteracaoSenhaComponent,
			PactoModalSize.MEDIUM
		);
	}

	abrirHistoricoAcesso(): void {
		const dialogRef = this.dialogService.open(
			this.traducoes.getLabel("ultimos-acessos-sistema"),
			ModalHistoricoAcessoComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.codigoUsuario =
			this.form.get("codigoUsuario").value;
	}
}
