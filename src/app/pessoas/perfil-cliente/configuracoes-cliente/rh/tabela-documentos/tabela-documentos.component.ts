import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import * as moment from "moment/moment";
import { RhService } from "pessoa-ms-api";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { BasePagination } from "../../base/base-pagination";
import { UploadDocsComponent } from "../../upload-docs/upload-docs.component";

@Component({
	selector: "pacto-tabela-documentos",
	template: `
		<pacto-relatorio
			[enableZebraStyle]="true"
			#docTableData
			[table]="docsTable"
			tableTitle="Documentos"
			i18n-tableTitle="@@config-rh:documentos-table"
			[showShare]="false"
			i18n-actionTitulo="@@label-acoes"
			actionTitulo="Ações"
			[customActions]="customActionDoc"
			(pageChangeEvent)="changePage($event)"
			(pageSizeChange)="changeSize($event)"
			(iconClick)="iconClick($event)"
			[itensPerPage]="itensPerPage"></pacto-relatorio>
		<ng-template #customActionDoc>
			<div class="d-flex justify-content-end">
				<pacto-cat-button
					type="OUTLINE"
					size="LARGE"
					label="Adicionar documento"
					i18n-label="@@config-rh:adicionar-documento"
					(click)="addDoc()"></pacto-cat-button>
			</div>
		</ng-template>

		<pacto-traducoes-xingling #traducoes>
			<span i18n-label="@@config-rh:delete-doc" xingling="delete-tooltip">
				Deletar documento
			</span>
			<span i18n-label="@@config-rh:download-doc" xingling="download-tooltip">
				Fazer download do documento
			</span>
			<span i18n-label="@@config-rh:adicionar-doc" xingling="adicionar-doc">
				Adicionar documento
			</span>
		</pacto-traducoes-xingling>

		<ng-template #descricaoDoc>
			<span i18n="@@aba-rh:descricao-do-documento">Descrição do documento</span>
		</ng-template>
		<ng-template #nomeArquivo>
			<span i18n="@@aba-rh:nome-do-arquivo">Nome do arquivo</span>
		</ng-template>
		<ng-template #usuario>
			<span i18n="@@aba-rh:usuario">Usuário</span>
		</ng-template>
		<ng-template #dataLancamento>
			<span i18n="@@aba-rh:data-de-lancamento">Data de lançamento</span>
		</ng-template>
	`,
	styles: [``],
	encapsulation: ViewEncapsulation.None,
})
export class TabelaDocumentosComponent
	extends BasePagination
	implements OnInit, AfterViewInit
{
	@Input() control: FormControl = new FormControl();

	@ViewChild("descricaoDoc", { static: true })
	descricaoDoc: TemplateRef<any>;

	@ViewChild("nomeArquivo", { static: true })
	nomeArquivo: TemplateRef<any>;

	@ViewChild("usuario", { static: true })
	usuario: TemplateRef<any>;

	@ViewChild("dataLancamento", { static: true })
	dataLancamento: TemplateRef<any>;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	@ViewChild("docTableData", { static: true }) docTableData: RelatorioComponent;

	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];

	docsTable: PactoDataGridConfig;

	constructor(
		private cd: ChangeDetectorRef,
		private dialogService: DialogService,
		private sessionService: SessionService,
		private rhService: RhService
	) {
		super();
		this.pageSize = 5;
		this.currentPage = 1;
	}

	ngOnInit() {
		this.data = {
			content: this.control.value || [],
		};
	}

	ngAfterViewInit(): void {
		this.docsTable = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: () => {
				return this.data;
			},
			columns: [
				{
					nome: "descricaoDocumento",
					titulo: this.descricaoDoc,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeArquivo",
					titulo: this.nomeArquivo,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeUsuario",
					titulo: this.usuario,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataLancamento",
					titulo: this.dataLancamento,
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "download",
					iconClass: "pct pct-download cor-azulim05",
					tooltipText: this.traducoes.getLabel("download-tooltip"),
				},
				{
					nome: "delete",
					iconClass: "pct pct-trash-2 cor-hellboy05",
					tooltipText: this.traducoes.getLabel("delete-tooltip"),
				},
			],
		});

		this.cd.detectChanges();
	}

	iconClick(evt: { row: any; iconName: string }): void {
		this[evt.iconName](evt);
	}

	addDoc(): void {
		this.dialogService
			.open(
				this.traducoes.getLabel("adicionar-doc"),
				UploadDocsComponent,
				PactoModalSize.LARGE
			)
			.result.then((data) => {
				const obj = {
					codigo: null,
					descricaoDocumento: data.nomeArquivo,
					nomeArquivo: data.fileNameControl,
					usuario: this.sessionService.loggedUser.usuarioZw,
					nomeUsuario: this.sessionService.loggedUser.nome,
					dataLancamento: moment(new Date()).format("DD/MM/YYYY HH:MM:SS"),
					docBase64: data.file,
				};
				this.addItemToData(obj);
				this.docTableData.reloadData();
				this.control.patchValue(this.data.fullArray);
			});
	}

	download(event): void {
		this.rhService
			.getUrlArquivo({ fotoKey: event.row.fotoKey })
			.subscribe((resp) => {
				window.open(resp, "_blank");
			});
	}

	itemAddedEvent(): void {
		this.docTableData.reloadData();
		this.control.patchValue(this.data.fullArray);
	}

	delete(event): void {
		const rowIsEmpty = Object.entries(event.row).every(([key, value]: any) => {
			// ignore the edit property
			if (key === "edit" && value === true) {
				return true;
			}

			return value === "";
		});

		// delete empty row
		if (rowIsEmpty) {
			event.data[event.index].edit = false;
			event.data.splice(event.index, 1);
		}

		// call API to delete item;
		if (!event.row.edit) {
			this.rhService
				.deleteArquivo({ fotoKey: event.row.fotoKey })
				.subscribe((resp) => {
					if (resp) {
						this.deleteItemFromData(event.index);
					}
				});
		}
	}

	itemDeletedEvent(): void {
		this.docTableData.reloadData();
		this.control.patchValue(this.data.fullArray);
	}

	pageChangedEvent(): void {
		this.docTableData.reloadData();
	}

	sizeChangedEvent(): void {
		this.docTableData.reloadData();
	}
}
