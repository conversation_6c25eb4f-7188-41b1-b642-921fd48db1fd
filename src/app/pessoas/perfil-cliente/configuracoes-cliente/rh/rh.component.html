<pacto-cat-card-plain>
	<h2 class="h6" i18n="@@config-rh:info-basicas">Informações básicas</h2>

	<div class="row align-items-end">
		<div class="col-sm-12 col-lg-5 col-md-5">
			<pacto-cat-form-multi-select-filter
				[control]="form.get('modalidadesColaboradorTrabalha')"
				[endpointUrl]="modalidadesUrl"
				[hidden]="!configCliFormValidation.isClientOnly"
				[paramBuilder]="selectBuilder"
				[resposeParser]="responseParser"
				i18n-label="@@config-rh:modalidade-colaborador"
				idKey="codigo"
				label="Modalidade em que o colaborador trabalha"
				labelKey="nome"></pacto-cat-form-multi-select-filter>
		</div>

		<div class="col-sm-12 col-lg-3 col-md-3">
			<pacto-cat-form-select-filter
				[control]="form.get('departamento').get('codigo')"
				[endpointUrl]="departamentosUrl"
				i18n-label="@@config-rh:departamento"
				idKey="codigo"
				label="Departamento"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-select
				[control]="form.get('cargaHoraria')"
				[items]="listaCargaHoraria"
				i18n-label="@@config-rh:carga-horaria"
				idKey="codigo"
				label="Carga horária"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				[control]="form.get('valorSalario')"
				i18n-label="@@config-rh:valor-salario"
				label="Valor do salário"></pacto-cat-form-input>
		</div>
	</div>

	<div class="row align-items-end">
		<div class="col-sm-12 col-lg-8 col-md-8">
			<pacto-cat-form-multi-select-filter
				[control]="form.get('tipoColaborador')"
				[options]="tiposColaborador"
				i18n-label="@@config-rh:tipo-colaborador"
				idKey="descricao"
				label="Tipo de colaborador"
				labelKey="valorDescricao"></pacto-cat-form-multi-select-filter>
		</div>

		<div class="col-sm-12 col-lg-2 col-md-23">
			<pacto-cat-form-select
				[control]="form.get('tamanhoCamisa')"
				[items]="listaTamanhoCamisa"
				i18n-label="@@config-rh:tamanho-camisa"
				idKey="codigo"
				label="Tamanho da camisa"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-select
				[control]="form.get('tamanhoCalca')"
				[items]="listaTamanhoCalsa"
				i18n-label="@@config-rh:tamanho-calca"
				idKey="codigo"
				label="Tamanho da calça"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>

	<div class="row">
		<div class="col-sm-12 col-lg-8 col-md-8">
			<pacto-cat-form-input
				[control]="form.get('observacao')"
				i18n-label="@@config-rh:observacao"
				label="Observação"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-datepicker
				[control]="form.get('dataCadastro')"
				[label]="
					(configCliFormValidation.configForms['dataCadastro']?.indicator ||
						'') + 'Data de cadastro'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@config-rh:data-cadastro-required"
				i18n-label="@@config-rh:data-cadastro"
				id="data-cadastro"></pacto-cat-form-datepicker>
		</div>
	</div>

	<pacto-cat-card-plain>
		<pacto-tabela-documentos
			[control]="form.get('documentos')"></pacto-tabela-documentos>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain>
		<pacto-tabela-afastamento-ferias
			[control]="
				form.get('afastamentoFerias')
			"></pacto-tabela-afastamento-ferias>
	</pacto-cat-card-plain>

	<h2 class="h6 mg-1">Studio</h2>

	<pacto-cat-button
		(click)="gerarUrlGoogle()"
		i18n-label="@@config-rh:gerar-token-goole"
		label="Gerar token google"
		size="LARGE"
		type="OUTLINE"></pacto-cat-button>

	<div class="row">
		<div class="col-sm-12 col-lg-12 col-md-12">
			<pacto-cat-form-input
				[control]="form.get('autenticarGoogle')"
				[label]="
					(configCliFormValidation.configForms['autenticarGoogle']?.indicator ||
						'') + 'Autenticar google'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@config-rh:autenticar-google-required"
				i18n-label="@@config-rh:autenticar-google"></pacto-cat-form-input>
		</div>
	</div>

	<div class="d-flex justify-content-end">
		<pacto-cat-button
			(click)="saveConfig.emit()"
			i18n-label="@@dados-acesso:btn-salvar"
			label="Salvar"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<ng-template #customAction>
	<div class="d-flex justify-content-end gap-1">
		<pacto-cat-button
			i18n-label="@@config-rh:visualizar-historico"
			label="Visualizar histórico"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="adicionarPeriodo()"
			i18n-label="@@config-rh:adicionar-periodo-afastamento-ferias"
			label="Adicionar novo período de afastamento / férias"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
	</div>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n-label="@@config-rh:delete-doc" xingling="delete-tooltip">
		Deletar documento
	</span>
	<span i18n-label="@@config-rh:download-doc" xingling="download-tooltip">
		Fazer download do documento
	</span>
	<span i18n-label="@@config-rh:delete-period" xingling="delete-period-tooltip">
		Deletar período
	</span>
	<span i18n-label="@@config-rh:edit-period" xingling="edit-period-tooltip">
		Editar item
	</span>
	<span i18n-label="@@config-rh:adicionar-doc" xingling="adicionar-doc">
		Adicionar documento
	</span>
	<span
		i18n-label="@@config-rh:adicionar-afastamento"
		xingling="adicionar-afastamento">
		Adicionar afastamento / férias
	</span>
	<span i18n="@@config-rh:consultor" xingling="consultor">Consultor</span>
	<span i18n="@@config-rh:orientador" xingling="orientador">Orientador</span>
	<span i18n="@@config-rh:personal-externo" xingling="personal-externo">
		Personal Externo
	</span>
	<span i18n="@@config-rh:personal-interno" xingling="personal-interno">
		Personal Interno
	</span>
	<span i18n="@@config-rh:personal-trainer" xingling="personal-trainer">
		Personal Trainer
	</span>
	<span i18n="@@config-rh:professor" xingling="professor">Professor</span>
	<span i18n="@@config-rh:professor-treino-web" xingling="professor-treino-web">
		Professor (TreinoWeb)
	</span>
	<span i18n="@@config-rh:terceirizado" xingling="terceirizado">
		Terceirizado
	</span>
</pacto-traducoes-xingling>

<ng-template #descricaoDoc>
	<span i18n="@@aba-rh:descricao-do-documento">Descrição do documento</span>
</ng-template>
<ng-template #nomeArquivo>
	<span i18n="@@aba-rh:nome-do-arquivo">Nome do arquivo</span>
</ng-template>
<ng-template #usuario>
	<span i18n="@@aba-rh:usuario">Usuário</span>
</ng-template>
<ng-template #dataLancamento>
	<span i18n="@@aba-rh:data-de-lancamento">Data de lançamento</span>
</ng-template>
<ng-template #periodo>
	<span i18n="@@aba-rh:periodo">Período</span>
</ng-template>
<ng-template #substituto>
	<span i18n="@@aba-rh:substituto">Substituto</span>
</ng-template>
<ng-template #motivo>
	<span i18n="@@aba-rh:motivo">Motivo</span>
</ng-template>
