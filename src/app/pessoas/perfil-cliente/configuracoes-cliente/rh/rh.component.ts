import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostBinding,
	Input,
	Output,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import {
	DialogService,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";

@Component({
	selector: "pacto-configuracoes-rh",
	templateUrl: "./rh.component.html",
	styleUrls: ["./rh.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ConfiguracoesRhComponent {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@HostBinding("class.pessoa-config-rh-tab")
	styleEncapsulation = true;

	modalidadesUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/modalidadesColaboradorTrabalha"
	);

	departamentosUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/departamentos"
	);

	@Input() form: FormGroup;

	urlCalendar = "http://app.pactosolucoes.com.br/studiocalendar/";
	googleClientID = "403136198769.apps.googleusercontent.com";

	tiposColaborador: Array<{ descricao: string; valorDescricao: string }> = [
		{
			descricao: "PR",
			valorDescricao: "Professor",
		},
		{
			descricao: "TW",
			valorDescricao: "Professor (TreinoWeb)",
		},
		{
			descricao: "PT",
			valorDescricao: "Personal Trainer",
		},
		{
			descricao: "OR",
			valorDescricao: "Orientador",
		},
		{
			descricao: "CO",
			valorDescricao: "Consultor",
		},
		{
			descricao: "PI",
			valorDescricao: "Personal Interno",
		},
		{
			descricao: "PE",
			valorDescricao: "Personal Externo",
		},
		{
			descricao: "TE",
			valorDescricao: "Terceirizado",
		},
		{
			descricao: "ES",
			valorDescricao: "Estúdio",
		},
		{
			descricao: "FO",
			valorDescricao: "Fornecedor",
		},
		{
			descricao: "CR",
			valorDescricao: "Coordenador",
		},
		{
			descricao: "MD",
			valorDescricao: "Médico",
		},
		{
			descricao: "FC",
			valorDescricao: "Funcionário",
		},
		{
			descricao: "AD",
			valorDescricao: "Administrador",
		},
	];
	listaCargaHoraria = [
		{
			codigo: 0,
			descricao: "",
		},
		{
			codigo: 2,
			descricao: "2 horas",
		},
		{
			codigo: 4,
			descricao: "4 horas",
		},
		{
			codigo: 6,
			descricao: "6 horas",
		},
		{
			codigo: 8,
			descricao: "8 horas",
		},
		{
			codigo: 12,
			descricao: "12 horas",
		},
	];
	listaTamanhoCamisa = [
		{
			codigo: "",
			descricao: "",
		},
		{
			codigo: "PP",
			descricao: "PP",
		},
		{
			codigo: "P",
			descricao: "P",
		},
		{
			codigo: "M",
			descricao: "M",
		},
		{
			codigo: "G",
			descricao: "G",
		},
		{
			codigo: "GG",
			descricao: "GG",
		},
		{
			codigo: "XG",
			descricao: "XG",
		},
		{
			codigo: "EG",
			descricao: "EG",
		},
		{
			codigo: "EGG",
			descricao: "EGG",
		},
	];
	listaTamanhoCalsa = [
		{
			codigo: 0,
			descricao: "",
		},
		{
			codigo: 34,
			descricao: "34",
		},
		{
			codigo: 36,
			descricao: "36",
		},
		{
			codigo: 38,
			descricao: "38",
		},
		{
			codigo: 40,
			descricao: "40",
		},
		{
			codigo: 42,
			descricao: "42",
		},
		{
			codigo: 44,
			descricao: "44",
		},
		{
			codigo: 46,
			descricao: "46",
		},
		{
			codigo: 48,
			descricao: "48",
		},
		{
			codigo: 50,
			descricao: "50",
		},
		{
			codigo: 52,
			descricao: "52",
		},
		{
			codigo: 54,
			descricao: "54",
		},
		{
			codigo: 56,
			descricao: "56",
		},
		{
			codigo: 58,
			descricao: "58",
		},
		{
			codigo: 60,
			descricao: "60",
		},
		{
			codigo: 62,
			descricao: "62",
		},
		{
			codigo: 64,
			descricao: "64",
		},
		{
			codigo: 66,
			descricao: "66",
		},
		{
			codigo: 68,
			descricao: "68",
		},
	];
	@Output() saveConfig = new EventEmitter<void>();

	constructor(
		private cd: ChangeDetectorRef,
		private dialogService: DialogService,
		private restService: RestService,
		public configCliFormValidation: ConfigCliFormValidationService
	) {}

	responseParser = (result) => {
		if (result && result.content && Array.isArray(result.content)) {
			return result.content;
		}
		return [];
	};

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	gerarUrlGoogle(): void {
		this.form.get("autenticarGoogle")
			.setValue(`https://accounts.google.com/ServiceLogin?service=lso&passive=1209600&continue=https://accounts.google.com/o/oauth2/auth?scope%3Dhttps://www.googleapis.com/auth/calendar%2Bhttps://www.googleapis.com/auth/calendar.readonly%26response_type%3Dcode%26access_type%3Doffline%26redirect_uri%3D
        ${this.urlCalendar}auth.php%26approval_prompt%3Dforce%26client_id%3D${this.googleClientID}%26hl%3Dpt%26from_login%3D1%26as%3D-4cdc3ffd4db6c4bf&ltmpl=popup`);
	}
}
