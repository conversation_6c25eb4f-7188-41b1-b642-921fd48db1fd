import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import * as moment from "moment";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { BasePagination } from "../../base/base-pagination";
import { ModalAdicionarPeriodoComponent } from "../../modal-adicionar-periodo/modal-adicionar-periodo.component";

@Component({
	selector: "pacto-tabela-afastamento-ferias",
	template: `
		<pacto-cat-table-editable
			#periodTableData
			[table]="periodsTable"
			tableTitle="Afastamento / Férias"
			i18n-tableTitle="@@config-rh:documentos-table"
			[customActions]="customAction"
			(delete)="delete($event)"
			[isEditable]="true"
			[showEdit]="false"
			i18n-actionTitle="@@label-acoes"
			actionTitle="Ações"
			[itensPerPage]="itensPerPage"></pacto-cat-table-editable>
		<ng-template #customAction>
			<div class="d-flex justify-content-end gap-1">
				<pacto-cat-button
					type="OUTLINE"
					size="LARGE"
					label="Visualizar histórico"
					i18n-label="@@config-rh:visualizar-historico"></pacto-cat-button>
				<pacto-cat-button
					type="OUTLINE"
					size="LARGE"
					label="Adicionar novo período de afastamento / férias"
					i18n-label="@@config-rh:adicionar-periodo-afastamento-ferias"
					(click)="adicionarPeriodo()"></pacto-cat-button>
			</div>
		</ng-template>
		<pacto-traducoes-xingling #traducoes>
			<span
				i18n-label="@@config-rh:adicionar-afastamento"
				xingling="adicionar-afastamento">
				Adicionar afastamento / férias
			</span>
		</pacto-traducoes-xingling>

		<ng-template #inicio>
			<span i18n="@@aba-rh:inicio">Inicio</span>
		</ng-template>
		<ng-template #fim>
			<span i18n="@@aba-rh:fim">Fim</span>
		</ng-template>
		<ng-template #substituto>
			<span i18n="@@aba-rh:substituto">Substituto</span>
		</ng-template>
		<ng-template #motivo>
			<span i18n="@@aba-rh:motivo">Motivo</span>
		</ng-template>
	`,
	styles: [``],
	encapsulation: ViewEncapsulation.None,
})
export class TabelaAfastamentoFeriasComponent
	extends BasePagination
	implements OnInit, AfterViewInit
{
	@Input() control: FormControl = new FormControl();

	@ViewChild("inicio", { static: true })
	inicio: TemplateRef<any>;

	@ViewChild("fim", { static: true })
	fim: TemplateRef<any>;

	@ViewChild("substituto", { static: true })
	substituto: TemplateRef<any>;

	@ViewChild("motivo", { static: true })
	motivo: TemplateRef<any>;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	@ViewChild("periodTableData", { static: true })
	periodTableData: RelatorioComponent;

	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];

	periodsTable: PactoDataGridConfig;

	constructor(
		private cd: ChangeDetectorRef,
		private dialogService: DialogService
	) {
		super();
		this.pageSize = 5;
		this.currentPage = 1;
	}

	ngOnInit() {
		this.data = {
			content: this.control.value || [],
		};
	}

	ngAfterViewInit(): void {
		this.periodsTable = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: () => {
				return this.data;
			},
			formGroup: new FormGroup({
				dtInicio: new FormControl(),
				dtFim: new FormControl(),
				codColaboradorSubstituto: new FormControl(),
				colaboradorSubstituto: new FormControl(),
				motivo: new FormControl(),
			}),
			columns: [
				{
					nome: "dtInicio",
					titulo: this.inicio,
					visible: true,
					ordenavel: false,
					inputType: "text",
				},
				{
					nome: "dtFim",
					titulo: this.fim,
					visible: true,
					ordenavel: false,
					inputType: "text",
				},
				{
					nome: "colaboradorSubstituto",
					titulo: this.substituto,
					visible: true,
					ordenavel: false,
					inputType: "text",
				},
				{
					nome: "motivo",
					titulo: this.motivo,
					visible: true,
					ordenavel: false,
					inputType: "text",
				},
			],
			actions: [],
		});

		this.cd.detectChanges();
	}

	adicionarPeriodo(): void {
		this.dialogService
			.open(
				this.traducoes.getLabel("adicionar-afastamento"),
				ModalAdicionarPeriodoComponent,
				PactoModalSize.LARGE
			)
			.result.then((data) => {
				this.addItemToData({
					dtInicio: moment(data.dtInicio).format("DD/MM/YYYY"),
					dtFim: moment(data.dtFim).format("DD/MM/YYYY"),
					codColaboradorSubstituto: data.substituto.codigo,
					colaboradorSubstituto: data.substituto.nome,
					motivo: data.motivo,
				});
				this.periodTableData.reloadData();
				this.control.patchValue(this.data.fullArray);
			});
	}

	itemAddedEvent(): void {
		this.periodTableData.reloadData();
		this.control.patchValue(this.data.fullArray);
	}

	delete(event): void {
		const rowIsEmpty = Object.entries(event.row).every(([key, value]: any) => {
			// ignore the edit property
			if (key === "edit" && value === true) {
				return true;
			}

			return value === "";
		});

		// delete empty row
		if (rowIsEmpty) {
			event.data[event.index].edit = false;
			event.data.splice(event.index, 1);
		}

		// call API to delete item;
		if (!event.row.edit) {
			this.deleteItemFromData(event.index);
		}
	}

	itemDeletedEvent(): void {
		this.periodTableData.reloadData();
		this.control.patchValue(this.data.fullArray);
	}

	pageChangedEvent(): void {
		this.periodTableData.reloadData();
	}

	sizeChangedEvent(): void {
		this.periodTableData.reloadData();
	}
}
