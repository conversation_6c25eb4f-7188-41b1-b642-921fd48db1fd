import { Component } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-upload-docs",
	templateUrl: "./upload-docs.component.html",
	styleUrls: ["./upload-docs.component.scss"],
})
export class UploadDocsComponent {
	form: FormGroup = new FormGroup({
		file: new FormControl(),
		nomeArquivo: new FormControl(),
		fileNameControl: new FormControl(),
	});

	url = "";

	constructor(private activeModal: NgbActiveModal) {}

	addDoc(): void {
		this.activeModal.close(this.form.value);
	}
}
