<div class="flex-column justify-content-center">
	<pacto-cat-camera
		(images)="captureImages($event)"
		*ngIf="enableCapture"
		width="800">
		<div class="d-flex justify-content-center">
			<pacto-cat-button
				(click)="goBack()"
				i18n-label="@@dados-basicos:voltar"
				label="Voltar"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button
				i18n-label="@@dados-basicos:tirar-foto"
				label="Tirar foto"
				pactoCatCameraCapture
				size="LARGE"
				style="margin-left: 16px"></pacto-cat-button>
		</div>
	</pacto-cat-camera>
	<ng-container *ngIf="!enableCapture">
		<img [src]="imageData" alt="Imagem capturada" />
		<div class="d-flex justify-content-center">
			<pacto-cat-button
				(click)="goBack()"
				label="Voltar"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="captureAnotherImage()"
				label="Capturar outra imagem"
				size="LARGE"
				style="margin-right: 16px; margin-left: 16px"></pacto-cat-button>

			<pacto-cat-button
				(click)="save()"
				label="Salvar alterações"
				size="LARGE"></pacto-cat-button>
		</div>
	</ng-container>
</div>
