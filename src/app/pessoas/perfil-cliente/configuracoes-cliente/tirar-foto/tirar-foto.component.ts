import { Component, Input, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-tirar-foto",
	templateUrl: "./tirar-foto.component.html",
	styleUrls: ["./tirar-foto.component.scss"],
})
export class TirarFotoComponent implements OnInit {
	@Input() control: FormControl = new FormControl();
	enableCapture: boolean = true;
	imageData;

	constructor(private dialog: NgbActiveModal) {}

	ngOnInit() {}

	goBack() {
		this.dialog.close();
	}

	captureImages(event: any) {
		this.enableCapture = false;
		this.imageData = event;
		this.control.setValue(this.imageData);
	}

	captureAnotherImage() {
		this.imageData = undefined;
		this.enableCapture = true;
	}

	save() {
		this.dialog.close();
	}
}
