@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts";

.config-cliente-dados-fin {
	pacto-cat-card-plain {
		margin-top: 1.5rem;
	}

	.gap-1 {
		> * {
			width: 100%;
			display: block;

			.nome {
				line-height: revert;
			}
		}

		gap: 1rem;
	}

	pacto-cat-form-input,
	pacto-cat-form-input-number {
		.nome {
			color: $typeDefaultText;

			span {
				@extend .pct-title4;
			}
		}
	}

	pacto-cat-form-select {
		.pacto-label {
			color: $typeDefaultText;

			span {
				@extend .pct-title4;
			}
		}
	}

	pacto-cat-form-textarea {
		.textarea-label {
			color: $typeDefaultText;

			span {
				@extend .pct-title4;
			}
		}
	}

	pacto-cat-form-textarea {
		margin-top: 0;
	}

	.h6 {
		font-weight: 700;
		font-size: 1rem;
		margin-bottom: 1.5rem;
	}

	dt {
		font-size: 0.87rem;
	}
}
