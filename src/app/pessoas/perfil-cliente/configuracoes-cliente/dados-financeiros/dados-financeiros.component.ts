import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostBinding,
	Input,
	OnInit,
	Output,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";
import { PermissaoService } from "pacto-layout";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-dados-financeiros",
	templateUrl: "./dados-financeiros.component.html",
	styleUrls: ["./dados-financeiros.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class DadosFinanceirosComponent implements OnInit, AfterViewInit {
	@HostBinding("class.config-cliente-dados-fin") styleEncapsulation = true;

	@Input() form: FormGroup;
	@Input() permiteEditar: boolean;
	DISABLED = "disabled";
	APP_DISABLED = "app-disabled";
	TAB_INDEX = "tabindex";
	TAG_ANCHOR = "a";
	@Output() saveConfig = new EventEmitter<void>();
	public contaMask = {
		mask: [/[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, "-", /[0-9]/],
	};
	public agenciaMask = {
		mask: [/[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, "-", /[0-9]/],
	};
	public digitoMask = { mask: [/[0-9]/] };
	public notaOptions = [
		{ label: "", value: "" },
		{ label: "Emitir nota no nome de terceiro", value: "terceiro" },
		{ label: "Emitir nota no nome do aluno", value: "aluno" },
		{ label: "Emitir nota no nome da mãe", value: "mae" },
	];
	existeConvenioVindi: boolean = false;

	constructor(
		private eleRef: ElementRef,
		private renderer: Renderer2,
		public configCliFormValidation: ConfigCliFormValidationService,
		private cdr: ChangeDetectorRef,
		private permissaoService: PermissaoService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		this.form.addControl("emitirNota", new FormControl(""));
		this.form.addControl("nomeTerceiro", new FormControl(""));
		this.form.addControl("cpfCNPJTerceiro", new FormControl(""));
		this.form.addControl("inscEstadualTerceiro", new FormControl(""));
		this.form.addControl("cfdfTerceiro", new FormControl(""));
		this.form.addControl("combinedAgenciaDigito", new FormControl(""));
		this.form.addControl("combinedContaDigito", new FormControl(""));
		this.form.addControl("nomeMae", new FormControl(""));
		this._configureFieldIdVindi();
		this.updateCombinedAgencia();
		this.updateCombinedConta();

		if (this.form.get("emitirNomeTerceiro").value == true) {
			this.form.get("emitirNota").setValue("terceiro");
		}
		if (this.form.get("emitirNotaNomeAluno").value == true) {
			this.form.get("emitirNota").setValue("aluno");
		}
		if (this.form.get("emitirNotaNomeMae").value == true) {
			this.form.get("emitirNota").setValue("mae");
		}
		if (
			this.form.get("emitirNomeTerceiro").value == false &&
			this.form.get("emitirNotaNomeAluno").value == false &&
			this.form.get("emitirNotaNomeMae").value == false
		) {
			this.form.get("emitirNota").setValue("");
		}
		this.form.get("emitirNota").valueChanges.subscribe((value) => {
			if (value === "terceiro") {
				this.form
					.get("emitirNomeTerceiro")
					.setValue(true, { emitEvent: false });
				this.form
					.get("emitirNotaNomeAluno")
					.setValue(false, { emitEvent: false });
				this.form
					.get("emitirNotaNomeMae")
					.setValue(false, { emitEvent: false });
				this.form.get("nomeTerceiro").enable({ emitEvent: false });
				this.form.get("cpfCNPJTerceiro").enable({ emitEvent: false });
				this.form.get("inscEstadualTerceiro").enable({ emitEvent: false });
				this.form.get("cfdfTerceiro").enable({ emitEvent: false });
			}
			if (value === "aluno") {
				this.form
					.get("emitirNomeTerceiro")
					.setValue(false, { emitEvent: false });
				this.form
					.get("emitirNotaNomeAluno")
					.setValue(true, { emitEvent: false });
				this.form
					.get("emitirNotaNomeMae")
					.setValue(false, { emitEvent: false });
				this.form.get("nomeTerceiro").disable({ emitEvent: false });
				this.form.get("cpfCNPJTerceiro").disable({ emitEvent: false });
				this.form.get("inscEstadualTerceiro").disable({ emitEvent: false });
				this.form.get("cfdfTerceiro").disable({ emitEvent: false });
			}
			if (value === "mae") {
				this.form
					.get("emitirNomeTerceiro")
					.setValue(false, { emitEvent: false });
				this.form
					.get("emitirNotaNomeAluno")
					.setValue(false, { emitEvent: false });
				this.form.get("emitirNotaNomeMae").setValue(true, { emitEvent: false });
				this.form.get("nomeTerceiro").disable({ emitEvent: false });
				this.form.get("cpfCNPJTerceiro").disable({ emitEvent: false });
				this.form.get("inscEstadualTerceiro").disable({ emitEvent: false });
				this.form.get("cfdfTerceiro").disable({ emitEvent: false });
			}
			if (value === "") {
				this.form
					.get("emitirNomeTerceiro")
					.setValue(false, { emitEvent: false });
				this.form
					.get("emitirNotaNomeAluno")
					.setValue(false, { emitEvent: false });
				this.form
					.get("emitirNotaNomeMae")
					.setValue(false, { emitEvent: false });
				this.form.get("nomeTerceiro").disable({ emitEvent: false });
				this.form.get("cpfCNPJTerceiro").disable({ emitEvent: false });
				this.form.get("inscEstadualTerceiro").disable({ emitEvent: false });
				this.form.get("cfdfTerceiro").disable({ emitEvent: false });
				this.cdr.detectChanges();
			}
		});

		this.form
			.get("agencia")
			.valueChanges.subscribe(() => this.updateCombinedAgencia());
		this.form
			.get("agenciaDigito")
			.valueChanges.subscribe(() => this.updateCombinedAgencia());
		this.form
			.get("conta")
			.valueChanges.subscribe(() => this.updateCombinedConta());
		this.form
			.get("contaDigito")
			.valueChanges.subscribe(() => this.updateCombinedConta());

		this.form.get("combinedAgenciaDigito").valueChanges.subscribe((value) => {
			const { valor: agencia, digito: agenciaDigito } =
				this.separateValues(value);
			this.form.get("agencia").setValue(agencia, { emitEvent: false });
			this.form
				.get("agenciaDigito")
				.setValue(agenciaDigito, { emitEvent: false });
		});
		this.form.get("combinedContaDigito").valueChanges.subscribe((value) => {
			const { valor: conta, digito: contaDigito } = this.separateValues(value);
			this.form.get("conta").setValue(conta, { emitEvent: false });
			this.form.get("contaDigito").setValue(contaDigito, { emitEvent: false });
		});

		if (this.form.get("emitirNota").value !== "terceiro") {
			this.form.get("nomeTerceiro").disable({ emitEvent: false });
			this.form.get("cpfCNPJTerceiro").disable({ emitEvent: false });
			this.form.get("inscEstadualTerceiro").disable({ emitEvent: false });
			this.form.get("cfdfTerceiro").disable({ emitEvent: false });
		}
		this.definirNotaOption();
	}

	ngAfterViewInit(): void {
		this.disableElement(this.eleRef.nativeElement);
	}

	private disableElement(element: any) {
		if (this.permiteEditar) {
			if (element.hasAttribute(this.APP_DISABLED)) {
				if (element.getAttribute("disabled") !== "") {
					element.removeAttribute(this.DISABLED);
				}
				element.removeAttribute(this.APP_DISABLED);
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					element.removeAttribute(this.TAB_INDEX);
				}
			}
		} else {
			if (!element.hasAttribute(this.DISABLED)) {
				this.renderer.setAttribute(element, this.APP_DISABLED, "");
				this.renderer.setAttribute(element, this.DISABLED, "true");

				// disabling anchor tab keyboard event
				if (element.tagName.toLowerCase() === this.TAG_ANCHOR) {
					this.renderer.setAttribute(element, this.TAB_INDEX, "-1");
				}
			}
		}
		if (element.children) {
			for (let ele of element.children) {
				this.disableElement(ele);
			}
		}
	}

	private updateCombinedAgencia() {
		const agencia = this.form.get("agencia").value || "";
		const digito = this.form.get("agenciaDigito").value || "";
		const combined = this.combineValues(agencia, digito);
		this.form
			.get("combinedAgenciaDigito")
			.setValue(combined, { emitEvent: false });
	}

	private updateCombinedConta() {
		const conta = this.form.get("conta").value || "";
		const digito = this.form.get("contaDigito").value || "";
		const combined = this.combineValues(conta, digito);
		this.form
			.get("combinedContaDigito")
			.setValue(combined, { emitEvent: false });
	}

	private combineValues(valor: string, digito: string): string {
		return `${valor}-${digito}`;
	}

	private separateValues(combinedValue: string): {
		valor: string;
		digito: string;
	} {
		const [valor, digito] = combinedValue.split("-");
		return { valor, digito };
	}

	private definirNotaOption() {
		if (this.form.get("apresentarOpcaoEmitirNomeMae").value === false) {
			this.notaOptions = this.notaOptions.filter(
				(item) => item.value !== "mae"
			);
		}
	}

	private _configureFieldIdVindi() {
		this.existeConvenioVindi = this.form.get("existeConvenioVindi").value;
		if (
			!this.permissaoService.temPermissaoAdm("2.76") &&
			!this.sessionService.isUsuarioPactoSolucoes
		) {
			this.existeConvenioVindi = false;
		}
	}
}
