<pacto-cat-card-plain>
	<form [formGroup]="form">
		<div class="row">
			<div class="col-3">
				<pacto-cat-form-input
					id="dados-finan-banco"
					[control]="form.get('banco')"
					i18n-label="@@config-pessoa-financeiro:banco"
					label="Banco"></pacto-cat-form-input>
			</div>
			<div class="col-3">
				<pacto-cat-form-input
					id="dados-finan-agencia"
					[control]="form.get('combinedAgenciaDigito')"
					[textMask]="agenciaMask"
					i18n-label="@@config-pessoa-financeiro:agencia"
					label="Agência com dígito"></pacto-cat-form-input>
				<input formControlName="agencia" type="hidden" />
				<input formControlName="agenciaDigito" type="hidden" />
			</div>
			<div class="col-3">
				<pacto-cat-form-input
					id="dados-finan-conta"
					[control]="form.get('combinedContaDigito')"
					[textMask]="contaMask"
					i18n-label="@@config-pessoa-financeiro:conta"
					label="Conta com dígito"></pacto-cat-form-input>
				<input formControlName="conta" type="hidden" />
				<input formControlName="contaDigito" type="hidden" />
			</div>
			<div class="col-3">
				<pacto-cat-form-input
					id="dados-finan-identificador-cobranca"
					[control]="form.get('identificadorCobranca')"
					i18n-label="@@config-pessoa-financeiro:identificador-cobranca"
					label="Identificador para cobrança"></pacto-cat-form-input>
			</div>
			<div class="col-4" *ngIf="existeConvenioVindi">
				<pacto-cat-form-input
					id="dados-finan-idvindi"
					[control]="form.get('idVindi')"
					i18n-label="@@config-pessoa-financeiro:idVindi"
					label="IdVindi"></pacto-cat-form-input>
			</div>
			<div
				[class.col-6]="!existeConvenioVindi"
				[class.col-4]="existeConvenioVindi">
				<pacto-cat-form-input-number
					id="dados-finan-porc-desc-pag-antecipado-boleto"
					[formControl]="
						form.get('porcentagemDescontoPagamentoAntecipadoBoleto')
					"
					decimal="true"
					i18n-label="@@config-pessoa-financeiro:desconto-pagamento-antecipado"
					label="Percentual de desconto para pagamento antecipado do boleto"
					max="99.99"></pacto-cat-form-input-number>
			</div>
			<div
				[class.col-6]="!existeConvenioVindi"
				[class.col-4]="existeConvenioVindi">
				<pacto-cat-form-input-number
					id="dados-finan-valor-maximo-caixa-aberto"
					[formControl]="form.get('valorMaximoDeixarCaixaAberto')"
					decimal="true"
					i18n-label="@@config-pessoa-financeiro:valor-maximo-caixa-aberto"
					label="Valor máximo para deixar no caixa em aberto (Venda Avulsa):"></pacto-cat-form-input-number>
			</div>
		</div>

		<hr />

		<h2 class="h5">Nota fiscal</h2>
		<div class="row">
			<div class="col-sm-12 col-md-12 col-md-12">
				<pacto-cat-form-select
					id="dados-finan-emitir-nota"
					[control]="form.get('emitirNota')"
					[items]="notaOptions"
					i18n-label="@@config-pessoa-financeiro:selecione-opcao"
					idKey="value"
					label="Emissão de nota"
					labelKey="label"></pacto-cat-form-select>
			</div>
			<div class="col-12">
				<pacto-cat-form-textarea
					id="dados-finan-obs-nota"
					[control]="form.get('observacaoNota')"
					i18n-label="@@config-pessoa-financeiro:obs-nfse"
					label="Observação NFSe"
					rows="4"></pacto-cat-form-textarea>
			</div>
		</div>
		<div *ngIf="form.get('emitirNota').value === 'terceiro'" class="row">
			<pacto-cat-form-input
				id="dados-finan-nome-terceiro"
				[control]="form.get('nomeTerceiro')"
				class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3"
				i18n-label="@@config-pessoa-financeiro:nome-terceiro"
				label="Nome Terceiro"
				maxlength="120"></pacto-cat-form-input>
			<pacto-cat-form-input
				id="dados-finan-cpf-cnpj-terceiro"
				[control]="form.get('cpfCNPJTerceiro')"
				class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3"
				i18n-label="@@config-pessoa-financeiro:cpfcnpj-terceiro"
				label="CPF/CNPJ Terceiro"
				maxlength="20"></pacto-cat-form-input>
			<pacto-cat-form-input
				id="dados-finan-insc-estadual-terceiro"
				[control]="form.get('inscEstadualTerceiro')"
				class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3"
				i18n-label="@@config-pessoa-financeiro:insc-estadual-terceiro"
				label="Inscrição Estadual Terceiro	"></pacto-cat-form-input>
			<pacto-cat-form-input
				id="dados-finan-cfdf-terceiro"
				[control]="form.get('cfdfTerceiro')"
				class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3"
				i18n-label="@@config-pessoa-financeiro:cfdf-terceiro"
				label="CFDF Terceiro"></pacto-cat-form-input>
		</div>
		<hr />

		<h2 class="h6">Dados adicionais</h2>

		<dl class="row">
			<div class="col-sm-12 col-lg-2 col-md-2">
				<dt i18n="@@config-pessoa-financeiro:customer-id-pagolivre">
					CustomerId PagoLivre
				</dt>
				<dd>
					{{
						form.get("customerIdPagoLivre").value ||
							"Aluno não possui integração"
					}}
				</dd>
			</div>

			<div class="col-sm-12 col-lg-2 col-md-2">
				<dt i18n="@@config-pessoa-financeiro:id-pagar-ms">Id PagarMe</dt>
				<dd>
					{{ form.get("idPagarMe").value || "Aluno não possui integração" }}
				</dd>
			</div>
		</dl>
		<div class="d-flex justify-content-end">
			<pacto-cat-button
				id="dados-finan-btn-salvar"
				(click)="saveConfig.emit()"
				[hidden]="!configCliFormValidation.isClientOnly"
				i18n-label="@@config-pessoa:btn-salvar"
				label="Salvar alterações"
				size="LARGE"></pacto-cat-button>
		</div>
	</form>
</pacto-cat-card-plain>
