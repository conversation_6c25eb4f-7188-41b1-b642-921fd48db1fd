@import "src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/colors";

.pacto-configuracoes-cliente {
	display: flex;
	flex-wrap: wrap;

	.nav-aux {
		flex-basis: 100%;
		margin: 0px 0px 20px 0px;
	}

	.top-navigation {
		color: $pretoPri;

		i {
			font-size: 28px;
			margin-right: 1rem;
		}

		@extend .type-h2;
	}

	.first * {
		margin-bottom: 0;
	}
}

.cli-notification {
	padding: 8px 16px;
	border-radius: 5px;
	margin-bottom: 22px;

	&.cli-not-error {
		background: $actionDefaultRisk01;
		color: $actionDefaultRisk05;
	}

	.cli-not-icon {
		margin-right: 8px;
	}

	span {
		&.cli-not-icon {
			display: inline-block;
		}
	}
}

.loading-blur {
	filter: blur(5px);
	pointer-events: none;
}

.spacer {
	border-bottom: 3px solid #d1d4dc;
}
