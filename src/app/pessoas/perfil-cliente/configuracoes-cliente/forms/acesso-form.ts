import { FormControl, FormGroup } from "@angular/forms";

export const FormGroupAcesso = new FormGroup({
	codigoUsuario: new FormControl(),
	usuarioAcessaWeb: new FormControl(),
	usuarioEmailDTO: new FormGroup({
		codigo: new FormControl(),
		usuario: new FormControl(),
		email: new FormControl(),
		verificado: new FormControl(),
	}),
	usuarioTelefoneDTO: new FormGroup({
		codigo: new FormControl(),
		usuario: new FormControl(),
		ddi: new FormControl(),
		numero: new FormControl(),
		verificado: new FormControl(),
	}),
	usuarioPerfilAcessoDTO: new FormControl([
		{
			codigo: new FormControl(),
			empresa: new FormGroup({
				codigo: new FormControl(),
				nome: new FormControl(),
			}),
			perfilAcesso: new FormGroup({
				codigo: new FormControl(),
				nome: new FormControl(),
			}),
		},
	]),
	horarioAcessoSistemaDTO: new FormGroup({
		codigo: new FormControl(),
		descricao: new FormControl(),
		livre: new FormControl(),
	}),
	base64Assinatura: new FormControl(),
	urlImagemAssinatura: new FormControl(),
	permiteExcluirClientesVinculosNoBanco: new FormControl(),
	usarSenhaMenosVezesDuranteOperacoes: new FormControl(),
	permiteAlterarPropriaSenha: new FormControl(),
});
