import { FormControl, FormGroup } from "@angular/forms";

export const FormGroupRh = new FormGroup({
	modalidadesColaboradorTrabalha: new FormControl(),
	departamento: new FormGroup({
		codigo: new FormControl(),
		nome: new FormControl(),
	}),
	cargaHoraria: new FormControl(),
	valorSalario: new FormControl(),
	observacao: new FormControl(),
	tamanhoCamisa: new FormControl(),
	tamanhoCalca: new FormControl(),
	documentos: new FormControl(),
	afastamentoFerias: new FormControl(),
	tipoColaborador: new FormControl([
		{
			codigo: new FormControl(),
			descricao: new FormControl(),
			valorDescricao: new FormControl(),
		},
	]),
	dataCadastro: new FormControl({ value: null, disabled: true }),
	autenticarGoogle: new FormControl(),
});
