import { FormControl, FormGroup } from "@angular/forms";

export const FormGroupDadosFinanceiros = new FormGroup({
	banco: new FormControl(),
	agencia: new FormControl(),
	agenciaDigito: new FormControl(),
	conta: new FormControl(),
	contaDigito: new FormControl(),
	identificadorCobranca: new FormControl(),
	porcentagemDescontoPagamentoAntecipadoBoleto: new FormControl(),
	valorMaximoDeixarCaixaAberto: new FormControl(),
	observacaoNota: new FormControl(),
	emitirNomeTerceiro: new FormControl(),
	emitirNotaNomeAluno: new FormControl(),
	idPagarMe: new FormControl(),
	customerIdPagoLivre: new FormControl(),
	nomeTerceiro: new FormControl(),
	inscEstadualTerceiro: new FormControl(),
	cfdfTerceiro: new FormControl(),
	cpfCNPJTerceiro: new FormControl(),
	emitirNotaNomeMae: new FormControl(),
	apresentarOpcaoEmitirNomeMae: new FormControl(),
	idVindi: new FormControl(),

	// somente para controle não deve ser utilizado no form
	existeConvenioVindi: new FormControl(),
});
