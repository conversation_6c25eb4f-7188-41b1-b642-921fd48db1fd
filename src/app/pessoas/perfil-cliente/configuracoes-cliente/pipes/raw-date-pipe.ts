import { Pipe, PipeTransform } from "@angular/core";

@Pipe({ name: "rawDate" })
export class RawDatePipe implements PipeTransform {
	transform(value: string | number | Date): string {
		if (!value) return "";

		const timeStamp =
			typeof value === "string" || typeof value === "number"
				? new Date(value).getTime()
				: value.getTime();

		const date = new Date(timeStamp);
		const year = date.getUTCFullYear();
		const month = String(date.getUTCMonth() + 1).padStart(2, "0");
		const day = String(date.getUTCDate()).padStart(2, "0");

		return `${day}/${month}/${year}`;
	}
}
