<pacto-cat-card-plain>
	<div>
		<pacto-cat-table-editable
			[enableZebraStyle]="true"
			#tableData
			[table]="tableTipoVinculo"
			[isEditable]="true"
			[showEdit]="true"
			[showAddRow]="permiteIncluir"
			[filterConfig]="filterConfig"
			[showDelete]="permiteExcluir"
			[actionTitle]="permiteEditar || permiteExcluir ? 'Ações' : ''"
			[class.edit-disabled]="!permiteEditar"
			i18n-actionTitle="@@label-acoes"
			actionTitle="Ações"
			class="custom-padding"
			emptyStateMessage="Nenhum vínculo cadastrado."
			(confirm)="confirm($event)"
			(delete)="delete($event)"
			(edit)="onEdit($event)"
			(pageChangeEvent)="changePage($event)"
			(pageSizeChange)="changeSize($event)"
			newLineTitle="Adicionar vinculo"></pacto-cat-table-editable>
	</div>

	<div class="d-flex justify-content-end gap-1">
		<pacto-cat-button
			(click)="openModalHistoricoVinculo()"
			i18n-label="@@configuracoes-cliente-vinculo:historico-vinculo"
			icon="pct pct-list"
			label=""
			size="LARGE"
			title="Histórico de vínculos"
			type="OUTLINE"></pacto-cat-button>
		<!--<pacto-cat-button
			i18n-label="@@configuracoes-cliente-vinculo:excluir-cliente"
			label="Excluir cliente"
			type="OUTLINE_DANGER"
			size="LARGE">
		</pacto-cat-button> -->
		<!-- <pacto-cat-button
			i18n-label="@@configuracoes-cliente-vinculo:mesclar-cliente"
			label="Mesclar cliente"
			icon="pct pct-list"
			type="OUTLINE"
			size="LARGE">
		</pacto-cat-button> -->
		<pacto-cat-button
			(click)="saveConfig.emit()"
			[hidden]="!configClienteFormValidation.isClientOnly"
			i18n-label="@@dados-acesso:btn-salvar"
			label="Salvar alterações"
			size="LARGE"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<ng-template #columnTipoVinculo>
	<span i18n="@@configuracoes-cliente-vinculo:tipo-vinculo">
		Tipo de vínculo
	</span>
</ng-template>
<ng-template #columnColaborador>
	<span i18n="@@configuracoes-cliente-vinculo:colaborador">Colaborador</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@aba-vinculos:consultor" xingling="consultor">Consultor</span>
	<span i18n="@@aba-vinculos:coordenador" xingling="coordenador">
		Coordenador
	</span>
	<span i18n="@@aba-vinculos:orientador" xingling="orientador">Orientador</span>
	<span i18n="@@aba-vinculos:personal-externo" xingling="personal-externo">
		Personal Externo
	</span>
	<span i18n="@@aba-vinculos:personal-interno" xingling="personal-interno">
		Personal Interno
	</span>
	<span i18n="@@aba-vinculos:personal-trainer" xingling="personal-trainer">
		Personal Trainer
	</span>
	<span i18n="@@aba-vinculos:professor" xingling="professor">Professor</span>
	<span
		i18n="@@aba-vinculos:professor-treino-web"
		xingling="professor-treino-web">
		Professor (TreinoWeb)
	</span>
	<span i18n="@@aba-vinculos:terceirizado" xingling="terceirizado">
		Terceirizado
	</span>
	<span i18n="@@aba-vinculos:added-vinculo" xingling="added-vinculo">
		Vínculo adicionado com sucesso! Clique em salvar para concluir.
	</span>
	<span i18n="@@aba-vinculos:consultor-existe" xingling="consultor-existe">
		Já existe um consultor responsável por este cliente
	</span>
	<span
		i18n="@@aba-vinculos:preencha-todos-campos"
		xingling="preencha-todos-campos">
		Preencha todos os campos para adicionar um vínculo!
	</span>
</pacto-traducoes-xingling>
