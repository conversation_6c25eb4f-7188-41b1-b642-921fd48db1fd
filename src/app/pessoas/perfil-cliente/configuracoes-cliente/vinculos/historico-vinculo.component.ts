import { DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { TipoColaboradorModel } from "../../../../../../projects/pessoa-ms-api/src/lib/config/model/tipo-colaborador.model";

declare var moment;

@Component({
	selector: "pacto-historico-vinculo",
	template: `
		<pacto-relatorio
			[enableZebraStyle]="true"
			#tableData
			[table]="table"
			[showShare]="false"
			emptyStateMessage="Nenhum histórico de vínculo encontrado"
			i18n-emptyStateMessage="
				@@configuracoes-cliente-vinculo:empty-historico"></pacto-relatorio>

		<ng-template #columnDataRegistro>
			<span i18n="@@configuracoes-cliente-vinculo:data-registro">Data</span>
		</ng-template>
		<ng-template #columnColaborador>
			<span i18n="@@configuracoes-cliente-vinculo:colaborador">
				Nome do colaborador
			</span>
		</ng-template>
		<ng-template #columnTipoColaborador>
			<span i18n="@@configuracoes-cliente-vinculo:tipo-colaborador">
				Tipo do colaborador
			</span>
		</ng-template>
		<ng-template #columnTipoHistoricoVinculo>
			<span i18n="@@configuracoes-cliente-vinculo:acao-realizada">
				Ação realizada
			</span>
		</ng-template>
		<ng-template #columnResponsavelAlteracao>
			<span i18n="@@configuracoes-cliente-vinculo:responsavel-alteracao">
				Responsável pela alteração
			</span>
		</ng-template>
	`,
	styles: [
		`
			::ng-deep .modal-historico-vinculo .modal-dialog {
				width: 1000px !important;
				max-width: 1000px !important;
			}
		`,
	],
})
export class HistoricoVinculoComponent implements AfterViewInit {
	@ViewChild("tableData", { static: false })
	tableData: RelatorioComponent;

	@ViewChild("columnDataRegistro", { static: true })
	columnDataRegistro: TemplateRef<any>;

	@ViewChild("columnColaborador", { static: true })
	columnColaborador: TemplateRef<any>;

	@ViewChild("columnTipoColaborador", { static: true })
	columnTipoColaborador: TemplateRef<any>;

	@ViewChild("columnTipoHistoricoVinculo", { static: true })
	columnTipoHistoricoVinculo: TemplateRef<any>;

	@ViewChild("columnResponsavelAlteracao", { static: true })
	columnResponsavelAlteracao: TemplateRef<any>;

	@Input() codigoCliente: number = null;

	table: PactoDataGridConfig;
	filterConfig = {};

	constructor(
		private cd: ChangeDetectorRef,
		private readonly datePipe: DatePipe,
		private restService: RestService
	) {}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.restService.buildFullUrlPessoaMs(
				`alunoColaboradorUsuario/historicoVinculo/cliente/${this.codigoCliente}`
			),
			quickSearch: true,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			rowClick: false,
			columns: [
				{
					nome: "data",
					titulo: this.columnDataRegistro,
					visible: true,
					ordenavel: false,
					width: "20%",
					valueTransform: (v) =>
						this.datePipe.transform(v, "dd/MM/yyyy HH:mm:SS"),
					styleClass: "center",
				},
				{
					nome: "nomeDoColaborador",
					titulo: this.columnColaborador,
					visible: true,
					ordenavel: false,
					width: "20%",
				},
				{
					nome: "tipoDoColaborador",
					titulo: this.columnTipoColaborador,
					visible: true,
					ordenavel: false,
					width: "14%",
					styleClass: "center",
					valueTransform: (v) => {
						const tipoColaborador = TipoColaboradorModel.getTipo(v);
						if (tipoColaborador) {
							return tipoColaborador.getDescricao();
						}
						return v;
					},
				},
				{
					nome: "acaoRealizada",
					titulo: this.columnTipoHistoricoVinculo,
					visible: true,
					ordenavel: false,
					width: "20%",
					styleClass: "center",
					valueTransform: (v) => {
						if (v === "SD") {
							return "Saída";
						}
						if (v === "EN") {
							return "Entrada";
						}
					},
				},
				{
					nome: "responsavelPelaAlteracao",
					titulo: this.columnResponsavelAlteracao,
					visible: true,
					ordenavel: false,
					width: "20%",
				},
			],
		});

		this.cd.detectChanges();
	}
}
