import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostBinding,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoAutorizarAcessoService, Recurso } from "adm-legado-api";

import { SnotifyService } from "ng-snotify";
import { BehaviorSubject, Observable } from "rxjs";
import { PerfilRecursoPermissoTipo } from "sdk";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { BasePagination } from "../base/base-pagination";
import { ConfigCliFormValidationService } from "../config-cli-form-validation.service";
import { HistoricoVinculoComponent } from "./historico-vinculo.component";

declare var moment;

@Component({
	selector: "pacto-configuracoes-vinculos",
	templateUrl: "./vinculos.component.html",
	styleUrls: ["./vinculos.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class VinculosComponent
	extends BasePagination
	implements OnInit, AfterViewInit
{
	@HostBinding("class.pacto-configuracoes-vinculos")
	styleEncasulation = true;

	@ViewChild("columnTipoVinculo", { static: true })
	columnTipoVinculo: TemplateRef<any>;

	@ViewChild("columnColaborador", { static: true })
	columnColaborador: TemplateRef<any>;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	tableTipoVinculo: PactoDataGridConfig;
	filterConfig: any;
	matricula: string;
	dadosPessoais$: Observable<ClienteDadosPessoais | null>;
	permiteEditar;
	permiteIncluir;
	permiteExcluir;
	@Input() form: FormGroup;
	@Input() codigoCliente: number | null = null;

	colaboradoresUrl = this.restService.buildFullUrlPessoaMs(
		"alunoColaboradorUsuario/colaboradores"
	);

	tipoVinculos$ = new BehaviorSubject<any[]>([]);
	vinculos = [];
	colaborators = [];

	@Output() saveConfig = new EventEmitter<void>();
	@Input() recursoVinculo: Recurso = undefined;
	private permiteCrudVinculo: boolean;
	private message: string;

	constructor(
		private route: ActivatedRoute,
		private snotify: SnotifyService,
		private restService: RestService,
		private modalService: DialogService,
		public configClienteFormValidation: ConfigCliFormValidationService,
		private cd: ChangeDetectorRef,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService
	) {
		super();
		this.pageSize = 10;
		this.currentPage = 1;
	}

	ngOnInit() {
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");
		this.permiteEditar =
			this.recursoVinculo &&
			this.recursoVinculo.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR
			);
		this.permiteIncluir =
			this.recursoVinculo &&
			this.recursoVinculo.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		this.permiteExcluir =
			this.recursoVinculo &&
			this.recursoVinculo.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		this.data = {
			content: this.form.get("vinculos").value || [],
		};
		console.log(this.data);

		this.form.valueChanges.subscribe((v) => {
			this.data = {
				content: this.form.get("vinculos").value || [],
			};
			this.cd.detectChanges();
			this.tableData.reloadData();
		});
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				this.sessionService.codigoEmpresa,
				"Vinculo",
				"2.29 - Vínculos de cliente e de colaborador"
			)
			.subscribe(
				(responseAutorizacao) => {
					this.permiteCrudVinculo = true;
				},
				(error) => {
					this.message = this.notifyRequestError(error);
					this.permiteCrudVinculo = false;
				}
			);
	}

	ngAfterViewInit(): void {
		this.tipoVinculos$.next([
			{
				codigo: "CO",
				descricao: this.traducoes.getLabel("consultor"),
			},
			{
				codigo: "CR",
				descricao: this.traducoes.getLabel("coordenador"),
			},
			{
				codigo: "OR",
				descricao: this.traducoes.getLabel("orientador"),
			},
			{
				codigo: "PE",
				descricao: this.traducoes.getLabel("personal-externo"),
			},
			{
				codigo: "PI",
				descricao: this.traducoes.getLabel("personal-interno"),
			},
			{
				codigo: "PT",
				descricao: this.traducoes.getLabel("personal-trainer"),
			},
			{
				codigo: "PR",
				descricao: this.traducoes.getLabel("professor"),
			},
			{
				codigo: "TW",
				descricao: this.traducoes.getLabel("professor-treino-web"),
			},
			{
				codigo: "TE",
				descricao: this.traducoes.getLabel("terceirizado"),
			},
		]);
		this.data.content.forEach((d) => {
			d.nomeColaborador = {
				codigo: d.colaborador,
				nome: d.nomeColaborador,
			};
			const tipoVinculo = this.tipoVinculos$.value.find(
				(tp) => tp.codigo === d.tipoVinculo
			);
			if (tipoVinculo) {
				d.tipoVinculo = {
					codigo: tipoVinculo.codigo,
					descricao: tipoVinculo.descricao,
				};
			}
		});
		this.tableTipoVinculo = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			pagination: true,
			rowClick: false,
			dataAdapterFn: (serverData) => {
				return this.data;
			},
			beforeConfirm: (row, form, data, indexRow) =>
				this.beforeConfirm(row, form, data, indexRow),
			formGroup: new FormGroup({
				tipoVinculo: new FormControl(null, Validators.required),
				nomeColaborador: new FormControl(null, Validators.required),
			}),
			columns: [
				{
					nome: "tipoVinculo",
					titulo: this.columnTipoVinculo,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					showAddSelectBtn: false,
					showSelectFilter: true,
					infiniteScrollEnabled: true,
					infiniteScrollElementsSize: 50,
					inputSelectData: this.tipoVinculos$.value,
					selectOptionChange: (option, form, row) =>
						form.get("nomeColaborador").setValue(undefined),
					selectParamBuilder: (term) => {
						const filteredItems = this.tipoVinculos$.value.filter((item) =>
							item.descricao.toLowerCase().includes(term.toLowerCase())
						);

						this.tipoVinculos$.next(filteredItems);
						return {
							filters: JSON.stringify({
								nome: term,
							}),
						};
					},
					/*valueTransform: (v) => {
						if (!v) {
							return v;
						}

						const _type = this.tipoVinculos$.value.find((t) => t.codigo === v);

						if (_type) {
							return _type.descricao;
						}

						return null;
					}*/
				},
				{
					nome: "nomeColaborador",
					titulo: this.columnColaborador,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					showAddSelectBtn: false,
					showSelectFilter: true,
					infiniteScrollEnabled: false,
					infiniteScrollElementsSize: 50,
					selectParamBuilder: (nome) => {
						const tipoVinculoControl =
							this.tableTipoVinculo.formGroup.get("tipoVinculo");
						const tipoVinculo = tipoVinculoControl
							? tipoVinculoControl.value
							: null;
						return {
							filters: JSON.stringify({
								nome: nome ? nome : "",
								empresa: this.sessionService.empresaId,
								tipoColaborador: tipoVinculo != null ? tipoVinculo.codigo : "",
							}),
						};
					},
					endpointUrl: this.colaboradoresUrl,
				},
			],
		});

		this.cd.detectChanges();
	}

	onEdit(event: any) {
		if (!this.permiteEditar) {
			event.preventDefault();
			event.stopPropagation();
			return false;
		}
		return true;
	}

	beforeConfirm(row, form, data, rowIndex) {
		if (
			!this.recursoVinculo ||
			(this.recursoVinculo.tipoPermissoes &&
				!this.recursoVinculo.tipoPermissoes.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			this.snotify.error(
				"Usuário não possui a permissão 2.29 - Vínculos de cliente e de colaborador ou não possui permissão para inclusão. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
		if (form.invalid) {
			this.snotify.error(this.traducoes.getLabel("preencha-todos-campos"), {
				timeout: 2000,
				bodyMaxLength: 300,
			});
			return false;
		}
		const { nomeColaborador, tipoVinculo } = form.value;
		const itemConsultor = data.find(
			(i, index) =>
				i.tipoVinculo.codigo === "CO" &&
				i.tipoVinculo.codigo === tipoVinculo.codigo &&
				rowIndex !== index
		);
		if (itemConsultor) {
			this.snotify.error(this.traducoes.getLabel("consultor-existe"), {
				timeout: 2000,
				bodyMaxLength: 300,
			});
			return false;
		}
		const itemIgual = data.find(
			(i, index) =>
				i.colaborador === nomeColaborador.codigo &&
				i.tipoVinculo.codigo === tipoVinculo.codigo &&
				rowIndex !== index
		);
		if (itemIgual) {
			const tipoVinc = this.tipoVinculos$.value.find(
				(v) => v.codigo === tipoVinculo.codigo
			);
			this.snotify.error(
				`Já existe um ${tipoVinc.descricao} para o colaborador ${nomeColaborador.nome}`,
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return false;
		}
		return true;
	}

	confirm(event): void {
		if (!event.row.codigo && !event.row.edit) {
			this.addItemToData(event.row);
		} else {
			const vinculoIndex = this.data.fullArray.findIndex(
				(v) => v.codigo === event.row.codigo
			);
			if (vinculoIndex !== -1) {
				this.data.fullArray[vinculoIndex] = event.row;
			}
		}
	}

	adicionarVinculo(): void {
		const { codigo, nomeColaborador, tipoVinculo } =
			this.tableTipoVinculo.formGroup.value;

		const vinculo = {
			tipoVinculo: tipoVinculo.codigo,
			colaborador: nomeColaborador.codigo,
			nomeColaborador: nomeColaborador.nome,
			codigo,
		};

		this.addItemToData(vinculo);
		this.data.content = this.data.fullArray.sort((v1, v2) => {
			if (v1.nomeColaborador < v2.nomeColaborador) {
				return -1;
			}

			if (v1.nomeColaborador > v2.nomeColaborador) {
				return 1;
			}

			return 0;
		});
		this.tableData.reloadData();
		this.form.get("vinculos").patchValue(this.data.fullArray);
		this.tableTipoVinculo.formGroup.reset(undefined, { emitEvent: false });
		this.snotify.success(this.traducoes.getLabel("added-vinculo"));
	}

	private notifyRequestError(error) {
		if (error.error) {
			const meta = error.error.meta;
			if (meta.messageID) {
				this.snotify.error(this.traducoes.getLabel(meta.messageID), {
					timeout: 2000,
					bodyMaxLength: 300,
				});
				return;
			}
			if (meta.messageValue) {
				return meta.messageValue;
			}
			if (meta.message) {
				return meta.message;
			}
			this.snotify.error(this.traducoes.getLabel("server-error"), {
				timeout: 2000,
				bodyMaxLength: 300,
			});
		}
	}

	openModalHistoricoVinculo(): void {
		this.modalService.open(
			"Histórico de vínculos",
			HistoricoVinculoComponent,
			PactoModalSize.LARGE,
			"modal-historico-vinculo"
		).componentInstance.codigoCliente = this.codigoCliente;
	}

	itemAddedEvent(): void {
		this.tableData.reloadData();
		this.form.get("vinculos").patchValue(this.data.fullArray);
	}

	delete(event): void {
		if (
			event.row.codigo &&
			(!this.recursoVinculo ||
				(this.recursoVinculo.tipoPermissoes &&
					!this.recursoVinculo.tipoPermissoes.find(
						(tp) =>
							tp === PerfilRecursoPermissoTipo.EXCLUIR ||
							tp === PerfilRecursoPermissoTipo.TOTAL ||
							tp === PerfilRecursoPermissoTipo.EDITAR
					)))
		) {
			this.snotify.error(
				"Usuário não possui a permissão 2.29 - Vínculos de cliente e de colaborador ou não possui permissão para remoção de vínculo. Verifique o tipo de ação na permissão!",
				{
					timeout: 2000,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const rowIsEmpty = Object.entries(event.row).every(([key, value]: any) => {
			// ignore the edit property
			if (key === "edit" && value === true) {
				return true;
			}

			return value === "";
		});

		// delete empty row
		if (rowIsEmpty) {
			event.data[event.index].edit = false;
			event.data.splice(event.index, 1);
		}

		// call API to delete item;
		this.deleteItemFromData(event.index);
	}

	itemDeletedEvent(): void {
		this.tableData.reloadData();
		this.form.get("vinculos").patchValue(this.data.fullArray);
	}

	pageChangedEvent(): void {
		this.tableData.reloadData();
	}

	sizeChangedEvent(): void {
		this.tableData.reloadData();
	}
}
