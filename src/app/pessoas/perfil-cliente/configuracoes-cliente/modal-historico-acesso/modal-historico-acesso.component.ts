import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "pacto-modal-historico-acesso",
	templateUrl: "./modal-historico-acesso.component.html",
	styleUrls: ["./modal-historico-acesso.component.scss"],
})
export class ModalHistoricoAcessoComponent implements AfterViewInit {
	table: PactoDataGridConfig;

	codigoUsuario: number;

	@ViewChild("columnCodigo", { static: true })
	columnCodigo: TemplateRef<any>;

	@ViewChild("columnEmpresa", { static: true })
	columnEmpresa: TemplateRef<any>;

	@ViewChild("columnDataRegistro", { static: true })
	columnDataRegistro: TemplateRef<any>;

	constructor(
		private cd: ChangeDetectorRef,
		private restService: RestService
	) {}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			endpointUrl: this.restService.buildFullUrlPessoaMs(
				`alunoColaboradorUsuario/ultimosAcessos/${this.codigoUsuario}`
			),
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeEmpresa",
					titulo: this.columnEmpresa,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataRegistro",
					titulo: this.columnDataRegistro,
					visible: true,
					ordenavel: false,
				},
			],
			actions: [],
		});

		this.cd.detectChanges();
	}
}
