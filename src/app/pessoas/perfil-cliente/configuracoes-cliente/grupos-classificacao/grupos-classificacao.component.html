<pacto-cat-card-plain>
	<div class="d-flex align-items-center gap-1">
		<pacto-cat-form-select
			[control]="form.get('grupoCliente')"
			[items]="[]"
			i18n-label="@@configuracoes-cliente-grupos:grupo-cliente"
			idKey="codigo"
			label="Grupo do cliente"
			labelKey="label"></pacto-cat-form-select>
		<pacto-cat-form-select
			[control]="form.get('classificacaoCliente')"
			[items]="[]"
			i18n-label="@@configuracoes-cliente-grupos:classificacao-cliente"
			idKey="codigo"
			label="Classificação do cliente"
			labelKey="label"></pacto-cat-form-select>
		<pacto-cat-button
			(click)="adicionarGrupo()"
			class="icon-button"
			icon="pct pct-plus-circle"
			iconPosition="before"
			label="Adicionar"
			size="LARGE"
			type="NO_BORDER"></pacto-cat-button>
	</div>
	<pacto-relatorio
		#tableData
		[enableZebraStyle]="true"
		[showShare]="false"
		[table]="table"
		actionTitulo="Ações"
		i18n-actionTitulo="@@label-acoes"></pacto-relatorio>

	<div class="d-flex justify-content-end">
		<pacto-cat-button
			(click)="saveConfig.emit()"
			i18n-label="@@config-pessoa:btn-salvar"
			label="Salvar"
			size="LARGE"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<ng-template #columnGrupoCliente>
	<span i18n="@@configuracoes-cliente-grupos:grupo-cliente">
		Grupo do cliente
	</span>
</ng-template>
<ng-template #columnClassificacaoCliente>
	<span i18n="@@configuracoes-cliente-grupos:classificacao-cliente">
		Classificação do cliente
	</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n-label="@@configuracoes-cliente-grupos:delete-grupo"
		xingling="delete-tooltip">
		Deletar item
	</span>
</pacto-traducoes-xingling>
