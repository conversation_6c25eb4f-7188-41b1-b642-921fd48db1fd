import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostBinding,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "pacto-grupos-classificacao",
	templateUrl: "./grupos-classificacao.component.html",
	styleUrls: ["./grupos-classificacao.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class GruposClassificacaoComponent implements AfterViewInit {
	@HostBinding("class.pacto-configuracoes-grupos-classificacao")
	styleEncasulation = true;

	@ViewChild("columnGrupoCliente", { static: true })
	columnGrupoCliente: TemplateRef<any>;
	@ViewChild("columnClassificacaoCliente", { static: true })
	columnClassificacaoCliente: TemplateRef<any>;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	table: PactoDataGridConfig;
	filterConfig: any;

	@Output() saveConfig = new EventEmitter<void>();

	form: FormGroup = new FormGroup({
		grupoCliente: new FormControl(),
		classificacaoCliente: new FormControl(),
	});

	constructor(private cd: ChangeDetectorRef) {}

	ngAfterViewInit(): void {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			exportButton: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: () => {
				return {
					content: Array.from({ length: 3 }, (_, index) => ({
						id: index,
						grupoCliente: "Desconto familia Silva",
						classificacaoCliente: "Adolescente",
					})),
				};
			},
			columns: [
				{
					nome: "grupoCliente",
					titulo: this.columnGrupoCliente,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "classificacaoCliente",
					titulo: this.columnClassificacaoCliente,
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: "delete",
					iconClass: "pct pct-trash-2 cor-hellboy05",
					tooltipText: this.traducoes.getLabel("delete-tooltip"),
				},
			],
		});

		this.cd.detectChanges();
	}

	adicionarGrupo(): void {}
}
