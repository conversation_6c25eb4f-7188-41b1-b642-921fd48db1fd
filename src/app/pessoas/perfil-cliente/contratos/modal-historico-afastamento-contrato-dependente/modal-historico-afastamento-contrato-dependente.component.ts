import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { AdmRestService } from "projects/adm/src/app/adm-rest.service";
import { DatePipe } from "@angular/common";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-historico-afastamento-contrato-dependente",
	templateUrl:
		"./modal-historico-afastamento-contrato-dependente.component.html",
	styleUrls: [
		"./modal-historico-afastamento-contrato-dependente.component.scss",
	],
})
export class ModalHistoricoAfastamentoContratoDependenteComponent
	implements OnInit
{
	dadosPessoais;
	contratoDependente;
	@ViewChild("colunaDependenteTitular", { static: true })
	public colunaDependenteTitular;
	@ViewChild("colunaTipoAfastamento", { static: true })
	public colunaTipoAfastamento;
	@ViewChild("tableItens", { static: false })
	public tableItens: RelatorioComponent;
	public table: PactoDataGridConfig;
	temItens = true;
	@Output()
	update = new EventEmitter<any>();

	constructor(
		private modal: NgbActiveModal,
		private snotifyService: SnotifyService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly sessionService: SessionService,
		private readonly cd: ChangeDetectorRef,
		private readonly datePipe: DatePipe,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly admRest: AdmRestService
	) {}

	ngOnInit() {
		this.carregarItens();
	}

	public carregarItens() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlAdmCore(
				`contratos/dependente/${this.contratoDependente.codigo}/afastamentos`
			),
			dataAdapterFn: (serveData) => {
				this.temItens = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "tipoAfastamento",
					titulo: "Tipo",
					visible: true,
					celula: this.colunaTipoAfastamento,
				},
				{
					nome: "dataRegistro",
					titulo: "Dt. Operação",
					visible: true,
					valueTransform: (v) => {
						if (v) {
							return this.datePipe.transform(v, "dd/MM/yyyy HH:mm:ss");
						}
						return v;
					},
				},
				{
					nome: "inicioAfastamento",
					titulo: "Dt. Início",
					visible: true,
					valueTransform: (v) => {
						if (v) {
							return this.datePipe.transform(v, "dd/MM/yyyy");
						}
						return v;
					},
				},
				{
					nome: "finalAfastamento",
					titulo: "Dt. Término",
					visible: true,
					valueTransform: (v) => {
						if (v) {
							return this.datePipe.transform(v, "dd/MM/yyyy");
						}
						return v;
					},
				},
				{
					nome: "observacao",
					titulo: "Observação",
					visible: true,
				},
			],
			actions: [
				{
					nome: "excluirAfastamentos",
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "Excluir afastamento",
					actionFn: (row) => this.excluir(row),
				},
			],
		});
	}

	fecharHandler() {
		this.modal.close();
	}

	excluir(row) {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"EstornoOperacaoContrato_Autorizar",
				"3.24 - Estorno de Operação de Contrato - Autorizar"
			)
			.subscribe(
				(response) => {
					this.admLegadoTelaClienteService
						.excluirAfastamentoContratoDependente(
							this.sessionService.chave,
							parseInt(this.sessionService.empresaId),
							this.sessionService.codigoUsuarioZw,
							row.row.codigo
						)
						.subscribe(
							(resp) => {
								this.snotifyService.success("Afastamento excluído");
								this.update.emit("atualizar_contrato_dependente");
								this.modal.close();
							},
							(httpErrorResponse) => {
								const err = httpErrorResponse.error;
								if (err.meta && err.meta.message) {
									this.snotifyService.error(err.meta.message);
								}
							}
						);
				},
				(httpResponseError) => {
					this.snotifyService.error(httpResponseError.error.meta.message);
				}
			);
	}
}
