<!-- <div class='div-empty' [hidden]="data || dataDependente">
	<pacto-cat-card-plain>
		<div class='d-flex flex-column align-items-center'>
			<img class='icon-empty' src='assets/images/empty-state-clipboard.svg'>
			<div class='text-empty mt-2 body-text-empty mt-3 mb-3'>O aluno ainda não possui nenhum histórico de contratos!</div>

				<pacto-cat-button id="pch-btn-novo-contrato" (click)="novoContrato()" label="Realizar venda de um contrato" size="LARGE"
					[title]="empresa?.permiteContratosConcomintante ? '' : traducoes.getLabel('contrato-concomitante-nao-habilitado')"
					[disabled]="!ableNovoContrato">
				</pacto-cat-button>

		</div>
	</pacto-cat-card-plain>
</div> -->
<div>
	<pacto-cat-card-plain [hidden]="!dataDependente">
		<div style="padding-bottom: 30px">
			<pacto-relatorio
				#tableContratoDependente
				*ngIf="tableDependente"
				[showShare]="false"
				[table]="tableDependente"
				actionTitulo="Ações"
				i18n-tableTitle="@@perfil-cliente:contrato-dependete-table-title"
				idSuffix="pch-table-contrato-depente"
				tableTitle="Histórico de Contratos Compartilhados"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>
	<pacto-cat-card-plain [hidden]="!data">
		<pacto-relatorio
			#tableContrato
			(iconClick)="iconClick($event)"
			(loadedData)="afterLoadData()"
			(rowClick)="openDetails($event.codigo)"
			[customActions]="addContrato"
			[showShare]="false"
			[table]="table"
			actionTitulo="Ações"
			i18n-tableTitle="@@perfil-cliente:contrato-table-title"
			idSuffix="pch-table-contrato"
			tableTitle="Histórico de contratos"></pacto-relatorio>
	</pacto-cat-card-plain>
	<div [hidden]="data" class="contratos-empty">
		<pacto-cat-card-plain>
			<div class="d-flex flex-column align-items-center">
				<img class="icon-empty" src="assets/images/empty-state-clipboard.svg" />
				<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
					O aluno ainda não possui nenhum histórico de contratos!
				</div>

				<pacto-cat-button
					id="pch-btn-novo-contrato-sem-contrato"
					(click)="novoContrato()"
					label="Realizar venda de um contrato"
					size="LARGE"
					[title]="
						apresentarMenuContrato
							? ''
							: traducoes.getLabel('contrato-concomitante-nao-habilitado')
					"
					[disabled]="!apresentarMenuContrato"></pacto-cat-button>
			</div>
		</pacto-cat-card-plain>
	</div>

	<ng-template #colunaVigenciaDe let-item="item">
		<span>{{ item?.vigenciaDe | date : "dd/MM/yyyy" : "UTC" }}</span>
	</ng-template>
	<ng-template #colunaVigenciaAte let-item="item">
		<span>{{ item?.vigenciaAte | date : "dd/MM/yyyy" : "UTC" }}</span>
	</ng-template>
	<ng-template #colunaVigenciaAteAjustada let-item="item">
		<span>{{ item?.vigenciaAteAjustada | date : "dd/MM/yyyy" : "UTC" }}</span>
	</ng-template>

	<ng-template #colunaDependenteTitular let-item="item">
		<span>{{ item?.contrato?.pessoaDTO?.nome | captalize }}</span>
	</ng-template>

	<ng-template #colunaDependenteContrato let-item="item">
		<span>{{ item?.contrato?.codigo }}</span>
	</ng-template>

	<ng-template #colunaDependenteInicio let-item="item">
		<span>{{ item?.dataInicio | date : "dd/MM/yyyy" : "UTC" }}</span>
	</ng-template>

	<ng-template #colunaDependenteFinal let-item="item">
		<span>{{ item?.dataFinalAjustada | date : "dd/MM/yyyy" : "UTC" }}</span>
	</ng-template>

	<ng-template #colunaSituacao let-item="item">
		<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
			<ng-container *ngIf="item.situacao === 'AT'">Ativo</ng-container>
			<ng-container *ngIf="item.situacao === 'FT'">Futuro</ng-container>
			<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
			<ng-container *ngIf="item.situacao === 'IN'">Inativo</ng-container>
			<ng-container *ngIf="item.situacao === 'TR'">Trancado</ng-container>
			<ng-container *ngIf="item.situacao === 'RN'">Renovado</ng-container>
		</div>
	</ng-template>

	<ng-template #colunaPlano let-item="item">
		<span>{{ item?.plano?.descricao }}</span>
	</ng-template>

	<ng-template #colunaDependenteSituacao let-item="item">
		<div [ngClass]="['situacao', item.contrato.situacao.toLowerCase()]">
			<ng-container *ngIf="item.contrato.situacao === 'AT'">Ativo</ng-container>
			<ng-container *ngIf="item.contrato.situacao === 'CA'">
				Cancelado
			</ng-container>
			<ng-container *ngIf="item.contrato.situacao === 'IN'">
				Inativo
			</ng-container>
			<ng-container *ngIf="item.contrato.situacao === 'TR'">
				Trancado
			</ng-container>
			<ng-container *ngIf="item.contrato.situacao === 'RN'">
				Renovado
			</ng-container>
		</div>
	</ng-template>

	<ng-template #colunaRenovarOuRematricular let-item="item">
		<div
			(click)="renovar(item); $event.stopPropagation()"
			*ngIf="item.renovarContrato"
			class="renovar"
			id="pch-renovar-contrato">
			Renovar
		</div>
		<div
			(click)="rematricular(item); $event.stopPropagation()"
			*ngIf="item.rematricularContrato"
			class="rematricular"
			id="pch-rematricular-contrato">
			Rematricular
		</div>
	</ng-template>

	<ng-template #addContrato>
		<button
			ds3-outlined-button
			(click)="openModalConfirmacaoAtualizacaoContrato()"
			class="pct pct-refresh-cw btn-atualizar-contrato"
			size="sm"
			*ngIf="apresentarAtualizarContrato && !mutavelContrato"></button>

		<pacto-cat-button
			id="pch-btn-novo-contrato"
			(click)="novoContrato()"
			label="Novo Contrato"
			size="LARGE"
			[title]="
				apresentarMenuContrato
					? ''
					: traducoes.getLabel('contrato-concomitante-nao-habilitado')
			"
			[disabled]="!apresentarMenuContrato"></pacto-cat-button>
	</ng-template>

	<pacto-traducoes-xingling #traducoes>
		<span
			i18n="@@table-contratos:contrato-concomitante-nao-habilitado"
			xingling="contrato-concomitante-nao-habilitado">
			A configuração de contratos concomitantes está desabilitada para essa
			empresa.
		</span>
	</pacto-traducoes-xingling>
</div>
