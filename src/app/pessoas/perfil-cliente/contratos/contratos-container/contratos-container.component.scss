@import "projects/ui/assets/scss/cores.vars";

.pacto-tela-aluno-contratos-container {
	.situacao {
		width: 107px;
		padding: 5px 28px;
		border-radius: 50px;
		font-size: 12px;
		font-weight: 400;
		line-height: 15px;
		text-align: center;
		color: $cinza05;
		background-color: $cinza01;

		&.at {
			color: $chuchuzinho05;
			background-color: $chuchuzinho01;
		}

		&.ft {
			color: var(--color-support-blue-0);
			background-color: var(--color-support-blue-4);
		}

		&.tr {
			color: $laranjinha05;
			background-color: $laranjinha01;
		}

		&.in {
			color: $cinza05;
			background-color: $cinza01;
		}

		&.ca {
			color: $hellboy05;
			background-color: $hellboy01;
		}

		&.rn {
			color: $azulim05;
			background-color: $azulim01;
		}
	}

	.renovar,
	.rematricular {
		text-align: end;
		color: #0380e3;
		font-size: 0.875rem;
		font-weight: 700;
		cursor: pointer;
	}

	.btn-atualizar-contrato {
		min-height: 40px !important;
		margin-right: 4px;
	}

	// INICIO | CUSTOMIZAÇÕES
	pacto-cat-card-plain {
		padding: 1rem !important;
		margin-bottom: 1rem;
	}

	pacto-relatorio {
		.pacto-table-title-block {
			padding-top: 0;
			padding-left: 0;
			padding-right: 0;

			.table-title {
				color: #51555a;
				font-size: 14px;
			}
		}

		.table-content {
			padding: 0;
		}

		.table-content table.table {
			padding-left: 0;
			padding-right: 0;

			th,
			.column-title {
				font-size: 14px;
				font-weight: 700;
			}

			td {
				font-size: 12px;

				i {
					font-size: 16px;
				}
			}
		}
	}

	// FIM | CUSTOMIZAÇÕES
}
