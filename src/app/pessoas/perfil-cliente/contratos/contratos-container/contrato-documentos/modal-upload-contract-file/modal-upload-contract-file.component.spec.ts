import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalUploadContractFileComponent } from "./modal-upload-contract-file.component";

describe("ModalUploadContractFileComponent", () => {
	let component: ModalUploadContractFileComponent;
	let fixture: ComponentFixture<ModalUploadContractFileComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalUploadContractFileComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalUploadContractFileComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
