import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { CatFileInputComponent, DialogService, PactoModalSize } from "ui-kit";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-upload-contract-file",
	templateUrl: "./modal-upload-contract-file.component.html",
	styleUrls: ["./modal-upload-contract-file.component.scss"],
})
export class ModalUploadContractFileComponent implements OnInit {
	@ViewChild("fileInputComponent", { static: true })
	fileInputComponent: CatFileInputComponent;
	data;
	imagens;
	codigoContrato;
	contratoDocumentosComponent;
	arquivosModificados;

	controlFile = new FormControl();
	controlFileName = new FormControl();

	constructor(
		private dialog: NgbActiveModal,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.controlFile.valueChanges.subscribe((v) => {
			this.cd.detectChanges();
		});
	}

	changeFile() {
		if (this.fileInputComponent) {
			this.fileInputComponent.reset();
		} else {
			this.controlFileName.reset();
			this.controlFile.reset();
		}
	}

	save() {
		if (this.controlFile.value && this.controlFile.value !== "") {
			this.dialog.close();
			this.data.fileUrl = this.controlFile.value;
			const splited = this.controlFile.value
				.substr(0, this.controlFile.value.search(";"))
				.split("/");
			this.data.fileFormat = splited[splited.length - 1].toUpperCase();
			const dialogRef = this.dialogService.open(
				`Documentos associados ao contrato ${this.codigoContrato}`,
				this.contratoDocumentosComponent,
				PactoModalSize.LARGE
			);
			dialogRef.componentInstance.rowSharedBetweenModal = this.data;
			dialogRef.componentInstance.codigoContrato = this.codigoContrato;
			dialogRef.componentInstance.imagens = this.imagens;
			dialogRef.componentInstance.arquivosModificados =
				this.arquivosModificados;
			this.notificationService.info("Clique em salvar alterações");
		}
	}

	goBack() {
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			`Documentos associados ao contrato ${this.codigoContrato}`,
			this.contratoDocumentosComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.rowSharedBetweenModal = this.data;
		dialogRef.componentInstance.imagens = this.imagens;
		dialogRef.componentInstance.arquivosModificados = this.arquivosModificados;
	}
}
