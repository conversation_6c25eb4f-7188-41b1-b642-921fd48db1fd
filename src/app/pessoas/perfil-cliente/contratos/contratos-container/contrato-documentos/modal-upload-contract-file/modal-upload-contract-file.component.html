<div class="modal-upload-contract-file">
	<pacto-cat-file-input
		#fileInputComponent
		*ngIf="!controlFile.value || controlFile.value === ''"
		[control]="controlFile"
		[nomeControl]="controlFileName"></pacto-cat-file-input>

	<div
		*ngIf="controlFile.value && controlFile.value !== ''"
		class="mucf-selected-img">
		<img [alt]="controlFileName.value" [src]="controlFile.value" />
	</div>

	<div class="mucf-btn-row">
		<pacto-cat-button
			(click)="goBack()"
			label="Voltar"
			size="LARGE"
			style="margin-right: 16px"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="changeFile()"
			*ngIf="controlFile.value && controlFile.value !== ''"
			label="Substituir arquivo"
			size="LARGE"
			style="margin-right: 16px"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="save()"
			[disabled]="!controlFile.value || controlFile.value === ''"
			label="Salvar alteração"
			size="LARGE"></pacto-cat-button>
	</div>
</div>
