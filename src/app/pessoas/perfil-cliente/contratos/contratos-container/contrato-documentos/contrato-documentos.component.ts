import {
	ChangeDete<PERSON><PERSON><PERSON>,
	<PERSON>mpo<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	On<PERSON><PERSON>t,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import {
	AdmLegadoTelaClienteService,
	ApiResponseSingle,
	ContratoAssinaturaDigitalModel,
	LinkAcessoRapidoService,
} from "adm-legado-api";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
} from "ui-kit";
import { Subscription } from "rxjs";
import { ModalUploadContractFileComponent } from "./modal-upload-contract-file/modal-upload-contract-file.component";
import { ModalCameraContractFileComponent } from "./modal-camera-contract-file/modal-camera-contract-file.component";
import { ModalViewContractFileComponent } from "./modal-view-contract-file/modal-view-contract-file.component";
import { SnotifyService } from "ng-snotify";

enum ContratoDocumentoActions {
	UPLOAD = "upload",
	ALTERAR_DOCUMENTOS = "alterar_documentos",
	REMOVER_DOCUMENTO = "remover_documento",
	VISULIZAR_DOCUMENTO = "visualizar_documento",
}

enum DocumentosType {
	DOCUMENTOS = "documentos",
	ENDERECO = "endereco",
	ATESTADO = "atestado",
	ANEXO1 = "anexo1",
	ANEXO2 = "anexo2",
	ANEXO_CANCELAMENTO = "anexoCancelamento",
}

@Component({
	selector: "pacto-contrato-documentos",
	templateUrl: "./contrato-documentos.component.html",
	styleUrls: ["./contrato-documentos.component.scss"],
})
export class ContratoDocumentosComponent implements OnInit, OnDestroy {
	codigoContrato;
	rowSharedBetweenModal;
	table: PactoDataGridConfig;
	link = "#";
	qrcode = "";
	imagensOriginal: ContratoAssinaturaDigitalModel;
	imagens: Array<any> = new Array<any>(
		{
			id: DocumentosType.DOCUMENTOS,
			label: "Documentos",
		},
		{
			id: DocumentosType.ENDERECO,
			label: "Endereço",
		},
		{
			id: DocumentosType.ATESTADO,
			label: "Atestado",
		},
		{
			id: DocumentosType.ANEXO1,
			label: "Anexo 01",
		},
		{
			id: DocumentosType.ANEXO2,
			label: "Anexo 02",
		},
		{
			id: DocumentosType.ANEXO_CANCELAMENTO,
			label: "Anexo cancelamento",
		}
	);

	subscriptions = new Array<Subscription>();
	arquivosModificados = [];

	@ViewChild("tableComponent", { static: true })
	tableComponent: RelatorioComponent;

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private linkAcessoRapidoService: LinkAcessoRapidoService,
		private cd: ChangeDetectorRef,
		private dialogService: DialogService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.createTable();
		this.obterDocsAssinados();
		const subscription = this.linkAcessoRapidoService
			.obterLinkAppsPacto(
				this.sessionService.chave,
				this.sessionService.empresaId,
				this.sessionService.codigoUsuarioZw.toString(),
				"assinaturadigital",
				"inicio"
			)
			.subscribe((response: any) => {
				let sufix = "";
				if (!response.content.linkAcesso.startsWith("http")) {
					sufix = this.sessionService.pathUrlZw;
				}
				this.link = sufix + response.content.linkAcesso;
				this.qrcode = sufix + response.content.qrCodeAcesso;
				this.cd.detectChanges();
			});

		this.subscriptions.push(subscription);
	}

	ngOnDestroy() {
		this.subscriptions.forEach((s) => s.unsubscribe());
	}

	lista() {
		const listaNova = [];
		for (let i = 0; i < this.imagens.length; i++) {
			if (
				this.imagens[i].id !== "anexoCancelamento" ||
				(this.imagens[i].id === "anexoCancelamento" &&
					this.imagens[i].fileUrl &&
					this.imagens[i].fileUrl.length > 0)
			) {
				listaNova.push(this.imagens[i]);
			}
		}
		return listaNova;
	}

	createTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.lista(),
				};
			},
			pagination: false,
			showFilters: false,
			columns: [
				{
					nome: "label",
					titulo: "Arquivos associados",
					visible: true,
				},
				{
					nome: "fileFormat",
					titulo: "Formato",
					visible: true,
				},
			],
			actions: [
				{
					iconClass: "pct pct-image cor-azulim-pri",
					nome: ContratoDocumentoActions.VISULIZAR_DOCUMENTO,
					tooltipText: "Visualizar",
					showIconFn: (row) => row.fileUrl && row.fileUrl !== "",
				},
				{
					iconClass: "pct pct-upload cor-azulim-pri",
					nome: ContratoDocumentoActions.UPLOAD,
					tooltipText: "Alterar - Upload Arquivo",
					showIconFn: (row) => row.id !== DocumentosType.ANEXO_CANCELAMENTO,
				},
				{
					iconClass: "pct pct-camera cor-azulim-pri",
					nome: ContratoDocumentoActions.ALTERAR_DOCUMENTOS,
					tooltipText: "Alterar - Câmera",
					showIconFn: (row) => row.id !== DocumentosType.ANEXO_CANCELAMENTO,
				},
				{
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					nome: ContratoDocumentoActions.REMOVER_DOCUMENTO,
					tooltipText: "Excluir",
					showIconFn: (row) =>
						row.id !== DocumentosType.ANEXO_CANCELAMENTO &&
						row.fileUrl &&
						row.fileUrl !== "",
				},
			],
		});
	}

	iconClick(event: { row: any; iconName: string }) {
		switch (event.iconName) {
			case ContratoDocumentoActions.ALTERAR_DOCUMENTOS:
				this.alterarArquivo(event.row);
				break;
			case ContratoDocumentoActions.REMOVER_DOCUMENTO:
				this.deleteDocumento(event.row);
				break;
			case ContratoDocumentoActions.VISULIZAR_DOCUMENTO:
				this.viewFile(event.row);
				break;
			case ContratoDocumentoActions.UPLOAD:
				this.uploadFile(event.row);
				break;
		}
	}

	obterDocsAssinados() {
		const sub = this.telaClienteService
			.obterDocumentosContrato(this.sessionService.chave, this.codigoContrato)
			.subscribe(
				(response: ApiResponseSingle<ContratoAssinaturaDigitalModel>) => {
					const content = response.content;
					this.imagensOriginal = response.content;
					if (!this.rowSharedBetweenModal) {
						this.imagens.forEach((img) => {
							switch (img.id) {
								case DocumentosType.DOCUMENTOS:
									if (content.documentos) {
										img.fileUrl = content.documentos;
									}
									break;
								case DocumentosType.ENDERECO:
									if (content.endereco) {
										img.fileUrl = content.endereco;
									}
									break;
								case DocumentosType.ATESTADO:
									if (content.atestado) {
										img.fileUrl = content.atestado;
									}
									break;
								case DocumentosType.ANEXO1:
									if (content.anexo1) {
										img.fileUrl = content.anexo1;
									}
									break;
								case DocumentosType.ANEXO2:
									if (content.anexo2) {
										img.fileUrl = content.anexo2;
									}
									break;
								case DocumentosType.ANEXO_CANCELAMENTO:
									if (content.anexoCancelamento) {
										img.fileUrl = content.anexoCancelamento;
									}
									break;
							}
							if (img.fileUrl && !this.rowSharedBetweenModal) {
								const strs = img.fileUrl.split(".");
								img.fileFormat = strs[strs.length - 1].toUpperCase();
							}
							if (
								this.rowSharedBetweenModal &&
								this.rowSharedBetweenModal.id === img.id
							) {
								Object.assign(img, this.rowSharedBetweenModal);
								this.rowSharedBetweenModal = undefined;
							}
						});
					}
					if (this.tableComponent) {
						this.tableComponent.reloadData();
					}
				}
			);
		this.subscriptions.push(sub);
	}

	uploadFile(row) {
		this.arquivosModificados.push(row.label);
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			"Upload arquivo",
			ModalUploadContractFileComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.data = row;
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.imagens = this.imagens;
		dialogRef.componentInstance.arquivosModificados = this.arquivosModificados;
		dialogRef.componentInstance.contratoDocumentosComponent =
			ContratoDocumentosComponent;
	}

	alterarArquivo(row) {
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			"Alterar imagem do documento",
			ModalCameraContractFileComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.data = row;
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.imagens = this.imagens;
		dialogRef.componentInstance.contratoDocumentosComponent =
			ContratoDocumentosComponent;
	}

	viewFile(row) {
		if (
			this.arquivosModificados &&
			this.arquivosModificados.includes(row.label) &&
			row.fileFormat &&
			row.fileFormat === "PDF"
		) {
			this.notificationService.info(
				"Clique em salvar alterações para poder visualizar o arquivo"
			);
			return;
		}
		if (row.fileUrl && row.fileFormat && row.fileFormat === "PDF") {
			window.open(row.fileUrl, "_blank");
		} else {
			this.dialog.close();
			const dialogRef = this.dialogService.open(
				"Upload arquivo",
				ModalViewContractFileComponent,
				PactoModalSize.LARGE
			);
			dialogRef.componentInstance.data = row;
			dialogRef.componentInstance.codigoContrato = this.codigoContrato;
			dialogRef.componentInstance.imagens = this.imagens;
			dialogRef.componentInstance.arquivosModificados =
				this.arquivosModificados;
			dialogRef.componentInstance.contratoDocumentosComponent =
				ContratoDocumentosComponent;
		}
	}

	deleteDocumento(row) {
		const arquivo = this.imagens.find((i) => i.id === row.id);
		if (arquivo) {
			arquivo.fileUrl = null;
			arquivo.fileFormat = null;

			this.tableComponent.reloadData();
		}
	}

	save() {
		const documentos = this.imagens.find(
			(i) => i.id === DocumentosType.DOCUMENTOS
		);
		if (documentos) {
			if (!this.isValidUrl(documentos.fileUrl)) {
				documentos.fileUrl = this.replaceImageData64(documentos.fileUrl);
			}
		}
		const endereco = this.imagens.find((i) => i.id === DocumentosType.ENDERECO);
		if (endereco) {
			if (!this.isValidUrl(endereco.fileUrl)) {
				endereco.fileUrl = this.replaceImageData64(endereco.fileUrl);
			}
		}
		const anexo1 = this.imagens.find((i) => i.id === DocumentosType.ANEXO1);
		if (anexo1) {
			if (!this.isValidUrl(anexo1.fileUrl)) {
				anexo1.fileUrl = this.replaceImageData64(anexo1.fileUrl);
			}
		}
		const anexo2 = this.imagens.find((i) => i.id === DocumentosType.ANEXO2);
		if (anexo2) {
			if (!this.isValidUrl(anexo2.fileUrl)) {
				anexo2.fileUrl = this.replaceImageData64(anexo2.fileUrl);
			}
		}
		const atestado = this.imagens.find((i) => i.id === DocumentosType.ATESTADO);
		if (atestado) {
			if (!this.isValidUrl(atestado.fileUrl)) {
				atestado.fileUrl = this.replaceImageData64(atestado.fileUrl);
			}
		}

		const data: ContratoAssinaturaDigitalModel = {
			token: "TelaCliente:" + this.sessionService.chave,
			operacao: "salvarImagens",
			contrato: this.codigoContrato,
			documentos: documentos.fileUrl,
			documentosFormat: documentos.fileFormat,
			documentosUpdate: this.imagensOriginal.documentos !== documentos.fileUrl,
			endereco: endereco.fileUrl,
			enderecoFormat: endereco.fileFormat,
			enderecoUpdate: this.imagensOriginal.endereco !== endereco.fileUrl,
			atestado: atestado.fileUrl,
			atestadoFormat: atestado.fileFormat,
			atestadoUpdate: this.imagensOriginal.atestado !== atestado.fileUrl,
			anexo1: anexo1.fileUrl,
			anexo1Format: anexo1.fileFormat,
			anexo1Update: this.imagensOriginal.anexo1 !== anexo1.fileUrl,
			anexo2: anexo2.fileUrl,
			anexo2Format: anexo2.fileFormat,
			anexo2Update: this.imagensOriginal.anexo2 !== anexo2.fileUrl,
			usuario: this.sessionService.loggedUser.usuarioZw,
		};
		this.telaClienteService.upload(this.sessionService.chave, data).subscribe(
			(response) => {
				this.notificationService.success("Imagens salvas com sucesso!");
				this.dialog.close();
			},
			(httpResponseError) => {
				if (httpResponseError.error) {
					if (httpResponseError.error.meta) {
						this.notificationService.error(
							httpResponseError.error.meta.message
						);
					}
				}
			}
		);
	}

	undo() {
		this.rowSharedBetweenModal = undefined;
		this.imagens.forEach((img) => {
			img.fileUrl = undefined;
			img.fileFormat = undefined;
		});
		this.obterDocsAssinados();
	}

	isValidUrl(str) {
		// Regular expression pattern for URL validation
		const pattern = new RegExp(
			"^(https?:\\/\\/)?" + // protocol
				"((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
				"((\\d{1,3}\\.){3}\\d{1,3}))" + // OR IP (v4) address
				"(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
				"(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
				"(\\#[-a-z\\d_]*)?$",
			"i"
		); // fragment locator

		return pattern.test(str);
	}

	replaceImageData64(img) {
		if (img) {
			// return img.replace(/^data:image\/(png|jpg|jpeg|pdf);base64,/, '');
			return img;
		}
	}
}
