.modal-contrato-documentos {
	padding: 24px;

	::ng-deep {
		pacto-relatorio {
			display: block;
			margin-bottom: 38px;

			.table-content {
				padding: unset;

				pacto-relatorio-renderer {
					table {
						th {
							font-size: 14px;
						}

						td {
							font-size: 12px;

							i {
								font-size: 16px;
							}
						}

						tbody {
							tr {
								td {
									&.action-cell {
										text-align: right;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.mcd-row {
		display: flex;

		.mcd-qrcode-section {
			.mcd-qrcode-title {
				font-weight: 700;
				line-height: 20px;
			}

			.mcd-qrcode-text {
				font-weight: 400;
				line-height: 20px;
				margin-bottom: 24px;
			}

			.mcd-qrcode-link {
				font-weight: 700;
				line-height: 20px;
			}
		}

		.mcd-qrcode {
			margin-left: 15px;
			height: 149px;
			width: 157px;

			img {
				width: inherit;
				height: inherit;
			}
		}
	}

	.mcd-btn-row {
		display: flex;
		justify-content: flex-end;
		margin-top: 24px;

		#mcd-btn-undo {
			margin-right: 24px;
		}
	}
}
