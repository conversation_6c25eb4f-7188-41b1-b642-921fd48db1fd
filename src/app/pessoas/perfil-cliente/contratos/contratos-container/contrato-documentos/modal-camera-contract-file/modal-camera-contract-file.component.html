<div class="modal-camera-contract-file">
	<pacto-cat-camera (images)="captureImages($event)" *ngIf="enableCapture">
		<pacto-cat-button
			(click)="goBack()"
			label="Voltar"
			size="LARGE"
			style="margin-right: 16px"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			label="Capturar imagem"
			pactoCatCameraCapture
			size="LARGE"></pacto-cat-button>
	</pacto-cat-camera>
	<ng-container *ngIf="!enableCapture">
		<img [src]="imageData" alt="Imagem capturada" />
		<div class="mccf-btn-row">
			<pacto-cat-button
				(click)="goBack()"
				label="Voltar"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="captureAnotherImage()"
				label="Capturar outra imagem"
				size="LARGE"
				style="margin-right: 16px; margin-left: 16px"></pacto-cat-button>

			<pacto-cat-button
				(click)="save()"
				label="Salvar alterações"
				size="LARGE"></pacto-cat-button>
		</div>
	</ng-container>
</div>
