import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalCameraContractFileComponent } from "./modal-camera-contract-file.component";

describe("ModalCameraContractFileComponent", () => {
	let component: ModalCameraContractFileComponent;
	let fixture: ComponentFixture<ModalCameraContractFileComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalCameraContractFileComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalCameraContractFileComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
