import { Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogService, PactoModalSize } from "ui-kit";

@Component({
	selector: "pacto-modal-camera-contract-file",
	templateUrl: "./modal-camera-contract-file.component.html",
	styleUrls: ["./modal-camera-contract-file.component.scss"],
})
export class ModalCameraContractFileComponent implements OnInit {
	data;
	codigoContrato;
	enableCapture = true;
	imageData;
	imagens;
	contratoDocumentosComponent;

	constructor(
		private dialog: NgbActiveModal,
		private dialogService: DialogService
	) {}

	ngOnInit() {}

	goBack() {
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			`Documentos associados ao contrato ${this.codigoContrato}`,
			this.contratoDocumentosComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.rowSharedBetweenModal = this.data;
		dialogRef.componentInstance.imagens = this.imagens;
	}

	captureImages(event: any) {
		console.log(event);
		console.log(this.data);
		this.enableCapture = false;
		this.imageData = event;
	}

	captureAnotherImage() {
		this.imageData = undefined;
		this.enableCapture = true;
	}

	save() {
		this.dialog.close();
		this.data.fileUrl = this.imageData;
		const splited = this.imageData
			.substr(0, this.imageData.search(";"))
			.split("/");
		this.data.fileFormat = splited[splited.length - 1].toUpperCase();
		const dialogRef = this.dialogService.open(
			`Documentos associados ao contrato ${this.codigoContrato}`,
			this.contratoDocumentosComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.rowSharedBetweenModal = this.data;
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.imagens = this.imagens;
	}
}
