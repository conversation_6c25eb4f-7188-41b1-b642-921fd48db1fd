import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalCameraContractFileComponent } from "../modal-camera-contract-file/modal-camera-contract-file.component";
import { DialogService, PactoModalSize } from "ui-kit";
import { ModalUploadContractFileComponent } from "../modal-upload-contract-file/modal-upload-contract-file.component";

@Component({
	selector: "pacto-modal-view-contract-file",
	templateUrl: "./modal-view-contract-file.component.html",
	styleUrls: ["./modal-view-contract-file.component.scss"],
})
export class ModalViewContractFileComponent implements OnInit {
	data;
	codigoContrato;
	imagens;
	contratoDocumentosComponent;
	arquivosModificados;

	constructor(
		private dialog: NgbActiveModal,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.cd.detectChanges();
	}

	close() {
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			`Documentos associados ao contrato ${this.codigoContrato}`,
			this.contratoDocumentosComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.rowSharedBetweenModal = this.data;
		dialogRef.componentInstance.imagens = this.imagens;
		dialogRef.componentInstance.arquivosModificados = this.arquivosModificados;
	}

	changeToCamera() {
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			"Alterar imagem do documento",
			ModalCameraContractFileComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.data = this.data;
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.imagens = this.imagens;
		dialogRef.componentInstance.arquivosModificados = this.arquivosModificados;
		dialogRef.componentInstance.contratoDocumentosComponent =
			this.contratoDocumentosComponent;
	}

	changeToUpload() {
		this.dialog.close();
		const dialogRef = this.dialogService.open(
			"Upload arquivo",
			ModalUploadContractFileComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.data = this.data;
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.imagens = this.imagens;
		dialogRef.componentInstance.arquivosModificados = this.arquivosModificados;
		dialogRef.componentInstance.contratoDocumentosComponent =
			this.contratoDocumentosComponent;
	}
}
