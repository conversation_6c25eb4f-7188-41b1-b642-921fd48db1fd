import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalViewContractFileComponent } from "./modal-view-contract-file.component";

describe("ModalViewContractFileComponent", () => {
	let component: ModalViewContractFileComponent;
	let fixture: ComponentFixture<ModalViewContractFileComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalViewContractFileComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalViewContractFileComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
