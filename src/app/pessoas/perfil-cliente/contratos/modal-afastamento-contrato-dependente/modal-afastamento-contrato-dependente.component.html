<div class="geral">
	<div *ngIf="ferias">
		<div class="col-sm-12 col-lg-12 col-md-12">
			<h4>Detalhe de férias</h4>
		</div>
		<div class="d-flex">
			<div class="col-sm-12 col-lg-3 col-md-3">
				<h4>Dias <PERSON>tai<PERSON></h4>
				<h5>{{ info?.diasUtilizados }}</h5>
			</div>
			<div class="col-sm-12 col-lg-3 col-md-3">
				<h4>Dias Utilizados</h4>
				<h5>{{ info?.diasUtilizados }}</h5>
			</div>
			<div class="col-sm-12 col-lg-3 col-md-3">
				<h4>Dias disponíveis</h4>
				<h5>{{ info?.diasRestantes }}</h5>
			</div>
			<div class="col-sm-12 col-lg-3 col-md-3">
				<h4>Mínimo de dias para solicitar férias</h4>
				<h5>{{ info?.diasMinimoSolicitar }}</h5>
			</div>
		</div>
	</div>
	<div>
		<div class="col-sm-12 col-lg-12 col-md-12">
			<h4 *ngIf="ferias">Definir período de férias</h4>
			<h4 *ngIf="!ferias">Definir período do atestado</h4>
		</div>

		<div class="d-flex">
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('dtInicio')"
					[label]="ferias ? 'Início de férias' : 'Início do atestado'"
					errorMsg="Campo obrigatório"
					i18n-label="@@modal-afastamento-contrato-dependente:data-inicio"
					id="data-inicio"></pacto-cat-form-datepicker>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-input
					(change)="alterouNrDias()"
					[control]="form.get('nrDias')"
					[label]="'Nº de dias'"
					errorMsg="Campo obrigatório"
					i18n-label="
						@@modal-afastamento-contrato-dependente:nrdias"></pacto-cat-form-input>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('dtFim')"
					[label]="ferias ? 'Fim de férias' : 'Fim do atestado'"
					errorMsg="Campo obrigatório"
					i18n-label="@@modal-afastamento-contrato-dependente:data-final"
					id="data-final"></pacto-cat-form-datepicker>
			</div>
		</div>

		<div class="d-flex">
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-select-filter
					[control]="form.get('justificativa')"
					[endpointUrl]="justificativaUrl"
					[label]="'Justificativa'"
					[paramBuilder]="justificativaParamBuilder"
					errorMsg="Campo obrigatório"
					idKey="codigo"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
			<div class="col-sm-12 col-lg-8 col-md-8">
				<pacto-cat-form-input
					[control]="form.get('observacao')"
					[id]="'input-afastamento-contrato-dependente-observacao'"
					[label]="'Observação'"></pacto-cat-form-input>
			</div>
		</div>
	</div>
	<div class="col-12 inferior text-lg-right">
		<pacto-cat-button
			(click)="fecharHandler()"
			id="obj-btn-cancelar"
			label="Cancelar"
			size="NORMAL"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			(click)="confirmar()"
			id="obj-btn-confirmar"
			label="Confirmar"
			size="NORMAL"
			style="padding-left: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
