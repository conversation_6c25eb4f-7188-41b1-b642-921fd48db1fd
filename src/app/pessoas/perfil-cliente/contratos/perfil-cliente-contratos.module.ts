import { CommonModule } from "@angular/common";
import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { NgbDropdownModule } from "@ng-bootstrap/ng-bootstrap";
import { CatTolltipModule, UiModule } from "ui-kit";
import { AlterarVencimentoParcelasComponent } from "./alterar-vencimento-parcelas/alterar-vencimento-parcelas.component";
import { AfastamentoDropdownComponent } from "./components/afastamento-dropdown/afastamento-dropdown.component";
import { ModalContratosRenovarERematricularComponent } from "./components/modal-contratos-renovar-e-rematricular/modal-contratos-renovar-e-rematricular.component";
import { ContratoDocumentosComponent } from "./contratos-container/contrato-documentos/contrato-documentos.component";
import { ModalCameraContractFileComponent } from "./contratos-container/contrato-documentos/modal-camera-contract-file/modal-camera-contract-file.component";
import { ModalUploadContractFileComponent } from "./contratos-container/contrato-documentos/modal-upload-contract-file/modal-upload-contract-file.component";
import { ModalViewContractFileComponent } from "./contratos-container/contrato-documentos/modal-view-contract-file/modal-view-contract-file.component";
import { ContratosContainerComponent } from "./contratos-container/contratos-container.component";
import { AfastamentoContratoComponent } from "./detalhamento-contrato/afastamento-contrato/afastamento-contrato.component";
import { AlterarHorarioContratoComponent } from "./detalhamento-contrato/alterar-horario-contrato/alterar-horario-contrato.component";
import { BonusContratoComponent } from "./detalhamento-contrato/bonus-contrato/bonus-contrato.component";
import { DetalhamentoContratoHistoricoComprasComponent } from "./detalhamento-contrato/detalhamento-contrato-historico-compras/detalhamento-contrato-historico-compras.component";
import { DetalhamentoContratoHistoricoContratoComponent } from "./detalhamento-contrato/detalhamento-contrato-historico-contrato/detalhamento-contrato-historico-contrato.component";
import { CadastrarControleCreditoTreinoComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/detalhamento-contrato-extrato-credito-treino/cadastrar-controle-credito-treino/cadastrar-controle-credito-treino.component";
import { DetalhamentoContratoExtratoCreditoTreinoComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/detalhamento-contrato-extrato-credito-treino/detalhamento-contrato-extrato-credito-treino.component";
import { DetalhamentoContratoModalidadesComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/detalhamento-contrato-modalidades.component";
import { ModalAulasDesmarcadasComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/modal-aulas-desmarcadas/modal-aulas-desmarcadas.component";
import { ModalDesmarcarAulaContratoComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/modal-desmarcar-aula-contrato/modal-desmarcar-aula-contrato.component";
import { ModalFaltasComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/modal-faltas/modal-faltas.component";
import { ModalHistoricoFaltasComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/modal-historico-faltas/modal-historico-faltas.component";
import { ModalReposicoesAulaComponent } from "./detalhamento-contrato/detalhamento-contrato-modalidades/modal-reposicoes-aula/modal-reposicoes-aula.component";
import { DetalhamentoContratoOperacoesContratoComponent } from "./detalhamento-contrato/detalhamento-contrato-operacoes-contrato/detalhamento-contrato-operacoes-contrato.component";
import { DetalhesDaOperacaoComponent } from "./detalhamento-contrato/detalhamento-contrato-operacoes-contrato/detalhes-da-operacao/detalhes-da-operacao.component";
import { DetalhamentoContratoVisaoGeralComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/detalhamento-contrato-visao-geral.component";
import { LiberarVagaTurmaComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/liberar-vaga-turma/liberar-vaga-turma.component";
import { ModalEditarDadosContratoComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/modal-editar-dados-contrato/modal-editar-dados-contrato.component";
import { RecuperarContratoComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/recuperar-contrato/recuperar-contrato.component";
import { RetornoAtestadoComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/retorno-atestado/retorno-atestado.component";
import { RetornoFeriasComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/retorno-ferias/retorno-ferias.component";
import { RetornoTrancamentoComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/retorno-trancamento/retorno-trancamento.component";
import { TransferirContratoComponent } from "./detalhamento-contrato/detalhamento-contrato-visao-geral/transferir-contrato/transferir-contrato.component";
import { DetalhamentoContratoComponent } from "./detalhamento-contrato/detalhamento-contrato.component";
import { EstornoContratoComponent } from "./detalhamento-contrato/estorno-contrato/estorno-contrato.component";
import { HistoricoChequeComponent } from "./detalhamento-contrato/historico-cheque/historico-cheque.component";
import { EnviarReciboComponent } from "./detalhamento-contrato/historico-de-pagamentos-efetuados/enviar-recibo/enviar-recibo.component";
import { HistoricoDePagamentosEfetuadosComponent } from "./detalhamento-contrato/historico-de-pagamentos-efetuados/historico-de-pagamentos-efetuados.component";
import { HistoricoDeParcelasGeradasComponent } from "./detalhamento-contrato/historico-de-parcelas-geradas/historico-de-parcelas-geradas.component";
import { ManutencaoModalidadeContratoComponent } from "./detalhamento-contrato/manutencao-modalidade-contrato/manutencao-modalidade-contrato.component";
import { EnviarContratoEmailComponent } from "./enviar-contrato-email/enviar-contrato-email.component";
import { ImprimirContratoComponent } from "./imprimir-contrato/imprimir-contrato.component";
import { ModalAfastamentoContratoDependenteComponent } from "./modal-afastamento-contrato-dependente/modal-afastamento-contrato-dependente.component";
import { ModalHistoricoAfastamentoContratoDependenteComponent } from "./modal-historico-afastamento-contrato-dependente/modal-historico-afastamento-contrato-dependente.component";
import { ModalAdicionarContratoDependenteComponent } from "./detalhamento-contrato/modal-adicionar-contrato-dependente/modal-adicionar-contrato-dependente.component";
import { DetalhamentoContratoDependenteComponent } from "./detalhamento-contrato/detalhamento-contrato-dependente/detalhamento-contrato-dependente.component";
import { ModalLogConciliadoraComponent } from "../financeiro/financeiro-recibos/modal-log-conciliadora/modal-log-conciliadora.component";
import { ModalAlterarVencimentoParcelasContratoComponent } from "./modal-alterar-vencimento-parcelas-contrato/modal-alterar-vencimento-parcelas-contrato.component";
import { ModalAlterarPlanoContratoComponent } from "./modal-alterar-plano-contrato/modal-alterar-plano-contrato.component";
import { PerfilClienteSharedModule } from "../perfil-cliente-shared/perfil-cliente-shared.module";
import { ModalAtualizarContratoComponent } from "./modal-atualizar-contrato/modal-atualizar-contrato.component";

@NgModule({
	declarations: [
		ContratosContainerComponent,
		DetalhamentoContratoComponent,
		DetalhamentoContratoVisaoGeralComponent,
		DetalhamentoContratoModalidadesComponent,
		DetalhamentoContratoOperacoesContratoComponent,
		DetalhamentoContratoHistoricoComprasComponent,
		DetalhamentoContratoHistoricoContratoComponent,
		AfastamentoDropdownComponent,
		AfastamentoContratoComponent,
		EstornoContratoComponent,
		AlterarHorarioContratoComponent,
		BonusContratoComponent,
		ManutencaoModalidadeContratoComponent,
		ImprimirContratoComponent,
		EnviarContratoEmailComponent,
		AlterarVencimentoParcelasComponent,
		ModalContratosRenovarERematricularComponent,
		ContratoDocumentosComponent,
		ModalViewContractFileComponent,
		ModalUploadContractFileComponent,
		ModalCameraContractFileComponent,
		ModalDesmarcarAulaContratoComponent,
		ModalReposicoesAulaComponent,
		ModalAulasDesmarcadasComponent,
		ModalFaltasComponent,
		ModalEditarDadosContratoComponent,
		TransferirContratoComponent,
		RecuperarContratoComponent,
		DetalhesDaOperacaoComponent,
		RetornoAtestadoComponent,
		RetornoTrancamentoComponent,
		RetornoFeriasComponent,
		LiberarVagaTurmaComponent,
		ModalHistoricoFaltasComponent,
		HistoricoDePagamentosEfetuadosComponent,
		HistoricoDeParcelasGeradasComponent,
		EnviarReciboComponent,
		DetalhamentoContratoExtratoCreditoTreinoComponent,
		CadastrarControleCreditoTreinoComponent,
		HistoricoChequeComponent,
		ModalAfastamentoContratoDependenteComponent,
		ModalHistoricoAfastamentoContratoDependenteComponent,
		ModalAdicionarContratoDependenteComponent,
		DetalhamentoContratoDependenteComponent,
		ModalLogConciliadoraComponent,
		ModalAlterarVencimentoParcelasContratoComponent,
		ModalAlterarPlanoContratoComponent,
		ModalAtualizarContratoComponent,
	],
	imports: [
		CommonModule,
		UiModule,
		NgbDropdownModule,
		ReactiveFormsModule,
		CatTolltipModule,
		BaseSharedModule,
		PerfilClienteSharedModule,
	],
	entryComponents: [
		AfastamentoContratoComponent,
		EstornoContratoComponent,
		AlterarHorarioContratoComponent,
		BonusContratoComponent,
		ManutencaoModalidadeContratoComponent,
		EnviarContratoEmailComponent,
		AlterarVencimentoParcelasComponent,
		ModalContratosRenovarERematricularComponent,
		ContratoDocumentosComponent,
		ModalViewContractFileComponent,
		ModalUploadContractFileComponent,
		ModalCameraContractFileComponent,
		ModalDesmarcarAulaContratoComponent,
		ModalReposicoesAulaComponent,
		ModalAulasDesmarcadasComponent,
		ModalFaltasComponent,
		ModalEditarDadosContratoComponent,
		TransferirContratoComponent,
		RecuperarContratoComponent,
		DetalhesDaOperacaoComponent,
		RetornoAtestadoComponent,
		RetornoTrancamentoComponent,
		RetornoFeriasComponent,
		LiberarVagaTurmaComponent,
		ModalHistoricoFaltasComponent,
		EnviarReciboComponent,
		CadastrarControleCreditoTreinoComponent,
		ModalAfastamentoContratoDependenteComponent,
		ModalHistoricoAfastamentoContratoDependenteComponent,
		ModalAdicionarContratoDependenteComponent,
		DetalhamentoContratoDependenteComponent,
		ModalLogConciliadoraComponent,
		ModalAlterarVencimentoParcelasContratoComponent,
		ModalAlterarPlanoContratoComponent,
		ModalAtualizarContratoComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class PerfilClienteContratosModule {}
