import { Component, Input, OnInit } from "@angular/core";
import { AdmRestService } from "projects/adm/src/app/adm-rest.service";
import { DialogService, PactoDataGridConfig } from "ui-kit";
import { DatePipe } from "@angular/common";

declare var moment;

@Component({
	selector: "pacto-detalhamento-contrato-historico-contrato",
	templateUrl: "./detalhamento-contrato-historico-contrato.component.html",
	styleUrls: [
		"./detalhamento-contrato-historico-contrato.component.scss",
		"../../contract-common.scss",
	],
})
export class DetalhamentoContratoHistoricoContratoComponent implements OnInit {
	table: PactoDataGridConfig;
	@Input() codigoContrato: any;

	constructor(
		private admRest: AdmRestService,
		private dialogService: DialogService,
		private datePipe: DatePipe
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			endpointUrl: this.admRest.buildFullUrlAdmCore(
				`contratos/${this.codigoContrato}/historicos`
			),
			columns: [
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
				},
				{
					nome: "responsavelRegistro",
					titulo: "Responsável",
					visible: true,
					valueTransform: (v) => (v ? v.nome : v),
				},
				{
					nome: "dataRegistro",
					titulo: "Data lançamento",
					visible: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
				{
					nome: "dataInicioSituacao",
					titulo: "Data início",
					visible: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
				{
					nome: "dataFinalSituacao",
					titulo: "Data fim",
					visible: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
			],
		});
	}

	onChangePage(event: any) {
		const table = document.getElementById(
			"tbl-title-block-dchc-tbl-hist-contrato"
		);
		if (table) {
			table.scrollIntoView();
		}
	}
}
