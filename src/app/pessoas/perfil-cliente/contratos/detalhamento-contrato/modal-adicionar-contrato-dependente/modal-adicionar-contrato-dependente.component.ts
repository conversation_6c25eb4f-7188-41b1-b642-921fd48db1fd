import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { SelectFilterParamBuilder } from "ui-kit";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-adicionar-contrato-dependente",
	templateUrl: "./modal-adicionar-contrato-dependente.component.html",
	styleUrls: ["./modal-adicionar-contrato-dependente.component.scss"],
})
export class ModalAdicionarContratoDependenteComponent implements OnInit {
	form: FormGroup;
	familiaresUrl;
	contrato;
	dadosPessoais;
	@Output()
	update = new EventEmitter<any>();

	constructor(
		private snotifyService: SnotifyService,
		private restService: RestService,
		private fb: FormBuilder,
		private modal: NgbActiveModal,
		private readonly cd: ChangeDetectorRef,
		private readonly sessionService: SessionService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {
		this.form = this.fb.group({
			aluno: this.fb.control("", [Validators.required]),
		});
		this.familiaresUrl = this.restService.buildFullUrlPessoaMs(
			`alunoColaboradorUsuario/familiares/${this.sessionService.empresaId}`
		);
	}

	fecharHandler() {
		this.modal.close();
	}

	familiarParamBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				nome: term,
				situacao: ["IN", "VI"],
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	confirmar() {
		if (!this.form.get("aluno").value) {
			this.snotifyService.error("Selecione o aluno");
			return;
		}
		this.admLegadoTelaClienteService
			.adicionarDependenteContratoDependente(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				this.contrato,
				this.dadosPessoais.codigoCliente,
				this.form.get("aluno").value.familiar
			)
			.subscribe(
				(resp) => {
					this.snotifyService.success("Dependente adicionado");
					this.update.emit("atualizar_contrato_dependente");
					this.fecharHandler();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					}
				}
			);
	}
}
