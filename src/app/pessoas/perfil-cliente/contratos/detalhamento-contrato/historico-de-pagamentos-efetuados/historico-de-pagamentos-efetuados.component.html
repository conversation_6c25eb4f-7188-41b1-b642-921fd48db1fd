<pacto-relatorio
	id="tbl-hist-pagamento-efetuados"
	(iconClick)="iconClick($event)"
	(pageChangeEvent)="onChangePage($event)"
	[itensPerPage]="itensPerPage"
	[showShare]="false"
	[table]="table"
	class="contract-table"
	i18n-tableTitle="@@detail-contract:historico-pagamentos-efetuados"
	idSuffix="dchc-tbl-hist-pag-efetuado"
	labelTotalElementsPosition="left"
	tableTitle="Histórico de pagamentos efetuados"></pacto-relatorio>

<ng-template #celulaRecibo let-item="item">
	<a
		(click)="emitirNfce(item)"
		*ngIf="apresentarNfce()"
		class="hpe-action-item"
		id="dchc-hist-pag-eft-emitir-nfce"
		ngbTooltip="Emitir NFC-e"
		style="
			cursor: pointer;
			color: #0380e3;
			font-size: 0.875rem;
			font-weight: 700;
		">
		NFC-e
	</a>
	<a
		(click)="emitirNfse(item)"
		*ngIf="apresentarNfse()"
		class="hpe-action-item"
		id="dchc-hist-pag-eft-emitir-nfse"
		ngbTooltip="Emitir NFS-e"
		style="
			cursor: pointer;
			color: #0380e3;
			font-size: 0.875rem;
			font-weight: 700;
		">
		NFS-e
	</a>
	<span
		(click)="imprimirRecibo(item)"
		class="hpe-action-item"
		id="dchc-hist-pag-eft-imprimir-recibo"
		ngbTooltip="Imprimir recibo">
		<i class="pct pct-printer cor-azulim05"></i>
	</span>
	<span
		(click)="enviarReciboEmail(item)"
		class="hpe-action-item"
		id="dchc-hist-pag-eft-enviar-recibo"
		ngbTooltip="Enviar recibo">
		<i class="pct pct-send cor-azulim05"></i>
	</span>
	<span
		(click)="editarPagamento(item)"
		class="hpe-action-item"
		id="dchc-hist-pag-eft-editar-recibo"
		ngbTooltip="Editar recibo">
		<i class="pct pct-edit cor-azulim05"></i>
	</span>
	<span
		(click)="consultarRecibo(item)"
		class="hpe-action-item"
		id="dchc-hist-pag-eft-visualizar-recibo"
		ngbTooltip="Visualizar recibo">
		<i class="pct pct-search cor-azulim05"></i>
	</span>
</ng-template>

<ng-template #statusConciliadora let-item="item">
	<span
		(click)="openLogConciliadora(item)"
		*ngIf="item.credito"
		[pactoCatTolltip]="
			statusPagamentoConciliadora.obterPorCodigo(item.statusConciliadora)
				? statusPagamentoConciliadora.obterPorCodigo(item.statusConciliadora)
						.descricao
				: ''
		"
		darkTheme="true"
		style="cursor: pointer">
		<i
			[class]="
				statusPagamentoConciliadora.obterPorCodigo(item.statusConciliadora)
					? statusPagamentoConciliadora.obterPorCodigo(item.statusConciliadora)
							.iconClass
					: ''
			"></i>
	</span>
</ng-template>
