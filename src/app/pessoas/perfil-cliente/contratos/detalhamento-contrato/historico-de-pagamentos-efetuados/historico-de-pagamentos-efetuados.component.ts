import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	ConfiguracaoSistema,
	Empresa,
	TipoRelatorioDF,
} from "adm-legado-api";
import {
	ClienteDadosPessoais,
	StatusPagamentoConciliadora,
} from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import {
	AutorizacaoAcessoComponent,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
} from "ui-kit";
import { ModalEditFormaPagamentoComponent } from "../../../financeiro/financeiro-compras/modals/modal-action-edit/modal-edit-forma-pagamento.component";
import { EnviarReciboComponent } from "./enviar-recibo/enviar-recibo.component";
import { PerfilClienteService } from "../../../perfil-cliente.service";
import { ModalActionNfceComponent } from "../../../financeiro/financeiro-compras/modals/modal-action-nfce/modal-action-nfce.component";
import { ModalActionNfseComponent } from "../../../financeiro/financeiro-compras/modals/modal-action-nfse/modal-action-nfse.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";
import { ModalLogConciliadoraComponent } from "../../../financeiro/financeiro-recibos/modal-log-conciliadora/modal-log-conciliadora.component";
import { ModalService } from "@base-core/modal/modal.service";
import { MatDialog } from "@angular/material";

@Component({
	selector: "pacto-historico-de-pagamentos-efetuados",
	templateUrl: "./historico-de-pagamentos-efetuados.component.html",
	styleUrls: ["./historico-de-pagamentos-efetuados.component.scss"],
})
export class HistoricoDePagamentosEfetuadosComponent implements OnInit {
	@Input()
	public codigoContrato: number;
	@Input()
	private dadosPessoais: ClienteDadosPessoais;

	@ViewChild("celulaRecibo", { static: true })
	celulaRecibo: TemplateRef<any>;
	@ViewChild("statusConciliadora", { static: true })
	statusConciliadoraCell: TemplateRef<any>;

	public table: PactoDataGridConfig;
	empresa: Empresa;
	configuracaoSistema: ConfiguracaoSistema;
	statusPagamentoConciliadora = StatusPagamentoConciliadora;
	isAutorizarNFSe = false;
	itensPerPage = [
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
		{ id: 50, label: "50" },
		{ id: 100, label: "100" },
		{ id: 250, label: "250" },
	];

	constructor(
		private readonly rest: RestService,
		private readonly service: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly dialogService: DialogService,
		private readonly pactoModal: ModalService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly cd: ChangeDetectorRef,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		private readonly perfilClienteService: PerfilClienteService,
		private ngbModal: NgbModal,
		private matDialog: MatDialog
	) {}

	ngOnInit() {
		this.empresa = this.perfilClienteService.empresa;
		this.configuracaoSistema = this.perfilClienteService.configuracoesSistema;
		this.isAutorizarNFSe =
			!!this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.find(
				(r) => r.referenciaFuncionalidade === "3.43"
			);

		if (this.empresa && this.configuracaoSistema) {
			this.table = new PactoDataGridConfig({
				rowClick: false,
				endpointUrl: this.rest.buildFullUrlAdmCore(
					`contratos/${this.codigoContrato}/pagamentos`
				),
				columns: [
					{
						nome: "codigo",
						titulo: "Codigo",
						visible: true,
					},
					{
						nome: "reciboPagamento",
						titulo: "Nº recibo",
						visible: true,
					},
					{
						nome: "empresa",
						titulo: "Empresa",
						visible:
							this.sessionService.empresas &&
							this.sessionService.empresas.length > 1,
						valueTransform: (data) => data.nome,
					},
					{
						nome: "nomePessoaPagador",
						titulo: "Nome do pagador",
						visible: true,
					},
					{
						nome: "dataLancamento",
						titulo: "Lançamento",
						visible: true,
						valueTransform: (data) => new Date(data).toLocaleString("pt-BR"),
					},
					{
						nome: "formaPagamento",
						titulo: "Forma de pagamento.",
						visible: true,
						valueTransform: (data) => data.descricao,
					},
					{
						nome: "valorTotal",
						titulo: "Valor",
						visible: true,
						valueTransform: (data) =>
							(data ? data : 0).toLocaleString("pt-BR", {
								style: "currency",
								currency: "BRL",
							}),
					},
					{
						nome: "statusConciliadora",
						titulo: "Status",
						visible: this.empresa.usarConciliadora,
						celula: this.statusConciliadoraCell,
					},
					{
						nome: "recibo",
						titulo: "Recibo",
						ordenavel: false,
						visible: true,
						celula: this.celulaRecibo,
					},
				],
			});
		}
	}

	iconClick(evt: { row: any; iconName: string }): void {
		this[evt.iconName](evt.row);
	}

	emitirNfce(row): void {
		const dialogRef = this.ngbModal.open(ModalActionNfceComponent, {
			centered: true,
			size: "lg",
			windowClass: "modal-action-nfce-component",
		});
		dialogRef.componentInstance.rowData = row;
		dialogRef.componentInstance.codigoRecibo = row.reciboPagamento;
		dialogRef.componentInstance.codigoUsuario =
			this.sessionService.codigoUsuarioZw;
		this.cd.detectChanges();
	}

	emitirNfse(row): void {
		const dialogRef = this.ngbModal.open(ModalActionNfseComponent, {
			centered: true,
			size: "lg",
			windowClass: "modal-action-nfse-component",
		});
		dialogRef.componentInstance.rowData = row;
		dialogRef.componentInstance.codigoRecibo = row.reciboPagamento;
		dialogRef.componentInstance.codigoUsuario =
			this.sessionService.codigoUsuarioZw;
		this.cd.detectChanges();
	}

	imprimirRecibo(row): void {
		this.service
			.imprimirRecibo(
				this.sessionService.chave,
				row.reciboPagamento,
				row.usuario
			)
			.subscribe(
				(res) => {
					if (res.content) {
						window.open(res.content, "_blank");
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	enviarReciboEmail(row): void {
		const dialog = this.dialogService.open(
			"Solicitar envio de recibo por e-mail",
			EnviarReciboComponent,
			PactoModalSize.MEDIUM
		);
		dialog.componentInstance.row = row;
	}

	editarPagamento(row) {
		if (row.empresa.codigo !== +this.sessionService.empresaId) {
			this.snotifyService.error(
				`É necessário estar logado na unidade ${row.empresa.nome} para realizar essa operação, pois esse pagamento pertence a essa unidade.`
			);
			return;
		}
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"EdicaoPagamento",
					"4.22 - Edição de Pagamento",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response) => {
						result.modal.close();

						this.clientDiscoveryService
							.linkZw(
								this.sessionService.usuarioOamd,
								this.sessionService.empresaId
							)
							.subscribe((result) => {
								let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&codigoPagamento=${row.codigo}&codigoUsuario=${response.content}&operacaoClienteEnumName=EDICAO_FORMA_PAGAMENTO&isOperacaoFinanceiro=true&menu=true`;
								window.open(url, "_self");
							});
						this.cd.detectChanges();
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					}
				);
		});
	}

	consultarRecibo(row) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&codigoRecibo=${row.reciboPagamento}&operacaoClienteEnumName=CONSULTA_RECIBO&isOperacaoFinanceiro=true&origem=angular`;
				this.abrirPopup(url, "Recibo", 800, 595);
			});
	}

	apresentarNfse() {
		return (
			this.isAutorizarNFSe &&
			this.dadosPessoais.empresa &&
			this.dadosPessoais.empresa.usarNfse &&
			this.dadosPessoais.empresa.tipoGestaoNfse ===
				TipoRelatorioDF.FATURAMENTO_DE_CAIXA.codigo
		);
	}

	apresentarNfce() {
		return (
			this.dadosPessoais.empresa &&
			this.dadosPessoais.empresa.usarNfce &&
			this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.findIndex(
				(f) => f.nome === "gestaonfce" && f.possuiFuncionalidade
			) > -1
		);
	}

	openLogConciliadora(movPagamento) {
		const modalRef = this.pactoModal.open(
			"Status integração conciliadora",
			ModalLogConciliadoraComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.movPagamento = movPagamento;
	}

	onChangePage(event: any) {
		const table = document.getElementById(
			"tbl-title-block-dchc-tbl-hist-pag-efetuado"
		);
		if (table) {
			table.scrollIntoView();
		}
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	): boolean {
		const posTopo = 0;
		const posEsquerda = 0;
		const atributos = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo}, width=${comprimento}, height=${altura}, dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes`;

		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		window.open(urlPopup, nomeJanela, atributos);

		return false;
	}
}
