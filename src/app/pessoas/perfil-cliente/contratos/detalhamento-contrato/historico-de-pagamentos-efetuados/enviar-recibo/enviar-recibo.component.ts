import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-enviar-recibo",
	templateUrl: "./enviar-recibo.component.html",
	styleUrls: ["./enviar-recibo.component.scss"],
})
export class EnviarReciboComponent implements OnInit {
	public form: FormGroup = new FormGroup({
		email: new FormControl("", [Validators.email, Validators.required]),
	});
	public emails: string[] = [];
	message: string;

	@Input()
	public row: any;

	constructor(
		private readonly dialog: NgbActiveModal,
		private readonly service: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly activeModal: NgbActiveModal
	) {}

	ngOnInit() {}

	public incluir(): void {
		this.form.markAsTouched();
		if (this.form.invalid) {
			return;
		}
		const emailValue = this.form.get("email").value;
		this.emails.push(emailValue);
		this.form.reset();
	}

	public excluir(index): void {
		this.emails.splice(index, 1);
	}

	public enviar(): void {
		this.service
			.enviarReciboEmail(
				this.sessionService.chave,
				this.row.reciboPagamento,
				this.sessionService.loggedUser.usuarioZw,
				this.emails
			)
			.subscribe(
				(res) => {
					this.message = res.content;
					if (res.content) {
						this.snotifyService.success(res.content);
						this.activeModal.close();
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	cancel() {
		this.dialog.close();
	}
}
