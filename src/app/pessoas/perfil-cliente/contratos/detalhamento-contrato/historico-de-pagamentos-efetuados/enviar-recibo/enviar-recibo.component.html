<div class="container">
	<ng-container *ngIf="!message">
		<form [formGroup]="form" class="form">
			<ds3-form-field class="input-email">
				<ds3-field-label>Adicionar novo e-mail</ds3-field-label>
				<input [type]="email" autofocus ds3Input formControlName="email" />
			</ds3-form-field>
			<button (click)="incluir()" class="button" ds3-text-button>
				Adicionar
			</button>
		</form>

		<div class="lista-de-emails">
			<span>E-mails para envio</span>
			<div *ngFor="let email of emails; index as i" class="email">
				<span>{{ email }}</span>
				<!-- <i class="pct pct-trash-2" (click)="removeEmail(i)"></i> -->
			</div>
		</div>

		<div class="acoes">
			<pacto-cat-button
				(click)="cancel()"
				label="Cancelar"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button (click)="enviar()" label="Enviar"></pacto-cat-button>
		</div>
	</ng-container>

	<ng-container *ngIf="message">
		<div class="sucesso">
			<div style="word-break: break-word">
				<span>{{ message }}</span>
			</div>
		</div>
		<div class="acoes">
			<pacto-cat-button (click)="cancel()" label="Fechar"></pacto-cat-button>
		</div>
	</ng-container>
</div>
