@import "projects/ui/assets/ds3/fonts/fonts.scss";

::ng-deep .modal-dialog {
	max-width: 681px;
}

.container {
	padding: 1.5rem;

	.form {
		display: flex;

		.input-email {
			width: -webkit-fill-available;
		}

		.button {
			height: min-content;
			margin-top: 28px;
		}
	}

	.lista-de-emails {
		display: flex;
		flex-direction: column;
		margin-top: 5%;
		@extend .pct-title4;

		span {
			margin-bottom: 5%;
		}

		.email {
			display: flex;
			justify-content: space-between;
			font-family: Nunito Sans;
			font-size: 14px;
			font-weight: 400;
			line-height: 19.2px;
			text-align: left;

			::ng-deep pacto-cat-radio {
				.content-div {
					width: 15px !important;
					height: 15px !important;
				}

				.radio {
					border: 2px solid #1e60fa;
				}
			}
		}
	}

	.acoes {
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
	}

	.sucesso {
		display: flex;
		justify-content: center;
		padding: 1.5rem;
		text-align: center;
	}
}
