import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { PactoDataGridConfig, PactoModalSize } from "ui-kit";
import { RelatorioComponent } from "../../../../../../../projects/ui/src/lib/components/relatorio/relatorio.component";
import { ModalService } from "@base-core/modal/modal.service";
import { DatePipe } from "@angular/common";
import { AdmRestService } from "../../../../../../../projects/adm/src/app/adm-rest.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { ModalAdicionarContratoDependenteComponent } from "../modal-adicionar-contrato-dependente/modal-adicionar-contrato-dependente.component";
import { ClienteDadosPessoais } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";

declare var moment;

@Component({
	selector: "pacto-detalhamento-contrato-dependente",
	templateUrl: "./detalhamento-contrato-dependente.component.html",
	styleUrls: ["./detalhamento-contrato-dependente.component.scss"],
})
export class DetalhamentoContratoDependenteComponent implements OnInit {
	@Input() codigoContrato;
	@Input() dadosPessoais: ClienteDadosPessoais;
	public tableDependentes: PactoDataGridConfig;
	dataDependentes = false;
	@ViewChild("tableDependentesRelatorio", { static: false })
	tableDependentesRelatorio: RelatorioComponent;
	@ViewChild("celulaDependente", { static: true })
	public celulaDependente;
	@ViewChild("celulaSituacaoDependente", { static: true })
	public celulaSituacaoDependente;
	infoContratoDependente;
	@Output()
	reloadContract: EventEmitter<any> = new EventEmitter<any>();

	constructor(
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private readonly pactoModal: ModalService,
		private readonly datePipe: DatePipe,
		private readonly admRest: AdmRestService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {
		this.initTableDependentes();
		this.obterInfoContratoDependente();
	}

	private initTableDependentes(): void {
		this.tableDependentes = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlAdmCore(
				`contratos/dependente/by-contrato/${this.codigoContrato}?somenteComCliente=true`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			dataAdapterFn: (serveData) => {
				this.dataDependentes = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "cliente",
					titulo: "Dependente",
					visible: true,
					ordenavel: true,
					celula: this.celulaDependente,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: true,
					celula: this.celulaSituacaoDependente,
				},
				{
					nome: "dataInicio",
					titulo: "Data início",
					visible: true,
					ordenavel: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
				{
					nome: "dataFinalAjustada",
					titulo: "Data fim",
					visible: true,
					ordenavel: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
			],
			actions: [
				{
					nome: "excluirDependente",
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "Excluir dependente",
					showIconFn: (row) => row.dataFinalAjustada > new Date().getTime(),
					actionFn: (row) => this.excluirDepente(row),
				},
			],
		});
	}

	excluirDepente(row) {
		const confirm = this.pactoModal.confirm(
			"Exclusão de Dependente",
			`Tem certeza de que deseja remover o dependente ${row.row.cliente.pessoa.nome} do contrato ${this.codigoContrato}? Esta ação não poderá ser desfeita, e uma vez removido o vínculo, o dependente não terá mais acesso aos beneficios deste plano.`,
			"Sim, excluir"
		);
		confirm.result.then(
			() => {
				this.admLegadoTelaClienteService
					.excluirDependenteContratoDependente(
						this.sessionService.chave,
						parseInt(this.sessionService.empresaId),
						this.sessionService.codigoUsuarioZw,
						row.row.codigo
					)
					.subscribe(
						(resp) => {
							this.notificationService.success("Dependente removido");
							this.tableDependentesRelatorio.reloadData();
							this.obterInfoContratoDependente();
							this.cd.detectChanges();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta && err.meta.message) {
								this.notificationService.error(err.meta.message);
							}
						}
					);
			},
			() => {}
		);
	}

	addDependente() {
		const modalRef = this.pactoModal.open(
			"Adicionar dependente ao contrato",
			ModalAdicionarContratoDependenteComponent,
			PactoModalSize.MEDIUM
		);
		modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
		modalRef.componentInstance.contrato = this.codigoContrato;
		modalRef.componentInstance.update.subscribe((res) => {
			if (res === "atualizar_contrato_dependente") {
				this.tableDependentesRelatorio.reloadData();
				this.obterInfoContratoDependente();
				this.cd.detectChanges();
			}
		});
	}

	obterInfoContratoDependente() {
		this.admLegadoTelaClienteService
			.obterInfoContratoDependente(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.dadosPessoais.codigoPessoa,
				this.codigoContrato
			)
			.subscribe(
				(resp) => {
					this.infoContratoDependente = resp.content;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					console.log(httpErrorResponse);
				}
			);
	}
}
