<pacto-cat-card-plain>
	<div class="contrato-dependente-detail">
		<div class="detail-title" i18n="@@detail-contrato-dependente:dependentes">
			Dependentes do contrato
		</div>
		<div class="btn-row">
			<pacto-cat-button
				(click)="addDependente()"
				[disabled]="!infoContratoDependente?.permiteCompartilharPlano"
				label="Adicionar dependente"
				size="LARGE"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</div>
	<div class="d-flex align-items-center divSuperior">
		<i class="pct pct-info"></i>
		<span class="infoSuperior">
			O cliente pode ter no máximo
			{{ infoContratoDependente?.quantidadeCompartilhamentosPlano }}
			dependente(s).
		</span>
	</div>
	<div class="div-contrato-dependente">
		<pacto-relatorio
			#tableDependentesRelatorio
			[class]="'table-contrato-dependente'"
			[emptyStateMessage]="'Nenhum dependente'"
			[enableZebraStyle]="true"
			[showShare]="false"
			[table]="tableDependentes"
			actionTitulo="Ação"
			idSuffix="cont-contrato-dependentes"></pacto-relatorio>
	</div>

	<ng-template #celulaDependente let-item="item">
		<div class="d-flex align-items-center">
			<pacto-cat-person-avatar
				[diameter]="25"
				[uri]="item?.cliente?.pessoa?.urlFoto"></pacto-cat-person-avatar>
			<span style="padding-left: 5px">
				{{ item?.cliente?.pessoa?.nome | captalize }}
			</span>
		</div>
	</ng-template>

	<ng-template #celulaSituacaoDependente let-item="item">
		<div class="status-situacao">
			<span
				[class]="
					'situacao-aluno primario ' + item?.cliente?.situacao | lowercase
				">
				<span *ngIf="item?.cliente?.situacao == 'AE'">Atestado</span>
				<span *ngIf="item?.cliente?.situacao == 'AT'">Ativo</span>
				<span *ngIf="item?.cliente?.situacao == 'AV'">A vencer</span>
				<span *ngIf="item?.cliente?.situacao == 'NO'">Normal</span>
				<span *ngIf="item?.cliente?.situacao == 'CR'">Férias</span>
				<span *ngIf="item?.cliente?.situacao == 'DE'">Desistente</span>
				<span *ngIf="item?.cliente?.situacao == 'VE'">Vencido</span>
				<span *ngIf="item?.cliente?.situacao == 'DI'">Diária</span>
				<span *ngIf="item?.cliente?.situacao == 'TP'">Totalpass</span>
				<span *ngIf="item?.cliente?.situacao == 'TR'">Trancado</span>
				<span *ngIf="item?.cliente?.situacao == 'GY'">Gympass</span>
				<span *ngIf="item?.cliente?.situacao == 'PE'">Freepass</span>
				<span *ngIf="item?.cliente?.situacao == 'VI'">Visitante</span>
				<span *ngIf="item?.cliente?.situacao == 'CA'">Cancelado</span>
				<span *ngIf="item?.cliente?.situacao == 'TR'">Trancado</span>
				<span *ngIf="item?.cliente?.situacao == 'IN'">INATIVO</span>
			</span>
		</div>
	</ng-template>
</pacto-cat-card-plain>
