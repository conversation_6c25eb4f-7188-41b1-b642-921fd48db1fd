import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";

@Component({
	selector: "pacto-bonus-contrato",
	templateUrl: "./bonus-contrato.component.html",
	styleUrls: ["./bonus-contrato.component.scss"],
})
export class BonusContratoComponent implements OnInit {
	@Input() codContrato;
	@Input() codCliente;
	url!: string;

	constructor(
		private dialog: NgbActiveModal,
		private clientDiscoveryService: ClientDiscoveryService,
		private session: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.clientDiscoveryService
			.linkZw(this.session.usuarioOamd, this.session.empresaId)
			.subscribe((result) => {
				this.url = `${result}&codCliente=${this.codCliente}&codContrato=${this.codContrato}&operacaoClienteEnumName=BONUS&isContratoOperacao=true`;
				this.cd.detectChanges();
			});
	}

	messageReceived(messageEvent: { event: MessageEvent; dataParsed: any }) {
		if (messageEvent && messageEvent.dataParsed) {
			if (messageEvent.dataParsed) {
				if (messageEvent.dataParsed.close) {
					this.dialog.close();
				} else if (messageEvent.dataParsed.reloadContractPage) {
					this.dialog.close({ reloadContrato: true });
				}
			}
		}
	}
}
