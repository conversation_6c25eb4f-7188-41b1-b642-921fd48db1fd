import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ManutencaoModalidadeContratoComponent } from "./manutencao-modalidade-contrato.component";

describe("ManutencaoModalidadeContratoComponent", () => {
	let component: ManutencaoModalidadeContratoComponent;
	let fixture: ComponentFixture<ManutencaoModalidadeContratoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ManutencaoModalidadeContratoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ManutencaoModalidadeContratoComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
