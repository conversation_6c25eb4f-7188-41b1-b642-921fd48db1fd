<div class="contract-detail">
	<div class="detail-title" i18n="@@detail-contract:operacoes-contrato">
		Operações
	</div>
	<div class="detail-data-container">
		<pacto-relatorio
			#tableRef
			(iconClick)="iconClick($event)"
			(pageChangeEvent)="onChangePage($event)"
			[showShare]="false"
			[table]="table"
			actionTitle="Ações"
			class="contract-table"
			emptyStateMessage="Nenhuma operação realizada no contrato"
			i18n-actionTitle="@@column-actions"
			idSuffix="dcoc-tbl-operacoes-contrato"
			labelTotalElementsPosition="left"
			tableTitle="Operações de contrato"></pacto-relatorio>
	</div>
</div>

<ng-template #tipoOperacaoCell let-item="item">
	<span>{{ tipoOperacaoText(item.tipoOperacao, item) }}</span>
</ng-template>
