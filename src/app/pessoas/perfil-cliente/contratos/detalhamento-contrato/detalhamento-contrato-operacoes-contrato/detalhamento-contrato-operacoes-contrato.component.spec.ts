import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { DetalhamentoContratoOperacoesContratoComponent } from "./detalhamento-contrato-operacoes-contrato.component";

describe("DetalhamentoContratoOperacoesContratoComponent", () => {
	let component: DetalhamentoContratoOperacoesContratoComponent;
	let fixture: ComponentFixture<DetalhamentoContratoOperacoesContratoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [DetalhamentoContratoOperacoesContratoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			DetalhamentoContratoOperacoesContratoComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
