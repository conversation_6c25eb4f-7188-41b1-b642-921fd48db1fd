import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { AdmCoreApiClienteService } from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "projects/adm/src/app/adm-rest.service";
import {
	AutorizacaoAcessoComponent,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
} from "ui-kit";
import { DetalhesDaOperacaoComponent } from "./detalhes-da-operacao/detalhes-da-operacao.component";
import { DatePipe } from "@angular/common";
import { LoaderService } from "../../../../../loader/loader.service";
import { MatDialog } from "@angular/material";

declare var moment;

@Component({
	selector: "pacto-detalhamento-contrato-operacoes-contrato",
	templateUrl: "./detalhamento-contrato-operacoes-contrato.component.html",
	styleUrls: [
		"./detalhamento-contrato-operacoes-contrato.component.scss",
		"../../contract-common.scss",
	],
	encapsulation: ViewEncapsulation.None,
})
export class DetalhamentoContratoOperacoesContratoComponent implements OnInit {
	table: PactoDataGridConfig;
	dadosPessoais: any;
	@Input() contrato: any;
	@Output() reloadContract: EventEmitter<boolean> = new EventEmitter<boolean>();
	@ViewChild("tableRef", { static: true })
	tableRef: RelatorioComponent;
	@ViewChild("tipoOperacaoCell", { static: true })
	tipoOperacaoCell: TemplateRef<any>;

	constructor(
		private readonly admRest: AdmRestService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly telaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly admCoreApiClienteService: AdmCoreApiClienteService,
		private readonly route: ActivatedRoute,
		private readonly snotify: SnotifyService,
		private readonly dialogService: DialogService,
		private datePipe: DatePipe,
		private loaderService: LoaderService,
		private matDialog: MatDialog
	) {}

	ngOnInit() {
		this.admCoreApiClienteService
			.dadosPessoais(this.route.snapshot.params["aluno-matricula"])
			.subscribe((response) => (this.dadosPessoais = response));

		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			endpointUrl: this.admRest.buildFullUrlAdmCore(
				`contratos/${this.contrato.codigo}/operacoes?validarOperacoesContrato=true`
			),
			columns: [
				{
					nome: "tipoOperacao",
					titulo: "Tipo de operação",
					visible: true,
					celula: this.tipoOperacaoCell,
				},
				{
					nome: "dataOperacao",
					titulo: "Data lançamento",
					visible: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
				},
				{
					nome: "dataInicioEfetivacaoOperacao",
					titulo: "Data início",
					visible: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
				{
					nome: "dataFimEfetivacaoOperacao",
					titulo: "Data fim",
					visible: true,
					valueTransform: (v) => moment(v).utc().format("DD/MM/YYYY"),
				},
				{
					nome: "diasOperacao",
					titulo: "Qtde. dias",
					visible: true,
					valueTransform: (v) => v || "-",
				},
			],
			actions: [
				{
					nome: "consultar",
					iconClass: "pct pct-search cor-azulim05",
				},
				{
					nome: "imprimir",
					iconClass: "pct pct-printer cor-azulim05",
				},
				{
					nome: "enviarPorEmail",
					iconClass: "pct pct-send cor-azulim05",
					showIconFn: (row) => this.permiteEnviarEmail(row),
				},
				{
					nome: "estornarContratoOperacao",
					iconClass: "pct pct-x cor-azulim05",
					showIconFn: (row) => row.permiteEstornar,
				},
				{
					nome: "downloadAtestado",
					iconClass: "pct pct-download cor-azulim05",
					showIconFn: (row) => row.tipoOperacao === "AT" && row.chaveArquivo,
				},
				{
					nome: "cancelarOperacao",
					iconClass: "pct pct-x cor-azulim05",
					showIconFn: (row) => this.permiteCancelarOperacao(row),
				},
			],
		});
	}

	iconClick(evt: { row: any; iconName: string }): void {
		this[evt.iconName](evt.row);
	}

	consultar(row) {
		const dialogRef = this.dialogService.open(
			"Detalhes da operação",
			DetalhesDaOperacaoComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.detalhes = row;
	}

	imprimir(row) {
		const data = { tipo: row.contrato.tipo, contrato: row.contrato.codigo };
		this.telaClienteService
			.imprimirContratoOperacao(this.sessionService.chave, row.codigo, data)
			.subscribe(
				(res) => {
					open(res.content, "_blank");
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.snotify.error(error.meta.message, { timeout: 4000 });
					} else {
						this.snotify.error(
							`Desculpe, ocorreu um erro ao imprerir o comprovante da operação.`,
							{ timeout: 4000 }
						);
					}
				}
			);
	}

	enviarPorEmail(row) {
		const data = {
			contratoOperacao: row.codigo,
			emails: this.dadosPessoais.emails,
		};
		this.telaClienteService
			.enviarEmailCancelamentoContrato(this.sessionService.chave, data)
			.subscribe(
				(res) => {
					this.snotify.success(res.content);
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.snotify.error(error.meta.message, { timeout: 4000 });
					} else {
						this.snotify.error(
							`Desculpe, ocorreu um erro ao enviar o e-mail.`,
							{ timeout: 4000 }
						);
					}
				}
			);
	}

	downloadAtestado(contratoOperacao) {
		window.open(contratoOperacao.urlArquivoAtestado, "_blank");
	}

	estornarContratoOperacao(contratoOperacao) {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.loaderService.initForce();
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"EstornoOperacaoContrato_Autorizar",
					"3.24 - Estorno de Operação de Contrato - Autorizar",
					this.sessionService.empresaId
				)
				.subscribe(
					(response: any) => {
						this.telaClienteService
							.estornarContratoOperacao(
								this.sessionService.chave,
								contratoOperacao.codigo,
								this.sessionService.codUsuarioZW
							)
							.subscribe(
								() => {
									result.modal.close();
									this.snotify.success("Estorno realizado com sucesso!");
									this.tableRef.reloadData();
									this.loaderService.stopForce();
								},
								(httpErrorResponse) => {
									const err = httpErrorResponse.error;
									if (err.meta && err.meta.message) {
										this.snotify.error(err.meta.message);
									} else if (err.meta && err.meta.error) {
										this.snotify.error(err.meta.error);
									}
									this.loaderService.stopForce();
								}
							);
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.message) {
							this.snotify.error(err.meta.message);
						} else if (err.meta && err.meta.error) {
							this.snotify.error(err.meta.error);
						}
						this.loaderService.stopForce();
					}
				);
		});
	}

	cancelarOperacao(contratoOperacao) {
		const dialogRef = this.dialogService.confirm(
			"Desfazer cancelamento",
			"Deseja desfazer o cancelamento?",
			"Confirmar"
		);
		dialogRef.result.then((result) => {
			this.telaClienteService
				.cancelarOperacao(
					this.sessionService.chave,
					contratoOperacao.codigo,
					this.sessionService.loggedUser.usuarioZw
				)
				.subscribe(
					() => {
						this.snotify.success("Operação cancelada com sucesso!");
						this.tableRef.reloadData();
						this.reloadContract.emit(true);
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.message) {
							this.snotify.error(err.meta.message);
						} else if (err.meta && err.meta.error) {
							this.snotify.error(err.meta.error);
						}
					}
				);
		});
	}

	permiteCancelarOperacao(contratoOperacao) {
		return (
			this.contrato &&
			this.contrato.contratoRecorrencia &&
			this.contrato.contratoRecorrencia.cancelamentoProporcional &&
			contratoOperacao.tipoOperacao === "CA" &&
			new Date().getTime() < contratoOperacao.dataInicioEfetivacaoOperacao
		);
	}

	tipoOperacaoText(tipoOperacao, operacao: any) {
		if (tipoOperacao == null) {
			tipoOperacao = "";
		}
		if (tipoOperacao === "RM") {
			return "Rematrícula";
		}
		if (tipoOperacao === "TS") {
			return "Transferência Saída";
		}
		if (tipoOperacao === "MA") {
			return "Matrícula";
		}
		if (tipoOperacao === "AD") {
			return "Alteração Duração";
		}
		if (tipoOperacao === "RE") {
			return "Renovação";
		}
		if (tipoOperacao === "BA") {
			return "Bônus- Acréscimo de dias ";
		}
		if (tipoOperacao === "BR") {
			return "Bônus -Redução de dias ";
		}
		if (tipoOperacao === "CR") {
			if (
				operacao.dataOperacao != null &&
				operacao.dataInicioEfetivacaoOperacao != null &&
				operacao.dataFimEfetivacaoOperacao != null
			) {
				const dataIni = new Date(operacao.dataInicioEfetivacaoOperacao);
				dataIni.setHours(0, 0, 0, 0);
				const dataOp = new Date(operacao.dataOperacao);
				dataOp.setHours(0, 0, 0, 0);
				if (dataOp > dataIni) {
					return "Férias (RETROATIVO)";
				}
			}
			return "Férias";
		}

		if (tipoOperacao === "TE") {
			return "Transferência Entrada";
		}
		if (tipoOperacao === "CA") {
			return "Cancelamento";
		}
		if (tipoOperacao === "AH") {
			return "Alteração Horário";
		}
		if (tipoOperacao === "TR") {
			return "Trancamento";
		}
		if (tipoOperacao === "TV") {
			return "Trancamento Vencido";
		}
		if (tipoOperacao === "RT") {
			return "Retorno Trancamento";
		}
		if (tipoOperacao === "IM") {
			return "Incluir Modalidade";
		}
		if (tipoOperacao === "EM") {
			return "Excluir Modalidade";
		}
		if (tipoOperacao === "AM") {
			return "Alterar Modalidade";
		}
		if (tipoOperacao === "AC") {
			return "Alteração Contrato";
		}
		if (tipoOperacao === "AT") {
			if (
				operacao.dataOperacao != null &&
				operacao.dataInicioEfetivacaoOperacao != null &&
				operacao.dataFimEfetivacaoOperacao != null
			) {
				const dataIni = new Date(operacao.dataInicioEfetivacaoOperacao);
				dataIni.setHours(0, 0, 0, 0);
				const dataOp = new Date(operacao.dataOperacao);
				dataOp.setHours(0, 0, 0, 0);
				if (dataOp > dataIni) {
					return "Atestado (RETROATIVO)";
				}
			}
			return "Atestado";
		}

		if (tipoOperacao === "RA") {
			return "Retorno - Atestado";
		}
		if (tipoOperacao === "LV") {
			return "Liberação de Vaga";
		}

		if (tipoOperacao === "BC") {
			return "Afastamento Coletivo";
		}

		if (tipoOperacao === "TD") {
			return "Transferência dos Direitos de uso";
		}

		if (tipoOperacao === "RD") {
			return "Retorno dos Direitos de uso";
		}
	}

	private permiteEnviarEmail(contratoOperacao: any) {
		return contratoOperacao.tipoOperacao == "CA";
	}

	onChangePage(event: any) {
		const table = document.getElementById(
			"tbl-title-block-dcoc-tbl-operacoes-contrato"
		);
		if (table) {
			table.scrollIntoView();
		}
	}
}
