<!-- <pre>{{ detalhes | json }}</pre> -->
<section>
	<div class="div-geral-detalhes">
		<div *ngIf="detalhes?.origemSistema">
			<h4 class="titulo">Origem</h4>
			<span *ngIf="detalhes.origemSistema === 1" class="descricao">
				ZillyonWeb
			</span>
			<span *ngIf="detalhes.origemSistema === 2" class="descricao">
				Agenda Web
			</span>
			<span *ngIf="detalhes.origemSistema === 3" class="descricao">
				Pacto Treino
			</span>
			<span *ngIf="detalhes.origemSistema === 4" class="descricao">
				App Treino
			</span>
			<span *ngIf="detalhes.origemSistema === 5" class="descricao">
				App Professor
			</span>
			<span *ngIf="detalhes.origemSistema === 6" class="descricao">
				Autoatendimento
			</span>
			<span *ngIf="detalhes.origemSistema === 7" class="descricao">
				Site Vendas
			</span>
			<span *ngIf="detalhes.origemSistema === 8" class="descricao">
				Buzz Lead
			</span>
			<span *ngIf="detalhes.origemSistema === 9" class="descricao">
				Vendas 2.0
			</span>
			<span *ngIf="detalhes.origemSistema === 10" class="descricao">
				App do consultor
			</span>
			<span *ngIf="detalhes.origemSistema === 11" class="descricao">
				Booking Gympass
			</span>
			<span *ngIf="detalhes.origemSistema === 12" class="descricao">
				Fila de espera
			</span>
			<span *ngIf="detalhes.origemSistema === 13" class="descricao">
				Importação API
			</span>
			<span *ngIf="detalhes.origemSistema === 14" class="descricao">
				Hubspot Lead
			</span>
		</div>
		<div *ngIf="detalhes?.responsavel?.nome">
			<h4 class="titulo">Responsável</h4>
			<span class="descricao">
				{{ detalhes?.responsavel?.nome }}
			</span>
		</div>
		<div *ngIf="detalhes?.tipoJustificativa?.descricao">
			<h4 class="titulo">Justificativa</h4>
			<span class="descricao">
				{{ detalhes?.tipoJustificativa?.descricao }}
			</span>
		</div>
		<!-- <div>
			<h4 class="titulo">Cliente transferido</h4>
			<span class="descricao"></span>
		</div> -->
	</div>
</section>
<section *ngIf="detalhes?.descricaoCalculo">
	<h4 class="titulo">Descrição</h4>
	<span class="descricao">{{ detalhes?.descricaoCalculo }}</span>
</section>
<section *ngIf="detalhes?.observacao">
	<h4 class="titulo">Observações</h4>
	<span class="descricao">{{ detalhes?.observacao }}</span>
</section>
