import {
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig } from "ui-kit";
import { DatePipe } from "@angular/common";

@Component({
	selector: "pacto-historico-de-parcelas-geradas",
	templateUrl: "./historico-de-parcelas-geradas.component.html",
	styleUrls: ["./historico-de-parcelas-geradas.component.scss"],
})
export class HistoricoDeParcelasGeradasComponent implements OnInit {
	@Input()
	public codigoContrato: number;
	@ViewChild("cellSituacao", { static: true }) cellSituacao: TemplateRef<any>;

	table: PactoDataGridConfig;

	constructor(private readonly rest: RestService, private datePipe: DatePipe) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`contratos/${this.codigoContrato}/parcelas`
			),
			columns: [
				{
					nome: "codigo",
					titulo: "Codigo",
					visible: true,
				},
				{
					nome: "contrato",
					titulo: "Nº contrato",
					visible: true,
				},
				{
					nome: "empresa",
					titulo: "Empresa",
					visible: true,
					valueTransform(data) {
						return data.nome;
					},
				},
				{
					nome: "descricao",
					titulo: "Descriçao",
					visible: true,
				},
				{
					nome: "dataLancamento",
					titulo: "Data lançamento",
					visible: true,
					valueTransform(data) {
						return new Date(data).toLocaleDateString("pt-BR");
					},
				},
				{
					nome: "dataVencimento",
					titulo: "Vencimento",
					visible: true,
					valueTransform: this.formatarData,
				},
				{
					nome: "valor",
					titulo: "Valor total",
					visible: true,
					valueTransform(data = 0) {
						return data.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
				{
					nome: "situacao",
					titulo: "Status",
					visible: true,
					celula: this.cellSituacao,
				},
			],
		});
	}

	onChangePage(event: any) {
		const table = document.getElementById(
			"tbl-title-block-dchc-tbl-hist-parcela-gerada"
		);
		if (table) {
			table.scrollIntoView();
		}
	}

	private formatarData = (data: any): string => {
		return this.datePipe.transform(data, "dd/MM/yyyy", "UTC");
	};
}
