<header class="cabecalho-principal">
	<div class="seta">
		<i
			(click)="goBack()"
			class="pct pct-arrow-left"
			id="detalhamento-contrato-voltar"></i>
	</div>
	<div class="breadcrumbs">Clientes / Perfil do cliente</div>
	<div class="titulo" i18n="@@contract-detail:info-title">
		Detalhamento de contrato
	</div>
</header>

<div class="contrato-container">
	<div class="contract-detail">
		<pacto-cat-card-plain>
			<!-- VISÃO GERAL  -->
			<pacto-detalhamento-contrato-visao-geral
				(reloadContract)="reload()"
				(goBackClient)="goBack()"
				*ngIf="contract && dadosPessoais"
				[contract]="contract"
				[dadosPessoais]="dadosPessoais"
				[opcoesContrato]="opcoesContrato"
				class="contract-detail"></pacto-detalhamento-contrato-visao-geral>
		</pacto-cat-card-plain>
	</div>

	<ng-container
		*ngIf="contract && contract?.plano?.quantidadeCompartilhamentos > 0">
		<pacto-detalhamento-contrato-dependente
			(reloadContract)="reload()"
			[codigoContrato]="contract.codigo"
			[dadosPessoais]="dadosPessoais"
			class="contract-detail"></pacto-detalhamento-contrato-dependente>
	</ng-container>

	<!-- MODALIDADES -->
	<ng-container *ngIf="modalidades && modalidades.length > 0 && contract">
		<div class="contract-detail">
			<pacto-cat-card-plain>
				<pacto-detalhamento-contrato-modalidades
					(reloadContract)="reload()"
					[contrato]="contract"
					[dadosPessoais]="dadosPessoais"
					[opcoesContrato]="opcoesContrato"
					class="contract-detail"></pacto-detalhamento-contrato-modalidades>
			</pacto-cat-card-plain>
		</div>
	</ng-container>

	<ng-container *ngIf="contract?.vendaCreditoTreino">
		<div class="contract-detail">
			<pacto-cat-card-plain>
				<pacto-detalhamento-contrato-extrato-credito-treino
					[contrato]="contract"
					[dadosPessoais]="dadosPessoais"
					class="contract-detail"></pacto-detalhamento-contrato-extrato-credito-treino>
			</pacto-cat-card-plain>
		</div>
	</ng-container>
	<!-- OPERAÇÕES -->
	<pacto-cat-card-plain *ngIf="contract">
		<pacto-detalhamento-contrato-operacoes-contrato
			#operacoesContratoRef
			(reloadContract)="reload()"
			*ngIf="contract"
			[contrato]="contract"></pacto-detalhamento-contrato-operacoes-contrato>
	</pacto-cat-card-plain>
	<!-- HISTÓRICO -->
	<pacto-cat-card-plain *ngIf="contract">
		<div class="contract-detail">
			<div class="detail-title" i18n="@@detail-contract:historicos">
				Históricos
			</div>
			<div class="detail-data-container">
				<pacto-detalhamento-contrato-historico-contrato
					[codigoContrato]="
						contract.codigo
					"></pacto-detalhamento-contrato-historico-contrato>
			</div>
			<div class="detail-data-container">
				<pacto-detalhamento-contrato-historico-compras
					[codigoContrato]="
						contract.codigo
					"></pacto-detalhamento-contrato-historico-compras>
			</div>
			<div class="detail-data-container">
				<pacto-historico-de-parcelas-geradas
					[codigoContrato]="
						contract.codigo
					"></pacto-historico-de-parcelas-geradas>
			</div>
			<div class="detail-data-container">
				<pacto-historico-de-pagamentos-efetuados
					[codigoCliente]="dadosPessoais.codigoCliente"
					[codigoContrato]="contract.codigo"
					[dadosPessoais]="dadosPessoais"
					[nomeEmpresa]="
						dadosPessoais.empresa.nome
					"></pacto-historico-de-pagamentos-efetuados>
			</div>

			<!-- <div class="detail-data-container">
				<pacto-historico-cheque [contrato]="contract"></pacto-historico-cheque>
			</div> -->
		</div>
	</pacto-cat-card-plain>
</div>
