@import "~src/assets/scss/pacto/plataforma-import.scss";

.cabecalho-principal {
	display: grid;
	grid-template-columns: 1.5rem auto;
	grid-template-rows: auto auto;
	gap: 0.75rem;

	.seta {
		grid-column: 1 / 2;
		grid-row: 2 / 3;

		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		color: #51555a;
		text-align: center;
		font-size: 1.5rem;
		font-weight: 400;
	}

	.breadcrumbs {
		grid-column: 2 / 3;
		grid-row: 1 / 2;

		color: #6f747b;
		font-size: 0.75rem;
		font-weight: 600;
	}

	.titulo {
		grid-column: 2 / 3;
		grid-row: 2 / 3;

		color: #51555a;
		font-size: 1.375rem;
		font-weight: 700;
	}
}

.contrato-container {
	pacto-cat-card-plain {
		width: 100%;
		padding: 1.25rem 1rem;
	}

	pacto-relatorio {
		.pacto-table-title-block {
			padding: 0;
		}
	}

	> * + * {
		margin-top: 2rem;
	}
}

.contrato-container {
	padding-top: 34px;
	display: flex;
	flex-direction: column;
	align-items: center;

	&:only-child {
		margin-bottom: 32px;
	}
}

.contrato-footer {
	padding-top: 16px;
	padding-bottom: 16px;

	.contrato-container {
		.btn-row {
			.btn-contract {
				margin-right: 16px;
				padding: 8px 20px;
				font-size: 16px;
				line-height: 24px;

				i {
					margin-right: 4px;
				}

				&:last-child {
					margin-right: unset;
				}
			}
		}
	}
}

.contrato-header {
	flex-direction: row;

	> * + * {
		margin-top: 0;
	}

	.contract-info {
		div:first-of-type {
			color: $cinza07;
			font-weight: 400;
			font-size: 12px;
			line-height: 16px;
			margin-bottom: 3px;
			margin-top: 3px;
		}

		div:last-of-type {
			color: $pretoPri;
			font-weight: 600;
			font-size: 20px;
			line-height: 27px;
		}
	}

	.header-title {
		display: flex;
		margin-right: auto;
		align-self: start;

		.icon-back {
			cursor: pointer;
			font-size: 28px;
			align-self: flex-start;
			margin-right: 19px;
			height: 28px;
			width: 28px;
		}
	}

	.header-resume {
		display: flex;

		.contract-customer {
			margin-right: 120px;
		}

		.contract-initial-date {
			margin-right: 50px;
		}

		.contract-end-date {
		}
	}
}

:host {
	::ng-deep {
		pacto-relatorio {
			.pacto-table-title-block {
				.table-title {
					color: #51555a;
					font-size: 14px;
				}
			}
		}
	}
}
