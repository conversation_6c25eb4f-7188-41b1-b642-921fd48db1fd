import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	ElementRef,
	Input,
	<PERSON><PERSON><PERSON>roy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import {
	AdmCoreApiClienteService,
	AdmCoreApiContratoService,
	ApiResponseList,
	ClienteDadosPessoais,
	ContratoModalidade,
} from "adm-core-api";
import { AdmLegadoTelaClienteService, OpcoesContrato } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { ApiResponseSingle, Plano, PlanoApiPlanoService } from "plano-api";
import { CobrancaService } from "../../../../cobranca/cobranca.service";

@Component({
	selector: "pacto-detalhamento-contrato",
	templateUrl: "./detalhamento-contrato.component.html",
	styleUrls: [
		"./detalhamento-contrato.component.scss",
		"../contract-common.scss",
	],
})
export class DetalhamentoContratoComponent implements OnInit, OnDestroy {
	@Input() id: any;
	dadosPessoais: ClienteDadosPessoais;
	opcoesContrato: OpcoesContrato;
	modalidades: any;
	dadosTurma: any;
	contract: any;
	codigoContrato: any;
	alunoMatricula: number;
	url: string;
	turmas: any;
	@ViewChild("operacoesContratoRef", { static: false })
	operacoesContratoRef: any;

	constructor(
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private admCoreApiService: AdmCoreApiContratoService,
		private admCoreApiClientService: AdmCoreApiClienteService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private el: ElementRef,
		private planoService: PlanoApiPlanoService,
		private notificationService: SnotifyService,
		private cobrancaService: CobrancaService
	) {}

	ngOnInit() {
		this.el.nativeElement.scrollTop = "0px";
		this.alunoMatricula =
			+this.activatedRoute.parent.snapshot.paramMap.get("aluno-matricula");
		this.codigoContrato = this.activatedRoute.snapshot.paramMap.get("id");
		this.getModalidades();
		if (this.alunoMatricula) {
			this.admCoreApiClientService
				.dadosPessoais(this.alunoMatricula)
				.subscribe((response) => {
					this.dadosPessoais = response;
					this.cobrancaService.definirTitleTreino(this.dadosPessoais.nome);
					this.getContrato();
					this.cd.detectChanges();
				});
		}
	}

	ngOnDestroy(): void {
		this.cobrancaService.definirTitleTreino("Sistema Pacto");
	}

	obterOpcoesContrato() {
		this.telaClienteService
			.opcoesContrato(
				this.sessionService.chave,
				this.contract.codigo,
				this.dadosPessoais.codigoCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw
			)
			.subscribe(
				(response: ApiResponseSingle<OpcoesContrato>) => {
					this.opcoesContrato = response.content;
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message);
					}
				}
			);
	}

	getContrato() {
		this.admCoreApiService
			.visaoGeralContrato(this.codigoContrato)
			.subscribe((response) => {
				this.contract = response;
				const codigo = this.contract.plano
					? this.contract.plano.codigo
					: this.contract.codigo;
				this.planoService
					.findById(codigo)
					.subscribe((planoResponse: ApiResponseSingle<Plano>) => {
						this.contract.plano = planoResponse.content;
						this.cd.detectChanges();
					});
				this.obterOpcoesContrato();
				this.cd.detectChanges();
			});
	}

	getModalidades() {
		this.admCoreApiService
			.modalidadesContrato(this.codigoContrato)
			.subscribe((response: ApiResponseList<ContratoModalidade>) => {
				this.modalidades = response.content;
				this.cd.detectChanges();
			});
	}

	goBack() {
		this.router.navigate([this.alunoMatricula, "contratos"], {
			relativeTo: this.activatedRoute.parent.parent,
		});
	}

	reload() {
		this.getContrato();
		this.getModalidades();
		if (this.operacoesContratoRef) {
			if (this.operacoesContratoRef.tableRef) {
				this.operacoesContratoRef.tableRef.reloadData();
			}
		}
	}
}
