import {
	AfterViewInit,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-faltas",
	templateUrl: "./modal-faltas.component.html",
	styleUrls: ["./modal-faltas.component.scss"],
})
export class ModalFaltasComponent implements OnInit, AfterViewInit {
	codigoContrato;
	situacaoContrato;
	listaFaltas;
	dadosPessoais;
	table: PactoDataGridConfig;
	modalidadeTurma;
	@ViewChild("tableRef", { static: false })
	tableRef: RelatorioComponent;
	actualPage = 0;
	actualSize = 10;
	@ViewChild("celulaDesmarcar", { static: true })
	celulaDesmarcar: TemplateRef<any>;
	@Output()
	response = new EventEmitter<string>();

	constructor(
		private admTelaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private ngbActiveModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.listaFaltas;
			},
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "S",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricaoAulaApresentar",
					titulo: "Descrição.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "desmarcar",
					titulo: "",
					celula: this.celulaDesmarcar,
					visible: this.situacaoContrato === "AT",
					ordenavel: false,
				},
			],
		});
	}

	ngAfterViewInit() {
		this.listFaltas();
	}

	listFaltas() {
		this.admTelaClienteService
			.aulasContratoFaltas(
				{
					page: this.actualPage,
					size: this.actualSize,
				},
				this.sessionService.chave,
				this.codigoContrato,
				this.modalidadeTurma.turma.codigo
			)
			.subscribe((response) => {
				this.listaFaltas = response;
				this.listaFaltas.content.forEach(
					(item, index) => (item.codigo = index + 1)
				);
				this.tableRef.reloadData();
			});
	}

	onPageSizeChange(event: any) {
		this.actualSize = event;
		this.listFaltas();
	}

	onPageChange(event: any) {
		this.actualPage = event - 1;
		this.listFaltas();
	}

	iconClick(evt: { row: any; iconName: string }): void {
		this[evt.iconName](evt.row);
	}

	desmarcar(item: any) {
		this.response.emit(item);
		this.ngbActiveModal.dismiss();
	}
}
