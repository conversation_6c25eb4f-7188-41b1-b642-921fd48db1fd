<div class="modalidade-detail">
	<div class="detail-title" i18n="@@detail-contract:modalidades">
		Modalidades
	</div>
	<div class="btn-row">
		<pacto-cat-button
			(click)="faltasTurmasRemovidas()"
			*ngIf="!contrato?.vendaCreditoTreino && historicoTurmasContrato?.qtd > 0"
			i18n-label="@@contract-detail:overview:faltas-turmas-removidas"
			id="dcm-btn-falta-turma-removidas"
			label="Falta de turmas removidas {{ historicoTurmasContrato?.qtd }}"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
		<pacto-cat-button
			(click)="manutencaoModalidade()"
			*ngIf="
				opcoesContrato?.manutencaoModalidade &&
				empresaLogada?.codigo === contrato?.empresa?.codigo
			"
			i18n-label="@@contract-detail:overview:manutencao-modalidade"
			id="dcm-btn-manutencao-modalidade"
			label="Manutenção de modalidade"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
<div
	*ngFor="let modalidadeTurma of modalidadeTurmas; let index = index"
	class="detail-data-container dcm-modalidade-turma">
	<pacto-cat-accordion
		#tag
		[showIndicator]="modalidadeTurma.turma"
		id="dcm-modalidade-accord-{{ index }}">
		<accordion-header id="dcm-modalidade-accord-header-{{ index }}">
			<div *ngIf="modalidadeTurma.modalidade" class="title-modalidade-turma">
				<span>
					<strong>Modalidade:</strong>
					{{ modalidadeTurma.modalidade.nome | captalize }}
				</span>
				<span>
					<strong>Repetições:</strong>
					{{ modalidadeTurma.vezesSemana }}x semana
				</span>
				<span *ngIf="modalidadeTurma?.turma?.turma?.descricao">
					<strong>Turma:</strong>
					{{ modalidadeTurma?.turma?.turma?.descricao | captalize }}
				</span>
			</div>
		</accordion-header>
		<accordion-body *ngIf="modalidadeTurma.turma">
			<div class="modalidade-data">
				<div class="modalidade-table-horario">
					<div *ngIf="modalidadeTurma.table" class="modalidade-table">
						<pacto-relatorio
							[showShare]="false"
							[table]="modalidadeTurma.table"
							class="modalidade-table-disponibilidades"
							idSuffix="dcm-tbl-modaliade-turma-{{ index }}"></pacto-relatorio>
					</div>
				</div>

				<div class="modalidade-graph">
					<div class="chart-section">
						<!-- TODO Esse posicionamento é temporário, já que a lib ainda não fornece uma forma de colocar label customizada -->
						<div class="chart-div">
							<pacto-cat-pie-chart
								[colors]="colors"
								[labelCenter]="'Realizadas'"
								[series]="modalidadeTurma.pieOcuAulas"
								[showLegend]="false"
								[showPercentage]="false"
								[showTotal]="false"
								[showValue]="false"
								[simbol]="'sb'"
								[states]="graphState"
								[tooltipEnabled]="false"
								height="200px"
								i18n-labelCenter="
									@@detalhamento-contrato-modalidades:realizadas"
								width="auto"></pacto-cat-pie-chart>
							<div class="chart-values">
								<div>
									<span
										[darkTheme]="true"
										[pactoCatTolltip]="infoCalculoRef"
										position="bottom">
										{{
											modalidadeTurma.pieOcuAulas[0].data +
												"/" +
												(modalidadeTurma.pieOcuAulas[0].data +
													modalidadeTurma.pieOcuAulas[1].data)
										}}
									</span>
									<ng-template #infoCalculoRef>
										<p *ngIf="tooltipMensagem === 'presencasFaltasDesmarcadas'">
											Presenças + Faltas + Aulas desmarcadas (exceto aulas
											desmarcadas futuras)
										</p>
										<p *ngIf="tooltipMensagem === 'saldoDeCredito'">
											Saldo de crédito. Obs.: O saldo de créditos pode ser maior
											que a quantidade de compra, pois pode haver transferência
											de saldo do contrato anterior ou ajuste manual de
											crédito para maior.
										</p>
									</ng-template>
								</div>
								<div>
									<span>Realizadas</span>
								</div>
								<div>
									<span>
										({{
											modalidadeTurma.pieOcuAulas[0].data +
												modalidadeTurma.pieOcuAulas[1].data -
												modalidadeTurma.pieOcuAulas[0].data
										}}
										Restantes)
									</span>
								</div>
							</div>
						</div>
						<div class="others-info">
							<div class="other-info-row">
								<div class="other-info">
									<div class="oi-value out-color" id="dcm-turma-presenca">
										{{
											modalidadeTurma?.turma.presencas
												? modalidadeTurma.turma.presencas
												: 0
										}}
									</div>
									<div class="oi-label">Presenças</div>
								</div>
								<div class="other-info">
									<div
										(click)="abrirReposicoes(modalidadeTurma)"
										[ngClass]="{
											'out-color': modalidadeTurma.turma.reposicoes == 0
										}"
										class="oi-value"
										id="dcm-turma-reposicao">
										{{
											modalidadeTurma?.turma.reposicoes
												? modalidadeTurma.turma.reposicoes
												: 0
										}}
									</div>
									<div class="oi-label">Reposições</div>
								</div>
							</div>
							<div class="other-info-row">
								<div class="other-info">
									<div
										(click)="
											contrato.vendaCreditoTreino
												? $event.preventDefault()
												: abrirFaltas(modalidadeTurma)
										"
										[ngClass]="{
											'out-color':
												modalidadeTurma.turma.faltas == 0 ||
												modalidadeTurma.turma.faltas == null ||
												contrato.vendaCreditoTreino
										}"
										class="oi-value"
										id="dcm-turma-falta">
										{{
											modalidadeTurma?.turma.faltas
												? modalidadeTurma.turma.faltas
												: 0
										}}
									</div>
									<div class="oi-label">Faltas</div>
								</div>
								<div class="other-info">
									<div
										(click)="abrirDesmarcadas(modalidadeTurma)"
										[ngClass]="{
											'out-color': modalidadeTurma.turma.desmarcadas == 0
										}"
										class="oi-value"
										id="dcm-turma-desmarcada">
										{{
											modalidadeTurma?.turma.desmarcadas
												? modalidadeTurma.turma.desmarcadas
												: 0
										}}
									</div>
									<div class="oi-label">Desmarcadas</div>
								</div>
							</div>
							<div
								*ngIf="configEmpresa?.adicionarAulasDesmarcadasContratoAnterior"
								class="other-info-row">
								<div class="other-info oi-contrato-anterior">
									<div
										(click)="abrirDesmarcadas(modalidadeTurma, true)"
										[ngClass]="{
											'out-color':
												modalidadeTurma.turma
													.totalAulasDesmarcadasContratoPassado == 0
										}"
										class="oi-value"
										id="dcm-turma-desmarcada-contrato-anterior">
										{{
											modalidadeTurma?.turma
												.totalAulasDesmarcadasContratoPassado
												? modalidadeTurma.turma
														.totalAulasDesmarcadasContratoPassado
												: 0
										}}
									</div>
									<div class="oi-label">Desmarcadas de Contrato anterior</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</accordion-body>
	</pacto-cat-accordion>
</div>
<!-- TEMPLATES SECTION -->
<ng-template #celulaRepetir let-modalidadeTurma="item">
	<span
		(click)="repor(modalidadeTurma.horarioTurma.codigo, 0)"
		class="detail-contract-celula-link">
		Repor
	</span>
</ng-template>

<ng-template #celulaDesmarcar let-modalidadeTurma="item">
	<span
		(click)="desmarcarProxima(modalidadeTurma, modalidadeTurma.horarioTurma)"
		class="detail-contract-celula-link">
		Desmarcar
	</span>
</ng-template>

<ng-template #celulaNivel let-item="item">
	<span [darkTheme]="true" [pactoCatTolltip]="'Nível'">
		{{ item?.horarioTurma?.nivelTurma?.descricao }}
	</span>
</ng-template>

<ng-template #celulaDiaSemana let-item="item">
	<span [darkTheme]="true" [pactoCatTolltip]="'Dia da semana'">
		{{ item?.diaSemana }}
	</span>
</ng-template>

<ng-template #celulaProfessor let-item="item">
	<span [darkTheme]="true" [pactoCatTolltip]="'Professor'">
		{{ item?.professor }}
	</span>
</ng-template>

<ng-template #celulaHorario let-item="item">
	<span [darkTheme]="true" [pactoCatTolltip]="'Horário'">
		{{ item?.horario }}
	</span>
</ng-template>

<ng-template #celulaLocal let-item="item">
	<span [darkTheme]="true" [pactoCatTolltip]="'Local'">
		{{ item?.local }}
	</span>
</ng-template>
