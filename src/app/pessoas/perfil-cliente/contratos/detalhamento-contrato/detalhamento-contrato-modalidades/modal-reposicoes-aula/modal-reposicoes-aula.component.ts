import {
	AfterViewInit,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-reposicoes-aula",
	templateUrl: "./modal-reposicoes-aula.component.html",
	styleUrls: ["./modal-reposicoes-aula.component.scss"],
})
export class ModalReposicoesAulaComponent implements OnInit, AfterViewInit {
	codigoContrato;
	listaReposicoes;
	table: PactoDataGridConfig;
	@ViewChild("tableRef", { static: false })
	tableRef: RelatorioComponent;
	modalidadeTurma;
	actualPage = 0;
	actualSize = 10;
	@Output()
	response = new EventEmitter<any>();

	constructor(
		private admTelaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.listaReposicoes;
			},
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataLancamento",
					titulo: "Dt. Lanc.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "aulaDesmarcada",
					titulo: "Aula desmarcada",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "aulaReposicao",
					titulo: "Aula reposição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "presenca",
					titulo: "Presença",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "usuario",
					titulo: "Usuário",
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "enviar",
					iconClass: "pct pct-send cor-azulim05",
					tooltipText:
						"Envia os dados da Reposição por e-mail e/ou SMS, se possível",
					actionFn: (row) => this.enviarEmailSms(row),
				},
				{
					nome: "excluir",
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "Excluir esta Reposição",
					actionFn: (row) => this.excluir(row),
					showIconFn: (row) => row.presenca === "Falta",
				},
			],
		});
	}

	ngAfterViewInit() {
		this.listReposicoes();
	}

	enviarEmailSms(rowInfo) {
		this.admTelaClienteService
			.enviarReposicaoEmailSMS(
				this.sessionService.chave,
				rowInfo.row.codigo,
				this.sessionService.codUsuarioZW
			)
			.subscribe(
				(response) => {
					if (!response.content) {
						this.snotifyService.error(response.meta.message);
					} else {
						this.snotifyService.success(response.content);
					}
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta) {
						if (error.meta.error) {
							this.snotifyService.error(error.meta.message);
						}
					}
				}
			);
	}

	excluir(rowInfo) {
		this.admTelaClienteService
			.excluirReposicao(
				this.sessionService.chave,
				rowInfo.row.codigo,
				this.sessionService.codUsuarioZW
			)
			.subscribe(
				(response) => {
					if (!response.content) {
						this.snotifyService.error(response.meta.message);
					} else {
						this.snotifyService.success("Reposição excluida");
						this.response.emit("atualizar_contrato");
						this.listReposicoes();
					}
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta) {
						if (error.meta.error) {
							this.snotifyService.error(error.meta.message);
						}
					}
				}
			);
	}

	listReposicoes() {
		this.admTelaClienteService
			.aulasContratoReposicoes(
				{
					page: this.actualPage,
					size: this.actualSize,
				},
				this.sessionService.chave,
				this.codigoContrato,
				this.modalidadeTurma.turma.turma.codigo
			)
			.subscribe((response) => {
				this.listaReposicoes = response;
				this.tableRef.reloadData();
			});
	}

	onPageSizeChange(event: any) {
		this.actualSize = event;
		this.listReposicoes();
	}

	onPageChange(event: any) {
		this.actualPage = event - 1;
		this.listReposicoes();
	}
}
