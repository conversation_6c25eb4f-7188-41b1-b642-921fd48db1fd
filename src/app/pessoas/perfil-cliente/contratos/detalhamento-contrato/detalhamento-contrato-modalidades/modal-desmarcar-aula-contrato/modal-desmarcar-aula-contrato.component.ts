import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService } from "../../../../../../microservices/client-discovery/client-discovery.service";

declare var moment;

@Component({
	selector: "pacto-modal-desmarcar-aula-contrato",
	templateUrl: "./modal-desmarcar-aula-contrato.component.html",
	styleUrls: ["./modal-desmarcar-aula-contrato.component.scss"],
})
export class ModalDesmarcarAulaContratoComponent implements OnInit {
	contrato;
	horarioTurma;
	aulaDesmarcada;
	codigoContrato;
	dadosPessoais: ClienteDadosPessoais;
	selecionarTurmaDesmarcarAula: boolean;
	turmas: Array<{ codigo: number; descricao: string }> = new Array<{
		codigo: number;
		descricao: string;
	}>();
	form = new FormGroup({
		data: new FormControl(),
		turma: new FormControl(),
	});
	diaDaSemana = {
		1: "Domingo",
		2: "Segunda-Feira",
		3: "Terça-Feira",
		4: "Quarta-Feira",
		5: "Quinta-Feira",
		6: "Sexta-Feira",
		7: "Sábado",
	};
	@Output()
	response = new EventEmitter<any>();

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService,
		private clientDiscoveryService: ClientDiscoveryService
	) {}

	ngOnInit() {
		if (this.horarioTurma.dataDesmarcar) {
			this.form.get("data").setValue(this.horarioTurma.dataDesmarcar);
		} else {
			// const dataDesmarcar = new Date();
			// do {
			// 	dataDesmarcar.setDate(dataDesmarcar.getDate() + 1);
			// } while (dataDesmarcar.getDay() !== this.horarioTurma.diaSemanaNumero - 1);
			// this.form.get('data').setValue(dataDesmarcar);
		}
		if (this.selecionarTurmaDesmarcarAula) {
			const dataStringSplitSpace = this.aulaDesmarcada.inicio.split(" ");
			const dataStringSplitBar = dataStringSplitSpace[0].split("/");

			const day = +dataStringSplitBar[0];
			const month = +dataStringSplitBar[1];
			const year = +dataStringSplitBar[2];

			const date = new Date(year, month - 1, day, 0, 0, 0, 0);

			this.form.get("data").setValue(date);
			if (this.contrato.modalidades) {
				this.contrato.modalidades.forEach((modalidade) => {
					if (modalidade.turmas) {
						modalidade.turmas.forEach((turma) => {
							this.turmas.push({
								codigo: turma.turma.codigo,
								descricao: turma.turma.descricao,
							});
						});
					}
				});
			}
		}
		if (this.turmas.length === 0) {
			this.selecionarTurmaDesmarcarAula = false;
			this.form.get("turma").disable();
		}
	}

	desmarcar() {
		if (!this.form.get("data").value) {
			this.notificationService.error("Informe o dia da aula");
			return;
		}
		const dataDesmarcar = moment(this.form.get("data").value).format(
			"YYYY-MM-DD"
		);
		this.telaClienteService
			.desmarcarHorario(
				this.sessionService.chave,
				this.horarioTurma.codigo,
				this.dadosPessoais.codigoCliente,
				this.codigoContrato,
				this.sessionService.loggedUser.usuarioZw,
				dataDesmarcar,
				+this.form.get("turma").value
			)
			.subscribe(
				(response) => {
					this.notificationService.success(response.content);
					this.response.emit("atualizar_contrato");
					this.dialog.close();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error) {
						if (error.meta) {
							this.notificationService.error(error.meta.message);
						}
					} else {
						this.notificationService.error(
							"Ocorreu um erro no servidor! Entre em contato com o suporte!"
						);
					}
				}
			);
	}

	openListaAlunosTurma() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=LISTA_ALUNO_TURMA&isOperacaoFinanceiro=true`;
				url += `&codHorarioTurma=${this.horarioTurma.codigo}&diaSemana=${
					this.horarioTurma.diaSemana
				}&dataBase=${new Date(this.form.get("data").value).toISOString()}`;
				// Fixes dual-screen position                             Most browsers      Firefox
				const dualScreenLeft =
					window.screenLeft !== undefined ? window.screenLeft : window.screenX;
				const dualScreenTop =
					window.screenTop !== undefined ? window.screenTop : window.screenY;
				const windowWidth = 800;
				const windowHeight = 525;
				const width = window.innerWidth
					? window.innerWidth
					: document.documentElement.clientWidth
					? document.documentElement.clientWidth
					: screen.width;
				const height = window.innerHeight
					? window.innerHeight
					: document.documentElement.clientHeight
					? document.documentElement.clientHeight
					: screen.height;

				const systemZoom = width / window.screen.availWidth;
				const posTopo =
					(height - windowHeight) / 2 / systemZoom + dualScreenTop;
				const posEsquerda =
					(width - windowWidth) / 2 / systemZoom + dualScreenLeft;

				let features = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo},`;
				features += ` width=${windowWidth}, height=${windowHeight} dependent=yes, menubar=no,`;
				features += " toolbar=no, resizable=yes , scrollbars=yes";
				window.open(url, "Lista de Alunos da Turma", features);
			});
	}
}
