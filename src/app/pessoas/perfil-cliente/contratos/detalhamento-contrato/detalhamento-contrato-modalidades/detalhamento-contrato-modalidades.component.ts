import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import {
	AdmCoreApiContratoService,
	ApiResponseList,
	ClienteDadosPessoais,
	ContratoModalidade,
	ContratoModalidadeTurma,
	Modalidade,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	OpcoesContrato,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { EmpresaFinanceiro } from "treino-api";
import {
	AutorizacaoAcessoComponent,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoColor,
	PactoDataGridConfig,
	PactoModalSize,
	PieChartSet,
} from "ui-kit";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";
import { PerfilClienteService } from "../../../perfil-cliente.service";
import { ModalAulasDesmarcadasComponent } from "./modal-aulas-desmarcadas/modal-aulas-desmarcadas.component";
import { ModalDesmarcarAulaContratoComponent } from "./modal-desmarcar-aula-contrato/modal-desmarcar-aula-contrato.component";
import { ModalFaltasComponent } from "./modal-faltas/modal-faltas.component";
import { ModalHistoricoFaltasComponent } from "./modal-historico-faltas/modal-historico-faltas.component";
import { ModalReposicoesAulaComponent } from "./modal-reposicoes-aula/modal-reposicoes-aula.component";
import { MatDialog } from "@angular/material";

@Component({
	selector: "pacto-detalhamento-contrato-modalidades",
	templateUrl: "./detalhamento-contrato-modalidades.component.html",
	styleUrls: [
		"./detalhamento-contrato-modalidades.component.scss",
		"../../contract-common.scss",
	],
	encapsulation: ViewEncapsulation.None,
})
export class DetalhamentoContratoModalidadesComponent implements OnInit {
	contratoModalidades: Array<ContratoModalidade>;
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() contrato: any;
	@Input() opcoesContrato: OpcoesContrato;
	@Output() reloadContract: EventEmitter<any> = new EventEmitter<any>();

	@ViewChild("celulaRepetir", { static: true }) celulaRepetir: TemplateRef<any>;
	@ViewChild("celulaDesmarcar", { static: true })
	celulaDesmarcar: TemplateRef<any>;
	@ViewChild("celulaNivel", { static: true }) celulaNivel: TemplateRef<any>;
	@ViewChild("celulaLocal", { static: true }) celulaLocal: TemplateRef<any>;
	@ViewChild("celulaDiaSemana", { static: true })
	celulaDiaSemana: TemplateRef<any>;
	@ViewChild("celulaProfessor", { static: true })
	celulaProfessor: TemplateRef<any>;
	@ViewChild("celulaHorario", { static: true }) celulaHorario: TemplateRef<any>;
	empresaLogada: EmpresaFinanceiro;

	url: string;
	codigoContrato: any;
	modalidadeTurmas: Array<{
		table?: PactoDataGridConfig;
		modalidade: Modalidade;
		turma?: ContratoModalidadeTurma;
		pieOcuAulas?: PieChartSet[];
		vezesSemana?: number;
	}> = new Array<{
		table: PactoDataGridConfig;
		modalidade: Modalidade;
		turma: ContratoModalidadeTurma;
		pieOcuAulas: PieChartSet[];
		vezesSemana?: number;
	}>();
	graphState: ApexStates = {
		hover: {
			filter: {
				type: "none",
			},
		},
	};

	table: PactoDataGridConfig;
	colors: PactoColor[] = [PactoColor.AZULIM_05, PactoColor.CINZA_CLARO_PRI];
	historicoTurmasContrato;
	configEmpresa;
	tooltipMensagem: "presencasFaltasDesmarcadas" | "saldoDeCredito";

	constructor(
		private dialogService: DialogService,
		private cd: ChangeDetectorRef,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private clientDiscoveryService: ClientDiscoveryService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private perfilClienteService: PerfilClienteService,
		private admCoreApiService: AdmCoreApiContratoService,
		private matDialog: MatDialog
	) {}

	ngOnInit() {
		this.url = window.location.href;
		this.codigoContrato = this.url.substring(this.url.lastIndexOf("/") + 1);
		this.configEmpresa = this.perfilClienteService.empresa;
		this.empresaLogada = this.sessionService.currentEmpresa;
		this.getContratoModalidades();
	}

	reload() {
		this.getContratoModalidades();
	}

	getContratoModalidades() {
		this.modalidadeTurmas = [];
		this.admCoreApiService
			.modalidadesContrato(this.codigoContrato)
			.subscribe((response: ApiResponseList<ContratoModalidade>) => {
				this.contratoModalidades = response.content;
				this.carregar();
				this.cd.detectChanges();
			});
	}

	carregar() {
		this.buscarFaltasTurmaRemovidas();
		const diaDaSemana = {
			DM: "Domingo",
			SG: "Segunda-Feira",
			TR: "Terça-Feira",
			QA: "Quarta-Feira",
			QI: "Quinta-Feira",
			SX: "Sexta-Feira",
			SB: "Sábado",
		};
		if (this.contratoModalidades !== undefined) {
			this.contratoModalidades.forEach((contratoModalidade: any) => {
				contratoModalidade.turmas.forEach((contratoModalidadeTurma) => {
					const horarios = [];
					contratoModalidadeTurma.horarios.forEach((horario) => {
						horarios.push({
							codigo: horario.codigo,
							percDesconto: horario.percDesconto,
							percOcupacao: horario.percOcupacao,
							horario: `${horario.horarioTurma.horaInicial} - ${horario.horarioTurma.horaFinal}`,
							local: horario.horarioTurma.ambiente.descricao,
							professor: horario.horarioTurma.professor.pessoa.nome,
							diaSemana: diaDaSemana[horario.horarioTurma.diaSemana],
							horarioTurma: horario.horarioTurma,
							turma: contratoModalidadeTurma.turma,
						});
					});
					horarios.sort((h1, h2) => {
						if (
							h1.horarioTurma.diaSemanaNumero > h2.horarioTurma.diaSemanaNumero
						) {
							return 1;
						}

						if (
							h1.horarioTurma.diaSemanaNumero < h2.horarioTurma.diaSemanaNumero
						) {
							return -1;
						}
						return 0;
					});
					let aulasRealizadas = 0;
					let aulasRestantes = 0;

					if (
						this.contrato &&
						(this.contrato.vendaCreditoTreino ||
							this.contrato.vendaCreditoSessao)
					) {
						aulasRealizadas =
							contratoModalidadeTurma.quantidadeCreditoCompra -
							contratoModalidadeTurma.quantidadeCreditoDisponivel;
						aulasRestantes =
							contratoModalidadeTurma.quantidadeCreditoDisponivel || 0;
						this.tooltipMensagem = "saldoDeCredito";
					} else {
						aulasRealizadas = contratoModalidadeTurma.totalAulasHoje;
						aulasRestantes =
							contratoModalidadeTurma.totalAulas -
							contratoModalidadeTurma.totalAulasHoje;
						this.tooltipMensagem = "presencasFaltasDesmarcadas";
					}
					this.modalidadeTurmas.push({
						modalidade: contratoModalidade.modalidade,
						turma: contratoModalidadeTurma,
						table: this.createTable(horarios),
						pieOcuAulas: [
							{
								// aulas realizadas
								name: "",
								data: aulasRealizadas,
							},
							{
								// aulas restantes
								name: "",
								data: aulasRestantes,
							},
						],
						vezesSemana: contratoModalidade.vezesSemana,
					});
				});
				if (contratoModalidade.turmas.length === 0) {
					this.modalidadeTurmas.push({
						modalidade: contratoModalidade.modalidade,
						vezesSemana: contratoModalidade.vezesSemana,
					});
				}
			});
			this.cd.detectChanges();
		}
	}

	buscarFaltasTurmaRemovidas() {
		this.telaClienteService
			.faltasTurmasRemovidas(
				this.sessionService.chave,
				this.contrato.codigo,
				this.contrato.vigenciaAteAjustada,
				this.dadosPessoais.codigoCliente,
				this.dadosPessoais.matricula
			)
			.subscribe((response) => {
				this.historicoTurmasContrato = response.content;
				this.cd.detectChanges();
			});
	}

	createTable(data: any) {
		console.log(data);
		if (!data) {
			return undefined;
		}
		return new PactoDataGridConfig({
			rowClick: false,
			pagination: false,
			dataAdapterFn: (serverData) => {
				let response = { content: data };
				response.content.forEach((element, index) => {
					element.diaSemana =
						element.diaSemana ||
						this.correctDiaSemana(element.horarioTurma.diaSemana);
				});
				return response;
			},
			columns: [
				{
					nome: "diaSemana",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDiaSemana,
				},
				{
					nome: "horario",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaHorario,
				},
				{
					nome: "professor",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaProfessor,
				},
				{
					nome: "local",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaLocal,
				},
				{
					nome: "nivel",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaNivel,
				},
				{
					nome: "repetir",
					titulo: "",
					celula: this.celulaRepetir,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "desmarcar",
					titulo: "",
					celula: this.celulaDesmarcar,
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	repor(codHorarioTurma: any, codAulaDesmarcada: any) {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.patchValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"RemarcarAulaPerdida_Autorizar",
					"3.23 - Remarcar Aula Perdida - Autorizar",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						this.clientDiscoveryService
							.linkZw(
								this.sessionService.usuarioOamd,
								this.sessionService.empresaId
							)
							.subscribe((oamdUrl) => {
								this.url = `${oamdUrl}&funcionalidadeNome=CONSULTA_DE_TURMAS&codHorarioTurma=${codHorarioTurma}&codCliente=${this.dadosPessoais.codigoCliente}&codAulaDesmarcada=${codAulaDesmarcada}&codContrato=${this.contrato.codigo}&prepararConsultaTurma=true&origem=angular`;
								this.abrirPopup(this.url, "Remarcar aula perdida", 800, 595);
							});
						this.cd.detectChanges();
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	desmarcarProxima(modalidadeTurma: any, horarioTurma: any) {
		// const dataDesmarcar = new Date();
		// do {
		// 	dataDesmarcar.setDate(dataDesmarcar.getDate() + 1);
		// } while (dataDesmarcar.getDay() !== (horarioTurma.diaSemanaNumero - 1));
		horarioTurma.identificadorTurma = modalidadeTurma.turma
			? modalidadeTurma.turma.descricao
			: "";
		// horarioTurma.dataDesmarcar = dataDesmarcar;;
		this.desmarcar(horarioTurma);
	}

	desmarcar(horarioTurma: any) {
		const dialogRef = this.dialogService.open(
			"Desmarcar aula",
			ModalDesmarcarAulaContratoComponent,
			PactoModalSize.LARGE,
			"modal-desmarcar-aula-contrato-wrapper"
		);
		dialogRef.componentInstance.horarioTurma = horarioTurma;
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		dialogRef.componentInstance.response.subscribe((res) => {
			if (res === "atualizar_contrato") {
				this.reloadContract.emit();
				this.reload();
				this.cd.detectChanges();
			}
		});
	}

	manutencaoModalidade() {
		this.telaClienteService
			.validarParcelaAbertaComBoletoPendente(
				this.sessionService.chave,
				this.codigoContrato
			)
			.subscribe(
				(response) => {
					this.clientDiscoveryService
						.linkZw(
							this.sessionService.usuarioOamd,
							this.sessionService.empresaId
						)
						.subscribe(
							(result) => {
								const url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&codContrato=${this.codigoContrato}&operacaoClienteEnumName=MANUTENCAO_MODALIDADE&isContratoOperacao=true&origem=angular`;
								this.abrirPopup(url, "Manutenção de Modalidade", 800, 595);
							},
							(error) => {
								console.error("Erro ao abrir o popup:", error);
								this.notificationService.error(error.error.meta.message, {
									bodyMaxLength: 1000,
								});
							}
						);
				},
				(error) => {
					console.error("Erro na validação de parcelas:", error);
					this.notificationService.error(error.error.meta.message, {
						bodyMaxLength: 1000,
					});
				}
			);
	}

	abrirReposicoes(modalidadeTurma) {
		const dialogRef = this.dialogService.open(
			"Reposições solicitadas na turma",
			ModalReposicoesAulaComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.modalidadeTurma = modalidadeTurma;
		dialogRef.componentInstance.response.subscribe((res) => {
			if (res === "atualizar_contrato") {
				this.reloadContract.emit();
				this.reload();
				this.cd.detectChanges();
			}
		});
	}

	abrirFaltas(modalidadeTurma) {
		const diaDaSemanaNumero = {
			DM: 1,
			SG: 2,
			TR: 3,
			QA: 4,
			QI: 5,
			SX: 6,
			SB: 7,
		};
		const dialogRef = this.dialogService.open(
			"Faltas",
			ModalFaltasComponent,
			PactoModalSize.LARGE,
			"modal-faltas-component-window"
		);
		dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		dialogRef.componentInstance.situacaoContrato = this.contrato.situacao;
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		dialogRef.componentInstance.modalidadeTurma = modalidadeTurma;
		dialogRef.componentInstance.response.subscribe((res) => {
			const pessoa = {
				nome: res.responsavel,
			};
			const professor = {
				pessoa,
			};
			const horario = res.horario.split(" - ");
			const inicio = res.inicio.split(" ");
			const dateString = inicio[0];
			const dateParts = dateString.split("/");
			const dataDesmarcar = new Date(
				+dateParts[2],
				dateParts[1] - 1,
				+dateParts[0]
			);

			const horarioTurma = {
				codigo: res.id,
				professor,
				diaSemana: res.diaSemana,
				identificadorTurma: res.titulo,
				diaSemanaNumero: diaDaSemanaNumero[res.diaSemana],
				horaInicial: horario[0],
				horaFinal: horario[1],
				dataDesmarcar,
			};

			this.desmarcar(horarioTurma);
			this.cd.detectChanges();
		});
	}

	abrirDesmarcadas(modalidadeTurma, contratoAnterior = false) {
		const dialogRef = this.dialogService.open(
			"Aulas desmarcadas",
			ModalAulasDesmarcadasComponent,
			PactoModalSize.LARGE
		);
		if (contratoAnterior) {
			dialogRef.componentInstance.codigoContrato = this.codigoContrato;
			dialogRef.componentInstance.codigoContratoAnterior =
				this.contrato.contratoBaseadoRenovacao ||
				this.contrato.contratoBaseadoRematricula;
		} else {
			dialogRef.componentInstance.codigoContrato = this.codigoContrato;
		}
		dialogRef.componentInstance.modalidadeTurma = modalidadeTurma;
		dialogRef.componentInstance.response.subscribe((res) => {
			this.repor(res.horarioTurma, res.aulaDesmarcada);
			this.cd.detectChanges();
		});
	}

	faltasTurmasRemovidas() {
		const dialogRef = this.dialogService.open(
			"Histórico de faltas",
			ModalHistoricoFaltasComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.contrato = this.contrato;
		dialogRef.componentInstance.listaFaltas =
			this.historicoTurmasContrato.faltas;
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		dialogRef.componentInstance.response.subscribe((res) => {
			if (res === "atualizar_contrato") {
				this.reloadContract.emit();
				this.reload();
				this.cd.detectChanges();
			}
		});
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	) {
		const posTopo = 0;
		const posEsquerda = 0;
		const atributos = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo}, width=${comprimento}, height=${altura}, dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes`;
		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		const win = window.open(urlPopup, nomeJanela, atributos);
		const timer = setInterval(() => {
			if (win.closed) {
				clearInterval(timer);
				this.reloadContract.emit();
				this.reload();
			}
		}, 500);
	}

	correctDiaSemana(string) {
		switch (string.toUpperCase()) {
			case "SG":
				return "Segunda";
			case "TR":
				return "Terça";
			case "QA":
				return "Quarta";
			case "QI":
				return "Quinta";
			case "SX":
				return "Sexta";
			case "SB":
				return "Sábado";
			case "DM":
				return "Domingo";
			default:
				return string;
		}
	}
}
