import {
	AfterViewInit,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-aulas-desmarcadas",
	templateUrl: "./modal-aulas-desmarcadas.component.html",
	styleUrls: ["./modal-aulas-desmarcadas.component.scss"],
})
export class ModalAulasDesmarcadasComponent implements OnInit, AfterViewInit {
	codigoContrato;
	codigoContratoAnterior;
	listaAulasDesmarcadas;
	table: PactoDataGridConfig;
	modalidadeTurma;
	@ViewChild("tableRef", { static: false })
	tableRef: RelatorioComponent;
	actualPage = 0;
	actualSize = 10;
	@Output()
	response = new EventEmitter<any>();

	constructor(
		private admTelaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private ngbActiveModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.listaAulasDesmarcadas;
			},
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataLancamento",
					titulo: "Dt. Lanc.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "aulaDesmarcada",
					titulo: "Aula desmarcada",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "usuario",
					titulo: "Usuário",
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "repor",
					iconClass: "pct pct-calendar cor-azulim05",
					tooltipText: "Agendar Reposição",
					actionFn: (row) => this.repor(row),
				},
				{
					nome: "excluir",
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "Excluir Aula Desmarcada",
					actionFn: (row) => this.excluir(row),
				},
			],
		});
	}

	ngAfterViewInit() {
		this.listAulasDesmarcadas();
	}

	listAulasDesmarcadas() {
		if (this.codigoContratoAnterior) {
			this.admTelaClienteService
				.aulasContratoDesmarcadasContratoAnterior(
					{
						page: this.actualPage,
						size: this.actualSize,
					},
					this.sessionService.chave,
					this.codigoContrato,
					this.codigoContratoAnterior,
					this.sessionService.empresaId
				)
				.subscribe((response) => {
					this.listaAulasDesmarcadas = response;
					this.tableRef.reloadData();
				});
		} else {
			this.admTelaClienteService
				.aulasContratoDesmarcadas(
					{
						page: this.actualPage,
						size: this.actualSize,
					},
					this.sessionService.chave,
					this.codigoContrato,
					this.modalidadeTurma.turma.turma.codigo
				)
				.subscribe((response) => {
					this.listaAulasDesmarcadas = response;
					this.tableRef.reloadData();
				});
		}
	}

	onPageSizeChange(event: any) {
		this.actualSize = event;
		this.listAulasDesmarcadas();
	}

	onPageChange(event: any) {
		this.actualPage = event - 1;
		this.listAulasDesmarcadas();
	}

	repor(rowInfo) {
		console.log(rowInfo);
		const res = {
			horarioTurma: rowInfo.row.horarioTurma,
			aulaDesmarcada: rowInfo.row.codigo,
		};
		this.response.emit(res);
		this.ngbActiveModal.dismiss();
	}

	excluir(rowInfo) {
		this.admTelaClienteService
			.excluirAulaDesmarcada(
				this.sessionService.chave,
				rowInfo.row.codigo,
				this.sessionService.codUsuarioZW
			)
			.subscribe(
				(response) => {
					if (!response.content) {
						this.snotifyService.error(response.meta.message);
					} else {
						this.snotifyService.success("Aula desmarcada foi excluída");
						this.listAulasDesmarcadas();
					}
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta) {
						if (error.meta.error) {
							this.snotifyService.error(error.meta.message);
						}
					}
				}
			);
	}
}
