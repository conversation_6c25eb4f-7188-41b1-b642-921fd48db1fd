@import "~src/assets/scss/pacto/plataforma-import.scss";

.modalidade-data {
	display: flex;
	align-items: center;
	width: 100%;

	.modalidade-table-horario {
		width: 62%;
		padding-right: 15px;
		position: relative;

		.modalidade-table-title {
			display: flex;
			width: inherit;
			font-weight: 700;
			font-size: 16px;
			line-height: 20px;
			color: $pretoPri;
			margin-bottom: 16px;

			.modalidade-name {
				margin-right: 74px;
			}
		}

		.modalidade-table {
			.table-content {
				padding: unset;

				pacto-relatorio-renderer {
					table > tbody > tr:nth-child(odd) {
						background-color: #fafafa;
					}

					table > tbody > tr td:nth-child(6),
					table > tbody > tr td:nth-child(5) {
						text-align: center;
					}

					.table {
						thead {
							display: none;
						}

						tbody {
							tr {
								td {
									border: 0;

									.column-cell {
										.detail-contract-celula-link {
											color: $azulimPri;
											cursor: pointer;
											font-weight: 900;

											&:hover {
												text-decoration: underline;
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.modalidade-graph {
		margin-left: auto;

		.chart-section {
			display: flex;
			margin-top: 16px;
			position: relative;

			&::after {
				position: absolute;
				height: 70%;
				top: 15%;
				left: 0;
				content: "";
				width: 1px;
				background-color: #e0e1e3;
			}

			.chart-div {
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;

				.chart-values {
					position: absolute;
					text-align: center;
					justify-content: center;
					top: 50%;
					left: 0;
					right: 0;
					transform: translateY(-50%);

					div {
						&:nth-child(1) {
							font-weight: 600;
							font-size: 28px;
							line-height: 38px;
							color: $pretoPri;
						}

						&:nth-child(2) {
							font-weight: 700;
							font-size: 10px;
							line-height: 13.64px;
							color: $pretoPri;
						}

						&:nth-child(3) {
							font-weight: 400;
							font-size: 10px;
							line-height: 13.64px;
							color: $preto02;
						}
					}
				}
			}

			.others-info {
				margin-left: auto;
				display: flex;
				flex-direction: column;
				justify-content: center;

				.other-info-row {
					display: flex;

					&:first-child {
						margin-bottom: 24px;
					}

					.other-info {
						text-align: center;
						min-width: 86px;
						min-height: 49px;

						&:first-child {
							margin-right: 20px;
						}

						&.oi-contrato-anterior {
							margin-right: unset;
							margin-top: 10px;
						}

						.oi-value {
							font-weight: 700;
							font-size: 24px;
							line-height: 33px;
							color: #1998fc;
							cursor: pointer;
						}

						.out-color {
							color: #67757c;
							cursor: default;
						}

						.oi-label {
							font-weight: 400;
							font-size: 14px;
							line-height: 12px;
							color: $preto02;
						}
					}
				}
			}
		}
	}
}

.modalidade-detail {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.closed {
	.modalidade-turma {
		display: none;
	}
}

.title-modalidade-turma {
	text-wrap: nowrap;
	width: auto;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	display: flex;
	gap: 15px;
	overflow: hidden;
	color: #51555a;
	font-weight: 400;
	justify-content: flex-start;
	padding-right: 15px;

	span {
		text-wrap: nowrap;
		width: auto;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}

	.title-accordion {
		font-family: Nunito Sans;
		font-size: 14px;
		font-weight: 700;
		line-height: 20px;
		letter-spacing: 0px;
		text-align: left;
		text-transform: lowercase;
		color: $pretoPri;

		&::first-line {
			text-transform: capitalize;
		}
	}
}

.body-section {
	box-shadow: inset 0 0px 0px 0 !important;
}

.accordion-wrapper {
	.header-section {
		box-shadow: 0 0 0 !important;
		padding: 14px 16px !important;
	}
}

.contract-detail {
	.detail-data-container {
		border: 1px solid #e3e4e5;
		border-radius: 10px;
	}
}

i.pct-chevron-up {
	display: inline-block !important;
}

.modal-faltas-component-window,
.modal-aulas-desmarcadas-component-windo {
	.modal-dialog {
		max-width: unset;
		max-height: unset;
		width: 1000px;
		height: 800px;
	}
}

.modal-desmarcar-aula-contrato-wrapper {
	.modal-dialog {
		max-width: unset;
		max-height: unset;
		width: 1000px;
	}
}
