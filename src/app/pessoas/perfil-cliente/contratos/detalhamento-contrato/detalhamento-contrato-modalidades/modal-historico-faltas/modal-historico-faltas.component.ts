import {
	AfterViewInit,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { DialogService, PactoDataGridConfig, PactoModalSize } from "ui-kit";
import { ModalDesmarcarAulaContratoComponent } from "../modal-desmarcar-aula-contrato/modal-desmarcar-aula-contrato.component";

@Component({
	selector: "pacto-modal-historico-faltas",
	templateUrl: "./modal-historico-faltas.component.html",
	styleUrls: ["./modal-historico-faltas.component.scss"],
})
export class ModalHistoricoFaltasComponent implements OnInit, AfterViewInit {
	@ViewChild("actionsCell", { static: false })
	actionsCell: TemplateRef<any>;

	contrato;
	dadosPessoais;
	table: PactoDataGridConfig;
	listaFaltas = new Array<any>();
	@Output() response: EventEmitter<any> = new EventEmitter<any>();

	constructor(
		private sessionService: SessionService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private dialogService: DialogService
	) {}

	ngOnInit() {
		if (!this.contrato) {
			throw Error("Contrato não informado");
		}
	}

	ngAfterViewInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.listaFaltas,
				};
			},
			pagination: false,
			columns: [
				{
					nome: "titulo",
					titulo: "Turma",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataAulaApresentar",
					titulo: "Dia",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "horario",
					titulo: "Horário",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "desmarcar",
					titulo: "Ações",
					celula: this.actionsCell,
					visible: this.contrato.situacao === "AT",
				},
			],
		});
	}

	desmarcar(item: any) {
		this.telaClienteService
			.buscarHorarioTurmaByCodigo(this.sessionService.chave, +item.id)
			.subscribe((response) => {
				const dialogRef = this.dialogService.open(
					"Desmarcar aula",
					ModalDesmarcarAulaContratoComponent,
					PactoModalSize.LARGE,
					"modal-desmarcar-aula-contrato-wrapper"
				);
				dialogRef.componentInstance.aulaDesmarcada = item;
				dialogRef.componentInstance.horarioTurma = response.content;
				dialogRef.componentInstance.selecionarTurmaDesmarcarAula = true;
				dialogRef.componentInstance.codigoContrato = this.contrato.codigo;
				dialogRef.componentInstance.contrato = this.contrato;
				dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
				dialogRef.componentInstance.response.subscribe((res) => {
					this.response.emit(res);
				});
			});
	}
}
