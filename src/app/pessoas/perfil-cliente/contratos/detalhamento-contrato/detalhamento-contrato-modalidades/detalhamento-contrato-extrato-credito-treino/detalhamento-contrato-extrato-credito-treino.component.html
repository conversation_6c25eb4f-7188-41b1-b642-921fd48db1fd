<div class="detail-title" i18n="@@detail-contract:extrato-credito-treino">
	Extrato {{ nomenClaturaVendaCredito }}
</div>

<div class="detail-data-container">
	<pacto-relatorio
		#tableRef
		(iconClick)="iconClick($event)"
		(pageChangeEvent)="onPageChange($event)"
		(pageSizeChange)="onPageSizeChange($event)"
		(rowClick)="viewItem($event)"
		(sortEvent)="onSortChange($event)"
		[customActions]="botaoNovoExtrato"
		[table]="table"
		class="contract-table"
		idSuffix="tbl-cect">
		<div #filterContent class="filter-content" filterContent>
			<form [formGroup]="filterForm" id="dcec-filter-form">
				<div #operacaoFilter class="dcect-filter-section">
					<div
						(click)="expandFilter(operacaoFilter)"
						class="dcect-filter-title-section"
						id="dced-exp-filter-operacao">
						<div class="dcect-filter-indicator">
							<i class="pct pct-chevron-right cor-preto-pri"></i>
						</div>
						<div class="dcect-filter-title">Operação</div>
						<div
							(click)="clearOperacaoFilter(); $event.stopPropagation()"
							class="dcect-filter-clear"
							id="dced-exp-limpar-filter-operacao">
							Limpar
						</div>
					</div>
					<div class="dcect-filter-form-controls">
						<pacto-cat-checkbox
							[control]="filterForm.get('aulasMarcadasCreditoExtra')"
							i18n-label="
								@@controle-extrato-credito:aulas-marcadas-credito-extra"
							id="dced-chckbox-aulas-marcadas-credito-extra"
							label="Aulas marcadas com crédito extra"></pacto-cat-checkbox>

						<pacto-cat-form-select
							[control]="filterForm.get('tipoOperacaoCreditoTreino')"
							[items]="optionsTipoOperacaoCreditoTreino"
							id="dced-select-tipo-operacao-credito-treino"
							idKey="codigo"
							labelKey="nome"></pacto-cat-form-select>
					</div>
				</div>

				<div #dataOperacaoFilter class="dcect-filter-section">
					<div
						(click)="expandFilter(dataOperacaoFilter)"
						class="dcect-filter-title-section"
						id="dced-exp-filter-data-operacao">
						<div class="dcect-filter-indicator">
							<i class="pct pct-chevron-right cor-preto-pri"></i>
						</div>
						<div class="dcect-filter-title">Data da operação</div>
						<div
							(click)="clearDataOperacaoFilter(); $event.stopPropagation()"
							class="dcect-filter-clear"
							id="dced-exp-limpar-data-operacao">
							Limpar
						</div>
					</div>
					<div class="dcect-filter-form-controls">
						<pacto-cat-form-datepicker
							[control]="filterForm.get('dataInicioOperacao')"
							id="dced-datepck-data-inicio-operacao"></pacto-cat-form-datepicker>
						<pacto-cat-form-datepicker
							[control]="filterForm.get('dataFimOperacao')"
							id="dced-datepck-data-fim-operacao"
							label="Até"></pacto-cat-form-datepicker>
					</div>
				</div>

				<div class="dcect-btn-row">
					<pacto-cat-button
						(click)="cleanAllFilters()"
						[disabled]="!this.filterForm.dirty"
						i18n-label="@@controle-extrato-credito:limpar-filtros"
						id="dcect-btn-limpar-filtros"
						label="Limpar filtros"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>
					<pacto-cat-button
						(click)="search()"
						i18n-label="@@controle-extrato-credito:consultar"
						id="dcect-btn-consulta"
						label="Consultar"
						size="LARGE"></pacto-cat-button>
				</div>
			</form>
		</div>
	</pacto-relatorio>
</div>

<ng-template #aulaDesmarcadaCell let-item="item">
	<ng-container
		*ngIf="
			item.tipoOperacaoCreditoTreinoEnum !==
			tipoOperacaoCreditoTreinoEnum.MARCOU_AULA
		">
		<ng-container *ngIf="item.reposicao?.codigo !== 0">
			<span
				*ngIf="item.reposicao?.marcacaoAula; else naoMarcaoAula"
				i18n="@@controle-extrato-credito:marcacao-aula-extra">
				Marcação de Aula Extra
			</span>
			<ng-template #naoMarcaoAula>
				{{ item.reposicao?.turmaOrigem?.modalidade?.nome }} -
				{{ item.reposicao?.dataOrigem | date : "dd/MM/yyyy" }} -
				{{ diaSemanaNome(item.reposicao?.horarioTurmaOrigem?.diaSemana) }} -
				{{ item.reposicao?.horarioTurmaOrigem?.horaInicial }} às
				{{ item.reposicao?.horarioTurmaOrigem?.horaFinal }}
			</ng-template>
		</ng-container>
		<ng-container
			*ngIf="item.reposicao?.codigo === 0 && item.aulaDesmarcada?.codigo !== 0">
			{{ item.aulaDesmarcada?.turma?.modalidade?.nome }} -
			{{ item.aulaDesmarcada?.dataOrigem | date : "dd/MM/yyyy" }} -
			{{ diaSemanaNome(item.reposicao?.horarioTurma?.diaSemana) }} -
			{{ item.aulaDesmarcada?.horarioTurma?.horaInicial }} às
			{{ item.aulaDesmarcada?.horarioTurma?.horaFinal }}
		</ng-container>
		<ng-container
			*ngIf="item.reposicao?.codigo === 0 && item.aulaDesmarcada?.codigo === 0">
			-
		</ng-container>
	</ng-container>
</ng-template>

<ng-template #aulaMarcadaCell let-item="item">
	<span *ngIf="item.reposicao?.codigo !== 0">
		{{ item.reposicao?.turmaOrigem?.modalidade?.nome }} -
		{{ item.reposicao?.dataReposicao | date : "dd/MM/yyyy" }} -
		{{ diaSemanaNome(item.reposicao?.horarioTurma?.diaSemana) }} -
		{{ item.reposicao?.horarioTurma?.horaInicial }} às
		{{ item.reposicao?.horarioTurma?.horaFinal }}
	</span>
	<span *ngIf="item.descricaoAulaMarcada && item.descricaoAulaMarcada !== ''">
		{{ item.descricaoAulaMarcada }}
	</span>
	<span
		*ngIf="
			item.reposicao?.codigo === 0 &&
			item.descricaoAulaMarcada &&
			item.descricaoAulaMarcada !== ''
		">
		-
	</span>
</ng-template>

<ng-template #botaoNovoExtrato>
	<pacto-cat-button
		(click)="addNew()"
		[icon]="'pct pct-plus'"
		[label]="'Novo'"
		class="btnAddExtratoCredito"
		size="LARGE"
		type="PRIMARY_ADD"></pacto-cat-button>
</ng-template>
