import {
	AfterViewInit,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	ConfiguracaoSistema,
} from "adm-legado-api";
import { PerfilClienteService } from "../../../../perfil-cliente.service";
import {
	AutorizacaoAcessoComponent,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
} from "ui-kit";
import {
	AdmCoreControleCreditoTreinoService,
	ApiResponseList,
	ControleCreditoTreino,
	TipoOperacaoCreditoTreinoEnum,
} from "adm-core-api";
import { DatePipe } from "@angular/common";
import { FormControl, FormGroup } from "@angular/forms";
import { CadastrarControleCreditoTreinoComponent } from "./cadastrar-controle-credito-treino/cadastrar-controle-credito-treino.component";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { MatDialog } from "@angular/material";

@Component({
	selector: "pacto-detalhamento-contrato-extrato-credito-treino",
	templateUrl: "./detalhamento-contrato-extrato-credito-treino.component.html",
	styleUrls: [
		"./detalhamento-contrato-extrato-credito-treino.component.scss",
		"../../../contract-common.scss",
	],
	encapsulation: ViewEncapsulation.None,
})
export class DetalhamentoContratoExtratoCreditoTreinoComponent
	implements OnInit, AfterViewInit
{
	@Input() contrato;
	@Input() dadosPessoais;
	@ViewChild("tableRef", { static: true })
	private tableRef: RelatorioComponent;
	@ViewChild("aulaDesmarcadaCell", { static: true })
	private aulaDesmarcadaCell: TemplateRef<any>;
	@ViewChild("aulaMarcadaCell", { static: true })
	private aulaMarcadaCell: TemplateRef<any>;

	nomenClaturaVendaCredito: string = "Crédito Treino";
	configuracoesSistema: ConfiguracaoSistema;
	actualPage = 0;
	actualSize = 10;
	table: PactoDataGridConfig;
	tipoOperacaoCreditoTreinoEnum = TipoOperacaoCreditoTreinoEnum;
	filterForm: FormGroup = new FormGroup({
		aulasMarcadasCreditoExtra: new FormControl(),
		tipoOperacaoCreditoTreino: new FormControl(),
		dataInicioOperacao: new FormControl(),
		dataFimOperacao: new FormControl(),
	});
	private sort = {
		sortDirection: "DESC",
		sortField: "dataOperacao",
	};
	filters: any = {};
	optionsTipoOperacaoCreditoTreino =
		TipoOperacaoCreditoTreinoEnum.asArray().sort((a, b) => {
			if (a.nome > b.nome) {
				return 1;
			}
			if (a.nome < b.nome) {
				return -1;
			}
			return 0;
		});

	constructor(
		private perfilClienteService: PerfilClienteService,
		private datePipe: DatePipe,
		private rest: RestService,
		private dialogService: DialogService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private matDialog: MatDialog
	) {}

	ngOnInit() {
		this.configuracoesSistema = this.perfilClienteService.configuracoesSistema;
		this.filterForm
			.get("aulasMarcadasCreditoExtra")
			.valueChanges.subscribe((v) => {
				if (v) {
					this.filterForm.get("tipoOperacaoCreditoTreino").reset("");
					this.filterForm.get("tipoOperacaoCreditoTreino").disable();
				} else {
					this.filterForm.get("tipoOperacaoCreditoTreino").enable();
				}
			});
		this.initTable();
	}

	ngAfterViewInit() {
		this.list();
	}

	list() {
		if (this.tableRef) {
			const formValues = this.filterForm.getRawValue();
			this.tableRef.addFilter(
				"aulasMarcadasCreditoExtra",
				formValues.aulasMarcadasCreditoExtra
			);
			this.tableRef.addFilter(
				"tipoOperacaoCreditoTreino",
				formValues.tipoOperacaoCreditoTreino
			);
			this.tableRef.addFilter(
				"dataInicioOperacao",
				formValues.dataInicioOperacao
			);
			this.tableRef.addFilter("dataFimOperacao", formValues.dataFimOperacao);
			this.tableRef.reloadData();
		}
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`controle-credito-treino/${this.contrato.codigo}`
			),
			showFilters: true,
			columns: [
				{
					nome: "dataOperacao",
					titulo: "Data",
					visible: true,
					valueTransform: (v) => {
						if (v) {
							return this.datePipe.transform(v, "dd/MM/yyyy HH:mm:ss");
						}
						return v;
					},
				},
				{
					nome: "tipoOperacaoCreditoTreino",
					titulo: "Operação",
					visible: true,
					valueTransform: (v) => this.tipoOperacaoCredito(v),
				},
				{
					nome: "usuario",
					titulo: "Usuário",
					visible: true,
					valueTransform: (v) => v.nome,
				},
				{
					nome: "observacao",
					titulo: "Observação",
					visible: true,
					valueTransform: (v) => v || "-",
				},
				{
					nome: "aulaDesmarcada",
					titulo: "Aula desmarcada",
					celula: this.aulaDesmarcadaCell,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "aulaMarcada",
					celula: this.aulaMarcadaCell,
					visible: true,
					titulo: "Aula marcada",
					ordenavel: false,
				},
				{
					nome: "quantidade",
					titulo: "Qtd.",
					visible: true,
				},
				{
					nome: "saldo",
					titulo: "Saldo",
					visible: true,
				},
			],
			actions: [
				{
					nome: "view-item",
					iconClass: "pct pct-search cor-azulim-pri",
				},
			],
		});
	}

	onPageSizeChange(event: any) {
		this.actualSize = event;
		this.list();
	}

	onPageChange(event: any) {
		this.actualPage = event - 1;
		this.list();
		const table = document.getElementById("tbl-title-block-tbl-cect");
		if (table) {
			table.scrollIntoView();
		}
	}

	private getNomenclaturaCreditoTreino() {
		switch (this.configuracoesSistema.nomenclaturaVendaCredito) {
			case "CT":
				return "Crédito Treino";
			case "CH":
				return "Crédito Hora/Aula";
			default:
				return "Crédito Treino";
		}
	}

	diaSemanaNome(diaSemana: string): string {
		if (!diaSemana) {
			diaSemana = "";
		}
		if (diaSemana === "DM") {
			return "Domingo";
		}
		if (diaSemana === "SG") {
			return "Segunda";
		}
		if (diaSemana === "TR") {
			return "Terça";
		}
		if (diaSemana === "QA") {
			return "Quarta";
		}
		if (diaSemana === "QI") {
			return "Quinta";
		}
		if (diaSemana === "SX") {
			return "Sexta";
		}
		if (diaSemana === "SB") {
			return "Sábado";
		}
		return "";
	}

	clearOperacaoFilter() {
		this.filterForm.get("aulasMarcadasCreditoExtra").reset(false);
		this.filterForm.get("tipoOperacaoCreditoTreino").reset("");
	}

	clearDataOperacaoFilter() {
		this.filterForm.get("dataInicioOperacao").reset("");
		this.filterForm.get("dataFimOperacao").reset("");
	}

	cleanAllFilters() {
		if (this.filterForm.dirty) {
			this.filters = {};
			this.filterForm.reset({});
			this.tableRef.filterDropdown.close();
			this.list();
		}
	}

	expandFilter(dataOperacaoFilter: HTMLDivElement) {
		if (dataOperacaoFilter) {
			const indicator = dataOperacaoFilter.querySelector(
				".dcect-filter-indicator"
			);
			if (indicator) {
				if (indicator.classList.contains("dcect-expanded")) {
					indicator.classList.remove("dcect-expanded");
				} else {
					indicator.classList.add("dcect-expanded");
				}
			}
			const controls = dataOperacaoFilter.querySelector(
				".dcect-filter-form-controls"
			);
			if (controls) {
				if (controls.classList.contains("dcect-expanded")) {
					controls.classList.remove("dcect-expanded");
				} else {
					controls.classList.add("dcect-expanded");
				}
			}
		}
	}

	search() {
		this.tableRef.filterDropdown.close();
		this.list();
	}

	addNew(data?) {
		let dialogRef = this.dialogService.open(
			"Controle crédito de treino",
			CadastrarControleCreditoTreinoComponent,
			PactoModalSize.LARGE,
			"mdl-ccct-new"
		);
		dialogRef.componentInstance.contrato = this.contrato;
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		if (data) {
			dialogRef.componentInstance.data = data;
		}
		dialogRef.result
			.then((result1) => {
				const modalConfirmacao: any = this.matDialog.open(
					DialogAutorizacaoAcessoComponent,
					{
						disableClose: true,
						id: "autorizacao-acesso",
						autoFocus: false,
					}
				);
				modalConfirmacao.componentInstance.form
					.get("usuario")
					.setValue(this.sessionService.loggedUser.username);

				modalConfirmacao.componentInstance.confirm.subscribe((result2) => {
					this.autorizarAcessoService
						.validarPermissao(
							this.sessionService.chave,
							result2.data.usuario,
							result2.data.senha,
							"NOVOAJUSTEMANUALCREDITOTREINO",
							"3.35 - Lançar Ajuste Manual Crédito de Treino",
							this.sessionService.empresaId
						)
						.subscribe(
							(response: any) => {
								// result2.modal.close();
								const data = result1.data;

								if (data.codigoTipoAjusteManualCreditoTreino == 2) {
									if (data.quantidade > 0) {
										data.quantidade = data.quantidade * -1;
									}
								} else if (data.codigoTipoAjusteManualCreditoTreino == 1) {
									if (data.quantidade < 0) {
										data.quantidade = data.quantidade * -1;
									}
								}
								this.telaClienteService
									.incluirControleCreditoTreino(this.sessionService.chave, data)
									.subscribe(
										(response) => {
											this.notificationService.success(response.content);
											this.list();
										},
										(httpResponseError) => {
											const error = httpResponseError.error;
											console.log(error);
											if (error && error.meta) {
												if (error.meta.error) {
													this.notificationService.error(error.meta.message);
												}
											}
										}
									);
							},
							(error) => {
								console.log(error);
								this.notificationService.error(error.error.meta.message);
							}
						);
				});

				modalConfirmacao.componentInstance.cancel.subscribe((result2) => {
					this.addNew(result1.data);
				});
			})
			.catch((e) => {});
	}

	iconClick(event: { row: any; iconName: string }) {
		if (event.iconName === "view-item") {
			this.viewItem(event.row);
		}
	}

	viewItem(itemRow) {
		const dialogRef = this.dialogService.open(
			"Controle crédito de treino",
			CadastrarControleCreditoTreinoComponent,
			PactoModalSize.LARGE,
			"mdl-ccct-view"
		);
		dialogRef.componentInstance.controleCreditoTreino = itemRow;
	}

	onSortChange(sort) {
		this.sort = {
			sortDirection: sort.direction,
			sortField: sort.columnName,
		};
		this.list();
	}

	tipoOperacaoCredito(codigo) {
		return TipoOperacaoCreditoTreinoEnum.buscarPorCodigo(codigo).descricao;
	}
}
