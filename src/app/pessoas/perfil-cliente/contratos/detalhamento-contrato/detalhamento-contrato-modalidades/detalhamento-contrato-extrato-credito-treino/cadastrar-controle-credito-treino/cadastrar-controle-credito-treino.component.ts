import {
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmCoreControleCreditoTreinoService,
	TipoOperacaoCreditoTreinoEnum,
} from "adm-core-api";
import { FormControl, FormGroup } from "@angular/forms";
import { DatePipe } from "@angular/common";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService, Cliente } from "adm-legado-api";

@Component({
	selector: "pacto-cadastrar-controle-credito-treino",
	templateUrl: "./cadastrar-controle-credito-treino.component.html",
	styleUrls: ["./cadastrar-controle-credito-treino.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CadastrarControleCreditoTreinoComponent implements OnInit {
	contrato;
	dadosPessoais;
	controleCreditoTreino: any;
	tipoOperacaoCreditoTreinoEnum = TipoOperacaoCreditoTreinoEnum;
	form: FormGroup;
	origemSistema: string = "-";
	aulaDesmarcada: string = "-";
	aulaMarcada: string = "-";
	data: any;
	clientes: Array<Cliente> = new Array<Cliente>();

	constructor(
		private dialog: NgbActiveModal,
		private controleCreditoTreinoService: AdmCoreControleCreditoTreinoService,
		private notificationService: SnotifyService,
		private datePipe: DatePipe,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.clientes = [];

		this.createForm();
		if (this.controleCreditoTreino) {
			if (this.controleCreditoTreino.codigo) {
				if (
					this.controleCreditoTreino.reposicao &&
					this.controleCreditoTreino.reposicao.codigo
				) {
					if (this.controleCreditoTreino.reposicao.origemSistemaEnum) {
						this.origemSistema =
							this.controleCreditoTreino.reposicao.origemSistemaEnum
								.descricao || "-";
					}
					if (this.controleCreditoTreino.reposicao.marcacaoAula) {
						this.aulaDesmarcada = "Marcação de aula extra";
					} else {
						this.aulaDesmarcada = `${this.controleCreditoTreino.reposicao.turmaOrigem.modalidade.nome} - `;
						this.aulaDesmarcada += `${this.datePipe.transform(
							this.controleCreditoTreino.reposicao.dataOrigem,
							"dd/MM/yyyy - HH:mm:ss"
						)} -`;
						this.aulaDesmarcada += `${this.diaSemanaNome(
							this.controleCreditoTreino.reposicao.horarioTurma.diaSemana
						)} - `;
						this.aulaDesmarcada += `${this.controleCreditoTreino.reposicao.horarioTurma.horaInicial} - `;
						this.aulaDesmarcada += `${this.controleCreditoTreino.reposicao.horarioTurma.horaFinal}`;
					}
					this.aulaMarcada = `${this.controleCreditoTreino.reposicao.turmaDestino.modalidade.nome} - `;
					this.aulaMarcada += `${this.datePipe.transform(
						this.controleCreditoTreino.reposicao.dataOrigem,
						"dd/MM/yyyy - HH:mm:ss"
					)} -`;
					this.aulaMarcada += `${this.diaSemanaNome(
						this.controleCreditoTreino.reposicao.horarioTurma.diaSemana
					)} - `;
					this.aulaMarcada += `${this.controleCreditoTreino.reposicao.horarioTurma.horaInicial} - `;
					this.aulaMarcada += `${this.controleCreditoTreino.reposicao.horarioTurma.horaFinal}`;
				} else if (
					this.controleCreditoTreino.aulaDesmarcada &&
					this.controleCreditoTreino.aulaDesmarcada.codigo
				) {
					if (this.controleCreditoTreino.aulaDesmarcada.origemSistemaEnum) {
						this.origemSistema =
							this.controleCreditoTreino.aulaDesmarcada.origemSistemaEnum
								.descricao || "-";
					}
					this.aulaDesmarcada = `${this.controleCreditoTreino.aulaDesmarcada.turma.modalidade.nome} - `;
					this.aulaDesmarcada += `${this.datePipe.transform(
						this.controleCreditoTreino.aulaDesmarcada.dataOrigem,
						"dd/MM/yyyy - HH:mm:ss"
					)} -`;
					this.aulaDesmarcada += `${this.diaSemanaNome(
						this.controleCreditoTreino.aulaDesmarcada.horarioTurma.diaSemana
					)} - `;
					this.aulaDesmarcada += `${this.controleCreditoTreino.aulaDesmarcada.horarioTurma.horaInicial} - `;
					this.aulaDesmarcada += `${this.controleCreditoTreino.aulaDesmarcada.horarioTurma.horaFinal}`;
				}
			}
		} else {
			this.controleCreditoTreino = {
				tipoOperacaoCreditoTreinoEnum:
					TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL,
			};
		}
	}

	listClients(term?) {
		if (term || this.clientes.length === 0) {
			this.telaClienteService
				.consultarAlunosTransferenciaDeCredito(
					this.sessionService.chave,
					this.sessionService.empresaId,
					term
				)
				.subscribe((response) => {
					this.clientes = response.content;
					this.cd.detectChanges();
				});
		}
	}

	mostrarTransferenciaDeCredito() {
		return this.contrato && this.contrato.plano
			? this.contrato.plano.permitirTransferenciaDeCredito
			: false;
	}

	onTipoOperacaoChange(event: any) {
		const selectedValue = event.value;
		if (selectedValue === "3" && this.clientes.length === 0) {
			this.listClients();
		}
	}

	createForm() {
		this.form = new FormGroup({
			tipoOperacaoCreditoTreino: new FormControl(),
			codigoTipoAjusteManualCreditoTreino: new FormControl(),
			dataLancamento: new FormControl(),
			dataOperacao: new FormControl(),
			origemSistema: new FormControl(),
			usuario: new FormControl(),
			aulaDesmarcada: new FormControl(),
			aulaMarcada: new FormControl(),
			quantidade: new FormControl(),
			observacao: new FormControl(),
			clienteRecebendoTransferencia: new FormControl(),
		});

		if (this.controleCreditoTreino) {
			if (this.controleCreditoTreino.codigo) {
				this.form.get("codigoTipoAjusteManualCreditoTreino").disable();
				this.form
					.get("codigoTipoAjusteManualCreditoTreino")
					.setValue(
						this.controleCreditoTreino.codigoTipoAjusteManualCreditoTreino.toString()
					);
				this.form.get("quantidade").disable();
				this.form
					.get("quantidade")
					.setValue(this.controleCreditoTreino.quantidade);
				this.form.get("observacao").disable();
				this.form
					.get("observacao")
					.setValue(this.controleCreditoTreino.observacao);
			}
		}

		if (this.data) {
			this.form.patchValue({
				codigoTipoAjusteManualCreditoTreino: this.data
					.codigoTipoAjusteManualCreditoTreino
					? this.data.codigoTipoAjusteManualCreditoTreino.toString()
					: "",
				quantidade: this.data.quantidade,
				observacao: this.data.observacao,
			});
		}
	}

	save() {
		const data: any = {
			tipoOperacaoCreditoTreino:
				TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL.codigo,
			codigoTipoAjusteManualCreditoTreino: +this.form.get(
				"codigoTipoAjusteManualCreditoTreino"
			).value,
			quantidade: +this.form.get("quantidade").value,
			observacao: this.form.get("observacao").value,
			contrato: {
				codigo: this.contrato.codigo,
			},
			usuario: { codigo: this.sessionService.loggedUser.usuarioZw },
			codCliente: this.dadosPessoais.codigoCliente,
			clienteRecebendoTransferencia: this.form.get(
				"clienteRecebendoTransferencia"
			).value,
		};
		if (!data.codigoTipoAjusteManualCreditoTreino) {
			this.notificationService.error(
				"O campo Tipo Operação deve ser informado!"
			);
			return;
		}

		if (!data.quantidade) {
			this.notificationService.error("O campo quantidade deve ser informado!");
			return;
		}

		this.dialog.close({ reloadData: true, data });
	}

	diaSemanaNome(diaSemana: string): string {
		if (!diaSemana) {
			diaSemana = "";
		}
		if (diaSemana === "DM") {
			return "Domingo";
		}
		if (diaSemana === "SG") {
			return "Segunda";
		}
		if (diaSemana === "TR") {
			return "Terça";
		}
		if (diaSemana === "QA") {
			return "Quarta";
		}
		if (diaSemana === "QI") {
			return "Quinta";
		}
		if (diaSemana === "SX") {
			return "Sexta";
		}
		if (diaSemana === "SB") {
			return "Sábado";
		}
		return "";
	}
}
