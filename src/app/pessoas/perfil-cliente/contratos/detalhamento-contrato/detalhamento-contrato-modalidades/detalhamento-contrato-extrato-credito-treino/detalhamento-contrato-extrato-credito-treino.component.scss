@import "~src/assets/scss/pacto/plataforma-import.scss";

.filter-content {
	width: 289px;

	form {
		.dcect-filter-section {
			padding: 16px;
			border-bottom: 1px solid #c9cbcf;

			.dcect-filter-title-section {
				display: flex;
				cursor: pointer;
				color: $pretoPri;
				align-items: center;

				.dcect-filter-indicator {
					margin-right: 4px;
					font-size: 16px;
					width: 16px;
					height: 16px;
					display: flex;
					align-content: center;
					justify-content: center;
					transition: {
						property: transform;
						duration: 0.2s;
					}

					&.dcect-expanded {
						transform: rotate(90deg);
					}
				}

				.dcect-filter-title {
					font-size: 12px;
					line-height: 15px;
					font-weight: 700;
				}

				.dcect-filter-clear {
					font-weight: 600;
					line-height: 12px;
					color: $azulimPri;
				}

				.dcect-filter-clear {
					margin-left: auto;
					cursor: pointer;
				}
			}

			.dcect-filter-form-controls {
				display: none;
				transition: height 0.2s ease;

				&.dcect-expanded {
					display: block;
				}

				pacto-cat-checkbox {
					margin-top: 16px;
					display: block;
				}

				pacto-cat-form-select {
					margin: 16px 0;

					.pct-error-msg {
						display: none;
					}

					.pacto-label {
						display: none;
					}
				}

				pacto-cat-form-datepicker {
					margin: 16px 0;

					.nome {
						display: none;
					}

					.pct-error-msg {
						display: none;
					}
				}

				:last-child {
					margin-bottom: 0;
				}
			}
		}

		.dcect-btn-row {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 16px 8px;
			background-color: $mobile;

			#dcect-btn-limpar-filtros {
				width: 137px;
				margin-right: 7px;
			}

			#dcect-btn-consulta {
				width: 113px;
			}
		}
	}
}

.mdl-ccct-view {
	&.modal {
		.modal-dialog {
			width: 1000px;
			max-width: unset;
		}
	}
}

.btnAddExtratoCredito {
	margin-left: 10px;

	button {
		height: 42px !important;
	}

	&.pacto-button {
		height: 42px;
	}
}
