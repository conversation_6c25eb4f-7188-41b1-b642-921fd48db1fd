import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "pacto-detalhamento-contrato-historico-compras",
	templateUrl: "./detalhamento-contrato-historico-compras.component.html",
	styleUrls: [
		"./detalhamento-contrato-historico-compras.component.scss",
		"../../contract-common.scss",
	],
	encapsulation: ViewEncapsulation.None,
})
export class DetalhamentoContratoHistoricoComprasComponent implements OnInit {
	@Input()
	public codigoContrato: number;
	public table: PactoDataGridConfig;

	@ViewChild("cellSituacao", { static: true })
	cellSituacao: TemplateRef<any>;
	itensPerPage = [
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
		{ id: 50, label: "50" },
		{ id: 100, label: "100" },
		{ id: 250, label: "250" },
	];

	constructor(private readonly rest: RestService) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`contratos/${this.codigoContrato}/produtos`
			),
			columns: [
				{
					nome: "codigo",
					titulo: "Codigo",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "contrato",
					titulo: "Contrato",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => (v ? v.codigo : "-"),
				},
				{
					nome: "empresa",
					titulo: "Empresa",
					visible: true,
					ordenavel: false,
					valueTransform(data) {
						return data.nome;
					},
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataLancamento",
					titulo: "Lançamento",
					visible: true,
					ordenavel: false,
					valueTransform(data) {
						return new Date(data).toLocaleDateString("pt-BR");
					},
				},
				{
					nome: "quantidade",
					titulo: "Quant.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "precoUnitario",
					titulo: "Valor unit.",
					visible: true,
					ordenavel: false,
					valueTransform(data = 0) {
						return data.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
				{
					nome: "valorDesconto",
					titulo: "Desconto",
					visible: true,
					ordenavel: false,
					valueTransform(data = 0) {
						return data.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
				{
					nome: "totalFinal",
					titulo: "Total",
					visible: true,
					ordenavel: false,
					valueTransform(data = 0) {
						return data.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: false,
					celula: this.cellSituacao,
				},
			],
		});
	}

	onChangePage(event: any) {
		const table = document.getElementById(
			"tbl-title-block-dchc-tbl-hist-compra"
		);
		if (table) {
			table.scrollIntoView();
		}
	}
}
