<pacto-relatorio
	id="tbl-hist-compras-contrato"
	(pageChangeEvent)="onChangePage($event)"
	[enableZebraStyle]="true"
	[itensPerPage]="itensPerPage"
	[showShare]="false"
	[table]="table"
	actionTitle="Recibo"
	class="contract-table"
	i18n-tableTitle="@@contract-detail:historico-compra"
	idSuffix="dchc-tbl-hist-compra"
	labelTotalElementsPosition="left"
	tableTitle="Histórico de compras"></pacto-relatorio>

<ng-template #cellSituacao let-item="item">
	<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
		<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
		<ng-container *ngIf="item.situacao === 'EA'">Em aberto</ng-container>
		<ng-container *ngIf="item.situacao === 'PG'">Pago</ng-container>
	</div>
</ng-template>
