import {
	AfterViewInit,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { AdmLegadoTelaClienteService, ApiResponseList } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { DatePipe, DecimalPipe } from "@angular/common";

@Component({
	selector: "pacto-historico-cheque",
	templateUrl: "./historico-cheque.component.html",
	styleUrls: [
		"./historico-cheque.component.scss",
		"../../contract-common.scss",
	],
})
export class HistoricoChequeComponent implements OnInit, AfterViewInit {
	@Input() contrato;
	@ViewChild("tableRef", { static: false })
	tableRef: RelatorioComponent;

	table: PactoDataGridConfig;
	cheques: ApiResponseList<any> = {} as ApiResponseList<any>;
	actualPage = 0;
	actualSize = 10;

	situacao = {
		CA: "Cancelado",
		DV: "Devolvido",
	};

	constructor(
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private datePipe: DatePipe,
		private decimalPipe: DecimalPipe
	) {}

	ngOnInit() {
		this.initTable();
	}

	ngAfterViewInit() {
		this.list();
	}

	list() {
		this.telaClienteService
			.obterCheques(this.sessionService.chave, this.contrato.codigo, {
				page: this.actualPage,
				size: this.actualSize,
			})
			.subscribe((response: ApiResponseList<any>) => {
				this.cheques = response;
				this.cheques.content.forEach((cheque) => {
					cheque.situacao = this.situacaoCheque(cheque);
				});
				if (this.tableRef) {
					this.tableRef.reloadData();
				}
			});
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => this.cheques,
			state: {
				paginaNumero: this.actualPage,
				paginaTamanho: this.actualSize,
				ordenacaoColuna: undefined,
				ordenacaoDirecao: undefined,
			},
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "banco",
					titulo: "Banco",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => v.nome,
				},
				{
					nome: "agencia",
					titulo: "Agência",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "conta",
					titulo: "Conta",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "numero",
					titulo: "Número cheque",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeNoCheque",
					titulo: "Nome no cheque",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataCompensacao",
					titulo: "Data compensação",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyy"),
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
				},
			],
		});
	}

	onPageSizeChange(event: any) {
		this.actualSize = event;
		this.list();
	}

	onPageChange(event: any) {
		this.actualPage = event - 1;
		this.list();
	}

	situacaoCheque(cheque) {
		const situacaoFromObj = this.situacao[cheque.situacao];
		if (situacaoFromObj) {
			return situacaoFromObj;
		}
		if (cheque.dataCompensacao <= new Date().getTime()) {
			return "Compensado";
		}
		if (cheque.dataCompensacao > new Date().getTime()) {
			return "Em aberto";
		}
	}

	onChangePage(event: any) {
		const table = document.getElementById(
			"tbl-title-block-dchc-tbl-hist-cheque"
		);
		if (table) {
			table.scrollIntoView();
		}
	}
}
