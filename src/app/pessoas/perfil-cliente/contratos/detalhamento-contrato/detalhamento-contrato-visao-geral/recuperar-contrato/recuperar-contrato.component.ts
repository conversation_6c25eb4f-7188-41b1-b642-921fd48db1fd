import { Component, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-recuperar-contrato",
	templateUrl: "./recuperar-contrato.component.html",
	styleUrls: ["./recuperar-contrato.component.scss"],
})
export class RecuperarContratoComponent implements OnInit {
	codContrato: number;
	codCliente: number;
	clienteNome: string;
	contractControl = new FormControl();
	clienteNomeControl = new FormControl();

	constructor(
		private dialog: NgbActiveModal,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private telaClienteService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {
		this.contractControl.setValue(this.codContrato);
		this.clienteNomeControl.setValue(this.clienteNome);
		this.contractControl.disable();
		this.clienteNomeControl.disable();
	}

	cancel() {
		this.dialog.close();
	}

	recoverRightUse() {
		this.telaClienteService
			.transferirDireitoContratoRecuperar(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				this.codCliente,
				this.codContrato
			)
			.subscribe(
				(response) => {
					this.notificationService.success(response.content);
					this.dialog.close(true);
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message);
					}
				}
			);
	}
}
