import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "../../../../../../microservices/client-discovery/client-discovery.service";

@Component({
	selector: "pacto-retorno-atestado",
	templateUrl: "./retorno-atestado.component.html",
	styleUrls: ["./retorno-atestado.component.scss"],
})
export class RetornoAtestadoComponent implements OnInit {
	@Input() codContrato;
	@Input() codCliente;
	@Input() matriculaCliente;
	url!: string;

	constructor(
		private dialog: NgbActiveModal,
		private clientDiscoveryService: ClientDiscoveryService,
		private session: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.clientDiscoveryService
			.linkZw(this.session.usuarioOamd, this.session.empresaId)
			.subscribe((result) => {
				this.url = `${result}&codCliente=${this.codCliente}&codContrato=${this.codContrato}&operacaoClienteEnumName=RETORNO_ATESTADO&isContratoOperacao=true`;
				this.cd.detectChanges();
			});
	}

	messageReceived(messageEvent: { event: MessageEvent; dataParsed: any }) {
		if (messageEvent && messageEvent.dataParsed) {
			if (messageEvent.dataParsed) {
				if (messageEvent.dataParsed.close) {
					this.dialog.close();
				} else if (messageEvent.dataParsed.reloadContractPage) {
					this.dialog.close({ reloadContrato: true });
				}
			}
		}
	}
}
