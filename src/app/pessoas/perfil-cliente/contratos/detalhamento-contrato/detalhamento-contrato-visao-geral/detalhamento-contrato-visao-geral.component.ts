import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostBinding,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import {
	AdmCoreApiColaboradorService,
	ClienteDadosPessoais,
	OrigemSistema,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	ConfiguracaoSistema,
	Empresa,
	OpcoesContrato,
	TipoContrato,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import {
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoModalSize,
} from "ui-kit";
import { PerfilClienteService } from "../../../perfil-cliente.service";
import { ModalEditarDadosContratoComponent } from "./modal-editar-dados-contrato/modal-editar-dados-contrato.component";
import { RecuperarContratoComponent } from "./recuperar-contrato/recuperar-contrato.component";
import { TransferirContratoComponent } from "./transferir-contrato/transferir-contrato.component";
import { ModalDetalhamentoVisaoGeralComponent } from "../../../perfil-cliente-shared/modal-detalhamento-visao-geral/modal-detalhamento-visao-geral.component";
import { ClientDiscoveryService } from "sdk";
import { ModalAlterarPlanoContratoComponent } from "../../modal-alterar-plano-contrato/modal-alterar-plano-contrato.component";
import { ModalAlterarVencimentoParcelasContratoComponent } from "../../modal-alterar-vencimento-parcelas-contrato/modal-alterar-vencimento-parcelas-contrato.component";
import { ModalService } from "@base-core/modal/modal.service";
import { MatDialog } from "@angular/material";
import { FormControl, FormGroup } from "@angular/forms";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";

@Component({
	selector: "pacto-detalhamento-contrato-visao-geral",
	templateUrl: "./detalhamento-contrato-visao-geral.component.html",
	styleUrls: [
		"./detalhamento-contrato-visao-geral.component.scss",
		"../../contract-common.scss",
	],
})
export class DetalhamentoContratoVisaoGeralComponent implements OnInit {
	@Input() contract;
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() opcoesContrato: OpcoesContrato;

	@Output()
	reloadContract: EventEmitter<any> = new EventEmitter<any>();

	@Output()
	goBackClient: EventEmitter<any> = new EventEmitter<any>();

	@HostBinding("class.pacto-detalhamento-contrato-visao-geral")
	encapsulate = true;
	any;
	textoOrigemContrato = "ZillyonWeb";
	configuracoesSistema: ConfiguracaoSistema;
	empresa: Empresa;
	saldoCredito: number = 0;
	marcacoesFuturas: number = 0;
	qtdGridColumn = 3;
	gridClassesCreditoSection;
	nomenclaturaVendaCredito: string = "Crédito Treino";

	isEditableContract = false;
	isEditableRenovacao = false;
	isEditableConsultorResponsavel = false;
	isAutorizarEstornoContrato = false;
	hasRenovacaoAutomatica = false;

	apresentarCampoAlterarConsultor!: boolean;
	form: FormGroup;

	tipoContratos = new Array<{ id: number; label: string }>(
		{ id: TipoContrato.AGENDADO, label: "Agendado" },
		{ id: TipoContrato.ESPONTANEO, label: "Espontâneo" }
	);
	renovacaoAutomaticaOptions = new Array<any>(
		{ value: true, label: "Sim" },
		{ value: false, label: "Não" }
	);
	consultores: Array<{ id: number; label: string }> = new Array<{
		id: number;
		label: string;
	}>();

	constructor(
		private session: SessionService,
		private dialogService: DialogService,
		private readonly pactoModal: ModalService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private clientDiscoveryService: ClientDiscoveryService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private perfilClienteService: PerfilClienteService,
		private router: Router,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private matDialog: MatDialog,
		private colaboradorService: AdmCoreApiColaboradorService
	) {
		this.builForm();
	}

	ngOnInit() {
		this.findOrigemSistema();
		this.getConsultores();
		this.verificaExibeRenovacao();
		this.configuracoesSistema = this.perfilClienteService.configuracoesSistema;
		this.empresa = this.perfilClienteService.empresa;
		this.isAutorizarEstornoContrato =
			!!this.session.perfilUsuarioAdm.perfilUsuario.funcionalidades.find(
				(r) => r.referenciaFuncionalidade === "3.19"
			);
		this.verificaExibeBotaoEstornar();
		if (this.configuracoesSistema) {
			this.nomenclaturaVendaCredito = this.getNomenclaturaCreditoTreino();
		}
		let countCreditColumns = 5;
		this.gridClassesCreditoSection = this._gridClassesCreditoSection;
		if (this.contract) {
			if (
				this.contract.contratoDuracao &&
				this.contract.contratoDuracao.contratoDuracaoCreditoTreino
			) {
				if (
					this.contract.contratoDuracao.contratoDuracaoCreditoTreino
						.creditoTreinoNaoCumulativo
				) {
					countCreditColumns++;
				}
			}
			if (this.contract.vendaCreditoSessao) {
				countCreditColumns++;
			}
		}
		if (countCreditColumns > 3) {
			this.qtdGridColumn = 2;
			this.gridClassesCreditoSection = this._gridClassesCreditoSection;
		}
		if (this.contract && this.contract.vendaCreditoTreino) {
			this.telaClienteService
				.consultarSaldoCredito(
					this.session.chave,
					this.contract.codigo,
					+this.dadosPessoais.matricula
				)
				.subscribe((response) => {
					this.saldoCredito = response.content.saldoCreditoTreino;
					this.marcacoesFuturas = response.content.marcacoesFuturas;
					this.cd.detectChanges();
				});
		}

		this.clientDiscoveryService
			.linkZw(this.session.usuarioOamd, this.session.empresaId)
			.subscribe((result) => {
				const cache = this.clientDiscoveryService.cache;
				cache.serviceUrls.zwUrlFull = result;
				this.clientDiscoveryService.cache = cache;
			});

		this.form.patchValue({
			tipoContrato: this.contract.origemContrato,
			nomeConsultorReponsavel: this.contract.consultorResponsavel.codigo,
			permiteRenovacaoAutomatica: this.contract.permiteRenovacaoAutomatica,
		});
	}

	builForm() {
		this.form = new FormGroup({
			tipoContrato: new FormControl(),
			nomeConsultorReponsavel: new FormControl(),
			permiteRenovacaoAutomatica: new FormControl(),
		});
	}

	toggleEdit(
		property:
			| "isEditableContract"
			| "isEditableRenovacao"
			| "isEditableConsultorResponsavel"
	) {
		if (this[property]) {
			this.saveForm();
		}
		this[property] = !this[property];
		this.cd.detectChanges();
	}

	getConsultores() {
		this.colaboradorService.findAllConsultoresAtivos().subscribe((response) => {
			if (response.content) {
				this.consultores = response.content.map((c) => ({
					id: c.codigo,
					label: c.pessoa.nome,
				}));
				const consultorResponsavel = {
					id: this.contract.consultorResponsavel.codigo,
					label: this.contract.consultorResponsavel.pessoa.nome,
				};
				if (!this.consultores.some((v) => v.id === consultorResponsavel.id)) {
					this.consultores.push(consultorResponsavel);
				}
				this.cd.detectChanges();
			}
		});
	}

	editarDadosContrato() {
		const dialogRef = this.dialogService.open(
			"Editar informações do contrato",
			ModalEditarDadosContratoComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.contract = this.contract;
		dialogRef.result
			.then((result) => this.reloadContract.emit())
			.catch((e) => {});
	}

	estornar() {
		let url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=ESTORNO&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Estorno", 800, 595);
	}

	bonus() {
		let url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=BONUS&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Bonus", 800, 595);
	}

	afastamento() {
		let url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=AFASTAMENTO&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Afastamento", 800, 595);
	}

	alterarHorario() {
		if (this.contract && this.contract.vendaCreditoTreino) {
			this.notificationService.error(
				"Operação não permitida para planos 'Venda crédito de treino'",
				"Alterar horário",
				{
					timeout: undefined,
				}
			);
			return;
		}
		const url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=ALTERAR_HORARIO&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Alterar Horário", 800, 595);
	}

	getZWUrlFull() {
		if (
			this.clientDiscoveryService.getUrlMap() &&
			this.clientDiscoveryService.getUrlMap().zwUrlFull
		) {
			return this.clientDiscoveryService.getUrlMap().zwUrlFull;
		}
	}

	transferirContrato() {
		const dialogRef = this.dialogService.open(
			`Transferir direito de uso do contrato`,
			TransferirContratoComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.codCliente = this.dadosPessoais.codigoCliente;
		dialogRef.componentInstance.codContrato = this.contract.codigo;
		dialogRef.result
			.then((result) => {
				if (result.reloadContrato) {
					this.reloadContract.emit();
				}
			})
			.catch((e) => {});
	}

	recuperarContrato() {
		const dialogRef = this.dialogService.open(
			`Recuperar os direitos de uso do contrato`,
			RecuperarContratoComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.codCliente = this.dadosPessoais.codigoCliente;
		dialogRef.componentInstance.codContrato = this.contract.codigo;
		dialogRef.componentInstance.clienteNome = this.dadosPessoais.nome;
		dialogRef.result
			.then((result) => {
				if (result.reloadContrato) {
					this.reloadContract.emit();
				}
			})
			.catch((e) => {});
	}

	retornoAtestado() {
		let url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=RETORNO_ATESTADO&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Retorno Atestado", 800, 595);
	}

	retornoTrancamento() {
		let url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=RETORNO_TRANCAMENTO&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Retorno Trancamento", 800, 595);
	}

	retornoFerias() {
		let url = `${this.getZWUrlFull()}&codCliente=${
			this.dadosPessoais.codigoCliente
		}&codContrato=${
			this.contract.codigo
		}&operacaoClienteEnumName=RETORNO_FERIAS&isContratoOperacao=true&origem=angular`;
		this.abrirPopup(url, "Retorno Férias", 800, 595);
	}

	liberarVagaTurma() {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.session.chave,
					result.data.usuario,
					result.data.senha,
					"LiberarVagaNaTurma",
					"9.34 - Liberar vaga na turma de aluno inativo",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response: any) => {
						this.telaClienteService
							.liberarVagaTurma(
								this.session.chave,
								this.contract.codigo,
								this.contract.empresa.codigo,
								this.session.loggedUser.usuarioZw,
								this.contract.vigenciaAteAjustada
							)
							.subscribe(
								(response) => {
									this.notificationService.success(response.content);
									result.modal.close();
									this.reloadContract.emit(true);
								},
								(error) => {
									this.notificationService.error(error.error.meta.message);
								}
							);
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	findOrigemSistema() {
		if (this.contract && this.contract.origemSistema) {
			switch (this.contract.origemSistema) {
				case OrigemSistema.ZW:
					this.textoOrigemContrato = "ZillyonWeb";
					break;
				case OrigemSistema.AULA_CHEIA:
					this.textoOrigemContrato = "Agenda Web";
					break;
				case OrigemSistema.TREINO:
					this.textoOrigemContrato = "Pacto Treino";
					break;
				case OrigemSistema.APP_TREINO:
					this.textoOrigemContrato = "App Treino";
					break;
				case OrigemSistema.APP_PROFESSOR:
					this.textoOrigemContrato = "App Professor";
					break;
				case OrigemSistema.AUTO_ATENDIMENTO:
					this.textoOrigemContrato = "Autoatendimento";
					break;
				case OrigemSistema.SITE:
					this.textoOrigemContrato = "Site vendas";
					break;
				case OrigemSistema.BUZZLEAD:
					this.textoOrigemContrato = "Buzz Lead";
					break;
				case OrigemSistema.VENDAS_ONLINE_2:
					this.textoOrigemContrato = "Vendas 2.0";
					break;
				case OrigemSistema.APP_CONSULTOR:
					this.textoOrigemContrato = "App do consultor";
					break;
				case OrigemSistema.BOOKING_GYMPASS:
					this.textoOrigemContrato = "Booking Gympass";
					break;
				case OrigemSistema.FILA_ESPERA:
					this.textoOrigemContrato = "Fila de espera";
					break;
				case OrigemSistema.IMPORTACAO_API:
					this.textoOrigemContrato = "Importação API";
					break;
				case OrigemSistema.HUBSPOT:
					this.textoOrigemContrato = "Hubspot Lead";
					break;
				case OrigemSistema.CRM_META_DIARIA:
					this.textoOrigemContrato = "CRM Meta Diária";
					break;
				case OrigemSistema.APP_FLOW:
					this.textoOrigemContrato = "App Flow";
					break;
				case OrigemSistema.NOVA_TELA_NEGOCIACAO:
					this.textoOrigemContrato = "Nova Tela de Negociação";
					break;
				default:
					this.textoOrigemContrato = "";
					break;
			}
			this.cd.detectChanges();
		}
	}

	private getNomenclaturaCreditoTreino() {
		switch (this.configuracoesSistema.nomenclaturaVendaCredito) {
			case "CT":
				return "Crédito Treino";
			case "CH":
				return "Crédito Hora/Aula";
			default:
				return "Crédito Treino";
		}
	}

	openModalDetalhamentoVisaoGeral() {
		const dialogRef = this.dialogService.open(
			"Detalhamento do visão geral",
			ModalDetalhamentoVisaoGeralComponent,
			PactoModalSize.LARGE,
			"modal-detalhamento-contrato"
		);
		dialogRef.componentInstance.contract = this.contract;
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		dialogRef.componentInstance.nomenclaturaCredito =
			this.nomenclaturaVendaCredito;
		dialogRef.componentInstance.opcoesContrato = this.opcoesContrato;
		dialogRef.componentInstance.reloadContract.subscribe((reload) => {
			if (reload) {
				this.reloadContract.emit();
				dialogRef.componentInstance.contract = this.contract;
				dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
				dialogRef.componentInstance.nomenclaturaCredito =
					this.nomenclaturaVendaCredito;
				dialogRef.componentInstance.opcoesContrato = this.opcoesContrato;
			}
		});
		dialogRef.result
			.then((result) => this.reloadContract.emit())
			.catch((e) => {});
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	) {
		const atributos = `width=${comprimento}, height=${altura},left=0,screenX=0,top=0,screenY=0,dependent=yes,menubar=no,toolbar=no,resizable=yes,scrollbars=yes`;
		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		const win = window.open(urlPopup, nomeJanela, atributos);
		const timer = setInterval(() => {
			if (win && win !== undefined && win.closed) {
				clearInterval(timer);
				this.goBackClient.emit();
			}
		}, 500);
	}

	openModalAlterarPlano() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.session.chave,
				this.session.loggedUser.usuarioZw,
				this.session.empresaId,
				"MudarPlanoContratoAtivo",
				"9.48 - Realizar mudança de plano de um contrato ativo (upgrade/downgrade). Cancelar o contrato atual e gerar um contrato com novo plano."
			)
			.subscribe(
				(response) => {
					const dialogRef = this.pactoModal.open(
						"Alterar plano",
						ModalAlterarPlanoContratoComponent,
						PactoModalSize.LARGE
					);
					dialogRef.componentInstance.contrato = this.contract.codigo;
					dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
					dialogRef.componentInstance.response.subscribe((res) => {
						if (res === "atualizar_cliente") {
							this.reloadContract.emit();
							this.cd.detectChanges();
							this.router.navigate(
								[
									"pessoas",
									"perfil-v2",
									this.dadosPessoais.matricula,
									"contratos",
								],
								{ queryParams: { reload: new Date().getTime() } }
							);
						} else if (res === "atualizar_contrato") {
							this.reloadContract.emit();
							this.cd.detectChanges();
						}
					});
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	openModalAlterarVencimento() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.session.chave,
				this.session.loggedUser.usuarioZw,
				this.session.empresaId,
				"MudarDataVencimentoParcelas",
				"9.51 - Realizar alteração na data de vencimento de contratos recorrentes vigentes"
			)
			.subscribe(
				(response) => {
					const modalRef = this.pactoModal.open(
						"Alterar vencimento de parcela",
						ModalAlterarVencimentoParcelasContratoComponent,
						PactoModalSize.MEDIUM
					);
					modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
					modalRef.componentInstance.contrato = this.contract.codigo;
					modalRef.componentInstance.diaVencimento =
						this.contract.contratoRecorrencia.diaVencimentoCartao;
					modalRef.componentInstance.response.subscribe((res) => {
						if (res === "atualizar_contrato") {
							this.reloadContract.emit();
							this.cd.detectChanges();
						}
					});
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	saveForm() {
		this.telaClienteService
			.alterarDadosContrato(
				this.session.chave,
				this.contract.codigo,
				this.session.loggedUser.usuarioZw,
				+this.form.get("nomeConsultorReponsavel").value,
				+this.form.get("tipoContrato").value,
				this.form.get("permiteRenovacaoAutomatica").value
			)
			.subscribe({
				next: () => {
					this.reloadContract.emit(true);
					this.isEditableContract = false;
					this.isEditableRenovacao = false;
					this.isEditableConsultorResponsavel = false;
					this.notificationService.success("Dados alterados com sucesso!");
				},
				error: (erro) => {
					const error = erro.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message);
					}
				},
			});
	}

	verificaExibeBotaoEstornar() {
		if (!this.isAutorizarEstornoContrato) {
			if (
				this.configuracoesSistema &&
				this.configuracoesSistema.permiteestornarcontrato30minaposlancamento &&
				this.configuracoesSistema.permiteestornarcontrato30minaposlancamento ===
					true
			) {
				if (
					this.contract.codUsuarioResponsavelLancamento ===
						this.session.codUsuarioZW &&
					(this.contract.dataAlteracaoManual === null ||
						this.contract.dataAlteracaoManual === undefined)
				) {
					let dataAtual = new Date();
					let dataAtualUtcDate = new Date(
						Date.UTC(
							dataAtual.getFullYear(),
							dataAtual.getMonth(),
							dataAtual.getDate(),
							dataAtual.getHours(),
							dataAtual.getMinutes(),
							dataAtual.getSeconds(),
							dataAtual.getMilliseconds()
						)
					);

					let dataLancamentoContrato = new Date(this.contract.dataLancamento);
					let dataLancamentoContratoUtcDate = new Date(
						Date.UTC(
							dataLancamentoContrato.getFullYear(),
							dataLancamentoContrato.getMonth(),
							dataLancamentoContrato.getDate(),
							dataLancamentoContrato.getHours(),
							dataLancamentoContrato.getMinutes(),
							dataLancamentoContrato.getSeconds(),
							dataLancamentoContrato.getMilliseconds()
						)
					);
					if (
						dataLancamentoContratoUtcDate.getTime() <
							dataAtualUtcDate.getTime() &&
						(dataAtualUtcDate.getTime() -
							dataLancamentoContratoUtcDate.getTime()) /
							1000 /
							60 <=
							30
					) {
						this.isAutorizarEstornoContrato = true;
					}
				}
			}
		}
	}

	verificaExibeRenovacao() {
		if (this.contract.contratoRecorrencia) {
			if (this.contract.plano.planoRecorrencia) {
				if (
					this.contract.contratoRecorrencia.renovavelAutomaticamente ||
					this.contract.plano.planoRecorrencia.renovavelAutomaticamente
				) {
					this.hasRenovacaoAutomatica = true;
				}
			}
		} else {
			if (this.contract.plano) {
				if (
					this.contract.renovavelAutomaticamente ||
					this.contract.plano.renovavelAutomaticamente
				) {
					this.hasRenovacaoAutomatica = true;
				}
			}
		}
	}

	private get _gridClassesCreditoSection() {
		return `pct-col-${this.qtdGridColumn} pct-col-md-${this.qtdGridColumn} pct-col-lg-${this.qtdGridColumn} pct-col-xl-${this.qtdGridColumn}`;
	}
}
