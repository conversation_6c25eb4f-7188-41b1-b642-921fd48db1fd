<!-- <div class="content">
	<div class="row">
		<div class="col-2 data">
			<span class="title">Contrato</span>
			<span class="value">{{ contract?.codigo || "-" }}</span>
		</div>
		<div class="col-2 data">
			<span class="title" i18n="@@contract-detail:resp-lancamento-contrato">
				Cliente
			</span>
			<span class="value">
				{{ (contract?.pessoaDTO?.nome | captalize) || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Nome do plano</span>
			<span class="value">
				{{ (contract?.descricaoPlano | captalize) || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Início</span>
			<span class="value">
				{{ contract?.vigenciaDe | date : "dd/MM/yyyy" || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Fim</span>
			<span class="value">
				{{ contract?.vigenciaAteAjustada | date : "dd/MM/yyyy" || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Valor do contrato</span>
			<span class="value">{{ contract?.valor | currency : "BRL" || "-" }}</span>
		</div>
	</div>

	<div class="row">
		<div class="col-2 data">
			<span class="title">Valor pago</span>
			<span class="value">
				{{ contract?.valorPago | currency : "BRL" || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Valor base</span>
			<span class="value">
				{{ contract?.valorBaseCalculo | currency : "BRL" || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Resp. lançamento do contrato</span>
			<span class="value">
				{{ (contract?.responsavelLancamento | captalize) || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Condição de pagamento</span>
			<span class="value">
				{{ (contract?.condicaoDePagamento | captalize) || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<div class="title">
				<span>Tipo de contrato</span>
				<i
					class="pct"
					[ngClass]="{
						'pct-save': isEditableContract,
						'pct-edit': !isEditableContract
					}"
					(click)="toggleEdit('isEditableContract')"></i>
				<pacto-cat-form-select
					*ngIf="isEditableContract"
					[control]="form.get('tipoContrato')"
					[items]="tipoContratos"></pacto-cat-form-select>
			</div>
			<span class="value" *ngIf="!isEditableContract">
				{{ contract?.tipoContrato | captalize }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Lançamento</span>
			<span class="value">
				{{ contract?.dataLancamento | date : "dd/MM/yyyy" || "-" }}
			</span>
		</div>
	</div>

	<div class="row">
		<div class="col-2 data">
			<span class="title">Horário</span>
			<span class="value">
				{{ (contract?.descricaoHorario | captalize) || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Data término original</span>
			<span class="value">
				{{ contract?.vigenciaAte | date : "dd/MM/yyyy" || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Dia do vencimento</span>
			<span class="value">
				{{ contract?.contratoRecorrencia?.diaVencimentoCartao || "-" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Duração</span>
			<span class="value">{{ (contract?.duracao | captalize) || "-" }}</span>
		</div>

		<div class="col-2 data">
			<div class="title">
				<span>Consultor responsável</span>
				<i
					class="pct"
					[ngClass]="{
						'pct-save': isEditableConsultorResponsavel,
						'pct-edit': !isEditableConsultorResponsavel
					}"
					(click)="toggleEdit('isEditableConsultorResponsavel')"></i>
				<pacto-cat-form-select
					*ngIf="isEditableConsultorResponsavel"
					[control]="form.get('nomeConsultorReponsavel')"
					[items]="consultores"></pacto-cat-form-select>
			</div>
			<span class="value" *ngIf="!isEditableConsultorResponsavel">
				{{ (contract?.nomeConsultorReponsavel | captalize) || "-" }}
			</span>
		</div>

		<div class="col-2 data">
			<div class="title">Direito de uso concedido</div>
			<div class="value">
				<div class="value">
					<ng-container *ngIf="contract.pessoaOriginal; else noData">
						<span
							*ngIf="
								opcoesContrato?.recuperarDireitoUso;
								else showOriginalName
							">
							{{ contract?.pessoaDTO?.nome | captalize }}
						</span>
						<ng-template #showOriginalName>
							{{ contract?.pessoaOriginal?.nome | captalize }}
							<i class="pct pct-chevron-right"></i>
							{{ contract?.pessoaDTO?.nome | captalize }}
						</ng-template>
					</ng-container>
					<ng-template #noData>-</ng-template>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-2 data">
			<span class="title">Bolsa</span>
			<div class="value">
				<ng-container *ngIf="contract?.plano?.bolsa; else noBolsa">
					<span *ngIf="contract?.bolsa; else noBolsaText">Sim</span>
					<ng-template #noBolsaText>
						<span i18n="@@contract-detail:contract-bolsa-no">Não</span>
					</ng-template>
				</ng-container>
				<ng-template #noBolsa>
					<span>-</span>
				</ng-template>
			</div>
		</div>
		<div class="col-2 data" *ngIf="hasRenovacaoAutomatica">
			<div class="title">
				<span>Renovação automática</span>
				<i
					class="pct"
					[ngClass]="{
						'pct-save': isEditableRenovacao,
						'pct-edit': !isEditableRenovacao
					}"
					(click)="toggleEdit('isEditableRenovacao')"></i>
				<pacto-cat-form-select
					*ngIf="isEditableRenovacao"
					[control]="form.get('permiteRenovacaoAutomatica')"
					[items]="renovacaoAutomaticaOptions"
					idKey="value"></pacto-cat-form-select>
			</div>
			<span class="value" *ngIf="!isEditableRenovacao">
				{{ contract?.permiteRenovacaoAutomatica ? "Sim" : "Não" }}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Empresa</span>
			<span class="value">{{ contract?.nomeEmpresa || "-" }}</span>
		</div>
		<div class="col-2 data" *ngIf="contract?.dataAlteracaoManual">
			<span class="title">Alteração data base</span>
			<span class="value">
				{{ (contract?.dataAlteracaoManual | date : "dd/MM/yyyy") || "-" }}
			</span>
		</div>
		<div class="col-2 data" *ngIf="contract?.convenioDesconto?.descricao">
			<span class="title">Convênio com desconto</span>
			<span class="value">
				{{ contract?.convenioDesconto?.descricao || "-" }}
			</span>
		</div>
		<div class="col-2 data" *ngIf="contract?.origemSistema !== 1">
			<span class="title">Origem</span>
			<span class="value">{{ textoOrigemContrato || "-" }}</span>
		</div>
	</div>

	<div class="row" *ngIf="contract?.grupoDesconto?.descricao">
		<div class="col-2 data">
			<span class="title">Grupo com desconto</span>
			<span class="value">{{ contract?.grupoDesconto?.descricao || "-" }}</span>
		</div>

		<div class="col-2 data" *ngIf="contract?.observacao">
			<span class="title">Observação</span>
			<span class="value">{{ contract?.observacao || "-" }}</span>
		</div>
	</div>
	<hr />
	<div class="row">
		<div class="col-2 data">
			<span class="title">Qnt. {{ nomenclaturaVendaCredito }} Compra</span>
			<span class="value">
				{{
					contract?.contratoDuracao?.contratoDuracaoCreditoTreino
						?.quantidadeCreditoCompra || 0
				}}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Qnt. Saldo</span>
			<span class="value">{{ saldoCredito }}</span>
		</div>
		<div class="col-2 data">
			<span class="title">Marcações futuras</span>
			<span class="value">{{ marcacoesFuturas }}</span>
		</div>
		<div class="col-2 data">
			<span class="title">Qnt. Mensal</span>
			<span class="value">
				{{
					contract?.contratoDuracao?.contratoDuracaoCreditoTreino
						?.creditoTreinoNaoCumulativo
						? contract?.contratoDuracao?.contratoDuracaoCreditoTreino
								?.quantidadeCreditoMensal || 0
						: "-"
				}}
			</span>
		</div>
		<div class="col-2 data">
			<span class="title">Vezes por semana</span>
			<span class="value">
				{{
					!contract?.vendaCreditoSessao
						? contract?.contratoDuracao?.contratoDuracaoCreditoTreino
								?.numeroVezesSemana || 0
						: "-"
				}}
			</span>
		</div>
	</div>
</div> -->
<div class="detail-title">
	<span i18n="@@contract-detail:overview">Visão geral</span>
	<pacto-cat-button
		id="dcvg-ver-mais"
		label="Ver Mais"
		type="OUTLINE_NO_BORDER"
		class="detail-title-action"
		(click)="openModalDetalhamentoVisaoGeral()"
		size="MEDIUM"></pacto-cat-button>
</div>
<div class="detail-data-container resume-visao-geral">
	<div class="contract-row">
		<div class="detail-data">
			<div class="detail-data-title">Contrato</div>
			<div class="detail-data-value">{{ contract?.codigo }}</div>
		</div>

		<div class="detail-data">
			<div class="detail-data-title">Cliente</div>
			<div class="detail-data-value">{{ contract?.pessoaDTO?.nome }}</div>
		</div>

		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-plan">Plano</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.descricaoPlano }}</span>
			</div>
		</div>

		<div class="detail-data">
			<div class="detail-data-title">Início</div>
			<div class="detail-data-value">
				{{ contract?.vigenciaDe | date : "dd/MM/yyyy" : "UTC" }}
			</div>
		</div>

		<div class="detail-data">
			<div class="detail-data-title">Fim</div>
			<div class="detail-data-value">
				{{ contract?.vigenciaAteAjustada | date : "dd/MM/yyyy" : "UTC" }}
			</div>
		</div>

		<div class="detail-data">
			<div class="detail-data-title">
				<span i18n="@@contract-detail:contract-value">Valor do contrato</span>
			</div>
			<div class="detail-data-value">
				<span>{{ contract?.valor | currency : "BRL" }}</span>
			</div>
		</div>
	</div>
</div>

<div class="btn-row">
	<pacto-cat-button
		*ngIf="opcoesContrato?.transferenciaDireitoUso"
		id="dcvg-tranferir-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="transferirContrato()"
		label="Transferir contrato"
		i18n-label="
			@@contract-detail:overview:transferir-contrato"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.recuperarDireitoUso"
		id="dcvg-recuperar-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="recuperarContrato()"
		label="Recuperar contrato"
		i18n-label="
			@@contract-detail:overview:recuperar-contrato"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="isAutorizarEstornoContrato"
		id="dcvg-estornar-contrato"
		type="OUTLINE_DANGER"
		size="LARGE"
		(click)="estornar()"
		label="Estornar"
		i18n-label="@@contract-detail:overview:estornar"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.retornoAtestado"
		id="dcvg-retorno-atestado-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="retornoAtestado()"
		label="Retorno atestado"
		i18n-label="@@contract-detail:overview:retorno-atestado"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.retornoTrancamento"
		id="dcvg-retorno-trancamento-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="retornoTrancamento()"
		label="Retorno trancamento"
		i18n-label="
			@@contract-detail:overview:retorno-trancamento"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.retornoCarencia"
		id="dcvg-retorno-ferias-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="retornoFerias()"
		label="Retorno férias"
		i18n-label="@@contract-detail:overview:retorno-ferias"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.alteracaoContrato"
		id="dcvg-liberar-vaga-turma-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="liberarVagaTurma()"
		label="Liberar vaga na turma"
		i18n-label="
			@@contract-detail:overview:liberar-vaga-turma"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.bonus"
		id="dcvg-bonus-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="bonus()"
		label="Bônus"
		i18n-label="@@contract-detail:overview:bonus"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.alterarVencimento"
		id="dcvg-alterar-vencimento"
		type="OUTLINE"
		size="LARGE"
		(click)="openModalAlterarVencimento()"
		label="Alterar vencimento"
		i18n-label="
			@@contract-detail:overview:alterar-vencimento"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.alterarHorario"
		id="dcvg-alterar-horario-contrato"
		type="OUTLINE"
		size="LARGE"
		(click)="alterarHorario()"
		label="Alterar horário"
		i18n-label="@@contract-detail:overview:aletrar-horario"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="opcoesContrato?.alterarPlano"
		id="dcvg-alterar-plano"
		type="OUTLINE"
		size="LARGE"
		(click)="openModalAlterarPlano()"
		label="Alterar plano"
		i18n-label="@@contract-detail:overview:alterar-plano"></pacto-cat-button>
	<pacto-cat-button
		*ngIf="
			opcoesContrato?.carencia ||
			opcoesContrato?.atestado ||
			opcoesContrato?.trancamento ||
			opcoesContrato?.cancelamento
		"
		id="dcvg-afastamento-contrato"
		icon="pct pct-pause"
		type="OUTLINE"
		size="LARGE"
		(click)="afastamento()"
		label="Afastamento"
		i18n-label="@@contract-detail:overview:afastamento"></pacto-cat-button>
	<!--		<pacto-afastamento-dropdown-->
	<!--			(selected)="afastamento()"-->
	<!--		></pacto-afastamento-dropdown>-->
</div>
