import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService, Cliente } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-transferir-contrato",
	templateUrl: "./transferir-contrato.component.html",
	styleUrls: ["./transferir-contrato.component.scss"],
})
export class TransferirContratoComponent implements OnInit {
	codContrato: number;
	codCliente: number;
	form: FormGroup = new FormGroup({
		cliente: new FormControl(undefined, Validators.required),
	});
	clientes: Array<Cliente> = new Array<Cliente>();
	contractControl = new FormControl();

	constructor(
		private dialog: NgbActiveModal,
		private notificationService: SnotifyService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.listClients();
		this.contractControl.setValue(this.codContrato);
		this.contractControl.disable();
	}

	listClients(term?) {
		if (term || this.clientes.length === 0) {
			this.telaClienteService
				.transferirDireitoContratoConsultaClientes(
					this.sessionService.chave,
					this.sessionService.loggedUser.usuarioZw,
					this.sessionService.empresaId,
					term
				)
				.subscribe((response) => {
					this.clientes = response.content;
					this.cd.detectChanges();
				});
		}
	}

	cancel() {
		this.dialog.close(false);
	}

	save() {
		if (this.form.valid) {
			this.telaClienteService
				.transferirDireitoContrato(
					this.sessionService.chave,
					this.sessionService.loggedUser.usuarioZw,
					this.codCliente,
					this.form.get("cliente").value.codigo,
					this.codContrato
				)
				.subscribe(
					(response) => {
						this.notificationService.success(response.content);
						this.dialog.close(true);
					},
					(httpResponseError) => {
						const error = httpResponseError.error;
						if (error.meta && error.meta.message) {
							this.notificationService.error(error.meta.message);
						}
					}
				);
		}
	}
}
