// @import "~src/assets/scss/pacto/plataforma-import.scss";

// ::ng-deep {
// 	.design-system3-adjust {
// 		.modal-dialog {
// 			display: flex;
// 			flex-direction: column;
// 			align-items: center;
// 			justify-content: center;
// 			padding: 20px;

// 			pacto-dialog .pacto-modal-wrapper {
// 				.modal-titulo {
// 					font-size: 14px;
// 					text-align: center;
// 					margin-bottom: 16px;
// 				}
// 			}
// 		}
// 	}
// }

// .pacto-detalhamento-contrato-visao-geral {
// 	pacto-cat-button {
// 		.pacto-button {
// 			padding: 12px 20px;

// 			.content {
// 				font-size: 14px;
// 				font-family: "Nunito Sans";
// 				font-style: normal;
// 				font-weight: 700;
// 				line-height: 100%;
// 				padding: 0;
// 				margin: 0;
// 			}
// 		}
// 	}

// 	.btn-row {
// 		display: flex;
// 		margin-bottom: 8px;

// 		> * + * {
// 			margin-left: 1rem;
// 		}
// 	}

// 	.detail-title {
// 		display: flex;
// 		justify-content: space-between;
// 		align-items: center;
// 	}
// }

// .contract-row {
// 	.actual-contract {
// 		.actual-contract-info {
// 			font-size: 16px;
// 			font-weight: 600;
// 			line-height: 27.28px;
// 			color: $pretoPri;
// 		}
// 	}

// 	.actual-contract-title,
// 	.client-data-title,
// 	.contract-start-date-title,
// 	.contract-final-date-title {
// 		font-size: 12px;
// 		font-weight: 400;
// 		line-height: 16.37px;
// 		margin-bottom: 3px;
// 		color: $cinza07;
// 	}

// 	.client-data-info,
// 	.contract-data-info,
// 	.contract-start-date-info,
// 	.contract-final-date-info {
// 		font-size: 16px;
// 		font-weight: 400;
// 		line-height: 25.89px;
// 		color: $pretoPri;
// 	}

// 	.client-data {
// 		min-width: 131px;
// 	}

// 	.contract-start-date,
// 	.contract-final-date {
// 		min-width: 86px;
// 	}

// 	.client-data,
// 	.contract-start-date {
// 		margin-right: 48px;
// 	}
// }

// .resume-visao-geral {
// 	.row {
// 		flex-wrap: wrap;

// 		.detail-data {
// 			&:not(:last-child) {
// 				margin-right: auto;
// 			}

// 			.detail-data-title {
// 				font-size: 12px;
// 				font-weight: 400;
// 				line-height: 16.37px;
// 				margin-bottom: 3px;
// 			}

// 			.detail-data-value {
// 				font-size: 16px;
// 				font-weight: 700;
// 				line-height: 20px;
// 			}
// 		}
// 	}
// }

// ::ng-deep.modal-detalhamento-contrato {
// 	&.modal {
// 		z-index: 2000;
// 	}

// 	.modal-dialog {
// 		max-width: 1000px;
// 	}
// }

@import "~src/assets/scss/pacto/plataforma-import.scss";

::ng-deep {
	.design-system3-adjust {
		.modal-dialog {
			pacto-dialog .pacto-modal-wrapper {
				.modal-titulo {
					font-size: 14px;
				}
			}
		}
	}
}

.pacto-detalhamento-contrato-visao-geral {
	pacto-cat-button {
		.pacto-button {
			padding: 12px 20px;

			.content {
				font-size: 14px;
				font-family: "Nunito Sans";
				font-style: normal;
				font-weight: 700;
				line-height: 100%;
				padding: 0;
				margin: 0;
			}
		}
	}

	.btn-row {
		display: flex;
		margin-bottom: 8px;

		> * + * {
			margin-left: 1rem;
		}
	}

	.detail-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}

.contract-row {
	.actual-contract {
		.actual-contract-info {
			font-size: 16px;
			font-weight: 600;
			line-height: 27.28px;
			color: $pretoPri;
		}
	}

	.actual-contract-title,
	.client-data-title,
	.contract-start-date-title,
	.contract-final-date-title {
		font-size: 12px;
		font-weight: 400;
		line-height: 16.37px;
		margin-bottom: 3px;
		color: $cinza07;
	}

	.client-data-info,
	.contract-data-info,
	.contract-start-date-info,
	.contract-final-date-info {
		font-size: 16px;
		font-weight: 400;
		line-height: 25.89px;
		color: $pretoPri;
	}

	.client-data {
		min-width: 131px;
	}

	.contract-start-date,
	.contract-final-date {
		min-width: 86px;
	}

	.client-data,
	.contract-start-date {
		margin-right: 48px;
	}
}

.resume-visao-geral {
	.contract-row {
		flex-wrap: wrap;

		.detail-data {
			&:not(:last-child) {
				margin-right: auto;
			}

			.detail-data-title {
				font-size: 12px;
				font-weight: 400;
				line-height: 16.37px;
				margin-bottom: 3px;
			}

			.detail-data-value {
				font-size: 16px;
				font-weight: 700;
				line-height: 20px;
			}
		}
	}
}

::ng-deep.modal-detalhamento-contrato {
	&.modal {
		z-index: 2000;
	}
	.modal-dialog {
		max-width: 1000px;
	}
}
