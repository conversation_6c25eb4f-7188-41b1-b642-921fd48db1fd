import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmCoreApiColaboradorService,
	ApiResponseList,
	Colaborador,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	TipoContrato,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-editar-dados-contrato",
	templateUrl: "./modal-editar-dados-contrato.component.html",
	styleUrls: ["./modal-editar-dados-contrato.component.scss"],
})
export class ModalEditarDadosContratoComponent implements OnInit {
	contract;
	form: FormGroup = new FormGroup({
		tipoContrato: new FormControl(),
		consultorResponsavel: new FormControl(),
		renovavelAutomaticamente: new FormControl(),
	});

	tipoContratos = new Array<{ id: number; label: string }>(
		{
			id: TipoContrato.AGENDADO,
			label: "Agendado",
		},
		{
			id: TipoContrato.ESPONTANEO,
			label: "Espontâneo",
		}
	);

	renovacaoAutomaticaOptions = new Array<any>(
		{
			value: true,
			label: "Sim",
		},
		{
			value: false,
			label: "Não",
		}
	);

	apresentarCampoAlterarConsultor = false;
	apresentarCampoRenovacaoAutomatica = false;
	consultores: Array<{ id: number; label: string }> = new Array<{
		id: number;
		label: string;
	}>();

	constructor(
		private dialog: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private cd: ChangeDetectorRef,
		private colaboradorService: AdmCoreApiColaboradorService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.populateForm();
		this.verificarPermissaoAlterarConsultor();
		this.obterConsultores();
		if (this.contract.contratoRecorrencia) {
			this.apresentarCampoRenovacaoAutomatica =
				(this.contract.plano &&
					this.contract.plano.planoRecorrencia &&
					this.contract.plano.planoRecorrencia.renovavelAutomaticamente) ||
				(this.contract.contratoRecorrencia &&
					this.contract.contratoRecorrencia.renovavelAutomaticamente);
		} else {
			this.apresentarCampoRenovacaoAutomatica =
				this.contract.plano.renovavelAutomaticamente ||
				this.contract.renovavelAutomaticamente;
		}
	}

	populateForm() {
		this.form.get("tipoContrato").setValue(this.contract.origemContrato);
		this.form
			.get("consultorResponsavel")
			.setValue(this.contract.consultorResponsavel.codigo);
		this.form
			.get("renovavelAutomaticamente")
			.setValue(this.contract.permiteRenovacaoAutomatica);
	}

	verificarPermissaoAlterarConsultor() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"AlterarConsultorContrato",
				"3.26 - Alterar Consultor do Contrato"
			)
			.subscribe(
				(response) => {
					this.apresentarCampoAlterarConsultor = true;
					this.cd.detectChanges();
				},
				(error) => {
					this.apresentarCampoAlterarConsultor = false;
					this.cd.detectChanges();
				}
			);
	}

	obterConsultores() {
		this.colaboradorService
			.findAllConsultoresAtivos()
			.subscribe((response: ApiResponseList<Colaborador>) => {
				if (response.content) {
					this.consultores = response.content.map((c) => {
						return {
							id: c.codigo,
							label: c.pessoa.nome,
						};
					});
					if (
						this.consultores.findIndex(
							(v) => v.id === this.contract.consultorResponsavel.codigo
						) === -1
					) {
						this.consultores.push(this.contract.consultorResponsavel.codigo);
					}
					this.cd.detectChanges();
				}
			});
	}

	save() {
		this.telaClienteService
			.alterarDadosContrato(
				this.sessionService.chave,
				this.contract.codigo,
				this.sessionService.loggedUser.usuarioZw,
				+this.form.get("consultorResponsavel").value,
				+this.form.get("tipoContrato").value,
				this.form.get("renovavelAutomaticamente").value
			)
			.subscribe(
				(response) => {
					this.notificationService.success("Dados alterados com sucesso!");
					this.dialog.close();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message);
					}
				}
			);
	}

	cancel() {
		this.dialog.close();
	}
}
