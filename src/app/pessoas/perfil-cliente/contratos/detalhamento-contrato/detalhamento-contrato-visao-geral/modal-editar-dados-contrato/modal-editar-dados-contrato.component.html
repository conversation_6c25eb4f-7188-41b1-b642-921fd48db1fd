<div class="modal-editar-dados-contrato">
	<form [formGroup]="form">
		<pacto-cat-form-select
			[control]="form.get('tipoContrato')"
			[items]="tipoContratos"
			i18n-label="@@contract-detal:edit-data-tipo-contrato"
			label="Tipo de contrato"></pacto-cat-form-select>
		<pacto-cat-form-select
			*ngIf="apresentarCampoAlterarConsultor"
			[control]="form.get('consultorResponsavel')"
			[items]="consultores"
			i18n-label="@@contract-detal:edit-data-consultor-responsavel"
			label="Consultor responsável"></pacto-cat-form-select>
		<pacto-cat-form-select
			[control]="form.get('renovavelAutomaticamente')"
			[items]="renovacaoAutomaticaOptions"
			i18n-label="@@contract-detal:edit-data-tipo-contrato"
			idKey="value"
			label="Renovação automática"></pacto-cat-form-select>
	</form>

	<div class="medc-btn-row">
		<pacto-cat-button
			(click)="cancel()"
			i18n="@@contract-detail:edit-data-btn-cancel"
			label="Cancelar"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			(click)="save()"
			i18n="@@contract-detail:edit-data-btn-finish"
			label="Concluir"
			size="LARGE"></pacto-cat-button>
	</div>
</div>
