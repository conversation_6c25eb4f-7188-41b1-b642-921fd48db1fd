import { DatePipe } from "@angular/common";
import {
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "ui-kit";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";

@Component({
	selector: "pacto-modal-contratos-renovar-e-rematricular",
	templateUrl: "./modal-contratos-renovar-e-rematricular.component.html",
	styleUrls: ["./modal-contratos-renovar-e-rematricular.component.scss"],
})
export class ModalContratosRenovarERematricularComponent implements OnInit {
	contratos = new Array<any>();
	codCliente;
	table: PactoDataGridConfig;
	@ViewChild("colunaSituacao", { static: true })
	colunaSituacao: TemplateRef<any>;
	@ViewChild("actionsColumn", { static: true }) actionsColumn: TemplateRef<any>;
	@Output()
	update = new EventEmitter<any>();

	constructor(
		private dialog: NgbActiveModal,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private datePipe: DatePipe
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.contratos,
				};
			},
			pagination: false,
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "planoDescricao",
					titulo: "Plano",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataInicio",
					titulo: "Dt. início",
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyy"),
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataTermino",
					titulo: "Dt. fim",
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyy"),
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: false,
					celula: this.colunaSituacao,
				},
				{
					nome: "",
					titulo: "Opções",
					visible: true,
					ordenavel: false,
					celula: this.actionsColumn,
				},
			],
		});
	}

	renovarRematricularContrato(contrato, actionClicked: string) {
		// this.clientDiscoveryService.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId).subscribe(result => {
		// 	let url = `${result}&contratoRenovar=${contrato.codigo}&codCliente=${this.codCliente}&operacaoClienteEnumName=REMATRICULAR_RENOVAR_CONTRATO&isContratoOperacao=true`;
		// 	if (actionClicked === 'renovar') {
		// 		url += `&renovar=true`;
		// 	}
		// 	if (actionClicked === 'rematricular') {
		// 		url += '&rematricular=true';
		// 	}
		// 	window.open(url, '_self');
		// });
		this.update.emit(actionClicked + contrato.codigo);
		this.dialog.close();
	}

	lancarNovoContrato() {
		// this.clientDiscoveryService.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId).subscribe(result => {
		// 	window.open(
		// 		`${result}&codCliente=${this.codCliente}&operacaoClienteEnumName=NOVO_CONTRATO&isContratoOperacao=true&contratoConcomitante=true`,
		// 		'_self'
		// 	);
		// });
		this.update.emit("novocontrato");
		this.dialog.close();
	}
}
