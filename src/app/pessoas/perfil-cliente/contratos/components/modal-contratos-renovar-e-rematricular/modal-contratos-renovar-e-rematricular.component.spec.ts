import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalContratosRenovarERematricularComponent } from "./modal-contratos-renovar-e-rematricular.component";

describe("ModalContratosRenovarERematricularComponent", () => {
	let component: ModalContratosRenovarERematricularComponent;
	let fixture: ComponentFixture<ModalContratosRenovarERematricularComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalContratosRenovarERematricularComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			ModalContratosRenovarERematricularComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
