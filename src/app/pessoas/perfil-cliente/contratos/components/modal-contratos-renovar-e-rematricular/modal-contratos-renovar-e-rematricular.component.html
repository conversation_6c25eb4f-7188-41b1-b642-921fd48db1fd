<div class="container">
	<div class="descricao">
		O(s) contrato(s) abaixo precisam ser renovados ou rematriculados. Você pode
		fazer isso clicando na "Opção" respectiva.
	</div>

	<pacto-relatorio [showShare]="false" [table]="table"></pacto-relatorio>

	<ng-template #colunaSituacao let-item="item">
		<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
			<ng-container *ngIf="item.situacao === 'AT'">Ativo</ng-container>
			<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
			<ng-container *ngIf="item.situacao === 'IN'">Inativo</ng-container>
			<ng-container *ngIf="item.situacao === 'TR'">Trancado</ng-container>
		</div>
	</ng-template>

	<ng-template #actionsColumn let-item="item">
		<div
			(click)="renovarRematricularContrato(item, 'renovar')"
			*ngIf="item.renovarContrato"
			class="rematricular-renovar-contrato">
			<span>Renovar</span>
		</div>
		<div
			(click)="renovarRematricularContrato(item, 'rematricular')"
			*ngIf="item.rematricularContrato"
			class="rematricular-renovar-contrato">
			<span>Rematricular</span>
		</div>
	</ng-template>

	<div class="section-lancar-novo-contrato">
		<div class="slnc-message">
			Porém, se mesmo assim você deseja realmente lançar um novo contrato,
			clique ao lado.
		</div>
		<div>
			<pacto-cat-button
				(click)="lancarNovoContrato()"
				label="Novo Contrato"></pacto-cat-button>
		</div>
	</div>
</div>
