import {
	Component,
	EventEmitter,
	Output,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "pacto-afastamento-dropdown",
	templateUrl: "./afastamento-dropdown.component.html",
	styleUrls: ["./afastamento-dropdown.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class AfastamentoDropdownComponent {
	@Output() selected = new EventEmitter<{ id: number; value: string }>();

	onSelected(): void {}
}
