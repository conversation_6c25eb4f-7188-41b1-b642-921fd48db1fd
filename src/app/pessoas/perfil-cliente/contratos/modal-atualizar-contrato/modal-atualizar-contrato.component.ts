import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-atualizar-contrato",
	templateUrl: "./modal-atualizar-contrato.component.html",
	styleUrls: ["./modal-atualizar-contrato.component.scss"],
})
export class ModalAtualizarContratoComponent implements OnInit {
	@Output() deleteEvent: EventEmitter<any> = new EventEmitter();
	numeroProdutos: number;
	constructor(private activeModal: NgbActiveModal) {}

	ngOnInit() {}

	cancel() {
		this.activeModal.dismiss();
	}

	confirmar() {
		this.deleteEvent.emit();
		this.activeModal.close(true);
	}
}
