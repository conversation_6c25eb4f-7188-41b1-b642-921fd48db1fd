@import "~src/assets/scss/pacto/plataforma-import.scss";

.contract-detail {
	display: block;
	width: 100%;

	&:last-child {
		margin-bottom: unset;
	}

	.detail-title {
		display: flex;
		font-weight: 700;
		font-size: 16px;
		line-height: 24px;
		color: $pretoPri;
		margin-bottom: 24px;

		.detail-title-action {
			margin-left: auto;
		}
	}
}

.detail-data-container {
	width: 100%;
	border: 1px solid $cinza03;
	border-radius: 8px;
	padding: 16px;
	margin-bottom: 24px;

	&.dcm-modalidade-turma {
		padding: 0;
	}

	.contract-row {
		display: flex;
		margin-bottom: 12px;

		&:last-child {
			margin-bottom: unset;
		}
	}

	&.detail-data-container-general-info {
		border: unset;

		.detail-data {
			margin-bottom: 24px;

			&:last-child {
				margin-bottom: unset;
			}
		}
	}

	.detail-data {
		.detail-data-title {
			font-weight: 700;
			font-size: 14px;
			line-height: 24px;
			margin-bottom: 4px;

			pacto-cat-form-select {
				margin: unset;

				.pacto-label {
					display: none;
				}

				.pct-error-msg {
					display: none;
				}
			}

			i {
				&.pct-edit,
				&.pct-save {
					cursor: pointer;
					margin-left: 8px;
					color: $azulimPri;
				}
			}
		}

		.detail-data-value {
			font-weight: 400;
			font-size: 12px;
			line-height: 24px;
			text-transform: lowercase;

			&::first-line {
				text-transform: capitalize;
			}
		}
	}
}

.btn-row {
	display: flex;
	justify-content: flex-end;

	pacto-cat-button {
		display: block;
		margin-right: 10px;
	}

	.btn-contract {
		background: #fafafa;
		font-weight: 400;
		font-size: 14px;
		line-height: 24px;
		border-radius: 4px;
		padding: 8px 12px;
		height: 40px;
		display: flex;
		justify-content: center;
		outline: none;
		border: unset;
		cursor: pointer;

		i {
			margin-right: 14px;
			font-size: 20px;
			line-height: 20px;
		}

		&.primary {
			color: $azulim05;
		}

		&.azul-pacto {
			color: $azulPacto02;
		}

		&.danger {
			color: $hellboyPri;
		}
	}
}

.contract-table {
	.pacto-table-title-block {
		padding: 0 0 15px;
	}

	.table-content {
		padding: unset;

		pacto-relatorio-renderer {
			table {
				tbody {
					td {
						text-transform: lowercase;

						&::first-line {
							text-transform: capitalize;
						}
					}
				}
			}
		}
	}
}

::ng-deep.afastamento-modal {
	&.modal {
		.modal-dialog {
			width: 830px;
			height: 650px;
			max-width: unset;
		}
	}
}

::ng-deep.alterar-vencimento-modal {
	&.modal {
		.modal-dialog {
			width: 1066px;
			height: 590px;
			max-width: unset;
		}
	}
}

:host {
	::ng-deep {
		pacto-relatorio {
			.table-content table.table {
				th {
					font-size: 14px;
				}

				td {
					font-size: 12px;

					i {
						font-size: 16px;
					}
				}
			}
		}
	}
}
