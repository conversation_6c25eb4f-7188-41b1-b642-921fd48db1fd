import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { DatePipe } from "@angular/common";
import { DialogService } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { ClienteDadosPessoais } from "adm-core-api";
import { FormBuilder, FormGroup } from "@angular/forms";
import { PermissaoService } from "pacto-layout";

@Component({
	selector: "pacto-modal-alterar-plano-contrato",
	templateUrl: "./modal-alterar-plano-contrato.component.html",
	styleUrls: ["./modal-alterar-plano-contrato.component.scss"],
})
export class ModalAlterarPlanoContratoComponent implements OnInit {
	contrato: number;
	dadosPessoais: ClienteDadosPessoais;
	form: FormGroup;
	lista = [];
	dadosCalculo;
	@Output()
	response = new EventEmitter<any>();

	constructor(
		private readonly dialog: NgbActiveModal,
		private readonly sessionService: SessionService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly datePipe: DatePipe,
		private readonly dialogService: DialogService,
		private readonly snotifyService: SnotifyService,
		private readonly cd: ChangeDetectorRef,
		private permissaoService: PermissaoService,
		private fb: FormBuilder
	) {}

	ngOnInit() {
		this.form = this.fb.group({
			plano: this.fb.control(0),
		});
		this.carregarPlanos();
		this.cd.detectChanges();
		this.form.get("plano").valueChanges.subscribe((value) => {
			if (value) {
				this.obterInfoAlteracaoPlano();
			} else {
				this.dadosCalculo = null;
			}
		});
	}

	carregarPlanos() {
		const body = {
			contrato: this.contrato,
		};
		this.dadosCalculo = null;
		this.admLegadoTelaClienteService
			.obterPlanosAlterarPlano(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					const json = JSON.parse(resp.content);
					this.lista = json.planos;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}

	close() {
		this.dialog.close();
	}

	obterInfoAlteracaoPlano() {
		this.dadosCalculo = null;
		const plano = this.form.get("plano").value;
		if (!plano || plano <= 0) {
			return;
		}
		const body = {
			contrato: this.contrato,
			cliente: this.dadosPessoais.codigoCliente,
			plano,
		};
		this.admLegadoTelaClienteService
			.obterInformacoesAlterarPlano(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					this.dadosCalculo = JSON.parse(resp.content);
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}

	salvar() {
		const plano = this.form.get("plano").value;
		if (!plano || plano <= 0) {
			this.snotifyService.error("Selecione o plano");
			return;
		}
		const body = {
			contrato: this.contrato,
			cliente: this.dadosPessoais.codigoCliente,
			plano,
		};
		this.admLegadoTelaClienteService
			.alterarPlano(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					const nomeCliente = this.dadosPessoais.nome;
					this.snotifyService.success(
						`Plano alterado com sucesso para o cliente: ${nomeCliente}.`
					);
					this.response.emit("atualizar_cliente");
					this.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}
}
