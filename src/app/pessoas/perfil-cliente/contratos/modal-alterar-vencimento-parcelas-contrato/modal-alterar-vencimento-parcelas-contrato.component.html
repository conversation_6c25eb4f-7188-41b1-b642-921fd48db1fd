<div class="div-geral">
	<pacto-cat-select
		[control]="form.get('diaVencimento')"
		[items]="listaDias"
		idKey="id"
		label="Escolha um novo dia de cobrança das parcelas"
		labelKey="label"></pacto-cat-select>

	<div *ngIf="permissao9_58 && isGerouProRata()" style="padding-top: 20px">
		<pacto-cat-checkbox
			[control]="form.get('liberarValorProRata')"
			[label]="'Liberar cobrança de pró-rata'"
			id="liberar-cobranca-pro-rata"></pacto-cat-checkbox>
	</div>

	<div
		*ngIf="
			dadosCalculo?.valorProRataAlteracaoDataVencimentoParcelas == 0 ||
			isLiberouProRata()
		"
		class="col-12 div-text-info">
		<div class="text-info">
			A data de vencimento das parcelas será alterada e nenhuma parcela de Pro
			Rata será cobrada.
		</div>
	</div>
	<div
		*ngIf="dadosCalculo?.valorProRataAlteracaoDataVencimentoParcelas == 0"
		class="col-12 div-text-info">
		<div
			*ngIf="
				dadosCalculo?.toleranciaProRataEmpresa > 0 &&
				dadosCalculo?.toleranciaProRataEmpresa >=
					dadosCalculo?.diferencaDiasNovoVencimento
			"
			class="text-info">
			Não será gerado nenhum valor de pro-rata pois a configuração da empresa
			está configurada para tolerar os dias em questão.
		</div>
	</div>

	<div *ngIf="isGerouProRata()" class="col-12 div-text-info">
		<div *ngIf="!isLiberouProRata()" class="text-info">
			Será gerado uma parcela de
			<span style="font-weight: bold">Pro Rata</span>
			para o dia
			<span style="font-weight: bold">
				{{ form.get("diaVencimento").value }}
			</span>
			no valor de
			<span style="font-weight: bold">
				{{
					dadosCalculo?.valorProRataAlteracaoDataVencimentoParcelas
						| currency : "BRL"
				}}
			</span>
			referente a
			<span style="font-weight: bold">
				{{ dadosCalculo?.diferencaDiasNovoVencimento }} dias
			</span>
		</div>
	</div>

	<div class="col-12 div-inferior-btn text-lg-right">
		<pacto-cat-button
			(click)="close()"
			id="obj-btn-cancelar"
			label="Cancelar"
			size="NORMAL"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvar()"
			[disabled]="disabledSalvar"
			id="obj-btn-confirmar"
			label="Confirmar alteração de vencimento"
			size="NORMAL"
			style="padding-left: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
