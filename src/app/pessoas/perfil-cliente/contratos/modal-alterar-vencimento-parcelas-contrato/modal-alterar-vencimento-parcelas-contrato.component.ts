import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { DatePipe } from "@angular/common";
import { DialogService } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { ClienteDadosPessoais } from "adm-core-api";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { PermissaoService } from "pacto-layout";

@Component({
	selector: "pacto-modal-alterar-vencimento-parcelas-contrato",
	templateUrl: "./modal-alterar-vencimento-parcelas-contrato.component.html",
	styleUrls: ["./modal-alterar-vencimento-parcelas-contrato.component.scss"],
})
export class ModalAlterarVencimentoParcelasContratoComponent implements OnInit {
	contrato: number;
	diaVencimento: any;
	dadosPessoais: ClienteDadosPessoais;
	form: FormGroup;
	listaDias = [];
	dadosCalculo;
	@Output()
	response = new EventEmitter<any>();
	permissao9_58 = false;

	constructor(
		private readonly dialog: NgbActiveModal,
		private readonly sessionService: SessionService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly datePipe: DatePipe,
		private readonly dialogService: DialogService,
		private readonly snotifyService: SnotifyService,
		private readonly cd: ChangeDetectorRef,
		private permissaoService: PermissaoService,
		private fb: FormBuilder
	) {}

	ngOnInit() {
		this.permissao9_58 = this.permissaoService.temPermissaoAdm("9.58");

		this.form = this.fb.group({
			diaVencimento: this.fb.control(this.diaVencimento),
			liberarValorProRata: this.fb.control(false),
		});

		this.carregarInfo();
		this.cd.detectChanges();
		this.form.get("diaVencimento").valueChanges.subscribe((value) => {
			if (value) {
				this.calcularAlteracaoDiaVencimento();
			} else {
				this.dadosCalculo = null;
			}
		});
	}

	get disabledSalvar(): boolean {
		return this.form.get("diaVencimento").value === this.diaVencimento;
	}

	carregarInfo() {
		const body = {
			contrato: this.contrato,
		};
		this.admLegadoTelaClienteService
			.obterInformacoesAlterarVencimentoParcelasContrato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					const json = JSON.parse(resp.content);
					this.listaDias = json.diasVencimento;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}

	close() {
		this.dialog.close();
	}

	calcularAlteracaoDiaVencimento() {
		const diaVencimento = this.form.get("diaVencimento").value;
		if (!diaVencimento || diaVencimento <= 0) {
			return;
		}
		const body = {
			contrato: this.contrato,
			diaVencimento,
		};
		this.admLegadoTelaClienteService
			.calcularAlterarVencimentoParcelasContrato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					this.dadosCalculo = JSON.parse(resp.content);
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}

	salvar() {
		if (this.disabledSalvar) {
			this.snotifyService.error(
				"O novo dia de vencimento não pode ser igual ao anterior, por favor escolha outro dia ou cancele a operação."
			);
			return;
		}

		const diaVencimento = this.form.get("diaVencimento").value;
		if (!diaVencimento || diaVencimento <= 0) {
			this.snotifyService.error("Selecione o dia de vencimento");
			return;
		}

		let liberarValorProRata = false;
		if (this.permissao9_58 && this.isGerouProRata()) {
			liberarValorProRata = this.form.get("liberarValorProRata").value;
		}
		const body = {
			contrato: this.contrato,
			diaVencimento,
			liberarValorProRata,
		};
		this.admLegadoTelaClienteService
			.alterarVencimentoParcelasContrato(
				this.sessionService.chave,
				parseInt(this.sessionService.empresaId),
				this.sessionService.codigoUsuarioZw,
				body
			)
			.subscribe(
				(resp) => {
					this.snotifyService.success(resp.content);
					this.response.emit("atualizar_contrato");
					this.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.snotifyService.error(err.meta.error);
					}
				}
			);
	}

	isLiberouProRata() {
		return this.form.get("liberarValorProRata").value;
	}

	isGerouProRata() {
		return (
			this.dadosCalculo &&
			this.dadosCalculo.valorProRataAlteracaoDataVencimentoParcelas &&
			this.dadosCalculo.valorProRataAlteracaoDataVencimentoParcelas > 0
		);
	}
}
