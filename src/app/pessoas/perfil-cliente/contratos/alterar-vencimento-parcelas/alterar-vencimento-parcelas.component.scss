@import "projects/ui/assets/scss/cores.vars";

.mavp-dia-vencimento {
	.mavp-dv-info-contrato-cliente {
		display: flex;
		margin-top: 16px;

		.mavp-dv-info {
			&:first-child {
				margin-right: 16px;
			}

			.mavp-dv-info-title {
				font-weight: 700;
				font-size: 14px;
				line-height: 24px;
				margin-bottom: 8px;
			}

			.mavp-dv-info-value {
				font-weight: 400;
				font-size: 12px;
				line-height: 24px;
				text-transform: lowercase;

				&::first-line {
					text-transform: capitalize;
				}
			}
		}
	}

	.mavp-input-dia-vencimento {
		display: flex;
		align-items: center;

		pacto-cat-form-input {
			margin-right: 16px;
			display: block;
		}
	}
}

.situacao {
	width: 6rem;
	padding: 0.3125rem 1rem;
	border-radius: 3.125rem;
	font-size: 0.75rem;
	font-weight: 400;
	text-align: center;

	&.ea {
		color: #bde2fe;
		background-color: #0380e3;
	}

	&.pg {
		color: #28ab45;
		background-color: #b7efc3;
	}

	&.ca {
		color: #90949a;
		background-color: #f3f3f4;
	}

	&.rg {
		color: #d6a10f;
		background-color: #fbeabf;
	}
}

.modal-alterar-vencimento-parcelas {
	max-height: 800px;
	overflow: auto;
}

:host {
	::ng-deep {
		pacto-relatorio {
			.table-content table.table {
				th {
					font-size: 14px;
				}

				td {
					font-size: 12px;

					i {
						font-size: 16px;
					}
				}
			}
		}

		pacto-cat-table-editable-renderer {
			.pct-table .column-cell {
				font-size: 12px;

				i {
					font-size: 16px;
				}
			}
		}
	}
}
