import { DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService, Parcela } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import {
	CatTableEditableComponent,
	DialogService,
	PactoModalSize,
} from "ui-kit";
import { PactoDataGridConfig } from "../../../../cobranca/components/relatorio-cobranca/data-grid.model";

declare var moment;

@Component({
	selector: "pacto-alterar-vencimento-parcelas",
	templateUrl: "./alterar-vencimento-parcelas.component.html",
	styleUrls: ["./alterar-vencimento-parcelas.component.scss"],
})
export class AlterarVencimentoParcelasComponent
	implements OnInit, AfterViewInit
{
	@Input()
	public contrato;
	@Input()
	public codigoCliente: number;
	public parcelas: ApiResponseList<Parcela> = {} as ApiResponseList<Parcela>;
	public table: PactoDataGridConfig;
	public errorMessage;
	apresentarDiaVencimento: boolean = false;
	@ViewChild("situacaoCell", { static: true })
	public situacaoCell: TemplateRef<any>;
	diaVencimentoControl: FormControl = new FormControl();
	private parcelasAlteradas: Array<Parcela> = new Array<Parcela>();
	private parcelasPageable: ApiResponseList<Parcela> =
		{} as ApiResponseList<Parcela>;
	private actualPage = 1;
	private actualSize = 10;
	private firstItemsLoad = true;
	@ViewChild("tableParcelas", { static: false })
	private tableParcelas: CatTableEditableComponent;
	@Output()
	update = new EventEmitter<any>();

	constructor(
		private readonly dialog: NgbActiveModal,
		private readonly sessionService: SessionService,
		private readonly telaClienteService: AdmLegadoTelaClienteService,
		private readonly datePipe: DatePipe,
		private readonly dialogService: DialogService,
		private readonly notificationService: SnotifyService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initTable();
		if (
			this.contrato &&
			this.contrato.regimeRecorrencia &&
			this.contrato.contratoRecorrencia
		) {
			this.apresentarDiaVencimento = true;
			this.diaVencimentoControl.setValue(
				this.contrato.contratoRecorrencia.diaVencimentoCartao
			);
		}
	}

	ngAfterViewInit() {
		this.list();
	}

	list() {
		this.telaClienteService
			.alterarVencimentoParcelasObter(
				{
					page: undefined,
					size: undefined,
				},
				this.sessionService.chave,
				this.contrato.codigo,
				this.codigoCliente,
				this.sessionService.loggedUser.usuarioZw
			)
			.subscribe(
				(response) => {
					this.parcelas = response;
					this.createEstadoPageObject();
					this.firstItemsLoad = false;
					if (this.tableParcelas) {
						this.tableParcelas.reloadData();
					}
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					if (
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta.message
					) {
						this.errorMessage = httpResponseError.error.meta.message;
					} else {
						this.notificationService.error(
							"Ocorreu um erro inesperado! Entre em contato com o suporte!"
						);
					}
					this.cd.detectChanges();
				}
			);
	}

	salvar() {
		this.dialog.close();
		const dialogRef = this.dialogService.confirm(
			"Confirmar alteração vencimento parcelas!",
			"O vencimento do contrato de recorrência e as parcelas em aberto serão alteradas permanentemente. Deseja continuar?",
			"Confirmar"
		);
		dialogRef.result
			.then((result) => {
				this.telaClienteService
					.alterarVencimentoParcelas(
						this.sessionService.chave,
						this.contrato.codigo,
						this.sessionService.loggedUser.usuarioZw,
						this.parcelasAlteradas,
						this.diaVencimentoControl.value
					)
					.subscribe(
						(response) => {
							this.notificationService.success(
								"Parcelas alteradas com sucesso"
							);
							this.update.emit("reloadContrato");
							this.dialog.close();
						},
						(httpResponseError) => {
							if (httpResponseError.error && httpResponseError.error.meta) {
								this.errorMessage = httpResponseError.error.meta.message;
								this.notificationService.error(
									httpResponseError.error.meta.message
								);
								this.cd.detectChanges();
							} else {
								console.log(httpResponseError);
							}
						}
					);
			})
			.catch(() => {
				// is called when you click the cancel button on the confirmation modal
				const dialogRef1 = this.dialogService.open(
					"Alterar vencimento",
					AlterarVencimentoParcelasComponent,
					PactoModalSize.LARGE,
					"alterar-vencimento-modal"
				);
				dialogRef1.componentInstance.codigoContrato = this.contrato.codigo;
				dialogRef1.componentInstance.codigoCliente = this.codigoCliente;
			});
	}

	onConfirm(event: any) {
		const controlVencimento: FormControl = event.form.get("vencimento");
		if (controlVencimento.dirty) {
			const data: any = {};
			Object.assign(data, event.row);
			data.vencimento = this.datePipe.transform(
				new Date(event.row.vencimento),
				"yyyy-MM-dd"
			);
			delete data.edit;
			this.parcelasAlteradas.push(data);
		}
	}

	situacaoText(situacao: string) {
		switch (situacao) {
			case "EA":
				return "Em Aberto";
			case "PG":
				return "Pago";
			case "CA":
				return "Cancelado";
			case "RG":
				return "Renegociado";
		}
	}

	onPageSizeChange(event: any) {
		this.actualSize = event;
		this.createEstadoPageObject();
	}

	onPageChange(event: any) {
		this.actualPage = event;
		this.createEstadoPageObject();
	}

	createEstadoPageObject() {
		this.parcelasPageable.totalElements = this.parcelas.content.length;
		this.parcelasPageable.size = this.actualSize;
		this.parcelasPageable.totalPages = Math.ceil(
			+(this.parcelas.content.length / this.actualSize)
		);
		this.parcelasPageable.first =
			this.actualPage === 0 || this.actualPage === 1;
		this.parcelasPageable.last =
			this.actualPage === this.parcelasPageable.totalPages;
		this.parcelasPageable.content = this.parcelas.content.slice(
			this.actualSize * this.actualPage - this.actualSize,
			this.actualSize * this.actualPage
		);
		this.table.state.paginaNumero = this.actualPage;
		this.table.state.paginaTamanho = this.actualSize;
		this.tableParcelas.reloadData();
		this.cd.detectChanges();
	}

	generateDates() {
		if (this.parcelas) {
			if (this.parcelas.content) {
				let novaData;
				this.parcelas.content.forEach((parcela, index) => {
					if (index === 0) {
						novaData =
							typeof parcela.vencimento === "number"
								? new Date(parcela.vencimento)
								: new Date(parcela.vencimento);
					}
					if (index > 0) {
						novaData.setMonth(novaData.getMonth() + 1);
						parcela.vencimento = new Date(novaData);
						this.parcelasAlteradas.push(parcela);
					}
				});
				this.tableParcelas.reloadData();
			}
		}
	}

	alterarDiaVencimento() {
		// const diaVencimento = this.diaVencimentoControl.value;
		// const hoje = new Date();
		// if (diaVencimento < hoje.getDate()) {
		// 	this.notificationService.error(`O dia para vencimento deve ser maior ou igual ao dia ${hoje.getDate()}`, {
		// 		timeout: 3000
		// 	});
		// 	return;
		// }
		// if (diaVencimento <= 0 || diaVencimento > 30) {
		// 	this.notificationService.error('O dia para vencimento deve estar entre 1 e 30');
		// 	return;
		// }

		if (this.parcelas) {
			if (this.parcelas.content) {
				this.parcelas.content.forEach((parcela, index) => {
					const novaData: Date =
						typeof parcela.vencimento === "number"
							? new Date(parcela.vencimento)
							: new Date(parcela.vencimento);
					novaData.setDate(this.diaVencimentoControl.value);
					parcela.vencimento = new Date(novaData);
					this.parcelasAlteradas.push(parcela);
				});
				this.tableParcelas.reloadData();
				this.notificationService.info(
					"Clique em salvar para alterar as parcelas"
				);
			}
		}
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => this.parcelasPageable,
			formGroup: new FormGroup({
				vencimento: new FormControl(),
			}),
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "contrato",
					titulo: "Contrato",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					editable: true,
					visible: true,
					inputType: "date",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					celula: this.situacaoCell,
					ordenavel: false,
				},
				{
					nome: "valorParcela",
					titulo: "Valor",
					visible: true,
					valueTransform(v) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
					ordenavel: false,
				},
			],
			beforeConfirm: (row, form, data, indexRow) =>
				this.beforeConfirm(row, form, data, indexRow),
		});
	}

	private beforeConfirm(row, form, data, indexRow) {
		const dataVencimento = form.get("vencimento").value;
		row.vencimento = new Date(new Date(row.vencimento).toISOString());
		if (dataVencimento < new Date().getTime()) {
			this.notificationService.error(
				"A data de vencimento da(s) parcela(s) não pode ser menor ou igual que a data de hoje."
			);
			return false;
		}
		return true;
	}
}
