<div class="modal-alterar-vencimento-parcelas">
	<ng-container *ngIf="!errorMessage">
		<div style="padding: 0 1rem 1rem 1rem">
			<ng-container *ngIf="apresentarDiaVencimento">
				<div class="mavp-dia-vencimento">
					<div class="mavp-dv-info-contrato-cliente">
						<div class="mavp-dv-info">
							<div class="mavp-dv-info-title">
								<span>Contrato</span>
							</div>
							<div class="mavp-dv-info-value">
								<span>{{ contrato?.codigo }}</span>
							</div>
						</div>
						<div class="mavp-dv-info">
							<div class="mavp-dv-info-title">
								<span>Cliente</span>
							</div>
							<div class="mavp-dv-info-value">
								<span>{{ contrato?.pessoaDTO?.nome }}</span>
							</div>
						</div>
					</div>

					<div class="mavp-input-dia-vencimento">
						<pacto-cat-form-input
							[control]="diaVencimentoControl"
							i18n-label="@@contrato:mavp-dia-vencimento"
							label="Dia vencimento"></pacto-cat-form-input>

						<div style="margin-top: 30px">
							<pacto-cat-button
								label="Alterar"
								i18n-label="@@contrato:mavp-btn-alterar"
								(click)="alterarDiaVencimento()"></pacto-cat-button>
						</div>
					</div>
				</div>
			</ng-container>
			<pacto-cat-table-editable
				#tableParcelas
				(confirm)="onConfirm($event)"
				(pageChangeEvent)="onPageChange($event)"
				(pageSizeChange)="onPageSizeChange($event)"
				[isEditable]="true"
				[showDelete]="false"
				[table]="table"
				actionTitle="Ações"></pacto-cat-table-editable>

			<div style="display: flex; justify-content: flex-end; gap: 1rem">
				<pacto-cat-button
					(click)="generateDates()"
					*ngIf="parcelas?.content?.length > 0"
					label="Gerar datas"
					size="LARGE"
					type="OUTLINE"></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					label="Salvar"
					size="LARGE"></pacto-cat-button>
			</div>
		</div>
	</ng-container>

	<ng-container *ngIf="errorMessage">
		<div class="modal-alterar-vencimento-parcelas">
			<span>{{ errorMessage }}</span>
		</div>
	</ng-container>

	<ng-template #situacaoCell let-item="item">
		<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
			<ng-container *ngIf="item.situacao === 'EA'">Em Aberto</ng-container>
			<ng-container *ngIf="item.situacao === 'PG'">Pago</ng-container>
			<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
			<ng-container *ngIf="item.situacao === 'RG'">Renegociado</ng-container>
		</div>
	</ng-template>
</div>
