import { Injectable } from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../../microservices/client-discovery/client-discovery.service";
import {
	AdmLegadoTelaClienteService,
	ApiResponseSingle,
	ConfiguracaoSistema,
	Empresa,
} from "adm-legado-api";

@Injectable({
	providedIn: "root",
})
export class PerfilClienteService {
	private _configuracoesSistema: ConfiguracaoSistema;
	private _empresa: Empresa;

	get empresa(): Empresa {
		return this._empresa;
	}

	get configuracoesSistema(): ConfiguracaoSistema {
		return this._configuracoesSistema;
	}

	constructor(
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private clientDiscoveryService: ClientDiscoveryService
	) {}

	loadData() {
		this.loadConfiguracoesSistema();
		this.loadEmpresa();
	}

	private loadConfiguracoesSistema() {
		this.telaClienteService
			.configuracoesSistema(this.sessionService.chave)
			.subscribe((response: ApiResponseSingle<ConfiguracaoSistema>) => {
				this._configuracoesSistema = response.content;
			});
	}

	private loadEmpresa() {
		this.telaClienteService
			.configuracoesEmpresa(
				this.sessionService.chave,
				+this.sessionService.empresaId
			)
			.subscribe((response: ApiResponseSingle<Empresa>) => {
				this._empresa = response.content;
			});
	}
}
