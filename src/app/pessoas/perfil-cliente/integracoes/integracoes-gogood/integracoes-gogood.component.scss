@import "src/assets/scss/pacto/plataforma-import.scss";

pacto-cat-card-plain {
	padding: 16px;
}

.nav-aux {
	flex-basis: 100%;
	margin: 12px 0px 36px 0px;
}

.initialForm {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.top-navigation {
	@extend .type-h2;
	color: $pretoPri;

	i {
		font-size: 24px;
		margin-right: 12px;
	}
}

.first * {
	margin-bottom: 0;
}

.divider {
	border: 0px;
	border-bottom: 1px solid #c9cbcf;
}

.table-title {
	font-size: 16px;
	font-weight: 700;
	color: #333;
}

::ng-deep {
	.pacto-table-title-block,
	.table-content {
		padding-left: 0px !important;
		padding-right: 0px !important;
	}
}

.itemVerde {
	background: green;
	padding: 10px;
	border-radius: 100%;
	width: 10px;
}

.itemVermelho {
	background: red;
	padding: 10px;
	border-radius: 100%;
	width: 10px;
}

.divBtnAcoes {
	text-align: right;
	min-width: 100%;
	display: inline-block;
	padding-top: 30px;
	grid-column-gap: 10%;
	padding-left: 1%;
}

.gogood-excluir {
	margin-left: 10px;
}

.text {
	font-family: "Nunito Sans";
	font-weight: 700;
	font-size: 18px;
	line-height: 24px;
	color: $pretoPri;
	margin-bottom: 32px;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.icone-historico {
	width: 112px;
	height: 112px;
	padding-bottom: 21px;
	display: block;
	margin: 0 auto;
}
