<div class="nav-aux">
	<a
		class="top-navigation"
		id="voltar-alunos"
		[routerLink]="['/pessoas', 'perfil-v2', matricula]">
		<i class="pct pct-arrow-left"></i>
		<span>Gogood</span>
	</a>
</div>

<pacto-cat-card-plain *ngIf="!empresaTemConfiguracao && !alunoTemDados">
	<img class="icone-historico" src="assets/images/historico_totalpass.svg" />
	<div class="text">A academia não possui integração com o GoGood</div>
</pacto-cat-card-plain>

<pacto-cat-card-plain *ngIf="empresaTemConfiguracao || alunoTemDados">
	<div class="row initialForm" *ngIf="empresaTemConfiguracao">
		<div class="col-12"><span class="table-title">Gogood</span></div>
		<div class="col-12">
			<pacto-cat-form-input
				label="Informe o token de usuário  gogood*"
				errorMsg="Informe o token de usuário gogood"
				[control]="form.get('token')"></pacto-cat-form-input>
		</div>
		<div class="col-2 divBtnAcoes">
			<pacto-cat-button
				id="btn-gogood-salvar"
				type="PRIMARY"
				label="Salvar"
				(click)="salvarGogood()"
				size="LARGE"></pacto-cat-button>

			<pacto-cat-button
				id="btn-gogood-autorizar"
				class="gogood-excluir"
				type="OUTLINE"
				label="Autorizar"
				*ngIf="temToken"
				(click)="autorizarGogood()"
				size="LARGE"></pacto-cat-button>

			<pacto-cat-button
				id="btn-gogood-excluir"
				class="gogood-excluir"
				type="OUTLINE"
				label="Excluir"
				*ngIf="temToken"
				(click)="excluirGogood()"
				size="LARGE"></pacto-cat-button>
		</div>
	</div>
	<hr class="divider" *ngIf="empresaTemConfiguracao && alunoTemDados" />
	<pacto-relatorio
		*ngIf="alunoTemDados"
		#tableDataRef
		[table]="tableData"
		tableTitle="Histórico de registro"
		[showShare]="false"></pacto-relatorio>
</pacto-cat-card-plain>

<ng-template #celulaLegenda let-gympass="item">
	<div *ngIf="!gympass.legenda" class="itemVermelho"></div>
	<div *ngIf="gympass.legenda" class="itemVerde"></div>
</ng-template>
