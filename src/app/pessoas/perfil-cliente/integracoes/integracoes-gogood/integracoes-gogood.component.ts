import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import * as moment from "moment";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiClienteService,
	AdmCoreApiIntegracoesService,
	ClienteDadosPessoais,
	ConfiguracaoIntegracaoGoGood,
} from "adm-core-api";

@Component({
	selector: "pacto-integracoes-gympass",
	templateUrl: "./integracoes-gogood.component.html",
	styleUrls: ["./integracoes-gogood.component.scss"],
})
export class IntegracoesGogoodComponent implements OnInit {
	@Input()
	dadosPessoais: ClienteDadosPessoais;

	tableData: PactoDataGridConfig;
	@ViewChild("tableDataRef", { static: false })
	tableDataRef: RelatorioComponent;
	@ViewChild("celulaLegenda", { static: true })
	public celulaLegenda;
	matricula;
	temToken = false;
	configuracao: ConfiguracaoIntegracaoGoGood;
	empresaTemConfiguracao: boolean = false;
	alunoTemDados: boolean = false;
	form = new FormGroup({
		token: new FormControl("", Validators.required),
	});

	constructor(
		private route: ActivatedRoute,
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private msIntegracoesService: AdmCoreApiIntegracoesService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private rest: RestService
	) {}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe(
			(dados) => {
				this.dadosPessoais = dados;
				this.consultarConfiguracao();
				this.initTable();
				this.initDadosGogoodCliente();
				this.obterTokenGogood();
				this.cd.detectChanges();
			},
			(error) => {
				this.snotifyService.error(error.error.meta.message);
			}
		);
	}

	initDadosGogoodCliente() {
		this.msAdmCoreService
			.historicoGogood(this.dadosPessoais.codigoPessoa)
			.subscribe((response) => {
				if (response.totalElements) {
					this.alunoTemDados = response.totalElements > 0;
					this.cd.detectChanges();
				}
			});
	}

	consultarConfiguracao() {
		this.msIntegracoesService
			.configuracaoIntegracaoGogood(this.sessionService.empresaId)
			.subscribe(
				(config) => {
					this.configuracao = config;
					this.empresaTemConfiguracao =
						this.configuracao.tokenAcademyGoGood &&
						this.configuracao.tokenAcademyGoGood.length > 0;
					this.cd.detectChanges();
				},
				(error) => {
					console.error(error);
					this.snotifyService.error(error.error.meta.message);
				}
			);
	}

	obterTokenGogood() {
		this.telaClienteService
			.obterTokenGogood(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa
			)
			.subscribe(
				(response) => {
					this.temToken =
						response.content.token && response.content.token.length > 0;
					this.form.get("token").setValue(response.content.token);
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					console.log(httpResponseError);
				}
			);
	}

	salvarGogood() {
		if (!this.form.valid) {
			this.snotifyService.error("Informe o token do gogood!");
			return;
		}
		this.telaClienteService
			.cadastrarGogood(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.form.get("token").value,
				this.configuracao.tokenAcademyGoGood
			)
			.subscribe(
				(response) => {
					this.snotifyService.success("Token salvo com sucesso!");
					this.dialog.close();
				},
				(httpResponseError) => {
					this.snotifyService.error(httpResponseError.error.meta.message);
				}
			);
	}

	autorizarGogood() {
		if (!this.form.valid) {
			this.snotifyService.error("Informe o token do gogood!");
			return;
		}
		this.telaClienteService
			.cadastrarGogood(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.form.get("token").value,
				this.configuracao.tokenAcademyGoGood
			)
			.subscribe(
				(response) => {
					this.snotifyService.success("Token validado com sucesso!");
					this.dialog.close();
				},
				(httpResponseError) => {
					this.snotifyService.error(httpResponseError.error.meta.message);
				}
			);
	}

	excluirGogood() {
		if (this.form.get("token").value && this.form.get("token").value !== "") {
			this.telaClienteService
				.excluirGogood(
					this.sessionService.chave,
					this.dadosPessoais.codigoPessoa,
					this.sessionService.empresaId,
					this.sessionService.loggedUser.usuarioZw
				)
				.subscribe(
					(response) => {
						this.obterTokenGogood();
						this.snotifyService.success("Token excluído com suceso!");
						this.dialog.close();
						this.cd.detectChanges();
					},
					(httpResponseError) => {
						this.snotifyService.error(httpResponseError.error.meta.message);
					}
				);
		}
	}

	private initTable() {
		this.tableData = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`clientes/${this.dadosPessoais.codigoPessoa}/historico-acessos-gogood`
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 3,
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataAcesso",
					titulo: "Data de acesso",
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return moment(v).format("DD/MM/YYYY");
					},
				},
				{
					nome: "tokenGogood",
					titulo: "Token",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "legenda",
					titulo: "Legenda",
					visible: true,
					ordenavel: true,
					celula: this.celulaLegenda,
				},
			],
		});
	}
}
