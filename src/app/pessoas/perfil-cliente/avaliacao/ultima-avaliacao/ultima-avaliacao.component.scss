@import "src/assets/scss/pacto/plataforma-import.scss";

.column-wrapper {
	display: grid;
	padding: 30px;
	grid-template-columns: repeat(4, 1fr);
	height: 424px;
}

.composicao {
	::ng-deep.pacto-pie-chart-wrapper {
		width: 70%;
		margin: 0 15%;
	}

	::ng-deep.legend-wrapper.vertical {
		display: grid;
		grid-template-columns: 1fr 1fr;
		justify-content: center;
		margin-top: 10px;
		margin-left: 80px;
	}
}

.avaliacao-column {
	display: flex;
	flex-direction: column;
	height: 100%;
	min-height: 100%;
	@media (max-width: $plataforma-breakpoint-medium) {
		flex-basis: 49%;
		padding-bottom: 30px;
		padding-top: 30px;
	}

	@media (max-width: $plataforma-breakpoint-small) {
		flex-basis: 100%;
		padding-bottom: 30px;
		padding-top: 30px;
	}
}

.rating {
	text-align: center;
	@extend .type-h6;
	color: $pretoPri;

	span {
		cursor: pointer;
	}

	font-weight: 700;
	font-size: 16px;

	.pct-info {
		position: relative;
		top: 2px;
		padding: 0px 5px;
	}
}

.column-title {
	@extend .type-h5;
	color: $pretoPri;
	text-align: center;
	font-weight: 700;
	flex: 1;
}

/* BASIC INFO */
.basic-info {
	.column-title {
		text-align: left;
		font-weight: 700;
	}

	.bit .value {
		@extend .type-h6;
		font-size: 16px;
		line-height: 16px;

		&.objetivos {
			text-transform: capitalize;
		}
	}

	.bit .bit-label {
		@extend .type-p-small-rounded;
		font-size: 16px;
		line-height: 16px;
		color: $pretoPri;
		font-weight: 700;
		margin-bottom: 10px;
	}
}

.bit {
	display: flex;
	flex-direction: column-reverse;
	flex: 1;
}

/* IMC */
.imc {
	text-align: center;

	.bit {
		display: flex;
		flex-direction: column-reverse;
		flex: 1;
	}

	.column-title {
		flex: 1;
		text-align: center;
	}

	.info-value {
		font-weight: 700;
		font-size: 56px;
		line-height: 56px;
	}

	.info-title {
		margin-top: 16px;
		color: $preto01;
		font-weight: 400;
		font-size: 16px;
		line-height: 16px;
	}

	.smalls {
		text-align: left;
		display: flex;
		justify-content: center;
		text-align: left;

		.small-info:last-child {
			margin-left: 80px;
		}

		.small-info {
			color: $pretoPri;
			font-size: 16px;
			line-height: 16px;

			.info-label {
				font-weight: 700;
			}
		}
	}
}

/* LISTA */
.lista-avaliacoes {
	display: flex;

	.bit:last-child {
		text-align: right;

		div {
			display: inline-block;

			::ng-deep.pacto-button {
				margin-left: 10px;
				padding: 3px 10px;
				font-size: 16px;
			}
		}
	}

	.bit:first-child {
		flex-direction: column;
	}

	pacto-cat-lista-avaliacoes {
		display: block;
		flex-basis: 100%;
	}

	.column-title {
		text-align: left;
	}
}
