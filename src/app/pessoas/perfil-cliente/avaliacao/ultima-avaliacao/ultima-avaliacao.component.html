<div *ngIf="ultima">
	<div class="column-wrapper">
		<!-- BASIC INFO -->
		<div class="basic-info avaliacao-column">
			<div class="column-title" i18n="@@perfil-aluno:avaliacoes:ultima">
				Última Avaliação
			</div>
			<div class="bit">
				<div
					[text]="objetivosLabel"
					class="value objetivos"
					pacto-cat-text></div>
				<div class="bit-label" i18n="@@perfil-aluno:avaliacoes:objetivo">
					Objetivo do aluno
				</div>
			</div>
			<div class="bit">
				<div class="value">{{ paFc }} bpm</div>
				<div class="bit-label">PA/FC</div>
			</div>
			<div class="bit ponta">
				<div class="value">{{ avaliadorNome }}</div>
				<div class="bit-label" i18n="@@perfil-aluno:avaliacoes:professor">
					Nome do professor
				</div>
			</div>
		</div>

		<!-- IMC -->
		<div class="imc avaliacao-column">
			<div class="column-title">IMC</div>
			<div class="bit">
				<div class="info-title" i18n="@@perfil-aluno:avaliacoes:info-title">
					Índice de massa corporal
				</div>
				<div class="info-value">
					{{ ultima.alunoBI.imc ? ultima.alunoBI.imc : "-" }}
				</div>
			</div>
			<div class="bit">
				<div class="smalls">
					<div class="small-info">
						<div class="info-label" i18n="@@perfil-aluno:avaliacoes:altura">
							Altura
						</div>
						<div class="value">{{ altura }} m</div>
					</div>
					<div class="small-info">
						<div class="info-label" i18n="@@perfil-aluno:avaliacoes:peso">
							Peso
						</div>
						<div class="value">{{ peso }} kg</div>
					</div>
				</div>
			</div>
			<div class="bit">
				<div class="rating">
					<span (click)="imcReferenciaHandler()">
						{{ imc.getLabel(ultima.imcNota) }}
						<i class="pct pct-info"></i>
					</span>
				</div>
			</div>
		</div>

		<!-- COMPOSIÇÃO CORPORAL -->
		<div
			*ngIf="informacoesComposicaoDisponiveis"
			class="avaliacao-column composicao">
			<div
				class="column-title"
				i18n="@@perfil-aluno:avaliacoes:composicaoCorporal">
				Composição corporal
			</div>
			<div class="bit">
				<pacto-cat-chart-legend
					[colors]="colors"
					[labels]="legendLabels"
					[vertical]="true"
					class="ocuAulasLabels"></pacto-cat-chart-legend>
				<pacto-cat-pie-chart
					[colors]="colors"
					[centerValue]="percentualGordura"
					[id]="'composicao-pie'"
					[labelCenter]="composicaoLabels.getLabel('gordura')"
					[labelFormatterBorder]="composicaoLabelBorderFn()"
					[labelFormatter]="composicaoLabelFn()"
					[series]="composicaoSeries"
					[showLegend]="false"
					[simbol]="'kg'"></pacto-cat-pie-chart>
			</div>
			<div class="bit">
				<div class="rating">
					<span
						(click)="composicaoReferenciaHandler()"
						id="av-composicao-ultima-avaliacao">
						{{ composicao.getLabel(ultima.composicaoNota) }}
						<i class="pct pct-info"></i>
					</span>
				</div>
			</div>
		</div>

		<!-- AVALIAÇÕES FISICAS -->
		<div class="lista-avaliacoes avaliacao-column">
			<div class="column-title" i18n="@@perfil-aluno:avaliacoes:titulo">
				Avaliações
			</div>
			<div class="bit">
				<pacto-cat-lista-avaliacoes
					(remove)="removeAvaliacaoHandler($event)"
					[alunoId]="aluno?.id"
					[avaliacoes]="todas"></pacto-cat-lista-avaliacoes>
			</div>
			<div class="bit">
				<div>
					<pacto-log [url]="urlLog" class="pequeno"></pacto-log>
					<pacto-cat-button
						(click)="validarCampos()"
						i18n-label="@@perfil-aluno:avaliacoes:criar"
						id="criar-avaliacao"
						label="Criar avaliação"></pacto-cat-button>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #xinglingRemoverAvaliacao>
	<span xingling="titulo">Remover Avaliação Física</span>
	<span xingling="body">
		Tem certeza que deseja remover a avaliação física criada no dia
		{{ avaliacaoParaRemover?.dataAvaliacao | date : "dd/MM/yyyy HH:mm" }} ?
	</span>
	<span xingling="sucesso">Avaliação removida com sucesso.</span>
	<span xingling="erro">Erro ao remover avaliação.</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #composicaoLabels>
	<span i18n="@@perfil-aluno:avaliacoes:gordura" xingling="gordura">
		Gordura
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:residuos" xingling="residuos">
		Resíduos
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:musculos" xingling="musculos">
		Músculos
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:ossos" xingling="ossos">Ossos</span>
	<span i18n="@@perfil-aluno:avaliacoes:naoInformado" xingling="naoInformado">
		Não informado
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoPorcentualMassaMagra"
		xingling="composicaoPorcentualMassaMagra">
		%Massa Magra
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoPorcentualGordura"
		xingling="composicaoPorcentualGordura">
		%Massa Gorda
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoMassaMagra"
		xingling="composicaoMassaMagra">
		Massa Magra
	</span>
	<span
		i18n="@@perfil-aluno-modal:avaliacoes:composicaoMassaGorda"
		xingling="composicaoMassaGorda">
		Massa Gorda
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #imc>
	<span xingling="NENHUM">-</span>
	<span i18n="@@perfil-aluno:avaliacoes:BAIXO" xingling="BAIXO">
		Abaixo do peso normal
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:NORMAL" xingling="NORMAL">
		Peso normal
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:SOBREPESO" xingling="SOBREPESO">
		Sobrepeso
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:OBESIDADE_CLASSE_I"
		xingling="OBESIDADE_CLASSE_I">
		Obesidade grau I
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:OBESIDADE_CLASSE_II"
		xingling="OBESIDADE_CLASSE_II">
		Obesidade grau II
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:OBESIDADE_CLASSE_III"
		xingling="OBESIDADE_CLASSE_III">
		Obesidade grau III
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #composicao>
	<span i18n="@@perfil-aluno:avaliacoes:EXCELENTE" xingling="EXCELENTE">
		Excelente
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:BOM" xingling="BOM">Bom</span>
	<span i18n="@@perfil-aluno:avaliacoes:ACIMA_MEDIA" xingling="ACIMA_MEDIA">
		Acima da média
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:MEDIA" xingling="MEDIA">Média</span>
	<span i18n="@@perfil-aluno:avaliacoes:ABAIXO_MEDIA" xingling="ABAIXO_MEDIA">
		Abaixo da média
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:RUIM" xingling="RUIM">Ruim</span>
	<span i18n="@@perfil-aluno:avaliacoes:MUITO_RUIM" xingling="MUITO_RUIM">
		Muito ruim
	</span>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@perfil-aluno:avaliacoes:CAMPOS_OBRIGATORIOS"
		xingling="CAMPOS_OBRIGATORIOS">
		Data de nascimento e sexo são obrigatórios.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:VALIDAR_PRODUTO"
		xingling="VALIDAR_PRODUTO">
		O aluno não tem um produto de Avaliação Física vigente pago.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:PRODUTO_ULTILIZADO"
		xingling="PRODUTO_ULTILIZADO">
		O produto vigente já foi usado.
	</span>
</pacto-traducoes-xingling>
<pacto-traducoes-xingling #ocuAulasLabels>
	<span i18n="@@perfil-aluno:avaliacoes:gordura" xingling="gordura">
		Gordura
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:residuos" xingling="residuos">
		Resíduos
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:musculos" xingling="musculos">
		Músculos
	</span>
	<span i18n="@@perfil-aluno:avaliacoes:ossos" xingling="ossos">Ossos</span>
	<span i18n="@@perfil-aluno:avaliacoes:naoInformado" xingling="naoInformado">
		Não informado
	</span>
</pacto-traducoes-xingling>
