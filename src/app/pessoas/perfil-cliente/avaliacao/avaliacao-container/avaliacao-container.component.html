<div *ngIf="showMensagemAviso" class="content-aviso-medico">
	<div class="aviso-medico">
		<i class="pct pct-alert-triangle"></i>
		<span>{{ clienteMensagemAviso }}</span>
	</div>
</div>

<div *ngIf="!existeAvaliacao" class="div-empty">
	<div class="d-flex flex-column align-items-center">
		<img class="icon-empty" src="assets/images/empty-state-search.svg" />
		<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
			O aluno ainda não possui nenhum registro de avaliação física!
		</div>
		<pacto-cat-button
			(click)="criarAvaliacao()"
			i18n-label="@@perfil-aluno:avaliacoes:criar"
			id="criar-avaliacao"
			label="Preencher avaliação física"></pacto-cat-button>
	</div>
</div>

<div class="content-aux">
	<div *ngIf="existeAvaliacao" class="bb">
		<div class="box-right-1 ultima">
			<pacto-ultima-avaliacao
				(update)="updateHandler()"
				[matricula]="matricula"></pacto-ultima-avaliacao>
		</div>

		<div class="box-right-4 exercicios">
			<div class="resumo-grupos">
				<div class="top-row">
					<div class="title">Grupos musculares</div>
					<div *ngIf="tableGrupos.length > 0" class="desc-label">
						<span i18n="@@treino-container:treinos-executados">
							Treinos executados no período:
						</span>
						{{ evolucaoFisica?.treinosPeriodos }}
					</div>
				</div>
				<div *ngIf="tableGrupos.length > 0" class="select-grupos">
					<pacto-cat-select
						*ngIf="gruposItens.length"
						[control]="gruposFc"
						[items]="gruposItens"
						[size]="'SMALL'"></pacto-cat-select>
				</div>
			</div>

			<div *ngIf="tableGrupos.length > 0" class="table-grupos">
				<div class="display-flex">
					<div class="col-md-6 componente-corpo-grupo-muscular">
						<div class="frontal">
							<pacto-corpo-frontal-grupo-muscular
								*ngIf="allGruposMusculares"
								[listaAtiva]="gruposMuscularesSelecionados"
								[listaCompletaGrupoMuscular]="allGruposMusculares"
								[podeSelecionarGrupoMuscular]="
									false
								"></pacto-corpo-frontal-grupo-muscular>
						</div>

						<div class="posterior">
							<pacto-corpo-posterior-grupo-muscular
								*ngIf="allGruposMusculares"
								[listaAtiva]="gruposMuscularesSelecionados"
								[listaCompletaGrupoMuscular]="allGruposMusculares"
								[podeSelecionarGrupoMuscular]="
									false
								"></pacto-corpo-posterior-grupo-muscular>
						</div>
					</div>

					<div class="col-md-6">
						<div
							[maxHeight]="'380px'"
							class="tabela-scroll"
							pactoCatSmoothScroll>
							<table class="table">
								<div *ngFor="let valor of tableGrupos" class="tabela-grupos">
									<div class="colum colum-1">{{ valor.nome }}</div>
									<div class="colum colum-2">
										{{ valor.exercicios }} Exercicíos
									</div>
									<div class="colum colum-3">{{ valor.porcentagem }}%</div>
								</div>
							</table>
						</div>
					</div>
				</div>
			</div>

			<div *ngIf="tableGrupos.length === 0" class="div-empty">
				<div class="div-interna-empty">
					<img class="icon-empty" src="assets/images/halter_grupo.svg" />
					<div class="titulo-empty">
						Ops, falta informações no programa de treino
					</div>
					<div class="text-empty">
						Para que o gráfico seja gerado com precisão, é essencial especificar
						os músculos envolvidos nas atividades incluídas no programa de
						treino do cliente.
					</div>
				</div>
			</div>
		</div>

		<div class="box-right-2 perimetria">
			<div class="resumo-perimetria">
				<div class="top-row">
					<div class="title">Perimetria</div>
					<div *ngIf="perimetriaItens.length" class="desc-label">
						{{ traducoes.getLabel(perimetriaFc.value) }}
					</div>
				</div>
				<div *ngIf="perimetriaItens.length" class="select-perimetria">
					<pacto-cat-select
						[control]="perimetriaFc"
						[idKey]="'id'"
						[items]="perimetriaItens"
						[labelKey]="'label'"
						[size]="'SMALL'"></pacto-cat-select>
				</div>
			</div>
			<div *ngIf="series" class="center-row-perimetria">
				<pacto-cat-column-chart
					[colors]="chartColorsPerimetria"
					[height]="280"
					[series]="series"
					[tooltipFormatter]="tooltipFormatter"
					[xAxisLabels]="labelsPerimeria"
					[yAxisTitle]="'( Valores em centímetros )'"></pacto-cat-column-chart>
			</div>
			<div *ngIf="!series" class="div-empty">
				<div class="div-interna-empty">
					<img class="icon-empty" src="assets/images/perimetria.svg" />
					<div class="titulo-empty">Ops! Dados insuficientes.</div>
					<div class="text-empty">
						O gráfico será gerado apenas a partir da segunda avaliação física,
						pois com apenas uma avaliação não possuímos dados suficientes para
						gerar os gráficos de comparação.
					</div>
				</div>
			</div>
		</div>

		<div class="box-right-2 peso-x-massa">
			<div class="resumo-perimetria">
				<div class="top-row">
					<div class="title">Peso x Percentual De Gordura</div>
				</div>
			</div>
			<div *ngIf="seriesPeso" class="center-row-perimetria">
				<div class="chart-peso">
					<pacto-cat-column-chart
						[colors]="chartColorsPeso"
						[height]="280"
						[series]="seriesPeso"
						[tooltipFormatter]="tooltipFormatterKg"
						[xAxisLabels]="labelsPeso"
						[yAxisTitle]="'( Valores em kg )'"></pacto-cat-column-chart>
				</div>
			</div>
			<div *ngIf="!seriesPeso" class="div-empty">
				<div class="div-interna-empty">
					<img class="icon-empty" src="assets/images/balanca.svg" />
					<div class="titulo-empty">Ops! Dados insuficientes.</div>
					<div class="text-empty">
						O gráfico será gerado apenas a partir da segunda avaliação física,
						pois com apenas uma avaliação não possuímos dados suficientes para
						gerar os gráficos de comparação.
					</div>
				</div>
			</div>
		</div>

		<div class="box-right-3 dobras">
			<div class="resumo-dobras">
				<div class="top-row-dobras">
					<div class="title">Dobras cutâneas</div>
					<div *ngIf="dobrasItens.length" class="select-dobras">
						<pacto-cat-select
							[control]="dobrasFc"
							[items]="dobrasItens"
							[size]="'SMALL'"></pacto-cat-select>
					</div>
				</div>
				<div *ngIf="seriesDobras" class="center-row-dobras">
					<pacto-cat-line-chart
						[series]="seriesDobras"
						[tooltipFormatter]="tooltipFormatterMm"
						[xAxisLabels]="labelsDobras"
						[yAxisTitle]="'(Medidas em mm)'"></pacto-cat-line-chart>
				</div>
				<div *ngIf="!seriesDobras" class="div-empty">
					<div class="div-interna-empty">
						<img class="icon-empty" src="assets/images/dobras_cutaneas.svg" />
						<div class="titulo-empty">Ops! Dados insuficientes.</div>
						<div class="text-empty">
							O gráfico será gerado apenas a partir da segunda avaliação física,
							pois com apenas uma avaliação não possuímos dados suficientes para
							gerar os gráficos de comparação.
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span xingling="pescoco">Pescoço</span>
	<span xingling="ombro">Ombro</span>
	<span xingling="braco_esquerdo">Braço esquerdo</span>
	<span xingling="braco_direito">Braço direito</span>
	<span xingling="antebraco_direito">Antebraço Direito</span>
	<span xingling="antebraco_esquerdo">Antebraço esq.</span>
	<span xingling="toraxBusto">Torax busto</span>
	<span xingling="cintura">Cintura</span>
	<span xingling="circunferenciaAbdominal">Abdominal</span>
	<span xingling="quadril">Quadril</span>
	<span xingling="gluteo">Gluteo</span>
	<span xingling="coxa_proximal_direita">Coxa P/ direita</span>
	<span xingling="coxa_medial_direita">Coxa M/ direita</span>
	<span xingling="coxa_distal_direita">Coxa D/ direita</span>
	<span xingling="coxa_proximal_esquerda">Coxa P/ esquerda</span>
	<span xingling="coxa_medial_esquerda">Coxa M/ esquerda</span>
	<span xingling="coxa_distal_esquerda">Coxa D/ esquerda</span>
	<span xingling="panturrilha_direita">Panturrilha direita</span>
	<span xingling="panturrilha_esquerda">Panturrilha esq.</span>
	<span xingling="no_periodo">No período</span>
	<span xingling="no_programa">No programa atual</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:CAMPOS_OBRIGATORIOS"
		xingling="CAMPOS_OBRIGATORIOS">
		Data de nascimento e sexo são obrigatórios.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:VALIDAR_PRODUTO"
		xingling="VALIDAR_PRODUTO">
		O aluno não tem um produto de Avaliação Física vigente pago.
	</span>
	<span
		i18n="@@perfil-aluno:avaliacoes:PRODUTO_ULTILIZADO"
		xingling="PRODUTO_ULTILIZADO">
		O produto vigente já foi usado.
	</span>
</pacto-traducoes-xingling>
