import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ColumnChartSet, PactoColor } from "ui-kit";
import { FormControl } from "@angular/forms";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { formatDate } from "@angular/common";
import { locale } from "moment";
import {
	AlunoBase,
	EvolucaoFisica,
	TreinoApiAlunosService,
	TreinoApiAvaliacaoCatalogoService,
	TreinoApiPerfilAlunoService,
	TreinoApiGrupoMuscularService,
	GrupoMuscular,
} from "treino-api";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import { SessionService } from "@base-core/client/session.service";
import { UltimaAvaliacaoComponent } from "../ultima-avaliacao/ultima-avaliacao.component";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";

@Component({
	providers: [UltimaAvaliacaoComponent],
	selector: "pacto-avaliacao-container",
	templateUrl: "./avaliacao-container.component.html",
	styleUrls: ["./avaliacao-container.component.scss"],
})
export class AvaliacaoContainerComponent implements OnInit {
	matricula: string;
	evolucaoFisica: EvolucaoFisica;
	@ViewChild("traducoes", { static: true }) traducoes;
	labelsPerimeria: any = [];
	labelsDobras: any = [];
	labelsPeso: any = [];
	perimetriaItens: any[] = [];
	gruposItens: any[] = [];
	dobrasItens: any[] = [];
	tableGrupos: any = [];
	perimetriaFc = new FormControl("antebraco_direito");
	dobrasFc = new FormControl("Peitoral");
	gruposFc = new FormControl("programaAtual");
	ready = false;
	dadosPerimetria;
	chartColorsPerimetria: PactoColor[] = [PactoColor.BUBBALOO_PRI];
	chartColorsPeso: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.PEQUIZAO_PRI,
	];
	aluno: AlunoBase;
	existeAvaliacao = true;
	clienteMensagemAviso: string;
	showMensagemAviso = false;
	allGruposMusculares: Array<GrupoMuscular> = [];
	gruposMuscularesSelecionados: any[] = [];

	constructor(
		private cd: ChangeDetectorRef,
		private route: ActivatedRoute,
		private router: Router,
		private localStorageService: LocalStorageSessionService,
		private perfilAlunoService: TreinoApiPerfilAlunoService,
		private alunoService: TreinoApiAlunosService,
		private catalogoService: TreinoApiAvaliacaoCatalogoService,
		private sessionService: SessionService,
		private ultimaAvaliacaoComponent: UltimaAvaliacaoComponent,
		private treinoConfigService: TreinoConfigCacheService,
		private grupoMuscularService: TreinoApiGrupoMuscularService
	) {
		this.localStorageService.setLocalStorageItem("tabAluno", "avaliacao");
	}

	series: ColumnChartSet[];
	seriesPeso: ColumnChartSet[];
	seriesDobras: ColumnChartSet[];

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.obterEvolucaoFisica().subscribe(() => {
			this.ready = true;
			this.loadInitialData();
			this.setupSelect();
			this.buildFromControl();
			this.cd.detectChanges();
		});
		this.initDados();
	}

	initDados() {
		this.alunoService
			.obterAlunoCompletoPorMatricula(this.matricula)
			.subscribe((alunoResponse) => {
				this.aluno = alunoResponse;
				this.catalogoService
					.obterUltimaAvaliacaoAluno(this.aluno.id)
					.subscribe((response) => {
						this.existeAvaliacao = response !== undefined;
						this.cd.detectChanges();
					});
				this.getClienteMensagemAviso();
				this.carregarTodosGruposMusculares();
			});
	}

	carregarTodosGruposMusculares() {
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.allGruposMusculares = dados;
				this.cd.detectChanges();
			});
	}

	private obterEvolucaoFisica(): Observable<any> {
		return this.perfilAlunoService.evolucaoFisica(this.matricula).pipe(
			map((result) => {
				if (result !== "error") {
					this.evolucaoFisica = result;
					this.setupChartPerimetria();
					this.setupChartPeso();
					return true;
				} else {
					return false;
				}
			})
		);
	}

	buildFromControl() {
		this.perimetriaFc.valueChanges.subscribe(() => {
			this.setupChartPerimetria();
		});
		this.gruposFc.valueChanges.subscribe(() => {
			this.buildDataGrupos();
		});
		this.dobrasFc.valueChanges.subscribe(() => {
			this.setupChartDobras();
		});
	}

	private loadInitialData() {
		this.buildDataGrupos();
		this.setupChartDobras();
	}

	private setupChartPerimetria() {
		this.series = [];
		this.labelsPerimeria = [];
		const dia = [];
		this.dadosPerimetria =
			this.evolucaoFisica.perimetria[this.perimetriaFc.value];
		this.dadosPerimetria.valores.forEach((result) => {
			dia.push(result.valor);
			const data = new Date(result.data);
			this.labelsPerimeria.push(formatDate(data, "MM/yyyy", locale()));
		});
		this.series = [
			{ name: this.traducoes.getLabel(this.perimetriaFc.value), data: dia },
		];
	}

	private setupChartDobras() {
		let dadosDobras;
		this.labelsDobras = [];
		const dobras = [];
		if (this.dobrasFc) {
			dadosDobras = this.evolucaoFisica.dobras.find((i) => {
				return i.nome === this.dobrasFc.value;
			});
		}
		dadosDobras.pontos.forEach((result) => {
			dobras.push(result.valor);
			const data = new Date(result.data);
			this.labelsDobras.push(formatDate(data, "MM/yyyy", locale()));
		});
		this.seriesDobras = [{ name: dadosDobras.nome, data: dobras }];
	}

	buildDataGrupos() {
		if (this.evolucaoFisica) {
			this.tableGrupos =
				this.evolucaoFisica.gruposTrabalhados[this.gruposFc.value];
			this.buildGruposMuscularesAtivos();
		}
	}

	buildGruposMuscularesAtivos() {
		this.gruposMuscularesSelecionados = [];
		this.tableGrupos.forEach((item) => {
			this.gruposMuscularesSelecionados.push({
				id: 0,
				nome: item.nome,
			});
		});
		this.cd.detectChanges();
	}

	private setupChartPeso() {
		this.labelsPeso = [];
		const peso = [];
		const gordura = [];
		this.evolucaoFisica.proporcaoPesoGordura.forEach((result) => {
			peso.push(result.peso);
			gordura.push(result.massaGorda);
			const data = new Date(result.data);
			this.labelsPeso.push(formatDate(data, "MM/yyyy", locale()));
		});
		this.seriesPeso = [
			{ name: "Peso", data: peso },
			{ name: "Gordura", data: gordura },
		];
	}

	setupSelect() {
		// tslint:disable-next-line:forin
		for (const perimetriaKey in this.evolucaoFisica.perimetria) {
			this.perimetriaItens.push({
				id: perimetriaKey,
				label: this.traducoes.getLabel(perimetriaKey),
			});
		}
		this.evolucaoFisica.dobras.forEach((dobra) => {
			this.dobrasItens.push({
				id: dobra.nome,
				label: dobra.nome,
			});
		});
		this.gruposItens.push(
			{
				id: "durantePeriodo",
				label: this.traducoes.getLabel("no_periodo"),
			},
			{
				id: "programaAtual",
				label: this.traducoes.getLabel("no_programa"),
			}
		);
	}

	tooltipFormatter() {
		return (val) => {
			return `${val} cm`;
		};
	}

	tooltipFormatterKg() {
		return (val) => {
			return `${val} kg`;
		};
	}

	tooltipFormatterMm() {
		return (val) => {
			return `${val} mm`;
		};
	}

	public criarAvaliacao(): void {
		this.ultimaAvaliacaoComponent.traducoes = this.traducoes;
		this.ultimaAvaliacaoComponent.criarPrimeiraAvaliacao(
			this.matricula,
			this.aluno
		);
	}

	updateHandler() {
		this.initDados();
		this.cd.detectChanges();
	}

	getClienteMensagemAviso() {
		this.alunoService
			.obterClienteMensagem(this.aluno.id, "AM")
			.subscribe((result) => {
				this.clienteMensagemAviso = result.toString().replace(/&nbsp;/g, " ");
				if (this.clienteMensagemAviso !== "") {
					this.validarConfigVisualizarMensagemAviso();
				}
				this.cd.detectChanges();
			});
	}

	validarConfigVisualizarMensagemAviso() {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino.visualizar_mensagem_aviso
		) {
			this.showMensagemAviso = true;
		} else {
			this.showMensagemAviso = false;
		}
	}
}
