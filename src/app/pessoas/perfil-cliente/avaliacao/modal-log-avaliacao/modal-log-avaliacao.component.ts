import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-log-avaliacao",
	templateUrl: "./modal-log-avaliacao.component.html",
	styleUrls: ["./modal-log-avaliacao.component.scss"],
})
export class ModalLogAvaliacaoComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	table: PactoDataGridConfig;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				// endpointUrl: this.admRest.buildFullUrlAcessoSistema('integracaoAcesso'),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: false,
				columns: [
					{
						nome: "operacao",
						titulo: "Operação",
						visible: true,
						ordenavel: true,
					},
					{
						nome: "usuario",
						titulo: "Usuário",
						visible: true,
						ordenavel: true,
					},
					{ nome: "data", titulo: "Data", visible: true, ordenavel: true },
					{ nome: "codigo", titulo: "Código", visible: true, ordenavel: true },
					{
						nome: "descricao",
						titulo: "Descrição",
						visible: true,
						ordenavel: true,
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
