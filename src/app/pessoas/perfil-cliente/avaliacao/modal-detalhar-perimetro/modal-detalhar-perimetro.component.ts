import { Component, Input, OnInit } from "@angular/core";
declare var moment;
@Component({
	selector: "pacto-modal-detalhar-perimetro",
	templateUrl: "./modal-detalhar-perimetro.component.html",
	styleUrls: ["./modal-detalhar-perimetro.component.scss"],
})
export class ModalDetalharPerimetroComponent implements OnInit {
	constructor() {}
	chartPerimetroDetalhado: ApexCharts.ApexOptions;
	@Input() entries;
	ngOnInit() {
		let sortedEntries = this.entries.sort((a, b) =>
			new Date(a.data) > new Date(b.data) ? 1 : -1
		);
		this.chartPerimetroDetalhado = {
			series: [
				{
					name: "Perímetro",
					data: sortedEntries.map((v) => v.valor),
					color: "#FF2970",
				},
			],
			chart: {
				toolbar: { show: false },
				type: "bar",
				height: 350,
			},
			plotOptions: {
				bar: {
					horizontal: false,
					columnWidth: "30%",
				},
			},
			dataLabels: {
				enabled: false,
			},
			stroke: {
				show: true,
				width: 2,
				colors: ["transparent"],
			},
			xaxis: {
				categories: sortedEntries.map((v) =>
					moment(v.data).format("DD/MM/YYYY")
				),
			},
			tooltip: { enabled: false },
			yaxis: {
				title: {
					rotate: 90,
					text: "cm",
				},
			},
			fill: {
				opacity: 1,
			},
		};
	}
}
