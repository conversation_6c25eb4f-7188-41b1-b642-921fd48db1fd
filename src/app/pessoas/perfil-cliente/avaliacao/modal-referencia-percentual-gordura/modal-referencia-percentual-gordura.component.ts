import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-referencia-percentual-gordura",
	templateUrl: "./modal-referencia-percentual-gordura.component.html",
	styleUrls: ["./modal-referencia-percentual-gordura.component.scss"],
})
export class ModalReferenciaPercentualGorduraComponent
	implements OnInit, OnChanges
{
	@ViewChild("table", { static: false }) table: RelatorioComponent;
	tableData: PactoDataGridConfig;

	constructor(private cd: ChangeDetectorRef) {}

	@Input() sexoAluno: string = "M";
	contentMasc = [
		{
			nivel: "Excelente",
			zeroADezoito: "-",
			dezoitoAVinteCinco: "Até 8%",
			vinteSeisATrintaCinco: "Até 12%",
			trintaSeisAQuarentaCinco: "Até 16%",
			quarentaSeisACinquentaCinco: "Até 18%",
			cinquentaSeisASessentaCinco: "Até 20%",
		},
		{
			nivel: "Bom",
			zeroADezoito: "Até 6%",
			dezoitoAVinteCinco: "8% - 12%",
			vinteSeisATrintaCinco: "12% - 16%",
			trintaSeisAQuarentaCinco: "16% - 19%",
			quarentaSeisACinquentaCinco: "18% - 21%",
			cinquentaSeisASessentaCinco: "20% - 22%",
		},
		{
			nivel: "Acima da Média",
			zeroADezoito: "6% - 10%",
			dezoitoAVinteCinco: "12% - 14%",
			vinteSeisATrintaCinco: "16% - 19%",
			trintaSeisAQuarentaCinco: "19% - 22%",
			quarentaSeisACinquentaCinco: "21% - 24%",
			cinquentaSeisASessentaCinco: "22% - 24%",
		},
		{
			nivel: "Média",
			zeroADezoito: "10% - 20%",
			dezoitoAVinteCinco: "14% - 17%",
			vinteSeisATrintaCinco: "18% - 22%",
			trintaSeisAQuarentaCinco: "22% - 24%",
			quarentaSeisACinquentaCinco: "24% - 26%",
			cinquentaSeisASessentaCinco: "24% - 26%",
		},
		{
			nivel: "Abaixo da Média",
			zeroADezoito: "20% - 25%",
			dezoitoAVinteCinco: "17% - 21%",
			vinteSeisATrintaCinco: "22% - 25%",
			trintaSeisAQuarentaCinco: "24% - 27%",
			quarentaSeisACinquentaCinco: "26% - 28%",
			cinquentaSeisASessentaCinco: "26% - 28%",
		},
		{
			nivel: "Ruim",
			zeroADezoito: "25% - 31%",
			dezoitoAVinteCinco: "21% - 26%",
			vinteSeisATrintaCinco: "25% - 28%",
			trintaSeisAQuarentaCinco: "27% - 30%",
			quarentaSeisACinquentaCinco: "28% - 32%",
			cinquentaSeisASessentaCinco: "28% - 32%",
		},
		{
			nivel: "Muito Ruim",
			zeroADezoito: "Maior que 31%",
			dezoitoAVinteCinco: "Acima de 26%",
			vinteSeisATrintaCinco: "Acima de 28%",
			trintaSeisAQuarentaCinco: "Acima de 30%",
			quarentaSeisACinquentaCinco: "Acima de 32%",
			cinquentaSeisASessentaCinco: "Acima de 32%",
		},
	];
	contentFem = [
		{
			nivel: "Excelente",
			zeroADezoito: "-",
			dezoitoAVinteCinco: "Até 17%",
			vinteSeisATrintaCinco: "Até 18%",
			trintaSeisAQuarentaCinco: "Até 20%",
			quarentaSeisACinquentaCinco: "Até 23%",
			cinquentaSeisASessentaCinco: "Até 24%",
		},
		{
			nivel: "Bom",
			zeroADezoito: "Até 12%",
			dezoitoAVinteCinco: "17% - 20%",
			vinteSeisATrintaCinco: "18% - 21%",
			trintaSeisAQuarentaCinco: "20% - 24%",
			quarentaSeisACinquentaCinco: "23% - 26%",
			cinquentaSeisASessentaCinco: "24% - 27%",
		},
		{
			nivel: "Acima da Média",
			zeroADezoito: "12% - 15%",
			dezoitoAVinteCinco: "20% - 23%",
			vinteSeisATrintaCinco: "21% - 24%",
			trintaSeisAQuarentaCinco: "24% - 27%",
			quarentaSeisACinquentaCinco: "26% - 29%",
			cinquentaSeisASessentaCinco: "27% - 30%",
		},
		{
			nivel: "Média",
			zeroADezoito: "15% - 25%",
			dezoitoAVinteCinco: "23% - 26%",
			vinteSeisATrintaCinco: "24% - 27%",
			trintaSeisAQuarentaCinco: "27% - 30%",
			quarentaSeisACinquentaCinco: "29% - 32%",
			cinquentaSeisASessentaCinco: "30% - 33%",
		},
		{
			nivel: "Abaixo da Média",
			zeroADezoito: "25% - 30%",
			dezoitoAVinteCinco: "26% - 29%",
			vinteSeisATrintaCinco: "27% - 31%",
			trintaSeisAQuarentaCinco: "30% - 33%",
			quarentaSeisACinquentaCinco: "32% - 35%",
			cinquentaSeisASessentaCinco: "33% - 36%",
		},
		{
			nivel: "Ruim",
			zeroADezoito: "30% - 36%",
			dezoitoAVinteCinco: "29% - 32%",
			vinteSeisATrintaCinco: "31% - 36%",
			trintaSeisAQuarentaCinco: "33% - 38%",
			quarentaSeisACinquentaCinco: "35% - 39%",
			cinquentaSeisASessentaCinco: "36% - 39%",
		},
		{
			nivel: "Muito Ruim",
			zeroADezoito: "Maior que 36%",
			dezoitoAVinteCinco: "Acima de 33%",
			vinteSeisATrintaCinco: "Acima de 36%",
			trintaSeisAQuarentaCinco: "Acima de 38%",
			quarentaSeisACinquentaCinco: "Acima de 39%",
			cinquentaSeisASessentaCinco: "Acima de 39%",
		},
	];

	ngOnInit() {
		this.initTable();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes["sexoAluno"] && changes["sexoAluno"].currentValue) {
			this.initTable();
		}
	}

	private initTable() {
		const dadosTabela =
			this.sexoAluno.toLowerCase() === "f" ? this.contentFem : this.contentMasc;

		this.tableData = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				if (!serverData) {
					return { content: [] };
				}

				serverData.content = dadosTabela;

				return serverData;
			},
			exportButton: false,
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			pagination: false,
			showFilters: false,
			rowClick: false,
			columns: [
				{
					nome: "nivel",
					titulo: "Nível",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "zeroADezoito",
					titulo: "0 - 17 anos",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dezoitoAVinteCinco",
					titulo: "18 - 25 anos",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "vinteSeisATrintaCinco",
					titulo: "26 - 35 anos",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "trintaSeisAQuarentaCinco",
					titulo: "36 - 45 anos",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "quarentaSeisACinquentaCinco",
					titulo: "46 - 55 anos",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "cinquentaSeisASessentaCinco",
					titulo: "56 - 65 anos",
					visible: true,
					ordenavel: false,
				},
			],
		});

		this.cd.detectChanges();
	}
}
