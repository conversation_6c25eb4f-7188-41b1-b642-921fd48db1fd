import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-referencia-imc",
	templateUrl: "./modal-referencia-imc.component.html",
	styleUrls: ["./modal-referencia-imc.component.scss"],
})
export class ModalReferenciaImcComponent implements OnInit {
	@ViewChild("table", { static: false }) table: RelatorioComponent;
	tableData: PactoDataGridConfig;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.initTable();
	}

	private initTable() {
		setTimeout(() => {
			this.tableData = new PactoDataGridConfig({
				dataAdapterFn(serverData) {
					serverData.content = [
						{
							valorImc: "Igual a 0",
							classificacao: "Nenhum",
						},
						{
							valorImc: "Menor que 18.5",
							classificacao: "Abaixo do peso normal",
						},
						{
							valorImc: "Entre 18.5 e 25",
							classificacao: "Peso normal",
						},
						{
							valorImc: "Entre 25 e 30",
							classificacao: "Sobrepeso",
						},
						{
							valorImc: "Entre 30 e 35",
							classificacao: "Obesidade grau I",
						},
						{
							valorImc: "Entre 35 e 40",
							classificacao: "Obesidade grau II",
						},
						{
							valorImc: "Maior que 40",
							classificacao: "Obesidade grau III",
						},
					];
					return serverData;
				},
				exportButton: false,
				quickSearch: false,
				ghostLoad: true,
				ghostAmount: 5,
				pagination: false,
				showFilters: false,
				rowClick: false,
				columns: [
					{
						nome: "valorImc",
						titulo: "Valor IMC",
						visible: true,
						ordenavel: false,
						width: "60%",
					},
					{
						nome: "classificacao",
						titulo: "Classificação",
						visible: true,
						ordenavel: false,
						width: "40%",
					},
				],
			});
			this.cd.detectChanges();
		});
	}
}
