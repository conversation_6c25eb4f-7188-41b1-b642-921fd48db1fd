@import "src/assets/scss/pacto/plataforma-import.scss";

// :host {
//   padding: 30px;
// }

// .ref-title {
//   @extend .type-h4;
//   color: $pretoPri;
//   margin-bottom: 20px;
// }

// .ref-footer {
//   display: flex;
//   margin-top: 15px;
//   flex-direction: row-reverse;
// }

// table {
//   width: 100%;
//   line-height: 2.5em;

//   td, th {
//     padding: 0px 5px;
//     @extend .type-h6;
//   }

//   th {
//     color: $pretoPri;
//   }

//   tbody tr:nth-child(2n+1) {
//     background-color: $cinzaClaroPri;
//   }
// }

::ng-deep {
	.table-imc {
		.table-content {
			padding: 16px;
		}
	}
}
