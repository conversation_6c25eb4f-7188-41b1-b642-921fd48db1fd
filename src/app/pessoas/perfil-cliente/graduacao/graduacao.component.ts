import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	ElementRef,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { Api } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { CriarComentarioModalComponent } from "src/app/graduacao/components/criar-comentario-modal/criar-comentario-modal.component";
import { ComentarioAlunoService } from "src/app/microservices/graduacao/comentario-aluno/comentario-aluno.service";
import { FichaService } from "src/app/microservices/graduacao/ficha/ficha.service";
import { NivelService } from "src/app/microservices/graduacao/nivel/nivel.service";
import { AlunoBase, TreinoApiAlunosService } from "treino-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

declare const moment;

interface Nivel {
	cor: string;
	nome: string;
	uri: string;
	inclusaoNoNivel: {
		data: string;
		duracao: string;
	};
	totalDeAulas: number;
	aulasRestantes: number;
}

interface Ficha {
	id: number;
	nome: string;
	niveis: number;
	nivelAtual: Nivel;
	proximoNivel: Nivel;
}

@Component({
	selector: "pacto-graduacao",
	templateUrl: "./graduacao.component.html",
	styleUrls: ["./graduacao.component.scss"],
})
export class GraduacaoComponent implements OnInit {
	@ViewChild("avaliacaoColumnNone", { static: true })
	private readonly avaliacaoColumnNone: TemplateRef<Element>;

	@ViewChild("avaliacaoColumnFinalizada", { static: true })
	private readonly avaliacaoColumnFinalizada: TemplateRef<Element>;

	@ViewChild("avaliacaoColumnFichaTecnica", { static: true })
	private readonly avaliacaoColumnFichaTecnica: TemplateRef<Element>;

	@ViewChild("avaliacaoColumnAvaliador", { static: true })
	private readonly avaliacaoColumnAvaliador: TemplateRef<Element>;

	@ViewChild("avaliacaoColumnResultado", { static: true })
	private readonly avaliacaoColumnResultado: TemplateRef<Element>;

	@ViewChild("relatorioAvaliacoesConcluidas", { static: false })
	private readonly relatorioAvaliacoesConcluidas: RelatorioComponent;

	@ViewChild("relatorioComentarios", { static: false })
	private readonly relatorioComentarios: RelatorioComponent;

	@ViewChild("comentarioColumnNone", { static: true })
	private readonly comentarioColumnNone: TemplateRef<Element>;

	@ViewChild("comentarioColumnIncluido", { static: true })
	private readonly comentarioColumnIncluido: TemplateRef<Element>;

	@ViewChild("comentarioColumnFichaTecnica", { static: true })
	private readonly comentarioColumnFichaTecnica: TemplateRef<Element>;

	@ViewChild("tooltipRemover", { static: true })
	private readonly tooltipRemover: ElementRef<HTMLSpanElement>;

	@ViewChild("removerTitulo", { static: true })
	private readonly removerTitulo: ElementRef<HTMLSpanElement>;

	@ViewChild("removerBody", { static: true })
	private readonly removerBody: ElementRef<HTMLSpanElement>;

	@ViewChild("erroRemover", { static: true })
	private readonly erroRemover: ElementRef<HTMLSpanElement>;

	@ViewChild("sucessoRemover", { static: true })
	private readonly sucessoRemover: ElementRef<HTMLSpanElement>;

	private matricula: string;

	private fichasDeAluno: Ficha[];

	public fichaSelecionada: Ficha;

	public listaDeEscolha: Array<{ id: number; nome: string }> = [];

	private aluno: AlunoBase;

	public temGraduacao = false;

	public nomeComentarioARemover;

	public tableAvaliacoesConcluidas: PactoDataGridConfig;

	public tableComentarios: PactoDataGridConfig;

	public nomeDaFichaFC: FormControl = new FormControl();

	dataAvaliacao = false;
	dataComentario = false;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly gradFichaService: FichaService,
		private readonly session: SessionService,
		private readonly alunoService: TreinoApiAlunosService,
		private readonly nivelService: NivelService,
		private readonly rest: RestService,
		private readonly router: Router,
		private readonly modal: ModalService,
		private readonly snotifyService: SnotifyService,
		private readonly comentarioService: ComentarioAlunoService,
		private readonly cd: ChangeDetectorRef
	) {}

	public ngOnInit(): void {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.alunoService
			.obterAlunoCompletoPorMatricula(this.matricula)
			.subscribe((result) => {
				this.aluno = result;
				this.carregarDados();
			});

		this.configTableAvaliacoesConcluidas();
		this.configTableComentarios();

		this.nomeDaFichaFC.valueChanges.subscribe((res) => {
			if (!res || !this.fichasDeAluno || this.fichasDeAluno.length === 0) {
				return;
			}
			this.fichaSelecionada = this.fichasDeAluno.find((ficha) => {
				return ficha.id === Number(res);
			});

			this.relatorioAvaliacoesConcluidas.table.endpointUrl =
				this.rest.buildFullUrl(
					`avaliacoes-progresso/${this.aluno.codigoCliente}/obter-avaliacoes-concluidas-por-aluno?fichaId=${res}`,
					true,
					Api.MSGRAD
				);
			this.relatorioComentarios.table.endpointUrl = this.rest.buildFullUrl(
				`comentarios/${this.aluno.codigoCliente}/obter-comentarios-aluno?fichaId=${res}`,
				true,
				Api.MSGRAD
			);

			this.relatorioAvaliacoesConcluidas.reloadData();
			this.relatorioComentarios.reloadData();

			this.cd.detectChanges();
		});
	}

	private carregarDados(): void {
		this.gradFichaService
			.obterFichasDeAluno(this.aluno.codigoCliente, this.session.chave)
			.subscribe(
				async (res) => {
					if (res.content) {
						this.fichasDeAluno = res.content.map((item) => {
							const niveis = item.ficha.niveis.map((value) => {
								return {
									id: value.id,
									cor: value.cor,
									nome: value.nome,
									uri: value.fotoUri,
									inclusaoNoNivel: {
										data: new Date(item.dataIngresso).toLocaleDateString(
											"pt-BR",
											{
												year: "numeric",
												month: "long",
												day: "numeric",
											}
										),
										duracao: this.duration(item.dataIngresso),
									},
									totalDeAulas: value.quantidadeMinimaAulas,
									aulasRestantes: null,
									alunoIds: value.alunoIds,
								};
							});
							let posicaoNivelAtual = 0;
							if (
								(niveis !== null || niveis !== undefined) &&
								niveis.length > 0
							) {
								niveis.forEach((nv, index) => {
									nv.alunoIds.forEach((a) => {
										if (a === this.aluno.codigoCliente) {
											posicaoNivelAtual = index;
										}
									});
								});
							}
							const ficha = {
								id: item.ficha.id,
								nome: item.ficha.nome,
								niveis: item.ficha.niveis.length,
								nivelAtual: niveis[posicaoNivelAtual],
								proximoNivel:
									(niveis !== null || niveis !== undefined) && niveis.length > 1
										? niveis[posicaoNivelAtual + 1]
										: niveis[posicaoNivelAtual],
							};

							this.obterNumeroDeAulasRestantes(item.nivelId, ficha.nivelAtual);

							return ficha;
						});

						this.temGraduacao = this.fichasDeAluno.length > 0;

						this.listaDeEscolha = res.content.map((item) => {
							return { id: item.ficha.id, nome: item.ficha.nome };
						});
						this.cd.detectChanges();

						this.nomeDaFichaFC.setValue(this.listaDeEscolha[0].id);
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					if (error && error.meta) {
						this.snotifyService.error(error.meta.message);
					}
				}
			);
	}

	public obterNumeroDeAulasRestantes(
		id: number,
		nivel: { totalDeAulas: number; aulasRestantes: number }
	) {
		this.nivelService
			.obtertotalAulaNivel(this.aluno.codigoCliente, Number(this.matricula), id)
			.subscribe(
				(res) => {
					if (res !== undefined) {
						nivel.aulasRestantes = nivel.totalDeAulas - Number(res);
					}
				},
				({ error }) => console.error(error.meta.message),
				() => this.cd.detectChanges()
			);
	}

	public duration(data): string {
		const duration = data - new Date().valueOf();
		const durationObject = moment.duration(duration);
		return durationObject.locale("pt-br").humanize(true);
	}

	public abrirAvaliacao(avaliacao) {
		this.router.navigate(["/graduacao/avaliacoes-progresso", avaliacao.id]);
	}

	public abrirComentario(comentario) {
		const ref = this.modal.open("Comentário", CriarComentarioModalComponent);
		const modal: CriarComentarioModalComponent = ref.componentInstance;
		modal.alunoId = this.aluno.codigoCliente;
		modal.fichaId = comentario.fichaId;
		modal.commentId = comentario.id;
		modal.fg.setValue({
			nome: comentario.nome,
			descricao: comentario.descricao,
			data: comentario.data,
		});
		ref.result.then(
			() => {},
			() => {}
		);
	}

	public novoComentario() {
		const ref = this.modal.open("Comentário", CriarComentarioModalComponent);
		const modal: CriarComentarioModalComponent = ref.componentInstance;
		modal.alunoId = this.aluno.codigoCliente;
		modal.fichaId = null;
		modal.listFichas = this.listaDeEscolha;
		ref.result.then(
			(comment) => {
				this.fetchDataComentarios();
				this.cd.detectChanges();
			},
			() => {}
		);
	}

	public actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "remove") {
			this.removerComentarioHandler($event.row);
		}
	}

	private removerComentarioHandler(comentario): void {
		this.nomeComentarioARemover = comentario.nome;
		setTimeout(() => {
			const title = this.removerTitulo.nativeElement.innerText;
			const body = this.removerBody.nativeElement.innerText;
			const mensagemError = this.erroRemover.nativeElement.innerText;
			const sucesso = this.sucessoRemover.nativeElement.innerText;

			const appModal = this.modal.confirm(title, body);
			appModal.result
				.then(() => {
					this.comentarioService
						.removerComentario(comentario.id)
						.subscribe((result) => {
							if (result === "registro_esta_sendo_usado") {
								this.snotifyService.error(mensagemError);
							} else {
								this.snotifyService.success(sucesso);
								this.fetchDataComentarios();
							}
						});
				})
				.catch(() => {});
		});
	}

	private fetchDataComentarios(): void {
		this.relatorioComentarios.reloadData();
	}

	private configTableAvaliacoesConcluidas(): void {
		this.tableAvaliacoesConcluidas = new PactoDataGridConfig({
			quickSearch: false,
			dataAdapterFn: (serveData) => {
				this.dataAvaliacao = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "nomeNivel",
					titulo: this.avaliacaoColumnNone,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "finalizadaEm",
					titulo: this.avaliacaoColumnFinalizada,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v ? new Date(v).toLocaleDateString("pt-BR") : "-";
					},
				},
				{
					nome: "nomeFicha",
					titulo: this.avaliacaoColumnFichaTecnica,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "nomeAvaliador",
					titulo: this.avaliacaoColumnAvaliador,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "resultado",
					titulo: this.avaliacaoColumnResultado,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v || "-";
					},
				},
			],
		});
	}

	private configTableComentarios(): void {
		this.tableComentarios = new PactoDataGridConfig({
			quickSearch: false,
			dataAdapterFn: (serveData) => {
				this.dataComentario = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "nome",
					titulo: this.comentarioColumnNone,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "data",
					titulo: this.comentarioColumnIncluido,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => {
						return v ? new Date(v).toLocaleDateString("pt-BR") : "-";
					},
				},
				{
					nome: "fichaNome",
					titulo: this.comentarioColumnFichaTecnica,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v || "-";
					},
				},
			],
			actions: [
				{
					nome: "remove",
					iconClass: "fa fa-trash-o",
					tooltipText: this.tooltipRemover.nativeElement.innerText,
				},
			],
		});
	}
}
