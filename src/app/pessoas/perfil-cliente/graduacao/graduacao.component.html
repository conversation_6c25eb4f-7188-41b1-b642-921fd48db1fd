<pacto-cat-card-plain *ngIf="!temGraduacao">
	<div class="d-flex flex-column align-items-center">
		<img class="icon-empty" src="assets/images/empty-state-podium.svg" />
		<div class="text-empty mt-2 body-text-empty">
			O aluno ainda não possui nenhum registro de graduação!
		</div>
	</div>
</pacto-cat-card-plain>

<div *ngIf="temGraduacao" class="card">
	<pacto-cat-card-plain>
		<header>
			<h1>Ficha técnica</h1>
		</header>

		<div class="escolha-da-ficha">
			<pacto-cat-select
				[control]="nomeDaFichaFC"
				[disabled]="listaDeEscolha.length < 2"
				[items]="listaDeEscolha"
				id="grd-select-ficha"
				idKey="id"
				label="Nome da ficha"
				labelKey="nome"></pacto-cat-select>
		</div>

		<hr *ngIf="fichaSelecionada" />

		<div *ngIf="fichaSelecionada" class="ficha">
			<div class="nome">
				<h6>{{ fichaSelecionada.nome }}</h6>
				<span>{{ fichaSelecionada.niveis }} níveis nesta ficha</span>
			</div>
			<div class="divisao"></div>
			<div class="nivel-atual">
				<!-- <div class="cor" [style.background-color]="fichaSelecionada.nivelAtual.cor"></div> -->
				<pacto-cat-person-avatar
					[diameter]="40"
					[uri]="fichaSelecionada.nivelAtual.uri"></pacto-cat-person-avatar>
				<div class="detalhe">
					<h6>{{ fichaSelecionada.nivelAtual.nome }}</h6>
					<span>Nível atual</span>
				</div>
			</div>
			<div class="divisao"></div>
			<div class="detalhes">
				<span class="data-de-inclusao">
					Inclusão no nível:
					{{ fichaSelecionada.nivelAtual.inclusaoNoNivel.data }} ({{
						fichaSelecionada.nivelAtual.inclusaoNoNivel.duracao
					}})
				</span>
				<span class="aulas-restantes">
					Aulas restantes no nível:
					{{ fichaSelecionada.nivelAtual.aulasRestantes || "-" }}
				</span>
			</div>
			<div *ngIf="fichaSelecionada.proximoNivel" class="divisao"></div>
			<div *ngIf="fichaSelecionada.proximoNivel" class="proximo-nivel">
				<!-- <div class="cor" [style.background-color]="fichaSelecionada.proximoNivel.cor"></div> -->
				<pacto-cat-person-avatar
					[diameter]="40"
					[uri]="fichaSelecionada.proximoNivel.uri"></pacto-cat-person-avatar>
				<div class="detalhe">
					<h6>{{ fichaSelecionada.proximoNivel.nome }}</h6>
					<span>Próximo nível</span>
				</div>
			</div>
			<div *ngIf="fichaSelecionada.proximoNivel" class="aulas-restantes">
				<span>
					Aulas restantes no nível:
					{{ fichaSelecionada.proximoNivel.totalDeAulas }}
				</span>
			</div>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain>
		<div [hidden]="dataAvaliacao" class="div-empty">
			<div class="title-text-empty">Avaliações concluídas</div>
			<div class="d-flex flex-column align-items-center">
				<img
					class="icon-empty"
					src="assets/images/empty-state-assessment-complet.svg" />
				<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
					O aluno ainda não possui nenhuma avaliação concluída!
				</div>
			</div>
		</div>

		<div [hidden]="!dataAvaliacao" class="tabela-de-avaliacoes-concluidas">
			<pacto-relatorio
				#relatorioAvaliacoesConcluidas
				(rowClick)="abrirAvaliacao($event)"
				[table]="tableAvaliacoesConcluidas"
				idSuffix="grd-tbl-avaliacoes-concluidas"
				tableTitle="Avaliações concluídas"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain>
		<div [hidden]="dataComentario" class="div-empty">
			<div class="title-text-empty">Comentários</div>
			<div class="d-flex flex-column align-items-center">
				<img class="icon-empty" src="assets/images/empty-state-comments.svg" />
				<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
					Ainda não foi registrado nenhum comentário para esse aluno!
				</div>
				<pacto-cat-button
					(click)="novoComentario()"
					*ngIf="temGraduacao"
					[id]="'addComentario'"
					[v2]="true"
					label="Adicionar um novo comentário"></pacto-cat-button>
			</div>
		</div>

		<div [hidden]="!dataComentario" class="tabela-de-comentarios">
			<pacto-relatorio
				#relatorioComentarios
				(btnAddClick)="novoComentario()"
				(iconClick)="actionClickHandler($event)"
				(rowClick)="abrirComentario($event)"
				[actionTitulo]="'Ações'"
				[labelBtnAdd]="'Novo Comentário'"
				[showBtnAdd]="true"
				[table]="tableComentarios"
				idSuffix="grd-tbl-comentario"
				tableTitle="Comentários"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>
</div>

<!--Columns avaliações concluídas-->
<ng-template #avaliacaoColumnNone>
	<ng-container i18n="@@crud-alunos:nome:title:table">Nome</ng-container>
</ng-template>

<ng-template #avaliacaoColumnFinalizada>
	<ng-container i18n="@@crud-alunos:finalizada:title:table">
		Finalizada em
	</ng-container>
</ng-template>

<ng-template #avaliacaoColumnFichaTecnica>
	<ng-container i18n="@@crud-alunos:ficha-tecnica:title:table">
		Ficha técnica
	</ng-container>
</ng-template>

<ng-template #avaliacaoColumnResultado>
	<ng-container i18n="@@crud-alunos:ficha-tecnica:title:table">
		Resultado
	</ng-container>
</ng-template>

<ng-template #avaliacaoColumnAvaliador>
	<ng-container i18n="@@crud-alunos:avaliador:title:table">
		Avaliador
	</ng-container>
</ng-template>

<!--Columns comentários-->
<ng-template #comentarioColumnNone>
	<ng-container i18n="@@crud-alunos:nome:title:table">Nome</ng-container>
</ng-template>

<ng-template #comentarioColumnIncluido>
	<ng-container i18n="@@crud-alunos:incluido-em:title:table">
		Incluído em
	</ng-container>
</ng-template>

<ng-template #comentarioColumnFichaTecnica>
	<ng-container i18n="@@crud-alunos:ficha-tecnica:title:table">
		Ficha tecnica
	</ng-container>
</ng-template>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>
<span
	#removerTitulo
	[hidden]="true"
	i18n="@@perfil-aluno-graduacao:remover-comentario:title">
	Remover comentário ?
</span>
<span
	#sucessoRemover
	[hidden]="true"
	i18n="@@perfil-aluno-graduacao:remover-comentario:mensagem-success">
	Comentário removido com sucesso.
</span>
<span
	#erroRemover
	[hidden]="true"
	i18n="@@perfil-aluno-graduacao:remover-comentario:mensagem-error">
	Não é possível remover esse comentário.
</span>
<span
	#removerBody
	[hidden]="true"
	i18n="@@perfil-aluno-graduacao:remover-comentario:body">
	Tem certeza que deseja remover o comentário: {{ nomeComentarioARemover }} ?
</span>
