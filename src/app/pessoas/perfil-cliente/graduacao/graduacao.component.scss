.card {
	display: flex;
	padding: 1rem;
	gap: 1.5rem;
	background: none;

	&.sem-graduacao {
		height: 15rem;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	header {
		display: flex;
		justify-content: space-between;

		h1,
		h3 {
			color: #51555a;
			font-family: Poppins;
			font-weight: 600;
		}

		h1 {
			font-size: 1.125rem;
		}

		h3 {
			font-size: 0.875rem;
		}
	}

	.escolha-da-ficha {
		max-width: 22.75rem;
	}

	.ficha {
		display: flex;
		justify-content: space-evenly;
		padding: 1rem;
		gap: 3rem;
		border-radius: 0.5rem;
		border: 1px solid #c9cbcf;

		.divisao {
			width: 1px;
			background-color: #c9cbcf;
		}

		.nome,
		.detalhes {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 0.5rem;
		}

		.nome {
			h6,
			span {
				text-align: center;
			}
		}

		.nivel-atual,
		.proximo-nivel {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 0.5rem;

			// .cor {
			// 	width: 40px;
			// 	height: 40px;
			// 	border-radius: 50%;
			// }

			.detalhe {
				display: flex;
				flex-direction: column;
				justify-content: space-evenly;
				gap: 0.125rem;
			}
		}

		.nome,
		.nivel-atual,
		.proximo-nivel {
			h6 {
				color: #51555a;
				font-family: Poppins;
				font-size: 0.875rem;
				font-weight: 600;
			}

			span {
				color: #797d86;
				font-family: Nunito Sans;
				font-size: 0.75rem;
				font-weight: 400;
			}
		}

		.detalhes {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 0.5rem;

			.data-de-inclusao {
				color: #51555a;
				text-align: center;
				font-size: 0.75rem;
				font-weight: 400;
			}

			.aulas-restantes {
				color: #797d86;
				text-align: center;
				font-size: 0.75rem;
				font-weight: 400;
			}
		}

		.aulas-restantes {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: end;
			gap: 0.5rem;

			span {
				color: #797d86;
				text-align: center;
				font-size: 0.75rem;
				font-weight: 400;
			}
		}
	}

	.tabela-de-avaliacoes-concluidas,
	.tabela-de-comentarios {
		pacto-relatorio::ng-deep {
			.pacto-table-title-block {
				padding-top: 0;
				padding-left: 0;
				padding-right: 0;

				.table-title {
					color: #51555a;
				}
			}

			.table-content {
				padding: 0;

				.table {
					padding-left: 0;
					padding-right: 0;

					thead {
						th,
						.action-column {
							text-transform: unset;
							color: #55585e;
							font-family: Poppins;
							font-size: 0.875rem;
							font-weight: 600;
							text-align: center;
						}
					}

					tbody {
						tr {
							td {
								color: #797d86;
								font-family: Nunito Sans;
								font-size: 0.75rem;
								font-weight: 400;

								&.action-cell {
									display: flex;
									justify-content: center;
									gap: 0.3125rem;
								}
							}
						}
					}
				}
			}
		}
	}
}
