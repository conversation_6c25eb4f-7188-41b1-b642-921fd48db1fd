import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { TreinoApiNivelService } from "treino-api";
import { SelectFilterParamBuilder, SelectFilterResponseParser } from "ui-kit";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-modal-nivel-cliente",
	templateUrl: "./modal-nivel-cliente.component.html",
	styleUrls: ["./modal-nivel-cliente.component.scss"],
})
export class ModalNivelClienteComponent implements OnInit {
	@Input() matricula;
	formGroup = new FormGroup({
		nivelDoClienteControl: new FormControl(),
	});

	constructor(
		private nivelService: TreinoApiNivelService,
		private cd: ChangeDetectorRef,
		private modalAtivo: NgbActiveModal,
		private rest: RestService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.cd.detectChanges();
	}

	cancelar() {
		this.modalAtivo.close();
	}

	confirmar() {
		if (
			!this.formGroup.get("nivelDoClienteControl").value ||
			this.formGroup.get("nivelDoClienteControl").value.id == null
		) {
			this.notificationService.error("Selecione o nível");
			return;
		}
		const objNivel = this.formGroup.get("nivelDoClienteControl").value;
		this.nivelService.editarNivel(this.matricula, objNivel.id).subscribe(
			(response) => {
				this.notificationService.success(
					"Nível do aluno foi alterado com sucesso!"
				);
				this.modalAtivo.close(objNivel);
			},
			(e) => {
				this.notificationService.error(
					"Por favor, tente novamente, se o erro persistir, entre em contato com o nosso suporte.",
					"Algo deu errado!"
				);
			}
		);
	}

	nivelSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	get _rest() {
		return this.rest;
	}
}
