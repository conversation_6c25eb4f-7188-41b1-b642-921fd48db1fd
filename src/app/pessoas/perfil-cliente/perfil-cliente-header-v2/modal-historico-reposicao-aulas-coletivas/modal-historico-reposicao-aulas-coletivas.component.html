<div class="row">
	<div class="actions"></div>
</div>
<div class="resume">
	<div class="disponiveis">
		<span>{{ repositionsData.reposicoesDisponiveis }}</span>
		<p>Disponiveis</p>
	</div>
	<div class="utilizadas">
		<span>{{ repositionsData.reposicoesUtilizadas }}</span>
		<p>Utilizadas</p>
	</div>
	<div class="expiradas">
		<span>{{ repositionsData.reposicoesExpiradas }}</span>
		<p>Expiradas</p>
	</div>
</div>

<pacto-relatorio
	#tableData
	[showShare]="true"
	[table]="table"
	actionTitulo="Ações"></pacto-relatorio>

<ng-template #reposicaoCellRef let-item="item">
	<div class="pills">
		<span
			*ngIf="item?.reposicao"
			class="status-pill {{ stringToKebab(item.reposicao) }}">
			{{ item.reposicao }}
		</span>
	</div>
</ng-template>
