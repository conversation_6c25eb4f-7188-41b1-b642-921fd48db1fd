@import "projects/ui/assets/ui-kit.scss";

::ng-deep {
	.pills {
		display: flex;
		align-items: center;
		flex-direction: row;
		justify-content: center;

		.status-pill {
			@extend .type-caption-rounded;
			padding: 5px 16px;
			color: $cinza06;
			background-color: $cinza01;
			border-radius: 50px;
			margin: 0 5px 0 0;

			&.dispon-vel {
				background-color: $feedbackGain01;
				color: $feedbackGain03;
			}

			&.utilizada {
				background-color: $feedbackInfo01;
				color: $feedbackInfo03;
			}

			&.expirada {
				background-color: $feedbackLoss01;
				color: $feedbackLoss03;
			}
		}
	}
}

.resume {
	width: calc(100% - 32px);
	margin: 16px;
	padding: 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	border: 1px solid $cinzaPri;
	border-radius: 8px;

	& > div {
		width: calc(33% - 32px);
		display: flex;
		flex-direction: column;
		align-items: center;

		p {
			@extend .pct-overline2;
			color: $cinza07;
			margin-bottom: 0px;
		}

		span {
			@extend .pct-display4;
			color: var(--color-typography-default-title);
		}
	}

	.disponiveis {
	}

	.utilizadas {
		border: 0px solid $cinzaPri;
		border-left-width: 1px;
		border-right-width: 1px;
	}

	.expiradas {
	}
}
