import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { AdmCoreApiClienteService } from "adm-core-api";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-historico-reposicao-aulas-coletivas",
	templateUrl: "./modal-historico-reposicao-aulas-coletivas.component.html",
	styleUrls: ["./modal-historico-reposicao-aulas-coletivas.component.scss"],
})
export class ModalHistoricoReposicaoAulasColetivasComponent implements OnInit {
	constructor(
		private restService: RestService,
		private cd: ChangeDetectorRef,
		private admRest: AdmCoreApiClienteService
	) {}

	@Input() dadosPessoais;
	repositionsData = {
		reposicoesDisponiveis: 0,
		reposicoesUtilizadas: 0,
		reposicoesExpiradas: 0,
	};
	public table: PactoDataGridConfig;
	@ViewChild("reposicaoCellRef", { static: true })
	reposicaoCellRef: TemplateRef<any>;
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;

	ngOnInit() {
		this.loadData();
	}

	loadData() {
		this.admRest
			.getInfoReposicoes(this.dadosPessoais.matricula)
			.subscribe((response) => {
				this.repositionsData = {
					reposicoesDisponiveis: response.reposicoesDisponiveis || 0,
					reposicoesUtilizadas: response.reposicoesUtilizadas || 0,
					reposicoesExpiradas: response.reposicoesExpiradas || 0,
				};
				this.cd.detectChanges();
			});
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			showFilters: true,
			endpointUrl: this.restService.buildFullUrlAdmCore(
				`clientes/${this.dadosPessoais.matricula}/historico-aulas-coletivas`
			),
			columns: [
				{
					nome: "modalidade",
					titulo: "Modalidade",
					visible: true,
				},
				{
					nome: "data",
					titulo: "Dt. desmarcada",
					visible: true,
				},
				{
					nome: "limiteParaResposicao",
					titulo: "Limite para resposição",
					visible: true,
				},
				{
					nome: "dataReposta",
					titulo: "Dt. resposição",
					visible: true,
				},
				{
					nome: "reposicao",
					titulo: "Reposição",
					visible: true,
					celula: this.reposicaoCellRef,
				},
			],
		});
	}

	stringToKebab(str: string) {
		return str
			.match(
				/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
			)
			.map((x) => x.toLowerCase())
			.join("-");
	}
}
