import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-alterar-matricula",
	templateUrl: "./alterar-matricula.component.html",
	styleUrls: ["./alterar-matricula.component.scss"],
})
export class AlterarMatriculaComponent implements OnInit {
	@Input()
	public dadosCliente: any;
	@Input()
	public matricula: string;
	@Output()
	public matriculaAlterada: EventEmitter<number> = new EventEmitter<number>();

	public form: FormGroup;

	constructor(
		private readonly fb: FormBuilder,
		private readonly service: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly ngbActiveModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.form = this.fb.group({
			usuario: this.fb.control(this.sessionService.codigoUsuarioZw),
			empresa: this.sessionService.empresaId,
			cliente: this.fb.control(this.dadosCliente.codigoCliente),
			nomeCliente: this.fb.control(this.dadosCliente.nome),
			matriculaAtual: this.fb.control(this.matricula),
			novaMatricula: this.fb.control("", Validators.required),
		});
	}

	salvar(): void {
		this.form.markAllAsTouched();
		if (this.form.invalid) {
			this.snotifyService.error("Preencha os dados corretamente");
			return;
		}

		const data = this.form.value;
		Object.keys(data).forEach((key) => (data[key] = Number(data[key])));
		this.service.alterarMatricula(this.sessionService.chave, data).subscribe(
			(response) => {
				if (!response.content) {
					this.snotifyService.error(response.meta.message);
				} else {
					this.snotifyService.success(response.content);
					this.matriculaAlterada.emit(data.novaMatricula);
					this.ngbActiveModal.dismiss();
				}
			},
			(httpResponseError) => {
				const error = httpResponseError.error;
				console.log(error);
				if (error && error.meta) {
					if (error.meta.error) {
						this.snotifyService.error(error.meta.message);
					}
				}
			}
		);
	}

	cancelar(): void {
		this.ngbActiveModal.close();
	}
}
