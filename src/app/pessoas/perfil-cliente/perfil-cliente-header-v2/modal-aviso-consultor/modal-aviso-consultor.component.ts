import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmCoreApiClienteMensagemService } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";

@Component({
	selector: "pacto-modal-aviso-consultor",
	templateUrl: "./modal-aviso-consultor.component.html",
	styleUrls: ["./modal-aviso-consultor.component.scss"],
})
export class ModalAvisoConsultorComponent implements OnInit {
	warning: FormControl = new FormControl();
	@Input() codCliente: number;
	@Input() codPessoa: number;
	@Input() matricula: string;
	resp: any = {};
	noWarning = true;
	temMensagem = false;

	@Output() update = new EventEmitter<void>();

	constructor(
		private modal: NgbActiveModal,
		private service: AdmCoreApiClienteMensagemService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.service.obterClienteMensagem(this.codPessoa).subscribe((res) => {
			if (res && res.content) {
				this.warning.setValue(this.obterMensagemSemHTML(res.content));
				this.resp = res.content;
				this.temMensagem = res.content.codigo && res.content.codigo > 0;
				this.noWarning = false;
			} else {
				this.noWarning = true;
			}

			this.cd.detectChanges();
		});
	}

	obterMensagemSemHTML(obj) {
		if (obj.mensagemSemHTML) {
			return obj.mensagemSemHTML;
		} else {
			return obj.mensagem
				.replaceAll(
					'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">',
					""
				)
				.replaceAll("<head>", "")
				.replaceAll("<title>Untitled document</title>", "")
				.replaceAll("<body>", "")
				.replaceAll("<html>", "")
				.replaceAll("<p>", "")
				.replaceAll("</p>", "")
				.replaceAll("<p>Untitled document</p>", "")
				.replaceAll("Untitled document", "")
				.replaceAll("</head>", "")
				.replaceAll("\n\n", "\n")
				.replaceAll("\n\n", "\n")
				.replaceAll("\n\n", "\n")
				.replaceAll("\n\n", "\n")
				.replaceAll("\n\n", "\n")
				.replaceAll("<strong>", "")
				.replaceAll("</strong>", "")
				.replaceAll("</body>", "")
				.replaceAll("</html>", "");
		}
	}

	save(): void {
		this.validarPermissao(true);
	}

	validarPermissao(alterar): void {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"LancarMensagemConsultor",
				"2.32 - Lançar aviso ao consultor a partir da tela de informações de cliente"
			)
			.subscribe(
				(response) => {
					// retorna o código do usuário caso tenha a permissao
					if (alterar) {
						this.salvar();
					} else {
						this.deletar();
					}
				},
				(error) => {
					const errorMsg =
						error.meta && error.meta.message
							? error.meta.message
							: `Usuário não possui permissão: 2.32 - Lançar aviso ao consultor a partir da tela de informações de cliente.`;
					this.snotifyService.error(errorMsg);
				}
			);
	}

	get isFormValid(): boolean {
		return !!this.warning.value;
	}

	salvar(): void {
		let codigoAnterior = null;
		if (this.resp && this.resp.codigo) {
			codigoAnterior = this.resp.codigo;
		}

		this.resp = {
			mensagem: this.warning.value,
			tipoMensagem: "AA",
			desabilitado: false,
			cliente: {
				codigo: Number(this.codCliente),
				matricula: this.matricula,
			},
			usuario: {
				codigo: this.sessionService.codUsuarioZW,
			},
		};
		if (codigoAnterior) {
			this.resp["codigo"] = codigoAnterior;
		}

		this.service.saveClienteMensagem(this.resp).subscribe(
			(res) => {
				this.warning.setValue(res.content.mensagem);
				this.snotifyService.success("Aviso gravado com sucesso");
				this.update.emit();
				this.modal.close("insert");
			},
			(httpResponseError) => {
				const err = httpResponseError.error;
				if (err.meta && err.meta.messageValue) {
					this.snotifyService.error(err.meta.messageValue);
				} else if (err.meta && err.meta.message) {
					this.snotifyService.error(err.meta.message);
				}
			}
		);
	}

	delete(): void {
		this.validarPermissao(false);
	}

	deletar(): void {
		this.service.excluirClienteMensagem(this.resp.codigo).subscribe(
			(res) => {
				this.warning.setValue(null);
				this.snotifyService.success("Aviso excluido com sucesso");
				this.update.emit();
				this.modal.close();
			},
			(httpResponseError) => {
				const err = httpResponseError.error;
				if (err.meta && err.meta.messageValue) {
					this.snotifyService.error(err.meta.messageValue);
				} else if (err.meta && err.meta.message) {
					this.snotifyService.error(err.meta.message);
				}
			}
		);
	}

	close(): void {
		this.modal.dismiss();
	}
}
