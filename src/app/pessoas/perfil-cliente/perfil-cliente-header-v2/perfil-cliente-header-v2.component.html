<div class="perfil-header">
	<div class="compact-info">
		<pacto-risco-churn-aluno
			*ngIf="dadosPessoais && dadosPessoais.riscoChurn"
			[dadosPessoais]="dadosPessoais"></pacto-risco-churn-aluno>

		<div class="row user-objecao" *ngIf="dadosPessoais?.objecao">
			<div class="col-10 titulo">
				<span><i class="pct pct-phone-off icon"></i></span>
				<span>Objeção definitiva:</span>
				<span class="objecao">
					{{ dadosPessoais?.objecao | captalize : true }}
				</span>
			</div>
			<div
				class="col-2 titulo remover-objecao"
				(click)="openModalRemoverObjecao()">
				Remover objeção
			</div>
		</div>
		<div class="row user-section">
			<div class="avatar" (click)="changeAvatar()" id="pch-change-avatar">
				<pacto-cat-person-avatar
					[uri]="dadosPessoais?.urlFoto ? dadosPessoais.urlFoto : null"
					[parqPositivo]="false"
					[diameter]="120"></pacto-cat-person-avatar>
				<div class="edit-overlay">
					<i class="pct pct-camera cor-branco"></i>
				</div>
			</div>

			<div class="col-6 userInfo">
				<p class="matricula">Matrícula: {{ dadosPessoais?.matricula }}</p>
				<p class="nome">
					{{ dadosPessoais?.nome | captalize }}
				</p>
				<div class="status-situacao">
					<span
						[class]="
							'situacao-aluno primario ' + dadosPessoais?.situacao | lowercase
						">
						{{ getSituacao(dadosPessoais?.situacao) | captalize : true }}
					</span>
					<span
						*ngIf="
							dadosPessoais?.situacaoContrato &&
							dadosPessoais?.situacaoContrato !== 'VI' &&
							dadosPessoais?.situacaoContrato !== 'TR'
						"
						[class]="
							'situacao-contrato secundario ' + dadosPessoais?.situacaoContrato
								| lowercase
						">
						{{
							getSituacao(dadosPessoais?.situacaoContrato) | captalize : true
						}}
					</span>
					<span
						*ngIf="
							dadosPessoais?.gympass ||
							dadosPessoais?.totalpass ||
							dadosPessoais?.gogood
						"
						[class]="
							'situacao-pass terciario ' +
							(dadosPessoais?.gympass
								? 'gympass'
								: dadosPessoais?.totalpass
								? 'totalPass'
								: 'goGood')
						">
						{{
							dadosPessoais && dadosPessoais?.gympass
								? "WellHub"
								: dadosPessoais?.totalpass
								? "TotalPass"
								: "GoGood"
						}}
					</span>
					<span
						*ngIf="dadosPessoais?.freepass || dadosPessoais?.freepass"
						[class]="'situacao-pass terciario freepass'">
						Freepass
					</span>
					<span
						*ngIf="
							dadosAuxiliares?.dataInclusaoSpc &&
							dadosAuxiliares?.parcelasSpc?.length > 0
						"
						[class]="'situacao-pass terciario spc'"
						[pactoCatTolltip]="spcParcelaRef"
						[darkTheme]="true">
						SPC
					</span>
					<span
						*ngIf="dadosPlano?.titularContratoMatricula"
						[class]="'situacao-pass terciario dependente'"
						[pactoCatTolltip]="tollDependente"
						[darkTheme]="true">
						Dependente
					</span>
					<ng-template #tollDependente>
						<span>
							Este aluno compartilha o plano com
							{{ dadosPlano?.titularContratoNome }}
						</span>
					</ng-template>

					<ng-template #spcParcelaRef>
						<p>
							{{
								"Negativado em " +
									(dadosAuxiliares?.dataInclusaoSpc | date : "dd/MM/yyyy")
							}}
						</p>
						<p *ngFor="let parcela of dadosAuxiliares?.parcelasSpc">
							{{
								parcela?.codigo +
									" - " +
									parcela?.descricao +
									" - " +
									(parcela?.valor | currency : "BRL" : "symbol-narrow")
							}}
						</p>
					</ng-template>
				</div>
				<div
					class="clienteTitularDiv"
					*ngIf="dadosPlano?.titularContratoMatricula">
					<span class="clienteTitular">Cliente titular do contrato:</span>
					<span
						class="clienteTitularNome"
						(click)="goTitularContratoCompartilhado()">
						{{ dadosPlano?.titularContratoNome }}
					</span>
				</div>
			</div>
			<div class="col-4 tags">
				<div class="saldos">
					<div class="pontos">
						<div
							(click)="goToHistoricoPontuacao()"
							id="pch-hist-pontos"
							[pactoCatTolltip]="'Pontuação'"
							[darkTheme]="true">
							<i class="pct pct-coins"></i>
							{{ pontos }}
						</div>
						<div
							[pactoCatTolltip]="'Lançar brinde'"
							[darkTheme]="true"
							(click)="goToClubeVantagensLancamentoBrinde()">
							<i
								class="pct pct-plus-circle text-color-action-default-able-4"></i>
						</div>
						<div
							[pactoCatTolltip]="'Ajustar pontuação'"
							[darkTheme]="true"
							(click)="goToClubeVantagensPontuacaoCliente()">
							<i class="pct pct-sliders text-color-action-default-able-4"></i>
						</div>
					</div>
					<div class="dinheiros" [ngClass]="saldoClasse" *ngIf="permissaoCard">
						<div
							(click)="goTomovimentacaoFinanceira()"
							id="pch-mov-fin"
							[pactoCatTolltip]="'Saldo'"
							[darkTheme]="true">
							{{ saldo | currency : "BRL" : "symbol-narrow" }}
						</div>
						<div
							pactoCatTolltip="Receber débito da conta corrente"
							[darkTheme]="true"
							*ngIf="saldo < 0"
							(click)="debitoContaCorrente()">
							<i class="pct pct-dollar-ok text-color-action-default-able-4"></i>
						</div>
						<div
							pactoCatTolltip="Ajustar débito conta corrente"
							[darkTheme]="true"
							(click)="contaCorrenteAjusteSaldo()"
							*ngIf="saldo < 0">
							<i class="pct pct-sliders text-color-action-default-able-4"></i>
						</div>
						<div
							pactoCatTolltip="Transferir crédito conta corrente"
							[darkTheme]="true"
							(click)="transferenciaDeSaldo()"
							*ngIf="saldo > 0">
							<i class="pct pct-repeat text-color-action-default-able-4"></i>
						</div>
						<div
							pactoCatTolltip="Devolver crédito conta corrente"
							[darkTheme]="true"
							(click)="contaCorrenteAjusteSaldo()"
							*ngIf="saldo > 0">
							<i
								class="pct pct-dollar-sign text-color-action-default-able-4"></i>
						</div>
					</div>
				</div>
				<div class="status">
					<div
						#statusBadge
						class="status-badge"
						[class.checked]="alunoAppInfo?.usaApp">
						<i class="status-icon pct pct-smart-check"></i>
						<span class="status-text">
							<ng-container
								*ngIf="alunoAppInfo?.usaApp; else elseBlockAppCadastrado">
								App cadastrado
							</ng-container>
							<!-- <ng-template #elseBlockAppCadastrado>App sem cadastrado <i
									class="pct pct-help-circle cor-azulim05 actionable "
									(click)="cadastrarAppCadastrado()"></i></ng-template> -->
							<ng-template #elseBlockAppCadastrado>
								App sem cadastrado
							</ng-template>
						</span>
					</div>
					<div
						#statusBadge
						class="status-badge"
						[class.checked]="assinatura?.facial">
						<i class="status-icon pct pct-facial-bio"></i>
						<span class="status-text">
							<ng-container
								*ngIf="assinatura?.facial; else elseBlockBiometriaFacial">
								Biometria facial
							</ng-container>
							<ng-template #elseBlockBiometriaFacial>
								Sem biometria facial
								<i
									class="pct pct-help-circle actionable"
									(click)="cadastrarBiometriaFacial()"></i>
							</ng-template>
						</span>
					</div>
					<div
						#statusBadge
						class="status-badge"
						[class.checked]="assinatura?.digital">
						<i class="status-icon pct pct-digital"></i>
						<span class="status-text">
							<ng-container
								*ngIf="assinatura?.digital; else elseBlockBiometriaDigital">
								Biometria digital
							</ng-container>
							<ng-template #elseBlockBiometriaDigital>
								Sem biometria digital
								<i
									class="pct pct-help-circle actionable"
									(click)="cadastrarBiometriaDigital()"></i>
							</ng-template>
						</span>
					</div>
					<div
						#statusBadge
						class="status-badge"
						[class.positivo]="parqPositivo"
						[class.checked]="parqNegativo">
						<i class="status-icon pct pct-heartbeat"></i>
						<span class="status-text">
							<ng-container *ngIf="parqPositivo">Par-Q Positivo</ng-container>
							<ng-container *ngIf="parqNegativo">Par-Q Negativo</ng-container>
							<ng-template [ngIf]="parqNaoRespondido">
								Sem Par-Q
								<i
									class="pct pct-help-circle actionable"
									(click)="cadastrarParQ()"></i>
							</ng-template>
						</span>
					</div>
				</div>
				<!-- <div class="status">
					<div #statusBadge class="status-badge" [class.checked]="usuarioMovel?.usuarioMovel">
						<i class="status-icon pct pct-smartphone"></i>
						<span class="status-text">App cadastrado</span>
					</div>
					<div #statusBadge class="status-badge" [class.checked]="assinatura?.assinaturaBiometriaFacial">
						<i class="status-icon pct pct-happy-01"></i>
						<span class="status-text">Biometria facial</span>
					</div>
					<div #statusBadge class="status-badge" [class.checked]="assinatura?.assinaturaDigitalBiometria">
						<i class="status-icon pct pct-digital"></i>
						<span class="status-text">Biometria digital</span>
					</div>
					<div #statusBadge class="status-badge" [class.checked]="
							usuarioVerificacao?.usuarioVerificacao === 2
						">
						<i class="status-icon pct pct-check"></i>
						<span class="status-text">Usuario verificado</span>
					</div>
					<div #statusBadge class="status-badge" [class.checked]="dadosPessoais?.parqPositivo === true">
						<i class="status-icon pct pct-heartbeat"></i>
						<span class="status-text">Par-Q</span>
					</div>
				</div> -->
			</div>
		</div>
		<div *ngIf="!isMiniPerfil">
			<hr class="separator" />
			<div class="d-flex justify-content-between functions-section">
				<div class="notifications">
					<div class="observacao notification-button">
						<pacto-cat-button
							id="pch-btn-verificar"
							icon="pct pct-check-circle"
							class="actionable"
							*ngIf="
								permissaoVerificarCliente &&
								usuarioVerificacao?.verificarCliente &&
								usuarioVerificacao?.apresentarVerificarCliente
							"
							[label]="'Verificar'"
							type="NO_BORDER"
							size="MEDIUM"
							(click)="verificarCliente()"></pacto-cat-button>
						<pacto-cat-button
							id="pch-btn-verificar-remover"
							icon="pct pct-check-circle"
							class="actionable"
							*ngIf="
								permissaoVerificarCliente &&
								usuarioVerificacao?.verificarCliente &&
								usuarioVerificacao?.apresentarDesverificarCliente
							"
							[label]="'Remover verificação'"
							type="NO_BORDER"
							size="MEDIUM"
							(click)="desverificarCliente()"></pacto-cat-button>
						<!-- <pacto-cat-button id='pch-btn-observacao' icon='pct pct-file-text' class='cor-azulim05'
							[label]='getObservationsButtonText(dadosPlano?.observacoes)' type='NO_BORDER' size='MEDIUM'
							(click)='openObservations()'></pacto-cat-button> -->
						<pacto-cat-button
							id="pch-btn-atualizar-bv"
							icon="pct pct-rotate-ccw"
							class="actionable btn-atualizar-bv"
							[label]="'Atualizar BV'"
							*ngIf="dadosPlano?.atualizarBv"
							type="NO_BORDER"
							size="MEDIUM"
							(click)="atualizarBV()"></pacto-cat-button>
					</div>
				</div>
				<!-- <div class=" functionalities">
					<div class="function-button">
						<pacto-cat-button
							 id="pch-btn-log"
							 title="Log"
							 iconPosition="after"
							 label=""
							 type="OUTLINE"
							 size="MEDIUM"
							 icon="list"
							 (click)="openLogAtividades()"
						></pacto-cat-button>
					</div>
					<div class="function-button caixa-em-aberto">
						<pacto-cat-button
							 id="pch-btn-caixa-em-aberto"
							 type="OUTLINE"
							 size="MEDIUM"
							 (click)="abrirCaixaEmAberto()"
							 label="Caixa em aberto"
						></pacto-cat-button>
					</div>
					<div class="function-button vendas">
						<div
							 class="dropdown"
							 ngbDropdown
							 [placement]="'bottom-right'"
							 [autoClose]="'outside'"
						>
							<div
								 class="dropdown__btn-wrapper"
								 ngbDropdownToggle
							>
								<pacto-cat-button
									 id="pch-btn-vendas"
									 type="OUTLINE"
									 label="Vendas"
									 class="dropdownbutton"
								></pacto-cat-button>
								<span class="icon-drop">
									<i class="pct pct-chevron-down"></i>
								</span>
							</div>
							<div
								 class="dropdown__content"
								 ngbDropdownMenu
							>
								<button
									 ngbDropdownItem
									 class="drowpdown-item-list"
									 (click)="goToMaisDiaria()"
									 id="pch-opt-vendas-diaria"
								>
									Diária
								</button>
								<button
									 ngbDropdownItem
									 class="drowpdown-item-list"
									 (click)="goToMaisVendaAvulsa()"
									 id="pch-opt-venda-avulsa"
								>
									Venda avulsa
								</button>
								<button
									 ngbDropdownItem
									 class="drowpdown-item-list"
									 id="pch-opt-novo-contrato"
									 (click)="novoContratoNew()"
								>
									{{getLabelButtonAddContrato(dadosPessoais?.situacao)}}
								</button>
							</div>
						</div>
					</div> -->
				<div class="functionalities">
					<div class="function-button">
						<pacto-cat-button
							id="pch-btn-log"
							title="Log"
							iconPosition="after"
							label=""
							type="OUTLINE"
							size="MEDIUM"
							icon="list"
							(click)="openLogAtividades()"></pacto-cat-button>
					</div>
					<div class="function-button caixa-em-aberto">
						<!--<pacto-cat-button
							id="pch-btn-caixa-em-aberto"
							type="OUTLINE"
							size="MEDIUM"
							(click)="abrirCaixaEmAberto()"
							label="Caixa em aberto"></pacto-cat-button>-->
						<button
							ds3-outlined-button
							id="pch-btn-caixa-em-aberto"
							(click)="abrirCaixaEmAberto()"
							size="sm">
							Caixa em aberto
						</button>
					</div>
					<div class="function-button vendas">
						<div
							class="dropdown"
							ngbDropdown
							[placement]="'bottom-right'"
							[autoClose]="'outside'">
							<div class="dropdown__btn-wrapper" ngbDropdownToggle>
								<pacto-cat-button
									id="pch-btn-vendas"
									type="OUTLINE"
									label="Vendas"
									class="dropdownbutton"></pacto-cat-button>
								<span class="icon-drop">
									<i class="pct pct-chevron-down"></i>
								</span>
							</div>
							<div class="dropdown__content" ngbDropdownMenu>
								<button
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goToMaisDiaria()"
									id="pch-opt-vendas-diaria">
									Diária
								</button>
								<button
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goToMaisVendaAvulsa()"
									id="pch-opt-venda-avulsa">
									Venda avulsa
								</button>
								<button
									*ngIf="habilitaNovoContrato"
									ngbDropdownItem
									class="drowpdown-item-list"
									id="pch-opt-novo-contrato"
									[disabled]="!habilitaNovoContrato"
									(click)="novoContrato()">
									{{ getLabelButtonAddContrato(dadosPessoais?.situacao) }}
								</button>
							</div>
						</div>
					</div>
					<div class="function-button mais">
						<div
							class="dropdown"
							ngbDropdown
							[placement]="'bottom-right'"
							[autoClose]="'outside'">
							<div class="dropdown__btn-wrapper" ngbDropdownToggle>
								<pacto-cat-button
									id="pch-btn-mais"
									type="PRIMARY"
									label="Mais opções"
									class="dropdownbutton"></pacto-cat-button>
								<span class="icon-drop-blue">
									<i class="pct pct-chevron-down"></i>
								</span>
							</div>
							<div
								class="dropdown__content"
								id="dropdown_btn_mais_opcoes"
								ngbDropdownMenu>
								<button
									id="pch-opt-mais-acesso"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="goToVendasAcessos()">
									Acessos
									<i
										[class]="
											isSubMenuVendasExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container *ngIf="isSubMenuVendasExpandido">
									<button
										id="pch-opt-acesso-definir-senha"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="definirSenhaDeAcesso()">
										Definir senha de acesso
									</button>
									<button
										id="pch-opt-acesso-bloqueio"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="bloqueioDeAcesso()">
										Bloqueio ou mensagem na catraca
									</button>
									<button
										id="pch-opt-acesso-manual"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="registrarAcessoManual()">
										Registrar acesso manual
									</button>
								</ng-container>

								<button
									id="pch-opt-mais-share-link"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="goToVendasCompartilharLink()">
									Compartilhar link
									<i
										[class]="
											isSubMenuCompartilharLinkExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container *ngIf="isSubMenuCompartilharLinkExpandido">
									<button
										id="pch-opt-mais-share-link-cad-cartao"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="linkDeCadastroDeCartao()">
										Link de cadastro de cartão
									</button>
									<button
										id="pch-opt-mais-share-link-pagamento"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="linkDePagamento()">
										Link de pagamento
									</button>
								</ng-container>
								<button
									id="pch-opt-mais-lancar-aviso"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="goToVendasLancarAviso()">
									Lançar aviso
									<i
										[class]="
											isSubMenuLancarAvisoExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container *ngIf="isSubMenuLancarAvisoExpandido">
									<button
										id="pch-opt-lancar-aviso-medico"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="avisoMedico()">
										Aviso médico
									</button>
									<button
										id="pch-opt-lancar-aviso-consultor"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="avisoAoConsultor(true)">
										Aviso ao consultor
									</button>
								</ng-container>
								<button
									id="pch-opt-mais-relacionamento"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="goToVendasRelacionamento()">
									Relacionamento
									<i
										[class]="
											isSubMenuRelacionamentoExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container *ngIf="isSubMenuRelacionamentoExpandido">
									<button
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="abrirBoletinsDeVisita()">
										Ver Boletins de Visitas
									</button>
									<button
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="goHistoricoDeIndicacoes()">
										Histórico de indicações
									</button>
									<button
										id="pch-opt-mais-relacionamento-contato-avulso"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="abrirContatoAvulso()">
										Contato Avulso
									</button>
									<button
										id="pch-opt-mais-relacionamento-objetivo-aluno"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="objetivoDoAluno()">
										Objetivo do aluno
									</button>
									<button
										id="pch-opt-mais-relacionamento-orcamento"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="orcamento()">
										Orçamento
									</button>
								</ng-container>
								<button
									*ngIf="isConfigPactoPrint"
									id="submenu-carteirinha-expandir-btn"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="expandirSubmenuCarteirinha()">
									Carteirinha
									<i
										[class]="
											isSubMenuCarteirinhaExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container
									id="submenu-carteirinha"
									*ngIf="isSubMenuCarteirinhaExpandido">
									<button
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="solicitarCarteirinha()">
										Solicitar Carteirinha
									</button>
									<button
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="visualizarCarteirinha()">
										Visualizar Carteirinha
									</button>
									<button
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="abrirHistoricoCarteirinha()">
										Histórico de Carteirinha
									</button>
								</ng-container>
								<button
									id="pch-opt-mais-armario"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goToVendasArmario()">
									Armário
								</button>
								<button
									id="pch-opt-mais-convidado"
									*ngIf="
										infoConvidado?.direito > 0 || infoConvidado?.exibirConvite
									"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goToVendasConvidado()">
									Convidado
								</button>
								<button
									id="pch-opt-mais-ativar-acesso-convidado"
									*ngIf="ativarAcessoConvidado"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="acaoAtivarAcessoConvidado()">
									Ativar acesso do convidado
								</button>
								<button
									id="pch-opt-mais-enviar-acesso-app"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="enviarEmailConfirmacaoApp()">
									Enviar acesso ao aplicativo
								</button>
								<button
									id="pch-opt-mais-gympass"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goToGympass()">
									WellHub
								</button>
								<button
									id="pch-opt-mais-gogood"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goToGogood()">
									Gogood
								</button>
								<button
									id="pch-opt-mais-totalpass"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="goTotalpass()">
									TotalPass
								</button>
								<button
									id="pch-opt-mais-linha-tempo"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="abrirLinhaDoTempo()">
									Linha do tempo
								</button>
								<!-- <button
									*ngIf="botaoTransferenciaSaldo !== '' || botaoAjustarSaldo !== '' "
									id="pch-opt-mais-conta-corrente"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="goToContaCorrente()"
								>
									Conta corrente
									<i [class]="
											isSubMenuContaCorrenteExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container *ngIf="isSubMenuContaCorrenteExpandido">
									<button
										id="pch-opt-mais-conta-corrente-transferencia"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="contaCorrente()"
										*ngIf="botaoTransferenciaSaldo !== ''"
									>
										{{ botaoTransferenciaSaldo }}
									</button>
									<button
										id="pch-opt-mais-conta-corrente-saldo"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="contaCorrenteAjustar()"
										*ngIf="botaoAjustarSaldo !== ''"
									>
										{{ botaoAjustarSaldo }}
									</button>
								</ng-container>

								<button
									id="pch-opt-mais-clube-vantagens"
									ngbDropdownItem
									class="drowpdown-item-list parent-sub-menu"
									(click)="goToClubeVantagens()"
								>
									Clube de vantagens
									<i [class]="
											isSubMenuClubeVantagensExpandido
												? 'pct pct-chevron-up'
												: 'pct pct-chevron-down'
										"></i>
								</button>
								<ng-container *ngIf="isSubMenuClubeVantagensExpandido">
									<button
										id="pch-opt-mais-clube-ajuste-pontuacao"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="goToClubeVantagensPontuacaoCliente()"
									>
										Ajuste de pontuação cliente
									</button>
									<button
										id="pch-opt-mais-clube-lancamento-brinde"
										ngbDropdownItem
										class="drowpdown-item-list children-sub-menu"
										(click)="goToClubeVantagensLancamentoBrinde()"
									>
										Lançamento de brinde
									</button>
								</ng-container> -->

								<button
									id="pch-opt-mais-gestao-recebiveis"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="abrirGestaoRecebiveis()">
									Gestão de Recebíveis
								</button>
								<button
									*ngIf="apresentarBtnMQV"
									id="pch-opt-mais-relatorio-mqv"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="abrirRelatorioMQV()">
									Visualizar relatório MQV
								</button>
								<button
									id="pch-opt-mais-reposicoes-de-aula-coletiva"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="openModalReposicoesDeAulaColetiva()">
									Reposições de aula coletiva
								</button>
								<button
									*ngIf="apresentarBtnSyncMGB"
									id="pch-opt-mais-sincronizar-aluno-mgb"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="sincronizarAlunoMgb()">
									Sincronizar aluno com MGB
								</button>
                                <ng-container *ngIf="isIntegracaoFogueteHabilitada">
                                    <button
                                        id="pch-opt-reenviar-acesso-foguete"
                                        ngbDropdownItem
                                        class="drowpdown-item-list"
                                        (click)="reenviarAcessoFoguete()">
                                        Reenviar acesso Foguete
                                    </button>
                                </ng-container>
								<button
									id="pch-opt-mais-alterar-matricula"
									ngbDropdownItem
									class="drowpdown-item-list"
									(click)="alterarMatricula()">
									Alterar matrícula
								</button>
								<ng-container *ngIf="utilizarGestaoClienteComRestricao">
									<button
										id="pch-opt-incluir-cliente-restricao"
										ngbDropdownItem
										class="drowpdown-item-list"
										(click)="
											permiteIncluirOrRetirirClienteRestricao
												? incluirOuRetirarClienteRestricao()
												: null
										"
										[pactoCatTolltip]="
											toolTipBtnIncluirOuRetirarClienteRestricao
										"
										[ngStyle]="
											permiteIncluirOrRetirirClienteRestricao
												? { color: '#FA1E1E' }
												: { color: '#7B7B7BFF', cursor: 'not-allowed' }
										">
										<i class="pct pct-x-octagon icon"></i>
										{{ labelBtnClienteRestricao }}
									</button>
								</ng-container>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div *ngIf="!isMiniPerfil">
			<div class="extra-info" *ngIf="isPerfilExpandido && permissaoCard">
				<div class="row info-cards">
					<div class="info-card dados-pessoais">
						<span class="info-title">
							Dados pessoais
							<i
								id="pch-link-dados-pessoais"
								class="pct pct-edit actionable"
								(click)="goDadosPessoais()"></i>
						</span>
						<p class="info-text">
							<span
								id="pch-tltp-sexo"
								*ngIf="!dadosPessoais?.genero"
								[pactoCatTolltip]="'Sexo biológico'"
								[darkTheme]="true">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									fill="none">
									<g fill="#797D86" clip-path="url(#a)">
										<path
											d="M6.897 4.404a.317.317 0 0 0-.364.278l-.05.442a.329.329 0 0 0 .28.358c1.837.293 3.116 1.95 2.912 3.77-.21 1.884-1.94 3.25-3.857 3.042a3.49 3.49 0 0 1-2.346-1.267A3.377 3.377 0 0 1 2.725 8.5a3.394 3.394 0 0 1 1.373-2.372.324.324 0 0 0 .079-.444l-.253-.37a.326.326 0 0 0-.213-.135.317.317 0 0 0-.241.056A4.522 4.522 0 0 0 1.628 8.38c-.275 2.46 1.519 4.683 4.008 4.985v.475c0 .03-.025.055-.055.055h-.81a.323.323 0 0 0-.325.32v.445c0 .177.146.32.325.32h.81c.03 0 .055.025.055.055v.644c0 .176.146.32.326.32h.452c.18 0 .326-.144.326-.32v-.644c0-.03.025-.055.055-.055h.822c.18 0 .325-.143.325-.32v-.445a.323.323 0 0 0-.325-.32h-.822a.055.055 0 0 1-.055-.055v-.42c0-.029.024-.054.055-.058a4.651 4.651 0 0 0 2.67-1.3 4.448 4.448 0 0 0 1.307-2.693 4.45 4.45 0 0 0-.927-3.254 4.59 4.59 0 0 0-2.948-1.712Z" />
										<path
											d="M13.363.038 11.06.18a.225.225 0 0 0-.206.242l.04.637a.225.225 0 0 0 .234.215l1.14-.07-.896 1.05a4.3 4.3 0 0 0-2.49-.613c-2.351.144-4.19 2.197-4.129 4.656a4.668 4.668 0 0 0 1.182 3.002 4.376 4.376 0 0 0 2.77 1.442.212.212 0 0 0 .162-.05.228.228 0 0 0 .08-.157l.05-.636a.231.231 0 0 0-.199-.248c-1.66-.193-2.944-1.645-2.987-3.376a3.503 3.503 0 0 1 .922-2.476A3.266 3.266 0 0 1 9.08 2.728c1.848-.04 3.39 1.484 3.438 3.398.029 1.198-.53 2.309-1.494 2.971a.234.234 0 0 0-.065.314l.329.542a.22.22 0 0 0 .************ 0 0 0 .166-.032 4.514 4.514 0 0 0 1.466-1.66c.357-.69.535-1.472.516-2.26a4.632 4.632 0 0 0-1.355-3.185l.822-.962.072 1.182a.225.225 0 0 0 .234.214l.616-.037a.225.225 0 0 0 .206-.242L14.021.64a.633.633 0 0 0-.658-.603Z" />
									</g>
									<defs>
										<clipPath id="a">
											<path fill="#fff" d="M0 0h16v16H0z" />
										</clipPath>
									</defs>
								</svg>
								<ng-container *ngIf="dadosPessoais?.sexo">
									{{ dadosPessoais?.sexo == "F" ? "Feminino" : "Masculino" }}
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.sexo">-</ng-container>
							</span>

							<span
								*ngIf="dadosPessoais?.genero"
								id="pch-tltp-genero"
								[pactoCatTolltip]="'Gênero'"
								[darkTheme]="true">
								<i class="pct pct-Gender"></i>
								{{ getGenero(dadosPessoais.genero) }}
							</span>
						</p>
						<p class="info-text">
							<span
								id="pch-tltp-aniversario"
								class="info-text side-by-side"
								[pactoCatTolltip]="'Aniversário'"
								[darkTheme]="true">
								<i class="pct pct-cake"></i>
								<ng-container *ngIf="dadosPessoais?.nascimento">
									{{ getTimezoneOffset(dadosPessoais?.nascimento) }}
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.nascimento">
									-
								</ng-container>
							</span>
							<span
								id="pch-tltp-idade"
								class="info-text"
								[pactoCatTolltip]="'Idade'"
								[darkTheme]="true">
								<i class="pct pct-user" style="padding-bottom: 5px"></i>
								<ng-container *ngIf="dadosPessoais?.idade">
									{{ dadosPessoais?.idade }} anos
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.idade">-</ng-container>
							</span>
						</p>
						<p class="info-text">
							<span id="pch-tltp-fone">
								<i class="pct pct-phone"></i>

								<ng-container
									*ngIf="
										dadosPessoais?.telefones &&
										dadosPessoais?.telefones.length > 0
									">
									<span
										style="padding-right: 5px"
										[darkTheme]="true"
										[pactoCatTolltip]="
											dadosPessoais?.telefones[0]?.descricao
												? dadosPessoais?.telefones[0]?.descricao
												: 'Telefone'
										">
										{{
											(dadosPessoais?.telefones[0]?.ddi
												? "+" + dadosPessoais?.telefones[0]?.ddi
												: "+55") +
												" " +
												dadosPessoais?.telefones[0]?.numero
										}}
									</span>
									<i
										*ngIf="apresentarWhatsApp()"
										(click)="clickWhatsApp()"
										class="pct pct-whatsapp actionable"
										[pactoCatTolltip]="'Abrir o Whatsapp Web'"
										[darkTheme]="true"
										style="
											margin-left: 5px;
											cursor: pointer;
											margin-right: 0px;
										"></i>

									<img
										*ngIf="existeFluxoGymbot"
										(click)="sendGymbot()"
										src="./assets/images/gymbot-icon.svg"
										[pactoCatTolltip]="'Enviar Fluxo Gymbot'"
										[darkTheme]="true"
										style="
											cursor: pointer;
											width: 15px;
											height: 15px;
											margin-bottom: 6px;
											margin-left: 5px;
										" />

									<img
										*ngIf="existeFluxoGymbotPro"
										(click)="sendGymbotPro()"
										src="./assets/images/gymbotpro-icon.svg"
										[pactoCatTolltip]="'Enviar GymBot Pro'"
										[darkTheme]="true"
										style="
											cursor: pointer;
											width: 25px;
											height: 25px;
											margin-bottom: 6px;
											margin-left: 5px;
										" />
								</ng-container>
								<ng-container
									*ngIf="
										!dadosPessoais?.telefones ||
										dadosPessoais?.telefones.length == 0
									">
									-
								</ng-container>
							</span>

							<span
								id="pch-tltp-more-fone"
								class="more-items"
								[pactoCatTolltip]="phonesRef"
								position="top"
								[darkTheme]="true"
								*ngIf="dadosPessoais?.telefones?.length > 1">
								(+{{ getItemsArray(dadosPessoais?.telefones).length }})
							</span>
						</p>
						<ng-template #phonesRef>
							<p *ngFor="let item of getItemsArray(dadosPessoais?.telefones)">
								{{
									(item?.ddi ? "+" + item?.ddi : "+55") +
										" " +
										item?.numero +
										(item?.descricao ? " - " + item?.descricao : "")
								}}
							</p>
						</ng-template>
						<p class="info-text">
							<span id="pch-tltp-email">
								<i class="pct pct-mail"></i>

								<ng-container
									*ngIf="
										dadosPessoais?.emails && dadosPessoais?.emails.length > 0
									">
									<span
										[pactoCatTolltip]="'E-mail'"
										[darkTheme]="true"
										style="padding-right: 5px">
										{{ dadosPessoais?.emails[0] }}
									</span>
									<i
										(click)="clickEmail()"
										class="pct pct-mail actionable"
										[pactoCatTolltip]="'Enviar e-mail'"
										[darkTheme]="true"
										style="margin-left: 5px; cursor: pointer"></i>
								</ng-container>

								<ng-container
									*ngIf="
										!dadosPessoais?.emails || dadosPessoais?.emails.length == 0
									">
									-
								</ng-container>
							</span>

							<span
								id="pch-tltp-more-email"
								class="more-items"
								[pactoCatTolltip]="emailsRef"
								[darkTheme]="true"
								position="top"
								*ngIf="dadosPessoais?.emails?.length > 1">
								(+{{ getItemsArray(dadosPessoais?.emails).length }})
							</span>
						</p>
						<ng-template #emailsRef>
							<p *ngFor="let item of getItemsArray(dadosPessoais?.emails)">
								{{ item }}
							</p>
						</ng-template>

						<p class="info-text">
							<span
								id="pch-tltp-empresa"
								[pactoCatTolltip]="'Empresa'"
								[darkTheme]="true">
								<i class="pct pct-market"></i>
								<ng-container *ngIf="dadosPessoais?.empresa">
									{{ dadosPessoais?.empresa?.nome | captalize }}
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.empresa">-</ng-container>
							</span>
						</p>
						<p class="info-text">
							<span
								id="pch-tltp-cadastro"
								class="info-text side-by-side"
								[pactoCatTolltip]="'Cadastro'"
								[darkTheme]="true">
								<i class="pct pct-calendar"></i>
								<ng-container *ngIf="dadosPessoais?.dataCadastro">
									{{ getTimezoneOffset(dadosPessoais?.dataCadastro) }}
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.dataCadastro">
									-
								</ng-container>
							</span>
						</p>

						<span
							id="pch-tltp-sesc-matricula"
							class="info-text side-by-side"
							[pactoCatTolltip]="'Matrícula Sesc'"
							[darkTheme]="true"
							*ngIf="dadosPessoais?.sesc">
							<i class="pct pct-file-text"></i>
							<ng-container *ngIf="dadosPessoais?.matriculaSesc">
								{{ dadosPessoais?.matriculaSesc }}
							</ng-container>
							<ng-container *ngIf="!dadosPessoais?.matriculaSesc">
								-
							</ng-container>
						</span>
						<span
							id="pch-tltp-sesc-dataValidadeCarteirinha"
							class="info-text side-by-side"
							[pactoCatTolltip]="'Validade cartão'"
							[darkTheme]="true"
							*ngIf="dadosPessoais?.sesc">
							<i class="pct pct-user pct-calendar"></i>
							<ng-container *ngIf="dadosPessoais?.dataValidadeCarteirinha">
								{{
									dadosPessoais?.dataValidadeCarteirinha | date : "dd/MM/yyyy"
								}}
							</ng-container>
							<ng-container *ngIf="!dadosPessoais?.dataValidadeCarteirinha">
								-
							</ng-container>
						</span>
						<span
							id="pch-tltp-sesc-renda"
							class="info-text side-by-side"
							[pactoCatTolltip]="'Renda'"
							[darkTheme]="true"
							*ngIf="dadosPessoais?.sesc">
							<i class="pct pct-payment-form"></i>
							<ng-container *ngIf="dadosPessoais?.renda">
								{{ dadosPessoais?.renda | currency : "BRL" : "symbol-narrow" }}
							</ng-container>
							<ng-container *ngIf="!dadosPessoais?.renda">-</ng-container>
						</span>
						<span
							id="pch-tltp-sesc-categoria"
							class="info-text side-by-side"
							[pactoCatTolltip]="'Categoria'"
							[darkTheme]="true"
							*ngIf="dadosPessoais?.sesc">
							<i class="pct pct-file-text"></i>
							<ng-container *ngIf="dadosPessoais?.categoria">
								{{ dadosPessoais?.categoria }}
							</ng-container>
							<ng-container *ngIf="!dadosPessoais?.categoria">-</ng-container>
						</span>
					</div>

					<div class="info-card dados-do-plano">
						<span class="info-title">
							Dados do plano
							<i
								*ngIf="
									temPermissaoVerAbaContrato &&
									planosAtivos &&
									planosAtivos?.length > 0
								"
								class="pct pct-zoom-in info-title-icon"
								(click)="openDetalhamentoContratos()"></i>
						</span>
						<p class="info-text">
							<span
								id="pch-tltp-plano"
								[pactoCatTolltip]="'Plano atual'"
								[darkTheme]="true">
								<i class="pct pct-file-text"></i>

								<ng-container *ngIf="planosAtivos && planosAtivos?.length > 0">
									{{ planosAtivos[0]?.descricaoPlano | captalize : true }}
								</ng-container>

								<ng-container
									*ngIf="!planosAtivos || planosAtivos?.length === 0">
									-
								</ng-container>
							</span>
							<span
								id="pch-tltp-more-plano"
								class="more-items"
								[pactoCatTolltip]="planosAtivosRef"
								style="padding-left: 5px"
								position="top"
								[darkTheme]="true"
								*ngIf="getFormatedPlanos(planosAtivos).length > 0">
								(+{{ getFormatedPlanos(planosAtivos).length }})
							</span>

							<ng-template #planosAtivosRef>
								<p *ngFor="let plano of getFormatedPlanos(planosAtivos)">
									{{ plano | captalize : true }}
								</p>
							</ng-template>
						</p>
						<p class="info-text">
							<span
								id="pch-tltp-modalidade"
								[pactoCatTolltip]="'Modalidade'"
								[darkTheme]="true">
								<i class="pct pct-cross"></i>

								<ng-container *ngIf="modalidades && modalidades?.length > 0">
									{{ getModalidades(modalidades)[0] | captalize : true }}
								</ng-container>

								<ng-container *ngIf="!modalidades || modalidades?.length === 0">
									-
								</ng-container>
							</span>

							<span
								id="pch-tltp-more-modalidade"
								class="more-items"
								style="padding-left: 5px"
								[pactoCatTolltip]="modalidadesRef"
								position="top"
								[darkTheme]="true"
								*ngIf="getFormatedModalidades(modalidades).length > 0">
								(+{{ getFormatedModalidades(modalidades).length }})
							</span>

							<ng-template #modalidadesRef>
								<p
									*ngFor="
										let modalidade of getFormatedModalidades(modalidades)
									">
									{{ modalidade | captalize : true }}
								</p>
							</ng-template>
						</p>

						<span
							*ngIf="dadosPagamentoCliente?.convenioCobranca"
							class="info-title dados-pagamento-title">
							Dados do pagamento
						</span>
						<div class="d-flex">
							<!-- <p class="info-text has-action">
								<span
									id="pch-tltp-genero"
									[pactoCatTolltip]="'Sexo biológico'"
									[darkTheme]="true"
								>
									<i class="pct pct-user"></i>
									<ng-container *ngIf="dadosPessoais?.sexo">
										{{
										dadosPessoais?.sexo == 'F'
										? 'Feminino'
										: 'Masculino'
										}}
									</ng-container>
									<ng-container *ngIf="!dadosPessoais?.sexo">-</ng-container>
								</span>
							</p>
							<span
								id="pch-tltp-aniversario"
								class="info-text side-by-side"
								[pactoCatTolltip]="'Aniversário'"
								[darkTheme]="true"
							>
								<i class="pct pct-cake"></i>
								<ng-container *ngIf="dadosPessoais?.nascimento">
									{{
									dadosPessoais?.nascimento | date: 'dd/MM':'UTC'
									}}
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.nascimento">-</ng-container>
							</span>
							<span
								id="pch-tltp-idade"
								class="info-text"
								[pactoCatTolltip]="'Idade'"
								[darkTheme]="true"
							>
								<i class="pct pct-calendar"></i>
								<ng-container *ngIf="dadosPessoais?.idade">
									{{
									dadosPessoais?.idade
									}}
								</ng-container>
								<ng-container *ngIf="!dadosPessoais?.idade">-</ng-container>
								anos
							</span>
							<p class="info-text">
								<span id="pch-tltp-fone">
									<i class="pct pct-phone"></i>

									<ng-container *ngIf="dadosPessoais?.telefones && dadosPessoais?.telefones.length > 0">
										<span
											style='padding-right: 5px'
											[darkTheme]="true"
											[pactoCatTolltip]="'Telefone'"
										>
											{{ dadosPessoais?.telefones[0]?.numero }}
										</span>
										<i
											*ngIf='apresentarWhatsApp()'
											(click)='clickWhatsApp()'
											class="pct pct-whatsapp"
											[pactoCatTolltip]="'Abrir o Whatsapp Web'"
											[darkTheme]="true"
											style='color: #0380E3; margin-left: 5px; cursor: pointer'
										></i>
									</ng-container>
									<ng-container
										*ngIf="!dadosPessoais?.telefones || dadosPessoais?.telefones.length == 0">-</ng-container>
								</span>


								<span
									id="pch-tltp-more-fone"
									class="more-items cor-azulim05"
									[pactoCatTolltip]="phonesRef"
									position="top"
									[darkTheme]="true"
									*ngIf="dadosPessoais?.telefones?.length > 1"
								>
									(+{{ getItemsArray(dadosPessoais?.telefones).length }})
								</span>
							</p>
							<ng-template #phonesRef>
								<p *ngFor="
										let item of getItemsArray(dadosPessoais?.telefones)
									">
									{{ item?.numero }}
								</p>
							</ng-template>
							<p class="info-text">
								<span id="pch-tltp-email">
									<i class="pct pct-mail"></i>

									<ng-container *ngIf="dadosPessoais?.emails && dadosPessoais?.emails.length > 0">
										<span
											[pactoCatTolltip]="'E-mail'"
											[darkTheme]="true"
											style='padding-right: 5px'
										>
											{{ dadosPessoais?.emails[0] }}
										</span>
										<i
											(click)='clickEmail()'
											class="pct pct-mail"
											[pactoCatTolltip]="'Enviar e-mail'"
											[darkTheme]="true"
											style='color: #0380E3; margin-left: 5px; cursor: pointer'
										></i>
									</ng-container>

									<ng-container
										*ngIf="!dadosPessoais?.emails || dadosPessoais?.emails.length == 0">-</ng-container>
								</span>

								<span
									id="pch-tltp-more-email"
									class="more-items cor-azulim05"
									[pactoCatTolltip]="emailsRef"
									[darkTheme]="true"
									position="top"
									*ngIf="dadosPessoais?.emails?.length > 1"
								>
									(+{{ getItemsArray(dadosPessoais?.emails).length }})
								</span>
							</p>
							<ng-template #emailsRef>
								<p *ngFor="
										let item of getItemsArray(dadosPessoais?.emails)
									">
									{{ item }}
								</p>
							</ng-template>

							<p class="info-text">
								<span
									id="pch-tltp-empresa"
									[pactoCatTolltip]="'Empresa'"
									[darkTheme]="true"
								>
									<i class="pct pct-market"></i>
									<ng-container *ngIf="dadosPessoais?.empresa">
										{{ dadosPessoais?.empresa?.nome | captalize }}
									</ng-container>
									<ng-container *ngIf="!dadosPessoais?.empresa">-</ng-container>
								</span>
							</p>
						</div>
						<div class="info-card dados-do-plano">
							<span class="info-title">
								Dados do plano
							</span>
							<p class="info-text">
								<span
									id="pch-tltp-plano"
									[pactoCatTolltip]="'Plano atual'"
									[darkTheme]="true"
								>
									<i class="pct pct-file-text"></i>

									<ng-container *ngIf="planosAtivos && planosAtivos?.length > 0">
										{{ planosAtivos[0]?.descricaoPlano | captalize:true }}
									</ng-container>

									<ng-container *ngIf="!planosAtivos || planosAtivos?.length === 0">
										-
									</ng-container>
								</span>
								<span
									id="pch-tltp-more-plano"
									class="more-items cor-azulim05"
									[pactoCatTolltip]="planosAtivosRef"
									style='padding-left: 5px'
									position="top"
									[darkTheme]="true"
									*ngIf="getFormatedPlanos(planosAtivos).length > 0"
								>
									(+{{ getFormatedPlanos(planosAtivos).length }})
								</span>

								<ng-template #planosAtivosRef>
									<p *ngFor="
											let plano of getFormatedPlanos(planosAtivos)
										">
										{{ plano | captalize:true }}
									</p>
								</ng-template>
							</p>
							<p class="info-text">
								<span
									id="pch-tltp-modalidade"
									[pactoCatTolltip]="'Modalidade'"
									[darkTheme]="true"
								>
									<i class="pct pct-cross"></i>

									<ng-container *ngIf="modalidades && modalidades?.length > 0">
										{{
										getModalidades(modalidades)[0] | captalize:true
										}}
									</ng-container>

									<ng-container *ngIf="
										!modalidades|| modalidades?.length === 0
									">-</ng-container>
								</span>

								<span
									id="pch-tltp-more-modalidade"
									class="more-items cor-azulim05"
									style='padding-left: 5px'
									[pactoCatTolltip]="modalidadesRef"
									position="top"
									[darkTheme]="true"
									*ngIf="
										getFormatedModalidades(modalidades).length > 0
									"
								>
									(+{{
									getFormatedModalidades(modalidades).length
									}})
								</span>

								<ng-template #modalidadesRef>
									<p *ngFor="
											let modalidade of getFormatedModalidades(modalidades)
										">
										{{ modalidade | captalize:true }}
									</p>
								</ng-template>
							</p>


							<span class="info-title dados-pagamento-title">Dados do pagamento</span>
							<div class="d-flex">
								<p class="info-text has-action">
									<span
										[pactoCatTolltip]="'Dia do vencimento'"
										[darkTheme]="true"
									>
										<i class="pct pct-date-atention"></i>
										{{ '01/09/2021' | date: 'dd/MM/yyyy' }}
									</span>
									<i
										class="pct pct-edit actionable"
									></i>
								</p> -->
							<p
								*ngIf="dadosPagamentoCliente?.convenioCobranca"
								class="info-text has-action">
								<span
									id="pch-tltp-cobranca-automatica"
									[pactoCatTolltip]="dadosPagamentoCliente.hintExibir"
									[darkTheme]="true"
									[ngClass]="{
										'text-liberada': cobrancaAutorizada,
										'text-bloqueada': !cobrancaAutorizada
									}">
									<i class="pct pct-credit-card"></i>
									{{ cobrancaAutorizada ? "Liberada" : "Bloqueada" }}
								</span>
								<button
									id="pch-btn-edit-liberacao-catraca"
									aria-label="editar liberação de catraca"
									[pactoCatTolltip]="'Clique para alterar'"
									(click)="openModalCobrancaAutomatica()">
									<i class="pct pct-edit actionable"></i>
								</button>
							</p>
						</div>

						<p
							class="info-text"
							*ngIf="dadosPagamentoCliente?.convenioCobranca">
							<span
								id="pch-tltp-convenio-cobranca"
								[pactoCatTolltip]="'Convênio da cobrança'"
								[darkTheme]="true">
								<i class="pct pct-payment-form"></i>

								<ng-container *ngIf="dadosPagamentoCliente?.convenioCobranca">
									{{ dadosPagamentoCliente?.convenioCobranca | captalize }}
								</ng-container>
								<ng-container *ngIf="!dadosPagamentoCliente?.convenioCobranca">
									-
								</ng-container>
							</span>
						</p>
					</div>
					<div class="info-card vinculos">
						<span class="info-title">
							Vínculos
							<i
								id="pch-edit-vinculo"
								class="pct pct-edit actionable"
								(click)="goVinculos()"></i>
						</span>

						<!-- Consultor -->
						<p class="info-text">
							<span
								id="pch-tltp-colsultor"
								[pactoCatTolltip]="'Consultor'"
								[darkTheme]="true">
								<i class="pct pct-wod"></i>

								<ng-container
									*ngIf="
										getVinculos(dadosPlano?.vinculos)?.CO &&
										getVinculos(dadosPlano?.vinculos).CO?.length > 0
									">
									{{
										getVinculos(dadosPlano?.vinculos)?.CO[0]?.colaborador
											| captalize
									}}
								</ng-container>

								<ng-container
									*ngIf="
										!getVinculos(dadosPlano?.vinculos)?.CO ||
										getVinculos(dadosPlano?.vinculos)?.CO?.length === 0
									">
									-
								</ng-container>
							</span>

							<span
								id="pch-tltp-more-vinculos"
								class="more-items"
								[pactoCatTolltip]="consultorListRef"
								position="top"
								[darkTheme]="true"
								*ngIf="
									getTooltipVinculos(getVinculos(dadosPlano?.vinculos)?.CO)
										?.length > 0
								">
								(+{{
									getTooltipVinculos(getVinculos(dadosPlano?.vinculos)?.CO)
										?.length
								}})
							</span>

							<ng-template #consultorListRef>
								<p
									*ngFor="
										let consultor of getTooltipVinculos(
											getVinculos(dadosPlano?.vinculos)?.CO
										)
									">
									{{ consultor | captalize }}
								</p>
							</ng-template>
						</p>

						<!-- Professor -->
						<p class="info-text">
							<span
								id="pch-tltp-professor"
								[pactoCatTolltip]="'Professor'"
								[darkTheme]="true">
								<i class="pct pct-classroom"></i>

								<ng-container
									*ngIf="
										getVinculos(dadosPlano?.vinculos)?.PR &&
										getVinculos(dadosPlano?.vinculos).PR?.length > 0
									">
									{{
										getVinculos(dadosPlano?.vinculos)?.PR[0]?.colaborador
											| captalize
									}}
								</ng-container>

								<ng-container
									*ngIf="
										!getVinculos(dadosPlano?.vinculos)?.PR ||
										getVinculos(dadosPlano?.vinculos)?.PR?.length === 0
									">
									-
								</ng-container>
							</span>

							<span
								id="pch-tltp-more-professor"
								class="more-items"
								[pactoCatTolltip]="professorListRef"
								position="top"
								[darkTheme]="true"
								*ngIf="
									getTooltipVinculos(getVinculos(dadosPlano?.vinculos)?.PR)
										?.length > 0
								">
								(+{{
									getTooltipVinculos(getVinculos(dadosPlano?.vinculos)?.PR)
										?.length
								}})
							</span>

							<ng-template #professorListRef>
								<p
									*ngFor="
										let professor of getTooltipVinculos(
											getVinculos(dadosPlano?.vinculos)?.PR
										)
									">
									{{ professor | captalize }}
								</p>
							</ng-template>
						</p>

						<!-- Professor web -->

						<p class="info-text">
							<span
								id="pch-tltp-professor-tr"
								[pactoCatTolltip]="'Professor (TreinoWeb)'"
								[darkTheme]="true">
								<i class="pct pct-monitor"></i>

								<ng-container
									*ngIf="
										getVinculos(dadosPlano?.vinculos)?.TW &&
										getVinculos(dadosPlano?.vinculos).TW?.length > 0
									">
									{{
										getVinculos(dadosPlano?.vinculos)?.TW[0]?.colaborador
											| captalize
									}}
								</ng-container>

								<ng-container
									*ngIf="
										!getVinculos(dadosPlano?.vinculos)?.TW ||
										getVinculos(dadosPlano?.vinculos)?.TW?.length === 0
									">
									-
								</ng-container>
							</span>

							<span
								id="pch-tltp-more-professor-tr"
								class="more-items"
								[pactoCatTolltip]="professorWebListRef"
								position="top"
								[darkTheme]="true"
								*ngIf="
									getTooltipVinculos(getVinculos(dadosPlano?.vinculos)?.TW)
										?.length > 0
								">
								(+{{
									getTooltipVinculos(getVinculos(dadosPlano?.vinculos)?.TW)
										?.length
								}})
							</span>

							<ng-template #professorWebListRef>
								<p
									*ngFor="
										let professorWeb of getTooltipVinculos(
											getVinculos(dadosPlano?.vinculos)?.TW
										)
									">
									{{ professorWeb | captalize }}
								</p>
							</ng-template>
						</p>

						<p class="info-text">
							<span
								id="pch-tltp-nivel"
								[pactoCatTolltip]="'Nível aluno'"
								[darkTheme]="true">
								<i class="pct pct-level"></i>
								<span *ngIf="aluno">
									{{ (aluno.nivel ? aluno.nivel.nome : "-") | captalize }}
								</span>
							</span>
							<i
								id="pch-edit-nivel"
								class="pct pct-edit actionable"
								(click)="openModalNivel()"></i>
						</p>

						<!-- Tempo de vínculo -->
						<p class="info-text">
							<span
								id="pch-tltp-tempo-vinculo"
								[pactoCatTolltip]="'Tempo de vínculo do Aluno'"
								[darkTheme]="true">
								<i class="pct pct-knowlodge"></i>
								<ng-container>
									<ng-container *ngIf="dataMatricula && !dataMatriculaHoje">
										<span *ngIf="dataVinculoAtual">
											Vínculo atual de
											{{ dataVinculoAtual | diffBetweenDates }}
											({{ dataVinculoAtual | date : "dd/MM/yyyy" }})
										</span>
										<span *ngIf="!dataVinculoAtual">
											Vínculo atual de
											{{ dataMatricula | diffBetweenDates }}({{
												dataMatricula | date : "dd/MM/yyyy"
											}})
										</span>
									</ng-container>
									<ng-container *ngIf="dataMatricula && dataMatriculaHoje">
										Se matriculou hoje.
									</ng-container>
									<ng-container *ngIf="!dataMatricula">-</ng-container>
								</ng-container>
							</span>
						</p>

						<!-- Receita e CAC -->
						<p class="info-text">
							<span id="pch-tltp-receita-cac">
								<ng-container *ngIf="permiteCacCliente()">
									<i class="pct pct-payment-form"></i>
									<ng-container>
										<span *ngIf="dataMatricula">
											Gerou
											{{
												dadosPlano?.valorReceitaCliente
													| currency : "BRL" : "symbol-narrow"
											}}
											de receita, uma média de
											{{
												dadosPlano?.valorReceitaMediaCliente
													| currency : "BRL" : "symbol-narrow"
											}}
											mensal.
										</span>
										<span *ngIf="dataMatricula">
											(CAC atual:
											{{ cacCliente | currency : "BRL" : "symbol-narrow" }})
										</span>
										<span *ngIf="!dataMatricula">-</span>
									</ng-container>
								</ng-container>
							</span>
						</p>
					</div>
				</div>
			</div>
			<div
				id="pch-expander"
				*ngIf="permissaoCard"
				class="expander"
				(click)="isPerfilExpandido = !isPerfilExpandido">
				<i
					[ngClass]="
						isPerfilExpandido ? 'pct pct-chevron-up' : 'pct pct-chevron-down'
					"></i>
			</div>
		</div>
	</div>
	<div class="cards-avisos-observacoes">
		<div class="card-avisos" *ngIf="permissaoAvisos && !isMiniPerfil">
			<div class="title">
				<span class="pct-title3">Avisos</span>
				<span class="link-cursor" (click)="verAvisos()">
					{{ getButtonText(avisos.length) }}
				</span>
			</div>
			<div
				class="card"
				*ngFor="let aviso of processedAvisos"
				[ngStyle]="{ cursor: 'pointer' }"
				(click)="verAvisos()">
				<span>
					<i class="pct pct-alert-triangle"></i>
					{{ aviso }}
				</span>
			</div>
			<div class="card" *ngIf="avisos?.length === 0">
				<span class="empty">O aluno não possui nenhum aviso adicionado.</span>
			</div>
		</div>
		<ds3-diviser></ds3-diviser>

		<div class="card-observacoes" *ngIf="canObservation">
			<div class="title">
				<span class="pct-title3">Observações</span>
				<span class="link-cursor" (click)="openObservations()">
					{{ getButtonText(dadosPlano?.observacoes) }}
				</span>
			</div>

			<div class="card" *ngFor="let observacao of observacaoList">
				<div class="dados-obs">
					<span class="nome-data">
						{{ observacao?.usuario?.nome | captalize }}
						{{ observacao?._dataCadastro }}
					</span>
					-
					<span class="mensagem">{{ observacao?.observacaoSemHTML }}</span>
				</div>
			</div>
			<div class="card" *ngIf="observacaoList && observacaoList.length === 0">
				<span class="empty">
					O aluno não possui nenhuma observação adicionada.
				</span>
			</div>
		</div>
	</div>
</div>
<pacto-traducoes-xingling #traducoes>
	<span
		xingling="confimUserTitle"
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:title">
		Reenviar e-mail de confirmação do usuário no aplicativo
	</span>
	<span
		xingling="confimUserBody"
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:body">
		Deseja reenviar email de confirmação para o usuário
		{{
			dadosPessoais?.emails && dadosPessoais?.emails.length > 0
				? dadosPessoais?.emails[0]
				: ""
		}}
		do aplicativo ?
	</span>
	<span
		xingling="confimUserButton"
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:button">
		Enviar
	</span>
	<span
		xingling="confimUserSuccess"
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:mensagem-success">
		Email enviado com sucesso.
	</span>
	<span
		i18n="@@table-contratos:contrato-concomitante-nao-habilitado"
		xingling="contrato-concomitante-nao-habilitado">
		A configuração de contratos concomitantes está desabilitada para essa
		empresa.
	</span>
	<span i18n="@@mensagem-sem-permissao" xingling="mensagem-sem-permissao">
		Seu usuário não possui permissão, procure seu administrador
	</span>
</pacto-traducoes-xingling>
