<div class="linha-do-tempo">
	<div class="filtro">
		<div class="contrato">
			<pacto-cat-form-select
				(change)="alterouContrato()"
				[control]="form.get('plano')"
				[items]="contratos"
				idKey="contrato"
				label="Contrato"
				labelKey="contratoPlano"></pacto-cat-form-select>
		</div>
		<div class="data">
			<pacto-cat-form-datepicker
				[control]="form.get('dataInicio')"
				[label]="'Início'"></pacto-cat-form-datepicker>
		</div>
		<div class="data">
			<pacto-cat-form-datepicker
				[control]="form.get('dataFim')"
				[label]="'Até'"></pacto-cat-form-datepicker>
			<!-- <form [formGroup]="form">
			<ds3-form-field>
				<ds3-input-date
					ds3Input
					dateType="dateranger"
					[label]="'Período'"
					[control]="form.get('dataFim')"
					[position]="middle-left">
				</ds3-input-date>
			</ds3-form-field>
		</form> -->
		</div>
	</div>
	<div class="shortcut">
		<ds3-shortcut-tabs #shortcutTabs (click)="consultar()" [fullTab]="true">
			<ds3-shortcut-tab
				[control]="form.get('tipo')"
				[tabTitle]="tiposLista[0].label"></ds3-shortcut-tab>
			<ds3-shortcut-tab
				[control]="form.get('tipo')"
				[tabTitle]="tiposLista[1].label"></ds3-shortcut-tab>
			<ds3-shortcut-tab
				[control]="form.get('tipo')"
				[tabTitle]="tiposLista[2].label"></ds3-shortcut-tab>
			<ds3-shortcut-tab
				[control]="form.get('tipo')"
				[tabTitle]="tiposLista[3].label"></ds3-shortcut-tab>
			<ds3-shortcut-tab
				[control]="form.get('tipo')"
				[tabTitle]="tiposLista[4].label"></ds3-shortcut-tab>
		</ds3-shortcut-tabs>
	</div>

	<div class="items">
		<div
			*ngFor="let dia of linhaTempo; let i = index; let last = last"
			class="linha-tempo-dia">
			<div
				*ngFor="
					let item of dia.itens;
					let firstItem = first;
					let lastItem = last;
					let i2 = index
				"
				class="item">
				<pacto-cat-accordion
					[open]="false"
					[showIndicator]="item.tipo === 3"
					class="accordion-geral-linha-tempo">
					<accordion-header class="accordion-header-linha-tempo">
						<div class="header">
							<div class="hora">{{ item.data | date : "HH:mm" }}</div>
							<div
								[ngClass]="{
									'background-icone':
										(firstItem && i === 0) ||
										(lastItem && i === dia.itens.length - 1),
									'background-icone-meio':
										!(firstItem && i === 0) &&
										!(lastItem && i === dia.itens.length - 1)
								}"
								class="background-icone">
								<div
									[style.background]="background(item)"
									[style.color]="cor(item)"
									class="icone">
									<div>
										<i
											[style.color]="corIcon(item)"
											class="pct {{ icon(item) }}"></i>
									</div>
								</div>
							</div>
							<div class="descricao">
								<span
									[darkTheme]="true"
									[pactoCatTolltip]="tolltipLinhaTemplate">
									{{ item?.operacaoDescricao }}
								</span>
								<ng-template #tolltipLinhaTemplate>
									<div style="display: grid">
										<span>{{ modulo(item) + ": " + item?.mensagem }}</span>
										<span *ngIf="item?.origem">{{ item?.origem }}</span>
									</div>
								</ng-template>
							</div>
							<div class="dataItem">{{ dia.data | date : "dd/MM/yyyy" }}</div>
						</div>
					</accordion-header>
					<accordion-body class="accordion-body-linha-tempo">
						<div class="d-flex">
							<div class="div-body-observacao-border"></div>
							<div class="div-body-observacao">
								<span [innerHTML]="item?.observacao"></span>
							</div>
						</div>
					</accordion-body>
				</pacto-cat-accordion>
			</div>
		</div>
	</div>
	<div *ngIf="linhaTempo?.length == 0" class="empty-state">
		<img alt="Imagem do funil" src="assets/images/ds3-funnel.png" />
		<span>Nenhum histórico foi encontrado</span>
	</div>
</div>
