@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts";

.linha-do-tempo {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 1rem 2rem;

	.filtro {
		display: flex;
		justify-content: space-between;
		width: 100%;

		.data {
			width: 30%;

			::ng-deep pacto-cat-form-datepicker {
				margin: 0;

				pct-error-msg {
					display: none;
				}
			}
		}

		.contrato {
			width: 30%;

			::ng-deep pacto-cat-form-select {
				margin: 0;
			}
		}
	}

	.shortcut {
		width: 100%;
	}

	.items {
		flex-direction: column;
		align-items: center;
		width: 100%;

		.linha-tempo-dia {
			//display: flex;
			//justify-content: space-between;
			align-items: center;
		}

		.dataItem {
			height: 16px;
			align-items: center;
			@extend .pct-overline2-regular;
		}

		.item {
			display: flex;
			align-items: center;
			//justify-content: space-between;
			gap: 0.625rem;
			width: 100%;
			border-bottom: 1px solid #dcdddf;

			.header {
				display: flex;
				//justify-content: space-between;
				align-items: center;

				&.no-border-bottom {
					border-bottom: none;
				}

				.background-icone {
					background: $supportGray;
					border-radius: 31px 31px 0px 0px;
					width: 36px;
					height: 40px;

					.icone {
						display: flex;
						align-items: center;
						gap: 10px;
						width: 28px;
						height: 28px;
						border-radius: 42px;
						justify-content: center;
						margin: 4px;
					}
				}

				.background-icone-meio {
					background: $supportGray;
					width: 36px;
					height: 40px;

					.icone {
						display: flex;
						align-items: center;
						gap: 10px;
						width: 28px;
						height: 28px;
						border-radius: 42px;
						justify-content: center;
						margin: 4px;
					}
				}

				.hora {
					height: 16px;
					align-items: center;
					margin-right: 10px;
					@extend .pct-overline2-regular;
				}

				.descricao {
					margin-left: 16px;
					height: 18px;
					align-items: center;
					width: 420px;
					@extend .pct-body2-bold;
				}
			}
		}
	}

	.empty-state {
		display: contents;
		color: $typeDefaultTitle;
		@extend .pct-title4;

		img {
			margin-bottom: 10px;
		}
	}

	pacto-cat-accordion::ng-deep {
		.header-section {
			padding: 0;
			width: 610px;
			box-shadow: none;
		}

		.body-section {
			box-shadow: none;
		}
	}
}

.accordion-header-linha-tempo {
	//width: 550px;
}

.div-body-observacao {
	max-width: 455px;
	margin-left: 17px;
	line-break: anywhere;
}

.div-body-observacao-border {
	background: $supportGray;
	width: 36px;
	//height: 40px;
	margin-left: 41px;
}
