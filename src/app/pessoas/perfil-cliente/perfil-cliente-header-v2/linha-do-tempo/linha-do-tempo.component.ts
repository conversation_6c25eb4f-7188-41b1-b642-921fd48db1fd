import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { FilterComponent } from "ui-kit";
import {
	AdmCoreApiClienteService,
	LinhaDoTempoDia,
	LinhaDoTempo,
	Plano,
} from "adm-core-api";
import { Ds3ShortcutTabsComponent } from "projects/ui/src/lib/ds3/ds3-shortcut-tab/ds3-shortcut-tabs.component";

@Component({
	selector: "pacto-linha-do-tempo",
	templateUrl: "./linha-do-tempo.component.html",
	styleUrls: ["./linha-do-tempo.component.scss"],
})
export class LinhaDoTempoComponent implements OnInit {
	@Input()
	public matricula: string;
	pessoa;
	@ViewChild("filtro", { static: false })
	public filtro: FilterComponent;
	public form: FormGroup;
	linhaTempo: LinhaDoTempoDia[];
	contratos: Array<Plano>;
	posicaoTooltip = "right";
	posicaoIndicador = "left";
	@ViewChild("shortcutTabs", { static: false })
	shortcutTabs: Ds3ShortcutTabsComponent;
	contrato: number | null = null;

	public tipo = {
		1: {
			background: "#BCCDF5",
			modulo: "Adm",
			cor: "#163E9C",
			icon: "pct-briefcase",
			corIcon: "#0380E3",
		},
		2: {
			background: "#BCF5D9",
			modulo: "Financeiro",
			cor: "#107040",
			icon: "pct-payment-form",
			corIcon: "#28AB45",
		},
		3: {
			background: "#F5E7BC",
			modulo: "CRM",
			cor: "#9C7B16",
			icon: "pct-telemarketing",
			corIcon: "#D6A10F",
		},
		4: {
			background: "#F5D6BC",
			modulo: "Treino",
			cor: "#9C5316",
			icon: "pct-treino",
			corIcon: "#F72500",
		},
	};

	public filterEventosAdm: [];
	public filterEventosFinanceiro: [];
	public filterEventosTreino: [];
	public filterEventosCRM: [];

	public tiposLista: any[] = [
		{ value: 999, label: "Todos" },
		{ value: 1, label: "Adm" },
		{ value: 2, label: "Financeiro" },
		{ value: 3, label: "CRM" },
		{ value: 4, label: "Treino" },
	];

	constructor(
		private readonly fb: FormBuilder,
		private readonly admCoreApiClienteService: AdmCoreApiClienteService,
		private readonly cd: ChangeDetectorRef,
		private readonly snotifyService: SnotifyService,
		private readonly dialog: NgbActiveModal
	) {}

	public ngOnInit(): void {
		const dateInicio = new Date();
		dateInicio.setFullYear(dateInicio.getFullYear() - 1);
		this.form = this.fb.group({
			dataInicio: this.fb.control(dateInicio.getTime()),
			dataFim: this.fb.control(new Date().getTime()),
			plano: this.fb.control(new FormControl()),
			tipo: this.fb.control(new FormControl()),
		});
		this.consultaContrato();
		this.consultar();
		this.form.get("dataInicio").valueChanges.subscribe((value) => {
			if (value !== null) {
				this.consultar();
			}
		});
		this.form.get("dataFim").valueChanges.subscribe((value) => {
			if (value !== null) {
				this.consultar();
			}
		});
	}

	public consultaContrato(): void {
		this.admCoreApiClienteService
			.linhaDoTempoContratos(parseInt(this.matricula))
			.subscribe(
				(result) => {
					result.content.unshift({
						contrato: null,
						plano: "",
						contratoPlano: "",
					});
					this.contratos = result.content;
					this.cd.detectChanges();
				},
				(error) => {
					this.snotifyService.error("Erro ao carregar dados do contrato!");
					this.dialog.close();
				}
			);
	}

	public alterouContrato(): void {
		this.contrato = this.form.get("plano").value;
		if (this.contrato !== null && this.contrato > 0) {
			this.form.get("dataInicio").setValue(null);
			this.form.get("dataInicio").disable();
			this.form.get("dataFim").setValue(null);
			this.form.get("dataFim").disable();
		} else {
			this.form.get("dataInicio").enable();
			this.form.get("dataFim").enable();
			this.form.get("dataInicio").setValue(new Date().getTime() - 2600000000);
			this.form.get("dataFim").setValue(new Date().getTime());
		}
		this.consultar();
	}

	public consultar(): void {
		this.fecharFiltro();
		setTimeout(() => {
			this.contrato = this.form.get("plano").value;
			const dataInicio = this.form.get("dataInicio").value;
			const dataFim = this.form.get("dataFim").value;

			const tipoAtivo = this.shortcutTabs.tabs.find((tab) => tab.active);
			const tipoName = tipoAtivo.tabTitle;
			let tipo = 999;

			if (tipoName === "Todos") {
				tipo = 999;
			}
			if (tipoName === "Adm") {
				tipo = 1;
			}
			if (tipoName === "Financeiro") {
				tipo = 2;
			}
			if (tipoName === "CRM") {
				tipo = 3;
			}
			if (tipoName === "Treino") {
				tipo = 4;
			}

			if (
				(this.contrato !== null && this.contrato > 0) ||
				dataFim > dataInicio
			) {
				const filtros = {
					dtInicio: dataInicio,
					dtFim: dataFim,
					contrato: this.contrato,
					tipo,
				};

				const params: any = {
					filters: JSON.stringify(filtros),
				};

				this.admCoreApiClienteService
					.linhaDoTempo(parseInt(this.matricula), params)
					.subscribe(
						(result) => {
							this.linhaTempo = result.content;
							this.cd.detectChanges();
						},
						(error) => {
							this.snotifyService.error(
								"Erro ao carregar dados da linha do tempo!"
							);
							this.dialog.close();
						}
					);
			} else {
				this.snotifyService.warning("Datas informadas incorretas.");
			}
		}, 500);
	}

	public onSearch(filtro): void {
		this.filterEventosAdm = filtro.filters.eventosAdm;
		this.filterEventosFinanceiro = filtro.filters.eventosFinanceiro;
		this.filterEventosTreino = filtro.filters.eventosTreino;
		this.filterEventosCRM = filtro.filters.eventosCRM;
		this.consultar();
	}

	background(item: LinhaDoTempo) {
		return this.tipo[item.tipo] ? this.tipo[item.tipo].background : "";
	}

	cor(item: LinhaDoTempo) {
		return this.tipo[item.tipo] ? this.tipo[item.tipo].cor : "";
	}

	modulo(item: LinhaDoTempo) {
		return this.tipo[item.tipo] ? this.tipo[item.tipo].modulo : "";
	}

	icon(item: LinhaDoTempo) {
		return this.tipo[item.tipo] ? this.tipo[item.tipo].icon : "";
	}

	corIcon(item: LinhaDoTempo) {
		return this.tipo[item.tipo] ? this.tipo[item.tipo].corIcon : "";
	}

	fecharFiltro() {
		try {
			if (document.getElementsByClassName("dropdown-menu show").length === 0) {
				return;
			}
			const doc = document.getElementById("filtros-dropdown");
			if (doc) {
				doc.click();
			}
		} catch (e) {
			console.log(e);
		}
	}
}
