import { HttpParams } from "@angular/common/http";
import {
	Component,
	EventEmitter,
	HostBinding,
	Inject,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { MENSAGENS_ENUM } from "adm-core-api";
import { Observable, combineLatest } from "rxjs";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	PactoDataGridOrdenacaoDirecao,
} from "ui-kit";
import { AdmCoreApiClienteMensagemService } from "adm-core-api";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

declare var moment;

@Component({
	selector: "pacto-avisos",
	templateUrl: "./avisos.component.html",
	styleUrls: ["./avisos.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class AvisosComponent implements OnInit {
	@HostBinding("class.pacto-avisos-modal-component")
	styleEncapsulation = true;
	@Input()
	public cliente: string;
	@Input()
	public matricula: string;
	// @Input()
	// public usuario: string;
	@Input()
	public pessoa: string;
	public table: PactoDataGridConfig;

	@Output()
	update = new EventEmitter<any>();

	mensagensEnum;

	@ViewChild("celulaAcoes", { static: true })
	celulaAcoes: TemplateRef<any>;
	@ViewChild("messageDescription", { static: true })
	messageDescription: TemplateRef<any>;
	@ViewChild("colunaTipoMensagem", { static: true })
	colunaTipoMensagem: TemplateRef<any>;
	@ViewChild("colunaData", { static: true })
	colunaData: TemplateRef<any>;

	warning: FormControl = new FormControl();
	newWarning: FormControl = new FormControl();
	warningType: FormControl = new FormControl();
	editingRowIndex: number | null = null;
	editedWarnings = [];
	deletedItems = [];
	messagesEnum = MENSAGENS_ENUM.filter((i: any) =>
		["AA", "AM"].includes(i.sigla)
	);

	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
	];

	@ViewChild("actions", { static: true }) actions: TemplateRef<any>;
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;

	constructor(
		private readonly restService: RestService,
		private readonly sessionService: SessionService,
		private ngbActiveModal: NgbActiveModal,
		private notificationService: SnotifyService,
		private admCoreService: AdmCoreApiClienteMensagemService
	) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			endpointUrl: this.restService.buildFullUrlAdmCore(
				`cliente-mensagem/by-pessoa/${this.pessoa}`
			),
			state: {
				paginaNumero: 0,
				paginaTamanho: 5,
				ordenacaoColuna: "",
				ordenacaoDirecao: PactoDataGridOrdenacaoDirecao.ASC,
			},
			dataAdapterFn: (data) => {
				if (data && data.content && Array.isArray(data.content)) {
					data["content"] = data.content.map((d) => ({
						...d,
						descricao: MENSAGENS_ENUM.find((i) => i.sigla === d.tipoMensagem)
							.mensagem,
					}));
				} else {
					data["content"] = [];
				}

				return data;
			},
			columns: [
				{
					nome: "mensagem",
					titulo: "Mensagem",
					visible: true,
					width: "55%",
					celula: this.messageDescription,
					ordenavel: false,
				},
				{
					nome: "dataRegistro",
					titulo: "Data",
					visible: true,
					width: "15%",
					celula: this.colunaData,
				},
				{
					nome: "tipoMensagem",
					titulo: "Tipo",
					visible: true,
					width: "20%",
					celula: this.colunaTipoMensagem,
				},
				{
					nome: "acoes",
					titulo: "Ações",
					celula: this.actions,
					visible: true,
					ordenavel: false,
				},
			],
		});
	}

	deleteWarning(row, index, data): void {
		if (this.editingRowIndex !== index && this.editingRowIndex !== null) {
			return;
		}
		this.editingRowIndex = null;
		this.deletedItems.push(row);
		data.splice(index, 1);

		const currentPage = this.table.state.paginaNumero;
		const setCurrentPage = (page: number) =>
			(this.table.state.paginaNumero = page);

		if (data.length === 0 && currentPage > 0) {
			setCurrentPage(currentPage - 1);
		}

		this.save();
	}

	isPermiteClick(row: any): boolean {
		return (
			row.tipoMensagem === "RI" ||
			row.tipoMensagem === "CV" ||
			row.tipoMensagem === "DI" ||
			row.tipoMensagem === "BP" ||
			row.tipoMensagem === "PV" ||
			row.tipoMensagem === "PA"
		);
	}

	isPermiteEditar(row: any): boolean {
		return row.tipoMensagem === "AA" || row.tipoMensagem === "AM";
	}

	editRow(rowIndex: number, row: any): void {
		if (this.editingRowIndex != null) {
			return;
		}
		this.editingRowIndex = rowIndex;
		this.warning.patchValue(this.obterMensagemSemHTML(row.mensagem));
	}

	cancelEditing(): void {
		this.editingRowIndex = null;
	}

	saveEdit(row): void {
		row.mensagem = this.warning.value;

		const index = this.editedWarnings.findIndex((d) => d.codigo === row.codigo);

		if (index === -1) {
			this.editedWarnings.push(row);
		} else {
			this.editedWarnings[index] = row;
		}

		this.editingRowIndex = null;
		this.save();
	}

	save(saveButton?: boolean): void {
		const observables: Observable<any>[] = [];

		if (saveButton && this.disableButtonSave) {
			if (this.warningType.value === null) {
				this.notificationService.error("Selecione um tipo de aviso.");
				return;
			}
			if (this.newWarning.value === null || this.newWarning.value === "") {
				this.notificationService.error("Digite uma mensagem.");
				return;
			}
		}
		if (this.newWarning.value) {
			const resp: any = {
				mensagem: this.newWarning.value,
				tipoMensagem: this.warningType.value.sigla,
				cliente: {
					codigo: Number(this.cliente),
					matricula: this.matricula,
				},
				usuario: {
					codigo: this.sessionService.codUsuarioZW,
				},
			};

			const observable = this.admCoreService.saveClienteMensagem(resp);

			observables.push(observable);
		}

		if (this.editedWarnings.length > 0) {
			this.editedWarnings.forEach((item) => {
				const resp: any = {
					codigo: item.codigo,
					mensagem: item.mensagem,
					tipoMensagem: item.tipoMensagem,
					cliente: {
						codigo: Number(this.cliente),
						matricula: this.matricula,
					},
					usuario: {
						codigo: this.sessionService.codUsuarioZW,
					},
				};

				const observable = this.admCoreService.saveClienteMensagem(resp);
				observables.push(observable);
			});
		}
		if (this.deletedItems.length > 0) {
			this.deletedItems.forEach((item) => {
				const observable = this.admCoreService.excluirClienteMensagem(
					item.codigo
				);
				observables.push(observable);
			});
			this.deletedItems = [];
		}

		combineLatest(observables).subscribe({
			next: (response) => {
				this.notificationService.success(
					"Operações de aviso realizadas com sucesso."
				);
				this.update.emit("update");
				this.tableData.reloadData();
				this.newWarning.reset();
				this.warningType.reset();
			},
			error: () => {
				this.notificationService.error(
					"Ocorreu um erro ao tentar salvar as atualizações."
				);
			},
		});
	}

	messageIsHTML(aviso: any = {}): boolean {
		return (
			aviso.mensagem &&
			aviso.mensagem &&
			(aviso.mensagem.includes("<title>") ||
				aviso.mensagem.includes("!DOCTYPE"))
		);
	}

	getMessage(aviso): string {
		if (!aviso) {
			return null;
		}

		switch (aviso.tipoMensagem) {
			case "OB":
				return aviso.mensagem.replace("<br/>", "").replace("<br>", "");
			default:
				return aviso.mensagem;
		}
	}

	correctToHtml(aviso: any) {
		if (!aviso || !aviso.mensagem) {
			return null;
		}
		return this.obterMensagemSemHTML(aviso.mensagem);
	}

	acaoClienteMensagem(clienteMensagem): void {
		if (this.isPermiteClick(clienteMensagem)) {
			this.update.emit(clienteMensagem);
			this.ngbActiveModal.dismiss();
		}
	}

	obterMensagemSemHTML(mensagem) {
		return mensagem
			.replaceAll(
				'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">',
				""
			)
			.replaceAll("<head>", "")
			.replaceAll("<title>Untitled document</title>", "")
			.replaceAll("<body>", "")
			.replaceAll("<html>", "")
			.replaceAll("<p>", "")
			.replaceAll("</p>", "")
			.replaceAll("<p>Untitled document</p>", "")
			.replaceAll("Untitled document", "")
			.replaceAll("</head>", "")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("\n\n", "\n")
			.replaceAll("<strong>", "")
			.replaceAll("</strong>", "")
			.replaceAll("</body>", "")
			.replaceAll("</html>", "");
	}

	get disableButtonSave(): boolean {
		return (
			this.warningType.value === null ||
			this.newWarning.value === null ||
			this.newWarning.value === ""
		);
	}
}
