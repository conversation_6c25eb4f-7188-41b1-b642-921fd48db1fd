<div class="container">
	<pacto-relatorio
		#tableData
		[customContent]="custom"
		[enableZebraStyle]="true"
		[itensPerPage]="itensPerPage"
		[showShare]="false"
		[table]="table"
		actionTitulo="Ações"
		class="contract-table"
		id="modal-avisos-cliente"
		i18n-actionTitulo="@@label-acoes"></pacto-relatorio>

	<ng-template #actions let-data="data" let-index="index" let-item="item">
		<div
			[class.low-opacity]="editingRowIndex != null && editingRowIndex !== index"
			class="action-buttons">
			<button
				(click)="editRow(index, item)"
				*ngIf="isPermiteEditar(item)"
				class="icon-button"
				title="Editar aviso"
				type="button">
				<i class="pct pct-edit cor-azulim05 pct-icon-mr-16"></i>
			</button>
			<button
				(click)="deleteWarning(item, index, data)"
				*ngIf="isPermiteEditar(item)"
				class="icon-button"
				title="Deletar aviso"
				type="button">
				<i class="pct pct-trash-2 cor-hellboy05 pct-icon-mr-16"></i>
			</button>
		</div>
	</ng-template>

	<ng-template #colunaData let-item="item">
		<div
			(click)="acaoClienteMensagem(item)"
			[ngStyle]="isPermiteClick(item) ? { cursor: 'pointer' } : {}">
			<span class="clearStyles">
				{{ item.dataRegistro | date : "dd/MM/yyyy" }}
			</span>
		</div>
	</ng-template>

	<ng-template #messageDescription let-item="item">
		<div
			(click)="acaoClienteMensagem(item)"
			[ngStyle]="isPermiteClick(item) ? { cursor: 'pointer' } : {}">
			<span
				*ngIf="messageIsHTML(item); else simpleTextMessage"
				[innerHTML]="correctToHtml(item)"
				class="clearStyles"></span>
			<ng-template #simpleTextMessage>
				<span>{{ getMessage(item) }}</span>
			</ng-template>
		</div>
	</ng-template>

	<ng-template #colunaTipoMensagem let-item="item">
		<div
			(click)="acaoClienteMensagem(item)"
			[ngStyle]="isPermiteClick(item) ? { cursor: 'pointer' } : {}">
			<span *ngIf="item.tipoMensagem === 'AC'" class="clearStyles">
				Mensagem de Acesso na Catraca
			</span>
			<span *ngIf="item.tipoMensagem === 'AM'" class="clearStyles">
				Mensagem de Aviso Médico
			</span>
			<span *ngIf="item.tipoMensagem === 'AA'" class="clearStyles">
				Mensagem de Aviso ao Consultor
			</span>
			<span *ngIf="item.tipoMensagem === 'BP'" class="clearStyles">
				BV não respondido
			</span>
			<span *ngIf="item.tipoMensagem === 'DI'" class="clearStyles">
				Cadastro pendente. Falta(m) campos
			</span>
			<span *ngIf="item.tipoMensagem === 'PA'" class="clearStyles">
				Parcela em atraso
			</span>
			<span *ngIf="item.tipoMensagem === 'RI'" class="clearStyles">
				Risco de sair da academia
			</span>
			<span *ngIf="item.tipoMensagem === 'OC'" class="clearStyles">
				Mensagem de Objetivo Curto
			</span>
			<span *ngIf="item.tipoMensagem === 'OB'" class="clearStyles">
				Mensagem de Observação
			</span>
			<span *ngIf="item.tipoMensagem === 'OP'" class="clearStyles">
				Mensagem de Observação ao Cliente
			</span>
			<span *ngIf="item.tipoMensagem === 'PV'" class="clearStyles">
				Produto com validade vencida
			</span>
			<span *ngIf="item.tipoMensagem === 'CV'" class="clearStyles">
				Cartão de Crédito vencido
			</span>
			<span *ngIf="item.tipoMensagem === 'PV'" class="clearStyles">
				Aluguel de armario com validade vencida
			</span>
			<span *ngIf="item.tipoMensagem === 'ES'" class="clearStyles">
				Estorno automático devido o não pagamento
			</span>
		</div>
	</ng-template>

	<ng-template #custom let-dados="dados" let-visibleColumns="colunas">
		<ng-container
			*ngFor="let row of dados; let lastRow = last; let rowIndex = index">
			<tr
				[ngClass]="{
					selected: row.selected
				}"
				id="element-{{ rowIndex }}">
				<!-- Data columns -->
				<td
					*ngFor="let column of visibleColumns; let firstColumn = first"
					[ngClass]="column.styleClass"
					[style.width]="column?.width">
					<ng-container *ngIf="!column.celula && row">
						<span
							[ngClass]="{
								'hover-cell': column.cellPointerCursor
							}"
							class="column-cell">
							<ng-container *ngIf="!column.celula && column.campo && row">
								{{
									column.valueTransform
										? column.valueTransform(row[column.campo])
										: row[column.campo]
								}}
							</ng-container>
							<ng-container *ngIf="column.celula && row">
								<ng-container
									*ngTemplateOutlet="
										column.celula;
										context: {
											item: row,
											index: rowIndex,
											data: dados
										}
									"></ng-container>
							</ng-container>
						</span>
					</ng-container>

					<ng-container *ngIf="column.celula && row">
						<ng-container
							*ngTemplateOutlet="
								column.celula;
								context: {
									item: row,
									index: rowIndex,
									data: dados
								}
							"></ng-container>
					</ng-container>
				</td>
			</tr>
			<tr *ngIf="editingRowIndex === rowIndex" class="detail">
				<td colspan="4">
					<div class="d-flex justify-content-between">
						<pacto-cat-form-textarea
							[control]="warning"
							id="edita-observacao"
							class="col-8"
							rows="4"></pacto-cat-form-textarea>
						<div class="form-buttons col-8">
							<pacto-cat-button
								(click)="cancelEditing()"
								class="low-opacity"
								label="Cancelar"
								size="LARGE"
								type="OUTLINE_DARK"></pacto-cat-button>
							<pacto-cat-button
								(click)="saveEdit(row)"
								icon="pct pct-check"
								label="Concluir"
								size="LARGE"></pacto-cat-button>
						</div>
					</div>
				</td>
			</tr>
		</ng-container>
	</ng-template>

	<p class="form-label">Novo aviso</p>
	<div class="row">
		<div class="col-md-5">
			<pacto-cat-form-select-filter
				[control]="warningType"
				[options]="messagesEnum"
				idKey="sigla"
				label=""
				labelKey="mensagem"
				placeholder="Selecione o tipo de aviso"></pacto-cat-form-select-filter>
		</div>
	</div>
	<pacto-cat-form-input
		[control]="newWarning"
		i18n-label="@@modal-observacao:nova-observacao"
		i18n-placeholder="@@modal-observacao:mensagem"
		placeholder="Mensagem"></pacto-cat-form-input>
	<div class="d-flex justify-content-end">
		<pacto-cat-button
			(click)="save()"
			icon="pct pct-save"
			label="Salvar"
			[disabled]="disableButtonSave"
			size="LARGE"></pacto-cat-button>
	</div>
</div>
