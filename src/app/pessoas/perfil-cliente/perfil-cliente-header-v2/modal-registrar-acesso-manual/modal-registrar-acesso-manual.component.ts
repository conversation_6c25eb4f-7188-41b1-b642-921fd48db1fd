import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ClienteDadosPessoais } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { FormControl, FormGroup } from "@angular/forms";
import { DialogService } from "ui-kit";
import { ModalAcessosDiaComponent } from "./modal-acessos-dia/modal-acessos-dia.component";
import { PactoModalSize } from "@base-core/modal/modal.service";

declare var moment;

@Component({
	selector: "pacto-modal-registrar-acesso-manual",
	templateUrl: "./modal-registrar-acesso-manual.component.html",
	styleUrls: ["./modal-registrar-acesso-manual.component.scss"],
})
export class ModalRegistrarAcessoManualComponent implements OnInit {
	dadosPessoais: ClienteDadosPessoais = new ClienteDadosPessoais();
	form: FormGroup = new FormGroup({
		localAcesso: new FormControl(),
		coletor: new FormControl(),
		dataAcesso: new FormControl(),
		horaEntrada: new FormControl(),
		horaSaida: new FormControl(),
		registrarSaida: new FormControl(false),
	});

	locaisDeAcesso: Array<any> = new Array<any>();
	coletores: Array<any> = new Array<any>();
	executeConsultarAcessosDia = true;
	registrarAcesso: any = {};
	hourEntradaMask = [/[0-2]/, /\d/, ":", /[0-5]/, /\d/];
	hourSaidaMask = [/[0-2]/, /\d/, ":", /[0-5]/, /\d/];

	constructor(
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private dialog: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		console.log(this.registrarAcesso);
		this.telaClienteService
			.listaLocalAcesso(
				this.sessionService.chave,
				this.sessionService.empresaId
			)
			.subscribe((response) => {
				this.locaisDeAcesso = response.content;
				this.cd.detectChanges();
			});
		this.createForm();
	}

	private createForm() {
		const now = new Date();
		const hourStr = now.getHours().toString();
		const hour = hourStr.length === 1 ? `0${hourStr}` : hourStr;

		const hourStrPlusOne = (now.getHours() + 1).toString();
		const hourPlusOne =
			hourStrPlusOne.length === 1 ? `0${hourStrPlusOne}` : hourStrPlusOne;

		const minStr = now.getMinutes().toString();
		const min = minStr.length === 1 ? `0${minStr}` : minStr;

		this.form.get("horaEntrada").setValue(`${hour}:${min}`);
		this.form.get("horaSaida").setValue(`${hourPlusOne}:${min}`);
		this.form.get("dataAcesso").setValue(now);

		this.form.get("horaEntrada").valueChanges.subscribe((value) => {
			if (value.charAt(0) === "2") {
				this.hourEntradaMask[1] = /[0-3]/;
			} else {
				this.hourEntradaMask[1] = /\d/;
			}
		});

		this.form.get("horaSaida").valueChanges.subscribe((value) => {
			if (value) {
				if (value.charAt(0) === "2") {
					this.hourSaidaMask[1] = /[0-3]/;
				} else {
					this.hourSaidaMask[1] = /\d/;
				}
			}
		});

		this.form.get("localAcesso").valueChanges.subscribe((v) => {
			this.telaClienteService
				.listaColetor(
					this.sessionService.chave,
					this.sessionService.empresaId,
					{
						codLocalAcesso: v,
					}
				)
				.subscribe((response) => {
					this.coletores = response.content;
					this.cd.detectChanges();
				});
		});
	}

	save() {
		if (this.executeConsultarAcessosDia) {
			this.populateRegistrarAcesso();
		}
		if (this.executeConsultarAcessosDia) {
			this.telaClienteService
				.consultarAcessosDia(
					this.sessionService.chave,
					this.sessionService.empresaId,
					this.registrarAcesso
				)
				.subscribe(
					(responseConsulta) => {
						this.registrar();
					},
					(httpErrorResponse) => {
						this.notificationService.error(
							httpErrorResponse.error.meta.message
						);
					}
				);
		} else {
			this.registrar();
		}
	}

	registrar() {
		this.populateRegistrarAcesso();
		this.telaClienteService
			.gravarAcessoManual(
				this.sessionService.chave,
				this.sessionService.empresaId,
				this.registrarAcesso
			)
			.subscribe(
				(responseGravar) => {
					this.notificationService.success(responseGravar.content);
					this.dialog.close();
				},
				(httpErrorResponse) => {
					this.notificationService.error(httpErrorResponse.error.meta.message);
				}
			);
	}

	private populateRegistrarAcesso() {
		this.registrarAcesso.codCliente = this.dadosPessoais.codigoCliente;
		this.registrarAcesso.dataAcesso = this.form.get("dataAcesso").value
			? moment(this.form.get("dataAcesso").value).local().format("YYYY-MM-DD")
			: undefined;
		this.registrarAcesso.codLocalAcesso = +this.form.get("localAcesso").value;
		this.registrarAcesso.codColetor = +this.form.get("coletor").value;
		this.registrarAcesso.horaEntrada = this.form.get("horaEntrada").value;
		this.registrarAcesso.horaSaida = this.form.get("horaSaida").value;
		this.registrarAcesso.registrarSaida = this.form.get("registrarSaida").value;
		this.registrarAcesso.codUsuario = this.sessionService.loggedUser.usuarioZw;
	}
}
