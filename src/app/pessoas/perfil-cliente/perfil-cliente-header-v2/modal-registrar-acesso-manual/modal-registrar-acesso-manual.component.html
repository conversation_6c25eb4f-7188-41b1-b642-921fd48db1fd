<div class="modal-registrar-acesso-manual">
	<form [formGroup]="form">
		<div class="mram-row">
			<pacto-cat-form-select
				[control]="form.get('localAcesso')"
				[items]="locaisDeAcesso"
				class="mram-row-width"
				id="slct-local-acesso"
				idKey="codigo"
				label="Local de acesso"
				labelKey="descricao"></pacto-cat-form-select>

			<pacto-cat-form-select
				[control]="form.get('coletor')"
				[items]="coletores"
				class="mram-row-width"
				id="slct-coletor"
				idKey="codigo"
				label="Coletor"
				labelKey="descricao"></pacto-cat-form-select>
		</div>

		<div class="mram-row">
			<pacto-cat-form-datepicker
				[control]="form.get('dataAcesso')"
				class="mram-row-width"
				id="inpt-data-acesso"
				label="Data"></pacto-cat-form-datepicker>

			<pacto-cat-form-input
				[control]="form.get('horaEntrada')"
				[textMask]="{ mask: hourEntradaMask }"
				class="mram-row-width-input"
				id="inpt-hora-entrada"
				label="Hora da entrada"></pacto-cat-form-input>
		</div>

		<div class="mram-row-width">
			<pacto-cat-checkbox
				[control]="form.get('registrarSaida')"
				id="chk-registrar-saida"
				label="Registrar saída"></pacto-cat-checkbox>
		</div>

		<div class="mram-row-width">
			<pacto-cat-form-input
				*ngIf="form.get('registrarSaida').value"
				[control]="form.get('horaSaida')"
				[textMask]="{ mask: hourSaidaMask }"
				class="mram-row-width"
				id="inpt-hora-saida"
				label="Hora da saída"></pacto-cat-form-input>
		</div>
	</form>

	<div class="mram-btn-row">
		<pacto-cat-button
			id="btn-mram-save"
			label="Salvar"
			size="LARGE"
			(click)="registrar()"></pacto-cat-button>
	</div>
</div>
