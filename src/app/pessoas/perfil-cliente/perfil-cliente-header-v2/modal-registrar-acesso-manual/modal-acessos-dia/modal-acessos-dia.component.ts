import { Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "pacto-modal-acessos-dia",
	templateUrl: "./modal-acessos-dia.component.html",
	styleUrls: ["./modal-acessos-dia.component.scss"],
})
export class ModalAcessosDiaComponent implements OnInit {
	ultimosAcesso = new Array<any>();
	table: PactoDataGridConfig;

	constructor(private dialog: NgbActiveModal) {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.ultimosAcesso,
				};
			},
			pagination: false,
			columns: [
				{
					nome: "dataHoraEntrada",
					titulo: "Entrada",
					visible: true,
				},
				{
					nome: "dataHoraSaida",
					titulo: "<PERSON><PERSON><PERSON>",
					visible: true,
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "pct pct-edit cor-azulim-pri",
				},
			],
		});
	}

	save() {
		this.dialog.close({ registrar: true });
	}

	onIconClick(event: { row: any; iconName: string }) {
		if (event.iconName === "edit") {
			const acesso: any = event.row;
			acesso.horaEntrada = this.horarioFormatado(event.row.dataHoraEntrada);
			if (event.row.dataHoraSaida) {
				acesso.horaSaida = this.horarioFormatado(event.row.dataHoraSaida);
			}
			this.dialog.close({ edit: true, acesso });
		}
	}

	private horarioFormatado(dataHora) {
		const data = new Date(dataHora);
		const horaStr = data.getHours().toString();
		const hora = horaStr.length === 1 ? `0${horaStr}` : horaStr;
		const minStr = data.getMinutes().toString();
		const min = minStr.length === 1 ? `0${minStr}` : minStr;

		return `${hora}:${min}`;
	}
}
