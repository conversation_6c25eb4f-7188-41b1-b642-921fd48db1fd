import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalRegistrarAcessoManualComponent } from "./modal-registrar-acesso-manual.component";

describe("ModalRegistrarAcessoManualComponent", () => {
	let component: ModalRegistrarAcessoManualComponent;
	let fixture: ComponentFixture<ModalRegistrarAcessoManualComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalRegistrarAcessoManualComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalRegistrarAcessoManualComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
