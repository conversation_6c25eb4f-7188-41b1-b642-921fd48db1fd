import {
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-log-atividade-modal",
	templateUrl: "./log-atividade-modal.component.html",
	styleUrls: ["./log-atividade-modal.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class LogAtividadeModalComponent implements OnInit {
	@Input() url;
	@Input() codigoPessoa;
	@Input() codigoCliente;
	table: PactoDataGridConfig;
	@ViewChild("data", { static: true }) data: TemplateRef<any>;
	@ViewChild("hora", { static: true }) hora: TemplateRef<any>;
	@ViewChild("descricao", { static: true }) descricao;
	@ViewChild("detalhes", { static: true }) detalhes;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("custom", { static: true }) custom;

	constructor(private rest: RestService) {}

	ngOnInit() {
		this.configTable();
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildLogAtividade(
				this.codigoCliente,
				this.codigoPessoa,
				0
			),
			quickSearch: true,
			showFilters: false,
			columns: [
				{
					nome: "operacao",
					titulo: "Operação",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "operacao",
				},
				{
					nome: "responsavelAlteracao",
					titulo: "Usuário",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "responsavelAlteracao",
				},
				{
					nome: "dataAlteracao",
					titulo: "Data",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "dataAlteracao",
					celula: this.data,
				},
				{
					nome: "horaAlteracao",
					titulo: "Horário",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "horaAlteracao",
					celula: this.hora,
				},
				{
					nome: "codigo",
					titulo: "Código",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "codigo",
				},
				{
					nome: "nomeEntidadeDescricao",
					titulo: "Descrição",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "nomeEntidadeDescricao",
				},
				{
					nome: "detalhes",
					titulo: "",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.detalhes,
				},
			],
		});
	}

	detalhar(item) {
		if (item.status) {
			item.status = item.status === "detalhando" ? "fechado" : "detalhando";
			item.selected = item.status === "detalhando" ? true : false;
		} else {
			item.status = "detalhando";
			item.selected = true;
		}
	}
}
