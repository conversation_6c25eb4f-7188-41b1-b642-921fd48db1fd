@import "projects/ui/assets/import.scss";

b {
	font-weight: 900;
}

tr.selected {
	border: none;
	border-top: 2px solid $branco;

	&.details {
		border-top: none;
		border-bottom: 2px solid $branco;
	}

	background-color: $branco;

	td {
		font-weight: 900;
	}

	::ng-deep.pacto-outline {
		background-color: $branco;
	}
}

.caixa-log {
	.titulo {
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
		color: $branco;
		font-weight: 900;
		padding: 8px 15px;

		&.alterado {
			background-color: $chuchuzinho02;
			border: 1px $chuchuzinho02 solid;

			&.removido {
				background-color: $hellboy03;
				border: 1px $hellboy03 solid;
			}
		}

		&.anterior {
			background-color: $cinzaPri;
			border: 1px $cinzaPri solid;
		}
	}

	.alteracoes {
		padding: 15px;
		border: 1px $cinzaPri solid;
		border-top: none;
		border-bottom-left-radius: 5px;
		border-bottom-right-radius: 5px;
	}

	background-color: $branco;
	border-radius: 5px;
	max-width: 296px;
	width: 100%;
	overflow-wrap: break-word;
}

.descricao-column {
	max-width: 350px;
}

.data-hora {
	font-family: "Nunito Sans";
	font-style: normal;
	font-weight: 300;
	font-size: 16px;
	line-height: 16px;
	/* identical to box height */

	/* Preto / 02 */

	color: #67757c;
}
