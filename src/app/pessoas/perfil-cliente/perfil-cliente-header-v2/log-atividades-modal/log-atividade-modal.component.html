<div class="container">
	<pacto-relatorio
		#tableData
		[customContent]="custom"
		[showShare]="true"
		[table]="table"></pacto-relatorio>

	<ng-template #custom let-dados="dados" let-visibleColumns="colunas">
		<ng-container
			*ngFor="let row of dados; let lastRow = last; let rowIndex = index">
			<tr
				[ngClass]="{
					selected: row.selected
				}"
				id="element-{{ rowIndex }}">
				<!-- Data columns -->
				<td
					*ngFor="let column of visibleColumns; let firstColumn = first"
					[ngClass]="column.styleClass">
					<ng-container *ngIf="!column.celula && row">
						<span
							[ngClass]="{
								'hover-cell': column.cellPointerCursor
							}"
							class="column-cell">
							<ng-container *ngIf="!column.celula && column.campo && row">
								{{
									column.valueTransform
										? column.valueTransform(row[column.campo])
										: row[column.campo]
								}}
							</ng-container>
							<ng-container *ngIf="column.celula && row">
								<ng-container
									*ngTemplateOutlet="
										column.celula;
										context: { item: row }
									"></ng-container>
							</ng-container>
						</span>
					</ng-container>

					<ng-container *ngIf="column.celula && row">
						<ng-container
							*ngTemplateOutlet="
								column.celula;
								context: { item: row }
							"></ng-container>
					</ng-container>
				</td>
			</tr>
			<tr *ngIf="row.selected" class="selected details">
				<td colspan="3">
					<div class="caixa-log">
						<div class="titulo anterior">VALOR ANTERIOR</div>
						<div class="alteracoes">
							<div>
								<span
									*ngIf="row.valorCampoAnterior && row.valorCampoAnterior != ''"
									[innerHTML]="'[' + row.valorCampoAnterior + ']'"></span>
							</div>
						</div>
					</div>
				</td>
				<td colspan="4">
					<div class="caixa-log">
						<div
							[ngClass]="{
								removido: row.operacao === 'Removido'
							}"
							class="titulo alterado">
							VALOR ALTERADO
						</div>
						<div class="alteracoes">
							<span *ngIf="row.operacao === 'Removido'">
								Este registro foi removido.
							</span>
							<div>
								<span
									*ngIf="row.valorCampoAlterado && row.valorCampoAlterado != ''"
									[innerHTML]="'[' + row.valorCampoAlterado + ']'"></span>
							</div>
						</div>
					</div>
				</td>
			</tr>
		</ng-container>
	</ng-template>

	<ng-template #descricao let-item="item">
		<div
			*ngIf="!item.selected"
			[innerHTML]="item.identificador + '<br/>' + item.descricao"
			class="descricao-column"></div>
		<div *ngIf="item.selected" class="descricao-column">
			<b>{{ item.identificador }}</b>
		</div>
	</ng-template>

	<ng-template #data let-item="item">
		<div class="data-hora">
			<span>{{ item.dataAlteracao | date : "dd/MM/yyyy" }}</span>
		</div>
	</ng-template>

	<ng-template #hora let-item="item">
		<div class="data-hora">
			<span>{{ item.dataAlteracao | date : "HH:mm" }}</span>
		</div>
	</ng-template>

	<ng-template #detalhes let-item="item">
		<pacto-cat-button
			(click)="detalhar(item)"
			*ngIf="!item.status || item.status === 'fechado'"
			[icon]="'pct pct-plus'"
			[type]="'OUTLINE'"
			label="Detalhes"></pacto-cat-button>

		<pacto-cat-button
			(click)="detalhar(item)"
			*ngIf="item.status && item.status === 'detalhando'"
			[icon]="'pct pct-minus'"
			[type]="'OUTLINE'"
			label="Detalhes"></pacto-cat-button>
	</ng-template>
</div>
