import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
	name: "diffBetweenDates",
})
export class DiffBetweenDatesPipe implements PipeTransform {
	transform(
		date1: Date,
		yearString: string = "anos",
		monthString: string = "meses",
		dayString: string = "dias",
		date2: Date = new Date()
	): any {
		if (!date1) {
			return undefined;
		}

		let years = date2.getFullYear() - date1.getFullYear();
		let months = date2.getMonth() - date1.getMonth();
		let days = date2.getDate() - date1.getDate();

		if (days < 0) {
			months--;
			const tempDate = new Date(date1);
			tempDate.setMonth(date1.getMonth() - 1);
			days = Math.round(
				(date1.getTime() - tempDate.getTime()) / (1000 * 60 * 60 * 24)
			);
		}

		if (months < 0) {
			years--;
			months += 12;
		}

		let message = "";
		if (years > 0) {
			if (years === 1) {
				yearString = "ano";
			}
			message = `${years} ${yearString}`;
		}

		if (months > 0) {
			if (message !== "") {
				message += ", ";
			}
			if (months === 1) {
				monthString = "mês";
			}
			message += `${months} ${monthString}`;
		}

		if (days > 0) {
			if (message.includes(monthString) || message.includes(yearString)) {
				message += " e ";
			}

			if (days === 1) {
				dayString = "dia";
			}

			message += `${days} ${dayString}`;
		}

		return message;
	}
}
