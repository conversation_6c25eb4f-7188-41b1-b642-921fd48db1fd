import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalCadastroStatusComponent } from "./modal-cadastro-status.component";

describe("ModalCadastroStatusComponent", () => {
	let component: ModalCadastroStatusComponent;
	let fixture: ComponentFixture<ModalCadastroStatusComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalCadastroStatusComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalCadastroStatusComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
