import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { DialogService } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { OamdService } from "sdk";
import { FormControl, FormGroup } from "@angular/forms";

declare var moment;

@Component({
	selector: "pacto-modal-desativar-tela-nova-aluno",
	templateUrl: "./modal-desativar-tela-nova-aluno.component.html",
	styleUrls: ["./modal-desativar-tela-nova-aluno.component.scss"],
})
export class ModalDesativarTelaNovaAlunoComponent implements OnInit {
	dataDesativar;
	recurso;
	@Output() acao: EventEmitter<any> = new EventEmitter();
	form: FormGroup = new FormGroup({
		feedback: new FormControl(""),
	});

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private oamdService: OamdService,
		private dialogService: DialogService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	cancelar() {
		this.dialog.close();
	}

	enviarFeedback() {
		const body = {
			chave: this.sessionService.chave,
			usuario: this.sessionService.codUsuarioZW,
			username: this.sessionService.loggedUser.username,
			recurso: this.recurso,
			feedback: this.form.get("feedback").value,
		};
		this.oamdService
			.feedbackTelaNova(
				this.sessionService.chave,
				this.sessionService.apiToken,
				body
			)
			.subscribe(
				(response) => {
					console.log(response);
				},
				(httpResponseError) => {
					console.log(httpResponseError);
				}
			);
		this.acao.emit("VOLTAR");
		this.cancelar();
	}
}
