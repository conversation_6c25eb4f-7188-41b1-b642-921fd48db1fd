import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalBloqueioAcessoCatracaComponent } from "./modal-bloqueio-acesso-catraca.component";

describe("ModalBloqueioAcessoCatracaComponent", () => {
	let component: ModalBloqueioAcessoCatracaComponent;
	let fixture: ComponentFixture<ModalBloqueioAcessoCatracaComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalBloqueioAcessoCatracaComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalBloqueioAcessoCatracaComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
