<div class="modal-bloqueio-acesso-catraca">
	<form [formGroup]="form">
		<div>
			<pacto-cat-form-datepicker
				[control]="form.get('dataBloqueio')"
				id="dt-data-bloqueio"
				label="Escolha a data inicial do bloqueio"></pacto-cat-form-datepicker>
			<span class="under-input">
				Por favor, informe uma data para o bloqueio na catraca. Se a data
				especificada for hoje, o bloqueio será instantâneo. Caso deixe a data em
				branco, o sistema não bloqueará o aluno na catraca, mas exibirá a
				mensagem cadastrada abaixo.
			</span>
		</div>
		<pacto-cat-form-textarea
			[control]="form.get('mensagem')"
			[disabled]="isEditing"
			[errorMsg]="
				form.get('mensagem').hasError('required')
					? 'Campo obrigatorio'
					: form.get('mensagem').hasError('max')
					? 'A mensagem deve conter somente 214 caracteres'
					: ''
			"
			id="inpt-txtar-mensagem"
			label="Mensagem de bloqueio (aquela que será mostrada na catraca)"
			maxlength="214"></pacto-cat-form-textarea>
		<span class="under-input">
			Por favor, digite o texto que deseja exibir na catraca. Esta mensagem será
			visível sempre que o cliente acessar a catraca, independentemente de estar
			bloqueada ou não, conforme configurado acima
		</span>
	</form>

	<div class="mbac-btn-row">
		<pacto-cat-button
			(click)="
				botaoOutline === 'Cancelar'
					? cancelar()
					: isEditing
					? editar()
					: delete()
			"
			[label]="botaoOutline"
			id="mbac-btn-excluir"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="isEditing ? delete() : save()"
			[label]="botaoNormal"
			id="mbac-btn-gravar"
			size="LARGE"></pacto-cat-button>
	</div>
</div>
