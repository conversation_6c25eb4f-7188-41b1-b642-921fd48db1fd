import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import {
	AutorizacaoAcessoComponent,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoModalSize,
} from "ui-kit";
import { ClienteDadosPessoais } from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatDialog } from "@angular/material";

declare var moment;

@Component({
	selector: "pacto-modal-bloqueio-acesso-catraca",
	templateUrl: "./modal-bloqueio-acesso-catraca.component.html",
	styleUrls: ["./modal-bloqueio-acesso-catraca.component.scss"],
})
export class ModalBloqueioAcessoCatracaComponent implements OnInit {
	dadosPessoais: ClienteDadosPessoais = new ClienteDadosPessoais();
	clienteMensagem;
	form: FormGroup = new FormGroup({
		dataBloqueio: new FormControl(""),
		mensagem: new FormControl("", [Validators.required, Validators.max(214)]),
		bloqueio: new FormControl(),
	});
	isEditing: boolean = false;
	botaoOutline: string = "Editar agendamento";
	botaoNormal: string = "Liberar acesso / retirar aviso";

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private dialogService: DialogService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private matDialog: MatDialog
	) {}

	ngOnInit() {
		this.telaClienteService
			.buscarMensagemCatraca(
				this.sessionService.chave,
				this.dadosPessoais.codigoCliente
			)
			.subscribe(
				(response) => {
					this.clienteMensagem = response.content;
					this.form.patchValue({
						mensagem: this.clienteMensagem.mensagem,
						// bloqueio: moment().isSame(this.clienteMensagem.dataBloqueio),
						dataBloqueio: Date.parse(
							`${this.clienteMensagem.dataBloqueio} 00:00:00`
						),
					});
					this.isEditing = !!this.clienteMensagem.mensagem;
					if (this.isEditing) {
						this.form.get("dataBloqueio").disable();
						this.botaoOutline = "Editar agendamento";
						this.botaoNormal = "Liberar acesso / retirar aviso";
					} else {
						this.botaoOutline = "Cancelar";
						this.botaoNormal = "Salvar aviso/bloqueio";
					}
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	cancelar() {
		this.dialog.close();
	}

	save() {
		if (this.form.valid) {
			this.dialog.close();

			const modalConfirmacao: any = this.matDialog.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);

			modalConfirmacao.componentInstance.cancel.subscribe(() => {
				const dialogRef = this.dialogService.open(
					"Bloqueio de acesso",
					ModalBloqueioAcessoCatracaComponent,
					PactoModalSize.LARGE
				);
				dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
			});

			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						"LancarMensagemCatraca",
						"2.31 - Lançar ou atualizar mensagem para a catraca",
						this.dadosPessoais.empresa.nome
					)
					.subscribe(
						(responseAutorizacao: any) => {
							result.modal.close();
							this.clienteMensagem.dataBloqueio = new Date(
								this.form.get("dataBloqueio").value
							);
							this.clienteMensagem.mensagem = this.form.get("mensagem").value;
							this.clienteMensagem.bloqueio = moment().isSame(
								this.clienteMensagem.dataBloqueio,
								"day"
							);
							this.telaClienteService
								.gravarMensagemCatraca(
									this.sessionService.chave,
									this.dadosPessoais.codigoCliente,
									this.clienteMensagem,
									responseAutorizacao.content
								)
								.subscribe(
									(responseGravar) => {
										this.notificationService.success(responseGravar.content);
										this.dialog.close();
									},
									(httpResponseError) =>
										this.notificationService.error(
											httpResponseError.error.meta.message
										)
								);
						},
						(error) => {
							this.notificationService.error(error.error.meta.message);
						}
					);
			});
		}
	}

	editar() {
		this.isEditing = false;
		this.form.get("dataBloqueio").enable();
		const excluirButton = document.getElementById("mbac-btn-excluir");
		if (excluirButton) {
			excluirButton.remove();
		}
		this.botaoNormal = "Salvar alteracoes";
		const salvarButton = document.getElementById("mbac-btn-gravar");
		if (salvarButton) {
			salvarButton.setAttribute("click", "save()");
		}
	}

	delete() {
		this.dialog.close();
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);

		modalConfirmacao.componentInstance.cancel.subscribe(() => {
			const dialogRef = this.dialogService.open(
				"Bloqueio de acesso",
				ModalBloqueioAcessoCatracaComponent,
				PactoModalSize.LARGE
			);
			dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		});

		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"RemoverMensagemCatraca",
					"2.77 - Excluir mensagem da catraca",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						this.telaClienteService
							.removerCatraca(
								this.sessionService.chave,
								this.dadosPessoais.codigoCliente,
								this.clienteMensagem.codigo,
								response.content
							)
							.subscribe(
								(responseGravar) => {
									this.notificationService.success(
										"Mensagem excluida com sucesso!"
									);
									this.dialog.close();
								},
								(httpResponseError) =>
									this.notificationService.error(
										httpResponseError.error.meta.message
									)
							);
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}
}
