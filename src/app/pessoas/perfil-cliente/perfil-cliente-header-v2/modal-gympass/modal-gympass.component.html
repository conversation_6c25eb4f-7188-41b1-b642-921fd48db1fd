<div class="modal-gympass">
	<form [formGroup]="form">
		<pacto-cat-form-select
			[control]="form.get('tipo')"
			[items]="tiposTokenGympass"
			id="gympass-tipo-acesso"
			label="Tipo de acesso"></pacto-cat-form-select>

		<pacto-cat-form-input
			[control]="form.get('token')"
			errorMsg="Informe o token do Gympass"
			id="gympass-token"
			label="Informe o token do Gympass*"></pacto-cat-form-input>

		<pacto-cat-button
			(click)="salvarGympass()"
			id="btn-gympass-salvar"
			label="Salvar"
			type="NO_BORDER"></pacto-cat-button>
		<pacto-cat-button
			(click)="excluirGympass()"
			[disabled]="!form.get('token').value || form.get('token').value === ''"
			id="btn-gympass-excluir"
			label="Excluir"
			type="NO_BORDER"></pacto-cat-button>
	</form>

	<div class="btn-row-autorizar-acesso">
		<pacto-cat-button
			(click)="autorizarGympass()"
			id="btn-gympass-autorizar-acesso"
			label="Autorizar acesso"></pacto-cat-button>
	</div>
</div>
