import { Component, OnInit } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-gympass",
	templateUrl: "./modal-gympass.component.html",
	styleUrls: ["./modal-gympass.component.scss"],
})
export class ModalGympassComponent implements OnInit {
	codCliente;
	form: FormGroup;

	tiposTokenGympass = [
		{
			id: 1,
			label: "Tipo 1",
		},
		{
			id: 2,
			label: "Tipo 2",
		},
		{
			id: 3,
			label: "Tipo 3",
		},
		{
			id: 4,
			label: "Tipo 4",
		},
		{
			id: 5,
			label: "Tipo 5",
		},
	];

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.createForm();
	}

	createForm() {
		this.form = new FormGroup({
			tipo: new FormControl(1),
			token: new FormControl("", Validators.required),
		});
	}

	salvarGympass() {
		if (!this.form.valid) {
			this.notificationService.error("Informe o token do gympass!");
			return;
		}
		this.telaClienteService
			.cadastrarGympass(
				this.sessionService.chave,
				this.codCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.form.get("tipo").value,
				this.form.get("token").value
			)
			.subscribe(
				(response) => {
					this.notificationService.success("Token salvo com sucesso!");
					this.dialog.close();
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	excluirGympass() {
		if (this.form.get("token").value && this.form.get("token").value !== "") {
			this.telaClienteService
				.excluirGympass(
					this.sessionService.chave,
					this.codCliente,
					this.sessionService.empresaId,
					this.sessionService.loggedUser.usuarioZw
				)
				.subscribe(
					(response) => {
						this.notificationService.success("Token excluído com suceso!");
						this.dialog.close();
					},
					(httpResponseError) => {
						this.notificationService.error(
							httpResponseError.error.meta.message
						);
					}
				);
		}
	}

	autorizarGympass() {
		this.telaClienteService
			.autorizarGympass(
				this.sessionService.chave,
				this.codCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw
			)
			.subscribe(
				(response) => {
					this.notificationService.success("Token excluído com suceso!");
					this.dialog.close();
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}
}
