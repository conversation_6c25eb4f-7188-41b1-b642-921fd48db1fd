import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalGympassComponent } from "./modal-gympass.component";

describe("ModalGympassComponent", () => {
	let component: ModalGympassComponent;
	let fixture: ComponentFixture<ModalGympassComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalGympassComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalGympassComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
