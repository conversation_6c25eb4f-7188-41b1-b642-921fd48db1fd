import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Component, OnInit, Output } from "@angular/core";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import { SessionService } from "@base-core/client/session.service";

import {
	ClienteRestricao,
	AdmCoreApiClienteRestricaoService,
	TipoClienteRestricaoEnum,
} from "adm-core-api";
import { NotificarClienteRestricaoService } from "./notificar-cliente-restricao.service";

@Component({
	selector: "pacto-modal-cliente-restricao",
	templateUrl: "./modal-cliente-restricao.component.html",
	styleUrls: ["./modal-cliente-restricao.component.scss"],
})
export class ModalClienteRestricaoComponent implements OnInit {
	dadosPessoais: any;
	formObservacao: FormControl = new FormControl("");

	constructor(
		private sessionService: SessionService,
		private admCoreApiClienteRestricaoService: AdmCoreApiClienteRestricaoService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal,
		private notificarClienteRestricaoService: NotificarClienteRestricaoService
	) {}

	ngOnInit() {}

	salvar() {
		const clienteRestricao = new ClienteRestricao();
		clienteRestricao.codigoMatricula = parseInt(
			this.dadosPessoais.matricula,
			10
		);
		clienteRestricao.nome = this.dadosPessoais.nome;
		clienteRestricao.cpf = this.dadosPessoais.cpf;
		clienteRestricao.chaveEmpresa = this.sessionService.chave;
		clienteRestricao.codigoEmpresa = this.dadosPessoais.empresa.codigo;
		clienteRestricao.nomeEmpresa = this.dadosPessoais.empresa.nome;
		clienteRestricao.observacao = this.formObservacao.value;
		clienteRestricao.tipo =
			TipoClienteRestricaoEnum.RESGRISTRO_MANUAL_TELA_CLIENTE;
		this.admCoreApiClienteRestricaoService.create(clienteRestricao).subscribe(
			() => {
				this.notificationService.success("Operação realiza com sucesso");
				this.notificarClienteRestricaoService.notifyAboutChange();
				this.activeModal.close("sucesso");
			},
			(responseError) => {
				const errorMsg =
					responseError.error.meta && responseError.error.meta.message
						? responseError.error.meta.message
						: `Falha ao tentar realizar a operação.`;
				this.notificationService.error(errorMsg);
				this.activeModal.close();
			}
		);
	}

	cancelar() {
		this.activeModal.close();
	}
}
