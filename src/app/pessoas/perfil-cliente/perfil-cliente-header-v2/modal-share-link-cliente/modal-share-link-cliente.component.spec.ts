import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalShareLinkClienteComponent } from "./modal-share-link-cliente.component";

describe("ModalShareLinkClienteComponent", () => {
	let component: ModalShareLinkClienteComponent;
	let fixture: ComponentFixture<ModalShareLinkClienteComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalShareLinkClienteComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalShareLinkClienteComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
