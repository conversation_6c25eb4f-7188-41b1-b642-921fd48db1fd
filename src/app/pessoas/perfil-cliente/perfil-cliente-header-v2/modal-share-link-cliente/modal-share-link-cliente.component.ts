import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-share-link-cliente",
	templateUrl: "./modal-share-link-cliente.component.html",
	styleUrls: ["./modal-share-link-cliente.component.scss"],
})
export class ModalShareLinkClienteComponent implements OnInit {
	typeLink: "cadastrar" | "pagamento";
	codCliente;
	telefoneCliente;
	url: string;
	telefoneInvalido = false;
	todasEmAberto;
	parcelasSelecionadas;
	numeroVezesParcelamentoOperadora;

	constructor(
		private dialog: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		if (!this.typeLink) {
			throw Error("Must pass the link type!");
		}
		this.telaClienteService
			.linkCartao(
				this.sessionService.chave,
				this.codCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.typeLink,
				this.todasEmAberto,
				this.parcelasSelecionadas,
				this.numeroVezesParcelamentoOperadora
			)
			.subscribe(
				(response) => {
					this.url = response.content || null;

					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message, {
							timeout: 4000,
						});
					} else {
						this.notificationService.error(
							`Desculpe, ocorreu um erro ao carregar link de cadastro de cartão.`,
							{ timeout: 4000 }
						);
					}
				}
			);

		this.telefoneInvalido =
			!this.telefoneCliente || typeof this.telefoneCliente !== "string";
	}

	copyLink() {
		if (!this.url) {
			return;
		}
		this.telaClienteService
			.linkCartao(
				this.sessionService.chave,
				this.codCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.typeLink,
				this.todasEmAberto,
				this.parcelasSelecionadas,
				this.numeroVezesParcelamentoOperadora,
				"copiar",
				true
			)
			.subscribe(
				(response) => {
					const linkarea = document.getElementById("share-link-section");
					const selection = document.getSelection();
					const range = document.createRange();
					range.selectNode(linkarea);
					selection.removeAllRanges();
					selection.addRange(range);
					console.log("copy success", document.execCommand("copy"));
					selection.removeAllRanges();
					setTimeout(() => {
						this.notificationService.success("Link copiado com sucesso!");
					}, 500);

					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message, {
							timeout: 4000,
						});
					} else {
						this.notificationService.error(
							`Desculpe, ocorreu um erro ao copiar link.`,
							{ timeout: 4000 }
						);
					}
				}
			);
	}

	shareViaWhatsapp() {
		if (!this.url) {
			return;
		}

		if (this.telefoneInvalido) {
			return;
		}
		this.telaClienteService
			.linkCartao(
				this.sessionService.chave,
				this.codCliente,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.typeLink,
				this.todasEmAberto,
				this.parcelasSelecionadas,
				this.numeroVezesParcelamentoOperadora,
				"whatsapp",
				true
			)
			.subscribe(
				(response) => {
					let text = `*Seu link para cadastrar o cartão de crédito. ${this.sessionService.currentEmpresa.nome}.*\n`;
					text += this.url;
					const target =
						"https://api.whatsapp.com/send?phone=" +
						"55" +
						this.telefoneCliente.replace(/[()]/g, "") +
						"&text=" +
						encodeURI(text);
					window.open(target, "_blank");
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message, {
							timeout: 4000,
						});
					} else {
						this.notificationService.error(
							`Desculpe, ocorreu um erro ao carregar link de cadastro de cartão.`,
							{ timeout: 4000 }
						);
					}
				}
			);
	}
}
