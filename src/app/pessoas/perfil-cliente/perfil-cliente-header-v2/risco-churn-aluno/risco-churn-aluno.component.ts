import { Component, Input, OnInit } from "@angular/core";
import { ClienteDadosPessoais } from "adm-core-api";

enum ZonaChurnEnum {
	SEGURANCA = "SEGURANCA",
	ATENCAO = "ATENCAO",
	RISCO = "RISCO",
	CRITICA = "CRITICA",
	DESPEDIDA = "DESPEDIDA",
}

interface ZonaChurn {
	min: number;
	max: number;
	descricao: ZonaChurnEnum;
}

const ZONAS_CHURN: ZonaChurn[] = [
	{ min: 0, max: 59, descricao: ZonaChurnEnum.SEGURANCA },
	{ min: 60, max: 69, descricao: ZonaChurnEnum.ATENCAO },
	{ min: 70, max: 84, descricao: ZonaChurnEnum.RISCO },
	{ min: 85, max: 94, descricao: ZonaChurnEnum.CRITICA },
	{ min: 95, max: 100, descricao: ZonaChurnEnum.DESPEDIDA },
];

@Component({
	selector: "pacto-risco-churn-aluno",
	templateUrl: "./risco-churn-aluno.component.html",
	styleUrls: ["./risco-churn-aluno.component.scss"],
})
export class RiscoChurnAlunoComponent implements OnInit {
	@Input() dadosPessoais: ClienteDadosPessoais;
	saibaMais = false;
	zona;

	constructor() {}

	ngOnInit() {
		this.zona = this.zonaChurnByNumero(this.dadosPessoais.riscoChurn);
	}

	zonaChurnByNumero(numero: number): ZonaChurnEnum {
		for (const zona of ZONAS_CHURN) {
			if (numero >= zona.min && numero <= zona.max) {
				return zona.descricao;
			}
		}
		return ZonaChurnEnum.SEGURANCA;
	}

	abrirSaibaMais() {
		this.saibaMais = true;
	}

	fecharSaibaMais() {
		this.saibaMais = false;
	}
}
