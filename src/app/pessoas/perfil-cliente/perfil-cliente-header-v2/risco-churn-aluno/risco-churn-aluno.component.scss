.toast-churn {
	padding: 8px 16px;
	justify-content: center;
	align-items: center;
	border-radius: 5px;
	margin-bottom: 8px;
	text-align: center;
	position: relative;
	display: flex;
	font-family: "Nunito Sans";
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 125%;

	p {
		margin: 0;
	}

	i {
		margin-right: 8px;
		margin-top: -1px;
	}

	&.SEGURANCA {
		background-color: #b4fdb4;
		color: #037d03;
	}

	&.ATENCAO {
		background-color: #efd78f;
		color: #705810;
	}

	&.RISCO {
		background-color: #fdfdb4;
		color: #7d7d03;
	}

	&.CRITICA {
		background-color: #f5bcca;
		color: #701028;
	}

	&.DESPEDIDA {
		background-color: #fdb4b4;
		color: #7d0303;
	}

	.saiba-mais {
		color: #1e60fa;
		text-align: center;
		font-feature-settings: "liga" off, "clig" off;
		font-family: Poppins;
		font-size: 12px;
		font-style: normal;
		font-weight: 600;
		line-height: 100%; /* 12px */
		letter-spacing: 0.25px;
		cursor: pointer;
		position: absolute;
		right: 16px;
	}
}

.background-lista-acesso-rapida {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999999;

	.title-sugestoes,
	.sugestoes {
		color: #55585e;
		font-size: 14px;
		font-style: normal;
		font-weight: 700;
		line-height: 125%;
		padding: 0 16px;
	}

	.sugestoes {
		font-weight: 400;
		padding: 16px;
	}

	.atualizacao {
		font-family: Nunito Sans;
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		text-align: right;
		padding: 16px;
	}

	.header-modal-notificacao-versao {
		font-family: Poppins, sans-serif;
		width: 100%;
		height: auto;
		display: grid;
		align-items: center;
		grid-template-columns: 3fr 1fr;
		background: #fafafa;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		padding: 15px 15px 0 15px;
		color: #55585e;
		font-size: 14px;
		font-weight: 600;
	}

	.gaveta-acessos .header-modal-notificacao-versao {
		padding: 15px;

		span {
			font-family: Poppins;
		}
	}

	.gaveta-acessos {
		width: 570px;
		height: 100vh;
		top: 0;
		right: 0;
		position: fixed;
		border-radius: 8px 0px 0px 0px;
		border: 2px solid #c9cbcf;
		background-color: #ffffff;
	}

	.container-gaveta {
		padding-left: 8px;
		padding-right: 8px;
		height: calc(100% - 120px);
		overflow: auto;
	}
}
