<div class="container">
	<pacto-relatorio
		#tableData
		[customContent]="custom"
		[enableZebraStyle]="true"
		[itensPerPage]="itensPerPage"
		[showShare]="false"
		[table]="table"
		actionTitulo="Ações"
		class="contract-table"
		emptyStateMessage="Nenhuma observação adicionada."
		i18n-actionTitulo="@@label-acoes"
		id="modal-observacoes"></pacto-relatorio>

	<ng-template #actions let-data="data" let-index="index" let-item="item">
		<div
			[class.low-opacity]="editingRowIndex != null && editingRowIndex !== index"
			class="action-buttons">
			<button
				(click)="editRow(index, item)"
				class="icon-button"
				title="Editar observação"
				type="button">
				<i class="pct pct-edit cor-azulim05 pct-icon-mr-16 actions-cell"></i>
			</button>
			<button
				(click)="deleteObservation(item, index, data)"
				class="icon-button"
				title="Deletar observação"
				type="button">
				<i
					class="pct pct-trash-2 cor-hellboy05 pct-icon-mr-16 actions-cell"></i>
			</button>
		</div>
	</ng-template>

	<ng-template #custom let-dados="dados" let-visibleColumns="colunas">
		<ng-container
			*ngFor="let row of dados; let lastRow = last; let rowIndex = index">
			<tr id="element-{{ rowIndex }}">
				<!-- Data columns -->
				<td
					*ngFor="let column of visibleColumns; let firstColumn = first"
					[ngClass]="column.styleClass"
					[style.width]="column?.width">
					<ng-container *ngIf="!column.celula && row">
						<span
							[ngClass]="{
								'hover-cell': column.cellPointerCursor,
								'overflow-ellipsis': column.campo === 'observacao'
							}"
							class="column-cell">
							<ng-container *ngIf="!column.celula && column.campo && row">
								{{
									column.valueTransform
										? column.valueTransform(row[column.campo])
										: row[column.campo]
								}}
							</ng-container>
							<ng-container *ngIf="column.celula && row">
								<ng-container
									*ngTemplateOutlet="
										column.celula;
										context: {
											item: row,
											index: rowIndex,
											data: dados
										}
									"></ng-container>
							</ng-container>
						</span>
					</ng-container>

					<ng-container *ngIf="column.celula && row">
						<ng-container
							*ngTemplateOutlet="
								column.celula;
								context: {
									item: row,
									index: rowIndex,
									data: dados
								}
							"></ng-container>
					</ng-container>
				</td>
			</tr>
			<tr *ngIf="editingRowIndex === rowIndex" class="detail">
				<td colspan="8">
					<div class="d-flex justify-content-between">
						<pacto-cat-form-textarea
							[control]="observation"
							id="edita-observacao"
							class="col-8"
							rows="3"></pacto-cat-form-textarea>
						<div class="form-buttons col-4">
							<pacto-cat-button
								(click)="cancelEditing()"
								class="low-opacity"
								label="Cancelar"
								size="LARGE"
								type="OUTLINE_DARK"></pacto-cat-button>
							<pacto-cat-button
								(click)="saveEdit(row)"
								icon="pct pct-check"
								label="Concluir"
								size="LARGE"></pacto-cat-button>
						</div>
					</div>
				</td>
			</tr>
		</ng-container>
	</ng-template>

	<pacto-cat-form-input
		[control]="newObservation"
		i18n-label="@@modal-observacao:nova-observacao"
		i18n-placeholder="@@modal-observacao:mensagem"
		label="Nova observação"
		placeholder="Mensagem"></pacto-cat-form-input>
	<pacto-cat-checkbox
		[control]="importante"
		[label]="
			'Adicionar como uma observação importante para apresentá-la no aplicativo.'
		"
		id="check-importante"></pacto-cat-checkbox>
	<div class="d-flex justify-content-end">
		<pacto-cat-button
			(click)="save()"
			icon="pct pct-save"
			label="Salvar"
			size="LARGE"></pacto-cat-button>
	</div>
</div>

<ng-template #observacaoCell let-item="item">
	<span class="text">{{ item?.observacaoSemHTML }}</span>
</ng-template>
