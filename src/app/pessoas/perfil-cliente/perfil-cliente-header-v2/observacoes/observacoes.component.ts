import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostBinding,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { RestService } from "@base-core/rest/rest.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmCoreApiObservacaoService } from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { combineLatest, Observable } from "rxjs";
import { TreinoApiAlunosService } from "treino-api";
import {
	PactoDataGridConfig,
	PactoDataGridOrdenacaoDirecao,
	RelatorioComponent,
} from "ui-kit";
import { PerfilRecursoPermissoTipo } from "sdk";
import { SessionService } from "@base-core/client/session.service";

declare var moment;

@Component({
	selector: "pacto-observacoes",
	templateUrl: "./observacoes.component.html",
	styleUrls: ["./observacoes.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ObservacoesComponent implements OnInit {
	@HostBinding("class.pacto-observacoes-modal-component")
	styleEncapsulation = true;

	@Input()
	public pessoa: string;
	@Input()
	public matricula: string;
	@Input()
	public usuario: string;
	@Input()
	public cliente: string;
	@Input()
	public observacoesV1;
	permissao2_36: any;

	@Output()
	update = new EventEmitter<void>();

	public table: PactoDataGridConfig;

	newObservation: FormControl = new FormControl();
	importante: FormControl = new FormControl();
	observation: FormControl = new FormControl();
	editingRowIndex: number | null = null;
	editedObservations = [];
	deletedItems = [];
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
	];

	@ViewChild("observacaoCell", { static: true })
	observacaoCell: TemplateRef<any>;

	@ViewChild("actions", { static: true }) actions: TemplateRef<any>;
	@ViewChild("tableData", { static: true }) tableData: RelatorioComponent;

	constructor(
		private readonly restService: RestService,
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private sessionService: SessionService,
		private observacoesService: AdmCoreApiObservacaoService,
		private notificationService: SnotifyService,
		private alunoService: TreinoApiAlunosService
	) {}

	ngOnInit() {
		this.initTable();
		this.permissao2_36 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.36"
			);
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			ghostLoad: true,
			endpointUrl: this.restService.buildFullUrlAdmCore(
				`cliente-observacao/tela-cliente/${this.pessoa}?matricula=${this.matricula}`
			),
			state: {
				paginaNumero: 0,
				paginaTamanho: 5,
				ordenacaoColuna: "",
				ordenacaoDirecao: PactoDataGridOrdenacaoDirecao.ASC,
			},
			columns: [
				{
					nome: "tipo",
					titulo: "Tipo",
					visible: false,
				},
				{
					nome: "observacao",
					titulo: "Observações Anteriores",
					visible: true,
					celula: this.observacaoCell,
					ordenavel: false,
				},
				{
					nome: "importante",
					titulo: "Importante",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => {
						return v ? "Sim" : "Não";
					},
				},
				{
					nome: "usuario",
					titulo: "Feito por",
					visible: true,
					valueTransform: (v) => {
						return v ? v.nome : v;
					},
					ordenavel: false,
				},
				{
					nome: "dataCadastro",
					titulo: "Data",
					visible: true,
					valueTransform: (v) => {
						return v ? moment(v).format("DD/MM/YYYY - HH:mm:ss") : v;
					},
					ordenavel: false,
				},
				{
					nome: "acoes",
					titulo: "Ações",
					celula: this.actions,
					visible: true,
					ordenavel: false,
				},
			],

			dataAdapterFn: (serverData) => {
				serverData.content = [...serverData.content, ...this.observacoesV1];

				serverData.totalElements += this.observacoesV1.length;

				serverData.totalPages = serverData.totalElements / serverData.size;

				return serverData;
			},
		});
	}

	deleteObservation(row, index, data): void {
		if (!this.permiteExcluir()) {
			this.notificationService.error(
				'Você não possui a permissão "2.36 - Observação para o cliente" para excluir essa observação.'
			);
			return;
		}

		if (this.editingRowIndex !== null && this.editingRowIndex !== index) {
			return;
		}

		this.editingRowIndex = null;
		this.deletedItems.push(row);
		data.splice(index, 1);

		const currentPage = this.table.state.paginaNumero;
		const setCurrentPage = (page: number) =>
			(this.table.state.paginaNumero = page);

		if (data.length === 0 && currentPage > 0) {
			setCurrentPage(currentPage - 1);
		}

		this.save();
	}

	editRow(rowIndex: number, row: any): void {
		if (!this.permiteEditar()) {
			this.notificationService.error(
				'Você não possui a permissão "2.36 - Observação para o cliente" para editar essa observação.'
			);
			return;
		}
		if (this.editingRowIndex != null) {
			return;
		}
		this.editingRowIndex = rowIndex;
		row.observacao = row.observacaoSemHTML;
		this.observation.patchValue(row.observacao);
	}

	cancelEditing(): void {
		this.editingRowIndex = null;
	}

	saveEdit(row): void {
		row.observacao = this.observation.value;

		const index = this.editedObservations.findIndex(
			(d) => d.codigo === row.codigo
		);

		if (index === -1) {
			this.editedObservations.push(row);
		} else {
			this.editedObservations[index] = row;
		}

		this.editingRowIndex = null;
		this.save();
	}

	save(): void {
		if (!this.permiteIncluir()) {
			this.notificationService.error(
				'Você não possui a permissão "2.36 - Observação para o cliente" para incluir observação.'
			);
			return;
		}
		const observables: Observable<any>[] = [];

		if (this.editedObservations.length > 0) {
			this.editedObservations.forEach((item) => {
				if (item.tipo === "v1") {
					const data = {
						importante: item.importante,
						observacao: item.observacao,
						cliente: {
							codigo: Number(this.cliente),
						},
						usuario: {
							codigo: Number(this.usuario),
						},
					};
					this.observacoesService
						.addObservacao(data)
						.subscribe((data) => console.log(data));
					observables.push(this.alunoService.removerObservacao(item.codigo));
					this.cd.detectChanges();
				} else {
					observables.push(
						this.observacoesService.updateObservacao(item.codigo, item)
					);
				}
			});
		}
		if (this.deletedItems.length > 0) {
			this.deletedItems.forEach((item) => {
				if (item.tipo === "v1") {
					observables.push(this.alunoService.removerObservacao(item.codigo));
					this.cd.detectChanges();
				} else {
					observables.push(
						this.observacoesService.deleteObservacao(item.codigo)
					);
				}
			});
			this.deletedItems = [];
		}

		if (this.newObservation.value) {
			const data = {
				observacao: this.newObservation.value,
				importante: this.importante.value,
				cliente: {
					codigo: Number(this.cliente),
				},
				usuario: {
					codigo: Number(this.usuario),
				},
			};

			observables.push(this.observacoesService.addObservacao(data));
		}

		combineLatest(observables).subscribe({
			next: (response) => {
				this.notificationService.success("Atualizações realizadas com sucesso");
				this.tableData.reloadData();
				this.update.next();
				this.newObservation.reset();
				this.deletedItems = [];
				this.editedObservations = [];
			},
			error: () => {
				this.notificationService.error(
					"Ocorreu um erro ao tentar salvar as atualizações."
				);
			},
		});
	}

	permiteExcluir(): any {
		const permition = this.permissao2_36;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		return isPermited !== undefined && isPermited;
	}

	permiteIncluir(): any {
		const permition = this.permissao2_36;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	permiteEditar(): any {
		const permition = this.permissao2_36;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	get disableButtonSave(): boolean {
		return (
			this.newObservation.value === null || this.newObservation.value === ""
		);
	}
}
