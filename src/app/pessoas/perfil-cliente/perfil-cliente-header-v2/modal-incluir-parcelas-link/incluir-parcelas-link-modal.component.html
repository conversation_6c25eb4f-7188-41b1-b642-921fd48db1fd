<div class="background-modal-atividades">
	<div [ngClass]="{ confirmando: confirmar }" class="content">
		<div class="header">
			<div class="row">
				<div class="col-md-9">
					<div class="row">
						<div class="col-md-5">
							<div class="titulo-msg">Compartilhar link de pagamento</div>
						</div>
					</div>
				</div>

				<div class="close-wrapper">
					<i (click)="cancelar()" class="pct pct-x"></i>
				</div>
			</div>
		</div>

		<div class="semana-control-wrapper">
			<div class="titulo-aulas">
				<pacto-cat-select
					[control]="tipoControl"
					[id]="'filtro-tipo'"
					[items]="loadTipoItens()"
					class="filter view"
					i18n-label="@@agenda-aulas:labelTipo"
					label="Tipo de compartilhamento"></pacto-cat-select>
			</div>
		</div>

		<div class="pos-header pretty-scroll">
			<div
				#listadiv
				(scroll)="onScroll($event)"
				*ngIf="tipoControl.value === '3'"
				class="lista">
				<table class="scrollable-table">
					<thead>
						<tr>
							<th class="tdcodigo">
								<div class="containerth">
									<i
										(click)="desmarcarTodos()"
										*ngIf="todosMarcados"
										class="pct pct-check-square"></i>
									<i
										(click)="marcarTodos()"
										*ngIf="!todosMarcados"
										class="pct pct-square"></i>
									<span>Código</span>
								</div>
							</th>
							<th class="tddesc">
								<div class="containerth">
									<span>Descrição</span>
								</div>
							</th>
							<th class="tdvenc">
								<div class="containerth">
									<span>Data de vencimento</span>
								</div>
							</th>
							<th class="tdvalor">
								<div class="containerth">
									<span>Valor</span>
								</div>
							</th>
							<th class="tdsituacao">
								<div class="containerth">
									<span>Situação</span>
								</div>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr
							(click)="toggle(parcela?.codigo)"
							*ngFor="
								let parcela of lista;
								let even = even;
								let lastRow = last;
								let rowIndex = index
							"
							[ngClass]="{ 'zebra-row': even }">
							<td class="tdcodigo">
								<div>
									<i
										*ngIf="marcado(parcela?.codigo)"
										class="pct pct-check-square"></i>
									<i
										*ngIf="!marcado(parcela?.codigo)"
										class="pct pct-square"></i>
									<span>{{ parcela?.codigo }}</span>
								</div>
							</td>
							<td class="tddesc">
								<div>
									{{ parcela.descricao }}
								</div>
							</td>
							<td class="tdvenc">
								<div>
									{{ parcela.dataVencimento }}
								</div>
							</td>
							<td class="tdvalor">
								<div>
									{{ parcela.valor }}
								</div>
							</td>
							<td class="tdsituacao">
								<div>
									<div>
										{{ parcela.situacao }}
									</div>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<div class="rodape">
				<div
					*ngIf="tipoControl.value === '3'"
					class="container-conteudo-lado-esquerdo">
					<div
						*ngIf="tipoControl.value === '3'"
						class="alunos-selecionados"
						style="width: 260px">
						<span>Selecionadas</span>
						<span class="total">
							{{ matriculasSelecionadas.length }} | R$
							{{ valorTotalParcelasSelecionadas }}
						</span>
					</div>

					<div
						*ngIf="tipoControl.value === '3'"
						class="alunos-selecionados"
						style="margin-left: 15px">
						<span>Parcelamento:</span>
						<div class="aux-parent" style="margin-left: 10px">
							<select
								[(ngModel)]="parcelamentoOperadoraSelecionado"
								[disabled]="desabilitarSelectParcelamentoOperadora"
								style="border-color: #a6aab1; border-radius: 5px">
								<option id="1" value="1" selected>À Vista</option>
								<option id="2" value="2">2 vezes</option>
								<option id="3" value="3">3 vezes</option>
								<option id="4" value="4">4 vezes</option>
								<option id="5" value="5">5 vezes</option>
								<option id="6" value="6">6 vezes</option>
								<option id="7" value="7">7 vezes</option>
								<option id="8" value="8">8 vezes</option>
								<option id="9" value="9">9 vezes</option>
								<option id="10" value="10">10 vezes</option>
								<option id="11" value="11">11 vezes</option>
								<option id="12" value="12">12 vezes</option>
							</select>
						</div>
					</div>
				</div>

				<div
					*ngIf="tipoControl.value === '1' || tipoControl.value === '2'"
					class="sem-parcelas"></div>
				<div class="right">
					<pacto-cat-button
						(click)="cancelar()"
						i18n-label="@@label-cancelar"
						id="btn-cancelar"
						label="Cancelar"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>

					<pacto-cat-button
						(click)="gerarParcelasSelecionadas()"
						[disabled]="
							(!matriculasSelecionadas ||
								matriculasSelecionadas.length === 0) &&
							tipoControl.value === '3'
						"
						i18n-label="@@label-confirmar-btn"
						id="btn-enviar"
						label="{{
							tipoControl.value === '3'
								? 'Confirmar parcelas e gerar link'
								: 'Confirmar e gerar link'
						}}"
						size="LARGE"
						type="PRIMARY"></pacto-cat-button>
				</div>
			</div>
		</div>
	</div>
</div>
