@import "src/assets/scss/pacto/plataforma-import.scss";

.semana-control-wrapper {
	margin-top: 10px;
	width: 99%;
	margin-left: 5px;
	margin-right: 5px;
	margin-bottom: 10px;
	border-bottom: 1px solid $cinza01;
	height: 80px;

	.titulo-aulas {
		align-items: center;
	}
}

.background-modal-atividades {
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;

	.pos-header {
		padding: 16px;
		display: grid;
		grid-gap: 16px;

		pacto-cat-form-select-filter,
		pacto-cat-form-input {
			margin: 0;
		}

		.warning {
			i {
				margin-right: 10px;
				line-height: 20px;
			}

			display: inline-flex;
			justify-content: center;
			align-items: center;
			color: #55585e;
			font-size: 14px;
			line-height: 20px;
			padding: 8px 16px;
			background-color: #fafafa;
			border-radius: 5px;
		}

		.lista {
			overflow-y: scroll;
			height: 303px;

			.scrollable-table {
				width: 100%;
				border-collapse: collapse;

				th {
					.containerth {
						align-items: center;

						i.orderby {
							cursor: pointer;
							margin-left: 14px;
							font-size: 12px;
							color: #1e60fa;

							&.pct-caret-down {
								font-size: 15px;
								transform: translateY(-4px);
							}
						}
					}
				}

				.tdcodigo {
					text-align: left;
					width: 187px;

					i {
						font-size: 16px;
						color: #1e60fa;
						margin-right: 24px;
					}

					div {
						display: flex;
						text-align: left;
					}
				}

				.tddescricao {
					width: 298px;
					text-align: center;
				}

				.tdvenc,
				.tdvalor,
				.tdsituacao {
					width: 187px;
					text-align: center;
				}

				th {
					border-bottom: 1px solid #d7d8db;
					color: var(--type-default-title, #55585e);
					font-size: 12px;
					font-style: normal;
					font-weight: 700;
					line-height: 125%;
					letter-spacing: 0.25px;
					text-align: center;
					padding: 16px;
				}

				thead {
					background-color: #ffffff;
					z-index: 30;
				}

				tr {
					width: 100%;
				}

				td {
					cursor: pointer;
					padding: 16px;
					color: #797d86;
					font-size: 12px;
					line-height: 16px;
					text-transform: capitalize;
					text-align: center;

					div {
						span {
							margin-left: 10px;
						}

						align-items: center;
					}

					.pills {
						display: flex;
						align-items: center;
						flex-direction: row;
						justify-content: center;

						.status-pill {
							@extend .type-caption-rounded;
							padding: 5px 16px;
							color: $cinza06;
							background-color: $cinza01;
							border-radius: 50px;
							margin: 0 5px 0 0;

							&.visitante,
							&.vi,
							&.normal,
							&.no,
							&.ativo,
							&.at {
								color: #163e9c;
								background-color: #bccdf5;
							}

							&.renovado,
							&.re,
							&.dependente,
							&.de,
							&.freePass,
							&.fr,
							&.gympass,
							&.gy,
							&.diaria,
							&.di {
								color: #107040;
								background-color: #bcf5d9;
							}

							&.inativo,
							&.in,
							&.cancelado,
							&.ca,
							&.desistente,
							&.de,
							&.vencido,
							&.ve {
								background-color: #f5bcca;
								color: #9c1638;
							}

							&.trancado,
							&.tr,
							&.atestado-m-dico,
							&.ferias,
							&.fe {
								background-color: #e4e5e7;
								color: #a1a4aa;
							}

							&.a-vencer,
							&.av {
								background-color: #f5e7bc;
								color: #9c7b16;
							}
						}
					}
				}

				.zebra-row {
					background-color: #fafafa;
				}
			}

			.scrollable-table th,
			.scrollable-table td {
			}

			.scrollable-table thead {
				position: sticky;
				top: 0;
				width: 100%;
			}

			.scrollable-table tbody {
				overflow-y: auto;
				height: 100%;
				width: 100%;
			}
		}

		.rodape {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			grid-template-columns: auto auto;

			.alunos-selecionados {
				border-radius: 8px;
				border: 1px solid var(--Support-Gray-2, #d7d8db);
				color: var(--cor---types-default-Text, #494b50);
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: 16px;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 180px;

				.total {
					font-size: 22px;
					font-weight: 500;
					margin-left: 16px;
				}
			}

			.sem-parcelas {
				width: 210px;
			}

			.container-conteudo-lado-esquerdo {
				display: flex;
				flex-direction: row;
				justify-content: left;
			}

			.right {
				* {
					margin-left: 16px;
				}

				justify-self: end;
			}
		}
	}

	.content {
		.busca-direita {
			text-align: right;
		}

		.busca-esquerda {
			padding-right: 10%;
		}

		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		display: flex;
		flex-direction: column;
		pointer-events: auto;
		background-color: $branco;
		background-clip: padding-box;
		border-radius: 8px;
		outline: 0;
		height: auto;
		width: 1000px;
		max-width: 1000px;
		min-width: 1000px;
		overflow: hidden;

		&.confirmando {
			height: 346px;
			width: 600px;
			max-width: 600px;
			min-width: 600px;

			.warning {
				color: #e10505;
				background-color: #fee6e6;
			}

			.lista {
				display: none;
			}

			.pos-header {
				padding: 16px;
				display: grid;
				grid-template-rows: 121px 70px 40px;

				.alert-sirene {
					text-align: center;

					img {
						height: 100%;
					}
				}
			}
		}
	}
}

.pretty-scroll {
	&::-webkit-scrollbar {
		padding: 11px 0 11px 11px;
		width: 11px;
		height: 18px;
	}

	&::-webkit-scrollbar-thumb {
		min-height: 100px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	&::-webkit-scrollbar-button {
		width: 11px;
		height: 0;
		display: none;
	}

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

.header {
	width: 100%;
	top: 0;
	left: 0;
	padding: 16px 16px 8px 16px;
	border-bottom: 1px solid #dcdddf;

	.titulo-msg {
		line-height: 22px;
		font-size: 16px;
		font-weight: 600;
	}

	.close-wrapper {
		position: absolute;
		right: 26px;
		top: 14px;
		font-size: 20px;
		color: #51555a;
		cursor: pointer;
	}
}

.situacao-aluno,
.situacao-pass,
.situacao-contrato {
	@extend .type-caption-rounded;
	padding: 5px 16px;
	color: $cinza06;
	background-color: $cinza01;
	border-radius: 50px;
	margin: 0 4px 0 0;

	&.alongado {
		width: 136px;
		height: 25px;
		display: block;
	}

	&.abreviado {
		width: 48px;
		height: 25px;
	}

	&.vi.primario {
		color: #163e9c;
		background-color: #bccdf5;
	}

	&.at.primario {
		color: #037d03;
		background-color: #b4fdb4;
	}

	&.in.primario {
		color: #7d0303;
		background-color: #fdb4b4;
	}

	&.tr.primario {
		color: #797d86;
		background-color: #e4e5e7;
	}

	&.no.secundario {
		color: #0a4326;
		background-color: #8fefbf;
	}

	&.di.secundario {
		color: #0a4326;
		background-color: #63e9a6;
	}

	&.pe.secundario {
		color: #0a4326;
		background-color: #1dc973;
	}

	&.av.secundario {
		color: #705810;
		background-color: #efd78f;
	}

	&.ve.secundario {
		color: #705810;
		background-color: #e9c763;
	}

	&.tv.secundario {
		color: #705810;
		background-color: #e2b736;
	}

	&.ca.secundario {
		color: #701028;
		background-color: #f5bcca;
	}

	&.de.secundario {
		color: #701028;
		background-color: #ef8fa7;
	}

	&.in.secundario {
		color: #701028;
		background-color: #e96384;
	}

	&.ae.secundario {
		color: #105870;
		background-color: #63c7e9;
	}

	&.cr.secundario {
		color: #105870;
		background-color: #8fd7ef;
	}

	&.gympass.terciario {
		color: #9c5316;
		background-color: #f5d6bc;
	}

	&.totalPass.terciario {
		color: #9c5316;
		background-color: #efba8f;
	}
}

.situacaoTreino {
	height: 22px;
	width: 123px;
	text-align: center;
	display: inline-block;
	border-radius: 50px;
	padding: 5px 16px 5px 16px;
	text-align: center;
	font-size: 12px;
	font-weight: 400;
	line-height: 12px;
	background-color: $hellboy01;
	color: $hellboy05;

	&.VENCIDO {
		background-color: #efba8f;
		color: #9c5316;
	}

	&.A_VENCER {
		background-color: #e9c763;
		color: #705810;
	}

	&.ATIVO {
		background-color: #8fefbf;
		color: #0a4326;
	}
}
