import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	OnInit,
	ViewChild,
} from "@angular/core";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";

import { DecimalPipe } from "@angular/common";
import { FormControl } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { GridFilterConfig, PactoDataGridConfig } from "ui-kit";
import { ClienteService } from "../../../../microservices/personagem/cliente/cliente.service";

@Component({
	selector: "pacto-modal-incluir-parcelas-link",
	templateUrl: "./incluir-parcelas-link-modal.component.html",
	styleUrls: ["./incluir-parcelas-link-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IncluirParcelasLinkModalComponent implements OnInit {
	@ViewChild("listadiv", { static: true }) listadiv: ElementRef;
	ready = false;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	filtros: any;
	pessoa: number;
	chave: string;
	confirmar = false;
	tipoControl = new FormControl("1");
	todosMarcados = false;
	ordenacao = "";
	lista: Array<any> = [];
	matriculasSelecionadas: Array<number> = [];
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	desabilitarSelectParcelamentoOperadora = true;
	parcelamentoOperadoraSelecionado: string = "1";
	valorTotalParcelasSelecionadas: string = "0,00";

	constructor(
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private clienteService: ClienteService,
		private decimalPipe: DecimalPipe
	) {}

	ngOnInit() {
		this.verificarPermissaoExibirSelectParcelamentoOperadora();
		setTimeout(() => {
			this.listar();
			this.ready = true;
			this.cd.detectChanges();
		}, 100);
	}

	gerarParcelasSelecionadas() {
		this.modal.close({
			parcelasSelecionadas: this.matriculasSelecionadas,
			tipoSelecionado: this.tipoControl.value,
			parcelamentoOperadoraSelecionado: this.parcelamentoOperadoraSelecionado,
		});
	}

	cancelar() {
		this.modal.close();
	}

	loadTipoItens() {
		return [
			{ id: "1", label: "Link para todas parcelas vencidas e vencendo hoje" },
			{ id: "2", label: "Link para todas parcelas em aberto" },
			{ id: "3", label: "Escolher parcelas" },
		];
	}

	onScroll(event: Event) {
		if (this.listadiv) {
			const element = this.listadiv.nativeElement as HTMLElement;
			if (
				element.scrollHeight - element.scrollTop - 2 <=
				element.clientHeight
			) {
				// this.listarAlunos(this.data.page + 1);
			}
		}
	}

	listarAlunos(page: any) {
		this.data.size = 100;
		this.data.page = page;
		this.clienteService
			.obterParcelasAbertoCliente(this.chave, this.pessoa)
			.subscribe((data) => {
				data.forEach((value) => {
					value.valor = this.transformValueMoney(value.valor);
					this.lista.push(value);
				});
				this.data.content = data;
				this.data.totalElements = data.length;
				this.cd.detectChanges();
			});
	}

	marcarTodos() {
		this.todosMarcados = true;
		this.matriculas(true);
	}

	transformValueMoney(v: number): string {
		return this.decimalPipe.transform(
			parseFloat(v.toString().replace(",", ".")),
			"1.2-2"
		);
	}

	desmarcarTodos() {
		this.todosMarcados = false;
		this.matriculas(false);
		this.valorTotalParcelasSelecionadas = "0,00";
		this.cd.detectChanges();
	}

	private matriculas(insert: boolean) {
		if (this.data.content && insert) {
			this.data.content.forEach((value) => {
				if (!this.matriculasSelecionadas.includes(value.codigo)) {
					this.toggle(value.codigo);
				}
			});
		} else if (this.data.content && !insert) {
			this.matriculasSelecionadas = this.matriculasSelecionadas.filter(
				(value) =>
					!this.data.content.filter((value2) => value2.codigo === value)
			);
		}
		this.cd.detectChanges();
	}

	ordenarLista(coluna: string) {
		this.ordenacao = coluna;
		this.listar();
	}

	listar() {
		this.lista = [];
		this.todosMarcados = false;
		this.listarAlunos(1);
	}

	marcado(matricula): boolean {
		// tslint:disable-next-line:radix
		return this.matriculasSelecionadas.includes(parseInt(matricula));
	}

	toggle(matricula) {
		// tslint:disable-next-line:radix
		matricula = parseInt(matricula);
		const indice = this.matriculasSelecionadas.indexOf(matricula);
		if (indice !== -1) {
			this.matriculasSelecionadas.splice(indice, 1);
			this.todosMarcados = false;
		} else {
			this.matriculasSelecionadas.push(matricula);
		}
		this.calcularValorTotalParcelasSelecionadas();
		this.cd.detectChanges();
	}

	verificarPermissaoExibirSelectParcelamentoOperadora() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"PermitirLinkPagamentoParceladoStone",
				"3.42 - Permitir gerar Link de Pagamento Escolher Parcelas"
			)
			.subscribe(
				(response) => {
					this.desabilitarSelectParcelamentoOperadora = false;
					this.cd.detectChanges();
				},
				(error) => {
					this.desabilitarSelectParcelamentoOperadora = true;
					this.cd.detectChanges();
				}
			);
	}

	calcularValorTotalParcelasSelecionadas() {
		this.valorTotalParcelasSelecionadas = "0,00";
		let total = 0;

		this.matriculasSelecionadas.forEach((parcelaSelecionada) => {
			const parcelaEncontrada = this.lista.find(
				(parcela) => parcela.codigo === parcelaSelecionada
			);
			if (parcelaEncontrada && typeof parcelaEncontrada.valor === "string") {
				const valorNumerico = parseFloat(
					parcelaEncontrada.valor.replace(".", "").replace(",", ".")
				);
				if (!isNaN(valorNumerico)) {
					total += valorNumerico;
				}
			}
		});

		if (total > 0) {
			let valorFormatado = total.toFixed(2).replace(".", ",");
			// Adiciona ponto como separador de milhar se tiver mais de 6 caracteres
			if (valorFormatado.length > 6) {
				const partes = valorFormatado.split(",");
				const inteiro = partes[0];
				const decimal = partes[1];

				const comSeparador = inteiro.slice(0, -3) + "." + inteiro.slice(-3);
				valorFormatado = comSeparador + "," + decimal;
			}
			this.valorTotalParcelasSelecionadas = valorFormatado;
		}

		this.cd.detectChanges();
	}
}
