import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClienteMensagem } from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";

@Component({
	selector: "pacto-modal-mensagem-produto-vencido",
	templateUrl: "./modal-mensagem-produto-vencido.component.html",
	styleUrls: ["./modal-mensagem-produto-vencido.component.scss"],
})
export class ModalMensagemProdutoVencidoComponent implements OnInit {
	@Input()
	public chave: string;
	@Input()
	public pessoa: number;
	@Input()
	public usuario: number;
	@Input()
	public cliente: number;
	@Input()
	public clienteMensagem: ClienteMensagem;
	@Output()
	response = new EventEmitter<string>();

	constructor(
		private ngbActiveModal: NgbActiveModal,
		private admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {}

	apagarMensagem() {
		this.admLegadoTelaClienteService
			.excluirClienteMensagemProdutoVencido(
				this.chave,
				this.clienteMensagem.codigo,
				this.pessoa,
				this.usuario
			)
			.subscribe(
				(response) => {
					if (!response.content) {
						this.snotifyService.error(response.meta.message);
					} else {
						this.snotifyService.success("Mensagem excluida");
						this.response.emit("atualizar");
						this.ngbActiveModal.dismiss();
					}
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					console.log(error);
					if (error && error.meta) {
						if (error.meta.error) {
							this.snotifyService.error(error.meta.message);
						}
					}
				}
			);
	}

	naoBloquear() {
		this.admLegadoTelaClienteService
			.desbloquearMsgProdutoVencido(
				this.chave,
				this.clienteMensagem.codigo,
				this.pessoa,
				this.usuario
			)
			.subscribe(
				(response) => {
					if (!response.content) {
						this.snotifyService.error(response.meta.message);
					} else {
						this.snotifyService.success("Mensagem excluida");
						this.response.emit("atualizar");
						this.ngbActiveModal.dismiss();
					}
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					console.log(error);
					if (error && error.meta) {
						if (error.meta.error) {
							this.snotifyService.error(error.meta.message);
						}
					}
				}
			);
	}

	realizarContato() {
		this.response.emit("crm");
		this.ngbActiveModal.dismiss();
	}
}
