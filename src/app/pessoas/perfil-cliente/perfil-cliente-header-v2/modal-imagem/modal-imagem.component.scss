@import "~src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

::ng-deep .mat-dialog-container {
	padding: 0;
	border-radius: 12px;
}

::ng-deep pacto-cat-camera .snap-container {
	margin-top: 0 !important;
	margin-bottom: 0 !important;
}

header {
	display: flex;
	justify-content: space-between;
	padding: 16px;
	border-bottom: 1px solid #dcdddf;

	span {
		@extend .pct-title4;
	}
}

.cursor-pointer {
	cursor: pointer;
}

.content-modal {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	padding: 0px 16px 16px 16px;
}

.tabs {
	display: flex;
	flex-direction: row;
	grid-area: tabs;
	padding-left: 2rem;
	padding-right: 2rem;

	.tab {
		line-height: 30px;
		margin: 0px;
		font-size: 14px;
		color: $cinza05;
		padding: 10px 20px;
		cursor: pointer;

		&.ativa {
			border-bottom: 3px solid #1998fc;
		}

		&:not(.ativa) {
			border-bottom: 3px solid #eff2f7;
		}
	}
}

.selecao-da-imagem,
.apresentacao-da-imagem {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	grid-area: imagem;
}

.selecao-da-imagem {
	pacto-cat-camera::ng-deep {
		video {
			width: 100%;
			height: 100%;
		}
	}

	pacto-cat-file-input {
		display: block;
		width: 100%;
		height: 287.888px;
		padding: 0 2rem 0 2rem;

		&::ng-deep {
			label {
				display: flex;
				height: inherit;
				text-align: center;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}
		}
	}
}

.buttons {
	display: flex;
	justify-content: flex-end;
	gap: 1rem;
}
