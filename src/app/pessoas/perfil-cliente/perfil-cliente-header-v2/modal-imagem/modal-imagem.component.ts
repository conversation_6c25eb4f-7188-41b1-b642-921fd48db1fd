import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Inject,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-imagem",
	templateUrl: "./modal-imagem.component.html",
	styleUrls: ["./modal-imagem.component.scss"],
})
export class ModalImagemComponent implements OnInit {
	@Output() public update: EventEmitter<void> = new EventEmitter<void>();

	public selecionado: "webcam" | "uploadDoComputador" = "webcam";
	public etapa: "vizualizacao" | "edicao" | "finalizacao" = "vizualizacao";
	public enableCapture: boolean = true;

	public formGroup = new FormGroup({
		fotoAluno: new FormControl(""),
		imageName: new FormControl(""),
	});

	public imagem: string = "";

	constructor(
		@Inject(MAT_DIALOG_DATA) public data: any,
		public dialogRef: MatDialogRef<ModalImagemComponent>,
		private readonly cd: ChangeDetectorRef,
		private readonly modal: NgbActiveModal,
		private readonly service: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService
	) {}

	public ngOnInit(): void {
		if (this.data.urlFoto) {
			this.etapa = "vizualizacao";
			this.imagem = this.data.urlFoto;
			this.cd.detectChanges();
		}

		this.formGroup.get("fotoAluno").valueChanges.subscribe((value: string) => {
			if (value) {
				this.etapa = "finalizacao";
				this.imagem = value;
				this.cd.detectChanges();
			}
		});
	}

	public capturar(event: string): void {
		this.enableCapture = false;
		this.formGroup.get("fotoAluno").setValue(event);
	}

	public cancelar(): void {
		this.etapa = "vizualizacao";
		this.formGroup.reset();
		this.enableCapture = true;
		this.imagem = this.data.urlFoto;
	}

	public removerImagem(): void {
		if (this.data.urlFoto) {
			this.service
				.excluirFotoAluno(
					this.sessionService.chave,
					this.data.codigoPessoa,
					this.sessionService.codigoUsuarioZw
				)
				.subscribe(
					(res) => {
						if (res.content) {
							this.snotifyService.success("Imagem excluída com sucesso");
							this.data.urlFoto = this.imagem;
							this.closeModal(true);
							this.update.emit();
						} else {
							this.snotifyService.error(res.meta.message);
						}
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					},
					() => this.cd.detectChanges()
				);
		} else {
			this.tentarNovamente();
		}
	}

	public editarImagem(): void {
		this.etapa = "edicao";
	}

	public tentarNovamente(): void {
		this.formGroup.reset();
		this.enableCapture = true;
		this.imagem = null;
		this.etapa = "edicao";
	}

	public salvarImagem(): void {
		const imageFotoAluno64 = this.imagem.replace(
			/^data:image\/(png|jpg|jpeg);base64,/,
			""
		);
		this.service
			.alterarFotoAluno(
				this.sessionService.chave,
				this.data.codigoPessoa,
				this.sessionService.codigoUsuarioZw,
				imageFotoAluno64
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.snotifyService.success("Imagem salva com sucesso");
						this.data.urlFoto = this.imagem;
						this.closeModal(true);
						this.update.emit();
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	closeModal(value: boolean) {
		this.dialogRef.close(value);
	}
}
