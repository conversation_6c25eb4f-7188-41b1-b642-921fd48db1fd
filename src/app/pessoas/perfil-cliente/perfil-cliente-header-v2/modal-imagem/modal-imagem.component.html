<header>
	<span>Imagem no perfil</span>
	<i
		(click)="closeModal(false)"
		class="pct pct-x cursor-pointer"
		id="btn-close"></i>
</header>

<div *ngIf="etapa === 'edicao'" [ngClass]="selecionado" class="tabs">
	<div
		(click)="selecionado = 'webcam'"
		[ngClass]="{ tab: true, webcam: true, ativa: selecionado === 'webcam' }">
		Webcam
	</div>
	<div
		(click)="selecionado = 'uploadDoComputador'"
		[ngClass]="{
			tab: true,
			uploadDoComputador: true,
			ativa: selecionado === 'uploadDoComputador'
		}">
		Upload do computador
	</div>
</div>

<div
	class="content-modal"
	[ngClass]="{ 'mt-5': etapa === 'vizualizacao' || etapa === 'finalizacao' }">
	<div
		*ngIf="etapa === 'vizualizacao' || etapa === 'finalizacao'"
		class="apresentacao-da-imagem">
		<pacto-cat-person-avatar
			[diameter]="287"
			[parqPositivo]="false"
			[uri]="imagem"></pacto-cat-person-avatar>
	</div>

	<div *ngIf="etapa === 'edicao'" class="selecao-da-imagem">
		<ng-container *ngIf="selecionado === 'webcam'">
			<pacto-cat-camera
				(images)="capturar($event)"
				[height]="537"
				[width]="537">
				<div class="buttons">
					<pacto-cat-button
						(click)="cancelar()"
						*ngIf="etapa === 'edicao'"
						label="Cancelar"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>
					<pacto-cat-button
						label="Capturar"
						pactoCatCameraCapture
						size="LARGE"
						type="PRIMARY"></pacto-cat-button>
				</div>
			</pacto-cat-camera>
		</ng-container>

		<ng-container *ngIf="selecionado === 'uploadDoComputador'">
			<pacto-cat-file-input
				class="mt-5"
				[control]="formGroup.get('fotoAluno')"
				[nomeControl]="formGroup.get('imageName')"
				formatos=".JPG, .JPEG, .PNG"
				formatosValidos="(jpeg|jpg|png)$"></pacto-cat-file-input>
		</ng-container>
	</div>

	<div
		class="buttons actions"
		[ngClass]="{ 'mt-3': etapa === 'vizualizacao' || etapa === 'finalizacao' }">
		<pacto-cat-button
			class="mt-3"
			(click)="cancelar()"
			*ngIf="etapa === 'edicao' && selecionado === 'uploadDoComputador'"
			label="Cancelar"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="removerImagem()"
			*ngIf="etapa === 'vizualizacao'"
			[disabled]="!imagem"
			label="Remover imagem"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="editarImagem()"
			*ngIf="etapa === 'vizualizacao'"
			[label]="imagem ? 'Alterar imagem' : 'Adicionar imagem'"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
		<pacto-cat-button
			(click)="tentarNovamente()"
			*ngIf="etapa === 'finalizacao'"
			label="Tentar novamente"
			size="LARGE"
			type="OUTLINE"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvarImagem()"
			*ngIf="etapa === 'finalizacao'"
			label="Salvar imagem"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
