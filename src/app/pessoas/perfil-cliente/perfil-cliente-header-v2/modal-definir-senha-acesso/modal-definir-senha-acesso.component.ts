import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ClienteDadosPessoais } from "adm-core-api";
import { SdkValidators } from "sdk";
import { debounceTime, delay } from "rxjs/operators";

@Component({
	selector: "pacto-modal-definir-senha-acesso",
	templateUrl: "./modal-definir-senha-acesso.component.html",
	styleUrls: ["./modal-definir-senha-acesso.component.scss"],
})
export class ModalDefinirSenhaAcessoComponent implements OnInit {
	dadosPessoais: ClienteDadosPessoais;
	form: FormGroup;
	errorMsgPsw = "";

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.createForm();
		this.form
			.get("confirmSenha")
			.valueChanges.pipe(debounceTime(200))
			.subscribe((v) => {
				if (this.form.get("confirmSenha").hasError("required")) {
					this.errorMsgPsw = "Campo obrigatório";
				} else if (this.form.get("confirmSenha").hasError("diffPassword")) {
					this.errorMsgPsw = "As senha não coincidem";
				} else {
					this.errorMsgPsw = "";
				}
			});

		this.telaClienteService
			.obterLiberarSenhaAcesso(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa
			)
			.subscribe((response) => {
				this.form
					.get("habilitarSenhaAcesso")
					.setValue(response.content, { emitEvent: false });
				this.cd.detectChanges();
			});

		this.form
			.get("habilitarSenhaAcesso")
			.valueChanges.pipe(delay(300))
			.subscribe((v) => {
				if (!v) {
					this.salvar(false);
				}
			});
	}

	createForm() {
		this.form = new FormGroup({
			newPassword: new FormControl("", Validators.required),
			confirmSenha: new FormControl("", [
				Validators.required,
				SdkValidators.comparePassword,
			]),
			habilitarSenhaAcesso: new FormControl(),
		});
	}

	salvar(close?) {
		this.telaClienteService
			.cadastrarSenhaDeAcesso(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.form.get("habilitarSenhaAcesso").value,
				this.dadosPessoais.codigoCliente,
				this.sessionService.empresaId,
				this.form.get("newPassword").value,
				this.form.get("confirmSenha").value
			)
			.subscribe(
				(response) => {
					this.notificationService.success(response.content);
					if (close) {
						this.dialog.close();
					}
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}
}
