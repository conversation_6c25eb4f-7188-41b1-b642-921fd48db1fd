<div class="modal-definir-senha-acesso">
	<div class="mdsa-nome-cliente">
		<div class="mdsa-nc-title">Nome do cliente</div>
		<div class="mdsa-nc-info">
			{{ dadosPessoais?.nome }}
		</div>
	</div>

	<form [formGroup]="form">
		<pacto-cat-checkbox
			[control]="form.get('habilitarSenhaAcesso')"
			id="inpt-mdsa-habilitar-senha"
			label="Habilitar senha de acesso"></pacto-cat-checkbox>
		<ng-container *ngIf="form.get('habilitarSenhaAcesso').value">
			<div class="mdsa-inputs-psw">
				<pacto-cat-form-input
					[control]="form.get('newPassword')"
					id="inpt-mdsa-senha"
					label="Senha de acesso"
					pactoOnlyNumber
					type="password"></pacto-cat-form-input>
				<pacto-cat-form-input
					[control]="form.get('confirmSenha')"
					[errorMsg]="errorMsgPsw"
					id="inpt-mdsa-confirm-senha"
					label="Confirmar senha"
					pactoOnlyNumber
					type="password"></pacto-cat-form-input>
			</div>
			<div style="margin-bottom: 20px">
				<i class="pct pct-info" style="margin-right: 8px"></i>
				Informe uma senha de 5 números para ser usada na catraca.
			</div>
		</ng-container>
	</form>
	<div
		*ngIf="form.get('habilitarSenhaAcesso').value"
		class="btn-row-modal-definir-senha-acesso">
		<pacto-cat-button
			(click)="salvar(true)"
			id="btn-mdsa-confirmar"
			label="Confirmar"
			size="LARGE"></pacto-cat-button>
	</div>
</div>
