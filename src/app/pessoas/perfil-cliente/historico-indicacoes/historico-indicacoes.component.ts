import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";
import {
	DataFiltro,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SnotifyService } from "ng-snotify";

declare var moment;

@Component({
	selector: "pacto-historico-indicacoes",
	templateUrl: "./historico-indicacoes.component.html",
	styleUrls: ["./historico-indicacoes.component.scss"],
})
export class HistoricoIndicacoesComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("celulaIndicados", { static: true })
	public celulaIndicados;
	@ViewChild("celulaResponsavel", { static: true })
	public celulaResponsavel;
	@ViewChild("celulaEvento", { static: true })
	public celulaEvento;
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	table: PactoDataGridConfig;
	matricula: string;
	link: string;
	temDados = false;

	constructor(
		private route: ActivatedRoute,
		private restService: RestService,
		private snotify: SnotifyService,
		private router: Router,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");
		this.link = `/pessoas/perfil-v2/${this.matricula}`;
		this.initTable();
	}

	initTable(): void {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.restService.buildFullUrlAdmCore(
				`indicacoes/find-by-matricula/${this.matricula}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.temDados = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "dia",
					titulo: "Data",
					visible: true,
					ordenavel: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
					orderColumn: "dataInicio",
				},
				{
					nome: "evento",
					titulo: "Evento",
					visible: true,
					ordenavel: true,
					celula: this.celulaEvento,
				},
				{
					nome: "indicados",
					titulo: "Indicados",
					visible: true,
					ordenavel: false,
					celula: this.celulaIndicados,
					valueTransform: (v: any) => v,
				},
				{
					nome: "responsavelCadastro",
					titulo: "Responsável Cadastro",
					visible: true,
					ordenavel: true,
					celula: this.celulaResponsavel,
				},
			],
		});
	}
}
