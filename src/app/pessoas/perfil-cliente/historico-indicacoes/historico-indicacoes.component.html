<div class="nav-aux">
	<a
		[routerLink]="['/pessoas', 'perfil-v2', matricula]"
		class="top-navigation"
		id="voltar-alunos">
		<i class="pct pct-arrow-left"></i>
		<span i18n="@@tela-do-cliente:title:historico-indicacoes">
			Histórico de indicações
		</span>
	</a>
</div>
<pacto-cat-card-plain>
	<pacto-relatorio
		#tableData
		[emptyStateMessage]="'Nenhuma indicação registrada'"
		[enableZebraStyle]="true"
		[showShare]="false"
		[table]="table"></pacto-relatorio>
</pacto-cat-card-plain>

<ng-template #celulaResponsavel let-item="item">
	<p>{{ item?.responsavelCadastro?.nome | captalize }}</p>
</ng-template>

<ng-template #celulaEvento let-item="item">
	<p *ngIf="item?.evento?.descricao">
		{{ item?.evento?.descricao | captalize }}
	</p>
	<p *ngIf="!item?.evento?.descricao">-</p>
</ng-template>

<ng-template #celulaIndicados let-indicacao="item">
	<p *ngFor="let item of indicacao?.indicados">
		{{ item?.nomeIndicado | captalize }}
	</p>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@tela-cliente-indicacoes:get-student-error"
		xingling="get-aluno-error">
		Erro ao buscar dados do aluno.
	</span>
	<span
		i18n="@@tela-cliente-indicacoes:invalid-student-error"
		xingling="aluno-invalido">
		Desculpe, não foi possível encontrar informações referentes ao aluno com
		essa matrícula.
	</span>
	<span i18n="@@tela-cliente-indicacoes:redirect-msg" xingling="redirect-msg">
		Em instantes vocês será redirecionado para a tela de listagem.
	</span>
</pacto-traducoes-xingling>
