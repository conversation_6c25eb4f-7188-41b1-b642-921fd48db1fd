<div [hidden]="data" class="div-empty">
	<pacto-cat-card-plain>
		<div class="d-flex flex-column align-items-center">
			<img class="icon-empty" src="assets/images/empty-state-nota-fiscal.svg" />
			<div class="text-empty mt-2 body-text-empty">
				O aluno ainda não possui nenhuma nota fiscal registrada!
			</div>
		</div>
	</pacto-cat-card-plain>
</div>
<div [hidden]="!data">
	<pacto-cat-card-plain>
		<pacto-relatorio
			#tableData
			*ngIf="table"
			[emptyStateMessage]="'Nenhuma nota fiscal registrada para este aluno'"
			[enableZebraStyle]="true"
			[showShare]="false"
			[tableTitle]="'Notas Fiscais'"
			[table]="table"
			class="table-nota-fiscal"
			idSuffix="pch-table-nota-fiscal"></pacto-relatorio>
	</pacto-cat-card-plain>
	<ng-template #cellTipo let-item="item">
		<div *ngIf="item.tipo === 0">NF-e</div>
		<div *ngIf="item.tipo === 1">NFS-e</div>
		<div *ngIf="item.tipo === 2">NFC-e</div>
	</ng-template>
	<ng-template #cellAcoes let-item="item">
		<div class="acoes-nota-fiscal">
			<a
				(click)="imprimirNotaSesi(item)"
				*ngIf="
					configuracaoSistema.utilizarServicoSesiSc &&
					item.linkPDF &&
					item.linkPDF.length > 0
				"
				[darkTheme]="true"
				[pactoCatTolltip]="'Download do PDF'">
				<i class="pct pct-file cor-azulim05"></i>
			</a>
			<a
				*ngIf="
					!configuracaoSistema.utilizarServicoSesiSc &&
					item.linkPDF &&
					item.linkPDF.length > 0
				"
				[darkTheme]="true"
				[pactoCatTolltip]="'Download do PDF'"
				href="{{ item.linkPDF }}"
				role="button"
				target="_blank">
				<i class="pct pct-file cor-azulim05"></i>
			</a>
			<a
				*ngIf="
					!configuracaoSistema.utilizarServicoSesiSc &&
					item.linkXML &&
					item.linkXML.length > 0
				"
				[darkTheme]="true"
				[pactoCatTolltip]="'Download do XML'"
				href="{{ item.linkXML }}"
				role="button"
				target="_blank">
				<img src="assets/images/pct-xml.svg" style="padding-bottom: 4px" />
			</a>
			<a
				(click)="enviarEmail(item)"
				*ngIf="
					!configuracaoSistema.utilizarServicoSesiSc &&
					item.linkPDF &&
					item.linkPDF.length > 0
				"
				[darkTheme]="true"
				[pactoCatTolltip]="'Enviar por e-mail'">
				<i class="pct pct-send cor-azulim05"></i>
			</a>
		</div>
	</ng-template>
</div>
