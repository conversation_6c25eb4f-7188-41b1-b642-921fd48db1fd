import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AdmCoreApiClienteService,
	AdmCoreApiConfiguracaoSistemaService,
	ClienteDadosPessoais,
	ConfiguracaoSistema,
} from "adm-core-api";
import { ActivatedRoute } from "@angular/router";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { Api } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { AdmLegadoTelaClienteService } from "adm-legado-api";

declare var moment;

@Component({
	selector: "pacto-nota-fiscal-container",
	templateUrl: "./nota-fiscal-container.component.html",
	styleUrls: ["./nota-fiscal-container.component.scss"],
})
export class NotaFiscalContainerComponent implements OnInit {
	matricula: string;
	dadosPessoais: ClienteDadosPessoais;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	public table: PactoDataGridConfig;
	@ViewChild("cellTipo", { static: false })
	private cellTipo: TemplateRef<any>;
	@ViewChild("cellAcoes", { static: false })
	private cellAcoes: TemplateRef<any>;
	data = false;
	configuracaoSistema: ConfiguracaoSistema;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly msAdmCoreService: AdmCoreApiClienteService,
		private readonly admLegadoService: AdmLegadoTelaClienteService,
		private readonly admCoreApiConfiguracaoSistema: AdmCoreApiConfiguracaoSistemaService,
		private readonly restApi: RestService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");
		this.admCoreApiConfiguracaoSistema
			.getConfiguracaoSistema()
			.subscribe((responseConfigSistema) => {
				this.configuracaoSistema = responseConfigSistema;
				if (this.matricula) {
					this.msAdmCoreService
						.dadosPessoais(this.matricula)
						.subscribe((data) => {
							this.dadosPessoais = data;
							this.initTable(data.codigoPessoa);
						});
					this.cd.detectChanges();
				}
			});
	}

	private initTable(codigoPessoa: number) {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.restApi.buildFullUrl(
				`nota-fiscal/by-pessoa/${codigoPessoa}`,
				true,
				Api.MSADMCORE
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "tipo",
					titulo: "Tipo",
					visible:
						this.configuracaoSistema &&
						!this.configuracaoSistema.utilizarServicoSesiSc,
					ordenavel: true,
					celula: this.cellTipo,
				},
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "serie",
					titulo: "Série",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "rps",
					titulo: "RPS",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "numeroNota",
					titulo: "Nota",
					visible:
						this.configuracaoSistema &&
						!this.configuracaoSistema.utilizarServicoSesiSc,
					ordenavel: true,
				},
				{
					nome: "razaoSocial",
					titulo: "Razão Social",
					visible:
						this.configuracaoSistema &&
						!this.configuracaoSistema.utilizarServicoSesiSc,
					ordenavel: true,
				},
				{
					nome: "dataEmissao",
					titulo: "Dt. Emissão",
					visible: true,
					ordenavel: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
				},
				{
					nome: "dataAutorizacao",
					titulo: "Dt. Envio",
					visible:
						this.configuracaoSistema &&
						this.configuracaoSistema.utilizarServicoSesiSc,
					ordenavel: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
				{
					nome: "statusNota",
					titulo: "Status",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataAutorizacao",
					titulo: "Dt. Autorização",
					visible:
						this.configuracaoSistema &&
						!this.configuracaoSistema.utilizarServicoSesiSc,
					ordenavel: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
				},
				{
					nome: "",
					titulo: "Ações",
					visible: true,
					ordenavel: false,
					celula: this.cellAcoes,
				},
			],
		});
		this.cd.detectChanges();
	}

	enviarEmail(row) {
		this.admLegadoService
			.enviarEmailNotaFiscal(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				row.codigo
			)
			.subscribe(
				(response) => {
					if (response.content) {
						this.snotifyService.success(response.content);
					} else {
						this.snotifyService.error(response.meta.message);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else {
						this.snotifyService.error(
							"Ocorreu um erro inesperado, tente novamente."
						);
					}
				}
			);
	}

	imprimirNotaSesi(row) {
		this.admLegadoService
			.imprimirNotaFiscalSesi(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				row.codigo
			)
			.subscribe(
				(response) => {
					if (response.content) {
						window.open(response.content, "_blank");
					} else {
						this.snotifyService.error(response.meta.message);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.snotifyService.error(err.meta.message);
					} else if (err.meta && err.meta.messageValue) {
						this.snotifyService.error(err.meta.messageValue);
					} else {
						this.snotifyService.error(
							"Ocorreu um erro inesperado, tente novamente."
						);
					}
				}
			);
	}
}
