@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	::ng-deep.columnTitle,
	::ng-deep.action-column {
		@extend .type-p-small-rounded;
		font-size: 14px !important;
		font-weight: 700 !important;
		color: $pretoPri;
	}

	.interagivel {
		color: $azulim06 !important;
		text-decoration: none !important;
	}

	::ng-deep.table-content {
		padding: 20px 0 0 0 !important;
	}

	.action-cell {
		display: flex;
		flex-wrap: wrap;
	}

	.table-title {
		font-family: Poppins;
		font-size: 14px;
		font-weight: 600;
		line-height: 18px;
		letter-spacing: 0.25px;
		text-align: left;
		color: $pretoPri;
	}
}

::ng-deep.table-nota-fiscal > .pacto-table-title-block {
	padding: unset;
}

.centered {
	display: flex;
	justify-content: space-around;
}

.acoes-nota-fiscal {
	a {
		padding-right: 7px;
		cursor: pointer;
	}
}
