import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { LogAtividadeComponent } from "src/app/base/log-atividade/components/log-atividade.component";
import { AcessosComponent } from "./acessos/acessos.component";
import { AvaliacaoContainerComponent } from "./avaliacao/avaliacao-container/avaliacao-container.component";
import { BoletimVisitaComponent } from "./boletim-visita/boletim-visita.component";
import { ConfiguracoesClienteComponent } from "./configuracoes-cliente/configuracoes-cliente.component";
import { ContratosContainerComponent } from "./contratos/contratos-container/contratos-container.component";
import { DetalhamentoContratoComponent } from "./contratos/detalhamento-contrato/detalhamento-contrato.component";
import { ImprimirContratoComponent } from "./contratos/imprimir-contrato/imprimir-contrato.component";
import { CadastrarConvidadoComponent } from "./convidados/cadastrar-convidado/cadastrar-convidado.component";
import { CrmContainerComponent } from "./crm/crm-container/crm-container.component";
import { DocumentosContainerComponent } from "./documentos/documentos-container/documentos-container.component";
import { FinanceiroContainerComponent } from "./financeiro/financeiro-container/financeiro-container.component";
import { GraduacaoComponent } from "./graduacao/graduacao.component";
import { HistoricoIndicacoesComponent } from "./historico-indicacoes/historico-indicacoes.component";
import { PactoPayContainerComponent } from "./pacto-pay/pacto-pay-container/pacto-pay-container.component";
import { PerfilClienteContainerComponent } from "./perfil-cliente-container/perfil-cliente-container.component";
import { PerfilClienteOutletComponent } from "./perfil-cliente-outlet.component";
import { ImprimirFichaComponent } from "./treino/imprimir-ficha/imprimir-ficha.component";
import { TreinoContainerComponent } from "./treino/treino-container/treino-container.component";
import { ProdutosComponent } from "./produtos/produtos.component";
import { IntegracoesGympassComponent } from "./integracoes/integracoes-gympass/integracoes-gympass.component";
import { TotalpassComponent } from "./totalpass/totalpass.component";
import { NotaFiscalContainerComponent } from "./nota-fiscal/nota-fiscal-container/nota-fiscal-container.component";
import { IntegracoesGogoodComponent } from "./integracoes/integracoes-gogood/integracoes-gogood.component";

const routes: Routes = [
	{
		path: "",
		component: PerfilClienteOutletComponent,
		children: [
			{
				path: "acessos",
				loadChildren: () =>
					import("./../../base/acessos/acessos.module").then(
						(m) => m.AcessosModule
					),
			},
			{
				path: "contratos/:aluno-matricula",
				children: [
					{
						path: "detail/:id",
						component: DetalhamentoContratoComponent,
					},
					{
						path: "print/:idContrato",
						component: ImprimirContratoComponent,
						data: {
							fullscreen: true,
						},
					},
				],
			},
			{
				path: "treino/:aluno-matricula",
				children: [
					{
						path: "print/:id-programa/:id-ficha",
						component: ImprimirFichaComponent,
						data: {
							fullscreen: true,
						},
					},
				],
			},
			{
				path: "log-atividade",
				component: LogAtividadeComponent,
			},
			{
				path: ":aluno-matricula/log-atividade/:pessoa-id",
				component: LogAtividadeComponent,
			},
			{
				path: ":aluno-matricula/cadastrar-convidado",
				component: CadastrarConvidadoComponent,
			},
			{
				path: "gympass/:aluno-matricula",
				component: IntegracoesGympassComponent,
			},
			{
				path: "gogood/:aluno-matricula",
				component: IntegracoesGogoodComponent,
			},
			{
				path: ":aluno-matricula/gympass",
				component: IntegracoesGympassComponent,
			},
			{
				path: ":aluno-matricula/gogood",
				component: IntegracoesGogoodComponent,
			},
			{
				path: "gympass",
				component: IntegracoesGympassComponent,
			},
			{
				path: "gogood",
				component: IntegracoesGogoodComponent,
			},
			{
				path: ":aluno-matricula/totalpass",
				component: TotalpassComponent,
			},
			{
				path: "totalpass",
				component: TotalpassComponent,
			},
			{
				path: "boletim-visita/:aluno-matricula",
				component: BoletimVisitaComponent,
			},
			{
				path: "boletim-visita",
				component: BoletimVisitaComponent,
			},
			{
				path: "historico-indicacoes/:aluno-matricula",
				component: HistoricoIndicacoesComponent,
			},
			{
				path: ":aluno-matricula/historico-indicacoes",
				component: HistoricoIndicacoesComponent,
			},
			{
				path: "historico-indicacoes",
				component: HistoricoIndicacoesComponent,
			},
			{
				path: ":aluno-matricula",
				component: PerfilClienteContainerComponent,
				children: [
					{
						path: "contratos",
						component: ContratosContainerComponent,
					},
					{
						path: "treinos",
						component: TreinoContainerComponent,
					},
					{
						path: "avaliacao",
						component: AvaliacaoContainerComponent,
					},
					{
						path: "pactopay",
						component: PactoPayContainerComponent,
					},
					{
						path: "graduacao",
						component: GraduacaoComponent,
					},
					{
						path: "crm",
						component: CrmContainerComponent,
					},
					{
						path: "documentos",
						component: DocumentosContainerComponent,
					},
					{
						path: "financeiro",
						component: FinanceiroContainerComponent,
					},
					{
						path: "acessos",
						component: AcessosComponent,
					},
					{
						path: "produtos",
						component: ProdutosComponent,
					},
					{
						path: "notafiscal",
						component: NotaFiscalContainerComponent,
					},
				],
			},
			{
				path: "configuracoes-cliente/:aluno-matricula/:codigo-pessoa",
				component: ConfiguracoesClienteComponent,
			},
		],
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class PerfilClienteRoutingModule {}
