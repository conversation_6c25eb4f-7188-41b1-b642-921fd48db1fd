<div class="div-empty" [hidden]="data">
	<pacto-cat-card-plain>
		<div class="d-flex flex-column align-items-center">
			<img class="icon-empty" src="assets/images/empty-state-bottle.svg" />
			<div class="text-empty mt-2 body-text-empty">
				O aluno ainda não possui nenhum produto!
			</div>
		</div>
	</pacto-cat-card-plain>
</div>
<div [hidden]="!data">
	<pacto-cat-card-plain>
		<div class="row secao-vencimentos">
			<div class="col-12">
				<span class="table-title">Lista de produtos</span>
			</div>
			<div class="col-5">
				<pacto-cat-form-input
					[control]="formGroup.get('input-pesquisa')"
					icon="pct pct-search"
					id="prod-list-inpt-search"
					placeholder="Busca rápida..."></pacto-cat-form-input>
			</div>
			<div class="col-4"></div>
			<div class="col-3">
				<pacto-cat-form-select
					[control]="formGroup.get('select-filter')"
					[items]="items"
					id="prod-list-select-search"></pacto-cat-form-select>
			</div>
			<div class="col-12">
				<div [hidden]="!data">
					<pacto-relatorio
						#tableData
						(iconClick)="iconClickFn($event)"
						*ngIf="table"
						[baseFilter]="baseFilter"
						[enableZebraStyle]="true"
						[showShare]="false"
						[table]="table"
						actionTitulo="Ações"></pacto-relatorio>
				</div>
			</div>
		</div>
		<div [hidden]="data" class="div-empty">
			<div class="d-flex flex-column align-items-center">
				<img class="icon-empty" src="assets/images/empty-state-bottle.svg" />
				<div class="text-empty mt-2 body-text-empty">
					O aluno ainda não possui nenhum {{ getTitleEmpty() }}!
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>
	<ng-template #columnCod><span>Cód.</span></ng-template>
	<ng-template #columnDescricao><span>Descrição</span></ng-template>
	<ng-template #columnEmpresa><span>Empresa</span></ng-template>
	<ng-template #columnLancamento><span>Lançamento</span></ng-template>
	<ng-template #columnDataInicio><span>Início vigência</span></ng-template>
	<ng-template #columnDataFinal><span>Final vigência</span></ng-template>
	<ng-template #columnRenovacaoAutomatica class="centered">
		<span>Renovação automática</span>
	</ng-template>
	<ng-template #columnValorUnit><span>Valor unit.</span></ng-template>

	<ng-template #cellDataInicioVigencia let-item="item">
		<div *ngIf="item.comVigencia">
			{{ item.dataInicioVigencia | date : "dd/MM/yyyy" }}
		</div>
		<div *ngIf="!item.comVigencia">-</div>
	</ng-template>
	<ng-template #cellDataFinalVigencia let-item="item">
		<div *ngIf="item.comVigencia">
			{{ item.dataFinalVigencia | date : "dd/MM/yyyy" }}
		</div>
		<div *ngIf="!item.comVigencia">-</div>
	</ng-template>
	<ng-template #cellRenovavelAutomaticamente let-item="item">
		<div class="d-flex">
			<span>{{ item.renovavelAutomaticamente ? "Sim" : "Não" }}</span>
			<span
				(click)="renovacaoAutomatica(item)"
				*ngIf="apresentarAlterarRenovacaoAutomatica(item)"
				[darkTheme]="true"
				[pactoCatTolltip]="'Alterar renovação automática'"
				style="padding-left: 10px">
				<i class="pct pct-repeat cor-azulim05" style="font-size: 16px"></i>
			</span>
		</div>
	</ng-template>
</div>
