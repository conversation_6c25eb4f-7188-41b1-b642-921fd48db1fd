@import "src/assets/scss/pacto/plataforma-import.scss";

.pacto-imprimir-treino-ficha-component {
	.row-atividade * {
		pointer-events: none;
	}

	.row-atividade {
		.infos-atividade {
			width: 33ch;
		}

		.campo-serie:not(:nth-child(2)) {
			width: 11% !important;
		}

		.campo-serie:nth-child(2) {
			width: 21ch !important;
			overflow-wrap: break-word;
		}
	}

	pacto-configurar-ficha .listagem-atividades {
		margin-bottom: 0.5rem !important;
	}

	.doNotPrint {
		margin-bottom: 2rem;
	}

	.doNotPrint.center-aux,
	pacto-configurar-ficha .center-aux {
		width: calc(100vw - 100px);
		max-width: 1400px;
		@media (max-width: $plataforma-breakpoint-large) {
			width: calc(100vw - 100px);
		}
	}

	footer,
	.detalhes-atividade,
	#replicar-series-repeticoes,
	.detalhar-todos,
	.pct-edit-3,
	[cdk<PERSON><PERSON>dle],
	pacto-adicionar-atividade {
		display: none !important;
	}

	@media print {
		.doNotPrint,
		pacto-cat-person-avatar {
			display: none !important;
		}
	}
}
