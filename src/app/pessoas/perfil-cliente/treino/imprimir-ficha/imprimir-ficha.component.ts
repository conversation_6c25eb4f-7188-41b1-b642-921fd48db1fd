import {
	ChangeDetectorRef,
	Component,
	ViewEncapsulation,
	HostBinding,
	OnInit,
	ComponentFactoryResolver,
	ViewChild,
	ViewContainerRef,
	ElementRef,
} from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import {
	PerfilAcessoRecursoNome,
	TreinoApiAlunosService,
	TreinoApiProgramaService,
} from "treino-api";
import { TraducoesXinglingComponent } from "ui-kit";
import { ActivatedRoute } from "@angular/router";

import { ConfigurarFichaService } from "../../../../treino/montagem-treino/configurar-ficha.service";
import { ConfigurarFichaComponent } from "../../../../treino/montagem-treino/configurar-ficha/configurar-ficha.component";
import { combineLatest } from "rxjs";

@Component({
	selector: "pacto-imprimir-ficha",
	templateUrl: "./imprimir-ficha.component.html",
	styleUrls: ["./imprimir-ficha.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ImprimirFichaComponent implements OnInit {
	@HostBinding("class.pacto-imprimir-treino-ficha-component")
	styleEncapsulation = true;
	permissaoProgramaTreino;

	@ViewChild("componentHolder", { static: true, read: ViewContainerRef })
	componentHolder: ViewContainerRef;
	@ViewChild("modalContainer", { static: true })
	modalContainer: ElementRef<HTMLElement>;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;

	constructor(
		private sanitized: DomSanitizer,
		private componentFactoryResolver: ComponentFactoryResolver,
		private cd: ChangeDetectorRef,
		private programaService: TreinoApiProgramaService,
		private snotify: SnotifyService,
		private sessionService: SessionService,
		private configurarFichaService: ConfigurarFichaService,
		private route: ActivatedRoute,
		private alunoService: TreinoApiAlunosService
	) {}

	ngOnInit(): void {
		const matricula = this.route.snapshot.paramMap.get("aluno-matricula");
		// double == covers null and undefined
		if (Number(matricula) == null) {
			window.close();
		}

		this.carregarPermissoes();

		if (!this.permissaoProgramaTreino.consultar) {
			this.snotify.warning(this.notificacoesTranslate.getLabel("semPermissao"));
			return;
		}

		const idPrograma = this.route.snapshot.paramMap.get("id-programa");
		const idFicha = this.route.snapshot.paramMap.get("id-ficha");

		this.componentHolder.clear();
		combineLatest([
			this.alunoService.obterAlunoCompletoPorMatricula(matricula),
			this.programaService.obterProgramaCompleto(idPrograma.toString()),
		]).subscribe(([aluno, programa]) => {
			if (!aluno || aluno.programas.length === 0) {
				window.close();
			}
			this.configurarFichaService.programa$.next(programa);
			const index = programa.fichas.findIndex((ficha) => ficha.id == idFicha);
			if (index === -1) {
				window.close();
			}

			this.configurarFichaService.selecionarFichaInicial(index);

			// creates a new instance of the component
			const bodyComponent =
				this.componentFactoryResolver.resolveComponentFactory(
					ConfigurarFichaComponent
				);
			// renders the component to the current HTMLElement
			const body = this.componentHolder.createComponent(bodyComponent);

			// assings the values
			body.instance.programa = programa;
			body.instance.aluno = aluno;

			// the data can arrive after the view is ready
			this.cd.detectChanges();
		});
	}

	print(): void {
		window.print();
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
		this.cd.detectChanges();
	}
}
