import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-comprovante-vacina",
	templateUrl: "./modal-comprovante-vacina.component.html",
	styleUrls: ["./modal-comprovante-vacina.component.scss"],
})
export class ModalComprovanteVacinaComponent implements OnInit {
	linkAcesso: string;
	qrCodeAscesso: string;

	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	abrirCartaoVacina(): void {
		window.open(this.linkAcesso, "_blank");
	}
}
