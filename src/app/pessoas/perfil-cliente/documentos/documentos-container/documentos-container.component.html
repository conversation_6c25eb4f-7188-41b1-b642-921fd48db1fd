<pacto-cat-card-plain>
	<span class="section-title">Incluir novos anexos</span>
	<div class="anexo-input">
		<pacto-cat-file-input
			#fileInputComponent
			[control]="formGroup.get('file')"
			[nomeControl]="formGroup.get('nomeArquivo')"></pacto-cat-file-input>
	</div>
</pacto-cat-card-plain>
<pacto-cat-card-plain>
	<span class="section-title">Documentos gerais</span>
	<div class="relatorio-anexos">
		<pacto-documentos-table
			#documentosTableComponent
			(documentosChangeEvent)="documentosChangeEvent($event)"
			(editEvent)="editEvent($event)"
			*ngIf="alunoBase"
			[alunoBase]="alunoBase"
			[isPermissaoExcluir]="isPermissaoExcluirDocumento"
			[permiteExcluirTreino]="permissaoExcluirAnexoTR"></pacto-documentos-table>
	</div>
</pacto-cat-card-plain>

<pacto-cat-card-plain>
	<span class="section-title">Histórico de documentos Par-Q</span>
	<div class="relatorio-parq">
		<pacto-historico-parq-table
			#HistoricoParqTableComponent
			(editEvent)="editEvent($event)"
			*ngIf="alunoBase"
			[alunoBase]="alunoBase"
			[ctx]="chave"
			[empresa]="empresa"
			[usuariozw]="usuariozw"></pacto-historico-parq-table>
	</div>
</pacto-cat-card-plain>

<pacto-cat-card-plain>
	<span class="section-title">Atestados de aptidão física</span>
	<div class="relatorio-atestado">
		<pacto-atestado-medico-table
			#atestadoMedicoTableComponent
			(atestadosMedicosChangeEvent)="atestadosMedicosChangeEvent($event)"
			(editEvent)="editEvent($event)"
			*ngIf="alunoBase"
			[alunoBase]="alunoBase"
			[isPermissaoExcluir]="
				isPermissaoExcluirAtestado
			"></pacto-atestado-medico-table>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@tamanho-limite-arquivo-ultrapassado"
		xingling="tamanho-limite-arquivo-ultrapassado">
		O arquivo deve ter no máximo 8.0MB
	</span>
</pacto-traducoes-xingling>
