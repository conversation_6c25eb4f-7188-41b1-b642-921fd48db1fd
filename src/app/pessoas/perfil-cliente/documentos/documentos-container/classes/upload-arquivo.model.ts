import { TipoUploadEnum } from "./tipo-upload.enum";
import { AlunoAnexoAvaliacaoZW } from "treino-api";
import { AtestadoAptidaoFisica, AtestadoContrato } from "adm-core-api";

export class UploadArquivo {
	tituloModal: string;
	tipo: TipoUploadEnum;
	alunoAnexoAvaliacaoZW: AlunoAnexoAvaliacaoZW;
	atestadoAptidaoFisica: AtestadoAptidaoFisica;
	atestadoContrato: AtestadoContrato;
	nomeArquivo: string;
	nomeArquivoApresentar: string;
	formatoArquivo: string;
	observacao: string;
	arquivoBase64: string;
	urlArquivo: string;
	sizeFileMb: number;
}
