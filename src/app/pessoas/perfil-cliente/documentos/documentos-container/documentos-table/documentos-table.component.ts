import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SnotifyService } from "ng-snotify";

import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";

import {
	AlunoAnexoAvaliacaoZW,
	AlunoBase,
	TreinoApiAlunosService,
} from "treino-api";
import { UploadArquivo } from "../classes/upload-arquivo.model";
import { TipoUploadEnum } from "../classes/tipo-upload.enum";
import { ModalDetalharDocumentoComponent } from "../modal-detalhar-documento/modal-detalhar-documento.component";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { HttpClient } from "@angular/common/http";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

declare var moment;

@Component({
	selector: "pacto-documentos-table",
	templateUrl: "./documentos-table.component.html",
	styleUrls: ["./documentos-table.component.scss"],
})
export class DocumentosTableComponent implements OnInit {
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnNomeArquivoDocumento", { static: true })
	columnNomeArquivoDocumento: TemplateRef<any>;
	@ViewChild("columnDataArquivo", { static: true })
	columnDataArquivo: TemplateRef<any>;
	@ViewChild("columnFormato", { static: true }) columnFormato: TemplateRef<any>;
	@ViewChild("tableDocumentosComponent", { static: false })
	tableDocumentosComponent: RelatorioComponent;
	@Output() documentosChangeEvent: EventEmitter<any> = new EventEmitter();
	@Output() editEvent: EventEmitter<any> = new EventEmitter();
	@Output() deleteEvent: EventEmitter<any> = new EventEmitter();
	@Input() alunoBase: AlunoBase;
	@Input() isPermissaoExcluir: any;
	@Input() permiteExcluirTreino: any;

	dataGridConfig: PactoDataGridConfig;
	documentos: Array<AlunoAnexoAvaliacaoZW>;
	alunoTemDados: boolean = false;

	constructor(
		private readonly rest: RestService,
		private treinoApiAlunosService: TreinoApiAlunosService,
		private admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private notify: SnotifyService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef,
		private http: HttpClient
	) {}

	ngOnInit() {
		this.initDados();
		this.initTable();
	}

	initTable(): void {
		this.dataGridConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlTreino(
				`alunos/${this.alunoBase.id}/anexosAvaliacao?buscarAnexoZw=true`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			columns: [
				{
					nome: "nomeArquivo",
					titulo: this.columnNomeArquivoDocumento,
					visible: true,
					ordenavel: false,
					width: "20%",
				},
				{
					nome: "data",
					titulo: this.columnDataArquivo,
					visible: true,
					ordenavel: false,
					valueTransform(v) {
						return moment(v).format("DD/MM/YYYY");
					},
					date: true,
				},
				{
					nome: "usuario",
					titulo: "Usuário",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "observacao",
					titulo: "Observação",
					visible: true,
					ordenavel: false,
					width: "40%",
				},
				{
					nome: "formatoArquivo",
					titulo: this.columnFormato,
					visible: true,
					ordenavel: false,
				},
			],
			actions: [
				{
					nome: "action-visualizar-documento",
					iconClass: "pct pct-eye cor-azulim05",
					tooltipText: "Visualizar arquivo",
				},
				{
					nome: "action-download-documento",
					iconClass: "pct pct-download cor-azulim05",
					tooltipText: "Baixar arquivo",
				},
				{
					nome: "action-edit-documento",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: "Editar",
					showIconFn: (row) => row.origem !== "ZW",
				},
				{
					nome: "action-edit-documento",
					iconClass: "pct pct-edit cor-cinza05",
					tooltipText:
						"A edição do documento não é possível, pois ele foi adicionado em uma versão anterior",
					showIconFn: (row) => row.origem === "ZW",
				},
				{
					nome: "action-delet-documento",
					iconClass: "pct pct-trash-2 cor-laranjinha05",
					tooltipText: "Excluir",
				},
			],
		});
	}

	initDados() {
		this.treinoApiAlunosService
			.obterAnexosDeAvaliacao(this.alunoBase.id, true)
			.subscribe((response) => {
				this.alunoTemDados = response.length > 0;
				this.cd.detectChanges();
			});
	}

	loadData() {
		this.initDados();
		this.initTable();
		this.cd.detectChanges();

		setTimeout(() => {
			if (
				this.tableDocumentosComponent &&
				this.tableDocumentosComponent.reloadData
			) {
				this.tableDocumentosComponent.reloadData();
			}
		}, 50);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		switch (event.iconName) {
			case "action-visualizar-documento":
				this.visualizar(event.row);
				break;
			case "action-download-documento":
				this.download(event.row);
				break;
			case "action-edit-documento":
				if (event.row.origem === "ZW") {
					return;
				}
				this.edit(event.row);
				break;
			case "action-delet-documento":
				this.delete(event.row);
				break;
		}
	}

	edit(alunoAnexo) {
		const uploadArquivo = new UploadArquivo();
		uploadArquivo.tipo = TipoUploadEnum.DOCUMENTO;
		uploadArquivo.tituloModal = "Documento";
		uploadArquivo.alunoAnexoAvaliacaoZW = alunoAnexo;
		uploadArquivo.nomeArquivo = alunoAnexo.nomeArquivo;
		uploadArquivo.nomeArquivoApresentar =
			alunoAnexo.nomeArquivo + alunoAnexo.formatoArquivo;
		uploadArquivo.observacao = alunoAnexo.observacao;
		uploadArquivo.urlArquivo = alunoAnexo.anexo;
		this.editEvent.emit(uploadArquivo);
	}

	download(row) {
		if (!this.isNullOrEmpty(row.anexo)) {
			window.open(row.anexo);
		} else {
			this.notify.warning("Esse registro não possui anexo!");
		}
	}

	delete(row: any) {
		if (this.permiteExcluirTreino) {
			this.excluir(row);
		} else {
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.isPermissaoExcluir.chave,
					this.isPermissaoExcluir.codigoUsuario,
					this.isPermissaoExcluir.codigoEmpresa,
					this.isPermissaoExcluir.funcionalidade,
					this.isPermissaoExcluir.permissao
				)
				.subscribe(
					(response) => {
						this.excluir(row);
					},
					(httpResponseError) => {
						this.notify.error(httpResponseError.error.meta.message);
					}
				);
		}
	}

	excluir(row: any) {
		if (row.origem === "ZW") {
			this.admLegadoTelaClienteService
				.excluirAnexoZW(
					this.isPermissaoExcluir.chave,
					row.id,
					this.isPermissaoExcluir.codigoUsuario
				)
				.subscribe(
					() => {
						this.notify.success(this.traducao.getLabel("excluido-com-sucesso"));
						this.loadData();
					},
					(error) => {
						this.notify.error(error.message);
					}
				);
		} else {
			this.treinoApiAlunosService.removerAnexoDeAvaliacao(row.id).subscribe(
				() => {
					this.notify.success(this.traducao.getLabel("excluido-com-sucesso"));
					this.loadData();
				},
				(error) => {
					this.notify.error(error.message);
				}
			);
		}
	}

	private isNullOrEmpty(value) {
		return value === null || value === undefined || value === "";
	}

	isAbrirDocumento(formatoArquivo) {
		return (
			formatoArquivo === ".pdf" ||
			formatoArquivo === ".doc" ||
			formatoArquivo === ".docx" ||
			formatoArquivo === ".txt"
		);
	}

	visualizar(row) {
		console.log(row);
		if (!this.isNullOrEmpty(row.anexo)) {
			if (this.isAbrirDocumento(row.formatoArquivo)) {
				const urlAbrir = row.anexo || row.urlArquivo;
				window.open(urlAbrir, "_blank");
			} else {
				const modalRef = this.dialogService.open(
					"Visualizar Documento",
					ModalDetalharDocumentoComponent,
					PactoModalSize.LARGE
				);
				modalRef.componentInstance.rowData = row;
			}
		} else {
			this.notify.warning("Esse registro não possui anexo!");
		}
	}
}
