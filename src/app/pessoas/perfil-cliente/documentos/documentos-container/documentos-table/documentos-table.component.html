<pacto-relatorio
	#tableDocumentosComponent
	(iconClick)="iconClickFn($event)"
	*ngIf="alunoTemDados"
	[enableZebraStyle]="true"
	[showShare]="false"
	[table]="dataGridConfig"
	actionTitulo="Ações"
	class="pacto-relatorio-custom-padding"></pacto-relatorio>

<div *ngIf="!alunoTemDados" class="div-empty">
	<div class="div-interna-empty">
		<img class="icon-empty" src="assets/images/pasta.svg" />
		<div class="titulo-empty">
			O aluno ainda não possui nenhum documento registrado!
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@perfil-cliente-documentos:tooltip-editar"
		xingling="tooltip-editar">
		Editar
	</span>
	<span
		i18n="@@perfil-cliente-documentos:tooltip-excluir"
		xingling="tooltip-excluir">
		Excluir
	</span>
	<span
		i18n="@@perfil-cliente-documentos:excluido-com-sucesso"
		xingling="excluido-com-sucesso">
		Excluido com sucesso!
	</span>
	<span
		i18n="@@perfil-cliente-documentos:tamanho-limite-arquivo-ultrapassado"
		xingling="tamanho-limite-arquivo-ultrapassado">
		O tamanho do arquivo não pode ultrapassar 8MB!
	</span>
</pacto-traducoes-xingling>

<ng-template #columnNomeArquivoDocumento>
	<span
		class="title-column"
		i18n="@@perfil-cliente-documentos:column-nome-arquivo-documento">
		Nome do arquivo
	</span>
</ng-template>
<ng-template #columnFormato>
	<span
		class="title-column"
		i18n="@@perfil-cliente-documentos:column-formato-arquivo-documento">
		Formato
	</span>
</ng-template>
<ng-template #columnDataArquivo>
	<span
		class="title-column"
		i18n="@@perfil-cliente-documentos:column-data-arquivo-documento">
		Data
	</span>
</ng-template>
