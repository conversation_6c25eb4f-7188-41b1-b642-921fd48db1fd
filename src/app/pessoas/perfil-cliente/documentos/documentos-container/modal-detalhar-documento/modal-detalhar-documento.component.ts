import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";
import { Observable } from "rxjs";
import { environment } from "../../../../../../environments/environment";
import { map } from "rxjs/operators";
import { ApiResponseSingle } from "@base-core/rest/rest.model";
import { HttpClient } from "@angular/common/http";

@Component({
	selector: "pacto-modal-detalhar-documento",
	templateUrl: "./modal-detalhar-documento.component.html",
	styleUrls: ["./modal-detalhar-documento.component.scss"],
})
export class ModalDetalharDocumentoComponent implements OnInit {
	constructor(
		private cd: ChangeDetectorRef,
		private http: HttpClient,
		private sanitizer: DomSanitizer
	) {}

	@Input() rowData;
	imagemUrl;
	titleImagem;
	formatoArquivo;
	urlSegura: SafeResourceUrl;
	carregando = false;

	ngOnInit() {
		console.log(this.rowData);
		this.imagemUrl = this.rowData.anexo || this.rowData.urlArquivo;
		this.titleImagem = this.rowData.nome || this.rowData.nomeArquivo;
		this.formatoArquivo = this.rowData.formatoArquivo;
		const urlView =
			"https://docs.google.com/gview?url=" + this.imagemUrl + "&embedded=true";
		console.log(urlView);
		this.urlSegura = this.sanitizer.bypassSecurityTrustResourceUrl(urlView);
		// if (this.isAbrirDocumento()) {
		// this.carregando = true;
		// this.carregarArquivo(urlteste).subscribe((resp) => {
		// 	this.carregando = false;
		// 	this.cd.detectChanges();
		// });
		// }
		this.cd.detectChanges();
	}

	isAbrirDocumento() {
		return (
			this.formatoArquivo === ".pdf" ||
			this.formatoArquivo === ".doc" ||
			this.formatoArquivo === ".docx" ||
			this.formatoArquivo === ".txt"
		);
	}

	carregarArquivo(url): Observable<any> {
		return this.http.get(url);
	}
}
