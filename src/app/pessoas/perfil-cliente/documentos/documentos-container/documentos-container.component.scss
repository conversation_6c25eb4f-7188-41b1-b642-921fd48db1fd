pacto-cat-card-plain {
	padding: 16px;
	padding-bottom: 1px;
	margin-bottom: 20px;

	&:first-of-type {
		padding-bottom: 7px;
	}
}

.section-title {
	color: #55585e;
	font-family: Poppins;
	font-size: 14px;
	font-weight: 600;
	margin: 16px 0px 32px;
}

.anexo-input {
	margin: 32px 16px;
}

.separator {
	margin: 32px 0px;
	border-color: #c9cbcf;
}

:host {
	::ng-deep {
		pacto-relatorio {
			.table-content table.table {
				th {
					font-size: 14px;
					font-weight: 700;
				}

				td {
					font-size: 12px;

					i {
						font-size: 16px;
					}
				}

				.action-cell {
					display: flex;

					i {
						margin: 0 8px;
					}
				}
			}
		}
	}
}
