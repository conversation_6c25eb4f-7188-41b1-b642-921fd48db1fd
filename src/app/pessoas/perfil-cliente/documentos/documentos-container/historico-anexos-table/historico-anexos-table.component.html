<pacto-relatorio
	#relatorioComponent
	(pageChangeEvent)="changePage($event)"
	(pageSizeChange)="changePageSize($event)"
	[itensPerPage]="itensPerPage"
	[showShare]="false"
	[table]="tableDataConfig"
	class="pacto-relatorio-custom-padding"></pacto-relatorio>

<ng-template #columnNomeArquivo>
	<span
		class="title-column"
		i18n="@@perfil-cliente-documentos:column-nome-arquivo">
		Nome do arquivo
	</span>
</ng-template>
<ng-template #columnTipo>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-tipo">
		Tipo
	</span>
</ng-template>
<ng-template #celulaTipoAnexo let-item="item">
	<ng-container>
		<div *ngIf="item.tipo.length < 13">{{ item.tipo }}</div>
		<div *ngIf="item.tipo.length >= 13" title="{{ item.tipo }}">
			{{ item.tipo.substring(0, 10) + "..." }}
		</div>
	</ng-container>
</ng-template>
<ng-template #columnDataUpload>
	<span
		class="title-column"
		i18n="@@perfil-cliente-documentos:column-data-upload">
		Data de upload
	</span>
</ng-template>
<ng-template #celulaNomeDoArquivo let-arquivo="item">
	<ng-container>
		<div *ngIf="arquivo.nomeArquivo.length < 15">{{ arquivo.nomeArquivo }}</div>
		<div
			*ngIf="arquivo.nomeArquivo.length >= 15"
			title="{{ arquivo.nomeArquivo }}">
			{{ arquivo.nomeArquivo.substring(0, 13) + "..." }}
		</div>
	</ng-container>
</ng-template>
