import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";

import {
	RelatorioComponent,
	PactoDataGridConfig,
	PactoDataGridState,
	TableData,
} from "ui-kit";

declare var moment;

@Component({
	selector: "pacto-historico-anexos-table",
	templateUrl: "./historico-anexos-table.component.html",
	styleUrls: ["./historico-anexos-table.component.scss"],
})
export class HistoricoAnexosTableComponent implements OnInit {
	@ViewChild("columnNomeArquivo", { static: true })
	columnNomeArquivo: TemplateRef<any>;
	@ViewChild("columnTipo", { static: true }) columnTipo: TemplateRef<any>;
	@ViewChild("columnDataUpload", { static: true })
	columnDataUpload: TemplateRef<any>;
	@ViewChild("celulaNomeDoArquivo", { static: true })
	celulaNomeDoArquivo: TemplateRef<any>;
	@ViewChild("celulaTipoAnexo", { static: true })
	celulaTipoAnexo: TemplateRef<any>;
	@ViewChild("relatorioComponent", { static: true })
	relatorioComponent: RelatorioComponent;
	@Input() historicoAnexos = new Array<any>();

	urlImagem = "";
	tableDataConfig: PactoDataGridConfig;
	dataTable = {
		content: new Array<{
			nomeArquivo: string;
			tipo: string;
			dataUpload: any;
		}>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	itensPerPage = [{ id: 5, label: "5" }];
	page = 0;
	size = 0;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.initTableHistoricoAnexos();
		if (this.historicoAnexos.length > 0) {
			this.createPageObject();
		}
	}

	initTableHistoricoAnexos() {
		const state: PactoDataGridState = new PactoDataGridState();
		state.paginaTamanho = 5;
		state.paginaNumero = 0;
		this.tableDataConfig = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			columns: [
				{
					nome: "nomeArquivo",
					titulo: this.columnNomeArquivo,
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeDoArquivo,
				},
				{
					nome: "tipo",
					titulo: this.columnTipo,
					visible: true,
					ordenavel: false,
					celula: this.celulaTipoAnexo,
				},
				{
					nome: "dataUpload",
					titulo: this.columnDataUpload,
					visible: true,
					ordenavel: false,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
			],
		});
		this.relatorioComponent.pageSizeControl.setValue(5);
		this.tableDataConfig.dataAdapterFn = (serverData): TableData<any> => {
			serverData = this.dataTable;
			return serverData;
		};
	}

	createPageObject(page = 1, size = 5) {
		this.dataTable.totalElements = this.historicoAnexos.length;
		this.dataTable.size = size;
		this.dataTable.totalPages = Math.ceil(
			+(this.dataTable.totalElements / this.dataTable.size)
		);
		this.dataTable.first = page === 0 || page === 1;
		this.dataTable.last = page === this.dataTable.totalPages;
		this.dataTable.content = this.historicoAnexos.slice(
			size * page - size,
			size * page
		);
		this.relatorioComponent.showBtnAdd = false;
		this.relatorioComponent.reloadData();
		this.cd.detectChanges();
	}

	changePage(page: any) {
		if (page >= 0) {
			this.page = page;
			this.createPageObject(this.page);
		}
	}

	changePageSize(size: any) {
		if (size > 0) {
			this.size = size;
			this.createPageObject(this.page, this.size);
		}
	}

	ordenarHistoricoAnexos() {
		this.historicoAnexos = this.historicoAnexos.sort((a, b) => {
			if (a.dataUpload && b.dataUpload) {
				return b.dataUpload.getTime() - a.dataUpload.getTime();
			}
		});
	}
}
