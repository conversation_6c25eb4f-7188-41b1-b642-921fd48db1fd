<div class="content-modal">
	<div class="content-form" style="margin-top: 32px">
		<a (click)="download()" *ngIf="existeArquivoAnexo()">
			<div [ngClass]="uploadArquivo.urlArquivo ? 'link-arquivo' : ''">
				<div class="document-file">
					<i class="pct-file-text icon-document-upload"></i>
					<div class="file-info">
						<span>{{ this.uploadArquivo.nomeArquivoApresentar }}</span>
						<span *ngIf="this.uploadArquivo.sizeFileMb > 0">
							{{ this.uploadArquivo.sizeFileMb + "Mb" }}
						</span>
					</div>
				</div>
			</div>
		</a>
		<div class="row">
			<div class="col-md-5">
				<pacto-cat-form-select
					[control]="formGroup.get('tipoArquivo')"
					[disabled]="formTipoArquivoEstaDesativado()"
					[items]="tipoUpload"
					i18n-label="@@perfil-cliente-upload-arquivos:label-tipo-arquivo"
					idKey="id"
					label="Tipo"
					labelKey="descricao"></pacto-cat-form-select>
			</div>
			<div class="col-md-5">
				<pacto-cat-form-select-filter
					*ngIf="isAtestadoAptidaoFisica()"
					[addtionalFilters]="getAddtionlaFiltersProdutoAtestado()"
					[control]="formGroup.get('produto')"
					[endpointUrl]="getUrlProdutos()"
					[paramBuilder]="produtoSelectBuilder"
					i18n-label="@@perfil-cliente-upload-arquivos:label-produto-atestado"
					idKey="codigo"
					label="Produto"
					labelKey="descricao"
					style="padding-top: 29px; margin: 0"></pacto-cat-form-select-filter>
				<pacto-cat-form-select-filter
					*ngIf="isAtestadoMedicoContrato()"
					[control]="formGroup.get('contrato')"
					[endpointUrl]="getUrlContratos()"
					[paramBuilder]="contratoSelectBuilder"
					i18n-label="@@perfil-cliente-upload-arquivos:label-contratos"
					idKey="codigo"
					label="Contrato"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div *ngIf="existeArquivoAnexo()" class="row">
			<div class="col-md-12">
				<pacto-cat-form-input
					[control]="formGroup.get('nomeArquivo')"
					i18n-label="@@perfil-cliente-upload-arquivos:label-renomear-arquivo"
					label="Renomear arquivo"></pacto-cat-form-input>
			</div>
		</div>
		<div *ngIf="isAtestadoMedico()" class="row">
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="formGroup.get('dataInicialAtestado')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@perfil-cliente-upload-arquivos:error-msg-data-inicio"
					i18n-label="@@perfil-cliente-upload-arquivos:label-data-inicio"
					label="Data de inicio"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-3">
				<pacto-cat-form-input-number
					[formControl]="formGroup.get('quantidadeDias')"
					[maxlength]="4"
					errorMsg="Forneça a quantidade de dias."
					i18n-label="@@perfil-cliente-upload-arquivos:label-quantidade-dias"
					label="Quantidade de dias"></pacto-cat-form-input-number>
			</div>
			<div class="col-md-2">
				<span *ngIf="formGroup.get('quantidadeDias').value >= 30">
					{{ qtdMesesPeriodoAtestado }}
				</span>
			</div>
		</div>
		<div *ngIf="isAtestadoMedicoContrato()" class="row">
			<div class="col-md-5">
				<pacto-cat-form-select-filter
					[addtionalFilters]="getAddtionlaFiltersJustificativa()"
					[control]="formGroup.get('justificativa')"
					[endpointUrl]="getUrlJustificativa()"
					[paramBuilder]="justificativaSelectBuilder"
					i18n-label="@@perfil-cliente-upload-arquivos:label-justificativa"
					idKey="codigo"
					label="Justificativa"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<pacto-cat-form-textarea
					[control]="formGroup.get('observacao')"
					[id]="'input-observacao'"
					[rows]="'5'"
					i18n-label="@@perfil-cliente-upload-arquivos:label-observacao"
					i18n-placeholder="
						@@perfil-cliente-upload-arquivos:placeholder-observacao"
					label="Observação"
					placeholder="Digite a observação"></pacto-cat-form-textarea>
			</div>
		</div>

		<div class="row justify-content-end">
			<pacto-cat-button
				(click)="gravar()"
				[icon]="'pct pct-save'"
				i18n-label="@@perfil-cliente-upload-file:btn-salvar"
				label="Gravar"
				size="NORMAL"
				style="margin: 0px 15px 32px 0px"
				type="PRIMARY"
				width="127px"></pacto-cat-button>
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@perfil-cliente-documentos-upload:saved-success"
		xingling="saved-success">
		Salvo com sucesso!
	</span>
	<span
		i18n="@@perfil-cliente-documentos-upload:observacao-obrigatoria"
		xingling="observacao-obrigatoria">
		O campo "Observação" deve ser informado"!
	</span>
	<span
		i18n="@@perfil-cliente-documentos-upload:quantidade-obrigatoria"
		xingling="quantidade-obrigatoria">
		O campo "Quantidade de dias" deve ser informado"!
	</span>
	<span
		i18n="@@perfil-cliente-contrato-upload:contrato-obrigatoria"
		xingling="contrato-obrigatoria">
		O campo "Contrato" deve ser informado"!
	</span>
	<span
		i18n="@@perfil-cliente-justificativa-upload:justificativa-obrigatoria"
		xingling="justificativa-obrigatoria">
		O campo "Justificativa" deve ser informado"!
	</span>
</pacto-traducoes-xingling>
