@import "~src/assets/scss/pacto/plataforma-import.scss";

@media (min-width: 1050px) {
	::ng-deep .modal-upload-file .modal-lg {
		max-width: 1100px;
	}
}

pacto-cat-form-input-number,
pacto-cat-form-datepicker,
pacto-cat-form-textarea,
pacto-cat-form-input {
	margin: 0px;
	padding: 0px;
}

.content-modal {
	padding: 13px 45px;

	.row {
		align-items: center;
	}

	.link-arquivo {
		cursor: pointer;
	}

	.document-file {
		display: flex;

		.icon-document-upload {
			font-family: "Pacto-Icons-Fonts";
			font-style: normal;
			font-weight: 400;
			font-size: 40px;
			line-height: 40px;
			text-align: center;
			color: $pretoPri;
		}

		.file-info {
			margin-left: 17px;
			display: flex;
			flex-direction: column;
		}
	}
}
