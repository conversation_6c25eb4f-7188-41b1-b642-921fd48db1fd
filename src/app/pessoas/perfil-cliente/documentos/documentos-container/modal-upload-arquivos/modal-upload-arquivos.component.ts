import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { Api } from "@base-core/rest/rest.model";

import { SnotifyService } from "ng-snotify";
import { debounceTime } from "rxjs/operators";

import { TipoUploadEnum } from "../classes/tipo-upload.enum";
import { UploadArquivo } from "../classes/upload-arquivo.model";
import { AlunoBase, TreinoApiAlunosService } from "treino-api";
import { SelectFilterParamBuilder, TraducoesXinglingComponent } from "ui-kit";
import {
	AdmCoreApiAtestadoService,
	AdmCoreApiContratoService,
	Arquivo,
	AtestadoAptidaoFisica,
	ContratoOperacao,
	MovProduto,
	AtestadoContrato,
} from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";

@Component({
	selector: "pacto-modal-upload-arquivos",
	templateUrl: "./modal-upload-arquivos.component.html",
	styleUrls: ["./modal-upload-arquivos.component.scss"],
})
export class ModalUploadArquivosComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Input() alunoBase: AlunoBase;
	@Input() uploadArquivo: UploadArquivo;
	@Input() isPermitidolancamentoDeAtestadoDeAptidaoFisica: boolean;
	@Input() isPermitidolancamentoDeDocumento: boolean;
	formGroup: FormGroup = new FormGroup({
		tipoArquivo: new FormControl(),
		nomeArquivo: new FormControl(""),
		observacao: new FormControl("", Validators.required),
		dataInicialAtestado: new FormControl(new Date()),
		quantidadeDias: new FormControl(0, Validators.min(1)),
		produto: new FormControl(undefined, Validators.required),
		contrato: new FormControl(undefined, Validators.required),
		justificativa: new FormControl(undefined, Validators.required),
	});
	tipoUpload = [];
	qtdMesesPeriodoAtestado = "";
	atestadoContrato: AtestadoContrato = new AtestadoContrato();

	constructor(
		private cd: ChangeDetectorRef,
		public dialog: NgbActiveModal,
		public restApi: RestService,
		public treinoApiAlunoService: TreinoApiAlunosService,
		public admCoreApiContrato: AdmCoreApiContratoService,
		public admCoreApiAtestado: AdmCoreApiAtestadoService,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.tipoUpload = [];
		if (this.isPermitidolancamentoDeAtestadoDeAptidaoFisica) {
			this.tipoUpload.push({
				id: TipoUploadEnum.ATESTADO_APTIDAO_FISICA,
				descricao: "Atestado aptidão física",
			});
		}
		if (this.isPermitidolancamentoDeDocumento) {
			this.tipoUpload.push({
				id: TipoUploadEnum.DOCUMENTO,
				descricao: "Documento",
			});
		}
		this.initForms();
		this.cd.detectChanges();
	}

	initForms() {
		this.formGroup.get("tipoArquivo").setValue(this.uploadArquivo.tipo);
		this.formGroup.get("nomeArquivo").setValue(this.uploadArquivo.nomeArquivo);
		this.formGroup.get("observacao").setValue(this.uploadArquivo.observacao);
		switch (this.uploadArquivo.tipo) {
			case TipoUploadEnum.NOVO:
				this.uploadArquivo.atestadoAptidaoFisica = new AtestadoAptidaoFisica();
				this.uploadArquivo.atestadoAptidaoFisica.movProduto = new MovProduto();
				this.uploadArquivo.atestadoAptidaoFisica.arquivo = new Arquivo();
				this.uploadArquivo.atestadoContrato = new AtestadoContrato();
				this.uploadArquivo.atestadoContrato.contratoOperacao =
					new ContratoOperacao();
			// tslint:disable-next-line:no-switch-case-fall-through
			case TipoUploadEnum.DOCUMENTO:
				break;
			case TipoUploadEnum.ATESTADO_APTIDAO_FISICA: {
				const dataInicial = new Date(
					this.uploadArquivo.atestadoAptidaoFisica.movProduto.dataInicioVigencia
				);
				const dataFinal = new Date(
					this.uploadArquivo.atestadoAptidaoFisica.movProduto.dataFinalVigencia
				);
				this.formGroup.get("dataInicialAtestado").setValue(dataInicial);
				this.formGroup
					.get("quantidadeDias")
					.setValue(this.diferencaEntreDatasEmDias(dataInicial, dataFinal) + 1);
				this.formGroup
					.get("produto")
					.setValue(
						this.uploadArquivo.atestadoAptidaoFisica.movProduto.produto
					);
				this.formGroup.get("produto").disable();
				this.formGroup.get("dataInicialAtestado").disable();
				this.formGroup.get("quantidadeDias").disable();
				break;
			}
			case TipoUploadEnum.ATESTADO_CONTRATO: {
				this.atestadoContrato = this.uploadArquivo.atestadoContrato;
				this.formGroup
					.get("contrato")
					.setValue(this.atestadoContrato.contratoOperacao.contrato);
				this.formGroup.get("contrato").disable();
				this.formGroup
					.get("justificativa")
					.setValue(this.atestadoContrato.justificativaOperacao);
				this.formGroup.get("justificativa").disable();
				const dataInicial = new Date(
					this.atestadoContrato.contratoOperacao.dataInicioEfetivacaoOperacao
				);
				const dataFinal = new Date(
					this.atestadoContrato.contratoOperacao.dataFimEfetivacaoOperacao
				);
				this.formGroup
					.get("quantidadeDias")
					.setValue(this.diferencaEntreDatasEmDias(dataInicial, dataFinal) + 1);
				this.formGroup.get("dataInicialAtestado").setValue(dataInicial);
				this.formGroup.get("dataInicialAtestado").disable();
				this.formGroup.get("quantidadeDias").disable();
				break;
			}
		}

		if (
			this.isNullOrUndefinedOrEmpty(this.uploadArquivo.nomeArquivoApresentar) &&
			!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.urlArquivo)
		) {
			this.uploadArquivo.nomeArquivoApresentar = "arquivo";
		}

		this.formGroup
			.get("quantidadeDias")
			.valueChanges.pipe(debounceTime(500))
			.subscribe((qtdDias) => {
				if (qtdDias > 0) {
					const dataInicial = new Date(
						this.formGroup.get("dataInicialAtestado").value
					);
					const dataFinal = new Date(
						this.formGroup.get("dataInicialAtestado").value
					);
					if (dataInicial) {
						dataFinal.setDate(dataInicial.getDate() + qtdDias);
						const qtdMeses = this.diferenceEntreDatasEmMeses(
							dataInicial,
							dataFinal
						);
						this.qtdMesesPeriodoAtestado =
							qtdMeses > 1 ? qtdMeses + " meses" : qtdMeses + " mês";
						this.atestadoContrato.contrato =
							this.formGroup.get("contrato").value;
						this.atestadoContrato.nrDias = qtdDias;
						this.atestadoContrato.dataInicio = dataInicial.getTime();
						this.atestadoContrato.dataTermino = dataFinal.getTime();
						this.admCoreApiContrato
							.gerarPeriodoRetornoAtestado(this.atestadoContrato)
							.subscribe((response) => {
								this.atestadoContrato = response;
							});
					}
				}
			});

		this.formGroup
			.get("contrato")
			.valueChanges.pipe(debounceTime(500))
			.subscribe((v) => {
				const contrato = this.formGroup.get("contrato").value;
				if (contrato) {
					this.admCoreApiContrato
						.validarSeExisteTrancamentoSemRetorno(contrato.codigo)
						.subscribe(
							() => {},
							(httpErrorResponse) => {
								const err = httpErrorResponse.error;
								if (err.meta && err.meta.messageValue) {
									this.notify.error(err.meta.messageValue);
								}
							}
						);
				}
			});
	}

	diferenceEntreDatasEmMeses(dataInicial: Date, dataFinal: Date) {
		return (
			dataFinal.getMonth() +
			12 * dataFinal.getFullYear() -
			(dataInicial.getMonth() + 12 * dataInicial.getFullYear())
		);
	}

	private diferencaEntreDatasEmDias(dataInicial: Date, dataFinal: Date) {
		const diaMls = 24 * 60 * 60 * 1000;
		const diferencaMls = dataFinal.getTime() - dataInicial.getTime();
		const diferencaEmDias = diferencaMls / diaMls;
		return diferencaEmDias;
	}

	gravar() {
		switch (this.formGroup.get("tipoArquivo").value) {
			case TipoUploadEnum.DOCUMENTO:
				this.gravarDocumento();
				break;
			case TipoUploadEnum.ATESTADO_APTIDAO_FISICA:
				this.gravarAtestadoAptidaoFisica();
				break;
			case TipoUploadEnum.ATESTADO_CONTRATO:
				this.gravarAtestadoContrato();
				break;
			default:
				this.notify.error('O campo "Tipo de arquivo" deve ser informado!');
				break;
		}
	}

	gravarAtestadoContrato() {
		if (!this.atestadoContrato) {
			this.atestadoContrato = new AtestadoContrato();
		}

		this.atestadoContrato.nomeArquivo = this.formGroup.get("nomeArquivo").value;
		this.atestadoContrato.dataInicio = this.formGroup.get(
			"dataInicialAtestado"
		).value;
		this.atestadoContrato.observacao = this.formGroup.get("observacao").value;
		this.atestadoContrato.formatoArquivo = this.uploadArquivo.formatoArquivo;
		this.atestadoContrato.dadosArquivo = this.uploadArquivo.arquivoBase64;
		this.atestadoContrato.contrato = this.formGroup.get("contrato").value;

		if (this.validarCamposContrato(this.atestadoContrato)) {
			const justificativa = this.formGroup.get("justificativa").value;
			if (justificativa) {
				this.atestadoContrato.justificativaOperacao = justificativa;
			}

			if (this.validarCamposDocumento(this.atestadoContrato)) {
				if (this.validarCamposDocumento(this.atestadoContrato)) {
					this.admCoreApiContrato
						.salvarOuAtualizarAtestadoContrato(this.atestadoContrato)
						.subscribe(
							(response) => {
								this.notify.success(this.traducoes.getLabel("saved-success"));
								const tableReload = "atestados";
								this.dialog.close(tableReload);
							},
							(httpErrorResponse) => {
								const err = httpErrorResponse.error;
								if (err.meta && err.meta.message) {
									this.notify.error(err.meta.message);
								}
							}
						);
				}
			}
		}
	}

	gravarAtestadoAptidaoFisica() {
		let atestadoAptidaoFisica: AtestadoAptidaoFisica =
			this.uploadArquivo.atestadoAptidaoFisica;
		if (this.isNullOrUndefinedOrEmpty(atestadoAptidaoFisica)) {
			atestadoAptidaoFisica = new AtestadoAptidaoFisica();
			atestadoAptidaoFisica.arquivo = new Arquivo();
			atestadoAptidaoFisica.movProduto = new MovProduto();
		}

		const dataInicial = new Date(
			this.formGroup.get("dataInicialAtestado").value
		);
		const qtdDias = this.formGroup.get("quantidadeDias").value;
		if (qtdDias <= 0) {
			this.notify.error(this.traducoes.getLabel("quantidade-obrigatoria"));
			return;
		}

		const dataFinal = new Date();
		dataFinal.setDate(dataInicial.getDate() + qtdDias);

		atestadoAptidaoFisica.movProduto.dataInicioVigencia = dataInicial.getTime();
		atestadoAptidaoFisica.movProduto.dataFinalVigencia = dataFinal.getTime();
		atestadoAptidaoFisica.movProduto.produto =
			this.formGroup.get("produto").value;
		if (atestadoAptidaoFisica.arquivo) {
			atestadoAptidaoFisica.arquivo.nome =
				this.formGroup.get("nomeArquivo").value;
			atestadoAptidaoFisica.arquivo.extensao =
				this.uploadArquivo.formatoArquivo;
			atestadoAptidaoFisica.arquivo.dados = this.uploadArquivo.arquivoBase64;
		}
		atestadoAptidaoFisica.observacao = this.formGroup.get("observacao").value;

		if (this.validarCamposDocumento(atestadoAptidaoFisica)) {
			this.admCoreApiAtestado
				.salvarAtestadoAptidaoFisica(
					this.alunoBase.matricula,
					atestadoAptidaoFisica
				)
				.subscribe(
					(response) => {
						this.notify.success(this.traducoes.getLabel("saved-success"));
						const tableReload = "atestados";
						this.dialog.close(tableReload);
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						}
					}
				);
		}
	}

	gravarDocumento() {
		const dto = {
			codigo: undefined,
			data: undefined,
			anexo: undefined,
			arquivoUpload: undefined,
			nomeArquivo: "",
			formatoArquivo: "",
			observacao: "",
		};
		dto.nomeArquivo = this.formGroup.get("nomeArquivo").value;
		dto.observacao = this.formGroup.get("observacao").value;

		if (this.uploadArquivo.alunoAnexoAvaliacaoZW) {
			dto.codigo = this.uploadArquivo.alunoAnexoAvaliacaoZW.id;
			dto.data = this.uploadArquivo.alunoAnexoAvaliacaoZW.data;
			dto.anexo = this.uploadArquivo.alunoAnexoAvaliacaoZW.anexo;
			dto.formatoArquivo =
				this.uploadArquivo.alunoAnexoAvaliacaoZW.formatoArquivo;
		} else {
			dto.data = new Date().getTime();
			dto.arquivoUpload = this.uploadArquivo.arquivoBase64;
			dto.formatoArquivo = this.uploadArquivo.formatoArquivo;
		}
		if (this.validarCamposDocumento(dto)) {
			this.treinoApiAlunoService
				.cadastrarAnexoDeAvaliacao(this.alunoBase.id, dto)
				.subscribe(
					() => {
						this.notify.success(this.traducoes.getLabel("saved-success"));
						const tableReload = "documentos";
						this.dialog.close(tableReload);
					},
					(error) => {
						this.notify.error(error.message);
					}
				);
		}
	}

	produtoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	contratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	justificativaSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["codigo"],
			}),
		};
	};

	getUrlProdutos(): string {
		return this.restApi.buildFullUrl("produtos", true, Api.MSPRODUTO);
	}

	getUrlContratos(): string {
		return this.restApi.buildFullUrl(
			`v1/contrato/matricula/${this.alunoBase.matricula}`,
			true,
			Api.ADMMS
		);
	}

	getUrlJustificativa(): string {
		return this.restApi.buildFullUrl(
			`justificativa-operacao`,
			true,
			Api.MSCADAUX
		);
	}

	getAddtionlaFiltersProdutoAtestado() {
		return { tipoProduto: "AT" };
	}

	getAddtionlaFiltersContrato() {
		return {
			permiteSituacaoAtestadoContrato: true,
			situacoes: ["AT", "IN"],
		};
	}

	getAddtionlaFiltersJustificativa() {
		return { tipoOperacao: "AT" };
	}

	formTipoArquivoEstaDesativado(): boolean {
		// permitir alterar o tipo apenas quando for um novo upload
		if (this.uploadArquivo.tipo !== TipoUploadEnum.NOVO) {
			return true;
		}
		return false;
	}

	isAtestadoMedico(): boolean {
		return (
			this.formGroup.get("tipoArquivo").value ===
				TipoUploadEnum.ATESTADO_APTIDAO_FISICA ||
			this.formGroup.get("tipoArquivo").value ===
				TipoUploadEnum.ATESTADO_CONTRATO
		);
	}

	isAtestadoAptidaoFisica(): boolean {
		return (
			this.formGroup.get("tipoArquivo").value ===
			TipoUploadEnum.ATESTADO_APTIDAO_FISICA
		);
	}

	isAtestadoMedicoContrato(): boolean {
		return (
			this.formGroup.get("tipoArquivo").value ===
			TipoUploadEnum.ATESTADO_CONTRATO
		);
	}

	download() {
		if (!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.urlArquivo)) {
			window.open(this.uploadArquivo.urlArquivo, "_blank");
		}
	}

	private validarCamposDocumento(dto): boolean {
		if (this.isNullOrUndefinedOrEmpty(dto.observacao)) {
			this.notify.error(this.traducoes.getLabel("observacao-obrigatoria"));
			return false;
		}
		return true;
	}

	private validarCamposContrato(dto): boolean {
		if (this.isNullOrUndefinedOrEmpty(dto.contrato)) {
			this.notify.error(this.traducoes.getLabel("contrato-obrigatoria"));
			return false;
		}
		return true;
	}

	private isNullOrUndefinedOrEmpty(value) {
		return value === null || value === undefined || value === "" || value === 0;
	}

	existeArquivoAnexo(): boolean {
		if (
			!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.urlArquivo) ||
			!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.arquivoBase64)
		) {
			return true;
		}
		return false;
	}
}
