<pacto-relatorio
	#relatorioComponent
	(iconClick)="iconClickFn($event)"
	[customEmptyContent]="customEmptyParQ"
	[enableZebraStyle]="true"
	[showShare]="false"
	[table]="tableDataConfig"
	actionTitulo="Ações"
	class="pacto-relatorio-custom-padding"></pacto-relatorio>

<ng-template #customEmptyParQ>
	<div class="div-empty">
		<div class="div-interna-empty">
			<img class="icon-empty" src="assets/images/planilha.svg" />
			<div class="titulo-empty">
				O aluno ainda não possui nenhum formulário Par-Q registrado!
			</div>
		</div>
	</div>
</ng-template>

<ng-template #columnResponsavel>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-atestado">
		Responsável
	</span>
</ng-template>
<ng-template #columnParQ>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-contrato">
		Código
	</span>
</ng-template>
<ng-template #columnData>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-data">
		Data
	</span>
</ng-template>
<ng-template #columnVigencia>
	<span class="title-column" i18n="@@perfil-cliente-documentos:column-vigencia">
		Vigência
	</span>
</ng-template>
<ng-template #statusCell let-item="item">
	<div
		[ngClass]="{
			'status-positivo': item.parQPositivo === 'Positivo',
			'status-vencido': item.parQPositivo === 'Vencido',
			'status-negativo': item.parQPositivo === 'Negativo'
		}">
		{{ item.parQPositivo || "Indefinido" }}
	</div>
</ng-template>
