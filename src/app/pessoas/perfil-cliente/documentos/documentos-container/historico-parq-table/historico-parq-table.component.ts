import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PactoDataGridConfig } from "../../../../../cobranca/components/relatorio-cobranca/data-grid.model";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { AlunoBase } from "../../../../../../../projects/treino-api/src/lib/aluno.model";
import { TraducoesXinglingComponent } from "../../../../../../../projects/ui/src/lib/components/traducoes-xingling/traducoes-xingling.component";
import moment from "moment";
import { RelatorioComponent } from "../../../../../../../projects/ui/src/lib/components/relatorio/relatorio.component";
import {
	TreinoApiAvaliacaoFisicaService,
	TreinoApiAlunosService,
} from "treino-api";
import { ActivatedRoute } from "@angular/router";
import { TableData } from "../../../../../share/share.component";
import { ClientDiscoveryService, SessionService } from "sdk";

@Component({
	selector: "pacto-historico-parq-table",
	templateUrl: "./historico-parq-table.component.html",
	styleUrls: ["./historico-parq-table.component.scss"],
})
export class HistoricoParqTableComponent implements OnInit {
	tableDataConfig: PactoDataGridConfig;
	private matricula: number;
	alunoBase: AlunoBase;
	private codigoCliente: number;
	currentUrl: string;
	token: number;
	historicoParq: Array<any> = [];
	page = 0;
	size = 5;

	@Input() ctx: string;
	@Input() usuariozw: number;
	@Input() empresa: string;

	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnResponsavel", { static: true })
	columnResponsavel: TemplateRef<any>;
	@ViewChild("columnParQ", { static: true }) columnParQ: TemplateRef<any>;
	@ViewChild("columnData", { static: true }) columnData: TemplateRef<any>;
	@ViewChild("columnVigencia", { static: true })
	columnVigencia: TemplateRef<any>;
	@ViewChild("columnStatus", { static: true }) columnStatus: TemplateRef<any>;
	@ViewChild("relatorioComponent", { static: true })
	relatorioComponent: RelatorioComponent;
	@ViewChild("statusCell", { static: true }) statusCell;

	constructor(
		private readonly rest: RestService,
		private readonly treinoApiAlunosService: TreinoApiAlunosService,
		private readonly notify: SnotifyService,
		private readonly cd: ChangeDetectorRef,
		private readonly avaliacaoApiService: TreinoApiAvaliacaoFisicaService,
		private readonly route: ActivatedRoute,
		private discoveryService: ClientDiscoveryService,
		private session: SessionService
	) {}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.currentUrl = this.discoveryService.getUrlMap().zwUrl;
		this.initTable();
	}

	initTable(): void {
		this.treinoApiAlunosService
			.obterAlunoCompletoPorMatricula(this.matricula)
			.subscribe(
				(alunoResponse) => {
					this.alunoBase = alunoResponse;
					this.codigoCliente = this.alunoBase.codigoCliente;

					this.avaliacaoApiService
						.listarAlunosParQ(this.codigoCliente)
						.subscribe(
							(response: any[]) => {
								this.historicoParq = response;
								this.createPageObject();
							},
							(error) => {
								console.error("Erro ao obter os alunos com PAR-Q:", error);
							}
						);

					this.tableDataConfig = new PactoDataGridConfig({
						quickSearch: false,
						ghostLoad: true,
						ghostAmount: 5,
						showFilters: false,
						pagination: true,
						columns: [
							{
								nome: "codigo",
								titulo: this.columnParQ,
								visible: true,
								ordenavel: false,
							},
							{
								nome: "usuario_nome",
								titulo: this.columnResponsavel,
								visible: true,
								ordenavel: false,
							},
							{
								nome: "dataresposta",
								titulo: this.columnData,
								visible: true,
								ordenavel: false,
								date: true,
								valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
							},
							{
								nome: "diasparavencimentoparq",
								titulo: this.columnVigencia,
								visible: true,
								ordenavel: false,
								date: true,
								valueTransform: (v) => {
									if (v === "-") {
										return "-";
									} else {
										return moment(v).format("DD/MM/YYYY");
									}
								},
							},
							{
								nome: "parQPositivo",
								titulo: this.columnStatus,
								visible: true,
								ordenavel: false,
								celula: this.statusCell,
							},
						],
						actions: [
							{
								nome: "action-visualizar-documento",
								iconClass: "pct pct-eye cor-azulim05",
								tooltipText: "Visualizar ParQ",
							},
							{
								nome: "action-repeat-parq",
								iconClass: "pct pct-repeat cor-azulim05",
								tooltipText: "Renovar ParQ",
								showIconFn: (item) => item.parQPositivo === "Vencido",
							},
						],
						dataAdapterFn: (): TableData<any> => {
							return {
								content: this.historicoParq.slice(
									this.page * this.size,
									(this.page + 1) * this.size
								),
								totalElements: this.historicoParq.length,
								size: this.size,
								number: this.page,
							};
						},
					});
					this.relatorioComponent.pageSizeControl.setValue(this.size);
					this.cd.detectChanges();
					this.relatorioComponent.fetchData();
				},
				(error) => {
					console.error("Erro ao obter o aluno completo:", error);
				}
			);
	}

	createPageObject(page = 0, size = 5) {
		this.page = page;
		this.size = size;
		this.relatorioComponent.reloadData();
		this.cd.detectChanges();
	}

	changePage(page: number) {
		if (page >= 0) {
			this.createPageObject(page, this.size);
		}
	}

	changePageSize(size: number) {
		if (size > 0) {
			this.createPageObject(this.page, size);
		}
	}

	loadData() {
		this.initDados();
		this.relatorioComponent.reloadData();
	}

	private initDados() {
		this.cd.detectChanges();
	}

	iconClickFn(event: { row: any; iconName: string }) {
		switch (event.iconName) {
			case "action-visualizar-documento":
				this.visualizar(event.row);
				break;
			case "action-repeat-parq":
				this.edit(event.row);
				break;
		}
	}

	visualizar(row) {
		const matricula = this.matricula.toString();

		this.avaliacaoApiService
			.obterLinkPdfParQAssinaturaDigital(this.ctx, matricula, row.codigo)
			.subscribe(
				(data) => {
					window.open(data.pdfLink, "_blank");
				},
				(error) => {
					console.error("Erro ao obter os links:", error);
				}
			);
	}

	edit(row) {
		const matricula = this.alunoBase.matriculaZW.toString();

		const linkDeAcesso = `${this.currentUrl}/prest/obter-links-apps-pacto?key=${this.ctx}&empresa=${this.empresa}&usuariozw=${this.usuariozw}&tela=par-q&acao=assinaturadigital`;

		fetch(linkDeAcesso)
			.then((response) => response.json())
			.then((data) => {
				const linkAcesso = data.content.linkAcesso;

				if (linkAcesso) {
					const tokenMatch = linkAcesso.match(/token=([a-zA-Z0-9]+)/);
					const token = tokenMatch ? tokenMatch[1] : null;

					if (token) {
						const urlFinal = `${this.currentUrl}/assinatura/contratos.html?token=${token}&tela=par-q&matricula=${matricula}`;
						window.open(urlFinal, "_blank");
					}
				}
			})
			.catch((error) => {
				console.error("Erro ao obter o link de acesso:", error);
				this.notify.error("Erro ao obter o link de acesso.");
			});
	}
}
