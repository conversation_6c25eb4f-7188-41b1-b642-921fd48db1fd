import { CurrencyPipe } from "@angular/common";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";

@Component({
	selector: "pacto-financeiro-container",
	templateUrl: "./financeiro-container.component.html",
	styleUrls: ["./financeiro-container.component.scss"],
	providers: [CurrencyPipe],
})
export class FinanceiroContainerComponent {
	matricula: string;
	dadosPessoais: ClienteDadosPessoais;
	hasDataCompra = false;
	hasDataCompraParcela = false;
	hasDataRecibo = false;

	constructor(
		private route: ActivatedRoute,
		private admCoreService: AdmCoreApiClienteService,
		private cd: ChangeDetectorRef
	) {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.loadData();
	}

	loadData() {
		this.admCoreService.dadosPessoais(this.matricula).subscribe((aluno) => {
			this.dadosPessoais = aluno;
			this.cd.detectChanges();
		});
	}

	setDataPresence(event: any, type: "compra" | "compraParcela" | "recibo") {
		switch (type) {
			case "compra":
				this.hasDataCompra = event;
				break;
			case "compraParcela":
				this.hasDataCompraParcela = event;
				break;
			case "recibo":
				this.hasDataRecibo = event;
				break;
		}
		this.cd.detectChanges();
	}

	get hasData(): boolean {
		return (
			this.hasDataCompra || this.hasDataCompraParcela || this.hasDataRecibo
		);
	}
}
