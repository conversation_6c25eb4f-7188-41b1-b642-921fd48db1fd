import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ClienteDadosPessoais } from "adm-core-api";
import {
	DataFiltro,
	PactoDataGridConfig,
	RelatorioComponent,
	DialogService,
	AutorizacaoAcessoComponent,
	TraducoesXinglingComponent,
	DialogAutorizacaoAcessoComponent,
} from "ui-kit";
import { ModalActionEditVencimentosComponent } from "./modals/modal-action-edit-vencimentos/modal-action-edit-vencimentos.component";
import { RestService } from "@base-core/rest/rest.service";
import { DatePipe, DecimalPipe } from "@angular/common";
import { ModalEditFormaPagamentoComponent } from "../financeiro-compras/modals/modal-action-edit/modal-edit-forma-pagamento.component";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { ModalActionRenovarProdutoComponent } from "./modals/modal-action-renovar-produto/modal-action-renovar-produto.component";
import { FormControl, FormGroup } from "@angular/forms";
import { MatDialog } from "@angular/material";

enum Actions {
	ALTERAR_DATA_VENCIMENTO = "alterar-data-vencimento",
	RENOVAR_PRODUTO_VALIDADE = "renovar-produto-validade",
	VISUALIZAR_UTILIZACAO_AVALIACAO_FISICA = "visualizar-utilizacao-avaliacao-fisica",
}

@Component({
	selector: "pacto-financeiro-produtos-vencimentos",
	templateUrl: "./financeiro-produtos-vencimentos.component.html",
	styleUrls: ["./financeiro-produtos-vencimentos.component.scss"],
})
export class FinanceiroProdutosVencimentosComponent implements AfterViewInit {
	@Input() dadosPessoais: ClienteDadosPessoais;

	formGroup = new FormGroup({
		"input-pesquisa": new FormControl(""),
		"select-filter": new FormControl("Todos os produtos"),
	});
	@ViewChild("tableVencimentosRef", { static: true })
	tableVencimentosRef: RelatorioComponent;
	tableVencimentos: PactoDataGridConfig;
	@ViewChild("tableVencimentosCodigo", { static: true })
	tableVencimentosCodigo: TemplateRef<any>;
	@ViewChild("tableVencimentosDescricao", { static: true })
	tableVencimentosDescricao: TemplateRef<any>;
	@ViewChild("tableVencimentosEmpresa", { static: true })
	tableVencimentosEmpresa: TemplateRef<any>;
	@ViewChild("tableVencimentosDataDaCompra", { static: true })
	tableVencimentosDataDaCompra: TemplateRef<any>;
	@ViewChild("tableVencimentosDataDaFinal", { static: true })
	tableVencimentosDataDaFinal: TemplateRef<any>;
	@ViewChild("tableVencimentosValorTotal", { static: true })
	tableVencimentosValorTotal: TemplateRef<any>;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	filtrosForm = {
		searchTerm: this.formGroup.get("input-pesquisa").value,
		todosOsProdutos: "true",
		produtosEstoque: "false",
		produtoServico: "false",
		produtosVencidos: "false",
		produtosComVencimento: "false",
	};
	baseFilter: DataFiltro = {};
	items = [
		{ id: "Todos os produtos", label: "Todos os produtos" },
		{ id: "Produtos estoque", label: "Produtos estoque" },
		{ id: "Produto serviço", label: "Produto serviço" },
		{ id: "Produtos vencidos", label: "Produtos vencidos" },
		{ id: "Produtos com validade", label: "Produtos com validade" },
	];

	constructor(
		private ngbModal: NgbModal,
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private datePipe: DatePipe,
		private decimalPipe: DecimalPipe,
		private dialogService: DialogService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private matDialog: MatDialog
	) {}

	ngAfterViewInit(): void {
		this.initTableVencimentos();
		this.cd.detectChanges();
		this.formGroup.valueChanges.subscribe(() => {
			let valorInicial = this.formGroup.get("input-pesquisa").value;
			this.filterSelector();
			setTimeout(() => {
				let valorFinal = this.formGroup.get("input-pesquisa").value;
				if (valorInicial === valorFinal) {
					this.tableVencimentosRef.reloadData();
				}
			}, 600);
		});
	}

	resetFilters() {
		this.filtrosForm.searchTerm = "";
		this.filtrosForm.todosOsProdutos = "false";
		this.filtrosForm.produtosEstoque = "false";
		this.filtrosForm.produtoServico = "false";
		this.filtrosForm.produtosVencidos = "false";
		this.filtrosForm.produtosComVencimento = "false";
		this.filtrosForm.todosOsProdutos = "false";
	}

	filterSelector() {
		this.resetFilters();
		this.filtrosForm.searchTerm = this.formGroup.get("input-pesquisa").value;
		switch (this.formGroup.get("select-filter").value) {
			case "Todos os produtos":
				this.filtrosForm.todosOsProdutos = "true";
				break;
			case "Produtos estoque":
				this.filtrosForm.produtosEstoque = "true";
				break;
			case "Produto serviço":
				this.filtrosForm.produtoServico = "true";
				break;
			case "Produtos vencidos":
				this.filtrosForm.produtosVencidos = "true";
				break;
			case "Produtos com validade":
				this.filtrosForm.produtosComVencimento = "true";
				break;
			default:
				this.filtrosForm.todosOsProdutos = "true";
				break;
		}
	}

	initTableVencimentos() {
		this.baseFilter.filters = this.filtrosForm;
		this.tableVencimentos = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`pessoas/${this.dadosPessoais.codigoPessoa}/produtos/tela-cliente`
			),
			columns: [
				{
					nome: "codigo",
					titulo: this.tableVencimentosCodigo,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "empresa",
					titulo: this.tableVencimentosEmpresa,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => v.nome,
				},
				{
					nome: "descricao",
					titulo: this.tableVencimentosDescricao,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataLancamento",
					titulo: this.tableVencimentosDataDaCompra,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyyy"),
				},
				{
					nome: "dataFinalVigencia",
					titulo: this.tableVencimentosDataDaFinal,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyyy"),
				},
				{
					nome: "totalFinal",
					titulo: this.tableVencimentosValorTotal,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						this.decimalPipe.transform(
							parseFloat(v ? v.toString().replace(",", ".") : 0),
							"1.2-2"
						),
				},
			],
			actions: [
				{
					nome: Actions.ALTERAR_DATA_VENCIMENTO,
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: this.traducoes.getLabel("alterar-data"),
					actionFn: (row) => this.actionEdit(row),
				},
				{
					nome: Actions.RENOVAR_PRODUTO_VALIDADE,
					iconClass: "pct pct-refresh-ccw cor-azulim05",
					tooltipText: this.traducoes.getLabel("renovar-produto"),
					showIconFn: (row) =>
						row.dataFinalVigencia && row.dataFinalVigencia !== "",
					actionFn: (row) => this.actionRenew(row),
				},
				{
					nome: Actions.VISUALIZAR_UTILIZACAO_AVALIACAO_FISICA,
					iconClass: "pct pct-zoom-in cor-azulim05",
					tooltipText: this.traducoes.getLabel("visualiza-utilizacoes"),
					actionFn: (row) => this.actionDetail(row),
					showIconFn(row) {
						return row.isDetailed;
					},
				},
			],
		});
	}

	filtrarTabelasPeloProdutoVencido(event) {
		console.log(event);
	}

	iconClickFnTableVencimentos(event) {
		switch (event.iconName) {
			case Actions.ALTERAR_DATA_VENCIMENTO:
				this.actionEdit(event.row);
				break;
			case Actions.RENOVAR_PRODUTO_VALIDADE:
				this.actionRenew(event.row);
				break;
			case Actions.VISUALIZAR_UTILIZACAO_AVALIACAO_FISICA:
				this.actionDetail(event.row);
				break;
		}
	}

	actionEdit(row) {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"AlterarDataVigenciaValidade_Autorizar",
					"3.29 - Alterar data de vigência do produto com validade",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						const dialogRef = this.dialogService.open(
							"Editar vigência de produto",
							ModalActionEditVencimentosComponent
						);
						dialogRef.componentInstance.produto = row.codigo;
						dialogRef.componentInstance.codigoUsuarioValidado =
							response.content;
						dialogRef.componentInstance.produtoDescricao = row.descricao;
						dialogRef.componentInstance.tableRef = this.tableVencimentosRef;
						this.cd.detectChanges();
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	actionRenew(row) {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"VendaAvulsa",
					"4.11 - Vendas avulsas",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						const dialogRef = this.dialogService.open(
							"Renovar produto com vencimento",
							ModalActionRenovarProdutoComponent
						);
						dialogRef.componentInstance.codMovProduto = row.codigo;
						this.cd.detectChanges();
						dialogRef.result
							.then((r) => {
								if (r) {
									this.tableVencimentosRef.reloadData();
								}
							})
							.catch((e) => {});
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	actionDetail(row) {
		console.log("actionDetail", row);
	}
}
