import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { FormControl, FormGroup, Validators } from "@angular/forms";

@Component({
	selector: "pacto-modal-action-renovar-produto",
	templateUrl: "./modal-action-renovar-produto.component.html",
	styleUrls: ["./modal-action-renovar-produto.component.scss"],
})
export class ModalActionRenovarProdutoComponent implements OnInit {
	codMovProduto;
	codProduto;
	form: FormGroup = new FormGroup({
		produto: new FormControl({}, Validators.required),
	});
	produtos: Array<any> = new Array<any>();

	constructor(
		private modal: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.telaClienteService
			.renovarMovProdutoObter(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.codMovProduto
			)
			.subscribe((response) => {
				this.produtos = response.content;
				if (this.codProduto) {
					for (const prod of this.produtos) {
						if (prod.codigo === this.codProduto) {
							this.form.get("produto").setValue(prod.codigo);
							break;
						}
					}
				}
				this.cd.detectChanges();
			});
	}

	renew() {
		this.telaClienteService
			.renovarMovProduto(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.codMovProduto,
				+this.form.get("produto").value,
				this.sessionService.empresaId
			)
			.subscribe((response) => {
				this.notificationService.success("Produto renovado com sucesso!");
				this.modal.close(true);
			});
	}

	cancel() {
		this.modal.close();
	}
}
