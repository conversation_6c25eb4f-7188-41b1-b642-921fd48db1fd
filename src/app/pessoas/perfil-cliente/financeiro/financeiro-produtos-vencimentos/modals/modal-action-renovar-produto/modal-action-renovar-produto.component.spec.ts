import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalActionRenovarProdutoComponent } from "./modal-action-renovar-produto.component";

describe("ModalActionRenovarProdutoComponent", () => {
	let component: ModalActionRenovarProdutoComponent;
	let fixture: ComponentFixture<ModalActionRenovarProdutoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalActionRenovarProdutoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ModalActionRenovarProdutoComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
