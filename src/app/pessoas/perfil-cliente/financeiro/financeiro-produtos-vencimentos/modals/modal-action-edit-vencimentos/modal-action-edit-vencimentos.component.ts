import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-action-edit-vencimentos",
	templateUrl: "./modal-action-edit-vencimentos.component.html",
	styleUrls: ["./modal-action-edit-vencimentos.component.scss"],
})
export class ModalActionEditVencimentosComponent implements OnInit {
	produto: number;
	produtoDescricao: string;
	produtoData: any;
	codigoUsuarioValidado: number;
	tableRef: RelatorioComponent;
	formGroup = new FormGroup({
		date: new FormControl("", [Validators.required]),
	});

	constructor(
		private dialog: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		if (this.produtoData) {
			this.formGroup.get("date").setValue(this.produtoData);
		}
	}

	salvar() {
		this.telaClienteService
			.alterarVigenciaProduto(
				this.sessionService.chave,
				this.produto,
				this.codigoUsuarioValidado,
				this.formGroup.get("date").value
			)
			.subscribe(
				(response) => {
					this.notificationService.success("Data alterada com sucesso!");
					this.tableRef.reloadData();
					this.dialog.close(true);
				},
				(error) => this.notificationService.error(error.error.meta.message)
			);
	}

	close() {
		this.dialog.close();
	}
}
