import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { Api } from "@base-core/rest/rest.model";

declare var moment;

@Component({
	selector: "pacto-modal-log-conciliadora",
	templateUrl: "./modal-log-conciliadora.component.html",
	styleUrls: ["./modal-log-conciliadora.component.scss"],
})
export class ModalLogConciliadoraComponent implements OnInit {
	movPagamento: number;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	public table: PactoDataGridConfig;
	data = false;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly sessionService: SessionService,
		private readonly snotifyService: SnotifyService,
		private readonly restApi: RestService,
		private readonly cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initTable();
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.restApi.buildFullUrl(
				`pagamentos/log-conciliadora/${this.movPagamento}`,
				true,
				Api.MSADMCORE
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "data",
					titulo: "Dt. Registro",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
				},
				{
					nome: "sucesso",
					titulo: "Sucesso",
					visible: true,
					ordenavel: false,
					valueTransform: (v) => (v ? "Sim" : "Não"),
				},
				{
					nome: "resultado",
					titulo: "Resultado",
					visible: true,
					ordenavel: false,
				},
			],
		});
		this.cd.detectChanges();
	}
}
