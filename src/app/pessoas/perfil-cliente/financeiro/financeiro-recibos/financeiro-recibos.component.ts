import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AdmCoreApiMovPagamentoService,
	AdmCoreApiReciboService,
	ApiResponseList,
	ClienteDadosPessoais,
	Empresa,
	MovPagamento,
} from "adm-core-api";
import {
	AutorizacaoAcessoComponent,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalActionNfceComponent } from "../financeiro-compras/modals/modal-action-nfce/modal-action-nfce.component";
import { ModalActionNfseComponent } from "../financeiro-compras/modals/modal-action-nfse/modal-action-nfse.component";
import { ModalActionSendComponent } from "../financeiro-compras/modals/modal-action-send/modal-action-send.component";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	TipoRelatorioDF,
} from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";
import { PerfilClienteService } from "../../perfil-cliente.service";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { ModalHistoricoAfastamentoContratoDependenteComponent } from "../../contratos/modal-historico-afastamento-contrato-dependente/modal-historico-afastamento-contrato-dependente.component";
import { ModalService } from "@base-core/modal/modal.service";
import { ModalLogConciliadoraComponent } from "./modal-log-conciliadora/modal-log-conciliadora.component";
import { MatDialog } from "@angular/material";

interface ReciboTableData {
	codigoRecibo: number;
	empresa: Empresa;
	nomePagador: string;
	dataLancamento: Date;
	formaPagamento: string;
	valor: number;
	pagamento: any;
}

@Component({
	selector: "pacto-financeiro-recibos",
	templateUrl: "./financeiro-recibos.component.html",
	styleUrls: ["./financeiro-recibos.component.scss"],
	providers: [CurrencyPipe],
})
export class FinanceiroRecibosComponent implements OnInit, AfterViewInit {
	@Output() hasDataRecibo = new EventEmitter();

	@Input() dadosPessoais: ClienteDadosPessoais;
	@ViewChild("tablePagamentosRef", { static: true })
	tablePagamentosRef: RelatorioComponent;
	tablePagamentos: PactoDataGridConfig;
	@ViewChild("tablePagamentosCodigo", { static: true })
	tablePagamentosCodigo: TemplateRef<any>;
	@ViewChild("tablePagamentosRecibo", { static: true })
	tablePagamentosRecibo: TemplateRef<any>;
	@ViewChild("tablePagamentosEmpresa", { static: true })
	tablePagamentosEmpresa: TemplateRef<any>;
	@ViewChild("tablePagamentosNomeDoPagador", { static: true })
	tablePagamentosNomeDoPagador: TemplateRef<any>;
	@ViewChild("tablePagamentosDataLancamento", { static: true })
	tablePagamentosDataLancamento: TemplateRef<any>;
	@ViewChild("tablePagamentosFormaDePagto", { static: true })
	tablePagamentosFormaDePagto: TemplateRef<any>;
	@ViewChild("tablePagamentosValor", { static: true })
	tablePagamentosValor: TemplateRef<any>;
	@ViewChild("tablePagamentosAcoes", { static: true })
	tablePagamentosAcoes: TemplateRef<any>;
	@ViewChild("actions", { static: true })
	actions: TemplateRef<any>;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	tableData: ApiResponseList<MovPagamento> = {
		content: new Array<MovPagamento>(),
	} as ApiResponseList<MovPagamento>;
	private actualPage = 0;
	private actualSize = 10;
	private sort = {
		sortDirection: "DESC",
		sortField: "dataLancamento",
	};
	dataRecibo = false;
	isAutorizarNFSe = false;
	public empresa;

	constructor(
		private rest: RestService,
		private ngbModal: NgbModal,
		private cd: ChangeDetectorRef,
		private reciboService: AdmCoreApiReciboService,
		private datePipe: DatePipe,
		private currencyPipe: CurrencyPipe,
		private telaClienteService: AdmLegadoTelaClienteService,
		private dialogService: DialogService,
		private sessionService: SessionService,
		private notificationService: SnotifyService,
		private readonly pactoModal: ModalService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private clientDiscoveryService: ClientDiscoveryService,
		private readonly perfilClienteService: PerfilClienteService,
		private movPagamentoService: AdmCoreApiMovPagamentoService,
		private matDialog: MatDialog
	) {}

	ngOnInit() {
		this.isAutorizarNFSe =
			!!this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.find(
				(r) => r.referenciaFuncionalidade === "3.43"
			);
		this.initTablePagamentos();
		this.empresa = this.perfilClienteService.empresa;
	}

	ngAfterViewInit() {
		this.list();
	}

	initTablePagamentos() {
		this.tablePagamentos = new PactoDataGridConfig({
			dataAdapterFn: (serveData) => this.tableData,
			columns: [
				{
					nome: "codigo",
					titulo: this.tablePagamentosCodigo,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "reciboPagamento",
					titulo: this.tablePagamentosRecibo,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "empresa",
					titulo: this.tablePagamentosEmpresa,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.captalize(v.nome),
				},
				{
					nome: "nomePessoaPagador",
					titulo: this.tablePagamentosNomeDoPagador,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => (v ? this.captalize(v) : "-"),
				},
				{
					nome: "dataLancamento",
					titulo: this.tablePagamentosDataLancamento,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						this.datePipe.transform(v, "dd/MM/yyyy - HH:mm:SS"),
				},
				{
					nome: "formaPagamento",
					titulo: this.tablePagamentosFormaDePagto,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => (v ? this.captalize(v.descricao) : "-"),
				},
				{
					nome: "valorTotal",
					titulo: this.tablePagamentosValor,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						this.currencyPipe.transform(
							parseFloat(v ? v.toString().replace(",", ".") : 0),
							"BRL",
							"symbol",
							"1.2-2"
						),
				},
				{
					nome: "acoes",
					titulo: this.tablePagamentosAcoes,
					visible: true,
					ordenavel: false,
					celula: this.actions,
				},
			],
			actions: [],
		});
	}

	captalize(valor) {
		const captalizePipe = new CaptalizePipe();
		return captalizePipe.transform(valor);
	}

	list() {
		this.movPagamentoService
			.findAllByCodPessoa(this.dadosPessoais.codigoPessoa, {
				page: this.actualPage,
				size: this.actualSize,
				sortField: this.sort.sortField,
				sortDirection: this.sort.sortDirection,
				filters: {
					buscarStatusConciliadora:
						this.empresa && this.empresa.usarConciliadora ? "true" : "false",
					telaAluno: "true",
				},
			})
			.subscribe((response) => {
				this.tableData = response;
				this.dataRecibo = response.totalElements > 0 ? true : false;
				if (this.dataRecibo) {
					this.hasDataRecibo.emit(true);
				}
				if (this.tablePagamentosRef) {
					this.tablePagamentosRef.reloadData();
				}
				this.cd.detectChanges();
			});
	}

	actionNfcE(row) {
		if (!this.showNfce()) {
			this.notificationService.warning(
				this.traducoes.getLabel("envio-nfce-nao-suportado"),
				{
					timeout: 4000,
				}
			);

			return;
		}

		const dialogRef = this.ngbModal.open(ModalActionNfceComponent, {
			centered: true,
			size: "lg",
			windowClass: "modal-action-nfce-component",
		});
		dialogRef.componentInstance.rowData = row;
		dialogRef.componentInstance.codigoRecibo = row.reciboPagamento;
		dialogRef.componentInstance.codigoUsuario =
			this.sessionService.codigoUsuarioZw;
		this.cd.detectChanges();
	}

	actionNfsE(row) {
		if (!this.shwoNfse()) {
			this.notificationService.warning(
				this.traducoes.getLabel("envio-nfse-nao-suportado"),
				{
					timeout: 4000,
				}
			);
			return;
		}
		const dialogRef = this.ngbModal.open(ModalActionNfseComponent, {
			centered: true,
			size: "lg",
			windowClass: "modal-action-nfse-component",
		});
		dialogRef.componentInstance.rowData = row;
		dialogRef.componentInstance.codigoRecibo = row.reciboPagamento;
		dialogRef.componentInstance.codigoUsuario =
			this.sessionService.codigoUsuarioZw;
		this.cd.detectChanges();
	}

	actionPrint(row) {
		this.telaClienteService
			.imprimirRecibo(
				this.sessionService.chave,
				row.reciboPagamento,
				this.sessionService.codigoUsuarioZw
			)
			.subscribe(
				(response) => {
					window.open(response.content, "_blank");
				},
				(error) => this.notificationService.error("Erro ao gerar recibo!")
			);
	}

	actionSend(row) {
		const dialogRef = this.ngbModal.open(ModalActionSendComponent, {
			centered: true,
			size: "lg",
			windowClass: "modal-action-send-component",
		});
		dialogRef.componentInstance.rowData = row;
		dialogRef.componentInstance.email = this.dadosPessoais.emails[0];
		dialogRef.componentInstance.codigoRecibo = row.reciboPagamento;
		this.cd.detectChanges();
	}

	actionEdicaoPagamento(row) {
		if (row.empresa.codigo !== +this.sessionService.empresaId) {
			this.notificationService.error(
				`É necessário estar logado na unidade ${row.empresa.nome} para realizar essa operação, pois esse pagamento pertence a essa unidade.`
			);
			return;
		}
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"EdicaoPagamento",
					"4.22 - Edição de Pagamento",
					this.dadosPessoais.empresa.nome
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						this.clientDiscoveryService
							.linkZw(
								this.sessionService.usuarioOamd,
								this.sessionService.empresaId
							)
							.subscribe((result) => {
								let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&codigoPagamento=${row.codigo}`;
								url += `&codigoUsuario=${response.content}&operacaoClienteEnumName=EDICAO_FORMA_PAGAMENTO&isOperacaoFinanceiro=true&menu=true`;
								window.open(url, "_self");
							});
					},
					(error) => {
						console.log(error);
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	actionConsultaRecibo(row) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&codigoRecibo=${row.reciboPagamento}&operacaoClienteEnumName=CONSULTA_RECIBO&isOperacaoFinanceiro=true&origem=angular`;
				this.abrirPopup(url, "Recibo", 800, 595);
			});
	}

	iconClickFnTablePagamentos(event) {
		switch (event.iconName) {
			case "ACTION_NFC_E":
				this.actionNfcE(event.row);
				break;
			case "ACTION_NFS_E":
				this.actionNfsE(event.row);
				break;
			case "ACTION_PRINT":
				this.actionPrint(event.row);
				break;
			case "ACTION_SEND":
				this.actionSend(event.row);
				break;
			case "ACTION_EDICAO_PAGAMENTO":
				this.actionEdicaoPagamento(event.row);
				break;
			case "ACTION_CONSULTA_RECIBO":
				this.actionConsultaRecibo(event.row);
				break;
		}
	}

	getTooltipNfce(row): string {
		if (!row.empresa.usarNfse) {
			return this.traducoes.getLabel("envio-nfce-nao-suportado");
		}

		return this.traducoes.getLabel("envio-nfce-suportado");
	}

	getTooltipNfse(row): string {
		if (!row.empresa.usarNfse) {
			return this.traducoes.getLabel("envio-nfse-nao-suportado");
		}

		return this.traducoes.getLabel("envio-nfse-suportado");
	}

	showNfce(): boolean {
		return (
			this.dadosPessoais.empresa &&
			this.dadosPessoais.empresa.usarNfce &&
			this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.findIndex(
				(f) => f.nome === "gestaonfce" && f.possuiFuncionalidade
			) > -1
		);
	}

	shwoNfse(): boolean {
		return (
			this.isAutorizarNFSe &&
			this.dadosPessoais.empresa &&
			this.dadosPessoais.empresa.usarNfse &&
			this.dadosPessoais.empresa.tipoGestaoNfse ===
				TipoRelatorioDF.FATURAMENTO_DE_CAIXA.codigo
		);
	}

	onSizeChange(size) {
		this.actualSize = size;
		this.list();
	}

	onPageChange(page) {
		this.actualPage = page - 1;
		this.list();
	}

	onSortChange(sort) {
		this.sort = {
			sortDirection: sort.direction,
			sortField: sort.columnName,
		};
		this.list();
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	): boolean {
		const posTopo = 0;
		const posEsquerda = 0;
		const atributos = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo}, width=${comprimento}, height=${altura}, dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes`;

		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		window.open(urlPopup, nomeJanela, atributos);

		return false;
	}

	abrirLogConciliadora(movPagamento) {
		const modalRef = this.pactoModal.open(
			"Status integração conciliadora",
			ModalLogConciliadoraComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.movPagamento = movPagamento;
	}
}
