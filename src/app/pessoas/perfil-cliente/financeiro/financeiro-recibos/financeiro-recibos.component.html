<div [hidden]="dataRecibo" class="div-empty">
	<div class="title-text-empty">Recibos</div>
	<div class="d-flex flex-column align-items-center">
		<img class="icon-empty" src="assets/images/empty-state-notas-fiscais.svg" />
		<div class="text-empty mt-2 body-text-empty">
			O aluno ainda não possui nenhum recibo!
		</div>
	</div>
</div>

<div [hidden]="!dataRecibo" class="row secao-recibos">
	<div class="col-12">
		<pacto-relatorio
			#tablePagamentosRef
			(iconClick)="iconClickFnTablePagamentos($event)"
			(pageChangeEvent)="onPageChange($event)"
			(pageSizeChange)="onSizeChange($event)"
			(sortEvent)="onSortChange($event)"
			[enableZebraStyle]="true"
			[showShare]="false"
			[table]="tablePagamentos"
			actionTitulo="Ações"
			class="table-pagamentos"
			idSuffix="fin-tbl-recibo"
			tableTitle="Histórico de recibos"></pacto-relatorio>

		<ng-template #tablePagamentosCodigo>
			<span class="columnTitle">Código</span>
		</ng-template>
		<ng-template #tablePagamentosRecibo>
			<span class="columnTitle">Recibo</span>
		</ng-template>
		<ng-template #tablePagamentosEmpresa>
			<span class="columnTitle">Empresa</span>
		</ng-template>
		<ng-template #tablePagamentosNomeDoPagador>
			<span class="columnTitle">Nome do pagador</span>
		</ng-template>
		<ng-template #tablePagamentosDataLancamento>
			<span class="columnTitle">Dt. lançamento</span>
		</ng-template>
		<ng-template #tablePagamentosFormaDePagto>
			<span class="columnTitle">Forma de pagto.</span>
		</ng-template>
		<ng-template #tablePagamentosValor>
			<span class="columnTitle">Valor</span>
		</ng-template>
		<ng-template #tablePagamentosAcoes>
			<span class="columnTitle">Ações</span>
		</ng-template>

		<ng-template #actions let-item="item">
			<span class="action-buttons">
				<button
					(click)="actionNfcE(item)"
					*ngIf="showNfce()"
					[ngbTooltip]="getTooltipNfce(item)"
					aria-label="enviar NFC-e"
					class="btn-icon"
					id="btn-enviar-nfce"
					type="button">
					<span i18n="@@table-pagamentos:action-nfce">NFC-e</span>
				</button>
				<button
					(click)="actionNfsE(item)"
					*ngIf="shwoNfse()"
					[ngbTooltip]="getTooltipNfse(item)"
					aria-label="enviar NFS-e"
					class="btn-icon"
					id="btn-enviar-nfse"
					type="button">
					<span i18n="@@table-pagamentos:action-nfse">NFS-e</span>
				</button>
				<button
					(click)="actionPrint(item)"
					aria-label="Imprimir"
					class="btn-icon"
					i18n-ngbTooltip="@@table-pagamentos:print-receive"
					id="btn-imprimir-recibo"
					ngbTooltip="Imprimir recibo"
					type="button">
					<i class="pct pct-printer cor-azulim05"></i>
				</button>
				<button
					(click)="actionSend(item)"
					aria-label="Enviar"
					class="btn-icon"
					i18n-ngbTooltip="@@table-pagamentos:enviar-recibo-email"
					id="btn-enviar-recibo"
					ngbTooltip="Enviar recibo por e-mail"
					type="button">
					<i class="pct pct-send cor-azulim05"></i>
				</button>
				<button
					(click)="actionEdicaoPagamento(item)"
					aria-label="Editar pagamento"
					class="btn-icon"
					i18n-ngbTooltip="@@table-pagamentos:edicao-pagamento"
					id="btn-editar-pagamento"
					ngbTooltip="Edição de pagamento"
					type="button">
					<i class="pct pct-edit cor-azulim05"></i>
				</button>
				<button
					(click)="actionConsultaRecibo(item)"
					aria-label="Ver detalhes"
					class="btn-icon"
					i18n-ngbTooltip="@@table-pagamentos:detalhamento-parcela"
					id="btn-ver-detalhes"
					ngbTooltip="Visualizar recibo"
					type="button">
					<i class="pct pct-search cor-azulim05"></i>
				</button>

				<!--				<span *ngIf='empresa?.usarConciliadora && item?.statusConciliadora === 0'-->
				<!--					  [pactoCatTolltip]="'Nenhum'" [darkTheme]="true">-->
				<!--					<i class="pct pct-search cor-azulim05"></i>-->
				<!--				</span>-->
				<span
					*ngIf="empresa?.usarConciliadora && item?.statusConciliadora === 1"
					[darkTheme]="true"
					[pactoCatTolltip]="'Status Conciliadora: Aguardando envio'">
					<i class="pct pct-clock" style="color: #6e6ddb"></i>
				</span>
				<span
					*ngIf="empresa?.usarConciliadora && item?.statusConciliadora === 2"
					[darkTheme]="true"
					[pactoCatTolltip]="'Status Conciliadora: Enviado'">
					<i class="pct pct-check-circle" style="color: #1dc973"></i>
				</span>
				<span
					(click)="abrirLogConciliadora(item?.codigo)"
					*ngIf="empresa?.usarConciliadora && item?.statusConciliadora === 3"
					[darkTheme]="true"
					[pactoCatTolltip]="
						'Status Conciliadora: Erro - Clique para verificar detalhes'
					">
					<i class="pct pct-x-circle" style="color: #e23661"></i>
				</span>
				<span
					*ngIf="empresa?.usarConciliadora && item?.statusConciliadora === 4"
					[darkTheme]="true"
					[pactoCatTolltip]="'Status Conciliadora: Pagamento não é cartão'">
					<i class="pct pct-help-circle" style="color: #e2b736"></i>
				</span>
			</span>
		</ng-template>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@table-pagamentos:envio-nfce-suportado"
		xingling="envio-nfce-suportado">
		Enviar NFC-e
	</span>
	<span
		i18n="@@table-pagamentos:envio-nfce-nao-suportado"
		xingling="envio-nfce-nao-suportado">
		Recurso de envio de NFC-e não configurado para essa empresa.
	</span>
	<span
		i18n="@@table-pagamentos:envio-nfse-suportado"
		xingling="envio-nfse-suportado">
		Enviar NFS-e
	</span>
	<span
		i18n="@@table-pagamentos:envio-nfse-nao-suportado"
		xingling="envio-nfse-nao-suportado">
		Recurso de envio de NFS-e não configurado para essa empresa.
	</span>
</pacto-traducoes-xingling>
