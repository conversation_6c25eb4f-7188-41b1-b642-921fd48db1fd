import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-tela-cliente-consulta-recibo",
	templateUrl: "./tela-cliente-consulta-recibo.component.html",
	styleUrls: ["./tela-cliente-consulta-recibo.component.scss"],
})
export class TelaClienteConsultaReciboComponent implements OnInit {
	@Input() codCliente;
	@Input() codRecibo;
	url: string;

	constructor(
		private modal: NgbActiveModal,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				this.url = `${result}&codCliente=${this.codCliente}&codigoRecibo=${this.codRecibo}&operacaoClienteEnumName=CONSULTA_RECIBO&isOperacaoFinanceiro=true`;
				this.cd.detectChanges();
			});
	}
}
