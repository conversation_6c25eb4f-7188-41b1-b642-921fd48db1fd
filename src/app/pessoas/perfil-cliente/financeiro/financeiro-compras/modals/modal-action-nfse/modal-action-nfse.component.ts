import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-action-nfse",
	templateUrl: "./modal-action-nfse.component.html",
	styleUrls: ["./modal-action-nfse.component.scss"],
})
export class ModalActionNfseComponent implements OnInit {
	@Input() rowData: any;
	@Input() codigoRecibo: number;
	@Input() codigoUsuario: number;

	message: string;

	constructor(
		private openModal: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	cancelar() {
		this.openModal.close();
	}

	enviar() {
		this.telaClienteService
			.emitirNfse(
				this.sessionService.chave,
				this.codigoRecibo,
				this.sessionService.codigoUsuarioZw
			)
			.subscribe(
				(response) => {
					this.openModal.close();
					this.message = "NFS-e emita com sucesso!";
				},
				(httpResponseError) => {
					this.message = httpResponseError.error.meta.message;
					this.cd.detectChanges();
				}
			);
	}

	close() {
		this.openModal.close();
	}
}
