<div class="pacto-modal">
	<div class="modal-titulo">
		<div class="title">Detalhamento da Parcela</div>
		<div (click)="close()" class="close-modal">
			<i class="pct pct-x" id="close-aba"></i>
		</div>
	</div>
	<div class="modal-conteudo">
		<pacto-cat-tabs-transparent>
			<ng-template label="Pagamentos" pactoTabTransparent>
				<hr />
				<div class="card">
					<div class="row">
						<div class="col-3"><span class="title">Cód. recibo:</span></div>
						<div class="col-6"><span class="title">Nome do aluno:</span></div>
						<div class="col-3"><span class="title">Valor total:</span></div>
					</div>
					<div class="row">
						<div class="col-3">
							<p class="data">000000</p>
						</div>
						<div class="col-6">
							<p class="data"><PERSON><PERSON><PERSON></p>
						</div>
						<div class="col-3">
							<p class="data">R$500,00</p>
						</div>
					</div>
				</div>
				<h6 class="section-title">Histórico de parcelas</h6>
				<pacto-relatorio
					#detailedPagamentosRef
					[showShare]="false"
					[table]="detailedPagamentos"
					class="detailed-pagamentos"></pacto-relatorio>
				<div class="row">
					<div class="col-12 buttons">
						<pacto-cat-button
							(click)="avancar()"
							class="button-avancar"
							label="Avancar"
							size="LARGE"
							type="PRIMARY"></pacto-cat-button>
					</div>
				</div>
			</ng-template>
			<ng-template label="Parcelas pagas" pactoTabTransparent>
				<h6 class="section-title">Parcelas pagas pelo recibo</h6>
				<pacto-relatorio
					#detailedParcelasPagasRef
					[showShare]="false"
					[table]="detailedParcelasPagas"
					class="detailed-parcelas-pagas"></pacto-relatorio>
				<h6 class="section-title">Confirmação da data de estorno</h6>
				<div class="datepicker-area">
					<pacto-cat-datepicker
						[className]="'datePicker'"
						[formControl]="formGroup.get('date')"
						errorMsg="Forneça uma data válida."
						label="Data de estorno"></pacto-cat-datepicker>
				</div>
				<div class="row">
					<div class="col-12 buttons">
						<pacto-cat-button
							(click)="voltar()"
							class="button-voltar"
							label="Voltar"
							size="LARGE"
							type="OUTLINE"></pacto-cat-button>
						<pacto-cat-button
							(click)="confirmar()"
							class="button-confirmar"
							label="confirmar"
							size="LARGE"
							type="PRIMARY"></pacto-cat-button>
					</div>
				</div>
			</ng-template>
		</pacto-cat-tabs-transparent>
	</div>
</div>

<ng-template #detailedPagamentosCod>
	<span class="columnTitle">Cód</span>
</ng-template>
<ng-template #detailedPagamentosPagador>
	<span class="columnTitle">Pagador</span>
</ng-template>
<ng-template #detailedPagamentosFormaDePagamento>
	<span class="columnTitle">Forma de pagamento</span>
</ng-template>
<ng-template #detailedPagamentosLancamento>
	<span class="columnTitle">Lançamento</span>
</ng-template>
<ng-template #detailedPagamentosValor>
	<span class="columnTitle">Valor</span>
</ng-template>

<ng-template #detailedParcelasPagContrato>
	<span class="columnTitle">Contrato</span>
</ng-template>
<ng-template #detailedParcelasPagCod>
	<span class="columnTitle">Cód</span>
</ng-template>
<ng-template #detailedParcelasPagNomeDoAluno>
	<span class="columnTitle">Nome do aluno</span>
</ng-template>
<ng-template #detailedParcelasPagDescricao>
	<span class="columnTitle">Descrição</span>
</ng-template>
<ng-template #detailedParcelasPagModalidade>
	<span class="columnTitle">Modalidade</span>
</ng-template>
<ng-template #detailedParcelasPagVencimento>
	<span class="columnTitle">Vencimento</span>
</ng-template>
<ng-template #detailedParcelasPagValor>
	<span class="columnTitle">Valor</span>
</ng-template>
<ng-template #detailedParcelasPagSit>
	<span class="columnTitle">Sit.</span>
</ng-template>
<ng-template #detailedParcelasPagCellSit let-item="item">
	<div [class]="'pill-status ' + stringToKebab(item.sit)">{{ item.sit }}</div>
</ng-template>
