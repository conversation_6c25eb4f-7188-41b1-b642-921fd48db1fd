@import "src/assets/scss/pacto/plataforma-import.scss";

.pacto-modal {
	.modal-titulo {
		@extend .type-h6-bold;
		padding: 32px 32px 16px 32px;
		position: relative;
		line-height: 20px;
		color: $pretoPri;
		font-weight: 700;
		border-bottom: 0.5px solid #c4c4c4;

		.title {
			line-height: 16px;
		}

		.close-modal {
			position: absolute;
			right: 26px;
			top: 26px;
			font-size: 20px;
			color: $pretoPri;
			cursor: pointer;
		}
	}

	.modal-conteudo {
		display: flex;
		align-items: flex-start;
		flex-direction: column;
		flex-wrap: nowrap;
		overflow: auto;
		margin: 16px;
	}
}

.pill-status {
	border-radius: 50px;
	text-align-last: center;
	background-color: grey;
	padding: 5px;

	&.em-aberto {
		color: $azulimPri;
		background-color: $azulim01;
	}

	&.pago {
		color: $chuchuzinhoPri;
		background-color: $chuchuzinho01;
	}
}

::ng-deep.columnTitle,
::ng-deep.action-column {
	@extend .type-p-small-rounded;
	font-size: 14px !important;
	font-weight: 700 !important;
	color: $pretoPri;
}

.section-title {
	@extend .type-h6-bold;
	font-weight: 700;
	color: $pretoPri;
}

.title {
	@extend .type-p-small-rounded;
	font-weight: 700;
}

.data {
	@extend .type-p-small-rounded;
}

hr {
	border: 1px $cinza01 solid;
}

.datepicker-area {
	width: 300px;
}

.card {
	background-color: #fafafa;
	padding: 24px 32px;
}

.buttons {
	display: flex;
	justify-content: flex-end;

	.button-avancar,
	.button-confirmar {
		padding: 16px 0;
	}

	.button-voltar {
		padding: 16px 16px 0 0;
	}
}
