import {
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-action-detail-parcela",
	templateUrl: "./modal-action-detail-parcela.component.html",
	styleUrls: ["./modal-action-detail-parcela.component.scss"],
})
export class ModalActionDetailParcelaComponent implements OnInit {
	@Input() rowData: any;

	constructor(private openModal: NgbActiveModal) {}

	@ViewChild("detailedPagamentosRef", { static: true })
	detailedPagamentosRef: RelatorioComponent;
	detailedPagamentos: PactoDataGridConfig;

	@ViewChild("detailedPagamentosCod", { static: true })
	detailedPagamentosCod: TemplateRef<any>;
	@ViewChild("detailedPagamentosPagador", { static: true })
	detailedPagamentosPagador: TemplateRef<any>;
	@ViewChild("detailedPagamentosFormaDePagamento", { static: true })
	detailedPagamentosFormaDePagamento: TemplateRef<any>;
	@ViewChild("detailedPagamentosLancamento", { static: true })
	detailedPagamentosLancamento: TemplateRef<any>;
	@ViewChild("detailedPagamentosValor", { static: true })
	detailedPagamentosValor: TemplateRef<any>;
	detailedPagamentosResponse: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: [
			{
				cod: "123",
				pagador: "Murilo Martins Melo",
				formaDePagamento: "Cartão de crédito",
				lancamento: "03/03/2023",
				valor: "R$100,00",
			},
			{
				cod: "123",
				pagador: "Murilo Martins Melo",
				formaDePagamento: "Cartão de débito",
				lancamento: "03/03/2023",
				valor: "R$100,00",
			},
			{
				cod: "123",
				pagador: "Murilo Martins Melo",
				formaDePagamento: "Dinheiro",
				lancamento: "03/03/2023",
				valor: "R$100,00",
			},
		],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	@ViewChild("detailedParcelasPagasRef", { static: true })
	detailedParcelasPagasRef: RelatorioComponent;
	detailedParcelasPagas: PactoDataGridConfig;

	@ViewChild("detailedParcelasPagContrato", { static: true })
	detailedParcelasPagContrato: TemplateRef<any>;
	@ViewChild("detailedParcelasPagCod", { static: true })
	detailedParcelasPagCod: TemplateRef<any>;
	@ViewChild("detailedParcelasPagNomeDoAluno", { static: true })
	detailedParcelasPagNomeDoAluno: TemplateRef<any>;
	@ViewChild("detailedParcelasPagDescricao", { static: true })
	detailedParcelasPagDescricao: TemplateRef<any>;
	@ViewChild("detailedParcelasPagModalidade", { static: true })
	detailedParcelasPagModalidade: TemplateRef<any>;
	@ViewChild("detailedParcelasPagVencimento", { static: true })
	detailedParcelasPagVencimento: TemplateRef<any>;
	@ViewChild("detailedParcelasPagValor", { static: true })
	detailedParcelasPagValor: TemplateRef<any>;
	@ViewChild("detailedParcelasPagSit", { static: true })
	detailedParcelasPagSit: TemplateRef<any>;
	@ViewChild("detailedParcelasPagCellSit", { static: true })
	detailedParcelasPagCellSit: TemplateRef<any>;
	detailedParcelasPagasResponse: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: [
			{
				contrato: "12345",
				cod: "123",
				nomeDoAluno: "Murilo Martins Melo",
				descricao: "Venda avulsa",
				modalidade: "---",
				vencimento: "03/03/2023",
				valor: "R$100,00",
				sit: "Pago",
			},
			{
				contrato: "12345",
				cod: "123",
				nomeDoAluno: "Murilo Martins Melo",
				descricao: "Parcela 02",
				modalidade: "---",
				vencimento: "03/03/2023",
				valor: "R$100,00",
				sit: "Pago",
			},
			{
				contrato: "12345",
				cod: "123",
				nomeDoAluno: "Murilo Martins Melo",
				descricao: "Parcela 03",
				modalidade: "---",
				vencimento: "03/03/2023",
				valor: "R$100,00",
				sit: "Pago",
			},
		],
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	formGroup = new FormGroup({
		date: new FormControl("", [Validators.required]),
	});

	ngOnInit() {
		this.initTablePagamentos();
		this.initTableParcelasPagas();
	}

	initTablePagamentos() {
		this.detailedPagamentos = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.detailedPagamentosResponse;
			},
			columns: [
				{
					nome: "cod",
					titulo: this.detailedPagamentosCod,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "pagador",
					titulo: this.detailedPagamentosPagador,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "formaDePagamento",
					titulo: this.detailedPagamentosFormaDePagamento,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "lancamento",
					titulo: this.detailedPagamentosLancamento,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "valor",
					titulo: this.detailedPagamentosValor,
					visible: true,
					ordenavel: true,
				},
			],
		});
	}

	initTableParcelasPagas() {
		this.detailedParcelasPagas = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.detailedParcelasPagasResponse;
			},
			columns: [
				{
					nome: "contrato",
					titulo: this.detailedParcelasPagContrato,
					visible: true,
					ordenavel: true,
					width: "75px",
				},
				{
					nome: "cod",
					titulo: this.detailedParcelasPagCod,
					visible: true,
					ordenavel: true,
					width: "57px",
				},
				{
					nome: "nomeDoAluno",
					titulo: this.detailedParcelasPagNomeDoAluno,
					visible: true,
					ordenavel: true,
					width: "148px",
				},
				{
					nome: "descricao",
					titulo: this.detailedParcelasPagDescricao,
					visible: true,
					ordenavel: true,
					width: "92px",
				},
				{
					nome: "modalidade",
					titulo: this.detailedParcelasPagModalidade,
					visible: true,
					ordenavel: true,
					width: "101px",
				},
				{
					nome: "vencimento",
					titulo: this.detailedParcelasPagVencimento,
					visible: true,
					ordenavel: true,
					width: "101px",
				},
				{
					nome: "valor",
					titulo: this.detailedParcelasPagValor,
					visible: true,
					ordenavel: true,
					width: "63px",
				},
				{
					nome: "sit",
					titulo: this.detailedParcelasPagSit,
					visible: true,
					ordenavel: true,
					width: "86px",
					celula: this.detailedParcelasPagCellSit,
				},
			],
		});
	}

	stringToKebab(str: string) {
		return str
			.match(
				/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
			)
			.map((x) => x.toLowerCase())
			.join("-");
	}

	close() {
		this.openModal.close();
	}

	avancar() {
		console.log("avancar");
	}

	voltar() {
		console.log("voltar");
	}

	confirmar() {
		console.log("confirmar");
	}
}
