@import "src/assets/scss/pacto/plataforma-import.scss";

.pacto-modal {
	.modal-titulo {
		@extend .type-h6-bold;
		padding: 32px 32px 16px 32px;
		position: relative;
		line-height: 20px;
		color: $pretoPri;
		font-weight: 700;
		border-bottom: 0.5px solid $pretoPri;

		.title {
			line-height: 16px;
		}

		.close-modal {
			position: absolute;
			right: 26px;
			top: 26px;
			font-size: 20px;
			color: $pretoPri;
			cursor: pointer;
		}
	}

	.modal-conteudo {
		display: flex;
		align-items: flex-start;
		flex-direction: column;
		flex-wrap: nowrap;
	}
}

.text {
	padding: 20px;
}

.buttons {
	padding: 0 0 27px 0;
	align-self: flex-end;

	.button-cancelar {
		button {
			padding: 15px 40px;
		}

		margin: 0 24px 27px 0;
	}

	.button-enviar {
		button {
			padding: 15px 40px;
		}

		margin: 0 32px 27px 0;
	}
}
