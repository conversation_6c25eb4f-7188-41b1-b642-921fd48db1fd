@import "src/assets/scss/pacto/plataforma-import.scss";

.pacto-modal {
	.modal-titulo {
		@extend .type-h6-bold;
		padding: 32px 32px 16px 32px;
		position: relative;
		line-height: 20px;
		color: $pretoPri;
		font-weight: 700;
		border-bottom: 0.5px solid #c4c4c4;

		.title {
			line-height: 16px;
		}

		.close-modal {
			position: absolute;
			right: 26px;
			top: 26px;
			font-size: 20px;
			color: $pretoPri;
			cursor: pointer;
		}
	}

	.modal-conteudo {
		display: flex;
		align-items: flex-start;
		flex-direction: column;
		flex-wrap: nowrap;
		margin: 24px;
	}
}

.card {
	padding: 16px;
	width: 100%;

	.separador {
		border-bottom: 1px dashed $cinza02;
	}
}

.card-title {
	@extend .type-h6-bold;
	font-weight: 700;
	border-bottom: 0.5px solid #c4c4c4;
}

.title {
	@extend .type-p-small-rounded;
	font-weight: 700;
}

.data {
	@extend .type-p-small-rounded;
}

.alert {
	background-color: $laranjinha01;
	border-radius: 5px;
	height: 33px;
	padding: 8px 8px 15px 15px;
	@extend .type-p-small-rounded;
	font-weight: 600;
	color: $laranjinha05;
	width: 100%;

	i {
		padding-right: 8.28px;
	}
}
