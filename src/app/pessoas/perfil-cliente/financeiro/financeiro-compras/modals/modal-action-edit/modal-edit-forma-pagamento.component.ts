import {
	ChangeDetectorRef,
	Component,
	Input,
	AfterContentInit,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "../../../../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-modal-edit-forma-pagamento",
	templateUrl: "./modal-edit-forma-pagamento.component.html",
	styleUrls: ["./modal-edit-forma-pagamento.component.scss"],
})
export class ModalEditFormaPagamentoComponent implements AfterContentInit {
	@Input() pagamento: any;
	@Input() codCliente: number;
	@Input() codigoUsuarioValidado: number;
	url: string;

	constructor(
		private openModal: NgbActiveModal,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngAfterContentInit() {
		/*TOD<PERSON> <PERSON>
		 *  Verificar erro onde no back o parâmetro menu não é reconhecido pelo MenuControle.isApresentarMenu */
		if (this.pagamento) {
			this.clientDiscoveryService
				.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
				.subscribe((result) => {
					this.url = `${result}&codCliente=${this.codCliente}&codigoPagamento=${
						this.pagamento ? this.pagamento.codigo : ""
					}`;
					this.url += `&codigoUsuario=${this.codigoUsuarioValidado}&operacaoClienteEnumName=EDICAO_FORMA_PAGAMENTO&isOperacaoFinanceiro=true&menu=false`;
					this.cd.detectChanges();
				});
		}
	}

	close() {
		this.openModal.close();
	}
}
