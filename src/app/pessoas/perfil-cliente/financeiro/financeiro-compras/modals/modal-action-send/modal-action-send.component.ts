import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-modal-action-send",
	templateUrl: "./modal-action-send.component.html",
	styleUrls: ["./modal-action-send.component.scss"],
})
export class ModalActionSendComponent implements OnInit {
	@Input() rowData: any;
	@Input() email: string = "";
	@Input() codigoRecibo: number;

	formGroup = new FormGroup({
		email: new FormControl("", [Validators.required, Validators.email]),
	});

	constructor(
		private openModal: NgbActiveModal,
		private telaClienteService: AdmLegadoTelaClienteService,
		private sessionService: SessionService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.formGroup.get("email").setValue(this.email);
	}

	enviar() {
		if (this.formGroup.valid) {
			this.telaClienteService
				.enviarReciboEmail(
					this.sessionService.chave,
					this.codigoRecibo,
					this.sessionService.codigoUsuarioZw,
					[this.formGroup.get("email").value]
				)
				.subscribe(
					(resp) => {
						this.notificationService.success(resp.content);
						this.close();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.message) {
							this.notificationService.error(err.meta.message);
						}
					}
				);
		} else {
			this.notificationService.error("O campo e-mail é obrigatório!");
		}
	}

	close() {
		this.openModal.close();
	}
}
