@import "src/assets/scss/pacto/plataforma-import.scss";

.pacto-modal {
	.modal-titulo {
		@extend .type-h6-bold;
		padding: 32px 32px 16px 32px;
		position: relative;
		line-height: 20px;
		color: $pretoPri;
		font-weight: 700;
		border-bottom: 0.5px solid $pretoPri;

		.title {
			line-height: 16px;
		}

		.close-modal {
			position: absolute;
			right: 26px;
			top: 26px;
			font-size: 20px;
			color: $pretoPri;
			cursor: pointer;
		}
	}

	.modal-conteudo {
		display: flex;
		justify-content: center;
		align-items: center;

		.row {
			align-items: center;
			padding: 32px;
		}
	}
}

.input {
	width: 35em;
}

::ng-deep.button-enviar > button {
	width: 130px;
}

.button-enviar {
	padding-left: 20px;
}
