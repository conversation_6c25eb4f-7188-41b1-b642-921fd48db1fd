import { Component, OnInit } from "@angular/core";
import { PactoDataGridConfig } from "ui-kit";

declare var moment;

@Component({
	selector: "pacto-modal-action-estornar",
	templateUrl: "./modal-action-estornar.component.html",
	styleUrls: ["./modal-action-estornar.component.scss"],
})
export class ModalActionEstornarComponent implements OnInit {
	table: PactoDataGridConfig;

	constructor() {}

	ngOnInit() {
		this.table = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			dataAdapterFn: () => {
				return {
					content: Array.from({ length: 4 }, (_, i) => ({
						codigo: i,
						contrato: `00239`,
						nomeAluno: `<PERSON> doe`,
						descricao: `Descricao da parcela`,
						vencimento: new Date(),
						valor: "30",
						situacao: "EA",
					})),
				};
			},
			columns: [
				{
					nome: "contrato",
					titulo: "Contrato",
					visible: true,
					ordenavel: true,
					width: "20%",
				},
				{
					nome: "nomeAluno",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: true,
					width: "25%",
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: true,
					width: "25%",
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: true,
					width: "20%",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					width: "20%",
					// valueTransform: v => moment(v).format('DD/MM/YYYY'),
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					ordenavel: true,
					width: "20%",
				},
			],
		});
	}

	estornar(): void {}
}
