import { C<PERSON><PERSON>cyPipe, DatePipe } from "@angular/common";
import {
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChange,
	SimpleChanges,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AdmCoreApiMovParcelaService,
	ClienteDadosPessoais,
} from "adm-core-api";
import moment from "moment";

import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";

@Component({
	selector: "pacto-modal-detalhe-parcelas",
	templateUrl: "./modal-detalhe-parcelas.component.html",
	styleUrls: ["./modal-detalhe-parcelas.component.scss"],
	providers: [CurrencyPipe],
})
export class ModalDetalheParcelasComponent implements OnInit, OnChanges {
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() movProduto: any;

	tableProdutosCobradosParcela: PactoDataGridConfig;
	@ViewChild("columnContratoTitle", { static: true })
	columnContratoTitle: TemplateRef<any>;
	@ViewChild("columnDescricaoTitle", { static: true })
	columnDescricaoTitle: TemplateRef<any>;
	@ViewChild("columnEmpresaTitle", { static: true })
	columnEmpresaTitle: TemplateRef<any>;
	@ViewChild("columnSituacaoTitle", { static: true })
	columnSituacaoTitle: TemplateRef<any>;
	@ViewChild("cellSituacao", { static: true })
	cellSituacao: TemplateRef<any>;
	@ViewChild("columnVencimentoTitle", { static: true })
	columnVencimentoTitle: TemplateRef<any>;
	@ViewChild("columnDataPagamentoTitle", { static: true })
	columnDataPagamentoTitle: TemplateRef<any>;
	@ViewChild("columnValorTotalTitle", { static: true })
	columnValorTotalTitle: TemplateRef<any>;
	@ViewChild("tableProdutos", { static: false })
	tableProdutos: RelatorioComponent;

	parcelas: any;
	emptyMessage: string;

	private actualPage = 0;
	private actualSize = 10;
	private sort = {
		sortDirection: "DESC",
		sortField: "dataVencimento",
	};

	constructor(
		private readonly currencyPipe: CurrencyPipe,
		private readonly datePipe: DatePipe,
		private readonly movParcelaService: AdmCoreApiMovParcelaService
	) {}

	ngOnInit() {
		this.initTable();
	}

	ngAfterViewInit() {
		this.list();
	}

	ngOnChanges(changes: SimpleChanges) {
		const produtos: SimpleChange = changes.movProduto;
		if (produtos && this.tableProdutos) {
			this.list();
			document.getElementById("tbl-prod-cobrados-parcela").scrollIntoView();
		}
	}

	private initTable() {
		this.tableProdutosCobradosParcela = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => this.parcelas || { content: [] },
			columns: [
				{
					nome: "codigo",
					titulo: this.columnContratoTitle,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => (v ? v : "-"),
				},
				{
					nome: "descricao",
					titulo: this.columnDescricaoTitle,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "empresa",
					titulo: this.columnEmpresaTitle,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => v.nome,
				},
				{
					nome: "situacao",
					titulo: this.columnSituacaoTitle,
					visible: true,
					ordenavel: true,
					celula: this.cellSituacao,
				},
				{
					nome: "dataVencimento",
					titulo: this.columnVencimentoTitle,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyyy"),
				},
				{
					nome: "dataPagamento",
					titulo: this.columnDataPagamentoTitle,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						v ? moment(v).utc().format("DD/MM/YYYY") : "-",
				},
				{
					nome: "valor",
					titulo: this.columnValorTotalTitle,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						this.currencyPipe.transform(
							parseFloat(v ? v.toString().replace(",", ".") : 0),
							"BRL",
							"symbol",
							"1.2-2"
						),
				},
			],
		});
	}

	onSizeChange(size) {
		this.actualSize = size;
		this.list();
	}

	onPageChange(page) {
		this.actualPage = page - 1;
		this.list();
	}

	onSortChange(sort) {
		this.sort = {
			sortDirection: sort.direction,
			sortField: sort.columnName,
		};
		this.list();
	}

	list() {
		if (!this.movProduto) {
			this.movParcelaService
				.findParcelasCodigoPessoa(
					{
						page: this.actualPage,
						size: this.actualSize,
						sortField: this.sort.sortField,
						sortDirection: this.sort.sortDirection,
					},
					this.dadosPessoais.codigoPessoa
				)
				.subscribe((response) => {
					this.parcelas = response;
					this.tableProdutos.reloadData();
				});
		} else {
			this.movParcelaService
				.findParcelasByMovProduto(
					{
						page: this.actualPage,
						size: this.actualSize,
						sortField: this.sort.sortField,
						sortDirection: this.sort.sortDirection,
					},
					this.movProduto.codigo
				)
				.subscribe((response) => {
					this.parcelas = response;
					this.emptyMessage = `Não há parcelas para a compra ${this.movProduto.codigo} - ${this.movProduto.descricao}`;
					this.tableProdutos.reloadData();
				});
		}
	}
}
