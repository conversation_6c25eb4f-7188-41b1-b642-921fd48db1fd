:host {
	::ng-deep.table-produtos-parcela {
		.pacto-table-title-block {
			padding-top: 0;
			padding-left: 0;
			padding-right: 0;
			display: none;

			.table-title {
				color: #51555a;
				font-size: 14px;
			}
		}

		.table-content {
			padding: 0;
		}

		.table-content table.table {
			padding-left: 0;
			padding-right: 0;

			th,
			.column-title {
				font-size: 14px;
				font-weight: 700;
			}

			td {
				font-size: 12px;

				i {
					font-size: 16px;
				}
			}
		}
	}
}

.modal-content-wrapper {
	padding: 1rem;
	padding-top: 1.5rem;
}

.d-flex {
	flex-wrap: wrap;
}

.gap {
	gap: 1rem 4.5rem;
}

.situacao {
	width: 6rem;
	padding: 0.3125rem 1rem;
	border-radius: 3.125rem;
	font-size: 0.75rem;
	font-weight: 400;
	text-align: center;

	&.ca {
		background-color: #f3b4ba;
		color: #ba202f;
	}

	&.ea {
		background-color: #bde2fe;
		color: #0380e3;
	}

	&.pg {
		background-color: #b7efc3;
		color: #28ab45;
	}
}

.entry {
	display: flex;
	flex-direction: column;

	.entry-label {
		//styleName: Title/4;
		font-family: Poppins;
		font-size: 14px;
		font-weight: 600;
		line-height: 18px;
		letter-spacing: 0.25px;
		text-align: left;
		color: #51555a;
	}

	.entry-value {
		//styleName: Body/2;
		font-family: Nunito Sans;
		font-size: 14px;
		font-weight: 400;
		line-height: 18px;
		letter-spacing: 0px;
		text-align: left;
		color: #51555a;
	}
}
