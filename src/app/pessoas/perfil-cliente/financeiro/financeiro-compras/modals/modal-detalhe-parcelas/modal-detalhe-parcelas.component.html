<div class="modal-content-wrapper">
	<div class="row">
		<div class="col-2 entry">
			<span class="entry-label">Cód.</span>
			<span class="entry-value">
				{{ movProduto?.contrato?.codigo || "-" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Descrição</span>
			<span class="entry-value">
				{{ movProduto?.descricao | captalize : true }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Lançamento</span>
			<span class="entry-value">
				{{ movProduto?.dataLancamento | date : "dd/MM/yyyy" }}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Valor uni.</span>
			<span class="entry-value">
				{{
					movProduto?.precoUnitario || 0 | currency : "BRL" : "symbol" : "1.2-2"
				}}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Desconto</span>
			<span class="entry-value">
				{{
					movProduto?.valorDesconto || 0 | currency : "BRL" : "symbol" : "1.2-2"
				}}
			</span>
		</div>
		<div class="col-2 entry">
			<span class="entry-label">Total</span>
			<span class="entry-value">
				{{
					movProduto?.totalFinal || 0 | currency : "BRL" : "symbol" : "1.2-2"
				}}
			</span>
		</div>
	</div>
	<hr />

	<pacto-relatorio
		#tableProdutos
		(pageChangeEvent)="onPageChange($event)"
		(pageSizeChange)="onSizeChange($event)"
		(sortEvent)="onSortChange($event)"
		[emptyStateMessage]="emptyMessage"
		[enableZebraStyle]="true"
		[showShare]="false"
		[table]="tableProdutosCobradosParcela"
		class="table-produtos-parcela"
		idSuffix="tbl-prod-cobrados-parcela"></pacto-relatorio>

	<ng-template #columnContratoTitle>
		<span class="columnTitle">Código parcela</span>
	</ng-template>
	<ng-template #columnDescricaoTitle>
		<span class="columnTitle">Descrição</span>
	</ng-template>
	<ng-template #columnEmpresaTitle>
		<span class="columnTitle">Empresa</span>
	</ng-template>
	<ng-template #columnSituacaoTitle>
		<span class="columnTitle">Situação</span>
	</ng-template>
	<ng-template #columnVencimentoTitle>
		<span class="columnTitle">Vencimento</span>
	</ng-template>
	<ng-template #columnDataPagamentoTitle>
		<span class="columnTitle">Pagamento</span>
	</ng-template>
	<ng-template #columnValorTotalTitle>
		<span class="columnTitle">Valor total</span>
	</ng-template>
	<ng-template #cellSituacao let-item="item">
		<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
			<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
			<ng-container *ngIf="item.situacao === 'EA'">Em aberto</ng-container>
			<ng-container *ngIf="item.situacao === 'PG'">Pago</ng-container>
		</div>
	</ng-template>
</div>
