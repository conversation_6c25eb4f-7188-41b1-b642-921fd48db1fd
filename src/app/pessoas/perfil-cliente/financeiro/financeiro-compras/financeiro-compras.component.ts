import { <PERSON><PERSON><PERSON>cy<PERSON>ipe, DatePipe } from "@angular/common";
import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { TipoProduto } from "produto-api";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
} from "ui-kit";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";
import { ImprimirContratoPrestacaoServicoComponent } from "./imprimir-contrato-prestacao-servico/imprimir-contrato-prestacao-servico.component";
import { ModalDetalheParcelasComponent } from "./modals/modal-detalhe-parcelas/modal-detalhe-parcelas.component";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";

@Component({
	selector: "pacto-financeiro-compras",
	templateUrl: "./financeiro-compras.component.html",
	styleUrls: ["./financeiro-compras.component.scss"],
})
export class FinanceiroComprasComponent implements OnInit {
	@Output() hasDataCompra = new EventEmitter();

	@Input() dadosPessoais: ClienteDadosPessoais;

	tableCompras: PactoDataGridConfig;

	@ViewChild("tableComprasCod", { static: true })
	tableComprasCod: TemplateRef<any>;

	@ViewChild("tableComprasContrato", { static: true })
	tableComprasContrato: TemplateRef<any>;

	@ViewChild("tableComprasEmpresa", { static: true })
	tableComprasEmpresa: TemplateRef<any>;

	@ViewChild("tableComprasDescricao", { static: true })
	tableComprasDescricao: TemplateRef<any>;

	@ViewChild("tableComprasLancamento", { static: true })
	tableComprasLancamento: TemplateRef<any>;

	@ViewChild("tableComprasQuantidade", { static: true })
	tableComprasQuantidade: TemplateRef<any>;

	@ViewChild("tableComprasValorUnit", { static: true })
	tableComprasValorUnit: TemplateRef<any>;

	@ViewChild("tableComprasDesconto", { static: true })
	tableComprasDesconto: TemplateRef<any>;

	@ViewChild("tableComprasTotal", { static: true })
	tableComprasTotal: TemplateRef<any>;

	@ViewChild("tableComprasSituacao", { static: true })
	tableComprasSituacao: TemplateRef<any>;

	@ViewChild("cellSituacao", { static: true })
	cellSituacao: TemplateRef<any>;

	@ViewChild("tableComprasCellDescricao", { static: true })
	tableComprasCellDescricao: TemplateRef<any>;

	@ViewChild("tableComprasRef", { static: true })
	tableComprasRef: RelatorioComponent;

	movProduto: any;
	data = false;
	dataParcela = false;

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly rest: RestService,
		private readonly currencyPipe: CurrencyPipe,
		private readonly datePipe: DatePipe,
		private readonly dialogService: DialogService,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		private readonly sessionService: SessionService,
		private readonly telaClienteService: AdmLegadoTelaClienteService
	) {}

	ngOnInit() {
		this.initTableCompras();
	}

	initTableCompras() {
		this.tableCompras = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`pessoas/${this.dadosPessoais.codigoPessoa}/produtos`
			),
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				if (this.data) {
					this.hasDataCompra.emit(true);
				}
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: this.tableComprasCod,
					visible: true,
					ordenavel: true,
					width: "5%",
				},
				{
					nome: "contrato",
					titulo: this.tableComprasContrato,
					visible: true,
					ordenavel: true,
					width: "8%",
					valueTransform: (v) => (v && v.codigo ? v.codigo : "-"),
				},
				{
					nome: "empresa",
					titulo: this.tableComprasEmpresa,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.captalize(v.nome),
				},
				{
					nome: "descricao",
					titulo: this.tableComprasDescricao,
					visible: true,
					ordenavel: true,
					celula: this.tableComprasCellDescricao,
				},
				{
					nome: "dataLancamento",
					titulo: this.tableComprasLancamento,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyyy"),
				},
				{
					nome: "quantidade",
					titulo: this.tableComprasQuantidade,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "precoUnitario",
					titulo: this.tableComprasValorUnit,
					visible: true,
					ordenavel: true,
					width: "11%",
					valueTransform: (v) =>
						this.currencyPipe.transform(
							parseFloat(v ? v.toString().replace(",", ".") : 0),
							"BRL",
							"symbol",
							"1.2-2"
						),
				},
				{
					nome: "valorDesconto",
					titulo: this.tableComprasDesconto,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						this.currencyPipe.transform(
							parseFloat(v ? v.toString().replace(",", ".") : 0),
							"BRL",
							"symbol",
							"1.2-2"
						),
				},
				{
					nome: "totalFinal",
					titulo: this.tableComprasTotal,
					visible: true,
					ordenavel: true,
					valueTransform: (v) =>
						this.currencyPipe.transform(
							parseFloat(v ? v.toString().replace(",", ".") : 0),
							"BRL",
							"symbol",
							"1.2-2"
						),
				},
				{
					nome: "situacao",
					titulo: this.tableComprasSituacao,
					visible: true,
					ordenavel: true,
					celula: this.cellSituacao,
					width: "8%",
				},
			],
			actions: [
				{
					nome: "ACTION_DETALHAR_PARCELAS",
					iconClass: "pct pct-zoom-in cor-azulim05",
					tooltipText: "Detalhar parcelas",
				},
				{
					nome: "ACTION_REVERSAL",
					iconClass: "pct pct-reversal cor-azulim05",
					tooltipText: "Estornar produto",
					showIconFn: (row) => this.apresetarEstorno(row),
				},
				{
					nome: "ACTION_CANCELAR_SESSAO",
					iconClass: "pct pct-x-circle cor-azulim05",
					tooltipText: "Cancelar sessão",
					showIconFn: (row) => this.apresentarCancelarSessao(row),
				},
				{
					nome: "ACTION_IMPRIMIR_CONTRATO_PRESTACAO_SERVICO",
					iconClass: "pct pct-file-text cor-azulim05",
					tooltipText: "Imprimir contrato de prestação de serviço",
					showIconFn: (row) =>
						this.apresentarImprimirContratoPrestacaoServico(row),
				},
				{
					nome: "ACTION_IMPRIMIR_RECIBO_DEVOLUCAO",
					iconClass: "pct pct-payment-form cor-azulim05",
					tooltipText: "Imprimir recibo devolução",
					showIconFn: (row) => this.apresentarImprimirReciboDevolucao(row),
				},
			],
		});
	}

	iconClickFnTableCompras(event) {
		switch (event.iconName) {
			case "ACTION_REVERSAL":
				this.actionEstornarProduto(event.row);
				break;
			case "ACTION_CANCELAR_SESSAO":
				this.actionCancelarSessao(event.row);
				break;
			case "ACTION_IMPRIMIR_CONTRATO_PRESTACAO_SERVICO":
				this.actionImprimirContratoPrestacaoServico(event.row);
				break;
			case "ACTION_IMPRIMIR_RECIBO_DEVOLUCAO":
				this.actionImprimirReciboDevolucao(event.row);
				break;
			case "ACTION_DETALHAR_PARCELAS":
				this.detalharacelasDaCompra(event.row);
				break;
			default:
				break;
		}
	}

	detalharacelasDaCompra(objetoLista) {
		const dialogRef = this.dialogService.open(
			"Parcelas cobradas",
			ModalDetalheParcelasComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.dadosPessoais = this.dadosPessoais;
		dialogRef.componentInstance.movProduto = objetoLista;
		this.cd.detectChanges();
	}

	actionEstornarProduto(row) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				const url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=ESTORNAR_PRODUTO&isOperacaoFinanceiro=true&codMovProduto=${row.codigo}&origem=angular`;
				this.abrirPopup(url, "Estorno de produto", 800, 595);
			});
	}

	private apresetarEstorno(row) {
		return (
			!row.contrato ||
			(!row.contrato.codigo &&
				(row.produto.tipoProduto === TipoProduto.produto_estoque ||
					row.produto.tipoProduto === TipoProduto.servico ||
					row.produto.tipoProduto === TipoProduto.diaria ||
					row.produto.tipoProduto === TipoProduto.aula_avulsa ||
					row.produto.tipoProduto === TipoProduto.sessao ||
					(row.produto.tipoProduto ===
						TipoProduto.deposito_conta_corrente_aluno &&
						row.situacao === "EA") ||
					(row.produto.tipoProduto ===
						TipoProduto.acerto_conta_corrente_aluno &&
						row.situacao === "EA") ||
					row.produto.tipoProduto === TipoProduto.taxa_personal ||
					row.produto.tipoProduto ===
						TipoProduto.taxa_de_adesao_plano_recorrencia ||
					row.produto.tipoProduto === TipoProduto.armario ||
					row.produto.tipoProduto === TipoProduto.desafio ||
					row.produto.tipoProduto === TipoProduto.credito_personal ||
					row.produto.tipoProduto === TipoProduto.homefit ||
					row.produto.tipoProduto === TipoProduto.appVitio ||
					row.produto.tipoProduto === TipoProduto.bioTotem ||
					row.produto.tipoProduto === TipoProduto.consultaNutricional))
		);
	}

	actionCancelarSessao(row) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=CANCELAR_SESSAO&isOperacaoFinanceiro=true&codMovProduto=${row.codigo}`;
				// Fixes dual-screen position                             Most browsers      Firefox
				const dualScreenLeft =
					window.screenLeft !== undefined ? window.screenLeft : window.screenX;
				const dualScreenTop =
					window.screenTop !== undefined ? window.screenTop : window.screenY;
				const windowWidth = 800;
				const windowHeight = 525;
				const width = window.innerWidth
					? window.innerWidth
					: document.documentElement.clientWidth
					? document.documentElement.clientWidth
					: screen.width;
				const height = window.innerHeight
					? window.innerHeight
					: document.documentElement.clientHeight
					? document.documentElement.clientHeight
					: screen.height;

				const systemZoom = width / window.screen.availWidth;
				const posTopo =
					(height - windowHeight) / 2 / systemZoom + dualScreenTop;
				const posEsquerda =
					(width - windowWidth) / 2 / systemZoom + dualScreenLeft;

				let features = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo},`;
				features += ` width=${windowWidth}, height=${windowHeight} dependent=yes, menubar=no,`;
				features += " toolbar=no, resizable=yes , scrollbars=yes";
				const win = window.open(url, "Estornar produto", features);
				const timer = setInterval(() => {
					if (win.closed) {
						this.tableComprasRef.reloadData();
					}
				}, 500);
			});
	}

	apresentarCancelarSessao(row) {
		const tipoProduto = TipoProduto;
		return (
			(!row.contrato || !row.contrato.codigo) &&
			row.produto.tipoProduto === tipoProduto.sessao &&
			row.situacao !== "CA" &&
			this.sessionService.perfilUsuarioAdm.perfilUsuario.funcionalidades.findIndex(
				(f) => f.nome === "cancelarsessao" && f.possuiFuncionalidade
			) > -1
		);
	}

	actionImprimirContratoPrestacaoServico(row) {
		const dialogRef = this.dialogService.open(
			"Imprimir contrato de prestação de serviços",
			ImprimirContratoPrestacaoServicoComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.movProduto = row;
	}

	apresentarImprimirContratoPrestacaoServico(row) {
		return (
			(!row.contrato || !row.contrato.codigo) &&
			row.produto.tipoProduto === TipoProduto.sessao &&
			row.situacao !== "CA"
		);
	}

	actionImprimirReciboDevolucao(row) {
		this.telaClienteService
			.imprimirReciboDevolucaoMovProduto(
				this.sessionService.chave,
				row.reciboDevolucao.codigo
			)
			.subscribe((response) => {
				window.open(response.content, "_blank");
			});
	}

	apresentarImprimirReciboDevolucao(row) {
		return row.reciboDevolucao && row.reciboDevolucao.codigo;
	}

	abrirPopup(
		URL: string,
		nomeJanela: string,
		comprimento: number,
		altura: number
	): boolean {
		const posTopo = 0;
		const posEsquerda = 0;
		const atributos = `left=${posEsquerda}, screenX=${posEsquerda}, top=${posTopo}, screenY=${posTopo}, width=${comprimento}, height=${altura}, dependent=yes, menubar=no, toolbar=no, resizable=yes, scrollbars=yes`;

		const parameterCaracter = URL.includes("?") ? "&" : "?";
		const urlPopup = URL + parameterCaracter + "from=popup";
		const win = window.open(urlPopup, nomeJanela, atributos);
		const timer = setInterval(() => {
			if (win.closed) {
				clearInterval(timer);
				this.tableComprasRef.reloadData();
			}
		}, 500);
		return false;
	}

	captalize(valor) {
		const captalizePipe = new CaptalizePipe();
		return captalizePipe.transform(valor);
	}
}
