import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-financeiro-compras-estornar-produto",
	templateUrl: "./financeiro-compras-estornar-produto.component.html",
	styleUrls: ["./financeiro-compras-estornar-produto.component.scss"],
})
export class FinanceiroComprasEstornarProdutoComponent implements OnInit {
	@Input() codMovProduto;
	@Input() codCliente;
	url!: string;

	constructor(
		private dialog: NgbActiveModal,
		private clientDiscoveryService: ClientDiscoveryService,
		private session: SessionService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.clientDiscoveryService
			.linkZw(this.session.usuarioOamd, this.session.empresaId)
			.subscribe((result) => {
				this.url = `${result}&codCliente=${this.codCliente}&operacaoClienteEnumName=ESTORNAR_PRODUTO&isOperacaoFinanceiro=true&codMovProduto=${this.codMovProduto}`;
				this.cd.detectChanges();
			});
	}

	messageReceived(messageEvent: { event: MessageEvent; dataParsed: any }) {
		if (messageEvent && messageEvent.dataParsed) {
			if (messageEvent.dataParsed) {
				if (messageEvent.dataParsed.close) {
					this.dialog.close();
				} else if (messageEvent.dataParsed.reload) {
					this.dialog.close({ reload: true });
				}
			}
		}
	}
}
