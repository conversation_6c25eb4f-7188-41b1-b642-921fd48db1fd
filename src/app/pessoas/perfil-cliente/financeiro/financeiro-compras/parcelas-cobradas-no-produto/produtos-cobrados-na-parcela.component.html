<div [hidden]="dataParcela" class="div-empty">
	<div class="title-text-empty">Histórico de parcelas</div>
	<div class="d-flex flex-column align-items-center">
		<img class="icon-empty" src="assets/images/empty-state-installments.svg" />
		<div class="text-empty mt-2 body-text-empty">
			O aluno ainda não possui nenhum histórico de parcelas!
		</div>
	</div>
</div>

<div [hidden]="!dataParcela">
	<pacto-relatorio
		#tableProdutos
		(pageChangeEvent)="onPageChange($event)"
		(pageSizeChange)="onSizeChange($event)"
		(sortEvent)="onSortChange($event)"
		[emptyStateMessage]="emptyMessage"
		[enableZebraStyle]="true"
		[showShare]="false"
		[table]="tableProdutosCobradosParcela"
		class="table-produtos-parcela"
		idSuffix="tbl-prod-cobrados-parcela"
		tableTitle="Histórico de Parcelas"></pacto-relatorio>

	<ng-template #columnCodigoTitle>
		<span class="columnTitle">Cód.</span>
	</ng-template>
	<ng-template #columnContratoTitle>
		<span class="columnTitle">Contrato</span>
	</ng-template>
	<ng-template #columnEmpresaTitle>
		<span class="columnTitle">Empresa</span>
	</ng-template>
	<ng-template #columnDescricaoTitle>
		<span class="columnTitle">Descrição</span>
	</ng-template>
	<ng-template #columnVencimentoTitle>
		<span class="columnTitle">Vencimento</span>
	</ng-template>
	<ng-template #columnValorTotalTitle>
		<span class="columnTitle">Valor</span>
	</ng-template>
	<ng-template #columnDataPagamentoTitle>
		<span class="columnTitle">Pagamento</span>
	</ng-template>
	<ng-template #columnSituacaoTitle>
		<span class="columnTitle">Situação</span>
	</ng-template>
	<ng-template #cellSituacao let-item="item">
		<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
			<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
			<ng-container *ngIf="item.situacao === 'EA'">Em aberto</ng-container>
			<ng-container *ngIf="item.situacao === 'PG'">Pago</ng-container>
			<ng-container *ngIf="item.situacao === 'RG'">Renegociado</ng-container>
		</div>
	</ng-template>

	<ng-template #colunaDataVencimento let-item="item">
		<span>{{ item.dataVencimento | rawDate }}</span>
	</ng-template>
</div>
