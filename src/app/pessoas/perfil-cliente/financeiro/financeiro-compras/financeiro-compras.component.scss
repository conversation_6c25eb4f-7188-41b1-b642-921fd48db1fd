@import "src/assets/scss/pacto/plataforma-import.scss";

::ng-deep.table-compras > .pacto-table-title-block {
	padding-left: unset;
}

:host {
	.situacao {
		width: 6rem;
		padding: 0.3125rem 1rem;
		border-radius: 3.125rem;
		font-size: 0.75rem;
		font-weight: 400;
		text-align: center;

		&.ca {
			background-color: #f3b4ba;
			color: #ba202f;
		}

		&.ea {
			background-color: #bde2fe;
			color: #0380e3;
		}

		&.pg {
			background-color: #b7efc3;
			color: #28ab45;
		}
	}

	::ng-deep.columnTitle,
	::ng-deep.action-column {
		@extend .type-p-small-rounded;
		font-size: 14px !important;
		font-weight: 700 !important;
		color: $pretoPri;
	}

	.interagivel {
		cursor: pointer;
		color: $azulim06 !important;
		text-decoration: none !important;
	}

	.section-title {
		@extend .type-h6-bold;
		font-weight: 700;
		color: $pretoPri;
	}

	::ng-deep.NFCE::before {
		content: "NFC\002D e ";
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: 700;
	}

	::ng-deep.NFSE::before {
		content: "NFS\002D e ";
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: 700;
	}

	::ng-deep.table-content {
		padding: 20px 0 0 0 !important;
	}

	::ng-deep .action-cell {
		display: flex;

		i {
			margin: 0 8px;
		}
	}
}

.div-empty {
	padding-bottom: 20px;
}
