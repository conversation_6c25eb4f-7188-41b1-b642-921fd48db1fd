<div class="modal-imprimir-contrato-prestacao-servico">
	<ng-container *ngIf="!apresentarMensagemErro">
		<form [formGroup]="form">
			<pacto-cat-form-select
				[control]="form.get('planoTextoPadrao')"
				[items]="modelosContrato"
				errorMsg="Selecione um modelo de contrato"
				idKey="codigo"
				label="Escolha o modelo de contrato"
				labelKey="descricao"></pacto-cat-form-select>
			<div class="micps-btn-row">
				<pacto-cat-button
					(click)="print()"
					[disabled]="form.invalid || !form.get('planoTextoPadrao').value"
					label="Imprimir"
					size="LARGE"></pacto-cat-button>
			</div>
		</form>
	</ng-container>

	<ng-container *ngIf="apresentarMensagemErro">
		<div class="micps-error-msg">
			<i class="pct pct-alert-triangle"></i>
			<span>
				Não existe Modelo de Contrato de Prestação de Serviço ativo. Cadastre um
				novo modelo ou Ative um existente para realizar essa impressão
			</span>
		</div>
	</ng-container>
</div>
