import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SessionService } from "@base-core/client/session.service";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	ApiResponseList,
	CadastroAuxApiModeloContratoService,
	ModeloContrato,
} from "cadastro-aux-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { PrintService } from "../../../../../print/print.component";
import { ActivatedRoute, Router } from "@angular/router";
import { ClientDiscoveryService } from "../../../../../microservices/client-discovery/client-discovery.service";

@Component({
	selector: "pacto-modal-imprimir-contrato-prestacao-serviceo",
	templateUrl: "./imprimir-contrato-prestacao-servico.component.html",
	styleUrls: ["./imprimir-contrato-prestacao-servico.component.scss"],
})
export class ImprimirContratoPrestacaoServicoComponent implements OnInit {
	apresentarMensagemErro = false;
	movProduto;
	modelosContrato = new Array<any>();
	form = new FormGroup({
		planoTextoPadrao: new FormControl(undefined, Validators.required),
	});

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private modeloContratoService: CadastroAuxApiModeloContratoService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private printService: PrintService,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private clientDiscoveryService: ClientDiscoveryService,
		@Inject(LOCALE_ID) private locale
	) {}

	ngOnInit() {
		this.obterModelosContrato();
	}

	obterModelosContrato() {
		this.modeloContratoService
			.findBySituacaoTipoContrato("AT", "SE")
			.subscribe((response: ApiResponseList<ModeloContrato>) => {
				this.modelosContrato = response.content;
				if (!this.modelosContrato || this.modelosContrato.length === 0) {
					this.apresentarMensagemErro = true;
				}
				this.cd.detectChanges();
			});
	}

	print() {
		if (this.form.valid) {
			this.telaClienteService
				.imprimirContratoPrestacaoServico(
					this.sessionService.chave,
					this.movProduto.codigo,
					this.sessionService.empresaId,
					this.form.get("planoTextoPadrao").value,
					this.sessionService.usuarioOamd
				)
				.subscribe((response) => {
					this.printService.htmlContent = response.content;
					console.log(this.activatedRoute.parent);
					const url = this.router.serializeUrl(
						this.router.createUrlTree(["..", "print"])
					);

					const trueUrl = `${
						this.clientDiscoveryService.getUrlMap().treinoFrontUrl
					}/${this.locale}${url}`;
					window.open(trueUrl, "_blank");
				});
		}
	}
}
