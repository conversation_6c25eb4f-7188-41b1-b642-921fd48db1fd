<div [hidden]="data" class="div-empty">
	<div class="title-text-empty">Histórico de compras</div>
	<div class="d-flex flex-column align-items-center">
		<img class="icon-empty" src="assets/images/empty-state-cash-register.svg" />
		<div class="text-empty mt-2 body-text-empty">
			O aluno ainda não possui nenhum histórico de compras!
		</div>
	</div>
</div>

<div [hidden]="!data" class="row secao-compras">
	<div class="col-12">
		<pacto-relatorio
			#tableComprasRef
			(iconClick)="iconClickFnTableCompras($event)"
			[enableZebraStyle]="true"
			[showShare]="false"
			[table]="tableCompras"
			actionTitulo="Ações"
			class="table-compras"
			idSuffix="pch-fin-table-compras"
			tableTitle="Histórico de Compras"></pacto-relatorio>

		<ng-template #tableComprasCod>
			<span class="columnTitle">Cód.</span>
		</ng-template>
		<ng-template #tableComprasContrato>
			<span class="columnTitle">Contrato</span>
		</ng-template>
		<ng-template #tableComprasEmpresa>
			<span class="columnTitle">Empresa</span>
		</ng-template>
		<ng-template #tableComprasDescricao>
			<span class="columnTitle">Descrição</span>
		</ng-template>
		<ng-template #tableComprasLancamento>
			<span class="columnTitle">Lançamento</span>
		</ng-template>
		<ng-template #tableComprasQuantidade>
			<span class="columnTitle">Qtd.</span>
		</ng-template>
		<ng-template #tableComprasValorUnit>
			<span class="columnTitle">Valor unit.</span>
		</ng-template>
		<ng-template #tableComprasDesconto>
			<span class="columnTitle">Desconto</span>
		</ng-template>
		<ng-template #tableComprasTotal>
			<span class="columnTitle">Total</span>
		</ng-template>
		<ng-template #tableComprasSituacao>
			<span class="columnTitle">Situação</span>
		</ng-template>
		<ng-template #cellSituacao let-item="item">
			<div [ngClass]="['situacao', item.situacao.toLowerCase()]">
				<ng-container *ngIf="item.situacao === 'CA'">Cancelado</ng-container>
				<ng-container *ngIf="item.situacao === 'EA'">Em aberto</ng-container>
				<ng-container *ngIf="item.situacao === 'PG'">Pago</ng-container>
			</div>
		</ng-template>
		<ng-template #tableComprasCellDescricao let-item="item">
			{{ item.descricao | captalize }}
		</ng-template>
	</div>
</div>
