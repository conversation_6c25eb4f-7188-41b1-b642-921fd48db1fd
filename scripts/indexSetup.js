/* eslint-disable no-undef */
const fs = require('fs');
const argparse = require('argparse');

/**
 * USAGE: node configEnvironment.js 
 * 
 * Optional arguments:
 *   -h, --help            Show this help message and exit.
 * 
 *   -u RELATIVE_PATH, --relative_path RELATIVE_PATH
 * 
*/
var ArgumentParser = argparse.ArgumentParser;
var parser = new ArgumentParser();
parser.addArgument(['-u', '--relative_path']);
var args = parser.parseArgs();

let data = fs.readFileSync('./src/index.html', 'utf8');
data = data.replace('{BASE_URL}', args.relative_path);
data = data.replace('{BASE_URL_LANG}', args.relative_path);
fs.writeFileSync('./src/index.html', data, () => {});



