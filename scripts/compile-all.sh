#!/bin/bash
set -e

# DEPRECATED: Este script foi substituido por comando no package.json
# Consulte a sessão ### Para realizar deploy em produção no README.md

DISCOVERY_URL='{DISCOVERY_URL}'
ENABLE_NEW_LOGIN='{ENABLE_NEW_LOGIN}'
ENABLE_LOGIN_CAPTCHA='{ENABLE_LOGIN_CAPTCHA}'

RELATIVE_PATH=$(dirname $0)

echo "Insall dependencies ============"
npm install
npm rebuild node-sass

echo "Build project sdk  ============"
bash $RELATIVE_PATH/build-sdk.sh -y "$DISCOVERY_URL" -n "$ENABLE_NEW_LOGIN"

echo "Build root project ============"
bash $RELATIVE_PATH/build-spa.sh -u '/pt/' \
    -l 'pt' \
    -y "$DISCOVERY_URL" \
    -n "$ENABLE_NEW_LOGIN"

bash $RELATIVE_PATH/build-spa.sh -u '/es/' \
    -l 'es' \
    -y "$DISCOVERY_URL" \
    -n "$ENABLE_NEW_LOGIN"

bash $RELATIVE_PATH/build-spa.sh -u '/en/' \
    -l 'en' \
    -y "$DISCOVERY_URL" \
    -n "$ENABLE_NEW_LOGIN"

echo "Build project canal-cliente  ============"
bash $RELATIVE_PATH/build-canal-cliente.sh -u "/pt/"

echo "BUILDING PACTO-LAYOUT"
./scripts/build-pacto-layout.sh

echo "Build project adm  ============"
bash $RELATIVE_PATH/build-adm.sh -u "/pt/" \
    -l 'pt'
bash $RELATIVE_PATH/build-adm.sh -u "/es/" \
    -l 'es'
bash $RELATIVE_PATH/build-adm.sh -u "/en/" \
    -l 'en'

echo "Build project login  ============"
bash $RELATIVE_PATH/build-login.sh -u "/pt/" \
    -l 'pt' -y "$DISCOVERY_URL" -e "$ENABLE_LOGIN_CAPTCHA"
bash $RELATIVE_PATH/build-login.sh -u "/es/" \
    -l 'es' -y "$DISCOVERY_URL" -e "$ENABLE_LOGIN_CAPTCHA"
bash $RELATIVE_PATH/build-login.sh -u "/en/" \
    -l 'en' -y "$DISCOVERY_URL" -e "$ENABLE_LOGIN_CAPTCHA"

echo "Build project crm pt  ============"
bash $RELATIVE_PATH/build-crm.sh -u "/pt/" \
    -l 'pt'
echo "Build project crm es  ============"
bash $RELATIVE_PATH/build-crm.sh -u "/es/" \
    -l 'es'
echo "Build project crm en  ============"
bash $RELATIVE_PATH/build-crm.sh -u "/en/" \
    -l 'en'
    
echo "Build project marketing  ============"
bash $RELATIVE_PATH/build-marketing.sh

