#!/bin/bash

#
# ./scripts/build-canal-cliente.sh -u '/canal-cliente/'
#
# 'u' - relative_url

set -e

while getopts ":u:l:" opt; do
    case $opt in
        u) relative_url="$OPTARG"
        ;;
        l) language="$OPTARG"
        ;;
        y) discovery_url="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# BUILDING API ADAPTERS
node  --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build marketing-api

# BUILDING PROJECTS
node  --max_old_space_size=4096 ./node_modules/@angular/cli/bin/ng build marketing
