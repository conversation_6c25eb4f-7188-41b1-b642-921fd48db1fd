#!/bin/bash

#
# ./scripts/build-canal-cliente.sh -u '/canal-cliente/'
#
# 'u' - relative_url
set -e

while getopts ":y:u:l:e:" opt; do
    case $opt in
        y) discovery_url="$OPTARG"
        ;;
        u) relative_url="$OPTARG"
        ;;
        l) language="$OPTARG"
        ;;
        e) enable_captcha="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng lint login

node ./scripts/config-login.js -u "$relative_url" -y "$discovery_url" -e "$enable_captcha"

# BULDING PT
if [ "$language" = "pt" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build login
fi

# BULDING ES
if [ "$language" = "es" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build login -c es
fi

# BULDING EN
if [ "$language" = "en" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build login -c en
fi
