/* eslint-disable no-undef */
const fs = require('fs');
require('typescript-require');
const path = require('path');
const argparse = require('argparse');
const prettyJSONStringify = require("pretty-json-stringify");

/**
 *
 * USAGE: node config-marketing.js -y '{DISCOVERY_URL}'
 *
 */
var ArgumentParser = argparse.ArgumentParser;
var parser = new ArgumentParser();
parser.addArgument(['-y', '--discovery_url']);

var args = parser.parseArgs();

environment = require('../projects/marketing/src/environments/environment.ts').environment;

const template = path.join(__dirname, '..', '/projects/marketing/src/index.root.html');
const final = path.join(__dirname, '..', '/projects/marketing/src/index.html');

let data = fs.readFileSync(template, 'utf8');
data = data.replace('{BASE_URL}', args.relative_path);
fs.writeFileSync(final, data, () => {});

environment.production = true;
environment.discoveryMsUrl = args.discovery_url;

console.log('------------------------');
console.log(environment);
console.log('------------------------');

dataOut = `// tslint:disable
export const environment = ${prettyJSONStringify(environment)}`;
dataOut = dataOut.replace(/"/g, "'");
dataOut += ';';

fs.writeFileSync(path.join(__dirname, '..', '/projects/marketing/src/environments/environment.ts'), dataOut);
