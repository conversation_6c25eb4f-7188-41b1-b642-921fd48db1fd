#!/bin/bash

#
# ./scripts/build-canal-cliente.sh -u '/canal-cliente/'
#
# 'u' - relative_url

set -e

# Parsear opções
while getopts ":u:" opt; do
    case $opt in
        u) relative_url="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# LINTING
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng lint canal-cliente --base-href "$relative_url"

# Index setup
node ./scripts/config-canal-cliente.js -u "$relative_url"

# BULDING PT
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build canal-cliente --base-href "$relative_url"



