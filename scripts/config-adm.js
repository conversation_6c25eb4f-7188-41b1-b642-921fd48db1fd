/* eslint-disable no-undef */
const fs = require('fs');
const path = require('path');
const argparse = require('argparse');

/**
 *
 * USAGE: node config-adm.js -u '/pt/'
 *
 */
var ArgumentParser = argparse.ArgumentParser;
var parser = new ArgumentParser();
parser.addArgument(['-u', '--relative_path']);
var args = parser.parseArgs();

const template = path.join(__dirname, '..', '/projects/adm/src/index.html');
const final = path.join(__dirname, '..', '/projects/adm/src/index.html');

let data = fs.readFileSync(template, 'utf8');
data = data.replace('{BASE_URL}', args.relative_path);
fs.writeFileSync(final, data, () => {});
