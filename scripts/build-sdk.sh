#!/bin/bash
set -e

while getopts ":y:n:" opt; do
    case $opt in
        y) discovery_url="$OPTARG"
        ;;
        n) enable_new_login="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

echo "1. Building sdk"

node ./scripts/configEnvironment.js -y "$discovery_url" -n "$enable_new_login"
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build sdk
