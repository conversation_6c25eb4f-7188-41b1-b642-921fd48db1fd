#!/bin/bash

set -e

while getopts ":u:l:" opt; do
    case $opt in
        u) relative_url="$OPTARG"
        ;;
        l) language="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# BULDING PT
if [ "$language" = "pt" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build crm --base-href "$relative_url"
fi

# BULDING ES
if [ "$language" = "es" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build crm -c es --base-href "$relative_url"
fi

# BULDING ES
if [ "$language" = "en" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build crm -c en --base-href "$relative_url"
fi
