#!/bin/bash

#
# 'y' - discovery url
# 'u' - relative_url
# 'l' - language: 'pt' | 'es'

set -e

# Parsear opções
while getopts ":y:u:l:n:" opt; do
    case $opt in
        y) discovery_url="$OPTARG"
        ;;
        u) relative_url="$OPTARG"
        ;;
        l) language="$OPTARG"
        ;;
        n) enable_new_login="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# LINTING
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng lint plataforma-pacto

# Configurando o arquivo environment.ts
node ./scripts/configEnvironment.js -y "$discovery_url" -n "$enable_new_login"

# Index setup
node ./scripts/indexSetup.js -u "$relative_url"

echo "BUILDING SDK"
./scripts/build-sdk.sh -y "$discovery_url"

# BUILDING UI
./scripts/build-ui.sh

echo "BUILDING PACTO-LAYOUT"
./scripts/build-pacto-layout.sh

# BUILDING API ADAPTERS
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build treino-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build integracao-gympass-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build zw-pactopay-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build zw-servlet-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build cadastro-aux-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build plano-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build produto-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build ms-pactopay-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build crm-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build midia-social-api

#BUILDING ADM API ADAPTERS
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build acesso-sistema-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build adm-core-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build adm-ms-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build pactopay-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build relatorio-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build pacto-api

# BUILDING ADM API ADAPTERS
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build clube-vantagens-api
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build login-app-api

# BULDING PT
if [ "$language" = "pt" ]; then
    node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build --base-href "$relative_url"
fi
# BULDING ES
if [ "$language" = "es" ]; then
    node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build -c es --base-href "$relative_url"
fi
# BULDING EN
if [ "$language" = "en" ]; then
    node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build -c en --base-href "$relative_url"
fi


