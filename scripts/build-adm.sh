#!/bin/bash

#
# ./scripts/build-canal-cliente.sh -u '/canal-cliente/'
#
# 'u' - relative_url

set -e

while getopts ":u:l:" opt; do
    case $opt in
        u) relative_url="$OPTARG"
        ;;
        l) language="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng lint adm

node ./scripts/config-adm.js -u "$relative_url"

# BUILDING API ADAPTERS
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build cadastro-aux-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build plano-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build produto-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm-core-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm-ms-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build relatorio-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build acesso-sistema-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build pacto-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build zw-pactopay-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build zw-servlet-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build ms-pactopay-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build marketing-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build integracao-gympass-api

node ./scripts/config-adm.js -u "$relative_url"

# BULDING PT
if [ "$language" = "pt" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm
fi

# BULDING ES
if [ "$language" = "es" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm -c es
fi

# BULDING ES
if [ "$language" = "en" ]; then
    node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm -c en
fi
