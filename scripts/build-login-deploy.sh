#!/bin/bash

#
# ./scripts/build-canal-cliente.sh -u '/canal-cliente/'
#
# 'u' - relative_url
set -e

while getopts ":y:u:l:e:n:" opt; do
    case $opt in
        y) discovery_url="$OPTARG"
        ;;
        u) relative_url="$OPTARG"
        ;;
        l) language="$OPTARG"
        ;;
        e) enable_captcha="$OPTARG"
        ;;
        n) enable_new_login="$OPTARG"
        ;;
        \?) echo "Invalid option -$OPTARG" >&2
        ;;
    esac
done

# node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng lint login

echo "BUILDING SDK"
./scripts/build-sdk.sh -y "$discovery_url" -n "$enable_new_login"

echo "BUILDING UI"
./scripts/build-ui.sh

node ./scripts/config-login.js -u "$relative_url" -y "$discovery_url" -e "$enable_captcha"

echo "Building login adapter api"
node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build login-app-api

# BULDING PT
if [ "$language" = "pt" ]; then
    node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build login --base-href "$relative_url"
fi

# BULDING ES
if [ "$language" = "es" ]; then
    node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build login -c es --base-href "$relative_url"
fi

# BULDING ES
if [ "$language" = "en" ]; then
    node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build login -c en --base-href "$relative_url"
fi

