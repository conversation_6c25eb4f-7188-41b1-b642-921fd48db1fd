/* eslint-disable no-undef */
const fs = require('fs');
require('typescript-require');
const prettyJSONStringify = require('pretty-json-stringify');

/**
 * USAGE: node configEnvironment.js 
 * 
 * Optional arguments:
 *   -h, --help            Show this help message and exit.
 *   -y DISCOVERY_URL, --discovery_url DISCOVERY_URL
 * 
*/
var ArgumentParser = require('argparse').ArgumentParser;
var parser = new ArgumentParser();
parser.addArgument(['-y', '--discovery_url']);
parser.addArgument(['-n', '--enable_new_login']);
var args = parser.parseArgs();

environment = require('../src/environments/environment.ts').environment;

/**
 * Replace values
 */
environment.production = true;
environment.discoveryMsUrl = args.discovery_url;

if (args.enable_new_login === '') {
    environment.newLoginEnabled = false;
} else if (args.enable_new_login === '{ENABLE_NEW_LOGIN}') {
    environment.newLoginEnabled = args.enable_new_login;
} else if (args.enable_new_login !== 'true' && args.enable_new_login !== 'false') {
    throw Error(`Invalid value for a boolean: ${args.enable_new_login }`);
} else {
    environment.newLoginEnabled = JSON.parse(args.enable_new_login);
}
console.log('------------------------');
console.log(environment);
console.log('------------------------');

/**
 * Export and save file content
 */
dataOut = `// tslint:disable
export const environment = ${prettyJSONStringify(environment)}`;
dataOut = dataOut.replace(/"/g, "'");
dataOut += ';';

fs.writeFileSync('./src/environments/environment.ts', dataOut);
fs.writeFileSync('./projects/sdk/src/lib/environments/environment.ts', dataOut);