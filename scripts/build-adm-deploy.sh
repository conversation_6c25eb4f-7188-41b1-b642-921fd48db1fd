#!/bin/bash

# 'y' - discovery url
# 'u' - relative_url
# 'l' - language: 'pt' | 'es' | 'en'

set -e

# Parsear opções
while getopts ":y:u:l:" opt; do
    case $opt in
        y) discovery_url="$OPTARG";;
        u) relative_url="$OPTARG";;
        l) language="$OPTARG";;
        \?) echo "Invalid option -$OPTARG" >&2;;
    esac
done

lang="${language:-pt}"

echo 'Variáveis ok?'
echo $discovery_url

#echo "LINTING"
#node --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng lint adm

#echo "Index setup"
#node ./scripts/indexSetup.js -u "$relative_url"

echo "BUILDING SDK"
./scripts/build-sdk.sh -y "$discovery_url"

echo "BUILDING UI"
./scripts/build-ui.sh

echo "Building ADM"
node ./scripts/config-adm.js -u "$relative_url"

node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm-legado-api

# BULDING in LANG
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build treino-api

node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build cadastro-aux-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build plano-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm-core-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build adm-ms-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build clube-vantagens-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build acesso-sistema-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build produto-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build relatorio-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build pacto-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build zw-pactopay-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build zw-servlet-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build ms-pactopay-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build login-app-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build integracao-gympass-api

if [ "$lang" = "pt" ]; then
  node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build adm --base-href "$relative_url"
else
  node  --max_old_space_size=32768 ./node_modules/@angular/cli/bin/ng build adm -c "$lang" --base-href "$relative_url"
fi
