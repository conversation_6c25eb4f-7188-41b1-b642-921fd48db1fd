#!/bin/bash

# 'y' - discovery url
# 'u' - relative_url
# 'l' - language: 'pt' | 'es' | 'en'

set -e

# Parsear opções
while getopts ":y:u:l:" opt; do
    case $opt in
        y) discovery_url="$OPTARG";;
        u) relative_url="$OPTARG";;
        l) language="$OPTARG";;
        \?) echo "Invalid option -$OPTARG" >&2;;
    esac
done

lang="${language:-pt}"

echo "BUILDING SDK"
./scripts/build-sdk.sh -y "$discovery_url"

echo "BUILDING UI"
./scripts/build-ui.sh

echo "Building ADAPTERS"
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build login-app-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build treino-api
node  --max_old_space_size=65536 ./node_modules/@angular/cli/bin/ng build midia-social-api

echo "Configuring environment"
node ./scripts/configEnvironment.js -y "$discovery_url" -n "false"

echo "BUILDING CRM"
./scripts/build-crm.sh -u "$relative_url" -l "$lang"
