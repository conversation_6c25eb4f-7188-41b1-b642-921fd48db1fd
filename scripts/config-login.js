/* eslint-disable no-undef */
const fs = require('fs');
require('typescript-require');
const path = require('path');
const argparse = require('argparse');
const prettyJSONStringify = require("pretty-json-stringify");

/**
 *
 * USAGE: node config-login.js -u '/pt/'
 *
 */
var ArgumentParser = argparse.ArgumentParser;
var parser = new ArgumentParser();
parser.addArgument(['-u', '--relative_path']);
parser.addArgument(['-y', '--discovery_url']);
parser.addArgument(['-e', '--enable_captcha']);

var args = parser.parseArgs();

environment = require('../projects/login/src/environments/environment.ts').environment;

const template = path.join(__dirname, '..', '/projects/login/src/index.html');
const final = path.join(__dirname, '..', '/projects/login/src/index.html');

let data = fs.readFileSync(template, 'utf8');
data = data.replace('{BASE_URL}', args.relative_path);
fs.writeFileSync(final, data, () => {});

environment.production = true;
environment.discoveryMsUrl = args.discovery_url;
if (args.enable_captcha === '') {
    environment.enableCaptcha = true;
} else if (args.enable_captcha === '{ENABLE_LOGIN_CAPTCHA}') {
    environment.enableCaptcha = args.enable_captcha;
} else if (args.enable_captcha !== 'true' && args.enable_captcha !== 'false') {
    throw Error(`Invalid value for a boolean: ${args.enable_captcha }`);
} else {
    environment.enableCaptcha = JSON.parse(args.enable_captcha);
}

console.log('------------------------');
console.log(environment);
console.log('------------------------');

dataOut = `// tslint:disable
export const environment = ${prettyJSONStringify(environment)}`;
dataOut = dataOut.replace(/"/g, "'");
dataOut += ';';

fs.writeFileSync(path.join(__dirname, '..', '/projects/login/src/environments/environment.ts'), dataOut);