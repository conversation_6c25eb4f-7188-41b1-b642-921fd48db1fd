// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')
Cypress.on('uncaught:exception', (err, runnable) => {
    // returning false here prevents <PERSON><PERSON> from
    // failing the test
    return false
})

Cypress.on('test:after:run', (test, run) => {
  
  if (test.title === run.parent.parent.suites[run.parent.parent.suites.length - 1].tests[run.parent.parent.suites[run.parent.parent.suites.length - 1].tests.length - 1].title) {
    localStorage.clear('apiToken');
    console.log('cleared \"apiToken\"');
  }

})

const clear = Cypress.LocalStorage.clear

Cypress.LocalStorage.clear = function (keys, ls, rs) {
  
  if (keys && keys.length == 0) {
    keys = Object.keys(localStorage);
  }
  whitelistKeys = ['apiToken', 'pactozw', 'moduleName'];
  keys = keys.filter(function (i) { return whitelistKeys.indexOf(i) < 0; });
  
  return clear.apply(this, arguments)

}
