// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

Cypress.Commands.add("login", (integrado, usuario, senha) => {

  if (integrado) {
    var key = Cypress.env('chaveIntegrado')
    var integracaoZW = true
  } else {
    var key = Cypress.env('chaveIndependente')
    var integracaoZW = false
  }
  if (usuario == null) {
    var usuario = 'pactobr'
  }
  if (senha == null) {
    var senha = 'JSP)34X&*'
  }


  const token = localStorage.getItem('apiToken')
  const urlTreino = Cypress.env('baseUrl') + 'TreinoWebTronco/prest' 
  const urLogin = urlTreino + '/login' 
  const body = { chave: key, username: usuario, senha: senha }
  let urlAddAccount = Cypress.env('baseUrl') + 'novotreino/pt/adicionarConta?token={token}'+
    '&moduleId=NTR'+
    '&modulos=TR%2CNTR%2CNAV%2CNCR%2CGP'+
    '&integracaoZW='+ integracaoZW +
    '&empresaId=1'+
    '&urladm='+ 
    '&urlapi=' + urlTreino
  if (integrado) {
    urlAddAccount = urlAddAccount.replace('&modulos=', '&modulos=ZW%2C')    
  }
    
    if (!token) {
      cy.request('POST', urLogin, body).then(res => {
        res.body
        urlAddAccount = urlAddAccount.replace('{token}', res.body.content)
        cy.visit(urlAddAccount)
      })
    } else {
      cy.visit('novotreino/pt/treino/bi')
    }

})
// Cypress.Commands.add("login", (login, senha) => {
//   cy.visit ('http://homologacao.pactosolucoes.com.br:8066/novotreino/pt/adicionarConta?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************.2s4AwwAHBHA45a1PLthfWpnQ-QR9EHF9_XcWdZH1wco&moduleId=NTR&modulos=TR%2CNTR%2CNAV%2CNCR%2CGP&integracaoZW=false&empresaId=1&urladm=7d8cc55bfc5e82cb67e463fb758fbe123bc689aeb469b645f50473cd57ab7d986eb35b88ab62b0b340d552a858ff2ff40b03160fe976993dea76b9411f2338007691bd42c85fa9a27c80af2acd5215ff&urlapi=http%3A//homologacao.pactosolucoes.com.br%3A8066/TreinoWebTronco/prest')
// })

// Cadastra um aluno com todos os dados exceto usuário movel
Cypress.Commands.add("cadastro", (nome, dados) => { 
    
    cy.get('#atalho-adicionar-aluno').click()
    cy.get('#nome-aluno-input').type('ALUNO TESTE AUTOMATIZADO')
    cy.get('#situacao-aluno-select').select('ATIVO')
    cy.get('#nivel-aluno-input').select('2')
    cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
    cy.get('#pacto-select-professor-item-10').click()
    cy.get('#data-nascimento-input').type('24/12/1992')
    cy.get('#email-input').type('<EMAIL>')
    cy.get('#email-adicionar').click()
    cy.get('#telefone-input').type('(62) 98574-5784')
    cy.get('#telefone-adicionar').click()
    cy.get('#salvar-cadastro-aluno').click()
    cy.get('.notify-success').should('contain', 'Aluno cadastrado com sucesso')
       
})

//Cria um programa a partir do perfil do aluno, cria duas fichas variando entre atividades cardio e neuro. 
Cypress.Commands.add("LancarProgramaAluno",(ficha, atividade) => {
    
    cy.get('#criar-programa').click()
    cy.get('#montagem-treino-criar-novo').click()
    cy.contains('Programa criado com sucesso')
    cy.get('.form-group > .ng-untouched > .cuppa-dropdown > .selected-list > .c-btn').click()
    cy.get('#select-atividade-search-input').click()
    cy.get('#select-atividade-search-input').type('001 atividade neuro')
    cy.wait(2000)
    cy.get('#select-atividade0').click()
    cy.get('#input-peso').type('33,2')
    cy.get('#input-repticoes').type('12')
    cy.get('#input-series').type('3')
    cy.get('#input-descanso').type('00:30')
    cy.get('#btn-adcionar').click()
    cy.contains('Atividade adicionada')
    cy.get('.form-group > .ng-valid > .cuppa-dropdown > .selected-list > .c-btn').click()
    cy.get('#select-atividade-search-input').click()
    cy.get('#select-atividade-search-input').type('002 atividade neuro')
    cy.wait(2000)
    cy.get('#select-atividade0').click()
    cy.get('#btn-adcionar').click()
    cy.contains('Atividade adicionada')
    cy.get('.form-group > .ng-valid > .cuppa-dropdown > .selected-list > .c-btn').click()
    cy.get('#select-atividade-search-input').click()
    cy.get('#select-atividade-search-input').type('003 atividade neuro')
    cy.wait(2000)
    cy.get('#select-atividade0').click()
    cy.get('#btn-adcionar').click()
    cy.contains('Atividade adicionada')
    // Pesquisa uma atividade espeficica na busca e adiciona a mesma, utilziei uma atividade cardiovascular para esse caso
    cy.get('.form-group > .ng-valid > .cuppa-dropdown > .selected-list > .c-btn').click()
    cy.get('#select-atividade-search-input').click()
    cy.get('#select-atividade-search-input').type('111 atividade cardio')
    cy.wait(2000)
    cy.get('#select-atividade0').click()
    cy.get('#input-velocidade').type('35,8')
    cy.get('#input-duracao').type('20:00')
    cy.get('#input-distancia').type('20')
    cy.get('#input-descanso').type('00:40')
    cy.get('#btn-adcionar').click()
    cy.contains('Atividade adicionada')
    // Adiciona uma segunda ficha
    cy.get('#motagem-treino-nova-ficha').click()
    cy.contains('Ficha criada com sucesso.')
    // Adiciona 1 atividade, já adicionando suas informações de peso, carga e repetição
    cy.get('.form-group > .ng-valid > .cuppa-dropdown > .selected-list > .c-btn').click()
    cy.get('#select-atividade-search-input').click()
    cy.get('#select-atividade-search-input').type('004 atividade neuro')
    cy.wait(2000)
    cy.get('#select-atividade0').click()
    cy.get('#input-peso').type('1')
    cy.get('#btn-adcionar').click()
    cy.contains('Atividade adicionada')
})

// Remove um programa de treino a partir do perfil do aluno
Cypress.Commands.add('removePrograma',(programa, remover) => {
  cy.get('#programa-0-remove').click()
  cy.get('#action-remover').click()
  cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').should('contain','Programa removido com sucesso.' )
})

// Adiciona um progrogama para o aluno aluno a partir do perfil
Cypress.Commands.add('adicionaPrograma',(programa, adicionar) => {
  cy.get('#criar-programa').click()
  cy.get('#montagem-treino-criar-novo').click()
  cy.contains('Programa criado com sucesso')
})

// Preenche os dados de uma atividade, sendo cardio ou neuro. 
Cypress.Commands.add('preencheDadosAtividadeTreino',() => {
  // cy.get('#lista-insert > .comp-wrapper > .ng-untouched > .cuppa-dropdown > .selected-list > .c-btn').click()
  // cy.get('#undefined-item-1').click()
  // cy.get('.col-lg-6:nth-child(2) > pacto-lista-insert-select-filter > .comp-wrapper > .ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
  // cy.get(':nth-child(1) > #undefined-item-5').click()
  cy.get('#btnAddImagem').click({force:true})
  cy.get('#imagem-1').click()
  cy.get('#selectImagem').click()
  cy.get('#btnSalvar').click({force:true})
  cy.contains('Atividade configurada com sucesso.')
})

// Preenche os dados de uma perimetria completa. 
Cypress.Commands.add('preencheDadosPerimetria',() => {
  cy.get('#abaPerimetria').click()
  cy.get('#inputOmbro').type('78') 
  cy.get('#inputTorax').type('78')  
  cy.get('#inputBracoRelaxEsq').type('52')  
  cy.get('#inputBracoRelaxDir').type('32')  
  cy.get('#inputBracoContraidoEsq').type('32') 
  cy.get('#inputBracoContraidoDir').type('34') 
  cy.get('#inputAntBracoEsq').type('34')
  cy.get('#inputAntBracoDir').type('34')
  cy.get('#inputCintura').type('80') 
  cy.get('#inputCircunferenciaAbdominal').type('78') 
  cy.get('#inputQuadril').type('86')
  cy.get('#inputCoxaProximalEsq').type('55')
  cy.get('#inputCoxaProximalDir').type('55')
  cy.get('#inputCoxaProximalDir').type('54')
  cy.get('#inputCoxaMediaEsq').type('54')
  cy.get('#inputCoxaMediaDir').type('52')
  cy.get('#inputCoxaDistalEsq').type('52') 
  cy.get('#inputPanturrilhaEsq').type('32')
  cy.get('#inputPanturrilhaDir').type('32')
})

//Preenche dados de protocolo pollock 7 dobras
Cypress.Commands.add('preencheDadosPollockSeteDobras',() => {
  cy.get('#idInputabdominal').type('10,2') 
  cy.get('#idInputpeitoral').type('10') 
  cy.get('#idInputcoxaMedial').type('10,9') 
  cy.get('#idInputsubescapular').type('10,9') 
  cy.get('#idInputsupraIliaca').type('10,9') 
  cy.get('#idInputtriceps').type('10,9')
  cy.get('#idInputaxilarMedia').type('10,9') 
})

//Preenche dados de protocolo pollock 3 dobras
Cypress.Commands.add('preencheDadosPollockTresDobras',() => {
  cy.get('#idInputcoxaMedial').type('10,2') 
  cy.get('#idInputsupraIliaca').type('10') 
  cy.get('#idInputtriceps').type('10,9') 
})

//Preenche dados da anamnese para ambiente INDEPENDENTE
Cypress.Commands.add('preencheDadosAnamneseIndependente',() => {
  cy.get('#42_sim').check()
  cy.get('#43_nao').check()
  cy.get('.resposta-wrapper:nth-child(1) > .row > .col-md-6 > .observacao > .form-group > .form-control').type('Somente Nargile')
  cy.get('#45_nao').check()
  cy.get('#46_nao').check()
  cy.get('#47_nao').check()
  cy.get('#48_nao').check()
  cy.get('#49_sim').check()
  cy.get('#50_nao').check()
  cy.get('#51_nao').check()
  cy.get('.content-column > .container-wrapper > .avaliacao > .avaliacao-abas > .aba:nth-child(2)').click()
})

//Preenche dados da anamnese para ambiente INTEGRADO
Cypress.Commands.add('preencheDadosAnamneseIntegrado',() => {
  cy.get('#181_nao').check()
  cy.get('.resposta-wrapper:nth-child(1) > .row > .col-md-6 > .observacao > .form-group > .form-control').type('Somente Nargile')
  cy.get('#182_nao').check()
  cy.get('#183_nao').check()
  cy.get('#184_nao').check()
  cy.get('#185_nao').check()
  cy.get('#186_nao').check()
  cy.get('#187_nao').check()
  cy.get('#188_nao').check()
  cy.get('#189_nao').check()
  cy.get('#190_nao').check()
})

//Preenche dados do Parq para ambiente INDEPENDENTE
Cypress.Commands.add('preencheDadosParQIndependente',() =>{
  cy.get('#35_nao').check()
  cy.get('#36_nao').check()
  cy.get('#37_nao').check()
  cy.get('#38_nao').check()
  cy.get('#39_nao').check()
  cy.get('#40_nao').check()
  cy.get('#41_nao').check()
})

//Preenche dados do Parq para ambiente INTEGRADO
Cypress.Commands.add('preencheDadosParQIntegrado',() =>{
  cy.get('#113_nao').check()
  cy.get('#114_nao').check()
  cy.get('#115_nao').check()
  cy.get('#116_nao').check()
  cy.get('#117_nao').check()
  cy.get('#118_nao').check()
  cy.get('#119_nao').check()
})


//Realiza logoff do sistema
Cypress.Commands.add('realizaLogoff', () => {
  cy.get('#open-user-drop-menu').click()
  cy.get('#deslogar-sistema').click()

})

//Verifica se os elementos (Adicionar, editar e Excluir) estão vigente na tela
Cypress.Commands.add('verificaEditarExcluirEstaoVisiveis', () => {
  cy.get('#element-0-remove').should('be.visible')
  cy.get('#element-0-edit').should('be.visible')
})

Cypress.Commands.add('cadastroPerilAcesso', (numeroPeril) => {
  if (numeroPeril == null){
    var numeroPeril = Math.random().toString(6).substring(2, 8);
  }
  cy.visit('/novotreino/pt/perfil-acesso')  
  cy.get('#adicionar-novo_perfil').click()
  cy.get('#input-nome-perfil').type('PERFIL UNICO ' + numeroPeril)
  cy.get('#salvar-perfil').click()
  cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').contains('Perfil criado com sucesso')
  cy.get('#inputGlobalSearch').type('perfil')
})
