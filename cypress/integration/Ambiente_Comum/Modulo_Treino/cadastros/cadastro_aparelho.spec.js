describe('Cadastro de aparelho do treino ', function() {

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true) 
        cy.visit('novotreino/pt/treino/cadastros/aparelhos')
    })

    it('Cadastro de aparelho', function(){ 
        cy.get('pacto-relatorio > .pacto-table-title-block > .actions > div > .btn').click()     
        cy.get('.row > .col-md-12 > pacto-input > .form-group > .form-control').type(Math.random().toString(36).substring(2, 15))  
        cy.get('.comp-wrapper > .ng-untouched > .cuppa-dropdown > .selected-list > .c-btn').click()
        cy.get('#undefined-item-17').click()
        cy.get('pacto-title-card > .card-wrapper > .content > .actions > .btn-primary').click()
        cy.contains('Aparelho configurado com sucesso.')  
    })

    it('Cadastro de aparelho com o mesmo nome', function(){    
        cy.get('pacto-relatorio > .pacto-table-title-block > .actions > div > .btn').click()     
        cy.get('.row > .col-md-12 > pacto-input > .form-group > .form-control').type('APARELHO DUPLICADO')  
        cy.get('.comp-wrapper > .ng-untouched > .cuppa-dropdown > .selected-list > .c-btn').click()
        cy.get('#undefined-item-17').click()
        cy.get('pacto-title-card > .card-wrapper > .content > .actions > .btn-primary').click()
        cy.contains('Já existe um cadastro com esse mesmo nome')  
        })
    
    it('Cadastro de aparelho sem o  nome', function(){     
        cy.get('pacto-relatorio > .pacto-table-title-block > .actions > div > .btn').click()      
        cy.get('.comp-wrapper > .ng-untouched > .cuppa-dropdown > .selected-list > .c-btn').click()
        cy.get('#undefined-item-17').click()
        cy.get('pacto-title-card > .card-wrapper > .content > .actions > .btn-primary').click()
        cy.contains('Campos obrigatórios não foram preenchidos.')  
        })
})    
