describe('Cadastro de atividade ', function() {
    var codAtividade = '';
    var atividadeInativa = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
        cy.visit('novotreino/pt/treino/cadastros/atividades')  
    })

    it('Cadastro de atividade neuromuscular treino', function(){
        codAtividade = Math.random().toString(4).substring(2, 4) 
        cy.get('#addAtividade').click()
        cy.get('#inptNomeAtividade').type('atividade ' + codAtividade)
        cy.get('#selectTipoAtividade').select('NEUROMUSCULAR')
        cy.preencheDadosAtividadeTreino() 

    })

    it('Cadastro de atividade cardiovascular treino', function(){   
        cy.get('#addAtividade').click()
        cy.get('#inptNomeAtividade').type('atividade ' + codAtividade)
        cy.get('#selectTipoAtividade').select('CARDIOVASCULAR')
        cy.preencheDadosAtividadeTreino() 
    
    })

    it('Cadastro de atividade sem o nome e tipo', function(){   
        cy.get('#addAtividade').click()
        cy.get('#btnSalvar').click()
        cy.contains('Campos obrigatórios não foram preenchidos.') 
    })

    it('Cadastro  uma atividade, e inativa a mesma', function(){
        atividadeInativa = Math.random().toString(5).substring(2, 9) 
        cy.get('#addAtividade').click()
        cy.get('#inptNomeAtividade').type('atividade ' + atividadeInativa)
        cy.get('#selectTipoAtividade').select('NEUROMUSCULAR')
        cy.preencheDadosAtividadeTreino()
        cy.get('#input-busca-rapida').type('atividade ' + atividadeInativa)
        cy.wait(2000)
        cy.get('#element-0-situacao').click()
        cy.get('#action-inativar').click()
        cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').contains('Atividade inativada com sucesso.')
        cy.get('#filtros-dropdown').click()
        cy.get('.grid-filter-wrapper > .filter:nth-child(1) > .filter-title > .aux-wrapper > span').click()
        cy.get('.filter > .filter-content > .options-wrapper > .option:nth-child(2) > span').click()
        cy.get('pacto-data-grid-filter > .grid-filter-wrapper > .search-button > .btn > span').click()  
        cy.get('#input-busca-rapida').clear().type('atividade ' + atividadeInativa)
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.contains('Editar Atividade')
    })
})    


   

