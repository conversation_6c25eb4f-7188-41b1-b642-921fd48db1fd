describe('Cadastro de grupo muscular ', function() {

    var nomeGrupoMuscular = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
        cy.visit('novotreino/pt/treino/cadastros/grupo-muscular')
    })

    it('Cadastro de grupo muscular', function(){
        nomeGrupoMuscular = Math.random().toString(4).substring(2, 4);
        cy.get('#adicionarGruposMusculares').click() 
        cy.get('#nome-grupo-muscular-input').type('grupo' + nomeGrupoMuscular)
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#undefined-item-3').should('be.visible')  
        cy.get('#undefined-item-3').click()
        cy.get('#btn-add-grupo-muscular').click()
        cy.contains('Grupo muscular configurado com sucesso.')
    })

    it('Cadastro de grupo muscular com o mesmo nome', function(){
        cy.get('#adicionarGruposMusculares').click() 
        cy.get('#nome-grupo-muscular-input').type('grupo' + nomeGrupoMuscular)
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#undefined-item-3').should('be.visible')   
        cy.get('#undefined-item-0').click()
        cy.get('#btn-add-grupo-muscular').click()
        cy.contains('Já existe um cadastro com esse mesmo nome')
    })

    it('Cadastro de grupo muscular sem campos obrigatorios e adição do nome sem seguida', function(){
        cy.get('#adicionarGruposMusculares').click()
        cy.get('#btn-add-grupo-muscular').click()
        cy.contains('Campos obrigatórios não preenchidos.')
        cy.get('#nome-grupo-muscular-input').type('grupo' + nomeGrupoMuscular)
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#undefined-item-3').should('be.visible')   
        cy.get('#undefined-item-3').click()
        cy.get('#btn-add-grupo-muscular').click()
        cy.contains('Grupo muscular configurado com sucesso.')
    })
})  

