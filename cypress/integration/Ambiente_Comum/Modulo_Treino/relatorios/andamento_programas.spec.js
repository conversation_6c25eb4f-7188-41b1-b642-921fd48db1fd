describe('Relatorio - Andamento de programas', function () {

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false)
        cy.visit('novotreino/pt/treino/gestao/andamento-programas')
    })
  
    it('<PERSON><PERSON>r Relatorio', function () {
        cy.contains('Gestão de Andamento dos Programas')
    })
  
    it('Verfica se esta carregando informações', function () {
        cy.get('#carregando-dados').should('not.be.visible')
      
    })
  
    it('Filtrar Professor', function () {
        cy.get('#filtros-dropdown').click()
        cy.get('pacto-data-grid-filter > .grid-filter-wrapper > .filter:nth-child(2) > .filter-title > .aux-wrapper').click()
        cy.get('.filter > .filter-content > .options-wrapper > .option:nth-child(1) > span').click()
        cy.get('pacto-data-grid-filter > .grid-filter-wrapper > .search-button > .btn > span').click()
        cy.get('#carregando-dados').should('not.be.visible')
    })
  
  })
  