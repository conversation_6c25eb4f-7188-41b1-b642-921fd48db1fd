describe('Relatorio - Car<PERSON><PERSON> de <PERSON>', function () {

  beforeEach(function () {
    cy.viewport(1440, 900)
    cy.login(false)
    cy.visit('novotreino/pt/treino/gestao/carteira-professores')
  })

  it('<PERSON><PERSON>r Relatorio', function () {
    cy.contains('Indicadores da carteira dos professores')
  })

  it('Listar Professores', function () {
    cy.contains('PACTO - MÉTODO DE GESTÃO ')
  })

  it('Listar Professores Inativos', function () {
    cy.get('#filtros-dropdown').click()
    cy.get('#incluirProfessorInativo').click()
    cy.get('.search-button').click()
    cy.get('.column-cell').contains('Professor Inativo')
  })

  it('Filtrar Professor', function () {
    cy.get('#filtros-dropdown').click()
    cy.get('.grid-filter-wrapper > .filter:nth-child(2)').click()
    cy.get('.form-control').type('pacto -')
    cy.get('.option').click()
    cy.get('.search-button').click()
    cy.contains('PACTO - MÉTODO DE GESTÃO')
  })

  it('Filtrar Status Aluno', function () {
    cy.get('#filtros-dropdown').click()
    cy.get('.grid-filter-wrapper > .filter:nth-child(3)').click()
    cy.get('.form-control').type('ativo')
    cy.get('.option').click({ multiple: true })
    cy.get('.search-button').click()
    cy.contains('Professor Ativo')
  })

  it('Filtrar Situacao Programa', function () {
    cy.get('#filtros-dropdown').click()
    cy.get('.grid-filter-wrapper > .filter:nth-child(4)').click()
    cy.get('.form-control').type('Renovados')
    cy.get('.option').click()
    cy.get('.search-button').click()
    cy.contains('PACTO - MÉTODO DE GESTÃO')
  })

})
