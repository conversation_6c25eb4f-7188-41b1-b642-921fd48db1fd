// Refatorar codigo assim que terminar a montagem de treino. 

// describe('Cria e valida regras do programa a partir da tela de montagem ', function() {

//     beforeEach(function () {
//         cy.viewport(1440, 900)
//         cy.login(true)
//     })
//     // Criação de um novo programa de treino,  altera a quantidade de aulas previstas, dias da semana e exclui o programa
//     it('Cria um programa de treino', function(){
//         cy.get('#inputGlobalSearch').type('monta')
//         cy.get('#global-search-montagem-treino').click()
//         cy.get(':nth-child(1) > .card-usuario-wrapper > .body > .info').should('be.visible')
//         cy.get('.row > .col-md-12 > .catalogo-filtros > .filtro-tipo > .form-control').select('SEM_TREINO')
//         cy.wait(3000)
//         cy.get(':nth-child(1) > .card-usuario-wrapper > .body > .info').click()
//         cy.get('#montagem-treino-criar-novo').click()
//         cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Programa criado com sucesso.')
//         cy.get('.form-group > .ng-valid > .cuppa-dropdown > .selected-list > .c-btn').click()
//         cy.get('.cuppa-dropdown > .dropdown-list > .list-area > .list-filter > .c-input').type('pacto -')
//         cy.get('.lazyContainer > span > .pure-checkbox > .user-item-wrapper > .name').click()
//         cy.get('[label="Aulas Previstas"] > .form-group > .form-control').clear().type('30')
//         cy.get('[label="Dias por Semana"] > .form-group > .form-control').clear().type('5')
//         cy.get('.content > .row > .col-md-12 > .button-options > .btn-primary').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').contains('Programa editado com sucesso')
//         cy.wait(5000)
//         cy.get('.content > .row > .col-md-12 > .button-option-remove > .btn').click()
//         cy.get('.snotify > ng-snotify-toast:nth-child(1) > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').should('be.visible')
//         cy.get('.snotify > ng-snotify-toast:nth-child(1) > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Programa removido com sucesso.')

//     })

// })
