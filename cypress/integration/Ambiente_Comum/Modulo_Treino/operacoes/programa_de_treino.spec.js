// Refatorar o codigo assim que tiver a alteração na montagem de treino. 

// describe('Valida operações basicas do programa de treino ', function() {

//     beforeEach(function () {
//         cy.viewport(1440, 900)
//         cy.login(true)
//     })

//     it('Criação de um novo programa de treino', function(){
//         cy.get('#inputGlobalSearch').type('Zt Aluno Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').click()
//         cy.adicionaPrograma()
//         cy.contains('Adicionar atividade')
//         cy.get('.header-left-section').click()
//         cy.get('#inputGlobalSearch').type('Zt Aluno Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').click()
//         cy.get('#programa-0-remove').should('be.visible')
//         cy.removePrograma()

//     })
    
//     it('Renovação do programa de treino', function(){
//         cy.get('#inputGlobalSearch').type('Zt Aluno Dois Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').click()
//         cy.adicionaPrograma()
//         cy.get('.header-left-section').click()
//         cy.get('#inputGlobalSearch').type('Zt Aluno Dois Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').should('be.visible')
//         cy.get('#global-search-nome-aluno').click()
//         cy.get('#renovar').click()
//         cy.get('#montagem-treino-criar-novo').click()
//         cy.contains('Programa criado com sucesso')
//         cy.contains('Adicionar atividade')
//         cy.get('.header-left-section').click()
//         cy.get('#inputGlobalSearch').type('Zt Aluno Dois Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').should('be.visible')
//         cy.get('#global-search-nome-aluno').click()
//         cy.removePrograma()
//         cy.wait(1000)
//         cy.removePrograma()
//         cy.contains('Esse aluno não possui nenhum programa.')
//     })

//     it('Exclusão de um programa de treino', function(){
//         cy.get('#inputGlobalSearch').type('Zt Aluno tres Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').click()
//         cy.adicionaPrograma()
//         cy.get('.header-left-section').click()
//         cy.get('#inputGlobalSearch').type('Zt Aluno tres Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').should('be.visible')
//         cy.get('#global-search-nome-aluno').click()
//         cy.removePrograma()
//         cy.contains('Esse aluno não possui nenhum programa.')
//     })

//     it('Criação de um novo programa usando predefinido', function(){
//         cy.get('#inputGlobalSearch').type('Zt Aluno quatro Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').click()
//         cy.get('#criar-programa').click()
//         cy.get('pacto-cadastro-programa > div > .programas > .programa > .nome').click()
//         cy.contains('Adicionar atividade')
//         cy.wait(1000)
//         cy.get('pacto-plataforma-layout > pacto-plataform-layout-top > .header-container > .header-left-section > .pct-logo').click()
//         cy.get('#inputGlobalSearch').type('Zt Aluno quatro Sem Programa Para Teste Ee')
//         cy.get('#global-search-nome-aluno').click()
//         cy.get('#programa-0-remove').should('be.visible')
//         cy.removePrograma()
//     })
// })
