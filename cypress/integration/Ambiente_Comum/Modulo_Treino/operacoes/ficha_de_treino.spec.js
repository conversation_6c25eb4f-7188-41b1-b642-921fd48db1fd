// Refatorar o codigo assim que tiver alteração  na montagem de treino. 

// describe('Valida montagem de fichas ', function() {

//     beforeEach(function () {
//         cy.viewport(1440, 900)
//         cy.login(true)
//     })

//     it('Cria um programa de treino, monta duas fichas e exclui o programa', function(){
//         cy.get('#inputGlobalSearch').type('Zw Aluno Para Lançar Programa De Treino')
//         cy.get('#global-search-nome-aluno').click()
//         cy.LancarProgramaAluno()
//         cy.get('#inputGlobalSearch').type('Zw Aluno Para Lançar Programa De Treino')
//         cy.get('#global-search-nome-aluno').click()
//         cy.removePrograma()
//     })

//     it('Cria um programa, adição e exclusão de ficha', function(){
//         cy.get('#inputGlobalSearch').type('Zw Aluno Para Testar Adicao E Exclusao De Ficha')
//         cy.get('#global-search-nome-aluno').click()
//         cy.adicionaPrograma()
//         cy.get('#motagem-treino-nova-ficha').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain','Ficha criada com sucesso.')
//         cy.get('#motagem-treino-nova-ficha').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain','Ficha criada com sucesso.')
//         cy.get('#motagem-treino-nova-ficha').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain','Ficha criada com sucesso.')
//         cy.get('#motagem-treino-nova-ficha').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain','Ficha criada com sucesso.')
//         cy.get('#tab-ficha > .ficha-tabs > .ficha:nth-child(3)').click({ force: true }) 
//         cy.get('.card-wrapper > .content > .title-section > .remove-action > span').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain','Ficha removida com sucesso.')
//         cy.get('#inputGlobalSearch').type('Zw Aluno Para Testar Adicao E Exclusao De Ficha')
//         cy.get('#global-search-nome-aluno').click()
//         cy.get('#programa-0').click()
//         cy.contains('FICHA A ')
//         cy.contains('FICHA D')
//         cy.get('#inputGlobalSearch').type('Zw Aluno Para Testar Adicao E Exclusao De Ficha')
//         cy.get('#global-search-nome-aluno').click()
//         cy.removePrograma()
//     })

//     it('Cria um programa, teste adicao do nome da ficha', function(){
//         cy.get('#inputGlobalSearch').type('ZW ALUNO PARA TROCAR O NOME DA FICHA')
//         cy.get('#global-search-nome-aluno').click()
//         cy.adicionaPrograma()
//         cy.get('#motagem-treino-nova-ficha').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain','Ficha criada com sucesso.')
//         cy.get('.card-wrapper > .content > .title-section > .edit-action > span').click()
//         cy.get('.row > .col-md-6 > pacto-input > .form-group > .form-control').clear().type('FICHA E editada')
//         cy.get('.modal-content > pacto-editor-detalhes-ficha > div > .modal-footer > .btn-primary').click()
//         cy.get('ng-snotify > .snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner').should('contain', 'Ficha atualizada com sucesso.') 
//         cy.get('#inputGlobalSearch').type('ZW ALUNO PARA TROCAR O NOME DA FICHA')
//         cy.get('#global-search-nome-aluno').click()
//         cy.get('#programa-0').click()
//         cy.contains('FICHA A ')
//         cy.contains('FICHA E editada')
//         cy.get('#inputGlobalSearch').type('ZW ALUNO PARA TROCAR O NOME DA FICHA')
//         cy.get('#global-search-nome-aluno').click()
//         cy.removePrograma()
//     })

// })



