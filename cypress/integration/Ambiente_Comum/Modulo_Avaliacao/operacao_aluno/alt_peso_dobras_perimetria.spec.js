// E preciso reescreer os testes, a tela de montagem de avaliação fisica teve alterações. 

// describe('Avaliação fisica protocolos', function() {
    
//     beforeEach(function () {
//         cy.viewport(1440, 900)
//         cy.login(true)
//     })

//     it('Pollock 7 dobras + perimetria', function() {
//         cy.get('#inputGlobalSearch').type('LANÇAR')
//         cy.get('#global-search-lançar-avaliacao').click({force : true})
//         cy.get(':nth-child(1) > .header-name').should('be.visible')
//         cy.get('#buscarAlunos').type('ALUNO TESTE AUTOMATIZADO AVALIAÇÃO')
//         cy.wait(3000)
//         cy.contains('ALUNO TESTE AUTOMATIZADO AVALIAÇÃO').click({force : true})
//         cy.get(':nth-child(1) > .header-name').click()
//         cy.get('#criarAvaliacao').click()
//         cy.get('#abaAlturaPeso').click({force: true})
//         cy.get('#inputPeso').type('85,5')
//         cy.get('#inputAltura').type('1,81')
//         cy.get('#abaDobras').click({force: true})
//         cy.get('#divDobrasProtocolo').click()
//         cy.get('#selectProtocolo').select('POLLOCK_7_DOBRAS')
//         cy.preencheDadosPollockSeteDobras()
//         cy.get('#abaPerimetria').click()
//         cy.preencheDadosPerimetria()
//         cy.get('#btnSalvarAvaliacao').click()
//         cy.contains('Avaliação criada com sucesso.')
//         cy.get('#btnOpcoesAvaliacao').click({force : true})
//         cy.get('#btnRemoverAvaliacao').click({force : true})
//         cy.contains('Avaliação removida com sucesso.')
//     })
    
//     it('Pollock 3 dobras + perimetria', function() {
//         cy.get('#inputGlobalSearch').type('LANÇAR')
//         cy.get('#global-search-lançar-avaliacao').click({force : true})
//         cy.get(':nth-child(1) > .header-name').should('be.visible')
//         cy.get('#buscarAlunos').type('ALUNO DOIS TESTE AUTOMATIZADO AVALIAÇÃO')
//         cy.wait(4000)
//         cy.get(':nth-child(1) > .header-name').click()
//         cy.get('#criarAvaliacao').click()
//         cy.get('#abaAlturaPeso').click({force: true})
//         cy.get('#inputPeso').type('85,5')
//         cy.get('#inputAltura').type('1,81')
//         cy.get('#abaDobras').click({force: true})
//         cy.preencheDadosPollockTresDobras()
//         cy.get('#abaPerimetria').click()
//         cy.preencheDadosPerimetria()
//         cy.get('#btnSalvarAvaliacao').click()
//         cy.contains('Avaliação criada com sucesso.')
//         cy.get('#btnOpcoesAvaliacao').click({force : true})
//         cy.get('#btnRemoverAvaliacao').click({force : true})
//         cy.contains('Avaliação removida com sucesso.')
//         })
// })
