describe('Anamnese ', function() {
    
    var nomeAnamnse = '';
    var nomeAnamnseExcluir = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
        cy.visit('novotreino/pt/avaliacao/anamneses')
    })

    it('Cadastro de anamnese', function(){
        nomeAnamnse = Math.random().toString(4).substring(2, 8);
        cy.get('#btn-novo-anamnese').click()
        cy.get('#nome-anamnese-input').type('Anamnese ' + nomeAnamnse)
        cy.get('#btn-add-pergunta').click()
        cy.get('#descricao-pergunta-0').type('pergunta texto')
        cy.get('#icon-add-pergunta-0').click()
        cy.get('#descricao-pergunta-1').type('pergunta multipla escolha')
        cy.get('#tipo-pergunta-select-1').select('ESCOLHA_MULTIPLA')
        cy.get('#add-opcao-check-1').click()
        cy.get('#add-opcao-check-1').click()
        cy.get('#add-opcao-check-1').click()
        cy.get('#icon-add-pergunta-1').click()
        cy.get('#descricao-pergunta-2').type('pergunta simples escolha')
        cy.get('#tipo-pergunta-select-2').select('ESCOLHA_UNICA')
        cy.get('#add-opcao-check-2').click()
        cy.get('#add-opcao-check-2').click()
        cy.get('#add-opcao-check-2').click()
        cy.get('#icon-add-pergunta-2').click()
        cy.get('#descricao-pergunta-3').type('pergunta sim ou não')
        cy.get('#tipo-pergunta-select-3').select('SIM_NAO')
        cy.get('#btn-add-anamnese').click()
        cy.contains('A anamnese foi criada com sucesso.')  
    })

    it('Exclusão de anamnese sem vinculo', function(){
        nomeAnamnseExcluir = Math.random().toString(4).substring(2, 8);
        cy.get('#btn-novo-anamnese').click()
        cy.get('#nome-anamnese-input').type('Anamnese ' + nomeAnamnseExcluir)
        cy.get('#btn-add-pergunta').click()
        cy.get('#descricao-pergunta-0').type('pergunta texto')
        cy.get('#icon-add-pergunta-0').click()
        cy.get('#descricao-pergunta-1').type('pergunta multipla escolha')
        cy.get('#tipo-pergunta-select-1').select('ESCOLHA_MULTIPLA')
        cy.get('#add-opcao-check-1').click()
        cy.get('#add-opcao-check-1').click()
        cy.get('#add-opcao-check-1').click()
        cy.get('#icon-add-pergunta-1').click()
        cy.get('#descricao-pergunta-2').type('pergunta simples escolha')
        cy.get('#tipo-pergunta-select-2').select('ESCOLHA_UNICA')
        cy.get('#add-opcao-check-2').click()
        cy.get('#add-opcao-check-2').click()
        cy.get('#add-opcao-check-2').click()
        cy.get('#icon-add-pergunta-2').click()
        cy.get('#descricao-pergunta-3').type('pergunta sim ou não')
        cy.get('#tipo-pergunta-select-3').select('SIM_NAO')
        cy.get('#btn-add-anamnese').click()
        cy.contains('A anamnese foi criada com sucesso.')  
        cy.get('#input-busca-rapida').type('Anamnese ' + nomeAnamnseExcluir)
        cy.wait(1000)
        cy.get('#element-0-remove').click()
        cy.get('#action-remover').click()
        cy.contains('Anamnese removida com sucesso.')  
    })

    it('Criação de anamnese sem os campos obrigatorios', function(){
        cy.get('#btn-novo-anamnese').click()
        cy.get('#btn-add-anamnese')
        cy.contains('A anamnese deve possuir um nome.')
        cy.get('#nome-anamnese-input').type('Anamnese v ' + nomeAnamnse)
        cy.get('#btn-add-pergunta').click()
        cy.contains('Todas perguntas devem ter uma descrição.')
        cy.get('#descricao-pergunta-0').type('pergunta texto')
        cy.get('#btn-add-anamnese').click()
        cy.contains('A anamnese foi criada com sucesso.')

    })

})
