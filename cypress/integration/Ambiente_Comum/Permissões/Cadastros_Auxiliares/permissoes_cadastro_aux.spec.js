describe('Permissões cadastro auxiliares  ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false,'permi','12345')
    })

    it('Permissão de grupo muscular', function() {
        cy.visit('novotreino/pt/treino/cadastros/grupo-muscular')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-grupos_musculares').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/treino/cadastros/grupo-muscular')
        cy.get('#adicionarGruposMusculares').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-grupos_musculares').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Permissão de aparelhos', function() {
        cy.visit('novotreino/pt/treino/cadastros/aparelhos')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-aparelhos').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/treino/cadastros/aparelhos')
        cy.verificaEditarExcluirEstaoVisiveis()
        cy.get('#btn-novo-aparelho').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-aparelhos').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Permissão de atividades treino', function() {
        cy.visit('novotreino/pt/treino/cadastros/atividades')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-atividades').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/treino/cadastros/atividades')
        cy.get('#element-0-edit').should('be.visible')
        cy.get('#addAtividade').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-atividades').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Permissão de niveis', function() {
        cy.visit('novotreino/pt/cadastros/niveis')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-niveis').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/cadastros/niveis')
        cy.verificaEditarExcluirEstaoVisiveis()
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-niveis').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Permissão de objetivos', function() {
        cy.visit('novotreino/pt/avaliacao/cadastros/objetivos')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-objetivos').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/avaliacao/cadastros/objetivos')
        cy.get('#element-0-remove').should('be.visible')
        cy.get('#novo-objetivo').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-objetivos').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Permissão de tipo de agendamento', function() {
        cy.visit('novotreino/pt/cadastros/tipo-agendamento')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-tipo_evento').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/cadastros/tipo-agendamento')
        cy.verificaEditarExcluirEstaoVisiveis()
        cy.get('#btn-novo-tipo-agendamento').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-tipo_evento').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Permissão de anamnese', function() {
        cy.visit('novotreino/pt/avaliacao/anamneses')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-anamnese').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/avaliacao/anamneses')
        cy.get('#element-0-remove').should('be.visible')
        cy.get('#btn-novo-anamnese').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-cadastro-auxiliares').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-anamnese').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })
})