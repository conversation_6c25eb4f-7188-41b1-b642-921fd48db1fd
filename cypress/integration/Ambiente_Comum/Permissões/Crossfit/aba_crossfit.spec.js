describe('Permissão aba CrossFit ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true,'permi','12345')
    })

    it('Valida permissão de benchmark', function() {
        cy.visit('novotreino/pt/crossfit/cadastros/benchmarks')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-benchmark').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/crossfit/cadastros/benchmarks')
        cy.get('#btn-novo-benchmark').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-benchmark').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        })


    it('Valida permissão tipo de  benchmark', function() {
        cy.visit('novotreino/pt/crossfit/cadastros/tipos-benchmark')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-tipo_benchmark').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/crossfit/cadastros/tipos-benchmark')
        cy.get('#adicionarTipoBenchmark').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-tipo_benchmark').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Valida permissão WOD', function() {
        cy.visit('novotreino/pt/crossfit/cadastros/wods')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/crossfit/cadastros/wods')
        cy.get('#btn-add-wod').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Valida permissão tipo de WOD', function() {
        cy.visit('novotreino/pt/crossfit/cadastros/tipos-wod')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-cadastrar_tipo_wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/crossfit/cadastros/tipos-wod')
        cy.get('#btn-novo-tipo-wod').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-cadastrar_tipo_wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        })

    it('Valida permissão atividade crossfit', function() {
        cy.visit('novotreino/pt/crossfit/cadastros/atividades-crossfit')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-atividades_wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/crossfit/cadastros/atividades-crossfit')
        cy.get('#btn-novo-atividade-crossfit').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-atividades_wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Valida permissão aparelho crossfit', function() {
        cy.visit('novotreino/pt/crossfit/cadastros/aparelhos')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-aparelhos_wod').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/crossfit/cadastros/aparelhos')
        cy.get('#btn-novo-aparelho').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-crossfit').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-aparelhos_wod').click({force:true})
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })
})