describe('Benchmark CrossFit ', function() {
    var nomeBenchmark = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)    
        cy.visit('novotreino/pt/crossfit/cadastros/benchmarks')
    })

    it('Cadastro benchmark completo', function(){
        nomeBenchmark = Math.random().toString(4).substring(1, 4);   
        cy.get('#btn-novo-benchmark').click()
        cy.get('#nome-benchmark-input').type('goku' + nomeBenchmark)
        cy.get('#tipo-benchmark-select').select('2')
        cy.get('#tipo-exercicio-select').select('FOR_TIME')
        cy.get('#exercicios-benchmark-text-area').type('Escrever descrição dos exercicios')
        cy.get('#observacao-benchmark-text-area').type('Escrever observação dos exercicios')
        cy.get('#btn-add-benchmark').click({force:true})
        cy.contains('Benchmark criado com sucesso.')   
    })

    it('Cadastro benchmark com o mesmo nome', function(){   
        cy.get('#btn-novo-benchmark').click()
        cy.get('#nome-benchmark-input').type('goku' + nomeBenchmark)
        cy.get('#tipo-benchmark-select').select('2')
        cy.get('#tipo-exercicio-select').select('FOR_TIME')
        cy.get('#exercicios-benchmark-text-area').type('Escrever descrição dos exercicios')
        cy.get('#observacao-benchmark-text-area').type('Escrever observação dos exercicios')
        cy.get('#btn-add-benchmark').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
    })

    it('Edição do nome e tipo do benchmark', function(){   
        cy.get('#input-busca-rapida').type('goku' + nomeBenchmark)
        cy.wait(1000)
        cy.get('#element-0-edit').click()
        cy.get('#nome-benchmark-input').clear().type('goku editado' )
        cy.get('#tipo-benchmark-select').select('2')
        cy.get('#tipo-exercicio-select').select('FOR_WEIGHT')
        cy.get('#exercicios-benchmark-text-area').clear().type('Escrever descrição dos exercicios EDITADA')
        cy.get('#observacao-benchmark-text-area').clear().type('Escrever observação dos exercicios EDITADA')
        cy.get('#btn-add-benchmark').click({force:true})
        cy.contains('Benchmark editado com sucesso.')
    })

    it('Exclusão de benchmark', function(){  
        cy.get('#input-busca-rapida').type('goku editado')
        cy.wait(1000)
        cy.get('#element-0-remove').click()
        cy.get('#action-remover').click()
        cy.contains('Benchmark removido com sucesso.')
    })
})    
