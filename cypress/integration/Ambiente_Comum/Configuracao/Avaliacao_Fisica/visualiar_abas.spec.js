// E preciso reescreber os testes, a tela de lançamento de avaliação fisica não existe e o modelo de montagem teve alteração. ( <PERSON>)



// describe('Visualizar Abas', function () {

//   beforeEach(function () {
//     cy.viewport(1440, 900)
//     cy.login(true)
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//   })

//   it('Aba de Objetivos e Anamnese', function () {
//     cy.get('#check-cfg_objetivos_anamnese').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaAnamnese').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_objetivos_anamnese').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaAnamnese').should('be.visible')
//   })

//   it('Aba de Par-Q', function() {
//     cy.get('#check-cfg_parq').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaParQ').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_parq').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaParQ').should('be.visible')
//   })

//   it('Aba de Peso, Altura, Pressão Arterial', function () {
//     cy.get('#check-cfg_peso_altura_pa_fc').uncheck()
//     cy.get('#check-cfg_composicao_corporal').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaAlturaPeso').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_peso_altura_pa_fc').check()
//     cy.get('#check-cfg_composicao_corporal').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaAlturaPeso').should('be.visible')
//   })

//   it('Aba de Dobra', function () {
//     cy.get('#check-cfg_dobras_cutaneas').uncheck()
//     cy.get('#check-cfg_composicao_corporal').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaDobras').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_peso_altura_pa_fc').check()
//     cy.get('#check-cfg_dobras_cutaneas').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaDobras').should('be.visible')
//   })

//   it('Aba de Perimetria', function () {
//     cy.get('#check-cfg_perimetria').uncheck()
//     cy.get('#check-cfg_composicao_corporal').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaPerimetria').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_perimetria').check()
//     cy.get('#check-cfg_composicao_corporal').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaPerimetria').should('be.visible')
//   })

//   it('Aba de Composicao', function () {
//     // Deve gerar exceção
//     cy.get('#check-cfg_peso_altura_pa_fc').uncheck()
//     cy.get('#check-cfg_composicao_corporal').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Para utilizar a "Composição Corporal" marcar "Altura, Peso , Pressão Arterial e Frequência Cardíaca", "Dobras Cutâneas" e "Perimetria".')

//     cy.get('#check-cfg_dobras_cutaneas').uncheck()
//     cy.get('#check-cfg_composicao_corporal').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Para utilizar a "Composição Corporal" marcar "Altura, Peso , Pressão Arterial e Frequência Cardíaca", "Dobras Cutâneas" e "Perimetria".')

//     cy.get('#check-cfg_perimetria').uncheck()
//     cy.get('#check-cfg_composicao_corporal').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Para utilizar a "Composição Corporal" marcar "Altura, Peso , Pressão Arterial e Frequência Cardíaca", "Dobras Cutâneas" e "Perimetria".')
//     cy.wait(5000)

//     // Fluxo Permitido
//     cy.get('#check-cfg_composicao_corporal').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaComposicao').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_peso_altura_pa_fc').check()
//     cy.get('#check-cfg_dobras_cutaneas').check()
//     cy.get('#check-cfg_perimetria').check()
//     cy.get('#check-cfg_composicao_corporal').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(1000)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaComposicao').should('be.visible')
//   })

//   it('Aba de Flexibilidade', function () {
//     cy.get('#check-cfg_flexibilidade').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaFlexibilidade').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_flexibilidade').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaFlexibilidade').should('be.visible')
//   })

//   it('Aba de Postura', function () {
//     cy.get('#check-cfg_postura').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaPostura').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_postura').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaPostura').should('be.visible')
//   })

//   it('Aba de RML', function () {
//     cy.get('#check-cfg_rml').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaRml').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_rml').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaRml').should('be.visible')
//   })

//   it('Aba de Vo2Max', function () {
//     // aba visivel
//     cy.get('#check-cfg_vo2max').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaVo2Max').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_vo2max').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaVo2Max').should('be.visible')

//     // ventilometri, maximo de campo, maximo de bike e protocolo Queens Collage
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_ventilometria').uncheck()
//     cy.get('#check-cfg_testes_campo').uncheck()
//     cy.get('#check-cfg_teste_bike').uncheck()
//     cy.get('#check-cfg_teste_queens').uncheck({force: true})
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaVo2Max').click()
//     cy.get('.category-title').should('not.contain', 'VENTILOMETRIA VO2')
//     cy.get('.category-title').should('not.contain', 'PROTOCOLOS DE CAMPO')
//     cy.get('.category-title').should('not.contain', 'TESTE SUBMÁXIMO DE ASTRAND EM CICLOERGÔMETRO (BICICLETA)')
//     cy.get('.category-title').should('not.contain', 'TESTE SUBMÁXIMO DE QUEENS COLLEGE')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_ventilometria').check()
//     cy.get('#check-cfg_testes_campo').check()
//     cy.get('#check-cfg_teste_bike').check()
//     cy.get('#check-cfg_teste_queens').check({force: true})
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaVo2Max').click()
//     cy.get('.category-title').should('contain', 'VENTILOMETRIA VO2')
//     cy.get('.category-title').should('contain', 'PROTOCOLOS DE CAMPO')
//     cy.get('.category-title').should('contain', 'TESTE SUBMÁXIMO DE ASTRAND EM CICLOERGÔMETRO (BICICLETA)')
//     cy.get('.category-title').should('contain', 'TESTE SUBMÁXIMO DE QUEENS COLLEGE')
//   })

//   it('Aba de Somatotipia', function () {
//     cy.get('#check-cfg_somatotipia').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaSomatotipia').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_somatotipia').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaSomatotipia').should('be.visible')
//   })

//   it('Aba de Meta/Recom', function () {
//     cy.get('#check-cfg_recomendacoes').uncheck()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaMeta').should('not.be.visible')
//     cy.get('#open-user-drop-menu').click()
//     cy.get('#configuracao-sistema').click()
//     cy.contains('Avaliação Fisica').click()
//     cy.get('#check-cfg_recomendacoes').check()
//     cy.get('#btn-save-config-avaliacao').click()
//     cy.get('.snotifyToast__body').contains('Configurações salvas com sucesso.')
//     cy.get('#inputGlobalSearch').type('lançar avaliação')
//     cy.get('#global-search-lançar-avaliacao').click()
//     cy.get('#buscarAlunos').type('aluno validação abas avaliação física')
//     cy.wait(450)
//     cy.get('.aluno-item-body').click()
//     cy.get('#abaMeta').should('be.visible')
//   })
// })
