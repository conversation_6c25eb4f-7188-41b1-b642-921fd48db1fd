describe('Edição do aluno pelo perfil', function() {

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false)
    })
    it('Edição de nivel do aluno ', function() {
        cy.get('#inputGlobalSearch').type('Aluno Para Editar')
        cy.get('#nivel-select').select('9')
        cy.contains('Aluno atualizado com sucesso')
        cy.get('#nivel-select').select('3')
        cy.contains('Aluno atualizado com sucesso')
    })

    it('Edi<PERSON> do professor do aluno', function() {
        cy.get('#inputGlobalSearch').type('Aluno Para Editar')
        cy.get('#professor-select').click()
        cy.get('#professor-select-filter').type('sem foto')
        cy.get('#professor-select-0').click()
        cy.contains('Aluno atualizado com sucesso')
        cy.get('#professor-select').click()
        cy.get('#professor-select-filter').type('professor')
        cy.get('#professor-select-2').click()
        cy.contains('Aluno atualizado com sucesso')
    })
})    
