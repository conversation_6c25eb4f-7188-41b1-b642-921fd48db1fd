describe('Pesquisa de menu global', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
    })
    
    // Itens comuns entre os módulos 
    it('Lista de aluno', function() {
        cy.get('#inputGlobalSearch').type('Alunos')
        cy.get('#global-search-listagem-aluno').click()
        cy.contains('Alunos Cadastrados')
    })

    it('Lista de colaborador', function() {
        cy.get('#inputGlobalSearch').type('Colaborador')
        cy.contains('Gerencie os colaboradores cadastrados')
    })

    it('Lista de niveis', function() {
        cy.get('#inputGlobalSearch').type('nivel')
        cy.contains('Gerencie os niveis cadastrados.')
    })

    it('Pequisa tipo de agendamento', function() {
        cy.get('#inputGlobalSearch').type('agendamento')
        cy.get('#global-search-cadastro-tipo-agendamento').click()
        cy.contains('Gerencie os tipos de agendamentos')
    })

    it('Lista de disponibilidade', function() {
        cy.get('#inputGlobalSearch').type('disponibilidade')
        cy.contains('Gestão de Disponibilidade da Agenda')
    })

    it('Relatorio de Agendamentos', function() {
        cy.get('#inputGlobalSearch').type('agendamento')
        cy.get('#global-search-indicadores-agenda').click()
        cy.contains(' Relatório dos Serviços Agendados')
    })

    it('Lista de aulas', function() {
        cy.get('#inputGlobalSearch').type('aula')
        cy.get('#global-search-listagem-aulas').click()
        cy.contains('Gerencie as aulas cadastradas.')
    })

    it('Lista de Ambientes', function() {
        cy.get('#inputGlobalSearch').type('ambientes')
        cy.contains('Gerencie os ambientes cadastrados.')
    })

    it('Relatorio professores substituidos', function() {
        cy.get('#inputGlobalSearch').type('substituidos')
        cy.contains('Professores substituídos')
    })

    // Intens relacionados ao módulo TREINO
    it('Dashboard Treino', function() {
        cy.get('#inputGlobalSearch').type('Dashboard')
        cy.get('#global-search-dashboard-treino').click()
        cy.contains('Indicadores')
    })

    it('Montagem de Treino', function() {
        cy.get('#inputGlobalSearch').type('montagem')
        cy.contains('Treinos')
    })

    it('Relatorio de carteiras', function() {
        cy.get('#inputGlobalSearch').type('carteira')
        cy.contains('Indicadores da carteira dos professores')
    })

    it('Relatorio Atividade dos professores', function() {
        cy.get('#inputGlobalSearch').type('atividade')
        cy.get('#global-search-atividade-professores').click()
        cy.contains('Indicadores da carteira dos professores')
    })

    it('Relatorio andamento dos programas', function() {
        cy.get('#inputGlobalSearch').type('programa')
        cy.get('#global-search-andamento-programas').click()
        cy.contains('Gestão de Andamento dos Programas')
    })

    it('Lista de Aparelhos Treino', function() {
        cy.get('#inputGlobalSearch').type('aparelho')
        cy.get('#global-search-listagem-aparelhos-treino').click()
        cy.contains(' Gerencie os aparelhos da sua academia.')
    })

    it('Lista de Atividades Treino', function() {
        cy.get('#inputGlobalSearch').type('atividade')
        cy.get('#global-search-listagem-atividade-treino').click()
        cy.contains('Gerencie as atividades da sua academia.')
    })
    
    it('Lista de grupo muscular', function() {
        cy.get('#inputGlobalSearch').type('grupo muscular')
        cy.contains('Gerencie os grupos musculares.')
    })

    // Intens relacionados ao módulo CROSSFIT
    it('Dashboard CrossFit', function() {
        cy.get('#inputGlobalSearch').type('DASHBOARD')
        cy.get('#global-search-dashboard-crossfit').click()
        cy.contains('Resultados Lançados')
    })

    it('Cadastro de WOD', function() {
        cy.get('#inputGlobalSearch').type('WOD')
        cy.get('#global-search-cadastro-wod').click()
        cy.contains('Gerencie os wods cadastrados')
    })

    it('Monitor', function() {
        cy.get('#inputGlobalSearch').type('monitor')
        cy.contains('O que você deseja visualizar?')
    })

    it('Aparelhos de CrossFit', function() {
        cy.get('#inputGlobalSearch').type('aparelho')
        cy.get('#global-search-listagem-aparelhos-crossfit').click()
        cy.contains('Gerencie os aparelhos da sua academia.')
    })

    it('Cadastro de atividade CrossFit', function() {
        cy.get('#inputGlobalSearch').type('atividade')
        cy.get('#global-search-listagem-atividades-crossfit').click()
        cy.contains('Gerencie as atividades cadastradas')
    })

    it('Cadastro de Benchmark', function() {
        cy.get('#inputGlobalSearch').type('benchmark')
        cy.get('#global-search-listagem-benchmarks').click()
        cy.contains('Gerencie os benchmarks cadastrados.')
    })

    it('Tipos de benchmark', function() {
        cy.get('#inputGlobalSearch').type('tipo')
        cy.get('#global-search-cadastro-tipo-benchmarks').click()
        cy.contains('Gerencie os tipos Benchmark cadastrados.')
    })

    it('Tipos de WOD', function() {
        cy.get('#inputGlobalSearch').type('tipo')
        cy.get('#global-search-listagem-tipo-wod').click()
        cy.contains('Gerencie os tipos de Wods cadastrados')
    })
    
    // Itens relacionados ao módulo AVALIAÇÃO FÍSICA
    it('Dashboard Avaliação física', function() {
        cy.get('#inputGlobalSearch').type('dashboard')
        cy.get('#global-search-dashboard-avaliacao').click()
        cy.contains('Avaliação realizadas no periodo')
    })

    it('Cadastro de Anmneses', function() {
        cy.get('#inputGlobalSearch').type('anamnese')
        cy.contains('Questionários customizáveis.')
    })

    it('Cadsatro de objetivos', function() {
        cy.get('#inputGlobalSearch').type('objetivo')
        cy.contains('Gerencie os objetivos cadastrados')
    })
})
