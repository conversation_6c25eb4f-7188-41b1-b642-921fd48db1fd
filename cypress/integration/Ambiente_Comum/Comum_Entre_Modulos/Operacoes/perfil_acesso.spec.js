describe('Crud perfil de acesso', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
    })
    

    it('Cadastro do perfil de acesso', function() {    
        let numeroPeril = Math.random().toString(6).substring(2, 8);
        cy.cadastroPerilAcesso(numeroPeril)
    })
    
    
    it('Cadastro e exclusão do perfil de acesso', function() {
        let numeroPeril = Math.random().toString(6).substring(2, 8)
        cy.cadastroPerilAcesso(numeroPeril)
        cy.get('#retornar-listagem-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('PERFIL UNICO ' + numeroPeril)
        cy.wait(2000)    
        cy.get('#element-0-remove').click()
        cy.get('#action-remover').click()
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil removido com sucesso')

    })
    
    it('Cadastra um perfil e confirma se ele esta disponivel para vincular a um usuário. ', function() {  
        let numeroPeril = Math.random().toString(6).substring(2, 8)
        cy.cadastroPerilAcesso(numeroPeril)
        cy.get('#open-user-drop-menu').click()
        cy.get('#editar-usuario').click()
        cy.get('#element-0').should('be.visible')
        cy.get('#element-0').click()
        cy.get('pacto-select > .form-group > .form-control').should('PERFIL UNICO ' + numeroPeril) //Falta criar ID para o select, teste esta quebrando
    })
    
    it('Tenta excluir um perfil de acesso vinculado a um usuário. ', function() {
        cy.visit('/novotreino/pt/perfil-acesso') 
        cy.get('#input-busca-rapida').type('coordenador')
        cy.get('#element-0').should('be.visible')      
        cy.get('#element-0-remove').click()
        cy.get('#action-remover').click()
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Não e possível excluir perfis vinculados a usuário')
    })

    it('Cria um perfil e edita o nome', function() {
        let numeroPeril = Math.random().toString(6).substring(2, 8)
        let perfilEditado = Math.random().toString(6).substring(2, 8)
        cy.cadastroPerilAcesso(numeroPeril)
        cy.get('#retornar-listagem-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('PERFIL UNICO ' + numeroPeril)
        cy.wait(1000)
        cy.get('#element-0-edit').click({force : true})
        cy.wait(1000)
        cy.get('#input-nome-perfil').clear().type('PERFIL ' + perfilEditado)
        cy.get('#btn-save-input-nome-perfil').click()
        cy.get('ng-snotify > .snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner').contains('Perfil editado com sucesso.')
        cy.get('#retornar-listagem-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('PERFIL ' + perfilEditado)
        cy.wait(2000)
        cy.get('#element-0-edit').click({force : true})
        cy.contains('PERFIL ' + perfilEditado)
    })

})
