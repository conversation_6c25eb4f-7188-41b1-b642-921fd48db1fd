describe('Cadastro de nivel ', function() {

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true) 
    })

    it('Cadastro de nivel', function(){ 
        cy.visit('novotreino/pt/cadastros/niveis')
        cy.get('#adicionarNivel').click()
        cy.get('#input-nome-nivel').type('nivel ' + Math.floor(Math.random() * 100 + 1))
        cy.get('#input-ordem-nivel').type(Math.floor(Math.random() * 100 + 1))
        cy.get('#gravarCadastroNivel').click()
        cy.contains('Nível criado com sucesso.')  
    })

    it('Cadastro de nivel sem nome e logo sem seguida completa as informações', function(){     
        cy.visit('novotreino/pt/cadastros/niveis')
        cy.get('#adicionarNivel').click()
        cy.get('#gravarCadastroNivel').click()
        cy.contains('Campos obrigatorio não preenchido!')
        cy.get('#input-nome-nivel').type('nivel ' + Math.floor(Math.random() * 100 + 1))
        cy.get('#input-ordem-nivel').type(Math.floor(Math.random() * 100 + 1))
        cy.get('#gravarCadastroNivel').click()
        cy.contains('Nível criado com sucesso.')    
    })
})
