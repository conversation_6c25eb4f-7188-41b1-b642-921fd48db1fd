describe('Objetivo ', function() {

    var nomeObjetivo = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
    })

    it('Cadastro de objetivo', function(){
        nomeObjetivo = Math.random().toString(36).substring(2, 15);   
        cy.visit('novotreino/pt/avaliacao/cadastros/objetivos')
        cy.get('#novo-objetivo').click()
        cy.get('#objetivo-input-nome').type(nomeObjetivo)
        cy.get('#objetivo-button-salvar').click()
        cy.contains('Objetivo criado com sucesso.')  
    })

    it('Cadastro de objetivo sem o nome', function(){    
        cy.visit('novotreino/pt/avaliacao/cadastros/objetivos')
        cy.get('#novo-objetivo').click()
        cy.get('#objetivo-button-salvar').click()
        cy.contains('Campo obrigatório não preenchido.')  
    })

    it('Cadastro de objetivo com o mesmo nome', function(){    
        cy.visit('novotreino/pt/avaliacao/cadastros/objetivos') 
        cy.get('#novo-objetivo').click()
        cy.get('#objetivo-input-nome').type(nomeObjetivo)
        cy.get('#objetivo-button-salvar').click()
        cy.contains('Já existe um cadastro com esse nome !')  
    })
}) 



