describe('Cadastro de aula  ', function() {
    var nomeAuala = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true) 
        cy.visit('novotreino/pt/treino/cadastros/aulas')
    })

    it('Cadastro de aula com todos os campos', function(){
        nomeAuala = Math.random().toString(6).substring(2, 8);      
        cy.get('#btn-nova-aula').click()
        cy.get('#nome-aula-input').type('AULA ' + nomeAuala)
        cy.get('#alta-frequencia-radio').check('ALTA_FREQUENCIA')
        cy.get('#modalidade-select').select('12')
        cy.get('#professor-select').select('PACTO - MÉTODO DE GESTÃO')
        cy.get('#ambiente-select').select('1')
        cy.get('#tolerancia-aula-input').type('20')
        cy.get('#capacidade-aula-input').type('50')
        cy.get('#segunda-check').check('segunda')
        cy.get('#terca-check').check('terca')
        cy.get('#quarta-check').check('quarta')
        cy.get('#sabado-check').check('sabado')
        cy.get('#horario-aula-input').type('08:30 - 09:00')
        cy.get('#btn-add-horario').click()
        cy.get('#horario-aula-input').type('12:00 - 12:30')
        cy.get('#btn-add-horario').click()
        cy.get('#data-inicio-input').type('01/01/2019')
        cy.get('#data-fim-input').type('12/12/2019')
        cy.get('#btn-add-aula').click({force:true}) 
        cy.contains('Aula criada com sucesso.')
        cy.get('#input-busca-rapida').type('AULA ' + nomeAuala)
        cy.wait(1000)
        cy.get('#element-0-edit').click()
    
    })

    it('Exclusão do cadastro de aula sem nenhum vinculo', function(){    
        cy.get('#input-busca-rapida').type('AULA ' + nomeAuala)
        cy.wait(1000)
        cy.get('#element-0-remove').click()
        cy.get('#action-remover').click()
        cy.contains('Aula removida com sucesso.')
    
    })

    it('Cadastro de aula sem os campos obrigatorios', function(){
        nomeAuala = Math.random().toString(6).substring(2, 8);    
        cy.get('#btn-nova-aula').click()
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#nome-aula-input').type('AULA ' + nomeAuala)
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#alta-frequencia-radio').check('ALTA_FREQUENCIA')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#modalidade-select').select('12')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#professor-select').select('PACTO - MÉTODO DE GESTÃO')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#ambiente-select').select('1')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#tolerancia-aula-input').type('20')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#capacidade-aula-input').type('50')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#segunda-check').check('segunda')
        cy.get('#terca-check').check('terca')
        cy.get('#quarta-check').check('quarta')
        cy.get('#sabado-check').check('sabado')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Campos obrigatórios não preenchido.')
        cy.get('#data-inicio-input').type('01/01/2019')
        cy.get('#data-fim-input').type('12/12/2019')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Inserir pelo menos um horário na lista')
        cy.get('#horario-aula-input').type('08:30 - 09:00')
        cy.get('#btn-add-horario').click()
        cy.get('#horario-aula-input').type('12:00 - 12:30')
        cy.get('#btn-add-horario').click()
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Aula criada com sucesso.')
        cy.get('#input-busca-rapida').type('AULA ' + nomeAuala)
        cy.wait(1000)
        cy.get('#element-0-remove').click()
        cy.get('#action-remover').click()
        cy.contains('Aula removida com sucesso.')
    }) 

    it('Valida se o horario inicial não e superior ao final', function(){
        nomeAuala = Math.random().toString(6).substring(2, 8);      
        cy.get('#btn-nova-aula').click()
        cy.get('#nome-aula-input').type('AULA ' + nomeAuala)
        cy.get('#alta-frequencia-radio').check('ALTA_FREQUENCIA')
        cy.get('#modalidade-select').select('12')
        cy.get('#professor-select').select('PACTO - MÉTODO DE GESTÃO')
        cy.get('#ambiente-select').select('1')
        cy.get('#tolerancia-aula-input').type('20')
        cy.get('#capacidade-aula-input').type('50')
        cy.get('#segunda-check').check('segunda')
        cy.get('#horario-aula-input').type('08:30 - 07:30')
        cy.get('#btn-add-horario').click()
        cy.contains('Horário cadastrado invalido, tente novamente')
        cy.get('#horario-aula-input').type('08:30 - 09:30')
        cy.get('#btn-add-horario').click()
        cy.get('#data-inicio-input').type('01/01/2019')
        cy.get('#data-fim-input').type('12/12/2019')
        cy.get('#btn-add-aula').click({force:true})
        cy.contains('Aula criada com sucesso.')
        cy.get('#input-busca-rapida').type('AULA ' + nomeAuala)
        cy.wait(1000)
        cy.get('#element-0-edit').click()
    
    })

    it('Valida se a data final não e inferior ao final ', function(){
            nomeAuala = Math.random().toString(6).substring(2, 8);      
            cy.get('#btn-nova-aula').click()
            cy.get('#nome-aula-input').type('AULA ' + nomeAuala)
            cy.get('#alta-frequencia-radio').check('ALTA_FREQUENCIA')
            cy.get('#modalidade-select').select('12')
            cy.get('#professor-select').select('PACTO - MÉTODO DE GESTÃO')
            cy.get('#ambiente-select').select('1')
            cy.get('#tolerancia-aula-input').type('20')
            cy.get('#capacidade-aula-input').type('50')
            cy.get('#segunda-check').check('segunda')
            cy.get('#horario-aula-input').type('08:30 - 09:30')
            cy.get('#btn-add-horario').click()
            cy.get('#data-inicio-input').type('01/01/2019')
            cy.get('#data-fim-input').type('12/12/2018')
            cy.get('#btn-add-aula').click({force:true})
            cy.contains('Data inicial não pode ser superior a data final')
            cy.get('#data-fim-input').clear().type('12/12/2020')
            cy.get('#btn-add-aula').click({force:true})
            cy.contains('Aula criada com sucesso.')
            cy.get('#input-busca-rapida').type('AULA ' + nomeAuala)
            cy.wait(1000)
            cy.get('#element-0-edit').click()
    })    
})

