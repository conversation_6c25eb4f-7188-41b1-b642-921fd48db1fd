// Esse spec foi criado separado para colocar os casos de teste que so devem ocorrer no ambiente integrado.
describe('Pesquisa do menu global', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
    })

    it('Relatorio ranking dos professores', function() {
        cy.get('#inputGlobalSearch').type('ranking')
        cy.get('#global-search-ranking-professores').click()
        cy.contains('Ranking de professores')
    })
})