
describe('Testando tela de login integrado', () => {
   
    it('Login com usuário e senha corretos',() => {
        cy.visit('/login-tronco/8b1f29fabafddbdf6cf2a28189b906a1')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('JSP)34X&*') 
        cy.get('#fmLay\\:btnEntrar').click()
        cy.get('#fmLay\\3A empresa').select('1')
        cy.get('#loginModuloTreinoNovo').should('be.visible')
        cy.get('#loginModuloTreinoNovo').click()
        cy.contains('treino')
    })

    it('Login com usuário e senha incorretos', () => {
        cy.visit('/login-tronco/8b1f29fabafddbdf6cf2a28189b906a1')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('JSP)SENHAERRADA&*') 
        cy.get('#fmLay\\:btnEntrar').click()
        cy.contains('Usuário ou senha inválidos!')
    })

    it('Login sem inserir a senha', () => {
        cy.visit('/login-tronco/8b1f29fabafddbdf6cf2a28189b906a1')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:btnEntrar').click()
        cy.contains('Preencha todos os campos para fazer o login!')
    })

    it('Validando logoff',() => {
        cy.visit('login-tronco/8b1f29fabafddbdf6cf2a28189b906a1')
        cy.get('#fmLay\\:usernameLoginZW').clear().type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('JSP)34X&*') 
        cy.get('#fmLay\\:btnEntrar').click()
        cy.get('#fmLay\\3A empresa').select('1')
        cy.get('#loginModuloTreinoNovo').should('be.visible')
        cy.get('#loginModuloTreinoNovo').click()
        cy.get('pacto-plataforma-user-menu > .pp-user-menu-wrapper > .dropdown-aux > .icon-wrapper > .pct').click()
        cy.get('pacto-plataforma-user-drop-menu > .drop-user-menu-wrapper > .menus > .menu:nth-child(5) > .menu-label').click()
        cy.contains('Esqueceu sua senha?')
    })
})
           
