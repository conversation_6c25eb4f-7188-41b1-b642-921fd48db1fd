describe('Funcinalidade aba geral ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true,'permi','12345')
    })

    it('Valida permissão tornar uma ficha predefinida', function() {
        cy.get('#inputGlobalSearch').type('modalidade')
        cy.get('#global-search-listagem-modalidades').click()
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.get('#inputGlobalSearch').type('perfil acesso')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-aula').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-modalidades').click()
        cy.get('#btn-salvar-funcionalidade').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.get('#inputGlobalSearch').type('modalidade')
        cy.get('#global-search-listagem-modalidades').click()
        cy.get('#element-0-edit').should('be.visible')
        cy.contains('Gerencie as modalidades cadastradas')
        cy.get('#inputGlobalSearch').type('perfil acesso')
        cy.get('#global-search-perfil-acesso').click()
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-aula').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-modalidades').click()
        cy.get('#btn-salvar-funcionalidade').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })
})