describe('Dashboard Treino ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(true)
    })

    it('Acessa indicadores de total de alunos (Ativos e Inativos)', function() {
        cy.get('#total_alunos_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos ativos', function() {
        cy.get('#total_alunos_ativos_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos inativos', function() {
        cy.get('#total_alunos_inativos_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de treinos vencidos', function() {
        cy.get('#porcentagem_treino_vencidos_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos sem treino', function() {
        cy.get('#porcentagem_alunos_semtreino_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos com treino em dia', function() {
        cy.get('#porcentagem_treino_em_dia_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos com treino', function() {
        cy.get('#porcentagem_alunos_comTreino_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos com treino a vencer', function() {
        cy.get('#porcentagem_treino_avencer_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de alunos com contratos a vencer', function() {
        cy.get('#porcentagem_contrato_avencer_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de contratos renovado renovados recentemente', function() {
        cy.get('#contratos_renovados_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Acessa indicadores de contratos renovado renovados recentemente', function() {
        cy.get('#contratos_renovados_treino_bi').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Nome do aluno')
        cy.get('#close-aba').click()
    })

    it('Resumo agenda - indicador de agendados', function() {
        cy.get('#total-agendados').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Agendados')
        cy.get('#close-aba').click()
    })

    it('Resumo agenda - indicador de executados', function() {
        cy.get('#total-executados').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Executados')
        cy.get('#close-aba').click()
})

    it('Resumo agenda - indicador de faltas', function() {
        cy.get('#total-faltas').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Faltas')
        cy.get('#close-aba').click()
    })

    it('Resumo agenda - indicador de cancelados', function() {
        cy.get('#total-cancelados').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Cancelados')
        cy.get('#close-aba').click()
    })

    it('Professores x Agendamentos - indicador de professores', function() {
        cy.get('#total-professores').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Professores')
        cy.get('#close-aba').click()
    })

    it('Professores x Agendamentos - indicador de Ocupação', function() {
        cy.get('#total-ocupacao').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Ocupação')
        cy.get('#close-aba').click()
    })

    it('Professores x Agendamentos - indicador de Treinos novos', function() {
        cy.get('#total-treinos-novos').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Treinos novos')
        cy.get('#close-aba').click()
    })

    it('Professores x Agendamentos - indicador de Treinos renovados', function() {
        cy.get('#total-treinos-renovados').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Treinos renovados')
        cy.get('#close-aba').click()
    })

    it('Professores x Agendamentos - indicador de Avalição fisica', function() {
        cy.get('#total-avalicao').click()
        cy.get('#carregando_dados').should('not.be.visible')
        cy.contains('Avaliações fisicas')
        cy.get('#close-aba').click()
    })

})

