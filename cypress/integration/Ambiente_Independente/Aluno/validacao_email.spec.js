describe('Validação campo e-mail do aluno  ', function() {

    var nomeAluno = '';
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false)
    })

    it('Valida a inserção de e-mail invalidos', function() {
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type('ALUNO VALIDACAO EMAIL')
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#pacto-select-professor-item-0').click()
        cy.get('#email-input').type('emailerrado')
        cy.get('#email-adicionar').click()
        cy.contains('O Formato do email informado é inválido.')
        cy.get('#email-input').clear().type('email@errado')
        cy.get('#email-adicionar').click()
        cy.contains('O Formato do email informado é inválido.')
        cy.get('#email-input').clear().type('emailerrado@')
        cy.get('#email-adicionar').click()
        cy.contains('O Formato do email informado é inválido.')
        cy.get('#email-input').clear().type('@pacto.')
        cy.get('#email-adicionar').click()
        cy.contains('O Formato do email informado é inválido.')
        cy.get('#email-input').clear().type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.contains('O Formato do email informado é inválido.')
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno cadastrado com sucesso')
    })

    it('Valida a inserção de e-mail validos', function() {
        nomeAluno = Math.random().toString(36).substring(2, 15);
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type(nomeAluno)
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#pacto-select-professor-item-0').click()
        cy.get('#email-input').type('<EMAIL>')
        cy.get('#email-adicionar').click()  
        cy.get('#email-input').clear().type('<EMAIL>')
        cy.get('#email-adicionar').click()   
        cy.get('#email-input').clear().type('<EMAIL>')
        cy.get('#email-adicionar').click()   
        cy.get('#email-input').clear().type('<EMAIL>')
        cy.get('#email-adicionar').click()  
        cy.get('#email-input').clear().type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno cadastrado com sucesso')
        cy.get('.right-aux > .column > .info-bit > .bit-value > .ver-mais').click()
        cy.contains('<EMAIL>')
        cy.contains('<EMAIL>')
        cy.contains('<EMAIL>')
        cy.contains('<EMAIL>')
        cy.contains('<EMAIL>')
    })
})

