describe('Cadastro de aluno ', function() {

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false)
    })

    it('Cadastrar um aluno completo, exceto usuário movel', function() {
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type('ALUNO TESTE AUTOMATIZADO')
        cy.get('#situacao-aluno-select').select('ATIVO')
        cy.get('#nivel-aluno-input').select('2')
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#pacto-select-professor-item-0').click()
        cy.get('#data-nascimento-input').type('24/12/1992')
        cy.get('#email-input').type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.get('#telefone-input').type('(62) 98574-5784')
        cy.get('#telefone-adicionar').click()
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno cadastrado com sucesso')
    })

    it('Cadastrar um aluno sem inserir o campo professor', function() {
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type('ALUNO TESTE AUTOMATIZADO')
        cy.get('#situacao-aluno-select').select('ATIVO')
        cy.get('#nivel-aluno-input').select('2')
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Campos obrigatórios não preenchidos.')
    })

    it('Cadastro de aluno com usuário movel', function(){
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type('ALUNO TESTE AUTOMATIZADO COM USUARIO MOVEL')
        cy.get('#situacao-aluno-select').select('ATIVO')
        cy.get('#nivel-aluno-input').select('2')
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#pacto-select-professor-item-0').click()
        cy.get('#data-nascimento-input').type('24/12/1992')
        cy.get('#email-input').type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.get('#telefone-input').type('(62) 98574-5784')
        cy.get('#telefone-adicionar').click()
        cy.get('#usarApp').check('')
        cy.get('#username-aluno').type('<EMAIL>' + Math.floor(Math.random() * 10000 + 1))
        cy.get('#password-aluno').type('12345')
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno cadastrado com sucesso')
    })
    
    it('Cadastro de aluno com usuário movel já existente', function(){
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type('ALUNO TESTE AUTOMATIZADO COM USUARIO MOVEL')
        cy.get('#situacao-aluno-select').select('ATIVO')
        cy.get('#nivel-aluno-input').select('2')
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#pacto-select-professor-item-0').click()
        cy.get('#data-nascimento-input').type('24/12/1992')
        cy.get('#email-input').type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.get('#telefone-input').type('(62) 98574-5784')
        cy.get('#telefone-adicionar').click()
        cy.get('#usarApp').check('')
        cy.get('#username-aluno').type('<EMAIL>')
        cy.get('#password-aluno').type('12345')
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Nome de usuário duplicado.')
    })
})
           
