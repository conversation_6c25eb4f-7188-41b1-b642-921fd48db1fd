describe('Edição de aluno crud lateral  ', function() {
    var nomeAluno = '';
    Cypress.Commands.add("CadastroDeAlunoParaEdicao", (nome, dados) => { 
    
        nomeAluno = Math.random().toString(10).substring(2, 8);
        cy.get('#atalho-adicionar-aluno').click()
        cy.get('#nome-aluno-input').type('ALUNO DE TESTE' + nomeAluno)
        cy.get('#situacao-aluno-select').select('ATIVO')
        cy.get('#nivel-aluno-input').select('2')
        cy.get('.ng-untouched > .cuppa-dropdown > .selected-list > .c-btn > span:nth-child(1)').click()
        cy.get('#pacto-select-professor-item-0').click()
        cy.get('#data-nascimento-input').type('24/12/1992')
        cy.get('#email-input').type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.get('#telefone-input').type('(62) 98574-5784')
        cy.get('#telefone-adicionar').click()
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno cadastrado com sucesso')
           
    })
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false)
    })

    it('Edição de dados pessoais nome, situação, data de nasc, sexo, email, telefone ', function() {
        cy.CadastroDeAlunoParaEdicao()
        // Ate esse momento adiciona um aluno e salva o nome dele para ser usado posteriormente
        cy.get('#editar').click()
        cy.get('#nome-aluno-input').clear().type('ALUNO EDITADO ' + nomeAluno)
        cy.get('#situacao-aluno-select').select('VISITANTE')
        cy.get('#data-nascimento-input').clear().type('23/10/1993')
        cy.get('#email-input').type('<EMAIL>')
        cy.get('#email-adicionar').click()
        cy.get('#telefone-input').type('(62) 98766-8766')
        cy.get('#telefone-adicionar').click()
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno editado com sucesso.')
        cy.contains('aluno editado ' + nomeAluno)
        cy.contains('VI')
        //cy.contains('23/10/1993')
        cy.get('.right-aux > .column > .info-bit > .bit-value > .ver-mais').click()
        cy.contains('<EMAIL>')
        cy.contains('(62) 98766-8766')
    })

    it('Edição do usuário movel do aluno, e reenvio de dados pelo e-mail', function() {
        cy.CadastroDeAlunoParaEdicao()
        cy.get('#inputGlobalSearch').type('ALUNO EDITADO ' + nomeAluno)
        cy.wait(1000)
        cy.get('#editar').click()
        cy.get('#usarApp').click()
        cy.get('#username-aluno').type('edicao' + nomeAluno + '@pacto.com.br')
        cy.get('#password-aluno').type('12345')
        cy.get('#salvar-cadastro-aluno').click()
        cy.contains('Aluno atualizado com sucesso')
        cy.get('#email').click()
        cy.get('.modal-content > pacto-confirm-modal > div > .modal-footer > .btn-danger').click()
        cy.contains('Email enviado com sucesso.')
    })
})
