describe('Permissão perfil de acesso ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false,'p.geral','12345',)
    })
    // Este este foi criado para validar a permissão "Pefil de Acesso", como  a permissão do usuário Pacto não deve ser alterada,
    //vamos usar um segundo usuário para testar a permissão. No teste o pacto vai alterar o perfil de acesso desse segundo usuário.
    // Nos outros testes de permissão e usado o usuário "permi", como ess testes não deve influenciar os outros vamos utilizar o "p.geral"
    it('Valida permissão de perfil de acesso', function() {
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#element-0-remove').should('be.visible')
        cy.get('#element-0-edit').should('be.visible')
        cy.get('#adicionar-novo_perfil').should('be.visible')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('PERMISSAO ABA GERAL')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-perfil_usuario').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.visit('novotreino/pt/treino/bi')
        cy.reload(true)
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.realizaLogoff()
        cy.get('#fmLay\\3AusernameLoginZW').type('PACTOBR')
        cy.get('#fmLay\\3ApwdLoginZW').type('JSP)34X&*')
        cy.get('#fmLay\\3A btnEntrar').click()
        cy.get('#loginModuloTreinoNovo').should('be.visible')
        cy.get('#loginModuloTreinoNovo').click()
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').click()
        cy.get('#input-busca-rapida').type('PERMISSAO ABA GERAL')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-perfil_usuario').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })    
})

describe ('Pemrmissão de usuários', function(){

    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false,'permi','12345')
    })

    it('Valida permissão de usuarrios', function(){
        cy.get('#open-user-drop-menu').click()
        cy.get('#editar-usuario').should('not.be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-usuarios').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.get('#open-user-drop-menu').click()
        cy.get('#editar-usuario').click()
        cy.contains('Gerencie os usuários cadastrados')
        cy.get('#element-0-edit').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-permissao').click()
        cy.get('#toggle-total-usuarios').click()
        cy.get('#btn-salvar-permissao').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Valida permissão de cadastrar colaboradores', function(){
        cy.visit('novotreino/pt/cadastros/colaboradores')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-colaboradores').click()
        cy.get('#btn-salvar-funcionalidade').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/cadastros/colaboradores')
        cy.get('#adcionarColaborador').should('be.visible')
        cy.get('#element-0-edit').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-geral').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-colaboradores').click()
        cy.get('#btn-salvar-funcionalidade').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')

    })
})
    