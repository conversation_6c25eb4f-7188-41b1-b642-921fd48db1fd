describe('Per<PERSON>s<PERSON><PERSON> aba aulas ', function() {
    
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false,'permi','12345')
    })

    it('Valida permissão cadastrar modalidade', function() {
        cy.visit('novotreino/pt/treino/cadastros/modalidades')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-aula').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-modalidades').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/treino/cadastros/modalidades')
        cy.verificaEditarExcluirEstaoVisiveis()
        cy.get('#nova-modalidade').should('be.visible')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-aula').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-modalidades').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })

    it('Valida permissão cadastrar ambiente', function() {
        cy.visit('novotreino/pt/treino/cadastros/ambientes')
        cy.get('.snotify > ng-snotify-toast > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Seu usuário não possui permissão, procure seu administrador')
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-aula').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-habilitado-ambientes').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
        cy.reload(true)
        cy.visit('novotreino/pt/treino/cadastros/ambientes')
        cy.get('#adicionarAmbiente').should('be.visible')
        cy.verificaEditarExcluirEstaoVisiveis()
        cy.visit('novotreino/pt/perfil-acesso')
        cy.get('#input-busca-rapida').type('TESTE PERMISSÃO NÃO MEXER')
        cy.wait(2000)
        cy.get('#element-0').click()
        cy.get('#tab-aula').click()
        cy.get('#open-accordion-funcionalidade').click()
        cy.get('#toggle-desabilitado-ambientes').click()
        cy.get('.title-content > accordion-header > .status-modify > .action-status > .action-status-modify:nth-child(1)').click()
        cy.get('.snotify > .ng-star-inserted > .snotifyToast > .snotifyToast__inner > .snotifyToast__body').contains('Perfil editado com sucesso.')
    })
})