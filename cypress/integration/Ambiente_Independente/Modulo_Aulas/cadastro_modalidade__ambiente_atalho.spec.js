describe('Cadastros pelo atalho no cadastro da aula ', function() {
    var nAula = Math.random().toString(6).substring(2, 8)
    var nModalidade = Math.random().toString(6).substr(2, 8)
    var nAmbiente = Math.random().toString(6).substr(2, 8)
    beforeEach(function () {
        cy.viewport(1440, 900)
        cy.login(false)
    })

    it.only('Cadastra aula, insere uma nova modalidade e ambiente', function(){
        
        cy.visit('novotreino/pt/treino/cadastros/aulas')
        cy.get('#btn-nova-aula').click()
        cy.get('#nome-aula-input').type('AULA ' + nAula)
        cy.get('#alta-frequencia-radio').check('ALTA_FREQUENCIA')
        //Neste momento cria uma modaldiade pelo atalho 
        cy.get('#btn-add-modalidade').click()
        cy.get('#input-nome-modalidade').type('Mod atalho ' + nModalidade)
        cy.get('#cor-id-10').click()
        cy.get('.modal-footer > #btn-salvar-modalidade').click()
        cy.contains('Modalidade criada com sucesso.')
        cy.get('#professor-select').select('PACTO - MÉTODO DE GESTÃO')
        //Neste momento cria um ambiente pelo atalho
        cy.get('#btn-add-ambiente').click()
        cy.get('#input-nome-ambiente').type('Ambiente ' + nAmbiente)
        cy.get('#input-capacidade-ambiente').type('100')
        cy.get('#btn-salvar-ambiente').click()
        cy.contains('Ambiente criado com sucesso.')
        cy.get('#tolerancia-aula-input').type('20')
        cy.get('#capacidade-aula-input').type('50')
        cy.get('#segunda-check').check('segunda')
        cy.get('#terca-check').check('terca')
        cy.get('#quarta-check').check('quarta')
        cy.get('#sabado-check').check('sabado')
        cy.get('#horario-aula-input').type('08:30 - 09:00')
        cy.get('#btn-add-horario').click()
        cy.get('#horario-aula-input').type('12:00 - 12:30')
        cy.get('#btn-add-horario').click()
        cy.get('#data-inicio-input').type('01/01/2019')
        cy.get('#data-fim-input').type('12/12/2019')
        cy.get('#btn-add-aula').click()
        cy.contains('Aula criada com sucesso.')
        cy.get('#input-busca-rapida').type('AULA ' + nAula)
        cy.wait(1000)
        cy.get('#element-0-edit').click()
        cy.contains('Mod atalho ' + nModalidade)
        cy.contains('Ambiente ' + nAmbiente)
    })

    it('Cadastra modalidade pelo atalho e verifica se ela vai aparecer no crud', function(){
        cy.visit('novotreino/pt/treino/cadastros/aulas')
        cy.get('#btn-nova-aula').click()
        cy.get('#btn-add-modalidade').click()
        cy.get('#input-nome-modalidade').type('Mod atalho ' + nModalidade)
        cy.get('#cor-id-10').click()
        cy.get('.modal-footer > #btn-salvar-modalidade').click()
        cy.contains('Modalidade criada com sucesso.')
        cy.visit('novotreino/pt/treino/cadastros/modalidades')
        cy.get('#input-busca-rapida').type('Mod atalho ' + nModalidade)
        cy.wait(1000)
        cy.get('#element-0-edit').click()
    })

    it('Cadastra ambiente pelo atalho e verifica se ele vai aparecer no crud', function(){
        cy.visit('novotreino/pt/treino/cadastros/aulas')
        cy.get('#btn-nova-aula').click()
        cy.get('#btn-add-ambiente').click()
        cy.get('#input-nome-ambiente').type('Ambiente ' + nAmbiente)
        cy.get('#input-capacidade-ambiente').type('100')
        cy.get('#btn-salvar-ambiente').click()
        cy.contains('Ambiente criado com sucesso.')
        cy.visit('novotreino/pt/treino/cadastros/ambientes')
        cy.get('#input-busca-rapida').type('Ambiente ' + nAmbiente)
        cy.wait(1000)
        cy.get('#element-0-edit').click()
    })
})
