
describe('Testando tela de login', () => {
   
    it('Login com usuário e senha corretos',() => {
        cy.visit('/login-tronco/c0f8462ce82bb5345cc260de9b12e899')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('JSP)34X&*') 
        cy.get('#fmLay\\:btnEntrar').click()
        cy.get('#loginModuloTreinoNovo').should('be.visible')
        cy.get('#loginModuloTreinoNovo').click()
        cy.contains('treino')
    })

    it('Login com usuário e senha incorretos', () => {
        cy.visit('/login-tronco/c0f8462ce82bb5345cc260de9b12e899')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('JSP)SENHAERRADA&*') 
        cy.get('#fmLay\\:btnEntrar').click()
        cy.contains('Usuário ou senha inválidos!')
    })

    it('Login sem inserir a senha', () => {
        cy.visit('/login-tronco/c0f8462ce82bb5345cc260de9b12e899')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:btnEntrar').click()
        cy.contains('Preencha todos os campos para fazer o login!')
    })

    it('Validando logoff',() => {
        cy.visit('login-tronco/c0f8462ce82bb5345cc260de9b12e899')
        cy.get('#fmLay\\:usernameLoginZW').clear().type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('JSP)34X&*') 
        cy.get('#fmLay\\:btnEntrar').click()
        cy.get('#loginModuloTreinoNovo').should('be.visible')
        cy.get('#loginModuloTreinoNovo').click()
        cy.get('pacto-plataforma-user-menu > .pp-user-menu-wrapper > .dropdown-aux > .icon-wrapper > .pct').click()
        cy.get('pacto-plataforma-user-drop-menu > .drop-user-menu-wrapper > .menus > .menu:nth-child(5) > .menu-label').click()
        cy.contains('Esqueceu sua senha?')
    
    })
})
           
