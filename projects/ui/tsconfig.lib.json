{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "../../out-tsc/lib", "target": "es2015", "module": "es2015", "moduleResolution": "node", "declaration": true, "sourceMap": true, "inlineSources": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"annotateForClosureCompiler": true, "skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableResourceInlining": true}, "exclude": ["src/test.ts", "**/*.spec.ts", "**/*.stories.ts"]}