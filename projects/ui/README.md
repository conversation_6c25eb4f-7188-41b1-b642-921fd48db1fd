# Instalação

Define a identidade visual básica dos produtos da Pacto.

### 1. Instalar peer depencencies
Instalar as peer depencencies descritas em *projects/ui/package.json*

### 2. Importar CSS
Adicione as seguintes linhas na propriedade 'styles' do arquivo angular.json:
```
"styles": [
    ...
    "dist/ui-kit/assets/ui-kit.scss",
    "dist/ui-kit/assets/scss/material-theme.scss",
    "node_modules/bootstrap/dist/css/bootstrap.min.css"
    ...
]
```
### 3. Instalar fonte
Adicione o seguinte código no arquivo index.html do projeto
```
<link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600&display=swap" rel="stylesheet">
```

### 4. Instalar Javascript
Adicione as seguintes linhas na propriedade 'scripts' do arquivo angular.json:
```
"scripts": [
    ...
    "node_modules/jquery/dist/jquery.min.js",
    "node_modules/moment/min/moment.min.js",
    "node_modules/apexcharts/dist/apexcharts.min.js"
    ...
]
```

### 5. Configurar rotas de assets
Adicone a seguinte configuração na propriedade 'assets' do arquivo angular.json:
```
{
    "glob": "**",
    "input": "dist/ui-kit/assets",
    "output": "pacto-ui"
}
```

### 6. Importar módulos
Importar os módulos 'NgbModule' e 'UiModule' no módulo principal da applicação:
```
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { UiModule } from 'ui-kit';

@NgModule({
imports: [
    ...
    NgbModule,
    UiModule
    ...
],
```
### 7. Variáveis e Mixins 
Para ter acesso as variáveis SCSS dentro de algum componente basta incluir a seguinte linha no inicio de arquivo de .scss do componente:
```
@import 'dist/ui-kit/assets/import.scss';
```

### 8. Instalar Angular Material / CDK
Instalar Angular Material/CDK

   
### Pronto! Agora basta usar os componentes do kit ui.