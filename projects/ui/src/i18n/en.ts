import { UiKitI18n } from "./i18n.model";

export const en: UiKitI18n = {
	menu: {
		logout: { hint: "Logout" },
		config: { hint: "Configurations" },
		trocarUnidade: "Change company",
		global: {
			modulos: "MODULES",
			disponiveis: "Available modules",
			funcionalidades: "Functionalities",
		},
		nav: {},
		modules: {},
	},
	relatorio: {
		buscaPlaceholder: "Quick search...",
		acoesColumn: "",
		total: "Total",
		adicionarLinha: "Add item",
		shareErrorConnection:
			"Unable to connect to sharing service. Please try again later.",
		emailEnviadoSucesso: "E-mail sended succesfully.",
		linkCopiado: "Link copied to clipboard.",
		tabela5KRegistros:
			"Your table has more than 5,000 records. Please, refine your search with more filters and try again.",
		exportar: {
			semPermissao: "Você não possui a permissão",
		},
	},
	compartilharBtn: {
		copiarLink: "Copy link",
		salvarArquivo: "Save archive",
		enviarWhatsapp: "Send by WhatsApp",
		numeroWhatsapp: "Enter phone number",
		enviarEmail: "Send by e-mail",
		emailEnvio: "Enter e-mail",
		imprimir: "Print out",
		open: "Open",
	},
	filter: {
		buscar: "Search",
		filtrarPorNome: "Filter by name...",
		limpar: "Clear",
		mesAtual: "Actual month",
		colunasVisiveis: "Visible columns",
	},
	perfil: {
		edit: "Edit Profile",
		sair: "Exit System",
		perfil: "Profile",
		canal: "Customer Channel",
	},
	step: {
		etapas: "steps",
	},
	actions: {
		editar: "Edit",
		salvar: "Save",
		excluir: "Delete",
	},
	form: {
		filter: "Filter...",
	},
	banner: {
		page: "Go to page",
	},
};
