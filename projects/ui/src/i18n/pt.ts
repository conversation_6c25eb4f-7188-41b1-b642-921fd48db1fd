import { UiKitI18n } from "./i18n.model";

export const pt: UiKitI18n = {
	menu: {
		logout: { hint: "Sair" },
		config: { hint: "Configurações" },
		trocarUnidade: "Trocar Unidade",
		global: {
			modulos: "<PERSON>ó<PERSON><PERSON>",
			explorar: "Explorar",
			disponiveis: "Módulos Disponíveis",
			funcionalidades: "Funcionalidades",
		},
		nav: {},
		modules: {},
	},
	relatorio: {
		buscaPlaceholder: "Busca rápida...",
		acoesColumn: "",
		total: "Total",
		adicionarLinha: "Adicionar linha",
		shareErrorConnection:
			"Não foi possível conectar ao serviço de compartilhamento. Tente novamente mais tarde.",
		tabela5KRegistros:
			"Sua tabela tem mais de 5.000 registros. Por favor, refine sua consulta com mais filtros e tente novamente.",
		emailEnviadoSucesso: "E-mail enviado com sucesso.",
		linkCopiado: "Link copiado para a área de transferência.",
		exportar: {
			semPermissao: "Você não possui a permissão",
		},
	},
	compartilharBtn: {
		copiarLink: "Copiar link",
		salvarArquivo: "Salvar arquivo",
		enviarWhatsapp: "Enviar por whatsapp",
		numeroWhatsapp: "Insira o número para envio",
		enviarEmail: "Enviar por e-mail",
		emailEnvio: "Insira o e-mail para envio",
		imprimir: "Imprimir",
		abrir: "Abrir",
	},
	filter: {
		buscar: "Buscar",
		filtrarPorNome: "Filtrar por nome...",
		limpar: "Limpar",
		mesAtual: "Mês Atual",
		colunasVisiveis: "Colunas visíveis",
	},
	perfil: {
		edit: "Editar Perfil",
		sair: "Sair do Sistema",
		perfil: "Perfil",
		canal: "Canal do Cliente",
	},
	step: {
		etapas: "etapas",
	},
	actions: {
		editar: "Editar",
		salvar: "Salvar",
		excluir: "Excluir",
	},
	form: {
		filter: "Filtro...",
	},
	banner: {
		page: "Ir para página",
	},
};
