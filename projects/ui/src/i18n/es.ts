import { UiKitI18n } from "./i18n.model";

export const es: UiKitI18n = {
	menu: {
		logout: { hint: "Salida" },
		config: { hint: "Configuración" },
		trocarUnidade: "Cambiar",
		global: {
			modulos: "MODULOS",
			explorar: "Explore",
			disponiveis: "Módulos Disponibles",
			funcionalidades: "Funcionalidad",
		},
		nav: {},
		modules: {},
	},
	relatorio: {
		buscaPlaceholder: "Búsqueda rápida...",
		acoesColumn: "",
		total: "Total",
		adicionarLinha: "Añadir línea",
		shareErrorConnection:
			"No se puede conectar al servicio para compartir. Vuelve a intentarlo más tarde.",
		tabela5KRegistros:
			"Su tabla tiene más de 5.000 registros. Defina mejor su consulta con más filtros y vuelva a intentarlo.",
		emailEnviadoSucesso: "Correo eletronico enviado con éxito.",
		linkCopiado: "Enlace copiado a la area de transferencia",
		exportar: {
			semPermissao: "Você não possui a permissão",
		},
	},
	compartilharBtn: {
		copiarLink: "Copiar enlace",
		salvarArquivo: "Guardar archivo",
		enviarWhatsapp: "Enviar por WhatsApp",
		numeroWhatsapp: "Ingrese el número de envío",
		enviarEmail: "Enviar por correo electrónico",
		emailEnvio: "Ingrese el e-mail para enviar",
		imprimir: "Imprimir",
		abrir: "Abrir",
	},
	filter: {
		buscar: "Búsqueda",
		filtrarPorNome: "Filtrar por nombre...",
		limpar: "Borrar",
		mesAtual: "Mes actual",
		colunasVisiveis: "Columnas visibles",
	},
	perfil: {
		edit: "Editar los perfil",
		sair: "Sistema de salida",
		perfil: "Perfil",
		canal: "Canal de Clientes",
	},
	step: {
		etapas: "etapas",
	},
	actions: {
		editar: "Editar",
		salvar: "Guardar",
		excluir: "Eliminar",
	},
	form: {
		filter: "Filtro...",
	},
};
