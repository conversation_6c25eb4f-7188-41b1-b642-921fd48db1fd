::ng-deep #autorizacao-acesso {
	width: 500px !important;
	padding: 0px;

	.head {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		flex-direction: row;
		align-items: center;

		p {
			color: var(--color-typography-default-title);
			padding: 0px;
			margin: 13px 16px;
		}

		button {
			margin-right: 8px;
		}

		ds3-diviser {
			width: 100%;
		}
	}

	.form {
		margin: 16px;

		form {
			display: flex;
			justify-content: space-between;
			// gap: 8px;

			ds3-form-field {
				width: calc(50% - 4px);

				ds3-field-label {
					color: var(--color-typography-default-title);
				}
			}
		}
	}

	.action {
		display: flex;
		justify-content: flex-end;
		flex-direction: row;
		gap: 8px;
		margin: 16px;
	}
}

.senha {
	display: flex;
}
