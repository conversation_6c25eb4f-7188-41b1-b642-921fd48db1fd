<section class="head">
	<p class="pct-title4">Autorizar acesso</p>
	<button (click)="cancelAction()" [id]="idBtnClose" ds3-icon-button>
		<i class="pct pct-x"></i>
	</button>
	<ds3-diviser></ds3-diviser>
</section>
<section class="form" *ngIf="form">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label class="pct-title4">Usuário</ds3-field-label>
			<input
				#nome
				[id]="idInputUser"
				ds3Input
				formControlName="usuario"
				type="text" />
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label class="pct-title4">Senha</ds3-field-label>
			<input
				#senha
				[id]="idInputPsw"
				class="senha"
				ds3Input
				formControlName="senha"
				type="password" />
		</ds3-form-field>
	</form>
</section>
<section class="action">
	<button (click)="cancelAction()" [id]="idBtnCancel" ds3-outlined-button>
		Cancelar
	</button>
	<button
		(click)="confirmAction()"
		[disabled]="!form.valid"
		[id]="idBtnConfirm"
		ds3-flat-button>
		Confirmar
	</button>
</section>
