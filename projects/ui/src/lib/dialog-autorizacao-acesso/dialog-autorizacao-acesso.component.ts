import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatDialog } from "@angular/material";
import { ToastrService } from "ngx-toastr";

@Component({
	selector: "dialog-autorizacao-acesso",
	templateUrl: "./dialog-autorizacao-acesso.component.html",
	styleUrls: ["./dialog-autorizacao-acesso.component.scss"],
})
export class DialogAutorizacaoAcessoComponent implements OnInit, AfterViewInit {
	@Input() idInputUser: string = "aut-input-user";
	@Input() idInputPsw: string = "aut-input-psw";
	@Input() idBtnConfirm: string = "aut-btn-confirm";
	@Input() idBtnCancel: string = "aut-btn-cancel";
	@Input() idBtnClose: string = "aut-btn-close";
	wait = false;
	permissao;
	manterAberta;
	@ViewChild("nome", { static: false }) nome: ElementRef;
	@ViewChild("senha", { static: false }) senha: ElementRef;
	@Output() confirm: EventEmitter<any> = new EventEmitter<any>();
	@Output() cancel: EventEmitter<any> = new EventEmitter<any>();
	form: FormGroup = new FormGroup({
		usuario: new FormControl("", {
			validators: Validators.required,
			updateOn: "change",
		}),
		senha: new FormControl("", {
			validators: Validators.required,
			updateOn: "change",
		}),
	});
	dialogAutorizacao;

	constructor(
		private cd: ChangeDetectorRef,
		private dialog: MatDialog,
		private toastrService: ToastrService
	) {}

	ngOnInit() {
		this.dialogAutorizacao = this.dialog.getDialogById("autorizacao-acesso");
	}

	confirmAction() {
		if (this.form.valid) {
			this.confirm.emit({
				modal: this.dialogAutorizacao,
				data: this.form.getRawValue(),
			});
			if (!this.manterAberta) {
				this.dialogAutorizacao.close();
			}
		} else {
			// this.toastrService.error('Campos inválidos');
		}
	}

	cancelAction() {
		this.cancel.emit();
		this.dialogAutorizacao.close();
	}

	ngAfterViewInit(): void {
		this.form.markAllAsTouched();
		if (this.form.controls["usuario"].valid) {
			this.form.controls["usuario"].updateValueAndValidity();
			this.form.controls["usuario"].markAsDirty();
			this.senha.nativeElement.focus();
		} else {
			this.nome.nativeElement.focus();
		}
	}

	enable(): void {
		this.wait = false;
		this.cd.detectChanges();
	}

	disable(): void {
		this.wait = true;
		this.cd.detectChanges();
	}

	@HostListener("window:keyup.Enter", ["$event"])
	onDialogClick(event: KeyboardEvent): void {
		this.confirmAction();
	}
}
