import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { BUTTON_TYPE } from "../forms/cat-button/cat-button.component";
import { CatFormInputComponent } from "../forms/cat-form-input/cat-form-input.component";

@Component({
	selector: "pacto-autorizacao-acesso",
	templateUrl: "./autorizacao-acesso.component.html",
	styleUrls: ["./autorizacao-acesso.component.scss"],
})
export class AutorizacaoAcessoComponent implements OnInit, AfterViewInit {
	@Input() idInputUser: string = "aut-input-user";
	@Input() idInputPsw: string = "aut-input-psw";
	@Input() idBtnConfirm: string = "aut-btn-confirm";
	@Input() idBtnCancel: string = "aut-btn-cancel";
	wait = false;
	permissao;
	@ViewChild("senha", { static: true }) senha: CatFormInputComponent;
	@Output() confirm: EventEmitter<any> = new EventEmitter<any>();
	@Output() cancel: EventEmitter<any> = new EventEmitter<any>();
	form: FormGroup = new FormGroup({
		usuario: new FormControl("", Validators.required),
		senha: new FormControl("", Validators.required),
	});
	buttonType = BUTTON_TYPE;

	constructor(private modal: NgbActiveModal, private cd: ChangeDetectorRef) {}

	ngOnInit() {}

	confirmAction() {
		this.confirm.emit({ modal: this.modal, data: this.form.getRawValue() });
	}

	cancelAction() {
		this.cancel.emit();
		this.modal.close();
	}

	ngAfterViewInit(): void {
		this.senha.focus();
	}

	enable(): void {
		this.wait = false;
		this.cd.detectChanges();
	}

	disable(): void {
		this.wait = true;
		this.cd.detectChanges();
	}
}
