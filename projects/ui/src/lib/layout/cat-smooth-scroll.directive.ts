import {
	Directive,
	Input,
	<PERSON><PERSON>nit,
	<PERSON><PERSON><PERSON>roy,
	<PERSON>ementRef,
	HostBinding,
} from "@angular/core";
import Scrollbar from "smooth-scrollbar";

@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "[pactoCatSmoothScroll]",
})
export class CatSmoothScrollDirective implements OnInit, OnDestroy {
	@Input() maxHeight = "400px";
	@Input() damping = 0.3;
	@Input() alwaysShowTracks = false;
	scroll: Scrollbar;

	constructor(private ref: ElementRef) {}

	@HostBinding("style.max-height")
	maxHeightBind = `400px`;

	ngOnInit() {
		this.maxHeightBind = this.maxHeight;
		this.setUpScroll();
	}

	ngOnDestroy() {
		if (this.scroll) {
			this.scroll.destroy();
		}
	}

	private setUpScroll() {
		this.scroll = Scrollbar.init(this.ref.nativeElement, {
			damping: this.damping,
			continuousScrolling: true,
			alwaysShowTracks: this.alwaysShowTracks,
		});
	}
}
