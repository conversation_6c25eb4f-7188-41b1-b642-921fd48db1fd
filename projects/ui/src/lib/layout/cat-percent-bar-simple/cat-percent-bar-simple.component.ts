import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { PactoColor } from "../../pacto-color.model";

@Component({
	selector: "pacto-cat-percent-bar-simple",
	templateUrl: "./cat-percent-bar-simple.component.html",
	styleUrls: ["./cat-percent-bar-simple.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatPercentBarSimpleComponent implements OnInit {
	@Input() percent;
	@Input() color: PactoColor = PactoColor.AZULIM_PRI;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		if (this.percentDefined) {
			const store = this.percent;
			this.percent = 0;
			setTimeout(() => {
				this.percent = store;
				this.cd.detectChanges();
			});
		} else {
			this.percent = 0;
		}
	}

	get percentDefined() {
		return !isNaN(this.percent);
	}
}
