@import "projects/ui/assets/import.scss";

:host {
	display: block;
}

.pct-xs-info-icon-wrapper {
	padding: 20px;
	display: flex;
	border-radius: 10px;
	height: 100%;
	width: 100%;
	background-color: $branco;
	box-shadow: 0px 2px 3px 1px $cinzaClaro02;
	-webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
	transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);

	&.action {
		cursor: pointer;

		&:hover {
			box-shadow: 2px 5px 8px 2px $cinzaClaro02;
		}
	}

	.icon-wrapper {
		display: flex;
		flex-direction: column;
		justify-content: center;
		color: $azulimPri;
		font-size: 28px;
		margin-right: 20px;
	}

	.info-wrapper {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.pct-label {
		@extend .type-caption;
		color: $cinza05;
	}

	.pct-value {
		@extend .type-h4-bold;
		color: $pretoPri;
	}
}
