import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { PactoIcon } from "../../pacto-icon.enum";

@Component({
	selector: "pacto-cat-xs-info-card-icon",
	templateUrl: "./cat-xs-info-card-icon.component.html",
	styleUrls: ["./cat-xs-info-card-icon.component.scss"],
})
export class CatXsInfoCardIconComponent implements OnInit {
	@Input() label;
	@Input() icon: PactoIcon;
	@Input() value = "-";
	@Input() action = false;
	@Input() id;

	constructor() {}

	ngOnInit() {}
}
