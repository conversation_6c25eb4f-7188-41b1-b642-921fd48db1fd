import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";

import { TextMaskModule } from "angular2-text-mask";
import { TranslateModule } from "@ngx-translate/core";

import { CatAccordionComponent } from "./cat-accordion/cat-accordion.component";
import { CatLayoutSimplesComponent } from "./cat-layout-simples/cat-layout-simples.component";
import { CatCardPlainComponent } from "./cat-card-plain/cat-card-plain.component";
import { CatGridCardComponent } from "./cat-grid-card/cat-grid-card.component";
import { CatPercentBarSimpleComponent } from "./cat-percent-bar-simple/cat-percent-bar-simple.component";
import { CatSmInfoBlockIconComponent } from "./cat-sm-info-block-icon/cat-sm-info-block-icon.component";
import { CatSmInfoBlockIconCursorAutoComponent } from "./cat-sm-info-block-icon-cursor-auto/cat-sm-info-block-icon-cursor-auto.component";
import { CatXsInfoCardIconComponent } from "./cat-xs-info-card-icon/cat-xs-info-card-icon.component";
import { CatSmInfoCardPercentComponent } from "./cat-sm-info-card-percent/cat-sm-info-card-percent.component";
import { CatSmoothScrollDirective } from "./cat-smooth-scroll.directive";

import { PactoFormsModule } from "../forms/pacto-forms.module";
import { CatPlatformMenuV2Component } from "./cat-platform-menu-v2/cat-platform-menu-v2.component";
import { CatMenuTaskbarComponent } from "./cat-platform-menu-v2/components/cat-menu-taskbar/cat-menu-taskbar.component";
import { CatMenuContextualComponent } from "./cat-platform-menu-v2/components/cat-menu-contextual/cat-menu-contextual.component";
import { CatTopbarComponent } from "./cat-platform-menu-v2/components/cat-topbar/cat-topbar.component";
import { CatOmniSearchComponent } from "./cat-platform-menu-v2/components/cat-omni-search/cat-omni-search.component";
import { CatGlobalMenuComponent } from "./cat-platform-menu-v2/components/cat-global-menu/cat-global-menu.component";
import { PlataformaMenuV2RouterComponent } from "./cat-plataforma-menu-v2-router/cat-plataforma-menu-v2-router.component";
import { CatLayoutV2Component } from "./cat-layout-v2/cat-layout-v2.component";
import { CatMigalhasComponent } from "./cat-migalhas/cat-migalhas.component";
import { CatPageTitleComponent } from "./cat-page-title/cat-page-title.component";
import { CatMenuUserComponent } from "./cat-platform-menu-v2/components/cat-menu-user/cat-menu-user.component";
import { CatOptionsMenuUserComponent } from "./cat-platform-menu-v2/components/cat-options-menu-user/cat-options-menu-user.component";
import { CatGridSquareCardComponent } from "./cat-grid-square-card/cat-grid-square-card.component";
import { CatGridMiniCardComponent } from "./cat-grid-mini-card/cat-grid-mini-card.component";
import { CatGridCardV2Component } from "./cat-grid-card-v2/cat-grid-card-v2.component";
import { CatOptionsAppsPactoComponent } from "./cat-platform-menu-v2/components/cat-options-apps-pacto/cat-options-apps-pacto.component";
import { CatModalAcessoAppsQrcodeComponent } from "./cat-platform-menu-v2/components/cat-modal-acesso-apps-qrcode/cat-modal-acesso-apps-qrcode.component";
import { CatAjudaComponent } from "./cat-platform-menu-v2/components/cat-ajuda/cat-ajuda.component";
import { CatVideoComponent } from "./cat-platform-menu-v2/components/cat-video/cat-video.component";
// import { UiCommomModule } from '../ui-commom/ui-commom.module';
import { MenuModuleItemDirective } from "../directives/menu-module-item.directive";
import { ModulesCarouselComponent } from "./cat-platform-menu-v2/components/modules-carousel/modules-carousel.component";
import { CatOptionsAlunosRecentesComponent } from "./cat-platform-menu-v2/components/cat-options-alunos-recentes/cat-options-alunos-recentes.component";
import { SafePipe } from "../safepipe.pipe";
import { UiCommomModule } from "../ui-commom/ui-commom.module";
import { A11yModule } from "@angular/cdk/a11y";

@NgModule({
	imports: [
		PactoFormsModule,
		CommonModule,
		FormsModule,
		TranslateModule.forChild({ extend: true }),
		ReactiveFormsModule,
		TextMaskModule,
		RouterModule.forChild([]),
		UiCommomModule,
		A11yModule,
	],
	declarations: [
		CatSmoothScrollDirective,
		CatSmInfoCardPercentComponent,
		CatXsInfoCardIconComponent,
		CatSmInfoBlockIconComponent,
		CatSmInfoBlockIconCursorAutoComponent,
		CatPercentBarSimpleComponent,
		CatGridCardComponent,
		CatCardPlainComponent,
		CatAccordionComponent,
		CatLayoutSimplesComponent,
		CatPlatformMenuV2Component,
		PlataformaMenuV2RouterComponent,
		CatMenuTaskbarComponent,
		CatMenuContextualComponent,
		CatTopbarComponent,
		CatOmniSearchComponent,
		CatGlobalMenuComponent,
		CatLayoutV2Component,
		CatMigalhasComponent,
		CatPageTitleComponent,
		CatMenuUserComponent,
		CatOptionsMenuUserComponent,
		CatGridSquareCardComponent,
		CatGridMiniCardComponent,
		CatGridCardV2Component,
		CatOptionsAppsPactoComponent,
		CatModalAcessoAppsQrcodeComponent,
		CatAjudaComponent,
		CatVideoComponent,
		MenuModuleItemDirective,
		ModulesCarouselComponent,
		CatOptionsAlunosRecentesComponent,
	],
	exports: [
		CatSmoothScrollDirective,
		CatSmInfoCardPercentComponent,
		CatXsInfoCardIconComponent,
		CatSmInfoBlockIconComponent,
		CatSmInfoBlockIconCursorAutoComponent,
		CatPercentBarSimpleComponent,
		CatGridCardComponent,
		CatCardPlainComponent,
		CatAccordionComponent,
		CatLayoutSimplesComponent,
		CatPlatformMenuV2Component,
		PlataformaMenuV2RouterComponent,
		CatLayoutV2Component,
		CatMigalhasComponent,
		CatPageTitleComponent,
		CatOptionsMenuUserComponent,
		CatGridSquareCardComponent,
		CatGridMiniCardComponent,
		CatGridCardV2Component,
		CatOptionsAppsPactoComponent,
		CatModalAcessoAppsQrcodeComponent,
		SafePipe,
		MenuModuleItemDirective,
		CatOptionsAlunosRecentesComponent,
	],
	entryComponents: [
		CatOptionsMenuUserComponent,
		CatAjudaComponent,
		CatVideoComponent,
		CatOptionsAppsPactoComponent,
		CatModalAcessoAppsQrcodeComponent,
		CatOptionsAlunosRecentesComponent,
	],
})
export class LayoutModule {}
