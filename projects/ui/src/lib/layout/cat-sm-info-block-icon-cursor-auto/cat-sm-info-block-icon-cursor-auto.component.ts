import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { PactoIcon } from "../../pacto-icon.enum";

@Component({
	selector: "pacto-cat-sm-info-block-icon-cursor-auto",
	templateUrl: "./cat-sm-info-block-icon-cursor-auto.component.html",
	styleUrls: ["./cat-sm-info-block-icon-cursor-auto.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatSmInfoBlockIconCursorAutoComponent implements OnInit {
	@Input() label;
	@Input() icon: PactoIcon;
	@Input() value = "-";

	constructor() {}

	ngOnInit() {}
}
