import { storiesOf, moduleMetadata } from "@storybook/angular";
import { Component, NO_ERRORS_SCHEMA } from "@angular/core";

import { CatGridCardComponent } from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        display: grid;
        gap: 30px;
        grid-template-columns: 1fr 1fr 1fr;
        background-color: #eee;
    ">
			<pacto-cat-grid-card *ngFor="let item of items; let index = index">
				<card-title>Card title - {{ index }}</card-title>
				<card-body>
					<div style="height: 100px;">Card body.</div>
				</card-body>
			</pacto-cat-grid-card>
		</div>
	`,
})
class HostComponent {
	items = [1, 2, 3, 4, 5, 6];
}

const metadata = {
	declarations: [CatGridCardComponent],
	schemas: [NO_ERRORS_SCHEMA],
};

storiesOf("Layout|Grid Card", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Simple Tabs", () => {
		return {
			component: HostComponent,
			props: {},
		};
	});
