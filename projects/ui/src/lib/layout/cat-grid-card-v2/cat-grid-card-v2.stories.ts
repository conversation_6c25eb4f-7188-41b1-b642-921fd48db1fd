import { storiesOf, moduleMetadata } from "@storybook/angular";
import { Component, NO_ERRORS_SCHEMA } from "@angular/core";

import { CatGridCardV2Component } from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        display: grid;
        gap: 30px;
        grid-template-columns: 1fr 1fr 1fr;
        background-color: #eee;
    ">
			<pacto-cat-grid-card-v2
				*ngFor="let item of items; let index = index"
				[type]="'LARANJINHA'">
				<div card-title>Card title - {{ index }}</div>
				<div card-body>
					<div style="height: 100px;">Card body.</div>
				</div>
			</pacto-cat-grid-card-v2>
		</div>
	`,
})
class HostComponent {
	items = [1, 2];
}

const metadata = {
	declarations: [CatGridCardV2Component],
	schemas: [NO_ERRORS_SCHEMA],
};

storiesOf("Layout|Grid Card V2", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Simple Tabs", () => {
		return {
			component: HostComponent,
			props: {},
		};
	});
