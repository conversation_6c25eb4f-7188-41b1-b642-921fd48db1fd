import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
} from "@angular/core";

export enum HEADER_DETAIL {
	LARANJINHA = "LARANJINHA",
	AZULIM = "AZULIM",
	BRANCO = "BRANCO",
}

@Component({
	selector: "pacto-cat-grid-card-v2",
	templateUrl: "./cat-grid-card-v2.component.html",
	styleUrls: ["./cat-grid-card-v2.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatGridCardV2Component implements OnInit {
	@Input() type: HEADER_DETAIL = HEADER_DETAIL.LARANJINHA;

	constructor() {}

	get HEADER_DETAIL() {
		return HEADER_DETAIL;
	}

	ngOnInit() {}
}
