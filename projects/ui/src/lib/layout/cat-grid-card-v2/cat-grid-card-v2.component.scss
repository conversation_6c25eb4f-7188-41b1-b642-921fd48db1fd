@import "projects/ui/assets/import.scss";

.grid-card-header {
	width: 100%;
	height: 12px;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	position: relative;
}

.azulim {
	background-color: $azulim06;
}

.laranjinha {
	background-color: $laranjinhaPri;
}

.branco {
	background-color: $branco;
}

.line {
	width: 100%;
	height: 0.5px;
	background: $cinza02;
}

.grid-card-title {
	background-color: #fff;
	color: #51555a;
	line-height: 40px;
	width: 100%;
	height: 44px;
	position: relative;
	font-weight: 700;
	padding: 5px 0px 0 12px;
	text-align: left;
	font-size: 24px;
}

.grid-card-body {
	background-color: $branco;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
	box-shadow: 0px 4px 6px #dadbe1;
	width: 100%;
}
