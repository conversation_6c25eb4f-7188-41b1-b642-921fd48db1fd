<div class="grid">
	<div
		[ngClass]="{
			chuchuzinho: type === GRID_TYPE.CHUCHUZINHO,
			azulim: type === GRID_TYPE.AZULIM
		}"
		class="grid-card-detail"></div>
	<div class="grid-card">
		<div class="grid-card-header">
			<i class="pct {{ icon }}"></i>
			{{ headerText }}
		</div>
		<div class="line"></div>
		<div class="grid-card-body">
			<ng-content select="[card-body]"></ng-content>
		</div>
	</div>
</div>
