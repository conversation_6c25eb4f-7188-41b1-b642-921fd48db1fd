import { Component, Input, OnInit } from "@angular/core";
import { PactoIcon } from "../../pacto-icon.enum";

export enum GRID_TYPE {
	CHUCHUZINHO = "CHUCHUZINHO",
	AZULIM = "AZULIM",
}

@Component({
	selector: "pacto-cat-grid-mini-card",
	templateUrl: "./cat-grid-mini-card.component.html",
	styleUrls: ["./cat-grid-mini-card.component.scss"],
})
export class CatGridMiniCardComponent implements OnInit {
	@Input() type: GRID_TYPE = GRID_TYPE.AZULIM;
	@Input() icon: PactoIcon;
	@Input() headerText = "";
	@Input() bodyText = "";
	@Input() valor = "";

	constructor() {}

	get GRID_TYPE() {
		return GRID_TYPE;
	}

	ngOnInit() {}
}
