@import "projects/ui/assets/import.scss";

.grid-card-detail {
	color: $branco;
	border-bottom-left-radius: 8px;
	border-top-left-radius: 8px;
}

.azulim {
	background-color: $azulim03;
}

.chuchuzinho {
	background-color: $chuchuzinho02;
}

.grid-card {
	background-color: $branco;
	color: $pretoPri;
	border-top-right-radius: 8px;
	border-bottom-right-radius: 8px;

	i {
		font-size: 20px;
	}
}

.grid-card-header {
	background-color: #fff;
	color: #25598b;
	padding: 12px 0 0 12px;
	border-top-right-radius: 8px;
	font-size: 14px;
	line-height: 14px;
	font-weight: bold;
}

.line {
	width: 90%;
	height: 0.5px;
	background: $cinza02;
	margin-left: 5%;
	margin-top: 11px;
	margin-bottom: 11px;
}

.grid-card-body {
	background-color: $branco;
	width: 100%;
	padding: 0px 0px 4px 12px;
	text-align: left;
	border-bottom-right-radius: 8px;
}

.grid {
	display: grid;
	grid-template-columns: 5% 95%;
	box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
	border-radius: 8px;
}
