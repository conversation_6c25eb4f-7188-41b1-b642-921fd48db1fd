import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnDestroy,
	OnInit,
	Output,
} from "@angular/core";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { Subscription } from "rxjs";
import { PlataformaNavigationService } from "../cat-platform-menu-v2/plataforma-navigation.service";

interface PlataformaRouteConfig {
	navigationPath: string[];
	moduleId: string;
	fullscreen: boolean;
}

@Component({
	selector: "pacto-cat-plataforma-menu-v2-router",
	templateUrl: "./cat-plataforma-menu-v2-router.component.html",
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PlataformaMenuV2RouterComponent implements OnInit, OnDestroy {
	@Output() logout: EventEmitter<any> = new EventEmitter();
	@Output() changeUnit: EventEmitter<any> = new EventEmitter();
	navigationPath: string[] = [];
	fullscreen: boolean;
	moduleId;

	private subscription: Subscription;

	constructor(
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private navConfigService: PlataformaNavigationService
	) {}

	ngOnInit() {
		this.subscription = this.router.events.subscribe((event) => {
			if (event instanceof NavigationEnd) {
				this.update();
			}
		});
		this.update();
	}

	ngOnDestroy() {
		this.subscription.unsubscribe();
	}

	private update() {
		const data = this.getFullCustomDataTree();
		this.moduleId = data.moduleId;
		this.navigationPath = data.navigationPath;
		this.fullscreen = data.fullscreen;
		this.cd.detectChanges();
	}

	private getFullCustomDataTree(): PlataformaRouteConfig {
		const data: any = {};
		let child = this.route;

		while (child) {
			if (child.firstChild) {
				Object.assign(data, this.getData(child));
				child = child.firstChild;
			} else {
				Object.assign(data, this.getData(child));
				child = null;
			}
		}

		const config: PlataformaRouteConfig = {
			moduleId: this.moduleId ? this.moduleId : data.moduleId,
			navigationPath: data.navigationPath,
			fullscreen: data.fullscreen,
		};

		return config;
	}

	private getData(route: ActivatedRoute) {
		if (route.snapshot.data) {
			return route.snapshot.data;
		} else {
			return {};
		}
	}
}
