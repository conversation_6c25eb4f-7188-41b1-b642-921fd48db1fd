import { Observable } from "rxjs";

export interface PlataformaMenuV2Config {
	ucpUrl?: string;
	configurationUrl?: string;
	avatarRedeUrl?: string;
	nomeUnidade: string;
	colaboradorNome: string;
	colaboradorNomeSimples: string;
	perfilAcessoAdm: string;
	perfilAcessoTreino: string;
	colaboradorAvatarUrl?: string;
	colaboradorUrl?: string;
	versao: string;
	multiUnidade?: boolean;
	independente?: boolean;
	ip: string;
	pathUrlZw?: string;
	key?: string;
	codigoUsuarioZw?: number;
	empresa?: string;
	isUsuarioPacto?: boolean;
}

export class PlataformaMenuV2Config {
	nomeRede = "Rede 1";
	nomeUnidade = "Unidade 1";
	colaboradorNome = "Colaborador";
	versao = "1.0.0";
	ip = "127.0.0.1";

	constructor(dto: Partial<PlataformaMenuV2Config> = {}) {
		for (const key in dto) {
			if (dto.hasOwnProperty(key)) {
				this[key] = dto[key];
			}
		}
	}
}
