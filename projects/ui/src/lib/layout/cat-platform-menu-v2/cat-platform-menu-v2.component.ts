import {
	Component,
	OnInit,
	OnDestroy,
	ChangeDetectionStrategy,
	Input,
	EventEmitter,
	Output,
	Optional,
	ChangeDetectorRef,
} from "@angular/core";

import { Subscription } from "rxjs";

import { PlataformaMenuV2Config } from "./model";
import { PlataformaConfigService } from "./plataforma-config.service";
import { PlataformaNavigationService } from "./plataforma-navigation.service";

@Component({
	selector: "pacto-cat-platform-menu-v2",
	templateUrl: "./cat-platform-menu-v2.component.html",
	styleUrls: ["./cat-platform-menu-v2.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatPlatformMenuV2Component implements OnInit, OnDestroy {
	configuracao: PlataformaMenuV2Config;
	@Input() navigationPath: string[] = [];
	@Input() moduloId: string;
	@Output() navigation: EventEmitter<string> = new EventEmitter();
	@Output() logout: EventEmitter<any> = new EventEmitter();
	@Output() changeUnit: EventEmitter<any> = new EventEmitter();

	private subscription: Subscription;

	constructor(
		@Optional() private configService: PlataformaConfigService,
		private navigationService: PlataformaNavigationService,
		private cd: ChangeDetectorRef
	) {}

	leaveLeftSide() {
		if (
			this.navigationService.isSmallScreen() &&
			this.navigationService.navigationMenuOpen$.value
		) {
			this.navigationService.navigationMenuOpen$.next(false);
		}
	}

	ngOnInit() {
		if (this.configService) {
			this.subscription = this.configService.getConfig().subscribe((config) => {
				this.configuracao = config;
				this.cd.detectChanges();
			});
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
	}

	ngOnDestroy() {
		this.subscription.unsubscribe();
	}
}
