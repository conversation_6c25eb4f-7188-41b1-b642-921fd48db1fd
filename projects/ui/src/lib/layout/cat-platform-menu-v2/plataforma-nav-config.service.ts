import { Injectable } from "@angular/core";

import { Observable } from "rxjs";

export interface PlataformaModule {
	moduleId: string;
	logoUri?: string;
	icon?: string;
	link?: string;
	queryParams?: {
		[name: string]: any;
	};
	baseColor?: string;
	showAsIcon?: boolean;
	linkAsHref?: boolean;
	openInNewTab?: boolean;
	hideTaskBar?: boolean;
	orderTaskbar?: number;
	showOnMenuTaskbar?: boolean;
	bottomSection?: boolean;
	orderTopbar: number;
	showOnMenuTopbar?: boolean;
	onlyLoadMenu?: boolean;
	onlyMakeRedirect?: boolean;
	menus?: PlataformaModuleItem[];
}

export interface PlataformaModuleItem {
	_searchSignature?: string;
	id: string;
	link?: string;
	parent?: string;
	newTab?: boolean;
	usuariosComPermissao?: string[];
	queryParams?: {
		[name: string]: any;
	};
	// TODO Algo temporário devido a estrutura do menu atual, durante a reformulação deverá ser repensado,
	//  devido a necessidade de validações assíncronas
	showWhen?: (permissoes?: Array<string>, modules?: Array<string>) => boolean;
}

@Injectable()
export abstract class PlataformaNavConfigService {
	constructor() {}

	abstract getModules(): Observable<PlataformaModule[]>;

	abstract resourceChange?(resource: string): void;

	abstract isBetaTester?(): Observable<boolean>;
}
