@import "projects/ui/assets/import.scss";

:host {
	display: flex;
	width: 100%;
	height: 100%;
}

.left-side {
	display: flex;
	z-index: 100;
}

.center {
	flex-grow: 1;
	background-color: #eff2f7;
}

pacto-cat-topbar {
	flex-shrink: 0;
}

.content-wrapper {
	height: calc(100% - 88px);
	overflow-y: auto;
	z-index: 0;

	&::-webkit-scrollbar {
		padding: 11px 0 11px 11px;
		width: 11px;
		height: 18px;
	}

	&::-webkit-scrollbar-thumb {
		height: 3px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	&::-webkit-scrollbar-button {
		width: 0;
		height: 0;
		display: none;
	}

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

.footer {
	margin: 80px auto 30px auto;
	font-weight: 400;
	max-width: 1290px;
	width: calc(100vw - 324px);
	padding-top: 32px;
	color: $cinza03;
	position: relative;

	.link-container {
		display: flex;
		justify-content: center;
		width: 100%;
		font-size: 14px;
		flex-wrap: wrap;

		.link {
			margin-right: 32px;
			text-decoration: none;

			a:link {
				color: $cinza03;
			}

			a:visited {
				color: $cinza03;
			}
		}
	}

	.copy {
		font-size: 12px;
		margin-top: 62px;
		text-align: center;

		.pct-heart {
			color: $laranjinhaPri;
		}

		i {
			display: inline-block;
			margin-left: 5px;
			margin-right: 5px;
		}
	}

	.chat {
		background: $azulPactoPri;
		border-radius: 50%;
		display: flex;
		flex-shrink: 0;
		justify-content: center;
		align-items: center;
		align-self: flex-end;
		position: absolute;
		right: 20px;
		bottom: 0;
		width: 36px;
		height: 36px;

		.pct-message-circle {
			color: white;
			font-size: 20px;
		}
	}
}

@media (max-width: 1326px) {
	.link {
		margin-right: 0;
	}
}

@media (max-width: 580px) {
	::ng-deep.md-chat-widget-btn-container {
		display: none;
	}

	.center {
		width: 79vw;
		flex-grow: 1;
		background-color: #eff2f7;

		::ng-deep.aux {
			pacto-cat-global-menu {
				margin-right: 0;
			}

			.pacto-user {
				margin-left: 0;

				.central {
					display: none;
				}
			}

			.search-results {
				width: 312px;
				left: -65px;
			}

			.aux {
				display: block;

				.modulos-disponiveis {
					border-bottom: $cinza02 solid 1px;
					margin-bottom: 28px;
				}
			}
		}
	}
}
