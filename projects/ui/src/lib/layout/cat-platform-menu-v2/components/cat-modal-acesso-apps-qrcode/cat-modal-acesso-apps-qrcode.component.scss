@import "projects/ui/assets/import.scss";

.container-acesso {
	margin: 24px 32px;

	.text-acesso {
		text-align: center;

		a {
			color: $azulimPri;
		}
	}

	.img-qrcode {
		text-align: center;

		img {
			width: 350px;
			height: 350px;
		}
	}

	.text-app-gestor {
		text-align: justify;
		margin-bottom: 32px;
	}

	.div-dados-acesso {
		margin-bottom: 24px;

		.div-item {
			margin-bottom: 24px;
		}

		.label-app-gestor {
			width: 100%;
			font-weight: 600;
			display: inline-block;
			margin-bottom: -0.5rem;
		}

		.input-app-gestor {
			min-height: 20px;
			color: #67757c;
			display: initial;
			padding: 0.25rem 0.5rem;
			font-size: 0.875rem;
			line-height: 1.5;
			border-radius: 0.2rem;
			width: 100%;
			background-color: #ffffff;
			border: 1px solid #ced4da;
			transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
			margin: 0;
			font-family: inherit;
		}
	}

	.div-center-btn {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;

		.btn-primary {
			width: 35%;
			background-color: #1998fc;
			border: 1px solid #1998fc;
			color: #fff;
			padding: 0;
			box-shadow: none;
			text-transform: uppercase;
			border-radius: 4px;
			line-height: 32px;
			cursor: pointer;
			position: relative;
			outline: 0;
			font-family: "Nunito Sans", sans-serif;
			font-weight: 600;
			font-size: 12px;
		}
	}
}
