import { ChangeDetectorRef, Component, OnInit, Optional } from "@angular/core";
import { PlataformaMenuV2Config } from "../../model";
import { PlataformaConfigService } from "../../plataforma-config.service";
import { Subscription } from "rxjs";
import { HttpClient } from "@angular/common/http";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService, SessionService } from "sdk";

@Component({
	selector: "pacto-cat-modal-acesso-apps-qrcode",
	templateUrl: "./cat-modal-acesso-apps-qrcode.component.html",
	styleUrls: ["./cat-modal-acesso-apps-qrcode.component.scss"],
})
export class CatModalAcessoAppsQrcodeComponent implements OnInit {
	private subscription: Subscription;
	configuracao: PlataformaMenuV2Config;
	linkAcesso: string;
	qrCodeAscesso: string;
	tela: string;
	appGestor: boolean;
	showQrCodeAppGestor = false;
	qrCodeAppGestor: string;
	timer = 30;
	showTimer = false;
	textoApresentacao: string;
	cliqueAqui: string;

	senhaFc: FormControl = new FormControl();
	emailFc: FormControl = new FormControl();

	listaServices;

	constructor(
		@Optional() private configService: PlataformaConfigService,
		private http: HttpClient,
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private notifyService: SnotifyService,
		private sessionServiceSdk: SessionService,
		private discoveryService: ClientDiscoveryService
	) {}

	ngOnInit() {
		console.log(this.configService);
		this.listaServices = this.discoveryService.getUrlMap();

		if (this.configService) {
			this.subscription = this.configService.getConfig().subscribe((config) => {
				this.configuracao = config;
				console.log(this.configuracao);
				if (this.tela === "app-gestor") {
					this.obterEmailUsuarioLogado();
				} else {
					this.obterLinksEQrCodeAcesso();
				}
			});
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
	}

	obterLinksEQrCodeAcesso() {
		const linkZW = this.configuracao.pathUrlZw || this.listaServices.zwUrl;
		const key = this.configuracao.key || this.sessionServiceSdk.chave;
		const empresa =
			this.configuracao.empresa || this.sessionServiceSdk.empresaId;
		const codigoUsuarioZw =
			this.configuracao.codigoUsuarioZw || this.sessionServiceSdk.loggedUser.id;
		const tela = this.tela;
		try {
			const url =
				linkZW +
				"/prest/obter-links-apps-pacto?key=" +
				key +
				"&empresa=" +
				empresa +
				"&usuariozw=" +
				codigoUsuarioZw +
				"&tela=" +
				tela +
				"&acao=assinaturadigital";
			this.http.get(url).subscribe((json: any) => {
				let hostZW = "";
				if (!json.content.linkAcesso.startsWith("http")) {
					hostZW = linkZW;
				}
				this.linkAcesso = hostZW + json.content.linkAcesso;
				this.qrCodeAscesso = hostZW + json.content.qrCodeAcesso;
				this.cd.detectChanges();
			});
		} catch (e) {
			console.log(e);
		}
	}

	obterEmailUsuarioLogado() {
		const linkZW = this.configuracao.pathUrlZw || this.listaServices.zwUrl;
		const key = this.configuracao.key || this.sessionServiceSdk.chave;
		const empresa =
			this.configuracao.empresa || this.sessionServiceSdk.empresaId;
		const codigoUsuarioZw =
			this.configuracao.codigoUsuarioZw || this.sessionServiceSdk.loggedUser.id;
		try {
			const url =
				linkZW +
				"/prest/obter-links-apps-pacto?key=" +
				key +
				"&empresa=" +
				empresa +
				"&usuariozw=" +
				codigoUsuarioZw +
				"&tela=" +
				"&acao=obteremailappgestor";
			this.http.get(url).subscribe((json: any) => {
				if (json.content !== "email-nao-encontrado") {
					this.emailFc.setValue(json.content);
					this.cd.detectChanges();
				} else {
					this.notifyService.error(
						"Seu usuário não possui email cadastrado para acessar o app do gestor"
					);
					this.closeModal();
				}
			});
		} catch (e) {
			console.log(e);
		}
	}

	gerarQrCodeAppGestor() {
		const linkZW = this.configuracao.pathUrlZw || this.listaServices.zwUrl;
		const key = this.configuracao.key || this.sessionServiceSdk.chave;
		const empresa =
			this.configuracao.empresa || this.sessionServiceSdk.empresaId;
		const codigoUsuarioZw =
			this.configuracao.codigoUsuarioZw || this.sessionServiceSdk.loggedUser.id;
		try {
			const url =
				linkZW +
				"/prest/obter-links-apps-pacto?key=" +
				key +
				"&empresa=" +
				empresa +
				"&usuariozw=" +
				codigoUsuarioZw +
				"&tela=" +
				"&acao=gerarqrcodeappgestor" +
				"&email=" +
				this.emailFc.value +
				"&senha=" +
				this.senhaFc.value;
			this.http.get(url).subscribe((json: any) => {
				if (json.status === "sucesso") {
					this.qrCodeAppGestor = json.qrcode;
					this.showQrCodeAppGestor = true;
					this.cd.detectChanges();
					this.startTimer();
				} else if (json.status === "senhaIncorreta") {
					this.notifyService.error("A senha informada está incorreta");
				} else {
					this.notifyService.error(
						"Não foi possível gerar o qr-code, tente novamente"
					);
				}
			});
		} catch (e) {
			console.log(e);
		}
	}

	startTimer() {
		const intervalId = setInterval(() => {
			this.timer = this.timer - 1;
			if (this.timer === 0) {
				clearInterval(intervalId);
				this.closeModal();
			}
			this.cd.detectChanges();
		}, 1000);
	}

	closeModal() {
		this.activeModal.close(false);
		this.cd.detectChanges();
	}
}
