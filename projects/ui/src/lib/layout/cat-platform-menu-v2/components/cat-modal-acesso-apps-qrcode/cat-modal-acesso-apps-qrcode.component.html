<div class="container-acesso">
	<ng-container *ngIf="!appGestor">
		<div class="text-acesso">
			<span>
				{{ textoApresentacao }}
				<a href="{{ linkAcesso }}" target="_blank">{{ cliqueAqui }}</a>
			</span>
		</div>

		<div class="img-qrcode">
			<img src="{{ qrCodeAscesso }}" />
		</div>
	</ng-container>

	<ng-container *ngIf="appGestor">
		<ng-container *ngIf="!showQrCodeAppGestor">
			<div class="text-app-gestor">
				<span>
					Faça login sem dificuldades: Entre com sua senha do ZW para gerar um
					QRCode que poderá ser lido pelo "App do Gestor"
				</span>
			</div>

			<div class="div-dados-acesso">
				<div class="div-item">
					<label class="label-app-gestor">E-mail:</label>
					<span>{{ emailFc.value }}</span>
				</div>
				<div>
					<label class="label-app-gestor">Senha:</label>
					<input
						(keyup.enter)="gerarQrCodeAppGestor()"
						[formControl]="senhaFc"
						class="input-app-gestor"
						type="password" />
				</div>
			</div>

			<div class="div-center-btn">
				<button (click)="gerarQrCodeAppGestor()" class="btn-primary">
					Gerar QRCode
				</button>
			</div>
		</ng-container>

		<ng-container *ngIf="showQrCodeAppGestor">
			<div class="text-acesso">
				<span>Expira em {{ timer }} segundos</span>
			</div>

			<div class="img-qrcode">
				<img src="{{ qrCodeAppGestor }}" />
			</div>
		</ng-container>
	</ng-container>
</div>
