import { BUTTON_TYPE } from "./../../../../forms/cat-button/cat-button.component";
import { PlataformaConfigService } from "../../plataforma-config.service";

import { Subscription } from "rxjs";
import { PlataformaMenuV2Config } from "./../../model";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	Component,
	OnInit,
	Input,
	Optional,
	ChangeDetectorRef,
	Output,
	EventEmitter,
	HostListener,
} from "@angular/core";

@Component({
	selector: "pacto-cat-options-menu-user",
	templateUrl: "./cat-options-menu-user.component.html",
	styleUrls: ["./cat-options-menu-user.component.scss"],
})
export class CatOptionsMenuUserComponent implements OnInit {
	@Output() logout: EventEmitter<any> = new EventEmitter();

	private subscription: Subscription;
	configuracao: PlataformaMenuV2Config;
	buttonType = BUTTON_TYPE;

	constructor(
		@Optional() private configService: PlataformaConfigService,
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		if (this.configService) {
			this.subscription = this.configService.getConfig().subscribe((config) => {
				this.configuracao = config;
			});
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
	}

	closeHandler() {
		this.activeModal.dismiss(false);
		this.cd.detectChanges();
	}

	closeLogoutHandler() {
		this.activeModal.dismiss(true);
		this.cd.detectChanges();
	}

	get colaboradorAvatar() {
		if (this.configuracao && this.configuracao.colaboradorAvatarUrl) {
			return this.configuracao.colaboradorAvatarUrl;
		} else {
			return null;
		}
	}

	trateAvatarError(event: ErrorEvent) {
		if (event.type === "error") {
			const target: HTMLImageElement = event.target as HTMLImageElement;
			target.src = "pacto-ui/images/user-image-default.svg";
			target.style.border = "unset";
			target.style.background = "unset";
		}
	}
}
