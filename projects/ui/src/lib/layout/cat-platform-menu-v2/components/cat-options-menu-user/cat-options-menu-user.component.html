<!--<div class="table-wrapper pacto-shadow">-->
<div class="blue-container">
	<div class="row">
		<div class="col-md-2 text-title">
			{{ "perfil.perfil" | translate }}
		</div>
		<div (click)="closeHandler()" class="close-x">
			<i class="pct pct-x close-x-pointer" id="btn-close"></i>
		</div>
	</div>
</div>

<div class="container">
	<div class="row text-container-perfil">
		<div class="col-md-2">
			<img
				(error)="trateAvatarError($event)"
				class="user-menu"
				src="{{ colaboradorAvatar }}" />
		</div>
		<div class="col text-perfil">
			<div class="row text-perfil-colab">
				<div class="capt">{{ configuracao?.colaboradorNomeSimples }}</div>
			</div>
			<div class="row text-perfil-item">
				<div *ngIf="configuracao?.perfilAcessoAdm" class="capt">
					{{ configuracao?.perfilAcessoAdm }}
				</div>

				<div *ngIf="configuracao?.perfilAcessoTreino" class="capt">
					{{ configuracao?.perfilAcessoTreino }}
				</div>
			</div>
		</div>
	</div>

	<div *ngIf="!configuracao?.isUsuarioPacto" class="row text-action">
		<div
			(click)="closeHandler()"
			[routerLink]="
				configuracao?.colaboradorUrl ? configuracao?.colaboradorUrl : []
			"
			class="text-action-item">
			<span class="title-perfil">{{ "perfil.edit" | translate }}</span>
			<i class="pct pct-chevron-right seta-float" id="btn-perfil"></i>
		</div>
	</div>

	<div class="row text-action">
		<div (click)="closeHandler()" class="text-action-item">
			<ng-container>
				<a
					[id]="'menu-cliente'"
					[queryParams]="{ redirectUri: 'uriCanal' }"
					[routerLink]="'redirectADM'"
					class="menu-cliente">
					<span class="title-perfil">{{ "perfil.canal" | translate }}</span>
					<i class="pct pct-chevron-right seta-float" id="btn-canal"></i>
				</a>
			</ng-container>
		</div>
	</div>

	<div class="btn-row button-log-out-margin">
		<pacto-cat-button
			(click)="closeLogoutHandler()"
			[icon]="'log-out'"
			[label]="'perfil.sair' | translate"
			[type]="buttonType.OUTLINE"
			class="button-log-out"
			height="32px"
			iconPosition="after"
			id="menu-user-btn-edit-logout"
			size="16px"
			type="OUTLINE"
			width="250px">
			<i class="pct pct-log-out"></i>
		</pacto-cat-button>
	</div>
</div>
