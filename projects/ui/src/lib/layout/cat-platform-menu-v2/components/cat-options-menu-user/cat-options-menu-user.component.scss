@import "projects/ui/assets/import.scss";

:host ::ng-deep pacto-cat-button span {
	font-size: 14px !important;
}

.blue-container {
	width: 100%;
	height: 40px;
	background-color: $azulim04;
	color: #ffffff;
	border-radius: 8px 8px 0px 0px;

	.close-x {
		margin-top: 14px;
		width: 70%;
	}

	.close-x-pointer {
		float: right;
		cursor: pointer;
	}
}

.text-title {
	margin-left: 15px;
	margin-top: 10px;
	font-weight: 700;
}

.container {
	border-radius: 0 0 8px 8px;
	box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.4);
	background-color: $branco;

	.text-user {
		font-size: 15px;
		text-align: center;
		color: $azulPacto02;
		font-weight: 600;
		line-height: 135%;
	}

	.text-perfil {
		margin-top: 15px;
		margin-left: 15px;
	}

	.text-container-perfil {
		border-bottom: 1px solid #eff2f7;
		padding-bottom: 10px;
		margin-bottom: 10px;
	}

	.text-action {
		margin-top: 10px;
		margin-bottom: 10px;
		border-bottom: 1px solid #eff2f7;
		padding-bottom: 10px;
		cursor: pointer;
		@extend .type-p-small-rounded;
		color: #383b3e;
	}

	.menu-cliente {
		color: #383b3e;
	}

	.text-action-item {
		margin-left: 20px;
		width: 90%;
	}

	.text-function {
		font-size: 12px;
		text-align: center;
	}

	.text-perfil-colab {
		font-style: normal;
		@extend .type-h6-bold;
		font-weight: 700;
		font-size: 16px;
		line-height: 100%;
		color: #51555a;
	}

	.title-perfil {
		@extend .type-p-small-rounded;
		font-style: normal;
		font-weight: 700;
		line-height: 100%;
		color: #383b3e;
	}

	.text-perfil-item {
		@extend .type-p-small-rounded;
		font-style: normal;
		line-height: 125%;
		color: #51555a;
		text-transform: lowercase;
	}

	.capt {
		text-transform: capitalize;
		display: inline-block;
		font-size: 16px;
	}

	.capt::first-line {
		text-transform: capitalize;
	}

	.button-edit {
		padding-bottom: 12px;
		display: flex;
		justify-content: center;
	}

	.button-log-out-margin {
		margin-top: 20px;
	}

	.button-log-out {
		padding-bottom: 24px;
		display: flex;
		justify-content: center;
		width: 100% !important;
	}

	.row-padding {
		padding-top: 16px;
	}

	.seta-float {
		float: right;
	}
}

.user-menu {
	display: flex;
	width: 32px;
	height: 32px;
	margin: 16px 0px 12px;
	border-radius: 32px;
	border: 0.1em solid $cinzaPri;
	background-color: $cinzaPri;
	overflow: hidden;
	margin-left: 28%;

	img {
		vertical-align: middle;
		max-height: 45px;
		max-width: 45px;
	}
}
