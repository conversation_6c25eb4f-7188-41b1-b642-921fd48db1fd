@import "projects/ui/assets/import.scss";

.blue-container {
	width: 100%;
	background-color: $azulim04;
	border-radius: 8px 8px 0px 0px;
	display: flex;
	justify-content: space-between;
}

.container {
	border-radius: 0 0 8px 8px;
	box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.4);
	background-color: $branco;
}

.card-aluno:hover {
	background-color: $cinza02;
}

.modal-title {
	@extend .type-h6-bold;
	color: $branco;
	font-weight: 700;
}

span.modal-title {
	padding: 12px 0px 12px 16px;
}

i.modal-title {
	padding: 16px 16px 12px 0px;

	&:hover {
		cursor: pointer;
	}
}

.section-title {
	@extend .type-h6;
	color: $pretoPri;
	padding: 10px 0 10px;
	margin: 0px;
}

.col-2 {
	font-size: 26px;
	align-self: center;
	display: flex;
	justify-content: center;
	align-items: center;
}

.nomeAluno,
.matricula<PERSON>luno {
	@extend .type-h6;
	font-size: 15px;
	color: $pretoPri;
}

.imagemAluno {
	width: 52px;
	height: 52px;
	border-radius: 50%;
}

.card-aluno {
	padding: 16px;
	border-bottom: 1px solid $cinzaClaroPri;

	.col {
		padding: 0px;
	}
}

.borderless {
	border: 0px;
}

.card-aluno:hover .inexistente {
	display: initial;
}

.icone-mensagem {
	padding: 0 0 0 5px;

	& img {
		height: 14px;
		width: 14px;
	}
}

.line2 {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.statusBadge {
	background-color: grey;
	font-size: 10px;
	border-radius: 50%;
	color: #fff;
	font-weight: 800;
	margin: 0;
	padding: 3px 4.5px 2px 4.5px;
}

.existente {
	display: initial;
}

.inexistente {
	display: none;
}

.ATIVO {
	background-color: $chuchuzinho05;
}

.INATIVO {
	background-color: $hellboyPri;
}

.VISITANTE {
	background-color: $pequizaoPri;
}

.TRANCADO {
	background-color: #fbc02d;
}

.matriculaAluno {
	padding: 0 5px 0 0;
}

.mensagemAluno {
	background-color: #fff;
	z-index: 2;
	box-shadow: 1px 1px 7px 0 #b4b7bb;
	display: flex;
	border: 1px solid #eff2f7;
	position: absolute;
	right: 10px;
	bottom: 80px;
	border-radius: 4px;
	flex-direction: column;
	align-items: flex-end;
	flex-wrap: nowrap;

	.textAreaMensagem {
		background-color: #fff;
		width: 160px;
		overflow: hidden;
		font-size: 14px;
		min-height: 80px;
		margin: 5px;
		border: 0;
		@extend .type-h6;
	}

	.buttonEdit {
		padding: 6px 15px;
		border-radius: 2px;
		background-color: #0380e3;
		color: #fff;
		margin: 4px 8px 4px 0px;
		box-shadow: none;
		font-size: 14px;
		border: 0px;
		@extend .type-h6;
	}
}

.label-falta {
	@extend .type-p-rounded;
	margin: 0 0 0 8px;
}

.actionable:hover {
	cursor: pointer;
}

::ng-deep.modal-content {
	border-radius: 9px;
	border: 0px;
}

.aluno-info {
	padding: 0 0 0 8px;
}

.favoritar-icon {
	width: 23px;

	i {
		font-size: 24px;
	}
}
