import { HttpClient } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	Optional,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { Subscription } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";
// import { TraducoesXinglingComponent } from '../../../../components/traducoes-xingling/traducoes-xingling.component';
import { PlataformaMenuV2Config } from "../../model";
import { PlataformaConfigService } from "../../plataforma-config.service";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-cat-options-alunos-recentes",
	templateUrl: "./cat-options-alunos-recentes.component.html",
	styleUrls: ["./cat-options-alunos-recentes.component.scss"],
})
export class CatOptionsAlunosRecentesComponent implements OnInit {
	// @ViewChild('traducao', { static: true }) traducao: TraducoesXinglingComponent;
	mensagem = { id: 7, isEditMode: false };
	favoritos = new Array();
	recentes = new Array();
	private subscription: Subscription;
	configuracao: PlataformaMenuV2Config;
	treinoFrontUrl = this.clientDiscoveryService.getUrlMap().treinoFrontUrl;

	constructor(
		private httpClient: HttpClient,
		private clientDiscoveryService: ClientDiscoveryService,
		private sessionService: SessionService,
		private notifyService: SnotifyService,
		@Optional() private configService: PlataformaConfigService,
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private router: Router,
		@Inject(LOCALE_ID) public locale
	) {}

	ngOnInit() {
		this.mensagem.isEditMode = false;
		this.loadData();
	}

	private mergedOptions() {
		const mergedHeaders = {
			Authorization: `Bearer ${this.sessionService.token}`,
		};
		return { headers: mergedHeaders };
	}

	hoverEvent(index) {
		this.mensagem.id = index;
		this.mensagem.isEditMode = false;
		setTimeout(() => {
			const textarea = document.getElementById(
				`aluno-favorito-mensagem-pos-${this.mensagem.id + 1}`
			);
			textarea.style.height = textarea.scrollHeight + "px";
		}, 100);
	}

	clickEvent(index) {
		this.mensagem.id = index;
		this.mensagem.isEditMode = true;
	}

	clearEvents() {
		this.mensagem.id = 7;
		this.mensagem.isEditMode = false;
	}

	autoGrow(textarea) {
		textarea.style.height = "auto";
		textarea.style.height = textarea.scrollHeight + "px";
	}

	autoShrink(textarea) {
		textarea.style.height = "80px";
	}

	salvar(aluno, i) {
		const DomElement = document.getElementsByName(
			"mensagemAlunoMatricula" + aluno.matricula
		) as any;

		this.httpClient
			.post(
				`${
					this.clientDiscoveryService.getUrlMap().admMsUrl
				}/v1/usuario/anotar-observacao-cliente-favorito?codigo=${
					aluno.codigo
				}&msg=${DomElement[0].value.toString()}`,
				this.mergedOptions()
			)
			.subscribe(
				(response: any) => {
					// this.notifyService.success(this.traducao.getLabel('alerta-anotado'));
					this.notifyService.success("Observação registrada com sucesso");
					this.clearEvents();
					this.loadData();
				},
				(err) => {
					this.notifyService.error("Houve um erro em executar a ação");
					console.error(err);
				}
			);
	}

	closeModal() {
		this.activeModal.close();
		this.cd.detectChanges();
	}

	favoritar(aluno) {
		this.httpClient
			.post(
				`${
					this.clientDiscoveryService.getUrlMap().admMsUrl
				}/v1/usuario/marcar-cliente?codigoMatricula=${aluno.matricula}&tipo=FA`,
				{},
				this.mergedOptions()
			)
			.subscribe(
				(response: any) => {
					if (response.content.statusCodeValue === 400) {
						if (
							response.content.body === "Cliente já está marcado como favorito!"
						) {
							this.notifyService.warning("Aluno já marcado como favorito.");
						} else if (
							response.content.body ===
							"A lista de favoritos já está no limite máximo de 3!"
						) {
							this.notifyService.warning(
								"Seus favoritos chegaram ao limite. Desmarque um aluno para adicionar outro."
							);
						}
					} else {
						// this.notifyService.success(this.traducao.getLabel('alerta-favoritado'));
						this.notifyService.success("Aluno marcado como favorito.");
					}
					this.clearEvents();
					this.loadData();
				},
				(err) => {
					this.notifyService.error("Houve um erro em executar a ação");
					console.error(err);
				}
			);
	}

	loadData() {
		if (this.configService) {
			this.subscription = this.configService.getConfig().subscribe((config) => {
				this.configuracao = config;
			});
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}

		this.httpClient
			.get(
				`${
					this.clientDiscoveryService.getUrlMap().admMsUrl
				}/v1/usuario/listar-clientes-marcado-por-usuario`,
				this.mergedOptions()
			)
			.subscribe(
				(response: any) => {
					this.favoritos = [];
					this.recentes = [];
					response.content.forEach((element) => {
						element.tipoClienteMarcado === "Favorito"
							? this.favoritos.push(element)
							: this.recentes.push(element);
					});
					const textarea = document.querySelector("textarea");
					textarea.style.height = textarea.scrollHeight + "px";
				},
				(err) => {
					this.notifyService.error("Houve um erro em executar a ação");
					console.error(err);
				}
			);
	}

	desfavoritar(aluno) {
		this.httpClient
			.post(
				`${
					this.clientDiscoveryService.getUrlMap().admMsUrl
				}/v1/usuario/desmarcar-cliente-favorito?codigo=${aluno.codigo}`,
				this.mergedOptions()
			)
			.subscribe(
				(response: any) => {
					// if (response.content.statusCodeValue === 400) {
					// this.notifyService.warning(response.content.body, this.traducao.getLabel('alerta-marcado-existente'));
					// } else {
					// this.notifyService.success(this.traducao.getLabel('alerta-desfavoritado'),'sucesso');
					this.notifyService.success("Aluno retirado dos favoritos.");
					// }
					this.clearEvents();
					this.loadData();
				},
				(err) => {
					this.notifyService.error("Houve um erro em executar a ação");
					console.error(err);
				}
			);
	}

	reformulaNome(nome: string) {
		const sentences = nome.split(/\s/);
		let Fname = "";
		let Mnames = "";
		let Lname = "";
		let nomeAbreviado;
		Fname = sentences[0];
		if (sentences.length > 2 && sentences[1].match(/[a-z]/i)) {
			Mnames = `${sentences[1].charAt(0).toUpperCase()}. `;
		} else if (sentences.length > 2 && !sentences[1].match(/[a-z]/i)) {
			Mnames = `${sentences[1]} `;
		}
		Lname = sentences[sentences.length - 1];
		nomeAbreviado = `${Fname} ${Mnames}${Lname}`;
		if (nomeAbreviado.length > 23) {
			nomeAbreviado = `${Fname} ${Mnames}${Lname.charAt(0).toUpperCase()}.`;
		}
		return nomeAbreviado;
	}

	statusTransform(texto: any) {
		return texto.toString().slice(0, 2).toUpperCase();
	}

	irPraAluno(codigoMatricula) {
		this.router.navigate(["pessoas", "perfil-v2", codigoMatricula, "treinos"]);
		setTimeout(() => {
			location.reload();
		}, 50);
	}
}
