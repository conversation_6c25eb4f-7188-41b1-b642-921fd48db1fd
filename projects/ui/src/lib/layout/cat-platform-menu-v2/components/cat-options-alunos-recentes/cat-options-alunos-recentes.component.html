<div class="blue-container">
	<span class="modal-title" i18n="@@alunos-recentes:modal-title">
		Alunos favoritos
	</span>
	<i (click)="closeModal()" class="pct pct-x modal-title"></i>
</div>
<div (mouseleave)="clearEvents()" class="container">
	<p class="section-title" i18n="@@alunos-recentes:alunos-favoritos-title">
		Favoritos
	</p>
	<ng-container *ngIf="!(favoritos.length > 0)">
		<span
			class="sem-alunos-favoritados label-falta"
			i18n="@@alunos-recentes:nenhum-aluno-favorito-label">
			Nenhum aluno adicionado na sua lista de favoritos. Você pode adicionar até
			3 (três) alunos aqui.
		</span>
	</ng-container>
	<ng-container *ngFor="let aluno of favoritos; let i = index">
		<div class="alunos favoritos">
			<div class="row">
				<div (mouseleave)="clearEvents()" class="col-12">
					<div [hidden]="mensagem.id != i" class="mensagemAluno">
						<textarea
							(blur)="autoShrink($event.target)"
							(change)="autoGrow($event.target)"
							(input)="autoGrow($event.target)"
							(onload)="autoGrow($event.target)"
							[class]="
								(aluno.mensagem || mensagem.isEditMode) && mensagem.id === i
									? 'textAreaMensagem existente'
									: 'textAreaMensagem inexistente'
							"
							[disabled]="!mensagem.isEditMode"
							[id]="'aluno-favorito-mensagem-pos-' + (i + 1)"
							[name]="'mensagemAlunoMatricula' + aluno.matricula"
							[value]="aluno.mensagem ? aluno.mensagem.trim() : ''"
							placeholder="|"></textarea>
						<button
							(click)="salvar(aluno, i)"
							*ngIf="mensagem.isEditMode"
							[id]="'aluno-favorito-salvar-pos-' + (i + 1)"
							class="buttonEdit actionable"
							i18n="@@alunos-recentes:salvar-button">
							Salvar
						</button>
					</div>
					<div class="row card-aluno">
						<div class="col col-2">
							<img
								[src]="
									aluno.imageURL
										? aluno.imageURL
										: aluno.fotokey
										? 'https://s3-sa-east-1.amazonaws.com/prod-zwphotos/' +
										  aluno.fotokey
										: 'pacto-ui/images/default-user-icon.png'
								"
								class="imagemAluno" />
						</div>
						<div class="col col-9">
							<div class="aluno-info">
								<div class="line1">
									<a
										[href]="
											treinoFrontUrl +
											'/' +
											locale +
											'/pessoas/perfil-v2/' +
											aluno.codigoMatricula
										">
										<span class="nomeAluno actionable">
											{{ reformulaNome(aluno.nome) | titlecase }}
										</span>
									</a>
									<span
										(click)="clickEvent(i)"
										(mouseenter)="hoverEvent(i)"
										[class]="
											aluno.mensagem
												? 'icone-mensagem actionable existente'
												: 'icone-mensagem actionable inexistente'
										"
										[id]="'aluno-favorito-icone-pos-' + (i + 1)">
										<i
											*ngIf="!aluno.mensagem"
											class="pct pct-message-square"></i>
										<img
											*ngIf="aluno.mensagem"
											src="pacto-ui/images/pct-message-square-filled.svg" />
									</span>
								</div>
								<div class="line2">
									<a
										[href]="
											treinoFrontUrl +
											'/' +
											locale +
											'/pessoas/perfil-v2/' +
											aluno.matricula
										">
										<span class="matriculaAluno actionable">
											mat: {{ aluno.matricula }}
										</span>
									</a>
									<span [class]="aluno.situacao + ' statusBadge'">
										{{ statusTransform(aluno.situacao) }}
									</span>
								</div>
							</div>
						</div>
						<div
							(click)="desfavoritar(aluno)"
							[id]="'aluno-favorito-desfavoritar-pos-' + (i + 1)"
							class="col col-1 actionable">
							<img src="pacto-ui/images/pct-star-filled.svg" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</ng-container>
	<p class="section-title" i18n="@@alunos-recentes:alunos-favoritos-title">
		Recentes
	</p>
	<ng-container *ngIf="!(recentes.length > 0)">
		<span
			class="sem-alunos-recentes label-falta"
			i18n="@@alunos-recentes:nenhum-aluno-recente-label">
			Nenhum aluno recente
		</span>
	</ng-container>
	<ng-container *ngFor="let aluno of recentes; let i = index">
		<div class="alunos recentes">
			<div class="row">
				<div class="col-12">
					<div [class.borderless]="i == 2" class="row card-aluno">
						<div class="col col-2">
							<img
								[src]="
									aluno.imageURL
										? aluno.imageURL
										: aluno.fotokey
										? 'https://s3-sa-east-1.amazonaws.com/prod-zwphotos/' +
										  aluno.fotokey
										: 'pacto-ui/images/default-user-icon.png'
								"
								class="imagemAluno" />
						</div>
						<div class="col col-9">
							<div class="aluno-info">
								<div class="line1">
									<a
										[href]="
											treinoFrontUrl +
											'/' +
											locale +
											'/pessoas/perfil-v2/' +
											aluno.codigoMatricula
										">
										<span class="nomeAluno actionable">
											{{ reformulaNome(aluno.nome) | titlecase }}
										</span>
									</a>
								</div>
								<div class="line2">
									<a
										[href]="
											treinoFrontUrl +
											'/' +
											locale +
											'/pessoas/perfil-v2/' +
											aluno.codigoMatricula
										">
										<span class="matriculaAluno actionable">
											mat: {{ aluno.matricula }}
										</span>
									</a>
									<span [class]="aluno.situacao + ' statusBadge'">
										{{ aluno.situacao.slice(0, 2) }}
									</span>
								</div>
							</div>
						</div>
						<div
							(click)="favoritar(aluno)"
							[id]="'aluno-recente-favoritar-pos-' + (i + 1)"
							class="col col-1 actionable favoritar-icon">
							<i class="pct pct-star"></i>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ng-container>
</div>
<!-- <pacto-traducoes-xingling #traducao>
	<span xingling="alerta-anotado" i18n="@@alunos-recentes:alerta-anotado">Observação registrada com sucesso</span>
	<span xingling="alerta-favoritos-max-reached" i18n="@@alunos-recentes:alerta-favoritos-max-reached">Seus favoritos chegaram ao limite. Desmarque um aluno para adicionar outro.</span>
	<span xingling="alerta-favoritado" i18n="@@alunos-recentes:alerta-favoritado">Aluno marcado como favorito.</span>
	<span xingling="alerta-marcado-existente" i18n="@@alunos-recentes:alerta-marcado-existente">Aluno já marcado como favorito.</span>
	<span xingling="alerta-desfavoritado" i18n="@@alunos-recentes:alerta-desfavoritado">Aluno retirado dos favoritos.</span>
	
</pacto-traducoes-xingling> -->
