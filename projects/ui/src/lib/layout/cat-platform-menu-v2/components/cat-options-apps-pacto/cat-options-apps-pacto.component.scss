@import "projects/ui/assets/import.scss";

.blue-triangle {
	width: 0;
	height: 0;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-bottom: 8px solid $azulim05;
	margin-left: 215px;
}

.blue-container {
	// width: 100%;
	width: 250px;
	height: 40px;
	background-color: $azulim05;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 15px;
	border-radius: 6px 6px 0 0;

	* {
		font-size: 16px;
	}

	i {
		position: relative;
		z-index: 9;
	}
}

.item-label {
	@extend .type-h6;
}

.item-label,
.item-icone {
	fill: $pretoPri;
	color: $pretoPri;
}

.title {
	@extend .type-h6-bold;
	font-weight: 700;
	color: $branco;
}

.container {
	border-radius: 0 0 6px 6px;
	background-color: $branco;
	padding: 8px 0;
	width: 250px;

	.option {
		padding: 9px 0;
		display: flex;
		cursor: pointer;
		border-bottom: 1px solid $cinzaClaroPri;

		.option-img {
			width: 28px;
			height: 28px;
			margin-left: 10px;
			margin-right: 10px;
			padding: 2px;

			img {
				width: 100%;
				height: 100%;
				padding: 3px;
			}
		}

		span {
			color: #51555a;
			font-weight: 700;
			font-size: 16px;
			padding-top: 3px;
		}
	}
}

.fechaModal {
	cursor: pointer;
}
