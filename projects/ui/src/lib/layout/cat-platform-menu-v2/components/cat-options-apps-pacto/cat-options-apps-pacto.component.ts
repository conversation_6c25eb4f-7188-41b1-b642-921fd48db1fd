import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-cat-options-apps-pacto",
	templateUrl: "./cat-options-apps-pacto.component.html",
	styleUrls: ["./cat-options-apps-pacto.component.scss"],
})
export class CatOptionsAppsPactoComponent implements OnInit {
	constructor(
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	closeHandler() {
		this.activeModal.close(false);
		this.cd.detectChanges();
	}

	abrirModalAcesso(valor) {
		this.activeModal.close(valor);
		this.cd.detectChanges();
	}

	closeModal() {
		this.activeModal.dismiss();
	}
}
