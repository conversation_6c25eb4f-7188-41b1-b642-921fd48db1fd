@import "projects/ui/assets/import.scss";

:host {
	display: flex;
	height: 100%;
	flex-shrink: 0;
	flex-direction: column;
	align-items: center;
	width: 64px;
	z-index: 99;
	position: relative;
	background-color: $azulPactoPri;
	border-right: 2px solid $azulPacto05;
	overflow: initial;

	&::-webkit-scrollbar {
		width: 0;
		height: 0;
	}

	& *:hover::-webkit-scrollbar {
		width: 0;
		height: 18px;
	}

	&::-webkit-scrollbar-thumb {
		height: 3px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	&::-webkit-scrollbar-button {
		width: 0;
		height: 0;
		display: none;
	}

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}

::-webkit-scrollbar-track {
	background-color: #1b4166;
}

::-webkit-scrollbar {
	width: 6px;
	background: #f4f4f4;
}

::-webkit-scrollbar-thumb {
	background: #dad7d7;
}

.main-logo {
	width: 40px;
	height: 40px;
	margin: 24px 12px;
}

.current-module {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	padding: 8px;
	line-height: 86%;
	color: $branco;
	font-size: 24px;

	a {
		i {
			cursor: pointer;
			color: $branco;
		}

		&:hover {
			text-decoration: none;
		}
	}
}

.icon-modules {
	margin-top: 23px;
	flex-grow: 1;

	a,
	.icon-module {
		margin-bottom: 20px;
		text-decoration: none;
		display: block;
		cursor: pointer;

		&:hover {
			text-decoration: none;
		}
	}

	i {
		font-size: 20px;
		color: $branco;
	}
}

.icon-height {
	line-height: 1.68;
}

.upper-section {
	padding-top: 24px;

	.modules-wrapper {
		background-color: $azulPacto05;
		width: 40px;
		padding: 18px 8px;
		border-radius: 20px;
	}

	.modulo {
		display: block;
		margin-bottom: 14px;

		&:last-child {
			margin-bottom: 0px;
		}

		.img-aux {
			border-radius: 13px;
			width: 24px;
			height: 24px;
			background-color: #163655;
			opacity: 0.5;
			transition: opacity 0.4s ease-out;

			&:hover {
				opacity: 1;
			}

			&.current {
				opacity: 1;
			}

			overflow: hidden;
		}

		img {
			width: 24px;
			height: 24px;
		}
	}
}

.lower-section {
	padding-bottom: 20px;

	a,
	.icon-module {
		margin-bottom: 20px;
		text-decoration: none;
		display: block;
		cursor: pointer;

		&:hover {
			text-decoration: none;
		}

		&:last-child {
			margin-bottom: 0;
		}
	}

	i {
		font-size: 20px;
		color: $branco;
	}
}

.inner-circle {
	width: 40px;
	height: 40px;
	pointer-events: none;
	display: flex;
	flex-wrap: wrap;
	flex-direction: row;
	align-items: center;
	justify-content: center;

	img {
		width: 32px;
		height: 32px;
	}
}

@media (max-width: 1366px) {
	.icon-modules {
		margin-top: 10px;
		flex-grow: 1;

		a {
			margin-bottom: 8px !important;
			text-decoration: none;
			display: block;

			&:hover {
				text-decoration: none;
			}
		}

		i {
			font-size: 20px;
			color: $branco;
		}
	}

	.upper-section {
		padding-top: 10px;

		.modulo {
			display: block;
			margin-bottom: 14px;

			&:last-child {
				margin-bottom: 0px;
			}

			.img-aux {
				border-radius: 13px;
				width: 24px;
				height: 24px;
				background-color: #163655;
				display: flex;
				opacity: 0.5;
				transition: opacity 0.4s ease-out;

				&:hover {
					opacity: 1;
				}

				&.current {
					opacity: 1;
				}

				overflow: hidden;
			}

			img {
				width: 24px;
				height: 24px;
			}
		}
	}
}

.icon-config {
	background-color: #204c77;
	padding: 6px 10px 2px 10px;
	border-radius: 5px;
	right: 0;

	&:hover {
		background-color: #163655;
	}

	i {
		font-size: 20px;
	}
}

/* HD */
@media (max-height: 720px) {
	.icon-modules {
		margin-top: 15px;
		flex-grow: 0;

		i {
			font-size: 17px;
		}
	}
	.current-module {
		padding: 3px;
	}
	.lower-section {
		i {
			font-size: 17px;
		}
	}
	.icon-config {
		background-color: transparent;
	}
	.main-logo {
		width: 38px;
		height: 38px;
		margin: 25px 12px;
	}
	.logo-aux {
		min-height: 36px;
		max-height: 38px;
	}
}

/* Full HD */
@media (min-height: 721px) {
	.upper-section {
		padding-top: 16px;
	}
	.icon-modules {
		margin-top: 16px;
	}
}
