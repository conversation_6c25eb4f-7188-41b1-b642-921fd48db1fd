<img
	alt="logo-pacto"
	class="main-logo"
	src="pacto-ui/images/logos/pct-icone-fundo-pacto-branco.svg" />

<div
	[ngStyle]="{
		'background-color':
			currentModule && currentModule.baseColor
				? currentModule.baseColor
				: '#2B68A3'
	}"
	class="current-module">
	<span class="inner-circle">
		<ng-container *ngIf="!currentModule?.showAsIcon">
			<a
				[id]="'current-module-' + currentModule?.id"
				[routerLink]="currentModule?.link">
				<img
					alt="logo-pacto-tranparent"
					height="40"
					src="pacto-ui/images/pct-transparente.svg"
					width="40" />
			</a>
		</ng-container>
		<ng-container *ngIf="currentModule?.showAsIcon">
			<a
				[id]="'current-module-' + currentModule?.id"
				[routerLink]="currentModule?.link">
				<i class="icon-height pct {{ currentModule?.icon }}"></i>
			</a>
		</ng-container>
	</span>
</div>

<div class="upper-section">
	<pacto-modules-carousel
		*ngIf="isBeta | async; else notBeta"
		[loadMenu]="loadMenu"
		[modules]="modules"
		[moduloId]="moduloId"></pacto-modules-carousel>

	<ng-template #notBeta>
		<div class="modules-wrapper">
			<ng-container *ngFor="let modulo of modulesFilter">
				<a
					(click)="loadMenu(modulo)"
					*ngIf="!modulo.linkAsHref"
					[id]="'taskbar-modulo-' + modulo.id"
					[queryParams]="modulo.queryParams"
					[routerLink]="modulo.link"
					class="modulo"
					title="{{ 'menu.modules.' + modulo.id + '.name' | translate }}">
					<div [ngClass]="{ current: modulo.id === moduloId }" class="img-aux">
						<img
							*ngIf="modulo.imageUri"
							alt="{{ modulo.id }}"
							src="{{ modulo.imageUri }}" />
					</div>
				</a>
				<a
					(click)="loadMenu(modulo)"
					*ngIf="modulo.linkAsHref"
					[href]="modulo.link"
					[id]="'taskbar-modulo-' + modulo.id"
					[target]="modulo.openInNewTab ? '_blank' : '_self'"
					class="modulo"
					title="{{ 'menu.modules.' + modulo.id + '.name' | translate }}">
					<div [ngClass]="{ current: modulo.id === moduloId }" class="img-aux">
						<img
							*ngIf="modulo.imageUri"
							alt="{{ modulo.id }}"
							src="{{ modulo.imageUri }}" />
					</div>
				</a>
			</ng-container>
		</div>
	</ng-template>
</div>

<div class="icon-modules">
	<ng-container *ngFor="let iconModule of iconModulesFilter">
		<ng-container *ngIf="!iconModule.link && !iconModule.linkAsHref">
			<span
				(click)="loadMenu(iconModule)"
				*ngIf="!iconModule.link"
				[id]="'taskbar-action-' + iconModule.id"
				class="icon-module"
				title="{{ 'menu.modules.' + iconModule.id + '.name' | translate }}">
				<i class="pct {{ iconModule?.icon }}"></i>
			</span>
		</ng-container>
		<ng-container *ngIf="iconModule.link || iconModule.linkAsHref">
			<a
				(click)="loadMenu(iconModule)"
				*ngIf="!iconModule.linkAsHref"
				[id]="'taskbar-action-' + iconModule.id"
				[queryParams]="iconModule.queryParams"
				[routerLink]="iconModule.link"
				class="icon-module"
				title="{{ 'menu.modules.' + iconModule.id + '.name' | translate }}">
				<i class="pct {{ iconModule?.icon }}"></i>
			</a>
			<a
				(click)="loadMenu(iconModule)"
				*ngIf="iconModule.linkAsHref"
				[href]="iconModule.link"
				[id]="'taskbar-action-' + iconModule.id"
				[target]="iconModule.openInNewTab ? '_blank' : '_self'"
				class="icon-module"
				title="{{ 'menu.modules.' + iconModule.id + '.name' | translate }}">
				<i class="pct {{ iconModule?.icon }}"></i>
			</a>
		</ng-container>
	</ng-container>

	<a
		*ngIf="configuracao?.ucpUrl"
		[attr.href]="configuracao?.ucpUrl"
		[id]="'taskbar-action-ucp'">
		<i class="pct pct-help-circle"></i>
	</a>
</div>

<div class="lower-section">
	<ng-container *ngFor="let iconModule of bottomModulesFilter">
		<ng-container *ngIf="!iconModule.link && !iconModule.linkAsHref">
			<span
				(click)="loadMenu(iconModule)"
				*ngIf="!iconModule.link"
				[id]="'taskbar-action-' + iconModule.id"
				class="icon-module icon-{{ iconModule.id }}"
				title="{{ 'menu.modules.' + iconModule.id + '.name' | translate }}">
				<i class="pct {{ iconModule?.icon }}"></i>
			</span>
		</ng-container>
		<ng-container *ngIf="iconModule.link || iconModule.linkAsHref">
			<a
				(click)="loadMenu(iconModule)"
				*ngIf="!iconModule.linkAsHref"
				[id]="'taskbar-action-' + iconModule.id"
				[queryParams]="iconModule.queryParams"
				[routerLink]="iconModule.link"
				class="icon-module icon-{{ iconModule.id }}"
				title="{{ 'menu.modules.' + iconModule.id + '.name' | translate }}">
				<i class="pct {{ iconModule?.icon }}"></i>
			</a>
			<a
				(click)="loadMenu(iconModule)"
				*ngIf="iconModule.linkAsHref"
				[href]="iconModule.link"
				[id]="'taskbar-action-' + iconModule.id"
				[target]="iconModule.openInNewTab ? '_blank' : '_self'"
				class="icon-module icon-{{ iconModule.id }}"
				title="{{ 'menu.modules.' + iconModule.id + '.name' | translate }}">
				<i class="pct {{ iconModule?.icon }}"></i>
			</a>
		</ng-container>
	</ng-container>
</div>

<!--


<div class="lower-section">
  <a [routerLink]="configuracao?.colaboradorUrl ? configuracao?.colaboradorUrl : []" class="user-menu"
    [attr.title]="configuracao?.colaboradorNome">
    <img src="{{ colaboradorAvatar }}">
  </a>
  <div class="action">
    <i title="{{ 'menu.logout.hint' | translate }}" (click)="logout.emit(true)" class="pct pct-log-out"></i>
  </div>
</div>
-->
