import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	Output,
} from "@angular/core";
import { BehaviorSubject, Subject } from "rxjs";
import { debounceTime, distinctUntilChanged, takeUntil } from "rxjs/operators";
import { PlataformaMenuV2Config } from "../../model";
import {
	PlataformaModuleSummary,
	PlataformaNavigationService,
} from "../../plataforma-navigation.service";

@Component({
	selector: "pacto-cat-menu-taskbar",
	templateUrl: "./cat-menu-taskbar.component.html",
	styleUrls: ["./cat-menu-taskbar.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatMenuTaskbarComponent implements OnInit, OnChanges, OnDestroy {
	@Output() logout: EventEmitter<any> = new EventEmitter();
	@Input() configuracao: PlataformaMenuV2Config;
	@Input() moduloId: string;

	currentModule: PlataformaModuleSummary;
	modules: PlataformaModuleSummary[] = [];

	isBeta: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
	private _destroy: Subject<void> = new Subject();

	constructor(
		private navService: PlataformaNavigationService,
		private cd: ChangeDetectorRef
	) {}

	get iconModulesFilter() {
		if (this.modules) {
			return this.modules.filter((module) => {
				const current = module.id === this.moduloId;
				return (
					!current &&
					module.showAsIcon &&
					!module.hideTaskBar &&
					!module.bottomSection
				);
			});
		}
		return [];
	}

	get bottomModulesFilter() {
		if (this.modules) {
			return this.modules.filter((module) => {
				const current = module.id === this.moduloId;
				return (
					!current &&
					module.showAsIcon &&
					!module.hideTaskBar &&
					module.bottomSection
				);
			});
		}
		return [];
	}

	get modulesFilter() {
		if (this.modules) {
			return this.modules.filter((module) => {
				const current = module.id === this.moduloId;
				return (
					!current &&
					!module.showAsIcon &&
					!module.hideTaskBar &&
					module.showOnMenuTaskbar
				);
			});
		}
		return [];
	}

	get colaboradorAvatar() {
		if (this.configuracao && this.configuracao.colaboradorAvatarUrl) {
			return this.configuracao.colaboradorAvatarUrl;
		} else {
			return null;
		}
	}

	ngOnInit() {
		this.navService
			.getModulesSummary(false)
			.pipe(
				distinctUntilChanged(
					(prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
				)
			)
			.subscribe((modules) => {
				this.modules = modules;
				this.cd.detectChanges();
			});

		if (this.navService.isBetaTester) {
			this.navService
				.isBetaTester()
				.pipe(debounceTime(300), takeUntil(this._destroy))
				.subscribe((isBeta) => {
					this.isBeta.next(isBeta);
				});
		}
	}

	ngOnChanges() {
		this.updateCurrentModule();
		this.navService.moduleHasChanged$
			.pipe(
				distinctUntilChanged(
					(prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
				)
			)
			.subscribe((val) => {
				if (val.moduleSummary) {
					if (val.moduleSummary.onlyLoadMenu) {
						this.moduloId = val.moduleSummary.id;
					}
					this.updateCurrentModule();
				}
			});
	}

	ngOnDestroy(): void {
		this._destroy.next();
	}

	private updateCurrentModule() {
		this.navService.getModulesSummary(false).subscribe((modules) => {
			if (modules) {
				this.currentModule = modules.find((i) => i.id === this.moduloId);
			}
			if (!this.cd["destroyed"]) {
				this.cd.detectChanges();
			}
		});
	}

	loadMenu(icon: PlataformaModuleSummary) {
		if (
			!icon.linkAsHref &&
			(!icon.link || !icon.link.includes("redirectADM"))
		) {
			this.navService.moduleHasChanged$.next({ moduleSummary: icon });
		}
	}
}
