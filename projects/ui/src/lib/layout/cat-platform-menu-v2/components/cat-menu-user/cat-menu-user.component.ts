import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	DialogService,
	PactoModalSize,
} from "./../../../../dialog/service/dialog.service";
import { CatOptionsMenuUserComponent } from "./../cat-options-menu-user/cat-options-menu-user.component";
import { CatOptionsAlunosRecentesComponent } from "../cat-options-alunos-recentes/cat-options-alunos-recentes.component";
import { PlataformaMenuV2Config } from "./../../model";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { CatOptionsAppsPactoComponent } from "../cat-options-apps-pacto/cat-options-apps-pacto.component";
import { CatModalAcessoAppsQrcodeComponent } from "../cat-modal-acesso-apps-qrcode/cat-modal-acesso-apps-qrcode.component";
import { CatAjudaComponent } from "../cat-ajuda/cat-ajuda.component";
import { CatVideoComponent } from "../cat-video/cat-video.component";

@Component({
	selector: "pacto-cat-menu-user",
	templateUrl: "./cat-menu-user.component.html",
	styleUrls: ["./cat-menu-user.component.scss"],
})
export class CatMenuUserComponent implements OnInit {
	@Output() logout: EventEmitter<any> = new EventEmitter();
	@Input() configuracao: PlataformaMenuV2Config;
	visible = false;
	visibleAppsPacto = false;
	visibleAjuda = false;
	visibleAlunoRecente = false;
	layoutFlag = true;
	private dialogRef: any = null;
	classModal: string;
	classBackdrop: string;

	constructor(private dialogService: DialogService, public modal: NgbModal) {}

	ngOnInit() {
		this.dialogService.emitLayoutModal.subscribe(
			(flag) => (this.layoutFlag = flag)
		);
	}

	abrirModalUsuario() {
		this.classBackdrop = "balloon-backdrop";
		if (this.layoutFlag) {
			this.classModal = "balloon-modal";
		} else {
			this.classModal = "balloon-modal-off-menu";
		}

		this.dialogRef = this.dialogService.openBalloon(
			"",
			CatOptionsMenuUserComponent,
			null,
			this.classBackdrop,
			this.classModal
		);
		this.visible = true;
		this.dialogRef.result
			.then((result) => {
				this.visible = false;
				if (result) {
					this.logoutAccount();
				}
			})
			.catch((error) => {
				this.visible = false;
				if (error) {
					this.logoutAccount();
				}
			});
	}

	abrirModalAjuda() {
		this.classBackdrop = "balloon-backdrop";
		if (this.layoutFlag) {
			this.classModal = "balloon-modal-ajuda";
		} else {
			this.classModal = "balloon-modal-off-menu-ajuda";
		}
		this.dialogRef = this.dialogService.openBalloon(
			"",
			CatAjudaComponent,
			null,
			this.classBackdrop,
			this.classModal
		);
		this.visibleAjuda = true;
		this.dialogRef.result
			.then((result) => {
				this.visibleAjuda = false;
				if (result) {
					const modal = this.dialogService.open(
						"",
						CatVideoComponent,
						PactoModalSize.MEDIUM,
						"modal-video"
					);
					modal.componentInstance.url = result;
				}
			})
			.catch((error) => {
				this.visibleAjuda = false;
			});
	}

	logoutAccount() {
		this.logout.emit(true);
	}

	get colaboradorAvatar() {
		if (this.configuracao && this.configuracao.colaboradorAvatarUrl) {
			return this.configuracao.colaboradorAvatarUrl;
		} else {
			return null;
		}
	}

	openModalAlunoRecente() {
		this.classBackdrop = "balloon-backdrop";
		if (this.layoutFlag) {
			this.classModal = "balloon-modal-alunos";
		} else {
			this.classModal = "balloon-modal-off-menu-alunos";
		}

		this.dialogRef = this.dialogService.openBalloon(
			"",
			CatOptionsAlunosRecentesComponent,
			null,
			this.classBackdrop,
			this.classModal
		);
		this.visibleAlunoRecente = true;

		this.dialogRef.result
			.then((result) => {
				this.visibleAlunoRecente = false;
				if (result) {
					console.log(result);
				}
			})
			.catch((error) => {
				this.visibleAlunoRecente = false;
			});
	}

	openModalAppsPacto() {
		this.classBackdrop = "balloon-backdrop";
		if (this.layoutFlag) {
			this.classModal = "balloon-modal-apps-pacto";
		} else {
			this.classModal = "balloon-modal-off-menu-apps-pacto";
		}
		this.dialogRef = this.dialogService.openBalloon(
			"",
			CatOptionsAppsPactoComponent,
			null,
			this.classBackdrop,
			this.classModal
		);
		this.visibleAppsPacto = true;
		this.dialogRef.result
			.then((result) => {
				switch (result) {
					case "assinatura-digital": {
						const modal = this.dialogService.open(
							"Acessar Assinatura Digital",
							CatModalAcessoAppsQrcodeComponent,
							PactoModalSize.MEDIUM,
							"modal-acesso-qrcode"
						);
						modal.componentInstance.tela = "inicio";
						modal.componentInstance.textoApresentacao =
							"Leia o QRCode e vá até o Assinatura Digital no seu dispositivo móvel, ou acesse o link:";
						modal.componentInstance.cliqueAqui = "Assinatura Digital";
						modal.componentInstance.appGestor = false;
						break;
					}
					case "cartao-vacina": {
						const modal = this.dialogService.open(
							"Acessar Cartão de Vacina",
							CatModalAcessoAppsQrcodeComponent,
							PactoModalSize.MEDIUM,
							"modal-acesso-qrcode"
						);
						modal.componentInstance.tela = "vacina";
						modal.componentInstance.textoApresentacao =
							"Leia o QRCode e vá até o Cartão de Vacina no seu dispositivo móvel, ou acesse o link:";
						modal.componentInstance.cliqueAqui = "Cartão de Vacina";
						modal.componentInstance.appGestor = false;
						break;
					}
					default: {
						const modal = this.dialogService.open(
							"Acessar Formulário Par-Q +",
							CatModalAcessoAppsQrcodeComponent,
							PactoModalSize.MEDIUM,
							"modal-acesso-qrcode"
						);
						modal.componentInstance.tela = "par-q";
						modal.componentInstance.textoApresentacao =
							"Leia o QRCode e vá até o Formulário Par-Q + no seu dispositivo móvel, ou acesse o link:";
						modal.componentInstance.cliqueAqui = "Formulário Par-Q +";
						modal.componentInstance.appGestor = false;
						break;
					}
				}
				this.visibleAppsPacto = false;
			})
			.catch((error) => {
				this.visibleAppsPacto = false;
			});
	}

	trateAvatarError(event: ErrorEvent) {
		if (event.type === "error") {
			const target: HTMLImageElement = event.target as HTMLImageElement;
			target.src = "pacto-ui/images/user-image-default.svg";
			target.style.height = "inherit";
			target.style.width = "inherit";

			const parent = target.parentElement;
			parent.style.background = "unset";
			parent.style.border = "unset";
		}
	}
}
