<div class="section-menu">
	<ng-container *ngIf="!configuracao?.independente">
		<div
			[ngClass]="
				visibleAppsPacto
					? 'apps-pacto-background-ativo'
					: 'apps-pacto-background'
			"
			class="apps-pacto"
			id="topbar-action-qrcode">
			<div (click)="openModalAppsPacto()">
				<i class="pct pct-qr-code"></i>
			</div>
		</div>
	</ng-container>

	<div
		[ngClass]="visibleAjuda ? 'ajuda-background-ativo' : 'ajuda-background'"
		class="apps-pacto"
		id="topbar-action-ajuda">
		<div (click)="abrirModalAjuda()">
			<i class="pct pct-help-circle"></i>
		</div>
	</div>

	<div [ngClass]="visible ? 'background-ativo' : 'background'">
		<div
			(click)="abrirModalUsuario()"
			class="user-section"
			id="topbar-action-user-profile">
			<a [attr.title]="configuracao?.colaboradorNome" class="user-menu">
				<img (error)="trateAvatarError($event)" src="{{ colaboradorAvatar }}" />
			</a>
			<i *ngIf="visible === false" class="pct pct-chevron-down"></i>
			<i *ngIf="visible === true" class="pct pct-chevron-up"></i>
		</div>
	</div>
</div>
