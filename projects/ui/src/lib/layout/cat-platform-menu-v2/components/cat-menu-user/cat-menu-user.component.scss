@import "projects/ui/assets/import.scss";

.section-menu {
	display: flex;
	width: 100%;
	justify-content: flex-end;

	.apps-pacto {
		color: #1b4166;
		width: 40px;
		height: 40px;
		border-radius: 20px;
		display: flex;
		margin-right: 10px;
		cursor: pointer;

		i {
			display: flex;
			font-size: 28px;
			margin-left: 7.7px;
			margin-top: 5.5px;
			color: $azulPactoPri;
		}
	}

	.apps-pacto-background,
	.alunos-recentes-background,
	.ajuda-background,
	.ajuda-background-ativo {
		background-color: #ffffff;
	}

	.alunos-recentes-background-ativo {
		background-color: $azulPacto02;

		i {
			color: #ffffff;
		}
	}

	.apps-pacto-background-ativo {
		background-color: $azulPacto02;

		i {
			color: #ffffff;
		}
	}

	.central {
		//background-color: $cinzaClaroPri;
		width: 40px;
		height: 40px;
		border-radius: 20px;
		display: flex;
		margin-right: 10px;
		cursor: pointer;

		i {
			display: flex;
			font-size: 28px;
			margin-left: 5.5px;
			margin-top: 5.5px;
			color: $azulPactoPri;
		}
	}

	.background {
		background-color: $cinzaClaroPri;
		width: 72px;
		height: 40px;
		border-radius: 52px;
		display: flex;

		.user-section {
			display: inline-flex;
			align-items: center;
			padding-bottom: 20px;
			padding-left: 6px;
			cursor: pointer;

			.action {
				display: block;

				margin: 24px 4px 0px 0px;
				text-decoration: none;

				i {
					display: block;
					font-size: 26px;
				}
			}

			.user-menu {
				width: 32px;
				height: 32px;
				margin: 12px 0px;
				border-radius: 18px;
				border: 0.1em solid $azulPactoPri;
				background-color: $azulim05;
				overflow: hidden;
				margin-top: 32px;

				img {
					vertical-align: middle;
					max-height: 32px;
					max-width: 32px;
				}
			}

			i {
				margin-top: 40%;
				margin-left: 4px;
				font-size: larger;
				color: $azulPacto02;
			}
		}
	}

	.background-ativo {
		background-color: $azulPacto02;
		width: 72px;
		height: 40px;
		border-radius: 52px;
		display: flex;

		.user-section {
			display: inline-flex;
			align-items: center;
			padding-bottom: 20px;
			padding-left: 6px;
			cursor: pointer;

			.action {
				display: block;

				margin: 24px 4px 0px 0px;
				text-decoration: none;

				i {
					display: block;
					font-size: 26px;
				}
			}

			.user-menu {
				width: 32px;
				height: 32px;
				margin: 12px 0px;
				border-radius: 18px;
				border: 0.1em solid $azulPactoPri;
				background-color: $azulim05;
				overflow: hidden;
				margin-top: 32px;

				img {
					vertical-align: middle;
					max-height: 32px;
					max-width: 32px;
				}
			}

			i {
				margin-top: 40%;
				margin-left: 4px;
				font-size: larger;
				color: $branco;
			}
		}
	}
}

.videoAberto {
	z-index: 999999;
	background: rgba(0, 0, 0, 0.5);
	position: fixed;
	left: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
}

.comImg {
	align-items: center;
	justify-content: center;
}
