<!-- MENU OPENER -->
<div
	(click)="menuToggleHandle()"
	(mouseenter)="toggleEnter()"
	[ngClass]="{
		menuOpen: menuOpen
	}"
	class="menu-toggle">
	<i *ngIf="menuOpen" class="pct pct-chevron-left"></i>
	<i *ngIf="!menuOpen" class="pct pct-chevron-right"></i>
</div>

<div
	[ngClass]="{
		open: menuOpen
	}"
	class="animated-width-container">
	<!-- MENU CONTENT -->
	<div *ngIf="menuOpen" class="content-wrapper">
		<div class="logo-aux">
			<div
				*ngIf="companyLogo"
				[ngStyle]="{ 'background-image': companyLogo }"
				class="logo"></div>
			<div class="unidade-aux">
				<div class="cliente" title="{{ configuracao?.nomeUnidade }}">
					{{ configuracao?.nomeUnidade }}
				</div>
				<div
					(click)="changeUnit.emit(true)"
					*ngIf="configuracao?.multiUnidade"
					class="unidade"
					id="menu-trocar-unidade">
					{{ "menu.trocarUnidade" | translate }}
					<i class="pct pct-repeat"></i>
				</div>
			</div>
		</div>

		<ng-container *ngIf="currentModule?.id">
			<div
				[ngStyle]="{
					'background-color':
						currentModule && currentModule.baseColor
							? currentModule.baseColor
							: '#2B68A3'
				}"
				class="modulo">
				<a
					*ngIf="!currentModule?.linkAsHref"
					[routerLink]="currentModule?.link"
					class="module-name">
					{{ "menu.modules." + currentModule?.id + ".name" | translate }}
				</a>
				<a
					*ngIf="currentModule?.linkAsHref"
					[href]="currentModule.link"
					[routerLink]="currentModule?.link"
					[target]="currentModule?.openInNewTab ? '_blank' : '_self'"
					class="module-name">
					{{ "menu.modules." + currentModule?.id + ".name" | translate }}
				</a>
			</div>
		</ng-container>

		<div
			(click)="voltarHandler()"
			*ngIf="depth && enableNavigation"
			class="voltar"
			id="menu-voltar">
			<i class="pct pct-arrow-left-circle"></i>
			<div>
				<span>
					{{
						"menu.nav." + currentModule?.id + "." + parentMenu.id + ".name"
							| translate
					}}
				</span>
			</div>
		</div>

		<div class="container-mask menus">
			<ng-container *ngIf="enableNavigation">
				<ng-container *ngFor="let item of contextItems">
					<!-- NAVIGATION MENU WITH LINK -->
					<a
						(click)="menuClickHandler(item)"
						*ngIf="item.link && !item.newTab"
						[id]="'menu-' + currentModule?.id + '-' + item.id"
						[queryParams]="item.queryParams"
						[routerLink]="item.link"
						class="menu">
						<div class="menu-name">
							{{
								"menu.nav." + currentModule?.id + "." + item.id + ".name"
									| translate
							}}
						</div>
						<i
							*ngIf="item.submenus"
							class="submenu-indicator pct pct-arrow-right-circle"></i>
						<div
							[routerLinkActiveOptions]="{ exact: true }"
							[routerLinkActive]="'active'"
							class="active-marker"></div>
					</a>

					<!-- WITHOUT LINK -->
					<a
						(click)="menuClickHandler(item)"
						*ngIf="!item.link"
						[id]="'menu-' + currentModule?.id + '-' + item.id"
						class="menu">
						<div class="menu-name">
							{{
								"menu.nav." + currentModule?.id + "." + item.id + ".name"
									| translate
							}}
						</div>
						<i
							*ngIf="item.submenus"
							class="submenu-indicator pct pct-arrow-right-circle"></i>
						<div
							[routerLinkActiveOptions]="{ exact: true }"
							[routerLinkActive]="'active'"
							class="active-marker"></div>
					</a>

					<a
						(click)="menuClickHandler(item)"
						*ngIf="item.link && item.newTab"
						[id]="'menu-' + currentModule?.id + '-' + item.id"
						[queryParams]="item.queryParams"
						[routerLink]="item.link"
						class="menu"
						target="_blank">
						<div class="menu-name">
							{{
								"menu.nav." + currentModule?.id + "." + item.id + ".name"
									| translate
							}}
						</div>
						<i
							*ngIf="item.submenus"
							class="submenu-indicator pct pct-arrow-right-circle"></i>
						<div
							[routerLinkActiveOptions]="{ exact: true }"
							[routerLinkActive]="'active'"
							class="active-marker"></div>
					</a>
				</ng-container>
			</ng-container>
		</div>

		<div class="rodape">
			<div class="system-stat">
				<a
					class="novidades_link"
					href="https://pactosolucoes.com.br/ajuda/kb/novidades"
					id="link-novidades"
					target="_blank">
					<i class="pct pct-star"></i>
					Novidades
				</a>
			</div>
			<div class="system-stat">
				<i class="pct pct-calendar"></i>
				({{ dataHoje | date : "dd/MM/yyyy" : lang }}) -
				{{ dataHoje | date : "shortTime" : lang }}
			</div>
			<div *ngIf="infoIp" class="system-stat">
				<i class="pct pct-monitor"></i>
				IP: {{ infoIp }}
			</div>
			<div *ngIf="infoVerao" class="system-stat">
				<i class="pct pct-package"></i>
				Sistema: {{ infoVerao }}
			</div>
			<div *ngIf="infoVerao" class="system-stat">
				<i class="pct pct-globe"></i>
				Lang:
				<span class="language">{{ lang }}</span>
			</div>
		</div>
	</div>
</div>
