@import "projects/ui/assets/import.scss";

:host {
	display: block;
	height: 100%;
	position: relative;
	min-width: 18px;
	z-index: 99;
}

::-webkit-scrollbar {
	padding: 11px 0 11px 11px;
	width: 11px;
	height: 18px;
}

::-webkit-scrollbar-thumb {
	height: 3px;
	border: 4px solid rgba(0, 0, 0, 0);
	background-clip: padding-box;
	border-radius: 3px;
	box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
		inset 1px 1px 0px rgba(0, 0, 0, 0.05);
	background-color: #6f747b;
}

::-webkit-scrollbar-button {
	width: 0;
	height: 0;
	display: none;
}

::-webkit-scrollbar-corner {
	background-color: transparent;
}

.menu-toggle {
	width: 24px;
	height: 32px;
	color: $branco;
	background-color: $azulPactoPri;
	font-size: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	cursor: pointer;
	right: -10px;
	border-radius: 4px;
	top: 100px;
	z-index: 12;
}

.animated-width-container {
	height: 100%;
	width: 18px;
	overflow: hidden;
	background-color: $azulPactoPri;

	&.open {
		width: 200px;
	}
}

@media (max-width: $plataforma-breakpoint-medium) {
	.animated-width-container {
		position: absolute;
		z-index: 11;
		left: 0px;
		top: 0px;
		bottom: 0px;
	}

	.menu-toggle.menuOpen {
		right: -192px;
	}
}

.content-wrapper {
	display: flex;
	flex-direction: column;
	width: 200px;
	height: 100%;
}

@media (min-height: 769px) {
	logo-aux {
		min-height: 56px;
	}
}

.logo-aux {
	margin: 24px 24px;
	display: flex;
	min-height: 40px;
	max-height: 40px;
	align-items: center;

	.logo {
		border-radius: 5px;
		width: 42px;
		height: 42px;
		margin-right: 8px;
		background-color: $branco;
		background-position: center;
		background-size: 100% 100%;
		flex-shrink: 0;
		flex-grow: 1;
	}

	.unidade-aux {
		flex-basis: 104px;
		overflow: hidden;
		flex-grow: 1;
	}

	.cliente {
		@extend .type-h6-bold;
		color: $canetaBicPri;
		white-space: nowrap;
		overflow: hidden;
		width: 100%;
		text-overflow: ellipsis;
	}

	.unidade {
		@extend .type-caption;
		color: $canetaBic01;
		cursor: pointer;
		display: flex;
		width: 100%;
		align-items: center;

		.pct {
			padding-left: 8px;
		}
	}
}

.voltar {
	cursor: pointer;
	display: flex;
	@extend .type-h6-bold;
	align-items: center;
	color: $branco;
	background-color: $azulPacto07;
	font-size: 14px;
	padding: 0px 0px 0px 15px;
	line-height: 56px;
	border-bottom: 1px solid $canetaBicPri;

	i {
		font-size: 18px;
		padding-right: 8px;
	}

	div {
		line-height: 1.6rem;
		height: 42px;
		display: flex;
		align-items: center;

		span {
			font-weight: 400;
		}
	}
}

.modulo {
	flex-shrink: 0;
	line-height: 56px;
	font-size: 16px;
	text-align: center;
	white-space: nowrap;
	overflow: hidden;
	display: flex;
	padding-left: 24px;
	align-items: center;
	margin-top: 0px;

	.module-name {
		color: $branco;
		margin-right: 24px;
		overflow: hidden;
		text-decoration: none;
		@extend .type-h6-bold;

		&:hover {
			text-decoration: none;
		}
	}
}

.menus {
	overflow: auto;
}

.menu {
	min-height: 43px;
	max-height: 61px;
	padding: 8px 7px 8px 21px;
	border-bottom: 1px solid $canetaBic06;
	display: flex;
	align-items: center;
	cursor: pointer;
	position: relative;
	text-decoration: none;
	transition: background-color 0.4s ease-out;

	&:hover {
		background-color: $azulPacto06;

		.menu-name {
			color: #5f9bc4;
		}
	}

	.submenu-indicator {
		color: $canetaBicPri;
	}

	.menu-name {
		flex-grow: 1;
		font-family: "Nunito Sans", sans-serif;
		color: $canetaBic03;
		font-size: 14px;
		font-weight: 600;
		padding-right: 4px;
		margin-left: -8px;
		margin-top: 2px;
		margin-bottom: 2px;
		margin-right: -4px;
	}

	.active-marker {
		position: absolute;
		left: 0px;
		top: 0px;
		bottom: 0px;
		background-color: $azulim01;
		opacity: 0;
		width: 4px;
		border-radius: 0px 3px 3px 0px;

		&.active {
			opacity: 1;
		}
	}
}

.rodape {
	border-top: 1px solid $canetaBic06;
	padding: 16px 20px 26px;
	color: $cinzaPri;
	margin-top: auto;
	@extend .type-caption;

	.system-stat {
		line-height: 22px;
		font-size: 12px;

		i {
			padding-right: 4px;
		}
	}

	.language {
		text-transform: uppercase;
	}
}

/* HD */

@media (max-height: 720px) {
	.modulo {
		line-height: 46px;
	}
	.menu-toggle {
		height: 26px;
		font-size: 18px;
		top: 97px;
	}
}
