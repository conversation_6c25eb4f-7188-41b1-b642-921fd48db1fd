import { DialogService } from "./../../../../dialog/service/dialog.service";

import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	LOCALE_ID,
	ChangeDetectorRef,
	Output,
	EventEmitter,
	OnChanges,
	Inject,
	OnDestroy,
} from "@angular/core";

import { Observable, of, Subscription, zip } from "rxjs";
import { tap, switchMap } from "rxjs/operators";

import { PlataformaMenuV2Config } from "../../model";
import {
	PlataformaNavigationService,
	PlataformaNavigationItem,
	PlataformaModuleSummary,
} from "../../plataforma-navigation.service";

export interface PlataformNavegacao {
	id: number;
	hasSubmenus?: boolean;
	name: string;
	route?: string;
	routeType?: PlataformProjectType;
	popUpWindow?: boolean;
}

export enum PlataformProjectType {
	JSF = "JSF",
	ANGULAR = "ANGULAR",
}

@Component({
	selector: "pacto-cat-menu-contextual",
	templateUrl: "./cat-menu-contextual.component.html",
	styleUrls: ["./cat-menu-contextual.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatMenuContextualComponent
	implements OnInit, OnChanges, OnDestroy
{
	@Input() configuracao: PlataformaMenuV2Config;
	@Input() navigationPath: string[] = [];
	@Input() moduloId: string;
	@Output() navigation: EventEmitter<string> = new EventEmitter();
	@Output() changeUnit: EventEmitter<any> = new EventEmitter();

	constructor(
		@Inject(LOCALE_ID) public locale,
		private navigationService: PlataformaNavigationService,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef
	) {}

	depth: number;
	currentModule: PlataformaModuleSummary;
	contextItems: PlataformaNavigationItem[];
	parentMenu: PlataformaNavigationItem;
	navigationHistory: string[];
	private toggleUpdateSubscription: Subscription;
	private moduleHasChangedSubscription: Subscription;
	dataHoje: Date = new Date();
	intervalId;
	recursosMenu;

	get menuOpen() {
		return this.navigationService.navigationMenuOpen$.value;
	}

	get enableNavigation() {
		return this.navigationService;
	}

	get currentNavigationId() {
		if (this.navigationPath && this.navigationPath.length) {
			return this.navigationPath[this.depth];
		}
	}

	get lang() {
		return this.locale ? this.locale : "pt";
	}

	get companyLogo() {
		if (this.configuracao && this.configuracao.avatarRedeUrl) {
			return `url(${this.configuracao.avatarRedeUrl})`;
		} else {
			return null;
		}
	}

	get infoIp() {
		if (this.configuracao && this.configuracao.ip) {
			return this.configuracao.ip;
		} else {
			return null;
		}
	}

	get infoVerao() {
		if (this.configuracao && this.configuracao.versao) {
			return this.configuracao.versao;
		} else {
			return null;
		}
	}

	toggleEnter() {
		if (this.navigationService.isSmallScreen() && !this.menuOpen) {
			this.menuToggleHandle();
		}
	}

	menuToggleHandle() {
		this.dialogService.emitFlagLayout(!this.menuOpen);
		this.navigationService.navigationMenuOpen$.next(!this.menuOpen);
	}

	voltarHandler() {
		this.depth = this.depth - 1;
		this.navigationHistory.pop();
		let target = this.navigationHistory[this.navigationHistory.length - 1];
		target = target ? target : this.moduloId;

		zip(
			this.navigationService.getMenu(target, this.moduloId),
			this.navigationService.getSubmenus(target, this.moduloId)
		)
			.pipe(
				tap((result) => {
					this.parentMenu = result[0];
					this.contextItems = result[1];
				})
			)
			.subscribe(() => {
				this.cd.detectChanges();
			});
	}

	ngOnInit() {
		this.toggleUpdateSubscription =
			this.navigationService.navigationMenuOpen$.subscribe(() => {
				this.cd.detectChanges();
			});
		this.moduleHasChangedSubscription =
			this.navigationService.moduleHasChanged$.subscribe((val) => {
				if (val.moduleSummary) {
					if (!val.moduleSummary.onlyMakeRedirect) {
						if (val.moduleSummary.onlyLoadMenu) {
							this.moduloId = val.moduleSummary.id;
							this.navigationService.currentModuleId = this.moduloId;
						}
					}
					this.initializeState().subscribe(() => {
						// solves ViewDestroyedError
						if (!this.cd["destroyed"]) {
							this.cd.detectChanges();
						}
					});
					this.updateCurrentModule();
				}
			});
		this.intervalId = setInterval(() => {
			this.dataHoje = new Date();
			this.cd.detectChanges();
		}, 1000);
	}

	ngOnDestroy() {
		this.toggleUpdateSubscription.unsubscribe();
		this.moduleHasChangedSubscription.unsubscribe();
		clearInterval(this.intervalId);
	}

	ngOnChanges() {
		if (this.navigationService) {
			this.initializeState().subscribe(() => {
				// solves ViewDestroyedError
				if (!this.cd["destroyed"]) {
					this.cd.detectChanges();
				}
			});
		}
		this.updateCurrentModule();
	}

	menuClickHandler(item: PlataformaNavigationItem) {
		if (
			this.navigationService &&
			this.navigationService.notificateResourceChange
		) {
			this.navigationService.notificateResourceChange(item.id);
		}
		sessionStorage.removeItem("plano");
		this.navigation.emit(item.id);
		if (item.submenus) {
			this.depth = this.depth + 1;
			this.navigationHistory.push(item.id);

			zip(
				this.navigationService.getMenu(item.id, this.moduloId),
				this.navigationService.getSubmenus(item.id, this.moduloId)
			)
				.pipe(
					tap((result) => {
						this.parentMenu = result[0];
						this.contextItems = result[1];
					})
				)
				.subscribe(() => {
					this.cd.detectChanges();
				});
		}
	}

	private updateCurrentModule() {
		this.navigationService.getModulesSummary().subscribe((modules) => {
			if (modules) {
				this.currentModule = modules.find((i) => i.id === this.moduloId);
			}
			// solves ViewDestroyedError
			if (!this.cd["destroyed"]) {
				this.cd.detectChanges();
			}
		});
	}

	/**
	 * Sets up:
	 *
	 * > depth
	 * > contextItems
	 * > parentMenu
	 * > navigationHistory
	 */
	private initializeState(): Observable<any> {
		if (!this.navigationPath) {
			this.navigationPath = [];
		}

		// zero
		if (!this.navigationPath.length) {
			this.depth = 0;
			this.contextItems = [];
			this.parentMenu = null;
			this.navigationHistory = [];
			return this.navigationService
				.getSubmenus(this.moduloId, this.moduloId)
				.pipe(tap((menus) => (this.contextItems = menus)));
		} else {
			return this.navigationService
				.getMenus(this.navigationPath, this.moduloId)
				.pipe(
					switchMap((menus) => {
						const last = menus[menus.length - 1];
						if (last.submenus) {
							this.depth = menus.length;
							this.parentMenu = last;
							this.navigationHistory = menus.map((i) => i.id);
						} else if (menus.length >= 2) {
							this.depth = menus.length - 1;
							this.parentMenu = menus[menus.length - 2];
							this.navigationHistory = menus.slice(0, -1).map((i) => i.id);
						} else {
							this.depth = menus.length - 1;
							this.parentMenu = null;
							this.navigationHistory = [];
						}
						const contextMenuId = this.parentMenu
							? this.parentMenu.id
							: this.moduloId;
						return this.navigationService
							.getSubmenus(contextMenuId, this.moduloId)
							.pipe(
								tap((submenus) => {
									this.contextItems = submenus;
								})
							);
					})
				);
		}
	}
}
