<div class="blue-triangle"></div>
<div class="blue-container">
	<span>Ajuda</span>
	<i (click)="closeModal()" class="pct pct-x"></i>
</div>

<div class="ponto-interrogacao">
	<textarea class="fake-input"></textarea>

	<div *ngFor="let v of conhecimento?.videos" class="video">
		<div>
			<img
				(click)="selecionarVideo(v)"
				[src]="getThumbnail(v)"
				class="thumb-yt" />
		</div>
		<div (click)="selecionarVideo(v)">
			<span class="title">{{ v.titulo }}</span>
			<span class="subtitle">{{ v.subtitulo }}</span>
		</div>
	</div>

	<div *ngIf="false" class="aprendizado">
		<div class="title">
			<span>Aprendizado guiado</span>
			<i class="pct pct-play-circle"></i>
		</div>
		<span>Faça um tour pelos recursos fundamentais desta tela.</span>
	</div>

	<div
		*ngIf="conhecimento?.linksArtigos && conhecimento?.linksArtigos?.length > 0"
		[ngClass]="{
			'tem-curso':
				conhecimento?.linksCursos && conhecimento?.linksCursos?.length > 0
		}"
		class="aprendizado">
		<div class="title">
			<span>Artigos recomendados</span>
		</div>
		<ng-container
			*ngFor="let l of conhecimento?.linksArtigos; let index = index">
			<div
				(click)="selecionarLink(l)"
				[ngClass]="{ interno: l.interno }"
				class="link">
				{{ index + 1 }}.
				<a>{{ l.titulo }}</a>
				<span *ngIf="l.interno" class="texto-interno">[Interno]</span>
			</div>
		</ng-container>
		<div
			(click)="verMais()"
			*ngIf="
				!conhecimento?.linksCursos || conhecimento?.linksCursos?.length == 0
			"
			class="link vermais">
			<a>Ver mais</a>
		</div>
	</div>

	<div
		*ngIf="conhecimento?.linksCursos && conhecimento?.linksCursos?.length > 0"
		class="aprendizado">
		<div class="title">
			<span>Certificações & Cursos</span>
		</div>
		<ng-container
			*ngFor="let l of conhecimento?.linksCursos; let index = index">
			<div
				(click)="selecionarLink(l)"
				[ngClass]="{ interno: l.interno }"
				class="link">
				{{ index + 1 }}.
				<a>{{ l.titulo }}</a>
				<span *ngIf="l.interno" class="texto-interno">[Interno]</span>
			</div>
		</ng-container>
		<div (click)="verMais()" class="link vermais"><a>Ver mais</a></div>
	</div>

	<div class="aprendizado">
		<div class="title">
			<span>Ainda precisa de ajuda?</span>
		</div>
		<div (click)="ajuda()" class="link icone">
			<i class="pct pct-book"></i>
			<a>Encontre respostas na Central de Ajuda</a>
		</div>
		<div (click)="chat()" class="link icone" id="link-chat-mov">
			<i class="pct pct-message-square"></i>
			<a>Fale com um atendente via Chat</a>
		</div>
		<div (click)="suporte()" class="link icone">
			<i class="pct pct-telemarketing"></i>
			<a>Abra um ticket de suporte</a>
		</div>
	</div>

	<div class="aprendizado util">
		<div class="title">
			<span>Isso foi útil pra você?</span>
		</div>
		<div class="title">
			<span [ngClass]="{ desmarcado: !sim && nao }" class="sim_nao">
				<span (click)="util()">Sim</span>
				<i (click)="util()" class="pct pct-thumbs-up"></i>
			</span>
			<span [ngClass]="{ desmarcado: sim && !nao }" class="sim_nao">
				<span (click)="inutil()">Não</span>
				<i (click)="inutil()" class="pct pct-thumbs-down"></i>
			</span>
		</div>
		<textarea
			[formControl]="comentarioFc"
			class="form-control form-control-sm"
			name="name"
			placeholder="Deixe seu feedback sobre o conteúdo deste Ponto de Interrogação (você não está abrindo suporte ou falando com um atendente aqui, apenas deixando feedback sobre o conteúdo deste recurso/tela)"
			rows="5"></textarea>

		<div class="acao">
			<pacto-cat-button
				(click)="avaliacao()"
				[disabled]="disabledBtnEnviar"
				id="enviar-util"
				label="Enviar"></pacto-cat-button>
		</div>
	</div>
</div>

<div *ngIf="videoAberto" class="videoAberto">
	<iframe
		allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
		allowfullscreen
		frameborder="0"
		height="315"
		src="{{ urlVideoAberto }}"
		title="YouTube video player"
		width="560"></iframe>
</div>
