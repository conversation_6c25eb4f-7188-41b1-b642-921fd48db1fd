@import "projects/ui/assets/import.scss";

.blue-triangle {
	margin-top: -8px;
	width: 0;
	height: 0;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-bottom: 8px solid $azulPacto02;
	margin-left: 355px;
}

.blue-container {
	width: 100%;
	height: 40px;
	line-height: 40px;
	color: $branco;
	font-size: 16px;
	background-color: $azulPacto02;
	display: flex;
	justify-content: space-between;
	padding: 0 16px;

	i {
		margin-top: 12px;
		cursor: pointer;
	}
}

.ponto-interrogacao {
	.fake-input {
		width: 0px;
		height: 0px;
		opacity: 0;
	}

	overflow-y: auto;
	scrollbar-width: none;
	color: #38383e;
	height: 619px;
	background-color: #ffffff;
	width: 452px;
	padding: 16px;

	.video {
		cursor: pointer;
		display: flex;
		margin-bottom: 16px;

		img {
			width: 180px;
			border-radius: 4px;
		}

		div {
			margin-left: 12px;

			.subtitle {
				font-size: 14px;
				font-weight: 300;
				line-height: 21px;
			}

			.title {
				font-size: 16px;
				font-weight: 600;
				line-height: 24px;
				margin-bottom: 8px;
				display: block;
			}
		}
	}

	.aprendizado {
		&.util {
			border-bottom: none;

			.title {
				display: block;
				text-align: center;

				.sim_nao {
					margin-right: 10px;

					&.desmarcado {
						opacity: 0.5;
					}
				}

				span,
				i {
					cursor: pointer;
				}

				.pct-thumbs-down {
					color: #9a1b27;
				}

				.pct-thumbs-up {
					color: #026abc;
					margin-right: 16px;
				}
			}
		}

		border-bottom: 1px solid #c7c9cc;
		padding: 16px 0;
		font-size: 14px;
		font-weight: 400;
		line-height: 14px;

		.link {
			&.icone {
				display: flex;
				font-size: 16px;
				font-weight: 400;
				line-height: 16px;
				color: #204c77;

				i {
					margin-right: 8px;
				}

				a {
					text-decoration-line: none;
				}
			}

			line-height: 21px;
			margin-bottom: 12px;

			&.vermais {
				font-weight: 600;
				margin-top: 8px;
				margin-bottom: 0;
			}

			a {
				color: #204c77;
				text-decoration-line: underline;
				cursor: pointer;
			}
		}

		.title {
			margin-bottom: 14px;
			display: flex;
			font-size: 16px;
			font-weight: 600;
			line-height: 16px;

			i {
				margin-left: 8px;
			}
		}

		pacto-textarea::ng-deep {
			.form-group {
				margin-bottom: 0;
			}
		}

		.acao {
			margin-top: 16px;
			display: block;
			text-align: right;
		}
	}
}

.ponto-interrogacao::-webkit-scrollbar-track {
	background: #ffffff;
}

.ponto-interrogacao::-webkit-scrollbar-thumb {
	background-color: #c7c9cc;
	border-radius: 10px;
	border: 3px solid #ffffff;
}

.ponto-interrogacao::-webkit-scrollbar {
	width: 12px;
}

.ponto-interrogacao {
	scrollbar-width: auto;
	scrollbar-color: #c7c9cc #ffffff;
}
