import { ChangeDetectorRef, Component, OnInit, Optional } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ActivatedRoute } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { PlataformaConfigService } from "../../plataforma-config.service";
import { PlataformaMenuV2Config } from "../../model";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { MovideskChatService } from "sdk";

@Component({
	selector: "pacto-cat-ajuda",
	templateUrl: "./cat-ajuda.component.html",
	styleUrls: ["./cat-ajuda.component.scss"],
})
export class CatAjudaComponent implements OnInit {
	videoAberto = false;
	urlVideoAberto;
	conhecimento;
	configuracao: PlataformaMenuV2Config;
	sim = false;
	nao = false;
	comentarioFc: FormControl = new FormControl();
	disabledBtnEnviar = true;

	constructor(
		@Optional() private configService: PlataformaConfigService,
		private activeModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private http: HttpClient,
		private movideskChatService: MovideskChatService,
		private snotifyService: SnotifyService,
		private activatedRoute: ActivatedRoute
	) {}

	ngOnInit() {
		this.initConhecimento();
		if (this.configService) {
			this.configService.getConfig().subscribe((config) => {
				this.configuracao = config;
			});
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
	}

	verMais() {
		this.utilizacao("VER_MAIS", null, null);
		if (
			this.conhecimento &&
			this.conhecimento.verMaisLink &&
			this.conhecimento.verMaisLink.startsWith("http")
		) {
			window.open(this.conhecimento.verMaisLink, "_blank");
		} else {
			window.open(
				"https://pactosolucoes.com.br/ajuda/search/" +
					this.conhecimento.verMaisLink,
				"_blank"
			);
		}
	}

	ajuda() {
		this.utilizacao("CENTRAL_AJUDA", null, null);
		window.open("https://pactosolucoes.com.br/ajuda/", "_blank");
	}

	chat() {
		const idchat = localStorage.getItem("movidesk-idchage");
		if (idchat) {
			this.movideskChatService.initMovidesk(idchat, true);
			this.closeModal();
		}
	}

	suporte() {
		this.utilizacao("SUPORTE", null, null);
		window.open(
			"https://pactosolucoes.movidesk.com/Account/Login?ReturnUrl=%2f",
			"_blank"
		);
	}

	selecionarLink(link) {
		this.utilizacao("LINK", null, link);
		window.open(link.url, "_blank");
	}

	selecionarVideo(video) {
		this.utilizacao("VIDEO", video, null);
		let url = video.url;
		if (url.includes("vimeo")) {
			url = "https://player.vimeo.com/video/" + this.getVimeoID(url);
		} else {
			url = "https://www.youtube.com/embed/" + this.getYoutubeID(url);
		}
		this.activeModal.close(url);
		this.cd.detectChanges();
	}

	closeModal() {
		this.activeModal.close();
		this.cd.detectChanges();
	}

	initConhecimento() {
		try {
			// passando usuario vazio é o mesmo que mandar um usuário sem ser pactobr;
			// conteúdo interno deve ser apresentado somente para pactobr
			const url =
				"https://ms1.pactosolucoes.com.br/pontointerrogacao/pagina?id=" +
				this.activatedRoute.snapshot["_routerState"].url.replace("/", "") +
				"&usuario=";
			this.http.get(url).subscribe((json: any) => {
				this.conhecimento = json.content;
			});
		} catch (e) {
			console.log(e);
		}
	}

	utilizacao(tipo, video, link) {
		try {
			if (this.configuracao.key) {
				const url = "https://ms1.pactosolucoes.com.br/pontointerrogacao/acao";
				this.http
					.post(url, {
						username: this.configuracao.colaboradorNomeSimples,
						chave: this.configuracao.key,
						codigoUsuario: this.configuracao.codigoUsuarioZw,
						nomeEmpresa: this.configuracao.nomeUnidade,
						empresa: this.configuracao.empresa,
						conhecimento: this.conhecimento ? this.conhecimento.cod : null,
						link: link ? link.cod : null,
						video: video ? video.cod : null,
						tipo,
					})
					.subscribe(() => {});
			}
		} catch (e) {
			console.log(e);
		}
	}

	avaliacao() {
		if (this.sim || this.nao) {
			try {
				if (this.configuracao.key) {
					const url =
						"https://ms1.pactosolucoes.com.br/pontointerrogacao/avaliacao";
					this.http
						.post(url, {
							username: this.configuracao.colaboradorNomeSimples,
							chave: this.configuracao.key,
							codigoUsuario: this.configuracao.codigoUsuarioZw,
							nomeEmpresa: this.configuracao.nomeUnidade,
							empresa: this.configuracao.empresa,
							conhecimento: this.conhecimento ? this.conhecimento.cod : null,
							tela: this.activatedRoute.snapshot["_routerState"].url.replace(
								"/",
								""
							),
							sim: this.sim,
							nao: this.nao,
							comentario: this.comentarioFc.value,
						})
						.subscribe(() => {
							this.snotifyService.success("Obrigado pela sua avaliação!");
							this.comentarioFc.reset("");
							this.sim = false;
							this.nao = false;
							this.disabledBtnEnviar = true;
							this.closeModal();
						});
				}
			} catch (e) {
				console.log(e);
			}
		} else {
			this.snotifyService.warning(
				"Para enviar sua avaliação é necessário informar se foi útil ou não!"
			);
		}
	}

	getThumbnail(video): string {
		if (video.url.includes("vimeo")) {
			return video.thumbnail;
		} else {
			return `https://i.ytimg.com/vi/${this.getYoutubeID(
				video.url
			)}/hqdefault.jpg`;
		}
	}

	getYoutubeID(url): string {
		try {
			const YoutubeRegexObject_v1 =
				/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i;
			const YoutubeVideoID = url.match(YoutubeRegexObject_v1);
			return YoutubeVideoID[1];
		} catch (e) {
			return url;
		}
	}

	getVimeoID(url): string {
		try {
			if (url.includes("manage")) {
				const split = url.split("videos/");
				console.log(split);
				let id = split[1];
				id = id.replace("/", "?h=");
				console.log(id);
				return id;
			} else {
				const match = /vimeo.*\/(\d+)/i.exec(url);
				return match[1];
			}
		} catch (e) {
			return url;
		}
	}

	util() {
		this.sim = true;
		this.nao = false;
		this.disabledBtnEnviar = false;
		this.cd.detectChanges();
	}

	inutil() {
		this.sim = false;
		this.nao = true;
		this.disabledBtnEnviar = false;
		this.cd.detectChanges();
	}
}
