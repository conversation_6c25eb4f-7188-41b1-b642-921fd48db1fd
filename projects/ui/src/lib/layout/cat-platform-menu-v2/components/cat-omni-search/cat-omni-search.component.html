<i class="pct pct-search"></i>
<input [formControl]="searchFc" id="omni-search-input" type="text" />

<div *ngIf="open" class="aux"></div>
<div *ngIf="open" class="search-results">
	<ng-container>
		<ng-container *ngIf="resultNav && resultNav.length">
			<div class="result-section-name">Funções</div>
			<div class="result-list">
				<ng-container *ngFor="let moduleResult of resultNav">
					<a
						(click)="
							funcionalidadeClickHandler();
							loadMenu(funcao, moduleResult.moduleId)
						"
						*ngFor="let funcao of moduleResult.matches"
						[id]="'search-funcao-' + funcao.id"
						[queryParams]="funcao.queryParams"
						[routerLink]="funcao.link"
						class="funcao">
						<img src="{{ funcao.logoUri }}" />
						<div class="detalhes">
							<div class="nome">
								{{
									"menu.nav." +
										moduleResult.moduleId +
										"." +
										funcao.id +
										".name" | translate
								}}
							</div>
							<div class="explicacao">
								{{
									"menu.nav." +
										moduleResult.moduleId +
										"." +
										funcao.id +
										".description" | translate
								}}
							</div>
						</div>
					</a>
				</ng-container>
			</div>
		</ng-container>
		<ng-container *ngIf="resultPeople && resultPeople.length">
			<div class="result-section-name">Clientes</div>
			<div *ngIf="resultPeople && resultPeople.length" class="result-list">
				<a
					(click)="clienteClickHandler()"
					*ngFor="let cliente of resultPeople"
					[id]="'search-cliente-' + cliente.id"
					[routerLink]="cliente.url"
					class="cliente">
					<img src="{{ cliente.avatarUri }}" />
					<div class="nome">{{ cliente.nome }}</div>
				</a>
			</div>
		</ng-container>
		<div *ngIf="!hasResults" class="empty">
			<div>Sem resultado...</div>
		</div>
	</ng-container>
</div>
