import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Optional,
	ChangeDetectorRef,
	ElementRef,
	<PERSON><PERSON><PERSON><PERSON>,
	HostListener,
	Input,
} from "@angular/core";
import { FormControl } from "@angular/forms";

import { debounceTime } from "rxjs/operators";
import { of, Subscription } from "rxjs";

import {
	PlataformaPersonSearchResult,
	PlataformaSearchService,
} from "../../plataforma-search.service";
import {
	PlataformaNavigationService,
	PlataformaNavigationItem,
	PlataformaModuleSummary,
} from "../../plataforma-navigation.service";

@Component({
	selector: "pacto-cat-omni-search",
	templateUrl: "./cat-omni-search.component.html",
	styleUrls: ["./cat-omni-search.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatOmniSearchComponent implements OnInit, On<PERSON><PERSON>roy {
	@Input() moduleId: string;

	constructor(
		@Optional() private searchService: PlataformaSearchService,
		private navigationService: PlataformaNavigationService,
		private ref: ElementRef,
		private cd: ChangeDetectorRef
	) {}

	searchFc: FormControl = new FormControl();

	open = false;
	resultPeople: PlataformaPersonSearchResult[];
	resultNav: {
		moduleId: string;
		matches: PlataformaNavigationItem[];
	}[] = [];

	private peopleSearchSub: Subscription;
	private navSearchSub: Subscription;
	private searchSub: Subscription;

	@HostListener("document:click", ["$event"])
	public clickHandler($event: MouseEvent) {
		const innerClick = this.isDescendant(this.ref.nativeElement, $event.target);
		if (!innerClick) {
			this.open = false;
			this.cd.detectChanges();
		}
	}

	ngOnInit() {
		this.setupEvents();
	}

	ngOnDestroy() {
		if (this.peopleSearchSub) {
			this.peopleSearchSub.unsubscribe();
		}
		if (this.navSearchSub) {
			this.navSearchSub.unsubscribe();
		}
		this.searchSub.unsubscribe();
	}

	clienteClickHandler() {
		this.searchFc.setValue(null, { emitEvent: false });
		this.open = false;
		this.cd.detectChanges();
	}

	funcionalidadeClickHandler() {
		this.searchFc.setValue(null, { emitEvent: false });
		this.open = false;
		this.cd.detectChanges();
	}

	get hasResults() {
		const people = this.resultPeople && this.resultPeople.length;
		const functions = this.resultNav && this.resultNav.length;
		return people || functions;
	}

	private setupEvents() {
		this.searchSub = this.searchFc.valueChanges
			.pipe(debounceTime(300))
			.subscribe((value) => this.search(value));
	}

	private search(term) {
		if (term && term.trim()) {
			this.open = true;
			this.resultNav = [];
			this.resultPeople = [];
			this.cd.detectChanges();

			if (this.peopleSearchSub) {
				this.peopleSearchSub.unsubscribe();
			}
			if (this.navSearchSub) {
				this.navSearchSub.unsubscribe();
			}

			// People
			if (this.searchService) {
				this.peopleSearchSub = this.searchService
					.searchPeople(term.trim())
					.subscribe((result) => {
						this.resultPeople = result;
						this.cd.detectChanges();
					});
			}

			// Navigation items
			this.navSearchSub = this.navigationService
				.search(term.trim())
				.subscribe((items) => {
					this.resultNav = items;
					this.cd.detectChanges();
				});
		}
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	loadMenu(icon: PlataformaModuleSummary, moduleId: string) {
		if (
			!icon.linkAsHref &&
			(!icon.link || !icon.link.includes("redirectADM"))
		) {
			this.navigationService.moduleHasChanged$.next({ moduleSummary: icon });
		}
	}
}
