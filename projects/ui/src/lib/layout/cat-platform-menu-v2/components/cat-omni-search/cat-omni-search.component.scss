@import "projects/ui/assets/import.scss";

:host {
	width: 100%;
	display: block;
	position: relative;
}

i.pct-search {
	position: absolute;
	left: 0px;
	top: 6px;
	color: #a1a5aa;
	font-size: 20px;
}

input {
	width: 100%;
	border: 0px;
	@extend .type-p-rounded;
	color: $cinza05;
	padding: 6px 30px;

	&:focus {
		outline: none;
		border-bottom: 1px solid $azulimPri;
	}

	border-bottom: 1px solid $cinza03;
}

.aux {
	position: absolute;
	left: 0px;
	right: 0px;
	height: 10px;
	background-color: $branco;
	top: 55px;
	z-index: 2;
}

.search-results {
	background-color: $branco;
	padding: 14px 0px 14px;
	box-shadow: 0px 2px 4px 3px #dcdcdca6;
	max-height: 500px;
	overflow-y: auto;
	position: absolute;
	left: 0px;
	right: 0px;
	z-index: 10;
	top: 61px;
}

.empty {
	width: calc(100% - 48px);
	height: 60px;
	display: flex;
	background-color: $cinzaPastel;
	@extend .type-p-small;
	color: $cinza03;
	justify-content: center;
	align-items: center;
	margin: 8px 24px 0px;
}

.result-section-name {
	color: $cinza05;
	@extend .type-h6;
	padding: 20px 24px 0px;

	&:first-of-type {
		padding-top: 0px;
	}
}

.funcao {
	display: flex;
	cursor: pointer;
	padding: 10px 20px;
	text-decoration: none;

	&:hover {
		background-color: #eff2f7;
	}

	img {
		width: 32px;
		height: 32px;
		border-radius: 16px;
		margin-right: 12px;
	}

	.nome {
		@extend .type-h6;
		color: $pretoPri;
		line-height: 1em;
	}

	.explicacao {
		@extend .type-caption;
		color: $cinzaPri;
	}
}

.cliente {
	align-items: center;
	display: flex;
	cursor: pointer;
	padding: 10px 24px;
	text-decoration: none;

	&:hover {
		background-color: #eff2f7;
	}

	img {
		width: 32px;
		height: 32px;
		border-radius: 16px;
		margin-right: 12px;
	}

	.nome {
		@extend .type-h6;
		color: $pretoPri;
	}
}
