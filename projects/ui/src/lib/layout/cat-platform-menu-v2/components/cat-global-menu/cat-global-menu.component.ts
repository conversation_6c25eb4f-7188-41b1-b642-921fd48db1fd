import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnInit,
	Optional,
	Output,
} from "@angular/core";

import { PlataformaMenuV2Config } from "../../model";
import {
	PlataformaModuleSummary,
	PlataformaNavigationItem,
	PlataformaNavigationService,
} from "../../plataforma-navigation.service";

import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import { Observable } from "rxjs";
import { distinctUntilChanged, share } from "rxjs/operators";

@Component({
	selector: "pacto-cat-global-menu",
	templateUrl: "./cat-global-menu.component.html",
	styleUrls: ["./cat-global-menu.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	animations: [
		trigger("openClose", [
			state(
				"open",
				style({
					height: "calc(100vh - 160px)",
					"padding-bottom": "16px",
				})
			),
			state(
				"closed",
				style({
					height: "0px",
					"padding-bottom": "0px",
				})
			),
			transition("open => closed", [animate("0.2s")]),
			transition("closed => open", [animate("0.2s")]),
		]),
	],
})
export class CatGlobalMenuComponent implements OnInit {
	@Input() moduloId: string;
	@Input() configuracao: PlataformaMenuV2Config;
	@Output() navigation: EventEmitter<string> = new EventEmitter();

	modules$: Observable<PlataformaModuleSummary[]>;
	navigationMenus: PlataformaNavigationItem[][] = [];
	dropdownOpen = false;
	integracaoZW = false;

	constructor(
		private ref: ElementRef,
		private navService: PlataformaNavigationService,
		private cd: ChangeDetectorRef,
		@Optional() private navigationService: PlataformaNavigationService
	) {}

	@HostListener("document:click", ["$event"])
	public clickHandler($event: MouseEvent) {
		const innerClick = this.isDescendant(this.ref.nativeElement, $event.target);
		if (!innerClick) {
			this.dropdownOpen = false;
		}
	}

	toggleDropdownHandler() {
		if (this.dropdownOpen) {
			this.dropdownOpen = false;
		} else {
			this.dropdownOpen = true;
			this.resetMenus();
		}
	}

	moduleClickHandler($event: MouseEvent) {
		$event.stopPropagation();
	}

	get enableNavigation() {
		return this.navigationService;
	}

	get depth() {
		return (this.navigationMenus && this.navigationMenus.length) || 0;
	}

	ngOnInit() {
		this.resetMenus();
		this.modules$ = this.navService.getModulesSummary().pipe(
			share(),
			distinctUntilChanged(
				(prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)
			)
		);
	}

	funcionalidadeHandler(
		index: number,
		funcionalidade: PlataformaNavigationItem
	) {
		this.navigation.emit(funcionalidade.id);
		if (funcionalidade.submenus) {
			this.navigationMenus = this.navigationMenus.slice(0, index + 1);
			this.navigationService
				.getSubmenus(funcionalidade.id, this.moduloId)
				.subscribe((menus) => {
					this.navigationMenus.push(menus);
					this.cd.detectChanges();
				});
		} else {
			this.dropdownOpen = false;
			this.cd.detectChanges();
		}
	}

	adaptaModulo(moduloId) {
		return moduloId.split("_")[0].toString();
	}

	goBackHandler($event: MouseEvent) {
		$event.stopPropagation();
		this.navigationMenus.pop();
	}

	private resetMenus() {
		if (!this.navigationService) {
			return false;
		}
		this.navigationService
			.getSubmenus(this.moduloId, this.moduloId)
			.subscribe((menus) => {
				this.navigationMenus = [menus];
				if (!this.cd["destroyed"]) {
					this.cd.detectChanges();
				}
			});
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	loadMenu(icon: PlataformaModuleSummary) {
		if (
			!icon.linkAsHref &&
			(!icon.link || !icon.link.includes("redirectADM"))
		) {
			this.moduloId = icon.id;
			this.resetMenus();
			this.navService.moduleHasChanged$.next({ moduleSummary: icon });
		}
	}
}
