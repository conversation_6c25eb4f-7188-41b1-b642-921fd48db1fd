@import "projects/ui/assets/import.scss";

:host {
	display: block;
	margin: 0 60px 0 20px;
	z-index: 10;
}

.modulo-opener {
	display: flex;
	align-items: center;
	@extend .type-p-small;
	color: $cinza05;
	cursor: pointer;
	font-size: 14px;

	.title-modulo-opener {
		font-weight: 900;
		font-size: 14px;
		line-height: 100%;
		color: $azulPactoPri;
	}

	i {
		color: $azulPactoPri;
		padding-right: 14px;
		font-size: 24px;
	}
}

.aux {
	// @include plataformaV2LarguraConteudo();
	max-width: 1920px;
	width: 100%;
	padding: 0px 16px;
	grid-template-columns: 0fr 2fr;
	display: grid;
	gap: 30px;
}

.global-menu-dropdown {
	display: flex;
	background-color: $branco;
	box-shadow: 2px 1px 2px 2px #9e9e9e4d;
	overflow-y: auto;
	position: absolute;
	top: 88px;
	left: 9px;
	right: 0px;
	margin: 0 0 0 25px;
	justify-content: flex-start;
	width: calc(100vw - 115px);

	/* width */
	&::-webkit-scrollbar {
		width: 8px;
		height: 6px;
		margin-right: 2px;
	}

	/* Track */
	&::-webkit-scrollbar-track {
		background: #f1f1f1;
	}

	/* Handle */
	&::-webkit-scrollbar-thumb {
		background: #ccc;
	}

	/* Handle on hover */
	&::-webkit-scrollbar-thumb:hover {
		background: #555;
	}
}

.section-title {
	color: $pretoPri;
	font-weight: 700;
	margin-bottom: 5px;
	@extend .type-h6;
}

.modulos-disponiveis {
	margin-bottom: 5px;
}

.modulo {
	display: flex;
	align-items: center;
	padding: 12px;
	margin: 16px 0 0 0;
	border-radius: 3px 0 0 3px;
	cursor: pointer;
	background-color: #fafafa;
	text-decoration: none;
	width: 244px;

	&.current {
		border-left: 4px solid $azulPactoPri;

		.nome {
			color: $pretoPri;
		}
	}

	&:hover {
		background-color: $cinza03;

		.nome {
			color: $pretoPri;
		}
	}

	.nome {
		color: $pretoPri;
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: 400;
	}

	.img-aux {
		width: 32px;
		height: 32px;
		border-radius: 16px;
		margin-right: 16px;
	}

	img {
		width: 32px;
		height: 32px;
	}
}

.funcionalidades {
	margin-bottom: 20px;
	margin-top: 20px;
	display: flex;

	.section-title {
		padding-left: 15px;
	}
}

.funcionalidade-list {
	flex-basis: 50%;
	overflow: hidden;
	display: none;

	&.visible {
		display: block;
	}
}

.go-back {
	color: #b4b7bb;
	cursor: pointer;
	font-size: 16px;
	margin-bottom: 23px;
	padding-left: 15px;
	visibility: hidden;

	&.visible {
		visibility: visible;
	}
}

a.funcionalidade {
	padding: 9px 15px;
	text-decoration: none;
	position: relative;
	display: block;
	cursor: pointer;
	height: 57px;

	&:hover {
		background-color: #eff2f7;
	}

	.nome {
		@extend .type-p-small;
		color: $pretoPri;
	}

	.descricao {
		@extend .type-caption;
		color: $cinzaPri;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.submenu-indicator {
		position: absolute;
		font-size: 16px;
		right: 15px;
		top: 20px;
		color: $cinzaPri;
	}
}

@media (max-width: 580px) {
	.title-modulo-opener {
		display: none;
	}
}
