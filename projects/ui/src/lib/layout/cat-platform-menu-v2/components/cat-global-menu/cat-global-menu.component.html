<div
	(click)="toggleDropdownHandler()"
	class="modulo-opener"
	id="global-open-menu">
	<i [hidden]="!dropdownOpen" class="pct pct-x"></i>
	<i [hidden]="dropdownOpen" class="pct pct-grid"></i>
	<div class="title-modulo-opener">
		{{ "menu.global.explorar" | translate }}
	</div>
</div>

<div
	[@openClose]="dropdownOpen ? 'open' : 'closed'"
	class="global-menu-dropdown">
	<div class="aux">
		<div class="modulos-disponiveis">
			<div class="modulos-list">
				<ng-container *ngFor="let modulo of modules$ | async">
					<a
						(click)="moduleClickHandler($event); loadMenu(modulo)"
						*ngIf="!modulo.linkAsHref && modulo.showOnMenuTopbar"
						[id]="'global-menu-' + modulo.id"
						[ngClass]="{ current: adaptaModulo(modulo.id) === moduloId }"
						[queryParams]="modulo.queryParams"
						[routerLink]="modulo.link"
						class="modulo">
						<div class="img-aux">
							<img *ngIf="modulo?.imageUri" src="{{ modulo.imageUri }}" />
						</div>
						<div class="nome">
							{{ "menu.modules." + modulo.id + ".name" | translate }}
						</div>
					</a>
					<a
						(click)="moduleClickHandler($event); loadMenu(modulo)"
						*ngIf="modulo.linkAsHref && modulo.showOnMenuTopbar"
						[href]="modulo.link"
						[id]="'global-menu-' + modulo.id"
						[ngClass]="{ current: modulo.id === moduloId }"
						[target]="modulo?.openInNewTab ? '_blank' : '_self'"
						class="modulo">
						<div class="img-aux">
							<img *ngIf="modulo?.imageUri" src="{{ modulo.imageUri }}" />
						</div>
						<div class="nome">
							{{ "menu.modules." + modulo.id + ".name" | translate }}
						</div>
					</a>
				</ng-container>
			</div>
		</div>

		<div *ngIf="enableNavigation" class="funcionalidades">
			<div
				*ngFor="
					let menu of navigationMenus;
					let index = index;
					let last = last;
					let first = first
				"
				[ngClass]="{ visible: first || last }"
				class="funcionalidade-list">
				<div *ngIf="first" class="section-title">
					{{ "menu.global.funcionalidades" | translate }}
				</div>
				<div
					(click)="goBackHandler($event)"
					*ngIf="!first"
					[ngClass]="{ visible: index >= 2 }"
					class="go-back"
					id="global-menu-funcionalidade-voltar">
					<i class="pct pct-arrow-left-circle"></i>
				</div>

				<ng-container *ngFor="let funcionalidade of menu">
					<!-- COM LINK -->
					<a
						(click)="funcionalidadeHandler(index, funcionalidade)"
						*ngIf="funcionalidade.link && !funcionalidade.newTab"
						[id]="'global-menu-funcionalidade-' + funcionalidade.id"
						[queryParams]="funcionalidade.queryParams"
						[routerLink]="funcionalidade.link"
						class="funcionalidade"
						title="{{
							'menu.nav.' + moduloId + '.' + funcionalidade.id + '.description'
								| translate
						}}">
						<div class="nome">
							{{
								"menu.nav." + moduloId + "." + funcionalidade.id + ".name"
									| translate
							}}
						</div>
						<div class="descricao">
							{{
								"menu.nav." +
									moduloId +
									"." +
									funcionalidade.id +
									".description" | translate
							}}
						</div>
						<i
							*ngIf="funcionalidade.submenus"
							class="submenu-indicator pct pct-arrow-right-circle"></i>
					</a>

					<!-- COM LINK -->
					<a
						(click)="funcionalidadeHandler(index, funcionalidade)"
						*ngIf="funcionalidade.link && funcionalidade.newTab"
						[id]="'global-menu-funcionalidade-' + funcionalidade.id"
						[queryParams]="funcionalidade.queryParams"
						[routerLink]="funcionalidade.link"
						class="funcionalidade"
						target="_blank"
						title="{{
							'menu.nav.' + moduloId + '.' + funcionalidade.id + '.description'
								| translate
						}}">
						<div class="nome">
							{{
								"menu.nav." + moduloId + "." + funcionalidade.id + ".name"
									| translate
							}}
						</div>
						<div class="descricao">
							{{
								"menu.nav." +
									moduloId +
									"." +
									funcionalidade.id +
									".description" | translate
							}}
						</div>
						<i
							*ngIf="funcionalidade.submenus"
							class="submenu-indicator pct pct-arrow-right-circle"></i>
					</a>

					<!-- SEM LINK -->
					<a
						(click)="funcionalidadeHandler(index, funcionalidade)"
						*ngIf="!funcionalidade.link"
						[id]="'global-menu-funcionalidade-' + funcionalidade.id"
						class="funcionalidade"
						title="{{
							'menu.nav.' + moduloId + '.' + funcionalidade.id + '.description'
								| translate
						}}">
						<div class="nome">
							{{
								"menu.nav." + moduloId + "." + funcionalidade.id + ".name"
									| translate
							}}
						</div>
						<div class="descricao">
							{{
								"menu.nav." +
									moduloId +
									"." +
									funcionalidade.id +
									".description" | translate
							}}
						</div>
						<i
							*ngIf="funcionalidade.submenus"
							class="submenu-indicator pct pct-arrow-right-circle"></i>
					</a>
				</ng-container>
			</div>
		</div>
	</div>
</div>
