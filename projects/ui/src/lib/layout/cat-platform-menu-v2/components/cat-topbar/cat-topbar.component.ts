import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	Output,
	EventEmitter,
} from "@angular/core";
import { PlataformaMenuV2Config } from "../../model";

@Component({
	selector: "pacto-cat-topbar",
	templateUrl: "./cat-topbar.component.html",
	styleUrls: ["./cat-topbar.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatTopbarComponent implements OnInit {
	@Input() moduloId: string;
	@Input() configuracao: PlataformaMenuV2Config;
	@Output() navigation: EventEmitter<string> = new EventEmitter();
	@Output() logout: EventEmitter<any> = new EventEmitter();

	constructor() {}

	ngOnInit() {}
}
