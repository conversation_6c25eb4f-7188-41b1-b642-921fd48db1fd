@import "projects/ui/assets/import.scss";

:host {
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: $branco;
	position: relative;
	height: 88px;
	z-index: 99;
	box-shadow: 2px 1px 2px 2px #9e9e9e4d;
}

.aux {
	display: flex;
	align-items: center;
	@include plataformaV2LarguraConteudo();
}

.pacto-primary {
	padding: 0 10px;
	margin-left: 40px;
	font-size: 12px;
	box-shadow: none;
	text-transform: uppercase;
	border-radius: 4px;
	line-height: 32px;
	cursor: pointer;
	position: relative;
	background-color: #1998fc;
	border: 1px solid #1998fc;
	border-top-color: rgb(25, 152, 252);
	border-top-style: solid;
	border-top-width: 1px;
	border-right-color: rgb(25, 152, 252);
	border-right-style: solid;
	border-right-width: 1px;
	border-bottom-color: rgb(25, 152, 252);
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-left-color: rgb(25, 152, 252);
	border-left-style: solid;
	border-left-width: 1px;
	border-image-source: initial;
	border-image-slice: initial;
	border-image-width: initial;
	border-image-outset: initial;
	border-image-repeat: initial;
	color: #fff;
}

.pacto-user {
	display: block;
	position: relative;
	width: 45%;
	max-width: 650px;
	margin-left: 193px;
}

.pacto-user-aluno {
	display: block;
	position: relative;
	width: 25%;
	margin-left: 32px;
}
