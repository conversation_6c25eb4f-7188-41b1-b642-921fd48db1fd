<div class="aux">
	<pacto-cat-global-menu
		(navigation)="navigation.emit($event)"
		[configuracao]="configuracao"
		[moduloId]="moduloId"></pacto-cat-global-menu>

	<pacto-cat-omni-search [moduleId]="moduloId"></pacto-cat-omni-search>

	<a
		*ngIf="
			configuracao &&
			configuracao.independente &&
			configuracao.independente === true
		"
		[routerLink]="'/cadastros/alunos/novo'"
		class="pacto-primary"
		id="atalho-adicionar-aluno">
		<i class="pct pct-plus-circle"></i>
		<span i18n="@@plataforma-global-menu:quick-actions:alunos">Alunos</span>
	</a>

	<pacto-cat-menu-user
		(logout)="logout.emit(true)"
		[configuracao]="configuracao"
		[ngClass]="
			configuracao &&
			configuracao.independente &&
			configuracao.independente === true
				? 'pacto-user-aluno'
				: 'pacto-user'
		"></pacto-cat-menu-user>
</div>
