<div class="modules-wrapper">
	<button
		(click)="previous()"
		[attr.data-inactive]="disablePreviousButton$ | async"
		class="carousel-button"
		data-variation="previous"
		tabindex="-1">
		<i class="pct pct-chevron-up"></i>
		<span class="vh">previous</span>
	</button>
	<div
		#modulesContainer
		[class.modules-delimiter]="modulesFilter && modulesFilter.length >= 6"
		[style.height.px]="containerHeight">
		<div #modulesWrapper class="module-tracks">
			<ng-container *ngFor="let modulo of modulesFilter">
				<a
					(click)="loadMenu(modulo)"
					*ngIf="!modulo.linkAsHref"
					[id]="'taskbar-modulo-' + modulo.id"
					[isCurrent]="modulo.id === moduloId"
					[queryParams]="modulo.queryParams"
					[routerLink]="modulo.link"
					class="modulo"
					pactoModuleItem
					title="{{ 'menu.modules.' + modulo.id + '.name' | translate }}">
					<div [class.current]="modulo.id === moduloId" class="img-aux">
						<img
							*ngIf="modulo.imageUri"
							alt="{{ modulo.id }}"
							src="{{ modulo.imageUri }}" />
					</div>
				</a>
				<a
					(click)="loadMenu(modulo)"
					*ngIf="modulo.linkAsHref"
					[href]="modulo.link"
					[id]="'taskbar-modulo-' + modulo.id"
					[isCurrent]="modulo.id === moduloId"
					[target]="modulo.openInNewTab ? '_blank' : '_self'"
					class="modulo"
					pactoModuleItem
					title="{{ 'menu.modules.' + modulo.id + '.name' | translate }}">
					<div [class.current]="modulo.id === moduloId" class="img-aux">
						<img
							*ngIf="modulo.imageUri"
							alt="{{ modulo.id }}"
							src="{{ modulo.imageUri }}" />
					</div>
				</a>
			</ng-container>
		</div>
	</div>
	<button
		(click)="next()"
		[attr.data-inactive]="disableNextButton$ | async"
		class="carousel-button"
		data-variation="next"
		tabindex="-1">
		<i class="pct pct-chevron-down"></i>
		<span class="vh">next</span>
	</button>
</div>
