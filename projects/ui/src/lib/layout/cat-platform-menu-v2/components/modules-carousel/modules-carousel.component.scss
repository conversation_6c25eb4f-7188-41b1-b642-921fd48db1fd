@import "projects/ui/assets/import.scss";

.vh {
	clip: rect(0 0 0 0);
	clip-path: inset(50%);
	height: 1px;
	overflow: hidden;
	position: absolute;
	white-space: nowrap;
	width: 1px;
}

.modules-wrapper {
	background-color: $azulPacto05;
	width: 40px;
	padding: 10px 8px;
	border-radius: 20px;
}

.modulo {
	display: block;
	margin-bottom: 14px;

	&:last-child {
		margin-bottom: 0px;
	}

	.img-aux {
		border-radius: 13px;
		width: 24px;
		height: 24px;
		background-color: $azulPacto05;
		display: flex;
		opacity: 0.5;
		transition: opacity 0.4s ease-out;

		&:hover {
			opacity: 1;
		}

		&.current {
			opacity: 1;
		}

		overflow: hidden;
	}

	img {
		width: 24px;
		height: 24px;
	}
}

.carousel-button {
	all: unset;
	width: 24px;
	height: 24px;
	border-radius: 26px;
	font-size: 1.2rem;
	color: white;
	text-align: center;
	display: block;
	cursor: pointer;

	&[data-variation="previous"] {
		margin-bottom: 14px;
	}

	&[data-variation="next"] {
		margin-top: 14px;
	}

	&[data-inactive="true"] {
		opacity: 0.4;
		cursor: default;
	}
}

.modules-delimiter {
	display: flex;
	overflow: hidden;
	align-items: center;
	position: relative;
	width: 100%;
}

.module-tracks {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	z-index: 2;
	width: 100%;
	transition: transform 0.5s ease-out;
}
