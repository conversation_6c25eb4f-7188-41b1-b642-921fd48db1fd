import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	Input,
	<PERSON><PERSON><PERSON><PERSON>,
	Query<PERSON>ist,
	ViewChild,
	ViewChildren,
	HostListener,
} from "@angular/core";
import { BehaviorSubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { MenuModuleItemDirective } from "../../../../directives/menu-module-item.directive";
import { PlataformaModuleSummary } from "../../plataforma-navigation.service";

@Component({
	selector: "pacto-modules-carousel",
	templateUrl: "./modules-carousel.component.html",
	styleUrls: ["./modules-carousel.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModulesCarouselComponent implements AfterViewInit, OnDestroy {
	@Input() modules: PlataformaModuleSummary[];
	@Input() moduloId: string;
	@Input() loadMenu: (icon: PlataformaModuleSummary) => void;

	@ViewChild("modulesContainer", { static: true })
	modulesContainer: ElementRef<HTMLElement>;
	@ViewChild("modulesWrapper", { static: true })
	modulesWrapper: ElementRef<HTMLElement>;
	@ViewChildren(MenuModuleItemDirective)
	modulesArray: QueryList<MenuModuleItemDirective>;

	_visibleItemsAmount = 6; // sem private para mudar valor conforme tamanho
	private _carouselStepsCount = 0;
	private _currentStep$: BehaviorSubject<number> = new BehaviorSubject<number>(
		1
	);
	private _destroy: Subject<void> = new Subject<void>();
	containerHeight: number;
	private marginSize = 14;
	disablePreviousButton$: BehaviorSubject<boolean> =
		new BehaviorSubject<boolean>(true);
	disableNextButton$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
		false
	);

	get modulesFilter() {
		if (this.modules) {
			return this.modules.filter((module) => {
				return (
					!module.showAsIcon && !module.hideTaskBar && module.showOnMenuTaskbar
				);
			});
		}
		return [];
	}

	// Declare height and width variables
	scrHeight: any;
	scrWidth: any;

	@HostListener("window:resize", ["$event"])
	getScreenSize(event?) {
		this.scrHeight = window.innerHeight;

		if (this.scrHeight <= 720) {
			this._visibleItemsAmount = 5;
		} else {
			this._visibleItemsAmount = 6;
		}
	}

	constructor(private cd: ChangeDetectorRef) {}

	ngAfterViewInit(): void {
		if (this.modulesContainer) {
			this.calcCarouselContainerHeight();
			// number of steps on the carousel (arredondado para cima)
			// ex 1.5 -> 2
			this._carouselStepsCount = Math.ceil(
				this.modulesArray.length / this._visibleItemsAmount
			);

			setTimeout(() => {
				this.scrollToCurrentModule();
			}, 100);
		}

		this._currentStep$
			.pipe(takeUntil(this._destroy))
			.subscribe((currentStep) => {
				this.disablePreviousButton$.next(currentStep === 1);
				this.disableNextButton$.next(
					this._carouselStepsCount === 1 ||
						currentStep === this._carouselStepsCount
				);
			});

		this.cd.detectChanges();
	}

	ngOnDestroy(): void {
		this._destroy.next();
	}

	next(): void {
		if (this._currentStep$.value < this._carouselStepsCount) {
			const currentStep = this._currentStep$.value + 1;
			this._currentStep$.next(currentStep);
			this.handleScroll((_, i) => i === currentStep);
		}
	}

	previous(): void {
		if (this._currentStep$.value > 1) {
			const currentStep = this._currentStep$.value - 1;
			this._currentStep$.next(currentStep);
			this.handleScroll((_, i) => i === currentStep);
		}
	}

	calcCarouselContainerHeight(): void {
		this.getScreenSize();
		console.log("_visibleItemsAmount carousel" + this._visibleItemsAmount);
		const carouselItem = this.modulesArray.toArray()[0];
		if (this.modulesContainer && carouselItem) {
			this.containerHeight =
				(carouselItem.elementRef.nativeElement.clientHeight + this.marginSize) *
					this._visibleItemsAmount -
				this.marginSize; // the last visible item margin gets hidden
		}
	}

	scrollToCurrentModule(): void {
		this.handleScroll((foundItem, i) => foundItem && i > 1);
	}

	handleScroll(conditionCallback: (foundItem, i) => boolean) {
		let chunkStart = 0;
		let chunkEnd = this._visibleItemsAmount;
		const modulesArray = this.modulesArray.toArray();

		Array.from(Array(this._carouselStepsCount)).forEach((_, i) => {
			const chunk = modulesArray.slice(chunkStart, chunkEnd);
			const foundItem = chunk.find((_module) => _module.isCurrent);

			chunkStart = chunkStart + this._visibleItemsAmount;
			chunkEnd = chunkStart + this._visibleItemsAmount;
			const fixedIndex = i + 1;

			const condition = conditionCallback(foundItem, fixedIndex);
			// get scroll position of item

			if (condition && chunk[0]) {
				const elementTop = chunk[0].elementRef.nativeElement.offsetTop;
				this._currentStep$.next(fixedIndex);
				this.modulesWrapper.nativeElement.style.transform = `translate3d(0, -${elementTop}px, 0)`;
			}
		});
	}
}
