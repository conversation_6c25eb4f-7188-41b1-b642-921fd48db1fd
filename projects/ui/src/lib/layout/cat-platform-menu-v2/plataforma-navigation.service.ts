import { Injectable, Optional, LOCALE_ID, Inject } from "@angular/core";

import { BehaviorSubject, Observable, of, Subject, zip } from "rxjs";
import { map, switchMap } from "rxjs/operators";
import { TranslateService } from "@ngx-translate/core";

import {
	PlataformaNavConfigService,
	PlataformaModule,
	PlataformaModuleItem,
} from "./plataforma-nav-config.service";
import FuzzySearch from "fuzzy-search";

export interface PlataformaNavigationItem {
	id: string;
	submenus?: boolean;
	link?: string;
	logoUri?: string;
	newTab?: boolean;
	queryParams?: {
		[name: string]: any;
	};
}

export interface PlataformaModuleSummary {
	id: string;
	icon?;
	imageUri?;
	link?: string;
	queryParams?: {
		[name: string]: any;
	};
	baseColor?: string;
	showAsIcon?: boolean;
	hideTaskBar?: boolean;
	logoUri?: string;
	linkAsHref?: boolean;
	openInNewTab?: boolean;
	showOnMenuTaskbar?: boolean;
	bottomSection?: boolean;
	showOnMenuTopbar?: boolean;

	/**
	 * Se passado true, não realizará o redirect e somente irá carregar o menu.
	 * Se passado o link do menu, o valor dessa propriedade será ignorado.
	 */
	onlyLoadMenu?: boolean;

	/**
	 * Manterá o módulo atual e realizará somente a navegação para a rota do menu
	 */
	onlyMakeRedirect?: boolean;
}

@Injectable({
	providedIn: "root",
})
export class PlataformaNavigationService {
	navigationMenuOpen$: BehaviorSubject<boolean>;
	moduleHasChanged$: BehaviorSubject<{
		moduleSummary: PlataformaModuleSummary;
	}>;
	private _currentModuleId: string;

	constructor(
		@Inject(LOCALE_ID) private locale,
		private translateService: TranslateService,
		@Optional() private navConfigService: PlataformaNavConfigService
	) {
		this.initContextMenuSatus();
	}

	getSubmenus(
		parent: string,
		moduleId: string
	): Observable<PlataformaNavigationItem[]> {
		return this.getModule(moduleId).pipe(
			map((module) => {
				if (module && module.menus) {
					const filter = parent === moduleId ? undefined : parent;
					return module.menus
						.filter((i) => i.parent === filter)
						.map((i) => this.convertToNavItem(module, i));
				} else {
					return [];
				}
			})
		);
	}

	getMenu(id: string, moduleId: string): Observable<PlataformaNavigationItem> {
		return this.getModule(moduleId).pipe(
			map((module) => {
				if (module) {
					const menu = module.menus.find((i) => i.id === id);
					return this.convertToNavItem(module, menu);
				} else {
					return null;
				}
			})
		);
	}

	getMenus(
		ids: string[],
		moduleId: string,
		loadModuleTaskbar: boolean = false
	): Observable<PlataformaNavigationItem[]> {
		return this.getModule(moduleId, loadModuleTaskbar).pipe(
			map((module) => {
				if (module) {
					return module.menus
						.filter((i) => ids.includes(i.id))
						.map((i) => this.convertToNavItem(module, i));
				} else {
					return [];
				}
			})
		);
	}

	getModulesSummary(
		loadModuleTaskbar: boolean = false
	): Observable<PlataformaModuleSummary[]> {
		if (this.navConfigService) {
			return this.navConfigService.getModules().pipe(
				map((modules) => {
					const modulesSorted = this.sortModule(modules, loadModuleTaskbar);
					if (modulesSorted && modulesSorted.length) {
						return modulesSorted.map((module) =>
							this.convertToModuleSummary(module)
						);
					}
				})
			);
		} else {
			return of([]);
		}
	}

	isSmallScreen() {
		const width = Math.max(
			document.documentElement.clientWidth || 0,
			window.innerWidth || 0
		);
		return width < 1280;
	}

	notificateResourceChange(resource: string): void {
		if (this.navConfigService && this.navConfigService.resourceChange) {
			this.navConfigService.resourceChange(resource);
		}
	}

	isBetaTester(): Observable<boolean> {
		if (this.navConfigService && this.navConfigService.isBetaTester) {
			return this.navConfigService.isBetaTester();
		}
	}

	private initContextMenuSatus() {
		const small = this.isSmallScreen();
		this.navigationMenuOpen$ = new BehaviorSubject(!small);
		this.moduleHasChanged$ = new BehaviorSubject({
			moduleSummary: undefined,
		});
	}

	private convertToModuleSummary(
		module: PlataformaModule
	): PlataformaModuleSummary {
		if (module) {
			return {
				id: module.moduleId,
				icon: module.icon,
				imageUri: module.logoUri,
				baseColor: module.baseColor,
				queryParams: module.queryParams,
				showAsIcon: module.showAsIcon,
				link: module.link,
				linkAsHref: module.linkAsHref,
				openInNewTab: module.openInNewTab,
				hideTaskBar: module.hideTaskBar,
				showOnMenuTaskbar: module.showOnMenuTaskbar,
				bottomSection: module.bottomSection,
				showOnMenuTopbar:
					module.showOnMenuTopbar !== undefined
						? module.showOnMenuTopbar
						: true,
				onlyLoadMenu: module.onlyLoadMenu,
				onlyMakeRedirect: module.onlyMakeRedirect,
			};
		} else {
			return null;
		}
	}

	private getModule(
		moduleId: string,
		loadModuleTaskbar: boolean = false
	): Observable<PlataformaModule> {
		if (this.navConfigService) {
			return this.navConfigService.getModules().pipe(
				map((modules) => {
					const modulesSorted = this.sortModule(modules, loadModuleTaskbar);
					const match = modulesSorted.find((m) => m.moduleId === moduleId);
					return match ? match : null;
				})
			);
		} else {
			return of(null);
		}
	}

	private getModules(
		loadModuleTaskbar: boolean = false
	): Observable<PlataformaModule[]> {
		if (this.navConfigService) {
			return this.navConfigService.getModules().pipe(
				map((modules) => {
					const modulesSorted = this.sortModule(modules, loadModuleTaskbar);
					return modulesSorted ? modulesSorted : [];
				})
			);
		} else {
			return of([]);
		}
	}

	search(
		term
	): Observable<{ moduleId: string; matches: PlataformaNavigationItem[] }[]> {
		return this.getModules().pipe(
			switchMap((modulos) => {
				const matches$ = modulos.map((module) =>
					this.searchInModule(module, term)
				);
				return zip(...matches$).pipe(
					map((matches) => {
						if (matches && matches.length) {
							const result = [];
							matches.forEach((moduleMatches, index) => {
								result.push({
									moduleId: modulos[index].moduleId,
									matches: moduleMatches,
								});
							});
							return result;
						} else {
							return [];
						}
					})
				);
			})
		);
	}

	private searchInModule(
		module: PlataformaModule,
		term
	): Observable<PlataformaNavigationItem[]> {
		if (module && module.menus && module.menus.length && term) {
			const candidates: PlataformaModuleItem[] = module.menus.filter(
				(i) => i.link
			);
			const searchTokens$ = candidates.map((i) =>
				this.getSearchSignature(module, i)
			);
			return zip(...searchTokens$).pipe(
				map((searchTokens) => {
					candidates.forEach((item, index) => {
						candidates[index]._searchSignature = searchTokens[index];
					});
					const searcher = new FuzzySearch(candidates, ["_searchSignature"]);
					const matches: PlataformaModuleItem[] = searcher.search(
						this.sanitize(term)
					);
					if (matches && matches.length) {
						return matches.map((item) => this.convertToNavItem(module, item));
					} else {
						return [];
					}
				})
			);
		} else {
			return of([]);
		}
	}

	private getSearchSignature(
		module: PlataformaModule,
		item: PlataformaModuleItem
	): Observable<string> {
		const tokenKey = `menu.nav.${module.moduleId}.${item.id}.searchTokens`;
		const nameKey = `menu.nav.${module.moduleId}.${item.id}.name`;

		const searchToken$ = this.translateService
			.get(tokenKey)
			.pipe(map((value) => (value === tokenKey ? false : value)));
		const name$ = this.translateService.get(nameKey);

		return zip(searchToken$, name$).pipe(
			map((translated) => {
				const result = [];
				result.push(translated[1]);
				if (translated[0]) {
					result.push(translated[0]);
				}
				if (result.length) {
					const joined = result.join();
					return this.sanitize(joined).replace(/[,;]/gi, "");
				} else {
					return "";
				}
			})
		);
	}

	private sanitize(value: string): string {
		if (value) {
			return value
				.normalize("NFD")
				.replace(/[\u0300-\u036f]/g, "")
				.toLowerCase();
		} else {
			return "";
		}
	}

	private convertToNavItem(
		module: PlataformaModule,
		moduleItem: PlataformaModuleItem
	): PlataformaNavigationItem {
		if (moduleItem) {
			return {
				id: moduleItem.id,
				submenus: this.hasSubmenus(moduleItem.id, module.menus),
				logoUri: module.logoUri,
				link: moduleItem.link,
				newTab: moduleItem.newTab,
				queryParams: moduleItem.queryParams,
			};
		} else {
			return null;
		}
	}

	private hasSubmenus(id: string, config: PlataformaModuleItem[]): boolean {
		if (config.find((i) => i.parent === id)) {
			return true;
		} else {
			return false;
		}
	}

	private sortModule(modules: PlataformaModule[], loadModuleTaskbar: boolean) {
		let modulesSorted = modules;
		if (loadModuleTaskbar) {
			modulesSorted = modules.filter(
				(m) => m.showOnMenuTaskbar || m.showAsIcon
			);
		}
		return modulesSorted.sort((m1, m2) => {
			const propertyToUse =
				loadModuleTaskbar &&
				m1.showOnMenuTaskbar &&
				m1.orderTaskbar !== undefined &&
				m1.orderTaskbar !== null
					? "orderTaskbar"
					: "orderTopbar";
			if (m1[propertyToUse] > m2[propertyToUse]) {
				return 1;
			}
			if (m1[propertyToUse] < m2[propertyToUse]) {
				return -1;
			}
			return 0;
		});
	}

	get currentModuleId(): string {
		return sessionStorage.getItem("current_module");
	}

	set currentModuleId(currentModuleId: string) {
		sessionStorage.setItem("current_module", currentModuleId);
	}
}
