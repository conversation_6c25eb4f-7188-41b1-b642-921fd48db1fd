import {
	CatCardPlainComponent,
	CatButtonComponent,
} from "projects/ui/src/public-api";

export default { title: "Layout|Plain Card" };

export const withText = () => ({
	moduleMetadata: {
		declarations: [CatCardPlainComponent],
	},
	template: `<pacto-cat-card-plain> Card simples </pacto-cat-card-plain>`,
});

export const withButton = () => ({
	moduleMetadata: {
		declarations: [CatCardPlainComponent, CatButtonComponent],
	},
	template: `
  <div styles="width: 200px; margin: 0px auto;">
    <pacto-cat-card-plain>
      <pacto-cat-button [label]="'Ação'"></pacto-cat-button>
    </pacto-cat-card-plain>
  </div>
  `,
});
