import { Component, ElementRef, OnInit, AfterViewInit } from "@angular/core";

@Component({
	selector: "pacto-cat-card-plain",
	templateUrl: "./cat-card-plain.component.html",
	styleUrls: ["./cat-card-plain.component.scss"],
})
export class CatCardPlainComponent implements OnInit, AfterViewInit {
	constructor(private elementRef: ElementRef) {}

	ngOnInit() {}

	ngAfterViewInit() {
		const host: HTMLElement = this.elementRef.nativeElement;
		let firstChild = host.firstElementChild as HTMLElement;

		if (
			firstChild &&
			(firstChild.tagName === "DIV" || firstChild.tagName === "FORM")
		) {
			const innerFirstChild = firstChild.firstElementChild as HTMLElement;
			if (innerFirstChild) {
				firstChild = innerFirstChild;
			}
		}

		if (firstChild) {
			firstChild.setAttribute("tabindex", "0");
			firstChild.focus();
		}
	}
}
