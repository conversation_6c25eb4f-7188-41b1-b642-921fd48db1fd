import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { PactoIcon } from "../../pacto-icon.enum";

@Component({
	selector: "pacto-cat-sm-info-block-icon",
	templateUrl: "./cat-sm-info-block-icon.component.html",
	styleUrls: ["./cat-sm-info-block-icon.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatSmInfoBlockIconComponent implements OnInit {
	@Input() label;
	@Input() icon: PactoIcon;
	@Input() value = "-";

	constructor() {}

	ngOnInit() {}
}
