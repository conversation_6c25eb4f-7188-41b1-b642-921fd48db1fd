import {
	Component,
	OnInit,
	Input,
	ViewChild,
	AfterViewInit,
	ContentChild,
	AfterContentInit,
} from "@angular/core";
import {
	trigger,
	state,
	style,
	transition,
	animate,
} from "@angular/animations";

@Component({
	selector: "pacto-cat-accordion",
	templateUrl: "./cat-accordion.component.html",
	styleUrls: ["./cat-accordion.component.scss"],
	animations: [
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class CatAccordionComponent implements OnInit {
	@Input() title = "";
	@Input() id = "";
	@Input() open = false;
	@Input() showIndicator = true;
	@ViewChild("bodySection", { static: true }) bodySection;

	constructor() {}

	ngOnInit() {}

	headerClickHandler() {
		if (this.showIndicator) {
			this.open = !this.open;
		}
	}
}
