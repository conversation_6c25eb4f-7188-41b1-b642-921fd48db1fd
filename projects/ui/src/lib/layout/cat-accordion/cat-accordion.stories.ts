import { CatAccordionComponent } from "projects/ui/src/public-api";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { NO_ERRORS_SCHEMA } from "@angular/core";

export default { title: "Layout|Accordion" };

const moduleMetadata = {
	declarations: [CatAccordionComponent],
	imports: [BrowserAnimationsModule],
	schemas: [NO_ERRORS_SCHEMA],
};

export const withText = () => ({
	moduleMetadata,
	template: `
  <pacto-cat-accordion [title]="'Titulo'">
    <accordion-body>
      <h1> Conteúdo </h1>
    </accordion-body>
  </pacto-cat-accordion>
  `,
});
