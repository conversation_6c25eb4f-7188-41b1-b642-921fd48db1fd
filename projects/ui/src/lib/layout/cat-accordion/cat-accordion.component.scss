@import "projects/ui/assets/import.scss";

.header-section {
	display: flex;
	align-items: center;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
	padding: 11px;
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;

	&.closed {
		border-bottom-left-radius: 10px;
		border-bottom-right-radius: 10px;
	}

	.section-title {
		@extend .type-h6-bold;
		color: $azulPactoPri;
	}

	.title-content {
		flex-grow: 1;
	}

	.status-control {
		height: 28px;

		.pct {
			position: relative;
			top: 2px;
			cursor: pointer;
			font-size: 24px;
			color: $azulPactoPri;
		}
	}
}

.body-section {
	background-color: $branco;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
	box-shadow: inset 0 1px 1px 0px #d4d4d4;
	overflow: hidden;
	width: 100%;
}
