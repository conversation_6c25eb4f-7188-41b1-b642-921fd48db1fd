<div class="accordion-wrapper">
	<div
		(click)="headerClickHandler()"
		[ngClass]="{ closed: !open }"
		[style.cursor]="showIndicator ? 'pointer' : 'cursor'"
		class="header-section">
		<div class="section-title">{{ title }}</div>
		<div class="title-content">
			<ng-content select="accordion-header"></ng-content>
		</div>
		<div *ngIf="showIndicator" class="status-control">
			<i
				*ngIf="open"
				class="pct pct-chevron-up"
				id="close-accordion-{{ id }}"></i>
			<i
				*ngIf="!open"
				class="pct pct-chevron-down"
				id="open-accordion-{{ id }}"></i>
		</div>
	</div>

	<div #bodySection [@openClose]="open ? 'true' : 'false'" class="body-section">
		<ng-content select="accordion-body"></ng-content>
	</div>
</div>
