@import "projects/ui/assets/import.scss";

.line {
	width: 100%;
	height: 0.5px;
	background: $cinza02;
}

.grid-card-title {
	background-color: transparent;
	color: #51555a;
	width: 100%;
	height: 40px;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	font-style: normal;
	text-align: center;
	padding: 16px;
	line-height: 15px;
	font-size: 14px;
}

.grid-card-body {
	background-color: transparent;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
	width: 100%;
	display: flex;
	justify-content: center;
}

.grid-card-foot {
	background-color: transparent;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
	display: flex;
	justify-content: center;
}

.grid {
	position: relative;
	background: transparent;

	&:hover .tooltiptext {
		display: block;
	}

	.tooltiptext {
		height: auto;
		padding: 10px;
		position: absolute;
		text-align: center;
		width: 220px;
		word-wrap: no-wrap;
		box-shadow: 1px 1px 20px #aaa;
		border-radius: 5px;
		background-color: #fff;
		left: 50%;
		transform: translate(-50%);
		transform-style: preserve-3d;
		z-index: 200;
		font-size: 0.9em;
		display: none;
		color: $pretoPri;
		box-shadow: 1px 1px 20px rgba($pretoPri, 0.3);
		top: -50px;
		border: 1px solid $pretoPri;
	}

	.tooltiptext::after {
		content: "";
		position: absolute;
		display: block;
		width: 10px;
		height: 10px;
		transform-origin: 50% 50%;
		transform: rotate(45deg) translateX(-50%);
		background-color: #fff;
		left: 50%;
		top: -1px;
		z-index: 400;
		top: auto;
		bottom: -8px;
		border-width: 1px;
		border-style: solid;
		border-color: transparent $pretoPri $pretoPri transparent;
	}

	.tooltiptext::before {
		content: "";
		display: block;
		position: absolute;
		width: 10px;
		height: 10px;
		transform-origin: 50% 50%;
		transform: rotate(45deg) translateX(-50%) translateZ(-1px);
		background-color: #fff;
		left: 50%;
		top: 0px;
		z-index: -1;
		box-shadow: -1px -1px 20px #aaa;
		top: auto;
		bottom: -8px;
	}
}
