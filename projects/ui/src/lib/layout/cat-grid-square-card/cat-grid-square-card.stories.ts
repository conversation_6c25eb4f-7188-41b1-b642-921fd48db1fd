import { storiesOf, moduleMetadata } from "@storybook/angular";
import { Component, NO_ERRORS_SCHEMA } from "@angular/core";

import {
	CatGridSquareCardComponent,
	CatButtonComponent,
} from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        display: grid;
        gap: 30px;
        grid-template-columns: 1fr 1fr 1fr;
        background-color: #eee;
    ">
			<pacto-cat-grid-square-card
				*ngFor="let item of items"
				[tooltiptext]="
					'Produto com estoque baixo Produto com estoque baixo Produto com estoque baixo'
				">
				<div card-title>{{ item.nome }}</div>
				<div card-body>
					<div style="height: 100px;">Card body.</div>
				</div>
				<div card-foot>
					<pacto-cat-button
						[label]="'Ver detalhes'"
						[type]="'OUTLINE'"></pacto-cat-button>
				</div>
			</pacto-cat-grid-square-card>
		</div>
	`,
})
class HostComponent {
	items = [
		{ index: 1, nome: "Previstos" },
		{ index: 2, nome: "Recebidos" },
		{ index: 3, nome: "Pendentes" },
		{ index: 4, nome: "Canceladas" },
	];
}

const metadata = {
	declarations: [CatGridSquareCardComponent, CatButtonComponent],
	schemas: [NO_ERRORS_SCHEMA],
};

storiesOf("Layout|Grid Square Card", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Simple Tabs", () => {
		return {
			component: HostComponent,
			props: {},
		};
	});
