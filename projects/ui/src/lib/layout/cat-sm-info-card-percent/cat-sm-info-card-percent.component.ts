import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { PactoColor } from "../../pacto-color.model";

export enum SmInfoCardPercentAccentColor {}

@Component({
	selector: "pacto-cat-sm-info-card-percent",
	templateUrl: "./cat-sm-info-card-percent.component.html",
	styleUrls: ["./cat-sm-info-card-percent.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatSmInfoCardPercentComponent implements OnInit {
	@Input() label;
	@Input() value = "-";
	@Input() percent: number = null;
	@Input() tooltip = null;
	@Input() accentColor: PactoColor;
	@Input() description: any;
	@Input() action = true;

	constructor() {}

	ngOnInit() {}

	get mainColor(): PactoColor {
		return this.accentColor ? this.accentColor : PactoColor.PRETO_PRI;
	}

	get percentColor(): PactoColor {
		return this.accentColor ? this.accentColor : PactoColor.AZULIM_PRI;
	}

	get percentDefined() {
		return this.percent !== null;
	}
}
