@import "projects/ui/assets/import.scss";

:host {
	width: 100%;
	display: block;
}

.sm-info-card-percent-wrapper {
	display: flex;
	height: 100%;
	box-shadow: 0px 2px 3px 1px $cinzaClaro02;
	-webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
	transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
	flex-direction: column;
	align-content: center;
	background-color: $branco;
	border-radius: 10px;
	align-items: center;
	padding: 20px;

	&.action {
		cursor: pointer;

		&:hover {
			box-shadow: 2px 5px 8px 2px $cinzaClaro02;
		}
	}

	.pct-label {
		@extend .type-h6;
		color: $cinza05;
		text-align: center;
	}

	.pct-value {
		@extend .type-hero-bold;
		flex-grow: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		text-align: center;
		line-height: 1em;
		margin: 15px 0px;
	}

	.percent-label {
		@extend .type-p-small-rounded;
		color: $cinza05;
		text-align: center;
		margin-top: 10px;
	}
}
