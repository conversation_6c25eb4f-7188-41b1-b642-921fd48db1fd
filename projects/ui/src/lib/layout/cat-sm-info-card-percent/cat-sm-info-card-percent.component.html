<div
	[ngClass]="{
		action: action
	}"
	class="sm-info-card-percent-wrapper">
	<div class="pct-label" title="{{ tooltip }}">{{ label }}</div>

	<div
		[ngStyle]="{
			color: mainColor
		}"
		class="pct-value">
		{{ value }}
	</div>

	<pacto-cat-percent-bar-simple
		*ngIf="percentDefined"
		[color]="percentColor"
		[percent]="percent"></pacto-cat-percent-bar-simple>

	<div *ngIf="percentDefined" class="percent-label">
		{{ description }}
	</div>
</div>
