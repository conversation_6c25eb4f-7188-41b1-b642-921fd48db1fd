@import "projects/ui/assets/import.scss";

:host {
	height: 100vh;
	width: 100%;
}

.layout-body {
	height: calc(100vh - 95px);
	background-color: $cinzaClaroPri;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.width-aux {
		@include plataformaConteudoLargura();
	}
}

.header-container {
	border-bottom: 1px solid $cinza02;
	background-color: #fff;
	justify-content: center;
	align-items: center;
	display: flex;
	height: 95px;
	position: relative;
}

.header-center {
	@include plataformaConteudoLargura();

	&.expanded {
		width: calc(100vw - 260px);
	}

	.pct-logo {
		cursor: pointer;
		width: 46px;
		height: 46px;
		margin-right: 15px;
	}

	display: flex;
	flex-shrink: 0;
	align-items: center;

	.meus-modulos {
		.text {
			cursor: pointer;
			display: flex;
			font-family: "Nunito Sans", sans-serif;
			font-weight: bold;
			font-size: 13px;
			line-height: 22px;
			text-transform: uppercase;
			color: #acb1b5;

			i {
				color: #00426b;
				font-size: 20px;
			}

			span {
				padding-left: 5px;
			}
		}
	}
}

@media (max-width: $plataforma-breakpoint-large) {
	.header-center {
		width: calc(100vw - 260px);
	}
}
