@import "src/assets/scss/variable.scss";
@import "src/assets/scss/functions.scss";

@mixin btn-actions($color-base, $color-shadow, $color-shadow-hover) {
	border: none;
	background-color: $color-base;
	box-shadow: 0 2px 2px 0 $color-shadow-hover,
		0 3px 1px -2px $color-shadow-hover, 0 1px 5px 0 $color-shadow;
}

@mixin btn-actions-hover($color-base, $color-shadow, $color-shadow-hover) {
	box-shadow: 0 14px 26px -12px $color-shadow-hover, 0 4px 23px 0 $color-shadow,
		0 8px 10px -5px $color-shadow-hover;
}

.modal-footer {
	&.module-crossfit .btn-action {
		@include btn-actions(
			primaryColor(treino, base),
			primaryColor(treino, shadow),
			primaryColor(treino, shadow-hover)
		);
	}

	&.module-treino .btn-action {
		@include btn-actions(
			primaryColor(crossfit, base),
			primaryColor(crossfit, shadow),
			primaryColor(crossfit, shadow-hover)
		);
	}

	&.module-avaliacao .btn-action {
		@include btn-actions(
			primaryColor(avaliacao, base),
			primaryColor(avaliacao, shadow),
			primaryColor(avaliacao, shadow-hover)
		);
	}

	&.module-canal-cliente .btn-action {
		@include btn-actions(
			primaryColor(canalCliente, base),
			primaryColor(canalCliente, shadow),
			primaryColor(canalCliente, shadow-hover)
		);
	}

	&.module-crossfit .btn-action:hover {
		@include btn-actions(
			primaryColor(treino, base),
			primaryColor(treino, shadow),
			primaryColor(treino, shadow-hover)
		);
	}

	&.module-treino .btn-action:hover {
		@include btn-actions(
			primaryColor(crossfit, base),
			primaryColor(crossfit, shadow),
			primaryColor(crossfit, shadow-hover)
		);
	}

	&.module-avaliacao .btn-action:hover {
		@include btn-actions(
			primaryColor(avaliacao, base),
			primaryColor(avaliacao, shadow),
			primaryColor(avaliacao, shadow-hover)
		);
	}

	&.module-canal-cliente .btn-action:hover {
		@include btn-actions-hover(
			primaryColor(canalCliente, base),
			primaryColor(canalCliente, shadow),
			primaryColor(canalCliente, shadow-hover)
		);
	}
}
