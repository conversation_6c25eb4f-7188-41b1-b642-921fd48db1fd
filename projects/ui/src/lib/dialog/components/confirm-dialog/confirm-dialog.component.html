<div>
	<div class="modal-header">
		<h4 class="modal-title">
			{{ title }}
		</h4>
		<button
			(click)="dismiss('close')"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div *ngIf="body" class="modal-body">
		{{ body }}
	</div>
	<div *ngIf="bodyRef" class="modal-body">
		<ng-container *ngTemplateOutlet="bodyRef"></ng-container>
	</div>
	<div class="modal-footer">
		<button
			(click)="dismiss('')"
			class="btn btn-outline-danger modal-item"
			i18n="@@buttons:cancelar"
			type="button">
			Cancelar
		</button>
		<button
			(click)="close('')"
			class="btn btn-primary modal-item"
			id="action-{{ actionLabel.toLowerCase().trim() }}"
			type="button">
			{{ actionLabel }}
		</button>
	</div>
</div>

<span #actionDefault [hidden]="true" i18n="@@buttons:remover">Remover</span>
