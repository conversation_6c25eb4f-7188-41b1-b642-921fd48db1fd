import {
	ChangeDetectionStrategy,
	Component,
	ElementRef,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-confirm-dialog",
	templateUrl: "./confirm-dialog.component.html",
	styleUrls: ["./confirm-dialog.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmDialogComponent implements OnInit {
	@Input() title: string;
	@Input() body: string;
	@Input() bodyRef: ElementRef;
	@ViewChild("actionDefault", { static: true }) actionDefault;
	@Input() actionLabel;
	@Input() cancelLabel;
	@Input() showCancelButton;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {
		if (!this.actionLabel) {
			this.actionLabel = this.actionDefault.nativeElement.innerHTML;
		}
		if (!this.cancelLabel) {
			this.cancelLabel = "Cancelar";
		}
		if (this.showCancelButton === null || this.showCancelButton === undefined) {
			this.showCancelButton = true;
		}
	}

	dismiss(reason) {
		this.openModal.dismiss(reason);
	}

	close(reason) {
		this.openModal.close(reason);
	}
}
