@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "modal-fullscreen-header",
})
// tslint:disable-next-line:directive-class-suffix
export class DialogFullscreenHeader {}

import {
	ChangeDetectionStrategy,
	Component,
	ComponentFactoryResolver,
	ContentChild,
	Directive,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
	ViewContainerRef,
} from "@angular/core";

@Component({
	selector: "pacto-dialog-fullscreen",
	templateUrl: "./dialog-fullscreen.component.html",
	styleUrls: ["./dialog-fullscreen.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DialogFullscreenComponent implements OnInit {
	@Input() title: string;
	@Output() dismiss: EventEmitter<boolean> = new EventEmitter();
	// tslint:disable-next-line: no-output-native
	@Output() close: EventEmitter<any> = new EventEmitter();
	@ViewChild("wrapperContainerRef", { read: ViewContainerRef, static: true })
	wrapperContainerRef;
	@ViewChild("headerRef", { read: ViewContainerRef, static: true }) headerRef;
	@ContentChild(DialogFullscreenHeader, { static: false }) header;

	componentRef: any;
	headerComponentRef: any;
	headerUseComponent = false;

	constructor(private componentFactoryResolver: ComponentFactoryResolver) {}

	ngOnInit() {}

	injectBodyComponent(bodyComponent) {
		const factory =
			this.componentFactoryResolver.resolveComponentFactory(bodyComponent);
		this.wrapperContainerRef.clear();
		this.componentRef =
			this.wrapperContainerRef.createComponent(factory).instance;
		return this.componentRef;
	}

	injectHeaderComponent(headerComponent) {
		this.headerUseComponent = true;
		const factory =
			this.componentFactoryResolver.resolveComponentFactory(headerComponent);
		this.headerRef.clear();
		this.headerComponentRef = this.headerRef.createComponent(factory).instance;
		return this.headerComponentRef;
	}

	closeHandler() {
		this.dismiss.emit(true);
	}
}
