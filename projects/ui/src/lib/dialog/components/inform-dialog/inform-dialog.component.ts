import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
	TemplateRef,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-inform-dialog",
	templateUrl: "./inform-dialog.component.html",
	styleUrls: ["./inform-dialog.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InformDialogComponent implements OnInit {
	@Input() title: TemplateRef<any>;
	@Input() body: TemplateRef<any>;

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	dismiss() {
		this.openModal.dismiss();
	}

	close() {
		this.openModal.close();
	}
}
