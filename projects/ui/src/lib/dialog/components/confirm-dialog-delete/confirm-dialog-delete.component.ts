import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-confirm-dialog-delete",
	templateUrl: "./confirm-dialog-delete.component.html",
	styleUrls: ["./confirm-dialog-delete.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmDialogDeleteComponent implements OnInit {
	@Input() texto = "Tem certeza que deseja excluir o item selecionado?";
	@Input() textoAlerta = "Atenção: essa ação não poderá ser desfeita.";
	@Input() titulo = "Excluir";
	@Input() actionButtonWith = "91px";
	@Input() actionLabel = "Excluir";

	constructor(private openModal: NgbActiveModal) {}

	ngOnInit() {}

	closeModal(confirmado) {
		this.openModal.close(confirmado);
	}

	dismissModal() {
		this.openModal.dismiss();
	}
}
