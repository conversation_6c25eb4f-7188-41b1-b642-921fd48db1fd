import { style } from "@angular/animations";
import { Injectable, TemplateRef, EventEmitter } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DialogModule } from "../dialog.module";
import { DialogComponent } from "../components/dialog/dialog.component";
import { ConfirmDialogComponent } from "../components/confirm-dialog/confirm-dialog.component";
import { InformDialogComponent } from "../components/inform-dialog/inform-dialog.component";
import { DialogFullscreenComponent } from "../components/dialog-fullscreen/dialog-fullscreen.component";
import { BalloonDialogComponent } from "../components/balloon-dialog/balloon-dialog.component";
import { NgbModalOptions } from "@ng-bootstrap/ng-bootstrap/modal/modal-config";

export interface PactoModalRef {
	componentInstanceHeader?: any;
	componentInstance?: any;
	result: Promise<any>;
	close: (result: any) => any;
	dismiss: (result: any) => any;
}

export enum PactoModalSize {
	SMALL = "SMALL",
	MEDIUM = "MEDIUM",
	LARGE = "LARGE",
}

type BootstrapModalSize = "sm" | "lg";

@Injectable({
	providedIn: DialogModule,
})
export class DialogService {
	emitLayoutModal: EventEmitter<any> = new EventEmitter();

	constructor(private modal: NgbModal) {}

	deleteDialog(
		title = "Excluir",
		message: string,
		warnMessage = "Atenção: essa ação não poderá ser revertida",
		options: NgbModalOptions = {
			centered: true,
		}
	) {
		/*const instance = this.modal.open(DeleteDialogComponent, options);
        instance.componentInstance.title = title;
        instance.componentInstance.message = message;
        instance.componentInstance.warnMessage = warnMessage;
        return instance;*/
	}

	confirm(title: string, body: string, action?: string) {
		const instance = this.modal.open(ConfirmDialogComponent, {
			centered: true,
		});
		instance.componentInstance.title = title;
		instance.componentInstance.body = body;
		if (action) {
			instance.componentInstance.actionLabel = action;
		}
		return instance;
	}

	confirmBodyRef(
		title: string,
		body: TemplateRef<any>,
		action?: string,
		cancel?: string,
		size?: "sm" | "lg"
	) {
		const instance = this.modal.open(ConfirmDialogComponent, {
			size,
			backdrop: false,
		});
		instance.componentInstance.title = title;
		instance.componentInstance.bodyRef = body;
		if (action) {
			instance.componentInstance.actionLabel = action;
		}
		if (cancel) {
			instance.componentInstance.cancelLabel = cancel;
		} else {
			instance.componentInstance.showCancelButton = false;
		}
		return instance;
	}

	/**
	 * Use esse modal para informar ao usuário uma informação não
	 * qual uma decisão não está envolvida.
	 *
	 */
	inform(title: TemplateRef<any>, body: TemplateRef<any>) {
		const instance = this.modal.open(InformDialogComponent);
		instance.componentInstance.title = title;
		instance.componentInstance.body = body;
		return instance;
	}

	/**
	 * Para controlar o fechar do modal, crie uma função chamada beforeDismiss()
	 */
	open(
		title: string,
		component: any,
		size: PactoModalSize = PactoModalSize.MEDIUM,
		windowClass?: string
	): PactoModalRef {
		const handle = this.modal.open(DialogComponent, {
			centered: true,
			size: this.convertSize(size),
			backdrop: true,
			beforeDismiss: () => {
				if (content.beforeDismiss) {
					return content.beforeDismiss();
				} else {
					return true;
				}
			},
			windowClass,
		});
		const instance: DialogComponent = handle.componentInstance;
		const content = instance.loadModal(title, component);
		return {
			componentInstance: content,
			result: handle.result,
			close: (result: any) => {
				handle.close(result);
			},
			dismiss: (result: any) => {
				handle.dismiss(result);
			},
		};
	}

	openBalloon(
		title: string,
		component: any,
		size: PactoModalSize = PactoModalSize.SMALL,
		backdropClass?: string,
		windowClass?: string
	): PactoModalRef {
		const handle = this.modal.open(BalloonDialogComponent, {
			centered: false,
			size: this.convertSize(size),
			backdrop: true,
			backdropClass,
			beforeDismiss: () => {
				if (content.beforeDismiss) {
					return content.beforeDismiss();
				} else {
					return true;
				}
			},
			windowClass,
		});
		const instance: BalloonDialogComponent = handle.componentInstance;
		const content = instance.loadModal(title, component);
		return {
			componentInstance: content,
			result: handle.result,
			close: (result: any) => {
				handle.close(result);
			},
			dismiss: (result: any) => {
				handle.dismiss(result);
			},
		};
	}

	openCustomFullscreen(component: any): PactoModalRef {
		const handle = this.modal.open(component, {
			centered: true,
			windowClass: "pacto-modal-fullscreen",
			backdrop: "static",
		});
		return {
			componentInstance: handle.componentInstance,
			result: handle.result,
			close: (result: any) => {
				handle.close(result);
			},
			dismiss: (result: any) => {
				handle.dismiss(result);
			},
		};
	}

	openFullscreen(title: string | any, component?: any): PactoModalRef {
		const handle = this.modal.open(DialogFullscreenComponent, {
			centered: true,
			backdrop: "static",
			windowClass: "pacto-modal-fullscreen",
			beforeDismiss: () => {
				if (contentInstance.beforeDismiss) {
					return contentInstance.beforeDismiss();
				} else {
					return true;
				}
			},
		});
		const fullscreenInstance: DialogFullscreenComponent =
			handle.componentInstance;

		fullscreenInstance.dismiss.subscribe(() => {
			handle.dismiss();
		});

		fullscreenInstance.close.subscribe((result) => {
			handle.close(result);
		});

		let contentInstanceHeader;
		if (typeof title === "string") {
			fullscreenInstance.title = title;
		} else {
			contentInstanceHeader = fullscreenInstance.injectHeaderComponent(title);
		}
		const contentInstance = fullscreenInstance.injectBodyComponent(component);

		return {
			componentInstanceHeader: contentInstanceHeader,
			componentInstance: contentInstance,
			result: handle.result,
			close: (result: any) => {
				handle.close(result);
			},
			dismiss: (result: any) => {
				handle.dismiss(result);
			},
		};
	}

	emitFlagLayout(flag: boolean) {
		this.emitLayoutModal.emit(flag);
	}

	private convertSize(size: PactoModalSize): BootstrapModalSize {
		if (size === PactoModalSize.SMALL) {
			return "sm";
		} else if (size === PactoModalSize.MEDIUM) {
			return null;
		} else if (size === PactoModalSize.LARGE) {
			return "lg";
		}
	}
}
