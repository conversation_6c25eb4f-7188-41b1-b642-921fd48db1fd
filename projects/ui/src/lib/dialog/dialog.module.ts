import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DialogComponent } from "./components/dialog/dialog.component";
import { PactoFormsModule } from "../forms/pacto-forms.module";
import { InformDialogComponent } from "./components/inform-dialog/inform-dialog.component";
import {
	DialogFullscreenComponent,
	DialogFullscreenHeader,
} from "./components/dialog-fullscreen/dialog-fullscreen.component";
import { ConfirmDialogComponent } from "./components/confirm-dialog/confirm-dialog.component";
import { BalloonDialogComponent } from "./components/balloon-dialog/balloon-dialog.component";
import { ConfirmDialogDeleteComponent } from "./components/confirm-dialog-delete/confirm-dialog-delete.component";

@NgModule({
	declarations: [
		DialogComponent,
		InformDialogComponent,
		DialogFullscreenComponent,
		ConfirmDialogComponent,
		DialogFullscreenHeader,
		BalloonDialogComponent,
		ConfirmDialogDeleteComponent,
	],
	entryComponents: [
		DialogComponent,
		InformDialogComponent,
		DialogFullscreenComponent,
		ConfirmDialogComponent,
		BalloonDialogComponent,
		ConfirmDialogDeleteComponent,
	],
	exports: [
		DialogComponent,
		InformDialogComponent,
		DialogFullscreenComponent,
		ConfirmDialogComponent,
		DialogFullscreenHeader,
		BalloonDialogComponent,
		ConfirmDialogDeleteComponent,
	],
	imports: [CommonModule, PactoFormsModule],
})
export class DialogModule {}
