import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CatFormFieldComponent } from "./cat-form-field.component";
import { CatErrorComponent } from "./cat-error/cat-error.component";
import { CatInputDirective } from "./directives/cat-input.directive";
import { CatPrefixDirective } from "./directives/cat-prefix.directive";
import { CatSuffixDirective } from "./directives/cat-suffix.directive";

@NgModule({
	declarations: [
		CatFormFieldComponent,
		CatErrorComponent,
		CatInputDirective,
		CatPrefixDirective,
		CatSuffixDirective,
	],
	imports: [CommonModule],
	exports: [
		CatFormFieldComponent,
		CatErrorComponent,
		CatInputDirective,
		CatPrefixDirective,
		CatSuffixDirective,
	],
})
export class CatFormFieldModule {}
