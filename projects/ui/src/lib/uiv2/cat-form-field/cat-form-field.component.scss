@import "projects/ui/assets/import.scss";

.cat-form-field {
	display: block;
	width: 400px;

	.pacto-cat-error {
		height: 26px;
		padding-top: 8px;
	}

	&.remove-error {
		.pacto-cat-error {
			display: none;
		}
	}
}

.cat-form-field-wrapper {
	&.error {
		.cat-form-field-input {
			border-color: $hellboyPri;

			&.cat-form-field-input-underlined {
				border-bottom-color: $hellboyPri;
			}
		}

		.cat-form-field-label {
			color: $hellboyPri;
		}
	}

	.cat-form-field-label {
		color: $cinza05;
		font-size: 16px;
		line-height: 21.82px;
		margin-bottom: 9px;
		font-weight: 600;

		label {
			margin-bottom: unset;
		}
	}

	.cat-form-field-input {
		background-color: #fff;
		padding: 6px 12px;
		display: flex;
		align-items: center;

		&.cat-form-field-input-outlined {
			border: 1px solid $cinzaPri;
			border-radius: 4px;
		}

		&.cat-form-field-input-underlined {
			border-bottom: 1px solid $cinzaPri;

			&:focus-within {
				border-color: $azulimPri;
			}
		}

		&.disabled {
			background-color: $cinza01;
			border: unset;

			input,
			textarea {
				&::placeholder,
				&:-ms-input-placeholder,
				&::-ms-input-placeholder {
					color: $cinza03;
				}
			}
		}

		.cat-prefix {
			margin-right: 8px;
		}

		.cat-suffix {
			margin-left: 8px;
		}

		input,
		textarea {
			border: none;
			outline: none;
			padding: 0;
			width: 100%;
			color: $pretoPri;
			font-size: 16px;

			&::placeholder {
				color: $cinzaPri;
				opacity: 1;
			}

			&:-ms-input-placeholder,
			&::-ms-input-placeholder {
				color: $cinzaPri;
			}

			&:disabled {
				background: inherit;
			}
		}

		input {
			min-height: 26px;
		}
	}
}
