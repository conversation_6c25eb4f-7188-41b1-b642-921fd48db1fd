import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChild,
	ContentChildren,
	ElementRef,
	HostListener,
	Input,
	OnInit,
	QueryList,
	Renderer2,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { CatInputDirective } from "./directives/cat-input.directive";
import { CatPrefixDirective } from "./directives/cat-prefix.directive";
import { CatSuffixDirective } from "./directives/cat-suffix.directive";
import { FormControl } from "@angular/forms";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-form-field",
	templateUrl: "./cat-form-field.component.html",
	styleUrls: ["./cat-form-field.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatFormFieldComponent implements OnInit, AfterViewInit {
	@ContentChild(CatInputDirective, { static: false })
	catInputDirective: CatInputDirective;
	@ContentChildren(CatPrefixDirective, { descendants: true })
	prefixes: QueryList<CatPrefixDirective>;
	@ContentChildren(CatSuffixDirective, { descendants: true })
	suffixes: QueryList<CatSuffixDirective>;
	@ViewChild("labelElement", { static: false }) labelElement;
	@Input() requiredSymbol = "*";
	@Input() appearance: "outlined" | "underlined" = "outlined";
	@Input() id: string;
	showLabel = true;

	get disabled(): boolean {
		if (this.catInputDirective) {
			if (this.catInputDirective.control) {
				return this.catInputDirective.control.disabled;
			} else if (
				this.catInputDirective.element.nativeElement.hasOwnProperty("disabled")
			) {
				return this.catInputDirective.element.nativeElement.hasOwnProperty(
					"disabled"
				);
			}
		}
	}

	constructor(
		public elementRef: ElementRef,
		private renderer2: Renderer2,
		private changeDetectorRef: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.renderer2.addClass(this.elementRef.nativeElement, "cat-form-field");
	}

	ngAfterViewInit() {
		this.showLabel =
			this.labelElement.nativeElement &&
			this.labelElement.nativeElement.children.length > 0;
		const label =
			this.elementRef.nativeElement.getElementsByTagName("label")[0];
		if (this.catInputDirective) {
			const id = this.catInputDirective.id;
			this.catInputDirective.element.nativeElement.id = id
				? id
				: this.id
				? this.id
				: `cat-input-${uniqueId++}`;
			if (label) {
				label.id = id
					? `cat-input-label-${id}`
					: this.id
					? `cat-input-label-${this.id}`
					: `cat-input-label-${uniqueId++}`;
				label.setAttribute(
					"for",
					this.catInputDirective.element.nativeElement.id
				);
				if (
					this.catInputDirective.element.nativeElement.hasAttribute(
						"required"
					) ||
					this.hasRequiredValidator(this.catInputDirective)
				) {
					label.textContent += ` ${this.requiredSymbol}`;
				}
			}

			if (
				this.catInputDirective.element.nativeElement.hasAttribute("autofocus")
			) {
				this.catInputDirective.element.nativeElement.focus();
			}

			if (
				this.catInputDirective.element.nativeElement.hasAttribute("disabled")
			) {
				this.catInputDirective.control.control.disable();
			}
		}
		this.changeDetectorRef.detectChanges();
	}

	@HostListener("click", ["$event"])
	onClick(event: PointerEvent) {
		if (this.catInputDirective) {
			this.catInputDirective.element.nativeElement.focus();
		}
	}

	hasRequiredValidator(directive) {
		if (directive.control) {
			const { validator } = directive.control.control;
			if (validator) {
				const validation = validator(new FormControl());
				return validation !== null && validation.required;
			}
		}
	}
}
