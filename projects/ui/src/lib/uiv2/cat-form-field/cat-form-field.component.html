<div
	[ngClass]="{
		error:
			catInputDirective?.control?.touched &&
			catInputDirective?.control?.invalid,
		disabled: disabled
	}"
	class="cat-form-field-wrapper">
	<div #labelElement *ngIf="showLabel" class="cat-form-field-label">
		<ng-content select="label"></ng-content>
	</div>
	<div
		[class]="'cat-form-field-input-' + appearance"
		[ngClass]="{ disabled: catInputDirective?.control?.disabled }"
		class="cat-form-field-input">
		<div *ngIf="prefixes?.length" class="cat-prefix">
			<ng-content select="[pactoCatPrefix]"></ng-content>
		</div>
		<ng-content select="[pactoCatInput]"></ng-content>
		<div *ngIf="suffixes?.length" class="cat-suffix">
			<ng-content select="[pactoCatSuffix]"></ng-content>
		</div>
	</div>
</div>
<div class="pacto-cat-error">
	<ng-content
		*ngIf="
			catInputDirective?.control?.touched && catInputDirective?.control?.invalid
		"
		select="pacto-cat-error"></ng-content>
</div>
