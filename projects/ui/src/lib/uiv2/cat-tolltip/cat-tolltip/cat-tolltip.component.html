<ng-content></ng-content>
<div #tooltip [ngClass]="{ 'dark-theme': darkTheme }" class="pacto-cat-tolltip">
	<div class="pacto-cat-tooltip-content-indicator">
		<div class="pacto-cat-tolltip-content">
			<ng-container *ngIf="!isTemplate() && !!pactoCatTolltip">
				{{ pactoCatTolltip }}
			</ng-container>
			<ng-container
				*ngIf="isTemplate()"
				[ngTemplateOutlet]="pactoCatTolltip"></ng-container>
		</div>
		<div
			class="pacto-cat-tolltip-indicator pacto-cat-tolltip-trigger-{{
				position
			}}"></div>
	</div>
</div>
