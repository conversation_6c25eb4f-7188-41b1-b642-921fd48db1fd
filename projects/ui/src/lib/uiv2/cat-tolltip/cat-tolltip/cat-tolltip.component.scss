@import "projects/ui/assets/import.scss";

.pacto-cat-tolltip {
	position: absolute;
	pointer-events: none;
	visibility: hidden;
	display: block;
	z-index: 5000;

	.pacto-cat-tooltip-content-indicator {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;

		.pacto-cat-tolltip-content {
			position: relative;
			padding: 12px;
			border-radius: 4px;
			border: 1px solid $cinzaPri;
			font-size: 14px;
			font-weight: 400;
			color: $cinza06;
			line-height: 1.25;
			background: $branco;
		}

		.pacto-cat-tolltip-indicator {
			position: absolute;

			&:after {
				content: "";
				display: block;
				width: 10px;
				height: 10px;
				background: $branco;
				border: 1px solid $cinzaPri;
				pointer-events: none;
			}

			&.pacto-cat-tolltip-trigger-top {
				bottom: -4.7px;

				&:after {
					transform: rotate(45deg);
					border-bottom-right-radius: 2px;
					border-top: 0;
					border-left: 0;
				}
			}

			&.pacto-cat-tolltip-trigger-bottom {
				top: -4.7px;

				&:after {
					transform: rotate(45deg);
					border-top-left-radius: 2px;
					border-bottom: 0;
					border-right: 0;
				}
			}

			&.pacto-cat-tolltip-trigger-left {
				right: -4.7px;

				&:after {
					transform: rotate(45deg);
					border-top-right-radius: 2px;
					border-bottom: 0;
					border-left: 0;
				}
			}

			&.pacto-cat-tolltip-trigger-right {
				left: -4.7px;

				&:after {
					transform: rotate(45deg);
					border-bottom-left-radius: 2px;
					border-top: 0;
					border-right: 0;
				}
			}
		}
	}

	&.dark-theme {
		.pacto-cat-tooltip-content-indicator {
			.pacto-cat-tolltip-content {
				border: 0;
				color: $branco;
				background: rgba($preto07, 0.9);
			}

			.pacto-cat-tolltip-indicator {
				&:after {
					background: rgba($preto07, 0.9);
					border: 0;
					pointer-events: none;
				}
			}
		}
	}
}
