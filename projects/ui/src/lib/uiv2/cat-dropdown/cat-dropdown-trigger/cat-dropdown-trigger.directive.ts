import {
	Directive,
	ElementRef,
	EventEmitter,
	HostListener,
	Inject,
	Input,
	OnInit,
	Output,
	Renderer2,
} from "@angular/core";
import { CatDropdownComponent } from "../cat-dropdown.component";
import { DOCUMENT } from "@angular/common";

let uniqueId = 0;
// TODO (<PERSON>) isso é temporário, irei realizar a refatoração após finalizar o menu.
let dropdownOpenedElement;

@Directive({
	selector: "[pactoCatDropdownTrigger]",
})
export class CatDropdownTriggerDirective implements OnInit {
	private _overlay: HTMLElement;

	@Input() pactoCatDropdownTrigger: CatDropdownComponent;
	@Input() triggerOpenedClass: string;
	@Output() innerClicked: EventEmitter<CatDropdownTriggerDirective> =
		new EventEmitter<CatDropdownTriggerDirective>();
	@Output() dropwDownOpened: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	constructor(
		private elementRef: ElementRef,
		private renderer: Renderer2,
		@Inject(DOCUMENT) private document
	) {}

	ngOnInit() {
		if (!this.elementRef.nativeElement.id) {
			this.renderer.setAttribute(
				this.elementRef.nativeElement,
				"id",
				`cat-dropdown-trigger-${uniqueId++}`
			);
		}
		this.renderer.addClass(
			this.elementRef.nativeElement,
			"cat-dropdown-trigger"
		);
		this.createOverlay();
	}

	@HostListener("click", ["$event"])
	onClick(event: MouseEvent) {
		if (
			dropdownOpenedElement &&
			dropdownOpenedElement.opened &&
			dropdownOpenedElement !== this.pactoCatDropdownTrigger
		) {
			dropdownOpenedElement.close();
			if (this._overlay.children.length === 0) {
				return;
			}
			const children = Array.from(this._overlay.children);
			for (const child of children) {
				if (child.id.includes("cat-dropdown")) {
					this._overlay.removeChild(child);
					break;
				}
			}
		}
		if (
			this.pactoCatDropdownTrigger &&
			this.isDescendant(this.elementRef.nativeElement, event.target)
		) {
			if (!this.pactoCatDropdownTrigger.opened) {
				this.pactoCatDropdownTrigger.open();
				this._overlay.appendChild(
					this.pactoCatDropdownTrigger.elementRef.nativeElement
				);
				this.calculateDropdownPosition();
				dropdownOpenedElement = this.pactoCatDropdownTrigger;
				this.dropwDownOpened.emit(true);
				if (this.triggerOpenedClass) {
					this.renderer.addClass(
						this.elementRef.nativeElement,
						this.triggerOpenedClass
					);
				}
			}
		}
	}

	private hasManterDropDownAberto(element: HTMLElement) {
		return element.hasAttribute("manterDropdownAberto");
	}

	private calculateDropdownPosition() {
		const triggerRect: ClientRect | DOMRect =
			this.elementRef.nativeElement.getBoundingClientRect();
		const dropDownRect: ClientRect | DOMRect =
			this.pactoCatDropdownTrigger.elementRef.nativeElement.getBoundingClientRect();
		const topPosition = triggerRect.top + triggerRect.height + 10 + "px";
		const leftPosition =
			triggerRect.left + triggerRect.width - dropDownRect.width + "px";
		this.renderer.setStyle(
			this.pactoCatDropdownTrigger.elementRef.nativeElement,
			"top",
			topPosition
		);
		this.renderer.setStyle(
			this.pactoCatDropdownTrigger.elementRef.nativeElement,
			"left",
			leftPosition
		);
	}

	@HostListener("document:click", ["$event"])
	closeDropdownListener(event) {
		if (
			!this.isDescendant(this.elementRef.nativeElement, event.target) &&
			!this.hasManterDropDownAberto(event.target)
		) {
			if (
				event.target === this.pactoCatDropdownTrigger.elementRef.nativeElement
			) {
				return;
			}
			const innerClick = this.isDescendant(
				this.pactoCatDropdownTrigger.elementRef.nativeElement,
				event.target
			);

			if (!innerClick) {
				this.closeDropdown(this.pactoCatDropdownTrigger);
			}
			if (innerClick) {
				this.innerClicked.emit(this);
			}
		}
	}

	closeDropdown(dropdown: CatDropdownComponent) {
		dropdown.close();
		if (this._overlay.children.length !== 0) {
			const children = Array.from(this._overlay.children);
			for (const child of children) {
				if (
					child.id.includes("cat-dropdown") &&
					dropdownOpenedElement &&
					child.id !== dropdownOpenedElement.elementRef.nativeElement.id
				) {
					this._overlay.removeChild(child);
					break;
				}
			}
		}
		if (this.pactoCatDropdownTrigger === dropdownOpenedElement) {
			dropdownOpenedElement = undefined;
		}
		this.dropwDownOpened.emit(false);
		if (this.triggerOpenedClass) {
			this.renderer.removeClass(
				this.elementRef.nativeElement,
				this.triggerOpenedClass
			);
		}
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	createOverlay() {
		this._overlay = this.document.getElementById("pacto-cat-overlay");
		if (!this._overlay) {
			this._overlay = this.document.createElement("div");
			this._overlay.id = "pacto-cat-overlay";
			this._overlay.style.position = "absolute";
			this._overlay.style.top = "0px";
			this._overlay.style.bottom = "0px";
			this._overlay.style.width = "100%";
			this._overlay.style.pointerEvents = "none";
			this._overlay = document.body.appendChild(this._overlay);
		}
	}
}
