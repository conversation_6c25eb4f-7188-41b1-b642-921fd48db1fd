import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CatDropdownComponent } from "./cat-dropdown.component";
import { CatDropdownItemComponent } from "./cat-dropdown-item/cat-dropdown-item.component";
import { CatDropdownTriggerDirective } from "./cat-dropdown-trigger/cat-dropdown-trigger.directive";

@NgModule({
	declarations: [
		CatDropdownComponent,
		CatDropdownTriggerDirective,
		CatDropdownItemComponent,
	],
	imports: [CommonModule],
	exports: [
		CatDropdownComponent,
		CatDropdownTriggerDirective,
		CatDropdownItemComponent,
	],
})
export class CatDropdownModule {}
