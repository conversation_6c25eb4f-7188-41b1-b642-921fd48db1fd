import {
	AfterViewInit,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-dropdown",
	templateUrl: "./cat-dropdown.component.html",
	styleUrls: ["./cat-dropdown.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatDropdownComponent implements OnInit, OnDestroy, AfterViewInit {
	@Input() indicatorClass = "bg-azulim04";
	@Input() showIndicator = true;
	render2Listeners: Array<() => any> = new Array<() => any>();

	private _opened = false;

	get opened() {
		return this._opened;
	}

	constructor(private render2: Renderer2, public elementRef: ElementRef) {}

	ngOnInit() {
		if (!this.elementRef.nativeElement.id) {
			this.render2.setAttribute(
				this.elementRef.nativeElement,
				"id",
				`cat-dropdown-${uniqueId++}`
			);
		}
		this.render2.addClass(this.elementRef.nativeElement, "cat-dropdown");
	}

	ngAfterViewInit() {}

	ngOnDestroy() {
		this.render2Listeners.forEach((i) => {
			i();
		});
	}

	open() {
		this._opened = true;
		this.render2.addClass(this.elementRef.nativeElement, "opened");
	}

	close() {
		this._opened = false;
		this.render2.removeClass(this.elementRef.nativeElement, "opened");
	}
}
