@import "projects/ui/assets/import.scss";

.cat-dropdown {
	position: absolute;
	z-index: 3000;
	visibility: hidden;
	display: none;
	height: 0;

	&.opened {
		display: block;
		height: fit-content;
		visibility: visible;
	}

	.cat-dropdown-menu {
		position: relative;
		border-radius: 10px;
		background: $branco;
		box-shadow: 0 0 2px 1px $cinzaPri;
		display: block;
		margin-top: 5px;
		margin-bottom: 5px;
		pointer-events: all;

		.cat-dropdown-indicator {
			position: absolute;
			top: -4.7px;
			right: 6px;
			height: 10px;
			width: 10px;
			transform: rotate(45deg);
			border-top-left-radius: 3px;
		}
	}
}
