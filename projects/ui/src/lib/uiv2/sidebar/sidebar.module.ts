import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CatSidebarContainerComponent } from "./cat-sidebar-container/cat-sidebar-container.component";
import { CatSidebarContentComponent } from "./cat-sidebar-content/cat-sidebar-content.component";
import { CatTaskbarComponent } from "./cat-taskbar/cat-taskbar.component";
import { CatSidebarComponent } from "./cat-sidebar/cat-sidebar.component";

@NgModule({
	declarations: [
		CatSidebarContainerComponent,
		CatSidebarContentComponent,
		CatTaskbarComponent,
		CatSidebarComponent,
	],
	imports: [CommonModule],
	exports: [
		CatSidebarContainerComponent,
		CatSidebarContentComponent,
		CatTaskbarComponent,
		CatSidebarComponent,
	],
})
export class SidebarModule {}
