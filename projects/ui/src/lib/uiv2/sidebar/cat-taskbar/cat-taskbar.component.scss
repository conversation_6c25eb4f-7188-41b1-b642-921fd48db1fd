@import "projects/ui/assets/import";

$taskbar-width: 66px;

.cat-taskbar {
	width: $taskbar-width;
	background: $azulPactoPri;
	overflow: auto;
	height: inherit;
	display: block;
	border-right: 2px solid $azulPacto05;
	flex: 1 0 $taskbar-width;

	&::-webkit-scrollbar {
		width: 0;
		height: 0;
	}

	& *:hover::-webkit-scrollbar {
		width: 0;
		height: 18px;
	}

	&::-webkit-scrollbar-thumb {
		height: 3px;
		border: 4px solid rgba(0, 0, 0, 0);
		background-clip: padding-box;
		border-radius: 3px;
		box-shadow: inset -1px -1px 0px rgba(0, 0, 0, 0.05),
			inset 1px 1px 0px rgba(0, 0, 0, 0.05);
		background-color: #6f747b;
	}

	&::-webkit-scrollbar-button {
		width: 0;
		height: 0;
		display: none;
	}

	&::-webkit-scrollbar-corner {
		background-color: transparent;
	}
}
