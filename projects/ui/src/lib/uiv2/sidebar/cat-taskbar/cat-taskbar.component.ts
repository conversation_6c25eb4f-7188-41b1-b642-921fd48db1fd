import {
	Component,
	ElementRef,
	Input,
	OnInit,
	Renderer2,
	TemplateRef,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "pacto-cat-taskbar",
	templateUrl: "./cat-taskbar.component.html",
	styleUrls: ["./cat-taskbar.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatTaskbarComponent implements OnInit {
	@Input() template: TemplateRef<any>;

	constructor(private elementRef: ElementRef, private renderer2: Renderer2) {}

	ngOnInit() {
		this.renderer2.addClass(this.elementRef.nativeElement, "cat-taskbar");
	}
}
