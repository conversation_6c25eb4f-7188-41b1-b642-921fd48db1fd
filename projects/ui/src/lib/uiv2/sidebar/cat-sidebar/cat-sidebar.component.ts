import {
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	Renderer2,
	SimpleChange,
	SimpleChanges,
	ViewEncapsulation,
} from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";

@Component({
	selector: "pacto-cat-sidebar",
	templateUrl: "./cat-sidebar.component.html",
	styleUrls: ["./cat-sidebar.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatSidebarComponent implements OnInit, OnChanges {
	@Input() opened: boolean;

	@Output() openChange: EventEmitter<boolean> = new EventEmitter<boolean>();

	@Input() mode: "side" | "overlay" = "side";

	@Input() position: "left" | "right" = "left";

	constructor(
		public elementRef: ElementRef,
		private renderer2: Renderer2,
		private router: Router
	) {}

	ngOnInit() {
		this.renderer2.addClass(this.elementRef.nativeElement, "cat-sidebar");
		this.changeClassesByMode();
		this.toggleClass();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes) {
			const mode: SimpleChange = changes.mode;
			if (!mode.isFirstChange()) {
				this.toggleClass();
				this.changeClassesByMode();
			}
		}
	}

	private changeClassesByMode() {
		const previousElement = this.elementRef.nativeElement.previousSibling;
		let previousElementClientRect: any = {};
		if (previousElement) {
			previousElementClientRect = previousElement.getBoundingClientRect();
			if (previousElementClientRect) {
				if (this.mode === "overlay") {
					if (this.position === "left") {
						this.renderer2.setStyle(
							this.elementRef.nativeElement,
							"left",
							`${previousElementClientRect.width}px`
						);
					} else if (this.position === "right") {
						this.renderer2.setStyle(
							this.elementRef.nativeElement,
							"right",
							`${previousElementClientRect.width}px`
						);
					}
				} else if (this.mode === "side") {
					if (this.position === "left") {
						this.renderer2.removeStyle(this.elementRef.nativeElement, "left");
					} else if (this.position === "right") {
						this.renderer2.removeStyle(this.elementRef.nativeElement, "right");
					}
				}
			}
		}
		if (this.mode === "overlay") {
			this.renderer2.addClass(
				this.elementRef.nativeElement,
				"cat-sidebar-overlay"
			);
		} else if (this.mode === "side") {
			this.renderer2.removeClass(
				this.elementRef.nativeElement,
				"cat-sidebar-overlay"
			);
		}
	}

	toggle() {
		this.opened = !this.opened;
		this.toggleClass();
		this.openChange.emit(this.opened);
	}

	private toggleClass() {
		if (this.opened) {
			this.renderer2.addClass(
				this.elementRef.nativeElement,
				"cat-sidebar-opened"
			);
		} else {
			this.renderer2.removeClass(
				this.elementRef.nativeElement,
				"cat-sidebar-opened"
			);
		}
	}
}
