import {
	AfterViewInit,
	ChangeDetectionStrategy,
	Component,
	ElementRef,
	HostBinding,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "pacto-cat-sidebar-content",
	templateUrl: "./cat-sidebar-content.component.html",
	styleUrls: ["./cat-sidebar-content.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatSidebarContentComponent implements OnInit, AfterViewInit {
	@HostBinding("class")
	class = "cat-sidebar-content";

	constructor(public elementRef: ElementRef) {}

	ngOnInit() {}

	ngAfterViewInit() {}
}
