import {
	AfterViewInit,
	ChangeDetectionStrategy,
	Component,
	ContentChild,
	HostBinding,
	OnInit,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";
import { CatTaskbarComponent } from "../cat-taskbar/cat-taskbar.component";
import { CatSidebarContentComponent } from "../cat-sidebar-content/cat-sidebar-content.component";
import { CatSidebarComponent } from "../cat-sidebar/cat-sidebar.component";

@Component({
	selector: "pacto-cat-sidebar-container",
	templateUrl: "./cat-sidebar-container.component.html",
	styleUrls: ["./cat-sidebar-container.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatSidebarContainerComponent implements OnInit, AfterViewInit {
	@ContentChild(CatTaskbarComponent, { static: false })
	taskbarComponent: CatTaskbarComponent;
	@ContentChild(CatSidebarComponent, { static: false })
	sidebarComponent: CatSidebarComponent;
	@ContentChild(CatSidebarContentComponent, { static: false })
	sidebarContentComponent: CatSidebarContentComponent;

	@HostBinding("class")
	class = "cat-sidebar-container";

	constructor(private renderer: Renderer2) {}

	ngOnInit() {}

	ngAfterViewInit() {
		if (this.sidebarComponent) {
			if (this.sidebarContentComponent) {
				if (this.sidebarComponent.mode === "overlay") {
					const sidebarRect =
						this.sidebarComponent.elementRef.nativeElement.getBoundingClientRect();
					if (this.sidebarComponent.position === "left") {
						this.renderer.setStyle(
							this.sidebarContentComponent.elementRef.nativeElement,
							"margin-left",
							`${sidebarRect.width}px`
						);
					} else if (this.sidebarComponent.position === "right") {
						this.renderer.setStyle(
							this.sidebarContentComponent.elementRef.nativeElement,
							"margin-right",
							`${sidebarRect.width}px`
						);
					}
				}
			}
		}
	}
}
