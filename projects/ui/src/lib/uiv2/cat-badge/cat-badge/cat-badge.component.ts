import {
	Component,
	ElementRef,
	HostBinding,
	Input,
	OnInit,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";

let uniqueId = 0;

@Component({
	selector: "[pactoCatBadge]",
	templateUrl: "./cat-badge.component.html",
	styleUrls: ["./cat-badge.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatBadgeComponent implements OnInit {
	@Input() pactoCatBadge: string;

	@HostBinding("id")
	@Input()
	id;

	@Input() bgColorClass = "bg-hellboy-pri";
	@Input() colorClass = "cor-branco";

	constructor(private elementRef: ElementRef, private renderer2: Renderer2) {}

	ngOnInit() {
		this.renderer2.addClass(this.elementRef.nativeElement, "pct-cat-badge");
		if (!this.id) {
			this.id = `pct-cat-badge-${uniqueId++}`;
		}
	}
}
