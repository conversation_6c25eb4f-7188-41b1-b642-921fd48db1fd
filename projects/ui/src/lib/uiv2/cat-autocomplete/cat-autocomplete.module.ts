import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CatAutocompleteComponent } from "./cat-autocomplete/cat-autocomplete.component";
import { CatAutocompleteTriggerDirective } from "./cat-autocomplete-trigger/cat-autocomplete-trigger.directive";
import { CatOptionModule } from "../cat-option/cat-option.module";

@NgModule({
	declarations: [CatAutocompleteComponent, CatAutocompleteTriggerDirective],
	imports: [CommonModule, CatOptionModule],
	exports: [
		CatAutocompleteComponent,
		CatAutocompleteTriggerDirective,
		CatOptionModule,
	],
})
export class CatAutocompleteModule {}
