import {
	AfterContentInit,
	Directive,
	ElementRef,
	Host,
	HostListener,
	Inject,
	Input,
	OnInit,
	Optional,
	QueryList,
	Renderer2,
} from "@angular/core";
import { CatAutocompleteComponent } from "../cat-autocomplete/cat-autocomplete.component";
import { DOCUMENT } from "@angular/common";
import { CatFormFieldComponent } from "../../cat-form-field/cat-form-field.component";
import { CatOptionComponent } from "../../cat-option/cat-option/cat-option.component";

@Directive({
	selector: "input[pactoCatAutocomplete], textarea[pactoCatAutocomplete]",
	exportAs: "pactoCatAutocompleteTrigger",
})
export class CatAutocompleteTriggerDirective
	implements OnInit, AfterContentInit
{
	@Input() pactoCatAutocomplete: CatAutocompleteComponent;
	overlay: HTMLElement;

	constructor(
		private elementRef: ElementRef,
		private renderer2: Renderer2,
		@Inject(DOCUMENT) private document,
		@Optional() @Host() private _formField: CatFormFieldComponent
	) {}

	ngOnInit() {
		this.createOverlay();
		this.renderer2.setAttribute(
			this.elementRef.nativeElement,
			"autocomplete",
			"off"
		);
		this.renderer2.setStyle(
			this.elementRef.nativeElement,
			"position",
			"relative"
		);
	}

	ngAfterContentInit() {
		if (this.pactoCatAutocomplete) {
			if (this.pactoCatAutocomplete.options) {
				this.pactoCatAutocomplete.options.changes.subscribe(
					(filteredQueryList) => {
						filteredQueryList.forEach((change: CatOptionComponent) => {
							change.selectionChange.subscribe((selectionChange) => {
								if (selectionChange.value) {
									this.closePanel();
								}
							});
						});
					}
				);
			}
		}
	}

	@HostListener("input", ["$event"])
	onInput(event) {
		const input = event.target as HTMLInputElement;
		const value: number | string | null = input.value;
		if (this.pactoCatAutocomplete) {
		}
		this.openPanel();
	}

	private openPanel() {
		this.overlay.appendChild(
			this.pactoCatAutocomplete.elementRef.nativeElement
		);
		const autocompleteRect: ClientRect | DOMRect =
			this.pactoCatAutocomplete.elementRef.nativeElement.getBoundingClientRect();
		const triggerRect: ClientRect | DOMRect =
			this.elementRef.nativeElement.parentElement.getBoundingClientRect();
		this.renderer2.setStyle(
			this.pactoCatAutocomplete.elementRef.nativeElement,
			"width",
			`${triggerRect.width}px`
		);
		this.renderer2.setStyle(
			this.pactoCatAutocomplete.elementRef.nativeElement,
			"left",
			`${triggerRect.left}px`
		);
		this.renderer2.setStyle(
			this.pactoCatAutocomplete.elementRef.nativeElement,
			"top",
			`${triggerRect.height + triggerRect.top}px`
		);
	}

	@HostListener("document:click", ["$event"])
	closePanelListerner(event) {
		if (event.target === this.pactoCatAutocomplete.elementRef.nativeElement) {
			return;
		}
		const innerClick =
			this.isDescendant(this.elementRef.nativeElement, event.target) ||
			this.isDescendant(
				this.pactoCatAutocomplete.elementRef.nativeElement,
				event.target
			);
		if (!innerClick) {
			this.closePanel();
		}
	}

	private closePanel() {
		if (this.overlay.children.length === 0) {
			return;
		}
		const children = Array.from(this.overlay.children);
		for (const child of children) {
			if (child.id.includes("cat-autocomplete")) {
				this.overlay.removeChild(
					this.pactoCatAutocomplete.elementRef.nativeElement
				);
				break;
			}
		}
		if (
			this._formField &&
			this._formField.catInputDirective &&
			this._formField.catInputDirective.control
		) {
			this._formField.catInputDirective.control.control.reset("", {
				emitEvent: false,
			});
		}
		this.elementRef.nativeElement.value = "";
	}

	// TODO Lucas(Feijão) This must become a component
	private createOverlay() {
		this.overlay = this.document.getElementById("pacto-cat-overlay");
		if (!this.overlay) {
			this.overlay = this.document.createElement("div");
			this.overlay.id = "pacto-cat-overlay";
			this.overlay.style.position = "absolute";
			this.overlay.style.top = "0px";
			this.overlay.style.bottom = "0px";
			this.overlay.style.width = "100%";
			this.overlay.style.pointerEvents = "none";
			this.overlay = document.body.appendChild(this.overlay);
		}
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}
}
