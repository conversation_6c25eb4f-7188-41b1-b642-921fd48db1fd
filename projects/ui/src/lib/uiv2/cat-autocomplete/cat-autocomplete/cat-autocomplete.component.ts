import {
	AfterContentInit,
	Component,
	ContentChildren,
	ElementRef,
	OnInit,
	QueryList,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";
import { CatOptionComponent } from "../../cat-option/cat-option/cat-option.component";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-autocomplete",
	templateUrl: "./cat-autocomplete.component.html",
	styleUrls: ["./cat-autocomplete.component.scss"],
	encapsulation: ViewEncapsulation.None,
	exportAs: "pactoCatAutocomplete",
})
export class CatAutocompleteComponent implements OnInit, AfterContentInit {
	@ContentChildren(CatOptionComponent) options: QueryList<CatOptionComponent>;

	constructor(public elementRef: ElementRef, private renderer2: Renderer2) {}

	ngOnInit() {
		this.renderer2.addClass(
			this.elementRef.nativeElement,
			"pacto-cat-autocomplete"
		);
		if (!this.elementRef.nativeElement.id) {
			this.renderer2.setAttribute(
				this.elementRef.nativeElement,
				"id",
				`cat-autocomplete-${uniqueId++}`
			);
		}
	}

	ngAfterContentInit() {
		if (this.options) {
			// TODO Lucas (Feijão) Finalizar a implementação para selecionar a opção levando em conta que no menu não
			// há seleção somente redirecionmente para a funcionalidade ou aluno.
			/*this.options.forEach(
                (c: CatOptionComponent) => {
                    /!*c.selectionChange.subscribe(
                        result => {
                        }
                    );*!/
                }
            );*/
		}
	}
}
