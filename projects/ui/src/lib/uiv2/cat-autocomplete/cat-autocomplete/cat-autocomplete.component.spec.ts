import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { CatAutocompleteComponent } from "./cat-autocomplete.component";

describe("CatAutocompleteComponent", () => {
	let component: CatAutocompleteComponent;
	let fixture: ComponentFixture<CatAutocompleteComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [CatAutocompleteComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(CatAutocompleteComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
