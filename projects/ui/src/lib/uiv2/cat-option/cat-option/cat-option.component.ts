import {
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnInit,
	Output,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-option",
	templateUrl: "./cat-option.component.html",
	styleUrls: ["./cat-option.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatOptionComponent implements OnInit {
	@Input() value: any;
	@Input() selected: boolean;
	@Output() selectionChange: EventEmitter<{ value: any }> = new EventEmitter<{
		value: any;
	}>();

	constructor(private elementRef: ElementRef, private renderer2: Renderer2) {}

	ngOnInit() {
		this.renderer2.addClass(this.elementRef.nativeElement, "pacto-cat-option");
		if (!this.elementRef.nativeElement.id) {
			this.renderer2.setAttribute(
				this.elementRef.nativeElement,
				"id",
				`cat-option-${uniqueId++}`
			);
		}
	}

	@HostListener("click", ["$event"])
	select(event) {
		if (!this.selected) {
			this.selected = true;
			this.selectionChange.emit({ value: true });
		}
	}

	unselect() {
		if (this.selected) {
			this.selected = false;
			this.selectionChange.emit({ value: false });
		}
	}
}
