import { Directive, ElementRef, HostListener, Input } from "@angular/core";

@Directive({
	selector: "[pactoOnlyNumber]",
})
export class OnlyNumberDirective {
	@Input() decimals = 0;
	@Input() maxlength = 0;

	private check(value: string) {
		if (this.decimals <= 0) {
			return value.match(new RegExp(/^\d+$/));
		} else {
			const regExpString =
				"^\\s*((\\d+(\\.\\d{0," +
				this.decimals +
				"})?)|((\\d*(\\.\\d{1," +
				this.decimals +
				"}))))\\s*$";
			return value.match(new RegExp(regExpString));
		}
	}

	private run(oldValue) {
		setTimeout(() => {
			const currentValue: string = this.getInputElement().value;
			if (currentValue !== "" && !this.check(currentValue)) {
				this.getInputElement().value = oldValue;
			}
		});
	}

	constructor(private el: ElementRef) {}

	@HostListener("keydown", ["$event"])
	onKeyDown(event: KeyboardEvent) {
		this.run(this.getInputElement().value);
	}

	getInputElement() {
		if (!(this.el.nativeElement instanceof HTMLInputElement)) {
			const inputWrapper = this.el.nativeElement.querySelector(".aux-wrapper");
			if (inputWrapper) {
				return inputWrapper.querySelector("input");
			}
		}
		return this.el.nativeElement;
	}

	@HostListener("paste", ["$event"])
	onPaste(event: ClipboardEvent) {
		this.run(this.getInputElement().value);
	}
}
