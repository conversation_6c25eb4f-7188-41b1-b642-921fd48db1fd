import { ControlValueAccessor } from "@angular/forms";

export class BaseControlValueAccessor<T> implements ControlValueAccessor {
	public disabled = false;
	public value: T;

	/**
	 * Call when value has changed programmatically
	 */
	// eslint-disable-next-line unused-imports/no-unused-vars-ts
	public onChange(v = null) {}

	public onTouched() {}

	/**
	 * Model -> View changes
	 */
	public writeValue(obj: T): void {
		this.value = obj;
	}

	public registerOnChange(fn: any): void {
		this.onChange = fn;
	}

	public registerOnTouched(fn: any): void {
		this.onTouched = fn;
	}

	public setDisabledState?(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}
}
