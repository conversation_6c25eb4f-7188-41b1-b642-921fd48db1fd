import { Component, Input, OnInit } from "@angular/core";

@Component({
	selector: "ds3-infos",
	templateUrl: "./ds3-infos.component.html",
	styleUrls: ["./ds3-infos.component.scss"],
})
export class Ds3InfosComponent implements OnInit {
	@Input() state: "disable" | "loss" | "alert" | "gain" | "default" = "default";
	@Input() size: number = 14;
	@Input() hover: boolean = false;
	@Input() icon: boolean = false;
	@Input() percent: boolean = false;
	@Input() coin: boolean = false;
	@Input() stateInfoComplementar:
		| "disable"
		| "loss"
		| "alert"
		| "gain"
		| "default" = "default";
	@Input() sizeInfoComplementar: number = 14;
	@Input() iconInfoComplementar: boolean = false;
	@Input() percentInfoComplementar: boolean = false;
	@Input() coinInfoComplementar: boolean = false;
	@Input() infoArray: {
		info: number;
		overline: string;
		infoComplementar: number;
	}[] = [];
	@Input() vertical: boolean = false;
	@Input() overline: boolean = true;
	@Input() avatar: boolean = false;
	@Input() iconButton: boolean = false;
	@Input() textButton: boolean = false;

	totalWidth: number = 0;
	hoverStates: Map<number, boolean> = new Map<number, boolean>();
	textoInternoButton: "Text Button";

	constructor() {}

	ngOnInit() {}

	getStateClasses(state: string) {
		return {
			disable: state === "disable",
			loss: state === "loss",
			alert: state === "alert",
			gain: state === "gain",
			default: state === "default",
		};
	}

	getColor(state: string): string {
		switch (state) {
			case "disable":
				return "hsla(220, 5%, 35%, 1)";
			case "loss":
				return "hsla(0, 96%, 35%, 1)";
			case "alert":
				return "hsla(60, 96%, 35%, 1)";
			case "gain":
				return "hsla(120, 96%, 35%, 1)";
			case "default":
			default:
				return "hsla(222, 96%, 55%, 1)";
		}
	}

	getHoverColor(state: string, index: number): string {
		const isHovered = this.hoverStates.get(index);
		if (this.hover && isHovered) {
			switch (state) {
				case "loss":
					return "hsla(347, 76%, 95%, 1)";
				case "alert":
					return "hsla(44, 76%, 95%, 1)";
				case "gain":
					return "hsla(148, 76%, 95%, 1)";
				case "default":
					return "hsla(223, 92%, 95%, 1)";
			}
		} else {
			return "transparent";
		}
	}

	onMouseEnter(index: number) {
		this.hoverStates.set(index, true);
	}

	onMouseLeave(index: number) {
		this.hoverStates.delete(index);
	}

	calculateHeight(): string {
		const itemHeight = 80;
		const totalHeight = this.infoArray.length * itemHeight;
		return `${totalHeight}px`;
	}

	getInfoFontSize(size: number): string {
		switch (size) {
			case 14:
				return "12px";
			case 16:
				return "14px";
			case 20:
				return "18px";
			case 24:
				return "22px";
			case 32:
				return "30px";
			case 42:
				return "40px";
			case 48:
				return "46px";
			default:
				return "12px";
		}
	}

	getIconSize(size: number): string {
		switch (size) {
			case 32:
				return "20px";
			case 42:
				return "20px";
			case 48:
				return "24px";
			default:
				return "14px";
		}
	}
}
