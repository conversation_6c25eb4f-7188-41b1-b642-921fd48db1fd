<div *ngIf="vertical">
	<div class="ds3-info">
		<div class="info-data">
			<ng-container *ngFor="let item of infoArray; let i = index">
				<div>
					<div class="info-data-geral">
						<div
							(mouseenter)="onMouseEnter(i)"
							(mouseleave)="onMouseLeave(i)"
							[ngStyle]="{ 'background-color': getHoverColor(this.state, i) }"
							class="info-data-container">
							<div
								*ngIf="icon == true"
								[ngStyle]="{
									width: getIconSize(this.size),
									'font-size': getIconSize(this.size),
									color: getColor(this.state)
								}"
								class="icon-info-group">
								<i class="pct pct-heart"></i>
							</div>
							<div
								*ngIf="coin == true"
								[ngStyle]="{
									'font-size': getIconSize(this.size),
									color: getColor(this.state)
								}"
								class="coin">
								R$
							</div>
							<div
								[ngStyle]="{
									'font-size': getInfoFontSize(this.size),
									color: getColor(this.state)
								}"
								class="info">
								{{ item.info }}
							</div>
							<div
								*ngIf="percent == true"
								[ngStyle]="{
									'font-size': getIconSize(this.size),
									color: getColor(this.state)
								}"
								class="percent">
								%
							</div>
						</div>
						<div
							(mouseenter)="onMouseEnter(i)"
							(mouseleave)="onMouseLeave(i)"
							[ngStyle]="{
								'background-color': getHoverColor(this.stateInfoComplementar, i)
							}"
							class="info-data-container">
							<div
								*ngIf="iconInfoComplementar == true"
								[ngStyle]="{
									width: getIconSize(this.sizeInfoComplementar),
									'font-size': getIconSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="icon-info-group">
								<i class="pct pct-heart"></i>
							</div>
							<div
								*ngIf="coinInfoComplementar == true"
								[ngStyle]="{
									'font-size': getIconSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="coin">
								R$
							</div>
							<div
								*ngIf="item.infoComplementar != null"
								[ngStyle]="{
									'font-size': getInfoFontSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="info">
								{{ item.infoComplementar }}
							</div>
							<div
								*ngIf="percentInfoComplementar == true"
								[ngStyle]="{
									'font-size': getIconSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="percent">
								%
							</div>
						</div>
					</div>
					<div class="info-data-aux">
						<ds3-avatar *ngIf="avatar == true" size="16"></ds3-avatar>
						<div *ngIf="overline == true" class="overline">
							<div class="overline-text">{{ item.overline }}</div>
						</div>
						<a
							*ngIf="iconButton == true"
							color="primary"
							ds3-icon-button
							size="sm">
							<i class="pct pct-external-link"></i>
						</a>
					</div>
					<div class="info-data-text-button">
						<a
							*ngIf="textButton == true"
							color="primary"
							ds3-text-button
							size="sm">
							textoInternoButton
						</a>
					</div>
					<div *ngIf="i < infoArray.length - 1" class="line-divider"></div>
				</div>
			</ng-container>
		</div>
	</div>
</div>
<div *ngIf="!vertical">
	<div class="ds3-info">
		<div class="info-data-horizontal">
			<ng-container *ngFor="let item of infoArray; let i = index">
				<div class="info-data-geral-container">
					<div class="info-data-geral">
						<div
							(mouseenter)="onMouseEnter(i)"
							(mouseleave)="onMouseLeave(i)"
							[ngStyle]="{ 'background-color': getHoverColor(this.state, i) }"
							class="info-data-container">
							<div
								*ngIf="icon == true"
								[ngStyle]="{
									width: getIconSize(this.size),
									'font-size': getIconSize(this.size),
									color: getColor(this.state)
								}"
								class="icon-info-group">
								<i class="pct pct-heart"></i>
							</div>
							<div
								*ngIf="coin == true"
								[ngStyle]="{
									'font-size': getIconSize(this.size),
									color: getColor(this.state)
								}"
								class="coin">
								R$
							</div>
							<div
								[ngStyle]="{
									'font-size': getInfoFontSize(this.size),
									color: getColor(this.state)
								}"
								class="info">
								{{ item.info }}
							</div>
							<div
								*ngIf="percent == true"
								[ngStyle]="{
									'font-size': getIconSize(this.size),
									color: getColor(this.state)
								}"
								class="percent">
								%
							</div>
						</div>
						<div
							(mouseenter)="onMouseEnter(i)"
							(mouseleave)="onMouseLeave(i)"
							[ngStyle]="{
								'background-color': getHoverColor(this.stateInfoComplementar, i)
							}"
							class="info-data-container">
							<div
								*ngIf="iconInfoComplementar == true"
								[ngStyle]="{
									width: getIconSize(this.sizeInfoComplementar),
									'font-size': getIconSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="icon-info-group">
								<i class="pct pct-heart"></i>
							</div>
							<div
								*ngIf="coinInfoComplementar == true"
								[ngStyle]="{
									'font-size': getIconSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="coin">
								R$
							</div>
							<div
								*ngIf="item.infoComplementar != null"
								[ngStyle]="{
									'font-size': getInfoFontSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="info">
								{{ item.infoComplementar }}
							</div>
							<div
								*ngIf="percentInfoComplementar == true"
								[ngStyle]="{
									'font-size': getIconSize(this.sizeInfoComplementar),
									color: getColor(this.stateInfoComplementar)
								}"
								class="percent">
								%
							</div>
						</div>
						<div
							*ngIf="i < infoArray.length - 1"
							class="line-divider-horizontal"></div>
					</div>
					<div class="info-data-aux">
						<ds3-avatar *ngIf="avatar == true" size="16"></ds3-avatar>
						<div *ngIf="overline == true" class="overline">
							<div class="overline-text">{{ item.overline }}</div>
						</div>
						<a
							*ngIf="iconButton == true"
							color="primary"
							ds3-icon-button
							size="sm">
							<i class="pct pct-external-link"></i>
						</a>
					</div>
					<div class="info-data-text-button">
						<a
							*ngIf="textButton == true"
							color="primary"
							ds3-text-button
							size="sm">
							textoInternoButton
						</a>
					</div>
				</div>
			</ng-container>
		</div>
	</div>
</div>
