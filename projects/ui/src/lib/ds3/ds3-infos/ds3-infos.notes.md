**Seletor:** `ds3-infos`

**Utilização:**`<ds3-infos></ds3-infos>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    infoArray = [{info: 10, overline: 'Contrato 1', infoComplementar: 0}, {info: 20, overline: 'Contrato 2', infoComplementar: 10}, {info: 30, overline: 'Contrato 3', infoComplementar: 5}]
}
```

> meu-exemplo-de-tela.component.html

```html
    <ds3-infos 
        [infoArray]="infoArray" 
        [icon]='icon' 
        [coin]='coin' 
        [percent]='percent' 
        [size]="size" 
        [hover]="hover" 
        [state]="state"
        [vertical]="vertical">
    </ds3-infos>
```
&nbsp;  

## Props / Inputs

| Property                                 | Values                                                      | Default              |
|---                                       |                                                          ---|                   ---|
| @Input() state: string                   |'disable', 'loss', 'alert', 'gain', 'default'                |'default'             |
| @Input() size: number                    | 14, 16, 20, 24, 32, 42, 48                                  |14                    |
| @Input() hover: boolean                  |                                                             |false                 |
| @Input() icon: boolean                   |                                                             |false                 |
| @Input() percent: boolean                |                                                             |false                 |
| @Input() coin: boolean                   |                                                             |false                 |
| @Input() infoArray: array                |{ info: number, overline: string, infoComplementar: number } |[]                    |
| @Input() vertical: boolean               |                                                             |false                 |

## More info

No more info.
