import { CommonModule } from "@angular/common";
import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
import {
	array,
	boolean,
	number,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
//@ts-ignore
import Notes from "./ds3-info-card.notes.md";

export default {
	title: "Design System 3 | Data/Info Card",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, CommonModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: Notes,
	},
};

const infoArray = [
	{ info: 10, overline: "Contrato 1", infoComplementar: 0 },
	{ info: 20, overline: "Contrato 2", infoComplementar: 10 },
	{ info: 30, overline: "Contrato 3", infoComplementar: 5 },
];
const size = [14, 16, 20, 24, 32, 42, 48];
const state = ["default", "disable", "loss", "alert", "gain"];
export const infos = () => {
	return {
		template: `
        <div class="col-md-4">
          <ds3-info-card [infoArray]="infoArray" 
            [icon]='icon'
            [showIcon]='showIcon'
            [coin]='coin' 
            [percent]='percent' 
            [size]="size"
            [hover]="hover" 
            [state]="state" 
            [iconInfoComplementar]='iconInfoComplementar' 
            [coinInfoComplementar]='coinInfoComplementar' 
            [percentInfoComplementar]='percentInfoComplementar' 
            [sizeInfoComplementar]="sizeInfoComplementar" 
            [stateInfoComplementar]="stateInfoComplementar" 
            [vertical]="vertical"
            [overline]="overline"
            [avatar]="avatar"
            [iconButton]="iconButton"
            [textButton]="textButton"
          ></ds3-info-card>
        </div>
      `,
		props: {
			infoArray: object("Informações", infoArray),
			showIcon: boolean("Mostrar Ícone", true),
			icon: text("Ícone", "pct pct-heart"),
			coin: boolean("Moeda", true),
			percent: boolean("Porcentagem", true),
			size: select("Tamanho", size, size[0]),
			hover: boolean("Hover", true),
			state: select("Estado", state, state[0]),
			iconInfoComplementar: boolean("Ícone Info Complementar", false),
			coinInfoComplementar: boolean("Moeda Info Complementar", false),
			percentInfoComplementar: boolean("Porcentagem Info Complementar", true),
			sizeInfoComplementar: select("Tamanho Info Complementar", size, size[0]),
			stateInfoComplementar: select(
				"Estado Info Complementar",
				state,
				state[4]
			),
			vertical: boolean("Vertical", false),
			overline: boolean("Overline", true),
			avatar: boolean("Avatar", false),
			iconButton: boolean("Icon Button", false),
			textButton: boolean("Text Button", false),
		},
	};
};
