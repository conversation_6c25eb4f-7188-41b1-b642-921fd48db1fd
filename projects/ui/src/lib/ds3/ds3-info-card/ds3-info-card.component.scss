@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

.ds3-info {
	box-sizing: border-box;
	display: inline-block;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 16px;
	gap: 16px;
	width: auto;
	height: auto;
	border: 1px solid $supportGray02;
	border-radius: 8px;
}

.ds3-info.hovered {
	cursor: pointer;
}

.info-data {
	display: inline;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 0px;
	gap: 8px;
	width: 46px;
	height: auto;
	flex: none;
	order: 0;
	flex-grow: 0;
}

.info-data-horizontal {
	display: flex;
	flex-direction: row;
	justify-content: space-around;
	align-items: center;
	padding: 0px;
	gap: 16px;
	width: auto;
	height: auto;
	flex: none;
	order: 0;
	flex-grow: 0;
}

.info-data-aux {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
}

.info-data-text-button {
	text-align: center;
}

.info-container {
	display: flex;
	flex-direction: column;
}

.info-container:not(:first-child) {
	border-left: 1px solid $supportGray02;
	padding-left: 20px;
}

.info-data-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	border-radius: 4px;
	width: fit-content;
	padding: 4px 8px;
}

.info-data-geral {
	display: flex;
}

.info-data-geral-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}

.icon-info-group,
.coin,
.info,
.percent {
	display: flex;
	align-items: center;
	letter-spacing: 0.25px;
	flex: none;
	flex-grow: 0;
	@extend .pct-display4;
}

.icon-info-group {
	margin-right: 4px;
}

.coin {
	height: 14px;
}

.info {
	text-align: center;
	margin-right: 4px;
}

.overline {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	height: 16px;
	flex: none;
	flex-grow: 0;
}

.overline-text {
	width: 60px;
	height: 16px;
	font-size: 12px;
	display: flex;
	align-items: center;
	text-align: center;
	margin-top: 5%;
	color: $supportGray07;
	@extend .pct-overline2;
}

.line-divider {
	width: 100%;
	height: 1px;
	background-color: $supportGray03;
	margin-top: 16px;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 15px;
}

.line-divider-horizontal {
	width: 0.5px;
	height: 100%;
	background-color: $supportGray03;
	position: absolute;
	top: 0;
	left: 102%;
}
