// @ts-ignore
import Notes from "./ds3-buttons.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, select, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";
import { borderColors, iconsName } from "../stories-utils";

const description = () => Notes;

const BUTTON_SIZES = [
	{
		id: "lg",
		label: "Large",
	},
	{
		id: "sm",
		label: "Small",
	},
];

const BUTTON_COLORS = [
	{
		id: "primary",
		label: "Primary",
	},
	{
		id: "secondary",
		label: "Secondary",
	},
];

export default {
	title: "Design System 3 | Actions/Buttons",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const FlatButton = () => ({
	template: `
		<div style="padding: 16px; display:column-flex;">
			<button ds3-flat-button [color]="color?.id"
            [size]="size?.id"
            [disabled]="disabled"
[class]="class">{{textoInternoButton}}</button>
            <hr>
            <a ds3-flat-button  [color]="color?.id"
            [size]="size?.id"
            [class]="class">{{textoInternoAnchor}}</a>
		</div>
    `,
	props: {
		textoInternoButton: text("Texto Button", "texto interno button"),
		textoInternoAnchor: text("Texto anchor", "texto interno anchor"),
		size: select("size", BUTTON_SIZES, { id: "lg", label: "Large" }),
		color: select("color", BUTTON_COLORS, { id: "primary", label: "Primary" }),
		class: text("class", "classe"),
		disabled: boolean("Disabled", false),
	},
});

export const OutlinedButton = () => ({
	template: `
		<div style="padding: 16px; display:column-flex;">
			<button ds3-outlined-button [color]="color?.id"
            [size]="size?.id"
            [disabled]="disabled"
[class]="class">{{textoInternoButton}}</button>
            <hr>
            <a ds3-outlined-button   [color]="color?.id"
            [size]="size?.id"
            [class]="class">{{textoInternoAnchor}}</a>
		</div>
    `,
	props: {
		textoInternoButton: text("Texto Button", "texto interno button"),
		textoInternoAnchor: text("Texto anchor", "texto interno anchor"),
		size: select("size", BUTTON_SIZES, { id: "lg", label: "Large" }),
		color: select("color", BUTTON_COLORS, { id: "primary", label: "Primary" }),
		disabled: boolean("Disabled", false),
		class: text("class", "classe"),
	},
});

export const IconButton = () => ({
	template: `
		<div style="padding: 16px; display:column-flex;">
			<button ds3-icon-button [color]="color?.id"
            [size]="size?.id"
            [disabled]="disabled"
[class]="class"><i class="pct pct-{{iconClass}}"></i></button>
            <hr>
            <a ds3-icon-button [color]="color?.id"
            [size]="size?.id"
            [class]="class"><i class="pct pct-{{iconClass}}"></i></a>
		</div>
    `,
	props: {
		size: select("size", BUTTON_SIZES, { id: "lg", label: "Large" }),
		color: select("color", BUTTON_COLORS, { id: "primary", label: "Primary" }),
		class: text("class", "classe"),
		disabled: boolean("Disabled", false),
		iconClass: select("iconClass", iconsName, iconsName[10]),
	},
});

export const TextButton = () => ({
	template: `
		<div style="padding: 16px; display:column-flex;">
			<button ds3-text-button [color]="color?.id"
            [size]="size?.id"
            [disabled]="disabled"
[class]="class">{{textoInternoButton}}</button>
            <hr>
            <a ds3-text-button   [color]="color?.id"
            [size]="size?.id"
            [class]="class">{{textoInternoAnchor}}</a>
		</div>
    `,
	props: {
		textoInternoButton: text("Texto Button", "texto interno button"),
		textoInternoAnchor: text("Texto anchor", "texto interno anchor"),
		size: select("size", BUTTON_SIZES, { id: "lg", label: "Large" }),
		color: select("color", BUTTON_COLORS, { id: "primary", label: "Primary" }),
		class: text("class", "classe"),
		disabled: boolean("Disabled", false),
	},
});
