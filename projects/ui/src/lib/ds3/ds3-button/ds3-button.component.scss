@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts";
@import "projects/ui/assets/ds3/shadow/shadow.scss";

button,
a {
	&.ds3-button {
		cursor: pointer;
		outline: none !important;
		border: none;
		-webkit-tap-highlight-color: transparent;
		padding: 12px 20px;
		margin-bottom: unset;
		border-radius: 4px;
		display: inline-flex;
		justify-content: center;
		align-items: center;
		transition: 0.2s;

		&:enabled {
			&:hover:not(.ds3-icon-button) {
				@extend .sh-bg-desktop6;
			}
		}

		&.ds3-btn-size-sm {
			min-height: 32px;
			padding: 10px 16px;
			@extend .pct-btn-default2;
		}

		&.ds3-btn-size-lg {
			min-height: 40px;
			@extend .pct-btn-default1;
		}

		&.ds3-flat-button {
			&.ds3-btn-secondary {
				background: $actionDefaultRisk04;
				color: $typeBgDarkText;

				&:enabled {
					&:hover {
						background: $actionDefaultRisk03;
					}

					&:focus {
						background: $actionDefaultRisk05;
					}
				}
			}

			&.ds3-btn-primary {
				background: $actionDefaultAble04;
				color: $typeBgDarkText;

				&:enabled {
					&:hover {
						background: $actionDefaultAble03;
					}

					&:focus {
						background: $actionDefaultAble05;
					}
				}
			}

			&:disabled {
				background: $actionDefaultDisabled01;
				color: $actionDefaultDisabled02;
				cursor: not-allowed;
			}
		}

		&.ds3-outlined-button {
			background: none;
			border-width: 1px;
			border-style: solid;

			&.ds3-btn-secondary {
				border-color: $actionDefaultRisk04;
				color: $actionDefaultRisk04;

				&:enabled {
					&:hover {
						background: $actionDefaultRisk01;
					}

					&:focus {
						background: $actionDefaultRisk02;
					}
				}
			}

			&.ds3-btn-primary {
				border-color: $actionDefaultAble04;
				color: $actionDefaultAble04;

				&:enabled {
					&:hover {
						background: $actionDefaultAble01;
					}

					&:focus {
						background: $actionDefaultAble02;
					}
				}
			}

			&:disabled {
				border-color: $actionDefaultDisabled01;
				color: $actionDefaultDisabled02;
				cursor: not-allowed;
			}
		}

		&.ds3-icon-button {
			background: unset;
			padding: 8px;

			&.ds3-btn-size-sm {
				min-height: 28px;
				font-size: 14px;
			}

			&.ds3-btn-size-lg {
				min-height: 32px;
				font-size: 16px;
			}

			&.ds3-btn-secondary {
				color: $actionDefaultRisk04;

				&:enabled {
					&:hover {
						background: $actionDefaultRisk01;
						color: $actionDefaultRisk03;
					}

					&:focus {
						background: $actionDefaultRisk02;
						color: $actionDefaultRisk05;
					}
				}
			}

			&.ds3-btn-primary {
				color: $actionDefaultAble04;

				&:enabled {
					&:hover {
						background: $actionDefaultAble01;
						color: $actionDefaultAble03;
					}

					&:focus {
						background: $actionDefaultAble02;
						color: $actionDefaultAble05;
					}
				}
			}

			&:disabled {
				color: $actionDefaultDisabled02;
				cursor: not-allowed;
			}
		}

		&.ds3-text-button {
			padding: 13px 8px;

			&.ds3-btn-size-sm {
				min-height: 32px;
				@extend .pct-btn-default2;

				&.btn-with-image {
					padding: 4px 8px;
					gap: 8px;

					img {
						border-radius: 50%;
						height: 24px;
						width: 24px;
					}

					&:enabled {
						&:hover {
							img {
								border: 1px solid white;
							}
						}
					}
				}
			}

			&.ds3-btn-size-lg {
				padding: 10px 8px;
				@extend .pct-btn-default1;
			}

			&.ds3-btn-primary {
				background: unset;
				color: $actionDefaultAble04;

				&:enabled {
					&:hover {
						color: $actionDefaultAble03;
						background: $actionDefaultAble01;
					}

					&:focus {
						color: $actionDefaultAble05;
						background: $actionDefaultAble02;
					}
				}
			}

			&.ds3-btn-secondary {
				background: unset;
				color: $actionDefaultRisk04;

				&:enabled {
					&:hover {
						color: $actionDefaultRisk03;
						background: $actionDefaultRisk01;
					}

					&:focus {
						color: $actionDefaultRisk05;
						background: $actionDefaultRisk02;
					}
				}
			}

			&:disabled {
				background: $actionDefaultDisabled01;
				color: $actionDefaultDisabled02;
				cursor: not-allowed;
			}
		}
	}
}
