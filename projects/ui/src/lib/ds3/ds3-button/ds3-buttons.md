# Buttons

**Table of content:**

-   [Selectors](#selectors)
    -   [flat](#seletor_flat)
    -   [outline](#seletor_outline)
    -   [text](#seletor_text)
    -   [icon](#seletor_icon)
-   [Inputs](#inputs)
-   [Outputs](#outputs)
-   [Example](#example)

## selectors

### seletor_flat

```html
<button ds3-flat-button></button>
<!-- Ou -->
<a ds3-flat-button></a>
```

### seletor_outline

```html
<button ds3-text-button></button>
<!-- Ou -->
<a ds3-text-button></a>
```

### seletor_text

```html
<button ds3-outlined-button></button>
<!-- Ou -->
<a ds3-ouline-button></a>
```

### seletor_icon

```html
<button ds3-icon-button></button>
<!-- Ou -->
<a ds3-icon-button></a>
```

## inputs

| inputName          | descricao             | possiveis valores        | valor inicial |
| ------------------ | --------------------- | ------------------------ | ------------- |
| size **opcional**  | define tamanho        | 'lg' \| 'sm'             | 'lg'          |
| color **opcional** | define tipo de uso    | 'primary' \| 'secondary' | 'primary'     |
| class **opcional** | define tipo de classe | string                   | nenhum        |

## outputs

## example
