import {
	Component,
	ElementRef,
	HostListener,
	Input,
	OnChanges,
	OnInit,
	Renderer2,
	SimpleChange,
	SimpleChanges,
	ViewEncapsulation,
} from "@angular/core";
import { getHostElement, hasHostAttributes } from "../../ds3-ui-utils";

let uniqueId = 0;

const BUTTON_TYPES = [
	"ds3-flat-button",
	"ds3-outlined-button",
	"ds3-icon-button",
	"ds3-text-button",
];

@Component({
	selector:
		"button[ds3-flat-button],a[ds3-flat-button]," +
		"button[ds3-outlined-button],a[ds3-outlined-button]," +
		"button[ds3-icon-button],a[ds3-icon-button]," +
		"button[ds3-text-button],a[ds3-text-button]",
	templateUrl: "./ds3-button.component.html",
	styleUrls: ["./ds3-button.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3ButtonComponent implements OnInit, OnChanges {
	private hostElement;

	@Input()
	size: "lg" | "sm" = "lg";

	@Input()
	color: "primary" | "secondary" = "primary";

	@Input()
	class: string;

	constructor(public elementRef: ElementRef, private renderer2: Renderer2) {}

	ngOnInit() {
		this.hostElement = getHostElement(this.elementRef);
		this.renderer2.addClass(this.hostElement, "ds3-button");
		this.addId();
		this.addTypeClass();
		this.addSize();
		this.addColor();
		if (this.class) {
			const classList = this.class.split(" ");
			classList.forEach((clazz) =>
				this.renderer2.addClass(this.hostElement, clazz)
			);
		}
	}

	ngOnChanges(changes: SimpleChanges) {
		const color: SimpleChange = changes.color;
		if (color && !color.firstChange) {
			this.addColor(color.previousValue);
		}
		const size: SimpleChange = changes.size;
		if (size && !size.firstChange) {
			this.addSize(size.previousValue);
		}
	}

	@HostListener("click")
	onClick() {
		this.hostElement.blur();
	}

	private addId() {
		if (!hasHostAttributes(this.hostElement, "id")) {
			this.renderer2.setAttribute(
				this.hostElement,
				"id",
				`ds3-btn-${uniqueId++}`
			);
		}
	}

	private addColor(previousColor?: string) {
		if (this.color) {
			if (previousColor) {
				this.renderer2.removeClass(
					this.hostElement,
					`ds3-btn-${previousColor}`
				);
			}
			this.renderer2.addClass(this.hostElement, `ds3-btn-${this.color}`);
		}
	}

	private addSize(previousSize?: string) {
		if (previousSize) {
			this.renderer2.removeClass(
				this.hostElement,
				`ds3-btn-size-${previousSize}`
			);
		}
		this.renderer2.addClass(this.hostElement, `ds3-btn-size-${this.size}`);
	}

	private addTypeClass() {
		for (const attr of BUTTON_TYPES) {
			if (hasHostAttributes(this.hostElement, attr)) {
				this.renderer2.addClass(this.hostElement, attr);
			}
		}
	}
}
