import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { MatMenuModule } from "@angular/material";
import { Ds3AccordionModule } from "./ds3-accordion/ds3-accordion.module";
import { Ds3AvatarModule } from "./ds3-avatar/ds3-avatar.module";
import { Ds3BarLineComponent } from "./ds3-bar-line/ds3-bar-line.component";
import { Ds3BiInfoModule } from "./ds3-bi-info/ds3-bi-info.module";
import { Ds3BreadcrumbsModule } from "./ds3-breadcrumbs/ds3-breadcrumbs.module";
import { Ds3ButtonModule } from "./ds3-button/ds3-button.module";
import { Ds3ChartsModule } from "./ds3-charts/ds3-charts.module";
import { Ds3CheckoutComponent } from "./ds3-checkout/ds3-checkout.component";
import { Ds3ChipsModule } from "./ds3-chips/ds3-chips.module";
import { Ds3DiviserModule } from "./ds3-diviser/ds3-diviser.module";
import { Ds3CheckboxModule } from "./ds3-forms/ds3-checkbox/ds3-checkbox.module";
import { Ds3ColorPickerModule } from "./ds3-forms/ds3-color-picker/ds3-color-picker.module";
import { Ds3FormFieldModule } from "./ds3-forms/ds3-form-field/ds3-form-field.module";
import { Ds3GroupedSelectFilterModule } from "./ds3-forms/ds3-grouped-select-filter/ds3-grouped-select-filter.module";
import { Ds3InputDateModule } from "./ds3-forms/ds3-input-date/ds3-input-date.module";
import { Ds3NumberFieldModule } from "./ds3-forms/ds3-number-field/ds3-number-field.module";
import { Ds3SelectModule } from "./ds3-forms/ds3-select/ds3-select.module";
import { Ds3InfosComponent } from "./ds3-infos/ds3-infos.component";
import { Ds3PactoLogoComponent } from "./ds3-pacto-logo/ds3-pacto-logo.component";
import { Ds3PaginationModule } from "./ds3-pagination/ds3-pagination.module";
import { Ds3ShortcutTabModule } from "./ds3-shortcut-tab/ds3-shortcut-tab.module";
import { Ds3StatusModule } from "./ds3-status/ds3-status.module";
import { Ds3StepsModule } from "./ds3-steps/ds3-steps.module";
import { Ds3SwitchComponent } from "./ds3-switch/ds3-switch.component";
import { Ds3TabsComponent } from "./ds3-tabs/ds3-tabs.component";
import { Ds3ToastrModule } from "./ds3-toastr/ds3-toastr.module";
import { Ds3TooltipModule } from "./ds3-tooltip/ds3-tooltip.module";
import { Ds3UploadModule } from "./ds3-upload/ds3-upload.module";
import { Ds3CountryPhoneModule } from "./ds3-forms/ds3-country-phone/ds3-country-phone.module";
import { Ds3TableModule } from "./ds3-table/ds3-table.module";

@NgModule({
	declarations: [
		Ds3BarLineComponent,
		Ds3CheckoutComponent,
		Ds3InfosComponent,
		Ds3PactoLogoComponent,
		Ds3SwitchComponent,
		Ds3TabsComponent,
	],
	imports: [
		CommonModule,
		Ds3AccordionModule,
		Ds3AvatarModule,
		Ds3BreadcrumbsModule,
		Ds3ButtonModule,
		Ds3ChartsModule,
		Ds3ChipsModule,
		Ds3DiviserModule,
		Ds3CheckboxModule,
		Ds3ColorPickerModule,
		Ds3FormFieldModule,
		Ds3GroupedSelectFilterModule,
		Ds3InputDateModule,
		Ds3NumberFieldModule,
		Ds3SelectModule,
		Ds3PaginationModule,
		Ds3ShortcutTabModule,
		Ds3CheckboxModule,
		Ds3DiviserModule,
		Ds3ChipsModule,
		Ds3GroupedSelectFilterModule,
		Ds3StatusModule,
		Ds3StepsModule,
		Ds3ToastrModule,
		Ds3UploadModule,
		MatMenuModule,
		Ds3BiInfoModule,
		Ds3CountryPhoneModule,
		Ds3TableModule,
		Ds3TooltipModule,
	],
	exports: [
		Ds3BarLineComponent,
		Ds3CheckoutComponent,
		Ds3InfosComponent,
		Ds3PactoLogoComponent,
		Ds3SwitchComponent,
		Ds3TabsComponent,
		Ds3AccordionModule,
		Ds3AvatarModule,
		Ds3BreadcrumbsModule,
		Ds3ButtonModule,
		Ds3ChartsModule,
		Ds3ChipsModule,
		Ds3DiviserModule,
		Ds3CheckboxModule,
		Ds3ColorPickerModule,
		Ds3FormFieldModule,
		Ds3GroupedSelectFilterModule,
		Ds3InputDateModule,
		Ds3DiviserModule,
		Ds3ChipsModule,
		MatMenuModule,
		Ds3PaginationModule,
		Ds3NumberFieldModule,
		Ds3SelectModule,
		Ds3ShortcutTabModule,
		Ds3StatusModule,
		Ds3StepsModule,
		Ds3ToastrModule,
		Ds3UploadModule,
		Ds3BiInfoModule,
		Ds3CountryPhoneModule,
		Ds3TableModule,
		Ds3TooltipModule,
	],
})
export class Ds3Module {}
