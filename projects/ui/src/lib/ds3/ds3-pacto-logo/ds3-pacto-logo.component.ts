import {
	Compo<PERSON>,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnChanges,
	OnInit,
	Output,
	Renderer2,
	<PERSON>Change,
	SimpleChanges,
} from "@angular/core";

export enum PactoLogoSize {
	XSM = "16px",
	SM = "24px",
	LG = "32px",
	XLG = "40px",
}

export class PactoLogoModule {
	public static readonly ADM: PactoLogoModule = new PactoLogoModule(
		"adm",
		"Adm"
	);
	public static readonly CV: PactoLogoModule = new PactoLogoModule(
		"cv",
		"Central de eventos"
	);
	public static readonly CRM: PactoLogoModule = new PactoLogoModule(
		"crm",
		"CRM"
	);
	public static readonly FIN: PactoLogoModule = new PactoLogoModule(
		"fin",
		"Financeiro"
	);
	public static readonly GOR: PactoLogoModule = new PactoLogoModule(
		"gor",
		"Game of results"
	);
	public static readonly TR: PactoLogoModule = new PactoLogoModule(
		"tr",
		"Tre<PERSON>"
	);
	public static readonly AGN: PactoLogoModule = new PactoLogoModule(
		"agn",
		"Agenda"
	);
	public static readonly CR: PactoLogoModule = new PactoLogoModule(
		"cr",
		"Cross"
	);
	public static readonly AV: PactoLogoModule = new PactoLogoModule(
		"av",
		"Avaliação física"
	);
	public static readonly GRD: PactoLogoModule = new PactoLogoModule(
		"grd",
		"Graduação"
	);
	public static readonly CCL: PactoLogoModule = new PactoLogoModule(
		"ccl",
		"Canal do cliente"
	);
	public static readonly NF: PactoLogoModule = new PactoLogoModule(
		"nf",
		"Nota fiscal"
	);
	public static readonly PAY: PactoLogoModule = new PactoLogoModule(
		"pay",
		"Pactopay"
	);
	public static readonly EST: PactoLogoModule = new PactoLogoModule(
		"est",
		"Estúdio"
	);

	private readonly _id;
	private readonly _description;

	private constructor(id: string, description: string) {
		this._id = id;
		this._description = description;
	}

	get id() {
		return this._id;
	}

	get description() {
		return this._description;
	}
}

@Component({
	selector: "ds3-pacto-logo",
	templateUrl: "./ds3-pacto-logo.component.html",
	styleUrls: ["./ds3-pacto-logo.component.scss"],
})
export class Ds3PactoLogoComponent implements OnInit, OnChanges {
	@Input() size: PactoLogoSize = PactoLogoSize.SM;
	@Input() module: PactoLogoModule;
	@Input() outlined: boolean;

	@Output() click: EventEmitter<any> = new EventEmitter<any>();

	hasClickHandler: boolean;
	logoSize = () => {
		switch (this.size) {
			case PactoLogoSize.XSM:
				return "xsm";
			case PactoLogoSize.SM:
				return "sm";
			case PactoLogoSize.LG:
				return "lg";
			case PactoLogoSize.XLG:
				return "xlg";
		}
	};

	private prefixLogoBgClass = "cat-pct-logo-bg-";

	constructor(private renderer: Renderer2, private el: ElementRef) {}

	ngOnInit() {
		if (!this.module) {
			throw Error("You must pass the input module!");
		}

		this.configureSize();
		this.configureModule();
		this.configureOutlined();

		this.hasClickHandler = this.click.observers.length > 0;
	}

	@HostListener("mouseover")
	onMouseOver() {
		if (this.hasClickHandler) {
			this.renderer.setStyle(this.el.nativeElement, "cursor", "pointer");
			this.renderer.setStyle(this.el.nativeElement, "opacity", ".5");
		}
	}

	@HostListener("mouseout")
	onMouseOut() {
		if (this.hasClickHandler) {
			this.renderer.removeStyle(this.el.nativeElement, "cursor");
			this.renderer.removeStyle(this.el.nativeElement, "opacity");
		}
	}

	ngOnChanges(changes: SimpleChanges) {
		const size: SimpleChange = changes.size;
		if (size && !size.firstChange) {
			this.configureSize();
		}

		const module: SimpleChange = changes.module;
		if (module && !module.firstChange) {
			this.configureModule(module.previousValue);
		}

		const outlined: SimpleChange = changes.outlined;
		if (outlined && !outlined.firstChange) {
			this.configureOutlined();
		}
	}

	private configureSize() {
		this.renderer.setStyle(this.el.nativeElement, "width", this.size);
		this.renderer.setStyle(this.el.nativeElement, "height", this.size);
	}

	private configureModule(previousModule?: PactoLogoModule) {
		const logoClass = `${this.prefixLogoBgClass}${this.module.id}`;
		if (previousModule) {
			this.renderer.removeClass(
				this.el.nativeElement,
				`${this.prefixLogoBgClass}${previousModule.id}`
			);
		}
		this.renderer.addClass(this.el.nativeElement, logoClass);
	}

	private configureOutlined() {
		if (this.outlined || this.el.nativeElement.hasAttribute("outlined")) {
			this.renderer.addClass(this.el.nativeElement, "cat-pct-logo-outlined");
		} else {
			this.renderer.removeClass(this.el.nativeElement, "cat-pct-logo-outlined");
		}
	}
}
