// import { moduleMetadata } from '@storybook/angular';
// import { boolean, select, text, withKnobs } from '@storybook/addon-knobs';

// import { CatPactoLogoModule } from '../../../lib/ds3/cat-pacto-logo/cat-pacto-logo.module';

// import Notes from './cat-pacto-logo.md';
// import { PactoLogoModule, PactoLogoSize } from '../../../lib/ds3/cat-pacto-logo/cat-pacto-logo.component';

// export default {
// 	title: 'Design System 3 | Pacto logo',
// 	decorators: [
// 		moduleMetadata({
// 			imports: [CatPactoLogoModule]
// 		}),
// 		withKnobs,
// 	],
// 	argTypes: {
// 		click: {
// 			action: 'click'
// 		}
// 	}
// };

// const sizes = Object.keys(PactoLogoSize).map(key => PactoLogoSize[key]);
// const modules = Object.keys(PactoLogoModule).map(key => ({label: PactoLogoModule[key].description, id: PactoLogoModule[key].id}));

// export const pactoLogo = () => ({
// 	template: `
// 		<div style="padding: 2rem" [style.background-color]="outlined ? 'hsla(222, 75%, 25%, 1)' : 'unset'">
// 			<pacto-cat-pacto-logo
// 				[size]="size"
// 				[module]="module"
// 				[outlined]="outlined"
// 			></pacto-cat-pacto-logo>
// 		</div>
//     `,
// 	props: {
// 		size: select('size', sizes, PactoLogoSize.SM),
// 		module: select('module', modules, {
// 			id: PactoLogoModule.ADM.id,
// 			label: PactoLogoModule.ADM.description
// 		}),
// 		outlined: boolean('outlined', false),
// 	}
// });

// pactoLogo.story = {
// 	parameters: {
// 		notes: { Notes },
// 		knobs: {
// 			timestamps: true
// 		}
// 	}
// };
