<!-- # Cat Pacto logo

`selector: pacto-cat-pacto-logo`

`Basic usage: <pacto-cat-pacto-logo></pacto-cat-pacto-logo>`

`Basic example:`

```ts
import { Component, NgModule } from '@angular/core';
import { CatLogoModule, PactoLogoSize, PactoLogoModule } from 'ui-kit';

@NgModule({
    declarations: [...],
    imports: [
        ...,
	    CatLogoModule,
        ...
    ]
})
class MeuAppModule {
}
```

**meu-app.module.ts**

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	module = PactoLogoModule;
	size = PactoLogoSize;

}
```

**meu-exemplo-de-tela.component.ts**

```html

<pacto-cat-pacto-logo
	[module]="module.ADM"
	[size]="size.SM"
	outlined
>
</pacto-cat-pacto-logo>
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>module <span class="bubble-sm danger">required</span></td>
            <td>
               Define qual será o estilo utilizado a partir do módulo passado. [Modules table](#modulesTable)
            </td>
            <td>```PactoLogoModule```</td>
            <td>ADM</td>
        </tr>
        <tr>
            <td>size <span class="bubble-sm info">optional</span></td>
            <td>
                Define qual será o estilo utilizado a partir do módulo passado. [Size table](#sizeTable)
                </td>
            <td>```PactoLogoSize```</td>
            <td>SM</td>
        </tr>
        <tr>
            <td>outlined <span class="bubble-sm danger">required</span></td>
            <td>
                Define se a logo possuirá ou não o outline.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
    </tbody>
</table>

## Outputs / Events

<div class="bubble info">
    <p>Component has no custom output.</p>
</div>

## More info

<h3 id="modulesTable">Module table</h3>

<table class="custom">
    <thead>
        <tr>
            <th>Module</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>`ADM`</td>
        </tr>
        <tr>
            <td>`CV`</td>
        </tr>
        <tr>
            <td>`CRM`</td>
        </tr>
        <tr>
            <td>`FIN`</td>
        </tr>
        <tr>
            <td>`GOR`</td>
        </tr>
        <tr>
            <td>`TR`</td>
        </tr>
        <tr>
            <td>`AGN`</td>
        </tr>
        <tr>
            <td>`CR`</td>
        </tr>
        <tr>
            <td>`AV`</td>
        </tr>
        <tr>
            <td>`GRD`</td>
        </tr>
        <tr>
            <td>`CCL`</td>
        </tr>
        <tr>
            <td>`NF`</td>
        </tr>
        <tr>
            <td>`PAY`</td>
        </tr>
        <tr>
            <td>`EST`</td>
        </tr>
    </tbody>
</table>

<h3 id="sizeTable">Size table</h3>

<table class="custom">
    <thead>
        <tr>
            <th>Size</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>`XSM`</td>
        </tr>
        <tr>
            <td>`SM`</td>
        </tr>
        <tr>
            <td>`LG`</td>
        </tr>
        <tr>
            <td>`XLG`</td>
        </tr>
    </tbody>
</table>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style> -->
