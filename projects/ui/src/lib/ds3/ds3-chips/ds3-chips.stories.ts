// @ts-ignore
import Notes from "./ds3-chips.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";

const CHIPS_LIST = [{ name: "<PERSON>" }, { name: "<PERSON><PERSON>" }, { name: "Apple" }];

const description = () => Notes;

export default {
	title: "Design System 3 | Actions/Chips",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chips-list>
				<ng-container *ngFor="let chip of chips">
					<ds3-chips
						[isRemovable]="isRemovable"
						[isDisabled]="isDisabled"
						(removed)="deleteChip(chip)"
					>{{chip.name}}</ds3-chips>
				</ng-container>
			</ds3-chips-list>
		</div>
    `,
	props: {
		isRemovable: boolean("Is removable", true),
		isDisabled: boolean("Is disabled", false),
		chips: CHIPS_LIST,
		deleteChip: (chip) => {
			const index = CHIPS_LIST.indexOf(chip);

			if (index >= 0) {
				CHIPS_LIST.splice(index, 1);
			}
		},
	},
});

export const removable = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chips-list>
				<ng-container *ngFor="let chip of chips">
					<ds3-chips
						[isRemovable]="isRemovable"
						(removed)="deleteChip(chip)"
					>{{chip.name}}</ds3-chips>
				</ng-container>
			</ds3-chips-list>
		</div>
    `,
	props: {
		isRemovable: boolean("Is removable", true),
		chips: CHIPS_LIST,
		deleteChip: (chip) => {
			const index = CHIPS_LIST.indexOf(chip);

			if (index >= 0) {
				CHIPS_LIST.splice(index, 1);
			}
		},
	},
});

export const defaultChips = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chips-list>
				<ng-container *ngFor="let chip of chips">
					<ds3-chips>{{chip.name}}</ds3-chips>
				</ng-container>
			</ds3-chips-list>
		</div>
    `,
	props: {
		chips: CHIPS_LIST,
	},
});

export const disabled = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chips-list>
				<ng-container *ngFor="let chip of chips">
					<ds3-chips
						[isDisabled]="isDisabled"
					>{{chip.name}}</ds3-chips>
				</ng-container>
			</ds3-chips-list>
		</div>
    `,
	props: {
		chips: CHIPS_LIST,
		isDisabled: boolean("Is disabled", true),
	},
});
