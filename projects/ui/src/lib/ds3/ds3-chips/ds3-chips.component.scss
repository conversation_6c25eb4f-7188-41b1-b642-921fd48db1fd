@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts";
@import "projects/ui/assets/ds3/animations";

.pct-chips {
	width: fit-content;
	border: 1px solid;
	padding: 3px 8px;
	border-radius: 100px;
	display: flex;
	align-items: center;
	cursor: pointer;
	gap: 8px;
	max-width: 300px;

	.pct-chips-label {
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	&.pct-chips-selected {
		border-color: $actionDefaultAble04;
		color: $actionDefaultDisabled02;
		cursor: default;

		i {
			position: relative;
			z-index: 2;
			color: $actionDefaultAble04;
			cursor: pointer;
		}

		&:hover {
			background-color: $actionDefaultAble01;
			border-color: $actionDefaultAble03;

			i {
				color: $actionDefaultAble03;
			}
		}
	}

	&.pct-chips-activated {
		background-color: $actionDefaultAble04;
		border-color: $actionDefaultAble04;
		color: $branco;

		&:hover {
			background-color: $actionDefaultAble03;
			border-color: $actionDefaultAble03;
		}
	}

	&.pct-chips-deactivated {
		border-color: $actionDefaultAble04;
		color: $actionDefaultDisabled02;

		&:hover {
			background-color: $actionDefaultAble01;
			border-color: $actionDefaultAble03;
		}
	}

	&.pct-chips-disabled {
		border-color: $actionDefaultDisabled02;
		color: $actionDefaultDisabled02;
		cursor: default;

		&:hover {
			border-color: $actionDefaultDisabled02;
			color: $actionDefaultDisabled02;
			background-color: transparent;
		}
	}
}
