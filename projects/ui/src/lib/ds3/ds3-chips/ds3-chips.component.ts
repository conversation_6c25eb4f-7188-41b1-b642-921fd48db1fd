import {
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { getHostElement } from "../../ds3-ui-utils";

@Component({
	selector: "ds3-chips",
	templateUrl: "./ds3-chips.component.html",
	styleUrls: ["./ds3-chips.component.scss"],
})
export class Ds3ChipsComponent implements OnInit {
	private hostElement;
	@ViewChild("pctChips", { static: false }) pctChips!: ElementRef;

	@Input() isActive = false;
	@Input() isRemovable = false;
	@Input() isDisabled = false;
	@Output() removed = new EventEmitter();
	@Output() selectionChange = new EventEmitter<boolean>();

	constructor(public elementRef: ElementRef) {}

	ngOnInit() {
		this.hostElement = getHostElement(this.elementRef);
	}

	emitRemoveEvent($event) {
		if (!this.isRemovable) {
			return;
		}

		this.removed.emit($event);
	}

	emitSelectionChangeEvent() {
		if (this.isRemovable || this.isDisabled) {
			return;
		}

		this.isActive = !this.isActive;
		this.selectionChange.emit(this.isActive);
	}
}
