<div
	(click)="emitSelectionChangeEvent()"
	[class.pct-chips-disabled]="isDisabled"
	[class.pct-chips-selected]="isRemovable"
	[ngClass]="
		isActive && !isRemovable && !isDisabled
			? 'pct-chips-activated'
			: 'pct-chips-deactivated'
	"
	class="pct-chips pct-overline2 pct-transition">
	<div class="pct-chips-label">
		<ng-content></ng-content>
	</div>
	<i
		(click)="emitRemoveEvent($event)"
		*ngIf="isRemovable && !isDisabled"
		class="pct pct-x"></i>
</div>
