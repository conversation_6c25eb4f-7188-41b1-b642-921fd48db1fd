**Seletor:** `ds3-chips`

**Utilização:**`<ds3-chips></ds3-chips>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    chips = [{name: 'Chip 1', name: 'Chip 2'}];
}
```

> meu-exemplo-de-tela.component.html

```html
<ng-container *ngFor="let chip of chips">
    <ds3-chips
        [isDisabled]="true"
    >{{chip.name}}</ds3-chips>
</ng-container>
```
&nbsp;  

## Props / Inputs

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Input() isDisabled: boolean             |                     |false                 |
| @Input() isRemovable: boolean            |                     |false                 |
| @Input() isActive: boolean               |                     |false                 |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Output() removed: EventEmitter          |                     |-                     |
| @Output() selectionChange: EventEmitter  |                     |-                     |

## More info

No more info.

&nbsp;  

# Chips List

> Componente de listagem de Chips do Pacto Design System.  
> Este componente oferece os estilos pré-definidos no design system para listar os chips corretamente.  
> Através deste componente também é possível trabalhar com Chips em Inputs.


Seletor: `ds3-chips-list`

Utilização:`<ds3-chips-list></ds3-chips-list>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    chips = [{name: 'Chip 1', name: 'Chip 2'}];
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chips-list>
    <ng-container *ngFor="let chip of chips">
        <ds3-chips
            [isDisabled]="true"
        >{{chip.name}}</ds3-chips>
    </ng-container>
</ds3-chips-list>
```
&nbsp;  

## Props / Inputs

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
|                                          |                     |                      |

## Outputs / Events

No outputs or events.

## More info

No more info.
