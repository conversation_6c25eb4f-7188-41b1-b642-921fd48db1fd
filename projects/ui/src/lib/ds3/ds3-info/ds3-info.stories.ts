import { CommonModule } from "@angular/common";
import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
import {
	array,
	boolean,
	number,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
//@ts-ignore
import Notes from "./ds3-info.notes.md";

export default {
	title: "Design System 3 | Data/Info",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, CommonModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: Notes,
	},
};

const size = [12, 14, 18, 22, 30, 40, 46];
const state = ["default", "disable", "loss", "alert", "gain"];

export const infos = () => {
	return {
		template: `
      <ds3-info
        [data]="30" 
        [icon]='icon'
        [showIcon]='showIcon' 
        [coin]='coin' 
        [percent]='percent' 
        [size]="size" 
        [hover]="hover"
        [state]="state" 
      ></ds3-info>
    `,
		props: {
			showIcon: boolean("Mostrar Ícone", true),
			icon: text("Ícone", "pct pct-heart"),
			coin: boolean("Moeda", true),
			percent: boolean("Porcentagem", true),
			size: select("Tamanho", size, size[0]),
			hover: boolean("Hover", false),
			state: select("Estado", state, state[0]),
		},
	};
};
