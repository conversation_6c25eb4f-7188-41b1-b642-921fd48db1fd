import { Component, Input, OnInit } from "@angular/core";

@Component({
	selector: "ds3-info",
	templateUrl: "./ds3-info.component.html",
	styleUrls: ["./ds3-info.component.scss"],
})
export class Ds3InfoComponent implements OnInit {
	@Input() state: "disable" | "loss" | "alert" | "gain" | "default" = "default";
	@Input() size: number = 12;
	@Input() hover: boolean = false;
	@Input() icon: string = "pct pct-heart";
	@Input() showIcon: boolean = false;
	@Input() percent: boolean = false;
	@Input() coin: boolean = false;
	@Input() data: number;

	hoverStates: Map<number, boolean> = new Map<number, boolean>();

	constructor() {}

	ngOnInit() {}

	getStateClasses(state: string) {
		return {
			disable: state === "disable",
			loss: state === "loss",
			alert: state === "alert",
			gain: state === "gain",
			default: state === "default",
		};
	}

	getColor(state: string): string {
		switch (state) {
			case "disable":
				return "hsla(220, 5%, 35%, 1)";
			case "loss":
				return "hsla(0, 96%, 35%, 1)";
			case "alert":
				return "hsla(60, 96%, 35%, 1)";
			case "gain":
				return "hsla(120, 96%, 35%, 1)";
			case "default":
			default:
				return "hsla(222, 96%, 55%, 1)";
		}
	}

	getHoverColor(state: string, index: number): string {
		const isHovered = this.hoverStates.get(index);
		if (this.hover && isHovered) {
			switch (state) {
				case "loss":
					return "hsla(347, 76%, 95%, 1)";
				case "alert":
					return "hsla(44, 76%, 95%, 1)";
				case "gain":
					return "hsla(148, 76%, 95%, 1)";
				case "default":
					return "hsla(223, 92%, 95%, 1)";
			}
		} else {
			return "transparent";
		}
	}

	getInfoFontSize(size: number): string {
		switch (size) {
			case 12:
				return "12px";
			case 14:
				return "14px";
			case 18:
				return "18px";
			case 22:
				return "22px";
			case 30:
				return "30px";
			case 40:
				return "40px";
			case 46:
				return "46px";
			default:
				return "12px";
		}
	}

	getIconSize(size: number): string {
		switch (size) {
			case 30:
				return "20px";
			case 40:
				return "20px";
			case 46:
				return "24px";
			default:
				return "12px";
		}
	}
}
