<div class="info-data-container">
	<div
		*ngIf="showIcon == true"
		[ngStyle]="{
			width: getIconSize(this.size),
			'font-size': getIconSize(this.size),
			color: getColor(this.state)
		}"
		class="icon-info-group">
		<i [class]="icon"></i>
	</div>
	<div
		*ngIf="coin == true"
		[ngStyle]="{
			'font-size': getIconSize(this.size),
			color: getColor(this.state)
		}"
		class="coin">
		R$
	</div>
	<div
		[ngStyle]="{
			'font-size': getInfoFontSize(this.size),
			color: getColor(this.state)
		}"
		class="info">
		{{ data }}
	</div>
	<div
		*ngIf="percent == true"
		[ngStyle]="{
			'font-size': getIconSize(this.size),
			color: getColor(this.state)
		}"
		class="percent">
		%
	</div>
</div>
