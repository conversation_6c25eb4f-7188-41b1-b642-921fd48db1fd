<input
	#fileInput
	[accept]="formatos"
	[disabled]="disabled"
	[formControl]="controlFc"
	class="pacto-input-file"
	id="{{ id }}"
	name="{{ id + '-name' }}"
	type="file" />

<label
	(click)="blockIfDisabled($event)"
	[class.disabled]="disabled"
	[ngClass]="determineClass()"
	class="pacto-input-file-label d-flex"
	for="{{ id }}">
	<ng-container *ngIf="!hasFile">
		<div [ngClass]="{ disabled: disabled }" class="file-upload-button">
			<i [ngClass]="['pct', 'upload', uploadIconClass]"></i>
			<div class="texto">{{ text }}</div>
			<div class="descricao mt-3">Adicione ou arraste um arquivo</div>
			<div class="descricao">{{ formatos }}</div>
		</div>
	</ng-container>

	<ng-container *ngIf="hasFile">
		<ng-container *ngIf="(!urlImage || urlImage === '') && previewUrl">
			<div
				(click)="blockIfDisabled($event)"
				*ngIf="hasFile"
				class="preview-container">
				<img
					[ngClass]="{ 'mt-0': isColumn }"
					[src]="previewUrl"
					class="preview-image" />
				<!-- <div class="texto">{{ filename }}</div> -->
			</div>
		</ng-container>

		<ng-container *ngIf="urlImage">
			<div
				(click)="blockIfDisabled($event)"
				*ngIf="hasFile"
				class="preview-container">
				<img
					[ngClass]="{ 'mt-0': isColumn }"
					[src]="urlImage"
					class="preview-image" />
				<!-- <div class="texto">{{ filename }}</div> -->
			</div>
		</ng-container>
		<div
			[ngClass]="{ 'mx-3': !isColumn, 'mt-1': isColumn }"
			class="name-wrapper">
			<button
				(click)="openFileSelector(fileInput)"
				[disabled]="disabled"
				ds3-flat-button>
				Alterar arquivo
			</button>
			<button
				(click)="removeHandler($event)"
				[disabled]="disabled"
				class="mt-1"
				ds3-text-button>
				<i class="pct pct-trash-2 mr-1"></i>
				<span>Remover arquivo</span>
			</button>
		</div>
	</ng-container>
</label>
