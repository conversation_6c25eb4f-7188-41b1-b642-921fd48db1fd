//@ts-ignore
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { withKnobs } from "@storybook/addon-knobs";
import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
import Notes from "./ds3-upload.notes.md";

export default {
	title: "Design System 3 | Navigation/upload",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, BrowserAnimationsModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `		
			<ds3-upload 
				[control]="form.get('arquivo.dados')" 
				[nomeControl]="form.get('arquivo.nome')"
				[urlImage]="form.get('fotoKeyUrlFull')?.value" 				
				[isColumn]="true" 
				[disabled]="false"
				[formatos]="'jpeg, jpg, png'"
			></ds3-upload>		
    `,
});
