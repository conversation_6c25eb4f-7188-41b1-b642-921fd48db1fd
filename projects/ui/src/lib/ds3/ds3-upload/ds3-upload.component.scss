@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/import.scss";

:host {
	display: block;
	width: 100%;
}

label {
	margin-bottom: 0px;
}

input[type="file"].pacto-input-file {
	width: 0.1px;
	height: 0.1px;
	opacity: 0;
	overflow: hidden;
	position: absolute;
	z-index: -1;
	display: block;
}

.pacto-input-file-label {
	border: 1px solid $actionDefaultAble04;
	border-radius: 8px;
	padding: 16px 25%;
	text-align: center;
	transition: background-color 0.3s ease, border-color 0.3s ease;

	&.disabled {
		border-color: $actionDefaultDisabled01;
		cursor: not-allowed;
	}

	.pct {
		font-size: 50px !important;

		&.upload {
			color: $actionDefaultAble04;
		}

		&.file {
			color: $azulimPri;
		}
	}

	.remove-action {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;

		.remove {
			color: $hellboyPri;
			font-size: 18px;
			position: relative;
			padding-left: 5px;
			top: 3px;
		}

		margin-top: 8px;
		margin-left: 4px;
	}

	.name-wrapper {
		display: flex;
		justify-content: center;
		flex-direction: column;

		.texto {
			max-width: 80%;
		}

		.pct {
			font-size: 14px !important;
		}
	}

	.texto {
		display: block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		vertical-align: top;
		padding: 5px;
		@extend .pct-title4;
		word-break: break-all;
		color: $pretoPri;
	}

	.descricao {
		@extend .pct-body2;
		color: $typeDefaultText;
	}

	.preview-image {
		max-width: 88px;
		height: 88px;
		border-radius: 4px;
	}

	.preview-container {
		pointer-events: none;
		cursor: not-allowed !important;
		max-width: 88px;
		max-height: 88px;
	}

	.file-upload-button {
		all: unset;
		cursor: pointer;

		&.disabled {
			cursor: not-allowed;
		}
	}
}
