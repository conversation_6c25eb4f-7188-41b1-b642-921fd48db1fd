**Seletor:** `ds3-upload`

**Utilização:**`<ds3-upload></ds3-upload>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {

  form: FormGroup;
  constructor(
    private fb: FormBuilder,
  ) { }

  ngOnInit() {
    this.form = this.fb.group({     
      arquivo: this.fb.group({
        dados: [null],
        nome: [null]
      }),
      fotoKey: ['null']
    });
  }  
}
```

> meu-exemplo-de-tela.component.html

```html	
		<ds3-upload 
			[control]="form.get('arquivo.dados')" 
			[nomeControl]="form.get('arquivo.nome')"
			[urlImage]="form.get('fotoKey')?.value" 
			class="w-100 d-block" 
			[isColumn]="true" 
			[disabled]="false"
			[formatos]="'jpeg, jpg, png'"
		></ds3-upload>	
```

&nbsp;  

## Props / Inputs


| Property                                    | Values               | Default   		 					| Description                                                                               |
|---                                          |                  --- |               --- 					|                                                                                           |
| @Input() control: FormControl()          	  | 				 --- |		         --- 					| Utilizado para capturar o valor do campo.                                 				|
| @Input() nomeControl: FormControl()         | 				 --- |               --- 					| Utilizado para capturar o valor do campo.                				  					|
| @Input() urlImage							  | 				 --- |               --- 					| URL da imagem salva no servidor, utilizada para exibir a imagem no upload.             	|
| @Input() uploadIconClass: string            | pct-save ...pct-plus | pct-upload-cloud  					| Classe do ícone de upload exibido no componente.                    				  		|
| @Input() text: string                       | texto livre          | Upload de arquivo 					| Texto exibido no componente de upload.                                               		|
| @Input() isColumn: boolean                  | true / false         | true              					| Define se o layout do componente será na vertical (coluna) ou horizontal (linha).         |
| @Input() disable: boolean                   | true / false         | false              					| Define se o componente está desabilitado para edição..															|
| @Input() formatos: string                   | jpeg, jpg, png       | jpeg, jpg, png, pdf, txt, doc e docx | Define os formatos de arquivo aceitos pelo componente.															 
