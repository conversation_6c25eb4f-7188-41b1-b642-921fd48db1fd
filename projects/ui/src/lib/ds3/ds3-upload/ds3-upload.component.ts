import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	HostListener,
	Input,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { Observable } from "rxjs";

@Component({
	selector: "ds3-upload",
	templateUrl: "./ds3-upload.component.html",
	styleUrls: ["./ds3-upload.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3UploadComponent {
	@ViewChild("fileInput", { static: true }) fileInput;
	@Input() control: FormControl;
	@Input() nomeControl: FormControl;
	@Input() formatos = "jpeg, jpg, png, pdf, txt, doc e docx";
	@Input() formatosValidos = new RegExp(
		"(jpeg|jpg|png|pdf|txt|doc|docx)$",
		"i"
	);
	@Input() id: string;
	@Input() urlImage: string;
	@Input() isColumn: boolean = true;
	@Input() uploadIconClass: string = "pct-upload-cloud";
	@Input() text: string = "Upload de arquivo";
	@Input() disabled: boolean = false;

	@Output() removed = new EventEmitter<void>();

	controlFc: FormControl = new FormControl();
	filename: string;

	previewUrl: string | ArrayBuffer;

	constructor(
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.setId();
		if (typeof this.formatosValidos === "string") {
			this.formatosValidos = new RegExp(this.formatosValidos, "i");
		}
		this.controlFc.valueChanges.subscribe(() => {
			if (this.files && this.files.length) {
				if (this.verificarExtensaoUpload(this.files.item(0).name)) {
					this.filename = this.files.item(0).name;
					this.nomeControl.setValue(this.files.item(0).name);
					this.getFileAsText(this.files[0]).subscribe((value) => {
						this.control.setValue(value);
						this.previewFile();
						this.urlImage = null;
					});
				}
			}
		});
		this.cd.detectChanges();
	}

	openFileSelector(fileInput: HTMLInputElement): void {
		if (!this.disabled) {
			fileInput.click();
		}
	}

	verificarExtensaoUpload(dto) {
		const formatoArquivo = dto.split(".");
		if (formatoArquivo[formatoArquivo.length - 1].match(this.formatosValidos)) {
			return true;
		} else {
			this.snotifyService.warning(
				"Arquivo inválido - Verifique a lista de formatos permitidos e tente novamente"
			);
			this.filename = null;
			this.controlFc.reset();
			this.control.reset();
			this.nomeControl.reset();
			return false;
		}
	}

	private setId() {
		if (!this.id) {
			const rdm = Math.trunc(Math.random() * 1000);
			this.id = `pacto-file-input-${rdm}`;
		}
	}

	@HostListener("dragover", ["$event"]) onDragOver(evt) {
		evt.preventDefault();
		evt.stopPropagation();
	}

	@HostListener("dragleave", ["$event"])
	public onDragLeave(evt) {
		evt.preventDefault();
		evt.stopPropagation();
	}

	@HostListener("drop", ["$event"])
	public ondrop(evt) {
		evt.preventDefault();
		evt.stopPropagation();
		const files = evt.dataTransfer.files;
		if (files && files.length) {
			this.filename = files.item(0).name;
			this.nomeControl.setValue(files.item(0).name);
			this.getFileAsText(files.item(0)).subscribe((text) => {
				this.control.setValue(text);
				this.previewFile();
			});
		}
	}

	get hasFile() {
		return (this.control && this.control.value) || this.urlImage;
	}

	get files(): FileList {
		return this.fileInput.nativeElement.files;
	}

	private getFileAsText(file): Observable<string> {
		const reader = new FileReader();
		return new Observable((observer) => {
			reader.readAsDataURL(file);
			reader.onload = (e: any) => {
				observer.next(e.target.result);
				observer.complete();
				this.cd.detectChanges();
			};
		});
	}

	removeHandler($event: MouseEvent) {
		$event.preventDefault();
		$event.stopPropagation();
		this.filename = null;
		this.urlImage = null;
		this.controlFc.reset();
		this.control.reset();
		this.nomeControl.reset();
		this.removed.emit();
	}

	blockIfDisabled(event: Event): void {
		if (this.disabled || this.hasFile) {
			event.preventDefault();
			event.stopPropagation(); // Impede a propagação do evento de clique
		}
	}

	reset() {
		this.filename = null;
		this.urlImage = null;
		this.controlFc.reset();
		this.control.reset();
		this.nomeControl.reset();
		this.cd.detectChanges();
	}

	previewFile() {
		const file = this.files.item(0);
		if (file) {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => {
				const extension = this.getExtension(file.name);
				if (this.isValidExtension(extension)) {
					this.previewUrl = reader.result as string;
				} else {
					this.previewUrl = "pacto-ui/images/pct-file-text.png";
				}
				this.cd.detectChanges();
			};
		}
	}

	getExtension(filename: string): string {
		return filename.split(".").pop().toLowerCase();
	}

	isValidExtension(extension: string): boolean {
		const validExtensions = ["jpeg", "jpg", "png"];
		return validExtensions.includes(extension);
	}

	determineClass(): string {
		let classes = "";

		if (this.disabled) {
			classes += "disabled ";
		}

		if (!this.isColumn && this.hasFile) {
			classes += "flex-row justify-content-center";
		} else {
			classes += "flex-column align-items-center";
		}

		return classes;
	}
}
