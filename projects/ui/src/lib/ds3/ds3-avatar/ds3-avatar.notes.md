**Seletor:** `ds3-avatar`

**Utilização:**`<ds3-avatar></ds3-avatar>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component, OnInit } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    

}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-avatar [src]="'https://i.ibb.co/r3vpkHQ/State-Filled-Size-144px.png'" size="64"></ds3-avatar>
```
&nbsp;  

## Props / Inputs

| Propiedade                               | Valores             | Valor <PERSON>ult        | Descrição          | 
|---                                       |                  ---|                   ---|                 ---|
| @Input() src: string                |  String      |Vazio                 | Define a imagem do avatar, caso não tiver imagem, aparecerá uma uma imagem padrão cinza|
| @Input() size: number                |  Number             |Number                 | Somente valores múltiplos de 8, 16 e 24 |

## More info

Caso o valor de size não for múltimplo de 6,8 ou 24, aparecerá mensagem de erro na tela.

&nbsp;  
