import { text, withKnobs } from "@storybook/addon-knobs";
import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
// @ts-ignore
import Notes from "./ds3-avatar.notes.md";

const description = () => Notes;

export default {
	title: "Design System 3 | Styles/Avatar",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		docs: {
			extractComponentDescription: description,
		},
	},
};

export const interactive = () => ({
	template: `	
    <div style='padding:15px;'>
    <div><ds3-avatar [src]="" size={{sizeAvatar1}}>
	</ds3-avatar></div>
	
    <div><ds3-avatar [src]="srcAvatar1" size={{sizeAvatar1}}>
	</ds3-avatar></div>
    </div>
    `,

	props: {
		srcAvatar1: text(
			"Imagem do Avatar 1",
			"https://i.ibb.co/r3vpkHQ/State-Filled-Size-144px.png"
		),
		sizeAvatar1: text("Tamanho do avatar 1:", "64"),
		srcAvatarSemImagem: text("Imagem do Avatar Sem foto", ""),
	},
});

// 			<ds3-chips-list>
// 				<ng-container *ngFor="let chip of chips">
// 					<ds3-chips
// 						[isRemovable]="isRemovable"
// 						(removed)="deleteChip(chip)"
// 					>{{chip.name}}</ds3-chips>
// 				</ng-container>
// 			</ds3-chips-list>
// 		</div>
//     `,
// 	props: {
// 		isRemovable: boolean('Is removable', true),
// 		chips: CHIPS_LIST,
// 		deleteChip: (chip) => {
// 			const index = CHIPS_LIST.indexOf(chip);

// 			if (index >= 0) {
// 				CHIPS_LIST.splice(index, 1);
// 			}
// 		}
// 	},
// });

// export const defaultChips = () => ({
// 	template: `
// 		<div style="padding: 16px;">
// 			<ds3-chips-list>
// 				<ng-container *ngFor="let chip of chips">
// 					<ds3-chips>{{chip.name}}</ds3-chips>
// 				</ng-container>
// 			</ds3-chips-list>
// 		</div>
//     `,
// 	props: {
// 		chips: CHIPS_LIST,
// 	}
// });

// export const disabled = () => ({
// 	template: `
// 		<div style="padding: 16px;">
// 			<ds3-chips-list>
// 				<ng-container *ngFor="let chip of chips">
// 					<ds3-chips
// 						[isDisabled]="isDisabled"
// 					>{{chip.name}}</ds3-chips>
// 				</ng-container>
// 			</ds3-chips-list>
// 		</div>
//     `,
// 	props: {
// 		chips: CHIPS_LIST,
// 		isDisabled: boolean('Is disabled', true)
// 	}
// });
