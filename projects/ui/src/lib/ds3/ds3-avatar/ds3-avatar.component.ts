import { Component, Input, OnInit } from "@angular/core";

@Component({
	selector: "ds3-avatar",
	templateUrl: "./ds3-avatar.component.html",
	styleUrls: ["./ds3-avatar.component.scss"],
})
export class Ds3AvatarComponent implements OnInit {
	@Input()
	src?: string = "";

	@Input()
	size: Number;

	@Input()
	srcError = "pacto-ui/images/user-image-default.svg";

	constructor() {}

	ngOnInit() {}

	trateCompanyLogoError(event: ErrorEvent) {
		if (event.type === "error") {
			const target: HTMLImageElement = event.target as HTMLImageElement;
			target.src = this.srcError;
		}
	}
}
