import {
	Component,
	Input,
	OnInit,
	Output,
	EventEmitter,
	Directive,
} from "@angular/core";

@Component({
	selector: "ds3-accordion",
	templateUrl: "./ds3-accordion.component.html",
	styleUrls: ["./ds3-accordion.component.scss"],
})
export class Ds3AccordionComponent implements OnInit {
	@Input() title: string;
	@Input() description: string;
	@Input() isDisabled: boolean;
	@Input() isExpanded: boolean;
	@Output() expansionChanged = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	toggleAccordionExpansion() {
		if (this.isDisabled) {
			return;
		}
		this.isExpanded = !this.isExpanded;
	}

	emitExpansionChangeEvent() {
		this.toggleAccordionExpansion();
		this.expansionChanged.emit(this.isExpanded);
	}
}
