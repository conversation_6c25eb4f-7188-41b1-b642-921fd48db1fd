@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts";
@import "projects/ui/assets/ds3/animations";

.ds3-accordion {
	border-radius: 8px;
	background-color: $plane03;
	margin-bottom: 8px;
}

.ds3-accordion-head {
	display: grid;
	gap: 8px;
	align-items: center;
	grid-template-columns: 1fr 32px;
	padding: 8px 16px;
	border-radius: 8px;
	background-color: $plane03;
	cursor: pointer;

	&:hover {
		background-color: darken($plane03, 2%);
	}
}

.ds3-accordion-title {
	margin-bottom: 0;
}

.ds3-accordion-description {
	margin: 16px 0 12px;
	color: $supportGray04;
}

.ds3-accordion-disabled {
	.ds3-accordion-head {
		cursor: not-allowed;
		opacity: 0.75;
	}
}

.ds3-accordion-body {
	padding: 16px;
	padding-top: 0;

	ds3-diviser {
		margin-bottom: 16px;
	}
}
