import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3AccordionComponent } from "./ds3-accordion.component";
import {
	Ds3AccordionDescriptionDirective,
	Ds3AccordionTitleDirective,
} from "./ds3-accordion-head.component";
import { UiModule } from "../../ui.module";
import { Ds3DiviserModule } from "../ds3-diviser/ds3-diviser.module";
import { Ds3ButtonModule } from "../ds3-button/ds3-button.module";

@NgModule({
	declarations: [
		Ds3AccordionComponent,
		Ds3AccordionTitleDirective,
		Ds3AccordionDescriptionDirective,
	],
	imports: [CommonModule, Ds3DiviserModule, Ds3ButtonModule],
	exports: [
		Ds3AccordionComponent,
		Ds3AccordionTitleDirective,
		Ds3AccordionDescriptionDirective,
	],
})
export class Ds3AccordionModule {}
