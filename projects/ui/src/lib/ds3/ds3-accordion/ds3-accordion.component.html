<div [class.ds3-accordion-disabled]="isDisabled" class="ds3-accordion">
	<div
		(click)="emitExpansionChangeEvent()"
		class="ds3-accordion-head pct-transition">
		<div>
			<h1 class="ds3-accordion-title pct-title4">{{ title }}</h1>
			<h2 *ngIf="description" class="ds3-accordion-description pct-overline1">
				{{ description }}
			</h2>
		</div>

		<button color="primary" ds3-icon-button>
			<i *ngIf="!isExpanded" class="pct pct-chevron-down"></i>
			<i *ngIf="isExpanded" class="pct pct-chevron-up"></i>
		</button>
	</div>
	<ng-container *ngIf="isExpanded">
		<div class="ds3-accordion-body">
			<ds3-diviser></ds3-diviser>
			<ng-content></ng-content>
		</div>
	</ng-container>
</div>
