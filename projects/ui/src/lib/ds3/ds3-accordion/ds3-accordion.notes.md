**Seletor:** `ds3-accordion`

**Utilização:**`<ds3-accordion></ds3-accordion>`

### Exemplo:

> meu-exemplo-de-tela.component.html

```html
<ds3-accordion
    title="Title of disabled accordion"
    description="Description of disabled accordion"
>
    This is the content of the accordion
</ds3-accordion>
```
&nbsp;  

## Props / Inputs

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Input() isDisabled: boolean             |                     |false                 |
| @Input() isExpanded: boolean             |                     |false                 |
| @Input() title: string                   |                     |''                    |
| @Input() description: string             |                     |''                    |

## Outputs / Events

| Property                                  | Values              | Default              |
|---                                        |                  ---|                   ---|
| @Output() expansionChanged: EventEmitter  |                     |-                     |

## More info

No more info.
