// @ts-ignore
import Notes from "./ds3-accordion.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";

const description = () => Notes;

export default {
	title: "Design System 3 | Navigation/Accordion",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-accordion
				[title]="title"
				[description]="description"
				[isDisabled]="isDisabled"
				[isExpanded]="isExpanded"
			>
				{{content}}
			</ds3-accordion>
		</div>
    `,
	props: {
		isDisabled: boolean("Is disabled", false),
		isExpanded: boolean("Is expanded", false),
		title: text("Title", "Title of the accordion"),
		description: text(
			"Description",
			"This is the description of the accordion"
		),
		content: text("Content", "Here comes the content of the accordion"),
	},
});

export const disabled = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-accordion
				title="This accordion is disabled"
				description="On disabled accordion you can't open or close it"
				[isDisabled]="isDisabled"
			>
				This is the accordion's content
			</ds3-accordion>
		</div>
    `,
	props: {
		isDisabled: boolean("Is disabled", true),
	},
});

export const noDescription = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-accordion
				title="This accordion has no description"
				[description]="description"
			>
				This is the accordion's content
			</ds3-accordion>
		</div>
    `,
	props: {
		description: text("Description", ""),
	},
});

export const expanded = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-accordion
				title="This accordion is presented expanded"
				[isExpanded]="isExpanded"
			>
				This is the accordion's content, click on the head to close it.
			</ds3-accordion>
		</div>
    `,
	props: {
		isExpanded: boolean("Is expanded", true),
	},
});

export const list = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-accordion
				title="This the first accordion"
				description="Description of first accordion"
			>
				This is the accordion's content
			</ds3-accordion>
			<ds3-accordion
				title="The second one has a different title"	
			>
				This is the accordion's content
			</ds3-accordion>
			<ds3-accordion
				title="The third accordion"
				description="Description of third accordion"
			>
				This is the accordion's content
			</ds3-accordion>
			<ds3-accordion
				[isDisabled]="true"
				title="Sorry, this accordion is disabled"
				description="Description of disabled accordion"
			>
				This is the accordion's content
			</ds3-accordion>
		</div>
    `,
});
