import { ElementRef } from "@angular/core";

export const getCssVariable = (variable: string) => {
	return getComputedStyle(document.documentElement).getPropertyValue(variable);
};

export const hslToHex = (h, s, l) => {
	l /= 100;
	const a = (s * Math.min(l, 1 - l)) / 100;
	const f = (n) => {
		const k = (n + h / 30) % 12;
		const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
		return Math.round(255 * color)
			.toString(16)
			.padStart(2, "0"); // convert to Hex and prefix "0" if needed
	};
	return `#${f(0)}${f(8)}${f(4)}`;
};

export const getCssVariableAndConvertToHex = (variable: string) => {
	const hsla = getComputedStyle(document.documentElement).getPropertyValue(
		variable
	);
	if (!hsla) {
		return "#fff";
	}

	const hslaArray = hsla.match(/\d+/g).map(Number);
	const hex = hslToHex(hslaArray[0], hslaArray[1], hslaArray[2]);
	return hex;
};

export function hasSomeHostAttributes(
	element: HTMLElement,
	...attributes: string[]
) {
	return attributes.some((attribute) => element.hasAttribute(attribute));
}

export function hasEveryHostAttributes(
	element: HTMLElement,
	...attributes: string[]
) {
	return attributes.every((attribute) => element.hasAttribute(attribute));
}

export function getHostElement(elementRef: ElementRef): HTMLElement {
	return elementRef.nativeElement as HTMLElement;
}
