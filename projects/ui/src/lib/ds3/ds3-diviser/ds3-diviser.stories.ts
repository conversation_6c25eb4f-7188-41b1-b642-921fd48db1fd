// @ts-ignore
import Notes from "./ds3-diviser.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, select, text, withKnobs } from "@storybook/addon-knobs";
import { borderColors, iconsName } from "../stories-utils";
import { Ds3DiviserComponent } from "./ds3-diviser.component";
import { Ds3DiviserModule } from "./ds3-diviser.module";

export default {
	title: "Design System 3 | Styles/Diviser",
	decorators: [
		moduleMetadata({
			imports: [Ds3DiviserModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const diviser = () => ({
	template: `
        <div style="margin-top: 2rem; margin-left: 2rem;">
            <ds3-diviser
               [vertical]="vertical"
               [centered]="centered"
               [colorClass]="colorClass"  
               [style.height]="vertical ? diviserHeight : 0"          
               [style.width]="!vertical ? diviserWidth : 0"          
            ></ds3-diviser>
        </div>
    `,
	props: {
		vertical: boolean("Vertical", false),
		centered: boolean("Centered", false),
		colorClass: select("colorClass", borderColors, borderColors[77]),
		diviserHeight: text("height", "100px"),
		diviserWidth: text("width", "100px"),
	},
});
