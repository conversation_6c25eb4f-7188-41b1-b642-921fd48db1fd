# diviser

**Table of content:**

-   [Selectors](#selectors)
-   [Inputs](#inputs)
-   [Outputs](#outputs)
-   [Example](#example)

# selectors

```html
<ds3-diviser></ds3-diviser>
```

# inputs

| inputName  | descricao                                 | possiveis valores           | valor inicial           |
| ---------- | ----------------------------------------- | --------------------------- | ----------------------- |
| vertical   | define se divisor vai ser vertical ou não | boolean                     | false                   |
| centered   | se deve se centralizar no espaço entorno  | boolean                     | false                   |
| colorClass | classe que define cor da borda            | string das classes de borda | 'border-support-gray03' |

# outputs

nenhum

# example

diviser horizontal padrão

```html
<ds3-diviser></ds3-diviser>
```

diviser vertical padrão

```html
<ds3-diviser [vertical]="true"></ds3-diviser>
```

diviser horizontal com borda customizada e centralizado

```html
<ds3-diviser [centered]="true" colorClass="border-support-pink05"></ds3-diviser>
```
