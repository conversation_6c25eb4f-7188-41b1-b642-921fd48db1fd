import {
	Component,
	ElementRef,
	Input,
	OnChanges,
	OnInit,
	Renderer2,
	SimpleChange,
	SimpleChanges,
} from "@angular/core";

@Component({
	selector: "ds3-diviser",
	templateUrl: "./ds3-diviser.component.html",
	styleUrls: ["./ds3-diviser.component.scss"],
})
export class Ds3DiviserComponent implements OnInit, OnChanges {
	@Input() vertical: boolean = false;
	@Input() centered: boolean = false;
	@Input() colorClass: string = "border-support-gray03";

	private verticalClass: string = "ds3-diviser-vertical";
	private centeredClass: string = "ds3-diviser-centered";
	private defaultColorClass: string = "border-support-gray03";

	constructor(private renderer: Renderer2, private el: ElementRef) {}

	ngOnInit() {
		this.addColorClass();
		this.addVerticalClass();
		this.addCenteredClass();
	}

	ngOnChanges(changes: SimpleChanges) {
		const vertical: SimpleChange = changes.vertical;
		if (vertical && !vertical.firstChange) {
			this.addVerticalClass();
		}

		const centered: SimpleChange = changes.centered;
		if (centered && !centered.firstChange) {
			this.addCenteredClass();
		}

		const colorClass: SimpleChange = changes.colorClass;
		if (colorClass && !colorClass.firstChange) {
			this.addColorClass(colorClass.previousValue);
		}
	}

	private addVerticalClass() {
		if (this.el.nativeElement.classList) {
			if (
				this.vertical &&
				!this.el.nativeElement.classList.contains(this.verticalClass)
			) {
				this.renderer.addClass(this.el.nativeElement, this.verticalClass);
			} else if (this.el.nativeElement.classList.contains(this.verticalClass)) {
				this.renderer.removeClass(this.el.nativeElement, this.verticalClass);
			}
		} else {
			this.renderer.addClass(this.el.nativeElement, this.verticalClass);
		}
	}

	private addCenteredClass() {
		if (this.el.nativeElement.classList) {
			if (
				this.centered &&
				!this.el.nativeElement.classList.contains(this.centeredClass)
			) {
				this.renderer.addClass(this.el.nativeElement, this.centeredClass);
			} else if (this.el.nativeElement.classList.contains(this.centeredClass)) {
				this.renderer.removeClass(this.el.nativeElement, this.centeredClass);
			}
		} else {
			this.renderer.addClass(this.el.nativeElement, this.centeredClass);
		}
	}

	private addColorClass(previousColor?: string) {
		if (previousColor) {
			this.renderer.removeClass(this.el.nativeElement, previousColor);
		}
		if (!this.colorClass || this.colorClass.length === 0) {
			this.colorClass = this.defaultColorClass;
		} else {
			this.renderer.removeClass(this.el.nativeElement, this.defaultColorClass);
		}
		if (!this.el.nativeElement.classList.contains(this.colorClass)) {
			this.renderer.addClass(this.el.nativeElement, this.colorClass);
		} else if (this.el.nativeElement.classList.contains(this.colorClass)) {
			this.renderer.removeClass(this.el.nativeElement, this.colorClass);
		}
	}
}
