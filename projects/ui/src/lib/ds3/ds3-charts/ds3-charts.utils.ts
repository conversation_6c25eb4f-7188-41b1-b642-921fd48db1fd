import { getCssVariable } from "../ds3.utils";

export const getColorsBasedOnColorOrder = (color, colorOrder) => {
	let colors;
	switch (color) {
		case "primary":
			colors = colorOrder.map((color) => {
				return getCssVariable("--color-support-" + color + "-4");
			});
			break;
		case "secondary":
			colors = colorOrder.map((color) => {
				return getCssVariable("--color-support-" + color + "-6");
			});
			break;
		case "tertiary":
			colors = colorOrder.map((color) => {
				return getCssVariable("--color-support-" + color + "-2");
			});
			break;
	}
	return colors;
};

export const setIdIfNotProvided = (id: string) => {
	if (id) {
		return id;
	}

	const randomId = Math.trunc(Math.random() * 10000);
	const chartId = `ds3-chart-${randomId}`;
	return chartId;
};

export const buildChart = (chartInstance, id, options) => {
	if (chartInstance) {
		chartInstance.destroy();
	}

	console.log(chartInstance, id, options);

	chartInstance = new ApexCharts(document.querySelector(`#${id}`), options);
	chartInstance.render();
};

export const getPieLabel = (showAverage, pieLabel) => {
	if (pieLabel) {
		return pieLabel;
	}
	if (showAverage) {
		return "Média";
	}
	return "Total";
};

export const colorOptions = {
	Primary: "primary",
	Secondary: "secondary",
	Tertiary: "tertiary",
};
