**Seletor:** `ds3-select`

**Utilização:**`<ds3-select></ds3-select>`

`<ds3-select>` é um FormControl para selecionar um valor de uma lista de opções, similar ao componente `<select>` nativo.

Este componente foi projetado para funcionar dentro de um elemento `<ds3-form-field>`. Portanto deve ser usada a diretiva `<ds3-select ds3Input>` ao referenciar o componente.

Para adicionar opções, o `<ds3-select>` recebe um input `[options]=""`, que deve ser um array de objetos com o formato `[{name: '', label: ''}]`.

O componente pode receber dados de formularios Template Driven e Reactive Forms.

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  select = new FormControl()
  options = [
    {value: 'value_1', name: 'This is the Value 1'},
    {value: 'value_2', name: 'Value 2'}
  ]
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-form-field>
  <ds3-field-label>Select</ds3-field-label>
  <ds3-select-multi
    [formControl]="select"
    [options]="options"
    ds3Input>
  </ds3-select-multi>
  <ds3-helper-message>
    This is the helper
  </ds3-helper-message>
</ds3-form-field>

```
&nbsp;  

## Props / Inputs

| Property                                      | Values              | Default              |
|---                                            |                  ---|                   ---|
| @Input() placeholder: string                  |                     |'Selecione um item'   |
| @Input() options: Array<Ds3SelectOptionModel> |                     |[]                    |
| @Input() disabled: boolean                    |                     |false                 |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  

# Multi Select

**Seletor:** `ds3-select-multi`

**Utilização:**`<ds3-select-multi></ds3-select-multi>`

`<ds3-select-multi>` é um FormControl para selecionar múltiplos valores de uma lista de opções, similar ao componente `<select multiple>` nativo.

Este componente foi projetado para funcionar dentro de um elemento `<ds3-form-field>`. Portanto deve ser usada a diretiva `<ds3-select-multi ds3Input>` ao referenciar o componente.

Para adicionar opções, o `<ds3-select-multi>` recebe um input `[options]=""`, que deve ser um array de objetos com o formato `[{name: '', label: ''}]`.

O componente pode receber dados de formularios Template Driven e Reactive Forms.

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  select = new FormControl()
  options = [
    {value: 'value_1', name: 'This is the Value 1'},
    {value: 'value_2', name: 'Value 2'}
  ]
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-form-field>
  <ds3-field-label>Select</ds3-field-label>
  <ds3-select-multi
    [formControl]="select"
    [options]="options"
    ds3Input>
  </ds3-select-multi>
  <ds3-helper-message>
    This is the helper
  </ds3-helper-message>
</ds3-form-field>

```
&nbsp;  

## Props / Inputs

| Property                                      | Values              | Default              |
|---                                            |                  ---|                   ---|
| @Input() placeholder: string                  |                     |'Selecione um item'   |
| @Input() options: Array<Ds3SelectOptionModel> |                     |[]                    |
| @Input() disabled: boolean                    |                     |false                 |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|

## More info

No more info.
