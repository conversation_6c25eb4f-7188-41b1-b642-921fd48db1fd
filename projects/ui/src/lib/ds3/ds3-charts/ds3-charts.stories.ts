// @ts-ignore
import Notes from "./ds3-charts.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, text, withKnobs } from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../ds3.module";

export default {
	title: "Design System 3 | Data/Charts",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};
