import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Ds3ChartLineComponent } from "./components/ds3-chart-line/ds3-chart-line.component";
import { Ds3ChartMixedComponent } from "./components/ds3-chart-mixed/ds3-chart-mixed.component";
import { Ds3ChartColumnComponent } from "./components/ds3-chart-column/ds3-chart-column.component";
import { Ds3ChartColumnStackedComponent } from "./components/ds3-chart-column-stacked/ds3-chart-column-stacked.component";
import { Ds3ChartColumnDistributedComponent } from "./components/ds3-chart-column-distributed/ds3-chart-column-distributed.component";
import { Ds3ChartBarComponent } from "./components/ds3-chart-bar/ds3-chart-bar.component";
import { Ds3ChartDonutComponent } from "./components/ds3-chart-donut/ds3-chart-donut.component";
import { Ds3ChartPieComponent } from "./components/ds3-chart-pie/ds3-chart-pie.component";
import { Ds3ChartMiddleComponent } from "./components/ds3-chart-middle/ds3-chart-middle.component";
import { Ds3ChartHeatmapComponent } from "./components/ds3-chart-heatmap/ds3-chart-heatmap.component";
import { Ds3ChartFunnelComponent } from "./components/ds3-chart-funnel/ds3-chart-funnel.component";
import { Ds3ChartFunnelColumnComponent } from "./components/ds3-chart-funnel/ds3-chart-funnel-column/ds3-chart-funnel-column.component";
import { Ds3ChartsSharedDirective } from "./directives/ds3-charts-shared.directive";
import { Ds3InfoModule } from "../ds3-info/ds3-info.module";
import { Ds3ChartLineGraficComponent } from "./components/ds3-chart-line-grafic/ds3-chart-line-grafic.component";

@NgModule({
	declarations: [
		Ds3ChartLineComponent,
		Ds3ChartMixedComponent,
		Ds3ChartColumnComponent,
		Ds3ChartColumnStackedComponent,
		Ds3ChartColumnDistributedComponent,
		Ds3ChartBarComponent,
		Ds3ChartDonutComponent,
		Ds3ChartPieComponent,
		Ds3ChartMiddleComponent,
		Ds3ChartFunnelComponent,
		Ds3ChartHeatmapComponent,
		Ds3ChartsSharedDirective,
		Ds3ChartFunnelColumnComponent,
		Ds3ChartLineGraficComponent,
	],
	imports: [CommonModule, FormsModule, Ds3InfoModule, ReactiveFormsModule],
	exports: [
		Ds3ChartLineComponent,
		Ds3ChartMixedComponent,
		Ds3ChartColumnComponent,
		Ds3ChartColumnStackedComponent,
		Ds3ChartColumnDistributedComponent,
		Ds3ChartBarComponent,
		Ds3ChartDonutComponent,
		Ds3ChartPieComponent,
		Ds3ChartMiddleComponent,
		Ds3ChartFunnelComponent,
		Ds3ChartHeatmapComponent,
		Ds3ChartFunnelColumnComponent,
		Ds3ChartLineGraficComponent,
	],
})
export class Ds3ChartsModule {}
