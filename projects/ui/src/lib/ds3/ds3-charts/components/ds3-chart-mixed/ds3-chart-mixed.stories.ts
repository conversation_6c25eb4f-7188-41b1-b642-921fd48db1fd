// @ts-ignore
import Notes from "./ds3-chart-mixed.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Mixed",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-mixed
				[series]="chartOptions.series"
				[labels]="chartOptions.labels"
				[yaxis]="chartOptions.yaxis"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-mixed>
		</div>
    `,
	props: {
		chartOptions: {
			labels: [
				"01 Jan",
				"02 Jan",
				"03 Jan",
				"04 Jan",
				"05 Jan",
				"06 Jan",
				"07 Jan",
			],
			series: [
				{
					name: "Website Blog",
					type: "column",
					data: [440, 505, 414, 671, 227, 413, 201],
				},
				{
					name: "Social Media",
					type: "line",
					data: [23, 42, 35, 27, 43, 22, 17],
				},
			],
			yaxis: [
				{
					title: {
						text: "Website Blog",
					},
				},
				{
					opposite: true,
					title: {
						text: "Social Media",
					},
				},
			],
		},
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color order: ", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
	},
});
