import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ApexOptions } from "apexcharts";
import { getColorsBasedOnColorOrder } from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-mixed",
	templateUrl: "./ds3-chart-mixed.component.html",
	styleUrls: ["./ds3-chart-mixed.component.scss"],
})
export class Ds3ChartMixedComponent implements OnInit, OnChanges, OnDestroy {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() labels: string[];
	@Input() height: number = 350;
	@Input() isStacked: boolean = false;
	@Input() disableAnimations: boolean = false;
	@Input() tooltip: ApexTooltip = {
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];
	@Input()
	dataLabels: ApexDataLabels = {
		enabled: false,
		enabledOnSeries: [1],
	};

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `ds3-chart-${rdn}`;
		}
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private getOptions() {
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			yaxis: this.yaxis,
			labels: this.labels,
			tooltip: this.tooltip,
			chart: {
				width: "100%",
				redrawOnWindowResize: true,
				redrawOnParentResize: true,
				stacked: this.isStacked,
				type: "line",
				height: this.height,
				zoom: {
					enabled: false,
				},
				toolbar: {
					show: false,
				},
				animations: {
					enabled: !this.disableAnimations,
				},
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
			stroke: {
				width: [0, 3],
			},
			dataLabels: this.dataLabels,
			markers: {
				strokeWidth: 0,
				size: 4,
			},
			plotOptions: {
				bar: {
					dataLabels: {
						position: "center",
					},
				},
			},
		} as ApexOptions;
		return options;
	}
}
