**Seletor:** `ds3-chart-mixed`

**Utilização:**`<ds3-chart-mixed></ds3-chart-mixed>`

`<ds3-chart-mixed>` é um componente criado a partir do ApexCharts para exibir dados através de um gráfico misto, com linhas e colunas.

O gráfico misto é um gráfico de linhas e colunas, portanto para exibir dados deve receber dois inputs. Series e xaxis.

``` series ```

Recebe um array de objetos com o nome, tipo e valores do gráfico.

``` labels ```

Recebe um array de strings com os nomes do eixo x.

Para mais informações, acesse a documentação do apexcharts.

[Apexcharts Docs](https://apexcharts.com/docs/installation/)

[Apexcharts Mixed Chart Demo](https://apexcharts.com/javascript-chart-demos/mixed-charts/line-column/)

### Cores:

Existem 3 temas de cores possíveis para ser usado nos gráficos. *primary*, *secondary* e *tertiary*.

Os temas representam as cores de Support 4, 6 e 2 respectivamente.

Existem 8 cores pré-selecionadas nos gráficos. Não é possível mudar as cores de exibição do gráfico, mas é possível alterar a ordem delas através do input ``` colorOrder ```

O input ``` colorOrder ``` recebe um array de strings com as cores pré-selecionadas para o gráfico. A ordem de exibição das cores segue a ordem definida nesse array. 

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  chartOptions = {
    labels: [
      "01 Jan",
      "02 Jan",
      "03 Jan",
      "04 Jan",
      "05 Jan",
      "06 Jan",
      "07 Jan",
    ],
    series: [
      {
        name: "Website Blog",
        type: "column",
        data: [440, 505, 414, 671, 227, 413, 201]
      },
      {
        name: "Social Media",
        type: "line",
        data: [23, 42, 35, 27, 43, 22, 17]
      }
    ],
  }
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chart-mixed
  [series]="chartOptions.series"
  [xaxis]="chartOptions.xaxis"
>
</ds3-chart-mixed>
```
&nbsp;  

## Props / Inputs

| Property                                              | Values              | Default                      |
|---                                                    |                  ---|                           ---|
| @Input() id: string                                   |                     |Valor gerado aleatoriamente   |
| @Input() series: ApexAxisChartSeries                  |                     |undefined                     |
| @Input() xaxis: ApexXAxis                             |                     |undefined                     |
| @Input() yaxis: ApexYAxis                             |                     |undefined                     |
| @Input() tooltip: ApexTooltip                         |                     |undefined                     |
| @Input() height: number                               |                     |350                           |
| @Input() color: ['primary', 'secondary', 'tertiary']  |                     |'primary'                     |
| @Input() colorOrder: ['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|     |['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  