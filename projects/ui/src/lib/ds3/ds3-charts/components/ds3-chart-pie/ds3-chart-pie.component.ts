import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-pie",
	templateUrl: "./ds3-chart-pie.component.html",
	styleUrls: ["./ds3-chart-pie.component.scss"],
})
export class Ds3ChartPieComponent implements OnInit, OnChanges, OnDestroy {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() labels;
	@Input() series: ApexAxisChartSeries;
	@Input() height: number = 240;
	@Input() disableAnimations: boolean = false;
	@Input() tooltip: ApexTooltip = {
		fillSeriesColor: false,
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			labels: this.labels,
			tooltip: this.tooltip,
			chart: {
				type: "pie",
				height: this.height,
				toolbar: {
					show: false,
				},
				animations: {
					enabled: !this.disableAnimations,
				},
			},
			plotOptions: {
				pie: {
					dataLabels: {
						offset: -10,
					},
				},
			},
			dataLabels: {
				dropShadow: {
					enabled: false,
				},
			},
			stroke: {
				show: false,
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
		};
		return options;
	}
}
