// @ts-ignore
import Notes from "./ds3-chart-pie.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Pie",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-pie
				[series]="chartOptions.series"
				[labels]="chartOptions.labels"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-pie>
		</div>
    `,
	props: {
		chartOptions: {
			series: [44, 55, 13, 43, 22],
			labels: ["Team A", "Team B", "Team C", "Team D", "Team E"],
		},
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
	},
});
