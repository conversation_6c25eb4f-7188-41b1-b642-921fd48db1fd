import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3ChartColumnStackedComponent } from "./ds3-chart-column-stacked.component";

describe("Ds3ChartColumnStackedComponent", () => {
	let component: Ds3ChartColumnStackedComponent;
	let fixture: ComponentFixture<Ds3ChartColumnStackedComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3ChartColumnStackedComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3ChartColumnStackedComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
