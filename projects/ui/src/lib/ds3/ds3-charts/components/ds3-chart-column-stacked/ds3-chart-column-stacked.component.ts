import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";
@Component({
	selector: "ds3-chart-column-stacked",
	templateUrl: "./ds3-chart-column-stacked.component.html",
	styleUrls: ["./ds3-chart-column-stacked.component.scss"],
})
export class Ds3ChartColumnStackedComponent
	implements OnInit, OnChanges, OnDestroy
{
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() tooltip: ApexTooltip = {
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() height: number = 350;
	@Input() disableAnimations: boolean = false;
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			yaxis: this.yaxis,
			tooltip: this.tooltip,
			chart: {
				width: "100%",
				redrawOnWindowResize: true,
				redrawOnParentResize: true,
				animations: {
					enabled: !this.disableAnimations,
				},
				type: "bar",
				height: 350,
				stacked: true,
				toolbar: {
					show: false,
				},
				zoom: {
					enabled: true,
				},
			},
			plotOptions: {
				bar: {
					horizontal: false,
				},
			},
			fill: {
				opacity: 1,
			},
			legend: {
				show: true,
				showForSingleSeries: true,
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
		};
		return options;
	}
}
