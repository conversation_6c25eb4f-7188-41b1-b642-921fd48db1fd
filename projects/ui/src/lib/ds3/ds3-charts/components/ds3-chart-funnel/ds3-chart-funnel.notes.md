**Seletor:** `ds3-chart-funnel`

**Utilização:**`<ds3-chart-funnel></ds3-chart-funnel>`

`<ds3-chart-funnel>` é um componente criado a partir do ApexCharts para exibir dados através de um gráfico de funil.

O gráfico de funil é um gráfico de linhas e colunas, portando para exibir dados deve receber dois inputs. data e labels.

``` data ```

Recebe um array de numeros com os dados do gráfico.

``` labels ```

Recebe um array de strings com os nome correspondente a cada dado.

### Cores:

Existem 3 temas de cores possíveis para ser usado nos gráficos. *primary*, *secondary* e *tertiary*.

Os temas representam as cores de Support 4, 6 e 2 respectivamente.

Existem 8 cores pré-selecionadas nos gráficos. Não é possível mudar as cores de exibição do gráfico, mas é possível alterar a ordem delas através do input ``` colorOrder ```

O input ``` colorOrder ``` recebe um array de strings com as cores pré-selecionadas para o gráfico. A ordem de exibição das cores segue a ordem definida nesse array. 

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  chartOptions = {
    data: [44, 55, 41, 67, 22, 43],
    label: [
      "01/2011",
      "02/2011",
      "03/2011",
      "04/2011"
    ]
  }
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chart-funnel
  [data]="chartOptions.data"
  [label]="chartOptions.label"
>
</ds3-chart-funnel>
```
&nbsp;  

## Props / Inputs

| Property                                              | Values              | Default                      |
|---                                                    |                  ---|                           ---|
| @Input() id: string                                   |                     |Valor gerado aleatoriamente   |
| @Input() data: number[]                               |                     |undefined                     |
| @Input() labels: string[]                             |                     |undefined                     |
| @Input() height: number                               |                     |250                           |
| @Input() color: ['primary', 'secondary', 'tertiary']  |                     |'primary'                     |
| @Input() colorOrder: ['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|     |['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  