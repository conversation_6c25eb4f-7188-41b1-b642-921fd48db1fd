import {
	ChangeDetectorRef,
	Component,
	Input,
	<PERSON><PERSON><PERSON><PERSON>,
	OnDestroy,
	OnInit,
} from "@angular/core";
import {
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-funnel",
	templateUrl: "./ds3-chart-funnel.component.html",
	styleUrls: ["./ds3-chart-funnel.component.scss"],
})
export class Ds3ChartFunnelComponent implements OnInit {
	@Input() data: number[];
	@Input() labels: string[];
	@Input() infos: {
		data: number;
		percent: boolean;
		icon: string;
		showIcon: boolean;
		state: "disable" | "loss" | "alert" | "gain" | "default";
	}[] = [];
	@Input() columnClick: (value: number, data: number[], index: number) => any;
	@Input() disableAnimations: boolean = false;

	@Input() id: string;
	@Input() height: number = 250;

	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	public series;
	public colors;
	public percentage;
	public min: number;
	public max: number;

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.colors = getColorsBasedOnColorOrder(this.color, this.colorOrder);

		this.series = this.data.map((item, index) => {
			let arr = [];
			if (index === 0) {
				arr.push(item);
				arr.push(this.getAverageNumber(item, this.data[index + 1], index));
				arr.push(item);
			} else {
				arr.push(this.data[index - 1]);
				arr.push(this.getAverageNumber(item, this.data[index + 1], index));
				arr.push(item);
			}

			return arr;
		});

		this.setMinValue();
		this.setMaxValue();
		this.percentage = this.getPercentageValues();
	}

	setMinValue() {
		this.min = Math.min(...this.data) * 0.8;
	}
	setMaxValue() {
		this.max = Math.max(...this.data) * 1.6;
	}

	getPercentageValues() {
		const sum = this.data.reduce((acc, val) => acc + val);
		const percent = this.data.map((item) => {
			const result = ((item / sum) * 100).toFixed(1);
			return Number(result);
		});
		return percent;
	}

	getAverageNumber(current, next, index) {
		const last = this.data[index - 1];

		if (index === 0) {
			if (current < next) {
				return ((current + current) / 2) * 0.9;
			}
			return ((current + current) / 2) * 1.1;
		}

		if (index === this.data.length - 1 && this.data.length > 2) {
			if (last < current) {
				return ((last + current) / 2) * 0.9;
			}

			return ((last + current) / 2) * 1.1;
		}

		if (current < last) {
			return ((last + current) / 2) * 0.9;
		}
		return ((last + current) / 2) * 1.1;
	}
}
