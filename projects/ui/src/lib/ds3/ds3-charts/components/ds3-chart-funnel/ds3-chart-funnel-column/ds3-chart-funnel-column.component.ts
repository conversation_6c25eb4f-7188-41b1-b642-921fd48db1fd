import {
	ChangeDetectorRef,
	Component,
	Input,
	<PERSON><PERSON><PERSON><PERSON>,
	On<PERSON>estroy,
	OnInit,
} from "@angular/core";
import {
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-funnel-column",
	templateUrl: "./ds3-chart-funnel-column.component.html",
	styleUrls: ["./ds3-chart-funnel-column.component.scss"],
})
export class Ds3ChartFunnelColumnComponent
	implements OnInit, OnDestroy, OnChanges
{
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() data: [];

	@Input() label: string;
	@Input() value: string;
	@Input() color: string;
	@Input() info: {
		data: number;
		percent: boolean;
		icon: string;
		showIcon: boolean;
		state: "disable" | "loss" | "alert" | "gain" | "default";
	};

	@Input() percent: number;

	@Input() min: number;
	@Input() max: number;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() disableAnimations: boolean = false;

	@Input() height: number;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: [
				{
					type: "area",
					data: this.data,
				},
			],
			xaxis: {
				position: "top",
				crosshairs: {
					show: false,
				},
				axisBorder: {
					show: false,
				},
				labels: {
					show: false,
				},
				axisTicks: {
					show: false,
				},
			},
			yaxis: {
				min: this.min,
				max: this.max,
				labels: {
					show: false,
				},
			},
			colors: [this.color],
			chart: {
				width: 300,
				height: this.height,
				type: "bar",
				parentHeightOffset: 0,
				sparkline: {
					enabled: false,
				},
				toolbar: {
					show: false,
				},
				animations: {
					enabled: !this.disableAnimations,
				},
				zoom: {
					enabled: false,
				},
			},
			plotOptions: {
				bar: {
					columnWidth: "100%",
				},
			},
			stroke: {
				curve: "smooth",
			},
			dataLabels: {
				enabled: false,
			},
			legend: {
				show: false,
			},
			grid: {
				show: false,
			},
		};
		return options;
	}
}
