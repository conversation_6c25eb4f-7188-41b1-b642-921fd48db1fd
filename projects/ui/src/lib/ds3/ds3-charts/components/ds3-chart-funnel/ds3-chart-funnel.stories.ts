// @ts-ignore
import Notes from "./ds3-chart-funnel.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { array, object, select, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Funnel",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

const infos = [
	{
		data: 20,
		percent: true,
		icon: "pct pct-chevron-up",
		showIcon: true,
		state: "gain",
	},
	{
		data: 12,
		percent: true,
		icon: "pct pct-chevron-down",
		showIcon: true,
		state: "loss",
	},
];

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-funnel
				[data]="data"
				[labels]="labels"
				[infos]="infos"
				[columnClick]="columnClick"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-funnel>
		</div>
    `,
	props: {
		data: object("Series:", [80, 92]),
		labels: object("Labels:", ["Janeiro", "Fevereiro"]),
		infos: object("Infos:", infos),
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"green",
			"blue",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
		columnClick: (test, oe) => console.log("test", test, oe),
	},
});

export const threeColumns = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-funnel
				[data]="data"
				[labels]="labels"
				[columnClick]="columnClick"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-funnel>
		</div>
    `,
	props: {
		data: object("Series:", [45, 70, 24]),
		labels: object("Labels:", ["Janeiro", "Fevereiro", "Março"]),
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"pink",
			"purple",
			"green",
			"blue",
			"yellow",
			"orange",
			"red",
			"lightblue",
		]),
		columnClick: (test, oe) => console.log("test", test, oe),
	},
});

export const fourColumns = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-funnel
				[data]="data"
				[labels]="labels"
				[columnClick]="columnClick"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-funnel>
		</div>
    `,
	props: {
		data: object("Series:", [130, 70, 64, 24]),
		labels: object("Labels:", ["Janeiro", "Fevereiro", "Março", "Abril"]),
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"orange",
			"red",
			"green",
			"blue",
			"yellow",
			"pink",
			"purple",
			"lightblue",
		]),
		columnClick: (test, oe) => console.log("test", test, oe),
	},
});
