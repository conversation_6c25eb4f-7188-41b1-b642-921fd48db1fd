import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3ChartFunnelColumnComponent } from "./ds3-chart-funnel-column.component";

describe("Ds3ChartFunnelColumnComponent", () => {
	let component: Ds3ChartFunnelColumnComponent;
	let fixture: ComponentFixture<Ds3ChartFunnelColumnComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3ChartFunnelColumnComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3ChartFunnelColumnComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
