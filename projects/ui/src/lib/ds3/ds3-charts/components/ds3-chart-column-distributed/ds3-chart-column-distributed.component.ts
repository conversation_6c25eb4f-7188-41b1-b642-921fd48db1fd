import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-column-distributed",
	templateUrl: "./ds3-chart-column-distributed.component.html",
	styleUrls: ["./ds3-chart-column-distributed.component.scss"],
})
export class Ds3ChartColumnDistributedComponent
	implements OnInit, OnChanges, OnDestroy
{
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() disableAnimations: boolean = false;
	@Input() tooltip: ApexTooltip = {
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() height: number = 350;
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			yaxis: this.yaxis,
			tooltip: this.tooltip,
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
			chart: {
				height: this.height,
				type: "bar",
				toolbar: {
					show: false,
				},
				animations: {
					enabled: !this.disableAnimations,
				},
			},
			plotOptions: {
				bar: {
					columnWidth: "45%",
					distributed: true,
				},
			},
			dataLabels: {
				enabled: false,
			},
		};
		return options;
	}
}
