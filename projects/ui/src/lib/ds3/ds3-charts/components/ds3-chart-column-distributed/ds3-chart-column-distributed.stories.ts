// @ts-ignore
import Notes from "./ds3-chart-column-distributed.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Column Distributed",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

const series = [
	{
		name: "distibuted",
		data: [21, 22, 10, 28, 16, 21, 13, 30],
	},
];
const xaxis = {
	categories: [
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
		"<PERSON>",
	],
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-column-distributed
				[series]="series"
				[xaxis]="xaxis"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-column-distributed>
		</div>
    `,
	props: {
		series: object("Series:", series),
		xaxis: object("xAxis:", xaxis),
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
	},
});
