import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3ChartColumnDistributedComponent } from "./ds3-chart-column-distributed.component";

describe("Ds3ChartColumnDistributedComponent", () => {
	let component: Ds3ChartColumnDistributedComponent;
	let fixture: ComponentFixture<Ds3ChartColumnDistributedComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3ChartColumnDistributedComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3ChartColumnDistributedComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
