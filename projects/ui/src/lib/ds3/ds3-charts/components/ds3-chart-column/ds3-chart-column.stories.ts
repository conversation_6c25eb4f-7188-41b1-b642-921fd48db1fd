// @ts-ignore
import Notes from "./ds3-chart-column.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Column",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

const series = [
	{
		name: "Produto A",
		data: [44, 55, 41, 67, 22, 43],
	},
	{
		name: "Produto B",
		data: [13, 23, 20, 8, 13, 27],
	},
	{
		name: "Produto C",
		data: [11, 17, 15, 15, 21, 14],
	},
	{
		name: "Produto D",
		data: [21, 7, 25, 13, 22, 8],
	},
];

const xaxis = {
	type: "category",
	categories: [
		"01/2011",
		"02/2011",
		"03/2011",
		"04/2011",
		"05/2011",
		"06/2011",
	],
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-column
				[series]="series"
				[xaxis]="xaxis"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-column>
		</div>
    `,
	props: {
		series: object("Series:", series),
		xaxis: object("xAxis:", xaxis),
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
	},
});
