**Seletor:** `ds3-chart-column`

**Utilização:**`<ds3-chart-column></ds3-chart-column>`

`<ds3-chart-column>` é um componente criado a partir do ApexCharts para exibir dados através de um gráfico de colunas.

O gráfico de colunas é um gráfico de linhas e colunas, portando para exibir dados deve receber dois inputs. Series e xaxis.

``` series ```

Recebe um array de objetos com o nome e dados do gráfico.

``` xaxis ```

Recebe um array de números com os valores do eixo x.

Para mais informações, acesse a documentação do apexcharts.

[Apexcharts Docs](https://apexcharts.com/docs/installation/)


[Apexcharts Column Chart Demo](https://apexcharts.com/angular-chart-demos/column-charts/basic/)

### Cores:

Existem 3 temas de cores possíveis para ser usado nos gráficos. *primary*, *secondary* e *tertiary*.

Os temas representam as cores de Support 4, 6 e 2 respectivamente.

Existem 8 cores pré-selecionadas nos gráficos. Não é possível mudar as cores de exibição do gráfico, mas é possível alterar a ordem delas através do input ``` colorOrder ```

O input ``` colorOrder ``` recebe um array de strings com as cores pré-selecionadas para o gráfico. A ordem de exibição das cores segue a ordem definida nesse array. 

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  chartOptions = {
    series: [
      {
        name: "Produto A",
        data: [44, 55, 41, 67, 22, 43]
      },
      {
        name: "Produto B",
        data: [13, 23, 20, 8, 13, 27]
      },
      {
        name: "Produto C",
        data: [11, 17, 15, 15, 21, 14]
      },
      {
        name: "Produto D",
        data: [21, 7, 25, 13, 22, 8]
      }
    ],
    xaxis: {
      type: "category",
      categories: [
        "01/2011",
        "02/2011",
        "03/2011",
        "04/2011",
        "05/2011",
        "06/2011"
      ]
    }
  }
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chart-column
  [series]="chartOptions.series"
  [xaxis]="chartOptions.xaxis"
>
</ds3-chart-column>
```
&nbsp;  

## Props / Inputs

| Property                                              | Values              | Default                      |
|---                                                    |                  ---|                           ---|
| @Input() id: string                                   |                     |Valor gerado aleatoriamente   |
| @Input() series: ApexAxisChartSeries                  |                     |undefined                     |
| @Input() xaxis: ApexXAxis                             |                     |undefined                     |
| @Input() yaxis: ApexYAxis                             |                     |undefined                     |
| @Input() tooltip: ApexTooltip                         |                     |undefined                     |
| @Input() height: number                               |                     |350                           |
| @Input() color: ['primary', 'secondary', 'tertiary']  |                     |'primary'                     |
| @Input() colorOrder: ['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|     |['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  