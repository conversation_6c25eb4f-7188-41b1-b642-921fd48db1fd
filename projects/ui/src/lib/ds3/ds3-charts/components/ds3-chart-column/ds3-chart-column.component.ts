import {
	ChangeDetectorRef,
	Component,
	Input,
	<PERSON><PERSON><PERSON><PERSON>,
	OnDestroy,
	OnInit,
} from "@angular/core";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-column",
	templateUrl: "./ds3-chart-column.component.html",
	styleUrls: ["./ds3-chart-column.component.scss"],
})
export class Ds3ChartColumnComponent implements OnInit, OnChanges, OnDestroy {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() height: number = 350;
	@Input() disableAnimations: boolean = false;
	@Input() tooltip: ApexTooltip = {
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			yaxis: this.yaxis,
			tooltip: this.tooltip,
			chart: {
				width: "100%",
				redrawOnWindowResize: true,
				redrawOnParentResize: true,
				animations: {
					enabled: !this.disableAnimations,
				},
				type: "bar",
				height: this.height,
				toolbar: {
					show: false,
				},
			},
			plotOptions: {
				bar: {
					horizontal: false,
					columnWidth: "55%",
				},
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
			dataLabels: {
				enabled: false,
			},
			stroke: {
				show: true,
				width: 2,
				colors: ["transparent"],
			},
		};
		return options;
	}
}
