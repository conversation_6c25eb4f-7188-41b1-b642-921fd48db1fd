// @ts-ignore
import Notes from "./ds3-chart-bar.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Bar",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

const series = [
	{
		name: "Google",
		data: [44],
	},
	{
		name: "Facebook",
		data: [53],
	},
	{
		name: "Instagram",
		data: [12],
	},
	{
		name: "TikTok",
		data: [9],
	},
];

const xaxis = {
	categories: [2008],
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-bar
				[series]="series"
				[xaxis]="xaxis"
				[color]="color"
				[colorOrder]="colorOrder"
			></ds3-chart-bar>
		</div>
    `,
	props: {
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
		series: object("Series:", series),
		xaxis: object("xAxis:", xaxis),
	},
});
