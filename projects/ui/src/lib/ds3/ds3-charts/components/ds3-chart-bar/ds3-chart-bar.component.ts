import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ApexOptions } from "apexcharts";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";

@Component({
	selector: "ds3-chart-bar",
	templateUrl: "./ds3-chart-bar.component.html",
	styleUrls: ["./ds3-chart-bar.component.scss"],
})
export class Ds3ChartBarComponent implements OnInit, OnChanges, OnDestroy {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() xaxis: ApexXAxis;
	@Input() yaxis: ApexYAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() events: any;
	@Input() tooltip: ApexTooltip = {
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() height: number = 350;
	@Input() disableAnimations: boolean = false;
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];
	@Input()
	stackType: string = "100%";
	@Input()
	barHeight: string;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private getOptions() {
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			yaxis: this.yaxis,
			tooltip: this.tooltip,
			chart: {
				width: "100%",
				redrawOnWindowResize: true,
				redrawOnParentResize: true,
				type: "bar",
				height: this.height,
				stacked: true,
				stackType: this.stackType,
				toolbar: {
					show: false,
				},
				animations: {
					enabled: !this.disableAnimations,
				},
				events: this.events,
			},
			plotOptions: {
				bar: {
					horizontal: true,
				},
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
		} as ApexOptions;

		if (this.barHeight) {
			options.plotOptions.bar.barHeight = this.barHeight;
		}
		return options;
	}
}
