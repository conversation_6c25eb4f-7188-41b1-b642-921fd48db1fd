import {
	ChangeDetectorRef,
	Component,
	Input,
	<PERSON><PERSON><PERSON><PERSON>,
	OnDestroy,
	OnInit,
} from "@angular/core";
import {
	getColorsBasedOnColorOrder,
	getPieLabel,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";
@Component({
	selector: "ds3-chart-donut",
	templateUrl: "./ds3-chart-donut.component.html",
	styleUrls: ["./ds3-chart-donut.component.scss"],
})
export class Ds3ChartDonutComponent implements OnInit, OnDestroy, OnChanges {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() labels;
	@Input() series: ApexAxisChartSeries;
	@Input() height: number = 240;
	@Input() tooltip: ApexTooltip = {
		fillSeriesColor: false,
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() disableAnimations: boolean = false;
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];
	@Input() showLabel: boolean = true;
	@Input() showAverage: boolean = false;
	@Input() pieLabel: string;
	@Input() pieFormatter = (w) => {
		const total = w.globals.seriesTotals.reduce((a, b) => {
			return a + b;
		}, 0);

		if (this.showAverage) {
			const length = w.globals.seriesTotals.length;
			return total / length;
		}

		return total;
	};

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			labels: this.labels,
			tooltip: this.tooltip,
			chart: {
				type: "donut",
				animations: {
					enabled: !this.disableAnimations,
				},
				height: this.height,
			},
			dataLabels: {
				dropShadow: {
					enabled: false,
				},
			},
			plotOptions: {
				pie: {
					expandOnClick: false,
					donut: {
						labels: {
							show: true,
							name: {
								show: true,
								offsetY: 20,
							},
							value: {
								show: true,
								offsetY: -20,
							},
							total: {
								show: this.showLabel,
								showAlways: false,
								label: getPieLabel(this.showAverage, this.pieLabel),
								formatter: this.pieFormatter,
							},
						},
					},
				},
			},
			stroke: {
				show: false,
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
		};
		return options;
	}
}
