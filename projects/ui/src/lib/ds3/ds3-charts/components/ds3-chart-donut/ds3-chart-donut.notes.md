**Seletor:** `ds3-chart-donut`

**Utilização:**`<ds3-chart-donut></ds3-chart-donut>`

`<ds3-chart-donut>` é um componente criado a partir do ApexCharts para exibir dados através de um gráfico de donut.

O gráfico de donut é um gráfico circular, portando para exibir dados deve receber dois inputs. Series e Labels.

``` series ```

Recebe um array de valores com os dados do gráfico.

``` labels ```

Recebe um array de strings com o nome de cada valor.

Para mais informações, acesse a documentação do apexcharts.

[Apexcharts Docs](https://apexcharts.com/docs/installation/)


[Apexcharts Donut Chart Demo](https://apexcharts.com/javascript-chart-demos/pie-charts/simple-donut/)

### Cores:

Existem 3 temas de cores possíveis para ser usado nos gráficos. *primary*, *secondary* e *tertiary*.

Os temas representam as cores de Support 4, 6 e 2 respectivamente.

Existem 8 cores pré-selecionadas nos gráficos. Não é possível mudar as cores de exibição do gráfico, mas é possível alterar a ordem delas através do input ``` colorOrder ```

O input ``` colorOrder ``` recebe um array de strings com as cores pré-selecionadas para o gráfico. A ordem de exibição das cores segue a ordem definida nesse array. 

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  chartOptions = {
    series: [
      79,
      94,
      18,
      83
    ],
    labels: ['Gordura', 'Músculos', 'Ossos', 'Resíduos']
  }
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chart-donut
  [series]="chartOptions.series"
  [labels]="chartOptions.labels"
>
</ds3-chart-donut>
```
&nbsp;  

## Props / Inputs

| Property                                              | Values              | Default                      |
|---                                                    |                  ---|                           ---|
| @Input() id: string                                   |                     |Valor gerado aleatoriamente   |
| @Input() series: ApexAxisChartSeries                  |                     |undefined                     |
| @Input() labels: any                                  |                     |undefined                     |
| @Input() tooltip: ApexTooltip                         |                     |{fillSeriesColor: false,y: {  formatter: (val) => '' + val}  }                     |
| @Input() height: number                               |                     |350                           |
| @Input() color: ['primary', 'secondary', 'tertiary']  |                     |'primary'                     |
| @Input() colorOrder: ['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|     |['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|
| @Input() showAverage: boolean                         |                     |false                         |
| @Input() pieLabel: string                             |                     |undefined                     |
| @Input() pieFormatter: (v) => v                       |                     |(w) => { const total = w.globals.seriesTotals.reduce((a, b) => { return a + b }, 0); if(this.showAverage) { const length = w.globals.seriesTotals.length; return total / length; } return total; }                     |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  