// @ts-ignore
import Notes from "./ds3-chart-donut.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Donut",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

const series = [79, 94, 18, 83];

const labels = ["Gordura", "Músculos", "Ossos", "Resíduos"];

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-donut
				[series]="series"
				[labels]="labels"
				[color]="color"
				[colorOrder]="colorOrder"
				[pieLabel]="pieLabel"
				[showAverage]="showAverage"
				[showLabel]="showLabel"
			></ds3-chart-donut>
		</div>
    `,
	props: {
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
		series: object("Series:", series),
		labels: object("Labels:", labels),
		showLabel: boolean("Mostrar Label:", false),
		showAverage: boolean("Mostrar Média:", true),
		pieLabel: text(" Label:", ""),
	},
});
