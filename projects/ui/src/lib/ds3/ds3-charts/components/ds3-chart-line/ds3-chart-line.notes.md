**Seletor:** `ds3-chart-line`

**Utilização:**`<ds3-chart-line></ds3-chart-line>`

`<ds3-chart-line>` é um componente criado a partir do ApexCharts para exibir dados através de um gráfico de linhas.

O gráfico de linhas é um gráfico de linhas e colunas, portando para exibir dados deve receber dois inputs. Series e xaxis.

``` series ```

Recebe um array de objetos com o nome e dados do gráfico.

``` xaxis ```

Recebe um array de números com os valores do eixo x.

Para mais informações, acesse a documentação do apexcharts.

[Apexcharts Docs](https://apexcharts.com/docs/installation/)


[Apexcharts Column Chart Demo](https://apexcharts.com/angular-chart-demos/line-charts/basic/)

### Cores:

Existem 3 temas de cores possíveis para ser usado nos gráficos. *primary*, *secondary* e *tertiary*.

Os temas representam as cores de Support 4, 6 e 2 respectivamente.

Existem 8 cores pré-selecionadas nos gráficos. Não é possível mudar as cores de exibição do gráfico, mas é possível alterar a ordem delas através do input ``` colorOrder ```

O input ``` colorOrder ``` recebe um array de strings com as cores pré-selecionadas para o gráfico. A ordem de exibição das cores segue a ordem definida nesse array. 

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  chartOptions = {
    series: [
			{
				name: "Desktops",
				data: [10, 75, 35, 51, 49, 22, 69]
			},
			{
				name: "Notebooks",
				data: [10, 41, 35, 12, 49, 44, 69]
			},
			{
				name: "Mobile",
				data: [10, 20, 96, 51, 49, 62, 88]
			}
		],
    xaxis: {
			categories: [
				"01 Jan",
				"02 Jan",
				"03 Jan",
				"04 Jan",
				"05 Jan",
				"06 Jan",
				"07 Jan",
			]
		}
  }
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chart-line
  [series]="chartOptions.series"
  [xaxis]="chartOptions.xaxis"
>
</ds3-chart-line>
```
&nbsp;  

## Props / Inputs

| Property                                              | Values              | Default                      |
|---                                                    |                  ---|                           ---|
| @Input() id: string                                   |                     |Valor gerado aleatoriamente   |
| @Input() series: ApexAxisChartSeries                  |                     |undefined                     |
| @Input() xaxis: ApexXAxis                             |                     |undefined                     |
| @Input() yaxis: ApexYAxis                             |                     |undefined                     |
| @Input() tooltip: ApexTooltip                         |                     |undefined                     |
| @Input() height: number                               |                     |350                           |
| @Input() color: ['primary', 'secondary', 'tertiary']  |                     |'primary'                     |
| @Input() colorOrder: ['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|     |['blue', 'green', 'yellow', 'orange', 'red', 'pink', 'purple', 'lightblue']|

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  