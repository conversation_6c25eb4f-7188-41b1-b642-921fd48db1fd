// @ts-ignore
import Notes from "./ds3-chart-line.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Line",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-line
				[series]="series"
				[xaxis]="xaxis"
				[colorOrder]="colorOrder"
				[color]="color"
			></ds3-chart-line>
		</div>
    `,
	props: {
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color order: ", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
		series: object("Series:", [
			{
				name: "Desktops",
				data: [10, 75, 35, 51, 49, 22, 69],
			},
			{
				name: "Notebooks",
				data: [10, 41, 35, 12, 49, 44, 69],
			},
			{
				name: "Mobile",
				data: [10, 20, 96, 51, 49, 62, 88],
			},
		]),
		xaxis: object("XAxis: ", {
			categories: [
				"01 Jan",
				"02 Jan",
				"03 Jan",
				"04 Jan",
				"05 Jan",
				"06 Jan",
				"07 Jan",
			],
		}),
	},
});
