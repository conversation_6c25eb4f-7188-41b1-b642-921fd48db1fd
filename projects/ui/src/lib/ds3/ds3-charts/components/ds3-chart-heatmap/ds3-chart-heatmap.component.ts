import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
} from "@angular/core";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";
import { getCssVariableAndConvertToHex } from "../../../ds3.utils";

enum ChartHeatmapColors {
	blue = "blue",
	green = "green",
	yellow = "yellow",
	orange = "orange",
	red = "red",
	pink = "pink",
	purple = "purple",
	lightblue = "lightblue",
}

enum ChartHeatmapFrequency {
	very_low = "very_low",
	low = "low",
	medium = "medium",
	high = "high",
	extreme = "extreme",
}

@Component({
	selector: "ds3-chart-heatmap",
	templateUrl: "./ds3-chart-heatmap.component.html",
	styleUrls: ["./ds3-chart-heatmap.component.scss"],
})
export class Ds3ChartHeatmapComponent implements OnInit, OnChang<PERSON>, OnDestroy {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() labels;
	@Input() xaxis: ApexXAxis;
	@Input() series: ApexAxisChartSeries;
	@Input() height: number = 350;
	@Input() color: ChartHeatmapColors = ChartHeatmapColors.blue;
	@Input() disableAnimations: boolean = false;
	@Input() customTooltip: ({
		series,
		seriesIndex,
		dataPointIndex,
		customTooltipData,
		x,
		y,
	}) => string;
	@Input() customTooltipData: any;
	@Input() ranges: Array<{
		from?: number;
		to?: number;
		color?: string;
		foreColor?: string;
		name?: string;
	}> = new Array<{
		from?: number;
		to?: number;
		color?: string;
		foreColor?: string;
		name?: string;
	}>();

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		this._configRange();
		const options = {
			series: this.series,
			xaxis: this.xaxis,
			chart: {
				height: this.height,
				type: "heatmap",
				toolbar: {
					show: false,
				},
				animations: {
					enabled: !this.disableAnimations,
				},
			},
			plotOptions: {
				heatmap: {
					shadeIntensity: 0.5,
					colorScale: {
						ranges: this.ranges,
					},
				},
			},
			tooltip: {
				enabled: true,
				shared: true,
				followCursor: true,
				custom: ({ series, seriesIndex, dataPointIndex, x, y }) =>
					this.customTooltip({
						series,
						seriesIndex,
						dataPointIndex,
						customTooltipData: this.customTooltipData,
						x,
						y,
					}),
			},
			legend: {
				show: true,
				position: "bottom",
			},
			dataLabels: {
				enabled: false,
			},
			states: {
				hover: {
					filter: {
						type: "none",
					},
				},
			},
		};
		return options;
	}

	private _configRange() {
		if (!this.ranges || this.ranges.length === 0) {
			this.ranges = new Array<{
				from?: number;
				to?: number;
				color?: string;
				foreColor?: string;
				name?: string;
			}>(
				{
					from: 0,
					to: 20,
				},
				{
					from: 21,
					to: 50,
				},
				{
					from: 51,
					to: 75,
				},
				{
					from: 76,
					to: 94,
				},
				{
					from: 95,
					to: 100,
				}
			);
		}
		this.ranges.forEach((range, index) => {
			switch (index) {
				case 0:
					range.name = "Muito baixo";
					range.color = this.getHeatmapColorBasedOnFrequency(
						this.color,
						ChartHeatmapFrequency.very_low
					);
					break;
				case 1:
					range.name = "Baixo";
					range.color = this.getHeatmapColorBasedOnFrequency(
						this.color,
						ChartHeatmapFrequency.low
					);
					break;
				case 2:
					range.name = "Médio";
					range.color = this.getHeatmapColorBasedOnFrequency(
						this.color,
						ChartHeatmapFrequency.medium
					);
					break;
				case 3:
					range.name = "Alto";
					range.color = this.getHeatmapColorBasedOnFrequency(
						this.color,
						ChartHeatmapFrequency.high
					);
					break;
				case 4:
					range.name = "Extremo";
					range.color = this.getHeatmapColorBasedOnFrequency(
						this.color,
						ChartHeatmapFrequency.extreme
					);
					break;
			}
		});
	}

	private getHeatmapColorBasedOnFrequency(
		color: ChartHeatmapColors,
		frequency: ChartHeatmapFrequency
	) {
		let colorVariable;
		switch (frequency) {
			case ChartHeatmapFrequency.very_low:
				colorVariable = getCssVariableAndConvertToHex(
					"--color-support-" + color + "-1"
				);
				break;
			case ChartHeatmapFrequency.low:
				colorVariable = getCssVariableAndConvertToHex(
					"--color-support-" + color + "-3"
				);
				break;
			case ChartHeatmapFrequency.medium:
				colorVariable = getCssVariableAndConvertToHex(
					"--color-support-" + color + "-4"
				);
				break;
			case ChartHeatmapFrequency.high:
				colorVariable = getCssVariableAndConvertToHex(
					"--color-support-" + color + "-5"
				);
				break;
			case ChartHeatmapFrequency.extreme:
				colorVariable = getCssVariableAndConvertToHex(
					"--color-support-" + color + "-6"
				);
				break;
		}

		return colorVariable;
	}
}
