// @ts-ignore
import Notes from "./ds3-chart-heatmap.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, select, text, withKnobs } from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Heatmap",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-heatmap
				[series]="chartOptions.series"
				[xaxis]="chartOptions.xaxis"
				[color]="color"
			></ds3-chart-heatmap>
		</div>
    `,
	props: {
		chartOptions: {
			series: [
				{
					name: "Metric1",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric2",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric3",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric4",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric5",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric6",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric7",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric8",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
				{
					name: "Metric9",
					data: generateData(7, {
						min: 0,
						max: 100,
					}),
				},
			],
			xaxis: {
				type: "category",
				categories: ["1", "2", "3", "4", "5", "6", "7"],
			},
		},
		color: select("Color:", colors, "red"),
	},
});

const colors = {
	blue: "blue",
	green: "green",
	yellow: "yellow",
	orange: "orange",
	red: "red",
	pink: "pink",
	purple: "purple",
	lightblue: "lightblue",
};

const generateData = (count, yrange) => {
	var i = 0;
	var series = [];
	while (i < count) {
		var x = "w" + (i + 1).toString();
		var y =
			Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;

		series.push({
			x: x,
			y: y,
		});
		i++;
	}
	return series;
};
