**Seletor:** `ds3-chart-heatmap`

**Utilização:**`<ds3-chart-heatmap></ds3-chart-heatmap>`

`<ds3-chart-heatmap>` é um componente criado a partir do ApexCharts para exibir dados através de um gráfico de mapa de calor.

O gráfico de mapa de calor é um gráfico de linhas e colunas, para exibir dados deve receber dois inputs. series e xaxis.

``` series ```

Recebe um array de objetos com o nome e dados do gráfico.

``` xaxis ```

Recebe um array de números com os valores do eixo x.

### Cores:

Existem 3 temas de cores possíveis para ser usado nos gráficos. *primary*, *secondary* e *tertiary*.

Os temas representam as cores de Support 4, 6 e 2 respectivamente.

Existem 8 cores pré-selecionadas nos gráficos. Não é possível mudar as cores de exibição do gráfico, mas é possível alterar a ordem delas através do input ``` colorOrder ```

O input ``` colorOrder ``` recebe um array de strings com as cores pré-selecionadas para o gráfico. A ordem de exibição das cores segue a ordem definida nesse array. 

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  chartOptions = {
    series: [
      {
        name: "Metric1",
        data: [44, 55, 41, 67, 22, 43, 95]
      },
      {
        name: "Metric2",
        data: [44, 32, 58, 67, 22, 43, 95]
      },
      {
        name: "Metric3",
        data: [58, 67, 22, 67, 40, 08, 79]
      },
      {
        name: "Metric4",
        data: [58, 67, 22, 67, 40, 08, 79]
      },
      {
        name: "Metric5",
        data: [44, 55, 41, 67, 22, 43, 95]
      },
      {
        name: "Metric6",
        data: [44, 55, 41, 67, 22, 43, 95]
      },
      {
        name: "Metric7",
        data: [44, 55, 41, 67, 22, 43, 95]
      },
      {
        name: "Metric8",
        data: [58, 67, 22, 67, 40, 08, 79]
      },
      {
        name: "Metric9",
        data: [58, 67, 22, 67, 40, 08, 79]
      }
    ],
    xaxis: {
      type: "category",
      categories: [
        "01/2011",
        "02/2011",
        "03/2011",
        "04/2011",
        "05/2011",
        "06/2011"
      ]
    }
  }
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-chart-heatmap
  [series]="chartOptions.series"
  [xaxis]="chartOptions.xaxis"
>
</ds3-chart-heatmap>
```
&nbsp;  

## Props / Inputs

| Property                                              | Values              | Default                      |
|---                                                    |                  ---|                           ---|
| @Input() id: string                                   |                     |Valor gerado aleatoriamente   |
| @Input() xaxis: ApexXAxis                             |                     |undefined                     |
| @Input() series: ApexAxisChartSeries                  |                     |undefined                     |
| @Input() height: number                               |                     |250                           |
| @Input() color: ['primary', 'secondary', 'tertiary']  |                     |'primary'                     |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|


## More info

No more info.

&nbsp;  