import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	buildChart,
	getColorsBasedOnColorOrder,
	getPieLabel,
	setIdIfNotProvided,
} from "../../ds3-charts.utils";
@Component({
	selector: "ds3-chart-middle",
	templateUrl: "./ds3-chart-middle.component.html",
	styleUrls: ["./ds3-chart-middle.component.scss"],
})
export class Ds3ChartMiddleComponent implements OnInit, OnChanges, OnDestroy {
	public chartOptions: ApexCharts.ApexOptions;

	private chartInstance;

	@Input() id: string;
	@Input() labels;
	@Input() series: ApexAxisChartSeries;
	@Input() height: number = 290;
	@Input() disableAnimations: boolean = false;
	@Input() tooltip: ApexTooltip = {
		fillSeriesColor: false,
		y: {
			formatter: (val) => "" + val,
		},
	};
	@Input() color: "primary" | "secondary" | "tertiary" = "primary";
	@Input() colorOrder: [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue"
	] = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];
	@Input() showLabel: boolean = true;
	@Input() showAverage: boolean = false;
	@Input() pieLabel: string;
	@Input() pieFormatter = (w) => {
		const total = w.globals.seriesTotals.reduce((a, b) => {
			return a + b;
		}, 0);

		if (this.showAverage) {
			const length = w.globals.seriesTotals.length;
			return total / length;
		}

		return total;
	};

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.id = setIdIfNotProvided(this.id);
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: this.series,
			labels: this.labels,
			tooltip: this.tooltip,
			chart: {
				type: "donut",
				height: this.height,
				animations: {
					enabled: !this.disableAnimations,
				},
			},
			dataLabels: {
				dropShadow: {
					enabled: false,
				},
			},
			plotOptions: {
				pie: {
					expandOnClick: false,
					startAngle: -90,
					endAngle: 90,
					donut: {
						labels: {
							show: true,
							name: {
								show: true,
								offsetY: -20,
							},
							value: {
								show: true,
								offsetY: -60,
								color: "#55585E",
							},
							total: {
								show: this.showLabel,
								showAlways: false,
								label: getPieLabel(this.showAverage, this.pieLabel),
								formatter: this.pieFormatter,
							},
						},
					},
				},
			},
			grid: {
				padding: {
					bottom: -200,
				},
			},
			legend: {
				position: "bottom",
			},
			stroke: {
				show: false,
			},
			colors: getColorsBasedOnColorOrder(this.color, this.colorOrder),
		};
		return options;
	}
}
