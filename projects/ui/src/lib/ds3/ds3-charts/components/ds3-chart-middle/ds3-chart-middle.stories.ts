// @ts-ignore
import Notes from "./ds3-chart-middle.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	array,
	boolean,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3Module } from "../../../ds3.module";
import { Ds3ChartsModule } from "../../ds3-charts.module";
import { colorOptions } from "../../ds3-charts.utils";

export default {
	title: "Design System 3 | Data/Charts/Middle",
	decorators: [
		moduleMetadata({
			imports: [Ds3ChartsModule, Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-chart-middle
				[series]="chartOptions.series"
				[labels]="chartOptions.labels"
				[color]="color"
				[colorOrder]="colorOrder"
				[pieLabel]="pieLabel"
				[showAverage]="showAverage"
				[showLabel]="showLabel"
			></ds3-chart-middle>
		</div>
    `,
	props: {
		chartOptions: {
			series: [79, 94, 18, 83],
			labels: ["Team A", "Team B", "Team C", "Team D", "Team E"],
		},
		color: select("Color:", colorOptions, "primary"),
		colorOrder: array("Color", [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		]),
		showLabel: boolean("Mostrar Label:", true),
		showAverage: boolean("Mostrar Média:", true),
		pieLabel: text("Label:", ""),
	},
});
