import { moduleMetadata } from "@storybook/angular";
import {
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import Notes from "./ds3-bar-line.md"; // eslint-disable-line
import {
	BarCorFundo,
	BarCorLinha,
	Ds3BarLineComponent,
} from "./ds3-bar-line.component";
import { Ds3Module } from "../ds3.module";

export default {
	title: "Design System 3 | Data/Line Bar",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Bar = () => ({
	template: `
    <div style="padding:20px;">
	tamanho minimo
	<div style="width:364px;">
    <ds3-bar-line [barData]="barData"></ds3-bar-line>
    </div>
	<hr>
	tamanho minimo 2x
	<div style="width:728px;">
    <ds3-bar-line [barData]="barData"></ds3-bar-line>
    </div>
	<hr>
	tamanho minimo 3x
	<div style="width:1092px;">
    <ds3-bar-line [barData]="barData"></ds3-bar-line>
    </div>
	<hr>
	tamanho 100% da div
	<div style="width:100%;">
    <ds3-bar-line [barData]="barData"></ds3-bar-line>
    </div>
	<hr>
	tamanho inferior ao minimo
	<div style="width:280px;">
    <ds3-bar-line [barData]="barData"></ds3-bar-line>
    </div>
	<hr> 
	<ds3-bar-line [barData]="barData"></ds3-bar-line>
</div>
    `,
	props: {
		barData: object("configuracao bar", {
			etapas: [
				{
					nome: "primeira etapa",
					mostraNome: true,
					corLinha: BarCorLinha.info,
					corFundo: BarCorFundo.default,
				},
				{
					nome: "segunda etapa",
					mostraNome: true,
					corLinha: BarCorLinha.info,
					corFundo: BarCorFundo.default,
				},
			],
			marcadores: [
				{
					nome: "META",
					porcentagem: 50,
				},
			],
			nome: "exemplo de barline",
			info: "um exemplo com varias configurações do barline",
			descricao: "descricao adicional",
			porcentagem: 45,
			mostraNome: true,
			mostraPorcentagem: true,
			porcentagemComoInfo: false,
		}),
	},
});
