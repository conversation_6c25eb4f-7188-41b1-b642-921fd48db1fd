<div
	*ngIf="!!barData && !!barData.etapas && barData.etapas.length > 0"
	class="ds3-bar-line">
	<div class="superior">
		<div class="nome">
			<span class="nome-texto">{{ barData.nome }}</span>
		</div>
		<div class="info">
			<span
				(click)="handleInfoClick(barData.info)"
				*ngIf="barData.info && !barData.porcentagemComoInfo"
				[ngClass]="{
					'info-texto': !textClickable,
					'info-text-clickable': textClickable
				}">
				{{ barData.info }}
			</span>
			<div
				*ngIf="
					barData.porcentagem &&
					barData.mostraPorcentagem &&
					barData.porcentagemComoInfo
				"
				class="info-texto">
				<span>{{ barData.porcentagem }}</span>
				<small>%</small>
			</div>
		</div>
	</div>
	<div class="inferior">
		<div class="etapas">
			<ng-container *ngFor="let marcador of barData.marcadores">
				<div [ngStyle]="ajustarMarcador(marcador.porcentagem)" class="marcador">
					<span
						[ds3Tooltip]="marcador.porcentagem.toString()"
						class="marcador-texto">
						{{ marcador.nome }}
					</span>
				</div>
			</ng-container>
			<div
				*ngFor="
					let etapa of barData.etapas;
					index as index;
					first as ehInicial;
					last as ehFinal
				"
				class="etapa">
				<div
					[ngClass]="{
						ehInicial: ehInicial,
						ehFinal: ehFinal,
						unico: barData.etapas.length === 1
					}"
					[ngStyle]="prenchedor(index, etapa)"
					class="barra"></div>
				<small *ngIf="etapa.mostraNome">{{ etapa.nome }}</small>
			</div>
		</div>
		<div
			*ngIf="
				barData.porcentagem &&
				barData.mostraPorcentagem &&
				!barData.porcentagemComoInfo
			"
			class="porcentagem">
			<span>{{ barData.porcentagem | number : "2.2-2" }}</span>
			<small>%</small>
		</div>
	</div>
	<div class="descricao">
		<span class="descricao-texto">{{ barData.descricao }}</span>
	</div>
</div>
