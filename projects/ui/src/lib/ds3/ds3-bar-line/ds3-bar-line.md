# Tooltip

| Table Of Contents           |
| --------------------------- |
| [Usabilidade](#usabilidade) |
| [Inputs](#inputs)           |
| [Outputs](#outputs)         |
| [Example](#example)         |

## Usabilidade

componente de bar line é altamente customizavel para diferentes casos de uso como por exemplo usando
o funcionamento do ds3-bar-line é implementado com um um seletor html e um objeto de configuração

> seletor no html

```html
<ds3-bar-line [barData]="objetoDeConfiguracao"></ds3-bar-line>
```

> objeto de configuração de exemplo

```ts
// ds3BarData deve ser importado e segue o seguinte padrão

//  Ds3BarData {
//   steps: { name: string }[],
//   label?: string,
//   info?: string,
//   percentage?: number,
//   color: BarsColors,
//   colorBg: BarsBgColors,
// }

// os enums de BarCorLinha e BarCorFundo devem ser importados e tem os seguintes valores

//  BarCorLinha {
//   "info" = "var(--color-feedback-info-2)",
//   "gain" = "var(--color-feedback-gain-2)",
//   "alert" = "var(--color-feedback-alert-2)",
//   "loss" = "var(--color-feedback-loss-2)",
//   "blue" = "var(--color-support-blue-4)",
//   "green" = "var(--color-support-green-4)",
//   "lightblue" = "var(--color-support-lightblue-4)",
//   "orange" = "var(--color-support-pink-4)",
//   "pink" = "var(--color-support-orange-4)",
//   "purple" = "var(--color-support-purple-4)",
//   "red" = "var(--color-support-red-4)",
//   "yellow" = "var(--color-support-yellow-4)"
// }

//  BarCorFundo {
//   "default_bg" = "var(--color-support-gray-2)",
//   "info_bg" = "var(--color-support-blue-1)",
//   "gain_bg" = "var(--color-support-green-1)",
//   "alert_bg" = "var(--color-support-yellow-1)",
//   "loss_bg" = "var(--color-support-red-1)",
//   "blue_bg" = "var(--color-support-blue-1)",
//   "green_bg" = "var(--color-support-green-1)",
//   "lightblue_bg" = "var(--color-support-lightblue-1)",
//   "orange_bg" = "var(--color-support-pink-1)",
//   "pink_bg" = "var(--color-support-orange-1)",
//   "purple_bg" = "var(--color-support-purple-1)",
//   "red_bg" = "var(--color-support-red-1)",
//   "yellow_bg" = "var(--color-support-yellow-1)"
// }

objetoDeConfiguracao: Ds3BarData = {
	etapas:[
		{
			nome: 'primeira etapa',
    mostraNome: true,
    corLinha: BarCorLinha.info,
    corFundo: BarCorFundo.default,
},
		{
			nome: 'segunda etapa',
    mostraNome: true,
    corLinha: BarCorLinha.info,
    corFundo: BarCorFundo.default,
},
],
  marcadores: [{
    nome: 'META',
    porcentagem: 15,
  }],
  nome: 'exemplo de barline',
  info: 'um exemplo com varias configurações do barline,
  descricao: 'descricao adicional',
  porcentagem: 10,
  mostraNome: true,
  mostraPorcentagem: true,
  porcentagemComoInfo: false
}

```

etapas:

## Inputs

| Input Name              | utilidade                     | Possiveis Valores    | Valor Inicial |
| ----------------------- | ----------------------------- | -------------------- | ------------- |
| barData **obrigatório** | objeto de configuração do bar | objeto de Ds3BarData |               |

```ts
barData: barData = {
	steps: [],
	percentage: 0,
	color: BarsColors.info,
	colorBg: BarsBgColors.default_bg
};
```

## Example

> html

```html
<ds3-bar-line [barData]="objetoDeConfiguracao"></ds3-bar-line>
```

> ts

```ts
objetoDeConfiguracao: Ds3BarData = {
	color: BarCorLinha.info,
	colorBg: BarCorFundo.default_bg,
	steps: [
		{
			name: 'Polichinelos'
		},
		{
			name: 'Abdominais'
		},
		{
			name: 'Flexão'
		}
	],
	info: 'Exercicios a serem desenvolvidos hoje',
	label: 'Exercicios',
	percentage: 11
};
```

## observações

cada bar tem um maximo de 5 steps

lembrar de importar ds3BarData, BarCorLinha e BarCorFundo para facilitar no momento da criação
