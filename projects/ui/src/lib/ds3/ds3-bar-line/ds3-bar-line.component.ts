import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
export enum BarCorLinha {
	"info" = "var(--color-feedback-info-2)",
	"gain" = "var(--color-feedback-gain-2)",
	"alert" = "var(--color-feedback-alert-2)",
	"loss" = "var(--color-feedback-loss-2)",
	"blue" = "var(--color-support-blue-4)",
	"green" = "var(--color-support-green-4)",
	"lightblue" = "var(--color-support-lightblue-4)",
	"pink" = "var(--color-support-pink-4)",
	"orange" = "var(--color-support-orange-4)",
	"purple" = "var(--color-support-purple-4)",
	"red" = "var(--color-support-red-4)",
	"yellow" = "var(--color-support-yellow-4)",
}

export enum BarCorFundo {
	"default_bg" = "var(--color-support-gray-2)",
	"info_bg" = "var(--color-support-blue-1)",
	"gain_bg" = "var(--color-support-green-1)",
	"alert_bg" = "var(--color-support-yellow-1)",
	"loss_bg" = "var(--color-support-red-1)",
	"blue_bg" = "var(--color-support-blue-1)",
	"green_bg" = "var(--color-support-green-1)",
	"lightblue_bg" = "var(--color-support-lightblue-1)",
	"orange_bg" = "var(--color-support-orange-1)",
	"pink_bg" = "var(--color-support-pink-1)",
	"purple_bg" = "var(--color-support-purple-1)",
	"red_bg" = "var(--color-support-red-1)",
	"yellow_bg" = "var(--color-support-yellow-1)",
}
export interface Ds3BarData {
	etapas: {
		nome: string;
		mostraNome?: boolean;
		corLinha?: BarCorLinha | string;
		corFundo?: BarCorFundo | string;
	}[];
	marcadores?: {
		nome?: string;
		porcentagem: number;
	}[];
	nome?: string;
	info?: string;
	descricao?: string;
	porcentagem?: number;
	mostraNome?: boolean;
	mostraPorcentagem?: boolean;
	porcentagemComoInfo?: boolean;
}

@Component({
	selector: "ds3-bar-line",
	templateUrl: "./ds3-bar-line.component.html",
	styleUrls: ["./ds3-bar-line.component.scss"],
})
export class Ds3BarLineComponent implements OnInit {
	// Output para enviar o valor do clique ao componente pai
	@Output() infoClicked: EventEmitter<string> = new EventEmitter<string>();
	@Input() textClickable: boolean = false;
	@Input() disableAnimations: boolean = false;

	@Input() barData: Ds3BarData = {
		etapas: [],
		marcadores: [],
		mostraNome: true,
	};

	prenchedor(index, etapaData): any {
		const COR_LINHA = etapaData.corLinha || BarCorLinha.info;
		const COR_FUNDO = etapaData.corFundo || BarCorFundo.default_bg;
		const PORCENTAGEM_TOTAL = this.barData.porcentagem;
		const TOTAL = this.barData.etapas.length;
		const PORCENTAGEM_CADA_PARTE = Math.floor((1 / TOTAL) * 100);
		const RESTANTE = Math.max(
			0,
			PORCENTAGEM_TOTAL - PORCENTAGEM_CADA_PARTE * index
		);
		const A_PINTAR = (RESTANTE / PORCENTAGEM_CADA_PARTE) * 100;
		if (PORCENTAGEM_TOTAL >= PORCENTAGEM_CADA_PARTE * (index + 1)) {
			return {
				"background-color": `${COR_LINHA}`,
			};
		} else if (A_PINTAR === 0) {
			return {
				"background-color": `${COR_FUNDO}`,
			};
		} else {
			return {
				"background-image": `linear-gradient(to right, ${COR_LINHA},${COR_LINHA}  ${A_PINTAR}% ,${COR_FUNDO} 0%,${COR_FUNDO} 100%)`,
			};
		}
	}

	ajustarMarcador(marcadorData) {
		const TOTAL = this.barData.etapas.length;
		// CONST WIDTHTOTAL =
		return { width: `calc(${marcadorData}%  + 12.6px)` };
	}

	ngOnInit() {
		// Limita a quantidade de steps a 5 itens
		this.barData.etapas = this.barData.etapas.slice(0, 5);
		// inicia vazio caso não exista
		this.barData.etapas = this.barData.etapas || [];
		// inicia vazio caso não exista
		this.barData.marcadores = this.barData.marcadores || [];
	}

	// Função para emitir o valor do info quando clicado
	handleInfoClick(info: string): void {
		this.infoClicked.emit(info);
	}
}
