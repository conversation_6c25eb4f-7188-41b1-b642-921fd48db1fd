@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/colors";
@import "../../../../assets/ds3/fonts/fonts";
@import "../../../../assets/ds3/typography/mixins";

:host {
	display: inline-flex;
	align-items: center;
	gap: 4px;
	width: fit-content;
	padding: 5px 16px;
	border-radius: 50px;
	@extend .pct-overline2;
	white-space: nowrap;

	&.ds3-status-gain {
		@extend .cor-feedback-gain03;
		background-color: $feedbackGain01;
	}

	&.ds3-status-loss {
		color: $feedbackLoss03;
		background-color: $feedbackLoss01;
	}

	&.ds3-status-alert {
		color: $feedbackAlert03;
		background-color: $feedbackAlert01;
	}

	&.ds3-status-info {
		color: $feedbackInfo03;
		background-color: $feedbackInfo01;
	}

	&.ds3-status-disabled {
		color: $actionDefaultDisabled02;
		background-color: $actionDefaultDisabled01;
	}

	&.ds3-status-blue {
		color: $supportBlue06;
		background-color: $supportBlue01;
	}

	&.ds3-status-green {
		color: $supportGreen06;
		background-color: $supportGreen01;
	}

	&.ds3-status-light_blue {
		color: $supportLightBlue06;
		background-color: $supportLightBlue01;
	}

	&.ds3-status-orange {
		color: $supportOrange06;
		background-color: $supportOrange01;
	}

	&.ds3-status-pink {
		color: $supportPink06;
		background-color: $supportPink01;
	}

	&.ds3-status-purple {
		color: $supportPurple06;
		background-color: $supportPurple01;
	}

	&.ds3-status-red {
		color: $supportRed06;
		background-color: $supportRed01;
	}

	&.ds3-status-yellow {
		color: $supportYellow06;
		background-color: $supportYellow01;
	}

	&.ds3-status-outlined {
		color: $typeDefaultText;
		background-color: none;
		border: 1px solid $supportGray03;
	}

	/*VISITANTE*/
	&.ds3-status-vi {
		color: $supportBlue06;
		background-color: $supportBlue01;
	}

	/*ATIVO*/
	&.ds3-status-at {
		color: $feedbackGain03;
		background-color: $feedbackGain01;
	}

	/*INATIVO - CLIENTE*/
	&.ds3-status-in {
		color: $feedbackLoss03;
		background-color: $feedbackLoss01;
	}

	/*TRANCADO*/
	&.ds3-status-tr {
		color: $actionDefaultDisabled02;
		background-color: $actionDefaultDisabled01;
	}

	/*RECEPTIVO*/
	&.ds3-status-rc {
		color: $supportBlue07;
		background-color: $supportBlue02;
	}

	/*Indicado*/
	&.ds3-status-indicado {
		color: $supportBlue04;
		background-color: $supportBlue;
	}

	/*Normal*/
	&.ds3-status-no {
		color: $supportGreen07;
		background-color: $supportGreen02;
	}

	/*Diária*/
	&.ds3-status-di {
		color: $supportGreen07;
		background-color: $supportGreen03;
	}

	/*FreePass*/
	&.ds3-status-pe {
		color: $supportGreen07;
		background-color: $supportGreen04;
	}

	/*Dependente*/
	&.ds3-status-dp {
		color: $typeBgDarkTitle;
		background-color: $supportGreen07;
	}

	/*A vencer*/
	&.ds3-status-av {
		color: $supportYellow07;
		background-color: $supportYellow02;
	}

	/*Inativo Vencido*/
	&.ds3-status-iv {
		color: $supportYellow07;
		background-color: $supportYellow03;
	}

	/*Trancado Vencido*/
	&.ds3-status-tv {
		color: $supportYellow07;
		background-color: $supportYellow04;
	}

	/*SPC*/
	&.ds3-status-spc {
		color: $typeBgDarkTitle;
		background-color: $supportBlack04;
	}

	/*Cancelado*/
	&.ds3-status-ca {
		color: $supportRed07;
		background-color: $supportRed01;
	}

	/*Desistente*/
	&.ds3-status-con-de {
		color: $supportRed07;
		background-color: $supportRed02;
	}

	/*Inativo - contrato*/
	&.ds3-status-con-in {
		color: $supportRed07;
		background-color: $supportRed03;
	}

	/*Atestado Médico*/
	&.ds3-status-atm {
		color: $supportLightBlue07;
		background-color: $supportLightBlue01;
	}

	/*Férias*/
	&.ds3-status-cr {
		color: $supportLightBlue07;
		background-color: $supportLightBlue02;
	}

	/*Atestado*/
	&.ds3-status-ae {
		color: $supportLightBlue07;
		background-color: $supportLightBlue03;
	}

	/*Gympass*/
	&.ds3-status-gy {
		color: $supportOrange01;
		background-color: $supportOrange07;
	}

	/*TotalPass*/
	&.ds3-status-tp {
		color: $supportOrange01;
		background-color: $supportOrange07;
	}
}
