import {
	Component,
	ElementRef,
	Input,
	<PERSON><PERSON>hanges,
	OnInit,
	Renderer2,
} from "@angular/core";
import { getHostElement } from "../../ds3-ui-utils";

@Component({
	selector: "ds3-status",
	templateUrl: "./ds3-status.component.html",
	styleUrls: ["./ds3-status.component.scss"],
})
export class Ds3StatusComponent implements OnInit, OnChanges {
	private hostElement;

	@Input()
	public color: string = "disabled";

	constructor(public elementRef: ElementRef, private renderer2: Renderer2) {}

	ngOnInit() {
		this.hostElement = getHostElement(this.elementRef);
		this.addMainClass();
		this.addColorClass();
	}

	ngOnChanges() {
		if (this.renderer2 && this.hostElement) {
			this.addColorClass();
		}
	}

	private addMainClass() {
		this.renderer2.addClass(this.hostElement, "ds3-status");
	}

	private addColorClass() {
		this.renderer2.addClass(this.hostElement, "ds3-status-" + this.color);
	}
}
