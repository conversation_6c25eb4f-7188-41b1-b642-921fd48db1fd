// @ts-ignore
import Notes from "./ds3-status.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { select, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";
import { Ds3StatusModule } from "./ds3-status.module";

export default {
	title: "Design System 3 | Data/Status",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, Ds3StatusModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: Notes,
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-status [color]="colors">This is the status</ds3-status>
		</div>
    `,
	props: {
		colors: select(
			"Cor: ",
			[
				"gain",
				"disabled",
				"alert",
				"loss",
				"info",
				"blue",
				"green",
				"light_blue",
				"orange",
				"pink",
				"purple",
				"red",
				"yellow",
				"outlined",
			],
			"gain"
		),
	},
});

export const icon = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-status color="gain">
				<i class="pct pct-check-square" ds3StatusIcon></i>
				This is the status
			</ds3-status>
		</div>
    `,
	props: {
		colors: select(
			"Cor: ",
			[
				"gain",
				"disabled",
				"alert",
				"loss",
				"info",
				"blue",
				"green",
				"light_blue",
				"orange",
				"pink",
				"purple",
				"red",
				"yellow",
				"outlined",
			],
			"gain"
		),
	},
});

export const all = () => ({
	template: `
		<div style="padding: 16px;">
			<ng-container *ngFor="let item of status">
				<ds3-status [color]="item.color">{{item.name}}</ds3-status>
			</ng-container>
		</div>
    `,
	props: {
		status: [
			{ name: "Gain", color: "gain" },
			{ name: "Disabled", color: "disabled" },
			{ name: "Alert", color: "alert" },
			{ name: "Loss", color: "loss" },
			{ name: "Info", color: "info" },
			{ name: "Blue", color: "blue" },
			{ name: "Green", color: "green" },
			{ name: "Light Blue", color: "light_blue" },
			{ name: "Orange", color: "orange" },
			{ name: "Pink", color: "pink" },
			{ name: "Purple", color: "purple" },
			{ name: "Red", color: "red" },
			{ name: "Yellow", color: "yellow" },
			{ name: "Outlined", color: "outlined" },
		],
	},
});
