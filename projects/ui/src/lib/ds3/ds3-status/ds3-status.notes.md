**Seletor:** `ds3-status`

**Utilização:**`<ds3-status></ds3-status>`

### Exemplo:

> meu-exemplo-de-tela.component.html

```html
<ds3-status [color]="gain">
    <i class="pct pct-check" ds3StatusIcon></i>
    Status
</ds3-status>
```
&nbsp;  

## Props / Inputs

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Input() color: string                   |'gain', 'disabled', 'alert', 'loss', 'info', 'blue', 'green', 'light_blue', 'orange', 'pink', 'purple', 'red', 'yellow','outlined'                     |'disabled'              |

## Outputs / Events

No outputs.

## More info

No more info.

&nbsp;  