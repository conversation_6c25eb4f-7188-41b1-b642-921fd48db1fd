**Seletor:** `ds3-pagination`

**Utilização:**`<ds3-pagination></ds3-pagination>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    
    totalItems: number = 30,
	pageSize: number =  5,
	currentPage: number = 1,	    
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-pagination 
    [length]="totalItems" 
    [pageSize]="pageSize" 
    [pageIndex]="currentPage"
    [showFirstLastButtons]="true" 
></ds3-pagination>
```

&nbsp;  

## Props / Inputs

| Property                                 | Values     | Default   | Description                                                                               |
|---                                       |         ---|        ---|                                                                                           |
| @Input() length: number                  |            |0          |Recebe o número total de itens disponíveis para paginação.                                 |
| @Input() pageSize: number                |            |5          |Recebe o número de itens exibidos por página.                                              |
| @Input() currentPage: number             |            |1          |Recebe o índice da página atual.                                                           |
| @Input() showFirstLastButtons: boolean   |            |true       |Variável de entrada que determina se os botões "Primeira" e "Última" devem ser habilitados.|
