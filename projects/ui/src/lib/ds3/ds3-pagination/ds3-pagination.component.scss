@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/colors.scss";

.custom-paginator {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 40px;

	.text {
		color: $typeDefaultText;
	}

	.textSpan {
		@extend .pct-body2;
	}

	.textOption {
		@extend .pct-body1;
	}

	.controls {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 180px;
		height: 38px;
		padding-left: 20px;
	}

	.material-symbols-outlined {
		display: flex;
		align-items: center;
		width: 28px;
		height: 38px;
		padding: 7px;
		font-size: 14px !important;
	}

	.numbers {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 21px;
		height: 38px;
		gap: 8px;
		@extend .pct-btn-menu2;
	}

	.number {
		color: #1e60fa;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 21px;
		height: 38px;
		cursor: pointer;

		&.active {
			font-weight: bold;
			background: hsla(222, 95%, 85%, 1);
			width: 21px;
			height: 38px;
			padding: 13px, 8px, 13px, 8px;
			border-radius: 4px;
			gap: 8px;
		}
	}

	.button {
		cursor: pointer;
		border: none;
		background-color: transparent;
		color: blue;
	}

	.button:disabled {
		cursor: default;
		color: gray;
	}

	.double-arrow-revert {
		transform: scaleX(-1);
	}

	.pageSizeOptions {
		padding: 0px 8px;
	}

	.pageIndex {
		padding: 0px 12px;

		select {
			width: 86px;
			height: 40px;
			padding: 16px;
			border-radius: 5px;
			border-color: rgb(168, 166, 166);
			padding: 4px 10px;
		}
	}

	.disabled {
		color: #ccc;
		pointer-events: none;
	}

	::ng-deep {
		.mat-form-field-appearance-outline .mat-form-field-wrapper {
			margin: 0;
		}
	}
}

::ng-deep .custom-mat-form-field {
	width: 86px !important;
	height: 40px !important;
	margin-left: 10px;

	.mat-form-field-infix {
		padding: 0px;

		.mat-select-trigger {
			display: flex;
			align-items: center;
		}

		.mat-select-value {
			max-width: initial;
		}
	}
}

::ng-deep .mat-form-field-flex {
	display: flex;
	width: 86px !important;
	height: 40px !important;
}

:ng-deep .mat-select-panel {
	width: 300px !important;
}

.mat-option {
	&:hover,
	&.mat-active {
		background: #e6edfe !important;
	}
}

.custom-mat-select-panel {
	top: 40px !important;
	left: 0 !important;
}
