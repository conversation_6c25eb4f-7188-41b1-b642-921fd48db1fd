<div class="custom-paginator">
	<span class="text">
		Mostrando {{ pageSize > length ? length : pageSize }} de {{ length }}
	</span>

	<mat-form-field
		[hidden]="true"
		appearance="outline"
		class="custom-mat-form-field">
		<mat-select
			(selectionChange)="onPageSizeChange($event)"
			[(ngModel)]="pageSize"
			class="custom-mat-select"
			disableRipple>
			<mat-option
				*ngFor="let option of pageSizeOptions; let i = index"
				[value]="option"
				class="custom-mat-option">
				{{ option }}
			</mat-option>
		</mat-select>
	</mat-form-field>
	<ds3-form-field class="custom-mat-form-field">
		<ds3-select
			(valueChanges)="onPageSizeChange($event)"
			[(ngModel)]="pageSize"
			[options]="pageSizeFormOptions"
			class="custom-mat-select"
			ds3Input></ds3-select>
	</ds3-form-field>

	<div class="controls">
		<div
			(click)="firstPage()"
			[class.disabled]="!controlsLeft"
			class="material-symbols-outlined button">
			<i class="pct pct-chevrons-left"></i>
		</div>
		<div
			(click)="previousPage()"
			[class.disabled]="!controlsLeft"
			class="material-symbols-outlined button">
			<i class="pct pct-chevron-left"></i>
		</div>

		<div
			*ngFor="let item of getDisplayedNumbers(); let i = index"
			class="numbers">
			<div
				(click)="onPageChange(item)"
				[class.active]="item === pageIndex"
				class="number">
				{{ item }}
			</div>
		</div>

		<div
			(click)="nextPage()"
			[class.disabled]="!controlsRight"
			class="material-symbols-outlined button">
			<i class="pct pct-chevron-right"></i>
		</div>
		<div
			(click)="lastPage()"
			[class.disabled]="!controlsRight"
			class="material-symbols-outlined button">
			<i class="pct pct-chevrons-right"></i>
		</div>
	</div>
</div>
