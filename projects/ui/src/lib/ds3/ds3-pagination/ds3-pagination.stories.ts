//@ts-ignore
import Notes from "./ds3-pagination.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, number, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

export default {
	title: "Design System 3 | Navigation/Pagination",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, BrowserAnimationsModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-pagination 
				[length]="totalItems" 
				[pageSize]="pageSize" 
				[pageIndex]="currentPage"
				[showFirstLastButtons]="true" 
			></ds3-pagination>
		</div>
    `,
	props: {
		totalItems: number("totalItens", 30),
		pageSize: number("pageSize", 10),
		currentPage: number("currentPage", 1),
	},
});
