import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3PaginationComponent } from "./ds3-pagination.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Ds3FormFieldModule } from "../ds3-forms/ds3-form-field/ds3-form-field.module";
import { Ds3SelectModule } from "../ds3-forms/ds3-select/ds3-select.module";
import { MatFormFieldModule, MatSelectModule } from "@angular/material";

@NgModule({
	declarations: [Ds3PaginationComponent],
	imports: [
		CommonModule,
		MatSelectModule,
		MatFormFieldModule,
		Ds3FormFieldModule,
		Ds3SelectModule,
		FormsModule,
		ReactiveFormsModule,
	],
	exports: [Ds3PaginationComponent],
})
export class Ds3PaginationModule {}
