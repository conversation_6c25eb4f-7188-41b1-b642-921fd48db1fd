@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
// @import "projects/ui/assets/import.scss";
//@import "projects/ui/assets/ui-kit.scss";

.ds3-step-header {
	display: flex;
	align-items: center;
	color: #c0c0c0;
	cursor: pointer;
	margin-top: 10px;
	margin-bottom: 10px;

	.ds3-step-indicator {
		border-radius: 50%;
		border: 1px solid $actionDefaultAble04;
		display: flex;
		width: 22px;
		height: 22px;
		font-size: 11px;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		color: $actionDefaultDisabled02;
	}

	.ds3-step-concluded {
		border-radius: 50%;
		border: 1px solid $actionDefaultAble04;
		background-color: $actionDefaultAble04;
		display: flex;
		width: 22px;
		height: 22px;
		font-size: 11px;
		color: #fff;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.done {
		font-size: 18px;
	}

	.ds3-next-icon {
		display: inline-block;
		text-align: center;
		width: 20px;
		height: 20px;
		margin-right: 5px;
		margin-left: 5px;
		margin-top: 6px;
		font-size: 16px;
		color: $typeDefaultTitle;
		line-height: 18px;
	}

	.ds3-step-label {
		margin-left: 9px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		flex: 1;
		padding-top: 6px;
		display: flex;

		.ds3-step-label-text {
			text-overflow: ellipsis;
			overflow: hidden;
			color: $actionDefaultAble04;
			@extend .pct-btn-default2;
		}
	}

	&.step-done {
		color: #2217c3;

		.ds3-step-indicator {
			border-color: $actionDefaultAble04;
		}

		.ds3-next-icon {
			color: $typeDefaultTitle;
		}
	}

	&.step-current {
		color: #000;

		.ds3-step-indicator {
			border-color: rgb(18, 18, 207);
		}

		.next-icon {
			color: $typeDefaultTitle;
		}
	}
}

.ds3-step-box {
	display: flex;
	padding: 4px 8px;
	border-radius: 4px;

	&:hover {
		background-color: $actionDefaultAble01;
	}
}

.ds3-step-concluded-disable {
	background-color: transparent;
	color: $typeDefaultText;
	cursor: default;

	.ds3-step-box {
		&:hover {
			background-color: transparent;
		}
	}

	.ds3-step-concluded {
		border-color: $typeDefaultText;
		background-color: $typeDefaultText;
		color: $branco;
	}

	.ds3-step-label {
		.ds3-step-label-text {
			color: $typeDefaultText;
		}
	}
}

.ds3-step-box {
	display: flex;
	padding: 4px 8px;
	border-radius: 4px;

	&:hover {
		background-color: $actionDefaultAble01;
	}
}

.ds3-step {
	display: flex;

	&:last-of-type {
		.ds3-next-icon {
			display: none !important;
		}
	}
}

.ds3-step-disabled-next {
	background-color: transparent;
	color: $typeDefaultText;
	cursor: default;

	.ds3-step-box {
		&:hover {
			background-color: transparent;
		}

		.ds3-step-indicator {
			border-color: $typeDefaultText;
		}
	}

	.ds3-step-concluded {
		border-color: $typeDefaultText;
		background-color: transparent;
		color: $typeDefaultText;
	}

	.ds3-step-label {
		.ds3-step-label-text {
			color: $typeDefaultText;
		}
	}
}
