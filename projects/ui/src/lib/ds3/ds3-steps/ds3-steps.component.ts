import { Directionality } from "@angular/cdk/bidi";
import { CdkStepper } from "@angular/cdk/stepper";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";

@Component({
	selector: "ds3-steps",
	templateUrl: "./ds3-steps.component.html",
	styleUrls: ["./ds3-steps.component.scss"],
	providers: [
		{
			provide: CdkStepper,
			useValue: { displayDefaultIndicatorType: true },
			useExisting: Ds3StepsComponent,
		},
	],
})
export class Ds3StepsComponent extends CdkStepper implements OnInit {
	constructor(dir: Directionality, changeDetectorRef: ChangeDetectorRef) {
		super(dir, changeDetectorRef);
	}

	ngOnInit() {}

	onClick(toGoIndex: number): void {
		// todos ifs são para não executar caso
		// seja linear e a etapa atual não esteja completa ou seja duas etapas após a selecionada
		if (
			this.linear &&
			(this.selected.completed || this.selected.optional) &&
			this.selectedIndex + 1 > toGoIndex
		) {
			return;
		}

		this.selectedIndex = toGoIndex;
	}
}
