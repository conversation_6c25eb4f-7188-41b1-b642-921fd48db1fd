**Seletor:** `ds3-steps`,`cdk-step`

**Utilização:**`<ds3-steps><cdk-step></cdk-step></ds3-steps>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component, OnInit } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    firstFormGroup: FormGroup;
	secondFormGroup: FormGroup;
	editable = true;
	isFormDadosValid: boolean = false;

	constructor(private cd: ChangeDetectorRef, private fb: FormBuilder) {}

	ngOnInit() {

		this.firstFormGroup = this.fb.group({
			firstCtrl: ['']
		});
		this.secondFormGroup = this.fb.group({
			secondCtrl: ['']
		});
		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

	verificaCamposValidos() {

		if ((this.isFormDadosValid = this.firstFormGroup.status == 'VALID')) {
			return true;
		} else {
			return false;
		}
	}

	ngAfterContentChecked() {
		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-steps linear>
		<cdk-step
			label="Text"
			[stepControl]="firstFormGroup"
			editable="false"

		
		>
			<form [formGroup]="firstFormGroup">
				<div class="row">
					<div class="col-md-4 col-sm-6">
					
					
							<label>
								<span class="pacto-label"
									>Título do template*</span
								>
							</label>
					
						
						<input type="text" ds3Input formControlName="firstCtrl" required />
		
						
						<button pct-text-button color="primary" size="lg" [disabled]="!isFormDadosValid"
						class="example-nav-button"

				
						cdkStepperNext>Avançar</button>
					
					</div>
				</div>
			</form>
		</cdk-step>
		<cdk-step
			label="Text"
			[stepControl]="secondFormGroup"
			[editable]="!secondFormGroup.get('secondCtrl').valid"
		>
			<form [formGroup]="secondFormGroup">
				<div class="row">
					<div class="col-md-4 col-sm-6">
						<pacto-cat-form-input
							[control]="secondFormGroup.get('secondCtrl')"
							[placeholder]="'Texto do campo 2'"
						>
							<label>
								<span class="pacto-label"
									>Título do template 2*</span
								>
							</label>
						</pacto-cat-form-input>
						
						<button pct-text-button color="primary" size="lg" [disabled]="!isFormDadosValid"
			

						
						cdkStepperNext>Avançar</button>
					</div>
				</div>
			</form>
		</cdk-step>
		<cdk-step label="Text" editable="true">aoba</cdk-step>
		<cdk-step label="Text" editable="true">aoba</cdk-step>
	</ds3-steps>
```
&nbsp;  

## Props / Inputs

| Propiedade                               | Valores             | Valor Default        | Descrição          | 
|---                                       |                  ---|                   ---|                 ---|
| <ds3-steps> linear: Boolean                |  False ou True      |Vazio                 | Define se a validade das etapas anteriores deve ser verificada ou não.|
| stepControl: control                |  FormGroup             |FormGroup                 | Controle do passo que atrela ao controle do form group para validação|
| editable: boolean or function               |  True or False or Function            |True                 | Define se o passo será editável quando estiver no próximo passo|
| cdkStepperNext: boolean               |  True or False       |True                 | Define a função para o pŕoximo passo caso habilitado, é uma propiedade para adicionar dentro do controle do step |
| cdkStepperPrevious: boolean               |  True or False       |True                 | Define a função para o passo anterior caso habilitado, é uma propiedade para adicionar dentro do controle do step |

## More info

- A tag `<ds3-steps linear>`é usada para envolver as tags que são os Passos que possui seletor `<cdk-step>`.
- O valor de editable, pode ser uma função de validação. Exemplo: `[editable]=!secondFormGroup.get('secondCtrl').valid`
- Estilos aplicados no Step, para cada função:
	- `Step Default:` Quando está ativo e os próximos passos ativos
	- `Step Conclued:` Quando o passo está concluido, mas pode ser editado.
	- `Step Conclued Disabled:` Quando o passo está conclúido, porem desativado para edição
	- `Step Disabled:` Passo desabilitado temporiariamente , no modo linear. Quando os próximos passos passos estão desabilitados até a validação dos atuais.

&nbsp;  
