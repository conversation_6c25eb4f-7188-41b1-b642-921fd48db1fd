<section>
	<header class="ds3-step-header">
		<div
			(click)="onClick(i)"
			*ngFor="let step of steps; let i = index"
			[ngClass]="{
				'step-active': selectedIndex === i,
				'ds3-step-concluded-disable':
					!step._editable && step.completed && selectedIndex != i,
				'ds3-step-disabled-next': i > selectedIndex + 1 && linear
			}"
			class="ds3-step">
			<div class="ds3-step-box">
				<div
					*ngIf="step.completed"
					class="ds3-step-indicator ds3-step-concluded pct pct-check"></div>
				<div *ngIf="!step.completed" class="ds3-step-indicator">
					{{ i + 1 }}
				</div>
				<div class="ds3-step-label">
					<div class="ds3-step-label-text">
						{{ step.label }}
					</div>
				</div>
			</div>
			<i class="pct pct-arrow-right ds3-next-icon"></i>
		</div>
	</header>

	<div [style.display]="selected ? 'block' : 'none'">
		<ng-container [ngTemplateOutlet]="selected.content"></ng-container>
	</div>

	<!-- <footer class="example-step-navigation-bar">
		<button class="example-nav-button" cdkStepperPrevious>&larr;</button>
		<button class="example-nav-button" cdkStepperNext>&rarr;</button>
	</footer> -->
</section>
