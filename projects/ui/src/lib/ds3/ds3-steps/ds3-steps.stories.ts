import { boolean, text, withKnobs } from "@storybook/addon-knobs";
import { moduleMetadata, storiesOf } from "@storybook/angular";

import { CdkStepperModule } from "@angular/cdk/stepper";
import { Ds3Module } from "../ds3.module";
// @ts-ignore
import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import {
	FormBuilder,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";
import { Ds3FormFieldModule } from "../ds3-forms/ds3-form-field/ds3-form-field.module";
import Notes from "./ds3-steps.notes.md";

const description = () => Notes;

export default {
	title: "Design System 3 | Navigation/Steps",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		docs: {
			extractComponentDescription: description,
		},
	},
};

@Component({
	template: `
		<ds3-steps linear="{{ linear }}">
			<cdk-step
				label="{{ titulo1 }}"
				[stepControl]="firstFormGroup"
				editable="{{ editable1 }}">
				<form [formGroup]="firstFormGroup">
					<div class="row">
						<div class="col-md-4 col-sm-6">
							<label>
								<span class="pacto-label">Título do template</span>
							</label>

							<input
								type="text"
								ds3Input
								formControlName="firstCtrl"
								required />

							<button
								ds3-text-button
								color="primary"
								size="lg"
								[disabled]="!isFormDadosValid"
								class="example-nav-button"
								cdkStepperNext>
								Avançar
							</button>
						</div>
					</div>
				</form>
			</cdk-step>
			<cdk-step
				label="{{ titulo2 }}"
				[stepControl]="secondFormGroup"
				[editable]="!secondFormGroup.get('secondCtrl').valid">
				<form [formGroup]="secondFormGroup">
					<div class="row">
						<div class="col-md-4 col-sm-6">
							<label>
								<span class="pacto-label">Título do template 2</span>
							</label>

							<input type="text" ds3Input formControlName="secondCtrl" />

							<button
								pct-text-button
								color="primary"
								size="lg"
								[disabled]="!isFormDadosValid"
								cdkStepperNext>
								Avançar
							</button>
						</div>
					</div>
				</form>
			</cdk-step>
			<cdk-step label="Text" editable="true">Texto qualquer</cdk-step>
			<cdk-step label="Text" editable="true">Texto qualquer</cdk-step>
		</ds3-steps>
	`,
})
class HostComponent implements OnInit {
	@Input() linear;
	@Input() titulo1;
	@Input() editable1;
	@Input() titulo2;
	@Input() editable2;
	firstFormGroup: FormGroup;
	secondFormGroup: FormGroup;

	constructor(private cd: ChangeDetectorRef, private fb: FormBuilder) {}

	isFormDadosValid: boolean = false;

	ngOnInit() {
		this.firstFormGroup = this.fb.group({
			firstCtrl: [""],
		});
		this.secondFormGroup = this.fb.group({
			secondCtrl: [""],
		});

		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

	ngAfterContentChecked() {
		this.cd.detectChanges();
		this.verificaCamposValidos();
	}

	verificaCamposValidos() {
		if ((this.isFormDadosValid = this.firstFormGroup.status == "VALID")) {
			return true;
		} else {
			return false;
		}
	}
}

storiesOf("Design System 3 | Navigation/Steps", module)
	.addDecorator(
		moduleMetadata({
			imports: [
				Ds3Module,
				CdkStepperModule,
				ReactiveFormsModule,
				FormsModule,
				Ds3FormFieldModule,
			],
		})
	)
	.addDecorator(withKnobs)
	.addParameters({
		notes: { Notes },
	})
	.add("interactive", () => {
		return {
			component: HostComponent,
			props: {
				linear: boolean("É linear", true),
				titulo1: text("Título 1", "Texto passo1"),
				editable1: boolean("É editavel passo 1", false),
				titulo2: text("Título 2", "Texto passo2"),
				editable2: boolean("É editavel passo 2", true),
			},
		};
	});
