export const borderColors = [
	"",
	"border-support-red",
	"border-support-red01",
	"border-support-red02",
	"border-support-red03",
	"border-support-red04",
	"border-support-red05",
	"border-support-red06",
	"border-support-red07",
	"border-support-pink",
	"border-support-pink01",
	"border-support-pink02",
	"border-support-pink03",
	"border-support-pink04",
	"border-support-pink05",
	"border-support-pink06",
	"border-support-pink07",
	"border-support-purple",
	"border-support-purple01",
	"border-support-purple02",
	"border-support-purple03",
	"border-support-purple04",
	"border-support-purple05",
	"border-support-purple06",
	"border-support-purple07",
	"border-support-blue",
	"border-support-blue01",
	"border-support-blue02",
	"border-support-blue03",
	"border-support-blue04",
	"border-support-blue05",
	"border-support-blue06",
	"border-support-blue07",
	"border-support-light-blue",
	"border-support-light-blue01",
	"border-support-light-blue02",
	"border-support-light-blue03",
	"border-support-light-blue04",
	"border-support-light-blue05",
	"border-support-light-blue06",
	"border-support-light-blue07",
	"border-support-green",
	"border-support-green01",
	"border-support-green02",
	"border-support-green03",
	"border-support-green04",
	"border-support-green05",
	"border-support-green06",
	"border-support-green07",
	"border-support-yellow",
	"border-support-yellow01",
	"border-support-yellow02",
	"border-support-yellow03",
	"border-support-yellow04",
	"border-support-yellow05",
	"border-support-yellow06",
	"border-support-yellow07",
	"border-support-orange",
	"border-support-orange01",
	"border-support-orange02",
	"border-support-orange03",
	"border-support-orange04",
	"border-support-orange05",
	"border-support-orange06",
	"border-support-orange07",
	"border-support-black",
	"border-support-black01",
	"border-support-black02",
	"border-support-black03",
	"border-support-black04",
	"border-support-black05",
	"border-support-black06",
	"border-support-black07",
	"border-support-gray",
	"border-support-gray01",
	"border-support-gray02",
	"border-support-gray03",
	"border-support-gray04",
	"border-support-gray05",
	"border-support-gray06",
	"border-support-gray07",
	"border-menu01",
	"border-menu02",
	"border-menu03",
	"border-menu04",
	"border-plane01",
	"border-plane02",
	"border-plane03",
	"border-plane04",
	"border-feedback-info01",
	"border-feedback-info02",
	"border-feedback-info03",
	"border-feedback-gain01",
	"border-feedback-gain02",
	"border-feedback-gain03",
	"border-feedback-alert01",
	"border-feedback-alert02",
	"border-feedback-alert03",
	"border-feedback-loss01",
	"border-feedback-loss02",
	"border-feedback-loss03",
	"border-action-default-able01",
	"border-action-default-able02",
	"border-action-default-able03",
	"border-action-default-able04",
	"border-action-default-able05",
	"border-action-default-risk01",
	"border-action-default-risk02",
	"border-action-default-risk03",
	"border-action-default-risk04",
	"border-action-default-risk05",
	"border-action-default-disabled01",
	"border-action-default-disabled02",
	"border-action-bg-dark-able01",
	"border-action-bg-dark-able02",
	"border-action-bg-dark-able03",
	"border-action-bg-dark-disabled01",
	"border-type-default-title",
	"border-type-default-text",
	"border-type-bg-dark-title",
	"border-type-bg-dark-text",
	"border-azul-pacto",
];

export const colorsByGroups = [
	{
		groupName: "Support",
		subgroups: [
			{
				groupName: "Red",
				colors: [
					"support-red",
					"support-red01",
					"support-red02",
					"support-red03",
					"support-red04",
					"support-red05",
					"support-red06",
					"support-red07",
				],
			},
			{
				groupName: "Pink",
				colors: [
					"support-pink",
					"support-pink01",
					"support-pink02",
					"support-pink03",
					"support-pink04",
					"support-pink05",
					"support-pink06",
					"support-pink07",
				],
			},
			{
				groupName: "Purple",
				colors: [
					"support-purple",
					"support-purple01",
					"support-purple02",
					"support-purple03",
					"support-purple04",
					"support-purple05",
					"support-purple06",
					"support-purple07",
				],
			},
			{
				groupName: "Blue",
				colors: [
					"support-blue",
					"support-blue01",
					"support-blue02",
					"support-blue03",
					"support-blue04",
					"support-blue05",
					"support-blue06",
					"support-blue07",
				],
			},
			{
				groupName: "Light blue",
				colors: [
					"support-light-blue",
					"support-light-blue01",
					"support-light-blue02",
					"support-light-blue03",
					"support-light-blue04",
					"support-light-blue05",
					"support-light-blue06",
					"support-light-blue07",
				],
			},
			{
				groupName: "Green",
				colors: [
					"support-green",
					"support-green01",
					"support-green02",
					"support-green03",
					"support-green04",
					"support-green05",
					"support-green06",
					"support-green07",
				],
			},
			{
				groupName: "Yellow",
				colors: [
					"support-yellow",
					"support-yellow01",
					"support-yellow02",
					"support-yellow03",
					"support-yellow04",
					"support-yellow05",
					"support-yellow06",
					"support-yellow07",
				],
			},
			{
				groupName: "Orange",
				colors: [
					"support-orange",
					"support-orange01",
					"support-orange02",
					"support-orange03",
					"support-orange04",
					"support-orange05",
					"support-orange06",
					"support-orange07",
				],
			},
			{
				groupName: "Black",
				colors: [
					"support-black",
					"support-black01",
					"support-black02",
					"support-black03",
					"support-black04",
					"support-black05",
					"support-black06",
					"support-black07",
				],
			},
			{
				groupName: "Gray",
				colors: [
					"support-gray",
					"support-gray01",
					"support-gray02",
					"support-gray03",
					"support-gray04",
					"support-gray05",
					"support-gray06",
					"support-gray07",
				],
			},
		],
	},
	{
		groupName: "Menu",
		colors: ["menu01", "menu02", "menu03", "menu04"],
	},
	{
		groupName: "Plane",
		colors: ["plane01", "plane02", "plane03", "plane04"],
	},
	{
		groupName: "Feedback",
		subgroups: [
			{
				groupName: "Info",
				colors: ["feedback-info01", "feedback-info02", "feedback-info03"],
			},
			{
				groupName: "Gain",
				colors: ["feedback-gain01", "feedback-gain02", "feedback-gain03"],
			},
			{
				groupName: "Alert",
				colors: ["feedback-alert01", "feedback-alert02", "feedback-alert03"],
			},
			{
				groupName: "Loss",
				colors: ["feedback-loss01", "feedback-loss02", "feedback-loss03"],
			},
		],
	},
	{
		groupName: "Action",
		subgroups: [
			{
				groupName: "Default able",
				colors: [
					"action-default-able01",
					"action-default-able02",
					"action-default-able03",
					"action-default-able04",
					"action-default-able05",
				],
			},
			{
				groupName: "Default risk",
				colors: [
					"action-default-risk01",
					"action-default-risk02",
					"action-default-risk03",
					"action-default-risk04",
					"action-default-risk05",
				],
			},
			{
				groupName: "Default disabled",
				colors: ["action-default-disabled01", "action-default-disabled02"],
			},
			{
				groupName: "Dark able",
				colors: [
					"action-bg-dark-able01",
					"action-bg-dark-able02",
					"action-bg-dark-able03",
				],
			},
			{
				groupName: "Dark disabled",
				colors: ["action-bg-dark-disabled01"],
			},
		],
	},
	{
		groupName: "Type",
		colors: [
			"type-default-title",
			"type-default-text",
			"type-bg-dark-title",
			"type-bg-dark-text",
		],
	},
	{
		groupName: "Ungrouped",
		colors: ["azul-pacto"],
	},
];

export const iconsName = [
	"",
	"add-image",
	"add-shopping-cart",
	"classroom",
	"diamond",
	"heartbeat",
	"hourglass",
	"knowlodge",
	"megaphone",
	"muscle",
	"new-brand-1",
	"new-brand-2",
	"reversal",
	"rocket",
	"shield-health",
	"star-filled",
	"user-fav",
	"add-notification",
	"calculator",
	"grocery",
	"market",
	"minimize-3",
	"qr-code",
	"recipe",
	"spivi",
	"spivi-not",
	"stop-recording",
	"telemarketing",
	"time-cap",
	"walk",
	"wellness",
	"afternoon",
	"biset",
	"circle-filled",
	"circularcontroller",
	"footprint",
	"gdrive",
	"google",
	"gsheets",
	"height",
	"ingredients",
	"insight",
	"logo-pacto",
	"morning",
	"move-2",
	"nutrition",
	"pin",
	"play-filled",
	"stack",
	"tray",
	"triset",
	"trophy",
	"trophy-filled",
	"unpin",
	"viewed",
	"weight",
	"whatsapp",
	"zoom-out",
	"zoom-in",
	"zap-off",
	"zap",
	"youtube",
	"x-square",
	"x-octagon",
	"x-circle",
	"x",
	"wod",
	"wind",
	"wifi-off",
	"wifi",
	"watch",
	"volume-x",
	"volume-2",
	"volume-1",
	"volume",
	"voicemail",
	"video-off",
	"video",
	"user-x",
	"users",
	"user-plus",
	"user-minus",
	"user-check",
	"user",
	"upload-cloud",
	"upload",
	"unlock",
	"underline",
	"umbrella",
	"type",
	"twitter",
	"tv",
	"truck",
	"triangle",
	"trending-up",
	"trending-down",
	"trello",
	"treino",
	"trash-2",
	"trash",
	"toggle-right",
	"toggle-left",
	"thumbs-up",
	"thumbs-down",
	"thermometer",
	"terminal",
	"target",
	"tag",
	"tablet",
	"sunset",
	"sunrise",
	"sun",
	"stop-circle",
	"star",
	"square",
	"speaker",
	"smile",
	"smartphone",
	"sliders",
	"slash",
	"slack",
	"skip-forward",
	"skip-back",
	"skill",
	"sidebar",
	"shuffle",
	"shopping-cart",
	"shopping-bag",
	"shield-off",
	"shield",
	"share-2",
	"share",
	"settings",
	"server",
	"send",
	"search",
	"scissors",
	"save",
	"rss",
	"rotate-cw",
	"rotate-ccw",
	"rewind",
	"repeat",
	"refresh-cw",
	"refresh-ccw",
	"radio",
	"printer",
	"power",
	"pocket",
	"plus-square",
	"plus-circle",
	"plus",
	"play-circle",
	"play-1",
	"play",
	"pie-chart",
	"phone-outgoing",
	"phone-off",
	"phone-incoming",
	"phone-forwarded",
	"phone-call",
	"phone",
	"percent",
	"pen-tool",
	"pause-circle",
	"pause",
	"paperclip",
	"package",
	"octagon",
	"navigation-2",
	"navigation",
	"music",
	"move",
	"mouse-pointer",
	"more-vertical",
	"more-horizontal",
	"moon",
	"monitor",
	"modality",
	"minus-square",
	"minus-circle",
	"minus",
	"minimize-2",
	"minimize",
	"mic-off",
	"mic",
	"message-square",
	"message-circle",
	"menu",
	"meh",
	"maximize-2",
	"maximize",
	"map-pin",
	"map",
	"mail",
	"log-out",
	"log-in",
	"lock",
	"loader",
	"list",
	"linkedin",
	"link-2",
	"link",
	"life-buoy",
	"level",
	"layout",
	"layers",
	"keyboard",
	"key",
	"italic",
	"instagram",
	"info",
	"inbox",
	"image",
	"home",
	"hexagon",
	"help-circle",
	"heart",
	"headphones",
	"hash",
	"hard-drive",
	"happy-03",
	"happy-02",
	"happy-01",
	"grid",
	"globe",
	"git-pull-request",
	"git-merge",
	"gitlab",
	"github",
	"git-commit",
	"git-branch",
	"gift",
	"frown",
	"folder-plus",
	"folder-minus",
	"folder",
	"flag",
	"filter",
	"film",
	"file-text",
	"file-plus",
	"file-minus",
	"file",
	"figma",
	"feather",
	"fast-forward",
	"facebook",
	"eye-off",
	"eye",
	"external-link",
	"evaluation",
	"edit-3",
	"edit-2",
	"edit",
	"droplet",
	"drop-down",
	"drag",
	"download-cloud",
	"download",
	"dollar-sign",
	"disc",
	"digital",
	"delete",
	"database",
	"crosshair",
	"cross",
	"crop",
	"credit-card",
	"cpu",
	"corner-up-right",
	"corner-up-left",
	"corner-right-up",
	"corner-right-down",
	"corner-left-up",
	"corner-left-down",
	"corner-down-right",
	"corner-down-left",
	"copy",
	"compass",
	"community",
	"command",
	"columns",
	"coffee",
	"codesandbox",
	"codepen",
	"code",
	"cloud-snow",
	"cloud-rain",
	"cloud-off",
	"cloud-lightning",
	"cloud-drizzle",
	"cloud",
	"clock",
	"clipboard",
	"circle",
	"chrome",
	"chevron-up",
	"chevrons-up",
	"chevrons-right",
	"chevrons-left",
	"chevrons-down",
	"chevron-right",
	"chevron-left",
	"chevron-down",
	"check-square",
	"check-circle",
	"check",
	"cast",
	"caret-up",
	"caret-right",
	"caret-left",
	"caret-down",
	"camera-off",
	"camera",
	"calendar",
	"briefcase",
	"box",
	"book-open",
	"bookmark",
	"book",
	"bold",
	"bluetooth",
	"bell-off",
	"bell",
	"battery-charging",
	"battery",
	"bar-chart-2",
	"bar-chart",
	"award",
	"at-sign",
	"arrow-up-right",
	"arrow-up-left",
	"arrow-up-circle",
	"arrow-up",
	"arrow-right-circle",
	"arrow-right",
	"arrow-left-circle",
	"arrow-left",
	"arrow-down-right",
	"arrow-down-left",
	"arrow-down-circle",
	"arrow-down",
	"archive",
	"aperture",
	"anchor",
	"align-right",
	"align-left",
	"align-justify",
	"align-center",
	"alert-triangle",
	"alert-octagon",
	"alert-circle",
	"airplay",
	"activity",
];
