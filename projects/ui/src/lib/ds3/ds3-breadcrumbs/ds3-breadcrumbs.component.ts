import {
	Component,
	ElementRef,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { distinctUntilChanged } from "rxjs/operators";

@Component({
	selector: "ds3-breadcrumbs",
	templateUrl: "./ds3-breadcrumbs.component.html",
	styleUrls: ["./ds3-breadcrumbs.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3BreadcrumbsComponent implements OnInit {
	@Input() routeData?: Array<ds3RouteData> = new Array();
	@Input() displayArrowReturn: boolean = true;
	@Input() displayArrowSection: boolean = true;
	isExpanderShow;
	totalItens;
	@ViewChild("dropdownRef", { static: true }) dropdownRef: TemplateRef<any>;

	constructor(
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private dialog: MatDialog
	) {}

	ngOnInit() {
		this.totalItens = this.routeData.length;
	}

	get lastPage() {
		return this.routeData[this.routeData.length - 2];
	}

	get actualPage() {
		return this.routeData[this.routeData.length - 1];
	}

	showDropdown(event: PointerEvent): void {
		console.log(event);

		const dialogRef = this.dialog.open(this.dropdownRef, {
			closeOnNavigation: true,
			backdropClass: "bg-fundo",
			id: "date-ranger-dialog",
			position: { top: `${event.y}px`, left: `${event.x}px` },
		});
	}
}

export interface ds3RouteData {
	title: string;
	path: Array<any>;
	isDisabled?: boolean;
}
