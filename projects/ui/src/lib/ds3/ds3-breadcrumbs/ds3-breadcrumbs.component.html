<div class="breadcrumbs">
	<div class="return-icon" *ngIf="displayArrowSection">
		<a
			*ngIf="routeData.length > 1 && displayArrowReturn"
			[routerLink]="lastPage.path">
			<i class="pct pct-arrow-left"></i>
		</a>
	</div>
	<div [class.solo]="routeData.length === 1" class="crumbs">
		<div class="path">
			<div
				*ngFor="let route of routeData; let i = index"
				[class.disabled]="route.isDisabled"
				class="item">
				<!-- apresentar link: nunca apresentar ultimo e caso tenha mais de 3 apresentar apenas quando for o primeiro ou  antepenultimo ou penultimo -->
				<a
					*ngIf="
						i != totalItens - 1 &&
						(totalItens > 3
							? i === 0 || i === totalItens - 3 || i === totalItens - 2
							: true)
					"
					[routerLink]="route.path">
					{{ route.title }}
				</a>
				<!-- apresentar '...': apenas quando tiver mais de 3 itens e apenas uma vez -->
				<div
					(click)="showDropdown($event)"
					*ngIf="totalItens > 4 && i === 1"
					class="expansioner">
					...
				</div>
				<!-- apresentar >: nunca apresentar no ultimo e quando tiver mais de 3 itens apresentar apenas pros 4 itens ( primeiro>...>antepenultima>penultima ) -->
				<i
					*ngIf="
						totalItens > 3
							? i === 0 || i === totalItens - 4 || i === totalItens - 3
							: i < totalItens - 2
					"
					class="pct pct-chevron-right"></i>
			</div>
		</div>
		<div class="actual">{{ actualPage.title }}</div>
	</div>
</div>

<ng-template #dropdownRef>
	<div *ngFor="let route of routeData; let i = index" class="dropwdown">
		<button
			*ngIf="
				i != totalItens - 1 &&
				!(totalItens > 3
					? i === 0 || i === totalItens - 3 || i === totalItens - 2
					: true)
			"
			[class.disabled]="route.isDisabled"
			[routerLink]="route.path"
			ds3-text-button>
			{{ route.title }}
		</button>
	</div>
</ng-template>
