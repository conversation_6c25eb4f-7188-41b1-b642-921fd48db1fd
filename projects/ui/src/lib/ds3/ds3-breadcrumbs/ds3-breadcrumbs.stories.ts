//@ts-ignore
import Notes from "./ds3-breadcrumbs.md";
import { moduleMetadata } from "@storybook/angular";
import { select, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";
import { Template } from "@angular/compiler/src/render3/r3_ast";
import { Ds3BreadcrumbsModule } from "./ds3-breadcrumbs.module";
import { RouterTestingModule } from "@angular/router/testing";
import { MatDialogModule } from "@angular/material";
import { NoopAnimationsModule } from "@angular/platform-browser/animations";

const description = () => Notes;

export default {
	title: "Design System 3 | Navigation/Breadcrumbs",
	parameters: {
		notes: { Notes },
	},
	decorators: [
		moduleMetadata({
			imports: [
				Ds3BreadcrumbsModule,
				RouterTestingModule,
				MatDialogModule,
				NoopAnimationsModule,
			],
		}),
		withKnobs,
	],
};

export const Breadcrumbs = () => ({
	template: `
    <hr>
    <p>exemplo breadcrumbs com 1 item</p>
    <ds3-breadcrumbs [routeData]="routeData1"></ds3-breadcrumbs>
    <hr>
    <p>exemplo breadcrumbs com 2 itens</p>
    <ds3-breadcrumbs [routeData]="routeData2"></ds3-breadcrumbs>
    <hr>
    <p>exemplo breadcrumbs com 3 itens</p>
    <ds3-breadcrumbs [routeData]="routeData3"></ds3-breadcrumbs>
    <hr>
    <p>exemplo breadcrumbs com 4 itens</p>
    <ds3-breadcrumbs [routeData]="routeData4"></ds3-breadcrumbs>
    <hr>
    <p>exemplo breadcrumbs com 5 itens</p>
    <ds3-breadcrumbs [routeData]="routeData5"></ds3-breadcrumbs>
    <hr>
    <p>exemplo breadcrumbs com 6 itens</p>
    <ds3-breadcrumbs [routeData]="routeData6"></ds3-breadcrumbs>
    <hr>
    <br>
    `,
	props: {
		routeData1: [{ path: ["/mock-story", "teste1"], title: "teste1" }],
		routeData2: [
			{ path: ["/mock-story", "teste1"], title: "teste1" },
			{ path: ["/mock-story", "teste1", "teste2"], title: "teste2" },
		],
		routeData3: [
			{ path: ["/mock-story", "teste1"], title: "teste1" },
			{ path: ["/mock-story", "teste1", "teste2"], title: "teste2" },
			{ path: ["/mock-story", "teste1", "teste2", "teste3"], title: "teste3" },
		],
		routeData4: [
			{ path: ["/mock-story", "teste1"], title: "teste1" },
			{ path: ["/mock-story", "teste1", "teste2"], title: "teste2" },
			{ path: ["/mock-story", "teste1", "teste2", "teste3"], title: "teste3" },
			{
				path: ["/mock-story", "teste1", "teste2", "teste3", "teste4"],
				title: "teste4",
			},
		],
		routeData5: [
			{ path: ["/mock-story", "teste1"], title: "teste1" },
			{ path: ["/mock-story", "teste1", "teste2"], title: "teste2" },
			{ path: ["/mock-story", "teste1", "teste2", "teste3"], title: "teste3" },
			{
				path: ["/mock-story", "teste1", "teste2", "teste3", "teste4"],
				title: "teste4",
			},
			{
				path: ["/mock-story", "teste1", "teste2", "teste3", "teste4", "teste5"],
				title: "teste5",
			},
		],
		routeData6: [
			{ path: ["/mock-story", "teste1"], title: "teste1" },
			{ path: ["/mock-story", "teste1", "teste2"], title: "teste2" },
			{ path: ["/mock-story", "teste1", "teste2", "teste3"], title: "teste3" },
			{
				path: ["/mock-story", "teste1", "teste2", "teste3", "teste4"],
				title: "teste4",
			},
			{
				path: ["/mock-story", "teste1", "teste2", "teste3", "teste4", "teste5"],
				title: "teste5",
			},
			{
				path: [
					"/mock-story",
					"teste1",
					"teste2",
					"teste3",
					"teste4",
					"teste5",
					"teste6",
				],
				title: "teste6",
			},
		],
	},
});
