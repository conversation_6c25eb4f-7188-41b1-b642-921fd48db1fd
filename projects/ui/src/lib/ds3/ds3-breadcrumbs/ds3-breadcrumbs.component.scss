@import "../../../../assets/ui-kit.scss";

.breadcrumbs {
	display: flex;
	align-items: center;
	flex-direction: row;

	.return-icon {
		margin: 30px 12px 0 4px;
		height: 32px;
		width: 32px;

		a {
			width: 100%;

			&:hover {
				cursor: pointer;
				outline: none !important;
				text-decoration: none !important;
			}

			i {
				font-size: 32px;
				color: var(--color-typography-default-title);
			}
		}
	}

	.crumbs {
		display: flex;
		align-items: center;
		flex-direction: column;

		&.solo {
			padding-left: calc(12px + 32px + 3px);
		}

		.path {
			display: flex;
			flex-direction: row;
			align-items: center;
			width: 100%;
			justify-content: flex-start;
			height: 30px;

			.item {
				height: 30px;
				display: flex;
				align-items: center;

				&.disabled {
					a {
						pointer-events: none;
						cursor: default;

						&:hover {
							cursor: default;
						}
					}

					a,
					i {
						color: var(--color-action-default-disable-2);
					}
				}

				a {
					@extend .pct-btn-menu1;
					color: var(--color-action-default-able-4);
					display: flex;
					justify-content: center;
					align-items: center;
					height: 30px;

					&:hover {
						cursor: pointer;
						outline: none !important;
						text-decoration: none !important;
					}
				}

				.expansioner {
					min-width: 30px;
					min-height: 20px;
					text-align: center;
					@extend .pct-btn-menu1;

					&:hover {
						cursor: pointer;
						outline: none !important;
						text-decoration: none !important;
					}
				}

				.pct-chevron-right {
					width: 30px;
					display: flex;
					justify-content: center;
					align-items: center;
					height: 30px;
				}
			}
		}

		.actual {
			@extend .pct-title1;
			display: flex;
			width: 100%;
			justify-content: flex-start;
			color: var(--color-typography-default-title);
		}
	}
}

.mat-dialog-container {
	padding: 0px !important;
}

.dropwdown > button[ds3-text-button] {
	height: 20px;
	width: 175px;
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-content: flex-start;
	@extend .pct-display6;
	background-color: var(--color-background-plane-2) !important;
	color: var(--color-support-black-3) !important;
	outline: none !important;
	padding: 16px !important;

	&:hover {
		background-color: var(--color-action-default-able-1) !important;
		color: var(--color-action-default-able-4) !important;
		outline: none !important;
		text-decoration: none !important;
	}

	&.disabled {
		pointer-events: none;
		cursor: default;
		color: var(--color-typography-default-title);

		&:hover {
			cursor: default;
			background-color: unset !important;
		}
	}
}

@media (max-width: 1279px) {
	.breadcrumbs {
		.return-icon {
			a {
				height: 24px;
				width: 24px;

				i {
					font-size: 24px;
				}
			}
		}

		.crumbs {
			&.solo {
				padding-left: calc(12px + 24px + 3px);
			}

			.path {
				.item {
					a {
						font-family: var(--typography-button-menu-2-font-family);
						font-size: var(--typography-title-5-font-size);
						line-height: var(--typography-button-menu-2-line-height);
						letter-spacing: var(--typography-button-menu-2-letter-spacing);
						font-weight: var(--typography-button-menu-2-font-weight);
					}

					.expansioner {
						font-family: var(--typography-button-menu-2-font-family);
						font-size: var(--typography-title-5-font-size);
						line-height: var(--typography-button-menu-2-line-height);
						letter-spacing: var(--typography-button-menu-2-letter-spacing);
						font-weight: var(--typography-button-menu-2-font-weight);
					}

					.pct-chevron-right {
						width: 16px;
						height: 16px;
					}
				}
			}

			.actual {
				font-family: var(--typography-title-2-font-family);
				font-size: var(--typography-title-2-font-size);
				line-height: var(--typography-title-2-line-height);
				letter-spacing: var(--typography-title-2-letter-spacing);
				font-weight: var(--typography-title-2-font-weight);
			}
		}
	}
}
