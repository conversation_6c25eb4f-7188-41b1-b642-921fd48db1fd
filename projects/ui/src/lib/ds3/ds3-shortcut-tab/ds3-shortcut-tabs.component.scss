@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.scss";

.tabs-container {
	margin: 5px;
}

.tabs-container .tab {
	user-select: none;
	padding: 10px 8px 10px 8px;
	text-align: center;
	line-height: 12px;
	font-family: $fontPoppins;
	color: $actionDefaultAble05;
	@extend .pct-title5;
	cursor: pointer;
	border-radius: 4px;
}

.tabs-container .tab:hover {
	border-radius: 2px;
	opacity: 90%;
}

.tabs-container .tab.active {
	color: $actionDefaultAble05;
	background-color: $feedbackInfo01;
}

.tabs-container {
	.nav {
		justify-content: center;
		background: $plane03;
		padding: 6px 4px;
	}
}

::ng-deep .full-tab .tabs-container {
	.tab {
		flex: 1;
	}
}
