import {
	AfterContentInit,
	Component,
	ContentChildren,
	ElementRef,
	Input,
	QueryList,
	Renderer2,
} from "@angular/core";
import { Ds3ShortcutTabComponent } from "./ds3-shortcut-tab.component";

@Component({
	selector: "ds3-shortcut-tabs",
	templateUrl: "./ds3-shortcut-tabs.component.html",
	styleUrls: ["./ds3-shortcut-tabs.component.scss"],
})
export class Ds3ShortcutTabsComponent implements AfterContentInit {
	@ContentChildren(Ds3ShortcutTabComponent)
	tabs: QueryList<Ds3ShortcutTabComponent>;
	customClass: string = "";

	//Com esse elemento a largura fica flexivel de acordo com o conteúdo.
	@Input() set fullTab(value: boolean) {
		if (value) {
			this.addCustomClass("full-tab");
		}
	}

	constructor(private renderer: Renderer2, private el: ElementRef) {}

	ngAfterContentInit() {
		const activeTabs = this.tabs.filter((tab) => tab.active);

		if (activeTabs.length === 0) {
			this.selectTab(this.tabs.first);
		}
	}

	selectTab(tab: Ds3ShortcutTabComponent) {
		this.tabs.toArray().forEach((tab) => (tab.active = false));
		tab.active = true;
	}

	// Método para adicionar classe personalizada
	private addCustomClass(className: string) {
		this.renderer.addClass(this.el.nativeElement, className);
	}
}
