import { boolean, text, withKnobs } from "@storybook/addon-knobs";
import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
import Notes from "./ds3-shortcut-tab.notes.md";

const description = () => Notes;

export default {
	title: "Design System 3 | Navigation/Shortcut Tab",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		docs: {
			extractComponentDescription: description,
		},
	},
};

export const interactive = () => ({
	template: `	
		<ds3-shortcut-tabs [fullTab]="isFullTab">
			
			<ds3-shortcut-tab [tabTitle]="titleTab">{{textTab}}</ds3-shortcut-tab>
			<ds3-shortcut-tab [tabTitle]="titleTab2">{{textTab2}}</ds3-shortcut-tab>
			<ds3-shortcut-tab [tabTitle]="titleTab3">{{textTab3}}</ds3-shortcut-tab>
		</ds3-shortcut-tabs>

    `,

	props: {
		isFullTab: boolean("Tabs full", false),
		titleTab: text("Tab Title 1", "Titulo"),
		textTab: text("Tab Text 1", "Conteudo 1"),
		titleTab2: text("Tab Title 2", "Titulo 2"),
		textTab2: text("Tab Text 2", "Conteudo 2"),
		titleTab3: text("Tab Title 3", "Titulo 3"),
		textTab3: text("Tab Text 3", "Conteudo 3"),
	},
});

// 	template: `
// 		<div style="padding: 16px;">
// 			<ds3-chips-list>
// 				<ng-container *ngFor="let chip of chips">
// 					<ds3-chips
// 						[isRemovable]="isRemovable"
// 						(removed)="deleteChip(chip)"
// 					>{{chip.name}}</ds3-chips>
// 				</ng-container>
// 			</ds3-chips-list>
// 		</div>
//     `,
// 	props: {
// 		isRemovable: boolean('Is removable', true),
// 		chips: CHIPS_LIST,
// 		deleteChip: (chip) => {
// 			const index = CHIPS_LIST.indexOf(chip);

// 			if (index >= 0) {
// 				CHIPS_LIST.splice(index, 1);
// 			}
// 		}
// 	},
// });

// export const defaultChips = () => ({
// 	template: `
// 		<div style="padding: 16px;">
// 			<ds3-chips-list>
// 				<ng-container *ngFor="let chip of chips">
// 					<ds3-chips>{{chip.name}}</ds3-chips>
// 				</ng-container>
// 			</ds3-chips-list>
// 		</div>
//     `,
// 	props: {
// 		chips: CHIPS_LIST,
// 	}
// });

// export const disabled = () => ({
// 	template: `
// 		<div style="padding: 16px;">
// 			<ds3-chips-list>
// 				<ng-container *ngFor="let chip of chips">
// 					<ds3-chips
// 						[isDisabled]="isDisabled"
// 					>{{chip.name}}</ds3-chips>
// 				</ng-container>
// 			</ds3-chips-list>
// 		</div>
//     `,
// 	props: {
// 		chips: CHIPS_LIST,
// 		isDisabled: boolean('Is disabled', true)
// 	}
// });
