**Seletores:** `ds3-shortcut-tabs, ds3-shortcut-tab`

**Utilização:**`<ds3-shortcut-tabs><ds3-shortcut-tab></ds3-shortcut-tab></ds3-shortcut-tabs>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component, OnInit } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    

}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-shortcut-tabs [fullTab]="true">
	<ds3-shortcut-tab tabTitle="Text button 1">Conteúdo 1 </ds3-shortcut-tab>
	<ds3-shortcut-tab tabTitle="Text button 2">Conteúdo 2</ds3-shortcut-tab>
	<ds3-shortcut-tab tabTitle="Text button 3">Conteúdo 3</ds3-shortcut-tab>
</ds3-shortcut-tabs>
```
&nbsp;  

## Props / Inputs

| Propiedade                               | Valores             | Valor Default        | Descrição          | 
|---                                       |                  ---|                   ---|                 ---|
| @Input() fullTab: boolean                |  true or false      |false                 | Define se as tabs vão ocupar todo conteúdo da box que envolve ou não|
| @Input() tabTitle: string                |  String             |Vazio                 | Define o Título da aba |

## More info

A tag <ds3-shortcut-tab> - Define uma Aba específica com seu conteúdo.<br>
A tag <ds3-shortcut-tabs> - Envolve o grupo de abas. Só funciona dessa forma.

&nbsp;  
