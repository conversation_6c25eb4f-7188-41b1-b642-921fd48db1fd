//@ts-ignore
import Notes from "./ds3-tooltip.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, select, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";

const description = () => Notes;

const posicoesIndicador = [
	"left",
	"right",
	"top-center",
	"top-left",
	"top-right",
	"bottom-center",
	"bottom-left",
	"bottom-right",
];
const posicoesTooltip = ["left", "right", "top", "bottom"];

export default {
	title: "Design System 3 | Tooltip",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const tooltip = () => ({
	template: `
    <div style="margin: 5em 15em;">
        <span [ds3Tooltip]="ds3Tooltip"
[showTooltipButton]="showTooltipButton"
[tooltipIndicator]="tooltipIndicator"
[tooltipPosition]="tooltipPosition"
[multiline]="multiline"
>
        {{internalSpan}}
        </span>
        </div>
    `,
	props: {
		internalSpan: text("internalSpan", "string Interno Da span"),
		ds3Tooltip: text("ds3Tooltip", "string Interno Da ds3Tooltip"),
		showTooltipButton: boolean("showTooltipButton", false),
		tooltipIndicator: select(
			"tooltipIndicator",
			posicoesIndicador,
			"bottom-center"
		),
		tooltipPosition: select("tooltipPosition", posicoesTooltip, "bottom"),
		multiline: boolean("multiline", false),
	},
});
