<ng-content></ng-content>
<div
	#tooltip
	(click)="toggleVariation()"
	[ngClass]="{
		showTooltipButton: showTooltipButton,
		multiline: showTooltipButton && multiline
	}"
	class="ds3-tooltip">
	<div class="ds3-tooltip-content-indicator">
		<div
			[ngStyle]="{ 'width.px': multiline ? 328 : null }"
			class="ds3-tooltip-content">
			<ng-container *ngIf="!isTemplate()">
				<ng-container *ngIf="asInnerHtml">
					<span [innerHTML]="ds3Tooltip"></span>
				</ng-container>
				<ng-container *ngIf="!asInnerHtml">
					{{ ds3Tooltip }}
				</ng-container>
			</ng-container>
			<ng-container
				*ngIf="isTemplate()"
				[ngTemplateOutlet]="ds3Tooltip"></ng-container>
			<i
				(click)="$event.stopPropagation(); hideTooltip()"
				*ngIf="showTooltipButton"
				class="pct pct-x close-button"></i>
		</div>
		<div
			class="ds3-tooltip-indicator {{ tooltipIndicator }} ds3-tooltip-trigger-{{
				tooltipPosition
			}}"></div>
	</div>
</div>
