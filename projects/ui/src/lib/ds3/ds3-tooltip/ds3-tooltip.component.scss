@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

.ds3-tooltip {
	position: absolute;
	pointer-events: none;
	visibility: hidden;
	display: none;
	z-index: 10000;

	&.showTooltipButton {
		width: 224px;
		height: 52px;

		.ds3-tooltip-content {
			@extend .typography-overline-2 !optional;
			width: 160px;
			color: $typeBgDarkTitle;
			flex: none;
			// order: 0;
			// flex-grow: 1;
			@extend .pct-overline2;
		}

		.close-button {
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			padding: 7px;
			width: 28px;
			height: 28px;
			flex: none;
			order: 1;
			flex-grow: 0;
			position: absolute;
			right: 2%;
			top: 14%;

			.pct-x {
				width: 14px;
				height: 14px;
			}
		}
	}

	.ds3-tooltip-content-indicator {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;

		.ds3-tooltip-content {
			position: relative;
			padding: 12px;
			border-radius: 4px;
			border: 1px solid #585a5c;
			font-size: 14px;
			font-weight: 400;
			color: $branco;
			line-height: 1.25;
			background: #585a5c;
			// width: 328px;
		}

		.ds3-tooltip-indicator {
			position: absolute;

			&:after {
				content: "";
				display: block;
				width: 10px;
				height: 10px;
				background: #585a5c;
				border: 1px solid #585a5c;
				pointer-events: none;
			}

			&.bottom-center {
				bottom: -4.7px;
				left: 50%;
				transform: translateX(-50%);

				&:after {
					transform: rotate(45deg);
					border-bottom-right-radius: 2px;
					border-top: 0;
					border-left: 0;
				}
			}

			&.bottom-right {
				bottom: -4.7px;
				left: auto;
				right: 10%;

				&:after {
					transform: rotate(45deg);
					border-bottom-left-radius: 2px;
					border-top: 0;
					border-right: 0;
				}
			}

			&.bottom-left {
				bottom: -4.7px;
				left: 10%;

				&:after {
					transform: rotate(45deg);
					border-bottom-right-radius: 2px;
					border-top: 0;
					border-left: 0;
				}
			}

			&.top-center {
				top: -4.7px;
				left: 50%;
				transform: translateX(-50%);

				&:after {
					transform: rotate(45deg);
					border-top-left-radius: 2px;
					border-bottom: 0;
					border-right: 0;
				}
			}

			&.top-right {
				top: -4.7px;
				left: auto;
				right: 10%;

				&:after {
					transform: rotate(45deg);
					border-top-left-radius: 2px;
					border-bottom: 0;
					border-right: 0;
				}
			}

			&.top-left {
				top: -4.7px;
				left: 10%;

				&:after {
					transform: rotate(45deg);
					border-top-right-radius: 2px;
					border-bottom: 0;
					border-left: 0;
				}
			}

			&.right {
				right: -4.7px;
				top: 50%;
				transform: translateY(-50%);

				&:after {
					transform: rotate(45deg);
					border-top-right-radius: 2px;
					border-bottom: 0;
					border-left: 0;
				}
			}

			&.left {
				left: -4.7px;
				top: 50%;
				transform: translateY(-50%);

				&:after {
					transform: rotate(45deg);
					border-top-left-radius: 2px;
					border-bottom: 0;
					border-right: 0;
				}
			}
		}
	}
}
