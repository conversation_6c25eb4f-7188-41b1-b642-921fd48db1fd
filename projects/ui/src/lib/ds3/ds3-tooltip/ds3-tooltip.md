# Tooltip

| Table Of Contents       |
| ----------------------- |
| [Selectors](#seletors) |
| [Inputs](#inputs)      |
| [Outputs](#outputs)    |
| [Example](#example)    |

## Seletors

o funcionamento do tooltip é chamado por uma diretive/input que insere o conteudo da tooltip

```ts
    <tag [ds3Tooltip]="'conteudo da tooltip'">conteudo da tag</tag>
```

## Inputs

| Input Name                     | utilidade                                                             | Possiveis Valores                                                                                                    | Valor Inicial   |
| ------------------------------ | --------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------- | --------------- |
| ds3Tooltip **obrigatório**     | valor que vai interno do tooltip                                      | templateRef \| string                                                                                                | nenhum          |
| showTooltipButton **opcional** | se o componente vai ter botão para fechar                             | boolean                                                                                                              | false           |
| tooltipIndicator **opcional**  | qual lado que vai ficar o triangulo de indicativo do tooltip          | 'left' \| 'right' \| 'top-center' \| 'top-left' \| 'top-right' \| 'bottom-center' \| 'bottom-left' \| 'bottom-right' | 'bottom-center' |
| tooltipPosition **opcional**   | qual lado o tooltip vai estar a partir do componente que foi indicado | 'left' \| 'right' \| 'top' \| 'bottom'                                                                               | 'top'           |
| multiline **opcional**         | se o tooltip deve ocupar mais de uma linha                            | boolean                                                                                                              | false           |

## Outputs

nenhum

## Example

> usando apenas string no ds3Tooltip

```html
<span [ds3Tooltip]="'texto interno da tooltip'">texto interno da span</span>
```

> usando apenas templateRef no ds3Tooltip

```html
<span [ds3Tooltip]="textoInternoRef">texto interno da span</span>

<ng-template #textoInternoRef> texto interno da tooltip </ng-template>
```

> usando apenas templateRef com uma lista no ds3Tooltip mas clicável

```html
 <!-- considerando telefones como:
	[
		'(12) 12345-6789',
		'(98) 987654321',
 		(22) 22222-2222
	]  -->
<p>
	{{telefones[0]}}
	<small [ds3Tooltip="telefonesExtraRef" [showTooltipButton]="true">
		+ {{telefones.lenght()-1}}
	</small>
</p>

<ng-template #telefonesExtraRef>
	<p *ngFor="let fone of telefones">
		{{fone}}
	</p>
</ng-template>
```
