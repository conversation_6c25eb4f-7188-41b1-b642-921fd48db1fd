@import "../../../../../assets/ds3/typography/mixins";
@import "../../../../../assets/ds3/colors.var.scss";
@import "../../../../../assets/ui-kit.scss";

.ds3-select-panel.mat-select-panel {
	margin-top: 98px;
	width: 300px;
	max-width: 300px;
	height: 45vh;
	max-height: 600px;
}

ds3-form-field > .content {
	margin: 8px 16px;
}

.group-label {
	@extend .pct-title5;
	color: var(--color-typography-default-text);
}

mat-form-field {
	width: 365px;
}

mat-select {
	@extend .pct-body1;
	max-width: 365px;
	border: 1px solid var(--color-support-gray-3);
	background: var(--color-background-plane-2);
	height: 40px;
	padding: 8px 16px 8px 16px;
	border-radius: 8px;
}

mat-option {
	@extend .pct-body2;
	color: var(--color-typography-default-title);
}

.input-area {
	display: flex !important;
	justify-content: space-between;
}

[ds3Suffix] > img,
[ds3Suffix] > i {
	max-width: 20px;
	max-height: 20px;
	font-size: 14px;
}
