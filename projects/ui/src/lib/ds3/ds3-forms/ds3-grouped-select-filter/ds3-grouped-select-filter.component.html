<mat-select
	#select="matSelect"
	(openedChange)="isOpened($event)"
	[formControl]="control"
	[multiple]="isMultiple"
	[placeholder]="placeholder"
	panelClass="ds3-select-panel">
	<mat-select-trigger *ngIf="isMultiple">
		{{ control.value ? control.value[0] : "" }}
		<span
			*ngIf="control.value?.length > 1"
			class="example-additional-selection">
			(+ {{ control.value.length - 1 }}
			{{ control.value?.length === 2 ? "selecionado" : "selecionados" }} )
		</span>
	</mat-select-trigger>
	<ds3-form-field>
		<input
			#filter
			[formControl]="filterFC"
			ds3Input
			placeholder="Filtrar por:" />
		<div *ngIf="isLoading" ds3Suffix>
			<img src="pacto-ui/images/loading.svg" />
		</div>
		<div *ngIf="!isLoading" ds3Suffix><i class="pct pct-search"></i></div>
	</ds3-form-field>
	<mat-option disabled value="none" *ngIf="!haveItens">Sem itens</mat-option>
	<ng-container *ngIf="haveItens">
		<ng-container *ngFor="let group of groupsArray">
			<div *ngIf="!unGroup; else ungroupedRef">
				<mat-optgroup>
					<ds3-diviser></ds3-diviser>
					<span class="group-label">{{ group.groupLabel }}</span>
					<ng-container
						*ngIf="
							group.groupData && group.groupData.length > 0;
							else noItensInCategory
						">
						<ng-container *ngFor="let entry of group.groupData">
							<mat-option [value]="entry.entryValue">
								{{ entry.entryLabel }}
							</mat-option>
						</ng-container>
					</ng-container>
					<ng-template #noItensInCategory>
						<mat-option disabled value="none">
							Sem itens na categoria
						</mat-option>
					</ng-template>
				</mat-optgroup>
			</div>
			<ng-template #ungroupedRef>
				<ng-container *ngFor="let entry of group.groupData">
					<mat-option [value]="entry.entryValue">
						{{ entry.entryLabel }}
					</mat-option>
				</ng-container>
			</ng-template>
		</ng-container>
	</ng-container>
</mat-select>
