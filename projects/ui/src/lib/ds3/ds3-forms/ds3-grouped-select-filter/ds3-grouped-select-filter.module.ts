import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3GroupedSelectFilterComponent } from "./ds3-grouped-select-filter.component";
import { MatSelectModule } from "@angular/material";
import { Ds3DiviserModule } from "../../ds3-diviser/ds3-diviser.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Ds3FormFieldModule } from "../ds3-form-field/ds3-form-field.module";

@NgModule({
	declarations: [Ds3GroupedSelectFilterComponent],
	imports: [
		MatSelectModule,
		Ds3DiviserModule,
		CommonModule,
		FormsModule,
		Ds3FormFieldModule,
		ReactiveFormsModule,
	],
	exports: [Ds3GroupedSelectFilterComponent, MatSelectModule],
})
export class Ds3GroupedSelectFilterModule {}
