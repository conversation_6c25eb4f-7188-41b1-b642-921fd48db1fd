import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3GroupedSelectFilterComponent } from "./ds3-grouped-select-filter.component";

describe("Ds3GroupedSelectFilterComponent", () => {
	let component: Ds3GroupedSelectFilterComponent;
	let fixture: ComponentFixture<Ds3GroupedSelectFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3GroupedSelectFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3GroupedSelectFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
