import { HttpClient } from "@angular/common/http";
import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	InjectionToken,
	Input,
	OnInit,
	Output,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import {
	MAT_SELECT_SCROLL_STRATEGY,
	MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
	MatSelect,
} from "@angular/material";
import { SPACE, ENTER } from "@angular/cdk/keycodes";
import { MAT_TOOLTIP_DEFAULT_OPTIONS } from "@angular/material";
import {
	NoopScrollStrategy,
	ScrollStrategyOptions,
} from "@angular/cdk/overlay";

@Component({
	selector: "ds3-grouped-select-filter",
	templateUrl: "./ds3-grouped-select-filter.component.html",
	styleUrls: ["./ds3-grouped-select-filter.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3GroupedSelectFilterComponent implements OnInit {
	GroupsModel: {
		groupLabel: string;
		groupData: [{ entryValue: any; entryLabel: string }];
	};

	urlsModel: [{ label: string; url: string; idKey?: string; idLabel?: string }];

	@Output() filterSearch = new EventEmitter<string>();
	@Output() change = new EventEmitter<any>();
	@Input() control: FormControl;

	@Input() label?;
	@Input() placeholder?;
	@Input() isMultiple?: boolean = false;
	@Input() unGroup?: boolean = false;
	@Input() urlArray?;
	@Input() groupsArray?;
	isLoading: boolean = false;

	@ViewChild("select", { static: true }) select: any;
	@ViewChild("filter", { static: true }) filter: any;
	filterFC: FormControl = new FormControl("");

	constructor(private http: HttpClient, private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.loadData();
		this.select._handleKeydown = (event: KeyboardEvent) => {
			if (event.keyCode == SPACE || event.keyCode == ENTER) {
				return;
			}
			if (!this.select.disabled) {
				this.select.panelOpen
					? this.select._handleOpenKeydown(event)
					: this.select._handleClosedKeydown(event);
			}
		};

		this.filterFC.valueChanges.subscribe((v) => {
			this.isLoading = true;
			setTimeout(() => {
				if (v != this.filterSearch) {
					this.filterSearch.emit(v);
					this.loadData();
				}
				this.isLoading = false;
			}, 999);
		});
		this.control.valueChanges.subscribe((v) => {
			this.change.emit(v);
		});
	}

	isOpened(v) {
		if (v) {
			this.filter.nativeElement.focus();
			this.filter.nativeElement.scrollIntoView();
		}
	}

	loadData() {
		if (this.urlArray) {
			this.urlArray.forEach((v) => {
				this.http
					.get(v.url, { params: { search: this.filterFC.value } })
					.subscribe((response: { content: [] }) => {
						let arrayCorrigido = new Array();
						response.content.forEach((element) => {
							let caminhoPropriedadeId = v.idKey || "id";
							let caminhoPropriedadeLabel = v.idLabel || "label";

							arrayCorrigido.push({
								entryLabel: element[caminhoPropriedadeLabel],
								entryValue: element[caminhoPropriedadeId],
							});
						});
						this.groupsArray.push({
							groupLabel: v.label,
							groupData: arrayCorrigido,
						});
					});
			});
		}
	}

	get haveItens(): boolean {
		return (
			this.groupsArray &&
			this.groupsArray.length > 0 &&
			this.groupsArray.some((v) => v.groupData && v.groupData.length > 0)
		);
	}
}
