import {
	AfterViewInit,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";

@Component({
	selector: "ds3-color-slider",
	templateUrl: "./ds3-color-slider.component.html",
	styleUrls: ["./ds3-color-slider.component.scss"],
})
export class Ds3ColorSliderComponent implements AfterViewInit {
	@ViewChild("canvas", { static: true })
	canvas: ElementRef<HTMLCanvasElement>;

	@Output()
	color: EventEmitter<string> = new EventEmitter();

	private ctx: CanvasRenderingContext2D;
	private mousedown: boolean = false;
	private selectedWidth: number;

	ngAfterViewInit() {
		this.draw();
	}

	draw() {
		if (!this.ctx) {
			this.ctx = this.canvas.nativeElement.getContext("2d");
		}
		const width = this.canvas.nativeElement.width;
		const height = this.canvas.nativeElement.height;

		this.ctx.clearRect(0, 0, width, height);

		const gradient = this.ctx.createLinearGradient(0, 0, width, 0);
		// // cores criadas na mão
		gradient.addColorStop(0, " hsl(0,100%,50%)");
		gradient.addColorStop(0.2, "hsl(60,100%,50%)");
		gradient.addColorStop(0.25, "hsl(120,100%,50%)");
		gradient.addColorStop(0.3, "hsl(180,100%,50%)");
		gradient.addColorStop(0.5, "hsl(240,100%,50%)");
		gradient.addColorStop(0.8, "hsl(300,100%,50%)");
		gradient.addColorStop(0.9, "hsl(360,100%,50%)");

		// // cores usando figma
		// gradient.addColorStop(0.9616, '#FF0312');
		// gradient.addColorStop(0.8754, '#F4053E');
		// gradient.addColorStop(0.8062, '#FE07E5');
		// gradient.addColorStop(0.7298, '#DB09FD');
		// gradient.addColorStop(0.6653, '#920AFD');
		// gradient.addColorStop(0.5936, '#930CFC');
		// gradient.addColorStop(0.5196, '#0129FC');
		// gradient.addColorStop(0.4312, '#168EFD');
		// gradient.addColorStop(0.3500, '#3DECEC');
		// gradient.addColorStop(0.2712, '#7EF436');
		// gradient.addColorStop(0.1900, '#FAFF09');
		// gradient.addColorStop(0.1860, '#FF1111');
		// gradient.addColorStop(0.0945, '#FFC700');

		this.ctx.beginPath();
		this.ctx.rect(0, 0, width, height);

		this.ctx.fillStyle = gradient;
		this.ctx.fill();
		this.ctx.closePath();

		if (this.selectedWidth) {
			this.ctx.beginPath();
			this.ctx.strokeStyle = "white";
			this.ctx.lineWidth = 3;
			this.ctx.arc(this.selectedWidth, 8, 4, 0, 2 * Math.PI);
			this.ctx.stroke();
			this.ctx.closePath();
		}
	}

	@HostListener("window:mouseup", ["$event"])
	onMouseUp(evt: MouseEvent) {
		this.mousedown = false;
	}

	onMouseDown(evt: MouseEvent) {
		this.mousedown = true;
		this.selectedWidth = evt.offsetX;
		this.draw();
		this.emitColor(evt.offsetX, evt.offsetY);
	}

	onMouseMove(evt: MouseEvent) {
		if (this.mousedown) {
			this.selectedWidth = evt.offsetX;
			this.draw();
			this.emitColor(evt.offsetX, evt.offsetY);
		}
	}

	emitColor(x: number, y: number) {
		const rgbaColor = this.getColorAtPosition(x, 8);
		this.color.emit(rgbaColor);
	}

	getColorAtPosition(x: number, y: number) {
		const imageData = this.ctx.getImageData(x, y, 1, 1).data;
		return (
			"rgba(" + imageData[0] + "," + imageData[1] + "," + imageData[2] + ",1)"
		);
	}
}
