@import "../../../../../assets/ui-kit.scss";

.wrapper {
	width: 100%;
	height: 40px;
	background-color: var(--color-background-plane-2);
	border: 1px solid var(--color-support-gray-3);
	border-radius: 8px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	@extend .typography-body-1;
	color: var(--color-typography-default-text);

	&.opened {
		border: 1px solid var(--color-action-default-able-4);
	}

	.info {
		display: flex;
		flex-wrap: nowrap;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;

		.cor {
			margin: 8px 16px;
			width: 24px;
			height: 24px;
			border-radius: 4px;
		}

		input {
			width: 100%;
			border: none;
			height: 20px;
			margin: 9px 0px 9px 16px;

			@extend .typography-body-1;
			color: var(--color-typography-default-text);

			&:focus-visible {
				outline: none;
			}
		}
	}
}

.mat-dialog-container {
	padding: 16px 8px;
}

.dialog-color-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;

	ds3-color-palette {
		padding-bottom: 16px;
	}
}

.dialog-input-wrapper {
	margin-top: 16px;
	display: flex;
	justify-content: space-between;
	flex-direction: row;

	.input-type {
		background-color: #e4e5e7;
		padding: 12px 17px;
		//styleName: Parágrafo / Normal;
		font-family: Nunito Sans;
		font-size: 16px;
		font-weight: 400;
		line-height: 25.89px;
		text-align: left;
		color: #797d86;
	}

	.input-value {
		border: 1px solid #c9cbcf;
		//styleName: Parágrafo / Normal;
		font-family: Nunito Sans;
		font-size: 16px;
		font-weight: 400;
		line-height: 25.89px;
		text-align: left;
		color: #797d86;
		border-radius: 4px;
		padding: 12px 16px;
	}
}

.mat-dialog-container {
	padding: 16px 8px !important;
}

// @@TODO change to tokens
