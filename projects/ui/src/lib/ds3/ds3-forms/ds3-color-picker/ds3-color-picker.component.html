<div (click)="openDialogColorPicker($event, 'bottom-center')" class="wrapper">
	<div class="info">
		<div [ngStyle]="{ background: color }" class="cor"></div>
		<div class="input">
			<input [(ngModel)]="color" readonly type="text" />
		</div>
	</div>
	<i class="pct pct-chevron-down"></i>
</div>

<ng-template #dialogContent>
	<div class="dialog-color-wrapper">
		<ds3-color-palette
			(color)="value = $event"
			[hue]="hue"
			[typeColor]="typeColor"></ds3-color-palette>
		<ds3-color-slider (color)="hue = $event"></ds3-color-slider>
	</div>
	<div class="dialog-input-wrapper">
		<span (click)="changeColorType()" class="input-type">
			{{ typeColor | uppercase }}
		</span>
		<span class="input-value">{{ color }}</span>
	</div>
</ng-template>
