// @ts-ignore
import { moduleMetadata } from "@storybook/angular";
import {
	boolean,
	number,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import { Ds3Module } from "../../ds3.module";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";

export default {
	title: "Design System 3 | Inputs/Date Input",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, FormsModule, ReactiveFormsModule],
		}),
		withKnobs,
	],
	parameters: {},
};

export const colorpicker = () => ({
	template: `
   <div class="workbench">
	<ds3-form-field>
		<ds3-field-label>{{label}}</ds3-field-label>
		<ds3-color-picker
			 ds3Input
			 [formControl]="form.get('control')"
		> </ds3-color-picker>
	</ds3-form-field>

	{{form.getRawValue()|json}}
</div>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		label: text("Label", "Label"),
	},
});
