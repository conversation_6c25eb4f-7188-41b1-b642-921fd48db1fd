# color picker

**Seletor:** `ds3-color-picker`

## Uso Básico

```html
<ds3-color-picker ds3Input></ds3-color-picker>
```

`<ds3-color-picker>` é o formato de seleção de cores do ds3, deve ser usado dentro de um `ds3-form-field` para melhor desempenho, sua funcionalidade se dá a partir de canvas e de sliders para fazer a seleção da cor, a saida dos valores são string com valores em
HEX (por exemplo #223e1f) ou
em rgb (por exemplo rgb(53,31,67)).

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component, FormControl, FormGroup } from "@angular/core";

@Component({
	selector: "meu-exemplo-de-tela",
	styleUrls: ["./meu-exemplo-de-tela.component.scss"],
	templateUrl: "./meu-exemplo-de-tela.component.html",
})
class MeuExemploDeTelaComponent {
	form = new FormGroup({
		color: new FormControl(),
	});
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-form-field>
	<ds3-field-label>color picker</ds3-field-label>
	<ds3-color-picker
		ds3Input
		[formControl]="form.get('color')"></ds3-color-picker>
</ds3-form-field>
```

&nbsp;

## Props / Inputs

| Property | Valor padrão | Descrição |
| -------- | ------------ | --------- |
| nenhum   | -            | -         |

## Outputs / Events

| Property          | Values | Default |
| ----------------- | ------ | ------- |
| onChange          | ------ | ------- |
| onTouch           | ------ | ------- |
| registerOnChange  | ------ | ------- |
| registerOnTouched | ------ | ------- |

## More info

No more info.
