import {
	Component,
	ElementRef,
	forwardRef,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { NG_VALUE_ACCESSOR } from "@angular/forms";
import { MatDialog } from "@angular/material";

@Component({
	selector: "ds3-color-picker",
	templateUrl: "./ds3-color-picker.component.html",
	styleUrls: ["./ds3-color-picker.component.scss"],
	encapsulation: ViewEncapsulation.None,
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3ColorPickerComponent),
			multi: true,
		},
	],
})
export class Ds3ColorPickerComponent implements OnInit {
	public hue: string;
	public color: string = "#000000";
	public typeColor: "hex" | "rgb" = "hex";

	constructor(private dialog: MatDialog) {}

	onChange: any = () => {};
	onTouch: any = () => {};

	set value(val) {
		if (!!val && this.color !== val) {
			this.color = val;
			this.onChange(val);
			this.onTouch(val);
		}
	}

	writeValue(value: any) {
		this.value = value;
	}

	registerOnChange(fn: any) {
		this.onChange = fn;
	}

	registerOnTouched(fn: any) {
		this.onTouch = fn;
	}

	ngOnInit() {}

	@ViewChild("dialogContent", { static: false })
	dialogContent: TemplateRef<any>;

	openDialogColorPicker(
		event: PointerEvent,
		position:
			| "top-left"
			| "top-center"
			| "top-right"
			| "middle-left"
			| "middle-center"
			| "middle-right"
			| "bottom-left"
			| "bottom-center"
			| "bottom-right"
	): void {
		let topVar, leftVar: number;
		const wrapperElement = event.currentTarget as HTMLElement;
		const wrapperRect = wrapperElement.getBoundingClientRect();
		const WRAPPER_Y = wrapperRect.top;
		const WRAPPER_X = wrapperRect.left;
		const INPUT_WIDTH = 350;
		const INPUT_HEIGHT = 40;
		const INPUT_WITH_LABEL_HEIGHT = 66.8;
		const DIALOG_WIDTH = 350;
		const DIALOG_HEIGHT = 250;
		switch (position) {
			case "top-left":
				topVar = WRAPPER_Y - DIALOG_HEIGHT - INPUT_WITH_LABEL_HEIGHT;
				leftVar = WRAPPER_X - DIALOG_WIDTH;
				break;

			case "top-center":
				topVar = WRAPPER_Y - DIALOG_HEIGHT - INPUT_WITH_LABEL_HEIGHT;
				leftVar = WRAPPER_X;
				break;

			case "top-right":
				topVar = WRAPPER_Y - DIALOG_HEIGHT - INPUT_WITH_LABEL_HEIGHT;
				leftVar = WRAPPER_X + INPUT_WIDTH;
				break;

			case "middle-left":
				topVar = WRAPPER_Y - DIALOG_HEIGHT / 2;
				leftVar = WRAPPER_X - DIALOG_WIDTH;
				break;

			case "middle-center":
				topVar = WRAPPER_Y - DIALOG_HEIGHT / 2;
				leftVar = WRAPPER_X;
				break;

			case "middle-right":
				topVar = WRAPPER_Y - DIALOG_HEIGHT / 2;
				leftVar = WRAPPER_X + INPUT_WIDTH;
				break;

			case "bottom-left":
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				leftVar = WRAPPER_X - DIALOG_WIDTH;
				break;

			case "bottom-center":
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				leftVar = WRAPPER_X;
				break;

			case "bottom-right":
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				leftVar = WRAPPER_X + INPUT_WIDTH;
				break;
			default:
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				leftVar = WRAPPER_X;
				break;
		}

		const dialogRef = this.dialog.open(this.dialogContent, {
			closeOnNavigation: true,
			backdropClass: "bg-fundo",
			id: "color-picker-dialog",
			position: { top: `${topVar}px`, left: `${leftVar}px` },
		});
	}

	changeColorType() {
		if (this.typeColor === "hex") {
			this.typeColor = "rgb";
			const hex = this.color.replace("#", "");
			const r = parseInt(hex.substring(0, 2), 16);
			const g = parseInt(hex.substring(2, 4), 16);
			const b = parseInt(hex.substring(4, 6), 16);
			this.color = `rgb(${r},${g},${b})`;
		} else {
			this.typeColor = "hex";
			const [r, g, b] = this.color.match(/\d+/g);
			this.color = `#${(
				(1 << 24) |
				(parseInt(r) << 16) |
				(parseInt(g) << 8) |
				parseInt(b)
			)
				.toString(16)
				.slice(1)}`;
		}
	}
}
