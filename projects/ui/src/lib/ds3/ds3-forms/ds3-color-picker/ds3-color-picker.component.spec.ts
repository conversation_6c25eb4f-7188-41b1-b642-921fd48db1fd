import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3ColorPickerComponent } from "./ds3-color-picker.component";

describe("Ds3ColorPickerComponent", () => {
	let component: Ds3ColorPickerComponent;
	let fixture: ComponentFixture<Ds3ColorPickerComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3ColorPickerComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3ColorPickerComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
