import { NgModule } from "@angular/core";
import { Ds3InputDateComponent } from "./ds3-input-date.component";
import { Ds3InputDateHeaderComponent } from "./ds3-input-date-header/ds3-input-date-header.component";
import { MatDatepickerModule, MatDialogModule } from "@angular/material";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { Ds3ButtonModule } from "../../ds3-button/ds3-button.module";
import { NgxMaterialTimepickerModule } from "ngx-material-timepicker";
import { CommonModule } from "@angular/common";
import { Ds3DiviserModule } from "../../ds3-diviser/ds3-diviser.module";
import { ReactiveFormsModule } from "@angular/forms";
import { Ds3FormFieldModule } from "../ds3-form-field/ds3-form-field.module";
@NgModule({
	declarations: [Ds3InputDateComponent, Ds3InputDateHeaderComponent],
	imports: [
		MatDatepickerModule,
		Ds3DiviserModule,
		NgbModule,
		CommonModule,
		Ds3ButtonModule,
		Ds3FormFieldModule,
		ReactiveFormsModule,
		MatDialogModule,
		NgxMaterialTimepickerModule.setOpts("pt-BR", "latn"),
	],
	entryComponents: [Ds3InputDateHeaderComponent],
	exports: [Ds3InputDateComponent],
})
export class Ds3InputDateModule {}
