<label #labelRef *ngIf="!!label && !showLabelOnModal">
	{{ label }}
</label>
<div [ngSwitch]="dateType">
	<ng-container *ngSwitchCase="'datepicker'">
		<div
			class="ds3-input-date-wrapper"
			[class.disabled]="disabled"
			[class.error]="control?.invalid && (control?.dirty || control?.touched)"
			(click)="datePicker.open()">
			<input
				[class.disabled]="disabled"
				#inputRef
				[formControl]="control"
				[matDatepicker]="datePicker"
				ds3Input
				matInput
				[disabled]="disabled"
				maxlength="10"
				tabindex="-1"
				(keyup)="manualEntry($event)"
				placeholder="dd/mm/aaaa"
				type="text" />
			<button [disabled]="disabled" ds3-icon-button type="button">
				<i class="pct pct-calendar"></i>
			</button>
		</div>
		<mat-datepicker
			#datePicker
			[startView]="startView"
			(opened)="datepickerOpenHandler()"
			[calendarHeaderComponent]="ds3InputDateHeaderComponent"
			panelClass="ds3-datepicker"></mat-datepicker>
	</ng-container>
	<ng-container *ngSwitchCase="'dateranger'">
		<div
			[class.opened]="openedDateRange"
			#wrapperDateRanger
			(click)="
				openDialogDateRanger($event, position); lastfocusedElement = $event
			"
			class="ds3-input-date-wrapper dateranger">
			<input
				#inputRef
				ds3Input
				readonly
				inert
				type="text"
				value="{{ inputDateRangeStart | date : 'shortDate' }}  -  {{
					inputDateRangeEnd | date : 'shortDate'
				}}" />
			<button ds3-icon-button #buttonOpenDateRanger type="button">
				<i class="pct pct-calendar"></i>
			</button>
		</div>
	</ng-container>
	<ng-container *ngSwitchCase="'timepicker'">
		<div class="ds3-input-date-wrapper">
			<input
				#inputRef
				[formControl]="control"
				[format]="format"
				[ngxTimepicker]="timepicker"
				ds3Input
				inert
				readonly />
			<button
				(click)="timepicker.open()"
				#timepickerButton
				ds3-icon-button
				type="button">
				<i class="pct pct-clock"></i>
			</button>
		</div>
		<ngx-material-timepicker
			#timepicker
			(timeChanged)="changeTime($event, control)"
			[appendToInput]="true"
			[cancelBtnTmpl]="cancelBtnTmpl"
			[confirmBtnTmpl]="confirmBtnTmpl"
			[editableHintTmpl]="editableHintTmpl"
			[enableKeyboardInput]="true"
			(closed)="setFocus(timepickerButton)"
			[theme]="ds3TimePicker"
			[timepickerClass]="'ds3-timepicker'"></ngx-material-timepicker>
	</ng-container>
	<ng-container *ngSwitchCase="'timeranger'">
		<div class="time-range">
			<div class="ds3-input-date-wrapper">
				<input
					[format]="format"
					[ngxTimepicker]="timeRangerStart"
					readonly
					inert />
				<button
					(click)="timeRangerStart.open()"
					#timeRangerStartButton
					ds3-icon-button
					type="button">
					<i class="pct pct-clock"></i>
				</button>
			</div>
			<div class="pct-title4">
				{{ separatorText }}
			</div>
			<div class="ds3-input-date-wrapper">
				<input
					[format]="format"
					[ngxTimepicker]="timeRangerEnd"
					readonly
					inert />
				<button
					(click)="timeRangerEnd.open()"
					#timeRangerEndButton
					ds3-icon-button
					type="button">
					<i class="pct pct-clock"></i>
				</button>
			</div>
		</div>
		<ngx-material-timepicker
			#timeRangerStart
			(timeChanged)="changeTime($event, controlStart)"
			[appendToInput]="true"
			[cancelBtnTmpl]="cancelBtnTmpl"
			[confirmBtnTmpl]="confirmBtnTmpl"
			[editableHintTmpl]="editableHintTmpl"
			[enableKeyboardInput]="true"
			(closed)="setFocus(timeRangerStartButton)"
			[theme]="ds3TimePicker"
			[timepickerClass]="'ds3-timepicker'"></ngx-material-timepicker>
		<ngx-material-timepicker
			#timeRangerEnd
			(timeChanged)="changeTime($event, controlEnd)"
			[appendToInput]="true"
			[cancelBtnTmpl]="cancelBtnTmpl"
			[confirmBtnTmpl]="confirmBtnTmpl"
			[editableHintTmpl]="editableHintTmpl"
			[enableKeyboardInput]="true"
			(closed)="setFocus(timeRangerEndButton)"
			[theme]="ds3TimePicker"
			[timepickerClass]="'ds3-timepicker'"></ngx-material-timepicker>
	</ng-container>
</div>

<ng-template #dateRanger>
	<div class="dateranger">
		<div class="date-range-label" *ngIf="showLabelOnModal">
			{{ label }}
		</div>
		<div class="presets">
			<button
				(click)="setPreset('ultimaSemana')"
				(keyup.enter)="setPreset('ultimaSemana')"
				[class.selected]="selectedPreset === 'ultimaSemana'"
				ds3-text-button
				type="button">
				Última semana
			</button>
			<button
				(click)="setPreset('ultimoMes')"
				(keyup.enter)="setPreset('ultimoMes')"
				[class.selected]="selectedPreset === 'ultimoMes'"
				ds3-text-button
				type="button">
				Último mês
			</button>
			<button
				(click)="setPreset('ultimos6Meses')"
				(keyup.enter)="setPreset('ultimos6Meses')"
				[class.selected]="selectedPreset === 'ultimos6Meses'"
				ds3-text-button
				type="button">
				Últimos 6 meses
			</button>
			<button
				(click)="setPreset('ultimoAno')"
				(keyup.enter)="setPreset('ultimoAno')"
				[class.selected]="selectedPreset === 'ultimoAno'"
				ds3-text-button
				type="button">
				Último ano
			</button>
			<button
				(click)="setPreset('personalizado')"
				[class.selected]="selectedPreset === 'personalizado'"
				ds3-text-button
				type="button">
				Personalizado
			</button>
		</div>
		<div class="calendars" tabindex="-1">
			<div class="cal">
				<span class="cal-header">Início</span>
				<mat-calendar
					#calendarStart
					(selectedChange)="onSelectStart($event, 'click')"
					[dateClass]="dateClass()"
					[headerComponent]="ds3InputDateHeaderComponent"
					[selected]="selectedDateStart"></mat-calendar>
			</div>
			<div class="cal">
				<span class="cal-header">Fim</span>
				<mat-calendar
					#calendarEnd
					(selectedChange)="onSelectEnd($event, 'click')"
					[dateClass]="dateClass()"
					[headerComponent]="ds3InputDateHeaderComponent"
					[selected]="selectedDateEnd"></mat-calendar>
			</div>
		</div>
	</div>
	<div mat-dialog-actions>
		<div class="ds3-mat-dialog-actions">
			<button (click)="limparFiltro()" ds3-outlined-button type="button">
				Limpar filtro
			</button>
			<button
				(click)="aplicarFiltro()"
				ds3-flat-button
				type="button"
				[disabled]="!(selectedDateStart && selectedDateEnd)">
				Aplicar filtro
			</button>
		</div>
	</div>
</ng-template>

<ng-template #cancelBtnTmpl>
	<button ds3-outlined-button type="button" (click)="onCancel(timepicker)">
		Limpar Filtro
	</button>
</ng-template>
<ng-template #confirmBtnTmpl>
	<button ds3-flat-button type="button">Aplicar Filtro</button>
</ng-template>
<ng-template #editableHintTmpl>
	<span class="info">
		<i class="pct pct-info"></i>
		Selecione as horas e minutos
	</span>
</ng-template>
