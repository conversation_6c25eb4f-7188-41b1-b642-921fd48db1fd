// @ts-ignore
import { moduleMetadata } from "@storybook/angular";
import {
	boolean,
	number,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import { Ds3Module } from "../../ds3.module";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";

export default {
	title: "Design System 3 | Inputs/Date Input",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, FormsModule, ReactiveFormsModule],
		}),
		withKnobs,
	],
	parameters: {},
};

export const datepicker = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
						<ds3-input-date ds3Input dateType="datepicker" formControlName="control"></ds3-input-date>
          </ds3-form-field>
        </form>
		
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		label: text("Label", "Label"),
		type: select(
			"Tipo de entrada",
			["text", "email", "number", "tel", "url"],
			"text"
		),
	},
});
export const dateranger = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
			<ds3-input-date ds3Input dateType="dateranger" [position]="position" formControlName="control"> </ds3-input-date>
          </ds3-form-field>
        </form>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		label: text("Label", "Label"),
		position: select(
			"Posição",
			[
				"top-right",
				"top-left",
				"bottom-right",
				"bottom-left",
				"middle-right",
				"middle-left",
			],
			"bottom-right"
		),
	},
});
export const timepicker = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
			<ds3-input-date ds3Input dateType="timepicker" [format]='format' formControlName="control"></ds3-input-date>
          </ds3-form-field>
        </form>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		label: text("Label", "Label"),
		type: select("Tipo de Horario", [24, 12], 12),
	},
});
