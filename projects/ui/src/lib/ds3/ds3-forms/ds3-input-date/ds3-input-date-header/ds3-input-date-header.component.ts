import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	LOCALE_ID,
	OnD<PERSON>roy,
	ViewEncapsulation,
} from "@angular/core";
import { DateAdapter, MatCalendar } from "@angular/material";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
	selector: "pacto-ds3-input-date-header",
	templateUrl: "./ds3-input-date-header.component.html",
	styleUrls: ["./ds3-input-date-header.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	encapsulation: ViewEncapsulation.None,
})
export class Ds3InputDateHeaderComponent<D> implements OnDestroy {
	private _destroyed = new Subject<void>();
	yearsPerPage = 24;
	constructor(
		private _calendar: MatCalendar<D>,
		private _dateAdapter: DateAdapter<D>,
		cdr: ChangeDetectorRef
	) {
		_calendar.stateChanges
			.pipe(takeUntil(this._destroyed))
			.subscribe(() => cdr.markForCheck());
	}

	ngOnD<PERSON>roy() {
		this._destroyed.next();
		this._destroyed.complete();
	}

	get periodLabel() {
		return this._calendar && this._calendar.activeDate instanceof Date
			? this._calendar.activeDate
			: new Date();
	}

	previousClicked(): void {
		const currentDate = this._calendar.activeDate || this._dateAdapter.today();
		this._calendar.activeDate =
			this._calendar.currentView == "month"
				? this._dateAdapter.addCalendarMonths(currentDate, -1)
				: this._dateAdapter.addCalendarYears(
						currentDate,
						this._calendar.currentView == "year" ? -1 : -this.yearsPerPage
				  );
	}

	nextClicked(): void {
		const currentDate = this._calendar.activeDate || this._dateAdapter.today();
		this._calendar.activeDate =
			this._calendar.currentView == "month"
				? this._dateAdapter.addCalendarMonths(currentDate, 1)
				: this._dateAdapter.addCalendarYears(
						currentDate,
						this._calendar.currentView == "year" ? 1 : this.yearsPerPage
				  );
	}

	openCalendar() {
		if (this._calendar.currentView == "month") {
			this._calendar._goToDateInView(this._calendar.activeDate, "year");
		} else if (this._calendar.currentView == "year") {
			this._calendar._goToDateInView(this._calendar.activeDate, "multi-year");
		}
	}

	get currentView() {
		return this._calendar.currentView;
	}
}
