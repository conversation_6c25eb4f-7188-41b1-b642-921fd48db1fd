<div class="calendar-header">
	<div class="calendar-selected">
		<span class="calendar-label">
			{{ periodLabel | date : "MMMM yyyy" | titlecase }}
		</span>
		<button
			(click)="openCalendar()"
			*ngIf="currentView != 'multi-year'"
			class="dropdown-icon"
			ds3-icon-button
			size="lg">
			<i class="pct pct-chevron-down"></i>
		</button>
	</div>
	<div class="calendar-actions">
		<button
			(click)="previousClicked()"
			class="month-prev"
			ds3-icon-button
			size="lg">
			<i class="pct pct-chevron-left"></i>
		</button>
		<button
			(click)="nextClicked()"
			class="month-next"
			ds3-icon-button
			size="lg">
			<i class="pct pct-chevron-right"></i>
		</button>
	</div>
</div>
