@import "../../../../../assets/ui-kit.scss";

.ds3-input-date-wrapper {
	height: 40px;
	background-color: var(--color-background-plane-2);
	border: 1px solid var(--color-support-gray-3);
	padding-right: 8px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	@extend .typography-body-1;
	color: var(--color-typography-default-text);

	&.opened {
		border: 1px solid var(--color-action-default-able-4);
	}

	input {
		width: 100%;
		border: none;
		height: 20px;
		margin: 9px 0px 9px 16px;

		@extend .typography-body-1;
		color: var(--color-typography-default-text);

		&:focus-visible {
			outline: none;
		}
	}

	.icon {
		font-size: 16px;
		color: var(--color-action-default-disabled-2);

		& > button:focus,
		& > button:hover {
			outline: none;
		}
	}

	&.error {
		border-color: var(--color-action-default-risk-5);
	}

	&.disabled {
		background-color: var(--form-field-disabled-background);
		border-color: var(--form-field-disabled-background);
	}

	input {
		&.error {
			color: var(--color-action-default-risk-5);
		}

		&.disabled {
			background-color: var(--form-field-disabled-background);
			border-color: var(--form-field-disabled-background);
		}
	}
}

//ESTILOS DATE

mat-month-view .mat-calendar-body-cell {
	@extend .typography-overline-2;
	color: var(--color-typography-default-text);

	.mat-calendar-body-selected {
		color: var(--color-typography-bg-dark-title);
		background-color: var(--color-action-default-able-4);
	}
}

mat-year-view,
mat-multi-year-view {
	.mat-calendar-body-selected {
		border-radius: 4px;
		background-color: var(--color-feedback-info-1);
		color: var(--color-action-default-able-5);
		border-color: none;
	}

	.mat-calendar-body-cell {
		@extend .typography-button-default-2;
		font-size: 14px;
		color: var(--color-action-default-able-4);
	}
}

:host {
	.mat-datepicker-content.mat-datepicker {
		top: 55px !important;
		left: 0px !important;
	}

	&.mat-calendar {
		width: 350px !important;
		height: 419px !important;
	}
}

label {
	@extend .typography-title-4;
	padding: 0px 0px 8px 0px;
}

#date-ranger-dialog {
	padding: 16px;
	margin: 0px;
}

.dateranger {
	.presets {
		display: flex;
		justify-content: space-between;
		flex-direction: row;
		gap: 8px;
		margin-bottom: 16px;
		padding: 8px;
		background-color: var(--color-background-plane-3);

		.selected {
			color: var(--color-action-default-able-5) !important;
			background: var(--color-feedback-info-1) !important;
		}
	}

	.date-range-label {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 12px;
		@extend .typography-overline-2;
		color: var(--color-typography-default-text);
	}

	.calendars {
		display: flex;
		justify-content: space-around;
		flex-direction: row;

		ds3-diviser {
			height: auto;
		}

		.cal {
			width: 317px;
			height: 355px;

			.cal-header {
				@extend .typography-overline-2;
			}
		}
	}
}

.mat-dialog-actions {
	padding: 33px 0 8px 0 !important;
	margin-bottom: 0px !important;
}

.bg-fundo {
	opacity: 0%;
}

.ranged {
	background-color: var(--color-action-default-able-1);
}

.range-start {
	background-color: var(--color-action-default-able-1);
	border-radius: 50% 0 0 50%;
}

.range-end {
	background-color: var(--color-action-default-able-1);
	border-radius: 0 50% 50% 0;
}

.ds3-mat-dialog-actions {
	display: flex;
	width: 100%;
	justify-content: flex-end;
	align-items: center;
	flex-direction: row;
	gap: 8px;
}

//ESTILOS DA HORA
.ds3-timepicker {
	.timepicker__header {
		padding: 16px !important;
		width: 100% !important;

		[ng-reflect-format="12"] .timepicker-dial__control {
			width: 60px !important;
		}

		.timepicker-dial {
			display: flex;
			flex-direction: column-reverse;
			align-items: baseline;

			.info {
				@extend .typography-overline-2;
				color: var(--color-action-default-disable-2) !important;

				i {
					padding-right: 4px;
				}
			}

			.timepicker-dial__container {
				width: 100%;
				justify-content: space-around;
				margin-top: 16px;

				.timepicker-dial__time {
					@extend .typography-overline-2;
					color: var(--color-action-default-disable-2) !important;

					span {
						padding: 10px 8px !important;
					}
				}

				.timepicker-dial__period {
					.timepicker-period {
						width: 91px;
						display: flex;
						align-items: center;
						flex-direction: row;
						background-color: #fafafa;
						gap: 8px;
						padding: 4px 6px;

						.timepicker-dial__item_active {
							background-color: var(--color-feedback-info-1);
							color: var(--color-action-default-able-5);
						}

						button {
							@extend .typography-button-default-2;
							color: var(--color-action-default-able-4);
							font-size: 12px;
							padding: 8px 10px;
						}
					}

					&.timepicker-dial__period--hidden {
						display: none;
					}
				}

				.timepicker-dial__control {
					@extend .typography-body-1;
					width: 118px;
					height: 40px !important;
					padding: 8px 16px 8px 16px !important;
					border-radius: 8px !important;
					gap: 16px !important;
					border: 1px solid #1e60fa !important;
					color: var(--color-action-default-disable-2) !important;
					text-align: center;
				}
			}
		}
	}

	.timepicker__actions {
		display: flex !important;
		align-items: center !important;
		justify-content: space-between !important;
	}
}

.ds3-datepicker .mat-calendar-table-header {
	height: initial;
}

:not(.range-start):not(.range-end).mat-calendar-body-active {
	background-color: var(--color-support-gray-2);
	border-radius: 50%;
}
