import { Pipe, PipeTransform } from "@angular/core";
import { Ds3SelectOptionModel } from "../ds3-select.component";

@Pipe({
	name: "findOptionNameBasedOnValue",
})
export class Ds3SelectFindOptionNameBasedOnValuePipe implements PipeTransform {
	transform(
		value: any,
		options: any[],
		valueKey: string,
		nameKey: string,
		useValueAsObject?: boolean
	): string {
		const foundOption = options.find((x) => {
			if (useValueAsObject) {
				return x[valueKey] === value[valueKey];
			}
			return x[valueKey] === value;
		});
		if (!foundOption) {
			return "";
		}

		return foundOption[nameKey];
	}
}
