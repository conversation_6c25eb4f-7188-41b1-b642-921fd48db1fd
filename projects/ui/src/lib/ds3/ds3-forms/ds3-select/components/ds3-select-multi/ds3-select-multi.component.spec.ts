import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3SelectMultiComponent } from "./ds3-select-multi.component";

describe("Ds3SelectMultiComponent", () => {
	let component: Ds3SelectMultiComponent;
	let fixture: ComponentFixture<Ds3SelectMultiComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3SelectMultiComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3SelectMultiComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
