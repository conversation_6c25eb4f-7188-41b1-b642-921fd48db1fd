import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3SelectArrowComponent } from "./ds3-select-arrow.component";

describe("Ds3SelectArrowComponent", () => {
	let component: Ds3SelectArrowComponent;
	let fixture: ComponentFixture<Ds3SelectArrowComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3SelectArrowComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3SelectArrowComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
