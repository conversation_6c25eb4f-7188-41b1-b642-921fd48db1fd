import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { Ds3ButtonComponent } from "../../../../ds3-button/ds3-button.component";

@Component({
	selector: "ds3-select-arrow",
	templateUrl: "./ds3-select-arrow.component.html",
	styleUrls: ["./ds3-select-arrow.component.scss"],
})
export class Ds3SelectArrowComponent implements OnInit {
	@Input()
	public isOpen: boolean;
	@Input()
	public remove: boolean;
	@ViewChild("arrowButton", { static: true }) arrowButton: Ds3ButtonComponent;

	@Input()
	set triggerFocus(val: boolean) {
		if (val && this.arrowButton && this.arrowButton.elementRef) {
			setTimeout(() => this.arrowButton.elementRef.nativeElement.focus(), 0);
		}
	}
	constructor() {}

	ngOnInit() {}
}
