# Select

**Seletor:** `ds3-select`

## Uso Básico

```html
<ds3-form-field>
  <ds3-select
    ds3Input
    [formControl]="formControl"
    [options]="[{value: 'valor', name: 'Nome'}]"
  ></ds3-select>
</ds3-form-field>
```

`<ds3-select>` é um FormControl para selecionar um valor de uma lista de opções, similar ao componente `<select>` nativo.

Este componente foi projetado para funcionar dentro de um elemento `<ds3-form-field>`. Portanto deve ser usada a diretiva `<ds3-select ds3Input>` ao referenciar o componente.

Para adicionar opções, o `<ds3-select>` recebe um input `[options]=""`, que deve ser um array de objetos com o formato `[{value: '', name: ''}]`. Caso o as chaves do objeto sejam diferente, essas devem ser passadas através dos inputs `valueKey` e `nameKey`.

O componente pode receber dados de formularios Template Driven e Reactive Forms.

### Exemplo

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  select = new FormControl()
  options = [
    {value: 'value_1', name: 'This is the Value 1'},
    {value: 'value_2', name: 'Value 2'}
  ]
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-form-field>
  <ds3-field-label>Select</ds3-field-label>
  <ds3-select-multi
    [formControl]="select"
    [options]="options"
    ds3Input>
  </ds3-select-multi>
  <ds3-helper-message>
    This is the helper
  </ds3-helper-message>
</ds3-form-field>

```
&nbsp;  

## Inputs

| Property                                               | Valor padrão         | Descrição                                                                         |
|---                                                     |                   ---|                                                                                   |
| @Input() placeholder: string                           | 'Selecione um item'  | Texto de espaço reservado do input                                                |
| @Input() options: Array<any>                           | []                   | Array de objetos com as opções do select                                          |
| @Input() valueKey: string                              | value                | Chave customizada para setar o valor do objeto do options                         |
| @Input() nameKey: string                               | name                 | Chave customizada para setar o nome do objeto do options                          |
| @Input() disabled: boolean                             | false                | Define se o estado do select é disabled (usado apenas em template driven forms)   |
| @Input() endpointUrl                                   |                      | Endpoint de onde vêm os dados do options                                          |
| @Input() paramBuilder: Ds3SelectFilterParamBuilder;    |                      | Função de callback para construção dos parâmetros da requisição                   |
| @Input() responseParser: Ds3SelectFilterResponseParser;|                      | Função de callback para transformar os dados da resposta da requisição            |
| @Input() additionalFilters: any;                       |                      |                                                                                   |
| @Input() initOption: any;                              |                      |                                                                                   |
| @Input() addEmptyOption = false;                       | false                | Adiciona uma opção vazia no primeiro item do options                              |
| @Input() useFullOption = false;                        | false                | Por padrão, o valor salvo é apenas o 'value' do objeto de Options. se useFullOption for true, será salvo o objeto completo com name e value. | 


## Outputs

| Property                                     | Descrição                                                                     |
|---                                           |                                                                            ---|
| @Output() valueChanges = new EventEmitter(); | Notifica os componentes pais sobre alterações nos valores do componente atual |


## More info

No more info.

&nbsp;  

# Select Multi

**Seletor:** `ds3-select-multi`

## Uso Básico

```html
<ds3-form-field>
  <ds3-select-multi
    ds3Input
    [formControl]="formControl"
    [options]="[{value: 'valor', name: 'Nome'}]"
  ></ds3-select-multi>
</ds3-form-field>
```

`<ds3-select-multi>` é um FormControl para selecionar múltiplos valores de uma lista de opções, similar ao componente `<select multiple>` nativo.

Este componente foi projetado para funcionar dentro de um elemento `<ds3-form-field>`. Portanto deve ser usada a diretiva `<ds3-select-multi ds3Input>` ao referenciar o componente.

Para adicionar opções, o `<ds3-select-multi>` recebe um input `[options]=""`, que deve ser um array de objetos com o formato `[{name: '', label: ''}]`.

O componente pode receber dados de formularios Template Driven e Reactive Forms.

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  select = new FormControl()
  options = [
    {value: 'value_1', name: 'This is the Value 1'},
    {value: 'value_2', name: 'Value 2'}
  ]
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-form-field>
  <ds3-field-label>Select</ds3-field-label>
  <ds3-select-multi
    [formControl]="select"
    [options]="options"
    ds3Input>
  </ds3-select-multi>
  <ds3-helper-message>
    This is the helper
  </ds3-helper-message>
</ds3-form-field>

```
&nbsp;  

## Props / Inputs

| Property | Valor padrão | Descrição |
|--- | ---| |
| @Input() placeholder: string | 'Selecione um item' | Texto de espaço reservado do input |
| @Input() options: Array<any> | [] | Array de objetos com as opções do select |
| @Input() valueKey: string | value | Chave customizada para setar o valor do objeto do options |
| @Input() nameKey: string | name | Chave customizada para setar o nome do objeto do options |
| @Input() disabled: boolean | false | Define se o estado do select é disabled (usado apenas em template driven forms) |
| @Input() endpointUrl | | Endpoint de onde vêm os dados do options |
| @Input() paramBuilder: Ds3SelectFilterParamBuilder; | | Função de callback para construção dos parâmetros da requisição |
| @Input() responseParser: Ds3SelectFilterResponseParser;| | Função de callback para transformar os dados da resposta da requisição |
| @Input() additionalFilters: any; | | Filtros adicionais que podem ser aplicados à requisição |
| @Input() initOption: any; | | Opção inicial a ser selecionada no dropdown |
| @Input() addEmptyOption = false; | false | Adiciona uma opção vazia no primeiro item do options |
| @Input() useFullOption = false; | false | Por padrão, o valor salvo é apenas o 'value' do objeto de options. Se useFullOption for true, será salvo o objeto completo com name e value. |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|

## More info

No more info.
