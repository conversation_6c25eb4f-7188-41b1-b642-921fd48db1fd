// @ts-ignore
import Notes from "./ds3-select.notes.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../../ds3.module";
import { Ds3FormFieldModule } from "../ds3-form-field/ds3-form-field.module";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";

export default {
	title: "Design System 3 | Inputs/Select",
	decorators: [
		moduleMetadata({
			imports: [
				Ds3Module,
				FormsModule,
				Ds3FormFieldModule,
				ReactiveFormsModule,
			],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>{{label}}</ds3-field-label>
					<ds3-select
						ds3Input
						formControlName="select"
						[placeholder]="placeholder"
						[options]="options"
						[valueKey]="valueKey"
						[nameKey]="nameKey"
						[useFullOption]="useFullOption"
					>
					</ds3-select>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			select: new FormControl(),
		}),
		valueKey: "codigo",
		nameKey: "nome",
		options: [
			{ codigo: "value_1", nome: "Value 1" },
			{ codigo: "value_2", nome: "Value 2" },
			{ codigo: "value_3", nome: "Value 3" },
			{ codigo: "value_4", nome: "Value 4" },
			{ codigo: "value_5", nome: "Value 5" },
			{ codigo: "value_55", nome: "Value 55" },
		],
		label: text("Label:", "Select"),
		placeholder: text("Placeholder:", "Selecione uma opção"),
		useFullOption: boolean("Usar objeto completo:", false),
	},
});

export const single = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>{{label}}</ds3-field-label>
					<ds3-select
						ds3Input
						formControlName="select"
						[options]="options"
					>
					</ds3-select>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			select: new FormControl(),
		}),
		options: [
			{ value: "value_1", name: "Value 1" },
			{ value: "value_2", name: "Value 2" },
			{ value: "value_3", name: "Value 3" },
			{ value: "value_4", name: "Value 4" },
			{ value: "value_5", name: "Value 5" },
			{ value: "value_55", name: "Value 55" },
		],
		label: text("Label:", "Select"),
	},
});

export const multiple = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>{{label}}</ds3-field-label>
					<ds3-select-multi
						ds3Input
						formControlName="select"
						[options]="options"
					>
					</ds3-select-multi>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			select: new FormControl("value_55"),
		}),
		options: [
			{ value: "value_1", name: "Value 1" },
			{ value: "value_2", name: "Value 2" },
			{ value: "value_3", name: "Value 3" },
			{ value: "value_4", name: "Value 4" },
			{ value: "value_5", name: "Value 5" },
			{ value: "value_55", name: "Value 55" },
		],
		label: text("Label:", "Select"),
	},
});

export const multipleDisabled = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>Select</ds3-field-label>
					<ds3-select-multi
						ds3Input
						formControlName="select"
						[options]="options"
					>
					</ds3-select-multi>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			select: new FormControl({
				value: ["value_55", "value_5"],
				disabled: true,
			}),
		}),
		options: [
			{ value: "value_1", name: "Value 1" },
			{ value: "value_2", name: "Value 2" },
			{ value: "value_3", name: "Value 3" },
			{ value: "value_4", name: "Value 4" },
			{ value: "value_5", name: "Value 5" },
			{ value: "value_55", name: "Value 55" },
		],
	},
});

export const singleDisabled = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>Select</ds3-field-label>
					<ds3-select
						ds3Input
						formControlName="select"
						[options]="options"
					>
					</ds3-select>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			select: new FormControl({ value: "value_55", disabled: true }),
		}),
		options: [
			{ value: "value_1", name: "Value 1" },
			{ value: "value_2", name: "Value 2" },
			{ value: "value_3", name: "Value 3" },
			{ value: "value_4", name: "Value 4" },
			{ value: "value_5", name: "Value 5" },
			{ value: "value_55", name: "Value 55" },
		],
	},
});

export const templateDriven = () => ({
	template: `
		<div style="padding: 16px;">
				<ds3-form-field>
					<ds3-field-label>Select</ds3-field-label>
					<ds3-select
						ds3Input
						[(ngModel)]="select"
						[options]="options"
					>
					</ds3-select>
				</ds3-form-field>
		</div>
    `,
	props: {
		select: "value_55",
		options: [
			{ value: "value_1", name: "Value 1" },
			{ value: "value_2", name: "Value 2" },
			{ value: "value_3", name: "Value 3" },
			{ value: "value_4", name: "Value 4" },
			{ value: "value_5", name: "Value 5" },
			{ value: "value_55", name: "Value 55" },
		],
	},
});
