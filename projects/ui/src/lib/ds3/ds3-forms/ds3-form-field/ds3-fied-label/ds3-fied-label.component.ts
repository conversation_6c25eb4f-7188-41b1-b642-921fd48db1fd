import {
	Component,
	ElementRef,
	OnInit,
	Renderer2,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "ds3-field-label",
	templateUrl: "./ds3-fied-label.component.html",
	styleUrls: ["./ds3-fied-label.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3FieldLabelComponent implements OnInit {
	@ViewChild("label", { static: true })
	private readonly label: ElementRef<HTMLLabelElement>;

	constructor(
		private readonly elementRef: ElementRef<HTMLOptionElement>,
		private readonly renderer2: Renderer2
	) {}

	public ngOnInit() {
		this.renderer2.addClass(this.elementRef.nativeElement, "ds3-field-label");
	}

	public setForAttribute(value: string) {
		this.label.nativeElement.setAttribute("for", value);
	}
}
