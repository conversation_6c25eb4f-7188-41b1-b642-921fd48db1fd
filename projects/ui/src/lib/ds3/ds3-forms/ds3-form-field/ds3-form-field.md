# Form Field

`<ds3-form-field>` é um componente usado para envolver campos e aplicar estilos e comportamentos, como prefixos, sufixos e mensagens de dica.

## Uso Básico

Você pode criar formulários de duas maneiras:

- Reactive forms: usam instâncias existentes de um FormControl ou FormGroup para construir um modelo de formulário.
- Template-driven forms: dependem de diretivas como NgModele NgModelGroup para criam o modelo de formulário para você.

O uso básico do componente `<ds3-form-field>` envolve a criação de um campo de entrada de texto. Aqui está um exemplo usando Reactive forms:

- Importe o módulo `Ds3FormsModule`:

```ts
import { ReactiveFormsModule } from '@angular/forms';
import { Ds3FormsModule } from 'ui-kit';

@NgModule({
	declarations: [...],
	imports: [
		...
		Ds3FormsModule,
		ReactiveFormsModule, // ou FormsModule caso opte pelo método Template-driven forms
		...
	],
})
class MeuAppModule {}
```

- Em seu componente:

```ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
	selector: 'pacto-meu-app',
	templateUrl: './meu-app.component.html',
	styleUrls: ['./meu-app.component.scss'],
})
export class MeuAppComponent implements OnInit {
	public form: FormGroup;

	constructor(private readonly fb: FormBuilder) {}

	ngOnInit() {
		this.form = this.fb.group(
			{
				// ...
				control: this.fb.control(null, [Validators.required]),
				// ...
			},
			{ updateOn: 'blur' } // O { updateOn: 'blur' } define que as atualizações no FormControl ocorrerão somente no evento 'blur' (quando o campo perde o foco).
		);
	}

	public salvar(): void {
		// Marca todos os controles como "touched" (tocado)
		this.form.markAllAsTouched();
		// Percorre todos os controles no formulário.
		Object.keys(this.form.getRawValue()).forEach(controlName => {
			// Atualiza o valor e a validade do controle.
			this.form.controls[controlName].updateValueAndValidity();
			// Marca o controle como "dirty" (modificado pelo usuário).
			this.form.controls[controlName].markAsDirty();
		});

		// Verifica se o formulário está válido
		if (this.form.valid) {
			console.log(this.form.value);
		}
	}
}
```

```html
<form [formGroup]="form" (submit)="salvar()">
	<ds3-form-field>
		<ds3-field-label>Label</ds3-field-label>
		<input type="text" ds3Input formControlName="control" />
	</ds3-form-field>
</form>
```

Neste exemplo:

- `<ds3-field-label>` é usado para definir a label.
- O campo de entrada `input` requer a diretiva `ds3Input` e `formControlName` (ou `formControl`). Por padrão, ele utiliza `type="text"`, mas também suporta vários outros tipos, como tel, email, url, password e outros. Alguns desses tipos possuem estilos diferentes e podem usar elementos adicionais em combinação. É importante observar que nem todos esses tipos são suportados pelo Design System, mas, se utilizados, serão exibidos de forma visualmente apropriada.
- O updateOn: 'blur' no FormGroup é usado para especificar que as atualizações no FormControl ocorrerão somente quando o campo perder o foco (evento 'blur'). Isso é útil para melhorar a eficiência do formulário, evitando validações e atualizações desnecessárias enquanto o usuário digita. É o valor recomendado para a maioria dos casos de uso.
- O método salvar é chamado quando o formulário é submetido e tem a responsabilidade de validar e marcar os campos como sujos (dirty). Ele percorre todos os controles no formulário, atualiza seus valores e validade, e os marca como "dirty" para indicar que foram modificados pelo usuário. Isso é útil para controlar o estado dos campos e pode ser personalizado de acordo com os requisitos do seu aplicativo.

## Prefixo e Sufixo

Você pode adicionar prefixos e sufixos aos campos, se necessário. Use as diretivas `ds3Prefix` e `ds3Suffix` em elementos nativos ou componentes Angular. Aqui está um exemplo:

```html
<ds3-form-field>
	<ds3-field-label>Text</ds3-field-label>
	<div ds3Prefix>prefixo</div>
	<input type="text" ds3Input formControlName="control" />
	<div ds3Suffix>sufixo</div>
</ds3-form-field>
```

Exemplo de sufixo com ícone de status:

```html
<ds3-form-field>
	<ds3-field-label>Text</ds3-field-label>
	<input type="text" ds3Input formControlName="control" />
	<i *ngIf="form.controls['control'].dirty && form.controls['control'].valid" ds3Suffix class="pct pct-check-circle"></i>
	<i *ngIf="form.controls['control'].dirty && form.controls['control'].invalid" ds3Suffix class="pct pct-alert-triangle"></i>
</ds3-form-field>
```
## Máscaras


O input é independente, permitindo ao desenvolvedor a liberdade de escolher a abordagem para a criação da máscara. Abaixo, apresentamos um exemplo de validação específica para o tipo "tel".

- Em seu componente:

```ts
	public validaTelf(evt) {
		let tel = evt.target.value;
		tel = tel.replace(/\D/g, '');
		tel = tel.slice(0, 11);
		tel = tel.replace(/(\d{2})(\d{1})(\d{4})(\d{4})/, '($1) $2$3-$4');
		this.form.get('control').setValue(tel);
	}
```
```html
	<input type="tel" ds3Input formControlName="control" (keyup)="validaTelf($event)"  />
```

## Campos de Pesquisa com Datalist

Para criar campos de pesquisa com um datalist, use o componente `ds3Datalist` e defina o input com o tipo `search`. Aqui está um exemplo:

```html
<ds3-form-field>
	<ds3-field-label>Aluno</ds3-field-label>
	<input type="search" ds3Input formControlName="control" />
	<datalist ds3Datalist>
		<option ds3DatalistOption value="Florisberta Catupiry da Silva"></option>
		<option ds3DatalistOption value="Djoni Clevertson da Silva"></option>
		<option ds3DatalistOption value="Regislaine Florisberto da Silva"></option>
		<option ds3DatalistOption value="Rosicléia Regilene da Silva"></option>
	</datalist>
</ds3-form-field>
```

## Preenchimento Dinâmico do Datalist

Você pode preencher dinamicamente as opções do datalist com base no que o usuário digitar. Aqui está um exemplo:

```html
<ds3-form-field>
	<ds3-field-label>Search</ds3-field-label>
	<input type="search" ds3Input formControlName="control" (keyup)="filtrar($event)" />
	<i *ngIf="form.controls['control'].pending" ds3Suffix class="pct pct-loader"></i>
	<datalist ds3Datalist>
		<option ds3DatalistOption *ngFor="let option of listaFiltrada" [value]="option"></option>
	</datalist>
</ds3-form-field>
```

## Mensagem de Ajuda

Você pode adicionar uma mensagem de ajuda usando o componente `<ds3-helper-message>`. Aqui está um exemplo:

```html
<ds3-form-field>
	<ds3-field-label>URL</ds3-field-label>
	<input type="url" ds3Input formControlName="control" />
	<ds3-helper-message>Helper message</ds3-helper-message>
</ds3-form-field>
```

Você pode querer mensagens para diferentes casos, eis aqui um exemplo:

```html
<ds3-form-field>
	<ds3-field-label>Email</ds3-field-label>
	<input type="email" ds3Input formControlName="control" />
	<ds3-helper-message *ngIf="!form.controls['control'].touched">
		Seu email mais usado
	</ds3-helper-message>
	<ds3-helper-message *ngIf="form.controls['control'].touched && form.controls['control'].hasError('required')">
		É obrigatório preencher este campo
	</ds3-helper-message>
	<ds3-helper-message *ngIf="form.controls['control'].dirty && form.controls['control'].hasError('email')">
		Email inválido
	</ds3-helper-message>
	<ds3-helper-message *ngIf="form.controls['control'].dirty && form.controls['control'].valid">
		Validado
	</ds3-helper-message>
</ds3-form-field>
```

## Textarea

Aqui está um campo de `textarea`:

```html
<ds3-form-field>
	<ds3-field-label>Textarea</ds3-field-label>
	<textarea ds3Input maxlength="20" formControlName="control" placeholder="Este é um placeholder" rows="5"></textarea>
</ds3-form-field>
```

## Atributos nativos

O componente `<ds3-form-field>` utiliza inputs nativos em sua implementação. Isso significa que você pode aproveitar todos os atributos permitidos pelos elementos de entrada HTML. Isso oferece uma grande flexibilidade ao personalizar seus campos de formulário. Abaixo estão alguns exemplos de atributos que podem ser usados:

### Exemplo de Input Text com Atributos Adicionais

```html
<ds3-form-field>
	<ds3-field-label>Nome</ds3-field-label>
	<input
		type="text"
		ds3Input
		formControlName="nome"
		placeholder="Insira seu nome"
		minlength="3"
		maxlength="50"
		autofocus
		readonly
	/>
</ds3-form-field>
```

Porém alguns atributos não são recomendados de se usar em conjunto com o Angular Forms.

Prefira:

```ts
this.form = this.fb.group({
	nome: this.fb.control({ value: null, disabled: true }, Validators.required),
});

// ou

this.form.get('nome').disable();
```

no lugar de:

```html
<ds3-form-field>
	<ds3-field-label>Nome</ds3-field-label>
	<input type="text" ds3Input formControlName="nome" disabled />
</ds3-form-field>
```

## Personalização

O componente `<ds3-form-field>` permite a personalização de vários aspectos visuais por meio dos tokens CSS. Você pode ajustar os estilos do `ds3-form-field` de acordo com as necessidades do seu projeto. Abaixo estão os tokens CSS disponíveis para personalização:

| Token                                   | Descrição                                                     | Valor Padrão                       |
| --------------------------------------- | ------------------------------------------------------------- | ---------------------------------- |
| --form-field-content-spacing-vertical   | Espaçamento vertical interno do conteúdo.                     | 0.5625rem                          |
| --form-field-content-spacing-horizontal | Espaçamento horizontal interno do conteúdo.                   | 1rem                               |
| --form-field-border-radius              | Raio dos cantos.                                              | 0.5rem                             |
| --form-field-input-color                | Cor do texto de entrada.                                      | --color-support-black-3            |
| --form-field-placeholder-color          | Cor do texto de orientação (placeholder).                     | --color-typography-default-text    |
| --form-field-border-color-default       | Cor da borda no estado padrão.                                | --color-support-gray-3             |
| --form-field-border-color-selected      | Cor da borda no estado selecionado.                           | --color-action-default-able-5      |
| --form-field-border-color-success       | Cor da borda no estado de sucesso.                            | --color-feedback-gain-2            |
| --form-field-border-color-error         | Cor da borda no estado de erro.                               | --color-action-default-risk-5      |
| --form-field-helper-color-selected      | Cor da mensagem de ajuda no estado selecionado.               | --form-field-border-color-selected |
| --form-field-helper-color-success       | Cor da mensagem de ajuda no estado de sucesso.                | --form-field-border-color-success  |
| --form-field-helper-color-error         | Cor da mensagem de ajuda no estado de erro.                   | --form-field-border-color-error    |
| --form-field-disabled-background        | Cor de fundo quando está desabilitado.                        | --color-action-default-disable-1   |
| --form-field-disabled-cursor            | Estilo do cursor quando o `ds3-form-field` está desabilitado. | default                            |
| --form-field-label-color                | Cor do rótulo no input                                        | --color-typography-default-text    |
| --form-field-label-adjacent-spacing     | Espaço adjacente ao rótulo                                    | 0.563rem                           |

### Exemplo de uso

Em seu componente que usará o FormField adicione as seguintes linhas para definir a cor de fundo e o cursor personalizado no modo desabilitado:

```css
ds3-form-field {
	--form-field-disabled-background: #333;
	--form-field-disabled-cursor: not-allowed;
}
```

Esses tokens CSS permitem personalizar diversos aspectos visuais do componente `ds3-form-field`. Lembre-se de substituir os valores nos exemplos acima pelos valores reais desejados para personalização.
