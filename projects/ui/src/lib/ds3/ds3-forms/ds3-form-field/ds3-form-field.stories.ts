// @ts-ignore
import Notes from "./ds3-form-field.md";
import { moduleMetadata } from "@storybook/angular";
import {
	boolean,
	number,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import "./index.scss";
import { Ds3FormFieldModule } from "./ds3-form-field.module";

import { Ds3Module } from "../../ds3.module";

export default {
	title: "Design System 3 | Inputs/Form field",
	decorators: [
		moduleMetadata({
			imports: [
				Ds3Module,
				Ds3FormFieldModule,
				FormsModule,
				ReactiveFormsModule,
			],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Text = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
            <div *ngIf="!!prefix" ds3Prefix>{{prefix}}</div>
            <input [type]="type" autofocus ds3Input [readonly]="readonly" formControlName="control" />
						<i *ngIf="form.controls['control'].dirty && form.controls['control'].valid" ds3Suffix class="pct pct-check-circle"></i>
						<i *ngIf="form.controls['control'].dirty && form.controls['control'].invalid" ds3Suffix class="pct pct-alert-triangle"></i>
            <div *ngIf="!!suffix" ds3Suffix>{{suffix}}</div>
            <ds3-helper-message *ngIf="hasHelperMessage">{{ helperMessage }}</ds3-helper-message>
          </ds3-form-field>
        </form>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		readonly: boolean("Somente leitura", false),
		label: text("Label", "Label"),
		type: select(
			"Tipo de entrada",
			["text", "email", "number", "tel", "url"],
			"text"
		),
		prefix: text("Prefixo", ""),
		suffix: text("Sufixo", ""),
		hasHelperMessage: boolean("Com mensagem de ajuda", true),
		helperMessage: text("Mensagens de dica", "Helper message"),
	},
});
export const TextArea = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
            <textarea autofocus ds3Input [maxLength]="maxlength" [readonly]="readonly" formControlName="control" rows="5"></textarea>
          </ds3-form-field>
        </form>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		readonly: boolean("Somente leitura", false),
		label: text("Label", "Label"),
		maxlength: number("Comprimento máximo", 50),
	},
});

export const Password = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
            <div *ngIf="!!prefix" ds3Prefix>{{prefix}}</div>
            <input type="password" autofocus ds3Input [readonly]="readonly" formControlName="control" />
            <div *ngIf="!!suffix" ds3Suffix>{{suffix}}</div>
          </ds3-form-field>
        </form>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		readonly: boolean("Somente leitura", false),
		label: text("Label", "Label"),
		prefix: text("Prefixo", null),
		suffix: text("Sufixo", null),
	},
});
export const Search = () => ({
	template: `
	<form [formGroup]="form" style="padding: 1rem">
          <ds3-form-field>
            <ds3-field-label *ngIf="!!label">{{ label }}</ds3-field-label>
            <input type="search" ds3Input [readonly]="readonly" formControlName="control" />
            <datalist ds3Datalist>
              <option ds3DatalistOption value="Batata">Batata</option>
              <option ds3DatalistOption value="Cebola">Cebola</option>
              <option ds3DatalistOption value="Cuscuz">Cuscuz</option>
              <option ds3DatalistOption value="Arroz">Arroz</option>
            </datalist>
          </ds3-form-field>
        </form>
    `,
	props: {
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
		readonly: boolean("Somente leitura", false),
		label: text("Label", null),
	},
});
