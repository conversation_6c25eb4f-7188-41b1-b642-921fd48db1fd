import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3FormFieldComponent } from "./ds3-form-field.component";
import { Ds3InputDirective } from "./ds3-input.directive";
import { Ds3PrefixDirective } from "./ds3-prefix.directive";
import { Ds3SuffixDirective } from "./ds3-suffix.directive";
import { Ds3DatalistComponent } from "./ds3-datalist/ds3-datalist.component";
import { Ds3DatalistOptionComponent } from "./ds3-datalist-option/ds3-datalist-option.component";
import { Ds3HelperMessageComponent } from "./ds3-helper-message/ds3-helper-message.component";
import { Ds3FieldLabelComponent } from "./ds3-fied-label/ds3-fied-label.component";

@NgModule({
	declarations: [
		Ds3FormFieldComponent,
		Ds3InputDirective,
		Ds3PrefixDirective,
		Ds3SuffixDirective,
		Ds3DatalistComponent,
		Ds3DatalistOptionComponent,
		Ds3FieldLabelComponent,
		Ds3HelperMessageComponent,
	],
	imports: [CommonModule],
	exports: [
		Ds3FormFieldComponent,
		Ds3InputDirective,
		Ds3PrefixDirective,
		Ds3SuffixDirective,
		Ds3DatalistComponent,
		Ds3DatalistOptionComponent,
		Ds3FieldLabelComponent,
		Ds3HelperMessageComponent,
	],
})
export class Ds3FormFieldModule {}
