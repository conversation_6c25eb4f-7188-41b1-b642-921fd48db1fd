@import "../../../../../../assets/ds3/typography/mixins";

option[ds3-datalist-option] {
	@include apply-typography-style("body", 1);
	color: var(--color-support-black-3, hsla(222, 5%, 30%, 1));
	padding: var(--datalist-content-spacing-vertical)
		var(--datalist-content-spacing-horizontal);
	cursor: pointer;

	&[hidden]:not([hidden="false"]) {
		display: none;
	}

	&[disabled]:not([disabled="false"]) {
		cursor: var(--datalist-disabled-cursor);
		background-color: var(--form-field-disabled-background);
	}

	&[focused]:not([focused="false"]),
	&:hover {
		color: var(--datalist-focused-color);
		background-color: var(--datalist-focused-background);
	}
}
