import { coerceBooleanProperty } from "@angular/cdk/coercion";
import {
	Component,
	ElementRef,
	EventEmitter,
	HostBinding,
	HostListener,
	Input,
	OnInit,
	Output,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "[ds3DatalistOption]",
	templateUrl: "./ds3-datalist-option.component.html",
	styleUrls: ["./ds3-datalist-option.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3DatalistOptionComponent implements OnInit {
	@Input()
	public value: any;

	@Output()
	public selected: EventEmitter<any> = new EventEmitter();

	private _disabled = false;

	private _hidden = false;

	private _focused = false;

	constructor(
		public readonly elementRef: ElementRef<HTMLOptionElement>,
		private readonly renderer2: Renderer2
	) {}

	public ngOnInit(): void {
		this.renderer2.addClass(
			this.elementRef.nativeElement,
			"ds3-datalist-option"
		);
	}

	@HostListener("click", ["$event"])
	public clickHandler(event: MouseEvent) {
		if (!this.disabled) {
			this.selected.next(this.value);
		}
	}

	public get hidden() {
		return this._hidden;
	}

	@Input()
	public set hidden(value: boolean) {
		this._hidden = coerceBooleanProperty(value);
		this.renderer2.setProperty(
			this.elementRef.nativeElement,
			"hidden",
			this._hidden
		);
	}

	public get disabled() {
		return this._disabled;
	}

	@Input()
	public set disabled(value: boolean) {
		this._disabled = coerceBooleanProperty(value);
		this.renderer2.setProperty(
			this.elementRef.nativeElement,
			"disabled",
			this._disabled
		);
	}

	@HostBinding("attr.focused")
	public set focused(value: boolean) {
		this._focused = this.disabled ? false : value;
	}

	public get focused() {
		return this._focused;
	}
}
