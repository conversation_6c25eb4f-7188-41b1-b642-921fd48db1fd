<div class="content">
	<ng-content select="ds3-field-label"></ng-content>
	<div
		(click)="displayClickHandler()"
		[ngClass]="{
			error: ngControl?.touched && ngControl?.invalid,
			default: ngControl?.pristine,
			success:
				ngControl?.touched && ngControl?.valid && ngControl?.control.validator
		}"
		class="input-area">
		<div *ngIf="hasPrefixes" class="prefixes">
			<ng-content select="[ds3Prefix]"></ng-content>
			<i *ngIf="inputType === 'search'" class="pct pct-search"></i>
		</div>
		<div class="infix">
			<ng-content></ng-content>
		</div>
		<div *ngIf="hasSuffixes" class="suffixes">
			<i
				(click)="seePasswordHandle($event)"
				*ngIf="typePassword"
				[ngClass]="{
					pct: true,
					'pct-eye': !seePassword,
					'pct-eye-off': seePassword
				}"
				[style.cursor]="'pointer'"></i>
			<ng-content select="[ds3Suffix]"></ng-content>
		</div>
	</div>
	<ng-content
		*ngIf="inputType === 'search' && !inputReadOnly"
		select="[ds3Datalist]"></ng-content>
	<ng-content select="ds3-helper-message"></ng-content>
	<span *ngIf="maxLength > 0" class="counter">
		{{ numberOfCharacters }}/{{ maxLength }}
	</span>
</div>
