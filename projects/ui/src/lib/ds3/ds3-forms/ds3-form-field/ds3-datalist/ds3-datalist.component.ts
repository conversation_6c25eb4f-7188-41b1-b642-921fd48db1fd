import {
	AfterContentInit,
	Component,
	<PERSON><PERSON>hildren,
	ElementRef,
	EventEmitter,
	On<PERSON><PERSON>roy,
	OnInit,
	Output,
	QueryList,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";
import { Subscription } from "rxjs";
import { Ds3DatalistOptionComponent } from "../ds3-datalist-option/ds3-datalist-option.component";

@Component({
	selector: "[ds3Datalist]",
	templateUrl: "./ds3-datalist.component.html",
	styleUrls: ["./ds3-datalist.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3DatalistComponent
	implements OnInit, AfterContentInit, OnDestroy
{
	@Output()
	public selected: EventEmitter<string> = new EventEmitter<string>();

	@ContentChildren(Ds3DatalistOptionComponent)
	private readonly options: QueryList<Ds3DatalistOptionComponent>;

	private optionSelectSubscriptions: Subscription[] = [];

	private optionListSubscription: Subscription;

	private keyboardSelectionIndex = null;

	constructor(
		public readonly elementRef: ElementRef<HTMLDataListElement>,
		private readonly renderer2: Renderer2
	) {}

	public ngOnInit(): void {
		this.renderer2.addClass(this.elementRef.nativeElement, "ds3-datalist");
	}

	public ngAfterContentInit(): void {
		this.hookUpOptionSubcriptions();
		this.optionListSubscription = this.options.changes.subscribe(() => {
			this.hookUpOptionSubcriptions();
		});
	}

	public ngOnDestroy(): void {
		this.clearOptionSelectSubcriptions();
		this.optionListSubscription.unsubscribe();
	}

	public get isOpen(): boolean {
		return this.elementRef.nativeElement.classList.contains("open");
	}

	public openDropdown(): void {
		this.renderer2.addClass(this.elementRef.nativeElement, "open");
	}

	public closeDropdown(): void {
		this.renderer2.removeClass(this.elementRef.nativeElement, "open");
	}

	public filter(term: string): void {
		if (!this.options) {
			return;
		}
		this.options.forEach((option) => {
			if (!term || option.value.toUpperCase().includes(term.toUpperCase())) {
				option.hidden = false;
			} else {
				option.hidden = true;
			}
		});

		this.keyboardSelectionIndex = null;
	}

	private clearOptionSelectSubcriptions() {
		this.optionSelectSubscriptions.forEach(async (subscription) => {
			subscription.unsubscribe();
		});
	}

	private hookUpOptionSubcriptions() {
		this.clearOptionSelectSubcriptions();
		this.optionSelectSubscriptions = this.options.map((option) => {
			return option.selected.subscribe((value) => {
				this.selected.emit(value);
			});
		});
	}

	public onKeydownHandler(event: KeyboardEvent) {
		if (event.key === "ArrowDown") {
			this.handleMoveKeyboardSelection("DOWN");
		}
		if (event.key === "ArrowUp") {
			this.handleMoveKeyboardSelection("UP");
		}

		if (event.key === "Enter") {
			if (this.isOpen && this.keyboardSelectionIndex !== null) {
				const value = this.options.toArray()[this.keyboardSelectionIndex].value;
				this.selected.emit(value);
				return;
			}
		}
	}

	private handleMoveKeyboardSelection(direction: "UP" | "DOWN") {
		const numberOfOptions = this.sizeOfOptions;
		const lastIndex = numberOfOptions - 1;
		const currentIndex = this.keyboardSelectionIndex;

		if (currentIndex === null) {
			const index = direction === "DOWN" ? 0 : lastIndex;
			const option = this.options.toArray()[index];
			if (option.hidden || option.disabled) {
				this.keyboardSelectionIndex =
					direction === "DOWN" ? index + 1 : index - 1;
				this.handleMoveKeyboardSelection(direction);
			} else {
				this.setKeyboardSelection(index);
			}
			return;
		}

		if (direction === "UP") {
			const negativeOverflow = currentIndex <= 0;
			let nextIndex = negativeOverflow ? lastIndex : currentIndex - 1;

			const option = this.options.toArray()[nextIndex];
			if (option.hidden || option.disabled) {
				this.keyboardSelectionIndex = nextIndex;
				this.handleMoveKeyboardSelection(direction);
			} else {
				this.setKeyboardSelection(nextIndex);
			}
			return;
		}

		if (direction === "DOWN") {
			const positiveOverflow = currentIndex === lastIndex;
			let nextIndex = positiveOverflow ? 0 : currentIndex + 1;

			const option = this.options.toArray()[nextIndex];
			if (option.hidden || option.disabled) {
				this.keyboardSelectionIndex = nextIndex;
				this.handleMoveKeyboardSelection(direction);
			} else {
				this.setKeyboardSelection(nextIndex);
			}
			return;
		}
	}

	private setKeyboardSelection(index) {
		this.keyboardSelectionIndex = index;
		this.options.forEach((option, optionIndex) => {
			option.focused = index === optionIndex;
		});
	}

	public get sizeOfOptions(): number {
		const map = this.options.toArray().filter((option) => !option.hidden);
		return map.length;
	}
}
