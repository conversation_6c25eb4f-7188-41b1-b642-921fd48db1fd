import {
	Component,
	ElementRef,
	OnInit,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "ds3-helper-message",
	templateUrl: "./ds3-helper-message.component.html",
	styleUrls: ["./ds3-helper-message.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3HelperMessageComponent implements OnInit {
	constructor(
		public readonly elementRef: ElementRef<HTMLElement>,
		private readonly renderer2: Renderer2
	) {}

	public ngOnInit(): void {
		this.renderer2.addClass(
			this.elementRef.nativeElement,
			"ds3-helper-message"
		);
	}
}
