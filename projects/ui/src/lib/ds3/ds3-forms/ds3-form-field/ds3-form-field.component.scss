@import "../../../../../assets/ds3/typography/mixins";
@import "../../../../../assets/ds3/colors.var.scss";
@import "../../../../../assets/ui-kit.scss";

:host {
	--form-field-content-spacing-vertical: 8px;
	--form-field-content-spacing-horizontal: 1rem;
	--form-field-border-radius: 0.5rem;
	--form-field-input-color: var(--color-support-black-3, hsla(222, 5%, 30%, 1));
	--form-field-placeholder-color: var(
		--color-typography-default-text,
		hsla(222, 5%, 50%, 1)
	);
	--form-field-border-color-default: var(
		--color-support-gray-3,
		hsla(222, 4%, 80%, 1)
	);
	--form-field-border-color-selected: var(
		--color-action-default-able-5,
		hsla(222, 95%, 45%, 1)
	);
	--form-field-border-color-success: var(
		--color-feedback-gain-2,
		hsla(120, 95%, 35%, 1)
	);
	--form-field-border-color-error: var(
		--color-action-default-risk-5,
		hsla(0, 95%, 45%, 1)
	);
	--form-field-helper-color-selected: var(--form-field-border-color-selected);
	--form-field-helper-color-success: var(--form-field-border-color-success);
	--form-field-helper-color-error: var(--form-field-border-color-error);
	--form-field-helper-message-color: var(
		--color-typography-default-text,
		hsla(222, 5%, 50%, 1)
	);
	--form-field-background-color: $branco;
	--form-field-disabled-background: var(
		--color-action-default-disable-1,
		hsla(222, 6%, 90%, 1)
	);
	--form-field-disabled-cursor: default;
	--form-field-label-color: var(
		--color-typography-default-text,
		hsla(222, 5%, 50%, 1)
	);
	--form-field-label-adjacent-spacing: var(--spacing-1, 0.5rem);

	.content {
		display: grid;
		--size-label: calc(
			(
					var(--typography-title-4-line-height) / 100 *
						var(--typography-title-4-font-size)
				) + var(--form-field-label-adjacent-spacing)
		);
		--size-helper-message: calc(
			var(--typography-overline-2-line-height) / 100 *
				var(--typography-overline-2-font-size)
		);
		grid-template-rows: var(--size-label) minmax(2.5rem, auto) var(
				--size-helper-message
			);

		&:not(.has-counter) {
			grid-template-areas:
				"label"
				"input-area"
				"helper-message";
		}

		&.has-counter {
			grid-template-columns: 1fr auto;
			grid-template-areas:
				"label label"
				"input-area input-area"
				"helper-message counter";

			.counter {
				grid-area: counter;
				color: var(--color-action-default-able-4, hsla(222, 95%, 55%, 1));
				@include apply-typography-style("overline", 2);
			}
		}

		&.has-datalist {
			position: relative;
		}

		.input-area {
			grid-area: input-area;
			display: grid;
			align-items: center;

			&.type-date,
			&.type-datetime-local,
			&.type-email,
			&.type-month,
			&.type-number,
			&.type-password,
			&.type-range,
			&.type-search,
			&.type-tel,
			&.type-text,
			&.type-time,
			&.type-url,
			&.type-week,
			&.type-textarea,
			&.type-select {
				height: 40px;
				border-width: 1px;
				border-style: solid;
				border-color: var(--form-field-border-color-default);
				background-color: #fff;
				transition-duration: 200ms;
				transition-timing-function: ease-in-out;
				transition-property: border-color;
				cursor: text;
				padding: var(--form-field-content-spacing-vertical)
					var(--form-field-content-spacing-horizontal);
				border-radius: var(--form-field-border-radius);

				.infix {
					display: flex;

					::ng-deep {
						input.number-input {
							color: $typeDefaultText;

							&:focus {
								color: $supportBlack03;
							}
						}

						input:not(.ds3-select-multiple-search),
						textarea {
							cursor: inherit;
							flex-shrink: 0;
							width: 100%;
							border: none;
							padding: 0;
							outline-style: none;
							background-color: transparent;
							color: var(--form-field-input-color);
							@extend .pct-body1;

							&::placeholder {
								color: var(--form-field-placeholder-color);
							}

							input,
							textarea {
								cursor: inherit;
								flex-shrink: 0;
								width: 100%;
								border: none;
								padding: 0;
								outline-style: none;
								background-color: inherit;
								line-height: 20px;
								color: var(--form-field-input-color);
								@include apply-typography-style("body", 1);

								&::placeholder {
									color: var(--form-field-placeholder-color);
								}
							}
						}
					}
				}
			}

			&.type-select {
				cursor: pointer;
				padding: 0;

				&.success {
					border-color: var(--form-field-border-color-selected);
				}
			}

			&.type-number {
				padding: 4px 1rem;

				&.success {
					border-color: var(--form-field-border-color-selected);
				}
			}

			&.type-textarea {
				height: initial;
			}

			&.has-prefixes_not-has-suffixes {
				grid-template-columns: auto 1fr;
			}

			&.has-suffixes_not-has-prefixes {
				grid-template-columns: 1fr auto;
				padding: 0px 1rem;
			}

			&.has-prefixes_has-suffixes {
				grid-template-columns: auto 1fr auto;
			}

			&.selected {
				border-color: var(--form-field-border-color-selected);
			}

			&.success {
				border-color: var(--form-field-border-color-success) !important;
			}

			&.error {
				border-color: var(--form-field-border-color-error);
				::ng-deep .inputPhone {
					border-color: var(--form-field-border-color-error) !important;
				}
			}

			&.loading {
				border-color: var(--form-field-border-color-default);
				color: var(--form-field-input-color);
			}

			&.selected + .ds3-helper-message {
				color: var(--form-field-helper-color-selected);
			}

			&.success + .ds3-helper-message {
				color: var(--form-field-helper-color-success);
			}

			&.error + .ds3-helper-message {
				color: var(--form-field-helper-color-error);
			}

			&:is(.read-only, .disabled):not(.type-custom) {
				background-color: var(--form-field-disabled-background);
				border-color: var(--form-field-disabled-background);
			}

			&.disabled {
				cursor: var(--form-field-disabled-cursor);
			}

			:is(.prefixes, .suffixes) {
				display: flex;
				align-items: center;
				gap: var(--form-field-content-spacing-horizontal);

				color: var(--form-field-input-color);
				font-family: "Poppins";
				font-size: 0.625rem;
				font-weight: 500;

				i.pct {
					font-size: 1rem;
				}
			}

			.prefixes {
				margin-right: var(--form-field-content-spacing-horizontal);
			}

			.suffixes {
				margin-left: var(--form-field-content-spacing-horizontal);
			}

			&.selected :is(.prefixes, .suffixes) {
				color: var(--form-field-border-color-selected);
			}

			&.success :is(.prefixes, .suffixes) {
				color: var(--form-field-border-color-success);
			}

			&.error :is(.prefixes, .suffixes) {
				color: var(--form-field-border-color-error);
			}
		}
	}
}
