import {
	AfterContentInit,
	Component,
	Content<PERSON>hild,
	Content<PERSON><PERSON><PERSON>n,
	<PERSON>ement<PERSON><PERSON>,
	HostListener,
	OnInit,
	Query<PERSON>ist,
	Renderer2,
} from "@angular/core";
import { AbstractControl, NgControl } from "@angular/forms";
import { Ds3SuffixDirective } from "./ds3-suffix.directive";
import { Ds3PrefixDirective } from "./ds3-prefix.directive";
import { Ds3InputDirective } from "./ds3-input.directive";
import { Ds3DatalistComponent } from "./ds3-datalist/ds3-datalist.component";
import { Ds3FieldLabelComponent } from "./ds3-fied-label/ds3-fied-label.component";
import { Ds3SelectComponent } from "../ds3-select/ds3-select.component";
import { Ds3SelectMultiComponent } from "../ds3-select/components/ds3-select-multi/ds3-select-multi.component";

enum InputStatus {
	<PERSON><PERSON>ult = "default",
	Selected = "selected",
	Success = "success",
	Error = "error",
}

@Component({
	selector: "ds3-form-field",
	templateUrl: "./ds3-form-field.component.html",
	styleUrls: ["./ds3-form-field.component.scss"],
})
export class Ds3FormFieldComponent implements OnInit, AfterContentInit {
	private static sequence = 0;

	@ContentChild(NgControl, { static: true })
	public readonly ngControl: NgControl;

	@ContentChild(Ds3FieldLabelComponent, { static: true })
	private readonly fieldLabel: Ds3FieldLabelComponent;

	@ContentChildren(Ds3SuffixDirective)
	private readonly suffixes: QueryList<Ds3SuffixDirective>;

	@ContentChildren(Ds3PrefixDirective)
	private readonly prefixes: QueryList<Ds3PrefixDirective>;

	@ContentChild(Ds3InputDirective, { static: true })
	public readonly inputDiretive: Ds3InputDirective;

	@ContentChild(Ds3DatalistComponent, { static: true })
	public readonly dataListComponent: Ds3DatalistComponent;

	@ContentChild(Ds3SelectComponent, { static: true })
	public readonly ds3SelectComponent: Ds3SelectComponent;

	@ContentChild(Ds3SelectMultiComponent, { static: true })
	public readonly ds3SelectMultiComponent: Ds3SelectMultiComponent;

	public currentInputStatus: string;

	public numberOfCharacters: number = 0;

	public typePassword: boolean = false;

	public seePassword: boolean = false;

	constructor(
		public readonly elementRef: ElementRef<HTMLElement>,
		private readonly renderer2: Renderer2
	) {}

	public ngOnInit(): void {
		this.renderer2.addClass(this.elementRef.nativeElement, "ds3-form-field");
	}

	public ngAfterContentInit(): void {
		if (this.inputDiretive) {
			this.typePassword = this.inputDiretive.type === "password";
			this.generateAndSetId();
			this.prepareListeners();
			this.prepareStyles();
		}
	}

	private generateAndSetId(): void {
		const currentId =
			this.inputDiretive.elementRef.nativeElement.getAttribute("id");
		const id = currentId || `ds3-field-${Ds3FormFieldComponent.sequence++}`;
		this.inputDiretive.elementRef.nativeElement.setAttribute("id", id);
		if (this.fieldLabel) {
			this.fieldLabel.setForAttribute(id);
		}
	}

	public get hasSuffixes(): boolean {
		return (
			(this.suffixes.length > 0 && this.allowPrefixOrSuffix(this.inputType)) ||
			this.typePassword
		);
	}

	public get hasPrefixes(): boolean {
		return (
			(this.prefixes.length > 0 && this.allowPrefixOrSuffix(this.inputType)) ||
			this.inputType === "search"
		);
	}

	public get inputType(): string {
		return this.inputDiretive ? this.inputDiretive.type : "";
	}

	public get maxLength(): number {
		return this.inputDiretive ? this.inputDiretive.maxLengthHandler() : null;
	}

	public displayClickHandler(): void {
		if (this.inputDiretive) {
			this.inputDiretive.elementRef.nativeElement.focus();
		}
	}

	private prepareListeners(): void {
		if (this.dataListComponent) {
			this.dataListComponent.selected.subscribe((value) => {
				if (value && this.ngControl.control) {
					this.ngControl.control.setValue(value);
				}
			});
		}
		if (this.inputDiretive) {
			this.inputDiretive.blur$.subscribe((value) => {
				if (
					value !== null &&
					this.dataListComponent &&
					this.dataListComponent.isOpen
				) {
					setTimeout(() => {
						this.closeDataList();
					}, 200);
				}

				if (this.ngControl && this.ngControl.control) {
					this.validateControl();
				}
			});

			this.inputDiretive.input$.subscribe((value) => {
				this.numberOfCharacters = value ? value.length : 0;

				if (!!this.dataListComponent) {
					this.dataListComponent.filter(value);
				}
			});

			this.inputDiretive.focus$.subscribe((value) => {
				if (value !== null) {
					if (!!this.dataListComponent && !this.dataListComponent.isOpen) {
						this.openDataList();
					}
					if (!this.inputDiretive.readOnlyHandler()) {
						this.inputStatus = InputStatus.Selected;
					}
				}
			});

			this.inputDiretive.attributeChanges.subscribe(() =>
				this.prepareInputAttributes()
			);
		}
		if (this.ngControl && this.ngControl.control) {
			this.ngControl.control.statusChanges.subscribe(() =>
				this.validateControl()
			);
		}

		if (this.ds3SelectComponent) {
			this.ds3SelectComponent.opened.subscribe((opened) => {
				if (opened && !this.inputDiretive.readOnlyHandler()) {
					this.inputStatus = InputStatus.Selected;
				} else {
					this.inputStatus = InputStatus.Default;
				}
			});
		}

		if (this.ds3SelectMultiComponent) {
			this.ds3SelectMultiComponent.opened.subscribe((opened) => {
				if (opened && !this.inputDiretive.readOnlyHandler()) {
					this.inputStatus = InputStatus.Selected;
				} else {
					this.inputStatus = InputStatus.Default;
				}
			});
		}

		this.prefixes.changes.subscribe(() => this.prepareSuffixAndPrefixGrid());
		this.suffixes.changes.subscribe(() => this.prepareSuffixAndPrefixGrid());
	}

	private prepareStyles(): void {
		this.prepareInputAttributes();
		this.prepareSuffixAndPrefixGrid();

		this.setClass(
			this.elementRef.nativeElement.querySelector(".content"),
			"has-datalist",
			!!this.dataListComponent
		);
	}

	private prepareInputAttributes(): void {
		const types = [
			"date",
			"datetime-local",
			"email",
			"month",
			"number",
			"password",
			"range",
			"search",
			"tel",
			"text",
			"time",
			"url",
			"week",
			"textarea",
			"select",
			"custom",
		];

		const inputArea =
			this.elementRef.nativeElement.querySelector(".input-area");
		const content = this.elementRef.nativeElement.querySelector(".content");

		for (const type of types) {
			this.renderer2.removeClass(inputArea, `type-${type}`);
			if (this.inputDiretive.type === type) {
				this.renderer2.addClass(inputArea, `type-${type}`);
			}
		}

		this.setClass(inputArea, "read-only", this.inputDiretive.readOnlyHandler());
		this.setClass(
			inputArea,
			"disabled",
			(this.ngControl && this.ngControl.disabled) ||
				(this.inputDiretive && this.inputDiretive.disabledHandler())
		);
		this.setClass(
			content,
			"has-counter",
			!!this.inputDiretive.maxLengthHandler()
		);
	}

	private setClass(
		element: Element,
		className: string,
		condition: boolean
	): void {
		if (condition) {
			this.renderer2.addClass(element, className);
		} else {
			this.renderer2.removeClass(element, className);
		}
	}

	private prepareSuffixAndPrefixGrid(): void {
		const inputArea =
			this.elementRef.nativeElement.querySelector(".input-area");

		this.renderer2.removeClass(inputArea, `has-prefixes_not-has-suffixes`);
		this.renderer2.removeClass(inputArea, `has-suffixes_not-has-prefixes`);
		this.renderer2.removeClass(inputArea, `has-prefixes_has-suffixes`);

		if (this.hasPrefixes && !this.hasSuffixes) {
			this.renderer2.addClass(inputArea, `has-prefixes_not-has-suffixes`);
		}

		if (this.hasSuffixes && !this.hasPrefixes) {
			this.renderer2.addClass(inputArea, `has-suffixes_not-has-prefixes`);
		}

		if (this.hasPrefixes && this.hasSuffixes) {
			this.renderer2.addClass(inputArea, `has-prefixes_has-suffixes`);
		}
	}

	public set inputStatus(status: InputStatus) {
		const inputArea =
			this.elementRef.nativeElement.querySelector(".input-area");

		const classListStatus = [
			InputStatus.Default,
			InputStatus.Selected,
			InputStatus.Error,
			InputStatus.Success,
		];
		if (classListStatus.includes(status)) {
			for (const classStatus of classListStatus) {
				inputArea.classList.remove(classStatus);
			}
			inputArea.classList.add(status);
		}

		this.currentInputStatus = status;

		this.prepareSuffixAndPrefixGrid();
	}

	public get inputReadOnly(): boolean {
		return this.inputDiretive ? this.inputDiretive.readOnlyHandler() : false;
	}

	private allowPrefixOrSuffix(inputType: string): boolean {
		const typesThatAllowPrefixOrSuffix = [
			"text",
			"password",
			"search",
			"tel",
			"email",
			"url",
			"number",
		];
		return typesThatAllowPrefixOrSuffix.includes(inputType);
	}

	private validateControl(): void {
		if (!this.ngControl.control.validator) {
			if (!this.ngControl.control.value) {
				this.inputStatus = InputStatus.Default;
			}
			return;
		}
		if (this.ngControl.valid) {
			this.inputStatus = InputStatus.Success;
		} else {
			this.inputStatus = InputStatus.Error;
		}
	}

	private openDataList() {
		this.dataListComponent.openDropdown();
	}

	private closeDataList(): void {
		this.dataListComponent.closeDropdown();
	}

	public seePasswordHandle(event: MouseEvent): void {
		this.seePassword = !this.seePassword;
		if (this.seePassword) {
			this.inputDiretive.elementRef.nativeElement.setAttribute("type", "text");
		} else {
			this.inputDiretive.elementRef.nativeElement.setAttribute(
				"type",
				"password"
			);
		}
	}

	@HostListener("keydown", ["$event"])
	public onKeydownHandler(event: KeyboardEvent) {
		if (this.dataListComponent) {
			this.dataListComponent.onKeydownHandler(event);
		}
	}
}
