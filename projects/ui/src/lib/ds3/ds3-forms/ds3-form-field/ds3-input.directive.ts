import {
	Directive,
	ElementRef,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	OnInit,
} from "@angular/core";
import { BehaviorSubject } from "rxjs";

@Directive({
	selector:
		"input[ds3Input], textarea[ds3Input], ds3-checkbox[ds3Input]," +
		"ds3-select[ds3Input], ds3-select-multi[ds3Input]," +
		"ds3-number-field[ds3Input], ds3-input-date[ds3Input]," +
		"ds3-color-picker[ds3Input], ds3-radio-group[ds3Input]",
})
export class Ds3InputDirective implements OnInit, OnDestroy {
	public readonly blur$: BehaviorSubject<string> = new BehaviorSubject<string>(
		null
	);

	public readonly focus$: BehaviorSubject<string> = new BehaviorSubject<string>(
		null
	);

	public readonly input$: BehaviorSubject<string> = new BehaviorSubject<string>(
		null
	);

	public readonly attributeChanges: BehaviorSubject<void> =
		new BehaviorSubject<void>(null);

	private mutationObserver: MutationObserver;

	constructor(
		public readonly elementRef: ElementRef<
			HTMLInputElement | HTMLTextAreaElement | HTMLElement
		>
	) {}

	public ngOnInit(): void {
		this.mutationObserver = new MutationObserver((mutationsList) => {
			this.attributeChanges.next();
		});

		this.mutationObserver.observe(this.elementRef.nativeElement, {
			attributes: true,
		});
	}

	public ngOnDestroy(): void {
		if (this.mutationObserver) {
			this.mutationObserver.disconnect();
		}
	}

	@HostListener("blur", ["$event.target"])
	public blurHandler(target: HTMLInputElement | HTMLTextAreaElement) {
		this.blur$.next(target.value);
	}

	@HostListener("focus", ["$event.target"])
	public focusHandler(target: HTMLInputElement | HTMLTextAreaElement) {
		this.focus$.next(target.value);
	}

	@HostListener("input", ["$event.target"])
	public inputHandler(target: HTMLInputElement | HTMLTextAreaElement) {
		this.input$.next(target.value);
	}

	public get type(): string {
		const inputTransformed = this.elementRef.nativeElement as HTMLInputElement;
		if (
			inputTransformed.localName === "ds3-select" ||
			inputTransformed.localName === "ds3-select-multi"
		) {
			return "select";
		}
		if (inputTransformed.localName === "ds3-number-field") {
			return "number";
		}
		return inputTransformed.type || "custom";
	}

	public readOnlyHandler(): boolean {
		// @ts-ignore
		return this.elementRef.nativeElement.readOnly;
	}

	public disabledHandler(): boolean {
		// @ts-ignore
		return this.elementRef.nativeElement.disabled;
	}

	public maxLengthHandler(): number {
		// @ts-ignore
		return this.elementRef.nativeElement.maxLength;
	}
}
