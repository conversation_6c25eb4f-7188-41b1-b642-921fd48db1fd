import {
	Component,
	forwardRef,
	Input,
	OnInit,
	ChangeDetectorRef,
	HostListener,
} from "@angular/core";
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from "@angular/forms";
import { FormControl, FormGroup, FormBuilder } from "@angular/forms";
import { Ds3CountryPhoneService } from "./ds3-country-phone.service";

@Component({
	selector: "ds3-country-phone",
	templateUrl: "./ds3-country-phone.component.html",
	styleUrls: ["./ds3-country-phone.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3CountryPhoneComponent),
			multi: true,
		},
	],
})
export class Ds3CountryPhoneComponent implements ControlValueAccessor, OnInit {
	@Input() singleInput = true;
	@Input() input1Name = "ddi";
	@Input() input2Name = "ddPhone";
	@Input() id: string = "";

	countries = [];
	filteredCountries = [];
	showDropdown = false;
	selectedCountryCode = 55;
	phoneMask = "(00) 00000-0000";
	selectedCountryName = "Selecione um país";

	formGroup: FormGroup;
	phoneControl: FormControl;
	filterControl: FormControl;

	constructor(
		private fb: FormBuilder,
		private cd: ChangeDetectorRef,
		private service: Ds3CountryPhoneService
	) {
		this.phoneControl = new FormControl("");
		this.filterControl = new FormControl("");
		this.formGroup = this.fb.group({
			phoneNumber: this.phoneControl,
			filterText: this.filterControl,
		});
	}

	ngOnInit() {
		this.countries = this.service.getCountries();
		this.filteredCountries = [...this.countries];

		this.filterControl.valueChanges.subscribe(() => this.filterCountries());
		this.phoneControl.valueChanges.subscribe((value) =>
			this.onPhoneNumberChange(value)
		);
		this.updateSelectedCountryName();
	}

	onPhoneNumberChange(value: string) {
		this.updateValue();
	}

	toggleDropdown() {
		this.showDropdown = !this.showDropdown;
	}

	filterCountries() {
		const filterText = this.filterControl.value || "";
		this.filteredCountries = this.countries.filter((country) =>
			country.name.toLowerCase().includes(filterText.toLowerCase())
		);
	}

	selectCountry(country: any) {
		this.selectedCountryCode = country.code;
		this.phoneMask = this.getPhoneMaskForCountry(country.code);
		this.phoneControl.reset(); // Limpar o número de telefone ao trocar de país
		this.updateSelectedCountryName();
		this.showDropdown = false;
		this.updateValue();
	}

	getPhoneMaskForCountry(countryCode: number): string {
		return countryCode === 55 ? "(00) 00000-0000" : "0*";
	}

	updateSelectedCountryName() {
		const selectedCountry = this.countries.find(
			(country) => country.code === this.selectedCountryCode
		);
		this.selectedCountryName = selectedCountry
			? selectedCountry.label
			: "Selecione um país";
	}

	updateValue() {
		const value = this.singleInput
			? `${this.selectedCountryCode}${this.phoneControl.value}`
			: {
					[this.input1Name]: Number(this.selectedCountryCode),
					[this.input2Name]: this.phoneControl.value,
			  };

		this.onChanged(value);
		this.onTouched();
	}

	writeValue(value: any): void {
		if (value) {
			const { countryCode, phoneNumber } = value;
			this.selectedCountryCode = countryCode || "";
			this.phoneControl.setValue(phoneNumber || "");
			this.updateSelectedCountryName();
		}
	}

	registerOnChange(fn: any): void {
		this.onChanged = fn;
	}

	registerOnTouched(fn: any): void {
		this.onTouched = fn;
	}

	onChanged: Function = () => {};
	onTouched: Function = () => {};

	@HostListener("document:click", ["$event"])
	onClickOutside(event: Event) {
		if (this.showDropdown) {
			const target = event.target as HTMLElement;
			if (!target.closest(".dropdown-container")) {
				this.showDropdown = false;
			}
		}
	}
}
