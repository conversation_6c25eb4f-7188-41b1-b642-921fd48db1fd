@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

.country-phone {
	display: flex;

	.custom-dropdown {
		position: relative;
		width: 100%;

		// Container do input principal
		.input-container {
			display: flex;
			flex-direction: row;
			height: 40px;
			gap: 10px;
			color: $typeDefaultText;

			.form-control {
				border: 1px solid #ced4da;
				border-radius: 8px;
				line-height: unset;
				color: $typeDefaultText;
			}
		}

		// Opção selecionada
		.selected-option {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: space-evenly;
			border: 1px solid #ced4da;
			border-radius: 8px;
			max-width: 103px;
			background-color: #fff;
			cursor: pointer;
			color: $typeDefaultText;

			i {
				pointer-events: none;
			}
		}

		// Estilo do input com foco
		.form-control {
			flex: 2;
			margin-left: 10px;

			&:focus {
				box-shadow: unset;
			}
		}

		// Dropdown de opções
		.dropdown-options {
			margin-top: 4px;
			padding: 16px;
			position: absolute;
			top: 100%;
			left: 0;
			right: 0;
			background: white;
			border: 1px solid #ccc;
			border-radius: 4px;
			z-index: 1000;
			max-height: 387px; // Limite do container geral
			box-sizing: border-box;

			// Container de filtro fixo
			.filter-container {
				position: sticky;
				top: 0;
				background: white;
				z-index: 1001;
				padding-bottom: 8px;
			}

			// Input de filtro
			.filter-input {
				width: 100%;
				padding: 8px;
				border: 1px solid #c9cbcf;
				border-radius: 4px;
				outline: none;
				@extend .pct-body2;
				color: #797d86;

				&:focus {
					border-color: #0547e1;
				}
			}

			// Lista de opções
			ul {
				list-style: none;
				padding: 0;
				margin: 0;
				overflow-y: auto; // Habilita barra de rolagem
				max-height: 300px; // Limita a altura da lista
				box-sizing: border-box;

				// Estiliza barra de rolagem no Firefox
				scrollbar-width: thin;
				scrollbar-color: #ccc transparent;
			}

			// Estilo dos itens da lista
			li {
				display: flex;
				align-items: center;
				height: 63px;
				padding: 8px 16px 8px 0;
				cursor: pointer;
				@extend .pct-body1;
				color: $supportBlack03;

				&:hover {
					background: #f0f0f0;
				}
			}
		}
	}
}
