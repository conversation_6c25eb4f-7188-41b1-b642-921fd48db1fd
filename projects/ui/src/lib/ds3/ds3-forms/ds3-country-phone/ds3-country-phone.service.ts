import { Injectable } from "@angular/core";

@Injectable({
	providedIn: "root",
})
export class Ds3CountryPhoneService {
	private countries = [
		{ name: "Afeganistão", code: 93, label: "+93" },
		{ name: "África do Sul", code: 27, label: "+27" },
		{ name: "Albânia", code: 355, label: "+355" },
		{ name: "Alemanha", code: 49, label: "+49" },
		{ name: "Andorra", code: 376, label: "+376" },
		{ name: "Angola", code: 244, label: "+244" },
		{ name: "Anguil<PERSON>", code: 1264, label: "+1264" },
		{ name: "Antígua e Barbuda", code: 1268, label: "+1268" },
		{ name: "<PERSON><PERSON><PERSON><PERSON>", code: 966, label: "+966" },
		{ name: "Argé<PERSON>", code: 213, label: "+213" },
		{ name: "Argentina", code: 54, label: "+54" },
		{ name: "Armênia", code: 374, label: "+374" },
		{ name: "Aruba", code: 297, label: "+297" },
		{ name: "<PERSON>st<PERSON><PERSON><PERSON>", code: 61, label: "+61" },
		{ name: "<PERSON>ust<PERSON>", code: 43, label: "+43" },
		{ name: "Azerbaijão", code: 994, label: "+994" },
		{ name: "Bahamas", code: 1242, label: "+1242" },
		{ name: "Bahrein", code: 973, label: "+973" },
		{ name: "Bangladesh", code: 880, label: "+880" },
		{ name: "Barbados", code: 1246, label: "+1246" },
		{ name: "Bélgica", code: 32, label: "+32" },
		{ name: "Belize", code: 501, label: "+501" },
		{ name: "Benim", code: 229, label: "+229" },
		{ name: "Bermudas", code: 1441, label: "+1441" },
		{ name: "Bielorrússia", code: 375, label: "+375" },
		{ name: "Bolívia", code: 591, label: "+591" },
		{ name: "Bósnia e Herzegovina", code: 387, label: "+387" },
		{ name: "Botsuana", code: 267, label: "+267" },
		{ name: "Brasil", code: 55, label: "+55" },
		{ name: "Brunei", code: 673, label: "+673" },
		{ name: "Bulgária", code: 359, label: "+359" },
		{ name: "Burkina Faso", code: 226, label: "+226" },
		{ name: "Burundi", code: 257, label: "+257" },
		{ name: "Butão", code: 975, label: "+975" },
		{ name: "Cabo Verde", code: 238, label: "+238" },
		{ name: "Camarões", code: 237, label: "+237" },
		{ name: "Camboja", code: 855, label: "+855" },
		{ name: "Canadá", code: 1, label: "+1" },
		{ name: "Catar", code: 974, label: "+974" },
		{ name: "Cazaquistão", code: 7, label: "+7" },
		{ name: "Chade", code: 235, label: "+235" },
		{ name: "Chile", code: 56, label: "+56" },
		{ name: "China", code: 86, label: "+86" },
		{ name: "Chipre", code: 357, label: "+357" },
		{ name: "Colômbia", code: 57, label: "+57" },
		{ name: "Comores", code: 269, label: "+269" },
		{ name: "Congo (Brazzaville)", code: 242, label: "+242" },
		{ name: "Congo (Kinshasa)", code: 243, label: "+243" },
		{ name: "Coreia do Norte", code: 850, label: "+850" },
		{ name: "Coreia do Sul", code: 82, label: "+82" },
		{ name: "Costa do Marfim", code: 225, label: "+225" },
		{ name: "Costa Rica", code: 506, label: "+506" },
		{ name: "Croácia", code: 385, label: "+385" },
		{ name: "Cuba", code: 53, label: "+53" },
		{ name: "Dinamarca", code: 45, label: "+45" },
		{ name: "Djibuti", code: 253, label: "+253" },
		{ name: "Dominica", code: 1767, label: "+1767" },
		{ name: "Egito", code: 20, label: "+20" },
		{ name: "El Salvador", code: 503, label: "+503" },
		{ name: "Emirados Árabes Unidos", code: 971, label: "+971" },
		{ name: "Equador", code: 593, label: "+593" },
		{ name: "Eritreia", code: 291, label: "+291" },
		{ name: "Eslováquia", code: 421, label: "+421" },
		{ name: "Eslovênia", code: 386, label: "+386" },
		{ name: "Espanha", code: 34, label: "+34" },
		{ name: "Estônia", code: 372, label: "+372" },
		{ name: "Etiópia", code: 251, label: "+251" },
		{ name: "Fiji", code: 679, label: "+679" },
		{ name: "Filipinas", code: 63, label: "+63" },
		{ name: "Finlândia", code: 358, label: "+358" },
		{ name: "França", code: 33, label: "+33" },
		{ name: "Gabão", code: 241, label: "+241" },
		{ name: "Gâmbia", code: 220, label: "+220" },
		{ name: "Gana", code: 233, label: "+233" },
		{ name: "Geórgia", code: 995, label: "+995" },
		{ name: "Grécia", code: 30, label: "+30" },
		{ name: "Granada", code: 1473, label: "+1473" },
		{ name: "Guatemala", code: 502, label: "+502" },
		{ name: "Guiana", code: 592, label: "+592" },
		{ name: "Guiné", code: 224, label: "+224" },
		{ name: "Guiné Equatorial", code: 240, label: "+240" },
		{ name: "Guiné-Bissau", code: 245, label: "+245" },
		{ name: "Haiti", code: 509, label: "+509" },
		{ name: "Honduras", code: 504, label: "+504" },
		{ name: "Hungria", code: 36, label: "+36" },
		{ name: "Índia", code: 91, label: "+91" },
		{ name: "Indonésia", code: 62, label: "+62" },
		{ name: "Irã", code: 98, label: "+98" },
		{ name: "Iraque", code: 964, label: "+964" },
		{ name: "Irlanda", code: 353, label: "+353" },
		{ name: "Islândia", code: 354, label: "+354" },
		{ name: "Israel", code: 972, label: "+972" },
		{ name: "Itália", code: 39, label: "+39" },
		{ name: "Jamaica", code: 1876, label: "+1876" },
		{ name: "Japão", code: 81, label: "+81" },
		{ name: "Jordânia", code: 962, label: "+962" },
		{ name: "Kuwait", code: 965, label: "+965" },
		{ name: "Laos", code: 856, label: "+856" },
		{ name: "Letônia", code: 371, label: "+371" },
		{ name: "Líbano", code: 961, label: "+961" },
		{ name: "Libéria", code: 231, label: "+231" },
		{ name: "Líbia", code: 218, label: "+218" },
		{ name: "Lituânia", code: 370, label: "+370" },
		{ name: "Luxemburgo", code: 352, label: "+352" },
		{ name: "Macedônia", code: 389, label: "+389" },
		{ name: "Madagascar", code: 261, label: "+261" },
		{ name: "Malásia", code: 60, label: "+60" },
		{ name: "Malaui", code: 265, label: "+265" },
		{ name: "Maldivas", code: 960, label: "+960" },
		{ name: "Mali", code: 223, label: "+223" },
		{ name: "Malta", code: 356, label: "+356" },
		{ name: "Marrocos", code: 212, label: "+212" },
		{ name: "Maurícia", code: 230, label: "+230" },
		{ name: "Mauritânia", code: 222, label: "+222" },
		{ name: "México", code: 52, label: "+52" },
		{ name: "Moçambique", code: 258, label: "+258" },
		{ name: "Mônaco", code: 377, label: "+377" },
		{ name: "Mongólia", code: 976, label: "+976" },
		{ name: "Montenegro", code: 382, label: "+382" },
		{ name: "Namíbia", code: 264, label: "+264" },
		{ name: "Nauru", code: 674, label: "+674" },
		{ name: "Nepal", code: 977, label: "+977" },
		{ name: "Nicarágua", code: 505, label: "+505" },
		{ name: "Níger", code: 227, label: "+227" },
		{ name: "Nigéria", code: 234, label: "+234" },
		{ name: "Noruega", code: 47, label: "+47" },
		{ name: "Nova Zelândia", code: 64, label: "+64" },
		{ name: "Omã", code: 968, label: "+968" },
		{ name: "Países Baixos", code: 31, label: "+31" },
		{ name: "Paquistão", code: 92, label: "+92" },
		{ name: "Panamá", code: 507, label: "+507" },
		{ name: "Papua Nova Guiné", code: 675, label: "+675" },
		{ name: "Paraguai", code: 595, label: "+595" },
		{ name: "Peru", code: 51, label: "+51" },
		{ name: "Polônia", code: 48, label: "+48" },
		{ name: "Portugal", code: 351, label: "+351" },
		{ name: "Quênia", code: 254, label: "+254" },
		{ name: "Quirguistão", code: 996, label: "+996" },
		{ name: "Reino Unido", code: 44, label: "+44" },
		{ name: "República Centro-Africana", code: 236, label: "+236" },
		{ name: "República Dominicana", code: 1809, label: "+1809" },
		{ name: "República Tcheca", code: 420, label: "+420" },
		{ name: "Romênia", code: 40, label: "+40" },
		{ name: "Ruanda", code: 250, label: "+250" },
		{ name: "Rússia", code: 7, label: "+7" },
		{ name: "Samoa", code: 685, label: "+685" },
		{ name: "Santa Lúcia", code: 1758, label: "+1758" },
		{ name: "São Cristóvão e Nevis", code: 1869, label: "+1869" },
		{ name: "São Tomé e Príncipe", code: 239, label: "+239" },
		{ name: "São Vicente e Granadinas", code: 1784, label: "+1784" },
		{ name: "Senegal", code: 221, label: "+221" },
		{ name: "Serra Leoa", code: 232, label: "+232" },
		{ name: "Sérvia", code: 381, label: "+381" },
		{ name: "Seychelles", code: 248, label: "+248" },
		{ name: "Singapura", code: 65, label: "+65" },
		{ name: "Síria", code: 963, label: "+963" },
		{ name: "Somália", code: 252, label: "+252" },
		{ name: "Sri Lanka", code: 94, label: "+94" },
		{ name: "Suazilândia", code: 268, label: "+268" },
		{ name: "Sudão", code: 249, label: "+249" },
		{ name: "Suécia", code: 46, label: "+46" },
		{ name: "Suíça", code: 41, label: "+41" },
		{ name: "Suriname", code: 597, label: "+597" },
		{ name: "Tailândia", code: 66, label: "+66" },
		{ name: "Taiwan", code: 886, label: "+886" },
		{ name: "Tanzânia", code: 255, label: "+255" },
		{ name: "Timor-Leste", code: 670, label: "+670" },
		{ name: "Togo", code: 228, label: "+228" },
		{ name: "Tonga", code: 676, label: "+676" },
		{ name: "Trinidad e Tobago", code: 1868, label: "+1868" },
		{ name: "Tunísia", code: 216, label: "+216" },
		{ name: "Turcomenistão", code: 993, label: "+993" },
		{ name: "Turquia", code: 90, label: "+90" },
		{ name: "Ucrânia", code: 380, label: "+380" },
		{ name: "Uganda", code: 256, label: "+256" },
		{ name: "Uruguai", code: 598, label: "+598" },
		{ name: "Uzbequistão", code: 998, label: "+998" },
		{ name: "Vanuatu", code: 678, label: "+678" },
		{ name: "Venezuela", code: 58, label: "+58" },
		{ name: "Vietnã", code: 84, label: "+84" },
		{ name: "Zâmbia", code: 260, label: "+260" },
		{ name: "Zimbábue", code: 263, label: "+263" },
	];

	constructor() {}

	getCountries() {
		return this.countries;
	}
}
