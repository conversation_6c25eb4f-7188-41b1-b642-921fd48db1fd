**Seletor:** `ds3-country-phone`

**Utilização:**`<ds3-country-phone formControlName="phone" ds3Input>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';;

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    
   myForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.myForm = this.fb.group({      
      phone: ['']
    });
  }	    
}
```

> meu-exemplo-de-tela.component.html

```html
<form [formGroup]="myForm">
	<ds3-form-field class="col-4">
		<ds3-field-label> Telefone </ds3-field-label>
		<ds3-country-phone formControlName="phone" ds3Input>
		</ds3-country-phone>
	</ds3-form-field>
</form>

sem usar o form-field

<ds3-country-phone [formControl]="myForm.get('phone2')" ds3Input required> </ds3-country-phone>

Usando as proprieadades

<form [formGroup]="myForm">
	<ds3-form-field class="col-4">
		<ds3-field-label> Telefone </ds3-field-label>
			<ds3-country-phone formControlName="phone" [singleInput]="false" [input1Name]="'input1'" [input2Name]="'input2'" ds3Input>
		</ds3-country-phone>
	</ds3-form-field>
</form>
```

&nbsp;  

## Props / Inputs

| Property                                 | Values     | Default   	| Description                                                                               |
|---                                       |         ---|        	 ---| 																							|
| @Input() singleInput: boolean            | true/false |      true 	|Define de o input será unico ou se ele vai separar o ddi do telefone.                      |
| @Input() input1Name: string              | 			|       ddi 	|Define o nome do campo onde escolhemos o pais.							                    |
| @Input() input2Name: string              | 			|ddd + telefone |Define o nome do campo onde digitamos o ddd e o numero de telefone.						|


                                                                                          |
