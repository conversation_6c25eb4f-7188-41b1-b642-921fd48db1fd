import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3CountryPhoneComponent } from "./ds3-country-phone.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { NgxMaskModule } from "ngx-mask";

@NgModule({
	declarations: [Ds3CountryPhoneComponent],
	imports: [CommonModule, ReactiveFormsModule, NgxMaskModule.forRoot()],
	exports: [Ds3CountryPhoneComponent],
})
export class Ds3CountryPhoneModule {}
