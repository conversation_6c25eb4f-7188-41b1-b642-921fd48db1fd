<div class="country-phone">
	<div class="custom-dropdown dropdown-container">
		<div class="input-container">
			<div class="selected-option" (click)="toggleDropdown()">
				<span class="pct-body1">
					{{ selectedCountryName }}
				</span>
				<i *ngIf="!showDropdown" class="pct pct-chevron-down"></i>
				<i *ngIf="showDropdown" class="pct pct-chevron-up"></i>
			</div>
			<input
				type="text"
				class="form-control pct-body1 inputPhone"
				[formControl]="phoneControl"
				[mask]="phoneMask"
				id="{{ id }}-input"
				autocomplete="off"
				maxlength="15"
				ds3Input />
		</div>

		<div class="dropdown-options" *ngIf="showDropdown">
			<div class="filter-container">
				<input
					type="text"
					class="filter-input"
					placeholder="Busque pelo nome do país"
					[formControl]="filterControl" />
			</div>
			<ul>
				<li *ngIf="filteredCountries.length === 0">
					<span>Nenhuma opção encontrada</span>
				</li>
				<li
					*ngFor="let country of filteredCountries"
					(click)="selectCountry(country)"
					class="pct-body1">
					<span>{{ country.name }} ({{ country.label }})</span>
				</li>
			</ul>
		</div>
	</div>
</div>
