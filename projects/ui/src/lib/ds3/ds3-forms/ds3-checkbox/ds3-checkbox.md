# Checkbox

`<ds3-checkbox>` é um componente personalizado usado para criar caixas de seleção (checkboxes) estilizadas. Ele pode ser usado como um componente autônomo ou dentro de um `ds3-form-field`.

## Uso Básico

O uso básico do componente `<ds3-checkbox>` envolve a criação de uma caixa de seleção simples. Aqui está um exemplo:

```html
<ds3-checkbox [formControl]="form.controls['control']">Checkbox</ds3-checkbox>
```

Neste exemplo, estamos usando o atributo `[formControl]` para vincular o controle da caixa de seleção ao formulário. Também é possível usar `formControlName` ou `ngModel`.

## Checkbox em um `ds3-form-field`

Você também pode usar `<ds3-checkbox>` dentro de um `ds3-form-field`, basta adicionar a diretiva `ds3Input`. Aqui está um exemplo:

```html
<ds3-form-field>
	<ds3-checkbox ds3Input formControlName="control">Checkbox</ds3-checkbox>
</ds3-form-field>
```

Neste exemplo, a caixa de seleção está estilizada dentro de um `ds3-form-field`, permitindo que você adicione outros elementos relacionados ao formulário.

## Atributos Permitidos

O componente `<ds3-checkbox>` permite a definição dos seguintes atributos:

- `disabled`: Usado para desabilitar a caixa de seleção. Quando definido, o usuário não poderá interagir com a caixa de seleção.

```html
<ds3-checkbox [formControl]="form.controls['control']" disabled>Checkbox Desabilitado</ds3-checkbox>
```

porém, usado em conjunto com um FormControl, é recomendado desabilita-lo diretamente no FormControl:

```ts
this.form = this.fb.group({
	control: this.fb.control({ value: null, disabled: true }, Validators.required),
});

// ou

this.form.get('control').disable();
```

- `checked`: Define se a caixa de seleção deve ser marcada como selecionada. Quando definido, a caixa de seleção é exibida como marcada.

```html
<ds3-checkbox [formControl]="form.controls['control']" checked>Checkbox Marcado</ds3-checkbox>
```

porém, usado em conjunto com um FormControl, é preferível marcalo diretamente no FormControl:

```ts
this.form = this.fb.group({
	control: this.fb.control(true),
});

// ou

this.form.get('control').setValue(true);
```

- `indeterminate`: Usado para definir o estado da caixa de seleção como indeterminado. Isso é útil quando você deseja representar um estado intermediário entre marcado e desmarcado.

```html
<ds3-checkbox [formControl]="form.controls['control']" indeterminate>Checkbox Indeterminado</ds3-checkbox>
```

Lembre-se de que esses atributos podem ser definidos de forma dinâmica com base na lógica do seu aplicativo.

## Personalização

O componente `<ds3-checkbox>` também suporta personalização de estilos, permitindo que você adapte a aparência das caixas de seleção. Você pode personalizar o estilo do componente `<ds3-checkbox>` por meio dos seguintes tokens CSS. Esses tokens permitem ajustar a aparência da caixa de seleção de acordo com as necessidades do seu projeto:

| Token                      | Descrição                                                       | Valor Padrão                     |
| -------------------------- | --------------------------------------------------------------- | -------------------------------- |
| --checkbox-check-color     | Cor da marca de seleção quando a caixa de seleção está marcada. | --color-action-default-able-4    |
| --checkbox-disabled-color  | Cor da caixa de seleção quando ela está desabilitada.           | --color-action-default-disable-2 |
| --checkbox-label-color     | Cor do rótulo associado à caixa de seleção.                     | --color-typography-default-title |
| --checkbox-size            | Tamanho da caixa de seleção.                                    | 0.875rem                         |
| --checkbox-gap             | Espaçamento entre a caixa de seleção e o rótulo.                | 0.5rem                           |
| --checkbox-disabled-cursor | Cursor quando a caixa de seleção está desabilitada.             | --form-field-disabled-cursor     |

### Exemplo de uso

Em seu componente que usará o Checkbox adicione as seguintes linhas para definir a cor e o cursor personalizado no modo desabilitado:

```css
ds3-checkbox {
	--checkbox-disabled-color: #333;
	--checkbox-disabled-cursor: not-allowed;
}
```

Certifique-se de substituir os valores nos exemplos acima pelos valores reais desejados para personalização.
