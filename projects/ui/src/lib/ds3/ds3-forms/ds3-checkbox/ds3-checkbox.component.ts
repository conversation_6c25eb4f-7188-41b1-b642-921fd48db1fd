import {
	coerceBooleanProperty,
	coerceNumberProperty,
} from "@angular/cdk/coercion";
import {
	AfterViewInit,
	Component,
	ElementRef,
	HostListener,
	Input,
	OnInit,
	Renderer2,
	ViewEncapsulation,
	forwardRef,
} from "@angular/core";
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from "@angular/forms";

@Component({
	selector: "ds3-checkbox",
	templateUrl: "./ds3-checkbox.component.html",
	styleUrls: ["./ds3-checkbox.component.scss"],
	encapsulation: ViewEncapsulation.None,
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3CheckboxComponent),
			multi: true,
		},
	],
})
export class Ds3CheckboxComponent
	implements OnInit, AfterViewInit, ControlValueAccessor
{
	private static sequence = 0;

	public _internalValue;

	private onChange: any = () => {};

	private onTouch: any = () => {};

	private _disabled = false;

	private _checked = false;

	private _indeterminate = false;

	private _tabindex = 0;
	private _square = true;

	constructor(
		public readonly elementRef: ElementRef<HTMLElement>,
		private readonly renderer2: Renderer2
	) {}

	public ngOnInit(): void {
		this.renderer2.addClass(this.elementRef.nativeElement, "ds3-checkbox");
		this.elementRef.nativeElement.setAttribute(
			"tabindex",
			String(this.tabindex) || "0"
		);
	}

	public ngAfterViewInit(): void {
		this.generateAndSetId();
	}

	public get disabled() {
		return this._disabled;
	}

	@Input()
	public set disabled(value: boolean) {
		this._disabled = coerceBooleanProperty(value);
		if (this._disabled) {
			this.elementRef.nativeElement.setAttribute("tabindex", "-1");
		} else {
			this.elementRef.nativeElement.setAttribute(
				"tabindex",
				String(this.tabindex) || "0"
			);
		}
	}

	public get checked() {
		return this._checked;
	}

	@Input()
	public set checked(value: boolean) {
		this._checked = coerceBooleanProperty(value);
		this.setValue(this._checked);
	}

	@Input()
	public set square(value: boolean) {
		this._square = coerceBooleanProperty(value);
	}

	public get square() {
		return this._square;
	}

	public get indeterminate() {
		return this._indeterminate;
	}

	@Input()
	public set indeterminate(value: boolean) {
		this._indeterminate = coerceBooleanProperty(value);
		this.elementRef.nativeElement.querySelector<HTMLInputElement>(
			"input"
		).indeterminate = this._indeterminate;
	}

	public get tabindex() {
		return this._tabindex;
	}

	@Input()
	public set tabindex(value: number) {
		this._tabindex = coerceNumberProperty(value, 0);
		this.elementRef.nativeElement.setAttribute(
			"tabindex",
			String(this._tabindex)
		);
	}

	private generateAndSetId(): void {
		const currentId = this.elementRef.nativeElement.getAttribute("id");
		const id = currentId || String(Ds3CheckboxComponent.sequence++);
		this.elementRef.nativeElement
			.querySelector<HTMLInputElement>("input")
			.setAttribute("id", `ds3-checkbox__${id}`);
		this.elementRef.nativeElement
			.querySelector<HTMLLabelElement>("label")
			.setAttribute("for", `ds3-checkbox__${id}`);
	}

	public setDisabledState(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	public writeValue(value: boolean): void {
		this._internalValue = value;
		this._checked = value;
	}

	public registerOnChange(fn: any): void {
		this.onChange = fn;
	}

	public registerOnTouched(fn: any): void {
		this.onTouch = fn;
	}

	@HostListener("blur", ["$event.target"])
	public blurHandler(target: HTMLInputElement) {
		this.onTouch();
	}

	@HostListener("keydown.space", ["$event"])
	public keydownSpaceHandler(event: KeyboardEvent) {
		event.preventDefault();
		this.setValue(!this._internalValue);
	}

	public tooggle() {
		if (!this.disabled) {
			this._internalValue = !this._internalValue;
			this._checked = this._internalValue;
			this.onChange(this._internalValue);
		}
	}

	public setValue(isChecked: boolean) {
		this._internalValue = isChecked;
		this._checked = isChecked;
		this.onChange(this._internalValue);
		this.onTouch();
	}

	@HostListener("keydown.enter", ["$event"])
	public keydownEnterHandler(event: KeyboardEvent) {
		if (this.disabled) return;
		event.preventDefault();
		this.setValue(!this._internalValue);
	}
}
