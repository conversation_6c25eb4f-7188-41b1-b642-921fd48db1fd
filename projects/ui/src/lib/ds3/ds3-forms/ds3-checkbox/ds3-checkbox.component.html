<input
	(change)="setValue($event.target.checked)"
	[checked]="_internalValue"
	[disabled]="disabled"
	type="checkbox" />
<div (click)="tooggle()" class="checkbox-mark">
	<svg fill="none" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
		<path
			class="check"
			*ngIf="!square"
			d="M4.83984 7.29803L6.58984 9.04803C6.69922 9.15741 6.83594 9.2121 7 9.2121C7.16406 9.2121 7.30078 9.15741 7.41016 9.04803L13.248 3.21014C13.3574 3.10077 13.4121 2.96405 13.4121 2.79999C13.4121 2.63593 13.3574 2.49921 13.248 2.38983C13.1296 2.27134 12.9883 2.2121 12.8242 2.2121C12.6693 2.2121 12.5371 2.27134 12.4277 2.38983L7 7.80389L5.66016 6.47772C5.55078 6.35923 5.41406 6.29999 5.25 6.29999C5.08594 6.29999 4.94922 6.35923 4.83984 6.47772C4.72135 6.5871 4.66211 6.72382 4.66211 6.88788C4.66211 7.04283 4.72135 7.17954 4.83984 7.29803ZM11.6621 7.4621V11.55C11.6621 11.632 11.6484 11.7095 11.6211 11.7824C11.5938 11.8462 11.5527 11.9055 11.498 11.9601C11.4434 12.0148 11.3796 12.0604 11.3066 12.0969C11.2337 12.1242 11.1608 12.1379 11.0879 12.1379H2.91211C2.83919 12.1379 2.76628 12.1242 2.69336 12.0969C2.62044 12.0604 2.55664 12.0148 2.50195 11.9601C2.44727 11.9055 2.40625 11.8462 2.37891 11.7824C2.35156 11.7095 2.33789 11.632 2.33789 11.55V3.38788C2.33789 3.30585 2.35156 3.22837 2.37891 3.15546C2.40625 3.08254 2.44727 3.0233 2.50195 2.97772C2.55664 2.92303 2.62044 2.88202 2.69336 2.85468C2.76628 2.81822 2.83919 2.79999 2.91211 2.79999H9.33789C9.49284 2.79999 9.625 2.7453 9.73438 2.63593C9.85286 2.51744 9.91211 2.37616 9.91211 2.2121C9.91211 2.05715 9.85286 1.92499 9.73438 1.81561C9.625 1.69712 9.49284 1.63788 9.33789 1.63788H2.91211C2.67513 1.63788 2.44727 1.68345 2.22852 1.7746C2.01888 1.86574 1.83659 1.98879 1.68164 2.14374C1.52669 2.3078 1.39909 2.49465 1.29883 2.70428C1.20768 2.91392 1.16211 3.14178 1.16211 3.38788V11.55C1.16211 11.787 1.20768 12.0148 1.29883 12.2336C1.38997 12.4432 1.51758 12.6255 1.68164 12.7805C1.83659 12.9445 2.01888 13.0721 2.22852 13.1633C2.44727 13.2544 2.67513 13.3 2.91211 13.3H11.0879C11.3249 13.3 11.5482 13.2544 11.7578 13.1633C11.9766 13.0721 12.1634 12.9445 12.3184 12.7805C12.4733 12.6255 12.5964 12.4432 12.6875 12.2336C12.7878 12.0148 12.8379 11.787 12.8379 11.55V7.4621C12.8379 7.30715 12.7786 7.17499 12.6602 7.06561C12.5508 6.94712 12.4141 6.88788 12.25 6.88788C12.0859 6.88788 11.9447 6.94712 11.8262 7.06561C11.7168 7.17499 11.6621 7.30715 11.6621 7.4621Z" />
		<path
			class="check scale03"
			*ngIf="square"
			d="M64 32C28.7 32 0 60.7 0 96L0 416c0 35.3 28.7 64 64 64l320 0c35.3 0 64-28.7 64-64l0-320c0-35.3-28.7-64-64-64L64 32zM337 209L209 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L303 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"
			fill="#1e60fa" />
		<path
			class="uncheck"
			d="M2.91211 1.63788C2.67513 1.63788 2.44727 1.68345 2.22852 1.7746C2.01888 1.86574 1.83659 1.98879 1.68164 2.14374C1.52669 2.3078 1.39909 2.49465 1.29883 2.70428C1.20768 2.91392 1.16211 3.14178 1.16211 3.38788V11.55C1.16211 11.787 1.20768 12.0148 1.29883 12.2336C1.38997 12.4432 1.51758 12.6255 1.68164 12.7805C1.83659 12.9445 2.01888 13.0721 2.22852 13.1633C2.44727 13.2544 2.67513 13.3 2.91211 13.3H11.0879C11.3249 13.3 11.5482 13.2544 11.7578 13.1633C11.9766 13.0721 12.1634 12.9445 12.3184 12.7805C12.4733 12.6255 12.5964 12.4432 12.6875 12.2336C12.7878 12.0148 12.8379 11.787 12.8379 11.55V3.38788C12.8379 3.14178 12.7923 2.91392 12.7012 2.70428C12.61 2.48553 12.4824 2.29869 12.3184 2.14374C12.1634 1.98879 11.9766 1.86574 11.7578 1.7746C11.5482 1.68345 11.3249 1.63788 11.0879 1.63788H2.91211ZM2.91211 2.79999H11.0879C11.1608 2.79999 11.2337 2.81822 11.3066 2.85468C11.3796 2.88202 11.4434 2.92303 11.498 2.97772C11.5527 3.0233 11.5938 3.08254 11.6211 3.15546C11.6484 3.22837 11.6621 3.30585 11.6621 3.38788V11.55C11.6621 11.632 11.6484 11.7095 11.6211 11.7824C11.5938 11.8462 11.5527 11.9055 11.498 11.9601C11.4434 12.0148 11.3796 12.0604 11.3066 12.0969C11.2337 12.1242 11.1608 12.1379 11.0879 12.1379H2.91211C2.83919 12.1379 2.76628 12.1242 2.69336 12.0969C2.62044 12.0604 2.55664 12.0148 2.50195 11.9601C2.44727 11.9055 2.40625 11.8462 2.37891 11.7824C2.35156 11.7095 2.33789 11.632 2.33789 11.55V3.38788C2.33789 3.30585 2.35156 3.22837 2.37891 3.15546C2.40625 3.08254 2.44727 3.0233 2.50195 2.97772C2.55664 2.92303 2.62044 2.88202 2.69336 2.85468C2.76628 2.81822 2.83919 2.79999 2.91211 2.79999Z" />
		<path
			class="indeterminate"
			d="M2.91211 1.63788C2.67513 1.63788 2.44727 1.68345 2.22852 1.7746C2.01888 1.86574 1.83659 1.98879 1.68164 2.14374C1.52669 2.3078 1.39909 2.49465 1.29883 2.70428C1.20768 2.91392 1.16211 3.14178 1.16211 3.38788V11.55C1.16211 11.787 1.20768 12.0148 1.29883 12.2336C1.38997 12.4432 1.51758 12.6255 1.68164 12.7805C1.83659 12.9445 2.01888 13.0721 2.22852 13.1633C2.44727 13.2544 2.67513 13.3 2.91211 13.3H11.0879C11.3249 13.3 11.5482 13.2544 11.7578 13.1633C11.9766 13.0721 12.1634 12.9445 12.3184 12.7805C12.4733 12.6255 12.5964 12.4432 12.6875 12.2336C12.7878 12.0148 12.8379 11.787 12.8379 11.55V3.38788C12.8379 3.14178 12.7923 2.91392 12.7012 2.70428C12.61 2.48553 12.4824 2.29869 12.3184 2.14374C12.1634 1.98879 11.9766 1.86574 11.7578 1.7746C11.5482 1.68345 11.3249 1.63788 11.0879 1.63788H2.91211ZM2.91211 2.79999H11.0879C11.1608 2.79999 11.2337 2.81822 11.3066 2.85468C11.3796 2.88202 11.4434 2.92303 11.498 2.97772C11.5527 3.0233 11.5938 3.08254 11.6211 3.15546C11.6484 3.22837 11.6621 3.30585 11.6621 3.38788V11.55C11.6621 11.632 11.6484 11.7095 11.6211 11.7824C11.5938 11.8462 11.5527 11.9055 11.498 11.9601C11.4434 12.0148 11.3796 12.0604 11.3066 12.0969C11.2337 12.1242 11.1608 12.1379 11.0879 12.1379H2.91211C2.83919 12.1379 2.76628 12.1242 2.69336 12.0969C2.62044 12.0604 2.55664 12.0148 2.50195 11.9601C2.44727 11.9055 2.40625 11.8462 2.37891 11.7824C2.35156 11.7095 2.33789 11.632 2.33789 11.55V3.38788C2.33789 3.30585 2.35156 3.22837 2.37891 3.15546C2.40625 3.08254 2.44727 3.0233 2.50195 2.97772C2.55664 2.92303 2.62044 2.88202 2.69336 2.85468C2.76628 2.81822 2.83919 2.79999 2.91211 2.79999ZM4.66211 8.04999H9.33789C9.49284 8.04999 9.625 7.9953 9.73438 7.88593C9.85286 7.76744 9.91211 7.62616 9.91211 7.4621C9.91211 7.30715 9.85286 7.17499 9.73438 7.06561C9.625 6.94712 9.49284 6.88788 9.33789 6.88788H4.66211C4.50716 6.88788 4.37044 6.94712 4.25195 7.06561C4.14258 7.17499 4.08789 7.30715 4.08789 7.4621C4.08789 7.62616 4.14258 7.76744 4.25195 7.88593C4.37044 7.9953 4.50716 8.04999 4.66211 8.04999Z"
			fill="#1E60FA" />
	</svg>
</div>
<label>
	<ng-content></ng-content>
</label>
