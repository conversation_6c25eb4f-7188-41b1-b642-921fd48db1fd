// @ts-ignore
import Notes from "./ds3-checkbox.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../../ds3.module";
import { FormControl, FormGroup, Validators } from "@angular/forms";

const description = () => Notes;

export default {
	title: "Design System 3 | Inputs/Checkbox",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Checkbox = () => ({
	template: `
	<div style="padding: 1rem">
	<ds3-checkbox [indeterminate]="indeterminate" [disabled]="disabled"> {{ label }} </ds3-checkbox>
</div>
    `,
	props: {
		indeterminate: boolean("Indeterminado", false),
		disabled: boolean("Desabilitado", false),
		label: text("Label", "Checkbox"),
		form: new FormGroup(
			{ control: new FormControl("", Validators.required) },
			{ updateOn: "blur" }
		),
	},
});
