@import "../../../../../assets/import.scss";
@import "../../../../../assets/ui-kit.scss";

.ds3-checkbox {
	--checkbox-check-color: var(
		--color-action-default-able-4,
		hsla(222, 95%, 55%, 1)
	);
	--checkbox-disabled-color: var(
		--color-action-default-disable-2,
		hsla(222, 5%, 50%, 1)
	);
	--checkbox-label-color: var(
		--color-typography-default-title,
		hsla(222, 5%, 35%, 1)
	);
	--checkbox-size: 0.875rem;
	--checkbox-gap: 0.5rem;
	--checkbox-disabled-cursor: var(--form-field-disabled-cursor, default);
}

.ds3-checkbox {
	display: inline-flex;
	align-items: center;
	gap: var(--checkbox-gap);

	&:focus-visible {
		background-color: var(--color-support-gray-2);
		border: 2px solid var(--color-support-gray-2);
	}

	.checkbox-mark {
		display: flex;
		align-items: center;
		justify-content: center;
		width: var(--checkbox-size);
		height: var(--checkbox-size);
		cursor: pointer;

		svg {
			width: 100%;
			height: 100%;

			path {
				fill: var(--checkbox-check-color);
				display: none;
			}

			.scale03 {
				scale: 0.03;
			}
		}
	}

	input {
		display: none;
	}

	input:checked:not(:indeterminate) ~ .checkbox-mark svg .check,
	input:not(:checked):not(:indeterminate) ~ .checkbox-mark svg .uncheck,
	input:indeterminate ~ .checkbox-mark svg .indeterminate {
		display: block;
	}

	input[disabled]:not([disabled="false"]) ~ .checkbox-mark {
		cursor: var(--checkbox-disabled-cursor);

		svg {
			path {
				fill: var(--checkbox-disabled-color);
			}
		}
	}

	label {
		color: var(--checkbox-label-color);
		@include apply-typography-style("body", 2);
		margin-bottom: 0;
		padding: 0;
	}

	input[disabled]:not([disabled="false"]) ~ label {
		cursor: var(--checkbox-disabled-cursor);
	}
}
