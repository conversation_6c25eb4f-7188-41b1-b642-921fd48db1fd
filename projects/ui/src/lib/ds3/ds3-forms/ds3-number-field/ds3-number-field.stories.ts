// @ts-ignore
import Notes from "./ds3-number-field.notes.md";
import { moduleMetadata } from "@storybook/angular";
import {
	boolean,
	number,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import { Ds3Module } from "../../ds3.module";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
} from "@angular/forms";

export default {
	title: "Design System 3 | Inputs/Number Field",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, FormsModule, ReactiveFormsModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const interactive = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>{{label}}</ds3-field-label>
					<ds3-number-field
						ds3Input
						[steps]="steps"
						[min]="min"
						[max]="max"
						formControlName="number"
					>
					</ds3-number-field>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		label: text("Label:", "Number field"),
		steps: number("Steps:", 1),
		min: number("Min:", 0),
		max: number("Max:", undefined),
		keyboardMode: select("Keyboard Mode", ["numeric", "default"], "default"),
		form: new FormGroup({
			number: new FormControl(4),
		}),
	},
});

export const customSteps = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>Custom steps</ds3-field-label>
					<ds3-number-field
						ds3Input
						[steps]="steps"
						[min]="min"
						[max]="max"
						formControlName="number"
					>
					</ds3-number-field>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		steps: number("Steps:", 10),
		min: number("Min:", 0),
		max: number("Max:", 100),
		form: new FormGroup({
			number: new FormControl(10),
		}),
	},
});

export const noMinMaxValues = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>Field without min or max values</ds3-field-label>
					<ds3-number-field
						ds3Input
						formControlName="number"
					>
					</ds3-number-field>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			number: new FormControl(10),
		}),
	},
});

export const disabled = () => ({
	template: `
		<div style="padding: 16px;">
			<form [formGroup]="form">
				<ds3-form-field>
					<ds3-field-label>Custom steps</ds3-field-label>
					<ds3-number-field
						ds3Input
						formControlName="number"
					>
					</ds3-number-field>
				</ds3-form-field>
			</form>
		</div>
    `,
	props: {
		form: new FormGroup({
			number: new FormControl({ value: 10, disabled: true }),
		}),
	},
});
