# Number Field

**Seletor:** `ds3-number-field`

## Uso Básico

```html
<ds3-form-field>
  <ds3-number-field
    ds3Input
    [formControl]="formControl"
  ></ds3-number-field>
</ds3-form-field>
```

**Seletor:** `ds3-number-field`

**Utilização:**`<ds3-number-field></ds3-number-field>`

`<ds3-number-field>` é um FormControl para permitir a entrada de um número pelo usuário, com botões para incrementar ou decrementar o valor.

Este componente foi projetado para funcionar dentro de um elemento `<ds3-form-field>`. Portanto deve ser usada a diretiva `<ds3-number-field ds3Input>` ao referenciar o componente.

O componente pode receber dados de formularios Template Driven e Reactive Forms.

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  number = new FormControl(0)
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-form-field>
  <ds3-field-label>Select</ds3-field-label>
  <ds3-number-field [formControl]="number" ds3Input>
  </ds3-number-field>
  <ds3-helper-message>
    This is the helper
  </ds3-helper-message>
</ds3-form-field>

```
&nbsp;  

## Props / Inputs

| Property                                      | Values              | Default              |
|---                                            |                  ---|                   ---|
| @Input() min: number or undefined             |                     |'Selecione um item'   |
| @Input() max: number or undefined             |                     |[]                    |
| @Input() mask: string or undefined            |                     |[]                    |
| @Input() keyboardMode: string                 | 'default', 'numeric'|'default'             |
| @Input() steps: number                        |                     |1                     |
| @Input() disabled: boolean                    |                     |false                 |

## Outputs / Events

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Output() change: EventEmitter           |-                    |-                     |
| @Output() increase: EventEmitter         |-                    |-                     |
| @Output() decrease: EventEmitter         |-                    |-                     |



## More info

No more info.

&nbsp;  
