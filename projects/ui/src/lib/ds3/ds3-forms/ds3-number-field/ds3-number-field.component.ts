import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	forwardRef,
} from "@angular/core";
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from "@angular/forms";
@Component({
	selector: "ds3-number-field",
	templateUrl: "./ds3-number-field.component.html",
	styleUrls: ["./ds3-number-field.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3NumberFieldComponent),
			multi: true,
		},
	],
})
export class Ds3NumberFieldComponent implements OnInit, ControlValueAccessor {
	value: number;
	type: "tel" | "number" = "number";

	public onChanged: Function;
	public onTouched: Function;

	@Input()
	public min: number | undefined = undefined;

	@Input()
	public max: number | undefined = undefined;

	@Input()
	public keyboardMode: "default" | "numeric" = "default";

	@Input()
	public disabled: boolean = false;

	@Input()
	public steps: number = 1;

	@Input()
	public id: string = "";

	@Output()
	public increase = new EventEmitter();

	@Output()
	public decrease = new EventEmitter();

	@Output()
	public change = new EventEmitter();

	constructor() {}

	ngOnInit() {
		this.setInputType();
	}

	registerOnChange(angularProvidedFunction: any): void {
		this.onChanged = angularProvidedFunction;
	}

	registerOnTouched(angularProvidedFunction: any): void {
		this.onTouched = angularProvidedFunction;
	}

	setDisabledState(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	writeValue(value: number): void {
		if (value < this.min) {
			this.value = this.min;
			return;
		}
		if (value > this.max) {
			this.value = this.max;
			return;
		}
		this.value = +value;
	}

	private updateValue(newValue: number): void {
		this.writeValue(newValue);
		this.onChanged(newValue);
		this.onTouched();
		this.change.emit();
	}

	private setInputType(): void {
		if (this.keyboardMode === "default") {
			this.type = "number";
			return;
		}
		if (this.keyboardMode === "numeric") {
			this.type = "tel";
			return;
		}
	}

	public increaseValue() {
		if (this.disabled) return;

		const increasedValue = +this.value + this.steps;

		if (increasedValue > this.max) return;

		this.updateValue(increasedValue);
		this.increase.emit();
	}

	public decreaseValue() {
		if (this.disabled) return;

		const decreasedValue = +this.value - this.steps;

		if (decreasedValue < this.min) return;

		this.updateValue(decreasedValue);
		this.decrease.emit();
	}

	isDecreasingAllowed() {
		if (this.min === undefined) {
			return true;
		}
		return this.value - this.steps >= this.min;
	}

	isIncreasingAllowed() {
		if (this.max === undefined) {
			return true;
		}
		return this.value + this.steps <= this.max;
	}

	checkValueOnKeyup() {
		if (+this.value > +this.max) {
			this.updateValue(this.max);
		}
		if (+this.value < +this.min) {
			this.updateValue(this.min);
		} else {
			this.onChanged(this.value);
		}
	}
}
