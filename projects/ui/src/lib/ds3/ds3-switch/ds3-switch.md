<!-- # Switch

`selector: pct-ds3-cat-switch`

`Basic usage: <pct-ds3-cat-switch></pct-ds3-cat-switch>`

`Basic example:`

```ts
import { Component, NgModule } from '@angular/core';
import { CatSwitchModule } from 'ui-kit';

@NgModule({
    declarations: [...],
    imports: [
        ...,
	    CatSwitchModule,
        ...
    ]
})
class MeuAppModule {
}
```

**meu-app.module.ts**

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
}
```

**meu-exemplo-de-tela.component.ts**

```html

<pct-ds3-cat-switch>Label</pct-ds3-cat-switch>
```

**meu-exemplo-de-tela.component.html**

`Form control example:`

```ts
import { Component, NgModule } from '@angular/core';
import { CatSwitchModule } from 'ui-kit';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
	declarations: [...],
	imports: [
		...,
	CatSwitchModule,
	ReactiveFormsModule,
	...
]
})
class MeuAppModule {
}
```

**meu-app.module.ts**

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';
import { FormControl } from '@angular/forms';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	formControl: FormControl = new FormControl();
}
```

**meu-exemplo-de-tela.component.ts**

```html

<pct-ds3-cat-switch [formControl]="formControl">Label</pct-ds3-cat-switch>
```

**meu-exemplo-de-tela.component.html**

`Form group example:`

```ts
import { Component, NgModule } from '@angular/core';
import { CatSwitchModule } from 'ui-kit';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
    declarations: [...],
    imports: [
        ...,
	    CatSwitchModule,
	    ReactiveFormsModule,
        ...
    ]
})
class MeuAppModule {
}
```

**meu-app.module.ts**

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	formGroup: FormGroup = new FormGroup({
      control: new FormControl();
    });
}
```

**meu-exemplo-de-tela.component.ts**

```html

<form [formGroup]="formGroup">
	<pct-ds3-cat-switch formControlName="control">Label</pct-ds3-cat-switch>
</form>
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>checked <span class="bubble-sm info">optional</span></td>
            <td>
               Defines if the switch is checked or not.
            </td>
            <td>```boolean```</td>
            <td>false</td>
        </tr>
        <tr>
            <td>size <span class="bubble-sm info">optional</span></td>
            <td>
               Defines the button size [Sizes table](#sizeTable)
            </td>
            <td>```string```</td>
            <td>sm</td>
        </tr>
        <tr>
            <td>labelPosition <span class="bubble-sm info">optional</span></td>
            <td>
               Defines the switch label position [Label position table](#labelPosition)
            </td>
            <td>```string```</td>
            <td>before</td>
        </tr>
        <tr>
            <td>disabled <span class="bubble-sm info">optional</span></td>
            <td>
               Defines if the switch is disabled or not
            </td>
            <td>```boolean```</td>
            <td>false</td>
        </tr>
    </tbody>
</table>

## Outputs / Events

<table class="full">
    <thead>
        <tr>
            <th>Output</th>
            <th>Description</th>
            <th>Type</th>
            <th>Return value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>checkedChange</td>
            <td>
               Emits every time that the switch is checked or unchecked.  
            </td>
            <td>```Boolean```</td>
            <td>`'true' | 'false'`</td>
        </tr>
    </tbody>
</table>

## More info

<h3 id="sizeTable">Size table</h3>

<table class="custom">
    <thead>
        <tr>
            <th>Size</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>lg</td>
        </tr>
        <tr>
            <td>sm</td>
        </tr>
    </tbody>
</table>

<h3 id="labelPosition">Label position table</h3>

<table class="custom">
    <thead>
        <tr>
            <th>Label position</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>before</td>
        </tr>
        <tr>
            <td>after</td>
        </tr>
    </tbody>
</table>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style> -->
