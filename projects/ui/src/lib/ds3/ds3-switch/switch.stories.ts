// import { moduleMetadata, storiesOf } from '@storybook/angular';
// import { boolean, select, text, withKnobs } from '@storybook/addon-knobs';
// import Notes from './switch.md';
// import { CatSwitchModule } from 'ui-kit';
// import { iconsName } from '../stories-utils';
// import { AfterViewInit, Component, Input, OnChanges } from '@angular/core';

// private const SIZES = [
// 	{
// 		id: 'sm',
// 		label: 'Small'
// 	},
// 	{
// 		id: 'lg',
// 		label: 'Large'
// 	},
// ];

// @Component({
// 	selector: 'pacto-cat-host',
// 	template: `
// 		<div class="content">
// 				<pacto-ds3-cat-switch
// 						[disabled]="disabled"
// 						[size]="size.id"
// 						[labelPosition]="labelPosition"
// 						[checked]="checked"
// 				>
// 						{{label}}
// 				</pacto-ds3-cat-switch>
// 		</div>
// 	`,
// 	styleUrls: ['switch.scss']
// })
// class HostComponent implements AfterViewInit, OnChanges {
// 	@Input() size;
// 	@Input() disabled: boolean;
// 	@Input() label: string;
// 	@Input() labelPosition: 'before' | 'after';
// 	@Input() checked: boolean
// }

// storiesOf('Design System 3 | Actions/Switch', module)
// 	.addDecorator(moduleMetadata({
// 		imports: [
// 			CatSwitchModule
// 		],
// 	}))
// 	.addDecorator(withKnobs)
// 	.addParameters({
// 		notes: { Notes },
// 	})
// 	.add('Switch', () => {
// 		return {
// 			component: HostComponent,
// 			props: {
// 				label: text('Label', 'Text Button'),
// 				size: select('Size', SIZES, SIZES[0]),
// 				disabled: boolean('Disabled', false),
// 				checked: boolean('Checked', false),
// 				labelPosition: select('Icon position', ['before', 'after'], 'before'),
// 			}
// 		};
// 	});
