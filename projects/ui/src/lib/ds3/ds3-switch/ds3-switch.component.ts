import {
	Component,
	ElementRef,
	EventEmitter,
	HostBinding,
	Input,
	OnChanges,
	OnInit,
	Optional,
	Output,
	Renderer2,
	SimpleChanges,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import {
	ControlContainer,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";

let uniqueId = 0;

@Component({
	selector: "ds3-switch",
	templateUrl: "./ds3-switch.component.html",
	styleUrls: ["./ds3-switch.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: Ds3SwitchComponent,
			multi: true,
		},
	],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3SwitchComponent implements OnInit, OnChanges {
	@HostBinding("class.pct-ds3-switch")
	hostClass: boolean = true;

	@Input() formControlName = "";
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective | undefined;
	@Input()
	formControl: FormControl | undefined;

	@ViewChild("content", { static: false }) contentElement: ElementRef<any>;

	@Input() id: string;
	@Input() disabled: boolean = false;
	@Input() size: "lg" | "sm" = "sm";

	@Input() labelPosition: "before" | "after" = "before";
	@Input() checked: boolean;
	@Output() checkedChange: EventEmitter<boolean> = new EventEmitter<boolean>();

	get control(): any {
		return (
			this.formControl ||
			(this.controlContainer &&
				this.controlContainer.control &&
				this.controlContainer.control.get(this.formControlName))
		);
	}

	constructor(
		@Optional()
		private controlContainer: ControlContainer,
		private elementRef: ElementRef,
		private renderer2: Renderer2
	) {}

	ngOnInit(): void {
		if (!this.id) {
			this.id = `cat-slide-toggle-${uniqueId++}`;
		}
		this.addDisabled(this.disabled);
		this.addSizeClasses();
	}

	ngOnChanges(changes: SimpleChanges) {
		const disabled = changes.disabled;
		if (disabled && !disabled.firstChange) {
			this.addDisabled(disabled.currentValue);
		} else {
			this.addDisabled(false);
		}

		const size = changes.size;
		if (size && !size.firstChange) {
			this.addSizeClasses(size.previousValue);
		}

		const labelPosition = changes.labelPosition;
		if (labelPosition && !labelPosition.firstChange) {
			this.labelPosition = labelPosition.currentValue;
			this.changeLabelPosition();
		}
	}

	addDisabled(value?: boolean) {
		if (
			value === undefined &&
			this.elementRef.nativeElement.hasAttribute("disabled")
		) {
			const disabledValue =
				this.elementRef.nativeElement.getAttribute("disabled");
			this.disabled = disabledValue === "" || disabledValue === "true";
		} else {
			this.disabled = value;
			this.renderer2.setAttribute(
				this.elementRef.nativeElement,
				"disabled",
				`${value}`
			);
		}
	}

	ngAfterViewInit() {
		this.changeLabelPosition();
	}

	registerOnTouched(fn: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.registerOnTouched(fn);
		}
	}

	registerOnChange(fn: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.registerOnChange(fn);
		}
	}

	writeValue(obj: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.writeValue(obj);
		}
	}

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
		}
		this.addDisabled(isDisabled);
	}

	onChecked() {
		if (!this.disabled) {
			this.checked = !this.checked;
			if (this.control) {
				this.control.setValue(this.checked);
			}
			this.checkedChange.emit(this.checked);
		}
	}

	private addSizeClasses(previousValue?: "lg" | "sm") {
		if (previousValue) {
			this.renderer2.removeClass(
				this.elementRef.nativeElement,
				`pct-cat-switch-${previousValue}`
			);
		}
		this.renderer2.addClass(
			this.elementRef.nativeElement,
			`pct-cat-switch-${this.size}`
		);
	}

	private changeLabelPosition() {
		if (this.contentElement) {
			const toggleDiv =
				this.elementRef.nativeElement.getElementsByClassName("cat-toggle")[0];
			toggleDiv.style.marginRight = "8px";
			toggleDiv.style.marginLeft = "unset";
			this.contentElement.nativeElement.style.display = "inline";

			if (this.labelPosition === "before") {
				toggleDiv.before(this.contentElement.nativeElement);
				toggleDiv.style.marginLeft = "8px";
				toggleDiv.style.marginRight = "unset";
			} else {
				toggleDiv.after(this.contentElement.nativeElement);
			}
		}
	}
}
