@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/scss/cores.vars";

.pct-ds3-switch {
	width: fit-content;
	display: inline-block;

	label {
		cursor: pointer;
		display: flex;
		align-items: center;
		color: $typeDefaultText;
		margin-bottom: unset;

		.cat-toggle {
			position: relative;
			background: $supportGray05;
			border-radius: 16px;
			transition: all 0.3s ease-in;
			display: flex;
			align-items: center;
			border: 1px solid $supportGray03;
			box-sizing: content-box;

			.cat-toggle-switch {
				border-radius: 50%;
				background: $plane02;
				transition: all 0.3s ease;
				transform: translateX(1.33px);
			}

			&.checked {
				background-color: $actionDefaultAble04;
			}
		}
	}

	&:not([disabled="false"]) {
		label {
			cursor: not-allowed;

			.cat-toggle {
				background: $actionDefaultDisabled01;
				border-color: $plane03;
			}
		}
	}

	&[disabled="false"] {
		label {
			.cat-toggle {
				&:hover:not(.checked) {
					background: $actionDefaultDisabled01;
					border-color: $actionDefaultDisabled02;
				}

				&.checked {
					&:hover {
						background: $actionDefaultAble03;
					}
				}
			}
		}
	}

	&.pct-cat-switch-sm {
		label {
			.cat-toggle {
				height: 16px;
				width: 32px;

				.cat-toggle-switch {
					height: 13.33px;
					width: 13.33px;
				}

				&.checked {
					.cat-toggle-switch {
						transform: translateX(17.33px);
					}
				}
			}
		}
	}

	&.pct-cat-switch-lg {
		label {
			.cat-toggle {
				height: 24px;
				width: 48px;

				.cat-toggle-switch {
					height: 20px;
					width: 20px;
				}

				&.checked {
					.cat-toggle-switch {
						transform: translateX(26px);
					}
				}
			}
		}
	}
}
