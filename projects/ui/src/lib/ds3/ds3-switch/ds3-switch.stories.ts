// @ts-ignore
import Notes from "./ds3-switch.md";
import { moduleMetadata } from "@storybook/angular";
import { boolean, select, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";

const SIZES = [
	{
		id: "sm",
		label: "Small",
	},
	{
		id: "lg",
		label: "Large",
	},
];

const description = () => Notes;

export default {
	title: "Design System 3 | Actions/Switch",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Switch = () => ({
	template: `
	<div style="padding: 1rem">
	<ds3-switch
	[disabled]="disabled"
	[size]="size.id"
	[labelPosition]="labelPosition"
	[checked]="checked"
>
	{{label}}
</ds3-switch>
</div>
    `,
	props: {
		label: text("Label", "Text Button"),
		size: select("Size", SIZES, SIZES[0]),
		disabled: boolean("Disabled", false),
		checked: boolean("Checked", false),
		labelPosition: select("Icon position", ["before", "after"], "before"),
	},
});
