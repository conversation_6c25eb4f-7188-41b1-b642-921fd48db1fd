// import { AfterViewInit, Component, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
// import { CatToastrModule, Ds3CatButtonModule } from 'ui-kit';
// import { moduleMetadata, storiesOf } from '@storybook/angular';
// import { select, text, withKnobs } from '@storybook/addon-knobs';
// import Notes from './fixed-toast.md';
// import { ToastContainerDirective, ToastrService } from 'ngx-toastr';

// const TOAST_LEVEL = ['warning', 'success', 'error', 'info'];

// @Component({
// 	selector: 'pacto-cat-host',
// 	template: `
// 		<div class="content">
// 			<button pct-flat-button color="primary" (click)="showToastr()" style="margin-bottom: 8px">Show toast</button>
// 			<div #toastContainer toastContainer></div>
// 		</div>
// 	`,
// 	styleUrls: ['fixed-toast.scss', '../../../stories.scss']
// })
// class HostComponent implements AfterViewInit, OnChanges {

// 	@Input() title: string;
// 	@Input() message: string;
// 	@Input() toastrLevel: 'warning' | 'success' | 'error' | 'info';
// 	@ViewChild('toastContainer', { read: ToastContainerDirective,  static: true }) toastContainer: ToastContainerDirective;

// 	constructor(
// 		private toastrService: ToastrService
// 	) {
// 	}

// 	ngAfterViewInit() {
// 	}

// 	ngOnChanges(changes: SimpleChanges) {
// 	}

// 	showToastr() {
// 		this.toastrService.overlayContainer = this.toastContainer;
// 		this.toastrService.toastrConfig.positionClass = 'pct-toastr-inline';
// 		this.toastrService[this.toastrLevel](this.message, this.title, {
// 		});
// 	}

// }

// storiesOf('Design System 3 | Feedback/Toast', module)
// 	.addDecorator(moduleMetadata({
// 		imports: [
// 			CatToastrModule,
// 			Ds3CatButtonModule
// 		]
// 	}))
// 	.addDecorator(withKnobs)
// 	.addParameters({
// 		notes: { Notes }
// 	})
// 	.add('Fixed', () => ({
// 			component: HostComponent,
// 			props: {
// 				message: text('Message', 'Here goes the message'),
// 				toastrLevel: select('Level', TOAST_LEVEL, TOAST_LEVEL[0])
// 			}
// 		}
// 	));
