// import { AfterViewInit, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
// import { CatToastrModule, Ds3CatButtonModule } from 'ui-kit';
// import { moduleMetadata, storiesOf } from '@storybook/angular';
// import { select, text, withKnobs } from '@storybook/addon-knobs';
// import Notes from './float-toast.md';
// import { ToastrService } from 'ngx-toastr';

// const TOASTR_POSITIONS = [
// 	{
// 		id: 'toast-top-right',
// 		label: 'Top right'
// 	},
// 	{
// 		id: 'toast-top-left',
// 		label: 'Top left'
// 	},
// 	{
// 		id: 'toast-bottom-left',
// 		label: 'Bottom left'
// 	},
// 	{
// 		id: 'toast-bottom-right',
// 		label: 'Bottom right'
// 	},
// 	{
// 		id: 'toast-top-full-width',
// 		label: 'Top full size'
// 	},
// 	{
// 		id: 'toast-bottom-full-width',
// 		label: 'Bottom full size'
// 	},
// 	{
// 		id: 'toast-top-center',
// 		label: 'Top center'
// 	},
// 	{
// 		id: 'toast-bottom-center',
// 		label: 'Bottom center'
// 	}
// ];

// const TOAST_LEVEL = ['warning', 'success', 'error', 'info'];

// @Component({
// 	selector: 'pacto-cat-host',
// 	template: `
// 		<div class="content">
// 			<button pct-flat-button color="primary" (click)="showToastr()">Show toast</button>
// 		</div>
// 	`,
// 	styleUrls: ['float-toast.scss']
// })
// class HostComponent implements AfterViewInit, OnChanges {

// 	@Input() title: string;
// 	@Input() message: string;
// 	@Input() toastrPosition: { id: string, label: string } = TOASTR_POSITIONS[0];
// 	@Input() toastrLevel: 'warning' | 'success' | 'error' | 'info';

// 	constructor(
// 		private toastrService: ToastrService
// 	) {
// 	}

// 	ngAfterViewInit() {
// 	}

// 	ngOnChanges(changes: SimpleChanges) {
// 	}

// 	showToastr() {
// 		this.toastrService[this.toastrLevel](this.message, this.title, {
// 			positionClass: this.toastrPosition.id,
// 		});
// 	}

// }

// storiesOf('Design System 3 | Feedback/Toast', module)
// 	.addDecorator(moduleMetadata({
// 		imports: [
// 			CatToastrModule,
// 			Ds3CatButtonModule
// 		]
// 	}))
// 	.addDecorator(withKnobs)
// 	.addParameters({
// 		notes: { Notes }
// 	})
// 	.add('Float', () => ({
// 			component: HostComponent,
// 			props: {
// 				title: text('Title', 'Title'),
// 				message: text('Message', 'Here goes the message'),
// 				toastrPosition: select('Position', TOASTR_POSITIONS, TOASTR_POSITIONS[0]),
// 				toastrLevel: select('Level', TOAST_LEVEL, TOAST_LEVEL[0])
// 			}
// 		}
// 	));
