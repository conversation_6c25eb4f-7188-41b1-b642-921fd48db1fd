import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ToastContainerModule, ToastrModule } from "ngx-toastr";
import { Ds3ToastrComponent } from "./ds3-toastr.component";

@NgModule({
	declarations: [Ds3ToastrComponent],
	imports: [
		CommonModule,
		ToastContainerModule,
		ToastrModule.forRoot({
			toastComponent: Ds3ToastrComponent,
			tapToDismiss: true,
			toastClass: "pct-toastr",
			maxOpened: 5,
			closeButton: true,
			progressBar: false,
			iconClasses: {
				success: "pct-toastr-success cor-plano02",
				error: "pct-toastr-error cor-plano02",
				warning: "pct-toastr-warning cor-plano02",
				info: "pct-toastr-info cor-plano02",
			},
		}),
	],
	exports: [Ds3ToastrComponent, ToastContainerModule],
	entryComponents: [Ds3ToastrComponent],
})
export class Ds3ToastrModule {}
