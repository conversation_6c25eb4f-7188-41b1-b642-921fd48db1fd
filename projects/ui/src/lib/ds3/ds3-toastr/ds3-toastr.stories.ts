// @ts-ignore
import Notes from "./ds3-toastr.md";
import { moduleMetadata } from "@storybook/angular";
import { select, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3Module } from "../ds3.module";
import {
	AfterViewInit,
	Component,
	Input,
	OnChanges,
	SimpleChanges,
	ViewChild,
} from "@angular/core";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

const TOAST_LEVEL = ["warning", "success", "error", "info"];
const TOASTR_POSITIONS = [
	{ id: "toast-top-right", label: "Top right" },
	{ id: "toast-top-left", label: "Top left" },
	{ id: "toast-bottom-left", label: "Bottom left" },
	{ id: "toast-bottom-right", label: "Bottom right" },
	{ id: "toast-top-full-width", label: "Top full size" },
	{ id: "toast-bottom-full-width", label: "Bottom full size" },
	{ id: "toast-top-center", label: "Top center" },
	{ id: "toast-bottom-center", label: "Bottom center" },
];
const description = () => Notes;

export default {
	title: "Design System 3 | Feedback/Toast",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module, BrowserAnimationsModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Fixed = () => ({
	component: fixedToastr,
	props: {
		message: text("Message", "mensagem"),
		toastrLevel: select("Level", TOAST_LEVEL, TOAST_LEVEL[0]),
	},
});
export const Float = () => ({
	component: floatToastr,
	props: {
		title: text("Title", "Title"),
		message: text("Message", "Here goes the message"),
		toastrPosition: select("Position", TOASTR_POSITIONS, TOASTR_POSITIONS[0]),
		toastrLevel: select("Level", TOAST_LEVEL, TOAST_LEVEL[0]),
	},
});

@Component({
	selector: "fixedtoastr",
	template: `
		<div class="content">
			<button
				pct-flat-button
				color="primary"
				(click)="showToastr()"
				style="margin-bottom: 8px">
				Show toast
			</button>
			<div #toastContainer toastContainer></div>
		</div>
	`,
})
class fixedToastr implements AfterViewInit, OnChanges {
	@Input() title: string;
	@Input() message: string;
	@Input() toastrLevel: "warning" | "success" | "error" | "info";
	@ViewChild("toastContainer", { read: ToastContainerDirective, static: true })
	toastContainer: ToastContainerDirective;

	constructor(private toastrService: ToastrService) {}

	ngAfterViewInit() {}

	ngOnChanges(changes: SimpleChanges) {}

	showToastr() {
		this.toastrService.overlayContainer = this.toastContainer;
		this.toastrService.toastrConfig.positionClass = "pct-toastr-inline";
		this.toastrService[this.toastrLevel](this.message, this.title, {});
	}
}

@Component({
	selector: "floattoastr",
	template: `
		<div class="content">
			<button pct-flat-button color="primary" (click)="showToastr()">
				Show toast
			</button>
		</div>
	`,
})
class floatToastr implements AfterViewInit, OnChanges {
	@Input() title: string;
	@Input() message: string;
	@Input() toastrPosition: { id: string; label: string } = TOASTR_POSITIONS[0];
	@Input() toastrLevel: "warning" | "success" | "error" | "info";

	constructor(private toastrService: ToastrService) {}

	ngAfterViewInit() {}

	ngOnChanges(changes: SimpleChanges) {}

	showToastr() {
		this.toastrService[this.toastrLevel](this.message, this.title, {
			positionClass: this.toastrPosition.id,
		});
	}
}
