<div class="pct-toastr-content-container">
	<div class="pct-toastr-icon"></div>
	<div class="pct-toastr-content">
		<div class="pct-toastr-title-section">
			<div *ngIf="title" [attr.aria-label]="title" [class]="options.titleClass">
				{{ title }}
				<ng-container *ngIf="duplicatesCount">
					[{{ duplicatesCount + 1 }}]
				</ng-container>
			</div>
		</div>
		<div
			*ngIf="message && options.enableHtml"
			[class]="options.messageClass"
			[innerHTML]="message"
			role="alert"></div>
		<div
			*ngIf="message && !options.enableHtml"
			[attr.aria-label]="message"
			[class]="options.messageClass"
			role="alert">
			{{ message }}
		</div>
	</div>
</div>
<div
	(click)="remove()"
	*ngIf="options?.closeButton"
	class="pct-toastr-close-section">
	<i aria-hidden="true" class="pct pct-x"></i>
</div>
<div *ngIf="options.progressBar">
	<div [style.width]="width + '%'" class="toast-progress"></div>
</div>
