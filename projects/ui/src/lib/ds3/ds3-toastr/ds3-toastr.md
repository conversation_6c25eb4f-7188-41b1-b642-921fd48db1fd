# Toastr

**Table of content:**

-   [Selectors](#selectors)
    -   [float](#seletor_float)
    -   [fixed](#seletor_fixed)
-   [Inputs](#inputs)
-   [Outputs](#outputs)
-   [Example](#example)
-   [Extra Info](#extra)

## selectors

as chamadas dos toastr's s]ão feitas diferentes para os dois formatos de exibição

### seletor_float

dentro do seu service.ts é nescessario importar o pacote do ngx-toastr e já está configurado no padrão do ds3

```ts
import { ToastrService } from 'ngx-toastr';
...
constructor( private toastrService: ToastrService ) {}
```

e depois é feito a chamada da seguinte forma

```ts
this.toastrService.success('Message', 'Title');
```

### seletor_fixed

para o toastr fixed é obrigatorio inserir dentro do html aonde vai ficar o toastr e modificar a configuração positionClass como ds3-toastr-inline durante a chamada dentro do arquivo ts da seguinte forma:

```ts
import { ToastrService, ToastContainerDirective } from 'ngx-toastr';
import { Component } from '@angular/core';
...
	@ViewChild('container', { read: ToastContainerDirective, static: true })
	toastContainer: ToastContainerDirective;
	constructor(private toastrService: ToastrService) {}

	showToastr() {
		this.toastrService.overlayContainer = this.toastContainer;
		this.toastrService.toastrConfig.positionClass = 'pct-toastr-inline';
		this.toastrService.success('Message');
	}
}
```

```html
<button (click)="showToastr()">Show toastr</button>
<div toastContainer #container></div>
```

## inputs
|  diretiva para funcionar toastFixed |
|---|
|  toastContainer |

## outputs

| Nome método   | valores esperados                                                                                                    | descricao                                | retorno                                  |
| ------------- | -------------------------------------------------------------------------------------------------------------------- | ---------------------------------------- | ---------------------------------------- |
| clear         | **opcional** toastId : number                                                                                        | remove todos toastr ou apenas um por id  | void                                     |
| remove        | **obrigatório** toastId : number                                                                                     | remove e destroi um unico toastr pelo id | boolean                                  |
| findDuplicate | **obrigatório** message: string, **obrigatório** resetOnDuplicate: boolean, **obrigatório** countDuplicates: boolean | indica se um toastr já foi apresentado   | ActiveToast<any> (informações do toast ) |

## example

chamando um toastr-float para informar sucesso no salvar dos dados

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	constructor(private toastrService: ToastrService) {}

	sucessToastr() {
		// lembrando que primeiro vem a mensagem e depois o titulo
		this.toastrService.success('Dados Salvos Com Sucesso', 'Êxito');
	}
}
```

> meu-exemplo-de-tela.component.html

```html
<button (click)="sucessToastr()">
	Mostrar toastr
</button>
```

---

chamando um toastr-float para informar falha no salvar dos dados

```ts
import { Component } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	constructor(private toastrService: ToastrService) {}

	errorToastr() {
		// lembrando que primeiro vem a mensagem e depois o titulo
		this.toastrService.error('Erro ao salvar dados ');
	}
}
```

> meu-exemplo-de-tela.component.html

```html
<button (click)="errorToastr()">
	Mostrar toastr
</button>
```

# extra

**documentação ngx-toastr** [ngx-toastr on npm](https://www.npmjs.com/package/ngx-toastr/v/11.3.3)
