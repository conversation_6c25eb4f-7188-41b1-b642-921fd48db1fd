import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3TooltipModule } from "../ds3-tooltip/ds3-tooltip.module";
import { Ds3BiInfoComponent } from "./ds3-bi-info.component";
import { Ds3DiviserModule } from "../ds3-diviser/ds3-diviser.module";
import { RouterModule } from "@angular/router";
import { Ds3ButtonModule } from "../ds3-button/ds3-button.module";

@NgModule({
	declarations: [Ds3BiInfoComponent],
	imports: [
		CommonModule,
		Ds3DiviserModule,
		RouterModule,
		Ds3ButtonModule,
		Ds3TooltipModule,
	],
	exports: [Ds3BiInfoComponent],
})
export class Ds3BiInfoModule {}
