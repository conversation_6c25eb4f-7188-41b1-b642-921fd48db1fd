@import "../../../../assets/ui-kit.scss";

.ds3-bi-info {
	height: 100%;

	.info-area {
		display: flex;
		justify-content: space-around;
		border: 1px solid var(--color-support-gray-1);
		border-radius: 8px;
		padding: 16px;
		width: 100%;
		background-color: var(--color-background-plane-2);

		ds3-diviser {
			min-height: 46px;
			height: 100%;
		}

		&.vertical {
			flex-direction: column;

			ds3-diviser {
				min-height: unset;
			}
		}

		ds3-diviser {
			align-self: center;
		}

		.item-box {
			align-items: center;
			padding: 16px;
			text-align: center;
			width: 100%;

			.info-item,
			.info-auxiliary {
				display: flex;
				align-items: center;
				flex-wrap: nowrap;
				padding: 4px 8px;
				border-radius: 4px;
				min-width: 50%;
				justify-content: center;

				&:hover {
					cursor: pointer;
				}

				span.value {
					display: flex;
					justify-content: center;
					align-items: center;
					@extend .pct-display4;

					small {
						@extend .pct-display6;
					}

					&.percent > small {
						padding-left: 8px;
					}
				}

				.before-icon {
					padding-right: 4px;
				}

				.after-icon {
					padding-left: 4px;
				}

				&.disable-state {
					color: var(--color-typography-default-title);
				}

				&.loss-state {
					color: var(--color-feedback-loss-2);

					&:hover {
						background-color: var(--color-feedback-loss-1);
					}
				}

				&.alert-state {
					color: var(--color-feedback-alert-2);

					&:hover {
						background-color: var(--color-feedback-alert-1);
					}
				}

				&.gain-state {
					color: var(--color-feedback-gain-2);

					&:hover {
						background-color: var(--color-feedback-gain-1);
					}
				}

				&.pink-state {
					color: var(--color-support-pink-4);

					&:hover {
						background-color: var(--color-support-pink-1);
					}
				}

				&.info-state,
				&.default-state {
					color: var(--color-action-default-able-4);

					&:hover {
						background-color: var(--color-action-default-able-1);
					}
				}
			}

			.info-auxiliary {
				width: 50%;
			}

			.middle-legend {
				&.middle-legend-hovered {
					cursor: pointer;

					&:hover {
						text-decoration: underline;
					}
				}
			}

			.overline {
				width: 100%;
				justify-content: center;
				display: flex;
				align-items: center;

				.overline-avatar {
					width: 25px;
					height: 25px;
					border-radius: 50%;
				}

				.overline-dot {
					width: 25px;
					height: 25px;
					border-radius: 50%;
				}

				span {
					@extend .pct-overline2;
					color: var(--color-typography-default-text);
				}
			}
			.ds3-bi-info-text-button {
				margin-top: 8px;
			}
		}
	}
}
