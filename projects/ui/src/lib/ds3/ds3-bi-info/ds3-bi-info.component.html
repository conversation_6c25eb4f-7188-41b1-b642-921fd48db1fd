<div class="ds3-bi-info">
	<div [ngClass]="[vertical ? 'vertical' : '', 'info-area']">
		<ng-container
			*ngFor="let item of infoData; let last = last; trackBy: trackByInfoData">
			<div class="item-box">
				<div
					[ngClass]="['info-item', item.info.state + '-state']"
					(click)="clickFn('info', item)">
					<i
						*ngIf="item.info.beforeIcon"
						[ngClass]="['before-icon', item?.info.beforeIcon.class]"
						[style.font-size]="item.info.beforeIcon.size"></i>
					<span
						class="value currency"
						[style.font-size]="item.info.size"
						*ngIf="item.info.isMonetary">
						<small>R$</small>
						<ng-container *ngIf="item.info.valueHint">
							<span [ds3Tooltip]="item.info.valueHint">
								{{ item.info.value | number : "1.2-2" }}
							</span>
						</ng-container>
						<span *ngIf="!item.info.valueHint">
							{{ item.info.value | number : "1.2-2" }}
						</span>
					</span>
					<span
						class="value percent"
						[style.font-size]="item.info.size"
						*ngIf="item.info.isPercentage">
						<ng-container *ngIf="item.info.valueHint">
							<span [ds3Tooltip]="item.info.valueHint">
								{{ item.info.value * 100 | number : "1.2-2" }}
							</span>
						</ng-container>
						<span *ngIf="!item.info.valueHint">
							{{ item.info.value * 100 | number : "1.2-2" }}
						</span>
						<small>%</small>
					</span>
					<span
						class="value normal"
						[style.font-size]="item.info.size"
						*ngIf="!item.info.isMonetary && !item.info.isPercentage">
						<ng-container *ngIf="item.info.valueHint">
							<span [ds3Tooltip]="item.info.valueHint">
								{{ item.info.value }}
							</span>
						</ng-container>
						<span *ngIf="!item.info.valueHint">
							{{ item.info.value }}
						</span>
					</span>
					<i
						*ngIf="item.info.afterIcon"
						[ngClass]="['after-icon', item?.info.afterIcon.class]"
						[style.font-size]="item.info.afterIcon.size"></i>
				</div>
				<div
					*ngIf="item?.auxiliary"
					[ngClass]="['info-auxiliary', item.auxiliary.state + '-state']"
					(click)="clickFn('auxiliary', item)">
					<i
						*ngIf="item.auxiliary.beforeIcon"
						[ngClass]="['before-icon', item?.auxiliary.beforeIcon.class]"
						[style.font-size]="item.auxiliary.beforeIcon.size"></i>
					<span
						class="value currency"
						[style.font-size]="item.auxiliary.size"
						*ngIf="item.auxiliary.isMonetary">
						<small>R$</small>
						{{ item.auxiliary.value | number : "1.2-2" }}
					</span>
					<span
						class="value percent"
						[style.font-size]="item.auxiliary.size"
						*ngIf="item.auxiliary.isPercentage">
						{{ item.auxiliary.value * 100 | number : "1.2-2" }}
						<small>%</small>
					</span>
					<span
						class="value normal"
						[style.font-size]="item.auxiliary.size"
						*ngIf="!item.auxiliary.isMonetary && !item.auxiliary.isPercentage">
						{{ item.auxiliary.value }}
					</span>
					<i
						*ngIf="item.auxiliary.afterIcon"
						[ngClass]="['after-icon', item?.auxiliary.afterIcon.class]"
						[style.font-size]="item.auxiliary.afterIcon.size"></i>
				</div>
				<div
					*ngIf="item?.middleLegend"
					class="middle-legend typography-overline-2"
					(click)="clickFn('middleLegend', item)"
					[class.middle-legend-hovered]="item?.middleLegend?.hovered">
					<span>{{ item?.middleLegend?.legend }}</span>
				</div>
				<div class="overline" *ngIf="item?.overline">
					<a
						ds3-icon-button
						*ngIf="item.overline.beforeIcon"
						(click)="clickFn('overline', item)">
						<i
							[ngClass]="['before-icon', item?.overline.beforeIcon.class]"
							[style.font-size]="item.overline.beforeIcon.size"></i>
					</a>
					<img
						*ngIf="item?.overline.avatarImage"
						[src]="item?.overline.avatarImage"
						alt=""
						class="overline-avatar" />
					<div
						class="overline-dot"
						*ngIf="item?.overline.dotHexColor"
						[style.background-color]="item.overline.dotHexColor"></div>
					<span *ngIf="item?.overline.text">
						{{ item?.overline.text }}
					</span>
					<a
						ds3-icon-button
						*ngIf="item.overline.afterIcon"
						(click)="clickFn('overline', item)">
						<i
							[ngClass]="['before-icon', item?.overline.afterIcon.class]"
							[style.font-size]="item.overline.afterIcon.size"></i>
					</a>
				</div>
				<div
					class="ds3-bi-info-text-button"
					*ngIf="item?.textButton"
					[ds3Tooltip]="item?.textButtonHint"
					[asInnerHtml]="true">
					<button
						ds3-text-button
						[disabled]="item?.textButtonDisabled"
						[color]="item?.textButtonColor || 'primary'"
						[size]="item?.textButtonSize || 'sm'"
						(click)="clickFn('textButton', item)">
						{{ item?.textButton }}
					</button>
				</div>
			</div>
			<ds3-diviser *ngIf="!last" [vertical]="!vertical"></ds3-diviser>
		</ng-container>
	</div>
</div>
