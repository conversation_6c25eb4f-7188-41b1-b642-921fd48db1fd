import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { InfoData } from "./ds3-bi-info.model";

@Component({
	selector: "ds3-bi-info",
	templateUrl: "./ds3-bi-info.component.html",
	styleUrls: ["./ds3-bi-info.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3BiInfoComponent implements OnInit {
	constructor() {}

	@Input() vertical: boolean = false;
	@Input() infoData: InfoData[] = [];
	@Output() clickEvent: EventEmitter<any> = new EventEmitter();

	ngOnInit() {
		if (this.infoData) {
			this.infoData.forEach((id, index) => {
				if (!id.id) {
					id.id = index;
				}
			});
		}
	}

	clickFn(partClicked: string, item: any) {
		this.clickEvent.emit({ partClicked: partClicked, infoData: item });
	}

	trackByInfoData(index, infoData: InfoData) {
		return infoData.id;
	}
}
