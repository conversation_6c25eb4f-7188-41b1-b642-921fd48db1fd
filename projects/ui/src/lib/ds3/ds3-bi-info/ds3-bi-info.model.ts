import { ComponentType } from "@angular/cdk/portal";
import { TemplateRef } from "@angular/core";
import { MatDialogConfig } from "@angular/material/dialog/typings/dialog-config";

export interface InfoContent {
	size: "14px" | "16px" | "20px" | "24px" | "32px" | "42px" | "48px";
	value: string | number;
	valueHint?: string;
	state: "disable" | "loss" | "alert" | "gain" | "info" | "default";
	isMonetary?: boolean;
	isPercentage?: boolean;
	beforeIcon?: {
		class: string;
		size: "14px" | "20px" | "24px";
		// color: string; herda do state
	};
	afterIcon?: {
		class: string;
		size: "14px" | "20px" | "24px";
		// color: string; herda do state
	};
}

export interface InfoData {
	id?: number;
	info: InfoContent;
	auxiliary?: InfoContent;
	isFullSize?: boolean;
	middleLegend?: {
		legend: string;
		hovered?: boolean;
		modalConfig?: {
			componentType?: ComponentType<any>;
			config?: MatDialogConfig;
		};
	};
	overline?: {
		avatarImage?: string;
		dotHexColor?: string;
		text?: string;
		beforeIcon?: {
			class: string;
			size: "14px" | "20px" | "24px";
			// color: string; herda do state
		};
		afterIcon?: {
			class: string;
			size: "14px" | "20px" | "24px";
			// color: string; herda do state
		};
		link?: string;
	};
	textButton?: string;
	textButtonColor?: "primary" | "secondary";
	textButtonSize?: "lg" | "sm";
	textButtonDisabled?: boolean;
	textButtonHint?: string | TemplateRef<any>;
	modalConfig?: {
		componentType?: ComponentType<any>;
		config?: MatDialogConfig;
	};
}
