import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3DragDropItemComponent } from "./ds3-drag-drop-item.component";

describe("Ds3DragDropItemComponent", () => {
	let component: Ds3DragDropItemComponent;
	let fixture: ComponentFixture<Ds3DragDropItemComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3DragDropItemComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3DragDropItemComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
