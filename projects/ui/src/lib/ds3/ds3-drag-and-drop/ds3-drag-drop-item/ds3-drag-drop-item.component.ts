import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";

@Component({
	selector: "ds3-drag-drop-item",
	templateUrl: "./ds3-drag-drop-item.component.html",
	styleUrls: ["./ds3-drag-drop-item.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3DragDropItemComponent implements OnInit {
	@ViewChild(TemplateRef, { static: false })
	templateRef: TemplateRef<any>;

	@Input()
	simpleView: boolean;

	constructor() {}

	ngOnInit() {}
}
