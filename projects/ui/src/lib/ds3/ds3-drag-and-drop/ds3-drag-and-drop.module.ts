import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3DragAndDropComponent } from "./ds3-drag-and-drop.component";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { Ds3DragDropItemComponent } from "./ds3-drag-drop-item/ds3-drag-drop-item.component";
import { Ds3DragDropAddComponent } from "./ds3-drag-drop-add/ds3-drag-drop-add.component";
import { Ds3DragDropRemoveComponent } from "./ds3-drag-drop-remove/ds3-drag-drop-remove.component";
import { Ds3DiviserModule } from "../ds3-diviser/ds3-diviser.module";
import { Ds3DragDropTitleComponent } from "./ds3-drag-drop-title/ds3-drag-drop-title.component";

@NgModule({
	declarations: [
		Ds3DragAndDropComponent,
		Ds3DragDropItemComponent,
		Ds3DragDropAddComponent,
		Ds3DragDropRemoveComponent,
		Ds3DragDropTitleComponent,
	],
	imports: [CommonModule, DragDropModule, Ds3DiviserModule],
	exports: [
		Ds3DragAndDropComponent,
		Ds3DragDropItemComponent,
		Ds3DragDropAddComponent,
		Ds3DragDropRemoveComponent,
		Ds3DragDropTitleComponent,
	],
	entryComponents: [Ds3DragDropItemComponent],
})
export class Ds3DragAndDropModule {}
