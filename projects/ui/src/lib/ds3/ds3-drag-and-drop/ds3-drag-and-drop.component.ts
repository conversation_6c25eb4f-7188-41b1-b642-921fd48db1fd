import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	HostBinding,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "ds3-drag-and-drop",
	templateUrl: "./ds3-drag-and-drop.component.html",
	styleUrls: ["./ds3-drag-and-drop.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3DragAndDropComponent implements OnInit {
	@HostBinding("class.ds3-drag-and-drop")
	enableEncapsulation = true;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {}
}
