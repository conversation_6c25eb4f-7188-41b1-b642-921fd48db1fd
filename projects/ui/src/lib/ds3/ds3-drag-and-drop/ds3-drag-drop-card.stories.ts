import Notes from "./ds3-drag-drop-card.md";
import { moduleMetadata } from "@storybook/angular";
import { object, text, withKnobs } from "@storybook/addon-knobs";
import { Ds3DragAndDropModule } from "./ds3-drag-and-drop.module";
import { DragDropColumnData } from "./ds3-drag-drop.model";

const description = () => Notes;

export default {
	title: "Design System 3 | Drag and drop",
	decorators: [
		moduleMetadata({
			imports: [Ds3DragAndDropModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};
const data = new Array<DragDropColumnData>(
	{
		column: 1,
		data: [
			{
				id: 1,
				label: "Item 1",
			},
			{
				id: 2,
				label: "Item 2",
			},
			{
				id: 3,
				label: "Item 3",
			},
			{
				id: 4,
				label: "Item 4",
			},
			{
				id: 5,
				label: "Item 5",
			},
		],
	},
	{
		column: 2,
		data: [
			{
				id: 6,
				label: "Item 6",
			},
			{
				id: 7,
				label: "Item 7",
			},
			{
				id: 8,
				label: "Item 8",
			},
			{
				id: 9,
				label: "Item 9",
			},
			{
				id: 10,
				label: "Item 10",
			},
		],
	}
);
const dataRemoved = [
	{
		id: 11,
		label: "Item 11",
	},
	{
		id: 12,
		label: "Item 12",
	},
	{
		id: 13,
		label: "Item 13",
	},
	{
		id: 14,
		label: "Item 14",
	},
	{
		id: 15,
		label: "Item 15",
	},
];
export const DragDropCard = () => ({
	template: `
	<div style="background: hsla(222, 33%, 95%, 1);">
		<ds3-drag-and-drop>
		  <ds3-drag-drop-remove [data]="notVisibleData">
			<ds3-drag-drop-title>
			  <i class="pct pct-eye-off"></i> {{ tituloCardsNaoVisiveis }}
			</ds3-drag-drop-title>
		
			<ds3-drag-drop-item *ngFor="let item of notVisibleData; let index = index" [simpleView]="true">{{item.label}}</ds3-drag-drop-item>
		  </ds3-drag-drop-remove>
		  <ds3-drag-drop-add [columnData]="visibleData">
			<ds3-drag-drop-title>
			  <i class="pct pct-eye"></i> {{ tituloCardsVisiveis }}
			</ds3-drag-drop-title>
		
			<ng-container *ngFor="let columnData of visibleData">
			  <ds3-drag-drop-item *ngFor="let item of columnData.data; let index = index">
				<div class="drag-drop-card-item-test" [ngClass]="{'drag-drop-card-item-test-more-width': index % 2 === 0}">
				  <span>{{item.label}}</span>
				</div>
			  </ds3-drag-drop-item>
			</ng-container>
		  </ds3-drag-drop-add>
		</ds3-drag-and-drop>
	</div>
    `,
	props: {
		tituloCardsNaoVisiveis: text(
			"Título da coluna de cards não visíveis",
			"Cards não que estarão visíveis"
		),
		tituloCardsVisiveis: text(
			"Título da coluna de cards visíveis",
			"Cards que estarão visíveis"
		),
		visibleData: object("Dados que estarão visíveis", data),
		notVisibleData: object("Dados não que estarão visíveis", dataRemoved),
	},
});
