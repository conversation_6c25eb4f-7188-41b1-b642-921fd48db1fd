**Seletor:** `ds3-accordion`

## Uso do componente

## Import in another module

```
import {Ds3DragAndDropCardModule} from 'ui-kit';

@NgModule({
    declarations: [],
    imports: [
        ...
        Ds3DragAndDropCardModule
        ...
    ]
})
export class MeuModule {
}
```
&nbsp;

### Utilização no componente:

> meu-exemplo-de-tela.component.ts

```typescript
import { Component, OnInit } from '@angular/core';
import { DragDropColumnData } from 'ui-kit';

@Component({
	selector: 'pacto-meu-exemplo-de-tela',
	templateUrl: './meu-exemplo-de-tela.component.html',
	styleUrls: ['./meu-exemplo-de-tela.component.scss']
})
export class MeuExemploDeTelaComponent {
	data = new Array<DragDropColumnData>(
		{
			column: 1,
			data: [
				{
					id: 1,
					label: 'Item 1'
				},
				{
					id: 2,
					label: 'Item 2'
				},
				{
					id: 3,
					label: 'Item 3'
				},
				{
					id: 4,
					label: 'Item 4'
				},
				{
					id: 5,
					label: 'Item 5'
				},
			]
		},
		{
			column: 2,
			data: [
				{
					id: 6,
					label: 'Item 6'
				},
				{
					id: 7,
					label: 'Item 7'
				},
				{
					id: 8,
					label: 'Item 8'
				},
				{
					id: 9,
					label: 'Item 9'
				},
				{
					id: 10,
					label: 'Item 10'
				},
			]
		}
	);
	dataRemoved = [
		{
			id: 11,
			label: 'Item 11'
		},
		{
			id: 12,
			label: 'Item 12'
		},
		{
			id: 13,
			label: 'Item 13'
		},
		{
			id: 14,
			label: 'Item 14'
		},
		{
			id: 15,
			label: 'Item 15'
		},
	];
}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-drag-and-drop>
	<ds3-drag-drop-remove [data]="dataRemoved">
		<ds3-drag-drop-title>
			<i class="pct pct-eye-off"></i> BI's não que estarão visíveis
		</ds3-drag-drop-title>

		<ds3-drag-drop-item *ngFor="let item of dataRemoved; let index = index" [simpleView]="true">{{item.label}}</ds3-drag-drop-item>
	</ds3-drag-drop-remove>
	<ds3-drag-drop-add [columnData]="data">
		<ds3-drag-drop-title>
			<i class="pct pct-eye"></i> BI's que estarão visíveis
		</ds3-drag-drop-title>

		<ng-container *ngFor="let columnData of data">
			<ds3-drag-drop-item *ngFor="let item of columnData.data; let index = index">
				<div class="drag-drop-card-item-test" [ngClass]="{'drag-drop-card-item-test-more-width': index % 2 === 0}">
					<span>{{item.label}}</span>
				</div>
			</ds3-drag-drop-item>
		</ng-container>
	</ds3-drag-drop-add>
</ds3-drag-and-drop>
```
&nbsp;

## Props / Inputs

> ds3-drag-drop-remove

| Property                  | Values | Default          |
|---------------------------|--------|------------------|
| @Input() data: Array<any> |        | new Array<any>() |

> ds3-drag-drop-add

| Property                                       | Values | Default                         |
|------------------------------------------------|--------|---------------------------------|
| @Input() columnData: Array<DragDropColumnData> |        | new Array<DragDropColumnData>() |
| @Input() columns: number                       |        | 2                               |

## Outputs / Events

> ds3-drag-drop-remove

| Property                          | Values | Default |
|-----------------------------------|--------|---------|
| @Output() dataMoved: EventEmitter |        | -       |

## More info

### Types

>DragDropColumnData

Utilizado para a tipagem dos dados passados para o ds3-drag-drop-add.

```typescript
export interface DragDropColumnData {
	data: Array<any>;
	column: number;
}
```
