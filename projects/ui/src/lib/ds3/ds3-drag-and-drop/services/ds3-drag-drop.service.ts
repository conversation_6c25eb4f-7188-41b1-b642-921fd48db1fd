import { Injectable } from "@angular/core";
import { Subject } from "rxjs";

@Injectable({
	providedIn: "root",
})
export class Ds3DragDropService {
	_dragDropAddIds: Subject<Array<string>> = new Subject();

	constructor() {}

	get dragDropAddIds$() {
		return this._dragDropAddIds.asObservable();
	}

	set dragDropAddIds(dragDropAddIds: Array<string>) {
		this._dragDropAddIds.next([...dragDropAddIds]);
	}
}
