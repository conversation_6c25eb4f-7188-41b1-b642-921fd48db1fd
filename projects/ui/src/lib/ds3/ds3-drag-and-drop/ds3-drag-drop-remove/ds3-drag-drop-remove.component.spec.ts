import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3DragDropRemoveComponent } from "./ds3-drag-drop-remove.component";

describe("Ds3DragDropRemoveComponent", () => {
	let component: Ds3DragDropRemoveComponent;
	let fixture: ComponentFixture<Ds3DragDropRemoveComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3DragDropRemoveComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3DragDropRemoveComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
