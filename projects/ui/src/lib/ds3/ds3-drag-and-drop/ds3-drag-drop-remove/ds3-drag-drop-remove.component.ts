import {
	AfterContentInit,
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	ElementRef,
	EventEmitter,
	HostBinding,
	Input,
	OnDestroy,
	OnInit,
	Output,
	QueryList,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { Ds3DragDropItemComponent } from "../ds3-drag-drop-item/ds3-drag-drop-item.component";
import {
	CdkDragDrop,
	CdkDragEnter,
	CdkDragExit,
	CdkDragMove,
	CdkDragPlaceholder,
	CdkDropList,
	moveItemInArray,
	transferArrayItem,
} from "@angular/cdk/drag-drop";
import { Ds3DragDropService } from "../services/ds3-drag-drop.service";
import { DragDropColumnData } from "../ds3-drag-drop.model";

@Component({
	selector: "ds3-drag-drop-remove",
	templateUrl: "./ds3-drag-drop-remove.component.html",
	styleUrls: ["./ds3-drag-drop-remove.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3DragDropRemoveComponent
	implements OnInit, AfterViewInit, AfterContentInit, OnDestroy
{
	@HostBinding("class.ds3-drag-drop-remove")
	enableEncapsulation = true;

	@ContentChildren(Ds3DragDropItemComponent, { descendants: true })
	dragDropItem: QueryList<Ds3DragDropItemComponent>;

	@ViewChild(CdkDropList, { static: false })
	dropList: CdkDropList;

	@ViewChild(CdkDragPlaceholder, { static: false })
	cdkDragPlaceholder: CdkDragPlaceholder;

	@Input()
	data: Array<any> = new Array<any>();

	@Input()
	scrollContainer: ElementRef<any>;

	@Output()
	dataMoved: EventEmitter<any> = new EventEmitter<any>();

	dragDropItemArray: Array<any> = new Array<any>();

	dragDropAddContainerIndexes = new Array<string>();

	iconElement: HTMLElement;
	private scrollInterval: any = null;

	constructor(
		private dragDropService: Ds3DragDropService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.dragDropService.dragDropAddIds$.subscribe(
			(dragDropAddId) => (this.dragDropAddContainerIndexes = dragDropAddId)
		);
		this.createIcon();
	}

	ngAfterViewInit() {
		if (this.cdkDragPlaceholder) {
			const nativeElement =
				this.cdkDragPlaceholder.templateRef.elementRef.nativeElement;
			if (nativeElement.lastElementChild) {
				nativeElement.lastElementChild.remove();
			}
			if (nativeElement instanceof HTMLElement) {
				nativeElement.append(this.iconElement);
			}
		}
	}

	ngAfterContentInit() {
		if (this.dragDropItem) {
			this.dragDropItemArray = this.dragDropItem.toArray();
			this.dragDropItem.changes.subscribe(() => {
				this.dragDropItemArray = this.dragDropItem.toArray();
				this.cd.markForCheck();
			});
		}
	}

	ngOnDestroy() {
		this.stopScrolling();
	}

	createIcon() {
		const tagIconElement = document.createElement("i");
		tagIconElement.className = "pct pct-eye-off";
		tagIconElement.id = "ds3-drag-drop-placeholder-remove-icon";
		this.iconElement = tagIconElement;
	}

	drop(event: CdkDragDrop<any>) {
		if (event.previousContainer === event.container) {
			moveItemInArray(
				this.dragDropItemArray,
				event.previousIndex,
				event.currentIndex
			);
		} else {
			const dragItemData = event.item.data as {
				dragDropItemArray: any;
				columnData: any;
				dragDropItem: any;
				column: number;
				index: number;
			};
			dragItemData.dragDropItemArray[dragItemData.column - 1].splice(
				dragItemData.index,
				1
			);
			transferArrayItem(
				event.previousContainer.data.data,
				event.container.data,
				event.previousIndex,
				event.currentIndex
			);
		}
		this.dataMoved.emit(this.data);
	}

	onEntered(cdkDragEnter: CdkDragEnter) {
		const placeholderElement = cdkDragEnter.item.getPlaceholderElement();
		if (placeholderElement.lastElementChild) {
			placeholderElement.lastElementChild.remove();
		}
		if (this.cdkDragPlaceholder) {
			placeholderElement.append(this.iconElement);
		}
	}

	onDragMoved(
		dragMove: CdkDragMove<{ index: number; dragDropItemArray: Array<any> }>
	) {
		if (this.scrollContainer) {
			const container = this.scrollContainer.nativeElement;
			const scrollSpeed = 15;
			const threshold = 50;
			const rect = container.getBoundingClientRect();

			let scrollDirection: "up" | "down" | null = null;
			if (dragMove.pointerPosition.y < rect.top + threshold) {
				scrollDirection = "up";
			} else if (dragMove.pointerPosition.y > rect.bottom - threshold) {
				scrollDirection = "down";
			} else {
				this.stopScrolling();
				return;
			}

			// Se já estiver rolando na direção certa, não cria outro intervalo
			if (this.scrollInterval) {
				return;
			}

			// Inicia um intervalo de rolagem
			this.scrollInterval = setInterval(() => {
				if (scrollDirection === "up") {
					container.scrollTop -= scrollSpeed;
				} else if (scrollDirection === "down") {
					container.scrollTop += scrollSpeed;
				}
			}, 10);
		}
	}

	stopScrolling() {
		if (this.scrollInterval) {
			clearInterval(this.scrollInterval);
			this.scrollInterval = null;
		}
	}

	onDragEnd() {
		this.stopScrolling();
	}
}
