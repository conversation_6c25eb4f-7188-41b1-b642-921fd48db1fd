import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3DragAndDropComponent } from "./ds3-drag-and-drop.component";

describe("Ds3DragAndDropComponent", () => {
	let component: Ds3DragAndDropComponent;
	let fixture: ComponentFixture<Ds3DragAndDropComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3DragAndDropComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3DragAndDropComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
