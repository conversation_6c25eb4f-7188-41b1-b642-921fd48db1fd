<ng-content select="ds3-drag-drop-title"></ng-content>

<div
	#scrollableContainer
	[ngStyle]="{ 'grid-template-columns': 'repeat(' + columns + ', 1fr)' }"
	class="ds3-drag-drop-add-container">
	<ng-container *ngFor="let column of columnData; let indexColumn = index">
		<div
			(cdkDropListDropped)="drop($event, indexColumn)"
			(cdkDropListEntered)="onEntered($event, column)"
			[cdkDropListConnectedTo]="dragDropContainerIndexes[indexColumn]"
			[cdkDropListData]="column"
			[id]="'drag-drop-add-list-' + indexColumn"
			cdkDropList
			class="ds3-drag-drop-add-list">
			<div
				(cdkDragStarted)="onDragStart($event)"
				*ngFor="let data of column.data; let indexData = index"
				[cdkDragData]="{
					dragDropItemArray: dragDropItemArray,
					columnData: columnData,
					dragDropItem: dragDropItemArray[indexColumn][indexData],
					column: column.column,
					index: indexData
				}"
				(cdkDragMoved)="onDragMoved($event)"
				(cdkDragEnded)="onDragEnd()"
				[id]="'ds3-drag-drop-add-drag-' + indexColumn + '-' + indexData"
				cdkDrag
				class="ds3-drag-drop-add-drag">
				<div *cdkDragPlaceholder class="ds3-drag-drop-placeholder">
					<i class="pct pct-eye" id="ds3-drag-drop-placeholder-add-icon"></i>
				</div>
				<div class="ds3-drag-drop-add-preview-move">
					<i class="pct pct-move"></i>
				</div>
				<ng-container
					*ngTemplateOutlet="
						dragDropItemArray[indexColumn][indexData]?.templateRef
					"></ng-container>
			</div>
		</div>
	</ng-container>
</div>
