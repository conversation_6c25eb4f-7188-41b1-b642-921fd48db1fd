import {
	AfterContentInit,
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	ElementRef,
	EventEmitter,
	HostBinding,
	Input,
	OnDestroy,
	OnInit,
	Output,
	QueryList,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import {
	CdkDragDrop,
	CdkDragEnter,
	CdkDragMove,
	CdkDragPlaceholder,
	CdkDragStart,
	moveItemInArray,
	transferArrayItem,
} from "@angular/cdk/drag-drop";
import { DragDropColumnData } from "../ds3-drag-drop.model";
import { Ds3DragDropItemComponent } from "../ds3-drag-drop-item/ds3-drag-drop-item.component";
import { Ds3DragDropService } from "../services/ds3-drag-drop.service";

@Component({
	selector: "ds3-drag-drop-add",
	templateUrl: "./ds3-drag-drop-add.component.html",
	styleUrls: ["./ds3-drag-drop-add.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3DragDropAddComponent
	implements OnInit, AfterViewInit, AfterContentInit, OnDestroy
{
	@HostBinding("class.ds3-drag-drop-add")
	enableEncapsulation = true;

	@ContentChildren(Ds3DragDropItemComponent, { descendants: true })
	dragDropItem: QueryList<Ds3DragDropItemComponent>;

	@ViewChild(CdkDragPlaceholder, { static: false })
	cdkDragPlaceholder: CdkDragPlaceholder;

	@Input()
	columnData: Array<DragDropColumnData> = new Array<DragDropColumnData>();

	@Input()
	columns: number = 2;

	@Input()
	scrollContainer: ElementRef<any>;

	@Output()
	dataMoved: EventEmitter<any> = new EventEmitter<any>();

	dragDropItemArray: Array<Array<Ds3DragDropItemComponent>> = new Array<
		Array<Ds3DragDropItemComponent>
	>();
	dragDropContainerIndexes = new Array<Array<string>>();

	private iconElement: HTMLElement;
	private scrollInterval: any = null;

	constructor(
		private scrollableContainer: ElementRef,
		private dragDropService: Ds3DragDropService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initializeIconElement();
	}

	ngAfterViewInit() {
		this.appendIconToPlaceholder();
	}

	ngAfterContentInit() {
		if (this.dragDropItem) {
			this.populateArrays();
			this.dragDropItem.changes.subscribe(() => this.populateArrays());
		}
	}

	ngOnDestroy() {
		this.stopScrolling();
	}

	private populateArrays(): void {
		this.dragDropItemArray = Array.from({ length: this.columns }, () => []);
		this.dragDropContainerIndexes = Array.from(
			{ length: this.columns },
			() => []
		);
		let dragDropAddIdsAux: string[] = [];
		this.dragDropItemArray = this.createDragDropItemArray();
		dragDropAddIdsAux = this.createDragDropContainerIndexes(dragDropAddIdsAux);
		this.dragDropService.dragDropAddIds = dragDropAddIdsAux;
		this.cd.markForCheck();
	}

	private createDragDropItemArray(): Array<Array<Ds3DragDropItemComponent>> {
		let sliceStart = 0;
		return this.columnData.map((column, index) => {
			const sliceEnd = this.calculateSliceEnd(index, sliceStart);
			sliceStart = sliceEnd;
			return this.dragDropItem
				.toArray()
				.slice(sliceStart - column.data.length, sliceEnd);
		});
	}

	private calculateSliceEnd(index: number, sliceStart: number): number {
		const length = this.columnData[index].data.length;
		return index === 0 ? length : sliceStart + length;
	}

	private createDragDropContainerIndexes(
		dragDropAddIdsAux: string[]
	): string[] {
		return this.columnData.reduce((acc, _, i) => {
			const otherIndexes = this.generateOtherIndexes(i);
			this.dragDropContainerIndexes[i].push(
				...otherIndexes,
				"ds3-drag-drop-remove-list"
			);
			return [...acc, ...otherIndexes];
		}, dragDropAddIdsAux);
	}

	private generateOtherIndexes(currentIndex: number): string[] {
		return this.columnData
			.map((_, j) => j)
			.filter((j) => j !== currentIndex)
			.map((j) => `drag-drop-add-list-${j}`);
	}

	private initializeIconElement() {
		const tagIconElement = document.createElement("i");
		tagIconElement.className = "pct pct-eye";
		tagIconElement.id = "ds3-drag-drop-placeholder-add-icon";
		this.iconElement = tagIconElement;
	}

	private appendIconToPlaceholder() {
		if (this.cdkDragPlaceholder) {
			const lastElementChild =
				this.cdkDragPlaceholder.templateRef.elementRef.nativeElement
					.lastElementChild;
			if (lastElementChild) {
				lastElementChild.remove();
			}
			if (
				this.cdkDragPlaceholder.templateRef.elementRef.nativeElement instanceof
				HTMLElement
			) {
				this.cdkDragPlaceholder.templateRef.elementRef.nativeElement.append(
					this.iconElement
				);
			}
		}
	}

	drop(event: CdkDragDrop<any>, indexColumn: number) {
		if (event.previousContainer === event.container) {
			moveItemInArray(
				this.dragDropItemArray[indexColumn],
				event.previousIndex,
				event.currentIndex
			);
			moveItemInArray(
				this.columnData[indexColumn].data,
				event.previousIndex,
				event.currentIndex
			);
		} else {
			this.handleExternalDrop(event);
		}
		this.dataMoved.emit(this.columnData);
	}

	private handleExternalDrop(event: CdkDragDrop<any>) {
		const previousContainerData = event.previousContainer.data;
		const actualContainerData = event.container.data;
		if (previousContainerData.column) {
			transferArrayItem(
				this.dragDropItemArray[previousContainerData.column - 1],
				this.dragDropItemArray[actualContainerData.column - 1],
				event.previousIndex,
				event.currentIndex
			);
			transferArrayItem(
				this.columnData[previousContainerData.column - 1].data,
				this.columnData[actualContainerData.column - 1].data,
				event.previousIndex,
				event.currentIndex
			);
		} else {
			const dragItemData: { dragDropItemArray: any; index: number } =
				event.item.data;
			dragItemData.dragDropItemArray.splice(dragItemData.index, 1);
			transferArrayItem(
				event.previousContainer.data,
				event.container.data.data,
				event.previousIndex,
				event.currentIndex
			);
		}
	}

	onEntered(
		cdkDragEnter: CdkDragEnter<DragDropColumnData>,
		column: DragDropColumnData
	) {
		const lastElementChild =
			cdkDragEnter.item.getPlaceholderElement().lastElementChild;
		if (lastElementChild) {
			lastElementChild.remove();
		}
		if (this.cdkDragPlaceholder) {
			cdkDragEnter.item.getPlaceholderElement().append(this.iconElement);
		}
	}

	onDragStart(dragStart: CdkDragStart) {
		dragStart.source.element.nativeElement.style.cursor = "grabbing";
		dragStart.source.element.nativeElement.style.zIndex = "2000";
	}

	onDragMoved(
		dragMove: CdkDragMove<{
			dragDropItem: Ds3DragDropItemComponent;
			column: number;
			columnData: Array<DragDropColumnData>;
			index: number;
			dragDropItemArray: Array<Array<Ds3DragDropItemComponent>>;
		}>
	) {
		if (this.scrollContainer) {
			const container = this.scrollContainer.nativeElement;
			const scrollSpeed = 15;
			const threshold = 50;
			const rect = container.getBoundingClientRect();

			let scrollDirection: "up" | "down" | null = null;
			if (dragMove.pointerPosition.y < rect.top + threshold) {
				scrollDirection = "up";
			} else if (dragMove.pointerPosition.y > rect.bottom - threshold) {
				scrollDirection = "down";
			} else {
				this.stopScrolling();
				return;
			}

			// Se já estiver rolando na direção certa, não cria outro intervalo
			if (this.scrollInterval) {
				return;
			}

			// Inicia um intervalo de rolagem
			this.scrollInterval = setInterval(() => {
				if (scrollDirection === "up") {
					container.scrollTop -= scrollSpeed;
				} else if (scrollDirection === "down") {
					container.scrollTop += scrollSpeed;
				}
			}, 10);
		}
	}

	stopScrolling() {
		if (this.scrollInterval) {
			clearInterval(this.scrollInterval);
			this.scrollInterval = null;
		}
	}

	onDragEnd() {
		this.stopScrolling();
	}
}
