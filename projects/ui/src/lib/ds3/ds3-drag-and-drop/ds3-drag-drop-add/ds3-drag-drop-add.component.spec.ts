import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3DragDropAddComponent } from "./ds3-drag-drop-add.component";

describe("Ds3DragDropAddComponent", () => {
	let component: Ds3DragDropAddComponent;
	let fixture: ComponentFixture<Ds3DragDropAddComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3DragDropAddComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3DragDropAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
