@import "projects/ui/assets/ds3/functions";
@import "projects/ui/assets/ds3/colors.var";

.ds3-drag-drop-add {
	.ds3-drag-drop-title {
		margin-bottom: px-to-rem(10px);
	}

	.ds3-drag-drop-add-container {
		display: grid;
		grid-gap: px-to-rem(16px);
		height: 100%;

		.ds3-drag-drop-add-list {
			display: flex;
			flex-direction: column;
			gap: px-to-rem(16px);

			.ds3-drag-drop-placeholder {
				min-height: 292px;
				border-radius: px-to-rem(8px);
				border: 1px solid var(--color-action-default-able-4);
				cursor: grabbing;

				font-size: px-to-rem(80px);
				display: flex;
				justify-content: center;
				align-items: center;
				color: var(--color-action-default-disable-2);
			}
		}
	}
}

.ds3-drag-drop-add-drag {
	position: relative;

	.ds3-drag-drop-add-preview-move {
		position: absolute;
		top: 0;
		opacity: 0;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: px-to-rem(80px);
		width: 100%;
		height: 100%;
		border-radius: px-to-rem(4px);
		color: var(--color-action-default-able-4);
		/*Como existe uma função no arquivo functions que sobrescreve o hsla padrão isso deverá ser utilizado no lugar
    *  teve que usar o valor direto já que não dá pra usar funções do scss para alterar o alpha
    */
		background-color: hsla(222, 6%, 20%, 0);
		transition: opacity ease-in-out 0.3s;
		z-index: 1;
	}

	&:hover {
		.ds3-drag-drop-add-preview-move {
			opacity: 1;
			background-color: hsla(222, 6%, 20%, 0.3);
			cursor: grab;
		}
	}

	&.cdk-drag-preview {
		/*
		 * Este valor é por conta do cdk-overlay que está com um z-index 9000 que ocasiona bug ao apresentar o preview
		 * em modais por exemplo
		 *
		 */
		z-index: 10000 !important;
		opacity: 0.7;

		.ds3-drag-drop-add-preview-move {
			opacity: 1;
			background-color: hsla(222, 6%, 20%, 0.3);
		}
	}
}
