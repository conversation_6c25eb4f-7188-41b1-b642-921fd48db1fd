import {
	ChangeDetectionStrategy,
	Component,
	HostBinding,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "ds3-drag-drop-title",
	templateUrl: "./ds3-drag-drop-title.component.html",
	styleUrls: ["./ds3-drag-drop-title.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3DragDropTitleComponent implements OnInit {
	@HostBinding("class.ds3-drag-drop-title")
	enableEncapsulation = true;

	constructor() {}

	ngOnInit() {}
}
