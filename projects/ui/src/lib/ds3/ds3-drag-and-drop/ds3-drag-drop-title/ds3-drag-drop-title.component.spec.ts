import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3DragDropTitleComponent } from "./ds3-drag-drop-title.component";

describe("Ds3DragDropTitleComponent", () => {
	let component: Ds3DragDropTitleComponent;
	let fixture: ComponentFixture<Ds3DragDropTitleComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3DragDropTitleComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3DragDropTitleComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
