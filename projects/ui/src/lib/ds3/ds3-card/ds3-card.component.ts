import {
	Component,
	OnInit,
	ViewEncapsulation,
	ChangeDetectionStrategy,
	HostBinding,
} from "@angular/core";

@Component({
	selector: "ds3-card",
	templateUrl: "./ds3-card.component.html",
	styleUrls: ["./ds3-card.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Ds3CardComponent implements OnInit {
	@HostBinding("class.ds3-card")
	enableEncapsulation = true;

	constructor() {}

	ngOnInit() {}
}
