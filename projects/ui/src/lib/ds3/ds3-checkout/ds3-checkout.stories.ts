import {
	boolean,
	object,
	select,
	text,
	withKnobs,
} from "@storybook/addon-knobs";
import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
// @ts-ignore
import Notes from "./ds3-checkout.notes.md";

const description = () => Notes;

export default {
	title: "Design System 3 | Data/Checkout",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		docs: {
			extractComponentDescription: description,
		},
	},
};

const size = ["small", "medium", "large", "xlarge"];
const buttons = ["0", "1", "2", "3"];
const button1Type = ["flat", "outlined"];
const button2Type = ["flat", "outlined"];
const button3Type = ["flat", "outlined"];
const infoArray = [
	{ info: 10, overline: "Contrato 1" },
	{ info: 20, overline: "Contrato 2" },
	{ info: 30, overline: "Contrato 3" },
];

export const interactive = () => ({
	template: `	
      <div style='padding:16px;'>
        <ds3-checkout [useBorder]="useBorder" [showAvatar]="showAvatar" [avatarSize]="avatarSize" [showInfo]="showInfo" 
            [infoArray]="infoArray" [buttons]="buttons" [button1Type]="button1Type" [button2Type]="button2Type" [button3Type]="button3Type"
            [button1Name]="button1Name" [button2Name]="button2Name" [button3Name]="button3Name">
        </ds3-checkout>
      </div>
      `,
	props: {
		useBorder: boolean("Border", false),
		showAvatar: boolean("Show Avatar", false),
		avatarSize: select("Avatar Size", size, size[0]),
		showInfo: boolean("Show Info", false),
		infoArray: object("Informações", infoArray),
		buttons: select("Buttons", buttons, buttons[0]),
		button1Type: select("Button 1 Type", button1Type, button1Type[0]),
		button1Name: text("Button 1 Name", "Botão"),
		button2Type: select("Button 2 Type", button2Type, button2Type[0]),
		button2Name: text("Button 2 Name", "Botão"),
		button3Type: select("Button 3 Type", button3Type, button3Type[0]),
		button3Name: text("Button 3 Name", "Botão"),
	},
});
