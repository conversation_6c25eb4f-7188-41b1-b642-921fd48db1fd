@import "projects/ui/assets/ds3/colors.var";

.ds3-checkout {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	width: 100%;
	height: auto;

	background: $plane02;
	box-shadow: 0px 8px 8px rgba(0, 0, 0, 0.06);
	border-radius: 8px;

	&.border-variant {
		border: 1px solid $supportGray03;
		box-shadow: none;
	}

	&.justify-between {
		.checkout-content {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			width: 100%;
		}
	}

	.avatar-container {
		display: flex;
		align-items: center;
		padding-right: 25%;
	}

	.buttons-container {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		position: relative;
		width: 100%;
		border-top: 1px solid $supportGray03;
		padding: 16px 0 16px 0;
	}

	.buttons-container-sm {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		position: relative;
		width: 100%;
		padding: 16px 0 16px 0;
	}

	&.flex-row {
		flex-direction: row;
	}
}

.checkout-content {
	&.flex-row {
		display: flex;
		flex-direction: row;
	}
}

ds3-infos ::ng-deep .ds3-info.default {
	border: none !important;
	position: relative;
	left: 0;
}

ds3-infos ::ng-deep .info-data-container {
	justify-content: center;
}
