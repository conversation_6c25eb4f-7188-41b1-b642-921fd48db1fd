**Seletor:** `ds3-checkout`

**Utilização:**`<ds3-checkout></ds3-checkout>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component, OnInit } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
    

}
```

> meu-exemplo-de-tela.component.html

```html
<ds3-checkout></ds3-checkout>
```
&nbsp;  

## Props / Inputs


## More info


&nbsp;  
