import { Component, Input, OnInit } from "@angular/core";

@Component({
	selector: "ds3-checkout",
	templateUrl: "./ds3-checkout.component.html",
	styleUrls: ["./ds3-checkout.component.scss"],
})
export class Ds3CheckoutComponent implements OnInit {
	@Input() useBorder: boolean = false;
	@Input() showAvatar: boolean = false;
	@Input() avatarSize: "small" | "medium" | "large" | "xlarge" = "medium";
	@Input() showInfo: boolean = false;
	@Input() infoArray: { info: number; overline: string }[] = [];
	@Input() buttons: number = 0;
	@Input() button1Type: "flat" | "outlined" = "flat";
	@Input() button1Name: string = "Botão";
	@Input() button2Type: "flat" | "outlined" = "flat";
	@Input() button2Name: string = "Botão";
	@Input() button3Type: "flat" | "outlined" = "flat";
	@Input() button3Name: string = "Botão";

	constructor() {}

	ngOnInit() {}
}
