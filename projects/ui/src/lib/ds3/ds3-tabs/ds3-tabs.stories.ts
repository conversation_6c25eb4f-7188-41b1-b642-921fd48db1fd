import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
import { array, withKnobs } from "@storybook/addon-knobs";

export default {
	title: "Design System 3 | Navigation/Tabs",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {},
};

const tabsList = ["Tab 1", "Tab 2", "Tab 3", "Tab Maior"];

export const tabs = () => ({
	template: `
		<div style="padding: 16px;">
			<ds3-tabs [tabs]="tabs"></ds3-tabs>
		</div>
    `,
	props: {
		tabs: array("Tabs", tabsList),
	},
});
