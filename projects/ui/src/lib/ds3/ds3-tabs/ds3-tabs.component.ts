import {
	AfterViewInit,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	Renderer2,
	ViewChild,
} from "@angular/core";

@Component({
	selector: "ds3-tabs",
	templateUrl: "./ds3-tabs.component.html",
	styleUrls: ["./ds3-tabs.component.scss"],
})
export class Ds3TabsComponent implements OnInit, AfterViewInit {
	constructor(private renderer: Renderer2) {}

	@Input() tabs: string[] = [];
	@Input() id: string;
	@ViewChild("tabsContainer", { static: true }) tabsContainer!: ElementRef;
	@Output() selectedTabEvent: EventEmitter<number> = new EventEmitter<number>();

	selectedTabIndex: number = 0;
	selectedTabTemplate: HTMLElement;
	scrollAmount = 200;

	canScrollLeft: boolean = false;
	canScrollRight: boolean = false;

	ngOnInit() {}

	ngAfterViewInit() {
		const tabsElement = this.tabsContainer.nativeElement.getElementsByClassName(
			"tab"
		) as HTMLCollection;
		if (tabsElement && tabsElement.length > 0) {
			this.selectTab(this.selectedTabIndex, tabsElement[this.selectedTabIndex]);
		}
	}

	selectTab(index: number, tab) {
		this.selectedTabIndex = index;
		this.selectedTabTemplate = tab;
		this.selectedTabEvent.emit(index);
		this.updateScrollState();
	}

	scrollTabs(position: "start" | "end"): void {
		const container = this.tabsContainer.nativeElement;
		const startPosition = container.scrollLeft;
		const change =
			position === "end" ? container.clientWidth : -container.clientWidth;
		const targetPosition = startPosition + change;
		const duration = 800;
		const increment = 20;
		let currentTime = 0;

		const animateScroll = () => {
			currentTime += increment;
			const val = this.easeInOutQuad(
				currentTime,
				startPosition,
				change,
				duration
			);
			container.scrollLeft = val;

			if (currentTime < duration) {
				setTimeout(animateScroll, increment);
			} else {
				container.scrollLeft = targetPosition;
			}
		};

		animateScroll();
		this.updateScrollState();
	}

	easeInOutQuad(
		currentTime: number,
		start: number,
		change: number,
		duration: number
	) {
		currentTime /= duration / 2;
		if (currentTime < 1)
			return (change / 2) * currentTime * currentTime + start;
		currentTime--;
		return (-change / 2) * (currentTime * (currentTime - 2) - 1) + start;
	}

	updateScrollState() {
		const container = this.tabsContainer.nativeElement;
		this.canScrollLeft = container.scrollLeft > 0;
		this.canScrollRight =
			container.scrollWidth > container.clientWidth + container.scrollLeft;
	}
}
