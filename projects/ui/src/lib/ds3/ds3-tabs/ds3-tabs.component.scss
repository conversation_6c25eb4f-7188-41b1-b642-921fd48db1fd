@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

.tabs {
	display: flex;
	border-bottom: 1px solid $supportGray03;
	position: relative;
	overflow: hidden;
}

.tab {
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: pointer;
	position: relative;

	&::after {
		content: "";
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		height: 3px;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
	}
}

.tab.active {
	flex: none;
	align-self: stretch;

	&::after {
		background-color: $actionDefaultAble04;
	}
}

.tab.active .tab-content {
	color: $typeDefaultTitle;
}

.tab:not(.active):hover {
	&::after {
		background-color: $actionDefaultAble02;
	}

	.tab-content {
		flex: none;
		order: 1;
		align-self: stretch;
		flex-grow: 0;
	}
}

.tab-content {
	flex: 1;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 10px 20px;
	gap: 10px;
	color: $actionDefaultDisabled02;
	width: auto;
	height: 32px;
	white-space: nowrap;
	overflow: hidden;
	@extend .pct-btn-default2;
}

.scroll-btn {
	width: 27px;
	height: 35px;
	background-color: $actionBgDarkAble03;
	border: none;
	cursor: pointer;
	z-index: 10;
	position: sticky;
	color: $supportGray05;
}

.scroll-btn:focus {
	outline: none;
}

.scroll-btn.left-btn.has-info i {
	color: $actionDefaultAble04;
}

.scroll-btn.right-btn.has-info i {
	color: $actionDefaultAble04;
}

.scroll-btn:hover {
	background-color: $actionDefaultAble01;
}

.left-btn {
	left: 0;
}

.right-btn {
	right: 0;
	position: absolute;
}

.fixed {
	position: fixed;
	margin-right: 16px;
}
