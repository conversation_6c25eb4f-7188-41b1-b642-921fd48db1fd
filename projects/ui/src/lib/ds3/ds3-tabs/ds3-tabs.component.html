<div class="tabs" #tabsContainer>
	<button
		*ngIf="canScrollLeft"
		class="scroll-btn left-btn"
		(click)="scrollTabs('start')">
		<i class="pct pct-chevron-left"></i>
	</button>
	<div
		*ngFor="let tab of tabs; let i = index"
		class="tab"
		[id]="'tab-' + id + '-' + i"
		#tabElement
		(click)="selectTab(i, tabElement)"
		[class.active]="selectedTabIndex === i">
		<div class="tab-content">{{ tab }}</div>
	</div>
	<button
		*ngIf="canScrollRight"
		class="scroll-btn right-btn fixed"
		(click)="scrollTabs('end')">
		<i class="pct pct-chevron-right"></i>
	</button>
</div>
