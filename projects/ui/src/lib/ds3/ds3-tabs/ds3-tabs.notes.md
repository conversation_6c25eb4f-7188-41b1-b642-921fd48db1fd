**Seletor:** `ds3-tabs`

**Utilização:**`<ds3-tabs></ds3-tabs>`

### Exemplo:

> meu-exemplo-de-tela.component.ts

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  tabs = ['Tab 1', 'Tab 2', 'Tab 3', 'Tab 4', 'Tab 5', 'Tab 6', 'Tab 7'];
}
```

> meu-exemplo-de-tela.component.html

```html
    <ds3-tabs [tabs]="tabs"></ds3-tabs>
```
&nbsp;  

## Props / Inputs

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Input() tabs: array                     |                     |[]                    |

## Outputs / Events

No Outputs

## More info

No more info.
