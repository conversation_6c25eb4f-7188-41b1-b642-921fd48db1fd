import { Injectable } from "@angular/core";

@Injectable({
	providedIn: "root",
})
export class PactoUtilService {
	constructor() {}

	isDescendant(parent: Element, child: Element) {
		let node = child.parentNode;
		if (parent === child) {
			return true;
		} else {
			while (node !== null) {
				if (node === parent) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}
}
