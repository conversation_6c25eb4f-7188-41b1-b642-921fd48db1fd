export enum PactoColor {
	HELLBOY_PRI = "#DB2C3D",
	PEQUIZAO_PRI = "#F0B924",
	AZULIM_PRI = "#1998fc",
	AZULIM_05 = "#0380e3",
	CINZA_CLARO_PRI = "#EFF2F7",
	AZUL_PACTO_PRI = "#1B4166",
	PRETO_PRI = "#2c343b",
	VERDINHO_PRI = "#7be542",
	CINZA_PRI = "#ECF0F1",
	CINZA_06 = "#90949A",
	GELO_PRI = "#E5E9F2",
	GELO_05 = "#92959B",
	CHUCHUZINHO = "#2EC750",
	BUBBALOO_PRI = "#ff2970",
	LARANJINHA = "#FF4B2B",
	ACAI = "#783878",
}

export class PactoColorScheme {
	private static defaultChartScheme: PactoColor[] = [
		PactoColor.HELLBOY_PRI,
		PactoColor.PEQUIZAO_PRI,
		PactoColor.GELO_05,
		PactoColor.AZULIM_PRI,
		PactoColor.VERDINHO_PRI,
		PactoColor.PRETO_PRI,
	];

	static getDefaultChartScheme(): PactoColor[] {
		const copy = Object.assign([], PactoColorScheme.defaultChartScheme);
		return copy;
	}
}
