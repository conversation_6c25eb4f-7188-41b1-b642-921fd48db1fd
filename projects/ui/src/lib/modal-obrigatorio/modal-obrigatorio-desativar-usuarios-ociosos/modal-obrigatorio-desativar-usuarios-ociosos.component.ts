import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	AfterViewInit,
	Output,
	ViewChild,
	TemplateRef,
} from "@angular/core";
import { MatDialogRef } from "@angular/material";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
} from "../../components/components-api";
import moment from "moment";
@Component({
	selector: "pacto-modal-obrigatorio-desativar-usuarios-ociosos",
	templateUrl: "./modal-obrigatorio-desativar-usuarios-ociosos.component.html",
	styleUrls: ["./modal-obrigatorio-desativar-usuarios-ociosos.component.scss"],
})
export class ModalObrigatorioDesativarUsuariosOciososComponent
	implements AfterViewInit
{
	@Input() dialogRef: MatDialogRef<any>;
	@Input() obrigatorio: boolean;
	@Input() tableData: any;
	@Output() onSubmit: EventEmitter<any> = new EventEmitter<any>();
	@ViewChild("thCodigo", { static: true })
	thCodigo: TemplateRef<any>;
	@ViewChild("celulaCodigo", { static: true })
	celulaCodigo: TemplateRef<any>;
	checks: {} = {};
	@ViewChild("tableRef", { static: false }) tableRef: RelatorioComponent;
	table: PactoDataGridConfig;
	entries;
	isLoaded = false;

	constructor(private cd: ChangeDetectorRef) {}
	ngAfterViewInit() {
		this.tableData.subscribe((v) => {
			this.entries = v.content;

			this.table = new PactoDataGridConfig({
				showFilters: false,
				quickSearch: false,
				pagination: false,
				dataAdapterFn(serverData) {
					return v || serverData;
				},
				columns: [
					{
						nome: "codigo",
						visible: true,
						ordenavel: false,
						titulo: this.thCodigo,
						celula: this.celulaCodigo,
					},
					{ titulo: "Nome", nome: "nome", visible: true, ordenavel: false },
					{
						titulo: "Perfil do usuário",
						nome: "perfil",
						visible: true,
						ordenavel: false,
					},
					{
						titulo: "Último acesso",
						nome: "ultimoAcesso",
						visible: true,
						ordenavel: false,
						valueTransform(v, row) {
							return moment(v).format("DD/MM/yyyy");
						},
					},
					{
						titulo: "Dias sem acesso",
						nome: "diasSemAcesso",
						visible: true,
						ordenavel: false,
					},
				],
			});
			this.isLoaded = true;
			this.cd.detectChanges();
		});
	}

	public checkAllRows(event): void {
		if (event.currentTarget.firstChild.checked) {
			this.checks = this.entries.reduce((acc, v) => {
				acc[v.codigo] = v;
				return acc;
			}, {});
		} else {
			this.checks = {};
		}
	}

	public checkRow(event, row): void {
		if (event.currentTarget.firstChild.checked) {
			this.checks[row.codigo] = row;
		} else {
			delete this.checks[row.codigo];
		}
	}

	get quantidadeDeItensSelecionados(): number {
		return Object.entries(this.checks).length;
	}

	desativarUsuarios() {
		this.onSubmit.emit({ usuarios: Object.values(this.checks) });
	}

	ignorarAviso() {
		this.onSubmit.emit({ usuarios: [], ignorar: true });
	}
}
