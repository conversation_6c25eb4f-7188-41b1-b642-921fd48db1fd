import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalObrigatorioDesativarUsuariosOciososComponent } from "./modal-obrigatorio-desativar-usuarios-ociosos.component";

describe("ModalObrigatorioDesativarUsuariosOciososComponent", () => {
	let component: ModalObrigatorioDesativarUsuariosOciososComponent;
	let fixture: ComponentFixture<ModalObrigatorioDesativarUsuariosOciososComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalObrigatorioDesativarUsuariosOciososComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			ModalObrigatorioDesativarUsuariosOciososComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
