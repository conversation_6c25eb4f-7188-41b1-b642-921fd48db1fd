<div matDialogTitle>
	<span>Usuários com muitos dias sem acessar o sistema</span>
	<button
		ds3-icon-button
		size="sm"
		*ngIf="!obrigatorio"
		(click)="dialogRef.close(true)">
		<i class="pct pct-x"></i>
	</button>
	<ds3-diviser></ds3-diviser>
</div>
<div matDialogContent>
	<p>
		Antes de entrar, gostaríamos que verifique se estes usuários ainda existem,
		caso o contrário pode desativá-los.
	</p>
	<div *ngIf="isLoaded">
		<pacto-relatorio
			#tableRef
			[enableDs3]="true"
			[showBtnAdd]="false"
			[showShare]="false"
			[table]="table"></pacto-relatorio>
	</div>
</div>
<div matDialogActions>
	<div class="totalizador">
		<span class="label">Usuários selecionados</span>
		<span class="value">{{ quantidadeDeItensSelecionados }}</span>
	</div>
	<button ds3-text-button (click)="ignorarAviso()">
		Não exibir novamente este aviso hoje
	</button>
	<button ds3-outlined-button (click)="dialogRef.close(true)">Cancelar</button>
	<button
		ds3-flat-button
		(click)="desativarUsuarios()"
		[disabled]="quantidadeDeItensSelecionados == 0">
		Desativar usuários
	</button>
</div>

<ng-template #thCodigo>
	<ds3-checkbox
		(click)="checkAllRows($event)"
		[checked]="
			quantidadeDeItensSelecionados != 0 &&
			quantidadeDeItensSelecionados == entries.length
		"></ds3-checkbox>
	<span>Código</span>
</ng-template>

<ng-template #celulaCodigo let-item="item" let-index="index">
	<ds3-checkbox
		(click)="checkRow($event, item)"
		[checked]="!!checks[item.codigo]"></ds3-checkbox>
	<span>{{ item.codigo }}</span>
</ng-template>
