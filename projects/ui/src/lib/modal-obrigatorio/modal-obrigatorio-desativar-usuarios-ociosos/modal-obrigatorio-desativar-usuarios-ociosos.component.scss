@import "../modal-obrigatorio-base.scss";

div[matDialogContent] {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	column-gap: 16px;

	p {
		width: 100%;
		margin: 0px 0px 8px;
		@extend .pct-title4;
	}

	div {
		width: 100%;
	}
}

div[matDialogActions] {
	.totalizador {
		margin-left: 16px;
		margin-right: auto;
		padding: 12px 16px;
		border: 1px solid var(--color-support-gray-2);
		display: flex;
		flex-direction: row;
		align-items: center;

		.label {
			@extend .pct-overline2-regular;
			color: var(--color-typography-default-text);
		}

		.value {
			padding-left: 8px;
			@extend .pct-display4;
			color: var(--color-typography-default-title);
		}
	}
}
