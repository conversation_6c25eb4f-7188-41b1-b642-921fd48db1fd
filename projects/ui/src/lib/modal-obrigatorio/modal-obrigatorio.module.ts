import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3Module } from "../ds3/ds3.module";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ModalObrigatorioAtualizacaoCadastralComponent } from "./modal-obrigatorio-atualizacao-cadastral/modal-obrigatorio-atualizacao-cadastral.component";
import { ModalObrigatorioAtualizacaoFotoComponent } from "./modal-obrigatorio-atualizacao-foto/modal-obrigatorio-atualizacao-foto.component";
import { ModalObrigatorioCertificadoNotaFiscalExpiradoComponent } from "./modal-obrigatorio-certificado-nota-fiscal-expirado/modal-obrigatorio-certificado-nota-fiscal-expirado.component";
import { ModalObrigatorioEmissaoNotaFiscalIncompletaComponent } from "./modal-obrigatorio-emissao-nota-fiscal-incompleta/modal-obrigatorio-emissao-nota-fiscal-incompleta.component";
import { ModalObrigatorioDesativarUsuariosOciososComponent } from "./modal-obrigatorio-desativar-usuarios-ociosos/modal-obrigatorio-desativar-usuarios-ociosos.component";
import { ComponentsModule } from "../components/components.module";

@NgModule({
	declarations: [
		ModalObrigatorioAtualizacaoCadastralComponent,
		ModalObrigatorioAtualizacaoFotoComponent,
		ModalObrigatorioCertificadoNotaFiscalExpiradoComponent,
		ModalObrigatorioEmissaoNotaFiscalIncompletaComponent,
		ModalObrigatorioDesativarUsuariosOciososComponent,
	],
	exports: [
		ModalObrigatorioAtualizacaoCadastralComponent,
		ModalObrigatorioAtualizacaoFotoComponent,
		ModalObrigatorioCertificadoNotaFiscalExpiradoComponent,
		ModalObrigatorioEmissaoNotaFiscalIncompletaComponent,
		ModalObrigatorioDesativarUsuariosOciososComponent,
	],
	entryComponents: [
		ModalObrigatorioAtualizacaoCadastralComponent,
		ModalObrigatorioAtualizacaoFotoComponent,
		ModalObrigatorioCertificadoNotaFiscalExpiradoComponent,
		ModalObrigatorioEmissaoNotaFiscalIncompletaComponent,
		ModalObrigatorioDesativarUsuariosOciososComponent,
	],
	imports: [
		CommonModule,
		Ds3Module,
		ComponentsModule,
		FormsModule,
		ReactiveFormsModule,
	],
})
export class ModalObrigatorioModule {}
