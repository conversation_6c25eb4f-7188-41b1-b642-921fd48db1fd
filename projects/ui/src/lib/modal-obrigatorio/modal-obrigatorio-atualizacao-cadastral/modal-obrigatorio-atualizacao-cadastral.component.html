<div matDialogTitle>
	<span>Atualização cadastral</span>
	<button
		ds3-icon-button
		size="sm"
		*ngIf="!obrigatorio"
		(click)="dialogRef.close(true)">
		<i class="pct pct-x"></i>
	</button>
	<ds3-diviser></ds3-diviser>
</div>
<div matDialogContent>
	<div class="step1" *ngIf="step == 1">
		<p>
			Bem-vindo ao Sistema Pacto! Nossa missão é facilitar seu dia a dia no
			trabalho. Antes de começarmos sua jornada, gostaríamos de atualizar
			algumas informações suas.
		</p>
		<ds3-form-field>
			<ds3-field-label>Qual é o seu nome?</ds3-field-label>
			<input ds3Input type="text" [formControl]="form.controls['nome']" />
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Qual é o seu sobrenome?</ds3-field-label>
			<input ds3Input type="text" [formControl]="form.controls['sobrenome']" />
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Qual é o seu cargo?</ds3-field-label>
			<ds3-select
				ds3Input
				[options]="cargoOptions"
				[formControl]="form.controls['cargo']"></ds3-select>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Qual é a sua função?</ds3-field-label>
			<ds3-select
				ds3Input
				[options]="funcaoOptions"
				[formControl]="form.controls['funcao']"></ds3-select>
		</ds3-form-field>
	</div>
	<div class="step2" *ngIf="step == 2">
		<p>
			Antes de concluirmos, é fundamental que você revise os termos de
			privacidade e segurança agora. Assim, garantimos que você esteja
			totalmente informado sobre como protegemos seus dados e asseguramos a
			segurança do sistema.
		</p>
		<ds3-form-field>
			<ds3-field-label>Pin</ds3-field-label>
			<input
				ds3Input
				type="password"
				autocomplete="novo-pin"
				name="novo-pin"
				[formControl]="form.controls['pin']" />
		</ds3-form-field>
		<ds3-form-field>
			<ds3-checkbox [formControl]="form.controls['aceitouTermosUso']" ds3Input>
				Li e aceito os
				<a
					class="linkTermoDeUso"
					target="_blank"
					href="https://sistemapacto.com.br/termos-de-uso/#:~:text=Seu%20perfil%20nas%20plataformas%20digitais,ou%20de%20outra%20forma%20question%C3%A1vel.">
					Termos de uso e privacidade
				</a>
				e privacidade do sistema Pacto, seus módulos e recursos.
			</ds3-checkbox>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-checkbox [formControl]="form.controls['novidadesEmail']" ds3Input>
				Autorizo o envio de novidades e atualizações do sistema e da Pacto
				Soluções através do e-mail informado.
			</ds3-checkbox>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-checkbox [formControl]="form.controls['novidadesSms']" ds3Input>
				Deseja receber novidades e atualizações do sistema e da Pacto Soluções
				via SMS e autorizo o recebimento através do celular informado.
			</ds3-checkbox>
		</ds3-form-field>
	</div>
</div>

<div matDialogActions>
	<button
		ds3-outlined-button
		*ngIf="!obrigatorio"
		(click)="dialogRef.close(true)">
		Fechar
	</button>
	<button
		ds3-flat-button
		(click)="step = 2"
		*ngIf="step == 1"
		[disabled]="!firstStepValid">
		Continuar
	</button>
	<button ds3-outlined-button (click)="step = 1" *ngIf="step == 2">
		Voltar
	</button>
	<button
		ds3-flat-button
		(click)="submitButtonClick()"
		*ngIf="step == 2"
		[disabled]="!form.valid">
		Concluir
	</button>
</div>
