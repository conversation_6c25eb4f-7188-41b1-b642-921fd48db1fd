import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatDialogRef } from "@angular/material";

@Component({
	selector: "pacto-modal-obrigatorio-atualizacao-cadastral",
	templateUrl: "./modal-obrigatorio-atualizacao-cadastral.component.html",
	styleUrls: ["./modal-obrigatorio-atualizacao-cadastral.component.scss"],
})
export class ModalObrigatorioAtualizacaoCadastralComponent implements OnInit {
	@Input() dialogRef: MatDialogRef<any>;
	@Input() obrigatorio: boolean;
	@Input() form = new FormGroup({
		nome: new FormControl(null, Validators.required),
		sobrenome: new FormControl(null, Validators.required),
		cargo: new FormControl(null, Validators.required),
		funcao: new FormControl(null, Validators.required),
		pin: new FormControl(null, Validators.required),
		aceitouTermosUso: new FormControl(null, Validators.requiredTrue),
		novidadesEmail: new FormControl(null),
		novidadesSms: new FormControl(null),
	});
	@Input() cargoOptions: Array<any> = new Array();
	@Input() funcaoOptions: Array<any> = new Array();
	@Output() onSubmit: EventEmitter<any> = new EventEmitter<any>();
	step = 1;
	constructor() {}

	ngOnInit() {}

	get firstStepValid() {
		return (
			this.form.controls["nome"].valid &&
			this.form.controls["sobrenome"].valid &&
			this.form.controls["cargo"].valid &&
			this.form.controls["funcao"].valid
		);
	}

	submitButtonClick() {
		if (this.form.valid) {
			this.onSubmit.emit(this.form.getRawValue());
		}
	}
}
