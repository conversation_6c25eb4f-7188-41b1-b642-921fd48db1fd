@import "../../../assets/ui-kit.scss";
@import "../../../assets/import.scss";

::ng-deep#modal-obrigatorio {
	padding: 0px;
}

div[matDialogTitle] {
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	padding: 12px 16px 8px 16px;
	flex-direction: row;
	flex-wrap: wrap;

	span {
		@extend .pct-title4;
		height: 18px;
	}

	ds3-diviser {
		width: calc(100% + 32px);
		margin: 3px -16px 0px -16px;
	}
}

div[matDialogContent] {
	padding: 24px 16px;
}

div[matDialogActions] {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	column-gap: 16px;
	padding: 0px 16px 16px 0px;
}
