import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
	selector: "pacto-modal-obrigatorio-emissao-nota-fiscal-incompleta",
	templateUrl:
		"./modal-obrigatorio-emissao-nota-fiscal-incompleta.component.html",
	styleUrls: [
		"./modal-obrigatorio-emissao-nota-fiscal-incompleta.component.scss",
	],
})
export class ModalObrigatorioEmissaoNotaFiscalIncompletaComponent
	implements OnInit
{
	@Input() dialogRef;
	@Input() obrigatorio: boolean;
	@Input() valorMensalEmitir: number = 0;
	@Input() valorEmitido: number = 0;
	@Output() onSubmit: EventEmitter<"hoje" | "depois"> = new EventEmitter();
	constructor() {}

	ngOnInit() {}

	ignorarAviso(ignorarPor: "hoje" | "depois") {
		this.onSubmit.emit(ignorarPor);
	}

	// abrirAviso() {
	// 	this.clientDiscoveryService
	// 	 .linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
	// 	 .subscribe((urlZw) => {
	// 	  let url = `${urlZw}&funcionalidadeNome=CONFIGURACAO_NOTAFISCAL&jspPage=configuracaoNotaFiscalCons.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
	// 	  url += `&matriculaCliente=${this.matricula}&&origem=angular`;
	// 	  this.abrirPopup(url, "Questionario", 800, 595);
	// 	 });
	//   }
}
