import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalObrigatorioEmissaoNotaFiscalIncompletaComponent } from "./modal-obrigatorio-emissao-nota-fiscal-incompleta.component";

describe("ModalObrigatorioEmissaoNotaFiscalIncompletaComponent", () => {
	let component: ModalObrigatorioEmissaoNotaFiscalIncompletaComponent;
	let fixture: ComponentFixture<ModalObrigatorioEmissaoNotaFiscalIncompletaComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalObrigatorioEmissaoNotaFiscalIncompletaComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			ModalObrigatorioEmissaoNotaFiscalIncompletaComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
