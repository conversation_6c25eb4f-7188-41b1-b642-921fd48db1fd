import { Component, Input, OnInit } from "@angular/core";
import { MatDialogRef } from "@angular/material";

@Component({
	selector: "pacto-modal-obrigatorio-certificado-nota-fiscal-expirado",
	templateUrl:
		"./modal-obrigatorio-certificado-nota-fiscal-expirado.component.html",
	styleUrls: [
		"./modal-obrigatorio-certificado-nota-fiscal-expirado.component.scss",
	],
})
export class ModalObrigatorioCertificadoNotaFiscalExpiradoComponent
	implements OnInit
{
	@Input() dialogRef: MatDialogRef<any>;
	@Input() obrigatorio: boolean = false;
	@Input() certificados: any[] = [];
	constructor() {}
	ngOnInit() {}

	// abrirAviso() {
	// 	this.clientDiscoveryService
	// 	 .linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
	// 	 .subscribe((urlZw) => {
	// 	  let url = `${urlZw}&funcionalidadeNome=CONFIGURACAO_NOTAFISCAL&jspPage=configuracaoNotaFiscalCons.jsp&codPessoa=${this.dadosPessoais.codigoPessoa}`;
	// 	  url += `&matriculaCliente=${this.matricula}&&origem=angular`;
	// 	  this.abrirPopup(url, "Questionario", 800, 595);
	// 	 });
	//   }
}
