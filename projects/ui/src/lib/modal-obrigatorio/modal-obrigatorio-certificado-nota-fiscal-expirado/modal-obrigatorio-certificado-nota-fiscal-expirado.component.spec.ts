import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ModalObrigatorioCertificadoNotaFiscalExpiradoComponent } from "./modal-obrigatorio-certificado-nota-fiscal-expirado.component";

describe("ModalObrigatorioCertificadoNotaFiscalExpiradoComponent", () => {
	let component: ModalObrigatorioCertificadoNotaFiscalExpiradoComponent;
	let fixture: ComponentFixture<ModalObrigatorioCertificadoNotaFiscalExpiradoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ModalObrigatorioCertificadoNotaFiscalExpiradoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			ModalObrigatorioCertificadoNotaFiscalExpiradoComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
