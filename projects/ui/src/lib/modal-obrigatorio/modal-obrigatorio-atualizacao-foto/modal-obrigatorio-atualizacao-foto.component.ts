import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MatDialogRef } from "@angular/material";
@Component({
	selector: "pacto-modal-obrigatorio-atualizacao-foto",
	templateUrl: "./modal-obrigatorio-atualizacao-foto.component.html",
	styleUrls: ["./modal-obrigatorio-atualizacao-foto.component.scss"],
})
export class ModalObrigatorioAtualizacaoFotoComponent implements OnInit {
	@Input() dialogRef: MatDialogRef<any>;
	@Input() obrigatorio: boolean = false;
	@Input() form = new FormGroup({
		foto: new FormControl(" "),
		ignorar: new FormControl(false),
	});
	imageDisplay;
	@Output() onSubmit: EventEmitter<any> = new EventEmitter<any>();
	step = 0;
	constructor() {}

	ngOnInit() {}
	submitButtonClick() {
		setTimeout(() => {
			this.onSubmit.emit(this.form.getRawValue());
		}, 250);
	}
	public capturar(event: string): void {
		this.form.get("foto").setValue(event);
		this.imageDisplay = event;
		this.step = 2;
	}

	updateImage(ev) {
		const file = ev.target.files[0];

		const reader = new FileReader();
		reader.onloadend = () => {
			const base64Image = reader.result as string;
			this.imageDisplay = base64Image;
			this.form.get("foto").setValue(base64Image);
			this.step = 2;
		};

		if (file) {
			reader.readAsDataURL(file);
		}
	}
}
