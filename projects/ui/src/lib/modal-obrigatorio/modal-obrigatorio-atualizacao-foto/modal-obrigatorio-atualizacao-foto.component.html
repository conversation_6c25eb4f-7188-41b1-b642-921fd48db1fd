<div matDialogTitle>
	<span>Atualização cadastral</span>
	<button
		ds3-icon-button
		size="sm"
		*ngIf="!obrigatorio"
		(click)="dialogRef.close(true)">
		<i class="pct pct-x"></i>
	</button>
	<ds3-diviser></ds3-diviser>
</div>

<div matDialogContent>
	<ng-container *ngIf="step == 0">
		<p>
			Que tal deixar seu perfil mais completo? Adicionar uma foto ajuda a
			personalizar sua experiência.
		</p>
		<button
			ds3-text-button
			*ngIf="!form.controls['ignorar'].value"
			(click)="step = 1">
			Adicionar foto
		</button>
		<ds3-form-field>
			<ds3-checkbox [formControl]="form.controls['ignorar']" ds3Input>
				Não exibir aviso novamente
			</ds3-checkbox>
		</ds3-form-field>
	</ng-container>

	<ng-container *ngIf="step == 1">
		<pacto-cat-camera (images)="capturar($event)" [height]="480" [width]="640">
			<div class="buttons">
				<button ds3-text-button (click)="inputFile.click()">
					Enviar como arquivo
				</button>
				<button ds3-text-button pactoCatCameraCapture>Captura</button>
			</div>
		</pacto-cat-camera>

		<input
			type="file"
			#inputFile
			hidden
			accept="image/*"
			(change)="updateImage($event)"
			[formControl]="form.controls['foto']" />
	</ng-container>

	<ng-container *ngIf="step == 2">
		<img id="previewImage" [src]="imageDisplay" *ngIf="imageDisplay" />
	</ng-container>
</div>

<div matDialogActions>
	<button
		ds3-flat-button
		*ngIf="step == 0 && form.controls['ignorar'].value"
		(click)="submitButtonClick()">
		Salvar mudança
	</button>

	<button ds3-outlined-button (click)="step = 1" *ngIf="step == 2">
		Editar Imagem
	</button>

	<button ds3-flat-button *ngIf="step == 2" (click)="submitButtonClick()">
		Salvar imagem
	</button>
</div>
