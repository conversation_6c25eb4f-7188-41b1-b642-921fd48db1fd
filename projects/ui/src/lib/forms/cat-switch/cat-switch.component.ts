import { Component, Input, OnInit } from "@angular/core";
import { FormControl } from "@angular/forms";

export enum SWITCH_TYPE {
	PRIMARY = "PRIMARY",
	GREEN = "GREEN",
}

@Component({
	selector: "pacto-cat-switch",
	templateUrl: "./cat-switch.component.html",
	styleUrls: ["./cat-switch.component.scss"],
})
export class CatSwitchComponent implements OnInit {
	@Input() control: FormControl;
	@Input() type: SWITCH_TYPE = SWITCH_TYPE.PRIMARY;

	constructor() {}

	ngOnInit() {}

	get SWITCH_TYPE() {
		return SWITCH_TYPE;
	}
}
