<div class="row">
	<div class="col-md-6">
		<pacto-cat-form-input
			[control]="fg.get('nome')"
			[errorMsg]="'Campo Obrigatório.'"
			[label]="'Nome*'"></pacto-cat-form-input>
	</div>
	<div class="col-md-6">
		<pacto-cat-form-textarea
			[control]="fg.get('descricao')"
			[label]="'Fale mais sobre você...'"></pacto-cat-form-textarea>
	</div>
</div>
<div class="row">
	<div class="col-md-6">
		<pacto-cat-form-input
			[control]="fg.get('rg')"
			[errorMsg]="'Forneça um RG no formato (13.131.312-3).'"
			[label]="'RG*'"
			[textMask]="{ mask: rgMask, guide: false }"></pacto-cat-form-input>
	</div>
	<div class="col-md-6">
		<pacto-cat-form-select
			[control]="fg.get('estado')"
			[items]="estados"
			[label]="'Estado'"></pacto-cat-form-select>
	</div>
</div>
<div class="row">
	<div class="col-md-6">
		<pacto-cat-form-multi-select-filter
			[control]="fg.get('professores')"
			[errorMsg]="'Selecione pelo penos um professor.'"
			[labelKey]="'nome'"
			[label]="'Professores'"
			[options]="professores"
			[resposeParser]="resposeParser"></pacto-cat-form-multi-select-filter>
	</div>
</div>
<div class="row">
	<div class="col-md-12 footer">
		<pacto-cat-button
			(click)="salvarHandler()"
			[label]="'Salvar'"></pacto-cat-button>
	</div>
</div>
