import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormControl } from "@angular/forms";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-checkbox",
	templateUrl: "./cat-checkbox.component.html",
	styleUrls: ["./cat-checkbox.component.scss"],
})
export class CatCheckboxComponent implements OnInit {
	@Input() checkBlue: boolean;
	@Input() textBlue: boolean;
	@Input() control: FormControl;
	@Input() label: string;
	@Input() id: string;
	@Output() valueChange = new EventEmitter<boolean>();

	constructor() {}

	ngOnInit() {
		if (this.control) {
			this.control.valueChanges.subscribe((value) => {
				this.valueChange.emit(value);
			});
		}
		if (!this.id) {
			this.id = `input-check-${uniqueId++}`;
		}
	}
}
