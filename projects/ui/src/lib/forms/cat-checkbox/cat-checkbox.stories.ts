import { Component, Input } from "@angular/core";
import { FormsModule, FormControl, ReactiveFormsModule } from "@angular/forms";

import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatCheckboxComponent } from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        height: 600px;
    ">
			<pacto-cat-checkbox
				[control]="control"
				[label]="label"></pacto-cat-checkbox>
			<div style="margin-top: 30px">
				Valor selecionado disabled (false): {{ control.value }}
			</div>
			<div style="margin-top: 50px">
				<pacto-cat-checkbox
					[control]="control2"
					[label]="label2"></pacto-cat-checkbox>
			</div>
			<div style="margin-top: 30px">
				Valor selecionado disabled (true): {{ control2.value }}
			</div>
		</div>
	`,
})
class HostComponent {
	@Input() control;
	@Input() control2;
	@Input() label;
	@Input() label2;
}

const metadata = {
	imports: [FormsModule, ReactiveFormsModule],
	declarations: [CatCheckboxComponent],
};

storiesOf("Forms|Checkbox", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Single checkbox", () => {
		return {
			component: HostComponent,
			props: {
				control: new FormControl(true),
				label: "I agree with the terms. Disabled (false)",
				control2: new FormControl({ value: true, disabled: true }),
				label2: "I agree with the terms. Disabled (true)",
			},
		};
	});
