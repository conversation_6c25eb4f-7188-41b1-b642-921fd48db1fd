<div class="cat-input-wrapper">
	<label>
		<input
			[attr.disabled]="control.disabled ? '' : null"
			[formControl]="control"
			id="{{ id }}-input"
			type="checkbox" />
		<img
			*ngIf="!checkBlue"
			class="checkmark-outline"
			src="pacto-ui/images/input-checkbox/checkmark-outline.svg" />
		<img
			*ngIf="!checkBlue"
			class="checkmark-outline-cut"
			src="pacto-ui/images/input-checkbox/checkmark-outline-cut.svg" />
		<img
			*ngIf="!checkBlue"
			class="checkmark"
			src="pacto-ui/images/input-checkbox/checkmark.svg" />
		<img
			*ngIf="checkBlue"
			class="checkmark-outline"
			src="pacto-ui/images/input-checkbox/checkmark-outline-blue.svg" />
		<img
			*ngIf="checkBlue"
			class="checkmark-outline-cut"
			src="pacto-ui/images/input-checkbox/checkmark-outline-cut-blue.svg" />
		<img
			*ngIf="checkBlue"
			class="checkmark"
			src="pacto-ui/images/input-checkbox/checkmark-blue.svg" />
		<div class="{{ textBlue ? 'textBlue' : 'text' }}">
			{{ label }}
		</div>
	</label>
</div>
