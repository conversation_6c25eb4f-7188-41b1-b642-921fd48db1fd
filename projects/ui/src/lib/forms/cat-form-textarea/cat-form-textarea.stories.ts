import { Component, OnInit } from "@angular/core";
import {
	FormsModule,
	FormControl,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { storiesOf, moduleMetadata } from "@storybook/angular";
import { TextMaskModule } from "angular2-text-mask";

import { CatFormTextareaComponent } from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-form-textarea.stories.html",
	styleUrls: ["./cat-form-textarea.stories.scss"],
})
class HostComponent implements OnInit {
	control = new FormControl(1);
	errorControl = new FormControl("", [
		Validators.required,
		Validators.minLength(10),
	]);

	ngOnInit() {
		this.errorControl.markAsTouched();
	}
}

const metadata = {
	imports: [FormsModule, ReactiveFormsModule, TextMaskModule],
	declarations: [CatFormTextareaComponent],
};

storiesOf("Forms|Textarea", module)
	.addDecorator(moduleMetadata(metadata))
	.add("States", () => {
		return {
			component: HostComponent,
		};
	});
