@import "projects/ui/assets/import.scss";

:host {
	display: block;
	margin: 15px 0px;
}

.textarea-label {
	@extend .type-h6;
	color: $gelo04;
	line-height: 2em;
	min-height: 32px;
	padding-left: 3px;
}

.textarea-icon {
	margin-left: -25px;
	margin-top: 9px;
	font-size: 15px;
	position: absolute;
}

.textarea-icon:hover {
	cursor: pointer;
}

textarea.error:focus {
	box-shadow: 0 0 0 0.2rem rgba($hellboyPri, 0.5);
}

textarea {
	border-radius: 3px;
	border: 1px solid $gelo03;
	color: $gelo05;
	padding: 10px;
	width: 100%;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.pct-error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	margin-top: 5px;
	line-height: 2em;
	min-height: 24px;
	padding-left: 5px;
}
