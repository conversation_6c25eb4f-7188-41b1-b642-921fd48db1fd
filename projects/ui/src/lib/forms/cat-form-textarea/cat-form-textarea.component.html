<div class="textarea-label">
	<span *ngIf="label">{{ label }}</span>
	<ng-container *ngIf="!label">
		<ng-content select="label"></ng-content>
	</ng-container>
</div>

<textarea
	[attr.disabled]="disabled ? true : null"
	[formControl]="control"
	[maxlength]="maxlength"
	[ngClass]="{ error: showError }"
	id="{{ id }}"
	name="name"
	style="resize: vertical"
	placeholder="{{ placeholder }}"
	rows="{{ rows }}"></textarea>
<i
	(click)="iconClickEvent()"
	*ngIf="icon"
	class="pct pct-{{ icon }} textarea-icon"
	title="{{ iconToolTip }}"></i>
<div *ngIf="errorMsg" class="pct-error-msg">
	<span *ngIf="showError">{{ errorMsg }}</span>
</div>
