import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-cat-form-textarea",
	templateUrl: "./cat-form-textarea.component.html",
	styleUrls: ["./cat-form-textarea.component.scss"],
})
export class CatFormTextareaComponent implements OnInit {
	@Input() id: string;
	@Input() name: string;
	@Input() label: string;
	@Input() errorMsg: string;
	@Input() rows = 3;
	@Input() disabled = false;
	@Input() placeholder: string;
	@Input() control: FormControl;
	@Input() icon: string;
	@Input() iconToolTip = "";
	@Input() maxlength: number;
	@Output() iconClick: EventEmitter<any> = new EventEmitter();

	constructor() {}

	ngOnInit() {}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get isInvalid() {
		return this.control && this.control.touched && !this.control.valid;
	}

	iconClickEvent() {
		this.iconClick.emit({ icon: this.icon, formControl: this.control });
	}
}
