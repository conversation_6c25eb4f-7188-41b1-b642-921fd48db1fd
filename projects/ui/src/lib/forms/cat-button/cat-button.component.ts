import { Component, Input, OnInit } from "@angular/core";

export enum BUTTON_TYPE {
	PRIMARY = "PRIMARY",
	OUTLINE = "OUTLINE",
	OUTLINE_ALERT = "OUTLINE_ALERT",
	OUTLINE_DARK = "OUTLINE_DARK",
	OUTLINE_SUCCESS = "OUTLINE_SUCCESS",
	OUTLINE_ACTION = "OUTLINE_ACTION",
	ACTION = "ACTION",
	ALERT = "ALERT",
	DARK = "DARK",
	SUCCESS = "SUCCESS",
	SUCCESS_PRI = "SUCCESS_PRI",
	NO_BORDER = "NO_BORDER",
	BLACK_BORDER = "BLACK_BORDER",
	PRIMARY_NO_TEXT_TRANSFORM = "PRIMARY_NO_TEXT_TRANSFORM",
	SECUNDARY = "SECUNDARY",
	ALERT_PARCELAS = "ALERT_PARCELAS",
	ALERT_DELETE = "ALERT_DELETE",
	OUTLINE_DANGER = "OUTLINE_DANGER",
	OUTLINE_GRAY = "OUTLINE_GRAY",
	PRIMARY_ADD = "PRIMARY_ADD",
	OUTLINE_FILTER = "OUTLINE_FILTER",
	OUTLINE_MODIFY = "OUTLINE_MODIFY",
	OUTLINE_NO_BORDER = "OUTLINE_NO_BORDER",
}

export enum BUTTON_SIZE {
	SMALL = "SMALL",
	NORMAL = "NORMAL",
	LARGE = "LARGE",
}

@Component({
	selector: "pacto-cat-button",
	templateUrl: "./cat-button.component.html",
	styleUrls: ["./cat-button.component.scss"],
})
export class CatButtonComponent implements OnInit {
	@Input() type: BUTTON_TYPE = BUTTON_TYPE.PRIMARY;
	@Input() size: BUTTON_SIZE = BUTTON_SIZE.NORMAL;
	@Input() icon: string;
	@Input() id: string;
	@Input() label = "Button";
	@Input() loading = null;
	@Input() apenasIcone = false;
	@Input() disabled: boolean | (() => boolean) = false;
	@Input() full = false;
	@Input() v2 = false;
	@Input() width: string;
	@Input() height: string;
	@Input() qntNotificacoes = 0;
	@Input() iconPosition: "after" | "before" = "before";

	constructor() {}

	get isDisabled() {
		if (this.disabled instanceof Function) {
			return this.disabled();
		} else {
			return this.disabled;
		}
	}

	get BUTTON_TYPE() {
		return BUTTON_TYPE;
	}

	get large() {
		return this.size === BUTTON_SIZE.LARGE;
	}

	get small() {
		return this.size === BUTTON_SIZE.SMALL;
	}

	ngOnInit() {
		this.setUpId();
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 100);
			this.id = `pacto-btn-${rdn}`;
		}
	}
}
