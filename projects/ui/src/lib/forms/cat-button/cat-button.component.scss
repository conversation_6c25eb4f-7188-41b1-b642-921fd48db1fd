@import "projects/ui/assets/import.scss";

.pacto-button {
	border: 0px;
	padding: 0px;
	box-shadow: none;
	border-radius: 4px;
	line-height: 32px;
	cursor: pointer;
	position: relative;
	outline: 0;
	@extend .type-btn-bold;

	.pct {
		font-size: 14px;
		position: relative;
		padding-right: 5px;
		top: 2px;
	}

	.content {
		padding: 0px 10px;
	}

	@media (max-width: 1700px) {
		.content {
			padding: 0px 10px;

			&.apenasIcone {
				.icon-before {
					margin-right: 0;
				}

				.lbl {
					display: none;
				}

				&:hover {
					.lbl {
						display: inline-block;
					}

					.icon-before {
						margin-right: 4px;
					}
				}
			}
		}
	}

	&.full {
		width: 100%;
	}

	&.small {
		line-height: 24px;
		font-size: 8px;

		.pct {
			font-size: 12px;
		}
	}

	&.large {
		line-height: 40px;
		font-size: 16px;
	}

	&:disabled {
		background-color: $cinzaClaro02;
		cursor: not-allowed;
		border-color: $cinzaClaro02;
		color: $cinza05;

		.pct {
			color: $cinza05;
		}
	}

	&.v2 {
		text-transform: inherit;
		font-size: 16px;
		line-height: 40px;
		font-weight: 700;

		&:hover {
			box-shadow: 0px 4px 6px #e4e5e6;
		}

		.content {
			padding: 0 20px;
		}

		.pct {
			font-size: 16px;
			line-height: 40px;
		}
	}

	.icon-before {
		padding-right: unset;
		margin-right: 4px;
	}

	.icon-after {
		padding-right: unset;
		margin-left: 4px;
	}

	&.pacto-primary-add {
		line-height: 38px;
	}
}

.pacto-button {
	.content {
		text-transform: lowercase;

		span {
			display: inline-block;

			&::first-letter {
				text-transform: uppercase !important;
			}
		}
	}
}

.pacto-dark {
	background-color: $pretoPri;
	border: 1px solid $pretoPri;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-primary {
	background-color: #0380e3;
	border: 1px solid $azulimPri;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-alert {
	background-color: $hellboyPri;
	border: 1px solid $hellboyPri;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-alert-delete {
	background-color: $hellboy05;
	border: 1px solid $hellboy05;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-outline {
	background-color: transparent;
	border: 1px solid $azulimPri;
	color: $azulimPri;

	.pct {
		color: $azulimPri;
	}
}

.pacto-outline-dark {
	background-color: transparent;
	border: 1px solid $pretoPri;
	color: $pretoPri;

	.pct {
		color: $pretoPri;
	}
}

.pacto-outline-success {
	background-color: transparent;
	border: 1px solid $verdinho04;
	color: $verdinho04;

	.pct {
		color: $verdinho04;
	}
}

.pacto-outline-danger {
	background-color: transparent;
	border: 1px solid $hellboyPri;
	color: $hellboyPri;

	.pct {
		color: $hellboyPri;
	}
}

.pacto-success {
	background-color: $verdinho04;
	border: 1px solid $verdinho04;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-success-pri {
	background-color: $chuchuzinhoPri;
	border: 1px solid $chuchuzinhoPri;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-no-border {
	background-color: transparent;
	color: $pretoPri;
	text-transform: none;

	.pct {
		color: $pretoPri;
	}
}

.pacto-black-border {
	background-color: transparent;
	border: 1px solid #6f747b;
	text-transform: none;

	.pct {
		border: 1px solid #6f747b;
	}
}

.pacto-primary-no-text-transform {
	background-color: $azulimPri;
	border: 1px solid $azulimPri;
	text-transform: none;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-action {
	border: 1px solid #fafafa;
	border-radius: 4px;
	color: $azulPacto02;
	padding-left: 24px;
	padding-right: 24px;
	white-space: nowrap;
	text-transform: none;
}

.pacto-action.large {
	font-weight: bold;
	font-size: 16px;
	height: 40px;
	line-height: 22px;

	i {
		font-size: 20px;
	}
}

.pacto-alert-parcelas {
	border-radius: 4px;
	padding-left: 24px;
	padding-right: 24px;
	white-space: nowrap;
	text-transform: none;
	border: 1px solid $hellboyPri;
	color: white;
	background-color: $hellboyPri;
}

.pacto-alert-parcelas.large {
	font-weight: bold;
	height: 40px;
	line-height: 40px;
	font-size: 16px;

	i {
		font-size: 20px;
	}
}

.pacto-outline-action {
	border: 1px solid #b4b7bb;
	box-sizing: border-box;
	border-radius: 4px;
	background: #ffffff;
	font-weight: bold;
	font-size: 14px;
	line-height: 22px;
	color: $preto02;
	text-transform: none;
}

.pacto-secundary {
	border: 1px solid #fafafa;
	background: #fafafa;
	color: #25598b;
}

.pacto-outline-gray {
	border: 1px solid $cinzaPri;
	color: $cinzaPri;
	background: none;
}

.pacto-outline-filter {
	font-weight: 700;
	color: #0380e3;
	border: 1px solid $azulim05;
	background: none;
}

.pacto-primary-add {
	height: 40px;
	background-color: $azulim05;
	border: 1px solid $azulim05;
	color: $branco;

	.pct {
		color: $branco;
	}
}

.pacto-modify-data {
	color: #1b4166;
	background-color: #fafafa;
	font-weight: bold;
	box-shadow: 0px 4px 6px #e4e5e6;
	padding-left: 10px;
	padding-right: 10px;
}

.pacto-outline-no-border {
	color: $azulim05;
	background-color: transparent;
	font-weight: 600;
	box-shadow: none;
	padding-left: 10px;
	padding-right: 10px;

	&:hover {
		background: #b4cafd;
	}
}
