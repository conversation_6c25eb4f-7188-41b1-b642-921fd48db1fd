import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatButtonComponent, BUTTON_TYPE } from "projects/ui/src/public-api";
import { Component } from "@angular/core";

@Component({
	selector: "pacto-cat-host",
	styleUrls: ["./cat-button.stories.scss"],
	templateUrl: "./cat-button.stories.html",
})
class HostComponent {}

const metadata = {
	declarations: [CatButtonComponent],
};

storiesOf("Button", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Button", () => {
		return {
			component: HostComponent,
			props: {
				type: BUTTON_TYPE.PRIMARY,
				icon: "",
				label: "Send",
				loading: "",
				disabled: "",
			},
		};
	});
