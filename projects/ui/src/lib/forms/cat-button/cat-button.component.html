<button
	[attr.disabled]="isDisabled ? '' : null"
	[ngClass]="{
		v2: v2,
		full: full,
		large: large,
		small: small,
		'pacto-primary': type === BUTTON_TYPE.PRIMARY,
		'pacto-outline': type === BUTTON_TYPE.OUTLINE,
		'pacto-action': type === BUTTON_TYPE.ACTION,
		'pacto-outline-action': type === BUTTON_TYPE.OUTLINE_ACTION,
		'pacto-outline-dark': type === BUTTON_TYPE.OUTLINE_DARK,
		'pacto-outline-alert': type === BUTTON_TYPE.OUTLINE_ALERT,
		'pacto-outline-success': type === BUTTON_TYPE.OUTLINE_SUCCESS,
		'pacto-dark': type === BUTTON_TYPE.DARK,
		'pacto-alert': type === BUTTON_TYPE.ALERT,
		'pacto-alert-parcelas': type === BUTTON_TYPE.ALERT_PARCELAS,
		'pacto-alert-delete': type === BUTTON_TYPE.ALERT_DELETE,
		'pacto-success': type === BUTTON_TYPE.SUCCESS,
		'pacto-success-pri': type === BUTTON_TYPE.SUCCESS_PRI,
		'pacto-no-border': type === BUTTON_TYPE.NO_BORDER,
		'pacto-black-border': type === BUTTON_TYPE.BLACK_BORDER,
		'pacto-primary-no-text-transform':
			type === BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
		'pacto-secundary': type === BUTTON_TYPE.SECUNDARY,
		'pacto-outline-danger': type === BUTTON_TYPE.OUTLINE_DANGER,
		'pacto-outline-gray': type === BUTTON_TYPE.OUTLINE_GRAY,
		'pacto-primary-add': type === BUTTON_TYPE.PRIMARY_ADD,
		'pacto-outline-filter': type === BUTTON_TYPE.OUTLINE_FILTER,
		'pacto-outline-no-border': type === BUTTON_TYPE.OUTLINE_NO_BORDER,
		'pacto-modify-data': type === BUTTON_TYPE.OUTLINE_MODIFY
	}"
	[style.height]="height"
	[style.width]="width"
	[attr.data-cy]="id"
	class="pacto-button"
	id="{{ id }}">
	<div [ngClass]="{ apenasIcone: apenasIcone }" class="content">
		<ng-container *ngIf="iconPosition === 'before'">
			<i *ngIf="icon" class="pct pct-{{ icon }} icon-before"></i>
		</ng-container>
		<span *ngIf="label && qntNotificacoes == 0" class="lbl">{{ label }}</span>
		<span *ngIf="label && qntNotificacoes > 0" class="lbl">
			{{ label }} ({{ qntNotificacoes }})
		</span>
		<ng-container *ngIf="iconPosition === 'after'">
			<i *ngIf="icon" class="pct pct-{{ icon }} icon-after"></i>
		</ng-container>
	</div>
</button>
