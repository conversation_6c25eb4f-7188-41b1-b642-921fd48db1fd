import { Component, OnInit, Input, ViewChild, ElementRef } from "@angular/core";

import { fromEvent } from "rxjs";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";

declare var moment: any;
declare var $;

@Component({
	/* tslint:disable-next-line */
	selector: "pacto-datepicker",
	templateUrl: "./datepicker.component.html",
	styleUrls: ["./datepicker.component.scss"],
})
export class DatepickerComponent implements OnInit {
	@Input() id;
	@Input() name;
	@Input() label;
	@Input() required = false;
	@Input() disabled;
	@Input() pactoFormGroup: FormGroup;
	@Input() message = "Campo obrigatório";
	@Input() control: FormControl;
	@Input() maxDate: Date;
	@Input() minDate: NgbDateStruct = { year: 1918, month: 1, day: 1 };
	@Input() direction;
	@ViewChild("dp", { static: true }) dp;
	open = false;
	dataLimite: any = null;

	constructor(private elementRef: ElementRef) {}

	ngOnInit() {
		this.elementRef.nativeElement.id = this.id;

		this.configureDatepickerClose();
		if (this.direction === "right") {
			this.dp.placement = "bottom-right";
		} else {
			this.dp.placement = "bottom-left";
		}

		if (!this.control) {
			this.control = new FormControl(undefined, []);
			this.pactoFormGroup.addControl(this.name, this.control);
		}
		this.inserirDataLimite();
		this.setUpId();
	}

	private inserirDataLimite() {
		if (this.maxDate) {
			this.dataLimite = {};
			this.dataLimite.day = this.maxDate.getDate();
			this.dataLimite.month = this.maxDate.getMonth() + 1;
			this.dataLimite.year = this.maxDate.getFullYear();
		}
	}

	blurHandler() {
		const value = this.control.value;
		const momentDate = moment(parseInt(value, 10));
		if (value && !momentDate.isValid()) {
			this.control.reset();
		}
	}

	calendarClickHandler($event) {
		$event.stopPropagation();
		if (!this.control.disabled) {
			this.dp.toggle();
		}
	}

	get showError() {
		const unset = this.control.value === null;
		return this.required && unset && this.control.touched;
	}

	private configureDatepickerClose() {
		fromEvent(window, "click").subscribe(($event) => {
			const target = $($event.target);
			const internalClick = target.closest("pacto-datepicker").length;
			if (!internalClick) {
				this.dp.close();
				this.open = false;
			}
		});
	}

	private toggle() {
		if (this.open) {
			this.dp.close();
		} else {
			this.dp.open();
		}
		this.open = !this.open;
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `input-component-${rdn}`;
		}
	}
}
