<div
	[ngClass]="{
		'calendar-open': open,
		'has-danger': showError
	}"
	class="form-group edit-datepicker">
	<label *ngIf="label" class="control-label">{{ label }}</label>
	<div class="wrapper">
		<input
			#dp="ngbDatepicker"
			(blur)="blurHandler()"
			[disabled]="disabled ? disabled : null"
			[formControl]="control"
			[maxDate]="dataLimite"
			[minDate]="minDate"
			class="form-control form-control-sm width-datepicker"
			id="{{ id ? id + '-input' : null }}"
			name="inicio-date"
			ngbDatepicker
			type="text" />
		<i
			(click)="calendarClickHandler($event)"
			class="toggle-icon pct pct-calendar"></i>
		<small *ngIf="showError" class="form-control-feedback">{{ message }}</small>
	</div>
</div>
