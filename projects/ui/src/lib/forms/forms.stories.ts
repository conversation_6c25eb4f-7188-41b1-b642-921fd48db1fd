import {
	FormGroup,
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { Component } from "@angular/core";

import { storiesOf, moduleMetadata } from "@storybook/angular";
import { TextMaskModule } from "angular2-text-mask";
import {
	CatFormInputComponent,
	CatMultiSelectFilterComponent,
	CatFormMultiSelectFilterComponent,
	CatButtonComponent,
	CatFormTextareaComponent,
	CatSmoothScrollDirective,
	CatFormSelectComponent,
	CatPersonAvatarComponent,
} from "projects/ui/src/public-api";
import { HttpClientModule } from "@angular/common/http";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./forms.stories.html",
	styleUrls: ["./forms.stories.scss"],
})
class HostComponent {
	estados = [{ id: 1, label: "Acre" }];
	professores = [
		{ id: 1, nome: "Ryu" },
		{ id: 2, nome: "Guile" },
		{ id: 3, nome: "Chun-li" },
		{ id: 31, nome: "M. Bison" },
		{ id: 4, nome: "Ken Masters" },
		{ id: 5, nome: "Sagat 1" },
		{ id: 6, nome: "Sagat 2" },
		{ id: 7, nome: "Sagat 3" },
		{ id: 8, nome: "Sagat 4" },
		{ id: 9, nome: "Sagat 5" },
		{ id: 10, nome: "Sagat 6" },
		{ id: 11, nome: "Sagat 7" },
		{ id: 12, nome: "Sagat 8" },
		{ id: 13, nome: "Sagat 9" },
	];
	rgMask = [
		/[0-9]/,
		/[0-9]/,
		".",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		".",
		/[0-9]/,
		/[0-9]/,
		/[0-9]/,
		"-",
		/[0-9]/,
	];
	fg = new FormGroup({
		nome: new FormControl(null, Validators.required),
		estado: new FormControl(null),
		professores: new FormControl(null, [Validators.required]),
		rg: new FormControl(null, [Validators.required, Validators.minLength(12)]),
		descricao: new FormControl(null),
	});
	resposeParser = () => [];

	salvarHandler() {
		for (const key in this.fg.controls) {
			const ownProperty = Object.prototype.hasOwnProperty.call(
				this.fg.controls,
				key
			);
			if (ownProperty) {
				const control = this.fg.controls[key];
				control.markAsTouched();
			}
		}
	}
}

const metadata = {
	imports: [
		FormsModule,
		BrowserAnimationsModule,
		HttpClientModule,
		ReactiveFormsModule,
		TextMaskModule,
	],
	declarations: [
		CatButtonComponent,
		CatSmoothScrollDirective,
		CatMultiSelectFilterComponent,
		CatFormMultiSelectFilterComponent,
		CatFormInputComponent,
		CatFormTextareaComponent,
		CatPersonAvatarComponent,
		CatFormSelectComponent,
	],
};

storiesOf("Forms|Grid", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Forms", () => {
		return {
			component: HostComponent,
			props: {},
		};
	});
