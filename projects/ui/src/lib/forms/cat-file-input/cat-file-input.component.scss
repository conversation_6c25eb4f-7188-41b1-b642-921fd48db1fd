@import "projects/ui/assets/import.scss";

input[type="file"].pacto-input-file {
	width: 0.1px;
	height: 0.1px;
	opacity: 0;
	overflow: hidden;
	position: absolute;
	z-index: -1;
}

.pacto-input-file-label {
	display: block;
	border: 2px dashed $gelo02;
	padding: 26px;
	cursor: pointer;
	text-align: center;

	.pct {
		font-size: 50px;

		&.upload {
			color: $verdinhoPri;
		}

		&.file {
			color: $azulimPri;
		}
	}

	.remove-action {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;

		.remove {
			color: $hellboyPri;
			font-size: 18px;
			position: relative;
			padding-left: 5px;
			top: 3px;
		}

		margin-top: 8px;
		margin-left: 4px;
	}

	.name-wrapper {
		display: flex;
		justify-content: center;

		.texto {
			max-width: 80%;
		}
	}

	.texto {
		margin-top: 8px;
		@extend .type-h6-bold;
		word-break: break-all;
		color: $pretoPri;
	}

	.descricao {
		@extend .type-p-small;
		color: $gelo04;
	}
}
