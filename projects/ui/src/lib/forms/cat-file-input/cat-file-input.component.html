<input
	#fileInput
	[accept]="formatos"
	[formControl]="controlFc"
	class="pacto-input-file"
	id="{{ id }}"
	name="{{ id + '-name' }}"
	type="file" />
<label class="pacto-input-file-label" for="{{ id }}">
	<ng-container *ngIf="!hasFile">
		<i class="pct upload pct-upload-cloud"></i>
		<div class="texto">Upload de arquivo</div>
		<div class="descricao">Adicione ou arraste um arquivo para cá</div>
		<div class="descricao">Formatos permitidos {{ formatos }}</div>
	</ng-container>

	<ng-container *ngIf="hasFile">
		<ng-container *ngIf="!urlImage || urlImage === ''">
			<i class="pct file pct-file"></i>
			<div class="name-wrapper">
				<div class="texto">
					{{ filename }}
				</div>
				<div class="remove-action">
					<i (click)="removeHandler($event)" class="pct remove pct-trash-2"></i>
				</div>
			</div>
		</ng-container>
		<ng-container *ngIf="urlImage">
			<img
				[alt]="imgAlt"
				[height]="imageHeight"
				[src]="urlImage"
				[width]="imageWidth" />
			<div class="name-wrapper">
				<div class="remove-action">
					<i (click)="removeHandler($event)" class="pct remove pct-trash-2"></i>
				</div>
			</div>
		</ng-container>
	</ng-container>
</label>
