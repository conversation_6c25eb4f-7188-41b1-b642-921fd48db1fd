@import "projects/ui/assets/import.scss";

.circle {
	border: 1px solid #a1a5aa;
	border-radius: 50%;

	&.checked {
		background: $azulimPri;
		border-color: $azulimPri;
	}

	&.disabled {
		cursor: not-allowed;
	}

	&:not(.checked) {
		&.disabled {
			border: 1px solid $cinza05;
			background: $cinza02;
			color: $cinza05;
		}
	}
}

.square {
	border-radius: 4px;
	border: 1px solid #a1a5aa;

	&.checked {
		background: $azulimPri;
		border-color: $azulimPri;
	}

	&.disabled {
		cursor: not-allowed;
	}

	&:not(.checked) {
		&.disabled {
			border: 1px solid $cinza05;
			background: $cinza02;
			color: $cinza05;
		}
	}
}

.content-div {
	color: $cinza05;
	display: flex;
	justify-content: center;
	align-items: center;

	&.checked {
		color: #fff;
	}
}

.option-label {
	cursor: pointer;
	height: auto;
	display: flex;
	align-items: center;
}

.left-label {
	margin-right: 10px;
}

.right-label {
	margin-left: 10px;
}
