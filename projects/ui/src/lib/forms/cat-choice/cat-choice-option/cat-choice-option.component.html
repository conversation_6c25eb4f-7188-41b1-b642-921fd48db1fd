<label (click)="onClick()" [id]="id">
	<div class="option-label">
		<div *ngIf="labelPosition === 'left'" class="left-label">{{ label }}</div>
		<div
			[ngClass]="{
				disabled: disabled,
				checked: checked,
				circle: format === 'circle',
				square: format === 'square'
			}"
			[ngStyle]="{
				width: radius,
				height: radius
			}"
			class="content-div">
			<ng-content></ng-content>
		</div>
		<div *ngIf="labelPosition === 'right'" class="right-label">{{ label }}</div>
	</div>
</label>
