import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	SimpleChanges,
} from "@angular/core";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-choice-option",
	templateUrl: "./cat-choice-option.component.html",
	styleUrls: ["./cat-choice-option.component.scss"],
})
export class CatChoiceOptionComponent implements OnInit {
	@Input() id;
	@Input() checked = false;
	@Input() label: string | undefined;
	@Input() value: any;
	disabled = false;
	radius: string;
	format: "square" | "circle";
	labelPosition: "left" | "right" = "right";
	clickEvent: EventEmitter<any> = new EventEmitter<any>();

	constructor(public elementRef: ElementRef) {}

	ngOnInit(): void {
		if (!this.id) {
			this.id = `choice-option-${uniqueId++}`;
		}
	}

	onClick() {
		if (!this.disabled) {
			this.checked = !this.checked;
			this.clickEvent.emit(this);
		}
	}
}
