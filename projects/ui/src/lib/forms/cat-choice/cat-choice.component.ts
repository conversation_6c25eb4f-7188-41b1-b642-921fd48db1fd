import {
	AfterContentInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	EventEmitter,
	Input,
	OnInit,
	Optional,
	Output,
	QueryList,
	ViewChild,
} from "@angular/core";
import { CatChoiceOptionComponent } from "./cat-choice-option/cat-choice-option.component";
import {
	Control<PERSON>ontainer,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";

export interface CatChoiceCheckedChanges {
	checked: boolean;
	value: any;
}

@Component({
	selector: "pacto-cat-choice",
	templateUrl: "./cat-choice.component.html",
	styleUrls: ["./cat-choice.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: CatChoiceComponent,
			multi: true,
		},
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatChoiceComponent
	implements OnInit, AfterContentInit, ControlValueAccessor
{
	@Input() formControlName = "";
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective | undefined;
	@Input()
	formControl: FormControl | undefined;
	@Input() multi = false;
	@Input() radius = "40px";
	@Input() format: "square" | "circle" = "square";
	@Input() labelPosition: "left" | "right" = "right";
	@Output() selectChanges: EventEmitter<CatChoiceCheckedChanges> =
		new EventEmitter<CatChoiceCheckedChanges>();

	@ContentChildren(CatChoiceOptionComponent, { descendants: true })
	options: QueryList<CatChoiceOptionComponent> =
		new QueryList<CatChoiceOptionComponent>();
	valuesSelected: Array<any> = new Array<any>();
	private disabled = false;

	get control(): any {
		return (
			this.formControl ||
			(this.controlContainer &&
				this.controlContainer.control &&
				this.controlContainer.control.get(this.formControlName))
		);
	}

	constructor(
		@Optional()
		private controlContainer: ControlContainer,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit(): void {}

	ngAfterContentInit() {
		const initialValues = this.control && this.multi ? this.control.value : [];
		this.disabled = this.control.disabled;
		this.options.forEach((item) => {
			if (this.multi) {
				if (this.control && this.control.value) {
					if (Array.isArray(initialValues)) {
						initialValues.forEach((v) => {
							if (item.value === v) {
								item.checked = true;
								this.selectOption(item);
							}
						});
					}
				}
			} else if (item.value === this.control.value) {
				item.checked = true;
				this.selectOption(item);
			}
			item.disabled = this.disabled;
			item.radius = this.radius;
			item.format = this.format;
			item.labelPosition = this.labelPosition;
			item.clickEvent.subscribe((option) => {
				this.selectOption(option);
				this.selectChanges.emit({
					checked: item.checked,
					value: option.value ? option.value : option.checked,
				});
			});
		});
	}

	selectOption(option: CatChoiceOptionComponent): void {
		if (option) {
			if (this.control) {
				if (this.multi) {
					let index;
					this.valuesSelected.find((v, i) => {
						if (v === option.value) {
							index = i;
							return v;
						}
					});
					if (option.checked) {
						if (option.value) {
							this.valuesSelected.push(option.value);
						} else {
							this.valuesSelected.push(option.checked);
						}
					} else {
						if (index !== undefined) {
							this.valuesSelected.splice(index, 1);
						}
					}
					this.control.setValue(this.valuesSelected);
				} else if (!this.multi && this.options) {
					if (option.checked) {
						if (option.value) {
							this.control.setValue(option.value);
						} else {
							this.control.setValue(option.checked);
						}
					} else {
						this.control.setValue(undefined);
					}
					this.options.forEach((opt) => {
						if (opt.value !== option.value && opt.checked) {
							opt.checked = false;
						}
					});
				}
			}
		}
	}

	registerOnTouched(fn: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.registerOnTouched(fn);
		}
	}

	registerOnChange(fn: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.registerOnChange(fn);
		}
	}

	writeValue(obj: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.writeValue(obj);
		}
	}

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
		}
	}
}
