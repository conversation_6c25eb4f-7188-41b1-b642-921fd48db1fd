import {
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-cat-form-input",
	templateUrl: "./cat-form-input.component.html",
	styleUrls: ["./cat-form-input.component.scss"],
})
export class CatFormInputComponent implements OnInit {
	@Input() id: string;
	@Input() label: string;
	@Input() placeholder = "";
	@Input() errorMsg = null;
	@Input() step = "";
	@Input() control: FormControl;
	@Input() checkTouched: boolean = true;
	@Input() selectOnFocus = false;
	@Input() enableClearInput = true;
	@Input() textMask;
	@Input() maxlength: string;
	@Input() type = "text";
	@Input() max = "";
	@Input() min = "";
	@Input() readonly = false;
	@Input() icon: string;
	@Input() iconToolTip = "";
	@Input() iconPosition: "before" | "after" = "after";
	@Input() autocomplete = "on";
	@Output() iconClick: EventEmitter<any> = new EventEmitter();
	@ViewChild("input", { static: true }) input: ElementRef;
	private disabledInternal = false;
	private navigationKeys = [
		"Backspace",
		"Delete",
		"Tab",
		"Escape",
		"Enter",
		"Home",
		"End",
		"ArrowLeft",
		"ArrowRight",
		"Clear",
		"Copy",
		"Paste",
	];
	controlErrorMsg: string = null;

	constructor() {}

	ngOnInit() {
		this.disabledInternal = this.control.disabled;
		this.control.registerOnDisabledChange((disabled) => {
			this.disabledInternal = disabled;
		});
		this.setUpId();
	}

	get disabled() {
		return this.disabledInternal;
	}

	get isInvalid() {
		const invalid =
			this.control &&
			(!this.checkTouched || this.control.touched) &&
			!this.control.valid;

		if (invalid && this.control.errors && this.control.errors.required) {
			this.controlErrorMsg = "Este campo é obrigatório";
		} else {
			this.controlErrorMsg = null;
		}

		return invalid;
	}

	get errorMessage(): string {
		return this.controlErrorMsg || this.errorMsg;
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get showClearInput() {
		return (
			this.enableClearInput &&
			this.control.value &&
			!this.showError &&
			!this.disabled
		);
	}

	focus() {
		this.input.nativeElement.focus();
	}

	clearHandler() {
		this.control.setValue("");
	}

	focusHandler($event) {
		if (this.selectOnFocus) {
			this.selectAll($event);
		}
	}

	clickHandler($event) {
		if (this.selectOnFocus) {
			this.selectAll($event);
		}
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `input-component-${rdn}`;
		}
	}

	private selectAll($event) {
		const target = $event.target;
		target.select();
		/* Mobile Safari */
		target.setSelectionRange(0, target.value.length);
	}

	validateValue(event, input) {
		if (input && input.hasAttribute("max")) {
			if (this.navigationKeys.indexOf(event.key) > -1) {
				return;
			}
			if (input.getAttribute("max") || input.getAttribute("max") !== "") {
				const trueValue = input.value + event.key;
				if (+trueValue > +input.getAttribute("max")) {
					event.preventDefault();
				}
			}
		}
	}

	iconClickEvent() {
		this.iconClick.emit({ icon: this.icon, formControl: this.control });
	}
}
