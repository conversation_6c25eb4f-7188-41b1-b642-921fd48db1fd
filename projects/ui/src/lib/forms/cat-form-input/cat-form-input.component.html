<div *ngIf="label" class="nome">
	<span>{{ label }}</span>
</div>
<ng-container *ngIf="!label">
	<ng-content select="label"></ng-content>
</ng-container>
<div [class]="'aux-wrapper icons-' + iconPosition">
	<input
		#input
		#input
		(click)="clickHandler($event)"
		(focus)="focusHandler($event)"
		(keypress)="validateValue($event, input)"
		[attr.disabled]="disabled ? true : null"
		[autocomplete]="autocomplete"
		[formControl]="control"
		[max]="max"
		[maxlength]="maxlength"
		[min]="min"
		[ngClass]="{ error: showError }"
		[placeholder]="placeholder"
		[readonly]="readonly"
		[step]="step"
		[textMask]="textMask ? textMask : { mask: false }"
		[title]="placeholder"
		[attr.data-cy]="id + '-input'"
		id="{{ id }}"
		type="{{ type }}" />
	<i [hidden]="!showError" class="pct pct-alert-triangle error-icon"></i>
	<i (click)="clearHandler()" [hidden]="!showClearInput" class="pct pct-x"></i>
	<i
		(click)="iconClickEvent()"
		*ngIf="icon && !showClearInput"
		class="pct pct-{{ icon }} icon-custom"
		title="{{ iconToolTip }}"></i>
</div>
<div *ngIf="showError" class="pct-error-msg">
	<span>{{ errorMessage }}</span>
</div>
