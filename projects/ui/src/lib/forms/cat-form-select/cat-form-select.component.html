<div *ngIf="label" class="pacto-label">
	<span>
		{{ label }}
		<span (click)="unlockHandler()" *ngIf="showKey" class="unlock-icon">
			<i class="pct pct-key"></i>
		</span>
	</span>
</div>
<ng-container *ngIf="!label">
	<ng-content select="label"></ng-content>
</ng-container>

<div class="aux-parent">
	<select
		[attr.disabled]="disabled ? true : null"
		[formControl]="control"
		[ngClass]="{ error: showError }"
		[attr.data-cy]="id"
		id="{{ id }}">
		<option
			*ngFor="let item of items; let index = index"
			id="{{ id }}-{{ index }}"
			value="{{ item[idKey] }}"
			[attr.data-cy]="id + '-' + index">
			<ng-container [ngSwitch]="isTemplate">
				<ng-container *ngSwitchCase="true">
					<ng-container *ngTemplateOutlet="item[labelKey]"></ng-container>
				</ng-container>

				<ng-container *ngSwitchCase="false">
					{{ item[labelKey] }}
				</ng-container>
			</ng-container>
		</option>
	</select>
	<img class="double-arrow" src="pacto-ui/images/double-arrow.svg" />
</div>

<div *ngIf="showError" class="pct-error-msg">
	<span>{{ errorMsg }}</span>
</div>
