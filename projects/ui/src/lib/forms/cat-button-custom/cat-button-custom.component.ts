import { Component, Input, OnInit } from "@angular/core";

export enum BUTTON_DETAIL {
	LARANJINHA = "LARANJINHA",
	AZULIM = "AZULIM",
}

@Component({
	selector: "pacto-cat-button-custom",
	templateUrl: "./cat-button-custom.component.html",
	styleUrls: ["./cat-button-custom.component.scss"],
})
export class CatButtonCustomComponent implements OnInit {
	@Input() type: BUTTON_DETAIL = BUTTON_DETAIL.AZULIM;

	constructor() {}

	get BUTTON_DETAIL() {
		return BUTTON_DETAIL;
	}

	ngOnInit() {}
}
