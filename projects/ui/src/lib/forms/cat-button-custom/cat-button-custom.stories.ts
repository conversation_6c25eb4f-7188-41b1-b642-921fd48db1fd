import { NO_ERRORS_SCHEMA, Component } from "@angular/core";
import { moduleMetadata, storiesOf } from "@storybook/angular";
import { CatButtonCustomComponent } from "projects/ui/src/public-api";

@Component({
	selector: "cat-buttom-custom",
	template: `
		<pacto-cat-button-custom [type]="'LARANJINHA'">
			<div button-body>
				<div>HUE</div>
				<div>123</div>
			</div>
		</pacto-cat-button-custom>
	`,
})
class HostComponent {
	items = [1, 2];
}

const metadata = {
	declarations: [CatButtonCustomComponent],
	schemas: [NO_ERRORS_SCHEMA],
};

storiesOf("Layout|Button Custom", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Custom", () => {
		return {
			component: HostComponent,
		};
	});
