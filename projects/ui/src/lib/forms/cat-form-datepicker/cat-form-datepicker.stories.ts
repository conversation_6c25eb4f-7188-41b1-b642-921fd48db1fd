import { Component, OnInit } from "@angular/core";
import {
	FormsModule,
	FormControl,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { MatNativeDateModule } from "@angular/material";

import { storiesOf, moduleMetadata } from "@storybook/angular";
import { TextMaskModule } from "angular2-text-mask";

import { CatFormDatepickerComponent } from "projects/ui/src/public-api";
import { CatButtonComponent } from "../cat-button/cat-button.component";
import { CatDatepickerComponent } from "../cat-datepicker/cat-datepicker.component";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-form-datepicker.stories.html",
	styleUrls: ["./cat-form-datepicker.stories.scss"],
})
class HostComponent implements OnInit {
	control: FormControl;

	ngOnInit() {
		this.control = new FormControl("", [Validators.required]);
	}
}

const metadata = {
	imports: [
		FormsModule,
		TextMaskModule,
		MatNativeDateModule,
		ReactiveFormsModule,
		MatDatepickerModule,
		BrowserAnimationsModule,
	],
	declarations: [
		CatButtonComponent,
		CatDatepickerComponent,
		CatFormDatepickerComponent,
	],
};

storiesOf("Forms|Datepicker", module)
	.addDecorator(moduleMetadata(metadata))
	.add("States", () => {
		return {
			component: HostComponent,
		};
	});
