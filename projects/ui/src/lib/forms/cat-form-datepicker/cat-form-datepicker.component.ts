import {
	Component,
	OnInit,
	Input,
	ViewChild,
	Output,
	EventEmitter,
} from "@angular/core";
import { FormControl } from "@angular/forms";

import { CatDatepickerComponent } from "../cat-datepicker/cat-datepicker.component";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-form-datepicker",
	templateUrl: "./cat-form-datepicker.component.html",
	styleUrls: ["./cat-form-datepicker.component.scss"],
})
export class CatFormDatepickerComponent implements OnInit {
	@Input() id: string = null;
	@Input() control: FormControl;
	@Input() onlyDay = false;
	@Input() dateFilter: (date: Date) => boolean;
	@Input() errorMsg = "";
	@Input() showKey = false;
	@Output() unlock: EventEmitter<any> = new EventEmitter();
	@Input() monthYearOnly: boolean;
	@Input() label;
	@Input() minDate;
	@ViewChild("datepicker", { static: true })
	datepicker: CatDatepickerComponent;

	constructor() {}

	ngOnInit() {
		if (!this.id) {
			this.id = `cat-datepicker-${uniqueId++}`;
		}
	}

	get showError() {
		if (this.datepicker) {
			return this.datepicker.showError;
		} else {
			return false;
		}
	}

	unlockHandler() {
		this.unlock.emit("unlock");
	}
}
