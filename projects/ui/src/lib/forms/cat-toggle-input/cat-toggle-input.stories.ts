import { Component, Input } from "@angular/core";
import { FormsModule, FormControl, ReactiveFormsModule } from "@angular/forms";
import { storiesOf, moduleMetadata } from "@storybook/angular";
import { TextMaskModule } from "angular2-text-mask";

import { CatToggleInputComponent } from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        height: 600px;
    ">
			<pacto-cat-toggle-input
				[label]="label"
				[control]="formControl"></pacto-cat-toggle-input>

			<div style="margin-top: 30px">Valor: {{ formControl.value }}</div>
		</div>
	`,
})
class HostComponent {
	@Input() label;
	@Input() formControl;
}

const metadata = {
	imports: [FormsModule, TextMaskModule, ReactiveFormsModule],
	declarations: [CatToggleInputComponent],
};

storiesOf("Forms|Toggle", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Toggle Input", () => {
		return {
			component: HostComponent,
			props: {
				label: "Enable User Module",
				control: new FormControl(),
			},
		};
	});
