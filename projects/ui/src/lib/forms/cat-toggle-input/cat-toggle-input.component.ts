import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	Input,
	ChangeDetectorRef,
} from "@angular/core";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-cat-toggle-input",
	templateUrl: "./cat-toggle-input.component.html",
	styleUrls: ["./cat-toggle-input.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatToggleInputComponent implements OnInit {
	@Input() label;
	@Input() id;
	@Input() control: FormControl;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.control.valueChanges.subscribe(() => {
			this.cd.detectChanges();
		});
	}

	clickHandler($event: MouseEvent) {
		$event.stopPropagation();
		const current = this.control.value;
		this.control.setValue(!current);
	}

	forceChange() {
		this.cd.detectChanges();
	}
}
