import {
	AfterViewInit,
	Component,
	forwardRef,
	Input,
	OnInit,
	Optional,
	ViewChild,
} from "@angular/core";
import {
	Control<PERSON>ontainer,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";

@Component({
	selector: "pacto-cat-input-color",
	templateUrl: "./cat-input-color.component.html",
	styleUrls: ["./cat-input-color.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useClass: forwardRef(() => CatInputColorComponent),
			multi: true,
		},
	],
})
export class CatInputColorComponent
	implements OnInit, AfterViewInit, ControlValueAccessor
{
	@Input() formControlName: string;
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective;
	@Input() formControl: FormControl;
	@ViewChild("labelElement", { static: false }) labelElement;
	showLabel = true;

	@Input() readonly: boolean;

	get control(): any {
		return (
			this.formControl ||
			(this.controlContainer &&
				this.controlContainer.control.get(this.formControlName))
		);
	}

	constructor(
		@Optional()
		private controlContainer: ControlContainer
	) {}

	ngOnInit() {
		if (this.control) {
			if (!this.control.value) {
				this.control.setValue("#1B4166");
			}
		}
	}

	ngAfterViewInit() {
		if (this.labelElement && this.labelElement.nativeElement) {
			this.showLabel =
				this.labelElement.nativeElement &&
				this.labelElement.nativeElement.children.length > 0;
		} else {
			this.showLabel = true;
		}
	}

	registerOnTouched(fn: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.registerOnTouched(fn);
		}
	}

	registerOnChange(fn: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.registerOnChange(fn);
		}
	}

	writeValue(obj: any): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.writeValue(obj);
		}
	}

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
		}
	}
}
