@import "projects/ui/assets/import";

.cat-input-color-wrapper {
	.cat-input-color-label {
		color: $cinza05;
		font-size: 16px;
		line-height: 21.82px;
		margin-bottom: 6px;
		font-weight: 600;

		label {
			margin-bottom: unset;
		}
	}

	.cat-input-color-field {
		border: 1px solid $cinza03;
		border-radius: 4px;
		display: flex;
		align-items: center;
		background-color: $branco;
		height: 40px;
		width: 133px;

		&.disabled {
			background-color: $cinza01;
			color: $cinza03;
			border: unset;
			cursor: not-allowed;
		}

		span {
			margin-left: 16px;
			color: $cinza05;
		}

		input[type="color"] {
			margin-left: auto;
			-webkit-appearance: none;
			border: none;
			width: 32px;
			height: 32px;
			background-color: unset;
			margin-right: 2px;

			&:disabled {
				background: inherit;
			}
		}

		input[type="color"]::-webkit-color-swatch-wrapper {
			padding: 0;
		}

		input[type="color"]::-webkit-color-swatch {
			border: none;
			border-radius: 4px;
		}
	}
}
