<div class="nome">
	<span>{{ label }}</span>
</div>
<div class="aux-wrapper asas">
	<input
		(focus)="onFocus($event)"
		[attr.disabled]="disabled ? true : null"
		[formControl]="control"
		[maxlength]="maxlength"
		[ngClass]="{ error: showError }"
		[options]="maskOptions"
		[placeholder]="placeholder"
		[readonly]="readonly"
		currencyMask
		id="{{ id }}" />

	<i [hidden]="!showError" class="pct pct-alert-triangle error-icon"></i>
	<i (click)="clearHandler()" [hidden]="!showClearInput" class="pct pct-x"></i>
</div>
<div class="pct-error-msg">
	<span *ngIf="showError">{{ errorMsg }}</span>
</div>
