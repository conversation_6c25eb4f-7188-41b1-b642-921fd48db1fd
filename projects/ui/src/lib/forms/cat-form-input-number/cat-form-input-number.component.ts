import {
	Component,
	ElementRef,
	Input,
	OnChanges,
	OnInit,
	Optional,
	SimpleChanges,
	ViewChild,
} from "@angular/core";
import {
	ControlC<PERSON>r,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { CurrencyMaskConfig } from "ngx-currency";

@Component({
	selector: "pacto-cat-form-input-number",
	templateUrl: "./cat-form-input-number.component.html",
	styleUrls: ["./cat-form-input-number.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useClass: CatFormInputNumberComponent,
			multi: true,
		},
	],
})
export class CatFormInputNumberComponent
	implements OnInit, ControlValueAccessor, OnChanges
{
	@Input() formControlName: string;
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective;
	@Input() formControl: FormControl;
	@Input() id: string;
	@Input() label: string;
	@Input() placeholder = "";
	@Input() decimal: boolean;
	@Input() errorMsg = null;
	@Input() step = "";
	@Input() selectOnFocus = false;
	@Input() enableClearInput = true;
	@Input() maxlength: string;
	@Input() prefix = "";
	@Input() max = "";
	@Input() min = "";
	@Input() decimalPrecision = 2;
	@Input() maskOptions: CurrencyMaskConfig;
	@Input() readonly = false;
	@ViewChild("input", { static: true }) input: ElementRef;
	private disabledInternal = false;
	private navigationKeys = [
		"Backspace",
		"Delete",
		"Tab",
		"Escape",
		"Enter",
		"Home",
		"End",
		"ArrowLeft",
		"ArrowRight",
		"Clear",
		"Copy",
		"Paste",
	];

	get control(): any {
		return (
			this.formControl ||
			this.controlContainer.control.get(this.formControlName)
		);
	}

	constructor(
		@Optional()
		private controlContainer: ControlContainer
	) {}

	ngOnInit() {
		this.disabledInternal = this.control.disabled;
		this.control.registerOnDisabledChange((disabled) => {
			this.disabledInternal = disabled;
		});
		this.setUpId();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (!changes.maskOptions) {
			this.decimalPrecision = this.decimal ? this.decimalPrecision : 0;
			this.maskOptions = {
				align: "right",
				allowNegative: false,
				allowZero: false,
				nullable: false,
				precision: this.decimalPrecision,
				suffix: "",
				decimal: ",",
				thousands: ".",
				prefix: "",
			};
			if (this.min) {
				this.maskOptions.min = +this.min;
			}

			if (this.max) {
				this.maskOptions.max = +this.max;
			}
		} else {
			if (changes.maskOptions.currentValue) {
				const maskOptions = changes.maskOptions.currentValue;
				if (maskOptions.suffix === undefined) {
					maskOptions.suffix = "";
				}
				this.maskOptions.suffix = maskOptions.suffix;
				if (maskOptions.decimal === undefined) {
					maskOptions.decimal = ",";
				}
				this.maskOptions.decimal = maskOptions.decimal;
				if (maskOptions.thousands === undefined) {
					maskOptions.thousands = ".";
				}
				this.maskOptions.thousands = maskOptions.thousands;
				if (maskOptions.prefix === undefined) {
					maskOptions.prefix = "";
				}
				this.maskOptions.prefix = maskOptions.prefix;
				if (maskOptions.align === undefined) {
					maskOptions.align = "right";
				}
				this.maskOptions.align = maskOptions.align;
				if (maskOptions.allowNegative === undefined) {
					maskOptions.allowNegative = false;
				}
				this.maskOptions.allowNegative = maskOptions.allowNegative;
				if (maskOptions.allowZero === undefined) {
					maskOptions.allowZero = false;
				}
				this.maskOptions.allowZero = maskOptions.allowZero;
				if (maskOptions.nullable === undefined) {
					maskOptions.nullable = false;
				}
				this.maskOptions.nullable = maskOptions.nullable || false;
			}
		}
	}

	get disabled() {
		return this.disabledInternal;
	}

	get isInvalid() {
		return this.control && this.control.touched && !this.control.valid;
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get showClearInput() {
		return (
			this.enableClearInput &&
			this.control.value &&
			!this.showError &&
			!this.disabled
		);
	}

	clearHandler() {
		this.control.setValue("");
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `input-component-${rdn}`;
		}
	}

	validateValue(event, input) {
		if (input && input.hasAttribute("max")) {
			if (this.navigationKeys.indexOf(event.key) > -1) {
				return;
			}
			if (input.getAttribute("max") || input.getAttribute("max") !== "") {
				const trueValue = input.value + event.key;
				if (+trueValue > +input.getAttribute("max")) {
					event.preventDefault();
				}
			}
		}
	}

	registerOnChange(fn: any): void {}

	registerOnTouched(fn: any): void {}

	writeValue(obj: any): void {}

	onFocus(event) {
		if (event.target.setSelectionRange) {
			setTimeout(() => {
				event.target.setSelectionRange(
					event.target.value.length * 2,
					event.target.value.length * 2
				);
			}, 1);
		}
	}
}
