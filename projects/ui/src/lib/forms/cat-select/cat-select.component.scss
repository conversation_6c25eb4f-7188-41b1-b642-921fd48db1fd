@import "projects/ui/assets/import.scss";

:host {
	display: block;
}

.small {
	select {
		line-height: 30px;
	}

	.double-arrow {
		bottom: 10px;
	}
}

.pacto-label {
	@extend .type-h6;
	color: $gelo04;
	line-height: 2em;
	padding-left: 3px;

	&.small {
		color: $gelo04;
		@extend .type-p-small;
		line-height: 22px;
	}
}

select {
	@extend .type-p-small-rounded;
	line-height: 40px;
	box-shadow: none;
	background-color: $branco;
	border-radius: 3px;
	height: 100%;
	width: 100%;
	padding-right: 35px;
	padding-left: 15px;
	border: 1px solid $gelo03;
	outline: none !important;

	&:not(:disabled) {
		cursor: pointer;
	}

	// Disable default styling on ff
	-moz-appearance: none;

	// Disable ugly ass outline on firefox
	&:-moz-focusring {
		color: transparent;
		text-shadow: 0 0 0 #000;
	}

	// Disable default styling on webkit browsers
	-webkit-appearance: none;

	// Disable default arrow on IE 11+
	&::-ms-expand {
		display: none;
	}

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.aux-parent {
	position: relative;
}

.double-arrow {
	position: absolute;
	right: 14px;
	bottom: 15px;
}

// IE 9 only
@media all and (min-width: 0 \0) and (min-resolution: 0.001dpcm) {
	.select {
		select {
			padding-right: 0;
		}

		&:after,
		&:before {
			display: none;
		}
	}
}
