import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { PactoSelectSize } from "../cat-form-select/cat-form-select.component";

@Component({
	selector: "pacto-cat-select",
	templateUrl: "./cat-select.component.html",
	styleUrls: ["./cat-select.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatSelectComponent implements OnInit {
	@Input() id: string;
	@Input() items: any[] = [];
	@Input() idKey = "id";
	@Input() labelKey = "label";
	@Input() label;
	@Input() control: FormControl;
	@Input() size: PactoSelectSize = PactoSelectSize.NORMAL;
	disabled = false;

	constructor() {}

	ngOnInit() {
		this.setUpId();

		if (!this.control) {
			this.control = new FormControl();
		}

		this.disabled = this.control.disabled;
		this.control.registerOnDisabledChange((disabled) => {
			this.disabled = disabled;
		});
	}

	get small() {
		return this.size === PactoSelectSize.SMALL;
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `pacto-select-${rdn}`;
		}
	}
}
