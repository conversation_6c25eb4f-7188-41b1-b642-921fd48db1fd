<div *ngIf="label" [ngClass]="{ small: small }" class="pacto-label">
	{{ label }}
</div>
<div [ngClass]="{ small: small }" class="aux-parent">
	<select
		[attr.disabled]="disabled ? true : null"
		[formControl]="control"
		id="{{ id }}">
		<option *ngIf="!control.value" [ngValue]="null" selected></option>
		<option
			*ngFor="let item of items; let index = index"
			id="{{ id }}-{{ index }}"
			value="{{ item[idKey] }}">
			{{ item[labelKey] }}
		</option>
	</select>
	<img class="double-arrow" src="pacto-ui/images/double-arrow.svg" />
</div>
