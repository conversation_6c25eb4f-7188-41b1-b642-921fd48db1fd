import { Component, OnInit } from "@angular/core";
import { FormsModule, FormControl, ReactiveFormsModule } from "@angular/forms";
import { storiesOf, moduleMetadata } from "@storybook/angular";
import { TextMaskModule } from "angular2-text-mask";

import { CatSelectComponent } from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-select.stories.html",
	styleUrls: ["./cat-select.stories.scss"],
})
class HostComponent implements OnInit {
	control = new FormControl(1);
	disabledControl = new FormControl(2);
	items = [
		{ id: 1, label: "Estado A" },
		{ id: 2, label: "Estado B" },
		{ id: 3, label: "Estado C" },
		{ id: 4, label: "Estado D" },
	];

	ngOnInit() {
		this.disabledControl.disable();
	}
}

const metadata = {
	imports: [FormsModule, ReactiveFormsModule, TextMaskModule],
	declarations: [CatSelectComponent],
};

storiesOf("Raw Input|Select", module)
	.addDecorator(moduleMetadata(metadata))
	.add("States", () => {
		return {
			component: HostComponent,
		};
	});
