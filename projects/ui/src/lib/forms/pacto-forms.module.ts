import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";

import { MatDatepickerModule } from "@angular/material/datepicker";
import { NgbDatepickerModule, NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { TextMaskModule } from "angular2-text-mask";

import { MatNativeDateModule } from "@angular/material";
import { NgxCurrencyModule } from "ngx-currency";
import { OnlyNumberDirective } from "../directives/only-number.directive";
import { CatButtonCustomComponent } from "./cat-button-custom/cat-button-custom.component";
import { CatButtonComponent } from "./cat-button/cat-button.component";
import { CatCheckboxComponent } from "./cat-checkbox/cat-checkbox.component";
import { CatChoiceOptionComponent } from "./cat-choice/cat-choice-option/cat-choice-option.component";
import { CatChoiceComponent } from "./cat-choice/cat-choice.component";
import { CatDatepickerComponent } from "./cat-datepicker/cat-datepicker.component";
import { CatFileInputComponent } from "./cat-file-input/cat-file-input.component";
import { CatFormDatepickerComponent } from "./cat-form-datepicker/cat-form-datepicker.component";
import { CatFormInputNumberComponent } from "./cat-form-input-number/cat-form-input-number.component";
import { CatFormInputComponent } from "./cat-form-input/cat-form-input.component";
import { CatFormSelectComponent } from "./cat-form-select/cat-form-select.component";
import { CatFormTextareaComponent } from "./cat-form-textarea/cat-form-textarea.component";
import { CatInputColorComponent } from "./cat-input-color/cat-input-color.component";
import { CatSelectComponent } from "./cat-select/cat-select.component";
import { CatSwitchComponent } from "./cat-switch/cat-switch.component";
import { CatTimeInputComponent } from "./cat-time-input/cat-time-input.component";
import { CatToggleInputComponent } from "./cat-toggle-input/cat-toggle-input.component";
import { DatepickerComponent } from "./datepicker/datepicker.component";

@NgModule({
	imports: [
		MatNativeDateModule,
		MatDatepickerModule,
		NgbDatepickerModule,
		CommonModule,
		FormsModule,
		TextMaskModule,
		ReactiveFormsModule,
		RouterModule.forChild([]),
		NgxCurrencyModule,
		NgbModule,
	],
	declarations: [
		CatSelectComponent,
		DatepickerComponent,
		CatTimeInputComponent,
		CatToggleInputComponent,
		CatFileInputComponent,
		CatFormTextareaComponent,
		CatButtonComponent,
		CatCheckboxComponent,
		CatFormSelectComponent,
		CatFormInputComponent,
		CatDatepickerComponent,
		CatFormDatepickerComponent,
		CatFormInputNumberComponent,
		OnlyNumberDirective,
		CatButtonCustomComponent,
		CatChoiceComponent,
		CatChoiceOptionComponent,
		CatInputColorComponent,
		CatSwitchComponent,
	],
	exports: [
		CatSelectComponent,
		DatepickerComponent,
		CatTimeInputComponent,
		CatToggleInputComponent,
		CatFileInputComponent,
		CatFormTextareaComponent,
		CatButtonComponent,
		CatCheckboxComponent,
		CatFormSelectComponent,
		CatFormInputComponent,
		CatDatepickerComponent,
		CatFormDatepickerComponent,
		CatFormInputNumberComponent,
		OnlyNumberDirective,
		CatButtonCustomComponent,
		CatChoiceComponent,
		CatChoiceOptionComponent,
		CatInputColorComponent,
		CatSwitchComponent,
	],
})
export class PactoFormsModule {}
