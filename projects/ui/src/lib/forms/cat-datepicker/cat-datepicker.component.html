<div *ngIf="label" class="nome">
	{{ label }}
	<span (click)="unlockHandler()" *ngIf="showKey" class="unlock-icon">
		<i class="pct pct-key"></i>
	</span>
</div>

<div class="icon-wrapper">
	<input
		(focus)="focusHandler($event)"
		[attr.disabled]="internalControl.disabled ? true : null"
		[formControl]="internalControl"
		[ngClass]="{ error: showError }"
		[placeholder]="onlyDay ? '' : monthYearOnly ? '__/____' : '__/__/____'"
		[textMask]="maskConfig"
		[attr.data-cy]="idInput"
		id="{{ idInput }}"
		type="text" />
	<i
		#toggle
		(click)="toggleDatepicker()"
		[ngClass]="{ disabled: internalControl.disabled }"
		class="pct pct-calendar"
		id="{{ id }}"></i>

	<mat-calendar
		#calendar
		(click)="calendarClickHandler($event)"
		(selectedChange)="calendarSelectHandler($event)"
		*ngIf="datepickerOpen"
		[dateFilter]="dateFilter"
		[minDate]="minDate"
		[ngClass]="{ 'hide-calendar': !datepickerOpen }"
		[selected]="formControl.value"
		[startAt]="calendarDay"></mat-calendar>

	<div *ngIf="showError" class="pct-error-msg">
		<span>{{ errorMessage }}</span>
	</div>
</div>
