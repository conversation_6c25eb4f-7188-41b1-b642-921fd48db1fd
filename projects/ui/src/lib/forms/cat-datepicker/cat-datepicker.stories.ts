import { FormsModule, ReactiveFormsModule, FormControl } from "@angular/forms";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { Component } from "@angular/core";

import { TextMaskModule } from "angular2-text-mask";
import { storiesOf, moduleMetadata } from "@storybook/angular";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatNativeDateModule } from "@angular/material";

import {
	CatDatepickerComponent,
	CatButtonComponent,
} from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-datepicker.stories.html",
	styleUrls: ["./cat-datepicker.stories.scss"],
})
class HostComponent {
	fc = new FormControl();

	toggleDisable() {
		if (this.fc.enabled) {
			this.fc.disable();
		} else {
			this.fc.enable();
		}
	}

	clear() {
		this.fc.reset();
	}

	setToday() {
		this.fc.setValue(new Date().valueOf());
	}
}

const metadata = {
	imports: [
		FormsModule,
		TextMaskModule,
		MatNativeDateModule,
		ReactiveFormsModule,
		MatDatepickerModule,
		BrowserAnimationsModule,
	],
	declarations: [CatButtonComponent, CatDatepickerComponent],
};

storiesOf("Raw Input|Datepicker", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Pre Loaded Options", () => {
		return {
			component: HostComponent,
			props: {},
		};
	});
