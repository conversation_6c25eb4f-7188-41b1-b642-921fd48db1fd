import {
	Component,
	OnInit,
	forwardRef,
	ChangeDetectionStrategy,
	HostListener,
	ElementRef,
	Input,
	ChangeDetectorRef,
	ViewChild,
	Output,
	EventEmitter,
} from "@angular/core";
import { BaseControlValueAccessor } from "../../BaseControlValueAccessor";
import { NG_VALUE_ACCESSOR, FormControl } from "@angular/forms";
import { debounceTime } from "rxjs/operators";

declare var moment;

@Component({
	selector: "pacto-cat-datepicker",
	templateUrl: "./cat-datepicker.component.html",
	styleUrls: ["./cat-datepicker.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => CatDatepickerComponent),
			multi: true,
		},
	],
})
export class CatDatepickerComponent
	extends BaseControlValueAccessor<number>
	implements OnInit
{
	@Input() idInput = "datepicker-input-label";
	@Input() id = "datepicker-input";
	@Input() formControl: FormControl;
	@Input() label;
	@Input() showKey = false;
	@Output() unlock: EventEmitter<any> = new EventEmitter();
	@Input() monthYearOnly: boolean;
	@Input() onlyDay = false;
	@Input() minDate;
	@Input() dateFilter: (date: Date) => boolean;
	@ViewChild("calendar", { static: false, read: ElementRef })
	calendar;
	@ViewChild("toggle", { static: false }) toggle;

	maskConfig = {
		guide: false,
		mask: [
			/[0-9]/,
			/[0-9]/,
			"/",
			/[0-9]/,
			/[0-9]/,
			"/",
			/[1-9]/,
			/[0-9]/,
			/[0-9]/,
			/[0-9]/,
		],
	};

	internalValue = null;
	internalControl: FormControl = new FormControl();
	datepickerOpen = false;
	calendarDay;

	constructor(private cd: ChangeDetectorRef, private ref: ElementRef) {
		super();
	}

	get errorMessage() {
		let error = "";
		if (this.formControl) {
			if (this.formControl.errors && this.formControl.errors.required) {
				error = "Este campo é obrigatório";
			} else if (
				this.formControl.errors instanceof Object &&
				this.formControl.errors["message"]
			) {
				error = this.formControl.errors["message"];
			} else if (Array.isArray(this.formControl.errors)) {
				this.formControl.errors.forEach((e) => {
					if (e instanceof Object && e["message"]) {
						error += e["message"];
					}
				});
			}

			return error;
		}
	}

	get showError() {
		const touched = this.internalControl.touched || this.formControl.touched;
		return touched && (!this.formControl.valid || this.value === false);
	}

	ngOnInit() {
		this.syncTextValueInput();
		this.updateEnabledStatus();
		this.formControl.registerOnDisabledChange(() => {
			this.updateEnabledStatus();
		});
		if (this.onlyDay) {
			this.maskConfig = {
				guide: false,
				mask: [/[0-9]/, /[0-9]/],
			};
		} else if (this.monthYearOnly) {
			this.maskConfig = {
				guide: false,
				mask: [/[0-1]/, /[0-9]/, "/", /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/],
			};
		}
	}

	private updateEnabledStatus() {
		const enabled = this.formControl.enabled;
		if (enabled) {
			this.internalControl.enable({ emitEvent: false });
		} else {
			this.internalControl.disable({ emitEvent: false });
		}
	}

	calendarSelectHandler(date: Date) {
		this.calendarDay = date;
		const momentValue = moment(date.valueOf());
		let dayFormat;
		if (this.onlyDay) {
			dayFormat = momentValue.format("DD");
		} else if (this.monthYearOnly) {
			dayFormat = momentValue.format("MM/YYYY");
		} else {
			dayFormat = momentValue.format("DD/MM/YYYY");
		}
		this.internalControl.setValue(dayFormat, { emitEvent: true });
		this.datepickerOpen = false;
	}

	get value() {
		return this.internalValue;
	}

	set value(value) {
		if (value === undefined) {
			value = "";
		}
		const momentValue = moment(value);
		const dayFormat = this.onlyDay
			? momentValue.format("DD")
			: this.monthYearOnly
			? momentValue.format("MM/YYYY")
			: momentValue.format("DD/MM/YYYY");
		this.internalControl.setValue(dayFormat, { emitEvent: false });
	}

	toggleDatepicker() {
		if (this.datepickerOpen) {
			this.datepickerOpen = false;
		} else if (this.internalControl.enabled) {
			this.datepickerOpen = true;
		}
	}

	focusHandler($event) {
		this.selectAll($event);
	}

	@HostListener("document:click", ["$event.target"])
	clickHandler(targetElement) {
		if (!this.calendar) {
			return;
		}
		const toggleClick = this.toggle.nativeElement === targetElement;
		const calendarClick = this.isDescendant(
			this.calendar.nativeElement,
			targetElement
		);
		if (!calendarClick && !toggleClick) {
			this.datepickerOpen = false;
		}
	}

	calendarClickHandler($event: MouseEvent) {
		$event.stopPropagation();
	}

	private selectAll($event) {
		const target = $event.target;
		target.select();
		/* Mobile Safari */
		target.setSelectionRange(0, target.value.length);
	}

	private syncTextValueInput() {
		this.internalControl.valueChanges
			.pipe(debounceTime(300))
			.subscribe((formatted) => {
				this.formControl.markAsTouched();
				if (formatted === undefined || formatted === null) {
					this.setInternalValue(null);
				} else {
					let momentDate;
					if (this.onlyDay) {
						momentDate = moment(formatted, "DD");
					} else if (this.monthYearOnly) {
						momentDate = moment(formatted, "MM/YYYY");
					} else {
						momentDate = moment(formatted, "DD/MM/YYYY");
					}
					const valid = momentDate.isValid();
					if (
						valid &&
						(formatted.length === 10 ||
							(this.onlyDay && formatted.length === 2) ||
							(this.monthYearOnly && formatted.length === 7))
					) {
						this.setInternalValue(momentDate.valueOf());
						this.formControl.setValue(momentDate.valueOf(), {
							emitEvent: false,
						});
						this.calendarDay = momentDate._d;
					} else {
						this.setInternalValue("");
						this.calendarDay = null;
						if (this.onlyDay && formatted.length === 2) {
							this.formControl.setValue("");
						}
					}
				}
			});
	}

	private setInternalValue(value) {
		if (this.internalValue !== value) {
			this.internalValue = value;
			this.onChange(value);
		}
	}

	private isDescendant(parent: Element, child: Element) {
		let node = child.parentNode;
		if (parent === child) {
			return true;
		} else {
			while (node !== null) {
				if (node === parent) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	unlockHandler() {
		this.unlock.emit("unlock");
	}
}
