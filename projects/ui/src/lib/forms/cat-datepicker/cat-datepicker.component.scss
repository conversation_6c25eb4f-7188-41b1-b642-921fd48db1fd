@import "projects/ui/assets/import.scss";

:host {
	display: block;
}

input.error:focus {
	box-shadow: 0 0 0 0.2rem rgba($hellboyPri, 0.5);
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

.nome {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}

.icon-wrapper {
	position: relative;

	i.pct {
		padding: 3px;
		position: absolute;
		cursor: pointer;
		right: 10px;
		top: 10px;

		&.disabled {
			cursor: not-allowed;
		}
	}
}

mat-calendar {
	width: 240px;
	display: block;
	position: absolute;
	z-index: 10;
	top: 42px;
	right: 0px;

	background-color: #ffffff;
	border: 1px solid #eee;
	border-radius: 4px;

	&.hide-calendar {
		visibility: hidden;
	}
}

::-webkit-input-placeholder {
	/* Chrome/Opera/Safari */
	color: $gelo05;
}

::-moz-placeholder {
	/* Firefox 19+ */
	color: $gelo05;
}

:-ms-input-placeholder {
	/* IE 10+ */
	color: $gelo05;
}

:-moz-placeholder {
	/* Firefox 18- */
	color: $gelo05;
}

.pct-error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	margin-top: 6px;
	margin-bottom: 2px;
	padding-left: 5px;
	min-height: 18px;
}

.unlock-icon {
	color: #47adfd;
	margin-left: 10px;
	cursor: pointer;
}
