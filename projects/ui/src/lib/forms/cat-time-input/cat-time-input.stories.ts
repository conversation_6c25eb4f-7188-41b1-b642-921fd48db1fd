import { Component, Input } from "@angular/core";
import { FormsModule, FormControl, ReactiveFormsModule } from "@angular/forms";
import { storiesOf, moduleMetadata } from "@storybook/angular";
import { TextMaskModule } from "angular2-text-mask";

import {
	CatTimeInputComponent,
	CatFormInputComponent,
} from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        height: 600px;
    ">
			<pacto-cat-time-input
				[label]="label"
				[control]="control"></pacto-cat-time-input>

			<div style="margin-top: 30px">Valor: {{ control.value }}</div>
		</div>
	`,
})
class HostComponent {
	@Input() label;
	@Input() control;
}

const metadata = {
	imports: [FormsModule, TextMaskModule, ReactiveFormsModule],
	declarations: [CatTimeInputComponent, CatFormInputComponent],
};

storiesOf("Forms|Time", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Time Input", () => {
		return {
			component: HostComponent,
			props: {
				label: "Start time (hh:mm)",
				control: new FormControl(),
			},
		};
	});
