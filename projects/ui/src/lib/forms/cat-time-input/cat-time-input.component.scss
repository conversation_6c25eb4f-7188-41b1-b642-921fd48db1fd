@import "projects/ui/assets/import.scss";

.nome {
	@extend .type-h6;
	color: $gelo04;
	line-height: 2em;
	margin-top: 6px;
	padding-left: 3px;
}

.pct-error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	margin-top: 6px;
	margin-bottom: 2px;
	padding-left: 5px;
	min-height: 18px;
}

.aux-wrapper {
	position: relative;
}

input {
	@extend .type-p-small-rounded;
	width: 100%;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;
	border: 1px solid $gelo03;
	border-radius: 3px;

	&:focus {
		border-color: $azulimPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri;
		background-color: $cinzaClaroPri;
	}
}

input:not(:disabled) + i.pct {
	cursor: pointer;
}

.loading {
	position: absolute;
	width: 28px;
	right: 12px;
	top: 8px;
}

i.pct {
	position: absolute;
	font-size: 18px;
	top: 12px;
	right: 14px;

	&.error-icon {
		color: $hellboyPri;
	}
}

.pct-form-input.has-error {
	input {
		border-color: $hellboyPri;
	}
}
