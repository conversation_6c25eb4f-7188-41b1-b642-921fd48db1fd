import {
	Component,
	OnInit,
	Input,
	ViewChild,
	ElementRef,
	ChangeDetectionStrategy,
} from "@angular/core";
import { FormControl } from "@angular/forms";

/**
 * Icones:
 *    > error - in case FormControl is invalid and touched
 *    > clear input - input is not empty and clear in enabled
 *    > loading - is enabled
 *
 *    Only one of them will show, with the following priority:
 *    1) Loading
 *    2) Error
 *    3) Clear
 *
 */
@Component({
	selector: "pacto-cat-time-input",
	templateUrl: "./cat-time-input.component.html",
	styleUrls: ["./cat-time-input.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatTimeInputComponent implements OnInit {
	@Input() id: string;
	@Input() label: string;
	@Input() placeholder = "00:00";
	@Input() errorMsg;
	@Input() control: FormControl;
	@ViewChild("input", { static: false }) input: ElementRef;

	constructor() {}

	ngOnInit() {
		this.setUpId();
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `input-component-${rdn}`;
		}
	}

	get mask() {
		return (value) => {
			const result = [/[0-2]/, null, ":", /[0-5]/, /[0-9]/];
			const firstD = parseInt(value[0], 10);

			/* digito 2 */
			if (!isNaN(firstD) && firstD === 2) {
				result[1] = /[0-3]/;
			} else {
				result[1] = /[0-9]/;
			}
			return result;
		};
	}
}
