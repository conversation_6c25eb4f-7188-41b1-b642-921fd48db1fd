import {
	Injectable,
	Inject,
	LOCALE_ID,
	InjectionToken,
	Optional,
} from "@angular/core";

import { Observable, of } from "rxjs";

import { TranslateLoader } from "@ngx-translate/core";
import { UiKitI18n, NavI18n, ModulesI18n } from "../i18n/i18n.model";
import { pt } from "../i18n/pt";
import { es } from "../i18n/es";
import { en } from "../i18n/en";

export interface MenuTranslation {
	pt?: MenuTranslationLang;
	es?: MenuTranslationLang;
	en?: MenuTranslationLang;
}

export interface MenuTranslationLang {
	modules?: ModulesI18n;
	navigation?: NavI18n;
}

export const MENU_I18N = new InjectionToken<MenuTranslation>(null);
export const MENU_I18N_PACTO_LAYOUT = new InjectionToken<MenuTranslation>(null);

@Injectable()
export class TranslationLoaderService implements TranslateLoader {
	getTranslation(lang: string): Observable<any> {
		let translations: UiKitI18n;

		if (this.locale === "es") {
			translations = es;
		} else if (this.locale === "en") {
			translations = en;
		} else {
			translations = pt;
		}

		if (this.navTranslation) {
			const menuI18n: MenuTranslationLang = this.navTranslation[this.locale];
			if (menuI18n && menuI18n.navigation) {
				Object.keys(menuI18n.navigation).forEach((key) => {
					Object.keys(menuI18n.navigation[key]).forEach((k) => {
						if (
							!menuI18n.navigation[key][k].nameInSidebar ||
							menuI18n.navigation[key][k].nameInSidebar === ""
						) {
							menuI18n.navigation[key][k].nameInSidebar =
								menuI18n.navigation[key][k].name;
						}
						if (
							!menuI18n.navigation[key][k].nameInExplorar ||
							menuI18n.navigation[key][k].nameInExplorar === ""
						) {
							menuI18n.navigation[key][k].nameInExplorar =
								menuI18n.navigation[key][k].name;
						}
					});
				});
				Object.assign(translations.menu.nav, menuI18n.navigation);
			}
			if (menuI18n && menuI18n.modules) {
				Object.assign(translations.menu.modules, menuI18n.modules);
			}
		}
		if (this.navTranslationPactoLayout) {
			const menuI18n: MenuTranslationLang =
				this.navTranslationPactoLayout[this.locale];
			if (menuI18n && menuI18n.navigation) {
				Object.keys(menuI18n.navigation).forEach((key) => {
					Object.keys(menuI18n.navigation[key]).forEach((k) => {
						if (
							!menuI18n.navigation[key][k].nameInSidebar ||
							menuI18n.navigation[key][k].nameInSidebar === ""
						) {
							menuI18n.navigation[key][k].nameInSidebar =
								menuI18n.navigation[key][k].name;
						}
						if (
							!menuI18n.navigation[key][k].nameInExplorar ||
							menuI18n.navigation[key][k].nameInExplorar === ""
						) {
							menuI18n.navigation[key][k].nameInExplorar =
								menuI18n.navigation[key][k].name;
						}
					});
				});
				Object.assign(translations.menu.nav, menuI18n.navigation);
			}
			if (menuI18n && menuI18n.modules) {
				Object.assign(translations.menu.modules, menuI18n.modules);
			}
		}
		return of(translations);
	}

	constructor(
		@Inject(LOCALE_ID) public locale: string,
		@Optional() @Inject(MENU_I18N) public navTranslation: MenuTranslation,
		@Optional()
		@Inject(MENU_I18N_PACTO_LAYOUT)
		public navTranslationPactoLayout: MenuTranslation
	) {}
}
