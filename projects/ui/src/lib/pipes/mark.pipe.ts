import { <PERSON><PERSON>, PipeTransform } from "@angular/core";
import { <PERSON>rowser<PERSON>odule, DomSanitizer } from "@angular/platform-browser";

@Pipe({
	name: "mark",
})
export class MarkPipe implements PipeTransform {
	constructor(private sanitizer: DomSanitizer) {}

	transform(value: any, args: any): any {
		if (!args || !value) {
			return value;
		}

		const valueNormalized = value
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(/\s+/g, " ");

		const argsNormalized = args
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(/\s+/g, " ");

		if (!valueNormalized.includes(argsNormalized)) {
			return value;
		}

		const pos = valueNormalized.indexOf(argsNormalized);
		const size = argsNormalized.length;
		const equivalence = value.substr(pos, size);
		const textReplaceMark = "<mark>" + equivalence + "</mark>";
		const replacedValue = value.replace(equivalence, textReplaceMark);
		return this.sanitizer.bypassSecurityTrustHtml(replacedValue);
	}
}
