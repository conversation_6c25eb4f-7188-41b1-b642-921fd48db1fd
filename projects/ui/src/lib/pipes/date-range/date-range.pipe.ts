import { Pipe, PipeTransform } from "@angular/core";
import { formatDate } from "@angular/common";

@Pipe({
	name: "dateRange",
})
export class DateRangePipe implements PipeTransform {
	transform(value: any, delimiter: string = "até"): any {
		if (!value) {
			return "";
		}

		const date = new Date(value);
		if (isNaN(date.getTime())) {
			return "";
		}

		const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
		const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

		const firstDayFormatted = formatDate(
			firstDayOfMonth,
			"dd/MM/yyyy",
			"pt-BR"
		);
		const lastDayFormatted = formatDate(lastDayOfMonth, "dd/MM/yyyy", "pt-BR");

		return `${firstDayFormatted} ${delimiter} ${lastDayFormatted}`;
	}
}
