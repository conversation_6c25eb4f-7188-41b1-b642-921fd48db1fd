import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
	name: "captalize",
})
export class CaptalizePipe implements PipeTransform {
	transform(value: string, firstLetter?: boolean): string {
		if (!value) {
			return value;
		}

		return this.captalize(value, firstLetter);
	}

	captalize(word: string, firstLetter) {
		if (firstLetter) {
			return word.charAt(0).toUpperCase() + word.substring(1).toLowerCase();
		}
		const splitStr = word.toLowerCase().split(" ");
		for (let i = 0; i < splitStr.length; i++) {
			splitStr[i] =
				splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
		}
		return splitStr.join(" ");
	}
}
