import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatColumnChartComponent } from "./cat-column-chart.component";
import { Component, Input } from "@angular/core";
import { PactoColor } from "ui-kit";

@Component({
	selector: "pacto-cat-column-bar-host",
	template: `
		<div class="host-wrapper">
			<pacto-cat-column-chart
				[yAxisTitle]="'Km quadrados desmatados 2019'"
				[xAxisLabels]="['Janeiro', 'Fevereiro', 'Março', 'Abril']"
				[series]="series"
				[colors]="colors"
				[distributed]="false"></pacto-cat-column-chart>
		</div>
	`,
})
export class ColumnChartHostComponent {
	@Input() series;
	@Input() colors;
}

const metadata = {
	declarations: [CatColumnChartComponent],
};

storiesOf("Charts|Column Chart", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Única columa", () => {
		return {
			component: ColumnChartHostComponent,
			props: {
				series: [
					{
						name: "Queimadas",
						data: [123, 4567, 334, 123],
					},
				],
				colors: [
					PactoColor.PEQUIZAO_PRI,
					PactoColor.AZULIM_PRI,
					PactoColor.VERDINHO_PRI,
					PactoColor.HELLBOY_PRI,
				],
			},
		};
	})
	.add("Mútiplas columa", () => {
		return {
			component: ColumnChartHostComponent,
			props: {
				series: [
					{ name: "Queimadas", data: [123, 456, 334, 123] },
					{ name: "CO2", data: [12, 47, 3334, 1223] },
				],
				colors: [
					PactoColor.PEQUIZAO_PRI,
					PactoColor.AZULIM_PRI,
					PactoColor.VERDINHO_PRI,
					PactoColor.HELLBOY_PRI,
				],
			},
		};
	});
