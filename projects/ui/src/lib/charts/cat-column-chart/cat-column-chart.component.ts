import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	Output,
} from "@angular/core";
import { PactoColor, PactoColorScheme } from "../../pacto-color.model";
import "apexcharts";
import { ColumnChartSet } from "../cat-line-chart/cat-line-chart.component";
import { ApexOptions } from "apexcharts";

@Component({
	selector: "pacto-cat-column-chart",
	template: "<div id={{id}}></div>",
	styleUrls: ["./cat-column-chart.component.scss"],
})
export class CatColumnChartComponent implements OnInit, OnDestroy, OnChanges {
	@Input() id: string;
	@Input() xAxisLabels: string[] = [];
	@Input() series: ColumnChartSet[] = [];
	@Input() yAxisTitle = "";
	@Input() height = 350;
	@Input() legend = true;
	@Input() animation = true;
	@Input() colors: PactoColor[];
	@Input() distributed = false;
	@Input() dashboard = false;
	@Input() options: Partial<ApexOptions>;
	@Output() clickEvent: EventEmitter<any> = new EventEmitter();

	private chartInstance;

	@Input() tooltipFormatter = (v) => v;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private getOptions() {
		if (this.options) {
			return this.options;
		}
		const options = {
			chart: {
				height: null,
				type: "bar",
				fontFamily: "'Nunito Sans', sans-serif",
				fontSize: "19px",
				toolbar: { show: false },
				animations: {
					enabled: this.animation,
				},
				events: {
					click: (event, chartContext, opts) => {
						this.clickEvent.emit({ event, chartContext, opts });
					},
				},
			},
			colors: null,
			plotOptions: {
				bar: {
					horizontal: false,
					columnWidth: `${25 * this.series.length}%`,
					endingShape: "rounded",
					distributed: this.distributed,
				},
			},
			stroke: {
				show: true,
				width: 2,
				colors: ["transparent"],
			},
			dataLabels: { enabled: false },
			series: this.series,
			xaxis: {},
			yaxis: {
				labels: {
					style: {
						fontSize: "14px",
						color: "#a6aab1",
					},
				},
				title: {
					text: this.yAxisTitle,
				},
			},
			fill: { opacity: 1 },
			tooltip: {
				y: { formatter: this.tooltipFormatter(null) },
			},
			legend: {
				show: this.legend,
				markers: {
					width: 12,
					height: 12,
					radius: 7,
				},
			},
		};
		if (!this.dashboard) {
			options.xaxis = {
				categories: this.xAxisLabels,
				labels: {
					style: {
						colors: new Array(this.xAxisLabels.length).fill("#a6aab1"),
						fontSize: "12px",
					},
				},
			};
		}
		if (this.colors) {
			options.colors = this.colors;
		} else {
			options.colors = PactoColorScheme.getDefaultChartScheme();
		}
		options.chart.height = this.height;
		return options;
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `column-chart-${rdn}`;
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}
}
