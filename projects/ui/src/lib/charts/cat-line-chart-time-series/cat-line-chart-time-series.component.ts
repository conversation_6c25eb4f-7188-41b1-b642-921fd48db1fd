import {
	Component,
	OnInit,
	OnDestroy,
	Input,
	ChangeDetectorRef,
	OnChanges,
	ChangeDetectionStrategy,
} from "@angular/core";
import { PactoColor, PactoColorScheme } from "../../pacto-color.model";
import "apexcharts";

// @ts-ignore
const pt = require("apexcharts/dist/locales/pt-br.json");

export interface LineChartTimeSet {
	name: string;
	data: [number, number][];
}

@Component({
	selector: "pacto-cat-line-chart-time-series",
	template: "<div id={{id}}></div>",
	styleUrls: ["./cat-line-chart-time-series.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatLineChartTimeSeriesComponent
	implements OnInit, OnDestroy, OnChanges
{
	@Input() id: string;
	@Input() series: LineChartTimeSet[] = [];
	@Input() yAxisTitle = "";
	@Input() height;
	@Input() colors: PactoColor[];
	private chartInstance;
	private destroyed = false;

	@Input() tooltipFormatter = (v) => v;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		this.destroyed = true;
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private getOptions() {
		const options: any = {
			chart: {
				locales: [pt],
				defaultLocale: "pt-br",
				type: "line",
				zoom: {
					type: "x",
					enabled: false,
				},
				fontFamily: "'Nunito Sans', sans-serif",
				fontSize: "19px",
				toolbar: { show: false },
			},
			markers: {
				size: 5,
			},
			colors: null,
			plotOptions: {},
			dataLabels: { enabled: false },
			series: this.series,
			xaxis: {
				type: "datetime",
				labels: {
					style: {
						colors: new Array(90).fill("#a6aab1"),
						fontSize: "12px",
					},
				},
			},
			yaxis: {
				labels: {
					style: {
						fontSize: "14px",
						color: "#a6aab1",
					},
				},
				title: {
					text: this.yAxisTitle,
				},
			},
			fill: { opacity: 1 },
			tooltip: {
				y: { formatter: this.tooltipFormatter },
			},
		};

		if (this.height) {
			options.chart.height = this.height;
		}
		if (this.colors) {
			options.colors = this.colors;
		} else {
			options.colors = PactoColorScheme.getDefaultChartScheme();
		}

		return options;
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `column-chart-${rdn}`;
		}
	}

	private buildChart() {
		if (this.destroyed) {
			return null;
		}

		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}
}
