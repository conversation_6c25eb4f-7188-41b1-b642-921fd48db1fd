import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatLineChartTimeSeriesComponent } from "./cat-line-chart-time-series.component";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-time-series-host",
	template: `
		<div class="host-wrapper" style="width: 600px;">
			<pacto-cat-line-chart-time-series
				[series]="series"></pacto-cat-line-chart-time-series>
		</div>
	`,
})
export class TimeSeriesHostComponent {
	@Input() series;
}

const metadata = {
	declarations: [CatLineChartTimeSeriesComponent],
};

const randomData = [];
let today = new Date().valueOf();
for (let i = 0; i <= 50; i++) {
	const value = Math.random() * 100;
	randomData.push([today, Math.trunc(value)]);
	today = today + 86400000;
}

storiesOf("Charts|Time Series Chart", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Única columa", () => {
		return {
			component: TimeSeriesHostComponent,
			props: {
				series: [
					{
						name: "Queimadas",
						data: randomData,
					},
				],
			},
		};
	});
