import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { PactoColor } from "../../pacto-color.model";

@Component({
	selector: "pacto-cat-chart-legend",
	templateUrl: "./cat-chart-legend.component.html",
	styleUrls: ["./cat-chart-legend.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatChartLegendComponent implements OnInit {
	@Input() labels: string[] = [];
	@Input() colors: PactoColor[] = [];
	@Input() vertical = false;

	constructor() {}

	ngOnInit() {}
}
