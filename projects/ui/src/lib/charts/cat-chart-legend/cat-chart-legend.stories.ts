import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatChartLegendComponent } from "./cat-chart-legend.component";

const metadata = {
	declarations: [CatChartLegendComponent],
};

storiesOf("Charts|Chart Legend", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Padrão", () => {
		return {
			template: `
            <pacto-cat-chart-legend
                style="display: block; width: 400px; margin: 40px;"
                [labels] = "[ 'Elefantes', 'Girafas', 'Plantas' ]"
                [colors] = " [ '#ECF0F1', '#F0B924', '#7be542' ] "
            ></pacto-cat-chart-legend>
        `,
		};
	});
