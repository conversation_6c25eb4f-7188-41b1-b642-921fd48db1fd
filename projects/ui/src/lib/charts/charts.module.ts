import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { NgModule } from "@angular/core";

import { CatAngularGaugeChartComponent } from "./cat-angular-gauge-chart/cat-angular-gauge-chart.component";
import { CatPieChartComponent } from "./cat-pie-chart/cat-pie-chart.component";
import { CatLineChartTimeSeriesComponent } from "./cat-line-chart-time-series/cat-line-chart-time-series.component";
import { CatLineChartComponent } from "./cat-line-chart/cat-line-chart.component";
import { CatColumnChartComponent } from "./cat-column-chart/cat-column-chart.component";
import { CatChartRadialBarComponent } from "./cat-chart-radial-bar/cat-chart-radial-bar.component";
import { CatChartLegendComponent } from "./cat-chart-legend/cat-chart-legend.component";

@NgModule({
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		RouterModule.forChild([]),
	],
	declarations: [
		CatChartLegendComponent,
		CatChartRadialBarComponent,
		CatColumnChartComponent,
		CatLineChartComponent,
		CatLineChartTimeSeriesComponent,
		CatAngularGaugeChartComponent,
		CatPieChartComponent,
	],
	exports: [
		CatChartLegendComponent,
		CatChartRadialBarComponent,
		CatColumnChartComponent,
		CatLineChartComponent,
		CatLineChartTimeSeriesComponent,
		CatAngularGaugeChartComponent,
		CatPieChartComponent,
	],
})
export class PactoChartsModule {}
