import {
	Component,
	OnInit,
	On<PERSON><PERSON>roy,
	Input,
	ChangeDetectorRef,
	OnChanges,
} from "@angular/core";
import "apexcharts";

export interface ColumnChartSet {
	name: string;
	data: number[];
}

@Component({
	selector: "pacto-cat-line-chart",
	template: "<div id={{id}}></div>",
	styleUrls: ["./cat-line-chart.component.scss"],
})
export class CatLineChartComponent implements OnInit, OnDestroy, OnChanges {
	@Input() id: string;
	@Input() xAxisLabels: string[] = [];
	@Input() series: ColumnChartSet[] = [];
	@Input() yAxisTitle = "";
	@Input() height = 350;
	@Input() legend = true;
	@Input() colors = ["#ff2970", "#1998fc", "#f2c54b", "#db2c3d", "#7be542"];
	@Input() yAxisMin = 0;
	private chartInstance;

	@Input() tooltipFormatter = (v) => v;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private getOptions() {
		const options = {
			chart: {
				height: null,
				type: "line",
				fontFamily: "'Nunito Sans', sans-serif",
				fontSize: "19px",
				toolbar: { show: false },
			},
			markers: {
				size: 6,
			},
			colors: this.colors,
			plotOptions: {
				bar: {
					horizontal: false,
					columnWidth: `${25 * this.series.length}%`,
					endingShape: "rounded",
				},
			},
			dataLabels: { enabled: false },
			series: this.series,
			xaxis: {
				categories: this.xAxisLabels,
				labels: {
					style: {
						colors: new Array(this.xAxisLabels.length).fill("#a6aab1"),
						fontSize: "12px",
					},
				},
			},
			legend: {
				show: this.legend,
			},
			yaxis: {
				min: this.yAxisMin,
				labels: {
					style: {
						fontSize: "14px",
						color: "#a6aab1",
					},
				},
				title: {
					text: this.yAxisTitle,
				},
			},
			fill: { opacity: 1 },
			tooltip: {
				y: { formatter: this.tooltipFormatter(null) },
			},
		};
		options.chart.height = this.height;
		return options;
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `column-chart-${rdn}`;
		}
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}
}
