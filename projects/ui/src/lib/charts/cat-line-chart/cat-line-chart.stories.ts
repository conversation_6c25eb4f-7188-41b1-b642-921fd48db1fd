import { CatLineChartComponent } from "./cat-line-chart.component";

export default { title: "Charts|Line Chart" };

export const noData = () => ({
	component: CatLineChartComponent,
	props: {
		series: [],
	},
});

export const withData = () => ({
	component: CatLineChartComponent,
	props: {
		series: [{ name: "<PERSON><PERSON>", data: [12, 15, 18, 21, 15, 12] }],
	},
});

export const multiLine = () => ({
	component: CatLineChartComponent,
	props: {
		series: [
			{ name: "Temperat<PERSON>", data: [12, 15, 18, 21, 15, 12] },
			{ name: "<PERSON><PERSON><PERSON><PERSON>", data: [95, 12, 45, 25, 65, 55] },
		],
	},
});
