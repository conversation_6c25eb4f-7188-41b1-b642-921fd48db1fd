import {
	Component,
	OnInit,
	On<PERSON><PERSON>roy,
	Input,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnChanges,
} from "@angular/core";
import { PactoColor } from "../../pacto-color.model";
import "apexcharts";

@Component({
	selector: "pacto-cat-chart-radial-bar",
	template: "<div id={{id}}></div>",
	styleUrls: ["./cat-chart-radial-bar.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatChartRadialBarComponent
	implements OnInit, OnChanges, OnDestroy
{
	@Input() id: string;
	@Input() label;
	@Input() value;

	private chartInstance;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `chart-radial-bar-${rdn}`;
		}
	}

	private getOptions() {
		const options = {
			chart: {
				type: "radialBar",
			},
			plotOptions: {
				radialBar: {
					hollow: {
						size: "50%",
					},
					dataLabels: {
						value: {
							fontSize: "40px",
							fontFamily: "'Nunito Sans', sans-serif",
							color: PactoColor.PRETO_PRI,
							offsetY: -10,
							formatter: (val) => {
								return val + "%";
							},
						},
						name: {
							fontSize: "10px",
							fontFamily: "'Nunito Sans', sans-serif",
							color: PactoColor.PRETO_PRI,
							offsetY: 30,
						},
					},
					track: {
						background: "#dadada",
					},
				},
			},
			fill: {
				colors: ["#1998FC"],
				type: "solid",
			},
			series: [this.value ? this.value : 0],
			labels: [this.label ? this.label : ""],
		};
		return options;
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}
}
