import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatChartRadialBarComponent } from "./cat-chart-radial-bar.component";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-radial-bar-host",
	template: `
		<div class="host-wrapper" style="width: 400px;">
			<pacto-cat-chart-radial-bar
				[id]="id"
				[label]="label"
				[value]="value"></pacto-cat-chart-radial-bar>
		</div>
	`,
})
export class RadialBarHostComponent {
	@Input() id;
	@Input() label;
	@Input() value;
}

const metadata = {
	declarations: [CatChartRadialBarComponent],
};

storiesOf("Charts|Chart Radial Bar", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Vazio", () => {
		return {
			component: RadialBarHostComponent,
			props: {
				id: "radial-chart-id",
				label: "Idade Media dos Alunos",
				value: null,
			},
		};
	})
	.add("Padrão", () => {
		return {
			component: RadialBarHostComponent,
			props: {
				id: "radial-chart-id",
				label: "Idade Media dos Alunos",
				value: 34,
			},
		};
	});
