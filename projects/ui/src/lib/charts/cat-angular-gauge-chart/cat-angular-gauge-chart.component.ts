import {
	Component,
	On<PERSON>ni<PERSON>,
	On<PERSON><PERSON>roy,
	Input,
	ChangeDetectorRef,
	ChangeDetectionStrategy,
	OnChanges,
} from "@angular/core";
import "apexcharts";

@Component({
	selector: "pacto-cat-angular-gauge-chart",
	styleUrls: ["./cat-angular-gauge-chart.component.scss"],
	template: "<div id={{id}}></div>",
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatAngularGaugeChartComponent
	implements OnInit, OnDestroy, OnChanges
{
	@Input() id: string;
	@Input() label;
	@Input() value;

	private chartInstance;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	ngOnChanges() {
		setTimeout(() => {
			this.buildChart();
		});
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `angular-gauge-chart-${rdn}`;
		}
	}

	private getOptions() {
		const options = {
			chart: {
				height: 240,
				type: "radialBar",
			},
			plotOptions: {
				radialBar: {
					startAngle: -135,
					endAngle: 135,
					dataLabels: {
						value: {
							offsetY: -5,
							fontSize: "28px",
							fontFamily: "'Nunito Sans', sans-serif",
							color: "#2C343B",
							formatter: (val) => {
								return val + "%";
							},
						},
						name: {
							fontSize: "14px",
							fontFamily: "'Nunito Sans', sans-serif",
							color: "#a6aab1",
							offsetY: 70,
						},
					},
					track: {
						background: "#dadada",
					},
				},
			},
			fill: {
				colors: ["#1998FC"],
				type: "solid",
			},
			stroke: {
				dashArray: 5,
				width: 1,
			},
			series: [this.value ? this.value : 0],
			labels: [this.label ? this.label : ""],
		};
		return options;
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}
}
