import {
	Component,
	OnInit,
	OnDestroy,
	Input,
	ChangeDetectorRef,
	OnChanges,
	ViewEncapsulation,
	Output,
	EventEmitter,
	SimpleChanges,
} from "@angular/core";
import { PactoColor, PactoColorScheme } from "../../pacto-color.model";
import "apexcharts";

export interface PieChartSet {
	name: string;
	data: number;
}

@Component({
	selector: "pacto-cat-pie-chart",
	template:
		'<div class="pacto-pie-chart-wrapper" [style.width]="width" id={{id}}></div>',
	styleUrls: ["./cat-pie-chart.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatPieChartComponent implements OnInit, OnDestroy, OnChanges {
	private chartInstance;
	/**
	 * Emits the arguments received by the function passed in to 'dataPointSelection'.
	 *
	 * Fires when user clicks on a datapoint (bar/column/marker/bubble/donut-slice).
	 * The third argument, in addition to the config object, also includes additional
	 * information like which dataPointIndex was selected of which series.
	 */
	@Output() dataPointSelected: EventEmitter<{
		event: MouseEvent;
		chartContext: any;
		config: any;
	}> = new EventEmitter();
	@Input() id: string;
	@Input() height;
	@Input() centerValue: string;
	@Input() labelCenter: string;
	@Input() series: PieChartSet[] = [];
	@Input() simbol: string;
	@Input() showLegend = true;
	@Input() showValue = true;
	@Input() showPercentage = true;
	@Input() tooltipEnabled = true;
	@Input() showTotal = true;
	@Input() colors: PactoColor[];
	@Input() width = "270px";
	@Input() states: ApexStates;
	@Input() labelFormatter = (v) => v;
	// variavel criada para definir as label da borda.
	@Input() labelFormatterBorder = (v) => v;

	constructor(private cd: ChangeDetectorRef) {
		this.series = [];
	}

	ngOnInit() {
		this.fillOutId();
		this.cd.detectChanges();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.series && !Array.isArray(changes.series.currentValue)) {
			this.series = [];
		}
		setTimeout(() => {
			this.buildChart();
		});
	}

	ngOnDestroy() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}
	}

	private fillOutId() {
		const rdn = Math.trunc(Math.random() * 10000);
		if (!this.id) {
			this.id = `column-chart-${rdn}`;
		}
	}

	private getOptions() {
		const dataPointFunction = this.dataPointSelected;
		const options: any = {
			chart: {
				type: "donut",
				events: {
					dataPointSelection(event, chartContext, config) {
						return dataPointFunction.emit(config.dataPointIndex);
					},
				},
			},
			plotOptions: {
				pie: {
					expandOnClick: false,
					donut: {
						size: "60%",
						labels: {
							show: true,
							name: {
								show: true,
								fontSize: "12px",
								fontFamily: "'Nunito Sans', sans-serif",
								color: "#2c343b",
								offsetY: 15,
							},
							value: {
								show: this.showValue,
								fontSize: "26px",
								fontFamily: "'Nunito Sans', sans-serif",
								color: "#2c343b",
								offsetY: -20,
								formatter: (value) => {
									if (this.showPercentage) {
										const total = this.valuesTotal(null);
										const percentage = this.truncateDecimais(
											100 * (value / total),
											2
										);
										return `${percentage}%`;
									}
									return value;
								},
							},
							total: {
								show: this.showTotal,
								fontSize: "12px",
								label: this.labelCenter ? this.labelCenter : "Total",
								color: "#000000",
								formatter: () => {
									const total = this.valuesTotal(this.centerValue);
									return this.labelFormatter(total);
								},
							},
						},
					},
				},
			},
			dataLabels: {
				enabled: false,
			},
			colors: null,
			labels: null,
			series: null,
			tooltip: {
				enabled: this.tooltipEnabled,
				fillSeriesColor: false,
				y: { formatter: this.labelFormatterBorder },
				style: {
					fontSize: "10px",
					fontFamily: "'Nunito Sans', sans-serif",
				},
			},
			legend: this.getLegendOptions(),
		};

		if (this.colors) {
			options.colors = this.colors;
		} else {
			options.colors = PactoColorScheme.getDefaultChartScheme();
		}
		if (this.height) {
			options.chart.height = this.height;
		}
		if (this.states) {
			options.states = this.states;
		}
		options.series = this.getValues();
		options.labels = this.getLabels();
		return options;
	}

	private truncateDecimais(numero, digitos) {
		const multiplier = Math.pow(10, digitos);
		const adjustedNum = numero * multiplier;
		const truncateNum = Math[adjustedNum < 0 ? "ceil" : "floor"](adjustedNum);

		return truncateNum / multiplier;
	}

	private valuesTotal(centerValue) {
		if (!centerValue && centerValue !== 0) {
			const values = this.getValues();
			let total = 0;
			values.forEach((value) => {
				total = total + value;
			});
			return this.truncateDecimais(total, 2);
		} else {
			return centerValue;
		}
	}

	private getValues(): number[] {
		if (!Array.isArray(this.series) || !this.series.forEach) {
			console.warn(
				'Esperava-se que "série" fosse um array, mas recebeu:',
				this.series
			);
			return [];
		}
		const result = this.series.map((set) => set.data);
		return result;
	}

	private getLegendOptions() {
		return {
			show: this.showLegend,
			labels: {
				colors: ["#2c343b"],
			},
			itemMargin: {
				horizontal: 0,
			},
			markers: {
				width: 12,
				height: 12,
				radius: 7,
			},
			fontFamily: "'Nunito Sans', sans-serif",
			fontSize: "10px",
			position: "bottom",
		};
	}

	private getLabels(): string[] {
		const result = [];
		this.series.forEach((set) => {
			result.push(set.name);
		});
		return result;
	}

	private buildChart() {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${this.id}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}
}
