import { CatPieChartComponent } from "./cat-pie-chart.component";

export default { title: "Charts|Pie Chart" };

export const withData = () => ({
	component: Cat<PERSON>ieChartComponent,
	props: {
		series: [
			{ name: "<PERSON><PERSON><PERSON>", data: 12 },
			{ name: "<PERSON><PERSON><PERSON><PERSON>", data: 4 },
			{ name: "<PERSON><PERSON>", data: 5 },
		],
	},
});

export const labelCustomizado = () => ({
	component: CatPieChartComponent,
	props: {
		labelCenter: "Massa Corporal",
		series: [
			{ name: "A", data: 1 },
			{ name: "B", data: 2 },
			{ name: "B", data: 4 },
			{ name: "D", data: 5 },
		],
		labelFormatter: (v) => {
			return `${v}kg`;
		},
	},
});
