import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-cat-editor",
	templateUrl: "./cat-editor.component.html",
	styleUrls: ["./cat-editor.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class CatEditorComponent implements OnInit {
	@Input() control: FormControl;
	@Input() placeHolder;
	@Input() id;
	@Input() styles: any;
	invalid = false;
	@Output() save: EventEmitter<any> = new EventEmitter();
	@ViewChild("inputHandle", { static: true }) editor;

	constructor() {}

	ngOnInit(): void {
		this.control.valueChanges.subscribe(() => {
			this.attemptSave();
		});
	}

	private attemptSave() {
		if (this.control.valid) {
			this.invalid = false;
			this.save.emit(this.control.value);
		} else {
			this.editor.markAsTouched();
			this.invalid = true;
		}
	}
}
