quill-editor {
	.ql-toolbar.ql-snow + .ql-container.ql-snow {
		border-top: 1px solid #ccc;
	}

	.ql-snow.ql-toolbar button,
	.ql-snow .ql-toolbar button {
		border: none;
		cursor: pointer;
		display: inline-block;
		float: left;
		height: 24px;
		padding: 3px 20px;
		width: 28px;
	}

	.ql-toolbar.ql-snow .ql-formats {
		margin-right: 10px;
		margin-top: 15px;
		margin-left: -30px;
	}

	padding-top: 18px;
	display: flex;
	flex-direction: column;

	.ql-editor.ql-blank::before {
		font-size: 14px;
		font-style: normal;
	}

	.ql-container .ql-editor {
		min-height: 120px;
		max-height: 250px;
	}

	.ql-snow .ql-picker.ql-expanded .ql-picker-options {
		display: block;
		margin-top: -1px;
		top: 100%;
		z-index: 1;
		top: auto;
		bottom: 100%;
	}

	.ql-container {
		border-radius: 6px 6px 0 0;
		border-color: black;
	}

	.ql-toolbar {
		order: 2;
		border-radius: 0 0 6px 6px;
	}

	.ql-toolbar.ql-snow {
		border: 0px solid #ccc;
		box-sizing: border-box;
		font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
		padding: 8px;
	}

	.ql-snow .ql-stroke {
		fill: none;
		stroke: #7e7c7c5c;
		stroke-linecap: round;
		stroke-linejoin: round;
		stroke-width: 2;
	}

	.ql-snow .ql-color-picker,
	.ql-snow .ql-icon-picker {
		width: 28px;
		margin-left: 15px;
	}
}
