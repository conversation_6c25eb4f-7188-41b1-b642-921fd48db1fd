import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-corpo-frontal-grupo-muscular",
	templateUrl: "./corpo-frontal-grupo-muscular.component.html",
	styleUrls: ["./corpo-frontal-grupo-muscular.component.scss"],
})
export class CorpoFrontalGrupoMuscularComponent implements OnInit {
	// GRUPO MUSCULAR SIMPLIFICADO FRONTAL

	@Input() listaAtiva: Array<any>;
	@Input() listaCompletaGrupoMuscular: Array<any>;
	@Input() podeSelecionarGrupoMuscular: boolean = false;
	@Output() grupoMuscularSelecionado: EventEmitter<any> =
		new EventEmitter<any>();

	tooltipAtivo: {
		nome: string;
		id: string;
		lado: "frontal" | "traseiro";
	};

	constructor(private snotifyService: SnotifyService) {}

	ngOnInit() {}

	pintarMusculo(nomeGrupo) {
		const normalizeText = (text: string) =>
			text
				.normalize("NFD")
				.replace(/[\u0300-\u036f]/g, "")
				.toUpperCase();

		const normalizedNomeGrupo = normalizeText(nomeGrupo);

		const grupoSelecionado = this.listaAtiva.some((obj) =>
			normalizedNomeGrupo.includes(normalizeText(obj.nome))
		);

		if (grupoSelecionado) {
			return "#366AE2";
		} else {
			return "#BCBEC2";
		}
	}

	tooltipParte(nomeGrupo) {
		this.tooltipAtivo = {
			nome: nomeGrupo.toUpperCase(),
			id: nomeGrupo.toUpperCase(),
			lado: "frontal",
		};
	}

	selecionarMusculo(nomeGrupo: string) {
		const normalizeText = (text: string) =>
			text
				.normalize("NFD")
				.replace(/[\u0300-\u036f]/g, "")
				.toUpperCase();

		const normalizedNomeGrupo = normalizeText(nomeGrupo);

		if (this.podeSelecionarGrupoMuscular) {
			let index = this.listaAtiva.findIndex((obj) =>
				normalizedNomeGrupo.includes(normalizeText(obj.nome))
			);
			if (index !== -1) {
				// Remover item existente da lista
				this.listaAtiva.splice(index, 1);

				// Segunda verificação para casos em que 2 grupos musculares estejam na mesma região
				index = this.listaAtiva.findIndex((obj) =>
					normalizedNomeGrupo.includes(normalizeText(obj.nome))
				);
				if (index !== -1) {
					// Remover item existente da lista
					this.listaAtiva.splice(index, 1);
				}
			} else {
				// Utilizar sempre o primeiro, pois é a referência no desenho do corpo humano
				const listaNomeGrupo = nomeGrupo.split(";");
				if (listaNomeGrupo[0]) {
					const normalizedListaNomeGrupo0 = normalizeText(listaNomeGrupo[0]);
					const grupoEncontrado = this.listaCompletaGrupoMuscular.find(
						(grupo) =>
							normalizedListaNomeGrupo0.includes(normalizeText(grupo.nome))
					);
					if (grupoEncontrado) {
						this.listaAtiva.push({
							id: grupoEncontrado.id,
							nome: grupoEncontrado.nome,
						});
					} else {
						this.snotifyService.warning(
							"O grupo muscular selecionado não foi encontrado na listagem de grupos musculares, " +
								"solicite ao suporte a revisão."
						);
					}
				}
			}
			this.onGrupoMuscularSelecionado();
		}
	}

	onGrupoMuscularSelecionado() {
		this.grupoMuscularSelecionado.emit(true);
	}
}
