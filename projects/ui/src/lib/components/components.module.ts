import { DragDropModule } from "@angular/cdk/drag-drop";
import { OverlayModule } from "@angular/cdk/overlay";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import {
	NgbActiveModal,
	NgbDatepickerModule,
	NgbDropdownModule,
	NgbModule,
	NgbPaginationModule,
	NgbTooltipModule,
} from "@ng-bootstrap/ng-bootstrap";
import { TranslateModule } from "@ngx-translate/core";
import { TextMaskModule } from "angular2-text-mask";
import { Ng2SearchPipeModule } from "ng2-search-filter";
import { NgxCurrencyModule } from "ngx-currency";
import { QuillModule } from "ngx-quill";
import { PactoFormsModule } from "../forms/pacto-forms-api";
import { LayoutModule } from "../layout/layout-api";
import {
	CatCameraCaptureDirective,
	CatCameraComponent,
} from "./cat-camera/cat-camera.component";
import { CatTableEditableComponent } from "./cat-editable-table/cat-table-editable.component";
import { CatTableEditableRendererComponent } from "./cat-editable-table/cat-table-renderer/cat-table-editable-renderer.component";
import { ColumnEditableColumnComponent } from "./cat-editable-table/cat-table-renderer/column-editable-column/column-editable-column.component";
import { ColumnCheckboxComponent } from "./cat-editable-table/cat-table-renderer/column-types/column-checkbox/column-checkbox.component";
import { ColumnDatepickerComponent } from "./cat-editable-table/cat-table-renderer/column-types/column-datepicker/column-datepicker.component";
import { ColumnInputDecimalComponent } from "./cat-editable-table/cat-table-renderer/column-types/column-input-decimal/column-input-decimal.component";
import { ColumnInputComponent } from "./cat-editable-table/cat-table-renderer/column-types/column-input/column-input.component";
import {
	ColumnSelectComponent,
	ColumnSelectOptionLabel,
} from "./cat-editable-table/cat-table-renderer/column-types/column-select/column-select.component";
import { CatTableRowComponent } from "./cat-editable-table/cat-table-row/cat-table-row.component";
import { CatEditorComponent } from "./cat-editor/cat-editor.component";
import { CatFormMultiSelectFilterComponent } from "./cat-form-multi-select-filter/cat-form-multi-select-filter.component";
import { CatFormSelectFilterComponent } from "./cat-form-select-filter/cat-form-select-filter.component";
import { CatIconComponent } from "./cat-icon/cat-icon.component";
import { CatLabelValueIconComponent } from "./cat-label-value-icon/cat-label-value-icon.component";
import { CatMultiSelectFilterComponent } from "./cat-multi-select-filter/cat-multi-select-filter.component";
import {
	CatNotificationComponent,
	CatPactoNotificationMessageDirective,
	CatPactoNotificationTitleDirective,
} from "./cat-notification/cat-notification.component";
import { CatPersonAvatarRatingComponent } from "./cat-person-avatar-rating/cat-person-avatar-rating.component";
import { CatPersonAvatarComponent } from "./cat-person-avatar/cat-person-avatar.component";
import {
	CatSelectFilterComponent,
	SelectItemDirective,
} from "./cat-select-filter/cat-select-filter.component";
import { CatStepSimpleComponent } from "./cat-stepper-simple/components/cat-step-simple/cat-step-simple.component";
import { CatStepperSimpleComponent } from "./cat-stepper-simple/components/cat-stepper-simple/cat-stepper-simple.component";
import { ButtonContainerDirective } from "./cat-stepper-simple/directives/button-container.directive";
import { CatStepSimpleNextDirective } from "./cat-stepper-simple/directives/cat-step-simple-next/cat-step-simple-next.directive";
import { CatStepSimplePreviousDirective } from "./cat-stepper-simple/directives/cat-step-simple-previous/cat-step-simple-previous.directive";
import { CatTabsTransparentComponent } from "./cat-tabs-transparent/cat-tabs-transparent.component";
import { TabTransparentDirective } from "./cat-tabs-transparent/tab.directive";
import { CatTabsVerticalComponent } from "./cat-tabs-vertical/cat-tabs-vertical.component";
import { TabVerticalDirective } from "./cat-tabs-vertical/tab-vertical.directive";
import { CatTabsComponent } from "./cat-tabs/cat-tabs.component";
import { TabDirective } from "./cat-tabs/tab.directive";
import {
	CustomDataGridFilterOptionComponent,
	PactoDataGridFilterOptionBody,
} from "./custom-data-grid-filter/custom-data-grid-filter-option/custom-data-grid-filter-option.component";
import {
	CustomDataGridFilterComponent,
	PactoDataGridFilterActionsContent,
	PactoDataGridFilterToggleContent,
} from "./custom-data-grid-filter/custom-data-grid-filter.component";
import { DetalhamentoModalComponent } from "./detalhamento-modal/detalhamento-modal.component";
import { FiltroCheckboxComponent } from "./filtro-checkbox/filtro-checkbox.component";
import { IframeLegadoComponent } from "./iframe-legado/iframe-legado.component";
import {
	PctImageCarousel,
	PctImageCarouselItem,
} from "./image-carousel/image-carousel";
import { PctImageCarouselButton } from "./image-carousel/image-carousel-button";
import { LoaderComponent } from "./loader/loader.component";
import { LogComponent } from "./log/log.component";
import { TabelaLogComponent } from "./log/tabela-log/tabela-log.component";
import { CatRadioGroupComponent } from "./radio-button/cat-radio-group.component";
import { CatRadioComponent } from "./radio-button/cat-radio/cat-radio.component";
import { DataGridFilterComponent } from "./relatorio/data-grid-filter/data-grid-filter.component";
import { FilterComponent } from "./relatorio/filter/filter.component";
import { RelatorioRendererComponent } from "./relatorio/relatorio-renderer/relatorio-renderer.component";
import { RelatorioComponent } from "./relatorio/relatorio.component";
import {
	ReportDetailComponent,
	ReportDetailItemComponent,
	ReportDetailItemTitleComponent,
	ReportDetailItemValueComponent,
	ReportDetailRowComponent,
	ReportDetailTitleComponent,
} from "./report-detail/report-detail.component";
import { ShareButtonComponent } from "./share-button/share-button.component";
import { CatTooltipTriggerDirective } from "./tooltip/cat-tooltip-trigger.directive";
import { CatTooltipComponent } from "./tooltip/cat-tooltip.component";
import { LabelXinglingDirective } from "./traducoes-xingling/label-xingling.directive";
import { TemplateXinglingDirective } from "./traducoes-xingling/template-xingling.directive";
import { TraducoesXinglingComponent } from "./traducoes-xingling/traducoes-xingling.component";
import { LogDefaultComponent } from "./log-default/log-default.component";
import { ModalLogDefaultComponent } from "./log-default/modal-log/modal-log.component";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
import { PhoneMaskPipe } from "./share-button/phoneMaskPipe";
import { Ds3Module } from "../ds3/ds3.module";
import { Ds3SideFilterComponent } from "./relatorio/ds3-side-filter/ds3-side-filter.component";
import { MatBadgeModule } from "@angular/material";
import { Ds3ButtonModule } from "../ds3/ds3-button/ds3-button.module";
import { CorpoFrontalGrupoMuscularComponent } from "./corpo-frontal-grupo-muscular/corpo-frontal-grupo-muscular.component";
import { CorpoPosteriorGrupoMuscularComponent } from "./corpo-posterior-grupo-muscular/corpo-posterior-grupo-muscular.component";
import { CatTolltipModule } from "../uiv2/cat-tolltip/cat-tolltip.module";

@NgModule({
	imports: [
		NgbPaginationModule,
		NgbDropdownModule,
		NgbDatepickerModule,
		NgbTooltipModule,
		TranslateModule.forChild({ extend: true }),
		Ng2SearchPipeModule,
		PactoFormsModule,
		LayoutModule,
		CommonModule,
		FormsModule,
		TextMaskModule,
		ReactiveFormsModule,
		RouterModule.forChild([]),
		NgxCurrencyModule,
		OverlayModule,
		DragDropModule,
		QuillModule.forRoot(),
		NgbModule,
		InfiniteScrollModule,
		Ds3Module,
		MatBadgeModule,
		Ds3ButtonModule,
		InfiniteScrollModule,
		CatTolltipModule,
		Ds3Module,
	],
	declarations: [
		PhoneMaskPipe,
		RelatorioComponent,
		CatFormMultiSelectFilterComponent,
		CatFormSelectFilterComponent,
		CatMultiSelectFilterComponent,
		DataGridFilterComponent,
		RelatorioRendererComponent,
		TabTransparentDirective,
		CatTabsTransparentComponent,
		TabDirective,
		CatTabsComponent,
		CatSelectFilterComponent,
		CatLabelValueIconComponent,
		CatPersonAvatarComponent,
		SelectItemDirective,
		CatPersonAvatarRatingComponent,
		TraducoesXinglingComponent,
		LabelXinglingDirective,
		TemplateXinglingDirective,
		ShareButtonComponent,
		CatTableEditableComponent,
		CatTableEditableRendererComponent,
		ColumnEditableColumnComponent,
		ColumnInputComponent,
		ColumnSelectComponent,
		ColumnSelectOptionLabel,
		CatTableRowComponent,
		ColumnDatepickerComponent,
		ColumnCheckboxComponent,
		CatTabsVerticalComponent,
		TabVerticalDirective,
		ColumnInputDecimalComponent,
		CatStepperSimpleComponent,
		CatStepSimpleComponent,
		CatStepSimpleNextDirective,
		CatStepSimplePreviousDirective,
		ButtonContainerDirective,
		FilterComponent,
		DetalhamentoModalComponent,
		LoaderComponent,
		LogComponent,
		TabelaLogComponent,
		FiltroCheckboxComponent,
		CatIconComponent,
		ReportDetailComponent,
		ReportDetailTitleComponent,
		ReportDetailItemComponent,
		ReportDetailItemTitleComponent,
		ReportDetailItemValueComponent,
		ReportDetailRowComponent,
		CatRadioGroupComponent,
		CatRadioComponent,
		CatEditorComponent,
		CatTooltipComponent,
		CatTooltipTriggerDirective,
		CatNotificationComponent,
		CatPactoNotificationMessageDirective,
		CatPactoNotificationTitleDirective,
		PctImageCarousel,
		PctImageCarouselItem,
		PctImageCarouselButton,
		CatCameraComponent,
		CatCameraCaptureDirective,
		IframeLegadoComponent,
		CustomDataGridFilterComponent,
		CustomDataGridFilterOptionComponent,
		PactoDataGridFilterToggleContent,
		PactoDataGridFilterActionsContent,
		PactoDataGridFilterOptionBody,
		LogDefaultComponent,
		ModalLogDefaultComponent,
		Ds3SideFilterComponent,
		CorpoFrontalGrupoMuscularComponent,
		CorpoPosteriorGrupoMuscularComponent,
		ModalLogDefaultComponent,
	],
	exports: [
		RelatorioComponent,
		CatFormSelectFilterComponent,
		CatFormMultiSelectFilterComponent,
		CatMultiSelectFilterComponent,
		DataGridFilterComponent,
		TabTransparentDirective,
		CatTabsTransparentComponent,
		TabDirective,
		CatTabsComponent,
		CatSelectFilterComponent,
		CatLabelValueIconComponent,
		CatPersonAvatarComponent,
		CatPersonAvatarRatingComponent,
		SelectItemDirective,
		TraducoesXinglingComponent,
		LabelXinglingDirective,
		TemplateXinglingDirective,
		CatTableEditableComponent,
		CatTableEditableRendererComponent,
		CatTabsVerticalComponent,
		CatStepperSimpleComponent,
		CatStepSimpleComponent,
		CatStepSimpleNextDirective,
		CatStepSimplePreviousDirective,
		ButtonContainerDirective,
		TabVerticalDirective,
		DetalhamentoModalComponent,
		FilterComponent,
		LoaderComponent,
		LogComponent,
		TabelaLogComponent,
		FiltroCheckboxComponent,
		CatIconComponent,
		ReportDetailComponent,
		ReportDetailTitleComponent,
		ReportDetailItemComponent,
		ReportDetailItemTitleComponent,
		ReportDetailItemValueComponent,
		ReportDetailRowComponent,
		CatRadioGroupComponent,
		CatRadioComponent,
		ShareButtonComponent,
		CatEditorComponent,
		CatTooltipComponent,
		CatTooltipTriggerDirective,
		CatNotificationComponent,
		CatPactoNotificationMessageDirective,
		CatPactoNotificationTitleDirective,
		PctImageCarousel,
		PctImageCarouselItem,
		CatCameraComponent,
		CatCameraCaptureDirective,
		CustomDataGridFilterComponent,
		CustomDataGridFilterOptionComponent,
		PactoDataGridFilterToggleContent,
		IframeLegadoComponent,
		CorpoFrontalGrupoMuscularComponent,
		CorpoPosteriorGrupoMuscularComponent,
		LogDefaultComponent,
		ModalLogDefaultComponent,
		Ds3SideFilterComponent,
	],
	entryComponents: [
		RelatorioComponent,
		Ds3SideFilterComponent,
		DetalhamentoModalComponent,
		FilterComponent,
		CorpoFrontalGrupoMuscularComponent,
		CorpoPosteriorGrupoMuscularComponent,
		ModalLogDefaultComponent,
	],
})
export class ComponentsModule {}
