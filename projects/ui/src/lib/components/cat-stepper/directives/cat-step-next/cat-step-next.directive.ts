import {
	Directive,
	ElementRef,
	HostListener,
	Renderer2,
	TemplateRef,
} from "@angular/core";
import { CatStepperComponent } from "../../components/cat-stepper/cat-stepper.component";

@Directive({
	selector: "[pactoCatStepNext]",
})
export class CatStepNextDirective {
	constructor(public stepper: CatStepperComponent, private el: ElementRef) {}

	@HostListener("click")
	public nextStep() {
		if (!this.isDisabled()) {
			this.stepper.next();
		}
	}

	isDisabled(): boolean {
		const ngReflectDisabled = this.el.nativeElement.getAttribute(
			"ng-reflect-disabled"
		);
		if (ngReflectDisabled === "true" || ngReflectDisabled === "") {
			return true;
		}
		const disableAttr = this.el.nativeElement.getAttribute("disabled");
		if (disableAttr === "true" || disableAttr === "") {
			return true;
		}
		const btnsElements = this.el.nativeElement.getElementsByTagName("button");
		if (
			btnsElements !== null &&
			btnsElements !== undefined &&
			btnsElements.length > 0
		) {
			for (const btnEl of btnsElements) {
				if (
					btnEl.getAttribute("disabled") === "true" ||
					btnEl.getAttribute("disabled") === ""
				) {
					return true;
				}
			}
		}
		return false;
	}
}
