<div
	[class.step-current]="selected === index"
	[class.step-done]="completed"
	class="pct-step-header">
	<i *ngIf="displayCompleted()" class="pct pct-check-circle done"></i>
	<i *ngIf="displayEditing()" class="pct pct-edit-3 done"></i>
	<div *ngIf="displayIndex()" class="step-indicator">{{ index + 1 }}</div>
	<div *ngIf="templateLabel()" class="step-label">
		<div class="step-label-text">
			<ng-template
				[ngTemplateOutlet]="templateLabel()!.templateRef"></ng-template>
		</div>
	</div>
	<i *ngIf="!isLast" class="pct pct-chevron-right next-icon"></i>
</div>
