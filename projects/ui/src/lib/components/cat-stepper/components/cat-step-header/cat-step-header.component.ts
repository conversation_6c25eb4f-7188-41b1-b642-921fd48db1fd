import { Component, Input, OnInit } from "@angular/core";
import { CatStepLabelDirective } from "../../directives/cat-step-label/cat-step-label.directive";

@Component({
	selector: "pacto-cat-step-header",
	templateUrl: "./cat-step-header.component.html",
	styleUrls: ["./cat-step-header.component.scss"],
})
export class CatStepHeaderComponent {
	@Input() label: CatStepLabelDirective | string;
	@Input() isLast: boolean;
	@Input() index: number;
	@Input() selected: number;
	@Input() completed: boolean;
	@Input() interacted: boolean;

	templateLabel(): CatStepLabelDirective | null {
		return this.label instanceof CatStepLabelDirective ? this.label : null;
	}

	stringLabel(): string | null {
		return this.label instanceof CatStepLabelDirective ? null : this.label;
	}

	displayCompleted() {
		if (this.completed == null) {
			return false;
		}
		return this.completed && !(this.selected === this.index) && this.interacted;
	}

	displayEditing() {
		return this.interacted && this.selected === this.index;
	}

	displayIndex() {
		return !this.displayCompleted() && !this.displayEditing();
	}
}
