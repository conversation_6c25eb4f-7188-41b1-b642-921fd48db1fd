@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ui-kit.scss";

.pct-step-header {
	display: flex;
	align-items: center;
	color: $cinza03;
	cursor: pointer;

	.step-indicator {
		border-radius: 50%;
		border: 1px solid $cinza03;
		display: flex;
		width: 24px;
		height: 24px;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.done {
		font-size: 24px;
	}

	.next-icon {
		display: inline-block;
		text-align: center;
		width: 20px;
		height: 20px;
		margin-right: 8px;
		margin-left: 8px;
		font-size: 20px;
	}

	.step-label {
		margin-left: 9px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		flex: 1;

		.step-label-text {
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}

	&.step-done {
		color: $azulimPri;

		.step-indicator {
			border-color: $azulimPri;
		}

		.next-icon {
			color: $preto05;
		}
	}

	&.step-current {
		color: $preto05;

		.step-indicator {
			border-color: $azulimPri;
		}

		.next-icon {
			color: $cinza03;
		}
	}
}
