import {
	AfterViewChecked,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChild,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { CatStepLabelDirective } from "../../directives/cat-step-label/cat-step-label.directive";

@Component({
	selector: "pacto-cat-step",
	templateUrl: "./cat-step.component.html",
	styleUrls: ["./cat-step.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatStepComponent implements AfterViewChecked {
	@ContentChild(CatStepLabelDirective, { static: false })
	stepLabel: CatStepLabelDirective;
	@ViewChild(TemplateRef, { static: false }) content: TemplateRef<any>;
	@Input() stepControl: FormControl | FormGroup;
	interacted = false;

	constructor(private cd: ChangeDetectorRef) {}

	ngAfterViewChecked(): void {
		this.cd.detectChanges();
	}

	get done() {
		return this.getDone() && this.interacted;
	}

	getDone(): boolean {
		if (this.stepControl) {
			return this.getFormValidity();
		}
		return this.stepLabel.done;
	}

	getFormValidity(): boolean {
		return this.stepControl ? this.stepControl.valid : false;
	}

	markAsInteracted(): void {
		this.interacted = true;
	}
}
