import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	QueryList,
} from "@angular/core";
import { CatStepComponent } from "../cat-step/cat-step.component";

@Component({
	selector: "pacto-cat-stepper",
	templateUrl: "./cat-stepper.component.html",
	styleUrls: ["./cat-stepper.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatStepperComponent implements AfterViewInit {
	@ContentChildren(CatStepComponent, { descendants: true })
	_steps: QueryList<CatStepComponent>;

	selectedIndex = 0;

	get template() {
		if (!isNaN(this.selectedIndex)) {
			const tabs = this._steps;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				return this._steps.toArray()[this.selectedIndex].content;
			}
		} else {
			return null;
		}
	}

	constructor(private cd: ChangeDetectorRef) {}

	ngAfterViewInit(): void {
		this.cd.detectChanges();
	}

	next(): void {
		if (this.selectedIndex < this._steps.length - 1) {
			this._steps.toArray()[this.selectedIndex].markAsInteracted();
			this.selectedIndex += 1;
		}
		this.cd.detectChanges();
	}

	previous(): void {
		if (this.selectedIndex !== 0 && this.selectedIndex < this._steps.length) {
			this.selectedIndex -= 1;
		}
		this.cd.detectChanges();
	}

	setSelected(index: number): void {
		this._steps.toArray()[this.selectedIndex].markAsInteracted();
		this.selectedIndex = index;
		this.cd.detectChanges();
	}
}
