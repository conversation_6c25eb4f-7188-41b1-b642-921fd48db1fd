<div class="pct-stepper-navigation-bar">
	<ng-container *ngFor="let step of _steps; let i = index; let isLast = last">
		<pacto-cat-step-header
			(click)="setSelected(i)"
			[completed]="step.done"
			[index]="i"
			[interacted]="step.interacted"
			[isLast]="isLast"
			[label]="step.stepLabel"
			[selected]="selectedIndex"></pacto-cat-step-header>
	</ng-container>
</div>
<hr />
<div class="pct-steps-container">
	<div class="pct-step-content">
		<ng-container *ngTemplateOutlet="template"></ng-container>
	</div>
</div>
