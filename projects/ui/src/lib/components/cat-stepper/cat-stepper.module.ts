import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CatStepperComponent } from "./components/cat-stepper/cat-stepper.component";
import { CatStepComponent } from "./components/cat-step/cat-step.component";
import { CatStepHeaderComponent } from "./components/cat-step-header/cat-step-header.component";
import { CatStepLabelDirective } from "./directives/cat-step-label/cat-step-label.directive";
import { CatStepIconDirective } from "./directives/cat-step-icon/cat-step-icon.directive";
import { CatStepNextDirective } from "./directives/cat-step-next/cat-step-next.directive";
import { CatStepPreviousDirective } from "./directives/cat-step-previous/cat-step-previous.directive";
import { CdkStepperModule } from "@angular/cdk/stepper";

@NgModule({
	declarations: [
		CatStepperComponent,
		CatStepComponent,
		CatStepHeaderComponent,
		CatStepLabelDirective,
		CatStepIconDirective,
		CatStepNextDirective,
		CatStepPreviousDirective,
	],
	imports: [CommonModule, CdkStepperModule],
	exports: [
		CatStepperComponent,
		CatStepComponent,
		CatStepHeaderComponent,
		CatStepLabelDirective,
		CatStepIconDirective,
		CatStepNextDirective,
		CatStepPreviousDirective,
	],
})
export class CatStepperModule {}
