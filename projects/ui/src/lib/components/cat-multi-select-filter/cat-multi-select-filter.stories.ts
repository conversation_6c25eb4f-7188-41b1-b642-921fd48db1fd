import { storiesOf, moduleMetadata } from "@storybook/angular";
import { FormsModule, ReactiveFormsModule, FormControl } from "@angular/forms";
import { HttpClientModule } from "@angular/common/http";
import {
	CatMultiSelectFilterComponent,
	CatSmoothScrollDirective,
	CatFormInputComponent,
	CatPersonAvatarComponent,
} from "projects/ui/src/public-api";
import { TextMaskModule } from "angular2-text-mask";
import { Component, Input } from "@angular/core";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-multi-select-filter.stories.html",
	styleUrls: ["./cat-multi-select-filter.stories.scss"],
})
class HostComponent {
	@Input() endpointUrl;
	@Input() resposeParser;
	@Input() control;
	@Input() size;
	@Input() options;
	@Input() labelKey;
	@Input() label;
}

const metadata = {
	imports: [
		FormsModule,
		BrowserAnimationsModule,
		HttpClientModule,
		TextMaskModule,
		ReactiveFormsModule,
	],
	declarations: [
		CatMultiSelectFilterComponent,
		CatPersonAvatarComponent,
		CatSmoothScrollDirective,
		CatFormInputComponent,
	],
};

storiesOf("Raw Input|Multi Select", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Normal", () => {
		const imageUri =
			"https://www.shareicon.net/data/512x512/2016/08/18/814068_face_512x512.png";
		return {
			component: HostComponent,
			props: {
				endpointUrl: null,
				options: [
					{ id: 1, nome: "Long name starts here: Ryu", imageUri },
					{ id: 2, nome: "Long name starts here: Guile", imageUri },
					{ id: 3, nome: "Long name starts here: Chun-li", imageUri },
					{
						id: 31,
						nome: "Long name starts here: M. Bison",
						imageUri,
					},
					{
						id: 4,
						nome: "Long name starts here: Ken Masters",
						imageUri,
					},
					{ id: 5, nome: "Long name starts here: Sagat 1", imageUri },
					{ id: 6, nome: "Long name starts here: Sagat 2", imageUri },
					{ id: 7, nome: "Long name starts here: Sagat 3", imageUri },
					{ id: 8, nome: "Long name starts here: Sagat 4", imageUri },
					{ id: 9, nome: "Long name starts here: Sagat 5", imageUri },
					{
						id: 10,
						nome: "Long name starts here: Sagat 6",
						imageUri,
					},
					{
						id: 11,
						nome: "Long name starts here: Sagat 7",
						imageUri,
					},
					{
						id: 12,
						nome: "Long name starts here: Sagat 8",
						imageUri,
					},
					{ id: 13, nome: "Long name starts here: Sagat 9", imageUri },
				],
				label: "Personagem",
				labelKey: "nome",
				imageKey: "imageUri",
				control: new FormControl(),
				resposeParser: () => {
					return [];
				},
			},
		};
	});
