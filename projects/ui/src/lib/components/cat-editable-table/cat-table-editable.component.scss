@import "projects/ui/assets/import.scss";

:host {
	display: block;
	background-color: #fff;
}

.editable-table-content {
	position: relative;
}

.editable-table.editable-table-content,
.editable-table.pacto-table-title-block i.pct {
	font-size: 16px;
	overflow-x: auto;
}

.editable-table-content {
	&::-webkit-scrollbar {
		width: 4px;
		height: 4px;
		background: $azulimPri;
	}

	&::-webkit-scrollbar:hover {
		width: 10px;
		height: 10px;
	}

	&::-webkit-scrollbar-thumb {
		background: $azulPacto01;
	}
}

.pacto-table-title-block {
	position: relative;
	padding: 30px 30px 15px;
	display: flex;

	.title-aux-wrapper {
		margin-right: 30px;
	}

	.table-title {
		font-size: 16px;
		font-weight: 700;
		color: rgb(51, 51, 51);
	}

	.table-description {
		font-size: 13px;
		font-weight: rgb(165, 165, 165);
		font-weight: 300;
	}

	.search {
		position: relative;
		flex-grow: 1;

		input {
			width: 230px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}
}

@media (max-width: 1024px) {
	.search {
		display: none;
	}

	.pacto-table-title-block {
		justify-content: space-between;
	}
}

pacto-table-renderer {
	display: block;
	margin-top: 20px;
}

.exportar-dropdown {
	.item {
		line-height: 24px;
		padding: 5px 20px;
		cursor: pointer;

		&:hover {
			background-color: #eee;
		}
	}
}

.footer-row {
	display: flex;
	margin-top: 20px;
	padding-top: 15px;
	border-top: 1px solid #f1f1f1;
	flex-direction: row-reverse;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}
}

.simple-total-row {
	background-color: #f9f9f9;
	border-top: 1px solid #ededed;
	border-bottom: 1px solid #ededed;
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 30px;
	margin-top: 14px;
	color: #7f7f7f;
}

.total-values {
	line-height: 32px;
	color: #9d9d9d;
	font-weight: bold;
	margin-right: 10px;
	font-size: 13px;

	.value {
		padding: 0px 3px;
	}
}

.novo-botao {
	margin-left: 15px;
}

.actions {
	display: flex;
}
