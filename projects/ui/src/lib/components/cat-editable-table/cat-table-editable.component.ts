import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
	ViewRef,
} from "@angular/core";
import {
	DataFiltro,
	PactoActionConfig,
	PactoDataGridConfig,
	RelatorioExportarFormato,
} from "../relatorio/data-grid.model";
import { GridFilterConfig } from "../relatorio/data-grid-filter.model";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { DataGridFilterComponent } from "../relatorio/data-grid-filter/data-grid-filter.component";
import { Subscription } from "rxjs";
import { FormControl } from "@angular/forms";
import { DataGridService } from "../relatorio/data-grid.service";
import { debounceTime } from "rxjs/operators";
import { CatTableEditableRendererComponent } from "./cat-table-renderer/cat-table-editable-renderer.component";

@Component({
	selector: "pacto-cat-table-editable",
	templateUrl: "./cat-table-editable.component.html",
	styleUrls: ["./cat-table-editable.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [DataGridService],
})
export class CatTableEditableComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	@Input() idSuffix;
	@Input() tableTitle: TemplateRef<any> | string;
	@Input() tableDescription: TemplateRef<any>;
	@Input() baseFilter: DataFiltro = {};
	@Input() filterConfig: GridFilterConfig;
	@Input() apiReturnProperty;
	@Input() table: PactoDataGridConfig = new PactoDataGridConfig({
		state: null,
		buttons: null,
		columns: [],
	});
	@Input() isEditable: boolean;
	@Input() showDelete = true;
	@Input() showEdit = true;
	@Input() editTooltipText = "Editar";
	@Input() editLinkText: string;
	@Input() confirmTooltipText = "Salvar";
	@Input() deleteTooltipText = "Excluir";
	@Input() showAddRow: boolean;
	@Input() isAddRowAvailable: boolean = true;
	@Input() actionTitle: string;
	@Input() customActions: TemplateRef<any>;
	@Input() itensPerPage = [
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];
	@Input() newLineTitle: string;
	@Output() pageChangeEvent: EventEmitter<any> = new EventEmitter();
	@Output() pageSizeChange: EventEmitter<any> = new EventEmitter();
	@Output() sortEvent: EventEmitter<any> = new EventEmitter();
	@Output() iconClick: EventEmitter<{
		row: any;
		iconName: string;
	}> = new EventEmitter();
	@Output() cellClick: EventEmitter<{
		row: any;
		column: any;
	}> = new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() btnClick: EventEmitter<any> = new EventEmitter();
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();
	@Output() edit: EventEmitter<any> = new EventEmitter<any>();
	@Output() confirm: EventEmitter<any> = new EventEmitter<any>();
	@Output() delete: EventEmitter<any> = new EventEmitter<any>();
	@Output() isEditingOrAddingItem: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	@ViewChild("renderer", { static: false })
	renderer: CatTableEditableRendererComponent;
	@ViewChild("quickSearch", { static: false }) quickSearch;
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("dataGridFilter", { static: false })
	dataGridFilter: DataGridFilterComponent;
	@ViewChild("filterToggleButton", { static: false })
	filterToggleButton: ElementRef;

	ngbPage;
	private dataFetchSubscription: Subscription;
	private temporaryFilters;

	pageSizeControl: FormControl = new FormControl();
	quickSearchControl: FormControl = new FormControl();
	dataFetchLoading = false;
	data: any = {
		content: [],
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		number: 0,
	};
	rawData = [];
	rawDataIcons: Array<Array<PactoActionConfig>> = [];
	rawDropdownActionItems: Array<Array<PactoActionConfig>> = [];

	get RelatorioExportarFormato() {
		return RelatorioExportarFormato;
	}

	constructor(
		private dataService: DataGridService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		if (
			this.isAddRowAvailable === undefined ||
			this.isAddRowAvailable === null
		) {
			this.isAddRowAvailable = true;
		}
		this.pageSizeControl.setValue(this.itensPerPage[0].id);
	}

	ngOnDestroy() {
		if (this.dataFetchSubscription) {
			this.dataFetchSubscription.unsubscribe();
		}
	}

	ngAfterViewInit() {
		setTimeout(() => {
			if (!(this.cd as ViewRef).destroyed) {
				this.initialSetup();
				if (this.dataGridFilter) {
					this.temporaryFilters = this.dataGridFilter.getFilterData();
				}
				this.fetchData();
				this.autoFocus();
			}
		});
	}

	updateRow(index, row) {
		this.rawData[index] = row;
		this.cd.detectChanges();
	}

	detectChanges() {
		this.cd.detectChanges();
	}

	addNewLine() {
		if (this.renderer) {
			this.renderer.addNewLine();
		}
	}
	reloadData() {
		this.fetchData();
	}

	private initialSetup() {
		// current page
		if (this.table) {
			this.ngbPage = this.table.state.paginaNumero + 1;
			// quick search
			if (this.table.quickSearch) {
				this.quickSearchControl.valueChanges
					.pipe(debounceTime(500))
					.subscribe(() => {
						this.table.state.paginaNumero = 0;
						this.ngbPage = 1;
						this.fetchData();
					});
			}
		}
		// page size
		this.pageSizeControl.valueChanges.subscribe((pageSize) => {
			this.table.state.paginaTamanho = parseInt(pageSize, 10);
			this.table.state.paginaNumero = 0;
			this.ngbPage = 1;
			if (this.table.endpointUrl) {
				this.fetchData();
			} else {
				this.pageSizeChange.emit(+pageSize);
			}
		});
	}

	private autoFocus() {
		if (this.table && this.table.quickSearch) {
			this.quickSearch.nativeElement.focus();
		}
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else {
			return type !== "string";
		}
	}

	alterfilterConfigUpdate(statusConfid) {
		this.filterConfigUpdate.emit(statusConfid);
	}

	btnCLickHandler() {
		this.btnClick.emit(this.table.buttons.nome);
	}

	filterHandler(filter) {
		this.temporaryFilters = filter;
		this.filterDropdown.close();
		this.fetchData();
	}

	private fetchData() {
		if (this.table) {
			const filtros = this.fetchFiltros();
			const baseFilter: DataFiltro = JSON.parse(
				JSON.stringify(this.baseFilter)
			);

			/**
			 * Merge 'filters'
			 */
			if (baseFilter.filters) {
				Object.assign(filtros.filters, baseFilter.filters);
			}

			this.dataFetchLoading = true;
			if (this.table.ghostLoad) {
				this.rawData = new Array(this.table.ghostAmount);
			} else {
				this.rawData = [];
			}

			if (this.dataFetchSubscription) {
				this.dataFetchSubscription.unsubscribe();
			}

			if (this.table.dataFn) {
				this.data = this.table.dataFn(filtros);
				this.rawData = this.data.content;
				this.populateRawDataIcons();
				this.populateRawDropdownActionItems();
				this.cd.detectChanges();
			} else {
				this.dataFetchSubscription = this.dataService
					.obterDados(
						this.table.endpointUrl,
						filtros,
						this.table.endpointParamsType
					)
					.subscribe((raw) => {
						let tranformedData;
						if (this.table.dataAdapterFn) {
							tranformedData = this.table.dataAdapterFn(raw);
						} else {
							tranformedData = raw;
						}
						this.dataFetchLoading = false;
						this.data = tranformedData;
						this.rawData = tranformedData.content || tranformedData.result;

						this.populateRawDataIcons();
						this.populateRawDropdownActionItems();
						this.cd.detectChanges();
					});
			}
		}
	}

	fetchFiltros(): DataFiltro {
		const filtros: DataFiltro = {
			filters:
				this.temporaryFilters && this.temporaryFilters.filters
					? this.temporaryFilters.filters
					: {},
			configs:
				this.temporaryFilters && this.temporaryFilters.configs
					? this.temporaryFilters.configs
					: {},
			sortField: this.table.state.ordenacaoColuna,
			sortDirection: this.table.state.ordenacaoDirecao,
		};

		if (this.table.quickSearch) {
			filtros.filters.quicksearchValue = this.quickSearchControl.value;
			filtros.filters.quicksearchFields = this.fetchQuicksearchFields();
		}

		if (this.table.pagination) {
			filtros.page = this.table.state.paginaNumero;
			filtros.size = this.table.state.paginaTamanho;
		}
		this.sanitizeFilter(filtros);
		const copy: DataFiltro = {};
		Object.assign(copy, filtros);
		return copy;
	}

	private sanitizeFilter(filtro: DataFiltro) {
		if (!filtro.sortField) {
			filtro.sortDirection = null;
		}
		this.removeUnsetValues(filtro);
		this.removeUnsetValues(filtro.filters);
	}

	private removeUnsetValues(object) {
		for (const key in object) {
			const ownProperty = Object.prototype.hasOwnProperty.call(object, key);
			if (ownProperty) {
				const element = object[key];
				if (element === undefined || element === null || element.length === 0) {
					delete object[key];
				}
			}
		}
	}

	private fetchQuicksearchFields() {
		const result = [];
		this.table.columns.forEach((column) => {
			if (column.buscaRapida) {
				result.push(column.nome);
			}
		});
		return result;
	}

	private populateRawDataIcons() {
		this.rawDataIcons = [];
		if (this.rawData) {
			this.rawData.forEach((rawItem) => {
				const actions = [];
				this.table.actions.forEach((action) => {
					if (action.showIconFn === null) {
						actions.push(action);
					} else if (action.showIconFn(rawItem)) {
						actions.push(action);
					}
				});

				this.rawDataIcons.push(actions);
			});
		}
	}

	private populateRawDropdownActionItems() {
		this.rawDropdownActionItems = [];
		if (this.rawData) {
			this.rawData.forEach((rawItem) => {
				const ddActions = [];
				this.table.dropDownActions.forEach((ddAction) => {
					if (ddAction.showIconFn === null) {
						ddActions.push(ddAction);
					} else if (ddAction.showIconFn(rawItem)) {
						ddActions.push(ddAction);
					}
				});

				this.rawDropdownActionItems.push(ddActions);
			});
		}
	}

	sortUpdateHandler() {
		this.sortEvent.emit({
			columnName: this.table.state.ordenacaoColuna,
			direction: this.table.state.ordenacaoDirecao,
			column: this.table.columns.find((column) =>
				column.orderColumn
					? column.orderColumn === this.table.state.ordenacaoColuna
					: column.nome === this.table.state.ordenacaoColuna
			),
		});
		this.fetchData();
	}

	cellClickHandler($event) {
		this.cellClick.emit($event);
	}

	pageChangeHandler(page) {
		this.table.state.paginaNumero = page - 1;
		if (this.table.endpointUrl) {
			this.fetchData();
		} else {
			this.pageChangeEvent.emit(page);
		}
	}

	relatorioHandler(format: RelatorioExportarFormato) {
		const filtros = this.fetchFiltros();
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}
		this.dataService
			.obterRelatorio(
				this.table.endpointUrl,
				filtros,
				format,
				this.table.endpointParamsType
			)
			.subscribe((link) => {
				window.open(link, "_blank");
			});
	}
}
