<ng-container *ngIf="table">
	<div
		class="pacto-table-title-block"
		*ngIf="
			tableTitle || table.quickSearch || (table.showFilters && filterConfig)
		">
		<div *ngIf="tableTitle" class="title-aux-wrapper">
			<div class="table-title" *ngIf="isTemplate(tableTitle)">
				<ng-container *ngTemplateOutlet="tableTitle"></ng-container>
			</div>
			<div class="table-title" *ngIf="!isTemplate(tableTitle)">
				{{ tableTitle }}
			</div>
			<div class="table-description" *ngIf="isTemplate(tableDescription)">
				<ng-container *ngTemplateOutlet="tableDescription"></ng-container>
			</div>
			<div class="table-description" *ngIf="!isTemplate(tableDescription)">
				{{ tableDescription }}
			</div>
		</div>

		<div class="search">
			<div *ngIf="table.quickSearch">
				<input
					#quickSearch
					placeholder="{{ 'relatorio.buscaPlaceholder' | translate }}"
					[formControl]="quickSearchControl"
					class="form-control"
					[id]="
						idSuffix ? 'input-busca-rapida-' + idSuffix : 'input-busca-rapida'
					"
					type="text" />
				<i class="pct pct-search"></i>
			</div>
		</div>

		<div class="actions">
			<div *ngIf="customActions">
				<ng-container *ngTemplateOutlet="customActions"></ng-container>
			</div>
			<div class="filter-wrapper" *ngIf="table.showFilters && filterConfig">
				<div
					ngbDropdown
					#filterDropdown="ngbDropdown"
					class="d-inline-block"
					[autoClose]="false"
					[placement]="'bottom-right'">
					<button
						#filterToggleButton
						class="btn btn-primary"
						[id]="
							idSuffix ? 'filtros-dropdown-' + idSuffix : 'filtros-dropdown'
						"
						ngbDropdownToggle>
						<i class="pct pct-filter"></i>
					</button>

					<div ngbDropdownMenu aria-labelledby="filtros-dropdown">
						<pacto-data-grid-filter
							#dataGridFilter
							[config]="filterConfig"
							[columnsConfig]="table.columns"
							(filter)="filterHandler($event)"
							(configUpdate)="
								alterfilterConfigUpdate($event)
							"></pacto-data-grid-filter>
					</div>
				</div>
			</div>
			<div
				ngbDropdown
				class="d-inline-block"
				*ngIf="table.exportButton"
				[placement]="'bottom-right'">
				<button
					class="btn btn-primary novo-botao"
					[id]="
						idSuffix ? 'relatorio-exportar-' + idSuffix : 'relatorio-exportar'
					"
					ngbDropdownToggle>
					Exportar
				</button>
				<div
					ngbDropdownMenu
					aria-labelledby="relatorio-exportar"
					class="exportar-dropdown">
					<div
						class="item"
						[id]="
							idSuffix
								? 'relatorio-exportar-pdf-' + idSuffix
								: 'relatorio-exportar-pdf'
						"
						(click)="relatorioHandler(RelatorioExportarFormato.PDF)">
						<i class="pct pct-file"></i>
						Em PDF
					</div>
					<div
						class="item"
						[id]="
							idSuffix
								? 'relatorio-exportar-excel-' + idSuffix
								: 'relatorio-exportar-excel'
						"
						(click)="relatorioHandler(RelatorioExportarFormato.XLS)">
						<i class="pct pct-file-text"></i>
						Em Excel
					</div>
				</div>
			</div>
			<div>
				<button
					*ngIf="table.buttons"
					(click)="btnCLickHandler()"
					[id]="idSuffix ? table.buttons.id + '-' + idSuffix : table.buttons.id"
					class="btn btn-primary novo-botao">
					<ng-container
						*ngTemplateOutlet="table.buttons.conteudo"></ng-container>
				</button>
			</div>
		</div>
	</div>

	<div class="editable-table editable-table-content">
		<pacto-cat-table-editable-renderer
			#renderer
			[idSuffix]="idSuffix"
			[showEdit]="showEdit"
			[dataGridConfig]="table"
			[newLineTitle]="newLineTitle"
			[loading]="dataFetchLoading"
			[data]="rawData"
			[rawDataIcons]="rawDataIcons"
			[rawDropdownActionItems]="rawDropdownActionItems"
			(sort)="sortUpdateHandler()"
			(cellClick)="cellClickHandler($event)"
			(rowClick)="rowClick.emit($event)"
			(confirmEvent)="confirm.emit($event)"
			(editEvent)="edit.emit($event)"
			(deleteEvent)="delete.emit($event)"
			(reloadEvent)="reloadData()"
			[isEditable]="isEditable"
			[showDelete]="showDelete"
			[editTooltipText]="editTooltipText"
			[editLinkText]="editLinkText"
			[confirmTooltipText]="confirmTooltipText"
			[deleteTooltipText]="deleteTooltipText"
			[showAddRow]="showAddRow"
			[isAddRowAvailable]="isAddRowAvailable"
			[neverShowAddRow]="!showAddRow"
			(isEditingOrAddingItem)="isEditingOrAddingItem.emit($event)"
			[actionTitle]="actionTitle"></pacto-cat-table-editable-renderer>

		<div
			class="simple-total-row"
			*ngIf="table.showSimpleTotalCount && !dataFetchLoading">
			{{ table.pagination ? data.totalElements : rawData.length }}
			<span>items</span>
		</div>

		<div class="footer-row" *ngIf="table.pagination">
			<ng-container *ngIf="!dataFetchLoading && rawData && rawData.length">
				<ngb-pagination
					[collectionSize]="data.totalElements"
					(pageChange)="pageChangeHandler($event)"
					[(page)]="ngbPage"
					[size]="'sm'"
					[pageSize]="data.size"
					[ellipses]="false"
					[boundaryLinks]="true"
					[maxSize]="7"
					class="d-flex justify-content-end"></ngb-pagination>

				<pacto-cat-select
					[control]="pageSizeControl"
					[size]="'SMALL'"
					[items]="itensPerPage"></pacto-cat-select>

				<div class="total-values">
					<span i18n="@@component-relatorio:mostrando">Mostrando</span>
					<span class="value">{{ data.content.length }}</span>
					<span i18n="@@component-relatorio:de">de</span>
					<span class="value">{{ data.totalElements }}</span>
				</div>
			</ng-container>
		</div>
	</div>
</ng-container>
