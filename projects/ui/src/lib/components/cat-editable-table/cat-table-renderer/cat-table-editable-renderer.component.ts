import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import {
	PactoActionConfig,
	PactoDataGridColumnConfig,
	PactoDataGridConfig,
	PactoDataGridOrdenacaoDirecao,
} from "../../relatorio/data-grid.model";
import { trigger } from "@angular/animations";
import { fadeIn } from "../../../pacto-animations";
import { DialogService } from "../../../dialog/service/dialog.service";
import { Observable, throwError } from "rxjs";
import { FormGroup } from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { SnotifyService } from "ng-snotify";
import { TranslatePipe, TranslateService } from "@ngx-translate/core";

declare var $;
let uniqueId = 0;

@Component({
	selector: "pacto-cat-table-editable-renderer",
	templateUrl: "./cat-table-editable-renderer.component.html",
	styleUrls: ["./cat-table-editable-renderer.component.scss"],
	changeDetection: ChangeDetectionStrategy.Default,
	animations: [trigger("fadeIn", fadeIn(":enter"))],
	providers: [TranslatePipe],
})
export class CatTableEditableRendererComponent implements OnInit {
	id = `table-renderer-${uniqueId++}`;
	@Input() idSuffix;
	@Input() dataGridConfig: PactoDataGridConfig;
	@Input() data: Array<any>;
	@Input() loading = false;
	@Input() rawDataIcons: Array<Array<PactoActionConfig>>;
	@Input() rawDropdownActionItems: Array<Array<PactoActionConfig>>;
	@Input() emptyStateMessage: string;
	@Input() isEditable: boolean;
	@Input() showDelete = true;
	@Input() editTooltipText = "Editar";
	@Input() editLinkText: string;
	@Input() confirmTooltipText = "Salvar";
	@Input() deleteTooltipText = "Excluir";
	@Input() showAddRow: boolean;
	@Input() isAddRowAvailable: boolean;
	@Input() neverShowAddRow: boolean;
	@Input() showEdit: boolean;
	@Input() actionTitle: string;
	@Input() newLineTitle: string;
	isEdittingOneRow: boolean;
	editingRowIndex: number;
	@Output() sort: EventEmitter<{
		column: string;
		direction: string;
	}> = new EventEmitter();
	@Output() cellClick: EventEmitter<{
		row: any;
		column: any;
	}> = new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() editEvent: EventEmitter<any> = new EventEmitter<any>();
	@Output() confirmEvent: EventEmitter<any> = new EventEmitter<any>();
	@Output() deleteEvent: EventEmitter<any> = new EventEmitter<any>();
	@Output() reloadEvent: EventEmitter<string> = new EventEmitter<string>();
	@Output() isEditingOrAddingItem: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	private isShowingRow = true;

	constructor(
		private dialogService: DialogService,
		private http: HttpClient,
		private notifyService: SnotifyService,
		private translatePipe: TranslateService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.editTooltipText = this.translatePipe.instant("actions.editar");
		this.confirmTooltipText = this.translatePipe.instant("actions.salvar");
		this.deleteTooltipText = this.translatePipe.instant("actions.excluir");
		this.isShowingRow = this.showAddRow;
		this.dataGridConfig.columns.forEach((column) => {
			if (column.visible) {
				if (column.endpointUrl) {
					const url = column.endpointUrl;
					this.http.get(url).subscribe((response: any) => {
						if (response.content) {
							column.inputSelectData = response.content;
						} else {
							column.inputSelectData = response;
						}
					});
				}
			}
		});
	}

	get visibleColumns() {
		return this.dataGridConfig.columns.filter((column) => {
			if (column.visible != null) {
				return column.visible;
			} else {
				return column.defaultVisible;
			}
		});
	}

	get dados() {
		return this.data;
	}

	edit(row, rowIndex) {
		if (this.dataGridConfig.formGroup) {
			this.isEdittingOneRow = true;
			this.showAddRow = false;
			this.editingRowIndex = rowIndex;
			this.isEditingOrAddingItem.emit(true);
			Object.keys(row).forEach((key) => {
				if (this.dataGridConfig.formGroup.controls.hasOwnProperty(key)) {
					const column = this.dataGridConfig.columns.find(
						(column2) => column2.nome === key
					);
					if (column) {
						if (column.inputType === "select") {
							if (!column.inputSelectData) {
								column.inputSelectData = new Array<any>();
							}
							if (column.inputSelectData.length === 0) {
								column.inputSelectData.push(row[key]);
							}
						}
						if (column.inputSelectData && column.inputSelectData.length > 0) {
							const result = column.inputSelectData.find((data) => {
								if (row[key] && row[key][column.idSelectKey]) {
									if (
										data[column.idSelectKey] === row[key][column.idSelectKey]
									) {
										return data;
									}
								} else if (row[key]) {
									if (data[column.idSelectKey] === row[key]) {
										return data;
									}
								}
							});
							if (result) {
								row[key] = result;
							}
						}
					}
					this.dataGridConfig.formGroup.get(key).setValue(row[key]);
				}
			});
		}
		this.editEvent.emit({ row, form: this.dataGridConfig.formGroup });
	}

	confirm(row, form: FormGroup, rowIndex) {
		Object.keys(form.controls).forEach((key) => {
			row[key] = form.get(key).value;
		});

		if (
			this.dataGridConfig.beforeConfirm === undefined ||
			this.dataGridConfig.beforeConfirm(row, form, this.data, rowIndex)
		) {
			if (!this.verifyIfNewExists(row, rowIndex)) {
				if (!this.isEdittingOneRow) {
					row.edit = false;
				}
				this.editingRowIndex = null;
				this.isEditingOrAddingItem.emit(false);
				if (this.isShowingRow) {
					this.showAddRow = true;
				}
				if (!row.edit && this.dataGridConfig.onAddFn) {
					const resultAddFn = this.dataGridConfig.onAddFn(
						row,
						this.data,
						rowIndex
					);
					if (resultAddFn instanceof Observable) {
						resultAddFn.subscribe(
							(result) => {
								if (
									result !== undefined &&
									result !== null &&
									typeof result === "object"
								) {
									this.data[rowIndex] = result;
								}
								if (result === true) {
									this.reloadEvent.emit("onAdd");
								}
							},
							(e) => {
								this.data.splice(rowIndex, 1);
								this.cd.detectChanges();
							}
						);
					}
				}
				if (row.edit && this.dataGridConfig.onEditFn) {
					const resultAddFn = this.dataGridConfig.onEditFn(
						row,
						this.data,
						rowIndex
					);
					if (resultAddFn instanceof Observable) {
						resultAddFn.subscribe((success) => {
							if (
								success !== undefined &&
								success !== null &&
								typeof success === "object"
							) {
								this.reloadEvent.emit("onEdit");
								row.edit = false;
							}
						});
					}
				}
				this.isEdittingOneRow = false;
				this.confirmEvent.emit({ row, form, data: this.data, rowIndex });
				row.edit = false;
				form.reset();
			} else {
				this.notifyService.error("Dados duplicados!");
			}
		}
	}

	verifyIfNewExists(row, rowIndex) {
		let exists = false;
		this.data.find((data, index) => {
			if (index !== rowIndex) {
				const compareObject: any = {};
				const dataCompare: any = {};
				Object.keys(row).forEach((key) => {
					if (key !== "edit") {
						compareObject[key] = row[key];
					}
				});
				Object.keys(data).forEach((key) => {
					if (key !== "edit") {
						dataCompare[key] = data[key];
					}
				});
			}
		});
		return exists;
	}

	editOrConfirm(row, form?: FormGroup, rowIndex?) {
		if (this.dataGridConfig.onClickEditFn && row.edit === false) {
			this.dataGridConfig.onClickEditFn(row, this.data, rowIndex, form);
			return;
		}

		if (!this.isEdittingOneRow || rowIndex === this.editingRowIndex) {
			if (!row.edit) {
				row.edit = true;
				this.edit(row, rowIndex);
			} else {
				if (form.valid) {
					let countEmpty = 0;
					Object.keys(form.getRawValue()).forEach((key) => {
						const column = this.dataGridConfig.columns.find(
							(column2) => column2.nome === key
						);
						if (
							column &&
							column.visible &&
							(form.getRawValue()[key] === undefined ||
								(form.getRawValue()[key] !== undefined &&
									form.getRawValue()[key] !== null &&
									form.getRawValue()[key].toString() === "") ||
								form.getRawValue()[key]) === undefined
						) {
							countEmpty++;
						}
					});
					if (countEmpty === 0) {
						this.confirm(row, form, rowIndex);
					}
				} else if (this.dataGridConfig.beforeConfirm) {
					this.dataGridConfig.beforeConfirm(row, form, this.data, rowIndex);
				}
			}
		}
	}

	delete(row, form, index?) {
		if (this.editingRowIndex !== undefined || index !== this.editingRowIndex) {
			this.isEdittingOneRow = false;
			this.editingRowIndex = null;
			this.isEditingOrAddingItem.emit(false);
			form.reset();
			this.deleteEvent.emit({ row, index, data: this.data });
			if (this.dataGridConfig.onDeleteFn) {
				const resultDeleteFn = this.dataGridConfig.onDeleteFn(
					row,
					this.data,
					index
				);
				if (resultDeleteFn instanceof Observable) {
					resultDeleteFn.subscribe((success) => {
						if (success === true) {
							this.reloadEvent.emit("onDelete");
						}
						if (success === null) {
							this.data.splice(index, 1);
							this.cd.markForCheck();
						}
					});
				}
			}
			this.showAddRow = !this.neverShowAddRow;
			this.cd.detectChanges();
		}
	}

	rowClickHandler(row, $event) {
		const $target = $($event.target);
		const actionClick = $target.closest(".action-cell").length;
		if (!actionClick && this.dataGridConfig.rowClick) {
			this.rowClick.emit(row);
		}
	}

	isSortable(column: string) {
		return this.dataGridConfig.columns.find((i) => i.nome === column).ordenavel;
	}

	sortClick(column: PactoDataGridColumnConfig) {
		if (this.isSortable(column.nome)) {
			this.updateSortState(column);
			this.sort.emit({
				column: this.dataGridConfig.state.ordenacaoColuna,
				direction: this.dataGridConfig.state.ordenacaoDirecao,
			});
		}
	}

	private updateSortState(column: PactoDataGridColumnConfig) {
		if (!column.ordenavel) {
			return false;
		}
		if (this.dataGridConfig.state.ordenacaoColuna !== column.nome) {
			this.dataGridConfig.state.ordenacaoColuna = column.nome;
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.ASC;
		} else if (
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.ASC
		) {
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.DESC;
		} else {
			this.dataGridConfig.state.ordenacaoDirecao =
				PactoDataGridOrdenacaoDirecao.ASC;
		}
	}

	getSortIconClass(column: string) {
		const ordenada = this.dataGridConfig.state.ordenacaoColuna === column;
		const asc =
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.ASC;
		const desc =
			this.dataGridConfig.state.ordenacaoDirecao ===
			PactoDataGridOrdenacaoDirecao.DESC;
		const ordenavel = this.dataGridConfig.columns.find(
			(c) => c.nome === column
		).ordenavel;
		if (ordenada && asc) {
			return "pct pct-caret-up";
		} else if (ordenada && desc) {
			return "pct pct-caret-down";
		} else if (ordenavel) {
			return "pct pct-drop-down";
		}
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else if (type === "string") {
			return false;
		} else {
			return true;
		}
	}

	columnCellClickHandler(row, column) {
		this.cellClick.emit({ row, column });
	}

	addNew(column: PactoDataGridColumnConfig) {
		if (column) {
			this.dialogService.open(
				column.modalTitle,
				column.modalComponent,
				column.modalSize
			);
		} else {
			throwError("modalAddComponent not set");
		}
	}

	addNewLine() {
		const newObj: any = {};
		this.dataGridConfig.columns.forEach((column) => {
			if (column.visible) {
				newObj[column.campo] = "";
			}
		});
		newObj.edit = true;
		if (this.data) {
			this.data.push(newObj);
		} else {
			this.data = new Array<any>();
			this.data.push(newObj);
		}
		this.showAddRow = false;
		this.editingRowIndex = this.data.length - 1;
		this.isEdittingOneRow = false;
		this.isEditingOrAddingItem.emit(true);
	}

	get vendaAvulsaMessage(): string {
		return "Esta locação não pode ser editada ou excluída, pois já existem parcelas geradas vinculadas a ela.";
	}

	handleEditClick(event: any, row: any, index: number) {
		if (row.vendaAvulsaCodigo) {
			return;
		}
		this.editOrConfirm(row, this.dataGridConfig.formGroup, index);
	}

	handleRemoveClick(event: any, row: any, index: number) {
		if (row.vendaAvulsaCodigo) {
			return;
		}
		this.delete(row, this.dataGridConfig.formGroup, index);
	}

	handleShowEdit(row: any): boolean {
		if (row.showEdit !== undefined) {
			return row.showEdit;
		} else {
			return true;
		}
	}

	handleShowDelete(row: any) {
		if (row.showDelete !== undefined) {
			return row.showDelete;
		} else {
			return true;
		}
	}
}
