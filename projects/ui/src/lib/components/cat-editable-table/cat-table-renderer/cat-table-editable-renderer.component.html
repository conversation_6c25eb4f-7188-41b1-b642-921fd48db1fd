<div class="pct-table">
	<div class="pct-header-group">
		<div class="pct-table-row first-line">
			<div
				class="pct-table-cell"
				*ngFor="let column of visibleColumns"
				[ngClass]="{ sortable: isSortable(column.nome) }"
				[style.max-width]="column.width"
				[style.width]="column.width">
				<span
					[id]="
						idSuffix
							? 'column-sort-' + column.name + '-' + idSuffix
							: 'column-sort-' + column.name
					"
					(click)="sortClick(column)"
					*ngIf="column.mostrarTitulo">
					<ng-container *ngIf="isTemplate(column.titulo)">
						<ng-container *ngTemplateOutlet="column.titulo"></ng-container>
					</ng-container>
					<ng-container *ngIf="!isTemplate(column.titulo)">
						{{ column.titulo }}
					</ng-container>
					<i
						class="{{ getSortIconClass(column.nome) }}"
						*ngIf="isSortable(column.nome)"></i>
				</span>
			</div>
			<div
				*ngIf="dataGridConfig.actions.length || isEditable"
				class="action-column pct-table-cell">
				{{ actionTitle | translate }}
			</div>
			<div
				*ngIf="dataGridConfig.dropDownActions.length"
				class="action-column pct-table-cell">
				{{ "relatorio.acoesColumn" | translate }}
			</div>
		</div>
	</div>
	<div class="pct-table-body pct-table-zebra">
		<ng-container
			*ngFor="let row of dados; let lastRow = last; let rowIndex = index">
			<ng-container *ngIf="dataGridConfig.formGroup">
				<form
					[formGroup]="dataGridConfig.formGroup"
					class="pct-table-row"
					[ngClass]="{
						'total-row': dataGridConfig.totalRow && lastRow,
						'row-clickable': dataGridConfig.rowClick,
						ghost: dataGridConfig.ghostLoad && loading,
						'row-editable': row.edit
					}"
					(click)="rowClickHandler(row, $event)"
					[@fadeIn]="dados.length">
					<!-- Data columns -->
					<div
						class="pct-table-cell"
						*ngFor="
							let column of visibleColumns;
							let firstColumn = first;
							let columnIndex = index
						"
						[style.max-width]="column.width"
						[style.width]="column.width">
						<ng-container
							[ngSwitch]="dataGridConfig.totalRow && firstColumn && lastRow">
							<!-- TOTAL COLUMN -->
							<ng-container *ngSwitchCase="true">
								<span>{{ "relatorio.total" | translate }}</span>
							</ng-container>

							<!-- DATA COLUMNS -->
							<ng-container *ngSwitchCase="false">
								<div>
									<span
										(click)="columnCellClickHandler(row, column)"
										*ngIf="!row.edit || !column.editable"
										class="column-cell"
										[ngClass]="{ 'hover-cell': column.cellPointerCursor }">
										<ng-container *ngIf="!column.celula && column.campo && row">
											<ng-container *ngIf="column.inputType !== 'color'">
												{{
													column.valueTransform
														? row[column.nome] &&
														  row[column.nome][column.labelSelectKey]
															? column.valueTransform(
																	row[column.nome][column.labelSelectKey],
																	row
															  )
															: column.valueTransform(row[column.nome], row)
														: row[column.nome] &&
														  row[column.nome][column.labelSelectKey]
														? row[column.nome][column.labelSelectKey]
														: row[column.nome]
												}}
											</ng-container>
											<ng-container *ngIf="column.inputType === 'color'">
												<div
													class="color-input-value"
													[style.background-color]="row[column.nome]"></div>
											</ng-container>
										</ng-container>
										<ng-container *ngIf="column.celula && row">
											<ng-container
												*ngTemplateOutlet="
													column.celula;
													context: { item: row, index: rowIndex }
												"></ng-container>
										</ng-container>
									</span>
									<ng-container
										[ngSwitch]="column.inputType"
										*ngIf="row.edit && column.editable">
										<pacto-column-input
											*ngSwitchCase="'text'"
											[formControlName]="column.nome"
											[id]="
												idSuffix
													? 'input-text-' + idSuffix + '-' + column.nome
													: 'input-text-' + column.nome
											"
											[inputTextMask]="column.inputTextMask"
											[type]="column.inputType"
											[maxlength]="column.maxlength"
											[errorMsg]="column.errorMessage"
											[enableClearInput]="true"
											[disabledControl]="
												column.isDisabled !== undefined &&
												column.isDisabled(row)
											"></pacto-column-input>
										<pacto-column-input-decimal
											*ngSwitchCase="'number'"
											[formControlName]="column.nome"
											[maxlength]="column.maxlength"
											[id]="
												idSuffix
													? 'input-number-' + idSuffix + '-' + column.nome
													: 'input-number-' + column.nome
											"
											[max]="column.maxValue"
											[min]="column.minValue"
											[type]="column.inputType"
											[errorMsg]="column.errorMessage"
											[enableClearInput]="true"
											[disabledControl]="
												column.isDisabled !== undefined &&
												column.isDisabled(row)
											"></pacto-column-input-decimal>
										<pacto-column-input-decimal
											*ngSwitchCase="'decimal'"
											[formControlName]="column.nome"
											[id]="
												idSuffix
													? 'input-decimal-' + idSuffix + '-' + column.nome
													: 'input-decimal-' + column.nome
											"
											[min]="column.minValue"
											[decimalPrecision]="column.decimalPrecision"
											[maxlength]="column.maxlength"
											[max]="column.maxValue"
											[errorMsg]="column.errorMessage"
											[enableClearInput]="true"
											[disabledControl]="
												column.isDisabled !== undefined &&
												column.isDisabled(row)
											"></pacto-column-input-decimal>
										<pacto-column-select
											*ngSwitchCase="'select'"
											[formControlName]="column.nome"
											[options]="column.inputSelectData"
											[optionsSubscription]="column.inputSelectDataSubscription"
											[id]="
												idSuffix
													? 'select-' + idSuffix + '-' + column.nome
													: 'select-' + column.nome
											"
											[infiniteScrollEnabled]="column.infiniteScrollEnabled"
											[showEmptyMessage]="column.showEmptyMessage"
											[elementsSize]="column.infiniteScrollElementsSize"
											[endpointUrl]="column.endpointUrl"
											[paramBuilder]="column.selectParamBuilder"
											[showFilter]="column.showSelectFilter"
											[showAddBtn]="column.showAddSelectBtn"
											[labelKey]="column.labelSelectKey"
											[idKey]="column.idSelectKey"
											(addEvent)="addNew(column)"
											[disabledControl]="
												column.isDisabled !== undefined &&
												column.isDisabled(row)
											"
											(optionChange)="
												column.selectOptionChange &&
													column.selectOptionChange(
														$event,
														dataGridConfig.formGroup,
														row
													)
											"
											[addEmptyOption]="column.addEmptyOption"
											[customOption]="column.customOption"
											[labelFn]="column.labelFn"
											[resposeParser]="
												column.responseParser
											"></pacto-column-select>
										<pacto-column-checkbox
											*ngSwitchCase="'checkbox'"
											[id]="
												idSuffix
													? 'chck-' + idSuffix + '-' + column.nome
													: 'chck-' + column.nome
											"
											[formControlName]="column.campo"
											[label]="column.labelText"
											[disabledControl]="
												column.isDisabled !== undefined &&
												column.isDisabled(row)
											"></pacto-column-checkbox>
										<pacto-cat-input-color
											*ngSwitchCase="'color'"
											class="column-input-color"
											[id]="
												idSuffix
													? 'input-color-' + idSuffix + '-' + column.nome
													: 'input-color-' + column.nome
											"
											[formControlName]="column.campo"></pacto-cat-input-color>
										<pacto-cat-form-datepicker
											*ngSwitchCase="'date'"
											class="column-input-color"
											[id]="
												idSuffix
													? 'input-date-' + idSuffix + '-' + column.nome
													: 'input-date-' + column.nome
											"
											[control]="
												dataGridConfig.formGroup.get(column.campo)
											"></pacto-cat-form-datepicker>
									</ng-container>
									<div
										*ngIf="dataGridConfig.ghostLoad && !row"
										style="height: 20px !important; width: 100% !important"
										class="ghost-bar"></div>
								</div>
							</ng-container>
						</ng-container>
					</div>

					<!--Action column -->
					<div
						*ngIf="isEditable"
						class="action-cell pct-table-cell"
						[ngbTooltip]="row.vendaAvulsaCodigo ? vendaAvulsaMessage : null">
						<i class="pct pct-alert-triangle" *ngIf="row.vendaAvulsaCodigo"></i>
						<ng-container *ngIf="handleShowEdit(row)">
							<i
								*ngIf="
									showEdit &&
									(dataGridConfig.showEdit === undefined ||
										(dataGridConfig.showEdit &&
											(dataGridConfig.showEdit(row, !showAddRow) === undefined
												? true
												: dataGridConfig.showEdit(row, !showAddRow))))
								"
								class="{{
									!row.edit
										? isEdittingOneRow ||
										  (editingRowIndex && rowIndex !== editingRowIndex)
											? 'pct pct-edit disabled'
											: 'pct pct-edit'
										: 'pct pct-save'
								}}"
								[ngClass]="{ 'text-muted': row.vendaAvulsaCodigo }"
								title="{{ !row.edit ? editTooltipText : confirmTooltipText }}"
								[id]="
									(!row.edit
										? 'element-' + rowIndex + '-editar'
										: 'element-' + rowIndex + '-confirm') +
									(idSuffix ? '-' + idSuffix : '')
								"
								(click)="handleEditClick($event, row, rowIndex)"></i>
						</ng-container>
						<ng-container *ngIf="dataGridConfig.actions.length">
							<i
								*ngFor="let icon of rawDataIcons[rowIndex]"
								class="{{ icon.iconClass }}"
								title="{{ icon.tooltipText }}"
								[id]="
									idSuffix
										? 'element-' +
										  rowIndex +
										  '-' +
										  icon.nome.toLowerCase() +
										  '-' +
										  idSuffix
										: 'element-' + rowIndex + '-' + icon.nome.toLowerCase()
								"
								(click)="icon.actionFn(row)"></i>
						</ng-container>
						<ng-container *ngIf="showDelete && handleShowDelete(row)">
							<i
								class="pct pct-trash-2"
								title="{{ deleteTooltipText }}"
								*ngIf="
									dataGridConfig.showDelete === undefined ||
									(!!dataGridConfig.showDelete &&
										(dataGridConfig.showDelete(row, !showAddRow) === undefined
											? true
											: dataGridConfig.showDelete(row, !showAddRow)))
								"
								[ngClass]="{
									disabled:
										isEdittingOneRow &&
										editingRowIndex !== undefined &&
										rowIndex !== editingRowIndex,
									'text-muted': row.vendaAvulsaCodigo
								}"
								[id]="
									idSuffix
										? 'element-' + rowIndex + '-excluir-' + idSuffix
										: 'element-' + rowIndex + '-excluir'
								"
								(click)="handleRemoveClick($event, row, rowIndex)"></i>
						</ng-container>
					</div>

					<!--Action column -->
					<div
						*ngIf="dataGridConfig.actions.length && !isEditable"
						class="action-cell pct-table-cell">
						<i
							*ngFor="let icon of rawDataIcons[rowIndex]"
							class="{{ icon.iconClass }}"
							title="{{ icon.tooltipText }}"
							[id]="
								idSuffix
									? 'element-' +
									  rowIndex +
									  '-' +
									  icon.nome.toLowerCase() +
									  '-' +
									  idSuffix
									: 'element-' + rowIndex + '-' + icon.nome.toLowerCase()
							"
							(click)="icon.actionFn(row)"></i>
					</div>

					<!--Dropdown Action column -->
					<div
						*ngIf="dataGridConfig.dropDownActions.length"
						class="action-cell pct-table-cell">
						<div
							ngbDropdown
							#dropdownActions="ngbDropdown"
							class="d-inline-block"
							[autoClose]="'outside'"
							[placement]="'bottom-right'">
							<div
								[id]="
									idSuffix ? 'dropdownActions-' + idSuffix : 'dropdownActions'
								"
								ngbDropdownToggle>
								<i
									class="pct pct-more-horizontal"
									title=""
									[id]="
										idSuffix
											? 'ddaction-' + rowIndex + '-' + idSuffix
											: 'ddaction-' + rowIndex
									"></i>
							</div>

							<div ngbDropdownMenu aria-labelledby="filtros-dropdown">
								<div
									*ngFor="
										let item of rawDropdownActionItems[rowIndex];
										let i = index
									"
									[attr.data-index]="i">
									<hr *ngIf="i > 0" class="solid" />
									<div
										class="type-p-small item-dropdown"
										title="{{ item.tooltipText }}"
										[id]="
											idSuffix
												? 'ddAction-' +
												  rowIndex +
												  '-' +
												  item.nome.toLowerCase() +
												  '-' +
												  idSuffix
												: 'ddAction-' + rowIndex + '-' + item.nome.toLowerCase()
										"
										(click)="item.actionFn(row)">
										<i
											*ngIf="item.iconClass.length"
											class="{{ item.iconClass }}"></i>
										{{ item.nome }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</ng-container>
		</ng-container>
	</div>
</div>

<ng-container *ngIf="isAddRowAvailable">
	<div class="add-row-container" *ngIf="showAddRow">
		<div
			class="add-row"
			(click)="addNewLine()"
			[id]="idSuffix ? id + '-add-row-' + idSuffix : id + '-add-row'">
			<i class="pct pct-plus-square"></i>
			{{
				newLineTitle ? newLineTitle : ("relatorio.adicionarLinha" | translate)
			}}
		</div>
	</div>
</ng-container>
