@import "projects/ui/assets/import.scss";

.pct-table {
	display: table;
	width: 100%;

	.pct-table-row {
		display: table-row;
	}

	.pct-table-cell {
		display: table-cell;
		padding: 0.75rem;
		vertical-align: middle;
	}

	.pct-header-group {
		display: table-header-group;
		color: $pretoPri;

		.pct-table-cell {
			font-weight: 600 !important;
			font-size: 14px;
			line-height: 14px;
			border-bottom: 2px solid #dee2e6;
		}

		.first-line .sortable {
			cursor: pointer;

			i.pct {
				padding-left: 10px;
				color: $pretoPri;
			}
		}
	}

	.pct-table-body {
		display: table-row-group;

		&.pct-table-zebra {
			.pct-table-row:nth-child(odd) {
				background-color: #fafafa;
			}
		}

		.pct-table-row {
			height: 64px;
			margin: 0 auto;
		}

		.pct-table-cell {
			font-size: 14px;
			line-height: 14px;
			color: $cinza05;
			border-top: 1px solid #dee2e6;
		}
	}

	.column-cell.hover-cell:hover {
		cursor: pointer;
		text-decoration: underline;
	}
}

.add-row-container {
	display: flex;
	align-items: center;
	height: 64px;
	width: 100%;

	.add-row {
		color: $azulimPri;
		font-size: 14px;
		font-weight: 700;
		cursor: pointer;
	}
}

.loading-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;

	.loading-data-icon {
		width: 30px;
		position: relative;
		top: -1px;
	}
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

.action-cell {
	i {
		font-size: 16px;

		&.pct {
			padding-right: 3px;
			cursor: pointer;
		}
	}
}

.action-column {
	width: 50px;
}

.dropdown-toggle::after {
	display: none;
}

.first-line > th {
	border-top: 0;
}

hr.solid {
	width: 92%;
	margin: 2px 8px;
	cursor: default !important;
}

.item-dropdown {
	padding: 8px;
	line-height: 1;
	color: $cinza05;
}

.item-dropdown:hover {
	color: $azulimPri;
	background: $cinzaPastel;
}

.row-editable {
	background: #ffffff;
	border-radius: 0;
}

.pct-trash-2 {
	color: $hellboyPri;

	&.disabled {
		color: #efbac0 !important;
	}
}

.pct-action {
	padding-left: 7px;
	padding-right: 7px;
	margin-right: 16px;
}

.pct-edit {
	color: $azulim03;

	&.disabled {
		color: #b8d9f3 !important;
	}
}

.pct-save {
	color: $azulim03;
}

.disabled {
	cursor: not-allowed !important;
	color: $cinzaClaroPri !important;
}

.column-input-color::ng-deep {
	.cat-input-color-wrapper {
		.cat-input-color-label {
			display: none;
		}
	}
}

.color-input-value {
	height: 20px;
	width: 20px;
	border-radius: 4px;
}

.edit-link-text {
	color: $azulimPri;
	cursor: pointer;
	text-align: center;
	font-feature-settings: "clig" off, "liga" off;
	font-size: 12px;
	font-style: normal;
	font-weight: 600;
	line-height: 100%; /* 12px */
	letter-spacing: 0.25px;
	width: 110px;
	margin-right: 23px;
}

.edit-link-cell {
	display: flex !important;
	flex-direction: row;
	align-items: center;
}

.tooltip.show {
	z-index: 9999 !important;
}
