<ng-container [ngSwitch]="column.inputType">
	<pacto-column-input
		*ngSwitchCase="'text'"
		[formControlName]="column.campo"
		[inputTextMask]="inputTextMask"
		[type]="typeInputText"
		[id]="idInputText"
		[placeholder]="placeholderInputText"
		[errorMsg]="errorMsgInputText"
		[enableClearInput]="enableClearInputText"></pacto-column-input>
	<pacto-column-select
		*ngSwitchCase="'select'"
		[formControl]="control"
		[options]="column.inputSelectData"
		[endpointUrl]="endpointUrl"
		[showEmptyMessage]="column.showEmptyMessage"
		[showFilter]="showSelectFilter"
		[showAddBtn]="showSelectAddBtn"
		[labelKey]="labelKey"
		[idKey]="idKey"
		(addEvent)="onAdd()"></pacto-column-select>
	<pacto-column-checkbox
		*ngSwitchCase="'checkbox'"
		[formControl]="control"
		[label]="labelText"
		[checked]="columnValue"></pacto-column-checkbox>
</ng-container>
