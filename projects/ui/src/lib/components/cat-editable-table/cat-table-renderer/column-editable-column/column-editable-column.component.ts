import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { PactoDataGridColumnConfig } from "../../../relatorio/data-grid.model";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-column-editable-column",
	templateUrl: "./column-editable-column.component.html",
	styleUrls: ["./column-editable-column.component.scss"],
})
export class ColumnEditableColumnComponent implements OnInit {
	@Input() endpointUrl: string;
	@Input() row: any;
	@Input() column: PactoDataGridColumnConfig;
	@Input() columnValue: any;
	@Input() showSelectFilter = false;
	@Input() showSelectAddBtn = false;
	@Input() labelKey = "label";
	@Input() labelText = "";
	@Input() idKey = "id";
	@Output() valueChanges: EventEmitter<any> = new EventEmitter<any>();
	@Output() addNew: EventEmitter<any> = new EventEmitter<any>();
	@Input() control: FormControl;

	@Input() idInputText: string;
	@Input() placeholderInputText = "";
	@Input() errorMsgInputText = null;
	@Input() enableClearInputText = true;
	@Input() inputTextMask = "";
	@Input() typeInputText = "text";

	@Output() check: EventEmitter<any> = new EventEmitter<any>();

	constructor(private changeDetectorRef: ChangeDetectorRef) {}

	ngOnInit() {
		if (!this.column.control) {
			this.column.control = new FormControl(this.columnValue);
		} else {
			this.column.control.setValue(this.columnValue);
		}
		this.column.control.valueChanges.subscribe((value) => {
			this.onValueChanges(value);
		});
	}

	onAdd() {
		this.addNew.emit();
	}

	onValueChanges(value) {
		this.valueChanges.emit(value);
	}
}
