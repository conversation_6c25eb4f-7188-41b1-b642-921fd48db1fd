import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { ColumnInputDecimalComponent } from "./column-input-decimal.component";

describe("ColumnInputDecimalComponent", () => {
	let component: ColumnInputDecimalComponent;
	let fixture: ComponentFixture<ColumnInputDecimalComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [ColumnInputDecimalComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(ColumnInputDecimalComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
