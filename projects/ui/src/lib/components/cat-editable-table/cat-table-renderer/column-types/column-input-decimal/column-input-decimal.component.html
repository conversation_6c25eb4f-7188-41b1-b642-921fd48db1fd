<div class="aux-wrapper">
	<input
		#input
		(focus)="onFocus($event)"
		[formControl]="control"
		[ngClass]="{ error: showError }"
		[options]="maskOptions"
		[placeholder]="placeholder"
		currencyMask
		id="{{ id }}" />
	<i [hidden]="!showError" class="pct pct-alert-triangle error-icon"></i>
	<i (click)="clearHandler()" [hidden]="!showClearInput" class="pct pct-x"></i>
</div>
<div *ngIf="showError" class="pct-error-msg">
	<span>{{ errorMsg }}</span>
</div>
