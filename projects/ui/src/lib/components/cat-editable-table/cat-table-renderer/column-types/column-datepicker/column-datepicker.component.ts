import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormControl } from "@angular/forms";
import { CatDatepickerComponent } from "../../../../../forms/cat-datepicker/cat-datepicker.component";

@Component({
	selector: "pacto-column-datepicker",
	templateUrl: "./column-datepicker.component.html",
	styleUrls: ["./column-datepicker.component.scss"],
})
export class ColumnDatepickerComponent implements OnInit {
	@Input() id = "datepicker-input";
	@Input() control: FormControl;
	@Input() errorMsg = "";
	@Input() label;
	@ViewChild("datepicker", { static: true }) datepicker: CatDatepickerComponent;

	constructor() {}

	ngOnInit() {}

	get showError() {
		if (this.datepicker) {
			return this.datepicker.showError;
		} else {
			return false;
		}
	}

	onChange(event) {
		console.log(event);
	}
}
