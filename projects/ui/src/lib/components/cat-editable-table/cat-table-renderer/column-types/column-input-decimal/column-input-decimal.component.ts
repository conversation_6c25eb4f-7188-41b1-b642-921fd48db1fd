import {
	AfterViewInit,
	Component,
	ElementRef,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	ViewChild,
} from "@angular/core";
import {
	ControlContainer,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { CurrencyMaskConfig } from "ngx-currency";

@Component({
	selector: "pacto-column-input-decimal",
	templateUrl: "./column-input-decimal.component.html",
	styleUrls: ["./column-input-decimal.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: ColumnInputDecimalComponent,
			multi: true,
		},
	],
})
export class ColumnInputDecimalComponent
	implements ControlValueAccessor, OnInit, AfterViewInit, OnChanges
{
	@Input() formControlName: string;
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective;
	@Input() formControl: FormControl;
	@Input() id: string;
	@Input() placeholder = "";
	@Input() errorMsg = null;
	@Input() disabledControl = false;
	@Input() enableClearInput = true;
	@Input() decimalPrecision = 2;
	@Input() min: number;
	@Input() max: number;
	@Input() type: "number" | "decimal" = "decimal";
	maskOptions: CurrencyMaskConfig;

	get control(): any {
		return (
			this.formControl ||
			this.controlContainer.control.get(this.formControlName)
		);
	}

	constructor(private controlContainer: ControlContainer) {}

	ngOnInit() {
		this.setUpId();
	}

	ngAfterViewInit() {}

	ngOnChanges(changes: SimpleChanges) {
		this.decimalPrecision = this.type === "number" ? 0 : this.decimalPrecision;
		if (this.disabledControl) {
			this.control.disable();
		} else {
			this.control.enable();
		}
		this.maskOptions = {
			align: "right",
			allowNegative: false,
			allowZero: false,
			nullable: false,
			precision: this.decimalPrecision,
			suffix: "",
			decimal: ",",
			thousands: ".",
			prefix: "",
			min: this.min,
			max: this.max,
		};
	}

	get disabled() {
		return this.formControl.disabled;
	}

	get isInvalid() {
		return (
			this.formControl && this.formControl.touched && !this.formControl.valid
		);
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}

	get showClearInput() {
		if (this.formControl) {
			return (
				this.enableClearInput &&
				this.control.value &&
				!this.showError &&
				!this.disabled
			);
		}
	}

	clearHandler() {
		this.formControl.setValue("");
	}

	private setUpId() {
		if (!this.id) {
			const rdn = Math.trunc(Math.random() * 1000);
			this.id = `input-component-${rdn}`;
		}
	}

	registerOnTouched(fn: any): void {
		// use the acessor of currencyMask
	}

	registerOnChange(fn: any): void {
		// use the acessor of currencyMask
	}

	writeValue(obj: any): void {
		// use the acessor of currencyMask
	}

	onFocus(event) {
		if (event.target.setSelectionRange) {
			setTimeout(() => {
				event.target.setSelectionRange(
					event.target.value.length * 2,
					event.target.value.length * 2
				);
			}, 1);
		}
	}
}
