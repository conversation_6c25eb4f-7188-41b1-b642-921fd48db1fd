import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Optional,
	Output,
	Self,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	ControlContainer,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
	NgControl,
} from "@angular/forms";

@Component({
	selector: "pacto-column-checkbox",
	templateUrl: "./column-checkbox.component.html",
	styleUrls: ["./column-checkbox.component.scss"],
})
export class ColumnCheckboxComponent implements OnInit, ControlValueAccessor {
	@Input() formControlName: string;
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective;
	@Input() formControl: FormControl;
	@Input() label: string;
	@Input() id: string;
	@Input() checked: boolean;
	@Output() changeValue: EventEmitter<any> = new EventEmitter<any>();
	@Input() disabledControl = false;

	get control(): any {
		return (
			this.formControl ||
			this.controlContainer.control.get(this.formControlName)
		);
	}

	constructor(
		@Self()
		@Optional()
		private ngControl: NgControl,
		@Optional()
		private controlContainer: ControlContainer
	) {
		if (ngControl) {
			ngControl.valueAccessor = this;
		}
	}

	ngOnInit() {
		if (this.disabledControl) {
			this.control.disable();
		} else {
			this.control.enable();
		}
		this.control.valueChanges.subscribe((value) =>
			this.changeValue.emit(value)
		);
	}

	registerOnTouched(fn: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.registerOnTouched(fn);
		}
	}

	registerOnChange(fn: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.registerOnChange(fn);
		}
	}

	writeValue(obj: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.writeValue(obj);
		}
	}

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
		}
	}
}
