<div class="aux-wrapper">
	<input
		#input
		[formControl]="control"
		[max]="max ? max.toString() : ''"
		[maxlength]="maxlength ? maxlength.toString() : ''"
		[min]="min ? min.toString() : ''"
		[ngClass]="{ error: showError }"
		[placeholder]="placeholder"
		[textMask]="inputTextMask ? inputTextMask : { mask: false }"
		[type]="type"
		id="{{ id }}" />
	<i [hidden]="!showError" class="pct pct-alert-triangle error-icon"></i>
	<i (click)="clearHandler()" [hidden]="!showClearInput" class="pct pct-x"></i>
</div>
<div *ngIf="showError" class="pct-error-msg">
	<span>{{ errorMsg }}</span>
</div>
