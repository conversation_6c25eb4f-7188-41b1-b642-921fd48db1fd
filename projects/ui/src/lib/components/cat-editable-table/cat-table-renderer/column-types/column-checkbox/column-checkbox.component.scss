@import "projects/ui/assets/import.scss";

.cat-input-wrapper {
	position: relative;
	line-height: 25px;

	label {
		margin: 0px;
		@extend .type-p-small;
		color: $pretoPri;
		cursor: pointer;
	}

	.text {
		padding-left: 25px;
	}

	input[type="checkbox"] {
		position: absolute;
		left: -10000px;
		top: auto;
		width: 1px;
		height: 1px;
		overflow: hidden;
	}

	input[type="checkbox"] ~ .checkmark {
		position: absolute;
		top: 4px;
		display: none;
		width: 18px;
	}

	input[type="checkbox"] ~ .checkmark-outline {
		width: 18px;
		top: 4px;
		position: absolute;
		display: block;
	}

	input[type="checkbox"] ~ .checkmark-outline-cut {
		width: 18px;
		top: 4px;
		position: absolute;
		display: none;
	}

	input[type="checkbox"]:checked ~ .checkmark {
		display: block;
		top: 4px;
	}

	input[type="checkbox"]:checked ~ .checkmark-outline {
		display: none;
		top: 4px;
	}

	input[type="checkbox"]:checked ~ .checkmark-outline-cut {
		display: block;
		top: 4px;
	}
}
