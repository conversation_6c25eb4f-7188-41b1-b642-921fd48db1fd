@import "projects/ui/assets/import.scss";

input.error:focus {
	box-shadow: 0 0 0 0.2rem rgba($hellboyPri, 0.5);
}

input[type="number"] {
	-moz-appearance: textfield;
	-webkit-appearance: textfield;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

input:not(:disabled) + i.pct {
	cursor: pointer;
}

.nome {
	@extend .type-h6;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}

.pct-error-msg {
	@extend .type-caption;
	color: $hellboyPri;
	margin-top: 5px;
	line-height: 2em;
	min-height: 24px;
	padding-left: 5px;
}

.aux-wrapper {
	position: relative;
}

i.pct {
	position: absolute;
	font-size: 18px;
	right: 14px;
	top: 12px;

	&.error-icon {
		color: $hellboyPri;
	}
}

::placeholder {
	color: $cinzaClaro02;
}
