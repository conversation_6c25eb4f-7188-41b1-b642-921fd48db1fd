<div
	ngbDropdown
	class="d-inline-block"
	[placement]="'bottom-right'"
	[autoClose]="'outside'">
	<button
		i18n="@@share-button:compartilhar"
		class="btn pacto-primary"
		[ngClass]="buttonClasses"
		[id]="idSuffix ? 'relatorio-exportar-' + idSuffix : 'relatorio-exportar'"
		ngbDropdownToggle>
		<span class="btn-share-label">Compartilhar</span>
		<span class="icon-drop">
			<i class="pct pct-chevron-down icon-drop-arrow"></i>
		</span>
	</button>
	<div
		ngbDropdownMenu
		aria-labelledby="relatorio-exportar"
		class="exportar-dropdown">
		<div id="contentShare" class="content-share">
			<div class="options-share">
				<label
					[id]="
						idSuffix
							? 'option-share-pdf-label-' + idSuffix
							: 'option-share-pdf-label'
					"
					[for]="idSuffix ? 'option-share-pdf-' + idSuffix : 'option-share-pdf'"
					(click)="onTipoChange('PDF')">
					<input
						[id]="
							idSuffix ? 'option-share-pdf-' + idSuffix : 'option-share-pdf'
						"
						value="PDF"
						[(ngModel)]="tipo"
						name="optshare"
						type="radio" />
					<i
						class="pct pct-circle options-share-radio"
						*ngIf="tipo !== 'PDF'"></i>
					<i
						class="pct pct-circle-filled options-share-radio"
						*ngIf="tipo === 'PDF'"></i>
					<i class="pct pct-file"></i>
					<span>PDF</span>
				</label>
				<label
					[id]="
						idSuffix
							? 'option-share-excel-label-' + idSuffix
							: 'option-share-excel-label'
					"
					[for]="
						idSuffix ? 'option-share-excel-' + idSuffix : 'option-share-excel'
					"
					(click)="onTipoChange('XLS')">
					<input
						value="XLS"
						[(ngModel)]="tipo"
						[id]="
							idSuffix ? 'option-share-excel-' + idSuffix : 'option-share-excel'
						"
						name="optshare"
						type="radio" />
					<i
						class="pct pct-circle options-share-radio"
						*ngIf="tipo !== 'XLS'"></i>
					<i
						class="pct pct-circle-filled options-share-radio"
						*ngIf="tipo === 'XLS'"></i>
					<i class="pct pct-file-text"></i>
					<span>Excel</span>
				</label>
				<label
					[id]="
						idSuffix
							? 'option-share-google-label-' + idSuffix
							: 'option-share-google-label'
					"
					[for]="
						idSuffix ? 'option-share-google-' + idSuffix : 'option-share-google'
					"
					(click)="onTipoChange('GOOGLE')">
					<input
						value="GOOGLE"
						[(ngModel)]="tipo"
						[id]="
							idSuffix
								? 'option-share-google-' + idSuffix
								: 'option-share-google'
						"
						name="optshare"
						type="radio" />
					<i
						class="pct pct-circle options-share-radio"
						*ngIf="tipo !== 'GOOGLE'"></i>
					<i
						class="pct pct-circle-filled options-share-radio"
						*ngIf="tipo === 'GOOGLE'"></i>
					<i class="pct pct-layout"></i>
					<span>Google Sheets</span>
				</label>
			</div>

			<ds3-diviser></ds3-diviser>

			<div class="links">
				<button
					ds3-text-button
					size="sm"
					(click)="executarAcao('copy')"
					[id]="idSuffix ? 'share-copy-link-' + idSuffix : 'share-copy-link'">
					{{ "compartilharBtn.copiarLink" | translate }}
				</button>
				<button
					ds3-text-button
					size="sm"
					*ngIf="tipo === 'PDF'"
					(click)="executarAcao('print')"
					[id]="idSuffix ? 'share-copy-link-' + idSuffix : 'share-copy-link'">
					{{ "compartilharBtn.imprimir" | translate }}
				</button>
				<button
					ds3-text-button
					size="sm"
					(click)="executarAcao('save')"
					[id]="
						idSuffix ? 'share-save-archive-' + idSuffix : 'share-save-archive'
					">
					<ng-container *ngIf="tipo !== 'GOOGLE'">
						{{ "compartilharBtn.salvarArquivo" | translate }}
					</ng-container>
					<ng-container *ngIf="tipo === 'GOOGLE'">
						{{ "compartilharBtn.abrir" | translate }}
					</ng-container>
				</button>
			</div>

			<div class="sends">
				<form [formGroup]="form">
					<div class="send">
						<div class="send-item">
							<ds3-form-field>
								<ds3-field-label>
									{{ "compartilharBtn.enviarWhatsapp" | translate }}
								</ds3-field-label>
								<input
									ds3Input
									#inputFone
									[id]="
										idSuffix
											? 'share-input-fone-' + idSuffix
											: 'share-input-fone'
									"
									[placeholder]="'compartilharBtn.numeroWhatsapp' | translate"
									type="text"
									formControlName="whatsApp"
									[value]="form.get('whatsApp').value | phoneMask"
									maxlength="15"
									(input)="onInputChange($event)" />
								<button
									ds3Suffix
									class="send-button"
									[id]="
										idSuffix ? 'share-send-fone-' + idSuffix : 'share-send-fone'
									"
									(click)="executarAcao('whatsapp')"
									[class.disabled]="form.get('whatsApp').invalid">
									<i class="pct pct-send"></i>
								</button>
							</ds3-form-field>
						</div>
						<div class="send-item">
							<ds3-form-field>
								<ds3-field-label>
									{{ "compartilharBtn.enviarEmail" | translate }}
								</ds3-field-label>
								<input
									ds3Input
									#inputEmail
									[id]="
										idSuffix
											? 'share-input-email-' + idSuffix
											: 'share-input-email'
									"
									[placeholder]="'compartilharBtn.emailEnvio' | translate"
									type="email"
									formControlName="email" />
								<button
									ds3Suffix
									class="send-button"
									[id]="
										idSuffix
											? 'share-send-email-' + idSuffix
											: 'share-send-email'
									"
									(click)="executarAcao('email')"
									[class.disabled]="form.get('email').invalid">
									<i
										class="pct pct-send"
										[ngClass]="{
											'error-icon':
												form.get('email').touched && form.get('email').invalid
										}"></i>
								</button>
							</ds3-form-field>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<!--
<iframe id="dynamicIframe" style="display: none;"></iframe>
<script>
    window.onload = function() {
        window.print();
    };
</script> -->
