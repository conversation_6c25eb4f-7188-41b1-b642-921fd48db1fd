import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
	name: "phoneMask",
})
export class PhoneMaskPipe implements PipeTransform {
	transform(value: string): string {
		if (!value) {
			return value;
		}

		const phoneNumber = value.replace(/\D/g, "");

		if (phoneNumber.length > 10) {
			return phoneNumber.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
		} else {
			return phoneNumber.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
		}
	}
}
