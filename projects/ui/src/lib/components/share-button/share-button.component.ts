import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormBuilder,
	FormGroup,
	ValidatorFn,
	Validators,
} from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "sdk";
import { ShareService } from "./share.service";

@Component({
	selector: "pacto-share-button",
	templateUrl: "./share-button.component.html",
	styleUrls: ["./share-button.component.scss"],
})
export class ShareButtonComponent implements OnInit, OnChanges {
	@Input() idSuffix;
	@Input() titulo: TemplateRef<any> | string;
	@Input() endpoint: string;
	@Input() columns: any;
	@Input() filtros: any;
	@Input() filterConfig: any;
	@Input() total: number;
	@Input() telaId: string;
	@Input() sizeSm = false;
	@Input() sessionService: any;
	@Input() table = false;

	@ViewChild("inputFone", { static: false }) inputFone;
	@ViewChild("inputEmail", { static: false }) inputEmail;

	link: string;
	tipo = "PDF";
	loading = false;
	fileName: string;
	buttonClasses: any;
	form: FormGroup;

	constructor(
		private service: ShareService,
		private notificationService: SnotifyService,
		private sdkSessionService: SessionService,
		private translateService: TranslateService,
		private fb: FormBuilder,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		if (!this.sessionService) {
			this.sessionService = this.sdkSessionService;
		}
		this.form = this.fb.group({
			whatsApp: [
				"",
				[Validators.required, customLengthValidator([10, 14, 15])],
			],
			email: ["", [Validators.required, Validators.email]],
		});
		if (!this.temPermissaoExportar()) {
			document.body.classList.add("disable-print");
		} else {
			document.body.classList.remove("disable-print");
		}
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.table || changes.sizeSm) {
			this.buttonClasses = {
				"btn-filter": this.table,
				sm: this.sizeSm,
				lg: !this.sizeSm,
			};
		}
	}

	onTipoChange(value) {
		this.tipo = value;
		this.cd.detectChanges();
	}

	executarAcao(acao: string): void {
		this.exportar(acao);
	}

	onInputChange(event: any) {
		const inputValue = event.target.value;
		event.target.value = inputValue.replace(/[^0-9]/g, "");
	}

	get tituloString(): string {
		try {
			if (
				this.titulo &&
				typeof this.titulo === "string" &&
				this.titulo.length > 0
			) {
				return this.titulo as string;
			}
			const strings = this.endpoint.split("/");
			return strings[strings.length - 1];
		} catch (e) {
			return "";
		}
	}

	get colunasExportar() {
		if (this.columns) {
			const colunasExportar = new Array();
			this.columns.forEach((col) => {
				if (
					(col.mostrarTitulo && col.visible === true) ||
					(col.visible === false &&
						col.exportNoVisible === true &&
						this.tipo === "XLS")
				) {
					let titulo = null;
					if (col.titulo) {
						if (col.titulo instanceof TemplateRef) {
							if (col.tituloShare) {
								titulo = col.tituloShare;
							}
						} else {
							titulo = col.titulo;
						}
					}
					if (col.date) {
						colunasExportar.push({
							titulo,
							campo: col.campo,
							date: col.date,
						});
					} else if (col.dateTime) {
						colunasExportar.push({
							titulo,
							campo: col.campo,
							dateTime: col.dateTime,
						});
					} else if (col.decimal) {
						colunasExportar.push({
							titulo,
							campo: col.campo,
							decimal: col.decimal,
							decimalPrecision: col.decimalPrecision,
						});
					} else {
						colunasExportar.push({
							titulo,
							campo: col.campo,
						});
					}
				}
			});
			return colunasExportar;
		}
		return [];
	}

	configsFiltros() {
		if (this.filterConfig && this.filterConfig.filters) {
			const cfgs = new Array();
			this.filterConfig.filters.forEach((cfg) => {
				if (cfg.name && cfg.options) {
					cfgs.push({
						name: cfg.name,
						options: cfg.options,
					});
				}
			});
			return cfgs;
		}
		return [];
	}

	exportar(destino): void {
		if (!this.temPermissaoExportar() && this.telaId) {
			this.notificationService.error(
				`${this.translateService.instant(
					"relatorio.exportar.semPermissao"
				)} 9.98 - Permitir exportar dados`
			);
			return;
		}
		if (this.tipo === "GOOGLE" && this.total && this.total > 5000) {
			this.notificationService.error(
				this.translateService.instant("relatorio.tabela5KRegistros")
			);
			return;
		}

		const dados: any = {
			endpoint: this.endpoint,
			filtros: this.filtros,
			titulo: this.tituloString,
			filterConfig: this.configsFiltros(),
			colunas: this.colunasExportar,
			destino,
			telaId: this.telaId,
			tipo: this.tipo,
		};

		if (destino === "copy") {
			this.copyComAcaoDireta(dados);
			return;
		}

		if (destino === "email") {
			dados.email = this.inputEmail.nativeElement.value;
			this.service.send(dados).subscribe((retorno) => {
				this.notificationService.success(
					this.translateService.instant("relatorio.emailEnviadoSucesso")
				);
			});
		} else {
			this.service.share(dados).subscribe(
				(retorno) => {
					this.link = retorno.content.url;
					this.fileName = this.extractFilenameFromUrl(this.link);
					console.log(this.fileName);
					if (destino === "whatsapp") {
						const valorInserido = this.inputFone.nativeElement.value;
						const valorInput = Number(
							valorInserido
								.replace("(", "")
								.replace(")", "")
								.replace(" ", "")
								.replace("-", "")
						);
						const target =
							"https://api.whatsapp.com/send?phone=" +
							"55" +
							valorInput +
							"&text=" +
							retorno.content.msg;
						window.open(target, "_blank");
					} else if (destino === "print") {
						window.open(this.link);
					} else if (this.tipo === "PDF" || this.tipo === "XLS") {
						fetch(this.link)
							.then((response) => response.blob()) // Convertendo a resposta em Blob
							.then((blob) => {
								const a = window.document.createElement("a");
								a.href = window.URL.createObjectURL(blob);
								this.extractFilenameFromUrl(this.link);
								a.download = this.fileName;
								document.body.appendChild(a); // Adicionar o 'a' ao DOM
								a.click(); // Simular um clique no 'a' para abrir a caixa de diálogo de salvar como
								document.body.removeChild(a); // Remover o link do DOM
							})
							.catch((error) =>
								console.error(
									"Erro ao abrir caixa de diálogo de salvar como:",
									error
								)
							);
					} else {
						window.open(this.link, "_blank");
					}
				},
				(httpError) => {
					if (httpError.error.meta && httpError.error.meta.error) {
						this.notificationService.error(httpError.error.meta.message);
					} else {
						this.notificationService.error(
							this.translateService.instant("relatorio.shareErrorConnection")
						);
					}
				}
			);
		}
	}

	private temPermissaoExportar() {
		if (!this.sessionService) {
			throw Error(
				"SessionService not found. Importe do sdk ou @base-core/rest/rest.model (em caso do treino)"
			);
		}
		const usaZW = this.sessionService.modulosHabilitados.includes("ZW");

		if (!usaZW) {
			return true;
		}
		return (
			this.sessionService.temPermissaoAdm &&
			this.sessionService.temPermissaoAdm("9.98")
		);
	}

	copyMessage(msg) {
		if (navigator.clipboard && window.isSecureContext && document.hasFocus()) {
			navigator.clipboard
				.writeText(msg)
				.then(() => {
					this.showSuccess();
				})
				.catch((err) => {
					console.error("Clipboard API failed", err);
					this.fallbackCopy(msg);
				});
		} else {
			this.fallbackCopy(msg);
		}
	}

	fallbackCopy(text) {
		const textarea = document.createElement("textarea");
		textarea.value = text;
		textarea.style.position = "fixed";
		textarea.style.opacity = "0";
		textarea.setAttribute("readonly", "true");
		textarea.setAttribute("aria-hidden", "true");
		document.body.appendChild(textarea);
		textarea.focus();
		textarea.select();
		try {
			const successful = document.execCommand("copy");
			if (!successful) {
				this.notificationService.error("Falha ao copiar o link!");
				return;
			}
		} catch (err) {
			this.notificationService.error("Falha ao copiar o link!");
			console.error("execCommand failed", err);
			return;
		}
		document.body.removeChild(textarea);
		this.showSuccess();
	}

	private showSuccess() {
		this.notificationService.success(
			this.translateService.instant("relatorio.linkCopiado")
		);
	}

	async delay(ms: number) {
		await new Promise((resolve) => setTimeout(() => resolve(), ms)).then();
	}

	extractFilenameFromUrl = (url: string): string | null => {
		const extensionRegex = /\/([^\/]+)\.(pdf|xls|xlsx)$/; // Expressão regular para extrair o nome do arquivo com extensão PDF, XLS ou XLSX
		const matches = extensionRegex.exec(url); // Executar a expressão regular no URL

		if (matches && matches.length > 1) {
			const filenameWithExtension = matches[1]; // O nome do arquivo com extensão estará na segunda posição do array de correspondências
			return filenameWithExtension;
		} else {
			return null;
		}
	};

	copyComAcaoDireta(dados): void {
		this.service.share(dados).subscribe(
			(retorno) => {
				const link = retorno.content.url;
				this.link = link;
				this.fileName = this.extractFilenameFromUrl(link);

				this.copyMessage(link);
			},
			(error) => {
				this.notificationService.error(
					this.translateService.instant("relatorio.shareErrorConnection")
				);
			}
		);
	}
}

export function customLengthValidator(lengths: number[]): ValidatorFn {
	return (control: AbstractControl): { [key: string]: any } | null => {
		const value = control.value as string;
		if (!value) {
			return null;
		}
		const isValidLength = lengths.some((length) => value.length === length);
		return isValidLength ? null : { customLength: { value: control.value } };
	};
}
