@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

// Defina suas variáveis
$primary-color: $azulim05;
$primary-text-color: $azul;
$disabled-color: #797d86 !important;

.pacto-primary {
	background-color: $primary-color;
	color: white;
	margin-left: 24px;
}

.btn.pacto-primary.btn-filter {
	background-color: $branco;
	border: 1px solid $actionDefaultAble04;
	color: $actionDefaultAble04;
	height: 40px;

	&:hover,
	&:focus {
		background-color: $branco;
		border: 1px solid $actionDefaultAble04;
		color: $actionDefaultAble04;
	}

	&:active {
		background-color: $branco;
		border: 1px solid $actionDefaultAble04;
		color: $actionDefaultAble04;
	}

	.icon-drop {
		border-left: 1px solid $actionDefaultAble04;
	}
}

.exportar-dropdown {
	padding: 0;
}

.content-share {
	width: 440px;
	padding: 16px;

	.options-share {
		display: flex;
		align-items: center;
		padding-bottom: 16px;
		justify-content: space-between;

		i {
			color: $typeDefaultText;
		}

		input {
			display: none;
		}

		.options-share-radio {
			color: $actionDefaultAble04;
		}

		span {
			font-size: 14px;
			color: $typeDefaultText;
		}

		label {
			display: flex;
			align-items: center;
			gap: 8px;
			margin-bottom: 0;
		}
	}
	.links {
		display: flex;
		padding: 16px 0 24px;
		justify-content: space-between;

		.link {
			display: flex;
			color: $primary-text-color;
			cursor: pointer;
			padding: 10px;
			border-radius: 6px;
			white-space: nowrap;

			&:hover {
				background-color: #eff2f7;
			}

			i {
				margin-right: 6px;
				color: $primary-text-color;
			}
		}
	}

	.sends {
		.send {
			color: #a1a5aa;

			label {
				margin-bottom: 8px;
				color: $typeDefaultText;
			}

			.send-item:not(:last-child) {
				margin-bottom: 16px;
			}

			.send-button {
				height: 16px;
				border: none;
				background: transparent;
				color: $actionDefaultAble04;
				font-size: 16px;
				outline: none;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			ds3-form-field {
				&::ng-deep {
					.counter {
						display: none;
					}
				}
			}
		}
	}
}

.dropdown-toggle::after {
	display: none;
}

.btn.pacto-primary {
	position: relative;

	&:not(.novo-botao) {
		padding-right: 35px;
	}

	.btn-share-label {
		margin-right: 10px;
	}

	.icon-drop {
		position: absolute;
		top: 0;
		right: 0;
		width: 35px;
		height: 100%;
		line-height: 38px;
		text-align: center;
		border-left: 1px solid white;
		display: flex;
		justify-content: center;
		align-items: center;

		.icon-drop-arrow {
			transition: {
				property: transform;
				duration: 0.5s;
			}
		}
	}
}

.show.dropdown {
	.btn.pacto-primary {
		.icon-drop {
			.icon-drop-arrow {
				transform: rotate(180deg);
			}
		}
	}
}

.has-error input {
	border-color: red;
	/* Adicione uma borda vermelha */
	box-shadow: none;
}

.disabled {
	cursor: not-allowed;
	/* Altera o cursor para "não permitido" */
	opacity: 0.5;
	/* Reduz a opacidade para indicar que está desativado */
	pointer-events: none;
	/* Impede que o elemento receba eventos de mouse */

	.pct-send {
		color: $disabled-color;
		/* Cor padrão para o ícone quando desabilitado */
	}
}

.form-control:focus {
	box-shadow: none;
}

.lg {
	box-shadow: unset;
	@extend .pct-btn-default1;
	max-height: 40px;
	padding-top: 13px;
	padding-bottom: 13px;
	padding-left: 20px;

	.btn-share-label {
		margin-right: 20px;
	}
}

.sm {
	box-shadow: unset;
	@extend .pct-btn-default2;
	max-height: 32px;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 16px;

	.btn-share-label {
		margin-right: 16px;
	}
}
