import {
	Component,
	ContentChild,
	Directive,
	HostBinding,
	Input,
	OnInit,
	TemplateRef,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "cat-report-detail-title",
	template: `
		<ng-content></ng-content>
	`,
	styleUrls: ["./report-detail.component.scss"],
})
export class ReportDetailTitleComponent {
	@HostBinding("class")
	titleClass = "dtlh-simples-title";
}

@Component({
	selector: "cat-report-detail-item-title",
	template: `
		<ng-content></ng-content>
	`,
	styleUrls: ["./report-detail.component.scss"],
})
export class ReportDetailItemTitleComponent {
	@HostBinding("class")
	titleClass = "dtlh-simples-item-title";

	constructor() {}
}

@Component({
	selector: "cat-report-detail-item-value",
	template: `
		<ng-content></ng-content>
	`,
	styleUrls: ["./report-detail.component.scss"],
})
export class ReportDetailItemValueComponent implements OnInit {
	@HostBinding("class")
	valueClass = "dtlh-simples-item-value";
	@Input() isBoolean = false;
	@Input() booleanValue: boolean;

	constructor() {}

	ngOnInit() {
		if (this.isBoolean) {
			if (typeof this.booleanValue === "string") {
				this.booleanValue = /true/i.test(this.booleanValue);
			}
			this.valueClass += ` report-item-value-boolean ${
				this.booleanValue
					? "report-item-value-active"
					: "report-item-value-inactive"
			}`;
		}
	}
}

@Component({
	selector: "cat-report-detail-item",
	template: `
		<ng-content select="cat-report-detail-item-title"></ng-content>
		<ng-content select="cat-report-detail-item-value"></ng-content>
	`,
	styleUrls: ["./report-detail.component.scss"],
})
export class ReportDetailItemComponent implements OnInit {
	@HostBinding("class")
	itemClass = "dtlh-simples-item";

	@Input() class;
	@Input() occupeAllRow = false;

	@ContentChild(ReportDetailItemTitleComponent, { static: false })
	itemTitle: ReportDetailItemTitleComponent;
	@ContentChild(ReportDetailItemValueComponent, { static: false })
	itemValue: ReportDetailItemValueComponent;

	ngOnInit() {
		if (!this.occupeAllRow) {
			this.itemClass += " col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3";
		} else {
			this.itemClass += " col-12";
		}
		if (this.class) {
			this.itemClass += ` ${this.class}`;
		}
	}
}

@Component({
	selector: "cat-report-detail-row",
	template: `
		<ng-content select="cat-report-detail-item"></ng-content>
	`,
	styleUrls: ["./report-detail.component.scss"],
})
export class ReportDetailRowComponent implements OnInit {
	@HostBinding("class")
	rowClass = "dtlh-simples-row";

	@Input() class;

	ngOnInit() {
		if (this.class) {
			this.rowClass += ` ${this.class}`;
		}
	}
}

@Component({
	selector: "cat-report-detail",
	templateUrl: "./report-detail.component.html",
	styleUrls: ["./report-detail.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class ReportDetailComponent implements OnInit {
	constructor() {}

	ngOnInit() {}
}
