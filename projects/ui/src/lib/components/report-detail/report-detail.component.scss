@import "projects/ui/assets/import.scss";

.dtlh-simples-content {
	background-color: #fafafa;
	padding: 16px;

	.dtlh-simples-title {
		display: block;
		color: $pretoPri;
		font-weight: 700;
		font-size: 14px;
		line-height: 14px;
		padding-bottom: 16px;
		border-bottom: 1px solid #c4c4c4;
	}

	.dtlh-simples-row {
		padding: 0 1px;
		display: flex;
		flex-wrap: wrap;
		border-bottom: 1px dashed $cinza02;

		&:last-child {
			border-bottom: unset;
		}

		.dtlh-simples-item {
			display: block;
			font-size: 14px;
			color: $pretoPri;
			margin-top: 7px;
			margin-bottom: 7px;

			.dtlh-simples-item-title {
				display: block;
				font-weight: 700;
			}

			.dtlh-simples-item-value {
				display: block;
				font-weight: 400;
				margin-top: 10px;

				&.report-item-value-boolean {
					margin-top: 6px;
					width: 86px;
					height: 24px;
					display: flex;
					font-size: 12px;
					justify-content: center;
					align-items: center;
					color: $branco;
					border-radius: 100px;
					font-weight: 700;

					&.report-item-value-active {
						background-color: $verdinho05;
					}

					&.report-item-value-inactive {
						background-color: $hellboy05;
					}
				}
			}
		}
	}
}
