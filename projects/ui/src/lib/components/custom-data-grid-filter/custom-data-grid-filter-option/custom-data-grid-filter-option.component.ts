import {
	Component,
	Directive,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";

@Directive({
	selector: "[pactoDataGridFilterOptionBody]",
})
export class PactoDataGridFilterOptionBody {}

@Component({
	selector: "pacto-custom-data-grid-filter-option",
	templateUrl: "./custom-data-grid-filter-option.component.html",
	styleUrls: ["./custom-data-grid-filter-option.component.scss"],
})
export class CustomDataGridFilterOptionComponent implements OnInit {
	@Output() clearFilter: EventEmitter<any> = new EventEmitter<any>();
	@Input()
	showClearFilter: boolean = false;
	expand: boolean = false;

	constructor() {}

	ngOnInit() {}

	filterClick() {
		this.expand = !this.expand;
	}

	clear(event: MouseEvent) {
		event.stopPropagation();
		this.clearFilter.emit();
	}
}
