<div (click)="filterClick()" class="filter-title">
	<span class="aux-wrapper">
		<span [ngClass]="{ 'filter-expanded': expand }" class="icon-wrapper">
			<i class="pct pct-caret-right"></i>
		</span>
		<ng-content></ng-content>
	</span>
	<span (click)="clear($event)" *ngIf="showClearFilter" class="clear-filter">
		{{ "filter.limpar" | translate }}
	</span>
</div>
<div [ngClass]="{ 'filter-expanded': expand }" class="filter-content">
	<ng-content select="[pactoDataGridFilterOptionBody]"></ng-content>
</div>
