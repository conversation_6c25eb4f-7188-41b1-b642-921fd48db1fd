.filter-title {
	color: #5a5a5a;
	font-size: 15px;
	line-height: 20px;
	position: relative;
	font-weight: bold;
	border-top: 1px solid #e6e6e6;
	height: 40px;
	display: flex;
	align-items: center;
	width: 100%;
	cursor: pointer;

	.clear-filter {
		display: inline-block;
		font-weight: bold;
		font-size: 12px;
		margin-left: auto;
	}

	.aux-wrapper {
		display: block;
		cursor: pointer;
	}

	.icon-wrapper {
		display: inline-flex;
		justify-content: center;
		align-items: center;
		padding-right: 5px;

		i.pct {
			transition: {
				property: transform;
				duration: 0.5s;
			}
		}

		&.filter-expanded {
			i.pct {
				transform: rotate(90deg);
			}
		}
	}

	&:hover {
		background-color: rgb(245, 245, 245);
	}
}

.filter-content {
	display: none;

	&.filter-expanded {
		display: block;
	}
}
