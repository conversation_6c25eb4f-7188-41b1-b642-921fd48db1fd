<div
	#filterDropdown="ngbDropdown"
	[autoClose]="false"
	[placement]="'bottom-right'"
	class="d-inline-block"
	ngbDropdown>
	<button
		#filterToggleButton
		class="btn btn-primary btn-icon"
		ngbDropdownToggle>
		<ng-container *ngIf="toggleContent; else defaultToggleContent">
			<ng-content select="[pactoDataGridFilterToggleContent]"></ng-content>
		</ng-container>
		<ng-template #defaultToggleContent>
			<i class="pct pct-filter"></i>
		</ng-template>
		<span class="icon-drop">
			<i class="pct pct-chevron-down icon-drop-arrow"></i>
		</span>
	</button>

	<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
		<ng-content></ng-content>
		<div class="pacto-filter-actions-row">
			<ng-content select="[pactoDataGridFilterActions]"></ng-content>
		</div>
	</div>
</div>
<button
	(click)="filterDropdown.close()"
	[hidden]="true"
	id="closeButtonDropdown"></button>
