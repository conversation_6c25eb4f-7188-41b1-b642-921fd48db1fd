@import "projects/ui/assets/scss/cores.vars";

:host {
	.dropdown-toggle::after {
		display: none;
	}

	.btn.btn-primary {
		.icon-drop {
			width: 16px;
			height: 16px;
			font-size: 16px;
			display: inline-flex;
			justify-content: center;
			align-items: center;
		}

		&:hover,
		&:focus {
			background: $azulim05;
			border: 1px solid $azulim05;
		}

		&:active {
			background: $azulim05;
			border: 1px solid $azulim05;
		}
	}

	.icon-drop {
		.icon-drop-arrow {
			transition: {
				property: transform;
				duration: 0.5s;
			}
		}
	}

	.show.dropdown {
		.btn-icon {
			.icon-drop {
				.icon-drop-arrow {
					transform: rotate(180deg);
				}
			}

			&.light {
				color: #b4b7bb;
				border-color: #b4b7bb;
			}
		}

		.dropdown-menu {
			padding: 16px;
			width: 350px;
		}
	}

	.pacto-filter-actions-row {
		display: flex;
		align-items: center;
		margin-top: 16px;
	}
}
