import {
	Component,
	ContentChild,
	Directive,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";

@Directive({
	selector: "[pactoDataGridFilterToggleContent]",
})
export class PactoDataGridFilterToggleContent {}

@Directive({
	selector: "[pactoDataGridFilterActions]",
})
export class PactoDataGridFilterActionsContent {}

@Component({
	selector: "pacto-custom-data-grid-filter",
	templateUrl: "./custom-data-grid-filter.component.html",
	styleUrls: ["./custom-data-grid-filter.component.scss"],
})
export class CustomDataGridFilterComponent implements OnInit {
	@ContentChild(PactoDataGridFilterToggleContent, { static: false })
	toggleContent: PactoDataGridFilterToggleContent;

	@ContentChild(PactoDataGridFilterActionsContent, { static: false })
	actions: PactoDataGridFilterActionsContent;

	@ContentChild("filterDropdown", { static: false }) filterDropdown;

	constructor() {}

	ngOnInit() {}

	close() {
		this.filterDropdown.close();
	}
}
