import { storiesOf, moduleMetadata } from "@storybook/angular";
import { FormsModule, ReactiveFormsModule, FormControl } from "@angular/forms";
import { HttpClientModule } from "@angular/common/http";
import {
	CatFormMultiSelectFilterComponent,
	CatSmoothScrollDirective,
	CatFormInputComponent,
	CatMultiSelectFilterComponent,
	CatPersonAvatarComponent,
} from "projects/ui/src/public-api";
import { TextMaskModule } from "angular2-text-mask";
import { Component, Input } from "@angular/core";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-form-multi-select-filter.stories.html",
	styleUrls: ["./cat-form-multi-select-filter.stories.scss"],
})
class HostComponent {
	@Input() endpointUrl;
	@Input() resposeParser;
	@Input() control;
	@Input() size;
	@Input() options;
	@Input() labelKey;
	@Input() label;
}

const metadata = {
	imports: [
		FormsModule,
		BrowserAnimationsModule,
		HttpClientModule,
		TextMaskModule,
		ReactiveFormsModule,
	],
	declarations: [
		CatFormMultiSelectFilterComponent,
		CatMultiSelectFilterComponent,
		CatPersonAvatarComponent,
		CatSmoothScrollDirective,
		CatFormInputComponent,
	],
};

storiesOf("Forms|Multi Select", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Normal", () => {
		return {
			component: HostComponent,
			props: {
				endpointUrl: null,
				options: [
					{ id: 1, nome: "Ryu", imageUri: "" },
					{ id: 2, nome: "Guile", imageUri: "" },
					{ id: 3, nome: "Chun-li", imageUri: "" },
					{ id: 31, nome: "M. Bison", imageUri: "" },
					{ id: 4, nome: "Ken Masters", imageUri: "" },
					{ id: 5, nome: "Sagat 1", imageUri: "" },
					{ id: 6, nome: "Sagat 2", imageUri: "" },
					{ id: 7, nome: "Sagat 3", imageUri: "" },
					{ id: 8, nome: "Sagat 4", imageUri: "" },
					{ id: 9, nome: "Sagat 5", imageUri: "" },
					{ id: 10, nome: "Sagat 6", imageUri: "" },
					{ id: 11, nome: "Sagat 7", imageUri: "" },
					{ id: 12, nome: "Sagat 8", imageUri: "" },
					{ id: 13, nome: "Sagat 9", imageUri: "" },
				],
				label: "Personagem",
				labelKey: "nome",
				imageKey: "imageUri",
				control: new FormControl(),
				resposeParser: () => {
					return [];
				},
			},
		};
	});
