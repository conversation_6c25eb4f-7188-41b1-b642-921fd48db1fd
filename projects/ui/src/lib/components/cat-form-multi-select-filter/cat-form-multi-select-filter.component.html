<input [formControl]="control" class="cat-select-filter" type="hidden" />

<div *ngIf="label" class="nome">
	<span>{{ label }}</span>
</div>

<pacto-cat-multi-select-filter
	[autocomplete]="autocomplete"
	[control]="control"
	[endpointUrl]="endpointUrl"
	[idKey]="idKey"
	[id]="id"
	[imageKey]="imageKey"
	[labelKey]="labelKey"
	[options]="options"
	[paramBuilder]="paramBuilder"
	[placeholder]="placeholder"
	[resposeParser]="resposeParser"></pacto-cat-multi-select-filter>

<div *ngIf="showError" class="pct-error-msg">
	<span>{{ errorMsg }}</span>
</div>
