import { Component, Input, ChangeDetectionStrategy } from "@angular/core";
import { FormControl } from "@angular/forms";

import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
} from "../cat-select-filter/cat-select-filter.component";

@Component({
	selector: "pacto-cat-form-multi-select-filter",
	templateUrl: "./cat-form-multi-select-filter.component.html",
	styleUrls: ["./cat-form-multi-select-filter.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatFormMultiSelectFilterComponent {
	@Input() id;
	/**
	 * Full url of endpoint without any GET params.
	 *
	 */
	@Input() endpointUrl;
	@Input() options: any[] = [];
	@Input() control: FormControl;
	@Input() paramBuilder: SelectFilterParamBuilder;
	@Input() resposeParser: SelectFilterResponseParser;
	@Input() placeholder = "-";
	@Input() label;
	@Input() idKey = "id";
	@Input() labelKey = "label";
	@Input() imageKey;
	@Input() errorMsg = null;
	@Input() disabled = false;
	@Input() autocomplete = "on";

	constructor() {}

	get isInvalid() {
		return this.control && this.control.touched && this.control.invalid;
	}

	get showError() {
		return this.isInvalid && !this.disabled;
	}
}
