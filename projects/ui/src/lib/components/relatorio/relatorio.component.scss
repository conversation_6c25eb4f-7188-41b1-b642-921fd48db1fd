@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "ds3-side-filter/ds3-side-filter.component.scss";

.table-content {
	font-size: 16px;
}

.pacto-table-title-block {
	position: relative;
	padding: 30px 30px 15px;
	display: flex;
	flex-wrap: wrap;

	.title-aux-wrapper {
		padding-bottom: 16px;
		margin-right: 30px;
		width: 100%;
	}

	.table-title {
		font-size: 16px;
		font-weight: 700;
		color: rgb(51, 51, 51);
	}

	.table-description {
		font-size: 13px;
		font-weight: rgb(165, 165, 165);
		font-weight: 300;
	}

	.search {
		position: relative;
		flex-grow: 1;

		input {
			width: 230px;
			padding-left: 30px;
		}

		i.pct {
			position: absolute;
			left: 10px;
			top: 12px;
		}
	}

	.command-buttons {
		display: flex;

		.command {
			vertical-align: top;
			line-height: 24px;
			border-radius: 4px;
			padding: 0px 5px;
			color: #a1a5aa;
			font-size: 12px;
			cursor: pointer;
			margin-right: 10px;
			background-color: #fafafa;
			border: 1px solid #a1a5aa;
			display: flex;
			align-items: center;
			padding: 6px;

			&:hover {
				background-color: rgba(243, 243, 243, 0.5);
			}

			span {
				vertical-align: top;

				&.icone {
					line-height: 29px;
					margin-right: 5px;

					i {
						font-size: 16px;
						display: flex;
						align-items: center;
					}
				}

				&.texto {
					line-height: 20px;
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	.search {
		display: none;
	}

	.pacto-table-title-block {
		justify-content: space-between;
	}
}

.table-content {
	padding: 0px 30px 30px;
	position: relative;

	&.ds3-relatorio {
		padding: 0 16px 32px;
	}
}

pacto-table-renderer {
	display: block;
	margin-top: 20px;
}

.exportar-dropdown {
	.item {
		line-height: 24px;
		padding: 5px 20px;
		cursor: pointer;

		&:hover {
			background-color: #eee;
		}
	}
}

.footer-row {
	position: relative;
	display: flex;
	margin-top: 20px;
	padding-top: 15px;
	border-top: 1px solid #f1f1f1;
	flex-direction: row-reverse;
	// TODO Correção para evitar a sobreposição de css do componente relatorio-cobranca do pactopay
	justify-content: unset !important;

	> * {
		display: block;
		margin-left: 20px;
	}

	&.label-total-el-pst {
		> * {
			margin-left: 28px;
		}
	}

	.legenda {
		display: flex;
		width: 50%;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}

	.div-show-and-select {
		display: flex;

		> * {
			margin-left: 20px;
		}

		&.label-total-el-pst {
			> * {
				margin-left: 28px;
			}
		}
	}
}

.simple-total-row {
	background-color: #f9f9f9;
	border-top: 1px solid #ededed;
	border-bottom: 1px solid #ededed;
	font-size: 14px;
	text-align: center;
	font-weight: bold;
	line-height: 30px;
	margin-top: 14px;
	color: #7f7f7f;
}

.total-values {
	line-height: 32px;
	color: #9d9d9d;
	font-weight: bold;
	font-size: 13px;

	.value {
		padding: 0px 3px;
	}
}

.margin-right-10 {
	margin-right: 10px;
}

.novo-botao {
	margin-left: unset;
}

.actions {
	display: flex;
	margin-left: auto;

	.div-actions-btn {
		display: flex;
	}
}

.dropdown-toggle::after {
	display: none;
}

.btn.btn-primary {
	position: relative;

	.icon-drop {
		position: absolute;
		top: 0;
		right: 0;
		width: 35px;
		height: 40px;
		line-height: 38px;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	&:hover,
	&:focus {
		background: $azulim05;
		border: 1px solid $azulim05;
	}

	&:active {
		background: $azulim05;
		border: 1px solid $azulim05;
	}
}

.btn-primary,
.btn.btn-primary:focus,
.show > .btn-primary.dropdown-toggle,
.btn-primary.disabled {
	background: $azulim05;
	border: 1px solid $azulim05;
	height: 40px;
}

.btn-primary.btn-filter {
	background: $branco;
	border: 1px solid $azulim05;
	color: $azulim05;

	&:hover,
	&:focus {
		background: $branco;
		border: 1px solid $azulim05;
		color: $azulim05;
	}

	&:active {
		background: $branco;
		border: 1px solid $azulim05;
		color: $azulim05;
	}
}

.btn-icon {
	padding-right: 35px;
}

.icon-drop {
	.icon-drop-arrow {
		transition: {
			property: transform;
			duration: 0.5s;
		}
	}
}

.show.dropdown {
	.icon-drop {
		.icon-drop-arrow {
			transform: rotate(180deg);
		}
	}

	&.light {
		color: #b4b7bb;
		border-color: #b4b7bb;
	}
}

.color {
	color: $cinzaPri;
}

.action-link-footer {
	margin-right: auto;
	cursor: pointer;
	color: $azulim04;
	font-size: 16px;
	font-style: normal;
	font-weight: 700;
}

// ::ng-deep #side-filter-content {
//   padding: 0px !important;
//   margin: 16px 8px;
// }

// .side-filter {
//   display: flex;
//   align-content: space-between;
//   height: 100%;
//   justify-content: flex-start;
//   align-items: center;
//   flex-direction: column;

//   .side-filter-title {
//     height: 5%;
//     width: 100%;
//     position: static;
//     display: flex;
//     align-items: baseline;
//     justify-content: space-between;
//     flex-direction: row;
//     margin-bottom: 4px;
//   }

//   .side-filter-content {
//     height: auto;
//     width: 100%;
//     overflow: auto;
//     padding: 8px 0px;

//     pacto-cat-form-input::ng-deep input,
//     pacto-cat-multi-select-filter::ng-deep .current-value {
//       border-radius: 8px;
//       border-color: var(--color-support-gray-3);
//       background-color: var(--color-background-plane-2);
//     }

//     .colunas-visiveis {
//       display: flex;
//       flex-wrap: wrap;
//       align-items: center;
//       justify-content: center;
//       flex-direction: row;
//       gap: 6px;

//       pacto-cat-button {
//         width: calc(50% - 6px);
//       }
//     }
//   }

// }

.side-filter-button {
	button {
		&.filterApplied {
			padding: 14px 20px 10px 20px !important;
		}

		&.ds3-button {
			padding: 12px 20px;
			position: relative;
		}

		.side-filter-icon .mat-badge-content {
			border: 1px solid var(--color-action-default-able-4) !important;
			color: var(--color-action-default-able-4) !important;
			background-color: transparent !important;
			top: -11px;
			right: -19px !important;
		}

		.side-filter-dropdown {
			position: absolute;
			display: flex;
			align-items: center;
			justify-content: center;
			top: 0;
			right: 0;
			height: 100%;
			width: 38px;
			border-left: 1px solid var(--color-action-default-able-4);
		}
	}
}

.div-actions-btn > div:not(:last-child) {
	margin-right: 16px;
	/* Adiciona um espaço entre as divs, exceto a última */
}

::ng-deep {
	#side-filter {
		padding: 0px !important;
	}
}

.ds3-relatorio {
	border-radius: 8px;
	background-color: #fff;

	.pacto-table-title-block {
		padding: 16px 16px 32px;

		.search {
			ds3-form-field {
				max-width: 364px;
				display: block;
			}

			i.pct {
				position: initial;
			}
		}

		pacto-share-button {
			.btn {
				padding-left: 20px;

				.btn-share-label {
					@extend .pct-btn-default1;
				}

				&.pacto-primary {
					display: flex;
					align-items: center;
					margin-left: 0;

					.btn-share-label {
						margin-right: 20px;
					}
				}
			}
		}

		pacto-log {
			margin-right: 0;

			.btn-primary {
				border-color: $azul !important;
				border: 1px solid $azul !important;
				color: $azul !important;
			}

			.btn-primary.focus,
			.btn-primary:focus {
				box-shadow: unset !important;
			}

			.btn.focus,
			.btn:focus {
				box-shadow: unset !important;
			}
		}
	}

	.table-content {
		padding: 0 16px 32px;
	}

	.table-title-wrapper {
		margin-bottom: 32px;
		width: 100%;

		ds3-diviser {
			margin-bottom: 32px;
		}

		.table-title {
			@extend .pct-title4;
		}

		.table-title,
		.table-description {
			padding: 0 16px;
		}
	}

	.overall {
		display: flex;
		margin: 0px 8px;
		flex-wrap: nowrap;
		flex-direction: row;
		padding: 8px 16px;
		border-radius: 8px;
		background-color: var(--color-background-plane-3);
		align-items: center;

		&-filters {
			gap: 8px;
			width: calc(100% - 55px);
			display: flex;
			flex-wrap: wrap;
			flex-direction: row;
			align-items: baseline;

			&-label {
				@extend .pct-title5;
				color: var(--color-support-black-0);
			}

			&-status {
				border-color: var(--color-support-grey-3);
				background-color: var(--color-background-plane-2);
			}
		}

		&-entries {
			width: 55px;

			&-label,
			&-value {
				padding-right: 4px;
				@extend .pct-display7;
				color: var(--color-support-black-0);
			}
		}
	}

	.footer-row {
		margin-top: 0;
		padding-top: 16px;
		border-top: none;
	}
}

.left-10 {
	margin-left: 10px;
}

.ds3-tooltip {
	.ds3-tooltip-content {
		padding: 8px 12px;

		.overall-tooltip {
			ds3-diviser {
				margin: 8px 0px;
			}

			.overall-tooltip-label {
				display: flex;
				justify-content: center;
			}

			.overall-tooltip-extra {
				@extend .pct-overline2;
				margin: 8px 0px;
				padding: 0px;
				text-align: center;
			}
		}
	}
}
