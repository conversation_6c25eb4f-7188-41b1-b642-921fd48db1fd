import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChild,
	ElementRef,
	EventEmitter,
	HostBinding,
	HostListener,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

import { Observable, Subscription } from "rxjs";
import { debounceTime, filter } from "rxjs/operators";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";

import {
	GridFilter,
	GridFilterConfig,
	GridFilterMany,
	GridFilterType,
} from "./data-grid-filter.model";
import { DataGridFilterComponent } from "./data-grid-filter/data-grid-filter.component";
import {
	DataFiltro,
	PactoActionConfig,
	PactoDataGridConfig,
	RelatorioExportarFormato,
} from "./data-grid.model";
import { DataGridService } from "./data-grid.service";
import { SnotifyService } from "ng-snotify";
import { Router } from "@angular/router";
import { MatDialog, MatDialogRef } from "@angular/material";
import { Ds3SideFilterComponent } from "./ds3-side-filter/ds3-side-filter.component";
import moment from "moment";
export interface TableData<T> {
	/**
	 * Size of each page.
	 */
	size?: number;
	/**
	 * Data to be displayed.
	 */
	content?: Array<T> | FormControl;
	/**
	 * Number of total elements.
	 */
	totalElements?: number;
	/**
	 * Number of current page, where zero is the first one.
	 */
	number?: number; // Zero-indexed
}

@Component({
	// tslint:disable-next-line:component-selector
	selector: "pacto-relatorio",
	templateUrl: "./relatorio.component.html",
	styleUrls: ["./relatorio.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [DataGridService],
})
export class RelatorioComponent implements OnInit, AfterViewInit {
	@Input() id = "";
	@Input() persistirFiltros = true;
	@Input() showShare = true;
	@Input() tableTitle: TemplateRef<any> | string;
	@Input() legenda: TemplateRef<any> | string;
	@Input() tableDescription: TemplateRef<any>;
	@Input() baseFilter: DataFiltro = {};
	@Input() customActions: TemplateRef<any>;
	@Input() btnAddRight: boolean = true;
	@Input() customActionsRight: TemplateRef<any>;
	@Input() filterConfig: GridFilterConfig;
	@Input() apiReturnProperty;
	@Input() emptyStateMessage;
	@Input() emptyStateDescription: string;
	@Input() customContent;
	@Input() customEmptyContent;
	@Input() alternatingColors: "first" | "second" | "none" = "none";
	@Input() actionTitulo: string;
	@Input() allowsCheck: any = {};
	@Input() dropDownActionTitulo: string;
	@Input() enableZebraStyle: boolean;
	@Input() enableDs3: boolean = false;
	@Input() enableOverall: boolean = true;
	@Input() customRows = false;
	@Input() nameFieldHeader: string;
	@Input() nameEnumHeaderField: string;
	@Input() iconEnumHeaderField: string;
	@Input() iconFieldHeader: string;
	@Input() nameFirstFieldContent: string;
	@Input() nameSecondFieldContent: string;
	@Input() iconFieldfooterleft: string;
	@Input() nameFieldfooterleft: string;
	@Input() iconFieldFirstfooterright: string;
	@Input() nameFieldFirstfooterright: string;
	@Input() nameFieldFirstObjectfooterright: string;
	@Input() iconFieldSecondfooterright: string;
	@Input() nameFieldSecondfooterright: string;
	@Input() labelTotalElementsPosition: "left" | "right" = "right";
	@Input() filterContentRef?: ElementRef<any>;
	@Input() customFormGroup?: FormGroup;
	@Input() isSideFilter: boolean;
	sideFilterRef: MatDialogRef<any, any>;
	// Id to be concatenated for all the components used by the relatorio.component
	@Input() idSuffix: string;

	@Input() table: PactoDataGridConfig = new PactoDataGridConfig({
		state: null,
		buttons: null,
		columns: [],
	});
	@Input() showBtnAdd: boolean;
	@Input() dataFetchLoading = false;
	@Input() labelBtnAdd: string;
	@Input() idBtnAdd: string = "btn-add-item";
	@Input() itensPerPage = [
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];
	@Input() labelActionLinkFooter: string;
	@Input() iconActionLinkFooter: string;
	@Input() quickSearchPlaceHolderCustom: string;
	@Input() telaId: string;
	@Input() sessionService: any;
	@Input() endpointShare: string;

	@Output() loadedData: EventEmitter<any> = new EventEmitter();
	@Output() pageChangeEvent: EventEmitter<any> = new EventEmitter();
	@Output() pageSizeChange: EventEmitter<any> = new EventEmitter();
	@Output() iconClick: EventEmitter<{
		row: any;
		iconName: string;
	}> = new EventEmitter();
	@Output() cellClick: EventEmitter<{
		row: any;
		column: any;
	}> = new EventEmitter();
	@Output() rowClick: EventEmitter<any> = new EventEmitter();
	@Output() rowCheck: EventEmitter<{
		row?: any;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}> = new EventEmitter();
	@Output() ds3SideFilterOpened: EventEmitter<{
		sideFilterRef: Ds3SideFilterComponent;
	}> = new EventEmitter<{ sideFilterRef: Ds3SideFilterComponent }>();

	@Output() btnClick: EventEmitter<any> = new EventEmitter();
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();
	@Output() filterChangeEvent: EventEmitter<any> = new EventEmitter<any>();
	@Output() sortEvent: EventEmitter<any> = new EventEmitter();
	@Output() btnAddClick: EventEmitter<any> = new EventEmitter();
	@Output() actionLinkFooterClick: EventEmitter<any> = new EventEmitter();
	@ViewChild("quickSearch", { static: false }) quickSearch;
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("dataGridFilter", { static: false })
	dataGridFilter: DataGridFilterComponent;
	@ViewChild("filterToggleButton", { static: false })
	filterToggleButton: ElementRef;
	@ViewChild("relSelectPageSize", { static: false })
	relSelectPageSize: ElementRef;
	@ViewChild("relPageTotalElements", { static: false })
	relPageTotalElements: ElementRef;
	@ViewChild("sideFilterTemplate", { static: false })
	sideFilterTemplate: TemplateRef<unknown>;
	@ContentChild("filterContent", { static: false })
	filterContent: ElementRef;
	@Input() showTooltipDS3Status: boolean = true;
	isSideFilterOpen: boolean = false;
	ngbPage;
	private dataFetchSubscription: Subscription;
	public temporaryFilters;

	pageSizeControl: FormControl = new FormControl();
	quickSearchControl: FormControl = new FormControl();
	quickSearchCustomValue: string;
	data: any = {
		content: [],
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		number: 0,
	};
	totalItems!: number;
	pageSize: number = 5;
	currentPage: number = 1;
	pageSizeOptions = [10, 20, 30];
	selectedItems = [];
	rawData = [];
	rawDataIcons: Array<Array<PactoActionConfig>> = [];
	rawDropdownActionItems: Array<Array<PactoActionConfig>> = [];
	groupFilter: Observable<
		Array<{ id: string; descricao: string; total: number }>
	>;
	localStorageData;

	@HostBinding("class.ds3-relatorio")
	get isDs3Enabled() {
		return this.enableDs3;
	}
	get isOverallEnable() {
		return this.enableDs3 && this.enableOverall && this.filterConfig;
	}

	@HostListener("document:click", ["$event.target"])
	public clickHandler(targetElement) {
		const filter =
			(this.filterConfig || this.filterContent) &&
			this.table.showFilters &&
			!this.enableDs3;
		if (filter) {
			const filterBtnClick = this.isDescendant(
				this.filterToggleButton.nativeElement,
				targetElement
			);
			let filterClick = false;
			if (this.filterContent) {
				filterClick = this.isDescendant(
					this.filterContent.nativeElement,
					targetElement
				);
			} else if (this.dataGridFilter) {
				filterClick = this.isDescendant(
					this.dataGridFilter.nativeElement,
					targetElement
				);
			}
			const calendarClick = this.isCalendarClick(targetElement);
			if (!filterClick && !filterBtnClick && !calendarClick) {
				this.filterDropdown.close();
			}
		}
	}

	get RelatorioExportarFormato() {
		return RelatorioExportarFormato;
	}

	checkSelectdItem(checkItem: { row: any; checked: boolean }) {
		if (checkItem.checked) {
			this.selectedItems.push(checkItem.row);
		} else {
			this.selectedItems = this.selectedItems.filter((item) => {
				return (
					item[this.table.valueRowCheck] !==
					checkItem.row[this.table.valueRowCheck]
				);
			});
		}
		this.rowCheck.emit({
			...checkItem,
			clearAll: false,
			selectedAll: false,
		});
	}

	selectAll() {
		this.table.allCheck = true;
		this.selectedItems = this.data.content;
		this.rowCheck.emit({ clearAll: false, selectedAll: true });
		this.cd.detectChanges();
	}

	clearAll() {
		this.table.allCheck = false;
		this.table.checkeds = new Array<any>();
		this.selectedItems = new Array<any>();
		this.rowCheck.emit({ clearAll: true, selectedAll: false });
		this.cd.detectChanges();
	}

	constructor(
		private dataService: DataGridService,
		private notifyService: SnotifyService,
		private router: Router,
		private cd: ChangeDetectorRef,
		private dialog: MatDialog
	) {}

	ngOnInit() {
		this.pageSizeControl.setValue(this.itensPerPage[0].id);
		if (this.itensPerPage) {
			this.pageSizeOptions = this.itensPerPage.map((v) => v.id);
		}
	}

	ngAfterViewInit() {
		setTimeout(() => {
			const rota = this.router.url;
			const data = JSON.parse(
				localStorage.getItem("config_table_" + this.id + "_" + rota)
			);
			this.localStorageData = data;
			this.loadDataGroup();
			this.initialSetup(data);
			if (this.dataGridFilter) {
				this.temporaryFilters = this.dataGridFilter.getFilterData();
			}
			if (this.enableDs3) {
				let initialFilter = {};
				if (this.filterConfig) {
					initialFilter = this.filterConfig.filters
						.filter((item) => item.hasOwnProperty("initialValue"))
						.reduce((acc, item) => {
							acc[item.name] = item.initialValue;
							return acc;
						}, {});
				}

				this.temporaryFilters = {
					filters: data && data.filters ? data.filters : initialFilter,
					configs: data && data.configs ? data.configs : {},
				};
			}
			if (this.table.initialFilters) {
				this.table.initialFilters.forEach((filter) => {
					this.addFilter(filter.name, filter.value);
				});
			}
			this.setupLocalStorage(data);
			this.fetchDataEmit(true);
			this.autoFocus();
		});

		if (this.relSelectPageSize && this.relPageTotalElements) {
			if (this.labelTotalElementsPosition === "right") {
				this.relPageTotalElements.nativeElement.before(
					this.relSelectPageSize.nativeElement
				);
			}
		}
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else {
			return type !== "string";
		}
	}

	relatorioHandler(format: RelatorioExportarFormato) {
		const filtros = this.fetchFiltros();
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}
		this.dataService
			.obterRelatorio(
				this.table.endpointUrl,
				filtros,
				format,
				this.table.endpointParamsType
			)
			.subscribe((link) => {
				window.open(link, "_blank");
			});
	}

	reloadData(emitFilters: boolean = true) {
		this.fetchDataEmit(true, emitFilters);
	}

	sortUpdateHandler() {
		this.sortEvent.emit({
			columnName: this.table.state.ordenacaoColuna,
			direction: this.table.state.ordenacaoDirecao,
			column: this.table.columns.find(
				(column) => column.nome === this.table.state.ordenacaoColuna
			),
		});
		this.fetchData();
	}

	pageChangeHandler(page) {
		this.currentPage = page;
		if (page > 0) {
			this.table.state.paginaNumero = page - 1;
		}
		if (this.table.endpointUrl) {
			this.fetchDataEmit(false, true, page);
		} else {
			this.pageChangeEvent.emit(page);
			this.cd.markForCheck();
		}
	}

	onPageSizeChange(pageSize: any): void {
		this.table.state.paginaTamanho = parseInt(
			pageSize,
			this.table.pageSize || 10
		);
		this.table.state.paginaNumero = 0;
		this.ngbPage = 1;
		if (this.table.endpointUrl) {
			this.fetchData();
		} else {
			this.pageSizeChange.emit(+pageSize);
		}
	}

	btnCLickHandler() {
		this.btnClick.emit(this.table.buttons.nome);
	}

	btnClickFilter(event: { index: number; previous: string; next: string }) {
		this.quickSearchCustomValue = event.next;
		this.fetchData();

		this.pageSizeControl.valueChanges.subscribe((pageSize) => {
			this.table.state.paginaTamanho = parseInt(
				pageSize,
				this.table.pageSize || 10
			);
			this.table.state.paginaNumero = 0;
			this.ngbPage = 1;
			if (this.table.endpointUrl) {
				this.fetchData();
			} else {
				this.pageSizeChange.emit(+pageSize);
			}
		});
	}

	filterHandler(filter) {
		this.table.state.paginaNumero = 0;
		this.temporaryFilters = filter;
		if (!this.enableDs3) {
			this.filterDropdown.close();
		}
		this.fetchData();
	}

	cellClickHandler($event) {
		this.cellClick.emit($event);
	}

	alterfilterConfigUpdate(statusConfid) {
		this.filterConfigUpdate.emit(statusConfid);
	}

	private populateRawDataIcons() {
		this.rawDataIcons = [];
		if (this.rawData) {
			this.rawData.forEach((rawItem) => {
				const actions = [];
				this.table.actions.forEach((action) => {
					if (action.showIconFn === null) {
						actions.push(action);
					} else if (action.showIconFn(rawItem)) {
						actions.push(action);
					}
				});

				this.rawDataIcons.push(actions);
			});
		}
	}

	private populateRawDropdownActionItems() {
		this.rawDropdownActionItems = [];
		if (this.rawData) {
			this.rawData.forEach((rawItem) => {
				const ddActions = [];
				this.table.dropDownActions.forEach((ddAction) => {
					if (ddAction.showIconFn === null) {
						ddActions.push(ddAction);
					} else if (ddAction.showIconFn(rawItem)) {
						ddActions.push(ddAction);
					}
				});

				this.rawDropdownActionItems.push(ddActions);
			});
		}
	}

	private isDescendant(parent, child) {
		let node = child.parentNode;
		if (parent === child) {
			return true;
		} else {
			while (node !== null) {
				if (node === parent) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private isCalendarClick(target) {
		let node = target.parentNode;
		while (node !== null) {
			if (node.tagName === "NGB-DATEPICKER") {
				return true;
			}
			node = node.parentNode;
		}

		if (
			target != null &&
			target.classList.contains("mat-calendar-body-cell-content")
		) {
			return true;
		}
		return false;
	}

	private setupLocalStorage(data) {
		if (!data || !this.persistirFiltros) {
			return;
		}
		let msg = false;
		if (data.ngbPage) {
			this.table.state.paginaNumero = data.ngbPage;
			this.ngbPage = data.ngbPage + 1;
			this.currentPage = data.ngbPage + 1;
		}
		if (data.size) {
			this.table.state.paginaTamanho = data.size;
		}

		if (this.temporaryFilters && data.filters) {
			this.temporaryFilters.filters = data.filters;
			if (this.dataGridFilter) {
				this.dataGridFilter.setFilterSelectionStatus(data.filters);
			}
		}
		if (this.temporaryFilters && data.configs) {
			this.temporaryFilters.configs = data.configs;
		}
		if (data.columns) {
			this.table.columns.forEach((column, index) => {
				column.visible = data.columns[index].visible;
			});
		}
		if (data.sortField) {
			this.table.state.ordenacaoColuna = data.sortField;
		}
		if (data.sortDirection) {
			this.table.state.ordenacaoDirecao = data.sortDirection;
		}
		if (this.isAplicouFiltro) {
			msg = true;
		}
		if (data && data.quicksearchValue && data.quicksearchValue !== "") {
			msg = true;
		}
		if (msg) {
			this.notifyService.info(
				"Para melhorar sua experiência, mantivemos os filtros da última pesquisa"
			);
		}
		return data;
	}

	get isAplicouFiltro(): boolean {
		try {
			if (this.temporaryFilters && this.temporaryFilters.filters) {
				const valorToLS = this.temporaryFilters.filters;
				Object.keys(valorToLS).forEach((item) => {
					if (
						!valorToLS[item] ||
						valorToLS[item].length === 0 ||
						item === "quicksearchValue" ||
						item === "quicksearchFields"
					) {
						delete valorToLS[item];
					}
				});
				const jsonFilter = JSON.stringify(valorToLS);
				if (jsonFilter !== "{}") {
					return true;
				}
			}
			return false;
		} catch (e) {
			return false;
		}
	}

	private saveLocalStorage() {
		if (!this.persistirFiltros) {
			return;
		}
		const rota = this.router.url;
		const data: any = {};

		if (this.temporaryFilters && this.temporaryFilters.filters) {
			data.filters = this.temporaryFilters.filters;
		}
		if (this.temporaryFilters && this.temporaryFilters.configs) {
			data.configs = this.temporaryFilters.configs;
		}
		if (this.table.columns && this.enableDs3) {
			const columns = this.table.columns.map((item) => {
				return {
					nome: item.nome,
					visible: item.visible,
				};
			});
			data.columns = columns;
		}
		data.sortField = this.table.state.ordenacaoColuna;
		data.sortDirection = this.table.state.ordenacaoDirecao;
		if (
			this.quickSearchCustomValue !== null &&
			this.quickSearchCustomValue !== undefined &&
			this.quickSearchCustomValue.toString().trim() !== ""
		) {
			data.quickSearchCustomValue = this.quickSearchCustomValue;
		}
		if (this.table.quickSearch) {
			data.quicksearchValue = this.quickSearchControl.value;
		}
		if (this.table.pagination) {
			data.ngbPage = this.table.state.paginaNumero;
			data.size = this.table.state.paginaTamanho;
		}
		localStorage.setItem(
			"config_table_" + this.id + "_" + rota,
			JSON.stringify(data)
		);
	}

	private initialSetup(data) {
		// current page
		this.ngbPage = this.table.state.paginaNumero + 1;
		this.currentPage = this.table.state.paginaNumero + 1;
		// quick search
		if (data && data.quicksearchValue) {
			this.quickSearchControl.setValue(data.quicksearchValue);
		}
		if (this.table.quickSearch) {
			this.quickSearchControl.valueChanges
				.pipe(debounceTime(500))
				.subscribe(() => {
					this.table.state.paginaNumero = 0;
					this.ngbPage = 1;
					this.fetchData();
				});
		}
		// page size
		this.pageSizeControl.valueChanges.subscribe((pageSize) => {
			this.table.state.paginaTamanho = parseInt(
				pageSize,
				this.table.pageSize || 10
			);
			this.table.state.paginaNumero = 0;
			this.ngbPage = 1;
			if (this.table.endpointUrl) {
				this.fetchData();
			} else {
				this.pageSizeChange.emit(+pageSize);
			}
		});
	}

	public fetchData() {
		this.fetchDataEmit(false);
	}

	public fetchDataEmit(
		emit: boolean,
		emitFilters: boolean = true,
		pageChanged?: boolean,
		page?: number,
		pageSizeChanged?: boolean,
		size?: number
	) {
		const filtros = this.fetchFiltros();

		this.saveLocalStorage();
		if (
			filtros &&
			filtros.filters &&
			this.filterConfig &&
			this.filterConfig.filters
		) {
			Object.keys(filtros.filters).forEach((key) => {
				this.filterConfig.filters.every((filter) => {
					if (filter.name === key) {
						if (filter.valueTransform) {
							if (filter.nameDateRangeStart || filter.nameDateRangeEnd) {
								if (
									filter.nameDateRangeStart &&
									filtros.filters[filter.nameDateRangeStart]
								) {
									filtros.filters[filter.nameDateRangeStart] =
										filter.valueTransform(
											filtros.filters[filter.nameDateRangeStart]
										);
								}
								if (
									filter.nameDateRangeEnd &&
									filtros.filters[filter.nameDateRangeEnd]
								) {
									filtros.filters[filter.nameDateRangeEnd] =
										filter.valueTransform(
											filtros.filters[filter.nameDateRangeEnd]
										);
								}
							} else {
								filtros.filters[key] = filter.valueTransform(
									filtros.filters[key]
								);
							}
						} else if (
							filter.transformDateString &&
							filtros.filters[key] instanceof Object &&
							filtros.filters[key]["year"] &&
							filtros.filters[key]["month"] &&
							filtros.filters[key]["day"]
						) {
							const date = new Date(
								filtros.filters[key]["year"],
								filtros.filters[key]["month"] - 1,
								filtros.filters[key]["day"]
							);
							const dateString = date.toISOString().split("T")[0];
							filtros.filters[key] = dateString;
						}
						return false;
					}
					return true;
				});
			});
		}

		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));

		/**
		 * Merge 'filters'
		 */
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}

		this.dataFetchLoading = true;
		if (this.table.ghostLoad) {
			this.rawData = new Array(this.table.ghostAmount);
		} else {
			this.rawData = [];
		}

		if (this.dataFetchSubscription) {
			this.dataFetchSubscription.unsubscribe();
		}

		this.loadDataGroup();
		this.dataFetchSubscription = this.dataService
			.obterDados(
				this.table.endpointUrl,
				filtros,
				this.table.endpointParamsType,
				this.table.paramAdapterFn
			)
			.subscribe(
				(raw) => {
					let tranformedData;

					if (this.table.dataFormControl) {
						tranformedData = this.table.dataFormControl.value;
						this.table.dataFormControl.valueChanges.subscribe(() => {
							this.reloadData();
						});
					} else if (this.table.dataAdapterFn) {
						tranformedData = this.table.dataAdapterFn(raw);
					} else {
						tranformedData = raw;
					}

					this.dataFetchLoading = false;
					this.data = tranformedData;
					this.totalItems = this.data.totalElements;
					this.pageSize = this.data.size;
					this.rawData = tranformedData.content;
					this.populateRawDataIcons();
					this.populateRawDropdownActionItems();
					this.cd.detectChanges();
					if (this.enableDs3) {
						this.disableQuickSearchIfDataIsEmpty();
					}
					if (!this.table.endpointUrl) {
						if (emitFilters) {
							this.filterChangeEvent.emit(filtros);
						}
					} else if (emit === true) {
						this.loadedData.emit();
					}

					if (pageChanged) {
						this.pageChangeEvent.emit(page);
					}
				},
				(error) => {
					this.dataFetchLoading = false;
					this.cd.detectChanges();
					const msg =
						error.error && error.error.meta && error.error.meta.message
							? error.error.meta.message
							: "Houve um problema ao carregar os dados";
					this.notifyService.error(msg);
					if (this.table.onError && this.table.onError instanceof Function) {
						this.table.onError(error);
					} else {
						console.error(error);
					}
				}
			);
	}

	disableQuickSearchIfDataIsEmpty() {
		if (
			this.data &&
			this.data.content.length === 0 &&
			!this.quickSearchControl.value
		) {
			this.quickSearchControl.disable({
				onlySelf: false,
				emitEvent: false,
			});

			return;
		}
		this.quickSearchControl.enable({
			onlySelf: false,
			emitEvent: false,
		});
	}

	allFilters(): DataFiltro {
		const filtros = this.fetchFiltros();
		const baseFilter: DataFiltro = JSON.parse(JSON.stringify(this.baseFilter));
		/**
		 * Merge 'filters'
		 */
		if (baseFilter.filters) {
			Object.assign(filtros.filters, baseFilter.filters);
		}
		return filtros;
	}

	addFilter(name: string, value: string) {
		if (this.temporaryFilters === null || this.temporaryFilters === undefined) {
			this.temporaryFilters = { filters: {} };
		}
		this.temporaryFilters.filters[name] = value;
	}

	removeFilter(name: string) {
		if (this.temporaryFilters !== null && this.temporaryFilters !== undefined) {
			delete this.temporaryFilters.filters[name];
		}
	}

	fetchFiltros(): DataFiltro {
		const filtros: DataFiltro = {
			filters:
				this.temporaryFilters && this.temporaryFilters.filters
					? this.temporaryFilters.filters
					: {},
			configs:
				this.temporaryFilters && this.temporaryFilters.configs
					? this.temporaryFilters.configs
					: {},
			sortField: this.table.state.ordenacaoColuna,
			sortDirection: this.table.state.ordenacaoDirecao,
		};

		if (this.table.quickSearch) {
			filtros.filters.quicksearchValue = this.quickSearchControl.value;
			filtros.filters.quicksearchFields = this.fetchQuicksearchFields();
		}

		if (
			this.quickSearchCustomValue !== null &&
			this.quickSearchCustomValue !== undefined &&
			this.quickSearchCustomValue.toString().trim() !== ""
		) {
			filtros.filters.quicksearchValue = this.quickSearchCustomValue;
			filtros.filters.quicksearchFields = this.fetchQuicksearchFields();
		}
		if (this.table.pagination) {
			filtros.page = this.table.state.paginaNumero;
			filtros.size = this.table.state.paginaTamanho;
		}
		this.sanitizeFilter(filtros);
		const copy: DataFiltro = {};
		Object.assign(copy, filtros);
		return copy;
	}

	private sanitizeFilter(filtro: DataFiltro) {
		if (!filtro.sortField) {
			filtro.sortDirection = null;
		}
		this.removeUnsetValues(filtro);
		this.removeUnsetValues(filtro.filters);
	}

	private removeUnsetValues(object) {
		for (const key in object) {
			const ownProperty = Object.prototype.hasOwnProperty.call(object, key);
			if (ownProperty) {
				const element = object[key];
				if (element === undefined || element === null || element.length === 0) {
					delete object[key];
				}
			}
		}
	}

	private fetchQuicksearchFields() {
		const result = [];
		this.table.columns.forEach((column) => {
			if (column.buscaRapida) {
				result.push(column.nome);
			}
		});
		return result;
	}

	private autoFocus() {
		if (this.table.quickSearch) {
			this.quickSearch.nativeElement.focus();
		}
	}

	btnAddClickEvent(event) {
		this.btnAddClick.emit(event);
	}

	loadDataGroup() {
		if (this.table && this.table.endpointUrlGroup) {
			this.groupFilter = this.dataService.obterGrupo(
				this.table.endpointUrlGroup
			);
		}
	}

	actionLinkFooterClickEvent() {
		this.actionLinkFooterClick.emit();
	}

	public openSideFilter() {
		if (this.filterContentRef) {
			this.sideFilterRef = this.dialog.open(this.sideFilterTemplate, {
				position: { right: "0px", top: "0px" },
				height: "100%",
				id: "side-filter",
				disableClose: true,
				autoFocus: false,
				width: "30rem",
			});
			this.sideFilterRef
				.afterClosed()
				.subscribe((result: { motivo: string; valores: string }) => {
					if (result.motivo === "filter") {
						this.filterHandler({ configs: {}, filters: result.valores });
					} else if (result.motivo === "configUpdate") {
						this.alterfilterConfigUpdate(result.valores);
					} else if (result.motivo === "limpar") {
						this.filterHandler({ configs: {}, filters: {} });
					}
					// fechar
					this.cd.detectChanges();
				});
		} else {
			this.sideFilterRef = this.dialog.open(Ds3SideFilterComponent, {
				position: { right: "0px", top: "0px" },
				height: "100%",
				id: "side-filter",
				disableClose: true,
				autoFocus: false,
				width: "30rem",
			});
			this.sideFilterRef.componentInstance.sidefilter = this.sideFilterRef;
			this.sideFilterRef.componentInstance.idSuffix = this.idSuffix;
			this.sideFilterRef.componentInstance.id = this.id;
			this.sideFilterRef.componentInstance.config = this.filterConfig;
			this.sideFilterRef.componentInstance.columnsConfig = this.table.columns;
			this.sideFilterRef.componentInstance.values =
				this.fetchFiltros().filters || {};

			this.ds3SideFilterOpened.emit({
				sideFilterRef: this.sideFilterRef.componentInstance,
			});
			this.sideFilterRef.afterOpened().subscribe((v) => {
				this.cd.detectChanges();
			});
			this.sideFilterRef
				.afterClosed()
				.subscribe((result: { motivo: string; valores: string }) => {
					if (result.motivo === "filter") {
						this.filterHandler({ configs: {}, filters: result.valores });
					} else if (result.motivo === "configUpdate") {
						this.alterfilterConfigUpdate(result.valores);
					} else if (result.motivo === "limpar") {
						this.filterHandler({ configs: {}, filters: {} });
					}
					// fechar
					this.cd.detectChanges();
				});
		}
	}

	public closeSideFilter() {
		this.dialog.closeAll();
	}

	sideFilterlimparTudo() {
		if (this.sideFilterRef) {
			this.customFormGroup.reset();
			this.sideFilterRef.close({ motivo: "limpar", valores: {} });
		}
	}

	sideFilterAplicar() {
		if (this.sideFilterRef) {
			this.sideFilterRef.close({
				motivo: "filter",
				valores: this.customFormGroup.getRawValue(),
			});
		}
	}

	get sizeTemporaryFilters() {
		let filtros = this.fetchFiltros().filters;
		let count = Object.keys(filtros).length;
		if (filtros.quicksearchFields) {
			count -= 1;
		}
		if (filtros.quicksearchValue) {
			count -= 1;
		}
		return count > 0 ? count : "";
	}

	get filterOverall() {
		if (this.filterConfig) {
			const filters = this.fetchFiltros().filters;
			const config = this.filterConfig.filters;
			let returnal;
			if (filters && config) {
				returnal = Object.entries(filters).map(([key, value]) => {
					if (key == "quicksearchValue") {
						return {
							label: "Pesquisa Rápida",
							value: value.toString(),
							tooltipText: [],
						};
					}
					const filterConfig = config.find(
						(f) => f.name === key
					) as GridFilterMany;
					if (filterConfig && filterConfig.type) {
						let campoLabel: string | boolean =
							filterConfig.label && typeof filterConfig.label == "string"
								? filterConfig.label
								: key;
						let returnalValue: string = "";
						let returnalTooltip: string[] = [];
						switch (filterConfig.type) {
							case GridFilterType.DS3_CHECKBOX:
								returnalValue = campoLabel;
								campoLabel = false;
								break;
							case GridFilterType.DS3_CHIPS:
								returnalValue = filterConfig.options.find(
									(v, index) => v.value == value[0]
								).label;
								if ((value as Array<any>).length > 1) {
									returnalValue += ` (+${(value as Array<any>).length - 1})`;
									returnalTooltip = (value as Array<any>).map((val) => {
										const option = filterConfig.options.find(
											(opt) => opt.value === val
										);
										return option ? option.label : val;
									});
								}
								break;
							case GridFilterType.DS3_INPUT:
								returnalValue = value.toString();
								break;
							case GridFilterType.DS3_SELECT_MANY:
								let obj;
								filterConfig.options.forEach((key) => {
									if (value[0] == key.value) {
										obj = key;
									}
								});
								returnalValue = obj.label;
								if ((value as Array<any>).length > 1) {
									returnalValue += ` (+${(value as Array<any>).length - 1})`;
									returnalTooltip = (value as Array<any>).map((val) => {
										const option = filterConfig.options.find(
											(opt) => opt.value === val
										);
										return option ? option.label : val;
									});
								}
								break;
							case GridFilterType.DS3_SELECT_ONE:
								returnalValue = filterConfig.options.find(
									(v) => v.value == value
								).label;
								break;
							case GridFilterType.DS3_DATE:
								returnalValue = moment(value).format("DD/MM/yyyy");
								break;
							case GridFilterType.DS3_DATE_RANGE:
								const filterCfg = config.find((f) => f.name === key);
								if (
									filterCfg &&
									filterCfg.nameDateRangeStart &&
									filterCfg.nameDateRangeEnd &&
									filters[filterCfg.nameDateRangeStart] &&
									filters[filterCfg.nameDateRangeEnd]
								) {
									const startDate = moment(
										filters[filterCfg.nameDateRangeStart]
									);
									const endDate = moment(filters[filterCfg.nameDateRangeEnd]);
									if (startDate.isValid() && endDate.isValid()) {
										returnalValue = `${startDate.format(
											"DD/MM/YYYY"
										)} - ${endDate.format("DD/MM/YYYY")}`;
									} else {
										returnalValue = "Data Inválida";
									}
								} else {
									returnalValue = "Data Inválida";
								}
								break;
							case GridFilterType.DS3_TIME:
								returnalValue = value.toString();
								break;
							case GridFilterType.DS3_TIME_RANGE:
								returnalValue = value.toString();
								break;
						}
						return {
							label: campoLabel,
							value: returnalValue,
							tooltipText: returnalTooltip,
						};
					}
				});
			}
			return returnal.filter((item) => item !== null);
		}
	}
}
