import { TemplateRef } from "@angular/core";
import { TableData } from "./relatorio.component";
import { FormControl, FormGroup } from "@angular/forms";
import { PactoModalSize } from "../../dialog/service/dialog.service";
import { Observable } from "rxjs";
import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
} from "../cat-select-filter/cat-select-filter.component";
import { CdkDragDrop } from "@angular/cdk/drag-drop";

export enum PactoDataGridOrdenacaoDirecao {
	ASC = "ASC",
	DESC = "DESC",
}

export class PactoDataGridState {
	ordenacaoColuna: string;
	ordenacaoDirecao: PactoDataGridOrdenacaoDirecao;
	paginaTamanho?: number;
	paginaNumero?: number;

	constructor(dados?) {
		dados = dados || {};
		this.ordenacaoColuna = dados.ordenacaoColuna;
		this.ordenacaoDirecao =
			dados.ordenacaoDirecao || PactoDataGridOrdenacaoDirecao.ASC;
		this.paginaTamanho = dados.paginaTamanho || 10;
		this.paginaNumero = dados.paginaNumero || 0;
	}
}

export interface PactoDataGridConfigDto {
	endpointUrl?: string;
	logUrl?: string;
	endpointParamsType?: "json" | "query";
	quickSearch?: boolean;
	customFilterTab?: boolean;
	endpintUrlGroup?: string;
	valueRowCheck?: string;
	tooltipCheckbox?: string;
	exportButton?: boolean;
	showFilters?: boolean;
	initialFilters?: Array<{ name: string; value: string }>;
	pagination?: boolean;
	rowClick?: boolean;
	totalRow?: boolean;
	ordenable?: boolean;
	onError?: (error) => void;
	onDragEnd?: (event: CdkDragDrop<string[]>, data: Array<any>) => void;
	dataAdapterFn?: (serverData: any) => TableData<any>;
	dataFn?: (filter: DataFiltro) => TableData<any>;
	paramAdapterFn?: (params: any) => any;
	dataFormControl?: FormControl;
	actions?: Array<{
		nome: string;
		iconClass: string;
		tooltipText?: string;
		showIconFn?: (row: any) => boolean;
		actionFn?: (row: any, rowIndex?: number) => any;
	}>;
	dropDownActions?: Array<{
		nome: string;
		iconClass: string;
		tooltipText?: string;
		showIconFn?: (row: any) => boolean;
		actionFn?: (row: any, rowIndex?: number) => any;
	}>;
	showSimpleTotalCount?: boolean;
	buttons?: PactoDataGridButtonConfig;
	columns?: Array<PactoDataGridColumnConfigDto>;
	state?: PactoDataGridState;
	ghostLoad?: boolean;
	ghostAmount?: number;
	ghostAmination?: boolean;
	visibleColumnFn?: (column: PactoDataGridColumnConfig) => boolean;
	formGroup?: FormGroup;
	selectParamBuilder?: SelectFilterParamBuilder;
	beforeConfirm?: (row, form, data, indexRow) => boolean;
	showDelete?: (row, isAdd) => boolean;
	showEdit?: (row, isAdd) => boolean;
	onDeleteFn?: (row, data, indexRow) => Observable<boolean>;
	onAddFn?: (row, data, indexRow) => Observable<boolean> | Observable<Object>;
	onEditFn?: (row, data, indexRow) => Observable<boolean> | Observable<Object>;
	onClickEditFn?: (row, data, indexRow, form) => void;
	selectOptionChange?: (option, form, row) => boolean;
}

export declare type ColumnInputType =
	| "text"
	| "decimal"
	| "date"
	| "number"
	| "checkbox"
	| "currency"
	| "select"
	| "color"
	| "switch"
	| "select-multi";

export class PactoDataGridColumnConfig {
	titulo: TemplateRef<any> | string;
	mostrarTitulo?: boolean;
	ordenavel: boolean;
	date?: boolean;
	dateTime?: boolean = false;
	/**
	 * É um atributo da entidade e é
	 * filtrado pela busca rápida.
	 */
	buscaRapida?: boolean;
	cellPointerCursor?: boolean;
	campo?: string;
	nome: string;
	visible: boolean;
	exportNoVisible?: boolean;
	defaultVisible?: boolean;
	valueTransform?: (v: any, row: any) => any;
	celula?: TemplateRef<any>;
	classes?: Array<string>;
	styleClass?: string;
	editable?: boolean;
	inputType: ColumnInputType;
	inputSelectData?: Array<any>;
	control?: FormControl;
	width?: string;
	labelSelectKey?: string = "label";
	idSelectKey?: string = "codigo";
	modalComponent?: any;
	modalSize?: PactoModalSize;
	modalTitle?: string = "Cadastrar";
	inputTextMask?;
	labelText?: string;
	labelTextUnchecked?: string;
	isObject?: boolean;
	objectAttrLabelName?: string = "label";
	showAddSelectBtn?: boolean = true;
	showSelectFilter?: boolean = true;
	endpointUrl?: string;
	logUrl?: string;
	inputSelectDataSubscription?: Observable<any>;
	errorMessage?: string;
	showEmptyMessage?: boolean;
	selectParamBuilder?: SelectFilterParamBuilder;
	labelFn?: (item: any) => string;
	maxlength?: number;
	maxValue?: number;
	minValue?: number;
	decimal?: boolean;
	decimalPrecision?: number = 2;
	prefix?: string = "";
	isDisabled?: (row) => boolean;
	selectOptionChange?: (option, form, row) => void;
	valueChange?: (value, form, row, data, rowIndex) => void;
	addEmptyOption?: boolean;
	customOption?: any;
	orderColumn?: string;
	infiniteScrollEnabled?: boolean;
	infiniteScrollElementsSize?: number;
	responseParser?: SelectFilterResponseParser;

	constructor(dados: PactoDataGridColumnConfigDto) {
		this.nome = dados.nome;
		this.titulo = dados.titulo;
		this.date = dados.date;
		this.dateTime = dados.dateTime;
		this.mostrarTitulo =
			dados.mostrarTitulo === undefined ? true : dados.mostrarTitulo;
		this.buscaRapida = dados.buscaRapida != null ? dados.buscaRapida : false;
		this.cellPointerCursor =
			dados.cellPointerCursor != null ? dados.cellPointerCursor : false;
		this.ordenavel = dados.ordenavel != null ? dados.ordenavel : true;
		this.defaultVisible =
			dados.defaultVisible != null ? dados.defaultVisible : false;
		this.campo = dados.campo || dados.nome;
		this.valueTransform = dados.valueTransform ? dados.valueTransform : null;
		this.celula = dados.celula;
		this.visible = dados.visible;
		this.exportNoVisible = dados.exportNoVisible;
		this.classes = dados.classes || [];
		this.styleClass = dados.styleClass;
		this.inputType = dados.inputType;
		this.editable = dados.editable;
		this.inputSelectData = dados.inputSelectData;
		this.orderColumn = dados.orderColumn;
		if (dados.control) {
			this.control = dados.control;
		}
		if (dados.width) {
			this.width = dados.width;
		} else {
			this.width = "auto";
		}

		if (dados.labelSelectKey) {
			this.labelSelectKey = dados.labelSelectKey;
		}

		if (dados.idSelectKey) {
			this.idSelectKey = dados.idSelectKey;
		}

		if (dados.selectOptionChange !== undefined) {
			this.selectOptionChange = dados.selectOptionChange;
		}

		if (dados.valueChange !== undefined) {
			this.valueChange = dados.valueChange;
		}

		if (dados.dialogComponent) {
			this.modalComponent = dados.dialogComponent;
		}
		if (dados.modalSize) {
			this.modalSize = dados.modalSize;
		}
		if (dados.modalAddTitle) {
			this.modalTitle = dados.modalAddTitle;
		}
		if (dados.inputTextMask) {
			this.inputTextMask = dados.inputTextMask;
		}

		if (dados.labelText) {
			this.labelText = dados.labelText;
		}

		if (dados.labelTextUnchecked) {
			this.labelTextUnchecked = dados.labelTextUnchecked;
		}

		this.isObject = dados.isObject;
		this.addEmptyOption = dados.addEmptyOption;
		this.customOption = dados.customOption;
		if (dados.objectAttrLabelName) {
			this.objectAttrLabelName = dados.objectAttrLabelName;
		}

		if (dados.showAddSelectBtn !== undefined) {
			this.showAddSelectBtn = dados.showAddSelectBtn;
		}
		if (dados.endpointUrl) {
			this.endpointUrl = dados.endpointUrl;
		}
		if (dados.logUrl) {
			this.logUrl = dados.logUrl;
		}
		if (dados.inputSelectDataSubscription) {
			this.inputSelectDataSubscription = dados.inputSelectDataSubscription;
		}
		if (dados.showEmptyMessage) {
			this.showEmptyMessage = dados.showEmptyMessage;
		}
		if (dados.showSelectFilter !== undefined) {
			this.showSelectFilter = dados.showSelectFilter;
		}
		if (dados.errorMessage) {
			this.errorMessage = dados.errorMessage;
		}
		if (dados.selectParamBuilder) {
			this.selectParamBuilder = dados.selectParamBuilder;
		}

		if (dados.maxlength) {
			this.maxlength = dados.maxlength;
		}

		if (dados.maxValue) {
			this.maxValue = dados.maxValue;
		}
		if (dados.minValue) {
			this.minValue = dados.minValue;
		}
		if (dados.prefix) {
			this.prefix = dados.prefix;
		}
		if (dados.decimal !== undefined) {
			this.decimal = dados.decimal;
		}
		if (dados.decimalPrecision !== undefined) {
			this.decimalPrecision = dados.decimalPrecision;
		}

		if (dados.isDisabled !== undefined) {
			this.isDisabled = dados.isDisabled;
		}

		if (dados.infiniteScrollEnabled !== undefined) {
			this.infiniteScrollEnabled = dados.infiniteScrollEnabled;
		}

		if (this.infiniteScrollEnabled) {
			if (dados.infiniteScrollElementsSize !== undefined) {
				this.infiniteScrollElementsSize = dados.infiniteScrollElementsSize;
			} else {
				this.infiniteScrollElementsSize = 10;
			}
		}

		this.labelFn = dados.labelFn;
		this.responseParser = dados.responseParser;
	}
}

export class PactoActionConfig {
	nome: string;
	iconClass: string;
	tooltipText?: string;
	showIconFn?: (row: any) => boolean;
	actionFn?: (row: any, rowIndex?: number) => any;

	constructor(dados) {
		this.nome = dados.nome;
		this.iconClass = dados.iconClass;
		this.tooltipText = dados.tooltipText;
		this.showIconFn = dados.showIconFn ? dados.showIconFn : null;
		this.actionFn = dados.actionFn ? dados.actionFn : null;
	}
}

export class PactoDataGridConfig {
	endpointUrl: string;
	logUrl: string;
	endpointParamsType: "json" | "query";
	valueRowCheck: string;
	tooltipCheckbox?: string;
	quickSearch: boolean;
	customFilterTab: boolean;
	endpointUrlGroup: string;
	exportButton: boolean;
	pagination: boolean;
	pageSize?: number;
	showFilters: boolean;
	initialFilters?: Array<{ name: string; value: string }> = [];
	rowClick: boolean;
	ordenable?: boolean;
	onError?: (error) => void;
	onDragEnd?: (event: CdkDragDrop<string[]>, data: Array<any>) => void;
	totalRow: boolean;
	showSimpleTotalCount: boolean;
	actions: Array<PactoActionConfig> = [];
	dropDownActions: Array<PactoActionConfig> = [];
	columns: Array<PactoDataGridColumnConfig> = [];
	dataAdapterFn: (serverData: any) => TableData<any>;
	dataFn: (filter: DataFiltro) => TableData<any>;
	paramAdapterFn?: (params: any) => any;
	dataFormControl?: FormControl;
	buttons: PactoDataGridButtonConfig;
	state: PactoDataGridState;
	ghostLoad?: boolean;
	ghostAmount?: number;
	ghostAnimation?: boolean;
	visibleColumnFn?: (column: PactoDataGridColumnConfig) => boolean;
	formGroup?: FormGroup;
	beforeConfirm?: (row, form, data, indexRow) => boolean;
	showDelete?: (row, isAdd) => boolean;
	showEdit?: (row, isAdd) => boolean;
	onDeleteFn?: (row, data, indexRow) => Observable<boolean>;
	onAddFn?: (row, data, indexRow) => Observable<boolean> | Observable<Object>;
	onEditFn?: (row, data, indexRow) => Observable<boolean> | Observable<Object>;
	onClickEditFn?: (row, data, indexRow, form) => void;
	checkeds: Array<any> = [];
	allCheck = false;

	constructor(config: PactoDataGridConfigDto) {
		if (
			config.endpointParamsType === null ||
			config.endpointParamsType === undefined
		) {
			config.endpointParamsType = "json";
		}

		if (config.columns) {
			config.columns.forEach((column) => {
				this.columns.push(new PactoDataGridColumnConfig(column));
			});
		}

		if (config.actions) {
			config.actions.forEach((action) => {
				this.actions.push(new PactoActionConfig(action));
			});
		}

		if (config.dropDownActions) {
			config.dropDownActions.forEach((action) => {
				this.dropDownActions.push(new PactoActionConfig(action));
			});
		}

		if (config.beforeConfirm !== undefined) {
			this.beforeConfirm = config.beforeConfirm;
		}

		if (config.showDelete !== undefined) {
			this.showDelete = config.showDelete;
		} else {
			this.showDelete = () => true;
		}

		if (config.showEdit !== undefined) {
			this.showEdit = config.showEdit;
		} else {
			this.showEdit = () => true;
		}

		this.endpointUrl = config.endpointUrl;
		this.logUrl = config.logUrl;
		this.endpointParamsType = config.endpointParamsType;
		this.valueRowCheck = config.valueRowCheck;
		this.tooltipCheckbox = config.tooltipCheckbox;
		this.dataAdapterFn = config.dataAdapterFn;
		this.dataFn = config.dataFn;
		this.ordenable = config.ordenable;
		this.onDragEnd = config.onDragEnd;
		this.customFilterTab =
			config.customFilterTab != null ? config.customFilterTab : false;
		this.endpointUrlGroup = config.endpintUrlGroup;
		this.quickSearch = config.quickSearch != null ? config.quickSearch : false;
		this.showFilters = config.showFilters != null ? config.showFilters : true;
		this.initialFilters = config.initialFilters;
		this.exportButton =
			config.exportButton != null ? config.exportButton : false;
		this.showSimpleTotalCount =
			config.showSimpleTotalCount != null ? config.showSimpleTotalCount : false;
		this.rowClick = config.rowClick != null ? config.rowClick : true;
		this.pagination = config.pagination != null ? config.pagination : true;
		this.totalRow = config.totalRow != null ? config.totalRow : false;
		this.buttons = config.buttons;
		this.ghostLoad = config.ghostLoad === null ? false : config.ghostLoad;
		this.state = new PactoDataGridState(config.state);
		this.ghostAmount = config.ghostAmount || this.state.paginaTamanho;
		this.ghostAnimation =
			config.ghostAmination === null || config.ghostAmination === undefined
				? false
				: config.ghostAmination;
		if (config.formGroup) {
			this.formGroup = config.formGroup;
		}

		this.onDeleteFn = config.onDeleteFn;
		this.onAddFn = config.onAddFn;
		this.onEditFn = config.onEditFn;
		this.onClickEditFn = config.onClickEditFn;
		this.paramAdapterFn = config.paramAdapterFn;
		this.onError = config.onError;
		this.visibleColumnFn = config.visibleColumnFn;

		this.dataFormControl = config.dataFormControl;
	}
}

export class PactoDataGridButtonConfig {
	conteudo: TemplateRef<any>;
	nome: string;
	id?: string;
}

export interface PactoDataGridColumnConfigDto {
	nome: string;
	titulo: TemplateRef<any> | string;
	valueTransform?: (v: any, row: any) => any;
	dataAdapterFn?: (serverData: any) => TableData<any>;
	mostrarTitulo?: boolean;
	ordenavel?: boolean;
	buscaRapida?: boolean;
	cellPointerCursor?: boolean;
	visible?: boolean;
	exportNoVisible?: boolean;
	defaultVisible?: boolean;
	date?: boolean;
	dateTime?: boolean;
	campo?: string;
	classes?: Array<string>;
	styleClass?: string;

	editable?: boolean;
	inputType?: ColumnInputType;
	inputSelectData?: Array<any>;
	inputSelectDataSubscription?: Observable<any>;
	control?: FormControl;
	width?: string;
	labelSelectKey?: string;
	idSelectKey?: string;
	dialogComponent?: any;
	modalSize?: PactoModalSize;
	modalAddTitle?: string;
	inputTextMask?;
	labelText?: string;
	labelTextUnchecked?: string;
	isObject?: boolean;
	objectAttrLabelName?: string;
	showAddSelectBtn?: boolean;
	endpointUrl?: string;
	logUrl?: string;
	showSelectFilter?: boolean;
	errorMessage?: string;
	showEmptyMessage?: boolean;
	/**
	 * Permite customizar conteudo da célula
	 *
	 * A variável item é igual o objeto renderizado
	 */
	celula?: TemplateRef<any>;
	selectParamBuilder?: SelectFilterParamBuilder;
	responseParser?: SelectFilterResponseParser;
	labelFn?: (item: any) => string;
	maxlength?: number;
	maxValue?: number;
	minValue?: number;
	decimalPrecision?: number;
	prefix?: string;
	decimal?: boolean;
	isDisabled?: (row) => boolean;
	selectOptionChange?: (option, form, row) => void;
	valueChange?: (value, form, row, data, rowIndex) => void;
	addEmptyOption?: boolean;
	customOption?: any;
	orderColumn?: string;
	infiniteScrollEnabled?: boolean;
	infiniteScrollElementsSize?: number;
}

export enum RelatorioExportarFormato {
	PDF = "PDF",
	XLS = "XLS",
}

export interface DataFiltro {
	quicksearchValue?: filterValue;
	quicksearchFields?: Array<filterValue>;
	filters?: { [key: string]: filterValue | Array<filterValue> };
	configs?: { [key: string]: any };
	page?: number;
	size?: number;
	/**
	 * ASC/DESC
	 */
	sortDirection?: string;
	sortField?: string;
}

export type filterValue = number | string;
