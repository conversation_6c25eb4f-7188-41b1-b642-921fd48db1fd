<div class="dialog">
	<div class="dialog-head">
		<span class="dialog-title">Filtros</span>
		<button (click)="sideFilterFechar()" type="button" ds3-icon-button>
			<i class="pct pct-x"></i>
		</button>
	</div>
	<div cdkScrollable class="dialog-content" cdkTrapFocus>
		<div
			*ngFor="let filter of config.filters; trackBy: filterName"
			MatDialogContent>
			<form [formGroup]="formGroup">
				<div [ngSwitch]="filter?.type">
					<div *ngSwitchCase="GridFilterType.DS3_INPUT" class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<input
								[formControl]="formGroup.controls[filter?.name]"
								ds3Input
								type="text" />
						</ds3-form-field>
					</div>
					<div
						*ngSwitchCase="GridFilterType.DS3_SELECT_MANY"
						class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<ds3-select-multi
								[formControl]="formGroup.controls[filter?.name]"
								[options]="filter?.options"
								ds3Input
								[placeholder]="
									filter?.selectPlaceholder ||
									'Selecione os ' + (filter?.label | lowercase)
								"
								nameKey="label"></ds3-select-multi>
						</ds3-form-field>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_SELECT_ONE" class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<ds3-select
								[formControl]="formGroup.controls[filter?.name]"
								[options]="filter?.options"
								ds3Input
								[placeholder]="
									filter?.selectPlaceholder ||
									'Selecione um ' + (filter?.label | lowercase)
								"
								nameKey="label"></ds3-select>
						</ds3-form-field>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_CHIPS" class="ds3-filter">
						<div>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
						</div>
						<div class="ds3-filter-chips">
							<ds3-chips
								(selectionChange)="
									chipsUpdateSelectionChange($event, chip.value, filter?.name)
								"
								*ngFor="let chip of filter?.options; trackBy: trackByChipValue"
								[isActive]="isChipActive(filter, chip.value)">
								{{ chip.label }}
							</ds3-chips>
						</div>
						<ds3-diviser></ds3-diviser>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_DATE" class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<ds3-input-date
								[control]="formGroup.controls[filter?.name]"
								dateType="datepicker"
								[formControl]="controlAux"
								ds3Input></ds3-input-date>
						</ds3-form-field>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_TIME" class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<ds3-input-date
								[control]="formGroup.controls[filter?.name]"
								dateType="timepicker"
								[formControl]="controlAux"
								ds3Input></ds3-input-date>
						</ds3-form-field>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_TIME_RANGE" class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<ds3-input-date
								[controlEnd]="
									formGroup.controls[
										filter?.nameDateRangeEnd
											? filter?.nameDateRangeEnd
											: filter?.name + '_end'
									]
								"
								[controlStart]="
									formGroup.controls[
										filter?.nameDateRangeStart
											? filter?.nameDateRangeStart
											: filter?.name + '_start'
									]
								"
								dateType="timeranger"
								[formControl]="controlAux"
								ds3Input
								separatorText=" - "
								position="bottom-center"></ds3-input-date>
						</ds3-form-field>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_DATE_RANGE" class="ds3-filter">
						<ds3-form-field>
							<ds3-field-label>{{ filter?.label }}</ds3-field-label>
							<ds3-input-date
								[controlEnd]="
									formGroup.controls[
										filter?.nameDateRangeEnd
											? filter?.nameDateRangeEnd
											: filter?.name + '_end'
									]
								"
								[controlStart]="
									formGroup.controls[
										filter?.nameDateRangeStart
											? filter?.nameDateRangeStart
											: filter?.name + '_start'
									]
								"
								[label]="filter?.label"
								dateType="dateranger"
								[formControl]="controlAux"
								ds3Input
								position="middle-screen"></ds3-input-date>
						</ds3-form-field>
					</div>
					<div *ngSwitchCase="GridFilterType.DS3_CHECKBOX" class="ds3-filter">
						<ds3-form-field>
							<ds3-checkbox
								[formControl]="formGroup.controls[filter?.name]"
								ds3Input>
								{{ filter?.label }}
							</ds3-checkbox>
						</ds3-form-field>
					</div>
				</div>
			</form>
		</div>
		<div *ngIf="columnsConfig" class="ds3-filter">
			<ds3-form-field>
				<ds3-field-label>
					{{ "filter.colunasVisiveis" | translate }}
				</ds3-field-label>
				<ds3-select-multi
					[formControl]="visibleColumnsControl"
					[options]="visibleColumnsOptions"
					ds3Input></ds3-select-multi>
			</ds3-form-field>
		</div>
	</div>
	<div class="dialog-actions">
		<button (click)="sideFilterlimparTudo()" ds3-outlined-button>
			Limpar filtros
		</button>
		<button (click)="sideFilterAplicar()" ds3-flat-button>
			Aplicar filtros
		</button>
	</div>
</div>
