import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EmbeddedViewRef,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	TemplateRef,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MatDialogRef } from "@angular/material";
import { Router } from "@angular/router";
import { GridFilterConfig, GridFilterType } from "../data-grid-filter.model";
import { PactoDataGridColumnConfig } from "../data-grid.model";

@Component({
	selector: "ds3-side-filter",
	templateUrl: "./ds3-side-filter.component.html",
	styleUrls: ["./ds3-side-filter.component.scss"],
})
export class Ds3SideFilterComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	@Input() config: GridFilterConfig;
	@Input() columnsConfig: PactoDataGridColumnConfig | any;
	@Input() sidefilter: MatDialogRef<Ds3SideFilterComponent, any>;
	@Input() idSuffix;
	@Input() localStorageData;
	@Input() values;
	@Input() id;
	@Output() filter: EventEmitter<any> = new EventEmitter();
	@Output() configUpdate: EventEmitter<any> = new EventEmitter();
	@Output() formGroupConfigured: EventEmitter<any> = new EventEmitter();

	formGroup = new FormGroup({});
	visibleColumnsOptions;
	visibleColumnsControl = new FormControl([]);
	filtrosAplicados;
	tempFilters: string[];
	readonly GridFilterType = GridFilterType;

	controlAux = new FormControl();
	constructor(private router: Router, private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.setFormGroup();
		this.getVisibleColumnsOptions();
	}

	ngAfterViewInit(): void {
		this.setVisibleColumns();
	}

	filterName(index: number, filter: any): string | number {
		return filter ? filter.name : index;
	}

	trackByChipValue(index: number, chip: any): any {
		return chip ? chip.value : index;
	}

	getVisibleColumnsOptions() {
		if (this.columnsConfig) {
			this.visibleColumnsOptions = this.columnsConfig.map((item) => {
				if (item.titulo instanceof TemplateRef) {
					return {
						name: (item.titulo.createEmbeddedView() as EmbeddedViewRef<any>)
							.rootNodes[0].textContent,
						value: item.nome,
					};
				}
				return {
					name: item.titulo,
					value: item.nome,
				};
			});
		}
	}

	setVisibleColumns() {
		const rota = this.router.url;
		const localStorageData = JSON.parse(
			localStorage.getItem("config_table_" + this.id + "_" + rota)
		);

		if (this.columnsConfig) {
			if (localStorageData && localStorageData.columns) {
				const columnsLocalStorage = localStorageData.columns
					.filter((column) => column.visible)
					.map((column) => column.nome);

				this.visibleColumnsControl.setValue(columnsLocalStorage);
				return;
			}
			const columnsTable = this.columnsConfig.map((item) => item.nome);
			this.visibleColumnsControl.setValue(columnsTable);
		}
	}

	findColumnByNome(nome: string) {
		return this.columnsConfig.find((column) => column.nome === nome);
	}

	chipsUpdateSelectionChange(
		status: boolean,
		value: string,
		controlName: string
	) {
		const formControl = this.formGroup.controls[controlName].value || [];

		if (!formControl.includes(value) && status) {
			formControl.push(value);
		} else {
			formControl.splice(formControl.indexOf(value), 1);
		}

		this.formGroup.get(controlName).setValue(formControl);
	}

	setFormGroup() {
		const rota = this.router.url;
		const data = JSON.parse(
			localStorage.getItem(`config_table_${this.id}_${rota}`)
		);
		if (data) {
			this.tempFilters = data.filters;
		}
		this.config.filters.forEach((v) => {
			let value = v.initialValue;
			if (data && data.filters) {
				value = data.filters[v.name];
			}
			if (
				v.type === GridFilterType.DS3_DATE ||
				v.type === GridFilterType.DS3_INPUT ||
				v.type === GridFilterType.DS3_SELECT_MANY ||
				v.type === GridFilterType.DS3_SELECT_ONE ||
				v.type === GridFilterType.DS3_TIME ||
				v.type === GridFilterType.DS3_CHIPS ||
				v.type === GridFilterType.DS3_CHECKBOX
			) {
				this.formGroup.addControl(
					v.name,
					new FormControl(value, { updateOn: "blur" })
				);
			} else if (
				v.type === GridFilterType.DS3_DATE_RANGE ||
				v.type === GridFilterType.DS3_TIME_RANGE
			) {
				let startControlName = v.name + "_start";
				let endControlName = v.name + "_end";
				if (v.nameDateRangeStart) {
					startControlName = v.nameDateRangeStart;
				}
				if (v.nameDateRangeEnd) {
					endControlName = v.nameDateRangeEnd;
				}
				let startValue = value;
				let endValue = value;
				if (data && data.filters) {
					startValue = data.filters[v.nameDateRangeStart];
					endValue = data.filters[v.nameDateRangeEnd];
				}
				this.formGroup.addControl(
					startControlName,
					new FormControl(startValue, { updateOn: "blur" })
				);
				this.formGroup.addControl(
					endControlName,
					new FormControl(endValue, { updateOn: "blur" })
				);
			}
		});
		if (this.values) {
			this.formGroup.patchValue(this.values);
			this.cd.detectChanges();
		}

		this.formGroupConfigured.emit();
	}

	sideFilterlimparTudo() {
		this.sidefilter.close({ motivo: "limpar", valores: "" });
		if (this.columnsConfig) {
			const columnsTable = this.columnsConfig.map((item) => item.nome);
			this.visibleColumnsControl.setValue(columnsTable);
			this.columnsConfig.forEach((column) => (column.visible = true));
		}
	}

	sideFilterAplicar() {
		this.filtrosAplicados = this.formGroup.getRawValue();
		if (this.columnsConfig) {
			this.columnsConfig.forEach((column) => {
				if (this.visibleColumnsControl.value.includes(column.nome)) {
					column.visible = true;
				} else {
					column.visible = false;
				}
			});
		}
		this.sidefilter.close({ motivo: "filter", valores: this.filtrosAplicados });
	}

	sideFilterFechar() {
		this.sidefilter.close({ motivo: "fechar" });
	}

	private chipActiveCache = new Map<string, boolean>();

	isChipActive(filter: any, value: any): boolean {
		const cacheKey = `${filter.name}_${value}`;

		if (this.chipActiveCache.has(cacheKey)) {
			return this.chipActiveCache.get(cacheKey)!;
		}

		let isActive = false;
		if (this.tempFilters && filter.initialValue) {
			if (this.tempFilters[filter.name]) {
				isActive = this.tempFilters[filter.name].includes(value);
			} else if (Array.isArray(filter.initialValue)) {
				isActive = filter.initialValue.includes(value);
			}
		}

		this.chipActiveCache.set(cacheKey, isActive);
		return isActive;
	}

	private clearChipActiveCache(): void {
		this.chipActiveCache.clear();
	}

	ngOnDestroy(): void {
		this.clearChipActiveCache();
	}
}
