import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { Ds3SideFilterComponent } from "./ds3-side-filter.component";

describe("Ds3SideFilterComponent", () => {
	let component: Ds3SideFilterComponent;
	let fixture: ComponentFixture<Ds3SideFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [Ds3SideFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(Ds3SideFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
