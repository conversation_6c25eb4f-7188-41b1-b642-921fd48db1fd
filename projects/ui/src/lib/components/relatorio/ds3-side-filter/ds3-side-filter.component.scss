@import "projects/ui/assets/ds3/typography/mixins";

// var(--color-background-plane-2);
// var(--color-support-gray-3);
// var(--color-typography-default-text);
// var(--color-action-default-able-4);
// var(--color-typography-default-text);
// var(--color-action-default-disabled-2);
// var(--color-typography-default-text);
// var(--color-typography-bg-dark-title);
// var(--color-action-default-able-4);
// var(--color-feedback-info-1);
// var(--color-action-default-able-5);
// var(--color-action-default-able-4);
// var(--color-typography-default-text) !important;
// var(--color-background-plane-3);
// var(--color-action-default-able-5) !important;
// var(--color-feedback-info-1) !important;
// var(--color-action-default-able-1);
// var(--color-action-default-able-1);
// var(--color-action-default-able-1);
// var(--color-action-default-disable-2) !important;
// var(--color-action-default-disable-2) !important;
// var(--color-feedback-info-1);
// var(--color-action-default-able-5);
// var(--color-action-default-able-4);
// var(--color-action-default-disable-2) !important;
::ng-deep {
	#side-filter {
		padding: 0;
	}
}

.ds3-filter-chips {
	display: flex;
	flex-wrap: wrap;
	gap: 4px 8px;
	margin-bottom: 8px;
}

.dialog {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.dialog-head {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		background: var(--color-background-plane-2);
		border-bottom: 1px solid var(--color-support-gray-3);
		padding: 16px 16px 7px 16px;

		.dialog-title {
			@include apply-typography-style("title", 4);
			color: var(--color-typography-default-title);
		}
		button {
			margin-right: 8px;
		}
	}

	.dialog-content {
		height: 100%;
		padding: 8px 16px;
		margin-top: 16px;
		gap: 16px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		overflow-x: hidden;
	}

	.dialog-actions {
		background-color: var(--color-background-plane-1);
		display: grid;
		gap: 16px;
		grid-template-columns: 1fr 1fr;
		padding: 8px 16px;
	}
}

::ng-deep {
	.timepicker-overlay {
		z-index: 10000000 !important;
	}

	.timepicker-backdrop-overlay {
		z-index: 10000000 !important;
	}
}
