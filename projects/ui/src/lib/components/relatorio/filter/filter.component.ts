import { DataGridFilterComponent } from "./../data-grid-filter/data-grid-filter.component";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	ViewChild,
	ElementRef,
	HostListener,
	TemplateRef,
} from "@angular/core";
import { GridFilterConfig } from "../data-grid-filter.model";
import { MatDialog } from "@angular/material";
import { Ds3SideFilterComponent } from "../ds3-side-filter/ds3-side-filter.component";

@Component({
	selector: "pacto-filter",
	templateUrl: "./filter.component.html",
	styleUrls: ["./filter.component.scss"],
})
export class FilterComponent implements OnInit {
	@Input() filterConfig: GridFilterConfig;
	@Output() filterChange: EventEmitter<any> = new EventEmitter();
	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("filterToggleButton", { static: false })
	filterToggleButton: ElementRef;
	@ViewChild("dataGridFilter", { static: false })
	dataGridFilter: DataGridFilterComponent;

	constructor(private modal: MatDialog) {}

	ngOnInit() {}

	filterHandler(filter) {
		this.filterChange.emit(filter);
	}

	@HostListener("document:click", ["$event.target"])
	public clickHandler(targetElement) {
		const filter = this.filterConfig;
		if (filter) {
			const filterBtnClick = this.isDescendant(
				this.filterToggleButton.nativeElement,
				targetElement
			);
			const filterClick = this.isDescendant(
				this.dataGridFilter.nativeElement,
				targetElement
			);
			if (!filterBtnClick && !filterClick) {
				this.filterDropdown.close();
			}
		}
	}

	private isDescendant(parent, child) {
		let node = child.parentNode;
		if (parent === child) {
			return true;
		} else {
			while (node !== null) {
				if (node === parent) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	alterfilterConfigUpdate(statusConfid) {
		this.filterConfigUpdate.emit(statusConfid);
	}

	public close() {
		this.filterDropdown.close();
	}
}
