import { TemplateRef } from "@angular/core";

export interface GridFilterConfig {
	filters?: Array<GridFilterMany | GridFilter>;
	configs?: Array<GridConfig>;
}

/**
 * FILTERS
 */
export enum GridFilterType {
	MANY,
	DATE_POINT,
	DATE_POINT_CAT, // Usa o pacto-cat-form-datepicker
	DATE_MONTH_YEAR,
	ONE_CHOICE,
	DS3_INPUT,
	DS3_SELECT_MANY,
	DS3_SELECT_ONE,
	DS3_DATE,
	DS3_TIME,
	DS3_TIME_RANGE,
	DS3_DATE_RANGE,
	DS3_CHIPS,
	DS3_CHECKBOX,
}

export interface GridFilter {
	name: string;
	nameDateRangeStart?: string;
	nameDateRangeEnd?: string;
	type?: GridFilterType;
	label?: TemplateRef<any> | string;
	selectPlaceholder?: string;
	initialValue?: Array<number> | number | Array<string> | string | boolean;
	valueTransform?: (value: any) => any;
	transformDateString?: boolean;
	showInputType?: boolean;
}

/**
 * FILTER MANY
 */
export interface GridFilterMany extends GridFilter {
	options: Array<GridFilterManyOption>;
	alternativeOptions: Array<GridFilterManyOption>;
	/**
	 * A template that expects a variable 'name'
	 * and uses it to translate into the proper language.
	 * The key passed in as 'name' will be the
	 * 'value' of the option.
	 */
	translator?: TemplateRef<any>;
	alternativeOptionsLabel?: TemplateRef<any>;
}

export interface GridConfig {
	id: any;
	label: TemplateRef<any> | string;
	initialValue?: boolean;
	cleanParams?: Array<string>;
}

export interface GridFilterManyOption {
	value: any;
	label: any;
}
