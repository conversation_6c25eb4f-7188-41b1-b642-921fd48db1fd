import {
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

import {
	GridFilter,
	GridFilterConfig,
	GridFilterMany,
	GridFilterType,
} from "../data-grid-filter.model";
import { PactoDataGridColumnConfig } from "../data-grid.model";

declare var moment;

@Component({
	// tslint:disable-next-line:component-selector
	selector: "pacto-data-grid-filter",
	templateUrl: "./data-grid-filter.component.html",
	styleUrls: ["./data-grid-filter.component.scss"],
})
export class DataGridFilterComponent implements OnInit {
	@Input() config: GridFilterConfig;
	@Input() columnsConfig: PactoDataGridColumnConfig;
	@Input() idSuffix;
	@Output() filter: EventEmitter<any> = new EventEmitter();
	@Output() configUpdate: EventEmitter<any> = new EventEmitter();

	constructor(private element: ElementRef, private cd: ChangeDetectorRef) {}

	get nativeElement() {
		return this.element.nativeElement;
	}

	filterCollapseStatus = {};
	filterManyFilterControls: { [name: string]: FormControl } = {};
	filterMonthYearFilterControls: { [name: string]: FormControl } = {};
	/**
	 * Contains the currently selectd values of the filters.
	 */
	filterSelectionStatus = {};
	filterDateControls: { [name: string]: FormControl } = {};
	alternativeOptionsControl: FormControl = new FormControl(false);
	configOptionsControl: FormGroup = new FormGroup({});
	formGroup: FormGroup = new FormGroup({
		dateSelected: new FormControl(0),
		anoSelected: new FormControl(0),
	});
	mesData = [];
	mesAtual: number;
	anoData = [];
	anoAtual: number;

	ngOnInit() {
		this.config.filters.forEach((filter) => {
			if (filter.type === GridFilterType.MANY) {
				this.filterManyFilterControls[filter.name] = new FormControl("");
			}
		});

		this.getDateCurrent();
		this.initStatusVariable();
		this.setUpDatepickerControls();
		this.setUpDateMonthYear();
		this.setUpFilterManyFilter();
		this.setUpFilterOneChoice();
		this.getMesesData();
	}

	changeMonth() {
		this.formGroup
			.get("dateSelected")
			.setValue(this.formGroup.get("dateSelected").value);
	}

	changeYear() {
		this.formGroup
			.get("anoSelected")
			.setValue(this.formGroup.get("anoSelected").value);

		const anoAtual = new Date(moment().subtract("year"));
		if (
			Number(this.formGroup.get("anoSelected").value) ===
			anoAtual.getUTCFullYear()
		) {
			const newDate = new Date(moment().subtract("month"));
			this.setMesesAnoIncompleto(newDate.getMonth() + 1);
		} else {
			this.setMesesAnoCompleto();
		}
		this.cd.detectChanges();
	}

	getDateCurrent() {
		this.mesAtual = new Date().getMonth() + 1;
		this.anoAtual = new Date().getFullYear();
		this.formGroup.get("dateSelected").setValue(this.mesAtual);
		this.formGroup.get("anoSelected").setValue(this.anoAtual);
	}

	get GridFilterType() {
		return GridFilterType;
	}

	getFilterOptions(filter) {
		if (filter.alternativeOptions && this.alternativeOptionsControl.value) {
			return filter.alternativeOptions;
		} else {
			return filter.options;
		}
	}

	removeFilter(
		filterType: GridFilterType,
		filterName: string,
		filterValue: string | Date | number
	) {
		if (
			filterType === GridFilterType.MANY ||
			filterType === GridFilterType.ONE_CHOICE
		) {
			this.filterSelectionStatus[filterName] = this.filterSelectionStatus[
				filterName
			].filter((value) => value !== filterValue);
		} else {
			this.filterDateControls[filterName].reset();
		}
	}

	addFilter(
		filterType: GridFilterType,
		filterName: string,
		filterValue: string | Date | number
	) {
		if (
			filterType === GridFilterType.MANY ||
			filterType === GridFilterType.ONE_CHOICE
		) {
			this.filterSelectionStatus[filterName].push(filterValue);
		} else {
			if (filterValue instanceof Date) {
				const dateFilterValue = filterValue as Date;
				this.filterDateControls[filterName].setValue({
					day: dateFilterValue.getDay(),
					month: dateFilterValue.getMonth(),
					year: dateFilterValue.getFullYear(),
				});
			} else {
				console.error(
					`GridFilterType ${GridFilterType[filterType]} allow only date object`
				);
			}
		}
	}

	clearFilter($event: MouseEvent, filter: GridFilter) {
		$event.stopPropagation();
		if (
			filter.type === GridFilterType.MANY ||
			filter.type === GridFilterType.ONE_CHOICE
		) {
			this.filterSelectionStatus[filter.name] = [];
		} else {
			this.filterDateControls[filter.name].reset();
		}
	}

	clearFilterManyFilter($event: MouseEvent, filter: GridFilter) {
		$event.stopPropagation();
		this.filterManyFilterControls[filter.name].setValue("");
	}

	filterClick(filter: GridFilter) {
		this.filterCollapseStatus[filter.name] =
			!this.filterCollapseStatus[filter.name];
	}

	filterHasValue(filter: GridFilter) {
		return this.filterSelectionStatus[filter.name]
			? this.filterSelectionStatus[filter.name].length
			: 0;
	}

	clickManyOptionHandler(filter: GridFilterMany, value: number) {
		const values: Array<any> = this.filterSelectionStatus[filter.name];
		if (values.includes(value)) {
			const removeIndex = values.findIndex((i) => {
				return i === value;
			});
			values.splice(removeIndex, 1);
		} else {
			values.push(value);
		}
	}

	clickOneChoiceOptionHandler(filter: GridFilterMany, value: number) {
		const values: Array<any> = this.filterSelectionStatus[filter.name];
		if (values.length > 0) {
			const existsValue = !values.includes(value);
			values.splice(0, 1);
			if (existsValue) {
				values.push(value);
			}
		} else {
			values.push(value);
		}
	}

	visibleColumnClickHandler(column: PactoDataGridColumnConfig) {
		if (column.visible != null) {
			column.visible = !column.visible;
		} else {
			column.visible = !column.defaultVisible;
		}
	}

	columnIsVisible(column: PactoDataGridColumnConfig) {
		if (column.visible != null) {
			return column.visible;
		} else {
			return column.defaultVisible;
		}
	}

	searchClickHandler() {
		this.emitFilterData();
	}

	setFilterSelectionStatus(filters: any) {
		for (const key in filters) {
			if (this.filterDateControls.hasOwnProperty(key)) {
				this.filterDateControls[key].setValue(filters[key]);
			}
			if (this.filterSelectionStatus.hasOwnProperty(key)) {
				this.filterSelectionStatus[key] = filters[key];
			}
		}
	}

	getFilterData() {
		const filters = Object.assign({}, this.filterSelectionStatus);
		for (const key in this.filterDateControls) {
			const ownProperty = Object.prototype.hasOwnProperty.call(
				this.filterDateControls,
				key
			);
			if (ownProperty) {
				const element = this.filterDateControls[key];
				filters[key] = element.value;
			}
		}
		const result: any = {};
		result.filters = filters;
		result.configs = this.configOptionsControl.getRawValue();
		return result;
	}

	alterConfigUpdate(configId) {
		this.config.configs.forEach((config) => {
			if (config.id === configId) {
				config.cleanParams.forEach((nameFilter) => {
					this.filterSelectionStatus[nameFilter] = [];
				});
			}
		});
		this.configUpdate.emit({
			clickConfigId: configId,
			configsValue: this.configOptionsControl.getRawValue(),
		});
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else if (type === "string") {
			return false;
		} else {
			return true;
		}
	}

	private setUpFilterManyFilter() {
		this.config.filters.forEach((filter) => {
			if (filter.type === GridFilterType.MANY) {
				this.filterManyFilterControls[filter.name] = new FormControl("");
			}
		});
	}

	private setUpFilterOneChoice() {
		this.config.filters.forEach((filter) => {
			if (filter.type === GridFilterType.ONE_CHOICE) {
				this.filterManyFilterControls[filter.name] = new FormControl("");
			}
		});
	}

	private setUpDateMonthYear() {
		this.config.filters.forEach((filter) => {
			if (filter.type === GridFilterType.DATE_MONTH_YEAR) {
				const mes = this.formGroup.get("dateSelected").value;
				const ano = this.formGroup.get("anoSelected").value;
				this.filterSelectionStatus[filter.name] = [];
				const values: Array<any> = this.filterSelectionStatus[filter.name];
				values.push({ mes, ano });
			}
		});
	}

	private setUpDatepickerControls() {
		this.config.filters.forEach((filter) => {
			if (
				filter.type === GridFilterType.DATE_POINT ||
				filter.type === GridFilterType.DATE_POINT_CAT
			) {
				this.filterDateControls[filter.name] = new FormControl(
					filter.initialValue
				);
			}
		});
	}

	initStatusVariable() {
		this.config.filters.forEach((filter) => {
			if (filter.type === GridFilterType.DATE_POINT) {
				this.filterSelectionStatus[filter.name] = null;
			} else {
				this.filterSelectionStatus[filter.name] = filter.initialValue
					? filter.initialValue
					: [];
			}
		});
		if (this.config.configs) {
			this.config.configs.forEach((config) => {
				this.configOptionsControl.addControl(
					config.id,
					new FormControl(config.initialValue ? config.initialValue : false)
				);
			});
		}
	}

	private emitFilterData() {
		this.setUpDateMonthYear();
		const result = this.getFilterData();
		this.filter.emit(result);
	}

	setMesesArray() {
		for (let i = 1; i < 7; i++) {
			const newDate = new Date(moment().subtract(i, "month"));
			const newYear = new Date(moment().subtract(i, "year"));
			this.anoData.push({ ano: newYear.getUTCFullYear(), id: i });
			if (i === 1) {
				this.setMesesAnoIncompleto(newDate.getMonth() + 1);
			}
		}
	}

	setMesesAnoIncompleto(mes: number) {
		this.mesData = [];
		for (let i = 1; i <= mes; i++) {
			const mesData = [];
			mesData.push({ mes: i, id: i });
			this.convertMonth(mesData);
		}
	}

	setMesesAnoCompleto() {
		this.mesData = [];
		for (let i = 1; i <= 12; i++) {
			const mesData = [];
			mesData.push({ mes: i, id: i });
			this.convertMonth(mesData);
		}
	}

	convertMonth(mesData) {
		mesData.forEach((mes) => {
			if (mes.mes === 1) {
				this.mesData.push({ id: mes.mes, mes: "Jan" });
			}
			if (mes.mes === 2) {
				this.mesData.push({ id: mes.mes, mes: "Fev" });
			}
			if (mes.mes === 3) {
				this.mesData.push({ id: mes.mes, mes: "Mar" });
			}
			if (mes.mes === 4) {
				this.mesData.push({ id: mes.mes, mes: "Abr" });
			}
			if (mes.mes === 5) {
				this.mesData.push({ id: mes.mes, mes: "Mai" });
			}
			if (mes.mes === 6) {
				this.mesData.push({ id: mes.mes, mes: "Jun" });
			}
			if (mes.mes === 7) {
				this.mesData.push({ id: mes.mes, mes: "Jul" });
			}
			if (mes.mes === 8) {
				this.mesData.push({ id: mes.mes, mes: "Ago" });
			}
			if (mes.mes === 9) {
				this.mesData.push({ id: mes.mes, mes: "Set" });
			}
			if (mes.mes === 10) {
				this.mesData.push({ id: mes.mes, mes: "Out" });
			}
			if (mes.mes === 11) {
				this.mesData.push({ id: mes.mes, mes: "Nov" });
			}
			if (mes.mes === 12) {
				this.mesData.push({ id: mes.mes, mes: "Dez" });
			}
		});
	}

	getMesesData() {
		this.setMesesArray();
	}

	get haveAnDs3(): boolean {
		return this.config.filters.includes((v) => {
			return (
				v.type === GridFilterType.DS3_INPUT ||
				v.type === GridFilterType.DS3_SELECT_MANY ||
				v.type === GridFilterType.DS3_SELECT_ONE ||
				v.type === GridFilterType.DS3_CHIPS ||
				v.type === GridFilterType.DS3_DATE ||
				v.type === GridFilterType.DS3_TIME ||
				v.type === GridFilterType.DS3_TIME_RANGE ||
				v.type === GridFilterType.DS3_DATE_RANGE
			);
		});
	}
}
