@import "projects/ui/assets/import.scss";

.grid-filter-wrapper {
	padding: 10px 15px 5px 15px;
	width: 350px;
}

.filter-input {
	position: relative;

	input {
		padding-right: 35px;
	}

	i.pct {
		position: absolute;
		cursor: pointer;
		color: #888;
		right: 10px;
		top: 7px;
	}
}

.options-config {
	padding: 10px 0px 10px 5px;
	border-top: 1px solid #e6e6e6;

	.form-check-label {
		cursor: pointer;
	}

	&:hover {
		background-color: rgb(245, 245, 245);
	}
}

.filter {
	border-top: 1px solid #e6e6e6;

	.filter-title {
		color: #5a5a5a;
		font-size: 15px;
		line-height: 20px;
		position: relative;
		font-weight: bold;

		.clear-filter {
			position: absolute;
			top: 14px;
			right: 6px;
			font-weight: bold;
			font-size: 12px;
		}

		.aux-wrapper {
			display: block;
			cursor: pointer;
			padding: 10px 0px 10px 5px;

			&:hover {
				background-color: rgb(245, 245, 245);
			}
		}

		i.pct {
			padding-right: 5px;
		}
	}

	.filter-content {
		margin-top: 0px;
		padding: 0px 5px;
	}

	.monthYear {
		margin-bottom: 10px;
	}

	&.filter {
		.options-wrapper {
			margin-top: 12px;
			margin-bottom: 20px;
			display: flex;
			flex-wrap: wrap;
		}

		.option {
			padding: 1px 10px;
			margin-right: 2px;
			line-height: 24px;
			font-size: 13px;
			color: #444;
			border: 1px solid #d8d8d8;
			border-radius: 13px;
			font-weight: bold;
			display: block;
			cursor: pointer;

			&.selected {
				background-color: #f1f1f1;
				color: #ffffff;
				background-color: $azulim06;
			}
		}
	}
}

.search-btn-filter {
	width: 100%;
	margin-top: 10px;

	i.pct {
		padding-right: 10px;
	}
}

::ng-deep.datepicker {
	.nome {
		display: none;
	}

	.pct-error-msg {
		display: none;
	}
}

.side-filter {
	display: flex;
	align-content: space-between;
	height: 100%;
	justify-content: flex-start;
	align-items: center;
	flex-direction: column;

	.side-filter-title {
		height: 5%;
		width: 100%;
		position: static;
		display: flex;
		align-items: baseline;
		justify-content: space-between;
		flex-direction: row;
		margin-bottom: 4px;
	}

	.side-filter-content {
		height: auto;
		width: 100%;
		overflow: auto;
		padding: 8px 0px;

		pacto-cat-form-input::ng-deep input,
		pacto-cat-multi-select-filter::ng-deep .current-value {
			border-radius: 8px;
			border-color: var(--color-support-gray-3);
			background-color: var(--color-background-plane-2);
		}

		.colunas-visiveis {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: center;
			flex-direction: row;
			gap: 6px;

			pacto-cat-button {
				width: calc(50% - 6px);
			}
		}
	}

	.side-filter-actions {
		height: 5%;
		margin-top: 12px;
		width: 100%;
		position: static;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		gap: 6px;

		button {
			width: 100%;
		}
	}
}
