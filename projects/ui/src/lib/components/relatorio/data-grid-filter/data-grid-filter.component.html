<div class="grid-filter-wrapper">
	<div *ngFor="let config of config.configs">
		<div class="options-config">
			<div class="form-check">
				<input
					(click)="alterConfigUpdate(config.id)"
					[formControl]="configOptionsControl.get(config.id)"
					[id]="idSuffix ? config.id + '-' + idSuffix : config.id"
					class="form-check-input"
					type="checkbox"
					value="" />
				<label
					[for]="idSuffix ? config.id + '-' + idSuffix : config.id"
					class="form-check-label">
					<ng-container [ngTemplateOutlet]="config.label"></ng-container>
				</label>
			</div>
		</div>
	</div>
	<ng-container *ngFor="let filter of config.filters">
		<ng-container [ngSwitch]="filter.type">
			<div *ngSwitchCase="GridFilterType.MANY" class="filter filter-many">
				<div class="filter-title">
					<span
						(click)="filterClick(filter)"
						[id]="
							idSuffix
								? 'filter-title-' + filter.name + '-' + idSuffix
								: 'filter-title-' + filter.name
						"
						class="aux-wrapper">
						<i
							[hidden]="filterCollapseStatus[filter.name]"
							class="pct pct-caret-right"></i>
						<i
							[hidden]="!filterCollapseStatus[filter.name]"
							class="pct pct-caret-down"></i>
						<ng-container
							*ngTemplateOutlet="
								templateOrText;
								context: { item: filter.label }
							"></ng-container>
					</span>
					<span
						(click)="clearFilter($event, filter)"
						*ngIf="filterHasValue(filter)"
						[id]="
							idSuffix
								? 'clear-filter-' + filter.name + '-' + idSuffix
								: 'clear-filter-' + filter.name
						"
						class="clear-filter">
						{{ "filter.limpar" | translate }}
					</span>
				</div>
				<div *ngIf="filterCollapseStatus[filter.name]" class="filter-content">
					<div
						*ngIf="filter.alternativeOptions"
						class="filter-alternative-control form-check">
						<input
							[formControl]="alternativeOptionsControl"
							[id]="
								idSuffix ? filter.name + '-id-' + idSuffix : filter.name + '-id'
							"
							class="form-check-input"
							type="checkbox" />
						<label
							[for]="
								idSuffix ? filter.name + '-id-' + idSuffix : filter.name + '-id'
							"
							class="form-check-label">
							<ng-container
								*ngTemplateOutlet="
									filter.alternativeOptionsLabel
								"></ng-container>
						</label>
					</div>

					<div class="filter-input" *ngIf="!filter.showInputType">
						<input
							[formControl]="filterManyFilterControls[filter.name]"
							[id]="
								idSuffix
									? 'filter-input-search-' + filter.name + '-' + idSuffix
									: 'filter-input-search-' + filter.name
							"
							class="form-control form-control-sm"
							placeholder="{{ 'filter.filtrarPorNome' | translate }}"
							type="text" />
						<i
							(click)="clearFilterManyFilter($event, filter)"
							class="pct pct-x"></i>
					</div>

					<div class="options-wrapper">
						<span
							(click)="clickManyOptionHandler(filter, option.value)"
							*ngFor="
								let option of getFilterOptions(filter)
									| filter : filterManyFilterControls[filter.name].value;
								let index = index
							"
							[id]="
								idSuffix
									? 'filter-option-' +
									  filter.name +
									  '-' +
									  idSuffix +
									  '-' +
									  index
									: 'filter-option-' + filter.name + '-' + index
							"
							[ngClass]="{
								selected: filterSelectionStatus[filter.name].includes(
									option.value
								)
							}"
							class="option">
							<ng-container *ngIf="filter.translator">
								<ng-container
									*ngTemplateOutlet="
										filter.translator;
										context: { name: option.value }
									"></ng-container>
							</ng-container>
							<span *ngIf="!filter.translator">{{ option.label }}</span>
						</span>
					</div>
				</div>
			</div>

			<div
				*ngSwitchCase="GridFilterType.ONE_CHOICE"
				class="filter filter-one-choice">
				<div class="filter-title">
					<span
						(click)="filterClick(filter)"
						[id]="
							idSuffix
								? 'filter-title-' + filter.name + '-' + idSuffix
								: 'filter-title-' + filter.name
						"
						class="aux-wrapper">
						<i
							[hidden]="filterCollapseStatus[filter.name]"
							class="pct pct-caret-right"></i>
						<i
							[hidden]="!filterCollapseStatus[filter.name]"
							class="pct pct-caret-down"></i>
						<ng-container
							*ngTemplateOutlet="
								templateOrText;
								context: { item: filter.label }
							"></ng-container>
					</span>
					<span
						(click)="clearFilter($event, filter)"
						*ngIf="filterHasValue(filter)"
						class="clear-filter"
						id="clear-filter-{{ filter.name }}">
						{{ "filter.limpar" | translate }}
					</span>
				</div>

				<div *ngIf="filterCollapseStatus[filter.name]" class="filter-content">
					<div
						*ngIf="filter.alternativeOptions"
						class="filter-alternative-control form-check">
						<input
							[formControl]="alternativeOptionsControl"
							[id]="
								idSuffix
									? filter.name + '-id-one-choice-' + idSuffix
									: filter.name + '-id-one-choice'
							"
							class="form-check-input"
							type="checkbox" />
						<label
							[for]="
								idSuffix
									? filter.name + '-id-one-choice-' + idSuffix
									: filter.name + '-id-one-choice'
							"
							class="form-check-label">
							<ng-container
								*ngTemplateOutlet="
									filter.alternativeOptionsLabel
								"></ng-container>
						</label>
					</div>

					<div class="filter-input">
						<input
							[formControl]="filterManyFilterControls[filter.name]"
							[id]="
								idSuffix
									? 'filter-input-search-' + filter.name + '-' + idSuffix
									: 'filter-input-search-' + filter.name
							"
							class="form-control form-control-sm"
							placeholder="Filtrar por nome..."
							type="text" />
						<i
							(click)="clearFilterManyFilter($event, filter)"
							class="pct pct-x"></i>
					</div>

					<div class="options-wrapper">
						<span
							(click)="clickOneChoiceOptionHandler(filter, option.value)"
							*ngFor="
								let option of getFilterOptions(filter)
									| filter : filterManyFilterControls[filter.name].value;
								let index = index
							"
							[id]="
								idSuffix
									? 'filter-option-' +
									  filter.name +
									  '-' +
									  index +
									  '-' +
									  idSuffix
									: 'filter-option-' + filter.name + '-' + index
							"
							[ngClass]="{
								selected: filterSelectionStatus[filter.name].includes(
									option.value
								)
							}"
							class="option">
							<ng-container *ngIf="filter.translator">
								<ng-container
									*ngTemplateOutlet="
										filter.translator;
										context: { name: option.value }
									"></ng-container>
							</ng-container>
							<span *ngIf="!filter.translator">{{ option.label }}</span>
						</span>
					</div>
				</div>
			</div>

			<div
				*ngSwitchCase="GridFilterType.DATE_POINT"
				class="filter filter-date-point">
				<div class="filter-title">
					<span
						(click)="filterClick(filter)"
						[id]="
							idSuffix
								? 'filter-title-' + filter.name + '-' + idSuffix
								: 'filter-title-' + filter.name
						"
						class="aux-wrapper">
						<i
							[hidden]="filterCollapseStatus[filter.name]"
							class="pct pct-caret-right"></i>
						<i
							[hidden]="!filterCollapseStatus[filter.name]"
							class="pct pct-caret-down"></i>
						<ng-container
							*ngTemplateOutlet="
								templateOrText;
								context: { item: filter.label }
							"></ng-container>
					</span>
					<span
						(click)="clearFilter($event, filter)"
						*ngIf="filterDateControls[filter.name].value"
						[id]="
							idSuffix
								? 'clear-filter-' + filter.name + '-' + idSuffix
								: 'clear-filter-' + filter.name
						"
						class="clear-filter">
						{{ "filter.limpar" | translate }}
					</span>
				</div>
				<div *ngIf="filterCollapseStatus[filter.name]" class="filter-content">
					<pacto-datepicker
						[control]="filterDateControls[filter.name]"
						[id]="'filter-option-date-' + filter.name"></pacto-datepicker>
				</div>
			</div>

			<div
				*ngSwitchCase="GridFilterType.DATE_POINT_CAT"
				class="filter filter-date-point">
				<div class="filter-title">
					<span
						(click)="filterClick(filter)"
						[id]="
							idSuffix
								? 'filter-title-' + filter.name + '-' + idSuffix
								: 'filter-title-' + filter.name
						"
						class="aux-wrapper">
						<i
							[hidden]="filterCollapseStatus[filter.name]"
							class="pct pct-caret-right"></i>
						<i
							[hidden]="!filterCollapseStatus[filter.name]"
							class="pct pct-caret-down"></i>
						<ng-container
							*ngTemplateOutlet="
								templateOrText;
								context: { item: filter.label }
							"></ng-container>
					</span>
					<span
						(click)="clearFilter($event, filter)"
						*ngIf="filterDateControls[filter.name].value"
						[id]="
							idSuffix
								? 'clear-filter-' + filter.name + '-' + idSuffix
								: 'clear-filter-' + filter.name
						"
						class="clear-filter">
						{{ "filter.limpar" | translate }}
					</span>
				</div>
				<div *ngIf="filterCollapseStatus[filter.name]" class="filter-content">
					<pacto-cat-form-datepicker
						[control]="filterDateControls[filter.name]"
						[id]="
							idSuffix
								? 'filter-option-date-' + filter.name + '-' + idSuffix
								: 'filter-option-date-' + filter.name
						"
						class="datepicker"></pacto-cat-form-datepicker>
				</div>
			</div>

			<div
				*ngSwitchCase="GridFilterType.DATE_MONTH_YEAR"
				class="filter filter-date-point">
				<div class="filter-title">
					<span
						(click)="filterClick(filter)"
						[id]="
							idSuffix
								? 'filter-title-' + filter.name + '-' + idSuffix
								: 'filter-title-' + filter.name
						"
						class="aux-wrapper">
						<i
							[hidden]="filterCollapseStatus[filter.name]"
							class="pct pct-caret-right"></i>
						<i
							[hidden]="!filterCollapseStatus[filter.name]"
							class="pct pct-caret-down"></i>
						<ng-container
							*ngTemplateOutlet="
								templateOrText;
								context: { item: filter.label }
							"></ng-container>
					</span>
				</div>
				<div *ngIf="filterCollapseStatus[filter.name]" class="filter-content">
					<div class="monthYear">
						<div class="container">
							<div class="row">
								<div class="col-6">
									<select
										(change)="changeMonth()"
										[formControl]="formGroup.get('dateSelected')"
										[id]="
											idSuffix
												? 'filter-month-select-' + filter.name + '-' + idSuffix
												: 'filter-month-select-' + filter.name
										"
										class="form-control form-control-sm">
										<option [value]="mesAtual">
											{{ "filter.mesAtual" | translate }}
										</option>
										<option *ngFor="let mes of mesData" value="{{ mes.id }}">
											{{ mes.mes }}
										</option>
									</select>
								</div>
								<div class="col-6">
									<select
										(change)="changeYear()"
										[formControl]="formGroup.get('anoSelected')"
										[id]="
											idSuffix
												? 'filter-year-select-' + filter.name + '-' + idSuffix
												: 'filter-year-select-' + filter.name
										"
										class="form-control form-control-sm">
										<option [value]="anoAtual">{{ anoAtual }}</option>
										<option *ngFor="let ano of anoData" value="{{ ano.ano }}">
											{{ ano.ano }}
										</option>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</ng-container>
	</ng-container>

	<div *ngIf="columnsConfig" class="filter">
		<div
			(click)="filterClick({ name: 'columns' })"
			[id]="
				idSuffix ? 'filter-title-column-' + idSuffix : 'filter-title-column'
			"
			class="filter-title">
			<span class="aux-wrapper">
				<i
					[hidden]="filterCollapseStatus['columns']"
					class="pct pct-caret-right"></i>
				<i
					[hidden]="!filterCollapseStatus['columns']"
					class="pct pct-caret-down"></i>
				<span>{{ "filter.colunasVisiveis" | translate }}</span>
			</span>
		</div>
		<div *ngIf="filterCollapseStatus['columns']" class="filter-content">
			<div class="options-wrapper">
				<span
					(click)="visibleColumnClickHandler(column)"
					*ngFor="let column of columnsConfig; let index = index"
					[id]="
						idSuffix
							? 'filter-columns-options-' + index + '-' + idSuffix
							: 'filter-columns-options-' + index
					"
					[ngClass]="{ selected: columnIsVisible(column) }"
					class="option">
					<ng-container
						*ngTemplateOutlet="
							templateOrText;
							context: { item: column.titulo }
						"></ng-container>
				</span>
			</div>
		</div>
	</div>

	<div *ngIf="!haveAnDs3" class="search-button">
		<button
			(click)="searchClickHandler()"
			[id]="
				idSuffix ? 'filter-search-button-' + idSuffix : 'filter-search-button'
			"
			class="btn btn-primary search-btn-filter">
			<i class="pct pct-search"></i>
			<span>{{ "filter.buscar" | translate }}</span>
		</button>
	</div>
</div>

<ng-template #templateOrText let-item="item">
	<ng-container *ngIf="isTemplate(item)">
		<ng-container *ngTemplateOutlet="item"></ng-container>
	</ng-container>
	<ng-container *ngIf="!isTemplate(item)">
		{{ item }}
	</ng-container>
</ng-template>
