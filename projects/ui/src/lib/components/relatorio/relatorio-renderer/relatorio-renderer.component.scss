@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";

table.table {
	margin-bottom: 0px;

	tbody {
		&.pct-table-zebra {
			tr:nth-child(odd) {
				background-color: #fafafa;
			}

			td {
				border: 0;
			}
		}
	}

	th {
		@extend .type-btn-bold;
		text-transform: capitalize;
		color: $pretoPri;

		&.sortable {
			cursor: pointer;

			i.pct {
				padding-left: 10px;
				color: $pretoPri;
			}
			button[ds3-icon-button] {
				margin-left: 4px;
				i.pct {
					padding: 0px;
				}
			}
		}
	}

	tr {
		@extend .type-p-small;
		color: $preto02;
	}

	tr.row-clickable:not(.ghost) {
		cursor: pointer;
	}

	tr.total-row td {
		font-weight: bold;
	}

	tr.selected {
		background-color: #eff2f7;
	}

	.column-cell.hover-cell:hover {
		cursor: pointer;
		text-decoration: underline;
	}

	td {
		&.center {
			text-align: center;
		}

		&.right {
			text-align: right;
		}
	}

	th {
		&.center {
			text-align: center;
		}

		&.right {
			text-align: right;
		}
	}

	&.ds3-table {
		th,
		th span,
		th span span {
			@extend .pct-title5;
			font-family: $fontPoppins !important;
			color: $typeDefaultTitle;
		}

		th {
			&.sortable {
				i.pct {
					color: $actionDefaultAble04;
				}
			}
		}

		.column-cell {
			display: inline-block;
			text-transform: lowercase;

			&::first-letter {
				text-transform: uppercase;
			}
		}

		td,
		th {
			height: 40px;
			padding: 7px max(5.3px, 0.75 * 10px);
		}

		td {
			@extend .pct-overline1-regular;
			color: $typeDefaultText;
		}
		thead {
			th {
				span {
					@extend .pct-title4;
					color: $typeDefaultTitle;
				}
				border-width: 1px;
				border-color: $supportGray02;
			}
		}
	}
}

.row-colored-first > tbody > tr:nth-child(odd) {
	background: #fafafa;
}

.row-colored-second > tbody > tr:nth-child(even) {
	background: #eff2f7;
}

.column-ordelable-icon {
	width: 2%;
	text-align: center;
}

.loading-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;

	.loading-data-icon {
		width: 30px;
		position: relative;
		top: -1px;
	}
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

.ds3-empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;

	img {
		margin: 24px 0 16px;
	}

	h2 {
		margin-bottom: 16px;
		@extend .pct-title4;
		color: $typeDefaultTitle;
	}

	p {
		@extend .pct-body1;
		color: $typeDefaultText;
	}
}

.selectable {
	width: 10px;
	text-align: center;

	input {
		position: relative;
		margin-top: calc(50% + 5px);
		margin-left: -2px;
	}
}

.action-cell i {
	font-size: 16px;
}

.action-cell i.fa-trash-o {
	color: #db2c3d;
}

.action-cell i.pct {
	margin-right: 1rem;
	cursor: pointer;
}

.action-column {
	width: 100px;
}

.dropdown-toggle::after {
	display: none;
}

.first-line > th {
	border-top: 0;
}

hr.solid {
	width: 92%;
	margin: 2px 8px;
	cursor: default !important;
}

.item-dropdown {
	padding: 8px;
	line-height: 1;
	color: $cinza05;
}

.item-dropdown:hover {
	color: $azulimPri;
	background: $cinzaPastel;
}

.table-tabe {
	background-color: #fafafa; //$azulimPastel;
	width: 100%;
	padding-top: 25px;
	border: 0;

	tr {
		width: 100%;
		border: 0 !important;
	}

	td {
		border: 0 !important;
		border-top: 0;
	}
}

.table-tabe-inside {
	width: 100%;

	tr {
		width: 100%;
		border: 0 !important;
	}

	td {
		border: 0 !important;
		border-top: 0;
	}

	span {
		word-break: break-word;
		font-family: "Nunito Sans";
		font-style: normal;
	}

	.item-title {
		font-weight: 600;
		font-size: 16px;
		line-height: 22px;
	}

	.item-content {
		font-weight: 400;
		line-height: 26px;
	}

	.item-footer {
		font-weight: 400;
		font-size: 14px;
		line-height: 26px;
	}
}

.icon-color {
	color: #47adfd;
}

.icon-color-head {
	color: #0380e3;
}

tbody {
	&.pct-table-zebra {
		tr:nth-child(odd) {
			background-color: #fafafa;
		}
	}
}

.cdk-drop-list-dragging .cdk-drag {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
	background-color: $branco;
}

.cdk-drag-preview {
	box-sizing: border-box;
	border-radius: 4px;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
		0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
	padding: 4px;
	background-color: $branco;
}
