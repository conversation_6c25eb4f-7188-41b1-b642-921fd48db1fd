<div *ngIf="table" [class.ds3-relatorio]="enableDs3">
	<div
		*ngIf="
			table.quickSearch ||
			(tableTitle && !enableDs3) ||
			showShare ||
			(table.showFilters && filterConfig) ||
			table.buttons ||
			table.valueRowCheck
		"
		[id]="'tbl-title-block-' + idSuffix"
		class="pacto-table-title-block">
		<div *ngIf="tableTitle && !enableDs3" class="title-aux-wrapper">
			<div *ngIf="isTemplate(tableTitle)" class="table-title">
				<ng-container *ngTemplateOutlet="tableTitle"></ng-container>
			</div>
			<div *ngIf="!isTemplate(tableTitle)" class="table-title">
				{{ tableTitle }}
			</div>
			<div *ngIf="isTemplate(tableDescription)" class="table-description">
				<ng-container *ngTemplateOutlet="tableDescription"></ng-container>
			</div>
			<div *ngIf="!isTemplate(tableDescription)" class="table-description">
				{{ tableDescription }}
			</div>
		</div>

		<div *ngIf="table.valueRowCheck" class="command-buttons">
			<div (click)="selectAll()" class="command">
				<span class="icone"><i class="pct pct-check-square"></i></span>
				<span class="texto">SELECIONAR TODOS</span>
			</div>
			<div (click)="clearAll()" class="command">
				<span class="icone"><i class="pct pct-trash-2 color"></i></span>
				<span class="texto">LIMPAR SELEÇÃO</span>
			</div>
		</div>

		<div *ngIf="tableTitle && enableDs3" class="table-title-wrapper">
			<div *ngIf="isTemplate(tableTitle)" class="table-title">
				<ng-container *ngTemplateOutlet="tableTitle"></ng-container>
			</div>
			<div *ngIf="!isTemplate(tableTitle)" class="table-title">
				{{ tableTitle }}
			</div>
			<div *ngIf="isTemplate(tableDescription)" class="table-description">
				<ng-container *ngTemplateOutlet="tableDescription"></ng-container>
			</div>
			<div *ngIf="!isTemplate(tableDescription)" class="table-description">
				{{ tableDescription }}
			</div>
		</div>

		<div class="search">
			<div *ngIf="table.quickSearch">
				<ng-container *ngIf="!enableDs3">
					<input
						#quickSearch
						[formControl]="quickSearchControl"
						[id]="
							idSuffix ? 'input-busca-rapida-' + idSuffix : 'input-busca-rapida'
						"
						class="form-control"
						placeholder="{{
							quickSearchPlaceHolderCustom
								? quickSearchPlaceHolderCustom
								: ('relatorio.buscaPlaceholder' | translate)
						}}"
						type="text" />
					<i class="pct pct-search"></i>
				</ng-container>
				<ng-container *ngIf="enableDs3">
					<ds3-form-field>
						<input
							#quickSearch
							[formControl]="quickSearchControl"
							[id]="
								idSuffix
									? 'input-busca-rapida-' + idSuffix
									: 'input-busca-rapida'
							"
							ds3Input
							placeholder="{{
								quickSearchPlaceHolderCustom
									? quickSearchPlaceHolderCustom
									: ('relatorio.buscaPlaceholder' | translate)
							}}"
							type="search" />
					</ds3-form-field>
				</ng-container>
			</div>
		</div>

		<div class="actions">
			<div class="div-actions-btn">
				<div>
					<pacto-log
						*ngIf="table.logUrl"
						[ngClass]="{ 'margin-right-10': showBtnAdd }"
						[table]="true"
						[titulo]="tableTitle"
						[url]="table.logUrl"></pacto-log>
				</div>

				<div *ngIf="!btnAddRight">
					<pacto-cat-button
						(click)="btnAddClickEvent($event)"
						*ngIf="showBtnAdd && !enableDs3"
						[icon]="'pct pct-plus'"
						[id]="idSuffix ? 'btn-add-' + idSuffix : idBtnAdd"
						label="{{ labelBtnAdd }}"
						size="LARGE"
						type="PRIMARY_ADD"></pacto-cat-button>
					<button
						(click)="btnAddClickEvent($event)"
						*ngIf="showBtnAdd && enableDs3"
						[id]="idSuffix ? 'btn-add-' + idSuffix : idBtnAdd"
						ds3-flat-button>
						{{ labelBtnAdd }}
					</button>
				</div>

				<div
					*ngIf="showShare && data.content.length"
					class="share-button-tabela">
					<pacto-share-button
						[columns]="table.columns"
						[endpoint]="table.endpointUrl || this.endpointShare"
						[filterConfig]="filterConfig"
						[filtros]="allFilters()"
						[idSuffix]="idSuffix"
						[sessionService]="sessionService"
						[table]="true"
						[telaId]="telaId"
						[titulo]="tableTitle"
						[total]="data.totalElements"></pacto-share-button>
				</div>

				<div *ngIf="customActionsRight">
					<ng-container *ngTemplateOutlet="customActionsRight"></ng-container>
				</div>

				<div
					*ngIf="
						table.showFilters && (filterConfig || filterContent) && !enableDs3
					"
					class="filter-wrapper">
					<div
						#filterDropdown="ngbDropdown"
						[autoClose]="false"
						[placement]="'bottom-right'"
						class="d-inline-block"
						ngbDropdown
						style="height: 100%">
						<button
							#filterToggleButton
							[id]="
								idSuffix ? 'filtros-dropdown-' + idSuffix : 'filtros-dropdown'
							"
							[ngClass]="{ 'btn-filter': isAplicouFiltro == false }"
							class="btn btn-primary btn-icon"
							ngbDropdownToggle>
							<i class="pct pct-filter"></i>
							<span class="icon-drop">
								<i class="pct pct-chevron-down icon-drop-arrow"></i>
							</span>
						</button>

						<div aria-labelledby="filtros-dropdown" ngbDropdownMenu>
							<ng-content select="[filterContent]"></ng-content>
							<pacto-data-grid-filter
								#dataGridFilter
								(configUpdate)="alterfilterConfigUpdate($event)"
								(filter)="filterHandler($event)"
								*ngIf="!filterContent"
								[columnsConfig]="table.columns"
								[config]="filterConfig"
								[idSuffix]="idSuffix"></pacto-data-grid-filter>
						</div>
					</div>
				</div>
				<div
					*ngIf="
						table.showFilters && (filterConfig || filterContent) && enableDs3
					"
					class="filter-wrapper side-filter-button">
					<button
						(click)="openSideFilter()"
						[class.filterApplied]="!!sizeTemporaryFilters"
						ds3-outlined-button>
						<i
							[matBadgeOverlap]="false"
							[matBadge]="sizeTemporaryFilters"
							class="pct pct-filter side-filter-icon"
							matBadgePosition="after"></i>
					</button>
				</div>

				<div *ngIf="customActions">
					<ng-container *ngTemplateOutlet="customActions"></ng-container>
				</div>

				<div *ngIf="btnAddRight">
					<pacto-cat-button
						(click)="btnAddClickEvent($event)"
						*ngIf="showBtnAdd && !enableDs3"
						[icon]="'pct pct-plus'"
						[id]="idSuffix ? 'btn-add-' + idSuffix : idBtnAdd"
						class="left-10"
						label="{{ labelBtnAdd }}"
						size="LARGE"
						type="PRIMARY_ADD"></pacto-cat-button>
					<button
						(click)="btnAddClickEvent($event)"
						*ngIf="showBtnAdd && enableDs3"
						[id]="idSuffix ? 'btn-add-' + idSuffix : idBtnAdd"
						ds3-flat-button>
						{{ labelBtnAdd }}
					</button>
				</div>
			</div>

			<div class="div-table-button">
				<button
					(click)="btnCLickHandler()"
					*ngIf="table.buttons"
					[id]="idSuffix ? table.buttons.id + '-' + idSuffix : table.buttons.id"
					class="btn btn-primary novo-botao">
					<ng-container
						*ngTemplateOutlet="table.buttons.conteudo"></ng-container>
				</button>
			</div>
		</div>
	</div>
	<div class="overall" *ngIf="isOverallEnable">
		<div class="overall-filters">
			<ng-container *ngIf="filterOverall.length > 0">
				<span class="overall-filters-label">Filtros aplicados:</span>
				<ng-container *ngFor="let item of filterOverall; let index = index">
					<ds3-status
						class="overall-filters-status"
						color="outlined"
						*ngIf="!!item">
						<span
							[ds3Tooltip]="showTooltipDS3Status && item.label ? index : false"
							tooltipIndicator="top-center"
							tooltipPosition="bottom">
							{{ item.value }}
						</span>
					</ds3-status>
					<ng-template #index>
						<div class="overall-tooltip">
							<span class="overall-tooltip-label">{{ item.label }}</span>
							<ds3-diviser *ngIf="item.tooltipText.length > 0"></ds3-diviser>
							<ng-container *ngFor="let tooltip of item.tooltipText">
								<p class="overall-tooltip-extra">{{ tooltip }}</p>
							</ng-container>
						</div>
					</ng-template>
				</ng-container>
			</ng-container>
		</div>
		<div class="overall-entries">
			<ng-container *ngIf="totalItems != 0">
				<span class="overall-entries-label">Total:</span>
				<span class="overall-entries-value">{{ totalItems }}</span>
			</ng-container>
		</div>
	</div>

	<div *ngIf="this.table.customFilterTab" class="table-content">
		<div>
			<pacto-cat-tabs-transparent
				(activateTab)="btnClickFilter($event)"
				[tabsJustify]="true">
				<div *ngFor="let itemgroup of this.groupFilter | async">
					<div
						[id]="
							idSuffix
								? 'id-' + itemgroup.id + '-' + idSuffix
								: 'id-' + itemgroup.id
						">
						<ng-template
							[label]="itemgroup.descricao"
							[pactoTabTransparent]="itemgroup.id"
							[tabTotal]="itemgroup.total"></ng-template>
					</div>
				</div>
			</pacto-cat-tabs-transparent>
		</div>
	</div>
	<div class="table-content">
		<pacto-relatorio-renderer
			(cellClick)="cellClickHandler($event)"
			(iconClick)="iconClick.emit($event)"
			(reload)="reloadData()"
			(rowCheck)="checkSelectdItem($event)"
			(rowClick)="rowClick.emit($event)"
			(sort)="sortUpdateHandler()"
			[actionTitulo]="actionTitulo"
			[allowsCheck]="allowsCheck"
			[alternatingColors]="alternatingColors"
			[customContent]="customContent"
			[customEmptyContent]="customEmptyContent"
			[customRows]="customRows"
			[dataGridConfig]="table"
			[data]="rawData"
			[dropDownActionTitulo]="dropDownActionTitulo"
			[emptyStateDescription]="emptyStateDescription"
			[emptyStateMessage]="emptyStateMessage"
			[enableDs3]="enableDs3"
			[enableZebraStyle]="enableZebraStyle"
			[iconEnumHeaderField]="iconEnumHeaderField"
			[iconFieldFirstfooterright]="iconFieldFirstfooterright"
			[iconFieldHeader]="iconFieldHeader"
			[iconFieldSecondfooterright]="iconFieldSecondfooterright"
			[iconFieldfooterleft]="iconFieldfooterleft"
			[idSuffix]="idSuffix"
			[loading]="dataFetchLoading"
			[nameEnumHeaderField]="nameEnumHeaderField"
			[nameFieldFirstObjectfooterright]="nameFieldFirstObjectfooterright"
			[nameFieldFirstfooterright]="nameFieldFirstfooterright"
			[nameFieldHeader]="nameFieldHeader"
			[nameFieldSecondfooterright]="nameFieldSecondfooterright"
			[nameFieldfooterleft]="nameFieldfooterleft"
			[nameFirstFieldContent]="nameFirstFieldContent"
			[nameSecondFieldContent]="nameSecondFieldContent"
			[rawDataIcons]="rawDataIcons"
			[rawDropdownActionItems]="
				rawDropdownActionItems
			"></pacto-relatorio-renderer>

		<div
			*ngIf="table.showSimpleTotalCount && !dataFetchLoading"
			class="simple-total-row">
			{{ table.pagination ? data.totalElements : rawData?.length }}
			<span>items</span>
		</div>

		<div
			*ngIf="table.pagination"
			[ngClass]="{
				'label-total-el-pst': labelTotalElementsPosition === 'left'
			}"
			class="footer-row">
			<ng-container *ngIf="!dataFetchLoading && rawData?.length">
				<ng-container *ngIf="!enableDs3; else ds3Pagination">
					<div class="div-pagination">
						<ngb-pagination
							(pageChange)="pageChangeHandler($event)"
							[(page)]="ngbPage"
							[boundaryLinks]="true"
							[collectionSize]="data.totalElements"
							[ellipses]="false"
							[maxSize]="7"
							[pageSize]="data.size"
							[size]="'sm'"
							class="d-flex justify-content-end"></ngb-pagination>
					</div>

					<div
						[ngClass]="{
							'label-total-el-pst': labelTotalElementsPosition === 'left'
						}"
						class="div-show-and-select">
						<div
							*ngIf="labelTotalElementsPosition === 'left'"
							class="total-values">
							<span i18n="@@component-relatorio:mostrando">Mostrando</span>
							<span class="value">{{ data.content.length }}</span>
							<span i18n="@@component-relatorio:de">de</span>
							<span class="value">{{ data.totalElements }}</span>
						</div>
						<div class="div-select-qt-show">
							<pacto-cat-select
								[control]="pageSizeControl"
								[id]="
									idSuffix
										? 'page-size-control-' + idSuffix
										: 'page-size-control'
								"
								[items]="itensPerPage"
								[size]="'SMALL'"></pacto-cat-select>
						</div>

						<div
							*ngIf="labelTotalElementsPosition === 'right'"
							class="total-values">
							<span i18n="@@component-relatorio:mostrando">Mostrando</span>
							<span class="value">{{ data.content.length }}</span>
							<span i18n="@@component-relatorio:de">de</span>
							<span class="value">{{ data.totalElements }}</span>
						</div>
					</div>
				</ng-container>
				<ng-template #ds3Pagination>
					<ds3-pagination
						(pageChange)="pageChangeHandler($event)"
						(pageSizeChange)="onPageSizeChange($event)"
						[length]="totalItems"
						[pageIndex]="currentPage"
						[pageSizeOptions]="pageSizeOptions"
						[pageSize]="pageSize"
						[showFirstLastButtons]="true"></ds3-pagination>
				</ng-template>
			</ng-container>
			<div *ngIf="legenda" class="legenda">
				<ng-container *ngTemplateOutlet="legenda"></ng-container>
			</div>

			<div
				(click)="actionLinkFooterClickEvent()"
				*ngIf="labelActionLinkFooter && iconActionLinkFooter"
				class="action-link-footer">
				<span class="icone">
					<i class="pct {{ iconActionLinkFooter }}"></i>
				</span>
				<span class="texto" style="margin-left: 4px">
					{{ labelActionLinkFooter }}
				</span>
			</div>
		</div>
	</div>
</div>

<ng-template #sideFilterTemplate>
	<div class="dialog">
		<div class="dialog-head">
			<span class="dialog-title">Filtros</span>
			<button (click)="closeSideFilter()" ds3-icon-button>
				<i class="pct pct-x"></i>
			</button>
		</div>
		<div cdkScrollable class="dialog-content">
			<div MatDialogContent>
				<ng-content *ngTemplateOutlet="filterContentRef"></ng-content>
			</div>
		</div>
		<div class="dialog-actions">
			<button (click)="sideFilterlimparTudo()" ds3-outlined-button>
				Limpar filtros
			</button>
			<button (click)="sideFilterAplicar()" ds3-flat-button>
				Aplicar filtros
			</button>
		</div>
	</div>
</ng-template>
