import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { map } from "rxjs/operators";
import { DataFiltro, RelatorioExportarFormato } from "./data-grid.model";
import { TableData } from "./relatorio.component";

@Injectable({
	providedIn: "root",
})
export class DataGridService {
	data: Array<any>;

	constructor(private http: HttpClient) {}

	/**
	 * Assinatura do endpoint GET /endpoint?{PARAMS}
	 *
	 * PARAMS:
	 *
	 *    &sort=FIELD,DIRECTION
	 *    &page=3
	 *    &size=20
	 *    filters= {
	 *        quicksearchValue: string,
	 *        quicksearchFields: string[],
	 *        NOME_CAMPO: VALOR_CAMPO,
	 *        NOME_CAMPO: [VALOR_1, VALOR_2, ...]
	 *    }
	 */
	obterDados(
		endpointUrl: string,
		filtros: DataFiltro,
		endpointParamsType: "json" | "query",
		paramAdapterFn: (params: any) => any = null
	): Observable<TableData<any>> {
		let params = this.getParams(filtros, endpointParamsType);
		if (endpointUrl) {
			if (endpointParamsType === "query") {
				let queryParams = { ...params.filters, ...params.configs, ...params };
				delete queryParams["filters"];
				delete queryParams["configs"];
				if (paramAdapterFn) {
					queryParams = paramAdapterFn(queryParams);
				}
				return this.http.get(endpointUrl, {
					params: queryParams,
				});
			} else {
				if (paramAdapterFn) {
					params = paramAdapterFn(params);
				}
				return this.http.get(endpointUrl, {
					params,
				});
			}
		} else {
			return of({ content: [] });
		}
	}

	obterGrupo(
		url: string
	): Observable<Array<{ id: string; descricao: string; total: number }>> {
		return this.http.get(url).pipe(
			map((result: any) => {
				return result.result;
			})
		);
	}
	obterRelatorio(
		endpointUrl: string,
		filtros: DataFiltro,
		formato: RelatorioExportarFormato,
		endpointParamsType: "json" | "query"
	): Observable<string> {
		const url = `${endpointUrl}/exportar`;
		const filtrosParsed = this.getParams(filtros, endpointParamsType);
		const params: any = {
			filtros: filtrosParsed,
			format: formato,
		};
		return this.http.get(url, { params }).pipe(
			map((result: any) => {
				return result.content;
			})
		);
	}

	private prepareParams(filtros) {
		// sort
		if (filtros.sortField && filtros.sortDirection) {
			const direction = filtros.sortDirection.toUpperCase();
			filtros.sort = `${filtros.sortField},${direction}`;
		}
		delete filtros.sortField;
		delete filtros.sortDirection;
	}

	private getParams(filtros, endpointParamsType: "json" | "query"): any {
		this.prepareParams(filtros);
		if (filtros.filters) {
			filtros.filters =
				endpointParamsType === "query"
					? filtros.filters
					: JSON.stringify(filtros.filters);
		}
		if (filtros.configs) {
			filtros.configs =
				endpointParamsType === "query"
					? filtros.filters
					: JSON.stringify(filtros.configs);
		}
		return filtros;
	}
}
