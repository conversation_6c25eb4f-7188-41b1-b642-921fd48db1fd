import { storiesOf, moduleMetadata } from "@storybook/angular";

import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { Ng2SearchPipeModule } from "ng2-search-filter";
import { HttpClientModule } from "@angular/common/http";
import { TranslateModule } from "@ngx-translate/core";
import { UiModule } from "../../ui.module";
import { SnotifyModule, SnotifyService, ToastDefaults } from "ng-snotify";

import {
	RelatorioComponent,
	PactoDataGridConfig,
	GridFilterConfig,
	GridFilterType,
} from "projects/ui/src/public-api";

const dataAdapterFn = () => {
	return {
		size: 5,
		content: [
			{ nome: "Aluno 1", pendente: "14,5" },
			{ nome: "Aluno 2", pendente: "250" },
			{ nome: "Aluno 3", pendente: "550" },
			{ nome: "Aluno 4", pendente: "1225,45" },
			{ nome: "Aluno 5", pendente: "14,5" },
		],
		totalElements: 25,
		number: 0,
	};
};

const gridConfig: GridFilterConfig = {
	filters: [
		{
			name: "status",
			label: "Status",
			type: GridFilterType.MANY,
			options: [
				{ value: "DEVENDO", label: "Devedor" },
				{ value: "EM_DIA", label: "Em Dia" },
			],
		},
		{
			name: "data_referencia",
			label: "Data de Referência",
			type: GridFilterType.DATE_POINT,
		},
	],
};

const metadata = {
	imports: [
		NgbModule,
		FormsModule,
		HttpClientModule,
		BrowserAnimationsModule,
		Ng2SearchPipeModule,
		ReactiveFormsModule,
		TranslateModule.forChild({ extend: true }),
		UiModule,
		SnotifyModule,
	],
	providers: [
		SnotifyService,
		{ provide: "SnotifyToastConfig", useValue: ToastDefaults },
	],
};

storiesOf("Relatório", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Relatório Simples", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				alternatingColors: "first",
				rowCheck: (name) => {
					console.log("Name check", name);
				},
				table: new PactoDataGridConfig({
					quickSearch: true,
					exportButton: false,
					dataAdapterFn,
					pagination: false,
					showFilters: false,
					rowClick: false,
					totalRow: false,
					actions: [],
					valueRowCheck: "nome",
					columns: [
						{ nome: "nome", titulo: "Nome", visible: true },
						{
							nome: "pendente",
							titulo: "Valor Pendente (R$)",
							visible: true,
						},
					],
				}),
			},
		};
	})
	.add("Relatório Com Paginação", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				table: new PactoDataGridConfig({
					quickSearch: false,
					exportButton: false,
					dataAdapterFn,
					showFilters: false,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [
						{ nome: "nome", titulo: "Nome", visible: true },
						{
							nome: "pendente",
							titulo: "Valor Pendente (R$)",
							valueTransform: (v) => `R$${v}`,
							visible: true,
						},
					],
				}),
			},
		};
	})
	.add("Relatório Com Filtro", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				alternatingColors: "second",
				filterConfig: gridConfig,
				table: new PactoDataGridConfig({
					quickSearch: false,
					exportButton: false,
					initialFilters: [
						{
							name: "nome",
							value: "PREVISTAS",
						},
					],
					dataAdapterFn,
					showFilters: true,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [{ nome: "nome", titulo: "Nome", visible: true }],
				}),
			},
		};
	})

	.add("Relatório Com Exportar", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				filterConfig: gridConfig,
				table: new PactoDataGridConfig({
					quickSearch: false,
					exportButton: true,
					dataAdapterFn,
					showFilters: false,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [{ nome: "nome", titulo: "Nome", visible: true }],
				}),
			},
		};
	})

	.add("Relatório Com Filtro e Exportar", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				filterConfig: gridConfig,
				table: new PactoDataGridConfig({
					quickSearch: false,
					exportButton: true,
					dataAdapterFn,
					showFilters: true,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [{ nome: "nome", titulo: "Nome", visible: true }],
				}),
			},
		};
	})

	.add("Relatório Com Exportar", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				filterConfig: gridConfig,
				table: new PactoDataGridConfig({
					quickSearch: false,
					exportButton: true,
					dataAdapterFn,
					showFilters: false,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [{ nome: "nome", titulo: "Nome", visible: true }],
				}),
			},
		};
	})
	.add("Relatório input de filtro customizado", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista telefones e mails",
				tableDescription: "Pessoas de todo lugar do mundo.",
				filterConfig: gridConfig,
				table: new PactoDataGridConfig({
					quickSearch: true,
					exportButton: true,
					endpointUrl: "https://randomuser.me/api",
					endpointParamsType: "query",
					showFilters: true,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [
						{
							nome: "cell",
							titulo: "Telefone",
							visible: true,
						},
						{
							nome: "email",
							titulo: "Email",
							visible: true,
						},
					],
				}),
			},
		};
	})
	.add("Relatório Com Filtro e Exportar", () => {
		return {
			component: RelatorioComponent,
			props: {
				tableTitle: "Lista de Alunos",
				tableDescription: "Alunos e valores pendentes da dívida.",
				filterConfig: gridConfig,
				table: new PactoDataGridConfig({
					quickSearch: false,
					exportButton: true,
					dataAdapterFn,
					showFilters: true,
					rowClick: false,
					totalRow: false,
					actions: [],
					columns: [{ nome: "nome", titulo: "Nome", visible: true }],
				}),
			},
		};
	});
