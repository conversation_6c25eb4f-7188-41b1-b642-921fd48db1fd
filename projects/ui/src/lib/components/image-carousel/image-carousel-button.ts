import { Component, EventEmitter, Input, Output } from "@angular/core";

@Component({
	selector: "pct-image-carousel-button",
	template: `
		<button
			type="button"
			(click)="selectItem.emit([$event, imageIndex])"
			[title]="buttonTitle"></button>
	`,
	styles: [
		`
			button {
				all: unset;
				width: 10px;
				height: 10px;
				border-radius: 10px;
				background-color: hsla(0deg, 100%, 100%, 0.6);
				border: 1.5px solid gray;
				cursor: pointer;
			}

			:host([data-current="true"]) button {
				background-color: hsla(0deg, 0%, 10%, 0.6);
			}
		`,
	],
})
// tslint:disable-next-line:component-class-suffix
export class PctImageCarouselButton {
	@Input() imageIndex: number;
	@Input() buttonTitle: string;
	@Output() selectItem: EventEmitter<Array<any>> = new EventEmitter();
}
