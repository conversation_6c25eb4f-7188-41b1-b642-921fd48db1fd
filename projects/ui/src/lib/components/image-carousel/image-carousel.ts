import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import {
	AfterContentInit,
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	EventEmitter,
	forwardRef,
	Inject,
	Input,
	OnDestroy,
	Output,
	QueryList,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { BehaviorSubject, interval, Subject, Subscription } from "rxjs";

import { map, startWith, switchMap, takeUntil, tap } from "rxjs/operators";

@Component({
	selector: "pct-image-carousel-item",
	template: `
		<ng-template>
			<ng-content></ng-content>
		</ng-template>
	`,
	styleUrls: ["./image-carousel.scss"],
})
// tslint:disable-next-line:component-class-suffix
export class PctImageCarouselItem implements AfterViewInit {
	_imageCarousel: PctImageCarousel;
	_visible$: BehaviorSubject<boolean> = new BehaviorSubject(false);
	_imageIndex: number;

	/** Template for step content. */
	@ViewChild(TemplateRef, { static: true }) content: TemplateRef<any>;

	constructor(@Inject(forwardRef(() => PctImageCarousel)) _imageCarousel) {
		this._imageCarousel = _imageCarousel;
	}

	ngAfterViewInit(): void {
		this._imageCarousel.carouselItems.changes
			.pipe(
				switchMap(() => {
					return this._imageCarousel.selectionChange.pipe(
						map((event) => {
							return event.selectedStep === this;
						}),
						startWith(this._imageCarousel.selected === this)
					);
				})
			)
			.subscribe((isSelected) => {
				if (isSelected) {
					this._visible$.next(isSelected);
				}
			});
	}

	select(): void {
		this._imageCarousel.selectedIndex = this._imageIndex;
		this._imageCarousel.reset();
		this._visible$.next(true);
	}

	reset(): void {
		this._visible$.next(false);
	}
}

@Component({
	selector: "pct-image-carousel",
	templateUrl: "./image-carousel.html",
	styleUrls: ["./image-carousel.scss"],
	animations: [
		trigger("slideIn", [
			state("left", style({ transform: "translateX(0)" })),
			state("right", style({ transform: "translateX(0)" })),
			transition("void => right", [
				style({ transform: "translateX(100%)" }),
				animate(500),
			]),
			transition("void => left", [
				animate(500, style({ transform: "translateX(-100%)" })),
			]),
		]),
	],
})
// tslint:disable-next-line:component-class-suffix
export class PctImageCarousel implements OnDestroy, AfterContentInit {
	@ContentChildren(PctImageCarouselItem)
	_carouselItems: QueryList<PctImageCarouselItem>;

	carouselItems: QueryList<PctImageCarouselItem> =
		new QueryList<PctImageCarouselItem>();

	@Input()
	get selectedIndex(): number {
		return this._selectedIndex;
	}

	set selectedIndex(index: number) {
		const newIndex = Number(index);

		if (isNaN(newIndex)) {
			return;
		}

		if (this._carouselItems && this.carouselItems) {
			if (this._selectedIndex !== newIndex) {
				this._updateSelectedItemIndex(newIndex);
			}
		} else {
			this._selectedIndex = newIndex;
		}
	}

	private _selectedIndex: number;

	/** The step that is selected. */
	@Input()
	get selected(): PctImageCarouselItem | undefined {
		return this.carouselItems
			? this.carouselItems.toArray()[this.selectedIndex]
			: undefined;
	}

	set selected(item: PctImageCarouselItem | undefined) {
		this.selectedIndex =
			item && this.carouselItems
				? this.carouselItems.toArray().indexOf(item)
				: -1;
	}

	@Output() readonly selectionChange = new EventEmitter<any>();
	@Input() animationDuration = 3000;

	_destroyed$: Subject<null> = new Subject();
	_destroyedInterval$: Subject<null> = new Subject();
	_animationDirection: "left" | "right" = "right";
	_animationDuration: string;

	constructor(private _changeDetectorRef: ChangeDetectorRef) {}

	ngAfterContentInit(): void {
		this._carouselItems.changes
			.pipe(startWith(this._carouselItems), takeUntil(this._destroyed$))
			.subscribe((items: QueryList<PctImageCarouselItem>) => {
				items.forEach((item, index) => {
					item._imageIndex = index;
				});

				// this.selectedIndex = -1;
				this.carouselItems = items;
				this.selectedIndex = 0;

				if (this.carouselItems.length > 1) {
					this._setupAnimation().subscribe(() => {});
				}
			});
	}

	ngOnDestroy(): void {
		this._destroyed$.next();
	}

	_updateSelectedItemIndex(newIndex: number): void {
		const childrenArray = this.carouselItems.toArray();

		this.selectionChange.emit({
			selectedIndex: newIndex,
			previouslySelectedIndex: this.selectedIndex,
			selectedStep: childrenArray[newIndex],
			previouslySelectedStep: childrenArray[this.selectedIndex],
		});
		// this._animationSlide = newIndex > this.selectedIndex ? "right" : "left";
		this._selectedIndex = newIndex;
		this._stateChanged();
		// clear the interval to prevent
		// race condition
		this._destroyedInterval$.next();

		if (this.carouselItems.length > 1) {
			this._setupAnimation().subscribe(() => {});
		}
	}

	_stateChanged() {
		this._changeDetectorRef.markForCheck();
	}

	_setupAnimation() {
		return interval(this.animationDuration).pipe(
			tap(() => {
				const _arrayLength = this.carouselItems.length;

				if (this.selectedIndex < _arrayLength) {
					this.selectedIndex += 1;
				}

				if (this.selectedIndex === _arrayLength) {
					this.selectedIndex = 0;
				}

				const selected = this.carouselItems.toArray()[this.selectedIndex];

				if (selected) {
					selected.select();
				}
			}),
			takeUntil(this._destroyedInterval$)
		);
	}

	reset(): void {
		this.carouselItems.forEach((item) => item.reset());
	}
}
