<ng-container *ngIf="carouselItems.changes | async"></ng-container>
<div [@.disabled]="carouselItems.length <= 1">
	<ng-container *ngFor="let item of carouselItems; let i = index;">
		<div
			*ngIf="item._visible$ | async"
			[@slideIn]="_animationDirection"
			class="pct-carousel-item">
			<ng-container [ngTemplateOutlet]="item.content"></ng-container>
		</div>
	</ng-container>
</div>
<div *ngIf="carouselItems.length > 1" class="pct-button-container">
	<ng-container *ngFor="let item of carouselItems; let i = index;">
		<pct-image-carousel-button
			(selectItem)="item.select()"
			[attr.data-current]="item._visible$ | async"
			[buttonTitle]="('banner.page' | translate) + ' ' + (i + 1)"
			[imageIndex]="i"></pct-image-carousel-button>
	</ng-container>
</div>
