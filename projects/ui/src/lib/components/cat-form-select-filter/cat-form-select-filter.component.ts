import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	Output,
} from "@angular/core";
import { FormControl } from "@angular/forms";

import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
} from "../cat-select-filter/cat-select-filter.component";

@Component({
	selector: "pacto-cat-form-select-filter",
	templateUrl: "./cat-form-select-filter.component.html",
	styleUrls: ["./cat-form-select-filter.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatFormSelectFilterComponent {
	@Input() id;
	@Input() addtionalFilters: any;
	/**
	 * Full url of endpoint without any GET params.
	 *
	 */
	@Input() endpointUrl: string;
	@Input() options: any[];
	@Input() control: FormControl;
	@Input() paramBuilder: SelectFilterParamBuilder;
	@Input() resposeParser: SelectFilterResponseParser;
	@Input() placeholder = "-";
	@Input() label: string;
	@Input() idKey: string;
	@Input() labelKey: string;
	@Input() labelFn: (item: any) => string;
	@Input() subLabelFn: (item: any) => string;
	@Input() imageKey: string;
	@Input() hasClearAction: boolean;
	@Input() errorMsg: string;
	@Input() addEmptyOption = false;
	@Input() subLabel = true;
	@Output() search: EventEmitter<string> = new EventEmitter<string>();

	get isInvalid() {
		return this.control && this.control.touched && !this.control.valid;
	}

	get showError() {
		return this.isInvalid && !this.control.disabled;
	}

	constructor() {}
}
