import { storiesOf, moduleMetadata } from "@storybook/angular";
import {
	CatTabsTransparentComponent,
	TabTransparentDirective,
	CatButtonComponent,
} from "projects/ui/src/public-api";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        height: 600px;
    ">
			<pacto-cat-tabs-transparent
				[showAction]="showAction"
				[actionIcon]="actionIcon"
				[actionLabel]="actionLabel">
				<ng-template pactoTabTransparent="'tab_a'" label="Tab A">
					<div style="padding: 20px">
						<div>Conteúdo da Tab A</div>
						<div>Conteúdo da Tab A</div>
						<div>Conteúdo da Tab A</div>
					</div>
				</ng-template>
				<ng-template pactoTabTransparent="'tab_b'" label="Tab B">
					<div style="padding: 20px">
						<div><PERSON><PERSON><PERSON><PERSON> da Tab B</div>
						<div>Conte<PERSON><PERSON> da Tab B</div>
						<div>Conteúdo da Tab B</div>
					</div>
				</ng-template>
			</pacto-cat-tabs-transparent>
		</div>
	`,
})
class HostComponent {
	@Input() actionLabel;
	@Input() actionIcon;
	@Input() showAction;
}

const metadata = {
	declarations: [
		CatTabsTransparentComponent,
		TabTransparentDirective,
		CatButtonComponent,
	],
};

storiesOf("Layout|Transparent Tabs", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Simple Tabs", () => {
		return {
			component: HostComponent,
			props: {},
		};
	})
	.add("Transparent Tabs with Button", () => {
		return {
			component: HostComponent,
			props: {
				actionLabel: "Create New",
				actionIcon: "pct pct-user",
				showAction: true,
			},
		};
	});
