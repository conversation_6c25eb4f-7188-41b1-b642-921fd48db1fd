import {
	Component,
	OnInit,
	ContentChildren,
	AfterViewInit,
	QueryList,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	AfterContentChecked,
	Input,
	Output,
	EventEmitter,
} from "@angular/core";
import { TabTransparentDirective } from "./tab.directive";

let uniqueId = 0;

@Component({
	selector: "pacto-cat-tabs-transparent",
	templateUrl: "./cat-tabs-transparent.component.html",
	styleUrls: ["./cat-tabs-transparent.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatTabsTransparentComponent
	implements OnInit, AfterViewInit, AfterContentChecked
{
	@ContentChildren(TabTransparentDirective)
	tabs: QueryList<TabTransparentDirective>;
	@Input() showAction = false;
	@Input() actionLabel;
	@Input() actionIcon;
	@Input() iconPosition;
	@Output() activateTab: EventEmitter<{
		index: number;
		previous: string;
		next: string;
		previousIndex: number;
	}> = new EventEmitter();
	@Output() action: EventEmitter<any> = new EventEmitter();
	tabNames: string[] = [];
	tabTotals: number[] = [];
	tabIcons: string[] = [];
	tabImages: string[] = [];
	@Input() tabIndex = 0;
	tabId;
	@Input() tabIcone;
	@Input() tabsJustify = false;
	@Input() actionId: string;

	constructor(private cd: ChangeDetectorRef) {}

	get template() {
		if (!isNaN(this.tabIndex)) {
			const tabs = this.tabs;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				const tab = this.tabs.toArray()[this.tabIndex];
				if (tab) {
					return tab.ref;
				}
			}
		} else {
			return null;
		}
	}

	ngOnInit() {
		if (!this.actionId) {
			this.actionId = `cat-tab-transparent-action-${uniqueId++}`;
		}
	}

	ngAfterViewInit() {
		setTimeout(() => {
			this.tabClickHandler(null, this.tabIndex);
		});
	}

	ngAfterContentChecked() {
		this.fetchTabNames();
		this.cd.detectChanges();
	}

	idTab(index: number) {
		if (this.tabs.toArray()[index].id) {
			return this.tabs.toArray()[index].id;
		}
		return this.tabs.toArray()[index].pactoTabTransparent;
	}

	tabClickHandler($event: MouseEvent, index: number) {
		const previousIndex = this.tabIndex;
		if (!this.tabs) {
			return;
		}
		if (!this.tabs.length) {
			return;
		}
		const tab = this.tabs.toArray()[index];
		if (!tab) {
			return;
		}
		let tabId = "";
		if (!tab.active) {
			tabId = this.idTab(index);
			this.tabId = tabId;
			this.activateTab.emit({
				index,
				previous: this.tabId,
				next: tabId,
				previousIndex,
			});
			return;
		}
		this.tabIndex = index;
		const previous = this.tabId;
		tabId = this.idTab(index);
		this.tabId = tabId;
		this.activateTab.emit({ index, previous, next: tabId, previousIndex });
	}

	private fetchTabNames() {
		if (this.tabs) {
			this.tabNames = [];
			this.tabTotals = [];
			this.tabIcons = [];
			this.tabImages = [];
			this.tabs.forEach((tab) => {
				const name = tab.label ? tab.label : "";
				if (tab.showTabIcon) {
					this.tabIcons.push(tab.tabIcone);
				}
				this.tabImages.push(tab.tabImage);
				this.tabNames.push(name);
				this.tabTotals.push(tab.tabTotal);
			});
		}
	}
}
