@import "projects/ui/assets/import.scss";

.pacto-tabs-wrapper {
	display: flex;
	flex-wrap: wrap;
}

.tabs {
	display: flex;
	flex-grow: 1;

	.flex-wrap {
		flex-wrap: wrap;
	}

	.tab {
		@extend .type-p-small;
		line-height: 42px;
		color: $gelo04;
		padding: 0px 20px;
		min-width: 60px;
		cursor: pointer;
		text-align: center;
		border-bottom: 3px solid $gelo02;

		&.active {
			border-bottom: 3px solid $azulimPri;
			color: $pretoPri;
			font-weight: bold;
		}

		.total-value {
			font-weight: 600;
			font-size: 14px;
			color: $azulim02;
		}

		&.justify {
			width: 100%;
			padding: 0px;
		}
	}

	.spacer {
		flex-grow: 1;
	}
}

.justify {
	justify-content: space-between;
}

.tab-content {
	width: 100%;
	flex-basis: 100%;
}
