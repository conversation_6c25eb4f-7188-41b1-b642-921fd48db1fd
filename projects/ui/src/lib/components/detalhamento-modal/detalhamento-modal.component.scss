@import "projects/ui/assets/import.scss";

.flex-space-between {
	display: flex;
	justify-content: space-between;
}

.auto-95 {
	margin: auto;
	width: 95%;
}

.sub-title {
	margin-left: 12px;
	margin-top: 16px;
	margin-bottom: 16px;
	display: inline-block;
	font-style: normal;
	font-weight: bold;
	font-size: 14px;
	line-height: 14px;
	color: #51555a;
}

.title {
	font-style: normal;
	font-weight: bold;
	font-size: 16px;
	line-height: 16px;
	color: #51555a;
}

.line {
	height: 1px;
	background: #c4c4c4;
	width: 95%;
	margin: auto;
}

.line-pont {
	border: 1px dashed #dcdddf;
	height: 1px;
	width: 95%;
	margin: auto;
}

.dados {
	background-color: #fafafa;
	width: 80%;
	margin: auto;
	margin-bottom: 32px;
	margin-top: 32px;
}

.flex-center {
	display: flex;
	justify-content: center;
	align-content: center;
}

.campos {
	display: flex;
	justify-content: space-around;
	margin-left: 22px;
	margin-top: 12px;
	margin-bottom: 12px;
}

.campo {
	display: flex;
	justify-content: space-between;
	align-items: left;
	width: 33%;
	font-style: normal;
	font-weight: bold;
	font-size: 14px;
	line-height: 14px;
	color: #51555a;
}

.table {
	margin: 32px;
}
