import { NO_ERRORS_SCHEMA, Component } from "@angular/core";
import { moduleMetadata, storiesOf } from "@storybook/angular";
import {
	DetalhamentoModalComponent,
	PactoDataGridConfig,
} from "projects/ui/src/public-api";

@Component({
	selector: "pacto-detalhamento-modal",
	template: `
		<pacto-detalhamento-modal></pacto-detalhamento-modal>
	`,
})
class HostComponent {}

const metadata = {
	declarations: [DetalhamentoModalComponent],
};

storiesOf("Detalhamento", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Custom", () => {
		return {
			component: DetalhamentoModalComponent,
			props: {
				table: new PactoDataGridConfig({
					quickSearch: true,
					ghostLoad: true,
					ghostAmount: 5,
					showFilters: true,
					columns: [
						{
							nome: "codigo",
							titulo: "Código",
							visible: true,
							ordenavel: true,
						},
						{ nome: "nome", titulo: "Nome", visible: true, ordenavel: true },
					],
					dropDownActions: [
						{
							nome: "Editar",
							iconClass: "",
							tooltipText: "Editar um plano",
						},
						{
							nome: "Excluir",
							iconClass: "",
							tooltipText: "Excluir um plano",
							actionFn: () => alert("clicou em excluir"),
						},
					],
				}),
			},
		};
	});
