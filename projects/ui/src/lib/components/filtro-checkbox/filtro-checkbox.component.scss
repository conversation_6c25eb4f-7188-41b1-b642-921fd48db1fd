.input-pesquisa {
	position: relative;
}

i.pct-search {
	position: absolute;
	top: 30px;
	left: 22px;
	width: 20px;
	height: 20px;
	color: #a1a5aa;
}

input {
	width: 410px;
	border: 0px;
	color: #a1a5aa;
	padding: 6px 30px;

	&:focus {
		outline: none;
		border-bottom: 1px solid #1998fcff;
	}

	border-bottom: 1px solid #c7c9cc;
	margin: 20px;
}

.check-selecionar-todos {
	top: 50%;
	left: 50%;
	padding-top: 27px;
	margin-left: 10px;
}

.filtros-consultores {
	margin: 0px;
	padding: 0px;
	border-bottom: solid 2px #dcdddf;
	margin-bottom: 15px;
}

ul {
	-webkit-columns: 4;
	-moz-columns: 4;
	columns: 4;
	padding: 20px;
	margin: 0px;
}

ul:nth-child(odd) {
	background-color: #fafafa;
}

li {
	list-style-type: none;
	margin-left: 1px;
	padding: 0px;
}

.footer-row {
	display: flex;
	margin-top: 20px;
	padding-top: 15px;
	border-top: 1px solid #f1f1f1;
	justify-content: space-between;

	> * {
		display: block;
		margin-left: 20px;
	}

	pacto-select .form-group {
		margin-bottom: 0px;
	}
}

.footer-page {
	display: flex;
	flex-direction: row-reverse;
}

.total-values {
	line-height: 32px;
	color: #9d9d9d;
	font-weight: bold;
	margin-right: 35px;
	font-size: 13px;
	margin-left: 12px;

	.value {
		padding: 0px 3px;
	}
}

pacto-cat-select {
	margin-right: 20px;
}

.input-custom {
	margin-left: 0px;
	margin-right: 0px;
}
