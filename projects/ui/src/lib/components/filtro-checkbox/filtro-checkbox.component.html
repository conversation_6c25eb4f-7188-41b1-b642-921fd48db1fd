<div class="row">
	<div class="input-pesquisa">
		<i class="pct pct-search"></i>
		<input
			#quickSearch
			[formControl]="convertToFormControl(formGroup.get('searchForm'))"
			[placeholder]="placeHolderInputBusca"
			type="text" />
	</div>
	<div class="check-selecionar-todos">
		<pacto-cat-checkbox
			[control]="convertToFormControl(formGroup.get('checkAllForm'))"
			i18n-label="@@pacto-filtro-checkbox:label-selecionar-todos"
			label="Selecionar todos"></pacto-cat-checkbox>
	</div>
</div>
<div
	*ngIf="labelInputNumber !== undefined && labelInputNumber !== ''"
	class="row input-custom">
	<div class="md-col-3">
		<pacto-cat-form-input-number
			[formControl]="convertToFormControl(formGroup.get(nameFormInputNumber))"
			[label]="labelInputNumber"
			[maxlength]="maxlengthInputNumber"
			[placeholder]="placeHolderInputNumber"
			decimal="true"></pacto-cat-form-input-number>
	</div>
</div>
<div class="filtros-consultores">
	<ul *ngFor="let l of getArrayLines()">
		<li *ngFor="let item of data.content.slice(l.indexStart, l.indexEnd)">
			<pacto-cat-checkbox
				(click)="checkRow(item)"
				[control]="getFormControl(item)"
				[label]="getItemLabel(item)"></pacto-cat-checkbox>
		</li>
	</ul>
</div>
<div>
	<div class=".footer-row">
		<div class="footer-page">
			<ng-container>
				<ngb-pagination
					(pageChange)="pageChangeHandler($event)"
					[(page)]="ngbPage"
					[boundaryLinks]="true"
					[collectionSize]="data.totalElements"
					[ellipses]="false"
					[maxSize]="7"
					[pageSize]="data.size"
					[size]="'sm'"
					class="d-flex justify-content-end margin-left"></ngb-pagination>
				<pacto-cat-select
					[control]="pageSizeControl"
					[items]="[
						{ id: 16, label: '16' },
						{ id: 32, label: '32' },
						{ id: 64, label: '64' }
					]"
					[size]="'SMALL'"></pacto-cat-select>
				<div class="total-values">
					<span i18n="@@pacto-filtro-checkbox:mostrando">Mostrando</span>
					<span class="value">{{ data.content.length }}</span>
					<span i18n="@@pacto-filtro-checkbox:de">de</span>
					<span class="value">{{ data.totalElements }}</span>
				</div>
			</ng-container>
		</div>
	</div>
</div>
