@import "projects/ui/assets/import.scss";

.pct-notification {
	width: 100%;
	border-radius: 8px;
	border-width: 1px;
	border-style: solid;
	font-family: "Nunito Sans", sans-serif;
	padding: 16px;
	background: #f7f7f7;

	.pct-notfication-title {
		color: #000;
		font-weight: 600;
		font-size: 14px;
		line-height: 16px;
		margin-bottom: 8px;
	}

	.pct-notification-message {
		font-weight: 400;
		font-size: 14px;
		line-height: 16px;
	}

	&.info {
		border-color: $cinzaPri;
		color: $preto02;
	}

	&.success {
		border-color: $chuchuzinhoPri;
		color: $chuchuzinho06;

		.pct-notfication-title {
			color: inherit;
		}
	}

	&.warn {
		border-color: $pequizaoPri;
		color: $pequizao06;

		.pct-notfication-title {
			color: inherit;
		}
	}

	&.danger {
		border-color: $hellboyPri;
		color: $hellboy06;

		.pct-notfication-title {
			color: inherit;
		}
	}
}
