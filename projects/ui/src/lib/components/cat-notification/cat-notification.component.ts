import { Component, Directive, Input, OnInit } from "@angular/core";

@Directive({
	selector: "[pactoNotificationTitle]",
})
export class CatPactoNotificationTitleDirective {}

@Directive({
	selector: "[pactoNotificationMessage]",
})
export class CatPactoNotificationMessageDirective {}

@Component({
	selector: "pacto-cat-notification",
	templateUrl: "./cat-notification.component.html",
	styleUrls: ["./cat-notification.component.scss"],
})
export class CatNotificationComponent implements OnInit {
	@Input() state: "danger" | "success" | "warn" | "info" = "info";

	constructor() {}

	ngOnInit() {}
}
