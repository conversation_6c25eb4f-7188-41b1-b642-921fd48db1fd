<button
	(click)="show()"
	[ngClass]="{ 'btn-filter': table }"
	class="btn btn-primary"
	id="show-log">
	<i class="pct pct-list"></i>
</button>

<ng-template #modalLog>
	<div class="modal-header">
		<h4 class="modal-title">
			<span *ngIf="local; else logName">
				Log
				<span *ngIf="titulo">
					de
					<ng-container *ngTemplateOutlet="titulo"></ng-container>
				</span>
			</span>
			<ng-template #logName>
				<ng-container *ngTemplateOutlet="titulo"></ng-container>
				Log
			</ng-template>
		</h4>
		<button
			(click)="dismiss('close')"
			aria-label="Close"
			class="close modal-item"
			type="button">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="container-modal-log">
		<pacto-tabela-log
			[dataGridConfig]="dataGridConfig"
			[dataGridFilterConfig]="dataGridFilterConfig"
			[url]="url"></pacto-tabela-log>
	</div>
</ng-template>
