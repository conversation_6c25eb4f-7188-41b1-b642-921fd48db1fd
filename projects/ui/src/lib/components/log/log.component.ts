import {
	Component,
	Inject,
	Input,
	LOCALE_ID,
	OnInit,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PactoDataGridConfig } from "../relatorio/data-grid.model";
import { GridFilterConfig } from "../relatorio/data-grid-filter.model";

@Component({
	selector: "pacto-log",
	templateUrl: "./log.component.html",
	styleUrls: ["./log.component.scss"],
})
export class LogComponent implements OnInit {
	@Input() titulo;
	@Input() url;
	@Input() table = false;
	@Input() dataGridConfig: PactoDataGridConfig;
	@Input() dataGridFilterConfig: GridFilterConfig;
	@ViewChild("modalLog", { static: true }) modalLog;

	constructor(
		public activeModal: NgbActiveModal,
		private modal: NgbModal,
		@Inject(LOCALE_ID) private locale
	) {}

	local: boolean;

	ngOnInit() {
		if (this.locale !== "en") {
			this.local = true;
		} else {
			this.local = false;
		}
	}

	show() {
		this.activeModal = this.modal.open(this.modalLog, {
			windowClass: "modal-xmxl",
		});
	}

	dismiss(reason) {
		this.activeModal.dismiss();
	}
}
