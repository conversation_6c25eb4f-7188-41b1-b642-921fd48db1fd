import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
	AfterViewInit,
} from "@angular/core";
import { PactoDataGridConfig } from "../../relatorio/data-grid.model";
import { RelatorioComponent } from "../../relatorio/relatorio.component";
import {
	GridFilterConfig,
	GridFilterType,
} from "../../relatorio/data-grid-filter.model";
import { FormControl } from "@angular/forms";

@Component({
	selector: "pacto-tabela-log",
	templateUrl: "./tabela-log.component.html",
	styleUrls: ["./tabela-log.component.scss"],
})
export class TabelaLogComponent implements OnInit, AfterViewInit {
	@Input() idSuffix: string;
	@Input() url;
	@Input() titulo;
	@Input() dataGridConfig: PactoDataGridConfig;
	@Input() dataGridFilterConfig: GridFilterConfig;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	@ViewChild("descricao", { static: true }) descricao;
	@ViewChild("detalhes", { static: true }) detalhes;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("custom", { static: true }) custom;
	// FILTER LABELS
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.clearPersistedGridState();

		this.configTable();
		this.configFilters();
	}

	ngAfterViewInit() {
		const rc = this.tableData as any;
		if (rc.quickSearchControl instanceof FormControl) {
			rc.quickSearchControl.setValue("", { emitEvent: false });
		}
		this.tableData.reloadData();
		this.cd.detectChanges();
	}

	private clearPersistedGridState() {
		if (!this.idSuffix) return;
		Object.keys(localStorage)
			.filter((key) => key.includes(this.idSuffix))
			.forEach((key) => localStorage.removeItem(key));
	}

	beforeDismiss() {}

	private configTable() {
		if (this.dataGridConfig) {
			this.table = this.dataGridConfig;
			return;
		}

		this.table = new PactoDataGridConfig({
			endpointUrl: this.url,
			quickSearch: true,
			exportButton: false,
			columns: [
				{
					nome: "operacao",
					titulo: "Operação",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "operacao",
				},
				{
					nome: "usuario",
					titulo: "Usuário",
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "usuario",
				},
				{
					nome: "origem",
					titulo: "Origem",
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "origem",
				},
				{
					nome: "data",
					titulo: "Data",
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "dia",
				},
				{
					nome: "id",
					titulo: "Código",
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "chave",
				},
				{
					nome: "desc",
					titulo: "Descrição",
					buscaRapida: false,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					campo: "descricao",
					celula: this.descricao,
				},
				{
					nome: "detalhes",
					titulo: "Detalhes",
					buscaRapida: false,
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					celula: this.detalhes,
				},
			],
		});
	}

	private configFilters() {
		if (this.dataGridFilterConfig) {
			this.filterConfig = this.dataGridFilterConfig;
			return;
		}

		this.filterConfig = {
			filters: [
				{
					name: "tipo",
					label: "Tipo",
					type: GridFilterType.MANY,
					options: [
						{ value: "INSERT", label: "Adicionado" },
						{ value: "UPDATE", label: "Alterado" },
						{ value: "DELETE", label: "Removido" },
					],
				},
				{
					name: "dataInicio",
					label: this.dataInicioLabel,
					type: GridFilterType.DATE_POINT,
				},
				{
					name: "dataFim",
					label: this.dataFimLabel,
					type: GridFilterType.DATE_POINT,
				},
			],
		};
	}

	detalhar(item) {
		if (item.status) {
			item.status = item.status === "detalhando" ? "fechado" : "detalhando";
			item.selected = item.status === "detalhando" ? true : false;
		} else {
			item.status = "detalhando";
			item.selected = true;
		}
	}
}
