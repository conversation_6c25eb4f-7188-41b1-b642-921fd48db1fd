<pacto-relatorio
	#tableData
	[idSuffix]="idSuffix"
	[customContent]="custom"
	[table]="table"
	[filterConfig]="filterConfig"
	[showShare]="false"></pacto-relatorio>

<ng-template #custom let-dados="dados" let-visibleColumns="colunas">
	<ng-container
		*ngFor="let row of dados; let lastRow = last; let rowIndex = index">
		<tr id="element-{{ rowIndex }}" [ngClass]="{ selected: row.selected }">
			<!-- Data columns -->
			<td
				*ngFor="let column of visibleColumns; let firstColumn = first"
				[ngClass]="column.styleClass">
				<ng-container *ngIf="!column.celula && row">
					<span
						class="column-cell"
						[ngClass]="{ 'hover-cell': column.cellPointerCursor }">
						<ng-container *ngIf="!column.celula && column.campo && row">
							{{
								column.valueTransform
									? column.valueTransform(row[column.campo], row)
									: row[column.campo]
							}}
						</ng-container>
						<ng-container *ngIf="column.celula && row">
							<ng-container
								*ngTemplateOutlet="
									column.celula;
									context: { item: row }
								"></ng-container>
						</ng-container>
					</span>
				</ng-container>
				<ng-container *ngIf="column.celula && row">
					<ng-container
						*ngTemplateOutlet="
							column.celula;
							context: { item: row }
						"></ng-container>
				</ng-container>
			</td>
		</tr>
		<tr class="selected details" *ngIf="row.selected">
			<td colspan="3">
				<div class="caixa-log">
					<div class="titulo anterior">VALOR ANTERIOR</div>
					<div class="alteracoes">
						<div
							*ngFor="let alteracao of row.alteracoes"
							style="word-wrap: break-word">
							<span
								*ngIf="alteracao.valorAnterior && alteracao.valorAnterior != ''"
								[innerHTML]="
									'[' + alteracao.campo + ': ' + alteracao.valorAnterior + ']'
								"></span>
							<span
								*ngIf="
									!alteracao.valorAnterior || alteracao.valorAnterior == ''
								"
								[innerHTML]="'[' + alteracao.campo + ': Sem valor]'"></span>
						</div>
					</div>
				</div>
			</td>
			<td colspan="4">
				<div class="caixa-log">
					<div
						class="titulo alterado"
						[ngClass]="{ removido: row.operacao === 'Removido' }">
						VALOR ALTERADO
					</div>
					<div class="alteracoes">
						<span *ngIf="row.operacao === 'Removido'">
							Este registro foi removido.
						</span>
						<div
							*ngFor="let alteracao of row.alteracoes"
							style="word-wrap: break-word">
							<span
								*ngIf="alteracao.valorAlterado && alteracao.valorAlterado != ''"
								[innerHTML]="
									'[' + alteracao.campo + ': ' + alteracao.valorAlterado + ']'
								"></span>
						</div>
					</div>
				</div>
			</td>
		</tr>
	</ng-container>
</ng-template>

<ng-template #descricao let-item="item">
	<div
		class="descricao-column"
		*ngIf="!item.selected"
		[innerHTML]="item.identificador + '<br/>' + item.descricao"></div>
	<div class="descricao-column" *ngIf="item.selected">
		<b>{{ item.identificador }}</b>
	</div>
</ng-template>

<ng-template #detalhes let-item="item">
	<pacto-cat-button
		[type]="'OUTLINE'"
		[id]="'tbl-log-btn-detail-' + idSuffix"
		label="Detalhes"
		(click)="detalhar(item)"
		*ngIf="!item.status || item.status === 'fechado'"
		[icon]="'pct pct-plus'"></pacto-cat-button>

	<pacto-cat-button
		[type]="'OUTLINE'"
		[id]="'tbl-log-btn-detail-' + idSuffix"
		label="Detalhes"
		(click)="detalhar(item)"
		*ngIf="item.status && item.status === 'detalhando'"
		[icon]="'pct pct-minus'"></pacto-cat-button>
</ng-template>

<!-- FILTER LABELS -->
<ng-template
	#dataInicioLabel
	i18n="@@relatorio-atividade-professores:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template
	#dataFimLabel
	i18n="@@relatorio-atividade-professores:data-fim:filtro">
	Data Fim
</ng-template>
