@import "projects/ui/assets/import.scss";

b {
	font-weight: 900;
}

tr.selected {
	border: none;
	border-top: 2px solid $cinzaPri;

	&.details {
		border-top: none;
		border-bottom: 2px solid $cinzaPri;
	}

	background-color: #eff2f7;

	td {
		font-weight: 900;
	}

	::ng-deep.pacto-outline {
		background-color: $branco;
	}
}

.caixa-log {
	.titulo {
		border-top-left-radius: 5px;
		border-top-right-radius: 5px;
		color: $branco;
		font-weight: 900;
		padding: 8px 15px;

		&.alterado {
			background-color: $chuchuzinho02;
			border: 1px $chuchuzinho02 solid;

			&.removido {
				background-color: $hellboy03;
				border: 1px $hellboy03 solid;
			}
		}

		&.anterior {
			background-color: $cinzaPri;
			border: 1px $cinzaPri solid;
		}
	}

	.alteracoes {
		padding: 15px;
		border: 1px $cinzaPri solid;
		border-top: none;
		border-bottom-left-radius: 5px;
		border-bottom-right-radius: 5px;
	}

	background-color: $branco;
	border-radius: 5px;
	max-width: 380px;
}

.descricao-column {
	max-width: 350px;
	word-break: break-word;
}
