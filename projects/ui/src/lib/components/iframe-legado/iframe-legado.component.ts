import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";

let uniqueId = 0;

@Component({
	selector: "pacto-iframe-legado",
	templateUrl: "./iframe-legado.component.html",
	styleUrls: ["./iframe-legado.component.scss"],
})
export class IframeLegadoComponent implements OnInit {
	@Input() url: string;
	@Input() id: string;
	@Input() height: string = "500px";
	@Input() width: string = "500px";
	@Input() scrolling: string = "no";
	@Output() messageReceived = new EventEmitter<{
		event: MessageEvent;
		dataParsed: any;
	}>();
	loading = true;
	private idPrefix: string = "pct-iframe-";

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		window.addEventListener("message", (event) => {
			let dataParsed: any = {};
			if (event.data && typeof event.data === "string") {
				dataParsed = JSON.parse(event.data);
			}
			if (dataParsed.loaded) {
				this.loading = false;
			}
			this.messageReceived.emit({ event, dataParsed });
		});

		if (!this.id) {
			this.id = `${this.idPrefix}${uniqueId++}`;
		} else {
			this.id = `${this.idPrefix}${this.id}`;
		}
	}

	load(event) {
		this.loading = false;
	}
}
