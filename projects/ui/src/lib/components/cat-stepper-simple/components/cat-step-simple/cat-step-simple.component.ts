import {
	AfterContentInit,
	AfterViewChecked,
	ChangeDetectorRef,
	Component,
	ContentChild,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ButtonContainerDirective } from "../../directives/button-container.directive";

@Component({
	selector: "pacto-cat-step-simple",
	templateUrl: "./cat-step-simple.component.html",
	styleUrls: ["./cat-step-simple.component.scss"],
})
export class CatStepSimpleComponent
	implements OnInit, AfterViewChecked, AfterContentInit
{
	@Input() stepLabel: string;
	@Input() stepDescription: string;
	@ViewChild(TemplateRef, { static: false }) content: TemplateRef<any>;
	@ContentChild(ButtonContainerDirective, { static: false })
	buttonContainerDirective: ButtonContainerDirective;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {}

	ngAfterViewChecked(): void {
		this.cd.detectChanges();
	}

	ngAfterContentInit() {}
}
