import {
	AfterContentChecked,
	AfterContentInit,
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	EmbeddedViewRef,
	EventEmitter,
	OnInit,
	Output,
	QueryList,
	ViewChild,
	ViewContainerRef,
} from "@angular/core";
import { CatStepSimpleComponent } from "../cat-step-simple/cat-step-simple.component";
import { StepperService } from "../../services/stepper.service";
import { DialogService } from "../../../../dialog/service/dialog.service";
import { TraducoesXinglingComponent } from "../../../traducoes-xingling/traducoes-xingling.component";

@Component({
	selector: "pacto-cat-stepper-simple",
	templateUrl: "./cat-stepper-simple.component.html",
	styleUrls: ["./cat-stepper-simple.component.scss"],
})
export class CatStepperSimpleComponent
	implements OnInit, AfterContentInit, AfterContentChecked, AfterViewInit
{
	@ContentChildren(CatStepSimpleComponent, { descendants: true })
	_steps: QueryList<CatStepSimpleComponent>;
	@Output() goBack: EventEmitter<any> = new EventEmitter<any>();
	selectedIndex = 0;
	_current: EmbeddedViewRef<any> | null = null;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;

	@ViewChild("vcr", { static: false, read: ViewContainerRef })
	vcr: ViewContainerRef;

	get template() {
		if (!isNaN(this.selectedIndex)) {
			const tabs = this._steps;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				return this._steps.toArray()[this.selectedIndex].content;
			}
		} else {
			return null;
		}
	}

	get label() {
		if (!isNaN(this.selectedIndex)) {
			const tabs = this._steps;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				return this._steps.toArray()[this.selectedIndex].stepLabel;
			}
		} else {
			return null;
		}
	}

	get description() {
		if (!isNaN(this.selectedIndex)) {
			const tabs = this._steps;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				return this._steps.toArray()[this.selectedIndex].stepDescription;
			}
		} else {
			return null;
		}
	}

	constructor(
		private cd: ChangeDetectorRef,
		private stepperService: StepperService,
		private dialogService: DialogService
	) {}

	ngOnInit() {}

	ngAfterContentInit() {}

	ngAfterContentChecked() {}

	ngAfterViewInit(): void {
		this.stepperService.contents.subscribe((ref) => {
			if (this._current !== null) {
				this._current.destroy();
				this._current = null;
			}
			if (ref === null) {
				return;
			}
			this._current = this.vcr.createEmbeddedView(ref);
		});
		this.stepperService.setContents(
			this._steps.toArray()[this.selectedIndex].buttonContainerDirective.ref
		);
	}

	next(): void {
		if (this.selectedIndex < this._steps.length - 1) {
			this.selectedIndex += 1;
			this.stepperService.setContents(
				this._steps.toArray()[this.selectedIndex].buttonContainerDirective.ref
			);
		}
	}

	previous(): void {
		if (this.selectedIndex !== 0 && this.selectedIndex < this._steps.length) {
			this.selectedIndex -= 1;
			this.stepperService.setContents(
				this._steps.toArray()[this.selectedIndex].buttonContainerDirective.ref
			);
		}
	}

	openGobackDialog() {
		const dialogRef = this.dialogService.confirm(
			this.traducao.getLabel("confirm-goback-title"),
			this.traducao.getLabel("confirm-goback-body"),
			this.traducao.getLabel("confirm-goback-proceed")
		);
		dialogRef.result
			.then((result) => {
				this.goBack.emit();
			})
			.catch((err) => {});
	}
}
