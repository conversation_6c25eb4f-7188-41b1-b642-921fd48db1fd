import { Directive, HostListener } from "@angular/core";
import { CatStepperSimpleComponent } from "../../components/cat-stepper-simple/cat-stepper-simple.component";

@Directive({
	selector: "[pactoCatStepSimplePrevious]",
})
export class CatStepSimplePreviousDirective {
	constructor(public stepper: CatStepperSimpleComponent) {}

	@HostListener("click")
	public previousStep() {
		this.stepper.previous();
	}
}
