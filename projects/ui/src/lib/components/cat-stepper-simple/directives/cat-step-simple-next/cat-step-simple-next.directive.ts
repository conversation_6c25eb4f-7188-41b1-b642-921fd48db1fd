import { Directive, ElementRef, HostListener } from "@angular/core";
import { CatStepperSimpleComponent } from "../../components/cat-stepper-simple/cat-stepper-simple.component";

@Directive({
	selector: "[pactoCatStepSimpleNext]",
})
export class CatStepSimpleNextDirective {
	constructor(
		public stepper: CatStepperSimpleComponent,
		private el: ElementRef
	) {}

	@HostListener("click")
	public nextStep() {
		const ngReflectDisabled = this.el.nativeElement.getAttribute(
			"ng-reflect-disabled"
		);
		if (
			ngReflectDisabled === null ||
			ngReflectDisabled === undefined ||
			ngReflectDisabled === "false"
		) {
			this.stepper.next();
		}
	}
}
