import { storiesOf, moduleMetadata } from "@storybook/angular";
import {
	CatLabelValueIconComponent,
	CatCardPlainComponent,
	PactoIcon,
} from "projects/ui/src/public-api";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div class="host-wrapper" style="width: 400px; margin: 80px;">
			<pacto-cat-card-plain>
				<pacto-cat-label-value-icon
					[label]="label"
					[value]="value"
					[icon]="icon"></pacto-cat-label-value-icon>
			</pacto-cat-card-plain>
		</div>
	`,
})
class HostComponent {
	@Input() label;
	@Input() value;
	@Input() icon;
}

const metadata = {
	declarations: [CatLabelValueIconComponent, CatCardPlainComponent],
};

storiesOf("Layout|Label Value Icon", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Única columa", () => {
		return {
			component: HostComponent,
			props: {
				label: "Preço por Unidade",
				value: "R$3.000,00",
				icon: PactoIcon.PCT_CHECK_CIRCLE,
			},
		};
	});
