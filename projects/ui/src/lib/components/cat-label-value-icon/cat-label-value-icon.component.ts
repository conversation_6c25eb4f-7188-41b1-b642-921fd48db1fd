import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";
import { PactoIcon } from "../../pacto-icon.enum";

@Component({
	selector: "pacto-cat-label-value-icon",
	templateUrl: "./cat-label-value-icon.component.html",
	styleUrls: ["./cat-label-value-icon.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatLabelValueIconComponent implements OnInit {
	@Input() label = "-";
	@Input() value = "-";
	@Input() icon: PactoIcon;

	constructor() {}

	ngOnInit() {}
}
