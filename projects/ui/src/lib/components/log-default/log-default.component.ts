import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
	GridFilterConfig,
	GridFilterType,
	LogComponent,
	PactoDataGridConfig,
	TableData,
} from "../components-api";
import { SnotifyService } from "ng-snotify";
import moment from "moment";
import { ModalLogDefaultComponent } from "./modal-log/modal-log.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ClientDiscoveryService } from "sdk";

@Component({
	selector: "pacto-log-default",
	templateUrl: "./log-default.component.html",
	styleUrls: ["./log-default.component.scss"],
})
export class LogDefaultComponent implements OnInit {
	@Input() apiBaseurl: string;
	@Input() logEntities: string[];

	@Input() dataAdapterFn?: (serverData: any) => TableData<any>;
	@Input() onError?: (error: any) => void;
	@Input() paramAdapterFn?: (params: any) => any;

	@ViewChild("logComponent", { static: true }) logComponent: LogComponent;

	@ViewChild("colunaValorAnterior", { static: true }) colunaValorAnterior;
	@ViewChild("colunaValorAlterado", { static: true }) colunaValorAlterado;

	constructor(
		private notificationService: SnotifyService,
		private discoveryService: ClientDiscoveryService,
		private ngbModal: NgbModal
	) {}

	ngOnInit() {
		if (this.apiBaseurl === undefined) {
			this.apiBaseurl = this.discoveryService.getUrlMap().admMsUrl;
		}

		this.validateInputs();
	}

	validateInputs() {
		if (this.logEntities === undefined) {
			throw new Error("O atributo logEntities precisa ser informado");
		}
	}

	get dataGridConfig(): PactoDataGridConfig {
		const filtroEntidades = this.logEntities
			.map((entidade) => `nomesentidades=${entidade}`)
			.join("&");

		const url = this.apiBaseurl + "/v1/log";

		return new PactoDataGridConfig({
			endpointUrl: `${url}?nomesentidades=configuracaosistema&${filtroEntidades}`,
			quickSearch: true,
			exportButton: false,
			endpointParamsType: "query",
			paramAdapterFn: this.paramAdapterFn,
			dataAdapterFn: this.dataAdapterFn,
			onError: (e) => {
				let msg = `Falha ao carregar log da api ${this.apiBaseurl}`;
				if (e && e.error && e.error.error) {
					msg = e.error.error;
				}

				this.notificationService.error(msg);
			},
			columns: [
				{
					nome: "operacao",
					titulo: "Operação",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "dataAlteracao",
					titulo: "Data",
					valueTransform: (v) => moment(v).format("DD/MM/YYYY HH:mm:ss"),
					visible: true,
					ordenavel: false,
				},
				{
					nome: "responsavelAlteracao",
					titulo: "Usuário",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nomeCampo",
					titulo: "Campo",
					visible: true,
					valueTransform: (value, row) =>
						row.nomeCampoTitulo ||
						row.nomeCampoDescricao ||
						row.nomeCampo ||
						value,
					ordenavel: false,
				},
				{
					nome: "valorCampoAnterior",
					titulo: "Valor anterior",
					valueTransform: this.valueTransFromBolleanToString,
					visible: true,
					ordenavel: false,
					celula: this.colunaValorAnterior,
				},
				{
					nome: "valorCampoAlterado",
					titulo: "Valor alterado",
					valueTransform: this.valueTransFromBolleanToString,
					visible: true,
					ordenavel: false,
					celula: this.colunaValorAlterado,
				},
			],
		});
	}

	valueTransFromBolleanToString(value: any) {
		if (value === "true") {
			return "Ativo";
		}

		if (value === "false") {
			return "Inativo";
		}

		return value;
	}

	get dataGridFilterConfig(): GridFilterConfig {
		return {
			filters: [
				{
					name: "operacoes",
					label: "Operação",
					type: GridFilterType.MANY,
					options: [
						{ value: "INCLUSAO", label: "Inclusão" },
						{ value: "ALTERACAO", label: "Alteração" },
						{ value: "EXCLUSAO", label: "Exclusão" },
					],
				},
				{
					name: "dataInicio",
					label: "Data de inicio",
					type: GridFilterType.DATE_POINT,
					transformDateString: true,
				},
				{
					name: "dataFim",
					label: "Data final",
					type: GridFilterType.DATE_POINT,
					transformDateString: true,
				},
			],
		};
	}

	onClickColumnValue(item: any) {
		const modal = this.ngbModal.open(ModalLogDefaultComponent, {
			windowClass: "custom-ngb-modal-window",
			backdropClass: "custom-ngb-modal-backdrop",
			centered: true,
		});
		modal.componentInstance.logComponent = this.logComponent;
		modal.componentInstance.item = item;
		this.logComponent.activeModal.close();
	}
}
