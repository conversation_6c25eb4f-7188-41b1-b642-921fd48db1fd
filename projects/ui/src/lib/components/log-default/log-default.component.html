<pacto-log
	#logComponent
	[dataGridConfig]="dataGridConfig"
	[dataGridFilterConfig]="dataGridFilterConfig"></pacto-log>

<ng-template #colunaValorAnterior let-item="item">
	<span (click)="onClickColumnValue(item)" class="column-value">
		{{ valueTransFromBolleanToString(item?.valorCampoAnterior) }}
	</span>
</ng-template>

<ng-template #colunaValorAlterado let-item="item">
	<span (click)="onClickColumnValue(item)" class="column-value">
		{{ valueTransFromBolleanToString(item?.valorCampoAlterado) }}
	</span>
</ng-template>
