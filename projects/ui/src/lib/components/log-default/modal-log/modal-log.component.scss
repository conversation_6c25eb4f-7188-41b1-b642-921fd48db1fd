@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/scss/cores.vars";

.modal-title {
	font-size: 20px;
	font-weight: 600;
	line-height: 20px;
}

.modal-header {
	border-bottom: solid #c7c9cc 1px;
}

.modal-footer {
	border-top: none;
}

.log-modal-container {
	display: flex;
	padding: 16px;
}

.icon-close-container {
	cursor: pointer;
}

.value-column {
	padding-top: 30px;
	padding-bottom: 30px;
	padding: 8px 16px 8px 16px;
	background-color: $plane03;
	color: $typeDefaultTitle;
	cursor: pointer;
	text-align: justify;
	display: flex;
	justify-content: center;
	align-items: center;
}

.title-column {
	@extend .pct-title5;

	display: flex;
	justify-content: center;
	align-items: center;
	height: 56px;
	border-bottom: solid #c7c9cc 2px;
	background-color: #ffffff;
}

.footer {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding: 16px 16px 16px 16px;
	height: 63px;
}

.table-column {
	flex-grow: 1;
	text-align: center;
}
