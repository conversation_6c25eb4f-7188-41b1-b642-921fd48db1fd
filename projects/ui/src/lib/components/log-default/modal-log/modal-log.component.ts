import { Component, Input, OnInit, ViewEncapsulation } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { LogComponent } from "../../components-api";

@Component({
	selector: "pacto-modal-log-default",
	templateUrl: "./modal-log.component.html",
	styleUrls: ["./modal-log.component.scss"],
})
export class ModalLogDefaultComponent implements OnInit {
	item: any;
	@Input() logComponent: LogComponent;

	constructor(private activeModal: NgbActiveModal) {}

	ngOnInit() {}

	get title() {
		return this.item.nomeCampoTitulo
			? this.item.nomeCampoTitulo
			: this.item.nomeCampo;
	}

	close() {
		this.activeModal.dismiss();
	}

	goBack() {
		this.activeModal.dismiss();
		this.logComponent.show();
	}

	valueTransFromBolleanToString(value: any) {
		if (value === "true") {
			return "Ativo";
		}

		if (value === "false") {
			return "Inativo";
		}

		return value;
	}

	get valorAnterior() {
		return this.valueTransFromBolleanToString(this.item.valorCampoAnterior);
	}

	get valorAlterado() {
		return this.valueTransFromBolleanToString(this.item.valorCampoAlterado);
	}
}
