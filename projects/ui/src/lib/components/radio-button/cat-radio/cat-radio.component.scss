@import "projects/ui/assets/import.scss";

.radio {
	border: 2px solid #51555a;
	border-radius: 50%;
	padding: 2px;

	&.checked {
		border: 2px solid $azulim05;

		.radio-intern {
			background: $azulim05;
		}

		&.disabled {
			.radio-intern {
				background: #797d86;
			}
		}
	}

	&.disabled {
		cursor: default;
		border-color: #797d86;
	}
}

.radio-intern {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #fff;
}

.content-div {
	color: $cinza05;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 20px;
	height: 20px;

	&.checked {
		color: #fff;
	}
}

.option-label {
	cursor: pointer;
	height: auto;
	display: flex;
	align-items: baseline;
}

.left-label {
	margin-right: 10px;
}

.right-label,
.right-label label {
	margin: 0px 30px 0px 10px;
}
