import {
	AfterViewInit,
	Component,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	Output,
	Renderer2,
	ViewChild,
} from "@angular/core";

@Component({
	selector: "pacto-cat-radio",
	templateUrl: "./cat-radio.component.html",
	styleUrls: ["./cat-radio.component.scss"],
})
export class CatRadioComponent implements AfterViewInit {
	@Input() checked = false;
	@ViewChild("labelElement", { static: false }) labelElement;
	showLabel = true;
	@Input() value: any;
	radius: string;
	labelPosition: "left" | "right" = "right";
	clickEvent: EventEmitter<any> = new EventEmitter<any>();
	disabled: boolean;
	// tslint:disable-next-line:no-output-rename
	@Output("checked") checkedEvent: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	@HostListener("click", ["$event"]) handlerClick($event) {
		if (!this.disabled) {
			this.clickEvent.emit(this);
			this.checkedEvent.emit(this.checked);
		}
	}

	constructor(public elementRef: ElementRef, private renderer: Renderer2) {}

	ngAfterViewInit() {
		this.showLabel =
			this.labelElement.nativeElement &&
			this.labelElement.nativeElement.children.length > 0;
		const label =
			this.elementRef.nativeElement.getElementsByTagName("label")[0];
	}
}
