import {
	AfterContentInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChild,
	ContentChildren,
	Input,
	OnInit,
	Optional,
	QueryList,
	ViewChild,
} from "@angular/core";
import {
	ControlContainer,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { CatRadioComponent } from "./cat-radio/cat-radio.component";

@Component({
	selector: "pacto-cat-radio-group",
	templateUrl: "./cat-radio-group.component.html",
	styleUrls: ["./cat-radio-group.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: CatRadioGroupComponent,
			multi: true,
		},
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatRadioGroupComponent implements OnInit, AfterContentInit {
	@Input() formControlName = "";
	@ViewChild(FormControlDirective, { static: true }) formControlDirective:
		| FormControlDirective
		| undefined;
	@Input() formControl: FormControl | undefined;
	@Input() labelPosition: "left" | "right" = "right";
	@ContentChildren(CatRadioComponent, { descendants: true })
	options: QueryList<CatRadioComponent> = new QueryList<CatRadioComponent>();
	private onChange: (value: any) => void = () => {};
	private onTouched: () => void = () => {};

	get control(): any {
		return (
			this.formControl ||
			(this.controlContainer &&
				this.controlContainer.control &&
				this.controlContainer.control.get(this.formControlName))
		);
	}

	constructor(
		@Optional()
		private controlContainer: ControlContainer,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit(): void {}

	ngAfterContentInit() {
		this.options.forEach((item) => {
			if (this.control.disabled) {
				item.disabled = true;
			}
			if (item.value === this.control.value) {
				item.checked = true;
				this.selectOption(item);
			}
			item.labelPosition = this.labelPosition;
			item.clickEvent.subscribe((value) => {
				this.selectOption(value);
				this.onChange(item.value); // informa Angular
			});
		});
	}

	// selectOption(option: CatRadioComponent): void {
	// 	if (option) {
	// 		if (this.control) {
	// 			if (this.options) {
	// 				this.control.setValue(option.value);
	// 				this.options.forEach((opt) => {
	// 					if (opt.value !== option.value && opt.checked) {
	// 						opt.checked = false;
	// 					} else if (opt.value === option.value && opt.checked === false) {
	// 						opt.checked = true;
	// 					}
	// 				});
	// 			}
	// 		}
	// 	}
	// }

	selectOption(option: CatRadioComponent): void {
		if (option && this.options) {
			this.options.forEach((opt) => {
				opt.checked = opt.value === option.value;
			});
			this.cd.markForCheck();
		}
	}

	// registerOnTouched(fn: any): void {
	// 	if (this.formControlDirective && this.formControlDirective.valueAccessor) {
	// 		this.formControlDirective.valueAccessor.registerOnTouched(fn);
	// 	}
	// }

	// registerOnChange(fn: any): void {
	// 	if (this.formControlDirective && this.formControlDirective.valueAccessor) {
	// 		this.formControlDirective.valueAccessor.registerOnChange(fn);
	// 	}
	// }

	// writeValue(obj: any): void {
	// 	if (this.formControlDirective && this.formControlDirective.valueAccessor) {
	// 		this.formControlDirective.valueAccessor.writeValue(obj);
	// 	}
	// }

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective && this.formControlDirective.valueAccessor) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
		}
	}

	registerOnChange(fn: any): void {
		this.onChange = fn;
	}

	registerOnTouched(fn: any): void {
		this.onTouched = fn;
	}

	writeValue(value: any): void {
		if (value !== undefined) {
			this.options.forEach((opt) => {
				opt.checked = opt.value === value;
			});
			this.cd.markForCheck();
		}
	}
}
