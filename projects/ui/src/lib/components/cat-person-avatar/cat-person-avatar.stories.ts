import { storiesOf, moduleMetadata } from "@storybook/angular";
import { CatPersonAvatarComponent } from "./cat-person-avatar.component";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div class="host-wrapper" style="width: 400px; margin: 80px;">
			<pacto-cat-person-avatar
				[uri]="url"
				[diameter]="diameter"
				[borderRadius]="borderRadius"></pacto-cat-person-avatar>
		</div>
	`,
})
class HostComponent {
	@Input() url;
	@Input() diameter;
	@Input() borderRadius;
}

const metadata = {
	declarations: [CatPersonAvatarComponent],
};

storiesOf("Person Avatar", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Round", () => {
		return {
			component: HostComponent,
			props: {
				url: "https://www.assiscity.com/img/2012/01/24/fileg_59743.jpg",
				diameter: 150,
				borderRadius: 75,
			},
		};
	})
	.add("Squre", () => {
		return {
			component: HostComponent,
			props: {
				url: "https://www.assiscity.com/img/2012/01/24/fileg_59743.jpg",
				diameter: 150,
				borderRadius: 10,
			},
		};
	});
