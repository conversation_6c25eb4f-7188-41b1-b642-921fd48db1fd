<div
	[ngStyle]="{
		'background-image': backgroundImage,
		width: diameter + 'px',
		height: diameter + 'px',
		'border-radius': borderRadius + 'px',
		border: border
	}"
	class="avatar-wrapper">
	<img
		*ngIf="parqPositivo"
		[ngStyle]="{
			left: imageLeft,
			bottom: imageBottom,
			width: 34 + 'px',
			height: 34 + 'px'
		}"
		class="parq"
		loading="lazy"
		src="pacto-ui/images/parq-positivo.svg" />
</div>
