import { Component, OnInit, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-person-avatar",
	templateUrl: "./cat-person-avatar.component.html",
	styleUrls: ["./cat-person-avatar.component.scss"],
})
export class CatPersonAvatarComponent implements OnInit {
	@Input() uri;
	@Input() diameter = 50;
	@Input() borderRadius: number;
	@Input() showBorder = true;
	@Input() parqPositivo = false;

	constructor() {}

	ngOnInit() {
		if (!this.uri) {
			this.uri = "pacto-ui/images/default-user-icon.png";
		}
		if (!this.borderRadius) {
			this.borderRadius = this.diameter / 2;
		}
	}

	get backgroundImage() {
		return `url('${this.uri}')`;
	}

	get border() {
		if (this.showBorder) {
			return this.showBorder;
		} else {
			let width = Math.trunc((this.diameter / 50) * 2);
			width = width < 1 ? 1 : width;
			return `${width}px solid`;
		}
	}

	get imageLeft() {
		return `calc(50% + 32.3333px)`;
	}

	get imageBottom() {
		return `75.3333px`;
	}
}
