import { Component, OnInit } from "@angular/core";
import { Subject } from "rxjs";
import { LoaderService } from "./loader.service";

@Component({
	selector: "pacto-loader2",
	templateUrl: "./loader.component.html",
	styleUrls: ["./loader.component.scss"],
})
export class LoaderComponent implements OnInit {
	public loading: Subject<boolean> = this.loaderService.isLoading;

	constructor(private loaderService: LoaderService) {}

	ngOnInit() {}
}
