import { Injectable } from "@angular/core";
import { Subject } from "rxjs";

@Injectable({
	providedIn: "root",
})
export class LoaderService {
	public isLoading = new Subject<boolean>();
	main$ = this.isLoading.asObservable();
	public forcing = false;

	show() {
		if (!this.forcing) {
			this.isLoading.next(true);
		}
	}

	hide() {
		if (!this.forcing) {
			this.isLoading.next(false);
		}
	}

	initForce() {
		this.forcing = true;
		this.isLoading.next(true);
	}

	stopForce() {
		this.forcing = false;
		this.isLoading.next(false);
	}
}
