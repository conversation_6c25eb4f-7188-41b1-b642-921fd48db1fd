import {
	AfterContentInit,
	AfterViewInit,
	Component,
	ContentChild,
	Directive,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnDestroy,
	Output,
	ViewChild,
} from "@angular/core";

@Directive({
	selector: "[pactoCatCameraCapture]",
})
export class CatCameraCaptureDirective {
	@Output() capture: EventEmitter<any> = new EventEmitter<any>();

	@HostListener("click", ["$event"])
	captureEvent(event) {
		this.capture.emit();
	}
}

@Component({
	selector: "pacto-cat-camera",
	templateUrl: "./cat-camera.component.html",
	styleUrls: ["./cat-camera.component.scss"],
})
export class CatCameraComponent
	implements AfterViewInit, AfterContentInit, OnDestroy
{
	@Input() width = 640;
	@Input() height = 480;
	@Input() multi = false;

	@Output() images: EventEmitter<any> = new EventEmitter<any>();
	@Output() error: EventEmitter<any> = new EventEmitter<any>();

	@ViewChild("video", { static: false })
	public video: ElementRef;

	@ViewChild("canvas", { static: false })
	public canvas: ElementRef;

	@ContentChild(CatCameraCaptureDirective, { static: false })
	catCameraCaptureDirective: CatCameraCaptureDirective;

	captures: string[] = [];
	loadCamera = true;
	stream: MediaStream;

	async ngAfterViewInit() {
		await this.setupDevices();
	}

	ngAfterContentInit() {
		if (this.catCameraCaptureDirective) {
			this.catCameraCaptureDirective.capture.subscribe(() => {
				this.captureImage();
			});
		}
	}

	ngOnDestroy() {
		this.stream.getTracks().forEach((track) => track.stop());
	}

	async setupDevices() {
		if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
			try {
				this.stream = await navigator.mediaDevices.getUserMedia({
					video: true,
				});
				if (this.stream) {
					this.video.nativeElement.srcObject = this.stream;
					this.video.nativeElement.play();
					this.loadCamera = true;
				} else {
					this.error.emit({
						id: "no-device",
						message: "You have no output video device",
					});
					this.loadCamera = false;
				}
			} catch (e) {
				this.loadCamera = false;
				this.error.emit({ id: typeof e, message: e });
			}
		}
	}

	captureImage() {
		this.drawImageToCanvas(this.video.nativeElement);
		const image = this.canvas.nativeElement.toDataURL("image/png");
		if (this.multi) {
			this.captures.push(image);
			this.images.emit(this.captures);
		} else {
			this.images.emit(image);
			this.video.nativeElement.stop();
		}
	}

	drawImageToCanvas(image: any) {
		this.canvas.nativeElement
			.getContext("2d")
			.drawImage(image, 0, 0, this.width, this.height);
	}
}
