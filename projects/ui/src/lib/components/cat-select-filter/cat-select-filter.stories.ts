import { Component, Input, NO_ERRORS_SCHEMA } from "@angular/core";
import {
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { HttpClientModule } from "@angular/common/http";

import { TextMaskModule } from "angular2-text-mask";
import { moduleMetadata, storiesOf } from "@storybook/angular";

import {
	CatFormInputComponent,
	SelectItemDirective,
	CatButtonComponent,
	CatSelectFilterComponent,
	CatPersonAvatarComponent,
	CatSmoothScrollDirective,
} from "projects/ui/src/public-api";

@Component({
	selector: "pacto-cat-host",
	templateUrl: "./cat-select-filter.stories.html",
	styleUrls: ["./cat-select-filter.stories.scss"],
})
class HostComponent {
	@Input() endpointUrl;
	@Input() resposeParser;
	@Input() control;
	@Input() size;
	@Input() options;
	@Input() labelKey;
	@Input() label;
	@Input() customInput;

	toggleHandler() {
		if (this.control.enabled) {
			this.control.disable();
		} else {
			this.control.enable();
		}
	}

	sendHandler() {
		this.control.markAsTouched();
	}
}

const metadata = {
	imports: [FormsModule, HttpClientModule, TextMaskModule, ReactiveFormsModule],
	declarations: [
		CatButtonComponent,
		SelectItemDirective,
		CatSmoothScrollDirective,
		CatSelectFilterComponent,
		CatPersonAvatarComponent,
		CatFormInputComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
};

storiesOf("Raw Input|Filter Select", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Normal", () => {
		return {
			component: HostComponent,
			props: {
				endpointUrl: null,
				options: [
					{ id: 1, nome: "Ryu" },
					{ id: 2, nome: "Guile" },
					{ id: 3, nome: "Chun-li" },
					{ id: 3, nome: "M. Bison" },
					{ id: 4, nome: "Ken Masters" },
					{ id: 5, nome: "Sagat" },
				],
				label: "Personagem",
				labelKey: "nome",
				// imageKey: 'afa',
				control: new FormControl(null, Validators.required),
				resposeParser: () => {
					return [];
				},
			},
		};
	})
	.add("Small", () => {
		return {
			component: HostComponent,
			props: {
				endpointUrl: "fake",
				label: "Personagem",
				size: "SMALL",
				labelKey: "nome",
				imageKey: "afa",
				control: new FormControl(null, Validators.required),
				resposeParser: () => {
					return [
						{ id: 1, nome: "Ryu" },
						{ id: 2, nome: "Guile" },
						{ id: 3, nome: "Chun-li" },
						{ id: 3, nome: "M. Bison" },
						{ id: 4, nome: "Ken Masters" },
						{ id: 5, nome: "Sagat" },
					];
				},
			},
		};
	});
