import { storiesOf, moduleMetadata } from "@storybook/angular";
import {
	CatTabsComponent,
	TabDirective,
	CatButtonComponent,
} from "projects/ui/src/public-api";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div
			class="host-wrapper"
			style="
        padding: 50px;
        background-color: #eee;
        height: 600px;
    ">
			<pacto-cat-tabs
				[showAction]="showAction"
				[actionIcon]="actionIcon"
				[actionLabel]="actionLabel">
				<ng-template pactoTab="'tab_a'" label="Tab A">
					<div style="padding: 20px">
						<div>Conteúdo da Tab A</div>
						<div>Conteúdo da Tab A</div>
						<div>Conteúdo da Tab A</div>
					</div>
				</ng-template>
				<ng-template pactoTab="'tab_b'" label="Tab B">
					<div style="padding: 20px">
						<div><PERSON><PERSON><PERSON><PERSON> da Tab B</div>
						<div><PERSON><PERSON><PERSON><PERSON> da Tab B</div>
						<div>Con<PERSON><PERSON><PERSON> da Tab B</div>
					</div>
				</ng-template>
			</pacto-cat-tabs>
		</div>
	`,
})
class HostComponent {
	@Input() actionLabel;
	@Input() actionIcon;
	@Input() showAction;
}

const metadata = {
	declarations: [CatTabsComponent, CatButtonComponent, TabDirective],
};

storiesOf("Layout|Tabs", module)
	.addDecorator(moduleMetadata(metadata))
	.add("Simple Tabs", () => {
		return {
			component: HostComponent,
			props: {},
		};
	})
	.add("Tabs with Button", () => {
		return {
			component: HostComponent,
			props: {
				actionLabel: "Create New",
				actionIcon: "pct pct-user",
				showAction: true,
			},
		};
	});
