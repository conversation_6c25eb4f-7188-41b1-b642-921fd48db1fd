@import "projects/ui/assets/import.scss";

.pacto-tabs-wrapper {
	display: flex;
	flex-wrap: wrap;
}

.tabs {
	display: flex;
	flex-grow: 1;

	.tab {
		@extend .type-p-small;
		line-height: 42px;
		color: $gelo04;
		background-color: $branco;
		border-right: 3px solid $cinzaClaroPri;
		padding: 0px 20px;
		min-width: 60px;
		cursor: pointer;
		text-align: center;

		&:first-child {
			border-top-left-radius: 10px;
		}

		&:last-child {
			border-top-right-radius: 10px;
			border-right: 0px;
		}

		&.active {
			color: $branco;
			background-color: $azulimPri;
		}
	}
}

.tab-content {
	width: 100%;
	flex-basis: 100%;
	background-color: $branco;
	border-top-right-radius: 10px;
	border-bottom-left-radius: 10px;
	border-bottom-right-radius: 10px;
}
