<div class="pacto-tabs-wrapper">
	<div class="tabs">
		<div
			(click)="tabClickHandler($event, index)"
			*ngFor="let tab of tabNames; let index = index"
			[ngClass]="{
				active: index === tabIndex
			}"
			class="tab">
			{{ tab }}
		</div>
	</div>
	<pacto-cat-button
		(click)="action.emit(true)"
		*ngIf="showAction"
		[icon]="actionIcon"
		[label]="actionLabel"></pacto-cat-button>

	<div class="tab-content">
		<ng-container *ngTemplateOutlet="template"></ng-container>
	</div>
</div>
