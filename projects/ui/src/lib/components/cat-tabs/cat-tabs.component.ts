import {
	Component,
	OnInit,
	ContentChildren,
	AfterViewInit,
	QueryList,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	AfterContentChecked,
	Input,
	Output,
	EventEmitter,
} from "@angular/core";
import { TabDirective } from "./tab.directive";

@Component({
	selector: "pacto-cat-tabs",
	templateUrl: "./cat-tabs.component.html",
	styleUrls: ["./cat-tabs.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatTabsComponent
	implements OnInit, AfterViewInit, AfterContentChecked
{
	@ContentChildren(TabDirective) tabs: QueryList<TabDirective>;
	@Input() showAction = false;
	@Input() actionLabel;
	@Input() actionIcon;
	@Output() activateTab: EventEmitter<string> = new EventEmitter();
	@Output() action: EventEmitter<any> = new EventEmitter();
	tabNames: string[] = [];
	@Input() tabIndex = 0;
	tabId;

	constructor(private cd: ChangeDetectorRef) {}

	get template() {
		if (!isNaN(this.tabIndex)) {
			const tabs = this.tabs;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				return this.tabs.toArray()[this.tabIndex].ref;
			}
		} else {
			return null;
		}
	}

	ngOnInit() {}

	ngAfterViewInit() {
		this.tabClickHandler(null, this.tabIndex);
	}

	ngAfterContentChecked() {
		this.fetchTabNames();
		this.cd.detectChanges();
	}

	tabClickHandler($event: MouseEvent, index: number) {
		this.tabIndex = index;
		const tabId = this.tabs.toArray()[index].pactoTab;
		this.tabId = tabId;
		this.activateTab.emit(tabId);
	}

	private fetchTabNames() {
		if (this.tabs) {
			this.tabNames = [];
			this.tabs.forEach((tab) => {
				const name = tab.label ? tab.label : "";
				this.tabNames.push(name);
			});
		}
	}
}
