import { storiesOf, moduleMetadata } from "@storybook/angular";
import {
	CatPersonAvatarRatingComponent,
	CatPersonAvatarComponent,
} from "projects/ui/src/public-api";
import { Component, Input } from "@angular/core";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div class="host-wrapper" style="width: 400px; margin: 80px;">
			<pacto-cat-person-avatar-rating
				[uri]="url"
				[rating]="rating"></pacto-cat-person-avatar-rating>
		</div>
	`,
})
class HostComponent {
	@Input() url;
	@Input() rating;
}

const metadata = {
	declarations: [CatPersonAvatarRatingComponent, CatPersonAvatarComponent],
};

storiesOf("Person Rating Avatar", module)
	.addDecorator(moduleMetadata(metadata))
	.add("1 Estrela", () => {
		return {
			component: HostComponent,
			props: {
				url: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRbxHMd2az0Epu6Jafi-3X8Kytwp6AXRoWNJlEmi3oOd6Lk9KCT",
				rating: 1,
			},
		};
	})
	.add("5 Estrela", () => {
		return {
			component: HostComponent,
			props: {
				url: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRbxHMd2az0Epu6Jafi-3X8Kytwp6AXRoWNJlEmi3oOd6Lk9KCT",
				rating: 5,
			},
		};
	});
