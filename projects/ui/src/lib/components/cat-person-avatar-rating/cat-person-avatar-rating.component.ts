import {
	Component,
	OnInit,
	Input,
	ChangeDetectionStrategy,
} from "@angular/core";

@Component({
	selector: "pacto-cat-person-avatar-rating",
	templateUrl: "./cat-person-avatar-rating.component.html",
	styleUrls: ["./cat-person-avatar-rating.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatPersonAvatarRatingComponent implements OnInit {
	@Input() uri;
	@Input() rating: number;

	constructor() {}

	ngOnInit() {}

	golden(index: number) {
		const rounded = Math.round(this.rating);
		return index <= rounded;
	}

	iconPath(index: number) {
		const golden = this.golden(index);
		const type = golden ? "gold" : "grey";
		return `pacto-ui/images/pct-star-rating-${type}.svg`;
	}
}
