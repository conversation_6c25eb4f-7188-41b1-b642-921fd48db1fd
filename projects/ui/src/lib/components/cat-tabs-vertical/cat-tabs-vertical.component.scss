@import "projects/ui/assets/import.scss";

.pacto-tabs-wrapper {
	display: flex;
}

.tabs {
	border-bottom: 1px solid $gelo02;
	display: inline-block;
	min-width: 220px;

	.tab {
		@extend .type-p-small;
		line-height: 42px;
		color: $preto05;
		padding: 0px 20px;
		min-width: 60px;
		cursor: pointer;
		background: $cinzaPastel;
		border-bottom: 1px solid $cinza03;
		border-right: 1px solid $cinza03;
		font-weight: 600;

		&:last-child {
			border-bottom: none;
		}

		&.active {
			border-left: 3px solid $azulimPri;
			color: $azulimPri;
			background: #ffffff;
		}
	}
}

.table-ferias > .table-content {
	padding: unset !important;
}

.table-desconto-plano-duracao > .table-content {
	padding: unset !important;
}

.tab-content {
	flex-grow: 1;
	overflow: auto;
	padding: 30px 30px 0 30px;
}
