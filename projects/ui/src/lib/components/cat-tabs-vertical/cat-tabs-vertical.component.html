<div class="pacto-tabs-wrapper">
	<div class="tabs">
		<div
			(click)="tabClickHandler($event, index)"
			*ngFor="let tab of tabNames; let index = index"
			[ngClass]="{ active: index === tabIndex }"
			class="tab"
			id="tab-{{ idTab(index) }}">
			{{ tab }}
		</div>
	</div>

	<div
		[ngStyle]="{
			'max-height': 'calc(43px * ' + tabNames.length + ')'
		}"
		class="tab-content">
		<ng-container *ngTemplateOutlet="template"></ng-container>
	</div>
</div>
