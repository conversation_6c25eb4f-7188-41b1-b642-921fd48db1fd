import {
	AfterContentChecked,
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	EventEmitter,
	Input,
	OnInit,
	Output,
	QueryList,
} from "@angular/core";
import { TabVerticalDirective } from "./tab-vertical.directive";

@Component({
	selector: "pacto-cat-tabs-vertical",
	templateUrl: "./cat-tabs-vertical.component.html",
	styleUrls: ["./cat-tabs-vertical.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatTabsVerticalComponent
	implements OnInit, AfterViewInit, AfterContentChecked
{
	@ContentChildren(TabVerticalDirective) tabs: QueryList<TabVerticalDirective>;
	@Input() showAction = false;
	@Input() actionLabel;
	@Input() actionIcon;
	@Output() activateTab: EventEmitter<{
		previous: string;
		next: string;
	}> = new EventEmitter();
	@Output() action: EventEmitter<any> = new EventEmitter();
	tabNames: string[] = [];
	@Input() tabIndex = 0;
	tabId;

	constructor(private cd: ChangeDetectorRef) {}

	get template() {
		if (!isNaN(this.tabIndex)) {
			const tabs = this.tabs;
			if (tabs && tabs.length > 0 && tabs.toArray()) {
				return this.tabs.toArray()[this.tabIndex].ref;
			}
		} else {
			return null;
		}
	}

	ngOnInit() {}

	ngAfterViewInit() {
		setTimeout(() => {
			this.tabClickHandler(null, this.tabIndex);
		});
	}

	ngAfterContentChecked() {
		this.fetchTabNames();
		this.cd.detectChanges();
	}

	idTab(index: number) {
		const tab = this.tabs.toArray()[index].pactoTabVertical;
		return tab ? tab : index;
	}

	tabClickHandler($event: MouseEvent, index: number) {
		this.tabIndex = index;
		if (this.tabs && this.tabs.length && this.tabs.toArray()[index]) {
			const previous = this.tabId;
			const tabId = this.idTab(index);
			this.tabId = tabId;
			this.activateTab.emit({ previous, next: tabId });
		}
	}

	private fetchTabNames() {
		if (this.tabs) {
			this.tabNames = [];
			this.tabs.forEach((tab) => {
				const name = tab.label ? tab.label : "";
				this.tabNames.push(name);
			});
		}
	}
}
