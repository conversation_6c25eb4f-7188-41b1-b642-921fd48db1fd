import { Directive, ElementRef, Input, OnInit } from "@angular/core";

@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "span[xingling]",
})
export class LabelXinglingDirective implements OnInit {
	// tslint:disable-next-line: no-input-rename
	@Input("xingling") xingling: string;

	constructor(private elementRef: ElementRef) {}

	ngOnInit(): void {}

	get id() {
		return this.xingling;
	}

	get label(): string {
		if (this.elementRef) {
			return this.elementRef.nativeElement.innerHTML;
		}
	}
}
