import { Directive, TemplateRef, OnInit, Input } from "@angular/core";

@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "ng-template[xingling]",
})
export class TemplateXinglingDirective implements OnInit {
	// tslint:disable-next-line: no-input-rename
	@Input("xingling") xingling: string;

	constructor(private templateRef: TemplateRef<any>) {}

	ngOnInit(): void {}

	get id() {
		return this.xingling;
	}

	get ref(): TemplateRef<any> {
		return this.templateRef;
	}
}
