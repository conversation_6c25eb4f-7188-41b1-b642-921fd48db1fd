import {
	Component,
	OnInit,
	<PERSON><PERSON><PERSON>dren,
	<PERSON><PERSON>iew<PERSON>nit,
	QueryList,
} from "@angular/core";
import { TemplateXinglingDirective } from "./template-xingling.directive";
import { LabelXinglingDirective } from "./label-xingling.directive";

@Component({
	selector: "pacto-traducoes-xingling",
	template: "",
})
export class TraducoesXinglingComponent implements OnInit, AfterViewInit {
	@ContentChildren(TemplateXinglingDirective)
	templates: QueryList<TemplateXinglingDirective>;
	@ContentChildren(LabelXinglingDirective)
	labels: QueryList<LabelXinglingDirective>;
	public loaded = false;
	integracaoZw;

	constructor() {}

	ngOnInit() {
		setTimeout(() => {
			this.loaded = true;
		});
	}

	ngAfterViewInit() {}

	getTemplate(id) {
		let result;
		if (this.templates) {
			const templates: TemplateXinglingDirective[] = this.templates.toArray();
			for (const template of templates) {
				if (template.xingling === id) {
					result = template.ref;
					break;
				}
			}
		}
		if (this.loaded) {
			return result;
		} else {
			return undefined;
		}
	}

	getAllLabels(): string[] {
		const result = [];
		if (!this.labels) {
			return [];
		}
		this.labels.forEach((label) => {
			result.push(label.label);
		});
		return result;
	}

	getLabel(id): string {
		if (!id) {
			return "";
		} else {
			let result = `${id} (key)`;
			if (!this.labels) {
				return result;
			}
			this.labels.forEach((label) => {
				if (label.id === id) {
					result = label.label;
				}
			});
			return result;
		}
	}
}
