import { moduleMetadata, storiesOf } from "@storybook/angular";
import { withKnobs } from "@storybook/addon-knobs";
import { CatTabModule } from "ui-kit";
import {
	AfterViewInit,
	Component,
	OnChanges,
	SimpleChanges,
} from "@angular/core";
import Notes from "./tab.md";

@Component({
	selector: "pacto-cat-host",
	template: `
		<div class="content">
			<pacto-cat-tab-group>
				<pacto-cat-tab label="Label">Body</pacto-cat-tab>
				<pacto-cat-tab>
					<ng-template pactoCatTabLabel>
						<i class="pct pct-edit"></i>
						Custom label
					</ng-template>
					Body
				</pacto-cat-tab>
			</pacto-cat-tab-group>
		</div>
	`,
	styleUrls: ["tab.scss"],
})
class HostComponent implements AfterViewInit, OnChanges {
	ngAfterViewInit() {}

	ngOnChanges(changes: SimpleChanges) {}
}

storiesOf("Design System 3 | Navigation", module)
	.addDecorator(
		moduleMetadata({
			imports: [CatTabModule],
		})
	)
	.addDecorator(withKnobs)
	.addParameters({
		notes: { Notes },
	})
	.add("Tabs", () => {
		return {
			component: HostComponent,
			props: {},
		};
	});
