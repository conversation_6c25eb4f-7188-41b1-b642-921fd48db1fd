.full {
	width: 100%;
	border-radius: 4px;
}

tr th {
	background-color: hsl(0, 0%, 95%);
}

tr td:last-child {
	text-align: center;
}

.bubble {
	padding: 1rem;
	width: 100%;
	border-radius: 4px;
	margin-top: 0.8rem;
}

.bubble-sm {
	padding: 0.1rem 0.4rem;
	border-radius: 2rem;
}

.info {
	background-color: hsla(215, 100%, 74%, 20%);
}

.info-text {
	color: hsla(215, 100%, 54%);
}

.danger {
	background-color: hsla(360, 87%, 63%, 20%);
}

.danger-text {
	color: hsl(360, 87%, 63%);
}

.warning {
	background-color: hsla(43, 87%, 63%, 20%);
}

.warning-text {
	color: hsl(43, 87%, 63%);
}

td,
p,
code {
	font-size: 1rem;
}

h2[id],
h2[id]:first-of-type {
	margin-bottom: 0.8rem;
	margin-top: 1rem;
}

table.full tr > td:last-of-type {
	text-align: center;
}

table tr > th:last-of-type {
	white-space: nowrap;
}

.custom {
	width: 30%;
}
