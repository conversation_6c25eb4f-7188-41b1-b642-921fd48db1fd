import { moduleMetadata, storiesOf } from "@storybook/angular";
import { withKnobs, text, boolean } from "@storybook/addon-knobs";
import { CatFileInputComponent } from "ui-kit";

import { FormControl, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import Notes from "./cat-file-input.md";

export default {
	title: "Form | Input",
	decorators: [
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule],
			declarations: [CatFileInputComponent],
			providers: [{ provide: SnotifyService }],
		}),
		withKnobs,
	],
};

export const fileInput = () => {
	let formControl = new FormControl();
	let nomeControl = new FormControl();
	return {
		component: CatFileInputComponent,
		template: `
        <div class="col-md-4" style="margin-top: 1rem;">
            <pacto-cat-file-input
                [control]="formControl"
                [formatos]="formatos"
                [formatosValidos]="formatosValidos"
                [urlImage]="urlImage"
                [imgAlt]="imgAlt"
                [imageHeight]="imageHeight"
                [imageWidth]="imageWidth">
            </pacto-cat-file-input> 
        </div>
           
            `,
		props: {
			control: formControl,
			nomeControl,
			formatos: text("formatos", "jpeg, jpg, png, pdf, txt, doc e docx"),
			formatosValidos: text("formatosValidos", ""),
			urlImage: text("urlImage", ""),
			imgAlt: text("imgAlt", "Imagem selecionada"),
			imageHeight: text("imageHeight", ""),
			imageWidth: text("imageWidth", ""),
		},
	};
};

fileInput.story = {
	parameters: { notes: { Notes } },
};
