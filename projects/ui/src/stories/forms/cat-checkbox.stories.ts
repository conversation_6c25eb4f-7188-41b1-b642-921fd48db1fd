import { moduleMetadata } from "@storybook/angular";
import { withKnobs, text } from "@storybook/addon-knobs";
import { action } from "@storybook/addon-actions";

import { CatCheckboxComponent } from "ui-kit";
import {
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";

import Notes from "./cat-checkbox.md";

export default {
	title: "Form | Checkbox",
	decorators: [
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule],
			declarations: [CatCheckboxComponent],
		}),
		withKnobs,
	],
};

export const checkbox = () => {
	let formControl = new FormControl("", Validators.required);
	return {
		component: CatCheckboxComponent,
		template: `
        <div class="col-md-4">
            <pacto-cat-checkbox
                [label]="label"
                [control]="control"
                (valueChange)="onValueChange($event)"
            >
            </pacto-cat-checkbox>
        </div>
        `,
		props: {
			label: text("label", "Nome"),
			control: formControl,
			onValueChange: (e) => action("valueChange")(e),
		},
	};
};

checkbox.story = {
	parameters: {
		notes: { Notes },
	},
};
