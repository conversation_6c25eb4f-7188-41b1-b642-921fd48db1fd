# Pacto Datepicker

`tag: pacto-datepicker`

`usage: <pacto-datepicker></pacto-datepicker>`

`Example:`

```ts
import { Component } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {}

```

**meu-exemplo-de-tela.component.ts**

```html
<pacto-datepicker
    id=1
    name = "pacto-datepicker"
    label = "Data"
    required = "true"
    disabled = "false"
    pactoFormGroup = "formGroup"
    message = "Campo obrigatório"
    control = "control.get('data')"
    maxDate = "2022-27-10"
    minDate = "1918-1-1"
    direction = "right"
>
</pacto-datepicker>

```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>id <span class="bubble-sm info">optional</span></td>
            <td>
               Define um identificador único para o componente.
            </td>
            <td>```Number```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>name <span class="bubble-sm info">optional</span></td>
            <td>
                Define um nome para o componente. 
                </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>label <span class="bubble-sm info">optional</span></td>
            <td>
                Define o texto que fica acima do input do componente.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
         <tr>
            <td>required <span class="bubble-sm info">optional</span></td>
            <td>
                Define se o campo é obrigatório.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
        <tr>
            <td>disabled <span class="bubble-sm info">optional</span></td>
            <td>
                Desabilita o input.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
        <tr>
            <td>pactoFormGroup <span class="bubble-sm info">optional</span></td>
            <td>
                Define um FormGroup para o componente.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>message <span class="bubble-sm info">optional</span></td>
            <td>
                Define uma menssagem quando o input não é preenchido e está como campo obrigatório.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>control <span class="bubble-sm danger">required</span></td>
            <td>
                Define o control que o campo será vinculado ao formulário.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>maxDate <span class="bubble-sm info">optional</span></td>
            <td>
                Define a data máxima permitida no input.
            </td>
            <td>```Date```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>minDate <span class="bubble-sm info">optional</span></td>
            <td>
                Define a data mínima permitida no input.
            </td>
            <td>```Date```</td>
            <td>1/1/1918</td>
        </tr>
        <tr>
            <td>direction <span class="bubble-sm info">optional</span></td>
            <td>
                Define a direção <strong>right</strong> ou <strong>left</strong> de onde o modal de calendário
                deve ser aberto.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
    </tbody>
</table>

## Outputs / Events

<div class="bubble info">
    <p>Component has no custom output.</p>
</div>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style>
