# Cat Checkbox

`tag: pacto-cat-checkbox`

`usage: <pacto-cat-checkbox></pacto-cat-checkbox>`

`Example:`

```ts
import { Component } from '@angular/core';
import { FormControl } from '@angular/forms';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	formControl = new FormControl();

	onValueChange(): void {
		// Do something
	}
}
```

**meu-exemplo-de-tela.component.ts**

```html
<pacto-cat-checkbox
	label="Minha label"
	[control]="formControl"
	(valueChange)="onValueChange($event)"
>
</pacto-cat-checkbox>
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>id <span class="bubble-sm info">optional</span></td>
            <td>
               Atributo id do componente (atributo HTML)
            </td>
            <td>```String```</td>
            <td>Valor aleatório</td>
        </tr>
        <tr>
            <td>label <span class="bubble-sm danger">required</span></td>
            <td>
               Define label que fica ao lado da checkbox
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>control <span class="bubble-sm danger">required</span></td>
            <td>
                ```new FormControl('')``` - Formulários reativos angular, 
                responsável por controlar o valor selecionado no componente
            </td>
            <td>```FormControl```</td>
            <td>-</td>
        </tr>
    </tbody>
</table>

## Outputs / Events

<table class="full">
    <thead>
        <tr>
            <th>Output</th>
            <th>Description</th>
            <th>Type</th>
            <th>Return value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>valueChange</td>
            <td>
               Emite valor da seleção do checkbox a cada evento de input do usuário no componente.
            </td>
            <td>```Boolean```</td>
            <td>`'true' | 'false'`</td>
        </tr>
    </tbody>
</table>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style>
