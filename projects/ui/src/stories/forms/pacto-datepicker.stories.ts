import { moduleMetadata } from "@storybook/angular";
import {
	withKnobs,
	text,
	boolean,
	number,
	date,
	object,
} from "@storybook/addon-knobs";

import { DatepickerComponent } from "ui-kit";
import { NgbDatepickerModule } from "@ng-bootstrap/ng-bootstrap";

import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { TextMaskModule } from "angular2-text-mask";
import Notes from "./pacto-datepicker.md";

export default {
	title: "Form | Input",
	decorators: [
		moduleMetadata({
			imports: [
				FormsModule,
				ReactiveFormsModule,
				TextMaskModule,
				NgbDatepickerModule,
			],
			declarations: [DatepickerComponent],
		}),
		withKnobs,
	],
};

export const pactoDatepicker = () => {
	let pactoFormGroup = FormGroup;
	let formControl = new FormControl("", Validators.required);
	return {
		component: DatepickerComponent,
		template: `
        <div class="col-md-4">
             <pacto-datepicker
             [id]="id"
             [name]="name"
             [label]="label"
             [required]="required"
             [disabled]="disabled"
             [pactoFormGroup]="pactoFormGroup"
             [message]="message"
             [control]="control"
             [maxDate]="maxDate"
             [minDate]="minDate"
             [direction]="direction"
             >
             </pacto-datepicker>
        </div>
           
            `,
		props: {
			id: number("id", 1),
			name: text("Name", "Nome"),
			label: text("label", "Data"),
			required: boolean("required", false),
			disabled: boolean("disabled", false),
			pactoFormGroup: pactoFormGroup,
			control: formControl,
			message: text("message", "Campo obrigatório"),
			maxDate: date("maxDate", new Date()),
			minDate: object("minDate", { year: 1918, month: 1, day: 1 }),
			direction: text("direction", "left"),
		},
	};
};

pactoDatepicker.story = { parameters: { notes: { Notes } } };
