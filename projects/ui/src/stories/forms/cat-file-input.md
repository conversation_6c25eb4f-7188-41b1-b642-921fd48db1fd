# Cat File Input

`tag: pacto-cat-file-input`

`usage: <pacto-cat-file-input></pacto-cat-file-input>`

`Example:`

```ts
import { Component } from '@angular/core';
import {
    FormControl
} from '@angular/forms';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	formControl = new FormControl();
  formatos=

}
```

**meu-exemplo-de-tela.component.ts**

```html
 <pacto-cat-file-input
    [control]="formControl"
    [formatos]="formatos"
    [formatosValidos]="formatosValidos"
    [urlImage]="urlImage"
    [imgAlt]="imgAlt"
    [imageHeight]="imageHeight"
    [imageWidth]="imageWidth">
</pacto-cat-file-input> 
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>id <span class="bubble-sm info">optional</span></td>
            <td>
               Atributo id do componente (atributo HTML)
            </td>
            <td>```String```</td>
            <td>Valor aleatório</td>
        </tr>
        
        <tr>
            <td>formatos <span class="bubble-sm info">optional</span></td>
            <td>
               Define os formatos de arquivos que podem ser enviados e serão exibidos no controle na tela como informativo para usuário. Tem opção de escolher os principais aceitos por cada tela.
               <span class="warning">Os formatos aceitos são:<br> jpeg, jpg, png, pdf, txt, doc e docx</span>
            </td>
            <td>```String```</td>
            <td>jpeg, jpg, png, pdf, txt, doc e docx</td>
        </tr>
        <tr>
            <td>formatosValidos <span class="bubble-sm info">optional</span></td>
            <td>
                Define através de  ["Regex"](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp) os formatos de arquivos que podem ser enviados e serão exibidos no controle na tela como informativo para usuário. Tem opção de escolher os principais aceitos por cada tela.
               <span class="warning">Os formatos aceitos são:<br> (jpeg|jpg|png|pdf|txt|doc|docx)$</span>
            </td>
            <td>```String```</td>
            <td>(jpeg|jpg|png|pdf|txt|doc|docx)$</td>
        </tr>
        <tr>
            <td>control <span class="bubble-sm danger">required</span></td>
            <td>
                ```new FormControl('')``` - Formulários reativos angular, 
                responsável por controlar arquivos enviados no componente
            </td>
            <td>```FormControl```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>urlImage <span class="bubble-sm danger">required</span></td>
            <td>
              Variavel com endereço da imagem a ser enviado para a api
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>imgAlt <span class="bubble-sm info">optional</span></td>
            <td>
               Define o texto de Texto Alternativo que a imagem exibirá após o upload ser feito na tela.
            </td>
            <td>```String```</td>
            <td>Imagem selecionada</td>
        </tr>
        <tr>
            <td>imageHeight <span class="bubble-sm info">optional</span></td>
            <td>
               Define a altura do arquivo de imagem que exibirá após o upload ser feito na tela.
            </td>
            <td>```Number```</td>
            <td>(tamanho real enviado)</td>
        </tr>
        <tr>
            <td>imageWidth <span class="bubble-sm info">optional</span></td>
            <td>
               Define a largura do arquivo de imagem que exibirá após o upload ser feito na tela.
            </td>
            <td>```Number```</td>
            <td>(tamanho real enviado)</td>
        </tr>
        
        
    </tbody>
</table>


## Outputs / Events

<div class="bubble info">
    <p>Component has no custom output.</p>
</div>


<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style>
