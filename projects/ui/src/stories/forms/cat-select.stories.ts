import { moduleMetadata } from "@storybook/angular";
import {
	withKnobs,
	text,
	boolean,
	number,
	date,
	object,
} from "@storybook/addon-knobs";
import { CatSelectComponent } from "ui-kit";
import {
	FormControl,
	FormGroup,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import Notes from "./cat-select.md";

export default {
	title: "Form | Select",
	decorators: [
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule],
			declarations: [CatSelectComponent],
		}),
		withKnobs,
	],
};

export const pactoCatSelect = () => {
	let formControl = new FormControl("", Validators.required);
	const items = [{ codigo: 1, nome: "name" }];
	return {
		component: CatSelectComponent,
		template: `
      <div class="col-md-4">
           <pacto-cat-select
            [id]="id"
            [items]="items"
            [idKey]="idKey"
            [labelKey]="labelKey"
            [label]="label"
            [control]="control"
            [size]="size"
           >
           </pacto-cat-select>
      </div>
         
          `,
		props: {
			id: number("id", 1),
			items: items,
			idKey: number("idKey", 1),
			labelKey: text("labelKey", ""),
			label: text("label", "select"),
			control: formControl,
			size: text("size", "PactoSelectSize.NORMAL"),
		},
	};
};

pactoCatSelect.story = { parameters: { notes: { Notes } } };
