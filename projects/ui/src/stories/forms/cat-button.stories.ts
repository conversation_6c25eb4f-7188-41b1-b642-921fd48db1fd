import { moduleMetadata } from "@storybook/angular";
import { withKnobs, text, boolean, select } from "@storybook/addon-knobs";

import { CatButtonComponent, BUTTON_TYPE, BUTTON_SIZE } from "ui-kit";

import Notes from "./cat-button.md";

export default {
	title: "Form | Button",
	decorators: [
		moduleMetadata({
			declarations: [CatButtonComponent],
		}),
		withKnobs,
	],
	argTypes: {
		label: {
			description: "Overwritten description",
			table: {
				type: {
					summary: "Something short",
					detail: "Something really really long",
				},
			},
			control: {
				type: null,
			},
		},
	},
};

const BUTTON_TYPES = Object.keys(BUTTON_TYPE).map((key) => key);
const BUTTON_SIZES = Object.keys(BUTTON_SIZE).map((key) => key);
const ICON_POSITION = ["after", "before"];

export const button = () => ({
	component: CatButtonComponent,
	template: `
        <div class="col-md-4" style="margin-top: 2rem; margin-left: 2rem">
            <pacto-cat-button 
            [type]="type"
            [icon]="icon"
            [label]="label"
            [disabled]="disabled"
            [size]="size"
            [iconPosition]="iconPosition"
            [full]="full"
            [width]="width"
            [height]="height">
            </pacto-cat-button>
        </div>
    `,
	props: {
		type: select("type", BUTTON_TYPES, BUTTON_TYPE.PRIMARY),
		icon: text("icon", ""),
		label: text("label", "Send"),
		disabled: boolean("disabled", false),
		size: select("size", BUTTON_SIZES, BUTTON_SIZE.NORMAL),
		iconPosition: select("iconPosition", ICON_POSITION, "before"),
		full: boolean("full", false),
		width: text("width", ""),
		height: text("height", ""),
	},
});

button.story = {
	parameters: {
		notes: { Notes },
	},
};
