import { moduleMetadata } from "@storybook/angular";
import { withKnobs, boolean } from "@storybook/addon-knobs";

import { CatInputColorComponent } from "ui-kit";
import {
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";

export default {
	title: "Form | Input",
	decorators: [
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule],
			declarations: [CatInputColorComponent],
		}),
		withKnobs,
	],
};

export const colorInput = () => {
	let formControl = new FormControl("", Validators.required);
	return {
		component: CatInputColorComponent,
		template: `
        <div class="col-md-4">
            <pacto-cat-input-color
                [readonly]="readonly"
                [formControl]="formControl"
                >
            </pacto-cat-input-color> 
        </div>
            `,
		props: {
			formControl,
			readonly: boolean("readonly", false),
		},
	};
};
