# Pacto Cat-Select

`tag: pacto-cat-select`

`usage: <pacto-cat-select></pacto-cat-select>`

`Example:`

```ts
import { Component } from '@angular/core';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {}
```

**meu-exemplo-de-tela.component.ts**

```html
<pacto-cat-select
	id="1"
	items="items"
	idKey="codigo"
	labelKey="nome"
	label="Select"
	control="control.get('select')"
	size="NORMAL"
>
</pacto-cat-select>
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>id <span class="bubble-sm info">optional</span></td>
            <td>
               Define um identificador único para o componente.
            </td>
            <td>```Number```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>items <span class="bubble-sm danger">required</span></td>
            <td>
                Define um array de objetos com o codigo e o nome de cada item. 
            </td>
            <td>```Array de objetos```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>idKey <span class="bubble-sm danger">required</span></td>
            <td>
                Define que o id/codigo do array de items será o valor passado no value do input.
            </td>
            <td>```Number```</td>
            <td>-</td>
        </tr>
         <tr>
            <td>labelKey <span class="bubble-sm danger">required</span></td>
            <td>
                Define a label de cada item do array de items no input.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>label <span class="bubble-sm info">optional</span></td>
            <td>
                Define a label do input.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>control <span class="bubble-sm danger">required</span></td>
            <td>
                Define o control do input no FormGroup.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>size <span class="bubble-sm info">optional</span></td>
            <td>
                Define o tamanho do input.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>   
    </tbody>
</table>

## Outputs / Events

<div class="bubble info">
    <p>Component has no custom output.</p>
</div>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style>
