import { moduleMetadata, storiesOf } from "@storybook/angular";
import { withKnobs, text, boolean, button } from "@storybook/addon-knobs";
import {
	AbstractControl,
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { TextMaskModule } from "angular2-text-mask";
import { CatFormInputNumberComponent } from "ui-kit";
import { NgxCurrencyModule } from "ngx-currency";

export default {
	title: "Form | Input",
	decorators: [
		moduleMetadata({
			imports: [
				FormsModule,
				ReactiveFormsModule,
				TextMaskModule,
				NgxCurrencyModule,
			],
			declarations: [CatFormInputNumberComponent],
		}),
		withKnobs,
	],
};

export const numberInput = () => {
	let formControl = new FormControl("", Validators.required);
	return {
		component: CatFormInputNumberComponent,
		template: `
        <div class="col-md-4">
            <pacto-cat-form-input-number
                [label]="label"
                [formControl]="control"
                [errorMsg]="errorMsg"
                [placeholder]="placeholder"
                [maxlength]="maxlength"
                [readonly]="readonly">
            </pacto-cat-form-input-number> 
        </div>
            `,
		props: {
			label: text("label", "Telefone"),
			control: formControl,
			errorMsg: text("errorMsg", "Campo obrigatório"),
			placeholder: text("text", "Digite um número"),
			maxlength: text("maxlength", "3"),
			readonly: boolean("readonly", false),
		},
	};
};
