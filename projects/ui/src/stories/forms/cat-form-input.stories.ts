import { moduleMetadata } from "@storybook/angular";
import { withKnobs, text, boolean, button } from "@storybook/addon-knobs";

import { CatFormInputComponent } from "ui-kit";
import {
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { TextMaskModule } from "angular2-text-mask";
import Notes from "./cat-form-input.md";

export default {
	title: "Form | Input",
	decorators: [
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule, TextMaskModule],
			declarations: [CatFormInputComponent],
		}),
		withKnobs,
	],
};

export const textInput = () => {
	let flag =
		localStorage.getItem("text-input-disable-require-validator") || false;
	let formControl = enableValidators()
		? new FormControl("", Validators.required)
		: new FormControl("");

	const handler = () => changeValidators();

	function enableValidators() {
		if (!flag || flag == "false") {
			return false;
		} else {
			return true;
		}
	}

	function changeValidators() {
		if (!flag || flag == "false") {
			localStorage.setItem("text-input-disable-require-validator", `true`);
			alert(
				`
                Required enabled. 
                Para utilizar o validador 'required', 
                verifique as informações na aba 'Notes'`
			);
		} else {
			localStorage.setItem("text-input-disable-require-validator", `false`);
			alert(
				`
                Required disabled. 
                Para utilizar o validador 'required', 
                verifique as informações na aba 'Notes'`
			);
		}
	}

	return {
		component: CatFormInputComponent,
		template: `
        <div class="col-md-4">
            <pacto-cat-form-input
                [label]="label"
                [control]="control"
                [errorMsg]="errorMsg"
                [min]="min"
                [max]="max"
                [readonly]="readonly"
                [enableClearInput]="enableClearInput"
                [selectOnFocus]="selectOnFocus"
                [maxlength]="maxlength"
                >
            </pacto-cat-form-input> 
        </div>
            `,
		props: {
			label: text("label", "Nome"),
			control: formControl,
			errorMsg: text("errorMsg", "O campo `nome` é obrigatório"),
			min: text("min", ""),
			max: text("max", ""),
			readonly: boolean("readonly", false),
			enableClearInput: boolean("enableClearInput", true),
			selectOnFocus: boolean("selectOnFocus", false),
			maxlength: text("maxlength", ""),
			// Apenas permite o teste de validadores no storybook
			required: button(`toggle required validator (*storybook only)`, handler),
		},
	};
};

textInput.story = {
	parameters: {
		notes: { Notes },
	},
};
