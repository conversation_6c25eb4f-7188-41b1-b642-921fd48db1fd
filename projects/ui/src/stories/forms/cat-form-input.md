# Cat Form Input

`tag: pacto-cat-form-input`

`usage: <pacto-cat-form-input></pacto-cat-form-input>`

`Example:`

```ts
import { Component } from '@angular/core';
import { BUTTON_TYPE, BUTTON_SIZE } from 'ui-kit';
import { FormControl, Validators } from '@angular/forms';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	formControl = new FormControl();
	// Example with validators
	// formControl = new FormControl('', Validators.required)
}
```

**meu-exemplo-de-tela.component.ts**

```html
<pacto-cat-form-input
	label="Nome"
	[control]="formControl"
	errorMsg="O campo 'nome' é obrigatório"
>
</pacto-cat-form-input>
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>id <span class="bubble-sm info">optional</span></td>
            <td>
               Atributo id do componente (atributo HTML)
            </td>
            <td>```String```</td>
            <td>Valor aleatório</td>
        </tr>
        <tr>
            <td>label <span class="bubble-sm danger">required</span></td>
            <td>
               Define label que fica acima do componente
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>placeholder <span class="bubble-sm info">optional</span></td>
            <td>
               Define valor exemplo que fica amostra no componente enquanto o usuário não digita nada no componente
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>errorMsg <span class="bubble-sm info">optional</span></td>
            <td>
               Define a mensagem que de erro que aparece quando são adicionados `Validators` (validadores) ao `FormControl`
               que é passado para o componente. 
               <span class="warning">Apenas aparece quando é definido algum **validator** para o **FormControl**</span>
            </td>
            <td>```String```</td>
            <td>PRIMARY</td>
        </tr>
        <tr>
            <td>step <span class="bubble-sm info">optional</span></td>
            <td>
               Define o incremento a ser utilizado para o input, o atributo funciona apenas em 
               inputs do tipo (number, date etc.), para informações acesse o link ["Step" atributo](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/step). 
            </td>
            <td>```String```</td>
            <td>PRIMARY</td>
        </tr>
        <tr>
            <td>control <span class="bubble-sm danger">required</span></td>
            <td>
                ```new FormControl('')``` - Formulários reativos angular, 
                responsável por controlar os valores digitados no componente
            </td>
            <td>```FormControl```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>selectOnFocus</td>
            <td>
               Caso verdadeiro, quando o componente receber `foco` (event) todo o texto presente no 
               componente será selecionado.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
        <tr>
            <td>enableClearInput <span class="bubble-sm info">optional</span></td>
            <td>
               Caso verdadeiro, permite que o usuário limpe o conteúdo do input clicando no `x`
            </td>
            <td>```Boolean```</td>
            <td>true</td>
        </tr>
        <tr>
            <td>textMask <span class="bubble-sm info">optional</span></td>
            <td>
               Define uma máscara para o conteúdo a ser entrado no input. 
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>maxlength <span class="bubble-sm info">optional</span></td>
            <td>
               Define o tamanho máximo do conteúdo a ser entrado no input.
            </td>
            <td>```String```</td>
            <td>PRIMARY</td>
        </tr>
        <tr>
            <td>type<span class="bubble-sm info">optional</span></td>
            <td>
               Define o tipo de dados a ser entrado no input (number, date etc.), mais informações 
               no link [Form Input](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input).
            </td>
            <td>```String```</td>
            <td>text</td>
        </tr>
        <tr>
            <td>max<span class="bubble-sm info">optional</span></td>
            <td>
               Define o valor máximo que pode ser digitado no componente de input. 
               <span class="warning">Não disponível para inputs com **type="text"** </span>
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>min<span class="bubble-sm info">optional</span></td>
            <td>
               Define o valor mínimo que pode ser digitado no componente de input. 
               <span class="warning">Não disponível para inputs com **type="text"** </span>
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>readonly <span class="bubble-sm info">optional</span></td>
            <td>
               Desabilita a possibilidade de edição do texto presente no componente.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
        
    </tbody>
</table>

## Outputs / Events

<div class="bubble info">
    <p>Component has no custom output.</p>
</div>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 20%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 20%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 20%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }


    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style>
