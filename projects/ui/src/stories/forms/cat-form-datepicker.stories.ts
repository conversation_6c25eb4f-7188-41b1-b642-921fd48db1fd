import { moduleMetadata, storiesOf } from "@storybook/angular";
import { withKnobs, text, boolean, number, date } from "@storybook/addon-knobs";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatNativeDateModule } from "@angular/material";
import {
	CatButtonComponent,
	CatDatepickerComponent,
	CatFormDatepickerComponent,
} from "ui-kit";
import {
	FormControl,
	FormsModule,
	ReactiveFormsModule,
	Validators,
} from "@angular/forms";
import { TextMaskModule } from "angular2-text-mask";

export default {
	title: "Form | Input",
	decorators: [
		moduleMetadata({
			imports: [
				FormsModule,
				ReactiveFormsModule,
				TextMaskModule,
				MatDatepickerModule,
				MatNativeDateModule,
			],
			declarations: [
				CatFormDatepickerComponent,
				CatButtonComponent,
				CatDatepickerComponent,
			],
		}),
		withKnobs,
	],
};

export const dateInput = () => {
	let formControl = new FormControl("", Validators.required);
	return {
		component: CatFormDatepickerComponent,
		template: `
        <div class="col-md-4">
            <pacto-cat-form-datepicker
                [label]="label"
                [control]="control"
                [errorMsg]="errorMsg"
                [onlyDay]="onlyDay"
                [monthYearOnly]="monthYearOnly">
            </pacto-cat-form-datepicker> 
        </div>
           
            `,
		props: {
			label: text("label", "Data"),
			control: formControl,
			errorMsg: text("errorMsg", "O campo `Data` é obrigatório"),
			onlyDay: boolean("onlyDay", false),
			monthYearOnly: boolean("monthYearOnly", false),
			dateFilter: text(
				"dateFilter",
				"Explicação da prop - Recebe função de callback que retorna um booleano"
			),
		},
	};
};
