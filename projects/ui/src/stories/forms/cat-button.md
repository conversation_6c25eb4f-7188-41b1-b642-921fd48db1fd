# Cat Button

`tag: pacto-cat-button`

`usage: <pacto-cat-button></pacto-cat-button>`

`Example:`

```ts
import { Component } from '@angular/core';
import { BUTTON_TYPE, BUTTON_SIZE } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	buttonTypes = BUTTON_TYPE;
	buttonSizes = BUTTON_SIZE;

	onClick(): void {
		// Do something
	}
}
```

**meu-exemplo-de-tela.component.ts**

```html
<pacto-cat-button
	type="buttonTypes.PRIMARY"
	icon="pct pct-trash"
	label="Delete"
	[size]="buttonSizes.LARGE"
	iconPosition="after"
	(click)="onClick()"
>
</pacto-cat-button>
```

**meu-exemplo-de-tela.component.html**

## Props / Inputs

<table class="full">
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default value</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>type <span class="bubble-sm danger">required</span></td>
            <td>
               Define os estilos (CSS) que serão aplicados no componente. A lista de tipos que podem ser aplicados, encontram-se na tabela [Types table](#typeTable)
            </td>
            <td>```String```</td>
            <td>PRIMARY</td>
        </tr>
        <tr>
            <td>icon <span class="bubble-sm info">optional</span></td>
            <td>
                Define se o botão contém um ícone e qual o ícone. 
                O ícone é definido por uma string e.g. `pct pct-trash`, o primeiro `pct` sempre é utilizado na definição do ícone (obrigatóriamente),
                o segundo valor `pct-trash`, é quem define o ícone a ser utilizado. A lista completa de ícones pode ser encontrada no [link](https://zeroheight.com/781f9d842/p/54415f-cones/b/072b0a).
                </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>label <span class="bubble-sm danger">required</span></td>
            <td>
                Define o texto que fica dentro do botão
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>disabled <span class="bubble-sm info">optional</span></td>
            <td>
                Bloqueia o clique no botão.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
        <tr>
            <td>size <span class="bubble-sm danger">required</span></td>
            <td>
                Texto exemplo que aparece enquanto o usuário não digitar nada no campo. Opções: ```'NORMAL' | 'SMALL' | 'LARGE'```
            </td>
            <td>```String```</td>
            <td>NORMAL</td>
        </tr>
        <tr>
            <td>iconPosition <span class="bubble-sm info">optional</span></td>
            <td>
                Modifica a posição do ícone para antes ou depois do texto. Opções: ```'after' | 'before'```
            </td>
            <td>```String```</td>
            <td>before</td>
        </tr>
        <tr>
            <td>full <span class="bubble-sm info">optional</span></td>
            <td>
                Define se o componente irá ocupar todo o espaço disponível no container ou se a largura será definida pelo tamanho do texto dentro do botão
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
        <tr>
            <td>width <span class="bubble-sm info">optional</span></td>
            <td>
                Define largura do botão. Para preencher o input, deve ser adicionado a 'unidade de medida' ao final do valor e.g. `[width]='200px' | [width]='10rem'` etc.
                <span class="warning">Caso não seja preenchido, a largura do botão será mantida como a largura do texto + padding </span>
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>height <span class="bubble-sm info">optional</span></td>
            <td>
                Define altura do botão. Para preencher o input, deve ser adicionado a 'unidade de medida' ao final do valor e.g. `[height]='50px' | [height]='2rem'` etc.
                <span class="warning">Caso não seja preenchido, a altura do botão será mantida como a altura do texto + padding </span>
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
    </tbody>
</table>

## Outputs / Events

<div class="bubble info">
    <p>Component has no custom output.</p>
</div>

## More info

<h3 id="typeTable">Types table</h3>

<table class="custom">
    <thead>
        <tr>
            <th>Type</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>`PRIMARY`</td>
        </tr>
        <tr>
            <td>`OUTLINE`</td>
        </tr>
        <tr>
            <td>`OUTLINE_ALERT`</td>
        </tr>
        <tr>
            <td>`OUTLINE_DARK`</td>
        </tr>
        <tr>
            <td>`OUTLINE_SUCCESS`</td>
        </tr>
        <tr>
            <td>`OUTLINE_ACTION`</td>
        </tr>
        <tr>
            <td>`ACTION`</td>
        </tr>
        <tr>
            <td>`ALERT`</td>
        </tr>
        <tr>
            <td>`DARK`</td>
        </tr>
        <tr>
            <td>`SUCCESS`</td>
        </tr>
        <tr>
            <td>`NO_BORDER`</td>
        </tr>
        <tr>
            <td>`BLACK_BORDER`</td>
        </tr>
        <tr>
            <td>`PRIMARY_NO_TEXT_TRANSFORM`</td>
        </tr>
        <tr>
            <td>`SECUNDARY`</td>
        </tr>
        <tr>
            <td>`ALERT_PARCELAS`</td>
        </tr>
        <tr>
            <td>`ALERT_DELETE`</td>
        </tr>
        <tr>
            <td>`OUTLINE_DANGER`</td>
        </tr>
        <tr>
            <td>`OUTLINE_GRAY`</td>
        </tr>
        <tr>
            <td>`PRIMARY_ADD`</td>
        </tr>
        <tr>
            <td>`OUTLINE_FILTER`</td>
        </tr>
    </tbody>
</table>

<style>
    .full {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
        margin-top: 0.8rem;
    }

    .bubble-sm {
        display: block;
        padding: 0.1rem 0.4rem;
        border-radius: 2rem;
        width: max-content;
        margin-left: -5px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 25%);
    }

    .info-text {
        color: hsla(215, 100%, 54%);
    }

    .danger {
        background-color: hsla(360, 87%, 63%, 25%);
    }

    .danger-text {
        color: hsl(360, 87%, 63%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 25%);
    }

    .warning-text {
        color: hsl(43, 87%, 63%);
    }

    td  span[class="warning"]:not(.bubble):not(.bubble-sm),
    td  span[class="danger"]:not(.bubble):not(.bubble-sm),
    td  span[class="info"]:not(.bubble):not(.bubble-sm) {
        padding-left: 0.2rem;
        padding-right: 0.2rem;
    }

    td, p, code {
        font-size: 1rem;
    }

    h2[id],
    h2[id]:first-of-type {
        margin-bottom: 0.8rem;
        margin-top: 1rem;
    }

    table.full tr > td:last-of-type {
        text-align: center;
    }

    table tr > th:last-of-type {
        white-space: nowrap;
    }

    .custom {
        width: 30%;
    }
</style>
