# Cat Form Textarea

```tag: pacto-cat-form-textarea```

## Props / Inputs

<table>
    <thead>
        <tr>
            <th>Prop</th>
            <th>Description</th>
            <th>Type</th>
            <th>Default</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>control</td>
            <td>
                ```new FormControl('')``` - Formulários reativos angular, responsável por controlar os valores digitados no componente
            </td>
            <td>```new FormControl('')```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>label</td>
            <td>Label que aparece acima do componente</td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>errorMsg</td>
            <td>
                Mensagem de erro que será mostrada caso tenham sido adicionados "Validators" ao form control, e.g. ```new FormControl('', Validators.required)```
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>rows</td>
            <td>
                Número de linhas que serão preenchidas pelo componente
            </td>
            <td>```Number```</td>
            <td>3</td>
        </tr>
        <tr>
            <td>placeholder</td>
            <td>
                Texto exemplo que aparece enquanto o usuário não digitar nada no campo.
            </td>
            <td>```String```</td>
            <td>-</td>
        </tr>
        <tr>
            <td>disabled</td>
            <td>
                Desabilita a possibilidade de edição do texto presente no componente.
            </td>
            <td>```Boolean```</td>
            <td>false</td>
        </tr>
    </tbody>
</table>



## Outputs / Events

<div class="bubble warning">
    <p>Component has no output.</p>
</div>



<style>
    table {
        width: 100%;
        border-radius: 4px;
    }

    tr th {
        background-color: hsl(0, 0%, 95%);
    }

    tr td:last-child {
        text-align: center;
    }

    .bubble {
        padding: 1rem;
        width: 100%;
        border-radius: 4px;
    }

    .info {
        background-color: hsla(215, 100%, 74%, 20%);
    }

    .error {
        background-color: hsla(360, 87%, 63%, 20%);
    }

    .warning {
        background-color: hsla(43, 87%, 63%, 20%);
    }

    td, p {
        font-size: 1rem;
    }
</style>