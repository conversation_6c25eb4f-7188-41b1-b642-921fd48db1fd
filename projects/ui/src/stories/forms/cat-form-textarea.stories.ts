import { moduleMetadata } from "@storybook/angular";
import { withKnobs, text, boolean, number } from "@storybook/addon-knobs";

import { CatFormTextareaComponent } from "ui-kit";
import { FormControl, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TextMaskModule } from "angular2-text-mask";

import Notes from "./cat-form-textarea.md";

export default {
	title: "Form | Textarea",
	decorators: [
		withKnobs,
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule, TextMaskModule],
			declarations: [CatFormTextareaComponent],
		}),
	],
};

export const textarea = () => {
	let formControl = new FormControl();
	return {
		component: CatFormTextareaComponent,
		template: `
            <div class="col-md-4">
                <pacto-cat-form-textarea
                    [control]="control"
                    [label]="label"
                    [rows]="rows"
                    [placeholder]="placeholder"
                    [disabled]="disabled"
                ></pacto-cat-form-textarea>
            </div>
            `,
		props: {
			label: text("label", "Input label"),
			control: formControl,
			errorMsg: text("errorMsg", "O campo `Input label` é obrigatório"),
			disabled: boolean("disabled", false),
			rows: number("rows", 3),
			placeholder: text("placeholder", "Meu placeholder"),
		},
	};
};

textarea.story = {
	parameters: {
		notes: { Notes },
	},
};
