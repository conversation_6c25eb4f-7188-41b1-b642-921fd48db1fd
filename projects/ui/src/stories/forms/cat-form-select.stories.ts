import { moduleMetadata } from "@storybook/angular";
import { withKnobs, text, boolean } from "@storybook/addon-knobs";

import { CatFormSelectComponent } from "ui-kit";
import { FormControl, FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TextMaskModule } from "angular2-text-mask";

export default {
	title: "Form | Select",
	decorators: [
		withKnobs,
		moduleMetadata({
			imports: [FormsModule, ReactiveFormsModule, TextMaskModule],
			declarations: [CatFormSelectComponent],
		}),
	],
};

export const simpleSelect = () => {
	let formControl = new FormControl();
	const items = Array.from(Array(10).keys()).map((i) => ({
		id: i,
		label: `item ${i}`,
	}));
	return {
		component: CatFormSelectComponent,
		template: `
            <div class="col-md-4">
                <pacto-cat-form-select
                    [control]="control"
                    [items]="items"
                    [disabled]="disabled"
                    [label]="label"
                ></pacto-cat-form-select>
            </div>
            `,
		props: {
			label: text("label", "Input label"),
			control: formControl,
			errorMsg: text("errorMsg", "O campo `Input label` é obrigatório"),
			items,
			disabled: false,
		},
	};
};
