@import "values";

// Fonts
:root {
	--#{$prefix}-font-primary: #{$font-primary};
	--#{$prefix}-font-secondary: #{$font-secondary};
}

// Title
:root {
	@each $scale, $value in $title-scales {
		--#{$prefix}-title-#{$scale}-font-family: var(--#{$prefix}-font-primary);
		--#{$prefix}-title-#{$scale}-font-size: #{$value};
		--#{$prefix}-title-#{$scale}-line-height: #{$title-line-height};
		--#{$prefix}-title-#{$scale}-letter-spacing: #{$title-letter-spacing};
		--#{$prefix}-title-#{$scale}-font-weight: 600;
	}
}

// Body
:root {
	@each $scale, $value in $body-scales {
		--#{$prefix}-body-#{$scale}-font-family: var(--#{$prefix}-font-secondary);
		--#{$prefix}-body-#{$scale}-font-size: #{$value};
		--#{$prefix}-body-#{$scale}-line-height: #{$body-line-height};
		--#{$prefix}-body-#{$scale}-letter-spacing: #{$body-letter-spacing};
		--#{$prefix}-body-#{$scale}-font-weight: 400;
	}
}

// Overline
:root {
	@each $scale, $value in $overline-scales {
		--#{$prefix}-overline-#{$scale}-font-family: var(
			--#{$prefix}-font-secondary
		);
		--#{$prefix}-overline-#{$scale}-font-size: #{$value};
		--#{$prefix}-overline-#{$scale}-line-height: #{$overline-line-height};
		--#{$prefix}-overline-#{$scale}-letter-spacing: #{$overline-letter-spacing};
		--#{$prefix}-overline-#{$scale}-font-weight: 400;
	}
}

// Overline bold
:root {
	@each $scale, $value in $overline-bold-scales {
		--#{$prefix}-overline-bold-#{$scale}-font-family: var(
			--#{$prefix}-font-secondary
		);
		--#{$prefix}-overline-bold-#{$scale}-font-size: #{$value};
		--#{$prefix}-overline-bold-#{$scale}-line-height: #{$overline-line-height};
		--#{$prefix}-overline-bold-#{$scale}-letter-spacing: #{$overline-letter-spacing};
		--#{$prefix}-overline-bold-#{$scale}-font-weight: 700;
	}
}

// Display
:root {
	@each $scale, $value in $display-scales {
		--#{$prefix}-display-#{$scale}-font-family: var(--#{$prefix}-font-primary);
		--#{$prefix}-display-#{$scale}-font-size: #{$value};
		--#{$prefix}-display-#{$scale}-line-height: #{$display-line-height};
		--#{$prefix}-display-#{$scale}-letter-spacing: #{$display-letter-spacing};
		--#{$prefix}-display-#{$scale}-font-weight: 500;
	}
}

// Button / Default
:root {
	@each $scale, $value in $button-default-scales {
		--#{$prefix}-button-default-#{$scale}-font-family: var(
			--#{$prefix}-font-primary
		);
		--#{$prefix}-button-default-#{$scale}-font-size: #{$value};
		--#{$prefix}-button-default-#{$scale}-line-height: #{$button-default-line-height};
		--#{$prefix}-button-default-#{$scale}-letter-spacing: #{$button-default-letter-spacing};
		--#{$prefix}-button-default-#{$scale}-font-weight: 600;
	}
}

// Button / Menu
:root {
	@each $scale, $value in $button-menu-scales {
		--#{$prefix}-button-menu-#{$scale}-font-family: var(
			--#{$prefix}-font-primary
		);
		--#{$prefix}-button-menu-#{$scale}-font-size: #{$value};
		--#{$prefix}-button-menu-#{$scale}-line-height: #{$button-menu-line-height};
		--#{$prefix}-button-menu-#{$scale}-letter-spacing: #{$button-menu-letter-spacing};
		--#{$prefix}-button-menu-#{$scale}-font-weight: 600;
	}
}
