import { withKnobs } from "@storybook/addon-knobs";
import { moduleMetadata, StoryObj } from "@storybook/angular";
import "dist/ui-kit/assets/ds3/typography/index.scss";
import "./index.scss";
import Notes from "./ds3-typography.md";
import { CommonModule } from "@angular/common";
import { UiModule } from "ui-kit";

export default {
	title: "Design System 3 | Styles/Typography",
	decorators: [
		moduleMetadata({
			imports: [CommonModule, UiModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Title: StoryObj = () => ({
	template: `
	<div style="padding: 1rem">
	<h1 class="typography-title-1">Lorem ipsum</h1>
	<h2 class="typography-title-2">Lorem ipsum</h2>
	<h3 class="typography-title-3">Lorem ipsum</h3>
	<h4 class="typography-title-4">Lorem ipsum</h4>
	<h5 class="typography-title-5">Lorem ipsum</h5>
</div>`,
});

export const Body: StoryObj = () => ({
	template: `
				<div style="padding: 1rem">
					<p class="typography-body-1">Lorem ipsum</p>
					<p class="typography-body-2">Lorem ipsum</p>
				</div>
			`,
});
export const Overline: StoryObj = () => ({
	template: `
				<div style="padding: 1rem">
					<p class="typography-overline-1">Lorem ipsum</p>
					<p class="typography-overline-2">Lorem ipsum</p>
				</div>
			`,
});
export const Display: StoryObj = () => ({
	template: `
				<div style="padding: 1rem">
					<p class="typography-display-1">Lorem ipsum</p>
					<p class="typography-display-2">Lorem ipsum</p>
					<p class="typography-display-3">Lorem ipsum</p>
					<p class="typography-display-4">Lorem ipsum</p>
					<p class="typography-display-5">Lorem ipsum</p>
					<p class="typography-display-6">Lorem ipsum</p>
					<p class="typography-display-7">Lorem ipsum</p>
				</div>
			`,
});
export const Button_defaukt: StoryObj = () => ({
	template: `
				<div style="padding: 1rem">
					<p class="typography-button-default-1">Lorem ipsum</p>
					<p class="typography-button-default-2">Lorem ipsum</p>
				</div>
			`,
});
export const Button_Menu: StoryObj = () => ({
	template: `
				<div style="padding: 1rem">
					<p class="typography-button-menu-1">Lorem ipsum</p>
					<p class="typography-button-menu-2">Lorem ipsum</p>
				</div>
			`,
});
