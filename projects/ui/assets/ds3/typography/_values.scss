@import "../functions";

$prefix: "typography";

// Fontes
$font-primary: <PERSON><PERSON><PERSON>;
$font-secondary: Nunito Sans;

// Title
$title-scales: (
	1: px-to-rem(30px),
	2: px-to-rem(22px),
	3: px-to-rem(18px),
	4: px-to-rem(14px),
	5: px-to-rem(12px),
);
$title-line-height: 125%;
$title-letter-spacing: px-to-rem(0.25px);

// Body
$body-scales: (
	1: px-to-rem(16px),
	2: px-to-rem(14px),
);
$body-line-height: 125%;
$body-letter-spacing: px-to-rem(0px);

// Overline
$overline-scales: (
	1: px-to-rem(14px),
	2: px-to-rem(12px),
);
$overline-line-height: 125%;
$overline-letter-spacing: px-to-rem(0px);

// Overline bold
$overline-bold-scales: (
	1: px-to-rem(14px),
	2: px-to-rem(12px),
);
$overline-bold-line-height: 125%;
$overline-bold-letter-spacing: px-to-rem(0px);

// Display
$display-scales: (
	1: px-to-rem(46px),
	2: px-to-rem(40px),
	3: px-to-rem(30px),
	4: px-to-rem(22px),
	5: px-to-rem(18px),
	6: px-to-rem(14px),
	7: px-to-rem(12px),
);
$display-line-height: 100%;
$display-letter-spacing: px-to-rem(0.25px);

// Button
$button-default-scales: (
	1: px-to-rem(30px),
	2: px-to-rem(22px),
);
$button-default-line-height: 125%;
$button-default-letter-spacing: px-to-rem(0.25px);

$button-menu-scales: (
	1: px-to-rem(30px),
	2: px-to-rem(22px),
);
$button-menu-line-height: 125%;
$button-menu-letter-spacing: px-to-rem(0.25px);
