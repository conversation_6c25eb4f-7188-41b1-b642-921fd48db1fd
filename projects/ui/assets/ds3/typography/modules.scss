@import "values";
@import "mixins";

// Fontes
.#{$prefix}-font-primary {
	font-family: var(--#{$prefix}-font-primary);
}

.#{$prefix}-font-secondary {
	font-family: var(--#{$prefix}-font-secondary);
}

// Tamanhos de título
@each $scale, $value in $title-scales {
	.#{$prefix}-title-#{$scale} {
		@include apply-typography-style("title", $scale);
	}
}

// Tamanhos de corpo
@each $scale, $value in $body-scales {
	.#{$prefix}-body-#{$scale} {
		@include apply-typography-style("body", $scale);
	}
}

// Tamanhos de overline
@each $scale, $value in $overline-scales {
	.#{$prefix}-overline-#{$scale} {
		@include apply-typography-style("overline", $scale);
	}
}

// Tamanhos de overline
@each $scale, $value in $overline-scales {
	.#{$prefix}-overline-bold-#{$scale} {
		@include apply-typography-style("overline-bold", $scale);
	}
}

// Tamanhos de display
@each $scale, $value in $display-scales {
	.#{$prefix}-display-#{$scale} {
		@include apply-typography-style("display", $scale);
	}
}

// Tamanhos de botão padrão
@each $scale, $value in $button-default-scales {
	.#{$prefix}-button-default-#{$scale} {
		@include apply-typography-style("button-default", $scale);
	}
}

// Tamanhos de botão de menu
@each $scale, $value in $button-menu-scales {
	.#{$prefix}-button-menu-#{$scale} {
		@include apply-typography-style("button-menu", $scale);
	}
}
