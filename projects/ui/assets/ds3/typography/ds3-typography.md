## Tokens de Tipografia

### Fontes:

| Token                       | Descrição                    | Valor <PERSON> |
| --------------------------- | ---------------------------- | ------------ |
| --typography-font-primary   | Família de fonte primária.   | Poppins      |
| --typography-font-secondary | Família de fonte secundária. | Nunito Sans  |

### Estilos de Tipografia

#### Categoria: `title`

| Token                               | Descrição                               | Valor Padrão              |
| ----------------------------------- | --------------------------------------- | ------------------------- |
| --typography-title-1-font-family    | Família de fonte para título 1.         | --typography-font-primary |
| --typography-title-1-font-size      | Tamanho da fonte para título 1.         | 1.875rem (30px)           |
| --typography-title-1-line-height    | Altura da linha para título 1.          | 125%                      |
| --typography-title-1-letter-spacing | Espaçamento entre letras para título 1. | 0.015625rem (0.25px)      |
| --typography-title-1-font-weight    | Peso da fonte para título 1.            | 600                       |
| --typography-title-2-font-family    | Família de fonte para título 2.         | --typography-font-primary |
| --typography-title-2-font-size      | Tamanho da fonte para título 2.         | 1.375rem (22px)           |
| --typography-title-2-line-height    | Altura da linha para título 2.          | 125%                      |
| --typography-title-2-letter-spacing | Espaçamento entre letras para título 2. | 0.015625rem (0.25px)      |
| --typography-title-2-font-weight    | Peso da fonte para título 2.            | 600                       |
| --typography-title-3-font-family    | Família de fonte para título 3.         | --typography-font-primary |
| --typography-title-3-font-size      | Tamanho da fonte para título 3.         | 1.125rem (18px)           |
| --typography-title-3-line-height    | Altura da linha para título 3.          | 125%                      |
| --typography-title-3-letter-spacing | Espaçamento entre letras para título 3. | 0.015625rem (0.25px)      |
| --typography-title-3-font-weight    | Peso da fonte para título 3.            | 600                       |
| --typography-title-4-font-family    | Família de fonte para título 4.         | --typography-font-primary |
| --typography-title-4-font-size      | Tamanho da fonte para título 4.         | 0.875rem (14px)           |
| --typography-title-4-line-height    | Altura da linha para título 4.          | 125%                      |
| --typography-title-4-letter-spacing | Espaçamento entre letras para título 4. | 0.015625rem (0.25px)      |
| --typography-title-4-font-weight    | Peso da fonte para título 4.            | 600                       |
| --typography-title-5-font-family    | Família de fonte para título 5.         | --typography-font-primary |
| --typography-title-5-font-size      | Tamanho da fonte para título 5.         | 0.75rem (12px)            |
| --typography-title-5-line-height    | Altura da linha para título 5.          | 125%                      |
| --typography-title-5-letter-spacing | Espaçamento entre letras para título 5. | 0.015625rem (0.25px)      |
| --typography-title-5-font-weight    | Peso da fonte para título 5.            | 600                       |

#### Categoria: `body`

| Token                              | Descrição                              | Valor Padrão                |
| ---------------------------------- | -------------------------------------- | --------------------------- |
| --typography-body-1-font-family    | Família de fonte para corpo 1.         | --typography-font-secondary |
| --typography-body-1-font-size      | Tamanho da fonte para corpo 1.         | 1rem (16px)                 |
| --typography-body-1-line-height    | Altura da linha para corpo 1.          | 125%                        |
| --typography-body-1-letter-spacing | Espaçamento entre letras para corpo 1. | 0rem (0px)                  |
| --typography-body-1-font-weight    | Peso da fonte para corpo 1.            | 400                         |
| --typography-body-2-font-family    | Família de fonte para corpo 2.         | --typography-font-secondary |
| --typography-body-2-font-size      | Tamanho da fonte para corpo 2.         | 0.875rem (14px)             |
| --typography-body-2-line-height    | Altura da linha para corpo 2.          | 125%                        |
| --typography-body-2-letter-spacing | Espaçamento entre letras para corpo 2. | 0rem (0px)                  |
| --typography-body-2-font-weight    | Peso da fonte para corpo 2.            | 400                         |

#### Categoria: `overline`

| Token                                  | Descrição                                     | Valor Padrão                |
| -------------------------------------- | --------------------------------------------- | --------------------------- |
| --typography-overline-1-font-family    | Família de fonte para sobreposição 1.         | --typography-font-secondary |
| --typography-overline-1-font-size      | Tamanho da fonte para sobreposição 1.         | 0.875rem (14px)             |
| --typography-overline-1-line-height    | Altura da linha para sobreposição 1.          | 125%                        |
| --typography-overline-1-letter-spacing | Espaçamento entre letras para sobreposição 1. | 0rem (0px)                  |
| --typography-overline-1-font-weight    | Peso da fonte para sobreposição 1.            | 400                         |
| --typography-overline-2-font-family    | Família de fonte para sobreposição 2.         | --typography-font-secondary |
| --typography-overline-2-font-size      | Tamanho da fonte para sobreposição 2.         | 0.75rem (12px)              |
| --typography-overline-2-line-height    | Altura da linha para sobreposição 2.          | 125%                        |
| --typography-overline-2-letter-spacing | Espaçamento entre letras para sobreposição 2. | 0rem (0px)                  |
| --typography-overline-2-font-weight    | Peso da fonte para sobreposição 2.            | 400                         |

#### Categoria: `display`

| Token                                 | Descrição                                        | Valor Padrão              |
| ------------------------------------- | ------------------------------------------------ | ------------------------- |
| --typography-display-1-font-family    | Família de fonte para texto destacado 1.         | --typography-font-primary |
| --typography-display-1-font-size      | Tamanho da fonte para texto destacado 1.         | 2.875rem (46px)           |
| --typography-display-1-line-height    | Altura da linha para texto destacado 1.          | 100%                      |
| --typography-display-1-letter-spacing | Espaçamento entre letras para texto destacado 1. | 0.015625rem (0.25px)      |
| --typography-display-1-font-weight    | Peso da fonte para texto destacado 1.            | 500                       |
| --typography-display-2-font-family    | Família de fonte para texto destacado 2.         | --typography-font-primary |
| --typography-display-2-font-size      | Tamanho da fonte para texto destacado 2.         | 2.5rem (40px)             |
| --typography-display-2-line-height    | Altura da linha para texto destacado 2.          | 100%                      |
| --typography-display-2-letter-spacing | Espaçamento entre letras para texto destacado 2. | 0.015625rem (0.25px)      |
| --typography-display-2-font-weight    | Peso da fonte para texto destacado 2.            | 500                       |
| --typography-display-3-font-family    | Família de fonte para texto destacado 3.         | --typography-font-primary |
| --typography-display-3-font-size      | Tamanho da fonte para texto destacado 3.         | 1.875rem (30px)           |
| --typography-display-3-line-height    | Altura da linha para texto destacado 3.          | 100%                      |
| --typography-display-3-letter-spacing | Espaçamento entre letras para texto destacado 3. | 0.015625rem (0.25px)      |
| --typography-display-3-font-weight    | Peso da fonte para texto destacado 3.            | 500                       |
| --typography-display-4-font-family    | Família de fonte para texto destacado 4.         | --typography-font-primary |
| --typography-display-4-font-size      | Tamanho da fonte para texto destacado 4.         | 1.375rem (22px)           |
| --typography-display-4-line-height    | Altura da linha para texto destacado 4.          | 100%                      |
| --typography-display-4-letter-spacing | Espaçamento entre letras para texto destacado 4. | 0.015625rem (0.25px)      |
| --typography-display-4-font-weight    | Peso da fonte para texto destacado 4.            | 500                       |
| --typography-display-5-font-family    | Família de fonte para texto destacado 5.         | --typography-font-primary |
| --typography-display-5-font-size      | Tamanho da fonte para texto destacado 5.         | 1.125rem (18px)           |
| --typography-display-5-line-height    | Altura da linha para texto destacado 5.          | 100%                      |
| --typography-display-5-letter-spacing | Espaçamento entre letras para texto destacado 5. | 0.015625rem (0.25px)      |
| --typography-display-5-font-weight    | Peso da fonte para texto destacado 5.            | 500                       |
| --typography-display-6-font-family    | Família de fonte para texto destacado 6.         | --typography-font-primary |
| --typography-display-6-font-size      | Tamanho da fonte para texto destacado 6.         | 0.875rem (14px)           |
| --typography-display-6-line-height    | Altura da linha para texto destacado 6.          | 100%                      |
| --typography-display-6-letter-spacing | Espaçamento entre letras para texto destacado 6. | 0.015625rem (0.25px)      |
| --typography-display-6-font-weight    | Peso da fonte para texto destacado 6.            | 500                       |
| --typography-display-7-font-family    | Família de fonte para texto destacado 7.         | --typography-font-primary |
| --typography-display-7-font-size      | Tamanho da fonte para texto destacado 7.         | 0.75rem (12px)            |
| --typography-display-7-line-height    | Altura da linha para texto destacado 7.          | 100%                      |
| --typography-display-7-letter-spacing | Espaçamento entre letras para texto destacado 7. | 0.015625rem (0.25px)      |
| --typography-display-7-font-weight    | Peso da fonte para texto destacado 7.            | 500                       |

#### Categoria: `button-default`

| Token                                        | Descrição                                     | Valor Padrão              |
| -------------------------------------------- | --------------------------------------------- | ------------------------- |
| --typography-button-default-1-font-family    | Família de fonte para botão padrão 1.         | --typography-font-primary |
| --typography-button-default-1-font-size      | Tamanho da fonte para botão padrão 1.         | 1.875rem (30px)           |
| --typography-button-default-1-line-height    | Altura da linha para botão padrão 1.          | 125%                      |
| --typography-button-default-1-letter-spacing | Espaçamento entre letras para botão padrão 1. | 0.015625rem (0.25px)      |
| --typography-button-default-1-font-weight    | Peso da fonte para botão padrão 1.            | 600                       |
| --typography-button-default-2-font-family    | Família de fonte para botão padrão 2.         | --typography-font-primary |
| --typography-button-default-2-font-size      | Tamanho da fonte para botão padrão 2.         | 1.375rem (22px)           |
| --typography-button-default-2-line-height    | Altura da linha para botão padrão 2.          | 125%                      |
| --typography-button-default-2-letter-spacing | Espaçamento entre letras para botão padrão 2. | 0.015625rem (0.25px)      |
| --typography-button-default-2-font-weight    | Peso da fonte para botão padrão 2.            | 600                       |

#### Categoria: `button-menu`

| Token                                     | Descrição                                      | Valor Padrão              |
| ----------------------------------------- | ---------------------------------------------- | ------------------------- |
| --typography-button-menu-1-font-family    | Família de fonte para botão de menu 1.         | --typography-font-primary |
| --typography-button-menu-1-font-size      | Tamanho da fonte para botão de menu 1.         | 1.875rem (30px)           |
| --typography-button-menu-1-line-height    | Altura da linha para botão de menu 1.          | 125%                      |
| --typography-button-menu-1-letter-spacing | Espaçamento entre letras para botão de menu 1. | 0.015625rem (0.25px)      |
| --typography-button-menu-1-font-weight    | Peso da fonte para botão de menu 1.            | 600                       |
| --typography-button-menu-2-font-family    | Família de fonte para botão de menu 2.         | --typography-font-primary |
| --typography-button-menu-2-font-size      | Tamanho da fonte para botão de menu 2.         | 1.375rem (22px)           |
| --typography-button-menu-2-line-height    | Altura da linha para botão de menu 2.          | 125%                      |
| --typography-button-menu-2-letter-spacing | Espaçamento entre letras para botão de menu 2. | 0.015625rem (0.25px)      |
| --typography-button-menu-2-font-weight    | Peso da fonte para botão de menu 2.            | 600                       |

## Classes que implementam os estilos

Temos classes prontas que aplicam os estilos:

## Classes para Família de Fontes

- `.typography-font-primary`: Define a família de fonte primária.
- `.typography-font-secondary`: Define a família de fonte secundária.

## Classes para Títulos

Essas classes são usadas para estilizar títulos em diferentes escalas.

- `.typography-title-1`: Título de nível 1.
- `.typography-title-2`: Título de nível 2.
- `.typography-title-3`: Título de nível 3.
- `.typography-title-4`: Título de nível 4.
- `.typography-title-5`: Título de nível 5.

## Classes para Corpo de Texto

- `.typography-body-1`: Estilo de corpo de texto 1.
- `.typography-body-2`: Estilo de corpo de texto 2.

## Classes para Sobreposição

Essas classes são usadas para estilizar texto de sobreposição.

- `.typography-overline-1`: Sobreposição de nível 1.
- `.typography-overline-2`: Sobreposição de nível 2.

## Classes para Texto Destacado

Essas classes são usadas para estilizar texto destacado em diferentes escalas.

- `.typography-display-1`: Texto destacado de nível 1.
- `.typography-display-2`: Texto destacado de nível 2.
- `.typography-display-3`: Texto destacado de nível 3.
- `.typography-display-4`: Texto destacado de nível 4.
- `.typography-display-5`: Texto destacado de nível 5.
- `.typography-display-6`: Texto destacado de nível 6.
- `.typography-display-7`: Texto destacado de nível 7.

## Classes para Botões Padrão

Essas classes são usadas para estilizar botões padrão.

- `.typography-button-default-1`: Botão padrão de nível 1.
- `.typography-button-default-2`: Botão padrão de nível 2.

## Classes para Botões de Menu

Essas classes são usadas para estilizar botões de menu.

- `.typography-button-menu-1`: Botão de menu de nível 1.
- `.typography-button-menu-2`: Botão de menu de nível 2.

## Exemplo de Uso

Aqui está um exemplo de como usar essas classes em HTML:

```html
<h1 class="typography-title-1">Título de Exemplo</h1>
<p class="typography-body-1">Este é um exemplo de texto de corpo.</p>
<button class="typography-button-default-1">Clique Aqui</button>
```

Aqui está um exemplo de uso dos tokens de tipografia em classes personalizadas:

```css
/* Classe personalizada com tokens de título 1 */
.teste {
	font-family: var(--typography-title-1-font-family);
	font-size: var(--typography-title-1-font-size);
	line-height: var(--typography-title-1-line-height);
	letter-spacing: var(--typography-title-1-letter-spacing);
	font-weight: var(--typography-title-1-font-weight);
}
```

Devido uma limitação no figma ainda não é possível que o estilo gerado pelo figma venha com os tokens de tipografia, mas para facilitar a aplicação do estilo basta usar o seguinte mixin:

```scss
/* Importar o mixin de tipografia */
@import 'projects/ui/assets/ds3/typography/mixins';

/* Aplicar o estilo de tipografia para título 1 */
.teste {
	// informe a categoria e a escala desejada nos parâmetros
	@include apply-typography-style('title', 1);
}
```
