@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

.ds3-charts {
	.apexcharts-yaxis-label {
		tspan {
			font-family: $fontNunito !important;
			@extend .pct-overline2;
		}
	}

	::ng-deep {
		.apexcharts-area-series {
			margin: 0 -60px;
		}

		.apexcharts-xaxis {
			tspan {
				@extend .pct-overline2;
			}
		}

		.apexcharts-yaxis {
			tspan {
				@extend .pct-overline2;
			}
		}

		.apexcharts-yaxis-label {
			tspan {
				font-family: $fontNunito !important;
				@extend .pct-overline2;
			}
		}

		.apexcharts-yaxis-title-text {
			@extend .pct-title4;
			font-family: $fontNunito !important;
			font-size: 14px !important;
			font-weight: 600 !important;
		}

		.apexcharts-tooltip {
			background-color: $plane04;
			color: $typeBgDarkText;
			box-shadow: none;
			border: 0;
		}

		.apexcharts-tooltip-title {
			padding: 8px 12px;
			margin-bottom: 0;
			background-color: transparent !important;
			border-bottom: none !important;
			@extend .pct-overline1;
			font-family: $fontNunito !important;
			font-size: 14px !important;
		}

		.apexcharts-tooltip-series-group {
			padding: 0 20px 5px;
		}

		.apexcharts-tooltip-marker {
			border: 1px solid $branco;
			width: 16px;
			height: 16px;
		}

		.apexcharts-tooltip-text {
			@extend .pct-overline2;
			font-family: $fontNunito !important;
		}

		.apexcharts-xaxistooltip {
			border-radius: 4px;
			border: none;
			background-color: $plane04;
			color: $typeBgDarkText;
		}

		.apexcharts-xaxistooltip-bottom {
			&:after {
				border-bottom-color: $plane04;
			}

			&:before {
				border-bottom-color: transparent;
			}
		}

		.apexcharts-xaxistooltip-text {
			@extend .pct-overline2;
			font-family: $fontNunito !important;
		}

		.apexcharts-legend {
			display: flex;
			justify-content: center;
			gap: 12px 32px;
			flex-wrap: wrap;
		}

		.apexcharts-legend-series {
			display: flex;
			align-items: center;
			gap: 8px;
			margin: 0 !important;
			padding: 4px 8px;
		}

		.apexcharts-legend-marker {
			width: 16px !important;
			height: 16px !important;
			border-radius: 16px !important;
		}

		.apexcharts-legend-text {
			@extend .pct-overline2;
			font-family: $fontNunito !important;
			color: $typeDefaultText !important;
		}

		.apexcharts-datalabels text {
			font-family: $fontNunito !important;
			@extend .pct-overline2;
		}

		.apexcharts-datalabel {
			font-family: $fontNunito !important;
			@extend .pct-overline2;
		}

		.apexcharts-datalabel-label {
			font-family: $fontNunito !important;
			@extend .pct-overline2;
			fill: $typeDefaultText !important;
		}

		.apexcharts-datalabel-value {
			font-family: $fontNunito !important;
			@extend .pct-display3;
			color: $typeDefaultText;
		}
	}
}
