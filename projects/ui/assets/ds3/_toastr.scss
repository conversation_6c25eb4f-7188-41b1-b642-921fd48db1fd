@import "./colors.var";
@import "./../scss/pacto-icon-font.scss";

.toast-container {
	.pct-toastr {
		padding: 8px 5px 8px 16px;
		border-radius: 4px;
		display: grid;
		grid-template-columns: 1fr 28px;
		gap: 8px;

		.pct-toastr-content-container {
			display: flex;
			gap: 8px;
			padding-top: 8px;
			padding-bottom: 8px;

			.pct-toastr-icon {
				display: flex;
				height: 16px;
				width: 16px;
				font-size: 16px;
				justify-content: center;
				align-items: center;
				font-weight: 600;
				margin-top: 4px;
			}

			.pct-toastr-content {
				font-weight: 400;

				.pct-toastr-title-section {
					display: flex;
					align-items: center;
					font-weight: 600;

					.toast-title {
						flex: 1;
					}
				}
			}
		}

		.pct-toastr-close-section {
			height: 28px;
			width: 28px;
			font-size: 14px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 600;
			border-radius: 4px;

			&:hover {
				background: $actionBgDarkAble01;
			}

			&:focus {
				background: $actionBgDarkAble02;
			}
		}

		&.pct-toastr-success {
			background: $feedbackGain03;

			.pct-toastr-content-container {
				.pct-toastr-icon {
					@extend .pct;
					@extend .pct-check;
				}
			}
		}

		&.pct-toastr-warning {
			background: $feedbackAlert03;

			.pct-toastr-content-container {
				.pct-toastr-icon {
					@extend .pct;
					@extend .pct-alert-triangle;
				}
			}
		}

		&.pct-toastr-info {
			background: $feedbackInfo03;

			.pct-toastr-content-container {
				.pct-toastr-icon {
					@extend .pct;
					@extend .pct-alert-circle;
				}
			}
		}

		&.pct-toastr-error {
			background: $feedbackLoss03;

			.pct-toastr-content-container {
				.pct-toastr-icon {
					@extend .pct;
					@extend .pct-alert-triangle;
				}
			}
		}
	}

	&.toast-top-center,
	&.toast-bottom-center {
		.pct-toastr {
			margin-left: auto;
			margin-right: auto;
			width: fit-content;
		}
	}
}

.pct-toastr-inline {
	position: unset;

	.pct-toastr {
		padding: 8px 16px;
		display: flex;

		.pct-toastr-content-container {
			padding-top: unset;
			padding-bottom: unset;

			.pct-toastr-icon {
				margin-top: 2px;
			}

			.pct-toastr-content {
				margin-top: unset;
				margin-bottom: unset;

				.pct-toastr-title-section {
					display: flex;
					font-weight: 600;

					.toast-title {
						flex: 1;
					}
				}

				.toast-message {
					line-height: 20px;
				}
			}
		}

		.pct-toastr-close-section {
			margin-left: unset;
			display: none;
		}

		&.pct-toastr-success {
			background: $feedbackGain01;
			color: $feedbackGain03;
		}

		&.pct-toastr-warning {
			background: $feedbackAlert01;
			color: $feedbackAlert03;
		}

		&.pct-toastr-info {
			background: $feedbackInfo01;
			color: $feedbackInfo03;
		}

		&.pct-toastr-error {
			background: $feedbackLoss01;
			color: $feedbackLoss03;
		}
	}
}
