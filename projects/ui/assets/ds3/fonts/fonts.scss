// @import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;0,1000;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900;1,1000&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

$fontNunito: "Nunito Sans", sans-serif;
$fontPoppins: "Poppins", sans-serif;

/**
 TODO By: Lucas Rezende
  Descomentar ao finalizar a migração
 {
  @extend .pct-body1;
}*/

@mixin font-poppins() {
	font-family: $fontPoppins;
}

@mixin font-nunito() {
	font-family: $fontNunito;
}

@mixin font-style(
	$family,
	$size,
	$lineHeight: 125%,
	$letterSpacing: 0.25px,
	$fontWeight: normal
) {
	font-family: $family;
	font-size: $size;
	line-height: $lineHeight;
	letter-spacing: $letterSpacing;
	font-weight: $fontWeight;
}

.pct-title1 {
	@include font-style($family: $fontPoppins, $size: 30px, $fontWeight: 600);
}

.pct-title2 {
	@include font-style($family: $fontPoppins, $size: 22px, $fontWeight: 600);
}

.pct-title3 {
	@include font-style($family: $fontPoppins, $size: 18px, $fontWeight: 600);
}

.pct-title4 {
	@include font-style($family: $fontPoppins, $size: 14px, $fontWeight: 600);
}

.pct-title5 {
	@include font-style($family: $fontPoppins, $size: 12px, $fontWeight: 700);
}

.pct-body1 {
	@include font-style(
		$family: $fontNunito,
		$size: 16px,
		$letterSpacing: 0,
		$fontWeight: 400
	);
}

.pct-body1-regular {
	@extend .pct-body1;
}

.pct-body1-bold {
	@include font-style(
		$family: $fontNunito,
		$size: 16px,
		$letterSpacing: 0,
		$fontWeight: 700
	);
}

.pct-body2 {
	@include font-style(
		$family: $fontNunito,
		$size: 14px,
		$letterSpacing: 0,
		$fontWeight: 400
	);
}

.pct-body2-regular {
	@extend .pct-body2;
}

.pct-body2-bold {
	@include font-style(
		$family: $fontNunito,
		$size: 14px,
		$letterSpacing: 0,
		$fontWeight: 700
	);
}

.pct-overline1 {
	@include font-style(
		$family: $fontNunito,
		$size: 14px,
		$letterSpacing: 0,
		$fontWeight: 400
	);
}

.pct-overline1-regular {
	@extend .pct-overline1;
}

.pct-overline1-bold {
	@include font-style(
		$family: $fontNunito,
		$size: 14px,
		$letterSpacing: 0,
		$fontWeight: 700
	);
}

.pct-overline2 {
	@include font-style(
		$family: $fontNunito,
		$size: 12px,
		$letterSpacing: 0,
		$lineHeight: 16px,
		$fontWeight: 400
	);
}

.pct-overline2-regular {
	@extend .pct-overline2;
}

.pct-overline2-bold {
	@include font-style(
		$family: $fontNunito,
		$size: 12px,
		$letterSpacing: 0,
		$lineHeight: 16px,
		$fontWeight: 700
	);
}

.pct-display1 {
	@include font-style(
		$family: $fontPoppins,
		$size: 46px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-display2 {
	@include font-style(
		$family: $fontPoppins,
		$size: 40px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-display3 {
	@include font-style(
		$family: $fontPoppins,
		$size: 30px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-display4 {
	@include font-style(
		$family: $fontPoppins,
		$size: 22px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-display5 {
	@include font-style(
		$family: $fontPoppins,
		$size: 18px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-display6 {
	@include font-style(
		$family: $fontPoppins,
		$size: 14px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-display7 {
	@include font-style(
		$family: $fontPoppins,
		$size: 12px,
		$lineHeight: 100%,
		$fontWeight: 500
	);
}

.pct-btn-default1 {
	@include font-style(
		$family: $fontPoppins,
		$size: 14px,
		$lineHeight: 100%,
		$fontWeight: 600
	);
}

.pct-btn-default2 {
	@include font-style(
		$family: $fontPoppins,
		$size: 12px,
		$lineHeight: 100%,
		$fontWeight: 600
	);
}

.pct-btn-menu1 {
	@include font-style(
		$family: $fontPoppins,
		$size: 14px,
		$lineHeight: 100%,
		$fontWeight: 600
	);
}

.pct-btn-menu2 {
	@include font-style(
		$family: $fontPoppins,
		$size: 12px,
		$lineHeight: 100%,
		$fontWeight: 600
	);
}
