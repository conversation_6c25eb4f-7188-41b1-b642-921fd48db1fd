import { moduleMetadata } from "@storybook/angular";
import { select, withKnobs } from "@storybook/addon-knobs";
import { CommonModule } from "@angular/common";
import "./fonts.scss";

export default {
	title: "Design System 3 | Fonts",
	decorators: [
		moduleMetadata({
			imports: [CommonModule],
		}),
		withKnobs,
	],
	parameters: {
		docs: {
			hidden: true,
		},
	},
};

export const fonts = () => ({
	template: `
        <div style="margin-top: 2rem; margin-left: 2rem;">
			<div [class]="titleFontClass" style="margin-bottom: 16px">Title font</div>
			<div [class]="bodyFontClass" style="margin-bottom: 16px">Body font</div>
			<div [class]="overlineFontClass" style="margin-bottom: 16px">Overline font</div>
			<div [class]="displayFontClass" style="margin-bottom: 16px">Display font</div>
			<div [class]="btnDefaultFontClass" style="margin-bottom: 16px">Button default font</div>
			<div [class]="btnMenuFontClass">Button menu font</div>
        </div>
    `,
	props: {
		titleFontClass: select(
			"titleFontClass",
			["pct-title1", "pct-title2", "pct-title3", "pct-title4", "pct-title5"],
			"pct-title1"
		),
		bodyFontClass: select(
			"bodyFontClass",
			[
				"pct-body1",
				"pct-body1-regular",
				"pct-body1-bold",
				"pct-body2",
				"pct-body2-regular",
				"pct-body2-bold",
			],
			"pct-body1"
		),
		overlineFontClass: select(
			"overlineFontClass",
			[
				"pct-overline1",
				"pct-overline1-regular",
				"pct-overline1-bold",
				"pct-overline2",
				"pct-overline2-regular",
				"pct-overline2-bold",
			],
			"pct-overline1"
		),
		displayFontClass: select(
			"displayFontClass",
			[
				"pct-display1",
				"pct-display2",
				"pct-display3",
				"pct-display4",
				"pct-display5",
				"pct-display6",
				"pct-display7",
			],
			"pct-display1"
		),
		btnDefaultFontClass: select(
			"btnDefaultFontClass",
			["pct-btn-default1", "pct-btn-default2"],
			"pct-btn-default1"
		),
		btnMenuFontClass: select(
			"btnMenuFontClass",
			["pct-btn-menu1", "pct-btn-menu2"],
			"pct-btn-menu1"
		),
	},
});

fonts.story = {
	parameters: {
		docs: {
			hidden: true,
		},
	},
};
