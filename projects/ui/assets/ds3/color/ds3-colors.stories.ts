import { select, withKnobs } from "@storybook/addon-knobs";

import { moduleMetadata, StoryObj } from "@storybook/angular";
import "./index.scss";
import Notes from "./ds3-colors.md"; // eslint-disable-line
import { CommonModule } from "@angular/common";
import { UiModule } from "ui-kit";

export default {
	title: "Design System 3 | Styles/Color",
	decorators: [
		moduleMetadata({
			imports: [CommonModule, UiModule],
		}),
		withKnobs,
	],
	parameters: {
		notes: { Notes },
	},
};

export const Support: StoryObj = () => ({
	template: `
		<div class="all">
			<div class="color-grid">
				<div class="color-cell" style="background-color: var(--color-support-red-0)">support-red-0</div>
				<div class="color-cell" style="background-color: var(--color-support-red-1)">support-red-1</div>
				<div class="color-cell" style="background-color: var(--color-support-red-2)">support-red-2</div>
				<div class="color-cell" style="background-color: var(--color-support-red-3)">support-red-3</div>
				<div class="color-cell" style="background-color: var(--color-support-red-4)">support-red-4</div>
				<div class="color-cell" style="background-color: var(--color-support-red-5)">support-red-5</div>
				<div class="color-cell" style="background-color: var(--color-support-red-6)">support-red-6</div>
				<div class="color-cell" style="background-color: var(--color-support-red-7)">support-red-7</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-0)">support-pink-0</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-1)">support-pink-1</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-2)">support-pink-2</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-3)">support-pink-3</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-4)">support-pink-4</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-5)">support-pink-5</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-6)">support-pink-6</div>
				<div class="color-cell" style="background-color: var(--color-support-pink-7)">support-pink-7</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-0)">support-purple-0</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-1)">support-purple-1</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-2)">support-purple-2</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-3)">support-purple-3</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-4)">support-purple-4</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-5)">support-purple-5</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-6)">support-purple-6</div>
				<div class="color-cell" style="background-color: var(--color-support-purple-7)">support-purple-7</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-0)">support-blue-0</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-1)">support-blue-1</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-2)">support-blue-2</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-3)">support-blue-3</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-4)">support-blue-4</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-5)">support-blue-5</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-6)">support-blue-6</div>
				<div class="color-cell" style="background-color: var(--color-support-blue-7)">support-blue-7</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-0)">support-lightblue-0</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-1)">support-lightblue-1</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-2)">support-lightblue-2</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-3)">support-lightblue-3</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-4)">support-lightblue-4</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-5)">support-lightblue-5</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-6)">support-lightblue-6</div>
				<div class="color-cell" style="background-color: var(--color-support-lightblue-7)">support-lightblue-7</div>
				<div class="color-cell" style="background-color: var(--color-support-green-0)">support-green-0</div>
				<div class="color-cell" style="background-color: var(--color-support-green-1)">support-green-1</div>
				<div class="color-cell" style="background-color: var(--color-support-green-2)">support-green-2</div>
				<div class="color-cell" style="background-color: var(--color-support-green-3)">support-green-3</div>
				<div class="color-cell" style="background-color: var(--color-support-green-4)">support-green-4</div>
				<div class="color-cell" style="background-color: var(--color-support-green-5)">support-green-5</div>
				<div class="color-cell" style="background-color: var(--color-support-green-6)">support-green-6</div>
				<div class="color-cell" style="background-color: var(--color-support-green-7)">support-green-7</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-0)">support-yellow-0</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-1)">support-yellow-1</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-2)">support-yellow-2</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-3)">support-yellow-3</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-4)">support-yellow-4</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-5)">support-yellow-5</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-6)">support-yellow-6</div>
				<div class="color-cell" style="background-color: var(--color-support-yellow-7)">support-yellow-7</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-0)">support-orange-0</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-1)">support-orange-1</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-2)">support-orange-2</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-3)">support-orange-3</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-4)">support-orange-4</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-5)">support-orange-5</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-6)">support-orange-6</div>
				<div class="color-cell" style="background-color: var(--color-support-orange-7)">support-orange-7</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-0)">support-gray-0</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-1)">support-gray-1</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-2)">support-gray-2</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-3)">support-gray-3</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-4)">support-gray-4</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-5)">support-gray-5</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-6)">support-gray-6</div>
				<div class="color-cell" style="background-color: var(--color-support-gray-7)">support-gray-7</div>
				<div class="color-cell" style="background-color: var(--color-support-black-0)">support-black-0</div>
				<div class="color-cell" style="background-color: var(--color-support-black-1)">support-black-1</div>
				<div class="color-cell" style="background-color: var(--color-support-black-2)">support-black-2</div>
				<div class="color-cell" style="background-color: var(--color-support-black-3)">support-black-3</div>
				<div class="color-cell" style="background-color: var(--color-support-black-4)">support-black-4</div>
				<div class="color-cell" style="background-color: var(--color-support-black-5)">support-black-5</div>
				<div class="color-cell" style="background-color: var(--color-support-black-6)">support-black-6</div>
				<div class="color-cell" style="background-color: var(--color-support-black-7)">support-black-7</div>
			</div>
		</div>
`,
});

export const Background: StoryObj = () => ({
	template: `
		<div class="all">
			<div class="color-grid">
				<div class="color-cell" style="background-color: var(--color-background-menu-1)">background-menu-1</div>
				<div class="color-cell" style="background-color: var(--color-background-menu-2)">background-menu-2</div>
				<div class="color-cell" style="background-color: var(--color-background-menu-3)">background-menu-3</div>
				<div class="color-cell" style="background-color: var(--color-background-menu-4)">background-menu-4</div>
				<div class="color-cell" style="background-color: var(--color-background-plane-1)">background-plane-1</div>
				<div class="color-cell" style="background-color: var(--color-background-plane-2)">background-plane-2</div>
				<div class="color-cell" style="background-color: var(--color-background-plane-3)">background-plane-3</div>
				<div class="color-cell" style="background-color: var(--color-background-plane-4)">background-plane-4</div>
			</div>
		</div>`,
});
export const Typography: StoryObj = () => ({
	template: `
		<div class="all">
			<div class="color-grid">
				<div class="color-cell" style="background-color: var(--color-typography-default-title)">typography-default-title</div>
				<div class="color-cell" style="background-color: var(--color-typography-default-text)">typography-default-text</div>
				<div class="color-cell" style="background-color: var(--color-typography-bg-dark-title)">typography-bg-dark-title</div>
				<div class="color-cell" style="background-color: var(--color-typography-bg-dark-text)">typography-bg-dark-text</div>
			</div>
		</div>`,
});

export const Feedback: StoryObj = () => ({
	template: `
		<div class="all">
			<div class="color-grid">
				<div class="color-cell" style="background-color: var(--color-feedback-info-1)">feedback-info-1</div>
				<div class="color-cell" style="background-color: var(--color-feedback-info-2)">feedback-info-2</div>
				<div class="color-cell" style="background-color: var(--color-feedback-info-3)">feedback-info-3</div>
				<div class="color-cell" style="background-color: var(--color-feedback-gain-1)">feedback-gain-1</div>
				<div class="color-cell" style="background-color: var(--color-feedback-gain-2)">feedback-gain-2</div>
				<div class="color-cell" style="background-color: var(--color-feedback-gain-3)">feedback-gain-3</div>
				<div class="color-cell" style="background-color: var(--color-feedback-alert-1)">feedback-alert-1</div>
				<div class="color-cell" style="background-color: var(--color-feedback-alert-2)">feedback-alert-2</div>
				<div class="color-cell" style="background-color: var(--color-feedback-alert-3)">feedback-alert-3</div>
				<div class="color-cell" style="background-color: var(--color-feedback-loss-1)">feedback-loss-1</div>
				<div class="color-cell" style="background-color: var(--color-feedback-loss-2)">feedback-loss-2</div>
				<div class="color-cell" style="background-color: var(--color-feedback-loss-3)">feedback-loss-3</div>
			</div>
		</div>`,
});

export const Action: StoryObj = () => ({
	template: `
		<div class="all">
			<div class="color-grid">
				<div class="color-cell" style="background-color: var(--color-action-default-able-1)">action-default-able-1</div>
				<div class="color-cell" style="background-color: var(--color-action-default-able-2)">action-default-able-2</div>
				<div class="color-cell" style="background-color: var(--color-action-default-able-3)">action-default-able-3</div>
				<div class="color-cell" style="background-color: var(--color-action-default-able-4)">action-default-able-4</div>
				<div class="color-cell" style="background-color: var(--color-action-default-able-5)">action-default-able-5</div>
				<div class="color-cell" style="background-color: var(--color-action-default-risk-1)">action-default-risk-1</div>
				<div class="color-cell" style="background-color: var(--color-action-default-risk-2)">action-default-risk-2</div>
				<div class="color-cell" style="background-color: var(--color-action-default-risk-3)">action-default-risk-3</div>
				<div class="color-cell" style="background-color: var(--color-action-default-risk-4)">action-default-risk-4</div>
				<div class="color-cell" style="background-color: var(--color-action-default-risk-5)">action-default-risk-5</div>
				<div class="color-cell" style="background-color: var(--color-action-default-disable-1)">action-default-disable-1</div>
				<div class="color-cell" style="background-color: var(--color-action-default-disable-2)">action-default-disable-2</div>
				<div class="color-cell" style="background-color: var(--color-action-bgdark-able-1)">action-bgdark-able-1</div>
				<div class="color-cell" style="background-color: var(--color-action-bgdark-able-2)">action-bgdark-able-2</div>
				<div class="color-cell" style="background-color: var(--color-action-bgdark-able-3)">action-bgdark-able-3</div>
				<div class="color-cell" style="background-color: var(--color-action-bgdark-disable-1)">action-bgdark-disable-1</div>
			</div>
		</div>`,
});

export const Classes: StoryObj = () => ({
	template: `
		<div class="all">
		 Prefixo:Indica o tipo de propriedade CSS que a classe representa, como'bg-', 'cor-'e 'border-'.
 Categoria:Refere - se à categoria de cor, como'support', 'background', 'typography', 'feedback', 'action'.
 Cor:Indica a cor específica dentro da categoria.Por exemplo, 'pink', 'purple', 'blue', etc.
 Tom:Representa a variação de tom dentro da cor específica.Por exemplo, '0', '1', '2', ..., '7'.
			<div class="main bg-{{bg}} border-{{border}}">
			<p class="cor-{{color}}">classe cor de fundo: .bg-{{bg}}</p>
			<p class="cor-{{color}}">classe cor de bordar: .border-{{border}}</p>
			<p class="cor-{{color}}">classe cor de texto: .cor-{{color}}</p>
			</div>
		</div>`,
	props: {
		bg: select("cor bg", cores, "menu03"),
		border: select("cor-border", cores, "support-red06"),
		color: select("cor-color", cores, "type-bg-dark-text"),
	},
});

const cores = [
	"support-red",
	"support-red01",
	"support-red02",
	"support-red03",
	"support-red04",
	"support-red05",
	"support-red06",
	"support-red07",
	"support-pink",
	"support-pink01",
	"support-pink02",
	"support-pink03",
	"support-pink04",
	"support-pink05",
	"support-pink06",
	"support-pink07",
	"support-purple",
	"support-purple01",
	"support-purple02",
	"support-purple03",
	"support-purple04",
	"support-purple05",
	"support-purple06",
	"support-purple07",
	"support-blue",
	"support-blue01",
	"support-blue02",
	"support-blue03",
	"support-blue04",
	"support-blue05",
	"support-blue06",
	"support-blue07",
	"support-light-blue",
	"support-light-blue01",
	"support-light-blue02",
	"support-light-blue03",
	"support-light-blue04",
	"support-light-blue05",
	"support-light-blue06",
	"support-light-blue07",
	"support-green",
	"support-green01",
	"support-green02",
	"support-green03",
	"support-green04",
	"support-green05",
	"support-green06",
	"support-green07",
	"support-yellow",
	"support-yellow01",
	"support-yellow02",
	"support-yellow03",
	"support-yellow04",
	"support-yellow05",
	"support-yellow06",
	"support-yellow07",
	"support-orange",
	"support-orange01",
	"support-orange02",
	"support-orange03",
	"support-orange04",
	"support-orange05",
	"support-orange06",
	"support-orange07",
	"support-black",
	"support-black01",
	"support-black02",
	"support-black03",
	"support-black04",
	"support-black05",
	"support-black06",
	"support-black07",
	"support-gray",
	"support-gray01",
	"support-gray02",
	"support-gray03",
	"support-gray04",
	"support-gray05",
	"support-gray06",
	"support-gray07",
	"menu01",
	"menu02",
	"menu03",
	"menu04",
	"plane01",
	"plane02",
	"plane03",
	"plane04",
	"feedback-info01",
	"feedback-info02",
	"feedback-info03",
	"feedback-gain01",
	"feedback-gain02",
	"feedback-gain03",
	"feedback-alert01",
	"feedback-alert02",
	"feedback-alert03",
	"feedback-loss01",
	"feedback-loss02",
	"feedback-loss03",
	"action-default-able01",
	"action-default-able02",
	"action-default-able03",
	"action-default-able04",
	"action-default-able05",
	"action-default-risk01",
	"action-default-risk02",
	"action-default-risk03",
	"action-default-risk04",
	"action-default-risk05",
	"action-default-disabled01",
	"action-default-disabled02",
	"action-bg-dark-able01",
	"action-bg-dark-able02",
	"action-bg-dark-able03",
	"action-bg-dark-disabled01",
	"type-default-title",
	"type-default-text",
	"type-bg-dark-title",
	"type-bg-dark-text",
];
