# cores
## Support

Cada família de tom do grupo, possuí em sua própria família 8 variações de luminosidade, conservando o hue, mas alterando a quantidade de luz.

Em sua maioria, existe uma cadência de 10% de uma cor para a outra (podendo ter exceções). Onde a cor 0 possuí a maior quantidade de luz (95% de brilho), a 10° cor, possuí a menor quantidade de luz (25% de brilho); e a 4°, possui 55% de luminosidade, e por ser a cor média, pode ser considerada também, a cor principal de cada família.

**Red**

| Token                 | Valor                  |                                                      | 
| --------------------- | ---------------------- | ---------------------------------------------------- | 
| --color-support-red-0 | hsla(345, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/fce9ed?text=+) | 
| --color-support-red-1 | hsla(345, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/f5bcca?text=+) | 
| --color-support-red-2 | hsla(345, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/ef8fa7?text=+) | 
| --color-support-red-3 | hsla(345, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/e96384?text=+) | 
| --color-support-red-4 | hsla(345, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/e23661?text=+) | 
| --color-support-red-5 | hsla(345, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/c91d48?text=+) | 
| --color-support-red-6 | hsla(345, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/9c1638?text=+) | 
| --color-support-red-7 | hsla(345, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/701028?text=+) | 

**Pink**

| Token                  | Valor                  |                                                      |
| ---------------------- | ---------------------- | ---------------------------------------------------- |
| --color-support-pink-0 | hsla(315, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/fce9f7?text=+) |
| --color-support-pink-1 | hsla(315, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/f5bce7?text=+) |
| --color-support-pink-2 | hsla(315, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/ef8fd7?text=+) |
| --color-support-pink-3 | hsla(315, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/e963c7?text=+) |
| --color-support-pink-4 | hsla(315, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/e236b7?text=+) |
| --color-support-pink-5 | hsla(315, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/c91d9e?text=+) |
| --color-support-pink-6 | hsla(315, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/9c167b?text=+) |
| --color-support-pink-7 | hsla(315, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/701058?text=+) |

**Purple**

| Token                    | Valor                  |                                                      |
| ------------------------ | ---------------------- | ---------------------------------------------------- |
| --color-support-purple-0 | hsla(285, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/f7e9fc?text=+) |
| --color-support-purple-1 | hsla(285, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/e7bcf5?text=+) |
| --color-support-purple-2 | hsla(285, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/d78fef?text=+) |
| --color-support-purple-3 | hsla(285, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/c763e9?text=+) |
| --color-support-purple-4 | hsla(285, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/b736e2?text=+) |
| --color-support-purple-5 | hsla(285, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/9e1dc9?text=+) |
| --color-support-purple-6 | hsla(285, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/7b169c?text=+) |
| --color-support-purple-7 | hsla(285, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/581070?text=+) |

**Blue**

| Token                  | Valor                  |                                                      |
| ---------------------- | ---------------------- | ---------------------------------------------------- |
| --color-support-blue-0 | hsla(222, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/e9eefc?text=+) |
| --color-support-blue-1 | hsla(222, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/bccdf5?text=+) |
| --color-support-blue-2 | hsla(222, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/8facef?text=+) |
| --color-support-blue-3 | hsla(222, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/638be9?text=+) |
| --color-support-blue-4 | hsla(222, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/366ae2?text=+) |
| --color-support-blue-5 | hsla(222, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/1d50c9?text=+) |
| --color-support-blue-6 | hsla(222, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/163e9c?text=+) |
| --color-support-blue-7 | hsla(222, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/102d70?text=+) |

**Light Blue**

| Token                       | Valor                  |                                                      |
| --------------------------- | ---------------------- | ---------------------------------------------------- |
| --color-support-lightblue-0 | hsla(195, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/e9f7fc?text=+) |
| --color-support-lightblue-1 | hsla(195, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/bce7f5?text=+) |
| --color-support-lightblue-2 | hsla(195, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/8fd7ef?text=+) |
| --color-support-lightblue-3 | hsla(195, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/63c7e9?text=+) |
| --color-support-lightblue-4 | hsla(195, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/36b7e2?text=+) |
| --color-support-lightblue-5 | hsla(195, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/1d9ec9?text=+) |
| --color-support-lightblue-6 | hsla(195, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/167b9c?text=+) |
| --color-support-lightblue-7 | hsla(195, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/105870?text=+) |

**Green**

| Token                   | Valor                  |                                                      |
| ----------------------- | ---------------------- | ---------------------------------------------------- |
| --color-support-green-0 | hsla(150, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/e9fcf2?text=+) |
| --color-support-green-1 | hsla(150, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/bcf5d9?text=+) |
| --color-support-green-2 | hsla(150, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/8fefbf?text=+) |
| --color-support-green-3 | hsla(150, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/63e9a6?text=+) |
| --color-support-green-4 | hsla(150, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/1dc973?text=+) |
| --color-support-green-5 | hsla(150, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/169c59?text=+) |
| --color-support-green-6 | hsla(150, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/107040?text=+) |
| --color-support-green-7 | hsla(150, 75%, 15%, 1) | ![cor](https://via.placeholder.com/30/0a4326?text=+) |

**Yellow**

| Token                    | Valor                 |                                                      |
| ------------------------ | --------------------- | ---------------------------------------------------- |
| --color-support-yellow-0 | hsla(45, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/fcf7e9?text=+) |
| --color-support-yellow-1 | hsla(45, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/f5e7bc?text=+) |
| --color-support-yellow-2 | hsla(45, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/efd78f?text=+) |
| --color-support-yellow-3 | hsla(45, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/e9c763?text=+) |
| --color-support-yellow-4 | hsla(45, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/e2b736?text=+) |
| --color-support-yellow-5 | hsla(45, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/c99e1d?text=+) |
| --color-support-yellow-6 | hsla(45, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/9c7b16?text=+) |
| --color-support-yellow-7 | hsla(45, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/705810?text=+) |

**Orange**

| Token                    | Valor                 |                                                      |
| ------------------------ | --------------------- | ---------------------------------------------------- |
| --color-support-orange-0 | hsla(25, 75%, 95%, 1) | ![cor](https://via.placeholder.com/30/fcf1e9?text=+) |
| --color-support-orange-1 | hsla(25, 75%, 85%, 1) | ![cor](https://via.placeholder.com/30/f5d4bc?text=+) |
| --color-support-orange-2 | hsla(25, 75%, 75%, 1) | ![cor](https://via.placeholder.com/30/efb78f?text=+) |
| --color-support-orange-3 | hsla(25, 75%, 65%, 1) | ![cor](https://via.placeholder.com/30/e99b63?text=+) |
| --color-support-orange-4 | hsla(25, 75%, 55%, 1) | ![cor](https://via.placeholder.com/30/e27e36?text=+) |
| --color-support-orange-5 | hsla(25, 75%, 45%, 1) | ![cor](https://via.placeholder.com/30/c9641d?text=+) |
| --color-support-orange-6 | hsla(25, 75%, 35%, 1) | ![cor](https://via.placeholder.com/30/9c4e16?text=+) |
| --color-support-orange-7 | hsla(25, 75%, 25%, 1) | ![cor](https://via.placeholder.com/30/703810?text=+) |

**Gray**

| Token                  | Valor                 |                                                      |
| ---------------------- | --------------------- | ---------------------------------------------------- |
| --color-support-gray-0 | hsla(222, 4%, 95%, 1) | ![cor](https://via.placeholder.com/30/f2f2f3?text=+) |
| --color-support-gray-1 | hsla(222, 4%, 90%, 1) | ![cor](https://via.placeholder.com/30/e4e5e7?text=+) |
| --color-support-gray-2 | hsla(222, 4%, 85%, 1) | ![cor](https://via.placeholder.com/30/d7d8da?text=+) |
| --color-support-gray-3 | hsla(222, 4%, 80%, 1) | ![cor](https://via.placeholder.com/30/cacbce?text=+) |
| --color-support-gray-4 | hsla(222, 4%, 75%, 1) | ![cor](https://via.placeholder.com/30/bdbec2?text=+) |
| --color-support-gray-5 | hsla(222, 4%, 70%, 1) | ![cor](https://via.placeholder.com/30/afb1b6?text=+) |
| --color-support-gray-6 | hsla(222, 4%, 65%, 1) | ![cor](https://via.placeholder.com/30/a2a4a9?text=+) |
| --color-support-gray-7 | hsla(222, 4%, 60%, 1) | ![cor](https://via.placeholder.com/30/95979d?text=+) |

**Black**

| Token                   | Valor                 |                                                      |
| ----------------------- | --------------------- | ---------------------------------------------------- |
| --color-support-black-0 | hsla(222, 5%, 45%, 1) | ![cor](https://via.placeholder.com/30/6d7078?text=+) |
| --color-support-black-1 | hsla(222, 5%, 40%, 1) | ![cor](https://via.placeholder.com/30/61646b?text=+) |
| --color-support-black-2 | hsla(222, 5%, 35%, 1) | ![cor](https://via.placeholder.com/30/55575e?text=+) |
| --color-support-black-3 | hsla(222, 5%, 30%, 1) | ![cor](https://via.placeholder.com/30/494b50?text=+) |
| --color-support-black-4 | hsla(222, 5%, 25%, 1) | ![cor](https://via.placeholder.com/30/3d3e43?text=+) |
| --color-support-black-5 | hsla(222, 5%, 20%, 1) | ![cor](https://via.placeholder.com/30/303236?text=+) |
| --color-support-black-6 | hsla(222, 5%, 15%, 1) | ![cor](https://via.placeholder.com/30/242528?text=+) |
| --color-support-black-7 | hsla(222, 5%, 10%, 1) | ![cor](https://via.placeholder.com/30/18191b?text=+) |

## Background

Background colors, é o grupo de cor que compõe o preenchimento de alguns componentes e templates do sistema.

O Background possui toda sua tonalidade de 222 - o mesmo hue do azul da Enredo. Em exceção a cor Plane 2 e 3, nas quais tem a necessidade de um fino ajuste visual em sua aplicação no sistema.

**Menu**

| Token                     | Valor                  |                                                      |
| ------------------------- | ---------------------- | ---------------------------------------------------- |
| --color-background-menu-1 | hsla(222, 55%, 35%, 1) | ![cor](https://via.placeholder.com/30/28468a?text=+) |
| --color-background-menu-2 | hsla(222, 55%, 30%, 1) | ![cor](https://via.placeholder.com/30/223c77?text=+) |
| --color-background-menu-3 | hsla(222, 55%, 25%, 1) | ![cor](https://via.placeholder.com/30/1d3263?text=+) |
| --color-background-menu-4 | hsla(222, 55%, 30%, 1) | ![cor](https://via.placeholder.com/30/223c77?text=+) |

**Plane**

| Token                      | Valor                  |                                                       |
| -------------------------- | ---------------------- | ----------------------------------------------------- |
| --color-background-plane-1 | hsla(222, 33%, 98%, 1) | ![cor](https://via.placeholder.com/30/f8f9fc?text=+)  |
| --color-background-plane-2 | hsla(222, 0%, 100%, 1) | ![cor](https://via.placeholder.com/30/fff?text=+)     |
| --color-background-plane-3 | hsla(222, 0%, 98%, 1)  | ![cor ](https://via.placeholder.com/30/fafafa?text=+) |
| --color-background-plane-4 | hsla(222, 6%, 20%, 1)  | ![cor ](https://via.placeholder.com/30/303236?text=+) |

## Typography

O grupo de cor types influência em toda a experiência textual do usuário no sistema. Está presente em todos os textos do produto: Títulos, textos corridos, legendas, rodapés, textos de botão e etc...

Na escolha das cores foi levado em consideração o nível de contraste entre o texto e fundo, por meio de testes de aplicação. Respeitando assim, o principio da acessibilidade

**Default**

| Token                            | Valor                 |                                                      |
| -------------------------------- | --------------------- | ---------------------------------------------------- |
| --color-typography-default-title | hsla(222, 5%, 35%, 1) | ![cor](https://via.placeholder.com/30/55575e?text=+) |
| --color-typography-default-text  | hsla(222, 5%, 50%, 1) | ![cor](https://via.placeholder.com/30/797d86?text=+) |

**Dark Background**

| Token                            | Valor                  |                                                   |
| -------------------------------- | ---------------------- | ------------------------------------------------- |
| --color-typography-bg-dark-title | hsla(222, 0%, 100%, 1) | ![cor](https://via.placeholder.com/30/fff?text=+) |
| --color-typography-bg-dark-text  | hsla(222, 0%, 100%, 1) | ![cor](https://via.placeholder.com/30/fff?text=+) |

## Feedback

Feedback color são cores utilizadas em exclusivo para simbolizar respostas positivas ou negativas de fluxos, ou fazer por meio de uma associação simbólica das cores no imaginário coletivo ocidental, acelerar a interpretação sobre o caráter de determinada informação. Por exemplo: aviso de alerta, ou saldo positivo, ou saldo negativo.

**Info**

| Token                   | Valor                  |                                                      |
| ----------------------- | ---------------------- | ---------------------------------------------------- |
| --color-feedback-info-1 | hsla(222, 95%, 85%, 1) | ![cor](https://via.placeholder.com/30/b4cafd?text=+) |
| --color-feedback-info-2 | hsla(222, 95%, 35%, 1) | ![cor](https://via.placeholder.com/30/0437ae?text=+) |
| --color-feedback-info-3 | hsla(222, 95%, 25%, 1) | ![cor](https://via.placeholder.com/30/03287c?text=+) |

**Gain**

| Token                   | Valor                  |                                                      |
| ----------------------- | ---------------------- | ---------------------------------------------------- |
| --color-feedback-gain-1 | hsla(120, 95%, 85%, 1) | ![cor](https://via.placeholder.com/30/b4fdb4?text=+) |
| --color-feedback-gain-2 | hsla(120, 95%, 35%, 1) | ![cor](https://via.placeholder.com/30/04ae04?text=+) |
| --color-feedback-gain-3 | hsla(120, 95%, 25%, 1) | ![cor](https://via.placeholder.com/30/037c03?text=+) |

**Alert**

| Token                    | Valor                 |                                                      |
| ------------------------ | --------------------- | ---------------------------------------------------- |
| --color-feedback-alert-1 | hsla(60, 95%, 85%, 1) | ![cor](https://via.placeholder.com/30/fdfdb4?text=+) |
| --color-feedback-alert-2 | hsla(60, 95%, 35%, 1) | ![cor](https://via.placeholder.com/30/aeae04?text=+) |
| --color-feedback-alert-3 | hsla(60, 95%, 25%, 1) | ![cor](https://via.placeholder.com/30/7c7c03?text=+) |

**Loss**

| Token                   | Valor                |                                                      |
| ----------------------- | -------------------- | ---------------------------------------------------- |
| --color-feedback-loss-1 | hsla(0, 95%, 85%, 1) | ![cor](https://via.placeholder.com/30/fdb4b4?text=+) |
| --color-feedback-loss-2 | hsla(0, 95%, 35%, 1) | ![cor](https://via.placeholder.com/30/ae0404?text=+) |
| --color-feedback-loss-3 | hsla(0, 95%, 25%, 1) | ![cor](https://via.placeholder.com/30/7c0303?text=+) |

## Action

O grupo action compõe todos os componentes do sistema que são interativos com o usuário. Todos recursos que possuem como característica a clicabilidade, apresentam as cores actions em sua composição.

Assim como as cores background, o tom azul das cores action - a cor presente em todas interfaces do sistema - possui sua tonalidade de 222, o mesmo hue do azul da Enredo.

**Default Able**

| Token                         | Valor                  |                                                      |
| ----------------------------- | ---------------------- | ---------------------------------------------------- |
| --color-action-default-able-1 | hsla(222, 95%, 95%, 1) | ![cor](https://via.placeholder.com/30/e6edfe?text=+) |
| --color-action-default-able-2 | hsla(222, 95%, 55%, 1) | ![cor](https://via.placeholder.com/30/1f61f9?text=+) |
| --color-action-default-able-3 | hsla(222, 95%, 65%, 1) | ![cor](https://via.placeholder.com/30/5184fb?text=+) |
| --color-action-default-able-4 | hsla(222, 95%, 55%, 1) | ![cor](https://via.placeholder.com/30/1f61f9?text=+) |
| --color-action-default-able-5 | hsla(222, 95%, 45%, 1) | ![cor](https://via.placeholder.com/30/0647e0?text=+) |

**Default Risk**

| Token                         | Valor                |                                                      |
| ----------------------------- | -------------------- | ---------------------------------------------------- |
| --color-action-default-risk-1 | hsla(0, 95%, 95%, 1) | ![cor](https://via.placeholder.com/30/fee6e6?text=+) |
| --color-action-default-risk-2 | hsla(0, 95%, 85%, 1) | ![cor](https://via.placeholder.com/30/fdb4b4?text=+) |
| --color-action-default-risk-3 | hsla(0, 95%, 65%, 1) | ![cor](https://via.placeholder.com/30/fb5151?text=+) |
| --color-action-default-risk-4 | hsla(0, 95%, 55%, 1) | ![cor](https://via.placeholder.com/30/f91f1f?text=+) |
| --color-action-default-risk-5 | hsla(0, 95%, 45%, 1) | ![cor](https://via.placeholder.com/30/e00606?text=+) |

**Default Disable**

| Token                            | Valor                 |                                                      |
| -------------------------------- | --------------------- | ---------------------------------------------------- |
| --color-action-default-disable-1 | hsla(222, 5%, 90%, 1) | ![cor](https://via.placeholder.com/30/e4e5e7?text=+) |
| --color-action-default-disable-2 | hsla(222, 5%, 50%, 1) | ![cor](https://via.placeholder.com/30/797d86?text=+) |

**Dark Background Able**

| Token                        | Valor                    |                                                       |
| ---------------------------- | ------------------------ | ----------------------------------------------------- |
| --color-action-bgdark-able-1 | hsla(222, 0%, 100%, 0.1) | ![cor)](https://via.placeholder.com/30/fd1e1e?text=+) |
| --color-action-bgdark-able-2 | hsla(222, 0%, 0%, 0.2)   | ![cor)](https://via.placeholder.com/30/797d86?text=+) |
| --color-action-bgdark-able-3 | hsla(222, 0%, 100%, 1)   | ![cor](https://via.placeholder.com/30/white?text=+)   |

**Dark Background Disable**

| Token                           | Valor                  |                                                       |
| ------------------------------- | ---------------------- | ----------------------------------------------------- |
| --color-action-bgdark-disable-1 | hsla(222, 0%, 0%, 0.4) | ![cor)](https://via.placeholder.com/30/0e0e0e?text=+) |

Esses tokens são usados para adicionar cores a elementos no CSS. Cada token tem a seguinte estrutura: `--color-{categoria}-{cor}-{tom}`.

Além disso, estão disponíveis classes que podem ser aplicadas diretamente aos elementos HTML para adicionar as respectivas cores usando as variáveis CSS.
Exemplo de uso:

```plaintext
.{prefixo}-{categoria}-{cor}-{tom}
```

- **Prefixo:** Indica o tipo de propriedade CSS que a classe representa, como `background-color`, `color`, etc.

- **Categoria:** Refere-se à categoria de cor, como `support`, `background`, `typography`, `feedback`, `action`.

- **Cor:** Indica a cor específica dentro da categoria. Por exemplo, `red`, `pink`, `purple`, `blue`, etc.

- **Tom:** Representa a variação de tom dentro da cor específica. Por exemplo, `0`, `1`, `2`, ..., `7`.

### Exemplo de Uso:

1. **Background Colors:**

   ```html
   <!-- Para um elemento com a cor de fundo vermelha da categoria "support" e tom "0" -->
   <div class="background-color-support-red-0"></div>

   <!-- Para um elemento com a cor de fundo azul da categoria "support" e tom "3" -->
   <div class="background-color-support-blue-3"></div>
   ```

2. **Text Colors:**

   ```html
   <!-- Para um texto com a cor vermelha da categoria "support" e tom "0" -->
   <p class="text-color-support-red-0">
   	Este é um texto de exemplo com a cor vermelha da categoria de suporte.
   </p>

   <!-- Para um texto com a cor azul da categoria "support" e tom "3" -->
   <p class="text-color-support-blue-3">
   	Este é um texto de exemplo com a cor azul da categoria de suporte.
   </p>
   ```

### Exemplo de Uso de Token em uma Classe Personalizada:

Se você tiver um token personalizado, pode incorporá-lo em uma classe da seguinte maneira:

```css
.custom {
	background-color: var(--color-support-blue-3);
}
```

E você pode usar essa classe diretamente em seu HTML:

```html
<div class="custom"></div>
```
