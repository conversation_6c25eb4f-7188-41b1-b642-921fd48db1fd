@import "../functions";

$prefix: "color";

$categories: (
	"support"
		(
			"red": (
				0: hsla(345, 75%, 95%, 1),
				1: hsla(345, 75%, 85%, 1),
				2: hsla(345, 75%, 75%, 1),
				3: hsla(345, 75%, 65%, 1),
				4: hsla(345, 75%, 55%, 1),
				5: hsla(345, 75%, 45%, 1),
				6: hsla(345, 75%, 35%, 1),
				7: hsla(345, 75%, 25%, 1),
			),
			"pink": (
				0: hsla(315, 75%, 95%, 1),
				1: hsla(315, 75%, 85%, 1),
				2: hsla(315, 75%, 75%, 1),
				3: hsla(315, 75%, 65%, 1),
				4: hsla(315, 75%, 55%, 1),
				5: hsla(315, 75%, 45%, 1),
				6: hsla(315, 75%, 35%, 1),
				7: hsla(315, 75%, 25%, 1),
			),
			"purple": (
				0: hsla(285, 75%, 95%, 1),
				1: hsla(285, 75%, 85%, 1),
				2: hsla(285, 75%, 75%, 1),
				3: hsla(285, 75%, 65%, 1),
				4: hsla(285, 75%, 55%, 1),
				5: hsla(285, 75%, 45%, 1),
				6: hsla(285, 75%, 35%, 1),
				7: hsla(285, 75%, 25%, 1),
			),
			"blue": (
				0: hsla(222, 75%, 95%, 1),
				1: hsla(222, 75%, 85%, 1),
				2: hsla(222, 75%, 75%, 1),
				3: hsla(222, 75%, 65%, 1),
				4: hsla(222, 75%, 55%, 1),
				5: hsla(222, 75%, 45%, 1),
				6: hsla(222, 75%, 35%, 1),
				7: hsla(222, 75%, 25%, 1),
			),
			"lightblue": (
				0: hsla(195, 75%, 95%, 1),
				1: hsla(195, 75%, 85%, 1),
				2: hsla(195, 75%, 75%, 1),
				3: hsla(195, 75%, 65%, 1),
				4: hsla(195, 75%, 55%, 1),
				5: hsla(195, 75%, 45%, 1),
				6: hsla(195, 75%, 35%, 1),
				7: hsla(195, 75%, 25%, 1),
			),
			"green": (
				0: hsla(150, 75%, 95%, 1),
				1: hsla(150, 75%, 85%, 1),
				2: hsla(150, 75%, 75%, 1),
				3: hsla(150, 75%, 65%, 1),
				4: hsla(150, 75%, 45%, 1),
				5: hsla(150, 75%, 35%, 1),
				6: hsla(150, 75%, 25%, 1),
				7: hsla(150, 75%, 15%, 1),
			),
			"yellow": (
				0: hsla(45, 75%, 95%, 1),
				1: hsla(45, 75%, 85%, 1),
				2: hsla(45, 75%, 75%, 1),
				3: hsla(45, 75%, 65%, 1),
				4: hsla(45, 75%, 55%, 1),
				5: hsla(45, 75%, 45%, 1),
				6: hsla(45, 75%, 35%, 1),
				7: hsla(45, 75%, 25%, 1),
			),
			"orange": (
				0: hsla(25, 75%, 95%, 1),
				1: hsla(25, 75%, 85%, 1),
				2: hsla(25, 75%, 75%, 1),
				3: hsla(25, 75%, 65%, 1),
				4: hsla(25, 75%, 55%, 1),
				5: hsla(25, 75%, 45%, 1),
				6: hsla(25, 75%, 35%, 1),
				7: hsla(25, 75%, 25%, 1),
			),
			"gray": (
				0: hsla(222, 4%, 95%, 1),
				1: hsla(222, 4%, 90%, 1),
				2: hsla(222, 4%, 85%, 1),
				3: hsla(222, 4%, 80%, 1),
				4: hsla(222, 4%, 75%, 1),
				5: hsla(222, 4%, 70%, 1),
				6: hsla(222, 4%, 65%, 1),
				7: hsla(222, 4%, 60%, 1),
			),
			"black": (
				0: hsla(222, 5%, 45%, 1),
				1: hsla(222, 5%, 40%, 1),
				2: hsla(222, 5%, 35%, 1),
				3: hsla(222, 5%, 30%, 1),
				4: hsla(222, 5%, 25%, 1),
				5: hsla(222, 5%, 20%, 1),
				6: hsla(222, 5%, 15%, 1),
				7: hsla(222, 5%, 10%, 1),
			),
		),
	"background"
		(
			"menu": (
				1: hsla(222, 55%, 35%, 1),
				2: hsla(222, 55%, 30%, 1),
				3: hsla(222, 55%, 25%, 1),
				4: hsla(222, 55%, 30%, 1),
			),
			"plane": (
				1: hsla(222, 33%, 98%, 1),
				2: hsla(222, 0%, 100%, 1),
				3: hsla(0, 0%, 96%, 1),
				4: hsla(222, 6%, 20%, 1),
			),
		),
	"typography"
		(
			"default": (
				"title": hsla(226, 12%, 29%, 1),
				"text": hsla(230, 9%, 27%, 1),
			),
			"bg-dark": (
				"title": hsla(222, 0%, 100%, 1),
				"text": hsla(222, 0%, 100%, 1),
			),
		),
	"feedback"
		(
			"info": (
				1: hsla(222, 95%, 85%, 1),
				2: hsla(222, 95%, 35%, 1),
				3: hsla(222, 95%, 25%, 1),
			),
			"gain": (
				1: hsla(120, 95%, 85%, 1),
				2: hsla(120, 95%, 35%, 1),
				3: hsla(120, 95%, 25%, 1),
			),
			"alert": (
				1: hsla(60, 95%, 85%, 1),
				2: hsla(60, 95%, 35%, 1),
				3: hsla(60, 95%, 25%, 1),
			),
			"loss": (
				1: hsla(0, 95%, 85%, 1),
				2: hsla(0, 95%, 35%, 1),
				3: hsla(0, 95%, 25%, 1),
			),
		),
	"action"
		(
			"default-able": (
				1: hsla(222, 95%, 95%, 1),
				2: hsla(222, 95%, 55%, 1),
				3: hsla(222, 95%, 65%, 1),
				4: hsla(222, 95%, 55%, 1),
				5: hsla(222, 95%, 45%, 1),
			),
			"default-risk": (
				1: hsla(0, 95%, 95%, 1),
				2: hsla(0, 95%, 85%, 1),
				3: hsla(0, 95%, 65%, 1),
				4: hsla(0, 95%, 55%, 1),
				5: hsla(0, 95%, 45%, 1),
			),
			"default-disable": (
				1: hsla(222, 6%, 90%, 1),
				2: hsla(222, 5%, 50%, 1),
			),
			"bgdark-able": (
				1: hsla(222, 0%, 100%, 0.1),
				2: hsla(222, 0%, 0%, 0.2),
				3: hsla(222, 0%, 100%, 1),
			),
			"bgdark-disable": (
				1: hsla(222, 0%, 0%, 0.4),
			),
		)
);
