$gap: 12px;
$rows: 1;
$columns: 12;
$columnsSm: 8px;
$breakpoints: (
	// <700px
	xs: 699px,
	// >= 700px,
	sm: 700px,
	// >= 1024px,
	md: 1024px,
	// >= 1280px
	xmd: 1280px,
	// >= 1366px,
	lg: 1366px,
	// >= 1440px
	xl: 1440px,
	// >= 1920px
	xxl: 1920px
);

.pacto-grid {
	display: grid;
	grid-template-rows: repeat($rows, 1fr);
	grid-template-columns: repeat($columns, 1fr);
	gap: $gap;
	@media (max-width: 1023px) {
		grid-template-columns: repeat($columnsSm, 1fr);
	}
}

@each $key, $size in $breakpoints {
	$infix: "-#{$key}";
	@media (min-width: $size) {
		@for $i from 1 through $columns {
			.pct-col#{$infix}-#{$i} {
				grid-column: auto / span $i;
			}
		}

		@for $i from 1 through ($columns - 1) {
			.pct-start#{$infix}-#{$i} {
				grid-column-start: $i;
			}
		}
	}
}
