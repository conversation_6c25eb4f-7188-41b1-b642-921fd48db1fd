## Shadow Tokens

| Token      | Valor                                                                                 |
| ---------- | ------------------------------------------------------------------------------------- |
| --shadow-1 | 0 2px 2px color-mix(in hsl longer hue, var(--color-support-black-7) 60%, transparent) |
| --shadow-2 | 0 4px 4px color-mix(in hsl longer hue, var(--color-support-black-7) 60%, transparent) |
| --shadow-3 | 0 6px 6px color-mix(in hsl longer hue, var(--color-support-black-7) 60%, transparent) |
| --shadow-4 | 0 8px 8px color-mix(in hsl longer hue, var(--color-support-black-7) 60%, transparent) |

Esses tokens são usados para adicionar sombras a elementos usando a propriedade `box-shadow` no CSS. Cada token `--shadow-X` possui um efeito de sombra diferente, onde `X` é a intensidade da sombra.

Além disso, estão disponíveis classes (`.shadow-1`, `.shadow-2`, `.shadow-3`, `.shadow-4`) que podem ser aplicadas diretamente aos elementos HTML para adicionar as respectivas sombras usando as variáveis CSS.
Exemplo de uso:

```html
<div class="box shadow-1"></div>
<div class="box shadow-2"></div>
<div class="box teste"></div>
<div class="box shadow-4"></div>

<style>
	.box {
		width: 100px;
		height: 100px;
		background-color: #f0f0f0;
		border-radius: 0.3rem;
		margin: 1rem;
	}

	.teste {
		box-shadow: var(--shadow-3);
	}
</style>
```
