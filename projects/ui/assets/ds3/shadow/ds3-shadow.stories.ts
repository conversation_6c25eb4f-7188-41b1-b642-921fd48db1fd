import { withKnobs } from "@storybook/addon-knobs";
import { storiesOf } from "@storybook/angular";
import "./index.scss";
import Notes from "./ds3-shadow.md";

storiesOf("Design System 3 | Styles/Shadow", module)
	.addParameters({
		notes: { Notes },
	})
	.addDecorator(withKnobs)
	.add("Tokens and Classes", () => {
		return {
			template: `
				<div class="box shadow-1"></div>
				<div class="box shadow-2"></div>
				<div class="box teste"></div>
				<div class="box shadow-4"></div>
				
				<style>
					.box {
						width: 100px;
						height: 100px;
						background-color: #f0f0f0;
						border-radius: 0.3rem;
						margin: 1rem;
					}
				
					.teste {
						box-shadow: var(--shadow-3);
					}
				</style>
      `,
		};
	});
