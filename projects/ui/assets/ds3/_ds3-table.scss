ds3-table {
	overflow: auto;
	display: block;
	padding-bottom: 20px;

	&::-webkit-scrollbar {
		width: 0;
		height: 0;
	}
}
.ds3-table {
	width: 100%;

	th {
		padding: 0 16px;
		white-space: nowrap;
		border-bottom: 1px solid $supportGray02;
		@extend .pct-title5;
		background-color: #fff;
		color: $typeDefaultTitle;
		line-height: 56px;
	}
	.sort-control {
		width: 28px;
		height: 28px;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		font-size: 14px !important;
		margin-left: 4px;
		cursor: pointer;

		.pct {
			color: $actionDefaultAble04;
		}
	}
	td {
		overflow: hidden;
		text-overflow: ellipsis;
		padding: 0 16px;
		height: 56px;
		white-space: nowrap;
		@include font-style(
			$family: $fontNunito,
			$size: 12px,
			$letterSpacing: 0,
			$lineHeight: 16px,
			$fontWeight: 400
		);
		color: $typeDefaultText;
	}

	&.ds3-table-small {
		th {
			line-height: 40px;
		}
		td {
			height: 40px;
		}
	}

	th,
	td {
		&.align-center {
			text-align: center;
		}
		&.align-right {
			text-align: right;
		}
	}
	tbody {
		tr {
			&:nth-child(odd) {
				td {
					background-color: $plane03;
				}
			}
			&:nth-child(even) {
				td {
					background-color: #fff;
				}
			}
		}

		.ds3-table-empty {
			td {
				padding-top: 60px;
				text-align: center;

				h2 {
					@include font-style(
						$family: $fontPoppins,
						$size: 14px,
						$letterSpacing: 0,
						$lineHeight: 17.5px,
						$fontWeight: 600
					);
				}

				p {
					@include font-style(
						$family: $fontPoppins,
						$size: 14px,
						$letterSpacing: 0,
						$lineHeight: 17.5px,
						$fontWeight: 400
					);
				}
			}
			&:nth-child(odd),
			&:nth-child(even) {
				td {
					background: unset;
				}
			}
		}
	}
}
.ds3-h-scroll {
	padding: 0 24px;
	box-shadow: 0 4px 4px 0 hsla(0, 0%, 0%, 0.06);
}
.ds3-table-scroll-button {
	position: absolute;
	height: 100%;
	width: 24px;
	border: 1px solid $supportGray01;
	background-color: #fff;
	outline: none;
	color: $actionDefaultAble04;
	transition: 0.2s all linear;
	cursor: pointer;

	&:hover {
		background-color: $actionDefaultAble02;
		border-color: $actionDefaultAble02;
		outline: none;
	}
	&:focus {
		outline: none;
	}
}
.ds3-table-scroll-button-prev {
	top: 0;
	left: 0;
	box-shadow: 4px -1px 4px 0px hsla(0, 0%, 0%, 0.06);
}
.ds3-table-scroll-button-next {
	top: 0;
	left: calc(100% - 30px);
	box-shadow: -4px -1px 4px 0px hsla(0, 0%, 0%, 0.06);
}
.ds3-first-column-fixed {
	thead {
		th:first-child {
			position: absolute;
			left: 0;
			z-index: 10;
		}
	}
	tbody {
		td:first-child {
			position: absolute;
			left: 0;
			display: flex;
			align-items: center;
			gap: 8px;
			z-index: 10;
		}
	}
}
// .ds3-first-column-fixed tbody td:last-child,
// .ds3-first-column-fixed thead th:last-child {
// 	position: relative !important;
// }
.ds3-actions-column-fixed {
	thead {
		th:last-child {
			position: absolute;
			right: 0;
		}
	}
	tbody {
		td:last-child {
			position: absolute;
			right: 0;
			display: flex;
			align-items: center;
			gap: 16px;
		}
	}
}
.ds3-table-dots-nav {
	position: absolute;
	top: calc(100% + 20px);
	left: 50%;
	padding: 0 4px;
	transform: translateX(-50%);
	display: flex;
	gap: 8px;

	.ds3-table-dot {
		width: 9px;
		height: 19px;
		border: none;
		outline: none;
		border-radius: 4px;
		background-color: $plane03;
		cursor: pointer;
		transition: 0.2s all linear;
		&:hover {
			background-color: $actionDefaultAble02;
		}

		&.active {
			background-color: $actionDefaultAble04;
			border-color: $actionDefaultAble04;
		}
	}
	.ds3-table-dots-button {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		cursor: pointer;
		background-color: #fff;
		border: none;
		outline: none;
		font-size: 14px;
		color: $actionDefaultDisabled02;

		&:hover {
			color: $actionDefaultAble04;
		}
	}
	.ds3-table-dots-button-prev {
		left: -36px;
	}
	.ds3-table-dots-button-next {
		right: -36px;
	}
}
.ds3-table-dot.active {
	background-color: #007bff; /* ou qualquer cor de destaque */
	border-color: #007bff;
}
.ds3-table-head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 32px;
	padding: 0 16px;

	pacto-share-button {
		.btn {
			padding-left: 20px;
			.btn-share-label {
				@extend .pct-btn-default1;
			}
			&.pacto-primary {
				display: flex;
				align-items: center;
				margin-left: 0;
				.btn-share-label {
					margin-right: 20px;
				}
			}
		}
	}
}
.ds3-table-search {
	display: flex;
	width: 386px;

	.ds3-table-search-button {
		display: flex;
		align-items: center;
		margin-left: 16px;
	}
}
.ds3-table-foot {
	td {
		@extend .pct-display7;
		color: $typeDefaultTitle;
		background-color: #fff;
	}
}
.ds3-table-actions {
	display: flex;
	align-items: center;
	margin-left: auto;
	gap: 16px;
}
.ds3-table-footer {
	display: flex;
	justify-content: flex-end;
	margin-top: 16px;
	padding: 0 16px;
}
