$supportRed: hsla(347, 75%, 95%, 1);
$supportRed01: hsla(345, 75%, 85%, 1);
$supportRed02: hsla(345, 75%, 75%, 1);
$supportRed03: hsla(345, 75%, 65%, 1);
$supportRed04: hsla(345, 75%, 55%, 1);
$supportRed05: hsla(345, 75%, 45%, 1);
$supportRed06: hsla(345, 75%, 35%, 1);
$supportRed07: hsla(345, 75%, 25%, 1);

$supportPink: hsla(316, 75%, 95%, 1);
$supportPink01: hsla(315, 75%, 85%, 1);
$supportPink02: hsla(315, 75%, 75%, 1);
$supportPink03: hsla(315, 75%, 65%, 1);
$supportPink04: hsla(315, 75%, 55%, 1);
$supportPink05: hsla(315, 75%, 45%, 1);
$supportPink06: hsla(315, 75%, 35%, 1);
$supportPink07: hsla(315, 75%, 25%, 1);

$supportPurple: hsla(284, 75%, 95%, 1);
$supportPurple01: hsla(285, 75%, 85%, 1);
$supportPurple02: hsla(285, 75%, 75%, 1);
$supportPurple03: hsla(285, 75%, 65%, 1);
$supportPurple04: hsla(285, 96%, 55%, 1);
$supportPurple05: hsla(285, 75%, 45%, 1);
$supportPurple06: hsla(285, 75%, 35%, 1);
$supportPurple07: hsla(285, 75%, 25%, 1);

$supportBlue: hsla(224, 75%, 95%, 1);
$supportBlue01: hsla(222, 75%, 85%, 1);
$supportBlue02: hsla(222, 75%, 75%, 1);
$supportBlue03: hsla(222, 75%, 65%, 1);
$supportBlue04: hsla(222, 75%, 55%, 1);
$supportBlue05: hsla(222, 75%, 45%, 1);
$supportBlue06: hsla(222, 75%, 35%, 1);
$supportBlue07: hsla(222, 75%, 25%, 1);

$supportLightBlue: hsla(196, 75%, 95%, 1);
$supportLightBlue01: hsla(195, 75%, 85%, 1);
$supportLightBlue02: hsla(195, 75%, 75%, 1);
$supportLightBlue03: hsla(195, 75%, 65%, 1);
$supportLightBlue04: hsla(195, 75%, 55%, 1);
$supportLightBlue05: hsla(195, 75%, 45%, 1);
$supportLightBlue06: hsla(195, 75%, 35%, 1);
$supportLightBlue07: hsla(195, 75%, 25%, 1);

$supportGreen: hsla(148, 75%, 95%, 1);
$supportGreen01: hsla(151, 75%, 85%, 1);
$supportGreen02: hsla(150, 75%, 75%, 1);
$supportGreen03: hsla(150, 75%, 65%, 1);
$supportGreen04: hsla(150, 75%, 45%, 1);
$supportGreen05: hsla(150, 75%, 45%, 1);
$supportGreen06: hsla(150, 75%, 25%, 1);
$supportGreen07: hsla(149, 75%, 15%, 1);

$supportYellow: hsla(44, 75%, 95%, 1);
$supportYellow01: hsla(45, 75%, 85%, 1);
$supportYellow02: hsla(45, 75%, 75%, 1);
$supportYellow03: hsla(45, 75%, 65%, 1);
$supportYellow04: hsla(45, 75%, 55%, 1);
$supportYellow05: hsla(45, 75%, 45%, 1);
$supportYellow06: hsla(45, 75%, 35%, 1);
$supportYellow07: hsla(45, 75%, 25%, 1);

$supportOrange: hsla(25, 75%, 95%, 1);
$supportOrange01: hsla(27, 75%, 85%, 1);
$supportOrange02: hsla(27, 75%, 75%, 1);
$supportOrange03: hsla(27, 75%, 65%, 1);
$supportOrange04: hsla(27, 75%, 55%, 1);
$supportOrange05: hsla(27, 75%, 45%, 1);
$supportOrange06: hsla(27, 75%, 35%, 1);
$supportOrange07: hsla(27, 75%, 25%, 1);

$supportBlack: hsla(222, 5%, 45%, 1);
$supportBlack01: hsla(222, 5%, 40%, 1);
$supportBlack02: hsla(222, 5%, 35%, 1);
$supportBlack03: hsla(222, 5%, 30%, 1);
$supportBlack04: hsla(222, 5%, 25%, 1);
$supportBlack05: hsla(222, 6%, 20%, 1);
$supportBlack06: hsla(222, 5%, 15%, 1);
$supportBlack07: hsla(222, 6%, 10%, 1);

$supportGray: hsla(222, 4%, 95%, 1);
$supportGray01: hsla(222, 6%, 90%, 1);
$supportGray02: hsla(222, 5%, 85%, 1);
$supportGray03: hsla(222, 6%, 80%, 1);
$supportGray04: hsla(222, 5%, 75%, 1);
$supportGray05: hsla(223, 5%, 70%, 1);
$supportGray06: hsla(220, 5%, 65%, 1);
$supportGray07: hsla(221, 5%, 60%, 1);

$menu01: hsla(222, 55%, 35%, 1);
$menu02: hsla(222, 55%, 30%, 1);
$menu03: hsla(222, 55%, 25%, 1);
$menu04: hsla(222, 55%, 20%, 1);

$plane01: hsla(222, 33%, 95%, 1);
$plane02: hsla(0, 0%, 100%, 1);
$plane03: hsla(0, 0%, 98%, 1);
$plane04: hsla(222, 6%, 20%, 0.8);

$feedbackInfo01: hsla(222, 95%, 85%, 1);
$feedbackInfo02: hsla(222, 96%, 35%, 1);
$feedbackInfo03: hsla(222, 95%, 25%, 1);

$feedbackGain01: hsla(120, 95%, 85%, 1);
$feedbackGain02: hsla(120, 96%, 35%, 1);
$feedbackGain03: hsla(120, 95%, 25%, 1);

$feedbackAlert01: hsla(60, 95%, 85%, 1);
$feedbackAlert02: hsla(60, 96%, 35%, 1);
$feedbackAlert03: hsla(60, 95%, 25%, 1);

$feedbackLoss01: hsla(0, 95%, 85%, 1);
$feedbackLoss02: hsla(0, 96%, 35%, 1);
$feedbackLoss03: hsla(0, 95%, 25%, 1);

$actionDefaultAble01: hsla(223, 92%, 95%, 1);
$actionDefaultAble02: hsla(222, 95%, 85%, 1);
$actionDefaultAble03: hsla(222, 96%, 65%, 1);
$actionDefaultAble04: hsla(222, 96%, 55%, 1);
$actionDefaultAble05: hsla(222, 96%, 45%, 1);

$actionDefaultRisk01: hsla(0, 92%, 95%, 1);
$actionDefaultRisk02: hsla(0, 95%, 85%, 1);
$actionDefaultRisk03: hsla(0, 96%, 65%, 1);
$actionDefaultRisk04: hsla(0, 96%, 55%, 1);
$actionDefaultRisk05: hsla(0, 96%, 45%, 1);

$actionDefaultDisabled01: hsla(222, 6%, 90%, 1);
$actionDefaultDisabled02: hsla(222, 5%, 50%, 1);

$actionBgDarkAble01: hsla(0, 0%, 100%, 0.1);
$actionBgDarkAble02: hsla(0, 0%, 0%, 0.2);
$actionBgDarkAble03: hsla(0, 0%, 100%, 1);

$actionBgDarkDisabled01: hsla(0, 0%, 0%, 0.4);

$typeDefaultTitle: hsla(220, 5%, 35%, 1);
$typeDefaultText: hsla(223, 5%, 30%, 1);

$typeBgDarkTitle: hsla(0, 0%, 100%, 1);
$typeBgDarkText: hsla(0, 0%, 100%, 1);

$azulPacto: hsla(222, 86%, 42%, 1);
$branco: #ffffff;

$colors: (
	support-red: $supportRed,
	support-red01: $supportRed01,
	support-red02: $supportRed02,
	support-red03: $supportRed03,
	support-red04: $supportRed04,
	support-red05: $supportRed05,
	support-red06: $supportRed06,
	support-red07: $supportRed07,
	support-pink: $supportPink,
	support-pink01: $supportPink01,
	support-pink02: $supportPink02,
	support-pink03: $supportPink03,
	support-pink04: $supportPink04,
	support-pink05: $supportPink05,
	support-pink06: $supportPink06,
	support-pink07: $supportPink07,
	support-purple: $supportPurple,
	support-purple01: $supportPurple01,
	support-purple02: $supportPurple02,
	support-purple03: $supportPurple03,
	support-purple04: $supportPurple04,
	support-purple05: $supportPurple05,
	support-purple06: $supportPurple06,
	support-purple07: $supportPurple07,
	support-blue: $supportBlue,
	support-blue01: $supportBlue01,
	support-blue02: $supportBlue02,
	support-blue03: $supportBlue03,
	support-blue04: $supportBlue04,
	support-blue05: $supportBlue05,
	support-blue06: $supportBlue06,
	support-blue07: $supportBlue07,
	support-light-blue: $supportLightBlue,
	support-light-blue01: $supportLightBlue01,
	support-light-blue02: $supportLightBlue02,
	support-light-blue03: $supportLightBlue03,
	support-light-blue04: $supportLightBlue04,
	support-light-blue05: $supportLightBlue05,
	support-light-blue06: $supportLightBlue06,
	support-light-blue07: $supportLightBlue07,
	support-green: $supportGreen,
	support-green01: $supportGreen01,
	support-green02: $supportGreen02,
	support-green03: $supportGreen03,
	support-green04: $supportGreen04,
	support-green05: $supportGreen05,
	support-green06: $supportGreen06,
	support-green07: $supportGreen07,
	support-yellow: $supportYellow,
	support-yellow01: $supportYellow01,
	support-yellow02: $supportYellow02,
	support-yellow03: $supportYellow03,
	support-yellow04: $supportYellow04,
	support-yellow05: $supportYellow05,
	support-yellow06: $supportYellow06,
	support-yellow07: $supportYellow07,
	support-orange: $supportOrange,
	support-orange01: $supportOrange01,
	support-orange02: $supportOrange02,
	support-orange03: $supportOrange03,
	support-orange04: $supportOrange04,
	support-orange05: $supportOrange05,
	support-orange06: $supportOrange06,
	support-orange07: $supportOrange07,
	support-black: $supportBlack,
	support-black01: $supportBlack01,
	support-black02: $supportBlack02,
	support-black03: $supportBlack03,
	support-black04: $supportBlack04,
	support-black05: $supportBlack05,
	support-black06: $supportBlack06,
	support-black07: $supportBlack07,
	support-gray: $supportGray,
	support-gray01: $supportGray01,
	support-gray02: $supportGray02,
	support-gray03: $supportGray03,
	support-gray04: $supportGray04,
	support-gray05: $supportGray05,
	support-gray06: $supportGray06,
	support-gray07: $supportGray07,
	menu01: $menu01,
	menu02: $menu02,
	menu03: $menu03,
	menu04: $menu04,
	plane01: $plane01,
	plane02: $plane02,
	plane03: $plane03,
	plane04: $plane04,
	feedback-info01: $feedbackInfo01,
	feedback-info02: $feedbackInfo02,
	feedback-info03: $feedbackInfo03,
	feedback-gain01: $feedbackGain01,
	feedback-gain02: $feedbackGain02,
	feedback-gain03: $feedbackGain03,
	feedback-alert01: $feedbackAlert01,
	feedback-alert02: $feedbackAlert02,
	feedback-alert03: $feedbackAlert03,
	feedback-loss01: $feedbackLoss01,
	feedback-loss02: $feedbackLoss02,
	feedback-loss03: $feedbackLoss03,
	action-default-able01: $actionDefaultAble01,
	action-default-able02: $actionDefaultAble02,
	action-default-able03: $actionDefaultAble03,
	action-default-able04: $actionDefaultAble04,
	action-default-able05: $actionDefaultAble05,
	action-default-risk01: $actionDefaultRisk01,
	action-default-risk02: $actionDefaultRisk02,
	action-default-risk03: $actionDefaultRisk03,
	action-default-risk04: $actionDefaultRisk04,
	action-default-risk05: $actionDefaultRisk05,
	action-default-disabled01: $actionDefaultDisabled01,
	action-default-disabled02: $actionDefaultDisabled02,
	action-bg-dark-able01: $actionBgDarkAble01,
	action-bg-dark-able02: $actionBgDarkAble02,
	action-bg-dark-able03: $actionBgDarkAble03,
	action-bg-dark-disabled01: $actionBgDarkDisabled01,
	type-default-title: $typeDefaultTitle,
	type-default-text: $typeDefaultText,
	type-bg-dark-title: $typeBgDarkTitle,
	type-bg-dark-text: $typeBgDarkText,
	azul-pacto: $azulPacto,
	branco: $branco,
);
