$plataforma-breakpoint-xlarge: 1920px;
$plataforma-breakpoint-large: 1440px;
$plataforma-breakpoint-medium: 1280px;
$plataforma-breakpoint-small: 1024px;

$bootstrap-breakpoint-xl: 1200px;
$bootstrap-breakpoint-lg: 992px;
$bootstrap-breakpoint-md: 768px;
$bootstrap-breakpoint-sm: 576px;

@mixin plataformaConteudoLargura() {
	width: calc(100vw - 460px);
	max-width: 1230px;

	@media (max-width: $plataforma-breakpoint-large) {
		width: calc(100vw - 260px);
	}
}

@mixin plataformaV2LarguraConteudo() {
	max-width: 1920px;
	width: 100%;
	padding: 32px 16px;
}

@mixin plataformaLarguraModalPerfil() {
	margin-top: 80px;
	width: 300px;
	float: right;
	left: calc(100vw - 234px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 193px);
	}

	@media (min-width: 2200px) {
		left: calc(50% + 907px);
	}
}

@mixin plataformaLarguraModalPerfilOffMenu() {
	margin-top: 80px;
	width: 300px;
	left: calc(100vw - 325px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 325px);
	}

	@media (min-width: 1878px) {
		left: calc(50% + 652px);
	}
}

@mixin plataformaLarguraModalAppsPacto() {
	margin-top: 80px;
	width: 196px;
	left: calc(100vw - 348px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 423px);
	}

	@media (min-width: 1878px) {
		left: 1525px;
	}

	@media (max-width: 580px) {
		left: calc(100vw - 293px);
	}
}

@mixin plataformaLarguraModalPerfilOffMenuAppsPacto() {
	margin-top: 80px;
	width: 196px;
	left: calc(100vw - 440px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 425px);
	}

	@media (min-width: 1878px) {
		left: 1435px;
	}
}

@mixin plataformaLarguraModalAjuda() {
	margin-top: 80px;
	width: 452px;
	left: calc(100vw - 604px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 700px);
	}

	@media (min-width: 1878px) {
		left: 1440px;
	}

	@media (max-width: 580px) {
		left: calc(100vw - 452px);
	}
}

@mixin plataformaLarguraModalPerfilOffMenuAjuda() {
	margin-top: 80px;
	width: 452px;
	left: calc(100vw - 604px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 479px);
	}

	@media (min-width: 1878px) {
		left: 1269px;
	}
}

@mixin plataformaLarguraModalAlunos() {
	margin-top: 80px;
	width: 305px;
	left: calc(75vw - 25px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(99vw - 20rem);
	}
}

@mixin plataformaLarguraModalPerfilOffMenuAlunos() {
	margin-top: 60px !important;
	width: 305px !important;
	overflow: hidden !important;
	left: calc(100vw - 350px) !important;

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 325px) !important;
	}

	@media (min-width: 1878px) {
		left: calc(90% - 325px) !important;
	}
}
