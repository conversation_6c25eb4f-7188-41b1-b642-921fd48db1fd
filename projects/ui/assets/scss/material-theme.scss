@import "node_modules/@angular/material/theming";

@include mat-core();

$md-primary: (
	50: #e4e8ed,
	100: #bbc6d1,
	200: #8da0b3,
	300: #5f7a94,
	400: #3d5e7d,
	500: #1b4166,
	600: #183b5e,
	700: #143253,
	800: #102a49,
	900: #081c38,
	A100: #70a5ff,
	A200: #3d85ff,
	A400: #0a64ff,
	A700: #0058f0,
	contrast: (
		50: #000000,
		100: #000000,
		200: #000000,
		300: #ffffff,
		400: #ffffff,
		500: #ffffff,
		600: #ffffff,
		700: #ffffff,
		800: #ffffff,
		900: #ffffff,
		A100: #000000,
		A200: #ffffff,
		A400: #ffffff,
		A700: #ffffff,
	),
);

$md-accent: (
	50: #effce8,
	100: #d7f7c6,
	200: #bdf2a1,
	300: #a3ed7b,
	400: #8fe95e,
	500: #7be542,
	600: #73e23c,
	700: #68de33,
	800: #5eda2b,
	900: #4bd31d,
	A100: #ffffff,
	A200: #dfffd6,
	A400: #b8ffa3,
	A700: #a4ff8a,
	contrast: (
		50: #000000,
		100: #000000,
		200: #000000,
		300: #000000,
		400: #000000,
		500: #000000,
		600: #000000,
		700: #000000,
		800: #000000,
		900: #000000,
		A100: #000000,
		A200: #000000,
		A400: #000000,
		A700: #000000,
	),
);

$pacto-primary: mat-palette($md-primary);
$pacto-accent: mat-palette($md-accent);
$pacto-warn: mat-palette($mat-red);
$pacto-theme: mat-light-theme($pacto-primary, $pacto-accent, $pacto-warn);
@include angular-material-theme($pacto-theme);

$pacto-typography: mat-typography-config(
	$font-family: "'Nunito Sans', sans-serif",
);

@include angular-material-typography($pacto-typography);
