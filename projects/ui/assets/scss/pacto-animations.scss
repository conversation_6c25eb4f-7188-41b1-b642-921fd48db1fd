@keyframes ghostBar {
	0% {
		background-position: -100px;
	}

	40% {
		background-position: 40vw;
	}

	100% {
		background-position: 60vw;
	}
}

@keyframes ghostCircle {
	0% {
		background-position: -100px + 68;
	}

	40%,
	100% {
		background-position: 140px + 68;
	}
}

.ghost-bar {
	margin: 5px !important;
	height: 50% !important;
	background-size: 80vw;
	background-image: linear-gradient(
		90deg,
		#dddddda4 0px,
		#f2f2f2e6 40px,
		#dddddda4 80px
	);
	animation: ghostBar 1.6s infinite linear;
}

.ghost-circle {
	background-size: 80vw;
	background-image: linear-gradient(
		90deg,
		#dddddda4 0px,
		#f2f2f2e6 40px,
		#dddddda4 80px
	);
	animation: ghostCircle 1.6s infinite linear;
}
