@import "./config.vars.scss";

ngb-modal-window.modal {
	z-index: 1100;
}

// Tooltip do ngb-tooltip com z-index alto para aparecer na frente do menu
ngb-tooltip-window {
	z-index: 10000 !important;
}

.tooltip {
	z-index: 10000 !important;
}

// Classe específica para tooltip do checkbox com z-index ainda mais alto
.checkbox-tooltip-high-zindex {
	z-index: 15000 !important;
	position: relative !important;
}

ngb-tooltip-window.checkbox-tooltip-high-zindex {
	z-index: 15000 !important;
	position: fixed !important;
}

// Classe específica para tooltip dos botões
.button-tooltip-high-zindex {
	z-index: 15000 !important;
	position: relative !important;
}

ngb-tooltip-window.button-tooltip-high-zindex {
	z-index: 15000 !important;
	position: fixed !important;
}

.balloon-backdrop {
	z-index: 98 !important;
}

.balloon-modal .modal-dialog {
	margin-right: 88vw;
	z-index: 1100;
	@include plataformaLarguraModalPerfil;
}

.modal-ajuda {
	margin-right: 153vw;
	z-index: 1100;
	margin-top: 80px;
	width: 452px;
	left: calc(100vw - 491px);

	@media (max-width: $plataforma-breakpoint-medium) {
		left: calc(100vw - 491px);
	}

	@media (min-width: 2200px) {
		left: calc(50% + 491px);
	}
}

.balloon-modal-off-menu .modal-dialog {
	margin-right: 100vw;
	z-index: 1100;
	@include plataformaLarguraModalPerfilOffMenu;
}

.balloon-modal-apps-pacto .modal-dialog {
	margin-right: 100vw;
	z-index: 1100;
	@include plataformaLarguraModalAppsPacto;
}

.balloon-modal-ajuda .modal-dialog,
.balloon-modal-off-menu-ajuda .modal-dialog {
	z-index: 1100;
	margin-top: 80px;
	position: absolute;
	width: 452px;
	right: 28px;
}

.modal-video .modal-dialog .modal-content {
	background: none;
	border: none;
}

.modal-video .modal-dialog .modal-content .modal-titulo {
	display: none;
}

.balloon-modal-off-menu-apps-pacto .modal-dialog {
	margin-right: 100vw;
	z-index: 1100;
	@include plataformaLarguraModalPerfilOffMenuAppsPacto;
}

.balloon-modal-alunos .modal-dialog {
	margin-right: 100vw;
	z-index: 1100;
	@include plataformaLarguraModalAlunos;
}

.balloon-modal-off-menu-alunos {
	margin-right: 100vw;
	z-index: 1100;
	@include plataformaLarguraModalPerfilOffMenuAlunos;
}

.balloon-modal .modal-content {
	background-color: transparent;
	border: 0px;
}

.balloon-modal-off-menu .modal-content {
	background-color: transparent;
	border: 0px;
}

.balloon-modal-apps-pacto .modal-content {
	background-color: transparent;
	border: 0px;
}

.balloon-modal-off-menu-apps-pacto .modal-content {
	background-color: transparent;
	border: 0px;
}

.balloon-modal-off-menu-ajuda .modal-content {
	background-color: transparent;
	border: 0px;
}

.pct-v2-largura-conteudo {
	@include plataformaV2LarguraConteudo();
}

.pct-layout-header {
	margin-bottom: 48px;
}

a.pct-migalha,
a.pct-migalha:hover,
a.pct-migalha:visited {
	color: $cinza06;
	@extend .type-p-small;
	font-weight: 600;
}

.pct-page-title {
	@extend .type-h3-bold;
}

.pct-page-subtitle {
	padding-left: 32px;
	color: $cinza06;
	@extend .type-caption;
}
