import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import {
	ActivatedRouteSnapshot,
	CanActivate,
	Router,
	RouterStateSnapshot,
} from "@angular/router";

import { JwtHelperService } from "@auth0/angular-jwt";

import {
	ClientDiscoveryService,
	environment,
	LocalStorageSessionService,
	SessionService,
} from "sdk";

import { Observable } from "rxjs";

@Injectable({
	providedIn: "root",
})
export class LoggedinGuard implements CanActivate {
	public constructor(
		private discover: ClientDiscoveryService,
		private localStorageSession: LocalStorageSessionService,
		private router: Router,
		private sessionService: SessionService,
		@Inject(LOCALE_ID) private locale
	) {}

	/**
	 * In case the user is not logged in, check for valid
	 * login credentials in the local storage. Otherwise,
	 * redirect user.
	 */
	canActivate(
		next: ActivatedRouteSnapshot,
		state: RouterStateSnapshot
	): Observable<boolean> | Promise<boolean> | boolean {
		// User is logged
		if (this.sessionService.isUserLogged()) {
			return true;

			// User is not logged, check in local storage
		} else if (this.isTokenValid()) {
			const urlParam: any = this.localStorageSession.getLocalStorageParams();
			urlParam.redirect = state.url;
			this.router.navigate(["adicionarConta"], {
				queryParams: urlParam,
			});
		} else {
			localStorage.removeItem("apiToken");
			console.info(`Token: has expired, redirect to login...`);
			const urlMap = this.discover.getUrlMap();
			let login;
			if (
				environment.newLoginEnabled &&
				urlMap.loginFrontUrl !== undefined &&
				urlMap.loginFrontUrl !== ""
			) {
				login = `${urlMap.loginFrontUrl}/${this.locale}/logout`;
			} else {
				login = urlMap.loginAppUrl;
				if (this.sessionService.chave) {
					login += `${urlMap}/${this.sessionService.chave}`;
				}
			}
			location.replace(login);
		}
	}

	private isTokenValid(): boolean {
		const token = localStorage.getItem("apiToken");
		if (token) {
			const helper = new JwtHelperService();
			const decodedToken = helper.decodeToken(token);
			const nowSeconds = new Date().valueOf() / 1000;
			return nowSeconds <= decodedToken.exp;
		} else {
			return false;
		}
	}
}
