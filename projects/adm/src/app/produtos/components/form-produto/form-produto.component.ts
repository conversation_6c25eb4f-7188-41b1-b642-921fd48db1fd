import { Component, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { BUTTON_TYPE, PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "adm-produto-form",
	templateUrl: "./form-produto.component.html",
	styleUrls: ["./form-produto.component.scss"],
})
export class FormProdutoComponent implements OnInit {
	buttonType = BUTTON_TYPE;
	formGroup: FormGroup;
	formBuilder: FormBuilder = new FormBuilder();
	selectedOptions: Array<any> = new Array<any>();

	table: PactoDataGridConfig;

	constructor(private activeModal: NgbActiveModal) {}

	ngOnInit() {
		this.initTable();
		this.createForm();
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					size: 3,
					content: [
						{
							codigo: "1",
							valorMensal: "150,00",
						},
					],
					totalElements: 3,
					number: 0,
				};
			},
			ghostLoad: true,
			ghostAmount: 5,
			formGroup: new FormGroup({
				valor: new FormControl(""),
			}),
			columns: [
				{ nome: "codigo", titulo: "#", visible: true, ordenavel: false },
				{
					nome: "vezesSemana",
					titulo: "Repetições na semana",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "descricao",
					inputSelectData: [
						{
							codigo: 1,
							label: "3x",
						},
					],
					showAddSelectBtn: false,
					width: "200px",
				},
				{
					nome: "valorMensal",
					titulo: "Valor mensal",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "text",
				},
			],
		});
	}

	createForm() {
		this.formGroup = new FormGroup({
			nome: new FormControl("", [Validators.required]),
			opcoes: new FormControl(""),
		});
	}

	closeHandler() {
		this.activeModal.dismiss({ teste: "asdf" });
	}

	save() {
		this.activeModal.dismiss({ teste: "asdf" });
	}
}
