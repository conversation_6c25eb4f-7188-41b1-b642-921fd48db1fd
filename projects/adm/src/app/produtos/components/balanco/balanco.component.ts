import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { ProdutoApiBalancoService } from "produto-api";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

declare var moment;

@Component({
	selector: "adm-balanco",
	templateUrl: "./balanco.component.html",
	styleUrls: ["./balanco.component.scss"],
})
export class BalancoComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("columnDataCadastro", { static: true })
	columnDataCadastro: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: true }) columnEmpresa: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;

	table: PactoDataGridConfig;
	permiteCadastrarBalanco: boolean;
	permiteCancelarBalanco: boolean;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private service: ProdutoApiBalancoService
	) {
		this.permiteCancelarBalanco = this.session.funcionalidades.get(
			PerfilAcessoRecursoNome.CANCELAR_BALANCO
		);
		this.permiteCadastrarBalanco = this.session.funcionalidades.get(
			PerfilAcessoRecursoNome.BALANCO
		);
	}

	ngOnInit() {
		this.initTable();
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("balanco", false, Api.MSPRODUTO),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "dataCadastro",
						// titulo: "Data",
						titulo: this.columnDataCadastro,
						visible: true,
						ordenavel: true,
						valueTransform: (v) =>
							moment(v).format(this.traducao.getLabel("data-formatado")),
					},
					{
						nome: "cancelado",
						// titulo: "Situação",
						titulo: this.columnSituacao,
						visible: true,
						ordenavel: true,
						valueTransform: (v) =>
							v
								? this.traducao.getLabel("Cancelado")
								: this.traducao.getLabel("Ativo"),
					},
					{
						nome: "empresa",
						// titulo: "Empresa",
						titulo: this.columnEmpresa,
						visible: true,
						ordenavel: true,
						valueTransform: (v: any) => v.nome,
					},
					{
						nome: "descricao",
						// titulo: "Descrição",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: this.traducao.getLabel("action-edit"),
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("editar-tooltip"),
						actionFn: (row) => this.editBalanco(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	editBalanco(balanco) {
		this.router.navigate(["adm", "produtos", "balanco", balanco.codigo]);
	}

	novoBalanco() {
		if (this.permiteCadastrarBalanco) {
			this.router.navigate(["adm", "produtos", "novo-balanco"]);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("permissao-cadastrar-balanco")
			);
		}
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editBalanco(event.row);
		}
	}
}
