import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

import { Balanco, BalancoItem, ProdutoApiBalancoService } from "produto-api";

import { BalancoModalComponent } from "../balanco-modal/balanco-modal.component";

@Component({
	selector: "adm-balanco-form",
	templateUrl: "./balanco-form.component.html",
	styleUrls: ["./balanco-form.component.scss"],
})
export class BalancoFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("tableBalancoItensComponent", { static: true })
	tableBalancoItensComponent: RelatorioComponent;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnQuantidadeBalanco", { static: true })
	columnQuantidadeBalanco: TemplateRef<any>;
	@ViewChild("columnQuantidadeAnterior", { static: true })
	columnQuantidadeAnterior: TemplateRef<any>;
	@ViewChild("columnDiferenca", { static: true })
	columnDiferenca: TemplateRef<any>;

	permiteCadastrarBalanco: boolean;
	permiteCancelarBalanco: boolean;
	page = 0;
	cancelado = false;
	hasId = false;
	clonar = false;
	tableBalancoItens: PactoDataGridConfig;

	produtoControl: FormControl = new FormControl();
	codigoControl: FormControl = new FormControl();
	qntdAnterior;
	form: FormGroup;
	formItems: FormGroup;
	id;

	balanco: Balanco = new Balanco();

	Empresas: Array<any> = new Array<any>();
	exibirEmpresa: boolean = this.sessionService.loggedUser.administrador;

	balancoItens: {
		content: Array<BalancoItem>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<BalancoItem>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	constructor(
		private router: Router,
		private notificationService: SnotifyService,
		private activatedRoute: ActivatedRoute,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private service: ProdutoApiBalancoService,
		private dialogService: DialogService
	) {
		this.permiteCancelarBalanco = this.sessionService.funcionalidades.get(
			PerfilAcessoRecursoNome.CANCELAR_BALANCO
		);
		this.permiteCadastrarBalanco = this.sessionService.funcionalidades.get(
			PerfilAcessoRecursoNome.BALANCO
		);
	}

	ngOnInit() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		if (this.id) {
			this.hasId = true;
		}

		this.loadEmpresas();

		this.initForms();
		this.form.get("dataCadastro").disable();
		this.initTableBalancoItens();
	}

	changeFormState(desabilitar: boolean = true) {
		if (desabilitar) {
			this.form.disable();
			this.formItems.disable();
			this.produtoControl.disable();
			return;
		}
		this.form.enable();
		this.form.get("dataCadastro").disable();
		this.form.get("dataCadastro").setValue(new Date());
		this.formItems.enable();
		this.produtoControl.enable();
	}

	ngAfterViewInit() {
		if (this.hasId) {
			this.changeFormState(this.id);

			this.service.find(this.id).subscribe((response) => {
				this.balanco = response.content;
				this.codigoControl.setValue(this.balanco.codigo);
				this.cancelado = this.balanco.cancelado;
				this.form.patchValue({
					empresa: this.balanco.empresa,
					descricao: this.balanco.descricao,
					dataCadastro: this.balanco.dataCadastro,
					observacoes: this.balanco.observacoes,
				});
				this.sortBalancoItens();
				this.createBalancoItensObject();
				this.cd.detectChanges();
			});
		}
	}

	createBalancoItensObject(page = 1, size = 10) {
		this.balancoItens.totalElements = this.balanco.balancoItens.length;
		this.balancoItens.size = size;
		this.balancoItens.totalPages = Math.ceil(
			+(this.balancoItens.totalElements / this.balancoItens.size)
		);
		this.balancoItens.first = page === 0 || page === 1;
		this.balancoItens.last = page === this.balancoItens.totalPages;
		this.balancoItens.content = this.balanco.balancoItens.slice(
			size * page - size,
			size * page
		);
		if (this.tableBalancoItensComponent) {
			this.tableBalancoItensComponent.reloadData();
		}
	}

	initTableBalancoItens() {
		this.tableBalancoItens = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return this.balancoItens;
			},
			columns: [
				{
					nome: "produto",
					titulo: this.columnProduto,
					visible: true,
					ordenavel: false,
					valueTransform: (v) => v.descricao,
				},
				{
					nome: "qtdeBalanco",
					titulo: this.columnQuantidadeBalanco,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "qtdeEstoqueAnterior",
					titulo: this.columnQuantidadeAnterior,
					visible: this.hasId,
					ordenavel: false,
					valueTransform: (v) => (this.qntdAnterior = v),
				},
				{
					nome: "qtdeBalanco",
					titulo: this.columnDiferenca,
					visible: this.hasId,
					ordenavel: false,
					valueTransform: (v) => v - this.qntdAnterior,
				},
			],
			actions: [
				{
					// nome: "Excluir",
					nome: this.traducao.getLabel("nome-excluir"),
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: this.traducao.getLabel("tooltip-excluir"),
					actionFn: (row) => this.deleteBalancoItens(row),
					showIconFn: (row) => !this.hasId,
				},
			],
		});
	}

	addBalancoItem() {
		if (this.formItems.get("produto").value === null) {
			this.notificationService.error(
				this.traducao.getLabel("validacaoProduto")
			);
			return;
		}
		if (
			typeof this.produtoControl.value === "string" &&
			this.produtoControl.value.length === 0
		) {
			this.notificationService.error(
				this.traducao.getLabel("validacaoProduto")
			);
			return;
		}
		if (this.formItems.get("qtdeBalanco").value === null) {
			this.notificationService.error(
				this.traducao.getLabel("validacaoQuantidade")
			);
			return;
		}
		if (this.formItems.get("qtdeBalanco").value === "") {
			this.formItems.get("qtdeBalanco").setValue(0);
		}
		const existsProduto = this.balanco.balancoItens.find(
			(obj) => obj.produto.codigo === this.formItems.get("produto").value.codigo
		);
		if (existsProduto) {
			this.notificationService.error(
				this.traducao.getLabel("validacaoProdutoExistente")
			);
			return;
		}
		console.log(this.formItems.getRawValue());
		this.balanco.balancoItens.push(this.formItems.getRawValue());
		this.formItems.patchValue(new BalancoItem());
		this.sortBalancoItens();
		this.createBalancoItensObject();
	}

	deleteBalancoItens(event) {
		const balancoItemSelecionado = event.row;

		if (balancoItemSelecionado.codigo) {
			this.balanco.balancoItens.splice(event.rowIndex, 1);
			this.tableBalancoItensComponent.reloadData();
			this.createBalancoItensObject();
		} else {
			this.notificationService.success(
				this.traducao.getLabel("itemExcluidoSucesso")
			);
			this.balanco.balancoItens.splice(event.rowIndex, 1);
			this.createBalancoItensObject();
			this.sortBalancoItens();
		}
	}

	sortBalancoItens() {
		this.balanco.balancoItens = this.balanco.balancoItens.sort((a, b) => {
			if (a.codigo > b.codigo) {
				return 1;
			} else if (a.codigo < b.codigo) {
				return -1;
			} else {
				return 0;
			}
		});
	}

	openModal() {
		if (!this.hasId) {
			if (
				this.sessionService.loggedUser.administrador &&
				this.form.get("empresa").value === null
			) {
				this.notificationService.error(
					this.traducao.getLabel("validacaoEmpresa")
				);
				return;
			} else {
				const dialogRef = this.dialogService.open(
					this.traducao.getLabel("modalProdutoCategoria"),
					BalancoModalComponent,
					PactoModalSize.LARGE
				);
				if (this.form.get("empresa").value) {
					dialogRef.componentInstance.codigoEmpresa =
						this.form.get("empresa").value.codigo;
				} else {
					dialogRef.componentInstance.codigoEmpresa =
						this.sessionService.empresaId;
				}
				if (dialogRef.result) {
					dialogRef.result
						.then((value) => {
							this.formItems.get("produto").setValue(value);
							this.produtoControl.setValue(value.descricao);
						})
						.catch((error) => {});
				}
				this.cd.detectChanges();
			}
		} else {
			return;
		}
	}

	empresaSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	initForms() {
		this.form = new FormGroup({
			empresa: new FormControl(),
			descricao: new FormControl(),
			dataCadastro: new FormControl(new Date()),
			observacoes: new FormControl(),
		});
		this.formItems = new FormGroup({
			produto: new FormControl(),
			qtdeBalanco: new FormControl(),
			qtdeestoqueanterior: new FormControl(),
		});
	}

	voltarListagem() {
		this.router.navigate(["adm", "produtos", "balanco"]);
	}

	novo() {
		if (this.permiteCadastrarBalanco) {
			this.router.navigate(["adm", "produtos", "novo-balanco"]);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("permissao-cadastrar-balanco")
			);
		}
	}

	salvar() {
		if (this.permiteCadastrarBalanco) {
			Object.keys(this.form.getRawValue()).forEach((key) => {
				this.balanco[key] = this.form.getRawValue()[key];
			});
			if (this.balanco.empresa == null) {
				const empresaUnica = this.Empresas.find(
					(obj) => obj.codigo == this.sessionService.empresaId
				);
				this.balanco.empresa = empresaUnica;
			}

			this.service.save(this.balanco).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("salvoSucesso")
					);
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("permissao-cadastrar-balanco")
			);
		}
	}

	cancelarBalanco() {
		if (
			this.permiteCancelarBalanco ||
			this.sessionService.loggedUser.administrador
		) {
			Object.keys(this.form.getRawValue()).forEach((key) => {
				this.balanco[key] = this.form.getRawValue()[key];
			});

			this.service.cancelar(this.balanco).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("canceladoSucesso")
					);
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("permissao-cancelar-balanco")
			);
		}
	}

	loadEmpresas() {
		this.service.getEmpresas().subscribe((response: any) => {
			this.Empresas = response.content;
		});
	}

	changePageBalancoItens(page) {
		this.page = page;
		this.createBalancoItensObject(page);
	}

	changePageSizeRespostaPergunta(size) {
		this.createBalancoItensObject(this.page, size);
	}

	clonarBalanco() {
		if (this.permiteCadastrarBalanco) {
			Object.keys(this.form.getRawValue()).forEach((key) => {
				this.balanco[key] = this.form.getRawValue()[key];
			});

			if (this.balanco.empresa == null) {
				this.balanco.empresa = this.Empresas.find(
					(obj) => obj.codigo == this.sessionService.empresaId
				);
			}

			this.service.clonar(this.balanco).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("clonadoSucesso")
					);
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("permissao-cadastrar-balanco")
			);
		}
	}

	prepararClonagemBalanco() {
		if (this.permiteCadastrarBalanco) {
			this.hasId = false;
			this.clonar = true;
			this.changeFormState(this.hasId);
			this.initTableBalancoItens();
			this.sortBalancoItens();
			this.createBalancoItensObject();
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("permissao-cadastrar-balanco")
			);
		}
	}

	imprimirBalanco() {
		this.service.imprimir(this.id).subscribe((response) => {
			const url = response.content;
			window.open(url);
		});
	}
}
