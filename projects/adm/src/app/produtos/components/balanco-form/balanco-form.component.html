<adm-layout
	i18n-pageTitle="@@balanco-pagetitle"
	pageTitle="Balanço"
	i18n-modulo="@@balanco-modulo"
	modulo="Administrativo"
	(goBack)="voltarListagem()"
	i18n-subtitle="@@balanco-form-subtitle"
	subtitle="Informe os dados abaixo">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<div class="row">
				<div
					*ngIf="exibirEmpresa"
					class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-select-filter
						id="balanco-select-empresa"
						i18n-label="@@balanco-empresa-label"
						label="Empresa"
						[control]="form.get('empresa')"
						i18n-errorMsg="@@balanco-empresa-error"
						errorMsg="Selecione uma empresa"
						[paramBuilder]="empresaSelectBuilder"
						idKey="codigo"
						labelKey="nome"
						[options]="Empresas"></pacto-cat-form-select-filter>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-input
						id="balanco-input-descricao"
						[control]="form.get('descricao')"
						i18n-label="@@balanco-descricao-label"
						label="Descrição*"
						i18n-placeholder="@@balanco-descricao-placeholder"
						placeholder="Descrição"></pacto-cat-form-input>
				</div>
			</div>
			<div class="row">
				<pacto-cat-form-datepicker
					id="balanco-datepicker-data"
					[control]="form.get('dataCadastro')"
					class="left-space data"
					i18n-label="@@balanco-data-cadastro-label"
					i18n-placeholder="@@balanco-data-cadastro-placeholder"
					label="Data balanço"
					placeholder="Data balanço"></pacto-cat-form-datepicker>
			</div>
			<div class="row">
				<pacto-cat-form-textarea
					id="balanco-text-observacoes"
					[control]="form.get('observacoes')"
					i18n-label="@@balanco-observacoes-label"
					label="Observações"
					class="left-space obs-textarea"></pacto-cat-form-textarea>
			</div>
			<div class="row">
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-input
						id="balanco-input-produtos"
						i18n-label="@@balanco-produtos-label"
						label="Produtos*"
						(click)="openModal()"
						[readonly]="true"
						[control]="produtoControl"></pacto-cat-form-input>
				</div>

				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						id="balanco-input-quantidade"
						[formControl]="formItems.get('qtdeBalanco')"
						i18n-label="@@balanco-qtde-label"
						label="Quantidade"
						placeholder="000"></pacto-cat-form-input-number>
				</div>
				<div
					*ngIf="!hasId"
					class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 d-flex align-items-center">
					<div
						(click)="addBalancoItem()"
						class="add-item cor-azulim-pri"
						id="balanco-btn-adicionar">
						<i class="pct pct-plus-circle"></i>
						<span style="margin-left: 4px">Adicionar</span>
					</div>
				</div>
			</div>
			<div class="table-wrapper">
				<pacto-relatorio
					#tableBalancoItensComponent
					[table]="tableBalancoItens"
					[showShare]="false"
					(iconClick)="deleteBalancoItens($event)"
					(pageChangeEvent)="changePageBalancoItens($event)"
					(pageSizeChange)="changePageSizeRespostaPergunta($event)"
					idSuffix="balanco-itens"
					telaId="balancoItens"></pacto-relatorio>
			</div>
			<div class="row justify-content-end">
				<pacto-cat-button
					type="OUTLINE_DARK"
					i18n-label="@@balanco:btn-cancelar"
					label="Cancelar"
					style="margin-right: 10px"
					(click)="voltarListagem()"></pacto-cat-button>

				<pacto-cat-button
					(click)="cancelarBalanco()"
					*ngIf="!cancelado && hasId"
					i18n-label="@@balanco:btn-cancelarBalanco"
					label="Cancelar balanço"
					style="margin-right: 10px"
					type="OUTLINE_RED"></pacto-cat-button>

				<pacto-cat-button
					(click)="prepararClonagemBalanco()"
					*ngIf="hasId"
					i18n-label="@@balanco:btn-clonarBalanco"
					label="Clonar balanço"
					style="margin-right: 10px"
					type="OUTLINE_RED"></pacto-cat-button>

				<pacto-cat-button
					(click)="imprimirBalanco()"
					*ngIf="hasId"
					i18n-label="@@balanco:btn-imprimir"
					label="Imprimir balanço"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>

				<pacto-cat-button
					(click)="novo()"
					*ngIf="hasId"
					i18n-label="@@balanco:btn-novo"
					label="Novo"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>

				<!-- salvar balanço -->
				<pacto-cat-button
					(click)="salvar()"
					*ngIf="!hasId && !clonar"
					i18n-label="@@balanco:btn-salvar"
					label="Salvar"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>

				<!-- clonar balanço -->
				<pacto-cat-button
					(click)="clonarBalanco()"
					*ngIf="clonar"
					i18n-label="@@balanco:btn-salvar"
					label="Salvar"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<ng-template #columnProduto>
	<span i18n="@@balancoItem:column-produto">Produto</span>
</ng-template>

<ng-template #columnQuantidadeBalanco>
	<span i18n="@@balancoItem:column-quantidadeBalanco">Quantidade balanço</span>
</ng-template>

<ng-template #columnQuantidadeAnterior>
	<span i18n="@@balancoItem:column-quantidadeAnterior">Estoque anterior</span>
</ng-template>

<ng-template #columnDiferenca>
	<span i18n="@@balancoItem:column-diferenca">Diferença balanço</span>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@balancoItem:btn-excluir" xingling="nome-excluir">Excluir</span>
	<span i18n="@@balanco:action-excluir" xingling="tooltip-excluir">
		Excluir uma opção
	</span>
	<span i18n="@@balanco:validacao-produto" xingling="validacaoProduto">
		Selecione um produto
	</span>
	<span
		i18n="@@balanco:validacao-produto-existente"
		xingling="validacaoProdutoExistente">
		Este produto já foi adicionado ao balanço
	</span>
	<span i18n="@@balanco:validacao-quantidade" xingling="validacaoQuantidade">
		informe uma quantidade
	</span>
	<span i18n="@@balanco:item-exlcuido-sucesso" xingling="itemExcluidoSucesso">
		Item excluído com sucesso
	</span>
	<span i18n="@@balanco-validacao-empresa" xingling="validacaoEmpresa">
		Selecione uma empresa
	</span>
	<span
		i18n="@@balanco:modal-produto-categoria"
		xingling="modalProdutoCategoria">
		Produto / Categoria
	</span>
	<span i18n="@@balanco:salvo-sucesso" xingling="salvoSucesso">
		Salvo com sucesso!
	</span>
	<span i18n="@@balanco:cancelado-sucesso" xingling="canceladoSucesso">
		Balanço cancelado com sucesso!
	</span>
	<span i18n="@@balanco:clonado-sucesso" xingling="clonadoSucesso">
		Balanço clonado com sucesso!
	</span>
	<span
		i18n="@@balanco:permissao-cadastrar-balanco"
		xingling="permissao-cadastrar-balanco">
		Seu usuário não possui a permissão: 12.03 - Balanço
	</span>
	<span
		i18n="@@balanco:permissao-cancelar-balanco"
		xingling="permissao-cancelar-balanco">
		Seu usuário não possui a permissão: 12.04 - Cancelar um balanço
	</span>
</pacto-traducoes-xingling>
