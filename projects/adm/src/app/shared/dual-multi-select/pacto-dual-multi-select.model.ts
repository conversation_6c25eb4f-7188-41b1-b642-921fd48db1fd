export class PactoDualMultiSelectConfig {
	data?: any;
	id: string;
	value: string;
	selectedData?: any;
	// endpointUrl?: string;
	constructor(config: PactoDualMultiSelectConfig) {
		this.data = config.data ? config.data : null;
		this.id = config.id;
		this.value = config.value;
		this.selectedData = config.selectedData;
		// this.endpointUrl = config.endpointUrl ? config.endpointUrl : null;
	}
}

export interface PactoDualMultiSelectConfig {
	data?: any;
	id: string;
	value: string;
	selectedData?: any;
	// endpointUrl?: string;
}
