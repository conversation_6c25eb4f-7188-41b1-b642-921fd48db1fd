<pacto-cat-layout-v2>
	<div class="dual-select">
		<div class="dual-select__left">
			<ul class="dual-select__items">
				<li
					*ngFor="let data of unselectedData"
					(click)="togglePendingSelection(data)"
					(dblclick)="addToSelectedData(data)"
					class="dual-select__item"
					[class.dual-select__item--selected]="pendingSelection[data.id]">
					<div class="data">
						<div class="data__value">
							{{ data.value }}
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="dual-select__controls">
			<button (click)="addToSelectedData()" class="dual-select__control">
				<i class="pct pct-chevron-right"></i>
			</button>

			<button (click)="removeFromSelectedData()" class="dual-select__control">
				<i class="pct pct-chevron-left"></i>
			</button>
		</div>
		<div class="dual-select__right">
			<ul class="dual-select__items">
				<li
					*ngFor="let data of selectedData"
					(click)="togglePendingSelection(data)"
					(dblclick)="removeFromSelectedData(data)"
					class="dual-select__item dual-select__item--new"
					[class.dual-select__item--selected]="pendingSelection[data.id]">
					<div class="data">
						<div class="data__value">
							{{ data.value }}
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<div class="area-button">
		<pacto-cat-button
			[type]="'PRIMARY_NO_TEXT_TRANSFORM'"
			[label]="'Selecionar'"
			(click)="enviar()"
			[size]="'LARGE'"></pacto-cat-button>
	</div>
</pacto-cat-layout-v2>
<!--<p class="note">
  You have
  <strong>{{ selectedData.length }} of {{ data.length }}</strong>
  datas selected.
</p>-->
