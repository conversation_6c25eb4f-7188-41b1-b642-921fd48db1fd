import { Injectable, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { CatAccordionComponent } from "./cat-accordion/cat-accordion.component";
import { CatExpansionPanelComponent } from "./cat-accordion/cat-expansion-panel/cat-expansion-panel.component";
import { CatExpansionHeaderComponent } from "./cat-accordion/cat-expansion-panel/cat-expansion-header/cat-expansion-header.component";
import { TranslateModule } from "@ngx-translate/core";
import { ReactiveFormsModule } from "@angular/forms";
import { OverlayModule } from "@angular/cdk/overlay";
import {
	DateAdapter,
	MatDatepickerModule,
	MatFormFieldModule,
	NativeDateAdapter,
} from "@angular/material";
import { TextMaskModule } from "angular2-text-mask";
import { UiModule } from "ui-kit";
import { CatIconModule } from "./cat-icon/cat-icon.module";

@Injectable()
export class AppDateAdapter extends NativeDateAdapter {
	parse(value: string) {
		const it = value.split("/");
		if (it.length === 3) {
			return new Date(+it[2], +it[1] - 1, +it[0], 12);
		}
		return super.parse(value);
	}
}

@NgModule({
	declarations: [
		CatAccordionComponent,
		CatExpansionPanelComponent,
		CatExpansionHeaderComponent,
	],
	exports: [
		CatAccordionComponent,
		CatExpansionPanelComponent,
		CatExpansionHeaderComponent,
		CatIconModule,
	],
	imports: [
		CommonModule,
		ReactiveFormsModule,
		UiModule,
		TranslateModule,
		OverlayModule,
		MatDatepickerModule,
		TextMaskModule,
		MatFormFieldModule,
		CatIconModule,
	],
	providers: [{ provide: DateAdapter, useClass: AppDateAdapter }],
})
export class AdmUiModule {}
