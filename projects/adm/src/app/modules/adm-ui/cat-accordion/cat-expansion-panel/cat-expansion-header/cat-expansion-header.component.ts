import {
	Component,
	ElementRef,
	EventEmitter,
	HostBinding,
	HostListener,
	Input,
	OnInit,
} from "@angular/core";

let uniqueId = 0;

@Component({
	selector: "adm-cat-expansion-header",
	templateUrl: "./cat-expansion-header.component.html",
	styleUrls: ["./cat-expansion-header.component.scss"],
})
export class CatExpansionHeaderComponent implements OnInit {
	@HostBinding("id")
	@Input()
	id: string;
	opened: EventEmitter<boolean> = new EventEmitter<boolean>();

	@HostBinding("class.opened")
	isOpen = false;

	constructor(private elementRef: ElementRef) {}

	ngOnInit() {
		if (!this.id) {
			this.id = `cat-expansion-header-${uniqueId++}`;
		}
	}

	@HostListener("click")
	onClick() {
		this.isOpen = !this.isOpen;
		this.opened.emit(this.isOpen);
	}
}
