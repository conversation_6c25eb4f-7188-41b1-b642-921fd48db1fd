import {
	AfterContentInit,
	OnChanges,
	Component,
	ContentChildren,
	EventEmitter,
	HostBinding,
	Input,
	OnInit,
	Output,
	QueryList,
	SimpleChanges,
} from "@angular/core";
import { CatExpansionHeaderComponent } from "./cat-expansion-header/cat-expansion-header.component";

let uniqueId = 0;

@Component({
	selector: "adm-cat-expansion-panel",
	templateUrl: "./cat-expansion-panel.component.html",
	styleUrls: ["./cat-expansion-panel.component.scss"],
})
export class CatExpansionPanelComponent
	implements OnInit, AfterContentInit, OnChanges
{
	@HostBinding("id")
	@Input()
	id: string;
	@Input()
	@HostBinding("class.expanded")
	expanded = false;
	@Output() opened: EventEmitter<boolean> = new EventEmitter<boolean>();

	@ContentChildren(CatExpansionHeaderComponent)
	headers: QueryList<CatExpansionHeaderComponent>;

	constructor() {}

	ngOnInit() {
		if (!this.id) {
			this.id = `cat-expansion-panel-${uniqueId++}`;
		}
	}

	ngAfterContentInit() {
		if (this.headers) {
			this.headers.forEach((header) => {
				header.opened.subscribe((value) => {
					this.expanded = value;
					this.opened.emit(value);
				});
				if (this.expanded) {
					header.isOpen = true;
					this.opened.emit(true);
				}
			});
		}
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes.expanded && changes.expanded.currentValue) {
			this.expanded = changes.expanded.currentValue;
			this.opened.emit(changes.expanded.currentValue);
			if (this.headers) {
				this.headers.forEach(
					(header) => (header.isOpen = changes.expanded.currentValue)
				);
			}
		}
	}
}
