:host {
	background: white;
	width: 100%;
	box-shadow: 0 4px 4px #e4e5e6;
	display: block;
	border-radius: 8px;
	margin-bottom: 24px;

	&:last-child {
		margin-bottom: 0;
	}

	.pct-expansion-panel-content {
		overflow: hidden;
		max-height: 0;
		-webkit-transition: max-height 0.6s;
		-moz-transition: max-height 0.6s;
		-ms-transition: max-height 0.6s;
		-o-transition: max-height 0.6s;
		transition: max-height 0.6s;

		.accordion-inner {
			padding: 0 32px 32px 32px;
		}
	}

	&.expanded {
		.pct-expansion-panel-content {
			max-height: 100%;
		}
	}
}
