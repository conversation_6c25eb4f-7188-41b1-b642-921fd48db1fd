import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { BiComponent } from "./bi.component";

const routes: Routes = [
	{
		path: "",
		component: BiComponent,
		children: [
			//<START REMOVE> de inicio vai ter apenas adm, quando for criado telas de bi para outros modulos é pra apagar
			{
				path: "",
				redirectTo: "adm",
			},
			//<END REMOVE>
			{
				path: "adm",
				loadChildren: () =>
					import("./bi-adm/bi-adm.module").then((m) => m.BiAdmModule),
			},
			{
				path: "agenda",
				loadChildren: () =>
					import("./bi-agenda/bi-agenda.module").then((m) => m.BiAgendaModule),
			},
			{
				path: "avaliacao-fisica",
				loadChildren: () =>
					import("./bi-avaliacao-fisica/bi-avaliacao-fisica.module").then(
						(m) => m.BiAvaliacaoFisicaModule
					),
			},
			{
				path: "crm",
				loadChildren: () =>
					import("./bi-crm/bi-crm.module").then((m) => m.BiCrmModule),
			},
			{
				path: "cross",
				loadChildren: () =>
					import("./bi-cross/bi-cross.module").then((m) => m.BiCrossModule),
			},
			{
				path: "financeiro",
				loadChildren: () =>
					import("./bi-financeiro/bi-financeiro.module").then(
						(m) => m.BiFinanceiroModule
					),
			},
			{
				path: "pacto-pay",
				loadChildren: () =>
					import("./bi-pactopay/bi-pactopay.module").then(
						(m) => m.BiPactopayModule
					),
			},
			{
				path: "treino",
				loadChildren: () =>
					import("./bi-treino/bi-treino.module").then((m) => m.BiTreinoModule),
			},
		],
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class BiRoutingModule {}
