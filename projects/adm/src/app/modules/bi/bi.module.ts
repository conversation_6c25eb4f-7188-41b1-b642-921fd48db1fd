import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ComponentsModule, UiModule } from "ui-kit";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { SdkModule } from "sdk";

import { BiRoutingModule } from "./bi-routing.module";
import { LayoutModule } from "../../layout/layout.module";
import { BiComponent } from "./bi.component";
import { MatDialogModule } from "@angular/material";

@NgModule({
	declarations: [BiComponent],
	imports: [
		CommonModule,
		SdkModule,
		LayoutModule,
		UiModule,
		NgbModule,
		ComponentsModule,
		BiRoutingModule,
		MatDialogModule,
	],
})
export class BiModule {}
