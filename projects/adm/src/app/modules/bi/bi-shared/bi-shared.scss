@import "dist/ui-kit/assets/ds3/fonts/fonts";

.inDevelopment {
	display: flex;
	flex-direction: column;
	align-items: center;
	align-content: center;
	text-align: center;
	gap: 14px;

	i.pct {
		width: 40px;
		height: 40px;
		font-size: 40px;
	}
}

.bi-content {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 1.5rem;

	.bi-first-col,
	.bi-second-col {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}
}

.bi-card {
	break-inside: avoid;
	background-color: var(--color-background-plane-2);
	padding: 16px;
	border-radius: 4px;
	border: 1px solid var(--color-support-gray-3);
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.35);
	-moz-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.35);
	-o-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.35);
	-webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.35);

	&-header {
		display: flex;
		justify-content: space-between;
		flex-direction: row;
		padding-bottom: 8px;

		&-label {
			display: flex;
			align-items: center;
			flex-direction: row;

			gap: 8px;

			&-title {
				@extend .pct-title4;
				color: var(--color-typography-default-title);
			}

			&-button {
			}
		}

		&-actions {
			display: flex;
			align-items: center;
			flex-direction: row;
			gap: 8px;
		}
	}

	&-content {
		ds3-diviser {
			margin: 8px 0;
		}

		&-filters {
			background-color: var(--color-background-plane-3);
			border-radius: 8px;
			padding: 8px 16px;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 8px;
			margin: 8px 0px;

			&-title {
				@extend .pct-title5;
				color: var(--color-typography-default-title);
			}
		}

		&-section {
			ds3-diviser {
				margin: 8px 0;
			}

			&-header {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;

				&-title {
					@extend .pct-title5;
					color: var(--color-typography-default-title);
				}

				&-info {
					@extend .pct-overline2-regular;
					color: var(--color-typography-default-text);
				}
			}

			&-content {
				padding: 8px 0px;
				width: 100%;

				&-65-35 {
					display: grid;
					grid-template-columns: 13fr 7fr;
					gap: 16px;
				}

				&-header {
					display: flex;
					width: 100%;
					margin-bottom: 8px;

					.bi-card-content-section-header-info {
						margin-left: auto;
					}
				}

				&-title {
					@extend .pct-title5;
					color: var(--color-typography-default-title);
					display: flex;
					justify-content: space-between;
					align-items: center;
				}

				&-list {
					padding: 16px 0px;

					&-header {
						display: flex;
						justify-content: space-evenly;
						padding: 22px 16px;
						text-align: center;
						border-bottom: 1px solid var(--color-support-gray-2);

						&-cell {
							@extend .pct-title5;
							color: var(--color-types-default-title);
							width: calc(1 / 7 * 100%);
							text-align: center;

							&.first {
								width: calc(2 / 7 * 100%);
								text-align: start;
							}
						}
					}

					&-item {
						padding: 0px 16px;
						min-height: 40px;
						display: flex;
						justify-content: flex-end;
						flex-direction: row;
						align-items: center;

						&.hoverable:hover {
							background-color: var(--color-support-gray-1);
						}

						&-text {
							@extend .pct-overline1-regular;
							color: var(--color-typography-default-text);
							width: 100%;
							text-align: start;
						}

						&-value {
							@extend .pct-display7;
							color: var(--color-action-default-able-4);
							width: 15%;
							text-align: center;

							button {
								width: 100%;
							}
						}

						&-percentage {
							@extend .pct-overline2-regular;
							color: var(--color-typography-default-text);
							width: 15%;
							text-align: center;
						}

						&-date {
							@extend .pct-overline2-regular;
							color: var(--color-typography-default-text);
							text-align: center;
							min-width: 15%;
						}

						&-info {
							@extend .pct-overline2-regular;
							color: var(--color-typography-default-text);
							text-align: center;
							width: 15%;
						}

						&:nth-child(odd) {
							background-color: var(--color-background-plane-3);
						}

						&.list-item-odd {
							background-color: var(--color-background-plane-3);
						}

						&-cell.table-item {
							display: flex;
							align-items: center;
							justify-content: space-evenly;

							&-cell {
								@extend .pct-overline2;
								color: var(--color-types-default-text);
								text-align: center;
								width: calc(1 / 7 * 100%);

								&.first {
									text-align: start;
									width: calc(2 / 7 * 100%);
								}
							}
						}

						&-revert-color {
							&:nth-child(odd) {
								background-color: white;
							}

							&:nth-child(even) {
								background-color: var(--color-background-plane-3);
							}
						}
					}

					&-actions {
						display: flex;
						justify-content: flex-end;
						padding: 8px;
						gap: 8px;
					}
				}

				&-flex {
					display: flex;
				}

				.bi-card-content-section-alert {
					display: flex;
					align-items: center;
					padding: 9px 16px;
					border-radius: 5px;
					@extend .pct-body2;

					&.bi-card-content-section-alert-column {
						flex-direction: column;
						align-items: unset;
						justify-content: center;
					}

					&.bi-card-content-section-alert-loss {
						color: var(--color-feedback-loss-2);
						background-color: var(--color-action-default-risk-1);
					}

					&.bi-card-content-section-alert-gain {
						color: var(--color-feedback-gain-3);
						background-color: var(--color-feedback-gain-1);
					}

					&.bi-card-content-section-alert-group {
						.bi-card-content-section-alert-label {
							&:not(:last-of-type) {
								margin-bottom: 16px;
							}
						}
					}

					.bi-card-content-section-alert-label {
						.bi-card-content-section-alert-label-link {
							cursor: pointer;
							&:hover {
								text-decoration: underline;
							}
						}
					}

					.bi-card-content-section-alert-value {
						margin-left: auto;
					}
				}

				.bi-card-content-section-content-action {
					margin-left: auto;
				}
			}
		}

		.bi-card-hint {
			display: flex;
			align-items: center;
			padding: 9px 16px;
			border-radius: 5px;
			@extend .pct-body2;

			&.bi-card-hint-info {
				color: var(--color-feedback-info-2);
				background-color: var(--color-action-default-able-1);
			}
		}

		.bi-card-content-loading {
			display: flex;
			justify-content: center;
			align-items: center;
			height: inherit;
			margin-top: 16px;
		}

		.bi-card-content-actions {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			margin-top: 16px;

			.bi-card-content-actions-start {
				margin-right: auto;
			}

			.bi-card-content-actions-end {
			}
		}
	}

	&.verificacao-clientes {
		.bi-card {
			&-content {
				ds3-bi-info {
					&::ng-deep {
						.info-area {
							height: 100%;
						}
					}
				}

				&-section {
					&-content {
						padding-bottom: 0;

						&:nth-child(2) {
							padding-top: 0;
						}

						&-list {
							padding-bottom: 0;
						}
					}
				}
			}
		}
	}
}

.bi-adm-card-filter {
	padding: var(--spacing-1) var(--spacing-2);
	margin-top: var(--spacing-2);
	margin-bottom: var(--spacing-2);
	width: 100%;

	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: var(--spacing-2);

	form {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		gap: var(--spacing-2);
		flex: 1;

		.bi-adm-data-geral-filter {
			max-width: 201px;
			width: calc(33% - var(--spacing-2));
		}

		.bi-adm-empresa-filter {
			min-width: 341px;
			max-width: max-content;
		}

		.bi-adm-empresa-filter-select::ng-deep {
			.ds3-select {
				.ds3-select-body {
					position: unset;
					width: 100%;
				}
				.ds3-select-value {
					position: unset;
				}
			}
		}
		.bi-adm-colaborador-filter {
			max-width: 336px;
			width: calc(33% - var(--spacing-2));
		}
	}

	.bi-adm-card-filter-buttons {
		margin-left: auto;

		button {
			&:not(:last-of-type) {
				margin-right: var(--spacing-2);
			}
		}
	}
}

.div-empty {
	margin: 5px 0;
	border-radius: 10px;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	width: 100%;
	margin-top: 45px;
	padding: 24px 16px 25px 22px;
	align-items: center;

	.div-interna-empty {
		width: 450px;
		text-align: center;
		justify-content: center;

		.icon-empty {
			width: 112px;
			height: 112px;
		}

		.titulo-empty {
			padding-top: 20px;
			font-size: 14px;
			color: #55585e;
			font-weight: 600;
		}

		.text-empty {
			font-size: 14px;
			color: #797d86;
			font-weight: 400;
			padding-top: 15px;
		}
	}
}

.body-text-empty {
	font-family: Poppins;
	font-size: 14px;
	font-weight: 600;
	line-height: 17.5px;
	letter-spacing: 0.25px;
	text-align: center;
	color: #55585e;
}

.bi-modal-situacao {
	:not(:last-child) {
		margin-right: 8px;
	}
}

.bi-modal-table-loader {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 100%;
	padding: 16px 0 16px 0;

	img {
		width: 100px;
	}
}

.bi-info-no-border {
	padding: 0 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	height: inherit;
	width: 125px;

	.bi-info-no-border-content {
		.bi-info-no-border-value {
			@extend .pct-display3;
			text-align: center;
			display: flex;
			align-items: center;
		}
		.bi-info-no-border-label {
			@extend .pct-overline2;
			text-align: center;
		}
	}
}

.icv-rc-btn-row {
	display: flex;
	justify-content: flex-end;
}

@media (max-width: 768px) {
	.bi-content {
		grid-template-columns: 1fr;
	}
}
