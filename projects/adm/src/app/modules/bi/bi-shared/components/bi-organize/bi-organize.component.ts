import {
	ChangeDetectionStrategy,
	Component,
	ContentChild,
	Directive,
	ElementRef,
	EventEmitter,
	HostBinding,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";

@Directive({
	selector: "[biOrganizeActions]",
})
export class BiOrganizeActionsDirective {}

@Component({
	selector: "adm-bi-organize",
	templateUrl: "./bi-organize.component.html",
	styleUrls: ["./bi-organize.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	encapsulation: ViewEncapsulation.None,
})
export class BiOrganizeComponent implements OnInit {
	@HostBinding("class.bi-organize")
	enableEncapsulation = true;

	@ViewChild("scrollContainer", { static: false })
	scrollContainer: ElementRef<any>;

	@ContentChild(BiOrganizeActionsDirective, { static: false })
	biOrganizeActions: BiOrganizeActionsDirective;

	@Output()
	close: EventEmitter<any> = new EventEmitter<any>();

	@Output()
	cancel: EventEmitter<any> = new EventEmitter<any>();

	@Output()
	backToDefault: EventEmitter<any> = new EventEmitter<any>();

	@Output()
	save: EventEmitter<any> = new EventEmitter<any>();

	constructor() {}

	ngOnInit() {}

	onClose() {
		this.close.emit();
	}

	onCancel() {
		this.cancel.emit();
	}

	onBackToDefault() {
		this.backToDefault.emit();
	}

	onSave() {
		this.save.emit();
	}
}
