import {
	Component,
	OnInit,
	ViewEncapsulation,
	ChangeDetectionStrategy,
	HostBinding,
	ChangeDetectorRef,
	Inject,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { BiMdlAjudaModel } from "../../models/bi-mdl-ajuda.model";
import { HttpClient } from "@angular/common/http";
import { DomSanitizer, SafeHtml } from "@angular/platform-browser";

@Component({
	selector: "adm-bi-mdl-ajuda",
	templateUrl: "./bi-mdl-ajuda.component.html",
	styleUrls: ["./bi-mdl-ajuda.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiMdlAjudaComponent implements OnInit {
	@HostBinding("class.bi-mdl-ajuda")
	enableEncapsulation = true;

	html: SafeHtml;

	private pontoInterrogacaoUrl: string =
		"https://ms1.pactosolucoes.com.br/pontointerrogacao";

	constructor(
		private cd: ChangeDetectorRef,
		private dialog: MatDialogRef<BiMdlAjudaComponent>,
		@Inject(MAT_DIALOG_DATA) public data: BiMdlAjudaModel,
		private domSanitizer: DomSanitizer,
		private http: HttpClient
	) {}

	ngOnInit() {
		this.loadHtml();
	}

	loadHtml() {
		this.http
			.get(`${this.pontoInterrogacaoUrl}/html`, {
				params: {
					url: this.data.url,
					id: this.data.id,
					siglaModulo: this.data.module,
				},
			})
			.subscribe((response: any) => {
				this.html = this.domSanitizer.bypassSecurityTrustHtml(response.content);
				this.cd.detectChanges();
			});
	}

	onClose() {
		this.dialog.close();
	}
}
