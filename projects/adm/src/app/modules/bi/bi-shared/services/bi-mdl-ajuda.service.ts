import { Injectable } from "@angular/core";
import { BiSharedModule } from "../bi-shared.module";
import { MatDialog } from "@angular/material/dialog";
import { BiMdlAjudaComponent } from "../components/bi-mdl-ajuda/bi-mdl-ajuda.component";
import {
	BiMdlAjudaModel,
	ConhecimentoEnum,
} from "../models/bi-mdl-ajuda.model";

@Injectable({
	providedIn: BiSharedModule,
})
export class BiMdlAjudaService {
	constructor(private dialog: MatDialog) {}

	public openMdl(data: BiMdlAjudaModel) {
		this.dialog.open(BiMdlAjudaComponent, {
			data,
			panelClass: "bi-mdl-ajuda-panel",
			position: {
				top: "0",
				right: "0",
			},
			autoFocus: false,
			height: "100%",
			width: "30rem",
		});
	}
}
