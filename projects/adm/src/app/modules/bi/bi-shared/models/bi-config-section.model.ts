import { ListItem } from "@adm/modules/bi/bi-adm/bi-adm.model";
import { ComponentType } from "@angular/cdk/portal";
import { MatDialogConfig } from "@angular/material/dialog/typings/dialog-config";
import { TraducoesXinglingComponent } from "ui-kit";

export interface BiConfigSection {
	key: string;
	id: string;
	textKey: string;
	tooltipKey: string;
	traducoes: TraducoesXinglingComponent;
	modalConfig?: {
		componentType?: ComponentType<any>;
		config?: MatDialogConfig;
	};
	monetary?: boolean;
	listItem?: ListItem;
}

export const createSection = (config: BiConfigSection): [string, ListItem] => [
	config.key,
	{
		...config.listItem,
		id: config.id,
		text: config.traducoes.getLabel(config.textKey),
		value: 0,
		monetary: config.monetary,
		tooltipText: config.traducoes.getLabel(config.tooltipKey),
		modalConfig: config.modalConfig,
	},
];
