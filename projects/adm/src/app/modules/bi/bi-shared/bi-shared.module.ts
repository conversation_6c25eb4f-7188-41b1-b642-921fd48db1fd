import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3RadioGroupModule } from "../../../../../../ui/src/lib/ds3/ds3-radio-group/ds3-radio-group.module";
import {
	BiOrganizeActionsDirective,
	BiOrganizeComponent,
} from "./components/bi-organize/bi-organize.component";
import { Ds3CardModule, Ds3DragAndDropModule, UiModule } from "ui-kit";
import { BiMdlAjudaComponent } from "./components/bi-mdl-ajuda/bi-mdl-ajuda.component";
import { HttpClientModule } from "@angular/common/http";
import { BiModalContentComponent } from "./components/bi-modal-content/bi-modal-content.component";
import { BiSideFilterComponent } from "./components/bi-side-filter/bi-side-filter.component";
import { MatSidenavModule } from "@angular/material/sidenav";
import { PortalModule } from "@angular/cdk/portal";
import { BiSidenavService } from "./services/bi-sidenav/bi-sidenav.service";
import { BiSideFilterBaseContentComponent } from "./components/bi-side-filter-base-content/bi-side-filter-base-content.component";
import { ReactiveFormsModule } from "@angular/forms";
import { Ds3Module } from "../../../../../../ui/src/lib/ds3/ds3.module";

@NgModule({
	declarations: [
		BiOrganizeComponent,
		BiOrganizeActionsDirective,
		BiMdlAjudaComponent,
		BiModalContentComponent,
		BiSideFilterComponent,
		BiSideFilterBaseContentComponent,
	],
	imports: [
		CommonModule,
		HttpClientModule,
		ReactiveFormsModule,
		UiModule,
		Ds3DragAndDropModule,
		Ds3CardModule,
		MatSidenavModule,
		PortalModule,
		Ds3RadioGroupModule,
	],
	providers: [BiSidenavService],
	exports: [
		BiOrganizeComponent,
		BiOrganizeActionsDirective,
		BiMdlAjudaComponent,
		UiModule,
		Ds3DragAndDropModule,
		Ds3CardModule,
		BiModalContentComponent,
		BiSideFilterComponent,
		BiSideFilterBaseContentComponent,
		Ds3RadioGroupModule,
	],
	entryComponents: [BiMdlAjudaComponent],
})
export class BiSharedModule {}
