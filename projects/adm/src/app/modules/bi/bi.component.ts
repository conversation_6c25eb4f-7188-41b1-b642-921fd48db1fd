import { NotificarRecursoEmpresaService } from "@adm/services/notificar-recurso-empresa/notificar-recurso-empresa.service";
import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { Router } from "@angular/router";
import { PermissaoService } from "pacto-layout";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
	selector: "adm-bi",
	templateUrl: "./bi.component.html",
	styleUrls: ["./bi.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiComponent implements OnInit, OnDestroy {
	private _destoyed$: Subject<void> = new Subject<void>();
	private _angularNavigation: boolean = false;
	private _permissaoBIAdm: boolean = false;

	constructor(
		private router: Router,
		private notificarRecursoEmpresaService: NotificarRecursoEmpresaService,
		private cd: ChangeDetectorRef,
		private permissaoService: PermissaoService
	) {
		this._angularNavigation = this.router.getCurrentNavigation() !== null;
	}

	route = [
		{ path: ["/adm"], title: "Administrativo" },
		{ path: ["/adm", "bi"], title: "BI" },
	];
	tabs = [];

	ngOnInit() {
		this._checkBisPermissions();
		this.checkTabsPermission();
		this._notifyRecursoEmpresaNavigation();
	}

	ngOnDestroy() {
		this._destoyed$.next();
	}

	checkTabsPermission() {
		if (this._permissaoBIAdm) {
			this.tabs.push("ADM");
		} // não sei o numero da permissão
		// if (this.permissaoService.temPermissaoAdm('13.08')) { this.tabs.push('Treino'); }
		// if (this.permissaoService.temPermissaoAdm('13.09')) { this.tabs.push('CRM'); }
		// if (this.permissaoService.temPermissaoAdm('13.04')) { this.tabs.push('Financeiro'); }
		// if (true) { this.tabs.push('Agenda'); } // não sei o numero da permissão
		// if (this.permissaoService.temPermissaoAdm('13.06')) { this.tabs.push('Pacto Pay'); }
		// if (this.permissaoService.temPermissaoAdm('13.01')) { this.tabs.push('Avaliação Física'); }
		// if (true) { this.tabs.push('Cross'); } // não sei o numero da permissão

		this.router.navigate(["adm", "home"]);
		this.cd.detectChanges();
	}

	private _checkBisPermissions() {
		this._permissaoBIAdm = this.permissaoService.temPermissaoAdm("2.43");
	}

	changeTab(index) {
		this.router.navigate(["/adm", "bi", this.stringToKebab(this.tabs[index])]);
	}

	stringToKebab(str: string): string {
		return str
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(/\s+/g, "-")
			.replace(/[^\w-]/g, "")
			.toLowerCase();
	}

	private _notifyRecursoEmpresaNavigation() {
		if (!this._angularNavigation) {
			return;
		}

		this.notificarRecursoEmpresaService
			.notifyRecursoEmpresaNavigation("NOVO-BI")
			.pipe(takeUntil(this._destoyed$))
			.subscribe();
	}
}
