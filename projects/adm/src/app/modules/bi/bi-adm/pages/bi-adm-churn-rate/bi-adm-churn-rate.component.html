<ds3-breadcrumbs
	[routeData]="route"
	[displayArrowReturn]="false"></ds3-breadcrumbs>
<section class="bi-churn-rate">
	<h2 class="pct-title4 cor-type-default-text">
		Comparativo dos últimos 3 anos da empresa
	</h2>
	<ds3-chart-line
		[disableAnimations]="true"
		[series]="chartOptions.series"
		[xaxis]="chartOptions.xaxis"
		[color]="chartOptions.color"></ds3-chart-line>
	<ds3-bi-info [infoData]="infoItens"></ds3-bi-info>
	<h2 class="pct-title4 cor-type-default-text">Custumer Churn Rate</h2>
	<p class="pct-body1">
		É quantidade de contratos cancelados e finalizados (data final do contrato)
		dentro do período pesquisado (Exceto os renovados), dividida pelos contratos
		ativos no início do mês + as matrículas, rematrículas e contratos
		transferidos do período. Os níveis do Churn Rate estão em:
		<br />
		<span class="cor-feedback-gain02">ATÉ 3% = NORMAL</span>
		 | 
		<span class="cor-feedback-alert02">ATÉ 8% = PERIGO</span>
		 | 
		<span class="cor-feedback-loss02">ACIMA DE 8% = ALERTA MÁXIMO</span>
		.
	</p>
	<pacto-relatorio
		[table]="tableChurnRate"
		[isSideFilter]="true"
		[enableDs3]="true"
		[showShare]="false"
		[actionTitulo]="'Ações'"></pacto-relatorio>
</section>

<ng-template #churnJanCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.jan)">{{ item?.jan }}%</strong>
</ng-template>
<ng-template #churnFebCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.fev)">{{ item?.fev }}%</strong>
</ng-template>
<ng-template #churnMarCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.mar)">{{ item?.mar }}%</strong>
</ng-template>
<ng-template #churnAprCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.abr)">{{ item?.abr }}%</strong>
</ng-template>
<ng-template #churnMayCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.mai)">{{ item?.mai }}%</strong>
</ng-template>
<ng-template #churnJunCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.jun)">{{ item?.jun }}%</strong>
</ng-template>
<ng-template #churnJulCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.jul)">{{ item?.jul }}%</strong>
</ng-template>
<ng-template #churnAugCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.ago)">{{ item?.ago }}%</strong>
</ng-template>
<ng-template #churnSepCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.set)">{{ item?.set }}%</strong>
</ng-template>
<ng-template #churnOctCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.out)">{{ item?.out }}%</strong>
</ng-template>
<ng-template #churnNovCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.nov)">{{ item?.nov }}%</strong>
</ng-template>
<ng-template #churnDecCell let-item="item">
	<strong [class]="getColorBasedOnValue(item?.dez)">{{ item?.dez }}%</strong>
</ng-template>
