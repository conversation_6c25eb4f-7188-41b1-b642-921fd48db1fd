import {
	ChangeDetectionStrategy,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "adm-bi-adm-churn-rate",
	templateUrl: "./bi-adm-churn-rate.component.html",
	styleUrls: ["./bi-adm-churn-rate.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiAdmChurnRateComponent implements OnInit {
	@ViewChild("churnJanCell", { static: true }) churnJanCell;
	@ViewChild("churnFebCell", { static: true }) churnFebCell;
	@ViewChild("churnMarCell", { static: true }) churnMarCell;
	@ViewChild("churnAprCell", { static: true }) churnAprCell;
	@ViewChild("churnMayCell", { static: true }) churnMayCell;
	@ViewChild("churnJunCell", { static: true }) churnJunCell;
	@ViewChild("churnJulCell", { static: true }) churnJulCell;
	@ViewChild("churnAugCell", { static: true }) churnAugCell;
	@ViewChild("churnSepCell", { static: true }) churnSepCell;
	@ViewChild("churnOctCell", { static: true }) churnOctCell;
	@ViewChild("churnNovCell", { static: true }) churnNovCell;
	@ViewChild("churnDecCell", { static: true }) churnDecCell;
	route = [
		{ path: ["/adm"], title: "Administrativo" },
		{ path: ["/adm", "bi"], title: "BI" },
		{ path: ["/adm", "bi", "churn-rate"], title: "Churn Rate" },
	];

	formControl = new FormControl(new Date());

	tableChurnRate: PactoDataGridConfig;

	infoItens = [
		{
			info: {
				value: 5,
				size: "24px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "disabled",
			},
			auxiliary: undefined,
			textButton: undefined,
			overline: {
				avatarImage: undefined,
				dotHexColor: undefined,
				link: undefined,
				text: "Mês atual",
			},
		},
		{
			info: {
				value: 0.5,
				size: "24px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "disabled",
			},
			auxiliary: {
				value: 5.5,
				size: "12px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "gain",
			},
			textButton: undefined,
			overline: {
				avatarImage: undefined,
				dotHexColor: undefined,
				link: undefined,
				text: "Mês anterior",
			},
		},
		{
			info: {
				value: 0.5,
				size: "24px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "disabled",
			},
			auxiliary: {
				value: 5.5,
				size: "12px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "gain",
			},
			textButton: undefined,
			overline: {
				avatarImage: undefined,
				dotHexColor: undefined,
				link: undefined,
				text: "6 meses",
			},
		},
		{
			info: {
				value: 0.5,
				size: "24px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "disabled",
			},
			auxiliary: {
				value: 5.5,
				size: "12px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: true,
				state: "gain",
			},
			textButton: undefined,
			overline: {
				avatarImage: undefined,
				dotHexColor: undefined,
				link: undefined,
				text: "12 meses",
			},
		},
	];

	chartOptions = {
		series: [
			{
				name: "2022",
				data: [69, 35, 51, 49, 22, 69, 10, 75, 35, 51, 49, 22],
			},
			{
				name: "2023",
				data: [10, 41, 35, 12, 49, 44, 69, 35, 51, 49, 22, 69],
			},
			{
				name: "2024",
				data: [10, 20, 35, 51, 49, 22, 96, 51, 49, 62, 88, 69],
			},
		],
		xaxis: {
			categories: [
				"Jan",
				"Fev",
				"Mar",
				"Abr",
				"Mai",
				"Jun",
				"Jul",
				"Ago",
				"Set",
				"Out",
				"Nov",
				"Dez",
			],
		},
		color: "primary",
	};

	constructor() {}

	ngOnInit() {
		this.initTableChurnRate();
	}

	private initTableChurnRate() {
		this.tableChurnRate = new PactoDataGridConfig({
			// endpointUrl: this.admRest.buildFullUrl('/pacote', false, Api.MSPLANO),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return {
					content: [
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
						{
							nome: "Nome da empresa",
							jan: 2,
							fev: 2,
							mar: 3,
							abr: 4,
							mai: 5,
							jun: 6,
							jul: 7,
							ago: 8,
							set: 9,
							out: 10,
							nov: 11,
							dez: 12,
						},
					],
					// content: [],
					first: true,
					last: false,
					number: 0,
					size: 10,
					totalElements: 10,
					totalPages: 1,
				};
			},
			columns: [
				{ nome: "nome", titulo: "Nome", visible: true, ordenavel: false },
				{
					nome: "jan",
					styleClass: "right",
					titulo: "JAN",
					visible: true,
					ordenavel: true,
					celula: this.churnJanCell,
				},
				{
					nome: "fev",
					styleClass: "right",
					titulo: "FEV",
					visible: true,
					ordenavel: true,
					celula: this.churnFebCell,
				},
				{
					nome: "mar",
					styleClass: "right",
					titulo: "MAR",
					visible: true,
					ordenavel: true,
					celula: this.churnMarCell,
				},
				{
					nome: "abr",
					styleClass: "right",
					titulo: "ABR",
					visible: true,
					ordenavel: true,
					celula: this.churnAprCell,
				},
				{
					nome: "mai",
					styleClass: "right",
					titulo: "MAI",
					visible: true,
					ordenavel: true,
					celula: this.churnMayCell,
				},
				{
					nome: "jun",
					styleClass: "right",
					titulo: "JUN",
					visible: true,
					ordenavel: true,
					celula: this.churnJunCell,
				},
				{
					nome: "jul",
					styleClass: "right",
					titulo: "JUL",
					visible: true,
					ordenavel: true,
					celula: this.churnJulCell,
				},
				{
					nome: "ago",
					styleClass: "right",
					titulo: "AGO",
					visible: true,
					ordenavel: true,
					celula: this.churnAugCell,
				},
				{
					nome: "set",
					styleClass: "right",
					titulo: "SET",
					visible: true,
					ordenavel: true,
					celula: this.churnSepCell,
				},
				{
					nome: "out",
					styleClass: "right",
					titulo: "OUT",
					visible: true,
					ordenavel: true,
					celula: this.churnOctCell,
				},
				{
					nome: "nov",
					styleClass: "right",
					titulo: "NOV",
					visible: true,
					ordenavel: true,
					celula: this.churnNovCell,
				},
				{
					nome: "dez",
					styleClass: "right",
					titulo: "DEZ",
					visible: true,
					ordenavel: true,
					celula: this.churnDecCell,
				},
			],
			actions: [
				{
					nome: "BI",
					iconClass: "pct pct-bi cor-action-default-able04 right",
					tooltipText: "BI",
					actionFn: (row) => this.navigateToBI(row),
				},
			],
		});
	}

	navigateToBI(item: any) {}

	getColorBasedOnValue(value) {
		if (value < 3) {
			return "cor-feedback-gain02";
		}
		if (value < 8) {
			return "cor-feedback-alert02";
		}
		if (value >= 8) {
			return "cor-feedback-loss02";
		}
	}
}
