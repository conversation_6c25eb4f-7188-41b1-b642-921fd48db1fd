import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from "@angular/material";

@Component({
	selector: "adm-modal-relatorio-alunos",
	templateUrl: "./modal-relatorio-alunos.component.html",
	styleUrls: ["./modal-relatorio-alunos.component.scss"],
})
export class ModalRelatorioAlunosComponent implements OnInit {
	constructor(
		@Inject(MAT_DIALOG_DATA) public data: any,
		public dialogRef: MatDialogRef<ModalRelatorioAlunosComponent>,
		public dialog: MatDialog
	) {}

	ngOnInit() {}

	closeModal() {
		this.dialogRef.close();
	}
}
