import {
	AfterViewInit,
	Component,
	Inject,
	OnInit,
	ViewChild,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "adm-bi-modal-ltv-infos",
	templateUrl: "./bi-modal-ltv-infos.component.html",
	styleUrls: ["./bi-modal-ltv-infos.component.scss"],
})
export class BiModalLtvInfosComponent implements OnInit, AfterViewInit {
	@ViewChild("traducoes", { static: false })
	traducoes: TraducoesXinglingComponent;

	modalTitle: string;

	constructor(
		public dialog: MatDialogRef<BiModalLtvInfosComponent>,
		@Inject(MAT_DIALOG_DATA)
		public data: { type: "lt" | "ltv" | "cac" | "churn"; filters: any }
	) {}

	ngOnInit() {
		if (this.data) {
			switch (this.data.type) {
				case "lt":
					this.modalTitle = "LT";
					break;
				case "cac":
					this.modalTitle = "CAC";
					break;
				case "ltv":
					this.modalTitle = "LTV";
					break;
				case "churn":
					this.modalTitle = "Churn rate";
					break;
			}
		}
	}

	ngAfterViewInit() {}
}
