import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from "@angular/material";

@Component({
	selector: "adm-modal-relatorio-indicador",
	templateUrl: "./modal-relatorio-indicador.component.html",
	styleUrls: ["./modal-relatorio-indicador.component.scss"],
})
export class ModalRelatorioIndicadorComponent implements OnInit {
	formGroup: FormGroup;

	public listTabs = [
		{ value: 1, label: "Hoje" },
		{ value: 2, label: "Dia personalizado" },
		{ value: 3, label: "Comparativo anual" },
	];

	public colorOrder = [
		"blue",
		"green",
		"yellow",
		"orange",
		"red",
		"pink",
		"purple",
		"lightblue",
	];

	chartOptionsToday = {
		series: [
			{
				name: "Quantidade de acessos",
				data: [50, 90, 10, 30, 66, 200, 130, 100, 65, 152, 75, 66],
			},
		],
		xaxis: {
			type: "category",
			categories: [
				"8h",
				"9h",
				"10h",
				"11h",
				"12h",
				"13h",
				"14h",
				"15h",
				"16h",
				"17h",
				"18h",
				"19h",
				"20h",
				"21h",
			],
		},
	};
	chartOptionsPersonalizado = {};
	chartOptionsAnual = {};

	constructor(
		@Inject(MAT_DIALOG_DATA) public data: any,
		public dialogRef: MatDialogRef<ModalRelatorioIndicadorComponent>,
		public dialog: MatDialog
	) {
		this.createForm();
	}

	ngOnInit() {
		this.carregarDados(1);
	}

	createForm() {
		this.formGroup = new FormGroup({
			data: new FormControl(new Date()),
		});
	}

	getChartOptions(value: number) {
		switch (value) {
			case 1:
				return this.chartOptionsToday;
			case 2:
				return this.chartOptionsPersonalizado;
			case 3:
				return this.chartOptionsAnual;
			default:
				return null;
		}
	}

	consultar(dataRangeTab: any) {
		if (dataRangeTab) {
			const activeTab = dataRangeTab.tabs.find((tab: any) => tab.active);
			if (activeTab) {
				const selectedTab = this.listTabs.find(
					(tab) => tab.label === activeTab.tabTitle
				);
				if (selectedTab) {
					this.carregarDados(selectedTab.value);
				} else {
					console.warn("Aba selecionada não foi encontrada na lista de tabs.");
				}
			}
		}
	}

	carregarDados(tipoData: number) {
		switch (tipoData) {
			case 1:
				this.carregarDadosHoje();
				break;
			case 2:
				this.carregarDadosPersonalizado();
				break;
			case 3:
				this.carregarDadosAnual();
				break;
			default:
				console.warn("Tipo de dados desconhecido:", tipoData);
		}
	}

	private carregarDadosHoje() {
		this.colorOrder = [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		];
		this.chartOptionsToday;
	}

	private carregarDadosPersonalizado() {
		this.colorOrder = [
			"blue",
			"green",
			"yellow",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		];
		this.chartOptionsPersonalizado = {
			series: [
				{
					name: "Quantidade de acessos",
					data: [8, 9, 10, 84, 66, 124, 76, 131, 117, 152, 84, 66],
				},
			],
			xaxis: {
				type: "category",
				categories: [
					"8h",
					"9h",
					"10h",
					"11h",
					"12h",
					"13h",
					"14h",
					"15h",
					"16h",
					"17h",
					"18h",
					"19h",
					"20h",
					"21h",
				],
			},
		};
	}

	private carregarDadosAnual() {
		this.colorOrder = [
			"yellow",
			"blue",
			"green",
			"orange",
			"red",
			"pink",
			"purple",
			"lightblue",
		];
		this.chartOptionsAnual = {
			series: [
				{
					name: "Ano passado",
					data: [131, 117, 152, 84, 66, 124, 76, 131, 117, 152, 84, 66],
				},
				{
					name: "Ano atual",
					data: [72, 46, 59, 96, 115, 102, 41, 72, 46, 59, 96, 115],
				},
			],
			xaxis: {
				type: "category",
				categories: [
					"Jan",
					"Fev",
					"Mar",
					"Abr",
					"Mai",
					"Jun",
					"Jul",
					"Ago",
					"Set",
					"Out",
					"Nov",
					"Dez",
				],
			},
			colors: ["#2E93fA", "#66DA26", "#546E7A", "#E91E63", "#FF9800"],
		};
	}

	closeModal() {
		this.dialogRef.close();
	}

	trackByIndex(index, item) {
		return index;
	}
}
