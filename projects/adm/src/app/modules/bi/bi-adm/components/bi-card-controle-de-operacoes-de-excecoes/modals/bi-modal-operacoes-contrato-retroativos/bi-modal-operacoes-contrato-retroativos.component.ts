import {
	Component,
	Inject,
	OnD<PERSON>roy,
	OnInit,
	Pipe,
	PipeTransform,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiContratoOperacaoService,
	ApiResponseList,
	FiltroBiControleOperacao,
	OperacoesContratoRetroativo,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Pipe({ name: "tipoOperacaoApresentar" })
export class TipoOperacaoApresentarPipe implements PipeTransform {
	transform(
		tipoOperacao: string,
		dataOperacao: number,
		dataInicioEfetivacaoOperacao: number,
		dataFimEfetivacaoOperacao: number
	): any {
		return this.getTipoOperacao_Apresentar(
			tipoOperacao,
			dataOperacao,
			dataInicioEfetivacaoOperacao,
			dataFimEfetivacaoOperacao
		);
	}

	private getTipoOperacao_Apresentar(
		tipoOperacao: string,
		dataOperacao: number,
		dataInicioEfetivacaoOperacao: number,
		dataFimEfetivacaoOperacao: number
	): string {
		if (!tipoOperacao) {
			tipoOperacao = "";
		}

		const operacoes: { [key: string]: string } = {
			RM: "Rematrícula",
			TS: "Transferência Saída",
			MA: "Matrícula",
			AD: "Alteração Duração",
			RE: "Renovação",
			BA: "Bônus- Acréscimo de dias",
			BR: "Bônus - Redução de dias",
			TE: "Transferência Entrada",
			CA: "Cancelamento",
			AH: "Alteração Horário",
			TR: "Trancamento",
			TV: "Trancamento Vencido",
			RT: "Retorno Trancamento",
			IM: "Incluir Modalidade",
			EM: "Excluir Modalidade",
			AM: "Alterar Modalidade",
			AC: "Alteração Contrato",
			RA: "Retorno - Atestado",
			LV: "Liberação de Vaga",
			BC: "Afastamento Coletivo",
			TD: "Transferência dos Direitos de uso",
			RD: "Retorno dos Direitos de uso",
		};

		if (tipoOperacao in operacoes) {
			return operacoes[tipoOperacao];
		}

		if (tipoOperacao === "CR") {
			if (
				dataOperacao &&
				dataInicioEfetivacaoOperacao &&
				dataFimEfetivacaoOperacao
			) {
				if (
					this.isMaior(dataOperacao, dataInicioEfetivacaoOperacao) &&
					this.isMaior(dataOperacao, dataFimEfetivacaoOperacao)
				) {
					return "Férias (RETROATIVO)";
				}
			}
			return "Férias";
		}

		if (tipoOperacao === "AT") {
			if (
				dataOperacao &&
				dataInicioEfetivacaoOperacao &&
				dataFimEfetivacaoOperacao
			) {
				if (
					this.isMaior(dataOperacao, dataInicioEfetivacaoOperacao) &&
					this.isMaior(dataOperacao, dataFimEfetivacaoOperacao)
				) {
					return "Atestado (RETROATIVO)";
				}
			}
			return "Atestado";
		}

		return tipoOperacao;
	}

	private isMaior(data1: number, data2: number): boolean {
		return data1 > data2;
	}
}

@Component({
	selector: "adm-bi-modal-operacoes-contrato-retroativos",
	templateUrl: "./bi-modal-operacoes-contrato-retroativos.component.html",
	styleUrls: [
		"./bi-modal-operacoes-contrato-retroativos.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalOperacoesContratoRetroativosComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "cliente.pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "cliente",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "cliente.matricula",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "matricula",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "cliente.situacaoApresentar",
			visible: true,
			titulo: "Situação",
			ordenavel: true,
			nome: "situacao",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "contratoOperacao.responsavel.colaborador.pessoa.nome",
			visible: true,
			titulo: "Resp. Lançamento",
			ordenavel: true,
			nome: "reponsavelLancamento",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "contrato.codigo",
			visible: true,
			titulo: "Contrato",
			ordenavel: true,
			nome: "codigoContrato",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "contratoOperacao.tipoOperacaoApresentar",
			visible: true,
			titulo: "Operação",
			ordenavel: true,
			nome: "tipoOperacao",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "contratoOperacao.dataInicioEfetivacaoOperacao",
			visible: true,
			titulo: "Dt. início",
			ordenavel: true,
			nome: "dataInicioEfetivacaoOperacao",
			inputType: "text",
			dateTime: true,
		},
		{
			mostrarTitulo: true,
			campo: "contratoOperacao.dataFimEfetivacaoOperacao",
			visible: true,
			titulo: "Dt. fim",
			ordenavel: true,
			nome: "dataFimEfetivacaoOperacao",
			inputType: "text",
			dateTime: true,
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"contrato-operacao/operacoes-retroativas"
	);

	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalOperacoesContratoRetroativosComponent>,
		private admCoreApiContratoOperacaoService: AdmCoreApiContratoOperacaoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiContratoOperacaoService
			.clientesInativosComPeriodoAcesso({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<OperacoesContratoRetroativo>) => {
					this.totalItems = response.totalElements;
					this._populateCorSituacao(response.content);
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _populateCorSituacao(data: Array<OperacoesContratoRetroativo>) {
		data.forEach((item) => {
			item.corSituacaoCliente = this.biCommonService.corDs3StatusCliente(
				item.cliente.situacao
			);
			item.corSituacaoContrato = this.biCommonService.corDs3StatusContrato(
				item.contrato.situacaoContrato
			);
		});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: OperacoesContratoRetroativo) {
		this.biCommonService.openCliente(item.cliente.matricula);
	}
}
