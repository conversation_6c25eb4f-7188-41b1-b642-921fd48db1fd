import { BiAulasExperimentaisFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-aulas-experimentais/bi-aulas-experimentais-filter/bi-aulas-experimentais-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	HostBinding,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import {
	AulaExperimentalResponseModel,
	BiMsApiAulaExperimentalService,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { TabData } from "../../bi-adm.model";

@Component({
	selector: "adm-bi-card-aulas-experimentais",
	templateUrl: "./bi-card-aulas-experimentais.component.html",
	styleUrls: ["./bi-card-aulas-experimentais.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardAulasExperimentaisComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	@HostBinding("class.adm-bi-card-aulas-experimentais")
	tabData: TabData;
	loading: boolean = false;
	tipoData: number = 1;

	public tiposLista: any[] = [
		{ value: 1, label: "Aulas" },
		{ value: 2, label: "Alunos" },
	];
	private aulaExperimentalResponse: AulaExperimentalResponseModel;

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected datePipe: DatePipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biMsApiAulaExperimentalService: BiMsApiAulaExperimentalService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		this.destroy();
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Aulas experimentais",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-aulas-experimentais-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiAulasExperimentaisFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	consultar(dataRangeTab) {
		this.tipoData = this.tiposLista.find(
			(tipo) => tipo.label === "Aulas"
		).value;
		if (dataRangeTab) {
			const tipoAtivo = dataRangeTab.tabs.find((tab) => tab.active);
			const tipoName = tipoAtivo.tabTitle;

			this.tipoData = this.tiposLista.find(
				(tipo) => tipo.label === tipoName
			).value;
		}
		this._populateTabData(this.aulaExperimentalResponse);
		this.cd.detectChanges();
	}

	loadData(reloadFull?: boolean) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		this.loading = true;
		this.cd.detectChanges();

		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.biMsApiAulaExperimentalService
			.list({
				filtroAulaExperimental: {
					inicio: this.filtros.dataInicio.getTime(),
					fim: this.filtros.data.getTime(),
					responsavel: 0,
					empresa: this.filtros.empresa,
					listas: false,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: AulaExperimentalResponseModel) => {
					this.aulaExperimentalResponse = v;
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	_populateTabData(v?: AulaExperimentalResponseModel) {
		if (!v) {
			v = {
				alunosAgendados: 0,
				executaram: 0,
				convertidos: 0,
				aulasAgendadas: 0,
				aulasExecutadas: 0,
				agendamentosAconteceram: 0,
				agendamentosAcontecer: 0,
				aulasPorAluno: 0,
				indicePresenca: 0,
				indiceConversaoAulas: 0,
				indiceConversaoProfessores: 0,
			};
		}
		this.tabData = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.alunosAgendados,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: false,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: "Alunos agendados",
							},
						},
						{
							info: {
								value: v.executaram,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: false,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: "Executaram",
							},
						},
						{
							info: {
								value: v.convertidos,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: false,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: "Convertidos",
							},
						},
					],
					title: "",
					listItens: [
						{
							text: "Total de aulas agendadas",
							value: v.aulasAgendadas,
						},
						{
							text: "Total de aulas executadas",
							value: v.aulasExecutadas,
						},
						{
							text: "Agendamentos que já aconteceram",
							value: v.agendamentosAconteceram,
						},
						{
							text: "Agendamentos que irão acontecer",
							value: v.agendamentosAcontecer,
						},
						{
							text: "Aulas agendadas por aluno",
							value: v.aulasPorAluno,
						},
						{
							text: "Índice de presença em aulas experimentais",
							percentage: v.indicePresenca,
						},
						{
							text: "Índice de conversão de aulas experimentais",
							percentage: v.indiceConversaoAulas,
						},
						{
							text: "Índice de conversão dos professores de aulas experimentais",
							percentage: v.indiceConversaoProfessores,
							clickable: true,
						},
					],
				},
			],
		};
	}

	trackByIndex(index, item) {
		return index;
	}
}
