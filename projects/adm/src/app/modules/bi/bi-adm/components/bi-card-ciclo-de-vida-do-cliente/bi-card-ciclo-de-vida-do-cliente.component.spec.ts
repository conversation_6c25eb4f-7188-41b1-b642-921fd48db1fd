import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiCardCicloDeVidaDoClienteComponent } from "./bi-card-ciclo-de-vida-do-cliente.component";

describe("BiCardCicloDeVidaDoClienteComponent", () => {
	let component: BiCardCicloDeVidaDoClienteComponent;
	let fixture: ComponentFixture<BiCardCicloDeVidaDoClienteComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiCardCicloDeVidaDoClienteComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiCardCicloDeVidaDoClienteComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
