import { <PERSON><PERSON><PERSON>cyPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	HostBinding,
	Inject,
	Input,
	LOCALE_ID,
	OnChanges,
	OnInit,
	SimpleChanges,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import {
	ApiResponseList,
	BiMsApiMetaFinanceiraService,
	ConversaoVendaResponseModel,
	ValoresFaturadosPorResponsavelModel,
} from "bi-ms-api";
import { PactoDataTableStateManager } from "ui-kit";

@Component({
	selector: "adm-bi-card-icv-ranking-consultores",
	templateUrl: "./bi-card-icv-ranking-consultores.component.html",
	styleUrls: [
		"./bi-card-icv-ranking-consultores.component.scss",
		"../../../../bi-shared/bi-shared.scss",
	],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardIcvRankingConsultoresComponent
	implements OnInit, AfterViewInit, OnChanges
{
	@HostBinding("class.bi-card-icv-ranking-consultores")
	enableEncapsulation = true;

	@ViewChild("celulaConsultor", { static: false })
	celularConsultor: TemplateRef<any>;

	@Input()
	conversaoVendaResponseModel: ConversaoVendaResponseModel;

	@Input()
	tipoData: number;

	@Input() filtros: any;

	@Input()
	withFakeData: boolean = false;

	listaConsultores: Array<ValoresFaturadosPorResponsavelModel> =
		new Array<ValoresFaturadosPorResponsavelModel>();
	listaConsultoresVisible: Array<ValoresFaturadosPorResponsavelModel> =
		new Array<ValoresFaturadosPorResponsavelModel>();
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();

	qtdConsultores = 3;

	constructor(
		@Inject(LOCALE_ID) private locale,
		private cd: ChangeDetectorRef,
		private domSanitizer: DomSanitizer,
		private currencyPipe: CurrencyPipe,
		private metaf: BiMsApiMetaFinanceiraService
	) {}

	ngOnInit() {
		this._loadRanking();
	}

	ngAfterViewInit() {}

	ngOnChanges(changes: SimpleChanges) {
		const conversaoVendaResponseModel = changes["conversaoVendaResponseModel"];
		if (
			conversaoVendaResponseModel &&
			!conversaoVendaResponseModel.firstChange
		) {
			this.conversaoVendaResponseModel =
				conversaoVendaResponseModel.currentValue;
		}
		const tipoData = changes["tipoData"];
		if (tipoData && !tipoData.firstChange) {
			this.tipoData = tipoData.currentValue;
		}
	}

	private _loadRanking() {
		if (this.withFakeData) {
			return;
		}
		this.tableState.patchState({ loading: true });
		const dataInicial = new Date(this.filtros.data);
		dataInicial.setDate(1);
		dataInicial.setHours(0, 0, 0, 0);
		this.metaf
			.rankingConsultores({
				empresa: this.filtros.empresa,
				dataInicial: dataInicial.getTime(),
				dataFinal: this.filtros.data.getTime(),
				responsaveisContrato: [],
			})
			.subscribe({
				next: (
					response: ApiResponseList<ValoresFaturadosPorResponsavelModel>
				) => {
					this.listaConsultores = response.content || [];

					this.listaConsultores.forEach((item) => {
						if (!item.fotoKey || item.fotoKey === "") {
							item.fotoKey = `pacto-ui/images/user-image-default.svg`;
						}
						item.fotoUrl = item.fotoKey;
					});

					if (this.listaConsultores.length > this.qtdConsultores) {
						this.listaConsultoresVisible = this.listaConsultores.slice(
							0,
							this.qtdConsultores
						);
					} else {
						this.listaConsultoresVisible = this.listaConsultores;
					}
					this.tableState.patchState({
						data: this.listaConsultoresVisible,
						loading: false,
					});
					this.cd.detectChanges();
				},
			});
	}

	showMoreConsultores() {
		if (this.listaConsultores.length > this.qtdConsultores) {
			let limit;
			if (
				this.listaConsultores.length - this.listaConsultoresVisible.length <
				this.qtdConsultores
			) {
				limit = this.listaConsultores.length;
			} else {
				limit = this.listaConsultoresVisible.length + this.qtdConsultores;
			}
			if (limit !== 0) {
				this.listaConsultoresVisible.push(
					...this.listaConsultores.slice(
						this.listaConsultoresVisible.length,
						limit
					)
				);

				this.tableState.patchState({
					data: this.listaConsultoresVisible,
				});
			}
		}
	}

	showLessConsultores() {
		if (this.listaConsultoresVisible.length > this.qtdConsultores) {
			let limit = this.qtdConsultores;

			if (
				(this.listaConsultoresVisible.length - limit) % this.qtdConsultores !==
				0
			) {
				limit = this.listaConsultoresVisible.length - limit;
			}

			this.listaConsultoresVisible.splice(
				this.listaConsultoresVisible.length - limit,
				this.qtdConsultores
			);

			this.tableState.patchState({
				data: this.listaConsultoresVisible,
			});
		}
	}

	resetVisibleConsultores() {
		if (this.listaConsultoresVisible.length > this.qtdConsultores) {
			this.listaConsultoresVisible = this.listaConsultores.slice(
				0,
				this.qtdConsultores
			);

			this.tableState.patchState({
				data: this.listaConsultoresVisible,
			});
		}
	}
}
