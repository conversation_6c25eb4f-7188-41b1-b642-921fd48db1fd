import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalAlunosExcluidosBdComponent } from "./bi-modal-alunos-excluidos-bd.component";

describe("BiModalAlunosExcluidosBdComponent", () => {
	let component: BiModalAlunosExcluidosBdComponent;
	let fixture: ComponentFixture<BiModalAlunosExcluidosBdComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalAlunosExcluidosBdComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalAlunosExcluidosBdComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
