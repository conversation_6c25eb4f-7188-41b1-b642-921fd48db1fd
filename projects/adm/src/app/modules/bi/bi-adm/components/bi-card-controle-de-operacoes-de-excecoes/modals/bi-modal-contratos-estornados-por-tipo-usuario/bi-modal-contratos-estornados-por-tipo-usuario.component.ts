import { Component, Inject, OnDestroy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiEstornoObservacaoService,
	ApiResponseList,
	EstornoObservacao,
	FiltroBiControleOperacao,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-contratos-estornados-por-tipo-usuario",
	templateUrl:
		"./bi-modal-contratos-estornados-por-tipo-usuario.component.html",
	styleUrls: [
		"./bi-modal-contratos-estornados-por-tipo-usuario.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalContratosEstornadosPorTipoUsuarioComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataEstorno",
			visible: true,
			titulo: "Dt. Exclusão",
			ordenavel: true,
			nome: "matricula",
			inputType: "text",
			dateTime: true,
		},
		{
			mostrarTitulo: true,
			campo: "usuarioResponsavel",
			visible: true,
			titulo: "Resp. Lançamento",
			ordenavel: true,
			nome: "situacao",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "justificativa",
			visible: true,
			titulo: "Situacao",
			ordenavel: true,
			nome: "Justificativa",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string;
	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalContratosEstornadosPorTipoUsuarioComponent>,
		private admCoreEstornoObservacaoService: AdmCoreApiEstornoObservacaoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
			this._infoByDataType();
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	private _infoByDataType() {
		const dataType = this.modalData.dataType;
		if (dataType === "admin") {
			this.shareUrl = this.admRest.buildFullUrlAdmCore(
				"estorno-observacao/admin"
			);
			return;
		}

		if (dataType === "recorrencia") {
			this.shareUrl = this.admRest.buildFullUrlAdmCore(
				"estorno-observacao/recorrencia"
			);
			return;
		}

		this.shareUrl = this.admRest.buildFullUrlAdmCore(
			"estorno-observacao/usuario-comum"
		);
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this._getApiMethod()
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<EstornoObservacao>) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _getApiMethod() {
		if (this.modalData) {
			if (this.modalData.dataType === "admin") {
				return this.admCoreEstornoObservacaoService.estornoObservacaoAdmin({
					filters: this.filters,
					page: this.page,
					size: this.size,
					orderBy: this.orderBy,
					orderDirection: this.orderDirection,
				});
			}
			if (this.modalData.dataType === "recorrencia") {
				return this.admCoreEstornoObservacaoService.estornoObservacaoRecorrencia(
					{
						filters: this.filters,
						page: this.page,
						size: this.size,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					}
				);
			}
			return this.admCoreEstornoObservacaoService.estornoObservacaoUsuarioComum(
				{
					filters: this.filters,
					page: this.page,
					size: this.size,
					orderBy: this.orderBy,
					orderDirection: this.orderDirection,
				}
			);
		}
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: EstornoObservacao) {
		this.biCommonService.openCliente(item.matriculaCliente);
	}
}
