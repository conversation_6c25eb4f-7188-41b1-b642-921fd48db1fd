import { BiPendenciasClienteModalComponent } from "@adm/modules/bi/bi-adm/components/bi-card-pendencias-de-clientes/modals/bi-pendencias-cliente-modal/bi-pendencias-cliente-modal.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import { IndicadorPendenciaClientesEnum } from "adm-core-api";
import {
	BiMsApiPendenciaService,
	FiltroPendencia,
	PendenciaResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { TabData } from "../../bi-adm.model";
import { BiCardPendenciasDeClientesFilterComponent } from "./bi-card-pendencias-de-clientes-filter/bi-card-pendencias-de-clientes-filter.component";

@Component({
	selector: "adm-bi-card-pendencias-de-clientes",
	templateUrl: "./bi-card-pendencias-de-clientes.component.html",
	styleUrls: ["./bi-card-pendencias-de-clientes.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardPendenciasDeClientesComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	tabData: TabData;
	config;
	filtros = {} as any;
	loading: boolean = false;
	showHiddenFields: boolean;
	pendenciaResponseModel: PendenciaResponseModel;

	constructor(
		private biMdlAjudaService: BiMdlAjudaService,
		private biMsApiPendenciaService: BiMsApiPendenciaService,
		private biSidenavService: BiSidenavService,
		protected biCommonService: BiCommonService,
		protected dialog: MatDialog,
		protected cd: ChangeDetectorRef,
		protected breakpointObserver: BreakpointObserver,
		protected currencyPipe: CurrencyPipe,
		private datePipe: DatePipe,
		private toastrService: ToastrService,
		protected router: Router
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		this.loading = true;
		const filtroPendencia: FiltroPendencia = {
			dataBase: this.filtros.data.getTime(),
			empresa: this.filtros.empresa,
			colaboradores: this.filtros.colaboradores,
			atualizarAgora: reloadFull,
		};

		if (this.filtros.dataInicio) {
			// filtroPendencia.dataLimite = Math.floor(
			// 	this.filtros.dataInicio.getTime() / 1000
			// );
		}
		this.cd.detectChanges();
		this.biMsApiPendenciaService
			.list({
				filtroPendencia,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Pendências de clientes",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-pendencias-de-clientes-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiCardPendenciasDeClientesFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	trackByIndex(index, item) {
		return index;
	}

	private _populateTabData(v?: PendenciaResponseModel) {
		if (!v) {
			v = {
				qtdParcelasEmAtraso: 0,
				valorParcelasEmAtraso: 0,
				qtdParcelasAPagar: 0,
				valorParcelasAPagar: 0,
				qtdParcelasColaborador: 0,
				valorParcelasColaborador: 0,
				qtdDebitoContaCorrente: 0,
				valorDebitoContaCorrente: 0,
				qtdCreditoContaCorrente: 0,
				valorCreditoContaCorrente: 0,
				qtdProdutosVencidos: 0,
				valorProdutosVencidos: 0,
				qtdAniversariantesClientes: 0,
				qtdAniversariantesColaboradores: 0,
				qtdBVPendente: 0,
				qtdCadastroIncompletoVisitantes: 0,
				qtdCadastroIncompletoCliente: 0,
				qtdSemAssinaturaDigital: 0,
				qtdSemAssinaturaDigitalCancelamento: 0,
				qtdSemReconhecimentoFacial: 0,
				qtdSemFoto: 0,
				qtdSemGeolocalizacao: 0,
				qtdSemProdutos: 0,
				qtdTrancamentoVencido: 0,
				qtdClienteComMuitosProfessores: 0,
			} as PendenciaResponseModel;
		}

		this.pendenciaResponseModel = v;
		this.tabData = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.valorParcelasEmAtraso,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "loss",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: `${v.qtdParcelasEmAtraso} ${
									v.qtdParcelasEmAtraso > 0 ? "parcelas" : "parcela"
								} ${this.traducoes.getLabel(
									"bi-pendencias:parcelas-em-atraso"
								)}`,
							},
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.PARCELAS_EM_ATRASO,
									},
								},
							},
						},
						{
							info: {
								value: v.valorParcelasAPagar,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "alert",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: `${v.qtdParcelasAPagar} ${
									v.qtdParcelasAPagar > 0 ? "parcelas" : "parcela"
								} ${this.traducoes.getLabel("bi-pendencias:parcelas-a-pagar")}`,
							},
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.PARCELAS_EM_ABERTO,
									},
								},
							},
						},
						{
							info: {
								value: v.valorParcelasColaborador,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "alert",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: `${v.qtdParcelasColaborador} ${this.traducoes.getLabel(
									"bi-pendencias:colab-parcelas-a-pagar"
								)}`,
							},
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.PARCELAS_EM_ABERTO_COLABORADOR,
									},
								},
							},
						},
					],
				},
				{
					infoItens: [
						{
							info: {
								value: v.valorDebitoContaCorrente * -1,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "loss",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: `${v.qtdDebitoContaCorrente} ${
									v.qtdDebitoContaCorrente > 0 ? "clientes" : "cliente"
								} ${this.traducoes.getLabel(
									"bi-pendencias:cliente-debito-conta-corrente"
								)}`,
							},
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.DEBITO_EM_CONTA_CORRENTE,
									},
								},
							},
						},
						{
							info: {
								value: v.valorCreditoContaCorrente,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "gain",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: `${v.qtdCreditoContaCorrente} ${
									v.qtdCreditoContaCorrente > 0 ? "clientes" : "cliente"
								} ${this.traducoes.getLabel(
									"bi-pendencias:cliente-credito-conta-corrente"
								)}`,
							},
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.CREDITO_EM_CONTA_CORRENTE,
									},
								},
							},
						},
						{
							info: {
								value: v.valorProdutosVencidos,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: `${v.qtdProdutosVencidos} ${
									v.qtdProdutosVencidos > 0
										? "produtos vencidos"
										: "produto vencido"
								}`,
							},
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador: IndicadorPendenciaClientesEnum.PRODUTOS_VENCIDOS,
									},
								},
							},
						},
					],
					title: "Operacional",
					listItens: [
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-aniversariantes-hoje"
							),
							value: v.qtdAniversariantesClientes,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.CLIENTES_ANIVERSARIANTES,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:colab-aniversariantes-hoje"
							),
							value: v.qtdAniversariantesColaboradores,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.COLABORADOR_ANIVERSARIANTES,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel("bi-pendencias:bvs-pendentes"),
							value: v.qtdBVPendente,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador: IndicadorPendenciaClientesEnum.BV_PENDENTE,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-com-cadastro-incompleto"
							),
							value: v.qtdCadastroIncompletoCliente,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.CADASTRO_INCOMPLETO_CLIENTE,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:visitantes-com-cadastro-incompleto"
							),
							value: v.qtdCadastroIncompletoVisitantes,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.CADASTRO_INCOMPLETO_VISITANTE,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-sem-assinatura-de-contrato"
							),
							value: v.qtdSemAssinaturaDigital,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.SEM_ASSINATURA_CONTRATO,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-sem-assinatura-de-cancelamento-de-contrato"
							),
							value: v.qtdSemAssinaturaDigitalCancelamento,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.SEM_ASSINATURA_CANCELAMENTO_CONTRATO,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-sem-reconhecimento-facial"
							),
							value: v.qtdSemReconhecimentoFacial,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.SEM_RECONHECIMENTO_FACIAL,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel("bi-pendencias:clientes-sem-foto"),
							value: v.qtdSemFoto,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador: IndicadorPendenciaClientesEnum.SEM_FOTO,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-sem-geolocalizacao"
							),
							value: v.qtdSemGeolocalizacao,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.SEM_GEOLOCALIZACAO,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-sem-produtos"
							),
							value: v.qtdSemProdutos,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador: IndicadorPendenciaClientesEnum.SEM_PRODUTOS,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-pendencias:clientes-com-trancamento-vencido"
							),
							value: v.qtdTrancamentoVencido,
							modalConfig: {
								componentType: BiPendenciasClienteModalComponent,
								config: {
									data: {
										indicador:
											IndicadorPendenciaClientesEnum.TRANCAMENTO_VENCIDO,
									},
								},
							},
						},
					],
				},
			],
		};
	}

	mostrarMais() {
		this.showHiddenFields = !this.showHiddenFields;
	}
}
