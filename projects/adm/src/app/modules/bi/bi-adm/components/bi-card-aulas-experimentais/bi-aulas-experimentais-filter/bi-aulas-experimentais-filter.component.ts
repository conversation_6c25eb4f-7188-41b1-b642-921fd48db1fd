import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-bi-aulas-experimentais-filter",
	templateUrl: "./bi-aulas-experimentais-filter.component.html",
	styleUrls: ["./bi-aulas-experimentais-filter.component.scss"],
})
export class BiAulasExperimentaisFilterComponent implements OnInit {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
	});

	filters: {
		data: Date;
	};

	constructor(
		private cd: ChangeDetectorRef,
		private biSidenavRef: BiSidenavRef<BiAulasExperimentaisFilterComponent>
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				data: this.filters.data,
			});
		}
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		this.biSidenavRef.close({
			filters: {
				...this.filters,
			},
		});
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}
}
