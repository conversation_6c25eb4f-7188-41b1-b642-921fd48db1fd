import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiContratoService,
	ApiResponseList,
	Contrato,
	FiltroBiControleOperacao,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-clientes-possuem-autorizacao-cob-contrato",
	templateUrl:
		"./bi-modal-clientes-possuem-autorizacao-cob-contrato.component.html",
	styleUrls: [
		"./bi-modal-clientes-possuem-autorizacao-cob-contrato.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalClientesPossuemAutorizacaoCobContratoComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "pessoaDTO.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "cliente.matricula",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "matricula",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "cliente.situacao",
			visible: true,
			titulo: "Situação",
			ordenavel: true,
			nome: "situacao",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "codigo",
			visible: true,
			titulo: "Contrato",
			ordenavel: true,
			nome: "contrato",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "vigenciaAteAjustada",
			visible: true,
			titulo: "Dt. Fim",
			ordenavel: true,
			nome: "vigenciaAteAjustada",
			inputType: "date",
			date: true,
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"contratos/com-autorizacao"
	);
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";
	private _destroy$: Subject<void> = new Subject();

	constructor(
		private dialog: MatDialogRef<BiModalClientesPossuemAutorizacaoCobContratoComponent>,
		private admCoreApiContratoService: AdmCoreApiContratoService,
		private admRest: AdmRestService,
		private toastrService: ToastrService,
		private biCommonService: BiCommonService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiContratoService
			.comAutorizacao({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
				comAutorizacaoRenovavel: this.filters.comAutorizacaoRenovavel,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<Contrato>) => {
					this.totalItems = response.totalElements;
					this._populateCorSituacao(response.content);
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _populateCorSituacao(data: Array<Contrato>) {
		data.forEach((item) => {
			item.corSituacaoCliente = this.biCommonService.corDs3StatusCliente(
				item.cliente.situacao
			);
			item.corSituacaoContrato = this.biCommonService.corDs3StatusContrato(
				item.situacaoContratoSW
			);
		});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: Contrato) {
		this.biCommonService.openCliente(item.cliente.matricula);
	}
}
