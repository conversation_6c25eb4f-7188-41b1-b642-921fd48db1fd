<adm-bi-modal-content
	[modalTitle]="modalTitle"
	[shareUrl]="shareUrl"
	[filters]="filters"
	[page]="page"
	[pageSize]="size"
	[totalItems]="totalItems"
	[colunasShare]="colunasShare"
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)">
	<ds3-table>
		<table ds3DataTable [stateManager]="tableState">
			<ng-container ds3TableColumn="pessoa">
				<th *ds3TableHeaderCell>Nome</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					<button
						ds3-text-button
						(click)="openCliente(item)"
						*ngIf="item?.pessoa?.nome && item?.pessoa?.nome !== ''">
						{{ item?.pessoa?.nome | titlecase }}
					</button>
					<button
						ds3-text-button
						disabled
						*ngIf="!item?.pessoa?.nome || item?.pessoa?.nome === ''">
						Aluno excluído
					</button>
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataEstorno">
				<th *ds3TableHeaderCell class="align-center">Dt. Exclusão</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.dataEstorno | date : "dd/MM/yyyy HH:mm:ss" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="usuarioResponsavel">
				<th *ds3TableHeaderCell class="align-center">Resp. Lançamento</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.usuarioResponsavel | titlecase }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="justificativa">
				<th *ds3TableHeaderCell class="align-center">Justificativa</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.justificativa }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				ds3-icon-button
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				(click)="triggerSortToggle()">
				<i
					class="pct"
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</ds3-table>
</adm-bi-modal-content>
