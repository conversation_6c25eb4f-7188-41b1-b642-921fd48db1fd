import { Component, Inject, OnD<PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiContratoService,
	ApiResponseList,
	Contrato,
	FiltroBiControleOperacao,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-contratos-database-alterada",
	templateUrl: "./bi-modal-contratos-database-alterada.component.html",
	styleUrls: [
		"./bi-modal-contratos-database-alterada.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalContratosDatabaseAlteradaComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "cliente.pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "cliente.pessoa.nome",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "cliente.matricula",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "cliente.matricula",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "codigo",
			visible: true,
			titulo: "N° contrato",
			ordenavel: true,
			nome: "codigo",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "responsavelDataBase.nome",
			visible: true,
			titulo: "Resp. Alteração",
			ordenavel: true,
			nome: "responsavelDataBase.nome",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataAlteracaoManual",
			visible: true,
			titulo: "Alteração Dt. Base",
			ordenavel: true,
			nome: "dataAlteracaoManual",
			inputType: "text",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "dataLancamento",
			visible: true,
			titulo: "Dt. Lançamento",
			ordenavel: true,
			nome: "dataLancamento",
			inputType: "text",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "vigenciaDe",
			visible: true,
			titulo: "Dt. Início",
			ordenavel: true,
			nome: "dataAlteracaoManual",
			inputType: "text",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "vigenciaAteAjustada",
			visible: true,
			titulo: "Dt. Fim",
			ordenavel: true,
			nome: "dataAlteracaoManual",
			inputType: "text",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "contratoDuracao.numeroMeses",
			visible: true,
			titulo: "Duração",
			ordenavel: true,
			nome: "contratoDuracao.numeroMeses",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"contratos/data-base-alterada"
	);
	private _destroy$: Subject<void> = new Subject();

	constructor(
		private dialog: MatDialogRef<BiModalContratosDatabaseAlteradaComponent>,
		private admCoreClienteService: AdmCoreApiContratoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreClienteService
			.contratosComDataBaseAlterada({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<Contrato>) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: Contrato) {
		this.biCommonService.openCliente(item.cliente.matricula);
	}
}
