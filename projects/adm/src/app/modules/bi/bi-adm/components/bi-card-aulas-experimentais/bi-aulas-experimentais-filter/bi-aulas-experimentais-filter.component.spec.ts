import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiAulasExperimentaisFilterComponent } from "./bi-aulas-experimentais-filter.component";

describe("BiAulasExperimentaisFilterComponent", () => {
	let component: BiAulasExperimentaisFilterComponent;
	let fixture: ComponentFixture<BiAulasExperimentaisFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiAulasExperimentaisFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiAulasExperimentaisFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
