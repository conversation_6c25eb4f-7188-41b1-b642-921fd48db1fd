import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiCardChurnPredictionFilterComponent } from "./bi-card-churn-prediction-filter.component";

describe("BiCardChurnPredictionFilterComponent", () => {
	let component: BiCardChurnPredictionFilterComponent;
	let fixture: ComponentFixture<BiCardChurnPredictionFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiCardChurnPredictionFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiCardChurnPredictionFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
