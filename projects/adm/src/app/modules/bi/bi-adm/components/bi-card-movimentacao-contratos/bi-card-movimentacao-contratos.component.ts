import { BiCardMovientacaoContratosFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-movimentacao-contratos/bi-card-movientacao-contratos-filter/bi-card-movientacao-contratos-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnChanges,
	OnDestroy,
	OnInit,
	SimpleChanges,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiEnum,
	BiMsApiRotatividadeContratoService,
	ConfiguracaoBIEnum,
	FiltroRotatividadeContrato,
	getConfigByBi,
	RotatividadeContratoResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { PermissaoService } from "pacto-layout";
import { takeUntil } from "rxjs/operators";
import { PactoDataTableStateManager } from "../../../../../../../../ui/src/lib/ds3/ds3-table/directives/ds3-data-table-state-manager";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";

@Component({
	selector: "adm-bi-card-movimentacao-contratos",
	templateUrl: "./bi-card-movimentacao-contratos.component.html",
	styleUrls: ["./bi-card-movimentacao-contratos.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardMovimentacaoContratosComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnChanges, OnDestroy
{
	loading: boolean = false;
	tabDataTop;
	tabDataBottom;
	tabDataMiddle;
	config;

	periodo: "hoje" | "ateHoje" = "hoje";

	private _rotatividadeContratoResponse: RotatividadeContratoResponseModel;
	usaPlanoRecorrenteCompartilhado: boolean = false;
	exibirAgregadoresMovimentacaoContrato: boolean = false;
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	tableStateDependentes: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biMsApiRotatividadeContratoService: BiMsApiRotatividadeContratoService,
		private permissaoService: PermissaoService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();

		if (this.permissaoService.configuracaoSistemaAdm) {
			this.usaPlanoRecorrenteCompartilhado =
				this.permissaoService.configuracaoSistemaAdm.usaplanorecorrentecompartilhado;
		}
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnChanges(changes: SimpleChanges) {
		const configs = changes.config;
		if (configs && !configs.isFirstChange()) {
			this.configBi = configs.currentValue;
			this.loadData(true);
		}
	}

	ngOnDestroy() {
		super.destroy();
	}

	setPeriodo(periodo) {
		this.periodo = periodo;
		this._populateTabData(this._rotatividadeContratoResponse);
		this.cd.detectChanges();
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		this.loading = true;
		this.tableState.patchState({ loading: true });
		this.tableStateDependentes.patchState({ loading: true });
		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;

		const configMovContrato = this.buildBiConfigs();

		const filtroRotatividadeContrato: FiltroRotatividadeContrato = {
			dataBase: this.filtros.data.getTime(),
			empresa: this.filtros.empresa,
			atualizarAgora: reloadFull,
			usaPlanoCompartilhado: this.usaPlanoRecorrenteCompartilhado,
			exibirAgregadoresMovimentacaoContrato:
				this.exibirAgregadoresMovimentacaoContrato,
			desconsiderarCancelamentoMudancaPlano:
				this.filtros.desconsiderarCancelamentoMudancaPlano || false,
			qtdCheckinConsiderarAgregador:
				configMovContrato.configQtdCheckinConsiderarAgregador,
			qtdDiasConsiderarAgregador:
				configMovContrato.configQtdDiasConsiderarAgregador,
		};

		this.cd.detectChanges();
		this.biMsApiRotatividadeContratoService
			.list({
				filtroRotatividadeContrato,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: RotatividadeContratoResponseModel) => {
					this._rotatividadeContratoResponse = v;
					this._populateTabData(v);
					this.loading = false;
					this.tableState.patchState({ loading: false });
					this.tableStateDependentes.patchState({ loading: false });
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.tableState.patchState({ loading: false });
					this.tableStateDependentes.patchState({ loading: false });
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Pendências de clientes",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-pendencias-de-clientes-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiCardMovientacaoContratosFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	abrirAjudaMovimentacoesContratos() {
		this.biMdlAjudaService.openMdl({
			title: "Movimentações de contratos",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-movimentacao-de-contratos-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	trackByIndex(index, item) {
		return index;
	}

	private _populateTabData(v?: RotatividadeContratoResponseModel) {
		if (!v) {
			v = {
				qtdVencido: 0,
				qtdVencidoFimMes: 0,
				qtdVigentesMesAnterior: 0,
				qtdeVigenteMesAtual: 0,
				qtdeFinalMesAtual: 0,
				qtdTotal: 0,
				qtdMatriculado: 0,
				qtdMatriculadoHoje: 0,
				qtdRematriculado: 0,
				qtdRematriculadoHoje: 0,
				qtdDesistente: 0,
				qtdDesistenteHoje: 0,
				qtdCancelado: 0,
				qtdCanceladoHoje: 0,
				qtdCanceladoComFiltro: 0,
				qtdCanceladoHojeComFiltro: 0,
				qtdTrancamento: 0,
				qtdTrancamentoHoje: 0,
				qtdRetornoTrancamento: 0,
				qtdRetornoTrancamentoHoje: 0,
				qtdVencidoMes: 0,
				qtdSaldo: 0,

				qtdSaldoDependentes: 0,
				qtdContratoTransferido: 0,
				qtdContratoTransferidoHoje: 0,

				qtdDependentesVinculados: 0,
				qtdDependentesVinculadosHoje: 0,
				qtdDependentesDesvinculados: 0,
				qtdDependentesDesvinculadosHoje: 0,
				qtdDependentesMesAtual: 0,
				qtdDependentesFinalMesAtual: 0,
				qtdClienteAtivosDependentesMesAtual: 0,
				qtdClienteAtivosDependentesFinalMesAtual: 0,
				qtdClientesBolsistasFinalMesAtual: 0,

				qtdAgregadoresMesAnterior: 0,
				qtdAgregadoresGympassMesAnterior: 0,
				qtdAgregadoresGogoodMesAnterior: 0,
				qtdAgregadoresTotalpassMesAnterior: 0,
				qtdAgregadoresHoje: 0,

				churnRate: 0,
			} as RotatividadeContratoResponseModel;
		}

		const fechamentoMesAnteriorInfoItems = [
			{
				info: {
					value: v.qtdeVigenteMesAtual,
					size: "24px",
					isMonetary: false,
					isPercentage: false,
					state: "gain",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: this.traducoes.getLabel(
						"bi-movimentacao-contratos:contratos-ativos"
					),
				},
			},
			{
				info: {
					value: v.qtdVencido,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "loss",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: this.traducoes.getLabel(
						"bi-movimentacao-contratos:contratos-vencidos"
					),
				},
			},
		];

		if (this.usaPlanoRecorrenteCompartilhado) {
			fechamentoMesAnteriorInfoItems.push({
				info: {
					value: v.qtdDependentesMesAtual,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "alert",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: this.traducoes.getLabel(
						"bi-movimentacao-contratos:dependentes"
					),
				},
			});
		}

		fechamentoMesAnteriorInfoItems.push({
			info: {
				value: v.qtdVigentesMesAnterior,
				size: "24px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: false,
				state: "default",
			},
			auxiliary: undefined,
			textButton: undefined,
			overline: {
				avatarImage: undefined,
				dotHexColor: undefined,
				link: undefined,
				text: this._getLabelTodosContratosTop(),
			},
		});

		const resultadoMesAtualInfoItens = [
			{
				info: {
					value: v.qtdeFinalMesAtual,
					size: "24px",
					isMonetary: false,
					isPercentage: false,
					state: "gain",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: this.traducoes.getLabel(
						"bi-movimentacao-contratos:contratos-ativos"
					),
				},
			},
			{
				info: {
					value: v.qtdVencidoMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "loss",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: this.traducoes.getLabel(
						"bi-movimentacao-contratos:contratos-vencidos"
					),
				},
			},
		];

		if (this.exibirAgregadoresMovimentacaoContrato) {
			resultadoMesAtualInfoItens.push({
				info: {
					value: v.qtdAgregadoresMesAnterior,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "pink",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: "Clientes do contrato com agregadores",
				},
			});
		}

		if (this.usaPlanoRecorrenteCompartilhado) {
			resultadoMesAtualInfoItens.push({
				info: {
					value: v.qtdDependentesFinalMesAtual,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "alert",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					link: undefined,
					text: this.traducoes.getLabel(
						"bi-movimentacao-contratos:dependentes"
					),
				},
			});
		}

		resultadoMesAtualInfoItens.push({
			info: {
				value: v.qtdTotal,
				size: "24px",
				afterIcon: undefined,
				beforeIcon: undefined,
				isMonetary: false,
				isPercentage: false,
				state: "default",
			},
			auxiliary: undefined,
			textButton: undefined,
			overline: {
				avatarImage: undefined,
				dotHexColor: undefined,
				link: undefined,
				text: this._getLabelTodosContratosBottom(),
			},
		});

		const data = [
			{
				label: this.traducoes.getLabel(
					"bi-movimentacao-contratos:matriculados"
				),
				valueDia: this.dadoPeriodo(v, "qtdMatriculado", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdMatriculado", "ateHoje"),
				type: "EN",
			},
			{
				label: this.traducoes.getLabel(
					"bi-movimentacao-contratos:rematriculados"
				),
				valueDia: this.dadoPeriodo(v, "qtdRematriculado", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdRematriculado", "ateHoje"),
				type: "EN",
			},
			{
				label: this.traducoes.getLabel("bi-movimentacao-contratos:cancelados"),
				valueDia: this.dadoPeriodo(v, "qtdCancelado", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdCancelado", "ateHoje"),
				type: "SD",
			},
			{
				label: this.traducoes.getLabel("bi-movimentacao-contratos:trancados"),
				valueDia: this.dadoPeriodo(v, "qtdTrancamento", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdTrancamento", "ateHoje"),
				type: "SD",
			},
			{
				label: this.traducoes.getLabel("bi-movimentacao-contratos:desistencia"),
				valueDia: this.dadoPeriodo(v, "qtdDesistente", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdDesistente", "ateHoje"),
				type: "SD",
			},
			{
				label: this.traducoes.getLabel(
					"bi-movimentacao-contratos:retorno-trancamento"
				),
				valueDia: this.dadoPeriodo(v, "qtdRetornoTrancamento", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdRetornoTrancamento", "ateHoje"),
				type: "EN",
			},
			{
				label: this.traducoes.getLabel(
					"bi-movimentacao-contratos:contratos-transferidos"
				),
				valueDia: this.dadoPeriodo(v, "qtdContratoTransferido", "hoje"),
				valueHoje: this.dadoPeriodo(v, "qtdContratoTransferido", "ateHoje"),
				type: "EN",
			},
		];

		this.tableState.patchState({
			data,
		});

		const dataDependentes = [];

		if (this.exibirAgregadoresMovimentacaoContrato) {
			dataDependentes.push({
				label: "Clientes do contrato com agregadores vinculados",
				valueDia: v.qtdAgregadoresHoje || 0,
				valueHoje: v.qtdAgregadoresMesAnterior || 0,
			});
		}

		if (this.usaPlanoRecorrenteCompartilhado) {
			dataDependentes.push(
				{
					label: "Dependentes vinculados",
					valueDia: v.qtdDependentesVinculadosHoje || 0,
					valueHoje: v.qtdDependentesVinculados || 0,
				},
				{
					label: "Dependentes desvinculados",
					valueDia: v.qtdDependentesDesvinculadosHoje || 0,
					valueHoje: v.qtdDependentesDesvinculados || 0,
					type: "SD",
				}
			);
		}

		this.tableStateDependentes.patchState({
			data: dataDependentes,
		});

		this.tabDataTop = {
			sections: [
				{
					infoTitle: this.traducoes.getLabel(
						"bi-movimentacao-contratos:fechamento-mes-anterior"
					),
					infoItens: fechamentoMesAnteriorInfoItems,
				},
			],
		};

		this.tabDataMiddle = {
			sections: [
				{
					alertItens: [
						{
							label: this.traducoes.getLabel(
								"bi-movimentacao-contratos:resultado-mes"
							),
							value: v.qtdSaldo,
							beforeIcon: {
								class: this.arrowBasedOnValue(v.qtdSaldo),
							},
							state: this.colorBasedOnValue(v.qtdSaldo),
						},
					],
				},
			],
		};

		this.tabDataBottom = {
			sections: [
				{
					alertItens: [
						{
							label: this.traducoes.getLabel(
								"bi-movimentacao-contratos:movimentacao-dependentes"
							),
							value: v.qtdSaldoDependentes,
							beforeIcon: {
								class: this.arrowBasedOnValue(v.qtdSaldoDependentes),
							},
							state: this.colorBasedOnValue(v.qtdSaldoDependentes),
						},
					],
				},
				{
					infoItens: resultadoMesAtualInfoItens,
				},
			],
		};
	}

	private _getLabelTodosContratosBottom() {
		if (
			!this.usaPlanoRecorrenteCompartilhado &&
			!this.exibirAgregadoresMovimentacaoContrato
		) {
			return "Ativos + Vencidos";
		}

		if (
			!this.usaPlanoRecorrenteCompartilhado &&
			this.exibirAgregadoresMovimentacaoContrato
		) {
			return "Ativos + Vencidos + Clientes do contrato com agregadores";
		}

		if (!this.exibirAgregadoresMovimentacaoContrato) {
			return "Ativos + Vencidos + Dependentes";
		}

		return "Ativos + Vencidos + Dependentes + Clientes do contrato com agregadores";
	}

	private _getLabelTodosContratosTop() {
		if (!this.usaPlanoRecorrenteCompartilhado) {
			return "Ativos + Vencidos";
		}

		return "Ativos + Vencidos + Dependentes";
	}

	dadoPeriodo(
		v: RotatividadeContratoResponseModel,
		atributePrefix: string,
		periodo: "hoje" | "ateHoje"
	) {
		return (
			(periodo === "hoje" ? v[atributePrefix + "Hoje"] : v[atributePrefix]) || 0
		);
	}

	totais(
		v: RotatividadeContratoResponseModel,
		atributesPrefixes: string[],
		periodo: "hoje" | "ateHoje"
	) {
		return atributesPrefixes
			.map((atributePrefix) => this.dadoPeriodo(v, atributePrefix, periodo))
			.reduce((a, b) => a + b, 0);
	}

	arrowBasedOnValue(value: number) {
		if (value === 0) {
			return "";
		}
		if (value < 0) {
			return "pct pct-arrow-down";
		}

		return "pct pct-arrow-up";
	}

	colorBasedOnValue(value: number) {
		if (value < 0) {
			return "loss";
		}
		return "gain";
	}

	private buildBiConfigs() {
		const configsMovContrato = getConfigByBi(
			BiEnum.ROTATIVIDADE_CONTRATO,
			this.configBi
		);

		let configQtdDiasConsiderarAgregadorValor = 30;
		const configQtdDiasConsiderarAgregador = configsMovContrato.find(
			(cfg) =>
				cfg.configuracao.codigo ===
				ConfiguracaoBIEnum.QTD_DIAS_CONSIDERAR_AGREGADOR
		);

		if (configQtdDiasConsiderarAgregador) {
			configQtdDiasConsiderarAgregadorValor =
				configQtdDiasConsiderarAgregador.valorAsInteger;
		}

		let configQtdCheckinConsiderarAgregadorValor = 1;
		const configQtdCheckinConsiderarAgregador = configsMovContrato.find(
			(cfg) =>
				cfg.configuracao.codigo ===
				ConfiguracaoBIEnum.QTD_CHECKIN_CONSIDERAR_AGREGADOR
		);

		if (configQtdCheckinConsiderarAgregador) {
			configQtdCheckinConsiderarAgregadorValor =
				configQtdCheckinConsiderarAgregador.valorAsInteger;
		}

		let configExibirAgregadoresValor = false;
		const configExibirAgregadores = configsMovContrato.find(
			(cfg) => cfg.configuracao.codigo === ConfiguracaoBIEnum.EXIBIR_AGREGADORES
		);

		if (configExibirAgregadores) {
			this.exibirAgregadoresMovimentacaoContrato =
				configExibirAgregadores.valorAsBoolean;
			configExibirAgregadoresValor = configExibirAgregadores.valorAsBoolean;
		}

		return {
			configQtdDiasConsiderarAgregador: configQtdDiasConsiderarAgregadorValor,
			configQtdCheckinConsiderarAgregador:
				configQtdCheckinConsiderarAgregadorValor,
			configExibirAgregadores: configExibirAgregadoresValor,
		};
	}
}
