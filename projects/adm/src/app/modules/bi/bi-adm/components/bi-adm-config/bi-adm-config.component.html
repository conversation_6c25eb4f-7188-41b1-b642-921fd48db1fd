<div class="bi-config-header">
	<span class="bi-config-title">Configurações gerais</span>

	<button ds3-icon-button (click)="close()">
		<i class="pct pct-x"></i>
	</button>
</div>
<div class="bi-config-content">
	<div class="bi-config-hint">
		<i class="pct pct-info"></i>
		<span class="typography-overline-2">
			Essa configurações serão aplicadas para todo o BI e para toda a empresa.
			Apenas pessoas com permissão poderão ter acesso
		</span>
	</div>
	<form [formGroup]="formConfigMovimentacaoContrato">
		<div class="bi-config-content-title typography-title-4">
			Movimentação de contrato
		</div>
		<ng-container
			*ngFor="let config of configsMovimentacaoContrato"
			[ngTemplateOutlet]="formFields"
			[ngTemplateOutletContext]="{ config: config }"></ng-container>
	</form>
	<ds3-diviser></ds3-diviser>
	<form [formGroup]="formConfigCicloVida">
		<div class="bi-config-content-title typography-title-4">
			Ciclo de vida do cliente
		</div>
		<ng-container
			*ngFor="let config of configsCicloVida"
			[ngTemplateOutlet]="formFields"
			[ngTemplateOutletContext]="{ config: config }"></ng-container>
	</form>
	<ds3-diviser></ds3-diviser>
	<form [formGroup]="formConfigTicketMedio">
		<div class="bi-config-content-title typography-title-4">Ticket Médio</div>
		<ng-container
			*ngFor="let config of configsTicketMedio"
			[ngTemplateOutlet]="formFields"
			[ngTemplateOutletContext]="{ config: config }"></ng-container>
	</form>
</div>
<div class="bi-config-actions">
	<button ds3-text-button (click)="close()">Cancelar</button>
	<button ds3-flat-button (click)="save()">Salvar alterações</button>
</div>

<ng-template #formFields let-config="config">
	<div
		class="bi-config-content-item"
		*ngIf="config?.configuracao?.tipo === configuracaoTipoEnum.BOOLEAN">
		<ds3-form-field>
			<ds3-checkbox ds3Input [formControl]="config?.configuracao?.formControl">
				{{ config?.configuracao?.nome }}
			</ds3-checkbox>
		</ds3-form-field>
	</div>
	<div
		class="bi-config-content-item"
		*ngIf="config?.configuracao?.tipo === configuracaoTipoEnum.COMBO">
		<ds3-form-field>
			<ds3-field-label>
				{{ config?.configuracao?.nome }}
			</ds3-field-label>
			<ds3-select
				ds3Input
				[formControl]="config?.configuracao?.formControl"
				[options]="config?.configuracao?.itens"
				valueKey="value"
				nameKey="label"></ds3-select>
		</ds3-form-field>
	</div>
	<div
		class="bi-config-content-item bi-config-content-item-dark"
		*ngIf="config?.configuracao?.tipo === configuracaoTipoEnum.RADIO">
		<div class="bi-config-radio-label typography-overline-1">
			{{ config?.configuracao?.nome }}
		</div>
		<ds3-radio-group [formControl]="config?.configuracao?.formControl">
			<ds3-radio
				*ngFor="let item of config?.configuracao?.itens"
				[value]="item.value">
				{{ item?.label }}
			</ds3-radio>
		</ds3-radio-group>
	</div>
</ng-template>
