import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiSharedModule } from "../../../bi-shared/bi-shared.module";
import { BiCardControleDeOperacoesDeExcecoesComponent } from "./bi-card-controle-de-operacoes-de-excecoes.component";
import { BiControleOpExcecaoFilterComponent } from "./bi-controle-op-excecao-filter/bi-controle-op-excecao-filter.component";
import { BiModalAlunosBolsaComponent } from "./modals/bi-modal-alunos-bolsa/bi-modal-alunos-bolsa.component";
import { BiModalAlunosExcluidosBdComponent } from "./modals/bi-modal-alunos-excluidos-bd/bi-modal-alunos-excluidos-bd.component";
import { BiModalAlunosExcluidosTwComVinculosComponent } from "./modals/bi-modal-alunos-excluidos-tw-com-vinculos/bi-modal-alunos-excluidos-tw-com-vinculos.component";
import { BiModalClientesComBonusComponent } from "./modals/bi-modal-clientes-com-bonus/bi-modal-clientes-com-bonus.component";
import { BiModalClientesContratoAtivoComPeriodoAcessoComponent } from "./modals/bi-modal-clientes-contrato-ativo-com-periodo-acesso/bi-modal-clientes-contrato-ativo-com-periodo-acesso.component";
import { BiModalClientesFreepassComponent } from "./modals/bi-modal-clientes-freepass/bi-modal-clientes-freepass.component";
import { BiModalClientesGympassComponent } from "./modals/bi-modal-clientes-gympass/bi-modal-clientes-gympass.component";
import { BiModalClientesPossuemAutorizacaoCobContratoComponent } from "./modals/bi-modal-clientes-possuem-autorizacao-cob-contrato/bi-modal-clientes-possuem-autorizacao-cob-contrato.component";
import { BiModalConsultoresContratosAlteradosComponent } from "./modals/bi-modal-consultores-contratos-alterados/bi-modal-consultores-contratos-alterados.component";
import { BiModalContratosCanceladosComponent } from "./modals/bi-modal-contratos-cancelados/bi-modal-contratos-cancelados.component";
import { BiModalContratosDatabaseAlteradaComponent } from "./modals/bi-modal-contratos-database-alterada/bi-modal-contratos-database-alterada.component";
import { BiModalContratosEstornadosPorTipoUsuarioComponent } from "./modals/bi-modal-contratos-estornados-por-tipo-usuario/bi-modal-contratos-estornados-por-tipo-usuario.component";
import { BiModalExclusaoVisitantesComponent } from "./modals/bi-modal-exclusao-visitantes/bi-modal-exclusao-visitantes.component";
import {
	BiModalOperacoesContratoRetroativosComponent,
	TipoOperacaoApresentarPipe,
} from "./modals/bi-modal-operacoes-contrato-retroativos/bi-modal-operacoes-contrato-retroativos.component";
import { BiModalPagamentosDatabaseAlteradaComponent } from "./modals/bi-modal-pagamentos-database-alterada/bi-modal-pagamentos-database-alterada.component";
import { BiModalPagamentosEditadosComponent } from "./modals/bi-modal-pagamentos-editados/bi-modal-pagamentos-editados.component";
import { BiModalParcelasCanceladasComponent } from "./modals/bi-modal-parcelas-canceladas/bi-modal-parcelas-canceladas.component";
import { BiModalParcelasRenegociadasComponent } from "./modals/bi-modal-parcelas-renegociadas/bi-modal-parcelas-renegociadas.component";
import { BiModalRecibosEstornadosComponent } from "./modals/bi-modal-recibos-estornados/bi-modal-recibos-estornados.component";
import { BiModalTotalValorNaoArrecadadoDevidoDescontosComponent } from "./modals/bi-modal-total-valor-nao-arrecadado-devido-descontos/bi-modal-total-valor-nao-arrecadado-devido-descontos.component";
import { BiModalContratosCanceladosTransferidosParaOutroAlunoComponent } from "./modals/bi-modal-transferidos-com-contratos-cancelados/bi-modal-contratos-cancelados-transferidos-para-outro-aluno.component";

@NgModule({
	declarations: [
		BiCardControleDeOperacoesDeExcecoesComponent,
		BiModalClientesContratoAtivoComPeriodoAcessoComponent,
		BiModalOperacoesContratoRetroativosComponent,
		BiModalContratosEstornadosPorTipoUsuarioComponent,
		BiModalParcelasCanceladasComponent,
		BiModalRecibosEstornadosComponent,
		BiModalContratosDatabaseAlteradaComponent,
		BiModalPagamentosDatabaseAlteradaComponent,
		BiModalClientesGympassComponent,
		BiModalClientesFreepassComponent,
		BiModalTotalValorNaoArrecadadoDevidoDescontosComponent,
		BiModalPagamentosEditadosComponent,
		BiModalContratosCanceladosComponent,
		BiModalClientesPossuemAutorizacaoCobContratoComponent,
		BiModalClientesComBonusComponent,
		BiModalAlunosBolsaComponent,
		BiControleOpExcecaoFilterComponent,
		TipoOperacaoApresentarPipe,
		BiModalExclusaoVisitantesComponent,
		BiModalContratosCanceladosTransferidosParaOutroAlunoComponent,
		BiModalConsultoresContratosAlteradosComponent,
		BiModalAlunosExcluidosTwComVinculosComponent,
		BiModalAlunosExcluidosBdComponent,
		BiModalParcelasRenegociadasComponent,
	],
	entryComponents: [
		BiModalClientesContratoAtivoComPeriodoAcessoComponent,
		BiModalOperacoesContratoRetroativosComponent,
		BiModalContratosEstornadosPorTipoUsuarioComponent,
		BiModalParcelasCanceladasComponent,
		BiModalRecibosEstornadosComponent,
		BiModalContratosDatabaseAlteradaComponent,
		BiModalPagamentosDatabaseAlteradaComponent,
		BiModalClientesGympassComponent,
		BiModalClientesFreepassComponent,
		BiModalTotalValorNaoArrecadadoDevidoDescontosComponent,
		BiModalPagamentosEditadosComponent,
		BiModalContratosCanceladosComponent,
		BiModalClientesPossuemAutorizacaoCobContratoComponent,
		BiModalClientesComBonusComponent,
		BiModalAlunosBolsaComponent,
		BiControleOpExcecaoFilterComponent,
		BiModalExclusaoVisitantesComponent,
		BiModalContratosCanceladosTransferidosParaOutroAlunoComponent,
		BiModalConsultoresContratosAlteradosComponent,
		BiModalAlunosExcluidosTwComVinculosComponent,
		BiModalAlunosExcluidosBdComponent,
		BiModalParcelasRenegociadasComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [
		BiCardControleDeOperacoesDeExcecoesComponent,
		BiControleOpExcecaoFilterComponent,
	],
})
export class BiCardControleDeOperacoesDeExcecoesModule {}
