import { BiCardChurnPredictionComponent } from "@adm/modules/bi/bi-adm/components/bi-card-churn-prediction/bi-card-churn-prediction.component";
import { RelatorioChurnPredictionComponent } from "@adm/modules/bi/bi-adm/components/bi-card-churn-prediction/modal/relatorio-churn-prediction.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiCardChurnPredictionFilterComponent } from "./bi-card-churn-prediction-filter/bi-card-churn-prediction-filter.component";

@NgModule({
	declarations: [
		BiCardChurnPredictionComponent,
		RelatorioChurnPredictionComponent,
		BiCardChurnPredictionFilterComponent,
	],
	imports: [BiSharedModule, CommonModule, ReactiveFormsModule],
	entryComponents: [
		RelatorioChurnPredictionComponent,
		BiCardChurnPredictionFilterComponent,
	],
	exports: [BiCardChurnPredictionComponent],
})
export class BiCardChurnPredictionModule {}
