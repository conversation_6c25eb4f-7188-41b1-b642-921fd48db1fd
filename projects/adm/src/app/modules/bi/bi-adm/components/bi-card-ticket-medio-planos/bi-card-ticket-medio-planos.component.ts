import { BiTicketMedioPlanosFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-ticket-medio-planos/bi-ticket-medio-planos-filter/bi-ticket-medio-planos-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiEnum,
	BiMsApiTicketMedioService,
	ConfiguracaoBIEnum,
	FiltroTicketMedio,
	getConfigByBi,
	TicketMedioResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";

@Component({
	selector: "adm-bi-card-ticket-medio-planos",
	templateUrl: "./bi-card-ticket-medio-planos.component.html",
	styleUrls: ["./bi-card-ticket-medio-planos.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardTicketMedioPlanosComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	@ViewChild("mediaContratosAtivosHint", { static: false })
	mediaContratosAtivosHint: TemplateRef<any>;
	@ViewChild("bolsasNoPeriodoHint", { static: false })
	bolsasNoPeriodoHint: TemplateRef<any>;
	@ViewChild("mediaPagantesHint", { static: false })
	mediaPagantesHint: TemplateRef<any>;
	@ViewChild("dependentesFimMesHint", { static: false })
	dependentesFimMesHint: TemplateRef<any>;

	tabData;
	loading: boolean = false;

	chartOptions = {
		series: [
			{
				name: "Competência",
				data: [69, 35, 51, 49, 22, 69],
			},
			{
				name: "Receita",
				data: [10, 41, 35, 12, 49, 44],
			},
			{
				name: "Despesa",
				data: [10, 20, 35, 51, 49, 22],
			},
		],
		xaxis: {
			categories: [
				"02/2024",
				"03/2024",
				"04/2024",
				"05/2024",
				"06/2024",
				"07/2024",
			],
		},
		color: "primary",
	};

	ticketMedioResponseModel: TicketMedioResponseModel;
	private configBiTicketMedio: {
		configIncluirProdutosReceita: boolean;
		configIncluirProdutosCompetencia: boolean;
		configAlunosBolsa: boolean;
		configAtivos: number;
		configFonteReceitaDespesa: number;
		configContarDependentesComoPagantes: boolean;
		configFiltrarParcelasVencidasNaoPagas: boolean;
	} = {
		configIncluirProdutosReceita: true,
		configIncluirProdutosCompetencia: false,
		configAlunosBolsa: false,
		configAtivos: 1,
		configFonteReceitaDespesa: 1,
		configContarDependentesComoPagantes: false,
		configFiltrarParcelasVencidasNaoPagas: false,
	};

	constructor(
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biMsApiTicketMedioService: BiMsApiTicketMedioService,
		protected datePipe: DatePipe,
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		this.destroy();
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull = false) {
		this.loading = true;
		const dataBase = new Date(this.filtros.data);
		this.configBiTicketMedio = this.buildBiConfig();

		const filtroTicketMedio: FiltroTicketMedio = {
			database: this.datePipe.transform(this.filtros.data, "dd/MM/yyyy"),
			empresa: this.filtros.empresa,
			mes: dataBase.getUTCMonth() + 1,
			ano: dataBase.getUTCFullYear(),
		};

		this.cd.detectChanges();
		this.biMsApiTicketMedioService
			.list({
				filtroTicketMedio,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: TicketMedioResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(BiTicketMedioPlanosFilterComponent, {
			globalFilter: this.globalFilterForm.value,
			filters: this.filtros,
		});

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	private _populateTabData(v?: TicketMedioResponseModel) {
		if (!v) {
			v = this._populateWithDefaultValues();
		}

		this.ticketMedioResponseModel = v;

		this.tabData = {
			sections: [
				{
					listItens: [
						{
							text: `Média de contratos ativos + vencidos em ${this.datePipe.transform(
								this.filtros.data,
								"MMMM"
							)}`,
							value: v.dados.mediaAtivosVencidos,
							valueHint: this.mediaContratosAtivosHint,
							show: this.configBiTicketMedio.configAtivos === 1,
						},
						{
							text: `Ativos em ${this.datePipe.transform(
								this.filtros.data,
								"MMMM"
							)}`,
							value: v.dados.ativos,
							show: this.configBiTicketMedio.configAtivos === 2,
						},
						{
							text: "Bolsas no período",
							value: v.dados.bolsas,
							valueHint: this.bolsasNoPeriodoHint,
							show: !this.configBiTicketMedio.configAlunosBolsa,
						},
						{
							text: "Dependentes no período",
							value: v.dados.dependentesFimMes,
							valueHint: this.dependentesFimMesHint,
							show: this.configBiTicketMedio
								.configContarDependentesComoPagantes,
						},
						{
							text: "Média de Pagantes",
							value: v.dados.pagantes,
							valueHint: this.mediaPagantesHint,
							show: !this.configBiTicketMedio.configAlunosBolsa,
						},
					],
				},
				{
					infoVertical: true,
					infoItens: [
						{
							info: {
								value: v.dados.caixaCompetencia,
								size: "24px",
								isMonetary: true,
								isPercentage: false,
								state: "disabled",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Caixa por competência mensal (planos)",
							},
						},
						{
							info: {
								value: v.dados.ticketCompetencia,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "disabled",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Ticket Médio",
							},
						},
					],
				},
				{
					infoVertical: true,
					infoItens: [
						{
							info: {
								value: v.dados.caixaReceita,
								size: "24px",
								isMonetary: true,
								isPercentage: false,
								state: "disabled",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Caixa por receita (total)",
							},
						},
						{
							info: {
								value: v.dados.ticketReceita,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "disabled",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Ticket Médio",
							},
						},
					],
				},
				{
					infoVertical: true,
					infoItens: [
						{
							info: {
								value: v.dados.despesaTotalMes,
								size: "24px",
								isMonetary: true,
								isPercentage: false,
								state: "disabled",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Despesa do mês (total)",
							},
						},
						{
							info: {
								value: v.dados.despesaPorAluno,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "loss",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Despesa por contrato",
							},
						},
					],
				},
			],
		};

		this.chartOptions = {
			series: [
				{
					name: "Competência",
					data: v.grafico.map((g) => g.tmCompetencia),
				},
				{
					name: "Receita",
					data: v.grafico.map((g) => g.tmReceita),
				},
				{
					name: "Despesa",
					data: v.grafico.map((g) => g.tmDespesa),
				},
			],
			xaxis: {
				categories: v.grafico.map((g) => g.mesAno),
			},
			color: "primary",
		};
	}

	private _populateWithDefaultValues() {
		return {
			dados: {
				ativosVencidosInicioMes: 0,
				ativosVencidosFimMes: 0,
				mediaAtivosVencidos: 0,
				bolsas: 0,
				ativos: 0,
				pagantes: 0,
				caixaCompetencia: 0,
				ticketCompetencia: 0,
				caixaFaturamentoRecebido: 0,
				ticketFaturamentoRecebido: 0,
				ticketReceita: 0,
				caixaReceita: 0,
				despesaTotalMes: 0,
				despesaPorAluno: 0,
				ultimaExecucaoDespesa: new Date(0),
				corDespesa: "#000000",
				mes: 0,
				ano: 0,
				ultimaAtualizacao: new Date(0),
				ticketReceitaFormatado: "R$ 0,00",
				ticketCompetenciaFormatado: "R$ 0,00",
				despesaPorAlunoFormatado: "R$ 0,00",
				dependentesInicioMes: 0,
				dependentesFimMes: 0,
			},
			grafico: [],
		};
	}

	clickHandler(event) {
		console.log(event.infoData.overline.link);
		if (event.infoData.overline.link) {
			this.router.navigate([event.infoData.overline.link]);
		}
	}

	abrirAjudaTicketMedioPlanos() {
		this.biMdlAjudaService.openMdl({
			title: "Ticket médio de planos",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-ticket-medio-de-planos-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	trackByIndex(index, item) {
		return index;
	}

	private buildBiConfig() {
		const configsTicketMedio = getConfigByBi(
			BiEnum.TICKET_MEDIO,
			this.configBi
		);

		const getBooleanValue = (
			codigo: ConfiguracaoBIEnum,
			defaultValue: boolean
		): boolean => {
			const cfg = configsTicketMedio.find(
				(c) => c.configuracao.codigo === codigo
			);
			return cfg ? cfg.valorAsBoolean : defaultValue;
		};

		const getNumberValue = (
			codigo: ConfiguracaoBIEnum,
			defaultValue: number
		): number => {
			const cfg = configsTicketMedio.find(
				(c) => c.configuracao.codigo === codigo
			);
			return cfg ? cfg.valorAsInteger : defaultValue;
		};

		return {
			configIncluirProdutosReceita: getBooleanValue(
				ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA,
				true
			),
			configIncluirProdutosCompetencia: getBooleanValue(
				ConfiguracaoBIEnum.INCLUIR_PRODUTOS_COMPETENCIA,
				false
			),
			configAlunosBolsa: getBooleanValue(
				ConfiguracaoBIEnum.ALUNOS_BOLSA,
				false
			),
			configAtivos: getNumberValue(ConfiguracaoBIEnum.ATIVOS, 1),
			configFonteReceitaDespesa: getNumberValue(
				ConfiguracaoBIEnum.FONTE_RECEITA_DESPESA,
				1
			),
			configContarDependentesComoPagantes: getBooleanValue(
				ConfiguracaoBIEnum.CONTAR_DEPENDENTES_COMO_PAGANTES,
				false
			),
			configFiltrarParcelasVencidasNaoPagas: getBooleanValue(
				ConfiguracaoBIEnum.FILTRAR_PARCELAS_VENCIDAS_NAO_PAGAS,
				false
			),
		};
	}
}
