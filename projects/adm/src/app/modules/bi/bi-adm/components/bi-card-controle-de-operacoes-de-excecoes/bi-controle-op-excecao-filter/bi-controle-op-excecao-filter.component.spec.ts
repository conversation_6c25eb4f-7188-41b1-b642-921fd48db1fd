import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiControleOpExcecaoFilterComponent } from "./bi-controle-op-excecao-filter.component";

describe("BiCardContOpExceFilterComponent", () => {
	let component: BiControleOpExcecaoFilterComponent;
	let fixture: ComponentFixture<BiControleOpExcecaoFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiControleOpExcecaoFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiControleOpExcecaoFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
