import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalClientesPossuemAutorizacaoCobContratoComponent } from "./bi-modal-clientes-possuem-autorizacao-cob-contrato.component";

describe("BiModalClientesPossuemAutorizacaoCobContratoComponent", () => {
	let component: BiModalClientesPossuemAutorizacaoCobContratoComponent;
	let fixture: ComponentFixture<BiModalClientesPossuemAutorizacaoCobContratoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalClientesPossuemAutorizacaoCobContratoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalClientesPossuemAutorizacaoCobContratoComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
