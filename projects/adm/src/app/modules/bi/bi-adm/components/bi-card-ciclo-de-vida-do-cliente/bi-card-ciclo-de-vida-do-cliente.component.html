<div class="bi-card ciclo-de-vida-do-cliente">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Ciclo de vida do cliente</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.empresaNome">
				{{ filtros?.empresaNome }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros?.dataInicio | date : "shortDate" }} -
				{{ filtros?.dataFim | date : "shortDate" }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.produtos && filtros?.produtos?.length !== 0">
				<span
					[ds3Tooltip]="tooltipProdutosRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.produtos?.length }}
					{{
						filtros?.produtos?.length > 1
							? "produtos selecionados"
							: "produto selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipProdutosRef>
				<div *ngFor="let produto of filtros?.produtos; trackBy: trackByIndex">
					{{ produto.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.servicos && filtros?.servicos?.length !== 0">
				<span
					[ds3Tooltip]="tooltipServicosRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.servicos?.length }}
					{{
						filtros?.servicos?.length > 1
							? "servicos selecionados"
							: "servico selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipServicosRef>
				<div *ngFor="let servico of filtros?.servicos; trackBy: trackByIndex">
					{{ servico.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.despesas && filtros?.despesas?.length !== 0">
				<span
					[ds3Tooltip]="tooltipDespesasRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.despesas?.length }}
					{{
						filtros?.despesas?.length > 1
							? "despesas selecionadas"
							: "despesa selecionada"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipDespesasRef>
				<div *ngFor="let despesa of filtros?.despesas; trackBy: trackByIndex">
					{{ despesa.nome }}
				</div>
			</ng-template>
		</div>
		<div
			class="bi-card-hint bi-card-hint-info"
			*ngIf="configLTV?.configLtvRealizado">
			<span>
				Filtro
				<strong>LTV Realizado</strong>
				está ativado.
			</span>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabData.sections; trackBy: trackByIndex">
				<ds3-bi-info
					[infoData]="section?.infoItens"
					(clickEvent)="openModalInfo($event)"></ds3-bi-info>
			</div>
		</div>

		<div class="bi-card-content-section" *ngIf="!loading">
			<div class="bi-card-content-section-content">
				<div class="bi-card-content-section-content-header">
					<span class="typography-title-4">Como calcular o LTV?</span>
				</div>
				<div class="typography-overline-1">
					<p *ngIf="!filtros?.considerandoLtvRealizado">
						LTV = [(Valor da soma de todos os contratos / pela duração dos
						mesmos) * Tempo médio de vida dos clientes]
					</p>
					<p *ngIf="filtros?.considerandoLtvRealizado">
						LTV = [(Valor da soma equivalente aos meses utilizados de todos os
						contratos / soma dos meses utilizados dos contratos) * Tempo médio
						de vida dos clientes]
					</p>
				</div>
			</div>
		</div>
		<div class="bi-card-content-actions" *ngIf="!loading">
			<button
				ds3-text-button
				class="bi-card-content-actions-end"
				(click)="verDetalhes()">
				Ver detalhes
			</button>
		</div>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
