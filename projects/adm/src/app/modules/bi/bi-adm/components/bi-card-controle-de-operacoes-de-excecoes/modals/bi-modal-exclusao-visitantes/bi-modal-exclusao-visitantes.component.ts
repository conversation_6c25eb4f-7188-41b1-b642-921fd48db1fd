import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiLogService,
	ApiResponseList,
	FiltroLog,
	Log,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";

@Component({
	selector: "adm-bi-modal-exclusao-visitantes",
	templateUrl: "./bi-modal-exclusao-visitantes.component.html",
	styleUrls: [
		"./bi-modal-exclusao-visitantes.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalExclusaoVisitantesComponent implements OnInit, OnDestroy {
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "nomeExclusaoVisitantes",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "nome",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dia",
			visible: true,
			titulo: "Dia",
			ordenavel: true,
			nome: "dia",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "hora",
			visible: true,
			titulo: "Horário",
			ordenavel: true,
			nome: "hora",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "responsavelAlteracao",
			visible: true,
			titulo: "Responsável",
			ordenavel: true,
			nome: "responsavelAlteracao",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroLog = {} as FiltroLog;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"logApi/exclusao-visitantes"
	);
	private _destroy$: Subject<void> = new Subject();

	constructor(
		private dialog: MatDialogRef<BiModalExclusaoVisitantesComponent>,
		private admCoreApiLogService: AdmCoreApiLogService,
		private admRest: AdmRestService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quicksearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiLogService
			.exclusaoVisitantes({
				filters: this.filters,
				page: this.page,
				size: this.size,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<Log>) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}
}
