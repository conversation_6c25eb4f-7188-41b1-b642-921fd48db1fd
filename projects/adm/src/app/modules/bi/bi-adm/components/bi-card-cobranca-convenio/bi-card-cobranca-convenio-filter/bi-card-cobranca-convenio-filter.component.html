<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="formGroup">
		<ds3-form-field>
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="formGroup.controls['data']"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Convênios</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="convenios"
				nameKey="descricao"
				valueKey="codigo"
				[useValueAsObject]="true"
				[options]="convenios"></ds3-select-multi>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Parcelas em vencimento</ds3-field-label>
			<ds3-chips-list>
				<ds3-chips
					(selectionChange)="onChipSelected($event, 'somenteParcelasMes')"
					[isActive]="formGroup.controls['somenteParcelasMes'].value">
					No próprio mês
				</ds3-chips>
				<ds3-chips
					(selectionChange)="onChipSelected($event, 'somenteParcelasForaMes')"
					[isActive]="formGroup.controls['somenteParcelasForaMes'].value">
					Em outros meses
				</ds3-chips>
			</ds3-chips-list>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Contratos cancelados</ds3-field-label>
			<ds3-chips
				(selectionChange)="onChipSelected($event, 'incluirContratosCancelados')"
				[isActive]="formGroup.controls['incluirContratosCancelados'].value">
				Incluir contratos cancelados
			</ds3-chips>
		</ds3-form-field>
	</form>
</adm-bi-side-filter-base-content>
