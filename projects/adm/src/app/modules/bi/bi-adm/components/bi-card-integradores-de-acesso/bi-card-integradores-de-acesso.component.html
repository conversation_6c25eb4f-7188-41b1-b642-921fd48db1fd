<div class="bi-card integradores-de-acesso works">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Integradores de acesso</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters" *ngIf="filtros">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros?.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined">
				<span>
					Integradores de acesso:
					<ng-container [ngSwitch]="filtros.tipoIntegradorAcesso">
						<ng-container *ngSwitchCase="0">Todos</ng-container>
						<ng-container *ngSwitchCase="1">Wellhub</ng-container>
						<ng-container *ngSwitchCase="2">Totalpass</ng-container>
					</ng-container>
				</span>
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros.acessoCheckinGympass">
				<span>Considerar acessos pelo check-in</span>
			</ds3-status>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div class="bi-card-content-section-content">
				<ng-container
					*ngFor="
						let section of tabData?.sections;
						trackBy: trackByIndex;
						let last = last
					">
					<ds3-bi-info
						*ngIf="section?.infoItens"
						[infoData]="section?.infoItens"></ds3-bi-info>
					<ng-container
						*ngFor="let chart of section.chartItens; trackBy: trackByIndex">
						<p class="bi-card-content-section-content-title">
							{{ section?.title }}
						</p>
						<ds3-chart-column
							*ngIf="chart.type === 'column'"
							[disableAnimations]="true"
							[series]="chart.series"
							[xaxis]="chart.xaxis"
							[yaxis]="chart.yaxis"></ds3-chart-column>
						<ds3-chart-line
							*ngIf="chart.type === 'line'"
							[disableAnimations]="true"
							[series]="chart.series"
							[xaxis]="chart.xaxis"
							[yaxis]="chart.yaxis"></ds3-chart-line>
					</ng-container>

					<ng-container *ngIf="section.barItens">
						<p class="bi-card-content-section-content-title">
							{{ section?.title }}
						</p>
						<ng-container
							*ngFor="let bar of section.barItens; trackBy: trackByIndex">
							<ds3-bar-line
								class="bi-card-int-accss-bar-line"
								[disableAnimations]="true"
								[barData]="bar"
								[textClickable]="true"></ds3-bar-line>
						</ng-container>
					</ng-container>
					<ds3-diviser *ngIf="!last" style="margin-top: 8px"></ds3-diviser>
				</ng-container>
			</div>
		</div>

		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
