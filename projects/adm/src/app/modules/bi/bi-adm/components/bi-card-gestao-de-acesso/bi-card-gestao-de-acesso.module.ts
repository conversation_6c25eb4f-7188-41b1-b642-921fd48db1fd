import { BiCardGestaoDeAcessoComponent } from "@adm/modules/bi/bi-adm/components/bi-card-gestao-de-acesso/bi-card-gestao-de-acesso.component";
import { ModalRelatorioAlunosComponent } from "@adm/modules/bi/bi-adm/components/bi-card-gestao-de-acesso/modal-relatorio-alunos/modal-relatorio-alunos.component";
import { ModalRelatorioIndicadorComponent } from "@adm/modules/bi/bi-adm/components/bi-card-gestao-de-acesso/modal-relatorio-indicador/modal-relatorio-indicador.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";

@NgModule({
	declarations: [
		BiCardGestaoDeAcessoComponent,
		ModalRelatorioIndicadorComponent,
		ModalRelatorioAlunosComponent,
	],
	imports: [CommonModule, BiSharedModule],
	entryComponents: [
		ModalRelatorioIndicadorComponent,
		ModalRelatorioAlunosComponent,
	],
	exports: [BiCardGestaoDeAcessoComponent],
})
export class BiCardGestaoDeAcessoModule {}
