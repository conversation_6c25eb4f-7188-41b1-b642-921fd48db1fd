import { Component, Inject, OnD<PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiJustificativaOperacaoService,
	ApiResponseList,
	FiltroBiControleOperacao,
	JustificativaOperacao,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";

@Component({
	selector: "adm-bi-modal-contratos-cancelados-transferidos-para-outro-aluno",
	templateUrl:
		"./bi-modal-contratos-cancelados-transferidos-para-outro-aluno.component.html",
	styleUrls: [
		"./bi-modal-contratos-cancelados-transferidos-para-outro-aluno.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalContratosCanceladosTransferidosParaOutroAlunoComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "descricao",
			visible: true,
			titulo: "Mensagem",
			ordenavel: true,
			nome: "descricao",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"justificativa-operacao/contratos-cancelados-transferidos"
	);
	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "descricao";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalContratosCanceladosTransferidosParaOutroAlunoComponent>,
		private admCoreApiJustificativaOperacaoService: AdmCoreApiJustificativaOperacaoService,
		private admRest: AdmRestService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiJustificativaOperacaoService
			.contratosCanceladosTransferidos({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<JustificativaOperacao>) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}
}
