import { BiCardAulasExperimentaisRankingConsultoresComponent } from "@adm/modules/bi/bi-adm/components/bi-card-aulas-experimentais/bi-card-aulas-experimentais-ranking-consultores/bi-card-aulas-experimentais-ranking-consultores.component";
import { BiCardAulasExperimentaisComponent } from "@adm/modules/bi/bi-adm/components/bi-card-aulas-experimentais/bi-card-aulas-experimentais.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiAulasExperimentaisFilterComponent } from "./bi-aulas-experimentais-filter/bi-aulas-experimentais-filter.component";

@NgModule({
	declarations: [
		BiCardAulasExperimentaisComponent,
		BiCardAulasExperimentaisRankingConsultoresComponent,
		BiAulasExperimentaisFilterComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardAulasExperimentaisComponent],
	entryComponents: [BiAulasExperimentaisFilterComponent],
})
export class BiCardAulasExperimentaisModule {}
