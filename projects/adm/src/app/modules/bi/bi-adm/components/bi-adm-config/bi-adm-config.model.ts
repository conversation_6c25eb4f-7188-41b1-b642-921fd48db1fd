export interface BiAdmConfigModel {
	configIcv: BiAdmConfigIcvModel;
	configMetaFinanceira: BiAdmConfigMetaFinanceiraModel;
	configMovimentacaoContrato: BiAdmConfigMovimentacaoContratoModel;
	configCicloVida: BiAdmConfigCicloVidaModel;
	configAulasExperimentais: BiAdmConfigAulasExperimentaisModel;
	configTicketMedio: BiAdmConfigTicketMedioModel;
}

export interface BiAdmConfigIcvModel {
	considerarLeadsIcv: boolean;
	considerarBvIcv: boolean;
	apresentarFaturamentoIcv: boolean;
	considerarPlanoBolsaIcv: boolean;
	considerarAlunosIntegradorAcesso: boolean;
}

export interface BiAdmConfigMetaFinanceiraModel {
	considerarUsuarioRecorrencia: boolean;
	considerarUsuarioAdm: boolean;
	trabalharPor: number;
}

export interface BiAdmConfigMovimentacaoContratoModel {
	considerarContratosVencido: boolean;
}

export interface BiAdmConfigCicloVidaModel {
	considerarLtvRealizado: boolean;
	considerarRenovacaoNoCalculoLt: boolean;
}

export interface BiAdmConfigAulasExperimentaisModel {
	apresentarFaturamento: boolean;
}

export interface BiAdmConfigTicketMedioModel {
	incluirProdutoCompetencia: boolean;
	ativosPor: number;
}
