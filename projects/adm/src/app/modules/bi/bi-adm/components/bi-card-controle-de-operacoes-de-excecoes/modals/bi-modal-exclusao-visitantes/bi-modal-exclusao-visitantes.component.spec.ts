import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalExclusaoVisitantesComponent } from "./bi-modal-exclusao-visitantes.component";

describe("BiModalExclusaoVisitantesComponent", () => {
	let component: BiModalExclusaoVisitantesComponent;
	let fixture: ComponentFixture<BiModalExclusaoVisitantesComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalExclusaoVisitantesComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalExclusaoVisitantesComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
