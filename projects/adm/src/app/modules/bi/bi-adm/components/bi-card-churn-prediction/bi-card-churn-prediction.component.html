<div class="bi-card churn-prediction">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Grupo de risco</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideFilter()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>

	<ds3-diviser></ds3-diviser>

	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined">{{ filtros.empresaNome }}</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGruposColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupos de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGruposColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradoresObj;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradoresObj;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>
		</div>
		<ng-container *ngIf="!loading">
			<div class="bi-card-content-section" *ngIf="tabData">
				<div class="bi-card-content-section-content">
					<div
						class="bi-card-content-section-alert bi-card-content-section-alert-group bi-card-content-section-alert-loss bi-card-content-section-alert-column">
						<div class="bi-card-content-section-alert-label">
							<i class="pct pct-alert-triangle"></i>
							Entre em contato urgente com esses alunos!
						</div>
						<div class="bi-card-content-section-alert-label">
							<strong>
								{{ grupoRiscoResponse?.percentualRisco | number : "1.2-2" }}%
							</strong>
							em ALTO risco de não renovar
						</div>
						<div class="bi-card-content-section-alert-label">
							<strong class="bi-card-content-section-alert-label-link">
								{{
									grupoRiscoResponse?.percentualContatoRisco | number : "1.2-2"
								}}%
							</strong>
							não receberam contato de risco nos últimos 15 dias
						</div>
					</div>
				</div>
				<div
					class="bi-card-content-section-content"
					*ngFor="
						let section of tabData.sections;
						index as i;
						trackBy: trackByIndex
					">
					<ng-container *ngIf="section.infoItens">
						<ds3-bi-info
							[infoData]="section?.infoItens"
							[vertical]="section?.infoVertical"
							(clickEvent)="openModalInfo($event)"></ds3-bi-info>
					</ng-container>
				</div>
			</div>
			<div class="icv-rc-btn-row">
				<button ds3-text-button (click)="openModalRisco()">Ver mais</button>
			</div>
		</ng-container>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
