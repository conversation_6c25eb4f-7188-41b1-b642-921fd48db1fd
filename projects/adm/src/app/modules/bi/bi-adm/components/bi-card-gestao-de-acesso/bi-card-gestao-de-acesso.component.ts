import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe, DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiMsApiGestaoAcessoService,
	DadosGestaoAcessoGraficoResponse,
	GestaoAcessoDiaHoraResponse,
	GestaoAcessoResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { TabData } from "../../bi-adm.model";
import { ModalRelatorioAlunosComponent } from "./modal-relatorio-alunos/modal-relatorio-alunos.component";
import { ModalRelatorioIndicadorComponent } from "./modal-relatorio-indicador/modal-relatorio-indicador.component";

const categories = [
	"06h",
	"07h",
	"08h",
	"09h",
	"10h",
	"11h",
	"12h",
	"13h",
	"14h",
	"15h",
	"16h",
	"17h",
	"18h",
	"19h",
	"20h",
	"21h",
	"22h",
	"23h",
	"00h",
];

const daysOfWeek = ["Sáb", "Seg", "Ter", "Qua", "Qui", "Sex", "Dom"];

@Component({
	selector: "adm-bi-card-gestao-de-acesso",
	templateUrl: "./bi-card-gestao-de-acesso.component.html",
	styleUrls: ["./bi-card-gestao-de-acesso.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardGestaoDeAcessoComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	tabData1: TabData;
	tabData2: TabData;
	loading: boolean;
	infoArray = [
		{ info: 10, overline: "Contrato 1", infoComplementar: 0 },
		{ info: 20, overline: "Contrato 2", infoComplementar: 10 },
		{ info: 30, overline: "Contrato 3", infoComplementar: 5 },
	];
	chartRange = [
		{
			from: 0,
			to: 20,
		},
		{
			from: 21,
			to: 50,
		},
		{
			from: 51,
			to: 75,
		},
		{
			from: 76,
			to: 94,
		},
		{
			from: 95,
			/*
			 * necessário pois o apexcharts trabalha com o range, sem isso ele altera
			 * as cores caso seja maior que o que foi definido no from
			 */
			to: 99999,
		},
	];

	mapDiaSema: Map<number, { day: string; dayFull: string }> = new Map<
		number,
		{ day: string; dayFull: string }
	>([
		[0, { day: "Dom", dayFull: "Domingo" }],
		[1, { day: "Seg", dayFull: "Segunda" }],
		[2, { day: "Ter", dayFull: "Terça" }],
		[3, { day: "Qua", dayFull: "Quarta" }],
		[4, { day: "Qui", dayFull: "Quinta" }],
		[5, { day: "Sex", dayFull: "Sexta" }],
		[6, { day: "Sáb", dayFull: "Sábado" }],
	]);

	data = [
		{
			value: 100,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 40,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "11",
		},
		{
			value: 50,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 30,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "06",
		},
		{
			value: 20,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 20,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "08",
		},
		{
			value: 10,
			dayFull: "Sábado",
			day: "Sáb",
			mediaAcesso: 10,
			mediaAcessoQuadrado: 5,
			gympass: 2,
			totalpass: 5,
			freepass: 5,
			diaria: 0,
			hora: "07",
		},
		{
			value: 100,
			dayFull: "Sexta",
			day: "Sex",
			mediaAcesso: 2,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "08",
		},
		{
			value: 50,
			dayFull: "Sexta",
			day: "Sex",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},
		{
			value: 100,
			dayFull: "Quarta",
			day: "Qua",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "15",
		},
		{
			value: 75,
			dayFull: "Quarta",
			day: "Qua",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "14",
		},
		{
			value: 50,
			dayFull: "Quarta",
			day: "Qua",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "13",
		},
		{
			value: 25,
			dayFull: "Quarta",
			day: "Qua",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "12",
		},
		{
			value: 50,
			dayFull: "Terça",
			day: "Ter",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},
		{
			value: 50,
			dayFull: "Terça",
			day: "Ter",
			mediaAcesso: 4,
			mediaAcessoQuadrado: 6,
			gympass: 4,
			totalpass: 2,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},
		{
			value: 50,
			dayFull: "Domingo",
			day: "Dom",
			mediaAcesso: 5,
			mediaAcessoQuadrado: 8,
			gympass: 6,
			totalpass: 4,
			freepass: 3,
			diaria: 1,
			hora: "07",
		},
		{
			value: 100,
			dayFull: "Domingo",
			day: "Dom",
			mediaAcesso: 10,
			mediaAcessoQuadrado: 8,
			gympass: 6,
			totalpass: 4,
			freepass: 3,
			diaria: 1,
			hora: "08",
		},
		{
			value: 100,
			dayFull: "Domingo",
			day: "Dom",
			mediaAcesso: 20,
			mediaAcessoQuadrado: 8,
			gympass: 6,
			totalpass: 4,
			freepass: 3,
			diaria: 1,
			hora: "06",
		},
	];

	chartOptions = {
		series: this.generateSeries(),
		xaxis: {
			type: "category",
			categories,
		},
	};

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected datePipe: DatePipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biMsApiGestaoAcessoService: BiMsApiGestaoAcessoService,
		private toastrService: ToastrService,
		private decimalPipe: DecimalPipe
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
		this.organizeDataByDayAndHour();
		this.updateData();
	}

	ngOnDestroy() {
		this.destroy();
	}

	loadData(reloadFull?: boolean) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull?: boolean) {
		this.loading = true;
		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.data = [];
		this.cd.detectChanges();
		const primeiroDiaMes = new Date();
		primeiroDiaMes.setDate(1);
		this.biMsApiGestaoAcessoService
			.list({
				filtroGestaoAcesso: {
					dataInicial: this.filtros.dataInicio.getTime(),
					dataFinal: this.filtros.data.getTime(),
					primeiroDiaMes: primeiroDiaMes.getTime(),
					atualizarAgora: reloadFull,
					empresa: this.filtros.empresa,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: GestaoAcessoResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	private _populateTabData(v?: GestaoAcessoResponseModel) {
		if (!v) {
			v = {
				alunosEmTempoReal: 0,
				alunosMusculacao: 0,
				alunosAulasAgendadas: 0,
				alunosOutraUnidade: 0,
				acessosLiberados: 0,
				inativosComPermissaoAcesso: 0,
				diaComMaisAcessoMes: 0,
				horarioComMaisAcessoMes: "",
				alunosPendenciasFinanceiras: 0,
				acessosBloqueados: 0,
				acessosBloqueadosMes: 0,
				listaAcessoDiaHora: new Array<DadosGestaoAcessoGraficoResponse>(),
				listaAcessoDiaHoraPassado:
					new Array<DadosGestaoAcessoGraficoResponse>(),
				acessosRealizadosDia: 0,
				percentualAcesso: 0,
				acessoMesAtual: 0,
				mediaAcessoPorMetroQuadrado: 0,
				acessoMesAnoPassado: 0,
				tempoMedioAcademia: "",
				lotacaoMaxima: 0,
				vagasDisponiveis: 0,
				vagasDisponiveis10min: 0,
				listaQtdAcessosDiaMesAnoHora: new Array<GestaoAcessoDiaHoraResponse>(),
			};
		}
		let percentualAcesso = 0;
		let qtdMesmoMesAnoPassado = 0;
		let qtdMesmoMesAnoAtual = 0;

		if (this.filtros.data) {
			const month = this.filtros.data.getUTCMonth() + 1;
			const year = this.filtros.data.getUTCFullYear();

			const dadosAcessoMesPassado = v.listaAcessoDiaHoraPassado.find(
				(acessoMesAnoPassado) =>
					acessoMesAnoPassado.mes === month &&
					acessoMesAnoPassado.ano === year - 1
			);

			if (dadosAcessoMesPassado) {
				qtdMesmoMesAnoPassado = dadosAcessoMesPassado.quantidade;
			}

			const dadosAcesoMesAtual = v.listaAcessoDiaHora.find(
				(acessoMesAnoAtual) =>
					acessoMesAnoAtual.mes === month && acessoMesAnoAtual.ano === year
			);

			if (dadosAcesoMesAtual) {
				qtdMesmoMesAnoAtual = dadosAcesoMesAtual.quantidade;
			}

			percentualAcesso = qtdMesmoMesAnoAtual - qtdMesmoMesAnoPassado;

			if (qtdMesmoMesAnoPassado > 0) {
				percentualAcesso = percentualAcesso / qtdMesmoMesAnoPassado;
			}
		}

		if (
			v.listaQtdAcessosDiaMesAnoHora &&
			v.listaQtdAcessosDiaMesAnoHora.length > 0
		) {
			v.listaQtdAcessosDiaMesAnoHora.forEach((qtdAcessoDiaMesAnoHora) => {
				const diaSemana = this.mapDiaSema.get(qtdAcessoDiaMesAnoHora.diaSemana);
				const data = {
					value: qtdAcessoDiaMesAnoHora.qtdAcessoTotal || 0,
					dayFull: diaSemana.dayFull,
					day: diaSemana.day,
					mediaAcesso: qtdAcessoDiaMesAnoHora.qtdAcessoMedia || 0,
					mediaAcessoQuadrado: 0,
					gympass: qtdAcessoDiaMesAnoHora.qtdAcessoGympass || 0,
					totalpass: qtdAcessoDiaMesAnoHora.qtdAcessoTotalpass || 0,
					freepass: qtdAcessoDiaMesAnoHora.qtdAcessoFreepass || 0,
					diaria: qtdAcessoDiaMesAnoHora.qtdAcessoDiaria || 0,
					hora: `${this.decimalPipe.transform(
						qtdAcessoDiaMesAnoHora.hora,
						"2.0-0"
					)}`,
				};
				this.data.push(data);
			});
			this.organizeDataByDayAndHour();
			this.updateData();
		}

		this.tabData1 = {
			sections: [
				{
					title: "Percentual de acesso",
					infoItens: [
						{
							info: {
								value: percentualAcesso,
								size: "24px",
								isPercentage: true,
								state: "disable",
							},
							overline: {
								text: percentualAcesso > 0 ? "Crescimento" : "Decrescimento",
							},
						},
					],
					chartItens: [
						{
							type: "column",
							series: [
								{
									name: "Acesso no mês atual",
									data: [qtdMesmoMesAnoAtual || 0],
								},
								{
									name: "Acessos no mesmo mês ano passado",
									data: [qtdMesmoMesAnoPassado || 0],
								},
							],
							xaxis: {
								type: "category",
								categories: [""],
							},
							yaxis: {},
						},
					],
				},
			],
		};
		this.tabData2 = {
			sections: [
				{
					listItens: [
						{ text: "Alunos em tempo real", value: v.alunosEmTempoReal || 0 },
						{
							text: "Alunos em aulas agendadas",
							value: v.alunosAulasAgendadas || 0,
						},
						{
							text: "Acessos bloqueados hoje",
							value: v.acessosBloqueados || 0,
						},
						{
							text: "Acessos realizados hoje",
							value: v.acessosRealizadosDia || 0,
						},
						{
							text: "Tempo médio de permanência na academia",
							time: v.tempoMedioAcademia || "00:00",
						},
					],
				},
				{
					listItens: [
						{
							text: "Alunos de outra unidade",
							value: v.alunosOutraUnidade || 0,
						},
						{ text: "Acessos liberados", value: v.acessosLiberados || 0 },
						{
							text: "Acessos bloqueados do mês",
							value: v.acessosBloqueadosMes || 0,
						},
						{
							text: "Alunos inativos com permissão de acesso",
							value: v.inativosComPermissaoAcesso || 0,
						},
						{
							text: "Dia com mais acessos do mês",
							value: v.diaComMaisAcessoMes || 0,
						},
						{
							text: "Horário com mais acessos do mês",
							info: v.horarioComMaisAcessoMes || "",
						},
						{
							text: "Alunos com pendências financeiras",
							value: v.alunosPendenciasFinanceiras || 0,
						},
					],
				},
			],
		};
	}

	organizeDataByDayAndHour() {
		const dataOrganizada = this.data.sort((a, b) => {
			const diaComparacao =
				daysOfWeek.indexOf(a.day) - daysOfWeek.indexOf(b.day);
			if (diaComparacao === 0) {
				return Number(a.hora) - Number(b.hora);
			}
			return diaComparacao;
		});
	}

	generateSeries() {
		return daysOfWeek.map((day) => ({
			name: day,
			data: this.generateInitialData(),
		}));
	}

	generateTooltip({
		series,
		seriesIndex,
		dataPointIndex,
		customTooltipData,
		x,
	}) {
		const row = daysOfWeek[seriesIndex];
		const colunn = categories[dataPointIndex];
		const filteredData = customTooltipData.filter((d) => d.day === row);
		const dataPoint = filteredData.filter(
			(v) =>
				v.value === series[seriesIndex][dataPointIndex] &&
				`${v.hora}h` === colunn
		)[0];
		if (!dataPoint) {
			return "";
		}
		return `<div style="margin: 8px 12px; display: flex; flex-direction: column;" >
                <span style="font-family: Nunito Sans; font-size: 14px; font-weight: 400; line-height: 17.5px; text-align: left;"> ${dataPoint.dayFull}, ${dataPoint.hora}h </span>                
                <span style="border-bottom: 1px solid; margin-top: 8px"></span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left; margin: 12px 0;"> Média de acesso: ${dataPoint.mediaAcesso} alunos </span> 
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left;"> Média de alunos ativos por metro quadrados: ${dataPoint.mediaAcessoQuadrado} alunos </span> 
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left;margin: 12px 0;"> Gympass: ${dataPoint.gympass} alunos </span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left;"> TotalPass: ${dataPoint.totalpass} alunos </span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left; margin: 12px 0;"> FreePass: ${dataPoint.freepass} alunos </span>
                <span style="font-family: Nunito Sans; font-size: 12px; font-weight: 400; line-height: 16px; text-align: left; margin-bottom: 12px"> Diária: ${dataPoint.diaria} alunos </span>
              </div>`;
	}

	generateInitialData() {
		return categories.map((category) => ({ x: category, y: 0 }));
	}

	updateData() {
		this.data.forEach((item) => {
			const index = categories.indexOf(`${item.hora}h`);
			const indexDay = daysOfWeek.indexOf(item.day);
			if (index !== -1 && indexDay !== -1) {
				this.chartOptions.series[indexDay].data[index].y = item.value;
			}
		});
	}

	openRelatorioIndicator() {
		const dialogRef = this.dialog.open(ModalRelatorioIndicadorComponent, {
			minWidth: "1000px",
			minHeight: "526px",
			autoFocus: false,
		});
	}

	openRelatorioAlunos(row) {
		const dialogRef = this.dialog.open(ModalRelatorioAlunosComponent, {
			minWidth: "1000px",
			minHeight: "508px",
			autoFocus: false,
			data: row,
		});
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Gestão de acessos",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-gestao-de-acessos-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	trackByIndex(index, item) {
		return index;
	}

	handleAbrirRelatorio(id: string) {}
}
