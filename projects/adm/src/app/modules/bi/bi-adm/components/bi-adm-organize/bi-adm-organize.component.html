<adm-bi-organize
	(close)="close()"
	(save)="save()"
	(cancel)="close()"
	(backToDefault)="backToDefault()"
	#admBiOrganizeComponent>
	<div class="bi-card-content-loading" *ngIf="loading">
		<img
			src="pacto-ui/images/gif/loading-pacto.gif"
			alt="Loading pacto"
			width="80" />
	</div>
	<ds3-drag-and-drop *ngIf="!loading">
		<ds3-drag-drop-remove
			[data]="dataRemoved"
			[scrollContainer]="admBiOrganizeComponent.scrollContainer">
			<ds3-drag-drop-title>
				<i class="pct pct-eye-off"></i>
				BI's não que estarão visíveis
			</ds3-drag-drop-title>
			<ng-container
				*ngFor="
					let item of dataRemoved;
					let index = index;
					trackBy: trackByIndex
				">
				<ds3-drag-drop-item *ngIf="item?.visible" [simpleView]="true">
					{{ item?.label }}
				</ds3-drag-drop-item>
			</ng-container>
		</ds3-drag-drop-remove>
		<ds3-drag-drop-add
			[columnData]="data"
			[scrollContainer]="admBiOrganizeComponent.scrollContainer">
			<ds3-drag-drop-title>
				<i class="pct pct-eye"></i>
				BI's que estarão visíveis
			</ds3-drag-drop-title>

			<ng-container *ngFor="let columnData of data; trackBy: trackByIndex">
				<ng-container
					*ngFor="
						let item of columnData.data;
						let index = index;
						trackBy: trackByItemId
					">
					<ds3-drag-drop-item *ngIf="item?.visible">
						<ng-container *ngIf="item?.template">
							<ng-container *ngTemplateOutlet="item?.template"></ng-container>
						</ng-container>
					</ds3-drag-drop-item>
				</ng-container>
			</ng-container>
		</ds3-drag-drop-add>
	</ds3-drag-and-drop>
</adm-bi-organize>

<ng-template #biControleOperacoesExcecoesTmpl>
	<adm-bi-card-controle-de-operacoes-de-excecoes
		[withFakeData]="true"></adm-bi-card-controle-de-operacoes-de-excecoes>
</ng-template>
<ng-template #biIntegradoresAcessoTmpl>
	<adm-bi-card-integradores-de-acesso
		[withFakeData]="true"></adm-bi-card-integradores-de-acesso>
</ng-template>
<ng-template #biIndiceRenovacaoTmpl>
	<adm-bi-card-indice-renovacao
		[withFakeData]="true"></adm-bi-card-indice-renovacao>
</ng-template>
<ng-template #biVerificacaoClientesTmpl>
	<adm-bi-card-verificacao-clientes
		[withFakeData]="true"></adm-bi-card-verificacao-clientes>
</ng-template>
<ng-template #biPendenciasClienteTmpl>
	<adm-bi-card-pendencias-de-clientes
		[withFakeData]="true"></adm-bi-card-pendencias-de-clientes>
</ng-template>
<ng-template #biChurnPredictionTmpl>
	<adm-bi-card-churn-prediction
		[withFakeData]="true"></adm-bi-card-churn-prediction>
</ng-template>
<ng-template #biCicloVidaClienteTmpl>
	<adm-bi-card-ciclo-de-vida-do-cliente
		[withFakeData]="true"></adm-bi-card-ciclo-de-vida-do-cliente>
</ng-template>
<ng-template #biInadimplenciaTmpl>
	<adm-bi-card-inadimplencia [withFakeData]="true"></adm-bi-card-inadimplencia>
</ng-template>
<ng-template #biMovimentacaoContratosTmpl>
	<adm-bi-card-movimentacao-contratos
		[withFakeData]="true"></adm-bi-card-movimentacao-contratos>
</ng-template>
<ng-template #biConversaoVendasTmpl>
	<adm-bi-card-conversao-vendas
		[withFakeData]="true"></adm-bi-card-conversao-vendas>
</ng-template>
<ng-template #biConversaoVendasSessaoTmpl>
	<!--	<span>BI Conversão de vendas por sessão em desenvolvimento</span>-->
</ng-template>
<ng-template #biMetasFinanceirasVendasTmpl>
	<adm-bi-card-metas-financeiras
		[withFakeData]="true"></adm-bi-card-metas-financeiras>
</ng-template>
<ng-template #biTicketMedioPlanosTmpl>
	<adm-bi-card-ticket-medio-planos
		[withFakeData]="true"></adm-bi-card-ticket-medio-planos>
</ng-template>
<ng-template #biAulasExperimentaisTmpl>
	<adm-bi-card-aulas-experimentais
		[withFakeData]="true"></adm-bi-card-aulas-experimentais>
</ng-template>
<ng-template #biGestaoAcessoTmpl>
	<adm-bi-card-gestao-de-acesso
		[withFakeData]="true"></adm-bi-card-gestao-de-acesso>
</ng-template>
<ng-template #biCobrancasConvenioTmpl>
	<adm-bi-card-cobranca-convenio
		[withFakeData]="true"></adm-bi-card-cobranca-convenio>
</ng-template>
