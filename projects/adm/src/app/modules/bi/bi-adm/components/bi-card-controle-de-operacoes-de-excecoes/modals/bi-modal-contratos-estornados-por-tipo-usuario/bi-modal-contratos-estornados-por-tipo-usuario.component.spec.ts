import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalContratosEstornadosPorTipoUsuarioComponent } from "./bi-modal-contratos-estornados-por-tipo-usuario.component";

describe("BiModalContratosEstornadosPeloAdminComponent", () => {
	let component: BiModalContratosEstornadosPorTipoUsuarioComponent;
	let fixture: ComponentFixture<BiModalContratosEstornadosPorTipoUsuarioComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalContratosEstornadosPorTipoUsuarioComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalContratosEstornadosPorTipoUsuarioComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
