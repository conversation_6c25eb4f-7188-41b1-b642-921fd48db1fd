<div class="bi-card bi-card-gestao-acesso">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span
				class="bi-card-header-label-title typography-title-4 text-color-typography-default-title">
				Gestão de acessos
			</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<ng-container *ngIf="!loading">
			<div class="bi-card-content-section-header">
				<span class="bi-card-content-section-header-title pt-2 pb-2">
					Percetual de acesso
				</span>
			</div>

			<div class="bi-card-content mt-2">
				<div class="bi-card-content-section" *ngIf="tabData2">
					<div
						*ngFor="let section of tabData2?.sections; trackBy: trackByIndex">
						<div
							class="bi-card-content-section-content p-0"
							*ngIf="section?.listItens && section?.listItens.length > 0">
							<div class="bi-card-content-section-content-list">
								<ng-container
									*ngFor="
										let listItem of section?.listItens;
										let odd = odd;
										trackBy: trackByIndex
									">
									<div
										[ngClass]="[
											'bi-card-content-section-content-list-item',
											odd ? 'list-item-even' : 'list-item-odd'
										]">
										<div
											class="bi-card-content-section-content-list-item-text"
											*ngIf="listItem?.text">
											{{ listItem.text }}
										</div>
										<a
											ds3-text-button
											class="bi-card-content-section-content-list-item-value"
											*ngIf="listItem?.value >= 0"
											(click)="openRelatorioAlunos(listItem)">
											{{ listItem.value }}
										</a>
										<div
											class="bi-card-content-section-content-list-item-info"
											*ngIf="listItem?.time">
											{{ listItem.time }}
										</div>
										<div
											class="bi-card-content-section-content-list-item-info"
											*ngIf="listItem?.info">
											{{ listItem.info }}
										</div>
										<div
											class="bi-card-content-section-content-list-item-percentage"
											*ngIf="listItem?.percentage >= 0">
											{{ listItem.percentage }}%
										</div>
									</div>
								</ng-container>
							</div>
							<ds3-diviser class="p-0"></ds3-diviser>
						</div>
					</div>
				</div>
			</div>

			<ds3-diviser></ds3-diviser>

			<div
				class="bi-card-content-section-content bi-card-content-section-content-flex">
				<button
					ds3-text-button
					(click)="openRelatorioIndicator()"
					class="bi-card-content-section-content-action">
					Ver detalhes do gráfico
				</button>
			</div>

			<div
				class="bi-card-content-section-content bi-card-content-section-content-65-35"
				*ngFor="let section of tabData1?.sections">
				<div>
					<ng-container
						*ngFor="let chart of section.chartItens; trackBy: trackByIndex">
						<ds3-chart-column
							*ngIf="chart.type === 'column'"
							[disableAnimations]="true"
							[series]="chart.series"
							[xaxis]="chart.xaxis"
							[yaxis]="chart.yaxis"></ds3-chart-column>
					</ng-container>
				</div>
				<ds3-bi-info
					[vertical]="true"
					[infoData]="section?.infoItens"
					class="bi-card-content-section-content-infos"></ds3-bi-info>
			</div>
		</ng-container>

		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
