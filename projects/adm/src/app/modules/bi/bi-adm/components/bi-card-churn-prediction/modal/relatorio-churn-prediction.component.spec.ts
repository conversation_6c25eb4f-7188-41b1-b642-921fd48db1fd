import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { RelatorioChurnPredictionComponent } from "./relatorio-churn-prediction/relatorio-churn-prediction.component";

describe("RelatorioChurnPredictionComponent", () => {
	let component: RelatorioChurnPredictionComponent;
	let fixture: ComponentFixture<RelatorioChurnPredictionComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [RelatorioChurnPredictionComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(RelatorioChurnPredictionComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
