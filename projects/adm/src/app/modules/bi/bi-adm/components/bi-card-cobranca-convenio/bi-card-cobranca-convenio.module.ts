import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiCardCobrancaConvenioFilterComponent } from "./bi-card-cobranca-convenio-filter/bi-card-cobranca-convenio-filter.component";
import { BiCardCobrancaConvenioComponent } from "./bi-card-cobranca-convenio.component";

@NgModule({
	declarations: [
		BiCardCobrancaConvenioComponent,
		BiCardCobrancaConvenioFilterComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardCobrancaConvenioComponent],
	entryComponents: [BiCardCobrancaConvenioFilterComponent],
})
export class BiCardCobrancaConvenioModule {}
