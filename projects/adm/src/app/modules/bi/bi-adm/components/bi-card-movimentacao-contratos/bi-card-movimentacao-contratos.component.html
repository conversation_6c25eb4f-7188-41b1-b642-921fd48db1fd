<div class="bi-card movimentacao-contratos">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Movimentações de contratos</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjudaMovimentacoesContratos()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.dataInicio | date : "shortDate" }} -
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGruposColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupos de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGruposColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradoresObj;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradoresObj;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>
		</div>
		<div class="bi-card-content-section" *ngIf="tabDataTop && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabDataTop.sections; trackBy: trackByIndex">
				<ng-container *ngIf="section.infoItens">
					<p
						*ngIf="section.infoTitle"
						class="bi-card-content-section-content-list-title pct-title5 cor-type-default-text">
						{{ section?.infoTitle }}
					</p>
					<ds3-bi-info
						[infoData]="section?.infoItens"
						(clickEvent)="openModalInfo($event)"></ds3-bi-info>
				</ng-container>
			</div>
			<ds3-diviser></ds3-diviser>
		</div>

		<ds3-table style="padding-bottom: unset">
			<table
				ds3DataTable
				[stateManager]="tableState"
				[smallRows]="true"
				style="table-layout: fixed">
				<ng-container ds3TableColumn="label">
					<th *ds3TableHeaderCell style="width: 50%; font-size: 14px">
						Mês atual
					</th>
					<td
						*ds3TableCell="let item"
						style="width: 50%; font-size: 14px; line-height: 125%">
						<span [class.text-color-feedback-loss-2]="item?.type === 'SD'">
							{{ item?.label }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="dia">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px">
						Hoje
					</th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span [class.text-color-feedback-loss-2]="item?.type === 'SD'">
							{{ item?.valueDia }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="hoje">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px">
						Até Hoje
					</th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span [class.text-color-feedback-loss-2]="item?.type === 'SD'">
							{{ item?.valueHoje }}
						</span>
					</td>
				</ng-container>

				<tr *ds3TableRow></tr>
				<tr *ds3TableEmptyRow class="ds3-table-empty">
					<td></td>
				</tr>

				<tbody *ds3TableLoading>
					<tr>
						<td>
							<div class="bi-modal-table-loader" role="status">
								<img
									alt="Loading pacto"
									src="pacto-ui/images/gif/loading-pacto.gif" />
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</ds3-table>
		<div class="bi-card-content-section" *ngIf="tabDataMiddle && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabDataMiddle.sections; trackBy: trackByIndex">
				<ng-container *ngIf="section?.alertItens">
					<div
						*ngFor="let alertItem of section?.alertItens"
						[class]="
							'bi-card-content-section-alert bi-card-content-section-alert-' +
							alertItem.state
						">
						<div class="bi-card-content-section-alert-label">
							{{ alertItem?.label }}
						</div>
						<div class="bi-card-content-section-alert-value">
							<i [class]="alertItem?.beforeIcon?.class"></i>
							{{ alertItem?.value }}
						</div>
					</div>
				</ng-container>
			</div>
		</div>
		<ds3-table
			style="padding-bottom: unset"
			*ngIf="
				usaPlanoRecorrenteCompartilhado || exibirAgregadoresMovimentacaoContrato
			">
			<table
				ds3DataTable
				[stateManager]="tableStateDependentes"
				[smallRows]="true"
				style="table-layout: fixed">
				<ng-container ds3TableColumn="label">
					<th
						*ds3TableHeaderCell
						style="
							width: 50%;
							border: none;
							font-size: 14px;
							line-height: 125%;
						"></th>
					<td
						*ds3TableCell="let item"
						style="width: 50%; font-size: 14px; line-height: 125%">
						<span [class.text-color-feedback-loss-2]="item?.type === 'SD'">
							{{ item?.label }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="dia">
					<th
						*ds3TableHeaderCell
						style="
							text-align: right;
							width: 20%;
							border: none;
							font-size: 14px;
							line-height: 125%;
						"></th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span [class.text-color-feedback-loss-2]="item?.type === 'SD'">
							{{ item?.valueDia }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="hoje">
					<th
						*ds3TableHeaderCell
						style="
							text-align: right;
							width: 20%;
							border: none;
							font-size: 14px;
							line-height: 125%;
						"></th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span [class.text-color-feedback-loss-2]="item?.type === 'SD'">
							{{ item?.valueHoje }}
						</span>
					</td>
				</ng-container>

				<tr *ds3TableRow></tr>
				<tr *ds3TableEmptyRow class="ds3-table-empty">
					<td></td>
				</tr>

				<tbody *ds3TableLoading>
					<tr>
						<td>
							<div class="bi-modal-table-loader" role="status">
								<img
									alt="Loading pacto"
									src="pacto-ui/images/gif/loading-pacto.gif" />
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</ds3-table>
		<div class="bi-card-content-section" *ngIf="tabDataBottom && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabDataBottom.sections; trackBy: trackByIndex">
				<ng-container *ngIf="section.infoItens">
					<p
						*ngIf="section.infoTitle"
						class="bi-card-content-section-content-list-title pct-title5 cor-type-default-text">
						{{ section?.infoTitle }}
					</p>
					<ds3-bi-info
						[infoData]="section?.infoItens"
						(clickEvent)="openModalInfo($event)"></ds3-bi-info>
				</ng-container>
				<div
					class="bi-card-content-section-content-list"
					*ngIf="section.listItens">
					<div class="bi-card-content-section-content-list-head">
						<p
							*ngIf="section.listTitle"
							class="bi-card-content-section-content-list-title pct-title5 cor-type-default-text">
							{{ section.listTitle }}
						</p>
					</div>
					<div class="bi-card-content-section-content-list">
						<ng-container
							*ngFor="
								let listItem of section?.listItens;
								let odd = odd;
								trackBy: trackByIndex
							">
							<div
								(click)="openModalItem(listItem)"
								[ngClass]="[
									'bi-card-content-section-content-list-item',
									odd ? 'list-item-even' : 'list-item-odd'
								]">
								<div
									class="bi-card-content-section-content-list-item-text"
									*ngIf="listItem?.text">
									{{ listItem.text }}
								</div>
								<div
									class="bi-card-content-section-content-list-item-value"
									*ngIf="listItem?.value >= 0">
									{{ listItem.value }}
								</div>
								<div
									class="bi-card-content-section-content-list-item-value"
									*ngIf="listItem?.secondValue >= 0">
									{{ listItem.secondValue }}
								</div>
							</div>
						</ng-container>
					</div>
				</div>
				<ng-container *ngIf="section?.alertItens">
					<div
						*ngFor="let alertItem of section?.alertItens"
						[class]="
							'bi-card-content-section-alert bi-card-content-section-alert-' +
							alertItem.state
						">
						<div class="bi-card-content-section-alert-label">
							{{ alertItem?.label }}
						</div>
						<div class="bi-card-content-section-alert-value">
							<i [class]="alertItem?.beforeIcon?.class"></i>
							{{ alertItem?.value }}
						</div>
					</div>
				</ng-container>
			</div>
		</div>

		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		xingling="bi-movimentacao-contratos:contratos-ativos"
		i18n="@@adm:bi-movimentacao-contratos:contratos-ativos">
		Contratos ativos
	</span>
	<span
		xingling="bi-movimentacao-contratos:contratos-vencidos"
		i18n="@@adm:bi-movimentacao-contratos:contratos-vencidos">
		Contratos vencidos
	</span>
	<span
		xingling="bi-movimentacao-contratos:todos-os-contratos"
		i18n="@@adm:bi-movimentacao-contratos:todos-os-contratos">
		Todos os contratos
	</span>
	<span
		xingling="bi-movimentacao-contratos:dependentes"
		i18n="@@adm:bi-movimentacao-contratos:dependentes">
		Dependentes
	</span>
	<span
		xingling="bi-movimentacao-contratos:todos-os-contratos"
		i18n="@@adm:bi-movimentacao-contratos:todos-os-contratos">
		Todos os contratos
	</span>
	<span
		xingling="bi-movimentacao-contratos:fechamento-mes-anterior"
		i18n="@@adm:bi-movimentacao-contratos:fechamento-mes-anterior">
		Fechamento do mês anterior
	</span>
	<span
		xingling="bi-movimentacao-contratos:matriculados"
		i18n="@@adm:bi-movimentacao-contratos:matriculados">
		Matriculados
	</span>
	<span
		xingling="bi-movimentacao-contratos:rematriculados"
		i18n="@@adm:bi-movimentacao-contratos:rematriculados">
		Rematriculados
	</span>
	<span
		xingling="bi-movimentacao-contratos:retorno-trancamento"
		i18n="@@adm:bi-movimentacao-contratos:retorno-trancamento">
		Retorno de trancamento
	</span>
	<span
		xingling="bi-movimentacao-contratos:contratos-transferidos"
		i18n="@@adm:bi-movimentacao-contratos:contratos-transferidos">
		Contratos transferidos
	</span>
	<span
		xingling="bi-movimentacao-contratos:dependentes-vinculados"
		i18n="@@adm:bi-movimentacao-contratos:dependentes-vinculados">
		Dependentes vinculados
	</span>
	<span
		xingling="bi-movimentacao-contratos:total-entradas"
		i18n="@@adm:bi-movimentacao-contratos:total-entradas">
		Total de entradas
	</span>
	<span
		xingling="bi-movimentacao-contratos:cancelados"
		i18n="@@adm:bi-movimentacao-contratos:cancelados">
		Cancelados
	</span>
	<span
		xingling="bi-movimentacao-contratos:trancados"
		i18n="@@adm:bi-movimentacao-contratos:trancados">
		Trancados
	</span>
	<span
		xingling="bi-movimentacao-contratos:desistencia"
		i18n="@@adm:bi-movimentacao-contratos:desistencia">
		Desistência
	</span>
	<span
		xingling="bi-movimentacao-contratos:dependentes-desvinculados"
		i18n="@@adm:bi-movimentacao-contratos:dependentes-desvinculados">
		Dependentes desvinculados
	</span>
	<span
		xingling="bi-movimentacao-contratos:total-saidas"
		i18n="@@adm:bi-movimentacao-contratos:total-saidas">
		Total de saídas
	</span>
	<span
		xingling="bi-movimentacao-contratos:resultado-mes"
		i18n="@@adm:bi-movimentacao-contratos:resultado-mes">
		Movimentação do mês
	</span>
	<span
		xingling="bi-movimentacao-contratos:movimentacao-de-contratos"
		i18n="@@adm:bi-movimentacao-contratos:movimentacao-de-contratos">
		Movimentação de contratos
	</span>
	<span
		xingling="bi-movimentacao-contratos:movimentacao-dependentes"
		i18n="@@adm:bi-movimentacao-contratos:movimentacao-dependentes">
		Movimentação de dependentes
	</span>
</pacto-traducoes-xingling>
