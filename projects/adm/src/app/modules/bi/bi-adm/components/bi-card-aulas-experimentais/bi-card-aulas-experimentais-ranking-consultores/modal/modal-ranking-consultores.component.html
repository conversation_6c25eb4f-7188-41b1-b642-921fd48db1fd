Em construção
<!-- <pacto-relatorio
	#tableData     
	id="ranking-consultores-aulas-experimentais-modal"
	idSuffix="ranking-consultores"
	class="relatorioIcvRankingConsultores"
    [customEmptyContent]="emptyState"    
	[showShare]="false"
	[table]="table"
	[enableDs3]="true"
></pacto-relatorio>

<ng-template #celulaConsultor let-consultor="item" let-index="index">
	<div class="icv-rc-celula-consultor">
		<img src="pacto-ui/images/medal-gold.png" width="17" height="25" *ngIf="index === 0" />
		<img src="pacto-ui/images/medal-silver.png" width="17" height="25" *ngIf="index === 1" />
		<img src="pacto-ui/images/medal-bronze.png" width="17" height="25" *ngIf="index === 2" />
		<span>{{consultor?.consultor?.nome}}</span>
	</div>
</ng-template> -->
