import { BiInadimplenciaFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-inadimplencia/bi-inadimplencia-filter/bi-inadimplencia-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { Util } from "@adm/util/Util";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe, DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiMsApiInadimplenciaService,
	FiltroInadimplencia,
	InadimplenciaResponseModel,
} from "bi-ms-api";
import { ApexTooltip } from "ng-apexcharts";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { TabData } from "../../bi-adm.model";

@Component({
	selector: "adm-bi-card-inadimplencia",
	templateUrl: "./bi-card-inadimplencia.component.html",
	styleUrls: ["./bi-card-inadimplencia.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardInadimplenciaComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	loading: boolean = false;
	tabData: TabData;
	showAsTable = true;
	colorOrder = [
		"green",
		"red",
		"pink",
		"purple",
		"yellow",
		"blue",
		"orange",
		"lightblue",
	];

	tooltipChart: ApexTooltip;

	dataLabelsChart: ApexDataLabels = {
		enabled: true,
		enabledOnSeries: [0, 1],
		textAnchor: "middle",
	};

	constructor(
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private biMsApiInadimplenciaService: BiMsApiInadimplenciaService,
		private decimalPipe: DecimalPipe,
		private toastrService: ToastrService,
		protected datePipe: DatePipe,
		protected breakpointObserver: BreakpointObserver,
		protected biCommonService: BiCommonService,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this._configureTooltipChart();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		this.destroy();
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Inadimplência",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/o-sistema-possui-alguma-ferramenta-que-permita-visualizar-as-inadimplencias-da-minha-empresa/",
			module: ConhecimentoEnum.ADM,
		});
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull = false) {
		this.loading = true;
		const filtroInadimplencia: FiltroInadimplencia = {
			atualizarAgora: reloadFull,
			dataBase: this.filtros.data.getTime(),
			empresas: this.filtros.empresa ? [this.filtros.empresa] : [],
			colaboradores: this.filtros.colaboradores,
			desconsiderarCanceladas: this.filtros.desconsiderarCanceladas,
			diaPagamento: this.filtros.diaPagamento,
		};

		if (this.filtros.dataMatricula) {
			filtroInadimplencia.dataMatricula = this.filtros.dataMatricula.getTime();
		}

		this.cd.detectChanges();
		this.biMsApiInadimplenciaService
			.list({
				filtroInadimplencia,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: InadimplenciaResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(BiInadimplenciaFilterComponent, {
			globalFilter: this.globalFilterForm.value,
			filters: this.filtros,
		});

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	trackByIndex(index, item) {
		return index;
	}

	private _populateTabData(v?: InadimplenciaResponseModel) {
		if (!v) {
			v = this._populateWithDefaultValues();
		}
		const dadosGrafico = {
			meses: new Array<string>(),
			recebidos: new Array<number>(),
			inadimplencia: new Array<number>(),
			previsao: new Array<number>(),
			porcentagemEficiencia: new Array<number>(),
			porcentagemInadimplencia: new Array<number>(),
			quantidadeAlunosPrevisao: new Array<number>(),
			quantidadeAlunosPagas: new Array<number>(),
			quantidadeAlunosInadimplencia: new Array<number>(),
			quantidadeAlunosCanceladas: new Array<number>(),
		};

		v.dadosGraficoInadimplencia.forEach((dgi) => {
			dadosGrafico.meses.push(dgi.mes);
			dadosGrafico.recebidos.push(dgi.valorPago || 0);
			dadosGrafico.inadimplencia.push(dgi.valorEmAberto || 0);
			dadosGrafico.previsao.push(dgi.valorTotal || 0);
			dadosGrafico.porcentagemEficiencia.push(
				this.roundTo(100 - (dgi.inadimplencia || 0), 2)
			);
			dadosGrafico.porcentagemInadimplencia.push(dgi.inadimplencia || 0);
			dadosGrafico.quantidadeAlunosPrevisao.push(
				dgi.quantidadeAlunosPrevisao || 0
			);
			dadosGrafico.quantidadeAlunosPagas.push(dgi.quantidadeAlunosPagas || 0);
			dadosGrafico.quantidadeAlunosInadimplencia.push(
				dgi.quantidadeAlunosInadimplencia || 0
			);
			dadosGrafico.quantidadeAlunosCanceladas.push(
				dgi.quantidadeAlunosCanceladas || 0
			);
		});

		const dadosTabela = {
			meses: new Array<string>(),
			recebidos: new Array<number>(),
			inadimplencia: new Array<number>(),
			previsao: new Array<number>(),
			porcentagemEficiencia: new Array<number>(),
			porcentagemInadimplecia: new Array<number>(),
			quantidadeAlunosPrevisao: new Array<number>(),
			quantidadeAlunosPagas: new Array<number>(),
			quantidadeAlunosInadimplencia: new Array<number>(),
			quantidadeAlunosCanceladas: new Array<number>(),
		};

		v.totalizador.forEach((total) => {
			if (total.previsao.mesReferencia) {
				let mes = this.datePipe.transform(total.previsao.mesReferencia, "MMMM");
				mes = mes.charAt(0).toUpperCase() + mes.substring(1);
				dadosTabela.meses.push(mes);
			}
			dadosTabela.meses.push("Média");

			dadosTabela.inadimplencia.push(total.inadimplencia.valor);
			dadosTabela.porcentagemInadimplecia.push(total.inadimplencia.porcentagem);
			dadosTabela.quantidadeAlunosInadimplencia.push(
				total.inadimplencia.quantidadeAlunos
			);
			dadosTabela.recebidos.push(total.pagas.valor);
			dadosTabela.quantidadeAlunosPagas.push(total.pagas.quantidadeAlunos);
			dadosTabela.previsao.push(total.previsao.valor);
			dadosTabela.quantidadeAlunosPrevisao.push(
				total.previsao.quantidadeAlunos
			);
		});

		this.tabData = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.previsaoBi.valor,
								size: "24px",
								isMonetary: true,
								state: "info",
							},
							overline: {
								text: "Previsão",
							},
							textButton: `${v.previsaoBi.quantidadeAlunos} alunos`,
							textButtonHint:
								"Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> do <b>mês</b>.",
						},
						{
							info: {
								value: v.pagasBi.valor,
								size: "24px",
								isMonetary: true,
								state: "gain",
							},
							overline: {
								text: "Recebido",
							},
							textButton: `${v.pagasBi.quantidadeAlunos} alunos`,
							textButtonHint: `Quantidade de alunos que possuem <b>parcelas</b> na <b>previsão</b> e que estão <b>pagas</b>. <br>Pode ocorrer dela ter sido paga em outros meses.`,
						},
						{
							info: {
								value: v.inadimplenciaBi.valor,
								size: "20px",
								isMonetary: true,
								state: "loss",
							},
							overline: {
								text: "Inadimplência",
							},
							textButton: `${Util.roundTo(
								v.inadimplenciaBi.porcentagem || 0,
								2
							)}%`,
							textButtonHint: `Percentual da <b>Inadimplência</b>.`,
						},
					],
				},
				{
					title: "Últimos 4 meses",
					chartItens: {
						labels: dadosGrafico.meses,
						series: [
							{
								name: "Recebido",
								type: "column",
								data: dadosGrafico.recebidos,
							},
							{
								name: "Inadimplência",
								type: "column",
								data: dadosGrafico.inadimplencia,
							},
							{
								name: "Previsão",
								type: "line",
								data: dadosGrafico.previsao,
							},
							{
								name: "Eficiência (%)",
								type: "line",
								data: dadosGrafico.porcentagemEficiencia,
							},
							{
								name: "Inadimplência (%)",
								type: "line",
								data: dadosGrafico.porcentagemInadimplencia,
							},
						],
						xaxis: {
							type: "category",
							categories: dadosGrafico.meses,
							tooltip: {
								enabled: false,
							},
						},
						yaxis: [
							{
								seriesName: "Recebido",
								opposite: true,
								labels: {
									formatter: (val) => {
										return val.toFixed(0);
									},
								},
							},
							{
								seriesName: "Recebido",
								show: false,
							},
							{
								seriesName: "Recebido",
								show: false,
							},
							{
								seriesName: "Eficiência (%)",
								min: 0,
								max: 100,
								stepSize: 20,
								tickAmount: 5,
								labels: {
									formatter: (val) => {
										return val.toFixed(0);
									},
								},
							},
							{
								seriesName: "Eficiência (%)",
								show: false,
							},
						],
					},
					tableItens: [
						{
							title: "Previsão (R$)",
							values: dadosTabela.previsao,
							monetary: true,
							isActionable: false,
						},
						{
							title: "Qtd. Alunos com parcela na previsão",
							values: dadosTabela.quantidadeAlunosPrevisao,
							isActionable: true,
						},
						{
							title: "Recebido (R$)",
							monetary: true,
							values: dadosTabela.recebidos,
							isActionable: false,
						},
						{
							title: "Qtd. Alunos com parcela na previsão e que estão pagas",
							values: dadosTabela.quantidadeAlunosPagas,
							isActionable: true,
						},
						{
							title: "Inadimplência (R$)",
							monetary: true,
							values: dadosTabela.inadimplencia,
							isActionable: false,
						},
						{
							title:
								"Qtd. Alunos com parcela na previsão e que estão em aberto",
							values: dadosTabela.quantidadeAlunosInadimplencia,
							isActionable: true,
						},
						{
							title: "Percentual da inadimplência",
							percentage: true,
							values: dadosTabela.porcentagemInadimplecia,
							isActionable: false,
						},
					],
				},
				{
					infoItens: [
						{
							info: {
								value: v.previsaoTotalBi.valor,
								size: "24px",
								isMonetary: true,
								state: "info",
							},
							overline: {
								text: "Previsão",
							},
							textButton: `${v.previsaoTotalBi.quantidadeAlunos} alunos`,
							textButtonHint:
								"Total de alunos que possuem parcelas na previsão dos últimos 4 meses.",
						},
						{
							info: {
								value: v.pagasTotalBi.valor,
								size: "24px",
								isMonetary: true,
								state: "gain",
							},
							overline: {
								text: "Recebido",
							},
							textButton: `${v.pagasTotalBi.quantidadeAlunos} alunos`,
							textButtonHint: `Total de alunos que possuem parcelas <b>Pagas</b> da <b>previsão</b> dos últimos 4 meses. <br>Pode ocorrer dela ter sido paga em outros meses.`,
						},
						{
							info: {
								value: v.inadimplenciaTotalBi.valor,
								size: "20px",
								isMonetary: true,
								state: "loss",
							},
							overline: {
								text: "Inadimplência",
							},
							textButton: `${Util.roundTo(
								v.inadimplenciaTotalBi.porcentagem || 0,
								2
							)}%`,
							textButtonHint:
								"Percentual da <strong>Inadimplência</strong> dos últimos 4 meses.",
						},
					],
				},
			],
		};
	}

	private _populateWithDefaultValues() {
		return {
			totalizador: [
				{
					previsao: {
						label: "",
						valor: 0,
						quantidade: 0,
						porcentagem: 0,
						mesReferencia: 0,
						quantidadeAlunos: 0,
					},
					pagas: {
						label: "",
						valor: 0,
						quantidade: 0,
						porcentagem: 0,
						mesReferencia: 0,
						quantidadeAlunos: 0,
					},
					inadimplencia: {
						label: "",
						valor: 0,
						quantidade: 0,
						porcentagem: 0,
						mesReferencia: 0,
						quantidadeAlunos: 0,
					},
					media: false,
				},
			],
			inadimplenciaBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			previsaoBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			pagasBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			canceladasBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			inadimplenciaTotalBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			previsaoTotalBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			pagasTotalBi: {
				label: "",
				valor: 0,
				quantidade: 0,
				porcentagem: 0,
				mesReferencia: 0,
				quantidadeAlunos: 0,
			},
			dadosGraficoInadimplencia: [
				{
					mes: "",
					valorPago: 0,
					valorEmAberto: 0,
					inadimplencia: 0,
					valorTotal: 0,
					quantidadeAlunosPrevisao: 0,
					quantidadeAlunosPagas: 0,
					quantidadeAlunosInadimplencia: 0,
					quantidadeAlunosCanceladas: 0,
				},
			],
		};
	}

	openTableItem(tableItem: {
		title?: string;
		values?: any[];
		isActionable?: boolean;
	}) {}

	roundTo(num: number, decimals: number): number {
		return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
	}

	private _configureTooltipChart() {
		this.tooltipChart = {
			shared: true,
			custom: ({ series, seriesIndex, dataPointIndex, w }) => {
				const label = w.globals.categoryLabels[dataPointIndex];
				const seriesNames = w.globals.seriesNames;

				let html = `
				<div style="padding: 8px 12px">
					<div style="padding-bottom: 8px; border-bottom: 1px solid white; margin-bottom: 8px">
						<span class="typography-overline-1">Últimos 4 meses</span>
					</div>
			`;

				seriesNames.forEach((serieName, index) => {
					let value = series[index][dataPointIndex];
					if (index >= 0 && index <= 2) {
						value = this.currencyPipe.transform(value, "BRL");
					} else {
						value = `${this.decimalPipe.transform(value, "1.2-2")}%`;
					}
					html += `
						<div style="margin-bottom: 8px; padding: 4px 8px; display: flex; align-items: center">
							<span style="display: inline-block; width: 16px; height: 16px; background-color: ${w.globals.colors[index]}; border-radius: 50%; border: 1px solid white; margin-right: 8px"></span>
							<span class="typography-overline-2">${serieName}:&nbsp;${value}</span>
						</div>
					`;
				});
				html += `
				</div>
			`;
				return html;
			},
		};
	}

	exportRelatorioInadimplencia() {}
}
