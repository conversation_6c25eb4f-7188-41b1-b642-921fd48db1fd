import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalOperacoesContratoRetroativosComponent } from "./bi-modal-operacoes-contrato-retroativos.component";

describe("BiModalOperacoesContratoRetroativosComponent", () => {
	let component: BiModalOperacoesContratoRetroativosComponent;
	let fixture: ComponentFixture<BiModalOperacoesContratoRetroativosComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalOperacoesContratoRetroativosComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalOperacoesContratoRetroativosComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
