import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalClientesComBonusComponent } from "./bi-modal-clientes-com-bonus.component";

describe("BiModalClientesComBonusComponent", () => {
	let component: BiModalClientesComBonusComponent;
	let fixture: ComponentFixture<BiModalClientesComBonusComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalClientesComBonusComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalClientesComBonusComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
