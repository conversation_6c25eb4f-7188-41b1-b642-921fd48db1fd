<div class="bi-card pendencias-de-clientes">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Pendências de clientes</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				(click)="openSideNav()"
				ds3-outlined-button
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGruposColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupos de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGruposColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradoresObj;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradoresObj;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabData.sections; trackBy: trackByIndex">
				<div class="bi-card-content-section-content-infos">
					<ds3-bi-info
						[infoData]="section?.infoItens"
						(clickEvent)="openModalInfo($event)"></ds3-bi-info>
				</div>
				<div
					class="bi-card-content-section-content-list"
					*ngIf="section.listItens && section?.listItens.length > 0">
					<p class="bi-card-content-section-content-title">
						{{ section.title }}
					</p>
					<ng-container
						*ngFor="
							let listItem of section?.listItens;
							let odd = odd;
							trackBy: trackByIndex
						">
						<div
							*ngIf="listItem?.value > 0 || showHiddenFields"
							class="bi-card-content-section-content-list-item bi-card-content-section-content-list-item-revert-color"
							(click)="openModalItem(listItem)">
							<div
								class="bi-card-content-section-content-list-item-text"
								*ngIf="listItem?.text">
								{{ listItem.text }}
							</div>
							<button
								ds3-text-button
								size="sm"
								class="bi-card-content-section-content-list-item-value">
								{{ listItem.value }}
							</button>
							<div
								class="bi-card-content-section-content-list-item-percentage"
								*ngIf="listItem?.percentage >= 0">
								{{ listItem.percentage }}%
							</div>
						</div>
					</ng-container>
					<div class="bi-card-content-section-content-list-actions">
						<button ds3-text-button (click)="mostrarMais()">
							{{ showHiddenFields ? "Contrair" : "Expandir" }} listagem
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		xingling="bi-pendencias:parcelas-em-atraso"
		i18n="@@adm:bi-pendencias:parcelas-em-atraso">
		em atraso
	</span>
	<span
		xingling="bi-pendencias:parcelas-a-pagar"
		i18n="@@adm:bi-pendencias:a-pagar">
		a pagar
	</span>
	<span
		xingling="bi-pendencias:colab-parcelas-a-pagar"
		i18n="@@adm:bi-pendencias:colab-parcelas-a-pagar">
		colab. c/ parcelas a pagar
	</span>
	<span
		xingling="bi-pendencias:cliente-debito-conta-corrente"
		i18n="@@adm:bi-pendencias:cliente-debito-conta-corrente">
		com débito em conta corrente
	</span>
	<span
		xingling="bi-pendencias:cliente-credito-conta-corrente"
		i18n="@@adm:bi-pendencias:cliente-credito-conta-corrente">
		com crédito em conta corrente
	</span>
	<span
		xingling="bi-pendencias:clientes-aniversariantes-hoje"
		i18n="@@adm:bi-pendencias:clientes-aniversariantes-hoje">
		Clientes que fazem aniversário hoje
	</span>
	<span
		xingling="bi-pendencias:colab-aniversariantes-hoje"
		i18n="@@adm:bi-pendencias:colab-aniversariantes-hoje">
		Colaboradores que fazem aniversário hoje
	</span>
	<span
		xingling="bi-pendencias:bvs-pendentes"
		i18n="@@adm:bi-pendencias:bvs-pendentes">
		BV’s pendentes
	</span>
	<span
		xingling="bi-pendencias:clientes-com-cadastro-incompleto"
		i18n="@@adm:bi-pendencias:clientes-com-cadastro-incompleto">
		Clientes com cadastro incompleto
	</span>
	<span
		xingling="bi-pendencias:visitantes-com-cadastro-incompleto"
		i18n="@@adm:bi-pendencias:visitantes-com-cadastro-incompleto">
		Visitantes com cadastro incompleto
	</span>
	<span
		xingling="bi-pendencias:clientes-sem-assinatura-de-contrato"
		i18n="@@adm:bi-pendencias:clientes-sem-assinatura-de-contrato">
		Sem assinatura de contrato
	</span>
	<span
		xingling="bi-pendencias:clientes-sem-assinatura-de-cancelamento-de-contrato"
		i18n="
			@@adm:bi-pendencias:clientes-sem-assinatura-de-cancelamento-de-contrato">
		Sem assinatura de cancelamento de contrato
	</span>
	<span
		xingling="bi-pendencias:clientes-sem-reconhecimento-facial"
		i18n="@@adm:bi-pendencias:clientes-sem-reconhecimento-facial">
		Sem reconhecimento facial
	</span>
	<span
		xingling="bi-pendencias:clientes-sem-foto"
		i18n="@@adm:bi-pendencias:clientes-sem-foto">
		Sem foto
	</span>
	<span
		xingling="bi-pendencias:clientes-sem-geolocalizacao"
		i18n="@@adm:bi-pendencias:clientes-sem-geolocalizacao">
		Sem geolocalização
	</span>
	<span
		xingling="bi-pendencias:clientes-sem-produtos"
		i18n="@@adm:bi-pendencias:clientes-sem-produtos">
		Sem produtos
	</span>
	<span
		xingling="bi-pendencias:clientes-com-trancamento-vencido"
		i18n="@@adm:bi-pendencias:clientes-com-trancamento-vencido">
		Trancamento vencido
	</span>
	<span
		xingling="bi-pendencias:alunos-com-mais-de-um-professor"
		i18n="@@adm:bi-pendencias:alunos-com-mais-de-um-professor">
		Alunos com mais de um professor
	</span>
</pacto-traducoes-xingling>
