import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Inject,
	OnInit,
	ViewChild,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from "@angular/material";
import { GridFilterConfig, PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "adm-relatorio-churn-prediction",
	templateUrl: "./relatorio-churn-prediction.component.html",
	styleUrls: ["./relatorio-churn-prediction.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RelatorioChurnPredictionComponent implements OnInit {
	@ViewChild("colunacodigo", { static: true }) colunacodigo;
	@ViewChild("colunaDescricao", { static: true }) colunaDescricao;
	@ViewChild("colunaEmpresa", { static: true }) colunaEmpresa;
	@ViewChild("colunaNvezes", { static: true }) colunaNvezes;
	@ViewChild("tableData", { static: true }) tableData;

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	recursoUsuario: any = {};

	constructor(
		@Inject(MAT_DIALOG_DATA) public data: any,
		public dialogRef: MatDialogRef<RelatorioChurnPredictionComponent>,
		public dialog: MatDialog,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	ngAfterViewInit(): void {
		this.initTable();
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			quickSearch: true,
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return {
					content: [
						{ nome: "João", matricula: "123", chancePerda: 1 },
						{ nome: "João", matricula: "123", chancePerda: 1 },
						{ nome: "João", matricula: "123", chancePerda: 1 },
						{ nome: "João", matricula: "123", chancePerda: 1 },
					],
					totalElements: 4,
					totalPages: 1,
					size: 10,
				};
			},
			columns: [
				{
					nome: "nome",
					titulo: "Nome",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "matricula",
					titulo: "Matricula",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "chancePerda",
					titulo: "Chance de deixar a academia",
					mostrarTitulo: true,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
					styleClass: "center",
				},
			],
		});
		this.cd.detectChanges();
	}

	closeModal() {
		this.dialogRef.close();
	}
}
