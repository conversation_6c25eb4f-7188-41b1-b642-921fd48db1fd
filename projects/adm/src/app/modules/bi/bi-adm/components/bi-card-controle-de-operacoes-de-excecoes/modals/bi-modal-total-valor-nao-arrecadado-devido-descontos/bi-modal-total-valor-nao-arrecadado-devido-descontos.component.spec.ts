import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalTotalValorNaoArrecadadoDevidoDescontosComponent } from "./bi-modal-total-valor-nao-arrecadado-devido-descontos.component";

describe("BiModalTotalValorNaoArrecadadoDevidoDescontosComponent", () => {
	let component: BiModalTotalValorNaoArrecadadoDevidoDescontosComponent;
	let fixture: ComponentFixture<BiModalTotalValorNaoArrecadadoDevidoDescontosComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalTotalValorNaoArrecadadoDevidoDescontosComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalTotalValorNaoArrecadadoDevidoDescontosComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
