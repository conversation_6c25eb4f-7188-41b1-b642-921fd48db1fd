<div class="bi-card indice-renovacao">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Índice de renovação</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.dataInicio | date : "shortDate" }} -
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGruposColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupos de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGruposColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradoresObj;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradoresObj;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="
					let section of tabData.sections;
					let firstSection = first;
					trackBy: trackByIndex
				">
				<div class="bi-card-content-section-content-header">
					<span class="bi-card-content-section-content-title">
						{{ section.title }}
					</span>
					<span
						class="bi-card-content-section-header-info"
						*ngIf="firstSection">
						Dia {{ tabData?.diaInicio | date : "dd" }} até
						{{ tabData?.diaFim | date : "dd" }}
					</span>
				</div>
				<div class="bi-card-content-section-content-list">
					<ng-container
						*ngFor="
							let listItem of section?.listItens;
							let odd = odd;
							trackBy: trackByIndex
						">
						<div
							class="bi-card-content-section-content-list-item bi-card-content-section-content-list-item-revert-color">
							<div
								class="bi-card-content-section-content-list-item-text"
								*ngIf="listItem?.text">
								{{ listItem?.text }}
								<span
									*ngIf="listItem.auxText"
									[class]="listItem.auxTextColor"
									[ds3Tooltip]="listItem.auxTextHint">
									{{ listItem.auxText }}
								</span>
							</div>
							<button
								ds3-text-button
								size="sm"
								(click)="openModalItem(listItem)"
								class="bi-card-content-section-content-list-item-value">
								{{ listItem?.value }}
							</button>
							<div class="bi-card-content-section-content-list-item-percentage">
								<span *ngIf="listItem?.showPercentage">
									{{ listItem?.percentage || 0 | number : "1.2-2" }}%
								</span>
								<span *ngIf="!listItem?.showPercentage">-</span>
							</div>
						</div>
					</ng-container>
				</div>
			</div>
		</div>

		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		xingling="bi-indc-rnv-previsao-title"
		i18n="@@adm:bi-indc-rnv-previsao-title">
		Previsão do início ao final do mês
	</span>

	<span
		xingling="bi-indc-rnv-alunos-ativos-previsao-rnv-overline"
		i18n="@@adm:bi-indc-rnv-alunos-ativos-previsao-rnv-overline">
		Alunos ativos na previsão de renovação
	</span>

	<span
		xingling="bi-indc-rnv-alunos-ativos-previsao-rnv-perc-overline"
		i18n="@@adm:bi-indc-rnv-alunos-ativos-previsao-rnv-perc-overline">
		Alunos ativos estão nesta previsão de renovação
	</span>

	<span
		xingling="bi-indc-rnv-contratos-previsao-renovados-atual-text"
		i18n="@@adm:bi-indc-rnv-contratos-previsao-renovados-atual-text">
		Contratos da previsão renovados em {{ filtros?.dataInicio | date : "MMMM" }}
	</span>

	<span
		xingling="bi-indc-rnv-contratos-previsao-renovados-next-month-text"
		i18n="@@adm:bi-indc-rnv-contratos-previsao-renovados-atual-text">
		Contratos da previsão renovados em
		{{ nextMonthDate | date : "MMMM" }} devido a carência de renovação
	</span>

	<span
		xingling="bi-indc-rnv-contratos-previsao-nao-renovados"
		i18n="@@adm:bi-indc-rnv-contratos-previsao-nao-renovados">
		Contratos da previsão que não foram renovados
	</span>

	<span
		xingling="bi-indc-rnv-contratos-renovados-mes"
		i18n="@@adm:bi-indc-rnv-contratos-renovados-mes">
		Contratos renovados neste mês
	</span>

	<span
		xingling="bi-indc-rnv-contratos-vencidos-mes-anterior-renovados-mes-atual"
		i18n="
			@@adm:bi-indc-rnv-contratos-vencidos-mes-anterior-renovados-mes-atual">
		Contratos que venceram no mês anterior, mas foram renovados no mês atual.
	</span>

	<span
		xingling="bi-ind-rnv-contratos-previstos-vencer-mes-atual-ja-renovados"
		i18n="@@adm:bi-ind-rnv-contratos-previstos-vencer-mes-atual-ja-renovados">
		Contratos que estão previstos para vencer no mês atual e já foram renovados
	</span>

	<span
		xingling="bi-ind-rnv-contratos-previstos-vencer-mes-futuro-ja-renovados"
		i18n="@@adm:bi-ind-rnv-contratos-previstos-vencer-mes-futuro-ja-renovados">
		Contratos que estão previstos para vencer em meses futuros e já foram
		renovados antecipadamente
	</span>

	<span
		xingling="bi-indc-rnv-contratos-renovados-previsao"
		i18n="@@adm:bi-indc-rnv-contratos-renovados-previsao">
		Renovados da previsão
	</span>

	<span
		xingling="bi-indc-rnv-contratos-nao-renovados-previsao"
		i18n="@@adm:bi-indc-rnv-contratos-nao-renovados-previsao">
		Não renovados da previsão
	</span>

	<span
		xingling="bi-indc-rnv-contratos-do-mes"
		i18n="@@adm:bi-indc-rnv-contratos-do-mes">
		Do mês
	</span>

	<span
		xingling="bi-indc-rnv-contratos-de-meses-passados"
		i18n="@@adm:bi-indc-rnv-contratos-de-meses-passados">
		De meses passados
	</span>

	<span
		xingling="bi-indc-rnv-contratos-da-previsao-mes"
		i18n="@@adm:bi-indc-rnv-contratos-da-previsao-mes">
		Da previsão do mês
	</span>

	<span
		xingling="bi-indc-rnv-contratos-da-meses-futuros"
		i18n="@@adm:bi-indc-rnv-contratos-da-meses-futuros">
		De Meses Futuros
	</span>

	<span
		xingling="bi-indc-rnv-alunos-com-muitos-professores"
		i18n="@@adm:bi-indc-rnv-alunos-com-muitos-professores">
		Alunos com mais de um professor
	</span>
</pacto-traducoes-xingling>

<ng-template #previsaoHint>
	<p>i.Não entram nesta previsão os planos cancelados e trancados.</p>
	<p>ii.Também não entram nesta previsão planos do tipo Bolsa.</p>
	<p>iii.Obs: se não entra na previsão também não entra como não renovado.</p>
</ng-template>
