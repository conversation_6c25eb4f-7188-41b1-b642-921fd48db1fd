import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import { BiMsApiControleOperacaoService } from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { BiCardWithModalsBase } from "../../../bi-shared/base/bi-card/bi-card-with-modals-base";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { BiSidenavService } from "../../../bi-shared/services/bi-sidenav/bi-sidenav.service";
import { TabData } from "../../bi-adm.model";
import { sectionsData } from "./bi-card-controle-operacoes.items";
import { BiControleOpExcecaoFilterComponent } from "./bi-controle-op-excecao-filter/bi-controle-op-excecao-filter.component";

@Component({
	selector: "adm-bi-card-controle-de-operacoes-de-excecoes",
	templateUrl: "./bi-card-controle-de-operacoes-de-excecoes.component.html",
	styleUrls: ["./bi-card-controle-de-operacoes-de-excecoes.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardControleDeOperacoesDeExcecoesComponent
	extends BiCardWithModalsBase
	implements OnInit, OnDestroy, AfterViewInit
{
	showHiddenFields: boolean;

	tabData: TabData;
	tabDataSectionsMap;
	loading: boolean = false;

	constructor(
		private biMdlAjudaService: BiMdlAjudaService,
		private biMsApiControleOperacaoService: BiMsApiControleOperacaoService,
		private biSidenavService: BiSidenavService,
		protected dialog: MatDialog,
		protected cd: ChangeDetectorRef,
		protected breakpointObserver: BreakpointObserver,
		protected currencyPipe: CurrencyPipe,
		protected biCommonService: BiCommonService,
		private toastrService: ToastrService,
		protected router: Router
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		if (this.traducoes) {
			this.tabDataSectionsMap = sectionsData(this.traducoes);
		}
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		this.destroy();
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Controle de operações de exceções",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-controle-de-operacoes-de-excecoes-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(BiControleOpExcecaoFilterComponent, {
			globalFilter: this.globalFilterForm.value,
			filters: this.filtros,
		});

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData();
			}
		});
	}

	loadData(reloadFull: boolean = false) {
		this.tabData = {
			sections: [
				{
					listItens: [...this.tabDataSectionsMap.values()],
				},
			],
		};
		if (this.withFakeData) {
			this.showHiddenFields = true;
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.loading = true;
		this.cd.detectChanges();
		this.biMsApiControleOperacaoService
			.list({
				filtroControleOperacao: {
					inicio: inicio.getTime(),
					fim: this.filtros.data.getTime(),
					empresa: this.filtros.empresa,
					empresaNome: this.filtros.empresaNome,
					colaboradores: this.filtros.colaboradores,
					colaboradoresNomes: this.filtros.colaboradoresNomes,
					gruposColaboradores: this.filtros.gruposColaboradores || [],
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v) => {
					Object.keys(v).forEach((k) => {
						if (this.tabDataSectionsMap.has(k)) {
							this.tabDataSectionsMap.get(k).value = v[k];
						}
					});
					this.tabData.sections[0].listItens = [
						...this.tabDataSectionsMap.values(),
					];
					this.isAllEmpty = !this.tabData.sections[0].listItens.some(
						(item) => item.value > 0
					);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	mostrarMais() {
		this.showHiddenFields = !this.showHiddenFields;
	}

	trackByIndex(index, item) {
		return index;
	}
}
