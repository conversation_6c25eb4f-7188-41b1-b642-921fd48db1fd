<adm-bi-modal-content
	[modalTitle]="modalTitle"
	[shareUrl]="shareUrl"
	[filters]="filters"
	[page]="page"
	[pageSize]="size"
	[totalItems]="totalItems"
	[colunasShare]="colunasShare"
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)">
	<ds3-table>
		<table ds3DataTable [stateManager]="tableState">
			<ng-container ds3TableColumn="pessoa">
				<th *ds3TableHeaderCell>Cliente</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					{{ item?.nomeExclusaoVisitantes | titlecase }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="matricula">
				<th *ds3TableHeaderCell class="align-center">Dt. exclusão</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.dataAlteracao | date : "dd/MM/yyyy HH:mm:ss" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="situacao">
				<th *ds3TableHeaderCell class="align-center">Responsável</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.responsavelAlteracao | titlecase }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</ds3-table>
</adm-bi-modal-content>
