import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	ViewEncapsulation,
} from "@angular/core";
import { ConversaoVendaResponseModel } from "bi-ms-api";
import { InfoData } from "ui-kit";

@Component({
	selector: "adm-bi-card-icv-leads",
	templateUrl: "./bi-card-icv-leads.component.html",
	styleUrls: ["./bi-card-icv-leads.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardIcvLeadsComponent implements OnInit, OnChanges {
	@Input()
	conversaoVendaResponseModel: ConversaoVendaResponseModel;
	@Input()
	tipoData: number;

	infoArrayLeads: Array<InfoData>;

	constructor() {}

	ngOnInit() {
		this._initInfoArray();
	}

	ngOnChanges(changes: SimpleChanges) {
		const conversaoVendaResponseModel = changes["conversaoVendaResponseModel"];
		if (
			conversaoVendaResponseModel &&
			!conversaoVendaResponseModel.firstChange
		) {
			this.conversaoVendaResponseModel =
				conversaoVendaResponseModel.currentValue;
		}
		const tipoData = changes["tipoData"];
		if (tipoData && !tipoData.firstChange) {
			this.tipoData = tipoData.currentValue;
		}

		this._initInfoArray();
	}

	onClickLeads($event: any) {}

	private _initInfoArray() {
		this.infoArrayLeads = new Array<InfoData>(
			{
				info: {
					value:
						this.tipoData === 1
							? this.conversaoVendaResponseModel.leadsConvertidosDia
							: this.conversaoVendaResponseModel.leadsConvertidosMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "default",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "Convertidos em BV",
				},
			},
			{
				info: {
					value:
						this.tipoData === 1
							? this.conversaoVendaResponseModel.leadsNaoConvertidosDia
							: this.conversaoVendaResponseModel.leadsNaoConvertidosMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "default",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "Não convertidos",
				},
			}
		);
	}
}
