<div class="bi-card ticket-medio-planos">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Ticket médio de planos</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjudaTicketMedioPlanos()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros?.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.fonteReceitaDespesa">
				{{ filtros?.fonteReceitaDespesa?.label }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.incluirBolsas">
				Incluir bolsas no cálculo
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.considerarDependentes">
				Considerar dependentes
			</ds3-status>
		</div>
		<ng-container *ngIf="!loading">
			<div class="bi-card-content-section" *ngIf="tabData">
				<div
					class="bi-card-content-section-content"
					*ngFor="
						let section of tabData.sections;
						index as i;
						trackBy: trackByIndex
					">
					<ng-container *ngIf="section.infoItens">
						<ds3-bi-info
							[infoData]="section?.infoItens"
							[vertical]="section?.infoVertical"
							(clickEvent)="clickHandler($event)"></ds3-bi-info>
						<ds3-diviser *ngIf="i === 0"></ds3-diviser>
					</ng-container>
					<div
						class="bi-card-content-section-content-list"
						*ngIf="section?.listItens">
						<ng-container
							*ngFor="
								let listItem of section?.listItens;
								let odd = odd;
								trackBy: trackByIndex
							">
							<div
								*ngIf="listItem?.show"
								[ngClass]="['bi-card-content-section-content-list-item']">
								<div
									class="bi-card-content-section-content-list-item-text"
									*ngIf="listItem?.text">
									{{ listItem.text }}
								</div>
								<div
									class="bi-card-content-section-content-list-item-value"
									[ds3Tooltip]="listItem?.valueHint">
									<button ds3-text-button *ngIf="listItem?.value >= 0">
										{{ listItem.value }}
									</button>
								</div>
								<div
									class="bi-card-content-section-content-list-item-percentage"
									*ngIf="listItem?.percentage >= 0 && !listItem?.clickable">
									{{ listItem.percentage }}%
								</div>
								<button
									ds3-text-button
									class="bi-card-content-section-content-list-item-percentage"
									*ngIf="listItem?.percentage >= 0 && listItem?.clickable">
									{{ listItem.percentage }}%
								</button>
							</div>
						</ng-container>
					</div>
				</div>
			</div>
			<div class="bi-card-content-chart">
				<h2 class="pct-title4 cor-type-default-text">Últimos 6 meses</h2>
				<ds3-chart-line
					[disableAnimations]="true"
					[series]="chartOptions.series"
					[xaxis]="chartOptions.xaxis"
					[color]="chartOptions.color"
					[colorOrder]="[
						'blue',
						'green',
						'red',
						'yellow',
						'orange',
						'pink',
						'purple',
						'lightblue'
					]"></ds3-chart-line>
			</div>
		</ng-container>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>

<ng-template #mediaContratosAtivosHint>
	<div>
		Ativos + Vencidos no início do mês:
		{{ ticketMedioResponseModel?.dados?.ativosVencidosInicioMes }}
	</div>
	<div>
		No fim do mês: {{ ticketMedioResponseModel?.dados?.ativosVencidosFimMes }}
	</div>
</ng-template>

<ng-template #bolsasNoPeriodoHint>
	<div>Contratos com bolsa no mês de {{ filtros?.data | date : "MMMM" }}</div>
</ng-template>

<ng-template #dependentesFimMesHint>
	<div>Dependentes no fim do mês de {{ filtros?.data | date : "MMMM" }}</div>
</ng-template>

<ng-template #mediaPagantesHint>
	<div>(Média de Ativos + Vencidos) - Bolsas</div>
</ng-template>
