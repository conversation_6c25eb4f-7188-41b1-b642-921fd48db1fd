import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalAlunosExcluidosTwComVinculosComponent } from "./bi-modal-alunos-excluidos-tw-com-vinculos.component";

describe("BiModalAlunosExcluidosTwComVinculosComponent", () => {
	let component: BiModalAlunosExcluidosTwComVinculosComponent;
	let fixture: ComponentFixture<BiModalAlunosExcluidosTwComVinculosComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalAlunosExcluidosTwComVinculosComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalAlunosExcluidosTwComVinculosComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
