<pacto-relatorio
	#tableData
	[enableDs3]="true"
	[showShare]="false"
	[table]="table"
	class="relatorioIcvRankingConsultores"
	id="ranking-consultores-aulas-experimentais-list"
	idSuffix="ranking-consultores"></pacto-relatorio>

<ng-template #celulaConsultor let-consultor="item" let-index="index">
	<div class="icv-rc-celula-consultor">
		<img
			*ngIf="index === 0"
			height="25"
			src="pacto-ui/images/medal-gold.png"
			width="17" />
		<img
			*ngIf="index === 1"
			height="25"
			src="pacto-ui/images/medal-silver.png"
			width="17" />
		<img
			*ngIf="index === 2"
			height="25"
			src="pacto-ui/images/medal-bronze.png"
			width="17" />
		<ds3-avatar [src]="consultor?.pessoa.fotoUrl" size="24"></ds3-avatar>
		<span>{{ consultor?.pessoa?.nome }}</span>
	</div>
</ng-template>

<div class="icv-rc-btn-row">
	<button (click)="openModalRankingConsultores()" ds3-text-button>
		Ver mais
	</button>
</div>
