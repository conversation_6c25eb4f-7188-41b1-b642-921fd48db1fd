import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiMovPagamentoService,
	ApiResponseSinglePaginated,
	FiltroBiControleOperacao,
	MovPagamento,
	MovPagamentoTotais,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-pagamentos-database-alterada",
	templateUrl: "./bi-modal-pagamentos-database-alterada.component.html",
	styleUrls: [
		"./bi-modal-pagamentos-database-alterada.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalPagamentosDatabaseAlteradaComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "cliente.pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataLancamento",
			visible: true,
			titulo: "Dt. Lançamento",
			ordenavel: true,
			nome: "dataLancamento",
			inputType: "date",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "dataPagamento",
			visible: true,
			titulo: "Dt. Pagamento",
			ordenavel: true,
			nome: "dataPagamento",
			inputType: "date",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "dataAlteracaoManual",
			visible: true,
			titulo: "Dt. Alteração",
			ordenavel: true,
			nome: "dataAlteracao",
			inputType: "date",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "valor",
			visible: true,
			titulo: "Valor",
			ordenavel: true,
			nome: "valor",
			inputType: "currency",
			decimal: true,
			decimalPrecision: 2,
		},
		{
			mostrarTitulo: true,
			campo: "formaPagamento.descricao",
			visible: true,
			titulo: "Forma de pagamento",
			ordenavel: true,
			nome: "formaPagamento",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "responsavelPagamento.nome",
			visible: true,
			titulo: "Resp. Pagamento",
			ordenavel: true,
			nome: "responsavelPagamento",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataQuitacao",
			visible: true,
			titulo: "Dt. Quitação",
			ordenavel: true,
			nome: "dataQuitacao",
			inputType: "date",
			date: true,
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"pagamentos/data-base-alterada-sem-totais"
	);
	movPagamentoTotais: MovPagamentoTotais;
	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalPagamentosDatabaseAlteradaComponent>,
		private admCoreApiMovPagamentoService: AdmCoreApiMovPagamentoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiMovPagamentoService
			.pagamentosComDataBaseAlterada({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseSinglePaginated<MovPagamentoTotais>) => {
					this.totalItems = response.totalElements;
					if (response.content) {
						this.movPagamentoTotais = response.content;
					}
					this.tableState.patchState({
						data: this.movPagamentoTotais.movPagamentos,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: MovPagamento) {
		this.biCommonService.openCliente(item.cliente.codigoMatricula);
	}
}
