import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiProdutoService,
	ApiResponseSinglePaginated,
	AplicacaoDesconto,
	AplicacaoDescontoTotais,
	FiltroBiControleOperacao,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-total-valor-nao-arrecadado-devido-descontos",
	templateUrl:
		"./bi-modal-total-valor-nao-arrecadado-devido-descontos.component.html",
	styleUrls: [
		"./bi-modal-total-valor-nao-arrecadado-devido-descontos.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalTotalValorNaoArrecadadoDevidoDescontosComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "nomeCliente",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "nomeCliente",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "matriculaCliente",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "matriculaCliente",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "nomeUsRespAlteracao",
			visible: true,
			titulo: "Resp. Alteração",
			ordenavel: true,
			nome: "nomeUsRespAlteracao",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "descricaoProduto",
			visible: true,
			titulo: "Tipo",
			ordenavel: true,
			nome: "descricaoProduto",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "descricaoConvenio",
			visible: true,
			titulo: "Convênio",
			ordenavel: true,
			nome: "descricaoConvenio",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataLancamento",
			visible: true,
			titulo: "Dt. Lançamento",
			ordenavel: true,
			nome: "dataLancamento",
			inputType: "text",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "valorDesconto",
			visible: true,
			titulo: "Valor",
			ordenavel: true,
			nome: "valorDesconto",
			inputType: "decimal",
			decimal: true,
			decimalPrecision: 2,
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"produtos/aplicacao-desconto-sem-totais"
	);
	aplicacaoDescontoTotais: AplicacaoDescontoTotais;
	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "cliente";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalTotalValorNaoArrecadadoDevidoDescontosComponent>,
		private admCoreApiProdutoService: AdmCoreApiProdutoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiProdutoService
			.aplicacaoDesconto({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (
					response: ApiResponseSinglePaginated<AplicacaoDescontoTotais>
				) => {
					this.totalItems = response.totalElements;
					if (response.content) {
						this.aplicacaoDescontoTotais = response.content;
					}
					this.tableState.patchState({
						data: this.aplicacaoDescontoTotais.aplicacaoDesconto,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: AplicacaoDesconto) {
		this.biCommonService.openCliente(item.matriculaCliente);
	}
}
