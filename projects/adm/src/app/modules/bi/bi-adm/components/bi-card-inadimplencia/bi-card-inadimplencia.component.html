<div class="bi-card inadimplencia">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Inadimplência</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="exportRelatorioInadimplencia()"
				class="pct pct-file-text"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.desconsiderarCanceladas">
				Desconsiderar parcelas canceladas
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.dataMatricula">
				Alunos matriculados a partir do dia:
				{{ filtros.dataMatricula | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.diaPagamento">
				Considerar pagamento até o dia: {{ filtros.diaPagamento }}
			</ds3-status>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="
					let section of tabData.sections;
					trackBy: trackByIndex;
					let i = index
				">
				<ng-container *ngIf="section.infoItens">
					<ds3-bi-info
						[infoData]="section?.infoItens"
						[vertical]="false"
						(clickEvent)="openModalInfo($event)"></ds3-bi-info>
				</ng-container>
				<ds3-diviser *ngIf="i === 0"></ds3-diviser>
				<ng-container *ngIf="section?.chartItens">
					<div class="bi-card-content-section-content-title">
						<span>{{ section?.title }}</span>
						<div class="bi-card-content-section-content-actions">
							<button
								ds3-text-button
								[class.active]="showAsTable"
								(click)="showAsTable = true">
								Lista
							</button>
							<button
								ds3-text-button
								[class.active]="!showAsTable"
								(click)="showAsTable = false">
								Gráfico
							</button>
						</div>
					</div>
					<ng-container *ngIf="!showAsTable">
						<ds3-chart-mixed
							*ngIf="!!section?.chartItens"
							[disableAnimations]="true"
							[isStacked]="true"
							[yaxis]="section?.chartItens.yaxis"
							[series]="section?.chartItens.series"
							[labels]="section?.chartItens.labels"
							[colorOrder]="colorOrder"
							[dataLabels]="dataLabelsChart"
							[tooltip]="tooltipChart"></ds3-chart-mixed>
					</ng-container>
					<ng-container *ngIf="showAsTable">
						<div class="bi-card-content-section-content-list">
							<div class="bi-card-content-section-content-list-header">
								<div
									class="bi-card-content-section-content-list-header-cell first">
									Mês referência
								</div>
								<div
									*ngFor="
										let head of section.chartItens.labels;
										trackBy: trackByIndex
									"
									class="bi-card-content-section-content-list-header-cell">
									{{ head }}
								</div>
								<div class="bi-card-content-section-content-list-header-cell">
									Média
								</div>
							</div>
							<ng-container
								*ngFor="
									let tableItem of section?.tableItens;
									let odd = odd;
									trackBy: trackByIndex
								">
								<div
									[ngClass]="[
										'bi-card-content-section-content-list-item',
										odd ? 'list-item-odd' : 'list-item-even',
										'table-item'
									]">
									<div
										class="bi-card-content-section-content-list-item-cell table-item-cell first"
										*ngIf="tableItem?.title">
										{{ tableItem.title }}
									</div>
									<div
										class="bi-card-content-section-content-list-item-cell table-item-cell"
										*ngFor="
											let data of tableItem?.values;
											trackBy: trackByIndex
										">
										<span *ngIf="!tableItem.isActionable">
											<ng-container
												*ngTemplateOutlet="dataTemplate"></ng-container>
										</span>
										<a
											*ngIf="tableItem.isActionable"
											(click)="openTableItem(tableItem)"
											ds3-text-button
											size="sm">
											<ng-container
												*ngTemplateOutlet="dataTemplate"></ng-container>
										</a>

										<ng-template #dataTemplate>
											<ng-container *ngIf="tableItem.percentage">
												{{ data | number : "1.2-2" }}%
											</ng-container>
											<ng-container *ngIf="tableItem.monetary">
												{{ data | currency : "BRL" }}
											</ng-container>
											<ng-container
												*ngIf="!tableItem.monetary && !tableItem.percentage">
												{{ data }}
											</ng-container>
										</ng-template>
									</div>
								</div>
							</ng-container>
						</div>
					</ng-container>
				</ng-container>
			</div>
		</div>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
