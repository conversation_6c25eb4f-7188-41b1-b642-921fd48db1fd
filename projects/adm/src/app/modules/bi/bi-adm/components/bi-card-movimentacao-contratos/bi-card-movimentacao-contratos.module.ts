import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiSharedModule } from "../../../bi-shared/bi-shared.module";
import { BiCardMovientacaoContratosFilterComponent } from "./bi-card-movientacao-contratos-filter/bi-card-movientacao-contratos-filter.component";
import { BiCardMovimentacaoContratosComponent } from "./bi-card-movimentacao-contratos.component";

@NgModule({
	declarations: [
		BiCardMovientacaoContratosFilterComponent,
		BiCardMovimentacaoContratosComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardMovimentacaoContratosComponent],
	entryComponents: [BiCardMovientacaoContratosFilterComponent],
})
export class BiCardMovimentacaoContratosModule {}
