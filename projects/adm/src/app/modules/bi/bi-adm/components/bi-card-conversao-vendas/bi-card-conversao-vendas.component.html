<div class="bi-card bi-card-conversao-vendas">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span
				class="bi-card-header-label-title typography-title-4 text-color-typography-default-title">
				Conversão de vendas
			</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.empresaNome">
				{{ filtros?.empresaNome }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros?.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.considerarPlanoBolsa">
				Considerar plano bolsa
			</ds3-status>

			<ds3-status color="outlined" *ngIf="filtros?.desconsiderarGympass">
				Desconsiderar alunos wellHub
			</ds3-status>

			<ds3-status
				color="outlined"
				*ngIf="filtros?.eventos && filtros?.eventos?.length !== 0">
				<span
					[ds3Tooltip]="tooltipEventosRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.eventos?.length }}
					{{
						filtros?.eventos?.length > 1
							? "eventos selecionados"
							: "evento selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipEventosRef>
				<div *ngFor="let evento of filtros?.eventos; trackBy: trackByIndex">
					{{ evento.descricao }}
				</div>
			</ng-template>

			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradores;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>

			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGrupoColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupo de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGrupoColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradores;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>

			<ds3-status
				color="outlined"
				*ngIf="filtros?.tipoBV && filtros?.tipoBV?.length !== 0">
				<span
					[ds3Tooltip]="tooltipTipoBVRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.tipoBV?.length }}
					{{
						filtros?.tipoBV?.length > 1
							? "tipos de BV selecionados"
							: "tipo de BV selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipTipoBVRef>
				<div *ngFor="let tipoBV of filtros?.tipoBV; trackBy: trackByIndex">
					<ng-container [ngSwitch]="tipoBV">
						<ng-container *ngSwitchCase="1">Matrícula</ng-container>
						<ng-container *ngSwitchCase="2">Rematrícula</ng-container>
						<ng-container *ngSwitchCase="3">Retorno</ng-container>
					</ng-container>
				</div>
			</ng-template>

			<ds3-status
				color="outlined"
				*ngIf="filtros?.origemSistema && filtros?.origemSistema?.length !== 0">
				<span
					[ds3Tooltip]="tooltipOrigemSistemaRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.origemSistema?.length }}
					{{
						filtros?.origemSistema?.length > 1
							? "tipos de origens selecionados"
							: "tipo de origem selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipOrigemSistemaRef>
				<div
					*ngFor="
						let origemSistema of filtros?.origemSistema;
						trackBy: trackByIndex
					">
					<ng-container [ngSwitch]="origemSistema">
						<ng-container *ngSwitchCase="'1,17'">Origem sistema</ng-container>
						<ng-container *ngSwitchCase="'9'">Origem do site</ng-container>
					</ng-container>
				</div>
			</ng-template>

			<ds3-status
				color="outlined"
				*ngIf="filtros?.tipoContrato && filtros?.tipoContrato?.length !== 0">
				<span
					[ds3Tooltip]="tooltipTipoContratoRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.tipoContrato?.length }}
					{{
						filtros?.tipoContrato?.length > 1
							? "tipos de contrato selecionados"
							: "tipo de contrato selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipTipoContratoRef>
				<div
					*ngFor="
						let tipoContrato of filtros?.tipoContrato;
						trackBy: trackByIndex
					">
					<ng-container [ngSwitch]="tipoContrato">
						<ng-container *ngSwitchCase="1">Espontâneo</ng-container>
						<ng-container *ngSwitchCase="2">Agendado</ng-container>
					</ng-container>
				</div>
			</ng-template>
		</div>
		<ds3-diviser></ds3-diviser>
		<div class="bi-card-content-section">
			<div
				class="bi-card-content-section-content bi-card-content-section-content-flex">
				<div class="bi-table">
					<ds3-table>
						<table ds3DataTable [stateManager]="tableState" [smallRows]="true">
							<ng-container ds3TableColumn="type">
								<th *ds3TableHeaderCell></th>
								<td *ds3TableCell="let item">
									<ng-container [ngSwitch]="item?.type">
										<ng-container *ngSwitchCase="1">
											<div
												style="border-radius: 50%; height: 16px; width: 16px"
												class="background-color-support-yellow-4"></div>
										</ng-container>
										<ng-container *ngSwitchCase="2">
											<div
												style="border-radius: 50%; height: 16px; width: 16px"
												class="background-color-support-blue-4"></div>
										</ng-container>
										<ng-container *ngSwitchCase="3">
											<div
												style="border-radius: 50%; height: 16px; width: 16px"
												class="background-color-support-green-4"></div>
										</ng-container>
									</ng-container>
								</td>
							</ng-container>

							<ng-container ds3TableColumn="label">
								<th *ds3TableHeaderCell></th>
								<td *ds3TableCell="let item">
									{{ item?.label }}
								</td>
							</ng-container>

							<ng-container ds3TableColumn="valueDia">
								<th *ds3TableHeaderCell>Dia</th>
								<td *ds3TableCell="let item">
									{{ item?.valueDia }}
								</td>
							</ng-container>

							<ng-container ds3TableColumn="valueMes">
								<th *ds3TableHeaderCell>Mês</th>
								<td *ds3TableCell="let item">
									{{ item?.valueMes }}
								</td>
							</ng-container>

							<tr *ds3TableRow></tr>
							<tr *ds3TableEmptyRow class="ds3-table-empty">
								<td></td>
							</tr>

							<tbody *ds3TableLoading>
								<tr>
									<td>
										<div class="bi-modal-table-loader" role="status">
											<img
												alt="Loading pacto"
												src="pacto-ui/images/gif/loading-pacto.gif" />
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</ds3-table>
				</div>

				<ds3-diviser [vertical]="true"></ds3-diviser>

				<div class="bi-info-no-border">
					<div class="bi-info-no-border-content">
						<div class="bi-info-no-border-value">
							{{
								conversaoVendaResponseModel?.indiceConversaoVendaMes
									| number : "1.2-2"
							}}
							<span class="typography-display-6" style="margin-left: 8px">
								%
							</span>
						</div>

						<div class="bi-info-no-border-label">ICV</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bi-card-content-section-content">
			<ds3-chart-bar
				[series]="optionsChartVerificacaoClientes.series"
				[disableAnimations]="true"
				[yaxis]="optionsChartVerificacaoClientes.yaxis"
				[xaxis]="optionsChartVerificacaoClientes.xaxis"
				[tooltip]="optionsChartVerificacaoClientes.tooltip"
				[events]="optionsChartVerificacaoClientes.events"
				[height]="150"
				[barHeight]="100"
				[colorOrder]="[
					'blue',
					'green',
					'red',
					'yellow',
					'orange',
					'pink',
					'purple',
					'lightblue'
				]"></ds3-chart-bar>
		</div>
	</div>
</div>
