import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiCardPendenciasDeClientesFilterComponent } from "./bi-card-pendencias-de-clientes-filter/bi-card-pendencias-de-clientes-filter.component";
import { BiCardPendenciasDeClientesComponent } from "./bi-card-pendencias-de-clientes.component";
import {
	BiPendenciasClienteModalComponent,
	ObjectValuePipe,
} from "./modals/bi-pendencias-cliente-modal/bi-pendencias-cliente-modal.component";

@NgModule({
	declarations: [
		BiCardPendenciasDeClientesFilterComponent,
		BiCardPendenciasDeClientesComponent,
		BiPendenciasClienteModalComponent,
		ObjectValuePipe,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	entryComponents: [
		BiCardPendenciasDeClientesFilterComponent,
		BiPendenciasClienteModalComponent,
	],
	providers: [ObjectValuePipe],
	exports: [BiCardPendenciasDeClientesComponent],
})
export class BiCardPendenciasDeClientesModule {}
