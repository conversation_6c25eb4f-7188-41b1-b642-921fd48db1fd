import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalClientesGympassComponent } from "./bi-modal-clientes-gympass.component";

describe("BiModalClientesGympassComponent", () => {
	let component: BiModalClientesGympassComponent;
	let fixture: ComponentFixture<BiModalClientesGympassComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalClientesGympassComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalClientesGympassComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
