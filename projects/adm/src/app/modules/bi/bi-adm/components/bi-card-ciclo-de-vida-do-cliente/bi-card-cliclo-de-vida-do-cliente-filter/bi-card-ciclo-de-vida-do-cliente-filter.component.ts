import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { AdmCoreApiPlanoContaService, PlanoConta, Produto } from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
	selector: "adm-bi-card-ciclo-de-vida-do-cliente-filter",
	templateUrl: "./bi-card-ciclo-de-vida-do-cliente-filter.component.html",
	styleUrls: ["./bi-card-ciclo-de-vida-do-cliente-filter.component.scss"],
})
export class BiCardCicloDeVidaDoClienteFilterComponent
	implements OnInit, OnDestroy
{
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		dataInicio: new FormControl(),
		dataFim: new FormControl(),
		produtos: new FormControl(),
		servicos: new FormControl(),
		despesas: new FormControl(),
	});

	filters: {
		dataInicio: Date;
		dataFim: Date;
		produtos: Array<{ label: string; value: any }>;
		servicos: Array<{ label: string; value: any }>;
		despesas: Array<{ label: string; value: any }>;
	};

	produtos: Array<Produto> = new Array<Produto>();
	servicos: Array<Produto> = new Array<Produto>();
	despesas: Array<PlanoConta> = new Array<PlanoConta>();

	private _destroy$: Subject<void> = new Subject<void>();

	constructor(
		private admCoreApiPlanoContaService: AdmCoreApiPlanoContaService,
		private biSidenavRef: BiSidenavRef<BiCardCicloDeVidaDoClienteFilterComponent>,
		private toastrService: ToastrService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
		this._loadProdutos();
		this._loadServicos();
		this._loadDespesas();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("dataInicio").setValue(this.filters.dataInicio);
			this.form.get("dataFim").setValue(this.filters.dataFim);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				dataInicio: this.filters.dataInicio,
				dataFim: this.filters.dataFim,
				produtos: this.filters.produtos,
				servicos: this.filters.servicos,
				despesas: this.filters.despesas,
			});
		}
	}

	private _loadProdutos(term = "") {
		this.admCoreApiPlanoContaService
			.findAllProdutos({
				filters: {
					parametro: term,
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response) => {
					this.produtos = response.content;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(
						message || "Ocorreu um erro desconhecido ao carregar os produtos!"
					);
					this.cd.detectChanges();
				},
			});
	}

	private _loadServicos(term = "") {
		this.admCoreApiPlanoContaService
			.findAllProdutos({
				filters: {
					somenteServicos: true,
					parametro: term,
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response) => {
					this.servicos = response.content;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(
						message || "Ocorreu um erro desconhecido ao carregar os serviços!"
					);
					this.cd.detectChanges();
				},
			});
	}

	private _loadDespesas(term = "") {
		this.admCoreApiPlanoContaService
			.findAllDespesas({
				filters: {
					codigoEmpresa: this.globalFilter.empresa,
					dataInicio: this.filters.dataInicio.getTime(),
					dataFim: this.filters.dataFim.getTime(),
					parametro: term,
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response) => {
					this.despesas = response.content;
					if (!this.filters.despesas || this.filters.despesas.length === 0) {
						const despesasPadraoLtv = this.despesas.filter((d) => d.insideltv);
						if (despesasPadraoLtv) {
							this.form.get("despesas").setValue(despesasPadraoLtv);
						}
					}
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(
						message || "Ocorreu um erro desconhecido ao carregar os serviços!"
					);
					this.cd.detectChanges();
				},
			});
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		if (this.filters.despesas.length === 0) {
			this.toastrService.error("Deve ser selecionado ao menos uma despesa!");
			return;
		}
		this.biSidenavRef.close({
			filters: {
				...this.filters,
			},
		});
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}
}
