import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { Util } from "@adm/util/Util";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiMsApiIndiceRenovacaoService,
	IndiceRenovacaoResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { BiCardWithModalsBase } from "../../../bi-shared/base/bi-card/bi-card-with-modals-base";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { BiSidenavService } from "../../../bi-shared/services/bi-sidenav/bi-sidenav.service";
import { TabData } from "../../bi-adm.model";
import { BiIndiceRenovacaoFilterComponentComponent } from "./bi-indice-renovacao-filter-component/bi-indice-renovacao-filter-component.component";

@Component({
	selector: "adm-bi-card-indice-renovacao",
	templateUrl: "./bi-card-indice-renovacao.component.html",
	styleUrls: ["./bi-card-indice-renovacao.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardIndiceRenovacaoComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	loading: boolean = false;

	tabData: TabData;
	filterConfig;

	nextMonthDate: Date = new Date();

	@ViewChild("previsaoHint", { static: false })
	previsaoHint: TemplateRef<any>;

	constructor(
		private biMdlAjudaService: BiMdlAjudaService,
		private bimsApiIndiceRenovacao: BiMsApiIndiceRenovacaoService,
		private biSidenavService: BiSidenavService,
		protected biCommonService: BiCommonService,
		protected dialog: MatDialog,
		protected cd: ChangeDetectorRef,
		protected breakpointObserver: BreakpointObserver,
		protected currencyPipe: CurrencyPipe,
		private datePipe: DatePipe,
		private toastrService: ToastrService,
		protected router: Router
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init({
			dataFimUltimoDiaMes: true,
		});
		if (!this.filtros.dataInicio) {
			this.filtros.dataInicio = new Date();
		}
		this._populateNextMonthDate();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	private _populateNextMonthDate() {
		this.nextMonthDate = new Date(this.filtros.dataInicio);
		this.nextMonthDate.setMonth(this.nextMonthDate.getMonth() + 1);
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Índice de renovação",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-indice-renovacao-mes-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiIndiceRenovacaoFilterComponentComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._populateNextMonthDate();
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.loading = true;
		this.cd.detectChanges();
		this.bimsApiIndiceRenovacao
			.list({
				filtroIndiceRenovacao: {
					dataInicial: inicio.getTime(),
					dataFinal: this.filtros.data.getTime(),
					empresa: this.filtros.empresa,
					colaboradores: this.filtros.colaboradores,
					retornarContratos: true,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	trackByIndex(index, item) {
		return index;
	}

	private _populateTabData(v?: IndiceRenovacaoResponseModel) {
		if (!v) {
			v = {
				previsaoMes: 0,
				renovadosPrevisaoMes: 0,
				naoRenovadosPrevisaoMes: 0,
				renovadosDentroMes: 0,
				renovadosToleranciaPrevisaoMes: 0,
				naoRenovadosToleranciaPrevisaoMes: 0,
				renovadosMesesPassados: 0,
				renovadosMesesFuturos: 0,
				renovadosTotal: 0,
				clientesComMuitosProfessores: 0,
				contratosPrevisaoMes: [],
				contratosRenovadosPrevisaoMes: [],
				contratosNaoRenovadosPrevisaoMes: [],
				contratosRenovadosDentroMes: [],
				contratosRenovadosToleranciaPrevisaoMes: [],
				contratosNaoRenovadosToleranciaPrevisaoMes: [],
				contratosRenovadosMesesPassados: [],
				contratosRenovadosMesesFuturos: [],
				contratosRenovadosTotal: [],
				contratosClientesComMuitosProfessores: [],
				contratosAtivos: 0,
			} as IndiceRenovacaoResponseModel;
		}

		const percentagePrevisaoRenovacao = Util.roundTo(
			(v.previsaoMes * 100) / v.contratosAtivos,
			2
		);

		this.tabData = {
			diaInicio: this.filtros.dataInicio,
			diaFim: this.filtros.data,
			sections: [
				{
					title: this.traducoes.getLabel("bi-indc-rnv-previsao-title"),
					listItens: [
						{
							text: "Previsão",
							value: v.previsaoMes,
							percentage: 100,
							showPercentage: true,
							auxText: `${percentagePrevisaoRenovacao || 0}% dos ativos`,
							auxTextHint: this.previsaoHint,
							auxTextColor:
								(v.previsaoMes * 100) / v.contratosAtivos < 15
									? "text-color-feedback-gain-2"
									: "text-color-feedback-loss-2",
						},
						{
							text: this.traducoes.getLabel(
								"bi-indc-rnv-contratos-renovados-previsao"
							),
							value: v.renovadosPrevisaoMes,
							percentage: v.previsaoMes
								? ((v.renovadosPrevisaoMes || 0) * 100) / v.previsaoMes
								: 0,
							showPercentage: true,
						},
						{
							text: this.traducoes.getLabel(
								"bi-indc-rnv-contratos-nao-renovados-previsao"
							),
							value: v.naoRenovadosPrevisaoMes,
							percentage: v.previsaoMes
								? (v.naoRenovadosPrevisaoMes * 100) / v.previsaoMes
								: 0,
							showPercentage: true,
						},
					],
				},
				{
					title: "Renovados",
					listItens: [
						{
							text: this.traducoes.getLabel("bi-indc-rnv-contratos-do-mes"),
							value: v.renovadosTotal,
							percentage: v.renovadosTotal
								? (v.renovadosTotal * 100) / v.renovadosTotal
								: 0,
							showPercentage: true,
						},
						{
							text: this.traducoes.getLabel(
								"bi-indc-rnv-contratos-de-meses-passados"
							),
							value: v.renovadosMesesPassados,
							percentage: v.renovadosTotal
								? ((v.renovadosMesesPassados || 0) * 100) / v.renovadosTotal
								: 0,
							showPercentage: true,
						},
						{
							text: this.traducoes.getLabel(
								"bi-indc-rnv-contratos-da-previsao-mes"
							),
							value: v.renovadosPrevisaoMes,
							percentage: v.renovadosTotal
								? ((v.renovadosPrevisaoMes || 0) * 100) / v.renovadosTotal
								: 0,
							showPercentage: true,
						},
						{
							text: this.traducoes.getLabel(
								"bi-indc-rnv-contratos-da-meses-futuros"
							),
							value: v.renovadosMesesFuturos,
							percentage: v.renovadosTotal
								? ((v.renovadosMesesFuturos || 0) * 100) / v.renovadosTotal
								: 0,
							showPercentage: true,
						},
						{
							text: this.traducoes.getLabel(
								"bi-indc-rnv-alunos-com-muitos-professores"
							),
							value: v.clientesComMuitosProfessores,
						},
					],
				},
			],
		};
	}
}
