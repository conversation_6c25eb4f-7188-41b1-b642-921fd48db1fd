import { BiCardVerificacaoClientesComponent } from "@adm/modules/bi/bi-adm/components/bi-card-verificacao-clientes/bi-card-verificacao-clientes.component";
import { BiClientesParaVerificarModalComponent } from "@adm/modules/bi/bi-adm/components/bi-card-verificacao-clientes/bi-clientes-para-verificar-modal/bi-clientes-para-verificar-modal.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";

@NgModule({
	declarations: [
		BiCardVerificacaoClientesComponent,
		BiClientesParaVerificarModalComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardVerificacaoClientesComponent],
	entryComponents: [BiClientesParaVerificarModalComponent],
})
export class BiCardVerificacaoClientesModule {}
