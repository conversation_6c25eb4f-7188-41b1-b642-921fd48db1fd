import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	ViewEncapsulation,
} from "@angular/core";
import { ConversaoVendaResponseModel } from "bi-ms-api";
import { InfoData } from "ui-kit";

@Component({
	selector: "adm-bi-card-icv-clientes-convertidos",
	templateUrl: "./bi-card-icv-clientes-convertidos.component.html",
	styleUrls: ["./bi-card-icv-clientes-convertidos.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardIcvClientesConvertidosComponent
	implements OnInit, OnChanges
{
	@Input()
	conversaoVendaResponseModel: ConversaoVendaResponseModel;
	@Input()
	tipoData: number;

	infoArrayAlunosConvertidos: Array<InfoData>;

	constructor() {}

	ngOnInit() {
		this._initInfoArray();
	}

	ngOnChanges(changes: SimpleChanges) {
		const conversaoVendaResponseModel = changes["conversaoVendaResponseModel"];
		if (
			conversaoVendaResponseModel &&
			!conversaoVendaResponseModel.firstChange
		) {
			this.conversaoVendaResponseModel =
				conversaoVendaResponseModel.currentValue;
		}
		const tipoData = changes["tipoData"];
		if (tipoData && !tipoData.firstChange) {
			this.tipoData = tipoData.currentValue;
		}

		this._initInfoArray();
	}

	onClickAlunosConvertidos($event: any) {}

	private _initInfoArray() {
		this.infoArrayAlunosConvertidos = new Array<InfoData>(
			{
				info: {
					value:
						this.tipoData === 1
							? this.conversaoVendaResponseModel.matriculasDia
							: this.conversaoVendaResponseModel.matriculasMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "default",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "Convertidos em matrículas",
				},
			},
			{
				info: {
					value:
						this.tipoData === 1
							? this.conversaoVendaResponseModel.reMatriculasDia
							: this.conversaoVendaResponseModel.reMatriculasMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "default",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "Convertidos em rematrículas",
				},
			},
			{
				info: {
					value:
						this.tipoData === 1
							? this.conversaoVendaResponseModel.boletinVisitaDia
							: this.conversaoVendaResponseModel.boletinVisitaMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: false,
					state: "loss",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "Não convertidos ",
				},
			}
		);
	}
}
