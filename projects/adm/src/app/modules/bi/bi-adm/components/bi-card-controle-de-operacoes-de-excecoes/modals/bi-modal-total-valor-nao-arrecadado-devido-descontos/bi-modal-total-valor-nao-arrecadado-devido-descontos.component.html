<adm-bi-modal-content
	[modalTitle]="modalTitle"
	[shareUrl]="shareUrl"
	[filters]="filters"
	[page]="page"
	[pageSize]="size"
	[totalItems]="totalItems"
	[colunasShare]="colunasShare"
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)"
	[withPaginatorDiviser]="true">
	<ds3-table>
		<table ds3DataTable [stateManager]="tableState">
			<ng-container ds3TableColumn="cliente">
				<th *ds3TableHeaderCell>Nome</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					<button ds3-text-button (click)="openCliente(item)">
						{{ item?.nomeCliente | titlecase }}
					</button>
				</td>
			</ng-container>

			<ng-container ds3TableColumn="matricula">
				<th *ds3TableHeaderCell>Matrícula</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					{{ item?.matriculaCliente }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="usuario">
				<th *ds3TableHeaderCell>Resp. Alteração</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					{{ item?.nomeUsRespAlteracao | titlecase }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="descricao">
				<th *ds3TableHeaderCell>Tipo</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					{{ item?.descricaoProduto }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="convenio">
				<th *ds3TableHeaderCell>Convênio</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					{{ item?.descricaoConvenio }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataLancamento">
				<th *ds3TableHeaderCell class="align-center">Dt. Lançamento</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.dataLancamento | date : "dd/MM/yyyy" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="valor">
				<th *ds3TableHeaderCell class="align-center">Valor</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.valorDesconto | currency : "BRL" }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				ds3-icon-button
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				(click)="triggerSortToggle()">
				<i
					class="pct"
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>

			<tfoot class="ds3-table-foot">
				<tr>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">
						{{ aplicacaoDescontoTotais?.totalDesconto | currency : "BRL" }}
					</td>
				</tr>
			</tfoot>
		</table>
	</ds3-table>
</adm-bi-modal-content>
