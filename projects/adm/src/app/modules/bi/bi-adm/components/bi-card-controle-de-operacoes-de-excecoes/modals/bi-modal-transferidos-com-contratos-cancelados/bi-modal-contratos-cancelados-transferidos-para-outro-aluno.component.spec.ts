import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalContratosCanceladosTransferidosParaOutroAlunoComponent } from "./bi-modal-contratos-cancelados-transferidos-para-outro-aluno.component";

describe("BiModalContratosCanceladosTransferidosParaOutroAlunoComponent", () => {
	let component: BiModalContratosCanceladosTransferidosParaOutroAlunoComponent;
	let fixture: ComponentFixture<BiModalContratosCanceladosTransferidosParaOutroAlunoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [
				BiModalContratosCanceladosTransferidosParaOutroAlunoComponent,
			],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalContratosCanceladosTransferidosParaOutroAlunoComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
