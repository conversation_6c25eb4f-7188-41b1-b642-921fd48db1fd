import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalContratosDatabaseAlteradaComponent } from "./bi-modal-contratos-database-alterada.component";

describe("BiModalContratosDatabaseAlteradaComponent", () => {
	let component: BiModalContratosDatabaseAlteradaComponent;
	let fixture: ComponentFixture<BiModalContratosDatabaseAlteradaComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalContratosDatabaseAlteradaComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalContratosDatabaseAlteradaComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
