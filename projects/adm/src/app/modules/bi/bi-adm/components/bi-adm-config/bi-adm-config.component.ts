import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import {
	ChangeDetectionStrategy,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	BiEnum,
	ConfiguracaoBi,
	ConfiguracaoTipoEnum,
	getConfigByBi,
} from "bi-ms-api";
import { Subject } from "rxjs";

function deepCopy<T>(obj: T, ...keysToIgnore): T {
	if (obj === null || typeof obj !== "object") {
		return obj;
	}

	if (obj instanceof Date) {
		return new Date(obj.getTime()) as any;
	}

	if (Array.isArray(obj)) {
		return obj.map((o) => deepCopy(o, ...keysToIgnore)) as any;
	}

	const result: any = {};
	for (const key in obj) {
		if (
			!keysToIgnore.includes(key) &&
			Object.prototype.hasOwnProperty.call(obj, key)
		) {
			result[key] = deepCopy((obj as any)[key], ...keysToIgnore);
		}
	}
	return result;
}

@Component({
	selector: "adm-bi-adm-config",
	templateUrl: "./bi-adm-config.component.html",
	styleUrls: ["./bi-adm-config.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiAdmConfigComponent implements OnInit, OnDestroy {
	formConfigMovimentacaoContrato: FormGroup = new FormGroup({
		exibirAgregadores: new FormControl(),
		qtdCheckinConsiderarAgregador: new FormControl(),
		qtdDiasConsiderarAgregador: new FormControl(),
	});

	formConfigCicloVida: FormGroup = new FormGroup({
		considerarLtvRealizado: new FormControl(),
		considerarRenovacaoNoCalculoLt: new FormControl(),
	});

	formConfigTicketMedio: FormGroup = new FormGroup({
		incluirProdutoCompetencia: new FormControl(),
		ativosPor: new FormControl(),
	});

	configuracaoBi: Array<ConfiguracaoBi> = new Array<ConfiguracaoBi>();
	configsTicketMedio: Array<ConfiguracaoBi> = new Array<ConfiguracaoBi>();
	configsCicloVida: Array<ConfiguracaoBi> = new Array<ConfiguracaoBi>();
	configsMovimentacaoContrato: Array<ConfiguracaoBi> =
		new Array<ConfiguracaoBi>();

	configuracaoTipoEnum = ConfiguracaoTipoEnum;

	private _destroy$: Subject<void> = new Subject();

	constructor(private biSidenavRef: BiSidenavRef<BiAdmConfigComponent>) {}

	ngOnInit() {
		this._buildConfigForms();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	private _buildConfigForms() {
		this._buildFormTicketMedio();
		this._buildFormMovimentacaoContrato();
		this._buildFormCicloVida();
	}

	private _buildFormTicketMedio() {
		this.configsTicketMedio = this.buildFormByConfig(
			this.formConfigTicketMedio,
			BiEnum.TICKET_MEDIO
		);
	}

	private _buildFormMovimentacaoContrato() {
		this.configsMovimentacaoContrato = this.buildFormByConfig(
			this.formConfigMovimentacaoContrato,
			BiEnum.ROTATIVIDADE_CONTRATO
		);
	}

	private _buildFormCicloVida() {
		this.configsCicloVida = this.buildFormByConfig(
			this.formConfigCicloVida,
			BiEnum.LTV
		);
	}

	private buildFormByConfig(form: FormGroup, bi: BiEnum) {
		let configs = getConfigByBi(bi, this.configuracaoBi);
		configs = configs.sort((c1, c2) => {
			if (c1.configuracao.codigo < c2.configuracao.codigo) {
				return -1;
			}
			if (c1.configuracao.codigo > c2.configuracao.codigo) {
				return 1;
			}
			return 0;
		});
		configs.forEach((cfg) => {
			const control = new FormControl();
			if (cfg.valor && cfg.valor.length > 0) {
				switch (cfg.configuracao.tipo) {
					case ConfiguracaoTipoEnum.BOOLEAN:
						control.setValue(cfg.valorAsBoolean);
						break;
					case ConfiguracaoTipoEnum.RADIO:
					case ConfiguracaoTipoEnum.COMBO:
						control.setValue(cfg.valorAsInteger);
						break;
				}
			} else {
				control.setValue(cfg.configuracao.valorPadrao);
			}
			cfg.configuracao.formControlName = cfg.configuracao.name.toLowerCase();
			cfg.configuracao.formControl = control;
			form.addControl(cfg.configuracao.formControlName, control);
		});

		return configs;
	}

	save() {
		this.configuracaoBi.forEach((cfg) => {
			switch (cfg.bi.codigo) {
				case BiEnum.TICKET_MEDIO:
					this._convertFormToConfig(
						this.formConfigTicketMedio,
						this.configsTicketMedio,
						cfg
					);
					break;
				case BiEnum.ROTATIVIDADE_CONTRATO:
					this._convertFormToConfig(
						this.formConfigMovimentacaoContrato,
						this.configsMovimentacaoContrato,
						cfg
					);
					break;
				case BiEnum.LTV:
					this._convertFormToConfig(
						this.formConfigCicloVida,
						this.configsCicloVida,
						cfg
					);
					break;
			}
		});

		this.biSidenavRef.close(
			deepCopy(this.configuracaoBi, "formControlName", "formControl")
		);
	}

	private _convertFormToConfig(
		form: FormGroup,
		config: Array<ConfiguracaoBi>,
		cfg: ConfiguracaoBi
	) {
		if (form.contains(cfg.configuracao.formControlName)) {
			cfg.valor = form.get(cfg.configuracao.formControlName).value.toString();
		}
	}

	close() {
		this.biSidenavRef.close();
	}
}
