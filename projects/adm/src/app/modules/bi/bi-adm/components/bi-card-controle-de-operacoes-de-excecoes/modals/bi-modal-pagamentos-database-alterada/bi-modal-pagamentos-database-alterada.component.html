<adm-bi-modal-content
	[modalTitle]="modalTitle"
	[shareUrl]="shareUrl"
	[filters]="filters"
	[page]="page"
	[pageSize]="size"
	[totalItems]="totalItems"
	[colunasShare]="colunasShare"
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)"
	[withPaginatorDiviser]="true">
	<ds3-table ds3FirstColumnFixed>
		<table ds3DataTable [stateManager]="tableState">
			<ng-container ds3TableColumn="pessoa">
				<th *ds3TableHeaderCell>Nome</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					<button ds3-text-button (click)="openCliente(item)">
						{{ item?.cliente?.pessoa?.nome | titlecase }}
					</button>
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataLancamento">
				<th *ds3TableHeaderCell class="align-center">Dt. Lançamento</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.dataLancamento | date : "dd/MM/yyyy" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataPagamento">
				<th *ds3TableHeaderCell class="align-center">Dt. Pagamento</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.dataPagamento | date : "dd/MM/yyyy" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataAlteracaoManual">
				<th *ds3TableHeaderCell class="align-center">Dt. Alteração</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.dataAlteracaoManual | date : "dd/MM/yyyy" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="valor">
				<th *ds3TableHeaderCell class="align-center">Valor</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.valor | currency : "BRL" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="formaPagamento">
				<th *ds3TableHeaderCell class="align-center">Forma de pagamento</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.formaPagamento?.descricao }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="responsavelPagamento">
				<th *ds3TableHeaderCell class="align-center">Resp. Pagamento</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.responsavelPagamento?.nome | titlecase }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataQuitacao">
				<th *ds3TableHeaderCell class="align-center">Dt. Quitação</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.dataQuitacao | date : "dd/MM/yyyy" }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				ds3-icon-button
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				(click)="triggerSortToggle()">
				<i
					class="pct"
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>

			<tfoot class="ds3-table-foot">
				<tr>
					<td>-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">
						{{ movPagamentoTotais?.valorTotalPagamentos | currency : "BRL" }}
					</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
					<td class="align-center">-</td>
				</tr>
			</tfoot>
		</table>
	</ds3-table>
</adm-bi-modal-content>
