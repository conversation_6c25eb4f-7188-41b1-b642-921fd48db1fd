import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalParcelasCanceladasComponent } from "./bi-modal-parcelas-canceladas.component";

describe("BiModalParcelasCanceladasComponent", () => {
	let component: BiModalParcelasCanceladasComponent;
	let fixture: ComponentFixture<BiModalParcelasCanceladasComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalParcelasCanceladasComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalParcelasCanceladasComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
