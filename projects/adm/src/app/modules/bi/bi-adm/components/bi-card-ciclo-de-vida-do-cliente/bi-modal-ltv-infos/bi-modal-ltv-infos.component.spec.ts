import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalLtvInfosComponent } from "./bi-modal-ltv-infos.component";

describe("BiModalLtvInfosComponent", () => {
	let component: BiModalLtvInfosComponent;
	let fixture: ComponentFixture<BiModalLtvInfosComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalLtvInfosComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalLtvInfosComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
