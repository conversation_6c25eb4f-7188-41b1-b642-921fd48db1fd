@import "dist/ui-kit/assets/ds3/typography/mixins";

:host {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.bi-config-header {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		background: var(--color-background-plane-2);
		border-bottom: 1px solid var(--color-support-gray-3);
		padding: 16px 16px 7px 16px;

		.bi-config-title {
			@include apply-typography-style("title", 4);
			color: var(--color-typography-default-title);
		}
		button {
			margin-right: 8px;
		}
	}

	.bi-config-content {
		height: 100%;
		margin-top: 16px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		overflow-x: hidden;

		.bi-config-hint {
			background: var(--color-background-plane-3);
			padding: 4px 16px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 16px;

			i {
				display: inline-block;
				margin-right: 8px;
			}
		}

		form {
			.bi-config-content-title {
				margin-bottom: 8px;
				padding: 0 16px;
			}

			.bi-config-content-item {
				padding: 16px;
				margin-bottom: 8px;
				display: flex;
				ds3-form-field {
					width: 100%;
				}

				&:nth-child(even) {
					background: var(--color-background-plane-3);
				}

				.bi-config-radio-label {
					margin-right: 8px;
				}

				&.bi-config-content-item-dark {
					background: var(--color-background-plane-3);
				}
			}
		}

		ds3-diviser {
			margin-bottom: 16px;
		}
	}

	.bi-config-actions {
		background-color: var(--color-background-plane-1);
		display: grid;
		gap: 16px;
		grid-template-columns: 1fr 1fr;
		padding: 8px 16px;
	}
}
