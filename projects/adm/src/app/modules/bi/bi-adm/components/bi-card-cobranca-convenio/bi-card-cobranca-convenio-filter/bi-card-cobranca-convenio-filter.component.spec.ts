import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiCardCobrancaConvenioFilterComponent } from "./bi-card-cobranca-convenio-filter.component";

describe("BiCardCobrancaConvenioFilterComponent", () => {
	let component: BiCardCobrancaConvenioFilterComponent;
	let fixture: ComponentFixture<BiCardCobrancaConvenioFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiCardCobrancaConvenioFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiCardCobrancaConvenioFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
