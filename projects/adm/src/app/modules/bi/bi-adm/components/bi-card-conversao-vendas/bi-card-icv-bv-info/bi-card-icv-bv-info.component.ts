import {
	ChangeDetectionStrategy,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	ViewEncapsulation,
} from "@angular/core";
import { ConversaoVendaResponseModel } from "bi-ms-api";
import { InfoData } from "ui-kit";

@Component({
	selector: "adm-bi-card-icv-bv-info",
	templateUrl: "./bi-card-icv-bv-info.component.html",
	styleUrls: ["./bi-card-icv-bv-info.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardIcvBvInfoComponent implements OnInit, OnChanges {
	@Input()
	conversaoVendaResponseModel: ConversaoVendaResponseModel;

	@Input()
	tipoData: number;

	infoArrayIcvFaturamento: Array<InfoData>;

	constructor() {}

	ngOnInit() {
		this._initInfoArray();
	}

	ngOnChanges(changes: SimpleChanges) {
		const conversaoVendaResponseModel = changes["conversaoVendaResponseModel"];
		if (
			conversaoVendaResponseModel &&
			!conversaoVendaResponseModel.firstChange
		) {
			this.conversaoVendaResponseModel =
				conversaoVendaResponseModel.currentValue;
		}
		const tipoData = changes["tipoData"];
		if (tipoData && !tipoData.firstChange) {
			this.tipoData = tipoData.currentValue;
		}

		this._initInfoArray();
	}

	onClickIcvFaturamento($event: any) {}

	private _initInfoArray() {
		this.infoArrayIcvFaturamento = new Array<InfoData>(
			{
				info: {
					value:
						(this.tipoData === 1
							? this.conversaoVendaResponseModel.indiceConversaoVendaDia
							: this.conversaoVendaResponseModel.indiceConversaoVendaMes) / 100,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: false,
					isPercentage: true,
					state: "disable",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "ICV",
				},
			},
			{
				info: {
					value:
						this.tipoData === 1
							? this.conversaoVendaResponseModel.faturamentoDia
							: this.conversaoVendaResponseModel.faturamentoMes,
					size: "24px",
					afterIcon: undefined,
					beforeIcon: undefined,
					isMonetary: true,
					isPercentage: false,
					state: "disable",
				},
				auxiliary: undefined,
				textButton: undefined,
				overline: {
					avatarImage: undefined,
					dotHexColor: undefined,
					text: "Faturamento",
				},
			}
		);
	}
}
