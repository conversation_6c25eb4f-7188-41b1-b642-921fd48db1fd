import { TraducoesXinglingComponent } from "ui-kit";
import { createSection } from "../../../bi-shared/models/bi-config-section.model";

import { BiModalAlunosBolsaComponent } from "./modals/bi-modal-alunos-bolsa/bi-modal-alunos-bolsa.component";
import { BiModalAlunosExcluidosBdComponent } from "./modals/bi-modal-alunos-excluidos-bd/bi-modal-alunos-excluidos-bd.component";
import { BiModalAlunosExcluidosTwComVinculosComponent } from "./modals/bi-modal-alunos-excluidos-tw-com-vinculos/bi-modal-alunos-excluidos-tw-com-vinculos.component";
import { BiModalClientesComBonusComponent } from "./modals/bi-modal-clientes-com-bonus/bi-modal-clientes-com-bonus.component";
import { BiModalClientesContratoAtivoComPeriodoAcessoComponent } from "./modals/bi-modal-clientes-contrato-ativo-com-periodo-acesso/bi-modal-clientes-contrato-ativo-com-periodo-acesso.component";
import { BiModalClientesFreepassComponent } from "./modals/bi-modal-clientes-freepass/bi-modal-clientes-freepass.component";
import { BiModalClientesGympassComponent } from "./modals/bi-modal-clientes-gympass/bi-modal-clientes-gympass.component";
import { BiModalClientesPossuemAutorizacaoCobContratoComponent } from "./modals/bi-modal-clientes-possuem-autorizacao-cob-contrato/bi-modal-clientes-possuem-autorizacao-cob-contrato.component";
import { BiModalConsultoresContratosAlteradosComponent } from "./modals/bi-modal-consultores-contratos-alterados/bi-modal-consultores-contratos-alterados.component";
import { BiModalContratosCanceladosComponent } from "./modals/bi-modal-contratos-cancelados/bi-modal-contratos-cancelados.component";
import { BiModalContratosDatabaseAlteradaComponent } from "./modals/bi-modal-contratos-database-alterada/bi-modal-contratos-database-alterada.component";
import { BiModalContratosEstornadosPorTipoUsuarioComponent } from "./modals/bi-modal-contratos-estornados-por-tipo-usuario/bi-modal-contratos-estornados-por-tipo-usuario.component";
import { BiModalExclusaoVisitantesComponent } from "./modals/bi-modal-exclusao-visitantes/bi-modal-exclusao-visitantes.component";
import { BiModalOperacoesContratoRetroativosComponent } from "./modals/bi-modal-operacoes-contrato-retroativos/bi-modal-operacoes-contrato-retroativos.component";
import { BiModalPagamentosDatabaseAlteradaComponent } from "./modals/bi-modal-pagamentos-database-alterada/bi-modal-pagamentos-database-alterada.component";
import { BiModalPagamentosEditadosComponent } from "./modals/bi-modal-pagamentos-editados/bi-modal-pagamentos-editados.component";
import { BiModalParcelasCanceladasComponent } from "./modals/bi-modal-parcelas-canceladas/bi-modal-parcelas-canceladas.component";
import { BiModalParcelasRenegociadasComponent } from "./modals/bi-modal-parcelas-renegociadas/bi-modal-parcelas-renegociadas.component";
import { BiModalRecibosEstornadosComponent } from "./modals/bi-modal-recibos-estornados/bi-modal-recibos-estornados.component";
import { BiModalTotalValorNaoArrecadadoDevidoDescontosComponent } from "./modals/bi-modal-total-valor-nao-arrecadado-devido-descontos/bi-modal-total-valor-nao-arrecadado-devido-descontos.component";
import { BiModalContratosCanceladosTransferidosParaOutroAlunoComponent } from "./modals/bi-modal-transferidos-com-contratos-cancelados/bi-modal-contratos-cancelados-transferidos-para-outro-aluno.component";

export const sectionsData = (
	traducoes: TraducoesXinglingComponent
): Map<string, any> => {
	return new Map<string, any>([
		qtdClientesInativosPeriodoAcesso(traducoes),
		qtdOperacoesContratoRetroativas(traducoes),
		qtdExclusaoVisitantes(traducoes),
		qtdAlteracaoConsultorContrato(traducoes),
		qtdEstornoContratoAdmin(traducoes),
		qtdEstornoContratoRecorrencia(traducoes),
		qtdEstornoContratoUsuarioComum(traducoes),
		qtdParcelasCanceladas(traducoes),
		qtdEstornoRecibo(traducoes),
		qtdAlteracoesDataBaseContrato(traducoes),
		qtdAlteracoesDataBasePagamento(traducoes),
		qtdRenegociacaoParcelaRetroativa(traducoes),
		qtdAlteracoesRecibo(traducoes),
		qtdContratosCancelamento(traducoes),
		qtdContratosTransferidosCancelados(traducoes),
		qtdClientesComBonus(traducoes),
		qtdClientesComFreePass(traducoes),
		qtdClientesComGymPass(traducoes),
		qtdContratosTipoBolsa(traducoes),
		qtdClientesAutorizacaoNaoRenovavel(traducoes),
		qtdClientesAutorizacaoRenovavel(traducoes),
		valorDescontos(traducoes),
		qtdClientesExcluidosTreinoWeb(traducoes),
		qtdClientesExcluidos(traducoes),
	]);
};

const qtdClientesInativosPeriodoAcesso = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdClientesInativosComPeriodoAcesso",
		id: "cliente-contrato-inativo-com-periodo-acesso",
		textKey: "textClientesInativosComPeriodoAcesso",
		tooltipKey: "tooltipClientesInativosComPeriodoAcesso",
		traducoes,
		modalConfig: {
			componentType: BiModalClientesContratoAtivoComPeriodoAcessoComponent,
		},
	});

const qtdOperacoesContratoRetroativas = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdOperacoesContratoRetroativas",
		id: "operacoes-contrato-retroativas",
		textKey: "textOperacoesContratoRetroativas",
		tooltipKey: "tooltipOperacoesContratoRetroativas",
		traducoes,
		modalConfig: {
			componentType: BiModalOperacoesContratoRetroativosComponent,
		},
	});

const qtdExclusaoVisitantes = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdExclusaoVisitantes",
		id: "exclusao-visitantes",
		textKey: "textExclusaoVisitantes",
		tooltipKey: "tooltipExclusaoVisitantes",
		traducoes,
		modalConfig: {
			componentType: BiModalExclusaoVisitantesComponent,
		},
	});

const qtdAlteracaoConsultorContrato = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdAlteracaoConsultorContrato",
		id: "consultores-contrato-alterados",
		textKey: "textAlteracaoConsultorContrato",
		tooltipKey: "tooltipAlteracaoConsultorContrato",
		traducoes,
		modalConfig: {
			componentType: BiModalConsultoresContratosAlteradosComponent,
		},
	});

const qtdEstornoContratoAdmin = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdEstornoContratoAdmin",
		id: "contratos-estornados-administrador",
		textKey: "textEstornoContratoAdmin",
		tooltipKey: "tooltipEstornoContratoAdmin",
		traducoes,
		modalConfig: {
			componentType: BiModalContratosEstornadosPorTipoUsuarioComponent,
			config: {
				data: {
					dataType: "admin",
				},
			},
		},
	});

const qtdEstornoContratoRecorrencia = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdEstornoContratoRecorrencia",
		id: "contratos-estornados-automaticamente",
		textKey: "textEstornoContratoRecorrencia",
		tooltipKey: "tooltipEstornoContratoRecorrencia",
		traducoes,
		modalConfig: {
			componentType: BiModalContratosEstornadosPorTipoUsuarioComponent,
			config: {
				data: {
					dataType: "recorrencia",
				},
			},
		},
	});

const qtdEstornoContratoUsuarioComum = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdEstornoContratoUsuarioComum",
		id: "contratos-estornados-usuario-comum",
		textKey: "textEstornoContratoUsuarioComum",
		tooltipKey: "tooltipEstornoContratoUsuarioComum",
		traducoes,
		modalConfig: {
			componentType: BiModalContratosEstornadosPorTipoUsuarioComponent,
		},
	});

const qtdParcelasCanceladas = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdParcelasCanceladas",
		id: "parcelas-canceladas",
		textKey: "textParcelasCanceladas",
		tooltipKey: "tooltipParcelasCanceladas",
		traducoes,
		modalConfig: {
			componentType: BiModalParcelasCanceladasComponent,
		},
	});

const qtdEstornoRecibo = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdEstornoRecibo",
		id: "estorno-recibo",
		textKey: "textEstornoRecibo",
		tooltipKey: "tooltipEstornoRecibo",
		traducoes,
		modalConfig: {
			componentType: BiModalRecibosEstornadosComponent,
			config: {
				data: {
					nomeEntidade: "RECIBOPAGAMENTO",
					operacao: "ESTORNO - RECIBO PAGAMENTO",
				},
			},
		},
	});

const qtdAlteracoesDataBaseContrato = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdAlteracoesDataBaseContrato",
		id: "alteracoes-data-base-contrato",
		textKey: "textAlteracoesDataBaseContrato",
		tooltipKey: "tooltipAlteracoesDataBaseContrato",
		traducoes,
		modalConfig: {
			componentType: BiModalContratosDatabaseAlteradaComponent,
		},
	});

const qtdAlteracoesDataBasePagamento = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdAlteracoesDataBasePagamento",
		id: "alteracoes-data-base-pagamento",
		textKey: "textAlteracoesDataBasePagamento",
		tooltipKey: "tooltipAlteracoesDataBasePagamento",
		traducoes,
		modalConfig: {
			componentType: BiModalPagamentosDatabaseAlteradaComponent,
		},
	});

const qtdRenegociacaoParcelaRetroativa = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdRenegociacaoParcelaRetroativa",
		id: "renegociacao-parcela-retroativa",
		textKey: "textRenegociacaoParcelaRetroativa",
		tooltipKey: "tooltipRenegociacaoParcelaRetroativa",
		traducoes,
		modalConfig: {
			componentType: BiModalParcelasRenegociadasComponent,
		},
	});

const qtdAlteracoesRecibo = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdAlteracoesRecibo",
		id: "alteracoes-recibo",
		textKey: "textAlteracoesRecibo",
		tooltipKey: "tooltipAlteracoesRecibo",
		traducoes,
		modalConfig: {
			componentType: BiModalPagamentosEditadosComponent,
		},
	});

const qtdContratosCancelamento = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdContratosCancelamento",
		id: "contratos-cancelamento",
		textKey: "textContratosCancelamento",
		tooltipKey: "tooltipContratosCancelamento",
		traducoes,
		modalConfig: {
			componentType: BiModalContratosCanceladosComponent,
		},
	});

const qtdContratosTransferidosCancelados = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdContratosTransferidosCancelados",
		id: "contratos-transferidos-cancelados",
		textKey: "textContratosTransferidosCancelados",
		tooltipKey: "tooltipContratosTransferidosCancelados",
		traducoes,
		modalConfig: {
			componentType:
				BiModalContratosCanceladosTransferidosParaOutroAlunoComponent,
		},
	});

const qtdClientesComBonus = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdClientesComBonus",
		id: "clientes-com-bonus",
		textKey: "textClientesComBonus",
		tooltipKey: "tooltipClientesComBonus",
		traducoes,
		modalConfig: {
			componentType: BiModalClientesComBonusComponent,
		},
	});

const qtdClientesComFreePass = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdClientesComFreePass",
		id: "clientes-com-freepass",
		textKey: "textClientesComFreePass",
		tooltipKey: "tooltipClientesComFreePass",
		traducoes,
		modalConfig: {
			componentType: BiModalClientesFreepassComponent,
		},
	});

const qtdClientesComGymPass = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdClientesComGymPass",
		id: "clientes-com-gympass",
		textKey: "textClientesComGymPass",
		tooltipKey: "tooltipClientesComGymPass",
		traducoes,
		modalConfig: {
			componentType: BiModalClientesGympassComponent,
		},
	});

const qtdContratosTipoBolsa = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdContratosTipoBolsa",
		id: "contratos-tipo-bolsa",
		textKey: "textContratosTipoBolsa",
		tooltipKey: "tooltipContratosTipoBolsa",
		traducoes,
		modalConfig: {
			componentType: BiModalAlunosBolsaComponent,
		},
	});

const qtdClientesAutorizacaoNaoRenovavel = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdClientesAutorizacaoNaoRenovavel",
		id: "clientes-autorizacao-nao-renovavel",
		textKey: "textClientesAutorizacaoNaoRenovavel",
		tooltipKey: "tooltipClientesAutorizacaoNaoRenovavel",
		traducoes,
		modalConfig: {
			componentType: BiModalClientesPossuemAutorizacaoCobContratoComponent,
			config: {
				data: {
					filters: {
						comAutorizacaoRenovavel: false,
					},
				},
			},
		},
	});

const qtdClientesAutorizacaoRenovavel = (
	traducoes: TraducoesXinglingComponent
) =>
	createSection({
		key: "qtdClientesAutorizacaoRenovavel",
		id: "clientes-autorizacao-renovavel",
		textKey: "textClientesAutorizacaoRenovavel",
		tooltipKey: "tooltipClientesAutorizacaoRenovavel",
		traducoes,
		modalConfig: {
			componentType: BiModalClientesPossuemAutorizacaoCobContratoComponent,
			config: {
				data: {
					filters: {
						comAutorizacaoRenovavel: true,
					},
				},
			},
		},
	});

const valorDescontos = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "valorDescontos",
		id: "valor-descontos",
		textKey: "textValorDescontos",
		tooltipKey: "tooltipValorDescontos",
		traducoes,
		modalConfig: {
			componentType: BiModalTotalValorNaoArrecadadoDevidoDescontosComponent,
		},
		monetary: true,
	});

const qtdClientesExcluidosTreinoWeb = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdClientesExcluidosTreinoWeb",
		id: "clientes-excluidos-treino-web",
		textKey: "textClientesExcluidosTreinoWeb",
		tooltipKey: "tooltipClientesExcluidosTreinoWeb",
		traducoes,
		modalConfig: {
			componentType: BiModalAlunosExcluidosTwComVinculosComponent,
		},
	});

const qtdClientesExcluidos = (traducoes: TraducoesXinglingComponent) =>
	createSection({
		key: "qtdClientesExcluidos",
		id: "clientes-excluidos",
		textKey: "textClientesExcluidos",
		tooltipKey: "tooltipClientesExcluidos",
		traducoes,
		modalConfig: {
			componentType: BiModalAlunosExcluidosBdComponent,
		},
	});
