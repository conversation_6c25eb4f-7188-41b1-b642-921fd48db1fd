<div class="bi-card movimentacao-contratos">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Cobrança por Convênio</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				[disabled]="loading"
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				[disabled]="loading"
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.dataInicio | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.incluirContratosCancelados">
				Incluir contratos cancelados
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.somenteParcelasForaMes">
				Parcelas em vencimento em outros meses
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.somenteParcelasMes">
				Parcelas em vencimento no próprio mês
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.convenios && filtros?.convenios?.length !== 0">
				<span
					[ds3Tooltip]="tooltipConveniosRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.convenios?.length }}
					{{
						filtros?.convenios?.length > 1
							? "convênios de cobrança selecionados"
							: "convenio de cobrança selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipConveniosRef>
				<div *ngFor="let convenio of filtros?.convenios">
					{{ convenio.descricao }}
				</div>
			</ng-template>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabData.sections">
				<div class="bi-card-content-section-content-list">
					<ng-container
						*ngFor="
							let listItem of section?.listItens;
							let index = index;
							let odd = odd
						">
						<div
							*ngIf="listItem?.value > 0 || showHiddenFields"
							[ngClass]="[
								'bi-card-content-section-content-list-item',
								'hoverable'
							]"
							[ds3Tooltip]="listItem.tooltipText"
							[asInnerHtml]="true"
							[multiline]="true">
							<div
								class="bi-card-content-section-content-list-item-text"
								*ngIf="listItem?.text">
								{{ listItem.text }}
							</div>
							<button
								ds3-text-button
								size="sm"
								(click)="openModalItem(listItem)"
								class="bi-card-content-section-content-list-item-value"
								*ngIf="listItem?.value >= 0">
								{{
									listItem.monetary
										? (listItem.value | currency : "BRL")
										: listItem.value
								}}
							</button>
							<div
								class="bi-card-content-section-content-list-item-percentage"
								*ngIf="listItem?.percentage >= 0">
								{{ listItem.percentage | number : "1.2-2" }}%
							</div>
							<div class="bi-card-content-section-content-list-item-percentage">
								<span *ngIf="listItem?.monetaryValue">
									{{ listItem?.monetaryValue || 0 | currency : "BRL" }}
								</span>
								<span *ngIf="!listItem?.monetaryValue">-</span>
							</div>
						</div>
					</ng-container>
					<div *ngIf="isAllEmpty && !showHiddenFields" class="div-empty">
						<div class="d-flex flex-column align-items-center">
							<img
								class="icon-empty"
								src="pacto-ui/images/empty-state-search.svg" />
							<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
								Nenhuma operação registrada!
							</div>
						</div>
					</div>
					<div class="bi-card-content-section-content-list-actions">
						<button ds3-text-button (click)="mostrarMais()">
							{{ showHiddenFields ? "Contrair" : "Expandir" }} listagem
						</button>
					</div>
				</div>
			</div>
			<ds3-diviser></ds3-diviser>
		</div>

		<ds3-table>
			<table
				ds3DataTable
				[stateManager]="tableStateTotalizadorParcelas"
				[smallRows]="true"
				style="table-layout: fixed">
				<ng-container ds3TableColumn="label">
					<th *ds3TableHeaderCell style="width: 50%; font-size: 14px">
						Totalizador de Parcelas
					</th>
					<td
						*ds3TableCell="let item"
						style="width: 50%; font-size: 14px; line-height: 125%">
						<span>
							{{ item?.label }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="quantidade">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px">
						Qtd.
					</th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span>
							{{ item?.quantidade }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="valor">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px">
						Valor
					</th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span>
							{{ item?.valor || 0 | currency : "BRL" }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="porcentagem">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px">
						%
					</th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span>{{ item?.porcentagem | number : "1.2-2" }}</span>
					</td>
				</ng-container>

				<tr *ds3TableRow></tr>
				<tr *ds3TableEmptyRow class="ds3-table-empty">
					<td></td>
				</tr>

				<tbody *ds3TableLoading>
					<tr>
						<td>
							<div class="bi-modal-table-loader" role="status">
								<img
									alt="Loading pacto"
									src="pacto-ui/images/gif/loading-pacto.gif" />
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</ds3-table>
		<ds3-table style="padding-bottom: unset">
			<table
				ds3DataTable
				[stateManager]="tableStateTotalizadorAguardandoRetorno"
				[smallRows]="true"
				style="table-layout: fixed">
				<ng-container ds3TableColumn="label">
					<th *ds3TableHeaderCell style="width: 50%; font-size: 14px"></th>
					<td
						*ds3TableCell="let item"
						style="width: 50%; font-size: 14px; line-height: 125%">
						<span>
							{{ item?.label }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="quantidade">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px"></th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span>
							{{ item?.quantidade }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="valor">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px"></th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span>
							{{ item?.valor || 0 | currency : "BRL" }}
						</span>
					</td>
				</ng-container>

				<ng-container ds3TableColumn="porcentagem">
					<th
						*ds3TableHeaderCell
						style="text-align: right; width: 20%; font-size: 14px"></th>
					<td
						*ds3TableCell="let item"
						style="
							text-align: right;
							width: 20%;
							font-size: 14px;
							line-height: 125%;
						">
						<span>{{ item?.porcentagem | number : "1.2-2" }}</span>
					</td>
				</ng-container>

				<tr *ds3TableRow></tr>
				<tr *ds3TableEmptyRow class="ds3-table-empty">
					<td></td>
				</tr>

				<tbody *ds3TableLoading>
					<tr>
						<td>
							<div class="bi-modal-table-loader" role="status">
								<img
									alt="Loading pacto"
									src="pacto-ui/images/gif/loading-pacto.gif" />
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</ds3-table>

		<ds3-diviser></ds3-diviser>

		<div
			class="bi-card-content-chart"
			*ngIf="!loading"
			style="margin-top: 16px">
			<h2 class="pct-title4 cor-type-default-text">Resultado do DCC</h2>
			<ds3-chart-mixed
				[disableAnimations]="true"
				[isStacked]="true"
				[xaxis]="chartOptions.xaxis"
				[yaxis]="chartOptions.yaxis"
				[series]="chartOptions.series"
				[labels]="chartOptions.xaxis.categories"
				[colorOrder]="[
					'blue',
					'green',
					'red',
					'yellow',
					'orange',
					'pink',
					'purple',
					'lightblue'
				]"
				[dataLabels]="dataLabelsChart"
				[tooltip]="tooltipChartResultadoDCC"></ds3-chart-mixed>
		</div>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
