import { BiCardIntegradoresDeAcessoFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-integradores-de-acesso/bi-card-integradores-de-acesso-filter/bi-card-integradores-de-acesso-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiMsApiIntegradoresAcessoService,
	DadosGraficoAcessoIntegradoresAcesso,
	FiltroIntegradoresAcesso,
	IntegradoresAcessoResponseModel,
	isTipoIntegradorGympass,
	isTipoIntegradorTotalpass,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { BarCorFundo, BarCorLinha } from "ui-kit";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { TabData } from "../../bi-adm.model";

@Component({
	selector: "adm-bi-card-integradores-de-acesso",
	templateUrl: "./bi-card-integradores-de-acesso.component.html",
	styleUrls: ["./bi-card-integradores-de-acesso.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardIntegradoresDeAcessoComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	tabData: TabData;
	config;
	loading: boolean = false;

	constructor(
		protected dialog: MatDialog,
		protected cd: ChangeDetectorRef,
		protected breakpointObserver: BreakpointObserver,
		protected currencyPipe: CurrencyPipe,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biMsApiAcessoGympassService: BiMsApiIntegradoresAcessoService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		this.init();
		this._initFilters();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		this.destroy();
	}

	private _initFilters() {
		this.filtros.dataFim = this.filtros.data;
		this.filtros.tipoIntegradorAcesso = 0;
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Integradores de acesso",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-gympass-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiCardIntegradoresDeAcessoFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull = false) {
		this.loading = true;

		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		const filtroIntegradoresAcesso: FiltroIntegradoresAcesso = {
			dataInicioMes: this.filtros.dataInicio.getTime(),
			dataFimMes: this.filtros.dataFim.getTime(),
			empresa: this.filtros.empresa,
			tipoIntegradorAcesso: this.filtros.tipoIntegradorAcesso || 0,
			acessoCheckinGympass: this.filtros.acessoCheckinGympass || false,
		};

		this.cd.detectChanges();
		this.biMsApiAcessoGympassService
			.list({
				filtroIntegradoresAcesso,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	private _populateTabData(v?: IntegradoresAcessoResponseModel) {
		if (!v) {
			v = {
				acessosPorDiaGympass: new Map<number, number>(),
				acessosPorDiaTotalPass: new Map<number, number>(),
				totalAlunosComIntegradoresAcesso: 0,
				totalAcessoComIntegradoresAcesso: 0,
				totalAcessosGympass: 0,
				totalAcessoClienteGympass: new Map<number, number>(),
				totalAcessoClienteTotalpass: new Map<number, number>(),
				totalAcessosTotalPass: 0,
				mediaAcessoFaixa1: 0,
				mediaAcessoFaixa2: 0,
				mediaAcessoFaixa3: 0,
				mediaAcessoFaixa4: 0,
				qtdAcessoGymPassInicioFaixa1: 0,
				qtdAcessoGymPassInicioFaixa2: 0,
				qtdAcessoGymPassInicioFaixa3: 0,
				qtdAcessoGymPassInicioFaixa4: 0,
				qtdAcessoGymPassFinalFaixa1: 0,
				qtdAcessoGymPassFinalFaixa2: 0,
				qtdAcessoGymPassFinalFaixa3: 0,
				qtdAcessoGymPassFinalFaixa4: 0,
				dadosGrafico: new Array<DadosGraficoAcessoIntegradoresAcesso>(),
			} as IntegradoresAcessoResponseModel;
		}

		const diasArray = new Array<number>();
		const qtdAcessos = new Array<number>();
		if (v.dadosGrafico && v.dadosGrafico.length > 0) {
			const acessosPorDia = new Map<number, number>();
			v.dadosGrafico
				.filter((dadosGrafico) => {
					if (
						isTipoIntegradorGympass(dadosGrafico.tipoIntegrador) &&
						isTipoIntegradorGympass(this.filtros.tipoIntegrador)
					) {
						return true;
					}
					if (
						isTipoIntegradorTotalpass(dadosGrafico.tipoIntegrador) &&
						isTipoIntegradorTotalpass(this.filtros.tipoIntegrador)
					) {
						return true;
					}
					return false;
				})
				.forEach((dadosGrafico) => {
					if (!diasArray.includes(dadosGrafico.dia)) {
						diasArray.push(dadosGrafico.dia);
					}
					const dia = dadosGrafico.dia;
					let acessosExistente = 0;
					if (acessosPorDia.has(dia)) {
						acessosExistente = acessosPorDia.get(dia);
					}
					acessosPorDia.set(dia, acessosExistente + dadosGrafico.qtdAcessos);
				});

			diasArray.push(...Array.from(acessosPorDia.keys()));
			qtdAcessos.push(...Array.from(acessosPorDia.values()));
		}
		this.tabData = {
			diaInicio: this.filtros.dataInicio,
			diaFim: this.filtros.dataFim,
			title: "Integradores de acesso",
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.totalAlunosComIntegradoresAcesso,
								size: "24px",
								state: "info",
							},
							overline: {
								text: "Alunos com integradores de acesso",
							},
						},
						{
							info: {
								value: v.totalAcessoComIntegradoresAcesso,
								size: "24px",
								state: "info",
							},
							overline: {
								text: "Acessos com integradores de acesso",
							},
						},
					],
				},
				{
					title: "Quantidade de alunos por integradores de acesso",
					chartItens: [
						{
							type: "column",
							series: [
								{
									name: "Wellhub",
									data: [v.totalAcessosGympass || 0],
								},
								{
									name: "TotalPass",
									data: [v.totalAcessosTotalPass || 0],
								},
							],
							xaxis: {
								type: "category",
								categories: [""],
							},
							yaxis: {
								stepSize: 20,
								logBase: 10,
							},
						},
					],
				},
				{
					title: "Média de acessos",
					barItens: [
						{
							etapas: [
								{
									nome: "etapa",
									mostraNome: false,
									corLinha: BarCorLinha.orange,
									corFundo: BarCorFundo.default_bg,
								},
							],
							info: `${v.mediaAcessoFaixa1} alunos`,
							nome: `Entre ${v.qtdAcessoGymPassInicioFaixa1} e ${v.qtdAcessoGymPassFinalFaixa1} acessos`,
							mostraPorcentagem: true,
							porcentagem:
								(v.mediaAcessoFaixa1 / v.totalAcessoComIntegradoresAcesso) *
								100,
						},
						{
							etapas: [
								{
									nome: "etapa",
									mostraNome: false,
									corLinha: BarCorLinha.purple,
									corFundo: BarCorFundo.default_bg,
								},
							],
							info: `${v.mediaAcessoFaixa2} alunos`,
							nome: `Entre ${v.qtdAcessoGymPassInicioFaixa2} e ${v.qtdAcessoGymPassFinalFaixa2} acessos`,
							mostraPorcentagem: true,
							porcentagem:
								(v.mediaAcessoFaixa2 / v.totalAcessoComIntegradoresAcesso) *
								100,
						},
						{
							etapas: [
								{
									nome: "etapa",
									mostraNome: false,
									corLinha: BarCorLinha.lightblue,
									corFundo: BarCorFundo.default_bg,
								},
							],
							info: `${v.mediaAcessoFaixa3} alunos`,
							nome: `Entre ${v.qtdAcessoGymPassInicioFaixa3} e ${v.qtdAcessoGymPassFinalFaixa3} acessos`,
							mostraPorcentagem: true,
							porcentagem:
								(v.mediaAcessoFaixa3 / v.totalAcessoComIntegradoresAcesso) *
								100,
						},
						{
							etapas: [
								{
									nome: "etapa",
									mostraNome: false,
									corLinha: BarCorLinha.pink,
									corFundo: BarCorFundo.default_bg,
								},
							],
							info: `${v.mediaAcessoFaixa4} alunos`,
							nome: `Entre ${v.qtdAcessoGymPassInicioFaixa4} e ${v.qtdAcessoGymPassFinalFaixa4} acessos`,
							mostraPorcentagem: true,
							porcentagem:
								(v.mediaAcessoFaixa4 / v.totalAcessoComIntegradoresAcesso) *
								100,
						},
					],
				},
				{
					title: "Acessos por dia",
					chartItens: [
						{
							type: "line",
							series: [
								{
									name: "TotalAcessos",
									data: qtdAcessos,
								},
							],
							xaxis: {
								type: "category",
								categories: diasArray,
							},
							yaxis: {
								stepSize: 20,
								tickAmount: 5,
							},
						},
					],
				},
			],
		};
	}

	trackByIndex(index, item) {
		return index;
	}
}
