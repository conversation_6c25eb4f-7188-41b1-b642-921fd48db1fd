import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalClientesFreepassComponent } from "./bi-modal-clientes-freepass.component";

describe("BiModalClientesFreepassComponent", () => {
	let component: BiModalClientesFreepassComponent;
	let fixture: ComponentFixture<BiModalClientesFreepassComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalClientesFreepassComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalClientesFreepassComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
