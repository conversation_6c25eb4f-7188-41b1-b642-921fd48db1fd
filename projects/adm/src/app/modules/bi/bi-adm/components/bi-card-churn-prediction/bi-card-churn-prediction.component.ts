import { BiCardChurnPredictionFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-churn-prediction/bi-card-churn-prediction-filter/bi-card-churn-prediction-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import { BiMsApiGrupoRiscoService } from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import {
	GrupoRiscoClientesResponse,
	GrupoRiscoResponseModel,
} from "../../../../../../../../bi-ms-api/src/lib/models/grupo-risco/grupo-risco-response.model";

import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";

@Component({
	selector: "adm-bi-card-churn-prediction",
	templateUrl: "./bi-card-churn-prediction.component.html",
	styleUrls: ["./bi-card-churn-prediction.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardChurnPredictionComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	tabData;
	loading: boolean = false;
	grupoRiscoResponse: GrupoRiscoResponseModel;

	constructor(
		private biSidenavService: BiSidenavService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biMsApiGrupoRiscoService: BiMsApiGrupoRiscoService,
		private toastrService: ToastrService,
		protected biCommonService: BiCommonService,
		protected datePipe: DatePipe,
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init({
			dataInicioPrimeiroDiaMes: true,
		});
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		} else {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull: boolean) {
		this.loading = true;
		this.cd.detectChanges();
		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.biMsApiGrupoRiscoService
			.list({
				filtroGrupoRisco: {
					dataInicial: this.filtros.dataInicio.getTime(),
					dataFinal: this.filtros.data.getTime(),
					empresa: this.filtros.empresa,
					/**
					 * Esse mapeamento é devido ao bi-ms precisar da flag de seleção de pendência que era enviado pelo legado
					 * ao selecionar um colaborador, visto que é enviado todos os colaboradores.
					 */
					colaboradores: (this.filtros.colaboradores || []).map(
						(colaborador) => `${colaborador};true`
					),
					contar: true,
					clientesSemContato: false,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: GrupoRiscoResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	private _populateTabData(v?: GrupoRiscoResponseModel) {
		if (!v) {
			v = {
				qtdClientes: new Map<string, number>(),
				listaClientes: new Array<GrupoRiscoClientesResponse>(),
				percentualRisco: 0,
				percentualContatoRisco: 0,
				totalRisco: 0,
			};
		}

		this.grupoRiscoResponse = v;

		this.tabData = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.qtdClientes.get("peso6") || 0,
								size: "24px",
								isMonetary: false,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Peso 6",
							},
						},
						{
							info: {
								value: v.qtdClientes.get("peso7") || 0,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: false,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Peso 7",
							},
						},
						{
							info: {
								value: v.qtdClientes.get("peso8") || 0,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: false,
								isPercentage: false,
								state: "info",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								link: undefined,
								text: "Peso 8",
							},
						},
					],
				},
			],
		};
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Grupo de risco",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-grupo-de-risco-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideFilter() {
		const ref = this.biSidenavService.open(
			BiCardChurnPredictionFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	openModalRisco() {}

	trackByIndex(index, item) {
		return index;
	}
}
