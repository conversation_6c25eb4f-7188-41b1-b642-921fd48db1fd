<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				ds3Input
				dateType="dateranger"
				[controlStart]="form.controls['dataInicio']"
				[controlEnd]="form.controls['dataFim']"
				position="middle-screen"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Produtos</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="produtos"
				nameKey="descricao"
				valueKey="codigo"
				[useValueAsObject]="true"
				[options]="produtos"></ds3-select-multi>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Serviços</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="servicos"
				nameKey="descricao"
				valueKey="codigo"
				[useValueAsObject]="true"
				[options]="servicos"></ds3-select-multi>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Despesas</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="despesas"
				nameKey="nome"
				valueKey="codigo"
				[useValueAsObject]="true"
				[options]="despesas"></ds3-select-multi>
		</ds3-form-field>
	</form>
</adm-bi-side-filter-base-content>
