import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiCardIndiceRenovacaoComponent } from "./bi-card-indice-renovacao.component";
import { BiIndiceRenovacaoFilterComponentComponent } from "./bi-indice-renovacao-filter-component/bi-indice-renovacao-filter-component.component";

@NgModule({
	declarations: [
		BiCardIndiceRenovacaoComponent,
		BiIndiceRenovacaoFilterComponentComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardIndiceRenovacaoComponent],
	entryComponents: [BiIndiceRenovacaoFilterComponentComponent],
})
export class BiCardIndiceRenovacaoModule {}
