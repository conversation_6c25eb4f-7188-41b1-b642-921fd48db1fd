import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalContratosCanceladosComponent } from "./bi-modal-contratos-cancelados.component";

describe("BiModalContratosCanceladosComponent", () => {
	let component: BiModalContratosCanceladosComponent;
	let fixture: ComponentFixture<BiModalContratosCanceladosComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalContratosCanceladosComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalContratosCanceladosComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
