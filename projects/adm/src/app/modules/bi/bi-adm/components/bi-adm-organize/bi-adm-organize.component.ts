import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { MatDialogRef } from "@angular/material/dialog";
import {
	ApiResponseSingle,
	BiMsApiOrganizeService,
	ConfiguracaoSistemaUsuario,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { PermissaoService } from "pacto-layout";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { DragDropColumnData } from "ui-kit";

@Component({
	selector: "adm-bi-adm-organize",
	templateUrl: "./bi-adm-organize.component.html",
	styleUrls: ["./bi-adm-organize.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiAdmOrganizeComponent
	implements OnInit, AfterViewInit, On<PERSON><PERSON><PERSON>
{
	@ViewChild("biControleOperacoesExcecoesTmpl", { static: false })
	biControleOperacoesExcecoesTmpl: TemplateRef<any>;

	@ViewChild("biIntegradoresAcessoTmpl", { static: false })
	biIntegradoresAcessoTmpl: TemplateRef<any>;

	@ViewChild("biIndiceRenovacaoTmpl", { static: false })
	biIndiceRenovacaoTmpl: TemplateRef<any>;

	@ViewChild("biChurnPredictionTmpl", { static: false })
	biChurnPredictionTmpl: TemplateRef<any>;

	@ViewChild("biInadimplenciaTmpl", { static: false })
	biInadimplenciaTmpl: TemplateRef<any>;

	@ViewChild("biCicloVidaClienteTmpl", { static: false })
	biCicloVidaClienteTmpl: TemplateRef<any>;

	@ViewChild("biPendenciasClienteTmpl", { static: false })
	biPendenciasClienteTmpl: TemplateRef<any>;

	@ViewChild("biConversaoVendasTmpl", { static: false })
	biConversaoVendasTmpl: TemplateRef<any>;

	@ViewChild("biMetasFinanceirasVendasTmpl", { static: false })
	biMetasFinanceirasVendasTmpl: TemplateRef<any>;

	@ViewChild("biAulasExperimentaisTmpl", { static: false })
	biAulasExperimentaisTmpl: TemplateRef<any>;

	@ViewChild("biGestaoAcessoTmpl", { static: false })
	biGestaoAcessoTmpl: TemplateRef<any>;

	@ViewChild("biMovimentacaoContratosTmpl", { static: false })
	biMovimentacaoContratosTmpl: TemplateRef<any>;

	@ViewChild("biCobrancasConvenioTmpl", { static: false })
	biCobrancasConvenioTmpl: TemplateRef<any>;

	@ViewChild("biTicketMedioPlanosTmpl", { static: false })
	biTicketMedioPlanosTmpl: TemplateRef<any>;

	@ViewChild("biVerificacaoClientesTmpl", { static: false })
	biVerificacaoClientesTmpl: TemplateRef<any>;

	data = new Array<DragDropColumnData>(
		{
			column: 1,
			data: [],
		},
		{
			column: 2,
			data: [],
		}
	);
	dataRemoved = [];
	permissaoVerificacaoClientes: boolean;
	permissaoIndiceRenovacao: boolean;
	permissaoPendenciaClientes: boolean;
	permissaoChurnPrediction: boolean;
	permissaoMovimentacaoContratos: boolean;
	permissaoCicloVidaCliente: boolean;
	permissaoIntegradoresAcesso: boolean;
	permissaoConversaoVendas: boolean;
	permissaoControleOperacoesExcecao: boolean;
	permissaoInadimplencia: boolean;
	permissaoAulasExperimentais: boolean;
	permissaoTicketMedioPlano: boolean;
	permissaoGestaoAcesso: boolean;
	permissaoCobrancasConvenio: boolean;
	permissaoMetasFinanceirasVendas: boolean;
	loading: boolean;

	private allData: Array<{
		id: number;
		label: string;
		template: TemplateRef<any>;
		visible: boolean;
	}> = new Array<{
		id: number;
		label: string;
		template: TemplateRef<any>;
		visible: boolean;
	}>();
	private _destroyed$: Subject<void> = new Subject<void>();
	private configuracaoSistemaUsuario: ConfiguracaoSistemaUsuario;

	constructor(
		private cd: ChangeDetectorRef,
		private permissaoService: PermissaoService,
		private biOrganizeService: BiMsApiOrganizeService,
		private toastrService: ToastrService,
		private dialog: MatDialogRef<BiAdmOrganizeComponent>
	) {}

	ngOnInit() {
		this.loadPermissoes();
	}

	ngAfterViewInit() {
		this.buildAllData();
		this._loadConfigOrdem();
	}

	ngOnDestroy() {
		this._destroyed$.next();
	}

	private _loadConfigOrdem() {
		this.loading = true;
		this.cd.detectChanges();
		this.biOrganizeService
			.findByLoggedUser()
			.pipe(takeUntil(this._destroyed$))
			.subscribe({
				next: (response: ApiResponseSingle<ConfiguracaoSistemaUsuario>) => {
					this.configuracaoSistemaUsuario = response.content;
					/*
					 * NECESSÁRIO POR CONTA DO CÓDIGO DA BUILD
					 * GERA UM CÓDIGO QUE OCASIONA EM UM UNDEFINED
					 * */
					const valor =
						this.configuracaoSistemaUsuario &&
						this.configuracaoSistemaUsuario.valor
							? this.configuracaoSistemaUsuario.valor
							: "";
					const configSplited = valor.split(",");
					configSplited.forEach((cs) => {
						const valorSplited = cs.split("-");
						const idBi = Number(valorSplited[0]);
						const columnIndex = Number(valorSplited[1]);

						if (isNaN(idBi) || isNaN(columnIndex)) {
							return;
						}

						const data = this.allData.find((d) => d.id === idBi);
						if (data) {
							switch (columnIndex) {
								case 0:
								case 1:
									this.data[columnIndex].data.push(data);
									break;
								case 2:
									this.dataRemoved.push(data);
									break;
							}
						}
					});
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	loadPermissoes() {
		/**
		 * Verificação antiga, como histórico para caso seja a correta.
		 *
		 * No antigo a permissão que realmente vale é a 1.12, as outras não interferem
		 *
		 * 		this.permissaoVerificacaoClientes =
		 * 			this.permissaoService.temPermissaoAdm("1.12") ||
		 * 			this.permissaoService.temPermissaoAdm("1.13") ||
		 * 			this.permissaoService.temPermissaoAdm("6.36");
		 */
		this.permissaoVerificacaoClientes =
			this.permissaoService.temPermissaoAdm("1.12");
		this.permissaoChurnPrediction =
			this.permissaoService.temPermissaoAdm("6.14");
		this.permissaoPendenciaClientes =
			this.permissaoService.temPermissaoAdm("6.15");
		this.permissaoIndiceRenovacao =
			this.permissaoService.temPermissaoAdm("6.11");
		this.permissaoConversaoVendas =
			this.permissaoService.temPermissaoAdm("6.12");
		this.permissaoMovimentacaoContratos =
			this.permissaoService.temPermissaoAdm("6.13");
		// TODO Adicionar validação para usuário que tem meta MetaFinanceiroBIControle.getUsuarioTemMeta
		this.permissaoMetasFinanceirasVendas =
			this.permissaoService.temPermissaoAdm("9.16") ||
			this.permissaoService.temPermissaoAdm("9.24");
		this.permissaoCobrancasConvenio =
			this.permissaoService.temPermissaoAdm("6.35") &&
			this.permissaoService.temPermissaoAdm("6.16");
		this.permissaoTicketMedioPlano =
			this.permissaoService.temPermissaoAdm("6.21");
		this.permissaoAulasExperimentais =
			this.permissaoService.temPermissaoAdm("6.28");
		this.permissaoControleOperacoesExcecao =
			this.permissaoService.temPermissaoAdm("6.33");
		this.permissaoInadimplencia = this.permissaoService.temPermissaoAdm("6.30");
		this.permissaoGestaoAcesso = this.permissaoService.temPermissaoAdm("6.34");
		this.permissaoIntegradoresAcesso =
			this.permissaoService.temPermissaoAdm("9.86");
		this.permissaoCicloVidaCliente =
			this.permissaoService.temPermissaoAdm("9.87");
	}

	private buildAllData() {
		this.allData = [
			this._dataPendenciasCliente(),
			this._dataConversaoVendas(),
			this._dataCobrancasPorConvenio(),
			this._dataMetasFinanceirasVenda(),
			this._dataTicketMedioPlanos(),
			this._dataChurnPrediction(),
			this._dataIndiceRenovacao(),
			this._dataMovimentacaoContratos(),
			this._dataControleOperacoesExcecoes(),
			this._dataVerificacaoClientes(),
			this._dataAulasExperimentais(),
			this._dataGestaoAcesso(),
			this._dataInadimplencia(),
			this._dataCicloVidaCliente(),
			this._dataIntegradoresAcesso(),
		];
	}

	private _dataPendenciasCliente() {
		return {
			id: 0,
			label: "Pendências de clientes",
			template: this.biPendenciasClienteTmpl,
			visible: this.permissaoPendenciaClientes,
		};
	}

	private _dataConversaoVendas() {
		return {
			id: 1,
			label: "Conversão de vendas",
			template: this.biConversaoVendasTmpl,
			visible: this.permissaoConversaoVendas,
		};
	}

	private _dataMetasFinanceirasVenda() {
		return {
			id: 3,
			label: "Metas financeiras de vendas",
			template: this.biMetasFinanceirasVendasTmpl,
			visible: this.permissaoMetasFinanceirasVendas,
		};
	}

	private _dataTicketMedioPlanos() {
		return {
			id: 4,
			label: "Ticket médio de planos",
			template: this.biTicketMedioPlanosTmpl,
			visible: this.permissaoTicketMedioPlano,
		};
	}

	/**
	 * Back-end BiEnum.GRUPO_RISCO
	 */
	private _dataChurnPrediction() {
		return {
			id: 5,
			label: "Churn Prediction",
			template: this.biChurnPredictionTmpl,
			visible: this.permissaoChurnPrediction,
		};
	}

	private _dataIndiceRenovacao() {
		return {
			id: 6,
			label: "Índice de renovação",
			template: this.biIndiceRenovacaoTmpl,
			visible: this.permissaoIndiceRenovacao,
		};
	}

	private _dataMovimentacaoContratos() {
		return {
			id: 7,
			label: "Movimentação de contratos",
			template: this.biMovimentacaoContratosTmpl,
			visible: this.permissaoMovimentacaoContratos,
		};
	}

	private _dataCobrancasPorConvenio() {
		return {
			id: 8,
			label: "Cobranças por Convênio",
			template: this.biCobrancasConvenioTmpl,
			visible: this.permissaoCobrancasConvenio,
		};
	}

	private _dataControleOperacoesExcecoes() {
		return {
			id: 9,
			label: "Controle de operações de exceções",
			template: this.biControleOperacoesExcecoesTmpl,
			visible: this.permissaoControleOperacoesExcecao,
		};
	}

	private _dataVerificacaoClientes() {
		return {
			id: 11,
			label: "Verificação de clientes",
			template: this.biVerificacaoClientesTmpl,
			visible: this.permissaoVerificacaoClientes,
		};
	}

	private _dataAulasExperimentais() {
		return {
			id: 12,
			label: "Aulas experimentais",
			template: this.biAulasExperimentaisTmpl,
			visible: this.permissaoAulasExperimentais,
		};
	}

	private _dataGestaoAcesso() {
		return {
			id: 13,
			label: "Gestão de acessos",
			template: this.biGestaoAcessoTmpl,
			visible: this.permissaoGestaoAcesso,
		};
	}

	private _dataInadimplencia() {
		return {
			id: 14,
			label: "Inadimplência",
			template: this.biInadimplenciaTmpl,
			visible: this.permissaoInadimplencia,
		};
	}

	private _dataCicloVidaCliente() {
		return {
			id: 16,
			label: "Ciclo de vida do cliente",
			template: this.biCicloVidaClienteTmpl,
			visible: this.permissaoCicloVidaCliente,
		};
	}

	private _dataIntegradoresAcesso() {
		return {
			id: 17,
			label: "Integradores de acesso",
			template: this.biIntegradoresAcessoTmpl,
			visible: this.permissaoIntegradoresAcesso,
		};
	}

	save(backToDefault = false) {
		this._convertDataToString(backToDefault);
		this.biOrganizeService.save(this.configuracaoSistemaUsuario).subscribe({
			next: (response) => {
				this.toastrService.success("Organização do BI salva com sucesso");
				this.dialog.close();
			},
			error: (error) => {
				if (error && error.error.meta) {
					if (error.error.meta) {
						if (error.error.meta.message) {
							this.toastrService.error(error.error.meta.message);
						} else {
							this.toastrService.error("Ocorreu um erro desconhecido!");
						}
					}
				} else {
					this.toastrService.error("Ocorreu um erro desconhecido!");
				}
			},
		});
	}

	private _convertDataToString(backToDefault = false) {
		if (backToDefault) {
			return;
		}
		let valor = "";
		valor += this.data[0].data.map((m) => `${m.id}-0`).join(",");
		valor += ",";
		valor += this.data[1].data.map((m, index) => `${m.id}-1`).join(",");
		valor += ",";
		valor += this.dataRemoved.map((m, index) => `${m.id}-2`).join(",");
		this.configuracaoSistemaUsuario.valor = valor;
	}

	close() {
		this.dialog.close();
	}

	trackByIndex(index, item) {
		return index;
	}

	trackByItemId(index, item) {
		if (item) {
			return item.id;
		}
		return index;
	}

	backToDefault() {
		this.configuracaoSistemaUsuario.valor = "";
		this.save(true);
	}
}
