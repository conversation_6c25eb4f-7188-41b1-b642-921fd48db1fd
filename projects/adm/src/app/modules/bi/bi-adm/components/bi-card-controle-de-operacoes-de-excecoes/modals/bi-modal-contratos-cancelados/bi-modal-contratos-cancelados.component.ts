import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiContratoOperacaoService,
	ApiResponseList,
	ContratosCancelados,
	FiltroBiControleOperacao,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-contratos-cancelados",
	templateUrl: "./bi-modal-contratos-cancelados.component.html",
	styleUrls: [
		"./bi-modal-contratos-cancelados.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalContratosCanceladosComponent implements OnInit, OnDestroy {
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "nomeCliente",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "matriculaCliente",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "matricula",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "responsavelOperacao",
			visible: true,
			titulo: "Resp. Cancelamento",
			ordenavel: true,
			nome: "responsavelOperacao",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "codigoContrato",
			visible: true,
			titulo: "N° Contrato",
			ordenavel: true,
			nome: "codigoContrato",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "descricaoJustificativa",
			visible: true,
			titulo: "Justificativa",
			ordenavel: true,
			nome: "descricaoJustificativa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataOperacao",
			visible: true,
			titulo: "Dt. Cancelamento",
			ordenavel: true,
			nome: "dataOperacao",
			inputType: "date",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "codigoCliente",
			visible: true,
			titulo: "Código",
			ordenavel: true,
			nome: "codigoCliente",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"contrato-operacao/contratos-cancelados"
	);
	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalContratosCanceladosComponent>,
		private admCoreApiContratoOperacaoService: AdmCoreApiContratoOperacaoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiContratoOperacaoService
			.contratosCancelados({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<ContratosCancelados>) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					} else {
						this.toastrService.error("Ocorreu um erro desconhecido!");
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: ContratosCancelados) {
		this.biCommonService.openCliente(item.matriculaCliente);
	}
}
