<div class="bi-card aulas-experimentais">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span
				class="bi-card-header-label-title typography-title-4 text-color-typography-default-title">
				Aulas experimentais
			</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>

	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.dataInicio | date : "shortDate" }} -
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
		</div>

		<ds3-diviser></ds3-diviser>

		<ng-container *ngIf="!loading">
			<div class="bi-card-content-section">
				<div
					class="bi-card-content-section-content"
					*ngFor="let section of tabData?.sections; trackBy: trackByIndex">
					<ds3-bi-info
						[infoData]="section?.infoItens"
						(clickEvent)="openModalInfo($event)"></ds3-bi-info>
				</div>
			</div>
			<ds3-diviser class="mt-2"></ds3-diviser>
			<div class="bi-card-content-section" *ngIf="tabData">
				<div *ngFor="let section of tabData?.sections; trackBy: trackByIndex">
					<div
						class="bi-card-content-section-content"
						*ngIf="section?.listItens && section?.listItens.length > 0">
						<div class="bi-card-content-section-content-list">
							<ng-container
								*ngFor="
									let listItem of section?.listItens;
									let odd = odd;
									trackBy: trackByIndex
								">
								<div
									[ngClass]="[
										'bi-card-content-section-content-list-item',
										odd ? 'list-item-even' : 'list-item-odd'
									]">
									<div
										class="bi-card-content-section-content-list-item-text"
										*ngIf="listItem?.text">
										{{ listItem.text }}
									</div>
									<button
										ds3-text-button
										class="bi-card-content-section-content-list-item-value"
										*ngIf="listItem?.value >= 0">
										{{ listItem.value }}
									</button>
									<div
										class="bi-card-content-section-content-list-item-percentage"
										*ngIf="listItem?.percentage >= 0 && !listItem?.clickable">
										{{ listItem.percentage }}%
									</div>
									<button
										ds3-text-button
										class="bi-card-content-section-content-list-item-percentage"
										*ngIf="listItem?.percentage >= 0 && listItem?.clickable">
										{{ listItem.percentage }}%
									</button>
								</div>
							</ng-container>
						</div>
					</div>
				</div>
			</div>
		</ng-container>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>
