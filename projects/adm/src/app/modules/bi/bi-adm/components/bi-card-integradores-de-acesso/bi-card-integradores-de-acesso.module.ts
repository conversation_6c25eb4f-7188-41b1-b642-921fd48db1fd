import { BiCardIntegradoresDeAcessoComponent } from "@adm/modules/bi/bi-adm/components/bi-card-integradores-de-acesso/bi-card-integradores-de-acesso.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiCardIntegradoresDeAcessoFilterComponent } from "./bi-card-integradores-de-acesso-filter/bi-card-integradores-de-acesso-filter.component";

@NgModule({
	declarations: [
		BiCardIntegradoresDeAcessoComponent,
		BiCardIntegradoresDeAcessoFilterComponent,
	],
	imports: [CommonModule, BiSharedModule, ReactiveFormsModule],
	exports: [BiCardIntegradoresDeAcessoComponent],
	entryComponents: [BiCardIntegradoresDeAcessoFilterComponent],
})
export class BiCardIntegradoresDeAcessoModule {}
