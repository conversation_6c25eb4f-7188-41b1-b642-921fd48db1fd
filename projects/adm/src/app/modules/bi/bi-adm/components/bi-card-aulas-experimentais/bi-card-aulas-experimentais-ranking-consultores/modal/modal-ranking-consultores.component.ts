import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { PactoDataGridConfig } from "ui-kit";

@Component({
	selector: "adm-modal-ranking-consultores",
	templateUrl: "./modal-ranking-consultores.component.html",
	styleUrls: ["./modal-ranking-consultores.component.scss"],
})
export class ModalRankingConsultoresComponent implements OnInit {
	@ViewChild("celulaConsultor", { static: false })
	celularConsultor: TemplateRef<any>;
	@ViewChild("totalAulasexperimentais", { static: true })
	totalAulasexperimentais;
	@ViewChild("convertidos", { static: true }) convertidos;
	@ViewChild("naoConvertidos", { static: true }) naoConvertidos;
	@ViewChild("conversaoAulasExperimentais", { static: true })
	conversaoAulasExperimentais;
	@ViewChild("tableData", { static: true }) tableData;

	table: PactoDataGridConfig;

	constructor(
		private cd: ChangeDetectorRef,
		private domSanitizer: DomSanitizer
	) {}

	ngOnInit() {}

	ngAfterViewInit(): void {
		this.initTable();
	}

	data = {
		content: [
			{
				consultor: {
					codigo: 1,
					nome: "Consulor 01",
					fotoUrl: this.domSanitizer.bypassSecurityTrustUrl(
						`pacto-ui/images/user-image-default.svg`
					),
				},
				totalAulasexperimentais: 0,
				convertidos: 0,
				naoConvertidos: 0,
				conversao: 0,
			},
			{
				consultor: {
					codigo: 2,
					nome: "Consulor 02",
					fotoUrl: this.domSanitizer.bypassSecurityTrustUrl(
						`pacto-ui/images/user-image-default.svg`
					),
				},
				totalAulasexperimentais: 0,
				convertidos: 5.356,
				naoConvertidos: 0,
				conversao: 0,
			},
			{
				consultor: {
					codigo: 3,
					nome: "Consulor 03",
					fotoUrl: this.domSanitizer.bypassSecurityTrustUrl(
						`pacto-ui/images/user-image-default.svg`
					),
				},
				totalAulasexperimentais: 0,
				convertidos: 0,
				naoConvertidos: 0,
				conversao: 0,
			},
		],
		first: true,
		last: false,
		number: 0,
		size: 10,
		totalElements: 0,
		totalPages: 10,
	};

	private initTable() {
		this.table = new PactoDataGridConfig({
			quickSearch: true,
			pagination: true,
			dataAdapterFn: (serverData) => {
				return this.data;
			},
			columns: [
				{
					titulo: "Consultor",
					nome: "consultor",
					visible: true,
					ordenavel: false,
					celula: this.celularConsultor,
					styleClass: "center",
				},
				{
					titulo: "Total de aulas experimentais",
					nome: "totalAulasexperimentais",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					styleClass: "center",
				},
				{
					titulo: "Convertidos",
					nome: "convertidos",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					styleClass: "center",
				},
				{
					titulo: "Nao Convertidas",
					nome: "naoConvertidos",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					styleClass: "center",
				},
				{
					titulo: "Conversão de aulas experimentais",
					nome: "conversao",
					visible: true,
					ordenavel: false,
					defaultVisible: true,
					styleClass: "center",
				},
			],
		});
		this.cd.detectChanges();
	}
}
