<adm-bi-modal-content
	[modalTitle]="modalTitle"
	[shareUrl]="shareUrl"
	[filters]="filters"
	[page]="page"
	[pageSize]="size"
	[totalItems]="totalItems"
	[colunasShare]="colunasShare"
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)"
	[withPaginatorDiviser]="true">
	<ds3-table>
		<table ds3DataTable [stateManager]="tableState">
			<ng-container ds3TableColumn="descricao">
				<th *ds3TableHeaderCell>Mensagem</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					{{ item?.descricao }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				ds3-icon-button
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				(click)="triggerSortToggle()">
				<i
					class="pct"
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</ds3-table>
</adm-bi-modal-content>
