import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalConsultoresContratosAlteradosComponent } from "./bi-modal-consultores-contratos-alterados.component";

describe("BiModalConsultoresContratosAlteradosComponent", () => {
	let component: BiModalConsultoresContratosAlteradosComponent;
	let fixture: ComponentFixture<BiModalConsultoresContratosAlteradosComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalConsultoresContratosAlteradosComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalConsultoresContratosAlteradosComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
