import { BiCardConversaoVendasComponent } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-card-conversao-vendas.component";
import { BiCardIcvBvInfoComponent } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-card-icv-bv-info/bi-card-icv-bv-info.component";
import { BiCardIcvClientesConvertidosComponent } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-card-icv-clientes-convertidos/bi-card-icv-clientes-convertidos.component";
import { BiCardIcvLeadsComponent } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-card-icv-leads/bi-card-icv-leads.component";
import { BiCardIcvRankingConsultoresComponent } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-card-icv-ranking-consultores/bi-card-icv-ranking-consultores.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiConversaoVendasFilterComponent } from "./bi-conversao-vendas-filter/bi-conversao-vendas-filter.component";

@NgModule({
	declarations: [
		BiCardConversaoVendasComponent,
		BiCardIcvBvInfoComponent,
		BiCardIcvClientesConvertidosComponent,
		BiCardIcvLeadsComponent,
		BiCardIcvRankingConsultoresComponent,
		BiConversaoVendasFilterComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardConversaoVendasComponent],
	entryComponents: [BiConversaoVendasFilterComponent],
})
export class BiCardConversaoVendasModule {}
