<adm-bi-modal-content
	[modalTitle]="modalTitle"
	[shareUrl]="shareUrl"
	[filters]="filters"
	[page]="page"
	[pageSize]="size"
	[totalItems]="totalItems"
	[colunasShare]="colunasShare"
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)">
	<ds3-table>
		<table ds3DataTable [stateManager]="tableState">
			<ng-container ds3TableColumn="pessoa">
				<th *ds3TableHeaderCell>Nome</th>
				<td class="nomeEntidade" *ds3TableCell="let item">
					<button ds3-text-button (click)="openCliente(item)">
						{{ item?.cliente?.pessoa?.nome | titlecase }}
					</button>
				</td>
			</ng-container>

			<ng-container ds3TableColumn="matricula">
				<th *ds3TableHeaderCell class="align-center">Matrícula</th>
				<td class="matricula align-center" *ds3TableCell="let item">
					{{ item?.cliente?.matricula }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="situacao">
				<th *ds3TableHeaderCell class="align-center">Situação</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					<ds3-status [color]="item?.corSituacaoCliente">
						{{ item?.cliente?.situacao }}
					</ds3-status>
					<ds3-status
						[color]="item?.corSituacaoContrato"
						*ngIf="item?.cliente?.situacao !== 'VI'">
						{{ item?.situacaoContrato }}
					</ds3-status>
				</td>
			</ng-container>

			<ng-container ds3TableColumn="dataLancamento">
				<th *ds3TableHeaderCell class="align-center">Dt. Lancamento</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.dataLancamento | date : "dd/MM/yyyy" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="responsavel">
				<th *ds3TableHeaderCell class="align-center">Resp. Lançamento</th>
				<td class="bi-modal-situacao align-center" *ds3TableCell="let item">
					{{ item?.responsavelContrato?.nome | titlecase }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				ds3-icon-button
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				(click)="triggerSortToggle()">
				<i
					class="pct"
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</ds3-table>
</adm-bi-modal-content>
