<header>
	<span>Relatório de indicador de acesso</span>
	<i class="pct pct-x cursor-pointer" id="btn-close" (click)="closeModal()"></i>
</header>

<ds3-diviser></ds3-diviser>

<main>
	<div class="main-header">
		<ds3-shortcut-tabs
			#dataRangeTab
			[fullTab]="false"
			(click)="consultar(dataRangeTab)">
			<ds3-shortcut-tab
				*ngFor="let tab of listTabs; trackBy: trackByIndex"
				[tabTitle]="tab.label">
				<div class="col-12">
					<ds3-form-field *ngIf="tab.value === 2" class="row-reverse">
						<ds3-input-date
							dateType="datepicker"
							[control]="formGroup.get('data')"
							ds3Input></ds3-input-date>
					</ds3-form-field>
				</div>

				<ds3-chart-column-stacked
					[series]="getChartOptions(tab.value).series"
					[disableAnimations]="true"
					[xaxis]="getChartOptions(tab.value).xaxis"
					[colorOrder]="colorOrder"></ds3-chart-column-stacked>
			</ds3-shortcut-tab>
		</ds3-shortcut-tabs>
	</div>
</main>
