<div class="bi-card verificacao-clientes">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">Verificação de clientes</span>
			<a
				(click)="abrirAjuda()"
				class="bi-card-header-label-button"
				ds3-icon-button
				size="sm">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				ds3-outlined-button
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div
			*ngIf="tabDataVerificacaoClientes && !loading"
			class="bi-card-content-section">
			<div
				*ngFor="let section of tabDataVerificacaoClientes.sections"
				class="bi-card-content-section-content bi-card-content-section-content-65-35">
				<div class="bi-card-content-section-content-list">
					<ng-container
						*ngFor="
							let listItem of section?.listItens;
							let odd = odd;
							trackBy: trackByIndex
						">
						<div
							(click)="handleAbrirRelatorio(listItem.id)"
							[ngClass]="[
								'bi-card-content-section-content-list-item',
								odd ? 'list-item-even' : 'list-item-odd'
							]">
							<div
								*ngIf="listItem?.text"
								class="bi-card-content-section-content-list-item-text">
								{{ listItem.text }}
							</div>
							<button
								(click)="openModalItem(listItem)"
								*ngIf="listItem?.value >= 0"
								class="bi-card-content-section-content-list-item-value"
								ds3-text-button>
								{{ listItem.value }}
							</button>
							<div class="bi-card-content-section-content-list-item-date">
								<span *ngIf="listItem?.date" [ds3Tooltip]="listItem?.dateHint">
									{{ listItem.date | date : "dd/MM/yy" }}
								</span>
								<span *ngIf="!listItem?.date">-</span>
							</div>
						</div>
					</ng-container>
				</div>
				<ds3-bi-info
					[infoData]="section?.infoItens"
					class="bi-card-content-section-content-infos"></ds3-bi-info>
			</div>
			<div class="bi-card-content-section-content">
				<ds3-chart-bar
					[barHeight]="100"
					[colorOrder]="[
						'green',
						'red',
						'yellow',
						'orange',
						'blue',
						'pink',
						'purple',
						'lightblue'
					]"
					[disableAnimations]="true"
					[events]="optionsChartVerificacaoClientes.events"
					[height]="150"
					[series]="optionsChartVerificacaoClientes.series"
					[tooltip]="optionsChartVerificacaoClientes.tooltip"
					[xaxis]="optionsChartVerificacaoClientes.xaxis"
					[yaxis]="optionsChartVerificacaoClientes.yaxis"></ds3-chart-bar>
			</div>
		</div>

		<div *ngIf="loading" class="bi-card-content-loading">
			<img
				alt="Loading pacto"
				src="pacto-ui/images/gif/loading-pacto.gif"
				width="80" />
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@adm:bi-verificacao-clientes:indice-verficicaao"
		xingling="bi-verificacao-clientes:indice-verificacao">
		Índice de verificação
	</span>
	<span
		i18n="@@adm:bi-verificacao-clientes:clientes-verificacao"
		xingling="bi-verificacao-clientes:clientes-verificacao">
		Total
	</span>
	<span
		i18n="@@adm:bi-verificacao-clientes:clientes-ja-verificados"
		xingling="bi-verificacao-clientes:clientes-ja-verificados">
		Verificados
	</span>
	<span
		i18n="@@adm:bi-verificacao-clientes:clientes-nao-verificados"
		xingling="bi-verificacao-clientes:clientes-nao-verificados">
		Não checados
	</span>
	<span
		i18n="@@adm:bi-verificacao-clientes:verificados"
		xingling="bi-verificacao-clientes:verificados">
		Verificados
	</span>
	<span
		i18n="@@adm:bi-verificacao-clientes:nao-verificados"
		xingling="bi-verificacao-clientes:nao-verificados">
		Não verificados
	</span>
</pacto-traducoes-xingling>
