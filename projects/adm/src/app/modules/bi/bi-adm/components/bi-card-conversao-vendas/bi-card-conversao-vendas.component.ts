import { BiConversaoVendasFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-conversao-vendas-filter/bi-conversao-vendas-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	HostBinding,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import {
	BiMsApiConversaoVendaService,
	ConversaoVendaResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { PactoDataTableStateManager } from "../../../../../../../../ui/src/lib/ds3/ds3-table/directives/ds3-data-table-state-manager";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";

@Component({
	selector: "adm-bi-card-conversao-vendas",
	templateUrl: "./bi-card-conversao-vendas.component.html",
	styleUrls: ["./bi-card-conversao-vendas.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardConversaoVendasComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	@HostBinding("class.adm-bi-card-conversao-vendas")
	enableEncapsulation = true;

	loading: boolean = false;
	tipoData: number = 1;
	formatoData: string = "dd 'de' MMMM 'de' yyyy";
	conversaoVendaResponseModel: ConversaoVendaResponseModel;
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();

	public tiposLista: any[] = [
		{ value: 1, label: "Hoje" },
		{ value: 2, label: "Até hoje" },
	];

	optionsChartVerificacaoClientes = {
		series: [],
		tooltip: {
			enabledOnSeries: true,
			onDatasetHover: {
				highlightDataSeries: true,
			},
			x: {
				show: false,
			},
		},
		yaxis: {
			show: false,
			labels: {
				show: false,
			},
			axisTicks: {
				show: false,
			},
			axisBorder: {
				show: false,
			},
		},
		xaxis: {} as ApexXAxis,
		events: {},
	};

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected datePipe: DatePipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biMsApiConversaoVendaService: BiMsApiConversaoVendaService,
		private biMdlAjudaService: BiMdlAjudaService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this._initFilters();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	private _initFilters() {
		this.filtros.tipoBV = [1, 2, 3];
		this.filtros.considerarPlanoBolsa = true;
		this.filtros.desconsiderarGympass = true;
		this.filtros.listaOrigemSistema = "1,17,9";
		this.filtros.origemSistema = ["1,17", "9"];
		this.filtros.tipoContrato = [1, 2];
		if (this.globalFilterForm && this.globalFilterForm.value) {
			this.filtros.colaboradores = this.globalFilterForm.value.colaboradoresObj;
		}
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Conversão de vendas",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-conversao-de-vendas-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(BiConversaoVendasFilterComponent, {
			globalFilter: this.globalFilterForm.value,
			filters: this.filtros,
		});

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		this.loading = true;
		this.tableState.patchState({ loading: true });
		this.cd.detectChanges();

		let colaboradores = [];
		if (this.filtros.colaboradores) {
			colaboradores = this.filtros.colaboradores.map((c) =>
				typeof c === "object" ? c.value : c
			);
		}

		let tipoBV = [];
		if (this.filtros.tipoBV) {
			tipoBV = this.filtros.tipoBV;
		}

		let considerarPlanoBolsa = false;
		if (this.filtros.considerarPlanoBolsa) {
			considerarPlanoBolsa = this.filtros.considerarPlanoBolsa;
		}

		let desconsiderarGympass = false;
		if (this.filtros.desconsiderarGympass) {
			desconsiderarGympass = this.filtros.desconsiderarGympass;
		}

		let listaOrigemSistema = "";
		if (this.filtros.listaOrigemSistema) {
			listaOrigemSistema = this.filtros.listaOrigemSistema;
		}

		let listaEvento = "";
		if (this.filtros.eventos) {
			listaEvento = this.filtros.eventos.map((t) => t.codigo).join(",");
		}

		let tipoContrato = [];
		if (this.filtros.tipoContrato) {
			tipoContrato = this.filtros.tipoContrato;
		}

		this.biMsApiConversaoVendaService
			.list({
				filtroConversaoVenda: {
					empresas: [this.filtros.empresa],
					data: this.datePipe.transform(this.filtros.data, "yyyy-MM-dd"),
					colaboradores,
					tipoBV,
					considerarPlanoBolsa,
					desconsiderarGympass,
					listaOrigemSistema,
					listaEvento,
					tipoContrato,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: ConversaoVendaResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	_populateTabData(v?: ConversaoVendaResponseModel) {
		if (!v) {
			v = {
				boletinVisitaDia: 0,
				codigosBoletinVisitaDia: [],
				boletinVisitaMes: 0,
				codigosBoletinVisitaMes: [],
				matriculasDia: 0,
				mapaMatriculasDias: new Map<number, number>(),
				matriculasMes: 0,
				mapaMatriculasMes: new Map<number, number>(),
				reMatriculasDia: 0,
				mapaRematriculasDias: new Map<number, number>(),
				reMatriculasMes: 0,
				mapaRematriculasMes: new Map<number, number>(),
				indiceConversaoVendaMes: 0,
				totalContratosMes: 0,
				receptivosMes: 0,
				indicacoesMes: 0,
				leadsMes: 0,
				receptivosDia: 0,
				indicacoesDia: 0,
				leadsDia: 0,
				indiceConversaoVendaDia: 0,
				faturamentoDia: 0,
				faturamentoMes: 0,
				leadsConvertidosDia: 0,
				leadsConvertidosMes: 0,
				leadsNaoConvertidosDia: 0,
				leadsNaoConvertidosMes: 0,
			};
		}
		this.conversaoVendaResponseModel = v;
		this.tableState.patchState({
			data: [
				{
					type: 1,
					label: "BV",
					valueDia: this.conversaoVendaResponseModel.boletinVisitaDia || 0,
					valueMes: this.conversaoVendaResponseModel.boletinVisitaMes || 0,
				},
				{
					type: 2,
					label: "Matrículas",
					valueDia: this.conversaoVendaResponseModel.matriculasDia || 0,
					valueMes: this.conversaoVendaResponseModel.matriculasMes || 0,
				},
				{
					type: 3,
					label: "Rematrículas",
					valueDia: this.conversaoVendaResponseModel.reMatriculasDia || 0,
					valueMes: this.conversaoVendaResponseModel.reMatriculasMes || 0,
				},
			],
			loading: false,
		});

		const maxChartValue = Math.max(
			v.matriculasMes,
			v.reMatriculasMes,
			v.boletinVisitaMes
		);

		this.optionsChartVerificacaoClientes.series = [
			{
				name: "Matrículas",
				data: [v.matriculasMes],
			},
			{
				name: "Rematrículas",
				data: [v.reMatriculasMes],
			},
			{
				name: "Só Visitou",
				data: [v.boletinVisitaMes - (v.matriculasMes + v.reMatriculasMes)],
			},
		];
	}

	consultar(dataRangeTab) {
		this.tipoData = this.tiposLista.find((tipo) => tipo.label === "Hoje").value;
		if (dataRangeTab) {
			const tipoAtivo = dataRangeTab.tabs.find((tab) => tab.active);
			const tipoName = tipoAtivo.tabTitle;

			this.tipoData = this.tiposLista.find(
				(tipo) => tipo.label === tipoName
			).value;
		}
		if (this.tipoData === 1) {
			this.formatoData = "dd 'de' MMMM 'de' yyyy";
		} else {
			this.formatoData = "MMMM 'de' yyyy";
		}
		this.cd.detectChanges();
	}

	trackByIndex(index, item) {
		return index;
	}

	afterReloadAllBIs() {
		if (this.globalFilterForm && this.globalFilterForm.value) {
			this.filtros.colaboradores = this.globalFilterForm.value.colaboradoresObj;
		}
	}
}
