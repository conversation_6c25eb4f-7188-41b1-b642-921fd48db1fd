<ds3-table>
	<table ds3DataTable [stateManager]="tableState">
		<ng-container ds3TableColumn="consultor">
			<th *ds3TableHeaderCell>Consultor</th>
			<td *ds3TableCell="let consultor">
				<div class="icv-rc-celula-consultor">
					<img
						*ngIf="consultor?.ordem === 1"
						height="25"
						src="pacto-ui/images/medal-gold.png"
						width="17" />
					<img
						*ngIf="consultor?.ordem === 2"
						height="25"
						src="pacto-ui/images/medal-silver.png"
						width="17" />
					<img
						*ngIf="consultor?.ordem === 3"
						height="25"
						src="pacto-ui/images/medal-bronze.png"
						width="17" />
					<ds3-avatar [src]="consultor?.fotoUrl" size="24"></ds3-avatar>
					<span>{{ consultor?.nomeResponsavel }}</span>
				</div>
			</td>
		</ng-container>

		<ng-container ds3TableColumn="valor">
			<th *ds3TableHeaderCell class="align-center">Valor</th>
			<td class="align-center" *ds3TableCell="let item">
				{{ item?.valor | currency : "BRL" }}
			</td>
		</ng-container>

		<ng-container ds3TableColumn="icv">
			<th *ds3TableHeaderCell class="align-center">Icv</th>
			<td *ds3TableCell="let item">{{ item.icv || 0 | number : "1.2-2" }}%</td>
		</ng-container>

		<tr *ds3TableRow></tr>

		<tr *ds3TableEmptyRow class="ds3-table-empty">
			<td>
				<h2>Nenhum consultor encontrado</h2>
				<p>
					Nenhum consultor encontrado no período, tente realizar uma nova busca.
				</p>
			</td>
		</tr>

		<tbody *ds3TableLoading>
			<tr>
				<td>
					<div class="bi-modal-table-loader" role="status">
						<img
							alt="Loading pacto"
							src="pacto-ui/images/gif/loading-pacto.gif" />
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</ds3-table>

<div *ngIf="listaConsultores.length > qtdConsultores" class="icv-rc-btn-row">
	<!--<button
		*ngIf="listaConsultoresVisible.length > qtdConsultores"
		ds3-text-button
		color="secondary"
		(click)="resetVisibleConsultores()"
	>Resetar</button>
	<button
		*ngIf="listaConsultoresVisible.length > qtdConsultores"
		ds3-text-button
		color="secondary"
		(click)="showLessConsultores()"
	>Ver menos</button>-->
	<button
		*ngIf="listaConsultoresVisible.length < listaConsultores.length"
		ds3-text-button
		(click)="showMoreConsultores()">
		Ver mais
	</button>
</div>
