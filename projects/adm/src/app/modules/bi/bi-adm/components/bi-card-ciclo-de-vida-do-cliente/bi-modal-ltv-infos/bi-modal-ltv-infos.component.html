<div class="bi-ltv-modal-title">
	<div class="bi-ltv-modal-title-content">
		<h2 class="pct-title4">
			{{ modalTitle }}
		</h2>
		<button ds3-icon-button (click)="dialog.close()">
			<i class="pct pct-x"></i>
		</button>
	</div>
	<ds3-diviser></ds3-diviser>
</div>

<div class="bi-ltv-modal-content">
	<div class="pct-body1-regular">
		<ng-container
			*ngTemplateOutlet="considerandoLtvRealizadoERenovacaoLT"></ng-container>
	</div>
</div>

<ng-template #considerandoLtvRealizadoERenovacaoLT>
	<ng-container [ngSwitch]="data?.type">
		<ng-container *ngSwitchCase="'lt'">
			<ng-container
				*ngIf="
					data?.filters?.considerandoLtvRealizado &&
					data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LT indica o tempo médio que os clientes utilizaram dos seus ciclos
					de contrato com a empresa. Nesse cálculo, são considerados apenas os
					períodos já usufruídos. Por exemplo, se um cliente renovou um contrato
					de 12 meses, mas apenas 3 meses foram utilizados até o momento, os 9
					meses restantes são desconsiderados.
				</p>
				<p>
					Um "ciclo do contrato" refere-se ao período contínuo em que o cliente
					permanece ativo com a empresa. Se um cliente adquiriu um plano anual
					há 4 anos e tem renovado sem interrupções, essa sequência de
					renovações é considerada como um único ciclo do contrato. Porém, novas
					matrículas ou rematrículas são tratadas como novos ciclos de contrato.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					!data?.filters?.considerandoLtvRealizado &&
					!data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LT reflete o tempo médio dos contratos do cliente com a empresa. O
					cálculo considera a duração total de todos os contratos, sem
					diferenciar o período já utilizado do restante.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					!data?.filters?.considerandoLtvRealizado &&
					data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LT reflete o tempo médio que os clientes mantêm um ciclo do contrato
					ativo com a empresa. Um ciclo do contrato é o período ininterrupto de
					atividade do cliente. Se um cliente adquiriu um plano anual há 4 anos
					e renovou continuamente, esse período é considerado um único ciclo do
					contrato. No entanto, novas matrículas ou rematrículas são tratadas
					como ciclos separados.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					data?.filters?.considerandoLtvRealizado &&
					!data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LT reflete o tempo médio que os clientes já utilizaram dos seus
					contratos com a empresa. Por exemplo, se um cliente firmou um contrato
					de 12 meses e já utilizou 8 meses, o cálculo considera apenas esses 8
					meses, desconsiderando o tempo restante.
				</p>
			</ng-container>
		</ng-container>

		<ng-container *ngSwitchCase="'cac'">
			<ng-container
				*ngIf="
					data?.filters?.considerandoLtvRealizado &&
					data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O CAC representa o valor que a empresa investe para conquistar novos
					clientes. O cálculo é feito somando todas as despesas relacionadas aos
					planos de contas que afetam o CAC e dividindo pelo número total de
					novos contratos firmados no período.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					!data?.filters?.considerandoLtvRealizado &&
					!data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O CAC representa o valor investido pela empresa para conquistar novos
					clientes. Para calculá-lo, o sistema soma as despesas relacionadas aos
					planos de contas que impactam o CAC e divide pelo número total de
					novos contratos firmados no período.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					!data?.filters?.considerandoLtvRealizado &&
					data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O CAC representa o valor investido pela empresa para atrair novos
					clientes. Para calculá-lo, o sistema soma as despesas associadas aos
					planos de contas que afetam o CAC e divide pelo número de novos
					contratos firmados no período.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					data?.filters?.considerandoLtvRealizado &&
					!data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O CAC reflete o valor investido pela empresa para atrair novos
					clientes. O cálculo é feito somando as despesas associadas aos planos
					de contas que impactam o CAC e dividindo pelo número de novos
					contratos no período.
				</p>
			</ng-container>
		</ng-container>

		<ng-container *ngSwitchCase="'ltv'">
			<ng-container
				*ngIf="
					data?.filters?.considerandoLtvRealizado &&
					data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LTV reflete a receita média gerada durante o período utilizado de
					cada ciclo do contrato. O cálculo é feito dividindo-se a receita total
					de todos os ciclos de contrato pela duração efetivamente utilizada de
					cada um. Isso representa a média de receita gerada até o momento por
					cada ciclo do contrato.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					!data?.filters?.considerandoLtvRealizado &&
					!data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LTV reflete a receita potencial média gerada por cada contrato. Ele
					é calculado dividindo o valor total dos contratos pela duração
					completa de cada um. Isso indica quanto, em média, cada contrato pode
					gerar de receita para a empresa.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					!data?.filters?.considerandoLtvRealizado &&
					data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LTV reflete a receita média gerada por cada ciclo do contrato. O
					cálculo é feito dividindo o valor total dos ciclos de contrato pela
					duração completa de cada um. Isso oferece uma previsão de quanto cada
					ciclo do contrato pode gerar de receita.
				</p>
			</ng-container>

			<ng-container
				*ngIf="
					data?.filters?.considerandoLtvRealizado &&
					!data?.filters?.considerandoRenovacaoCalculoLt
				">
				<p>
					O LTV reflete a receita média gerada durante o período já utilizado de
					cada contrato. O cálculo é feito dividindo-se a receita total gerada
					pelos contratos pela duração já utilizada. Isso representa a média de
					quanto cada contrato gerou de receita até o momento.
				</p>
			</ng-container>
		</ng-container>

		<ng-container *ngSwitchCase="'churn'">
			<p>
				É quantidade de contratos cancelados e finalizados (data final do
				contrato) dentro do período pesquisado (Exceto os renovados), dividida
				pelos contratos ativos no início do mês + as matrículas, rematrículas e
				contratos transferidos do período. Os níveis do Churn Rate estão em:
			</p>
			<p>
				<strong class="text-color-support-green-4">ATÉ 3% = NORMAL</strong>
				|
				<strong class="text-color-support-orange-4">ATÉ 8% = PERIGO</strong>
				|
				<strong class="text-color-support-red-4">
					ACIMA DE 8% = ALERTA MÁXIMO
				</strong>
				.
			</p>
		</ng-container>
	</ng-container>
</ng-template>
