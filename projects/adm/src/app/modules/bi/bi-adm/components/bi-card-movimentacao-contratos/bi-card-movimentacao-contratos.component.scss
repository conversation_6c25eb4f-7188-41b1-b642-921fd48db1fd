@import "../../../bi-shared/bi-shared.scss";
@import "dist/ui-kit/assets/ds3/colors";

.bi-card.movimentacao-contratos {
	.bi-card-content-section-content-list {
		padding: 0;
	}
	.bi-card-content-section-content-list-table-title {
		margin-bottom: 0;
	}
}
.bi-card-content-section-content-list-head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8px;

	.bi-card-content-section-content-list-title {
		margin-bottom: 0;
	}
}
.bi-card-content-section-content-list-title {
	margin-bottom: 16px;
}

.bi-card-content-section-content-list-buttons {
	padding: 4px 6px;
	border-radius: 8px;

	button {
		&.selected {
			@extend .cor-action-default-able05;
			@extend .bg-action-default-able02;
		}
	}
}
.bi-card-content-section-content-list-body {
	display: grid;
	gap: 8px;
	grid-template-columns: 1fr 1fr;
	margin-bottom: 8px;
}
.bi-card-content-section-content-list-table {
	border: 1px solid var(--color-support-gray-1);
	border-radius: 8px;
	overflow: hidden;
}
.bi-card-content-section-content-list-table-exits
	.bi-card-content-section-content-list-item-value {
	@extend .cor-feedback-loss02;
}
.bi-card-content-section-content-list-table-title {
	padding: 8px 12px;
}
