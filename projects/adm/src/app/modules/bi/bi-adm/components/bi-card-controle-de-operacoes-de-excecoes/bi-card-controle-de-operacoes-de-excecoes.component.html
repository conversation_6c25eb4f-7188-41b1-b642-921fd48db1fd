<div class="bi-card controle-de-operacoes-de-excecoes">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">
				Controle de operações de exceções
			</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.dataInicio | date : "shortDate" }} -
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGruposColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupos de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGruposColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradoresObj;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradoresObj;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>
		</div>
		<div class="bi-card-content-section" *ngIf="tabData && !loading">
			<div
				class="bi-card-content-section-content"
				*ngFor="let section of tabData.sections; trackBy: trackByIndex">
				<div class="bi-card-content-section-content-list">
					<ng-container
						*ngFor="
							let listItem of section?.listItens;
							let index = index;
							let odd = odd;
							trackBy: trackByIndex
						">
						<div
							*ngIf="listItem?.value > 0 || showHiddenFields"
							[ngClass]="[
								'bi-card-content-section-content-list-item',
								'hoverable'
							]"
							[ds3Tooltip]="listItem.tooltipText"
							[multiline]="true">
							<div
								class="bi-card-content-section-content-list-item-text"
								*ngIf="listItem?.text">
								{{ listItem.text }}
							</div>
							<button
								ds3-text-button
								size="sm"
								(click)="openModalItem(listItem)"
								class="bi-card-content-section-content-list-item-value"
								*ngIf="listItem?.value >= 0">
								{{
									listItem.monetary
										? (listItem.value | currency : "BRL")
										: listItem.value
								}}
							</button>
							<div
								class="bi-card-content-section-content-list-item-percentage"
								*ngIf="listItem?.percentage >= 0">
								{{ listItem.percentage }}%
							</div>
						</div>
					</ng-container>
					<div *ngIf="isAllEmpty && !showHiddenFields" class="div-empty">
						<div class="d-flex flex-column align-items-center">
							<img
								class="icon-empty"
								src="pacto-ui/images/empty-state-search.svg" />
							<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
								Nenhuma operação registrada!
							</div>
						</div>
					</div>
					<div class="bi-card-content-section-content-list-actions">
						<button ds3-text-button (click)="mostrarMais()">
							{{ showHiddenFields ? "Contrair" : "Expandir" }} listagem
						</button>
					</div>
				</div>
			</div>
		</div>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@adm-bi-cont-op-exc:textClientesInativosComPeriodoAcesso"
		xingling="textClientesInativosComPeriodoAcesso">
		Clientes com contrato inativo com período de acesso
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesInativosComPeriodoAcesso"
		xingling="tooltipClientesInativosComPeriodoAcesso">
		Apresenta os clientes que possuem contratos inativos (vencidos ou
		cancelados) que tem período de acesso vigente, podendo o aluno acessar
		academia mesmo sem contrato ativo.
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textOperacoesContratoRetroativas"
		xingling="textOperacoesContratoRetroativas">
		Operações de contrato retroativas
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipOperacoesContratoRetroativas"
		xingling="tooltipOperacoesContratoRetroativas">
		Este BI apresenta os clientes que realizaram operações de contrato
		retroativos (passados) em seus contratos. A pesquisa considera o contrato
		atual, a partir do primeiro dia do mês pesquisado até o dia escolhido para a
		pesquisa. Lembrando que é considerado a data de lançamento da operação para
		a pesquisa de datas.
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textExclusaoVisitantes"
		xingling="textExclusaoVisitantes">
		Exclusão de visitantes
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipExclusaoVisitantes"
		xingling="tooltipExclusaoVisitantes">
		Este BI mostra os logs de clientes que visitaram a academia e que também
		tiveram seus logs excluídos. A pesquisa é feita a partir do primeiro dia do
		mês pesquisado até o dia escolhido para a pesquisa.
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textAlteracaoConsultorContrato"
		xingling="textAlteracaoConsultorContrato">
		Consultores de contrato alterados
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipAlteracaoConsultorContrato"
		xingling="tooltipAlteracaoConsultorContrato">
		Cada contrato possui um consultor responsável pelo lançamento, e neste
		indicador ele apresentará todas as vezes que, por algum motivo, o consultor
		de um contrato for modificado
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textEstornoContratoAdmin"
		xingling="textEstornoContratoAdmin">
		Contratos estornados pelo administrador
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipEstornoContratoAdmin"
		xingling="tooltipEstornoContratoAdmin">
		Mostra os logs (histórico de alteração de contratos) com o motivo registrado
		pelo usuário, de contratos estornados através dos planos de recorrência
		(para a pesquisa, o BI considera a data da consulta a partir do primeiro dia
		do mês pesquisado até o dia escolhido para a pesquisa)
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textEstornoContratoRecorrencia"
		xingling="textEstornoContratoRecorrencia">
		Contratos estornados automaticamente
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipEstornoContratoRecorrencia"
		xingling="tooltipEstornoContratoRecorrencia">
		Mostra os logs com o motivo registrado pelo usuário de contratos estornados,
		por usuário comum, ou seja, ele apresenta um histórico dos contratos que
		foram alterados e quem foi o usuário comum que fez a alteração. Para a
		pesquisa, o BI considera a data da consulta a partir do primeiro dia do mês
		pesquisado até o dia escolhido para a pesquisa
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textEstornoContratoUsuarioComum"
		xingling="textEstornoContratoUsuarioComum">
		Contratos estornados pelos usuários da empresa
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipEstornoContratoUsuarioComum"
		xingling="tooltipEstornoContratoUsuarioComum">
		Mostra o controle das parcelas que foram canceladas (logs com o registro de
		usuário, data, hora, o aluno e sua parcela que foi cancelada)
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textParcelasCanceladas"
		xingling="textParcelasCanceladas">
		Parcelas canceladas
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipParcelasCanceladas"
		xingling="tooltipParcelasCanceladas">
		Aqui você terá o controle das operações de estorno de recibo. Este BI
		registra o usuário, a data, a hora e o aluno. Ao clicar na lupa é possível
		visualizar o log, exibindo todas as informações do recibo que foi estornado
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textEstornoRecibo"
		xingling="textEstornoRecibo">
		Recibos estornados
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipEstornoRecibo"
		xingling="tooltipEstornoRecibo">
		Mostra contratos em que sua data de lançamento foi modificada por algum
		motivo. Ele considera a data da consulta a partir do primeiro dia do mês
		pesquisado, até o dia escolhido para a pesquisa
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textAlteracoesDataBaseContrato"
		xingling="textAlteracoesDataBaseContrato">
		Contratos com DataBase alterada
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipAlteracoesDataBaseContrato"
		xingling="tooltipAlteracoesDataBaseContrato">
		Mostra os contratos que tiveram sua data de pagamento modificada por algum
		motivo. Ele considera a data da consulta a partir do primeiro dia do mês
		pesquisado até o dia escolhido para a pesquisa
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textAlteracoesDataBasePagamento"
		xingling="textAlteracoesDataBasePagamento">
		Pagamentos com DataBase alterada
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipAlteracoesDataBasePagamento"
		xingling="tooltipAlteracoesDataBasePagamento">
		Apresenta os alunos que tiveram parcelas renegociadas, diferente dos
		registros do controle de log ele agrupa as ações, desde que estejam dentro
		do mesmo período
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textRenegociacaoParcelaRetroativa"
		xingling="textRenegociacaoParcelaRetroativa">
		Parcelas renegociadas
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipRenegociacaoParcelaRetroativa"
		xingling="tooltipRenegociacaoParcelaRetroativa">
		Aluno “A” teve duas renegociações em momentos diferentes, mas no mesmo mês,
		no log ele vai exibir as duas renegociações, e no BI só uma vez o nome do
		aluno, mas ao clicar na lupa o sistema vai apresentar que foram duas
		renegociações. Ele considera a data da consulta a partir do primeiro dia do
		mês pesquisado até o dia escolhido para a pesquisa
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textAlteracoesRecibo"
		xingling="textAlteracoesRecibo">
		Pagamentos editados
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipAlteracoesRecibo"
		xingling="tooltipAlteracoesRecibo">
		Mostra os logs de edições de pagamentos já realizadas. Na pesquisa, ele
		considera a data da consulta a partir do primeiro dia do mês pesquisado até
		o dia escolhido para a pesquisa. Ao clicar na quantidade de edições de
		pagamento, você verá uma lista de logs agrupados por chave primária
		referentes a alterações de pagamento. Ao clicar na lupa verá mais detalhes
		deste log, incluindo os valores anteriores a edição e os valores atuais após
		a edição
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textContratosCancelamento"
		xingling="textContratosCancelamento">
		Contratos cancelados
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipContratosCancelamento"
		xingling="tooltipContratosCancelamento">
		Mostra os contratos que foram cancelados. Ele considera a data da consulta a
		partir do primeiro dia do mês pesquisado até o dia escolhido para a
		pesquisa. Para usuários com permissão, o sistema permitirá pesquisar por
		colaboradores de outros grupos ou do próprio grupo. Ao clicar em algum dos
		números será aberta uma página contendo uma lista com esses clientes
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textContratosTransferidosCancelados"
		xingling="textContratosTransferidosCancelados">
		Contratos cancelados que foram transferidos para outro aluno
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipContratosTransferidosCancelados"
		xingling="tooltipContratosTransferidosCancelados">
		Mostra todos os alunos que fizeram o cancelamento de um plano com
		transferência de contrato para outro aluno
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesComBonus"
		xingling="textClientesComBonus">
		Clientes com bônus
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesComBonus"
		xingling="tooltipClientesComBonus">
		Mostra alunos que possuem algum tipo de bônus, na empresa, sendo que, ele
		considera o dia da consulta que esteja entre a data de início e data final
		de lançamento do bônus, ou considera os bônus que vão iniciar no futuro
		(bônus iniciando na data da consulta do BI ou após a data), se for o mês
		atual. Se não for o mês atual, será apresentado somente os clientes com
		bônus em que a data da consulta do BI esteja entre o seu início e término
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesComFreePass"
		xingling="textClientesComFreePass">
		Clientes com freePass
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesComFreePass"
		xingling="tooltipClientesComFreePass">
		O relatório vai trazer alunos com status inativo, caso este possua bônus
		dentro do período de pesquisa. O relatório vai exibir também as pessoas que
		não tem nenhum vínculo com responsável, nas opções do bônus. A opção de
		pesquisa por vínculos de colaboradores, não irá apresentar os colaboradores
		sem grupo (CRM) para pesquisa, mas, se a pesquisa dos alunos for feita sem
		filtro de pesquisa, então, serão apresentados todos os alunos independente
		dos vínculos existentes com os colaboradores. Se o cliente tiver mais de um
		bônus lançado, o sistema só vai apresentar o primeiro bônus
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesComGymPass"
		xingling="textClientesComGymPass">
		Clientes com WellHub
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesComGymPass"
		xingling="tooltipClientesComGymPass">
		Apresentará um relatório de clientes que estão acessando a empresa porque
		possuem um FreePass (uma espécie de passe livre que permite que os clientes
		tenham entradas gratuitas na empresa)
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textContratosTipoBolsa"
		xingling="textContratosTipoBolsa">
		Alunos bolsa
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipContratosTipoBolsa"
		xingling="tooltipContratosTipoBolsa">
		Apresentará um relatório de clientes que estão acessando a empresa porque
		possuem um token do Gympass (uma espécie de passe livre que permite que os
		clientes tenham entradas gratuitas na empresa)
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesAutorizacaoNaoRenovavel"
		xingling="textClientesAutorizacaoNaoRenovavel">
		Clientes que possuem autorização de cobrança mas que seus contratos não
		possuem renovação automática
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesAutorizacaoNaoRenovavel"
		xingling="tooltipClientesAutorizacaoNaoRenovavel">
		São todos os clientes que possuem pelo menos um plano bolsa (valor zerado)
		lançados
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesAutorizacaoRenovavel"
		xingling="textClientesAutorizacaoRenovavel">
		Clientes que possuem autorização de cobrança e contrato que renova de forma
		automática
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesAutorizacaoRenovavel"
		xingling="tooltipClientesAutorizacaoRenovavel">
		São todos os clientes que possuem uma autorização de cobrança cadastrada,
		porém no plano lançado para o cliente, não está marcado para renovar
		automaticamente
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textValorDescontos"
		xingling="textValorDescontos">
		Total de valor não arrecadado devido a aplicação de descontos
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipValorDescontos"
		xingling="tooltipValorDescontos">
		Apresenta a somatória de todos os descontos aplicados nas vendas;Observação:
		os descontos incluídos neste indicador abrangem aqueles concedidos por
		convênios de descontos, descontos extras e descontos em vendas de produtos
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesExcluidosTreinoWeb"
		xingling="textClientesExcluidosTreinoWeb">
		Alunos excluídos do treinoweb que possuíam vínculos
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesExcluidosTreinoWeb"
		xingling="tooltipClientesExcluidosTreinoWeb">
		Apresenta todos os clientes que foram excluídos do módulo do Treino e que
		possuíam vínculos com professores
	</span>

	<span
		i18n="@@adm-bi-cont-op-exc:textClientesExcluidos"
		xingling="textClientesExcluidos">
		Alunos excluídos da base de dados
	</span>
	<span
		i18n="@@adm-bi-cont-op-exc:tooltipClientesExcluidos"
		xingling="tooltipClientesExcluidos">
		Apresenta todos os cadastro de clientes que foram excluídos da base dados,
		ou seja foram excluídos permanentemente seus cadastros do sistema
	</span>
</pacto-traducoes-xingling>
