import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalClientesContratoAtivoComPeriodoAcessoComponent } from "./bi-modal-clientes-contrato-ativo-com-periodo-acesso.component";

describe("BiModalClientesContratoAtivoComPeriodoAcessoComponent", () => {
	let component: BiModalClientesContratoAtivoComPeriodoAcessoComponent;
	let fixture: ComponentFixture<BiModalClientesContratoAtivoComPeriodoAcessoComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalClientesContratoAtivoComPeriodoAcessoComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiModalClientesContratoAtivoComPeriodoAcessoComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
