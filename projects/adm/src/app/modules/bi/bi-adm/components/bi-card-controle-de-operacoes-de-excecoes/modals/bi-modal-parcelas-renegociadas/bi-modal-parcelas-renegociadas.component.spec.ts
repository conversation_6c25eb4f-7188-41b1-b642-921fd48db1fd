import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalParcelasRenegociadasComponent } from "./bi-modal-parcelas-renegociadas.component";

describe("BiModalParcelasRenegociadasComponent", () => {
	let component: BiModalParcelasRenegociadasComponent;
	let fixture: ComponentFixture<BiModalParcelasRenegociadasComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalParcelasRenegociadasComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalParcelasRenegociadasComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
