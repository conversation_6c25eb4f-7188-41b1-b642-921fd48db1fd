import { TabData } from "@adm/modules/bi/bi-adm/bi-adm.model";
import { BiClientesParaVerificarModalComponent } from "@adm/modules/bi/bi-adm/components/bi-card-verificacao-clientes/bi-clientes-para-verificar-modal/bi-clientes-para-verificar-modal.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiMsApiClientesVerificadosService,
	ClientesVerificadosResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { InfoData } from "ui-kit";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";

interface TabDataVerificacaoClientes {
	sections: {
		infoItens: InfoData[];
		listItens: {
			text?: string;
			value?: number;
			valueHint?: string;
			percentage?: number;
			date?: Date;
			dateHint?: string;
			id: "verificados" | "nao_verificados" | "para_verificar";
		}[];
		chart: { name: string; data: number[] }[];
	}[];
}

@Component({
	selector: "adm-bi-card-verificacao-clientes",
	templateUrl: "./bi-card-verificacao-clientes.component.html",
	styleUrls: ["./bi-card-verificacao-clientes.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardVerificacaoClientesComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	loading: boolean = false;

	tabDataVerificacaoClientes: TabData = undefined;
	optionsChartVerificacaoClientes = {
		series: [],
		tooltip: {
			enabledOnSeries: true,
			onDatasetHover: {
				highlightDataSeries: true,
			},
			x: {
				show: false,
			},
		},
		yaxis: {
			show: false,
			labels: {
				show: false,
			},
			axisTicks: {
				show: false,
			},
			axisBorder: {
				show: false,
			},
		},
		events: {
			click: (event, chartContext, opts) => {
				if (opts.seriesIndex !== -1) {
					this.handleAbrirRelatorio(
						this.getRelatorioIdBySeriesName(
							opts.config.series[opts.seriesIndex].name
						)
					);
				}
			},
		},
	};
	private _clientesVerificadosResponse: ClientesVerificadosResponseModel;

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private datePipe: DatePipe,
		private toastrService: ToastrService,
		private biMsApiClientesVerificadosService: BiMsApiClientesVerificadosService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		} else {
			this._loadDataApi(reloadFull);
		}
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Verificação de clientes",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-verificacao-de-clientes/",
			module: ConhecimentoEnum.ADM,
		});
	}

	handleAbrirRelatorio(
		id: "verificados" | "nao_verificados" | "para_verificar"
	) {}

	getRelatorioIdBySeriesName(seriesName: string) {
		switch (seriesName) {
			case "Não verificados":
				return "nao_verificados";
			case "Verificados":
				return "verificados";
		}
	}

	trackByIndex(index, item) {
		return index;
	}

	private _loadDataApi(reloadFull: boolean = false) {
		this.loading = true;
		this.cd.detectChanges();
		this.biMsApiClientesVerificadosService
			.indicadores({
				filtroClientesVerificados: {
					atualizarAgora: reloadFull,
					data: this.filtros.data.getTime(),
					empresa: this.filtros.empresa,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: ClientesVerificadosResponseModel) => {
					this._clientesVerificadosResponse = v;
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	private _populateTabData(v?: ClientesVerificadosResponseModel) {
		if (!v) {
			v = {
				qtdVerificado: 0,
				qtdNaoVerificado: 0,
				totalVerificar: 0,
				indiceVerificacao: 0,
				dataPrimeiraVerificacao: 0,
				dataUltimaVerificacao: 0,
				mediaVerificacao: 0,
			} as ClientesVerificadosResponseModel;
		}
		this.tabDataVerificacaoClientes = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.indiceVerificacao / 100,
								valueHint: `${v.mediaVerificacao || 0} verificações por dia`,
								size: "24px",
								isPercentage: true,
								state: "disable",
							},
							overline: {
								text: this.traducoes.getLabel(
									"bi-verificacao-clientes:indice-verificacao"
								),
							},
						},
					],
					listItens: [
						{
							text: this.traducoes.getLabel(
								"bi-verificacao-clientes:clientes-verificacao"
							),
							value: v.totalVerificar,
							date:
								v.dataUltimaVerificacao && v.dataUltimaVerificacao > 0
									? new Date(v.dataPrimeiraVerificacao)
									: undefined,
							dateHint:
								v.dataUltimaVerificacao && v.dataUltimaVerificacao > 0
									? `Primeira verificação em: ${this.datePipe.transform(
											v.dataPrimeiraVerificacao,
											"dd/MM/yyyy - HH:mm:ss"
									  )}`
									: "Não há verificações",
							id: "para_verificar",
							modalConfig: {
								componentType: BiClientesParaVerificarModalComponent,
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-verificacao-clientes:clientes-ja-verificados"
							),
							value: v.qtdVerificado,
							date:
								v.dataUltimaVerificacao && v.dataUltimaVerificacao > 0
									? new Date(v.dataUltimaVerificacao)
									: undefined,
							dateHint:
								v.dataUltimaVerificacao && v.dataUltimaVerificacao > 0
									? `Última verificação em: ${this.datePipe.transform(
											v.dataUltimaVerificacao,
											"dd/MM/yyyy - HH:mm:ss"
									  )}`
									: "Não há verificações",
							id: "verificados",
							modalConfig: {
								componentType: BiClientesParaVerificarModalComponent,
								config: {
									data: {
										verificado: true,
									},
								},
							},
						},
						{
							text: this.traducoes.getLabel(
								"bi-verificacao-clientes:clientes-nao-verificados"
							),
							value: v.qtdNaoVerificado,
							id: "nao_verificados",
							modalConfig: {
								componentType: BiClientesParaVerificarModalComponent,
								config: {
									data: {
										verificado: false,
									},
								},
							},
						},
					],
					chart: [
						{
							name: this.traducoes.getLabel(
								"bi-verificacao-clientes:verificados"
							),
							data: [v.qtdVerificado],
						},
						{
							name: this.traducoes.getLabel(
								"bi-verificacao-clientes:nao-verificados"
							),
							data: [v.qtdNaoVerificado],
						},
					],
				},
			],
		};

		this.optionsChartVerificacaoClientes.series = [
			{
				name: this.traducoes.getLabel("bi-verificacao-clientes:verificados"),
				data: [v.qtdVerificado],
			},
			{
				name: this.traducoes.getLabel(
					"bi-verificacao-clientes:nao-verificados"
				),
				data: [v.qtdNaoVerificado],
			},
		];
	}
}
