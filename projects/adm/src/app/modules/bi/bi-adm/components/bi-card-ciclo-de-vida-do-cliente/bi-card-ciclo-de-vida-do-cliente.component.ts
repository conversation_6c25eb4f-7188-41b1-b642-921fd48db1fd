import { BiCardCicloDeVidaDoClienteFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-ciclo-de-vida-do-cliente/bi-card-cliclo-de-vida-do-cliente-filter/bi-card-ciclo-de-vida-do-cliente-filter.component";
import { BiModalLtvInfosComponent } from "@adm/modules/bi/bi-adm/components/bi-card-ciclo-de-vida-do-cliente/bi-modal-ltv-infos/bi-modal-ltv-infos.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnChanges,
	OnDestroy,
	OnInit,
	SimpleChanges,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { MatDialogConfig } from "@angular/material/dialog/typings/dialog-config";
import { Router } from "@angular/router";
import { AdmCoreApiPlanoContaService } from "adm-core-api";
import {
	BiEnum,
	BiMsApiLtvService,
	ConfiguracaoBIEnum,
	DadosLtvChurnRateEmpresaJSON,
	DadosLtvGraficoChurnRateJSON,
	getConfigByBi,
	LtvResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { PermissaoService } from "pacto-layout";
import { takeUntil } from "rxjs/operators";
import { ConhecimentoEnum } from "../../../bi-shared/models/bi-mdl-ajuda.model";
import { BiMdlAjudaService } from "../../../bi-shared/services/bi-mdl-ajuda.service";
import { TabData } from "../../bi-adm.model";

@Component({
	selector: "adm-bi-card-ciclo-de-vida-do-cliente",
	templateUrl: "./bi-card-ciclo-de-vida-do-cliente.component.html",
	styleUrls: ["./bi-card-ciclo-de-vida-do-cliente.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardCicloDeVidaDoClienteComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy, OnChanges
{
	loading: boolean = false;
	tabData: TabData;
	configLTV: { configLtvRealizado: boolean } = { configLtvRealizado: false };

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private biMdlAjudaService: BiMdlAjudaService,
		private biSidenavService: BiSidenavService,
		private toastrService: ToastrService,
		private biMsApiLtvService: BiMsApiLtvService,
		private admCoreApiPlanoContaService: AdmCoreApiPlanoContaService,
		private permissaoService: PermissaoService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init({
			dataFimUltimoDiaMes: true,
		});
		this.filtros.dataFim = this.filtros.data;
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this._loadDespesas();
	}

	ngOnChanges(changes: SimpleChanges) {
		const configs = changes.config;
		if (configs && !configs.isFirstChange()) {
			this.configBi = configs.currentValue;
			this.loadData(true);
		}
	}

	ngOnDestroy() {
		super.destroy();
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Ciclo de vida do cliente",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/como-habilitar-o-bi-ciclo-de-vida-do-seu-cliente/",
			module: ConhecimentoEnum.ADM,
		});
	}

	openSideNav() {
		const ref = this.biSidenavService.open(
			BiCardCicloDeVidaDoClienteFilterComponent,
			{
				globalFilter: this.globalFilterForm.value,
				filters: this.filtros,
			}
		);

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	loadData(reloadFull: boolean = false) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		}
		if (!this.withFakeData) {
			this._loadDataApi(reloadFull);
		}
	}

	private _loadDataApi(reloadFull) {
		this.loading = true;
		const produtosCode = this.filtros.produtos
			? this.filtros.produtos.map((v) => v.codigo)
			: [];
		const servicosCode = this.filtros.servicos
			? this.filtros.servicos.map((v) => v.codigo)
			: [];
		const despesasCode = this.filtros.despesas
			? this.filtros.despesas.map((v) => v.codigo)
			: [];

		this.configLTV = this.buildBiConfig();
		this.cd.detectChanges();
		this.biMsApiLtvService
			.list({
				filtroLtv: {
					dataInicio: this.filtros.dataInicio.getTime(),
					dataFim: this.filtros.dataFim.getTime(),
					empresa: this.filtros.empresa,
					consultarTodasEmpresas: this.permissaoService.temPermissaoAdm("9.47"),
					produtos: [...produtosCode, ...servicosCode],
					servicos: servicosCode.length > 0 || true,
					empresaGrafico: this.filtros.empresa,
					planoDeContas: despesasCode,
					configLtvRealizado: this.configLTV.configLtvRealizado,
				},
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: LtvResponseModel) => {
					this._populateTabData(v);
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	trackByIndex(index, item) {
		return index;
	}

	private _populateTabData(v?: LtvResponseModel) {
		if (!v) {
			v = {
				ltv: 0,
				ltvGeral: 0,
				cac: 0,
				churn: 0,
				tempoDuracaoMedioDosContratos: 0,
				valorProdutos: 0,
				valorServicos: 0,
				valorMedioMensal: 0,
				valorDuracoes: 0,
				mesAtual: 0,
				mesAnterior: 0,
				diferencaMesAnterior: 0,
				corDiferencaMesAnterior: "",
				seisMeses: 0,
				diferencaSeisMeses: 0,
				corDiferencaSeisMeses: "",
				dozeMeses: 0,
				diferencaDozeMeses: 0,
				corDiferencaDozeMeses: "",
				listaAnoAtual: [] as DadosLtvGraficoChurnRateJSON[],
				listaAnoPassado: [] as DadosLtvGraficoChurnRateJSON[],
				listaAnoRetrasado: [] as DadosLtvGraficoChurnRateJSON[],
				dadosDoAnoDasEmpresas: [] as DadosLtvChurnRateEmpresaJSON[],
			};
		}

		this.tabData = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.cac,
								size: "24px",
								isMonetary: true,
								state: "disable",
							},
							textButton: undefined,
							middleLegend: {
								legend: "Custo de aquisição do cliente",
							},
							overline: {
								afterIcon: {
									class: "pct pct-info",
									size: "20px",
								},
								text: "CAC",
							},
							modalConfig: {
								componentType: BiModalLtvInfosComponent,
								config: {
									data: {
										type: "cac",
										filters: this.filtros,
									},
									width: "600px",
								} as MatDialogConfig,
							},
						},
						{
							info: {
								value: v.ltv,
								size: "24px",
								isMonetary: true,
								state: "info",
							},
							textButton: undefined,
							overline: {
								afterIcon: {
									class: "pct pct-info",
									size: "20px",
								},
								text: "LTV",
							},
							middleLegend: {
								legend: `Tempo médio de vida dos clientes: ${v.tempoDuracaoMedioDosContratos} meses.`,
								hovered: true,
							},
							modalConfig: {
								componentType: BiModalLtvInfosComponent,
								config: {
									data: {
										type: "ltv",
										filters: this.filtros,
									},
									width: "600px",
								} as MatDialogConfig,
							},
						},
						{
							info: {
								value: v.churn / 100,
								size: "24px",
								isMonetary: false,
								isPercentage: true,
								state: "info",
							},
							middleLegend: {
								legend: "Taxa de cancelamento",
							},
							textButton: undefined,
							overline: {
								text: "Churn rate",
								afterIcon: {
									class: "pct pct-info",
									size: "20px",
								},
							},
							modalConfig: {
								componentType: BiModalLtvInfosComponent,
								config: {
									data: {
										type: "churn",
										filters: this.filtros,
									},
									width: "600px",
								} as MatDialogConfig,
							},
						},
					],
					listItens: [],
				},
			],
		};
	}

	private buildBiConfig() {
		const configsLTV = getConfigByBi(BiEnum.LTV, this.configBi);

		let configLtvRealizadoValor = false;
		const configLtvRealizado = configsLTV.find(
			(cfg) =>
				cfg.configuracao.codigo ===
				ConfiguracaoBIEnum.LTV_CONSIDERAR_LTV_REALIZADO
		);

		if (configLtvRealizado) {
			configLtvRealizadoValor = configLtvRealizado.valorAsBoolean;
		}

		return {
			configLtvRealizado: configLtvRealizadoValor,
		};
	}

	private _loadDespesas() {
		if (this.withFakeData) {
			return;
		}
		this.admCoreApiPlanoContaService
			.findAllDespesas({
				filters: {
					codigoEmpresa: this.filtros.empresa,
					dataInicio: this.filtros.dataInicio.getTime(),
					dataFim: this.filtros.dataFim.getTime(),
				},
				page: 0,
				size: 50,
			})
			.pipe(takeUntil(super.destroy$))
			.subscribe({
				next: (response) => {
					const despesas = response.content;
					if (!this.filtros.despesas || this.filtros.despesas.length === 0) {
						const despesasPadraoLtv = despesas.filter((d) => d.insideltv);
						if (despesasPadraoLtv) {
							this.filtros.despesas = despesasPadraoLtv;
						}
					}
					this.loadData(this.reloadFull);
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(
						message || "Ocorreu um erro desconhecido ao carregar as despesas!"
					);
					this.cd.detectChanges();
				},
			});
	}

	verDetalhes() {}
}
