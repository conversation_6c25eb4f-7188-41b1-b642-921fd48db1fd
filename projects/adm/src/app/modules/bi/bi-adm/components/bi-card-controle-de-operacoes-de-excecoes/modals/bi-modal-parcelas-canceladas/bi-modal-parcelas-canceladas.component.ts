import { Component, Inject, OnD<PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiObservacaoOperacaoService,
	ApiResponseSinglePaginated,
	FiltroBiControleOperacao,
	ObservacaoOperacao,
	ObservacaoOperacaoTotais,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-modal-parcelas-canceladas",
	templateUrl: "./bi-modal-parcelas-canceladas.component.html",
	styleUrls: [
		"./bi-modal-parcelas-canceladas.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalParcelasCanceladasComponent implements OnInit, OnDestroy {
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "movParcela.codigo",
			visible: true,
			titulo: "Parcela",
			ordenavel: true,
			nome: "parcela",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "movParcela.valor",
			visible: true,
			titulo: "Valor",
			ordenavel: true,
			nome: "valor",
			inputType: "currency",
			decimal: true,
			decimalPrecision: 2,
		},
		{
			mostrarTitulo: true,
			campo: "dataOperacao",
			visible: true,
			titulo: "Dt. Operação",
			ordenavel: true,
			nome: "dataOperacao",
			inputType: "date",
			date: true,
		},
		{
			mostrarTitulo: true,
			campo: "usuarioResponsavel",
			visible: true,
			titulo: "Resp. Lançamento",
			ordenavel: true,
			nome: "usuarioResponsavel",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "justificativa",
			visible: true,
			titulo: "Justificativa",
			ordenavel: true,
			nome: "justificativa",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBiControleOperacao = {} as FiltroBiControleOperacao;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"observacao-operacao/parcelas-canceladas-sem-totais"
	);
	observacaoOperacao: ObservacaoOperacaoTotais = {} as ObservacaoOperacaoTotais;
	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiModalParcelasCanceladasComponent>,
		private admCoreObservacaoOperacaoService: AdmCoreApiObservacaoOperacaoService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreObservacaoOperacaoService
			.parcelasCanceladas({
				filters: this.filters,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (
					response: ApiResponseSinglePaginated<ObservacaoOperacaoTotais>
				) => {
					this.totalItems = response.totalElements;
					const content = response.content;
					if (content.observacoes) {
						this.observacaoOperacao = content;
					}
					this.tableState.patchState({
						data: this.observacaoOperacao.observacoes,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: ObservacaoOperacao) {
		this.biCommonService.openCliente(item.matriculaCliente);
	}
}
