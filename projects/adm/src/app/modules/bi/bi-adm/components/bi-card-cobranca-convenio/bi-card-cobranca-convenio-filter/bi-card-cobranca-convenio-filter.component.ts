import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	AdmCoreApiConvenioCobrancaService,
	ApiResponseList,
	ConvenioCobranca,
} from "adm-core-api";

@Component({
	selector: "adm-bi-card-cobranca-convenio-filter",
	templateUrl: "./bi-card-cobranca-convenio-filter.component.html",
	styleUrls: ["./bi-card-cobranca-convenio-filter.component.scss"],
})
export class BiCardCobrancaConvenioFilterComponent implements OnInit {
	globalFilter: BiGlobalFilter;

	formGroup: FormGroup = new FormGroup({
		data: new FormControl(),
		convenios: new FormControl(),
		incluirContratosCancelados: new FormControl(),
		somenteParcelasForaMes: new FormControl(),
		somenteParcelasMes: new FormControl(),
	});
	filters: {
		data: Date;
		convenios: Array<ConvenioCobranca>;
		incluirContratosCancelados: boolean;
		somenteParcelasForaMes: boolean;
		somenteParcelasMes: boolean;
	} = {
		data: undefined,
		convenios: undefined,
		incluirContratosCancelados: false,
		somenteParcelasForaMes: false,
		somenteParcelasMes: false,
	};
	convenios: Array<ConvenioCobranca> = new Array<ConvenioCobranca>();

	constructor(
		private admCoreApiConvenioCobrancaService: AdmCoreApiConvenioCobrancaService,
		private sidenav: BiSidenavRef<BiCardCobrancaConvenioFilterComponent>
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
		this._loadConvenios();
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.formGroup.get("data").setValue(this.filters.data);
			this.formGroup
				.get("incluirContratosCancelados")
				.setValue(this.filters.incluirContratosCancelados);
			this.formGroup
				.get("somenteParcelasForaMes")
				.setValue(this.filters.somenteParcelasForaMes);
			this.formGroup
				.get("somenteParcelasMes")
				.setValue(this.filters.somenteParcelasMes);
		}
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.formGroup.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _loadConvenios() {
		this.admCoreApiConvenioCobrancaService
			.findAllByEmpresa(this.globalFilter.empresa)
			.subscribe((response: ApiResponseList<ConvenioCobranca>) => {
				this.convenios = response.content;

				if (this.filters.convenios) {
					this.formGroup.get("convenios").setValue(this.filters.convenios);
				}
			});
	}

	onClose() {
		this.sidenav.close();
	}

	onFilter() {
		this.sidenav.close({
			filters: { ...this.globalFilter, ...this.formGroup.value },
		});
	}

	onClear() {
		this.formGroup.reset({});
		this.sidenav.close({ filters: this.globalFilter });
	}

	onChipSelected(selected: boolean, controlName: string) {
		this.formGroup.get(controlName).setValue(selected);
	}
}
