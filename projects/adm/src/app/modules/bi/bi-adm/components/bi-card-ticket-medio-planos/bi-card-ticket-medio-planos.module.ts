import { BiCardTicketMedioPlanosComponent } from "@adm/modules/bi/bi-adm/components/bi-card-ticket-medio-planos/bi-card-ticket-medio-planos.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiTicketMedioPlanosFilterComponent } from "./bi-ticket-medio-planos-filter/bi-ticket-medio-planos-filter.component";

@NgModule({
	declarations: [
		BiCardTicketMedioPlanosComponent,
		BiTicketMedioPlanosFilterComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardTicketMedioPlanosComponent],
	entryComponents: [BiTicketMedioPlanosFilterComponent],
})
export class BiCardTicketMedioPlanosModule {}
