import { Component, Inject, OnD<PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiLogService,
	ApiResponseList,
	FiltroLog,
	Log,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../../adm-rest.service";

@Component({
	selector: "adm-bi-modal-alunos-excluidos-tw-com-vinculos",
	templateUrl: "./bi-modal-alunos-excluidos-tw-com-vinculos.component.html",
	styleUrls: [
		"./bi-modal-alunos-excluidos-tw-com-vinculos.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiModalAlunosExcluidosTwComVinculosComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "nomeExclusaoVisitantes",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "dataAlteracao",
			visible: true,
			titulo: "Dt. Alteração",
			ordenavel: true,
			nome: "dataAlteracao",
			inputType: "date",
			dateTime: true,
		},
		{
			mostrarTitulo: true,
			campo: "responsavelAlteracao",
			visible: true,
			titulo: "Resp. Alteração",
			ordenavel: true,
			nome: "responsavelAlteracao",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroLog = {} as FiltroLog;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"logApi/por-entidade-operacao-com-cliente"
	);
	private _destroy$: Subject<void> = new Subject();

	constructor(
		private dialog: MatDialogRef<BiModalAlunosExcluidosTwComVinculosComponent>,
		private admCoreApiLogService: AdmCoreApiLogService,
		private admRest: AdmRestService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = this.modalData.modalTitle;
			}
		}
		this._loadData();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quicksearchValue = value;
		this.page = 0;
		this._loadData();
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.filters.nomeEntidade = "CLIENTESINTETICO";
		this.filters.operacao = "EXCLUSÃO";
		this.admCoreApiLogService
			.porEntidadeOperacaoComCliente({
				filters: this.filters,
				page: this.page,
				size: this.size,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<Log>) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}
}
