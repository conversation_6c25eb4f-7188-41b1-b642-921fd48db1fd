@import "../../../bi-shared/bi-shared.scss";
@import "dist/ui-kit/assets/ds3/colors.var";
@import "dist/ui-kit/assets/ds3/colors";

.bi-card.ticket-medio-planos {
	.bi-card-content-chart {
		h2 {
			margin-top: 16px;
			margin-bottom: 16px;
		}
	}
	.bi-card-content-section {
		display: flex;
		flex-wrap: wrap;
		gap: 16px 8px;
	}
	.bi-card-content-section-content {
		width: calc(33% - 5.3px);
		padding: 0;
		&:nth-child(3),
		&:nth-child(4) {
			ds3-bi-info {
				&::ng-deep {
					.info-area {
					}
				}
			}
		}
		&:first-child {
			width: 100%;

			ds3-bi-info {
				&::ng-deep {
					.info-area {
						display: flex;
						flex-wrap: wrap;

						ds3-diviser:first-of-type {
							display: none;
						}
					}
					.item-box {
						width: calc(33% - 5.3px);
						&:first-child {
							width: 100%;
							border-bottom: 1px solid $supportGray03;
						}
					}
				}
			}
		}
	}
}
