import { BiCardInadimplenciaComponent } from "@adm/modules/bi/bi-adm/components/bi-card-inadimplencia/bi-card-inadimplencia.component";
import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiInadimplenciaFilterComponent } from "./bi-inadimplencia-filter/bi-inadimplencia-filter.component";

@NgModule({
	declarations: [BiCardInadimplenciaComponent, BiInadimplenciaFilterComponent],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardInadimplenciaComponent],
	entryComponents: [BiInadimplenciaFilterComponent],
})
export class BiCardInadimplenciaModule {}
