import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiCardCicloDeVidaDoClienteFilterComponent } from "./bi-card-ciclo-de-vida-do-cliente-filter.component";

describe("BiCardCicloDeVidaDoClienteFilterComponent", () => {
	let component: BiCardCicloDeVidaDoClienteFilterComponent;
	let fixture: ComponentFixture<BiCardCicloDeVidaDoClienteFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiCardCicloDeVidaDoClienteFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiCardCicloDeVidaDoClienteFilterComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
