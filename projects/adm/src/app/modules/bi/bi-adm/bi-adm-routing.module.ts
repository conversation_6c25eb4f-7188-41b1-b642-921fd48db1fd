import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { BiAdmComponent } from "./bi-adm.component";
import { BiAdmChurnRateComponent } from "./pages/bi-adm-churn-rate/bi-adm-churn-rate.component";

const routes: Routes = [
	{
		path: "",
		component: BiAdmComponent,
	},
	{
		path: "churn-rate",
		component: BiAdmChurnRateComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class BiAdmRoutingModule {}
