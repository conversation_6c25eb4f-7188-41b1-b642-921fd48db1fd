<adm-bi-side-filter></adm-bi-side-filter>
<ng-container *ngIf="!loadingBiConfig">
	<ds3-card class="bi-adm-card-filter">
		<form [formGroup]="filterForm">
			<ds3-input-date
				class="bi-adm-data-geral-filter"
				dateType="datepicker"
				[control]="filterForm.controls['dataGeral']"></ds3-input-date>
			<ds3-form-field class="bi-adm-empresa-filter">
				<ds3-select
					class="bi-adm-empresa-filter-select"
					ds3Input
					formControlName="empresa"
					[options]="optionsEmpresa"
					valueKey="codigo"
					nameKey="nome"
					placeholder="Selecione uma empresa"></ds3-select>
			</ds3-form-field>
			<ds3-form-field class="bi-adm-colaborador-filter">
				<ds3-select-multi
					ds3Input
					formControlName="colaboradores"
					[options]="optionsColaboradores"
					nameKey="label"
					valueKey="value"
					placeholder="Selecione um colaborador"></ds3-select-multi>
			</ds3-form-field>
		</form>
		<div class="bi-adm-card-filter-buttons">
			<button ds3-outlined-button (click)="applyGlobalFilters(true)">
				<i class="pct pct-refresh-cw"></i>
			</button>
			<button ds3-outlined-button (click)="configBI()">
				<i class="pct pct-settings"></i>
			</button>
			<button ds3-outlined-button (click)="organizeBI()">
				<i class="pct pct-move"></i>
			</button>
		</div>
	</ds3-card>
	<div class="bi-content">
		<div class="bi-first-col">
			<ng-container *ngFor="let data of firstColumnTemplates">
				<ng-container *ngIf="data?.visible">
					<ng-template *ngTemplateOutlet="data?.template"></ng-template>
				</ng-container>
			</ng-container>
		</div>
		<div class="bi-second-col">
			<ng-container *ngFor="let data of secondColumnTemplates">
				<ng-container *ngIf="data?.visible">
					<ng-template *ngTemplateOutlet="data?.template"></ng-template>
				</ng-container>
			</ng-container>
		</div>
	</div>
</ng-container>
<div class="bi-adm-loading" *ngIf="loadingBiConfig">
	<img
		src="pacto-ui/images/gif/loading-pacto.gif"
		alt="Loading pacto"
		width="80" />
</div>
<ng-template #biControleOperacoesExcecoesTmpl>
	<adm-bi-card-controle-de-operacoes-de-excecoes
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-controle-de-operacoes-de-excecoes>
</ng-template>
<ng-template #biIntegradoresAcessoTmpl>
	<adm-bi-card-integradores-de-acesso
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-integradores-de-acesso>
</ng-template>
<ng-template #biIndiceRenovacaoTmpl>
	<adm-bi-card-indice-renovacao
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-indice-renovacao>
</ng-template>
<ng-template #biVerificacaoClientesTmpl>
	<adm-bi-card-verificacao-clientes
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-verificacao-clientes>
</ng-template>
<ng-template #biPendenciasClienteTmpl>
	<adm-bi-card-pendencias-de-clientes
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-pendencias-de-clientes>
</ng-template>
<ng-template #biChurnPredictionTmpl>
	<adm-bi-card-churn-prediction
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-churn-prediction>
</ng-template>
<ng-template #biCicloVidaClienteTmpl>
	<adm-bi-card-ciclo-de-vida-do-cliente
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-ciclo-de-vida-do-cliente>
</ng-template>
<ng-template #biInadimplenciaTmpl>
	<adm-bi-card-inadimplencia
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-inadimplencia>
</ng-template>
<ng-template #biMovimentacaoContratosTmpl>
	<adm-bi-card-movimentacao-contratos
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-movimentacao-contratos>
</ng-template>
<ng-template #biConversaoVendasTmpl>
	<adm-bi-card-conversao-vendas
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-conversao-vendas>
</ng-template>
<ng-template #biConversaoVendasSessaoTmpl>
	<!--	<span>BI Conversão de vendas por sessão em desenvolvimento</span>-->
</ng-template>
<ng-template #biMetasFinanceirasVendasTmpl>
	<adm-bi-card-metas-financeiras
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-metas-financeiras>
</ng-template>
<ng-template #biTicketMedioPlanosTmpl>
	<adm-bi-card-ticket-medio-planos
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-ticket-medio-planos>
</ng-template>
<ng-template #biAulasExperimentaisTmpl>
	<adm-bi-card-aulas-experimentais
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-aulas-experimentais>
</ng-template>
<ng-template #biGestaoAcessoTmpl>
	<adm-bi-card-gestao-de-acesso
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-gestao-de-acesso>
</ng-template>
<ng-template #biCobrancasConvenioTmpl>
	<adm-bi-card-cobranca-convenio
		[globalFilterForm]="filterForm"
		[reloadFull]="reloadFull"
		[configBi]="config"></adm-bi-card-cobranca-convenio>
</ng-template>
