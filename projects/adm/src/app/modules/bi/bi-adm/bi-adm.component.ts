import { BiAdmConfigComponent } from "@adm/modules/bi/bi-adm/components/bi-adm-config/bi-adm-config.component";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { HttpClient } from "@angular/common/http";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MatDialog } from "@angular/material";
import { DomSanitizer } from "@angular/platform-browser";
import { ActivatedRoute, Router } from "@angular/router";
import {
	AdmCoreApiBiAbaAdmService,
	AdmCoreApiGrupoColaboradorService,
	ApiResponseList,
	Colaborador,
	GrupoColaborador,
	GrupoColaboradorParticipante,
} from "adm-core-api";
import {
	ApiResponseList as BiMsApiResponseList,
	ApiResponseSingle,
	BiMsApiConfigBiService,
	BiMsApiOrganizeService,
	ConfiguracaoBi,
	ConfiguracaoSistemaUsuario,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { PermissaoService } from "pacto-layout";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { EmpresaFinanceiro, SessionService } from "sdk";
import { BiAdmOrganizeComponent } from "./components/bi-adm-organize/bi-adm-organize.component";

function deepCopy<T>(obj: T, ...keysToIgnore): T {
	if (obj === null || typeof obj !== "object") {
		return obj;
	}

	if (obj instanceof Date) {
		return new Date(obj.getTime()) as any;
	}

	if (Array.isArray(obj)) {
		return obj.map((o) => deepCopy(o, ...keysToIgnore)) as any;
	}

	const result: any = {};
	for (const key in obj) {
		if (
			!keysToIgnore.includes(key) &&
			Object.prototype.hasOwnProperty.call(obj, key)
		) {
			result[key] = deepCopy((obj as any)[key], ...keysToIgnore);
		}
	}
	return result;
}

@Component({
	selector: "adm-bi-adm",
	templateUrl: "./bi-adm.component.html",
	styleUrls: ["./bi-adm.component.scss", "../bi-shared/bi-shared.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiAdmComponent implements OnInit, AfterViewInit, OnDestroy {
	@ViewChild("biControleOperacoesExcecoesTmpl", { static: false })
	biControleOperacoesExcecoesTmpl: TemplateRef<any>;

	@ViewChild("biIntegradoresAcessoTmpl", { static: false })
	biIntegradoresAcessoTmpl: TemplateRef<any>;

	@ViewChild("biIndiceRenovacaoTmpl", { static: false })
	biIndiceRenovacaoTmpl: TemplateRef<any>;

	@ViewChild("biChurnPredictionTmpl", { static: false })
	biChurnPredictionTmpl: TemplateRef<any>;

	@ViewChild("biInadimplenciaTmpl", { static: false })
	biInadimplenciaTmpl: TemplateRef<any>;

	@ViewChild("biCicloVidaClienteTmpl", { static: false })
	biCicloVidaClienteTmpl: TemplateRef<any>;

	@ViewChild("biPendenciasClienteTmpl", { static: false })
	biPendenciasClienteTmpl: TemplateRef<any>;

	@ViewChild("biConversaoVendasTmpl", { static: false })
	biConversaoVendasTmpl: TemplateRef<any>;

	@ViewChild("biMetasFinanceirasVendasTmpl", { static: false })
	biMetasFinanceirasVendasTmpl: TemplateRef<any>;

	@ViewChild("biAulasExperimentaisTmpl", { static: false })
	biAulasExperimentaisTmpl: TemplateRef<any>;

	@ViewChild("biGestaoAcessoTmpl", { static: false })
	biGestaoAcessoTmpl: TemplateRef<any>;

	@ViewChild("biMovimentacaoContratosTmpl", { static: false })
	biMovimentacaoContratosTmpl: TemplateRef<any>;

	@ViewChild("biCobrancasConvenioTmpl", { static: false })
	biCobrancasConvenioTmpl: TemplateRef<any>;

	@ViewChild("biTicketMedioPlanosTmpl", { static: false })
	biTicketMedioPlanosTmpl: TemplateRef<any>;

	@ViewChild("biVerificacaoClientesTmpl", { static: false })
	biVerificacaoClientesTmpl: TemplateRef<any>;

	detaildedSideRelatorioData: any;
	permissaoVerificacaoClientes: boolean;
	permissaoIndiceRenovacao: boolean;
	permissaoPendenciaClientes: boolean;
	permissaoChurnPrediction: boolean;
	permissaoMovimentacaoContratos: boolean;
	permissaoCobrancasConvenio: boolean;
	permissaoCicloVidaCliente: boolean;
	permissaoIntegradoresAcesso: boolean;
	permissaoConversaoVendas: boolean;
	permissaoControleOperacoesExcecao: boolean;
	permissaoInadimplencia: boolean;
	permissaoAulasExperimentais: boolean;
	permissaoTicketMedioPlano: boolean;
	permissaoGestaoAcesso: boolean;
	permissaoMetasFinanceirasVendas: boolean;

	optionsEmpresa: Array<EmpresaFinanceiro> = new Array<EmpresaFinanceiro>();

	filterForm: FormGroup = new FormGroup({
		dataGeral: new FormControl(new Date()),
		empresa: new FormControl(),
		empresaNome: new FormControl(),
		colaboradores: new FormControl(),
		colaboradoresObj: new FormControl(),
		nomesColaboradores: new FormControl(),
	});

	config: Array<ConfiguracaoBi> = new Array<ConfiguracaoBi>();
	reloadFull: boolean = false;

	gruposColaboradores: Array<GrupoColaborador> = new Array<GrupoColaborador>();
	optionsColaboradores: Array<{ value: any; label: string }> = new Array<{
		value: any;
		label: string;
	}>();

	sideFilterRef;
	firstColumnTemplates: Array<{
		id: number;
		template: TemplateRef<any>;
		visible: boolean;
	}> = new Array<{
		id: number;
		template: TemplateRef<any>;
		visible: boolean;
	}>();
	secondColumnTemplates: Array<{
		id: number;
		template: TemplateRef<any>;
		visible: boolean;
	}> = new Array<{
		id: number;
		template: TemplateRef<any>;
		visible: boolean;
	}>();
	loadingBiConfig: boolean = false;

	private allColumnsTemplate: Array<{
		id: number;
		template: TemplateRef<any>;
		visible: boolean;
	}> = new Array<{
		id: number;
		template: TemplateRef<any>;
		visible: boolean;
	}>();
	private _destroyed$: Subject<void> = new Subject<void>();
	private _configuracoesBi: Array<ConfiguracaoBi> = new Array<ConfiguracaoBi>();

	constructor(
		private dialogService: MatDialog,
		private permissaoService: PermissaoService,
		private sanitizer: DomSanitizer,
		private httpClient: HttpClient,
		private admCoreService: AdmCoreApiBiAbaAdmService,
		private cd: ChangeDetectorRef,
		private admCoreApiGrupoColaboradorService: AdmCoreApiGrupoColaboradorService,
		private sessionService: SessionService,
		private biOrganizeService: BiMsApiOrganizeService,
		private biSidenavService: BiSidenavService,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private biApiMsConfigBiService: BiMsApiConfigBiService,
		private toastrService: ToastrService,
		private biCommonService: BiCommonService
	) {}

	ngOnInit() {
		if (!this.permissaoService.temPermissaoAdm("2.43")) {
			this.toastrService.error(
				"Seu usuário não possui permissão, procure seu administrador!"
			);
			/**
			 * Necessário pois por algum motivo somente chamar o navigate não faz mais
			 * a navegação, isso deveria funcionar como já funcionava em outros locais
			 * do sistema e estes também podem ter parado de funcionar.
			 */
			Promise.resolve().then(() => this.router.navigate(["/adm/home"]));
			return;
		}
		this._populateOptionsEmpresa();
		this.loadPermissoes();
		this._loadgruposColaboradores();
		this.onFilterFormChanges();
		this.filterForm
			.get("empresa")
			.setValue(this.sessionService.currentEmpresa.codigo);
		this.applyGlobalFilters();
		this._loadConfig();
	}

	ngAfterViewInit() {
		this._buildAllData();
		this._loadOrganizeOrder();
	}

	ngOnDestroy() {
		this._destroyed$.next();
	}

	private _loadConfig() {
		this.loadingBiConfig = true;
		this.biApiMsConfigBiService
			.findAllByEmpresa(this.filterForm.get("empresa").value)
			.pipe(takeUntil(this._destroyed$))
			.subscribe({
				next: (response: BiMsApiResponseList<ConfiguracaoBi>) => {
					this._configuracoesBi = response.content;
					this.config = response.content;
					this.loadingBiConfig = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					this.loadingBiConfig = false;
					this.cd.detectChanges();
				},
			});
	}

	private _populateOptionsEmpresa() {
		const empresasSession = [...this.sessionService.empresas];
		if (empresasSession.length > 1) {
			empresasSession.unshift({
				codigo: 0,
				nome: "Todas",
			} as EmpresaFinanceiro);
		}
		this.optionsEmpresa = empresasSession;
	}

	loadPermissoes() {
		/**
		 * Verificação antiga, como histórico para caso seja a correta.
		 *
		 * No antigo a permissão que realmente vale é a 1.12, as outras não interferem
		 *
		 * 		this.permissaoVerificacaoClientes =
		 * 			this.permissaoService.temPermissaoAdm("1.12") ||
		 * 			this.permissaoService.temPermissaoAdm("1.13") ||
		 * 			this.permissaoService.temPermissaoAdm("6.36");
		 */
		this.permissaoVerificacaoClientes =
			this.permissaoService.temPermissaoAdm("1.12");
		this.permissaoChurnPrediction =
			this.permissaoService.temPermissaoAdm("6.14");
		this.permissaoPendenciaClientes =
			this.permissaoService.temPermissaoAdm("6.15");
		this.permissaoIndiceRenovacao =
			this.permissaoService.temPermissaoAdm("6.11");
		this.permissaoConversaoVendas =
			this.permissaoService.temPermissaoAdm("6.12");
		this.permissaoMovimentacaoContratos =
			this.permissaoService.temPermissaoAdm("6.13");
		// TODO Adicionar validação para usuário que tem meta MetaFinanceiroBIControle.getUsuarioTemMeta
		this.permissaoMetasFinanceirasVendas =
			this.permissaoService.temPermissaoAdm("9.16") ||
			this.permissaoService.temPermissaoAdm("9.24");
		this.permissaoCobrancasConvenio =
			this.permissaoService.temPermissaoAdm("6.35") &&
			this.permissaoService.temPermissaoAdm("6.16");
		this.permissaoTicketMedioPlano =
			this.permissaoService.temPermissaoAdm("6.21");
		this.permissaoAulasExperimentais =
			this.permissaoService.temPermissaoAdm("6.28");
		this.permissaoControleOperacoesExcecao =
			this.permissaoService.temPermissaoAdm("6.33");
		this.permissaoInadimplencia = this.permissaoService.temPermissaoAdm("6.30");
		this.permissaoGestaoAcesso = this.permissaoService.temPermissaoAdm("6.34");
		this.permissaoIntegradoresAcesso =
			this.permissaoService.temPermissaoAdm("9.86");
		this.permissaoCicloVidaCliente =
			this.permissaoService.temPermissaoAdm("9.87");
	}

	configBI() {
		const ref = this.biSidenavService.open(BiAdmConfigComponent, {
			configuracaoBi: this._configuracoesBi,
		});

		ref.afterClosed.subscribe((config) => {
			if (config) {
				this.loadingBiConfig = true;
				this._configuracoesBi = config;
				/**
				 * O save é aqui e não no componente em si devido a um bug com a sidenav
				 * do angular que acaba que não realiza o close dentro de uma subscription
				 * Será analisado melhor futuramente.
				 */
				this.biApiMsConfigBiService.save(this._configuracoesBi).subscribe({
					next: (response) => {
						this.toastrService.success("Configurações salvas com sucesso!");
						this._configuracoesBi = response.content;
						this.config = response.content;
						this.reloadFull = true;
						this.loadingBiConfig = false;

						this.cd.detectChanges();
					},
				});
			}
		});
	}

	organizeBI() {
		const dialogRef = this.dialogService.open(BiAdmOrganizeComponent, {
			width: "100%",
			height: "100%",
			maxWidth: "unset",
			autoFocus: false,
		});
		dialogRef.afterClosed().subscribe((_) => {
			this._loadOrganizeOrder();
		});
	}

	private _loadOrganizeOrder() {
		this.biOrganizeService
			.findByLoggedUser()
			.pipe(takeUntil(this._destroyed$))
			.subscribe((response: ApiResponseSingle<ConfiguracaoSistemaUsuario>) => {
				this.firstColumnTemplates = new Array<{
					id: number;
					template: TemplateRef<any>;
					visible: boolean;
				}>();
				this.secondColumnTemplates = new Array<{
					id: number;
					template: TemplateRef<any>;
					visible: boolean;
				}>();
				const config: ConfiguracaoSistemaUsuario = response.content;
				/*
				 * NECESSÁRIO POR CONTA DO CÓDIGO DA BUILD
				 * GERA UM CÓDIGO QUE OCASIONA EM UM UNDEFINED
				 * */
				const valor = config && config.valor ? config.valor : "";
				const configSplited = valor.split(",");
				configSplited.forEach((cs) => {
					const valorSplited = cs.split("-");
					const idBi = Number(valorSplited[0]);
					const columnIndex = Number(valorSplited[1]);

					if (isNaN(idBi) || isNaN(columnIndex)) {
						return;
					}

					const data = this.allColumnsTemplate.find((d) => d.id === idBi);
					if (data) {
						switch (columnIndex) {
							case 0:
								this.firstColumnTemplates.push(data);
								break;
							case 1:
								this.secondColumnTemplates.push(data);
								break;
						}
					}
				});
				this.cd.detectChanges();
			});
	}

	onFilterFormChanges() {
		this.filterForm.get("empresa").valueChanges.subscribe((v) => {
			if (this.optionsEmpresa) {
				const empresaNome: EmpresaFinanceiro = this.optionsEmpresa.find(
					(e) => e.codigo === v
				);
				if (empresaNome) {
					/**
					 * FIXME Lucas Rezende
					 * O emit Event false aqui é devido ao componente ds3-select atualizar o valor internamente,
					 * o que ocasiona uma emição de evento duplicada caso não possua.
					 * Isso é um bug que não deveria ocorrer, será analisado com mais calma posteriormente.
					 */
					this.filterForm
						.get("empresaNome")
						.setValue(empresaNome.nome, { emitEvent: false });
				}
			}
		});

		this.filterForm.get("colaboradores").valueChanges.subscribe((v) => {
			if (this.optionsColaboradores) {
				const colaboradores = this.optionsColaboradores.filter((c) =>
					v.includes(c.value)
				);
				if (colaboradores) {
					/**
					 * FIXME Lucas Rezende
					 * O emit Event false aqui é devido ao componente ds3-select-multi atualizar o valor internamente,
					 * o que ocasiona uma emição de evento duplicada caso não possua.
					 * Isso é um bug que não deveria ocorrer, será analisado com mais calma posteriormente.
					 */
					this.filterForm.get("nomesColaboradores").setValue(
						colaboradores.map((c) => c.label),
						{ emitEvent: false }
					);
					this.filterForm
						.get("colaboradoresObj")
						.setValue(colaboradores, { emitEvent: false });
				}
			}
		});
	}

	private _loadgruposColaboradores() {
		this.admCoreApiGrupoColaboradorService
			.findByEmpresaLogada()
			.subscribe((response: ApiResponseList<GrupoColaborador>) => {
				this.gruposColaboradores = response.content;
				this.gruposColaboradores = this.gruposColaboradores.map((v) => {
					if (v.descricao === "SEM GRUPO") {
						v.codigo = -1;
					}
					return v;
				});
				const colaboradores = [];
				const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
				const colaboradoresDistinct: Map<number, Colaborador> = new Map<
					number,
					Colaborador
				>();
				this.gruposColaboradores.forEach((gc) =>
					grupoColaboradores.push(...gc.grupoColaboradorParticipantes)
				);
				grupoColaboradores.forEach((v) => {
					if (!colaboradoresDistinct.has(v.codigo)) {
						colaboradoresDistinct.set(
							v.colaboradorParticipante.codigo,
							v.colaboradorParticipante
						);
					}
				});
				colaboradoresDistinct.forEach((v) => {
					colaboradores.push({ value: v.codigo, label: v.pessoa.nome });
				});
				colaboradores.forEach((c) => this.optionsColaboradores.push(c));
				this.optionsColaboradores = this.optionsColaboradores.sort((c1, c2) => {
					if (c1.label > c2.label) {
						return 1;
					} else if (c1.label < c2.label) {
						return -1;
					} else {
						return 0;
					}
				});
				this.cd.detectChanges();
			});
	}

	private _buildAllData() {
		this.allColumnsTemplate = [
			this._dataPendenciasCliente(),
			this._dataConversaoVendas(),
			this._dataCobrancasPorConvenio(),
			this._dataMetasFinanceirasVenda(),
			this._dataTicketMedioPlanos(),
			this._dataChurnPrediction(),
			this._dataIndiceRenovacao(),
			this._dataMovimentacaoContratos(),
			this._dataControleOperacoesExcecoes(),
			this._dataVerificacaoClientes(),
			this._dataAulasExperimentais(),
			this._dataGestaoAcesso(),
			this._dataInadimplencia(),
			this._dataCicloVidaCliente(),
			this._dataIntegradoresAcesso(),
		];
	}

	private _dataPendenciasCliente() {
		return {
			id: 0,
			template: this.biPendenciasClienteTmpl,
			visible: this.permissaoPendenciaClientes,
		};
	}

	private _dataConversaoVendas() {
		return {
			id: 1,
			template: this.biConversaoVendasTmpl,
			visible: this.permissaoConversaoVendas,
		};
	}

	private _dataMetasFinanceirasVenda() {
		return {
			id: 3,
			template: this.biMetasFinanceirasVendasTmpl,
			visible: this.permissaoMetasFinanceirasVendas,
		};
	}

	private _dataTicketMedioPlanos() {
		return {
			id: 4,
			template: this.biTicketMedioPlanosTmpl,
			visible: this.permissaoTicketMedioPlano,
		};
	}

	/**
	 * Back-end BiEnum.GRUPO_RISCO
	 */
	private _dataChurnPrediction() {
		return {
			id: 5,
			template: this.biChurnPredictionTmpl,
			visible: this.permissaoChurnPrediction,
		};
	}

	private _dataIndiceRenovacao() {
		return {
			id: 6,
			template: this.biIndiceRenovacaoTmpl,
			visible: this.permissaoIndiceRenovacao,
		};
	}

	private _dataMovimentacaoContratos() {
		return {
			id: 7,
			template: this.biMovimentacaoContratosTmpl,
			visible: this.permissaoMovimentacaoContratos,
		};
	}

	private _dataCobrancasPorConvenio() {
		return {
			id: 8,
			template: this.biCobrancasConvenioTmpl,
			visible: this.permissaoCobrancasConvenio,
		};
	}

	private _dataControleOperacoesExcecoes() {
		return {
			id: 9,
			template: this.biControleOperacoesExcecoesTmpl,
			visible: this.permissaoControleOperacoesExcecao,
		};
	}

	private _dataVerificacaoClientes() {
		return {
			id: 11,
			template: this.biVerificacaoClientesTmpl,
			visible: this.permissaoVerificacaoClientes,
		};
	}

	private _dataAulasExperimentais() {
		return {
			id: 12,
			template: this.biAulasExperimentaisTmpl,
			visible: this.permissaoAulasExperimentais,
		};
	}

	private _dataGestaoAcesso() {
		return {
			id: 13,
			template: this.biGestaoAcessoTmpl,
			visible: this.permissaoGestaoAcesso,
		};
	}

	private _dataInadimplencia() {
		return {
			id: 14,
			template: this.biInadimplenciaTmpl,
			visible: this.permissaoInadimplencia,
		};
	}

	private _dataCicloVidaCliente() {
		return {
			id: 16,
			template: this.biCicloVidaClienteTmpl,
			visible: this.permissaoCicloVidaCliente,
		};
	}

	private _dataIntegradoresAcesso() {
		return {
			id: 17,
			template: this.biIntegradoresAcessoTmpl,
			visible: this.permissaoIntegradoresAcesso,
		};
	}

	applyGlobalFilters(reloadFull?: boolean) {
		this.biCommonService.updatedGlobalFilter = this.filterForm.value;
		this.biCommonService.reloadAllBI = reloadFull;
		this.reloadFull = reloadFull;
		this.cd.detectChanges();
	}
}
