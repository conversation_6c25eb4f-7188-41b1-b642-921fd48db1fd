import { ComponentType } from "@angular/cdk/portal";
import { TemplateRef } from "@angular/core";
import { MatDialogConfig } from "@angular/material/dialog/typings/dialog-config";
import { Ds3BarData, InfoData } from "ui-kit";

export interface ListItem {
	text?: string;
	auxText?: string;
	auxTextHint?: string | TemplateRef<any>;
	auxTextColor?: string;
	value?: number;
	monetaryValue?: number;
	percentage?: number;
	showPercentage?: boolean;
	monetary?: boolean;
	tooltipText?: string;
	time?: string;
	info?: string;
	date?: Date;
	dateHint?: string;
	id?: string;
	clickable?: boolean;
	modalConfig?: {
		componentType?: ComponentType<any>;
		config?: MatDialogConfig;
	};
}

interface ChartItem {
	type?: string;
	series: ApexAxisChartSeries;
	xaxis?: ApexXAxis;
	yaxis?: ApexYAxis | ApexYAxis[];
	labels?: string[];
}

export interface TabData {
	title?: string;
	diaInicio?: Date;
	diaFim?: Date;
	sections?: {
		id?: string;
		infoItens?: InfoData[];
		title?: string;
		listItens?: ListItem[];
		barItens?: Ds3BarData[];
		chartItens?: ChartItem | ChartItem[];
		chart?: { name: string; data: number[] }[];
		tableItens?: {
			title?: string;
			values?: any[];
			isActionable?: boolean;
			percentage?: boolean;
			monetary?: boolean;
		}[];
	}[];
}
