import { BiCardAulasExperimentaisModule } from "@adm/modules/bi/bi-adm/components/bi-card-aulas-experimentais/bi-card-aulas-experimentais.module";
import { BiCardChurnPredictionModule } from "@adm/modules/bi/bi-adm/components/bi-card-churn-prediction/bi-card-churn-prediction.module";
import { BiCardCobrancaConvenioModule } from "@adm/modules/bi/bi-adm/components/bi-card-cobranca-convenio/bi-card-cobranca-convenio.module";
import { BiCardConversaoVendasModule } from "@adm/modules/bi/bi-adm/components/bi-card-conversao-vendas/bi-card-conversao-vendas.module";
import { BiCardGestaoDeAcessoModule } from "@adm/modules/bi/bi-adm/components/bi-card-gestao-de-acesso/bi-card-gestao-de-acesso.module";
import { BiCardInadimplenciaModule } from "@adm/modules/bi/bi-adm/components/bi-card-inadimplencia/bi-card-inadimplencia.module";
import { BiCardMetasFinanceirasModule } from "@adm/modules/bi/bi-adm/components/bi-card-metas-financeiras/bi-card-metas-financeiras.module";
import { BiCardTicketMedioPlanosModule } from "@adm/modules/bi/bi-adm/components/bi-card-ticket-medio-planos/bi-card-ticket-medio-planos.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { BiSharedModule } from "../bi-shared/bi-shared.module";
import { BiAdmRoutingModule } from "./bi-adm-routing.module";
import { BiAdmComponent } from "./bi-adm.component";
import { BiAdmConfigComponent } from "./components/bi-adm-config/bi-adm-config.component";
import { BiAdmOrganizeComponent } from "./components/bi-adm-organize/bi-adm-organize.component";
import { ModalRankingConsultoresComponent } from "./components/bi-card-aulas-experimentais/bi-card-aulas-experimentais-ranking-consultores/modal/modal-ranking-consultores.component";
import { RelatorioChurnPredictionComponent } from "./components/bi-card-churn-prediction/modal/relatorio-churn-prediction.component";
import { BiCardCicloDeVidaDoClienteModule } from "./components/bi-card-ciclo-de-vida-do-cliente/bi-card-ciclo-de-vida-do-cliente.module";
import { BiCardControleDeOperacoesDeExcecoesModule } from "./components/bi-card-controle-de-operacoes-de-excecoes/bi-card-controle-de-operacoes-de-excecoes.module";
import { BiCardIndiceRenovacaoModule } from "./components/bi-card-indice-renovacao/bi-card-indice-renovacao.module";
import { BiCardIntegradoresDeAcessoModule } from "./components/bi-card-integradores-de-acesso/bi-card-integradores-de-acesso.module";
import { BiCardMovimentacaoContratosModule } from "./components/bi-card-movimentacao-contratos/bi-card-movimentacao-contratos.module";
import { BiCardPendenciasDeClientesModule } from "./components/bi-card-pendencias-de-clientes/bi-card-pendencias-de-clientes.module";
import { BiCardVerificacaoClientesModule } from "./components/bi-card-verificacao-clientes/bi-card-verificacao-clientes.module";
import { BiAdmChurnRateComponent } from "./pages/bi-adm-churn-rate/bi-adm-churn-rate.component";

@NgModule({
	declarations: [
		BiAdmComponent,
		BiAdmOrganizeComponent,
		BiAdmChurnRateComponent,
		ModalRankingConsultoresComponent,
		BiAdmConfigComponent,
	],
	entryComponents: [
		RelatorioChurnPredictionComponent,
		BiAdmOrganizeComponent,
		ModalRankingConsultoresComponent,
		BiAdmConfigComponent,
	],
	imports: [
		CommonModule,
		BiAdmRoutingModule,
		ReactiveFormsModule,
		BiSharedModule,
		BiCardControleDeOperacoesDeExcecoesModule,
		BiCardChurnPredictionModule,
		BiCardIndiceRenovacaoModule,
		BiCardPendenciasDeClientesModule,
		BiCardMovimentacaoContratosModule,
		BiCardVerificacaoClientesModule,
		BiCardCicloDeVidaDoClienteModule,
		BiCardIntegradoresDeAcessoModule,
		BiCardConversaoVendasModule,
		BiCardInadimplenciaModule,
		BiCardTicketMedioPlanosModule,
		BiCardAulasExperimentaisModule,
		BiCardGestaoDeAcessoModule,
		BiCardMetasFinanceirasModule,
		BiCardCobrancaConvenioModule,
	],
})
export class BiAdmModule {}
