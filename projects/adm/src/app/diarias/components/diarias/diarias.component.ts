import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	DialogAutorizacaoAcessoComponent,
	DialogService,
	LoaderService,
	PactoModalSize,
	SelectFilterParamBuilder,
} from "ui-kit";
import { ClientDiscoveryService, SessionService } from "sdk";
import { AdmRestService } from "@adm/adm-rest.service";
import {
	AdmLegadoAutorizarAcessoService,
	ZwBootDiariaService,
} from "adm-legado-api";
import { forkJoin } from "rxjs";
import moment from "moment";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import { Ds3SelectComponent } from "../../../../../../ui/src/lib/ds3/ds3-forms/ds3-select/ds3-select.component";
import { ModalEnviarLinkPagamentoComponent } from "@adm/negociacao/modal-enviar-link-pagamento/modal-enviar-link-pagamento.component";

@Component({
	selector: "adm-diarias",
	templateUrl: "./diarias.component.html",
	styleUrls: ["./diarias.component.scss"],
})
export class DiariasComponent implements OnInit {
	@ViewChild("editDialog", { static: true })
	editDialog: TemplateRef<any>;
	@ViewChild("clienteSelect", { static: false })
	clienteSelect: Ds3SelectComponent;
	produtos = [];
	modalidades = [];
	descontos = [];
	formGroup: FormGroup;
	formLancamento = new FormControl(new Date());
	cliente;
	produto;
	modalidade;
	valorProduto: number = 0.0;
	valorFinal: number = 0.0;
	descontoManualAutorizado = false;
	dialogRef: MatDialogRef<any, any>;
	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			quickSearch: term,
		};
	};

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private service: AdmCoreApiNegociacaoService,
		private clientDiscoveryService: ClientDiscoveryService,
		private loaderService: LoaderService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private readonly fb: FormBuilder,
		private sessionService: SessionService,
		private matDialog: MatDialog,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private admRestService: AdmRestService,
		private readonly diariaService: ZwBootDiariaService,
		private modal: DialogService
	) {}

	get _rest() {
		return this.admRestService;
	}

	ngOnInit() {
		this.formGroup = this.fb.group({
			cliente: new FormControl(),
			produto: new FormControl(),
			modalidade: new FormControl(),
			desconto: new FormControl(),
			valorDesconto: new FormControl(0.0),
			dataInicio: new FormControl(new Date()),
		});
		this.initCliente();
		this.formGroup.get("valorDesconto").disable();
		this.formGroup.get("modalidade").valueChanges.subscribe((modalidade) => {
			this.modalidade = modalidade;
			this.calcularValor();
		});
		this.formGroup.get("desconto").valueChanges.subscribe((desc) => {
			if (desc) {
				this.abrirAutorizacaoDesconto(true);
			} else {
				this.formGroup.get("valorDesconto").setValue(0.0);
			}
		});
		this.formGroup.get("valorDesconto").valueChanges.subscribe((desc) => {
			this.calcularValor();
		});
		this.formGroup.get("produto").valueChanges.subscribe((prod) => {
			this.produto = prod;
			this.formGroup.get("desconto").setValue(null);
			this.formGroup.get("valorDesconto").setValue(0.0);
			this.calcularValor();
		});
		forkJoin({
			produtos: this.diariaService.produtos(),
			modalidades: this.diariaService.modalidades(),
			descontos: this.diariaService.descontos(),
		}).subscribe(({ produtos, modalidades, descontos }) => {
			this.produtos = produtos.content;
			this.modalidades = modalidades.content;
			this.descontos = descontos.content;
			this.formGroup.get("cliente").valueChanges.subscribe((cli) => {
				this.cliente = cli;
				this.cd.detectChanges();
			});
		});
	}

	initCliente() {
		this.route.params.subscribe((params) => {
			if (params.matricula) {
				this.diariaService.clientes(params.matricula).subscribe((result) => {
					if (result.content && result.content.length > 0) {
						this.formGroup.get("cliente").setValue(result.content[0]);
						this.clienteSelect.selectedValue = result.content[0];
						this.cliente = result.content[0];
						this.cd.detectChanges();

						if (
							result.content[0].codEmpresa !==
							parseInt(this.sessionService.empresaId)
						) {
							this.notificationService.warning(
								"O aluno pertence a outra unidade. A diária será lançada para a unidade logada."
							);
						}
					}
				});
			}
		});
	}

	calcularValor() {
		this.valorProduto = this.produto ? this.produto.valor : 0.0;
		this.valorFinal = this.valorProduto - this.valorDesconto;
		this.valorFinal = this.valorFinal < 0 ? 0 : this.valorFinal;
		if (this.valorProduto < this.valorDesconto) {
			this.notificationService.warning(
				"O desconto não pode ser maior que o valor do produto."
			);
		}
		this.cd.detectChanges();
	}

	cancelar() {
		this.limparCampos();
	}

	receber(ignorarIrParaCaixa = false, enviarLink = false) {
		const body = {
			cliente: this.cliente.codCliente,
			produto: this.produto.codigo,
			modalidade: this.modalidade.codigo,
			dataInicio: moment(this.formGroup.get("dataInicio").value).format(
				"yyyy-MM-DD"
			),
			dataLancamento: moment(this.formLancamento.value).format("yyyy-MM-DD"),
			desconto: this.formGroup.get("desconto").value
				? this.formGroup.get("desconto").value.codigo
				: null,
			valorDesconto: this.valorDesconto,
			gerarLink: enviarLink,
		};
		this.loaderService.initForce();
		this.diariaService.salvar(body).subscribe(
			(res) => {
				if (res.content) {
					this.notificationService.success("Diária lançada com sucesso!");
					if (!this.valoresOK) {
						this.cancelar();
						this.loaderService.stopForce();
						return;
					}
					if (!ignorarIrParaCaixa && !enviarLink) {
						this.direcionarCaixa(res.content.codigo);
					} else if (ignorarIrParaCaixa) {
						this.cancelar();
						this.loaderService.stopForce();
						if (enviarLink) {
							this.abrirModalEnviarLink(res.content);
						}
					}
				}
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				} else {
					this.notificationService.error(
						"Ocorreu um erro inesperado, tente novamente."
					);
				}
				this.loaderService.stopForce();
			}
		);
	}

	abrirAutorizacaoEditarData() {
		this.abrirAutorizacao(
			"editar-data",
			"alterarLancamentoDiaria",
			"2.67 - Alterar data de lançamento da Diária"
		);
	}

	abrirAutorizacaoDesconto(aplicarDescontoPredefinido) {
		this.abrirAutorizacao(
			aplicarDescontoPredefinido ? "desconto-pre" : "desconto",
			"DescontoVendaDiaria",
			"2.46 - Desconto em produto de Venda de Diária"
		);
	}

	abrirAutorizacaoReceber() {
		this.abrirAutorizacao("receber", "VendaAvulsa", "4.11 - Venda Avulsa");
	}
	abrirAutorizacaoDeixarNoCaixa() {
		this.abrirAutorizacao("deixar", "VendaAvulsa", "4.11 - Venda Avulsa");
	}
	abrirAutorizacaoEnviarLink() {
		this.abrirAutorizacao("enviar", "VendaAvulsa", "4.11 - Venda Avulsa");
	}

	abrirModalEnviarLink(sucessResponse): void {
		const modal = this.modal.open(
			"Compartilhar link de pagamento",
			ModalEnviarLinkPagamentoComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.config = sucessResponse;
	}

	get algumCampoNaoPreenchido(): boolean {
		return !this.cliente || !this.produto || !this.modalidade;
	}

	abrirAutorizacao(acao, descPermissao, nrPermissao): void {
		if (
			this.sessionService.loggedUser.pedirSenhaFuncionalidade &&
			this.sessionService.loggedUser.pedirSenhaFuncionalidade === true
		) {
			const modalConfirmacao: any = this.matDialog.open(
				DialogAutorizacaoAcessoComponent,
				{
					disableClose: true,
					id: "autorizacao-acesso",
					autoFocus: false,
				}
			);
			modalConfirmacao.componentInstance.form
				.get("usuario")
				.setValue(this.sessionService.loggedUser.username);
			modalConfirmacao.componentInstance.cancel.subscribe((result) => {
				switch (acao) {
					case "desconto-pre":
						this.formGroup.get("desconto").setValue(null);
						break;
				}
			});
			modalConfirmacao.componentInstance.confirm.subscribe((result) => {
				this.autorizarAcessoService
					.validarPermissao(
						this.sessionService.chave,
						result.data.usuario,
						result.data.senha,
						descPermissao,
						nrPermissao,
						this.sessionService.empresaId
					)
					.subscribe(
						(response: any) => {
							result.modal.close();
							switch (acao) {
								case "editar-data":
									this.abrirDataLancamento();
									break;
								case "receber":
									this.receber(false, false);
									break;
								case "deixar":
									this.receber(true, false);
									break;
								case "enviar":
									this.receber(true, true);
									break;
								case "desconto":
									this.autorizarDesconto(false);
									break;
								case "desconto-pre":
									this.autorizarDesconto(true);
									break;
							}
						},
						(error) => {
							this.notificationService.error(error.error.meta.message);
						}
					);
			});
		} else {
			this.autorizarAcessoService
				.validarPermissaoUsuarioLogado(
					this.sessionService.chave,
					this.sessionService.loggedUser.id,
					this.sessionService.empresaId,
					descPermissao,
					nrPermissao
				)
				.subscribe(
					(response: any) => {
						switch (acao) {
							case "editar-data":
								this.abrirDataLancamento();
								break;
							case "receber":
								this.receber(false, false);
								break;
							case "deixar":
								this.receber(true, false);
								break;
							case "enviar":
								this.receber(true, true);
								break;
							case "desconto":
								this.autorizarDesconto(false);
								break;
							case "desconto-pre":
								this.autorizarDesconto(true);
								break;
						}
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		}
	}

	abrirDataLancamento() {
		this.dialogRef = this.matDialog.open(this.editDialog, {
			disableClose: true,
			id: "editar",
			autoFocus: false,
			width: "800px",
			panelClass: "editar",
		});
	}

	get dataLancamentoStr(): string {
		return moment(this.formLancamento.value).format("DD/MM/yyyy");
	}

	private limparCampos() {
		this.formGroup.get("cliente").setValue(null);
		this.formGroup.get("produto").setValue(null);
		this.formGroup.get("modalidade").setValue(null);
		this.formGroup.get("desconto").setValue(null);
		this.formGroup.get("valorDesconto").setValue(0.0);
		this.formGroup.get("dataInicio").setValue(new Date());
		this.cliente = null;
		this.produto = null;
		this.modalidade = null;
		this.valorProduto = 0.0;
		this.valorFinal = 0.0;
		this.cd.detectChanges();
	}

	private direcionarCaixa(idDiaria) {
		this.service.recursoHabilitado("CAIXA_ABERTO").subscribe({
			next: (responseV) => {
				if (responseV) {
					this.acessarCaixaEmAbertoNovo(idDiaria);
				} else {
					this.acessarCaixaEmAbertoAntigo(idDiaria);
				}
			},
			error: (err) => {
				this.acessarCaixaEmAbertoAntigo(idDiaria);
			},
		});
	}

	acessarCaixaEmAbertoNovo(idDiaria) {
		this.loaderService.stopForce();
		this.router.navigateByUrl(
			"/adm/caixa-em-aberto/receber-parcelas/pagamentoDiaria_" + idDiaria
		);
	}

	acessarCaixaEmAbertoAntigo(id) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, null)
			.subscribe((result) => {
				window.open(result + "&urlRedirect=pagamentoDiaria_" + id, "_self");
			});
	}

	autorizarDesconto(aplicarDescontoPredefinido) {
		if (aplicarDescontoPredefinido) {
			const desc = this.formGroup.get("desconto").value;
			if (desc && this.produto) {
				this.formGroup
					.get("valorDesconto")
					.setValue(
						desc.tipo === "PE"
							? this.produto.valor * (desc.valor / 100.0)
							: desc.valor
					);
			}
		}
		this.descontoManualAutorizado = true;
		this.formGroup.get("valorDesconto").enable();
		this.cd.detectChanges();
	}

	limparDesconto() {
		this.descontoManualAutorizado = false;
		this.formGroup.get("desconto").setValue(null);
		this.formGroup.get("valorDesconto").setValue(0.0);
		this.formGroup.get("valorDesconto").disable();
		this.calcularValor();
	}

	get valorDesconto(): number {
		return this.formGroup.get("valorDesconto").value;
	}

	get valoresOK(): boolean {
		return (
			(this.produto && this.produto.valor ? this.produto.valor : 0.0) >
			this.valorDesconto
		);
	}

	cancelarDataLancamento() {
		if (!moment(this.formLancamento.value).isBefore(moment())) {
			this.formLancamento = new FormControl(new Date());
		}
		this.dialogRef.close("cancelar");
	}

	salvarData() {
		if (moment(this.formLancamento.value).isBefore(moment())) {
			this.dialogRef.close("salvar");
		} else {
			this.formLancamento = new FormControl(new Date());
			this.notificationService.error("Data de lançamento não pode ser futura");
		}
	}
}
