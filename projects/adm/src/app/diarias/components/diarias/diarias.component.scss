@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var";

ds3-form-field {
	display: block;
	margin-bottom: 16px;
}
.info-top {
	background-color: #fafafa;
	padding: 8px 16px 8px 16px;
	border-radius: 8px;
	margin-bottom: 16px;
	display: flex;
	justify-content: space-between;

	div {
		padding-top: 3px;
	}

	span {
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: $supportGray07;
		border: 1px solid $supportGray03;
		border-radius: 20px;
		background-color: #ffffff;
		padding: 2px 12px;
		margin-right: 10px;
	}
}
.info {
	border-radius: 8px;
	background-color: #ffffff;
	padding: 12px 16px;
	width: 100%;
	margin-top: 16px;
	height: 102px;
	border: 1px solid #d7d8db;
	display: grid;
	grid-template-columns: 1fr auto auto auto;
	gap: 10px;

	.foto-dados-aluno {
		display: flex;
		padding-left: 0px;
	}

	div {
		display: grid;
		align-items: center;
		text-align: left;
		padding-left: 40px;
	}

	.dados-aluno {
		text-align: left;
		font-family: "Nunito Sans";
		font-style: normal;
		font-weight: 400;
		font-size: 12px;
		line-height: 16px;
		align-items: center;
		color: #80858c;
		padding-left: 12px;

		.nome-aluno {
			text-transform: capitalize;
			ffont-family: "Poppins";
			font-style: normal;
			font-weight: 600;
			font-size: 14px;
			line-height: 125%;
			letter-spacing: 0.25px;
			color: #383b3e;
			flex: none;
			order: 0;
			flex-grow: 0;
		}

		i {
			color: #1e60fa;
			font-size: 12px;
			cursor: pointer;
		}
	}

	.valores {
		color: #80858c;
		font-weight: 600;
		font-size: 22px;

		&.total {
			color: #19a45f;
		}
	}
}

.itens-tooltip {
	display: grid;
	grid-template-columns: 1fr 1fr;
	font-size: 18px;

	.valor-item {
		text-align: right;
	}

	.nome-item {
		text-transform: capitalize;
	}
}

.space-16 {
	margin-right: 16px;
}

.acoes {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex-wrap: nowrap;
	gap: 16px;
	margin: 16px 0;

	button > i {
		padding-right: 8px;
	}
}

::ng-deep#editar,
::ng-deep#envioEmail,
::ng-deep#finalizar {
	padding: 0;
	border-radius: 12px;

	[matDialogTitle] {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid var(--color-support-gray-3);
		padding: 16px 16px 8px 16px;

		span {
			@extend .pct-title4;
			color: var(--color-support-black-4);
		}
	}

	[matDialogContent] {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		margin: 20px 20px 0 20px;
		gap: 16px;

		.metade {
			width: calc(50% - 8px);
		}

		.inteiro {
			width: 100%;
		}

		.fixed-toast {
			padding: 12px 12px 3px 11px;
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			background-color: var(--color-feedback-alert-1);
			color: var(--color-feedback-alert-3);

			&-body {
				@extend .pct-body1;
				width: 100%;
			}

			button {
				background-color: unset;
			}
		}

		.encerramento {
			display: flex;
			width: 100%;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			gap: 16px;
			margin-bottom: 20px;

			img {
				height: 112px;
				width: 112px;
			}

			p {
				@extend .pct-title4;
				color: var(--color-typography-default-title);
			}

			span {
				@extend .pct-body1;
				color: var(--color-typography-default-text);
			}
		}
	}

	[matDialogActions] {
		display: flex;
		gap: 30px;
		padding: 0 20px 20px 20px;

		.btn-finalizar {
			align-items: center;
			justify-content: center;
			display: flex;
			width: 100%;
			gap: 30px;
		}

		.div-inferior-btn {
			width: 100%;

			.div-imprimir-email {
				display: grid;
				grid-template-columns: 1fr 1fr;
				margin-bottom: 20px;
				gap: 20px;

				.icon-recibo-rece {
					padding-right: 5px;
				}
			}

			.div-botoes {
				display: flex;
				gap: 20px;

				.btn-recebimento {
					width: 100%;
				}
			}
		}

		.btn-editar {
			align-items: end;
			justify-content: end;
			display: flex;
			width: 100%;
			gap: 30px;
			margin-top: 20px;
		}
	}
}
.btn-control-descontos {
	.pct {
		font-size: 16px;
		font-weight: 400;
		cursor: pointer;
		margin-left: 5px;
	}
}
