<adm-layout
	i18n-modulo="@@diarias:modulo"
	i18n-pageTitle="@@diarias:title"
	modulo="Administrativo"
	pageTitle="Diária">
	<pacto-cat-card-plain>
		<form>
			<div class="table-wrapper pacto-shadow">
				<div class="info-top">
					<div>
						<span>Data de lançamento: {{ dataLancamentoStr }}</span>
					</div>

					<button
						ds3-text-button
						id="receber-parcelas-editar"
						(click)="abrirAutorizacaoEditarData()">
						Editar
					</button>
				</div>

				<div class="row">
					<div class="col-md-4">
						<ds3-form-field>
							<ds3-field-label>Cliente</ds3-field-label>
							<ds3-select
								#clienteSelect
								id="select-codigo-cliente"
								[valueKey]="'codCliente'"
								[useFullOption]="true"
								[nameKey]="'nome'"
								[emptyMsg]="'Digite para buscar clientes'"
								[placeholder]="'Busque o cliente pelo nome ou matrícula'"
								[paramBuilder]="selectBuilder"
								[endpointUrl]="_rest.buildFullUrlZwBack('diaria/clientes')"
								[formControl]="formGroup.get('cliente')"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
					<div class="col-md-4">
						<ds3-form-field>
							<ds3-field-label>Produto</ds3-field-label>
							<ds3-select
								id="select-codigo-produto"
								[options]="produtos"
								[useFullOption]="true"
								[placeholder]="'Selecione o produto aqui'"
								[valueKey]="'codigo'"
								[nameKey]="'descricao'"
								[formControl]="formGroup.get('produto')"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
					<div class="col-md-4">
						<ds3-form-field>
							<ds3-field-label>Modalidade</ds3-field-label>
							<ds3-select
								id="select-codigo-produto"
								[options]="modalidades"
								[useFullOption]="true"
								[placeholder]="'Selecione a modalidade aqui'"
								[valueKey]="'codigo'"
								[nameKey]="'modalidade'"
								[formControl]="formGroup.get('modalidade')"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
				</div>

				<div class="row">
					<div class="col-md-4">
						<ds3-form-field>
							<ds3-field-label>Desconto</ds3-field-label>
							<ds3-select
								id="select-codigo-produto"
								[useFullOption]="true"
								[options]="descontos"
								[placeholder]="'Selecione o desconto predefinido aqui'"
								[valueKey]="'codigo'"
								[remove]="true"
								[nameKey]="'descricao'"
								[formControl]="formGroup.get('desconto')"
								ds3Input></ds3-select>
						</ds3-form-field>
					</div>
					<div class="col-md-4">
						<ds3-form-field>
							<ds3-field-label>Valor do desconto</ds3-field-label>
							<div></div>
							<input
								ds3Input
								[formControl]="formGroup.get('valorDesconto')"
								currencyMask
								[options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" />
							<span ds3Suffix class="btn-control-descontos">
								<i
									*ngIf="!descontoManualAutorizado"
									(click)="abrirAutorizacaoDesconto()"
									class="pct pct-key"></i>
								<i
									*ngIf="valorDesconto > 0.0"
									(click)="limparDesconto()"
									class="pct pct-x-circle"></i>
							</span>
						</ds3-form-field>
					</div>
					<div class="col-md-4">
						<ds3-form-field>
							<ds3-field-label>Data de início</ds3-field-label>
							<ds3-input-date
								id="freepass-data-inicio"
								dateType="datepicker"
								[position]="'middle-left'"
								[control]="formGroup.get('dataInicio')"
								ds3Input></ds3-input-date>
						</ds3-form-field>
					</div>
				</div>
			</div>
		</form>

		<div class="info">
			<div class="foto-dados-aluno">
				<pacto-cat-person-avatar
					[diameter]="64"
					[uri]="
						cliente && cliente.urlFoto
							? cliente.urlFoto
							: 'pacto-ui/images/default-user-icon.png'
					"></pacto-cat-person-avatar>
				<div class="dados-aluno">
					<span class="nome-aluno">
						{{ cliente?.nome }}
					</span>
				</div>
			</div>

			<div>
				<label>Produtos</label>
				<span class="valores">{{ valorProduto | currency : "R$" }}</span>
			</div>
			<div>
				<label>Desconto</label>
				<span *ngIf="valorDesconto > 0" class="valores">
					{{ valorDesconto | currency : "R$" }}
				</span>
				<span *ngIf="valorDesconto === 0" class="valores">-</span>
			</div>
			<div>
				<label>Total</label>
				<span class="valores total">{{ valorFinal | currency : "R$" }}</span>
			</div>
		</div>

		<div class="acoes">
			<div>
				<button
					size="lg"
					ds3-outlined-button
					[disabled]="algumCampoNaoPreenchido"
					id="btn-enviar-link-pagamento"
					(click)="abrirAutorizacaoEnviarLink()">
					Enviar link pagamento
				</button>
			</div>
			<div>
				<button
					size="lg"
					ds3-outlined-button
					[disabled]="algumCampoNaoPreenchido"
					id="btn-deixar-no-caixa-em-aberto"
					(click)="abrirAutorizacaoDeixarNoCaixa()">
					Deixar no caixa em aberto
				</button>
			</div>
			<div>
				<button
					size="lg"
					ds3-flat-button
					[disabled]="algumCampoNaoPreenchido"
					(click)="abrirAutorizacaoReceber()"
					id="btn-receber-receber">
					<i class="pct pct-dollar-sign"></i>
					Receber
				</button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<ng-template #editDialog>
	<div class="dialog-container">
		<div matDialogTitle>
			<span>Editar data de lançamento</span>
			<button
				ds3-icon-button
				id="editar-datalancamento-btn-fechar"
				(click)="dialogRef.close('x')">
				<i class="pct pct-x"></i>
			</button>
		</div>
		<div matDialogContent>
			<ds3-form-field class="metade">
				<ds3-field-label>Data do lançamento</ds3-field-label>
				<ds3-input-date
					ds3Input
					id="receber-parcelas-editar-data-recebimento"
					[control]="formLancamento"></ds3-input-date>
			</ds3-form-field>
		</div>

		<div matDialogActions>
			<div class="btn-editar">
				<button
					ds3-text-button
					id="receber-parcelas-editar-btn-cancelar"
					(click)="cancelarDataLancamento()">
					Cancelar
				</button>
				<button
					ds3-flat-button
					id="receber-parcelas-editar-btn-salvar"
					(click)="salvarData()">
					Salvar
				</button>
			</div>
		</div>
	</div>
</ng-template>
