import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { OamdService, SessionService } from "sdk";
import { DialogService } from "ui-kit";

@Component({
	selector: "adm-modal-recurso-padrao-feedback",
	templateUrl: "./modal-recurso-padrao-feedback.component.html",
	styleUrls: ["./modal-recurso-padrao-feedback.component.scss"],
})
export class ModalRecursoPadraoFeedbackComponent implements OnInit {
	dataDesativar;
	recurso;
	@Output() acao: EventEmitter<any> = new EventEmitter();
	form: FormGroup = new FormGroup({
		feedback: new FormControl(""),
	});

	constructor(
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private oamdService: OamdService,
		private dialogService: DialogService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	cancelar() {
		this.dialog.close();
	}

	enviarFeedback() {
		const body = {
			chave: this.sessionService.chave,
			usuario: this.sessionService.loggedUser.id,
			username: this.sessionService.loggedUser.username,
			recurso: this.recurso,
			feedback: this.form.get("feedback").value,
		};
		this.oamdService
			.feedbackTelaNova(
				this.sessionService.chave,
				this.sessionService.token,
				body
			)
			.subscribe(
				(response) => {
					console.log(response);
				},
				(httpResponseError) => {
					console.log(httpResponseError);
				}
			);
		this.cancelar();
		this.acao.emit("VOLTAR");
	}
}
