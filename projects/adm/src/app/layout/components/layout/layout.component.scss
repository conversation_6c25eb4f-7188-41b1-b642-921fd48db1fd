@import "src/assets/scss/pacto/plataforma-import";

.layout-subtitle {
	display: flex;
	flex-wrap: wrap;
}

.texto-subtitulo {
	position: relative;
	flex-grow: 1;
}

.content-wrapper {
	width: 100%;
	display: flex;
	margin-bottom: 30px;
}

.icon-voltar {
	padding: 4px;
	cursor: pointer;
	margin-right: 16px;
	align-items: center;
	display: flex;
}

.content-title {
	width: 100%;

	.recurso2 {
		display: grid;
		grid-template-columns: 1fr 1fr;
		align-items: center;

		:first-child {
			justify-self: start; /* Alinhamento esquerdo */
		}

		:last-child {
			justify-self: end; /* Alinhamento direito */
		}
	}

	.recurso3 {
		display: grid;
		grid-template-columns: 1fr 2fr 1fr;
		align-items: center;

		:first-child {
			justify-self: start; /* Alinhamento esquerdo */
		}

		:last-child {
			justify-self: end; /* Alinhamento direito */
		}
	}
}

.margin-left-32 {
	margin-left: 32px;
}

.div-topo-feeback {
	display: flex;
	justify-content: center;

	.toast-beta {
		background-color: #fafafa;
		border-radius: 5px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 8px;
		gap: 8px;

		.toast-text {
			//styleName: Overline/2;
			font-family: Nunito Sans;
			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
			color: #797d86;
			display: flex;
			align-items: center;
			gap: 8px;

			.icone {
				font-size: 18px;
			}

			strong {
				//styleName: Button/Default/2;
				font-family: Poppins;
				font-size: 12px;
				font-weight: 600;
				line-height: 12px;
				letter-spacing: 0.25px;
				text-align: left;
			}
		}

		.toast-button {
		}
	}
}
