import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Inject,
	Input,
	LOCALE_ID,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { ClientDiscoveryService, environment, SessionService } from "sdk";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import { SnotifyService } from "ng-snotify";
import { DialogService, TraducoesXinglingComponent } from "ui-kit";
import { RecursosMenuADM } from "../../../recursosMenu";

let goBackUniqueId = 0;

@Component({
	selector: "adm-layout",
	templateUrl: "./layout.component.html",
	styleUrls: ["./layout.component.scss"],
})
export class LayoutComponent implements OnInit {
	@Input() showAdd: boolean;
	@Input() hideBackButton: boolean;
	@Input() addBtnLabel = "Novo";
	@Input() showBtnPlanoAntigo = false;
	@Input() uriVersaoAntiga: string;
	@Input() recurso: string;
	@Input() forcarTrue: boolean = false;
	@Input() empresaId: string;
	@Output() add: EventEmitter<any> = new EventEmitter<any>();
	@Input() showConfirmDialog = false;
	@Input() pageTitle;
	@Input() subtitle;
	@Input() modulo;
	@Input() formVersaoBeta;
	recursoTelaAlunoPadraoEmpresa: boolean = true;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Output() goBack: EventEmitter<any> = new EventEmitter<any>();
	@Output() changeNovaVersao: EventEmitter<boolean> = new EventEmitter();
	showLoader = false;
	idGoBack = `go-back-${goBackUniqueId++}`;

	constructor(
		private clientDiscoveryService: ClientDiscoveryService,
		private session: SessionService,
		private cd: ChangeDetectorRef,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private notificationService: SnotifyService,
		private dialogService: DialogService,
		@Inject(LOCALE_ID) private locale
	) {}

	ngOnInit() {
		this.carregarRecursoPadraoEmpresa();
	}

	acessarPlanoAntigo() {
		this.showLoader = true;
		this.clientDiscoveryService
			.linkZw(this.session.usuarioOamd, null)
			.subscribe(
				(result) => {
					result += `&urlRedirect=uriPlano`;
					window.open(
						result,
						"Plano",
						"left=0, screenX=0, top=0, screenY=0, width=800, height=594 dependent=yes, menubar=no," +
							" toolbar=no, resizable=yes , scrollbars=yes"
					);
					this.showLoader = false;
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					this.showLoader = false;
					this.cd.detectChanges();
					if (
						httpResponseError.error.meta.message ===
						"Erro ao recuperar usuário!"
					) {
						const urlMap = this.clientDiscoveryService.getUrlMap();
						let loginUrl;
						if (
							environment.newLoginEnabled &&
							urlMap.loginFrontUrl !== undefined &&
							urlMap.loginFrontUrl !== ""
						) {
							loginUrl = `${urlMap.loginFrontUrl}/${this.locale}/logout`;
						} else {
							loginUrl = urlMap.loginAppUrl;
							if (this.session.chave) {
								loginUrl += `${urlMap}/${this.session.chave}`;
							}
						}
						location.replace(`${loginUrl}`);
					} else {
						this.notificationService.error(
							"Ocorreu um erro ao tentar acessar o antigo plano! Tente novamente em isntantes!"
						);
					}
				}
			);
	}

	openGobackDialog() {
		if (this.showConfirmDialog) {
			const dialogRef = this.dialogService.confirm(
				this.traducoes.getLabel("confirm-goback-title"),
				this.traducoes.getLabel("confirm-goback-body"),
				this.traducoes.getLabel("confirm-goback-proceed")
			);
			dialogRef.result
				.then((result) => {
					this.goBack.emit();
				})
				.catch((err) => {});
		} else {
			this.goBack.emit();
		}
	}

	acessarVersaoAntiga() {
		this.showLoader = true;
		this.clientDiscoveryService
			.linkZw(
				this.session.usuarioOamd,
				this.empresaId && this.empresaId !== "" && this.empresaId !== "0"
					? this.empresaId
					: null
			)
			.subscribe(
				(result) => {
					result += `&urlRedirect=${this.uriVersaoAntiga}`;
					if (this.uriVersaoAntiga === "uriTurma") {
						window.open(
							result,
							"Turma",
							"left=0, screenX=0, top=0, screenY=0, width=800, height=594 dependent=yes, menubar=no," +
								" toolbar=no, resizable=yes , scrollbars=yes"
						);
						this.session.notificarRecursoEmpresa(
							RecursosMenuADM.ANTIGA_TELA_TURMA
						);
					} else {
						window.open(result, "_self");
					}
					this.showLoader = false;
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					this.showLoader = false;
					this.cd.detectChanges();
					if (
						httpResponseError.error.meta.message ===
						"Erro ao recuperar usuário!"
					) {
						const chave = this.session.chave;
						const loginUrl =
							this.clientDiscoveryService.getUrlMap().loginAppUrl;
						location.replace(`${loginUrl}/${chave}`);
					} else {
						this.notificationService.error(
							"Ocorreu um erro ao tentar acessar a versão antiga! Tente novamente em isntantes!"
						);
					}
				}
			);
	}

	change(state) {
		this.changeNovaVersao.emit(state);
	}

	openFeedbackForm() {
		window.open(this.formVersaoBeta, "_blank");
	}

	getMsgVersaoBeta() {
		if (this.recurso && this.formVersaoBeta && this.formVersaoBeta.length > 0) {
			if (
				this.recurso === "CAIXA_ABERTO" ||
				this.recurso === "INCLUIR_CLIENTE"
			) {
				return "Sentiu falta de algo, teve dificuldade ou percebeu algum problema?";
			} else {
				return "Você está experimentando a nova versão aproveite e teste as melhorias. Você poderá nos enviar feedbacks e ajudar a construir um sistema melhor para o seu dia a dia";
			}
		}
		return "Sentiu falta de algo, teve dificuldade ou percebeu algum problema?";
	}

	carregarRecursoPadraoEmpresa() {
		if (this.recurso && this.recurso.length > 0) {
			this.admCoreApiNegociacaoService
				.recursoPadraoEmpresa(this.recurso)
				.subscribe(
					(response) => {
						this.recursoTelaAlunoPadraoEmpresa = response;
					},
					(httpErrorResponse) => {
						console.log(httpErrorResponse);
						this.recursoTelaAlunoPadraoEmpresa = false;
					}
				);
		} else {
			this.recursoTelaAlunoPadraoEmpresa = false;
		}
	}

	apresentarFeedback() {
		if (this.recursoTelaAlunoPadraoEmpresa) {
			return false;
		} else {
			return this.formVersaoBeta && this.formVersaoBeta.length > 0;
		}
	}

	classDiv(): string {
		if (this.apresentarFeedback() && this.recurso && this.recurso.length > 0) {
			return "type-h3-bold recurso3";
		} else if (
			this.apresentarFeedback() ||
			(this.recurso && this.recurso.length > 0)
		) {
			return "type-h3-bold recurso2";
		} else {
			return "type-h3-bold";
		}
	}
}
