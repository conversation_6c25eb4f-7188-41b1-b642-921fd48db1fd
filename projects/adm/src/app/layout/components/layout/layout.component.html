<pacto-cat-layout-v2>
	<!-- <div *ngIf="modulo" class="type-p-small cor-cinza05">
		{{ traducoes.getLabel("actual-module") }} {{ modulo }}
	</div> -->

	<div class="content-wrapper">
		<div
			(click)="openGobackDialog()"
			*ngIf="!hideBackButton"
			[id]="idGoBack"
			class="type-h4-bold icon-voltar">
			<i class="pct pct-arrow-left"></i>
		</div>
		<div class="content-title">
			<div class="{{ classDiv() }}">
				<span>{{ pageTitle }}</span>

				<div class="div-topo-feeback" *ngIf="apresentarFeedback()">
					<div class="toast-beta">
						<div class="toast-text">
							<i class="pct pct-info icone"></i>
							<span class="text-topo-feedback">{{ getMsgVersaoBeta() }}</span>
						</div>
						<div class="toast-button">
							<pacto-cat-button
								label="Envie seu feedback"
								type="OUTLINE_NO_BORDER"
								size="LARGE"
								(click)="openFeedbackForm()"></pacto-cat-button>
						</div>
					</div>
				</div>

				<adm-recurso-padrao
					(change)="change($event)"
					*ngIf="recurso"
					[forcarTrue]="forcarTrue"
					[recurso]="recurso"></adm-recurso-padrao>
			</div>
			<div class="layout-subtitle">
				<div class="type-p-small cor-cinza05 texto-subtitulo">
					{{ subtitle }}
				</div>
				<div style="margin-left: auto">
					<pacto-cat-button
						(click)="acessarVersaoAntiga()"
						*ngIf="uriVersaoAntiga"
						[icon]="'pct pct-repeat'"
						[label]="
							showLoader
								? traducoes.getLabel('btn-versao-antiga-aguarde')
								: traducoes.getLabel('btn-versao-antiga')
						"
						[size]="'LARGE'"
						[type]="'OUTLINE'"
						style="margin-right: 15px"></pacto-cat-button>
					<pacto-cat-button
						(click)="acessarPlanoAntigo()"
						*ngIf="showBtnPlanoAntigo"
						[icon]="'pct pct-repeat'"
						[label]="
							showLoader
								? traducoes.getLabel('btn-versao-antiga-aguarde')
								: traducoes.getLabel('btn-versao-antiga')
						"
						[size]="'LARGE'"
						[type]="'OUTLINE'"
						id="btn-show-plano-antigo"
						style="margin-right: 15px"></pacto-cat-button>
					<pacto-cat-button
						(click)="add.emit()"
						*ngIf="showAdd"
						[icon]="'pct pct-plus-circle'"
						[label]="addBtnLabel"
						[size]="'LARGE'"
						[type]="'PRIMARY'"></pacto-cat-button>
				</div>
			</div>
		</div>
	</div>
	<ng-content></ng-content>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@adm:btn-versao-antiga-aguarde"
		xingling="btn-versao-antiga-aguarde">
		Aguarde...
	</span>
	<span i18n="@@adm:btn-versa-antiga" xingling="btn-versao-antiga">
		Ir para a versão antiga
	</span>
	<span i18n="@@adm:actual-module" xingling="actual-module">Você está em:</span>
	<span i18n="@@confirm-goback-title" xingling="confirm-goback-title">
		Confirmar retorno
	</span>
	<span i18n="@@confirm-goback-body" xingling="confirm-goback-body">
		Ao confirmar as alterações serão perdidas, deseja prosseguir?
	</span>
	<span i18n="@@confirm-goback-proceed" xingling="confirm-goback-proceed">
		Prosseguir
	</span>
</pacto-traducoes-xingling>
