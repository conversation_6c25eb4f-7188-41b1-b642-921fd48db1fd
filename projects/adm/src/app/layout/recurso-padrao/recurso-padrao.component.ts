import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import { ModalRecursoPadraoFeedbackComponent } from "../modal-recurso-padrao-feedback/modal-recurso-padrao-feedback.component";
import { ModalService } from "@base-core/modal/modal.service";
import { PactoModalSize } from "ui-kit";
import { OamdService, SessionService } from "sdk";

@Component({
	selector: "adm-recurso-padrao",
	templateUrl: "./recurso-padrao.component.html",
	styleUrls: ["./recurso-padrao.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RecursoPadraoComponent implements OnInit {
	@Input() recurso: string;
	habilitado = false;
	padraoEmpresa = true;
	@Input() forcarTrue = false;
	bloquearClique: boolean = false;
	@Output() change: EventEmitter<any> = new EventEmitter();

	constructor(
		private service: AdmCoreApiNegociacaoService,
		private cd: ChangeDetectorRef,
		private oamdService: OamdService,
		private sessionService: SessionService,
		private pactoModal: ModalService
	) {}

	ngOnInit() {
		if (this.forcarTrue) {
			this.habilitado = true;
			this.padraoEmpresa = true;
		} else {
			this.verificarRecurso();
		}
	}

	alternar() {
		if (this.recurso === "CAIXA_ABERTO") {
			this.oamdService
				.dataDesativarTelaNova(
					this.sessionService.chave,
					this.recurso,
					this.sessionService.token
				)
				.subscribe(
					(response) => {
						if (response.content) {
							const dialogRef = this.pactoModal.open(
								"Acessar versão antiga",
								ModalRecursoPadraoFeedbackComponent,
								PactoModalSize.LARGE
							);
							dialogRef.componentInstance.recurso = this.recurso;
							dialogRef.componentInstance.dataDesativar = response.content;
							dialogRef.componentInstance.acao.subscribe((res) => {
								if (res === "VOLTAR") {
									this.acaoClick();
								}
							});
						} else {
							this.acaoClick();
						}
					},
					(httpResponseError) => {
						console.log(httpResponseError);
						this.acaoClick();
					}
				);
		} else {
			this.acaoClick();
		}
	}

	acaoClick() {
		if (!this.bloquearClique) {
			this.bloquearClique = true;

			if (this.forcarTrue) {
				this.change.emit(this.habilitado);
				return;
			}

			const novoEstado = !this.habilitado;
			if (novoEstado !== this.habilitado) {
				this.habilitado = novoEstado;
				this.service
					.alterarRecurso(this.recurso, this.habilitado, "NZW")
					.subscribe((result) => {
						this.change.emit("voltar_antiga");
					});
				this.cd.detectChanges();
			}
		}
	}

	verificarRecurso() {
		this.service.recursoPadraoEmpresa(this.recurso).subscribe(
			(response) => {
				this.padraoEmpresa = response;
				this.cd.detectChanges();
			},
			(httpErrorResponse) => {
				console.log(httpErrorResponse);
				this.padraoEmpresa = false;
				this.cd.detectChanges();
			}
		);
		this.service.recursoHabilitado(this.recurso).subscribe((result) => {
			this.habilitado = result;
			this.cd.detectChanges();
		});
	}
}
