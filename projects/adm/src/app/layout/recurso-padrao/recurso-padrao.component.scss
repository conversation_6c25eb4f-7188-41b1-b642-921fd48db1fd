* {
	cursor: pointer;
}

.padrao {
	display: flex;
	color: #797d86;
	font-size: 16px;
	font-weight: 400;
	line-height: 16px;

	label {
		margin-left: 8px;
	}

	.switch {
		position: relative;
		display: inline-block;
		width: 30px;
		height: 17px;
	}

	/* Hide default HTML checkbox */
	.switch input {
		opacity: 0;
		width: 0;
		height: 0;
	}

	/* The slider */
	.slider {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		-webkit-transition: 0.4s;
		transition: 0.4s;
	}

	.slider:before {
		position: absolute;
		content: "";
		height: 13px;
		width: 13px;
		left: 2px;
		bottom: 2px;
		background-color: white;
		-webkit-transition: 0.4s;
		transition: 0.4s;
	}

	.checked + .slider {
		background-color: #1e60fa;
	}

	input:focus + .slider {
		box-shadow: 0 0 1px #1e60fa;
	}

	.checked + .slider:before {
		-webkit-transform: translateX(13px);
		-ms-transform: translateX(13px);
		transform: translateX(13px);
	}

	/* Rounded sliders */
	.slider.round {
		border-radius: 17px;
	}

	.slider.round:before {
		border-radius: 50%;
	}
}
