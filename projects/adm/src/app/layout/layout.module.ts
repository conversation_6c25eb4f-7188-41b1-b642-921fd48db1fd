import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { UiModule } from "ui-kit";
import { LayoutComponent } from "./components/layout/layout.component";
import { RecursoPadraoComponent } from "./recurso-padrao/recurso-padrao.component";
import { ModalRecursoPadraoFeedbackComponent } from "./modal-recurso-padrao-feedback/modal-recurso-padrao-feedback.component";

@NgModule({
	declarations: [
		LayoutComponent,
		RecursoPadraoComponent,
		ModalRecursoPadraoFeedbackComponent,
	],
	imports: [CommonModule, UiModule, ReactiveFormsModule],
	exports: [LayoutComponent, ReactiveFormsModule],
	entryComponents: [ModalRecursoPadraoFeedbackComponent],
})
export class LayoutModule {}
