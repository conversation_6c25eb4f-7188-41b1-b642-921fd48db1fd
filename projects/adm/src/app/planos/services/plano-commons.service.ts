import { Injectable } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Plano, PlanoModalidade, TipoPlano } from "../plano.model";
import { ActivatedRoute, Router } from "@angular/router";
import { PlanoApiProdutoService } from "plano-api";
import { PlanoStateService } from "../components/cadastrar-plano/plano-state.service";
import { PlanoService } from "../components/cadastrar-plano/plano.service";
import { SnotifyService } from "ng-snotify";
import { OamdService, SessionService } from "sdk";
import { PerfilAcessoRecursoNome } from "../../perfil-acesso/perfil-acesso-recurso.model";
import { TraducoesXinglingComponent } from "ui-kit";
import { PlanoConfiguracaoSistema } from "./plano-configuracao-sistema.model";
import { Observable } from "rxjs";

@Injectable({
	providedIn: "root",
})
export class PlanoCommonsService {
	redeEmpresa: any;

	get recurso() {
		return this.sessionService.recursos.get(PerfilAcessoRecursoNome.PLANO);
	}

	constructor(
		private router: Router,
		public produtoService: PlanoApiProdutoService,
		private planoStateService: PlanoStateService,
		private notificationService: SnotifyService,
		private sessionService: SessionService,
		private oamdService: OamdService,
		private planoService: PlanoService
	) {
		this.oamdService
			.findRedeEmpresa(this.sessionService.chave)
			.subscribe((result) => {
				this.redeEmpresa = result.return;
			});
	}

	get isPertenceRedeEmpresa(): boolean {
		return this.redeEmpresa !== undefined && this.redeEmpresa !== null
			? this.redeEmpresa.id > 0
			: false;
	}

	formDadosBasicos(tipoPlano: TipoPlano) {
		const form = new FormGroup(
			{
				descricao: new FormControl("", [Validators.required]),
				tipoPlano: new FormControl(tipoPlano),
				vigenciaDe: new FormControl("", [Validators.required]),
				ingressoAte: new FormControl("", [Validators.required]),
				vigenciaAte: new FormControl("", [Validators.required]),
				produtoContrato: new FormControl(),
			},
			{ updateOn: "change" }
		);

		if (tipoPlano === TipoPlano.PLANO_CREDITO) {
			form.setControl("creditoSessao", new FormControl());
			form.setControl("creditoTreinoNaoCumulativo", new FormControl(""));
		}

		return form;
	}

	updateStateAferFomrInit(plano: Plano, form: FormGroup) {
		plano = form.getRawValue();
		this.planoStateService.updateState(plano);
		return plano;
	}

	updateFormDadosBasicos(form: FormGroup, plano: Plano) {
		const date = new Date();
		date.setFullYear(date.getFullYear() + 1);
		plano = this.planoStateService.updatePlanoObj();
		if (plano.vigenciaDe === undefined) {
			plano.vigenciaDe = new Date().getTime();
		}
		if (plano.ingressoAte === undefined) {
			plano.ingressoAte = date.getTime();
		}
		if (plano.vigenciaAte === undefined) {
			plano.vigenciaAte = date.getTime();
		}
		const formValue: any = {
			descricao: plano.descricao,
			tipoPlano: plano.tipoPlano,
			vigenciaDe: plano.vigenciaDe,
			ingressoAte: plano.ingressoAte,
			vigenciaAte: plano.vigenciaAte,
			produtoContrato: plano.produtoContrato,
		};

		if (plano.tipoPlano === TipoPlano.PLANO_CREDITO) {
			formValue.creditoSessao = plano.creditoSessao;
			formValue.creditoTreinoNaoCumulativo = plano.creditoTreinoNaoCumulativo;
		}

		form.patchValue(formValue);
	}

	updatePlanoDadosBasicos(formRawValue, plano: Plano) {
		plano = this.planoStateService.updatePlanoObj();
		plano.descricao = formRawValue.descricao;
		plano.tipoPlano = formRawValue.tipoPlano;
		plano.vigenciaDe = formRawValue.vigenciaDe;
		plano.vigenciaAte = formRawValue.vigenciaAte;
		plano.ingressoAte = formRawValue.ingressoAte;
		plano.produtoContrato = formRawValue.produtoContrato;
		if (plano.tipoPlano === TipoPlano.PLANO_CREDITO) {
			plano.creditoSessao = formRawValue.creditoSessao;
			plano.creditoTreinoNaoCumulativo =
				formRawValue.creditoTreinoNaoCumulativo;
		}
		this.planoStateService.updateState(plano);
	}

	formDadosContratuais(tipoPlano: TipoPlano) {
		const form = new FormGroup({
			modeloContrato: new FormControl("", Validators.required),
			percentualMultaCancelamento: new FormControl(0),
			dividirManutencaoParcelasEA: new FormControl(),
			produtoTaxaCancelamento: new FormControl("", Validators.required),
		});

		if (tipoPlano !== TipoPlano.PLANO_CREDITO) {
			form.setControl("restringirQtdMarcacaoPorDia", new FormControl());
			form.setControl("restringirMarcacaoAulasColetivas", new FormControl());
			form.setControl("restringirQtdMarcacaoPorDiaGeral", new FormControl());
			form.setControl(
				"restringirMarcacaoAulaPorNrVezesModalidade",
				new FormControl()
			);
		}

		if (
			tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			tipoPlano === TipoPlano.PLANO_PERSONAL
		) {
			form.setControl("valorMensal", new FormControl("", Validators.required));
			form.setControl("duracaoPlano", new FormControl("", Validators.required));
			form.setControl("taxaAdesao", new FormControl(""));
			form.setControl("permiteSituacaoAtestadoContrato", new FormControl(""));
		}
		return form;
	}

	updateFormDadosContratuais(form: FormGroup, plano: Plano) {
		const formValue: any = {
			modeloContrato: plano.modeloContrato,
			percentualMultaCancelamento: plano.percentualMultaCancelamento
				? plano.percentualMultaCancelamento
				: 0,
			restringirQtdMarcacaoPorDia: plano.restringirQtdMarcacaoPorDia,
			restringirMarcacaoAulasColetivas: plano.restringirMarcacaoAulasColetivas,
			restringirQtdMarcacaoPorDiaGeral: plano.restringirQtdMarcacaoPorDiaGeral,
			restringirMarcacaoAulaPorNrVezesModalidade:
				plano.restringirMarcacaoAulaPorNrVezesModalidade,
			dividirManutencaoParcelasEA: plano.dividirManutencaoParcelasEA,
			produtoTaxaCancelamento: plano.produtoTaxaCancelamento,
		};
		if (
			plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			plano.tipoPlano === TipoPlano.PLANO_PERSONAL
		) {
			formValue.valorMensal = plano.planoRecorrencia
				? plano.planoRecorrencia.valorMensal
				: "";
			formValue.taxaAdesao =
				plano.planoRecorrencia && plano.planoRecorrencia.taxaAdesao
					? plano.planoRecorrencia.taxaAdesao
					: 0;
			if (plano.planoRecorrencia && !plano.codigo) {
				plano.planoRecorrencia.duracaoPlano = 12;
			}
			formValue.duracaoPlano = plano.planoRecorrencia
				? plano.planoRecorrencia.duracaoPlano
				: "";
			formValue.permiteSituacaoAtestadoContrato =
				plano.permiteSituacaoAtestadoContrato;
		}
		if (plano.tipoPlano === TipoPlano.PLANO_CREDITO) {
			formValue.creditoTreinoNaoCumulativo = plano.creditoTreinoNaoCumulativo;
		}
		form.patchValue(formValue);
	}

	updatePlanoDadosContratuais(form: FormGroup, plano: Plano) {
		form.valueChanges.subscribe((value) => {
			plano = this.planoStateService.updatePlanoObj();
			if (!plano.planoRecorrencia) {
				plano.planoRecorrencia = {};
			}
			plano.modeloContrato = value.modeloContrato;
			plano.percentualMultaCancelamento = value.percentualMultaCancelamento;
			plano.restringirQtdMarcacaoPorDia = value.restringirQtdMarcacaoPorDia;
			plano.restringirMarcacaoAulasColetivas =
				value.restringirMarcacaoAulasColetivas;
			plano.restringirQtdMarcacaoPorDiaGeral =
				value.restringirQtdMarcacaoPorDiaGeral;
			plano.restringirMarcacaoAulaPorNrVezesModalidade =
				value.restringirMarcacaoAulaPorNrVezesModalidade;
			plano.dividirManutencaoParcelasEA = value.dividirManutencaoParcelasEA;
			plano.produtoTaxaCancelamento = value.produtoTaxaCancelamento;
			if (
				plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
				plano.tipoPlano === TipoPlano.PLANO_PERSONAL
			) {
				plano.planoRecorrencia.valorMensal = value.valorMensal;
				plano.planoRecorrencia.taxaAdesao = value.taxaAdesao;
				plano.planoRecorrencia.duracaoPlano = value.duracaoPlano;
				plano.permiteSituacaoAtestadoContrato =
					value.permiteSituacaoAtestadoContrato;
			}
			this.planoStateService.updateState(plano);
		});
	}

	formConfigBasicos(tipoPlano: TipoPlano) {
		let form: FormGroup;
		if (
			tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			tipoPlano === TipoPlano.PLANO_PERSONAL ||
			tipoPlano === TipoPlano.PLANO_CREDITO
		) {
			form = new FormGroup({
				inicioMinimoContrato: new FormControl(""),
				quantidadeMaximaFrequencia: new FormControl(""),
				convidadosPorMes: new FormControl(""),
				site: new FormControl(false),
				permitirVendaPlanoSiteNoBalcao: new FormControl(false),
				permitirCompartilharPLanoNoSite: new FormControl(false),
				bolsa: new FormControl(false),
				comissao: new FormControl(false),
				quantidadeCompartilhamentos: new FormControl(""),
				apresentarPactoFlow: new FormControl(false),
				permitirTurmasVendasOnline: new FormControl(false),
				videoSiteUrl: new FormControl(""),
				observacaoSite: new FormControl(""),
				diasBloquearCompraMesmoPlano: new FormControl(""),
				observacao1: new FormControl(""),
				observacao2: new FormControl(""),
			});
			form.addControl("bloquearRecompra", new FormControl(false));
			form.addControl("contratosEncerramDia", new FormControl(null));
			form.addControl("enviarDependenteFoguete", new FormControl(false));
			if (
				tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
				tipoPlano === TipoPlano.PLANO_PERSONAL
			) {
				form.setControl("apresentarVendaRapida", new FormControl(false));
			}
			if (
				tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
				tipoPlano === TipoPlano.PLANO_CREDITO
			) {
				form.setControl("termoAceite", new FormControl());
				form.setControl("totem", new FormControl());
				form.setControl("renovarComDescontoTotem", new FormControl());
				form.setControl("permitirVendaPlanoTotemNoBalcao", new FormControl());
				form.setControl("descricaoEncantamento", new FormControl());
				form.setControl("permitirTransferenciaDeCredito", new FormControl());
				form.setControl(
					"quantidadeATransferirPermitidaPorAluno",
					new FormControl()
				);
			}
		}

		return form;
	}

	updateformConfigBasicos(form: FormGroup, plano: Plano) {
		form.patchValue({
			inicioMinimoContrato: plano.inicioMinimoContrato,
			termoAceite: plano.termoAceite,
			totem: plano.totem,
			renovarComDescontoTotem: plano.renovarComDescontoTotem,
			permitirVendaPlanoTotemNoBalcao: plano.permitirVendaPlanoTotemNoBalcao,
			descricaoEncantamento: plano.descricaoEncantamento,
			quantidadeMaximaFrequencia: plano.quantidadeMaximaFrequencia,
			convidadosPorMes: plano.convidadosPorMes,
			site: plano.site,
			permitirCompartilharPLanoNoSite: plano.permitirCompartilharPLanoNoSite,
			permitirVendaPlanoSiteNoBalcao: plano.permitirVendaPlanoSiteNoBalcao,
			apresentarVendaRapida: plano.apresentarVendaRapida,
			bolsa: plano.bolsa,
			comissao: plano.comissao,
			quantidadeCompartilhamentos: plano.quantidadeCompartilhamentos,
			apresentarPactoFlow: plano.apresentarPactoFlow,
			permitirTurmasVendasOnline: plano.permitirTurmasVendasOnline,
			videoSiteUrl: plano.videoSiteUrl,
			observacaoSite: plano.observacaoSite,
			diasBloquearCompraMesmoPlano: plano.diasBloquearCompraMesmoPlano,
			permitirTransferenciaDeCredito: plano.permitirTransferenciaDeCredito,
			quantidadeATransferirPermitidaPorAluno:
				plano.quantidadeATransferirPermitidaPorAluno,
			observacao1: plano.observacao1,
			observacao2: plano.observacao2,
		});
	}

	formConfigDadosContratuais(tipoPlano: TipoPlano) {
		const form = new FormGroup({
			aceitaDescontoExtra: new FormControl(true),
			descontoAntecipado: new FormControl(),
			diasVencimentoProrata: new FormControl(),
			prorataObrigatorio: new FormControl(),
			obrigatorioInformarCartaoCreditoVenda: new FormControl(),
			renovavelAutomaticamente: new FormControl(),
			renovarAutomaticamenteComDesconto: new FormControl(),
			renovarProdutoObrigatorio: new FormControl(),
			renovarAutomaticamenteUtilizandoValorBaseContrato: new FormControl(),
			parcelamentoOperadora: new FormControl(),
			parcelamentoOperadoraDuracao: new FormControl(),
			maximoVezesParcelar: new FormControl(),
			cobrarAdesaoSeparada: new FormControl(),
			nrVezesParcelarAdesao: new FormControl(),
			cobrarProdutoSeparado: new FormControl(),
			nrVezesParcelarProduto: new FormControl(),
			permitirAcessoRedeEmpresa: new FormControl(),
			acessoRedeEmpresasEspecificas: new FormControl(),
			site: new FormControl(false),
			permitirCompartilharPLanoNoSite: new FormControl(false),
			permitirVendaPlanoSiteNoBalcao: new FormControl(false),
			videoSiteUrl: new FormControl(""),
			observacaoSite: new FormControl(""),
			diasBloquearCompraMesmoPlano: new FormControl(""),
			inicioMinimoContrato: new FormControl(""),
			apresentarPactoFlow: new FormControl(false),
			permitirTurmasVendasOnline: new FormControl(false),
			planoDiferenteRenovacao: new FormControl(),
			modalidadesPlanoDiferenteRenovacao: new FormControl(),
			horarioPlanoDiferenteRenovacao: new FormControl(),
			duracaoPlanoDiferenteRenovacao: new FormControl(),
			condicaoPagPlanoDiferenteRenovacao: new FormControl(),
			observacao1: new FormControl(),
			observacao2: new FormControl(),
		});

		if (tipoPlano !== TipoPlano.PLANO_CREDITO) {
			form.setControl("cancelamentoProporcional", new FormControl());
			form.setControl("qtdDiasCobrarProximaParcela", new FormControl());
			form.setControl("qtdDiasCobrarAnuidadeTotal", new FormControl());
			form.setControl("naoRenovarParcelaVencida", new FormControl());
			form.setControl("termoAceite", new FormControl());
		}

		if (tipoPlano === TipoPlano.PLANO_CREDITO) {
			form.setControl(
				"naoRenovarContratoParcelaVencidaAberto",
				new FormControl()
			);
			form.setControl("termoAceite", new FormControl());
		}

		if (
			tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			tipoPlano === TipoPlano.PLANO_PERSONAL
		) {
			form.setControl("gerarParcelasValorDiferente", new FormControl());
			form.setControl(
				"gerarParcelasValorDiferenteRenovacao",
				new FormControl()
			);
			form.setControl("parcelarAnuidade", new FormControl());
			form.setControl("parcelaAnuidade", new FormControl());
			form.setControl("anuidadeNaParcela", new FormControl());
			form.setControl("valorAnuidade", new FormControl());
			form.setControl("diaAnuidade", new FormControl());
			form.setControl("mesAnuidade", new FormControl());
			form.setControl(
				"qtdDiasAposVencimentoCancelamentoAutomatico",
				new FormControl()
			);
			form.setControl("renovarAnuidadeAutomaticamente", new FormControl());
			form.setControl("apresentarVendaRapida", new FormControl());
		}

		if (tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			form.setControl("naoCobrarAnuidadeProporcional", new FormControl());
			form.setControl("termoAceite", new FormControl());
		}
		return form;
	}

	updateformConfigDadosContratuais(form: FormGroup, plano: Plano) {
		const diasVencimentoProrataObjs: Array<{ label: string; id: number }> =
			new Array<{ label: string; id: number }>();
		if (plano.diasVencimentoProrata) {
			const diasVencimentoProrata = plano.diasVencimentoProrata.split(",");
			diasVencimentoProrata.forEach((dvp) => {
				diasVencimentoProrataObjs.push({
					id: dvp,
					label: dvp,
				});
			});
		}

		if (!plano.planoRecorrencia) {
			plano.planoRecorrencia = {};
		}

		const patchValue: any = {
			diasVencimentoProrata: diasVencimentoProrataObjs,
			prorataObrigatorio: plano.prorataObrigatorio,
			obrigatorioInformarCartaoCreditoVenda:
				plano.obrigatorioInformarCartaoCreditoVenda,
			renovarAutomaticamenteComDesconto:
				plano.renovarAutomaticamenteComDesconto,
			renovarProdutoObrigatorio: plano.renovarProdutoObrigatorio,
			renovarAutomaticamenteUtilizandoValorBaseContrato:
				plano.renovarAutomaticamenteUtilizandoValorBaseContrato,
			naoRenovarParcelaVencida: plano.planoRecorrencia
				? plano.planoRecorrencia.naoRenovarParcelaVencida
				: "",
			parcelamentoOperadora: plano.parcelamentoOperadora,
			parcelamentoOperadoraDuracao: plano.parcelamentoOperadoraDuracao,
			maximoVezesParcelar: plano.maximoVezesParcelar,
			cobrarAdesaoSeparada: plano.cobrarAdesaoSeparada,
			nrVezesParcelarAdesao: plano.nrVezesParcelarAdesao,
			cobrarProdutoSeparado: plano.cobrarProdutoSeparado,
			nrVezesParcelarProduto: plano.nrVezesParcelarProduto,
			cancelamentoProporcional: plano.planoRecorrencia
				? plano.planoRecorrencia.cancelamentoProporcional
				: "",
			qtdDiasCobrarProximaParcela: plano.planoRecorrencia
				? plano.planoRecorrencia.qtdDiasCobrarProximaParcela
				: "",
			qtdDiasCobrarAnuidadeTotal: plano.planoRecorrencia
				? plano.planoRecorrencia.qtdDiasCobrarAnuidadeTotal
				: "",
			percentualMultaCancelamento: plano.percentualMultaCancelamento,
			produtoTaxaCancelamento: plano.produtoTaxaCancelamento,
			descontoAntecipado: plano.descontoAntecipado,
			naoRenovarContratoParcelaVencidaAberto:
				plano.naoRenovarContratoParcelaVencidaAberto,
			aceitaDescontoExtra: plano.aceitaDescontoExtra,
			permitirAcessoRedeEmpresa: plano.permitirAcessoRedeEmpresa,
			acessoRedeEmpresasEspecificas: plano.acessoRedeEmpresasEspecificas,
			site: plano.site,
			permitirCompartilharPLanoNoSite: plano.permitirCompartilharPLanoNoSite,
			permitirVendaPlanoSiteNoBalcao: plano.permitirVendaPlanoSiteNoBalcao,
			termoAceite: plano.termoAceite,
			inicioMinimoContrato: plano.inicioMinimoContrato,
			apresentarPactoFlow: plano.apresentarPactoFlow,
			permitirTurmasVendasOnline: plano.permitirTurmasVendasOnline,
			videoSiteUrl: plano.videoSiteUrl,
			observacaoSite: plano.observacaoSite,
			diasBloquearCompraMesmoPlano: plano.diasBloquearCompraMesmoPlano,
			planoDiferenteRenovacao: plano.planoDiferenteRenovacao,
			horarioPlanoDiferenteRenovacao: plano.horarioPlanoDiferenteRenovacao,
			duracaoPlanoDiferenteRenovacao: plano.duracaoPlanoDiferenteRenovacao,
			condicaoPagPlanoDiferenteRenovacao:
				plano.condicaoPagPlanoDiferenteRenovacao,
			observacao1: plano.observacao1,
			observacao2: plano.observacao2,
		};
		if (plano.planoDiferenteRenovacao) {
			this.planoService
				.findById(plano.planoDiferenteRenovacao)
				.subscribe((resp) => {
					patchValue.planoDiferenteRenovacao = resp.content;
					form.patchValue(patchValue);
				});
		}
		if (plano.modalidadesPlanoDiferenteRenovacao) {
			patchValue.modalidadesPlanoDiferenteRenovacao =
				plano.modalidadesPlanoDiferenteRenovacao
					.split(",")
					.map((id) => ({ id: id.trim() }));
		}
		if (plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			patchValue.renovavelAutomaticamente =
				plano.planoRecorrencia.renovavelAutomaticamente;
		} else {
			patchValue.renovavelAutomaticamente = plano.renovavelAutomaticamente;
		}

		if (
			plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			plano.tipoPlano === TipoPlano.PLANO_PERSONAL
		) {
			if (plano.planoRecorrencia) {
				patchValue.gerarParcelasValorDiferente =
					plano.planoRecorrencia.gerarParcelasValorDiferente;
				patchValue.gerarParcelasValorDiferenteRenovacao =
					plano.planoRecorrencia.gerarParcelasValorDiferenteRenovacao;
				patchValue.anuidadeNaParcela = plano.planoRecorrencia.anuidadeNaParcela;
				patchValue.parcelarAnuidade = plano.planoRecorrencia.parcelarAnuidade;
				patchValue.parcelaAnuidade = plano.planoRecorrencia.parcelaAnuidade;
				patchValue.valorAnuidade = plano.planoRecorrencia.valorAnuidade;
				patchValue.renovarAnuidadeAutomaticamente =
					plano.renovarAnuidadeAutomaticamente;
				patchValue.qtdDiasAposVencimentoCancelamentoAutomatico =
					plano.planoRecorrencia.qtdDiasAposVencimentoCancelamentoAutomatico;
				patchValue.aplicarDiasVencimentoContratosAtivos =
					plano.planoRecorrencia.aplicarDiasVencimentoContratosAtivos;
				patchValue.aplicarCancelamentoProporcionalContratosAtivos =
					plano.planoRecorrencia.aplicarCancelamentoProporcionalContratosAtivos;
				patchValue.diaAnuidade =
					plano.planoRecorrencia.diaAnuidade !== undefined
						? plano.planoRecorrencia.diaAnuidade
						: 1;
				patchValue.mesAnuidade =
					plano.planoRecorrencia.mesAnuidade !== undefined
						? plano.planoRecorrencia.mesAnuidade
						: 1;
				plano.planoRecorrencia.parcelarAnuidade
					? form.get("valorAnuidade").disable()
					: form.get("valorAnuidade").enable();
			}
		}

		if (plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			patchValue.naoCobrarAnuidadeProporcional =
				plano.planoRecorrencia.naoCobrarAnuidadeProporcional;
		}

		form.patchValue(patchValue);
	}

	produtoSelectBuilder(term) {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	}

	modeloContratoSelectBuilder(term) {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	}

	loadProdutosContrato() {
		return this.produtoService.getProdutosTipoPlano();
	}

	voltarParaListagem() {
		this.planoStateService.removeFromSession();
		this.router.navigate(["adm", "planos"]);
	}

	tiposPlano(traducoes: TraducoesXinglingComponent) {
		const promise = new Promise<Array<{ label: string; id: string }>>(
			(resolve, reject) => {
				setTimeout(() => {
					const tipos = [];
					tipos.push({
						label: traducoes.getLabel("PN"),
						id: TipoPlano.PLANO_NORMAL,
					});
					tipos.push({
						label: traducoes.getLabel("PR"),
						id: TipoPlano.PLANO_RECORRENCIA,
					});
					tipos.push({
						label: traducoes.getLabel("PC"),
						id: TipoPlano.PLANO_CREDITO,
					});
					tipos.push({
						label: traducoes.getLabel("PP"),
						id: TipoPlano.PLANO_PERSONAL,
					});
					resolve(tipos);
				});
			}
		);
		return promise;
	}

	verifyIfNewExists(row, dataTable, rowIndex, keysAvoidCompare?: string[]) {
		let exists = false;
		if (!keysAvoidCompare) {
			keysAvoidCompare = [];
		}
		keysAvoidCompare.push("edit");
		keysAvoidCompare.push("codigo");
		dataTable.find((data, index) => {
			if (index !== rowIndex) {
				exists = this.compareObjects(row, data, "codigo", keysAvoidCompare);
				if (exists) {
					return;
				}
			}
		});
		return exists;
	}

	// TODO Lucas: Corrigir bug para uma coluna
	compareObjects(obj1, obj2, uniqueKey?, keysAvoidCompare?: string[]) {
		let equals = true;
		Object.keys(obj1).forEach((key) => {
			if (keysAvoidCompare.find((v) => v === key)) {
				return;
			}
			if (
				obj1[key] !== undefined &&
				obj2[key] !== undefined &&
				obj1[key] !== null &&
				obj2[key] !== null
			) {
				if (typeof obj1[key] !== "object") {
					if (obj1[key].toString() !== obj2[key].toString()) {
						equals = false;
						return;
					}
				} else {
					if (uniqueKey) {
						if (
							(obj1[key][uniqueKey] &&
								obj1[key][uniqueKey] !== obj2[key][uniqueKey]) ||
							(obj1[key]["value"] && obj1[key]["value"] !== obj2[key]["value"])
						) {
							equals = false;
							return;
						}
					} else {
						equals = this.compareObjects(obj1[key], obj2[key]);
					}
				}
			}
		});
		return equals;
	}

	initTiposOperacoes(traducao: TraducoesXinglingComponent) {
		const promise = new Promise<Array<{ label: string; codigo: string }>>(
			(resolve, reject) => {
				setTimeout(() => {
					const tipoOperacoes = [];
					tipoOperacoes.push({ label: traducao.getLabel("AC"), codigo: "AC" });
					tipoOperacoes.push({ label: traducao.getLabel("RE"), codigo: "RE" });
					resolve(tipoOperacoes);
				});
			}
		);
		return promise;
	}

	initTiposValor(traducao: TraducoesXinglingComponent) {
		const promise = new Promise<Array<{ label: string; codigo: string }>>(
			(resolve, reject) => {
				setTimeout(() => {
					const tipoOperacoes = [];
					tipoOperacoes.push({ label: traducao.getLabel("VE"), codigo: "VE" });
					tipoOperacoes.push({ label: traducao.getLabel("PD"), codigo: "PD" });
					resolve(tipoOperacoes);
				});
			}
		);
		return promise;
	}

	initFrequencias(traducao: TraducoesXinglingComponent) {
		const promise = new Promise<Array<{ label: string; value: number }>>(
			(resolve, reject) => {
				setTimeout(() => {
					const frequencias = [];
					for (let i = 1; i <= 7; i++) {
						frequencias.push({
							value: i,
							label: `${i}x ${traducao.getLabel("por-semana")}`,
						});
					}
					resolve(frequencias);
				});
			}
		);
		return promise;
	}

	vezesSemanaOptions(): Array<{ codigo: number; label: string }> {
		return [
			{
				codigo: 1,
				label: "1x",
			},
			{
				codigo: 2,
				label: "2x",
			},
			{
				codigo: 3,
				label: "3x",
			},
			{
				codigo: 4,
				label: "4x",
			},
			{
				codigo: 5,
				label: "5x",
			},
			{
				codigo: 6,
				label: "6x",
			},
			{
				codigo: 7,
				label: "7x",
			},
		];
	}

	convertPlano(plano: Plano) {
		plano.modalidades = this.createPlanoModalidades(plano);
		plano.horarios = this.updateHorarios(plano);
		return plano;
	}

	populateModalidadeAux(plano) {
		if (!plano.modalidadesAux) {
			plano.modalidadesAux = new Array<any>();
		}
		if (plano.modalidades) {
			plano.modalidades.forEach((planoModalidade) => {
				if (planoModalidade.vezesSemana) {
					planoModalidade.vezesSemana.forEach((vezSemana) => {
						const pm: any = {
							codigo: vezSemana.codigo,
							codigoPlanoModalidade: planoModalidade.codigo,
							modalidade: planoModalidade.modalidade,
						};
						pm.vezesSemana = vezSemana.nrVezes;
						pm.valor = vezSemana.valorEspecifico;
						plano.modalidadesAux.push(pm);
					});
				}
			});
		}
		return plano;
	}

	populateHorarioValor(plano: Plano) {
		if (plano.horarios) {
			plano.horarios.forEach((horario) => {
				if (horario.tipoValor === "VE") {
					horario.valor = horario.valorEspecifico;
				}
				if (horario.tipoValor === "PD") {
					horario.valor = horario.percentualDesconto;
				}
			});
		}
		return plano;
	}

	createPlanoModalidades(plano) {
		const planoModalidades = new Array<PlanoModalidade>();
		if (plano.modalidadesAux) {
			plano.modalidadesAux.forEach((planoModalidadeAux) => {
				if (planoModalidades.length === 0) {
					const vezSemana: any = {
						codigo: planoModalidadeAux.codigo,
						nrVezes:
							planoModalidadeAux.vezesSemana &&
							planoModalidadeAux.vezesSemana.codigo
								? planoModalidadeAux.vezesSemana.codigo
								: planoModalidadeAux.vezesSemana,
					};
					if (plano.creditoSessao) {
						vezSemana.valorEspecifico = planoModalidadeAux.valor;
						vezSemana.tipoOperacao = "EX";
					}
					planoModalidades.push({
						codigo: planoModalidadeAux.codigoPlanoModalidade,
						modalidade: planoModalidadeAux.modalidade,
						vezesSemana: [vezSemana],
					});
				} else {
					const finded = planoModalidades.find(
						(pm) =>
							pm.modalidade.codigo === planoModalidadeAux.modalidade.codigo
					);
					if (finded) {
						const vezSemana: any = {
							codigo: planoModalidadeAux.codigo,
							nrVezes:
								planoModalidadeAux.vezesSemana &&
								planoModalidadeAux.vezesSemana.codigo
									? planoModalidadeAux.vezesSemana.codigo
									: planoModalidadeAux.vezesSemana,
						};
						if (plano.creditoSessao) {
							vezSemana.valorEspecifico = planoModalidadeAux.valor;
							vezSemana.tipoOperacao = "EX";
						}
						finded.vezesSemana.push(vezSemana);
					} else {
						const vezSemana: any = {
							codigo: planoModalidadeAux.codigo,
							nrVezes:
								planoModalidadeAux.vezesSemana &&
								planoModalidadeAux.vezesSemana.codigo
									? planoModalidadeAux.vezesSemana.codigo
									: planoModalidadeAux.vezesSemana,
						};
						if (plano.creditoSessao) {
							vezSemana.valorEspecifico = planoModalidadeAux.valor;
							vezSemana.tipoOperacao = "EX";
						}
						planoModalidades.push({
							codigo: planoModalidadeAux.codigoPlanoModalidade,
							modalidade: planoModalidadeAux.modalidade,
							vezesSemana: [vezSemana],
						});
					}
				}
			});
		} else {
			return plano.modalidades;
		}
		return planoModalidades;
	}

	updateHorarios(plano: Plano) {
		const horarios = plano.horarios;
		if (horarios) {
			horarios.forEach((horario) => {
				horario.tipoValor =
					horario.tipoValor &&
					(horario.tipoValor.codigo || horario.tipoValor.codigo === null)
						? horario.tipoValor.codigo
						: horario.tipoValor;
				horario.tipoOperacao =
					horario.tipoOperacao &&
					(horario.tipoOperacao.codigo || horario.tipoOperacao.codigo === null)
						? horario.tipoOperacao.codigo
						: horario.tipoOperacao;
				if (horario.tipoValor === "VE") {
					horario.valorEspecifico = horario.valor;
					horario.valor = undefined;
				}
				if (horario.tipoValor === "PD") {
					horario.percentualDesconto = horario.valor;
					horario.valor = undefined;
				}
			});
		}
		return horarios;
	}

	updateCondicoesPagamento(plano: Plano) {
		const duracoes = plano.duracoes;
		if (duracoes) {
			duracoes.forEach((duracao) => {
				if (duracao.condicoesPagamento) {
					duracao.condicoesPagamento.forEach((condicaoPagamento) => {
						if (condicaoPagamento.tipoValor) {
							if (typeof condicaoPagamento.tipoValor === "object") {
								condicaoPagamento.tipoValor =
									condicaoPagamento.tipoValor.codigo;
							}
							if (condicaoPagamento.tipoValor === "VE") {
								condicaoPagamento.valorEspecifico = condicaoPagamento.valor;
								condicaoPagamento.valor = undefined;
							}
							if (condicaoPagamento.tipoValor === "PD") {
								condicaoPagamento.percentualDesconto = condicaoPagamento.valor;
								condicaoPagamento.valor = undefined;
							}
							if (
								typeof condicaoPagamento.tipoValor === "object" &&
								condicaoPagamento.tipoValor &&
								!condicaoPagamento.tipoValor.codigo
							) {
								condicaoPagamento.tipoValor = undefined;
							}
						}
						if (
							typeof condicaoPagamento.tipoOperacao === "object" &&
							condicaoPagamento.tipoOperacao
						) {
							condicaoPagamento.tipoOperacao =
								condicaoPagamento.tipoOperacao.codigo;
						}
					});
				}
			});
		}
		return duracoes;
	}

	updateCondicoesPagamentoEdit(plano: Plano) {
		const duracoes = plano.duracoes;
		if (duracoes) {
			duracoes.forEach((duracao) => {
				if (duracao.condicoesPagamento) {
					duracao.condicoesPagamento.forEach((condicaoPagamento) => {
						if (condicaoPagamento.tipoValor) {
							if (
								condicaoPagamento.tipoValor === "VE" ||
								condicaoPagamento.tipoValor.codigo === "VE"
							) {
								condicaoPagamento.valor = condicaoPagamento.valorEspecifico;
								condicaoPagamento.valorEspecifico = undefined;
							}
							if (
								condicaoPagamento.tipoValor === "PD" ||
								condicaoPagamento.tipoValor.codigo === "PD"
							) {
								condicaoPagamento.valor = condicaoPagamento.percentualDesconto;
								condicaoPagamento.percentualDesconto = undefined;
							}
						}
						if (
							condicaoPagamento.tipoOperacao &&
							condicaoPagamento.tipoOperacao.codigo
						) {
							condicaoPagamento.tipoOperacao =
								condicaoPagamento.tipoOperacao.codigo;
						}
					});
				}
			});
		}
		return duracoes;
	}

	beforeConfirmCondicaoPagamento(
		row: any,
		form: any,
		data: any,
		rowIndex: any,
		notificationService: SnotifyService
	) {
		const tipoValor = form.get("tipoValor").value;
		const condicaoPagamento = form.get("condicaoPagamento").value;
		const tipoOperacao = form.get("tipoOperacao").value;
		const valor = form.get("valor").value;

		if (condicaoPagamento === null || condicaoPagamento === undefined) {
			notificationService.error(
				"Por favor selecione uma condição de pagamento!"
			);
			return false;
		}

		if (
			data.find(
				(planocondicaopagamento, index) =>
					planocondicaopagamento.condicaoPagamento.codigo ===
						condicaoPagamento.codigo && rowIndex !== index
			)
		) {
			notificationService.error(
				`Condição de pagamento ${condicaoPagamento.descricao} já foi adicionada!`
			);
			return false;
		}

		if (
			(valor === "" || valor === 0 || valor === null || valor === undefined) &&
			tipoValor !== null &&
			tipoValor !== undefined &&
			tipoValor !== "" &&
			typeof tipoValor === "object" &&
			tipoValor.codigo !== null
		) {
			notificationService.error("Por favor preencha o valor do reajuste!");
			return false;
		}

		if (valor && (!tipoValor || !tipoValor.codigo)) {
			notificationService.error("Por favor selecione uma forma de cálculo");
			return false;
		}
		if (
			(tipoOperacao === null ||
				tipoOperacao === undefined ||
				tipoOperacao === "" ||
				tipoOperacao.codigo === null) &&
			tipoValor !== null &&
			tipoValor !== undefined &&
			tipoValor !== "" &&
			typeof tipoValor === "object" &&
			tipoValor.codigo !== null
		) {
			notificationService.error("Por favor selecione o tipo da operação!");
			return false;
		}
		return true;
	}

	clonar(activatedRoute: ActivatedRoute, planoService: PlanoService) {
		const code: number = +activatedRoute.snapshot.queryParamMap.get("code");
		return planoService.clone(code);
	}

	numeroParcelas() {
		const numeroParcelasOptions = new Array<{ label: string; value: number }>();
		for (let i = 1; i <= 12; i++) {
			numeroParcelasOptions.push({
				label: `${i}x`,
				value: i,
			});
		}
		return numeroParcelasOptions;
	}

	initDiasProrata(plano: Plano) {
		for (let i = 1; i <= 31; i++) {
			if (!plano.diasVencimentoProrata) {
				plano.diasVencimentoProrata = `${i},`;
				continue;
			}
			if (i === 31) {
				plano.diasVencimentoProrata += `${i}`;
			} else {
				plano.diasVencimentoProrata += `${i},`;
			}
		}
	}

	initConfiguracaoSistema(): Observable<PlanoConfiguracaoSistema> {
		return this.planoService.configuracaoSistema();
	}
}
