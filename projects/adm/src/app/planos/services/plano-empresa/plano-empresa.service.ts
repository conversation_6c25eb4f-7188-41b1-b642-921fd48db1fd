import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { AdmRestService } from "../../../adm-rest.service";
import { Observable } from "rxjs";

@Injectable({
	providedIn: "root",
})
export class PlanoEmpresaService {
	urlPlanoEmpresa: string;

	constructor(private http: HttpClient, private admRest: AdmRestService) {
		this.urlPlanoEmpresa = this.admRest.buildFullUrlPlano("plano-empresas");
	}

	public listByPlanoId(idPlano?: number): Observable<any> {
		const url = idPlano
			? `${this.urlPlanoEmpresa}/${idPlano}`
			: `${this.urlPlanoEmpresa}`;
		return this.http.get(`${url}`);
	}
}
