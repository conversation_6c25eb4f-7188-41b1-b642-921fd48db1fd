export enum tipoConvenioCobranca {
	NENHUM = 0,
	BOLETO = 1,
	DCC = 2,
	DCO_BB = 3,
	<PERSON><PERSON>_BRADESCO = 4,
	DCO_ITAU = 5,
	DCO_CAIXA = 6,
	DCO_HSBC = 7,
	DCC_GETNET = 8,
	<PERSON><PERSON><PERSON><PERSON>_ITAU = 9,
	DCO_SANTANDER = 10,
	DCC_BIN = 11,
	DCC_VINDI = 12,
	DCC_CIELO_ONLINE = 13,
	DCC_MAXIPAGO = 15,
	DCC_E_REDE = 16,
	BOLETO_DAYCOVAL = 17,
	DCC_FITNESS_CARD = 18,
	DCO_BRADESCO_240 = 19,
	DCC_GETNET_ONLINE = 20,
	DCC_STONE_ONLINE = 21,
	DCO_SANTANDER_150 = 22,
	DCO_CAIXA_SICOV = 23,
	BOLETO_PJBANK = 24,
	ITAU = 25,
	DCC_MUNDIPAGG = 26,
	DCC_PAGAR_ME = 27,
	PIX_BB = 28,
	PIX_BRADESCO = 29,
	PIX_SANTANDER = 30,
	DCC_STRIPE = 31,
	DCC_PAGOLIVRE = 32,
	<PERSON><PERSON><PERSON><PERSON>_ITAU_ONLINE = 33,
	DCC_VALORIBANK = 34,
	DCC_ONE_PAYMENT = 35,
	PINPAD_STONE_CONNECT = 36,
	PINPAD_GETCARD_SCOPE = 37,
	PIX_PJBANK = 38,
	BOLETO_ASAAS = 39,
	PIX_ASAAS = 40,
	DCC_FACILITEPAY = 41,
}

export const getTipoConvenioCobrancaText = (tipoCodigo) => {
	switch (tipoCodigo) {
		case tipoConvenioCobranca.NENHUM:
			return "-";
		case tipoConvenioCobranca.BOLETO:
			return "Boleto Bancário";
		case tipoConvenioCobranca.DCC:
			return "DCC Cielo EDI";
		case tipoConvenioCobranca.DCO_BB:
			return "DCO Banco do Brasil";
		case tipoConvenioCobranca.DCO_BRADESCO:
			return "DCO Bradesco";
		case tipoConvenioCobranca.DCO_ITAU:
			return "DCO Itaú";
		case tipoConvenioCobranca.DCO_CAIXA:
			return "DCO Caixa (SIACC 150)";
		case tipoConvenioCobranca.DCO_HSBC:
			return "DCO HSBC";
		case tipoConvenioCobranca.DCC_GETNET:
			return "DCC Getnet EDI";
		case tipoConvenioCobranca.BOLETO_ITAU:
			return "Boleto Bancário Itaú";
		case tipoConvenioCobranca.DCO_SANTANDER:
			return "DCO Santander";
		case tipoConvenioCobranca.DCC_BIN:
			return "DCC Bin EDI";
		case tipoConvenioCobranca.DCC_VINDI:
			return "DCC Vindi Online";
		case tipoConvenioCobranca.DCC_CIELO_ONLINE:
			return "DCC Cielo Online";
		case tipoConvenioCobranca.DCC_MAXIPAGO:
			return "DCC MaxiPago Online";
		case tipoConvenioCobranca.DCC_E_REDE:
			return "DCC Rede Online";
		case tipoConvenioCobranca.BOLETO_DAYCOVAL:
			return "Boleto Bancário Daycoval";
		case tipoConvenioCobranca.DCC_FITNESS_CARD:
			return "DCC Fitness Card Online";
		case tipoConvenioCobranca.DCO_BRADESCO_240:
			return "DCO Bradesco 240";
		case tipoConvenioCobranca.DCC_GETNET_ONLINE:
			return "DCC Getnet Online";
		case tipoConvenioCobranca.DCC_STONE_ONLINE:
			return "DCC Stone Online";
		case tipoConvenioCobranca.DCO_SANTANDER_150:
			return "DCO Santander Layout Febraban 150";
		case tipoConvenioCobranca.DCO_CAIXA_SICOV:
			return "DCO Caixa (SICOV 150)";
		case tipoConvenioCobranca.BOLETO_PJBANK:
			return "Boleto Bancário PJBank";
		case tipoConvenioCobranca.ITAU:
			return "Boleto Bancário Itaú CNAB400";
		case tipoConvenioCobranca.DCC_MUNDIPAGG:
			return "DCC Mundipagg Online";
		case tipoConvenioCobranca.DCC_PAGAR_ME:
			return "DCC Pagar.me Online";
		case tipoConvenioCobranca.PIX_BB:
			return "Pix Banco do Brasil";
		case tipoConvenioCobranca.PIX_BRADESCO:
			return "Pix Bradesco";
		case tipoConvenioCobranca.PIX_SANTANDER:
			return "Pix Santander";
		case tipoConvenioCobranca.DCC_STRIPE:
			return "DCC Stripe Online";
		case tipoConvenioCobranca.DCC_PAGOLIVRE:
			return "DCC PagoLivre Online";
		case tipoConvenioCobranca.BOLETO_ITAU_ONLINE:
			return "Boleto Bancário Itaú - Registro Online";
		case tipoConvenioCobranca.DCC_VALORIBANK:
			return "DCC ValoriBank Online";
		case tipoConvenioCobranca.DCC_ONE_PAYMENT:
			return "DCC One Payment Online";
		case tipoConvenioCobranca.PINPAD_STONE_CONNECT:
			return "PinPad - Stone Connect 2.0 (POS)";
		case tipoConvenioCobranca.PINPAD_GETCARD_SCOPE:
			return "PinPad - GetCard (Scope)";
		case tipoConvenioCobranca.PIX_PJBANK:
			return "Pix PjBank";
		case tipoConvenioCobranca.BOLETO_ASAAS:
			return "Boleto Bancário Asaas";
		case tipoConvenioCobranca.PIX_ASAAS:
			return "Pix Asaas";
		case tipoConvenioCobranca.DCC_FACILITEPAY:
			return "DCC Fypay Online";
		default:
			return "-";
	}
};
