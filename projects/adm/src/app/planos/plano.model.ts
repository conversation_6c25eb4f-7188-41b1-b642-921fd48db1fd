import { PlanoHorario } from "adm-core-api/lib/plano-horario.model";
import { Categoria } from "cadastro-aux-api";
import {
	Modalidade,
	Pacote,
	PlanoModalidadeVezesSemana,
	Produto,
} from "plano-api";

export class Plano {
	codigo: number;
	descricao: string;
	vigenciaDe: number;
	vigenciaAte: number;
	ingressoAte: number;
	tipoPlano?: TipoPlano = TipoPlano.NENHUM;
	atividades: any;
	excecoes: Array<any>;
	produtosSugeridos: Array<any>;
	duracoes: Array<any>;
	modeloContrato: any;
	termoAceite: any;
	permitePacotes: boolean;
	percentualMultaCancelamento: any;
	porcentagemDescontoBoletoPagAntecipado: any;
	aceitaDescontoPorPlano: boolean = false;
	restringirQtdMarcacaoPorDia: number;
	restringirMarcacaoAulasColetivas: boolean;
	permiteSituacaoAtestadoContrato: boolean;
	dividirManutencaoParcelasEA: boolean;
	diasVencimentoProrata: any = "";
	prorataObrigatorio: any;
	obrigatorioInformarCartaoCreditoVenda: boolean;
	renovavelAutomaticamente: any;
	renovarAutomaticamenteComDesconto: any;
	renovarProdutoObrigatorio: any;
	renovarAutomaticamenteUtilizandoValorBaseContrato: any;
	naoRenovarContratoParcelaVencidaAberto: any;
	renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia: any;
	permitePagarComBoleto: any;
	diaDoMesDescontoBoletoPagAntecipado: any;
	cobrarAdesaoSeparada: any;
	nrVezesParcelarAdesao: any;
	cobrarProdutoSeparado: any;
	nrVezesParcelarProduto: any;
	produtoTaxaCancelamento: any;
	produtoContrato: any;
	planoRecorrencia: any;
	descontoAntecipado: any;
	bolsa: boolean;
	quantidadeMaximaFrequencia: number;
	convidadosPorMes: number;
	naoRenovarParcelaVencida: boolean;
	parcelamentoOperadora: boolean;
	parcelamentoOperadoraDuracao: boolean;
	maximoVezesParcelar: number = 1;
	site: boolean;
	permitirVendaPlanoSiteNoBalcao: boolean;
	permitirCompartilharPLanoNoSite: boolean;
	totem: boolean;
	permitirVendaPlanoTotemNoBalcao: boolean;
	renovarComDescontoTotem: boolean;
	descricaoEncantamento: string;
	apresentarVendaRapida: boolean;
	comissao = true;
	quantidadeCompartilhamentos: number;
	regimeRecorrencia: boolean;
	vendaCreditoTreino: boolean;
	planoPersonal: boolean;
	creditoSessao: boolean;
	modalidades: Array<PlanoModalidade>;
	modalidadesAux: Array<any>;
	horarios: Array<any>;
	creditoTreinoNaoCumulativo: boolean;
	qtdSemanasAno: number;
	inicioMinimoContrato: Date;
	empresas: Array<PlanoEmpresa>;
	permitirAcessoSomenteNaEmpresaVendeuContrato: boolean;
	aceitaDescontoExtra = true;
	permitirAcessoRedeEmpresa: boolean;
	restringirQtdMarcacaoPorDiaGeral: number;
	limiteReposicaoAulaColetiva: number;
	renovarAnuidadeAutomaticamente: boolean;
	restringirMarcacaoAulaPorNrVezesModalidade: boolean;
	apresentarPactoFlow: boolean;
	permitirTurmasVendasOnline: boolean;
	observacaoSite: string;
	videoSiteUrl: string;
	pacotes: PlanoPacote[];
	categorias: PlanoCategoria[];
	restringeVendaPorCategoria: Boolean;
	diasBloquearCompraMesmoPlano: number;
	planoDiferenteRenovacao: number;
	horarioPlanoDiferenteRenovacao: number;
	modalidadesPlanoDiferenteRenovacao: string;
	duracaoPlanoDiferenteRenovacao: number;
	condicaoPagPlanoDiferenteRenovacao: number;
	bloquearRecompra: boolean;
	contratosEncerramDia: any;
	acessoRedeEmpresasEspecificas: boolean;
	empresasRede: Array<PlanoEmpresaRedeAcesso>;
	permitirTransferenciaDeCredito: boolean;
	quantidadeATransferirPermitidaPorAluno: number;
	enviarDependenteFoguete: boolean;
	observacao1: string;
	observacao2: string;

	constructor() {
		for (let i = 1; i <= 31; i++) {
			if (i === 31) {
				this.diasVencimentoProrata += `${i}`;
				continue;
			}
			this.diasVencimentoProrata += `${i},`;
		}
	}
}

export class PlanoEmpresa {
	codigo: number;
	plano: Plano;
	empresa: Empresa;
	venda: boolean;
	acesso: boolean;
}

export class PlanoEmpresaRedeAcesso {
	codigo: number;
	plano: Plano;
	chave: string;
	nomeEmpresa: string;
	codigoEmpresa: number;
}

export class Empresa {
	codigo: number;
	nome: string;
	setor: string;
	estado: Estado;
	cidade: Cidade;
}

export class Cidade {
	codigo: number;
	nome: string;
	estado: Estado;
}

export class Estado {
	codigo: number;
	nome: string;
	sigla: string;
}

export class PlanoModalidade {
	codigo?: number;
	modalidade: Modalidade;
	listaVezesSemana?: string;
	vezesSemana: Array<PlanoModalidadeVezesSemana>;
}

export class PlanoPacote {
	codigo: number;
	pacote: Pacote;

	constructor(codigo: number, pacote: Pacote) {
		this.codigo = codigo;
		this.pacote = pacote;
	}
}

export class PlanoCategoria {
	codigo: number;
	categoria: Categoria;

	constructor(codigo: number, categoria: Categoria) {
		this.codigo = codigo;
		this.categoria = categoria;
	}
}

export class PlanoProdutoSugerido {
	codigo: number;
	produto: Produto;
	obrigatorio: boolean;
	valorProduto: number;
}

export enum TipoPlano {
	NENHUM = "NENHUM",
	PLANO_NORMAL = "PLANO_NORMAL",
	PLANO_AVANCADO = "PLANO_AVANCADO",
	PLANO_RECORRENCIA = "PLANO_RECORRENCIA",
	PLANO_CREDITO = "PLANO_CREDITO",
	PLANO_PERSONAL = "PLANO_PERSONAL",
}

export class NavegacaoPlano {
	static DADOS_BASICOS = 1;
	static MODALIDADES = 2;
	static DURACAO_VALORES = 3;
	static PRODUTOS_SERVICOS = 4;
	static DADOS_CONTRATUAIS = 5;
	static CONDICAO_PAGAMENTO = 6;
	tipoPlano: TipoPlano;
	passos: number[];
}

export class NavegacaoPlanoAvancado {
	static DADOS_BASICOS = 1;
	static PACOTES = 2;
	static MODALIDADES = 3;
	static DURACAO_VALORES = 4;
	static PRODUTOS_SERVICOS = 5;
	static DADOS_CONTRATUAIS = 6;
	static CONDICAO_PAGAMENTO = 7;
	tipoPlano: TipoPlano;
	passos: number[];
}

export enum TipoHorarioCreditoTreino {
	LIVRE = 1,
	LIVRE_OBRIGATORIO_MARCAR_AULA = 2,
	HORARIO_TURMA = 3,
}

interface TipoHorarioCreditoTreinoSpec {
	label: string;
	value: number;
}

export function getTiposHorariosCreditoTreino(): Array<TipoHorarioCreditoTreinoSpec> {
	const tiposHorarios = new Array<TipoHorarioCreditoTreinoSpec>();
	tiposHorarios.push({
		value: TipoHorarioCreditoTreino.LIVRE,
		label: "LIVRE",
	});
	tiposHorarios.push({
		value: TipoHorarioCreditoTreino.LIVRE_OBRIGATORIO_MARCAR_AULA,
		label: "LIVRE OBRIGATÓRIO MARCAR TURMA",
	});
	tiposHorarios.push({
		value: TipoHorarioCreditoTreino.HORARIO_TURMA,
		label: "HORÁRIO DA TURMA",
	});
	return tiposHorarios;
}

export function getTipoHorarioCreditoTreinoByCode(
	code
): TipoHorarioCreditoTreinoSpec {
	const tiposHorarios = getTiposHorariosCreditoTreino();
	return tiposHorarios.find((th) => th.value === code);
}
