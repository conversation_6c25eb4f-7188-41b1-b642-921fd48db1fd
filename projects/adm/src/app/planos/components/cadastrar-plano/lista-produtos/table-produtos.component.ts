import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	CatTableEditableComponent,
	DialogService,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { PlanoApiProdutoService } from "plano-api";
import { PlanoStateService } from "../plano-state.service";
import { Plano } from "../../../plano.model";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";

@Component({
	selector: "adm-table-produtos",
	templateUrl: "./table-produtos.component.html",
	styleUrls: ["./table-produtos.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableProdutosComponent implements OnInit, AfterViewInit {
	@ViewChild("tableProdutos", { static: false })
	tableProdutos: CatTableEditableComponent;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnValorProduto", { static: true })
	columnValorProduto: TemplateRef<any>;
	@ViewChild("columnObrigatorio", { static: true })
	columnObrigatorio: TemplateRef<any>;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("columnSituacaoPlano", { static: true })
	columnSituacaoPlano: TemplateRef<any>;

	// TODO: Tipar o produto
	produtos: Array<any> = [];
	@Input() planoDTO: Plano;
	table: PactoDataGridConfig;
	produtosPadroes: Array<any> = new Array<any>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	loading = false;

	constructor(
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private produtoService: PlanoApiProdutoService,
		private notificationService: SnotifyService,
		private state: PlanoStateService,
		private decimalPipe: DecimalPipe,
		private dialogService: DialogService
	) {}

	ngOnInit() {
		this.initTable();
		if (this.state.plano) {
			this.planoDTO = this.state.plano;
			if (this.planoDTO.produtosSugeridos) {
				this.planoDTO.produtosSugeridos.sort((a: any, b: any) => {
					if (a.codigo < b.codigo) {
						return -1;
					} else if (a.codigo > b.codigo) {
						return 1;
					}
					return 0;
				});
			}
		}
	}

	ngAfterViewInit() {
		if (this.planoDTO) {
			if (!this.planoDTO.produtosSugeridos) {
				this.planoDTO.produtosSugeridos = new Array<any>();
			}
			if (this.planoDTO.codigo === undefined) {
				this.produtoService.produtoPlanoBasico().subscribe(
					(data: any) => {
						const produtos = data.content;
						this.produtosPadroes = produtos;
						produtos.forEach((prod) => {
							if (this.planoDTO.produtosSugeridos.length > 0) {
								const findProd = this.planoDTO.produtosSugeridos.find(
									(v) => v.produto.codigo === prod.produto.codigo
								);
								if (!findProd) {
									this.planoDTO.produtosSugeridos.push({
										codigo: "",
										produto: prod.produto,
										obrigatorio: prod.obrigatorio,
										valorProduto: prod.valorProduto,
										ativoPlano: true,
									});
								}
							} else {
								this.planoDTO.produtosSugeridos.push({
									produto: prod.produto,
									obrigatorio: prod.obrigatorio,
									valorProduto: prod.valorProduto,
									ativoPlano: true,
								});
							}
						});
						this.produtos = this.planoDTO.produtosSugeridos;
						this.state.plano = this.planoDTO;
						this.table.dataAdapterFn = (serverData) => {
							return {
								content: this.produtos,
							};
						};
						if (this.tableProdutos) {
							this.tableProdutos.reloadData();
						}
					},
					(error) => {
						console.log("Error ", error);
					}
				);
			} else {
				this.loading = true;
				this.produtoService.produtoPlanoBasico().subscribe(
					(data: any) => {
						this.produtosPadroes = data.content;
						this.produtos = this.planoDTO.produtosSugeridos;
						this.table.dataAdapterFn = (serverData) => {
							return {
								content: this.produtos,
							};
						};
						if (this.tableProdutos) {
							this.tableProdutos.reloadData();
						}
						this.loading = false;
					},
					(error) => {
						console.log("Error ", error);
					}
				);
			}
		}
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				produto: new FormControl("", Validators.required),
				valorProduto: new FormControl("", Validators.required),
				obrigatorio: new FormControl(false),
				ativoPlano: new FormControl(true),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "produto",
					titulo: this.columnProduto,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					width: "200px",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					objectAttrLabelName: "descricao",
					endpointUrl: this.admRest.buildFullUrlPlano(
						this.produtoService.produtosVendaveis
					),
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					selectOptionChange: (option, form, row) => {
						form.get("valorProduto").setValue(option.valorFinal);
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
					isDisabled: (row) => this.isProductDisabled(row),
				},
				{
					nome: "valorProduto",
					titulo: this.columnValorProduto,
					visible: true,
					ordenavel: false,
					editable: true,
					width: "137px",
					inputType: "decimal",
					decimal: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
				{
					nome: "obrigatorio",
					titulo: this.columnObrigatorio,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					width: "100px",
					valueTransform: (v) =>
						v
							? this.traducoes.getLabel("label-sim")
							: this.traducoes.getLabel("label-nao"),
					isDisabled: (row) => this.isProductDisabled(row),
				},
				{
					nome: "ativoPlano",
					titulo: this.columnSituacaoPlano,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					valueTransform: (v) =>
						v || v === undefined
							? this.traducoes.getLabel("label-ativo")
							: this.traducoes.getLabel("label-inativo"),
				},
			],
		});
		this.cd.detectChanges();
	}

	confirm(event) {
		this.planoDTO = this.state.plano;
		if (
			this.produtos &&
			event.row &&
			event.row.codigo &&
			event.row.codigo !== "" &&
			event.row.codigo !== undefined &&
			event.row.codigo !== null
		) {
			this.produtos.forEach((p) => {
				if (p.codigo === event.row.codigo) {
					p.edit = false;
				}
			});
		}
		this.planoDTO.produtosSugeridos = this.produtos;
		this.state.plano = this.planoDTO;
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this.planoDTO = this.state.plano;
		const obrigatorio = form.get("obrigatorio").value;
		const ativoPlano = form.get("ativoPlano").value;
		if (obrigatorio === undefined || obrigatorio === null) {
			form.get("obrigatorio").setValue(false);
			row.obrigatorio = false;
		}
		if (ativoPlano === undefined || ativoPlano === null) {
			form.get("ativoPlano").setValue(true);
			row.ativoPlano = true;
		}
		const result = !this.verifyIfNewExists(row, data, rowIndex);
		if (result) {
			if (
				data.find(
					(d, index) =>
						index !== rowIndex &&
						d.produto.codigo === form.get("produto").value.codigo
				)
			) {
				this.notificationService.error(
					this.traducoes.getLabel("produto-duplicado")
				);
				return false;
			}
			const produtosMatricula = data.filter(
				(d) => d.produto.tipoProduto === "MA" && d.ativoPlano
			);
			if (produtosMatricula && produtosMatricula.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Matrícula ativos no plano!"
				);
				return false;
			}

			const produtosRematricula = data.filter(
				(d) => d.produto.tipoProduto === "RE" && d.ativoPlano
			);

			if (produtosRematricula && produtosRematricula.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Rematrícula ativos no plano!"
				);
				return false;
			}

			const produtosRenovacao = data.filter(
				(d) => d.produto.tipoProduto === "RN" && d.ativoPlano
			);
			if (produtosRenovacao && produtosRenovacao.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Renovação ativos no plano!"
				);
				return false;
			}
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("dado-duplicado-table")
			);
		}

		return result;
	}

	showDelete(row, isAdd) {
		const finded = this.produtosPadroes.find((v) => {
			if (v.produto && row.produto && v.produto.codigo === row.produto.codigo) {
				return v;
			}
		});
		return !!!finded;
	}

	isProductDisabled(row) {
		const finded = this.produtosPadroes.find((v) => {
			if (v.produto && row.produto && v.produto.codigo === row.produto.codigo) {
				return v;
			}
		});
		return finded !== undefined;
	}

	delete(event) {
		if (this.produtos) {
			let index;
			if (event.row.codigo) {
				const excecao = this.produtos.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.produtos.splice(index, 1);
				}
			} else {
				this.produtos.splice(event.index, 1);
			}
			this.planoDTO.produtosSugeridos = this.produtos;
			this.state.plano = this.planoDTO;
		}
	}

	verifyIfNewExists(row, dataTable, rowIndex) {
		let exists = false;
		dataTable.find((data, index) => {
			if (index !== rowIndex) {
				exists = this.compareObjects(row, data, "codigo", "edit", "codigo");
				if (exists) {
					return;
				}
			}
		});
		return exists;
	}

	compareObjects(obj1, obj2, uniqueKey?, ...keysAvoidCompare) {
		let equals = true;
		Object.keys(obj1).forEach((key) => {
			if (keysAvoidCompare.find((v) => v === key)) {
				return;
			}
			if (
				obj1[key] !== undefined &&
				obj2[key] !== undefined &&
				obj1[key] !== null &&
				obj2[key] !== null
			) {
				if (typeof obj1[key] !== "object") {
					if (obj1[key].toString() !== obj2[key].toString()) {
						equals = false;
						return;
					}
				} else {
					if (uniqueKey) {
						if (obj1[key][uniqueKey] !== obj2[key][uniqueKey]) {
							equals = false;
							return;
						}
					} else {
						equals = this.compareObjects(obj1[key], obj2[key]);
					}
				}
			}
		});
		return equals;
	}
}
