@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
}

.content-lista-produto {
	border-top: 1px solid $cinza02;
	margin-bottom: 50px;
}

.block-info-produto {
	display: flex;
	width: 32%;
	max-width: 70px;
	margin-top: 6px;
}

.info-produto {
	display: block;
	padding: 7px 0px 0px 15px;

	.descricao-produto {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		@extend .type-h6-bold;
		color: $cinza03;
	}

	.subdescricao-produto {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		@extend .type-p-small;
		color: $cinza03;
	}

	.produtos-set {
		padding-left: 15px;
		font-size: 9px;
		font-weight: 600;
		color: $cinza03;
	}
}

.block-input {
	width: 95%;
}

.block-actions {
	display: flex;
	width: 5%;

	i {
		cursor: pointer;
		padding: 40px 5px 0px 5px;
	}

	.pct-trash-2 {
		color: $hellboyPri;
	}
}

.cdk-drag-preview {
	background-color: $branco;
	z-index: 2147483547 !important;
	box-sizing: border-box;
	border-radius: 4px;
	box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
		0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.content-lista-produto.cdk-drop-list-dragging
	.row-produto:not(.cdk-drag-placeholder) {
	transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.nome {
	@extend .type-caption;
	color: $gelo04;
	min-height: 32px;
	line-height: 2em;
	padding-left: 3px;
}

.aux-wrapper {
	position: relative;
	display: flex;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}
}

::placeholder {
	color: $cinzaClaro02;
}

.pct-table {
	display: table;

	.pct-table-row {
		display: table-row;
		padding: 12px 0px;
		width: 100%;
		border-bottom: dashed 1px $cinza02;
		border-left: solid 5px $branco;

		.pct-drag {
			cursor: move;
			font-size: 32px;
			line-height: 2;
		}

		&:hover {
			.descricao-produto {
				color: $pretoPri;
			}
		}
	}

	.pct-table-columns-row {
		display: table-row;

		.pct-table-cell {
			display: table-cell;
		}
	}

	.pct-table-cell {
		display: table-cell;
	}
}

.situacao {
	display: inline-block;

	i {
		font-weight: bold;
		margin-left: 5px;
		cursor: pointer;
	}
}
