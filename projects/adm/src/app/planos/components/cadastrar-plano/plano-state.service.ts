import { Injectable } from "@angular/core";
import {
	NavegacaoPlano,
	NavegacaoPlanoAvancado,
	Plano,
	TipoPlano,
} from "../../plano.model";
import { BehaviorSubject, Observable } from "rxjs";

@Injectable({
	providedIn: "root",
})
export class PlanoStateService {
	passoAtual = 1;
	mapaNavegacao: NavegacaoPlano[] = [
		{
			tipoPlano: TipoPlano.PLANO_NORMAL,
			passos: [
				NavegacaoPlano.DADOS_BASICOS,
				NavegacaoPlano.DURACAO_VALORES,
				NavegacaoPlano.PRODUTOS_SERVICOS,
				NavegacaoPlano.DADOS_CONTRATUAIS,
				NavegacaoPlano.CONDICAO_PAGAMENTO,
			],
		},
		{
			tipoPlano: TipoPlano.PLANO_RECORRENCIA,
			passos: [
				NavegacaoPlano.DADOS_BASICOS,
				NavegacaoPlano.MODALIDADES,
				NavegacaoPlano.PRODUTOS_SERVICOS,
				NavegacaoPlano.DADOS_CONTRATUAIS,
			],
		},
		{
			tipoPlano: TipoPlano.PLANO_CREDITO,
			passos: [
				NavegacaoPlano.DADOS_BASICOS,
				NavegacaoPlano.MODALIDADES,
				NavegacaoPlano.DURACAO_VALORES,
				NavegacaoPlano.PRODUTOS_SERVICOS,
				NavegacaoPlano.DADOS_CONTRATUAIS,
				NavegacaoPlano.CONDICAO_PAGAMENTO,
			],
		},
		{
			tipoPlano: TipoPlano.PLANO_PERSONAL,
			passos: [NavegacaoPlano.DADOS_BASICOS, NavegacaoPlano.DADOS_CONTRATUAIS],
		},
		{
			tipoPlano: TipoPlano.PLANO_AVANCADO,
			passos: [
				NavegacaoPlanoAvancado.DADOS_BASICOS,
				NavegacaoPlanoAvancado.PACOTES,
				NavegacaoPlanoAvancado.MODALIDADES,
				NavegacaoPlanoAvancado.DURACAO_VALORES,
				NavegacaoPlanoAvancado.PRODUTOS_SERVICOS,
				NavegacaoPlanoAvancado.DADOS_CONTRATUAIS,
				NavegacaoPlanoAvancado.CONDICAO_PAGAMENTO,
			],
		},
	];
	private planoChanged: BehaviorSubject<Plano> = new BehaviorSubject<Plano>(
		new Plano()
	);
	public $planoChanged: Observable<Plano>;

	constructor() {
		this.$planoChanged = this.planoChanged.asObservable();
	}

	get plano(): Plano {
		return JSON.parse(sessionStorage.getItem("plano"));
	}

	set plano(plano: Plano) {
		sessionStorage.setItem("plano", JSON.stringify(plano));
	}

	updatePlanoSubject(plano: Plano) {
		this.planoChanged.next(plano);
	}

	removeFromSession() {
		sessionStorage.removeItem("plano");
	}

	proximoPasso(tipoPlano: TipoPlano): void {
		const tipoPlanoPassos = this.mapaNavegacao.find(
			(it) => it.tipoPlano === tipoPlano
		);
		const indexOf = tipoPlanoPassos.passos.indexOf(this.passoAtual);
		this.passoAtual = tipoPlanoPassos.passos[indexOf + 1];
	}

	passoAnterior(tipoPlano: TipoPlano): void {
		const tipoPlanoPassos = this.mapaNavegacao.find(
			(it) => it.tipoPlano === tipoPlano
		);
		const indexOf = tipoPlanoPassos.passos.indexOf(this.passoAtual);
		this.passoAtual = tipoPlanoPassos.passos[indexOf - 1];
	}

	updateState(plano: Plano) {
		if (plano) {
			this.plano = plano;
		}
	}

	updatePlanoObj() {
		if (this.plano) {
			return this.plano;
		}
		return new Plano();
	}
}
