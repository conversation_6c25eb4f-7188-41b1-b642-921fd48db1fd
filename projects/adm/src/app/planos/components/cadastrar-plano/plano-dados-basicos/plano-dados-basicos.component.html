<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-input
					id="input-plano-nome"
					i18n-label="@@plano:label-descricao"
					label="Nome"
					[control]="formGroup.get('descricao')"
					i18n-errorMsg="@@plano:error-msg-descricao"
					errorMsg="Forneça um nome para o plano."
					maxlength="200"></pacto-cat-form-input>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select
					id="select-tipo-plano"
					i18n-label="@@plano:label-tipo-plano"
					label="Tipo de plano"
					[control]="formGroup.get('tipoPlano')"
					i18n-errorMsg="@@plano:error-msg-tipo-plano"
					errorMsg="Tipo de plano"
					[items]="tiposPlano"
					disabled="true"></pacto-cat-form-select>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					id="select-produto-contrato-plano"
					i18n-label="@@plano:label-produto-contrato"
					label="Produto padrão gerar parcelas contrato"
					[control]="formGroup.get('produtoContrato')"
					i18n-errorMsg="@@plano:error-msg-produto-contrato"
					errorMsg="Selecione um produto"
					[paramBuilder]="produtoelectBuilder"
					idKey="codigo"
					labelKey="descricao"
					[options]="produtosContrato"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					id="datepicker-vigenciade-plano"
					i18n-label="@@plano:label-vigenciade"
					label="Plano ativo a partir de"
					i18n-errorMsg="@@plano:error-msg-vigenciade"
					errorMsg="Forneça uma data válida."
					[control]="formGroup.get('vigenciaDe')"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					id="datepicker-ingresso-plano"
					i18n-label="@@plano:label-ingressoate"
					label="Permite matrículas/rematrículas até"
					i18n-errorMsg="@@plano:error-msg-ingressoate"
					errorMsg="Forneça uma data válida."
					[control]="formGroup.get('ingressoAte')"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					id="datepicker-vigenciaate-plano"
					i18n-label="@@plano:label-vigenciaate"
					label="Permite renovação atés"
					i18n-errorMsg="@@plano:error-msg-vigenciaate"
					errorMsg="Forneça uma data válida."
					[control]="formGroup.get('vigenciaAte')"></pacto-cat-form-datepicker>
			</div>
		</div>
		<div class="row" *ngIf="planoDTO.tipoPlano === 'PLANO_AVANCADO'">
			<div class="col-md-6 mb-2">
				<pacto-cat-checkbox
					id="check-restringe-venda-por-categoria"
					[control]="formGroup.get('restringeVendaPorCategoria')"
					i18n-label="@@plano:label-restringe-venda-por-categoria"
					label="Restringir venda por categoria do cliente: "></pacto-cat-checkbox>
			</div>
		</div>
		<div class="row">
			<div class="col-md-5">
				<pacto-cat-button
					(click)="configuracoesPlano()"
					id="btn-advanced-config-plano"
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					[label]="traducoes.getLabel('advanced-config')"></pacto-cat-button>
			</div>

			<div class="col-md-4">
				<pacto-cat-checkbox
					*ngIf="formGroup.get('tipoPlano').value === 'PLANO_CREDITO'"
					id="check-gerar-parcelas-valor-diferente-plano"
					[control]="formGroup.get('gerarParcelasValorDiferente')"
					i18n-label="@@plano:label-gerar-parcelas-valor-diferente"
					label="Modalidades com valores diferentes"></pacto-cat-checkbox>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducoes>
	<span xingling="PN" i18n="@@plano:tipo-normal">Plano Normal</span>
	<span xingling="PR" i18n="@@plano:tipo-recorrencia">Plano Recorrência</span>
	<span xingling="PC" i18n="@@plano:tipo-credito">Plano Crédito</span>
	<span xingling="PP" i18n="@@plano:tipo-personal">Plano Personal</span>
	<span xingling="PA" i18n="@@plano:tipo-avancado">
		Plano Normal (Avançado)
	</span>

	<span xingling="advanced-config" i18n="@@adm:advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
