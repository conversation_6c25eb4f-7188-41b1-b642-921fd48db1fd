import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { AdmRestService } from "../../../adm-rest.service";
import { Observable } from "rxjs";
import { ApiResponseList, ApiResponseSingle } from "sdk";
import { PlanoRedeEmpresa } from "../../services/plano-rede-empresa.model";
import { map } from "rxjs/operators";
import { PlanoConfiguracaoSistema } from "../../services/plano-configuracao-sistema.model";

@Injectable({
	providedIn: "root",
})
export class PlanoService {
	urlPlano: string;
	urlProdutos: string;

	constructor(private http: HttpClient, private admRest: AdmRestService) {
		this.urlPlano = this.admRest.buildFullUrlPlano("planos");
		this.urlProdutos = this.admRest.buildFullUrlPlano("produtos");
	}

	findById(idPlano): Observable<any> {
		return this.http.get(`${this.urlPlano}/${idPlano}`);
	}

	save(plano): Observable<any> {
		return this.http.post(`${this.urlPlano}`, plano);
	}

	delete(idPlano): Observable<any> {
		return this.http.delete(`${this.urlPlano}/${idPlano}`);
	}

	clone(code: number) {
		return this.http.get<any>(`${this.urlPlano}/${code}/clonar`);
	}

	findByIdCondicaoPagamento(idCondicaoPagamento): Observable<any> {
		const url = `${this.urlPlano}/find-by-condicao-pagamento?idCondicaoPagamento=${idCondicaoPagamento}`;
		return this.http.get(url);
	}

	replicar(
		codigoPlanoOrigin: number,
		chaveDestino: string,
		empresaDestino: number
	): Observable<PlanoRedeEmpresa> {
		const url = this.admRest.buildFullUrlPlano(
			`planoRedeEmpresa/replicar/${codigoPlanoOrigin}/${chaveDestino}/${empresaDestino}`
		);
		return this.http.post(url, null).pipe(
			map((response: ApiResponseSingle<PlanoRedeEmpresa>) => {
				return response.content;
			})
		);
	}

	removerVinculoReplicacao(
		codigoPlanoOrigin: number,
		chaveDestino: string,
		empresaDestino: number
	): Observable<PlanoRedeEmpresa> {
		const url = this.admRest.buildFullUrlPlano(
			`planoRedeEmpresa/removerVinculoReplicacao/${codigoPlanoOrigin}/${chaveDestino}/${empresaDestino}`
		);
		return this.http.post(url, null).pipe(
			map((response: ApiResponseSingle<PlanoRedeEmpresa>) => {
				return response.content;
			})
		);
	}

	replicarAutomaticoTodosReplicados(
		codigoPlanoOrigin: number
	): Observable<PlanoRedeEmpresa> {
		const url = this.admRest.buildFullUrlPlano(
			`planoRedeEmpresa/replicarAutomaticoTodosReplicados/${codigoPlanoOrigin}`
		);
		return this.http.post(url, null).pipe(
			map((response: ApiResponseSingle<PlanoRedeEmpresa>) => {
				return response.content;
			})
		);
	}

	planoReplicados(codigoPlanoOrigin: number): Observable<PlanoRedeEmpresa[]> {
		const url = this.admRest.buildFullUrlPlano(
			`planoRedeEmpresa/plano/${codigoPlanoOrigin}`
		);
		return this.http.get(url).pipe(
			map((response: ApiResponseList<PlanoRedeEmpresa>) => {
				return response.content;
			})
		);
	}

	configuracaoSistema(): Observable<PlanoConfiguracaoSistema> {
		const url = this.admRest.buildFullUrlPlano(`configuracaoSistema`);
		return this.http.get(url).pipe(
			map((response: ApiResponseSingle<PlanoConfiguracaoSistema>) => {
				return response.content;
			})
		);
	}

	findAllOnlyCodName(filters: any, paginador: any): Observable<any> {
		const params: any = {
			filters: JSON.stringify(filters),
			page: paginador.page,
			size: paginador.size,
			sort: paginador.sort,
		};
		const url = `${this.urlPlano}/only-cod-name`;
		return this.http.get(url, { params });
	}

	consultarQuantidadeContratosAtivosPorPlano(idPlano): Observable<any> {
		const url = `${this.urlPlano}/consultar-quantidade-contratos-ativos-por-plano?idPlano=${idPlano}`;
		return this.http.get(url);
	}

	atualizarParaAvancado(codigo: number): Observable<any> {
		const url = `${this.urlPlano}/atualizar-plano-para-avancado?codigo=${codigo}`;
		return this.http.put(url, {}).pipe(
			map((response: ApiResponseSingle<PlanoConfiguracaoSistema>) => {
				return response.content;
			})
		);
	}

	validarExisteContratoDuracao(
		codigoDoPlano: number,
		duracaoModalidade: number
	): Observable<any> {
		const params: any = {
			codigoPlano: codigoDoPlano,
			duracao: duracaoModalidade,
		};
		const url = `${this.urlPlano}/validar-contrato-duracao`;
		return this.http.get(url, { params });
	}

	aplicarParaContratosJaLancados(planoContrato: any): Observable<any> {
		const url = `${this.urlPlano}/aplicar-para-contratos-lancados`;
		return this.http.post(url, planoContrato);
	}
}
