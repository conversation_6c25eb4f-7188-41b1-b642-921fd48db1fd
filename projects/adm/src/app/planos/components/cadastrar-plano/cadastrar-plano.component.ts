import {
	AfterContentInit,
	ChangeDetectorRef,
	Component,
	HostListener,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Plano, TipoPlano } from "../../plano.model";
import { PlanoStateService } from "./plano-state.service";
import { PlanoService } from "./plano.service";
import {
	PlanoApiCondicaoPagamentoService,
	PlanoApiProdutoService,
} from "plano-api";
import { PlanoDadosBasicosComponent } from "./plano-dados-basicos/plano-dados-basicos.component";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "../../../adm-rest.service";
import { PlanoDadosContratuaisComponent } from "./plano-dados-contratuais/plano-dados-contratuais.component";
import { PlanoRecorrenciaDadosContratuaisComponent } from "../recorrencia/plano-recorrencia-dados-contraturais/plano-recorrencia-dados-contratuais.component";
import { PlanoPersonalDadosContratuaisComponent } from "../personal/plano-personal-dados-contratuais/plano-personal-dados-contratuais.component";
import { PlanoCommonsService } from "../../services/plano-commons.service";
import { TraducoesXinglingComponent, CaptalizePipe } from "ui-kit";

@Component({
	selector: "adm-cadastrar-plano",
	templateUrl: "./cadastrar-plano.component.html",
	styleUrls: ["./cadastrar-plano.component.scss"],
})
export class CadastrarPlanoComponent implements OnInit, AfterContentInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("dadosBasicosComponent", { static: false })
	dadosBasicosComponent: PlanoDadosBasicosComponent;
	@ViewChild("dadosContratuaisComponent", { static: false })
	dadosContratuaisComponent: PlanoDadosContratuaisComponent;
	@ViewChild("recorrenciaDadosContratuaisComponent", { static: false })
	recorrenciaDadosContratuaisComponent: PlanoRecorrenciaDadosContratuaisComponent;
	@ViewChild("personalDadosContratuaisComponent", { static: false })
	personalDadosContratuaisComponent: PlanoPersonalDadosContratuaisComponent;
	isSaving = false;
	loading = false;
	planoDTO: Plano;
	tipoPlano = TipoPlano;
	duracoes: Array<any> = new Array<any>();
	condicoesPagamentos: Array<any> = new Array<any>();
	form: FormGroup = new FormGroup({
		teste: new FormControl(),
	});
	formGroup: FormGroup = new FormGroup({
		modeloContrato: new FormControl(),
		percentualCancelamento: new FormControl(),
		descontoRenovacaoAntecipada: new FormControl(),
		marcacaoAulasColetivas: new FormControl(),
		permiteAtestado: new FormControl(),
		dividirManutencaoParcelas: new FormControl(),
	});

	formBuilder: FormBuilder = new FormBuilder();
	disableNextDuracaoValor = false;
	disableNextProduto = false;
	disableNextCondicaoPagamento = false;
	planoNome = "";
	constructor(
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private cd: ChangeDetectorRef,
		public state: PlanoStateService,
		private planoService: PlanoService,
		private produtoService: PlanoApiProdutoService,
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		private admRest: AdmRestService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		if (!this.state.plano) {
			const clonar = this.activatedRoute.snapshot.queryParamMap.get("clonar");
			const code: number =
				+this.activatedRoute.snapshot.queryParamMap.get("code");
			if (clonar) {
				this.loading = true;
				this.planoService.clone(code).subscribe((response) => {
					this.planoDTO = response.content;
					this.state.plano = this.planoDTO;
					this.updateCondicaoPagamento();
					this.loading = false;
					setTimeout(() => {
						this.cd.detectChanges();
					}, 0);
				});
			}
		}
		if (this.state.plano) {
			this.planoDTO = this.state.plano;
			if (this.planoDTO.duracoes) {
				this.duracoes = this.planoDTO.duracoes.sort((a: any, b: any) => {
					if (+a.numeroMeses < +b.numeroMeses) {
						return -1;
					} else if (+a.numeroMeses > +b.numeroMeses) {
						return 1;
					}
					return 0;
				});
			}
		} else {
			this.planoDTO = new Plano();
		}
		this.loadForm();
		this.loadCondicoesPagamento();
	}

	ngAfterContentInit() {
		this.cd.detectChanges();
	}

	loadCondicoesPagamento() {
		this.condicaoPagamentoService
			.findByLessThenNrParcelas()
			.subscribe((response: any) => {
				this.condicoesPagamentos = response.content;
				this.updateCondicaoPagamento();
			});
	}

	private loadForm() {}

	updateCondicaoPagamento(event?) {
		if (event) {
			this.planoDTO = event.planoDTO;
		}
		if (this.planoDTO.tipoPlano === TipoPlano.PLANO_AVANCADO) {
			this.duracoes = this.planoDTO.duracoes;
		} else if (this.planoDTO.tipoPlano === TipoPlano.PLANO_NORMAL) {
			if (this.planoDTO.excecoes) {
				if (this.planoDTO.excecoes.length === 0) {
					this.planoDTO.duracoes = [];
					this.duracoes = new Array<any>();
				} else {
					this.planoDTO.excecoes.forEach((excecao) => {
						if (excecao.duracao !== "" && excecao.duracao > 0) {
							const condicoesFiltered = this.condicoesPagamentos.filter(
								(condicaoPagamento) =>
									condicaoPagamento.nrParcelas <= +excecao.duracao
							);
							const condicoesPagamentos = new Array<any>();
							const duracao2: any = {
								situacao: true,
							};
							const filterDuracoes = this.duracoes.filter(
								(dur) => +dur.numeroMeses === +excecao.duracao
							);
							if (filterDuracoes.length === 0) {
								duracao2.numeroMeses = excecao.duracao;
								condicoesFiltered.forEach((condicaoPG) => {
									condicoesPagamentos.push({
										condicaoPagamento: {
											codigo: condicaoPG.codigo,
											descricao: condicaoPG.descricao,
											nrParcelas: condicaoPG.nrParcelas,
										},
									});
								});
								duracao2.condicoesPagamento = condicoesPagamentos.sort(
									(a: any, b: any) => {
										if (
											a.condicaoPagamento.nrParcelas <
											b.condicaoPagamento.nrParcelas
										) {
											return -1;
										} else if (
											a.condicaoPagamento.nrParcelas >
											b.condicaoPagamento.nrParcelas
										) {
											return 1;
										}
										return 0;
									}
								);
								this.duracoes.push(duracao2);
								this.duracoes = this.duracoes.sort((a: any, b: any) => {
									if (+a.numeroMeses < +b.numeroMeses) {
										return -1;
									} else if (+a.numeroMeses > +b.numeroMeses) {
										return 1;
									}
									return 0;
								});
								this.planoDTO.duracoes = this.duracoes;
							}
						}
					});
					const duracoesAux = [];
					Object.assign(duracoesAux, this.duracoes);
					duracoesAux.forEach((v, index) => {
						if (
							!this.planoDTO.excecoes.some((v2) => v2.duracao === v.numeroMeses)
						) {
							this.duracoes.splice(index, 1);
						}
					});
					this.planoDTO.duracoes = this.duracoes;
				}
				this.state.plano = this.planoDTO;
			}
		}
	}

	voltarListagemPlanos() {
		this.state.removeFromSession();
		this.router.navigate(["adm", "planos"]);
	}

	voltar() {
		if (this.state.passoAtual <= 1) {
			this.voltarListagemPlanos();
		} else {
			this.planoDTO = this.formGroup.getRawValue();
			this.salvarStatePlano();
			this.state.passoAnterior(this.planoDTO.tipoPlano);
		}
	}

	avancar() {
		this.planoDTO = this.formGroup.getRawValue();
		this.salvarStatePlano();
		this.state.proximoPasso(this.planoDTO.tipoPlano);
	}

	salvarStatePlano() {
		this.state.plano = this.planoDTO;
	}

	save() {
		if (this.isSaving) return;
		this.loadDadosBasicoPlano();

		if (this.planoDTO.descricao && this.planoDTO.descricao.length > 200) {
			this.notificationService.error(
				this.traducoes.getLabel("descricao-length-error")
			);
			return;
		}

		this.planoDTO = this.state.plano;
		const {
			tipoPlano,
			diasVencimentoProrata,
			excecoes,
			bolsa,
			site,
			termoAceite,
			planoRecorrencia,
		} = this.planoDTO;

		this.configurarTipoPlano(tipoPlano);
		this.configurarDiasProrata(diasVencimentoProrata, tipoPlano);

		this.planoCommonsService.updateCondicoesPagamento(this.planoDTO);
		this.verificaModalidadeBolsa(excecoes, bolsa);

		if (!site && termoAceite) {
			this.planoDTO.termoAceite = null;
		}
		this.configurarParcelasAnuidade(planoRecorrencia);
		this.isSaving = true;

		this.planoService.save(this.sanitizeObject(this.planoDTO)).subscribe({
			next: (response) => this.onSaveSuccess(),
			error: (error) => this.onSaveError(error),
		});
	}

	private configurarTipoPlano(tipoPlano) {
		switch (tipoPlano) {
			case "PLANO_RECORRENCIA":
				this.planoDTO.regimeRecorrencia = true;
				break;
			case "PLANO_CREDITO":
				this.planoDTO.vendaCreditoTreino = true;
				break;
			case "PLANO_PERSONAL":
				this.planoDTO.planoPersonal = true;
				break;
		}
	}

	private configurarDiasProrata(diasVencimentoProrata, tipoPlano) {
		if (
			!diasVencimentoProrata &&
			(tipoPlano === TipoPlano.PLANO_PERSONAL ||
				tipoPlano === TipoPlano.PLANO_RECORRENCIA)
		) {
			this.planoCommonsService.initDiasProrata(this.planoDTO);
		}
	}

	private verificaModalidadeBolsa(excecoes, bolsa) {
		if (!excecoes) return;

		excecoes.forEach((excecao) => {
			if (excecao.vezesSemana && excecao.vezesSemana.codigo) {
				excecao.vezesSemana = excecao.vezesSemana.codigo;
			}
		});

		if (!bolsa) {
			let modalidadeValorNaoZerado = false;
			let semModalidade = true;

			for (const excecao of excecoes) {
				if (excecao.modalidade) {
					if (excecao.modalidade.valorMensal > 0) {
						modalidadeValorNaoZerado = true;
						break;
					}
					semModalidade = false;
				}
			}

			if (!semModalidade && !modalidadeValorNaoZerado) {
				this.notificationService.error(
					this.traducoes.getLabel("modalidade-zerada-hint"),
					{ timeout: 10000 }
				);
				this.notificationService.error(
					this.traducoes.getLabel("modalidade-zerada-error"),
					{ timeout: 10000 }
				);
				return;
			}
		}
	}

	private configurarParcelasAnuidade(planoRecorrencia) {
		if (!planoRecorrencia) return;

		if (planoRecorrencia.parcelarAnuidade) {
			planoRecorrencia.parcelaAnuidade = undefined;
		} else {
			planoRecorrencia.parcelasAnuidade = undefined;
		}

		if (planoRecorrencia.parcelasAnuidade) {
			planoRecorrencia.parcelasAnuidade.forEach((parcelaAnuidade) => {
				if (parcelaAnuidade.parcela && parcelaAnuidade.parcela.value) {
					parcelaAnuidade.parcela = parcelaAnuidade.parcela.value;
				}
			});
		}
		this.planoDTO.planoRecorrencia = planoRecorrencia;
	}

	private loadDadosBasicoPlano() {
		this.dadosBasicosComponent.formGroup.patchValue((value) => {
			this.planoDTO.descricao = value.descricao;
			this.planoDTO.tipoPlano = value.tipoPlano;
			this.planoDTO.vigenciaDe = value.vigenciaDe;
			this.planoDTO.ingressoAte = value.ingressoAte;
			this.planoDTO.vigenciaAte = value.vigenciaAte;
			this.planoDTO.produtoContrato = value.produtoContrato;
			this.planoDTO.restringeVendaPorCategoria =
				value.restringeVendaPorCategoria;
			this.planoDTO.planoRecorrencia = {
				gerarParcelasValorDiferente: value.gerarParcelasValorDiferente,
			};
		});
	}

	private onSaveSuccess() {
		this.admRest.notificarNovoPlanoAcesso("NOVO_PLANO_SALVAR");
		this.notificationService.success(this.traducoes.getLabel("saved-success"));
		this.voltarListagemPlanos();
		this.isSaving = false;
	}

	onSaveError(responseError) {
		this.isSaving = false;
		if (responseError.error && responseError.error.meta) {
			const error =
				responseError.error.meta.error || responseError.error.meta.messageValue;
			this.notificationService.error(error);
		}
	}

	private cadastrarPlano(tipoPlano: TipoPlano) {
		this.planoDTO.tipoPlano = tipoPlano;
		this.salvarStatePlano();
		this.cd.detectChanges();
	}

	cadastrarPlanoNormal() {
		this.cadastrarPlano(TipoPlano.PLANO_NORMAL);
	}

	cadastrarPlanoAvancado() {
		this.cadastrarPlano(TipoPlano.PLANO_AVANCADO);
	}

	cadastrarPlanoCredito() {
		this.cadastrarPlano(TipoPlano.PLANO_CREDITO);
	}

	cadastrarPlanoPersonal() {
		this.cadastrarPlano(TipoPlano.PLANO_PERSONAL);
	}

	cadastrarPlanoRecorrencia() {
		this.cadastrarPlano(TipoPlano.PLANO_RECORRENCIA);
	}

	exibirTabCondicaoPagamento() {
		return (
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_CREDITO ||
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_NORMAL ||
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_AVANCADO
		);
	}

	exibirTabPacotes() {
		return this.state.plano.tipoPlano === this.tipoPlano.PLANO_AVANCADO;
	}

	exibirTabModalidade() {
		return this.state.plano.tipoPlano === this.tipoPlano.PLANO_AVANCADO;
	}

	exibirTabCategoria() {
		return (
			this.state.plano.restringeVendaPorCategoria === true &&
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_AVANCADO
		);
	}

	getStepDuracaoTitulo() {
		let captalizePipe = new CaptalizePipe();
		let valorPipeado = `${captalizePipe.transform(this.planoNome)} / `;
		if (this.state.plano.tipoPlano === this.tipoPlano.PLANO_AVANCADO) {
			valorPipeado += this.traducoes.getLabel("steplabel-duracao");
		}

		if (
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_CREDITO ||
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_RECORRENCIA
		) {
			valorPipeado += this.traducoes.getLabel("steplabel-modalidade");
		} else {
			valorPipeado += this.traducoes.getLabel("steplabel-durval");
		}
		return valorPipeado;
	}

	getStepDuracaoDescricao() {
		if (this.state.plano.tipoPlano === this.tipoPlano.PLANO_AVANCADO) {
			return this.traducoes.getLabel("stepdescription-durval");
		}

		if (
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_CREDITO ||
			this.state.plano.tipoPlano === this.tipoPlano.PLANO_RECORRENCIA
		) {
			return this.traducoes.getLabel("stepdescription-modalidade");
		} else {
			return this.traducoes.getLabel("stepdescription-durval");
		}
	}

	isNextDuracaoDisabled() {
		const hasNoExcecoes =
			!this.state.plano.excecoes || this.state.plano.excecoes.length === 0;
		const hasNoDuracoes =
			!this.state.plano.duracoes && this.state.plano.duracoes.length === 0;
		return this.disableNextDuracaoValor || hasNoExcecoes || hasNoDuracoes;
	}

	@HostListener("window:popstate")
	back() {
		this.state.removeFromSession();
	}

	sanitizeObject(obj) {
		for (let key in obj) {
			if (
				obj[key] &&
				typeof obj[key] === "object" &&
				Object.keys(obj[key]).length === 0
			) {
				obj[key] = null; // ou um valor padrão como 0
			}
		}
		return obj;
	}
}
