<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row start-align">
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[addtionalFilters]="modeloContratoAddtionalFilters"
					[control]="formGroup.get('modeloContrato')"
					[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
					[idKey]="'codigo'"
					[labelKey]="'descricao'"
					[paramBuilder]="modeloContratoSelectBuilder"
					errorMsg="Selecione um modelo de contrato"
					i18n-errorMsg="@@plano:error-modelo-contrato"
					i18n-label="@@plano:label-modelo-contrato"
					id="select-modelo-contrato-plano"
					label="Modelo de contrato do plano"></pacto-cat-form-select-filter>

				<pacto-cat-button
					class="mx-auto d-block"
					(click)="aplicarContratosJaLancados()"
					label="Aplicar à contratos já lançados"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					ds3="true"
					id="btn-advanced-config-contratual-plano"></pacto-cat-button>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-input-number
					[formControl]="formGroup.get('percentualMultaCancelamento')"
					decimal="true"
					i18n-label="@@plano:label-percentual-multa-cancelamento"
					id="input-percentual-multa-cancelamento-plano"
					label="Percentual da multa de cancelamento"
					max="100"
					maxlength="5"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[control]="formGroup.get('produtoTaxaCancelamento')"
					[endpointUrl]="
						admRestService.buildFullUrlPlano(
							produtoService.produtosCancelamento
						)
					"
					[paramBuilder]="produtoSelectBuilder"
					errorMsg="Por favor selecione um produto"
					i18n-errorMsg="@@plano:error-produto-taxa-cancelamento"
					i18n-label="@@plano:label-produto-taxa-cancelamento"
					id="select-produto-taxa-cancelamento-plano"
					idKey="codigo"
					label="Produto taxa cancelamento:"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div class="row end-align">
			<div class="col-md-4">
				<pacto-cat-form-input-number
					[formControl]="formGroup.get('restringirQtdMarcacaoPorDiaGeral')"
					i18n-label="@@plano:label-restringir-uma-marcacao-por-dia-geral"
					id="input-restringir-marcacao-dia-geral-plano"
					label="Restringir quantidade marcação por dia de aula coletiva (geral)"
					max="99"
					maxlength="2"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-4">
				<pacto-cat-form-input-number
					[formControl]="formGroup.get('restringirQtdMarcacaoPorDia')"
					i18n-label="@@plano:label-restringir-uma-marcacao-por-dia"
					id="input-restringir-marcacao-dia-plano"
					label="Restringir quantidade marcação por dia de aula coletiva (por modalidade)"
					max="99"
					maxlength="2"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-4">
				<div>
					<label class="control-label">
						Limite de reposições para aula coletiva
					</label>
					<i
						class="pct pct-info info-reposicao"
						ngbTooltip='Limite "0" significa não gerar reposição para aula coletiva'
						i18n-ngbTooltip="@@plano:tooltip-limite-reposicao-aula-coletiva"
						tabindex="0"
						role="button"
						aria-label="Informação sobre limite de reposições para aula coletiva"></i>
				</div>
				<pacto-cat-form-input-number
					class="input-info"
					[formControl]="formGroup.get('limiteReposicaoAulaColetiva')"
					i18n-label="@@plano:label-limite-Reposicao-Aula-Coletiva"
					id="input-limite-Reposicao-Aula-Coletiva"
					max="99"
					maxlength="2"></pacto-cat-form-input-number>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-cat-checkbox
					[control]="formGroup.get('permiteSituacaoAtestadoContrato')"
					i18n-label="@@plano:label-permite-atestado-medico"
					id="check-permite-situacao-atestado-contrato-plano"
					label="Permite atestado médico"></pacto-cat-checkbox>
			</div>

			<div class="col-md-6">
				<pacto-cat-checkbox
					[control]="formGroup.get('restringirMarcacaoAulasColetivas')"
					i18n-label="@@plano:label-restringir-marcacao-aulas-coletivas"
					id="check-restringir-marcacao-aula-coletiva-plano"
					label="Restringir marcações de aulas coletivas (quantidade de vezes por semana da modalidade)"></pacto-cat-checkbox>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<pacto-cat-checkbox
					[control]="formGroup.get('dividirManutencaoParcelasEA')"
					i18n-label="@@plano:label-dividir-valor-manutencao-parcelas"
					id="check-dividir-manutencao-pacelas"
					label="Dividir valor da manutenção nas parcelas"></pacto-cat-checkbox>
			</div>

			<div class="col-md-6">
				<pacto-cat-checkbox
					[control]="
						formGroup.get('restringirMarcacaoAulaPorNrVezesModalidade')
					"
					i18n-label="@@plano:label-restringir-marcacao-contabilizando-dia"
					label="Validar vezes na semana para marcação de aula contabilizando no mesmo dia"></pacto-cat-checkbox>
			</div>
		</div>

		<div class="row">
			<div class="col-md-5">
				<pacto-cat-button
					(click)="configuracoesAvancadas()"
					[label]="traducao.getLabel('advanced-config')"
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					id="btn-advanced-config-contratual-plano"></pacto-cat-button>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:advanced-config" xingling="advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
