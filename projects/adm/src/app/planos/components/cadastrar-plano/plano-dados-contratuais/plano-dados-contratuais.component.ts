import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../plano-state.service";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PlanoAdvancedConfigComponent } from "../../plano-advanced-config/plano-advanced-config.component";
import { PlanoApiProdutoService } from "plano-api";
import { AdmRestService } from "../../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { PlanoService } from "../plano.service";

@Component({
	selector: "adm-plano-dados-contratuais",
	templateUrl: "./plano-dados-contratuais.component.html",
	styleUrls: ["./plano-dados-contratuais.component.scss"],
})
export class PlanoDadosContratuaisComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() plano: Plano;

	modelosContrato: Array<any> = new Array<any>();

	formGroup: FormGroup = new FormGroup({
		modeloContrato: new FormControl("", Validators.required),
		percentualMultaCancelamento: new FormControl(0),
		restringirQtdMarcacaoPorDia: new FormControl(),
		restringirMarcacaoAulasColetivas: new FormControl(),
		permiteSituacaoAtestadoContrato: new FormControl(),
		dividirManutencaoParcelasEA: new FormControl(),
		restringirMarcacaoAulaPorNrVezesModalidade: new FormControl(),
		produtoTaxaCancelamento: new FormControl(),
		restringirQtdMarcacaoPorDiaGeral: new FormControl(),
		limiteReposicaoAulaColetiva: new FormControl(),
	});
	produtosTaxaCancelamento: Array<any> = new Array<any>();
	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "PL",
	};
	modeloContratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};
	produtoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	constructor(
		public admRestService: AdmRestService,
		private planoState: PlanoStateService,
		private dialogService: DialogService,
		public produtoService: PlanoApiProdutoService,
		private notificationService: SnotifyService,
		private planoService: PlanoService
	) {}

	ngOnInit() {
		if (this.plano && !this.plano.codigo) {
			this.plano = this.planoState.plano;
		}
		if (this.plano) {
			this.formGroup.patchValue({
				modeloContrato: this.plano.modeloContrato,
				percentualMultaCancelamento:
					this.plano.percentualMultaCancelamento === undefined
						? 0
						: this.plano.percentualMultaCancelamento,
				restringirQtdMarcacaoPorDia: this.plano.restringirQtdMarcacaoPorDia,
				restringirMarcacaoAulasColetivas:
					this.plano.restringirMarcacaoAulasColetivas,
				permiteSituacaoAtestadoContrato:
					this.plano.permiteSituacaoAtestadoContrato,
				dividirManutencaoParcelasEA: this.plano.dividirManutencaoParcelasEA,
				restringirMarcacaoAulaPorNrVezesModalidade:
					this.plano.restringirMarcacaoAulaPorNrVezesModalidade,
				produtoTaxaCancelamento: this.plano.produtoTaxaCancelamento,
				restringirQtdMarcacaoPorDiaGeral:
					this.plano.restringirQtdMarcacaoPorDiaGeral,
				limiteReposicaoAulaColetiva: this.plano.limiteReposicaoAulaColetiva,
			});
		}
		this.formGroup.valueChanges.subscribe((value) => {
			if (this.plano) {
				this.plano = this.planoState.plano;
			}
			this.plano.modeloContrato = value.modeloContrato;
			this.plano.percentualMultaCancelamento =
				value.percentualMultaCancelamento;
			this.plano.restringirQtdMarcacaoPorDia =
				value.restringirQtdMarcacaoPorDia;
			this.plano.restringirMarcacaoAulasColetivas =
				value.restringirMarcacaoAulasColetivas;
			this.plano.permiteSituacaoAtestadoContrato =
				value.permiteSituacaoAtestadoContrato;
			this.plano.dividirManutencaoParcelasEA =
				value.dividirManutencaoParcelasEA;
			this.plano.restringirMarcacaoAulaPorNrVezesModalidade =
				value.restringirMarcacaoAulaPorNrVezesModalidade;
			this.plano.produtoTaxaCancelamento = value.produtoTaxaCancelamento;
			this.plano.restringirQtdMarcacaoPorDiaGeral =
				value.restringirQtdMarcacaoPorDiaGeral;
			this.plano.limiteReposicaoAulaColetiva =
				value.limiteReposicaoAulaColetiva;
			this.planoState.plano = this.plano;
		});
		this.loadProdutosCancelamento();
	}

	loadProdutosCancelamento() {
		this.produtoService.getProdutosCancelamento().subscribe((response: any) => {
			this.produtosTaxaCancelamento = response.content;
			if (
				(this.planoState.plano && this.planoState.plano.codigo === undefined) ||
				this.planoState.plano.codigo === null
			) {
				this.formGroup
					.get("produtoTaxaCancelamento")
					.setValue(
						this.produtosTaxaCancelamento.find(
							(prod) =>
								prod.descricao.toUpperCase() ===
								"CUSTO ADMINISTRATIVO DO CANCELAMENTO"
						)
					);
			}
		});
	}

	configuracoesAvancadas() {
		const dialogRef = this.dialogService.open(
			this.traducao.getLabel("advanced-config"),
			PlanoAdvancedConfigComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoState.plano;
		dialogRef.componentInstance.mostrarAbaFerias = true;
		dialogRef.componentInstance.configRecorrenciaDadosContratuais =
			this.planoState.plano.tipoPlano === "PLANO_RECORRENCIA";
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {
					this.planoState.plano = value;
				})
				.catch((error) => {
					console.log(error);
				});
		}
	}

	aplicarContratosJaLancados() {
		if (!this.plano.modeloContrato) {
			this.notificationService.error("Selecione um modelo de contrato");
			return;
		}

		const dialogRef = this.dialogService.confirm(
			"Confirmar alteração para contratos já lançados!",
			"Os contratos já lançados serão alterados permanentemente. Deseja continuar?",
			"Confirmar"
		);
		dialogRef.result.then((result) => {
			this.planoService
				.aplicarParaContratosJaLancados({
					codigo: this.plano.codigo,
					modeloDeContrato: this.plano.modeloContrato.codigo,
					tipoPlano: this.plano.tipoPlano,
				})
				.subscribe(
					(response) => {
						this.notificationService.success(
							"Texto aplicado para contratos já lançados"
						);
					},
					(httpResponseError) => {
						if (httpResponseError.error && httpResponseError.error.meta) {
							this.notificationService.error(
								httpResponseError.error.meta.message
							);
						} else {
							console.log(httpResponseError);
						}
					}
				);
		});
	}
}
