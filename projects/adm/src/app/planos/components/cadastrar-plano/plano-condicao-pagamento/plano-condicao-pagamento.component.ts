import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { PlanoStateService } from "../plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { DecimalPipe } from "@angular/common";
import { SnotifyService } from "ng-snotify";
import { PlanoApiCondicaoPagamentoService } from "plano-api";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-plano-condicao-pagamento",
	templateUrl: "./plano-condicao-pagamento.component.html",
	styleUrls: ["./plano-condicao-pagamento.component.scss"],
})
export class PlanoCondicaoPagamentoComponent implements AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCondicaoPagamento", { static: true })
	columnCondicaoPagamento: TemplateRef<any>;
	@ViewChild("columnTipoOperacao", { static: true })
	columnTipoOperacao: TemplateRef<any>;
	@ViewChild("columnTipoValor", { static: true })
	columnTipoValor: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@Input() duracao;
	@Input() planoDTO;
	@Input() condicoesPagamento: Array<{
		codigo: number;
		descricao: string;
	}> = new Array<{ codigo: number; descricao: string }>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	tiposOperacoes: Array<{ codigo: string; label: string }> = new Array<{
		codigo: string;
		label: string;
	}>();
	tiposValor: Array<{ codigo: string; label: string }> = new Array<{
		codigo: string;
		label: string;
	}>();

	table: PactoDataGridConfig;

	constructor(
		private cd: ChangeDetectorRef,
		private admRestService: AdmRestService,
		private state: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private decimalPipe: DecimalPipe,
		private notificationService: SnotifyService
	) {}

	ngAfterViewInit() {
		this.initTiposOperacoes();
		this.initTiposValor();
		this.initTableDuracaoValor();
		if (this.duracao && this.duracao.condicoesPagamento) {
			this.duracao.condicoesPagamento.sort((a, b) => {
				if (a.codigo < b.codigo) {
					return -1;
				} else if (a.codigo > b.codigo) {
					return 1;
				}
				return 0;
			});
			this.duracao.condicoesPagamento.forEach((condicaoPagamento) => {
				if (typeof condicaoPagamento.tipoValor === "string") {
					condicaoPagamento.tipoValor = this.tiposValor.find(
						(v) => v.codigo === condicaoPagamento.tipoValor
					);
				}
				if (typeof condicaoPagamento.tipoOperacao === "string") {
					condicaoPagamento.tipoOperacao = this.tiposOperacoes.find(
						(v) => v.codigo === condicaoPagamento.tipoOperacao
					);
				}
			});
			this.table.dataAdapterFn = (serverData) => {
				serverData.content = this.duracao.condicoesPagamento;
				return serverData;
			};
		}
	}

	private initTableDuracaoValor() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				condicaoPagamento: new FormControl(),
				tipoOperacao: new FormControl(),
				tipoValor: new FormControl(),
				valor: new FormControl(),
			}),
			beforeConfirm: (row, form, data, indexRow) =>
				this.planoCommonsService.beforeConfirmCondicaoPagamento(
					row,
					form,
					data,
					indexRow,
					this.notificationService
				),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					editable: false,
					ordenavel: false,
				},
				{
					nome: "condicaoPagamento",
					titulo: this.columnCondicaoPagamento,
					inputType: "select",
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					endpointUrl:
						this.admRestService.buildFullUrlPlano("condicoesPagamento") +
						"/find",
					showAddSelectBtn: false,
					editable: true,
					visible: true,
					showSelectFilter: true,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					ordenavel: false,
				},
				{
					nome: "tipoOperacao",
					titulo: this.columnTipoOperacao,
					visible: true,
					inputType: "select",
					inputSelectData: this.tiposOperacoes,
					showAddSelectBtn: false,
					showSelectFilter: false,
					editable: true,
					ordenavel: false,
					addEmptyOption: true,
				},
				{
					nome: "tipoValor",
					titulo: this.columnTipoValor,
					visible: true,
					editable: true,
					inputType: "select",
					ordenavel: false,
					showAddSelectBtn: false,
					inputSelectData: this.tiposValor,
					addEmptyOption: true,
					showSelectFilter: false,
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					inputType: "decimal",
					decimal: true,
					visible: true,
					editable: true,
					ordenavel: false,
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
			],
		});
		this.cd.detectChanges();
	}

	confirm(obj) {
		this.planoDTO = this.state.plano;
		const finded = this.planoDTO.duracoes.find(
			(dur) => +dur.numeroMeses === +this.duracao.numeroMeses
		);
		if (finded) {
			finded.condicoesPagamento = this.duracao.condicoesPagamento;
			finded.tipoOperacao = this.duracao.tipoOperacao;
			finded.tipoValor = this.duracao.tipoValor;
		}
		this.state.plano = this.planoDTO;
	}

	delete(event) {
		if (this.duracao.condicoesPagamento) {
			let index;
			if (event.row.codigo) {
				const excecao = this.duracao.condicoesPagamento.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.duracao.condicoesPagamento.splice(index, 1);
				}
			} else {
				index = event.index;
				this.duracao.condicoesPagamento.splice(index, 1);
			}
			this.planoDTO = this.state.plano;
			const finded = this.planoDTO.duracoes.find(
				(dur) => +dur.numeroMeses === +this.duracao.numeroMeses
			);
			if (finded) {
				finded.condicoesPagamento = this.duracao.condicoesPagamento;
				this.state.plano = this.planoDTO;
			}
		}
		this.planoDTO = this.state.updatePlanoObj();
		if (this.planoDTO.duracoes) {
			let index;
			this.planoDTO.duracoes.find((dur, i) => {
				if (dur.numeroMeses === this.duracao.numeroMeses) {
					index = i;
					return dur;
				}
			});
			if (index >= 0) {
				this.planoDTO.duracoes[index] = this.duracao;
				this.state.updateState(this.planoDTO);
			}
		}
	}

	private initTiposOperacoes() {
		this.tiposOperacoes.push(
			{
				label: this.traducao.getLabel("AC"),
				codigo: "AC",
			},
			{
				label: this.traducao.getLabel("RE"),
				codigo: "RE",
			}
		);
	}

	private initTiposValor() {
		this.tiposValor.push(
			{
				label: this.traducao.getLabel("VE"),
				codigo: "VE",
			},
			{
				label: this.traducao.getLabel("PD"),
				codigo: "PD",
			}
		);
	}
}
