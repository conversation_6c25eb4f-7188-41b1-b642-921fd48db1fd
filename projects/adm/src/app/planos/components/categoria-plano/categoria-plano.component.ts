import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Plano, PlanoCategoria } from "../../plano.model";
import {
	TraducoesXinglingComponent,
	CatTableEditableComponent,
	PactoDataGridConfig,
} from "ui-kit";
import { Categoria } from "cadastro-aux-api";
import { FormGroup, FormControl } from "@angular/forms";
import { ClientDiscoveryService } from "sdk";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { Observable, of } from "rxjs";
import { SnotifyService } from "ng-snotify";
import { getTipoCategoriaLabelByKey } from "../../../cadastro-auxliar/enums/TipoCategoria";

@Component({
	selector: "adm-categoria-plano",
	templateUrl: "./categoria-plano.component.html",
	styleUrls: ["./categoria-plano.component.scss"],
})
export class CategoriaPlanoComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("table", { static: false }) table: CatTableEditableComponent;
	@ViewChild("tipoCategoria", { static: true })
	tipoCategoriaCelula: TemplateRef<any>;

	@Input() planoDTO: Plano;
	@Input() codigo: number;
	@Input() formDadosBasicos: FormGroup;

	gridConfig: PactoDataGridConfig;
	categoriasSelecionadas: Categoria[] = [];

	categoriaFormGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		nome: new FormControl(),
		tipoCategoria: new FormControl(),
	});

	constructor(
		private discoveryService: ClientDiscoveryService,
		private planoState: PlanoStateService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.initGridConfig();
		this.cd.detectChanges();
	}

	ngAfterViewInit(): void {
		if (this.planoDTO.categorias) {
			this.categoriasSelecionadas = this.planoDTO.categorias.map(
				(cat) => cat.categoria
			);
			this.gridConfig.dataAdapterFn = (serverData) => {
				serverData.content = this.planoDTO.categorias.map(
					(categoria) => categoria.categoria
				);
				return serverData;
			};
		}
	}

	initGridConfig() {
		const planoParam = this.codigo ? `?plano=${this.codigo}` : "";
		this.gridConfig = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: `${
				this.discoveryService.getUrlMap().planoMsUrl
			}/planos/categorias${planoParam}`,
			endpointParamsType: "query",
			formGroup: this.categoriaFormGroup,
			onAddFn: (row, data, indexRow) => {
				this.planoDTO = this.planoState.plano;
				if (data[indexRow].nome === null) {
					data.splice(indexRow, 1);
					this.notificationService.error("Adicione uma categoria válida");
					return of(false);
				}
				if (!this.planoDTO.categorias) {
					this.planoDTO.categorias = [];
				}
				const categoria = data[indexRow].nome;

				this.categoriasSelecionadas.push(categoria);
				if (this.formDadosBasicos) {
					this.formDadosBasicos
						.get("categoriasPlano")
						.setValue(this.categoriasSelecionadas);
				}
				this.planoDTO.categorias.push(new PlanoCategoria(null, categoria));
				this.planoState.updateState(this.planoDTO);
				return data;
			},
			onEditFn: (row, data, index) => {
				this.planoDTO = this.planoState.plano;
				this.categoriasSelecionadas = data.map((p) => {
					if (typeof p.nome === "string") return p;
					else return p.nome;
				});
				if (this.formDadosBasicos) {
					this.formDadosBasicos
						.get("categoriasPlano")
						.setValue(this.categoriasSelecionadas);
				}
				this.planoDTO.categorias = this.categoriasSelecionadas.map(
					(categoria) => new PlanoCategoria(null, categoria)
				);
				this.planoState.updateState(this.planoDTO);
				return data;
			},
			onDeleteFn: (row, data, indexRow) => this.deletar(row, data, indexRow),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			dataAdapterFn(serverData) {
				this.categoriasSelecionadas = serverData.content;
				return serverData.content;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: false,
					ordenavel: false,
					editable: false,
				},
				{
					nome: "nome",
					titulo: "Categoria",
					endpointUrl:
						this.discoveryService.getUrlMap().planoMsUrl + "/categorias",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					showEmptyMessage: true,
					width: "40%",
					labelFn: (row) => {
						return row.nome === undefined ? row : row.nome;
					},
					selectParamBuilder: (param) => {
						return {
							page: "0",
							size: "10",
							filters: JSON.stringify({
								quicksearchValue: param,
								codigosNaoConsultar: this.categoriasSelecionadas.map(
									(cat) => cat.codigo
								),
							}),
						};
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
				},
				{
					nome: "tipoCategoria",
					titulo: "Tipo",
					visible: true,
					ordenavel: false,
					editable: false,
					valueTransform: (v, row) => {
						if (row.nome.tipoCategoria) {
							return getTipoCategoriaLabelByKey(
								row.nome.tipoCategoria,
								this.traducao
							);
						}
						return getTipoCategoriaLabelByKey(v, this.traducao);
					},
					width: "40%",
				},
			],
		});
	}

	deletar(row: any, data: any, indexRow: any): Observable<boolean> {
		this.planoDTO = this.planoState.plano;
		const index = this.planoDTO.categorias.findIndex((p) => {
			if (row.codigo) {
				return p.categoria.codigo === row.codigo;
			} else {
				return p.categoria.codigo === row.nome.codigo;
			}
		});
		if (index == -1) return of(false);
		this.planoDTO.categorias.splice(index, 1);
		this.planoState.plano = this.planoDTO;
		this.categoriasSelecionadas = this.planoDTO.categorias.map(
			(cat) => cat.categoria
		);
		if (this.formDadosBasicos) {
			this.formDadosBasicos
				.get("categoriasPlano")
				.setValue(this.categoriasSelecionadas);
		}
		data.splice(indexRow, 1);
		return data;
	}

	showDelete(row: any, isAdd: any): boolean {
		if (isAdd) return false;
		return true;
	}
}
