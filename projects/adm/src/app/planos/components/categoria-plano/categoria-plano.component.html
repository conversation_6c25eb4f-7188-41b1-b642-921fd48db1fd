<pacto-cat-table-editable
	id="table-categoria"
	#table
	[showAddRow]="true"
	[isEditable]="true"
	[showEdit]="true"
	[showDelete]="true"
	[newLineTitle]="'Adicionar Categoria'"
	[actionTitle]="'Ações'"
	accesskey="Ações"
	[table]="gridConfig"></pacto-cat-table-editable>
<ng-template #tipoCategoria let-item="item">
	<div>
		{{ item.tipoCategoria }}
	</div>
</ng-template>
<pacto-traducoes-xingling #traducao>
	<span xingling="NS" i18n="@@categoria:naoSocio">Não Sócio</span>
	<span xingling="AL" i18n="@@categoria:aluno">Aluno</span>
	<span xingling="SO" i18n="@@categoria:socio">Sócio</span>
	<span xingling="CO" i18n="@@categoria:comerciario">Comerciário</span>
	<span xingling="DE" i18n="@@categoria:dependente">Dependente</span>
	<span xingling="US" i18n="@@categoria:aluno">Usuário</span>
	<span xingling="EV" i18n="@@categoria:eventos">Eventos</span>
</pacto-traducoes-xingling>
