import {
	AfterViewInit,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { DecimalPipe } from "@angular/common";
import { PlanoApiProdutoService } from "plano-api";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pc-produtos",
	templateUrl: "./pc-produtos.component.html",
	styleUrls: ["./pc-produtos.component.scss"],
})
export class PcProdutosComponent implements OnInit, AfterViewInit {
	@ViewChild("tableProdutos", { static: false })
	tableProdutos: CatTableEditableComponent;
	@ViewChild("columnSituacaoPlano", { static: true })
	columnSituacaoPlano: TemplateRef<any>;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnValorProduto", { static: true })
	columnValorProduto: TemplateRef<any>;
	@ViewChild("columnObrigatorio", { static: true })
	columnObrigatorio: TemplateRef<any>;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	@Output() isEditinOrAdding: EventEmitter<any> = new EventEmitter<any>();
	produtos: Array<any> = [];
	table: PactoDataGridConfig;
	plano: Plano;

	produtosPadroes: Array<any> = new Array<any>();

	constructor(
		private produtoService: PlanoApiProdutoService,
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe,
		private admRest: AdmRestService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.initTable();

		this.table.showDelete = (row, isAdd) => this.showDelete(row, isAdd);
	}

	ngAfterViewInit() {
		if (!this.plano.produtosSugeridos) {
			this.plano.produtosSugeridos = new Array<any>();
		}
		if (this.plano.codigo === undefined) {
			this.produtoService.produtoPlanoBasico().subscribe(
				(data: any) => {
					const produtos = data.content;
					this.produtosPadroes = produtos;
					produtos.forEach((prod) => {
						if (this.plano.produtosSugeridos.length > 0) {
							const findProd = this.plano.produtosSugeridos.find(
								(v) => v.produto.codigo === prod.produto.codigo
							);
							if (!findProd) {
								this.plano.produtosSugeridos.push({
									codigo: "",
									produto: prod.produto,
									obrigatorio: prod.obrigatorio,
									valorProduto: prod.valorProduto,
									ativoPlano: true,
								});
							}
						} else {
							this.plano.produtosSugeridos.push({
								produto: prod.produto,
								obrigatorio: prod.obrigatorio,
								valorProduto: prod.valorProduto,
								ativoPlano: true,
							});
						}
					});
					this.produtos = this.plano.produtosSugeridos;
					this.planoStateService.plano = this.plano;
					this.table.dataAdapterFn = (serverData) => {
						return {
							content: this.produtos,
						};
					};
					if (this.tableProdutos) {
						this.tableProdutos.reloadData();
					}
				},
				(error) => {
					console.log("Erro ", error);
				}
			);
		} else {
			this.produtoService.produtoPlanoBasico().subscribe(
				(data: any) => {
					this.produtosPadroes = data.content;
					this.produtos = this.plano.produtosSugeridos;
					this.table.dataAdapterFn = (serverData) => {
						return {
							content: this.produtos,
						};
					};
					if (this.tableProdutos) {
						this.tableProdutos.reloadData();
					}
				},
				(error) => {
					console.log("Erro ", error);
				}
			);
		}
	}

	confirm(event) {
		this.plano = this.planoStateService.plano;
		if (
			event &&
			this.produtos &&
			event.row &&
			event.row.codigo &&
			event.row.codigo !== "" &&
			event.row.codigo !== undefined &&
			event.row.codigo !== null
		) {
			this.produtos.forEach((prod) => {
				if (prod.codigo === event.row.codigo) {
					prod.edit = false;
				}
			});
		}
		this.plano.produtosSugeridos = this.produtos;
		this.planoStateService.plano = this.plano;
	}

	delete(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.produtos) {
			let index;
			if (event.row.codigo) {
				const excecao = this.produtos.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.produtos.splice(index, 1);
				}
			} else {
				this.produtos.splice(event.index, 1);
			}
			this.plano.produtosSugeridos = this.produtos;
			this.planoStateService.plano = this.plano;
		}
	}

	showDelete(row, isAdd) {
		const finded = this.produtosPadroes.find((v) => {
			if (v.produto && row.produto && v.produto.codigo === row.produto.codigo) {
				return v;
			}
		});
		return !!!finded;
	}

	isProductDisabled(row) {
		const finded = this.produtosPadroes.find((v) => {
			if (v.produto && row.produto && v.produto.codigo === row.produto.codigo) {
				return v;
			}
		});
		return finded !== undefined;
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				produto: new FormControl("", Validators.required),
				valorProduto: new FormControl("", Validators.required),
				obrigatorio: new FormControl(false),
				ativoPlano: new FormControl(true),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmProdutos(row, form, data, rowIndex),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "produto",
					titulo: this.columnProduto,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					width: "200px",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					objectAttrLabelName: "descricao",
					endpointUrl: this.admRest.buildFullUrlPlano(
						this.produtoService.produtosVendaveis
					),
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					selectOptionChange: (option, form, row) => {
						const clean = option.valorFinal.toString().replace(".", ",");
						form.get("valorProduto").setValue(clean);
					},
					isDisabled: (row) => this.isProductDisabled(row),
					showSelectFilter: true,
					showAddSelectBtn: false,
				},
				{
					nome: "valorProduto",
					titulo: this.columnValorProduto,
					visible: true,
					ordenavel: false,
					editable: true,
					width: "137px",
					inputType: "decimal",
					decimal: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
				},
				{
					nome: "obrigatorio",
					titulo: this.columnObrigatorio,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					width: "100px",
					isDisabled: (row) => this.isProductDisabled(row),
					valueTransform: (v) =>
						v
							? this.traducoes.getLabel("label-sim")
							: this.traducoes.getLabel("label-nao"),
				},
				{
					nome: "ativoPlano",
					titulo: this.columnSituacaoPlano,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					valueTransform: (v) =>
						v || v === undefined
							? this.traducoes.getLabel("label-ativo")
							: this.traducoes.getLabel("label-inativo"),
				},
			],
		});
	}

	private beforeConfirmProdutos(row: any, form: any, data: any, indexRow: any) {
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			indexRow
		);
		if (!exists) {
			const produto = form.get("produto").value;
			const valorProduto = form.get("valorProduto").value;
			const obrigatorio = form.get("obrigatorio").value;
			const ativoPlano = form.get("ativoPlano").value;
			if (obrigatorio === undefined || obrigatorio === null) {
				form.get("obrigatorio").setValue(false);
				row.obrigatorio = false;
			}
			if (ativoPlano === undefined || ativoPlano === null) {
				form.get("ativoPlano").setValue(true);
				row.ativoPlano = true;
			}
			if (
				produto === null ||
				produto === undefined ||
				produto === "" ||
				valorProduto === null ||
				valorProduto === undefined ||
				valorProduto === ""
			) {
				this.notificationService.error(
					this.traducoes.getLabel("error-table-data-empty")
				);
				return false;
			}
			if (
				data.find(
					(d, index) =>
						index !== indexRow &&
						d.produto.codigo === form.get("produto").value.codigo
				)
			) {
				this.notificationService.error(
					this.traducoes.getLabel("produto-duplicado")
				);
				return false;
			}
			const produtosMatricula = data.filter(
				(d) => d.produto.tipoProduto === "MA" && d.ativoPlano
			);
			if (produtosMatricula && produtosMatricula.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Matrícula ativos no plano!"
				);
				return false;
			}

			const produtosRematricula = data.filter(
				(d) => d.produto.tipoProduto === "RE" && d.ativoPlano
			);
			if (produtosRematricula && produtosRematricula.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Rematrícula ativos no plano!"
				);
				return false;
			}

			const produtosRenovacao = data.filter(
				(d) => d.produto.tipoProduto === "RN" && d.ativoPlano
			);
			if (produtosRenovacao && produtosRenovacao.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Renovação ativos no plano!"
				);
				return false;
			}
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("dado-duplicado-table")
			);
			return false;
		}
		return true;
	}
}
