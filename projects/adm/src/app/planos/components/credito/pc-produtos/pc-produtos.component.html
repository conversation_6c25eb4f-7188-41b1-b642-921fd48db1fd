<pacto-cat-card-plain>
	<pacto-cat-table-editable
		#tableProdutos
		(confirm)="confirm($event)"
		(delete)="delete($event)"
		(isEditingOrAddingItem)="isEditinOrAdding.emit($event)"
		[isEditable]="true"
		[showAddRow]="true"
		[table]="table"></pacto-cat-table-editable>
</pacto-cat-card-plain>

<ng-template #columnProduto>
	<span i18n="@@adm:table-column-nome">Nome</span>
</ng-template>

<ng-template #columnValorProduto>
	<span i18n="@@adm:table-column-valor-produto">Valor dentro do plano</span>
</ng-template>

<ng-template #columnObrigatorio>
	<span i18n="@@adm:table-column-obrigatorio">Obrigatório</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@adm:label-sim" xingling="label-sim">Sim</span>
	<span i18n="@@adm:label-nao" xingling="label-nao">Não</span>
	<span i18n="@@adm:label-ativo" xingling="label-ativo">Ativo</span>
	<span i18n="@@adm:label-inativo" xingling="label-inativo">Inativo</span>
	<span i18n="@@adm:dado-duplicado-table" xingling="dado-duplicado-table">
		Já existe um dado na tabela com estes valores.
	</span>
	<span i18n="@@adm:error-table-data-empty" xingling="error-table-data-empty">
		Preencha todos os dados da tabela
	</span>
	<span i18n="@@plano:produto-duplicado" xingling="produto-duplicado">
		Produto já inserido
	</span>
	<span
		i18n="@@plano:produto-matricula-ativo-plano"
		xingling="produto-matricula-ativo-plano">
		Ao menos um produto do tipo MATRÍCULA deve estar ativo no plano!
	</span>
	<span
		i18n="@@plano:produto-rematricula-ativo-plano"
		xingling="produto-rematricula-ativo-plano">
		Ao menos um produto do tipo REMATRÍCULA deve estar ativo no plano!
	</span>
	<span
		i18n="@@plano:produto-renovacao-ativo-plano"
		xingling="produto-renovacao-ativo-plano">
		Ao menos um produto do tipo RENOVAÇÃO deve estar ativo no plano!
	</span>
	<span
		i18n="@@plano:somente-um-produto-adesao-ativo"
		xingling="somente-um-produto-adesao-ativo">
		Não pode conter mais de um produto do tipo adesão ativo no plano!
	</span>
	<span
		i18n="@@plano:nenhum-produto-adesao-ativo"
		xingling="nenhum-produto-adesao-ativo">
		Deve conter ao menos um produto do tipo adesão ativo no plano!
	</span>
	<span
		i18n="@@plano:somente-um-produto-anuidade-ativo"
		xingling="somente-um-produto-anuidade-ativo">
		Não pode conter mais de um produto do tipo anuidade ativo no plano!
	</span>
	<span
		i18n="@@plano:nenhum-produto-anuidade-ativo"
		xingling="nenhum-produto-anuidade-ativo">
		Deve conter ao menos um produto do tipo anuidade ativo no plano!
	</span>
</pacto-traducoes-xingling>

<ng-template #columnSituacaoPlano>
	<div class="situacao">
		<span i18n="@@adm:plano-situacao-plano">Situação no plano</span>
		<i
			class="pct pct-help-circle cor-azulim-pri"
			i18n-title="@@adm:plano-situacao-plano-title"
			title="Ao inativar esta configuração, o produto não será apresentado ao realizar uma negociação."></i>
	</div>
</ng-template>
