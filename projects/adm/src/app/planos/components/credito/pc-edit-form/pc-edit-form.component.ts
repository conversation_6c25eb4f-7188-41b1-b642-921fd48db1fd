import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { Router } from "@angular/router";
import {
	PlanoApiCondicaoPagamentoService,
	PlanoApiProdutoService,
} from "plano-api";
import { DecimalPipe } from "@angular/common";
import { SnotifyService } from "ng-snotify";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PcDadosBasicosComponent } from "../pc-dados-basicos/pc-dados-basicos.component";
import { FormGroup } from "@angular/forms";
import { PerfilAcessoRecurso } from "sdk";
import { TraducoesXinglingComponent } from "ui-kit";
import { PlanoConfiguracaoSistema } from "../../../services/plano-configuracao-sistema.model";

@Component({
	selector: "adm-pc-edit-form",
	templateUrl: "./pc-edit-form.component.html",
	styleUrls: ["./pc-edit-form.component.scss"],
})
export class PcEditFormComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("dadosBasicosComponent", { static: false })
	dadosBasicosComponent: PcDadosBasicosComponent;
	@Input() plano: Plano;
	formDadosBasicos: FormGroup;
	duracoes: Array<any> = new Array<any>();
	duracoesCondicaoOrdenada: Array<any> = new Array<any>();
	condicoesPagamento: Array<any> = new Array<any>();
	isSaving = false;
	recurso: PerfilAcessoRecurso;
	configuracaoSistema: PlanoConfiguracaoSistema;
	nomePlano = "";
	constructor(
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		public planoStateService: PlanoStateService,
		private planoService: PlanoService,
		private router: Router,
		private cd: ChangeDetectorRef,
		private statePlano: PlanoStateService,
		private produtoService: PlanoApiProdutoService,
		private decimalPipe: DecimalPipe,
		private notificationService: SnotifyService,
		private planoCommonsService: PlanoCommonsService
	) {
		this.recurso = planoCommonsService.recurso;
	}

	ngOnInit() {
		this.initConfiguracaoSistema();
		this.plano = this.planoStateService.updatePlanoObj();
		this.plano = this.planoCommonsService.populateModalidadeAux(this.plano);
		this.sortDuracoes();
		this.listCondicoesPagamento();
	}

	initConfiguracaoSistema() {
		this.planoCommonsService
			.initConfiguracaoSistema()
			.subscribe((data: PlanoConfiguracaoSistema) => {
				this.configuracaoSistema = data;
				this.cd.detectChanges();
			});
	}

	listCondicoesPagamento() {
		this.condicaoPagamentoService
			.findByLessThenNrParcelas()
			.subscribe((response) => {
				this.condicoesPagamento = response.content;
			});
	}

	save() {
		if (!this.isSaving) {
			const planoDTO = this.planoStateService.plano;
			planoDTO.vendaCreditoTreino = true;
			planoDTO.duracoes.forEach((dur) => {
				if (dur.condicoesPagamento) {
					dur.condicoesPagamento.forEach((condPagDur) => {
						if (condPagDur.tipoValor) {
							condPagDur.tipoValor = condPagDur.tipoValor.codigo;
						}
						if (condPagDur.tipoOperacao) {
							condPagDur.tipoOperacao = condPagDur.tipoOperacao.codigo;
						}
					});
				}
			});
			this.planoCommonsService.updateCondicoesPagamento(planoDTO);
			this.planoCommonsService.convertPlano(planoDTO);
			if (!planoDTO.bolsa && planoDTO.modalidades) {
				let modalidadeValorZerado = false;
				for (const d of planoDTO.modalidades) {
					if (d.modalidade.valorMensal > 0) {
						modalidadeValorZerado = true;
						break;
					}
				}
				if (!modalidadeValorZerado) {
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-hint"),
						{
							timeout: 10000,
						}
					);
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-error"),
						{
							timeout: 10000,
						}
					);
					return;
				}
			}
			this.isSaving = true;
			this.planoService.save(planoDTO).subscribe(
				(response) => {
					if ((planoDTO && planoDTO.codigo && planoDTO.codigo > 0) && (this.configuracaoSistema && this.configuracaoSistema.permitirreplicarplanoredeempresa)) {
						this.planoService.replicarAutomaticoTodosReplicados(planoDTO.codigo).subscribe((ret) => {
						});
					}
					this.isSaving = false;
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					sessionStorage.removeItem("plano");
					this.router.navigate(["adm", "planos"]);
				},
				(error) => {
					this.isSaving = false;
					this.cd.detectChanges();
					this.notificationService.error(error.error.meta.messageValue);
				}
			);
		}
	}

	goBack() {
		sessionStorage.removeItem("plano");
		this.router.navigate(["adm", "planos"]);
	}

	sortDuracoes() {
		this.plano.duracoes = this.plano.duracoes.sort((a: any, b: any) => {
			if (+a.numeroMeses < +b.numeroMeses) {
				return -1;
			} else if (+a.numeroMeses > +b.numeroMeses) {
				return 1;
			}
			return 0;
		});
	}

	updateCondicaoPagamento(update) {
		if (update) {
			this.plano = this.planoStateService.updatePlanoObj();
			this.sortDuracoes();
			this.plano.duracoes.forEach((dur) => {
				if (!dur.condicoesPagamento) {
					dur.condicoesPagamento = new Array<any>();
				}
				this.condicoesPagamento.forEach((condicaoPagamento) => {
					const exists = dur.condicoesPagamento.find(
						(condPagDur) =>
							condPagDur.condicaoPagamento.codigo === condicaoPagamento.codigo
					);
					if (
						!exists &&
						condicaoPagamento.nrParcelas <= dur.nrMaximoParcelasCondPagamento
					) {
						dur.condicoesPagamento.push({
							condicaoPagamento,
						});
					}
				});
				dur.condicoesPagamento = dur.condicoesPagamento.sort((a, b) => {
					if (a.condicaoPagamento.nrParcelas < b.condicaoPagamento.nrParcelas) {
						return -1;
					} else if (
						a.condicaoPagamento.nrParcelas > b.condicaoPagamento.nrParcelas
					) {
						return 1;
					}
					return 0;
				});
			});
			this.planoStateService.updateState(this.plano);
		}
	}
}
