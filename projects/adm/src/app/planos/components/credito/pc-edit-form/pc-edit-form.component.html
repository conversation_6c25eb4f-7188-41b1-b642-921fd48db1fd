<adm-layout
	(goBack)="goBack()"
	[showConfirmDialog]="true"
	i18n-modulo="@@plano:modulo"
	i18n-pageTitle="@@plano:edit-page-title"
	modulo="Administrativo"
	pageTitle="Editar Plano: {{ nomePlano | captalize }}">
	<pacto-cat-tabs-transparent
		(action)="save()"
		[actionLabel]="'Salvar'"
		[showAction]="!isSaving && recurso.editar">
		<ng-template
			i18n-label="@@plano:edit-label-dados-basicos"
			label="Dados Básicos"
			pactoTabTransparent>
			<adm-pc-dados-basicos
				#dadosBasicosComponent
				(nome)="nomePlano = $event"></adm-pc-dados-basicos>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-modalidades"
			label="Modalidades"
			pactoTabTransparent>
			<adm-pc-modalidades></adm-pc-modalidades>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-durval"
			label="Duração e valores"
			pactoTabTransparent>
			<adm-pc-duracao-valor
				(updateCondicoesPagmento)="
					updateCondicaoPagamento($event)
				"></adm-pc-duracao-valor>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-horarios"
			label="Horários"
			pactoTabTransparent>
			<adm-pc-table-horario></adm-pc-table-horario>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-prodserv"
			label="Produtos e serviços"
			pactoTabTransparent>
			<adm-pc-produtos></adm-pc-produtos>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-dados-contratuais"
			label="Dados Contratuais"
			pactoTabTransparent>
			<adm-pc-dados-contratual></adm-pc-dados-contratual>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-condpag"
			label="Condição de Pagamento"
			pactoTabTransparent>
			<ng-container *ngIf="plano.duracoes && plano.duracoes.length > 0">
				<adm-pc-condicao-pagamento
					*ngFor="let duracao of plano.duracoes"
					[duracao]="duracao"></adm-pc-condicao-pagamento>
			</ng-container>
			<ng-container *ngIf="!plano.duracoes || plano.duracoes?.length === 0">
				<pacto-cat-card-plain>
					<div class="table-wrapper pacto-shadow">
						<span i18n="@@plano:condpag-hint-durval">
							Para criar uma condição de pagamento é necessário acessar o passo
							"Duração e Valores" e adicionar ao menos uma duração.
						</span>
					</div>
				</pacto-cat-card-plain>
			</ng-container>
		</ng-template>
		<ng-template
			*ngIf="configuracaoSistema?.permitirreplicarplanoredeempresa"
			i18n-label="@@plano:edit-label-empresas"
			label="Replicar empresa"
			pactoTabTransparent>
			<adm-replicar-plano [plano]="plano"></adm-replicar-plano>
		</ng-template>
	</pacto-cat-tabs-transparent>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:saved-success" xingling="saved-success">
		Plano salvo com sucesso.
	</span>
	<span i18n="@@plano:modalidade-zerada-hint" xingling="modalidade-zerada-hint">
		Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais
		uma modalidade junto que tenha valor, ou então coloque valor no cadastro
		dela.
	</span>
	<span
		i18n="@@plano:modalidade-zerada-error"
		xingling="modalidade-zerada-error">
		Você adicionou modalidade com valor zerado na aba "modalidades". Como este
		plano não foi marcado como "PLANO BOLSA", esta operação não é permitida.
	</span>
</pacto-traducoes-xingling>
