import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { FormControl, FormGroup } from "@angular/forms";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pc-modalidades",
	templateUrl: "./pc-modalidades.component.html",
	styleUrls: ["./pc-modalidades.component.scss"],
})
export class PcModalidadesComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnModalidade", { static: true })
	columnModalidade: TemplateRef<any>;
	@ViewChild("columnVezesSemana", { static: true })
	columnVezesSemana: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;

	@Output() isEditinOrAdding: EventEmitter<any> = new EventEmitter<any>();
	@ViewChild("tableModalidadeComponent", { static: false })
	tableModalidadeComponent: CatTableEditableComponent;
	@Input() formDadosBasicos: FormGroup;
	tableModalidades: PactoDataGridConfig;
	plano: Plano;
	modalidades: Array<any> = new Array<any>();

	constructor(
		private planoStateService: PlanoStateService,
		private admRestService: AdmRestService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.plano.modalidadesAux) {
			this.modalidades = this.plano.modalidadesAux;
		} else {
			this.planoCommonsService.populateModalidadeAux(this.plano);
			this.planoStateService.updateState(this.plano);
			this.modalidades = this.plano.modalidadesAux;
		}
		this.initTableModalidades();
		if (this.formDadosBasicos) {
			this.formDadosBasicos
				.get("creditoSessao")
				.valueChanges.subscribe((value) => {
					this.initTableModalidades();
				});
		}
	}

	confirm(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (
			this.modalidades &&
			event &&
			event.row &&
			event.row.codigo !== "" &&
			event.row.codigo !== null &&
			event.row.codigo !== undefined
		) {
			this.modalidades.forEach((m) => {
				m.edit = false;
			});
		}
		this.plano.modalidadesAux = this.modalidades;
		this.planoStateService.updateState(this.plano);
	}

	delete(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.modalidades) {
			let index;
			if (event.row.codigo) {
				const finded = this.modalidades.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (finded && index !== undefined) {
					this.modalidades.splice(index, 1);
				}
			} else {
				this.modalidades.splice(event.index, 1);
			}
			this.plano.modalidadesAux = this.modalidades;
		}
		this.planoStateService.updateState(this.plano);
	}

	initTableModalidades() {
		this.tableModalidades = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.modalidades,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				modalidade: new FormControl(),
				vezesSemana: new FormControl(""),
				valor: new FormControl(""),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmModalidades(row, form, data, rowIndex),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "modalidade",
					titulo: this.columnModalidade,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: true,
					showAddSelectBtn: false,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "nome",
					labelSelectKey: "nome",
					width: "193px",
					endpointUrl:
						this.admRestService.buildFullUrlPlano("modalidades") +
						"/only-cod-name",
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					addEmptyOption: true,
				},
				{
					nome: "vezesSemana",
					titulo: this.columnVezesSemana,
					visible: this.formDadosBasicos
						? this.formDadosBasicos.get("creditoSessao").value
						: this.plano.creditoSessao,
					ordenavel: false,
					editable: true,
					valueTransform: (v) => (v ? v + "x" : ""),
					inputType: "number",
					showAddSelectBtn: false,
					showSelectFilter: false,
					width: "200px",
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					visible: this.formDadosBasicos
						? this.formDadosBasicos.get("creditoSessao").value
						: this.plano.creditoSessao,
					valueTransform: (v) => {
						if (v !== null && v !== undefined) {
							return this.decimalPipe.transform(
								parseFloat(v.toString().replace(",", ".")),
								"1.2-2"
							);
						}
					},
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimal: true,
					errorMessage: this.traducao.getLabel("error-valor-obrigatorio"),
				},
			],
		});
	}

	updateTable() {
		this.tableModalidades.dataAdapterFn = (serverData) => {
			return {
				content: this.modalidades,
			};
		};
	}

	beforeConfirmModalidades(row, form, data, rowIndex): boolean {
		const keysAvoidCompare = [];
		const creditoSessao = this.formDadosBasicos
			? this.formDadosBasicos.get("creditoSessao").value
			: this.plano.creditoSessao;
		if (!creditoSessao) {
			keysAvoidCompare.push("vezesSemana");
			keysAvoidCompare.push("valor");
		}
		let exists = false;
		if (!creditoSessao) {
			exists = data.find(
				(d, i) =>
					d.modalidade.codigo === row.modalidade.codigo && rowIndex !== i
			);
		} else {
			exists = this.planoCommonsService.verifyIfNewExists(
				row,
				data,
				rowIndex,
				keysAvoidCompare
			);
		}
		if (!exists) {
			if (creditoSessao) {
				const modalidade = form.get("modalidade").value;
				const vezesSemana = form.get("vezesSemana").value;
				const valor = form.get("valor").value;

				if (
					!modalidade ||
					modalidade === "" ||
					modalidade.nome === "-" ||
					vezesSemana === undefined ||
					vezesSemana === null ||
					vezesSemana === "" ||
					vezesSemana === 0 ||
					valor === ""
				) {
					this.notificationService.error(
						this.traducao.getLabel("error-preencha-todos-campos-tabela")
					);
					return false;
				}
			} else {
				const modalidade = form.get("modalidade").value;
				if (!modalidade || modalidade === "" || modalidade.nome === "-") {
					this.notificationService.error(
						this.traducao.getLabel("error-modalidade-vazia")
					);
					return false;
				}
			}
		} else {
			this.notificationService.error(
				this.traducao.getLabel("dado-duplicado-table")
			);
			return false;
		}
		return true;
	}
}
