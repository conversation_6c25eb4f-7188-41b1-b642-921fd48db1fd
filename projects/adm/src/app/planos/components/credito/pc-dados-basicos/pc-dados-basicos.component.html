<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-input
					[control]="form.get('descricao')"
					errorMsg="Forneça um nome para o plano."
					i18n-errorMsg="@@plano:error-msg-descricao"
					i18n-label="@@plano:label-descricao"
					id="input-plano-nome"
					label="Nome"
					maxlength="200"></pacto-cat-form-input>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select
					[control]="form.get('tipoPlano')"
					[items]="tiposPlano"
					disabled="true"
					errorMsg="Tipo de plano"
					i18n-errorMsg="@@plano:error-msg-tipo-plano"
					i18n-label="@@plano:label-tipo-plano"
					id="select-tipo-plano"
					label="Tipo de plano"></pacto-cat-form-select>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[control]="form.get('produtoContrato')"
					[options]="produtosContrato"
					[paramBuilder]="produtoelectBuilder"
					errorMsg="Selecione um produto"
					i18n-errorMsg="@@plano:error-msg-produto-contrato"
					i18n-label="@@plano:label-produto-contrato"
					id="select-produto-contrato-plano"
					idKey="codigo"
					label="Produto padrão gerar parcelas contrato"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('vigenciaDe')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@plano:error-msg-vigenciade"
					i18n-label="@@plano:label-vigenciade"
					id="datepicker-vigenciade-plano"
					label="Plano ativo a partir de"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('ingressoAte')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@plano:error-msg-ingressoate"
					i18n-label="@@plano:label-ingressoate"
					id="datepicker-ingresso-plano"
					label="Permite matrículas/rematrículas até"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('vigenciaAte')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@plano:error-msg-vigenciaate"
					i18n-label="@@plano:label-vigenciaate"
					id="datepicker-vigenciaate-plano"
					label="Permite renovação até"></pacto-cat-form-datepicker>
			</div>
		</div>
		<div class="row">
			<div class="col-md-5">
				<pacto-cat-button
					(click)="configuracoesPlano()"
					[label]="traducoes.getLabel('advanced-config')"
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					id="btn-advanced-config-plano"></pacto-cat-button>
			</div>

			<div class="col-md-5">
				<div *ngIf="!form.get('creditoTreinoNaoCumulativo').value">
					<pacto-cat-checkbox
						[control]="form.get('creditoSessao')"
						i18n-label="@@plano:label-creditoSessao"
						id="check-credito-sessao-plano"
						label="Venda crédito por sessão"></pacto-cat-checkbox>
				</div>

				<div
					*ngIf="!form.get('creditoSessao').value"
					class="d-flex align-items-center gap-1">
					<pacto-cat-checkbox
						[control]="form.get('creditoTreinoNaoCumulativo')"
						id="check-credito-treino-nao-cumulativo-plano"
						i18n-label="@@plano:creditoTreinoNaoCumulativo"
						label="Crédito Mensal (não cumulativo)"></pacto-cat-checkbox>

					<label for="check-credito-treino-nao-cumulativo-plano" class="mb-0">
						<i
							class="pct pct-help-circle cor-azulim-pri ms-1"
							i18n-title="@@plano:creditoTreinoNaoCumulativoHintTitle"
							title="Ao marcar essa configuração será acrescentado na aba duração e valores o campo 'Quantidade de crédito Mensal' e fará com que o plano receba créditos mensalmente."></i>
					</label>
				</div>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:tipo-normal" xingling="PN">Plano Normal</span>
	<span i18n="@@plano:tipo-recorrencia" xingling="PR">Plano Recorrência</span>
	<span i18n="@@plano:tipo-credito" xingling="PC">Plano Crédito</span>
	<span i18n="@@plano:tipo-personal" xingling="PP">Plano Personal</span>

	<span i18n="@@adm:advanced-config" xingling="advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
