import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Plano, TipoPlano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoApiProdutoService } from "plano-api";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PcConfigDadosBasicosComponent } from "../pc-config-dados-basicos/pc-config-dados-basicos.component";

@Component({
	selector: "adm-pc-dados-basicos",
	templateUrl: "./pc-dados-basicos.component.html",
	styleUrls: ["./pc-dados-basicos.component.scss"],
})
export class PcDadosBasicosComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	form: FormGroup;
	plano: Plano;
	tiposPlano: any[];
	produtosContrato: Array<any> = new Array<any>();
	produtoelectBuilder: SelectFilterParamBuilder;
	@Output() nome: EventEmitter<string> = new EventEmitter();

	constructor(
		private planoStateService: PlanoStateService,
		private dialogService: DialogService,
		public produtoService: PlanoApiProdutoService,
		private planoCommonsService: PlanoCommonsService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.form = this.planoCommonsService.formDadosBasicos(
			TipoPlano.PLANO_CREDITO
		);
		this.setTiposPlano();
		this.produtoelectBuilder = this.planoCommonsService.produtoSelectBuilder;
		this.planoCommonsService.updateFormDadosBasicos(this.form, this.plano);
		this.form.valueChanges.subscribe((value) => {
			if (value.creditoSessao) {
				value.creditoTreinoNaoCumulativo = false;
			}
			if (value.creditoTreinoNaoCumulativo) {
				value.creditoSessao = false;
			}
			this.planoCommonsService.updatePlanoDadosBasicos(value, this.plano);
		});
		this.loadProdutosContrato();
		this.nome.emit(this.form.controls["descricao"].value);
		this.form.controls["descricao"].valueChanges.subscribe((v) => {
			this.nome.emit(v);
		});
		this.form.updateValueAndValidity();
	}

	async setTiposPlano() {
		this.tiposPlano = await this.planoCommonsService.tiposPlano(this.traducoes);
		this.cd.detectChanges();
	}

	loadProdutosContrato() {
		this.produtoService.getProdutosTipoPlano().subscribe((response: any) => {
			this.produtosContrato = response.content;
			if (
				(this.planoStateService.plano &&
					this.planoStateService.plano.codigo === undefined) ||
				this.planoStateService.plano.codigo === null
			) {
				this.form
					.get("produtoContrato")
					.setValue(
						this.produtosContrato.find((prod) => prod.tipoProduto === "PM")
					);
			}
		});
	}

	configuracoesPlano() {
		const dialogRef = this.dialogService.open(
			this.traducoes.getLabel("advanced-config"),
			PcConfigDadosBasicosComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoStateService.plano;
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {})
				.catch((error) => {
					console.log(error);
				});
		}
	}
}
