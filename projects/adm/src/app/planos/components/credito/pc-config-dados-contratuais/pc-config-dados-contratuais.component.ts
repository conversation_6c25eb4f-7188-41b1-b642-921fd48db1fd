import {
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Plano } from "../../../plano.model";
import { FormControl, FormGroup } from "@angular/forms";
import {
	BUTTON_TYPE,
	PactoDataGridConfig,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoApiProdutoService } from "plano-api";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pc-config-dados-contratuais",
	templateUrl: "./pc-config-dados-contratuais.component.html",
	styleUrls: ["./pc-config-dados-contratuais.component.scss"],
})
export class PcConfigDadosContratuaisComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnNumeroMeses", { static: true })
	columnNumeroMeses: TemplateRef<any>;
	@ViewChild("columnCarencia", { static: true })
	columnCarencia: TemplateRef<any>;

	@Input() plano: Plano;
	form: FormGroup;
	buttonType = BUTTON_TYPE;
	frequencias: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	descontoSelectBuilder: SelectFilterParamBuilder;
	listaParcelas: Array<any> = new Array<any>();
	days: Array<{ label: string; id: number }> = new Array<{
		label: string;
		id: number;
	}>();
	meses: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	tablePlanoRecorrenciaParcelas: PactoDataGridConfig;
	parcelas: Array<any> = new Array<any>();
	parcelasMatriculas: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	tableFerias: PactoDataGridConfig;

	constructor(
		public dialog: NgbActiveModal,
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		public admRestService: AdmRestService,
		public produtoService: PlanoApiProdutoService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		if (!this.plano.planoRecorrencia) {
			this.plano.planoRecorrencia = {};
		}
		if (this.plano.planoRecorrencia.parcelas) {
			this.parcelas = this.plano.planoRecorrencia.parcelas;
		}
		this.form = this.planoCommonsService.formConfigDadosContratuais(
			this.plano.tipoPlano
		);
		this.initDays();
		this.initListaParcelas();
		this.initMeses();
		this.populateParcelasMatriculas();
		this.initDiasProrata();
		this.initTableFerias();
		this.planoCommonsService.updateformConfigDadosContratuais(
			this.form,
			this.plano
		);
		this.planoStateService.updateState(this.plano);
	}

	confirm() {
		this.plano.planoRecorrencia.parcelas = this.parcelas;
		this.plano.planoRecorrencia.parcelas.forEach((parcela) => {
			parcela.numero = parcela.numero.value;
		});
		this.planoStateService.plano = this.plano;
	}

	initTableFerias() {
		this.tableFerias = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.plano.duracoes,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				carencia: new FormControl(),
			}),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "100px",
				},
				{
					nome: "numeroMeses",
					titulo: this.columnNumeroMeses,
					visible: true,
					ordenavel: false,
					valueTransform: (v) => v + "m",
					width: "100px",
				},
				{
					nome: "carencia",
					titulo: this.columnCarencia,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					width: "100px",
				},
			],
		});
	}

	confirmFerias(event) {}

	initDays() {
		for (let i = 1; i <= 31; i++) {
			this.days.push({
				label: i.toString(),
				id: i,
			});
		}
	}

	initListaParcelas() {
		for (let i = 1; i <= 12; i++) {
			this.listaParcelas.push({
				label: `${i}x`,
				value: i,
			});
		}
	}

	initMeses() {
		this.meses.push(
			{
				label: this.traducao.getLabel("mes-janeiro"),
				value: 1,
			},
			{
				label: this.traducao.getLabel("mes-fevereiro"),
				value: 2,
			},
			{
				label: this.traducao.getLabel("mes-marco"),
				value: 3,
			},
			{
				label: this.traducao.getLabel("mes-abril"),
				value: 4,
			},
			{
				label: this.traducao.getLabel("mes-maio"),
				value: 5,
			},
			{
				label: this.traducao.getLabel("mes-junho"),
				value: 6,
			},
			{
				label: this.traducao.getLabel("mes-julho"),
				value: 7,
			},
			{
				label: this.traducao.getLabel("mes-agosto"),
				value: 8,
			},
			{
				label: this.traducao.getLabel("mes-setembro"),
				value: 9,
			},
			{
				label: this.traducao.getLabel("mes-outubro"),
				value: 10,
			},
			{
				label: this.traducao.getLabel("mes-novembro"),
				value: 11,
			},
			{
				label: this.traducao.getLabel("mes-dezembro"),
				value: 12,
			}
		);
	}

	populateParcelasMatriculas() {
		for (let i = 1; i <= this.plano.planoRecorrencia.duracaoPlano; i++) {
			if (this.parcelas && this.parcelas.length > 0) {
				const parcela = this.parcelas.find((v) => v === i);
				if (!parcela) {
					const parcelaMat = this.parcelasMatriculas.find((v) => v.value === i);
					if (!parcelaMat) {
						this.parcelasMatriculas.push({
							label: `${this.traducao.getLabel("label-parcela")} ${i}`,
							value: i,
						});
					}
				}
			} else {
				const parcela = this.parcelasMatriculas.find((v) => v.value === i);
				if (!parcela) {
					this.parcelasMatriculas.push({
						label: `${this.traducao.getLabel("label-parcela")} ${i}`,
						value: i,
					});
				}
			}
		}
	}

	initDiasProrata() {
		if (!this.plano.diasVencimentoProrata) {
			this.plano.diasVencimentoProrata = "";
			this.days = this.days.sort((a, b) => {
				if (a.id < b.id) {
					return -1;
				} else if (a.id > b.id) {
					return 1;
				}
				return 0;
			});
			this.days.forEach((day, index) => {
				if (index === this.days.length - 1) {
					this.plano.diasVencimentoProrata += `${day.id}`;
				} else {
					this.plano.diasVencimentoProrata += `${day.id},`;
				}
			});
		}
	}

	salvar() {
		let diasVencimentoProrata: Array<{ label: string; id: number }> =
			this.form.get("diasVencimentoProrata").value;
		let diasVencimentoProrataStr = "";
		diasVencimentoProrata = diasVencimentoProrata.sort((a, b) => {
			if (+a.id < +b.id) {
				return -1;
			} else if (+a.id > +b.id) {
				return 1;
			}
			return 0;
		});
		diasVencimentoProrata.forEach((dvp, index) => {
			diasVencimentoProrataStr += dvp.id;
			if (index + 1 < diasVencimentoProrata.length) {
				diasVencimentoProrataStr += ",";
			}
		});
		this.plano.diasVencimentoProrata = diasVencimentoProrataStr;
		this.plano.descontoAntecipado = this.form.get("descontoAntecipado").value;
		this.plano.prorataObrigatorio = this.form.get("prorataObrigatorio").value;
		this.plano.obrigatorioInformarCartaoCreditoVenda = this.form.get(
			"obrigatorioInformarCartaoCreditoVenda"
		).value;
		this.plano.planoRecorrencia.renovavelAutomaticamente = this.form.get(
			"renovavelAutomaticamente"
		).value;
		this.plano.renovavelAutomaticamente = this.form.get(
			"renovavelAutomaticamente"
		).value;
		this.plano.renovarAutomaticamenteComDesconto = this.form.get(
			"renovarAutomaticamenteComDesconto"
		).value;
		this.plano.renovarProdutoObrigatorio = this.form.get(
			"renovarProdutoObrigatorio"
		).value;
		this.plano.renovarAutomaticamenteUtilizandoValorBaseContrato =
			this.form.get("renovarAutomaticamenteUtilizandoValorBaseContrato").value;
		this.plano.naoRenovarContratoParcelaVencidaAberto = this.form.get(
			"naoRenovarContratoParcelaVencidaAberto"
		).value;
		this.plano.parcelamentoOperadora = this.form.get(
			"parcelamentoOperadora"
		).value;
		this.plano.parcelamentoOperadoraDuracao = this.form.get(
			"parcelamentoOperadoraDuracao"
		).value;
		this.plano.maximoVezesParcelar = this.form.get("maximoVezesParcelar").value;
		this.plano.cobrarAdesaoSeparada = this.form.get(
			"cobrarAdesaoSeparada"
		).value;
		this.plano.nrVezesParcelarAdesao = this.form.get(
			"nrVezesParcelarAdesao"
		).value;
		this.plano.cobrarProdutoSeparado = this.form.get(
			"cobrarProdutoSeparado"
		).value;
		this.plano.nrVezesParcelarProduto = this.form.get(
			"nrVezesParcelarProduto"
		).value;
		this.plano.planoRecorrencia.parcelas =
			this.planoStateService.plano.planoRecorrencia.parcelas;
		this.plano.aceitaDescontoExtra = this.form.get("aceitaDescontoExtra").value;
		this.planoStateService.updateState(this.plano);
		this.dialog.close();
	}
}
