import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { Plano } from "../../../plano.model";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	BUTTON_TYPE,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "../../../../adm-rest.service";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-pc-config-dados-basicos",
	templateUrl: "./pc-config-dados-basicos.component.html",
	styleUrls: ["./pc-config-dados-basicos.component.scss"],
})
export class PcConfigDadosBasicosComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() plano: Plano;
	form: FormGroup;
	buttonType = BUTTON_TYPE;
	frequencias: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	private inicioMinimoContratoValid = true;
	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "VO",
	};
	modeloContratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};
	modules = {};
	fontSizeArr = ["8px", "9px", "10px", "11px", "12px", "14px", "16px"];
	Size = Quill.import("attributors/style/size");

	constructor(
		public admRestService: AdmRestService,
		public dialog: NgbActiveModal,
		public planoStateService: PlanoStateService,
		private notificationService: SnotifyService,
		public planoCommonsService: PlanoCommonsService,
		private cd: ChangeDetectorRef
	) {
		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ align: [] }],
					["clean"],
				],
			},
		};
	}

	ngOnInit() {
		this.Size.whitelist = this.fontSizeArr;
		Quill.register(this.Size, true);

		const Block = Quill.import("blots/block");
		Block.tagName = "DIV";
		Quill.register(Block, true);

		this.plano = this.planoStateService.updatePlanoObj();
		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ align: [] }],
					["clean"],
				],
			},
		};
		this.plano = this.planoStateService.updatePlanoObj();
		this.form = this.planoCommonsService.formConfigBasicos(
			this.plano.tipoPlano
		);
		this.setFrequencias();
		this.planoCommonsService.updateformConfigBasicos(this.form, this.plano);
		if (this.plano.site) {
			this.form.get("termoAceite").setValidators(Validators.required);
			this.form.get("termoAceite").updateValueAndValidity();
		} else {
			this.form.get("termoAceite").clearValidators();
			this.form.get("termoAceite").updateValueAndValidity();
			this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
			this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
		}
		if (!this.plano.totem) {
			this.form.get("permitirVendaPlanoTotemNoBalcao").setValue(false);
			this.form.get("renovarComDescontoTotem").setValue(false);
		}
		this.form.get("site").valueChanges.subscribe((value) => {
			if (value) {
				this.form.get("termoAceite").setValidators(Validators.required);
				this.form.get("termoAceite").updateValueAndValidity();
			} else {
				this.form.get("termoAceite").clearValidators();
				this.form.get("termoAceite").updateValueAndValidity();
				this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
				this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
			}
		});

		this.form.get("totem").valueChanges.subscribe((value) => {
			if (!value) {
				this.form.get("permitirVendaPlanoTotemNoBalcao").setValue(false);
				this.form.get("renovarComDescontoTotem").setValue(false);
			}
			this.plano.totem = value;
		});

		if (!this.plano.permitirTransferenciaDeCredito) {
			this.form.get("quantidadeATransferirPermitidaPorAluno").setValue(0);
		}

		this.form
			.get("permitirTransferenciaDeCredito")
			.valueChanges.subscribe((value) => {
				if (!value) {
					this.form.get("quantidadeATransferirPermitidaPorAluno").setValue(0);
				}
				this.plano.totem = value;
			});

		this.form.get("site").valueChanges.subscribe((value) => {
			if (value) {
				this.form.get("apresentarVendaRapida").setValue(false);
			}
			this.plano.site = value;
		});

		this.form
			.get("permitirVendaPlanoSiteNoBalcao")
			.valueChanges.subscribe((value) => {
				if (value) {
					this.form.get("apresentarVendaRapida").setValue(false);
				}
				this.plano.permitirVendaPlanoSiteNoBalcao = value;
			});

		this.form
			.get("permitirCompartilharPLanoNoSite")
			.valueChanges.subscribe((value) => {
				this.plano.permitirCompartilharPLanoNoSite = value;
			});

		this.form.get("inicioMinimoContrato").valueChanges.subscribe((value) => {
			const now = new Date();
			now.setHours(0, 0, 0, 0);
			if (value !== "" && value < now) {
				this.form.get("inicioMinimoContrato").setErrors({ incorrect: true });
			}
			this.inicioMinimoContratoValid = this.form.get(
				"inicioMinimoContrato"
			).valid;
		});
		this.form
			.get("bloquearRecompra")
			.setValue(this.plano.bloquearRecompra || false);
		this.form
			.get("contratosEncerramDia")
			.setValue(this.plano.contratosEncerramDia);
	}

	async setFrequencias() {
		this.frequencias = await this.planoCommonsService.initFrequencias(
			this.traducao
		);
		this.cd.detectChanges();
	}

	salvar() {
		if (this.inicioMinimoContratoValid) {
			Object.keys(this.form.controls).forEach((controlKey) => {
				this.plano[controlKey] = this.form.get(controlKey).value;
			});
			this.planoStateService.updateState(this.plano);
			this.dialog.close();
		} else {
			this.notificationService.error(
				this.traducao.getLabel("error-data-retroativa-obrigatorio"),
				this.traducao.getLabel("error-location-vendas-online")
			);
		}
	}

	dateFilter(date: Date): boolean {
		const actualDate = new Date();
		actualDate.setHours(0, 0, 0, 0);
		return date.getTime() >= actualDate.getTime();
	}
}
