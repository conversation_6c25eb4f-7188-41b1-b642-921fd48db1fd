<pacto-cat-tabs-vertical>
	<ng-template
		pactoTabVertical="tab-acessos-semanais"
		i18n-label="@@adm:tab-label-acesso-semanal"
		label="Acessos semanais">
		<pacto-cat-form-select
			id="select-qtd-max-frequencia"
			[control]="form.get('quantidadeMaximaFrequencia')"
			[items]="frequencias"
			idKey="value"
			i18n-label="@@adm:label-acesso-semanal"
			label="Acesso semanal"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		pactoTabVertical="tab-convites"
		i18n-label="@@adm:tab-label-convites"
		label="Convites">
		<pacto-cat-form-input-number
			id="input-convidados-mes"
			[formControl]="form.get('convidadosPorMes')"
			i18n-label="@@adm:label-convidados-por-mes"
			label="Quantidade de convite por mês"></pacto-cat-form-input-number>
	</ng-template>
	<ng-template
		pactoTabVertical="tab-vendas-online"
		i18n-label="@@adm:tab-label-vendas-online"
		label="Vendas online">
		<pacto-cat-checkbox
			id="check-site"
			[control]="form.get('site')"
			i18n-label="@@adm:label-vendas-online"
			label="Participa do vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-compartilhar-plano-site"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirCompartilharPLanoNoSite')"
			label="Permite compartilhar plano no vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			i18n-label="@@adm:label-vendas-online"
			id="check-permite-venda-plano-site-balcao"
			[control]="form.get('permitirVendaPlanoSiteNoBalcao')"
			label="Permite venda do plano no balcão"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			id="check-pacto-flow"
			[control]="form.get('apresentarPactoFlow')"
			i18n-label="@@adm:label-apresentar-no-pacto-flow"
			label="Apresentar no Pacto Flow"></pacto-cat-checkbox>
		<pacto-cat-form-datepicker
			id="datepicker-inicio-minimo-contrato"
			[control]="form.get('inicioMinimoContrato')"
			i18n-label="@@adm:label-inicio-minimo-contrato"
			label="Contratos iniciam a partir de"
			[dateFilter]="dateFilter"></pacto-cat-form-datepicker>
		<pacto-cat-form-select-filter
			id="select-termo-aceite"
			*ngIf="form.get('site').value"
			i18n-label="@@adm:label-termo-aceite"
			label="Termo de aceite"
			[control]="form.get('termoAceite')"
			i18n-errorMsg="@@adm:label-termo-aceite"
			errorMsg="Selecione um termo de aceite"
			[idKey]="'codigo'"
			[labelKey]="'descricao'"
			[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
			[addtionalFilters]="modeloContratoAddtionalFilters"
			[paramBuilder]="
				modeloContratoSelectBuilder
			"></pacto-cat-form-select-filter>
		<div class="row" *ngIf="form.get('site').value">
			<div class="col-md-12">
				<div class="input-nome">Vídeo youtube</div>
				<input
					placeholder="Informe aqui o código do vídeo no youtube Ex: 6AidSn00VGo"
					type="text"
					class="input-text"
					[formControl]="form.get('videoSiteUrl')" />
			</div>
		</div>

		<label *ngIf="form.get('site').value" class="input-nome">Observação</label>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12 div-text-area-modelo-contrato">
				<quill-editor
					[formControl]="form.get('observacaoSite')"
					[id]="'editor-texto-observacao'"
					[modules]="modules"></quill-editor>
			</div>
		</div>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-totem"
		label="Totem"
		pactoTabVertical="tab-totem">
		<pacto-cat-checkbox
			[control]="form.get('totem')"
			i18n-label="@@adm:label-totem"
			id="check-totem"
			label="Totem"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('totem').value"
			i18n-label="@@adm:label-permite-venda-plano-totem-balcao"
			id="check-permite-venda-plano-totem-balcao"
			[control]="form.get('permitirVendaPlanoTotemNoBalcao')"
			label="Permite venda do plano no balcão"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('totem').value"
			[control]="form.get('renovarComDescontoTotem')"
			i18n-label="@@adm:label-renovar-com-desconto-totem"
			id="check-renovar-com-desconto-totem"
			label="Permitir que o plano seja renovado pelo totem com desconto"></pacto-cat-checkbox>
		<pacto-cat-form-input
			*ngIf="form.get('totem').value"
			[control]="form.get('descricaoEncantamento')"
			i18n-label="@@adm:label-descricao-encantamento"
			id="input-descricao-encantamento"
			label="Descrição de encantamento"></pacto-cat-form-input>
	</ng-template>
	<ng-template
		*ngIf="!form.get('site').value"
		i18n-label="@@adm:tab-label-bolsa"
		label="Bolsa"
		pactoTabVertical="tab-bolsa">
		<pacto-cat-checkbox
			[control]="form.get('bolsa')"
			i18n-label="@@adm:label-bolsa"
			id="check-bolsa"
			label="Plano bolsa"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-comissao"
		label="Comissão"
		pactoTabVertical="tab-comissão">
		<pacto-cat-checkbox
			[control]="form.get('comissao')"
			i18n-label="@adm:label-gerar-comissao"
			id="check-comissao"
			label="Gerar comissão"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-restricao-recompra"
		label="Restrição de venda"
		pactoTabVertical="restricao-recompra">
		<pacto-cat-checkbox
			[control]="form.get('bloquearRecompra')"
			i18n-label="@@adm:label-bloquear-recompra"
			id="check-bloquear-recompra"
			label="Venda apenas para visitante"></pacto-cat-checkbox>

		<pacto-cat-form-datepicker
			*ngIf="
				plano.tipoPlano !== 'PLANO_PERSONAL' &&
				plano.tipoPlano !== 'PLANO_CREDITO' &&
				plano.tipoPlano !== 'PLANO_AVANCADO'
			"
			[control]="form.get('contratosEncerramDia')"
			i18n-label="@@adm:label--encerram-dia"
			id="datepicker-encerram-dia"
			label="Contratos encerram no dia"></pacto-cat-form-datepicker>
	</ng-template>
	<ng-template
		label="Transferência de crédito"
		pactoTabVertical="tab-permitirTransferenciaDeCredito">
		<pacto-cat-checkbox
			[control]="form.get('permitirTransferenciaDeCredito')"
			id="check-permitirTransferenciaDeCredito"
			label="Permitir Transferência de crédito"></pacto-cat-checkbox>
		<pacto-cat-form-input
			*ngIf="form.get('permitirTransferenciaDeCredito').value"
			[control]="form.get('quantidadeATransferirPermitidaPorAluno')"
			id="input-quantidade-permitidas-de-transferencias"
			label="Quantidade de créditos que poderão ser transferidos por aluno"></pacto-cat-form-input>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-observacoes"
		label="Observações"
		pactoTabVertical="obsercacoes">
		<div class="input-nome">Observação 1</div>
		<input
			[formControl]="form.get('observacao1')"
			class="input-text"
			type="text" />
		<div class="input-nome">Observação 2</div>
		<input
			[formControl]="form.get('observacao2')"
			class="input-text"
			type="text" />
	</ng-template>
</pacto-cat-tabs-vertical>

<div class="action-container btn-row-adm">
	<pacto-cat-button
		(click)="salvar()"
		[type]="buttonType.PRIMARY"
		i18n-label="@@adm:btn-salvar-config"
		id="btn-save-config"
		label="Salvar Configurações"></pacto-cat-button>
</div>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@plano:error-data-retroativa-obrigatoria"
		xingling="error-data-retroativa-obrigatoria">
		Informe uma data não retroativa!
	</span>
	<span
		i18n="@@plano:error-location-vendas-online"
		xingling="error-location-vendas-online">
		Vendas online
	</span>
	<span i18n="@@adm:por-semana" xingling="por-semana">por semana</span>
</pacto-traducoes-xingling>
