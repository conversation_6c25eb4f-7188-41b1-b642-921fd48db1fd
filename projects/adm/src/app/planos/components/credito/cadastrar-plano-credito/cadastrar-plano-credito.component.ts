import {
	AfterContentInit,
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
} from "@angular/core";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { Router } from "@angular/router";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { PactoDataGridConfig } from "ui-kit";
import { DecimalPipe } from "@angular/common";
import { PlanoApiProdutoService } from "plano-api";
import { SnotifyService } from "ng-snotify";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-cadastrar-plano-credito",
	templateUrl: "./cadastrar-plano-credito.component.html",
	styleUrls: ["./cadastrar-plano-credito.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CadastrarPlanoCreditoComponent
	implements OnInit, AfterViewInit, AfterContentInit
{
	planoDTO: Plano;
	formDadosBasicos: FormGroup;
	formDuracaoValores: FormGroup;
	tableProdutoServico: PactoDataGridConfig;
	numeroParcelasOptions: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	formDadosContratuais: FormGroup;
	disableNextModalidade = false;
	vezesSemanaOptions: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();

	constructor(
		public planoStateService: PlanoStateService,
		private planoService: PlanoService,
		private router: Router,
		private cd: ChangeDetectorRef,
		private statePlano: PlanoStateService,
		private produtoService: PlanoApiProdutoService,
		private decimalPipe: DecimalPipe,
		private admRest: AdmRestService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		if (this.statePlano.plano) {
			this.planoDTO = this.statePlano.plano;
		}
		if (this.planoDTO) {
			if (this.planoDTO.duracoes) {
				this.planoDTO.duracoes = this.planoDTO.duracoes.sort(
					(a: any, b: any) => {
						if (+a.numeroMeses < +b.numeroMeses) {
							return -1;
						} else if (+a.numeroMeses > +b.numeroMeses) {
							return 1;
						}
						return 0;
					}
				);
			}
		}
		this.initFormDadosBasicos();
		this.initFormDadosContratuais();
		this.setNumeroParcelas();
		this.setNumeroVezesSemana();
		// this.initFormDuracaoValores();
		// this.initTableModalidades();
		// this.initTableDuracaoValor();
		// this.initTableProdutoServico();
	}

	ngAfterContentInit() {
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.cd.detectChanges();
	}

	voltarListagemPlanos() {
		this.planoStateService.removeFromSession();
		this.router.navigate(["adm", "planos"]);
	}

	initFormDadosBasicos() {
		const hoje = new Date();
		const dataFutura = new Date();
		dataFutura.setFullYear(dataFutura.getFullYear() + 1);
		this.formDadosBasicos = new FormGroup({
			descricao: new FormControl(null, [Validators.required]),
			tipoPlano: new FormControl(),
			vigenciaDe: new FormControl(hoje.getTime(), [Validators.required]),
			ingressoAte: new FormControl(dataFutura.getTime(), [Validators.required]),
			vigenciaAte: new FormControl(dataFutura.getTime(), [Validators.required]),
			produtoContrato: new FormControl(),
			creditoSessao: new FormControl(),
		});

		this.formDadosBasicos
			.get("creditoSessao")
			.valueChanges.subscribe((value: boolean) => {
				// this.initTableModalidades();
				// this.initFormDuracaoValores();
				this.planoDTO.creditoSessao = value;
			});
		this.populateFormByPlano(this.formDadosBasicos, this.planoDTO);
		this.populateObjectByForm(this.formDadosBasicos);
		this.formDadosBasicos
			.get("creditoSessao")
			.setValue(this.planoDTO.creditoSessao);
	}

	initFormDadosContratuais() {
		this.formDadosContratuais = new FormGroup({
			modeloContrato: new FormControl("", Validators.required),
			percentualMultaCancelamento: new FormControl("", Validators.required),
			restringirQtdMarcacaoPorDia: new FormControl(),
			restringirMarcacaoAulasColetivas: new FormControl(),
			dividirManutencaoParcelasEA: new FormControl(),
			produtoTaxaCancelamento: new FormControl(),
			creditoTreinoNaoCumulativo: new FormControl(),
		});
		this.populateFormByPlano(this.formDadosContratuais, this.planoDTO);
		this.populateObjectByForm(this.formDadosContratuais);
	}

	populateObjectByForm(form: FormGroup) {
		form.valueChanges.subscribe((value) => {
			this.planoDTO = this.planoStateService.plano;
			Object.keys(value).forEach((key) => {
				if (value[key] !== undefined && value[key] !== null) {
					this.planoDTO[key] = value[key];
				}
			});
			this.planoStateService.plano = this.planoDTO;
		});
	}

	populateFormByPlano(form: FormGroup, plano: Plano) {
		Object.keys(plano).forEach((key) => {
			if (form.controls.hasOwnProperty(key)) {
				form.get(key).setValue(plano[key]);
			}
		});
	}

	nextDadosBasicos() {}

	setNumeroParcelas() {
		for (let i = 1; i <= 12; i++) {
			this.numeroParcelasOptions.push({
				label: `${i}x`,
				value: i,
			});
		}
	}

	setNumeroVezesSemana() {
		for (let i = 1; i <= 7; i++) {
			this.vezesSemanaOptions.push({
				label: `${i}x`,
				value: i,
			});
		}
	}

	verifyIfNewExists(row, dataTable, rowIndex, keysAvoidCompare?: string[]) {
		let exists = false;
		if (!keysAvoidCompare) {
			keysAvoidCompare = [];
		}
		keysAvoidCompare.push("edit");
		keysAvoidCompare.push("codigo");
		dataTable.find((data, index) => {
			if (index !== rowIndex) {
				exists = this.compareObjects(row, data, "codigo", keysAvoidCompare);
				if (exists) {
					return;
				}
			}
		});
		return exists;
	}

	compareObjects(obj1, obj2, uniqueKey?, keysAvoidCompare?: string[]) {
		let equals = true;
		Object.keys(obj1).forEach((key) => {
			if (keysAvoidCompare.find((v) => v === key)) {
				return;
			}
			if (
				obj1[key] !== undefined &&
				obj2[key] !== undefined &&
				obj1[key] !== null &&
				obj2[key] !== null
			) {
				if (typeof obj1[key] !== "object") {
					if (obj1[key].toString() !== obj2[key].toString()) {
						equals = false;
						return;
					}
				} else {
					if (uniqueKey) {
						if (obj1[key][uniqueKey] !== obj2[key][uniqueKey]) {
							equals = false;
							return;
						}
					} else {
						equals = this.compareObjects(obj1[key], obj2[key]);
					}
				}
			}
		});
		return equals;
	}

	updateCondicaoPagamento(update) {
		if (update) {
			this.planoDTO = this.planoStateService.plano;
		}
	}

	save() {
		const planoDTO = this.planoStateService.plano;
		if (planoDTO.descricao && planoDTO.descricao.length > 200) {
			this.notificationService.error(
				"O nome do plano deve conter menos que 200 caracteres!"
			);
			return;
		}
		planoDTO.vendaCreditoTreino = true;
		planoDTO.duracoes.forEach((dur) => {
			dur.condicoesPagamento.forEach((condPagDur) => {
				if (condPagDur.tipoValor) {
					condPagDur.tipoValor = condPagDur.tipoValor.codigo;
				}
				if (condPagDur.tipoOperacao) {
					condPagDur.tipoOperacao = condPagDur.tipoOperacao.codigo;
				}
			});
		});
		this.planoService.save(planoDTO).subscribe(
			(response) => {
				this.admRest.notificarNovoPlanoAcesso("NOVO_PLANO_SALVAR");

				this.notificationService.success("Plano salvo com sucesso!");
				this.router.navigate(["adm", "planos"]);
				console.log(response);
			},
			(responseError) => {
				this.notificationService.error(responseError.error.meta.message);
				console.log(responseError);
			}
		);
	}
}
