<!--
<pacto-cat-stepper-simple (goBack)="voltarListagemPlanos()">
    <pacto-cat-step-simple stepLabel="Dados Básicos"
                           stepDescription="Informe os dados necessários para criar um novo plano.">
        <ng-template pactoCatStepLabel>Dados Básicos</ng-template>
        <ng-container *pactoButtonContainer>
            <pacto-cat-button pactoCatStepNext
                              (click)="nextDadosBasicos()"
                              class="margin-left-12"
                              [size]="'LARGE'"
                              [type]="'PRIMARY'"
                              [label]="'Avançar'">
            </pacto-cat-button>
        </ng-container>
        <adm-pc-dados-basicos [formGroup]="formDadosBasicos" [planoDTO]="planoDTO"></adm-pc-dados-basicos>
    </pacto-cat-step-simple>
    <pacto-cat-step-simple stepLabel="Modalidades" stepDescription="Defina as modalidades que farão parte do plano.">
        <ng-template pactoCatStepLabel>Modalidades</ng-template>
        <ng-container *pactoButtonContainer>
            <pacto-cat-button pactoCatStepPrevious
                              [size]="'LARGE'"
                              [type]="'OUTLINE'"
                              [label]="'Voltar'">
            </pacto-cat-button>
            <pacto-cat-button pactoCatStepNext
                              class="margin-left-12"
                              [disabled]="false"
                              [size]="'LARGE'"
                              [type]="'PRIMARY'"
                              [label]="'Avançar'">
            </pacto-cat-button>
        </ng-container>
        <adm-pc-modalidades [tableModalidades]="tableModalidades"
                            (isEditinOrAdding)="disableNextModalidade = $event"></adm-pc-modalidades>
    </pacto-cat-step-simple>
    <pacto-cat-step-simple stepLabel="Duração e Valores" stepDescription="Informe as variações de tempo e valor do plano.">
        <ng-container *pactoButtonContainer>
            <pacto-cat-button pactoCatStepPrevious
                              [size]="'LARGE'"
                              [type]="'OUTLINE'"
                              [label]="'Voltar'">
            </pacto-cat-button>
            <pacto-cat-button pactoCatStepNext
                              class="margin-left-12"
                              [size]="'LARGE'"
                              [type]="'PRIMARY'"
                              [label]="'Avançar'">
            </pacto-cat-button>
        </ng-container>
        <adm-pc-duracao-valor
                [vezesSemanaOptions]="vezesSemanaOptions"
                [numeroParcelasOptions]="numeroParcelasOptions"
                [table]="tableDuracaoValor"
                [form]="formDuracaoValores"
                (updateCondicoesPagmento)="updateCondicaoPagamento($event)"
        ></adm-pc-duracao-valor>
    </pacto-cat-step-simple>
    <pacto-cat-step-simple stepLabel="Produtos e Serviços" stepDescription="Informe os produtos e serviços sugeridos, seus valores dentro do plano e se são obrigatórios.">
        <ng-container *pactoButtonContainer>
            <pacto-cat-button pactoCatStepPrevious
                              [size]="'LARGE'"
                              [type]="'OUTLINE'"
                              [label]="'Voltar'">
            </pacto-cat-button>
            <pacto-cat-button pactoCatStepNext
                              class="margin-left-12"
                              [disabled]="disableNextProduto"
                              [size]="'LARGE'"
                              [type]="'PRIMARY'"
                              [label]="'Avançar'">
            </pacto-cat-button>
        </ng-container>
        <adm-pc-produtos [table]="tableProdutoServico"
                         (isEditinOrAdding)="disableNextProduto = $event"></adm-pc-produtos>
    </pacto-cat-step-simple>
    <pacto-cat-step-simple stepLabel="Dados Contratuais" stepDescription="Defina os termos de contratação do seu plano.">
        <ng-container *pactoButtonContainer>
            <pacto-cat-button pactoCatStepPrevious
                              [size]="'LARGE'"
                              [type]="'OUTLINE'"
                              [label]="'Voltar'">
            </pacto-cat-button>
            <pacto-cat-button pactoCatStepNext
                              class="margin-left-12"
                              [disabled]="false"
                              [size]="'LARGE'"
                              [type]="'PRIMARY'"
                              [label]="'Avançar'">
            </pacto-cat-button>
        </ng-container>
        <adm-pc-dados-contratual [form]="formDadosContratuais"></adm-pc-dados-contratual>
    </pacto-cat-step-simple>
    <pacto-cat-step-simple stepLabel="Condição de Pagamento"
                           stepDescription="Informe as negociações possível na contratação do plano, determinando como o pagamento será realizado.">
        <ng-container *pactoButtonContainer>
            <pacto-cat-button pactoCatStepPrevious
                              [size]="'LARGE'"
                              [type]="'OUTLINE'"
                              [label]="'Voltar'">
            </pacto-cat-button>
            <pacto-cat-button
                    (click)="save()"
                    class="margin-left-12"
                    [disabled]="false"
                    [size]="'LARGE'"
                    [type]="'PRIMARY'"
                    [label]="'Concluir'">
            </pacto-cat-button>
        </ng-container>
        <ng-container *ngIf="planoDTO.duracoes && planoDTO.duracoes.length > 0">
            <adm-pc-condicao-pagamento *ngFor="let duracao of planoDTO.duracoes"
                                       [duracao]="duracao"></adm-pc-condicao-pagamento>
        </ng-container>
        <ng-container *ngIf="!planoDTO.duracoes || planoDTO.duracoes?.length === 0">
            <pacto-cat-card-plain>
                <div class="table-wrapper pacto-shadow">
                    <span>Para criar uma condição de pagamento é necessário acessar o passo "Duração e Valores" e adicionar ao menos uma duração.</span>
                </div>
            </pacto-cat-card-plain>
        </ng-container>
    </pacto-cat-step-simple>
</pacto-cat-stepper-simple>
-->
