@import "src/assets/scss/pacto/plataforma-import.scss";

.texto-subtitulo {
	position: relative;
	flex-grow: 1;
}

.content-wrapper {
	width: 100%;
	display: flex;
	margin-bottom: 30px;
}

.icon-voltar {
	padding: 4px;
	cursor: pointer;
	margin-right: 16px;
}

.content-title {
	width: 100%;
}

.margin-left-12 {
	margin-left: 12px;
}

.card-plano {
	padding: 20px;
	width: 300px;
	min-height: 410px;
	position: relative;
	display: flex;
	flex-direction: column;
	margin-bottom: 30px;
}

.card-plano > pacto-cat-button {
	width: 100%;
	margin-top: auto;
}

.card-plano-row {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
}

.margin-right-30 {
	margin-right: 30px;
}

.titulo-card {
	margin-top: 140px;
	margin-bottom: 12px;
}

.descricao-card {
	margin-bottom: 30px;
}

.imagem-card {
	height: 150px;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
	background-size: cover;
	background-repeat: no-repeat;
}

@media (max-width: 1613px) {
	.card-plano {
		&:nth-child(3).margin-right-30 {
			margin-right: 0;
		}
	}
}

@media (max-width: 1253px) {
	.card-plano {
		&:nth-child(2).margin-right-30 {
			margin-right: 0;
		}

		&:nth-child(3).margin-right-30 {
			margin-right: 30px;
		}
	}
}

@media (max-width: 923px) {
	.card-plano {
		&.margin-right-30 {
			margin-right: 0;
		}
	}
}
