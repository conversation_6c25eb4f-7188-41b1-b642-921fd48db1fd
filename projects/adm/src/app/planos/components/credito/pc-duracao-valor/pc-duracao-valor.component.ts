import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	DialogService,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import {
	getTipoHorarioCreditoTreinoByCode,
	getTiposHorariosCreditoTreino,
	Plano,
	TipoHorarioCreditoTreino,
} from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";
import { Util } from "../../../../util/Util";

@Component({
	selector: "adm-pc-duracao-valor",
	templateUrl: "./pc-duracao-valor.component.html",
	styleUrls: ["./pc-duracao-valor.component.scss"],
})
export class PcDuracaoValorComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("columnNumeroMeses", { static: true })
	columnNumeroMeses: TemplateRef<any>;
	@ViewChild("columnQuantidadeDiasExtra", { static: true })
	columnQuantidadeDiasExtra: TemplateRef<any>;
	@ViewChild("columnNrMaximoParcelasCondPagamento", { static: true })
	columnNrMaximoParcelasCondPagamento: TemplateRef<any>;
	@ViewChild("columnTipoHorarioCreditoTreino", { static: true })
	columnTipoHorarioCreditoTreino: TemplateRef<any>;
	@ViewChild("columnNumeroVezesSemana", { static: true })
	columnNumeroVezesSemana: TemplateRef<any>;
	@ViewChild("columnQuantidadeCreditoMensal", { static: true })
	columnQuantidadeCreditoMensal: TemplateRef<any>;
	@ViewChild("columnQuantidadeCreditoCompra", { static: true })
	columnQuantidadeCreditoCompra: TemplateRef<any>;
	@ViewChild("columnValorUnitario", { static: true })
	columnValorUnitario: TemplateRef<any>;
	@ViewChild("columnValorTotal", { static: true })
	columnValorTotal: TemplateRef<any>;

	@Input() formDadosBasicos: FormGroup;
	@Output() isEditinOrAdding: EventEmitter<any> = new EventEmitter<any>();
	@Output() updateCondicoesPagmento: EventEmitter<any> =
		new EventEmitter<any>();
	table: PactoDataGridConfig;
	vezesSemanaOptions: Array<{ label: string; codigo: number }> = new Array<{
		label: string;
		codigo: number;
	}>();
	numeroParcelasOptions: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	form: FormGroup;
	qtdVezesSemanaOptions = [
		{
			label: 48,
			value: 48,
		},
		{
			label: 52,
			value: 52,
		},
	];
	plano: Plano;
	duracoes: Array<any> = new Array<any>();
	duracoesCreditoTreino: Array<any> = new Array<any>();
	duracoesByNumeroMeses: Array<any> = new Array<any>();

	constructor(
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private decimalPipe: DecimalPipe,
		private notificationService: SnotifyService,
		private dialogService: DialogService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.numeroParcelasOptions = this.planoCommonsService.numeroParcelas();
		this.vezesSemanaOptions = this.planoCommonsService.vezesSemanaOptions();
		this.initTableDuracaoValor();
		this.initFormDuracaoValores();
		if (this.formDadosBasicos) {
			this.formDadosBasicos
				.get("creditoSessao")
				.valueChanges.subscribe((value) => {
					this.initTableDuracaoValor();
				});
			this.formDadosBasicos
				.get("creditoTreinoNaoCumulativo")
				.valueChanges.subscribe((value) => {
					this.initTableDuracaoValor();
				});
		}
		if (this.plano.duracoes) {
			this.duracoesCreditoTreino = this.plano.duracoes;
			this.duracoesCreditoTreino.forEach((duracao) => {
				duracao.duracoesCreditoTreino.forEach((duracaoCreditoTreino) => {
					const duracaoObj: any = {
						codigo: duracao.codigo,
						codigoDurCredTreino: duracaoCreditoTreino.codigo,
						numeroMeses: duracao.numeroMeses,
						quantidadeDiasExtra: duracao.quantidadeDiasExtra,
						nrMaximoParcelasCondPagamento: this.numeroParcelasOptions.find(
							(v) => v.value === duracao.nrMaximoParcelasCondPagamento
						),
					};
					duracaoObj.tipoHorarioCreditoTreino =
						getTipoHorarioCreditoTreinoByCode(
							duracaoCreditoTreino.tipoHorarioCreditoTreino
						);
					duracaoObj.numeroVezesSemana = this.vezesSemanaOptions.find(
						(v) => v.codigo === duracaoCreditoTreino.numeroVezesSemana
					);
					duracaoObj.valorUnitario = duracaoCreditoTreino.valorUnitario;
					duracaoObj.quantidadeCreditoMensal =
						duracaoCreditoTreino.quantidadeCreditoMensal;
					duracaoObj.quantidadeCreditoCompra =
						duracaoCreditoTreino.quantidadeCreditoCompra;
					duracaoObj.valorTotal =
						duracaoCreditoTreino.quantidadeCreditoCompra *
						duracaoCreditoTreino.valorUnitario;
					duracaoObj.condicoesPagamento = duracao.condicoesPagamento;
					this.duracoes.push(duracaoObj);
				});
			});
		}
		this.updateTable();
	}

	confirm(event) {
		event.row.valorTotal =
			event.row.quantidadeCreditoCompra * event.row.valorUnitario;
		this.duracoes.forEach((dur) => {
			if (dur.numeroMeses === event.row.numeroMeses) {
				dur.nrMaximoParcelasCondPagamento =
					event.row.nrMaximoParcelasCondPagamento;
				dur.quantidadeDiasExtra = event.row.quantidadeDiasExtra;
			}
		});
		this.convertToPlanoDuracoes(undefined, undefined, event.rowIndex);
		this.plano = this.planoStateService.updatePlanoObj();
		this.plano.duracoes = this.duracoesCreditoTreino;
		this.planoStateService.plano = this.plano;
		this.updateCondicoesPagmento.emit(true);
	}

	delete(event) {
		let index;
		if (event.row.codigo) {
			const excecao = this.duracoes.find((ex, i) => {
				if (ex.codigo === event.row.codigo) {
					index = i;
					return ex;
				}
			});
			if (excecao && index !== undefined) {
				this.duracoes.splice(index, 1);
			}
		} else {
			this.duracoes.splice(event.index, 1);
		}
		this.plano = this.planoStateService.updatePlanoObj();
		this.convertToPlanoDuracoes(true, event.row.numeroMeses);
		this.plano = this.planoStateService.updatePlanoObj();
		this.plano.duracoes = this.duracoesCreditoTreino;
		this.planoStateService.plano = this.plano;
		this.updateCondicoesPagmento.emit(true);
	}

	convertToPlanoDuracoes(deleteDct = false, numeroMeses?, rowIndex?) {
		this.duracoesCreditoTreino = new Array<any>();
		this.duracoes.forEach((duracao, durIndex) => {
			const indexDurCred = Util.indexOfByAttr(
				this.duracoesCreditoTreino,
				"numeroMeses",
				duracao.numeroMeses
			);
			if (indexDurCred === -1) {
				this.duracoesCreditoTreino.push({
					codigo: duracao.codigo,
					numeroMeses: duracao.numeroMeses,
					quantidadeDiasExtra: duracao.quantidadeDiasExtra,
					condicoesPagamento: duracao.condicoesPagamento,
					nrMaximoParcelasCondPagamento: duracao.nrMaximoParcelasCondPagamento
						? duracao.nrMaximoParcelasCondPagamento.value
						: "",
					duracoesCreditoTreino: [
						{
							codigo: duracao.codigoDurCredTreino,
							tipoHorarioCreditoTreino: duracao.tipoHorarioCreditoTreino
								? duracao.tipoHorarioCreditoTreino.value
								: "",
							numeroVezesSemana: duracao.numeroVezesSemana
								? duracao.numeroVezesSemana.codigo
								: "",
							valorUnitario: duracao.valorUnitario,
							quantidadeCreditoCompra: duracao.quantidadeCreditoCompra,
							quantidadeCreditoMensal: duracao.quantidadeCreditoMensal,
							valorTotal:
								duracao.quantidadeCreditoCompra * duracao.valorUnitario,
						},
					],
				});
			} else {
				this.duracoesCreditoTreino[indexDurCred].duracoesCreditoTreino.push({
					codigo: duracao.codigoDurCredTreino,
					tipoHorarioCreditoTreino: duracao.tipoHorarioCreditoTreino
						? duracao.tipoHorarioCreditoTreino.value
						: "",
					numeroVezesSemana: duracao.numeroVezesSemana
						? duracao.numeroVezesSemana.codigo
						: "",
					valorUnitario: duracao.valorUnitario,
					quantidadeCreditoCompra: duracao.quantidadeCreditoCompra,
					quantidadeCreditoMensal: duracao.quantidadeCreditoMensal,
					valorTotal: duracao.quantidadeCreditoCompra * duracao.valorUnitario,
				});
			}
		});

		const duracoesCreditoTreinoFilter = this.duracoes.filter(
			(dct, index, array) => dct.numeroMeses === numeroMeses
		);
		if (
			duracoesCreditoTreinoFilter &&
			duracoesCreditoTreinoFilter.length === 0
		) {
			let index2;
			this.duracoesCreditoTreino.find((dct, index) => {
				if (dct.numeroMeses === numeroMeses) {
					index2 = index;
					return dct;
				}
			});
			if (index2 >= 0) {
				this.duracoesCreditoTreino.splice(index2, 1);
			}
		}
		this.updateTable();
	}

	updateTable() {
		this.table.dataAdapterFn = (serverData) => {
			return {
				content: this.duracoes,
			};
		};
	}

	initFormDuracaoValores() {
		this.form = new FormGroup({
			qtdSemanasAno: new FormControl(""),
		});
		this.form.get("qtdSemanasAno").valueChanges.subscribe((value) => {
			this.plano.qtdSemanasAno = value;
			this.planoStateService.updateState(this.plano);
		});
		if (this.plano.qtdSemanasAno) {
			this.form.get("qtdSemanasAno").setValue(this.plano.qtdSemanasAno);
		} else {
			this.form.get("qtdSemanasAno").setValue(48);
		}
	}

	getCreditoTreinoNaoCumulativo() {
		return this.formDadosBasicos
			? this.formDadosBasicos.get("creditoTreinoNaoCumulativo").value
			: this.plano.creditoTreinoNaoCumulativo;
	}

	private initTableDuracaoValor() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				numeroMeses: new FormControl(""),
				quantidadeDiasExtra: new FormControl(""),
				nrMaximoParcelasCondPagamento: new FormControl(""),
				tipoHorarioCreditoTreino: new FormControl(""),
				numeroVezesSemana: new FormControl(""),
				quantidadeCreditoCompra: new FormControl(""),
				quantidadeCreditoMensal: new FormControl(""),
				valorUnitario: new FormControl(""),
			}),
			beforeConfirm: (row: any, form: any, data: any, indexRow: any) =>
				this.beforeConfirmDuracaoValor(row, form, data, indexRow),
			columns: [
				{ nome: "codigo", titulo: "#", visible: false, width: "50px" },
				{
					nome: "numeroMeses",
					titulo: this.columnNumeroMeses,
					ordenavel: false,
					width: "200px",
					visible: true,
					editable: true,
					inputType: "number",
				},
				{
					nome: "quantidadeDiasExtra",
					titulo: this.columnQuantidadeDiasExtra,
					ordenavel: false,
					width: "200px",
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value
						: !this.plano.creditoSessao,
					editable: true,
					inputType: "number",
				},
				{
					nome: "nrMaximoParcelasCondPagamento",
					titulo: this.columnNrMaximoParcelasCondPagamento,
					width: "200px",
					visible: true,
					ordenavel: false,
					editable: true,
					inputSelectData: this.numeroParcelasOptions,
					inputType: "select",
					showAddSelectBtn: false,
					showSelectFilter: false,
					idSelectKey: "value",
				},
				{
					nome: "tipoHorarioCreditoTreino",
					titulo: this.columnTipoHorarioCreditoTreino,
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value
						: !this.plano.creditoSessao,
					ordenavel: false,
					editable: true,
					inputSelectData: getTiposHorariosCreditoTreino(),
					inputType: "select",
					width: "150px",
					idSelectKey: "value",
					showSelectFilter: false,
					showAddSelectBtn: false,
					selectOptionChange: (option, form, row) =>
						this.tipoHorarioChange(option, form, row),
				},
				{
					nome: "numeroVezesSemana",
					titulo: this.columnNumeroVezesSemana,
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value
						: !this.plano.creditoSessao,
					ordenavel: false,
					editable: true,
					inputType: "select",
					inputSelectData: this.vezesSemanaOptions,
					idSelectKey: "codigo",
					showSelectFilter: false,
					width: "100px",
					showAddSelectBtn: false,
					selectOptionChange: (option, form, row) =>
						this.vezesSemanaChange(form),
					isDisabled: (row) =>
						row.tipoHorarioCreditoTreino &&
						(row.tipoHorarioCreditoTreino.value ===
							TipoHorarioCreditoTreino.LIVRE_OBRIGATORIO_MARCAR_AULA ||
							row.tipoHorarioCreditoTreino.value ===
								TipoHorarioCreditoTreino.LIVRE),
				},
				{
					nome: "quantidadeCreditoMensal",
					titulo: this.columnQuantidadeCreditoMensal,
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value &&
						  this.formDadosBasicos.get("creditoTreinoNaoCumulativo").value
						: !this.plano.creditoSessao &&
						  this.plano.creditoTreinoNaoCumulativo,
					ordenavel: false,
					editable: true,
					width: "100px",
					inputType: "number",
					decimal: false,
					isDisabled: (row) =>
						this.getCreditoTreinoNaoCumulativo() &&
						row.tipoHorarioCreditoTreino &&
						row.tipoHorarioCreditoTreino.value ===
							TipoHorarioCreditoTreino.HORARIO_TURMA,
				},
				{
					nome: "quantidadeCreditoCompra",
					titulo: this.columnQuantidadeCreditoCompra,
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value ||
						  this.formDadosBasicos.get("creditoTreinoNaoCumulativo").value
						: !this.plano.creditoSessao ||
						  this.plano.creditoTreinoNaoCumulativo,
					ordenavel: false,
					editable: true,
					inputType: "number",
					width: "100px",
					decimal: false,
					isDisabled: (row) =>
						row.tipoHorarioCreditoTreino &&
						((this.getCreditoTreinoNaoCumulativo() &&
							(row.tipoHorarioCreditoTreino.value ===
								TipoHorarioCreditoTreino.LIVRE_OBRIGATORIO_MARCAR_AULA ||
								row.tipoHorarioCreditoTreino.value ===
									TipoHorarioCreditoTreino.LIVRE)) ||
							row.tipoHorarioCreditoTreino.value ===
								TipoHorarioCreditoTreino.HORARIO_TURMA),
				},
				{
					nome: "valorUnitario",
					titulo: "Valor unitário do crédito",
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value
						: !this.plano.creditoSessao,
					ordenavel: false,
					editable: true,
					width: "150px",
					inputType: "decimal",
					decimal: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
				},
				{
					nome: "valorTotal",
					titulo: this.columnValorTotal,
					width: "100px",
					visible: this.formDadosBasicos
						? !this.formDadosBasicos.get("creditoSessao").value
						: !this.plano.creditoSessao,
					ordenavel: false,
					editable: false,
					inputType: "decimal",
					decimal: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
				},
			],
		});
		this.table.formGroup
			.get("numeroMeses")
			.valueChanges.pipe()
			.subscribe((value) => {
				this.vezesSemanaChange(this.table.formGroup);
			});
		this.table.formGroup
			.get("quantidadeCreditoMensal")
			.valueChanges.pipe()
			.subscribe((value) => {
				if (this.getCreditoTreinoNaoCumulativo()) {
					this.table.formGroup
						.get("quantidadeCreditoCompra")
						.setValue(value * this.table.formGroup.get("numeroMeses").value);
				}
			});
	}

	private beforeConfirmDuracaoValor(
		row: any,
		form: any,
		data: any,
		indexRow: any
	) {
		const keysAvoidCompare = ["valorTotal"];
		this.plano = this.planoStateService.updatePlanoObj();
		const creditoSessao = this.formDadosBasicos
			? this.formDadosBasicos.get("creditoSessao").value
			: this.plano.creditoSessao;
		const creditoTreinoNaoCumulativo = this.formDadosBasicos
			? this.formDadosBasicos.get("creditoTreinoNaoCumulativo").value
			: this.plano.creditoTreinoNaoCumulativo;
		if (creditoSessao) {
			keysAvoidCompare.push("tipoHorarioCreditoTreino");
			keysAvoidCompare.push("numeroVezesSemana");
			keysAvoidCompare.push("quantidadeCreditoCompra");
			keysAvoidCompare.push("valorUnitario");
		}
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			indexRow,
			keysAvoidCompare
		);
		if (!this.getCreditoTreinoNaoCumulativo()) {
			const existsNumeroMesesDiasExtraDiferente = data.find(
				(value, index) =>
					form.get("numeroMeses").value === value.numeroMeses &&
					form.get("quantidadeDiasExtra").value !== value.quantidadeDiasExtra &&
					index !== indexRow
			);
			if (existsNumeroMesesDiasExtraDiferente) {
				this.notificationService.error(
					this.traducoes.getLabel("error-duracao-duplicado-table-dias-extras")
				);
				return false;
			}
			const existsNumeroMeses = data.find(
				(value, index) =>
					form.get("numeroMeses").value === value.numeroMeses &&
					form.get("quantidadeDiasExtra").value === value.quantidadeDiasExtra &&
					form.get("quantidadeCreditoCompra").value ===
						value.quantidadeCreditoCompra &&
					index !== indexRow
			);
			if (existsNumeroMeses) {
				this.notificationService.error(
					this.traducoes.getLabel("error-duracao-duplicado-table")
				);
				return false;
			}
		}
		if (!exists) {
			const numeroMeses = form.get("numeroMeses").value;
			const nrMaximoParcelasCondPagamento = form.get(
				"nrMaximoParcelasCondPagamento"
			).value;
			if (!creditoSessao) {
				const tipoHorarioCreditoTreino = form.get(
					"tipoHorarioCreditoTreino"
				).value;
				const numeroVezesSemana = form.get("numeroVezesSemana").value;
				const quantidadeCreditoCompra = creditoTreinoNaoCumulativo
					? form.get("quantidadeCreditoMensal").value
					: form.get("quantidadeCreditoCompra").value;
				const valorUnitario = form.get("valorUnitario").value;
				if (
					numeroMeses === null ||
					numeroMeses === undefined ||
					numeroMeses === "" ||
					nrMaximoParcelasCondPagamento === null ||
					nrMaximoParcelasCondPagamento === undefined ||
					nrMaximoParcelasCondPagamento === "" ||
					tipoHorarioCreditoTreino === null ||
					tipoHorarioCreditoTreino === undefined ||
					tipoHorarioCreditoTreino === "" ||
					numeroVezesSemana === null ||
					numeroVezesSemana === undefined ||
					numeroVezesSemana === "" ||
					quantidadeCreditoCompra === null ||
					quantidadeCreditoCompra === undefined ||
					quantidadeCreditoCompra === "" ||
					valorUnitario === null ||
					valorUnitario === undefined ||
					valorUnitario === ""
				) {
					this.notificationService.error(
						this.traducoes.getLabel("error-table-data-empty")
					);
					return false;
				}
			} else {
				if (
					numeroMeses === null ||
					numeroMeses === undefined ||
					numeroMeses === "" ||
					numeroMeses === 0 ||
					nrMaximoParcelasCondPagamento === null ||
					nrMaximoParcelasCondPagamento === undefined ||
					nrMaximoParcelasCondPagamento === ""
				) {
					this.notificationService.error(
						this.traducoes.getLabel("error-table-data-empty")
					);
					return false;
				}
			}
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("dado-duplicado-table")
			);
			return false;
		}

		return true;
	}

	private tipoHorarioChange(option: any, form: FormGroup, row: any) {
		if (option) {
			switch (option.value) {
				case TipoHorarioCreditoTreino.LIVRE:
				case TipoHorarioCreditoTreino.LIVRE_OBRIGATORIO_MARCAR_AULA:
					form
						.get("numeroVezesSemana")
						.setValue(this.vezesSemanaOptions.find((v) => v.codigo === 7));
					form.get("numeroVezesSemana").disable();
					form.get("quantidadeCreditoMensal").enable();
					if (!this.getCreditoTreinoNaoCumulativo()) {
						form.get("quantidadeCreditoCompra").enable();
					} else {
						form.get("quantidadeCreditoCompra").disable();
					}
					break;
				case TipoHorarioCreditoTreino.HORARIO_TURMA:
					form.get("numeroVezesSemana").enable();
					form.get("quantidadeCreditoCompra").disable();
					if (!this.getCreditoTreinoNaoCumulativo()) {
						form.get("quantidadeCreditoMensal").enable();
					} else {
						form.get("quantidadeCreditoMensal").disable();
					}
					break;
			}
		}
	}

	private vezesSemanaChange(form: FormGroup) {
		if (
			form.get("tipoHorarioCreditoTreino").value &&
			form.get("tipoHorarioCreditoTreino").value.value ===
				TipoHorarioCreditoTreino.HORARIO_TURMA
		) {
			let qtdSemanaMes = 4;
			if (this.form.get("qtdSemanasAno").value === 52) {
				qtdSemanaMes = 4.34524;
			}
			form
				.get("quantidadeCreditoMensal")
				.setValue(form.get("numeroVezesSemana").value.codigo * qtdSemanaMes);
			form
				.get("quantidadeCreditoCompra")
				.setValue(
					form.get("numeroMeses").value *
						form.get("numeroVezesSemana").value.codigo *
						qtdSemanaMes
				);
		}
	}

	edit(event: any) {}
}
