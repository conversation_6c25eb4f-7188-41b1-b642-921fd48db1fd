<div class="col-12" style="max-width: 300px">
	<pacto-cat-form-select
		[control]="form.get('qtdSemanasAno')"
		[items]="qtdVezesSemanaOptions"
		i18n-label="@@plano:qtd-semanas-ano"
		id="select-qtd-semanas-ano-plano"
		idKey="value"
		label="Quantidade de semanas no ano"></pacto-cat-form-select>
</div>

<pacto-cat-card-plain>
	<pacto-cat-table-editable
		(confirm)="confirm($event)"
		(delete)="delete($event)"
		(edit)="edit($event)"
		(isEditingOrAddingItem)="isEditinOrAdding.emit($event)"
		[isEditable]="true"
		[showAddRow]="true"
		[table]="table"
		idSuffix="table-duracao-plano"></pacto-cat-table-editable>
</pacto-cat-card-plain>

<ng-template #columnNumeroMeses>
	<span i18n="@@adm:table-column-numero-meses">Duração (em meses)</span>
</ng-template>

<ng-template #columnQuantidadeDiasExtra>
	<span i18n="@@adm:table-column-quantidade-dias-extra">Dias extras</span>
</ng-template>

<ng-template #columnNrMaximoParcelasCondPagamento>
	<span i18n="@@adm:table-column-numero-maximo-parcelas-cond-pagamento">
		Número de parcelas
	</span>
</ng-template>

<ng-template #columnTipoHorarioCreditoTreino>
	<span i18n="@@adm:table-column-tipo-horario-credito-treino">
		Tipo de horário
	</span>
</ng-template>

<ng-template #columnNumeroVezesSemana>
	<span i18n="@@adm:table-column-numero-vezes-semana">Vezes na semana</span>
</ng-template>

<ng-template #columnQuantidadeCreditoMensal>
	<span i18n="@@adm:table-column-qtd-credito-mensal">
		Quantidade de crédito Mensal
	</span>
</ng-template>

<ng-template #columnQuantidadeCreditoCompra>
	<span i18n="@@adm:table-column-quantidade-credito-compra">
		Quantidade de créditos
	</span>
</ng-template>

<ng-template #columnValorUnitario>
	<span i18n="@@adm:table-column-valor-unitario">
		Valor unitário do crédito
	</span>
</ng-template>

<ng-template #columnValorTotal>
	<span i18n="@@adm:table-column-valor-total">Valor total</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@adm:error-table-data-empty" xingling="error-table-data-empty">
		Preencha todos os dados da tabela
	</span>
	<span i18n="@@adm:dado-duplicado-table" xingling="dado-duplicado-table">
		Já existe um dado na tabela com estes valores.
	</span>
	<span
		i18n="@@adm-plano:duracao-duplicado-table-dias-extras"
		xingling="error-duracao-duplicado-table-dias-extras">
		Já existe uma duração na tabela com este valor. Necessário informar a
		quantidade de dias extras igual a duração já cadastrada.
	</span>
	<span
		i18n="@@adm-plano:duracao-duplicado-table"
		xingling="error-duracao-duplicado-table">
		Já existe uma duração na tabela com este valor.
	</span>
</pacto-traducoes-xingling>
