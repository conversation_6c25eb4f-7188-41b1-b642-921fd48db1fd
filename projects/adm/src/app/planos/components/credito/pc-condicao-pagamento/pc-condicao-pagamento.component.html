<pacto-cat-card-plain *ngIf="duracao" style="margin-bottom: 20px">
	<div class="table-wrapper pacto-shadow">
		<div class="pct-cond-pag-title">
			<span i18n="@@plano:cond-pag-title">
				Duração de {{ duracao.numeroMeses }}
				{{
					duracao.numeroMeses == 1
						? traducao.getLabel("mes")
						: traducao.getLabel("meses")
				}}
			</span>
		</div>

		<pacto-cat-table-editable
			#tableCondicaoPagamento
			(confirm)="confirm($event)"
			(delete)="delete($event)"
			[isEditable]="true"
			[showAddRow]="true"
			[table]="table"
			class="relatorio"
			idSuffix="table-condicao-pag-plano"></pacto-cat-table-editable>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:valor" xingling="VE">Valor</span>
	<span i18n="@@adm:percentual" xingling="PD">Percentual</span>
	<span i18n="@@adm:acrescimo" xingling="AC">Acréscimo</span>
	<span i18n="@@adm:reducao" xingling="RE">Redução</span>
	<span i18n="@@adm:mes" xingling="mes">mês</span>
	<span i18n="@@adm:meses" xingling="meses">meses</span>
</pacto-traducoes-xingling>
