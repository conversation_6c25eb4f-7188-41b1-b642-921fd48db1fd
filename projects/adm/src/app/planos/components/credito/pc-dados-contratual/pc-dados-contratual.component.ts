import { Component, OnInit, ViewChild } from "@angular/core";
import { FormGroup } from "@angular/forms";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PlanoApiProdutoService } from "plano-api";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { Plano, TipoPlano } from "../../../plano.model";
import { PcConfigDadosContratuaisComponent } from "../pc-config-dados-contratuais/pc-config-dados-contratuais.component";
import { AdmRestService } from "../../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { PlanoService } from "../../cadastrar-plano/plano.service";

@Component({
	selector: "adm-pc-dados-contratual",
	templateUrl: "./pc-dados-contratual.component.html",
	styleUrls: ["./pc-dados-contratual.component.scss"],
})
export class PcDadosContratualComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	form: FormGroup;
	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "PL",
	};
	modeloContratoSelectBuilder: SelectFilterParamBuilder;
	produtoSelectBuilder: SelectFilterParamBuilder;
	plano: Plano;

	constructor(
		public admRestService: AdmRestService,
		private dialogService: DialogService,
		public produtoService: PlanoApiProdutoService,
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private planoService: PlanoService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.form = this.planoCommonsService.formDadosContratuais(
			TipoPlano.PLANO_CREDITO
		);
		this.planoCommonsService.updateFormDadosContratuais(this.form, this.plano);
		this.modeloContratoSelectBuilder =
			this.planoCommonsService.modeloContratoSelectBuilder;
		this.produtoSelectBuilder = this.planoCommonsService.produtoSelectBuilder;
		this.planoCommonsService.updatePlanoDadosContratuais(this.form, this.plano);
	}

	configuracoesAvancadas() {
		const dialogRef = this.dialogService.open(
			this.traducao.getLabel("advanced-config"),
			PcConfigDadosContratuaisComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoStateService.plano;
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {})
				.catch((error) => {
					console.log(error);
				});
		}
	}

	aplicarContratosJaLancados() {
		if (!this.plano.modeloContrato) {
			this.notificationService.error("Selecione um modelo de contrato");
			return;
		}
		const dialogRef = this.dialogService.confirm(
			"Confirmar alteração para contratos já lançados!",
			"Os contratos já lançados serão alterados permanentemente. Deseja continuar?",
			"Confirmar"
		);

		dialogRef.result
			.then((result) => {
				this.planoService
					.aplicarParaContratosJaLancados({
						codigo: this.plano.codigo,
						modeloDeContrato: this.plano.modeloContrato.codigo,
						tipoPlano: this.plano.tipoPlano,
					})
					.subscribe(
						(response) => {
							this.notificationService.success(
								"Texto aplicado para contratos já lançados"
							);
						},
						(httpResponseError) => {
							if (httpResponseError.error && httpResponseError.error.meta) {
								this.notificationService.error(
									httpResponseError.error.meta.message
								);
							} else {
								console.log(httpResponseError);
							}
						}
					);
			})
			.catch((error) => {
				console.log("error", error);
			});
	}
}
