<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[addtionalFilters]="modeloContratoAddtionalFilters"
					[control]="form.get('modeloContrato')"
					[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
					[idKey]="'codigo'"
					[labelKey]="'descricao'"
					[paramBuilder]="modeloContratoSelectBuilder"
					errorMsg="Selecione um modelo de contrato"
					i18n-errorMsg="@@plano:error-modelo-contrato"
					i18n-label="@@plano:label-modelo-contrato"
					id="select-modelo-contrato-plano"
					label="Modelo de contrato do plano"></pacto-cat-form-select-filter>

				<pacto-cat-button
					class="mx-auto d-block"
					(click)="aplicarContratosJaLancados()"
					label="Aplicar à contratos já lançados"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					ds3="true"
					id="btn-advanced-config-contratual-plano"></pacto-cat-button>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-input-number
					[formControl]="form.get('percentualMultaCancelamento')"
					decimal="true"
					errorMsg="Campo obrigatório"
					i18n-label="@@plano:label-percentual-multa-cancelamento"
					id="input-percentual-multa-cancelamento-plano"
					label="Percentual da multa de cancelamento"
					max="100"
					maxlength="5"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[control]="form.get('produtoTaxaCancelamento')"
					[endpointUrl]="
						admRestService.buildFullUrlPlano(
							produtoService.produtosCancelamento
						)
					"
					[paramBuilder]="produtoSelectBuilder"
					errorMsg="Por favor selecione um produto"
					i18n-errorMsg="@@plano:error-produto-taxa-cancelamento"
					i18n-label="@@plano:label-produto-taxa-cancelamento"
					id="select-produto-taxa-cancelamento-plano"
					idKey="codigo"
					label="Produto taxa cancelamento:"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>

		<div class="row mb-3 mt-5">
			<div class="col-md-6">
				<pacto-cat-checkbox
					[control]="form.get('dividirManutencaoParcelasEA')"
					i18n-label="@@plano:label-dividir-valor-manutencao-parcelas"
					id="check-dividir-manutencao-pacelas"
					label="Dividir valor da manutenção nas parcelas"></pacto-cat-checkbox>
			</div>
		</div>

		<div class="row">
			<div class="col-md-5">
				<pacto-cat-button
					(click)="configuracoesAvancadas()"
					[label]="traducao.getLabel('advanced-config')"
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					id="btn-advanced-config-contratual-plano"></pacto-cat-button>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:advanced-config" xingling="advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
