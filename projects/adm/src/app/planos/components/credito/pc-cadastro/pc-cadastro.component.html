<ng-container *ngIf="loading">
	<div class="loader">
		<img src="assets/images/loading.svg" />
		<span i18n="@@plano:loading-message">Carregando plano...</span>
	</div>
</ng-container>
<pacto-cat-layout-v2 *ngIf="!loading">
	<pacto-cat-stepper-simple (goBack)="voltarParaListagemPlano()">
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepDescription-basico"
			i18n-stepLabel="@@plano:steplabel-basico"
			stepDescription="Informe os dados necessários para criar um novo plano"
			stepLabel="Dados Básicos">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[disabled]="
						dadosBasicosComponent.form && dadosBasicosComponent.form.invalid
					"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-dados-basicos"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pc-dados-basicos
				#dadosBasicosComponent
				(nome)="planoNome = $event"></adm-pc-dados-basicos>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepdescription-modalidade"
			i18n-stepLabel="@@plano:steplabel-modalidade"
			stepDescription="Defina as modalidades que farão parte do plano."
			stepLabel="{{ planoNome | captalize }} / Modalidades">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-modalidades"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="disableNextModalidade()"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-modalidades"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pc-modalidades
				(isEditinOrAdding)="isEditingModalidade = $event"
				[formDadosBasicos]="dadosBasicosComponent.form"></adm-pc-modalidades>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepdescription-durval"
			i18n-stepLabel="@@plano:steplabel-durval"
			stepDescription="Informe as variações de tempo e valor do plano."
			stepLabel="{{ planoNome | captalize }} / Duração e Valores">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-table-duracao-valor"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="disableNextDuracoes()"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-table-duracao-valor"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pc-duracao-valor
				(isEditinOrAdding)="isEditingDuracaoValor = $event"
				(updateCondicoesPagmento)="updateCondicaoPagamento($event)"
				[formDadosBasicos]="dadosBasicosComponent.form"></adm-pc-duracao-valor>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:steplabel-description"
			i18n-stepLabel="@@plano:steplabel-horario"
			stepDescription="Informe as variações de tempo e valor do plano."
			stepLabel="{{ planoNome | captalize }} / Horários">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-table-horario"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="disableNextHorarios()"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-table-horario"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pc-table-horario
				(isEditinOrAdding)="isEditingHorario = $event"></adm-pc-table-horario>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepDescription-prod-serv"
			i18n-stepLabel="@@plano:steplabel-prod-serv"
			stepDescription="Informe os produtos e serviços sugeridos, seus valores dentro do plano e se são obrigatórios."
			stepLabel="{{ planoNome | captalize }} / Produtos e Serviços">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-table-produtos"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-table-produtos"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pc-produtos
				(isEditinOrAdding)="isEditingProdutos = $event"></adm-pc-produtos>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			stepLabel="{{ planoNome | captalize }} / Dados Contraturais"
			i18n-stepDescription="@@plano:stepdescription-dadoscontratuais"
			i18n-stepLabel="@@plano:steplabel-dadoscontratuais"
			stepDescription="Defina os termos de contratação do seu plano.">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-dados-contraturais"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="dadosContratualComponent.form?.invalid"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-dados-contratuais"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pc-dados-contratual
				#dadosContratualComponent></adm-pc-dados-contratual>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepDescription-condpag"
			i18n-stepLabel="@@plano:steplabel-condpag"
			stepDescription="Informe as negociações possiveis na contratação do plano, determinando como o pagamento será realizado."
			stepLabel="{{ planoNome | captalize }} / Condição de Pagamento">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-condicao-pagamento"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					[disabled]="saving"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-concluir"
					id="btn-plano-concluir-condicao-pagamento"
					label="Concluir"></pacto-cat-button>
			</ng-container>
			<ng-container *ngIf="plano.duracoes && plano.duracoes.length > 0">
				<adm-pc-condicao-pagamento
					*ngFor="let duracao of plano.duracoes"
					[duracao]="duracao"></adm-pc-condicao-pagamento>
			</ng-container>
			<ng-container *ngIf="!plano.duracoes || plano.duracoes?.length === 0">
				<pacto-cat-card-plain>
					<div class="table-wrapper pacto-shadow">
						<span i18n="@@plano:condpag-hint-durval">
							Para criar uma condição de pagamento é necessário acessar o passo
							"Duração e Valores" e adicionar ao menos uma duração.
						</span>
					</div>
				</pacto-cat-card-plain>
			</ng-container>
		</pacto-cat-step-simple>
	</pacto-cat-stepper-simple>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:descricao-length-error" xingling="descricao-length-error">
		O nome do plano deve conter menos que 200 caracteres!
	</span>
	<span i18n="@@plano:modalidade-zerada-hint" xingling="modalidade-zerada-hint">
		Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais
		uma modalidade junto que tenha valor, ou então coloque valor no cadastro
		dela.
	</span>
	<span
		i18n="@@plano:modalidade-zerada-error"
		xingling="modalidade-zerada-error">
		Você adicionou modalidade com valor zerado na aba "modalidades". Como este
		plano não foi marcado como "PLANO BOLSA", esta operação não é permitida.
	</span>
	<span i18n="@@plano:saved-success" xingling="saved-success">
		Plano salvo com sucesso.
	</span>
</pacto-traducoes-xingling>
