import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { Plano, TipoPlano } from "../../../plano.model";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { AdmRestService } from "../../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "adm-pc-cadastro",
	templateUrl: "./pc-cadastro.component.html",
	styleUrls: ["./pc-cadastro.component.scss", "../../../css/plano.scss"],
})
export class PcCadastroComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	loading = false;
	saving = false;
	plano: Plano = new Plano();
	isEditingModalidade: boolean;
	isEditingDuracaoValor: boolean;
	isEditingHorario: boolean;
	isEditingProdutos: boolean;
	planoNome = "";
	constructor(
		private planoCommonsService: PlanoCommonsService,
		private planoStateService: PlanoStateService,
		private planoService: PlanoService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private activatedRoute: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router
	) {}

	ngOnInit() {
		const clonar = this.activatedRoute.snapshot.queryParamMap.get("clonar");
		if (clonar) {
			this.loading = true;
			this.planoCommonsService
				.clonar(this.activatedRoute, this.planoService)
				.subscribe((response) => {
					this.plano = response.content;
					this.plano = this.planoCommonsService.populateModalidadeAux(
						this.plano
					);
					this.plano = this.planoCommonsService.populateHorarioValor(
						this.plano
					);
					this.planoStateService.updateState(this.plano);
					this.router.navigate(["adm", "planos", "novo-plano", "credito"]);
					this.loading = false;
					setTimeout(() => this.cd.detectChanges());
				});
		} else {
			this.plano = this.planoStateService.updatePlanoObj();
			this.plano.tipoPlano = TipoPlano.PLANO_CREDITO;
			this.plano.diasVencimentoProrata = undefined;
			if (!this.plano.planoRecorrencia) {
				this.plano.planoRecorrencia = {};
			}
			this.planoStateService.updateState(this.plano);
			this.cd.detectChanges();
		}
	}

	voltarParaListagemPlano() {
		this.planoCommonsService.voltarParaListagem();
	}

	updateCondicaoPagamento(update) {
		if (update) {
			this.plano = this.planoStateService.updatePlanoObj();
		}
	}

	salvar() {
		if (!this.saving) {
			const plano = this.planoCommonsService.convertPlano(
				this.planoStateService.updatePlanoObj()
			);
			plano.vendaCreditoTreino = true;

			if (!this.plano.diasVencimentoProrata) {
				this.planoCommonsService.initDiasProrata(this.plano);
			}
			this.planoCommonsService.updateCondicoesPagamento(plano);
			if (!plano.bolsa && plano.modalidades) {
				let existeModalidadeNaoZerada = false;
				for (const d of plano.modalidades) {
					if (d.modalidade.valorMensal > 0) {
						existeModalidadeNaoZerada = true;
						break;
					}
				}
				if (!existeModalidadeNaoZerada) {
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-hint"),
						{
							timeout: 10000,
						}
					);
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-error"),
						{
							timeout: 10000,
						}
					);
					return;
				}
			}
			this.saving = true;
			this.planoService.save(plano).subscribe(
				(response) => {
					this.admRest.notificarNovoPlanoAcesso("NOVO_PLANO_SALVAR");
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					this.voltarParaListagemPlano();
					this.saving = false;
				},
				(responseError) => {
					this.saving = false;
					if (responseError.error) {
						if (responseError.error.meta) {
							this.notificationService.error(
								responseError.error.meta.messageValue
							);
						}
					}
				}
			);
		}
	}

	disableNextModalidade() {
		return (
			(this.planoStateService.plano &&
				!this.planoStateService.plano.modalidadesAux) ||
			this.planoStateService.plano.modalidadesAux.length === 0 ||
			this.isEditingModalidade
		);
	}

	disableNextDuracoes() {
		return (
			(this.planoStateService.plano &&
				!this.planoStateService.plano.duracoes) ||
			this.planoStateService.plano.duracoes.length === 0 ||
			this.isEditingDuracaoValor
		);
	}

	disableNextHorarios() {
		return (
			(this.planoStateService.plano &&
				!this.planoStateService.plano.horarios) ||
			this.planoStateService.plano.horarios.length === 0 ||
			this.isEditingHorario
		);
	}
}
