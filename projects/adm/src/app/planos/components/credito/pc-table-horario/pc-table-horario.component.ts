import {
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { Plano } from "../../../plano.model";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pc-table-horario",
	templateUrl: "./pc-table-horario.component.html",
	styleUrls: ["./pc-table-horario.component.scss"],
})
export class PcTableHorarioComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnHorario", { static: true }) columnHorario: TemplateRef<any>;
	@Output() isEditinOrAdding: EventEmitter<any> = new EventEmitter<any>();
	table: PactoDataGridConfig;
	horarios: Array<any> = new Array<any>();
	plano: Plano;

	constructor(
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private admRestService: AdmRestService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.plano.horarios) {
			this.horarios = this.plano.horarios;
		}
		this.initTable();
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.horarios,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				horario: new FormControl(),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "horario",
					titulo: this.columnHorario,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: true,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term || "",
								quicksearchFields: ["descricao"],
							}),
						};
					},
					showAddSelectBtn: false,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					infiniteScrollEnabled: true,
					width: "200px",
					endpointUrl:
						this.admRestService.buildFullUrlPlano("horarios") + "/only-ativos",
				},
			],
		});
	}

	confirm(event) {
		Object.keys(event.form.controls).forEach(
			(key) => (event.row[key] = event.form.get(key).value)
		);
		this.horarios.forEach((planoModalidade) => {
			if (planoModalidade.vezesSemana) {
				planoModalidade.vezesSemana = planoModalidade.vezesSemana.codigo;
			}
		});
		if (
			this.horarios &&
			event &&
			event.row &&
			event.row.codigo &&
			event.row.codigo !== undefined &&
			event.row.codigo !== null &&
			event.row.codigo !== ""
		) {
			this.horarios.forEach((h) => {
				if (h.codigo === event.row.codigo) {
					h.edit = false;
				}
			});
		}
		this.plano.horarios = this.horarios;
		this.planoStateService.updateState(this.plano);
	}

	delete(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.horarios) {
			let index;
			if (event.row.codigo) {
				const finded = this.horarios.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (finded && index !== undefined) {
					this.horarios.splice(index, 1);
				}
			} else {
				this.horarios.splice(event.index, 1);
			}
			this.plano.horarios = this.horarios;
		}
		this.planoStateService.updateState(this.plano);
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this.plano = this.planoStateService.plano;
		const horario = form.get("horario").value;
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			rowIndex
		);
		if (!exists) {
			if (horario === null || horario === undefined) {
				this.notificationService.error(
					this.traducao.getLabel("horario-obrigatorio")
				);
				return false;
			}

			if (
				data.find(
					(d, index) =>
						index !== rowIndex && d.horario.codigo === horario.codigo
				)
			) {
				this.notificationService.error(
					this.traducao.getLabel("horario-incluido")
				);
				return false;
			}
		} else {
			this.notificationService.error(
				this.traducao.getLabel("dado-duplicado-table")
			);
		}
		return !exists;
	}
}
