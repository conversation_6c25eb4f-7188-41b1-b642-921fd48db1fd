<div class="modal-content">
	<div class="grid-container">
		<div class="table-wrapper pacto-shadow">
			<div class="row align-items-center">
				<div class="col-md-5 mt-4">
					<pacto-cat-form-select-filter
						id="select-pacote"
						*ngIf="formGroup.get('pacote')"
						i18n-label="@@adm:label-termo-aceite"
						label="Escolha um pacote"
						[control]="formGroup.get('pacote')"
						[idKey]="'codigo'"
						[labelKey]="'descricao'"
						[options]="pacotes"></pacto-cat-form-select-filter>
				</div>
				<div class="icon">
					<small class="cor-azulim05">ou</small>
				</div>
				<div class="col-md-5">
					<pacto-cat-form-input
						id="input-plano-valor-referencia"
						i18n-label="@@plano:label-valor-referencia"
						label="Valor de Referência"
						[control]="formGroup.get('valorReferencia')"
						type="number"
						maxlength="9"
						placeholder="R$ 00,00"></pacto-cat-form-input>
				</div>
			</div>
			<div class="row align-items-center">
				<div class="col-md-5">
					<pacto-cat-form-input
						id="input-plano-numero-meses"
						i18n-label="@@plano:label-numero-meses"
						label="Número de Meses"
						[control]="formGroup.get('numeroMeses')"
						type="number"
						maxlength="5"></pacto-cat-form-input>
				</div>
				<div class="icon">
					<small class="cor-azulim05">+</small>
				</div>
				<div class="col-md-5">
					<pacto-cat-form-input
						id="input-plano-dias-extras"
						i18n-label="@@plano:label-descricao"
						label="Dias extras"
						[control]="formGroup.get('diasExtras')"
						maxlength="5"
						type="number"></pacto-cat-form-input>
				</div>
				<div class="icon">
					<span>
						<small>= {{ dias }} dias</small>
					</span>
				</div>
			</div>

			<div class="row align-items-center mt-4">
				<div class="col-md-5">
					<pacto-cat-select
						class="filter"
						id="input-plano-forma-calculo"
						label="Forma de cálculo"
						[control]="formGroup.get('formaCalculo')"
						[items]="formasDeCalculo"
						[idKey]="'codigo'"
						[labelKey]="'nome'"></pacto-cat-select>
				</div>
				<div class="space"></div>
				<div class="col-md-5">
					<pacto-cat-select
						class="filter"
						id="input-plano-tipo-operacao"
						label="Tipo de Operação"
						[control]="formGroup.get('tipoOperacao')"
						[items]="tiposOperacao"
						[idKey]="'codigo'"
						[labelKey]="'nome'"></pacto-cat-select>
				</div>
			</div>
			<div class="row align-items-center mt-4">
				<div class="col-md-5">
					<pacto-cat-form-input-number
						id="input-plano-numero-parcela"
						label="N° maximo de parcelas"
						[formControl]="formGroup.get('numeroParcelas')"
						placeholder="0"
						maxlength="3"
						type="number"></pacto-cat-form-input-number>
				</div>
				<div class="space"></div>
				<div class="col-md-5">
					<pacto-cat-form-input-number
						id="input-plano-valor-desejado-mensal"
						label="Valor desejado mensal"
						[formControl]="formGroup.get('valorDesejadoMensal')"
						placeholder="R$ 00,00"
						[decimal]="true"
						maxlength="7"
						type="number"></pacto-cat-form-input-number>
				</div>
				<div class="icon-calculo">
					<span
						id="icon-calculo-valor-desejado-mensal"
						(click)="calcularValorAjusteMensal()"
						class="hpe-action-item"
						ngbTooltip="Calcular valor desejado mensal">
						<i class="pct pct-calculator cor-azulim05"></i>
					</span>
				</div>
			</div>
			<div class="row align-items-center mt-2">
				<div class="col-md-5">
					<pacto-cat-form-input-number
						id="input-plano-valor-desejado-parcela"
						i18n-label="@@plano:label-descricao"
						label="Valor desejado da parcela"
						[formControl]="formGroup.get('valorDesejadoParcela')"
						[decimal]="true"
						placeholder="R$ 00,00"
						maxlength="7"></pacto-cat-form-input-number>
				</div>
				<div class="icon-calculo">
					<span
						id="icon-calculo-valor-parcela"
						(click)="calcularValorParcela()"
						class="hpe-action-item"
						ngbTooltip="Calcular valor de Parcela">
						<i class="pct pct-calculator cor-azulim05"></i>
					</span>
				</div>
				<div class="col-md-5">
					<pacto-cat-form-input-number
						id="input-plano-valor-mensal-ajuste"
						i18n-label="@@plano:label-descricao"
						label="Valor mensal para Ajuste"
						[formControl]="formGroup.get('valorDesejado')"
						placeholder="R$ 00,00"
						[decimal]="true"
						maxlength="7"></pacto-cat-form-input-number>
				</div>
				<div class="icon-calculo">
					<span
						id="icon-calculo-ajuste-mensal"
						(click)="calcularDuracao()"
						class="hpe-action-item"
						ngbTooltip="Calcular valor de ajuste">
						<i class="pct pct-calculator cor-azulim05"></i>
					</span>
				</div>
			</div>
			<div class="row">
				<div class="col-md-5">
					<pacto-cat-switch
						[control]="formGroup.get('situacao')"></pacto-cat-switch>
					<span [style.marginLeft]="'1rem'">Inativar</span>
				</div>
			</div>
		</div>
	</div>
	<div class="footer">
		<pacto-cat-button
			id="btn-save-duracao"
			label="Salvar Condição de Duração"
			style="margin-right: 10px"
			(click)="salvar()"></pacto-cat-button>
	</div>
</div>
