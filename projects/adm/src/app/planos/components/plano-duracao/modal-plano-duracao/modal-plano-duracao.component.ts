import { Component, OnInit } from "@angular/core";
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { PlanoApiCondicaoPagamentoService } from "plano-api";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-modal-plano-duracao",
	templateUrl: "./modal-plano-duracao.component.html",
	styleUrls: ["./modal-plano-duracao.component.scss"],
})
export class ModalPlanoDuracaoComponent implements OnInit {
	pacotes: any[];
	planoDuracao = {
		codigo: null,
		numeroMeses: null,
		nrMaximoParcelasCondPagamento: 0,
		carencia: 0,
		quantidadeDiasExtra: 0,
		pontos: 0,
		situacao: false,
		tipoValor: "",
		tipoOperacao: "",
		valorDesejado: 0,
		valorDesejadoParcela: 0,
		valorDesejadoMensal: 0,
		duracoesCreditoTreino: [],
		condicoesPagamento: [] as any,
		replicar: null,
		valorEspecifico: 0,
		percentualdesconto: 0,
		totalDias: 0,
		codigoPacoteReferencia: null,
	};

	pacote: any;

	excecoes: Array<any> = new Array<any>();
	condicoesPagamento = [];

	dias: number = 0;
	formGroup: FormGroup = new FormGroup({
		pacote: new FormControl(),
		valorReferencia: new FormControl(),
		numeroParcelas: new FormControl(),
		numeroMeses: new FormControl(),
		diasExtras: new FormControl(),
		formaCalculo: new FormControl(),
		tipoOperacao: new FormControl(),
		valorDesejadoMensal: new FormControl(),
		valorDesejadoParcela: new FormControl(),
		valorDesejado: new FormControl(),
		situacao: new FormControl(),
	});

	numberMask = [/[0-9]/, /[0-9]/, /[0-9]/];

	formasDeCalculo = [
		{ codigo: "VE", nome: "Valor" },
		{ codigo: "PD", nome: "Percentual" },
	];

	tiposOperacao = [
		{ codigo: "AC", nome: "Acréscimo" },
		{ codigo: "RE", nome: "Redução" },
	];
	codigoPacoteReferencia: number;

	constructor(
		private openModal: NgbActiveModal,
		public admRest: AdmRestService,
		private notificationService: SnotifyService,
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService
	) {}

	ngOnInit() {
		this.loadPlanoDuracaoData();
		this.formGroup.valueChanges.subscribe((value) => {
			this.updateDiasExtras(value.numeroMeses, value.diasExtras);
		});

		this.formGroup.get("pacote").valueChanges.subscribe((value) => {
			if (value && value.precoComposicao !== undefined) {
				this.codigoPacoteReferencia = value.codigo;
				this.pacotes.find(
					(pacote) => pacote.codigo === value.codigoPacoteReferencia
				);
				this.formGroup
					.get("valorReferencia")
					.setValue(value.precoComposicao, { emitEvent: false });
			}
		});

		this.formGroup.get("numeroParcelas").valueChanges.subscribe((value) => {
			this.obterCondicoesPagamentoPorNumeroParcelas(value);
		});
	}

	private loadPlanoDuracaoData() {
		if (this.planoDuracao && this.planoDuracao.numeroMeses !== null) {
			this.formGroup.patchValue({
				pacote: this.pacote ? this.pacote : "",
				valorReferencia: this.pacote
					? this.pacote.precoComposicao
					: this.planoDuracao.valorEspecifico,
				numeroParcelas: this.planoDuracao.nrMaximoParcelasCondPagamento,
				numeroMeses: this.planoDuracao.numeroMeses,
				diasExtras: this.planoDuracao.quantidadeDiasExtra,
				tipoOperacao: this.planoDuracao.tipoOperacao,
				formaCalculo: this.planoDuracao.tipoValor,
				valorDesejadoMensal: this.planoDuracao.valorDesejadoMensal,
				valorDesejadoParcela: this.planoDuracao.valorDesejadoParcela,
				valorDesejado: this.planoDuracao.valorDesejado,
				situacao: !this.planoDuracao.situacao,
			});
			this.dias = this.planoDuracao.totalDias;
			this.codigoPacoteReferencia = this.planoDuracao.codigoPacoteReferencia;
			this.carregaPacoteDuracao();
			this.condicoesPagamento = this.planoDuracao.condicoesPagamento.map(
				(condPagDur) => condPagDur.condicaoPagamento
			);
		}
	}

	private carregaPacoteDuracao() {
		if (this.planoDuracao.codigoPacoteReferencia) {
			const pacote = this.pacotes.find(
				(pacote) => pacote.codigo === this.planoDuracao.codigoPacoteReferencia
			);
			this.formGroup.get("pacote").setValue(pacote);
			this.formGroup.get("valorReferencia").setValue(pacote.precoComposicao);
		}
	}

	salvar() {
		if (this.validarDadosForm()) return;
		this.planoDuracao.codigoPacoteReferencia = this.formGroup.get("pacote")
			.value
			? this.formGroup.get("pacote").value.codigo
			: null;
		this.planoDuracao.nrMaximoParcelasCondPagamento =
			this.formGroup.get("numeroParcelas").value;
		this.planoDuracao.situacao = this.formGroup.get("situacao").value;
		this.planoDuracao.tipoOperacao = this.formGroup.get("tipoOperacao").value;
		this.planoDuracao.totalDias = this.dias;
		this.planoDuracao.tipoValor = this.formGroup.get("formaCalculo").value;
		this.planoDuracao.quantidadeDiasExtra =
			this.formGroup.get("diasExtras").value;
		this.planoDuracao.numeroMeses = this.formGroup.get("numeroMeses").value;
		this.planoDuracao.situacao = !this.formGroup.get("situacao").value;
		this.planoDuracao.valorDesejadoMensal = this.formGroup.get(
			"valorDesejadoMensal"
		).value;
		this.planoDuracao.valorDesejadoParcela = this.formGroup.get(
			"valorDesejadoParcela"
		).value;
		this.planoDuracao.valorDesejado = this.formGroup.get("valorDesejado").value;

		if (
			this.planoDuracao.valorDesejado !== 0 &&
			this.planoDuracao.tipoValor !== null
		) {
			if (this.planoDuracao.tipoValor === "VE") {
				this.planoDuracao.valorEspecifico = this.planoDuracao.valorDesejado;
			} else if (this.planoDuracao.tipoValor === "PD") {
				this.planoDuracao.percentualdesconto = this.planoDuracao.valorDesejado;
			}
		}

		this.addCondicaoPagamentoDefault();
		this.openModal.close({
			planoDuracao: this.planoDuracao,
		});
	}

	onChangePacote($event) {
		this.formGroup.get("valorReferencia").setValue($event.valorReferencia);
	}

	updateDiasExtras(numeroMeses, diasExtras): void {
		if (numeroMeses === null || numeroMeses === undefined) {
			numeroMeses = 0;
		}
		if (diasExtras === null || diasExtras === undefined) {
			diasExtras = 0;
		}
		this.dias = parseInt(numeroMeses) * 30 + parseInt(diasExtras);
	}

	calcularValorAjusteMensal() {
		const valorModalidade = this.formGroup.get("valorReferencia").value;
		const valorDesejadoMensal = this.formGroup.get("valorDesejadoMensal").value;

		if (valorModalidade <= 0.0 && valorDesejadoMensal > 0.0) {
			this.notificationService.error(
				"O 'valor mensal de referência' deve ser informado."
			);
			return;
		}

		this.validarDadosForm();

		const numeroMeses = this.formGroup.get("numeroMeses").value;
		const valorMensal = valorDesejadoMensal * numeroMeses;
		const valorModalidadeDuracao = numeroMeses * valorModalidade;

		if (valorDesejadoMensal != 0) {
			if (valorDesejadoMensal >= valorModalidade) {
				this.acrescimo(valorMensal, valorModalidadeDuracao);
			} else if (valorDesejadoMensal < valorModalidade) {
				this.reducao(valorMensal, valorModalidadeDuracao);
			}
		}
		this.formGroup
			.get("valorDesejadoParcela")
			.setValue(
				this.arredondar2casas(
					(valorDesejadoMensal * numeroMeses) /
						this.formGroup.get("numeroParcelas").value
				)
			);
		this.calculoTipoValor(this.planoDuracao);
	}

	acrescimo(valorMensal: number, valorModalidade: number) {
		let porcentagem: number = 0.0;
		this.formGroup.get("tipoOperacao").setValue("AC"); // setar acrescimo
		const tipoOperacao = this.formGroup.get("tipoOperacao").value;

		if (tipoOperacao === "PD") {
			porcentagem = valorMensal - valorModalidade;
			this.planoDuracao.tipoValor = "PD";
			this.planoDuracao.percentualdesconto =
				(porcentagem * 100) / valorModalidade;
		} else {
			this.planoDuracao.tipoValor = "VE";
			this.planoDuracao.valorEspecifico =
				(valorMensal - valorModalidade) /
				this.formGroup.get("numeroMeses").value;
		}
	}

	reducao(valorMensal: number, valorModalidade: number) {
		let porcentagem: number = 0.0;
		this.formGroup.get("tipoOperacao").setValue("RE"); // setar redução
		const tipoOperacao = this.formGroup.get("tipoOperacao").value;

		if (tipoOperacao === "PD") {
			porcentagem = valorModalidade - valorMensal;
			this.planoDuracao.tipoValor = "PD";
			this.planoDuracao.percentualdesconto =
				(porcentagem * 100) / valorModalidade;
		} else {
			this.planoDuracao.tipoValor = "VE";
			this.planoDuracao.valorEspecifico =
				(valorModalidade - valorMensal) /
				this.formGroup.get("numeroMeses").value;
		}
	}

	private calculoTipoValor(planoDuracao) {
		if (planoDuracao.tipoValor === "VE") {
			this.formGroup
				.get("valorDesejado")
				.setValue(this.arredondar2casas(planoDuracao.valorEspecifico));
		} else {
			this.formGroup
				.get("valorDesejado")
				.setValue(planoDuracao.percentualdesconto);
		}
	}

	calcularValorParcela() {
		const valorModalidade = this.formGroup.get("valorReferencia").value;
		const valorDesejadoParcela = this.formGroup.get(
			"valorDesejadoParcela"
		).value;

		if (valorModalidade <= 0.0 && valorDesejadoParcela > 0.0) {
			this.notificationService.error(
				"O 'Valor Desejado de Parcela' deve ser informado."
			);
			return;
		}
		this.validarDadosForm();

		const numeroMeses = this.formGroup.get("numeroMeses").value;
		const numeroParcelas = this.formGroup.get("numeroParcelas").value;

		const valorMensal = valorDesejadoParcela * numeroParcelas;
		const valorModalidadeDuracao = numeroMeses * valorModalidade;

		if (valorDesejadoParcela != 0) {
			if (valorDesejadoParcela >= valorModalidade) {
				this.acrescimo(valorMensal, valorModalidadeDuracao);
			} else if (valorDesejadoParcela < valorModalidade) {
				this.reducao(valorMensal, valorModalidadeDuracao);
			}
		}
		this.formGroup
			.get("valorDesejadoMensal")
			.setValue(
				this.arredondar2casas(
					(valorDesejadoParcela * numeroParcelas) / numeroMeses
				)
			);

		this.calculoTipoValor(this.planoDuracao);
	}

	calcularDuracao() {
		this.validarDadosForm();
		const valorDesejado = this.formGroup.get("valorDesejado").value;
		const tipoOperacao = this.formGroup.get("tipoOperacao").value;
		const valorModalidade = this.formGroup.get("valorReferencia").value;
		const numeroMeses = this.formGroup.get("numeroMeses").value;
		const numeroParcelas = this.formGroup.get("numeroParcelas").value;

		if (!valorDesejado) {
			this.notificationService.error(
				"O 'Valor Desejado de Parcela' deve ser informado."
			);
			return;
		}

		if (!tipoOperacao) {
			this.notificationService.error("O 'Tipo Operação' deve ser informado.");
			return;
		}

		const tipoValor = this.formGroup.get("formaCalculo").value;
		if (tipoOperacao === "AC") {
			if (tipoValor === "PD") {
				this.planoDuracao.percentualdesconto = valorDesejado;
				const valorDesejadoMensal =
					valorModalidade + valorModalidade * (valorDesejado / 100);
				this.formGroup.get("valorDesejadoMensal").setValue(valorDesejadoMensal);
				this.formGroup
					.get("valorDesejadoParcela")
					.setValue((numeroMeses * valorDesejadoMensal) / numeroParcelas);
			} else {
				this.planoDuracao.valorEspecifico = valorDesejado;
				const valorDesejadoMensal = valorModalidade + valorDesejado;
				this.formGroup.get("valorDesejadoMensal").setValue(valorDesejadoMensal);
				this.formGroup
					.get("valorDesejadoParcela")
					.setValue((numeroMeses * valorDesejadoMensal) / numeroParcelas);
			}
		} else {
			if (tipoValor === "PD") {
				this.planoDuracao.percentualdesconto = valorDesejado;
				const valorDesejadoMensal =
					valorModalidade - valorModalidade * (valorDesejado / 100);
				this.formGroup.get("valorDesejadoMensal").setValue(valorDesejadoMensal);
				this.formGroup
					.get("valorDesejadoParcela")
					.setValue((numeroMeses * valorDesejadoMensal) / numeroParcelas);
			} else {
				this.planoDuracao.valorEspecifico = valorDesejado;
				const valorDesejadoMensal = valorModalidade - valorDesejado;
				this.formGroup.get("valorDesejadoMensal").setValue(valorDesejadoMensal);
				this.formGroup
					.get("valorDesejadoParcela")
					.setValue((numeroMeses * valorDesejadoMensal) / numeroParcelas);
			}
		}
	}

	private validarDadosForm() {
		const {
			tipoOperacao,
			numeroParcelas,
			numeroMeses,
			formaCalculo,
			valorDesejado,
		} = this.formGroup.value;

		let isFormValid = false;
		const validacoes = [
			{
				invalido: !numeroParcelas || numeroParcelas === 0,
				mensagem: "O campo 'Número de parcelas' deve ser informado.",
			},
			{
				invalido: !numeroMeses || numeroMeses === 0,
				mensagem: "O campo 'Número de Meses' deve ser informado.",
			},
			{
				invalido: !formaCalculo || formaCalculo === "",
				mensagem: "O 'Tipo valor' deve ser informado.",
			},
			{
				invalido: !tipoOperacao && valorDesejado != 0,
				mensagem: "O 'Tipo Operação' deve ser informado.",
			},
		];

		validacoes.forEach(({ invalido, mensagem }) => {
			if (invalido) {
				this.notificationService.error(mensagem);
				isFormValid = invalido;
			}
		});
		return isFormValid;
	}

	setValorAjuste(planoDuracao) {
		return planoDuracao.tipoOperacao === "PD"
			? this.planoDuracao.percentualdesconto
			: this.planoDuracao.valorEspecifico;
	}

	private addCondicaoPagamentoDefault() {
		if (!this.planoDuracao.condicoesPagamento) {
			this.planoDuracao.condicoesPagamento = new Array<any>();
		}
		if (this.planoDuracao.condicoesPagamento.length === 0) {
			if (!this.planoDuracao.codigo) {
				this.condicoesPagamento.forEach((condicaoPagamento) => {
					const exists = this.planoDuracao.condicoesPagamento.find(
						(condPagDur) =>
							condPagDur.condicaoPagamento.codigo === condicaoPagamento.codigo
					);
					if (!exists) {
						this.planoDuracao.condicoesPagamento.push({
							condicaoPagamento,
						});
					}
				});
			}

			this.planoDuracao.condicoesPagamento.forEach((condicaoPagamento) => {
				if (typeof condicaoPagamento.tipoValor === "string") {
					condicaoPagamento.tipoValor = this.formasDeCalculo.find(
						(v) => v.codigo === condicaoPagamento.tipoValor
					);
				}
				if (typeof condicaoPagamento.tipoOperacao === "string") {
					condicaoPagamento.tipoOperacao = this.tiposOperacao.find(
						(v) => v.codigo === condicaoPagamento.tipoOperacao
					);
				}
			});
			this.planoDuracao.condicoesPagamento.sort((a, b) => {
				if (a.condicaoPagamento.nrParcelas < b.condicaoPagamento.nrParcelas) {
					return -1;
				} else if (
					a.condicaoPagamento.nrParcelas > b.condicaoPagamento.nrParcelas
				) {
					return 1;
				}
				return 0;
			});
		}
	}

	obterCondicoesPagamentoPorNumeroParcelas(nrPacerlas) {
		this.condicaoPagamentoService
			.findByLessThenNrParcelas(nrPacerlas)
			.subscribe((response: any) => {
				this.condicoesPagamento = response.content;
				this.condicoesPagamento.sort((a: any, b: any) => {
					if (a.nrParcelas < b.nrParcelas) {
						return -1;
					} else if (a.nrParcelas > b.nrParcelas) {
						return 1;
					}
					return 0;
				});
			});
	}

	arredondar2casas(valor: number): number {
		const valorStr = valor.toFixed(2);
		return parseFloat(valorStr);
	}

	formatarValor(value: number) {
		return Number(value).toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		});
	}
}
