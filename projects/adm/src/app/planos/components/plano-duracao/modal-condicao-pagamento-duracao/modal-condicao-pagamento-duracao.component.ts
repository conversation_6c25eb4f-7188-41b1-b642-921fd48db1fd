import { DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import {
	PlanoApiCondicaoPagamentoService,
	PlanoCondicaoPagamento,
	PlanoDuracao,
} from "plano-api";
import { AdmRestService } from "../../../../adm-rest.service";
import {
	TraducoesXinglingComponent,
	CatTableEditableComponent,
	PactoDataGridConfig,
} from "ui-kit";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { Plano } from "../../../plano.model";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "adm-modal-condicao-pagamento-duracao",
	templateUrl: "./modal-condicao-pagamento-duracao.component.html",
	styleUrls: ["./modal-condicao-pagamento-duracao.component.scss"],
})
export class ModalCondicaoPagamentoDuracaoComponent
	implements OnInit, AfterViewInit
{
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCondicaoPagamento", { static: true })
	columnCondicaoPagamento: TemplateRef<any>;
	@ViewChild("columnTipoOperacao", { static: true })
	columnTipoOperacao: TemplateRef<any>;
	@ViewChild("columnTipoValor", { static: true })
	columnTipoValor: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("tableCondicaoPagamento", { static: false })
	tableCondicaoPagamento: CatTableEditableComponent;

	table: PactoDataGridConfig;

	tiposOperacoes: Array<{ codigo: string; label: string }> = new Array<{
		codigo: string;
		label: string;
	}>();
	tiposValor: Array<{ codigo: string; label: string }> = new Array<{
		codigo: string;
		label: string;
	}>();
	condicoesPagamentos: Array<any> = new Array<any>();
	nrMaximoParcelasCondPagamento: number;
	duracao: any;
	plano: Plano;

	constructor(
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		private admRestService: AdmRestService,
		private state: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe,
		private openModal: NgbActiveModal
	) {}

	ngOnInit() {
		this.plano = this.state.updatePlanoObj();
		this.nrMaximoParcelasCondPagamento =
			typeof this.duracao.nrMaximoParcelasCondPagamento === "object"
				? this.duracao.nrMaximoParcelasCondPagamento
				: this.duracao.nrMaximoParcelasCondPagamento;
		this.initTiposOperacoes();
		this.initTiposValor();
		this.initTable();
	}

	ngAfterViewInit() {
		this.condicaoPagamentoService
			.findByLessThenNrParcelas(this.nrMaximoParcelasCondPagamento)
			.subscribe((response: any) => {
				this.plano = this.state.updatePlanoObj();
				this.condicoesPagamentos = response.content;
				this.condicoesPagamentos = this.condicoesPagamentos.sort(
					(a: any, b: any) => {
						if (a.nrParcelas < b.nrParcelas) {
							return -1;
						} else if (a.nrParcelas > b.nrParcelas) {
							return 1;
						}
						return 0;
					}
				);
				this.updateCondicaoPagamento();
				this.updateDuracaoPlano();
				this.state.plano = this.plano;
			});
	}

	updateCondicaoPagamento() {
		if (this.duracao) {
			if (!this.duracao.condicoesPagamento) {
				this.duracao.condicoesPagamento = new Array<any>();
			}
			if (this.duracao.condicoesPagamento.length === 0) {
				if (!this.duracao.codigo) {
					this.condicoesPagamentos.forEach((condicaoPagamento) => {
						const exists = this.duracao.condicoesPagamento.find(
							(condPagDur) =>
								condPagDur.condicaoPagamento.codigo === condicaoPagamento.codigo
						);
						if (!exists) {
							this.duracao.condicoesPagamento.push({
								condicaoPagamento,
							});
						}
					});
				}
			}
			this.duracao.condicoesPagamento.forEach((condicaoPagamento) => {
				if (typeof condicaoPagamento.tipoValor === "string") {
					condicaoPagamento.tipoValor = this.tiposValor.find(
						(v) => v.codigo === condicaoPagamento.tipoValor
					);
				}
				if (typeof condicaoPagamento.tipoOperacao === "string") {
					condicaoPagamento.tipoOperacao = this.tiposOperacoes.find(
						(v) => v.codigo === condicaoPagamento.tipoOperacao
					);
				}
			});
			this.duracao.condicoesPagamento.sort((a, b) => {
				if (a.condicaoPagamento.nrParcelas < b.condicaoPagamento.nrParcelas) {
					return -1;
				} else if (
					a.condicaoPagamento.nrParcelas > b.condicaoPagamento.nrParcelas
				) {
					return 1;
				}
				return 0;
			});
			this.table.dataAdapterFn = (serverData) => {
				serverData.content = this.duracao.condicoesPagamento;
				return serverData;
			};
			this.tableCondicaoPagamento.reloadData();
		}
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			formGroup: new FormGroup({
				condicaoPagamento: new FormControl(),
				tipoOperacao: new FormControl(),
				tipoValor: new FormControl(),
				valor: new FormControl(),
			}),
			beforeConfirm: (row, form, data, indexRow) =>
				this.planoCommonsService.beforeConfirmCondicaoPagamento(
					row,
					form,
					data,
					indexRow,
					this.notificationService
				),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					editable: false,
					ordenavel: false,
				},
				{
					nome: "condicaoPagamento",
					titulo: this.columnCondicaoPagamento,
					inputType: "select",
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					endpointUrl:
						this.admRestService.buildFullUrlPlano("condicoesPagamento") +
						"/find",
					showAddSelectBtn: false,
					showSelectFilter: false,
					editable: true,
					visible: true,
					ordenavel: false,
					width: "20%",
				},
				{
					nome: "tipoOperacao",
					titulo: this.columnTipoOperacao,
					visible: true,
					inputType: "select",
					inputSelectData: this.tiposOperacoes,
					showAddSelectBtn: false,
					showSelectFilter: false,
					editable: true,
					ordenavel: false,
					addEmptyOption: true,
					width: "20%",
				},
				{
					nome: "tipoValor",
					titulo: this.columnTipoValor,
					visible: true,
					editable: true,
					inputType: "select",
					ordenavel: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
					inputSelectData: this.tiposValor,
					addEmptyOption: true,
					width: "20%",
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					inputType: "decimal",
					visible: true,
					editable: true,
					ordenavel: false,
					decimal: true,
					width: "20%",
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
			],
		});
		this.cd.detectChanges();
	}

	private initTiposOperacoes() {
		setTimeout(() => {
			this.tiposOperacoes.push(
				{
					label: this.traducao.getLabel("AC"),
					codigo: "AC",
				},
				{
					label: this.traducao.getLabel("RE"),
					codigo: "RE",
				}
			);
		});
	}

	private initTiposValor() {
		setTimeout(() => {
			this.tiposValor.push(
				{
					label: this.traducao.getLabel("VE"),
					codigo: "VE",
				},
				{
					label: this.traducao.getLabel("PD"),
					codigo: "PD",
				}
			);
		});
	}

	confirm(obj) {
		this.plano = this.state.plano;
		this.updateDuracaoPlano();
		this.state.plano = this.plano;
	}

	delete(event) {
		if (this.duracao.condicoesPagamento) {
			let index;
			if (event.row.codigo) {
				const excecao = this.duracao.condicoesPagamento.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.duracao.condicoesPagamento.splice(index, 1);
				}
			} else {
				index = event.index;
				this.duracao.condicoesPagamento.splice(index, 1);
			}
		}
		this.plano = this.state.updatePlanoObj();
		if (this.plano.duracoes) {
			let index;
			this.plano.duracoes.find((dur, i) => {
				if (
					dur.numeroMeses === this.duracao.numeroMeses &&
					dur.nrMaximoParcelasCondPagamento ===
						this.nrMaximoParcelasCondPagamento
				) {
					index = i;
					return dur;
				}
			});
			if (index >= 0) {
				this.plano.duracoes[index] = this.duracao;
				this.state.updateState(this.plano);
			}
		}
	}

	updateDuracaoPlano() {
		this.plano.duracoes.forEach((duracao) => {
			if (
				duracao.numeroMeses === this.duracao.numeroMeses &&
				duracao.nrMaximoParcelasCondPagamento ===
					this.nrMaximoParcelasCondPagamento
			) {
				duracao.condicoesPagamento = this.duracao.condicoesPagamento;
			}
		});
		this.state.plano = this.plano;
	}

	close() {
		this.openModal.close({
			plano: this.plano,
			duracao: this.duracao,
		});
	}
}
