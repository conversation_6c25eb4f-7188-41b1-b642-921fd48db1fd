<div class="btn-row">
	<h5><small>Duração do plano</small></h5>
	<pacto-cat-button
		id="btn-save-config"
		i18n-label="@@adm:btn-salvar-config"
		label="Adicionar condição de Duração"
		[type]="buttonType.OUTLINE"
		(click)="openModalDuracaoCriarDuracao()"></pacto-cat-button>
</div>
<pacto-cat-table-editable
	id="table-modalidade"
	#table
	[isEditable]="false"
	[showEdit]="false"
	[showDelete]="false"
	[table]="tableDuracaoPlano"></pacto-cat-table-editable>
<ng-template #celulaCondicaoPagamento let-row="item">
	<pacto-cat-button
		type="NO_BORDER"
		[icon]="
			row.condicoesPagamento && row.condicoesPagamento.length > 0
				? 'pct pct-edit cor-azulim05'
				: 'pct pct-plus-square cor-azulim05'
		"
		[label]="
			row.condicoesPagamento && row.condicoesPagamento.length > 0
				? 'Editar'
				: 'Adicionar'
		"
		(click)="editCondicaoPagamento(row)"></pacto-cat-button>
</ng-template>
<ng-template #celulaSituacao let-row="item">
	<div class="pill-status {{ row.situacao ? 'ativo' : 'inativo' }}">
		{{ row.situacao === true ? "Ativo" : "Inativo" }}
	</div>
</ng-template>
<ng-template #celulaAcoes let-row="item" let-index="index">
	<pacto-cat-button
		type="NO_BORDER"
		label=""
		[icon]="'pct pct-edit cor-azulim05'"
		(click)="openDuracaoModal(row)"></pacto-cat-button>
	<pacto-cat-button
		type="NO_BORDER"
		label=""
		[icon]="'pct pct-trash-2 cor-hellboy05'"
		(click)="deletarDuracao(row, index)"></pacto-cat-button>
</ng-template>
