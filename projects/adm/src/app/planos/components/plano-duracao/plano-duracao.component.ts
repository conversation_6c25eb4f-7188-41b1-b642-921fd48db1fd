import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SnotifyService } from "ng-snotify";
import {
	BUTTON_TYPE,
	CatTableEditableComponent,
	DataFiltro,
	DialogService,
	PactoDataGridConfig,
	PactoModalRef,
	PactoModalSize,
	TableData,
} from "ui-kit";
import { ModalPlanoDuracaoComponent } from "./modal-plano-duracao/modal-plano-duracao.component";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { PlanoApiCondicaoPagamentoService, PlanoDuracao } from "plano-api";
import { FormControl, FormGroup } from "@angular/forms";
import { Plano } from "../../plano.model";
import { ModalCondicaoPagamentoDuracaoComponent } from "./modal-condicao-pagamento-duracao/modal-condicao-pagamento-duracao.component";
import { DecimalPipe } from "@angular/common";

@Component({
	selector: "adm-plano-duracao",
	templateUrl: "./plano-duracao.component.html",
	styleUrls: ["./plano-duracao.component.scss"],
})
export class PlanoDuracaoComponent implements OnInit, AfterViewInit {
	@ViewChild("table", { static: false }) table: CatTableEditableComponent;
	@ViewChild("celulaCondicaoPagamento", { static: true })
	celulaCondicaoPagamento: TemplateRef<any>;
	@ViewChild("celulaSituacao", { static: true })
	celulaSituacao: TemplateRef<any>;
	@ViewChild("celulaValorTotal", { static: true })
	celulaValorTotal: TemplateRef<any>;
	@ViewChild("celulaAcoes", { static: true }) celulaAcoes: TemplateRef<any>;

	@Input() formDadosBasicos: FormGroup;
	@Input() planoDTO: Plano;
	@Output() afterAdd: EventEmitter<any> = new EventEmitter<any>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	tableDuracaoPlano: PactoDataGridConfig;
	duracoes: PlanoDuracao[] = [];
	buttonType = BUTTON_TYPE;

	constructor(
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		private state: PlanoStateService,
		private notificationService: SnotifyService,
		private modalService: DialogService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef
	) {}

	formGroup: FormGroup = new FormGroup({
		numeroMeses: new FormControl(),
		totalDias: new FormControl(),
		nrParcelas: new FormControl(0),
		operacao: new FormControl(),
		custo: new FormControl(),
		valorTotal: new FormControl(),
		inativar: new FormControl(),
	});

	ngOnInit() {
		this.initTableDuracao();
	}

	ngAfterViewInit(): void {
		if (this.planoDTO.duracoes) {
			this.duracoes = this.planoDTO.duracoes;
			this.tableDuracaoPlano.dataAdapterFn = (serverData) => {
				serverData.content = this.planoDTO.duracoes;
				return serverData;
			};
		}
	}

	initTableDuracao() {
		this.formGroup.valueChanges.subscribe((values) => {
			this.planoDTO.duracoes = [...this.duracoes];
			this.state.plano = this.planoDTO;
		});

		this.tableDuracaoPlano = new PactoDataGridConfig({
			ghostLoad: true,
			ghostAmount: 5,
			endpointParamsType: "query",
			formGroup: this.formGroup,
			dataFn: (filter: DataFiltro): TableData<any> => {
				return {
					content: this.duracoes,
					size: this.duracoes.length,
					totalElements: this.duracoes.length,
					number: 0,
				};
			},
			pagination: true,
			columns: [
				{
					nome: "numeroMeses",
					titulo: "N° meses",
					visible: true,
					ordenavel: true,
					editable: false,
				},
				{
					nome: "totalDias",
					titulo: "Total de Dias",
					visible: true,
					ordenavel: true,
					editable: true,
					valueTransform: (v, row) => {
						return row.totalDias;
					},
				},
				{
					nome: "nrMaximoParcelasCondPagamento",
					titulo: "N° de Parcelas",
					visible: true,
					ordenavel: true,
					editable: false,
					inputType: "text",
				},
				{
					nome: "tipoOperacao",
					titulo: "Operação",
					visible: true,
					ordenavel: true,
					editable: false,
					valueTransform: (v, row) => {
						return row.tipoOperacao === "AC" ? "Acréscimo" : "Redução";
					},
				},
				{
					nome: "valorDesejadoMensal",
					titulo: "Custo",
					visible: true,
					ordenavel: true,
					editable: false,
					valueTransform: (v, row) => {
						const formattedValue = Number(v).toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							maximumFractionDigits: 2,
						});
						if (row.formaCalculo === "PD" || row.tipoValor === "PD") {
							return `${formattedValue} %`;
						}
						return `R$ ${formattedValue}`;
					},
				},
				{
					nome: "valorTotal",
					titulo: "Valor Total",
					visible: true,
					ordenavel: true,
					editable: false,
					valueTransform: (v, row) => this.obterValorTotal(row),
				},
				{
					nome: "situacao",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					editable: false,
					celula: this.celulaSituacao,
				},
				{
					nome: "editar_condicao",
					titulo: "Condição de Pagamento",
					visible: true,
					ordenavel: false,
					editable: false,
					celula: this.celulaCondicaoPagamento,
				},
				{
					nome: "acoes",
					titulo: "Ações",
					visible: true,
					ordenavel: false,
					celula: this.celulaAcoes,
				},
			],
		});
	}

	openModalDuracaoCriarDuracao() {
		this.planoDTO = this.state.plano;
		const modal: PactoModalRef = this.modalService.open(
			"Adicionar condição duração",
			ModalPlanoDuracaoComponent,
			PactoModalSize.LARGE
		);
		if (this.state.plano.pacotes) {
			modal.componentInstance.pacotes = this.state.plano.pacotes.map(
				(p) => p.pacote
			);
		}
		modal.result.then((modalResult) => {
			const planoDuracao = modalResult.planoDuracao;
			this.duracoes.push(planoDuracao);

			this.planoDTO.duracoes = [...this.duracoes];
			this.table.rawData = this.planoDTO.duracoes;
			this.table.reloadData();
			this.state.updateState(this.planoDTO);

			this.cd.detectChanges();
			this.afterAdd.emit({ planoDTO: this.planoDTO });
		});
	}

	openDuracaoModal(row) {
		this.planoDTO = this.state.plano;
		const modal: PactoModalRef = this.modalService.open(
			"Editar condição duração",
			ModalPlanoDuracaoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.pacotes = this.planoDTO.pacotes.map(
			(p) => p.pacote
		);
		modal.componentInstance.planoDuracao = row;
		modal.componentInstance.pacote = row.pacote;

		modal.result.then((modalResult) => {
			const duracaoIndex = this.duracoes.findIndex(
				(duracao) =>
					duracao.codigo === row.codigo &&
					duracao.numeroMeses === row.numeroMeses &&
					duracao.nrMaximoParcelasCondPagamento ===
						row.nrMaximoParcelasCondPagamento
			);
			this.duracoes[duracaoIndex] = modalResult.planoDuracao;
			this.planoDTO.duracoes = [...this.duracoes];
			this.table.rawData = this.planoDTO.duracoes;

			this.table.reloadData();
			this.state.updateState(this.planoDTO);

			this.cd.detectChanges();
			this.afterAdd.emit({ planoDTO: this.planoDTO });
		});
	}

	editCondicaoPagamento(row) {
		const modal: PactoModalRef = this.modalService.open(
			"Editar condições pagamento",
			ModalCondicaoPagamentoDuracaoComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.plano = this.planoDTO;
		modal.componentInstance.duracao = row;

		modal.result.then((modalResult) => {
			this.planoDTO = modalResult.plano;

			this.state.plano = this.planoDTO;
			this.afterAdd.emit({ planoDTO: this.planoDTO });
			this.cd.detectChanges();
		});
		this.table.reloadData();
	}

	obterValorTotal(planoDuracao: PlanoDuracao) {
		if (!planoDuracao.valorDesejadoMensal || !planoDuracao.numeroMeses) {
			return "R$ 0.0";
		}
		return (
			"R$ " +
			this.converteDecimal(
				planoDuracao.valorDesejadoMensal * planoDuracao.numeroMeses
			)
		);
	}

	converteDecimal(value: any): string {
		let valueToString = value ? value.toString().replace(",", ".") : "";
		return this.decimalPipe.transform(parseFloat(valueToString), "1.2-2") || "";
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
	}

	deletarDuracao(row, index) {
		this.table.data.content.splice(index, 1);

		this.planoDTO.duracoes = this.table.rawData;
		this.state.plano = this.planoDTO;
		this.table.reloadData();
	}
}
