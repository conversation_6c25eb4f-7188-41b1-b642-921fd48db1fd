<pacto-cat-table-editable
	id="table-plano-horarios"
	#table
	[showAddRow]="true"
	[isEditable]="true"
	[showEdit]="true"
	[showDelete]="true"
	[newLineTitle]="'Adicionar horarios'"
	actionTitle="Ações"
	[table]="gridConfig"></pacto-cat-table-editable>
<ng-template #columnTipoOperacao let-row="item">
	<div *ngIf="row.tipoOperacao">
		{{ row.tipoOperacao === "AC" ? "Acréscimo" : "Redução" }}
	</div>
</ng-template>
<ng-template #columnTipoValor let-row="item">
	<div *ngIf="row.tipoValor">
		{{ row.tipoValor === "PD" ? "Percentual" : "Valor" }}
	</div>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:valor" xingling="VE">Valor</span>
	<span i18n="@@adm:percentual" xingling="PD">Percentual</span>
	<span i18n="@@adm:acrescimo" xingling="AC">Acréscimo</span>
	<span i18n="@@adm:reducao" xingling="RE">Redução</span>
	<span i18n="@@adm:mes" xingling="mes">mês</span>
	<span i18n="@@adm:meses" xingling="meses">meses</span>
</pacto-traducoes-xingling>
