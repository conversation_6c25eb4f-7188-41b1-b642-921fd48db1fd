import { DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { of } from "rxjs";
import { ClientDiscoveryService } from "sdk";
import {
	TraducoesXinglingComponent,
	CatTableEditableComponent,
	PactoDataGridConfig,
} from "ui-kit";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { Plano } from "../../plano.model";

@Component({
	selector: "adm-plano-horario",
	templateUrl: "./plano-horario.component.html",
	styleUrls: ["./plano-horario.component.scss"],
})
export class PlanoHorarioComponent implements OnInit, AfterViewInit {
	@ViewChild("table", { static: false }) table: CatTableEditableComponent;
	@Input() planoDTO: Plano;
	@Input() codigo: number;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("columnTipoOperacao", { static: true })
	columnTipoOperacao: TemplateRef<any>;
	@ViewChild("columnTipoValor", { static: true })
	columnTipoValor: TemplateRef<any>;
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;

	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	horariosSelecionados: any[] = [];
	gridConfig: PactoDataGridConfig;

	tiposOperacoes: Array<{ codigo: string; label: string }> = [];
	tiposValor: Array<{ codigo: string; label: string }> = [];

	horariosFormGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		horario: new FormControl(),
		tipoOperacao: new FormControl(),
		tipoValor: new FormControl(),
		percentualDesconto: new FormControl(),
		valor: new FormControl(),
	});

	constructor(
		private discoveryService: ClientDiscoveryService,
		private planoState: PlanoStateService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.initTiposValor();
		this.initTiposOperacoes();
		this.initGridConfig();
	}

	ngAfterViewInit(): void {
		this.initState();
		if (this.planoDTO.horarios) {
			const horariosMap = this.planoDTO.horarios.map((horario) => {
				if (horario.tipoValor === "VE") {
					horario.valor = horario.valorEspecifico;
				} else if (horario.tipoValor === "PD") {
					horario.valor = horario.percentualDesconto;
				} else {
					horario.valor = null;
				}
				return horario;
			});

			this.horariosSelecionados = horariosMap;
			this.gridConfig.dataAdapterFn = (serverData) => {
				serverData.content = horariosMap;
				return serverData;
			};
		}
	}

	private initTiposOperacoes() {
		setTimeout(() => {
			this.tiposOperacoes.push(
				{
					codigo: "AC",
					label: this.traducao.getLabel("AC"),
				},
				{
					codigo: "RE",
					label: this.traducao.getLabel("RE"),
				}
			);
		});
	}

	private initTiposValor() {
		setTimeout(() => {
			this.tiposValor.push(
				{
					codigo: "VE",
					label: this.traducao.getLabel("VE"),
				},
				{
					codigo: "PD",
					label: this.traducao.getLabel("PD"),
				}
			);
		});
	}

	initState() {
		this.planoDTO = this.planoState.plano;
	}

	initGridConfig() {
		const planoParam = this.planoDTO.codigo
			? `?plano=${this.planoDTO.codigo}`
			: "";

		this.gridConfig = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: `${this.discoveryService.getUrlMap().planoMsUrl}/horarios`,
			endpointParamsType: "query",
			formGroup: this.horariosFormGroup,
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			onAddFn: (row, data, index) => {
				this.adicionarHorarios(row);
				return data;
			},
			dataAdapterFn: (serverData) => {
				this.horariosSelecionados = serverData.content;
				return serverData.content;
			},
			onEditFn: (row, data) => {
				this.editarHorarios(data, row);
				return data;
			},
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			onDeleteFn: (row, data, indexRow) => this.deletar(row, data, indexRow),
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: false,
					ordenavel: false,
					editable: false,
				},
				{
					nome: "horario",
					titulo: "Horário",
					endpointUrl:
						this.discoveryService.getUrlMap().planoMsUrl +
						"/horarios/only-ativos",
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					width: "30%",
					showEmptyMessage: true,
					selectParamBuilder: (param) => {
						return {
							page: "0",
							size: "10",
							filters: JSON.stringify({
								quicksearchValue: param,
								codigosNaoConsultar: this.horariosSelecionados.map(
									(horario) => horario.codigo
								),
							}),
						};
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
				},
				{
					nome: "tipoOperacao",
					titulo: "Tipo Operação",
					visible: true,
					inputType: "select",
					inputSelectData: this.tiposOperacoes,
					showAddSelectBtn: false,
					showSelectFilter: false,
					editable: true,
					ordenavel: false,
					width: "30%",
					addEmptyOption: true,
					celula: this.columnTipoOperacao,
				},
				{
					nome: "tipoValor",
					titulo: "Tipo Valor",
					visible: true,
					editable: true,
					inputType: "select",
					ordenavel: false,
					showSelectFilter: false,
					showAddSelectBtn: false,
					width: "20%",
					inputSelectData: this.tiposValor,
					addEmptyOption: true,
					celula: this.columnTipoValor,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					decimalPrecision: 2,
					minValue: 0,
					width: "20%",
					errorMessage: "Valor inválido",
					valueTransform: (v, row) => {
						const formattedValue = Number(v).toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							maximumFractionDigits: 2,
						});
						if (row.tipoValor && row.tipoValor === "PD") {
							return `${formattedValue} %`;
						} else {
							return `R$ ${formattedValue}`;
						}
					},
				},
			],
		});
		this.cd.detectChanges();
	}
	beforeConfirm(row: any, form: FormGroup, data: any, rowIndex: any): boolean {
		if (!row.horario) {
			this.notificationService.error("Selecione um horário.");
			return false;
		}

		if (row.valor && form.get("valor").value != null) {
			if (!row.tipoOperacao || form.get("tipoOperacao").value === null) {
				this.notificationService.error("Selecione um tipo de operação");
				return false;
			} else if (!row.tipoValor || form.get("tipoValor").value === null) {
				this.notificationService.error("Selecione um tipo de valor");
				return false;
			}
		}

		return true;
	}

	private editarHorarios(data: any, row: any) {
		this.planoDTO = this.planoState.plano;

		this.horariosSelecionados = data.map((horario) =>
			this.setarValorPercentualOrValorEspecifico(horario)
		);
		this.planoDTO.horarios = this.horariosSelecionados;

		this.planoState.updateState(this.planoDTO);
		this.cd.detectChanges();
	}

	private adicionarHorarios(row: any) {
		this.planoDTO = this.planoState.plano;
		if (!this.planoDTO.horarios) {
			this.planoDTO.horarios = [];
		}
		const novoHorario = this.setarValorPercentualOrValorEspecifico(row);

		if (
			!this.horariosSelecionados.some((h) => h.codigo === novoHorario.codigo)
		) {
			const exists = this.planoDTO.horarios.some(
				(horario) => horario.codigo === novoHorario.codigo
			);
			if (!exists) {
				this.planoDTO.horarios.push(novoHorario);
			}
		}
		this.planoDTO.horarios.push(novoHorario);
		this.planoState.updateState(this.planoDTO);
	}

	private setarValorPercentualOrValorEspecifico(horario: any) {
		const tipoOperacaoCode =
			horario.tipoOperacao && typeof horario.tipoOperacao === "object"
				? horario.tipoOperacao.codigo
				: horario.tipoOperacao;

		const tipoValorCode =
			horario.tipoValor && typeof horario.tipoValor === "object"
				? horario.tipoValor.codigo
				: horario.tipoValor;

		horario.tipoOperacao = tipoOperacaoCode;
		horario.tipoValor = tipoValorCode;

		if (tipoValorCode !== null && tipoValorCode === "VE") {
			horario.valorEspecifico = horario.valor;
		}
		if (tipoValorCode !== null && tipoValorCode === "PD") {
			horario.percentualDesconto = horario.valor;
		}

		return horario;
	}

	showDelete(row: any, isAdd: any): boolean {
		if (isAdd) return false;
		return true;
	}

	private deletar(row: any, data: any, indexRow: any) {
		this.planoDTO = this.planoState.plano;
		const codigo =
			typeof row.horario === "object" ? row.horario.codigo : row.codigo;
		const index = this.planoDTO.horarios.findIndex(
			(h) => h.horario && h.horario.codigo === codigo
		);

		if (index === -1) {
			return data;
		}

		this.planoDTO.horarios.splice(index, 1);
		this.planoState.updateState(this.planoDTO);
		this.horariosSelecionados = this.planoDTO.horarios;
		data.splice(indexRow, 1);
		return data;
	}

	converteDecimal(value: any): string {
		let valueToString = value ? value.toString().replace(",", ".") : "";
		return this.decimalPipe.transform(parseFloat(valueToString), "1.2-2") || "";
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
	}
}
