<div id="table-empresas-rede">
	<div class="row row-add-empresa">
		<div class="col-md-8 col-auto">
			<pacto-cat-form-select-filter
				id="select-empresa-rede"
				[control]="toFormControl(formGroup.get('empresaRede'))"
				[paramBuilder]="selectBuilder"
				idKey="chaveId"
				labelKey="descricao"
				[addEmptyOption]="true"
				[options]="empresasRedeOptions"></pacto-cat-form-select-filter>
		</div>
		<div class="col-md-1 btn-add">
			<pacto-cat-button
				id="btn-add"
				size="LARGE"
				type="OUTLINE_GRAY"
				i18n-label="@@plano-config-avancada:label-btn-add"
				label="Adicionar"
				(click)="adicionarEmpresa()"></pacto-cat-button>
		</div>
	</div>
	<pacto-cat-table-editable
		#tableDataComponent
		[table]="tableData"
		(delete)="delete($event)"
		(pageChangeEvent)="pageChangeEvent($event)"
		(pageSizeChange)="pageSizeChange($event)"
		[actionTitle]="'Ações'"
		[itensPerPage]="itensPerPage"
		(sortEvent)="ordenar($event)"></pacto-cat-table-editable>
</div>
<ng-template #columnNomeEmpresa>
	<span i18n="@@adm:table-column-nome-empresa">Empresa</span>
</ng-template>
<ng-template #columnChave>
	<span i18n="@@adm:table-column-chave">Chave</span>
</ng-template>
<pacto-traducoes-xingling #traducao>
	<span xingling="empresa-ja-adicionada" i18n="@@adm:empresa-ja-adicionada">
		Empresa já adicionada.
	</span>
</pacto-traducoes-xingling>
