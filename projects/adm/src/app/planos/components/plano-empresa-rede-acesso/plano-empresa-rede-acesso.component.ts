import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { OamdService, Rede, SessionService } from "sdk";

import { PlanoCommonsService } from "../../services/plano-commons.service";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { PlanoEmpresaRedeAcesso } from "../../plano.model";

@Component({
	selector: "adm-plano-empresa-rede-acesso",
	templateUrl: "./plano-empresa-rede-acesso.component.html",
	styleUrls: ["./plano-empresa-rede-acesso.component.scss"],
})
export class PlanoEmpresaRedeAcessoComponent implements OnInit {
	@ViewChild("tableDataComponent", { static: true })
	tableDataComponent: RelatorioComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnNomeEmpresa", { static: true })
	columnNomeEmpresa: TemplateRef<any>;
	@ViewChild("columnChave", { static: true }) columnChave: TemplateRef<any>;

	@Input() empresasRede: Array<PlanoEmpresaRedeAcesso>;
	@Output() empresasRedeUpdate: EventEmitter<any> = new EventEmitter();

	empresasRedeOptions = new Array<{
		chave: string;
		nomeEmpresa: string;
		empresaZw: number;
		descricao: string;
		chaveId: string;
	}>();

	formGroup: FormGroup = new FormGroup({
		empresaRede: new FormControl(""),
	});
	tableData: PactoDataGridConfig;
	empresasRedeData: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();

	constructor(
		private cd: ChangeDetectorRef,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService,
		private sessionService: SessionService,
		private oamdService: OamdService,
		private planoCommonsService: PlanoCommonsService,
		private planoStateService: PlanoStateService
	) {}

	ngOnInit() {
		this.empresasRede = this.empresasRede
			? this.empresasRede
			: new Array<PlanoEmpresaRedeAcesso>();
		this.initEmpresasRede();
		this.initTableData();
		this.createPageObject();
	}

	initEmpresasRede() {
		this.oamdService
			.findEmpresasRede(this.sessionService.chave)
			.subscribe((empresas: Rede[]) => {
				empresas.forEach((e) => {
					if (this.sessionService.chave !== e.chave) {
						this.empresasRedeOptions.push({
							chave: e.chave,
							nomeEmpresa: e.nomeFantasia,
							empresaZw: e.empresaZw,
							descricao: `${e.nomeFantasia} - ${e.chave}`,
							chaveId: `${e.chave}-${e.empresaZw}`,
						});
					}
				});
				this.cd.detectChanges();
			});
	}

	initTableData() {
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.tableData = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			pagination: true,
			state: this.state,
			dataAdapterFn: (serverData) => {
				return this.empresasRedeData;
			},
			formGroup: new FormGroup({
				chaveId: new FormControl(),
				descricao: new FormControl(),
			}),
			columns: [
				{ nome: "chaveId", titulo: "Chave", visible: false },
				{
					nome: "descricao",
					titulo: this.columnNomeEmpresa,
					visible: true,
					ordenavel: true,
					width: "90%",
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("action-delete"),
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					tooltipText: "Excluir",
					actionFn: (row) => this.delete(row),
				},
			],
		});
		this.tableDataComponent.pageSizeControl.setValue(5);
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	delete(row) {
		const indexExcluir = this.empresasRede.findIndex(
			(e) => `${e.chave}-${e.codigoEmpresa}` === row.chaveId
		);
		if (indexExcluir >= 0) {
			this.empresasRede.splice(indexExcluir, 1);
			this.empresasRedeUpdate.emit(this.empresasRede);
		}
		this.empresasRedeData.content.splice(row.rowIndex, 1);
		this.createPageObject();
	}

	adicionarEmpresa() {
		const empresaRedeAdd = this.formGroup.get("empresaRede").value;
		if (empresaRedeAdd) {
			const index = this.empresasRede.findIndex(
				(p) => `${p.chave}-${p.codigoEmpresa}` === empresaRedeAdd.chaveId
			);
			if (index === -1) {
				const planoEmpresaRedeAcesso = new PlanoEmpresaRedeAcesso();
				planoEmpresaRedeAcesso.chave = empresaRedeAdd.chave;
				planoEmpresaRedeAcesso.codigoEmpresa = empresaRedeAdd.empresaZw;
				planoEmpresaRedeAcesso.nomeEmpresa = empresaRedeAdd.nomeEmpresa;
				this.empresasRede.push(planoEmpresaRedeAcesso);
				this.empresasRedeUpdate.emit(this.empresasRede);
				this.formGroup.get("empresaRede").setValue(undefined);
				this.createPageObject(this.page, this.size, true);
			} else {
				this.notify.warning(this.traducao.getLabel("empresa-ja-adicionada"));
			}
		}
	}

	ordenar(eventSort) {
		this.empresasRede = this.sortList(
			this.empresasRede,
			"nomeEmpresa",
			eventSort.direction
		);
		this.createPageObject(this.page, this.size, true);
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (a[columnName] > b[columnName]) {
					return 1;
				} else if (a[columnName] < b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (a[columnName] < b[columnName]) {
					return 1;
				} else if (a[columnName] > b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createPageObject(this.page, this.size);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createPageObject(this.page, this.size);
	}

	createPageObject(page = 1, size = 5, reloadData = true) {
		this.empresasRedeData.totalElements = this.empresasRede.length;
		this.empresasRedeData.size = size;
		this.empresasRedeData.totalPages = Math.ceil(
			+(this.empresasRedeData.totalElements / this.empresasRedeData.size)
		);
		this.empresasRedeData.first = page === 0 || page === 1;
		this.empresasRedeData.last = page === this.empresasRedeData.totalPages;
		const planoEmpresaRedeAcesso = this.empresasRede.slice(
			size * page - size,
			size * page
		);
		this.empresasRedeData.content = new Array<any>();
		planoEmpresaRedeAcesso.forEach((empresasRede) => {
			this.empresasRedeData.content.push({
				chave: empresasRede.chave,
				nomeEmpresa: empresasRede.nomeEmpresa,
				empresaZw: empresasRede.codigoEmpresa,
				descricao: `${empresasRede.nomeEmpresa} - ${empresasRede.chave}`,
				chaveId: `${empresasRede.chave}-${empresasRede.codigoEmpresa}`,
			});
		});
		this.tableDataComponent.showBtnAdd = false;
		if (reloadData) {
			this.tableDataComponent.reloadData();
		}
		this.tableDataComponent.ngbPage = this.page;
	}
}
