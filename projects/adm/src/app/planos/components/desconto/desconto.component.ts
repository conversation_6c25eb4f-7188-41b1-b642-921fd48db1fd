import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import { Router } from "@angular/router";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { getTipoProdutoLabelByKey } from "produto-api";
import {
	PlanoApiDescontoService,
	Desconto,
	getTipoDescontoLabelByKey,
} from "plano-api";

@Component({
	selector: "adm-desconto",
	templateUrl: "./desconto.component.html",
	styleUrls: ["./desconto.component.scss"],
})
export class DescontoComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducaoDesconto", { static: true })
	traducaoDesconto: TraducoesXinglingComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnTipoProduto", { static: true })
	columnTipoProduto: TemplateRef<any>;
	@ViewChild("columnTipo", { static: true }) columnTipo: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("columnAtivo", { static: true }) columnAtivo: TemplateRef<any>;
	table: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private descontoService: PlanoApiDescontoService
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.DESCONTO);
	}

	ngOnInit() {
		this.initTable();
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("/desconto", false, Api.MSPLANO),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoProduto",
						titulo: this.columnTipoProduto,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => getTipoProdutoLabelByKey(v, this.traducoes),
					},
					{
						nome: "tipoDesconto",
						titulo: this.columnTipo,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => getTipoDescontoLabelByKey(v, this.traducoes),
					},
					{
						nome: "valor",
						titulo: this.columnValor,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "ativo",
						titulo: this.columnAtivo,
						visible: true,
						ordenavel: true,
						valueTransform: (v) =>
							v
								? this.traducoes.getLabel("SIM_VALUE")
								: this.traducoes.getLabel("NAO_VALUE"),
					},
				],
				dropDownActions: [
					{
						nome: this.traducoes.getLabel("BTTN_EDIT"),
						iconClass: "",
						tooltipText: this.traducoes.getLabel("TOOLTIP_EDIT"),
						actionFn: (row) => this.editDesconto(row),
						showIconFn: (row) => this.recurso.editar,
					},
					{
						nome: this.traducoes.getLabel("BTTN_DELETE"),
						iconClass: "",
						tooltipText: this.traducoes.getLabel("TOOLTIP_DELETE"),
						actionFn: (row) => this.deleteDesconto(row),
						showIconFn: (row) => this.recurso.excluir,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	editDesconto(desconto) {
		this.router.navigate(["adm", "planos", "desconto", desconto.codigo]);
	}

	deleteDesconto(desconto: Desconto) {
		if (this.recurso.excluir) {
			this.descontoService.delete(desconto.codigo).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducoes.getLabel("DELETE_MSG")
					);
					this.tableData.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducoes.getLabel("PERMISSION_MSG")
			);
		}
	}

	novoDesconto() {
		this.router.navigate(["adm", "planos", "novo-desconto"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editDesconto(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteDesconto(event.row);
		}
	}
}
