import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AbstractControl, FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import {
	CondicaoPagamento,
	CondicaoPagamentoParcela,
	CondicaoPagamentoPlanoDuracao,
	PlanoApiCondicaoPagamentoService,
} from "plano-api";
import { PlanoService } from "../cadastrar-plano/plano.service";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

@Component({
	selector: "adm-condicao-pagamento-form",
	templateUrl: "./condicao-pagamento-form.component.html",
	styleUrls: ["./condicao-pagamento-form.component.scss"],
})
export class CondicaoPagamentoFormComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnNrParcela", { static: true })
	columnNrParcela: TemplateRef<any>;
	@ViewChild("columnNrDiasParcela", { static: true })
	columnNrDiasParcela: TemplateRef<any>;
	@ViewChild("tableCondicaoPagamentoParcelaComponent", { static: false })
	tableCondicaoPagamentoParcelaComponent: RelatorioComponent;
	@Output() isEditinOrAdding: EventEmitter<any> = new EventEmitter<any>();
	page = 1;
	size = 10;
	condicaoPagamento: CondicaoPagamento = new CondicaoPagamento();
	condicaoPagamentoPlanosDuracoes: Array<CondicaoPagamentoPlanoDuracao> =
		new Array<CondicaoPagamentoPlanoDuracao>();
	formGroup: FormGroup;
	codigoControl: FormControl = new FormControl();
	id;
	tipoConvenioCobrancaArray: Array<{ id: number; label: string }> = [
		{ id: 0, label: "Nenhum" },

		{ id: 1, label: "Boleto Bancário" },
		{ id: 39, label: "Boleto Bancário Asaas" },
		{ id: 47, label: "Boleto Bancário Banco do Brasil - Registro Online" },
		{ id: 45, label: "Boleto Bancário Caixa - Registro Online" },
		{ id: 17, label: "Boleto Bancário Daycoval" },
		{ id: 9, label: "Boleto Bancário Itaú" },
		{ id: 33, label: "Boleto Bancário Itaú - Registro Online" },
		{ id: 25, label: "Boleto Bancário Itaú CNAB400" },
		{ id: 24, label: "Boleto Bancário PJBank" },

		{ id: 11, label: "DCC Bin EDI" },
		{ id: 46, label: "DCC Caixa Online" },
		{ id: 44, label: "DCC CeoPag Online" },
		{ id: 2, label: "DCC Cielo EDI" },
		{ id: 13, label: "DCC Cielo Online" },
		{ id: 42, label: "DCC FacilitePay MS Online" },
		{ id: 18, label: "DCC Fitness Card Online" },
		{ id: 41, label: "DCC Fypay Online" },
		{ id: 8, label: "DCC Getnet EDI" },
		{ id: 20, label: "DCC Getnet Online" },
		{ id: 15, label: "DCC MaxiPago Online" },
		{ id: 26, label: "DCC Mundipagg Online" },
		{ id: 35, label: "DCC One Payment Online" },
		{ id: 27, label: "DCC Pagar.me Online" },
		{ id: 48, label: "DCC PagBank Online" },
		{ id: 32, label: "DCC PagoLivre Online" },
		{ id: 16, label: "DCC Rede Online" },
		{ id: 21, label: "DCC Stone Online" },
		{ id: 50, label: "DCC Stone Online v5" },
		{ id: 31, label: "DCC Stripe Online" },
		{ id: 34, label: "DCC ValoriBank Online" },
		{ id: 12, label: "DCC Vindi Online" },

		{ id: 3, label: "DCO Banco do Brasil" },
		{ id: 4, label: "DCO Bradesco" },
		{ id: 19, label: "DCO Bradesco 240" },
		{ id: 6, label: "DCO Caixa (SIACC 150)" },
		{ id: 23, label: "DCO Caixa (SICOV 150)" },
		{ id: 7, label: "DCO HSBC" },
		{ id: 5, label: "DCO Itaú" },
		{ id: 10, label: "DCO Santander" },
		{ id: 22, label: "DCO Santander Layout Febraban 150" },

		{ id: 37, label: "PinPad - GetCard (Scope)" },
		{ id: 36, label: "PinPad - Stone Connect 2.0 (POS)" },

		{ id: 40, label: "Pix Asaas" },
		{ id: 28, label: "Pix Banco do Brasil" },
		{ id: 29, label: "Pix Bradesco" },
		{ id: 43, label: "Pix Inter" },
		{ id: 49, label: "Pix Itaú" },
		{ id: 38, label: "Pix PjBank" },
		{ id: 30, label: "Pix Santander" },
	];

	tableCondicaoPagamentoParcela: PactoDataGridConfig;
	condicoesPagamentoParcela: {
		content: Array<CondicaoPagamentoParcela>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<CondicaoPagamentoParcela>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	recurso: PerfilAcessoRecurso;

	constructor(
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService,
		private planoService: PlanoService,
		private cd: ChangeDetectorRef,
		private session: SessionService
	) {
		this.recurso = this.session.recursos.get(
			PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO
		);
	}

	ngOnInit() {
		this.createForm();
		this.initTableCondicaoPagamentoParcela();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
		if (this.id) {
			this.condicaoPagamentoService.find(this.id).subscribe((response) => {
				this.condicaoPagamento = response.content;
				this.codigoControl.setValue(this.condicaoPagamento.codigo);
				this.formGroup.patchValue({
					descricao: this.condicaoPagamento.descricao,
					nrParcelas: this.condicaoPagamento.nrParcelas,
					intervaloEntreParcela:
						this.condicaoPagamento.intervaloEntreParcela === undefined
							? 0
							: this.condicaoPagamento.intervaloEntreParcela,
					entrada: this.condicaoPagamento.entrada,
					percentualValorEntrada: this.condicaoPagamento.percentualValorEntrada,
					tipoConvenioCobranca: this.condicaoPagamento.tipoConvenioCobranca,
					recorrencia: this.condicaoPagamento.recorrencia,
					recebimentoPrePago: this.condicaoPagamento.recebimentoPrePago,
					geradoAutomaticoPlanoRecorrente:
						this.condicaoPagamento.geradoAutomaticoPlanoRecorrente,
				});
				this.sortCondicoesPagamentoParcela();
				this.createCondicaoPagamentoParcelaPageObject();
			});
		} else {
			this.formGroup.patchValue({
				entrada: false,
				intervaloEntreParcela: 0,
				tipoConvenioCobranca: 0,
				recorrencia: false,
				recebimentoPrePago: false,
				geradoAutomaticoPlanoRecorrente: false,
			});
		}
	}

	initTableCondicaoPagamentoParcela() {
		setTimeout(() => {
			this.tableCondicaoPagamentoParcela = new PactoDataGridConfig({
				dataAdapterFn: (serverData) => {
					return this.condicoesPagamentoParcela;
				},
				columns: [
					{
						nome: "nrParcela",
						titulo: this.columnNrParcela,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nrDiasParcela",
						titulo: this.columnNrDiasParcela,
						visible: true,
						ordenavel: true,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	createCondicaoPagamentoParcelaPageObject(page = 1, size = 10) {
		this.condicoesPagamentoParcela.totalElements =
			this.condicaoPagamento.condicaoPagamentoParcela.length;
		this.condicoesPagamentoParcela.size = size;
		this.condicoesPagamentoParcela.totalPages = Math.ceil(
			+(
				this.condicoesPagamentoParcela.totalElements /
				this.condicoesPagamentoParcela.size
			)
		);
		if (this.condicoesPagamentoParcela.totalPages === 1) {
			page = 1;
			this.page = page;
		}
		this.condicoesPagamentoParcela.first = page === 0 || page === 1;
		this.condicoesPagamentoParcela.last =
			page === this.condicoesPagamentoParcela.totalPages;
		this.condicoesPagamentoParcela.content =
			this.condicaoPagamento.condicaoPagamentoParcela.slice(
				size * page - size,
				size * page
			);
		this.tableCondicaoPagamentoParcelaComponent.reloadData();
	}

	changePageCondicaoPagamentoParcela(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createCondicaoPagamentoParcelaPageObject(this.page, this.size);
	}

	changePageSizeCondicaoPagamentoParcela(size) {
		if (!isNaN(this.size)) {
			this.size = size;
		}
		this.createCondicaoPagamentoParcelaPageObject(this.page, this.size);
	}

	createForm() {
		this.formGroup = new FormGroup({
			descricao: new FormControl(),
			nrParcelas: new FormControl(),
			intervaloEntreParcela: new FormControl(),
			entrada: new FormControl(),
			percentualValorEntrada: new FormControl(),
			tipoConvenioCobranca: new FormControl(),
			recorrencia: new FormControl(),
			recebimentoPrePago: new FormControl(),
			geradoAutomaticoPlanoRecorrente: new FormControl(),
		});
	}

	voltarListagem() {
		this.router.navigate(["adm", "planos", "condicao-pagamento"]);
	}

	salvarCondicaoPagamento() {
		Object.keys(this.formGroup.getRawValue()).forEach((key) => {
			this.condicaoPagamento[key] = this.formGroup.getRawValue()[key];
		});
		if (this.condicaoPagamento.condicaoPagamentoParcela) {
			this.condicaoPagamento.condicaoPagamentoParcela =
				this.condicaoPagamento.condicaoPagamentoParcela.sort();
		}
		this.condicaoPagamentoService
			.save(this.condicaoPagamento, this.condicaoPagamentoPlanosDuracoes)
			.subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("saved-success")
					);
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
	}

	sortCondicoesPagamentoParcela() {
		this.condicaoPagamento.condicaoPagamentoParcela =
			this.condicaoPagamento.condicaoPagamentoParcela.sort((a, b) => {
				if (a.nrParcela > b.nrParcela) {
					return 1;
				} else if (a.nrParcela < b.nrParcela) {
					return -1;
				} else {
					return 0;
				}
			});
	}

	confirm(event) {
		// TODO implementar confirm
	}

	edit(event) {
		// TODO implementar confirm
	}

	delete(event) {
		// TODO implementar confirm
	}

	exibirRecebimentoPrePago(): boolean {
		const tipoConvenioSelecionado = Number(
			this.formGroup.getRawValue().tipoConvenioCobranca
		);
		let exibirRecebimentoPrePago = false;

		const tipoConvenioPermiteCobrancaRecebimentoPrePago = [
			{ id: 2, label: "DCC Cielo EDI" },
			{ id: 11, label: "DCC Bin EDI" },
			{ id: 8, label: "DCC Getnet EDI" },
			{ id: 20, label: "DCC Getnet Online" },
			{ id: 12, label: "DCC Vindi Online" },
			{ id: 13, label: "DCC Cielo Online" },
			{ id: 15, label: "DCC MaxiPago Online" },
			{ id: 16, label: "DCC Rede Online" },
			{ id: 21, label: "DCC Stone Online" },
			{ id: 27, label: "DCC Pagar.me Online" },
			{ id: 31, label: "DCC Stripe Online" },
			{ id: 32, label: "DCC PagoLivre Online" },
			{ id: 34, label: "DCC ValoriBank Online" },
			{ id: 35, label: "DCC One Payment Online" },
			{ id: 41, label: "DCC Fypay Online" },
		];

		// tslint:disable-next-line:prefer-for-of
		for (
			let i = 0;
			i < tipoConvenioPermiteCobrancaRecebimentoPrePago.length;
			i++
		) {
			if (
				tipoConvenioPermiteCobrancaRecebimentoPrePago[i].id ===
				tipoConvenioSelecionado
			) {
				exibirRecebimentoPrePago = true;
				break;
			}
		}
		if (!exibirRecebimentoPrePago) {
			this.formGroup.patchValue({
				recebimentoPrePago: false,
			});
		}
		return exibirRecebimentoPrePago;
	}

	exibirPercentualEntrada(): boolean {
		const entrada = this.formGroup.getRawValue().entrada;

		if (!entrada) {
			this.formGroup.patchValue({
				percentualValorEntrada: undefined,
			});
		}
		return entrada;
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const ctrl = absCtrl as FormControl;
		return ctrl;
	}

	onAtualizarPlanos(
		codicaoPagamentoPlanosDuracoes: Array<CondicaoPagamentoPlanoDuracao>
	) {
		this.condicaoPagamentoPlanosDuracoes = codicaoPagamentoPlanosDuracoes;
	}
}
