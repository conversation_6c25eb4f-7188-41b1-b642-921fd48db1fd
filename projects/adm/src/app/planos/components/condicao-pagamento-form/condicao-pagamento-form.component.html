<adm-layout
	(goBack)="voltarListagem()"
	i18n-modulo="@@condicao-pagamento:modulo"
	i18n-pageTitle="@@condicao-pagamento:title"
	i18n-subtitle="@@condicao-pagamento:form-subtitle"
	modulo="Administrativo"
	pageTitle="Condição de pagamento"
	subtitle="Informe os dados abaixo">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow" id="div-main-cond-pg">
			<div class="row">
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="codigoControl"
						i18n-label="@@condicao-pagamento:label-codigo"
						id="nova-condicao-pagamento-codigo"
						label="Código"
						placeholder="000"
						readonly="true"></pacto-cat-form-input-number>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-4">
					<pacto-cat-form-input
						[control]="toFormControl(formGroup.get('descricao'))"
						[maxlength]="45"
						i18n-label="@@condicao-pagamento:label-descricao"
						i18n-placeholder="@@condicao-pagamento:placeholder-descricao"
						id="nova-condicao-pagamento-descricao"
						label="Descrição*"
						placeholder="Descrição"></pacto-cat-form-input>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="toFormControl(formGroup.get('nrParcelas'))"
						[maxlength]="3"
						i18n-label="@@condicao-pagamento:label-nr-parcelas"
						id="nova-condicao-pagamento-numero-parcelas"
						label="Número de parcelas*"
						placeholder="0"></pacto-cat-form-input-number>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-4">
					<pacto-cat-form-input-number
						[formControl]="
							toFormControl(formGroup.get('intervaloEntreParcela'))
						"
						[maxlength]="3"
						i18n-label="
							@@condicao-pagamento:label-intervalo-dias-entre-parcelas"
						i18n-placeholder="
							@@condicao-pagamento:placeholder-intervalo-dias-entre-parcelas"
						id="nova-condicao-pagamento-intervalo-parcelas"
						label="Intervalo de dias entre as parcelas*"
						placeholder="0"></pacto-cat-form-input-number>
				</div>
			</div>
			<div class="row">
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-3">
					<div class="checkbox-padrao">
						<pacto-cat-checkbox
							[control]="toFormControl(formGroup.get('entrada'))"
							i18n-label="@@condicao-pagamento:label-entrada"
							id="nova-condicao-pagamento-entrada"
							label="Entrada"></pacto-cat-checkbox>
					</div>
				</div>
				<div class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<div class="checkbox-padrao" style="pointer-events: none">
						<pacto-cat-checkbox
							[control]="
								toFormControl(formGroup.get('geradoAutomaticoPlanoRecorrente'))
							"
							i18n-label="
								@@condicao-pagamento:label-gerado-automatico-plano-recorrente"
							id="nova-condicao-pagamento-gerado-automatico-plano-recorrente"
							label="Gerado automático na criação de plano recorrente"></pacto-cat-checkbox>
					</div>
				</div>
			</div>
			<div class="row">
				<div
					*ngIf="exibirPercentualEntrada()"
					class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-4">
					<pacto-cat-form-input-number
						[formControl]="
							toFormControl(formGroup.get('percentualValorEntrada'))
						"
						decimal="true"
						i18n-label="@@condicao-pagamento:label-percentual-valor-entrada"
						id="nova-condicao-pagamento-percentual-valor-entrada"
						label="Percentual de entrada"
						max="100"
						maxlength="5"
						placeholder="0,00"></pacto-cat-form-input-number>
				</div>
				<div class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-4">
					<pacto-cat-form-select
						[control]="toFormControl(formGroup.get('tipoConvenioCobranca'))"
						[items]="tipoConvenioCobrancaArray"
						i18n-label="@@condicao-pagamento:label-tipo-convenio-cobranca"
						id="nova-condicao-pagamento-tipo-convenio-cobranca"
						label="Tipo convênio cobrança"></pacto-cat-form-select>
				</div>
			</div>
			<div class="row" id="div-recorrencia">
				<div class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-2">
					<div class="checkbox-padrao">
						<pacto-cat-checkbox
							[control]="toFormControl(formGroup.get('recorrencia'))"
							i18n-label="@@condicao-pagamento:label-recorrencia"
							id="nova-condicao-pagamento-recorrencia"
							label="Recorrência"></pacto-cat-checkbox>
					</div>
				</div>
				<div
					*ngIf="exibirRecebimentoPrePago()"
					class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3"
					id="div-rec-pre-pago">
					<div class="checkbox-padrao">
						<pacto-cat-checkbox
							[control]="toFormControl(formGroup.get('recebimentoPrePago'))"
							i18n-label="@@condicao-pagamento:label-recebimento-pre-pago"
							id="nova-condicao-pagamento-recebimento-pre-pago"
							label="Recebimento pré-pago"></pacto-cat-checkbox>
					</div>
				</div>
			</div>
			<div class="row" style="margin-top: 15px; margin-bottom: 15px">
				<p i18n="@@condicao-pagamento:p_incluir-condicao">
					Incluir condição de pagamento no cadastro de plano
				</p>
			</div>

			<div>
				<adm-condicao-pagamento-plano-table
					#idCondicaoPagamentoTable
					(atualizarCondicaoPagamentoPlanos)="onAtualizarPlanos($event)"
					[condicaoPagamentoDTO]="condicaoPagamento"
					[condicaoPagamentoPlanosDuracoes]="condicaoPagamentoPlanosDuracoes"
					[formDadosBasicos]="formGroup"></adm-condicao-pagamento-plano-table>
			</div>
		</div>
		<div *ngIf="recurso.editar" class="row justify-content-end">
			<pacto-cat-button
				(click)="voltarListagem()"
				i18n-label="@@condicao-pagamento:btn-cancelar"
				id="nova-condicao-pagamento-btn-cancelar"
				label="Cancelar"
				style="margin-right: 10px"
				type="OUTLINE_DARK"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvarCondicaoPagamento()"
				i18n-label="@@condicao-pagamento:btn-salvar"
				id="nova-condicao-pagamento-btn-salvar"
				label="Salvar"
				style="margin-right: 10px"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>

	<ng-container>
		<pacto-cat-card-plain style="display: block; margin-top: 15px">
			<div class="table-wrapper">
				<div class="pct-cond-pag-title" style="margin-bottom: 15px">
					<span i18n="@@plano:span-parcela-da-condicao-de-pagamento">
						Parcelas da condição de pagamento
					</span>
				</div>
				<pacto-relatorio
					#tableCondicaoPagamentoParcelaComponent
					(pageChangeEvent)="changePageCondicaoPagamentoParcela($event)"
					(pageSizeChange)="changePageSizeCondicaoPagamentoParcela($event)"
					[showShare]="false"
					[table]="tableCondicaoPagamentoParcela"
					idSuffix="nova-condicao-pagamento-parcelas"></pacto-relatorio>
				<ng-template #columnNrParcela>
					<span i18n="@@condicao-pagamento:column-nr-parcela">
						Número da parcela
					</span>
				</ng-template>

				<ng-template #columnNrDiasParcela>
					<span i18n="@@condicao-pagamento:column-nr-dias-parcela">
						Número de dias
					</span>
				</ng-template>
			</div>
		</pacto-cat-card-plain>
	</ng-container>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@condicao-pagamento:saved-success" xingling="saved-success">
		Condicão de pagamento salva com sucesso!
	</span>
</pacto-traducoes-xingling>
