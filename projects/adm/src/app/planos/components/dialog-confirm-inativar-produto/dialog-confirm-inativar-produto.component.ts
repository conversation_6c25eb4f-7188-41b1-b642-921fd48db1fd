import { Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "adm-dialog-confirm-inativar-produto",
	templateUrl: "./dialog-confirm-inativar-produto.component.html",
	styleUrls: ["./dialog-confirm-inativar-produto.component.scss"],
})
export class DialogConfirmInativarProdutoComponent implements OnInit {
	@Input() ativoPlano = false;

	constructor(public dialog: NgbActiveModal) {}

	ngOnInit() {}

	confirm() {
		this.dialog.close();
	}

	cancel() {
		this.dialog.dismiss("dismiss");
	}
}
