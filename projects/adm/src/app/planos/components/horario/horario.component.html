<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="adm:moduleName"
	i18n-pageTitle="@@planos:horario:mainTitle"
	modulo="Administrativo"
	pageTitle="Horário">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableData
			(btnAddClick)="novoHorario()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editHorario($event)"
			[enableDs3]="true"
			[filterConfig]="filterConfig"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="tableHorario"
			actionTitulo="Ações"
			i18n-actionTitulo="@@acoes:table-column-acoes"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			id="list-horarios"
			telaId="horario"></pacto-relatorio>
	</div>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@planos:horario:editBttn" xingling="BTTN_EDIT">Editar</span>
	<span i18n="@@planos:horario:editTooltip" xingling="TOOLTIP_EDIT">
		Editar horário
	</span>
	<span i18n="@@planos:horario:deleteBttn" xingling="BTTN_DELETE">Excluir</span>
	<span i18n="@@planos:horario:deleteTooltip" xingling="TOOLTIP_DELETE">
		Excluir horário
	</span>
	<span i18n="@@planos:horario:permission" xingling="PERMISSION_MSG">
		Seu usuário não possui permissão, procure seu administrador.
	</span>
	<span i18n="@@planos:horario:deleteMsg" xingling="DELETE_MSG">
		Horário excluído com sucesso.
	</span>
	<span i18n="@@planos:horario:situacaoLabel" xingling="SITUACAO_LABEL">
		Situação
	</span>
	<span i18n="@@planos:horario:ativoLabel" xingling="ATIVO_LABEL">Ativo</span>
	<span i18n="@@planos:horario:inativoLabel" xingling="INATIVO_LABEL">
		Inativo
	</span>
	<span i18n="@@adm:action-clonar" xingling="action-clonar">Clonar</span>
	<span i18n="@@adm:tooltip-clonar-horario" xingling="tooltip-clonar-horario">
		Clonar horário
	</span>
	<span
		i18n="@@cad-aux:instrucao:table-column-acoes"
		xingling="table-column-acoes">
		Ações
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@planos:horario:tableColumnCodigo">Código</span>
</ng-template>

<ng-template #columnDescricao>
	<span i18n="@@planos:horario:tableColumnNome">Descrição</span>
</ng-template>

<ng-template #columnSituacao>
	<span i18n="@@planos:horario:columnSituacao">Situação</span>
</ng-template>

<ng-template #bodyRemoverHorario>
	<div>
		<span>
			Confirmar a remoção do horário
			<strong>
				{{ horarioRemover.codigo }} - {{ horarioRemover.descricao }}
			</strong>
			?
		</span>
	</div>
	<div>
		<strong>Esta ação não poderá ser desfeita!</strong>
	</div>
</ng-template>
