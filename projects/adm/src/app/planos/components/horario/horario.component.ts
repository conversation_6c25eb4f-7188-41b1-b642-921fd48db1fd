import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import { Router } from "@angular/router";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	PlanoApiHorarioService,
	getIdentificadorByIndex,
	getIdentificadorLabelByKey,
	Horario,
	HorarioDisponibilidadeSimple,
} from "plano-api";

@Component({
	selector: "adm-horario",
	templateUrl: "./horario.component.html",
	styleUrls: ["./horario.component.scss"],
})
export class HorarioComponent implements OnInit, AfterViewInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnSituacao", { static: true })
	columnSituacao: TemplateRef<any>;
	@ViewChild("bodyRemoverHorario", { static: true })
	bodyRemoverHorario: TemplateRef<any>;
	filterConfig: GridFilterConfig = { filters: [] };
	tableHorario: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;
	horarioRemover: Horario;

	horarios: {
		content: Array<Horario>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<Horario>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private horarioService: PlanoApiHorarioService,
		private ngbModal: NgbModal
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.HORARIO);
	}

	ngOnInit() {
		this.initFilter();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	private initTable() {
		this.tableHorario = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrl("/horarios", false, Api.MSPLANO),
			logUrl: this.admRest.buildFullUrlAdmCore("logApi/logs-horario"),
			quickSearch: true,
			showFilters: true,
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
					defaultVisible: true,
				},
				{
					nome: "ativo",
					titulo: this.columnSituacao,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => {
						return v
							? this.traducoes.getLabel("ATIVO_LABEL")
							: this.traducoes.getLabel("INATIVO_LABEL");
					},
				},
			],
			actions: [
				{
					nome: "editHorario",
					iconClass: "pct pct-edit cor-action-default-able04",
					tooltipText: this.traducoes.getLabel("TOOLTIP_EDIT"),
					actionFn: (row) => this.editHorario(row),
				},
				{
					nome: "deleteHorario",
					iconClass: "pct pct-trash-2 cor-action-default-risk04",
					tooltipText: this.traducoes.getLabel("TOOLTIP_DELETE"),
				},
				{
					nome: "clonarHorario",
					iconClass: "pct pct-copy cor-azulim05",
					tooltipText: this.traducoes.getLabel("tooltip-clonar-horario"),
					actionFn: (row) => this.cloneHorario(row),
				},
			],
		});
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "ativo",
						label: this.traducoes.getLabel("SITUACAO_LABEL"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducoes.getLabel("ATIVO_LABEL") },
							{
								value: "false",
								label: this.traducoes.getLabel("INATIVO_LABEL"),
							},
						],
						initialValue: ["true"],
					},
				],
			};
		});
	}

	editHorario(horario) {
		this.router.navigate(["adm", "planos", "horarios", horario.codigo]);
	}

	deleteHorario(horario: Horario) {
		if (this.recurso.excluir) {
			const dialogRef = this.ngbModal.open(ConfirmDialogDeleteComponent, {
				centered: true,
			});
			dialogRef.componentInstance.texto = `Confirmar exclusão do horário ${horario.codigo} - ${horario.descricao}?`;
			dialogRef.componentInstance.textoAlerta =
				"Esta ação não poderá ser desfeita";
			dialogRef.componentInstance.actionLabel = "Excluir";
			this.horarioRemover = horario;
			dialogRef.result
				.then((r) => {
					if (r) {
						this.horarioService.delete(horario.codigo).subscribe(
							(response) => {
								this.notificationService.success(
									this.traducoes.getLabel("DELETE_MSG")
								);
								this.tableData.reloadData();
							},
							(httpErrorResponse) => {
								const err = httpErrorResponse.error;
								if (err.meta && err.meta.messageValue) {
									this.notificationService.error(err.meta.messageValue);
								}
							}
						);
					}
				})
				.catch((er) => {});
		} else {
			this.notificationService.warning(
				this.traducoes.getLabel("PERMISSION_MSG")
			);
		}
	}

	novoHorario() {
		this.router.navigate(["adm", "planos", "novo-horario"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editHorario") {
			this.editHorario(event.row);
		} else if (event.iconName === "deleteHorario") {
			this.deleteHorario(event.row);
		} else if (event.iconName === "clonarHorario") {
			this.cloneHorario(event.row);
		}
	}

	cloneHorario(horario) {
		this.router.navigate(["adm", "planos", "novo-horario"], {
			queryParams: { clonar: true, code: horario.codigo },
		});
	}
}
