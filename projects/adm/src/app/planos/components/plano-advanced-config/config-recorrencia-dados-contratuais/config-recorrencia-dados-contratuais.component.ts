import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { PactoDataGridConfig, SelectFilterParamBuilder } from "ui-kit";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-config-recorrencia-dados-contratuais",
	templateUrl: "./config-recorrencia-dados-contratuais.component.html",
	styleUrls: ["./config-recorrencia-dados-contratuais.component.scss"],
})
export class ConfigRecorrenciaDadosContratuaisComponent implements OnInit {
	@Input() form: FormGroup;
	@Input() plano: Plano;
	parcelasMatriculas: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	tablePlanoRecorrenciaParcelas: PactoDataGridConfig;
	parcelas: Array<any> = new Array<any>();
	listaParcelas: Array<any> = new Array<any>();
	days: Array<{ label: string; id: number }> = new Array<{
		label: string;
		id: number;
	}>();
	meses: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	tableParcelasAnuidade: PactoDataGridConfig;
	descontoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	constructor(
		private state: PlanoStateService,
		public admRestService: AdmRestService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef
	) {
		for (let i = 1; i <= 31; i++) {
			this.days.push({
				label: i.toString(),
				id: i,
			});
		}
		for (let i = 1; i <= 12; i++) {
			this.listaParcelas.push({
				label: `${i}x`,
				value: i,
			});
		}
		this.meses.push(
			{
				label: "Janeiro",
				value: 1,
			},
			{
				label: "Fevereiro",
				value: 2,
			},
			{
				label: "Março",
				value: 3,
			},
			{
				label: "Abril",
				value: 4,
			},
			{
				label: "Maio",
				value: 5,
			},
			{
				label: "Junho",
				value: 6,
			},
			{
				label: "Julho",
				value: 7,
			},
			{
				label: "Agosto",
				value: 8,
			},
			{
				label: "Setembro",
				value: 9,
			},
			{
				label: "Outubro",
				value: 10,
			},
			{
				label: "Novembro",
				value: 11,
			},
			{
				label: "Dezembro",
				value: 12,
			}
		);
	}

	ngOnInit() {
		this.initPlanoRecorrenciaParcelas();
		this.populateParcelasMatriculas();
		if (this.plano) {
			if (!this.plano.planoRecorrencia) {
				this.plano.planoRecorrencia = {
					parcelas: [],
				};
			} else if (!this.plano.planoRecorrencia.parcelas) {
				this.plano.planoRecorrencia.parcelas = new Array<any>();
			}
			this.parcelas = this.plano.planoRecorrencia.parcelas;
			const diasVencimentoProrataObjs: Array<{ label: string; id: number }> =
				new Array<{ label: string; id: number }>();
			if (!this.plano.diasVencimentoProrata) {
				this.plano.diasVencimentoProrata = "";
				this.days.forEach((day, index) => {
					if (index === this.days.length - 1) {
						this.plano.diasVencimentoProrata += `${day.id}`;
					} else {
						this.plano.diasVencimentoProrata += `${day.id},`;
					}
				});
			}
			if (!this.plano.diasVencimentoProrata) {
				this.initDiasProrata();
			}
			if (this.plano.diasVencimentoProrata) {
				const diasVencimentoProrata =
					this.plano.diasVencimentoProrata.split(",");
				diasVencimentoProrata.forEach((dvp) => {
					diasVencimentoProrataObjs.push({
						id: dvp,
						label: dvp,
					});
				});
			}
			this.form.patchValue({
				gerarParcelasValorDiferente: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.gerarParcelasValorDiferente
					: "",
				anuidadeNaParcela: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.anuidadeNaParcela
					: false,
				valorAnuidade: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.valorAnuidade
					: "",
				diaAnuidade:
					this.plano.planoRecorrencia &&
					this.plano.planoRecorrencia.diaAnuidade !== undefined
						? this.plano.planoRecorrencia.diaAnuidade
						: 1,
				mesAnuidade:
					this.plano.planoRecorrencia &&
					this.plano.planoRecorrencia.mesAnuidade !== undefined
						? this.plano.planoRecorrencia.mesAnuidade
						: 1,
				diasVencimentoProrata: diasVencimentoProrataObjs,
				prorataObrigatorio: this.plano.prorataObrigatorio,
				obrigatorioInformarCartaoCreditoVenda:
					this.plano.obrigatorioInformarCartaoCreditoVenda,
				renovavelAutomaticamente: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.renovavelAutomaticamente
					: "",
				renovarAutomaticamenteComDesconto:
					this.plano.renovarAutomaticamenteComDesconto,
				renovarProdutoObrigatorio: this.plano.renovarProdutoObrigatorio,
				renovarAutomaticamenteUtilizandoValorBaseContrato:
					this.plano.renovarAutomaticamenteUtilizandoValorBaseContrato,
				naoRenovarParcelaVencida: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.naoRenovarParcelaVencida
					: "",
				parcelamentoOperadora: this.plano.parcelamentoOperadora,
				parcelamentoOperadoraDuracao: this.plano.parcelamentoOperadoraDuracao,
				maximoVezesParcelar: this.plano.maximoVezesParcelar,
				cobrarAdesaoSeparada: this.plano.cobrarAdesaoSeparada,
				nrVezesParcelarAdesao: this.plano.nrVezesParcelarAdesao,
				cobrarProdutoSeparado: this.plano.cobrarProdutoSeparado,
				nrVezesParcelarProduto: this.plano.nrVezesParcelarProduto,
				cancelamentoProporcional: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.cancelamentoProporcional
					: "",
				qtdDiasCobrarProximaParcela: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.qtdDiasCobrarProximaParcela
					: "",
				qtdDiasCobrarAnuidadeTotal: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.qtdDiasCobrarAnuidadeTotal
					: "",
				percentualMultaCancelamento: this.plano.percentualMultaCancelamento,
				produtoTaxaCancelamento: this.plano.produtoTaxaCancelamento,
				descontoAntecipado: this.plano.descontoAntecipado,
			});
			this.plano.planoRecorrencia.anuidadeNaParcela
				? this.form.get("valorAnuidade").disable()
				: this.form.get("valorAnuidade").enable();
		}
		this.tablePlanoRecorrenciaParcelas.dataAdapterFn = (serverData) => {
			serverData.content = this.parcelas;
			return serverData;
		};
		this.form
			.get("anuidadeNaParcela")
			.valueChanges.subscribe((value) =>
				value
					? this.form.get("valorAnuidade").disable()
					: this.form.get("valorAnuidade").enable()
			);
		this.initTableParcelasAnuidade();
	}

	initDiasProrata() {
		this.days.forEach((day, index) => {
			if (index === this.days.length - 1) {
				this.plano.diasVencimentoProrata += `${day.id}`;
			} else {
				this.plano.diasVencimentoProrata += `${day.id},`;
			}
		});
	}

	populateParcelasMatriculas() {
		for (let i = 1; i <= this.plano.planoRecorrencia.duracaoPlano; i++) {
			if (this.parcelas && this.parcelas.length > 0) {
				const parcela = this.parcelas.find((v) => v === i);
				if (!parcela) {
					const parcelaMat = this.parcelasMatriculas.find((v) => v.value === i);
					if (!parcelaMat) {
						this.parcelasMatriculas.push({
							label: `Parcela ${i}`,
							value: i,
						});
					}
				}
			} else {
				const parcela = this.parcelasMatriculas.find((v) => v.value === i);
				if (!parcela) {
					this.parcelasMatriculas.push({
						label: `Parcela ${i}`,
						value: i,
					});
				}
			}
		}
	}

	initPlanoRecorrenciaParcelas() {
		this.tablePlanoRecorrenciaParcelas = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.parcelas,
				};
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmParcelas(row, form, data, rowIndex),
			pagination: false,
			formGroup: new FormGroup({
				numero: new FormControl(),
				valor: new FormControl(),
			}),
			columns: [
				{
					nome: "numero",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
					editable: true,
					width: "100px",
					inputType: "select",
					showAddSelectBtn: false,
					showSelectFilter: false,
					idSelectKey: "value",
					inputSelectData: this.parcelasMatriculas,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					decimal: true,
					prefix: "R$",
					width: "100px",
				},
			],
		});
	}

	beforeConfirmParcelas(row, form, data, rowIndex) {
		if (
			this.plano.planoRecorrencia &&
			+form.get("valor").value > +this.plano.planoRecorrencia.valorMensal
		) {
			this.notificationService.error(
				"O valor da parcela não deve ser maior que o valor da mensalidade!"
			);
			return false;
		}

		return true;
	}

	delete(event) {
		if (this.parcelas) {
			let index;
			if (event.row.codigo) {
				const excecao = this.parcelas.find((ex, i) => {
					if (ex.numero === event.row.numero) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.parcelas.splice(index, 1);
				}
			} else {
				this.parcelas.splice(event.index, 1);
			}
			this.plano.planoRecorrencia.parcelas = this.parcelas;
		}
	}

	prorataObrigatorioconfirm() {
		this.plano.planoRecorrencia.parcelas = this.parcelas;
		this.plano.planoRecorrencia.parcelas.forEach((parcela) => {
			parcela.numero = parcela.numero.value;
		});
		this.state.plano = this.plano;
	}

	initTableParcelasAnuidade() {
		if (!this.plano.planoRecorrencia.parcelasAnuidade) {
			this.plano.planoRecorrencia.parcelasAnuidade = new Array<any>();
		}
		if (this.plano.codigo) {
			this.plano.planoRecorrencia.parcelasAnuidade.forEach(
				(parcelaAnuidade) => {
					parcelaAnuidade.parcela = this.parcelasMatriculas.find(
						(parcela) => parcela.value === parcelaAnuidade.parcela
					);
				}
			);
		}
		this.tableParcelasAnuidade = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.plano.planoRecorrencia.parcelasAnuidade,
				};
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmParcelaAnuidade(row, form, data, rowIndex),
			pagination: false,
			formGroup: new FormGroup({
				parcela: new FormControl(),
				valor: new FormControl(),
			}),
			columns: [
				{
					nome: "parcela",
					titulo: "Parcela",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					showAddSelectBtn: false,
					showSelectFilter: false,
					idSelectKey: "value",
					width: "150px",
					inputSelectData: this.parcelasMatriculas,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					decimal: true,
					width: "100px",
					valueTransform: (v) => {
						return this.decimalPipe.transform(parseFloat(v), "1.2-2");
					},
				},
				{
					nome: "parcela",
					titulo: "Data Cobrança",
					visible: true,
					ordenavel: false,
					editable: false,
					valueTransform: (v) => {
						if (v && v.value !== undefined && v.value !== null) {
							return `No mesmo dia da PARCELA ${v.value}`;
						}
						return "";
					},
				},
			],
		});
	}

	beforeConfirmParcelaAnuidade(row, form, data, rowIndex): boolean {
		if (
			form.get("parcela").value === null ||
			form.get("parcela").value === undefined
		) {
			this.notificationService.error("Selecione uma parcela!");
			return false;
		}

		if (
			form.get("valor").value === null ||
			form.get("valor").value === undefined ||
			form.get("valor").value === 0
		) {
			this.notificationService.error("Informe o valor da parcela!");
			return false;
		}

		if (
			data.find(
				(parcelaAnuidade, index) =>
					index !== rowIndex &&
					parcelaAnuidade.parcela.value === form.get("parcela").value.value
			)
		) {
			this.notificationService.error(
				`Parcela ${form.get("parcela").value.value} já foi adicionada!`
			);
			return false;
		}

		return true;
	}

	deleteParcelaAnuidade(event) {
		if (this.plano.planoRecorrencia.parcelasAnuidade) {
			let index;
			if (event.row.codigo) {
				const excecao = this.plano.planoRecorrencia.parcelasAnuidade.find(
					(ex, i) => {
						if (ex.numero === event.row.numero) {
							index = i;
							return ex;
						}
					}
				);
				if (excecao && index !== undefined) {
					this.plano.planoRecorrencia.parcelasAnuidade.splice(index, 1);
				}
			} else {
				this.plano.planoRecorrencia.parcelasAnuidade.splice(event.index, 1);
			}
			this.updateValorAnuidadeByParcelasAnuidade(event.data);
		}
	}

	confirmParcelaAnuidade(event) {
		if (event.data) {
			this.updateValorAnuidadeByParcelasAnuidade(event.data);
		}
	}

	updateValorAnuidadeByParcelasAnuidade(data) {
		let valorAnuidade = 0;
		data.forEach((parcelaAnuidade) => {
			valorAnuidade += +parcelaAnuidade.valor;
		});
		this.form.get("valorAnuidade").setValue(valorAnuidade);
	}
}
