<pacto-cat-tabs-vertical>
	<ng-template label="Parcelas com valor diferente" pactoTabVertical>
		<pacto-cat-checkbox
			[control]="form.get('gerarParcelasValorDiferente')"
			label="Definir parcelas com valor diferente (matr<PERSON><PERSON> ou rematrícula)"></pacto-cat-checkbox>
		<pacto-cat-table-editable
			(confirm)="confirm()"
			(delete)="delete($event)"
			[isEditable]="true"
			[table]="tablePlanoRecorrenciaParcelas"
			showAddRow="true"></pacto-cat-table-editable>
	</ng-template>
	<ng-template label="Anuidade" pactoTabVertical>
		<pacto-cat-checkbox
			[control]="form.get('anuidadeNaParcela')"
			label="Anuidade com mesmo vencimento da parcela"></pacto-cat-checkbox>
		<pacto-cat-form-input-number
			[decimal]="false"
			[formControl]="form.get('valorAnuidade')"
			label="Valor"></pacto-cat-form-input-number>
		<ng-container *ngIf="!form.get('anuidadeNaParcela').value">
			<pacto-cat-form-input-number
				[formControl]="form.get('diaAnuidade')"
				label="Dia"
				max="31"></pacto-cat-form-input-number>
			<pacto-cat-form-select
				[control]="form.get('mesAnuidade')"
				[items]="meses"
				idKey="value"
				label="Mês"></pacto-cat-form-select>
		</ng-container>
		<ng-container *ngIf="form.get('anuidadeNaParcela').value">
			<pacto-cat-table-editable
				(confirm)="confirmParcelaAnuidade($event)"
				(delete)="deleteParcelaAnuidade($event)"
				[isEditable]="true"
				[table]="tableParcelasAnuidade"
				showAddRow="true"></pacto-cat-table-editable>
		</ng-container>
	</ng-template>
	<ng-template label="Data de pagamento" pactoTabVertical>
		<pacto-cat-form-multi-select-filter
			[control]="form.get('diasVencimentoProrata')"
			[options]="days"
			label="Escolha os dias do mês"></pacto-cat-form-multi-select-filter>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('prorataObrigatorio')"
				label="Data de vencimento obrigátoria"></pacto-cat-checkbox>
		</div>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('obrigatorioInformarCartaoCreditoVenda')"
				i18n-label="adm:label-obrigatorio-informar-cartao-venda"
				id="check-obrigatorio-informar-cartao-venda"
				label="Obrigatoriedade de cadastro de cartão de crédito para venda"></pacto-cat-checkbox>
		</div>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		label="Renovação automática"
		pactoTabVertical>
		<pacto-cat-checkbox
			[control]="form.get('renovavelAutomaticamente')"
			class="mt-16 mb-16 display-block"
			label="Ativar renovação automática"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarAutomaticamenteComDesconto')"
			class="mt-16 mb-16 display-block"
			label="Renovar plano com desconto"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarProdutoObrigatorio')"
			class="mt-16 mb-16 display-block"
			label="Renovar produtos obrigatórios"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarAutomaticamenteUtilizandoValorBaseContrato')"
			class="mt-16 mb-16 display-block"
			label="Renovar contrato automaticamente utilizando valor base do contrato"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		label="Não renovar com parcela aberta"
		pactoTabVertical>
		<pacto-cat-checkbox
			[control]="form.get('naoRenovarParcelaVencida')"
			class="mt-16 mb-16 display-block"
			label="Não renovar com parcela aberta (vencida)"></pacto-cat-checkbox>
	</ng-template>
	<ng-template label="Parcela pela operadora" pactoTabVertical>
		<ng-container>
			<pacto-cat-checkbox
				[control]="form.get('parcelamentoOperadora')"
				class="mt-16 mb-16 display-block"
				label="Permite parcelamento pela operadora"></pacto-cat-checkbox>
			<pacto-cat-checkbox
				*ngIf="form.get('parcelamentoOperadora').value"
				[control]="form.get('parcelamentoOperadoraDuracao')"
				class="mt-16 mb-16 display-block"
				label="Número de vezes de acordo com a duração do contrato/plano"></pacto-cat-checkbox>
			<pacto-cat-form-input-number
				*ngIf="
					form.get('parcelamentoOperadora').value &&
					form.get('parcelamentoOperadoraDuracao').value
				"
				[formControl]="form.get('maximoVezesParcelar')"
				label="Número máximo de parcelas"></pacto-cat-form-input-number>
		</ng-container>
	</ng-template>
	<ng-template label="Cobrar adesão separada" pactoTabVertical>
		<pacto-cat-checkbox
			[control]="form.get('cobrarAdesaoSeparada')"
			class="mt-16 mb-16 display-block"
			label="Permite cobrar adesão separada"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarAdesao')"
			[items]="listaParcelas"
			idKey="value"
			label="Número máximo de parcelas"></pacto-cat-form-select>
	</ng-template>
	<ng-template label="Cancelamento verificando parcela" pactoTabVertical>
		<div style="width: 480px">
			<pacto-cat-checkbox
				[control]="form.get('cancelamentoProporcional')"
				class="mt-16 mb-16 display-block"
				label="Habilitar cancelamento verificando próxima parcela em aberto"></pacto-cat-checkbox>
			<pacto-cat-form-input-number
				[decimal]="false"
				[formControl]="form.get('qtdDiasCobrarProximaParcela')"
				label="Prazo para avisar com antecedência para não cobrar próxima parcela"></pacto-cat-form-input-number>
			<pacto-cat-form-input-number
				[decimal]="false"
				[formControl]="form.get('qtdDiasCobrarAnuidadeTotal')"
				label="Prazo para cobrar valor total de anuidade antes do vencimento"></pacto-cat-form-input-number>
		</div>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		label="Desconto"
		pactoTabVertical>
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('descontoAntecipado')"
			[endpointUrl]="admRestService.buildFullUrlPlano('desconto')"
			[label]="'Desconto em renovação antecipada'"
			[paramBuilder]="descontoSelectBuilder"
			idKey="codigo"
			labelKey="descricao"></pacto-cat-form-select-filter>
	</ng-template>
</pacto-cat-tabs-vertical>
