<pacto-cat-tabs-vertical>
	<ng-template
		pactoTabVertical
		label="Acessos semanais"
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'">
		<pacto-cat-form-select
			[control]="form.get('quantidadeMaximaFrequencia')"
			[items]="frequencias"
			idKey="value"
			label="Acesso semanal"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		pactoTabVertical
		label="Convites"
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'">
		<pacto-cat-form-input-number
			[formControl]="form.get('convidadosPorMes')"
			label="Quantidade de convite por mês"></pacto-cat-form-input-number>
	</ng-template>
	<ng-template pactoTabVertical label="Vendas online">
		<pacto-cat-checkbox
			[control]="form.get('site')"
			label="Participa do vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-compartilhar-plano-site"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirCompartilharPLanoNoSite')"
			label="Permite compartilhar plano no vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirVendaPlanoSiteNoBalcao')"
			label="Permite venda do plano no balcão"></pacto-cat-checkbox>
	</ng-template>
	<ng-template pactoTabVertical label="Venda rápida">
		<pacto-cat-checkbox
			[control]="form.get('apresentarVendaRapida')"
			label="Apresentar na venda rápida"></pacto-cat-checkbox>
	</ng-template>
	<ng-template pactoTabVertical label="Bolsa">
		<pacto-cat-checkbox
			[control]="form.get('bolsa')"
			label="Plano bolsa"></pacto-cat-checkbox>
	</ng-template>
	<ng-template pactoTabVertical label="Comissão">
		<pacto-cat-checkbox
			[control]="form.get('comissao')"
			label="Gerar comissão"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		pactoTabVertical
		label="Compartilhamento de Plano"
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'">
		<pacto-cat-checkbox
			[control]="form.get('quantidadeCompartilhamentos')"
			label="Quantidade de compartilhamentos do plano"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-restricao-recompra"
		label="Restrição de venda"
		pactoTabVertical="restricao-recompra">
		<pacto-cat-checkbox
			[control]="form.get('bloquearRecompra')"
			i18n-label="@@adm:label-bloquear-recompra"
			id="check-bloquear-recompra"
			label="Venda apenas para visitante"></pacto-cat-checkbox>

		<pacto-cat-form-datepicker
			*ngIf="
				plano.tipoPlano !== 'PLANO_PERSONAL' &&
				plano.tipoPlano !== 'PLANO_CREDITO' &&
				plano.tipoPlano !== 'PLANO_AVANCADO'
			"
			[control]="form.get('contratosEncerramDia')"
			i18n-label="@@adm:label-encerram-dia"
			id="datepicker-encerram-dia"
			label="Contratos encerram no dia"></pacto-cat-form-datepicker>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-observacoes"
		label="Observações"
		pactoTabVertical="observacoes">
		<div class="input-nome">Observação 1</div>
		<input
			[formControl]="form.get('observacao1')"
			class="input-text"
			type="text" />

		<div class="input-nome">Observação 2</div>
		<input
			[formControl]="form.get('observacao2')"
			class="input-text"
			type="text" />
	</ng-template>
</pacto-cat-tabs-vertical>
