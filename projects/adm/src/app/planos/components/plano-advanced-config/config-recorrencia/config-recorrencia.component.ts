import { Component, Input, OnInit } from "@angular/core";
import { Plano } from "../../../plano.model";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-config-recorrencia",
	templateUrl: "./config-recorrencia.component.html",
	styleUrls: ["./config-recorrencia.component.scss"],
})
export class ConfigRecorrenciaComponent implements OnInit {
	@Input() plano = new Plano();
	@Input() form: FormGroup;

	frequencias: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();

	constructor() {}

	ngOnInit() {
		this.initFrequencias();
		if (this.plano) {
			this.form.patchValue({
				quantidadeMaximaFrequencia: this.plano.quantidadeMaximaFrequencia,
				convidadosPorMes: this.plano.convidadosPorMes,
				site: this.plano.site,
				permitirCompartilharPLanoNoSite:
					this.plano.permitirCompartilharPLanoNoSite,
				permitirVendaPlanoSiteNoBalcao:
					this.plano.permitirVendaPlanoSiteNoBalcao,
				apresentarVendaRapida: this.plano.apresentarVendaRapida,
				bolsa: this.plano.bolsa,
				comissao: this.plano.comissao,
				quantidadeCompartilhamentos: this.plano.quantidadeCompartilhamentos,
				videoSiteUrl: this.plano.videoSiteUrl,
				observacaoSite: this.plano.observacaoSite,
				bloquearRecompra: this.plano.bloquearRecompra || false,
				observacao1: this.plano.observacao1,
				observacao2: this.plano.observacao2,
				contratosEncerramDia: this.plano.contratosEncerramDia,
			});
		}
	}

	initFrequencias() {
		for (let i = 1; i <= 7; i++) {
			this.frequencias.push({
				value: i,
				label: `${i}x por semana`,
			});
		}
	}
}
