import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";
import {
	PactoDataGridConfig,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";
import { Plano, PlanoEmpresaRedeAcesso, TipoPlano } from "../../../plano.model";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoService } from "../../cadastrar-plano/plano.service";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-config-normal",
	templateUrl: "./config-normal.component.html",
	styleUrls: ["./config-normal.component.scss"],
})
export class ConfigNormalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnNumeroMeses", { static: true })
	columnNumeroMeses: TemplateRef<any>;
	@ViewChild("columnCarencia", { static: true })
	columnCarencia: TemplateRef<any>;
	@ViewChild("columnValorMaximoDescontoPlano", { static: true })
	columnValorMaximoDescontoPlano: TemplateRef<any>;
	@Input() plano = new Plano();
	@Input() form: FormGroup;
	days: Array<{ label: string; id: number }> = new Array<{
		label: string;
		id: number;
	}>();
	@Input() mostrarAbaFerias: boolean;
	frequencias: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	inicioMinimoContratoValid = true;

	tableFerias: PactoDataGridConfig;
	tableDescontoDuracao: PactoDataGridConfig;
	parcelas: Array<{ id: number; label: string }> = new Array<{
		id: number;
		label: string;
	}>();
	mask = { mask: [/[0-3]/, /[0-9]?/], guide: false };
	planoDuracoes: Array<any> = new Array<any>();
	listaParcelas: Array<any> = new Array<any>();
	modules = {};
	fontSizeArr = ["8px", "9px", "10px", "11px", "12px", "14px", "16px"];
	Size = Quill.import("attributors/style/size");

	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "VO",
	};
	modeloContratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};
	descontoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	constructor(
		public admRestService: AdmRestService,
		public planoCommonsService: PlanoCommonsService,
		public planoStateService: PlanoStateService,
		public planoService: PlanoService,
		private cd: ChangeDetectorRef
	) {
		for (let i = 1; i <= 31; i++) {
			this.days.push({
				label: i.toString(),
				id: i,
			});
		}

		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ align: [] }],
					["clean"],
				],
			},
		};
	}

	ngOnInit() {
		this.Size.whitelist = this.fontSizeArr;
		Quill.register(this.Size, true);

		const Block = Quill.import("blots/block");
		Block.tagName = "DIV";
		Quill.register(Block, true);

		this.initListaParcelas();
		this.setFrequencias();
		if (this.plano) {
			let diasVencimentoProrataObjs: Array<{ label: string; id: number }> =
				new Array<{ label: string; id: number }>();
			if (!this.plano.diasVencimentoProrata) {
				this.initDiasProrata();
			}
			if (this.plano.diasVencimentoProrata) {
				const diasVencimentoProrata =
					this.plano.diasVencimentoProrata.split(",");
				diasVencimentoProrata.forEach((dvp) => {
					diasVencimentoProrataObjs.push({
						id: dvp,
						label: dvp,
					});
				});
				diasVencimentoProrataObjs = diasVencimentoProrataObjs.sort((a, b) => {
					if (+a.id < +b.id) {
						return -1;
					} else if (+a.id > +b.id) {
						return 1;
					}
					return 0;
				});
			}
			this.form.patchValue({
				termoAceite: this.plano.termoAceite,
				site: this.plano.site,
				permitirCompartilharPLanoNoSite:
					this.plano.permitirCompartilharPLanoNoSite,
				permitirVendaPlanoSiteNoBalcao:
					this.plano.permitirVendaPlanoSiteNoBalcao,
				apresentarPactoFlow: this.plano.apresentarPactoFlow,
				inicioMinimoContrato: this.plano.inicioMinimoContrato,
				bolsa: this.plano.bolsa,
				parcelamentoOperadora: this.plano.parcelamentoOperadora,
				parcelamentoOperadoraDuracao: this.plano.parcelamentoOperadoraDuracao,
				maximoVezesParcelar: this.plano.maximoVezesParcelar,
				convidadosPorMes: this.plano.convidadosPorMes,
				quantidadeMaximaFrequencia: this.plano.quantidadeMaximaFrequencia,
				diasVencimentoProrata: diasVencimentoProrataObjs,
				prorataObrigatorio: this.plano.prorataObrigatorio,
				obrigatorioInformarCartaoCreditoVenda:
					this.plano.obrigatorioInformarCartaoCreditoVenda,
				renovavelAutomaticamente: this.plano.renovavelAutomaticamente,
				renovarAutomaticamenteComDesconto:
					this.plano.renovarAutomaticamenteComDesconto,
				renovarProdutoObrigatorio: this.plano.renovarProdutoObrigatorio,
				renovarAutomaticamenteUtilizandoValorBaseContrato:
					this.plano.renovarAutomaticamenteUtilizandoValorBaseContrato,
				naoRenovarContratoParcelaVencidaAberto:
					this.plano.naoRenovarContratoParcelaVencidaAberto,
				renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia:
					this.plano.renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia,
				permitePagarComBoleto: this.plano.permitePagarComBoleto,
				diaDoMesDescontoBoletoPagAntecipado:
					this.plano.diaDoMesDescontoBoletoPagAntecipado,
				porcentagemDescontoBoletoPagAntecipado:
					this.plano.porcentagemDescontoBoletoPagAntecipado,
				aceitaDescontoPorPlano: this.plano.aceitaDescontoPorPlano,
				cobrarAdesaoSeparada: this.plano.cobrarAdesaoSeparada,
				nrVezesParcelarAdesao: this.plano.nrVezesParcelarAdesao,
				cobrarProdutoSeparado: this.plano.cobrarProdutoSeparado,
				nrVezesParcelarProduto: this.plano.nrVezesParcelarProduto,
				descontoAntecipado: this.plano.descontoAntecipado,
				comissao: this.plano.comissao,
				aceitaDescontoExtra: this.plano.aceitaDescontoExtra,
				permitirAcessoRedeEmpresa: this.plano.permitirAcessoRedeEmpresa,
				permitirTurmasVendasOnline: this.plano.permitirTurmasVendasOnline,
				videoSiteUrl: this.plano.videoSiteUrl,
				observacaoSite: this.plano.observacaoSite,
				planoDiferenteRenovacao: this.plano.planoDiferenteRenovacao,
				horarioPlanoDiferenteRenovacao:
					this.plano.horarioPlanoDiferenteRenovacao,
				duracaoPlanoDiferenteRenovacao:
					this.plano.duracaoPlanoDiferenteRenovacao,
				condicaoPagPlanoDiferenteRenovacao:
					this.plano.condicaoPagPlanoDiferenteRenovacao,
				bloquearRecompra: this.plano.bloquearRecompra || false,
				observacao1: this.plano.observacao1,
				observacao2: this.plano.observacao2,
				contratosEncerramDia: this.plano.contratosEncerramDia,
			});
			if (this.plano.planoDiferenteRenovacao) {
				this.planoService
					.findById(this.plano.planoDiferenteRenovacao)
					.subscribe((resp) => {
						this.form.get("planoDiferenteRenovacao").setValue(resp.content);
					});
			}
			if (this.plano.modalidadesPlanoDiferenteRenovacao) {
				this.form
					.get("modalidadesPlanoDiferenteRenovacao")
					.setValue(
						this.plano.modalidadesPlanoDiferenteRenovacao
							.split(",")
							.map((id) => ({ id: id.trim() }))
					);
			}
		}
		let firstDigit;
		this.form.get("diasVencimentoProrata").valueChanges.subscribe((value) => {
			if (value.length === 1) {
				firstDigit = value;
			}
			if (+value > 31) {
				if (firstDigit) {
					this.form.get("diasVencimentoProrata").setValue(firstDigit + "");
				} else {
					this.form.get("diasVencimentoProrata").setValue("");
				}
			}
		});
		this.initTableFerias();
		this.initTableDescontoDuracao();
		this.preencheParcelas();

		this.form.get("site").valueChanges.subscribe((value) => {
			this.plano.site = value;
		});

		if (this.form.get("permitirVendaPlanoSiteNoBalcao") !== null) {
			this.form
				.get("permitirVendaPlanoSiteNoBalcao")
				.valueChanges.subscribe((value) => {
					this.plano.permitirVendaPlanoSiteNoBalcao = value;
				});
		}

		if (this.form.get("permitirCompartilharPLanoNoSite") !== null) {
			this.form
				.get("permitirCompartilharPLanoNoSite")
				.valueChanges.subscribe((value) => {
					this.plano.permitirCompartilharPLanoNoSite = value;
				});
		}
	}

	async setFrequencias() {
		this.frequencias = await this.planoCommonsService.initFrequencias(
			this.traducao
		);
		this.cd.detectChanges();
	}

	initDiasProrata() {
		if (!this.plano.diasVencimentoProrata) {
			this.plano.diasVencimentoProrata = "";
			this.days = this.days.sort((a, b) => {
				if (a.id < b.id) {
					return -1;
				} else if (a.id > b.id) {
					return 1;
				}
				return 0;
			});
			this.days.forEach((day, index) => {
				if (index === this.days.length - 1) {
					this.plano.diasVencimentoProrata += `${day.id}`;
				} else {
					this.plano.diasVencimentoProrata += `${day.id},`;
				}
			});
		}
	}

	initTableFerias() {
		this.tableFerias = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.plano.duracoes,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				carencia: new FormControl(),
				bloquearRecompra: new FormControl(false),
				contratosEncerramDia: new FormControl(null),
			}),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "100px",
				},
				{
					nome: "numeroMeses",
					titulo: this.columnNumeroMeses,
					visible: true,
					ordenavel: false,
					valueTransform: (v) => v + "m",
					width: "100px",
				},
				{
					nome: "carencia",
					titulo: this.columnCarencia,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					width: "100px",
				},
			],
		});
	}

	initTableDescontoDuracao() {
		this.tableDescontoDuracao = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.plano.duracoes,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				valorMaximoDescontoPlano: new FormControl(),
			}),
			columns: [
				{
					nome: "numeroMeses",
					titulo: this.columnNumeroMeses,
					visible: true,
					ordenavel: false,
					valueTransform: (v) => v + "m",
					width: "100px",
				},
				{
					nome: "valorMaximoDescontoPlano",
					titulo: this.columnValorMaximoDescontoPlano,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					width: "100px",
				},
			],
		});
	}

	confirmFerias(event) {}

	confirmDescontoDuracao(event) {}

	preencheParcelas() {
		for (let i = 1; i <= 12; i++) {
			this.parcelas.push({
				id: i,
				label: `${i}x`,
			});
		}
	}

	dateFilter(date: Date): boolean {
		const actualDate = new Date();
		actualDate.setHours(0, 0, 0, 0);
		return date.getTime() >= actualDate.getTime();
	}

	initListaParcelas() {
		for (let i = 1; i <= 12; i++) {
			this.listaParcelas.push({
				label: `${i}x`,
				value: i,
			});
		}
	}

	existeModalidadeTurmaSelecionada() {
		if (this.plano && this.plano.excecoes) {
			const index = this.plano.excecoes.findIndex(
				(e) => e.modalidade.utilizaTurma
			);
			return index >= 0;
		}
		return false;
	}

	getPlanosPermitidos() {
		return (
			this.plano.tipoPlano === TipoPlano.PLANO_NORMAL ||
			this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			this.plano.tipoPlano === TipoPlano.PLANO_AVANCADO
		);
	}

	initMods(): Array<{ label: string; id: number }> {
		if (
			this.form.get("modalidadesPlanoDiferenteRenovacao").value &&
			this.form.get("modalidadesPlanoDiferenteRenovacao").value.length > 0 &&
			!this.form.get("modalidadesPlanoDiferenteRenovacao").value[0].label
		) {
			const modView = [];
			this.form.get("modalidadesPlanoDiferenteRenovacao").value.forEach((m) => {
				if (
					this.form.get("planoDiferenteRenovacao").value &&
					this.form.get("planoDiferenteRenovacao").value.modalidades
				) {
					const modalis = this.form
						.get("planoDiferenteRenovacao")
						.value.modalidades.find((mod) => mod.modalidade.codigo == m.id);
					if (modalis) {
						modView.push({
							label: modalis.modalidade.nome,
							id: modalis.modalidade.codigo,
						});
					}
				}
			});
			this.form
				.get("modalidadesPlanoDiferenteRenovacao")
				.setValue(
					modView.length > 0
						? modView
						: this.form.get("modalidadesPlanoDiferenteRenovacao").value
				);
		}

		const mods = [];
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.modalidades
		) {
			this.form
				.get("planoDiferenteRenovacao")
				.value.modalidades.forEach((m) => {
					mods.push({
						label: m.modalidade.nome,
						id: m.modalidade.codigo,
					});
				});
		}
		return mods;
	}

	initHors(): Array<{ descricao: string; codigo: number }> {
		const hors = [
			{
				descricao: "-",
				codigo: undefined,
			},
		];
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.horarios
		) {
			this.form.get("planoDiferenteRenovacao").value.horarios.forEach((h) => {
				hors.push({
					descricao: h.horario.descricao,
					codigo: h.horario.codigo,
				});
			});
		}
		return hors;
	}

	initDuracoes(): Array<{ descricao: string; codigo: number }> {
		const dura = [
			{
				descricao: "-",
				codigo: undefined,
			},
		];
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.duracoes
		) {
			this.form.get("planoDiferenteRenovacao").value.duracoes.forEach((h) => {
				dura.push({
					descricao: h.numeroMeses + (h.numeroMeses == 1 ? " Mes" : " Meses"),
					codigo: h.codigo,
				});
			});
		}
		return dura;
	}

	initConds(): Array<{ descricao: string; codigo: number }> {
		const cond = [
			{
				descricao: "-",
				codigo: undefined,
			},
		];
		if (
			this.form.get("duracaoPlanoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.duracoes
		) {
			this.form.get("planoDiferenteRenovacao").value.duracoes.forEach((d) => {
				if (d.codigo == this.form.get("duracaoPlanoDiferenteRenovacao").value) {
					d.condicoesPagamento.forEach((c) => {
						cond.push({
							descricao: c.condicaoPagamento.descricao,
							codigo: c.condicaoPagamento.codigo,
						});
					});
				}
			});
		}
		return cond;
	}

	selectBuilderPlanosDiferentes: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "500",
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	isPlanoDiferenteTurma(): boolean {
		return (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.codigo &&
			this.form
				.get("planoDiferenteRenovacao")
				.value.modalidades.some((pM: any) => pM.modalidade.utilizaTurma)
		);
	}

	limparCamposPlanoDiferente(todos) {
		if (todos) {
			this.plano.planoDiferenteRenovacao = null;
			this.form.get("planoDiferenteRenovacao").setValue(null);
		}
		this.plano.horarioPlanoDiferenteRenovacao = null;
		this.plano.duracaoPlanoDiferenteRenovacao = null;
		this.plano.condicaoPagPlanoDiferenteRenovacao = null;
		this.plano.modalidadesPlanoDiferenteRenovacao = null;
		this.form.get("horarioPlanoDiferenteRenovacao").setValue(null);
		this.form.get("duracaoPlanoDiferenteRenovacao").setValue(null);
		this.form.get("condicaoPagPlanoDiferenteRenovacao").setValue(null);
		this.form.get("modalidadesPlanoDiferenteRenovacao").setValue(null);
		this.cd.detectChanges();
	}

	empresasRedeUpdate(empresasRede: Array<PlanoEmpresaRedeAcesso>) {
		this.plano.empresasRede = empresasRede;
	}
}
