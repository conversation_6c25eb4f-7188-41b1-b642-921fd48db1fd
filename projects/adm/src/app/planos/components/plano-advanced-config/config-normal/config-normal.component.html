<pacto-cat-tabs-vertical>
	<ng-template
		i18n-label="@@adm:tab-label-acesso-semanal"
		label="Acessos semanais"
		pactoTabVertical="acessos-semanais">
		<pacto-cat-form-select
			[control]="form.get('quantidadeMaximaFrequencia')"
			[items]="frequencias"
			i18n-label="@@adm:label-acesso-semanal"
			id="select-qtd-max-frequencia"
			idKey="value"
			label="Acesso semanal"></pacto-cat-form-select>
	</ng-template>

	<ng-template
		i18n-label="@@adm:tab-label-vendas-online"
		label="Vendas online"
		pactoTabVertical="vendas-online">
		<pacto-cat-checkbox
			[control]="form.get('site')"
			i18n-label="@@adm:label-vendas-online"
			id="check-vendas-online"
			label="Participa do vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-compartilhar-plano-site"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirCompartilharPLanoNoSite')"
			label="Permite compartilhar plano no vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-venda-plano-site-balcao"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirVendaPlanoSiteNoBalcao')"
			label="Permite venda do plano no balcão"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			id="check-pacto-flow"
			[control]="form.get('apresentarPactoFlow')"
			i18n-label="@@adm:label-apresentar-no-pacto-flow"
			label="Apresentar no Pacto Flow"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="
				form.get('site').value &&
				existeModalidadeTurmaSelecionada() &&
				getPlanosPermitidos()
			"
			[control]="form.get('permitirTurmasVendasOnline')"
			i18n-label="@@adm:label-permitir-turmas-vendas-online"
			id="check-permitir-turma-vendas-online"
			label="Permitir turmas no Vendas Online"></pacto-cat-checkbox>
		<pacto-cat-form-datepicker
			[control]="form.get('inicioMinimoContrato')"
			[dateFilter]="dateFilter"
			errorMsg="Informe uma data não retroativa!"
			i18n-errorMsg="@@adm:error-inicio-minimo-contrato"
			i18n-label="@@adm:label-inicio-minimo-contrato"
			id="datepicker-inicio-min-contrato"
			label="Contratos iniciam a partir de"></pacto-cat-form-datepicker>
		<pacto-cat-form-select-filter
			*ngIf="form.get('site').value"
			[addtionalFilters]="modeloContratoAddtionalFilters"
			[control]="form.get('termoAceite')"
			[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
			[idKey]="'codigo'"
			[labelKey]="'descricao'"
			[paramBuilder]="modeloContratoSelectBuilder"
			errorMsg="Selecione um termo de aceite"
			i18n-errorMsg="@@adm:label-termo-aceite"
			i18n-label="@@adm:label-termo-aceite"
			id="select-termo-aceite"
			label="Termo de aceite"></pacto-cat-form-select-filter>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12">
				<div class="input-nome">Vídeo youtube</div>
				<input
					[formControl]="form.get('videoSiteUrl')"
					class="input-text"
					placeholder="Informe aqui o código do vídeo no youtube Ex: 6AidSn00VGo"
					type="text" />
			</div>
		</div>

		<label *ngIf="form.get('site').value" class="input-nome">Observação</label>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12 div-text-area-modelo-contrato">
				<quill-editor
					[formControl]="form.get('observacaoSite')"
					[id]="'editor-texto-observacao'"
					[modules]="modules"></quill-editor>
			</div>
		</div>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-convites"
		label="Convites"
		pactoTabVertical="convites">
		<pacto-cat-form-input-number
			[formControl]="form.get('convidadosPorMes')"
			i18n-label="@@adm:label-convidados-por-mes"
			id="input-convidados-mes"
			label="Quantidade de convite por mês"></pacto-cat-form-input-number>
	</ng-template>
	<ng-template
		*ngIf="!form.get('site').value"
		i18n-label="@@adm:tab-label-bolsa"
		label="Bolsa"
		pactoTabVertical="bolsa">
		<pacto-cat-checkbox
			[control]="form.get('bolsa')"
			i18n-label="@@adm:label-bolsa"
			id="check-bolsa"
			label="Plano bolsa"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		*ngIf="mostrarAbaFerias"
		i18n-label="@@adm:tab-label-ferias"
		label="Férias"
		pactoTabVertical="ferias">
		<div class="table-wrapper pacto-shadow">
			<pacto-cat-table-editable
				(confirm)="confirmFerias($event)"
				[isEditable]="true"
				[showDelete]="false"
				[table]="tableFerias"
				class="table-ferias"
				idSuffix="ferias-plano"></pacto-cat-table-editable>
		</div>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-data-pagamento"
		label="Data de pagamento"
		pactoTabVertical="data-pagamento">
		<pacto-cat-form-multi-select-filter
			[control]="form.get('diasVencimentoProrata')"
			[options]="days"
			i18n-label="adm:label-dias-prorata"
			id="select-dias-prorata"
			label="Escolha os dias do mês"></pacto-cat-form-multi-select-filter>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('prorataObrigatorio')"
				i18n-label="adm:label-prorata-obrigatorio"
				id="check-prorata-obrigatorio"
				label="Data de vencimento obrigátoria"></pacto-cat-checkbox>
		</div>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('obrigatorioInformarCartaoCreditoVenda')"
				i18n-label="adm:label-obrigatorio-informar-cartao-venda"
				id="check-obrigatorio-informar-cartao-venda"
				label="Obrigatoriedade de cadastro de cartão de crédito para venda"></pacto-cat-checkbox>
		</div>
	</ng-template>
	<ng-template
		pactoTabVertical="renovacao-automatica"
		i18n-label="@@adm:tab-label-renovacao-automatica"
		label="Renovação automática">
		<pacto-cat-checkbox
			[control]="form.get('renovarAutomaticamenteComDesconto')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovar-plano-desconto"
			id="check-renovar-plano-desconto"
			label="Renovar plano com desconto"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarProdutoObrigatorio')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovar-produto-obrigatorio"
			id="check-renovar-produto-obrigatorio"
			label="Renovar produtos obrigatórios"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('naoRenovarContratoParcelaVencidaAberto')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-nao-renovar-contrato-parcela-vencida-aberta"
			id="check-nao-renovar-contrato-parcela-vencida-aberta"
			label="Não renovar contrato com parcela vencida em aberto"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="
				form.get('renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia')
			"
			class="mt-16 display-block"
			i18n-label="adm:label-renovar-auto-apenas-plano-cond-pag-recorrencia"
			id="check-renovar-auto-apenas-plano-cond-pag-rec"
			label="Renovar apenas planos com condição de pagamento recorrência"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			class="mt-16 mb-16 display-block"
			i18n-label="
				adm:label-renovar-automaticamente-utilizando-valor-base-contrato
			"
			id="check-renovar-auto-valor-base-contrato"
			i18n-label="
				adm:label-renovar-automaticamente-utilizando-valor-base-contrato
			"
			label="Renovar contrato automaticamente utilizando valor base do contrato"
			[control]="
				form.get('renovarAutomaticamenteUtilizandoValorBaseContrato')
			"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			id="check-renovavel-auto"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovavel-automaticamente"
			label="Ativar renovação automática"
			(click)="limparCamposPlanoDiferente(true)"
			[control]="form.get('renovavelAutomaticamente')"></pacto-cat-checkbox>
		<pacto-cat-form-select-filter
			*ngIf="form.get('renovavelAutomaticamente').value"
			[label]="'Selecionar plano para renovação automática'"
			[control]="form.get('planoDiferenteRenovacao')"
			[endpointUrl]="admRestService.buildFullUrlPlano('planos')"
			labelKey="descricao"
			idKey="codigo"
			(click)="limparCamposPlanoDiferente(false)"
			[addEmptyOption]="true"
			class="bottom-renov-selec"
			[paramBuilder]="
				selectBuilderPlanosDiferentes
			"></pacto-cat-form-select-filter>
		<div style="width: 100%">
			<span
				style="color: red; text-align: center; margin-top: 10px; display: block"
				*ngIf="
					form.get('renovavelAutomaticamente').value && isPlanoDiferenteTurma()
				">
				Plano selecionado possui turma e não é permitido, escolha outro.
			</span>
		</div>
		<pacto-cat-form-multi-select-filter
			label="Modalidades"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!form.get('planoDiferenteRenovacao').value.regimeRecorrencia &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('modalidadesPlanoDiferenteRenovacao')"
			[options]="initMods()"></pacto-cat-form-multi-select-filter>
		<pacto-cat-form-select
			[label]="'Horario'"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('horarioPlanoDiferenteRenovacao')"
			[items]="initHors()"
			labelKey="descricao"
			idKey="codigo"></pacto-cat-form-select>
		<pacto-cat-form-select
			[label]="'Duração'"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('duracaoPlanoDiferenteRenovacao')"
			[items]="initDuracoes()"
			labelKey="descricao"
			idKey="codigo"></pacto-cat-form-select>
		<pacto-cat-form-select
			[label]="'Condição de Pagamento'"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('condicaoPagPlanoDiferenteRenovacao')"
			[items]="initConds()"
			labelKey="descricao"
			idKey="codigo"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		*ngIf="!form.get('site').value"
		i18n-label="@@adm:tab-label-pagamento-boleto"
		label="Pagamento com boleto"
		pactoTabVertical="pagamento-boleto">
		<pacto-cat-checkbox
			[control]="form.get('permitePagarComBoleto')"
			i18n-label="adm:label-permite-pagar-boleto"
			id="check-permite-pagar-boleto"
			label="Permitir pagar com boleto"></pacto-cat-checkbox>
		<pacto-cat-form-input-number
			[formControl]="form.get('diaDoMesDescontoBoletoPagAntecipado')"
			i18n-label="adm:label-dia-mes-desconto-boleto-pag-antecipado"
			id="input-dia-mes-desconto-boleto-pag-antecipado"
			label="Data mensal para pagamento antecipado do boleto"
			max="31"></pacto-cat-form-input-number>
		<pacto-cat-form-input-number
			[formControl]="form.get('porcentagemDescontoBoletoPagAntecipado')"
			decimal="true"
			i18n-label="adm:label-percentual-desconto-boleto-pag-antecipado"
			id="input-percentual-desconto-boleto-pag-antecipado"
			label="Desconto em pagamento antecipado no boleto (%)"
			max="100"
			maxlength="5"></pacto-cat-form-input-number>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-parcela-pela-operado"
		label="Parcela pela operadora"
		pactoTabVertical="parcela-operadora">
		<ng-container>
			<pacto-cat-checkbox
				[control]="form.get('parcelamentoOperadora')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-permite-parcelamento-operadora"
				id="check-permite-parcelamento-operadora"
				label="Permite parcelamento pela operadora"></pacto-cat-checkbox>
			<pacto-cat-checkbox
				*ngIf="form.get('parcelamentoOperadora').value"
				[control]="form.get('parcelamentoOperadoraDuracao')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-permite-parcelamento-operadora-duracao"
				id="check-permite-parcelamento-operadora-duracao"
				label="Número de vezes de acordo com a duração do contrato/plano"></pacto-cat-checkbox>
			<pacto-cat-form-input-number
				*ngIf="form.get('parcelamentoOperadora').value"
				[formControl]="form.get('maximoVezesParcelar')"
				label="Número máximo de parcelas"></pacto-cat-form-input-number>
		</ng-container>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cobrar-adesao-separada"
		label="Cobrar matrícula separada"
		pactoTabVertical="cobrar-matricula-separada">
		<pacto-cat-checkbox
			[control]="form.get('cobrarAdesaoSeparada')"
			i18n-label="@@adm:label-permite-cobrar-adesao-separada"
			id="check-permite-cobrar-adesao-separada"
			label="Permite cobrar matrícula separada"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarAdesao')"
			[items]="parcelas"
			i18n-label="@@adm:label-parcelar-matriculas"
			id="select-parcelar-matriculas"
			label="Parcelar matrículas"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cobrar-produtos-separado"
		label="Cobrar produtos separado"
		pactoTabVertical="cobrar-produtos-separado">
		<pacto-cat-checkbox
			[control]="form.get('cobrarProdutoSeparado')"
			i18n-label="@@adm:label-permite-cobrar-produto-separado"
			id="check-permite-cobrar-produto-separado"
			label="Permite cobrar produtos separados"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarProduto')"
			[items]="parcelas"
			i18n-label="@@adm:label-parcelar-produtos"
			id="select-parcelar-produtos"
			label="Parcelar produtos"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		*ngIf="!form.get('site').value"
		i18n-label="@@adm:tab-label-desconto"
		label="Desconto"
		pactoTabVertical="desconto">
		<pacto-cat-form-select-filter
			[addEmptyOption]="true"
			[control]="form.get('descontoAntecipado')"
			[endpointUrl]="admRestService.buildFullUrlPlano('desconto')"
			[paramBuilder]="descontoSelectBuilder"
			i18n-label="@@adm:label-desconto-antecipado"
			id="select-desconto-antecipado"
			idKey="codigo"
			label="Desconto em renovação antecipada"
			labelKey="descricao"></pacto-cat-form-select-filter>
		<pacto-cat-checkbox
			[control]="form.get('aceitaDescontoExtra')"
			i18n-label="@@adm:label-aceita-desconto-extra"
			id="check-aceita-desconto-extra"
			label="Aceita dar desconto extra"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('aceitaDescontoPorPlano')"
			i18n-label="@@adm:label-aceita-desconto-plano"
			id="check-aceita-desconto-plano"
			label="Aceita dar desconto por plano"></pacto-cat-checkbox>
		<pacto-cat-table-editable
			*ngIf="form.get('aceitaDescontoPorPlano').value"
			(confirm)="confirmDescontoDuracao($event)"
			[isEditable]="true"
			[showDelete]="false"
			[table]="tableDescontoDuracao"
			class="table-desconto-plano-duracao"
			idSuffix="desconto-plano-duracao"></pacto-cat-table-editable>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-comissao"
		label="Comissão"
		pactoTabVertical="comissao">
		<pacto-cat-checkbox
			[control]="form.get('comissao')"
			i18n-label="@adm:label-gerar-comissao"
			id="check-comissao"
			label="Gerar comissão"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		*ngIf="planoCommonsService.isPertenceRedeEmpresa"
		i18n-label="@@adm:tab-label-plano-vip"
		label="Plano Vip"
		pactoTabVertical="plano-vip">
		<pacto-cat-checkbox
			[control]="form.get('permitirAcessoRedeEmpresa')"
			i18n-label="@@adm:label-permitir-acesso-rede-empresa"
			id="check-permitir-acesso-rede-empresa"
			label="Permitir acesso as empresas da rede"></pacto-cat-checkbox>

		<ng-container *ngIf="form.get('permitirAcessoRedeEmpresa').value">
			<pacto-cat-checkbox
				id="check-especificar-empresas-acesso-rede"
				[control]="form.get('acessoRedeEmpresasEspecificas')"
				i18n-label="@@adm:label-especificar-empresas-acesso-rede"
				label="Especificar empresas"></pacto-cat-checkbox>
			<adm-plano-empresa-rede-acesso
				[empresasRede]="plano.empresasRede"
				(empresasRedeUpdate)="empresasRedeUpdate($event)"
				*ngIf="
					form.get('acessoRedeEmpresasEspecificas').value
				"></adm-plano-empresa-rede-acesso>
		</ng-container>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-restricao-recompra"
		label="Restrição de venda"
		pactoTabVertical="restricao-recompra">
		<pacto-cat-checkbox
			[control]="form.get('bloquearRecompra')"
			i18n-label="@@adm:label-bloquear-recompra"
			id="check-bloquear-recompra"
			label="Venda apenas para visitante"></pacto-cat-checkbox>

		<pacto-cat-form-datepicker
			*ngIf="
				plano.tipoPlano !== 'PLANO_PERSONAL' &&
				plano.tipoPlano !== 'PLANO_CREDITO' &&
				plano.tipoPlano !== 'PLANO_AVANCADO'
			"
			[control]="form.get('contratosEncerramDia')"
			i18n-label="@@adm:label--encerram-dia"
			id="datepicker-encerram-dia"
			label="Contratos encerram no dia"></pacto-cat-form-datepicker>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-observacoes"
		label="Observações"
		pactoTabVertical="observacoes">
		<div class="input-nome">Observação 1</div>
		<input
			[formControl]="form.get('observacao1')"
			class="input-text"
			type="text" />

		<div class="input-nome">Observação 2</div>
		<input
			[formControl]="form.get('observacao2')"
			class="input-text"
			type="text" />
	</ng-template>
</pacto-cat-tabs-vertical>

<ng-template #columnNumeroMeses>
	<span i18n="@@adm:pc-column-duracao-contrato">Duração do contrato</span>
</ng-template>

<ng-template #columnCarencia>
	<span i18n="@@adm:column-carencia">Dias de férias</span>
</ng-template>

<ng-template #columnValorMaximoDescontoPlano>
	<span i18n="@@adm:column-valor-maximo-desconto-plano">
		Desconto máximo por valor
	</span>
</ng-template>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:por-semana" xingling="por-semana">por semana</span>
</pacto-traducoes-xingling>
