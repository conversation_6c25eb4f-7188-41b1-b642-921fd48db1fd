<div class="modal-horarios-content">
	<!--CHOICES-->
	<div class="choiceHorario row">
		<label i18n="@@produtos:horario:repetir">
			Repetir apenas nestes dias da semana
		</label>
		<pacto-cat-choice [formControl]="form.get('diasSemana')" multi="true">
			<!--DOMINGO-->
			<pacto-cat-choice-option [value]="'DO'" class="optionHorario">
				D
			</pacto-cat-choice-option>
			<!--SEGUNDA-->
			<pacto-cat-choice-option [value]="'SG'" class="optionHorario">
				S
			</pacto-cat-choice-option>
			<!--TERÇA-->
			<pacto-cat-choice-option [value]="'TE'" class="optionHorario">
				T
			</pacto-cat-choice-option>
			<!--QUARTA-->
			<pacto-cat-choice-option [value]="'QA'" class="optionHorario">
				Q
			</pacto-cat-choice-option>
			<!--QUINTA-->
			<pacto-cat-choice-option [value]="'QI'" class="optionHorario">
				Q
			</pacto-cat-choice-option>
			<!--SEXTA-->
			<pacto-cat-choice-option [value]="'SX'" class="optionHorario">
				S
			</pacto-cat-choice-option>
			<!--SÁBADO-->
			<pacto-cat-choice-option [value]="'SB'" class="optionHorario">
				S
			</pacto-cat-choice-option>
		</pacto-cat-choice>
	</div>

	<div class="inputHorarioMod row">
		<div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
			<pacto-cat-select-filter
				[control]="form.get('horarioInicial')"
				[options]="getHorarioInicial"
				[paramBuilder]=""
				i18n-label="@@planos:horarios:horaInicialLabel"
				id="add-horario-select-hora-inicial"
				label="Horário inicial"></pacto-cat-select-filter>
		</div>

		<div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
			<pacto-cat-select-filter
				[control]="form.get('horarioFinal')"
				[options]="getHorarioAux"
				[paramBuilder]=""
				i18n-label="@@planos:horarios:horaFinalLabel"
				id="add-horario-select-hora-final"
				label="Horário final"></pacto-cat-select-filter>
		</div>
	</div>

	<div class="pct-row">
		<div
			(click)="addHorario()"
			class="add-horario cor-azulim05"
			id="add-horario-btn-add-hora">
			<i class="pct pct-plus-circle"></i>
			<span i18n="@@nps-adicionar">Adicionar</span>
		</div>
	</div>

	<pacto-relatorio
		#tableHorario
		(iconClick)="iconClick($event)"
		(pageChangeEvent)="changePageHorarioConfig($event)"
		(pageSizeChange)="changePageSizeHorarioConfig($event)"
		(rowClick)="editHorario($event)"
		[itensPerPage]="itemsPerPage"
		[showShare]="false"
		[table]="table"
		actionTitulo="Opções"
		class="table-horario"
		i18n-actionTitulo="@@horario:column-opcoes"></pacto-relatorio>

	<div class="bttnActionMod row">
		<pacto-cat-button
			(click)="cancel()"
			[full]="true"
			class="cancelModHorario"
			i18n-label="@@adm:planos:horarioMod"
			id="add-horario-btn-cancelar"
			label="Cancelar"
			style="margin-right: 10px"
			type="DARK"></pacto-cat-button>
		<pacto-cat-button
			(click)="salvarHorario()"
			[full]="true"
			class="saveModHorario"
			i18n-label="@@adm:planos:horarioModSave"
			id="add-horario-btn-salvar"
			label="Salvar"
			style="margin-right: 10px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>
<pacto-traducoes-xingling #traducoes>
	<span i18n="@@planos:horario:horaInicialValida" xingling="HORAINICIAL">
		Informe um horário inicial válido.
	</span>
	<span i18n="@@planos:horario:horaFinalValido" xingling="HORAFINAL">
		Informe um horário final válido.
	</span>
	<span i18n="@@planos:horario:diasSemanaValidaMod" xingling="DIAS_SEMANA">
		Selecione ao menos um dia na semana
	</span>
	<span i18n="@@planos:horario:validaMaior" xingling="VALIDA_MAIOR">
		Horário final não pode ser menor ou igual que o horário inicial
	</span>

	<span i18n="@@planos:horario:horario-duplicado" xingling="horario-duplicado">
		Já existe um item com os horários informados!
	</span>
	<span i18n="@@planos:horario:horario-in-range" xingling="horario-in-range">
		Exclua este horário e adicione um novo para prosseguir.
	</span>
	<span i18n="@@planos:horario:sem-horario" xingling="sem-horario">
		Não foi adicionado nenhum horário!
	</span>
</pacto-traducoes-xingling>

<ng-template #columnHorarioInicial>
	<span i18n="@@horario:column-horario-inicial">Horário Inicial</span>
</ng-template>

<ng-template #columnHorarioFinal>
	<span i18n="@@horario:column-horario-final">Horário Final</span>
</ng-template>
