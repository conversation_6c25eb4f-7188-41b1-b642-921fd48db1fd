import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ActivatedRoute } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { MaskService } from "ngx-mask";
import { HorarioDisponibilidadeSimple } from "plano-api";

@Component({
	selector: "adm-add-horario",
	templateUrl: "./add-horario.component.html",
	styleUrls: ["./add-horario.component.scss"],
	encapsulation: ViewEncapsulation.None,
	providers: [MaskService],
})
export class AddHorarioComponent implements OnInit, AfterViewInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("columnHorarioInicial", { static: false })
	columnHorarioInicial: TemplateRef<any>;
	@ViewChild("columnHorarioFinal", { static: false })
	columnHorarioFinal: TemplateRef<any>;
	@ViewChild("tableHorario", { static: false })
	tableHorario: RelatorioComponent;

	@Input() horarioDisponibilidades: Array<HorarioDisponibilidadeSimple> =
		new Array<HorarioDisponibilidadeSimple>();
	@Input() horarioDisponibilidadeEditing: HorarioDisponibilidadeSimple =
		new HorarioDisponibilidadeSimple();
	@Input() horarioDisponibilidadeIndexEditing;

	itemsPerPage = [
		{ id: 3, label: "3" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];

	table: PactoDataGridConfig;
	horarios: Array<{ horarioInicial: string; horarioFinal: string }> =
		new Array<{ horarioInicial: string; horarioFinal: string }>();
	horariosData: {
		content: Array<{ horarioInicial: string; horarioFinal: string }>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<{ horarioInicial: string; horarioFinal: string }>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 3;

	edit = false;
	horarioEditingIndex;
	form: FormGroup;
	getHorarioInicial: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>(
		{ id: "0000", label: "00:00" },
		{ id: "0030", label: "00:30" },
		{ id: "0100", label: "01:00" },
		{ id: "0130", label: "01:30" },
		{ id: "0200", label: "02:00" },
		{ id: "0230", label: "02:30" },
		{ id: "0300", label: "03:00" },
		{ id: "0330", label: "03:30" },
		{ id: "0400", label: "04:00" },
		{ id: "0430", label: "04:30" },
		{ id: "0500", label: "05:00" },
		{ id: "0530", label: "05:30" },
		{ id: "0600", label: "06:00" },
		{ id: "0630", label: "06:30" },
		{ id: "0700", label: "07:00" },
		{ id: "0730", label: "07:30" },
		{ id: "0800", label: "08:00" },
		{ id: "0830", label: "08:30" },
		{ id: "0900", label: "09:00" },
		{ id: "0930", label: "09:30" },
		{ id: "1000", label: "10:00" },
		{ id: "1030", label: "10:30" },
		{ id: "1100", label: "11:00" },
		{ id: "1130", label: "11:30" },
		{ id: "1200", label: "12:00" },
		{ id: "1230", label: "12:30" },
		{ id: "1300", label: "13:00" },
		{ id: "1330", label: "13:30" },
		{ id: "1400", label: "14:00" },
		{ id: "1430", label: "14:30" },
		{ id: "1500", label: "15:00" },
		{ id: "1530", label: "15:30" },
		{ id: "1600", label: "16:00" },
		{ id: "1630", label: "16:30" },
		{ id: "1700", label: "17:00" },
		{ id: "1730", label: "17:30" },
		{ id: "1800", label: "18:00" },
		{ id: "1830", label: "18:30" },
		{ id: "1900", label: "19:00" },
		{ id: "1930", label: "19:30" },
		{ id: "2000", label: "20:00" },
		{ id: "2030", label: "20:30" },
		{ id: "2100", label: "21:00" },
		{ id: "2130", label: "21:30" },
		{ id: "2200", label: "22:00" },
		{ id: "2230", label: "22:30" },
		{ id: "2300", label: "23:00" },
		{ id: "2330", label: "23:30" }
	);
	getHorarioFinal: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>(
		{ id: "0030", label: "00:30" },
		{ id: "0100", label: "01:00" },
		{ id: "0130", label: "01:30" },
		{ id: "0200", label: "02:00" },
		{ id: "0230", label: "02:30" },
		{ id: "0300", label: "03:00" },
		{ id: "0330", label: "03:30" },
		{ id: "0400", label: "04:00" },
		{ id: "0430", label: "04:30" },
		{ id: "0500", label: "05:00" },
		{ id: "0530", label: "05:30" },
		{ id: "0600", label: "06:00" },
		{ id: "0630", label: "06:30" },
		{ id: "0700", label: "07:00" },
		{ id: "0730", label: "07:30" },
		{ id: "0800", label: "08:00" },
		{ id: "0830", label: "08:30" },
		{ id: "0900", label: "09:00" },
		{ id: "0930", label: "09:30" },
		{ id: "1000", label: "10:00" },
		{ id: "1030", label: "10:30" },
		{ id: "1100", label: "11:00" },
		{ id: "1130", label: "11:30" },
		{ id: "1200", label: "12:00" },
		{ id: "1230", label: "12:30" },
		{ id: "1300", label: "13:00" },
		{ id: "1330", label: "13:30" },
		{ id: "1400", label: "14:00" },
		{ id: "1430", label: "14:30" },
		{ id: "1500", label: "15:00" },
		{ id: "1530", label: "15:30" },
		{ id: "1600", label: "16:00" },
		{ id: "1630", label: "16:30" },
		{ id: "1700", label: "17:00" },
		{ id: "1730", label: "17:30" },
		{ id: "1800", label: "18:00" },
		{ id: "1830", label: "18:30" },
		{ id: "1900", label: "19:00" },
		{ id: "1930", label: "19:30" },
		{ id: "2000", label: "20:00" },
		{ id: "2030", label: "20:30" },
		{ id: "2100", label: "21:00" },
		{ id: "2130", label: "21:30" },
		{ id: "2200", label: "22:00" },
		{ id: "2230", label: "22:30" },
		{ id: "2300", label: "23:00" },
		{ id: "2330", label: "23:30" },
		{ id: "0000", label: "00:00" }
	);
	getHorarioAux = new Array(...this.getHorarioFinal);
	id;

	constructor(
		public dialog: NgbActiveModal,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		public maskService: MaskService
	) {}

	ngOnInit() {
		this.createFormHorario();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		if (this.horarioDisponibilidadeEditing) {
			if (this.horarioDisponibilidadeEditing.diaSemana) {
				this.form
					.get("diasSemana")
					.value.push(this.horarioDisponibilidadeEditing.diaSemana);
			}
			if (
				this.horarioDisponibilidadeEditing.horarios &&
				this.horarioDisponibilidadeEditing.horarios.length > 0
			) {
				this.horarioDisponibilidadeEditing.horarios.forEach((h) => {
					const horario: any = {};
					Object.keys(h).forEach((k) => {
						horario[k] = h[k];
					});
					this.horarios.push(horario);
				});
				this.createHorarioConfigPageObject();
			}
		}
	}

	ngAfterViewInit() {
		this.initTableHorarios();
		this.form.get("horarioInicial").valueChanges.subscribe((v) => {
			if (v) {
				const index = this.getHorarioInicial.findIndex((v2) => v2.id === v.id);

				if (index !== -1 && this.form.get("horarioFinal").value) {
					const index2 = this.getHorarioFinal.findIndex(
						(v2) => v2.id === this.form.get("horarioFinal").value.id
					);
					if (
						index2 <= index &&
						this.form.get("horarioFinal").value.id !== "0000"
					) {
						this.form.get("horarioFinal").setValue(null);
					}
				}

				Object.assign(this.getHorarioAux, this.getHorarioFinal);
				if (index !== -1) {
					this.getHorarioAux.splice(0, index);
				}
				this.cd.detectChanges();
			}
		});
	}

	initTableHorarios() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: () => this.horariosData,
			pagination: true,
			columns: [
				{
					nome: "horarioInicial",
					titulo: this.columnHorarioInicial,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.maskService.applyMask(v, "00:00"),
				},
				{
					nome: "horarioFinal",
					titulo: this.columnHorarioFinal,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.maskService.applyMask(v, "00:00"),
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "pct pct-edit-3 cor-azulim-pri",
					tooltipText: "",
				},
				{
					nome: "delete",
					iconClass: "pct pct-trash-2 cor-hellboy-pri",
					tooltipText: "",
				},
			],
		});
	}

	addHorario() {
		if (!this.form.get("horarioInicial").value) {
			this.notificationService.error(this.traducoes.getLabel("HORAINICIAL"));
			return;
		} else if (!this.form.get("horarioFinal").value) {
			this.notificationService.error(this.traducoes.getLabel("HORAFINAL"));
			return;
		}
		const diasSemana = this.form.get("diasSemana").value;
		if (!diasSemana || diasSemana.length === 0) {
			this.notificationService.error(this.traducoes.getLabel("DIAS_SEMANA"));
			return;
		}
		const horarioInicial = this.form.get("horarioInicial").value.id;
		const horarioFinal = this.form.get("horarioFinal").value.id;
		if (
			this.horarios.some(
				(h, index) =>
					h.horarioInicial === horarioInicial &&
					h.horarioFinal === horarioFinal &&
					index !== this.horarioEditingIndex
			)
		) {
			this.notificationService.error(
				this.traducoes.getLabel("horario-duplicado")
			);
			return;
		}

		const dataInicial = this.convertDate(horarioInicial);
		const dataFinal = this.convertDate(horarioFinal);

		if (
			dataInicial.getTime() >= dataFinal.getTime() &&
			horarioFinal !== "0000"
		) {
			this.notificationService.error(this.traducoes.getLabel("VALIDA_MAIOR"));
			return;
		}

		const indexesObject = this.convertHours(horarioInicial, horarioFinal);

		if (
			indexesObject &&
			indexesObject.indexHourBeforeInitialAfterFinal.length === 0 &&
			indexesObject.indexHourAfterAndBeforeFinal.length === 0 &&
			indexesObject.indexHourAfterAndBeforeInit.length === 0 &&
			indexesObject.indexHoursInRangeOfExisting.length === 0
		) {
			if (this.horarioEditingIndex !== undefined) {
				this.horarios[this.horarioEditingIndex].horarioInicial = horarioInicial;
				this.horarios[this.horarioEditingIndex].horarioFinal = horarioFinal;
			}
		}

		if (!this.edit) {
			this.horarioDisponibilidades.forEach((hd) => {
				if (diasSemana.includes(hd.diaSemana)) {
					const indexHourBeforeInitialAfterFinal =
						this.getHorariosIndexesBeforeInitialAfterFinal(
							horarioInicial,
							horarioFinal,
							hd.horarios
						);
					const indexHourAfterAndBeforeFinal =
						this.getHorariosIndexesBeforeAndAfterFinal(
							horarioInicial,
							horarioFinal,
							hd.horarios
						);
					const indexHourAfterAndBeforeInit =
						this.getHorariosIndexesBeforeAndAfterInit(
							horarioInicial,
							horarioFinal,
							hd.horarios
						);
					if (
						indexHourBeforeInitialAfterFinal.length === 0 &&
						indexHourAfterAndBeforeFinal.length === 0 &&
						indexHourAfterAndBeforeInit.length === 0
					) {
						hd.horarios.push({
							horarioInicial,
							horarioFinal,
						});
					}
				}
			});
		}

		this.form.get("horarioInicial").reset();
		this.form.get("horarioFinal").reset();
		this.changePageHorarioConfig(this.page);
		this.horarioEditingIndex = undefined;
	}

	convertHours(
		horarioInicial,
		horarioFinal,
		index?,
		showNotifications = true,
		horarios = this.horarios
	) {
		if (index !== undefined) {
			this.horarioEditingIndex = index;
		}

		let indexHoursInRangeOfExisting = [];
		if (index === undefined) {
			indexHoursInRangeOfExisting = this.getHorariosIndexesInRangeOfExists(
				horarioInicial,
				horarioFinal
			);
			if (
				indexHoursInRangeOfExisting.length > 0 &&
				horarioFinal !== "0000" &&
				showNotifications
			) {
				this.notificationService.warning(
					this.traducoes.getLabel("horario-in-range")
				);
				return;
			}
		}

		this.ajustarHorariosConcomitantesComAFaixaHorariaInformada(
			horarioInicial,
			horarioFinal,
			horarios
		);

		return {
			indexHourBeforeInitialAfterFinal:
				this.getHorariosIndexesBeforeInitialAfterFinal(
					horarioInicial,
					horarioFinal,
					horarios
				),
			indexHourAfterAndBeforeFinal: this.getHorariosIndexesBeforeAndAfterFinal(
				horarioInicial,
				horarioFinal,
				horarios
			),
			indexHourAfterAndBeforeInit: this.getHorariosIndexesBeforeAndAfterInit(
				horarioInicial,
				horarioFinal,
				horarios
			),
			indexHoursInRangeOfExisting,
		};
	}

	/**
	 * Encontrar os horários em que a hora inicial do informado é anterior a hora inicial de um existente
	 * e a hora final é posterior a hora final de um existente.
	 * Exemplo: Horário Existente -> 06:00 - 09:00
	 *          Horário Informado -> 03:00 - 10:00
	 *          =                 -> 03:00 - 10:00
	 */
	getHorariosIndexesBeforeInitialAfterFinal(
		horarioInicial,
		horarioFinal,
		horarios = this.horarios
	) {
		const indexHourBeforeInitAfterFinal = [];
		horarios.forEach((h, index) => {
			if (index !== this.horarioEditingIndex) {
				const dtInicial = this.convertDate(h.horarioInicial);
				const dtFinal = this.convertDate(h.horarioFinal);

				if (
					this.convertDate(horarioInicial).getTime() <= dtInicial.getTime() &&
					this.convertDate(horarioFinal).getTime() >= dtFinal.getTime()
				) {
					indexHourBeforeInitAfterFinal.push(index);
				}
			}
		});
		indexHourBeforeInitAfterFinal.forEach((i, idx) => {
			if (idx === 0) {
				horarios[i].horarioInicial = horarioInicial;
				horarios[i].horarioFinal = horarioFinal;
			} else {
				horarios.splice(i, 1);
			}
		});
		return indexHourBeforeInitAfterFinal;
	}

	/**
	 * Esse metodo encontra e ajusta os horários que estão concomitando com a faixa horaria informada.
	 * A ideia é unificar as faixas de horários da lista.
	 * Exemplo1: Horários Existentes -> 06:00 - 09:00 | 09:30 10:30
	 *          Horário Informado -> 07:00 - 11:00
	 *          =                 -> 06:00 - 11:00
	 *
	 * Exemplo2: Horários Existentes -> 06:00 - 06:30 | 08:00 - 09:00 | 10:00 - 11:00
	 *          Horário Informado -> 05:00 - 11:00
	 *          =                 -> 05:00 - 11:00
	 */
	ajustarHorariosConcomitantesComAFaixaHorariaInformada(
		horarioInicial,
		horarioFinal,
		horarios = this.horarios
	) {
		const indicesInicioFimEntreAFaixa = [];
		const indicesApenasFimForaDaFaixa = [];
		const indicesApenasInicioForaDaFaixa = [];
		const indicesInicioEFimForaDaFaixa = [];

		horarios.forEach((h, index) => {
			if (index !== this.horarioEditingIndex) {
				const dtInicial = this.convertDate(h.horarioInicial);
				const dtFinal = this.convertDate(h.horarioFinal);

				const dtHorarioInicial = this.convertDate(horarioInicial);
				const dtHorarioFinal = this.convertDate(horarioFinal);

				if (
					dtInicial.getTime() >= dtHorarioInicial.getTime() &&
					dtFinal.getTime() <= dtHorarioFinal.getTime()
				) {
					indicesInicioFimEntreAFaixa.push(index);
				} else if (
					dtInicial.getTime() >= dtHorarioInicial.getTime() &&
					dtInicial.getTime() <= dtHorarioFinal.getTime() &&
					(dtFinal.getTime() < dtHorarioInicial.getTime() ||
						dtFinal.getTime() > dtHorarioFinal.getTime())
				) {
					indicesApenasFimForaDaFaixa.push(index);
				} else if (
					(dtInicial.getTime() < dtHorarioInicial.getTime() ||
						dtInicial.getTime() > dtHorarioFinal.getTime()) &&
					dtFinal.getTime() >= dtHorarioInicial.getTime() &&
					dtFinal.getTime() <= dtHorarioFinal.getTime()
				) {
					indicesApenasInicioForaDaFaixa.push(index);
				} else {
					indicesInicioEFimForaDaFaixa.push(index);
				}
			}
		});

		const novosHorarios = [];
		// ajustar horarioInicio e Fim de acordo com o inicio e fim dos horarios concomitantes
		indicesApenasFimForaDaFaixa.forEach((i) => {
			if (
				this.convertDate(horarios[i].horarioFinal).getTime() >
				this.convertDate(horarioFinal).getTime()
			) {
				horarioFinal = horarios[i].horarioFinal;
			}
		});
		indicesApenasInicioForaDaFaixa.forEach((i) => {
			if (
				this.convertDate(horarios[i].horarioInicial) <
				this.convertDate(horarioInicial)
			) {
				horarioInicial = horarios[i].horarioInicial;
			}
		});

		const indice =
			this.horarioEditingIndex !== undefined
				? this.horarioEditingIndex
				: novosHorarios.length;

		// adicionar os demais horarios que estão fora da faixa de horario informada.
		indicesInicioEFimForaDaFaixa.forEach((i) => {
			novosHorarios.push(horarios[i]);
		});

		if (novosHorarios.length > indice) {
			novosHorarios.splice(indice, 0, { horarioInicial, horarioFinal });
		} else {
			novosHorarios.push({ horarioInicial, horarioFinal });
		}

		this.horarios = novosHorarios;
	}

	/**
	 * Encontrar os horários em que a hora final do informado é anterior a hora final de um existente
	 * e a hora inicial é posterior a hora inicial de um existente.
	 * Exemplo: Horário Existente -> 06:00 - 09:00
	 *          Horário Informado -> 07:00 - 10:00
	 *          =                 -> 06:00 - 10:00
	 */
	getHorariosIndexesBeforeAndAfterFinal(
		horarioInicial,
		horarioFinal,
		horarios = this.horarios
	) {
		const indexes = [];

		horarios.forEach((h, index) => {
			if (index !== this.horarioEditingIndex) {
				const dtInicial = this.convertDate(h.horarioInicial);
				const dtFinal = this.convertDate(h.horarioFinal);

				if (
					this.convertDate(horarioInicial).getTime() >= dtInicial.getTime() &&
					this.convertDate(horarioInicial).getTime() <= dtFinal.getTime() &&
					this.convertDate(horarioFinal).getTime() >= dtFinal.getTime()
					// || (this.getDiffBetweenDatesInMinutes(this.convertDate(horarioInicial), dtFinal) === 30
					//     && this.convertDate(horarioInicial).getTime() > dtFinal.getTime())
				) {
					indexes.push(index);
				}
			}
		});
		indexes.forEach((i, idx) => {
			if (idx === 0) {
				horarios[i].horarioFinal = horarioFinal;
			} else {
				horarios.splice(i, 1);
			}
		});
		return indexes;
	}

	/**
	 * Encontrar os horários em que a hora inicial do informado é maior que a existente e menor que a hora
	 * final existente e a hora final informada é maior que a hora final existente
	 * Exemplo: Horário Existente -> 06:00 - 09:00
	 *          Horário Informado -> 07:00 - 12:00
	 *          =                 -> 06:00 - 12:00
	 */
	getHorariosIndexesBeforeAndAfterInit(
		horarioInicial,
		horarioFinal,
		horarios = this.horarios
	) {
		const indexes = [];

		horarios.forEach((h, index) => {
			if (index !== this.horarioEditingIndex) {
				const dtInicial = this.convertDate(h.horarioInicial);
				const dtFinal = this.convertDate(h.horarioFinal);

				if (
					this.convertDate(horarioInicial).getTime() <= dtInicial.getTime() &&
					this.convertDate(horarioFinal).getTime() <= dtFinal.getTime() &&
					this.convertDate(horarioFinal).getTime() >= dtInicial.getTime()
					// || (this.getDiffBetweenDatesInMinutes(this.convertDate(horarioFinal), dtInicial) === 30
					//     && this.convertDate(horarioFinal).getTime() < dtInicial.getTime())
				) {
					indexes.push(index);
				}
			}
		});

		indexes.forEach((i, idx) => {
			if (idx === 0) {
				horarios[i].horarioInicial = horarioInicial;
			} else {
				horarios.splice(i, 1);
			}
		});
		return indexes;
	}

	/**
	 * Encontrar os horários em que a as hora inicial e final informada estão no mesmo range.
	 * Exemplo: Horário Existente -> 06:00 - 09:00
	 *          Horário Informado -> 08:00 - 08:30
	 *          =                 -> 06:00 - 09:00
	 */
	getHorariosIndexesInRangeOfExists(
		horarioInicial,
		horarioFinal,
		horarios = this.horarios
	) {
		const indexes = [];

		horarios.forEach((h, index) => {
			if (index !== this.horarioEditingIndex) {
				const dtInicial = this.convertDate(h.horarioInicial);
				const dtFinal = this.convertDate(h.horarioFinal);

				if (
					this.convertDate(horarioInicial).getTime() >= dtInicial.getTime() &&
					this.convertDate(horarioFinal).getTime() <= dtFinal.getTime()
				) {
					indexes.push(index);
				}
			}
		});

		return indexes;
	}

	getDiffBetweenDatesInMinutes(dataInicial, dataFinal) {
		return Math.abs(
			Math.floor((dataInicial.getTime() - dataFinal.getTime()) / 1000 / 60)
		);
	}

	iconClick(event: { row: any; iconName: string; rowIndex: number }) {
		if (event.iconName === "edit") {
			event.row.rowIndex = event.rowIndex;
			this.editHorario(event.row);
		} else {
			this.deleteHorario(event.rowIndex);
		}
	}

	convertDate(horario: string) {
		const date = new Date();
		date.setHours(+horario.substring(0, 2));
		date.setMinutes(+horario.substring(2));
		date.setSeconds(0);
		date.setMilliseconds(0);

		return date;
	}

	editHorario(item: any) {
		this.horarioEditingIndex = item.rowIndex;
		this.form
			.get("horarioInicial")
			.setValue(
				this.getHorarioInicial.find((hi) => hi.id === item.horarioInicial)
			);
		this.form
			.get("horarioFinal")
			.setValue(this.getHorarioFinal.find((hi) => hi.id === item.horarioFinal));
	}

	deleteHorario(index: any) {
		this.horarios.splice(index, 1);
		this.createHorarioConfigPageObject();
	}

	changePageHorarioConfig(page = 1) {
		this.page = page;
		this.createHorarioConfigPageObject();
	}

	changePageSizeHorarioConfig(size) {
		this.size = size;
		this.createHorarioConfigPageObject();
	}

	createHorarioConfigPageObject() {
		if (this.horarios) {
			this.sortHorarios();
			this.horariosData.totalElements = this.horarios.length;
			this.horariosData.size = this.size;
			this.horariosData.totalPages = Math.ceil(
				+(this.horariosData.totalElements / this.horariosData.size)
			);
			this.horariosData.first = this.page === 0 || this.page === 1;
			this.horariosData.last = this.page === this.horariosData.totalPages;
			this.horariosData.content = this.horarios.slice(
				this.size * this.page - this.size,
				this.size * this.page
			);
			if (this.tableHorario) {
				this.tableHorario.reloadData();
			}
		}
	}

	sortHorarios() {
		this.horarios = this.horarios.sort((a, b) => {
			if (
				this.convertDate(a.horarioInicial).getTime() >
				this.convertDate(b.horarioInicial).getTime()
			) {
				return 1;
			} else if (
				this.convertDate(a.horarioInicial).getTime() <
				this.convertDate(b.horarioInicial).getTime()
			) {
				return -1;
			} else {
				return 0;
			}
		});
	}

	createFormHorario() {
		this.form = new FormGroup({
			diasSemana: new FormControl([]),
			horarioInicial: new FormControl(),
			horarioFinal: new FormControl(),
		});
		if (this.edit) {
			this.form.get("diasSemana").disable();
		}
	}

	cancel() {
		this.dialog.dismiss();
	}

	salvarHorario() {
		if (this.horarios.length === 0 && !this.edit) {
			this.notificationService.error(this.traducoes.getLabel("sem-horario"));
			return;
		}

		if (!this.edit) {
			this.dialog.close({
				horarioDisponibilidades: this.horarioDisponibilidades,
				edit: this.edit,
			});
		} else {
			this.horarioDisponibilidadeEditing.horarios = this.horarios;
			this.dialog.close({
				horarioDisponibilidade: this.horarioDisponibilidadeEditing,
				horarioDisponibilidadeIndexEditing:
					this.horarioDisponibilidadeIndexEditing,
				edit: this.edit,
			});
		}
	}
}
