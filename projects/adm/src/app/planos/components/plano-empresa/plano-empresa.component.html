<pacto-cat-card-plain>
	<div style="padding-left: 29px; margin-bottom: 15px">
		<pacto-cat-checkbox
			[control]="permitirAcessoSomenteNaEmpresaVendeuContratoControl"
			i18n-label="@@adm:label-permitir-acesso-somente-empresa-vender-contrato"
			label="Permitir Acesso somente na empresa que vender o contrato"></pacto-cat-checkbox>
	</div>
	<div class="table-wrapper pacto-shadow">
		<div class="table-wrapper pacto-shadow">
			<pacto-cat-table-editable
				#tableComponent
				(confirm)="confirm($event)"
				(edit)="edit($event)"
				(isEditingOrAddingItem)="isEditingOrAdding($event)"
				[isEditable]="true"
				[showAddRow]="false"
				[showDelete]="false"
				[table]="table"></pacto-cat-table-editable>
		</div>
	</div>
</pacto-cat-card-plain>

<ng-template #columnVenda>
	<span i18n="@@adm:column-permitir-venda">Permitir venda</span>
</ng-template>

<ng-template #columnAcesso>
	<span i18n="@@adm:column-permitir-acesso">Permitir acesso</span>
</ng-template>

<ng-template #columnNome>
	<span i18n="@@adm:column-nome-empresa">Empresa</span>
</ng-template>

<ng-template #columnCidade>
	<span i18n="@@adm:column-cidade">Cidade</span>
</ng-template>

<ng-template #columnEstado>
	<span i18n="@@adm:column-estado">Estado</span>
</ng-template>

<ng-template #columnSetor>
	<span i18n="@@adm:column-setor">Setor</span>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:label-sim" xingling="label-sim">Sim</span>
	<span i18n="@@adm:label-nao" xingling="label-nao">Não</span>
	<span i18n="@@adm:dado-duplicado-table" xingling="dado-duplicado-table">
		Já existe um dado na tabela com estes valores.
	</span>
</pacto-traducoes-xingling>
