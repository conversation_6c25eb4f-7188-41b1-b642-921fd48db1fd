import {
	AfterViewInit,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PlanoEmpresaService } from "../../services/plano-empresa/plano-empresa.service";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import {
	Cidade,
	Empresa,
	Estado,
	Plano,
	PlanoEmpresa,
} from "../../plano.model";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-plano-empresa",
	templateUrl: "./plano-empresa.component.html",
	styleUrls: ["./plano-empresa.component.scss"],
})
export class PlanoEmpresaComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("tableComponent", { static: false })
	tableComponent: CatTableEditableComponent;
	@ViewChild("columnVenda", { static: false }) columnVenda: TemplateRef<any>;
	@ViewChild("columnAcesso", { static: false }) columnAcesso: TemplateRef<any>;
	@ViewChild("columnNome", { static: false }) columnNome: TemplateRef<any>;
	@ViewChild("columnCidade", { static: false }) columnCidade: TemplateRef<any>;
	@ViewChild("columnEstado", { static: false }) columnEstado: TemplateRef<any>;
	@ViewChild("columnSetor", { static: false }) columnSetor: TemplateRef<any>;
	table: PactoDataGridConfig;
	plano: Plano;
	permitirAcessoSomenteNaEmpresaVendeuContratoControl: FormControl;
	empresas: Array<{
		codigo: number;
		codigoEmpresa: number;
		cidade: string;
		estado: string;
		nome: string;
		setor: string;
		acesso: boolean;
		venda: boolean;
	}> = new Array<{
		codigo: number;
		codigoEmpresa: number;
		cidade: string;
		estado: string;
		nome: string;
		setor: string;
		acesso: boolean;
		venda: boolean;
	}>();

	constructor(
		private planoEmpresaService: PlanoEmpresaService,
		private planoStateService: PlanoStateService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.permitirAcessoSomenteNaEmpresaVendeuContratoControl = new FormControl(
			this.plano.permitirAcessoSomenteNaEmpresaVendeuContrato
		);
		this.permitirAcessoSomenteNaEmpresaVendeuContratoControl.valueChanges.subscribe(
			(value) => {
				this.plano = this.planoStateService.updatePlanoObj();
				this.plano.permitirAcessoSomenteNaEmpresaVendeuContrato = value;
				this.planoStateService.updateState(this.plano);
			}
		);
		this.initTable();
	}

	ngAfterViewInit() {
		this.planoEmpresaService
			.listByPlanoId(this.plano.codigo)
			.subscribe((response) => {
				this.plano = this.planoStateService.updatePlanoObj();
				const planoEmpresas = response.content;
				planoEmpresas.forEach((planoEmpresa) =>
					this.empresas.push({
						codigo: planoEmpresa.codigo,
						codigoEmpresa: planoEmpresa.empresa.codigo,
						nome: planoEmpresa.empresa.nome,
						cidade: planoEmpresa.empresa.cidade.nome,
						estado: planoEmpresa.empresa.estado.sigla,
						setor: planoEmpresa.empresa.setor,
						acesso: planoEmpresa.acesso,
						venda: planoEmpresa.venda,
					})
				);
				if (!this.plano.empresas) {
					this.plano.empresas = new Array<PlanoEmpresa>();
				}
				this.plano.empresas.forEach((planoEmpresa) => {
					const findedEmpresa = this.empresas.find(
						(emp) => emp.codigoEmpresa === planoEmpresa.empresa.codigo
					);
					if (findedEmpresa) {
						findedEmpresa.codigo = planoEmpresa.codigo;
						findedEmpresa.codigoEmpresa = planoEmpresa.empresa.codigo;
						findedEmpresa.nome = planoEmpresa.empresa.nome;
						findedEmpresa.cidade = planoEmpresa.empresa.cidade.nome;
						findedEmpresa.estado = planoEmpresa.empresa.estado.sigla;
						findedEmpresa.setor = planoEmpresa.empresa.setor;
						findedEmpresa.acesso = planoEmpresa.acesso;
						findedEmpresa.venda = planoEmpresa.venda;
					} else {
						this.empresas.push({
							codigo: planoEmpresa.codigo,
							codigoEmpresa: planoEmpresa.empresa.codigo,
							nome: planoEmpresa.empresa.nome,
							cidade: planoEmpresa.empresa.cidade.nome,
							estado: planoEmpresa.empresa.estado.sigla,
							setor: planoEmpresa.empresa.setor,
							acesso: planoEmpresa.acesso,
							venda: planoEmpresa.venda,
						});
					}
				});
				this.sortEmpreas();
				this.tableComponent.reloadData();
				this.convertEmpresasToPlanoEmpresa();
			});
	}

	sortEmpreas() {
		this.empresas.sort((a, b) => {
			if (a.nome < b.nome) {
				return -1;
			}
			if (a.nome > b.nome) {
				return 1;
			}
			return 0;
		});
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return { content: this.empresas };
			},
			formGroup: new FormGroup({
				venda: new FormControl(),
				acesso: new FormControl(),
			}),
			pagination: false,
			columns: [
				{
					nome: "venda",
					titulo: this.columnVenda,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					valueTransform: (v) =>
						v
							? this.traducao.getLabel("label-sim")
							: this.traducao.getLabel("label-nao"),
				},
				{
					nome: "acesso",
					titulo: this.columnAcesso,
					visible:
						!this.permitirAcessoSomenteNaEmpresaVendeuContratoControl.value,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					valueTransform: (v) =>
						v
							? this.traducao.getLabel("label-sim")
							: this.traducao.getLabel("label-nao"),
				},
				{
					nome: "nome",
					titulo: this.columnNome,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "cidade",
					titulo: this.columnCidade,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "estado",
					titulo: this.columnEstado,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "setor",
					titulo: this.columnSetor,
					visible: true,
					ordenavel: false,
				},
			],
		});
		this.permitirAcessoSomenteNaEmpresaVendeuContratoControl.valueChanges.subscribe(
			(value) => {
				this.initTable();
			}
		);
	}

	confirm(event: any) {
		this.convertEmpresasToPlanoEmpresa(event);
	}

	convertEmpresasToPlanoEmpresa(event?) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (!this.plano.empresas) {
			this.plano.empresas = new Array<PlanoEmpresa>();
		}
		let index;
		if (event) {
			this.plano.empresas.find((pe, i) => {
				if (
					(pe.codigo && pe.codigo === event.row.codigo) ||
					pe.empresa.codigo === event.row.codigoEmpresa
				) {
					index = i;
					return pe;
				}
			});
			if (index >= 0) {
				this.plano.empresas[index].acesso = event.row.acesso;
				this.plano.empresas[index].venda = event.row.venda;
			}
		} else {
			this.empresas.forEach((emp) => {
				const planoEmpresa = new PlanoEmpresa();
				planoEmpresa.codigo = emp.codigo;
				planoEmpresa.acesso = emp.acesso;
				planoEmpresa.venda = emp.venda;
				const empresa: Empresa = new Empresa();
				empresa.nome = emp.nome;
				empresa.codigo = emp.codigoEmpresa;
				const cidade = new Cidade();
				cidade.nome = emp.cidade;
				const estado = new Estado();
				estado.sigla = emp.estado;
				empresa.estado = estado;
				empresa.cidade = cidade;
				planoEmpresa.empresa = empresa;
				if (index === undefined) {
					this.plano.empresas.find((pe, i) => {
						if (
							(pe.codigo && pe.empresa.nome === emp.nome) ||
							pe.empresa.codigo === emp.codigoEmpresa
						) {
							index = i;
							return pe;
						}
					});
				}
				if (index >= 0) {
					this.plano.empresas[index] = planoEmpresa;
					index = undefined;
				} else {
					this.plano.empresas[index] = planoEmpresa;
					this.plano.empresas.push(planoEmpresa);
				}
			});
		}
		this.planoStateService.updateState(this.plano);
	}

	delete(event: any) {}

	edit(event: any) {}

	isEditingOrAdding(event: boolean) {}
}
