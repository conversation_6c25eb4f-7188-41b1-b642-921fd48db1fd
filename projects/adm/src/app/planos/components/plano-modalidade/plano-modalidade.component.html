<pacto-cat-table-editable
	id="table-modalidade"
	#table
	[showAddRow]="true"
	[isEditable]="true"
	[showEdit]="true"
	[showDelete]="true"
	[actionTitle]="'Ações'"
	[newLineTitle]="'Adicionar modalidade'"
	[table]="gridConfig"></pacto-cat-table-editable>
<ng-template #celulaAcoes let-row="item" let-index="index">
	<pacto-cat-button
		[id]="'btn-editar-add-vezes-semana-' + index"
		type="NO_BORDER"
		[icon]="
			isVezesSemana(row)
				? 'pct pct-edit cor-azulim05'
				: 'pct pct-plus-square cor-azulim05'
		"
		[label]="isVezesSemana(row) ? 'Editar' : 'Adicionar'"
		(click)="editVezesSemana(row)"></pacto-cat-button>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span xingling="tooltip-text">Editar Vezes Na Semana</span>
</pacto-traducoes-xingling>
