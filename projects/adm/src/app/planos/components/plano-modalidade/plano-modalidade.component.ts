import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	CatTableEditableComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalRef,
	PactoModalSize,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ClientDiscoveryService } from "sdk";
import { ModalVezesSemanaComponent } from "./modal-vezes-semana/modal-vezes-semana.component";
import { Modalidade, PlanoModalidadeVezesSemana } from "plano-api";
import { Plano } from "../../plano.model";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { Observable, of } from "rxjs";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-plano-modalidade",
	templateUrl: "./plano-modalidade.component.html",
	styleUrls: ["./plano-modalidade.component.scss"],
})
export class PlanoModalidadeComponent implements OnInit, AfterViewInit {
	@ViewChild("table", { static: false }) table: CatTableEditableComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("celulaAcoes", { static: true }) celulaAcoes: TemplateRef<any>;

	@Input() codigo: number;
	@Input() planoDTO: Plano;

	gridConfig: PactoDataGridConfig;
	vezesSemanaTableData: Map<number, PlanoModalidadeVezesSemana[]> = new Map();
	modalidadesSelecionadas: Modalidade[] = [];

	constructor(
		private discoveryService: ClientDiscoveryService,
		private modalService: DialogService,
		private planoState: PlanoStateService,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	modalidadeFormGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		modalidade: new FormControl(),
		nrVezesDescricao: new FormControl(),
		nrVezes: new FormControl(),
	});

	formasDeCalculo = [
		{ codigo: "VE", nome: "Valor" },
		{ codigo: "PD", nome: "Percentual" },
	];

	tiposOperacao = [
		{ codigo: "AC", nome: "Acrescimo" },
		{ codigo: "EX", nome: "Exatamente" },
		{ codigo: "RE", nome: "Redução" },
	];

	ngOnInit() {
		this.initGridConfig();
		this.cd.detectChanges();
	}

	ngAfterViewInit(): void {
		this.initState();
		if (this.planoDTO.modalidades) {
			this.modalidadesSelecionadas = this.planoDTO.modalidades.map(
				(plMod) => plMod.modalidade
			);
			this.gridConfig.dataAdapterFn = (serverData) => {
				serverData.content = this.planoDTO.modalidades.map((m) => {
					return {
						codigo: m.codigo,
						modalidade: m.modalidade,
						nrVezesDescricao: m.listaVezesSemana,
						vezesSemana: m.vezesSemana,
					};
				});
				return serverData;
			};
		}
	}

	initState() {
		this.planoDTO = this.planoState.plano;
	}

	initGridConfig() {
		const planoParam = this.codigo ? `?plano=${this.codigo}` : "";

		this.gridConfig = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: `${
				this.discoveryService.getUrlMap().planoMsUrl
			}/plano/modalidades${planoParam}`,
			endpointParamsType: "query",
			formGroup: this.modalidadeFormGroup,
			onAddFn: (row, data, indexRow) => {
				if (data[indexRow].modalidade === null) {
					data.splice(indexRow, 1);
					this.notificationService.error("Adicione uma modalidade válida");
					return of(false);
				}
				this.adicionarModalidade(data, indexRow);
				row.nrVezesDescricao = `${data[indexRow].modalidade.nrVezes || ""} ${
					data[indexRow].modalidade.nrVezes > 1 ? "Vezes" : "Vez"
				}`;
				return data;
			},
			onEditFn: (row, data, index) => {
				this.planoDTO = this.planoState.plano;
				this.planoDTO.modalidades = data.map((m) => {
					return {
						codigo: m.codigo,
						modalidade: m.modalidade,
						nrVezesDescricao: m.nrVezesDescricao,
						vezesSemana: m.vezesSemana,
					};
				});

				this.planoState.updateState(this.planoDTO);
				this.modalidadesSelecionadas = data.map((m) => m.modalidade);
				this.atualizarVezesSemana(this.planoDTO, this.vezesSemanaTableData);
				return data;
			},
			onDeleteFn: (row, data, indexRow) => this.deletar(row, data, indexRow),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: false,
					ordenavel: false,
					editable: false,
				},
				{
					nome: "modalidade",
					titulo: "Modalidade",
					endpointUrl: `${
						this.discoveryService.getUrlMap().planoMsUrl
					}/modalidades/only-cod-name`,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					width: "40%",
					selectParamBuilder: (param) => {
						return {
							page: "0",
							size: "10",
							filters: JSON.stringify({
								quicksearchValue: param,
								codigosNaoConsultar: this.modalidadesSelecionadas.map(
									(m) => m.codigo
								),
							}),
						};
					},
					selectOptionChange: (option: Modalidade, form, row) => {
						//form.controls['nrVezesDescricao'].setValue(option.nrVezes);
						// form.controls['nrVezes'].setValue(option.nrVezes);
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
				},
				{
					nome: "nrVezesDescricao",
					titulo: "Número de Vezes por Semana",
					visible: true,
					ordenavel: false,
					editable: false,
					inputType: "text",
					width: "30%",
				},
				{
					nome: "nrVezes",
					titulo: "Vezes por Semana",
					visible: true,
					ordenavel: false,
					editable: false,
					celula: this.celulaAcoes,
					width: "10%",
				},
			],
		});
	}
	private adicionarModalidade(data: any, indexRow: any) {
		this.planoDTO = this.planoState.plano;
		if (!this.planoDTO.modalidades) {
			this.planoDTO.modalidades = [];
		}
		const modalidade = data[indexRow].modalidade;

		this.planoDTO.modalidades.push({
			modalidade: modalidade,
			listaVezesSemana: `${modalidade.nrVezesDescricao}`,
			vezesSemana: this.vezesSemanaTableData.get(modalidade.codigo),
		});

		this.planoState.updateState(this.planoDTO);
		this.modalidadesSelecionadas.push(modalidade);
	}

	editVezesSemana(row: any, index: number): any {
		if (row.modalidade && row.modalidade !== "") {
			const modal: PactoModalRef = this.modalService.open(
				"Configurar vezes na semana",
				ModalVezesSemanaComponent,
				PactoModalSize.LARGE
			);
			modal.componentInstance.planoModalidade = row;
			modal.componentInstance.formGroupPlanoModalidade =
				this.modalidadeFormGroup;
			modal.componentInstance.tableData =
				this.vezesSemanaTableData.get(row.modalidade.codigo) || [];

			if (
				row.vezesSemana !== undefined &&
				row.vezesSemana.length > 0 &&
				row.vezesSemana[0].tipoOperacao !== ""
			) {
				modal.componentInstance.tableData = row.vezesSemana.map((vez) => {
					if (vez.codigo === undefined || vez.codigo === null) {
						return vez;
					} else
						return {
							codigo: vez.codigo,
							vezesSemana: vez.nrVezes ? vez.nrVezes : vez.vezesSemana,
							formaCalculo: vez.tipoValor
								? this.formasDeCalculo.find((f) => f.codigo === vez.tipoValor)
								: vez.formaCalculo,
							custo: vez.tipoValor
								? vez.tipoValor === "VE"
									? vez.valorEspecifico
									: vez.percentualDesconto
								: vez.custo,
							tipoOperacao:
								typeof vez.tipoOperacao !== "string"
									? vez.tipoOperacao
									: this.tiposOperacao.find(
											(tip) => tip.codigo === vez.tipoOperacao
									  ),
							vezesSemanaReferencia: vez.vezesSemanaReferencia,
						};
				});
			}

			modal.result.then((modalResult) => {
				const modalidade = modalResult.planoModalidade.modalidade;

				let nrVezesDescricao = this.nrVezesDescricao(modalResult);
				modalResult.planoModalidade.nrVezesDescricao = nrVezesDescricao;
				this.vezesSemanaTableData.set(modalidade.codigo, modalResult.tableData);
				this.table.updateRow(index, modalResult.planoModalidade);
				this.atualizarVezesSemana(this.planoDTO, this.vezesSemanaTableData);
			});
		} else {
			this.notificationService.error("Selecione uma modalidade");
		}
	}

	private nrVezesDescricao(modalResult: any) {
		const planoModalidade = modalResult.planoModalidade;
		let nrVezesDescricao = planoModalidade.nrVezes
			? `${planoModalidade.nrVezes}`
			: planoModalidade.modalidade.nrVezes;
		if (modalResult.tableData && modalResult.tableData.length > 0) {
			const descricaoConfiguracaoVezesSemana = modalResult.tableData
				.map(this.descricaoVezesSemana)
				.join(", ");
			nrVezesDescricao = `${descricaoConfiguracaoVezesSemana}`;
		} else {
			nrVezesDescricao = `${nrVezesDescricao}`;
		}
		return nrVezesDescricao;
	}

	private descricaoVezesSemana(item) {
		const descricaoCusto =
			item.formaCalculo.codigo === "PD" ? `${item.custo}%` : `R$ ${item.custo}`;
		const descricaoVezes = item.vezesSemana > 1 ? "vezes" : "vez";
		let descricao = `${item.vezesSemana} ${descricaoVezes} - ${item.tipoOperacao.nome} ${descricaoCusto}`;
		if (item.vezesSemanaReferencia) {
			descricao += ` - Vezes na semana referência`;
		}
		return descricao;
	}

	private atualizarVezesSemana(
		planoDTO: Plano,
		vezesSemanaTableData: Map<number, any[]>
	) {
		planoDTO.modalidades.forEach((planoModalidade) => {
			let newVezesSemana = vezesSemanaTableData.get(
				planoModalidade.modalidade.codigo
			);
			if (newVezesSemana !== undefined) {
				planoModalidade.vezesSemana = newVezesSemana.map((vez) => {
					return {
						codigo: vez.codigo,
						tipoOperacao: vez.tipoOperacao.codigo,
						tipoValor: vez.formaCalculo.codigo,
						nrVezes: vez.vezesSemana,
						valorEspecifico: vez.custo,
						percentualDesconto: vez.percentualDesconto,
						referencia: vez.referencia,
					};
				});
			}
		});
		this.modalidadesSelecionadas = planoDTO.modalidades.map(
			(plMod) => plMod.modalidade
		);
		this.planoState.updateState(planoDTO);
	}

	isVezesSemana(row: any): Boolean {
		if (
			row.vezesSemana !== undefined &&
			row.vezesSemana.length > 0 &&
			row.vezesSemana[0].tipoOperacao !== ""
		) {
			return true;
		}
		return false;
	}

	deletar(row: any, data: any, indexRow: any): Observable<boolean> {
		this.planoDTO = this.planoState.plano;
		const index = this.planoDTO.modalidades.findIndex(
			(md) => md.modalidade.codigo === row.modalidade.codigo
		);

		if (index === -1) return of(false);

		this.planoDTO.modalidades.splice(index, 1);
		this.planoState.plano = this.planoDTO;
		this.modalidadesSelecionadas = this.planoDTO.modalidades.map(
			(plMod) => plMod.modalidade
		);
		data.splice(indexRow, 1);
		return data;
	}

	showDelete(row: any, isAdd: any): boolean {
		if (isAdd) return false;
		return true;
	}
}
