import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ClientDiscoveryService } from "sdk";
import {
	CatTableEditableComponent,
	DataFiltro,
	PactoDataGridConfig,
	TableData,
} from "ui-kit";
import { BehaviorSubject, of } from "rxjs";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PlanoModalidadeVezesSemana } from "plano-api";
import { PlanoModalidade } from "../../../plano.model";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-modal-vezes-semana",
	templateUrl: "./modal-vezes-semana.component.html",
	styleUrls: ["./modal-vezes-semana.component.scss"],
})
export class ModalVezesSemanaComponent implements OnInit {
	@ViewChild("table", { static: false }) table: CatTableEditableComponent;
	tableData: PlanoModalidadeVezesSemana[];
	gridConfig: PactoDataGridConfig;
	planoModalidade: PlanoModalidade;

	private formasDeCalculoSource = new BehaviorSubject<any[]>([]);
	formasDeCalculoObservable$ = this.formasDeCalculoSource.asObservable();
	formasDeCalculo: Array<{ codigo: string; nome: string }>;
	tiposOperacao: Array<{ codigo: string; nome: string }>;
	tipoOperacaoSelecionada: { codigo: string; nome: string };

	constructor(
		private discoveryService: ClientDiscoveryService,
		private openModal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	vezesSemanaForm: FormGroup = new FormGroup({
		codigo: new FormControl(),
		vezesSemana: new FormControl(),
		tipoOperacao: new FormControl(),
		formaCalculo: new FormControl(),
		custo: new FormControl(),
		vezesSemanaReferencia: new FormControl(),
	});

	ngOnInit() {
		this.formasDeCalculo = [
			{ codigo: "VE", nome: "Valor" },
			{ codigo: "PD", nome: "Percentual" },
		];

		this.tiposOperacao = [
			{ codigo: "AC", nome: "Acrescimo" },
			{ codigo: "EX", nome: "Exatamente" },
			{ codigo: "RE", nome: "Redução" },
		];

		const planoModalideParam =
			this.planoModalidade && this.planoModalidade.modalidade.codigo
				? `?planoModalidade=${this.planoModalidade.modalidade.codigo}`
				: "";
		this.gridConfig = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: `${
				this.discoveryService.getUrlMap().planoMsUrl
			}/plano/modalidades/vezes-semana${planoModalideParam}`,
			endpointParamsType: "query",
			dataFn: (filter: DataFiltro): TableData<any> => {
				return {
					content: this.tableData,
					size: this.tableData.length,
					totalElements: this.tableData.length,
					number: 0,
				};
			},
			visibleColumnFn: (column) => {
				if (
					column.nome === "vezesSemanaReferencia" &&
					this.tipoOperacaoSelecionada &&
					this.tipoOperacaoSelecionada.codigo === "EX"
				) {
					return true;
				}
				return column.visible;
			},
			onAddFn: (row, data, indexRow) => {
				if (
					row.tipoOperacao === null ||
					row.formaCalculo === null ||
					row.custo === null
				) {
					this.notificationService.error("Adicione a vez na semana válida");
					data.splice(indexRow, 1);
					return of(false);
				}
				return data;
			},
			onDeleteFn: (row, data, indexRow) => this.onDelete(row, data, indexRow),
			formGroup: this.vezesSemanaForm,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: false,
					ordenavel: false,
					editable: false,
				},
				{
					nome: "vezesSemana",
					titulo: "Vezes na semana",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					width: "122px",
					minValue: 1,
				},
				{
					nome: "tipoOperacao",
					titulo: "Tipo de operação",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					width: "161px",
					inputSelectData: this.tiposOperacao,
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					showSelectFilter: false,
					addEmptyOption: false,
					showAddSelectBtn: false,
					selectOptionChange: (option, form, row) => {
						const optionValor = { codigo: "VE", nome: "Valor" };
						this.tipoOperacaoSelecionada = option;
						if (option.codigo === "EX") {
							this.formasDeCalculoSource.next([optionValor]);
							form.controls["formaCalculo"].setValue(optionValor);
						} else {
							this.formasDeCalculoSource.next([
								optionValor,
								{ codigo: "PD", nome: "Percentual" },
							]);
						}
					},
				},
				{
					nome: "formaCalculo",
					titulo: "Forma de cálculo",
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					addEmptyOption: false,
					showAddSelectBtn: false,
					idSelectKey: "codigo",
					labelSelectKey: "nome",
					inputType: "select",
					width: "122px",
					inputSelectData: this.formasDeCalculo,
				},
				{
					nome: "vezesSemanaReferencia",
					titulo: "Vezes na semana referência",
					labelText: "Vezes na semana referência",
					visible: false,
					ordenavel: false,
					editable: true,
					width: "214px",
					inputType: "checkbox",
					valueTransform: (v, row) => {
						if (v) {
							return "Sim";
						} else {
							return "Não";
						}
					},
				},
				{
					nome: "custo",
					titulo: "Custo",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimalPrecision: 2,
					minValue: 0,
					decimal: true,
					width: "83px",
					valueTransform: (v, row) => {
						const formattedValue = Number(v).toLocaleString("pt-BR", {
							minimumFractionDigits: 2,
							maximumFractionDigits: 2,
						});
						return row.formaCalculo && row.formaCalculo.codigo === "PD"
							? `${formattedValue} %`
							: `R$ ${formattedValue}`;
					},
				},
			],
		});
	}

	close() {
		this.planoModalidade.vezesSemana = this.table.rawData;
		this.openModal.close({
			tableData: this.table.rawData,
			planoModalidade: this.planoModalidade,
		});
	}

	onDelete(row, data, index) {
		data.splice(index, 1);
		return of(data);
	}
}
