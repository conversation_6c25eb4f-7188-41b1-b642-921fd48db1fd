<adm-layout
	(goBack)="voltarHome()"
	i18n-modulo="@@codicao-pagamento:modulo"
	i18n-pageTitle="@@condicao-pagamento:title"
	modulo="Administrativo"
	pageTitle="Condição de pagamento">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableData
			(btnAddClick)="novaCondicaoPagamento()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editCondicaoPagamento($event)"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			idSuffix="condicao-pagamento"
			labelBtnAdd="Adicionar"
			telaId="condicaoPagamento"></pacto-relatorio>
	</div>
</adm-layout>
<pacto-traducoes-xingling #traducao>
	<span i18n="@@mensagem-sem-permissao" xingling="mensagem-sem-permissao">
		Seu usuário não possui permissão, procure seu administrador
	</span>
	<span
		i18n="@@condicao-pagamento:success-excluded"
		xingling="success-excluded">
		Condição de pagamento excluída com sucesso
	</span>
	<span i18n="@@adm:action-editar" xingling="action-editar">Editar</span>
	<span
		i18n="@@adm:tooltip-editar-condicao-pagamento"
		xingling="tooltip-editar-condicao-pagamento">
		Editar uma condição de pagamento
	</span>
	<span i18n="@@excluir:excluir" xingling="action-excluir">Excluir</span>
	<span
		i18n="@@adm:tooltip-excluir-condicao-pagamento"
		xingling="tooltip-excluir-condicao-pagamento">
		Excluir uma condição de pagamento
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@condicao-pagamento:column-codigo">Código</span>
</ng-template>
<ng-template #columnDescricao>
	<span i18n="@@condicao-pagamento:column-descricao">Descrição</span>
</ng-template>
<ng-template #columnTipoConvenioCobranca>
	<span i18n="@@condicao-pagamento:column-tipo-convenio-cobranca">
		Tipo convênio cobrança
	</span>
</ng-template>
<ng-template #columnNrParcelas>
	<span i18n="@@condicao-pagamento:column-nr-parcelas">NR de parcelas</span>
</ng-template>
<ng-template #columnPercentualValorEntrada>
	<span i18n="@@condicao-pagamento:column-percentual-valor-entrada">
		Percentual de entrada
	</span>
</ng-template>
