import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
	AfterViewInit,
} from "@angular/core";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import { Router } from "@angular/router";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { getTipoConvenioCobrancaText } from "../../enum/TipoConvenioCobranca";
import { DecimalPipe } from "@angular/common";
import { tipoConvenioCobranca } from "../../enum/TipoConvenioCobranca";
import { PlanoApiCondicaoPagamentoService, CondicaoPagamento } from "plano-api";

@Component({
	selector: "adm-condicao-pagamento",
	templateUrl: "./condicao-pagamento.component.html",
	styleUrls: ["./condicao-pagamento.component.scss"],
})
export class CondicaoPagamentoComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnTipoConvenioCobranca", { static: true })
	columnTipoConvenioCobranca: TemplateRef<any>;
	@ViewChild("columnNrParcelas", { static: true })
	columnNrParcelas: TemplateRef<any>;
	@ViewChild("columnPercentualValorEntrada", { static: true })
	columnPercentualValorEntrada: TemplateRef<any>;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	table: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		private decimalPipe: DecimalPipe
	) {
		this.recurso = this.session.recursos.get(
			PerfilAcessoRecursoNome.CONDICAO_PAGAMENTO
		);
	}

	ngOnInit() {
		this.initTable();
	}

	editCondicaoPagamento(condicaoPagamento) {
		this.router.navigate([
			"adm",
			"planos",
			"condicao-pagamento",
			condicaoPagamento.codigo,
		]);
	}

	deleteCondicaoPagamento(condicaoPagamento: CondicaoPagamento) {
		if (this.recurso.excluir) {
			this.condicaoPagamentoService.delete(condicaoPagamento.codigo).subscribe(
				(response) => {
					this.notificationService.success(
						this.traducao.getLabel("success-excluded")
					);
					this.tableData.reloadData();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("mensagem-sem-permissao")
			);
		}
	}

	novaCondicaoPagamento() {
		this.router.navigate(["adm", "planos", "nova-condicao-pagamento"]);
	}

	voltarHome() {
		this.router.navigate(["adm", "planos"]);
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/condicoesPagamento",
					false,
					Api.MSPLANO
				),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoConvenioCobranca",
						titulo: this.columnTipoConvenioCobranca,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => {
							return getTipoConvenioCobrancaText(v);
						},
					},
					{
						nome: "nrParcelas",
						titulo: this.columnNrParcelas,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "percentualValorEntrada",
						titulo: this.columnPercentualValorEntrada,
						visible: true,
						ordenavel: true,
						decimal: true,
						valueTransform: (v) => {
							if (v !== undefined) {
								return this.decimalPipe.transform(
									parseFloat(v.toString().replace(",", ".")),
									"1.2-2"
								);
							} else {
								return "-";
							}
						},
					},
				],
				dropDownActions: [
					{
						nome: this.traducao.getLabel("action-editar"),
						iconClass: "",
						tooltipText: this.traducao.getLabel(
							"tooltip-editar-condicao-pagamento"
						),
						actionFn: (row) => this.editCondicaoPagamento(row),
						showIconFn: (row) => this.recurso.editar,
					},
					{
						nome: this.traducao.getLabel("action-excluir"),
						iconClass: "",
						tooltipText: this.traducao.getLabel(
							"tooltip-excluir-condicao-pagamento"
						),
						actionFn: (row) => this.deleteCondicaoPagamento(row),
						showIconFn: (row) => this.recurso.excluir,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editCondicaoPagamento(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteCondicaoPagamento(event.row);
		}
	}
}
