import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	HostListener,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import {
	BUTTON_TYPE,
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { TipoPlano } from "../../plano.model";
import { PlanoService } from "../cadastrar-plano/plano.service";
import { PlanoAdvancedConfigComponent } from "../plano-advanced-config/plano-advanced-config.component";
import { SnotifyService } from "ng-snotify";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../services/plano-commons.service";
import { PerfilAcessoRecurso } from "sdk";
import { LocalizationService } from "../../../services/localization.service";
import { PlanoConfiguracaoSistema } from "../../services/plano-configuracao-sistema.model";
import {
	PlanoApiCondicaoPagamentoService,
	PlanoApiProdutoService,
} from "plano-api";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "adm-edit-form-plano",
	templateUrl: "./edit-form-plano.component.html",
	styleUrls: ["./edit-form-plano.component.scss"],
})
export class EditFormPlanoComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	loading = false;
	isSaving = false;
	moneyMask: any = { guide: false };
	plano: any;
	tiposPlano: any[];
	buttonType = BUTTON_TYPE;
	formGroup: FormGroup = new FormGroup({
		descricao: new FormControl(null, [Validators.required]),
		tipoPlano: new FormControl(),
		vigenciaDe: new FormControl(new Date(), [Validators.required]),
		ingressoAte: new FormControl(new Date(), [Validators.required]),
		vigenciaAte: new FormControl(new Date(), [Validators.required]),
		produtoContrato: new FormControl(),
		gerarParcelasValorDiferente: new FormControl(),
		restringeVendaPorCategoria: new FormControl(),
		categoriasPlano: new FormControl(),
	});
	codigoControl: FormControl = new FormControl("");
	idPlano: number;
	duracoes: Array<any> = new Array<any>();
	duracoesCondicaoOrdenada: Array<any> = new Array<any>();
	condicoesPagamento: Array<any> = new Array<any>();
	tipoPlanoParam: string;
	recurso: PerfilAcessoRecurso;
	configuracaoSistema: PlanoConfiguracaoSistema;
	exibeCategoria: Boolean;

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private planoService: PlanoService,
		private localizationService: LocalizationService,
		public produtoService: PlanoApiProdutoService,
		private condicaoPagamentoService: PlanoApiCondicaoPagamentoService,
		private dialogService: DialogService,
		private notificationService: SnotifyService,
		private planoCommonsService: PlanoCommonsService,
		public planoState: PlanoStateService,
		private cd: ChangeDetectorRef,
		public admRestService: AdmRestService
	) {
		this.moneyMask.mask = this.localizationService.getCurrencyMask();
		this.recurso = this.planoCommonsService.recurso;
	}

	ngOnInit() {
		if (this.planoState.plano) {
			this.plano = this.planoState.plano;
		}
		this.codigoControl.disable();
		this.idPlano = +this.route.snapshot.paramMap.get("id");
		this.tipoPlanoParam = this.route.snapshot.queryParamMap.get("tipo");
		this.initTiposPlano();
		this.initConfiguracaoSistema();
		this.condicaoPagamentoService
			.findByLessThenNrParcelas()
			.subscribe((data: any) => {
				this.condicoesPagamento = data.content;
			});
		this.loading = true;
		this.planoService.findById(this.idPlano).subscribe((data: any) => {
			this.plano = data.content;
			this.planoCommonsService.updateCondicoesPagamentoEdit(this.plano);
			if (this.plano.excecoes) {
				this.plano.excecoes.sort((a: any, b: any) => {
					if (a.codigo < b.codigo) {
						return -1;
					} else if (a.codigo > b.codigo) {
						return 1;
					}
					return 0;
				});
			}
			if (this.plano.produtosSugeridos) {
				this.plano.produtosSugeridos.sort((a: any, b: any) => {
					if (a.codigo < b.codigo) {
						return -1;
					} else if (a.codigo > b.codigo) {
						return 1;
					}
					return 0;
				});
			}
			this.planoState.plano = this.plano;
			this.codigoControl.setValue(this.plano.codigo);
			this.formGroup.patchValue({
				descricao: this.plano.descricao,
				tipoPlano: this.getTipoPlanoByLabel(this.plano.tipoPlano),
				vigenciaDe: this.plano.vigenciaDe,
				ingressoAte: this.plano.ingressoAte,
				vigenciaAte: this.plano.vigenciaAte,
				produtoContrato: this.plano.produtoContrato,
				restringeVendaPorCategoria: this.plano.restringeVendaPorCategoria,
			});
			this.duracoes = this.plano.duracoes.sort((a: any, b: any) => {
				if (a.codigo < b.codigo) {
					return -1;
				} else if (a.codigo > b.codigo) {
					return 1;
				}
				return 0;
			});
			this.duracoesCondicaoOrdenada = this.plano.duracoes.sort(
				(a: any, b: any) => {
					if (a.numeroMeses < b.numeroMeses) {
						return -1;
					} else if (a.numeroMeses > b.numeroMeses) {
						return 1;
					}
					return 0;
				}
			);
			this.loading = false;
			this.cd.detectChanges();
		});
		this.formGroup
			.get("tipoPlano")
			.valueChanges.subscribe((value) => (this.plano.tipoPlano = value));
		this.formGroup
			.get("restringeVendaPorCategoria")
			.valueChanges.subscribe((value) => {
				this.exibeCategoria = value;
				if (this.exibeCategoria) {
					this.plano.categorias;
				}
				this.cd.detectChanges();
			});
	}

	configuracoesPlano() {
		const dialogRef = this.dialogService.open(
			"Configurações Avançadas",
			PlanoAdvancedConfigComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoState.plano;
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {
					this.planoState.plano = value;
				})
				.catch((error) => {
					console.log(error);
				});
		}
	}

	private initTiposPlano() {
		setTimeout(() => {
			const tipos = [];
			tipos.push({
				label: this.traducoes.getLabel("PN"),
				id: TipoPlano.PLANO_NORMAL,
			});
			tipos.push({
				label: this.traducoes.getLabel("PA"),
				id: TipoPlano.PLANO_AVANCADO,
			});
			tipos.push({
				label: this.traducoes.getLabel("PR"),
				id: TipoPlano.PLANO_RECORRENCIA,
			});
			tipos.push({
				label: this.traducoes.getLabel("PC"),
				id: TipoPlano.PLANO_CREDITO,
			});
			tipos.push({
				label: this.traducoes.getLabel("PP"),
				id: TipoPlano.PLANO_PERSONAL,
			});
			this.tiposPlano = tipos;
			this.cd.detectChanges();
		});
	}

	initConfiguracaoSistema() {
		if (
			this.tipoPlanoParam === "PLANO_NORMAL" ||
			this.tipoPlanoParam === "PLANO_AVANCADO"
		) {
			this.planoCommonsService
				.initConfiguracaoSistema()
				.subscribe((data: PlanoConfiguracaoSistema) => {
					this.configuracaoSistema = data;
					this.cd.detectChanges();
				});
		}
	}

	private getTipoPlanoByLabel(label) {
		return this.tiposPlano.find((tipoplano) => tipoplano.id === label).id;
	}

	goBack() {
		sessionStorage.removeItem("plano");
		this.router.navigate(["adm", "planos"]);
	}

	confirmDuracaoValor(event) {
		Object.keys(event.form.controls).forEach(
			(key) => (event.row[key] = event.form.get(key).value)
		);
		const duracao = this.plano.excecoes.find(
			(exc) => exc.codigo === event.row.codigo
		);
		if (duracao) {
			duracao.vezesSemana = event.row.vezesSemana.codigo;
		}
	}

	updateCondicaoPagamento(event?) {
		if (event) {
			this.plano = event.planoDTO;
		}
		if (this.plano.duracoes) {
			this.duracoes = this.plano.duracoes.sort((a: any, b: any) => {
				if (+a.numeroMeses < +b.numeroMeses) {
					return -1;
				} else if (+a.numeroMeses > +b.numeroMeses) {
					return 1;
				}
				return 0;
			});
		}
		this.duracoesCondicaoOrdenada = this.duracoes;
		if (this.plano.tipoPlano === TipoPlano.PLANO_AVANCADO) {
			this.plano.duracoes = this.duracoes;
		}
		if (this.plano.tipoPlano === TipoPlano.PLANO_NORMAL) {
			if (this.plano.excecoes && this.plano.excecoes.length > 0) {
				this.plano.excecoes.forEach((excecao) => {
					const condicoesFiltered = this.condicoesPagamento.filter(
						(condicaoPagamento) =>
							condicaoPagamento.nrParcelas <= +excecao.duracao
					);
					const condicoesPagamentos = new Array<any>();
					const duracao2: any = {
						situacao: true,
					};
					const filterDuracoes = this.duracoes.filter(
						(dur) => +dur.numeroMeses === +excecao.duracao
					);
					if (filterDuracoes.length === 0) {
						duracao2.numeroMeses = excecao.duracao;
						condicoesFiltered.forEach((condicaoPG) => {
							condicoesPagamentos.push({
								condicaoPagamento: {
									codigo: condicaoPG.codigo,
									descricao: condicaoPG.descricao,
									nrParcelas: condicaoPG.nrParcelas,
								},
							});
						});
						duracao2.condicoesPagamento = condicoesPagamentos.sort(
							(a: any, b: any) => {
								if (
									a.condicaoPagamento.nrParcelas <
									b.condicaoPagamento.nrParcelas
								) {
									return -1;
								} else if (
									a.condicaoPagamento.nrParcelas >
									b.condicaoPagamento.nrParcelas
								) {
									return 1;
								}
								return 0;
							}
						);
						this.duracoes.push(duracao2);
					}
				});
				const duracoesAux = [];
				Object.assign(duracoesAux, this.duracoes);
				duracoesAux.forEach((v, index) => {
					if (!this.plano.excecoes.some((v2) => v2.duracao === v.numeroMeses)) {
						this.duracoes.splice(index, 1);
					}
				});
				this.duracoesCondicaoOrdenada = this.duracoes.sort((a: any, b: any) => {
					if (+a.numeroMeses < +b.numeroMeses) {
						return -1;
					} else if (+a.numeroMeses > +b.numeroMeses) {
						return 1;
					}
					return 0;
				});
			} else {
				this.duracoes = [];
				this.duracoesCondicaoOrdenada = [];
			}
			this.plano.duracoes = this.duracoes;
			this.planoState.plano = this.plano;
		}
	}

	editDuracaoValor(row) {
		row.vezesSemana = {
			codigo: row.vezesSemana,
		};
	}

	save() {
		if (!this.isSaving) {
			this.plano = this.planoState.plano;
			this.plano.descricao = this.formGroup.get("descricao").value;
			this.plano.tipoPlano = this.formGroup.get("tipoPlano").value;
			this.plano.vigenciaDe = this.formGroup.get("vigenciaDe").value;
			this.plano.ingressoAte = this.formGroup.get("ingressoAte").value;
			this.plano.vigenciaAte = this.formGroup.get("vigenciaAte").value;
			this.plano.produtoContrato = this.formGroup.get("produtoContrato").value;
			this.plano.restringeVendaPorCategoria = this.formGroup.get(
				"restringeVendaPorCategoria"
			).value;
			if (this.plano.planoRecorrencia) {
				this.plano.planoRecorrencia.gerarParcelasValorDiferente =
					this.formGroup.get("gerarParcelasValorDiferente").value;
				if (this.plano.planoRecorrencia.parcelarAnuidade) {
					this.plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					this.plano.planoRecorrencia.parcelasAnuidade = undefined;
				}
				if (this.plano.planoRecorrencia.parcelasAnuidade) {
					this.plano.planoRecorrencia.parcelasAnuidade.forEach(
						(parcelaAnuidade) => {
							if (parcelaAnuidade.parcela && parcelaAnuidade.parcela.value) {
								parcelaAnuidade.parcela = parcelaAnuidade.parcela.value;
							}
						}
					);
				}
			}
			this.planoCommonsService.updateCondicoesPagamento(this.plano);
			if (this.plano.excecoes) {
				this.plano.excecoes.forEach((excecao) => {
					if (excecao.vezesSemana && excecao.vezesSemana.codigo) {
						excecao.vezesSemana = excecao.vezesSemana.codigo;
					}
				});
			}
			if (!this.plano.bolsa && this.plano.excecoes) {
				let modalidadeValorNaoZerado = false;
				let semModalidade = true;
				for (const d of this.plano.excecoes) {
					if (d.modalidade) {
						if (d.modalidade.valorMensal > 0) {
							modalidadeValorNaoZerado = true;
							break;
						}
						semModalidade = false;
					}
				}
				if (!semModalidade) {
					if (!modalidadeValorNaoZerado) {
						this.notificationService.error(
							this.traducoes.getLabel("modalidade-zerada-hint"),
							{
								timeout: 10000,
							}
						);
						this.notificationService.error(
							this.traducoes.getLabel("modalidade-zerada-error"),
							{
								timeout: 10000,
							}
						);
						return;
					}
				}
			}
			this.isSaving = true;
			this.planoService.save(this.plano).subscribe(
				(data) => {
					if (
						this.plano &&
						this.plano.codigo &&
						this.plano.codigo > 0 &&
						this.configuracaoSistema &&
						this.configuracaoSistema.permitirreplicarplanoredeempresa
					) {
						this.planoService
							.replicarAutomaticoTodosReplicados(this.plano.codigo)
							.subscribe((ret) => {});
					}
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					this.planoState.removeFromSession();
					this.router.navigate(["adm", "planos"]);
					this.isSaving = false;
				},
				(error) => {
					this.isSaving = false;
					this.cd.detectChanges();
					if (error.error) {
						if (error.error.meta && error.error.meta.error) {
							this.notificationService.error(error.error.meta.error);
						} else {
							this.notificationService.error(error.error.meta.messageValue);
						}
					}
				}
			);
		}
	}

	produtoelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["descricao"],
			}),
		};
	};

	@HostListener("window:popstate")
	back() {
		this.planoState.removeFromSession();
	}
}
