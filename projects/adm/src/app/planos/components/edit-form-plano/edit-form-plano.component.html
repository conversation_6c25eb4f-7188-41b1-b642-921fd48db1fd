<ng-container *ngIf="loading">
	<div class="loader">
		<img src="assets/images/loading.svg" />
		<span i18n="@@plano:loading-message">Carregando plano...</span>
	</div>
</ng-container>
<ng-container *ngIf="!loading">
	<adm-layout
		i18n-pageTitle="@@plano:edit-page-title"
		pageTitle="Editar Plano: {{ formGroup.get('descricao').value | captalize }}"
		i18n-modulo="@@plano:modulo"
		modulo="Administrativo"
		[showConfirmDialog]="true"
		(goBack)="goBack()"
		*ngIf="
			tipoPlanoParam === 'PLANO_NORMAL' || tipoPlanoParam === 'PLANO_AVANCADO'
		">
		<pacto-cat-tabs-transparent
			[showAction]="!isSaving && recurso.editar"
			[actionLabel]="'Salvar'"
			actionId="plano-tabs-btn-salvar"
			(action)="save()">
			<ng-template
				pactoTabTransparent
				id="plano-tab-dados-basicos"
				i18n-label="@@plano:edit-label-dados-basicos"
				label="Dados Básicos">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="row">
						<div class="col-md-4">
							<pacto-cat-form-input
								id="input-plano-nome"
								i18n-label="@@plano:label-descricao"
								label="Nome"
								[control]="formGroup.get('descricao')"
								i18n-errorMsg="@@plano:error-msg-descricao"
								errorMsg="Forneça um nome para o plano"></pacto-cat-form-input>
						</div>
						<div class="col-md-4">
							<pacto-cat-form-select
								id="select-tipo-plano"
								i18n-label="@@plano:label-tipo-plano"
								label="Tipo de plano"
								[control]="formGroup.get('tipoPlano')"
								i18n-errorMsg="@@plano:error-msg-tipo-plano"
								errorMsg="Tipo de plano"
								[items]="tiposPlano"
								disabled="true"></pacto-cat-form-select>
						</div>
						<div class="col-md-4">
							<pacto-cat-form-select-filter
								id="select-produto-contrato-plano"
								i18n-label="@@plano:label-produto-contrato"
								label="Produto padrão gerar parcelas contrato"
								[control]="formGroup.get('produtoContrato')"
								i18n-errorMsg="@@plano:error-msg-produto-contrato"
								errorMsg="Selecione um produto"
								[paramBuilder]="produtoelectBuilder"
								idKey="codigo"
								labelKey="descricao"
								[endpointUrl]="
									admRestService.buildFullUrlPlano(
										produtoService.produtosTipoPlano
									)
								"></pacto-cat-form-select-filter>
						</div>
					</div>
					<div class="row">
						<div class="col-md-4">
							<pacto-cat-form-datepicker
								id="datepicker-vigenciade-plano"
								i18n-label="@@plano:label-vigenciade"
								label="Plano ativo a partir de"
								i18n-errorMsg="@@plano:error-msg-vigenciade"
								errorMsg="Forneça uma data válida."
								[control]="
									formGroup.get('vigenciaDe')
								"></pacto-cat-form-datepicker>
						</div>
						<div class="col-md-4">
							<pacto-cat-form-datepicker
								id="datepicker-ingresso-plano"
								i18n-label="@@plano:label-ingressoate"
								label="Permite matrículas/rematrículas até"
								i18n-errorMsg="@@plano:error-msg-ingressoate"
								errorMsg="Forneça uma data válida."
								[control]="
									formGroup.get('ingressoAte')
								"></pacto-cat-form-datepicker>
						</div>
						<div class="col-md-4">
							<pacto-cat-form-datepicker
								id="datepicker-vigenciaate-plano"
								i18n-label="@@plano:label-vigenciaate"
								label="Permite renovação até"
								i18n-errorMsg="@@plano:error-msg-vigenciaate"
								errorMsg="Forneça uma data válida."
								[control]="
									formGroup.get('vigenciaAte')
								"></pacto-cat-form-datepicker>
						</div>
					</div>
					<div
						class="row"
						*ngIf="formGroup.get('tipoPlano').value === 'PLANO_AVANCADO'">
						<div class="col-md-6 mb-2">
							<pacto-cat-checkbox
								id="check-restringe-venda-por-categoria"
								[control]="formGroup.get('restringeVendaPorCategoria')"
								i18n-label="@@plano:label-restringe-venda-por-categoria"
								label="Restringir venda por categoria do cliente: "></pacto-cat-checkbox>
						</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-3">
							<pacto-cat-button
								id="btn-advanced-config-plano"
								(click)="configuracoesPlano()"
								[size]="'LARGE'"
								[type]="'OUTLINE'"
								[label]="
									traducoes.getLabel('advanced-config')
								"></pacto-cat-button>
						</div>

						<div class="col-md-3">
							<pacto-cat-checkbox
								*ngIf="formGroup.get('tipoPlano').value === 'PLANO_CREDITO'"
								id="check-gerar-parcelas-valor-diferente-plano"
								[control]="formGroup.get('gerarParcelasValorDiferente')"
								i18n-label="@@plano:label-gerar-parcelas-valor-diferente"
								label="Modalidades com valores diferentes"></pacto-cat-checkbox>
						</div>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-pacotes"
				label="Pacotes"
				*ngIf="
					planoState.plano && planoState.plano.tipoPlano === 'PLANO_AVANCADO'
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<adm-plano-pacote [planoDTO]="plano"></adm-plano-pacote>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-modalidades"
				label="Modalidade"
				*ngIf="
					planoState.plano && planoState.plano.tipoPlano === 'PLANO_AVANCADO'
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<adm-plano-modalidade [planoDTO]="plano"></adm-plano-modalidade>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-categoria"
				label="Categoria"
				*ngIf="
					planoState.plano &&
					planoState.plano.tipoPlano === 'PLANO_AVANCADO' &&
					exibeCategoria
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<adm-categoria-plano
							[formDadosBasicos]="formGroup"
							[planoDTO]="plano"></adm-categoria-plano>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-duracao"
				i18n-label="@@plano:edit-label-durval"
				label="Duração"
				*ngIf="
					planoState.plano && planoState.plano.tipoPlano === 'PLANO_AVANCADO'
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<div class="table-wrapper pacto-shadow">
							<adm-plano-duracao
								[planoDTO]="plano"
								(isEditinOrAdding)="disableNextDuracaoValor = $event"
								(afterAdd)="
									updateCondicaoPagamento($event)
								"></adm-plano-duracao>
						</div>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-duracao-e-valores"
				i18n-label="@@plano:edit-label-durval"
				[label]="
					planoState.plano && planoState.plano.tipoPlano === 'PLANO_AVANCADO'
						? 'Exceções'
						: 'Duração e Valores'
				"
				*ngIf="
					planoState.plano && planoState.plano.tipoPlano !== 'PLANO_PERSONAL'
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<div class="table-wrapper pacto-shadow">
							<adm-table-duracao-valor
								(afterAdd)="updateCondicaoPagamento($event)"
								[formDadosBasicos]="formGroup"
								[planoDTO]="plano"></adm-table-duracao-valor>
						</div>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-horarios-avancado"
				i18n-label="@@plano:edit-label-durval"
				[label]="'Horários'"
				*ngIf="
					planoState.plano && planoState.plano.tipoPlano === 'PLANO_AVANCADO'
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<div class="table-wrapper pacto-shadow">
							<adm-plano-horario
								[planoDTO]="plano"
								(isEditinOrAdding)="
									disableNextProduto = $event
								"></adm-plano-horario>
						</div>
					</div>
				</pacto-cat-card-plain>
			</ng-template>

			<ng-template
				pactoTabTransparent
				id="plano-tab-produtos-e-servicos"
				i18n-label="@@plano:edit-label-prod-serv"
				label="Produtos e Serviços"
				*ngIf="
					planoState.plano && planoState.plano.tipoPlano !== 'PLANO_PERSONAL'
				">
				<pacto-cat-card-plain style="display: block; margin-top: 15px">
					<div class="table-wrapper pacto-shadow">
						<adm-table-produtos [planoDTO]="plano"></adm-table-produtos>
					</div>
				</pacto-cat-card-plain>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-dados-contratuais"
				i18n-label="@@plano:edit-label-dados-contratuais"
				label="Dados Contratuais">
				<adm-plano-dados-contratuais
					style="display: block; margin-top: 15px"
					[plano]="plano"
					*ngIf="
						plano.tipoPlano === 'PLANO_NORMAL' ||
						plano.tipoPlano === 'PLANO_AVANCADO'
					"></adm-plano-dados-contratuais>

				<adm-plano-recorrencia-dados-contratuais
					style="display: block; margin-top: 15px"
					[plano]="plano"
					*ngIf="
						plano.tipoPlano === 'PLANO_RECORRENCIA'
					"></adm-plano-recorrencia-dados-contratuais>

				<adm-plano-personal-dados-contratuais
					style="display: block; margin-top: 15px"
					[plano]="plano"
					*ngIf="
						plano.tipoPlano === 'PLANO_PERSONAL'
					"></adm-plano-personal-dados-contratuais>
			</ng-template>
			<ng-template
				pactoTabTransparent
				id="plano-tab-condicao-de-pagamento"
				i18n-label="@@plano:edit-label-condpag"
				label="Condição de Pagamento"
				*ngIf="
					plano &&
					(plano.tipoPlano === 'PLANO_NORMAL' ||
						plano.tipoPlano === 'PLANO_CREDITO' ||
						plano.tipoPlano === 'PLANO_AVANCADO')
				">
				<ng-container
					*ngIf="
						duracoesCondicaoOrdenada && duracoesCondicaoOrdenada.length > 0
					">
					<adm-plano-condicao-pagamento
						style="display: block; margin-top: 15px"
						*ngFor="let duracao of duracoesCondicaoOrdenada"
						[duracao]="duracao"
						[condicoesPagamento]="
							condicoesPagamento
						"></adm-plano-condicao-pagamento>
				</ng-container>
				<ng-container
					*ngIf="
						!duracoesCondicaoOrdenada || duracoesCondicaoOrdenada?.length === 0
					">
					<pacto-cat-card-plain>
						<div class="table-wrapper pacto-shadow" style="margin-top: 15px">
							<span i18n="@@plano:condpag-hint-durval">
								Para criar uma condição de pagamento é necessário acessar o
								passo "Duração e Valores" e adicionar ao menos uma duração.
							</span>
						</div>
					</pacto-cat-card-plain>
				</ng-container>
			</ng-template>
			<ng-template
				pactoTabTransparent
				*ngIf="configuracaoSistema?.permitirreplicarplanoredeempresa"
				i18n-label="@@plano:edit-label-empresas"
				label="Replicar empresa">
				<adm-replicar-plano [plano]="plano"></adm-replicar-plano>
			</ng-template>
		</pacto-cat-tabs-transparent>
	</adm-layout>
	<adm-pr-edit *ngIf="tipoPlanoParam === 'PLANO_RECORRENCIA'"></adm-pr-edit>
	<adm-pc-edit-form
		*ngIf="tipoPlanoParam === 'PLANO_CREDITO'"></adm-pc-edit-form>
	<adm-pp-edit-form
		*ngIf="tipoPlanoParam === 'PLANO_PERSONAL'"></adm-pp-edit-form>
</ng-container>

<pacto-traducoes-xingling #traducoes>
	<span xingling="PN" i18n="@@plano:tipo-normal">Plano Normal</span>
	<span xingling="PA" i18n="@@plano:tipo-avancado">Plano Avançado</span>
	<span xingling="PR" i18n="@@plano:tipo-recorrencia">Plano Recorrência</span>
	<span xingling="PC" i18n="@@plano:tipo-credito">Plano Crédito</span>
	<span xingling="PP" i18n="@@plano:tipo-personal">Plano Personal</span>
	<span xingling="saved-success" i18n="@@plano:saved-success">
		Plano salvo com sucesso.
	</span>
	<span xingling="modalidade-zerada-hint" i18n="@@plano:modalidade-zerada-hint">
		Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais
		uma modalidade junto que tenha valor, ou então coloque valor no cadastro
		dela.
	</span>
	<span
		xingling="modalidade-zerada-error"
		i18n="@@plano:modalidade-zerada-error">
		Você adicionou modalidade com valor zerado na aba "modalidades". Como este
		plano não foi marcado como "PLANO BOLSA", esta operação não é permitida.
	</span>
	<span xingling="advanced-config" i18n="@@adm:advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
