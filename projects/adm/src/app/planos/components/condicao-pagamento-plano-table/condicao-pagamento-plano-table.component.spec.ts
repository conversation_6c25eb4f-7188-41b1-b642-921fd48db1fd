import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { CondicaoPagamentoPlanoTableComponent } from "./condicao-pagamento-plano-table.component";

describe("CondicaoPagamentoPlanoTableComponent", () => {
	let component: CondicaoPagamentoPlanoTableComponent;
	let fixture: ComponentFixture<CondicaoPagamentoPlanoTableComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [CondicaoPagamentoPlanoTableComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(CondicaoPagamentoPlanoTableComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
