import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";

import { AdmRestService } from "../../../adm-rest.service";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TableData,
	TraducoesXinglingComponent,
	DataFiltro,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { PlanoService } from "../cadastrar-plano/plano.service";
import { CondicaoPagamento, CondicaoPagamentoPlanoDuracao } from "plano-api";
import { ActivatedRoute } from "@angular/router";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-condicao-pagamento-plano-table",
	templateUrl: "./condicao-pagamento-plano-table.component.html",
	styleUrls: ["./condicao-pagamento-plano-table.component.scss"],
})
export class CondicaoPagamentoPlanoTableComponent implements AfterViewInit {
	@Input() condicaoPagamentoDTO: CondicaoPagamento = new CondicaoPagamento();
	@Input()
	condicaoPagamentoPlanosDuracoes: Array<CondicaoPagamentoPlanoDuracao> =
		new Array<CondicaoPagamentoPlanoDuracao>();
	@Input() formDadosBasicos: FormGroup;
	@Output() atualizarCondicaoPagamentoPlanos: EventEmitter<any> =
		new EventEmitter<any>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("tableCondPgComponent", { static: false })
	tableCondPgComponent: CatTableEditableComponent;
	@ViewChild("columnPlanoDescricao", { static: true })
	columnPlanoDescricao: TemplateRef<any>;
	@ViewChild("columnPlanoDuracao", { static: true })
	columnPlanoDuracao: TemplateRef<any>;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	tableCondPgPlano: PactoDataGridConfig;
	id;
	planoDuracaoAnterior: number;
	planosDuracoes: Array<any> = new Array<any>();
	planosDuracoesTable: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 10;
	planos = new Array<any>();
	baseFilter: DataFiltro = {};

	constructor(
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private planoService: PlanoService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService
	) {}

	ngAfterViewInit() {
		this.initTableCondicaoPagamentoPlano();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		if (this.condicaoPagamentoPlanosDuracoes === undefined) {
			this.condicaoPagamentoPlanosDuracoes =
				new Array<CondicaoPagamentoPlanoDuracao>();
		}
		if (this.id) {
			this.planoService
				.findByIdCondicaoPagamento(this.id)
				.subscribe((data: any) => {
					data.content.forEach((p) => {
						p.duracoes.forEach((pd) => {
							pd.condicoesPagamento.forEach((pcpgto) => {
								if (pcpgto.condicaoPagamento.codigo === Number(this.id)) {
									this.planosDuracoes.push({
										codigoPlanoCondicaoPagamento: pcpgto.codigo,
										codigoCondicaoPagamento: pcpgto.condicaoPagamento.codigo,
										plano: p,
										planoDuracao: pd,
									});
									const indexCondicaoPagamentoPlanoDuracao =
										this.condicaoPagamentoPlanosDuracoes.findIndex(
											(c) => c.planoDuracao === pd.codigo
										);
									if (indexCondicaoPagamentoPlanoDuracao < 0) {
										const condicaoPagamentoPlanoDuracao =
											new CondicaoPagamentoPlanoDuracao();
										condicaoPagamentoPlanoDuracao.condicaoPagamento =
											this.condicaoPagamentoDTO;
										condicaoPagamentoPlanoDuracao.plano = p.codigo;
										condicaoPagamentoPlanoDuracao.planoDuracao = pd.codigo;
										this.condicaoPagamentoPlanosDuracoes.push(
											condicaoPagamentoPlanoDuracao
										);
										this.atualizarCondicaoPagamentoPlanos.emit(
											this.condicaoPagamentoPlanosDuracoes
										);
									}
								}
							});
						});
					});
					if (this.tableCondPgComponent) {
						this.createCondicaoPagamentoPlanoPageObject();
					}
				});
		}
		this.tableCondPgPlano.dataAdapterFn = (serverData): TableData<any> => {
			serverData = this.planosDuracoesTable;
			return serverData;
		};
	}

	initTableCondicaoPagamentoPlano() {
		this.baseFilter.filters = {
			status: "true",
		};
		this.tableCondPgPlano = new PactoDataGridConfig({
			pagination: true,
			formGroup: new FormGroup({
				plano: new FormControl(),
				planoDuracao: new FormControl(),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "plano",
					titulo: this.columnPlanoDescricao,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					showAddSelectBtn: false,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					width: "500px",
					endpointUrl:
						this.planoService.urlPlano + "/consultar-planos-condicao-pagamento",
					addEmptyOption: false,
					customOption: {
						codigo: null,
						descricao: this.traducoes.getLabel(
							"table-condicao-pagamento-plano-todos"
						),
					},
					selectOptionChange: (option, form, row) =>
						this.onPlanoChange(option, form, row),
				},
				{
					nome: "planoDuracao",
					titulo: this.columnPlanoDuracao,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["numeroMeses"],
							}),
						};
					},
					showAddSelectBtn: false,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "",
					labelSelectKey: "numeroMeses",
					width: "300px",
					addEmptyOption: false,
				},
			],
			endpointUrl:
				this.planoService.urlPlano + "/consultar-planos-condicao-pagamento",
		});
		this.cd.detectChanges();
	}

	confirm(event) {
		this.adicionarCondicaoPagamentoPlano(event);
		this.createCondicaoPagamentoPlanoPageObject();
	}

	private adicionarCondicaoPagamentoPlano(event) {
		if (
			event.row.plano.descricao ===
			this.traducoes.getLabel("table-condicao-pagamento-plano-todos")
		) {
			this.planos.forEach((p) => {
				p.duracoes.forEach((pd) => {
					if (
						event.row.planoDuracao.numeroMeses ===
							this.traducoes.getLabel("table-condicao-pagamento-plano-todas") ||
						event.row.planoDuracao.numeroMeses === pd.numeroMeses
					) {
						this.condicaoPagamentoPlanosDuracoes.push({
							condicaoPagamento: this.condicaoPagamentoDTO,
							plano: p.codigo,
							planoDuracao: pd.codigo,
						});
						const index = this.planosDuracoes.findIndex(
							(pd2) => pd2.planoDuracao.codigo === pd.codigo
						);
						if (index === -1) {
							this.planosDuracoes.push({ plano: p, planoDuracao: pd });
						}
					}
				});
			});
			this.atualizarCondicaoPagamentoPlanos.emit(
				this.condicaoPagamentoPlanosDuracoes
			);
		} else if (
			event.row.planoDuracao.numeroMeses ===
			this.traducoes.getLabel("table-condicao-pagamento-plano-todas")
		) {
			event.row.plano.duracoes.forEach((pd) => {
				this.condicaoPagamentoPlanosDuracoes.push({
					condicaoPagamento: this.condicaoPagamentoDTO,
					plano: event.row.plano.codigo,
					planoDuracao: pd.codigo,
				});
				const index = this.planosDuracoes.findIndex(
					(pd2) => pd2.planoDuracao.codigo === pd.codigo
				);
				if (index === -1) {
					this.planosDuracoes.push({
						plano: event.row.plano,
						planoDuracao: pd,
					});
				}
			});
			this.atualizarCondicaoPagamentoPlanos.emit(
				this.condicaoPagamentoPlanosDuracoes
			);
		} else {
			if (this.planoDuracaoAnterior === undefined) {
				this.condicaoPagamentoPlanosDuracoes.push({
					condicaoPagamento: this.condicaoPagamentoDTO,
					plano: event.row.plano.codigo,
					planoDuracao: event.row.planoDuracao.codigo,
				});
				this.planosDuracoes.push(event.row);
				this.atualizarCondicaoPagamentoPlanos.emit(
					this.condicaoPagamentoPlanosDuracoes
				);
				this.planoDuracaoAnterior = undefined;
			} else if (this.planoDuracaoAnterior !== event.row.planoDuracao.codigo) {
				let indexCondicaoRemover;
				for (const i in this.condicaoPagamentoPlanosDuracoes) {
					const indexCondicao = this.planosDuracoes.findIndex(
						(pdt) =>
							pdt.planoDuracao.codigo ===
							this.condicaoPagamentoPlanosDuracoes[i].planoDuracao
					);
					if (indexCondicao === -1) {
						indexCondicaoRemover = i;
					}
				}
				if (indexCondicaoRemover >= 0) {
					this.condicaoPagamentoPlanosDuracoes.splice(indexCondicaoRemover, 1);
				}
				const indexPlanoDuracaoRemover = this.planosDuracoes.findIndex(
					(pd) => pd.planoDuracao.codigo === this.planoDuracaoAnterior
				);
				if (indexPlanoDuracaoRemover) {
					this.planosDuracoes.splice(indexPlanoDuracaoRemover, 1);
				}
				const cppd = new CondicaoPagamentoPlanoDuracao();
				cppd.condicaoPagamento = this.condicaoPagamentoDTO;
				cppd.plano = event.row.plano.codigo;
				cppd.planoDuracao = event.row.planoDuracao.codigo;
				this.condicaoPagamentoPlanosDuracoes.push(cppd);
				this.planosDuracoes.push(event.row);
				this.atualizarCondicaoPagamentoPlanos.emit(
					this.condicaoPagamentoPlanosDuracoes
				);
				this.planoDuracaoAnterior = undefined;
			}
		}
	}

	delete(event) {
		// Deletar algum planocondicaopagamento do planosDuracoesTable e do objeto condicaoPagamentoDTO
		let indexPlanoDuracaoTable;
		if (this.planosDuracoes) {
			this.planosDuracoes.find((pdt, i) => {
				if (
					pdt.codigoPlanoCondicaoPagamento ===
					event.row.codigoPlanoCondicaoPagamento
				) {
					indexPlanoDuracaoTable = i;
					return pdt;
				}
			});

			this.planosDuracoes.splice(indexPlanoDuracaoTable, 1);

			const indexCondicaoPagamentoPlanosDuracoes =
				this.condicaoPagamentoPlanosDuracoes.findIndex(
					(c) =>
						c.plano === event.row.plano.codigo &&
						c.planoDuracao === event.row.planoDuracao.codigo
				);
			if (indexCondicaoPagamentoPlanosDuracoes !== -1) {
				this.condicaoPagamentoPlanosDuracoes.splice(
					indexCondicaoPagamentoPlanosDuracoes,
					1
				);
			}
		} else {
			this.planosDuracoes.splice(event.index, 1);
		}
		this.atualizarCondicaoPagamentoPlanos.emit(
			this.condicaoPagamentoPlanosDuracoes
		);
		this.createCondicaoPagamentoPlanoPageObject();
	}

	editCondicaoPagamentoPlano(event) {
		this.planoDuracaoAnterior = event.row.planoDuracao.codigo;
		if (event && event.row.plano) {
			if (event.row.plano.duracoes) {
				this.onPlanoChange(event.row.plano, event.form);
			}
		}
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		let result = !this.verifyIfNewExists(row, data, rowIndex);
		if (result) {
			result = this.validatePlanoRow(row, form);
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("table-condicao-pagamento-plano-dado-duplicado")
			);
		}
		if (result) {
			this.clearInputOptionsDuracao();
		}
		return result;
	}

	private clearInputOptionsDuracao() {
		// remover opcoes de duracao que foram adicionadas no metodo "onPlanoChange", para que a proxima linha que o usuario tentar editar seja selecionada corretamente.
		const duracao = this.tableCondPgPlano.columns.find(
			(v) => v.nome === "planoDuracao"
		);
		duracao.inputSelectData = undefined;
	}

	verifyIfNewExists(row, dataTable, rowIndex) {
		let exists = false;
		dataTable.find((data, index) => {
			if (index !== rowIndex) {
				exists = this.compareObjects(row, data, "codigo", "edit", "codigo");
				if (exists) {
					return;
				}
			}
		});
		return exists;
	}

	validatePlanoRow(row, form) {
		const result = true;
		const plano = form.get("plano").value;
		const planoDuracao = form.get("planoDuracao").value;

		if (!plano || plano === "" || plano.descricao === "-") {
			this.notificationService.error(
				this.traducoes.getLabel(
					"table-condicao-pagamento-plano-error-plano-vazio"
				)
			);
			return false;
		}

		if (
			!planoDuracao ||
			planoDuracao === "" ||
			planoDuracao.numeroMeses === "-"
		) {
			this.notificationService.error(
				this.traducoes.getLabel(
					"table-condicao-pagamento-plano-error-duracao-vazia"
				)
			);
			return false;
		}

		return result;
	}

	compareObjects(obj1, obj2, uniqueKey?, ...keysAvoidCompare) {
		let equals = true;
		Object.keys(obj1).forEach((key) => {
			if (keysAvoidCompare.find((v) => v === key)) {
				return;
			}
			if (
				obj1[key] !== undefined &&
				obj2[key] !== undefined &&
				obj1[key] !== null &&
				obj2[key] !== null
			) {
				if (typeof obj1[key] !== "object") {
					if (obj1[key].toString() !== obj2[key].toString()) {
						equals = false;
						return;
					}
				} else {
					if (uniqueKey) {
						if (obj1[key][uniqueKey] !== obj2[key][uniqueKey]) {
							equals = false;
							return;
						}
					} else {
						equals = this.compareObjects(obj1[key], obj2[key]);
					}
				}
			}
		});
		return equals;
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
		this.onPlanoChange();
	}

	private onPlanoChange(option?: any, form?: any, row?: any) {
		const duracao = this.tableCondPgPlano.columns.find(
			(v) => v.nome === "planoDuracao"
		);
		const plano = this.tableCondPgPlano.columns.find((v) => v.nome === "plano");
		this.planos = plano.inputSelectData;
		if (
			option != null &&
			option.codigo != null &&
			option.duracoes &&
			option.duracoes.length > 0
		) {
			duracao.endpointUrl = undefined;
			const options = new Array<any>();
			options.push({
				codigo: undefined,
				numeroMeses: this.traducoes.getLabel(
					"table-condicao-pagamento-plano-todas"
				),
			});
			option.duracoes.forEach((value) => {
				options.push(value);
			});
			duracao.inputSelectData = options;
		} else if (
			option.descricao ===
			this.traducoes.getLabel("table-condicao-pagamento-plano-todos")
		) {
			const options = new Array<any>();
			options.push({
				codigo: undefined,
				numeroMeses: this.traducoes.getLabel(
					"table-condicao-pagamento-plano-todas"
				),
			});
			plano.inputSelectData.forEach((p) => {
				p.duracoes.forEach((pd) => {
					const index = options.findIndex(
						(op) => op.numeroMeses === pd.numeroMeses
					);
					if (index === -1) {
						options.push(pd);
					}
				});
			});
			duracao.inputSelectData = options;
		}
		duracao.inputSelectData = duracao.inputSelectData.sort((a: any, b: any) => {
			if (a.numeroMeses < b.numeroMeses) {
				return -1;
			} else if (a.numeroMeses > b.numeroMeses) {
				return 1;
			}
			return 0;
		});
	}

	createCondicaoPagamentoPlanoPageObject(page = 1, size = 10) {
		this.planosDuracoesTable.totalElements = this.planosDuracoes.length;
		this.planosDuracoesTable.size = size;
		this.planosDuracoesTable.totalPages = Math.ceil(
			+(this.planosDuracoesTable.totalElements / this.planosDuracoesTable.size)
		);
		this.planosDuracoesTable.first = page === 0 || page === 1;
		this.planosDuracoesTable.last =
			page === this.planosDuracoesTable.totalPages;
		this.planosDuracoesTable.content = this.planosDuracoes.slice(
			size * page - size,
			size * page
		);
		this.tableCondPgComponent.reloadData();
	}

	changePageCondicaoPagamentoPlano(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createCondicaoPagamentoPlanoPageObject(this.page, this.size);
	}

	changePageSizeCondicaoPagamentoPlano(size) {
		if (!isNaN(size)) {
			this.size = size;
		}
		this.createCondicaoPagamentoPlanoPageObject(this.page, this.size);
	}
}
