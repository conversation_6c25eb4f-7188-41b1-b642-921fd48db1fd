<pacto-cat-table-editable
	#tableCondPgComponent
	(confirm)="confirm($event)"
	(delete)="delete($event)"
	(edit)="editCondicaoPagamentoPlano($event)"
	(isEditingOrAddingItem)="isEditingOrAdding($event)"
	(pageChangeEvent)="changePageCondicaoPagamentoPlano($event)"
	(pageSizeChange)="changePageSizeCondicaoPagamentoPlano($event)"
	[baseFilter]="baseFilter"
	[isEditable]="true"
	[showAddRow]="true"
	[table]="tableCondPgPlano"
	idSuffix="nova-condicao-pagamento-plano"></pacto-cat-table-editable>

<ng-template #columnPlanoDescricao>
	<span i18n="@@adm:table-condicao-pagamento-plano-column-plano">Plano</span>
</ng-template>

<ng-template #columnPlanoDuracao>
	<span i18n="@@adm:table-condicao-pagamento-plano-column-duracao">
		Duração
	</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@adm:table-condicao-pagamento-plano-dado-duplicado"
		xingling="table-condicao-pagamento-plano-dado-duplicado">
		Já existe um dado na tabela com estes valores.
	</span>
	<span
		i18n="@@adm:table-condicao-pagamento-plano-error-plano-vazio"
		xingling="table-condicao-pagamento-plano-error-plano-vazio">
		Por favor selecione um plano!
	</span>
	<span
		i18n="@@adm:table-condicao-pagamento-plano-error-duracao-vazia"
		xingling="table-condicao-pagamento-plano-error-duracao-vazia">
		Por favor selecione uma duração!
	</span>
	<span
		i18n="@@adm:table-condicao-pagamento-plano-todos"
		xingling="table-condicao-pagamento-plano-todos">
		Todos
	</span>
	<span
		i18n="@@adm:table-condicao-pagamento-plano-todas"
		xingling="table-condicao-pagamento-plano-todas">
		Todas
	</span>
</pacto-traducoes-xingling>
