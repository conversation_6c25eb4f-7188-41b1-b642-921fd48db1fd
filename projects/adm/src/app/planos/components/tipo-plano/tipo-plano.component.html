<adm-layout
	(goBack)="voltarListagemPlanos()"
	i18n-modulo="@@tipoPlano:modulo"
	i18n-pageTitle="@@tipoPlano:pageTitle"
	modulo="Administrativo"
	pageTitle="Tipo de plano">
	<div class="table-wrapper">
		<pacto-relatorio
			#tableData
			(btnAddClick)="novoTipoPlano()"
			(iconClick)="iconClickFn($event)"
			(rowClick)="editTipoPlano($event)"
			[enableDs3]="true"
			[showBtnAdd]="true"
			[showShare]="true"
			[table]="table"
			i18n-labelBtnAdd="@@label-add-btn"
			labelBtnAdd="Adicionar"
			telaId="tipoPlano"></pacto-relatorio>
	</div>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	<span in18n="@@tipoPlano:ativo" xingling="Ativo">Ativo</span>
	<span in18n="@@tipoPlano:inativo" xingling="Inativo">Inativo</span>
	<span i18n="@@tipoPlano:tipoPlanoExcluido" xingling="TipoPlanoExcluido">
		Tipo de plano excluído com sucesso!
	</span>
	<span
		i18n="@@tipoPlano:editar_TipoPlano_nome"
		xingling="editar_TipoPlano_nome">
		Editar
	</span>
	<span
		i18n="@@tipoPlano:editar_TipoPlano_tooltip"
		xingling="editar_TipoPlano_tooltip">
		Editar um tipo de plano
	</span>
	<span
		i18n="@@tipoPlano:excluir_TipoPlano_nome"
		xingling="excluir_TipoPlano_nome">
		Excluir
	</span>
	<span
		i18n="@@tipoPlano:excluir_TipoPlano_tooltip"
		xingling="excluir_TipoPlano_tooltip">
		Excluir um tipo de plano
	</span>
</pacto-traducoes-xingling>

<ng-template #columnCodigo>
	<span i18n="@@adm:table-column-codigo-tipoPlano">Código</span>
</ng-template>

<ng-template #columnNome>
	<span i18n="@@adm:table-column-nome-tipoPlano">Nome</span>
</ng-template>

<ng-template #columnAtivo>
	<span i18n="@@adm:table-column-ativo-tipoPlano">Ativo</span>
</ng-template>
