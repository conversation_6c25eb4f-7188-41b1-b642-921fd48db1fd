import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { Api } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { PlanoApiTipoPlanoService, TipoPlano } from "plano-api";

@Component({
	selector: "adm-tipo-plano",
	templateUrl: "./tipo-plano.component.html",
	styleUrls: ["./tipo-plano.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TipoPlanoComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnAtivo", { static: true }) columnAtivo: TemplateRef<any>;
	table: PactoDataGridConfig;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private tipoPlanoService: PlanoApiTipoPlanoService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.initTable();
	}

	editTipoPlano(tipoPlano) {
		this.router.navigate(["adm", "planos", "tipo-plano", tipoPlano.codigo]);
	}

	deleteTipoPLano(tipoPlano: TipoPlano) {
		this.tipoPlanoService.delete(tipoPlano.codigo).subscribe(
			(response) => {
				const message = this.traducao.getLabel("TipoPlanoExcluido");
				this.notificationService.success(message);
				this.tableData.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	novoTipoPlano() {
		this.router.navigate(["adm", "planos", "novo-tipo-plano"]);
	}

	voltarListagemPlanos() {
		this.router.navigate(["adm", "planos"]);
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("tipoPlano", false, Api.MSPLANO),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "nome",
						titulo: this.columnNome,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "ativo",
						titulo: this.columnAtivo,
						visible: true,
						ordenavel: true,
						valueTransform: (v) =>
							v
								? this.traducao.getLabel("Ativo")
								: this.traducao.getLabel("Inativo"),
					},
				],
				dropDownActions: [
					{
						nome: this.traducao.getLabel("editar_TipoPlano_nome"),
						iconClass: "",
						tooltipText: this.traducao.getLabel("editar_TipoPlano_tooltip"),
						actionFn: (row) => this.editTipoPlano(row),
					},
					{
						nome: this.traducao.getLabel("excluir_TipoPlano_nome"),
						iconClass: "",
						tooltipText: this.traducao.getLabel("excluir_TipoPlano_tooltip"),
						actionFn: (row) => this.deleteTipoPLano(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editTipoPlano(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deleteTipoPLano(event.row);
		}
	}
}
