<adm-layout
	(goBack)="voltarListagem()"
	[showConfirmDialog]="true"
	i18n-modulo="@@tipoPlano:modulo"
	i18n-pageTitle="@@tipoPlano:pageTitle"
	i18n-subtitle="@@tipoPlano:subtitle"
	modulo="Administrativo"
	pageTitle="Tipo de plano"
	subtitle="Informe os dados abaixo">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<div class="row">
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="codigoControl"
						i18n-label="@@tipoPlano:codigo_label"
						label="Código"
						placeholder="000"
						readonly="true"></pacto-cat-form-input-number>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-input
						[control]="form.get('nome')"
						i18n-label="@@tipoPlano:nome_label"
						i18n-placeholder="@@tipoPlano:nome_placeholder"
						label="Nome"
						placeholder="Nome"></pacto-cat-form-input>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-input
						[control]="form.get('tipo')"
						i18n-label="@@tipoPlano:tipo_label"
						i18n-placeholder="@@tipoPlano:tipo_placeholder"
						label="Tipo"
						placeholder="Tipo"></pacto-cat-form-input>
				</div>
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-input
						[control]="form.get('limiteVendas')"
						i18n-label="@@tipoPlano:limite_vendas"
						label="Limitação de Vendas"
						i18n-placeholder="@@tipoPlano:limite_vendas_placeholder"
						placeholder="Limitação de Vendas"></pacto-cat-form-input>
				</div>
			</div>
			<div class="row">
				<div class="checkbox-sms">
					<pacto-cat-checkbox
						[control]="form.get('ativo')"
						i18n-label="@@tipoPlano:sms_label"
						label="Ativo"></pacto-cat-checkbox>
				</div>
			</div>

			<div class="row">
				<p i18n="@@tipoPlano:codigoOperacao_p">
					Códigos de operação financeira por tipo de produto:
				</p>
			</div>
			<div class="row">
				<ng-container *ngFor="let tipoProduto of tiposProdutoDesc">
					<div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
						<pacto-cat-form-input
							[control]="tipoProduto.codigooperacaofinanceiraControl"
							[label]="tipoProduto.desc"
							[placeholder]="tipoProduto.desc"></pacto-cat-form-input>
					</div>
				</ng-container>
			</div>
			<div class="row justify-content-end">
				<pacto-cat-button
					(click)="voltarListagem()"
					i18n-label="@@tipoPlano:cancelar_label"
					label="Cancelar"
					style="margin-right: 10px"
					type="OUTLINE_DARK"></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					i18n-label="@@tipoPlano:salvar_label"
					label="Salvar"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@tipoPlano:MA" xingling="MA">Matrícula</span>
	<span i18n="@@tipoPlano:RE" xingling="RE">Rematrícula</span>
	<span i18n="@@tipoPlano:RN" xingling="RN">Renovação</span>
	<span i18n="@@tipoPlano:PM" xingling="PM">Mês de referência plano</span>
	<span i18n="@@tipoPlano:SE" xingling="SE">Serviço</span>
	<span i18n="@@tipoPlano:AA" xingling="AA">Aula avulsa</span>
	<span i18n="@@tipoPlano:DI" xingling="DI">Diária</span>
	<span i18n="@@tipoPlano:TD" xingling="TD">
		Taxa de adesão plano recorrência
	</span>
	<span i18n="@@tipoPlano:TN" xingling="TN">Taxa de renegociação</span>
	<span i18n="@@tipoPlano:TA" xingling="TA">
		Taxa de anuidade plano recorrência
	</span>
	<span
		i18n="@@tipoPlano:PLANO_TIPO_SALVO_SUCESSO"
		xingling="PLANO_TIPO_SALVO_SUCESSO">
		Tipo De Plano cadastrado com sucesso
	</span>
</pacto-traducoes-xingling>
