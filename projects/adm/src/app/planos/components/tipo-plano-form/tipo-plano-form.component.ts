import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { TraducoesXinglingComponent } from "ui-kit";
import {
	PlanoApiTipoPlanoService,
	PlanoTipoTipoProduto,
	TipoPlano,
} from "plano-api";

@Component({
	selector: "adm-tipo-plano-form",
	templateUrl: "./tipo-plano-form.component.html",
	styleUrls: ["./tipo-plano-form.component.scss"],
})
export class TipoPlanoFormComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;

	codigoControl: FormControl = new FormControl();
	planoTipo: TipoPlano = new TipoPlano();
	form: FormGroup;
	formPlanoTipoTipoProduto: FormGroup;
	id;
	saved = false;
	tiposProdutoDesc: Array<{
		simbolo: string;
		desc: string;
		codigooperacaofinanceiraControl: FormControl;
		codigo: FormControl;
	}> = new Array<{
		simbolo: string;
		desc: string;
		codigooperacaofinanceiraControl: FormControl;
		codigo: FormControl;
	}>();

	constructor(
		private router: Router,
		private planoTipoService: PlanoApiTipoPlanoService,
		private activatedRoute: ActivatedRoute,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef
	) {}

	initArray() {
		setTimeout(() => {
			const tiposProdutoDescTranslate = new Array<{
				simbolo: string;
				desc: string;
				codigooperacaofinanceiraControl: FormControl;
				codigo: FormControl;
			}>();

			tiposProdutoDescTranslate.push({
				simbolo: "MA",
				desc: this.traducao.getLabel("MA"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "RE",
				desc: this.traducao.getLabel("RE"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "RN",
				desc: this.traducao.getLabel("RN"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "PM",
				desc: this.traducao.getLabel("PM"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "SE",
				desc: this.traducao.getLabel("SE"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "AA",
				desc: this.traducao.getLabel("AA"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "DI",
				desc: this.traducao.getLabel("DI"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "TD",
				desc: this.traducao.getLabel("TD"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "TN",
				desc: this.traducao.getLabel("TN"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});
			tiposProdutoDescTranslate.push({
				simbolo: "TA",
				desc: this.traducao.getLabel("TA"),
				codigooperacaofinanceiraControl: new FormControl(),
				codigo: new FormControl(),
			});

			this.tiposProdutoDesc = tiposProdutoDescTranslate;
			this.cd.detectChanges();
		});
	}

	ngOnInit() {
		this.initArray();
		this.createForm();
		this.createFormProduto();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
		if (this.id) {
			this.planoTipoService.find(this.id).subscribe((response) => {
				this.planoTipo = response.content;
				this.tiposProdutoDesc.map((a) => {
					const tipoProduto = this.planoTipo.tiposProduto.find(
						(b) => a.simbolo === b.tipoProduto
					);
					if (tipoProduto) {
						a.codigooperacaofinanceiraControl.setValue(
							tipoProduto.codigoOperacaoFinanceira
						);
						a.codigo.setValue(tipoProduto.codigo);
					}
				});

				this.codigoControl.setValue(this.planoTipo.codigo);
				this.form.patchValue({
					nome: this.planoTipo.nome,
					tipo: this.planoTipo.tipo,
					ativo: this.planoTipo.ativo,
					limiteVendas: this.planoTipo.limiteVendas,
				});
			});
		}
	}

	voltarListagem() {
		this.router.navigate(["adm", "planos", "tipo-plano"]);
	}

	createForm() {
		this.form = new FormGroup({
			nome: new FormControl(),
			ativo: new FormControl(),
			tipo: new FormControl(),
			limiteVendas: new FormControl(),
		});
	}

	createFormProduto() {
		this.formPlanoTipoTipoProduto = new FormGroup({
			codigooperacaofinanceira: new FormControl(),
			tipoproduto: new FormControl(),
			codigo: new FormControl(),
		});
	}

	produtoSetTipo(name) {
		this.formPlanoTipoTipoProduto.patchValue({ tipoproduto: name });
	}

	salvar() {
		if (!this.saved) {
			this.planoTipo.tiposProduto = new Array<PlanoTipoTipoProduto>();
			this.tiposProdutoDesc.map((tipoProduto) => {
				const b = {
					codigoOperacaoFinanceira:
						tipoProduto.codigooperacaofinanceiraControl.value,
					tipoProduto: tipoProduto.simbolo,
					codigo: tipoProduto.codigo.value,
				};
				this.planoTipo.tiposProduto.push(b);
			});
			Object.keys(this.form.getRawValue()).forEach((key) => {
				this.planoTipo[key] = this.form.getRawValue()[key];
			});

			this.planoTipoService.save(this.planoTipo).subscribe(
				(response) => {
					const message = this.traducao.getLabel("PLANO_TIPO_SALVO_SUCESSO");
					this.notificationService.success(message);
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		}
	}
}
