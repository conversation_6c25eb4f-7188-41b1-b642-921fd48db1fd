import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { Plano, TipoPlano } from "../../../plano.model";
import { FormGroup } from "@angular/forms";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PrAdvancedConfigComponent } from "../../recorrencia/pr-advanced-config/pr-advanced-config.component";

@Component({
	selector: "adm-pp-dados-basicos",
	templateUrl: "./pp-dados-basicos.component.html",
	styleUrls: ["./pp-dados-basicos.component.scss"],
})
export class PpDadosBasicosComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	plano: Plano = new Plano();
	form: FormGroup;
	tiposPlano: Array<{ label: string; id: string }> = new Array<{
		label: string;
		id: string;
	}>();
	produtoelectBuilder: SelectFilterParamBuilder;
	produtosContrato: Array<any> = new Array<any>();
	@Output() nome: EventEmitter<string> = new EventEmitter();
	constructor(
		private planoCommonsService: PlanoCommonsService,
		private planoStateService: PlanoStateService,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.form = this.planoCommonsService.formDadosBasicos(
			TipoPlano.PLANO_PERSONAL
		);
		this.produtoelectBuilder = this.planoCommonsService.produtoSelectBuilder;
		this.setTiposPlano();
		this.planoCommonsService.updateFormDadosBasicos(this.form, this.plano);
		this.form.valueChanges.subscribe((value) => {
			this.planoCommonsService.updatePlanoDadosBasicos(value, this.plano);
		});
		this.loadProdutosContrato();
		this.nome.emit(this.form.controls["descricao"].value);
		this.form.controls["descricao"].valueChanges.subscribe((v) => {
			this.nome.emit(v);
		});
		this.form.updateValueAndValidity();
	}

	async setTiposPlano() {
		this.tiposPlano = await this.planoCommonsService.tiposPlano(this.traducoes);
		this.cd.detectChanges();
	}

	loadProdutosContrato() {
		this.planoCommonsService
			.loadProdutosContrato()
			.subscribe((response: any) => {
				this.produtosContrato = response.content;
				if (
					(this.planoStateService.plano &&
						this.planoStateService.plano.codigo === undefined) ||
					this.planoStateService.plano.codigo === null
				) {
					this.form
						.get("produtoContrato")
						.setValue(
							this.produtosContrato.find((prod) => prod.tipoProduto === "PM")
						);
				}
			});
	}

	configuracoesPlano() {
		const dialogRef = this.dialogService.open(
			this.traducoes.getLabel("advanced-config"),
			PrAdvancedConfigComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoStateService.updatePlanoObj();
		if (dialogRef.result) {
			dialogRef.result.then((value) => {}).catch((error) => {});
		}
	}
}
