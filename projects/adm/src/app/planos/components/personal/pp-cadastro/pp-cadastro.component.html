<ng-container *ngIf="loading">
	<div class="loader">
		<img src="assets/images/loading.svg" />
		<span i18n="@@plano:loading-message">Carregando plano...</span>
	</div>
</ng-container>
<pacto-cat-layout-v2 *ngIf="!loading">
	<pacto-cat-stepper-simple (goBack)="voltarParaListagemPlano()">
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepDescription-basico"
			i18n-stepLabel="@@plano:steplabel-basico"
			stepDescription="Informe os dados necessários para criar um novo plano"
			stepLabel="Dados Básicos">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[disabled]="
						dadosBasicosComponent.form && dadosBasicosComponent.form.invalid
					"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-dados-basicos"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pp-dados-basicos
				#dadosBasicosComponent
				(nome)="planoNome = $event"></adm-pp-dados-basicos>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			stepLabel="{{ planoNome | captalize }} / Dados Contraturais"
			i18n-stepDescription="@@plano:stepdescription-dadoscontratuais"
			i18n-stepLabel="@@plano:steplabel-dadoscontratuais"
			stepDescription="Defina os termos de contratação do seu plano.">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-dados-contraturais"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					*ngIf="planoStateService.plano.apresentarVendaRapida"
					[disabled]="!dadosContratuaisComponent.form?.valid"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-dados-contratuais"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					*ngIf="!planoStateService.plano.apresentarVendaRapida"
					[disabled]="!dadosContratuaisComponent.form?.valid || isSaving"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-concluir"
					id="btn-plano-concluir-dados-contratuais"
					label="Concluir"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pp-dados-contratuais
				#dadosContratuaisComponent></adm-pp-dados-contratuais>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			*ngIf="planoStateService.plano.apresentarVendaRapida"
			i18n-stepDescription="@@adm:stepDescription-empresas"
			i18n-stepLabel="@@adm:steplabel-empresas"
			stepDescription="Defina as empresas que o plano pode ser vendido."
			stepLabel="Empresas">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-empresa"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					[disabled]="isSaving"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-concluir"
					id="btn-plano-concluir-empresa"
					label="Concluir"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-plano-empresa></adm-plano-empresa>
		</pacto-cat-step-simple>
	</pacto-cat-stepper-simple>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:saved-success" xingling="saved-success">
		Plano salvo com sucesso.
	</span>
</pacto-traducoes-xingling>
