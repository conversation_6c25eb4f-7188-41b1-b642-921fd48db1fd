<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[addtionalFilters]="modeloContratoAddtionalFilters"
					[control]="form.get('modeloContrato')"
					[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
					[idKey]="'codigo'"
					[labelKey]="'descricao'"
					[paramBuilder]="modeloContratoSelectBuilder"
					errorMsg="Selecione um modelo de contrato"
					i18n-errorMsg="@@plano:error-modelo-contrato"
					i18n-label="@@plano:label-modelo-contrato"
					id="select-modelo-contrato-plano"
					label="Modelo de contrato do plano"></pacto-cat-form-select-filter>

				<pacto-cat-button
					class="mx-auto d-block"
					(click)="aplicarContratosJaLancados()"
					label="Aplicar à contratos já lançados"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					ds3="true"
					id="btn-advanced-config-contratual-plano"></pacto-cat-button>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-input-number
					[formControl]="form.get('percentualMultaCancelamento')"
					decimal="true"
					i18n-label="@@plano:label-percentual-multa-cancelamento"
					id="input-percentual-multa-cancelamento-plano"
					label="Percentual da multa de cancelamento"
					max="100"
					maxlength="5"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[control]="form.get('produtoTaxaCancelamento')"
					[endpointUrl]="
						admRestService.buildFullUrlPlano(
							produtoService.produtosCancelamento
						)
					"
					[paramBuilder]="produtoSelectBuilder"
					errorMsg="Por favor selecione um produto"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@plano:error-produto-taxa-cancelamento"
					i18n-label="@@plano:label-produto-taxa-cancelamento"
					id="select-produto-taxa-cancelamento-plano"
					idKey="codigo"
					label="Produto taxa cancelamento:"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-input-number
					[formControl]="form.get('valorMensal')"
					decimal="true"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@adm:error-campo-obrigatorio"
					i18n-label="@@plano:label-valor-mensalidade"
					id="input-valor-mensalidade"
					label="Valor da mensalidade"></pacto-cat-form-input-number>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-input-number
					[decimal]="true"
					[formControl]="form.get('taxaAdesao')"
					i18n-label="@@plano:label-taxa-adesao"
					id="input-taxa-adesao"
					label="Taxa de adesão"></pacto-cat-form-input-number>
			</div>

			<div class="col-md-4">
				<pacto-cat-form-input-number
					[decimal]="false"
					[formControl]="form.get('duracaoPlano')"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@adm:error-campo-obrigatorio"
					i18n-label="@@plano:label-fidelidade-plano"
					id="input-fidelidade-plano"
					label="Fidelidade do plano (meses)"></pacto-cat-form-input-number>
			</div>
		</div>
		<div class="row mb-3">
			<div class="col-md-6">
				<pacto-cat-checkbox
					[control]="form.get('permiteSituacaoAtestadoContrato')"
					i18n-label="@@plano:label-permite-atestado-medico"
					id="check-permite-atestado-medico"
					label="Permite atestado médico"></pacto-cat-checkbox>
			</div>
		</div>

		<div class="row">
			<div class="col-md-5">
				<pacto-cat-button
					(click)="configuracoesAvancadas()"
					[label]="traducoes.getLabel('advanced-config')"
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					id="btn-advanced-config-contratual"></pacto-cat-button>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@adm:advanced-config" xingling="advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
