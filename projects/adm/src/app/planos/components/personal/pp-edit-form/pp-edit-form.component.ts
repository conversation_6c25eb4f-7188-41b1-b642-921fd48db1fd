import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { PerfilAcessoRecursoNome } from "../../../../perfil-acesso/perfil-acesso-recurso.model";
import { TraducoesXinglingComponent } from "ui-kit";
import { PlanoConfiguracaoSistema } from "../../../services/plano-configuracao-sistema.model";

@Component({
	selector: "adm-pp-edit-form",
	templateUrl: "./pp-edit-form.component.html",
	styleUrls: ["./pp-edit-form.component.scss"],
})
export class PpEditFormComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	isSaving = false;
	plano: Plano;
	recurso: PerfilAcessoRecurso;
	configuracaoSistema: PlanoConfiguracaoSistema;
	planoNome = "";
	constructor(
		public planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private planoService: PlanoService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {
		this.recurso = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PLANO
		);
	}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.plano = this.planoCommonsService.populateModalidadeAux(this.plano);
		this.plano = this.planoCommonsService.populateHorarioValor(this.plano);
		this.planoStateService.updateState(this.plano);
	}

	goBack() {
		this.planoCommonsService.voltarParaListagem();
	}

	save() {
		if (!this.isSaving) {
			const plano = this.planoCommonsService.convertPlano(
				this.planoStateService.updatePlanoObj()
			);
			if (plano.planoRecorrencia) {
				if (!plano.planoRecorrencia.anuidadeNaParcela) {
					plano.planoRecorrencia.parcelarAnuidade = undefined;
					plano.planoRecorrencia.parcelasAnuidade = undefined;
					plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					plano.planoRecorrencia.diaAnuidade = undefined;
					plano.planoRecorrencia.mesAnuidade = undefined;
				}
				if (plano.planoRecorrencia.parcelarAnuidade) {
					plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					plano.planoRecorrencia.parcelasAnuidade = undefined;
				}
				if (plano.planoRecorrencia.parcelasAnuidade) {
					plano.planoRecorrencia.parcelasAnuidade.forEach((parcelaAnuidade) => {
						if (parcelaAnuidade.parcela && parcelaAnuidade.parcela.value) {
							parcelaAnuidade.parcela = parcelaAnuidade.parcela.value;
						}
					});
				}
			}
			this.isSaving = true;
			this.planoService.save(plano).subscribe(
				(response) => {
					if ((this.plano && this.plano.codigo && this.plano.codigo > 0) && (this.configuracaoSistema && this.configuracaoSistema.permitirreplicarplanoredeempresa)) {
						this.planoService.replicarAutomaticoTodosReplicados(this.plano.codigo).subscribe((ret) => {
						});
					}
					this.isSaving = false;
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					this.goBack();
				},
				(responseError) => {
					this.isSaving = false;
					this.cd.detectChanges();
					if (responseError.error) {
						if (responseError.error.meta) {
							this.notificationService.error(
								responseError.error.meta.messageValue
							);
						}
					}
				}
			);
		}
	}
}
