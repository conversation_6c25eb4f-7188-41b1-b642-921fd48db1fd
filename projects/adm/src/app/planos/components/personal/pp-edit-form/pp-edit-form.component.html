<adm-layout
	(goBack)="goBack()"
	[showConfirmDialog]="true"
	i18n-modulo="@@plano:modulo"
	i18n-pageTitle="@@plano:edit-page-title"
	modulo="Administrativo"
	pageTitle="Editar Plano: {{ planoNome | captalize }}">
	<pacto-cat-tabs-transparent
		(action)="save()"
		[actionLabel]="'Salvar'"
		[showAction]="!isSaving && recurso.editar">
		<ng-template
			i18n-label="@@plano:edit-label-dados-basicos"
			label="Dados Básicos"
			pactoTabTransparent>
			<adm-pr-dados-basicos (nome)="planoNome = $event"></adm-pr-dados-basicos>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-dados-contratuais"
			label="Dados Contratuais"
			pactoTabTransparent>
			<adm-pp-dados-contratuais></adm-pp-dados-contratuais>
		</ng-template>
		<ng-template
			*ngIf="planoStateService.plano.apresentarVendaRapida"
			i18n-label="@@plano:edit-label-empresas"
			label="Empresas"
			pactoTabTransparent>
			<adm-plano-empresa></adm-plano-empresa>
		</ng-template>
		<ng-template
			*ngIf="configuracaoSistema?.permitirreplicarplanoredeempresa"
			i18n-label="@@plano:edit-label-empresas"
			label="Replicar empresa"
			pactoTabTransparent>
			<adm-replicar-plano [plano]="plano"></adm-replicar-plano>
		</ng-template>
	</pacto-cat-tabs-transparent>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:saved-success" xingling="saved-success">
		Plano salvo com sucesso.
	</span>
</pacto-traducoes-xingling>
