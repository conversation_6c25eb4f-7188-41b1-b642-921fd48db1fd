import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	Desconto,
	DescontoEmpresa,
	DescontoRenovacao,
	getTipoDescontoText,
	PlanoApiDescontoEmpresaService,
	PlanoApiDescontoService,
	TipoIntervalo,
} from "plano-api";
import { getTipoProdutoText } from "produto-api";
import { PlanoCommonsService } from "../../services/plano-commons.service";
import { switchMap } from "rxjs/operators";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "adm-desconto-form",
	templateUrl: "./desconto-form.component.html",
	styleUrls: ["./desconto-form.component.scss"],
})
export class DescontoFormComponent implements OnInit, AfterViewInit {
	/*
	 *
	 * utlizaEmpresa: ele é uma flag para verificar se será ou não inserida no descontoempresa
	 *   se marcado, então adicona no array de descontoempresa
	 *   caso contratio, o contratio
	 *  itera no descontoempresa do desconto que estamos editando comparando as empresas buscadas pelo findbydesconto
	 *
	 * */

	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("traducaoIntervalo", { static: true })
	traducaoIntervalo: TraducoesXinglingComponent;
	@ViewChild("traducaoDesconto", { static: true })
	traducaoDesconto: TraducoesXinglingComponent;
	@ViewChild("tableComponent", { static: false })
	tableComponent: CatTableEditableComponent;
	@ViewChild("tableRenovacaoComponent", { static: false })
	tableRenovacaoComponent: CatTableEditableComponent;
	table: PactoDataGridConfig;
	tableRenov: PactoDataGridConfig;
	form: FormGroup;
	codigoControl: FormControl = new FormControl();
	isEdittingTableRenovacao = false;
	desconto: Desconto = new Desconto();
	empresas: Array<{
		utilizaEmpresa?: boolean;
		codigo: number;
		codigoEmpresa: number;
		cidade: string;
		estado: string;
		nome: string;
		setor: string;
	}> = new Array<{
		utilizaEmpresa?: boolean;
		codigo: number;
		codigoEmpresa: number;
		cidade: string;
		estado: string;
		nome: string;
		setor: string;
	}>();
	descontoRenovacoes: Array<{
		codigo: number;
		tipoIntervalo: { id: string; label: string };
		intervaloDe: number;
		intervaloAte: number;
		tipoDesconto: { id: string; label: string };
		valor: number;
		justificativaOperacao: { codigo: number; descricao: string };
	}> = new Array<{
		codigo: number;
		tipoIntervalo: { id: string; label: string };
		intervaloDe: number;
		intervaloAte: number;
		tipoDesconto: { id: string; label: string };
		valor: number;
		justificativaOperacao: { codigo: number; descricao: string };
	}>();
	getTipoProdutoText: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>();
	getTipoDescontoText: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>();
	getTipoIntervaloText: Array<{ id: string; label: string }> = new Array<{
		id: string;
		label: string;
	}>({ id: "AT", label: "Atrasado" }, { id: "AN", label: "Antecipado" });
	id;

	constructor(
		private descontoService: PlanoApiDescontoService,
		private descontoEmpresaService: PlanoApiDescontoEmpresaService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService,
		private admRestService: AdmRestService,
		private cd: ChangeDetectorRef,
		private planoCommonsService: PlanoCommonsService
	) {}

	ngOnInit() {
		this.createForm();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
	}

	ngAfterViewInit() {
		this.initTipoIntervaloArray();
		this.initTipoDescontoArray();
		this.initTipoProdutoArray();
		this.initTable();
		this.initRenovTable();
		if (this.id) {
			this.descontoEmpresaService
				.findByDesconto(this.id)
				.pipe(
					switchMap((response) => {
						this.populateEmpresas(response);
						return this.descontoService.find(this.id);
					})
				)
				.subscribe((response) => {
					this.desconto = response.content;
					this.codigoControl.setValue(this.desconto.codigo);
					this.form.patchValue({
						descricao: this.desconto.descricao,
						tipoProduto: this.desconto.tipoProduto,
						tipoDesconto: this.desconto.tipoDesconto,
						ativo: this.desconto.ativo,
						aplicarEmpresas: this.desconto.aplicarEmpresas,
						valor: this.desconto.valor,
					});
					if (!this.desconto.descontoRenovacoes) {
						this.desconto.descontoRenovacoes = new Array<DescontoRenovacao>();
					}
					if (!this.desconto.descontoEmpresas) {
						this.desconto.descontoEmpresas = new Array<DescontoEmpresa>();
					}
					this.desconto.descontoRenovacoes.forEach((dr) => {
						if (!this.desconto.descontoRenovacoes) {
							this.desconto.descontoRenovacoes = new Array<DescontoRenovacao>();
						}
						const descontoRenovacao: any = {
							codigo: dr.codigo,
							tipoIntervalo: {
								id: dr.tipoIntervalo,
								label: this.traducaoIntervalo.getLabel(dr.tipoIntervalo),
							},
							intervaloDe: dr.intervaloDe,
							intervaloAte: dr.intervaloAte,
							tipoDesconto: {
								id: dr.tipoDesconto,
								label: this.traducaoDesconto.getLabel(dr.tipoDesconto),
							},
							valor: dr.valor,
						};
						if (dr.justificativaOperacao) {
							descontoRenovacao.justificativaOperacao = {
								codigo: dr.justificativaOperacao.codigo,
								descricao: dr.justificativaOperacao.descricao,
							};
						}
						this.descontoRenovacoes.push(descontoRenovacao);
					});
					this.cd.detectChanges();
					if (this.tableComponent) {
						this.tableComponent.reloadData();
					}
					if (this.tableRenovacaoComponent) {
						this.tableRenovacaoComponent.reloadData();
					}
					this.empresas.forEach((emp) => {
						const empr = this.desconto.descontoEmpresas.find(
							(de) => de.empresa.codigo === emp.codigoEmpresa
						);
						if (empr) {
							emp.utilizaEmpresa = true;
						}
					});
				});
		} else {
			this.descontoEmpresaService
				.findByDesconto(this.id)
				.subscribe((response) => {
					this.populateEmpresas(response);
				});
		}
	}

	createForm() {
		this.form = new FormGroup({
			descricao: new FormControl(this.desconto.descricao, [
				Validators.required,
			]),
			tipoProduto: new FormControl(),
			tipoDesconto: new FormControl(),
			ativo: new FormControl(),
			aplicarEmpresas: new FormControl(),
			tipoIntervalo: new FormControl(),
			intervaloDe: new FormControl(),
			intervaloAte: new FormControl(),
			valor: new FormControl(),
			justificativaOperacao: new FormControl(),
		});
		this.form.get("tipoProduto").valueChanges.subscribe((value) => {
			let i;
			const bonusItem = this.getTipoDescontoText.find((item, index) => {
				if (item.id === "BO") {
					i = index;
					return item;
				}
			});
			if (value !== "DR") {
				if (i) {
					this.getTipoDescontoText.splice(i, 1);
				}
			} else {
				if (!i) {
					this.getTipoDescontoText.push({
						label: this.traducaoDesconto.getLabel("BO"),
						id: "BO",
					});
				}
			}
		});
	}

	sortEmpreas() {
		this.empresas.sort((a, b) => {
			if (a.nome < b.nome) {
				return -1;
			}
			if (a.nome > b.nome) {
				return 1;
			}
			return 0;
		});
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return { content: this.empresas };
			},
			formGroup: new FormGroup({
				utilizaEmpresa: new FormControl(),
			}),
			pagination: false,
			columns: [
				{
					nome: "utilizaEmpresa",
					titulo: "",
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					valueTransform: (v) =>
						v
							? this.traducoes.getLabel("YES_VALUE")
							: this.traducoes.getLabel("NO_VALUE"),
				},
				{
					nome: "nome",
					titulo: this.traducoes.getLabel("EMPRESA_LABEL"),
					ordenavel: false,
					visible: true,
				},
				{
					nome: "estado",
					titulo: this.traducoes.getLabel("ESTADO_LABEL"),
					visible: true,
					ordenavel: false,
				},
				{
					nome: "cidade",
					titulo: this.traducoes.getLabel("CIDADE_LABEL"),
					ordenavel: false,
					visible: true,
				},
				{
					nome: "setor",
					titulo: this.traducoes.getLabel("BAIRRO_LABEL"),
					ordenavel: false,
					visible: true,
				},
			],
		});
	}

	initRenovTable() {
		this.tableRenov = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return { content: this.descontoRenovacoes };
			},
			formGroup: new FormGroup({
				tipoIntervalo: new FormControl(),
				intervaloDe: new FormControl(),
				intervaloAte: new FormControl(),
				tipoDesconto: new FormControl(),
				valor: new FormControl(),
				justificativaOperacao: new FormControl(),
			}),
			pagination: false,
			beforeConfirm: (row: any, form: any, data: any, indexRow: any) =>
				this.beforConfirmDescontoRenov(row, form, data, indexRow),
			columns: [
				{
					nome: "tipoIntervalo",
					titulo: this.traducoes.getLabel("TIPO_INTERVALO"),
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					showAddSelectBtn: false,
					idSelectKey: "id",
					inputSelectData: this.getTipoIntervaloText,
					prefix: "tipo-intervalo",
				},
				{
					nome: "intervaloDe",
					titulo: this.traducoes.getLabel("INTERVALO_DE"),
					ordenavel: false,
					editable: true,
					visible: true,
					inputType: "number",
				},
				{
					nome: "intervaloAte",
					titulo: this.traducoes.getLabel("INTERVALO_ATE"),
					ordenavel: false,
					editable: true,
					visible: true,
					inputType: "number",
				},
				{
					nome: "tipoDesconto",
					titulo: this.traducoes.getLabel("TIPO_DESCONTO"),
					ordenavel: false,
					editable: true,
					visible: true,
					inputType: "select",
					showAddSelectBtn: false,
					inputSelectData: this.getTipoDescontoText,
					idSelectKey: "id",
				},
				{
					nome: "valor",
					titulo: this.traducoes.getLabel("VALOR"),
					ordenavel: false,
					editable: true,
					visible: true,
					inputType: "decimal",
					decimal: true,
					decimalPrecision: 2,
				},
				{
					nome: "justificativaOperacao",
					titulo: this.traducoes.getLabel("JUSTIFICATIVA_BONUS"),
					ordenavel: false,
					editable: true,
					visible: true,
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					inputType: "select",
					isDisabled: (row) => {
						if (row.tipoDesconto && row.tipoDesconto.id === "BO") {
							return false;
						}
						return true;
					},
					showAddSelectBtn: false,
					endpointUrl: `${this.admRestService.buildFullUrlPlano(
						"justificativa-operacao"
					)}/BO`,
				},
			],
		});
		if (this.tableRenov) {
			this.tableRenov.formGroup
				.get("tipoDesconto")
				.valueChanges.subscribe((value) => {
					if (value && value.id === "BO") {
						this.tableRenov.formGroup.get("justificativaOperacao").enable();
					} else {
						this.tableRenov.formGroup.get("justificativaOperacao").disable();
						this.tableRenov.formGroup
							.get("justificativaOperacao")
							.setValue(null);
					}
				});
			const columnDias = this.tableRenov.columns.find(
				(c) => c.nome === "valor"
			);
			if (!this.tableRenov.formGroup.get("tipoDesconto").value) {
				columnDias.titulo = this.traducaoDesconto.getLabel("VA");
			} else {
				if (this.tableRenov.formGroup.get("tipoDesconto").value.id) {
					columnDias.titulo = this.traducoes.getLabel("DIAS");
					columnDias.titulo = this.traducaoDesconto.getLabel("PE");
				}
			}
			this.tableRenov.formGroup
				.get("tipoDesconto")
				.valueChanges.subscribe((value) => {
					if (value && value.id === "BO") {
						columnDias.titulo = this.traducoes.getLabel("DIAS");
						columnDias.inputType = "number";
						columnDias.maxValue = undefined;
					} else if (value && value.id === "VA") {
						columnDias.titulo = this.traducaoDesconto.getLabel("VA");
						columnDias.inputType = "decimal";
						columnDias.decimalPrecision = 2;
						columnDias.maxValue = undefined;
					} else if (value && value.id === "PE") {
						columnDias.titulo = this.traducaoDesconto.getLabel("PE");
						columnDias.inputType = "decimal";
						columnDias.maxValue = 100;
						columnDias.decimalPrecision = 3;
					}

					this.cd.detectChanges();
				});
		}
	}

	private beforConfirmDescontoRenov(
		row: any,
		form: any,
		data: any,
		indexRow: any
	) {
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			indexRow
		);
		if (exists) {
			this.notificationService.error(this.traducoes.getLabel("DADO_TABELA"));
			return false;
		} else {
			const tipoDesconto = form.get("tipoDesconto").value;
			const justificativaOp = form.get("justificativaOperacao").value;
			if (tipoDesconto.id === "BO" && justificativaOp === null) {
				this.notificationService.error(
					this.traducoes.getLabel("VALIDA_JUSTOP")
				);
				return false;
			}
		}
		return !exists;
	}

	voltarListagem() {
		this.router.navigate(["adm", "planos", "desconto"]);
	}

	salvarDesconto() {
		if (!this.isEdittingTableRenovacao) {
			Object.keys(this.form.getRawValue()).forEach((key) => {
				this.desconto[key] = this.form.getRawValue()[key];
			});

			if (this.descontoRenovacoes.length > 0) {
				const drAux = new Array<DescontoRenovacao>();
				this.descontoRenovacoes.forEach((dr, index) => {
					if (!this.desconto.descontoRenovacoes) {
						this.desconto.descontoRenovacoes = new Array<DescontoRenovacao>();
					}
					const descontoRenovacao: any = {
						codigo: dr.codigo,
						tipoIntervalo: dr.tipoIntervalo.id,
						intervaloDe: dr.intervaloDe,
						intervaloAte: dr.intervaloAte,
						tipoDesconto: dr.tipoDesconto.id,
						valor: dr.valor,
					};
					if (dr.justificativaOperacao) {
						descontoRenovacao.justificativaOperacao = {
							codigo: dr.justificativaOperacao.codigo,
							descricao: dr.justificativaOperacao.descricao,
						};
					}
					drAux.push(descontoRenovacao);
				});
				this.desconto.descontoRenovacoes = drAux;
			}
			if (this.empresas.length > 0) {
				const deAux = new Array<DescontoEmpresa>();
				this.empresas.forEach((emp) => {
					if (!this.desconto.descontoEmpresas) {
						this.desconto.descontoEmpresas = new Array<DescontoEmpresa>();
					}
					const descontoEmpresa: any = {
						codigo: emp.codigo,
						empresa: { codigo: emp.codigoEmpresa },
					};
					if (emp.utilizaEmpresa) {
						deAux.push(descontoEmpresa);
					}
				});
				this.desconto.descontoEmpresas = deAux;
			}
			this.descontoService.save(this.desconto).subscribe(
				(response) => {
					this.notificationService.success(this.traducoes.getLabel("SAVE_MSG"));
					this.voltarListagem();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(err.meta.messageValue);
					}
				}
			);
		}
	}

	confirmDescontoRenovacao(event: any) {
		this.descontoRenovacoes = event.data;
	}

	confirm(event: any) {}

	deleteDescontoRenovacao(event: any) {
		this.descontoRenovacoes.splice(event.index, 1);
	}

	edit(event: any) {}

	isEditingOrAddingDescontoRenovacao(event: boolean) {
		this.isEdittingTableRenovacao = event;
	}

	private initTipoProdutoArray() {
		this.getTipoProdutoText = getTipoProdutoText(this.traducao);
		this.getTipoProdutoText.push({
			id: "VG",
			label: this.traducao.getLabel("VG"),
		});
		this.cd.detectChanges();
	}

	private initTipoDescontoArray() {
		this.getTipoDescontoText = getTipoDescontoText(this.traducaoDesconto);
		this.cd.detectChanges();
	}

	private initTipoIntervaloArray() {
		const tipoIntervaloArray = [];
		tipoIntervaloArray.push({
			label: this.traducaoIntervalo.getLabel("AT"),
			id: TipoIntervalo.atrasado,
		});
		tipoIntervaloArray.push({
			label: this.traducaoIntervalo.getLabel("AN"),
			id: TipoIntervalo.antecipado,
		});
		this.getTipoIntervaloText = tipoIntervaloArray;

		this.cd.detectChanges();
	}

	private populateEmpresas(response: any) {
		const descontoEmpresas = response.content;
		descontoEmpresas.forEach((descontoEmpresa) =>
			this.empresas.push({
				codigo: descontoEmpresa.codigo,
				codigoEmpresa: descontoEmpresa.empresa.codigo,
				nome: descontoEmpresa.empresa.nome,
				cidade: descontoEmpresa.empresa.cidade.nome,
				estado: descontoEmpresa.empresa.estado.sigla,
				setor: descontoEmpresa.empresa.setor,
			})
		);
		if (!this.desconto.descontoEmpresas) {
			this.desconto.descontoEmpresas = new Array<DescontoEmpresa>();
		}
		this.desconto.descontoEmpresas.forEach((descontoEmpresa) => {
			const findedEmpresa = this.empresas.find(
				(emp) => emp.codigoEmpresa === descontoEmpresa.empresa.codigo
			);
			if (findedEmpresa) {
				findedEmpresa.codigo = descontoEmpresa.codigo;
				findedEmpresa.codigoEmpresa = descontoEmpresa.empresa.codigo;
				findedEmpresa.nome = descontoEmpresa.empresa.nome;
				findedEmpresa.cidade = descontoEmpresa.empresa.cidade.nome;
				findedEmpresa.estado = descontoEmpresa.empresa.estado.sigla;
				findedEmpresa.setor = descontoEmpresa.empresa.setor;
			} else {
				this.empresas.push({
					codigo: descontoEmpresa.codigo,
					codigoEmpresa: descontoEmpresa.empresa.codigo,
					nome: descontoEmpresa.empresa.nome,
					cidade: descontoEmpresa.empresa.cidade.nome,
					estado: descontoEmpresa.empresa.estado.sigla,
					setor: descontoEmpresa.empresa.setor,
				});
			}
		});
		this.sortEmpreas();
		if (this.tableComponent) {
			this.tableComponent.reloadData();
		}
	}
}
