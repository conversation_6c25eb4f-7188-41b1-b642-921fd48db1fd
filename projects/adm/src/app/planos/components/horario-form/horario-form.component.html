<adm-layout
	(goBack)="voltarListagem()"
	i18n-modulo="@@adm:moduleName"
	i18n-pageTitle="@@planos:horario:mainTitle"
	i18n-subtitle="@@adm:horario:subtitle"
	modulo="Administrativo"
	pageTitle="Horário"
	subtitle="Informe os dados abaixo">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<div class="row">
				<!-- CODIGO -->
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="codigoControl"
						i18n-label="@@planos:horario:codigoLabel"
						id="horario-input-codigo"
						label="Código"
						placeholder="000"
						readonly="true"></pacto-cat-form-input-number>
				</div>
				<!-- DESCRIÇÃO -->
				<div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
					<pacto-cat-form-input
						[control]="form.get('descricao')"
						errorMsg="Insira uma descrição"
						i18n-errorMsg="@@planos:horario:inputErrorMsg"
						i18n-label="@@planos:horario:inputLabel"
						i18n-placeholder="@@planos:horario:inputPlaceholder"
						id="horario-input-descricao"
						label="Descrição*"
						placeholder="Descrição"></pacto-cat-form-input>
				</div>
				<!-- CORRESPONDENCIA ZW -->
				<div
					*ngIf="sessionService.loggedUser.administrador"
					class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-input
						[control]="form.get('correspondencia_zd')"
						i18n-label="@@planos:horario:inputLabel"
						id="horario-input-correspondencia_zd"
						label="Correspondência ZD"></pacto-cat-form-input>
				</div>
			</div>

			<div class="row">
				<pacto-cat-checkbox
					[control]="form.get('livre')"
					class="checkboxLivre"
					i18n-label="@@planos:horario:checkboxLivreLabel"
					id="horario-chk-livre"
					label="Livre"></pacto-cat-checkbox>
				<pacto-cat-checkbox
					[control]="form.get('ativo')"
					class="checkboxDefault"
					i18n-label="@@planos:horario:checkboxAtivoLabel"
					id="horario-chk-ativo"
					label="Ativo"></pacto-cat-checkbox>
			</div>

			<div *ngIf="!this.form.get('livre').value">
				<div class="divBttn row">
					<!-- ADD HORÁRIO-->
					<h6 class="addHorarioLabel" i18n="@@planos:horario:add">
						Adicionar horários
					</h6>
					<div class="bttnHorario">
						<pacto-cat-button
							(click)="addModHorario()"
							[full]="true"
							i18n-label="@@planos:horario:addModLabel"
							id="horario-btn-add-horario"
							label="ADICIONAR HORÁRIO"
							size="LARGE"></pacto-cat-button>
					</div>
				</div>

				<!-- TABELA HORÁRIO-->
				<div class="table-wrapper">
					<pacto-relatorio
						#tableHorarioComponent
						(iconClick)="iconClick($event)"
						(rowClick)="addModHorario($event)"
						[showShare]="false"
						[table]="table"
						actionTitulo="Opções"
						class="table-horario-disp"
						i18n-actionTitulo="@@horario:opcoes-column"></pacto-relatorio>
				</div>
			</div>

			<!--BOTÕES-->
			<div class="row justify-content-end">
				<pacto-log
					*ngIf="this.horario.codigo"
					[url]="urlLog"
					table="true"
					style="margin-right: 10px"></pacto-log>
				<pacto-cat-button
					(click)="voltarListagem()"
					i18n-label="@@planos:cancelBttn"
					id="horario-btn-cancel"
					label="Voltar"
					size="LARGE"
					style="margin-right: 10px"
					type="OUTLINE_DARK"></pacto-cat-button>
				<pacto-cat-button
					(click)="salvarHorario()"
					i18n-label="@@planos:saveBttn"
					id="horario-btn-salvar"
					label="Salvar"
					size="LARGE"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<ng-template #columnPeriodo>
	<span i18n="@@produtos:horario:columnPeriodo">Período</span>
</ng-template>

<ng-template #columnHorario>
	<span i18n="@@produtos:horario:columnHorario">Horário</span>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@planos:horario:saveMsg" xingling="SAVE_MSG">
		Horário cadastrado com sucesso!
	</span>
	<span i18n="@@planos:horario:saveMsg" xingling="DESCRICAO_NULL">
		O campo DESCRIÇÃO deve ser informado.
	</span>
	<span i18n="@@planos:horario:addModLabel" xingling="MOD_LABEL">
		Adicionar horário
	</span>

	<span i18n="@@horario:identificadorDO" xingling="DO">Domingo</span>
	<span i18n="@@horario:identificadorSG" xingling="SG">Segunda</span>
	<span i18n="@@horario:identificadorTE" xingling="TE">Terça</span>
	<span i18n="@@horario:identificadorQA" xingling="QA">Quarta</span>
	<span i18n="@@horario:identificadorQI" xingling="QI">Quinta</span>
	<span i18n="@@horario:identificadorSX" xingling="SX">Sexta</span>
	<span i18n="@@horario:identificadorSB" xingling="SB">Sábado</span>

	<span i18n="@@horario:0030" xingling="0000">00:00</span>
	<span i18n="@@horario:identificadorSG" xingling="0030">00:30</span>
	<span i18n="@@horario:identificadorTE" xingling="0100">01:00</span>
	<span i18n="@@horario:identificadorQA" xingling="0130">01:30</span>
	<span i18n="@@horario:identificadorQI" xingling="0200">02:00</span>
	<span i18n="@@horario:identificadorSX" xingling="0230">02:30</span>
	<span i18n="@@horario:identificadorSB" xingling="0300">03:00</span>
	<span i18n="@@horario:identificadorDO" xingling="0330">03:30</span>
	<span i18n="@@horario:identificadorSG" xingling="0400">03:30</span>
	<span i18n="@@horario:identificadorTE" xingling="0400">04:00</span>
	<span i18n="@@horario:identificadorQA" xingling="0430">04:30</span>
	<span i18n="@@horario:identificadorQI" xingling="0500">05:00</span>
	<span i18n="@@horario:identificadorSX" xingling="0530">05:30</span>
	<span i18n="@@horario:identificadorSB" xingling="0600">06:00</span>
	<span i18n="@@horario:identificadorDO" xingling="0630">06:30</span>
	<span i18n="@@horario:identificadorSG" xingling="0700">07:00</span>
	<span i18n="@@horario:identificadorTE" xingling="0730">07:30</span>
	<span i18n="@@horario:identificadorQA" xingling="0800">08:00</span>
	<span i18n="@@horario:identificadorQI" xingling="0830">08:30</span>
	<span i18n="@@horario:identificadorSX" xingling="0900">09:00</span>
	<span i18n="@@horario:identificadorSB" xingling="0930">09:30</span>
	<span i18n="@@horario:identificadorDO" xingling="1000">10:00</span>
	<span i18n="@@horario:identificadorSG" xingling="1030">10:30</span>
	<span i18n="@@horario:identificadorTE" xingling="1100">11:00</span>
	<span i18n="@@horario:identificadorQA" xingling="1130">11:30</span>
	<span i18n="@@horario:identificadorQI" xingling="1200">12:00</span>
	<span i18n="@@horario:identificadorSX" xingling="1230">12:30</span>
	<span i18n="@@horario:identificadorSB" xingling="1300">13:00</span>
	<span i18n="@@horario:identificadorDO" xingling="1330">13:30</span>
	<span i18n="@@horario:identificadorSG" xingling="1400">14:00</span>
	<span i18n="@@horario:identificadorTE" xingling="1430">14:30</span>
	<span i18n="@@horario:identificadorQA" xingling="1500">15:00</span>
	<span i18n="@@horario:identificadorQI" xingling="1530">15:30</span>
	<span i18n="@@horario:identificadorSX" xingling="1600">16:00</span>
	<span i18n="@@horario:identificadorSB" xingling="1630">16:30</span>
	<span i18n="@@horario:identificadorDO" xingling="1700">17:00</span>
	<span i18n="@@horario:identificadorSG" xingling="1730">17:30</span>
	<span i18n="@@horario:identificadorTE" xingling="1800">18:00</span>
	<span i18n="@@horario:identificadorQA" xingling="1830">18:30</span>
	<span i18n="@@horario:identificadorQI" xingling="1900">19:00</span>
	<span i18n="@@horario:identificadorSX" xingling="1930">19:30</span>
	<span i18n="@@horario:identificadorSB" xingling="2000">20:00</span>
	<span i18n="@@horario:identificadorDO" xingling="2030">20:30</span>
	<span i18n="@@horario:identificadorSG" xingling="2100">21:00</span>
	<span i18n="@@horario:identificadorTE" xingling="2130">21:30</span>
	<span i18n="@@horario:identificadorQA" xingling="2200">22:00</span>
	<span i18n="@@horario:identificadorQI" xingling="2230">22:30</span>
	<span i18n="@@horario:identificadorSX" xingling="2300">23:00</span>
	<span i18n="@@horario:identificadorSB" xingling="2330">23:30</span>
</pacto-traducoes-xingling>
