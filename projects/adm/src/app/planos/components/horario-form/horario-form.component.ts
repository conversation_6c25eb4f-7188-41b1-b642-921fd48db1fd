import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import {
	DialogService,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { AddHorarioComponent } from "../add-horario/add-horario.component";
import { SessionService } from "sdk";
import { MaskService } from "ngx-mask";
import {
	PlanoApiHorarioService,
	getIdentificadorByIndex,
	getIdentificadorLabelByKey,
	Horario,
	HorarioDisponibilidadeSimple,
} from "plano-api";

@Component({
	selector: "adm-horario-form",
	templateUrl: "./horario-form.component.html",
	styleUrls: ["./horario-form.component.scss"],
	providers: [MaskService],
	encapsulation: ViewEncapsulation.None,
})
export class HorarioFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("columnPeriodo", { static: true }) columnPeriodo: TemplateRef<any>;
	@ViewChild("columnHorario", { static: true }) columnHorario: TemplateRef<any>;
	@ViewChild("tableHorarioComponent", { static: false })
	tableHorarioComponent: RelatorioComponent;
	codigoControl: FormControl = new FormControl();
	horario: Horario = new Horario();
	table: PactoDataGridConfig;
	form: FormGroup;
	page = 0;
	id;

	horarioDisponibilidadeArray: {
		content: Array<HorarioDisponibilidadeSimple>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<HorarioDisponibilidadeSimple>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	constructor(
		private horarioService: PlanoApiHorarioService,
		private admRest: AdmRestService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		private dialogService: DialogService,
		public sessionService: SessionService,
		public maskService: MaskService
	) {}

	ngOnInit() {
		this.createForm();
		this.initDiasSemana();
		const clonar = this.activatedRoute.snapshot.queryParamMap.get("clonar");
		const code: number =
			+this.activatedRoute.snapshot.queryParamMap.get("code");
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();

		if (clonar) {
			this.horarioService.clone(code).subscribe((response) => {
				this.horario = response.content;
				this.afterConsulting();
			});
		} else if (this.id) {
			this.horarioService.find(this.id).subscribe((response) => {
				this.horario = response.content;
				this.codigoControl.setValue(this.horario.codigo);
				this.afterConsulting();
			});
		}
	}

	ngAfterViewInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	initDiasSemana() {
		if (!this.horario.horarioDisponibilidadesSimple) {
			this.horario.horarioDisponibilidadesSimple =
				new Array<HorarioDisponibilidadeSimple>();
		}
		if (this.horario.horarioDisponibilidadesSimple.length === 0) {
			for (let i = 1; i <= 7; i++) {
				this.horario.horarioDisponibilidadesSimple.push({
					diaSemana: getIdentificadorByIndex(i),
					horarios: new Array<{
						horarioInicial: string;
						horarioFinal: string;
					}>(),
				});
			}
		}
		this.createHorarioConfigPageObject();
	}

	afterConsulting() {
		this.form.patchValue({
			descricao: this.horario.descricao,
			correspondencia_zd: this.horario.correspondencia_zd,
			livre: this.horario.livre,
		});

		this.createHorarioConfigPageObject();
		this.cd.detectChanges();
	}

	createForm() {
		this.form = new FormGroup({
			descricao: new FormControl(this.horario.descricao, [Validators.required]),
			correspondencia_zd: new FormControl(),
			livre: new FormControl(),
			ativo: new FormControl("true"),
		});
		this.form.get("livre").valueChanges.subscribe((v) => {
			if (v) {
				this.initDiasSemana();
			}
		});
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			pagination: false,
			dataAdapterFn: (serverData) => {
				return this.horarioDisponibilidadeArray;
			},
			columns: [
				{
					nome: "diaSemana",
					titulo: this.columnPeriodo,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => {
						return getIdentificadorLabelByKey(v, this.traducoes);
					},
				},
				{
					nome: "horarios",
					titulo: this.columnHorario,
					visible: true,
					ordenavel: true,
					valueTransform: (
						v: Array<{ horarioInicial: string; horarioFinal: string }>
					) => {
						if (v.length === 0) {
							return "---------";
						} else {
							let horarios = "";
							v.forEach((horario, index) => {
								horarios +=
									index === v.length - 1
										? `${this.maskService.applyMask(
												horario.horarioInicial,
												"00:00"
										  )} - ${this.maskService.applyMask(
												horario.horarioFinal,
												"00:00"
										  )}`
										: `${this.maskService.applyMask(
												horario.horarioInicial,
												"00:00"
										  )} - ${this.maskService.applyMask(
												horario.horarioFinal,
												"00:00"
										  )} | `;
							});
							return horarios;
						}
					},
				},
			],
			actions: [
				{
					nome: "editHorarioMod",
					iconClass: "pct pct-edit-3 cor-azulim-pri",
					tooltipText: "",
				},
			],
		});
	}

	addModHorario(rowInfo?) {
		const dialogRef = this.dialogService.open(
			this.traducoes.getLabel("MOD_LABEL"),
			AddHorarioComponent
		);
		if (rowInfo) {
			dialogRef.componentInstance.edit = true;
			dialogRef.componentInstance.horarioDisponibilidadeEditing = rowInfo;
			dialogRef.componentInstance.horarioDisponibilidadeIndexEditing =
				rowInfo.rowIndex;
		} else {
			Object.assign(
				dialogRef.componentInstance.horarioDisponibilidades,
				this.horario.horarioDisponibilidadesSimple
			);
		}
		if (dialogRef.result) {
			dialogRef.result
				.then(
					(value: {
						horarioDisponibilidades: Array<HorarioDisponibilidadeSimple>;
						horarioDisponibilidade: HorarioDisponibilidadeSimple;
						horarioDisponibilidadeIndexEditing: number;
						edit: boolean;
					}) => {
						if (value.edit) {
							this.horario.horarioDisponibilidadesSimple[
								value.horarioDisponibilidadeIndexEditing
							] = value.horarioDisponibilidade;
						} else {
							this.horario.horarioDisponibilidadesSimple =
								value.horarioDisponibilidades;
						}
						this.createHorarioConfigPageObject();
					}
				)
				.catch((error) => {
					console.log(error);
				});
		}
	}

	iconClick(event) {
		if (event.iconName === "editHorarioMod") {
			event.row.rowIndex = event.rowIndex;
			this.addModHorario(event.row);
		}
	}

	createHorarioConfigPageObject(page = 1, size = 10) {
		if (isNaN(page)) {
			page = 1;
		}
		if (this.horario.horarioDisponibilidadesSimple) {
			this.horarioDisponibilidadeArray.totalElements =
				this.horario.horarioDisponibilidadesSimple.length;
			this.horarioDisponibilidadeArray.size = size;
			this.horarioDisponibilidadeArray.totalPages = Math.ceil(
				+(
					this.horarioDisponibilidadeArray.totalElements /
					this.horarioDisponibilidadeArray.size
				)
			);
			this.horarioDisponibilidadeArray.first = page === 0 || page === 1;
			this.horarioDisponibilidadeArray.last =
				page === this.horarioDisponibilidadeArray.totalPages;
			this.horarioDisponibilidadeArray.content =
				this.horario.horarioDisponibilidadesSimple.slice(
					size * page - size,
					size * page
				);
			if (this.tableHorarioComponent) {
				this.tableHorarioComponent.reloadData();
			}
		}
	}

	get urlLog() {
		return this.admRest.buildFullUrlAdmCore(`logApi/logs-horario/${this.id}`);
	}

	voltarListagem() {
		this.router.navigate(["adm", "planos", "horarios"]);
	}

	salvarHorario() {
		if (
			!this.form.get("descricao").value ||
			(this.form.get("descricao").value &&
				this.form.get("descricao").value === "")
		) {
			this.notificationService.error(this.traducoes.getLabel("DESCRICAO_NULL"));
		} else {
			Object.keys(this.form.getRawValue()).forEach((key) => {
				this.horario[key] = this.form.getRawValue()[key];
			});
			this.horarioService.save(this.horario).subscribe(
				(response) => {
					this.notificationService.success(this.traducoes.getLabel("SAVE_MSG"));
					this.voltarListagem();
				},
				(error) => {
					this.cd.detectChanges();
					if (error.error) {
						if (error.error.meta && error.error.meta.error) {
							this.notificationService.error(error.error.meta.error);
						} else {
							this.notificationService.error(error.error.meta.messageValue);
						}
					}
				}
			);
		}
	}
}
