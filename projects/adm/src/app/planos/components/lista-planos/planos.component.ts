import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import { AdmRestService } from "../../../adm-rest.service";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { Plano, TipoPlano } from "../../plano.model";
import { PlanoService } from "../cadastrar-plano/plano.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";

declare var moment;

@Component({
	selector: "adm-planos",
	templateUrl: "./planos.component.html",
	styleUrls: ["./planos.component.scss"],
})
export class PlanosComponent implements OnInit {
	// Traduções
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnTipo", { static: true }) columnTipo: TemplateRef<any>;
	@ViewChild("columnStatus", { static: true }) columnStatus: TemplateRef<any>;
	@ViewChild("columnIngressoAte", { static: true })
	columnIngressoAte: TemplateRef<any>;
	@ViewChild("columnVigenciaAte", { static: true })
	columnVigenciaAte: TemplateRef<any>;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	recurso: PerfilAcessoRecurso;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;

	@ViewChild("celulaStatus", { static: true }) celulaStatus;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private planoState: PlanoStateService,
		private planoService: PlanoService,
		private notificationService: SnotifyService
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.PLANO);
	}

	ngOnInit() {
		this.initFilter();
		this.initTable();
		this.admRest.notificarNovoPlanoAcesso("NOVO_PLANO_ACESSO");
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "tipo",
						label: this.traducao.getLabel("filtro-tipo"),
						type: GridFilterType.DS3_SELECT_MANY,
						options: [
							{ value: "normal", label: this.traducao.getLabel("PN") },
							{ value: "avancado", label: this.traducao.getLabel("PA") },
							{ value: "recorrencia", label: this.traducao.getLabel("PR") },
							{ value: "credito", label: this.traducao.getLabel("PC") },
							{ value: "personal", label: this.traducao.getLabel("PP") },
						],
					},
					{
						name: "status",
						label: this.traducao.getLabel("filtro-status"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducao.getLabel("PAT") },
							{ value: "false", label: this.traducao.getLabel("PIN") },
						],
						initialValue: ["true"],
					},
					{
						name: "ingressoate",
						label: this.traducao.getLabel("filtro-ingressoate"),
						type: GridFilterType.DS3_DATE,
					},
					{
						name: "vigenciaate",
						label: this.traducao.getLabel("filtro-vigenciaate"),
						type: GridFilterType.DS3_DATE,
					},
				],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("/planos", false, Api.MSPLANO),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "tipoPlano",
						titulo: this.columnTipo,
						visible: true,
						ordenavel: false,
						valueTransform: (v) => this.getTipoPlanoText(v),
					},
					{
						nome: "vigenciaAte",
						titulo: this.columnStatus,
						visible: true,
						celula: this.celulaStatus,
						ordenavel: false,
					},
					{
						nome: "ingressoAte",
						titulo: this.columnIngressoAte,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					},
					{
						nome: "vigenciaAte",
						titulo: this.columnVigenciaAte,
						visible: true,
						ordenavel: true,
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
					},
				],
				dropDownActions: [
					{
						nome: this.traducao.getLabel("action-editar"),
						iconClass: "",
						tooltipText: this.traducao.getLabel("tooltip-editar-plano"),
						actionFn: (row) => this.editPlano(row),
						showIconFn: (row) => this.recurso.editar,
					},
					{
						nome: this.traducao.getLabel("action-excluir"),
						iconClass: "",
						tooltipText: this.traducao.getLabel("tooltip-excluir-plano"),
						actionFn: (row) => this.deletePlano(row),
						showIconFn: (row) => this.recurso.excluir,
					},
					{
						nome: this.traducao.getLabel("action-clonar"),
						iconClass: "",
						tooltipText: this.traducao.getLabel("tooltip-clonar-plano"),
						actionFn: (row) => this.clonePlano(row),
						showIconFn: (row) => this.recurso.incluir,
					},
					{
						nome: this.traducao.getLabel("action-atualizar"),
						iconClass: "",
						tooltipText: this.traducao.getLabel("tooltip-atualizar-plano"),
						actionFn: (row) => this.atualizarPlanoParaAvancado(row),
						showIconFn: (row) =>
							row.tipoPlano === TipoPlano.PLANO_NORMAL
								? this.recurso.incluir
								: null,
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	dropAction(): any[] {
		return [];
	}

	abrirPlano(event: any) {
		this.router.navigate(["adm", "planos", event.codigo]);
	}

	editPlano(plano) {
		this.router.navigate(["adm", "plano", plano.codigo], {
			queryParams: {
				tipo: plano.tipoPlano,
			},
		});
	}

	clonePlano(plano) {
		if (plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			this.router.navigate(["adm", "planos", "novo-plano", "recorrencia"], {
				queryParams: { clonar: true, code: plano.codigo },
			});
			return;
		}
		if (plano.tipoPlano === TipoPlano.PLANO_PERSONAL) {
			this.router.navigate(["adm", "planos", "novo-plano", "personal"], {
				queryParams: { clonar: true, code: plano.codigo },
			});
			return;
		}
		if (plano.tipoPlano === TipoPlano.PLANO_CREDITO) {
			this.router.navigate(["adm", "planos", "novo-plano", "credito"], {
				queryParams: { clonar: true, code: plano.codigo },
			});
			return;
		}
		this.router.navigate(["adm", "planos", "novo-plano"], {
			queryParams: { clonar: true, code: plano.codigo },
		});
	}

	deletePlano(plano: Plano) {
		if (this.recurso.excluir) {
			this.planoService.delete(plano.codigo).subscribe(
				(data) => {
					let mensagem = `${this.traducao.getLabel("sucesso-exclusao1")} ${
						plano.descricao
					}`;
					mensagem += ` ${this.traducao.getLabel("sucesso-exclusao2")}`;
					this.notificationService.success(mensagem);
					this.tableData.reloadData();
				},
				(error) => {
					this.notificationService.error(error.error.meta.messageValue);
				}
			);
		} else {
			this.notificationService.warning(
				this.traducao.getLabel("mensagem-sem-permissao")
			);
		}
	}

	atualizarPlanoParaAvancado(plano: Plano) {
		if (
			!plano.planoPersonal &&
			!plano.planoRecorrencia &&
			plano.tipoPlano === TipoPlano.PLANO_NORMAL
		) {
			this.planoService.atualizarParaAvancado(plano.codigo).subscribe(
				(planoSub) => {
					this.notificationService.success(
						`Plano ${planoSub.nome} atualizado para AVANÇADO `
					);
					plano.tipoPlano = planoSub.tipoPlano;
				},
				(error) => {
					this.notificationService.error("Erro ao atualizar plano");
				}
			);
			this.tableData.reloadData();
		} else {
			this.notificationService.error(
				"Apenas planos normais podem ser atualizado."
			);
		}
	}

	btnClickHandler() {}

	novoPlano() {
		this.planoState.passoAtual = 1;
		this.planoState.removeFromSession();
		this.router.navigate(["adm", "planos", "novo-plano"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	getTipoPlanoText(tipoPlano) {
		switch (tipoPlano) {
			case TipoPlano.NENHUM:
				return this.traducao.getLabel("PLANO_NENHUM");
			case TipoPlano.PLANO_CREDITO:
				return this.traducao.getLabel("PLANO_CREDITO");
			case TipoPlano.PLANO_NORMAL:
				return this.traducao.getLabel("PLANO_NORMAL");
			case TipoPlano.PLANO_AVANCADO:
				return this.traducao.getLabel("PLANO_AVANCADO");
			case TipoPlano.PLANO_PERSONAL:
				return this.traducao.getLabel("PLANO_PERSONAL");
			case TipoPlano.PLANO_RECORRENCIA:
				return this.traducao.getLabel("PLANO_RECORRENCIA");
				break;
		}
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "action-edit (key)") {
			this.editPlano(event.row);
		} else if (event.iconName === "action-delete (key)") {
			this.deletePlano(event.row);
		}
	}
}
