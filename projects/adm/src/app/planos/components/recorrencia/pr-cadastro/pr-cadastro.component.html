<ng-container *ngIf="loading">
	<div class="loader">
		<img src="assets/images/loading.svg" />
		<span i18n="@@plano:loading-message">Carregando plano...</span>
	</div>
</ng-container>
<pacto-cat-layout-v2 *ngIf="!loading">
	<pacto-cat-stepper-simple (goBack)="voltarParaListagemPlano()">
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepDescription-basico"
			i18n-stepLabel="@@plano:steplabel-basico"
			stepDescription="Informe os dados necessários para criar um novo plano"
			stepLabel="Dados Básicos">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[disabled]="
						dadosBasicosComponent.form && dadosBasicosComponent.form.invalid
					"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-dados-basicos"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pr-dados-basicos
				#dadosBasicosComponent
				(nome)="planoNome = $event"></adm-pr-dados-basicos>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepdescription-modalidade"
			i18n-stepLabel="@@plano:steplabel-modalidade"
			stepDescription="Defina as modalidades que farão parte do plano."
			stepLabel="{{ planoNome | captalize }} / Modalidades">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-table-modalidades"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="
						!tableModalidadesComponent.plano?.modalidadesAux ||
						!tableModalidadesComponent.plano?.modalidadesAux.length === 0 ||
						isEditingModalidade
					"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-table-modalidades"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pr-table-modalidades
				#tableModalidadesComponent
				(isEditinOrAdding)="
					isEditingModalidade = $event
				"></adm-pr-table-modalidades>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:steplabel-description"
			i18n-stepLabel="@@plano:steplabel-horario"
			stepDescription="Informe as variações de tempo e valor do plano."
			stepLabel="{{ planoNome | captalize }} / Horários">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-table-horario"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="
						!tableHorariosComponent.plano?.horarios ||
						!tableHorariosComponent.plano?.horarios.length === 0 ||
						isEditingHorario
					"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-table-horario"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pr-table-horarios
				#tableHorariosComponent
				(isEditinOrAdding)="isEditingHorario = $event"></adm-pr-table-horarios>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			i18n-stepDescription="@@plano:stepDescription-prod-serv"
			i18n-stepLabel="@@plano:steplabel-prod-serv"
			stepDescription="Informe os produtos e serviços sugeridos, seus valores dentro do plano e se são obrigatórios."
			stepLabel="{{ planoNome | captalize }} / Produtos e Serviços">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-table-produtos"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					[disabled]="
						!tableProdutosComponent.plano?.produtosSugeridos ||
						!tableProdutosComponent.plano?.produtosSugeridos.length === 0 ||
						isEditingProdutos
					"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-table-produtos"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pr-table-produtos
				#tableProdutosComponent
				(isEditinOrAdding)="isEditingProdutos = $event"></adm-pr-table-produtos>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			stepLabel="{{ planoNome | captalize }} / Dados Contraturais"
			i18n-stepDescription="@@plano:stepdescription-dadoscontratuais"
			i18n-stepLabel="@@plano:steplabel-dadoscontratuais"
			stepDescription="Defina os termos de contratação do seu plano.">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-dados-contraturais"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					*ngIf="planoStateService.plano.apresentarVendaRapida"
					[disabled]="!dadosContratuaisComponent.form?.valid"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-avancar"
					id="btn-plano-avancar-dados-contratuais"
					label="Avançar"
					pactoCatStepSimpleNext></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					*ngIf="!planoStateService.plano.apresentarVendaRapida"
					[disabled]="!dadosContratuaisComponent.form?.valid || isSaving"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-concluir"
					id="btn-plano-concluir-dados-contratuais"
					label="Concluir"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-pr-dados-contratuais
				#dadosContratuaisComponent></adm-pr-dados-contratuais>
		</pacto-cat-step-simple>
		<pacto-cat-step-simple
			*ngIf="planoStateService.plano.apresentarVendaRapida"
			i18n-stepDescription="@@adm:stepDescription-empresas"
			i18n-stepLabel="@@adm:steplabel-empresas"
			stepDescription="Defina as empresas que o plano pode ser vendido."
			stepLabel="{{ planoNome | captalize }} / Empresas">
			<ng-container *pactoButtonContainer>
				<pacto-cat-button
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					class="margin-left-12"
					i18n-label="@@adm:btn-voltar"
					id="btn-plano-voltar-empresa"
					label="Voltar"
					pactoCatStepSimplePrevious></pacto-cat-button>
				<pacto-cat-button
					(click)="salvar()"
					[disabled]="isSaving"
					[size]="'LARGE'"
					[type]="'PRIMARY'"
					class="margin-left-12"
					i18n-label="@@adm:btn-concluir"
					id="btn-plano-concluir-empresa"
					label="Concluir"
					pactoCatStepSimpleNext></pacto-cat-button>
			</ng-container>
			<adm-plano-empresa></adm-plano-empresa>
		</pacto-cat-step-simple>
	</pacto-cat-stepper-simple>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:descricao-length-error" xingling="descricao-length-error">
		O nome do plano deve conter menos que 200 caracteres!
	</span>
	<span i18n="@@plano:modalidade-zerada-hint" xingling="modalidade-zerada-hint">
		Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais
		uma modalidade junto que tenha valor, ou então coloque valor no cadastro
		dela.
	</span>
	<span
		i18n="@@plano:modalidade-zerada-error"
		xingling="modalidade-zerada-error">
		Você adicionou modalidade com valor zerado na aba "modalidades". Como este
		plano não foi marcado como "PLANO BOLSA", esta operação não é permitida.
	</span>
	<span i18n="@@plano:saved-success" xingling="saved-success">
		Plano salvo com sucesso.
	</span>
</pacto-traducoes-xingling>
