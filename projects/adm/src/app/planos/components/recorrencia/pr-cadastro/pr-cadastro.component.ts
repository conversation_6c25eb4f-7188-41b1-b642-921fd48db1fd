import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { Plano, PlanoEmpresa, TipoPlano } from "../../../plano.model";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { AdmRestService } from "../../../../adm-rest.service";
import { PcDadosContratualComponent } from "../../credito/pc-dados-contratual/pc-dados-contratual.component";
import { TraducoesXinglingComponent } from "ui-kit";

@Component({
	selector: "adm-pr-cadastro",
	templateUrl: "./pr-cadastro.component.html",
	styleUrls: ["./pr-cadastro.component.scss", "../../../css/plano.scss"],
})
export class PrCadastroComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("dadosContratuaisComponent", { static: false })
	dadosContratuaisComponent: PcDadosContratualComponent;
	loading = false;
	isSaving = false;
	plano: Plano = new Plano();
	isEditingModalidade: boolean;
	isEditingHorario: boolean;
	isEditingProdutos: boolean;
	planoNome = "";
	constructor(
		private planoCommonsService: PlanoCommonsService,
		public planoStateService: PlanoStateService,
		private planoService: PlanoService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private activatedRoute: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router
	) {}

	ngOnInit() {
		const clonar = this.activatedRoute.snapshot.queryParamMap.get("clonar");
		if (clonar) {
			this.loading = true;
			this.planoCommonsService
				.clonar(this.activatedRoute, this.planoService)
				.subscribe((response) => {
					this.plano = response.content;
					this.plano = this.planoCommonsService.populateModalidadeAux(
						this.plano
					);
					this.plano = this.planoCommonsService.populateHorarioValor(
						this.plano
					);
					this.planoStateService.updateState(this.plano);
					this.router.navigate(["adm", "planos", "novo-plano", "recorrencia"]);
					this.loading = false;
					setTimeout(() => {
						this.cd.detectChanges();
					});
				});
		} else {
			this.plano = this.planoStateService.updatePlanoObj();
			this.plano.tipoPlano = TipoPlano.PLANO_RECORRENCIA;
			if (!this.plano.planoRecorrencia) {
				this.plano.planoRecorrencia = {};
			}
			if (
				this.plano.planoRecorrencia.renovavelAutomaticamente === undefined ||
				this.plano.planoRecorrencia.renovavelAutomaticamente === null
			) {
				this.plano.planoRecorrencia.renovavelAutomaticamente = true;
			}
			if (!this.plano.planoRecorrencia.diaAnuidade) {
				this.plano.planoRecorrencia.diaAnuidade = 1;
			}
			if (!this.plano.planoRecorrencia.mesAnuidade) {
				this.plano.planoRecorrencia.mesAnuidade = 1;
			}
			this.planoStateService.updateState(this.plano);
			this.cd.detectChanges();
		}
	}

	voltarParaListagemPlano() {
		this.planoCommonsService.voltarParaListagem();
	}

	salvar() {
		if (
			this.dadosContratuaisComponent &&
			this.dadosContratuaisComponent.form.valid &&
			!this.isSaving
		) {
			this.plano = this.planoStateService.updatePlanoObj();
			this.plano.regimeRecorrencia = true;
			const plano = this.planoCommonsService.convertPlano(this.plano);
			if (!plano.diasVencimentoProrata) {
				this.planoCommonsService.initDiasProrata(plano);
			}
			if (!plano.site) {
				if (plano.termoAceite) {
					plano.termoAceite = null;
				}
			}
			if (plano.planoRecorrencia) {
				if (plano.planoRecorrencia.parcelarAnuidade) {
					plano.planoRecorrencia.parcelaAnuidade = undefined;
				} else {
					plano.planoRecorrencia.parcelasAnuidade = undefined;
				}
				if (plano.planoRecorrencia.parcelasAnuidade) {
					plano.planoRecorrencia.parcelasAnuidade.forEach((parcelaAnuidade) => {
						if (parcelaAnuidade.parcela && parcelaAnuidade.parcela.value) {
							parcelaAnuidade.parcela = parcelaAnuidade.parcela.value;
						}
					});
				}
			}
			if (!this.plano.apresentarVendaRapida) {
				this.plano.empresas = new Array<PlanoEmpresa>();
			}
			if (!plano.bolsa && plano.modalidades) {
				let modalidadeValorZerado = false;
				for (const d of plano.modalidades) {
					if (d.modalidade.valorMensal > 0) {
						modalidadeValorZerado = true;
						break;
					}
				}
				if (!modalidadeValorZerado) {
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-hint"),
						{
							timeout: 10000,
						}
					);
					this.notificationService.error(
						this.traducoes.getLabel("modalidade-zerada-error"),
						{
							timeout: 10000,
						}
					);
					return;
				}
			}
			this.isSaving = true;
			this.planoService.save(plano).subscribe(
				(response) => {
					this.isSaving = false;
					this.admRest.notificarNovoPlanoAcesso("NOVO_PLANO_SALVAR");
					this.notificationService.success(
						this.traducoes.getLabel("saved-success")
					);
					this.voltarParaListagemPlano();
				},
				(responseError) => {
					this.isSaving = false;
					if (responseError.error) {
						if (responseError.error.meta) {
							this.notificationService.error(
								responseError.error.meta.messageValue
							);
						}
					}
				}
			);
		}
	}
}
