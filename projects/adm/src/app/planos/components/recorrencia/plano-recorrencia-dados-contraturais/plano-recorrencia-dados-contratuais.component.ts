import { Component, Input, OnInit } from "@angular/core";
import { Plano } from "../../../plano.model";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
} from "ui-kit";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoApiProdutoService } from "plano-api";
import { PlanoAdvancedConfigComponent } from "../../plano-advanced-config/plano-advanced-config.component";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-plano-recorrencia-dados-contratuais",
	templateUrl: "./plano-recorrencia-dados-contratuais.component.html",
	styleUrls: ["./plano-recorrencia-dados-contratuais.component.scss"],
})
export class PlanoRecorrenciaDadosContratuaisComponent implements OnInit {
	@Input() plano: Plano;

	modelosContrato: Array<any> = new Array<any>();
	marcacoesAulasColetivas = [
		{
			label: "marcação de aula coletia por dia",
		},
		{
			label: "quantidade de vezes na semana da aula",
		},
	];

	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "PL",
	};
	formGroup: FormGroup = new FormGroup({
		valorMensal: new FormControl("", Validators.required),
		taxaAdesao: new FormControl(""),
		duracaoPlano: new FormControl("", Validators.required),
		modeloContrato: new FormControl("", Validators.required),
		percentualMultaCancelamento: new FormControl(0),
		restringirQtdMarcacaoPorDia: new FormControl(),
		restringirMarcacaoAulasColetivas: new FormControl(),
		permiteSituacaoAtestadoContrato: new FormControl(),
		dividirManutencaoParcelasEA: new FormControl(),
		permitirAcessoRedeEmpresa: new FormControl(),
		produtoTaxaCancelamento: new FormControl("", Validators.required),
	});
	produtosTaxaCancelamento: Array<any> = new Array<any>();

	modeloContratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};
	produtoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	constructor(
		public admRestService: AdmRestService,
		private planoState: PlanoStateService,
		private dialogService: DialogService,
		public produtoService: PlanoApiProdutoService
	) {}

	ngOnInit() {
		if (this.plano && !this.plano.codigo) {
			this.plano = this.planoState.plano;
		}
		if (this.plano) {
			this.formGroup.patchValue({
				valorMensal: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.valorMensal
					: "",
				taxaAdesao: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.taxaAdesao === undefined
						? 0
						: this.plano.planoRecorrencia.taxaAdesao
					: "",
				duracaoPlano: this.plano.planoRecorrencia
					? this.plano.planoRecorrencia.duracaoPlano
					: "",
				modeloContrato: this.plano.modeloContrato,
				percentualMultaCancelamento:
					this.plano.percentualMultaCancelamento === undefined
						? 0
						: this.plano.percentualMultaCancelamento,
				restringirQtdMarcacaoPorDia: this.plano.restringirQtdMarcacaoPorDia,
				restringirMarcacaoAulasColetivas:
					this.plano.restringirMarcacaoAulasColetivas,
				permiteSituacaoAtestadoContrato:
					this.plano.permiteSituacaoAtestadoContrato,
				dividirManutencaoParcelasEA: this.plano.dividirManutencaoParcelasEA,
				permitirAcessoRedeEmpresa: this.plano.permitirAcessoRedeEmpresa,
				produtoTaxaCancelamento: this.plano.produtoTaxaCancelamento,
			});
		}
		this.formGroup.valueChanges.subscribe((value) => {
			if (this.plano) {
				this.plano = this.planoState.plano;
			}
			if (!this.plano.planoRecorrencia) {
				this.plano.planoRecorrencia = {};
			}
			this.plano.planoRecorrencia.valorMensal = value.valorMensal;
			this.plano.planoRecorrencia.taxaAdesao = value.taxaAdesao;
			this.plano.planoRecorrencia.duracaoPlano = value.duracaoPlano;
			this.plano.duracoes = [
				{
					numeroMeses: value.duracaoPlano,
					codicoesPagamento: [
						{
							qtdParcela: value.duracaoPlano,
						},
					],
				},
			];
			this.plano.modeloContrato = value.modeloContrato;
			this.plano.percentualMultaCancelamento =
				value.percentualMultaCancelamento;
			this.plano.restringirQtdMarcacaoPorDia =
				value.restringirQtdMarcacaoPorDia;
			this.plano.restringirMarcacaoAulasColetivas =
				value.restringirMarcacaoAulasColetivas;
			this.plano.permiteSituacaoAtestadoContrato =
				value.permiteSituacaoAtestadoContrato;
			this.plano.dividirManutencaoParcelasEA =
				value.dividirManutencaoParcelasEA;
			this.plano.produtoTaxaCancelamento = value.produtoTaxaCancelamento;
			this.plano.permitirAcessoRedeEmpresa = value.permitirAcessoRedeEmpresa;
			this.planoState.plano = this.plano;
		});
		this.loadProdutosCancelamento();
	}

	loadProdutosCancelamento() {
		this.produtoService.getProdutosCancelamento().subscribe((response: any) => {
			this.produtosTaxaCancelamento = response.content;
			if (
				(this.planoState.plano && this.planoState.plano.codigo === undefined) ||
				this.planoState.plano.codigo === null
			) {
				this.formGroup
					.get("produtoTaxaCancelamento")
					.setValue(
						this.produtosTaxaCancelamento.find(
							(prod) =>
								prod.descricao.toUpperCase() ===
								"CUSTO ADMINISTRATIVO DO CANCELAMENTO"
						)
					);
			}
		});
	}

	configuracoesAvancadas() {
		const dialogRef = this.dialogService.open(
			"Configurações Avançadas",
			PlanoAdvancedConfigComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoState.plano;
		dialogRef.componentInstance.mostrarAbaFerias = true;
		dialogRef.componentInstance.configRecorrenciaDadosContratuais =
			this.planoState.plano.tipoPlano === "PLANO_RECORRENCIA";
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {
					console.log(value);
					this.planoState.plano = value;
				})
				.catch((error) => {
					console.log(error);
				});
		}
	}
}
