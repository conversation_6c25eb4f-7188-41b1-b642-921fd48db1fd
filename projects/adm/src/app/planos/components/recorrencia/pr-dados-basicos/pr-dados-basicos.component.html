<pacto-cat-card-plain>
	<div class="table-wrapper pacto-shadow">
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-input
					[control]="form.get('descricao')"
					errorMsg="Forneça um nome para o plano."
					i18n-errorMsg="@@plano:error-msg-descricao"
					i18n-label="@@plano:label-descricao"
					id="input-plano-nome"
					label="Nome"
					maxlength="200"></pacto-cat-form-input>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select
					[control]="form.get('tipoPlano')"
					[items]="tiposPlano"
					disabled="true"
					errorMsg="Tipo de plano"
					i18n-errorMsg="@@plano:error-msg-tipo-plano"
					i18n-label="@@plano:label-tipo-plano"
					id="select-tipo-plano"
					label="Tipo de plano"></pacto-cat-form-select>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-select-filter
					[control]="form.get('produtoContrato')"
					[options]="produtosContrato"
					[paramBuilder]="produtoelectBuilder"
					errorMsg="Selecione um produto"
					i18n-errorMsg="@@plano:error-msg-produto-contrato"
					i18n-label="@@plano:label-produto-contrato"
					id="select-produto-contrato-plano"
					idKey="codigo"
					label="Produto padrão gerar parcelas contrato"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
		</div>
		<div class="row">
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('vigenciaDe')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@plano:error-msg-vigenciade"
					i18n-label="@@plano:label-vigenciade"
					id="datepicker-vigenciade-plano"
					label="Plano ativo a partir de"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('ingressoAte')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@plano:error-msg-ingressoate"
					i18n-label="@@plano:label-ingressoate"
					id="datepicker-ingresso-plano"
					label="Permite matrículas/rematrículas até"></pacto-cat-form-datepicker>
			</div>
			<div class="col-md-4">
				<pacto-cat-form-datepicker
					[control]="form.get('vigenciaAte')"
					errorMsg="Forneça uma data válida."
					i18n-errorMsg="@@plano:error-msg-vigenciaate"
					i18n-label="@@plano:label-vigenciaate"
					id="datepicker-vigenciaate-plano"
					label="Permite renovação até"></pacto-cat-form-datepicker>
			</div>
		</div>
		<div class="row">
			<div class="col-md-5">
				<pacto-cat-button
					(click)="configuracoesPlano()"
					[label]="traducoes.getLabel('advanced-config')"
					[size]="'LARGE'"
					[type]="'OUTLINE'"
					id="btn-advanced-config-plano"></pacto-cat-button>
			</div>

			<div class="col-md-4">
				<pacto-cat-checkbox
					*ngIf="form.get('tipoPlano').value === 'PLANO_CREDITO'"
					[control]="form.get('gerarParcelasValorDiferente')"
					i18n-label="@@plano:label-gerar-parcelas-valor-diferente"
					id="check-gerar-parcelas-valor-diferente-plano"
					label="Modalidades com valores diferentes"></pacto-cat-checkbox>
			</div>
		</div>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:tipo-normal" xingling="PN">Plano Normal</span>
	<span i18n="@@plano:tipo-recorrencia" xingling="PR">Plano Recorrência</span>
	<span i18n="@@plano:tipo-credito" xingling="PC">Plano Crédito</span>
	<span i18n="@@plano:tipo-personal" xingling="PP">Plano Personal</span>

	<span i18n="@@adm:advanced-config" xingling="advanced-config">
		Configurações avançadas
	</span>
</pacto-traducoes-xingling>
