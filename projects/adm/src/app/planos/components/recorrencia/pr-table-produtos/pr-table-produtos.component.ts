import {
	AfterViewInit,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Plano, TipoPlano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { PlanoApiProdutoService } from "plano-api";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { DecimalPipe } from "@angular/common";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pr-table-produtos",
	templateUrl: "./pr-table-produtos.component.html",
	styleUrls: ["./pr-table-produtos.component.scss"],
})
export class PrTableProdutosComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnValorProduto", { static: true })
	columnValorProduto: TemplateRef<any>;
	@ViewChild("columnObrigatorio", { static: true })
	columnObrigatorio: TemplateRef<any>;

	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("tableProdutos", { static: false })
	tableProdutos: CatTableEditableComponent;
	@ViewChild("columnSituacaoPlano", { static: true })
	columnSituacaoPlano: TemplateRef<any>;
	plano: Plano = new Plano();
	produtos: Array<any> = new Array<any>();
	table: PactoDataGridConfig;
	produtosPadroes: Array<any> = new Array<any>();
	loading = false;

	constructor(
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private produtoService: PlanoApiProdutoService,
		private decimalPipe: DecimalPipe,
		private admRest: AdmRestService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.plano.produtosSugeridos) {
			this.produtos = this.plano.produtosSugeridos;
		}
		this.initTable();
	}

	ngAfterViewInit() {
		if (this.tableProdutos) {
			if (!this.plano.produtosSugeridos) {
				this.plano.produtosSugeridos = new Array<any>();
			}
			if (this.plano.codigo === undefined) {
				this.produtoService.produtoPlanoBasico().subscribe(
					(data: any) => {
						this.plano = this.planoStateService.updatePlanoObj();
						if (!this.plano.produtosSugeridos) {
							this.plano.produtosSugeridos = new Array<any>();
						}
						const produtos = data.content;
						this.produtosPadroes = produtos;
						produtos.forEach((prod) => {
							if (this.plano.produtosSugeridos.length > 0) {
								const findProd = this.plano.produtosSugeridos.find(
									(v) => v.produto && v.produto.codigo === prod.produto.codigo
								);
								if (!findProd) {
									this.plano.produtosSugeridos.push({
										codigo: "",
										produto: prod.produto,
										obrigatorio: prod.obrigatorio,
										valorProduto: prod.valorProduto,
										ativoPlano: true,
									});
								}
							} else {
								this.plano.produtosSugeridos.push({
									produto: prod.produto,
									obrigatorio: prod.obrigatorio,
									valorProduto: prod.valorProduto,
									ativoPlano: true,
								});
							}
						});
						this.produtos = this.plano.produtosSugeridos;
						this.planoStateService.updateState(this.plano);
						if (this.tableProdutos) {
							this.tableProdutos.reloadData();
						}
					},
					(error) => {
						console.log("Erro ", error);
					}
				);
			} else {
				this.loading = true;
				this.produtoService.produtoPlanoBasico().subscribe(
					(data: any) => {
						this.produtosPadroes = data.content;
						if (this.tableProdutos) {
							this.tableProdutos.reloadData();
						}
						this.loading = false;
					},
					(error) => {
						console.log("Erro ", error);
					}
				);
			}
		}
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.produtos,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				produto: new FormControl("", Validators.required),
				valorProduto: new FormControl("", Validators.required),
				obrigatorio: new FormControl(false),
				ativoPlano: new FormControl(true),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "produto",
					titulo: this.columnProduto,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					width: "200px",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					objectAttrLabelName: "descricao",
					endpointUrl: this.admRest.buildFullUrlPlano(
						this.produtoService.produtosVendaveis
					),
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					selectOptionChange: (option, form, row) => {
						form.get("valorProduto").setValue(option.valorFinal);
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
					isDisabled: (row) => this.isProductDisabled(row),
				},
				{
					nome: "valorProduto",
					titulo: this.columnValorProduto,
					visible: true,
					ordenavel: false,
					editable: true,
					width: "137px",
					inputType: "decimal",
					decimal: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
				{
					nome: "obrigatorio",
					titulo: this.columnObrigatorio,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					width: "100px",
					valueTransform: (v) =>
						v
							? this.traducao.getLabel("label-sim")
							: this.traducao.getLabel("label-nao"),
					isDisabled: (row) => this.isProductDisabled(row),
				},
				{
					nome: "ativoPlano",
					titulo: this.columnSituacaoPlano,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "checkbox",
					valueTransform: (v) =>
						v || v === undefined
							? this.traducao.getLabel("label-ativo")
							: this.traducao.getLabel("label-inativo"),
				},
			],
		});
	}

	confirm(event) {
		if (
			event &&
			this.produtos &&
			event.row &&
			event.row.codigo &&
			event.row.codigo !== "" &&
			event.row.codigo !== undefined &&
			event.row.codigo !== null
		) {
			this.produtos.forEach((p) => {
				if (p.codigo === event.row.codigo) {
					p.edit = false;
				}
			});
		}

		this.plano.produtosSugeridos = this.produtos;
		if (this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			if (
				event.row.produto &&
				event.row.produto.tipoProduto &&
				event.row.produto.tipoProduto === "TD"
			) {
				this.plano.planoRecorrencia.taxaAdesao = event.row.valorProduto;
			}
			if (
				event.row.produto &&
				event.row.produto.tipoProduto &&
				event.row.produto.tipoProduto === "TA"
			) {
				this.plano.planoRecorrencia.valorAnuidade = event.row.valorProduto;
			}
		}
		this.planoStateService.updateState(this.plano);
	}

	delete(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.produtos) {
			let index;
			if (event.row.codigo) {
				const excecao = this.produtos.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.produtos.splice(index, 1);
				}
			} else {
				this.produtos.splice(event.index, 1);
			}
			this.plano.produtosSugeridos = this.produtos;
		}
		this.planoStateService.updateState(this.plano);
	}

	showDelete(row, isAdd) {
		const finded = this.produtosPadroes.find((v) => {
			if (v.produto && row.produto && v.produto.codigo === row.produto.codigo) {
				return v;
			}
		});
		return !!!finded;
	}

	isProductDisabled(row) {
		const finded = this.produtosPadroes.find((v) => {
			if (v.produto && row.produto && v.produto.codigo === row.produto.codigo) {
				return v;
			}
		});
		return finded !== undefined;
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this.plano = this.planoStateService.updatePlanoObj();
		const obrigatorio = form.get("obrigatorio").value;
		const ativoPlano = form.get("ativoPlano").value;
		if (obrigatorio === undefined || obrigatorio === null) {
			form.get("obrigatorio").setValue(false);
			row.obrigatorio = false;
		}
		if (ativoPlano === undefined || ativoPlano === null) {
			form.get("ativoPlano").setValue(true);
			row.ativoPlano = true;
		}
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			rowIndex
		);
		if (!exists) {
			if (
				data.find(
					(d, index) =>
						index !== rowIndex &&
						d.produto.codigo === form.get("produto").value.codigo
				)
			) {
				this.notificationService.error(
					this.traducao.getLabel("produto-duplicado")
				);
				return false;
			}
			const produtosMatricula = data.filter(
				(d) => d.produto.tipoProduto === "MA" && d.ativoPlano
			);
			if (produtosMatricula && produtosMatricula.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Matrícula ativos no plano!"
				);
				return false;
			}

			const produtosRematricula = data.filter(
				(d) => d.produto.tipoProduto === "RE" && d.ativoPlano
			);
			if (produtosRematricula && produtosRematricula.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Rematrícula ativos no plano!"
				);
				return false;
			}

			const produtosRenovacao = data.filter(
				(d) => d.produto.tipoProduto === "RN" && d.ativoPlano
			);
			if (produtosRenovacao && produtosRenovacao.length > 1) {
				this.notificationService.error(
					"Não pode conter mais de um produto do tipo Renovação ativos no plano!"
				);
				return false;
			}
		} else {
			this.notificationService.error(
				this.traducao.getLabel("dado-duplicado-table")
			);
		}
		return !exists;
	}
}
