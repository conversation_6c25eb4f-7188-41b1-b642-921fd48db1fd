import { Component, OnInit, ViewChild } from "@angular/core";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { PlanoApiProdutoService } from "plano-api";
import { Plano, TipoPlano } from "../../../plano.model";
import { FormGroup } from "@angular/forms";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PrAdvancedConfigDadosContratuaisComponent } from "../pr-advanced-config-dados-contratuais/pr-advanced-config-dados-contratuais.component";
import { AdmRestService } from "../../../../adm-rest.service";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { PlanoConfiguracaoSistema } from "../../../services/plano-configuracao-sistema.model";

@Component({
	selector: "adm-pr-dados-contratuais",
	templateUrl: "./pr-dados-contratuais.component.html",
	styleUrls: ["./pr-dados-contratuais.component.scss"],
})
export class PrDadosContratuaisComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	plano: Plano;

	modelosContrato: Array<any> = new Array<any>();
	configuracaoSistema: PlanoConfiguracaoSistema;

	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "PL",
	};
	form: FormGroup;
	produtoSelectBuilder: SelectFilterParamBuilder;
	modeloContratoSelectBuilder: SelectFilterParamBuilder;

	constructor(
		public admRestService: AdmRestService,
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private dialogService: DialogService,
		public produtoService: PlanoApiProdutoService,
		private planoService: PlanoService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.form = this.planoCommonsService.formDadosContratuais(
			TipoPlano.PLANO_RECORRENCIA
		);
		this.planoCommonsService.updateFormDadosContratuais(this.form, this.plano);
		this.produtoSelectBuilder = this.planoCommonsService.produtoSelectBuilder;
		this.modeloContratoSelectBuilder =
			this.planoCommonsService.modeloContratoSelectBuilder;
		this.planoCommonsService.updatePlanoDadosContratuais(this.form, this.plano);
		this.initConfiguracaoSistema();
	}

	configuracoesAvancadas() {
		const dialogRef = this.dialogService.open(
			this.traducao.getLabel("advanced-config"),
			PrAdvancedConfigDadosContratuaisComponent,
			PactoModalSize.LARGE
		);
		dialogRef.componentInstance.plano = this.planoStateService.updatePlanoObj();
		dialogRef.componentInstance.mostrarAbaFerias =
			this.configuracaoSistema.permitelancarferiasplanorecorrente;
		if (dialogRef.result) {
			dialogRef.result
				.then((value) => {
					if (this.planoStateService.plano.site) {
						this.modeloContratoAddtionalFilters.tipoContrato = "VO";
					}
				})
				.catch((error) => {});
		}
	}

	initConfiguracaoSistema() {
		this.planoService
			.configuracaoSistema()
			.subscribe((data: PlanoConfiguracaoSistema) => {
				this.configuracaoSistema = data;
			});
	}

	aplicarContratosJaLancados() {
		if (!this.plano.modeloContrato) {
			this.notificationService.error("Selecione um modelo de contrato");
			return;
		}
		const dialogRef = this.dialogService.confirm(
			"Confirmar alteração para contratos já lançados!",
			"Os contratos já lançados serão alterados permanentemente. Deseja continuar?",
			"Confirmar"
		);
		dialogRef.result.then((result) => {
			this.planoService
				.aplicarParaContratosJaLancados({
					codigo: this.plano.codigo,
					modeloDeContrato: this.plano.modeloContrato.codigo,
					tipoPlano: this.plano.tipoPlano,
				})
				.subscribe(
					(response) => {
						this.notificationService.success(
							"Texto aplicado para contratos já lançados"
						);
					},
					(httpResponseError) => {
						if (httpResponseError.error && httpResponseError.error.meta) {
							this.notificationService.error(
								httpResponseError.error.meta.message
							);
						} else {
							console.log(httpResponseError);
						}
					}
				);
		});
	}
}
