<adm-layout
	(goBack)="goBack()"
	[showConfirmDialog]="true"
	i18n-modulo="@@plano:modulo"
	i18n-pageTitle="@@plano:edit-page-title"
	modulo="Administrativo"
	pageTitle="Editar Plano: {{ nomePlano | captalize }}">
	<pacto-cat-tabs-transparent
		(action)="save()"
		[actionLabel]="'Salvar'"
		[showAction]="!isSaving && recurso.editar">
		<ng-template
			i18n-label="@@plano:edit-label-dados-basicos"
			label="Dados Básicos"
			pactoTabTransparent>
			<adm-pr-dados-basicos (nome)="nomePlano = $event"></adm-pr-dados-basicos>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-modalidades"
			label="Modalidades"
			pactoTabTransparent>
			<adm-pr-table-modalidades></adm-pr-table-modalidades>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-horarios"
			label="Horários"
			pactoTabTransparent>
			<adm-pr-table-horarios></adm-pr-table-horarios>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-prodserv"
			label="Produtos e serviços"
			pactoTabTransparent>
			<adm-pr-table-produtos></adm-pr-table-produtos>
		</ng-template>
		<ng-template
			i18n-label="@@plano:edit-label-dados-contratuais"
			label="Dados Contratuais"
			pactoTabTransparent>
			<adm-pr-dados-contratuais></adm-pr-dados-contratuais>
		</ng-template>
		<ng-template
			*ngIf="planoStateService.plano.apresentarVendaRapida"
			i18n-label="@@plano:edit-label-empresas"
			label="Empresas"
			pactoTabTransparent>
			<adm-plano-empresa></adm-plano-empresa>
		</ng-template>
		<ng-template
			*ngIf="configuracaoSistema?.permitirreplicarplanoredeempresa"
			i18n-label="@@plano:edit-label-empresas"
			label="Replicar empresa"
			pactoTabTransparent>
			<adm-replicar-plano [plano]="plano"></adm-replicar-plano>
		</ng-template>
	</pacto-cat-tabs-transparent>
</adm-layout>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@plano:saved-success" xingling="saved-success">
		Plano salvo com sucesso.
	</span>
	<span i18n="@@plano:modalidade-zerada-hint" xingling="modalidade-zerada-hint">
		Para prosseguir, remova a modalidade zerada do plano, ou então adicione mais
		uma modalidade junto que tenha valor, ou então coloque valor no cadastro
		dela.
	</span>
	<span
		i18n="@@plano:modalidade-zerada-error"
		xingling="modalidade-zerada-error">
		Você adicionou modalidade com valor zerado na aba "modalidades". Como este
		plano não foi marcado como "PLANO BOLSA", esta operação não é permitida.
	</span>
</pacto-traducoes-xingling>
