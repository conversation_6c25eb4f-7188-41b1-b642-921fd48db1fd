import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Plano, PlanoEmpresaRedeAcesso, TipoPlano } from "../../../plano.model";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import {
	BUTTON_TYPE,
	ConfirmDialogDeleteComponent,
	PactoDataGridConfig,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { PlanoApiProdutoService } from "plano-api";
import { SnotifyService } from "ng-snotify";
import { DecimalPipe } from "@angular/common";
import { PlanoService } from "../../cadastrar-plano/plano.service";
import { AdmRestService } from "../../../../adm-rest.service";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-pr-advanced-config-dados-contratuais",
	templateUrl: "./pr-advanced-config-dados-contratuais.component.html",
	styleUrls: ["./pr-advanced-config-dados-contratuais.component.scss"],
})
export class PrAdvancedConfigDadosContratuaisComponent
	implements OnInit, AfterViewInit
{
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("columnDataCobranca", { static: true })
	columnDataCobranca: TemplateRef<any>;
	@ViewChild("columnNumeroMeses", { static: true })
	columnNumeroMeses: TemplateRef<any>;
	@ViewChild("columnCarencia", { static: true })
	columnCarencia: TemplateRef<any>;

	@Input() mostrarAbaFerias: boolean;
	tableFerias: PactoDataGridConfig;
	@Input() plano: Plano;
	form: FormGroup;
	buttonType = BUTTON_TYPE;
	frequencias: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	descontoSelectBuilder: SelectFilterParamBuilder;
	listaParcelas: Array<any> = new Array<any>();
	days: Array<{ label: string; id: number }> = new Array<{
		label: string;
		id: number;
	}>();
	meses: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	tablePlanoRecorrenciaParcelas: PactoDataGridConfig;
	tableParcelasAnuidade: PactoDataGridConfig;
	parcelas: Array<any> = new Array<any>();
	parcelasMatriculas: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	_isEditable = true;
	private inicioMinimoContratoValid = true;
	modules = {};
	fontSizeArr = ["8px", "9px", "10px", "11px", "12px", "14px", "16px"];
	Size = Quill.import("attributors/style/size");

	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "VO",
	};
	modeloContratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	constructor(
		public dialog: NgbActiveModal,
		private planoStateService: PlanoStateService,
		public planoCommonsService: PlanoCommonsService,
		public produtoService: PlanoApiProdutoService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef,
		private ngbModal: NgbModal,
		public admRestService: AdmRestService,
		private planoService: PlanoService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.plano.planoRecorrencia.parcelas) {
			this.parcelas = this.plano.planoRecorrencia.parcelas;
		}
		this.form = this.planoCommonsService.formConfigDadosContratuais(
			this.plano.tipoPlano
		);
		this.initDays();
		this.initListaParcelas();
		if (this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			if (this.plano.site) {
				this.form.get("termoAceite").setValidators(Validators.required);
				this.form.get("termoAceite").updateValueAndValidity();
			} else {
				this.form.get("termoAceite").clearValidators();
				this.form.get("termoAceite").updateValueAndValidity();
				this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
				this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
			}
			this.form.get("site").valueChanges.subscribe((value) => {
				if (value) {
					this.form.get("termoAceite").setValidators(Validators.required);
					this.form.get("termoAceite").updateValueAndValidity();
				} else {
					this.form.get("termoAceite").clearValidators();
					this.form.get("termoAceite").updateValueAndValidity();
					this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
					this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
				}
			});
			this.form.get("site").valueChanges.subscribe((value) => {
				if (value) {
					this.form.get("apresentarVendaRapida").setValue(false);
				}
				this.plano.site = value;
			});
		}
		this.form.get("inicioMinimoContrato").valueChanges.subscribe((value) => {
			const now = new Date();
			now.setHours(0, 0, 0, 0);
			if (value !== "" && value < now) {
				this.form.get("inicioMinimoContrato").setErrors({ incorrect: true });
			}
			this.inicioMinimoContratoValid = this.form.get(
				"inicioMinimoContrato"
			).valid;
		});
		if (this.form.get("parcelarAnuidade").value) {
			this.plano.planoRecorrencia.parcelaAnuidade = undefined;
		} else if (this.plano.planoRecorrencia.duracaoPlano) {
			if (this.parcelasMatriculas.length > 0) {
				this.plano.planoRecorrencia.parcelaAnuidade =
					this.parcelasMatriculas[0].value;
			}
		}
		this.planoCommonsService.updateformConfigDadosContratuais(
			this.form,
			this.plano
		);
		this.form.get("parcelarAnuidade").valueChanges.subscribe((value) => {
			if (value) {
				this.plano.planoRecorrencia.parcelaAnuidade = undefined;
			} else {
				this.plano.planoRecorrencia.parcelaAnuidade =
					this.parcelasMatriculas[0].value;
			}
			return value
				? this.form.get("valorAnuidade").disable()
				: this.form.get("valorAnuidade").enable();
		});

		if (this.form.get("anuidadeNaParcela").value) {
			this.form.get("naoCobrarAnuidadeProporcional").disable();
			this.form.get("naoCobrarAnuidadeProporcional").setValue(true);
		}

		this.form.get("anuidadeNaParcela").valueChanges.subscribe((value) => {
			if (value) {
				this.form.get("naoCobrarAnuidadeProporcional").disable();
				this.form.get("naoCobrarAnuidadeProporcional").setValue(true);
			} else {
				this.form.get("naoCobrarAnuidadeProporcional").enable();
			}
		});
		if (!this.plano.codigo) {
			if (!this.plano.duracoes) {
				this.plano.duracoes = [];
				this.plano.duracoes.push({ numeroMeses: 12 });
			}
		}
		this.initTableFerias();
		this.form
			.get("gerarParcelasValorDiferente")
			.valueChanges.subscribe((value) => {
				if (value === false) {
					this.form.get("gerarParcelasValorDiferenteRenovacao").setValue(value);
				}
			});
	}

	ngAfterViewInit() {
		this.initMeses();
		this.populateParcelasMatriculas();
		this.initDiasProrata();
		this.initTableParcelasAnuidade();
		this.initTablePlanoRecorrenciaParcelas();
		this.cd.detectChanges();
	}

	initTablePlanoRecorrenciaParcelas() {
		this.tablePlanoRecorrenciaParcelas = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.parcelas,
				};
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmParcelas(row, form, data, rowIndex),
			pagination: false,
			formGroup: new FormGroup({
				parcela: new FormControl(),
				valor: new FormControl(),
			}),
			columns: [
				{
					nome: "parcela",
					titulo: this.traducao.getLabel("label-parcela"),
					visible: true,
					ordenavel: false,
					editable: true,
					width: "100px",
					inputType: "select",
					showAddSelectBtn: false,
					showSelectFilter: false,
					idSelectKey: "value",
					inputSelectData: this.parcelasMatriculas,
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimal: true,
					prefix: "R$",
					width: "100px",
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
			],
		});
	}

	beforeConfirmParcelas(row, form, data, rowIndex) {
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			rowIndex
		);
		const parcela = form.get("parcela").value.value;
		const parcelaJaExiste = data.find(
			(d, index) => d.parcela.value === row.parcela.value && index !== rowIndex
		);
		if (parcelaJaExiste) {
			this.notificationService.error(
				`${this.traducao.getLabel(
					"label-parcela"
				)} ${parcela} ${this.traducao.getLabel("label-ja-adicionada")}`
			);
			return false;
		}
		if (!exists) {
			const valor = +form.get("valor").value;
			if (
				valor === null ||
				valor === undefined ||
				parcela === undefined ||
				parcela === null ||
				parcela === ""
			) {
				this.notificationService.error(
					this.traducao.getLabel("error-todos-dados")
				);
				return false;
			}

			if (
				this.plano.planoRecorrencia &&
				valor > +this.plano.planoRecorrencia.valorMensal
			) {
				this.notificationService.error(
					this.traducao.getLabel("error-valor-parcela-maior-valor-mensalidade")
				);
				return false;
			}
		} else {
			this.notificationService.error(
				this.traducao.getLabel("dado-duplicado-table")
			);
			return false;
		}

		return true;
	}

	delete(event) {
		if (this.parcelas) {
			let index;
			if (event.row.codigo) {
				const excecao = this.parcelas.find((ex, i) => {
					if (ex.parcela.value === event.row.parcela.value) {
						index = i;
						return ex;
					}
				});
				if (excecao && index !== undefined) {
					this.parcelas.splice(index, 1);
				}
			} else {
				this.parcelas.splice(event.index, 1);
			}
			this.plano.planoRecorrencia.parcelas = this.parcelas;
			this.planoStateService.updateState(this.plano);
		}
	}

	confirm() {
		this.plano.planoRecorrencia.parcelas = this.parcelas;
		this.plano.planoRecorrencia.parcelas.forEach((parcelaRecorrencia) => {
			parcelaRecorrencia.numero = parcelaRecorrencia.parcela.value;
		});
		this.planoStateService.plano = this.plano;
	}

	initTableParcelasAnuidade() {
		if (!this.plano.planoRecorrencia.parcelasAnuidade) {
			this.plano.planoRecorrencia.parcelasAnuidade = new Array<any>();
		} else {
			this.plano.planoRecorrencia.parcelasAnuidade =
				this.plano.planoRecorrencia.parcelasAnuidade.sort((p1, p2) => {
					if (+p1.numero < +p2.numero) {
						return -1;
					} else if (+p1.numero > +p2.numero) {
						return 1;
					}
				});
		}

		this.tableParcelasAnuidade = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.plano.planoRecorrencia.parcelasAnuidade,
				};
			},
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirmParcelaAnuidade(row, form, data, rowIndex),
			pagination: false,
			formGroup: new FormGroup({
				parcela: new FormControl(),
				valor: new FormControl(),
			}),
			columns: [
				{
					nome: "parcela",
					titulo: this.traducao.getLabel("label-parcela"),
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					showAddSelectBtn: false,
					showSelectFilter: false,
					idSelectKey: "value",
					width: "150px",
					inputSelectData: this.parcelasMatriculas,
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimal: true,
					width: "100px",
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
				{
					nome: "parcela",
					titulo: this.columnDataCobranca,
					visible: true,
					ordenavel: false,
					editable: false,
					valueTransform: (v) => {
						if (v) {
							if (typeof v === "string") {
								return `${this.traducao.getLabel(
									"label-mesmo-dia-da"
								)} ${v.toUpperCase()}`;
							} else if (typeof v === "number") {
								return `${this.traducao.getLabel(
									"label-mesmo-dia-da"
								)} ${this.traducao
									.getLabel("label-parcela")
									.toUpperCase()} ${v}`;
							}
						}
						return "";
					},
				},
			],
		});
	}

	beforeConfirmParcelaAnuidade(row, form, data, rowIndex): boolean {
		if (
			form.get("parcela").value === null ||
			form.get("parcela").value === undefined
		) {
			this.notificationService.error(
				this.traducao.getLabel("error-parcela-obrigatorio")
			);
			return false;
		}

		if (
			form.get("valor").value === null ||
			form.get("valor").value === undefined ||
			form.get("valor").value === 0
		) {
			this.notificationService.error(
				this.traducao.getLabel("error-valor-parcela-obrigatorio")
			);
			return false;
		}

		if (
			data.find(
				(parcelaAnuidade, index) =>
					index !== rowIndex &&
					parcelaAnuidade.parcela.value === form.get("parcela").value.value
			)
		) {
			this.notificationService.error(
				`${this.traducao.getLabel("label-parcela")} ${
					form.get("parcela").value.value
				} ${this.traducao.getLabel("label-ja-adicionada")}`
			);
			return false;
		}

		return true;
	}

	deleteParcelaAnuidade(event) {
		if (this.plano.planoRecorrencia.parcelasAnuidade) {
			let index;
			if (event.row.codigo) {
				const excecao = this.plano.planoRecorrencia.parcelasAnuidade.find(
					(ex, i) => {
						if (ex.numero === event.row.numero) {
							index = i;
							return ex;
						}
					}
				);
				if (excecao && index !== undefined) {
					this.plano.planoRecorrencia.parcelasAnuidade.splice(index, 1);
				}
			} else {
				this.plano.planoRecorrencia.parcelasAnuidade.splice(event.index, 1);
			}
			this.updateValorAnuidadeByParcelasAnuidade(event.data);
		}
	}

	confirmParcelaAnuidade(event) {
		if (event.data) {
			this.updateValorAnuidadeByParcelasAnuidade(event.data);
		}
	}

	updateValorAnuidadeByParcelasAnuidade(data) {
		let valorAnuidade = 0;
		data.forEach((parcelaAnuidade) => {
			valorAnuidade += parcelaAnuidade.valor;
			parcelaAnuidade.numero = parcelaAnuidade.parcela.value
				? parcelaAnuidade.parcela.value
				: parcelaAnuidade.parcela;
		});
		this.form.get("valorAnuidade").setValue(valorAnuidade);
	}

	initDays() {
		for (let i = 1; i <= 31; i++) {
			this.days.push({
				label: `${i}`,
				id: i,
			});
		}
	}

	initMods(): Array<{ label: string; id: number }> {
		if (
			this.form.get("modalidadesPlanoDiferenteRenovacao").value &&
			this.form.get("modalidadesPlanoDiferenteRenovacao").value.length > 0 &&
			!this.form.get("modalidadesPlanoDiferenteRenovacao").value[0].label
		) {
			const modView = [];
			this.form.get("modalidadesPlanoDiferenteRenovacao").value.forEach((m) => {
				if (
					this.form.get("planoDiferenteRenovacao").value &&
					this.form.get("planoDiferenteRenovacao").value.modalidades
				) {
					const modalis = this.form
						.get("planoDiferenteRenovacao")
						.value.modalidades.find((mod) => mod.modalidade.codigo == m.id);
					if (modalis) {
						modView.push({
							label: modalis.modalidade.nome,
							id: modalis.modalidade.codigo,
						});
					}
				}
			});
			this.form
				.get("modalidadesPlanoDiferenteRenovacao")
				.setValue(
					modView.length > 0
						? modView
						: this.form.get("modalidadesPlanoDiferenteRenovacao").value
				);
		}

		const mods = [];
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.modalidades
		) {
			this.form
				.get("planoDiferenteRenovacao")
				.value.modalidades.forEach((m) => {
					mods.push({
						label: m.modalidade.nome,
						id: m.modalidade.codigo,
					});
				});
		}
		return mods;
	}

	initHors(): Array<{ descricao: string; codigo: number }> {
		const hors = [
			{
				descricao: "-",
				codigo: undefined,
			},
		];
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.horarios
		) {
			this.form.get("planoDiferenteRenovacao").value.horarios.forEach((h) => {
				hors.push({
					descricao: h.horario.descricao,
					codigo: h.horario.codigo,
				});
			});
		}
		return hors;
	}

	initDuracoes(): Array<{ descricao: string; codigo: number }> {
		const dura = [
			{
				descricao: "-",
				codigo: undefined,
			},
		];
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.duracoes
		) {
			this.form.get("planoDiferenteRenovacao").value.duracoes.forEach((h) => {
				dura.push({
					descricao: h.numeroMeses + (h.numeroMeses == 1 ? " Mes" : " Meses"),
					codigo: h.codigo,
				});
			});
		}
		return dura;
	}

	initConds(): Array<{ descricao: string; codigo: number }> {
		const cond = [
			{
				descricao: "-",
				codigo: undefined,
			},
		];
		if (
			this.form.get("duracaoPlanoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.duracoes
		) {
			this.form.get("planoDiferenteRenovacao").value.duracoes.forEach((d) => {
				if (d.codigo == this.form.get("duracaoPlanoDiferenteRenovacao").value) {
					d.condicoesPagamento.forEach((c) => {
						cond.push({
							descricao: c.condicaoPagamento.descricao,
							codigo: c.condicaoPagamento.codigo,
						});
					});
				}
			});
		}
		return cond;
	}

	initListaParcelas() {
		for (let i = 1; i <= 12; i++) {
			this.listaParcelas.push({
				label: `${i}x`,
				value: i,
			});
		}
	}

	initMeses() {
		this.meses.push(
			{
				label: this.traducao.getLabel("mes-janeiro"),
				value: 1,
			},
			{
				label: this.traducao.getLabel("mes-fevereiro"),
				value: 2,
			},
			{
				label: this.traducao.getLabel("mes-marco"),
				value: 3,
			},
			{
				label: this.traducao.getLabel("mes-abril"),
				value: 4,
			},
			{
				label: this.traducao.getLabel("mes-maio"),
				value: 5,
			},
			{
				label: this.traducao.getLabel("mes-junho"),
				value: 6,
			},
			{
				label: this.traducao.getLabel("mes-julho"),
				value: 7,
			},
			{
				label: this.traducao.getLabel("mes-agosto"),
				value: 8,
			},
			{
				label: this.traducao.getLabel("mes-setembro"),
				value: 9,
			},
			{
				label: this.traducao.getLabel("mes-outubro"),
				value: 10,
			},
			{
				label: this.traducao.getLabel("mes-novembro"),
				value: 11,
			},
			{
				label: this.traducao.getLabel("mes-dezembro"),
				value: 12,
			}
		);
	}

	populateParcelasMatriculas() {
		for (let i = 1; i <= this.plano.planoRecorrencia.duracaoPlano; i++) {
			this.parcelasMatriculas.push({
				label: `${this.traducao.getLabel("label-parcela")} ${i}`,
				value: i,
			});
		}
		this.parcelasMatriculas.forEach((pm) => {
			const parcela = this.parcelas.find((v) => v.numero === pm.value);
			if (parcela) {
				parcela.parcela = {
					label: pm.label,
					value: pm.value,
				};
			}
		});
		this.parcelas = this.parcelas.sort((p1, p2) => {
			if (+p1.parcela.value < +p2.parcela.value) {
				return -1;
			} else if (+p1.parcela.value > +p2.parcela.value) {
				return 1;
			}
			return 0;
		});
	}

	initDiasProrata() {
		if (!this.plano.diasVencimentoProrata) {
			this.plano.diasVencimentoProrata = "";
			this.days = this.days.sort((a, b) => {
				if (a.id < b.id) {
					return -1;
				} else if (a.id > b.id) {
					return 1;
				}
				return 0;
			});
			this.days.forEach((day, index) => {
				if (index === this.days.length - 1) {
					this.plano.diasVencimentoProrata += `${day.id}`;
				} else {
					this.plano.diasVencimentoProrata += `${day.id},`;
				}
			});
		}
	}

	salvar() {
		this.plano.planoRecorrencia.gerarParcelasValorDiferente = this.form.get(
			"gerarParcelasValorDiferente"
		).value;
		this.plano.planoRecorrencia.gerarParcelasValorDiferenteRenovacao =
			this.form.get("gerarParcelasValorDiferente").value
				? this.form.get("gerarParcelasValorDiferenteRenovacao").value
				: false;
		this.plano.planoRecorrencia.anuidadeNaParcela =
			this.form.get("anuidadeNaParcela").value;
		this.plano.planoRecorrencia.parcelarAnuidade =
			this.form.get("parcelarAnuidade").value;
		this.plano.planoRecorrencia.parcelaAnuidade =
			this.form.get("parcelaAnuidade").value;
		this.plano.planoRecorrencia.valorAnuidade =
			this.form.get("valorAnuidade").value;
		this.plano.planoRecorrencia.qtdDiasAposVencimentoCancelamentoAutomatico =
			this.form.get("qtdDiasAposVencimentoCancelamentoAutomatico").value;
		this.plano.planoRecorrencia.diaAnuidade =
			+this.form.get("diaAnuidade").value;
		this.plano.planoRecorrencia.mesAnuidade =
			+this.form.get("mesAnuidade").value;
		if (this.plano.tipoPlano !== TipoPlano.PLANO_PERSONAL) {
			this.plano.planoRecorrencia.naoCobrarAnuidadeProporcional = this.form.get(
				"naoCobrarAnuidadeProporcional"
			).value;
		}
		let diasVencimentoProrata: Array<{ label: string; id: number }> =
			this.form.get("diasVencimentoProrata").value;
		let diasVencimentoProrataStr = "";
		diasVencimentoProrata = diasVencimentoProrata.sort((a, b) => {
			if (+a.id < +b.id) {
				return -1;
			} else if (+a.id > +b.id) {
				return 1;
			}
			return 0;
		});
		diasVencimentoProrata.forEach((dvp, index) => {
			diasVencimentoProrataStr += dvp.id;
			if (index + 1 < diasVencimentoProrata.length) {
				diasVencimentoProrataStr += ",";
			}
		});
		this.plano.diasVencimentoProrata = diasVencimentoProrataStr;
		this.plano.descontoAntecipado = this.form.get("descontoAntecipado").value;
		this.plano.prorataObrigatorio = this.form.get("prorataObrigatorio").value;
		this.plano.obrigatorioInformarCartaoCreditoVenda = this.form.get(
			"obrigatorioInformarCartaoCreditoVenda"
		).value;
		this.plano.planoRecorrencia.renovavelAutomaticamente = this.form.get(
			"renovavelAutomaticamente"
		).value;
		this.plano.renovarAutomaticamenteComDesconto = this.form.get(
			"renovarAutomaticamenteComDesconto"
		).value;
		this.plano.renovarProdutoObrigatorio = this.form.get(
			"renovarProdutoObrigatorio"
		).value;
		this.plano.renovarAutomaticamenteUtilizandoValorBaseContrato =
			this.form.get("renovarAutomaticamenteUtilizandoValorBaseContrato").value;
		this.plano.planoRecorrencia.naoRenovarParcelaVencida = this.form.get(
			"naoRenovarParcelaVencida"
		).value;
		this.plano.parcelamentoOperadora = this.form.get(
			"parcelamentoOperadora"
		).value;
		this.plano.parcelamentoOperadoraDuracao = this.form.get(
			"parcelamentoOperadoraDuracao"
		).value;
		this.plano.maximoVezesParcelar = this.form.get("maximoVezesParcelar").value;
		this.plano.cobrarAdesaoSeparada = this.form.get(
			"cobrarAdesaoSeparada"
		).value;
		this.plano.nrVezesParcelarAdesao = this.form.get(
			"nrVezesParcelarAdesao"
		).value;
		this.plano.cobrarProdutoSeparado = this.form.get(
			"cobrarProdutoSeparado"
		).value;
		this.plano.nrVezesParcelarProduto = this.form.get(
			"nrVezesParcelarProduto"
		).value;
		this.plano.renovarAnuidadeAutomaticamente = this.form.get(
			"renovarAnuidadeAutomaticamente"
		).value;
		this.plano.planoRecorrencia.cancelamentoProporcional = this.form.get(
			"cancelamentoProporcional"
		).value;
		this.plano.planoRecorrencia.qtdDiasCobrarProximaParcela = this.form.get(
			"qtdDiasCobrarProximaParcela"
		).value;
		this.plano.planoRecorrencia.qtdDiasCobrarAnuidadeTotal = this.form.get(
			"qtdDiasCobrarAnuidadeTotal"
		).value;
		this.plano.planoRecorrencia.parcelas =
			this.planoStateService.plano.planoRecorrencia.parcelas;
		this.plano.aceitaDescontoExtra = this.form.get("aceitaDescontoExtra").value;
		this.plano.permitirAcessoRedeEmpresa = this.form.get(
			"permitirAcessoRedeEmpresa"
		).value;
		this.plano.site = this.form.get("site").value;
		this.plano.permitirCompartilharPLanoNoSite = this.form.get(
			"permitirCompartilharPLanoNoSite"
		).value;
		this.plano.permitirVendaPlanoSiteNoBalcao = this.form.get(
			"permitirVendaPlanoSiteNoBalcao"
		).value;
		this.plano.termoAceite = this.form.get("termoAceite").value;
		this.plano.apresentarPactoFlow = this.form.get("apresentarPactoFlow").value;
		this.plano.permitirTurmasVendasOnline = this.form.get(
			"permitirTurmasVendasOnline"
		).value;
		this.plano.videoSiteUrl = this.form.get("videoSiteUrl").value;
		this.plano.observacaoSite = this.form.get("observacaoSite").value;
		this.plano.diasBloquearCompraMesmoPlano = this.form.get(
			"diasBloquearCompraMesmoPlano"
		).value;
		this.plano.inicioMinimoContrato = this.form.get(
			"inicioMinimoContrato"
		).value;
		if (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.codigo
		) {
			if (
				this.plano.regimeRecorrencia &&
				!this.form.get("planoDiferenteRenovacao").value.regimeRecorrencia
			) {
				this.notificationService.error(
					"O plano selecionado não é do mesmo tipo do plano atual: Recorrência"
				);
				return;
			}
			if (!this.form.get("horarioPlanoDiferenteRenovacao").value) {
				this.notificationService.error(
					"Campo obrigatório para renovação plano diferente: Horário"
				);
				return;
			} else if (!this.form.get("duracaoPlanoDiferenteRenovacao").value) {
				this.notificationService.error(
					"Campo obrigatório para renovação plano diferente: Duração"
				);
				return;
			} else if (!this.form.get("condicaoPagPlanoDiferenteRenovacao").value) {
				this.notificationService.error(
					"Campo obrigatório para renovação plano diferente: Condição de Pagamento"
				);
				return;
			} else if (
				!this.form.get("planoDiferenteRenovacao").value.regimeRecorrencia &&
				!this.form.get("modalidadesPlanoDiferenteRenovacao").value
			) {
				this.notificationService.error(
					"Campo obrigatório para renovação plano diferente: Modalidade"
				);
				return;
			} else {
				this.plano.planoDiferenteRenovacao = this.form.get(
					"planoDiferenteRenovacao"
				).value.codigo;
				this.plano.horarioPlanoDiferenteRenovacao = this.form.get(
					"horarioPlanoDiferenteRenovacao"
				).value;
				this.plano.duracaoPlanoDiferenteRenovacao = this.form.get(
					"duracaoPlanoDiferenteRenovacao"
				).value;
				this.plano.condicaoPagPlanoDiferenteRenovacao = this.form.get(
					"condicaoPagPlanoDiferenteRenovacao"
				).value;
				if (this.form.get("modalidadesPlanoDiferenteRenovacao").value) {
					this.plano.modalidadesPlanoDiferenteRenovacao = this.form
						.get("modalidadesPlanoDiferenteRenovacao")
						.value.map((item) => item.id.toString())
						.join(", ");
				}
			}
		} else {
			this.plano.planoDiferenteRenovacao = null;
			this.plano.horarioPlanoDiferenteRenovacao = null;
			this.plano.duracaoPlanoDiferenteRenovacao = null;
			this.plano.condicaoPagPlanoDiferenteRenovacao = null;
			this.plano.modalidadesPlanoDiferenteRenovacao = null;
			this.form.get("planoDiferenteRenovacao").setValue(null);
			this.form.get("modalidadesPlanoDiferenteRenovacao").setValue(null);
			this.form.get("horarioPlanoDiferenteRenovacao").setValue(null);
			this.form.get("duracaoPlanoDiferenteRenovacao").setValue(null);
			this.form.get("condicaoPagPlanoDiferenteRenovacao").setValue(null);
		}
		if (this.plano.planoRecorrencia) {
			if (
				this.plano.planoRecorrencia &&
				this.plano.tipoPlano !== TipoPlano.PLANO_PERSONAL
			) {
				if (!this.plano.planoRecorrencia.anuidadeNaParcela) {
					if (
						(!this.plano.planoRecorrencia.diaAnuidade ||
							this.plano.planoRecorrencia.diaAnuidade <= 0) &&
						(!this.plano.planoRecorrencia.mesAnuidade ||
							this.plano.planoRecorrencia.mesAnuidade <= 0)
					) {
						this.notificationService.error(
							this.traducao.getLabel("error-dia-mes-anuidade-obrigatorio"),
							this.traducao.getLabel("label-anuidade")
						);
						return;
					}
					if (
						!this.plano.planoRecorrencia.diaAnuidade ||
						this.plano.planoRecorrencia.diaAnuidade <= 0
					) {
						this.notificationService.error(
							this.traducao.getLabel("error-dia-anuidade-obrigatorio"),
							this.traducao.getLabel("label-anuidade")
						);
						return;
					}
					if (
						!this.plano.planoRecorrencia.mesAnuidade ||
						this.plano.planoRecorrencia.mesAnuidade <= 0
					) {
						this.notificationService.error(
							this.traducao.getLabel("error-mes-anuidade-obrigatorio"),
							this.traducao.getLabel("label-anuidade")
						);
						return;
					}
				} else if (this.plano.planoRecorrencia.parcelarAnuidade) {
					if (this.plano.planoRecorrencia.parcelasAnuidade.length === 0) {
						this.notificationService.error(
							this.traducao.getLabel("error-sem-parcela"),
							this.traducao.getLabel("label-anuidade")
						);
						return;
					}
				}
			}
		}
		if (!this.inicioMinimoContratoValid) {
			this.notificationService.error(
				this.traducao.getLabel("error-data-retroativa-obrigatorio"),
				this.traducao.getLabel("error-location-vendas-online")
			);
		}

		this.plano.acessoRedeEmpresasEspecificas = this.form.get(
			"acessoRedeEmpresasEspecificas"
		).value;

		this.plano.observacao1 = this.form.get("observacao1").value;
		this.plano.observacao2 = this.form.get("observacao2").value;
		this.planoStateService.updateState(this.plano);
		this.dialog.close();
	}

	isEditable() {
		if (
			this.plano &&
			this.plano.planoRecorrencia &&
			(this.plano.planoRecorrencia.duracaoPlano === "" ||
				this.plano.planoRecorrencia.duracaoPlano === 0)
		) {
			this.notificationService.warning(
				this.traducao.getLabel("warn-fidelidade-obrigatoria-adicionar-parcela")
			);
			this.form.get("gerarParcelasValorDiferente").disable();
			this.form.get("gerarParcelasValorDiferenteRenovacao").disable();
		}
	}

	initTableFerias() {
		this.tableFerias = new PactoDataGridConfig({
			dataAdapterFn: () => {
				return {
					content: this.plano.duracoes,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				carencia: new FormControl(),
			}),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "100px",
				},
				{
					nome: "numeroMeses",
					titulo: this.columnNumeroMeses,
					visible: true,
					ordenavel: false,
					valueTransform: (v) => v + "m",
					width: "100px",
				},
				{
					nome: "carencia",
					titulo: this.columnCarencia,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					width: "100px",
				},
			],
		});
	}

	openModalConfirmacaoAplicarDiasVencimentoContratosAtivos() {
		this.planoService
			.consultarQuantidadeContratosAtivosPorPlano(this.plano.codigo)
			.subscribe((data: any) => {
				const modalConfirmacao = this.ngbModal.open(
					ConfirmDialogDeleteComponent,
					{ windowClass: "modal-confirmacao" }
				);
				if (modalConfirmacao.result) {
					modalConfirmacao.result
						.then((excluir) => {
							this.plano.planoRecorrencia.aplicarDiasVencimentoContratosAtivos =
								true;
						})
						.catch((error) => {});
				}
				modalConfirmacao.componentInstance.titulo =
					"Aplicar a contratos ativos";
				if (
					this.form.get("qtdDiasAposVencimentoCancelamentoAutomatico").value ===
					0
				) {
					modalConfirmacao.componentInstance.texto =
						"Esta ação irá desabilitar o cancelamento automático para os contratos ativos deste plano. Tem certeza disso?";
				} else {
					modalConfirmacao.componentInstance.texto =
						"Os contratos ativos deste plano serão modificados para cancelamento automático caso tenha uma parcela vencida a mais de " +
						this.form.get("qtdDiasAposVencimentoCancelamentoAutomatico").value +
						" dias. Tem certeza disso?";
				}
				modalConfirmacao.componentInstance.textoAlerta =
					"Atenção: essa ação irá impactar " + data.content + " contratos.";
				modalConfirmacao.componentInstance.actionLabel = "Aplicar";

				this.cd.detectChanges();
			});
	}

	openModalConfirmacaoAplicarConfiguracoesCancelamentoProporcionalContratosAtivos() {
		this.planoService
			.consultarQuantidadeContratosAtivosPorPlano(this.plano.codigo)
			.subscribe((data: any) => {
				const modalConfirmacao = this.ngbModal.open(
					ConfirmDialogDeleteComponent,
					{ windowClass: "modal-confirmacao" }
				);
				if (modalConfirmacao.result) {
					modalConfirmacao.result
						.then((excluir) => {
							this.plano.planoRecorrencia.aplicarCancelamentoProporcionalContratosAtivos =
								true;
						})
						.catch((error) => {});
				}
				modalConfirmacao.componentInstance.titulo =
					"Aplicar a contratos ativos";
				if (this.form.get("cancelamentoProporcional").value === false) {
					modalConfirmacao.componentInstance.texto =
						"Esta ação irá desabilitar o cancelamento verificando parcela para os contratos ativos deste plano. Tem certeza disso?";
				} else {
					modalConfirmacao.componentInstance.texto =
						"Os contratos ativos deste plano serão modificados para Cancelamento verificando parcela. Tem certeza disso?";
				}
				modalConfirmacao.componentInstance.textoAlerta =
					"Atenção: essa ação irá impactar " + data.content + " contratos.";
				modalConfirmacao.componentInstance.actionLabel = "Aplicar";

				this.cd.detectChanges();
			});
	}

	dateFilter(date: Date): boolean {
		const actualDate = new Date();
		actualDate.setHours(0, 0, 0, 0);
		return date.getTime() >= actualDate.getTime();
	}

	existeModalidadeTurmaSelecionada(): boolean {
		if (
			this.plano &&
			this.plano.modalidades &&
			(!this.plano.modalidadesAux || this.plano.modalidadesAux.length === 0)
		) {
			const index = this.plano.modalidades.findIndex(
				(pm) => pm.modalidade.utilizaTurma
			);
			return index >= 0;
		} else if (this.plano.modalidadesAux) {
			const index = this.plano.modalidadesAux.findIndex(
				(pm) => pm.modalidade.utilizaTurma
			);
			return index >= 0;
		}
		return false;
	}

	getPlanosPermitidos() {
		return (
			this.plano.tipoPlano === TipoPlano.PLANO_NORMAL ||
			this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA
		);
	}

	selectBuilderPlanosDiferentes: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "500",
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	isPlanoDiferenteTurma(): boolean {
		return (
			this.form.get("planoDiferenteRenovacao").value &&
			this.form.get("planoDiferenteRenovacao").value.codigo &&
			this.form
				.get("planoDiferenteRenovacao")
				.value.modalidades.some((pM: any) => pM.modalidade.utilizaTurma)
		);
	}

	limparCamposPlanoDiferente(todos) {
		if (todos) {
			this.plano.planoDiferenteRenovacao = null;
			this.form.get("planoDiferenteRenovacao").setValue(null);
		}
		this.plano.horarioPlanoDiferenteRenovacao = null;
		this.plano.duracaoPlanoDiferenteRenovacao = null;
		this.plano.condicaoPagPlanoDiferenteRenovacao = null;
		this.plano.modalidadesPlanoDiferenteRenovacao = null;
		this.form.get("horarioPlanoDiferenteRenovacao").setValue(null);
		this.form.get("duracaoPlanoDiferenteRenovacao").setValue(null);
		this.form.get("condicaoPagPlanoDiferenteRenovacao").setValue(null);
		this.form.get("modalidadesPlanoDiferenteRenovacao").setValue(null);
		this.cd.detectChanges();
	}

	empresasRedeUpdate(empresasRede: Array<PlanoEmpresaRedeAcesso>) {
		this.plano.empresasRede = empresasRede;
	}
}
