<pacto-cat-tabs-vertical>
	<ng-template
		i18n-label="@@adm:tab-label-parcela-valor-diferente"
		label="Parcelas com valor diferente"
		pactoTabVertical="parcela-valor-diferente">
		<pacto-cat-checkbox
			(click)="isEditable()"
			[control]="form.get('gerarParcelasValorDiferente')"
			i18n-label="@@adm:label-gerar-parcela-valor-diferente"
			i18n-title="
				@@adm:title-gerar-parcela-valor-diferente-matricula-rematricula"
			id="check-gerar-parcela-valor-diferente"
			label="Definir parcelas com valor diferente (matrícula ou rematrícula)"
			title="Ao habilitar essa configuração o sistema passará a gerar as parcelas dos contratos(matrícula ou rematrícula), considerando os valores adicionados abaixo."></pacto-cat-checkbox>
		<pacto-cat-checkbox
			(click)="isEditable()"
			*ngIf="form.get('gerarParcelasValorDiferente').value"
			[control]="form.get('gerarParcelasValorDiferenteRenovacao')"
			i18n-label="@@adm:label-gerar-parcela-valor-diferente-renovacao"
			i18n-title="@@adm:title-gerar-parcela-valor-diferente-renovacao"
			id="check-gerar-parcela-valor-diferente-renovacao"
			label="Utilizar também na renovação"
			title="Ao habilitar essa configuração o sistema passará a gerar as parcelas dos contratos(renovação), considerando os valores adicionados abaixo."></pacto-cat-checkbox>
		<pacto-cat-table-editable
			(confirm)="confirm()"
			(delete)="delete($event)"
			*ngIf="form.get('gerarParcelasValorDiferente').value"
			[isEditable]="true"
			[table]="tablePlanoRecorrenciaParcelas"
			idSuffix="config-parcelas-recorrencia"
			showAddRow="true"></pacto-cat-table-editable>
	</ng-template>

	<ng-template
		*ngIf="mostrarAbaFerias"
		i18n-label="@@adm:tab-label-ferias"
		label="Férias"
		pactoTabVertical="ferias">
		<div class="table-wrapper pacto-shadow">
			<pacto-cat-table-editable
				(confirm)="confirmFerias($event)"
				[isEditable]="true"
				[showDelete]="false"
				[table]="tableFerias"
				class="table-ferias"
				idSuffix="config-ferias"></pacto-cat-table-editable>
		</div>
	</ng-template>

	<ng-template
		*ngIf="
			!form.get('apresentarVendaRapida').value &&
			plano.tipoPlano !== 'PLANO_PERSONAL'
		"
		i18n-label="@@adm:tab-label-vendas-online"
		label="Vendas online"
		pactoTabVertical="vendas-online">
		<pacto-cat-checkbox
			[control]="form.get('site')"
			i18n-label="@@adm:label-vendas-online"
			id="check-vendas-online"
			label="Participa do Vendas Online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-compartilhar-plano-site"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirCompartilharPLanoNoSite')"
			label="Permite compartilhar plano no vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="
				form.get('site').value &&
				existeModalidadeTurmaSelecionada() &&
				getPlanosPermitidos()
			"
			[control]="form.get('permitirTurmasVendasOnline')"
			i18n-label="@@adm:label-permitir-turmas-vendas-online"
			id="check-permitir-turma-vendas-online"
			label="Permitir turmas no Vendas Online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('apresentarPactoFlow')"
			label="Apresentar no Pacto Flow"></pacto-cat-checkbox>
		<pacto-cat-form-input-number
			id="diasBloquearCompraMesmoPlano"
			*ngIf="form.get('site').value"
			label="Intervalo de recompra do plano"
			placeholder="0"
			[max]="'9999'"
			[formControl]="
				form.get('diasBloquearCompraMesmoPlano')
			"></pacto-cat-form-input-number>
		<pacto-cat-form-datepicker
			[control]="form.get('inicioMinimoContrato')"
			[dateFilter]="dateFilter"
			errorMsg="Informe uma data não retroativa!"
			i18n-errorMsg="@@adm:error-inicio-minimo-contrato"
			i18n-label="@@adm:label-inicio-minimo-contrato"
			id="datepicker-inicio-minimo-contrato"
			label="Contratos iniciam a partir de"></pacto-cat-form-datepicker>
		<pacto-cat-form-select-filter
			*ngIf="form.get('site').value"
			[addtionalFilters]="modeloContratoAddtionalFilters"
			[control]="form.get('termoAceite')"
			[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
			[idKey]="'codigo'"
			[labelKey]="'descricao'"
			[paramBuilder]="modeloContratoSelectBuilder"
			errorMsg="Selecione um termo de aceite"
			i18n-errorMsg="@@adm:label-termo-aceite"
			i18n-label="@@adm:label-termo-aceite"
			id="select-termo-aceite"
			label="Termo de aceite"></pacto-cat-form-select-filter>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12">
				<div class="input-nome">Vídeo youtube</div>
				<input
					[formControl]="form.get('videoSiteUrl')"
					class="input-text"
					placeholder="Informe aqui o código do vídeo no youtube Ex: 6AidSn00VGo"
					type="text" />
			</div>
		</div>

		<label *ngIf="form.get('site').value" class="input-nome">Observação</label>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12 div-text-area-modelo-contrato">
				<quill-editor
					[formControl]="form.get('observacaoSite')"
					[id]="'editor-texto-observacao'"
					[modules]="modules"></quill-editor>
			</div>
		</div>
	</ng-template>

	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-label-anuidade"
		label="Anuidade"
		pactoTabVertical="anuidade">
		<div class="d-flex align-items-center">
			<pacto-cat-checkbox
				[control]="form.get('anuidadeNaParcela')"
				i18n-label="@@adm:label-anuidade-mesmo-venc-parcela"
				id="check-anuidade-mesmo-venc-parcela"
				label="Anuidade com mesmo vencimento da parcela"></pacto-cat-checkbox>

			<i
				class="pct pct-help-circle cor-azulim-pri ms-3"
				title="Ao marcar essa opção, a anuidade será cobrada na mesma data de vencimento da parcela. Na renovação do plano, a taxa de anuidade também será incluída automaticamente."></i>
		</div>

		<div
			class="d-flex align-items-center"
			*ngIf="
				(plano?.renovavelAutomaticamente ||
					form.get('renovavelAutomaticamente').value == true) &&
				!form.get('anuidadeNaParcela').value
			">
			<pacto-cat-checkbox
				[control]="form.get('renovarAnuidadeAutomaticamente')"
				i18n-label="@@adm:label-renovar-anuidade-automaticamente"
				id="check-renovar-anuidade-automaticamente"
				label="Renovar anuidade automaticamente"></pacto-cat-checkbox>

			<i
				class="pct pct-help-circle cor-azulim-pri ms-3"
				title="Ao marcar essa opção, na renovação do plano a taxa de anuidade também será incluída automaticamente."></i>
		</div>

		<pacto-cat-checkbox
			[control]="form.get('naoCobrarAnuidadeProporcional')"
			i18n-label="@@adm:label-nao-cobrar-anuidade-proporcional"
			id="check-nao-cobrar-anuidade-proporcional"
			label="Não cobrar anuidade proporcional"></pacto-cat-checkbox>
		<pacto-cat-form-input-number
			[decimal]="true"
			[formControl]="form.get('valorAnuidade')"
			i18n-label="@@adm:label-valor"
			id="input-valor"
			label="Valor"></pacto-cat-form-input-number>
		<ng-container *ngIf="!form.get('anuidadeNaParcela').value">
			<pacto-cat-form-input-number
				[formControl]="form.get('diaAnuidade')"
				i18n-label="@@adm:label-dia"
				id="input-dia"
				label="Dia"
				max="31"></pacto-cat-form-input-number>
			<pacto-cat-form-select
				[control]="form.get('mesAnuidade')"
				[items]="meses"
				i18n-label="@@adm:label-mes"
				id="input-mes"
				idKey="value"
				label="Mês"></pacto-cat-form-select>
		</ng-container>
		<ng-container *ngIf="form.get('anuidadeNaParcela').value">
			<pacto-cat-checkbox
				[control]="form.get('parcelarAnuidade')"
				i18n-label="@@adm:label-parcelar-anuidade"
				id="check-parcelar-anuidade"
				label="Parcelar anuidade"></pacto-cat-checkbox>
			<pacto-cat-table-editable
				(confirm)="confirmParcelaAnuidade($event)"
				(delete)="deleteParcelaAnuidade($event)"
				*ngIf="form.get('parcelarAnuidade').value"
				[isEditable]="true"
				[table]="tableParcelasAnuidade"
				idSuffix="parcelas-anuidade"
				showAddRow="true"></pacto-cat-table-editable>
			<pacto-cat-form-select
				*ngIf="!form.get('parcelarAnuidade').value"
				[control]="form.get('parcelaAnuidade')"
				[items]="parcelasMatriculas"
				i18n-label="@@adm:label-parcela-anuidade"
				id="select-parcela-anuidade"
				idKey="value"
				label="Parcela da anuidade"></pacto-cat-form-select>
		</ng-container>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-data-pagamento"
		label="Data de pagamento"
		pactoTabVertical="data-pagamento">
		<pacto-cat-form-multi-select-filter
			[control]="form.get('diasVencimentoProrata')"
			[options]="days"
			i18n-label="adm:label-dias-prorata"
			id="select-dias-prorata"
			label="Escolha os dias do mês"></pacto-cat-form-multi-select-filter>
		<div>
			<pacto-cat-checkbox
				[control]="form.get('prorataObrigatorio')"
				i18n-label="adm:label-prorata-obrigatorio"
				id="check-prorata-obrigatorio"
				label="Data de vencimento obrigátoria"></pacto-cat-checkbox>
		</div>
		<ng-container *ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'">
			<pacto-cat-checkbox
				[control]="form.get('obrigatorioInformarCartaoCreditoVenda')"
				i18n-label="adm:label-obrigatorio-informar-cartao-venda"
				id="check-obrigatorio-informar-cartao-venda"
				label="Obrigatoriedade de cadastro de cartão de crédito para venda"></pacto-cat-checkbox>
		</ng-container>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-label-renovacao-automatica"
		label="Renovação automática"
		pactoTabVertical="renovacao-automatica">
		<pacto-cat-checkbox
			[control]="form.get('renovarAutomaticamenteComDesconto')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovar-plano-desconto"
			id="renovar-plano-desconto"
			label="Renovar plano com desconto"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarProdutoObrigatorio')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-renovar-produto-obrigatorio"
			id="check-renovar-produto-obrigatorio"
			label="Renovar produtos obrigatórios"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('renovarAutomaticamenteUtilizandoValorBaseContrato')"
			class="mt-16 mb-16 display-block"
			i18n-label="
				adm:label-renovar-automaticamente-utilizando-valor-base-contrato
			"
			id="check-renovar-auto-valor-base-contrato"
			label="Renovar contrato automaticamente utilizando valor base do contrato"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			class="mt-16 mb-16 display-block"
			id="check-renovavel-automaticamente"
			i18n-label="adm:label-renovavel-automaticamente"
			label="Ativar renovação automática"
			(click)="limparCamposPlanoDiferente(true)"
			[control]="form.get('renovavelAutomaticamente')"></pacto-cat-checkbox>
		<pacto-cat-form-select-filter
			*ngIf="form.get('renovavelAutomaticamente').value"
			[label]="'Selecionar plano para renovação automática'"
			[control]="form.get('planoDiferenteRenovacao')"
			[endpointUrl]="admRestService.buildFullUrlPlano('planos')"
			labelKey="descricao"
			idKey="codigo"
			(click)="limparCamposPlanoDiferente(false)"
			[addEmptyOption]="true"
			class="bottom-renov-selec"
			[paramBuilder]="
				selectBuilderPlanosDiferentes
			"></pacto-cat-form-select-filter>
		<div style="width: 100%">
			<span
				style="color: red; text-align: center; margin-top: 10px; display: block"
				*ngIf="
					form.get('renovavelAutomaticamente').value && isPlanoDiferenteTurma()
				">
				Plano selecionado possui turma e não é permitido, escolha outro.
			</span>
		</div>
		<pacto-cat-form-multi-select-filter
			label="Modalidades"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!form.get('planoDiferenteRenovacao').value.regimeRecorrencia &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('modalidadesPlanoDiferenteRenovacao')"
			[options]="initMods()"></pacto-cat-form-multi-select-filter>
		<pacto-cat-form-select
			[label]="'Horario'"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('horarioPlanoDiferenteRenovacao')"
			[items]="initHors()"
			labelKey="descricao"
			idKey="codigo"></pacto-cat-form-select>
		<pacto-cat-form-select
			[label]="'Duração'"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('duracaoPlanoDiferenteRenovacao')"
			[items]="initDuracoes()"
			labelKey="descricao"
			idKey="codigo"></pacto-cat-form-select>
		<pacto-cat-form-select
			[label]="'Condição de Pagamento'"
			*ngIf="
				form.get('planoDiferenteRenovacao').value?.codigo &&
				!isPlanoDiferenteTurma()
			"
			[control]="form.get('condicaoPagPlanoDiferenteRenovacao')"
			[items]="initConds()"
			labelKey="descricao"
			idKey="codigo"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-nao-renovar-parcela-aberta"
		label="Não renovar com parcela aberta"
		pactoTabVertical="nao-renovar-parcela-aberta">
		<pacto-cat-checkbox
			[control]="form.get('naoRenovarParcelaVencida')"
			class="mt-16 mb-16 display-block"
			i18n-label="adm:label-nao-renovar-parcela-aberta-vencida"
			id="check-nao-renovar-parcela-aberta-vencida"
			label="Não renovar com parcela aberta (vencida)"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-parcela-pela-operado"
		label="Parcela pela operadora"
		pactoTabVertical="parcela-operadora">
		<ng-container>
			<pacto-cat-checkbox
				[control]="form.get('parcelamentoOperadora')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-permite-parcelamento-operadora"
				id="check-permite-parcelamento-operadora"
				label="Permite parcelamento pela operadora"></pacto-cat-checkbox>
			<pacto-cat-checkbox
				*ngIf="form.get('parcelamentoOperadora').value"
				[control]="form.get('parcelamentoOperadoraDuracao')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-permite-parcelamento-operadora-duracao"
				id="check-permite-parcelamento-operadora-duracao"
				label="Número de vezes de acordo com a duração do contrato/plano"></pacto-cat-checkbox>
			<pacto-cat-form-select
				*ngIf="
					form.get('parcelamentoOperadora').value &&
					!form.get('parcelamentoOperadoraDuracao').value
				"
				[control]="form.get('maximoVezesParcelar')"
				[items]="listaParcelas"
				i18n-label="@@adm:label-maximo-vezes-parcelar"
				id="select-num-max-parcelas"
				idKey="value"
				label="Número máximo de parcelas"></pacto-cat-form-select>
		</ng-container>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cobrar-adesao-separada"
		label="Cobrar adesão separada"
		pactoTabVertical="cobrar-adesao-separada">
		<pacto-cat-checkbox
			[control]="form.get('cobrarAdesaoSeparada')"
			class="mt-16 mb-16 display-block"
			i18n-label="@@adm:label-permite-cobrar-adesao-separada"
			id="check-permite-cobrar-adesao-separada"
			label="Permite cobrar adesão separada"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarAdesao')"
			[items]="listaParcelas"
			i18n-label="@@adm:label-numero-maximo-parcelas"
			id="select-numero-maximo-parcelas"
			idKey="value"
			label="Número máximo de parcelas"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-label-cobrar-produtos-separado"
		label="Cobrar produtos separado"
		pactoTabVertical="cobrar-produtos-separado">
		<pacto-cat-checkbox
			[control]="form.get('cobrarProdutoSeparado')"
			i18n-label="@@adm:label-permite-cobrar-produtos-separados"
			id="check-permite-cobrar-produtos-separados"
			label="Permite cobrar produtos separados"></pacto-cat-checkbox>
		<pacto-cat-form-select
			[control]="form.get('nrVezesParcelarProduto')"
			[items]="listaParcelas"
			i18n-label="@@adm:label-parcelar-produtos"
			id="select-parcelar-produtos"
			idKey="value"
			label="Parcelar produtos"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cancelamento-verificando-parcela"
		label="Cancelamento verificando parcela"
		pactoTabVertical="cancelamento-verificando-parcelas">
		<div style="width: 480px">
			<pacto-cat-checkbox
				[control]="form.get('cancelamentoProporcional')"
				class="mt-16 mb-16 display-block"
				i18n-label="@@adm:label-cancelamento-proporcional"
				id="check-cancelamento-proporcional"
				label="Habilitar cancelamento verificando próxima parcela em aberto"></pacto-cat-checkbox>
			<pacto-cat-form-input-number
				[decimal]="false"
				[formControl]="form.get('qtdDiasCobrarProximaParcela')"
				i18n-label="@@adm:label-qtd-dias-cobrar-proxima-parcela"
				id="input-qtd-dias-cobrar-prox-parcela"
				label="Prazo para avisar com antecedência para não cobrar próxima parcela"></pacto-cat-form-input-number>
			<pacto-cat-form-input-number
				[decimal]="false"
				[formControl]="form.get('qtdDiasCobrarAnuidadeTotal')"
				i18n-label="@@adm:label-qtd-dias-cobrar-anuidade-total"
				id="input-qtd-dias-cobrar-anuidade-total"
				label="Prazo para cobrar valor total de anuidade antes do vencimento"></pacto-cat-form-input-number>
			<pacto-cat-button
				(click)="
					openModalConfirmacaoAplicarConfiguracoesCancelamentoProporcionalContratosAtivos()
				"
				[type]="buttonType.PRIMARY"
				i18n-label="@@adm:aplicar-dias-vencimento-contratos-ativos"
				label="Aplicar a contratos ativos"></pacto-cat-button>
		</div>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-desconto"
		label="Desconto"
		pactoTabVertical="desconto">
		<pacto-cat-checkbox
			[control]="form.get('aceitaDescontoExtra')"
			i18n-label="@@adm:label-aceita-desconto-extra"
			id="check-aceita-desconto-extra"
			label="Aceita dar desconto extra"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-cancelamento-auto-contrato"
		label="Cancelamento Automático do Contrato"
		pactoTabVertical="cancelamento-auto-contrato">
		<pacto-cat-form-input-number
			[decimal]="false"
			[formControl]="form.get('qtdDiasAposVencimentoCancelamentoAutomatico')"
			i18n-label="@@adm:label-qtd-dia-apos-venc-cancel-auto"
			id="input-qtd-dia-apos-venc-cancel-auto"
			label="Quantidade de dias após vencimento da parcela para cancelamento automático do contrato"></pacto-cat-form-input-number>

		<pacto-cat-button
			(click)="openModalConfirmacaoAplicarDiasVencimentoContratosAtivos()"
			[type]="buttonType.PRIMARY"
			i18n-label="@@adm:aplicar-dias-vencimento-contratos-ativos"
			label="Aplicar a contratos ativos"></pacto-cat-button>
	</ng-template>
	<ng-template
		*ngIf="
			planoCommonsService.isPertenceRedeEmpresa &&
			plano.tipoPlano !== 'PLANO_PERSONAL'
		"
		i18n-label="@@adm:tab-label-plano-vip"
		label="Plano Vip"
		pactoTabVertical="plano-vip">
		<pacto-cat-checkbox
			[control]="form.get('permitirAcessoRedeEmpresa')"
			i18n-label="@@adm:label-permitir-acesso-rede-empresa"
			id="check-permitir-acesso-rede-empresa"
			label="Permitir acesso as empresas da rede"></pacto-cat-checkbox>
		<ng-container *ngIf="form.get('permitirAcessoRedeEmpresa').value">
			<pacto-cat-checkbox
				id="check-especificar-empresas-acesso-rede"
				[control]="form.get('acessoRedeEmpresasEspecificas')"
				i18n-label="@@adm:label-especificar-empresas-acesso-rede"
				label="Especificar empresas"></pacto-cat-checkbox>
			<adm-plano-empresa-rede-acesso
				[empresasRede]="plano.empresasRede"
				(empresasRedeUpdate)="empresasRedeUpdate($event)"
				*ngIf="
					form.get('acessoRedeEmpresasEspecificas').value
				"></adm-plano-empresa-rede-acesso>
		</ng-container>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-observacoes"
		label="Observações"
		pactoTabVertical="obsercacoes">
		<div class="input-nome">Observação 1</div>
		<input
			[formControl]="form.get('observacao1')"
			class="input-text"
			type="text" />

		<div class="input-nome">Observação 2</div>
		<input
			[formControl]="form.get('observacao2')"
			class="input-text"
			type="text" />
	</ng-template>
</pacto-cat-tabs-vertical>

<div class="action-container btn-row-adm">
	<pacto-cat-button
		(click)="salvar()"
		[type]="buttonType.PRIMARY"
		i18n-label="@@adm:btn-salvar-config"
		id="btn-save-config"
		label="Salvar Configurações"></pacto-cat-button>
</div>

<ng-template #columnValor>
	<span i18n="@@adm:valor">Valor</span>
</ng-template>

<ng-template #columnDataCobranca>
	<span i18n="@@adm:data-cobranca">Data Cobrança</span>
</ng-template>

<ng-template #columnNumeroMeses>
	<span i18n="@@adm:pc-column-duracao-contrato">Duração do contrato</span>
</ng-template>

<ng-template #columnCarencia>
	<span i18n="@@adm:column-carencia">Dias de férias</span>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@adm:error-todos-dados" xingling="error-todos-dados">
		Informe todos os dados!
	</span>
	<span
		i18n="@@adm:error-valor-parcela-maior-valor-mensalidade"
		xingling="error-valor-parcela-maior-valor-mensalidade">
		O valor da parcela não deve ser maior ou igual ao valor da mensalidade!
	</span>
	<span i18n="@@adm:dado-duplicado-table" xingling="dado-duplicado-table">
		Já existe um dado na tabela com estes valores.
	</span>
	<span i18n="@@adm:label-parcela" xingling="label-parcela">Parcela</span>
	<span i18n="@@adm:label-ja-adicionada" xingling="label-ja-adicionado">
		já foi adicionada!
	</span>
	<span
		i18n="@@adm:error-valor-parcela-obrigatorio"
		xingling="error-valor-parcela-obrigatorio">
		Informe o valor da parcela!
	</span>
	<span
		i18n="@@adm:error-parcela-obrigatoria"
		xingling="error-parcela-obrigatoria">
		Selecione uma parcela!
	</span>
	<span i18n="@@adm:label-mesmo-dia-da" xingling="label-mesmo-dia-da">
		No mesmo dia da
	</span>
	<span i18n="@@adm:label-anuidade" xingling="label-anuidade">Anuidade</span>
	<span
		i18n="@@adm:error-dia-mes-anuidade-obrigatorio"
		xingling="error-dia-mes-anuidade-obrigatorio">
		Informe o dia e o mês da anuidade
	</span>
	<span
		i18n="@@adm:error-dia-anuidade-obrigatorio"
		xingling="error-dia-anuidade-obrigatorio">
		Informe o dia da anuidade
	</span>
	<span
		i18n="@@adm:error-mes-anuidade-obrigatorio"
		xingling="error-mes-anuidade-obrigatorio">
		Informe o mês da anuidade
	</span>
	<span i18n="@@adm:error-sem-parcela" xingling="error-sem-parcela">
		Insira ao menos uma parcela
	</span>
	<span
		i18n="@@adm:warn-fidelidade-obrigatoria-adicionar-parcela"
		xingling="warn-fidelidade-obrigatoria-adicionar-parcela">
		Informe a fidelidade do plano para poder adicionar as parcelas!
	</span>
	<span
		i18n="@@plano:error-data-retroativa-obrigatorio"
		xingling="error-data-retroativa-obrigatorio">
		Informe uma data não retroativa!
	</span>
	<span
		i18n="@@plano:error-location-vendas-online"
		xingling="error-location-vendas-online">
		Vendas online
	</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-janeiro">Janeiro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-fevereiro">Fevereiro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-marco">Março</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-abril">Abril</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-maio">Maio</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-junho">Junho</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-julho">Julho</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-agosto">Agosto</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-setembro">Setembro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-outubro">Outubro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-novembro">Novembro</span>
	<span i18n="@@adm:mes-janeiro" xingling="mes-dezembro">Dezembro</span>
</pacto-traducoes-xingling>
