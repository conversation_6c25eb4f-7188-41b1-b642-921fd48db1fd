<pacto-cat-tabs-vertical>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-label-acesso-semanal"
		label="Acessos semanais"
		pactoTabVertical="acessos-semanais">
		<pacto-cat-form-select
			[control]="form.get('quantidadeMaximaFrequencia')"
			[items]="frequencias"
			i18n-label="@@plano:label-acesso-semanal"
			id="select-qtd-max-frequencia"
			idKey="value"
			label="Acesso semanal"></pacto-cat-form-select>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-label-convites"
		label="Convites"
		pactoTabVertical="convites">
		<pacto-cat-form-input-number
			[formControl]="form.get('convidadosPorMes')"
			i18n-label="@@adm:label-convidados-por-mes"
			id="input-convidados-mes"
			label="Quantidade de convite por mês"></pacto-cat-form-input-number>
	</ng-template>
	<ng-template
		*ngIf="
			!form.get('apresentarVendaRapida').value &&
			plano.tipoPlano !== 'PLANO_PERSONAL'
		"
		i18n-label="@@adm:tab-label-vendas-online"
		label="Vendas online"
		pactoTabVertical="vendas-online">
		<pacto-cat-checkbox
			[control]="form.get('site')"
			i18n-label="@@adm:label-vendas-online"
			id="check-vendas-online"
			label="Participa do Vendas Online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-compartilhar-plano-site"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirCompartilharPLanoNoSite')"
			label="Permite compartilhar plano no vendas online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('site').value"
			id="check-permite-venda-plano-site-balcao"
			i18n-label="@@adm:label-vendas-online"
			[control]="form.get('permitirVendaPlanoSiteNoBalcao')"
			label="Permite venda do plano no balcão"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="
				form.get('site').value &&
				existeModalidadeTurmaSelecionada() &&
				getPlanosPermitidos()
			"
			[control]="form.get('permitirTurmasVendasOnline')"
			i18n-label="@@adm:label-permitir-turmas-vendas-online"
			id="check-permitir-turma-vendas-online"
			label="Permitir turmas no Vendas Online"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			[control]="form.get('apresentarPactoFlow')"
			id="check-pacto-flow"
			label="Apresentar no Pacto Flow"></pacto-cat-checkbox>
		<pacto-cat-form-input-number
			id="diasBloquearCompraMesmoPlano"
			*ngIf="form.get('site').value"
			label="Intervalo de recompra do plano"
			placeholder="0"
			[max]="'9999'"
			[formControl]="
				form.get('diasBloquearCompraMesmoPlano')
			"></pacto-cat-form-input-number>
		<pacto-cat-form-datepicker
			[control]="form.get('inicioMinimoContrato')"
			[dateFilter]="dateFilter"
			errorMsg="Informe uma data não retroativa!"
			i18n-errorMsg="@@adm:error-inicio-minimo-contrato"
			i18n-label="@@adm:label-inicio-minimo-contrato"
			id="datepicker-inicio-minimo-contrato"
			label="Contratos iniciam a partir de"></pacto-cat-form-datepicker>
		<pacto-cat-form-select-filter
			*ngIf="form.get('site').value"
			[addtionalFilters]="modeloContratoAddtionalFilters"
			[control]="form.get('termoAceite')"
			[endpointUrl]="admRestService.buildFullUrlPlano('modelosContrato')"
			[idKey]="'codigo'"
			[labelKey]="'descricao'"
			[paramBuilder]="modeloContratoSelectBuilder"
			errorMsg="Selecione um termo de aceite"
			i18n-errorMsg="@@adm:label-termo-aceite"
			i18n-label="@@adm:label-termo-aceite"
			id="select-termo-aceite"
			label="Termo de aceite"></pacto-cat-form-select-filter>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12">
				<div class="input-nome">Vídeo youtube</div>
				<input
					[formControl]="form.get('videoSiteUrl')"
					class="input-text"
					placeholder="Informe aqui o código do vídeo no youtube Ex: 6AidSn00VGo"
					type="text" />
			</div>
		</div>

		<label *ngIf="form.get('site').value" class="input-nome">Observação</label>
		<div *ngIf="form.get('site').value" class="row">
			<div class="col-md-12 div-text-area-modelo-contrato">
				<quill-editor
					[formControl]="form.get('observacaoSite')"
					[id]="'editor-texto-observacao'"
					[modules]="modules"></quill-editor>
			</div>
		</div>
	</ng-template>
	<ng-template
		*ngIf="!form.get('site').value"
		i18n-label="@@adm:tab-label-venda-rapida"
		label="Venda rápida"
		pactoTabVertical="venda-rapida">
		<pacto-cat-checkbox
			[control]="form.get('apresentarVendaRapida')"
			i18n-label="@@adm:label-apresentar-venda-rapida"
			id="check-apresentar-venda-rapida"
			label="Apresentar na venda rápida"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		*ngIf="plano.tipoPlano !== 'PLANO_PERSONAL'"
		i18n-label="@@adm:tab-label-totem"
		label="Totem"
		pactoTabVertical="totem">
		<pacto-cat-checkbox
			[control]="form.get('totem')"
			i18n-label="@@adm:label-totem"
			id="check-totem"
			label="Totem"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('totem').value"
			id="check-permite-venda-plano-totem-balcao"
			i18n-label="@@adm:label-permite-venda-plano-totem-balcao"
			[control]="form.get('permitirVendaPlanoTotemNoBalcao')"
			label="Permite venda do plano no balcão"></pacto-cat-checkbox>
		<pacto-cat-checkbox
			*ngIf="form.get('totem').value"
			[control]="form.get('renovarComDescontoTotem')"
			i18n-label="@@adm:label-renovar-com-desconto-totem"
			id="check-renovar-com-desconto-totem"
			label="Permitir que o plano seja renovado pelo totem com desconto"></pacto-cat-checkbox>
		<pacto-cat-form-input
			*ngIf="form.get('totem').value"
			[control]="form.get('descricaoEncantamento')"
			i18n-label="@@adm:label-descricao-encantamento"
			id="input-descricao-encantamento"
			label="Descrição de encantamento"></pacto-cat-form-input>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-comissao"
		label="Comissão"
		pactoTabVertical="comissao">
		<pacto-cat-checkbox
			[control]="form.get('comissao')"
			i18n-label="@adm:label-gerar-comissao"
			id="check-comissao"
			label="Gerar comissão"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-restricao-recompra"
		label="Restrição de venda"
		pactoTabVertical="restricao-recompra">
		<pacto-cat-checkbox
			[control]="form.get('bloquearRecompra')"
			i18n-label="@@adm:label-bloquear-recompra"
			id="check-bloquear-recompra"
			label="Venda apenas para visitante"></pacto-cat-checkbox>

		<pacto-cat-form-datepicker
			*ngIf="
				plano.tipoPlano !== 'PLANO_PERSONAL' &&
				plano.tipoPlano !== 'PLANO_CREDITO' &&
				plano.tipoPlano !== 'PLANO_AVANCADO'
			"
			[control]="form.get('contratosEncerramDia')"
			i18n-label="@@adm:label--encerram-dia"
			id="datepicker-encerram-dia"
			label="Contratos encerram no dia"></pacto-cat-form-datepicker>
	</ng-template>
	<ng-template
		*ngIf="
			configuracaoSistema &&
			configuracaoSistema.usaPlanoRecorrenteCompartilhado &&
			plano.tipoPlano !== 'PLANO_PERSONAL'
		"
		label="Compartilhamento de Plano"
		pactoTabVertical="compartilhamento-plano">
		<pacto-cat-form-input-number
			[formControl]="form.get('quantidadeCompartilhamentos')"
			i18n-label="@@adm:label-quantidade-compartilhamentos-plano"
			id="input-qtd-compartilhamento"
			label="Quantidade de compartilhamentos do plano"></pacto-cat-form-input-number>

		<pacto-cat-checkbox
			*ngIf="
				form.get('quantidadeCompartilhamentos').value &&
				form.get('quantidadeCompartilhamentos').value > 0 &&
				fogueteHabilitada
			"
			[control]="form.get('enviarDependenteFoguete')"
			i18n-label="@@adm:label-dependente-foguete"
			id="check-enviar-dependente-foguete"
			label="Enviar dependentes para integração Foguete"></pacto-cat-checkbox>
	</ng-template>
	<ng-template
		i18n-label="@@adm:tab-label-observacoes"
		label="Observações"
		pactoTabVertical="obsercacoes">
		<div class="input-nome">Observação 1</div>
		<input
			[formControl]="form.get('observacao1')"
			class="input-text"
			type="text" />
		<div class="input-nome">Observação 2</div>
		<input
			[formControl]="form.get('observacao2')"
			class="input-text"
			type="text" />
	</ng-template>
</pacto-cat-tabs-vertical>

<div class="action-container btn-row-adm">
	<pacto-cat-button
		(click)="salvar()"
		[type]="buttonType.PRIMARY"
		i18n-label="@@adm:btn-salvar-config"
		id="btn-save-config"
		label="Salvar Configurações"></pacto-cat-button>
</div>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@plano:error-data-retroativa-obrigatoria"
		xingling="error-data-retroativa-obrigatoria">
		Informe uma data não retroativa!
	</span>
	<span
		i18n="@@plano:error-location-vendas-online"
		xingling="error-location-vendas-online">
		Vendas online
	</span>
	<span i18n="@@adm:por-semana" xingling="por-semana">por semana</span>
</pacto-traducoes-xingling>
