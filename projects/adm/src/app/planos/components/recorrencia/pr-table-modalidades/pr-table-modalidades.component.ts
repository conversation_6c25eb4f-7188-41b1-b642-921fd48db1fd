import {
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { Plano } from "../../../plano.model";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { FormControl, FormGroup } from "@angular/forms";
import { CadastrarModalidadePlanoComponent } from "../../cadastrar-modalidade-plano/cadastrar-modalidade-plano.component";
import { CadastrarModalidadeRepeticaoComponent } from "../../cadastrar-modalidade-repeticao/cadastrar-modalidade-repeticao.component";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "projects/adm/src/app/adm-rest.service";

@Component({
	selector: "adm-pr-table-modalidades",
	templateUrl: "./pr-table-modalidades.component.html",
	styleUrls: ["./pr-table-modalidades.component.scss"],
})
export class PrTableModalidadesComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnModalidade", { static: true })
	columnModalidade: TemplateRef<any>;
	@ViewChild("columnVezesSemana", { static: true })
	columnVezesSemana: TemplateRef<any>;
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();

	table: PactoDataGridConfig;
	modalidades: Array<any> = new Array<any>();
	plano: Plano = new Plano();

	constructor(
		private planoStateService: PlanoStateService,
		private planoCommonService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private admRestService: AdmRestService
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.plano.modalidadesAux) {
			this.modalidades = this.plano.modalidadesAux;
		}
		this.initTable();
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.modalidades,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				modalidade: new FormControl(),
				vezesSemana: new FormControl(""),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "modalidade",
					titulo: this.columnModalidade,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: true,
					selectParamBuilder: (term) => {
						return {
							filters: JSON.stringify({
								quicksearchValue: term,
								quicksearchFields: ["descricao"],
							}),
						};
					},
					showAddSelectBtn: false,
					inputType: "select",
					dialogComponent: CadastrarModalidadePlanoComponent,
					idSelectKey: "codigo",
					objectAttrLabelName: "nome",
					labelSelectKey: "nome",
					width: "193px",
					endpointUrl:
						this.admRestService.buildFullUrlPlano("modalidades") +
						"/only-cod-name",
					addEmptyOption: true,
				},
				{
					nome: "vezesSemana",
					titulo: this.columnVezesSemana,
					visible: true,
					ordenavel: false,
					editable: true,
					dialogComponent: CadastrarModalidadeRepeticaoComponent,
					inputType: "number",
					width: "193px",
					valueTransform: (v) => (v ? v + "x" : ""),
					showAddSelectBtn: false,
					showSelectFilter: false,
				},
			],
		});
	}

	confirm(event) {
		if (
			event &&
			this.modalidades &&
			event.row &&
			event.row.codigo &&
			event.row.codigo !== "" &&
			event.row.codigo !== undefined &&
			event.row.codigo !== null
		) {
			this.modalidades.forEach((m) => {
				if (m.codigo === event.row.codigo) {
					m.edit = false;
				}
			});
		}
		this.plano.modalidadesAux = this.modalidades;
		this.planoStateService.updateState(this.plano);
	}

	delete(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.modalidades) {
			let index;
			if (event.row.codigo) {
				const finded = this.modalidades.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (finded && index !== undefined) {
					this.modalidades.splice(index, 1);
				}
			} else {
				this.modalidades.splice(event.index, 1);
			}
			this.plano.modalidadesAux = this.modalidades;
		}
		this.planoStateService.updateState(this.plano);
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this.plano = this.planoStateService.plano;
		let result = !this.planoCommonService.verifyIfNewExists(
			row,
			data,
			rowIndex
		);
		if (result) {
			result = this.validatePlanoRecorrencia(row, form);
		} else {
			this.notificationService.error(
				this.traducao.getLabel("dado-duplicado-table")
			);
		}
		return result;
	}

	validatePlanoRecorrencia(row, form) {
		let result;
		const modalidade = form.get("modalidade").value;
		const vezesSemana = form.get("vezesSemana").value;

		result =
			modalidade !== undefined &&
			modalidade !== null &&
			modalidade.nome !== "-" &&
			modalidade.nome !== "" &&
			vezesSemana !== undefined &&
			vezesSemana !== null &&
			vezesSemana !== "" &&
			vezesSemana !== 0;

		return result;
	}
}
