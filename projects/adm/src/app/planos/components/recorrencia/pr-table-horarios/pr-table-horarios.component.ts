import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig, TraducoesXinglingComponent } from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { Plano } from "../../../plano.model";
import { DecimalPipe } from "@angular/common";
import { AdmRestService } from "../../../../adm-rest.service";

@Component({
	selector: "adm-pr-table-horarios",
	templateUrl: "./pr-table-horarios.component.html",
	styleUrls: ["./pr-table-horarios.component.scss"],
})
export class PrTableHorariosComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnHorario", { static: true }) columnHorario: TemplateRef<any>;
	@ViewChild("columnTipoOperacao", { static: true })
	columnTipoOperacao: TemplateRef<any>;
	@ViewChild("columnTipoValor", { static: true })
	columnTipoValor: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	plano: Plano = new Plano();
	table: PactoDataGridConfig;
	horarios: Array<any> = new Array<any>();
	@Output() isEditinOrAdding: EventEmitter<any> = new EventEmitter<any>();
	tiposOperacao: Array<{ label: string; codigo: string }> = new Array<{
		label: string;
		codigo: string;
	}>();
	tiposValor: Array<{ label: string; codigo: string }> = new Array<{
		label: string;
		codigo: string;
	}>();

	constructor(
		private admRestService: AdmRestService,
		private planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.plano = this.planoStateService.updatePlanoObj();
		this.setTiposOperacao();
		this.setTiposValor();
		setTimeout(() => {
			if (this.plano.horarios) {
				this.convertHorarios();
			}
			this.initTable();
			this.cd.detectChanges();
		});
	}

	initTable() {
		this.table = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.horarios,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				horario: new FormControl(),
				tipoOperacao: new FormControl(""),
				tipoValor: new FormControl(""),
				valor: new FormControl(""),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "codigo",
					titulo: "#",
					visible: true,
					ordenavel: false,
					width: "50px",
				},
				{
					nome: "horario",
					titulo: this.columnHorario,
					visible: true,
					ordenavel: false,
					editable: true,
					showSelectFilter: false,
					showAddSelectBtn: false,
					inputType: "select",
					idSelectKey: "codigo",
					objectAttrLabelName: "descricao",
					labelSelectKey: "descricao",
					width: "150px",
					endpointUrl:
						this.admRestService.buildFullUrlPlano("horarios") + "/only-ativos",
					infiniteScrollEnabled: true,
				},
				{
					nome: "tipoOperacao",
					titulo: this.columnTipoOperacao,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					objectAttrLabelName: "label",
					showAddSelectBtn: false,
					showSelectFilter: false,
					inputSelectData: this.tiposOperacao,
					addEmptyOption: true,
					width: "70px",
				},
				{
					nome: "tipoValor",
					titulo: this.columnTipoValor,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					objectAttrLabelName: "label",
					showAddSelectBtn: false,
					showSelectFilter: false,
					inputSelectData: this.tiposValor,
					addEmptyOption: true,
					selectOptionChange: (option, form, row) => {
						if (option && option.codigo !== null) {
							form.get("valor").enable();
						} else {
							form.get("valor").setValue(0);
							form.get("valor").disable();
						}
					},
					width: "70px",
				},
				{
					nome: "valor",
					titulo: this.columnValor,
					visible: true,
					ordenavel: false,
					editable: true,
					decimal: true,
					inputType: "decimal",
					showAddSelectBtn: false,
					showSelectFilter: false,
					width: "100px",
					isDisabled: (v) => {
						return (
							v.tipoValor === "" ||
							v.tipoValor === undefined ||
							v.tipoValor === null
						);
					},
					valueTransform: (v) => {
						return this.decimalPipe.transform(v, "1.2-2");
					},
				},
			],
		});
	}

	async setTiposOperacao() {
		this.tiposOperacao = await this.planoCommonsService.initTiposOperacoes(
			this.traducao
		);
		this.cd.detectChanges();
	}

	async setTiposValor() {
		this.tiposValor = await this.planoCommonsService.initTiposValor(
			this.traducao
		);
		this.cd.detectChanges();
	}

	confirm(event) {
		Object.keys(event.form.controls).forEach(
			(key) => (event.row[key] = event.form.get(key).value)
		);
		this.horarios.forEach((planoModalidade) => {
			if (planoModalidade.vezesSemana) {
				planoModalidade.vezesSemana = planoModalidade.vezesSemana.codigo;
			}
		});
		this.plano.horarios = this.horarios;
		this.planoStateService.updateState(this.plano);
	}

	delete(event) {
		this.plano = this.planoStateService.updatePlanoObj();
		if (this.horarios) {
			let index;
			if (event.row.codigo) {
				const finded = this.horarios.find((ex, i) => {
					if (ex.codigo === event.row.codigo) {
						index = i;
						return ex;
					}
				});
				if (finded && index !== undefined) {
					this.horarios.splice(index, 1);
				}
			} else {
				this.horarios.splice(event.index, 1);
			}
			this.plano.horarios = this.horarios;
		}
		this.planoStateService.updateState(this.plano);
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		this.plano = this.planoStateService.plano;
		const horario = form.get("horario").value;
		const tipoValor = form.get("tipoValor").value;
		const tipoOperacao = form.get("tipoOperacao").value;
		const exists = this.planoCommonsService.verifyIfNewExists(
			row,
			data,
			rowIndex
		);
		if (!exists) {
			if (horario === null || horario === undefined) {
				this.notificationService.error(
					this.traducao.getLabel("horario-ja-incluso")
				);
				return false;
			}

			if (
				data.find(
					(d, index) =>
						index !== rowIndex && d.horario.codigo === horario.codigo
				)
			) {
				this.notificationService.error("Este horário já está incluído!");
				this.notificationService.error(this.traducao.getLabel("horario-vazio"));
				return false;
			}

			if (tipoOperacao && tipoOperacao.codigo !== null) {
				if (
					tipoValor === null ||
					tipoValor === undefined ||
					tipoValor.codigo === null
				) {
					this.notificationService.error(
						this.traducao.getLabel("tipo-valor-vazio")
					);
					return false;
				}
			}

			if (tipoValor && tipoValor.codigo !== null) {
				const valor = form.get("valor").value;
				if (tipoValor.codigo === "PD" && valor > 100) {
					form.get("valor").setValue(100);
				}
				if (
					valor === null ||
					valor === undefined ||
					valor === "" ||
					form.get("valor").value === 0
				) {
					this.notificationService.error(this.traducao.getLabel("valor-vazio"));
					return false;
				}
				if (
					tipoOperacao === null ||
					tipoOperacao === undefined ||
					tipoOperacao === ""
				) {
					this.notificationService.error(
						this.traducao.getLabel("tipo-operacao-vazio")
					);
					return false;
				}
			}
		} else {
			this.notificationService.error(
				this.traducao.getLabel("dado-duplicado-table")
			);
		}
		return !exists;
	}

	convertHorarios() {
		if (this.plano.horarios) {
			this.plano.horarios.forEach((horario) => {
				const h = {
					codigo: horario.codigo,
					horario: horario.horario,
					tipoOperacao: horario.tipoOperacao
						? this.tiposOperacao.find(
								(tp) =>
									tp.codigo === horario.tipoOperacao.codigo ||
									tp.codigo === horario.tipoOperacao
						  )
						: null,
					tipoValor: horario.tipoValor
						? this.tiposValor.find(
								(tv) =>
									tv.codigo === horario.tipoValor.codigo ||
									tv.codigo === horario.tipoValor
						  )
						: null,
					valor:
						horario.tipoValor &&
						(horario.tipoValor.codigo === "VE" || horario.tipoValor === "VE")
							? horario.valorEspecifico
							: horario.percentualDesconto,
				};
				this.horarios.push(h);
			});
		}
	}
}
