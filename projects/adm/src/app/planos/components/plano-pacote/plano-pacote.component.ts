import { DecimalPipe } from "@angular/common";
import {
	Component,
	Input,
	OnInit,
	ViewChild,
	ChangeDetectorRef,
	AfterViewInit,
	EventEmitter,
	Output,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Pacote } from "plano-api";
import { ClientDiscoveryService } from "sdk";
import {
	PactoDataGridConfig,
	CatTableEditableComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Observable, of } from "rxjs";
import { SnotifyService } from "ng-snotify";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";
import { Plano, PlanoPacote } from "../../plano.model";
import { PlanoModalidadeComponent } from "../plano-modalidade/plano-modalidade.component";

@Component({
	selector: "adm-plano-pacote",
	templateUrl: "./plano-pacote.component.html",
	styleUrls: ["./plano-pacote.component.scss"],
})
export class PlanoPacoteComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("table", { static: false }) table: CatTableEditableComponent;
	@Input() planoDTO: Plano;
	@Input() codigo: number;
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@Output() afterAdd: EventEmitter<any> = new EventEmitter<any>();

	pacotesSelecionados: Pacote[] = [];
	gridConfig: PactoDataGridConfig;

	pacoteFormGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		descricao: new FormControl(),
		precoComposicao: new FormControl(),
	});

	constructor(
		private discoveryService: ClientDiscoveryService,
		private planoState: PlanoStateService,
		private decimalPipe: DecimalPipe,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.initGridConfig();
	}

	ngAfterViewInit(): void {
		this.initState();
		if (this.planoDTO.pacotes) {
			this.pacotesSelecionados = this.planoDTO.pacotes.map(
				(pacote) => pacote.pacote
			);
			this.gridConfig.dataAdapterFn = (serverData) => {
				serverData.content = this.planoDTO.pacotes.map(
					(pacote) => pacote.pacote
				);
				return serverData;
			};
		}
	}

	initState() {
		this.planoDTO = this.planoState.plano;
	}

	initGridConfig() {
		const planoParam = this.planoDTO.codigo
			? `?plano=${this.planoDTO.codigo}`
			: "";

		this.gridConfig = new PactoDataGridConfig({
			pagination: false,
			endpointUrl: `${
				this.discoveryService.getUrlMap().planoMsUrl
			}/planos/pacotes${planoParam}`,
			endpointParamsType: "query",
			formGroup: this.pacoteFormGroup,
			onAddFn: (row, data, indexRow) => {
				if (data[indexRow].descricao === null) {
					data.splice(indexRow, 1);
					this.notificationService.error("Adicione um pacote válido");
					return of(false);
				}
				this.adicionarPacote(data, indexRow);
				return data;
			},
			onEditFn: (row, data, index) => {
				this.editarPacote(data, row);
				return data;
			},
			dataAdapterFn(serverData) {
				this.pacotesSelecionados = serverData.content;
				return serverData.content;
			},
			showDelete: (row, isAdd) => this.showDelete(row, isAdd),
			onDeleteFn: (row, data, indexRow) => this.deletar(row, data, indexRow),
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: false,
					ordenavel: false,
					editable: false,
				},
				{
					nome: "descricao",
					titulo: "Pacote",
					endpointUrl:
						this.discoveryService.getUrlMap().planoMsUrl + "/pacotes",
					visible: true,
					ordenavel: true,
					editable: true,
					inputType: "select",
					idSelectKey: "codigo",
					labelSelectKey: "descricao",
					width: "50%",
					showEmptyMessage: true,
					selectOptionChange: (option: Pacote, form, row) => {
						form.controls["precoComposicao"].setValue(option.precoComposicao);
					},
					labelFn: (row) => {
						if (row.descricao) {
							return `${row.descricao} - R$ ${row.precoComposicao}`;
						}
						return row;
					},
					selectParamBuilder: (param) => {
						return {
							page: "0",
							size: "10",
							filters: JSON.stringify({
								quicksearchValue: param,
								codigosNaoConsultar: this.pacotesSelecionados.map(
									(pacote) => pacote.codigo
								),
							}),
						};
					},
					showSelectFilter: true,
					showAddSelectBtn: false,
				},
				{
					nome: "precoComposicao",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					editable: false,
					decimal: true,
					valueTransform: (v) => this.converteDecimal(v),
					inputType: "number",
					width: "30%",
				},
			],
		});
		this.cd.detectChanges();
	}

	private editarPacote(data: any, row: any) {
		this.planoDTO = this.planoState.plano;
		this.pacotesSelecionados = data.map((p) => {
			if (typeof p.descricao === "string") return p;
			else return p.descricao;
		});
		this.planoDTO.pacotes = this.pacotesSelecionados.map(
			(p) => new PlanoPacote(null, p)
		);
		this.planoState.updateState(this.planoDTO);
	}

	private adicionarPacote(data: any, indexRow: any) {
		this.planoDTO = this.planoState.plano;
		if (!this.planoDTO.pacotes) {
			this.planoDTO.pacotes = [];
		}
		const pacote = data[indexRow].descricao;
		this.pacotesSelecionados.push(pacote);
		this.planoDTO.pacotes.push(new PlanoPacote(null, pacote));

		if (pacote.modalidades) {
			if (!this.planoDTO.modalidades) {
				this.planoDTO.modalidades = [];
			}
			for (const modalidade of pacote.modalidades) {
				this.planoDTO.modalidades.push({
					modalidade: modalidade.modalidade,
					listaVezesSemana: `${modalidade.nrVezes}`,
					vezesSemana: [],
				});
			}
		}
		this.afterAdd.emit({ planoDTO: this.planoDTO });
		this.planoState.plano = this.planoDTO;
	}

	showDelete(row: any, isAdd: any): boolean {
		if (isAdd) return false;
		return true;
	}

	deletar(row: any, data: any, indexRow: any): Observable<boolean> {
		this.planoDTO = this.planoState.plano;
		const codigo =
			typeof row.descricao === "object" ? row.descricao.codigo : row.codigo;
		const index = this.planoDTO.pacotes.findIndex(
			(p) => p.pacote.codigo === codigo
		);

		if (index == -1) return of(false);
		this.planoDTO.pacotes.splice(index, 1);
		this.planoState.plano = this.planoDTO;
		this.pacotesSelecionados = this.planoDTO.pacotes.map(
			(pacote) => pacote.pacote
		);

		data.splice(indexRow, 1);
		return data;
	}

	converteDecimal(value: any): string {
		let valueToString = value ? value.toString().replace(",", ".") : "";
		return this.decimalPipe.transform(parseFloat(valueToString), "1.2-2") || "";
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
	}
}
