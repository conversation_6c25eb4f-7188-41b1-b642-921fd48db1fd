import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { UiModule } from "ui-kit";
import { LayoutModule } from "../layout/layout.module";
import { FreepassRoutingModule } from "./freepass-routing.module";
import { FreepassComponent } from "./freepass.component";
import { FreepassModalLogComponent } from "./components/freepass-modal-log/freepass-modal-log.component";

@NgModule({
	declarations: [FreepassComponent, FreepassModalLogComponent],
	imports: [
		CommonModule,
		FreepassRoutingModule,
		BaseSharedModule,
		LayoutModule,
		UiModule,
	],
	entryComponents: [FreepassModalLogComponent],
})
export class FreepassModule {}
