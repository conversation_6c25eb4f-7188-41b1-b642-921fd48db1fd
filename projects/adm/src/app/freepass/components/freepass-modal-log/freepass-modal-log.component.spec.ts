import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { FreepassModalLogComponent } from "./freepass-modal-log.component";

describe("FreepassModalLogComponent", () => {
	let component: FreepassModalLogComponent;
	let fixture: ComponentFixture<FreepassModalLogComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [FreepassModalLogComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(FreepassModalLogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
