import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Api } from "sdk";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	TabelaLogComponent,
} from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { switchMap } from "rxjs/operators";
import { AdmCoreApiFreePassService } from "adm-core-api";

@Component({
	selector: "adm-freepass-modal-log",
	templateUrl: "./freepass-modal-log.component.html",
	styleUrls: ["./freepass-modal-log.component.scss"],
})
export class FreepassModalLogComponent implements OnInit, AfterViewInit {
	@ViewChild("tableFreepassModalLog", { static: false })
	tableFreepassModalLogComponent: RelatorioComponent;
	tableFreepassModalLog: TabelaLogComponent;

	url;

	constructor(private cd: ChangeDetectorRef, private admRest: AdmRestService) {}

	ngOnInit() {
		this.carregarDados();
	}

	ngAfterViewInit() {}

	carregarDados() {
		this.url = this.admRest.buildFullUrl("freepass/logs", false, Api.MSADMCORE);
		if (this.tableFreepassModalLog && this.tableFreepassModalLog.tableData) {
			this.tableFreepassModalLog.table.endpointUrl = this.url;
			this.tableFreepassModalLog.tableData.reloadData();
		}
		this.cd.detectChanges();
	}
}
