<pacto-cat-layout-v2>
	<ds3-breadcrumbs
		[routeData]="route"
		[displayArrowSection]="false"></ds3-breadcrumbs>

	<pacto-cat-card-plain class="freepass-form-card">
		<form class="freepass">
			<div class="row">
				<div class="col-3">
					<ds3-form-field>
						<ds3-field-label>Data de início*</ds3-field-label>
						<ds3-input-date
							id="freepass-data-inicio"
							dateType="datepicker"
							[control]="form.get('dataInicio')"
							ds3Input></ds3-input-date>
					</ds3-form-field>
					<ds3-form-field>
						<ds3-field-label>Cliente*</ds3-field-label>
						<ds3-select
							id="select-codigo-cliente"
							[valueKey]="'codCliente'"
							[nameKey]="'nome'"
							[paramBuilder]="selectBuilder"
							[endpointUrl]="
								_rest.buildFullUrlAdmCore('venda-avulsa/compradores')
							"
							[formControl]="form.get('codigoCliente')"
							ds3Input></ds3-select>
					</ds3-form-field>
					<ds3-form-field>
						<ds3-field-label>Produto*</ds3-field-label>
						<ds3-select
							id="select-codigo-produto"
							[options]="produtos"
							[valueKey]="'codigo'"
							[nameKey]="'descricao'"
							[formControl]="form.get('produto')"
							ds3Input></ds3-select>
					</ds3-form-field>
					<ds3-form-field *ngIf="isNrDiasEnabled">
						<ds3-field-label>Quantidade de dias</ds3-field-label>
						<input
							id="freepass-qtd-dias"
							type="text"
							[formControl]="form.get('nrDias')"
							ds3Input />
						<div ds3Suffix>dias</div>
					</ds3-form-field>
				</div>
			</div>

			<div class="freepass-message cliente-ativo" *ngIf="clienteAtivo">
				<p>
					<i class="pct pct-info"></i>
					Não é possível lançar FreePass para cliente com contrato vigente.
				</p>
			</div>

			<div class="freepass-message" *ngIf="freepassMessage">
				<p>
					<i class="pct pct-info"></i>
					{{ freepassMessage }}
				</p>

				<div class="message-actions">
					<button
						id="btn-freepass-cancelar-novo"
						(click)="limparCampos()"
						ds3-text-button>
						Cancelar este novo
					</button>
					<button
						id="btn-freepass-excluir-antigo"
						(click)="excluirFreepassAntigo()"
						ds3-text-button
						color="secondary">
						Excluir o antigo
					</button>
				</div>
			</div>
			<div class="freepass-footer">
				<button
					id="btn-freepass-logs"
					ds3-outlined-button
					(click)="abrirLogs()">
					<i class="pct pct-list"></i>
				</button>
				<button
					id="btn-freepass-cancelar"
					ds3-outlined-button
					[disabled]="!isFormValid || freepassMessage || clienteAtivo"
					(click)="limparCampos()">
					Cancelar
				</button>
				<button
					id="btn-freepass-confirmar"
					ds3-flat-button
					[disabled]="!isFormValid || freepassMessage || clienteAtivo"
					(click)="confirmar()">
					Confirmar
				</button>
			</div>
		</form>
	</pacto-cat-card-plain>
</pacto-cat-layout-v2>
