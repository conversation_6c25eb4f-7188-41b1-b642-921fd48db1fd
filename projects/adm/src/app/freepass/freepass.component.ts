import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { Router } from "@angular/router";
import {
	AdmCoreApiClienteService,
	AdmCoreApiFreePassService,
	Produto,
} from "adm-core-api";
import moment from "moment";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "sdk";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	LoaderService,
} from "ui-kit";
import { AdmRestService } from "../adm-rest.service";
import { FreepassModalLogComponent } from "./components/freepass-modal-log/freepass-modal-log.component";

@Component({
	selector: "adm-freepass",
	templateUrl: "./freepass.component.html",
	styleUrls: ["./freepass.component.scss"],
})
export class FreepassComponent implements OnInit {
	public form: FormGroup;
	public produtos: Produto[] = [];
	public clientes: any[] = [];
	public isNrDiasEnabled = false;
	public freepassMessage = "";
	public clienteAtivo = "";
	public botao: "confirmar" | "deletar" = "confirmar";
	public clienteOptions = [];
	public isFormValid = false;
	route = [
		{ path: ["/adm"], title: "Administrativo", isDisabled: true },
		{ path: ["/adm"], title: "Operações", isDisabled: true },
		{ path: ["/adm/freepass"], title: "Freepass" },
	];
	infoArray = [];

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			quickSearch: term,
		};
	};

	constructor(
		private readonly snotifyService: SnotifyService,
		private readonly router: Router,
		private readonly fb: FormBuilder,
		private clienteService: AdmCoreApiClienteService,
		private readonly admCoreApiFreePass: AdmCoreApiFreePassService,
		private readonly cd: ChangeDetectorRef,
		private readonly sessionService: SessionService,
		private dialogService: DialogService,
		private admRestService: AdmRestService,
		private loaderService: LoaderService
	) {}

	public ngOnInit(): void {
		this.form = this.fb.group({
			codigoCliente: new FormControl("", Validators.required),
			produto: new FormControl(null, Validators.required),
			dataInicio: new FormControl(new Date().getTime(), Validators.required),
			nrDias: new FormControl("", [
				Validators.required,
				Validators.minLength(1),
			]),
		});
		this.form.get("nrDias").disable();
		this.preencherCamposPadrao();
		this.admCoreApiFreePass.obterProdutosAtivos().subscribe(async (res) => {
			if (res.content) {
				this.produtos = res.content;
			}
		});

		this.form.valueChanges.subscribe((value) => {
			this.isFormValid = this.form.valid;
		});

		this.form.get("codigoCliente").valueChanges.subscribe((value) => {
			this.limparMensagem();

			if (!value) {
				return;
			}

			this.admCoreApiFreePass.verificarCadastro(value).subscribe(
				(res) => {
					if (res.content) {
						this.botao = "deletar";
						const inicio = moment(res.content.dataInicioAcesso).format(
							"DD/MM/yyyy"
						);
						const fim = moment(res.content.dataFinalAcesso).format(
							"DD/MM/yyyy"
						);
						this.freepassMessage = `Existe um Free Pass para este aluno para o período de ${inicio} até ${fim}. É necessário excluir esse Free Pass para lançar outro.`;

						this.form.get("produto").clearValidators();
						this.form.get("dataInicio").clearValidators();
						this.form.get("nrDias").clearValidators();
					} else {
						this.botao = "confirmar";
						this.form.get("produto").setValidators(Validators.required);
						this.form.get("dataInicio").setValidators(Validators.required);
						this.form
							.get("nrDias")
							.setValidators([Validators.minLength(1), Validators.required]);
					}
					this.form.get("produto").updateValueAndValidity();
					this.form.get("dataInicio").updateValueAndValidity();
					this.form.get("nrDias").updateValueAndValidity();
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message === "cliente_ativo_freepass") {
						this.clienteAtivo = err.meta.message;
						this.cd.detectChanges();
					}
				}
			);
		});

		this.form.get("produto").valueChanges.subscribe((value) => {
			if (value) {
				const produto = this.produtos.find(
					(produto) => produto.codigo === Number(value)
				);
				if (produto) {
					this.form.get("nrDias").setValue(produto.nrDiasVigencia);
				}
				this.updateNrDiasVisibilityState();
			}
		});

		this.optionsConfig();
	}

	public excluirFreepassAntigo() {
		const body = {
			cliente: this.form.get("codigoCliente").value,
		};
		this.admCoreApiFreePass.excluir(body).subscribe(
			(res) => {
				if (res.content) {
					this.snotifyService.success("Freepass excluído com sucesso!");
					const cliente = this.form.get("codigoCliente").value;
					const dataInicio = this.form.get("dataInicio").value;
					this.limparCampos();
					this.form.get("codigoCliente").setValue(cliente);
					this.form.get("dataInicio").setValue(dataInicio);
					this.cd.detectChanges();
				}
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.snotifyService.error(err.meta.messageValue);
				} else {
					this.snotifyService.error(
						"Ocorreu um erro inesperado, tente novamente."
					);
				}
			}
		);
	}

	private optionsConfig() {
		this.clienteService
			.obterTodosClientes({ page: 0, size: 50 })
			.subscribe((result) => {
				this.clienteOptions = result.content.map((cliente) => {
					return {
						value: cliente.codigoCliente.toString(),
						name: cliente.nome,
					};
				});
				this.cd.detectChanges();
			});
	}

	private updateNrDiasVisibilityState() {
		this.isNrDiasEnabled = !this.isNrDiasEnabled;
	}

	public confirmar(): void {
		if (
			!this.sessionService.funcionalidadesPermitidas.includes(
				"permissaofreepass"
			)
		) {
			this.snotifyService.error(
				'Este usuário não possui permissão para esta operação, você precisa da permissão "2.51 - PERMISSÃO PARA LANÇAR FREE PASS"'
			);
			return;
		}

		this.form.markAllAsTouched();
		if (this.form.invalid) {
			this.snotifyService.error("Preencha todos os campos validamente");
			return;
		}

		const form = this.form.value;
		const body = {
			cliente: form.codigoCliente,
			produto: form.produto,
			dataInicio: moment(form.dataInicio).format("yyyy-MM-DD"),
			nrDias: Number(form.nrDias),
		};
		this.loaderService.initForce();
		this.admCoreApiFreePass.salvar(body).subscribe(
			(res) => {
				if (res.content) {
					this.snotifyService.success("Freepass lançado com sucesso!");
					this.limparCampos();
					this.loaderService.stopForce();
				}
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.snotifyService.error(err.meta.messageValue);
				} else {
					this.snotifyService.error(
						"Ocorreu um erro inesperado, tente novamente."
					);
				}
				this.loaderService.stopForce();
			}
		);
	}

	public preencherCamposPadrao() {
		this.botao = "confirmar";
		this.form.get("codigoCliente").setValue("");
		this.form.get("nrDias").setValue("");
		this.form.get("dataInicio").setValue(new Date());
	}

	public limparCampos() {
		this.form.reset();
		this.preencherCamposPadrao();
		this.limparMensagem();
		this.cd.detectChanges();
	}

	public abrirLogs() {
		const modalRef = this.dialogService.open(
			"FreePass Logs",
			FreepassModalLogComponent,
			PactoModalSize.LARGE
		);
	}

	private limparMensagem() {
		this.freepassMessage = "";
		this.clienteAtivo = "";
	}

	public voltar(): void {
		this.router.navigate(["adm"]);
	}

	get _rest() {
		return this.admRestService;
	}
}
