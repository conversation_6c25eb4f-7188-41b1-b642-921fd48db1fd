import {
	AfterViewInit,
	Directive,
	ElementRef,
	HostListener,
	Inject,
	Input,
	OnInit,
	Renderer2,
	TemplateRef,
} from "@angular/core";
import { AdmCatTooltipComponent } from "./adm-cat-tooltip.component";
import { DOCUMENT } from "@angular/common";

@Directive({
	// tslint:disable-next-line:directive-selector
	selector: "[admPactoCatTooltipTrigger]",
})
export class CatTooltipTriggerDirective implements OnInit {
	@Input() admPactoCatTooltipTrigger: AdmCatTooltipComponent | undefined;
	@Input() position: "left" | "right" | "bottom" | "top" = "top";

	constructor(
		private element: ElementRef,
		private renderer: Renderer2,
		@Inject(DOCUMENT) private document: Document
	) {}

	//
	ngOnInit(): void {
		const div = this.document.createElement("div");
		div.appendChild(this.admPactoCatTooltipTrigger.elementRef.nativeElement);
		this.document.body.appendChild(div);
	}

	@HostListener("mouseenter", ["$event"])
	private onMouseEnter(event: MouseEvent) {
		const tooltipElement: Element =
			this.admPactoCatTooltipTrigger.elementRef.nativeElement.getElementsByClassName(
				"cat-tooltip-aux"
			)[0];
		const arrow: Element = tooltipElement.getElementsByClassName("arrow")[0];
		const target = event.target as Element;

		this.renderer.addClass(tooltipElement, "show");
		console.log(target.getBoundingClientRect());
		/*
		 * top = altura do content - 9
		 * right = largura do content / 2
		 * */
		switch (this.position) {
			case "top":
				this.renderer.addClass(arrow, "top");
				this.renderer.setStyle(
					arrow,
					"top",
					`${tooltipElement.getBoundingClientRect().height - 11}px`
				);
				this.renderer.setStyle(
					arrow,
					"right",
					`${tooltipElement.getBoundingClientRect().width / 2}px`
				);
				this.renderer.setStyle(
					tooltipElement,
					"top",
					`${
						target.getBoundingClientRect().top -
						tooltipElement.getBoundingClientRect().height -
						30
					}px`
				);
				const targetWidth = target.getBoundingClientRect().width;
				const tooltipWidth = tooltipElement.getBoundingClientRect().width;
				const multiply = tooltipWidth / 2 / targetWidth;
				const leftPosition = targetWidth * (multiply - 1);
				this.renderer.setStyle(
					tooltipElement,
					"left",
					`${target.getBoundingClientRect().left - leftPosition}px`
				);
				break;
			case "bottom":
				break;
			case "left":
				break;
			case "right":
				break;
		}
	}

	@HostListener("mouseleave", ["$event"])
	private onMouseLeave(event) {
		const tooltipElement =
			this.admPactoCatTooltipTrigger.elementRef.nativeElement.getElementsByClassName(
				"cat-tooltip-aux"
			)[0];
		this.renderer.removeClass(tooltipElement, "show");
	}
}
