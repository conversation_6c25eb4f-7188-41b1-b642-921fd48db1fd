<adm-layout
	(goBack)="voltarListagem()"
	i18n-modulo="@@adm:moduleName"
	i18n-pageTitle="@@planos:pacote:mainTitle"
	modulo="Administrativo"
	pageTitle="Pacote">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<h3 i18n="@@integracao-acesso:insira-os-dados">Insira os dados</h3>
			<div class="testeAlign row">
				<!-- CODIGO -->
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="codigoControl"
						i18n-label="@@planos:pacote:codigoLabel"
						label="Código"
						placeholder="000"
						readonly="true"></pacto-cat-form-input-number>
				</div>
				<!--ADMIN EMPRESA-->
				<div
					*ngIf="sessionService.loggedUser.administrador"
					class="empresaPacote col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-select-filter
						[control]="form.get('empresa')"
						[options]="sessionService.empresas"
						[paramBuilder]="empresasParam"
						errorMsg="Campo EMPRESA precisa ser informado."
						i18n-errorMsg="@@planos:pacote:empresaErrorMsg"
						i18n-label="@@planos:pacote:empresaLabel"
						idKey="codigo"
						label="Empresa*"
						labelKey="nome"></pacto-cat-select-filter>
				</div>
				<!-- DESCRIÇÃO -->
				<div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
					<pacto-cat-form-input
						[control]="form.get('descricao')"
						errorMsg="Campo DESCRIÇÃO precisa ser informado."
						i18n-errorMsg="@@planos:pacote:inputDescricaoErrorMsg"
						i18n-label="@@planos:pacote:inputDescricaoLabel"
						i18n-placeholder="@@planos:pacote:inputDescricaoPlaceholder"
						label="Descrição*"
						maxlength="45"
						placeholder="Descrição"></pacto-cat-form-input>
				</div>
				<!-- INPUT VALOR-->
				<div class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-input-number
						[enableClearInput]="false"
						[formControl]="form.get('precoComposicao')"
						[readonly]="form.get('modalidadesEspecificas').value"
						decimal="true"
						decimalPrecision="2"
						i18n-label="@@planos:pacote:inputValorLabel"
						label="Valor total do pacote"
						placeholder="199,90"></pacto-cat-form-input-number>
				</div>

				<!-- Valor base -->
				<div
					*ngIf="!form.get('modalidadesEspecificas').value"
					class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-input-number
						[formControl]="form.get('quantidadeModalidades')"
						[required]="!form.get('modalidadesEspecificas').value"
						errorMsg="Informe a quantidade de modalidades para o pacote"
						i18n-errorMsg="@@planos:pacote:quantidadeModError"
						i18n-label="@@planos:pacote:quantidadeModLabel"
						label="Quantidade de modalidades"></pacto-cat-form-input-number>
				</div>
			</div>

			<div class="checkboxPacote row">
				<!-- CHECKBOX ADICIONAL -->
				<pacto-cat-checkbox
					[control]="form.get('pacoteAdicional')"
					class="checkboxAdicional"
					i18n-label="@@planos:pacote:checkboxAdicionalLabel"
					label="Pacote adicional"></pacto-cat-checkbox>
				<!-- CHECKBOX DEFAULT -->
				<pacto-cat-checkbox
					[control]="form.get('pacotePadrao')"
					class="checkboxDefault"
					i18n-label="@@planos:pacote:checkboxDefaultLabel"
					label="Pacote padrão"></pacto-cat-checkbox>
				<!-- CHECKBOX MODALIDADES-->
				<pacto-cat-checkbox
					[control]="form.get('modalidadesEspecificas')"
					class="checkboxModalidades"
					i18n-label="@@planos:pacote:checkboxModalidadesLabel"
					label="Modalidades específicas"></pacto-cat-checkbox>
			</div>

			<div>
				<!-- PACOTE MODALIDADES -->
				<div class="pacModalidades">
					<h6 i18n="@@planos:pacote:h6Label">Modalidades do pacote</h6>
				</div>
			</div>

			<div class="row">
				<!-- Modalidade -->
				<div class="modalidade col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
					<pacto-cat-select-filter
						[control]="formCompMod.get('modalidade')"
						[endpointUrl]="
							admRestService.buildFullUrlPlano('modalidades') + '/only-cod-name'
						"
						[idKey]="'codigo'"
						[labelKey]="'nome'"
						[paramBuilder]="modalidadeParam"
						i18n-label="@@planos:pacote:modalidadeSelectLabel"
						label="Modalidade"></pacto-cat-select-filter>
				</div>

				<div class="valorBase col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-input-number
						[formControl]="formCompMod.get('nrVezes')"
						i18n-label="@@planos:pacote:nrVezesLabel"
						label="Vezes por semana no pacote"></pacto-cat-form-input-number>
				</div>

				<div
					*ngIf="form.get('modalidadesEspecificas').value"
					class="valorBase col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
					<pacto-cat-form-input-number
						[formControl]="formCompMod.get('valorMensalPacote')"
						decimal="true"
						decimalPrecision="2"
						i18n-label="@@planos:pacote:valorMensalLabel"
						label="Valor modalidade no pacote"></pacto-cat-form-input-number>
				</div>

				<div
					class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 d-flex align-items-center">
					<div
						(click)="addModalidade()"
						class="pacote-add-button cor-azulim-pri"
						id="add-modalidade-pacote">
						<i class="pct pct-plus-square"></i>
						<span
							i18n="@@produtos:comissaoProduto:adicionarComissao"
							style="margin-left: 4px">
							Adicionar
						</span>
					</div>
				</div>
			</div>

			<div class="row align-items-center">
				<!-- TABELA -->
				<div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
					<div class="pacote-table-wrapper pacto-shadow">
						<pacto-relatorio
							#tableComponent
							(iconClick)="iconClickFn($event)"
							(pageChangeEvent)="changePagePacoteModConfig($event)"
							(pageSizeChange)="changePageSizePacoteModConfig($event)"
							(rowClick)="editPacoteMod($event)"
							(sortEvent)="sort($event)"
							[showShare]="false"
							[table]="table"></pacto-relatorio>
					</div>
				</div>
			</div>

			<div class="d-flex justify-content-end">
				<pacto-cat-button
					(click)="voltarListagem()"
					i18n-label="@@planos:cancelBttn"
					label="Cancelar"
					size="LARGE"
					style="margin-right: 10px"
					type="OUTLINE_DARK"></pacto-cat-button>
				<pacto-cat-button
					(click)="salvarPacote()"
					i18n-label="@@planos:saveBttn"
					label="Salvar"
					size="LARGE"
					style="margin-right: 10px"
					type="PRIMARY"></pacto-cat-button>
			</div>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<ng-template #celulaModalidade let-composicaoModalidade="item">
	{{ composicaoModalidade.modalidade.nome
	}}{{ !composicaoModalidade.modalidade.ativo ? " (Inativa)" : "" }}
</ng-template>

<ng-template #celulaValorModalidade let-composicaoModalidade="item">
	<ng-container *ngIf="composicaoModalidade.nrVezes === 0">
		{{ composicaoModalidade.valorMensalPacote }}
		<i
			[admPactoCatTooltipTrigger]="tooltip"
			class="pct pct-alert-triangle cor-pequizao05"></i>
		<adm-pacto-cat-tooltip #tooltip>
			Este valor será utilizado como base durante a negociação caso seja
			informada uma modalidade com vezes na semana que não esteja no pacote.
		</adm-pacto-cat-tooltip>
	</ng-container>
	<ng-container *ngIf="composicaoModalidade.nrVezes !== 0">
		{{ composicaoModalidade.valorMensalPacote }}
	</ng-container>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span i18n="@@planos:pacote:saveMsg" xingling="SAVE_MSG">
		Pacote cadastrado com sucesso!
	</span>
	<span i18n="@@planos:pacote:valorModalidade" xingling="VALOR_MODALIDADE">
		Valor da Modalidade
	</span>
	<span
		i18n="@@planos:pacote:valorModalidadePacote"
		xingling="VALOR_MODALIDADE_PACOTE">
		Valor modalidade no pacote
	</span>
	<span i18n="@@planos:pacote:numeroVezes" xingling="NUMERO_VEZES">
		Vezes na semana
	</span>
	<span i18n="@@planos:pacote:modalidadeTabela" xingling="MODALIDADE_MODAL">
		Plano e modalidade vezes por semana
	</span>
	<span i18n="@@planos:pacote:modalidadeTabela" xingling="MODALIDADE_TABELA">
		Modalidade
	</span>
	<span i18n="@@planos:pacote:modalidadeMsg" xingling="VALIDA_MODALIDADE">
		O campo MODALIDADE deve ser informado.
	</span>
	<span i18n="@@planos:pacote:modalidadeVazia" xingling="SEM_MODALIADES">
		Não foi informado nenhuma modalidade para o pacote.
	</span>
	<span
		i18n="@@planos:pacote:qtdModalidadeVazia"
		xingling="QTD_MODALIDADE_VAZIA">
		Não foi informada uma quantidade de modalidades válida para este pacote.
	</span>
	<span i18n="@@planos:pacote:totalPacoteVazio" xingling="TOTAL_PACOTE_VAZIO">
		Informe o valor total para o pacote.
	</span>
</pacto-traducoes-xingling>
