import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "sdk";
import { DecimalPipe } from "@angular/common";
import { PlanoApiPacoteService, PacoteModalidade, Pacote } from "plano-api";
import { AdmRestService } from "../../../adm-rest.service";

@Component({
	selector: "adm-pacote-form",
	templateUrl: "./form-pacote.component.html",
	styleUrls: ["./form-pacote.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class PacoteFormComponent implements OnInit, AfterViewInit {
	@Input() codigoPacoteEdit: number;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("tableComponent", { static: false })
	tableComponent: CatTableEditableComponent;
	@ViewChild("celulaModalidade", { static: false })
	celulaModalidade: TemplateRef<any>;
	@ViewChild("celulaValorModalidade", { static: false })
	celulaValorModalidade: TemplateRef<any>;
	table: PactoDataGridConfig;
	form: FormGroup;
	formCompMod: FormGroup;
	moneyMask = [
		"R",
		"$",
		" ",
		/[0-9]?/,
		/[0-9]?/,
		/[0-9]/,
		/[0-9]/,
		",",
		/[0-9]/,
		/[0-9]/,
	];
	codigoControl: FormControl = new FormControl();
	pacote: Pacote = new Pacote();
	private executingFindPacote = false;
	id;
	private indexEdit: number;
	page = 1;
	size = 10;
	composicaoModalidadeArray: {
		content: Array<PacoteModalidade>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<PacoteModalidade>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	empresasParam: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	modalidadeParam: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	constructor(
		private pacoteService: PlanoApiPacoteService,
		private activatedRoute: ActivatedRoute,
		private router: Router,
		private notificationService: SnotifyService,
		private cd: ChangeDetectorRef,
		public sessionService: SessionService,
		public admRestService: AdmRestService,
		public decimalPipe: DecimalPipe
	) {}

	ngOnInit() {
		this.initTableModalidade();
		this.createForm();
		this.createFormCompModalidade();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.codigoControl.disable();
		if (this.pacote) {
			if (!this.pacote.modalidades) {
				this.pacote.modalidades = new Array<PacoteModalidade>();
			}
		}
	}

	ngAfterViewInit() {
		if (this.id) {
			this.pacoteService.find(this.id).subscribe((response: any) => {
				this.pacote = response.content;
				this.codigoControl.setValue(this.pacote.codigo);
				this.executingFindPacote = true;
				this.form.patchValue(
					{
						descricao: this.pacote.descricao,
						precoComposicao: this.pacote.precoComposicao,
						pacotePadrao: this.pacote.pacotePadrao,
						pacoteAdicional: this.pacote.pacoteAdicional,
						modalidadesEspecificas: this.pacote.modalidadesEspecificas,
						quantidadeModalidades: this.pacote.quantidadeModalidades,
					},
					{
						emitEvent: false,
					}
				);
				this.executingFindPacote = false;
				this.createPacoteModConfigPageObject();
				this.cd.detectChanges();
			});
		}
	}

	createForm() {
		this.form = new FormGroup({
			descricao: new FormControl(),
			precoComposicao: new FormControl(),
			pacotePadrao: new FormControl(),
			pacoteAdicional: new FormControl(),
			modalidadesEspecificas: new FormControl(true),
			quantidadeModalidades: new FormControl(),
		});
		this.form.get("modalidadesEspecificas").valueChanges.subscribe((v) => {
			if (!this.executingFindPacote) {
				this.form.get("precoComposicao").setValue(0);
			}
			this.pacote.modalidades.forEach((cm) => (cm.valorMensalPacote = 0));
			this.initTableModalidade();
			this.createPacoteModConfigPageObject();
		});
	}

	createFormCompModalidade() {
		this.formCompMod = new FormGroup({
			modalidade: new FormControl(),
			valorMensalPacote: new FormControl(0),
			nrVezes: new FormControl(0),
		});
	}

	initTableModalidade() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				pagination: true,
				dataAdapterFn: (serverData) => {
					return this.composicaoModalidadeArray;
				},
				columns: [
					{
						nome: "modalidade",
						celula: this.celulaModalidade,
						objectAttrLabelName: "nome",
						titulo: this.traducoes.getLabel("MODALIDADE_TABELA"),
						visible: true,
						ordenavel: false,
					},
					{
						nome: "modalidade",
						objectAttrLabelName: "valorMensal",
						titulo: this.traducoes.getLabel("VALOR_MODALIDADE"),
						visible: true,
						ordenavel: false,
						valueTransform: (v) =>
							this.decimalPipe.transform(v.valorMensal, "1.2-2"),
					},
					{
						nome: "nrVezes",
						titulo: this.traducoes.getLabel("NUMERO_VEZES"),
						visible: true,
					},
					{
						nome: "valorMensalPacote",
						titulo: this.traducoes.getLabel("VALOR_MODALIDADE_PACOTE"),
						celula: this.celulaValorModalidade,
						visible: true,
						valueTransform: (v) => this.decimalPipe.transform(v, "1.2-2"),
					},
				],
				actions: [
					{
						nome: "editPacoteMod",
						iconClass: "pct pct-edit-3 cor-azulim-pri",
						tooltipText: "",
						actionFn: (row, rowIndex) => {
							console.log(row);
							row.rowIndex = rowIndex;
							console.log(row);
							this.editPacoteMod(row);
						},
					},
					{
						nome: "deletePacoteMod",
						iconClass: "pct pct-trash-2 cor-hellboy-pri",
						tooltipText: "",
						actionFn: (row) => this.deletePacoteMod(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	voltarListagem() {
		this.router.navigate(["adm", "planos", "pacotes"]);
	}

	salvarPacote() {
		Object.keys(this.form.getRawValue()).forEach((key) => {
			this.pacote[key] = this.form.getRawValue()[key];
		});
		if (this.pacote.modalidades.length === 0) {
			this.notificationService.error(this.traducoes.getLabel("SEM_MODALIADES"));
			return;
		}

		if (!this.pacote.modalidadesEspecificas) {
			if (
				this.pacote.quantidadeModalidades === undefined ||
				this.pacote.quantidadeModalidades === 0 ||
				(this.pacote.quantidadeModalidades &&
					this.pacote.quantidadeModalidades.toString() === "")
			) {
				this.notificationService.error(
					this.traducoes.getLabel("QTD_MODALIDADE_VAZIA")
				);
				return;
			}
			if (
				this.pacote.precoComposicao === undefined ||
				this.pacote.precoComposicao === 0 ||
				(this.pacote.precoComposicao &&
					this.pacote.precoComposicao.toString() === "")
			) {
				this.notificationService.error(
					this.traducoes.getLabel("TOTAL_PACOTE_VAZIO")
				);
				return;
			}
			this.pacote.modalidades.forEach((cm) => (cm.valorMensalPacote = 0));
		}

		this.pacoteService.save(this.pacote).subscribe(
			(responsePacote) => {
				this.notificationService.success(this.traducoes.getLabel("SAVE_MSG"));
				this.voltarListagem();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	addModalidade() {
		if (
			this.formCompMod.get("modalidade").value === null ||
			!this.formCompMod.get("modalidade").value
		) {
			this.notificationService.error(
				this.traducoes.getLabel("VALIDA_MODALIDADE")
			);
			return;
		}
		if (
			this.formCompMod.get("nrVezes").value === undefined ||
			this.formCompMod.get("nrVezes").value === "" ||
			this.formCompMod.get("nrVezes").value === null
		) {
			this.formCompMod.get("nrVezes").setValue(0);
		}
		if (
			this.formCompMod.get("valorMensalPacote").value === undefined ||
			this.formCompMod.get("valorMensalPacote").value === "" ||
			this.formCompMod.get("valorMensalPacote").value === null
		) {
			this.formCompMod.get("valorMensalPacote").setValue(0);
		}

		const compMod = this.formCompMod.getRawValue();
		const indexSameVezesSemana = this.pacote.modalidades.findIndex(
			(cm) =>
				cm.modalidade.codigo ===
					this.formCompMod.get("modalidade").value.codigo &&
				cm.nrVezes === this.formCompMod.get("nrVezes").value
		);
		if (indexSameVezesSemana !== -1) {
			compMod.codigo = this.pacote.modalidades[indexSameVezesSemana].codigo;
			this.pacote.modalidades[indexSameVezesSemana] =
				this.formCompMod.getRawValue();
		} else if (this.indexEdit !== undefined) {
			compMod.codigo = this.pacote.modalidades[this.indexEdit].codigo;
			this.pacote.modalidades[this.indexEdit] = compMod;
			this.indexEdit = undefined;
		} else {
			this.pacote.modalidades.push(this.formCompMod.getRawValue());
		}
		const indexVezesSemanaZerada = this.pacote.modalidades.findIndex(
			(cm) =>
				cm.modalidade.codigo ===
					this.formCompMod.get("modalidade").value.codigo && cm.nrVezes === 0
		);
		if (indexVezesSemanaZerada === -1 && indexSameVezesSemana === -1) {
			this.pacote.modalidades.push({
				modalidade: this.formCompMod.get("modalidade").value,
				valorMensalPacote: 0,
				nrVezes: 0,
			} as any);
		}
		this.createPacoteModConfigPageObject();
		this.setValorTotalPacote();
		this.formCompMod.reset();
	}

	editPacoteMod(rowInfo) {
		this.indexEdit = rowInfo.rowIndex;
		this.formCompMod.patchValue(rowInfo);
	}

	setValorTotalPacote() {
		let precoComposicao = 0;
		if (this.form.get("modalidadesEspecificas").value) {
			this.pacote.modalidades.forEach((cm) => {
				if (cm.nrVezes === 0 || !cm.nrVezes) {
					precoComposicao += cm.valorMensalPacote;
				}
			});
			this.form.get("precoComposicao").setValue(precoComposicao);
		}
	}

	createPacoteModConfigPageObject() {
		if (isNaN(this.page)) {
			this.page = 1;
		}

		this.pacote.modalidades = this.pacote.modalidades.sort((a, b) => {
			if (a.modalidade.nome > b.modalidade.nome) {
				return 1;
			}
			if (a.modalidade.nome < b.modalidade.nome) {
				return -1;
			}
			return 0;
		});

		this.pacote.modalidades = this.pacote.modalidades.sort((a, b) => {
			if (a.nrVezes > b.nrVezes && a.modalidade.nome === b.modalidade.nome) {
				return 1;
			}
			if (a.nrVezes < b.nrVezes && a.modalidade.nome === b.modalidade.nome) {
				return -1;
			}
			return 0;
		});

		this.composicaoModalidadeArray.totalElements =
			this.pacote.modalidades.length;
		this.composicaoModalidadeArray.size = this.size;
		this.composicaoModalidadeArray.totalPages = Math.ceil(
			+(
				this.composicaoModalidadeArray.totalElements /
				this.composicaoModalidadeArray.size
			)
		);
		this.composicaoModalidadeArray.first = this.page === 0 || this.page === 1;
		this.composicaoModalidadeArray.last =
			this.page === this.composicaoModalidadeArray.totalPages;
		this.composicaoModalidadeArray.content = this.pacote.modalidades.slice(
			this.size * this.page - this.size,
			this.size * this.page
		);
		if (this.tableComponent) {
			this.tableComponent.reloadData();
		}
	}

	changePagePacoteModConfig(page) {
		this.page = page;
		this.createPacoteModConfigPageObject();
	}

	changePageSizePacoteModConfig(size) {
		this.size = size;
		this.createPacoteModConfigPageObject();
	}

	deletePacoteMod(rowInfo) {
		this.pacote.modalidades.splice(rowInfo.rowIndex, 1);
		this.createPacoteModConfigPageObject();
		this.setValorTotalPacote();
	}

	iconClickFn(event) {
		const row = event.row;
		row.rowIndex = event.rowIndex;
		if (event.iconName === "editPacoteMod") {
			this.editPacoteMod(row);
		} else if (event.iconName === "deletePacoteMod") {
			this.deletePacoteMod(event.row);
		}
	}

	sort(event: any) {
		this.pacote.modalidades.sort((cm1, cm2) => {
			let valor1 = cm1[event.columnName];
			let valor2 = cm2[event.columnName];
			if (event.column.objectAttrLabelName !== "label") {
				valor1 = cm1[event.columnName][event.column.objectAttrLabelName];
				valor2 = cm2[event.columnName][event.column.objectAttrLabelName];
			}
			if (event.direction === "ASC") {
				if (valor1 > valor2) {
					return 1;
				} else if (valor1 < valor2) {
					return -1;
				}
			} else if (event.direction === "DESC") {
				if (valor1 < valor2) {
					return 1;
				} else if (valor1 > valor2) {
					return -1;
				}
			}
			return 0;
		});
		this.createPacoteModConfigPageObject();
	}
}
