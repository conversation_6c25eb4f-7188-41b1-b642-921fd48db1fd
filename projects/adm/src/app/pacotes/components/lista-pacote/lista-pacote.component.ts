import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import { Router } from "@angular/router";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PlanoApiPacoteService, Pacote } from "plano-api";

@Component({
	selector: "adm-pacote",
	templateUrl: "./lista-pacote.component.html",
	styleUrls: ["./lista-pacote.component.scss"],
})
export class ListaPacoteComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescription", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnEmpresa", { static: true }) columnEmpresa: TemplateRef<any>;
	@ViewChild("columnValor", { static: true }) columnValor: TemplateRef<any>;
	@ViewChild("confirmDelete", { static: false })
	cofirmDeleteBody: TemplateRef<any>;
	filterConfig: GridFilterConfig = { filters: [] };
	tablePacote: PactoDataGridConfig;
	recurso: PerfilAcessoRecurso;
	moneyMask = [
		"R",
		"$",
		" ",
		/[0-9]?/,
		/[0-9]?/,
		/[0-9]/,
		/[0-9]/,
		",",
		/[0-9]/,
		/[0-9]/,
	];
	pacotes: {
		content: Array<Pacote>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<Pacote>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	pacoteToDelete: Pacote;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private pacoteService: PlanoApiPacoteService,
		private ngbModal: NgbModal
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.PACOTE);
	}

	ngOnInit() {
		this.initTable();
		this.initFilter();
		this.cd.detectChanges();
	}

	private initTable() {
		setTimeout(() => {
			this.tablePacote = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl("/pacotes", false, Api.MSPLANO),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "descricao",
						titulo: this.columnDescricao,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "empresa",
						titulo: this.columnEmpresa,
						visible: true,
						ordenavel: true,
						valueTransform: (v: any) => v.nome,
					},
					{
						nome: "precoComposicao",
						titulo: this.columnValor,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editPacote",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("EDIT_TOOLTIP"),
						actionFn: (row) => this.editPacote(row),
					},
					{
						nome: "deletePacote",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("DELETE_TOOLTIP"),
						actionFn: (row) => this.deletePacote(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	private initFilter() {
		if (!this.tablePacote) {
			this.tablePacote = new PactoDataGridConfig({
				showFilters: true,
			});
		}
		this.filterConfig = {
			filters: [],
		};
	}

	editPacote(pacote) {
		this.router.navigate(["adm", "planos", "pacotes", pacote.codigo]);
	}

	deletePacote(pacote: any) {
		if (this.recurso.excluir) {
			this.pacoteToDelete = pacote;
			const dialogRef = this.ngbModal.open(ConfirmDialogDeleteComponent, {
				centered: true,
			});
			dialogRef.componentInstance.texto = `Tem certeza que deseja excluir o pacote ${pacote.codigo} - ${pacote.descricao}?`;
			if (dialogRef.result) {
				dialogRef.result
					.then((confirmado) => {
						if (confirmado) {
							this.pacoteService.delete(pacote.codigo).subscribe(
								(response) => {
									this.notificationService.success(
										this.traducao.getLabel("SUCESS_DELETE")
									);
									this.tableData.reloadData();
								},
								(httpErrorResponse) => {
									const err = httpErrorResponse.error;
									if (err.meta && err.meta.messageValue) {
										this.notificationService.error(err.meta.messageValue);
									}
								}
							);
						}
					})
					.catch((e) => {});
			}
		} else {
			this.notificationService.warning(this.traducao.getLabel("NO_PERMISSION"));
		}
	}

	novoPacote() {
		this.router.navigate(["adm", "planos", "novo-pacote"]);
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editPacote") {
			this.editPacote(event.row);
		} else if (event.iconName === "deletePacote") {
			this.deletePacote(event.row);
		}
	}
}
