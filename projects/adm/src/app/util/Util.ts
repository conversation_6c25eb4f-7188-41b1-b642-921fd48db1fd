export class Util {
	public static indexOfByAttr(
		array: Array<any>,
		attrName: string,
		value: any
	): number {
		for (let i = 0; i < array.length; i++) {
			if (array[i][attrName] === value) {
				return i;
			}
		}
		return -1;
	}

	public static calcularDiferencaEmMinutos(
		horaInicio: string,
		horaFim: string
	): number {
		// Converte as strings para objetos Date usando uma data fixa
		const dataFixa = "1970-01-01"; // Data fixa para evitar problemas
		const inicio = new Date(`${dataFixa}T${horaInicio}:00`);
		const fim = new Date(`${dataFixa}T${horaFim}:00`);

		// Calcula a diferença em milissegundos e converte para minutos
		const diferencaEmMilissegundos = fim.getTime() - inicio.getTime();
		return diferencaEmMilissegundos / (1000 * 60);
	}

	public static calcularHoraFim(
		horaInicio: string,
		duracaoEmMinutos: number
	): string {
		// Divide a string no formato "HH:mm" em horas e minutos
		const [horas, minutos] = horaInicio.split(":").map(Number);

		// Converte tudo para minutos
		const totalMinutosInicio = horas * 60 + minutos;

		// Soma a duração
		const totalMinutosFim =
			Number(totalMinutosInicio) + Number(duracaoEmMinutos);

		// Converte de volta para horas e minutos, garantindo que horas sejam no formato 24h
		const horasFim = Math.floor(totalMinutosFim / 60) % 24; // Ajusta o ciclo de 24 horas
		const minutosFim = totalMinutosFim % 60;

		// Formata o resultado no formato "HH:mm"
		return `${horasFim.toString().padStart(2, "0")}:${minutosFim
			.toString()
			.padStart(2, "0")}`;
	}

	public static fromTimestampToUTC(date) {
		const utcDate = new Date(date);
		return new Date(
			utcDate.getUTCFullYear(),
			utcDate.getUTCMonth(),
			utcDate.getUTCDate(),
			utcDate.getUTCHours(),
			utcDate.getUTCMinutes(),
			utcDate.getUTCSeconds()
		).getTime();
	}

	public static roundTo(num: number, decimals: number): number {
		return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
	}
}
