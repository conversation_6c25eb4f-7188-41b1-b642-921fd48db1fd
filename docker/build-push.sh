BRANCH=${1:-"auto"}

set -e

current_branch(){
    echo $(git branch | sed -n -e 's/^\* \(.*\)/\1/p' | sed -e 's/\//-/' | sed -e 's/#/-/')
}

if [[ $BRANCH == 'auto' ]]; then
    BRANCH=$(current_branch)
fi

IMAGE_NAME="registry.gitlab.com/plataformazw/zw_ui:${BRANCH}"

cd ..
npm install
./scripts/build-spa.sh -m 'prod' -u '/' -l pt
cp -R dist/prod-pt docker/dist
cd docker
docker build -t $IMAGE_NAME .
docker push $IMAGE_NAME
