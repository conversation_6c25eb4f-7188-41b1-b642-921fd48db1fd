server {
    listen       80;
    server_name  localhost;
    root   /usr/share/nginx/html;

    location ${SERVER_PATH}/pt {
        try_files $uri $uri/ ${SERVER_PATH}/pt/index.html;
    }

    location ${SERVER_PATH}/marketing {
        try_files $uri $uri/ ${SERVER_PATH}/marketing/index.html;
    }

    location ${SERVER_PATH}/en {
        try_files $uri $uri/ ${SERVER_PATH}/en/index.html;
    }

    location ${SERVER_PATH}/es {
        try_files $uri $uri/ ${SERVER_PATH}/es/index.html;
    }

    location ${SERVER_PATH}/ {
        try_files $uri $uri/ ${SERVER_PATH}/index.html;
    }
}
