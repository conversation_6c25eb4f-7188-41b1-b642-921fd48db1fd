setup-env

# Executa o setup do ambiente, caso necessário
echo "Starting Nginx..."

# Definir context dinamicamente (ou através de variáveis de ambiente)
echo "Server Path :$SERVER_PATH"

TARGET_PATH="/usr/share/nginx/html/"
if [ -n "$SERVER_PATH" ]; then
    TARGET_PATH="/usr/share/nginx/html/$SERVER_PATH/"
    mkdir -p "$TARGET_PATH"
fi

# Define a origem, garantindo que o $INITIAL_CONTEXT tenha apenas uma barra se vazio
echo "Initial Context: $INITIAL_CONTEXT"
if [ -n "$INITIAL_CONTEXT" ]; then
    echo "Entrou no if do Initial Context"
    cd "$INITIAL_CONTEXT" || exit
fi

if [ -n "$SERVER_PATH" ]; then
  find . -name "index.html" -exec sed -i 's|\(<base href="\)/\([^"]*\)"|\1'"$SERVER_PATH"'/\2"|' {} \;
  echo "Sed aplicado..."
fi

cp -rf . "$TARGET_PATH"
echo "Copy realizado..."

# Substituir SERVER_PATH no template
envsubst "\$SERVER_PATH" < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf
echo "Config NGINX ok..."

# Iniciar o Nginx
nginx -g 'daemon off;'
